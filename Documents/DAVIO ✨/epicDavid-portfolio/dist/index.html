<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<!-- Updated description based on the new config translations -->
		<meta name="description" content="React | TailwindCSS | Vite | TypeScript | JavaScript | MongoDB" />
		<!-- Updated keywords to match the new description -->
		<meta name="keywords" content="Web Developer, React, TypeScript, JavaScript, MongoDB" />

		<!-- Simple Discord embed meta tags (same as your working site) -->
		<!-- Updated og:title based on the new config translations -->
		<meta property="og:title" content="⚒⎮𝙚𝙥𝙞𝙘.𝘿𝙖𝙫𝙞𝙙 ☻ :)" />
		<meta property="og:image" content="https://i.ibb.co/vxCfZL6K/epic-Davidpic.gif" />
		<meta name="theme-color" content="#121212" />

		<link rel="icon" type="image/svg+xml" href="/favicon.png" />
		<title>Loading...</title> <!-- Initial title before JS updates it -->
		<script type="module" crossorigin src="/assets/index-5dnPL6G7.js"></script>
		<link rel="stylesheet" crossorigin href="/assets/index-CuiSt-Jx.css">
	</head>
	<body>
		<div id="root"></div>
		<script>
			// Updated titleText to match the new config translations
			const titleText = "⚒⎮𝙚𝙥𝙞𝙘.𝘿𝙖𝙫𝙞𝙙 ☻ :)";
			let index = 0;
			let direction = 1;

			function updateTitle() {
				// Ensure the document.title is updated dynamically
				document.title = titleText.substring(0, index);
				index += direction;
				if (index > titleText.length) {
					direction = -1;
				} else if (index < 2) { // Changed from 2 to 0 to ensure full text is shown before shrinking
					direction = 1;
				}
				setTimeout(updateTitle, 200);
			}

			// Call updateTitle after the DOM is fully loaded to ensure document.title is available
			document.addEventListener('DOMContentLoaded', updateTitle);
		</script>
	</body>
</html>
