(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const l of o.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function ep(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var pc={exports:{}},qi={},hc={exports:{}},T={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $r=Symbol.for("react.element"),tp=Symbol.for("react.portal"),np=Symbol.for("react.fragment"),rp=Symbol.for("react.strict_mode"),ip=Symbol.for("react.profiler"),op=Symbol.for("react.provider"),lp=Symbol.for("react.context"),sp=Symbol.for("react.forward_ref"),up=Symbol.for("react.suspense"),ap=Symbol.for("react.memo"),cp=Symbol.for("react.lazy"),Ou=Symbol.iterator;function fp(e){return e===null||typeof e!="object"?null:(e=Ou&&e[Ou]||e["@@iterator"],typeof e=="function"?e:null)}var mc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},gc=Object.assign,vc={};function Dn(e,t,n){this.props=e,this.context=t,this.refs=vc,this.updater=n||mc}Dn.prototype.isReactComponent={};Dn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Dn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function yc(){}yc.prototype=Dn.prototype;function ws(e,t,n){this.props=e,this.context=t,this.refs=vc,this.updater=n||mc}var xs=ws.prototype=new yc;xs.constructor=ws;gc(xs,Dn.prototype);xs.isPureReactComponent=!0;var _u=Array.isArray,wc=Object.prototype.hasOwnProperty,Ss={current:null},xc={key:!0,ref:!0,__self:!0,__source:!0};function Sc(e,t,n){var r,i={},o=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(o=""+t.key),t)wc.call(t,r)&&!xc.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(s===1)i.children=n;else if(1<s){for(var u=Array(s),a=0;a<s;a++)u[a]=arguments[a+2];i.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)i[r]===void 0&&(i[r]=s[r]);return{$$typeof:$r,type:e,key:o,ref:l,props:i,_owner:Ss.current}}function dp(e,t){return{$$typeof:$r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ks(e){return typeof e=="object"&&e!==null&&e.$$typeof===$r}function pp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ju=/\/+/g;function Ao(e,t){return typeof e=="object"&&e!==null&&e.key!=null?pp(""+e.key):t.toString(36)}function ii(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(o){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case $r:case tp:l=!0}}if(l)return l=e,i=i(l),e=r===""?"."+Ao(l,0):r,_u(i)?(n="",e!=null&&(n=e.replace(ju,"$&/")+"/"),ii(i,t,n,"",function(a){return a})):i!=null&&(ks(i)&&(i=dp(i,n+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(ju,"$&/")+"/")+e)),t.push(i)),1;if(l=0,r=r===""?".":r+":",_u(e))for(var s=0;s<e.length;s++){o=e[s];var u=r+Ao(o,s);l+=ii(o,t,n,u,i)}else if(u=fp(e),typeof u=="function")for(e=u.call(e),s=0;!(o=e.next()).done;)o=o.value,u=r+Ao(o,s++),l+=ii(o,t,n,u,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Ar(e,t,n){if(e==null)return e;var r=[],i=0;return ii(e,r,"","",function(o){return t.call(n,o,i++)}),r}function hp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ge={current:null},oi={transition:null},mp={ReactCurrentDispatcher:ge,ReactCurrentBatchConfig:oi,ReactCurrentOwner:Ss};function kc(){throw Error("act(...) is not supported in production builds of React.")}T.Children={map:Ar,forEach:function(e,t,n){Ar(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ar(e,function(){t++}),t},toArray:function(e){return Ar(e,function(t){return t})||[]},only:function(e){if(!ks(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};T.Component=Dn;T.Fragment=np;T.Profiler=ip;T.PureComponent=ws;T.StrictMode=rp;T.Suspense=up;T.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=mp;T.act=kc;T.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=gc({},e.props),i=e.key,o=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,l=Ss.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)wc.call(t,u)&&!xc.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&s!==void 0?s[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){s=Array(u);for(var a=0;a<u;a++)s[a]=arguments[a+2];r.children=s}return{$$typeof:$r,type:e.type,key:i,ref:o,props:r,_owner:l}};T.createContext=function(e){return e={$$typeof:lp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:op,_context:e},e.Consumer=e};T.createElement=Sc;T.createFactory=function(e){var t=Sc.bind(null,e);return t.type=e,t};T.createRef=function(){return{current:null}};T.forwardRef=function(e){return{$$typeof:sp,render:e}};T.isValidElement=ks;T.lazy=function(e){return{$$typeof:cp,_payload:{_status:-1,_result:e},_init:hp}};T.memo=function(e,t){return{$$typeof:ap,type:e,compare:t===void 0?null:t}};T.startTransition=function(e){var t=oi.transition;oi.transition={};try{e()}finally{oi.transition=t}};T.unstable_act=kc;T.useCallback=function(e,t){return ge.current.useCallback(e,t)};T.useContext=function(e){return ge.current.useContext(e)};T.useDebugValue=function(){};T.useDeferredValue=function(e){return ge.current.useDeferredValue(e)};T.useEffect=function(e,t){return ge.current.useEffect(e,t)};T.useId=function(){return ge.current.useId()};T.useImperativeHandle=function(e,t,n){return ge.current.useImperativeHandle(e,t,n)};T.useInsertionEffect=function(e,t){return ge.current.useInsertionEffect(e,t)};T.useLayoutEffect=function(e,t){return ge.current.useLayoutEffect(e,t)};T.useMemo=function(e,t){return ge.current.useMemo(e,t)};T.useReducer=function(e,t,n){return ge.current.useReducer(e,t,n)};T.useRef=function(e){return ge.current.useRef(e)};T.useState=function(e){return ge.current.useState(e)};T.useSyncExternalStore=function(e,t,n){return ge.current.useSyncExternalStore(e,t,n)};T.useTransition=function(){return ge.current.useTransition()};T.version="18.3.1";hc.exports=T;var $=hc.exports;const gp=ep($);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vp=$,yp=Symbol.for("react.element"),wp=Symbol.for("react.fragment"),xp=Object.prototype.hasOwnProperty,Sp=vp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,kp={key:!0,ref:!0,__self:!0,__source:!0};function Cc(e,t,n){var r,i={},o=null,l=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)xp.call(t,r)&&!kp.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:yp,type:e,key:o,ref:l,props:i,_owner:Sp.current}}qi.Fragment=wp;qi.jsx=Cc;qi.jsxs=Cc;pc.exports=qi;var S=pc.exports,Ec={exports:{}},_e={},Pc={exports:{}},Nc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,L){var R=O.length;O.push(L);e:for(;0<R;){var G=R-1>>>1,te=O[G];if(0<i(te,L))O[G]=L,O[R]=te,R=G;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var L=O[0],R=O.pop();if(R!==L){O[0]=R;e:for(var G=0,te=O.length,Dr=te>>>1;G<Dr;){var Ft=2*(G+1)-1,Fo=O[Ft],At=Ft+1,Fr=O[At];if(0>i(Fo,R))At<te&&0>i(Fr,Fo)?(O[G]=Fr,O[At]=R,G=At):(O[G]=Fo,O[Ft]=R,G=Ft);else if(At<te&&0>i(Fr,R))O[G]=Fr,O[At]=R,G=At;else break e}}return L}function i(O,L){var R=O.sortIndex-L.sortIndex;return R!==0?R:O.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var u=[],a=[],f=1,h=null,c=3,v=!1,g=!1,y=!1,E=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(O){for(var L=n(a);L!==null;){if(L.callback===null)r(a);else if(L.startTime<=O)r(a),L.sortIndex=L.expirationTime,t(u,L);else break;L=n(a)}}function w(O){if(y=!1,p(O),!g)if(n(u)!==null)g=!0,rn(x);else{var L=n(a);L!==null&&oe(w,L.startTime-O)}}function x(O,L){g=!1,y&&(y=!1,m(N),N=-1),v=!0;var R=c;try{for(p(L),h=n(u);h!==null&&(!(h.expirationTime>L)||O&&!X());){var G=h.callback;if(typeof G=="function"){h.callback=null,c=h.priorityLevel;var te=G(h.expirationTime<=L);L=e.unstable_now(),typeof te=="function"?h.callback=te:h===n(u)&&r(u),p(L)}else r(u);h=n(u)}if(h!==null)var Dr=!0;else{var Ft=n(a);Ft!==null&&oe(w,Ft.startTime-L),Dr=!1}return Dr}finally{h=null,c=R,v=!1}}var P=!1,k=null,N=-1,M=5,j=-1;function X(){return!(e.unstable_now()-j<M)}function Dt(){if(k!==null){var O=e.unstable_now();j=O;var L=!0;try{L=k(!0,O)}finally{L?Fe():(P=!1,k=null)}}else P=!1}var Fe;if(typeof d=="function")Fe=function(){d(Dt)};else if(typeof MessageChannel<"u"){var ft=new MessageChannel,nn=ft.port2;ft.port1.onmessage=Dt,Fe=function(){nn.postMessage(null)}}else Fe=function(){E(Dt,0)};function rn(O){k=O,P||(P=!0,Fe())}function oe(O,L){N=E(function(){O(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){g||v||(g=!0,rn(x))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return c},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(O){switch(c){case 1:case 2:case 3:var L=3;break;default:L=c}var R=c;c=L;try{return O()}finally{c=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,L){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var R=c;c=O;try{return L()}finally{c=R}},e.unstable_scheduleCallback=function(O,L,R){var G=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?G+R:G):R=G,O){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=R+te,O={id:f++,callback:L,priorityLevel:O,startTime:R,expirationTime:te,sortIndex:-1},R>G?(O.sortIndex=R,t(a,O),n(u)===null&&O===n(a)&&(y?(m(N),N=-1):y=!0,oe(w,R-G))):(O.sortIndex=te,t(u,O),g||v||(g=!0,rn(x))),O},e.unstable_shouldYield=X,e.unstable_wrapCallback=function(O){var L=c;return function(){var R=c;c=L;try{return O.apply(this,arguments)}finally{c=R}}}})(Nc);Pc.exports=Nc;var Cp=Pc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ep=$,Ne=Cp;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Oc=new Set,cr={};function en(e,t){_n(e,t),_n(e+"Capture",t)}function _n(e,t){for(cr[e]=t,e=0;e<t.length;e++)Oc.add(t[e])}var ot=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sl=Object.prototype.hasOwnProperty,Pp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Lu={},Ru={};function Np(e){return Sl.call(Ru,e)?!0:Sl.call(Lu,e)?!1:Pp.test(e)?Ru[e]=!0:(Lu[e]=!0,!1)}function Op(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function _p(e,t,n,r){if(t===null||typeof t>"u"||Op(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,i,o,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var ue={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ue[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ue[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ue[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ue[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ue[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ue[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ue[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ue[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ue[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var Cs=/[\-:]([a-z])/g;function Es(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Cs,Es);ue[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Cs,Es);ue[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Cs,Es);ue[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ue[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});ue.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ue[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ps(e,t,n,r){var i=ue.hasOwnProperty(t)?ue[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(_p(t,n,i,r)&&(n=null),r||i===null?Np(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ct=Ep.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ur=Symbol.for("react.element"),un=Symbol.for("react.portal"),an=Symbol.for("react.fragment"),Ns=Symbol.for("react.strict_mode"),kl=Symbol.for("react.profiler"),_c=Symbol.for("react.provider"),jc=Symbol.for("react.context"),Os=Symbol.for("react.forward_ref"),Cl=Symbol.for("react.suspense"),El=Symbol.for("react.suspense_list"),_s=Symbol.for("react.memo"),pt=Symbol.for("react.lazy"),Lc=Symbol.for("react.offscreen"),$u=Symbol.iterator;function bn(e){return e===null||typeof e!="object"?null:(e=$u&&e[$u]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Uo;function Xn(e){if(Uo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Uo=t&&t[1]||""}return`
`+Uo+e}var bo=!1;function Vo(e,t){if(!e||bo)return"";bo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(a){var r=a}Reflect.construct(e,[],t)}else{try{t.call()}catch(a){r=a}e.call(t.prototype)}else{try{throw Error()}catch(a){r=a}e()}}catch(a){if(a&&r&&typeof a.stack=="string"){for(var i=a.stack.split(`
`),o=r.stack.split(`
`),l=i.length-1,s=o.length-1;1<=l&&0<=s&&i[l]!==o[s];)s--;for(;1<=l&&0<=s;l--,s--)if(i[l]!==o[s]){if(l!==1||s!==1)do if(l--,s--,0>s||i[l]!==o[s]){var u=`
`+i[l].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=l&&0<=s);break}}}finally{bo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Xn(e):""}function jp(e){switch(e.tag){case 5:return Xn(e.type);case 16:return Xn("Lazy");case 13:return Xn("Suspense");case 19:return Xn("SuspenseList");case 0:case 2:case 15:return e=Vo(e.type,!1),e;case 11:return e=Vo(e.type.render,!1),e;case 1:return e=Vo(e.type,!0),e;default:return""}}function Pl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case an:return"Fragment";case un:return"Portal";case kl:return"Profiler";case Ns:return"StrictMode";case Cl:return"Suspense";case El:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case jc:return(e.displayName||"Context")+".Consumer";case _c:return(e._context.displayName||"Context")+".Provider";case Os:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _s:return t=e.displayName||null,t!==null?t:Pl(e.type)||"Memo";case pt:t=e._payload,e=e._init;try{return Pl(e(t))}catch{}}return null}function Lp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Pl(t);case 8:return t===Ns?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Lt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Rc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Rp(e){var t=Rc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(l){r=""+l,o.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function br(e){e._valueTracker||(e._valueTracker=Rp(e))}function $c(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Rc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function yi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Nl(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Lt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Tc(e,t){t=t.checked,t!=null&&Ps(e,"checked",t,!1)}function Ol(e,t){Tc(e,t);var n=Lt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?_l(e,t.type,n):t.hasOwnProperty("defaultValue")&&_l(e,t.type,Lt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Mu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function _l(e,t,n){(t!=="number"||yi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Jn=Array.isArray;function xn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Lt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function jl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function zu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(Jn(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Lt(n)}}function Mc(e,t){var n=Lt(t.value),r=Lt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function zc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ll(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?zc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Vr,Ic=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Vr=Vr||document.createElement("div"),Vr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Vr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function fr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var tr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},$p=["Webkit","ms","Moz","O"];Object.keys(tr).forEach(function(e){$p.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),tr[t]=tr[e]})});function Dc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||tr.hasOwnProperty(e)&&tr[e]?(""+t).trim():t+"px"}function Fc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Dc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Tp=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Rl(e,t){if(t){if(Tp[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function $l(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tl=null;function js(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ml=null,Sn=null,kn=null;function Du(e){if(e=zr(e)){if(typeof Ml!="function")throw Error(C(280));var t=e.stateNode;t&&(t=io(t),Ml(e.stateNode,e.type,t))}}function Ac(e){Sn?kn?kn.push(e):kn=[e]:Sn=e}function Uc(){if(Sn){var e=Sn,t=kn;if(kn=Sn=null,Du(e),t)for(e=0;e<t.length;e++)Du(t[e])}}function bc(e,t){return e(t)}function Vc(){}var Bo=!1;function Bc(e,t,n){if(Bo)return e(t,n);Bo=!0;try{return bc(e,t,n)}finally{Bo=!1,(Sn!==null||kn!==null)&&(Vc(),Uc())}}function dr(e,t){var n=e.stateNode;if(n===null)return null;var r=io(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var zl=!1;if(ot)try{var Vn={};Object.defineProperty(Vn,"passive",{get:function(){zl=!0}}),window.addEventListener("test",Vn,Vn),window.removeEventListener("test",Vn,Vn)}catch{zl=!1}function Mp(e,t,n,r,i,o,l,s,u){var a=Array.prototype.slice.call(arguments,3);try{t.apply(n,a)}catch(f){this.onError(f)}}var nr=!1,wi=null,xi=!1,Il=null,zp={onError:function(e){nr=!0,wi=e}};function Ip(e,t,n,r,i,o,l,s,u){nr=!1,wi=null,Mp.apply(zp,arguments)}function Dp(e,t,n,r,i,o,l,s,u){if(Ip.apply(this,arguments),nr){if(nr){var a=wi;nr=!1,wi=null}else throw Error(C(198));xi||(xi=!0,Il=a)}}function tn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Hc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Fu(e){if(tn(e)!==e)throw Error(C(188))}function Fp(e){var t=e.alternate;if(!t){if(t=tn(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Fu(i),e;if(o===r)return Fu(i),t;o=o.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=o;else{for(var l=!1,s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l){for(s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function Wc(e){return e=Fp(e),e!==null?Kc(e):null}function Kc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Kc(e);if(t!==null)return t;e=e.sibling}return null}var Qc=Ne.unstable_scheduleCallback,Au=Ne.unstable_cancelCallback,Ap=Ne.unstable_shouldYield,Up=Ne.unstable_requestPaint,Y=Ne.unstable_now,bp=Ne.unstable_getCurrentPriorityLevel,Ls=Ne.unstable_ImmediatePriority,Gc=Ne.unstable_UserBlockingPriority,Si=Ne.unstable_NormalPriority,Vp=Ne.unstable_LowPriority,Yc=Ne.unstable_IdlePriority,eo=null,Je=null;function Bp(e){if(Je&&typeof Je.onCommitFiberRoot=="function")try{Je.onCommitFiberRoot(eo,e,void 0,(e.current.flags&128)===128)}catch{}}var He=Math.clz32?Math.clz32:Kp,Hp=Math.log,Wp=Math.LN2;function Kp(e){return e>>>=0,e===0?32:31-(Hp(e)/Wp|0)|0}var Br=64,Hr=4194304;function Zn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ki(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~i;s!==0?r=Zn(s):(o&=l,o!==0&&(r=Zn(o)))}else l=n&~i,l!==0?r=Zn(l):o!==0&&(r=Zn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-He(t),i=1<<n,r|=e[n],t&=~i;return r}function Qp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-He(o),s=1<<l,u=i[l];u===-1?(!(s&n)||s&r)&&(i[l]=Qp(s,t)):u<=t&&(e.expiredLanes|=s),o&=~s}}function Dl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Xc(){var e=Br;return Br<<=1,!(Br&4194240)&&(Br=64),e}function Ho(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Tr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-He(t),e[t]=n}function Yp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-He(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Rs(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-He(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var I=0;function Jc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Zc,$s,qc,ef,tf,Fl=!1,Wr=[],xt=null,St=null,kt=null,pr=new Map,hr=new Map,mt=[],Xp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Uu(e,t){switch(e){case"focusin":case"focusout":xt=null;break;case"dragenter":case"dragleave":St=null;break;case"mouseover":case"mouseout":kt=null;break;case"pointerover":case"pointerout":pr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":hr.delete(t.pointerId)}}function Bn(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=zr(t),t!==null&&$s(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Jp(e,t,n,r,i){switch(t){case"focusin":return xt=Bn(xt,e,t,n,r,i),!0;case"dragenter":return St=Bn(St,e,t,n,r,i),!0;case"mouseover":return kt=Bn(kt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return pr.set(o,Bn(pr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,hr.set(o,Bn(hr.get(o)||null,e,t,n,r,i)),!0}return!1}function nf(e){var t=Bt(e.target);if(t!==null){var n=tn(t);if(n!==null){if(t=n.tag,t===13){if(t=Hc(n),t!==null){e.blockedOn=t,tf(e.priority,function(){qc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function li(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Al(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Tl=r,n.target.dispatchEvent(r),Tl=null}else return t=zr(n),t!==null&&$s(t),e.blockedOn=n,!1;t.shift()}return!0}function bu(e,t,n){li(e)&&n.delete(t)}function Zp(){Fl=!1,xt!==null&&li(xt)&&(xt=null),St!==null&&li(St)&&(St=null),kt!==null&&li(kt)&&(kt=null),pr.forEach(bu),hr.forEach(bu)}function Hn(e,t){e.blockedOn===t&&(e.blockedOn=null,Fl||(Fl=!0,Ne.unstable_scheduleCallback(Ne.unstable_NormalPriority,Zp)))}function mr(e){function t(i){return Hn(i,e)}if(0<Wr.length){Hn(Wr[0],e);for(var n=1;n<Wr.length;n++){var r=Wr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xt!==null&&Hn(xt,e),St!==null&&Hn(St,e),kt!==null&&Hn(kt,e),pr.forEach(t),hr.forEach(t),n=0;n<mt.length;n++)r=mt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<mt.length&&(n=mt[0],n.blockedOn===null);)nf(n),n.blockedOn===null&&mt.shift()}var Cn=ct.ReactCurrentBatchConfig,Ci=!0;function qp(e,t,n,r){var i=I,o=Cn.transition;Cn.transition=null;try{I=1,Ts(e,t,n,r)}finally{I=i,Cn.transition=o}}function e0(e,t,n,r){var i=I,o=Cn.transition;Cn.transition=null;try{I=4,Ts(e,t,n,r)}finally{I=i,Cn.transition=o}}function Ts(e,t,n,r){if(Ci){var i=Al(e,t,n,r);if(i===null)el(e,t,r,Ei,n),Uu(e,r);else if(Jp(i,e,t,n,r))r.stopPropagation();else if(Uu(e,r),t&4&&-1<Xp.indexOf(e)){for(;i!==null;){var o=zr(i);if(o!==null&&Zc(o),o=Al(e,t,n,r),o===null&&el(e,t,r,Ei,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else el(e,t,r,null,n)}}var Ei=null;function Al(e,t,n,r){if(Ei=null,e=js(r),e=Bt(e),e!==null)if(t=tn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Hc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ei=e,null}function rf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(bp()){case Ls:return 1;case Gc:return 4;case Si:case Vp:return 16;case Yc:return 536870912;default:return 16}default:return 16}}var yt=null,Ms=null,si=null;function of(){if(si)return si;var e,t=Ms,n=t.length,r,i="value"in yt?yt.value:yt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===i[o-r];r++);return si=i.slice(e,1<r?1-r:void 0)}function ui(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Kr(){return!0}function Vu(){return!1}function je(e){function t(n,r,i,o,l){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(o):o[s]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Kr:Vu,this.isPropagationStopped=Vu,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Kr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Kr)},persist:function(){},isPersistent:Kr}),t}var Fn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},zs=je(Fn),Mr=K({},Fn,{view:0,detail:0}),t0=je(Mr),Wo,Ko,Wn,to=K({},Mr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Is,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Wn&&(Wn&&e.type==="mousemove"?(Wo=e.screenX-Wn.screenX,Ko=e.screenY-Wn.screenY):Ko=Wo=0,Wn=e),Wo)},movementY:function(e){return"movementY"in e?e.movementY:Ko}}),Bu=je(to),n0=K({},to,{dataTransfer:0}),r0=je(n0),i0=K({},Mr,{relatedTarget:0}),Qo=je(i0),o0=K({},Fn,{animationName:0,elapsedTime:0,pseudoElement:0}),l0=je(o0),s0=K({},Fn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),u0=je(s0),a0=K({},Fn,{data:0}),Hu=je(a0),c0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},f0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},d0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function p0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=d0[e])?!!t[e]:!1}function Is(){return p0}var h0=K({},Mr,{key:function(e){if(e.key){var t=c0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ui(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?f0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Is,charCode:function(e){return e.type==="keypress"?ui(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ui(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),m0=je(h0),g0=K({},to,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wu=je(g0),v0=K({},Mr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Is}),y0=je(v0),w0=K({},Fn,{propertyName:0,elapsedTime:0,pseudoElement:0}),x0=je(w0),S0=K({},to,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),k0=je(S0),C0=[9,13,27,32],Ds=ot&&"CompositionEvent"in window,rr=null;ot&&"documentMode"in document&&(rr=document.documentMode);var E0=ot&&"TextEvent"in window&&!rr,lf=ot&&(!Ds||rr&&8<rr&&11>=rr),Ku=" ",Qu=!1;function sf(e,t){switch(e){case"keyup":return C0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function uf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var cn=!1;function P0(e,t){switch(e){case"compositionend":return uf(t);case"keypress":return t.which!==32?null:(Qu=!0,Ku);case"textInput":return e=t.data,e===Ku&&Qu?null:e;default:return null}}function N0(e,t){if(cn)return e==="compositionend"||!Ds&&sf(e,t)?(e=of(),si=Ms=yt=null,cn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return lf&&t.locale!=="ko"?null:t.data;default:return null}}var O0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!O0[e.type]:t==="textarea"}function af(e,t,n,r){Ac(r),t=Pi(t,"onChange"),0<t.length&&(n=new zs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ir=null,gr=null;function _0(e){xf(e,0)}function no(e){var t=pn(e);if($c(t))return e}function j0(e,t){if(e==="change")return t}var cf=!1;if(ot){var Go;if(ot){var Yo="oninput"in document;if(!Yo){var Yu=document.createElement("div");Yu.setAttribute("oninput","return;"),Yo=typeof Yu.oninput=="function"}Go=Yo}else Go=!1;cf=Go&&(!document.documentMode||9<document.documentMode)}function Xu(){ir&&(ir.detachEvent("onpropertychange",ff),gr=ir=null)}function ff(e){if(e.propertyName==="value"&&no(gr)){var t=[];af(t,gr,e,js(e)),Bc(_0,t)}}function L0(e,t,n){e==="focusin"?(Xu(),ir=t,gr=n,ir.attachEvent("onpropertychange",ff)):e==="focusout"&&Xu()}function R0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return no(gr)}function $0(e,t){if(e==="click")return no(t)}function T0(e,t){if(e==="input"||e==="change")return no(t)}function M0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ke=typeof Object.is=="function"?Object.is:M0;function vr(e,t){if(Ke(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Sl.call(t,i)||!Ke(e[i],t[i]))return!1}return!0}function Ju(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zu(e,t){var n=Ju(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ju(n)}}function df(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?df(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function pf(){for(var e=window,t=yi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=yi(e.document)}return t}function Fs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function z0(e){var t=pf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&df(n.ownerDocument.documentElement,n)){if(r!==null&&Fs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Zu(n,o);var l=Zu(n,r);i&&l&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var I0=ot&&"documentMode"in document&&11>=document.documentMode,fn=null,Ul=null,or=null,bl=!1;function qu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;bl||fn==null||fn!==yi(r)||(r=fn,"selectionStart"in r&&Fs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),or&&vr(or,r)||(or=r,r=Pi(Ul,"onSelect"),0<r.length&&(t=new zs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=fn)))}function Qr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var dn={animationend:Qr("Animation","AnimationEnd"),animationiteration:Qr("Animation","AnimationIteration"),animationstart:Qr("Animation","AnimationStart"),transitionend:Qr("Transition","TransitionEnd")},Xo={},hf={};ot&&(hf=document.createElement("div").style,"AnimationEvent"in window||(delete dn.animationend.animation,delete dn.animationiteration.animation,delete dn.animationstart.animation),"TransitionEvent"in window||delete dn.transitionend.transition);function ro(e){if(Xo[e])return Xo[e];if(!dn[e])return e;var t=dn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in hf)return Xo[e]=t[n];return e}var mf=ro("animationend"),gf=ro("animationiteration"),vf=ro("animationstart"),yf=ro("transitionend"),wf=new Map,ea="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mt(e,t){wf.set(e,t),en(t,[e])}for(var Jo=0;Jo<ea.length;Jo++){var Zo=ea[Jo],D0=Zo.toLowerCase(),F0=Zo[0].toUpperCase()+Zo.slice(1);Mt(D0,"on"+F0)}Mt(mf,"onAnimationEnd");Mt(gf,"onAnimationIteration");Mt(vf,"onAnimationStart");Mt("dblclick","onDoubleClick");Mt("focusin","onFocus");Mt("focusout","onBlur");Mt(yf,"onTransitionEnd");_n("onMouseEnter",["mouseout","mouseover"]);_n("onMouseLeave",["mouseout","mouseover"]);_n("onPointerEnter",["pointerout","pointerover"]);_n("onPointerLeave",["pointerout","pointerover"]);en("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));en("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));en("onBeforeInput",["compositionend","keypress","textInput","paste"]);en("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));en("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));en("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var qn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),A0=new Set("cancel close invalid load scroll toggle".split(" ").concat(qn));function ta(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Dp(r,t,void 0,e),e.currentTarget=null}function xf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],u=s.instance,a=s.currentTarget;if(s=s.listener,u!==o&&i.isPropagationStopped())break e;ta(i,s,a),o=u}else for(l=0;l<r.length;l++){if(s=r[l],u=s.instance,a=s.currentTarget,s=s.listener,u!==o&&i.isPropagationStopped())break e;ta(i,s,a),o=u}}}if(xi)throw e=Il,xi=!1,Il=null,e}function U(e,t){var n=t[Kl];n===void 0&&(n=t[Kl]=new Set);var r=e+"__bubble";n.has(r)||(Sf(t,e,2,!1),n.add(r))}function qo(e,t,n){var r=0;t&&(r|=4),Sf(n,e,r,t)}var Gr="_reactListening"+Math.random().toString(36).slice(2);function yr(e){if(!e[Gr]){e[Gr]=!0,Oc.forEach(function(n){n!=="selectionchange"&&(A0.has(n)||qo(n,!1,e),qo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Gr]||(t[Gr]=!0,qo("selectionchange",!1,t))}}function Sf(e,t,n,r){switch(rf(t)){case 1:var i=qp;break;case 4:i=e0;break;default:i=Ts}n=i.bind(null,t,n,e),i=void 0,!zl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function el(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===i||s.nodeType===8&&s.parentNode===i)break;if(l===4)for(l=r.return;l!==null;){var u=l.tag;if((u===3||u===4)&&(u=l.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;l=l.return}for(;s!==null;){if(l=Bt(s),l===null)return;if(u=l.tag,u===5||u===6){r=o=l;continue e}s=s.parentNode}}r=r.return}Bc(function(){var a=o,f=js(n),h=[];e:{var c=wf.get(e);if(c!==void 0){var v=zs,g=e;switch(e){case"keypress":if(ui(n)===0)break e;case"keydown":case"keyup":v=m0;break;case"focusin":g="focus",v=Qo;break;case"focusout":g="blur",v=Qo;break;case"beforeblur":case"afterblur":v=Qo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Bu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=r0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=y0;break;case mf:case gf:case vf:v=l0;break;case yf:v=x0;break;case"scroll":v=t0;break;case"wheel":v=k0;break;case"copy":case"cut":case"paste":v=u0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Wu}var y=(t&4)!==0,E=!y&&e==="scroll",m=y?c!==null?c+"Capture":null:c;y=[];for(var d=a,p;d!==null;){p=d;var w=p.stateNode;if(p.tag===5&&w!==null&&(p=w,m!==null&&(w=dr(d,m),w!=null&&y.push(wr(d,w,p)))),E)break;d=d.return}0<y.length&&(c=new v(c,g,null,n,f),h.push({event:c,listeners:y}))}}if(!(t&7)){e:{if(c=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",c&&n!==Tl&&(g=n.relatedTarget||n.fromElement)&&(Bt(g)||g[lt]))break e;if((v||c)&&(c=f.window===f?f:(c=f.ownerDocument)?c.defaultView||c.parentWindow:window,v?(g=n.relatedTarget||n.toElement,v=a,g=g?Bt(g):null,g!==null&&(E=tn(g),g!==E||g.tag!==5&&g.tag!==6)&&(g=null)):(v=null,g=a),v!==g)){if(y=Bu,w="onMouseLeave",m="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(y=Wu,w="onPointerLeave",m="onPointerEnter",d="pointer"),E=v==null?c:pn(v),p=g==null?c:pn(g),c=new y(w,d+"leave",v,n,f),c.target=E,c.relatedTarget=p,w=null,Bt(f)===a&&(y=new y(m,d+"enter",g,n,f),y.target=p,y.relatedTarget=E,w=y),E=w,v&&g)t:{for(y=v,m=g,d=0,p=y;p;p=on(p))d++;for(p=0,w=m;w;w=on(w))p++;for(;0<d-p;)y=on(y),d--;for(;0<p-d;)m=on(m),p--;for(;d--;){if(y===m||m!==null&&y===m.alternate)break t;y=on(y),m=on(m)}y=null}else y=null;v!==null&&na(h,c,v,y,!1),g!==null&&E!==null&&na(h,E,g,y,!0)}}e:{if(c=a?pn(a):window,v=c.nodeName&&c.nodeName.toLowerCase(),v==="select"||v==="input"&&c.type==="file")var x=j0;else if(Gu(c))if(cf)x=T0;else{x=R0;var P=L0}else(v=c.nodeName)&&v.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(x=$0);if(x&&(x=x(e,a))){af(h,x,n,f);break e}P&&P(e,c,a),e==="focusout"&&(P=c._wrapperState)&&P.controlled&&c.type==="number"&&_l(c,"number",c.value)}switch(P=a?pn(a):window,e){case"focusin":(Gu(P)||P.contentEditable==="true")&&(fn=P,Ul=a,or=null);break;case"focusout":or=Ul=fn=null;break;case"mousedown":bl=!0;break;case"contextmenu":case"mouseup":case"dragend":bl=!1,qu(h,n,f);break;case"selectionchange":if(I0)break;case"keydown":case"keyup":qu(h,n,f)}var k;if(Ds)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else cn?sf(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(lf&&n.locale!=="ko"&&(cn||N!=="onCompositionStart"?N==="onCompositionEnd"&&cn&&(k=of()):(yt=f,Ms="value"in yt?yt.value:yt.textContent,cn=!0)),P=Pi(a,N),0<P.length&&(N=new Hu(N,e,null,n,f),h.push({event:N,listeners:P}),k?N.data=k:(k=uf(n),k!==null&&(N.data=k)))),(k=E0?P0(e,n):N0(e,n))&&(a=Pi(a,"onBeforeInput"),0<a.length&&(f=new Hu("onBeforeInput","beforeinput",null,n,f),h.push({event:f,listeners:a}),f.data=k))}xf(h,t)})}function wr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Pi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=dr(e,n),o!=null&&r.unshift(wr(e,o,i)),o=dr(e,t),o!=null&&r.push(wr(e,o,i))),e=e.return}return r}function on(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function na(e,t,n,r,i){for(var o=t._reactName,l=[];n!==null&&n!==r;){var s=n,u=s.alternate,a=s.stateNode;if(u!==null&&u===r)break;s.tag===5&&a!==null&&(s=a,i?(u=dr(n,o),u!=null&&l.unshift(wr(n,u,s))):i||(u=dr(n,o),u!=null&&l.push(wr(n,u,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var U0=/\r\n?/g,b0=/\u0000|\uFFFD/g;function ra(e){return(typeof e=="string"?e:""+e).replace(U0,`
`).replace(b0,"")}function Yr(e,t,n){if(t=ra(t),ra(e)!==t&&n)throw Error(C(425))}function Ni(){}var Vl=null,Bl=null;function Hl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wl=typeof setTimeout=="function"?setTimeout:void 0,V0=typeof clearTimeout=="function"?clearTimeout:void 0,ia=typeof Promise=="function"?Promise:void 0,B0=typeof queueMicrotask=="function"?queueMicrotask:typeof ia<"u"?function(e){return ia.resolve(null).then(e).catch(H0)}:Wl;function H0(e){setTimeout(function(){throw e})}function tl(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),mr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);mr(t)}function Ct(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function oa(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var An=Math.random().toString(36).slice(2),Ye="__reactFiber$"+An,xr="__reactProps$"+An,lt="__reactContainer$"+An,Kl="__reactEvents$"+An,W0="__reactListeners$"+An,K0="__reactHandles$"+An;function Bt(e){var t=e[Ye];if(t)return t;for(var n=e.parentNode;n;){if(t=n[lt]||n[Ye]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=oa(e);e!==null;){if(n=e[Ye])return n;e=oa(e)}return t}e=n,n=e.parentNode}return null}function zr(e){return e=e[Ye]||e[lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function io(e){return e[xr]||null}var Ql=[],hn=-1;function zt(e){return{current:e}}function b(e){0>hn||(e.current=Ql[hn],Ql[hn]=null,hn--)}function A(e,t){hn++,Ql[hn]=e.current,e.current=t}var Rt={},pe=zt(Rt),xe=zt(!1),Gt=Rt;function jn(e,t){var n=e.type.contextTypes;if(!n)return Rt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Se(e){return e=e.childContextTypes,e!=null}function Oi(){b(xe),b(pe)}function la(e,t,n){if(pe.current!==Rt)throw Error(C(168));A(pe,t),A(xe,n)}function kf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(C(108,Lp(e)||"Unknown",i));return K({},n,r)}function _i(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Rt,Gt=pe.current,A(pe,e),A(xe,xe.current),!0}function sa(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=kf(e,t,Gt),r.__reactInternalMemoizedMergedChildContext=e,b(xe),b(pe),A(pe,e)):b(xe),A(xe,n)}var tt=null,oo=!1,nl=!1;function Cf(e){tt===null?tt=[e]:tt.push(e)}function Q0(e){oo=!0,Cf(e)}function It(){if(!nl&&tt!==null){nl=!0;var e=0,t=I;try{var n=tt;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}tt=null,oo=!1}catch(i){throw tt!==null&&(tt=tt.slice(e+1)),Qc(Ls,It),i}finally{I=t,nl=!1}}return null}var mn=[],gn=0,ji=null,Li=0,Re=[],$e=0,Yt=null,nt=1,rt="";function Ut(e,t){mn[gn++]=Li,mn[gn++]=ji,ji=e,Li=t}function Ef(e,t,n){Re[$e++]=nt,Re[$e++]=rt,Re[$e++]=Yt,Yt=e;var r=nt;e=rt;var i=32-He(r)-1;r&=~(1<<i),n+=1;var o=32-He(t)+i;if(30<o){var l=i-i%5;o=(r&(1<<l)-1).toString(32),r>>=l,i-=l,nt=1<<32-He(t)+i|n<<i|r,rt=o+e}else nt=1<<o|n<<i|r,rt=e}function As(e){e.return!==null&&(Ut(e,1),Ef(e,1,0))}function Us(e){for(;e===ji;)ji=mn[--gn],mn[gn]=null,Li=mn[--gn],mn[gn]=null;for(;e===Yt;)Yt=Re[--$e],Re[$e]=null,rt=Re[--$e],Re[$e]=null,nt=Re[--$e],Re[$e]=null}var Pe=null,Ee=null,B=!1,Ve=null;function Pf(e,t){var n=Te(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ua(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Pe=e,Ee=Ct(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Pe=e,Ee=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Yt!==null?{id:nt,overflow:rt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Te(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Pe=e,Ee=null,!0):!1;default:return!1}}function Gl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Yl(e){if(B){var t=Ee;if(t){var n=t;if(!ua(e,t)){if(Gl(e))throw Error(C(418));t=Ct(n.nextSibling);var r=Pe;t&&ua(e,t)?Pf(r,n):(e.flags=e.flags&-4097|2,B=!1,Pe=e)}}else{if(Gl(e))throw Error(C(418));e.flags=e.flags&-4097|2,B=!1,Pe=e}}}function aa(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Pe=e}function Xr(e){if(e!==Pe)return!1;if(!B)return aa(e),B=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Hl(e.type,e.memoizedProps)),t&&(t=Ee)){if(Gl(e))throw Nf(),Error(C(418));for(;t;)Pf(e,t),t=Ct(t.nextSibling)}if(aa(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ee=Ct(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ee=null}}else Ee=Pe?Ct(e.stateNode.nextSibling):null;return!0}function Nf(){for(var e=Ee;e;)e=Ct(e.nextSibling)}function Ln(){Ee=Pe=null,B=!1}function bs(e){Ve===null?Ve=[e]:Ve.push(e)}var G0=ct.ReactCurrentBatchConfig;function Kn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(l){var s=i.refs;l===null?delete s[o]:s[o]=l},t._stringRef=o,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function Jr(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ca(e){var t=e._init;return t(e._payload)}function Of(e){function t(m,d){if(e){var p=m.deletions;p===null?(m.deletions=[d],m.flags|=16):p.push(d)}}function n(m,d){if(!e)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function r(m,d){for(m=new Map;d!==null;)d.key!==null?m.set(d.key,d):m.set(d.index,d),d=d.sibling;return m}function i(m,d){return m=Ot(m,d),m.index=0,m.sibling=null,m}function o(m,d,p){return m.index=p,e?(p=m.alternate,p!==null?(p=p.index,p<d?(m.flags|=2,d):p):(m.flags|=2,d)):(m.flags|=1048576,d)}function l(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,d,p,w){return d===null||d.tag!==6?(d=al(p,m.mode,w),d.return=m,d):(d=i(d,p),d.return=m,d)}function u(m,d,p,w){var x=p.type;return x===an?f(m,d,p.props.children,w,p.key):d!==null&&(d.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===pt&&ca(x)===d.type)?(w=i(d,p.props),w.ref=Kn(m,d,p),w.return=m,w):(w=mi(p.type,p.key,p.props,null,m.mode,w),w.ref=Kn(m,d,p),w.return=m,w)}function a(m,d,p,w){return d===null||d.tag!==4||d.stateNode.containerInfo!==p.containerInfo||d.stateNode.implementation!==p.implementation?(d=cl(p,m.mode,w),d.return=m,d):(d=i(d,p.children||[]),d.return=m,d)}function f(m,d,p,w,x){return d===null||d.tag!==7?(d=Qt(p,m.mode,w,x),d.return=m,d):(d=i(d,p),d.return=m,d)}function h(m,d,p){if(typeof d=="string"&&d!==""||typeof d=="number")return d=al(""+d,m.mode,p),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Ur:return p=mi(d.type,d.key,d.props,null,m.mode,p),p.ref=Kn(m,null,d),p.return=m,p;case un:return d=cl(d,m.mode,p),d.return=m,d;case pt:var w=d._init;return h(m,w(d._payload),p)}if(Jn(d)||bn(d))return d=Qt(d,m.mode,p,null),d.return=m,d;Jr(m,d)}return null}function c(m,d,p,w){var x=d!==null?d.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return x!==null?null:s(m,d,""+p,w);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Ur:return p.key===x?u(m,d,p,w):null;case un:return p.key===x?a(m,d,p,w):null;case pt:return x=p._init,c(m,d,x(p._payload),w)}if(Jn(p)||bn(p))return x!==null?null:f(m,d,p,w,null);Jr(m,p)}return null}function v(m,d,p,w,x){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(p)||null,s(d,m,""+w,x);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Ur:return m=m.get(w.key===null?p:w.key)||null,u(d,m,w,x);case un:return m=m.get(w.key===null?p:w.key)||null,a(d,m,w,x);case pt:var P=w._init;return v(m,d,p,P(w._payload),x)}if(Jn(w)||bn(w))return m=m.get(p)||null,f(d,m,w,x,null);Jr(d,w)}return null}function g(m,d,p,w){for(var x=null,P=null,k=d,N=d=0,M=null;k!==null&&N<p.length;N++){k.index>N?(M=k,k=null):M=k.sibling;var j=c(m,k,p[N],w);if(j===null){k===null&&(k=M);break}e&&k&&j.alternate===null&&t(m,k),d=o(j,d,N),P===null?x=j:P.sibling=j,P=j,k=M}if(N===p.length)return n(m,k),B&&Ut(m,N),x;if(k===null){for(;N<p.length;N++)k=h(m,p[N],w),k!==null&&(d=o(k,d,N),P===null?x=k:P.sibling=k,P=k);return B&&Ut(m,N),x}for(k=r(m,k);N<p.length;N++)M=v(k,m,N,p[N],w),M!==null&&(e&&M.alternate!==null&&k.delete(M.key===null?N:M.key),d=o(M,d,N),P===null?x=M:P.sibling=M,P=M);return e&&k.forEach(function(X){return t(m,X)}),B&&Ut(m,N),x}function y(m,d,p,w){var x=bn(p);if(typeof x!="function")throw Error(C(150));if(p=x.call(p),p==null)throw Error(C(151));for(var P=x=null,k=d,N=d=0,M=null,j=p.next();k!==null&&!j.done;N++,j=p.next()){k.index>N?(M=k,k=null):M=k.sibling;var X=c(m,k,j.value,w);if(X===null){k===null&&(k=M);break}e&&k&&X.alternate===null&&t(m,k),d=o(X,d,N),P===null?x=X:P.sibling=X,P=X,k=M}if(j.done)return n(m,k),B&&Ut(m,N),x;if(k===null){for(;!j.done;N++,j=p.next())j=h(m,j.value,w),j!==null&&(d=o(j,d,N),P===null?x=j:P.sibling=j,P=j);return B&&Ut(m,N),x}for(k=r(m,k);!j.done;N++,j=p.next())j=v(k,m,N,j.value,w),j!==null&&(e&&j.alternate!==null&&k.delete(j.key===null?N:j.key),d=o(j,d,N),P===null?x=j:P.sibling=j,P=j);return e&&k.forEach(function(Dt){return t(m,Dt)}),B&&Ut(m,N),x}function E(m,d,p,w){if(typeof p=="object"&&p!==null&&p.type===an&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case Ur:e:{for(var x=p.key,P=d;P!==null;){if(P.key===x){if(x=p.type,x===an){if(P.tag===7){n(m,P.sibling),d=i(P,p.props.children),d.return=m,m=d;break e}}else if(P.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===pt&&ca(x)===P.type){n(m,P.sibling),d=i(P,p.props),d.ref=Kn(m,P,p),d.return=m,m=d;break e}n(m,P);break}else t(m,P);P=P.sibling}p.type===an?(d=Qt(p.props.children,m.mode,w,p.key),d.return=m,m=d):(w=mi(p.type,p.key,p.props,null,m.mode,w),w.ref=Kn(m,d,p),w.return=m,m=w)}return l(m);case un:e:{for(P=p.key;d!==null;){if(d.key===P)if(d.tag===4&&d.stateNode.containerInfo===p.containerInfo&&d.stateNode.implementation===p.implementation){n(m,d.sibling),d=i(d,p.children||[]),d.return=m,m=d;break e}else{n(m,d);break}else t(m,d);d=d.sibling}d=cl(p,m.mode,w),d.return=m,m=d}return l(m);case pt:return P=p._init,E(m,d,P(p._payload),w)}if(Jn(p))return g(m,d,p,w);if(bn(p))return y(m,d,p,w);Jr(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,d!==null&&d.tag===6?(n(m,d.sibling),d=i(d,p),d.return=m,m=d):(n(m,d),d=al(p,m.mode,w),d.return=m,m=d),l(m)):n(m,d)}return E}var Rn=Of(!0),_f=Of(!1),Ri=zt(null),$i=null,vn=null,Vs=null;function Bs(){Vs=vn=$i=null}function Hs(e){var t=Ri.current;b(Ri),e._currentValue=t}function Xl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function En(e,t){$i=e,Vs=vn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function ze(e){var t=e._currentValue;if(Vs!==e)if(e={context:e,memoizedValue:t,next:null},vn===null){if($i===null)throw Error(C(308));vn=e,$i.dependencies={lanes:0,firstContext:e}}else vn=vn.next=e;return t}var Ht=null;function Ws(e){Ht===null?Ht=[e]:Ht.push(e)}function jf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ws(t)):(n.next=i.next,i.next=n),t.interleaved=n,st(e,r)}function st(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var ht=!1;function Ks(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Lf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function it(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Et(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,st(e,n)}return i=r.interleaved,i===null?(t.next=t,Ws(r)):(t.next=i.next,i.next=t),r.interleaved=t,st(e,n)}function ai(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Rs(e,n)}}function fa(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=l:o=o.next=l,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ti(e,t,n,r){var i=e.updateQueue;ht=!1;var o=i.firstBaseUpdate,l=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var u=s,a=u.next;u.next=null,l===null?o=a:l.next=a,l=u;var f=e.alternate;f!==null&&(f=f.updateQueue,s=f.lastBaseUpdate,s!==l&&(s===null?f.firstBaseUpdate=a:s.next=a,f.lastBaseUpdate=u))}if(o!==null){var h=i.baseState;l=0,f=a=u=null,s=o;do{var c=s.lane,v=s.eventTime;if((r&c)===c){f!==null&&(f=f.next={eventTime:v,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var g=e,y=s;switch(c=t,v=n,y.tag){case 1:if(g=y.payload,typeof g=="function"){h=g.call(v,h,c);break e}h=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=y.payload,c=typeof g=="function"?g.call(v,h,c):g,c==null)break e;h=K({},h,c);break e;case 2:ht=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,c=i.effects,c===null?i.effects=[s]:c.push(s))}else v={eventTime:v,lane:c,tag:s.tag,payload:s.payload,callback:s.callback,next:null},f===null?(a=f=v,u=h):f=f.next=v,l|=c;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;c=s,s=c.next,c.next=null,i.lastBaseUpdate=c,i.shared.pending=null}}while(!0);if(f===null&&(u=h),i.baseState=u,i.firstBaseUpdate=a,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do l|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Jt|=l,e.lanes=l,e.memoizedState=h}}function da(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var Ir={},Ze=zt(Ir),Sr=zt(Ir),kr=zt(Ir);function Wt(e){if(e===Ir)throw Error(C(174));return e}function Qs(e,t){switch(A(kr,t),A(Sr,e),A(Ze,Ir),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ll(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ll(t,e)}b(Ze),A(Ze,t)}function $n(){b(Ze),b(Sr),b(kr)}function Rf(e){Wt(kr.current);var t=Wt(Ze.current),n=Ll(t,e.type);t!==n&&(A(Sr,e),A(Ze,n))}function Gs(e){Sr.current===e&&(b(Ze),b(Sr))}var H=zt(0);function Mi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var rl=[];function Ys(){for(var e=0;e<rl.length;e++)rl[e]._workInProgressVersionPrimary=null;rl.length=0}var ci=ct.ReactCurrentDispatcher,il=ct.ReactCurrentBatchConfig,Xt=0,W=null,Z=null,ne=null,zi=!1,lr=!1,Cr=0,Y0=0;function ae(){throw Error(C(321))}function Xs(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ke(e[n],t[n]))return!1;return!0}function Js(e,t,n,r,i,o){if(Xt=o,W=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ci.current=e===null||e.memoizedState===null?q0:eh,e=n(r,i),lr){o=0;do{if(lr=!1,Cr=0,25<=o)throw Error(C(301));o+=1,ne=Z=null,t.updateQueue=null,ci.current=th,e=n(r,i)}while(lr)}if(ci.current=Ii,t=Z!==null&&Z.next!==null,Xt=0,ne=Z=W=null,zi=!1,t)throw Error(C(300));return e}function Zs(){var e=Cr!==0;return Cr=0,e}function Ge(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?W.memoizedState=ne=e:ne=ne.next=e,ne}function Ie(){if(Z===null){var e=W.alternate;e=e!==null?e.memoizedState:null}else e=Z.next;var t=ne===null?W.memoizedState:ne.next;if(t!==null)ne=t,Z=e;else{if(e===null)throw Error(C(310));Z=e,e={memoizedState:Z.memoizedState,baseState:Z.baseState,baseQueue:Z.baseQueue,queue:Z.queue,next:null},ne===null?W.memoizedState=ne=e:ne=ne.next=e}return ne}function Er(e,t){return typeof t=="function"?t(e):t}function ol(e){var t=Ie(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=Z,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var l=i.next;i.next=o.next,o.next=l}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var s=l=null,u=null,a=o;do{var f=a.lane;if((Xt&f)===f)u!==null&&(u=u.next={lane:0,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null}),r=a.hasEagerState?a.eagerState:e(r,a.action);else{var h={lane:f,action:a.action,hasEagerState:a.hasEagerState,eagerState:a.eagerState,next:null};u===null?(s=u=h,l=r):u=u.next=h,W.lanes|=f,Jt|=f}a=a.next}while(a!==null&&a!==o);u===null?l=r:u.next=s,Ke(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,W.lanes|=o,Jt|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ll(e){var t=Ie(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var l=i=i.next;do o=e(o,l.action),l=l.next;while(l!==i);Ke(o,t.memoizedState)||(we=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function $f(){}function Tf(e,t){var n=W,r=Ie(),i=t(),o=!Ke(r.memoizedState,i);if(o&&(r.memoizedState=i,we=!0),r=r.queue,qs(If.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ne!==null&&ne.memoizedState.tag&1){if(n.flags|=2048,Pr(9,zf.bind(null,n,r,i,t),void 0,null),re===null)throw Error(C(349));Xt&30||Mf(n,t,i)}return i}function Mf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=W.updateQueue,t===null?(t={lastEffect:null,stores:null},W.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function zf(e,t,n,r){t.value=n,t.getSnapshot=r,Df(t)&&Ff(e)}function If(e,t,n){return n(function(){Df(t)&&Ff(e)})}function Df(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ke(e,n)}catch{return!0}}function Ff(e){var t=st(e,1);t!==null&&We(t,e,1,-1)}function pa(e){var t=Ge();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Er,lastRenderedState:e},t.queue=e,e=e.dispatch=Z0.bind(null,W,e),[t.memoizedState,e]}function Pr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=W.updateQueue,t===null?(t={lastEffect:null,stores:null},W.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Af(){return Ie().memoizedState}function fi(e,t,n,r){var i=Ge();W.flags|=e,i.memoizedState=Pr(1|t,n,void 0,r===void 0?null:r)}function lo(e,t,n,r){var i=Ie();r=r===void 0?null:r;var o=void 0;if(Z!==null){var l=Z.memoizedState;if(o=l.destroy,r!==null&&Xs(r,l.deps)){i.memoizedState=Pr(t,n,o,r);return}}W.flags|=e,i.memoizedState=Pr(1|t,n,o,r)}function ha(e,t){return fi(8390656,8,e,t)}function qs(e,t){return lo(2048,8,e,t)}function Uf(e,t){return lo(4,2,e,t)}function bf(e,t){return lo(4,4,e,t)}function Vf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Bf(e,t,n){return n=n!=null?n.concat([e]):null,lo(4,4,Vf.bind(null,t,e),n)}function eu(){}function Hf(e,t){var n=Ie();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Xs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wf(e,t){var n=Ie();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Xs(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Kf(e,t,n){return Xt&21?(Ke(n,t)||(n=Xc(),W.lanes|=n,Jt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function X0(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=il.transition;il.transition={};try{e(!1),t()}finally{I=n,il.transition=r}}function Qf(){return Ie().memoizedState}function J0(e,t,n){var r=Nt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gf(e))Yf(t,n);else if(n=jf(e,t,n,r),n!==null){var i=me();We(n,e,r,i),Xf(n,t,r)}}function Z0(e,t,n){var r=Nt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gf(e))Yf(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var l=t.lastRenderedState,s=o(l,n);if(i.hasEagerState=!0,i.eagerState=s,Ke(s,l)){var u=t.interleaved;u===null?(i.next=i,Ws(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=jf(e,t,i,r),n!==null&&(i=me(),We(n,e,r,i),Xf(n,t,r))}}function Gf(e){var t=e.alternate;return e===W||t!==null&&t===W}function Yf(e,t){lr=zi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Rs(e,n)}}var Ii={readContext:ze,useCallback:ae,useContext:ae,useEffect:ae,useImperativeHandle:ae,useInsertionEffect:ae,useLayoutEffect:ae,useMemo:ae,useReducer:ae,useRef:ae,useState:ae,useDebugValue:ae,useDeferredValue:ae,useTransition:ae,useMutableSource:ae,useSyncExternalStore:ae,useId:ae,unstable_isNewReconciler:!1},q0={readContext:ze,useCallback:function(e,t){return Ge().memoizedState=[e,t===void 0?null:t],e},useContext:ze,useEffect:ha,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,fi(4194308,4,Vf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fi(4194308,4,e,t)},useInsertionEffect:function(e,t){return fi(4,2,e,t)},useMemo:function(e,t){var n=Ge();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ge();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=J0.bind(null,W,e),[r.memoizedState,e]},useRef:function(e){var t=Ge();return e={current:e},t.memoizedState=e},useState:pa,useDebugValue:eu,useDeferredValue:function(e){return Ge().memoizedState=e},useTransition:function(){var e=pa(!1),t=e[0];return e=X0.bind(null,e[1]),Ge().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=W,i=Ge();if(B){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),re===null)throw Error(C(349));Xt&30||Mf(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,ha(If.bind(null,r,o,e),[e]),r.flags|=2048,Pr(9,zf.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ge(),t=re.identifierPrefix;if(B){var n=rt,r=nt;n=(r&~(1<<32-He(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Cr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Y0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},eh={readContext:ze,useCallback:Hf,useContext:ze,useEffect:qs,useImperativeHandle:Bf,useInsertionEffect:Uf,useLayoutEffect:bf,useMemo:Wf,useReducer:ol,useRef:Af,useState:function(){return ol(Er)},useDebugValue:eu,useDeferredValue:function(e){var t=Ie();return Kf(t,Z.memoizedState,e)},useTransition:function(){var e=ol(Er)[0],t=Ie().memoizedState;return[e,t]},useMutableSource:$f,useSyncExternalStore:Tf,useId:Qf,unstable_isNewReconciler:!1},th={readContext:ze,useCallback:Hf,useContext:ze,useEffect:qs,useImperativeHandle:Bf,useInsertionEffect:Uf,useLayoutEffect:bf,useMemo:Wf,useReducer:ll,useRef:Af,useState:function(){return ll(Er)},useDebugValue:eu,useDeferredValue:function(e){var t=Ie();return Z===null?t.memoizedState=e:Kf(t,Z.memoizedState,e)},useTransition:function(){var e=ll(Er)[0],t=Ie().memoizedState;return[e,t]},useMutableSource:$f,useSyncExternalStore:Tf,useId:Qf,unstable_isNewReconciler:!1};function Ue(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Jl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var so={isMounted:function(e){return(e=e._reactInternals)?tn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=me(),i=Nt(e),o=it(r,i);o.payload=t,n!=null&&(o.callback=n),t=Et(e,o,i),t!==null&&(We(t,e,i,r),ai(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=me(),i=Nt(e),o=it(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Et(e,o,i),t!==null&&(We(t,e,i,r),ai(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=me(),r=Nt(e),i=it(n,r);i.tag=2,t!=null&&(i.callback=t),t=Et(e,i,r),t!==null&&(We(t,e,r,n),ai(t,e,r))}};function ma(e,t,n,r,i,o,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,l):t.prototype&&t.prototype.isPureReactComponent?!vr(n,r)||!vr(i,o):!0}function Jf(e,t,n){var r=!1,i=Rt,o=t.contextType;return typeof o=="object"&&o!==null?o=ze(o):(i=Se(t)?Gt:pe.current,r=t.contextTypes,o=(r=r!=null)?jn(e,i):Rt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=so,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function ga(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&so.enqueueReplaceState(t,t.state,null)}function Zl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Ks(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=ze(o):(o=Se(t)?Gt:pe.current,i.context=jn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Jl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&so.enqueueReplaceState(i,i.state,null),Ti(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Tn(e,t){try{var n="",r=t;do n+=jp(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function sl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ql(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var nh=typeof WeakMap=="function"?WeakMap:Map;function Zf(e,t,n){n=it(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Fi||(Fi=!0,as=r),ql(e,t)},n}function qf(e,t,n){n=it(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ql(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ql(e,t),typeof r!="function"&&(Pt===null?Pt=new Set([this]):Pt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function va(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new nh;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=gh.bind(null,e,t,n),t.then(e,e))}function ya(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wa(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=it(-1,1),t.tag=2,Et(n,t,1))),n.lanes|=1),e)}var rh=ct.ReactCurrentOwner,we=!1;function he(e,t,n,r){t.child=e===null?_f(t,null,n,r):Rn(t,e.child,n,r)}function xa(e,t,n,r,i){n=n.render;var o=t.ref;return En(t,i),r=Js(e,t,n,r,o,i),n=Zs(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ut(e,t,i)):(B&&n&&As(t),t.flags|=1,he(e,t,r,i),t.child)}function Sa(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!uu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,ed(e,t,o,r,i)):(e=mi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var l=o.memoizedProps;if(n=n.compare,n=n!==null?n:vr,n(l,r)&&e.ref===t.ref)return ut(e,t,i)}return t.flags|=1,e=Ot(o,r),e.ref=t.ref,e.return=t,t.child=e}function ed(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(vr(o,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,ut(e,t,i)}return es(e,t,n,r,i)}function td(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},A(wn,Ce),Ce|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,A(wn,Ce),Ce|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,A(wn,Ce),Ce|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,A(wn,Ce),Ce|=r;return he(e,t,i,n),t.child}function nd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function es(e,t,n,r,i){var o=Se(n)?Gt:pe.current;return o=jn(t,o),En(t,i),n=Js(e,t,n,r,o,i),r=Zs(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ut(e,t,i)):(B&&r&&As(t),t.flags|=1,he(e,t,n,i),t.child)}function ka(e,t,n,r,i){if(Se(n)){var o=!0;_i(t)}else o=!1;if(En(t,i),t.stateNode===null)di(e,t),Jf(t,n,r),Zl(t,n,r,i),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var u=l.context,a=n.contextType;typeof a=="object"&&a!==null?a=ze(a):(a=Se(n)?Gt:pe.current,a=jn(t,a));var f=n.getDerivedStateFromProps,h=typeof f=="function"||typeof l.getSnapshotBeforeUpdate=="function";h||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||u!==a)&&ga(t,l,r,a),ht=!1;var c=t.memoizedState;l.state=c,Ti(t,r,l,i),u=t.memoizedState,s!==r||c!==u||xe.current||ht?(typeof f=="function"&&(Jl(t,n,f,r),u=t.memoizedState),(s=ht||ma(t,n,s,r,c,u,a))?(h||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=a,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Lf(e,t),s=t.memoizedProps,a=t.type===t.elementType?s:Ue(t.type,s),l.props=a,h=t.pendingProps,c=l.context,u=n.contextType,typeof u=="object"&&u!==null?u=ze(u):(u=Se(n)?Gt:pe.current,u=jn(t,u));var v=n.getDerivedStateFromProps;(f=typeof v=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==h||c!==u)&&ga(t,l,r,u),ht=!1,c=t.memoizedState,l.state=c,Ti(t,r,l,i);var g=t.memoizedState;s!==h||c!==g||xe.current||ht?(typeof v=="function"&&(Jl(t,n,v,r),g=t.memoizedState),(a=ht||ma(t,n,a,r,c,g,u)||!1)?(f||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,g,u),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,g,u)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),l.props=r,l.state=g,l.context=u,r=a):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),r=!1)}return ts(e,t,n,r,o,i)}function ts(e,t,n,r,i,o){nd(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return i&&sa(t,n,!1),ut(e,t,o);r=t.stateNode,rh.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=Rn(t,e.child,null,o),t.child=Rn(t,null,s,o)):he(e,t,s,o),t.memoizedState=r.state,i&&sa(t,n,!0),t.child}function rd(e){var t=e.stateNode;t.pendingContext?la(e,t.pendingContext,t.pendingContext!==t.context):t.context&&la(e,t.context,!1),Qs(e,t.containerInfo)}function Ca(e,t,n,r,i){return Ln(),bs(i),t.flags|=256,he(e,t,n,r),t.child}var ns={dehydrated:null,treeContext:null,retryLane:0};function rs(e){return{baseLanes:e,cachePool:null,transitions:null}}function id(e,t,n){var r=t.pendingProps,i=H.current,o=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(i&2)!==0),s?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),A(H,i&1),e===null)return Yl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,o?(r=t.mode,o=t.child,l={mode:"hidden",children:l},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=l):o=co(l,r,0,null),e=Qt(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=rs(n),t.memoizedState=ns,e):tu(t,l));if(i=e.memoizedState,i!==null&&(s=i.dehydrated,s!==null))return ih(e,t,l,r,s,i,n);if(o){o=r.fallback,l=t.mode,i=e.child,s=i.sibling;var u={mode:"hidden",children:r.children};return!(l&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Ot(i,u),r.subtreeFlags=i.subtreeFlags&14680064),s!==null?o=Ot(s,o):(o=Qt(o,l,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,l=e.child.memoizedState,l=l===null?rs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},o.memoizedState=l,o.childLanes=e.childLanes&~n,t.memoizedState=ns,r}return o=e.child,e=o.sibling,r=Ot(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function tu(e,t){return t=co({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Zr(e,t,n,r){return r!==null&&bs(r),Rn(t,e.child,null,n),e=tu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ih(e,t,n,r,i,o,l){if(n)return t.flags&256?(t.flags&=-257,r=sl(Error(C(422))),Zr(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=co({mode:"visible",children:r.children},i,0,null),o=Qt(o,i,l,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Rn(t,e.child,null,l),t.child.memoizedState=rs(l),t.memoizedState=ns,o);if(!(t.mode&1))return Zr(e,t,l,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var s=r.dgst;return r=s,o=Error(C(419)),r=sl(o,r,void 0),Zr(e,t,l,r)}if(s=(l&e.childLanes)!==0,we||s){if(r=re,r!==null){switch(l&-l){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|l)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,st(e,i),We(r,e,i,-1))}return su(),r=sl(Error(C(421))),Zr(e,t,l,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=vh.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ee=Ct(i.nextSibling),Pe=t,B=!0,Ve=null,e!==null&&(Re[$e++]=nt,Re[$e++]=rt,Re[$e++]=Yt,nt=e.id,rt=e.overflow,Yt=t),t=tu(t,r.children),t.flags|=4096,t)}function Ea(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Xl(e.return,t,n)}function ul(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function od(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(he(e,t,r.children,n),r=H.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ea(e,n,t);else if(e.tag===19)Ea(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(A(H,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Mi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ul(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Mi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ul(t,!0,n,null,o);break;case"together":ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function di(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ut(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Jt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Ot(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ot(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function oh(e,t,n){switch(t.tag){case 3:rd(t),Ln();break;case 5:Rf(t);break;case 1:Se(t.type)&&_i(t);break;case 4:Qs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;A(Ri,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(A(H,H.current&1),t.flags|=128,null):n&t.child.childLanes?id(e,t,n):(A(H,H.current&1),e=ut(e,t,n),e!==null?e.sibling:null);A(H,H.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return od(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),A(H,H.current),r)break;return null;case 22:case 23:return t.lanes=0,td(e,t,n)}return ut(e,t,n)}var ld,is,sd,ud;ld=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};is=function(){};sd=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Wt(Ze.current);var o=null;switch(n){case"input":i=Nl(e,i),r=Nl(e,r),o=[];break;case"select":i=K({},i,{value:void 0}),r=K({},r,{value:void 0}),o=[];break;case"textarea":i=jl(e,i),r=jl(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ni)}Rl(n,r);var l;n=null;for(a in i)if(!r.hasOwnProperty(a)&&i.hasOwnProperty(a)&&i[a]!=null)if(a==="style"){var s=i[a];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else a!=="dangerouslySetInnerHTML"&&a!=="children"&&a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(cr.hasOwnProperty(a)?o||(o=[]):(o=o||[]).push(a,null));for(a in r){var u=r[a];if(s=i!=null?i[a]:void 0,r.hasOwnProperty(a)&&u!==s&&(u!=null||s!=null))if(a==="style")if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(a,n)),n=u;else a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,s=s?s.__html:void 0,u!=null&&s!==u&&(o=o||[]).push(a,u)):a==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(a,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&(cr.hasOwnProperty(a)?(u!=null&&a==="onScroll"&&U("scroll",e),o||s===u||(o=[])):(o=o||[]).push(a,u))}n&&(o=o||[]).push("style",n);var a=o;(t.updateQueue=a)&&(t.flags|=4)}};ud=function(e,t,n,r){n!==r&&(t.flags|=4)};function Qn(e,t){if(!B)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function lh(e,t,n){var r=t.pendingProps;switch(Us(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ce(t),null;case 1:return Se(t.type)&&Oi(),ce(t),null;case 3:return r=t.stateNode,$n(),b(xe),b(pe),Ys(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Xr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ve!==null&&(ds(Ve),Ve=null))),is(e,t),ce(t),null;case 5:Gs(t);var i=Wt(kr.current);if(n=t.type,e!==null&&t.stateNode!=null)sd(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return ce(t),null}if(e=Wt(Ze.current),Xr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Ye]=t,r[xr]=o,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(i=0;i<qn.length;i++)U(qn[i],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":Tu(r,o),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},U("invalid",r);break;case"textarea":zu(r,o),U("invalid",r)}Rl(n,o),i=null;for(var l in o)if(o.hasOwnProperty(l)){var s=o[l];l==="children"?typeof s=="string"?r.textContent!==s&&(o.suppressHydrationWarning!==!0&&Yr(r.textContent,s,e),i=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(o.suppressHydrationWarning!==!0&&Yr(r.textContent,s,e),i=["children",""+s]):cr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&U("scroll",r)}switch(n){case"input":br(r),Mu(r,o,!0);break;case"textarea":br(r),Iu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ni)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=zc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[Ye]=t,e[xr]=r,ld(e,t,!1,!1),t.stateNode=e;e:{switch(l=$l(n,r),n){case"dialog":U("cancel",e),U("close",e),i=r;break;case"iframe":case"object":case"embed":U("load",e),i=r;break;case"video":case"audio":for(i=0;i<qn.length;i++)U(qn[i],e);i=r;break;case"source":U("error",e),i=r;break;case"img":case"image":case"link":U("error",e),U("load",e),i=r;break;case"details":U("toggle",e),i=r;break;case"input":Tu(e,r),i=Nl(e,r),U("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=K({},r,{value:void 0}),U("invalid",e);break;case"textarea":zu(e,r),i=jl(e,r),U("invalid",e);break;default:i=r}Rl(n,i),s=i;for(o in s)if(s.hasOwnProperty(o)){var u=s[o];o==="style"?Fc(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Ic(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&fr(e,u):typeof u=="number"&&fr(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(cr.hasOwnProperty(o)?u!=null&&o==="onScroll"&&U("scroll",e):u!=null&&Ps(e,o,u,l))}switch(n){case"input":br(e),Mu(e,r,!1);break;case"textarea":br(e),Iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Lt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?xn(e,!!r.multiple,o,!1):r.defaultValue!=null&&xn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ni)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ce(t),null;case 6:if(e&&t.stateNode!=null)ud(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=Wt(kr.current),Wt(Ze.current),Xr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ye]=t,(o=r.nodeValue!==n)&&(e=Pe,e!==null))switch(e.tag){case 3:Yr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Yr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ye]=t,t.stateNode=r}return ce(t),null;case 13:if(b(H),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(B&&Ee!==null&&t.mode&1&&!(t.flags&128))Nf(),Ln(),t.flags|=98560,o=!1;else if(o=Xr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(C(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(C(317));o[Ye]=t}else Ln(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ce(t),o=!1}else Ve!==null&&(ds(Ve),Ve=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||H.current&1?q===0&&(q=3):su())),t.updateQueue!==null&&(t.flags|=4),ce(t),null);case 4:return $n(),is(e,t),e===null&&yr(t.stateNode.containerInfo),ce(t),null;case 10:return Hs(t.type._context),ce(t),null;case 17:return Se(t.type)&&Oi(),ce(t),null;case 19:if(b(H),o=t.memoizedState,o===null)return ce(t),null;if(r=(t.flags&128)!==0,l=o.rendering,l===null)if(r)Qn(o,!1);else{if(q!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Mi(e),l!==null){for(t.flags|=128,Qn(o,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,l=o.alternate,l===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return A(H,H.current&1|2),t.child}e=e.sibling}o.tail!==null&&Y()>Mn&&(t.flags|=128,r=!0,Qn(o,!1),t.lanes=4194304)}else{if(!r)if(e=Mi(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Qn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!l.alternate&&!B)return ce(t),null}else 2*Y()-o.renderingStartTime>Mn&&n!==1073741824&&(t.flags|=128,r=!0,Qn(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(n=o.last,n!==null?n.sibling=l:t.child=l,o.last=l)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Y(),t.sibling=null,n=H.current,A(H,r?n&1|2:n&1),t):(ce(t),null);case 22:case 23:return lu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ce&1073741824&&(ce(t),t.subtreeFlags&6&&(t.flags|=8192)):ce(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function sh(e,t){switch(Us(t),t.tag){case 1:return Se(t.type)&&Oi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $n(),b(xe),b(pe),Ys(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Gs(t),null;case 13:if(b(H),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));Ln()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return b(H),null;case 4:return $n(),null;case 10:return Hs(t.type._context),null;case 22:case 23:return lu(),null;case 24:return null;default:return null}}var qr=!1,de=!1,uh=typeof WeakSet=="function"?WeakSet:Set,_=null;function yn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function os(e,t,n){try{n()}catch(r){Q(e,t,r)}}var Pa=!1;function ah(e,t){if(Vl=Ci,e=pf(),Fs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var l=0,s=-1,u=-1,a=0,f=0,h=e,c=null;t:for(;;){for(var v;h!==n||i!==0&&h.nodeType!==3||(s=l+i),h!==o||r!==0&&h.nodeType!==3||(u=l+r),h.nodeType===3&&(l+=h.nodeValue.length),(v=h.firstChild)!==null;)c=h,h=v;for(;;){if(h===e)break t;if(c===n&&++a===i&&(s=l),c===o&&++f===r&&(u=l),(v=h.nextSibling)!==null)break;h=c,c=h.parentNode}h=v}n=s===-1||u===-1?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Bl={focusedElem:e,selectionRange:n},Ci=!1,_=t;_!==null;)if(t=_,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,_=e;else for(;_!==null;){t=_;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var y=g.memoizedProps,E=g.memoizedState,m=t.stateNode,d=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:Ue(t.type,y),E);m.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){Q(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,_=e;break}_=t.return}return g=Pa,Pa=!1,g}function sr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&os(t,n,o)}i=i.next}while(i!==r)}}function uo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ls(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ad(e){var t=e.alternate;t!==null&&(e.alternate=null,ad(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ye],delete t[xr],delete t[Kl],delete t[W0],delete t[K0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function cd(e){return e.tag===5||e.tag===3||e.tag===4}function Na(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||cd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ni));else if(r!==4&&(e=e.child,e!==null))for(ss(e,t,n),e=e.sibling;e!==null;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(us(e,t,n),e=e.sibling;e!==null;)us(e,t,n),e=e.sibling}var le=null,be=!1;function dt(e,t,n){for(n=n.child;n!==null;)fd(e,t,n),n=n.sibling}function fd(e,t,n){if(Je&&typeof Je.onCommitFiberUnmount=="function")try{Je.onCommitFiberUnmount(eo,n)}catch{}switch(n.tag){case 5:de||yn(n,t);case 6:var r=le,i=be;le=null,dt(e,t,n),le=r,be=i,le!==null&&(be?(e=le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):le.removeChild(n.stateNode));break;case 18:le!==null&&(be?(e=le,n=n.stateNode,e.nodeType===8?tl(e.parentNode,n):e.nodeType===1&&tl(e,n),mr(e)):tl(le,n.stateNode));break;case 4:r=le,i=be,le=n.stateNode.containerInfo,be=!0,dt(e,t,n),le=r,be=i;break;case 0:case 11:case 14:case 15:if(!de&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,l=o.destroy;o=o.tag,l!==void 0&&(o&2||o&4)&&os(n,t,l),i=i.next}while(i!==r)}dt(e,t,n);break;case 1:if(!de&&(yn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Q(n,t,s)}dt(e,t,n);break;case 21:dt(e,t,n);break;case 22:n.mode&1?(de=(r=de)||n.memoizedState!==null,dt(e,t,n),de=r):dt(e,t,n);break;default:dt(e,t,n)}}function Oa(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new uh),t.forEach(function(r){var i=yh.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ae(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:le=s.stateNode,be=!1;break e;case 3:le=s.stateNode.containerInfo,be=!0;break e;case 4:le=s.stateNode.containerInfo,be=!0;break e}s=s.return}if(le===null)throw Error(C(160));fd(o,l,i),le=null,be=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(a){Q(i,t,a)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)dd(t,e),t=t.sibling}function dd(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ae(t,e),Qe(e),r&4){try{sr(3,e,e.return),uo(3,e)}catch(y){Q(e,e.return,y)}try{sr(5,e,e.return)}catch(y){Q(e,e.return,y)}}break;case 1:Ae(t,e),Qe(e),r&512&&n!==null&&yn(n,n.return);break;case 5:if(Ae(t,e),Qe(e),r&512&&n!==null&&yn(n,n.return),e.flags&32){var i=e.stateNode;try{fr(i,"")}catch(y){Q(e,e.return,y)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,l=n!==null?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{s==="input"&&o.type==="radio"&&o.name!=null&&Tc(i,o),$l(s,l);var a=$l(s,o);for(l=0;l<u.length;l+=2){var f=u[l],h=u[l+1];f==="style"?Fc(i,h):f==="dangerouslySetInnerHTML"?Ic(i,h):f==="children"?fr(i,h):Ps(i,f,h,a)}switch(s){case"input":Ol(i,o);break;case"textarea":Mc(i,o);break;case"select":var c=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var v=o.value;v!=null?xn(i,!!o.multiple,v,!1):c!==!!o.multiple&&(o.defaultValue!=null?xn(i,!!o.multiple,o.defaultValue,!0):xn(i,!!o.multiple,o.multiple?[]:"",!1))}i[xr]=o}catch(y){Q(e,e.return,y)}}break;case 6:if(Ae(t,e),Qe(e),r&4){if(e.stateNode===null)throw Error(C(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(y){Q(e,e.return,y)}}break;case 3:if(Ae(t,e),Qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{mr(t.containerInfo)}catch(y){Q(e,e.return,y)}break;case 4:Ae(t,e),Qe(e);break;case 13:Ae(t,e),Qe(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(iu=Y())),r&4&&Oa(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(de=(a=de)||f,Ae(t,e),de=a):Ae(t,e),Qe(e),r&8192){if(a=e.memoizedState!==null,(e.stateNode.isHidden=a)&&!f&&e.mode&1)for(_=e,f=e.child;f!==null;){for(h=_=f;_!==null;){switch(c=_,v=c.child,c.tag){case 0:case 11:case 14:case 15:sr(4,c,c.return);break;case 1:yn(c,c.return);var g=c.stateNode;if(typeof g.componentWillUnmount=="function"){r=c,n=c.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(y){Q(r,n,y)}}break;case 5:yn(c,c.return);break;case 22:if(c.memoizedState!==null){ja(h);continue}}v!==null?(v.return=c,_=v):ja(h)}f=f.sibling}e:for(f=null,h=e;;){if(h.tag===5){if(f===null){f=h;try{i=h.stateNode,a?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(s=h.stateNode,u=h.memoizedProps.style,l=u!=null&&u.hasOwnProperty("display")?u.display:null,s.style.display=Dc("display",l))}catch(y){Q(e,e.return,y)}}}else if(h.tag===6){if(f===null)try{h.stateNode.nodeValue=a?"":h.memoizedProps}catch(y){Q(e,e.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;f===h&&(f=null),h=h.return}f===h&&(f=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Ae(t,e),Qe(e),r&4&&Oa(e);break;case 21:break;default:Ae(t,e),Qe(e)}}function Qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(cd(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(fr(i,""),r.flags&=-33);var o=Na(e);us(e,o,i);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Na(e);ss(e,s,l);break;default:throw Error(C(161))}}catch(u){Q(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ch(e,t,n){_=e,pd(e)}function pd(e,t,n){for(var r=(e.mode&1)!==0;_!==null;){var i=_,o=i.child;if(i.tag===22&&r){var l=i.memoizedState!==null||qr;if(!l){var s=i.alternate,u=s!==null&&s.memoizedState!==null||de;s=qr;var a=de;if(qr=l,(de=u)&&!a)for(_=i;_!==null;)l=_,u=l.child,l.tag===22&&l.memoizedState!==null?La(i):u!==null?(u.return=l,_=u):La(i);for(;o!==null;)_=o,pd(o),o=o.sibling;_=i,qr=s,de=a}_a(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,_=o):_a(e)}}function _a(e){for(;_!==null;){var t=_;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:de||uo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!de)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ue(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&da(t,o,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}da(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var a=t.alternate;if(a!==null){var f=a.memoizedState;if(f!==null){var h=f.dehydrated;h!==null&&mr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}de||t.flags&512&&ls(t)}catch(c){Q(t,t.return,c)}}if(t===e){_=null;break}if(n=t.sibling,n!==null){n.return=t.return,_=n;break}_=t.return}}function ja(e){for(;_!==null;){var t=_;if(t===e){_=null;break}var n=t.sibling;if(n!==null){n.return=t.return,_=n;break}_=t.return}}function La(e){for(;_!==null;){var t=_;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{uo(4,t)}catch(u){Q(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){Q(t,i,u)}}var o=t.return;try{ls(t)}catch(u){Q(t,o,u)}break;case 5:var l=t.return;try{ls(t)}catch(u){Q(t,l,u)}}}catch(u){Q(t,t.return,u)}if(t===e){_=null;break}var s=t.sibling;if(s!==null){s.return=t.return,_=s;break}_=t.return}}var fh=Math.ceil,Di=ct.ReactCurrentDispatcher,nu=ct.ReactCurrentOwner,Me=ct.ReactCurrentBatchConfig,z=0,re=null,J=null,se=0,Ce=0,wn=zt(0),q=0,Nr=null,Jt=0,ao=0,ru=0,ur=null,ye=null,iu=0,Mn=1/0,et=null,Fi=!1,as=null,Pt=null,ei=!1,wt=null,Ai=0,ar=0,cs=null,pi=-1,hi=0;function me(){return z&6?Y():pi!==-1?pi:pi=Y()}function Nt(e){return e.mode&1?z&2&&se!==0?se&-se:G0.transition!==null?(hi===0&&(hi=Xc()),hi):(e=I,e!==0||(e=window.event,e=e===void 0?16:rf(e.type)),e):1}function We(e,t,n,r){if(50<ar)throw ar=0,cs=null,Error(C(185));Tr(e,n,r),(!(z&2)||e!==re)&&(e===re&&(!(z&2)&&(ao|=n),q===4&&gt(e,se)),ke(e,r),n===1&&z===0&&!(t.mode&1)&&(Mn=Y()+500,oo&&It()))}function ke(e,t){var n=e.callbackNode;Gp(e,t);var r=ki(e,e===re?se:0);if(r===0)n!==null&&Au(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Au(n),t===1)e.tag===0?Q0(Ra.bind(null,e)):Cf(Ra.bind(null,e)),B0(function(){!(z&6)&&It()}),n=null;else{switch(Jc(r)){case 1:n=Ls;break;case 4:n=Gc;break;case 16:n=Si;break;case 536870912:n=Yc;break;default:n=Si}n=Sd(n,hd.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function hd(e,t){if(pi=-1,hi=0,z&6)throw Error(C(327));var n=e.callbackNode;if(Pn()&&e.callbackNode!==n)return null;var r=ki(e,e===re?se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ui(e,r);else{t=r;var i=z;z|=2;var o=gd();(re!==e||se!==t)&&(et=null,Mn=Y()+500,Kt(e,t));do try{hh();break}catch(s){md(e,s)}while(!0);Bs(),Di.current=o,z=i,J!==null?t=0:(re=null,se=0,t=q)}if(t!==0){if(t===2&&(i=Dl(e),i!==0&&(r=i,t=fs(e,i))),t===1)throw n=Nr,Kt(e,0),gt(e,r),ke(e,Y()),n;if(t===6)gt(e,r);else{if(i=e.current.alternate,!(r&30)&&!dh(i)&&(t=Ui(e,r),t===2&&(o=Dl(e),o!==0&&(r=o,t=fs(e,o))),t===1))throw n=Nr,Kt(e,0),gt(e,r),ke(e,Y()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:bt(e,ye,et);break;case 3:if(gt(e,r),(r&130023424)===r&&(t=iu+500-Y(),10<t)){if(ki(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){me(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Wl(bt.bind(null,e,ye,et),t);break}bt(e,ye,et);break;case 4:if(gt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var l=31-He(r);o=1<<l,l=t[l],l>i&&(i=l),r&=~o}if(r=i,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*fh(r/1960))-r,10<r){e.timeoutHandle=Wl(bt.bind(null,e,ye,et),r);break}bt(e,ye,et);break;case 5:bt(e,ye,et);break;default:throw Error(C(329))}}}return ke(e,Y()),e.callbackNode===n?hd.bind(null,e):null}function fs(e,t){var n=ur;return e.current.memoizedState.isDehydrated&&(Kt(e,t).flags|=256),e=Ui(e,t),e!==2&&(t=ye,ye=n,t!==null&&ds(t)),e}function ds(e){ye===null?ye=e:ye.push.apply(ye,e)}function dh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Ke(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gt(e,t){for(t&=~ru,t&=~ao,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-He(t),r=1<<n;e[n]=-1,t&=~r}}function Ra(e){if(z&6)throw Error(C(327));Pn();var t=ki(e,0);if(!(t&1))return ke(e,Y()),null;var n=Ui(e,t);if(e.tag!==0&&n===2){var r=Dl(e);r!==0&&(t=r,n=fs(e,r))}if(n===1)throw n=Nr,Kt(e,0),gt(e,t),ke(e,Y()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,bt(e,ye,et),ke(e,Y()),null}function ou(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Mn=Y()+500,oo&&It())}}function Zt(e){wt!==null&&wt.tag===0&&!(z&6)&&Pn();var t=z;z|=1;var n=Me.transition,r=I;try{if(Me.transition=null,I=1,e)return e()}finally{I=r,Me.transition=n,z=t,!(z&6)&&It()}}function lu(){Ce=wn.current,b(wn)}function Kt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,V0(n)),J!==null)for(n=J.return;n!==null;){var r=n;switch(Us(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Oi();break;case 3:$n(),b(xe),b(pe),Ys();break;case 5:Gs(r);break;case 4:$n();break;case 13:b(H);break;case 19:b(H);break;case 10:Hs(r.type._context);break;case 22:case 23:lu()}n=n.return}if(re=e,J=e=Ot(e.current,null),se=Ce=t,q=0,Nr=null,ru=ao=Jt=0,ye=ur=null,Ht!==null){for(t=0;t<Ht.length;t++)if(n=Ht[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var l=o.next;o.next=i,r.next=l}n.pending=r}Ht=null}return e}function md(e,t){do{var n=J;try{if(Bs(),ci.current=Ii,zi){for(var r=W.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}zi=!1}if(Xt=0,ne=Z=W=null,lr=!1,Cr=0,nu.current=null,n===null||n.return===null){q=1,Nr=t,J=null;break}e:{var o=e,l=n.return,s=n,u=t;if(t=se,s.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var a=u,f=s,h=f.tag;if(!(f.mode&1)&&(h===0||h===11||h===15)){var c=f.alternate;c?(f.updateQueue=c.updateQueue,f.memoizedState=c.memoizedState,f.lanes=c.lanes):(f.updateQueue=null,f.memoizedState=null)}var v=ya(l);if(v!==null){v.flags&=-257,wa(v,l,s,o,t),v.mode&1&&va(o,a,t),t=v,u=a;var g=t.updateQueue;if(g===null){var y=new Set;y.add(u),t.updateQueue=y}else g.add(u);break e}else{if(!(t&1)){va(o,a,t),su();break e}u=Error(C(426))}}else if(B&&s.mode&1){var E=ya(l);if(E!==null){!(E.flags&65536)&&(E.flags|=256),wa(E,l,s,o,t),bs(Tn(u,s));break e}}o=u=Tn(u,s),q!==4&&(q=2),ur===null?ur=[o]:ur.push(o),o=l;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Zf(o,u,t);fa(o,m);break e;case 1:s=u;var d=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof d.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Pt===null||!Pt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=qf(o,s,t);fa(o,w);break e}}o=o.return}while(o!==null)}yd(n)}catch(x){t=x,J===n&&n!==null&&(J=n=n.return);continue}break}while(!0)}function gd(){var e=Di.current;return Di.current=Ii,e===null?Ii:e}function su(){(q===0||q===3||q===2)&&(q=4),re===null||!(Jt&268435455)&&!(ao&268435455)||gt(re,se)}function Ui(e,t){var n=z;z|=2;var r=gd();(re!==e||se!==t)&&(et=null,Kt(e,t));do try{ph();break}catch(i){md(e,i)}while(!0);if(Bs(),z=n,Di.current=r,J!==null)throw Error(C(261));return re=null,se=0,q}function ph(){for(;J!==null;)vd(J)}function hh(){for(;J!==null&&!Ap();)vd(J)}function vd(e){var t=xd(e.alternate,e,Ce);e.memoizedProps=e.pendingProps,t===null?yd(e):J=t,nu.current=null}function yd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=sh(n,t),n!==null){n.flags&=32767,J=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{q=6,J=null;return}}else if(n=lh(n,t,Ce),n!==null){J=n;return}if(t=t.sibling,t!==null){J=t;return}J=t=e}while(t!==null);q===0&&(q=5)}function bt(e,t,n){var r=I,i=Me.transition;try{Me.transition=null,I=1,mh(e,t,n,r)}finally{Me.transition=i,I=r}return null}function mh(e,t,n,r){do Pn();while(wt!==null);if(z&6)throw Error(C(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Yp(e,o),e===re&&(J=re=null,se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ei||(ei=!0,Sd(Si,function(){return Pn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Me.transition,Me.transition=null;var l=I;I=1;var s=z;z|=4,nu.current=null,ah(e,n),dd(n,e),z0(Bl),Ci=!!Vl,Bl=Vl=null,e.current=n,ch(n),Up(),z=s,I=l,Me.transition=o}else e.current=n;if(ei&&(ei=!1,wt=e,Ai=i),o=e.pendingLanes,o===0&&(Pt=null),Bp(n.stateNode),ke(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Fi)throw Fi=!1,e=as,as=null,e;return Ai&1&&e.tag!==0&&Pn(),o=e.pendingLanes,o&1?e===cs?ar++:(ar=0,cs=e):ar=0,It(),null}function Pn(){if(wt!==null){var e=Jc(Ai),t=Me.transition,n=I;try{if(Me.transition=null,I=16>e?16:e,wt===null)var r=!1;else{if(e=wt,wt=null,Ai=0,z&6)throw Error(C(331));var i=z;for(z|=4,_=e.current;_!==null;){var o=_,l=o.child;if(_.flags&16){var s=o.deletions;if(s!==null){for(var u=0;u<s.length;u++){var a=s[u];for(_=a;_!==null;){var f=_;switch(f.tag){case 0:case 11:case 15:sr(8,f,o)}var h=f.child;if(h!==null)h.return=f,_=h;else for(;_!==null;){f=_;var c=f.sibling,v=f.return;if(ad(f),f===a){_=null;break}if(c!==null){c.return=v,_=c;break}_=v}}}var g=o.alternate;if(g!==null){var y=g.child;if(y!==null){g.child=null;do{var E=y.sibling;y.sibling=null,y=E}while(y!==null)}}_=o}}if(o.subtreeFlags&2064&&l!==null)l.return=o,_=l;else e:for(;_!==null;){if(o=_,o.flags&2048)switch(o.tag){case 0:case 11:case 15:sr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,_=m;break e}_=o.return}}var d=e.current;for(_=d;_!==null;){l=_;var p=l.child;if(l.subtreeFlags&2064&&p!==null)p.return=l,_=p;else e:for(l=d;_!==null;){if(s=_,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:uo(9,s)}}catch(x){Q(s,s.return,x)}if(s===l){_=null;break e}var w=s.sibling;if(w!==null){w.return=s.return,_=w;break e}_=s.return}}if(z=i,It(),Je&&typeof Je.onPostCommitFiberRoot=="function")try{Je.onPostCommitFiberRoot(eo,e)}catch{}r=!0}return r}finally{I=n,Me.transition=t}}return!1}function $a(e,t,n){t=Tn(n,t),t=Zf(e,t,1),e=Et(e,t,1),t=me(),e!==null&&(Tr(e,1,t),ke(e,t))}function Q(e,t,n){if(e.tag===3)$a(e,e,n);else for(;t!==null;){if(t.tag===3){$a(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Pt===null||!Pt.has(r))){e=Tn(n,e),e=qf(t,e,1),t=Et(t,e,1),e=me(),t!==null&&(Tr(t,1,e),ke(t,e));break}}t=t.return}}function gh(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=me(),e.pingedLanes|=e.suspendedLanes&n,re===e&&(se&n)===n&&(q===4||q===3&&(se&130023424)===se&&500>Y()-iu?Kt(e,0):ru|=n),ke(e,t)}function wd(e,t){t===0&&(e.mode&1?(t=Hr,Hr<<=1,!(Hr&130023424)&&(Hr=4194304)):t=1);var n=me();e=st(e,t),e!==null&&(Tr(e,t,n),ke(e,n))}function vh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),wd(e,n)}function yh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),wd(e,n)}var xd;xd=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||xe.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,oh(e,t,n);we=!!(e.flags&131072)}else we=!1,B&&t.flags&1048576&&Ef(t,Li,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;di(e,t),e=t.pendingProps;var i=jn(t,pe.current);En(t,n),i=Js(null,t,r,e,i,n);var o=Zs();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Se(r)?(o=!0,_i(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ks(t),i.updater=so,t.stateNode=i,i._reactInternals=t,Zl(t,r,e,n),t=ts(null,t,r,!0,o,n)):(t.tag=0,B&&o&&As(t),he(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(di(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=xh(r),e=Ue(r,e),i){case 0:t=es(null,t,r,e,n);break e;case 1:t=ka(null,t,r,e,n);break e;case 11:t=xa(null,t,r,e,n);break e;case 14:t=Sa(null,t,r,Ue(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),es(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),ka(e,t,r,i,n);case 3:e:{if(rd(t),e===null)throw Error(C(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Lf(e,t),Ti(t,r,null,n);var l=t.memoizedState;if(r=l.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Tn(Error(C(423)),t),t=Ca(e,t,r,n,i);break e}else if(r!==i){i=Tn(Error(C(424)),t),t=Ca(e,t,r,n,i);break e}else for(Ee=Ct(t.stateNode.containerInfo.firstChild),Pe=t,B=!0,Ve=null,n=_f(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ln(),r===i){t=ut(e,t,n);break e}he(e,t,r,n)}t=t.child}return t;case 5:return Rf(t),e===null&&Yl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,l=i.children,Hl(r,i)?l=null:o!==null&&Hl(r,o)&&(t.flags|=32),nd(e,t),he(e,t,l,n),t.child;case 6:return e===null&&Yl(t),null;case 13:return id(e,t,n);case 4:return Qs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Rn(t,null,r,n):he(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),xa(e,t,r,i,n);case 7:return he(e,t,t.pendingProps,n),t.child;case 8:return he(e,t,t.pendingProps.children,n),t.child;case 12:return he(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,l=i.value,A(Ri,r._currentValue),r._currentValue=l,o!==null)if(Ke(o.value,l)){if(o.children===i.children&&!xe.current){t=ut(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var s=o.dependencies;if(s!==null){l=o.child;for(var u=s.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=it(-1,n&-n),u.tag=2;var a=o.updateQueue;if(a!==null){a=a.shared;var f=a.pending;f===null?u.next=u:(u.next=f.next,f.next=u),a.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),Xl(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(o.tag===10)l=o.type===t.type?null:o.child;else if(o.tag===18){if(l=o.return,l===null)throw Error(C(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Xl(l,n,t),l=o.sibling}else l=o.child;if(l!==null)l.return=o;else for(l=o;l!==null;){if(l===t){l=null;break}if(o=l.sibling,o!==null){o.return=l.return,l=o;break}l=l.return}o=l}he(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,En(t,n),i=ze(i),r=r(i),t.flags|=1,he(e,t,r,n),t.child;case 14:return r=t.type,i=Ue(r,t.pendingProps),i=Ue(r.type,i),Sa(e,t,r,i,n);case 15:return ed(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),di(e,t),t.tag=1,Se(r)?(e=!0,_i(t)):e=!1,En(t,n),Jf(t,r,i),Zl(t,r,i,n),ts(null,t,r,!0,e,n);case 19:return od(e,t,n);case 22:return td(e,t,n)}throw Error(C(156,t.tag))};function Sd(e,t){return Qc(e,t)}function wh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Te(e,t,n,r){return new wh(e,t,n,r)}function uu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xh(e){if(typeof e=="function")return uu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Os)return 11;if(e===_s)return 14}return 2}function Ot(e,t){var n=e.alternate;return n===null?(n=Te(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function mi(e,t,n,r,i,o){var l=2;if(r=e,typeof e=="function")uu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case an:return Qt(n.children,i,o,t);case Ns:l=8,i|=8;break;case kl:return e=Te(12,n,t,i|2),e.elementType=kl,e.lanes=o,e;case Cl:return e=Te(13,n,t,i),e.elementType=Cl,e.lanes=o,e;case El:return e=Te(19,n,t,i),e.elementType=El,e.lanes=o,e;case Lc:return co(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case _c:l=10;break e;case jc:l=9;break e;case Os:l=11;break e;case _s:l=14;break e;case pt:l=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=Te(l,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Qt(e,t,n,r){return e=Te(7,e,r,t),e.lanes=n,e}function co(e,t,n,r){return e=Te(22,e,r,t),e.elementType=Lc,e.lanes=n,e.stateNode={isHidden:!1},e}function al(e,t,n){return e=Te(6,e,null,t),e.lanes=n,e}function cl(e,t,n){return t=Te(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Sh(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ho(0),this.expirationTimes=Ho(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ho(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function au(e,t,n,r,i,o,l,s,u){return e=new Sh(e,t,n,s,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Te(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ks(o),e}function kh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:un,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function kd(e){if(!e)return Rt;e=e._reactInternals;e:{if(tn(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Se(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Se(n))return kf(e,n,t)}return t}function Cd(e,t,n,r,i,o,l,s,u){return e=au(n,r,!0,e,i,o,l,s,u),e.context=kd(null),n=e.current,r=me(),i=Nt(n),o=it(r,i),o.callback=t??null,Et(n,o,i),e.current.lanes=i,Tr(e,i,r),ke(e,r),e}function fo(e,t,n,r){var i=t.current,o=me(),l=Nt(i);return n=kd(n),t.context===null?t.context=n:t.pendingContext=n,t=it(o,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Et(i,t,l),e!==null&&(We(e,i,l,o),ai(e,i,l)),l}function bi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ta(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function cu(e,t){Ta(e,t),(e=e.alternate)&&Ta(e,t)}function Ch(){return null}var Ed=typeof reportError=="function"?reportError:function(e){console.error(e)};function fu(e){this._internalRoot=e}po.prototype.render=fu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));fo(e,t,null,null)};po.prototype.unmount=fu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Zt(function(){fo(null,e,null,null)}),t[lt]=null}};function po(e){this._internalRoot=e}po.prototype.unstable_scheduleHydration=function(e){if(e){var t=ef();e={blockedOn:null,target:e,priority:t};for(var n=0;n<mt.length&&t!==0&&t<mt[n].priority;n++);mt.splice(n,0,e),n===0&&nf(e)}};function du(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ho(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ma(){}function Eh(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var a=bi(l);o.call(a)}}var l=Cd(t,r,e,0,null,!1,!1,"",Ma);return e._reactRootContainer=l,e[lt]=l.current,yr(e.nodeType===8?e.parentNode:e),Zt(),l}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var s=r;r=function(){var a=bi(u);s.call(a)}}var u=au(e,0,!1,null,null,!1,!1,"",Ma);return e._reactRootContainer=u,e[lt]=u.current,yr(e.nodeType===8?e.parentNode:e),Zt(function(){fo(t,u,n,r)}),u}function mo(e,t,n,r,i){var o=n._reactRootContainer;if(o){var l=o;if(typeof i=="function"){var s=i;i=function(){var u=bi(l);s.call(u)}}fo(t,l,e,i)}else l=Eh(n,t,e,i,r);return bi(l)}Zc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Zn(t.pendingLanes);n!==0&&(Rs(t,n|1),ke(t,Y()),!(z&6)&&(Mn=Y()+500,It()))}break;case 13:Zt(function(){var r=st(e,1);if(r!==null){var i=me();We(r,e,1,i)}}),cu(e,1)}};$s=function(e){if(e.tag===13){var t=st(e,134217728);if(t!==null){var n=me();We(t,e,134217728,n)}cu(e,134217728)}};qc=function(e){if(e.tag===13){var t=Nt(e),n=st(e,t);if(n!==null){var r=me();We(n,e,t,r)}cu(e,t)}};ef=function(){return I};tf=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};Ml=function(e,t,n){switch(t){case"input":if(Ol(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=io(r);if(!i)throw Error(C(90));$c(r),Ol(r,i)}}}break;case"textarea":Mc(e,n);break;case"select":t=n.value,t!=null&&xn(e,!!n.multiple,t,!1)}};bc=ou;Vc=Zt;var Ph={usingClientEntryPoint:!1,Events:[zr,pn,io,Ac,Uc,ou]},Gn={findFiberByHostInstance:Bt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Nh={bundleType:Gn.bundleType,version:Gn.version,rendererPackageName:Gn.rendererPackageName,rendererConfig:Gn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ct.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Wc(e),e===null?null:e.stateNode},findFiberByHostInstance:Gn.findFiberByHostInstance||Ch,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ti=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ti.isDisabled&&ti.supportsFiber)try{eo=ti.inject(Nh),Je=ti}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ph;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!du(t))throw Error(C(200));return kh(e,t,null,n)};_e.createRoot=function(e,t){if(!du(e))throw Error(C(299));var n=!1,r="",i=Ed;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=au(e,1,!1,null,null,n,!1,r,i),e[lt]=t.current,yr(e.nodeType===8?e.parentNode:e),new fu(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=Wc(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return Zt(e)};_e.hydrate=function(e,t,n){if(!ho(t))throw Error(C(200));return mo(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!du(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",l=Ed;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Cd(t,null,e,1,n??null,i,!1,o,l),e[lt]=t.current,yr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new po(t)};_e.render=function(e,t,n){if(!ho(t))throw Error(C(200));return mo(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!ho(e))throw Error(C(40));return e._reactRootContainer?(Zt(function(){mo(null,null,e,!1,function(){e._reactRootContainer=null,e[lt]=null})}),!0):!1};_e.unstable_batchedUpdates=ou;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ho(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return mo(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function Pd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Pd)}catch(e){console.error(e)}}Pd(),Ec.exports=_e;var Nd=Ec.exports,Od,za=Nd;Od=za.createRoot,za.hydrateRoot;var _d={exports:{}},jd={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zn=$;function Oh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var _h=typeof Object.is=="function"?Object.is:Oh,jh=zn.useState,Lh=zn.useEffect,Rh=zn.useLayoutEffect,$h=zn.useDebugValue;function Th(e,t){var n=t(),r=jh({inst:{value:n,getSnapshot:t}}),i=r[0].inst,o=r[1];return Rh(function(){i.value=n,i.getSnapshot=t,fl(i)&&o({inst:i})},[e,n,t]),Lh(function(){return fl(i)&&o({inst:i}),e(function(){fl(i)&&o({inst:i})})},[e]),$h(n),n}function fl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!_h(e,n)}catch{return!0}}function Mh(e,t){return t()}var zh=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Mh:Th;jd.useSyncExternalStore=zn.useSyncExternalStore!==void 0?zn.useSyncExternalStore:zh;_d.exports=jd;var Ih=_d.exports,Ld={exports:{}},Rd={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var go=$,Dh=Ih;function Fh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ah=typeof Object.is=="function"?Object.is:Fh,Uh=Dh.useSyncExternalStore,bh=go.useRef,Vh=go.useEffect,Bh=go.useMemo,Hh=go.useDebugValue;Rd.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var o=bh(null);if(o.current===null){var l={hasValue:!1,value:null};o.current=l}else l=o.current;o=Bh(function(){function u(v){if(!a){if(a=!0,f=v,v=r(v),i!==void 0&&l.hasValue){var g=l.value;if(i(g,v))return h=g}return h=v}if(g=h,Ah(f,v))return g;var y=r(v);return i!==void 0&&i(g,y)?(f=v,g):(f=v,h=y)}var a=!1,f,h,c=n===void 0?null:n;return[function(){return u(t())},c===null?void 0:function(){return u(c())}]},[t,n,r,i]);var s=Uh(e,o[0],o[1]);return Vh(function(){l.hasValue=!0,l.value=s},[s]),Hh(s),s};Ld.exports=Rd;var Wh=Ld.exports;function Kh(e){e()}let $d=Kh;const Qh=e=>$d=e,Gh=()=>$d,Ia=Symbol.for("react-redux-context"),Da=typeof globalThis<"u"?globalThis:{};function Yh(){var e;if(!$.createContext)return{};const t=(e=Da[Ia])!=null?e:Da[Ia]=new Map;let n=t.get($.createContext);return n||(n=$.createContext(null),t.set($.createContext,n)),n}const $t=Yh();function pu(e=$t){return function(){return $.useContext(e)}}const Td=pu(),Xh=()=>{throw new Error("uSES not initialized!")};let Md=Xh;const Jh=e=>{Md=e},Zh=(e,t)=>e===t;function qh(e=$t){const t=e===$t?Td:pu(e);return function(r,i={}){const{equalityFn:o=Zh,stabilityCheck:l=void 0,noopCheck:s=void 0}=typeof i=="function"?{equalityFn:i}:i,{store:u,subscription:a,getServerState:f,stabilityCheck:h,noopCheck:c}=t();$.useRef(!0);const v=$.useCallback({[r.name](y){return r(y)}}[r.name],[r,h,l]),g=Md(a.addNestedSub,u.getState,f||u.getState,v,o);return $.useDebugValue(g),g}}const em=qh();var zd={exports:{}},D={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ie=typeof Symbol=="function"&&Symbol.for,hu=ie?Symbol.for("react.element"):60103,mu=ie?Symbol.for("react.portal"):60106,vo=ie?Symbol.for("react.fragment"):60107,yo=ie?Symbol.for("react.strict_mode"):60108,wo=ie?Symbol.for("react.profiler"):60114,xo=ie?Symbol.for("react.provider"):60109,So=ie?Symbol.for("react.context"):60110,gu=ie?Symbol.for("react.async_mode"):60111,ko=ie?Symbol.for("react.concurrent_mode"):60111,Co=ie?Symbol.for("react.forward_ref"):60112,Eo=ie?Symbol.for("react.suspense"):60113,tm=ie?Symbol.for("react.suspense_list"):60120,Po=ie?Symbol.for("react.memo"):60115,No=ie?Symbol.for("react.lazy"):60116,nm=ie?Symbol.for("react.block"):60121,rm=ie?Symbol.for("react.fundamental"):60117,im=ie?Symbol.for("react.responder"):60118,om=ie?Symbol.for("react.scope"):60119;function Le(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case hu:switch(e=e.type,e){case gu:case ko:case vo:case wo:case yo:case Eo:return e;default:switch(e=e&&e.$$typeof,e){case So:case Co:case No:case Po:case xo:return e;default:return t}}case mu:return t}}}function Id(e){return Le(e)===ko}D.AsyncMode=gu;D.ConcurrentMode=ko;D.ContextConsumer=So;D.ContextProvider=xo;D.Element=hu;D.ForwardRef=Co;D.Fragment=vo;D.Lazy=No;D.Memo=Po;D.Portal=mu;D.Profiler=wo;D.StrictMode=yo;D.Suspense=Eo;D.isAsyncMode=function(e){return Id(e)||Le(e)===gu};D.isConcurrentMode=Id;D.isContextConsumer=function(e){return Le(e)===So};D.isContextProvider=function(e){return Le(e)===xo};D.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===hu};D.isForwardRef=function(e){return Le(e)===Co};D.isFragment=function(e){return Le(e)===vo};D.isLazy=function(e){return Le(e)===No};D.isMemo=function(e){return Le(e)===Po};D.isPortal=function(e){return Le(e)===mu};D.isProfiler=function(e){return Le(e)===wo};D.isStrictMode=function(e){return Le(e)===yo};D.isSuspense=function(e){return Le(e)===Eo};D.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===vo||e===ko||e===wo||e===yo||e===Eo||e===tm||typeof e=="object"&&e!==null&&(e.$$typeof===No||e.$$typeof===Po||e.$$typeof===xo||e.$$typeof===So||e.$$typeof===Co||e.$$typeof===rm||e.$$typeof===im||e.$$typeof===om||e.$$typeof===nm)};D.typeOf=Le;zd.exports=D;var lm=zd.exports,Dd=lm,sm={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},um={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Fd={};Fd[Dd.ForwardRef]=sm;Fd[Dd.Memo]=um;var F={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vu=Symbol.for("react.element"),yu=Symbol.for("react.portal"),Oo=Symbol.for("react.fragment"),_o=Symbol.for("react.strict_mode"),jo=Symbol.for("react.profiler"),Lo=Symbol.for("react.provider"),Ro=Symbol.for("react.context"),am=Symbol.for("react.server_context"),$o=Symbol.for("react.forward_ref"),To=Symbol.for("react.suspense"),Mo=Symbol.for("react.suspense_list"),zo=Symbol.for("react.memo"),Io=Symbol.for("react.lazy"),cm=Symbol.for("react.offscreen"),Ad;Ad=Symbol.for("react.module.reference");function De(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case vu:switch(e=e.type,e){case Oo:case jo:case _o:case To:case Mo:return e;default:switch(e=e&&e.$$typeof,e){case am:case Ro:case $o:case Io:case zo:case Lo:return e;default:return t}}case yu:return t}}}F.ContextConsumer=Ro;F.ContextProvider=Lo;F.Element=vu;F.ForwardRef=$o;F.Fragment=Oo;F.Lazy=Io;F.Memo=zo;F.Portal=yu;F.Profiler=jo;F.StrictMode=_o;F.Suspense=To;F.SuspenseList=Mo;F.isAsyncMode=function(){return!1};F.isConcurrentMode=function(){return!1};F.isContextConsumer=function(e){return De(e)===Ro};F.isContextProvider=function(e){return De(e)===Lo};F.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===vu};F.isForwardRef=function(e){return De(e)===$o};F.isFragment=function(e){return De(e)===Oo};F.isLazy=function(e){return De(e)===Io};F.isMemo=function(e){return De(e)===zo};F.isPortal=function(e){return De(e)===yu};F.isProfiler=function(e){return De(e)===jo};F.isStrictMode=function(e){return De(e)===_o};F.isSuspense=function(e){return De(e)===To};F.isSuspenseList=function(e){return De(e)===Mo};F.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Oo||e===jo||e===_o||e===To||e===Mo||e===cm||typeof e=="object"&&e!==null&&(e.$$typeof===Io||e.$$typeof===zo||e.$$typeof===Lo||e.$$typeof===Ro||e.$$typeof===$o||e.$$typeof===Ad||e.getModuleId!==void 0)};F.typeOf=De;function fm(){const e=Gh();let t=null,n=null;return{clear(){t=null,n=null},notify(){e(()=>{let r=t;for(;r;)r.callback(),r=r.next})},get(){let r=[],i=t;for(;i;)r.push(i),i=i.next;return r},subscribe(r){let i=!0,o=n={callback:r,next:null,prev:n};return o.prev?o.prev.next=o:t=o,function(){!i||t===null||(i=!1,o.next?o.next.prev=o.prev:n=o.prev,o.prev?o.prev.next=o.next:t=o.next)}}}}const Fa={notify(){},get:()=>[]};function dm(e,t){let n,r=Fa,i=0,o=!1;function l(y){f();const E=r.subscribe(y);let m=!1;return()=>{m||(m=!0,E(),h())}}function s(){r.notify()}function u(){g.onStateChange&&g.onStateChange()}function a(){return o}function f(){i++,n||(n=e.subscribe(u),r=fm())}function h(){i--,n&&i===0&&(n(),n=void 0,r.clear(),r=Fa)}function c(){o||(o=!0,f())}function v(){o&&(o=!1,h())}const g={addNestedSub:l,notifyNestedSubs:s,handleChangeWrapper:u,isSubscribed:a,trySubscribe:c,tryUnsubscribe:v,getListeners:()=>r};return g}const pm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",hm=pm?$.useLayoutEffect:$.useEffect;function mm({store:e,context:t,children:n,serverState:r,stabilityCheck:i="once",noopCheck:o="once"}){const l=$.useMemo(()=>{const a=dm(e);return{store:e,subscription:a,getServerState:r?()=>r:void 0,stabilityCheck:i,noopCheck:o}},[e,r,i,o]),s=$.useMemo(()=>e.getState(),[e]);hm(()=>{const{subscription:a}=l;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),s!==e.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[l,s]);const u=t||$t;return $.createElement(u.Provider,{value:l},n)}function Ud(e=$t){const t=e===$t?Td:pu(e);return function(){const{store:r}=t();return r}}const gm=Ud();function vm(e=$t){const t=e===$t?gm:Ud(e);return function(){return t().dispatch}}const bd=vm();Jh(Wh.useSyncExternalStoreWithSelector);Qh(Nd.unstable_batchedUpdates);function Be(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map(function(i){return"'"+i+"'"}).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function Tt(e){return!!e&&!!e[V]}function at(e){var t;return!!e&&(function(n){if(!n||typeof n!="object")return!1;var r=Object.getPrototypeOf(n);if(r===null)return!0;var i=Object.hasOwnProperty.call(r,"constructor")&&r.constructor;return i===Object||typeof i=="function"&&Function.toString.call(i)===Nm}(e)||Array.isArray(e)||!!e[Wa]||!!(!((t=e.constructor)===null||t===void 0)&&t[Wa])||wu(e)||xu(e))}function qt(e,t,n){n===void 0&&(n=!1),Un(e)===0?(n?Object.keys:On)(e).forEach(function(r){n&&typeof r=="symbol"||t(r,e[r],e)}):e.forEach(function(r,i){return t(i,r,e)})}function Un(e){var t=e[V];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:wu(e)?2:xu(e)?3:0}function Nn(e,t){return Un(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ym(e,t){return Un(e)===2?e.get(t):e[t]}function Vd(e,t,n){var r=Un(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function Bd(e,t){return e===t?e!==0||1/e==1/t:e!=e&&t!=t}function wu(e){return Em&&e instanceof Map}function xu(e){return Pm&&e instanceof Set}function Vt(e){return e.o||e.t}function Su(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Wd(e);delete t[V];for(var n=On(t),r=0;r<n.length;r++){var i=n[r],o=t[i];o.writable===!1&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[i]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[i]})}return Object.create(Object.getPrototypeOf(e),t)}function ku(e,t){return t===void 0&&(t=!1),Cu(e)||Tt(e)||!at(e)||(Un(e)>1&&(e.set=e.add=e.clear=e.delete=wm),Object.freeze(e),t&&qt(e,function(n,r){return ku(r,!0)},!0)),e}function wm(){Be(2)}function Cu(e){return e==null||typeof e!="object"||Object.isFrozen(e)}function qe(e){var t=gs[e];return t||Be(18,e),t}function xm(e,t){gs[e]||(gs[e]=t)}function ps(){return Or}function dl(e,t){t&&(qe("Patches"),e.u=[],e.s=[],e.v=t)}function Vi(e){hs(e),e.p.forEach(Sm),e.p=null}function hs(e){e===Or&&(Or=e.l)}function Aa(e){return Or={p:[],l:Or,h:e,m:!0,_:0}}function Sm(e){var t=e[V];t.i===0||t.i===1?t.j():t.g=!0}function pl(e,t){t._=t.p.length;var n=t.p[0],r=e!==void 0&&e!==n;return t.h.O||qe("ES5").S(t,e,r),r?(n[V].P&&(Vi(t),Be(4)),at(e)&&(e=Bi(t,e),t.l||Hi(t,e)),t.u&&qe("Patches").M(n[V].t,e,t.u,t.s)):e=Bi(t,n,[]),Vi(t),t.u&&t.v(t.u,t.s),e!==Hd?e:void 0}function Bi(e,t,n){if(Cu(t))return t;var r=t[V];if(!r)return qt(t,function(s,u){return Ua(e,r,t,s,u,n)},!0),t;if(r.A!==e)return t;if(!r.P)return Hi(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var i=r.i===4||r.i===5?r.o=Su(r.k):r.o,o=i,l=!1;r.i===3&&(o=new Set(i),i.clear(),l=!0),qt(o,function(s,u){return Ua(e,r,i,s,u,n,l)}),Hi(e,i,!1),n&&e.u&&qe("Patches").N(r,n,e.u,e.s)}return r.o}function Ua(e,t,n,r,i,o,l){if(Tt(i)){var s=Bi(e,i,o&&t&&t.i!==3&&!Nn(t.R,r)?o.concat(r):void 0);if(Vd(n,r,s),!Tt(s))return;e.m=!1}else l&&n.add(i);if(at(i)&&!Cu(i)){if(!e.h.D&&e._<1)return;Bi(e,i),t&&t.A.l||Hi(e,i)}}function Hi(e,t,n){n===void 0&&(n=!1),!e.l&&e.h.D&&e.m&&ku(t,n)}function hl(e,t){var n=e[V];return(n?Vt(n):e)[t]}function ba(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function vt(e){e.P||(e.P=!0,e.l&&vt(e.l))}function ml(e){e.o||(e.o=Su(e.t))}function ms(e,t,n){var r=wu(t)?qe("MapSet").F(t,n):xu(t)?qe("MapSet").T(t,n):e.O?function(i,o){var l=Array.isArray(i),s={i:l?1:0,A:o?o.A:ps(),P:!1,I:!1,R:{},l:o,t:i,k:null,o:null,j:null,C:!1},u=s,a=_r;l&&(u=[s],a=er);var f=Proxy.revocable(u,a),h=f.revoke,c=f.proxy;return s.k=c,s.j=h,c}(t,n):qe("ES5").J(t,n);return(n?n.A:ps()).p.push(r),r}function km(e){return Tt(e)||Be(22,e),function t(n){if(!at(n))return n;var r,i=n[V],o=Un(n);if(i){if(!i.P&&(i.i<4||!qe("ES5").K(i)))return i.t;i.I=!0,r=Va(n,o),i.I=!1}else r=Va(n,o);return qt(r,function(l,s){i&&ym(i.t,l)===s||Vd(r,l,t(s))}),o===3?new Set(r):r}(e)}function Va(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return Su(e)}function Cm(){function e(o,l){var s=i[o];return s?s.enumerable=l:i[o]=s={configurable:!0,enumerable:l,get:function(){var u=this[V];return _r.get(u,o)},set:function(u){var a=this[V];_r.set(a,o,u)}},s}function t(o){for(var l=o.length-1;l>=0;l--){var s=o[l][V];if(!s.P)switch(s.i){case 5:r(s)&&vt(s);break;case 4:n(s)&&vt(s)}}}function n(o){for(var l=o.t,s=o.k,u=On(s),a=u.length-1;a>=0;a--){var f=u[a];if(f!==V){var h=l[f];if(h===void 0&&!Nn(l,f))return!0;var c=s[f],v=c&&c[V];if(v?v.t!==h:!Bd(c,h))return!0}}var g=!!l[V];return u.length!==On(l).length+(g?0:1)}function r(o){var l=o.k;if(l.length!==o.t.length)return!0;var s=Object.getOwnPropertyDescriptor(l,l.length-1);if(s&&!s.get)return!0;for(var u=0;u<l.length;u++)if(!l.hasOwnProperty(u))return!0;return!1}var i={};xm("ES5",{J:function(o,l){var s=Array.isArray(o),u=function(f,h){if(f){for(var c=Array(h.length),v=0;v<h.length;v++)Object.defineProperty(c,""+v,e(v,!0));return c}var g=Wd(h);delete g[V];for(var y=On(g),E=0;E<y.length;E++){var m=y[E];g[m]=e(m,f||!!g[m].enumerable)}return Object.create(Object.getPrototypeOf(h),g)}(s,o),a={i:s?5:4,A:l?l.A:ps(),P:!1,I:!1,R:{},l,t:o,k:u,o:null,g:!1,C:!1};return Object.defineProperty(u,V,{value:a,writable:!0}),u},S:function(o,l,s){s?Tt(l)&&l[V].A===o&&t(o.p):(o.u&&function u(a){if(a&&typeof a=="object"){var f=a[V];if(f){var h=f.t,c=f.k,v=f.R,g=f.i;if(g===4)qt(c,function(p){p!==V&&(h[p]!==void 0||Nn(h,p)?v[p]||u(c[p]):(v[p]=!0,vt(f)))}),qt(h,function(p){c[p]!==void 0||Nn(c,p)||(v[p]=!1,vt(f))});else if(g===5){if(r(f)&&(vt(f),v.length=!0),c.length<h.length)for(var y=c.length;y<h.length;y++)v[y]=!1;else for(var E=h.length;E<c.length;E++)v[E]=!0;for(var m=Math.min(c.length,h.length),d=0;d<m;d++)c.hasOwnProperty(d)||(v[d]=!0),v[d]===void 0&&u(c[d])}}}}(o.p[0]),t(o.p))},K:function(o){return o.i===4?n(o):r(o)}})}var Ba,Or,Eu=typeof Symbol<"u"&&typeof Symbol("x")=="symbol",Em=typeof Map<"u",Pm=typeof Set<"u",Ha=typeof Proxy<"u"&&Proxy.revocable!==void 0&&typeof Reflect<"u",Hd=Eu?Symbol.for("immer-nothing"):((Ba={})["immer-nothing"]=!0,Ba),Wa=Eu?Symbol.for("immer-draftable"):"__$immer_draftable",V=Eu?Symbol.for("immer-state"):"__$immer_state",Nm=""+Object.prototype.constructor,On=typeof Reflect<"u"&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols!==void 0?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Wd=Object.getOwnPropertyDescriptors||function(e){var t={};return On(e).forEach(function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)}),t},gs={},_r={get:function(e,t){if(t===V)return e;var n=Vt(e);if(!Nn(n,t))return function(i,o,l){var s,u=ba(o,l);return u?"value"in u?u.value:(s=u.get)===null||s===void 0?void 0:s.call(i.k):void 0}(e,n,t);var r=n[t];return e.I||!at(r)?r:r===hl(e.t,t)?(ml(e),e.o[t]=ms(e.A.h,r,e)):r},has:function(e,t){return t in Vt(e)},ownKeys:function(e){return Reflect.ownKeys(Vt(e))},set:function(e,t,n){var r=ba(Vt(e),t);if(r!=null&&r.set)return r.set.call(e.k,n),!0;if(!e.P){var i=hl(Vt(e),t),o=i==null?void 0:i[V];if(o&&o.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(Bd(n,i)&&(n!==void 0||Nn(e.t,t)))return!0;ml(e),vt(e)}return e.o[t]===n&&(n!==void 0||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return hl(e.t,t)!==void 0||t in e.t?(e.R[t]=!1,ml(e),vt(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=Vt(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.i!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty:function(){Be(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Be(12)}},er={};qt(_r,function(e,t){er[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),er.deleteProperty=function(e,t){return er.set.call(this,e,t,void 0)},er.set=function(e,t,n){return _r.set.call(this,e[0],t,n,e[0])};var Om=function(){function e(n){var r=this;this.O=Ha,this.D=!0,this.produce=function(i,o,l){if(typeof i=="function"&&typeof o!="function"){var s=o;o=i;var u=r;return function(y){var E=this;y===void 0&&(y=s);for(var m=arguments.length,d=Array(m>1?m-1:0),p=1;p<m;p++)d[p-1]=arguments[p];return u.produce(y,function(w){var x;return(x=o).call.apply(x,[E,w].concat(d))})}}var a;if(typeof o!="function"&&Be(6),l!==void 0&&typeof l!="function"&&Be(7),at(i)){var f=Aa(r),h=ms(r,i,void 0),c=!0;try{a=o(h),c=!1}finally{c?Vi(f):hs(f)}return typeof Promise<"u"&&a instanceof Promise?a.then(function(y){return dl(f,l),pl(y,f)},function(y){throw Vi(f),y}):(dl(f,l),pl(a,f))}if(!i||typeof i!="object"){if((a=o(i))===void 0&&(a=i),a===Hd&&(a=void 0),r.D&&ku(a,!0),l){var v=[],g=[];qe("Patches").M(i,a,v,g),l(v,g)}return a}Be(21,i)},this.produceWithPatches=function(i,o){if(typeof i=="function")return function(a){for(var f=arguments.length,h=Array(f>1?f-1:0),c=1;c<f;c++)h[c-1]=arguments[c];return r.produceWithPatches(a,function(v){return i.apply(void 0,[v].concat(h))})};var l,s,u=r.produce(i,o,function(a,f){l=a,s=f});return typeof Promise<"u"&&u instanceof Promise?u.then(function(a){return[a,l,s]}):[u,l,s]},typeof(n==null?void 0:n.useProxies)=="boolean"&&this.setUseProxies(n.useProxies),typeof(n==null?void 0:n.autoFreeze)=="boolean"&&this.setAutoFreeze(n.autoFreeze)}var t=e.prototype;return t.createDraft=function(n){at(n)||Be(8),Tt(n)&&(n=km(n));var r=Aa(this),i=ms(this,n,void 0);return i[V].C=!0,hs(r),i},t.finishDraft=function(n,r){var i=n&&n[V],o=i.A;return dl(o,r),pl(void 0,o)},t.setAutoFreeze=function(n){this.D=n},t.setUseProxies=function(n){n&&!Ha&&Be(20),this.O=n},t.applyPatches=function(n,r){var i;for(i=r.length-1;i>=0;i--){var o=r[i];if(o.path.length===0&&o.op==="replace"){n=o.value;break}}i>-1&&(r=r.slice(i+1));var l=qe("Patches").$;return Tt(n)?l(n,r):this.produce(n,function(s){return l(s,r)})},e}(),Oe=new Om,Kd=Oe.produce;Oe.produceWithPatches.bind(Oe);Oe.setAutoFreeze.bind(Oe);Oe.setUseProxies.bind(Oe);Oe.applyPatches.bind(Oe);Oe.createDraft.bind(Oe);Oe.finishDraft.bind(Oe);function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function _m(e,t){if(jr(e)!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(jr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function jm(e){var t=_m(e,"string");return jr(t)=="symbol"?t:t+""}function Lm(e,t,n){return(t=jm(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ka(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Qa(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ka(Object(n),!0).forEach(function(r){Lm(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ka(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function fe(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Ga=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),gl=function(){return Math.random().toString(36).substring(7).split("").join(".")},Wi={INIT:"@@redux/INIT"+gl(),REPLACE:"@@redux/REPLACE"+gl(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+gl()}};function Rm(e){if(typeof e!="object"||e===null)return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Qd(e,t,n){var r;if(typeof t=="function"&&typeof n=="function"||typeof n=="function"&&typeof arguments[3]=="function")throw new Error(fe(0));if(typeof t=="function"&&typeof n>"u"&&(n=t,t=void 0),typeof n<"u"){if(typeof n!="function")throw new Error(fe(1));return n(Qd)(e,t)}if(typeof e!="function")throw new Error(fe(2));var i=e,o=t,l=[],s=l,u=!1;function a(){s===l&&(s=l.slice())}function f(){if(u)throw new Error(fe(3));return o}function h(y){if(typeof y!="function")throw new Error(fe(4));if(u)throw new Error(fe(5));var E=!0;return a(),s.push(y),function(){if(E){if(u)throw new Error(fe(6));E=!1,a();var d=s.indexOf(y);s.splice(d,1),l=null}}}function c(y){if(!Rm(y))throw new Error(fe(7));if(typeof y.type>"u")throw new Error(fe(8));if(u)throw new Error(fe(9));try{u=!0,o=i(o,y)}finally{u=!1}for(var E=l=s,m=0;m<E.length;m++){var d=E[m];d()}return y}function v(y){if(typeof y!="function")throw new Error(fe(10));i=y,c({type:Wi.REPLACE})}function g(){var y,E=h;return y={subscribe:function(d){if(typeof d!="object"||d===null)throw new Error(fe(11));function p(){d.next&&d.next(f())}p();var w=E(p);return{unsubscribe:w}}},y[Ga]=function(){return this},y}return c({type:Wi.INIT}),r={dispatch:c,subscribe:h,getState:f,replaceReducer:v},r[Ga]=g,r}function $m(e){Object.keys(e).forEach(function(t){var n=e[t],r=n(void 0,{type:Wi.INIT});if(typeof r>"u")throw new Error(fe(12));if(typeof n(void 0,{type:Wi.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(fe(13))})}function Tm(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var i=t[r];typeof e[i]=="function"&&(n[i]=e[i])}var o=Object.keys(n),l;try{$m(n)}catch(s){l=s}return function(u,a){if(u===void 0&&(u={}),l)throw l;for(var f=!1,h={},c=0;c<o.length;c++){var v=o[c],g=n[v],y=u[v],E=g(y,a);if(typeof E>"u")throw a&&a.type,new Error(fe(14));h[v]=E,f=f||E!==y}return f=f||o.length!==Object.keys(u).length,f?h:u}}function Ki(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.length===0?function(r){return r}:t.length===1?t[0]:t.reduce(function(r,i){return function(){return r(i.apply(void 0,arguments))}})}function Mm(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return function(){var i=r.apply(void 0,arguments),o=function(){throw new Error(fe(15))},l={getState:i.getState,dispatch:function(){return o.apply(void 0,arguments)}},s=t.map(function(u){return u(l)});return o=Ki.apply(void 0,s)(i.dispatch),Qa(Qa({},i),{},{dispatch:o})}}}function Gd(e){var t=function(r){var i=r.dispatch,o=r.getState;return function(l){return function(s){return typeof s=="function"?s(i,o,e):l(s)}}};return t}var vs=Gd();vs.withExtraArgument=Gd;var Yd=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),zm=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,l;return l={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function s(a){return function(f){return u([a,f])}}function u(a){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(o=a[0]&2?i.return:a[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,a[1])).done)return o;switch(i=0,o&&(a=[a[0]&2,o.value]),a[0]){case 0:case 1:o=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,i=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){n=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){n.label=a[1];break}if(a[0]===6&&n.label<o[1]){n.label=o[1],o=a;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(a);break}o[2]&&n.ops.pop(),n.trys.pop();continue}a=t.call(e,n)}catch(f){a=[6,f],i=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},In=function(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e},Im=Object.defineProperty,Dm=Object.defineProperties,Fm=Object.getOwnPropertyDescriptors,Ya=Object.getOwnPropertySymbols,Am=Object.prototype.hasOwnProperty,Um=Object.prototype.propertyIsEnumerable,Xa=function(e,t,n){return t in e?Im(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},_t=function(e,t){for(var n in t||(t={}))Am.call(t,n)&&Xa(e,n,t[n]);if(Ya)for(var r=0,i=Ya(t);r<i.length;r++){var n=i[r];Um.call(t,n)&&Xa(e,n,t[n])}return e},vl=function(e,t){return Dm(e,Fm(t))},bm=function(e,t,n){return new Promise(function(r,i){var o=function(u){try{s(n.next(u))}catch(a){i(a)}},l=function(u){try{s(n.throw(u))}catch(a){i(a)}},s=function(u){return u.done?r(u.value):Promise.resolve(u.value).then(o,l)};s((n=n.apply(e,t)).next())})},Vm=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Ki:Ki.apply(null,arguments)};function Bm(e){if(typeof e!="object"||e===null)return!1;var t=Object.getPrototypeOf(e);if(t===null)return!0;for(var n=t;Object.getPrototypeOf(n)!==null;)n=Object.getPrototypeOf(n);return t===n}function jt(e,t){function n(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];if(t){var o=t.apply(void 0,r);if(!o)throw new Error("prepareAction did not return an object");return _t(_t({type:e,payload:o.payload},"meta"in o&&{meta:o.meta}),"error"in o&&{error:o.error})}return{type:e,payload:r[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(r){return r.type===e},n}var Hm=function(e){Yd(t,e);function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}return Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return e.prototype.concat.apply(this,n)},t.prototype.prepend=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return n.length===1&&Array.isArray(n[0])?new(t.bind.apply(t,In([void 0],n[0].concat(this)))):new(t.bind.apply(t,In([void 0],n.concat(this))))},t}(Array),Wm=function(e){Yd(t,e);function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}return Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return e.prototype.concat.apply(this,n)},t.prototype.prepend=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return n.length===1&&Array.isArray(n[0])?new(t.bind.apply(t,In([void 0],n[0].concat(this)))):new(t.bind.apply(t,In([void 0],n.concat(this))))},t}(Array);function ys(e){return at(e)?Kd(e,function(){}):e}function Km(e){return typeof e=="boolean"}function Qm(){return function(t){return Gm(t)}}function Gm(e){e===void 0&&(e={});var t=e.thunk,n=t===void 0?!0:t;e.immutableCheck,e.serializableCheck,e.actionCreatorCheck;var r=new Hm;return n&&(Km(n)?r.push(vs):r.push(vs.withExtraArgument(n.extraArgument))),r}function Ym(e){var t=Qm(),n=e||{},r=n.reducer,i=r===void 0?void 0:r,o=n.middleware,l=o===void 0?t():o,s=n.devTools,u=s===void 0?!0:s,a=n.preloadedState,f=a===void 0?void 0:a,h=n.enhancers,c=h===void 0?void 0:h,v;if(typeof i=="function")v=i;else if(Bm(i))v=Tm(i);else throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');var g=l;typeof g=="function"&&(g=g(t));var y=Mm.apply(void 0,g),E=Ki;u&&(E=Vm(_t({trace:!1},typeof u=="object"&&u)));var m=new Wm(y),d=m;Array.isArray(c)?d=In([y],c):typeof c=="function"&&(d=c(m));var p=E.apply(void 0,d);return Qd(v,f,p)}function Xd(e){var t={},n=[],r,i={addCase:function(o,l){var s=typeof o=="string"?o:o.type;if(!s)throw new Error("`builder.addCase` cannot be called with an empty action type");if(s in t)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return t[s]=l,i},addMatcher:function(o,l){return n.push({matcher:o,reducer:l}),i},addDefaultCase:function(o){return r=o,i}};return e(i),[t,n,r]}function Xm(e){return typeof e=="function"}function Jm(e,t,n,r){n===void 0&&(n=[]);var i=typeof t=="function"?Xd(t):[t,n,r],o=i[0],l=i[1],s=i[2],u;if(Xm(e))u=function(){return ys(e())};else{var a=ys(e);u=function(){return a}}function f(h,c){h===void 0&&(h=u());var v=In([o[c.type]],l.filter(function(g){var y=g.matcher;return y(c)}).map(function(g){var y=g.reducer;return y}));return v.filter(function(g){return!!g}).length===0&&(v=[s]),v.reduce(function(g,y){if(y)if(Tt(g)){var E=g,m=y(E,c);return m===void 0?g:m}else{if(at(g))return Kd(g,function(d){return y(d,c)});var m=y(g,c);if(m===void 0){if(g===null)return g;throw Error("A case reducer on a non-draftable value must not return undefined")}return m}return g},h)}return f.getInitialState=u,f}function Zm(e,t){return e+"/"+t}function qm(e){var t=e.name,n=typeof e.initialState=="function"?e.initialState:ys(e.initialState),r=e.reducers||{},i=Object.keys(r),o={},l={},s={};i.forEach(function(f){var h=r[f],c=Zm(t,f),v,g;"reducer"in h?(v=h.reducer,g=h.prepare):v=h,o[f]=v,l[c]=v,s[f]=g?jt(c,g):jt(c)});function u(){var f=typeof e.extraReducers=="function"?Xd(e.extraReducers):[e.extraReducers],h=f[0],c=h===void 0?{}:h,v=f[1],g=v===void 0?[]:v,y=f[2],E=y===void 0?void 0:y,m=_t(_t({},c),l);return Jm(n,function(d){for(var p in m)d.addCase(p,m[p]);for(var w=0,x=g;w<x.length;w++){var P=x[w];d.addMatcher(P.matcher,P.reducer)}E&&d.addDefaultCase(E)})}var a;return{name:t,reducer:function(f,h){return a||(a=u()),a(f,h)},actions:s,caseReducers:o,getInitialState:function(){return a||(a=u()),a.getInitialState()}}}var eg="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",tg=function(e){e===void 0&&(e=21);for(var t="",n=e;n--;)t+=eg[Math.random()*64|0];return t},ng=["name","message","stack","code"],yl=function(){function e(t,n){this.payload=t,this.meta=n}return e}(),Ja=function(){function e(t,n){this.payload=t,this.meta=n}return e}(),rg=function(e){if(typeof e=="object"&&e!==null){for(var t={},n=0,r=ng;n<r.length;n++){var i=r[n];typeof e[i]=="string"&&(t[i]=e[i])}return t}return{message:String(e)}};(function(){function e(t,n,r){var i=jt(t+"/fulfilled",function(a,f,h,c){return{payload:a,meta:vl(_t({},c||{}),{arg:h,requestId:f,requestStatus:"fulfilled"})}}),o=jt(t+"/pending",function(a,f,h){return{payload:void 0,meta:vl(_t({},h||{}),{arg:f,requestId:a,requestStatus:"pending"})}}),l=jt(t+"/rejected",function(a,f,h,c,v){return{payload:c,error:(r&&r.serializeError||rg)(a||"Rejected"),meta:vl(_t({},v||{}),{arg:h,requestId:f,rejectedWithValue:!!c,requestStatus:"rejected",aborted:(a==null?void 0:a.name)==="AbortError",condition:(a==null?void 0:a.name)==="ConditionError"})}}),s=typeof AbortController<"u"?AbortController:function(){function a(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return a.prototype.abort=function(){},a}();function u(a){return function(f,h,c){var v=r!=null&&r.idGenerator?r.idGenerator(a):tg(),g=new s,y;function E(d){y=d,g.abort()}var m=function(){return bm(this,null,function(){var d,p,w,x,P,k,N;return zm(this,function(M){switch(M.label){case 0:return M.trys.push([0,4,,5]),x=(d=r==null?void 0:r.condition)==null?void 0:d.call(r,a,{getState:h,extra:c}),og(x)?[4,x]:[3,2];case 1:x=M.sent(),M.label=2;case 2:if(x===!1||g.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return P=new Promise(function(j,X){return g.signal.addEventListener("abort",function(){return X({name:"AbortError",message:y||"Aborted"})})}),f(o(v,a,(p=r==null?void 0:r.getPendingMeta)==null?void 0:p.call(r,{requestId:v,arg:a},{getState:h,extra:c}))),[4,Promise.race([P,Promise.resolve(n(a,{dispatch:f,getState:h,extra:c,requestId:v,signal:g.signal,abort:E,rejectWithValue:function(j,X){return new yl(j,X)},fulfillWithValue:function(j,X){return new Ja(j,X)}})).then(function(j){if(j instanceof yl)throw j;return j instanceof Ja?i(j.payload,v,a,j.meta):i(j,v,a)})])];case 3:return w=M.sent(),[3,5];case 4:return k=M.sent(),w=k instanceof yl?l(null,v,a,k.payload,k.meta):l(k,v,a),[3,5];case 5:return N=r&&!r.dispatchConditionRejection&&l.match(w)&&w.meta.condition,N||f(w),[2,w]}})})}();return Object.assign(m,{abort:E,requestId:v,arg:a,unwrap:function(){return m.then(ig)}})}}return Object.assign(u,{pending:o,rejected:l,fulfilled:i,typePrefix:t})}return e.withTypes=function(){return e},e})();function ig(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function og(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var Pu="listenerMiddleware";jt(Pu+"/add");jt(Pu+"/removeAll");jt(Pu+"/remove");var Za;typeof queueMicrotask=="function"&&queueMicrotask.bind(typeof window<"u"?window:typeof global<"u"?global:globalThis);Cm();const lg="/assets/Feel-It-DN7DVGY1.mp3",sg=Object.freeze(Object.defineProperty({__proto__:null,default:lg},Symbol.toStringTag,{value:"Module"})),ug="/assets/Goodie-Bag-ODYgd-Mn.mp3",ag=Object.freeze(Object.defineProperty({__proto__:null,default:ug},Symbol.toStringTag,{value:"Module"})),cg="/assets/Ivy-DCgUs2Pq.mp3",fg=Object.freeze(Object.defineProperty({__proto__:null,default:cg},Symbol.toStringTag,{value:"Module"})),dg="/assets/Playboi-Carti-NO-9-REMIX-B4t7onom.mp3",pg=Object.freeze(Object.defineProperty({__proto__:null,default:dg},Symbol.toStringTag,{value:"Module"})),hg="/assets/Timeless-Cc6XckVF.mp3",mg=Object.freeze(Object.defineProperty({__proto__:null,default:hg},Symbol.toStringTag,{value:"Module"})),gg="/assets/Told-ya-Ca2lSNdt.mp3",vg=Object.freeze(Object.defineProperty({__proto__:null,default:gg},Symbol.toStringTag,{value:"Module"})),yg={"Playboi-Carti-NO-9-REMIX":"Playboi Carti - NO. 9 (Remix)","Feel-It":"D4vd - Feel It","Goodie-Bag":"Still Woozy - Goodie Bag",Timeless:"The Weekend - Timeless (feat. Playboi Carti)",Ivy:"Frank Ocean - Ivy","Told-ya":"Told Ya - Yeat"};function wg(){const t=Object.entries(Object.assign({"../assets/music/Feel-It.mp3":sg,"../assets/music/Goodie-Bag.mp3":ag,"../assets/music/Ivy.mp3":fg,"../assets/music/Playboi-Carti-NO-9-REMIX.mp3":pg,"../assets/music/Timeless.mp3":mg,"../assets/music/Told-ya.mp3":vg})).map(([r,i])=>{var l;const o=((l=r.split("/").pop())==null?void 0:l.replace(".mp3",""))||"";return{path:i.default,name:yg[o]||o}}),n=Math.floor(Math.random()*t.length);return t[n]}const xg={isPlaying:!1,volume:.5,currentSong:wg()},Jd=qm({name:"audio",initialState:xg,reducers:{togglePlay:e=>{e.isPlaying=!e.isPlaying},setVolume:(e,t)=>{e.volume=t.payload},setCurrentSong:(e,t)=>{e.currentSong=t.payload}}}),{togglePlay:gi,setVolume:ni}=Jd.actions,Sg=Jd.reducer,kg=Ym({reducer:{audio:Sg}}),Cg={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class Qi{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(t,n)}init(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=n.prefix||"i18next:",this.logger=t||Cg,this.options=n,this.debug=n.debug}log(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"log","",!0)}warn(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"warn","",!0)}error(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"error","")}deprecate(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"warn","WARNING DEPRECATED: ",!0)}forward(t,n,r,i){return i&&!this.debug?null:(typeof t[0]=="string"&&(t[0]=`${r}${this.prefix} ${t[0]}`),this.logger[n](t))}create(t){return new Qi(this.logger,{prefix:`${this.prefix}:${t}:`,...this.options})}clone(t){return t=t||this.options,t.prefix=t.prefix||this.prefix,new Qi(this.logger,t)}}var Xe=new Qi;class Do{constructor(){this.observers={}}on(t,n){return t.split(" ").forEach(r=>{this.observers[r]=this.observers[r]||[],this.observers[r].push(n)}),this}off(t,n){if(this.observers[t]){if(!n){delete this.observers[t];return}this.observers[t]=this.observers[t].filter(r=>r!==n)}}emit(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];this.observers[t]&&[].concat(this.observers[t]).forEach(l=>{l(...r)}),this.observers["*"]&&[].concat(this.observers["*"]).forEach(l=>{l.apply(l,[t,...r])})}}function Yn(){let e,t;const n=new Promise((r,i)=>{e=r,t=i});return n.resolve=e,n.reject=t,n}function qa(e){return e==null?"":""+e}function Eg(e,t,n){e.forEach(r=>{t[r]&&(n[r]=t[r])})}function Nu(e,t,n){function r(l){return l&&l.indexOf("###")>-1?l.replace(/###/g,"."):l}function i(){return!e||typeof e=="string"}const o=typeof t!="string"?[].concat(t):t.split(".");for(;o.length>1;){if(i())return{};const l=r(o.shift());!e[l]&&n&&(e[l]=new n),Object.prototype.hasOwnProperty.call(e,l)?e=e[l]:e={}}return i()?{}:{obj:e,k:r(o.shift())}}function ec(e,t,n){const{obj:r,k:i}=Nu(e,t,Object);r[i]=n}function Pg(e,t,n,r){const{obj:i,k:o}=Nu(e,t,Object);i[o]=i[o]||[],i[o].push(n)}function Gi(e,t){const{obj:n,k:r}=Nu(e,t);if(n)return n[r]}function Ng(e,t,n){const r=Gi(e,n);return r!==void 0?r:Gi(t,n)}function Zd(e,t,n){for(const r in t)r!=="__proto__"&&r!=="constructor"&&(r in e?typeof e[r]=="string"||e[r]instanceof String||typeof t[r]=="string"||t[r]instanceof String?n&&(e[r]=t[r]):Zd(e[r],t[r],n):e[r]=t[r]);return e}function ln(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var Og={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function _g(e){return typeof e=="string"?e.replace(/[&<>"'\/]/g,t=>Og[t]):e}const jg=[" ",",","?","!",";"];function Lg(e,t,n){t=t||"",n=n||"";const r=jg.filter(l=>t.indexOf(l)<0&&n.indexOf(l)<0);if(r.length===0)return!0;const i=new RegExp(`(${r.map(l=>l==="?"?"\\?":l).join("|")})`);let o=!i.test(e);if(!o){const l=e.indexOf(n);l>0&&!i.test(e.substring(0,l))&&(o=!0)}return o}function Yi(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!e)return;if(e[t])return e[t];const r=t.split(n);let i=e;for(let o=0;o<r.length;++o){if(!i||typeof i[r[o]]=="string"&&o+1<r.length)return;if(i[r[o]]===void 0){let l=2,s=r.slice(o,o+l).join(n),u=i[s];for(;u===void 0&&r.length>o+l;)l++,s=r.slice(o,o+l).join(n),u=i[s];if(u===void 0)return;if(u===null)return null;if(t.endsWith(s)){if(typeof u=="string")return u;if(s&&typeof u[s]=="string")return u[s]}const a=r.slice(o+l).join(n);return a?Yi(u,a,n):void 0}i=i[r[o]]}return i}function Xi(e){return e&&e.indexOf("_")>0?e.replace("_","-"):e}class tc extends Do{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=t||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(t){this.options.ns.indexOf(t)<0&&this.options.ns.push(t)}removeNamespaces(t){const n=this.options.ns.indexOf(t);n>-1&&this.options.ns.splice(n,1)}getResource(t,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const o=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,l=i.ignoreJSONStructure!==void 0?i.ignoreJSONStructure:this.options.ignoreJSONStructure;let s=[t,n];r&&typeof r!="string"&&(s=s.concat(r)),r&&typeof r=="string"&&(s=s.concat(o?r.split(o):r)),t.indexOf(".")>-1&&(s=t.split("."));const u=Gi(this.data,s);return u||!l||typeof r!="string"?u:Yi(this.data&&this.data[t]&&this.data[t][n],r,o)}addResource(t,n,r,i){let o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const l=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let s=[t,n];r&&(s=s.concat(l?r.split(l):r)),t.indexOf(".")>-1&&(s=t.split("."),i=n,n=s[1]),this.addNamespaces(n),ec(this.data,s,i),o.silent||this.emit("added",t,n,r,i)}addResources(t,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const o in r)(typeof r[o]=="string"||Object.prototype.toString.apply(r[o])==="[object Array]")&&this.addResource(t,n,o,r[o],{silent:!0});i.silent||this.emit("added",t,n,r)}addResourceBundle(t,n,r,i,o){let l=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1},s=[t,n];t.indexOf(".")>-1&&(s=t.split("."),i=r,r=n,n=s[1]),this.addNamespaces(n);let u=Gi(this.data,s)||{};i?Zd(u,r,o):u={...u,...r},ec(this.data,s,u),l.silent||this.emit("added",t,n,r)}removeResourceBundle(t,n){this.hasResourceBundle(t,n)&&delete this.data[t][n],this.removeNamespaces(n),this.emit("removed",t,n)}hasResourceBundle(t,n){return this.getResource(t,n)!==void 0}getResourceBundle(t,n){return n||(n=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(t,n)}:this.getResource(t,n)}getDataByLanguage(t){return this.data[t]}hasLanguageSomeTranslations(t){const n=this.getDataByLanguage(t);return!!(n&&Object.keys(n)||[]).find(i=>n[i]&&Object.keys(n[i]).length>0)}toJSON(){return this.data}}var qd={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,i){return e.forEach(o=>{this.processors[o]&&(t=this.processors[o].process(t,n,r,i))}),t}};const nc={};class Ji extends Do{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Eg(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],t,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Xe.create("translator")}changeLanguage(t){t&&(this.language=t)}exists(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(t==null)return!1;const r=this.resolve(t,n);return r&&r.res!==void 0}extractFromKey(t,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let o=n.ns||this.options.defaultNS||[];const l=r&&t.indexOf(r)>-1,s=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!Lg(t,r,i);if(l&&!s){const u=t.match(this.interpolator.nestingRegexp);if(u&&u.length>0)return{key:t,namespaces:o};const a=t.split(r);(r!==i||r===i&&this.options.ns.indexOf(a[0])>-1)&&(o=a.shift()),t=a.join(i)}return typeof o=="string"&&(o=[o]),{key:t,namespaces:o}}translate(t,n,r){if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof n=="object"&&(n={...n}),n||(n={}),t==null)return"";Array.isArray(t)||(t=[String(t)]);const i=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:l,namespaces:s}=this.extractFromKey(t[t.length-1],n),u=s[s.length-1],a=n.lng||this.language,f=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(a&&a.toLowerCase()==="cimode"){if(f){const w=n.nsSeparator||this.options.nsSeparator;return i?{res:`${u}${w}${l}`,usedKey:l,exactUsedKey:l,usedLng:a,usedNS:u,usedParams:this.getUsedParamsDetails(n)}:`${u}${w}${l}`}return i?{res:l,usedKey:l,exactUsedKey:l,usedLng:a,usedNS:u,usedParams:this.getUsedParamsDetails(n)}:l}const h=this.resolve(t,n);let c=h&&h.res;const v=h&&h.usedKey||l,g=h&&h.exactUsedKey||l,y=Object.prototype.toString.apply(c),E=["[object Number]","[object Function]","[object RegExp]"],m=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,d=!this.i18nFormat||this.i18nFormat.handleAsObject;if(d&&c&&(typeof c!="string"&&typeof c!="boolean"&&typeof c!="number")&&E.indexOf(y)<0&&!(typeof m=="string"&&y==="[object Array]")){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const w=this.options.returnedObjectHandler?this.options.returnedObjectHandler(v,c,{...n,ns:s}):`key '${l} (${this.language})' returned an object instead of string.`;return i?(h.res=w,h.usedParams=this.getUsedParamsDetails(n),h):w}if(o){const w=y==="[object Array]",x=w?[]:{},P=w?g:v;for(const k in c)if(Object.prototype.hasOwnProperty.call(c,k)){const N=`${P}${o}${k}`;x[k]=this.translate(N,{...n,joinArrays:!1,ns:s}),x[k]===N&&(x[k]=c[k])}c=x}}else if(d&&typeof m=="string"&&y==="[object Array]")c=c.join(m),c&&(c=this.extendTranslation(c,t,n,r));else{let w=!1,x=!1;const P=n.count!==void 0&&typeof n.count!="string",k=Ji.hasDefaultValue(n),N=P?this.pluralResolver.getSuffix(a,n.count,n):"",M=n.ordinal&&P?this.pluralResolver.getSuffix(a,n.count,{ordinal:!1}):"",j=n[`defaultValue${N}`]||n[`defaultValue${M}`]||n.defaultValue;!this.isValidLookup(c)&&k&&(w=!0,c=j),this.isValidLookup(c)||(x=!0,c=l);const Dt=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&x?void 0:c,Fe=k&&j!==c&&this.options.updateMissing;if(x||w||Fe){if(this.logger.log(Fe?"updateKey":"missingKey",a,u,l,Fe?j:c),o){const oe=this.resolve(l,{...n,keySeparator:!1});oe&&oe.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let ft=[];const nn=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&nn&&nn[0])for(let oe=0;oe<nn.length;oe++)ft.push(nn[oe]);else this.options.saveMissingTo==="all"?ft=this.languageUtils.toResolveHierarchy(n.lng||this.language):ft.push(n.lng||this.language);const rn=(oe,O,L)=>{const R=k&&L!==c?L:Dt;this.options.missingKeyHandler?this.options.missingKeyHandler(oe,u,O,R,Fe,n):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(oe,u,O,R,Fe,n),this.emit("missingKey",oe,u,O,c)};this.options.saveMissing&&(this.options.saveMissingPlurals&&P?ft.forEach(oe=>{this.pluralResolver.getSuffixes(oe,n).forEach(O=>{rn([oe],l+O,n[`defaultValue${O}`]||j)})}):rn(ft,l,j))}c=this.extendTranslation(c,t,n,h,r),x&&c===l&&this.options.appendNamespaceToMissingKey&&(c=`${u}:${l}`),(x||w)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?c=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}:${l}`:l,w?c:void 0):c=this.options.parseMissingKeyHandler(c))}return i?(h.res=c,h.usedParams=this.getUsedParamsDetails(n),h):c}extendTranslation(t,n,r,i,o){var l=this;if(this.i18nFormat&&this.i18nFormat.parse)t=this.i18nFormat.parse(t,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const a=typeof t=="string"&&(r&&r.interpolation&&r.interpolation.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let f;if(a){const c=t.match(this.interpolator.nestingRegexp);f=c&&c.length}let h=r.replace&&typeof r.replace!="string"?r.replace:r;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),t=this.interpolator.interpolate(t,h,r.lng||this.language,r),a){const c=t.match(this.interpolator.nestingRegexp),v=c&&c.length;f<v&&(r.nest=!1)}!r.lng&&this.options.compatibilityAPI!=="v1"&&i&&i.res&&(r.lng=i.usedLng),r.nest!==!1&&(t=this.interpolator.nest(t,function(){for(var c=arguments.length,v=new Array(c),g=0;g<c;g++)v[g]=arguments[g];return o&&o[0]===v[0]&&!r.context?(l.logger.warn(`It seems you are nesting recursively key: ${v[0]} in key: ${n[0]}`),null):l.translate(...v,n)},r)),r.interpolation&&this.interpolator.reset()}const s=r.postProcess||this.options.postProcess,u=typeof s=="string"?[s]:s;return t!=null&&u&&u.length&&r.applyPostProcessor!==!1&&(t=qd.handle(u,t,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),t}resolve(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,i,o,l,s;return typeof t=="string"&&(t=[t]),t.forEach(u=>{if(this.isValidLookup(r))return;const a=this.extractFromKey(u,n),f=a.key;i=f;let h=a.namespaces;this.options.fallbackNS&&(h=h.concat(this.options.fallbackNS));const c=n.count!==void 0&&typeof n.count!="string",v=c&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),g=n.context!==void 0&&(typeof n.context=="string"||typeof n.context=="number")&&n.context!=="",y=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);h.forEach(E=>{this.isValidLookup(r)||(s=E,!nc[`${y[0]}-${E}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(s)&&(nc[`${y[0]}-${E}`]=!0,this.logger.warn(`key "${i}" for languages "${y.join(", ")}" won't get resolved as namespace "${s}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),y.forEach(m=>{if(this.isValidLookup(r))return;l=m;const d=[f];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(d,f,m,E,n);else{let w;c&&(w=this.pluralResolver.getSuffix(m,n.count,n));const x=`${this.options.pluralSeparator}zero`,P=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(d.push(f+w),n.ordinal&&w.indexOf(P)===0&&d.push(f+w.replace(P,this.options.pluralSeparator)),v&&d.push(f+x)),g){const k=`${f}${this.options.contextSeparator}${n.context}`;d.push(k),c&&(d.push(k+w),n.ordinal&&w.indexOf(P)===0&&d.push(k+w.replace(P,this.options.pluralSeparator)),v&&d.push(k+x))}}let p;for(;p=d.pop();)this.isValidLookup(r)||(o=p,r=this.getResource(m,E,p,n))}))})}),{res:r,usedKey:i,exactUsedKey:o,usedLng:l,usedNS:s}}isValidLookup(t){return t!==void 0&&!(!this.options.returnNull&&t===null)&&!(!this.options.returnEmptyString&&t==="")}getResource(t,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(t,n,r,i):this.resourceStore.getResource(t,n,r,i)}getUsedParamsDetails(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=t.replace&&typeof t.replace!="string";let i=r?t.replace:t;if(r&&typeof t.count<"u"&&(i.count=t.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!r){i={...i};for(const o of n)delete i[o]}return i}static hasDefaultValue(t){const n="defaultValue";for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&n===r.substring(0,n.length)&&t[r]!==void 0)return!0;return!1}}function wl(e){return e.charAt(0).toUpperCase()+e.slice(1)}class rc{constructor(t){this.options=t,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Xe.create("languageUtils")}getScriptPartFromCode(t){if(t=Xi(t),!t||t.indexOf("-")<0)return null;const n=t.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(t){if(t=Xi(t),!t||t.indexOf("-")<0)return t;const n=t.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(t){if(typeof t=="string"&&t.indexOf("-")>-1){const n=["hans","hant","latn","cyrl","cans","mong","arab"];let r=t.split("-");return this.options.lowerCaseLng?r=r.map(i=>i.toLowerCase()):r.length===2?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=wl(r[1].toLowerCase()))):r.length===3&&(r[0]=r[0].toLowerCase(),r[1].length===2&&(r[1]=r[1].toUpperCase()),r[0]!=="sgn"&&r[2].length===2&&(r[2]=r[2].toUpperCase()),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=wl(r[1].toLowerCase())),n.indexOf(r[2].toLowerCase())>-1&&(r[2]=wl(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(t){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(t=this.getLanguagePartFromCode(t)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(t)>-1}getBestMatchFromCodes(t){if(!t)return null;let n;return t.forEach(r=>{if(n)return;const i=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(i))&&(n=i)}),!n&&this.options.supportedLngs&&t.forEach(r=>{if(n)return;const i=this.getLanguagePartFromCode(r);if(this.isSupportedCode(i))return n=i;n=this.options.supportedLngs.find(o=>{if(o===i)return o;if(!(o.indexOf("-")<0&&i.indexOf("-")<0)&&o.indexOf(i)===0)return o})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(t,n){if(!t)return[];if(typeof t=="function"&&(t=t(n)),typeof t=="string"&&(t=[t]),Object.prototype.toString.apply(t)==="[object Array]")return t;if(!n)return t.default||[];let r=t[n];return r||(r=t[this.getScriptPartFromCode(n)]),r||(r=t[this.formatLanguageCode(n)]),r||(r=t[this.getLanguagePartFromCode(n)]),r||(r=t.default),r||[]}toResolveHierarchy(t,n){const r=this.getFallbackCodes(n||this.options.fallbackLng||[],t),i=[],o=l=>{l&&(this.isSupportedCode(l)?i.push(l):this.logger.warn(`rejecting language code not found in supportedLngs: ${l}`))};return typeof t=="string"&&(t.indexOf("-")>-1||t.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&o(this.formatLanguageCode(t)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&o(this.getScriptPartFromCode(t)),this.options.load!=="currentOnly"&&o(this.getLanguagePartFromCode(t))):typeof t=="string"&&o(this.formatLanguageCode(t)),r.forEach(l=>{i.indexOf(l)<0&&o(this.formatLanguageCode(l))}),i}}let Rg=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],$g={1:function(e){return+(e>1)},2:function(e){return+(e!=1)},3:function(e){return 0},4:function(e){return e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2},5:function(e){return e==0?0:e==1?1:e==2?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},6:function(e){return e==1?0:e>=2&&e<=4?1:2},7:function(e){return e==1?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2},8:function(e){return e==1?0:e==2?1:e!=8&&e!=11?2:3},9:function(e){return+(e>=2)},10:function(e){return e==1?0:e==2?1:e<7?2:e<11?3:4},11:function(e){return e==1||e==11?0:e==2||e==12?1:e>2&&e<20?2:3},12:function(e){return+(e%10!=1||e%100==11)},13:function(e){return+(e!==0)},14:function(e){return e==1?0:e==2?1:e==3?2:3},15:function(e){return e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2},16:function(e){return e%10==1&&e%100!=11?0:e!==0?1:2},17:function(e){return e==1||e%10==1&&e%100!=11?0:1},18:function(e){return e==0?0:e==1?1:2},19:function(e){return e==1?0:e==0||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3},20:function(e){return e==1?0:e==0||e%100>0&&e%100<20?1:2},21:function(e){return e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0},22:function(e){return e==1?0:e==2?1:(e<0||e>10)&&e%10==0?2:3}};const Tg=["v1","v2","v3"],Mg=["v4"],ic={zero:0,one:1,two:2,few:3,many:4,other:5};function zg(){const e={};return Rg.forEach(t=>{t.lngs.forEach(n=>{e[n]={numbers:t.nr,plurals:$g[t.fc]}})}),e}class Ig{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=t,this.options=n,this.logger=Xe.create("pluralResolver"),(!this.options.compatibilityJSON||Mg.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=zg()}addRule(t,n){this.rules[t]=n}getRule(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi())try{return new Intl.PluralRules(Xi(t),{type:n.ordinal?"ordinal":"cardinal"})}catch{return}return this.rules[t]||this.rules[this.languageUtils.getLanguagePartFromCode(t)]}needsPlural(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(t,n);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(t,r).map(i=>`${n}${i}`)}getSuffixes(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(t,n);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((i,o)=>ic[i]-ic[o]).map(i=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${i}`):r.numbers.map(i=>this.getSuffix(t,i,n)):[]}getSuffix(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const i=this.getRule(t,r);return i?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${i.select(n)}`:this.getSuffixRetroCompatible(i,n):(this.logger.warn(`no plural rule found for: ${t}`),"")}getSuffixRetroCompatible(t,n){const r=t.noAbs?t.plurals(n):t.plurals(Math.abs(n));let i=t.numbers[r];this.options.simplifyPluralSuffix&&t.numbers.length===2&&t.numbers[0]===1&&(i===2?i="plural":i===1&&(i=""));const o=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return this.options.compatibilityJSON==="v1"?i===1?"":typeof i=="number"?`_plural_${i.toString()}`:o():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&t.numbers.length===2&&t.numbers[0]===1?o():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!Tg.includes(this.options.compatibilityJSON)}}function oc(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=Ng(e,t,n);return!o&&i&&typeof n=="string"&&(o=Yi(e,n,r),o===void 0&&(o=Yi(t,n,r))),o}class Dg{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=Xe.create("interpolator"),this.options=t,this.format=t.interpolation&&t.interpolation.format||(n=>n),this.init(t)}init(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};t.interpolation||(t.interpolation={escapeValue:!0});const n=t.interpolation;this.escape=n.escape!==void 0?n.escape:_g,this.escapeValue=n.escapeValue!==void 0?n.escapeValue:!0,this.useRawValueToEscape=n.useRawValueToEscape!==void 0?n.useRawValueToEscape:!1,this.prefix=n.prefix?ln(n.prefix):n.prefixEscaped||"{{",this.suffix=n.suffix?ln(n.suffix):n.suffixEscaped||"}}",this.formatSeparator=n.formatSeparator?n.formatSeparator:n.formatSeparator||",",this.unescapePrefix=n.unescapeSuffix?"":n.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":n.unescapeSuffix||"",this.nestingPrefix=n.nestingPrefix?ln(n.nestingPrefix):n.nestingPrefixEscaped||ln("$t("),this.nestingSuffix=n.nestingSuffix?ln(n.nestingSuffix):n.nestingSuffixEscaped||ln(")"),this.nestingOptionsSeparator=n.nestingOptionsSeparator?n.nestingOptionsSeparator:n.nestingOptionsSeparator||",",this.maxReplaces=n.maxReplaces?n.maxReplaces:1e3,this.alwaysFormat=n.alwaysFormat!==void 0?n.alwaysFormat:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const t=`${this.prefix}(.+?)${this.suffix}`;this.regexp=new RegExp(t,"g");const n=`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`;this.regexpUnescape=new RegExp(n,"g");const r=`${this.nestingPrefix}(.+?)${this.nestingSuffix}`;this.nestingRegexp=new RegExp(r,"g")}interpolate(t,n,r,i){let o,l,s;const u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function a(g){return g.replace(/\$/g,"$$$$")}const f=g=>{if(g.indexOf(this.formatSeparator)<0){const d=oc(n,u,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(d,void 0,r,{...i,...n,interpolationkey:g}):d}const y=g.split(this.formatSeparator),E=y.shift().trim(),m=y.join(this.formatSeparator).trim();return this.format(oc(n,u,E,this.options.keySeparator,this.options.ignoreJSONStructure),m,r,{...i,...n,interpolationkey:E})};this.resetRegExp();const h=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,c=i&&i.interpolation&&i.interpolation.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>a(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?a(this.escape(g)):a(g)}].forEach(g=>{for(s=0;o=g.regex.exec(t);){const y=o[1].trim();if(l=f(y),l===void 0)if(typeof h=="function"){const m=h(t,o,i);l=typeof m=="string"?m:""}else if(i&&Object.prototype.hasOwnProperty.call(i,y))l="";else if(c){l=o[0];continue}else this.logger.warn(`missed to pass in variable ${y} for interpolating ${t}`),l="";else typeof l!="string"&&!this.useRawValueToEscape&&(l=qa(l));const E=g.safeValue(l);if(t=t.replace(o[0],E),c?(g.regex.lastIndex+=l.length,g.regex.lastIndex-=o[0].length):g.regex.lastIndex=0,s++,s>=this.maxReplaces)break}}),t}nest(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i,o,l;function s(u,a){const f=this.nestingOptionsSeparator;if(u.indexOf(f)<0)return u;const h=u.split(new RegExp(`${f}[ ]*{`));let c=`{${h[1]}`;u=h[0],c=this.interpolate(c,l);const v=c.match(/'/g),g=c.match(/"/g);(v&&v.length%2===0&&!g||g.length%2!==0)&&(c=c.replace(/'/g,'"'));try{l=JSON.parse(c),a&&(l={...a,...l})}catch(y){return this.logger.warn(`failed parsing options string in nesting for key ${u}`,y),`${u}${f}${c}`}return delete l.defaultValue,u}for(;i=this.nestingRegexp.exec(t);){let u=[];l={...r},l=l.replace&&typeof l.replace!="string"?l.replace:l,l.applyPostProcessor=!1,delete l.defaultValue;let a=!1;if(i[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(i[1])){const f=i[1].split(this.formatSeparator).map(h=>h.trim());i[1]=f.shift(),u=f,a=!0}if(o=n(s.call(this,i[1].trim(),l),l),o&&i[0]===t&&typeof o!="string")return o;typeof o!="string"&&(o=qa(o)),o||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${t}`),o=""),a&&(o=u.reduce((f,h)=>this.format(f,h,r.lng,{...r,interpolationkey:i[1].trim()}),o.trim())),t=t.replace(i[0],o),this.regexp.lastIndex=0}return t}}function Fg(e){let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const i=r[1].substring(0,r[1].length-1);t==="currency"&&i.indexOf(":")<0?n.currency||(n.currency=i.trim()):t==="relativetime"&&i.indexOf(":")<0?n.range||(n.range=i.trim()):i.split(";").forEach(l=>{if(!l)return;const[s,...u]=l.split(":"),a=u.join(":").trim().replace(/^'+|'+$/g,"");n[s.trim()]||(n[s.trim()]=a),a==="false"&&(n[s.trim()]=!1),a==="true"&&(n[s.trim()]=!0),isNaN(a)||(n[s.trim()]=parseInt(a,10))})}return{formatName:t,formatOptions:n}}function sn(e){const t={};return function(r,i,o){const l=i+JSON.stringify(o);let s=t[l];return s||(s=e(Xi(i),o),t[l]=s),s(r)}}class Ag{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=Xe.create("formatter"),this.options=t,this.formats={number:sn((n,r)=>{const i=new Intl.NumberFormat(n,{...r});return o=>i.format(o)}),currency:sn((n,r)=>{const i=new Intl.NumberFormat(n,{...r,style:"currency"});return o=>i.format(o)}),datetime:sn((n,r)=>{const i=new Intl.DateTimeFormat(n,{...r});return o=>i.format(o)}),relativetime:sn((n,r)=>{const i=new Intl.RelativeTimeFormat(n,{...r});return o=>i.format(o,r.range||"day")}),list:sn((n,r)=>{const i=new Intl.ListFormat(n,{...r});return o=>i.format(o)})},this.init(t)}init(t){const r=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}}).interpolation;this.formatSeparator=r.formatSeparator?r.formatSeparator:r.formatSeparator||","}add(t,n){this.formats[t.toLowerCase().trim()]=n}addCached(t,n){this.formats[t.toLowerCase().trim()]=sn(n)}format(t,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return n.split(this.formatSeparator).reduce((s,u)=>{const{formatName:a,formatOptions:f}=Fg(u);if(this.formats[a]){let h=s;try{const c=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},v=c.locale||c.lng||i.locale||i.lng||r;h=this.formats[a](s,v,{...f,...i,...c})}catch(c){this.logger.warn(c)}return h}else this.logger.warn(`there was no format function for ${a}`);return s},t)}}function Ug(e,t){e.pending[t]!==void 0&&(delete e.pending[t],e.pendingCount--)}class bg extends Do{constructor(t,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=t,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=i,this.logger=Xe.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,i.backend,i)}queueLoad(t,n,r,i){const o={},l={},s={},u={};return t.forEach(a=>{let f=!0;n.forEach(h=>{const c=`${a}|${h}`;!r.reload&&this.store.hasResourceBundle(a,h)?this.state[c]=2:this.state[c]<0||(this.state[c]===1?l[c]===void 0&&(l[c]=!0):(this.state[c]=1,f=!1,l[c]===void 0&&(l[c]=!0),o[c]===void 0&&(o[c]=!0),u[h]===void 0&&(u[h]=!0)))}),f||(s[a]=!0)}),(Object.keys(o).length||Object.keys(l).length)&&this.queue.push({pending:l,pendingCount:Object.keys(l).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(o),pending:Object.keys(l),toLoadLanguages:Object.keys(s),toLoadNamespaces:Object.keys(u)}}loaded(t,n,r){const i=t.split("|"),o=i[0],l=i[1];n&&this.emit("failedLoading",o,l,n),r&&this.store.addResourceBundle(o,l,r),this.state[t]=n?-1:2;const s={};this.queue.forEach(u=>{Pg(u.loaded,[o],l),Ug(u,t),n&&u.errors.push(n),u.pendingCount===0&&!u.done&&(Object.keys(u.loaded).forEach(a=>{s[a]||(s[a]={});const f=u.loaded[a];f.length&&f.forEach(h=>{s[a][h]===void 0&&(s[a][h]=!0)})}),u.done=!0,u.errors.length?u.callback(u.errors):u.callback())}),this.emit("loaded",s),this.queue=this.queue.filter(u=>!u.done)}read(t,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,l=arguments.length>5?arguments[5]:void 0;if(!t.length)return l(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:t,ns:n,fcName:r,tried:i,wait:o,callback:l});return}this.readingCalls++;const s=(a,f)=>{if(this.readingCalls--,this.waitingReads.length>0){const h=this.waitingReads.shift();this.read(h.lng,h.ns,h.fcName,h.tried,h.wait,h.callback)}if(a&&f&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,t,n,r,i+1,o*2,l)},o);return}l(a,f)},u=this.backend[r].bind(this.backend);if(u.length===2){try{const a=u(t,n);a&&typeof a.then=="function"?a.then(f=>s(null,f)).catch(s):s(null,a)}catch(a){s(a)}return}return u(t,n,s)}prepareLoading(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();typeof t=="string"&&(t=this.languageUtils.toResolveHierarchy(t)),typeof n=="string"&&(n=[n]);const o=this.queueLoad(t,n,r,i);if(!o.toLoad.length)return o.pending.length||i(),null;o.toLoad.forEach(l=>{this.loadOne(l)})}load(t,n,r){this.prepareLoading(t,n,{},r)}reload(t,n,r){this.prepareLoading(t,n,{reload:!0},r)}loadOne(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const r=t.split("|"),i=r[0],o=r[1];this.read(i,o,"read",void 0,void 0,(l,s)=>{l&&this.logger.warn(`${n}loading namespace ${o} for language ${i} failed`,l),!l&&s&&this.logger.log(`${n}loaded namespace ${o} for language ${i}`,s),this.loaded(t,l,s)})}saveMissing(t,n,r,i,o){let l=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},s=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(n)){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if(this.backend&&this.backend.create){const u={...l,isUpdate:o},a=this.backend.create.bind(this.backend);if(a.length<6)try{let f;a.length===5?f=a(t,n,r,i,u):f=a(t,n,r,i),f&&typeof f.then=="function"?f.then(h=>s(null,h)).catch(s):s(null,f)}catch(f){s(f)}else a(t,n,r,i,s,u)}!t||!t[0]||this.store.addResource(t[0],n,r,i)}}}function lc(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(t){let n={};if(typeof t[1]=="object"&&(n=t[1]),typeof t[1]=="string"&&(n.defaultValue=t[1]),typeof t[2]=="string"&&(n.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const r=t[3]||t[2];Object.keys(r).forEach(i=>{n[i]=r[i]})}return n},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}}function sc(e){return typeof e.ns=="string"&&(e.ns=[e.ns]),typeof e.fallbackLng=="string"&&(e.fallbackLng=[e.fallbackLng]),typeof e.fallbackNS=="string"&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e}function ri(){}function Vg(e){Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(n=>{typeof e[n]=="function"&&(e[n]=e[n].bind(e))})}class Lr extends Do{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(super(),this.options=sc(t),this.services={},this.logger=Xe,this.modules={external:[]},Vg(this),n&&!this.isInitialized&&!t.isClone){if(!this.options.initImmediate)return this.init(t,n),this;setTimeout(()=>{this.init(t,n)},0)}}init(){var t=this;let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;typeof n=="function"&&(r=n,n={}),!n.defaultNS&&n.defaultNS!==!1&&n.ns&&(typeof n.ns=="string"?n.defaultNS=n.ns:n.ns.indexOf("translation")<0&&(n.defaultNS=n.ns[0]));const i=lc();this.options={...i,...this.options,...sc(n)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...i.interpolation,...this.options.interpolation}),n.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=n.keySeparator),n.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=n.nsSeparator);function o(f){return f?typeof f=="function"?new f:f:null}if(!this.options.isClone){this.modules.logger?Xe.init(o(this.modules.logger),this.options):Xe.init(null,this.options);let f;this.modules.formatter?f=this.modules.formatter:typeof Intl<"u"&&(f=Ag);const h=new rc(this.options);this.store=new tc(this.options.resources,this.options);const c=this.services;c.logger=Xe,c.resourceStore=this.store,c.languageUtils=h,c.pluralResolver=new Ig(h,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),f&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(c.formatter=o(f),c.formatter.init(c,this.options),this.options.interpolation.format=c.formatter.format.bind(c.formatter)),c.interpolator=new Dg(this.options),c.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},c.backendConnector=new bg(o(this.modules.backend),c.resourceStore,c,this.options),c.backendConnector.on("*",function(v){for(var g=arguments.length,y=new Array(g>1?g-1:0),E=1;E<g;E++)y[E-1]=arguments[E];t.emit(v,...y)}),this.modules.languageDetector&&(c.languageDetector=o(this.modules.languageDetector),c.languageDetector.init&&c.languageDetector.init(c,this.options.detection,this.options)),this.modules.i18nFormat&&(c.i18nFormat=o(this.modules.i18nFormat),c.i18nFormat.init&&c.i18nFormat.init(this)),this.translator=new Ji(this.services,this.options),this.translator.on("*",function(v){for(var g=arguments.length,y=new Array(g>1?g-1:0),E=1;E<g;E++)y[E-1]=arguments[E];t.emit(v,...y)}),this.modules.external.forEach(v=>{v.init&&v.init(this)})}if(this.format=this.options.interpolation.format,r||(r=ri),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const f=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);f.length>0&&f[0]!=="dev"&&(this.options.lng=f[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(f=>{this[f]=function(){return t.store[f](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(f=>{this[f]=function(){return t.store[f](...arguments),t}});const u=Yn(),a=()=>{const f=(h,c)=>{this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),u.resolve(c),r(h,c)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return f(null,this.t.bind(this));this.changeLanguage(this.options.lng,f)};return this.options.resources||!this.options.initImmediate?a():setTimeout(a,0),u}loadResources(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ri;const i=typeof t=="string"?t:this.language;if(typeof t=="function"&&(r=t),!this.options.resources||this.options.partialBundledLanguages){if(i&&i.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const o=[],l=s=>{if(!s||s==="cimode")return;this.services.languageUtils.toResolveHierarchy(s).forEach(a=>{a!=="cimode"&&o.indexOf(a)<0&&o.push(a)})};i?l(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>l(u)),this.options.preload&&this.options.preload.forEach(s=>l(s)),this.services.backendConnector.load(o,this.options.ns,s=>{!s&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(s)})}else r(null)}reloadResources(t,n,r){const i=Yn();return t||(t=this.languages),n||(n=this.options.ns),r||(r=ri),this.services.backendConnector.reload(t,n,o=>{i.resolve(),r(o)}),i}use(t){if(!t)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!t.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return t.type==="backend"&&(this.modules.backend=t),(t.type==="logger"||t.log&&t.warn&&t.error)&&(this.modules.logger=t),t.type==="languageDetector"&&(this.modules.languageDetector=t),t.type==="i18nFormat"&&(this.modules.i18nFormat=t),t.type==="postProcessor"&&qd.addPostProcessor(t),t.type==="formatter"&&(this.modules.formatter=t),t.type==="3rdParty"&&this.modules.external.push(t),this}setResolvedLanguage(t){if(!(!t||!this.languages)&&!(["cimode","dev"].indexOf(t)>-1))for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}}changeLanguage(t,n){var r=this;this.isLanguageChangingTo=t;const i=Yn();this.emit("languageChanging",t);const o=u=>{this.language=u,this.languages=this.services.languageUtils.toResolveHierarchy(u),this.resolvedLanguage=void 0,this.setResolvedLanguage(u)},l=(u,a)=>{a?(o(a),this.translator.changeLanguage(a),this.isLanguageChangingTo=void 0,this.emit("languageChanged",a),this.logger.log("languageChanged",a)):this.isLanguageChangingTo=void 0,i.resolve(function(){return r.t(...arguments)}),n&&n(u,function(){return r.t(...arguments)})},s=u=>{!t&&!u&&this.services.languageDetector&&(u=[]);const a=typeof u=="string"?u:this.services.languageUtils.getBestMatchFromCodes(u);a&&(this.language||o(a),this.translator.language||this.translator.changeLanguage(a),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(a)),this.loadResources(a,f=>{l(f,a)})};return!t&&this.services.languageDetector&&!this.services.languageDetector.async?s(this.services.languageDetector.detect()):!t&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(s):this.services.languageDetector.detect(s):s(t),i}getFixedT(t,n,r){var i=this;const o=function(l,s){let u;if(typeof s!="object"){for(var a=arguments.length,f=new Array(a>2?a-2:0),h=2;h<a;h++)f[h-2]=arguments[h];u=i.options.overloadTranslationOptionHandler([l,s].concat(f))}else u={...s};u.lng=u.lng||o.lng,u.lngs=u.lngs||o.lngs,u.ns=u.ns||o.ns,u.keyPrefix=u.keyPrefix||r||o.keyPrefix;const c=i.options.keySeparator||".";let v;return u.keyPrefix&&Array.isArray(l)?v=l.map(g=>`${u.keyPrefix}${c}${g}`):v=u.keyPrefix?`${u.keyPrefix}${c}${l}`:l,i.t(v,u)};return typeof t=="string"?o.lng=t:o.lngs=t,o.ns=n,o.keyPrefix=r,o}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(t){this.options.defaultNS=t}hasLoadedNamespace(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],i=this.options?this.options.fallbackLng:!1,o=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const l=(s,u)=>{const a=this.services.backendConnector.state[`${s}|${u}`];return a===-1||a===2};if(n.precheck){const s=n.precheck(this,l);if(s!==void 0)return s}return!!(this.hasResourceBundle(r,t)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||l(r,t)&&(!i||l(o,t)))}loadNamespaces(t,n){const r=Yn();return this.options.ns?(typeof t=="string"&&(t=[t]),t.forEach(i=>{this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}),this.loadResources(i=>{r.resolve(),n&&n(i)}),r):(n&&n(),Promise.resolve())}loadLanguages(t,n){const r=Yn();typeof t=="string"&&(t=[t]);const i=this.options.preload||[],o=t.filter(l=>i.indexOf(l)<0);return o.length?(this.options.preload=i.concat(o),this.loadResources(l=>{r.resolve(),n&&n(l)}),r):(n&&n(),Promise.resolve())}dir(t){if(t||(t=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!t)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=this.services&&this.services.languageUtils||new rc(lc());return n.indexOf(r.getLanguagePartFromCode(t))>-1||t.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return new Lr(t,n)}cloneInstance(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ri;const r=t.forkResourceStore;r&&delete t.forkResourceStore;const i={...this.options,...t,isClone:!0},o=new Lr(i);return(t.debug!==void 0||t.prefix!==void 0)&&(o.logger=o.logger.clone(t)),["store","services","language"].forEach(s=>{o[s]=this[s]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},r&&(o.store=new tc(this.store.data,i),o.services.resourceStore=o.store),o.translator=new Ji(o.services,i),o.translator.on("*",function(s){for(var u=arguments.length,a=new Array(u>1?u-1:0),f=1;f<u;f++)a[f-1]=arguments[f];o.emit(s,...a)}),o.init(i,n),o.translator.options=i,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const ee=Lr.createInstance();ee.createInstance=Lr.createInstance;ee.createInstance;ee.dir;ee.init;ee.loadResources;ee.reloadResources;ee.use;ee.changeLanguage;ee.getFixedT;ee.t;ee.exists;ee.setDefaultNamespace;ee.hasLoadedNamespace;ee.loadNamespaces;ee.loadLanguages;const Bg=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Hg={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Wg=e=>Hg[e],Kg=e=>e.replace(Bg,Wg);let uc={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Kg};function Qg(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};uc={...uc,...e}}const Gg={type:"3rdParty",init(e){Qg(e.options.react)}},Yg=Object.freeze({app:Object.freeze({landing:Object.freeze({clickToEnter:"Click To Enter..."}),config:Object.freeze({title:"⚒⎮𝙚𝙥𝙞𝙘.𝘿𝙖𝙫𝙞𝙙 ☻ :)",description:"React | TailwindCSS | Vite | TypeScript | JavaScript | MongoDB"})})}),Xg={app:Object.freeze({landing:Object.freeze({clickToEnter:"Haga clic para entrar..."}),config:Object.freeze({title:"Kkermit",description:"| React | TailwindCSS | Vite | TypeScript | JavaScript | Java | MongoDB |"})})},Jg={app:Object.freeze({landing:Object.freeze({clickToEnter:"Cliquez pour entrer..."}),config:Object.freeze({title:"⚒⎮𝙚𝙥𝙞𝙘.𝘿𝙖𝙫𝙞𝙙 ☻ :)",description:"React | TailwindCSS | Vite | TypeScript | JavaScript | MongoDB"})})};ee.use(Gg).init({resources:{en:{translation:Yg},es:{translation:Xg},fr:{translation:Jg}},lng:"en",fallbackLng:"en",interpolation:{escapeValue:!1}});const Zg=({onEnter:e})=>{const t=bd(),n=()=>{t(gi()),e()};return S.jsx("div",{onClick:n,className:"fixed inset-0 z-20 flex items-center justify-center bg-black/90 cursor-pointer transition-opacity duration-1000 p-4",children:S.jsx("div",{className:"text-center max-w-[90vw] sm:max-w-[80vw] md:max-w-[70vw] lg:max-w-[60vw]",children:S.jsx("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-8xl text-white font-bold mb-4 animate-pulse animate-bounce tracking-wider",children:ee.t("app.landing.clickToEnter")})})})},qg="/assets/epicDavidpic-notagif-Ve2isbRx.png",e1=()=>S.jsx("figure",{className:"hidden custom:block",children:S.jsx("img",{src:qg,alt:"Profile",className:`h-64 w-64 rounded-full border-4 border-white bg-black p-1.5 
                     transition-transform duration-300 hover:scale-105
                     hover:rotate-[360deg] transition-all duration-[1500ms]`})}),t1=({children:e})=>{const t=$.useMemo(()=>[...Array(40)].map((n,r)=>({delay:`${Math.random()*4}s`,size:`${Math.random()*6+3}px`,speed:`${Math.random()*2+2}s`,left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,moveX:`${Math.random()*100-50}px`,moveY:`${Math.random()*100-50}px`,id:`sparkle-${r}`})),[]);return S.jsxs("div",{className:"sparkles-wrapper",children:[t.map(n=>S.jsx("div",{className:"sparkle",style:{"--delay":n.delay,"--size":n.size,"--speed":n.speed,"--left":n.left,"--top":n.top,"--moveX":n.moveX,"--moveY":n.moveY}},n.id)),S.jsx("div",{className:"sparkles-content",children:e})]})},n1=gp.memo(t1),ac={title:ee.t("app.config.title"),description:ee.t("app.config.description")},r1="1182064854006251520",i1=()=>S.jsxs("header",{className:"text-center mb-8",children:[S.jsx("div",{className:"relative inline-block",children:S.jsx("h1",{className:`text-5xl md:text-6xl font-black text-white 
                          drop-shadow-[0_0_5px_#f5f5f5] mb-3`,children:S.jsx(n1,{children:ac.title})})}),S.jsx("p",{className:`text-xl md:text-2xl font-medium text-white 
                     drop-shadow-[0_0_3px_#f5f5f5]`,children:ac.description})]}),o1="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='50px'%20height='50px'%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3egithub%20[%23142]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-140.000000,%20-7559.000000)'%20fill='%23fff'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M94,7399%20C99.523,7399%20104,7403.59%20104,7409.253%20C104,7413.782%20101.138,7417.624%2097.167,7418.981%20C96.66,7419.082%2096.48,7418.762%2096.48,7418.489%20C96.48,7418.151%2096.492,7417.047%2096.492,7415.675%20C96.492,7414.719%2096.172,7414.095%2095.813,7413.777%20C98.04,7413.523%20100.38,7412.656%20100.38,7408.718%20C100.38,7407.598%2099.992,7406.684%2099.35,7405.966%20C99.454,7405.707%2099.797,7404.664%2099.252,7403.252%20C99.252,7403.252%2098.414,7402.977%2096.505,7404.303%20C95.706,7404.076%2094.85,7403.962%2094,7403.958%20C93.15,7403.962%2092.295,7404.076%2091.497,7404.303%20C89.586,7402.977%2088.746,7403.252%2088.746,7403.252%20C88.203,7404.664%2088.546,7405.707%2088.649,7405.966%20C88.01,7406.684%2087.619,7407.598%2087.619,7408.718%20C87.619,7412.646%2089.954,7413.526%2092.175,7413.785%20C91.889,7414.041%2091.63,7414.493%2091.54,7415.156%20C90.97,7415.418%2089.522,7415.871%2088.63,7414.304%20C88.63,7414.304%2088.101,7413.319%2087.097,7413.247%20C87.097,7413.247%2086.122,7413.234%2087.029,7413.87%20C87.029,7413.87%2087.684,7414.185%2088.139,7415.37%20C88.139,7415.37%2088.726,7417.2%2091.508,7416.58%20C91.513,7417.437%2091.522,7418.245%2091.522,7418.489%20C91.522,7418.76%2091.338,7419.077%2090.839,7418.982%20C86.865,7417.627%2084,7413.783%2084,7409.253%20C84,7403.59%2088.478,7399%2094,7399'%20id='github-[%23142]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",l1="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='50px'%20height='50px'%20viewBox='0%20-28.5%20256%20256'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20preserveAspectRatio='xMidYMid'%3e%3cg%3e%3cpath%20d='M216.856339,16.5966031%20C200.285002,8.84328665%20182.566144,3.2084988%20164.041564,0%20C161.766523,4.11318106%20159.108624,9.64549908%20157.276099,14.0464379%20C137.583995,11.0849896%20118.072967,11.0849896%2098.7430163,14.0464379%20C96.9108417,9.64549908%2094.1925838,4.11318106%2091.8971895,0%20C73.3526068,3.2084988%2055.6133949,8.86399117%2039.0420583,16.6376612%20C5.61752293,67.146514%20-3.4433191,116.400813%201.08711069,164.955721%20C23.2560196,181.510915%2044.7403634,191.567697%2065.8621325,198.148576%20C71.0772151,190.971126%2075.7283628,183.341335%2079.7352139,175.300261%20C72.104019,172.400575%2064.7949724,168.822202%2057.8887866,164.667963%20C59.7209612,163.310589%2061.5131304,161.891452%2063.2445898,160.431257%20C105.36741,180.133187%20151.134928,180.133187%20192.754523,160.431257%20C194.506336,161.891452%20196.298154,163.310589%20198.110326,164.667963%20C191.183787,168.842556%20183.854737,172.420929%20176.223542,175.320965%20C180.230393,183.341335%20184.861538,190.991831%20190.096624,198.16893%20C211.238746,191.588051%20232.743023,181.531619%20254.911949,164.955721%20C260.227747,108.668201%20245.831087,59.8662432%20216.856339,16.5966031%20Z%20M85.4738752,135.09489%20C72.8290281,135.09489%2062.4592217,123.290155%2062.4592217,108.914901%20C62.4592217,94.5396472%2072.607595,82.7145587%2085.4738752,82.7145587%20C98.3405064,82.7145587%20108.709962,94.5189427%20108.488529,108.914901%20C108.508531,123.290155%2098.3405064,135.09489%2085.4738752,135.09489%20Z%20M170.525237,135.09489%20C157.88039,135.09489%20147.510584,123.290155%20147.510584,108.914901%20C147.510584,94.5396472%20157.658606,82.7145587%20170.525237,82.7145587%20C183.391518,82.7145587%20193.761324,94.5189427%20193.539891,108.914901%20C193.539891,123.290155%20183.391518,135.09489%20170.525237,135.09489%20Z'%20fill='%235865F2'%20fill-rule='nonzero'%3e%3c/path%3e%3c/g%3e%3c/svg%3e",s1="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='50px'%20height='50px'%20viewBox='0%200%2048%2048'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3eSpotify-color%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Icons'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Color-'%20transform='translate(-200.000000,%20-460.000000)'%20fill='%2300DA5A'%3e%3cpath%20d='M238.16,481.36%20C230.48,476.8%20217.64,476.32%20210.32,478.6%20C209.12,478.96%20207.92,478.24%20207.56,477.16%20C207.2,475.96%20207.92,474.76%20209,474.4%20C217.52,471.88%20231.56,472.36%20240.44,477.64%20C241.52,478.24%20241.88,479.68%20241.28,480.76%20C240.68,481.6%20239.24,481.96%20238.16,481.36%20M237.92,488.08%20C237.32,488.92%20236.24,489.28%20235.4,488.68%20C228.92,484.72%20219.08,483.52%20211.52,485.92%20C210.56,486.16%20209.48,485.68%20209.24,484.72%20C209,483.76%20209.48,482.68%20210.44,482.44%20C219.2,479.8%20230,481.12%20237.44,485.68%20C238.16,486.04%20238.52,487.24%20237.92,488.08%20M235.04,494.68%20C234.56,495.4%20233.72,495.64%20233,495.16%20C227.36,491.68%20220.28,490.96%20211.88,492.88%20C211.04,493.12%20210.32,492.52%20210.08,491.8%20C209.84,490.96%20210.44,490.24%20211.16,490%20C220.28,487.96%20228.2,488.8%20234.44,492.64%20C235.28,493%20235.4,493.96%20235.04,494.68%20M224,460%20C210.8,460%20200,470.8%20200,484%20C200,497.2%20210.8,508%20224,508%20C237.2,508%20248,497.2%20248,484%20C248,470.8%20237.32,460%20224,460'%20id='Spotify'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/svg%3e",xl=({href:e,icon:t,alt:n,glowColor:r})=>S.jsx("a",{href:e,target:"_blank",rel:"noopener noreferrer",children:S.jsx("img",{src:t,alt:n,style:{filter:`drop-shadow(0 0 3px ${r})`},className:`w-10 h-10 cursor-pointer transition-all duration-300 
                     hover:scale-110 hover:brightness-110`})}),vi={github:"http://github.com/epicDavid-public/",discord:`https://discord.com/users/${0x106789b8cc844000}`,spotify:"https://open.spotify.com/user/31lmwvamzrwrtuoih3x3rzhixt3i"},u1=async e=>{try{const n=await(await fetch(`https://api.lanyard.rest/v1/users/${e}`)).json();if(!n.success)throw new Error("Failed to fetch Discord user data");return{id:n.data.discord_user.id,username:n.data.discord_user.username,discriminator:n.data.discord_user.discriminator,avatar:n.data.discord_user.avatar,status:n.data.discord_status,discord_user:n.data.discord_user,activities:n.data.activities,spotify:n.data.spotify,listening_to_spotify:n.data.listening_to_spotify,active_on_discord_desktop:n.data.active_on_discord_desktop,active_on_discord_mobile:n.data.active_on_discord_mobile,active_on_discord_web:n.data.active_on_discord_web}}catch(t){throw console.error("Error fetching Discord user:",t),t}},a1=(e,t)=>{const n=Date.now()+0;if(e.end){const r=Math.floor((e.end-n)/1e3);if(r<=0)return"ending now";const i=Math.floor(r/3600),o=Math.floor(r%3600/60);return i>0?`${i}h ${o}m remaining`:`${o}m ${r%60}s remaining`}else if(e.start){const r=Math.floor((n-e.start)/1e3),i=Math.floor(r/3600),o=Math.floor(r%3600/60);return i>0?`${i}h ${o}m elapsed`:o>0?`${o}m ${r%60}s elapsed`:`${r}s elapsed`}return""},Rr="rgba(88, 85, 85, 0.4)",cc="rgba(88, 85, 85, 0.6)",fc="rgba(116, 116, 116, 0.6)",Zi={backgroundColor:Rr,backdropFilter:"none",WebkitBackdropFilter:"none"},c1={0:"Playing",1:"Streaming",2:"Listening to",3:"Watching",4:"Custom",5:"Competing in"},dc={online:"bg-green-500",idle:"bg-yellow-400",dnd:"bg-red-500",offline:"bg-gray-500"},f1=()=>{var E,m,d;const[e,t]=$.useState(null),[n,r]=$.useState(!0),[i,o]=$.useState(null),[l,s]=$.useState(0),u=$.useRef([]);$.useEffect(()=>{const p=window.setInterval(()=>{s(w=>w+1)},1e3);return u.current.push(p),()=>{window.clearInterval(p)}},[]),$.useEffect(()=>{const p=async()=>{try{const P=await u1(r1);t(P)}catch(x){o("Failed to load Discord status"),console.error(x)}finally{r(!1)}};p();const w=window.setInterval(p,3e4);return u.current.push(w),()=>{u.current.forEach(x=>window.clearInterval(x)),u.current=[]}},[]);const a=()=>{window.open(vi.discord,"_blank","noopener,noreferrer")},f=(p,w)=>{if(!p)return"";if(p.startsWith("mp:external/"))try{const x=p.split("mp:external/")[1],P=x.indexOf("/https/");if(P!==-1)return"https://"+x.substring(P+6);{const k=x.match(/\/https\/(.+)/);if(k&&k[1])return"https://"+k[1]}return p}catch(x){return console.error("Error extracting image URL:",x),""}return p.startsWith("spotify:")?`https://i.scdn.co/image/${p.split(":")[1]}`:w?`https://cdn.discordapp.com/app-assets/${w}/${p}.png`:p},h=(p,w)=>{const x=Date.now()+l*0,P=Math.floor((w-p)/1e3),k=Math.floor((x-p)/1e3),N=Math.floor(k/60),M=k%60,j=Math.floor(P/60),X=P%60;return`${N}:${M.toString().padStart(2,"0")} / ${j}:${X.toString().padStart(2,"0")}`};if(n)return S.jsx("div",{className:"w-full mx-auto mb-4 sm:mb-6 rounded-xl p-3 sm:p-4 flex justify-center items-center h-[80px] sm:h-[100px]",style:{backgroundColor:Rr,boxShadow:"none"},children:S.jsx("div",{className:"animate-spin h-5 w-5 sm:h-6 sm:w-6 border-2 border-white rounded-full border-t-transparent"})});if(i||!e)return S.jsx("div",{className:"w-full mx-auto mb-4 sm:mb-6 rounded-xl p-3 sm:p-4 text-center text-white",style:Zi,children:"Unable to load Discord status"});const c=e.avatar?`https://cdn.discordapp.com/avatars/${e.id}/${e.avatar}.png?size=256`:"https://cdn.discordapp.com/embed/avatars/0.png",v=((E=e.activities)==null?void 0:E.filter(p=>p.type!==4))||[],g=(m=e.activities)==null?void 0:m.find(p=>p.type===4),y=v.slice(0,3);return S.jsxs("div",{onClick:a,className:"w-full mx-auto mb-4 sm:mb-6 rounded-xl p-3 sm:p-4 cursor-pointer",style:{...Zi,boxShadow:"none",WebkitBoxShadow:"none",MozBoxShadow:"none"},children:[S.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[S.jsxs("div",{className:"relative",children:[S.jsx("img",{src:c,alt:e.username||"Discord User",className:"w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"}),S.jsxs("div",{className:"absolute bottom-0 right-0 flex items-center justify-center",children:[S.jsx("div",{className:`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full border-2 border-[rgba(58,58,58,0.8)] ${dc[e.status||"offline"]}`}),e.status!=="offline"&&S.jsx("span",{className:`absolute inline-flex h-full w-full rounded-full ${dc[e.status||"offline"]} opacity-75 animate-ping`})]})]}),S.jsxs("div",{className:"flex-1 min-w-0",children:[S.jsx("div",{className:"font-medium text-white flex items-center text-sm sm:text-base truncate",children:((d=e.discord_user)==null?void 0:d.display_name)||e.username}),S.jsxs("div",{className:"flex items-center text-xs text-gray-300 truncate",children:[S.jsx("span",{className:"capitalize mr-1",children:e.status||"offline"}),g&&g.emoji&&S.jsx("span",{className:"flex items-center gap-1 ml-1 flex-shrink-0",children:g.emoji.animated?S.jsx("img",{src:`https://cdn.discordapp.com/emojis/${g.emoji.id}.gif`,alt:g.emoji.name,className:"w-3 h-3 sm:w-4 sm:h-4"}):S.jsx("span",{children:g.emoji.name})})]})]})]}),g&&g.state&&S.jsx("div",{className:"mt-1.5 sm:mt-2 text-gray-200 text-xs sm:text-sm truncate",children:g.state.startsWith("https://")?S.jsx("a",{href:g.state,target:"_blank",rel:"noopener noreferrer",onClick:p=>p.stopPropagation(),className:"text-blue-300 hover:text-blue-200 hover:underline",children:g.state}):g.state}),e.spotify&&S.jsx("div",{className:"mt-2 sm:mt-3 border-t border-white/10 pt-2 sm:pt-3 px-0 sm:px-1",children:S.jsxs("div",{className:"flex items-center",children:[S.jsxs("div",{className:"flex-1 min-w-0",children:[S.jsxs("div",{className:"text-xs text-green-400 mb-0.5 sm:mb-1 flex items-center",children:[S.jsx("div",{className:"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full mr-1"}),S.jsx("span",{className:"truncate",children:"Listening to Spotify"})]}),S.jsx("div",{className:"text-white font-medium text-xs sm:text-sm truncate",children:e.spotify.song}),S.jsxs("div",{className:"text-gray-300 text-xs truncate",children:["by ",e.spotify.artist]}),S.jsxs("div",{className:"text-gray-400 text-xs truncate",children:["on ",e.spotify.album]}),e.spotify.timestamps&&S.jsx("div",{className:"text-gray-400 text-xs mt-1 hidden sm:block",children:h(e.spotify.timestamps.start,e.spotify.timestamps.end)})]}),S.jsx("div",{className:"ml-2 flex-shrink-0",children:S.jsx("img",{src:e.spotify.album_art_url,alt:e.spotify.album,className:"w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover"})})]})}),y.filter(p=>p.name!=="Spotify").map((p,w)=>{var x,P;return S.jsxs("div",{className:"mt-2 sm:mt-3 border-t border-white/10 pt-2 sm:pt-3 px-0 sm:px-1",children:[S.jsxs("div",{className:"flex items-center",children:[S.jsxs("div",{className:"flex-1 min-w-0",children:[S.jsxs("div",{className:"text-xs text-gray-300 mb-0.5 sm:mb-1 flex items-center flex-wrap gap-y-1",children:[S.jsx("span",{className:"mr-1",children:c1[p.type]||"Playing"}),p.name==="Code"&&S.jsx("span",{className:"px-1 py-0.5 text-[8px] sm:text-[10px] bg-blue-600 rounded-full",children:"VS Code"})]}),S.jsx("div",{className:"text-white font-medium text-xs sm:text-sm truncate",children:p.name}),p.details&&S.jsx("div",{className:"text-gray-200 text-xs truncate",children:p.details}),p.state&&!p.state.includes("buymeacoffee")&&S.jsx("div",{className:"text-gray-300 text-xs truncate",children:p.state}),p.timestamps&&S.jsx("div",{className:"text-gray-400 text-xs mt-0.5 sm:mt-1 hidden sm:block",children:a1(p.timestamps,l)})]}),((x=p.assets)==null?void 0:x.large_image)&&S.jsx("div",{className:"ml-2 flex-shrink-0",children:S.jsxs("div",{className:"relative w-12 h-12 sm:w-16 sm:h-16 rounded-lg overflow-hidden",style:{backgroundColor:cc},children:[S.jsx("img",{src:f(p.assets.large_image,p.application_id),alt:p.assets.large_text||p.name,className:"w-full h-full object-cover rounded-lg",title:p.assets.large_text||"",onError:k=>{var N;console.error("Failed to load image:",(N=p.assets)==null?void 0:N.large_image),k.currentTarget.style.display="none"}}),((P=p.assets)==null?void 0:P.small_image)&&S.jsx("div",{className:"absolute bottom-0 right-0 p-0.5 rounded-full",style:{backgroundColor:cc},children:S.jsx("img",{src:f(p.assets.small_image,p.application_id),alt:p.assets.small_text||"",title:p.assets.small_text||"",className:"w-3 h-3 sm:w-4 sm:h-4 rounded-full",onError:k=>{var N;console.error("Failed to load small image:",(N=p.assets)==null?void 0:N.small_image),k.currentTarget.style.display="none"}})})]})})]}),p.buttons&&p.buttons.length>0&&S.jsx("div",{className:"mt-1.5 sm:mt-2 flex flex-wrap gap-1 sm:gap-2",children:p.buttons.map((k,N)=>S.jsx("button",{className:`bg-[rgba(88,85,85,0.5)] hover:bg-[rgba(100,100,100,0.6)] 
                              text-white text-[10px] sm:text-xs py-0.5 px-1.5 sm:py-1 sm:px-2 
                              rounded transition-colors`,onClick:M=>M.stopPropagation(),children:k},N))})]},w)}),v.length>3&&S.jsx("div",{className:"mt-2 sm:mt-3 border-t border-white/10 pt-2 sm:pt-3 px-0 sm:px-1 text-center",children:S.jsxs("span",{className:"text-xs text-gray-400",children:["+",v.length-3," more ",v.length-3===1?"activity":"activities"]})})]})},d1=()=>S.jsxs("div",{className:"rounded-xl p-6 max-w-[600px] mx-auto mt-12 sm:mt-6",style:{...Zi,backgroundColor:Rr},children:[S.jsx(f1,{}),S.jsxs("nav",{className:"flex justify-center gap-8",children:[S.jsx(xl,{href:vi.github,icon:o1,alt:"GitHub",glowColor:"#fff"}),S.jsx(xl,{href:vi.discord,icon:l1,alt:"Discord",glowColor:"#5865f2"}),S.jsx(xl,{href:vi.spotify,icon:s1,alt:"Spotify",glowColor:"#00da5a"})]})]}),p1=()=>{const[e,t]=$.useState({x:0,y:0}),[n,r]=$.useState({x:0,y:0}),i=$.useRef(null),o=s=>{if(!i.current)return;const u=i.current.getBoundingClientRect(),a=s.clientX-u.left,f=s.clientY-u.top,h=a/u.width,c=f/u.height,v=(.5-c)*15,g=(.5-h)*15,y=(h-.5)*30,E=(c-.5)*30;t({x:v,y:g}),r({x:y,y:E})},l=()=>{t({x:0,y:0}),r({x:0,y:0})};return S.jsx("main",{className:"min-h-screen flex items-center justify-center",children:S.jsxs("section",{className:"flex flex-col md:flex-row items-center gap-32 px-4 py-8 pt-16 sm:pt-8",children:[S.jsx(e1,{}),S.jsxs("article",{ref:i,onMouseMove:o,onMouseLeave:l,style:{...Zi,transform:`perspective(1000px) scale(1.02)
                      rotateX(${e.x}deg)
                      rotateY(${e.y}deg)
                      skew(${e.y*.5}deg, ${e.x*.5}deg)`,boxShadow:`${n.x}px ${n.y}px 20px rgba(255,255,255,0.1),
                       0 0 30px rgba(255,255,255,0.05)`,transition:"transform 0.2s ease-out, box-shadow 0.2s ease-out"},className:`w-[90vw] max-w-[400px] md:max-w-[700px] rounded-xl p-8 cursor-pointer
                  hover:shadow-xl hover:shadow-white/10`,children:[S.jsx(i1,{}),S.jsx(d1,{})]})]})})},h1="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='36px'%20height='36px'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M16%209.50009L21%2014.5001M21%209.50009L16%2014.5001M4.6%209.00009H5.5012C6.05213%209.00009%206.32759%209.00009%206.58285%208.93141C6.80903%208.87056%207.02275%208.77046%207.21429%208.63566C7.43047%208.48353%207.60681%208.27191%207.95951%207.84868L10.5854%204.69758C11.0211%204.17476%2011.2389%203.91335%2011.4292%203.88614C11.594%203.86258%2011.7597%203.92258%2011.8712%204.04617C12%204.18889%2012%204.52917%2012%205.20973V18.7904C12%2019.471%2012%2019.8113%2011.8712%2019.954C11.7597%2020.0776%2011.594%2020.1376%2011.4292%2020.114C11.239%2020.0868%2011.0211%2019.8254%2010.5854%2019.3026L7.95951%2016.1515C7.60681%2015.7283%207.43047%2015.5166%207.21429%2015.3645C7.02275%2015.2297%206.80903%2015.1296%206.58285%2015.0688C6.32759%2015.0001%206.05213%2015.0001%205.5012%2015.0001H4.6C4.03995%2015.0001%203.75992%2015.0001%203.54601%2014.8911C3.35785%2014.7952%203.20487%2014.6422%203.10899%2014.4541C3%2014.2402%203%2013.9601%203%2013.4001V10.6001C3%2010.04%203%209.76001%203.10899%209.54609C3.20487%209.35793%203.35785%209.20495%203.54601%209.10908C3.75992%209.00009%204.03995%209.00009%204.6%209.00009Z'%20stroke='%23fff'%20stroke-width='2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",m1="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='36px'%20height='36px'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M16.0004%209.00009C16.6281%209.83575%2017%2010.8745%2017%2012.0001C17%2013.1257%2016.6281%2014.1644%2016.0004%2015.0001M18%205.29177C19.8412%206.93973%2021%209.33459%2021%2012.0001C21%2014.6656%2019.8412%2017.0604%2018%2018.7084M4.6%209.00009H5.5012C6.05213%209.00009%206.32759%209.00009%206.58285%208.93141C6.80903%208.87056%207.02275%208.77046%207.21429%208.63566C7.43047%208.48353%207.60681%208.27191%207.95951%207.84868L10.5854%204.69758C11.0211%204.17476%2011.2389%203.91335%2011.4292%203.88614C11.594%203.86258%2011.7597%203.92258%2011.8712%204.04617C12%204.18889%2012%204.52917%2012%205.20973V18.7904C12%2019.471%2012%2019.8113%2011.8712%2019.954C11.7597%2020.0776%2011.594%2020.1376%2011.4292%2020.114C11.239%2020.0868%2011.0211%2019.8254%2010.5854%2019.3026L7.95951%2016.1515C7.60681%2015.7283%207.43047%2015.5166%207.21429%2015.3645C7.02275%2015.2297%206.80903%2015.1296%206.58285%2015.0688C6.32759%2015.0001%206.05213%2015.0001%205.5012%2015.0001H4.6C4.03995%2015.0001%203.75992%2015.0001%203.54601%2014.8911C3.35785%2014.7952%203.20487%2014.6422%203.10899%2014.4541C3%2014.2402%203%2013.9601%203%2013.4001V10.6001C3%2010.04%203%209.76001%203.10899%209.54609C3.20487%209.35793%203.35785%209.20495%203.54601%209.10908C3.75992%209.00009%204.03995%209.00009%204.6%209.00009Z'%20stroke='%23fff'%20stroke-width='2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",g1=()=>{const e=$.useRef(null),t=bd(),{isPlaying:n,volume:r,currentSong:i}=em(s=>s.audio),o=i.name.length>5;$.useEffect(()=>{e.current&&(e.current.volume=r)},[r]),$.useEffect(()=>{e.current&&(n?e.current.play():e.current.pause())},[n]);const l=async()=>{try{if(n)e.current&&e.current.pause(),t(ni(0)),t(gi());else if(t(gi()),t(ni(.5)),e.current){const s=e.current.play();s&&await s}}catch{t(gi()),t(ni(0))}};return S.jsxs("div",{className:"fixed top-4 left-4 z-50",children:[S.jsx("audio",{ref:e,src:i.path,loop:!0}),S.jsx("div",{className:"group relative",children:S.jsxs("div",{className:"flex flex-col",children:[S.jsxs("div",{className:"flex items-center p-1.5 rounded-[15px] border-3 transition-all duration-300 w-[51.7px] group-hover:w-[200px] shadow-lg",style:{backgroundColor:Rr,borderColor:fc},children:[S.jsx("div",{className:"min-w-[32px] h-[32px] flex items-center justify-center",children:S.jsx("button",{onClick:l,className:"cursor-pointer flex items-center justify-center w-8 h-8 transition-transform active:scale-110 duration-150",children:S.jsx("img",{src:n?m1:h1,alt:n?"Play":"Pause",className:"w-8 h-8"})})}),S.jsx("div",{className:"overflow-hidden transition-all duration-300 w-0 group-hover:w-full group-hover:ml-4",children:S.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:r,onChange:s=>t(ni(parseFloat(s.target.value))),className:"w-24 accent-white"})})]}),S.jsx("div",{className:"absolute top-full left-0 mt-2 w-full opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none",children:S.jsx("div",{className:"rounded-[15px] border-3 p-2",style:{backgroundColor:Rr,borderColor:fc},children:S.jsx("div",{className:"scroll-container w-[150px] mx-auto",children:o?S.jsxs("span",{className:"scroll-text text-white text-sm",children:[i.name,"    "]}):S.jsx("p",{className:"text-white text-sm text-center truncate",children:i.name.slice(0,25)})})})})]})})]})},v1=()=>{const e=$.useRef(null),t=$.useRef();return $.useEffect(()=>{const n=e.current;if(!n)return;const r=n.getContext("2d");if(!r)return;const i=()=>{n&&(n.width=window.innerWidth,n.height=window.innerHeight)};i();const o=[];for(let s=0;s<200;s++)o.push({x:Math.random()*n.width,y:Math.random()*n.height,size:Math.random()*2,speed:Math.random()*.5});const l=()=>{r.fillStyle="rgba(0, 0, 0, 1)",r.fillRect(0,0,n.width,n.height),o.forEach(s=>{r.fillStyle="white",r.beginPath(),r.arc(s.x,s.y,s.size,0,Math.PI*2),r.fill(),s.y+=s.speed,s.y>n.height&&(s.y=0,s.x=Math.random()*n.width)}),t.current=requestAnimationFrame(l)};return l(),window.addEventListener("resize",i),()=>{window.removeEventListener("resize",i),t.current&&cancelAnimationFrame(t.current)}},[]),S.jsx("canvas",{ref:e,"data-testid":"particle-canvas",className:"fixed top-0 left-0 w-full h-full pointer-events-none"})};function y1(){const[e,t]=$.useState(!1);return S.jsx(mm,{store:kg,children:S.jsxs("div",{className:"relative","data-testid":"render-ui",children:[S.jsx(v1,{}),S.jsxs("div",{className:"relative z-10",children:[!e&&S.jsx(Zg,{onEnter:()=>t(!0)}),S.jsxs("div",{className:`transition-opacity duration-1000 ${e?"opacity-100":"opacity-0 pointer-events-none"}`,children:[S.jsx(g1,{}),S.jsx(p1,{})]})]})]})})}Od(document.getElementById("root")).render(S.jsx($.StrictMode,{children:S.jsx(y1,{})}));
