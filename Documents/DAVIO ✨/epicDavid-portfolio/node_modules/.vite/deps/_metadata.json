{"hash": "419ae232", "configHash": "f05ff2ef", "lockfileHash": "d78be80f", "browserHash": "36659b10", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "26f874a7", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "afe924f3", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "5708da72", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4f5305f9", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.esm.js", "file": "@reduxjs_toolkit.js", "fileHash": "c87b9bf5", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "fdca4c0d", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "16f87f3c", "needsInterop": true}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "a0caa855", "needsInterop": false}, "react-redux": {"src": "../../react-redux/es/index.js", "file": "react-redux.js", "fileHash": "4755dd9d", "needsInterop": false}}, "chunks": {"chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-EQCCHGRT": {"file": "chunk-EQCCHGRT.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}