import esTranslations from "./es"; // Assuming 'es.ts' or 'es.js' exists with the new structure

describe("Spanish Translations", () => {
	it("should have correct structure and be immutable", () => {
		// Check top-level properties
		expect(esTranslations).toHaveProperty("app");
		expect(Object.isFrozen(esTranslations)).toBe(true); // Verify top-level immutability

		// Check 'app' properties and immutability
		expect(esTranslations.app).toHaveProperty("landing");
		expect(esTranslations.app).toHaveProperty("config");
		expect(Object.isFrozen(esTranslations.app)).toBe(true); // Verify 'app' immutability

		// Check 'landing' properties and immutability
		expect(esTranslations.app.landing).toHaveProperty("clickToEnter");
		expect(Object.isFrozen(esTranslations.app.landing)).toBe(true); // Verify 'landing' immutability

		// Check 'config' properties and immutability
		expect(esTranslations.app.config).toHaveProperty("title");
		expect(esTranslations.app.config).toHaveProperty("description");
		expect(Object.isFrozen(esTranslations.app.config)).toBe(true); // Verify 'config' immutability
	});

	it("should have correct landing page translations", () => {
		expect(esTranslations.app.landing.clickToEnter).toBe("Haga clic para entrar...");
	});

	it("should have correct config translations", () => {
		// Updated to match the new structure's content from the English example,
		// with 'title' and 'description' values adjusted for Spanish context if needed.
		// Since "⚒⎮𝙚𝙥𝙞𝙘.𝘿𝙖𝙫𝙞𝙙 ☻ :)" appears to be a handle/name, it's kept as is.
		// Technology stacks are typically not translated.
		expect(esTranslations.app.config.title).toBe("⚒⎮𝙚𝙥𝙞𝙘.𝘿𝙖𝙫𝙞𝙙 ☻ :)");
		expect(esTranslations.app.config.description).toBe(
			"React | TailwindCSS | Vite | TypeScript | JavaScript | MongoDB",
		);
	});

	it("should not have undefined or null values and all values should be strings", () => {
		const checkValues = (obj: Record<string, unknown>) => {
			Object.values(obj).forEach((value) => {
				if (typeof value === "object" && value !== null) {
					checkValues(value as Record<string, unknown>);
				} else {
					expect(value).toBeDefined();
					expect(value).not.toBeNull();
					expect(typeof value).toBe("string"); // Ensure all final values are strings
				}
			});
		};
		checkValues(esTranslations);
	});

	// The immutability test is now partially covered by the first test case
	// using Object.isFrozen. This additional test attempts modification to
	// explicitly ensure no changes are possible.
	it("should be deeply immutable", () => {
		const originalTranslations = JSON.stringify(esTranslations);
		try {
			// Attempt to modify a nested property
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			(esTranslations.app.config as any).title = "Modified";
		} catch {
			// If an error is caught, it means the object is frozen, which is good.
		}
		// If the object was truly immutable, the JSON string should remain the same.
		// If an error was caught, it confirms the immutability.
		expect(JSON.stringify(esTranslations)).toBe(originalTranslations);
		// Optionally, you can assert that an error was indeed thrown if the environment supports it
		// expect(modified).toBe(true); // This would confirm that the 'catch' block was hit
	});
});