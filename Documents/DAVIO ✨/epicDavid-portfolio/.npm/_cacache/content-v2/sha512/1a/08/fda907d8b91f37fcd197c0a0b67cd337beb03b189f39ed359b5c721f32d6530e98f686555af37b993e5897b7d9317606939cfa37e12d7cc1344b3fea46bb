{"_id": "camelcase-css", "_rev": "7-1a9cab38eefdd52a3c11c2626d0ec101", "name": "camelcase-css", "description": "Convert a kebab-cased CSS property into a camelCased DOM property.", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "camelcase-css", "description": "Convert a dash-separated CSS property to a camelCased DOM property.", "version": "1.0.0", "license": "MIT", "homepage": "https://github.com/stevenvachon/camelcase-css", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.svachon.com/"}, "repository": {"type": "git", "url": "git://github.com/stevenvachon/camelcase-css.git"}, "bugs": {"url": "https://github.com/stevenvachon/camelcase-css/issues"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.2.5"}, "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha test.js --reporter spec --check-leaks --bail --no-exit"}, "files": ["index.js", "license"], "keywords": ["camelcase", "case", "css", "dom"], "gitHead": "cb3b8d8fad4609d78ce54b10aa35fa3ab4c55921", "_id": "camelcase-css@1.0.0", "_shasum": "468c391d9c1ebc9540548ab5294a487a5c9032ba", "_from": "git://github.com/stevenvachon/camelcase-css.git", "_resolved": "git://github.com/stevenvachon/camelcase-css.git#cb3b8d8fad4609d78ce54b10aa35fa3ab4c55921", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "468c391d9c1ebc9540548ab5294a487a5c9032ba", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-1.0.0.tgz", "integrity": "sha512-mcLid07BxNg0ZP1+mKz+CJF2fWskaHBrqKQ7OQPgmncmPVLnkdohA+7ZH82qq7bDX67bCsBrT8j/8LQrR7rhiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXvMhAdFLkJcdhgQ8jkQoTqbBfuRY3BtHlfIUazqjHcAiBlJUwKp5wNqmM0X5YHb7oVyE0UIpI31/24VbxmgVgP/Q=="}]}, "maintainers": [{"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "camelcase-css", "description": "Convert a dash-separated CSS property to a camelCased DOM property.", "version": "1.0.1", "license": "MIT", "homepage": "https://github.com/stevenvachon/camelcase-css", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.svachon.com/"}, "repository": {"type": "git", "url": "git://github.com/stevenvachon/camelcase-css.git"}, "bugs": {"url": "https://github.com/stevenvachon/camelcase-css/issues"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^2.4.5"}, "engines": {"node": ">= 0.10"}, "scripts": {"test": "mocha test.js --reporter spec --check-leaks --bail --no-exit"}, "files": ["index.js", "license"], "keywords": ["camelcase", "case", "css", "dom"], "gitHead": "062c673ab979e8443a8c9edd5768e4079de0e436", "_id": "camelcase-css@1.0.1", "_shasum": "157c4238265f5cf94a1dffde86446552cbf3f705", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.0", "_npmUser": {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"shasum": "157c4238265f5cf94a1dffde86446552cbf3f705", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-1.0.1.tgz", "integrity": "sha512-cvhbU5XiKkPbU4TZ+8o8uMFAeNtl31W/EIy9EKLrHKFnz9EsS7/iPaKr1FkU7w5PEmCJXeS/69y2v8iUhFfn4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgS39Lp1k5KPWurlItKssnyWvPGOBuy5Xt6YAFA+tX6gIhAINPsTrK/BvhBDAfXEo0PB8g6MuMpLWNKWsT6Rfibdqm"}]}, "maintainers": [{"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/camelcase-css-1.0.1.tgz_1456551211233_0.2956849338952452"}, "directories": {}}, "2.0.0": {"name": "camelcase-css", "description": "Convert a dash-separated CSS property to a camelCased DOM property.", "version": "2.0.0", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.svachon.com/"}, "repository": {"type": "git", "url": "git+https://github.com/stevenvachon/camelcase-css.git"}, "devDependencies": {"chai": "^4.1.2", "mocha": "^5.0.5"}, "engines": {"node": ">= 6"}, "scripts": {"test": "mocha test.js --check-leaks --bail"}, "files": ["index.js", "license"], "keywords": ["camelcase", "case", "css", "dom"], "gitHead": "b108cdd6bd38ae3423383e9dce7696e6a05c36b4", "bugs": {"url": "https://github.com/stevenvachon/camelcase-css/issues"}, "homepage": "https://github.com/stevenvachon/camelcase-css#readme", "_id": "camelcase-css@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.8.0", "_npmUser": {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FnvdCC/TkM9eoypxeMPS41Vxu7moPmgsu37oeVxIhiXi8t0KrGGKCLaASkQ+/5tX4X9r/S8UDlV4Efz7VvHIRw==", "shasum": "76f0aed4924b9d3dde36adf14225c06d5a9835ae", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.0.tgz", "fileCount": 4, "unpackedSize": 3104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmlODm2+B2vVw3BJM9yrxqtx0O9Ft4T5NEXiINlBHS3wIgP8F8GN+EeqT0DI9BGICkz4TMSQEeJ/QD4SqdAm/oAfI="}]}, "maintainers": [{"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase-css_2.0.0_1522687119183_0.34261833693887866"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "camelcase-css", "description": "Convert a kebab-cased CSS property into a camelCased DOM property.", "version": "2.0.1", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.svachon.com/"}, "repository": {"type": "git", "url": "git+https://github.com/stevenvachon/camelcase-css.git"}, "browser": "index-es5.js", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-plugin-optimize-starts-with": "^1.0.1", "babel-preset-env": "^1.7.0", "chai": "^4.1.2", "mocha": "^5.2.0"}, "engines": {"node": ">= 6"}, "scripts": {"pretest": "babel index.js --out-file=index-es5.js --presets=env --plugins=optimize-starts-with", "test": "mocha test.js --check-leaks --bail"}, "files": ["index.js", "index-es5.js"], "keywords": ["camelcase", "case", "css", "dom"], "gitHead": "5fe025f5e8fdad5b1a347f2a4b301e1e32cc2723", "bugs": {"url": "https://github.com/stevenvachon/camelcase-css/issues"}, "homepage": "https://github.com/stevenvachon/camelcase-css#readme", "_id": "camelcase-css@2.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==", "shasum": "ee978f6947914cc30c6b44741b6ed1df7f043fd5", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz", "fileCount": 5, "unpackedSize": 4051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbjTpCRA9TVsSAnZWagAAzpQP/iM8aenqDc7dc3DGueCT\nVoNqTsE3vvaTGD/bEspr0uJOnJs7DNnrFlhGIuKm+f++dgWZ7AAa/OEnmqoH\nHyTaQeLZjX99459/b8x7fjCIok/W1I4eM/yZWXg+rtLmYAYd0ODPrMm+fZ7p\nEBM+Uo0cV/4aNhsQQVRriZfXR02mR9H11nNx9Bht1R/n4+sFGOSZDkprKH9i\ntHgvXDkxXGPm/da13LKeVVzzHySS9/WOAQi6W5P2nbmTSBYuwpOv0xXKOik0\nDVorsHmHbZtTWtJB9cIQhFLDpxH6sy8xDuz/GODxoK9GLBanPW3bxtCUAcb7\nzG/NRP/lpLoZzl1Tna8Yp994SF/rthyP6fuCqDKXNHbxtFSJoO44muHNEWMH\nOUc8Dbzr3C/NjBJSWAiFzHwZYmRocTeua8DxLl1lcIOHoHe4+l7ljAAhEc/L\nbflnk1Rhv0mCyuz+mwJemHzb5+EItdwh0H6rpxvLdKOiDkGdLLpvjNMkuZ+J\nSeTMoTJfE4k5TykLMsjXwb/FsOvjqPsKrpBIVssQK2ZvrxT0B1ckZlbrjVze\nqucfKIXlOUOlVG8YDGIaI62ET22SqVtkukqwpmLmdlyvf5UhNjZKu9PAi7n+\nGdhiqW8wFKP/+JxR8wk+Y7HXoSHLWzuF7a8tpKgVBaarAjX0EZcqGCXrg4vZ\nYDIj\r\n=gVkR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZtNWVuk0LB2kkUZk0MYujvr33KHVXuhv35PAoApUlCgIhAPxGdVz2zcny3tr0C6ncpfjp4+2r9360hvxd+7gOxKu9"}]}, "maintainers": [{"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase-css_2.0.1_1533949161335_0.8238687452019227"}, "_hasShrinkwrap": false}}, "readme": "# camelcase-css [![NPM Version][npm-image]][npm-url] [![Build Status][travis-image]][travis-url]\n\n> Convert a kebab-cased CSS property into a camelCased DOM property.\n\n\n## Installation\n[Node.js](http://nodejs.org/) `>= 6` is required. Type this at the command line:\n```shell\nnpm install camelcase-css\n```\n\n\n## Usage\n```js\nconst camelCaseCSS = require('camelcase-css');\n\ncamelCaseCSS('-webkit-border-radius');  //-> WebkitBorderRadius\ncamelCaseCSS('-moz-border-radius');     //-> MozBorderRadius\ncamelCaseCSS('-ms-border-radius');      //-> msBorderRadius\ncamelCaseCSS('border-radius');          //-> borderRadius\n```\n\n\n[npm-image]: https://img.shields.io/npm/v/camelcase-css.svg\n[npm-url]: https://npmjs.org/package/camelcase-css\n[travis-image]: https://img.shields.io/travis/stevenvachon/camelcase-css.svg\n[travis-url]: https://travis-ci.org/stevenvachon/camelcase-css\n", "maintainers": [{"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T05:30:29.480Z", "created": "2015-08-28T13:40:26.404Z", "1.0.0": "2015-08-28T13:40:26.404Z", "1.0.1": "2016-02-27T05:33:34.486Z", "2.0.0": "2018-04-02T16:38:39.300Z", "2.0.1": "2018-08-11T00:59:21.455Z"}, "homepage": "https://github.com/stevenvachon/camelcase-css#readme", "keywords": ["camelcase", "case", "css", "dom"], "repository": {"type": "git", "url": "git+https://github.com/stevenvachon/camelcase-css.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.svachon.com/"}, "bugs": {"url": "https://github.com/stevenvachon/camelcase-css/issues"}, "license": "MIT", "readmeFilename": "README.md"}