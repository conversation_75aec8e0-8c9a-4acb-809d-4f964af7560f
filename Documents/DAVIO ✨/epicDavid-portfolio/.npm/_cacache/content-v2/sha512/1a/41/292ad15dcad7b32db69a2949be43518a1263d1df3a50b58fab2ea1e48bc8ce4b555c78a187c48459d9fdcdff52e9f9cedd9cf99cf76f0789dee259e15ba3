{"_id": "levn", "_rev": "24-6f495491e4e13ac826f06c3444b994fa", "name": "levn", "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "dist-tags": {"latest": "0.4.1"}, "versions": {"0.1.0": {"name": "levn", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https//github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": "https://github.com/gkz/levn/issues", "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "type-check": "~0.2.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.1.0", "dist": {"shasum": "7aeb836aa99ab24fc440813f0d2095683bd927e7", "tarball": "https://registry.npmjs.org/levn/-/levn-0.1.0.tgz", "integrity": "sha512-1xuvZ+Fb6BrRBsWnoAXT9lU1aTJOahWidKjXhmmG/ggbpgRttz+6uzikQkIGJtmdjQtNifxq7rO/N5EYhnkkIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW2Etd69toX9nYjIR7JmHGFCSwjsr4k9Mj4YrccXDcWgIgCPnWdWkLiYgEiuB5FhHowP5QotXpyNbqM6+4+yprZ8g="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "levn", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "type-check": "~0.3.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.2.0", "dist": {"shasum": "4459afbb892370b28eedeeb20362302dea002e4b", "tarball": "https://registry.npmjs.org/levn/-/levn-0.2.0.tgz", "integrity": "sha512-ihC++Ku8I75cwtzvmcZe5Lac4CT2L2nwcBZpl5K5hcqzjduiFHHySd4N13W3lcLTMgwCrUA1L4Z2pEa5kiIiRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFt2AsvEPwsk4W1mnUOU0Ikz0IYUtsWPmFBqlrLLvlA9AiB0XgVcLt0afhhAFs2Y4uc57il1Q7X20z2kdHOrWy/3JA=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "levn", "version": "0.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "type-check": "~0.3.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.2.1", "dist": {"shasum": "3d5a758b6d5157ddcbe644d127c4edc8b851971a", "tarball": "https://registry.npmjs.org/levn/-/levn-0.2.1.tgz", "integrity": "sha512-nVvlEDTviPmm2wgEAe1B9haHSElTcX6chBvACPZMfWURcUt+5UXfCQ52/n7IMepsf7oZFimcP4l/NqhXtdHI9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW+ZH5mJzcUROKDFzgA19J0sk1vqiVK2LXmIW8DdZz+QIgJZTLzYBnwTAzrjphDLW57TPItk72fYOaQVQzOTfchvY="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "levn", "version": "0.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "type-check": "~0.3.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.2.2", "dist": {"shasum": "664bdfa08778f8d6f731da6a2ffa6e1bd91875e8", "tarball": "https://registry.npmjs.org/levn/-/levn-0.2.2.tgz", "integrity": "sha512-ZtWddL1QMMSUZYPqGDvaG2PQqRzlm6Cy5utKorH8apIM3q9zq2PWbAhzm8pIqrE0a4iESMpc3EDfpiNEyIR9cQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVN+3tPGnHPEvTl8nUJFNLcXhuQhHfH/sJi3YHYcMs0AIhAIHCC4wclx4Sgu0P4Y8vWNkNIQ7oqhFbi82DJytoJlOv"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "levn", "version": "0.2.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "type-check": "~0.3.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.2.3", "dist": {"shasum": "e17520e044bc5b72100cc51aeea4463e50af3bf4", "tarball": "https://registry.npmjs.org/levn/-/levn-0.2.3.tgz", "integrity": "sha512-+k+dNP0+EC3WMecCapjn1qoXV016wRvZhMYK3N9Pdt/97tSkzTRTGWcCcL+U+lR5b4muWmcBrDel5xHlPf1X0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfwvz1haU82WY0gRUrmiZ99DV9ysDXPS591vz41j/65AIgIIzk4MNeZHfPAtp8hGHU40+cRxSxpIxwDNfpBHgzNi8="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "levn", "version": "0.2.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.0", "type-check": "~0.3.1"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.2.4", "dist": {"shasum": "4dcfc656a1af95c2c0dfee18876cd0a66f549ff5", "tarball": "https://registry.npmjs.org/levn/-/levn-0.2.4.tgz", "integrity": "sha512-gnp53CDvwi1n7nUoGzvr67TharM3357QZNsLl0vnqAJgTwxg1WKn8uwi0Prz7N8Y8dlOwzKBMys1phSj5CzD0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5v7UhyhXhvw/dMQUb2oQCQZfULFMBybQv6Sb+Kr0KFAiBcUhGbOBQVzdJTNo6arb9SRrBpM8/9EpKsKtGklHS/kw=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "levn", "version": "0.2.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/levn/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.0", "type-check": "~0.3.1"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "levn@0.2.5", "dist": {"shasum": "ba8d339d0ca4a610e3a3f145b9caf48807155054", "tarball": "https://registry.npmjs.org/levn/-/levn-0.2.5.tgz", "integrity": "sha512-mvp+NO++YH0B+e8cC/SvJxk6k5Z9Ngd3iXuz7tmT8vZCyQZj/5SI1GkFOiZGGPkm5wWGI9SUrqiAfPq7BJH+0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKSG1i3KdV62qKXNVK7Q/7PEuCGKu8ctHsMd/zqDd3jwIgfOp/FNEEoCaYU48lDrsv/kwX4Cc40c61BYiS0OwVo7o="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "levn", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "devDependencies": {"livescript": "~1.4.0", "mocha": "~2.3.4", "istanbul": "~0.4.1"}, "gitHead": "a92b9acf928282ba81134b4ae8e6a5f29e1f5e1e", "_id": "levn@0.3.0", "_shasum": "3b09924edf9f083c0490fdd4c0bc4421e04764ee", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "3b09924edf9f083c0490fdd4c0bc4421e04764ee", "tarball": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "integrity": "sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCp1rg3VAPC5WAuql9Cz3VnRPb7XSqW1MpG8H1li8eWCQIgR/UqI8zY9gOIaEcDSeSsbGoxDOI3e6lgq5ad4MouP3I="}]}, "directories": {}}, "0.4.0": {"name": "levn", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "gitHead": "0c5f341936715f9dc4be60c7141ec5eb0769f536", "_id": "levn@0.4.0", "_nodeVersion": "6.16.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-6REBFsAveILomsPsobo2dy9hNNv+AS+u7fcTaiWU918cqWDl8nZqN63RqhWGBFE4zZRbUslXnpKHFgj1vJdNOQ==", "shasum": "fc813073038db9d43f56f26185c9b17b54d6dc9a", "tarball": "https://registry.npmjs.org/levn/-/levn-0.4.0.tgz", "fileCount": 6, "unpackedSize": 24947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeh+VZCRA9TVsSAnZWagAAGFQP/iKze0qKloHVOdgSXL6s\nX6DDP/jYwGsORmtQmCj7v8KtsDnWDBBXYys3eIcd+D5bBrNnbqav453IiPqw\n7zH9w3bFCGTqOLVAVKJrZiCOG+rY4VKMDWVrgCM5U6h4UYpW7yx7c7SD2KxL\n1W78SpmejSSz6sMXTI7yaH93U4sy+WSYoXRunqdmL14hXFwIuPS6iTx4iOyQ\n0lkTQLaRdvnhabs/NHzvT6yEsXi30hKsKOjQjsDyBNjKKP3H7vekj52EAdqn\nFxzV6On5eHqsmMASUypMQSdAJgJzVJHtLoQHe2KHSufvAHZFavjLjaJeYrBS\n8BIY076oZjciCoBqLamni0F7ozhaZa0FFQ1CrKWLmdT3WTHowpA3hiQwSUvX\nxfaGm4Bz8g9TNDR59k9CAkcARDDCb3hVW+fbSWQtti7D/XHH/ZussCCxsqy5\n4O+vNcfOMTBuBYUVkxbw4W2/Cq/q8KKcnhEAbvf7Go+dwRXTb55Cn0EaVh6N\nx6t6IBCIjbcjCNbZ5iJ04Q75+x3LZO2SLGK//bTEiZBtQFSmqMFb6giBYB7H\n4WPQtT7/hJIjIBeGDQhcB0YejyHBvg74VYyrvs6zZfLkTznAXo/aKQWrTHSf\n+b+Wrz7ieeu4xT6Attxhfj1boqahtxQAR8+8KmdOV0iFp935CGWLBKzBKTOH\nCpcL\r\n=c0Xk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRFKQvNHod1uiKUCPErV4YnHL7rfZZUo8VZ+n/pDSVWAIhAMqKsbTyB/lz8rYbD3wR8kgVY7kZmQVWeQwomcX+54Lw"}]}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/levn_0.4.0_1585964377325_0.30892921032298104"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "levn", "version": "0.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/levn/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "gitHead": "eb1040d2ec21bc3655ee140f38eb880adcd77134", "_id": "levn@0.4.1", "_nodeVersion": "6.16.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "shasum": "ae4562c007473b932a6200d403268dd2fffc6ade", "tarball": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "fileCount": 6, "unpackedSize": 24947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeh+trCRA9TVsSAnZWagAASnwP/0GqXB5d1RSf2hLXdEJc\nFR3ZuUdR8W8Nyrxb7so2IwHkXGRo5UHiiQ3J1LGtPz4KrWUhdOAB1y+3OmBb\nspLwIVcX/bYfJNU+NGpTN7sCOVFRf97pguD9zy+p7EVF+mFoUOv1nQke34xV\nik83Jwi6SWYqWNc3FMuqvY0Uh5ITMM4GKELD9EuMeIW73SAZKTwT9F/hRpsm\nOw386wT2aNhYII/A7VOzvnjdK84O8OgQCGypscovspfmvZRBE62BAVEibEG6\nf7urWnZAcv1wnMzN1NRsZlYwsGJhI/J90diW2wTuZOgxBW+54uMLcIuOHZcx\na6e2HBOLwNJILBb2PyH6Izye4BSinPaW2aZ0vKsHJyyFoPo44wmhf25MBdJZ\njddUjv1mA/PluOY6fhuP9MK+TjwK3xoWTEEL45BQcqmRWqwPPYg0D/7ca/KL\nO6GX/DzpxaJvHG7fcQIp+gOtzVpUzMLzshhnw2NkUi+aocecOPlItfvp8KT3\n7oL0zCG0+iA3R2Kc1xT4WQOzAgoxS7tktv0nFBPHjROXuw7kaD2+BIJ5G4bk\nolMXMegr/E8TNHBhCxlRChHrm8emRH15ldSg4bWIZ+5v+Zl89vxoF/KBd/de\n+lMsw/lbFk1Z2g4KLEw88jp9NJc4pwIBMX4tUYQGe9jcf8VgdJKlIuurchk2\n4/dN\r\n=jiMv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCHzFlDcWpiUGwP1Top48qY7c8HJS8Dh96wgPI6z3f2fcCIQDY4zrqL8lcDzeCAkenXew7aTI/VIov/8DNJa8el94h/Q=="}]}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/levn_0.4.1_1585965931388_0.22817660164562037"}, "_hasShrinkwrap": false}}, "readme": "# levn [![Build Status](https://travis-ci.org/gkz/levn.png)](https://travis-ci.org/gkz/levn) <a name=\"levn\" />\n__Light ECMAScript (JavaScript) Value Notation__\nLevn is a library which allows you to parse a string into a JavaScript value based on an expected type. It is meant for short amounts of human entered data (eg. config files, command line arguments).\n\nLevn aims to concisely describe JavaScript values in text, and allow for the extraction and validation of those values. Levn uses [type-check](https://github.com/gkz/type-check) for its type format, and to validate the results. MIT license. Version 0.4.1.\n\n__How is this different than JSON?__ levn is meant to be written by humans only, is (due to the previous point) much more concise, can be validated against supplied types, has regex and date literals, and can easily be extended with custom types. On the other hand, it is probably slower and thus less efficient at transporting large amounts of data, which is fine since this is not its purpose.\n\n    npm install levn\n\nFor updates on levn, [follow me on twitter](https://twitter.com/gkzahariev).\n\n\n## Quick Examples\n\n```js\nvar parse = require('levn').parse;\nparse('Number', '2');      // 2\nparse('String', '2');      // '2'\nparse('String', 'levn');   // 'levn'\nparse('String', 'a b');    // 'a b'\nparse('Boolean', 'true');  // true\n\nparse('Date', '#2011-11-11#'); // (Date object)\nparse('Date', '2011-11-11');   // (Date object)\nparse('RegExp', '/[a-z]/gi');  // /[a-z]/gi\nparse('RegExp', 're');         // /re/\nparse('Int', '2');             // 2\n\nparse('Number | String', 'str'); // 'str'\nparse('Number | String', '2');   // 2\n\nparse('[Number]', '[1,2,3]');                      // [1,2,3]\nparse('(String, Boolean)', '(hi, false)');         // ['hi', false]\nparse('{a: String, b: Number}', '{a: str, b: 2}'); // {a: 'str', b: 2}\n\n// at the top level, you can ommit surrounding delimiters\nparse('[Number]', '1,2,3');                      // [1,2,3]\nparse('(String, Boolean)', 'hi, false');         // ['hi', false]\nparse('{a: String, b: Number}', 'a: str, b: 2'); // {a: 'str', b: 2}\n\n// wildcard - auto choose type\nparse('*', '[hi,(null,[42]),{k: true}]'); // ['hi', [null, [42]], {k: true}]\n```\n## Usage\n\n`require('levn');` returns an object that exposes three properties. `VERSION` is the current version of the library as a string. `parse` and `parsedTypeParse` are functions.\n\n```js\n// parse(type, input, options);\nparse('[Number]', '1,2,3'); // [1, 2, 3]\n\n// parsedTypeParse(parsedType, input, options);\nvar parsedType = require('type-check').parseType('[Number]');\nparsedTypeParse(parsedType, '1,2,3'); // [1, 2, 3]\n```\n\n### parse(type, input, options)\n\n`parse` casts the string `input` into a JavaScript value according to the specified `type` in the [type format](https://github.com/gkz/type-check#type-format) (and taking account the optional `options`) and returns the resulting JavaScript value.\n\n##### arguments\n* type - `String` - the type written in the [type format](https://github.com/gkz/type-check#type-format) which to check against\n* input - `String` - the value written in the [levn format](#levn-format)\n* options - `Maybe Object` - an optional parameter specifying additional [options](#options)\n\n##### returns\n`*` - the resulting JavaScript value\n\n##### example\n```js\nparse('[Number]', '1,2,3'); // [1, 2, 3]\n```\n\n### parsedTypeParse(parsedType, input, options)\n\n`parsedTypeParse` casts the string `input` into a JavaScript value according to the specified `type` which has already been parsed (and taking account the optional `options`) and returns the resulting JavaScript value. You can parse a type using the [type-check](https://github.com/gkz/type-check) library's `parseType` function.\n\n##### arguments\n* type - `Object` - the type in the parsed type format which to check against\n* input - `String` - the value written in the [levn format](#levn-format)\n* options - `Maybe Object` - an optional parameter specifying additional [options](#options)\n\n##### returns\n`*` - the resulting JavaScript value\n\n##### example\n```js\nvar parsedType = require('type-check').parseType('[Number]');\nparsedTypeParse(parsedType, '1,2,3'); // [1, 2, 3]\n```\n\n## Levn Format\n\nLevn can use the type information you provide to choose the appropriate value to produce from the input. For the same input, it will choose a different output value depending on the type provided. For example, `parse('Number', '2')` will produce the number `2`, but `parse('String', '2')` will produce the string `\"2\"`.\n\nIf you do not provide type information, and simply use `*`, levn will parse the input according the unambiguous \"explicit\" mode, which we will now detail - you can also set the `explicit` option to true manually in the [options](#options).\n\n* `\"string\"`, `'string'` are parsed as a String, eg. `\"a msg\"` is `\"a msg\"`\n* `#date#` is parsed as a Date, eg. `#2011-11-11#` is `new Date('2011-11-11')`\n* `/regexp/flags` is parsed as a RegExp, eg. `/re/gi` is `/re/gi`\n* `undefined`, `null`, `NaN`, `true`, and `false` are all their JavaScript equivalents\n* `[element1, element2, etc]` is an Array, and the casting procedure is recursively applied to each element. Eg. `[1,2,3]` is `[1,2,3]`.\n* `(element1, element2, etc)` is an tuple, and the casting procedure is recursively applied to each element. Eg. `(1, a)` is `(1, a)` (is `[1, 'a']`).\n* `{key1: val1, key2: val2, ...}` is an Object, and the casting procedure is recursively applied to each property. Eg. `{a: 1, b: 2}` is `{a: 1, b: 2}`.\n* Any test which does not fall under the above, and which does not contain special characters (`[``]``(``)``{``}``:``,`) is a string, eg. `$12- blah` is `\"$12- blah\"`.\n\nIf you do provide type information, you can make your input more concise as the program already has some information about what it expects. Please see the [type format](https://github.com/gkz/type-check#type-format) section of [type-check](https://github.com/gkz/type-check) for more information about how to specify types. There are some rules about what levn can do with the information:\n\n* If a String is expected, and only a String, all characters of the input (including any special ones) will become part of the output. Eg. `[({})]` is `\"[({})]\"`, and `\"hi\"` is `'\"hi\"'`.\n* If a Date is expected, the surrounding `#` can be omitted from date literals. Eg. `2011-11-11` is `new Date('2011-11-11')`.\n* If a RegExp is expected, no flags need to be specified, and the regex is not using any of the special characters,the opening and closing `/` can be omitted - this will have the affect of setting the source of the regex to the input. Eg. `regex` is `/regex/`.\n* If an Array is expected, and it is the root node (at the top level), the opening `[` and closing `]` can be omitted. Eg. `1,2,3` is `[1,2,3]`.\n* If a tuple is expected, and it is the root node (at the top level), the opening `(` and closing `)` can be omitted. Eg. `1, a` is `(1, a)` (is `[1, 'a']`).\n* If an Object is expected, and it is the root node (at the top level), the opening `{` and closing `}` can be omitted. Eg `a: 1, b: 2` is `{a: 1, b: 2}`.\n\nIf you list multiple types (eg. `Number | String`), it will first attempt to cast to the first type and then validate - if the validation fails it will move on to the next type and so forth, left to right. You must be careful as some types will succeed with any input, such as String. Thus put String at the end of your list. In non-explicit mode, Date and RegExp will succeed with a large variety of input - also be careful with these and list them near the end if not last in your list.\n\nWhitespace between special characters and elements is inconsequential.\n\n## Options\n\nOptions is an object. It is an optional parameter to the `parse` and `parsedTypeParse` functions.\n\n### Explicit\n\nA `Boolean`. By default it is `false`.\n\n__Example:__\n\n```js\nparse('RegExp', 're', {explicit: false});          // /re/\nparse('RegExp', 're', {explicit: true});           // Error: ... does not type check...\nparse('RegExp | String', 're', {explicit: true});  // 're'\n```\n\n`explicit` sets whether to be in explicit mode or not. Using `*` automatically activates explicit mode. For more information, read the [levn format](#levn-format) section.\n\n### customTypes\n\nAn `Object`. Empty `{}` by default.\n\n__Example:__\n\n```js\nvar options = {\n  customTypes: {\n    Even: {\n      typeOf: 'Number',\n      validate: function (x) {\n        return x % 2 === 0;\n      },\n      cast: function (x) {\n        return {type: 'Just', value: parseInt(x)};\n      }\n    }\n  }\n}\nparse('Even', '2', options); // 2\nparse('Even', '3', options); // Error: Value: \"3\" does not type check...\n```\n\n__Another Example:__\n```js\nfunction Person(name, age){\n  this.name = name;\n  this.age = age;\n}\nvar options = {\n  customTypes: {\n    Person: {\n      typeOf: 'Object',\n      validate: function (x) {\n        x instanceof Person;\n      },\n      cast: function (value, options, typesCast) {\n        var name, age;\n        if ({}.toString.call(value).slice(8, -1) !== 'Object') {\n          return {type: 'Nothing'};\n        }\n        name = typesCast(value.name, [{type: 'String'}], options);\n        age = typesCast(value.age, [{type: 'Numger'}], options);\n        return {type: 'Just', value: new Person(name, age)};\n    }\n  }\n}\nparse('Person', '{name: Laura, age: 25}', options); // Person {name: 'Laura', age: 25}\n```\n\n`customTypes` is an object whose keys are the name of the types, and whose values are an object with three properties, `typeOf`, `validate`, and `cast`. For more information about `typeOf` and `validate`, please see the [custom types](https://github.com/gkz/type-check#custom-types) section of type-check.\n\n`cast` is a function which receives three arguments, the value under question, options, and the typesCast function. In `cast`, attempt to cast the value into the specified type. If you are successful, return an object in the format `{type: 'Just', value: CAST-VALUE}`, if you know it won't work, return `{type: 'Nothing'}`.  You can use the `typesCast` function to cast any child values. Remember to pass `options` to it. In your function you can also check for `options.explicit` and act accordingly.\n\n## Technical About\n\n`levn` is written in [LiveScript](http://livescript.net/) - a language that compiles to JavaScript. It uses [type-check](https://github.com/gkz/type-check) to both parse types and validate values. It also uses the [prelude.ls](http://preludels.com/) library.\n", "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:32:51.151Z", "created": "2013-10-04T20:10:33.861Z", "0.1.0": "2013-10-04T20:10:35.117Z", "0.2.0": "2013-10-29T23:20:26.681Z", "0.2.1": "2013-12-17T23:20:46.320Z", "0.2.2": "2014-03-20T00:28:20.920Z", "0.2.3": "2014-03-20T23:59:27.077Z", "0.2.4": "2014-04-08T06:17:16.950Z", "0.2.5": "2014-04-29T05:16:08.135Z", "0.3.0": "2015-12-29T05:57:03.996Z", "0.4.0": "2020-04-04T01:39:37.463Z", "0.4.1": "2020-04-04T02:05:31.527Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "bugs": {"url": "https://github.com/gkz/levn/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"quzhi78": true, "pwn": true, "flumpus-dev": true}}