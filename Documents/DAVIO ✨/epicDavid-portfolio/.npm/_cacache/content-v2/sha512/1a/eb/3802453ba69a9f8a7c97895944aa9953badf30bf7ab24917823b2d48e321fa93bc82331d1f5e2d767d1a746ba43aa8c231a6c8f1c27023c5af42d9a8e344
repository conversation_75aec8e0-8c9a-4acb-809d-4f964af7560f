{"_id": "@babel/plugin-transform-dotall-regex", "_rev": "102-ce77b6a67d576fe3d9385703938f8263", "name": "@babel/plugin-transform-dotall-regex", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.36": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.36", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.36", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "07b1f135e22d597878fdb4b75a1c414d4d3ae863", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.36.tgz", "integrity": "sha512-b46NEiKIf9wT1Id/nUDjqy8MZkcwAAxaC9CZSCacA6uB9+MdR5weh/vA6u8gxNKBTEE00hmNloBWoRZ9e52FWw==", "signatures": [{"sig": "MEUCIQDOUCZwrAGL95KdVW9wZS7JwSvKpv0jxvYNw3RBK79d+AIgerojOLFRgaw0Aq3+By2BoGIoGAsRi6Vrd2qYhaTgom0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex-7.0.0-beta.36.tgz_1514228712515_0.17982800910249352", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.37", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "805cbb032591eb490777f1b1b80516aee65e5fb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.37.tgz", "integrity": "sha512-FDuLTnpB5F/jTKgKW6vSA1iGRHKqnhvj1/aOoVP7N7A7ZB1jeV4SCKMxIHM2QMT6Jh2pt5BDREThnWRWGT8JAw==", "signatures": [{"sig": "MEYCIQDEe/K5PIEWWtUO9HjQPQOnpSd8/B8ZVYTXpxixzuv1LAIhAOgw1DE3R8+/yys4yWo+mgWl8hvVPx5Pix3NeEG2dZU8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex-7.0.0-beta.37.tgz_1515427373341_0.9675894523970783", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.38", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "65f6bf180c3d7a87773db672c26192e361d0f7ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.38.tgz", "integrity": "sha512-MYjo3Fg3EjmAcZBG+gnbP8HjdmkoRpjGV68+Q9w853Ye9VimO47bz9HBojsClYpXsXbyxNbPRZteGU/jrMcKig==", "signatures": [{"sig": "MEQCIEQjCFKD7RPiE70rpcSM4NEdWquH+PUTwtiHAa4lhfC5AiBmipTCXz0z5vE0trVLqLZHKgKn1bNLqcQ+EkHbmZ1JeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex-7.0.0-beta.38.tgz_1516206740589_0.9564708122052252", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.39", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "cb403b70bad7f302832bdbc8eeeb53832852ca5d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.39.tgz", "integrity": "sha512-GeVYCtbispQixusVDn28jF6BZEM5fihUz2ffTWM9Rlnbn2V5tVCHLuwslbHnqtMaIVdAG6/DmXkhdbkymQElfA==", "signatures": [{"sig": "MEQCICzNmUfrW8z8yuP15fTH4Nn9RNZToSINpJ65wuQiXHD9AiAL4xeZlhDF0VVLVpgWZ+xyvTdD2PkNuVlSGy+KOigoYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex-7.0.0-beta.39.tgz_1517344070679_0.03904038039036095", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.40", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "89b5ccff477624b97129f9a7e262a436437d7ae2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.40.tgz", "fileCount": 10, "integrity": "sha512-5npi7X6QGNWWfXxVBMQl+fEAn0LceNNFT139yaGRkyWZtUXmueFLgXKsa9dY2DhuEis29KuZrwGcmGSQWnEmtg==", "signatures": [{"sig": "MEYCIQDCYA9dULqIET5CotAkiynm9ZHeyIH7wch7CU+uR0PjUwIhAJ4ksPbJdB/5HAUSgfrtj+3EDR0F7frGPmGYF/wfYy/G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3979}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.40_1518453728976_0.9017522507150202", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.41", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "0dc2f0411a11b2821caf0d7a1ca82c079cb35db5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.41.tgz", "fileCount": 10, "integrity": "sha512-4asyDwqQmjqkfHwdocuBTlZK3OA7jTF+HmbrIzdKAzlZjVtLUwgvYTc4U9QpGZU2jL/982E0RCg9DLSgNpHUPw==", "signatures": [{"sig": "MEYCIQDHFIRIR/ukT/YSZ3lU2HVrKPgz9D1WkdPDV4z0hSl7CgIhAJ9F+MXaukfdlWw+6lmCal8epytBYhEhTwmCyAnbFq4d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4275}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.41_1521044766880_0.6396635620484117", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.42", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "af7ead30c1b6c3ea8a53973cfcfdbda9edc3c967", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.42.tgz", "fileCount": 10, "integrity": "sha512-g/XbJzib6qmbHOJv/2n/bVKLcfJUBRSo9zO73O7lvJEYh8JvHWM/oVU829ztVPpdPT3xgHMiTVhnV/O5r5dYNA==", "signatures": [{"sig": "MEUCIBq67lImwSNsnvj0QQ7zTIds5pBf8jYvZwGhMRLKmIZAAiEAqIewSmkVXRdeRBfoy6+d0b0E3i5l67rsKZ0tssyT4BE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4275}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.42_1521147040761_0.6448379757070257", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.43", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f4c69c5cb09f6e7a6f2bb5441eafaab5b0232c19", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.43.tgz", "fileCount": 10, "integrity": "sha512-Hq3/nS/1fe/Kfxn3BvKfTyZ+Uxn6kBWkpB6ySJPMfhVI8N1Sp9xvAj9KZy2nqdxYGdwwq1CvYnUXSIUGC8KMOw==", "signatures": [{"sig": "MEUCIHyVf3Lc2Ztr1An4rNG/jcvMh5QqgRr9Q0OZSaC+3RI1AiEAyOT/J9lDZnqzKapVS1O5W37jpVePTBL56CBeDI8sVQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4575}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.43_1522687703222_0.0804854135033326", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.44", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "414bd71f39199e45a8ddaa8053cb5bd9690707f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.44.tgz", "fileCount": 10, "integrity": "sha512-4oF4gIPOUbljRGzdMdTAKVojOurkoMh3QLsGTPgNp1HrGtIKAQMprojA9O6gXwseIqiaVSnx3RwaVo3nHzS0oA==", "signatures": [{"sig": "MEYCIQDXQhwxpErltj19S7TMGWcjBrHCVlSENpd6ruhCQf0Y2wIhANSX/FxXcjTOJb7VRuwkapeTdQOdLad2D/F6iQbWYB60", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4633}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.44_1522707605071_0.16252698140953825", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.45", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f79c7f0ddbe01e85342c6cb6b94c957c68a68f87", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.45.tgz", "fileCount": 10, "integrity": "sha512-56rStS71rA/whe/lFvy/uLTVv3w+kEh/hhBBqbfXE76pWOAiSkoc67NyINbQzJ0hzRQ/y6b8tkezeEWThg0LLQ==", "signatures": [{"sig": "MEYCIQDOW2Cd/nLVMZdcq9uaXOYlDk2BvlSvSDDnQDH4WEbSmgIhANcFaLfpfZtX/mttNUTjZuUSeEhbKRpB1Z4/g9S6GNkO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1bCRA9TVsSAnZWagAAw8gQAJaHMRYkCFvqQQYNc38I\n3bQCCEI/VKoo0lMq87uxuBOPo99rTK9VoLxyAtW5s3usnYf083oOiMOJEAST\n8HboxRT/bns5lPM+E7j68ueiLwz3gVOPJQO4yPGE9Qz0TvlhQSMZWte2nTBR\nY7mS9puXENZrMb/PHybkWRlfckARTy+ZGZBsa1pRJlYcN9mEdVqkD+P7Mj+/\nIafMlWbTQ27CzOhJH3xPvGrjDKr4h5KSEviGko4DHFlN/VnJnxvbiKSwiqa5\nkUhAn4JihnUiax20ACbGeowEXc6N2g4S4qTknE8C7qlHKOP79MrccGG8D0Hk\nfKKmyXOpU0Bh62yiEcUiWGWXlLX9lcVT7dR4f4kalkkJF5ydKy1t9fWaT9Fn\nhP7aD5lKpRpAXcpS5BGs9WCOuob95O8ZE9/lD+DHUxcicRqpCzb/WKWFA7y4\n7s9YuN820+3VNftAwohMWPJj4FnjXVGBmQUt4+t4AqXhJrQtoZGXwq8WMXPA\niisrbHlx7OBukHnXbGEWATlH7jEl4Sol1LF5wfPPAXk4T0eUkcUH/N7mqbhq\nJSjBr4B0Dg6FTn4GHXgU7cIR3gd7WrKf8PukV/2MXOHbCud9u7/x3GIr/oVy\n+L+ZGgD/8OBiNn/gkISu4++EcyNGzxEEN0VrEp3FV9tNhW6U0SB2mqA+ub4Z\nfHXT\r\n=WBKE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.45_1524448602781_0.294674741012116", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.46", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e5bbd78c1a94455e6d5dd1c77f32357b84355e06", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.46.tgz", "fileCount": 10, "integrity": "sha512-5bO0XvTP+2LFDQ9qT/WaXfyieLtqz1yGsfOuq86VXmwX9tDnBnNS6pCHEGFQ866c1HmlNBWtaXttTTnvWkFBkw==", "signatures": [{"sig": "MEUCIQCfAGdAZJ7wUZVjf1qi15J88K4brrYZXu/X0WG4ghDNqgIgEGSErGBYfTomaBAlDIhJv1QqLC1uOnroP8dbxyj217k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGKCRA9TVsSAnZWagAAfwEP/Rf00fs4pFw2JMAgimVB\nMjLE43Skini8tblRQ4VQKzivbKvDmCuMmE9j6cXFEuDDNfejM9pMd0qOsrOM\nkI0ZwDTTUvSAH//TTkF8sR0MJhD+4Z74i7DSklyACR9d5aY1UDDQuun0IseH\nmPxUsoU+TJwzqUkqQ4rDvJ+Y8d4gkmIfxr9cNnPtzj9hr+SloO2hbWh2p7e6\nD7v1o6xhKJuCbAREsuKMOa3MhZ2J/F9IOqKA6wX4AY3vwGItbIyjeq2ayLgv\nSboEk14iVYykk16QPXvCpVcjCVTp+a1fSX3iQtrixGnr+PhfoU2ghI+0XNUr\niw5orN7dVIR2Dc7cyeHyCs8cI/QQBCWk0O8pif2vlmkvb84RxQoyrY8dX0w9\nI8Im7dWrax6Ymeijo2k/C4ff81uEOXX4dn085ttCEAYlpmEqeFfwtUH5h/s7\nz6wIXVN2R1fPO+nmA9/g5FfIbUe0mK3ZHXjgrfKfqp4N2vVrz5IvQo1P22Xb\nnv8vn/aGnY7TTV/SbTPwOFt/mVU4+Cghr0qbGs/kkBdRHmE/FMUh1Al3f1oY\nBkogb/+flpU1ccW7V61oh5k3IVIbJ9vSWIZ7iI6gxmsiTcTKtVeB7ErvIJIX\nldmeTclvIUHt87H0LINvEhqs94QprjUNxOiKGkbfpwzg4+Tlhhv0WF17KUMJ\nWXP8\r\n=C70z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.46_1524457866311_0.5999051876150119", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.47", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d8da9b706d4bfc68dec9d565661f83e6e8036636", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.47.tgz", "fileCount": 10, "integrity": "sha512-ofB5GwipMoaOH3Qyr5g5FpXWePhIAaD4zMDOoAHDYBPuLWxzAME8YQCa0S3HJf3eTu/HTN/c/G1gDwDB8Z/gKQ==", "signatures": [{"sig": "MEQCIEiIY1WGb3/yuPRYAV9mXRLevcce3GI7d3C+oNsP5t4xAiBUFxOvryOGnrLxxRg37Anmiaf/kzyPhGAibNuVlwWzuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUQCRA9TVsSAnZWagAAYc0P/3I4/7Brigf97RghB0Wl\nUEK1rYNm/88xilb8JlC51wwEpu0jzHHtJEm3MXolvK2lwvq+Cf/q0DqvSBLh\nBN1D2CW2Ohaadl2FPlngFp2KUJOsBzeIZxj17KnIY59yt5zc4xkuYudy00eq\nMlHwSROVPcr5FFRmo23y6u+i73nDXbGyhCTwsfBBQZ8VPhViPBLWxLdjWjvd\nSs1A1YoUCwTOEMo6T2jTGWQKTFqHhoGfGA5bTD2X4FIOGRh1Qdv8IoX58sZ2\nkvKa6d4/WnYILw+4Mt0qMoCGKgw+H+aXqRbmbdGr/jm2mfTPKn0PENkYJBKO\nrhaQXvpIHTDeyg18FnrZyfY8s+QUEU+vyD9xC7tp/0wtH8bRq28vgopWWh8X\nHuNvNGisb9fKk41EeSsaYwmD1lEsrndLl05K1nKG5mDFV9DkCmtSR+BUVZjR\nYBJLV+yuU6Ro6pQfWy6DM8SouWCLkp8cY4ylbKN4+NsGK/6rCMQJ0qkjiSlY\nKIU1Q+Xj0JUb/WCDLSFnbaafZlKhwJFeDcejZsA3YNWAr1b3gSiuOMFrbASz\ng3blx5j/17pBAcapEKXk6uMuKg3Mb0o/L1cn4uyYUBXQ5Bp6ipWbm+QK5dII\njWfIqRNWoy4txsV0XsJy8hcrP0HFgK+xMptVfhdsnehlYG650wFj7YCB7uJ8\np/Lq\r\n=ztbs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.47_1526342928387_0.7430125276076824", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.48", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "90e9d7cc3c8a835c122fafb5f05396ce5b87c090", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.48.tgz", "fileCount": 10, "integrity": "sha512-V1aVbyLT4dZGdDU45D5Bh/H8IVrVfY3xZYfHeEIPhR47Qs0gIn54xlJmaUVTLIYp5kx3qEwBPKI07BZbHd/P2g==", "signatures": [{"sig": "MEUCIAVN+jItujJqHvhPRen0XZ8PWzbLq77w1dN9RfIZGXFpAiEApQJNHCuPUv1WCbNPZBE3jn202awwCHw277lX/laztYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDoCRA9TVsSAnZWagAAupoQAJwAgZh/mbXy+iDFrVVh\nRG4bMOJOx0BdDfdhxMIjA7VM2cUQvYkWNsFaOjFz0jDhBSzkmCEFGrOwqVhq\ncFhwio+7796QmMNBrmOVzbA+jRml4qExktw3oRbq9IYBRkf2/VBnDKab6ctW\nGzcKY6aAkUSr20qVLRE61+rVCXxQU/sw9IBsHkq/aHxJ83KXpWFx+dPUpe6k\n9f4qvF0JX45ulWq4N7KT26DFzTZvwzkVyM1KvqowDdI1q8sp0i6x6ig6lf8L\nrsY51J5xQVRLLwYyneckZUHkeus2RLfyL6D4QhUrwtTBRy/ZSn0Vhrze0cJj\nEcoT3CAat9+YzpSEcHfoXlRyiAIeGKu3Yk2r0Wf7ECSxqLkrcpTN2IVNyxfa\nlrQmHxy0Uq8lbNpw09UHZXOijxw8ZcA1LDVB61a2zwtiaCGoBHqvyofRHt1O\nRuJsJYrWHV1ztSC3pf3r91b1jepTdB4+KDJmIMsse3p7Dwlg1e2qP+xjdJST\n6nbAuOuoBuq4PjFZTf3uZuFHdnI5hEgyru4nKUbdVdF8iNhrmY2UO9pvktTn\nSJBP2GridxwQJ1N2ZbkDTkvowYWzwoKcvuNYS4R1oKNfmt9gp7//DAwzgw83\n4Jv2G4gIfOpsWBseDdfjxbWnUEuVVhdfyddvr1/z3tZPwwWiUbb/Lzxg2Ldr\nUq/A\r\n=CmQ9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.48_1527189736165_0.49732441866331323", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.49", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "35ae2bc187bee752d0f7785d2704e52b87377369", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.49.tgz", "fileCount": 10, "integrity": "sha512-wDhLN/KcIHd25LLVcgfW3W/FXA75aUccgyrNne6C87VsVNGWP6U4505pJq5DVp/mJvgb4aOLk3+5A9kxDfWLRw==", "signatures": [{"sig": "MEQCIGCkwQ0YtOGbuJqn1QNC9bm/HINxaWNr+mlV6f9ur6WrAiAySCRdu/Ze/w5Xa9pLpO0kdTxnStdnMs6JZGgM6HczaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDN5CRA9TVsSAnZWagAAF4MP/jf/w97FypgHWBmPASxE\n4Nx8VFBoi5J8AyDQsfgeXQjsGHos1qNo8bO9ZrNdGjKSCxv/9s7J+qvXlGN1\nW3+BYOIEvVRMT8JVP759i5u9hyquY/A48JHIq6VF1Nuc7UJr7WPX4zh6MlwC\nHTBw//vbpQRRs1VKRpnHF/veX1HV1aQA+K+j/adiZRMbBZFryaRR61AvCogA\nFjfzTg9wiBQVfX8n09JG5DiktDUV0E9Y/VvHfk6ElhaKddV96P6UTepIc0mN\nPBklxrYS1epd69AYuJ8oRH+P7c0X11+avwaExcYwZEYTfBdvR4bCj95D+BHw\nj3y+L+G9xRYxtn30q6vJahyvJFit++pL5tJLJSJYVBiorR0K1+keDDfXkMA9\nQpqjoh0dcnNoDdgpf8ckf4b5JU0nWQxJeAzsVuO3WnAep/hpxFGbq0pDTqsz\n0Wvrl/NtNNfuBD5yANO9CpjUpTrh27iVTQViN3Lr6x9C01BjrBIcuxk5vA7Q\nRPb7KYiEDZxq4Q9UK3j9kVkIsPzq6sgtrgdN94w5SLv6YYFC6n9RzprzYe42\n9zCxy0QSKui/mI6Q5GHn/ENVqbQirlywpa6b0PtQf2koJCrAj2UhB8kawj/0\nDmeLI3ALjJsv7/jI3qjzHu6u19q1NpGVc2rnT9hv2Rm71C7muVR+zFuFJuLs\nomAf\r\n=iNyE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "35ae2bc187bee752d0f7785d2704e52b87377369", "engines": {"node": ">=4"}, "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.49_1527264120795_0.3452965176105458", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.50", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "70cd491101c3b0ddcd176692760b23fc656d4342", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.50.tgz", "fileCount": 18, "integrity": "sha512-fCVSfdP+XakSV2HW5/Ij/VBCbUviuF3S8ABQQ0eLvACEBEisa8eNAQoYlkBol6JzFqVxQF9RDH9zJjCP85b7aQ==", "signatures": [{"sig": "MEYCIQDeZlgzF0Oj+2URTU5WP5Gi1RbR3UAJ/7maEpmhWPoU0QIhANrMbeWRWp6chBWfZl2faurxphe+hms1E/MANI/k7Ogi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3835}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.50_1528832835563_0.6097391313835432", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.51", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "980558a1e5f7e28850f5ffde20404291e2aa33fb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.51.tgz", "fileCount": 18, "integrity": "sha512-UobLM3Q1DJ1lYH0EbtEMfFgb3DYtYogxwGv1GX8k555cx0nXwj+RXuIEJPMHL89ulhr6fSBGixoKPnUXPnm19A==", "signatures": [{"sig": "MEQCICEj3POuFQdNR7feDCsIBFMa8B7uXur6p/eUfolOaqnXAiAV15sG1Ua4SINxeleSnDdwbSFbDqC7VCK7A9GSnSTWSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3849}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.51_1528838385786_0.9555154104333667", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.52", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "caefead9870a06410ebc807d07b31b85fc46cd3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.52.tgz", "fileCount": 18, "integrity": "sha512-pWyuRzEmwvTmtCB4GhbqcgXOeNtizDv+N4Ms77QRo13k8WKPYCtK5804RWL98pGNyHTV3ou3GkKmv/9aOixl0w==", "signatures": [{"sig": "MEUCIQCaWOHgvWHKiz5jy6tsMIdqwPLn41s87dcnRgNaWpMt/gIgevuWl8Vfgb2oOGjYNGyLaisGwccEmBM7kS0vus4olrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.52_1530838764130_0.3525773845832789", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.53", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4c464731a45ff059b7e933ac76cc05cc70651a40", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.53.tgz", "fileCount": 18, "integrity": "sha512-0tKRsm0+QtBBzX4J0pk7T/QiSma4o6zkgYSSVKu8WmF9JREeeFX2YzumEpbf2g9V8+eBgLxsskr4NgiH+XsnVQ==", "signatures": [{"sig": "MEQCIFbS7LEe2p3M2LAJj7GI6oeF+KzZGfEsxc99jASp1e2CAiBQJfp6egcGIxE6oNviLuITlNxm7sQSvLDk6G8n2/A5Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.53_1531316414294_0.09386013311528241", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.54", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2835b7f4141b19fa0648eb96ffe3c4fccd1eca20", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.54.tgz", "fileCount": 18, "integrity": "sha512-gNVOwf6OVvBCHrdkRPZgk+0DfECowWoub9YpW54kvPMoxIynmlfYva5B1wTstVBEjt5181axV3BVVFLFc34dPg==", "signatures": [{"sig": "MEUCIET1x1Xo8KiOT4JS2g+Qw4TKX471o95LNguyBlAzT7dZAiEAnwjuBvxg4MvV3uLumKDYYmVuv/eEZ55Wdwnd2huTJt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.54_1531764004744_0.7751089137665792", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.55", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2b9c2d13b79b660789b40f9f49873525d7d77437", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.55.tgz", "fileCount": 18, "integrity": "sha512-FyqlgcF0AVlkXC5sowGWtRDQk1J25eVLPk6dtt+WJHMX/WJdlelbi3E82o5xebfbCiX3vU2uqOjMPpvltT40tQ==", "signatures": [{"sig": "MEYCIQD1ekTUF0BA5oRaijehSPhzRobufe+jzqpbhvBjl/cKfAIhAOr36WiwnZWb8RQufvblHdxChEmY959Ie4u0XGQ96YTH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.55_1532815634081_0.6245006922933409", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-beta.56", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f8586e81e4d5f6609f1b7be0dcbce77a2bd3bd24", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-beta.56.tgz", "fileCount": 18, "integrity": "sha512-nM7ZXzwdDwviGUleCdDrQ4fGXtTkEFg0HHbZ5LD7XlJrN4goJmi6xHBOoZ8iWdTPrzAuDi+FT87RWCHFDcU4xA==", "signatures": [{"sig": "MEUCIBHdjuh/K3Va8EXVr8beNnh9VBRFsUVpoZyfAIibMpW9AiEA1ZSqsw5Om1644J2Tl4l5F6OZiXdqRH07GOtxcyZXCPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvOCRA9TVsSAnZWagAAPAkP/02ZIqfjDuOBYNY7+SkV\nfIRNiERib8zQuCbWm/3Vz/88mq3GN6wjbYT5WDap3dVdkRojrghsnF/bgz0L\ny1tiHD24w+LowzaYSzAYgAMPUnSNECyGgLXBBX31A7OMaivVOxDSYswEcOBE\n+AKWG764L+2XlXeVv0xMoCEoVnxHZBNrhoo5zTl6SdGCUnKirYnV5Vetlyqa\nsIBKao6Iod7wxQnH7dQPk7ZM5nNqKdCClwUPcamU16sd8PKGFEvCclF+w1km\n4SbnliCg9vrw9oGP5rmnbwh0nEdPDV+4mTSFOD5mlkS7GmZyxwtbQRdQ9xbr\nZHhSJDZm6MEj4bUCtokwRbmgrKSij9oaqB2k2d8U3wCxpaW81aeyvSN/GYI1\nUuF676tuokJyYaJG8kiG4OlYx21PyWHTtRTqRDjkGLWoYMX7hJP4FKY73uuL\njmLk3uo5VpWXTgDTnNaE9VnSP+OkdpWEKvUOOpuPVdQfvlvr6XE+zjI8wKAn\npOXre2jVPgxh4lm1RJ6BjJztIfxJcLfEMhoyks/qPEJrIJNbopZ6YewhZbrw\nzf/ciLy7JyteP8J5HK/BIxSjVVjA0nIkrJGtVm/AjKFEzzNixgXxfFfSrFSM\nKD/d6dmjvob58MIyqE2430r+jVUWUxzEIv2mTd/4AMRFXOwxPhlzmkGJhBUC\nO52l\r\n=Pzq0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-beta.56_1533344717554_0.8614003073525747", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-rc.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e0124033e5801b84b24dbd656e7d692ae21dc5a5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-rc.0.tgz", "fileCount": 18, "integrity": "sha512-7yub8/f8DXU13lxa1kN+RqmIBE6oEvZY0PyIblM2CQy46sh/0m2nUgUVxdSB626ab2DxlpTTeuuSpMkgy1Zosw==", "signatures": [{"sig": "MEYCIQDv4z/Du+CH7hVhOnzMUMIurApq84ulEMlY9DLLRnquJwIhAJMkipgvhTe5KHrGGdtE/IMSRxc7KWVs53XNNfL3Wz6G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSTCRA9TVsSAnZWagAArIcQAIU4CUKcH2r5HMXh995J\nymi0LXh3rMvhN/qAROG4ZJ+fvJjkMSKdfUa9JidI6KMaBA9yHM6h5D/hBDGS\nyAMXSsGfGId8KmV4AGLnYsGRu8rdVdQA1bWsh70/CzzZgkd6WqhnJNWL98r2\nXPnH2HwdObye/HmfeZ7Aw+r734VtfAq86lhsvwDHeNO7FbaQNKQ3txIcydr/\n9tjPC5SkbYYM+XQ5G9q2pdGmhwxzJCcBGgI051Y5sBVYj9QmG6Z8MRCT8+4E\nLFAI9cus03TzTX+tQufXyFEXbnkPop2hFTmp9604PTSE4Kq0DDlW9q7yQ93X\nIhXyioGhyIyWCJxT1pQFxN9Gx+hreAvHoz+6ODfyM4r0aCDwzcwUC65+I0ok\nvrp28TgNnyqN515o63ybVSFdeyPd48YfKcmREVNo/PyiKQ9uRPtJxGAFRf0s\n9KMF3MrihbnJGhh4AgeRSBZH6hNpAUOSDMjFJwbtKdyalON+nL6Ji2VKLwT8\nVCTxg2Tu7eTMTf10rZgm/IpjFfIvRW8luymRPBR5P/4qh10DSOaVJd6cBWxV\nLC1+jGqjKuzyCzLMDLRKjbp8+HyZOKB2HwEiRUBiJDSLk+eHdYDlvZf+001x\nV/5Jkm8YcmCRzOC9BJSPHtVyuhvW/86haA2nSJzA9fHnbSI0ixcz8bvMiHvl\neos+\r\n=hAi4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-rc.0_1533830290553_0.3430563624797107", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-rc.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "3209d77c7905883482ff9d527c2f96d0db83df0a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-rc.1.tgz", "fileCount": 18, "integrity": "sha512-6G62wnwVWCjhvmWmWatXHO4wfvWhUL1bJX0MABYIf1bpD5ROFly/HxgWkuMVcTSeIuLzsfsYKSF1CMUI0bykXw==", "signatures": [{"sig": "MEUCIQCqbfiHIzTWRTL6wC2/T9BFee2oNy4qT4Giu1Nbn7N5EwIgdx9zLENqWYEK53LgF0S+5/cVCbIZQU3JEt/XRZ/FFxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8XCRA9TVsSAnZWagAAnBUQAJ0CwHvwT7KQsAVAkomI\nrzIdrxaiOD34zbsYg5ee+sBgyMBshUVOMX9/WigCAOVIJRwg2C8Bt9fET3i1\nXRZzndwRMa8VK0zEtCwCAkc3LLdKfwdQcBCWObGZ5dZXVN7BqBTEbv8+JY/2\n3hQQCysK+VhU5WHghbSQQCKQH6axAyDYZFzQKGddkrqN6et3/w9QSP2/j2O5\n2Pbwk6rkU6OwI7/QTygxqCnPePameihVA9Mr8l1OJNd0jHWMQZ9H1kaHrTpe\nxXOCP4SlKftl8tABc0i77C0aTKWT6h/47DSU6+FGFBzPnF5fN3+7cgr7iMVt\nNw5xBDe+b/R0DzewcnGmJoBevQoN1CO03PaRcRRjBKdeBvXfDxKccHH2FXOQ\n96k2INrgyMTdAipC8u8h9yoF9aY+kEDN+St9uaQEMP3s3MGRLV4gJ1CLrcV3\ntnaKDhtbwf9YlbjqUBVkXQOt3TgsdYTl6e0GdMIpBeio5QajoIMRS7++PFom\nHw4Yp+bs8ubnI2aR0zG5V4CwmX0DB3hgBMnme39QCsBUkrCRzmo1ttJ43PnU\nSsXuuSZtfxFQcX9heRfm8Bq2rJ16rOj9XvuKZDNteWAMewOCqd6PojFJ67Iw\ngl6oSbmKF5Gttj7Cuh31a3fpzf2r1sQI9L8a3r1zYzcyqx0RyvICWALSpU9z\nhUV3\r\n=50hT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-rc.1_1533845270807_0.8668493159414918", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-rc.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "012766ab7dcdf6afea5b3a1366f3e6fff368a37f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-rc.2.tgz", "fileCount": 18, "integrity": "sha512-BCeZNoleLez7Rg0+iLKK+dqCJDGufKUPISRZr8e6NvSjwwRvovQuNMtdj4Dxww9Xk8zz0e9eKyf2Dt/h9CyECQ==", "signatures": [{"sig": "MEYCIQD3l1txbvrNrQuo3GJQrMU443WjRJZftg/5Rno3B7oajQIhALzYlCLMRQF27QdtKVTBqZSuoyIcPMXwsLvYXWx6cADS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbECRA9TVsSAnZWagAA6qgP+wWC2SA5ikGpGtesglXs\nmgQPuJqSmdPGRJEwNbHHdYbJSIobCdtMZinykk59SxoUCU4Nm2npWFH6RvgC\nwiOYlljNXUORop2A5QPwADkAWwbBa0p0ZnxJMWLF/EOBO8qtnRYjvHmwbXRS\nsW20ZZkYSPgDIFiYqjVVFiaI1NVldidCxw/COpSLFVQIHUNC5CjkEkrl+Kob\n3LCQreUtJ94mvHgP++zCnam3/fwLLpTIiZn2WuxgRP/P+OV4GCrjl2PWDJGy\nj+8Fca0vCWCWqavZftsaiQANAx5xMlYK8GI/Ulz+eI/hZ6G4df0KHpmbDFT3\nfWj1bosvAmdnSsigNA1WSFIkBn9fwNO83ZAqlIePsv4F5mcGeepG9KuCbWb9\n4HMvAUxnmt/mbV7x7IkNJBA0/fySycEFxmCnrRvr5uVywRja7ehby834Ys76\n1yCoKRXqku6dAgzsohZm79nWkZHjvfbtcT0799nl3a2a92c4O1QGW4GVmEQD\n/Qlyelojq34HZ3o3TrvrWKGsNvIRw2AI9Y3xMXzSC192717wogzMKclggZpf\n6mYQVF3u61cE8S8aEnAxnKCWQJGsasmo/n7mLcrt4RvVRyhhDydVefzR4wV2\nflnfpNpUlXBDfncxunG2/gSNxx1XdJ6lYACHn5Zm+rxUSnpdctJc+BTcnPYq\ndQ4b\r\n=ffC8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-rc.2_1534879427973_0.7054077372697958", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-rc.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "7c7acf5cc4279831eabf3817daa85f7862e647cd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-rc.3.tgz", "fileCount": 19, "integrity": "sha512-AI8mGUPXYgJl2c5WfFtA6a9qw4OJJXx0anSk6Q2ZUqURCcXObQ8ID0D2afAEvji7uqpw+nnxN7LLrPYTc3O3WQ==", "signatures": [{"sig": "MEYCIQDk1Gvik+CficZ+L1Kz7c5WFJIKVbRP5zvtLJryEd902AIhAJ9EcynVux8kyO59NpIY+h1DP8mlf75uxbj0LzCwfhUi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEl5CRA9TVsSAnZWagAAa4oP/32fQmMY9jrxJrf08271\nqzvPDT0eryMD/CHkY2+50vWQhHSi47Xn0xSsqBN72CG4T9KD2M4gzR7SAdaP\nmFCZbAf7SNBJ3jmDl6fecpwu5k7/eaSraq6cqexrmh5dBGPnHwfGKqufaA9v\nXeMOK3/WgMvAihKFm2LL3gDKlw6APmunn66og/jWZ9bgOoBHdeFCpLu8GwnN\nqakjnVL6GqYikfw4tvUl0qQWB7aZzSPQuZvrdGLCIivrH3Zv5I+31EIa8jbE\nU/MibN9+SKqU2UyHfTLqRnVM4mRPAOcry/ioVmtlv+cC9ZS/OSi0AzzXF5Lm\n6S8Ui5xhwYR9e1ynCjR8gUunerVp/J83fe6lXJuXIZ9bYdn9y67fcUKb1RW+\nF14xJbIhYd/3yqxeJM1UqEUNF+F4BNahPOcx9Ok2uwdiaUDAkgEacGyRhLGW\nUFfx9RhwenUiSV+Or2PCNglWAtiyUHkqfhKDpWL2ZE/biyNi4pvNyVG4wgDt\nK2itN1DiW46Uoh+eF5l7HxB7mk7yQb7/tg/Q+SknfUbriKeZscWv2FXZAziQ\nTMjbtwASo5gKF00UTPrdFuWCZtRSJ1G81dOnue9S3mBGVaENFfPlb0fOm4Pa\nLSLp+LpamwDjMJfkXywBFkhFSsOJNgl+FmEiUtgMM2a6FhcfvrV/ySA+94wv\n4IOe\r\n=O1Yu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-rc.3_1535134072688_0.19853497077403026", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0-rc.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "072b0cd398f9ce93a3855801091e9a49a458627d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0-rc.4.tgz", "fileCount": 19, "integrity": "sha512-k/rzTZFxKHjfzDeQ8Vzqw66S21o4f04ZGkcG8meCb7/0De2he7tRNS90WmibLWzJytZvycO4prY0DYX65pGPhA==", "signatures": [{"sig": "MEUCIQDDh94P+Hk0sHL+Llhq7RsGSJ3CXuHTQCRtWAFJ/ic0SgIgcbPOKMr8Dal7qMyJzVrln23HSmVlzqQx3DOwZh1fhRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpVCRA9TVsSAnZWagAAqAMP/3Sr/PerouXcWQJc2Lp4\nv9F0DU5kEp7IZfILo5u/FIW8kug3Xhodrmnv29Y3VlLBDQE4MVnb5ACPrb89\nPPZPmTGpbYXCFDo0P4dZZmtZRGz7MBVR9G9DqhB2mWrbX2JNpgEpkM7b7P2n\nIzoj+O6rHwfO0dznu62n/lBhFGeeAP/fxPXmJ3IXsO1rsY1piV0OljuzKwu8\nGtiJcuw7oHcRTsvUXNJh3FDCmWhOLF7iujm/DFU4xaqRIL5PShesTiBfzpXf\nNT1xPx/nFXTP34uSr+lw53QkHqES97acdzJSqxctbWaZGiHnxrPQ24FCMK/N\n1qoDWMBLmKc6m54abPx5x3pcdk0pVvJdTcJPEiWkBdhTYH9JVkBYJfCR+art\nJO7Qh2zSmmjV5UhPZJOAE3SDojNQvLiOqLnMKufTT7QEGsV66T4c+JnMNK7k\n25/0jtXasvg0DIDcI6OsjxSvW68u9bjmLR5stokQkbkpjruRl0QCCxt/HsmT\ng6TGLReqqQ8PAEIqZrtb3AZhkHF0TCU3zX6x/jdK/3rP0RTP7bIWyrztO+6g\nag9vBGzsc0X1n0cnzptzWe3/dM9DhvSfBBAC7Hb2WN8GkZnxHXPjR30e0I1C\njA4iU6CTMS2XK5Sd3zdqzG+dVRy9l+4XPte9XX4Ox2uRTHRZ7ID2DpaPFvgB\nQn7j\r\n=g9yu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0-rc.4_1535388244262_0.19273128829291264", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.0.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "73a24da69bc3c370251f43a3d048198546115e58", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0.tgz", "fileCount": 19, "integrity": "sha512-00THs8eJxOJUFVx1w8i1MBF4XH4PsAjKjQ1eqN/uCH3YKwP21GCKfrn6YZFZswbOk9+0cw1zGQPHVc1KBlSxig==", "signatures": [{"sig": "MEUCIQDfps/gXxTEL7JeXrpLRopUthqvR4daxFuhhwyly99G7AIgCdU6jtqkz5Buenu0LhSHJMFzRMIHCBhykmz6EMatx+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBmCRA9TVsSAnZWagAAaNoP/RirIPcEmyU8+mJRD3D2\nq8J8FZG/r6YRqXi1sYM3zHFbVZj+ZT7h2LbpAb8ViEx2ftsXgehwCvWWDc2T\na5Xa+Q2uuflDe/3ts+DoNIriukbjF8QOEna9j+GWkuZk4RcNFjMG6NQNuMy8\nnpomEBMv4PoIgfJKbIG3XnObU14cXbJSzuoBc0nhJI4iU75xcrBXectullZ/\nzXNITK2q/ZndjB0h5GYGLvKo/GTJWU9vL18RRe7PAhi6aW7VWvgG+pE1E07m\nEGj9rY0N+CGed7IGCotlxZRprymMYeYiEzv7bblFXA/keqhTLmI8qkygnlhU\nFb5iBvAMbnJxemVAdgFVKjggJ4yDh1GeN1KipLe/eGG5BD2Oijs6Jmn5dzPf\n3PT0LbfMrTKZT+OwYByT3jwInKpr0T61zR4bWktioW3nqLAuE2if83PNwSv7\nGPQKyKHMtyjHbj7i0fjMsJvUZl8PGhoKMD45XL61usRnujXxKVtc9f4vuEhL\n5J30NR1T+RHILGJPIuZSbbQW+uOxYKSu98zbGfcLIT+wxDPiO+JU9v55WDNE\n9aXD8Nnmfxg9j3GK6oq0sZAFTmngVo0YDarCnZanN4pgX9YVW5nbB+PA6G6q\nSslQhHvy4shCEk6fLV3Gd/HP57fxD0MFYSMhki3iBEH+VT12ip9G+M+nELKe\ndzvm\r\n=2TF+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.0.0_1535406182334_0.4261867208511658", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.2.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f0aabb93d120a8ac61e925ea0ba440812dbe0e49", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.2.0.tgz", "fileCount": 19, "integrity": "sha512-sKxnyHfizweTgKZf7XsXu/CNupKhzijptfTM+bozonIuyVrLWVUvYjE2bhuSBML8VQeMxq4Mm63Q9qvcvUcciQ==", "signatures": [{"sig": "MEUCIH3fggxJtAPUg4qnjbH/bQhkqJmV0EI06eL8+60FHzlHAiEAo3DUKNNBkZhwEglc7q3+zr6RlBfL9swmGlYD8v6Sg6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1/CRA9TVsSAnZWagAA1aYP/3VT7s1COH/FxrvxVxD2\nyowgnTykUN0BR03dqm3ft5ziJs0GO8WnE6W/eakg+ix6eLXyuiyg0t4W2SYm\n+aAzxJXWhTL07qtEGbqi3di62A3ABg/DX+KJqelx1TVF2r+fQhCZJwuGfRPd\nM2viHbqO+jWR4ZWrC3xu0HQ3tXzy/M1tXok6bQ5qwidzZLwVBQe3Ai8zMNME\n/GlLKzRfBNzdoAsOk9dHQvkZ8n65MxT1aXjtzzfVlN1ZkPK2Kj1aEr91tx3q\n+6xKHnT753s9Kz6soDa4pl8WDH0B1WtgreGWWmZztyEFsjzBOIJLvFs+vi5Q\njAfFS7h8aFWdqYNdvU5+py24lMPiIm1Lb7gTtZ727C9/LYImSbhsJ3pYRluA\nO+krp0+a0BBT9AwiUxEVnBxHPFT7/8TWyHnzwr3XOQfv7NvT67J3acxPElvA\n/U1VpzZa3jZD30CbgcVBbKX5gW4rUteRRdSZgBRQ4XpmNOdbQpt/J/3fdruf\nbVzcWboX5uD0N9LTdnMzvijvpc2D8pKRbpkY12WcfJGTRz0sK/zxSij3R7//\nYmDUBJIBzOm4xbKM+pjjbbRbGC+9mUaXqVyaJCUiLV5wav+SSRprMX6phH6V\n2i1i5fB1wKcUB6p0cF1AQ3IkjNLmMS9RXyJzgadpzM0nBgk3H7+DuGWsb9L0\n4afy\r\n=DZxj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex.git", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.1.3", "@babel/helper-regex": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.2.0_1543863678309_0.10662234801419035", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.4.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "fceff1c16d00c53d32d980448606f812cd6d02bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.4.3.tgz", "fileCount": 11, "integrity": "sha512-9Arc2I0AGynzXRR/oPdSALv3k0rM38IMFyto7kOCwb5F9sLUt2Ykdo3V9yUPR+Bgr4kb6bVEyLkPEiBhzcTeoA==", "signatures": [{"sig": "MEYCIQCUr1j765rVCwRPK66lJrVs/cHRb+y/DpnDqtF3gFeJmQIhAK8M5kajshVh80nHifL1OHp5TO2J5Jtnf4kTdVyZXtnv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75gCRA9TVsSAnZWagAAm+0P/RcYbzsCnFhO22w+SdGB\nH9OC58bY4QBjfHAuT9e5UD8XA3r8Zg63og9jQ7E5ugiVbHiMkzsZfJ9uL7G+\ng+FuPJhj/iEOZqDAt7816Mh5uD7ANkSdP+lM1qBixV28NO7EgtmvCzF3rfst\nXjMsbWZZd9wSQVTzrGmhiYjJG0DLSA5SMc4rZhHFfehWz2M1PkztYrnK9Jsu\nFgYMX9HP1Na2zGFnz/Q7GWhKKQyR6xAT6SnFUyFKDS9IrcgRHdoIHXlA4qdc\nvLEaNdQeoyidJenukNqILDRbId9uNEGQ8TGmywFVa9LlntRNvJLomPSi5Afu\ntwU079kSUOT6CHlODb/DiSW3uF2dggX0P9gTXYk0Xaf5VwgADdpEkZn3yNl8\nxAXIyEqGPaUbPSrsI+Y4FwmuYzwR3mmzY0dqQZ7FDv8JOmghvIceUV0byGSH\nyNMDDk282SKz/oY2hIwk5DZHDubvKacvjIt5Y6fkbMMGqpw9o01Paayby8cV\nrsGOUcmIq+LzhPnV2h7GWeHunsYlRxlgHhOOhxvmQWmPlwPAoLFpgPGc906+\ndbTGpdPA5xAqt0Ktn1pxYFg6otdYPN4losv3C0jLkD96Dh9JGyyZSG5BIHsR\nowqhvghM9yocQaPzAhy79wFrMDjdBs/jctiwkSppW75OJeT6u/xxFgFKVBFU\ndH5s\r\n=xm+V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.5.4", "@babel/helper-regex": "^7.4.3", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.4.3_1554234976295_0.3184763836101301", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.4.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "361a148bc951444312c69446d76ed1ea8e4450c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.4.4.tgz", "fileCount": 11, "integrity": "sha512-P05YEhRc2h53lZDjRPk/OektxCVevFzZs2Gfjd545Wde3k+yFDbXORgl2e0xpbq8mLcKJ7Idss4fAg0zORN/zg==", "signatures": [{"sig": "MEYCIQD4m4rFAo5AXI/lOADABpisZsAe6ShpwJJtuylCI73w0AIhAJJLj/4yKevmL8RMAvB/ay3ZfHx2JjHTr1INdDWVjLkS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JlCRA9TVsSAnZWagAAAp4P/0oNh4dmRriATDxAIVRX\nCrIl5B8YcJwNIgXGAoFNUbMJrtzhpcy7LNO4DjNXeyNs3wfGbCN8aFhrHtdX\nIB7B8lL2DZXafYuHHJznRA9D8c5N+gXYyV/ER0/EczU+5a9suIIod0NjBXmq\nr5eKTj/MBwSf24n9ZrUT/UdqFRNZrLyqExhB1p6OmKoTDZDjWn+rmJRe98Q+\nqJsalXEN/3WQ32qA7a1FmaB3Gul+LKpSEJDNMLStAzcPHdTxFQhlQAaZk51E\nJQn9Bc6uj8wSNOfI/A8TZ4Pd1amRU+JOKGeIQLW54q8eQkUyn0CPbC6LAIU0\nPksRbpadtRUFm+Us5Nd5kl4Rvg8L8wtJ6YFVCA99yVzBDn9qiHMtI0dj5hpD\nuygMry/MFA4VXL/uhBHGhGrqDNBpDkT/vSx3GrvUbg5QFUm8JvSr0hZBKrfN\nnvLng8zrbX0cFQOHBwlzCPnI7UPR7Re00+sxqKrNUmDMK2LQCQAuGXXTkp1S\nXe9tpUNMZmCkJQe8/fdq9yK1IcTaZqqL+Inz9Lhao7QB8MMbQbKDQjH3KrCV\nHGqEznqj/8UoZA8a0xuW4aG5ecFpnEqtFGb9h+fLQx//k22EKr3Dsl1hjPLn\n/T9X0aryfgwpmysG1zeKeEchS/BAjkrlHZxe2g/g8z8d8OJba5C+y1O4qmxR\n6Ufs\r\n=nbYq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexpu-core": "^4.5.4", "@babel/helper-regex": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.4.4_1556312677372_0.6153472820258472", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.6.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.6.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "44abb948b88f0199a627024e1508acaf8dc9b2f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.6.2.tgz", "fileCount": 11, "integrity": "sha512-KGKT9aqKV+9YMZSkowzYoYEiHqgaDhGmPNZlZxX6UeHC4z30nC1J9IrZuGqbYFB1jaIGdv91ujpze0exiVK8bA==", "signatures": [{"sig": "MEUCIDzZmelGjgL+3WSukf+kB+YOPuq8mpI96trI+fDIXWJFAiEA4197ZE5/W5NA/SyqA+2eOlVhwPbzw3ndCCSofsIUuMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdfCRA9TVsSAnZWagAAKkEP/1rmikrHbDxsk1O+jG7q\ngzNQYXv/Xyech0+VKRanF8TlMX99Q8BVCVXLA+rRJVDM1JSKPT26Hn6r8is5\nvX1lZtqlgMoad2pFrkiL3yjffNjxYJgA1hKWgHe88tqc6jmCN9m9+mqmXmkR\nu7vwapRyAs/tZ8AMDUYEM+dZ6vZ/D1Fj7Ia46sbh6kg7J0ezICJOOzOXqVHR\nHi4rnRRBKRKxZ/9kVtOoBnxNFpbrbAuSOylvHlBI3sRLmugsyExnnWz3n5wK\nXSzAzZomJUf5SX067ahf4VBuuCEAhLmm4x1HtaLhcqcnSAcwnADlvhK3uMn/\nuQYcQUV8y9w1CvJSL64E4GnZDGnK5hBHqeMxHQHzq+UeoKkzugFSkDSfXZQ/\nFZilh3G+ImsC4QPdbDkF0cAmradu5DLBNdF7tZyJK8cwhZqT1qbbA8pI27lq\nk9sn/FpOuJp1Zly8O829omvGlhl7Y5hWUPBnPPT5RbSTkpb6ABTD5TFHSzZh\nPlF2IUm4NKi4RbrC0yrD90/f94ngFhxo8/t3nQNLq6MKCs4YLtYKd7ooQBZV\nEDPMMjNW2bdbkO5rUFC0gdCt7FnPulLuntFludx7dBvQaGUwx7CWKz7FCu8D\nvS2v/uGFTpXpCwy9AX2OvjCfpEH3ku14GFrcp7NI0igfcLODiN1YPE0GRqzR\niTQ0\r\n=x+Yv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "gitHead": "b9cb4af953afb1a5aeed9b18526192ab15bb45c1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.6.2_1569273694512_0.46372184576285846", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.7.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c5c9ecacab3a5e0c11db6981610f0c32fd698b3b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.7.0.tgz", "fileCount": 14, "integrity": "sha512-3QQlF7hSBnSuM1hQ0pS3pmAbWLax/uGNCbPBND9y+oJ4Y776jsyujG2k0Sn2Aj2a0QwVOiOFL5QVPA7spjvzSA==", "signatures": [{"sig": "MEQCICaYByYrZgetFH+lKM40HeuvCRDedbAcGUB6CoQ+LVdiAiAlZz0zRd8hpZ+3dgwYcCy4cbOupsp5VbSw8yYJziB7Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVShCRA9TVsSAnZWagAAY9MP+QCO2DQCDGDcxLWPRs+P\nzspfpcRLrRy4gyCBdPCUuBjA2XOFiQvXNN4TYzHWSG1uf3ysNPClU7a0qNe2\nPGHHl/cCadTkiI/0l1Vb5u553M3ch8ahwOc3Gepf+AysZGjm8A3pXlHcBHtx\nVQ4hqc53dP2HH0yqS5AZm1HrPZc0l13qNXM+bsGFtj6oyvLWlkHNjCFdjJws\nEJBD9CJ+BmeaTL4UNcUhI5VWZUngvOIFtTUfkkNl2ZuIjekEWAZUaoVMyxnQ\nhUvvoc6+fa9KX8W1Q6KmYYn2XvCrb0erI7bNaDqk9HeuCVjDIHKhW8lEKUUZ\nILnmt+HVnVPXkXIuIdR+8K1NoaIFyPh+fOgmQK/YZmSBqH0RMIdEvhM44u9z\nt+jJu/gXTGQeynbL184YZU1wXoIXs89K8kfiCZCAaaXOHKh903P25Of2Ro+S\nZjK3MqHpsXl2BqVyLMdI1ZR7GNkBQAiplBvNt6tlINwV8tDtlm9o4oUHfHTx\nldrQ2BxaJeRbz9z4gHldSgmz743YPe1k0fFVAsBOOvpXO9tyRKB0TfkbxF1d\n/bjO5ZE6xpDeWEZPqfkMyHWkKc91Q21THbLO1JyvYSRE8kOf0AJ/3NoC+4F3\npTrmYYlPWdpjm9gkTZZ+oOML2SyFbF3JLx3xi5RbJhkg/YJfnWxjEUpW+qfC\n2Vpr\r\n=xRsG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-create-regexp-features-plugin": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.7.0_1572951201567_0.17423415381504137", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.7.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f7ccda61118c5b7a2599a72d5e3210884a021e96", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.7.4.tgz", "fileCount": 14, "integrity": "sha512-mk0cH1zyMa/XHeb6LOTXTbG7uIJ8Rrjlzu91pUx/KS3JpcgaTDwMS8kM+ar8SLOvlL2Lofi4CGBAjCo3a2x+lw==", "signatures": [{"sig": "MEYCIQC1dYJA+U+aMBr5W7IZ+xCDiZus2x7U6rlOGkm575VqxQIhAIZ4xYUQbotCSyyHAkJiLULZN9ES4br+1a66Rrggme9q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HASCRA9TVsSAnZWagAAYk8P/jfnTaR2J+kHd2KmYG8n\ncRTsY5lqf3uhLEx/Eik1nsvg2OFg8qRPxGuFbUVjmdTp6Q5t/RN7uwJ4iku5\ncEXrX4E0TWaMCnX4znoevyWjJj7lMkp9CblpIapOwyzTeZBcPVSJKDvkc4TP\ng1DTTVVJF7aYWulLsoY9IkcxsEMH2gJieZq4pYoUttSsU+4evHrtQ8IE+Q/Z\nMCe95veR5vbydU/+IzgH+zQjsJGkceHbfdsNjrgXdSYQy0wWbDIzftXQBLU2\nE4QrHHKn1m7AIhSjZcKFgvKVgDn9YFlQ3ongpz5H8isCeemOJ+yAVpqwuJDa\nmvhQabgdzqp6YFCixdTqOdyjOJnGRxFTrZlthtEnf/bdSniBFmh38zNW6a5h\nbsZkdxBTw0xZqWd75eCEHIcrR67onMM8DEXfYuGCtdkNh2cTHL1ilGhRYbJa\nhZ3vI7GJKTMB8esLmtwtVGqKoXmMqrtMWryDrC+koyeJiuhYxTLFhG78qAMh\n3Xyz8Y9qnS+8ZCni+qEpN/4a9nyU1aewn6AB1ZrwMr3MEI9OlVIHQdduRcg7\nBZniJbN9iqLndK3NP3UVfortOcRuq+/OqA/P1rk2jWXoCXQojSQwTURMw8o4\nZ7QAAUgppbYfR9u6UJPDB7catAzktw66s9mY6soFEPy/LmF5X3Qb/U8ZgVGM\nCy9H\r\n=dzkP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-create-regexp-features-plugin": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.7.4_1574465554340_0.6062280342619011", "host": "s3://npm-registry-packages"}}, "7.7.7": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.7.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.7.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "3e9713f1b69f339e87fa796b097d73ded16b937b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.7.7.tgz", "fileCount": 4, "integrity": "sha512-b4in+YlTeE/QmTgrllnb3bHA0HntYvjz8O3Mcbx75UBPJA2xhb5A8nle498VhxSXJHQefjtQxpnLPehDJ4TRlg==", "signatures": [{"sig": "MEYCIQC1Th0cOJzZBSMZeU1bUsU3lD2FL8sm08aAkzrALT0sBwIhANi66lMqNUj4AoZ++muzNCEO/nsRyEDMu/X8go40kXb3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+snyCRA9TVsSAnZWagAAtw0P/iq45YaPgB/eCc1Xw4pi\nwnqlQOuy2rga+sjzdWLdqar2QzSXrG88228YWDaLCq7OF/yY2izNN2VIvbaO\nFHNdh/sZRwUbKN+e70MYgrJCjHvgM2CLlik5dCN8TML7fLERtRD+aoL8BnBO\nXDsXU3Ml6fum7opQ6fgSjHRw6vCk+kTMgdzavnogStpatztW0suMDuakZ/OY\nxJSK4RfYh2ljdBUhj3wVbjKAE1gCBV2YifDBzsiQHfUCvSCqqqL+/ChgKvsZ\nc2rv+LY3aj97HszQwvti2RgRHjcr3uZZLAXWxWbsXneIiVE8429RUWXNFw+L\nGu5OxCPz1dP3CuSw4vJyzZ4FaOxGCwafFkqQJd6S3SZYzSdy+YTUs3HWwL1q\nrdpNxOsu1nYd/tEyqD49GnglIZCrLzVDQTtKAMKGI5Y6Oftv88JJFRxIPmt6\nNA5KfSLp535N4wPlTGXXsNm2aRevvZALVLbUAjAq6nGSFlro7qZ9Phmnk7A7\nhqWa0Jk2uNyyX8+KwCijuE50uJXlqa+TsgNMAcq5OotdS7hz0DNaRDTlkDvV\nByppEFsRz8xokUnS9FjoAN2i6wma06No1NgYSyKJwMGJY5AcNPVXqsndQT8v\nKVZHJoCI89QdZ+IAT9WDBT+jsNAd1FnB5YsC6T8eE6uc5Lj32TGIztDMgc+P\nFocd\r\n=vlPS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "12da0941c898987ae30045a9da90ed5bf58ecaf9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.4.0+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-create-regexp-features-plugin": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.7", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.7.7_1576716785858_0.6977329693420777", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.8.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "7e9e439e85219be091c5dbf1be138320600d1172", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-pq/XLkDB4MPvTe9ktHJInfWksalXogrIGRZJUG7RiDXhEfdNrlducoMPbACZQuCFtelVgVpD0VyreiY0l38G7g==", "signatures": [{"sig": "MEUCIBKYUbXcbyTK5uJpSdEN4f4FT6mjL3lTQf/THNgp5pJHAiEA5NYb+mr3nIcHKrrWPu7aqhZCL8f93IbW6CvA1DMwnu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVzCRA9TVsSAnZWagAAsCwP/1aoArzYwXUuU1R2Cxw4\niQOnBDc/Gqe0jT584N7LBpVNaA06ebNb6xsKHKEQQBCqpnyQ1y0acrnY/IUy\neLKPa0aP/p/sv5XgMMn/ZAyspu/X/RRMScqlm6PCmhIw2bdOwPkaHA45EleK\ncWCuXgargC1uf9ZEFRQtwBy3XufjI/jp6UouX7PI5paBDEGWR+xVsKbjZuXk\n9JvLj6mSNM36HehZTYGtrqeyh/5iiFZ3HvC7RZGgk1U4vC7o3dX0GGmy/2uX\nrg1AtpAVBsCWngvQJjYKFefxn3mjR6c+t/QT6S9f/b//CDCPf6Y/gi1BnuzO\nvUyRJ7zCWe6rULnHYz7ovAGIxJjgWskUwT4xEi/GIoXhAfke3KB6LqTL64bC\namXeg9DzqvLRwZASl1CX3l9PcOjjvwlh+lyunYGw4RI2LY3AWjw96t+eetpd\nyNo5i4qwTMWPZfxyU6V7QQa4Mp2KtXScsFldMeSJUvaghi7z7MVvdHf5G8mP\nlWrdhC7xyXfXF/Uu6y10SXmKC5ehGVnfLuwIetC8OBgoOTFCeOnI8v+JJAEo\nKA7pXjamxAfJNTgmRhnnO9JRBsQZL7E2bx5JTM0Uc4mPHSYQarnxHSTeJrAP\ncnCN2nsMnl9MHGcOWbS0+bPVsY17yzFtfezIf7c9HR3/IFh7+Y22PtwBpCAb\nPgiI\r\n=8rE3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-create-regexp-features-plugin": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.8.0_1578788211084_0.42533868970647304", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.8.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c3c6ec5ee6125c6993c5cbca20dc8621a9ea7a6e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-kLs1j9Nn4MQoBYdRXH6AeaXMbEJFaFu/v1nQkvib6QzTj8MZI5OQzqmD83/2jEM1z0DLilra5aWO5YpyC0ALIw==", "signatures": [{"sig": "MEYCIQD1/QNjUKOR29aDqxosf/LcjTFxOamcXQMneH6mGdRrsgIhAKvaN/icVCiXWvrdFW5oL++QAbDhA6Dp8EnGV63BIgEr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQxCRA9TVsSAnZWagAAiQUP/0bvREfUf26XrwEG0GJs\nvbr4ZIbJFbNREkUibz+JR+6Rmc4hCFcZxfKpHlflAmfN3KH0yPi00DOeZ8Cu\nz8DrzFunpYX4+AmNVL0x/yORD+4Hop1uxfZtr3i6K8sfTJiSaLv60PIvvXOp\n3+YRxBPk7MLnGo6XYbvIClZEMfnTC4bANIzApIHL1EJ7vsU0QpgKfeXC+SCP\neRXjmXRC/79fcgCUD1stNfsJiujkBJr0Us3sN4v+aQvsBJP2reHi1lKMiuNX\n8ChqnE9rpxjRITpTLsPq4ADRSXElTOLM3hCSwatDxITbV4lrZzK3zRsRGwxd\nGSeo8LInfzyw5n+IhlyO82Tz/oRblgpW8K9QkWeVa9HTFoLrdCOP+mRFZGUe\nW/DFBeUT71mZBfbJBVgCUG+3cn7ZAVZAUdoez4HvwJK18tgthTHLLpsSuekB\n4rXyG/kirzeuBfrau0KSgY7GGLOYtNw5bY0LiNex0a3fKec6AK47lKDNSx4W\nTO8vcfOPskH4MeBOiIZF+J3sldnh0BihPT/JmvJ5uVhTjXWdWDp3MH2byUuu\nsDYHBdzuc754xIMNz7j76xdSdn5+3+VZn0sIQlzUXUFqj6U17PrzAP4W/guD\neKvYGpqH1et6jPxa8G3DObGx+Ug3rNq2QQ0MgTFDDibPMiIO7CeFtAebNoJv\n5UYi\r\n=FaIk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-dotall-regex", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-create-regexp-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.8.3_1578951729382_0.18605111292073184", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.10.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "920b9fec2d78bb57ebb64a644d5c2ba67cc104ee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-19VIMsD1dp02RvduFUmfzj8uknaO3uiHHF0s3E1OHnVsNj8oge8EQ5RzHRbJjGSetRnkEuBYO7TG1M5kKjGLOA==", "signatures": [{"sig": "MEYCIQD1h3vUD7aXmgRU5hBbD+RCWk2k6AzPUVgWp5v688UlLAIhAMFaaT3CmfEK9ezFQsYZMv6QNwvzRk5s1dQv1b9OTeiI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTKCRA9TVsSAnZWagAAzOgQAIipU+8UYzJJnNf73hcP\nD2IxBccQ11BQwtAO4unWmmp4mdqICTCLGfYMoAGXLn11w1/uBDlOonQ9GGsG\ncquR5GFP5t8Uq5tax3f7whkdoGHRTRRu4oHyWTwz/UJYWu7ZMn23FuGo08pg\nk1Siwqouz6A207ajlTn4H0eALRGoy+YqKToPDgh1g0MuP5vBIFzmsYt59Q7O\noPDSORt1rOjJdLGtpvsWAlzCgtF+b2zS3YKPKtnF5QTFpwfsgpvSePbLQaSQ\nVrhl1MYDSv5u9Km2w4f+gEVE/FxRzPW6r73zBA6C7/riWsz6b4DlrJohdCAO\n1ihWK1GiWlYBlt+sweKoibidJR2tLVcdQyoaJBasa9TlAIjFnLes6AKqyFmt\nEsLozNY2R/ots/JV5iDFWoMQmgVV5QVU/CdchDo6Ugxkh/UuEfMiPjdb/zjF\ndcQb1ljO2hwJipjywta4oTA37mVM2IatClgnEIw61NEpjJ14Njf4890Mc1dC\nUl0ZFnfoIxg6JnFDN5vuaXuckmGm2SAbOoPZuy9lkZmGElCEkhQkHhIuzwt9\nkCNjlJfg4J5Sb4BUdv6JRFccip8Xl+c0lR3IaHXxdRoPcUqJXaN3lA0mSQVR\nyqzXscW2O0FJHQ7lHcE3PEKShBBHnyE5sTANnya7P//dvs2ZhldK05tQhK6L\nrk6e\r\n=EaB9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-create-regexp-features-plugin": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.10.1_1590617289590_0.33390166633759044", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.10.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "469c2062105c1eb6a040eaf4fac4b488078395ee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-ZEAVvUTCMlMFAbASYSVQoxIbHm2OkG2MseW6bV2JjIygOjdVv8tuxrCTzj1+Rynh7ODb8GivUy7dzEXzEhuPaA==", "signatures": [{"sig": "MEUCIQCUz3wyjCabzLDDcadH/HfSpqZZmy/IshtSrSmNB5dLEgIgaMH1iIY5HyU4GTdUi4oqawmuVG/eKVPoW5jTE6FeHQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpNCRA9TVsSAnZWagAAl9AP/AlXLaxjoHJpupI88mdE\nOY3MSPM1B2kertmalRCiSEz7FTGXZpA0286DjkDEzyj5vag8IHBF/YuEWP0E\nJo8wwXkrTsbeVN9FkR24fLklWRNvS1/EdtQz9dKj4y5CY765pX5hVwifdrE1\nNTGzGt4w4owrQd2E2FcmFR7WGHlwKb0rXIgeq6oY/nF3Fmy+aou1uYzqNMe9\nal076m02ZZtog+DNo11koaEfEVkFPXv6szvTAScsOEmezy/c5CMRBOM32QEe\nds749dPVSPqgFsdz0aadUi46+Ca/9azpTNx74CuaP+NisqUkWXWwTWjUsRfD\n+W7b4jZVFMbkcaqyS1K0HWyfmE1XoVEOUp6rYJs53DXUW3vIKEPPt4H9Ba3T\n7u6PxIFpXaiDiizPV9+m5dtqocZ0ogwusFsPPs+pHjRCnaeOL8DJNAzDVFnF\nLOpA2f9svOsml7tyGRDdRmg1BtIdkfDTTa01pYpLcHfAus5OlH37dWJ/aqL5\n476LJL0Hq+egG3x+rHFmmXOdBsuDj6Is/+KitHInFYVL8QXHRIoqCS1pmAE/\nrpdnFfyn11DaIXyqxaHi2WaNrzF3dYIBJknzI1PtrZS9yVeifutmlA7Kfi0+\nl8pgi6GLF+pB/pD2NYqQiLQBUs56vgiKbP0DHEOrE/pFiN1SJouE5LkkkmyK\nRnQd\r\n=kt2M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-create-regexp-features-plugin": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.10.4_1593522764760_0.347020409703799", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.12.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a1d16c14862817b6409c0a678d6f9373ca9cd975", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-B2pXeRKoLszfEW7J4Hg9LoFaWEbr/kzo3teWHmtFCszjRNa/b40f9mfeqZsIDLLt/FjwQ6pz/Gdlwy85xNckBA==", "signatures": [{"sig": "MEUCIF4Axp/3GyrtxJeVhEYCJ0fMNJqYNhekRCHiv3xOqN6BAiEAlfdbUMtUBAxF6oO2m82CCG/yX19IBzrv6Fc8210zTjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAKCRA9TVsSAnZWagAAuvEP/2l3h1qKtKhIJyhgPU3R\nPBYJlNuv06SYH4O+C3U0T9EqBQKertcKga7dhudOR7dodu+R53Xj1L7bhwg1\nIy2+2xIzwT5/CAv/Mp7W2yALwYobAe7a2xQeXflyKtPQtjg2YNB6lukj5ijL\ndS7qS7ko6G4xS6QEvIZT0sVqnpHzD/tCvh+Hj0MkLZXk4FfBzw8cPGYp5N75\n0q12SL9875/Ku9k1jB7F4+8bcWVfc4q8HuFrMSiCxrntC/WOX5OKVMmJs7za\nkdmQd+SC9WrfDbPYJ9pzab6Q7BoKWLA7D9ayzdOpyHUXFpZj1POYRtLTcxWo\nlKiMOqH5XT7ZiK5aHMHiNceToKR5NM2pzRu4nCqL9wSf3iaLU4r4wuYIMW6i\nuEkbLgKsQ2fktL48l2ECEC71g3af6xLJ0qBn9kZedXCqqHy1ueHibn7I3c9d\ny4YIiwzKfdsipMYADRhy0VYHb6mQwYQPZ+41kuXoeJOjYcP3h3kUee6TNWYF\nsY4ProSDNwsjMtfw7lNF2esnLlx7zzExmn/7WKEXZlEmdcxmWsd4Gzcvts0v\nFOmtV381ccfgSEvc2xyS0BJ9wJSV9nw3Ssfkegoca1+8OwpTE6IUh8USUkHy\n4OGAFbibDI0meIVibTmXlz2kJ0JjBSVnXHWmUW0jH0HYFedAek5JSMJhBqfr\na6aE\r\n=Mu18\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-create-regexp-features-plugin": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.12.1_1602801674550_0.040512535758661095", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.12.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "3f1601cc29905bfcb67f53910f197aeafebb25ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-foDrozE65ZFdUC2OfgeOCrEPTxdB3yjqxpXh8CH+ipd9CHd4s/iq81kcUpyH8ACGNEPdFqbtzfgzbT/ZGlbDeQ==", "signatures": [{"sig": "MEUCIDEpOXIZ6/KbR1ac8+m0fiLn2bb8Bd8d2IfndGrAkHQPAiEAlv1+YK7a0TI+9G8C6IQ0B4UpHupiCJTsOZki0+V6+6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhHCRA9TVsSAnZWagAAV18P/Rvbm/xz0Eh9Zn02n2fp\nf/cUe6a7GpbP7IXpCotYEl9Vgoqy2m2zDhN3d0UEI6xBbekp5H11BQ0breNC\nKIMSQjdhs1ken/xWyIdT87pTVlMe5YsXv605LlLYUXVf3uGeDIZ4weXkxwuh\nQtgUhh5nD7TwQsK//Of0kz+oIQPpG1SLdgD8Vq+d52EkexFWr/m/UvvS+Cwd\ndUUw6bdWzoADT8pMqOH3VwweiuhyinWeWnjEHhF254+g0LOvqMy1MAHf1JoX\n/bqOST8taVb5BCZDuFErxyr+SjSuvI6gp4dKZomY2if2Ifb7ZH0DiHhpHR9e\nR0WV5V0ujy3cStygBsqeswr9fFZ2P6wfZWsoIu8z/B9e+efZZ+4QzHzjvaIj\nQW8sg54Z42RFXeY2SEeba5/tKufJmrNSWfw1+4ICCD9A1X60yRLfpNTUo7Wt\nu2qgEsaQwrx8caDg88qCdMj4bM7vy/qXD2W9NR1ucqN8fE4w5XMafNbwAhCL\nLA5QwcsUjeGBIHUYSEA5ouWA1KTMyDbwZNnwqm1V0pudaV5xtG/6zFqnb78L\nM8OSc6qObLpLIR2luohHrMaMuxFOxa/LPMipMuOCSwuXwgm8+bUIaRSUdev+\njv9A4X85uoeehjyndxfzgV0ANx9nTlnY9mo6zCimjDBBN/2J4J5oKvoz36dW\n/ZOL\r\n=pLIJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-create-regexp-features-plugin": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.12.13_1612314694694_0.2103800118889525", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.14.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "2f6bf76e46bdf8043b4e7e16cf24532629ba0c7a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-loGlnBdj02MDsFaHhAIJzh7euK89lBrGIdM9EAtHFo6xKygCUGuuWe07o1oZVk287amtW1n0808sQM99aZt3gw==", "signatures": [{"sig": "MEUCIGnlKYmiGwKhz9MqWD+Z8W4PqGKIp+g6ma8eYmLtpECHAiEAsV9Uw1y7rcoXb3cQk1guD0i69yTtdTEQhQFd5VZ+2iE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr0CRA9TVsSAnZWagAAbcwP/0JgSOpbzaGcE7AA31fZ\n6gntom1cTb+pi75zUYSMbi+TnuT+7FzupestS3LVwvxiM0ghyG5/mRSxKrM4\nt/EQHl2pkvRfsR+3++EYRZ9Ndyf9N2n679RUmmNw4LE8NTud/TqRuvkS1nSJ\nhSkJjq6U8WHBsMYMMXTZeOZE2yxuj5cGs3Dn/HX3WJShbvMF0u1m7toozBXB\n6+24r5HhW7XWTZ4VRTsan5OxlrCkjC2YgVyVrPPIJ9CY8AXQbYymAfUS+Zdn\nSwx8Upwp8mwnE3tBL7FwosKQBqCI3XYfGe1M3zJQRN4Sspz4OZ45NKPW+ZCc\nFM1aNa9LcezI85ogSVxLN0Vmxn5B2g27ZHVhfDT7dzQxCUFLwhrQEfktXLXY\nDZ05ezcn2QDjAIqJXtRxVyagYnMHy7NI3j+fjHzI7zlgRNn7WYASt607d7bO\nhLd+/iLPtCqGskq2EPAbrfDfzDiln9JqvtrsUzNeU0uelLrIrtzBe/Y0pmcH\nrUJ7L3dtpGpoDaArvNn/CKQENgXbmiVZnW2OmcoUqVfjEcLWyoWyKOssa2W9\nuQygioUn0IqKV60++0TueLe69Y3ekW95k5d0bam3ANggeF3Gk8BEJyzcXvoY\nx6QIvaN9tqhwt2RbNEgiWUWoY/0153azglUUkFx3+DeI0N49Iof893vn5hQ/\nzyeV\r\n=Th9V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-create-regexp-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.14.5_1623280372493_0.6709522445072962", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.16.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "50bab00c1084b6162d0a58a818031cf57798e06f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-FXlDZfQeLILfJlC6I1qyEwcHK5UpRCFkaoVyA1nk9A1L1Yu583YO4un2KsLBsu3IJb4CUbctZks8tD9xPQubLw==", "signatures": [{"sig": "MEUCIA2eypJ+FlcJYNm5Oe4iNOkVQ06xRUBmxuihH4J8sKO4AiEAwFZmkCp76hYxjqYCJJJH9iGgmsFodGbisAO1+JCSoRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3106}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-create-regexp-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.16.0_1635551269902_0.36215424911884253", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.16.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "b40739c00b6686820653536d6d143e311de67936", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-iQiEMt8Q4/5aRGHpGVK2Zc7a6mx7qEAO7qehgSug3SDImnuMzgmm/wtJALXaz25zUj1PmnNHtShjFgk4PDx4nw==", "signatures": [{"sig": "MEQCIEwXVJlG99Wg48j5vFfnDPS6guW0coaRZ4PH9waykiqFAiB7xQAQK++kUXgr5g6iDw54RoK25PhyCtPeLnjDqKn5AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j6CRA9TVsSAnZWagAAnEEQAI2lLqvtEftgpj8RzTXH\n7BXXcWMx3lmwrIEM4rwsUmuTsa6CvKaxo79Jcb5INSK2yPtZRd49zNhYtLSy\nTcBNu1yE0A8OZ3muTiqHm4Z0WHqGVAIVvfuH20GNj8rx2Rb7qWy/FUiUqFaz\npha+B4cxw4ceAi169ZUyOK1oFmkoRyIOvcb67Vz0qmo4HINkjSVG/U+CmQDW\nf1/1giQl3cBk7iAst2scLeUf59doI9b9UjRtQLjm9HBBAehiEXxrxHxiZeE1\n6kCBI0+bPFsx1uZS8CmR03s4VS2L+bBsoK9GQqWermp3qvVmxViaFY5xfaI8\n726jQQGHe2ig2gkEuFVk5X1k6tiNCzZCZkrzKaWePf7Da0LTC9c01doZI3yo\nYyhpiGiuPZuj7OzgPoncTX4jejMGgpqMrtLpg4cNTO0dcJwAXF6xEx5pGmOZ\nMAlnfm3d/UVEbKUkX9XXb1Ji7m2f3khUprDDykdECFjvnY4FVVNfn0vpQ531\nJzVr0hgl0MGN+eMUyYGpod/gSlNTJOxABtZNLl4s3AePjqJGnijcK6Ovc/vE\nkJWRtPjz+pxvOtFnBXDo8up1R1dmKkQTthZzFT92MOYWJguuxV8j3eT3lhF+\naqZcWrjyRfTex4PVKNVcj7AUZipYUaEMtNdhgRZz3gdRaA4j6qHGCWcaXzrr\nhQCz\r\n=oHsM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-create-regexp-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.16.5_1639434490152_0.6013709452114122", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.16.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "6b2d67686fab15fb6a7fd4bd895d5982cfc81241", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-Lyttaao2SjZF6Pf4vk1dVKv8YypMpomAbygW+mU5cYP3S5cWTfCJjG8xV6CFdzGFlfWK81IjL9viiTvpb6G7gQ==", "signatures": [{"sig": "MEUCIEJwdOWkUZOMaA4il8a+4IlvhrH22Aweb6z8eLAd6IJWAiEAo3qYB2s+DQ1uDn3FZtv9NfDpLS1aa/Kxxlb37NaUuNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1SCRA9TVsSAnZWagAA2b4P/A85OOkHzS9+EC9vAvV+\nkAE4KH3h4AQFygi8VmTt5gqq3rXotKoWNRWnPYBFN2hG5NaZiaLIQ3baoGeh\nG7wAhUv7s+qogVeV17kO9AY+2C6snYApGm79zZpakqpJIUEPwoaR1hz4Ir5j\nfsKE8yf5uyWO9HsHkeHqGuLIz5qQ56lC5q8c8kUW/N4HJAoUfpunCi6PX+mG\ncf9hqqZJXql1Wpfqbamd2vglaGPj4Q2eNpd1oAN8/jWY7b9/WvXUo+X4mV1J\n6V2vnLGqEVsajSJX1c7qA9nvqGvBIYgsAAc4f7+UbUAGOwM/CGRiUPYe0JcN\nDSHXPx4zF5YUbI1ClnLf1RedUlFOxmUd5pV0tiqGoewuWZLYZAVyJAatA1s9\nUD3jFU0lKuKRYvk/db2thyDbzMLWmkfC6MY0o9NCcgA0n3mhD+HLzmxYq3y5\npm/ig49hDhBX8bNKlcz5Fp1nPiFtkpEldZSrd2X+gFlamh8/jkjQcRTsvE/g\nCvX39K2AFGC4Dg2XOL1MR/yQ+6a6adBqHgUYgk6Cq4Sq+sXFWkZpmrcoMbWL\nQrKX+GnTOBdRPV1gnZYishn4T7NULvxGYXb1tSoAw45ZqLjxWrIRFMbghkAv\n+zURWMcBwnrIp1YNu01bSrJCYsiH7SCvTOS0XXvFSDFIDsKYdfOMor5hsBd1\ne87b\r\n=BXBH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-create-regexp-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.16.7_1640910162706_0.017565245973596433", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.18.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "b286b3e7aae6c7b861e45bed0a2fafd6b1a4fef8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==", "signatures": [{"sig": "MEYCIQCLur2GIWSPpHR2dEYEYo60rRzIZlWPuJnzARf91khM6wIhAJDJga5uoCcndpWzZAae0j0H2acVCOO6MSMBgvynzdek", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXMxAAjT+lSFNr1yV7JQ0ij8OY9ccBL/fhFw5yppjxCryulpKQxOIR\r\ngxvTiKKf7XCV2l8aCTis7DhV7dWeQfMBhQObGI8e1htjjUaRMUE9W73rOfcI\r\n/N7wDjZUuzMD38ixYYYA4MWwR1rwQPdqS11lqFsCkDc5Jgyq8NLFLBZ57A/l\r\nf6Gog5zjDKGdxLDzVjxkWIbF67LbVVIr3ICgm6SBBr5XMbOTPODD24b3tKTp\r\nGdIRquPZ+zvmUIiQZwTf3L0HbNaHyn/S3FkjM2RDGZMzZGzsqtStVaCYhL8l\r\njozi6QWXPVFbK8vH8T7QoPqFFhN9IfUalMC0RdEUSKUuhhcFdUrXLiTMEK+3\r\npr2qzTSuOQ6Ngp3XE3+ZzVQU5cntTa6ZU67bKomoafAhcbYhfhZUBvhT3mdM\r\nKHPi1jcCY0758y8IQZ6gZNtwu+bQelxVRVi+qllJXNa3bZSX6oBAtTcknpV4\r\nXqKtQT01jZ9/iLHmmt6Dqit0IyEPsrgM6pbJn0cqdgEy5IqW507SRTbJ2eAj\r\nyxqwWzk3qOhNt/JWc0LYeFpCDuwnY6YfUr/f6MlvSdxS1So4ZZMbChFJ6Aq2\r\nabsC9P2V57bCG/s0r3M2C9k8R9Cvu70yrEn+3Hnja4K55AcjrzeFhRR2t3aC\r\n71Un2A8BHfnOThRTYFswnSC4Stad/m2EyzY=\r\n=yohB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-create-regexp-features-plugin": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.18.6_1656359425775_0.3511727140336447", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.21.4-esm", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "027a4cca01cfac898a5698608c01883aee0ab05a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-UibXewW5sqjBn8o42Cs2zncZWbYL5GFJs8aAU66Fc8JLzzQfRoZ4+UT1B3fWWlVSqTVnRn5189hsUMCVTvZPPg==", "signatures": [{"sig": "MEQCIDCC7QPcc801Q0EAR52VcFvKHWm79Fdj1C8QqzwKJUGzAiBaI2atsbgo2PdR7FsAmakO6cmJAq5KrXHFGw40ZMSOww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdPQ//aPG/udbNIwp+KJNRfIjgxDVr0GrE4e78kPLYUAcWrtuVaPA2\r\nOZbTlAjMTJqZ3h67TN4u454xs6yzKScuWJLKq1CDnf+SKmlKZyqUUO100aDm\r\nEAeTh7voEA1O9qbOS++xAZKd81a61VnWMYqjoijdm3KvDjcXGdVlXNKpzeWE\r\nvmBqgOVh83/4gSVGozBkaOICC0BGjnK4oLSb0dXt+spFV9VJRUhGYb5vvc+D\r\nZX1rSId2u1wTScKMhoAcs8JFRaS40B8BN0wpSSkGicC4IvLObOP/yAgeQZmf\r\n1N2TrLX6Bbm3e59k663VArqUdozFP+3of3YKTfBXAdUMSnmY57Uf4q+APtQM\r\nZmJNNaEfBsYtqzPJUGHSgUNGaiJYOWBK/1aCJpvaZtVa/LvqPlkc7NBnZHIk\r\npRAlqdoy2g3HPodMcojDeCUqk1swVlPOkpc6JfZjIrVirk63CA03nbV3xl6o\r\n0eMzimyJCsJPfaQ75d/xwHg/fYTWpxcUciDgx7KakHmwZJe4Yr6hXO6JD4rF\r\nHEojfawnb1BaCuKuEQIV7sFftWqdvtjS6oqP4WJ4+aeHYI2H0cIHHHr0cSeh\r\np2L3M1tzyGkmBLIdoPmkuJ1n4Jtap1aREatqsbVbOiglaIDQ5lyKVccElQ1Z\r\nsEYvIlEUtAgRUdjUW6R5MB5QllbEoNtffi0=\r\n=Zag3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-create-regexp-features-plugin": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.21.4-esm_1680617389766_0.8322032758804399", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.21.4-esm.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "db05405f5c15aa39c7ddcac97763727f0c3d5126", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-wYdvMVyUzswOXgxdXFGAvuQdz04rAb96OxdRMo6D/cY+yRWr3Vc9sGlX+P9K42RroMea7JG9qxMdy2OtUBkf+g==", "signatures": [{"sig": "MEQCICRzQ3aHbjw1JQsqsedFWUvnd8hUnHKIeeT67a+S1AwDAiBN49v1gPR70cKqhKZtUvth8or/kb7OeSx35vLXvQdCsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7pg/9HzJVrU2UP/hJeFV73eCvHxqU16vIK6YuOuVYchWauHz3UaaS\r\nWx6LidHII+M6pBlg7QlEiEA4niG0ClMRfEAPi/gF/1LVoKBM8lKyJJEauCcd\r\nyBn77y+ivYpFnThVBbIP8IIaeNKBPm5HQcXo98GibXAEXpiQ9aCVu5qo1SbY\r\nuA8MdXOjDWX/P7wsINJ2+COSa+U2hbCwtBreABz6otV19U9/RP4eohC321gN\r\n0JSqWB/n9Av913kMDsL1KjafBYgGKdoduauQhNmzNowrlc7EQKHUr2EkKHxL\r\nq6sVizPAXkUztZCq5wJQHvBo2fMkJ+x8IOjj4erXTECJlc78Y83Uwd2rgV5/\r\nA18BJHLMUktyHh2UkczaDkD2oq8xqHvT4i9+L3VYgcqUt61FrO+mFhJ7bR2k\r\nkmMJcYO2L8sThySHZW5kc9YfUDTtWTxvaX//3TFgmTEllDo4/DC0oJamWhZX\r\n0WwvploW8lPOtTBkDPT31AHS9PgF4Q5JQsVIbFWb6+iVqk00rAvzV7Kz66nd\r\ns6oZXvmhjWVFPEEhICfNqZDCxUL7hGZ2/9l36pqJ12PuYlF1iVdMgh94Gp1j\r\niyR3ltdyc9vuSR5BJ9bNqK8eEgkE7c67qED2tlJz4Z53lT1DhuviYi63nVs2\r\nrtpOyRU4fGFvtfm26IMLOQ/M+ylQfz05DRA=\r\n=/J1u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-create-regexp-features-plugin": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.21.4-esm.1_1680618106919_0.7886654159679913", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.21.4-esm.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "8c2577ea1a2a795709b0d02ca428677593dc83ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-9TBIfEfMs6CTOc1NXlHoKZ28nQOUA4RGkEDHzAr/MWer+6bVthEufQvoAMe5+JYQLRKTOAu1MEG5NaiBMd5Kjw==", "signatures": [{"sig": "MEUCIQCtLM9PNdI+cKTuXVZCb8eX9vdEVIDjVaC15+UGaf1axwIgN3rBr3fvOFbkLBpq02pKjrf4sSnBDZadQfRq6EiQfmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3760, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDawACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoW0w//WUm/K+9oKHz9K9XYHhRqoYTAo5m+zl5vZVsSBUOtKCzSUQgw\r\nHvCayBuMXe4JQE6CAOo5pl8CWL8t/XY2pNJ+MoswGQPv4amnwzBHovqaAQpL\r\nyj/u0gABP5MziMbd/HgCrXAC2oWe3q2GtusUVS0n5wZE4s4PlaqQllYdmE8O\r\n6q2CyvcgEYXNfeRv7YQmmS0PGCtfI5RdmIhkvbnramgVy8QzjWwMPcLTas5K\r\nZCigbGkzrFtZgyzURLe8vTKTs0hNhqpaNct61qOTGIClX1mb73gLjVz+v/3y\r\nuK7pRu3EGUmkDaoNOJ8bvQm5emU+j4K/aC1N8ZMgYH92J0rcZvMVVoq2WiEf\r\nebvFXrkqDdOH6DLVJovomYtqh8TZ62tKd82x5AsmzxgLt602PsueGT6YJ15/\r\no+9NPk7Gu+x7ZpgeYVV6pcurM6rqBOI7jkfTAsLgRHl0INEhd+T8AjAjvfzH\r\n8EfCROdc/jxiKpskPn0dpOMbqJiw/9FnFreYy6aBNBIyonc+2Gm2N+6D1dPl\r\nILMyxPtNWs1CtfSH3O9gTL08nDuAQ1yZsXSSrXiMMvSvqfpKIKvaP4pK/+TB\r\n7UAsP+CJH8ukD6ZIfqDMp12R6b7qIjHqx9kUOu2xCtdpC7sKb/2HujiqLV1V\r\nH19jrA5c7OpuCvwLri91fkqL+vEbkv53vtk=\r\n=30NP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.21.4-esm.2_1680619184652_0.9053098274053102", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.21.4-esm.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "b0eb90c718fb2742a4d3caaaf5534ca7610bcbd1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-h+prAPmixFwROXy/KiKyqQNF9iIDvm+zUdrJ1uKAPjbfu1n04V4lcrjkupc89o1ssTlL5f7wBWLyd4LkYtbUTA==", "signatures": [{"sig": "MEUCIQDmwxr4SvkS+jMQEA8q0Ba1NeHfc3hYP8n3jpjyw3wcHgIgdqfTa6SrJuVU2YsLdJoAxzREHzlFVb+leNdo5J2k3a0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTmw/9EIiw217+CQy4HumMaerY8+jI/pcb7XI+HayvzK2sIZw1a+Uw\r\nQZ5tOamxylEKozTs4H2RwbSk1nlPtlpaDvT2YD2S8BHUsh9yQhzF84oNPofO\r\n+X5u2IU+o1mK+NGnu3fl4O/raBRaCpRHc/KxNMKMCZmhZ2uMbpVzDVNLsVgZ\r\nl6/MTPKmoCGBqef+f7m2SgCVm9y6ITyB0Ko8dXIVU5fQyRN941b/IPqrT9t0\r\nhsYzyuxyTyWEVGXDoqSvMRgcFawvj3xmB2Qa3jmrB3v/BCJ2VKwBl+aGDfTu\r\nL5MZddszDMmY96GhZjKvo8y1GYI8qwXlw7xVGxjp5kfwgYBcrsBURKnVQGhl\r\niHXe/d+oS1Mn1VAtuGYQV7ejVghnpSE5M+FjrClotRvJan6XnvvtrsFDBgAj\r\nRpF5DUn2X9Tf1HyVoWHPGgzvVkhRaykIGCTckRFc9KUST+NJXLZU48Zclm8C\r\nDHh+eVUxVelJkxe9jVIhnLZjLYzztD95fnT8YSJcB01O85DZXZpwbEsu8yMs\r\ngyqpC/o6X7Ux3MM/icNzZ5oulwZrD2QgOlg17dZTGcWocddf/xZA12GgTccR\r\nVvK6uVHnA+Yuv9iRDGl/S/YBbaRjoj9WOJ2bNDTmdkq1jpiuDPLIipARkw5/\r\n34SzI7AaUhIYpIartBRWbFr10ZChy2bErBI=\r\n=NShj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.21.4-esm.3_1680620192309_0.15226622264228507", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.21.4-esm.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "054c51292d84061aaa0c4cd93ede80707eefa15f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-jWUBSJnFLv1Lo9SING0ykJmu9jv895+R+1/dB3K/E6nvvI/hp9/taw/vk0Qxh4m3sdnAY7TLpTTsm5Ny5OAwoQ==", "signatures": [{"sig": "MEUCIQD9ko1r/shCYDfvGPpB6r/elXLrqGl41aRK3Ks+8vsqMAIgNI6fClSVEgPqcTyPUvZAuEQngPq7mPdgRKPeQ4w/45I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBbw//ZdeDK1sRnmsrKeLFPjOvDoCRTTqsrnTAgNL5t8Z9p0rIQzES\r\n2qQLyz4Acrp4WgNy4qK1XpvCmJkfmcsjiGQ6BYouzv0zYOHRMUQZA2FlR9kM\r\nV2JLAZDFT/0CfbAPjyC8gCvvZxbnu68tr/m8rsjMVxhjyN+3Jqw/up64a7WL\r\nWGaopxN7qTQ73B2kE0vA3DsFYZ6E2qnOQuqwKhHnfga55FGLYcOTkIgoE8BY\r\nkCVqZNZ+eyDYfTie0n5JU6Ltik8wHOiXQ9gwLI+xo7S/y5FMqPZAPrN+/Zej\r\nrGnGnPQzOCBF72Mh+WqJRdkAxDvZ5UbHAJUodT9A+unW/NTCgCglCSxStG38\r\nrkr+Wx9RiLNeHpXGTk9q2OO0lCvl8mYIPLTaEOoEBWzbAyEAkwHzAMqvXyra\r\nk/VTKlUK9hW7zzbUCL8RDcz/okjFkgk4lbeeWaNY0mSYiDd0gd6maZar2LjL\r\nCPHtLxHEMmX31NM7QFJIWnM1FKTBpNUfYQoWfFP9DCsEWbaLKAzL2v6D0sFY\r\nzyj69Ju9NnYkTfxFvvRw3kcEETTLGwi3RQzN0w2eeu7xdj2fjTApe/FcRzfv\r\nHY5AJqDxMrKYgVmaYmb3pk8x87UVEIlwBHyky0ToyRQAaCVHUiBYW3fMDcH0\r\n1cXFjJbXxWuEV2vjbwNgYBViamtd5H0hiD0=\r\n=MMQC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.21.4-esm.4_1680621223113_0.30671130569393834", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.22.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "dbb4f0e45766eb544e193fb00e65a1dd3b2a4165", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-5/Yk9QxCQCl+sOIB1WelKnVRxTJDSAIxtJLL2/pqL14ZVlbH0fUQUZa/T5/UnQtBNgghR7mfB8ERBKyKPCi7Vw==", "signatures": [{"sig": "MEQCIGOHUUj8XQuZJP76mAZI5rSlVnf8pxUgGxYGmhtdTAluAiBm5UUmsb/Jbkbe2XencIB9MtHcyeznwb9Xa/TR5Pryvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4090}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.22.5_1686248497641_0.8571902359891435", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a2ea694f53a8e6ec2b8348d9daf4cffc73cb65a3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-OTGXO2dnW0ZJkHtmKRbZfWPsbzDmPHglzDE4ElrHFd7lTnWbBjWDSSZdLFnwsvG5hp605nEdHvfiTJR/hskB7g==", "signatures": [{"sig": "MEQCIAYcQQMBPiZydhvjcskeFOIzu6Joh4FZft1QrDyrMTjEAiA+fpSM5S+e63JjFavqca0PgkSEpCP34pVr1UXDxQzE1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3896}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.0_1689861620167_0.32243746522387573", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "16ebe3fe506c3187f3fb12b3c4df17186c1a0a25", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-6ev5nk0vPHKic6bzOOek5xS1xL6z0utb1Y5fJN4OfDDiFK2rZXX4ZyVFK7j9G22gfZAk4y+TPuOmrj0rUl0fBQ==", "signatures": [{"sig": "MEYCIQCFfgc4mv+CzdGLOXCoFtKIL/F0aC/ZvJhPDo9KpQ/3sQIhAJTDvlgJnMAzVngSAOLpzdiGYTyVz7W/pCgnNUfh6qLZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3896}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.1_1690221173685_0.2954064969216019", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "3f4f6311c53202fca8619bb535024dff4f9e748b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-rAmfdtmaPc+ICUBizppZak9n8ZwKVmsIbhcUP+0MlXSZME2lwSw1ggsyYcs2/SBaU9KLOVS3sQoz7t7Frn846Q==", "signatures": [{"sig": "MEUCIQDYiFqo45d1InDhTtsKBlUtTBq7MBmRHNaqcxizscSgsAIgem/E4epxqG42HQ+0EQ45b0sc1zELj/x+2I/5Ax1xhVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3896}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.2_1691594116854_0.03362434838826367", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "696417bf57330cb64600518b83c99478b6868c9a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-YapkDHtrFyOt82PsYZHJheatUfgRd3qh5DPvtwblkyjejX6OJPZLJCVEx/9TzCyQ7cGPUviV2T3X64MVvCZJ0w==", "signatures": [{"sig": "MEYCIQDI378ge0lgj/UItIyR5TUrVQ21rByJgSYCJrkFeZnn2wIhAOKQLX0HMtLcQ1w1j1OWCdRDdgNVKXO1yCsK7Lqp49vf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3896}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.3_1695740248356_0.8961078425615574", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "aac28c1d68739e29a93e5df91780aa46ee46b8fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Wyb/OFec14v4BSe75eLpHMYgyni7QkYklvsE2NMY143r4Zn0FjL4/l3g4ngtGQLb1H6IZhkc6JQ/U2j5+5g0aw==", "signatures": [{"sig": "MEQCIDQM/AJPZlSrFoOQykRYUWUVpx35IajF5A18N8RSl+9UAiBsm1+93nWTO+V3LF+nW9fONUJZ/3xACqOTv9EMzoo7qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3896}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.4_1697076402565_0.7825690279799882", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.23.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "3f7af6054882ede89c378d0cf889b854a993da50", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-vgnFYDHAKzFaTVp+mneDsIEbnJ2Np/9ng9iviHw3P/KVcgONxpNULEW/51Z/BaFojG2GI2GwwXck5uV1+1NOYQ==", "signatures": [{"sig": "MEYCIQDhN/MEtyUj6KH43zZwKC6kh0vgfwtrWHpSZeKmfxmggQIhAJoBZXH7eTmrsMHv8Mu9U9qeziS6Z3Vi2qe2E65bL8o6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4171}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.23.3_1699513427990_0.7555596619099738", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "403868c08d0e1e62a48b3f6803e57ab53cf89189", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-dgSyRVdlERhvLnxMyk88QIDtW+zfkr4XOYVfWkY0SIpFs4GNmb/ThPSfT6D8VmqGNjKMbAPTCkMy2y9CgfE9sg==", "signatures": [{"sig": "MEYCIQCdPSBrJbLtTARLYBI16MjBdv+nfmBwSadk7NbBAWiw6gIhAJJMBJaRRuJI3lX/bXRYhDmVTw2w/FtBogZKI1t0JJGX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4009}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.5_1702307974446_0.6845023834101893", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "d1c625557e20561e3204ce6dd5315fd489b545be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-7wk9UNiMA8dwFKiYa3lJtC5r8rA0t5g4WVLsTMQGP68G671NhSKxz9Xppcwb4gQayrAzL8OrwYBkCA4MHdH5QQ==", "signatures": [{"sig": "MEQCIEPj2wZnMGtQzAzcCn8K2tZGOKTqYsszvUC60T/w41FfAiBcAqJtqthARRWwc6qdLHNDdVJtzavgOu5sRgLHR8ILOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4009}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.6_1706285674379_0.6384222459654967", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "57615c3537c1a28037715771856884c4ed07274a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-D2MYr97DFhic7ag6HW1P/TM282hvKkiIBTwOLhxpp4yVTbLRB0TJ5BG6TGgCNz3ld6OIvfWSZkMNVGce8iTbKQ==", "signatures": [{"sig": "MEQCIDcJSrjRv0V5GFwI3PiXevqkwS6Ss8oZxx8q9hstCz8mAiAhU3Uk2woLuCvTyRc5fjbwsy24Z7EqLY5KnIphWRvlRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4009}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.7_1709129134482_0.809948565060258", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.24.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "d56913d2f12795cc9930801b84c6f8c47513ac13", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-p7uUxgSoZwZ2lPNMzUkqCts3xlp8n+o05ikjy7gbtFJSt9gdU88jAmtfmOxHM14noQXBxfgzf2yRWECiNVhTCw==", "signatures": [{"sig": "MEUCIQCv+GkzqcvvwVzCSAdjtWjyoYi4ejRhEOVr8szToUFJtgIgKP1oGzJqFQgQJhv21MWpfhM/fvxToQmLlNrgWCLGTxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4102}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.24.1_1710841713851_0.45652425979842515", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "4ef2ed6f65f673064e71aa09fd66d1be105bfcbf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-US5yoWV/6rFIuYl/cZMIls0WX1mFkDaklY+4GGh4YsXILp/X5Vg2EsBv/64ClWVSVCTs2Zq2yhxOAHVZJIQWow==", "signatures": [{"sig": "MEYCIQCP6wNheKCiaPL1/C3yQM/r+nm8suLWoC934JGTfSlfygIhAO02N3hNNuOBRue0N+5FvwTJoZp2MCi8Pwtwj2Q0x8PC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3923}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.8_1712236813338_0.29835120338527465", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.24.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5a6b3148ec5f4f274ff48cebea90565087cad126", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-rCXPnSEKvkm/EjzOtLoGvKseK+dS4kZwx1HexO3BtRtgL0fQ34awHn34aeSHuXtZY2F8a1X8xqBBPRtOxDVmcA==", "signatures": [{"sig": "MEYCIQCtWGIE53GI40LGIp6iqOFO4/1WNsrDGwGQ9aa6TvXjswIhAPqVSZrdOlop6wvnw/2YFOVyenpj4LSJhvUGcFfJQ6Nf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70475}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-create-regexp-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.24.6_1716553504010_0.5992112207391325", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "108d4081dc1f297ae47e560b8e41bfe09934bb7a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-wkbx0VfFSYhytnY8rIpBFxim9E5KalSom/KgHDY7jBfURCFCbtcnbWDiD4P296wzizGuSk733a3vX2I3KbHmXA==", "signatures": [{"sig": "MEUCIQDC2jSC/KELuOSApY9csgIKPTSUs+1TRhRw+dR/H9hd+QIgaf2daD1VQgFn8Aq5nFIfbFXpbfoUtSR2h8WdSdeSc/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70574}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.9_1717423490496_0.7659963059493635", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "1bc8d876f1e436b8e15e83f22182ff8b250ccd4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-eRLADD06wjcUHUtXxSywYzikdIt3fwRHLRTPTDg9Y7u5rQcNCVf+PEBKKMukT/GUPCf0PQzFTa7ZbHraT1A+0g==", "signatures": [{"sig": "MEUCIQC0TyzDlibdCnhJFlVgtN5ZYQ8t6OLOpkqQuFonhx2ZqwIgZvTiY7Iqt/0f53GjkGjbuWoY5yEeWpLPPJa+f0CR7Mc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70582}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.10_1717500026872_0.12109241092533174", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.24.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5f8bf8a680f2116a7207e16288a5f974ad47a7a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-ZOA3W+1RRTSWvyqcMJDLqbchh7U4NRGqwRfFSVbOLS/ePIP4vHB5e8T8eXcuqyN1QkgKyj5wuW0lcS85v4CrSw==", "signatures": [{"sig": "MEYCIQCpnPIomBraKU+nhmr9fyVOJOJxQV3vK5RcFbk1g0c16gIhALpo0HVDDOt3KdTtiZaeLKfh/vX8t2fmpyOY40TVWbJO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70471}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-create-regexp-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.24.7_1717593340761_0.7612405866824001", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a6a8048b4ddd426c82454f71098a542b700f8c71", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-xlYIcczwvkX0jfTGEpgkY7qFOo91gvbFVlAy69QdJVrvj1xU5EPvaksuI9YyjueuiXZ2hqhFjNjPgF1cVNEqKA==", "signatures": [{"sig": "MEUCIDRg81+yDBc2Nqu689T+qCAN4fg8/NBe1tYpTSfMLpFCAiEA6q00q3GFMVn75afU4Gd6sHnwBA2MSK2XI7GzuRlDa1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70471}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.11_1717751751999_0.3887352258145267", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "50c4b257df50a8cae8aaf4bb9deeeb6f4ac24db3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-zHdaq/NFphEXv8/5hL3xXl1vHR0sbNUatTBW/KStWdxm5UkmXfCcnq0ZSg0scOvFRB41jGHt6oCSAHlcjBgOFA==", "signatures": [{"sig": "MEUCIFLzZRzTx7RmhzmVKuPHj0vzDJTawF1ELqysJ1jKEP6MAiEAqgrVn9myBGzz8AKLVZUhqflD3cA5Zr6kN0WOUZKtzqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67250}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.12_1722015224335_0.24699506038393437", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.25.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9d775c4a3ff1aea64045300fcd4309b4a610ef02", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-kXzXMMRzAtJdDEgQBLF4oaiT6ZCU3oWHgpARnTKDAqPkDJ+bs3NrZb310YYevR5QlRo3Kn7dzzIdHbZm1VzJdQ==", "signatures": [{"sig": "MEYCIQD9oJX14Ij2f0VWa6E39A5OJQ1D6kLW4sDKZ/9XASm3WwIhAMZfhrSApboMHtAVQmD9PqsQCmgdElgxV4QfU6hu8HnS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74992}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-regexp-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.25.7_1727882110360_0.7501563860029448", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.25.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "bad7945dd07734ca52fe3ad4e872b40ed09bb09a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-t7ZQ7g5trIgSRYhI9pIJtRl64KHotutUJsh4Eze5l7olJv+mRSg4/MmbZ0tv1eeqRbdvo/+trvJD/Oc5DmW2cA==", "signatures": [{"sig": "MEUCIQCQpk68xZupthhq1eq9CwFp/yEE4uUwER7Vbf4MPiYvrAIgE1iOAkjyIZDC+4ixwcjAc6zbuYALALZFKh+NGBSFXjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4134}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.25.9_1729610485903_0.8595254182683001", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "552cc7f86b0806b7b768cf3c28d89072a4c48c4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-2Mp5icM50VXXeqffHfp268lcWozzH5FTyb2BHANAgF5oMhgclphPKcXIX/QeBD9/rISw4qFKR+adwJPhjycInA==", "signatures": [{"sig": "MEQCIAyG+QUmAzf8g+t2Qr963iVnaAhzGNxe1nJAdlXVnt0iAiAjOGfXxTY9r+TKNsBJcp6wNlqHB19RgDfmcSJFMZ1eEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4262}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.13_1729864466440_0.9337264982927211", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "2f87b1c2770f0c63721edabd7d3a34b32ce2bb77", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-ggjYu96CPStKhgqZGcPLAFxF7NNXImK6/ZJd05SnYN7dotbSfNK/Pr4thbNiTQKCe2BuoinKPAHq738bazcl1g==", "signatures": [{"sig": "MEUCIAR+Pm6UoBKjQx8zaW0n9j5m/JvN1ahfARP/kt8WJCfcAiEA0E/GwWNCe8y3P7jglK344lHvnrWbWvqQ3f8BG9G4m4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4262}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.14_1733504056860_0.9065790519734858", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "8c7205c771171bac48a3d6ac83d428626f33aff5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-ExLTj2PmolflDWuGsBRZZUGr6zW/KPFIJ1uM9Sty7+oynVC0j9cLsdbpGvVc0nhN/GJ6+Qf/c+MgZgTWf6gieA==", "signatures": [{"sig": "MEYCIQCp/C0i7lGJ6XRCFBy+G3pNamMQQbltImDMrO8dewqdxgIhAJY01F4nb5qJK9oSbw6QO1ZblA30V6NmAq0SCQLKvOoj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4262}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.15_1736529884083_0.9356303981279159", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "81a50cfbb8682b5ef18bb91586c955fcf3a9b662", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-/1UCpwqTcWeJ+Pd7GwlAdfbfex4AKrcqm3Xyixm/ne4zI6dCP33IWTZZA9chgffKpieezO3eewVHhR7XDu9kng==", "signatures": [{"sig": "MEUCIQDLvoa8pFNJ8WETznEQ+KKWf2mq2eNsv+aJuBx0Mg/5gwIgWr147oJuWZ1Cp0oJsuzZllgcimMpWOlfxA5RzfXyymY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4262}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.16_1739534360154_0.5408852744439705", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "efa89d86819993d773b161da281818ddafdeb044", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-H8zWGFzE8IiU7/5lPF6/dR6hTNSVoukWrqaBcrvDIRGqVNK3w4bdaVw7++cfQan9HGgUf3xXBtuS9Kiy2/8jGA==", "signatures": [{"sig": "MEUCIQDAs93Qm/Zyp+ohr7ybNn0lP9khVnbBTTOMGwO8+9VqUgIgVyjxzcKStp6bJqsRVaEOvCckXCbpoblqcjcPWdmoYaI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4262}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-alpha.17_1741717513301_0.02907175065886225", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "7.27.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "aa6821de864c528b1fecf286f0a174e38e826f4d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==", "signatures": [{"sig": "MEUCIQDTfx5EGUNY931GWEiBRw/CnjrJ6bL8C9iedfxUbpmUSAIgSK35o3yzhGrKU7ipKK5TA6yO5cjpqScac+M/uMYzNV4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4134}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_7.27.1_1746025749554_0.43095601097514447", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "c883e83e46b09044ef15ed2709b09ae986f78b02", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-QHBl72i5NK2FY1y0s69YbRp1yYhj5tzcQ4zDo1a/VWjrCV3Rk1GF6AeNSCte3Yi15e38+PwOR3fX/2FPzqypWg==", "signatures": [{"sig": "MEUCIQCxdvQee1/egGB5ed802S5RCNY5Fw1h0VGsa2qU6mt7OQIgfiIrF4PDCtQeDerR3Q2atjNZXybdF/b23an+XW+y9hk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4236}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dotall-regex_8.0.0-beta.0_1748620286445_0.902706631355199", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-dotall-regex", "version": "8.0.0-beta.1", "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-dotall-regex@8.0.0-beta.1", "dist": {"shasum": "9aa5a6f2e85a701d660094122ac803931ca41f7e", "integrity": "sha512-bAieq+cotWnMzMb44STMJW9HJzWIM69HaSl3/BFZy4WYGenUFMLgrNPsMjxt/UXtFSy782N/MTybYDjCCijWuA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4236, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCnWnyRBbZ1FdtDLlqqxq88CH5UNLzmB7SOWrd4YNoeyAIhAJud80EOl+F7lPaBAWoOF9iXevudjEq4sr3OUZoBD9//"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-dotall-regex_8.0.0-beta.1_1751447070808_0.7020350504541331"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-12-25T19:05:13.449Z", "modified": "2025-07-02T09:04:31.252Z", "7.0.0-beta.36": "2017-12-25T19:05:13.449Z", "7.0.0-beta.37": "2018-01-08T16:02:53.432Z", "7.0.0-beta.38": "2018-01-17T16:32:20.787Z", "7.0.0-beta.39": "2018-01-30T20:27:50.738Z", "7.0.0-beta.40": "2018-02-12T16:42:09.039Z", "7.0.0-beta.41": "2018-03-14T16:26:06.931Z", "7.0.0-beta.42": "2018-03-15T20:50:40.826Z", "7.0.0-beta.43": "2018-04-02T16:48:23.289Z", "7.0.0-beta.44": "2018-04-02T22:20:05.229Z", "7.0.0-beta.45": "2018-04-23T01:56:42.898Z", "7.0.0-beta.46": "2018-04-23T04:31:06.430Z", "7.0.0-beta.47": "2018-05-15T00:08:48.481Z", "7.0.0-beta.48": "2018-05-24T19:22:16.249Z", "7.0.0-beta.49": "2018-05-25T16:02:00.863Z", "7.0.0-beta.50": "2018-06-12T19:47:15.610Z", "7.0.0-beta.51": "2018-06-12T21:19:45.841Z", "7.0.0-beta.52": "2018-07-06T00:59:24.182Z", "7.0.0-beta.53": "2018-07-11T13:40:14.348Z", "7.0.0-beta.54": "2018-07-16T18:00:04.810Z", "7.0.0-beta.55": "2018-07-28T22:07:14.124Z", "7.0.0-beta.56": "2018-08-04T01:05:18.094Z", "7.0.0-rc.0": "2018-08-09T15:58:10.645Z", "7.0.0-rc.1": "2018-08-09T20:07:50.967Z", "7.0.0-rc.2": "2018-08-21T19:23:48.076Z", "7.0.0-rc.3": "2018-08-24T18:07:52.759Z", "7.0.0-rc.4": "2018-08-27T16:44:04.363Z", "7.0.0": "2018-08-27T21:43:02.415Z", "7.2.0": "2018-12-03T19:01:18.484Z", "7.4.3": "2019-04-02T19:56:16.409Z", "7.4.4": "2019-04-26T21:04:37.505Z", "7.6.2": "2019-09-23T21:21:34.622Z", "7.7.0": "2019-11-05T10:53:21.690Z", "7.7.4": "2019-11-22T23:32:34.484Z", "7.7.7": "2019-12-19T00:53:06.069Z", "7.8.0": "2020-01-12T00:16:51.177Z", "7.8.3": "2020-01-13T21:42:09.484Z", "7.10.1": "2020-05-27T22:08:09.750Z", "7.10.4": "2020-06-30T13:12:44.884Z", "7.12.1": "2020-10-15T22:41:14.660Z", "7.12.13": "2021-02-03T01:11:34.826Z", "7.14.5": "2021-06-09T23:12:52.653Z", "7.16.0": "2021-10-29T23:47:50.034Z", "7.16.5": "2021-12-13T22:28:10.296Z", "7.16.7": "2021-12-31T00:22:42.866Z", "7.18.6": "2022-06-27T19:50:25.920Z", "7.21.4-esm": "2023-04-04T14:09:49.915Z", "7.21.4-esm.1": "2023-04-04T14:21:47.107Z", "7.21.4-esm.2": "2023-04-04T14:39:44.825Z", "7.21.4-esm.3": "2023-04-04T14:56:32.527Z", "7.21.4-esm.4": "2023-04-04T15:13:43.299Z", "7.22.5": "2023-06-08T18:21:37.814Z", "8.0.0-alpha.0": "2023-07-20T14:00:20.392Z", "8.0.0-alpha.1": "2023-07-24T17:52:53.825Z", "8.0.0-alpha.2": "2023-08-09T15:15:17.016Z", "8.0.0-alpha.3": "2023-09-26T14:57:28.521Z", "8.0.0-alpha.4": "2023-10-12T02:06:42.773Z", "7.23.3": "2023-11-09T07:03:48.183Z", "8.0.0-alpha.5": "2023-12-11T15:19:34.624Z", "8.0.0-alpha.6": "2024-01-26T16:14:34.567Z", "8.0.0-alpha.7": "2024-02-28T14:05:34.645Z", "7.24.1": "2024-03-19T09:48:34.014Z", "8.0.0-alpha.8": "2024-04-04T13:20:13.487Z", "7.24.6": "2024-05-24T12:25:04.199Z", "8.0.0-alpha.9": "2024-06-03T14:04:50.653Z", "8.0.0-alpha.10": "2024-06-04T11:20:27.069Z", "7.24.7": "2024-06-05T13:15:40.932Z", "8.0.0-alpha.11": "2024-06-07T09:15:52.174Z", "8.0.0-alpha.12": "2024-07-26T17:33:44.536Z", "7.25.7": "2024-10-02T15:15:10.656Z", "7.25.9": "2024-10-22T15:21:26.165Z", "8.0.0-alpha.13": "2024-10-25T13:54:26.590Z", "8.0.0-alpha.14": "2024-12-06T16:54:17.008Z", "8.0.0-alpha.15": "2025-01-10T17:24:44.317Z", "8.0.0-alpha.16": "2025-02-14T11:59:20.328Z", "8.0.0-alpha.17": "2025-03-11T18:25:13.497Z", "7.27.1": "2025-04-30T15:09:09.747Z", "8.0.0-beta.0": "2025-05-30T15:51:26.594Z", "8.0.0-beta.1": "2025-07-02T09:04:31.004Z"}, "bugs": "https://github.com/babel/babel/issues", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}