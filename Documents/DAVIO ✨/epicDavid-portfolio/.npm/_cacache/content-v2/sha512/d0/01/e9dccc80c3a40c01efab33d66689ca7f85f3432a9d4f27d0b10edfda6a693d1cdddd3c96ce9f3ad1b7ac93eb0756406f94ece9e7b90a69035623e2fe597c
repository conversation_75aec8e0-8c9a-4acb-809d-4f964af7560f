{"_id": "@rollup/rollup-linux-x64-gnu", "_rev": "154-739a3449f4b2035409890d3b276aff09", "name": "@rollup/rollup-linux-x64-gnu", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ca805833144435670535d6a266418b35b343335a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-q+7+6fufLhYPVjN9grzvOBotCik8F5n6szaC5vf8Cyiu49/aPjyf7OjA8wtclsdHbO/xrNebKi/z6L+QkP60bA==", "signatures": [{"sig": "MEUCIC8xKMzlKCoOM3Mxpc8yfoZRI/HWdOosaRpgbDdpwhrcAiEApZajVlk57UrJx/cKMc/u6U1ojyXXumD2FhFjoTzAQVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 751}, "libc": ["glibc"], "main": "native/rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-0_1690831068580_0.00024237780952729437", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d8ac67c419b1ab86ab752f2000d21e8d29fe073c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-KGKVBMSQSM4Ruo+L+bauX5avSQRD6Yu0P7iZ+ZV3T9NynOixXoazg/xZwPEMKdI43L1zDbHH6GJT3reRiuy/Ug==", "signatures": [{"sig": "MEUCIQC2nXMT+17ePJ/d3A4rtLXOlQWPBjpMulYrz4VHY2cLhwIgWJmiBaNdz2QXvGWcD/9VVn3yxa0WB/0u7/y4TKiW6fM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2497688}, "libc": ["glibc"], "main": "rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-1_1690865353143_0.7451041543004187", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d819fcaacd774b99be03165e8cbcebaa7b0d839d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-OLu3OrV9waUMry/VrH6easQe50DBHC2RvXG2Y9aQhX5WEk7fShow2wqNSE1Dc+Unu3/nmied0nF1tHYIrW2fFw==", "signatures": [{"sig": "MEQCIHGb/ZCTxNgah2nN9pDMKqvbx7MC2zptUhYqKuecwkNoAiBBSfBr8rtLU3rET9494kVqbi6ua1gCUHtHdiMsmsp0gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2497688}, "libc": ["glibc"], "main": "rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-2_1690888609774_0.45304507124393", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "930a3bd8d026853681e6997a186fc5f20ba6ab8b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-aH1jUI+j9oPzNS8PCUpZ2evR55fHL2Hrxuprm9ZJHiOVD81MJ2tSNYf59Q5N6re1jm5G5DU8UFhaiNF60wvWkA==", "signatures": [{"sig": "MEUCIQCZ+Erai5YiFh+k5Qnn+QqCq+1uJ4vGuNz1DIX6hYJczgIgKhZfY7mB9awpTjiGcbme2xMykyie8jQ3LhoDp8yhZPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485400}, "libc": ["glibc"], "main": "rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-3_1691137033925_0.053304730871615025", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9ffd5827a630039ef4591ed3ef63db0003c5050c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-XUfN9iqgH/tpy8MQACS9geH5vHr3e400I+zfaiAdKSItIhgJXVwYTwC814aVzjNd6QDcGQLI1d6ZDWWY3hjeHQ==", "signatures": [{"sig": "MEUCIQCCNvZY+BvJqBiuNOiSgtk+n8GE5y+t75Nvdc4RRPKCXAIgNFNtGk8TGFvRsifvIUy9IqXMUm6EfhiGUgLKHTPvgc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485400}, "libc": ["glibc"], "main": "rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-4_1691149021206_0.8955783657714296", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9441cb3df645e6e2fc8b92e96a1543344a9e1201", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-45hFPQles6F1uEaAul/rEZNze7uyIeFUvw6QACLssmbNFFVToW/wQ8vAIBFdfau+FY1vd9LW/mNkp//1XkKJ5Q==", "signatures": [{"sig": "MEUCIQDnqrnLTvPMGzEELg5wn0OG7Gi1Q1nk1xbRSamWKr69qwIgbS4WuXnpXiO8Un8wrhd+3JppLh+hxS4apQ/y1YrNqaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874643}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-gnu.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-5_1692514626830_0.6686395527871698", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ed4389a73a34306aa39c8fd510af072475bf3ec8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-jPawuNQ4KZ84FX7mllWTz1KWlhGY8TxCSt1OSFR1/o6Gq3ax4zc+welluiTT9KnN/pY3z5IH44n5OkeXHpSkZg==", "signatures": [{"sig": "MEUCIB7OZ1fq/2pvbJ02XZO3OPLmQNoP4JIHZjtOR/hbhsQTAiEA37YyIB+ztN0nv8oth1rE1ofBJixup3LeJwmGH/eNwrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874643}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-gnu.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-6_1692517914527_0.706762653624232", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1975e79b211aeb8399a895b26c9ad4bb56305029", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-p03Dh88/srSJ8g6ljOGA47+5bMFujDaqYILCTYsLrOqaQnhMWI9fme8pcpOpvQDGtfvm72f55Qw4Sxbtw/Gsbg==", "signatures": [{"sig": "MEQCICVYefAR1WlhOr4Mktx3t/lDgG5TCx8mjJVkVXQQHrvxAiBnk7tdcf6gAqUew6ZqcV4hWSgCB2BnMpvOb2zI98qlYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8329565}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-gnu.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-7_1692527638935_0.9905449362213119", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c9ccfb592b8317e03928b30101bde59a37531dbf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-peBVvhazJgiIBM2aiILH8xIliYrm/uo1/+OZ4Xvi46PNrPCntmpMKlvYtgXTvub+pl/fcwPQ7iWCemENkLI8qw==", "signatures": [{"sig": "MEUCIGgmeriAv72sNV6dQZsgggZEiAvUemaolIDB48qTJ4NtAiEA85yE1Izjeui93xYLBDeLFGuUH9h6hfLCZXsNQgIjXrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8329565}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-gnu.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-8_1692530570845_0.7532011113098382", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7b661bcf80769f2fa8c03082c1a80cf99dffb7b2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-jBces0MJPvaxlavRsj0+A3RKKXNtyEF1J8m78vh0cLrPUnrBJGw5Eiy5iCS527J6Y9H91Qp2sUlWe3rKCUlIJQ==", "signatures": [{"sig": "MEUCIA9eA91LSb0JUKO7yS1CORw8o+na+2EGVwnsFpOhNs/rAiEA3+c02YWJNur6ypyHEh3tPOxR1P7KFgfjIK5rGs3icN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8329565}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-gnu.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-9_1692541776237_0.9881791511879203", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "382bfee0a7d0835bd94a0b8c16069a159111fb0c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-Okf3Jny3/5aSMBAkwgLKJdejs+IpULDGXD0LjXetCj68oTmJOs9+hM7rWzTy4KdpYG29RnjxRDOs6MiQMUNB+A==", "signatures": [{"sig": "MEUCIQD1AuSUBJQWKgQna/KLKSGj/dZwlpg3TkpvrLGK58N8+wIgHX2shJDBrWu6U/saTrJWO8mpoQwy41VlJSlOPIxMqGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8354873}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-gnu.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-10_1692631834793_0.04312769847235298", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a88dcad0db8e66fc19ce69b3bc1c6fa1645e11ff", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-qeMI+J6C0K88hui3muITHYWIskdu6TCu+jmV359A/mUGqiAzY/ekoCGExQQQxIE7dvACe5npdvWj5G6B2n87Pg==", "signatures": [{"sig": "MEUCIQDM1rvuV9fichG87+G/moS2l589rarekxoq+h6n5BxVCAIgQpjNHMz+okdNJGELRtwVWry5KX7ODqGYGaGxlGCo+QU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-11_1692785777022_0.404653390533138", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9ac691c2be52b071fa0abfb27437f516e92fa2bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-KzxqAtWnLcj5++4yYmGtLF+jZzNYNyfnpOFvFgpiHqb6zGCB+p0ZXK5hG59RJvrohuM5Z00AmrRcUNMzEoCK1w==", "signatures": [{"sig": "MEQCIF94iMaeucQDK77O70nJxuxrxqHqgVma9l2JwKAN8CQRAiAX1Sq7KogDWhozDnautgVdUNaMg8iktEFSPSatihD++g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-12_1692801666571_0.5794707475809004", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "62833b60213182035f7d4543327d4e2f043e1877", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-3qkLy/cOszkCsIOgKtduIRfNV+N/N9DRSMiPBM7sBx78lyXhVmK4HaW48fgYmk3Qll5Hqiw8aMeOHHvpz7sFiw==", "signatures": [{"sig": "MEQCIHmsYCkix1Dd2di3eROONey6rP3IY2pTYOzLfL1OX6UeAiAl4xU8QjX41yMcmdfI1vh7f/l2GeI+FO/PFyMiBWYKdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2895012}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-13_1692892132721_0.4993570218261223", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "058f40d95533fea694d7296f8aed734df4c41650", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-5xdE2FCB3IfowYUV6308myF89W0ib7fkLhhfFJ6jqCy4fwyBmtNRCt+Qv3KwvQoAUIIUidqw9etmFw8jSx4QRA==", "signatures": [{"sig": "MEYCIQC5p1pwFs1mptU9bwI0j7wmqHHemNjO96m41GhNJXhi1AIhAOd1O+2SK03lJUH0KtrlHJu66gH9F9miDr7eAWJOgNvA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-14_1694781283231_0.18030986240411329", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "57a6177e3f041b6ed83152808b1ccee0ebd167ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-b0S/OXF7qhjO7JJXBjF48pdp6wEk+oK8IXovFDZSyaOmT50ZTnAtMTadC0kGwMifXe9MoRtMRw7FSnDxrTdiCg==", "signatures": [{"sig": "MEQCIBm0wmtOCm4vfojRzg4HeDfK1jtrjdBpmWdc+rID2VpgAiBk/he52YOlu260HimW6f+VZxQN0z3fiAUqmnhVYwrz4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-15_1694783230325_0.7035390640202024", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a00f0d2cd40eaccff92c3ef8b28abc285679295c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-1ppJRjxK7IDkbpKvi9BAppR9P4pqwx05J7rFYAwye0oEn9RiUe7JXqPKIWNE1CK5z4GDbv/iApgzKPB6DZ37eg==", "signatures": [{"sig": "MEUCIGKfpF9luNo8R8y16TjmMkY98MxdLcv0eEzLuq9W/U2CAiEAmI5mimobl+OMIkq5fLE1OXcVAl34ABtx3aN4R6/irMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-16_1694787455895_0.6777348517337138", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7824f693236a624b152687ca4166dd28df896e4d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-cBDvgACgMXZTELqCk3PfiGvVr4g4upcDbQFUKfPVR+jidlHjJ+GUJvu/Xv+1v6+Jf9F7A3aTuleggS3Fs8zThQ==", "signatures": [{"sig": "MEUCIQDzC8SXUZFq5Lp1JOW6X8owZb2Dd4gsKl3jJjZbQbfEbwIgSr8lAe0uc8b7K+oC+Is3dLU/B+dPGNoNGttqvLuFaSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-17_1694789965703_0.3320883927053979", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bbf3a20f11f083058e04b43bccba16056f38fc1a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-/DdZWk/8Pq5COnEqX7i9rsJJqWE2MW01q7Tc6RB5yuXAzvI4+KfvXbJZfm+eKHpw17MUktAUrfStAfJnCAnn0w==", "signatures": [{"sig": "MEQCIHI3C3Hkq3D3Z+WFAsX+lPwo6iHU5w/nV7KjP+PkapkEAiAnZtp7Hm7vDb0S1Px/VzjfGQFvNVVmGdSgxZuPBjPUTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-18_1694794265553_0.163690818450543", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "29f8e7393f27f92beee83de54e32f4a07bbea6d7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-MdsDeKZTknxvMHJU3iwCnbAyGh5eDvwFja1SQBrjcDDvI1sDRXxp2mZDjdFvcQ123OjJ5bi/odZMHwCOfiWVbA==", "signatures": [{"sig": "MEUCIENug21C7iog6EJ2h27HTNIiCFEkqPW9GAq1FaO+cFZgAiEA3VLakmWNjAElndF/aP/sQo0iWJP8eggmxu1bPXJk168=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882724}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-19_1694803876227_0.7685090909841672", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "312301cd90f60b659592fe5b03c31f2cff687c97", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-7k9kPx9n0RmoKL947Ge49lLBpoRzGq5KIRoX4MHRANXpf8h/DrT9XlcQNgBgTsMyzgZ3+xpMamO4yq3Gg+OmEA==", "signatures": [{"sig": "MEUCIH/uVWwZOLwf+bU+NnkTTMsGKRhUB5gTPt+nLb8CdtXjAiEA8alD+wmspZeGXM4T3hcByX+Vit3sKPiFZdY3o8lhEyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874394}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-20_1695535855921_0.2139295302923272", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d07be764d029a001e0cb65c1daffabe29187bf71", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-nomm2oWbWGCosho0GSuLdwtGsJkij82/wnMH8Al2kOmNUaG9ebvxUINfVtVT3cJ70VEoWi2M/xNmX4iR/JiB/A==", "signatures": [{"sig": "MEQCIGUwQVIbCpEoR8242Agz38ALeoaO4V/ge9V/QjQJEE9aAiBCwECmCv0Nmiu35rpUbCtBTEoSgNtq3gyogIhfUp7/ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874394}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-21_1695576162533_0.8768388545158483", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d954c2fd326280117b95d1877fa91255bab48cc3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-pGoBdjhSU+vPtU4Eu5Drc36lVYLF4PMsFxPod4+RYL/GKNmktMOhHD5EwIFqtv66pwIxbXv2s4J/bG95ddJtJQ==", "signatures": [{"sig": "MEUCIQCreg66yq23PonHw7Jt/9rn96H9dHUARYteUjcnJxssQgIgWPhYG6+9k31ZwP1P3LJd5IxYz8b7Dj8zS6cw7Jt+5uY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874394}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-22_1695745074905_0.06002549582504191", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "21864146739076c1924292e6a31b6348eb0d4c35", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-ZB7Sl94bhM4sg5ZVOWKg0MfakJ6W4kDTZNUufteK83eTVHDMtGqCDTxBfMOAT3lDgkK+H6b1txaKgSht72Ss8A==", "signatures": [{"sig": "MEYCIQDqX3gDZqZvVDbi6co4jylGhypSUBIYZuz3JcNJLFfHsAIhAJNbPvD6bDuQa/2xa9ZD5NBDduy320MEKhw0pTZw9FiN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874394}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-23_1695759284815_0.7703254337571068", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a88e93edc076f5be10ef205adf06c7b39e9268c3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-FzhHpp+vRTjIUYXMExj9Ffj2bCQgnRAzlWlsQTdYGYvPQMVadfPMvnlcr4Li8P7Yv1iBFtDzRVfZAgL5glvIAA==", "signatures": [{"sig": "MEYCIQCpApFKsz2XHRNJtxtplmzU/vQqVoQ9fHPxUuWS2vfaRwIhAMMT8z5dUEC67Nsd5nPv8SdrXPrLfHWPnYi0BB9kvjV5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2898970}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-24_1696309987076_0.531213570308656", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3dce9434a4cbac7ec946e8520faeceba10f51074", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-lwItFjnUUJ/sG1L+lmkoYZng3NKsUncXdmGG0Ry4inD4L2nNRjJ3G7vTUjIJg0Mik7GDtf+rYMGN1/vdvPM5uQ==", "signatures": [{"sig": "MEYCIQCshm6fDNNme3ZR9exyG5KBcwzlzAbksctuT0Wdov3yHwIhAMVlA6QyDJqryyMWQAC2Sw0LhtC6xvd/Nt+K8QHmS+6u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874394}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0-25_1696515215146_0.245257285795202", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0c00d8e8c3d95c399c21161905f50e8bb06efb0b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-Pz2FD/4FUZM98+rcpuGAJgatW5/dW/pXXrbanjtir38EYqqmdVc0odHwqlQ+KFY2C5P+B6PJO5vom8PmJQLdug==", "signatures": [{"sig": "MEYCIQCMnYyWp8fnUdlOifOT7e2XTby/xzhTD/KbveES/0n+cQIhAIswik+1CgsMYuIy+InAV5cIj2lIrHpcs1HO6ltvo0XT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874391}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.0_1696518913744_0.3604743523340783", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "423553c397f9238b0544c7215c7fda5b5c2324c2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-8ow9ljQUBOdLKIKWFBVfdR9O80+TjkbrIo65YhC24Zxnpld342vZdgF3BRKgRECfKIq5nZY4A75JIhr9KaJiEA==", "signatures": [{"sig": "MEQCIF7zGnov0IVH28UM9f208Ny4PtK1am87xbU8gBcFJAn2AiA3PPhn1moR4ai2CH9VX4uGovazzyEgYBUIffCsQmcpNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874391}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.1_1696595824171_0.1382724038865144", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "94dd9d135df6e38d4fd2ca24f5a2ace3389696fe", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-Zqw4iVnJr2naoyQus0yLy7sLtisCQcpdMKUCeXPBjkJtpiflRime/TMojbnl8O3oxUAj92mxr+t7im/RbgA20w==", "signatures": [{"sig": "MEUCIHYEjFDBjidCPuvYtG8Zo4b9Uh2s+ojbQjjKWMXkOrftAiEAj+ZYNDkxIlI/6Hqn3s6ZIYHU3z7FYSZn5f/RnDaqjKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874391}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.0.2_1696601944527_0.16186463390922068", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ae86dc2cbac20af863af2a15377e54047cc0c073", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-8wsvV6xnkLal4WHiSORLeE84FZr/dOalahEK9o1xldj42gafOHnZsGop4Ai2FuryckskHSaufJmxBF6Ps/9+Ww==", "signatures": [{"sig": "MEUCIEgr1aEjbZr410mQl7cf/s2HSejF89W2/fJBkwoeeH4eAiEA9eIH1geaqXpoV/42KMhmHSQMxohWSjGT1G+scCSkvgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2933727}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.1.0_1697262754329_0.4053490639461206", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4b2620816b18873de43a68dbc7c2f44e33b18e13", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-txMwKD8ufSlguqLuiqG3ZFE8TTICKzmYA0x03XCuZoS6DKCXHMtSXQzuk9uz1oDeXKTqMEpvr4hfnyE9YepJVA==", "signatures": [{"sig": "MEUCID5gPgf78Yf/l9tUnjAWeIdjMTtTWZRdFuokTOhnf5UnAiEAk4Oq1cNKs5aAofrA9I4qebcFKSHhJTyXhKW82vn60d4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3048807}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.1.1_1697351528695_0.6371367584406455", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "322f765a1c941dd458cecd88e45eba4f33e85df4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-Zbb2CWJfbu2aZqd7hl9otRffh0MgYVhVsOz/sV6LSJcJ2LldBuvn2K9wVTo1gKh2xtTnJo93vusSppk1WtB4NA==", "signatures": [{"sig": "MEMCIAFbdAlO0Ho5N2rVoo3NERYXsxGpT3hqABuh7Yt6U3SoAh87QYwE+B8ueQf+7Skhtg7yMXav9p/65Cp1APmMxi7j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3048807}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.1.3_1697392126824_0.7551030208416167", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e5000b4e6e2a81364083d64a608b915f4e92a9c1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-77Fb79ayiDad0grvVsz4/OB55wJRyw9Ao+GdOBA9XywtHpuq5iRbVyHToGxWquYWlEf6WHFQQnFEttsAzboyKg==", "signatures": [{"sig": "MEQCIGZweLamNcoDgJpp37yXzaHnZ6A9ORqkX/OD2gspb8X/AiAKjS8kRL49MMfCPASYrjvyjgdWgLeiB99ZaQZnkzzNMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3032423}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.1.4_1697430869877_0.030431318737713875", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1867e169a32f6d9d302df443dd0fbef25f5b34e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-mldtP9UEBurIq2+GYMdNeiqCLW1fdgf4KdkMR/QegAeXk4jFHkKQl7p0NITrKFVyVqzISGXH5gR6GSTBH4wszw==", "signatures": [{"sig": "MEQCIHWB/uURXTWaclbR+yxELuL5cgf6g/sed76cwUEEqTO/AiAy6MRQ2eCxVFPMQCRJERJc227gAs1eTsnKiUTmCDvdiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3028327}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.1.5_1698485033218_0.8313722547783933", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2ab954da55f354ded9f156f28c05403e3e55b480", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-4Ma7HL5zh72pTg4NevIt12DpPOkN8xd4f4FwnIpgC4gsxraEQExqYNHDtmmsyzFXw90rpcaFw+vH82Hns7E95A==", "signatures": [{"sig": "MEQCIGS00Tlf3Oumj+SpN0rGIXZXkmCufU7dMIc/PctUvC/dAiBAFHkpvyRw77baG13S63rXWIcQjpO1CzYNKbEw16IaVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3028327}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.1.6_1698731136401_0.6976462017618568", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "30fb4756ebf5c7e059a12bdf42d900c38041e04d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-3p3iRtQmv2aXw+vtKNyZMLOQ+LSRsqArXjKAh2Oj9cqwfIRe7OXvdkOzWfZOIp1F/x5KJzVAxGxnniF4cMbnsQ==", "signatures": [{"sig": "MEUCIC3eYacOqULbaH2FSIkLII80MgQm+ldXYwRr64FgNrETAiEAlSTzW2JJUYOa4lmnMaxzKEcWlsqgwtiWhaIkpwNija8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3040615}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.2.0_1698739863444_0.4248674992438426", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "00c1ff131ba16881eb1a0ad46b0aa10dcacb010e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-pw77m8QywdsoFdFOgmc8roF1inBI0rciqzO8ffRUgLoq7+ee9o5eFqtEcS6hHOOplgifAUUisP8cAnwl9nUYPw==", "signatures": [{"sig": "MEUCIQD6i1QofTWVMBh8iLU5NgGZxACSWe7/WJBfRk4FL4Sk4wIgDekG/3RxoEecLcbpIhKO9L1Z64pZQNoDrDZegjVstaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3048807}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.3.0_1699042406709_0.532738616905289", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7781b91f1c032fd1cd5d27a72e9966a44d7e308d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-Q1nbux0VbjeSSYns31wa4r8pssxg/bmYD7kH9ArSfSLxN0OaJaDTaBfHuGC/Ou7dWbg83ca0YQTYHQ6rzZVvgg==", "signatures": [{"sig": "MEQCIGfQUZHtShWIauE8JrROMImgVNUTHzmmIyT4b6VtpX0xAiAbNiiSAeYBPK4KhxgoaSmVmnemDEIVPzkoF/A/cZXN8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3034759}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.3.1_1699689492788_0.35505072780482183", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "18c0c94e44bc778297216eaac93ef49681bc7fd5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-VvpAdh5SgewmWo8sa5QPYG8aSKH9hU2Kr5+3of0GzBI/8n8PBqhLyvF0DbO+zDW8j5IM8NDebv82MpHrZaD0Cw==", "signatures": [{"sig": "MEUCIQCR69EUezgl73EdHrBwQL43HWM2vgI7act8ZDsAnpwoUgIgDkj5Y6bE7Y3Vbs8y2GlVFEsQ63EKH3XbtexKiRMoy28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637447}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.4.0_1699775410756_0.7593702885656457", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e8e8e87ab098784383a5ced4aa4bbfa7b2c92a4e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-mHIlRLX+hx+30cD6c4BaBOsSqdnCE4ok7/KDvjHYAHoSuveoMMxIisZFvcLhUnyZcPBXDGZTuBoalcuh43UfQQ==", "signatures": [{"sig": "MEUCIFR7DhXbK/JhJ3dmWpHIFYoItedDnYR20PXdqpQb1Tb9AiEA4doAbeyMqoN6mL3uO179nQxswzspH1dJOZBwHWrISIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637447}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.4.1_1699939566824_0.9637505152042862", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6929bf3013e9d599605953ea1bc51f35376bfff7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-FVyFI13tXw5aE65sZdBpNjPVIi4Q5mARnL/39UIkxvSgRAIqCo5sCpCELk0JtXHGee2owZz5aNLbWNfBHzr71Q==", "signatures": [{"sig": "MEQCID9OQ0DdAd0NNEwpIno9T1f1NRJTzXKYJ0SmdDhWRQT6AiBfehZg6ZdDThK3SqHmBIFrsDGfj0Ao03PgQGaPFOY/oA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649735}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.5.0_1700286749405_0.41639308571329514", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "079ca543a649b1dcf9832a34dff94ebb46c96745", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-CWPkPGrFfN2vj3mw+S7A/4ZaU3rTV7AkXUr08W9lNP+UzOvKLVf34tWCqrKrfwQ0NTk5GFqUr2XGpeR2p6R4gw==", "signatures": [{"sig": "MEUCIQCfdvTHHvRYmCBj7CHvZDz0ztEQbFjb875iYyoaOcTWhQIgW2PGcIK0Aug3FG/vTj/NwrgO+cy+m86J6n8UzWhfktg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649735}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.5.1_1700597607652_0.9223773287858104", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "566cda292f95d6ef995344887217a82e9fd20ad6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-My+53GasPa2D2tU5dXiyHYwrELAUouSfkNlZ3bUKpI7btaztO5vpALEs3mvFjM7aKTvEbc7GQckuXeXIDKQ0fg==", "signatures": [{"sig": "MEQCIGV12ax2t9HCbBjnUC5DgAmXSJ9Do9LKoB8oDQvvdBqlAiBt6+GGyTvnWEcBijGa6iRiBmT0H6iXWPaXkzm1fNCr0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649735}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.5.2_1700807408664_0.08846799099030922", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e9071050bed7c64a9fd964cde3c8bd139bf8e489", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-rouezFHpwCqdEXsqAfNsTgSWO0FoZ5hKv5p+TGO5KFhyN/dvYXNMqMolOb8BkyKcPqjYRBeT+Z6V3aM26rPaYg==", "signatures": [{"sig": "MEUCIQCxWJiXLy6iGK1BZVQZ5hVPdJvuw/vFXzD1JolzRAu/AwIgDa96i+WIeYwXmSUx5mEkVbzGnLn3PetC9NBO8s87Sd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649735}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.6.0_1701005971018_0.8357469837638283", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f07bae3f7dc532d9ea5ab36c9071db329f9a1efb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-DNGZvZDO5YF7jN5fX8ZqmGLjZEXIJRdJEdTFMhiyXqyXubBa0WVLDWSNlQ5JR2PNgDbEV1VQowhVRUh+74D+RA==", "signatures": [{"sig": "MEUCIBF350UfEhkNPuPLu8+pUQ5h8BLEYlyol2OOjhigIOOtAiEAnOPMfyYVlAofM5ahWCAlpdHyZE0K6+OJbl5F4DCD2yQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649735}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.6.1_1701321808739_0.7527194979287744", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bccd53f20de2c1b1e7024898dc5b69375a5abe4e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-cCkoGlGWfBobdDtiiypxf79q6k3/iRVGu1HVLbD92gWV5WZbmuWJCgRM4x2N6i7ljGn1cGytPn9ZAfS8UwF6vg==", "signatures": [{"sig": "MEQCIBPmlbETIV1UuS5FnrCh7t0SlSvcG/C5LaOvAscKQ0TyAiBs6gEMyLgX149UDPdEhKwhqPvzGgA4DM7BkaO5lCt/3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645639}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.7.0_1702022302328_0.8486798992163329", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "48e09a455d543be986003c7c2ea37c16ff4a53d5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-PmvAj8k6EuWiyLbkNpd6BLv5XeYFpqWuRvRNRl80xVfpGXK/z6KYXmAgbI4ogz7uFiJxCnYcqyvZVD0dgFog7Q==", "signatures": [{"sig": "MEYCIQDH6nMhlqGwm73TAhhRver3bpEXAYqq4/y/+axh0KM8IAIhAPqARtgMSLbcfYi0WLmZLaPw5NO4jgbHn9tcXQhdRZ/u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645639}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.8.0_1702275917258_0.77204307453306", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ece153613f0cf2c864dbfc2076c579da8abd51a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-m6pkSwcZZD2LCFHZX/zW2aLIISyzWLU3hrLLzQKMI12+OLEzgruTovAxY5sCZJkipklaZqPy/2bEEBNjp+Y7xg==", "signatures": [{"sig": "MEQCIAE11+L0wG1Sp93nQacXqS7RY+fF/wnFXxnKJPcd0VsmAiB6S+0xlenTW9oRkBEkVGtxXsYiYcFg+F8Ud2+G7pHrvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645639}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.0_1702459477149_0.18832531491238225", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0983385dd753a2e0ecaddea7a81dd37fea5114f5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-kr8rEPQ6ns/Lmr/hiw8sEVj9aa07gh1/tQF2Y5HrNCCEPiCBGnBUt9tVusrcBBiJfIt1yNaXN6r1CCmpbFEDpg==", "signatures": [{"sig": "MEQCIHrQk8H7elum5ljJ2LPI4628s1tucONVKhnLjJg3JD4UAiAiWeByoYp3VoMHfIJzU+wqHUC0bNkZwRo6+qdqGOxPBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641599}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.1_1702794389643_0.175699353316779", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "db1cece244ea46706c0e1a522ec19ca0173abc55", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-ym5x1cj4mUAMBummxxRkI4pG5Vht1QMsJexwGP8547TZ0sox9fCLDHw9KCH9c1FO5d9GopvkaJsBIOkTKxksdw==", "signatures": [{"sig": "MEQCIA59qA9K5OpFRqBLJH1XGETRlzkDTo6YtmYh4N8ji2fMAiA20yV6nibWnyH1IT6PjfsMwNCXJbYK+D6ZVC1b9sHZpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645695}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.2_1703917424756_0.775976199191601", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d07f3ad71b60b015cfebd2d753cb22459e2e6f18", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-koLC6D3pj1YLZSkTy/jsk3HOadp7q2h6VQl/lPX854twOmmLNekHB6yuS+MkWcKdGGdW1JPuPBv/ZYhr5Yhtdg==", "signatures": [{"sig": "MEQCICdAGb22VbpsrX/zDQRnQlOHP+LDtA6UXwGe20bbH3yoAiAAl5Wn+K5E+g/S1Y8s2iduva4z4SP9c9PFJ0t5gkgfsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637503}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.3_1704435665157_0.14141353579542093", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bd312bb5b5f02e54d15488605d15cfd3f90dda7c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-dIYgo+j1+yfy81i0YVU5KnQrIJZE8ERomx17ReU4GREjGtDW4X+nvkBak2xAUpyqLs4eleDSj3RrV72fQos7zw==", "signatures": [{"sig": "MEQCIH0Ll6Nw/DaI21O+YPaYbPG2IggM90ip/E183vnL0WBkAiB4Ni0bROXHiaTuKSJMzAJSNR6rAqbrFPqUl2pj6KxE6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637503}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.4_1704523158940_0.8145404263401059", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "85946ee4d068bd12197aeeec2c6f679c94978a49", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-Dq1bqBdLaZ1Gb/l2e5/+o3B18+8TI9ANlA1SkejZqDgdU/jK/ThYaMPMJpVMMXy2uRHvGKbkz9vheVGdq3cJfA==", "signatures": [{"sig": "MEQCIDd/D2f8TOZUz6fTv+iryzTpH8R1rVkNscOXlNf5AOmjAiAONtgmIqEmWrNLe5/b7aagrIcWGHbZTXKAr/m/eaQ/Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649791}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.5_1705040191470_0.6049652666787522", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5ac5d068ce0726bd0a96ca260d5bd93721c0cb98", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-HUNqM32dGzfBKuaDUBqFB7tP6VMN74eLZ33Q9Y1TBqRDn+qDonkAUyKWwF9BR9unV7QUzffLnz9GrnKvMqC/fw==", "signatures": [{"sig": "MEQCIBExpXi4JQeyLq4U0fZaiNmQNL/ZNvz5zes5yu59bzvwAiB7/8sVYtCAYDcWJlGEKWWT93vjc6dmEoxqDI0Oy7q4lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637503}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.9.6_1705816357082_0.7406062213941755", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3f06b55ccf173446d390d0306643dff62ec99807", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-w/5OpT2EnI/Xvypw4FIhV34jmNqU5PZjZue2l2Y3ty1Ootm3SqhI+AmfhlUYGBTd9JnpneZCDnt3uNOiOBkMyw==", "signatures": [{"sig": "MEQCIEpJNoIiITxp/qXC9xXnyETRsVP7LAsD2D/QQk1QpqUYAiBuoeufPrrUcvzupnp9b+odTP6SplGJJHF/Pw7vZ/Tqbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703040}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.10.0_1707544741245_0.5587090754304094", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8c318d9c33a89cf9f917a1be90b0d8c75e0ab3ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-6q4ESWlyTO+erp1PSCmASac+ixaDv11dBk1fqyIuvIUc/CmRAX2Zk+2qK1FGo5q7kyDcjHCFVwgGFCGIZGVwCA==", "signatures": [{"sig": "MEYCIQCkFKlcW99gDCqhC06EpfWX0xSXWyOwMn/o0KE5M/bf7QIhAKfGGddNP9SYs9Ia0GHR3ORkpfDb4M5ypvjZ1GU0reZT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703040}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.11.0_1707977408488_0.2989732033075303", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9d62485ea0f18d8674033b57aa14fb758f6ec6e3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-TenQhZVOtw/3qKOPa7d+QgkeM6xY0LtwzR8OplmyL5LrgTWIXpTQg2Q2ycBf8jm+SFW2Wt/DTn1gf7nFp3ssVA==", "signatures": [{"sig": "MEYCIQDu2l5hY6wT8ePojfhzyfBNxoRK8ugUO1feWp8MtyHysQIhAPJ9I0cMVMUvEpcxKAQEYg8g/MpBg8iDrSWYmwWGZSsN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2731712}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.12.0_1708090386072_0.5260578970321863", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "34a12fa305e167105eab70dbf577cd41e5199709", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-nD3YcUv6jBJbBNFvSbp0IV66+ba/1teuBcu+fBBPZ33sidxitc6ErhON3JNavaH8HlswhWMC3s5rgZpM4MtPqQ==", "signatures": [{"sig": "MEYCIQC53N1SmMaBdcmnIu1HBRDbeDkKc+db3CyQKyrtUnh0iQIhAI/vuSyjOIqhrY5qD4cvJIvnCu/NGZJQzndkJpzy3Y2D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715320}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.12.1_1709705041907_0.9287012628757141", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f672f6508f090fc73f08ba40ff76c20b57424778", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-yUD/8wMffnTKuiIsl6xU+4IA8UNhQ/f1sAnQebmE/lyQ8abjsVyDkyRkWop0kdMhKMprpNIhPmYlCxgHrPoXoA==", "signatures": [{"sig": "MEQCIHEGYEXxbeqNSKhyvTTYJ5TpEJtRbgWMqO/k5TA4cMvKAiBxDCf2BTk9BK4Z3Z6CceSgIpcjlTmTt0uub9vnoMuv2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711224}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.13.0_1710221346976_0.46214896132521077", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0c3a77aee18160a6c298d82920a2ee809439761e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-YTPsM+0QYbtMDvqVkPjN5k4gA/iIvi+NkT0YZTZrcQ6lovTKPHDnnC/Vkz5NGMCTTeBCtb0m7rlef95aVvKu0g==", "signatures": [{"sig": "MEYCIQCnNT4oUMZ3mdQcPVFtEAdeyUKcCEWnAVy6bxfeQil6JgIhAPPYqVDg1rJc4psIKFSOELGT/3lUie7l5IfupaAhZtDe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727610}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.13.1-1_1711265991111_0.2426966032651663", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "836f948b6efc53f05f57d1d9ba92e90d629b3f22", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-zGRDulLTeDemR8DFYyFIQ8kMP02xpUsX4IBikc7lwL9PrwR3gWmX2NopqiGlI2ZVWMl15qZeUjumTwpv18N7sQ==", "signatures": [{"sig": "MEUCIQD/jN/Coz9O/vPpbZutKiQ77yNBIGExO8w67Eo9VtW9sQIgak9x65vUkavy1EjtonbRakjPTYGLS/rUh1zhxhGHgDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727608}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.13.1_1711535289610_0.7543176319596119", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "270e939194b66df77bcb33dd9a5ddf7784bd7997", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-xXMLUAMzrtsvh3cZ448vbXqlUa7ZL8z0MwHp63K2IIID2+DeP5iWIT6g1SN7hg1VxPzqx0xZdiDM9l4n9LRU1A==", "signatures": [{"sig": "MEQCID3qUxzfyp0YK1OVvH+ygnm+bzmPXQz9xQJYaGTrdV+dAiBxJLQnVM+AgfGEMv/P2GoOq/lfQ9QsKJOChvwKn0beDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727608}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.13.2_1711635250899_0.48112640953648134", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "820ada75c68ead1acc486e41238ca0d8f8531478", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-C6y6z2eCNCfhZxT9u+jAM2Fup89ZjiG5pIzZIDycs1IwESviLxwkQcFRGLjnDrP+PT+v5i4YFvlcfAs+LnreXg==", "signatures": [{"sig": "MEQCIDS+4pmUaGdsxx7oks+Tm2zi5zUSCXIb2ErY3Oy9F66MAiA78vr/VlPJQ53QJImZdBxo0cSYPTy19Apcl7GfraIsVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711224}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.14.0_1712121814836_0.7570502591902637", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0aaf79e5b9ccf7db3084fe6c3f2d2873a27d5af4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-9Q7DGjZN+hTdJomaQ3Iub4m6VPu1r94bmK2z3UeWP3dGUecRC54tmVu9vKHTm1bOt3ASoYtEz6JSRLFzrysKlA==", "signatures": [{"sig": "MEQCIHf1NlBYX/5ht6cAB1qm4EDGCHMw3RnSOAGUVc37rAl+AiByFYiZV40hOOsIrKRhs4f20bzKfGc0865EFu4fL1VdXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711224}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.14.1_1712475363833_0.07364732368914884", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2ab802ce25c0d0d44a0ea55b0068f79e462d22cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-QBhtr07iFGmF9egrPOWyO5wciwgtzKkYPNLVCFZTmr4TWmY0oY2Dm/bmhHjKRwZoGiaKdNcKhFtUMBKvlchH+Q==", "signatures": [{"sig": "MEUCIQCVa1PG0EKn07bbzLXvOegRLixrXrIMU3kMJZmUYEbQQwIgAcK4D3MikAIxo6QCQgWugES/bjVKOwM2SXljzkLScWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703032}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.14.2_1712903044942_0.5739430723314074", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "197f27fd481ad9c861021d5cbbf21793922a631c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-8ybV4Xjy59xLMyWo3GCfEGqtKV5M5gCSrZlxkPGvEPCGDLNla7v48S662HSGwRd6/2cSneMQWiv+QzcttLrrOA==", "signatures": [{"sig": "MEQCIAzQWGh7CrKHnfe2YkhKbpc2D9CI2EII/CAoG38kpxosAiAh/d5eWY7jqtb82q7Zx2oyO+f+Bz8xyAlBAHPLs5Wojw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727608}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.14.3_1713165537644_0.180872558385073", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "25c1fb87b2255949ee7ff6956e205710c5d7c414", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-511qln5mPSUKwv7HI28S1jCD1FK+2WbX5THM9A9annr3c1kzmfnf8Oe3ZakubEjob3IV6OPnNNcesfy+adIrmw==", "signatures": [{"sig": "MEQCICxCz1fY0xOwpENw1vFhMQikU0qAPZJ9W3Me/dOuFgasAiA/wS8M8K2yIDCp6Fc/uzaRgvsh1i1lj/pBq8c0cOWkjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805432}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.15.0_1713591464601_0.7235925970598003", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "41b43abe9073cfa4a02130e4d050fa3b10f82199", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-SjsBA1a9wrEleNneGEsR40HdxKdwCatyHC547o/XINqwPW4cqTYiNy/lL1WTJYWU/KgWIb8HH4SgmFStbWoBzw==", "signatures": [{"sig": "MEQCIDEGrRVhKVzCsxt8Z7J+thAkRjHgEYnmdXtr6R9HfYqSAiB2ol+pQSEsJiVIFc2CTs5R3lF/vL/bj3ypZa+8JhbUtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805432}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.16.0_1713674579357_0.5370323047228232", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d0c03203ddeb9454fc6fdde93a39b01c176ac6d9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-N2ZizKhUryqqrMfdCnjhJhZRgv61C6gK+hwVtCIKC8ts8J+go+vqENnGexwg21nHIOvLN5mBM8a7DI2vlyIOPg==", "signatures": [{"sig": "MEUCIFhyQ1SxhOl86MwzCPWDA7rfaGOu14DCBOvmuuB5U9zDAiEAynBEVw0B5kOZX9hdcLOqSPxjcdc+C29CATq6sn3Xe1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805432}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.16.1_1713724234090_0.6544133908051313", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "30b88169db18dec202ab9662d5148523d59da553", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-oc5/SlITI/Vj/qL4UM+lXN7MERpiy1HEOnrE+SegXwzf7WP9bzmZd6+MDljCEZTdSY84CpvUv9Rq7bCaftn1+g==", "signatures": [{"sig": "MEYCIQDZnyC3/pbWWwMxG0YLbOGkHDJx9PJpwUvAwmo0HDoo2AIhAMEk32QV/GTvdwRod75zUJT1pDfa08IYKPoQv6GTDZUs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805432}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.16.2_1713799191998_0.6243750847605929", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "166c92e54d93f09100fb404d907e22852a41f280", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-n4HEgIJulNSmAKT3SYF/1wuzf9od14woSBseNkzur7a+KJIbh2Jb+J9KIsdGt3jJnsLW0BT1Sj6MiwL4Zzku6Q==", "signatures": [{"sig": "MEUCIGzLWSXCNhNuOJY6uNiwaVTUmrDBP/PraddrdEc3T/++AiEAzfoU/AytznMyC2hCVG7ZyTRIYPJU/tecxUMymPz1nnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805432}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.16.3_1713849180459_0.32368142316144954", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6356c5a03a4afb1c3057490fc51b4764e109dbc7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-LuOGGKAJ7dfRtxVnO1i3qWc6N9sh0Em/8aZ3CezixSTM+E9Oq3OvTsvC4sm6wWjzpsIlOCnZjdluINKESflJLA==", "signatures": [{"sig": "MEQCIHs6WA3hiPv3rtrc256xrX+UZdLRMN4gbtLHyTVOTCW5AiA64spMcJvBM255yP+NIMo2MCWvCR8zC98+pfwTVp2PCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805432}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.16.4_1713878133156_0.2927722518546092", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "854122ced306100a0f1bfe9085dce98360554efa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-v6eiam/1w3HUfU/ZjzIDodencqgrSqzlNuNtiwH7PFJHYSo1ezL0/UIzmS2lpSJF1ORNaplXeKHYmmdt81vV2g==", "signatures": [{"sig": "MEUCIGDJXO49jcTPzEyADpRfUNaWbdMhTMWHyIPpi8gHN2ICAiEAs82t0kaHFdOIzdE/4oHKnGTHZrivdh5SiNm2hq/QgGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784952}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.17.0_1714217418386_0.31077172946653175", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "61fbc6580b972893c1e74ac460d41f6ca4143482", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-AsdnINQoDWfKpBzCPqQWxSPdAWzSgnYbrJYtn6W0H2E9It5bZss99PiLA8CgmDRfvKygt20UpZ3xkhFlIfX9zQ==", "signatures": [{"sig": "MEQCIC9fgtmY039tCeY2PWhXFPagJEcADJ8kU2UpDGBBYdKpAiAHbH/ef6Ib2CVvVsiqcr+3VJKg+WNq+4fUM2ETlQoFRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784952}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.17.1_1714366699473_0.5718352371239914", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "b521d271798d037ad70c9f85dd97d25f8a52e811", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-Hy7pLwByUOuyaFC6mAr7m+oMC+V7qyifzs/nW2OJfC8H4hbCzOX07Ov0VFk/zP3kBsELWNFi7rJtgbKYsav9QQ==", "signatures": [{"sig": "MEQCIAHrcq2CZplXA8qB3fYf3WoVhbOSTYuoNREVoY4WeUTnAiAI2dVAwPUsdC65FPydEMFFA0jqCuNDTsMksgQdvDusjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784952}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.17.2_1714453279803_0.99343356975288", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1a7481137a54740bee1ded4ae5752450f155d942", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-xuglR2rBVHA5UsI8h8UbX4VJ470PtGCf5Vpswh7p2ukaqBGFTnsfzxUBetoWBWymHMxbIG0Cmx7Y9qDZzr648w==", "signatures": [{"sig": "MEYCIQC5CpP3UIsyZp0DhjSm6GEBCIRlBNBH3THveEbfBTpM0wIhAJnt1QlWljKwVk6NLYCNx992+FFHFGx+dCbopRc3bdue", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2793144}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.18.0_1716354253793_0.6430358980788959", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1e781730be445119f06c9df5f185e193bc82c610", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-7O5u/p6oKUFYjRbZkL2FLbwsyoJAjyeXHCU3O4ndvzg2OFO2GinFPSJFGbiwFDaCFc+k7gs9CF243PwdPQFh5g==", "signatures": [{"sig": "MEUCIG8r5ljDTBYcOxfc77lKPepEpjrCxmnzHijqdVMQuKNUAiEAoMa61cbqtEenWRVqWxw/61bIzSfSlK+X4cXgXuJ78Jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2682576}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.18.1_1720452341112_0.8401936751694277", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f0282d761b8b4e7b92b236813475248e37231849", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-HBndjQLP8OsdJNSxpNIN0einbDmRFg9+UQeZV1eiYupIRuZsDEoeGU43NQsS34Pp166DtwQOnpcbV/zQxM+rWA==", "signatures": [{"sig": "MEUCIQCsHcB1udx5f2Gu03Xild8EFe3gpTzOF+9mwSYVm1sVbQIgYU0LYgmS3lxAf8hF0QOPdkIHf9aeFN/ZE/7ZLIobwrM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2633424}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.19.0_1721454402686_0.5292179019802024", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0af2b6541ab0f4954d2c4f96bcdc7947420dd28c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-XUXeI9eM8rMP8aGvii/aOOiMvTs7xlCosq9xCjcqI9+5hBxtjDpD+7Abm1ZhVIFE1J2h2VIg0t2DX/gjespC2Q==", "signatures": [{"sig": "MEYCIQCpro7MGHEj2YUEK+ekBQXomxarw1LXi26qGbB/ezZf6QIhANdiXgdBs/3WXc0JuL/1hjBBcVaNHDifjmCmw0iFp2Je", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2629328}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.19.1_1722056067347_0.3914817028682551", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6a275282a0080fee98ddd9fda0de23c4c6bafd48", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-OgaToJ8jSxTpgGkZSkwKE+JQGihdcaqnyHEFOSAU45utQ+yLruE1dkonB2SDI8t375wOKgNn8pQvaWY9kPzxDQ==", "signatures": [{"sig": "MEYCIQCOe1WJS25Nn9Xm61badyjSS9anSQ3Ar9gdt07IsQzVnwIhAMmKizXKg5mIKMYGJPv0FY47VCAoNQgnsRhfmLjYvlxq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2547408}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.19.2_1722501201556_0.1421330407696335", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "cc1f26398bf777807a99226dc13f47eb0f6c720d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-y+eoL2I3iphUg9tN9GB6ku1FA8kOfmF4oUEWhztDJ4KXJy1agk/9+pejOuZkNFhRwHAOxMsBPLbXPd6mJiCwew==", "signatures": [{"sig": "MEYCIQDjz2kpgJHsRDC5nwFdeOX80ATz6pKU7Y4vimTpsAdstgIhAJR23eFWY0h+XilWWSUd9vwKmUewH4slgaQS7X2V3XR5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2547408}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.20.0_1722660559097_0.653751044029268", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4fd36a6a41f3406d8693321b13d4f9b7658dd4b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-e2hrvElFIh6kW/UNBQK/kzqMNY5mO+67YtEh9OA65RM5IJXYTWiXjX6fjIiPaqOkBthYF1EqgiZ6OXKcQsM0hg==", "signatures": [{"sig": "MEUCIFIOMAtN1QyKpBYG3jrwn2IDHToN44kW2kYY5YxHOvoaAiEA+ZQzagnUD7GrFiPup8KdyPzdOwHo7zUJNAb6N5w33Jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477776}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.21.0_1723960559278_0.8455898220757607", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "790ae96118cc892464e9f10da358c0c8a6b9acdd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-kXQVcWqDcDKw0S2E0TmhlTLlUgAmMVqPrJZR+KpH/1ZaZhLSl23GZpQVmawBQGVhyP5WXIsIQ/zqbDBBYmxm5w==", "signatures": [{"sig": "MEQCIGCjLTAuT8ll+yiyoOhlsctMH+f0J7H+B3gJXrXoLr00AiBANRaR6s7Z2bLIePmHJ8y4hv0pzdjwfQvzEItQkQDsoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477776}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.21.1_1724687682272_0.9632480683458526", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0dadf34be9199fcdda44b5985a086326344f30ad", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-B90tYAUoLhU22olrafY3JQCFLnT3NglazdwkHyxNDYF/zAxJt5fJUB/yBoWFoIQ7SQj+KLe3iL4BhOMa9fzgpw==", "signatures": [{"sig": "MEUCIEgJbV98dQBzABS2esL1MR0L4u3eic07Cd3aZl4ts5r6AiEArZ+ulSsszQV4Mte5aw6HGkdyt0WgEuxItZshTJQQ9Xs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481872}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.21.2_1725001493427_0.11038153516848737", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5226cde6c6b495b04a3392c1d2c572844e42f06b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-kZPbX/NOPh0vhS5sI+dR8L1bU2cSO9FgxwM8r7wHzGydzfSjLRCFAT87GR5U9scj2rhzN3JPYVC7NoBbl4FZ0g==", "signatures": [{"sig": "MEUCIQDi0+zqjZLDo+4skz5XXc30zOw0cBiGJvV4AMpHBcVAwAIgR8aQGJLqJBZKgCAB26CgtDxvFmHM3IBwlHwHm41pnZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2473680}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.21.3_1726124779127_0.6415861713493787", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d731a19af5f05eabcba871bda2eeb2fa8c8adb67", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-tsSWy3YQzmpjDKnQ1Vcpy3p9Z+kMFbSIesCdMNgLizDWFhrLZIoN21JSq01g+MZMDFF+Y1+4zxgrlqPjid5ohg==", "signatures": [{"sig": "MEQCH1ocOVm31Kukmu1GPv+nyhONd++ezm/zh23Cv546aOACIQCY5ncCTcL/zKhzeojpiSBNQqvsUBjxFNcAEx719+W/PQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485968}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.0_1726721759350_0.8068063764431563", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a46f8e3bc77cd87b1aacb83cf52cd62a59135be8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-ot1DPlQZGGiZYNyE/PF3jbT6juuG0W5oiguHQEvjoZZ3+FSxMfdJnBz1P71QeqICSOlSFG9Z31oA/uXyuxDEVw==", "signatures": [{"sig": "MEQCIHgPHc0PtQnTJmfx+MDnz4z3JfpPrtkxeAAWb//+CExoAiAeJHc/mBIJXLcv0SLJ/GUWFVoDcCYhcykZUMG7Rkiu3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485968}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.1_1726820540402_0.34381814341334294", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "be182ef761c9b0147496e647ace44fd1b912344f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-a0lkvNhFLhf+w7A95XeBqGQaG0KfS3hPFJnz1uraSdUe/XImkp/Psq0Ca0/UdD5IEAGoENVmnYrzSC9Y2a2uKQ==", "signatures": [{"sig": "MEQCIEl4ewQMgRmBP66J+c7mKOgcY85iTV4VX7vHa4JRWH6sAiAYpZX5hHM2BNf5QrtJif4tunTszaw6J+ojMtbu8gAWug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485968}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.2_1726824853379_0.9638043698806187", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "67424426cf86000ea3e112a08cb164d25a532d75", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-Ip6wSbRvZ4a3d+bD3TCEV42S4xtLOkAzrahXdrqnvPPGf9V8v+Iszp2ta3hIvgnZVnLUucw37JcQvSFZF4tytw==", "signatures": [{"sig": "MEYCIQDtqkLA4TtqwrTSH9kFiBGIdjMQ9Gl66n2dTAh3pmLIUAIhAO0jmyXJl9V6t6ddxzNnUAC+9VKnxas+pauB+r2o2WuG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485970}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.3-0_1726843707679_0.16576023339270818", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8363eb345b75c3058cb3181b802cbe303ef9fcc9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-QWAgO5V7E6n3w4a1H7qrqA7k4IDqGYjT/P/wTV1b5o3wifLB1sYmuXw1urw4HWP3KmZxIKb5uEwZPW6Ec4l9DA==", "signatures": [{"sig": "MEQCICgfKq9D1O1TCKq0Xe74S6H30ASs5rpkdP8DGZoz2LG7AiA8CgDuF/bzKbZPeSjKL9tn6RXpA0SF0zTp2oPNnTXrdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485968}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.3_1726895019082_0.3783706715566948", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "70116ae6c577fe367f58559e2cffb5641a1dd9d0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-87v0ol2sH9GE3cLQLNEy0K/R0pz1nvg76o8M5nhMR0+Q+BBGLnb35P0fVz4CQxHYXaAOhE8HhlkaZfsdUOlHwg==", "signatures": [{"sig": "MEYCIQCQhsKBt2dgnMRWnHgs35YYvf1ZdTwtHGRjcyPGHcz+6QIhAICxeNhMNG/AYXf2nPPaEl4xbmimZDYg2oEXx37H45+H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485968}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.4_1726899108443_0.41182692765808726", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a135b040b21582e91cfed2267ccfc7d589e1dbc6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-N0jPPhHjGShcB9/XXZQWuWBKZQnC1F36Ce3sDqWpujsGjDz/CQtOL9LgTrJ+rJC8MJeesMWrMWVLKKNR/tMOCA==", "signatures": [{"sig": "MEYCIQCc7FUd45se/st5PPCsVRKLvkAXRTluU4r+WMsT4rsB+gIhAPemNToo4+8fG8GUUhobLSQktgXdvd116W2WhHQr83WJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469584}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.22.5_1727437726959_0.5596711370120484", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5d813f8fa79830e13ebeb69433cc786c5522da87", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-I/eXsdVoCKtSgK9OwyQKPAfricWKUMNCwJKtatRYMmDo5N859tbO3UsBw5kT3dU1n6ZcM1JDzPRSGhAUkxfLxw==", "signatures": [{"sig": "MEQCIGhjbZG/ATzOrsV/T9S8lO8yDgucZdriIIS0Fyt5aI12AiBtZClkTVRiTxxI6uFat8YqMz58mLhazvdVfnifNyNgPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2449104}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.23.0_1727766646375_0.14385604221887016", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "78144741993100f47bd3da72fce215e077ae036b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-ZXFk7M72R0YYFN5q13niV0B7G8/5dcQ9JDp8keJSfr3GoZeXEoMHP/HlvqROA3OMbMdfr19IjCeNAnPUG93b6A==", "signatures": [{"sig": "MEYCIQD6TypuoX37MlaF3RHffWIHRt3RYNE6oawCJYRDbrimWgIhAI8gcilbPBE/Q3kNOqSH1uoRFMHhjgdqKYWPLx6HnV2n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457296}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.24.0_1727861870816_0.3935419841703134", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "92078c050e652fb527caf7b8e21485269cf5c8a2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-j0RPQWteEXAAxRQI+IcX3i7WQb7hFe7CW94H3l0edBVyJMIPOlr/hqc5CGG1FBDW9gNr0ZC2IzwSta1iSNJIoA==", "signatures": [{"sig": "MEYCIQDUqbuxRDm2mLSmnrli8lMedSQKgzGRO1iVXs1R+cGi7QIhAKbgWxpDJvaSI1mZC4gG9YcUGu/FxfpUW1untOGz6ini", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457296}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.24.1_1730011418357_0.3087313373737517", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4cc3a4f31920bdb028dbfd7ce0e972a17424a63c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-jOG/0nXb3z+EM6SioY8RofqqmZ+9NKYvJ6QQaa9Mvd3RQxlH68/jcB/lpyVt4lCiqr04IyaC34NzhUqcXbB5FQ==", "signatures": [{"sig": "MEUCIQCeSXXg9kFMfufYVrQtLGFqfckhyO+Z2SjNVTUUg2likgIgXmz+hqSmm6cl/wq3jJyFQhVunc26YQ6WNr/HkfOX7jU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457296}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.24.2_1730043645359_0.8228832384587237", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d39d0516cbf8f3de0caf31c6efedeab434970e20", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-VgnqWO6cz0R5EfhLHXpiSn3EfrZHCf3ZKM2iPZq2/EB62kq7ooRRGehrKxDjahFCLUOQ5jeO41Q/hK3Vntrz2A==", "signatures": [{"sig": "MEQCIHOf4m+5CZxQcGzAB94GJmX1egDagi1XqCK3ajhU/d90AiAxHEbA8mvzRIyw3j2Ja7HY8+2hzi2xy7BEzRH1wGQZMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457298}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.25.0-0_1730182547222_0.4764192524181683", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e86172a407b2edd41540ec2ae636e497fadccff6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-mnEOh4iE4USSccBOtcrjF5nj+5/zm6NcNhbSEfR3Ot0pxBwvEn5QVUXcuOwwPkapDtGZ6pT02xLoPaNv06w7KQ==", "signatures": [{"sig": "MEYCIQDbeJ4UzIxMheFPQbqhZXLhZML/0f/D8rHEBnBAlffV5QIhAIbI4yfLCb6QCCgxUe62o+SqtqiVhQtM62L9BCTctoPh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457296}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.24.3_1730211285741_0.6367537731383637", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e473de5e4acb95fcf930a35cbb7d3e8080e57a6f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-K03TljaaoPK5FOyNMZAAEmhlyO49LaE4qCsr0lYHUKyb6QacTNF9pnfPpXnFlFD3TXuFbFbz7tJ51FujUXkXYA==", "signatures": [{"sig": "MEQCICMI+/vOeqTlrdqVLSQm5hnVWuKJ3Ne/52E9+AkTtWssAiAq1xLB6nvpXKvhvHT8kBZIouPQ1mm+6cj3bZ5PS6Tr1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2506448}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.24.4_1730710063597_0.40622971028224475", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "dfcceebc5ccac7fc2db19471996026258c81b55f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-ThcnU0EcMDn+J4B9LD++OgBYxZusuA7iemIIiz5yzEcFg04VZFzdFjuwPdlURmYPZw+fgVrFzj4CA64jSTG4Ig==", "signatures": [{"sig": "MEUCIDCZDLDbxsYB1a1bJbo70wJHNHVtNXK5AjfcMzPtGAfAAiEAx6Vkh9ONqoBr6KC2koAn6clqwCHnT8wKkpIcJRzNESQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2490064}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.25.0_1731141478897_0.23294152979128024", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9909570be5cb738c23858c94308d37dde363eb7e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-ZuwpfjCwjPkAOxpjAEjabg6LRSfL7cAJb6gSQGZYjGhadlzKKywDkCUnJ+KEfrNY1jH5EEoSIKLCb572jSiglA==", "signatures": [{"sig": "MEUCIBRfDUSRIb4fyqoeShB/z9siw8DfzLbMMp/xuosX/RLHAiEAmGm1/dq3YNd47ZAxTODEMbmY23v3D9gj/SstGQ5sOaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2490064}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.26.0_1731480335021_0.20176344616615216", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "57794d95a6233741623b809b891f8f9b054253ef", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-RALW2OmKnu7cxm4tK7Y3xC4tEILtJ/IL+25T1ddmZoqo0fAxei3xpzW2SMYWKs/P2rMKCZ5HybjxLr68wpF3Tg==", "signatures": [{"sig": "MEQCIDdUZR/zdwsBHwY8+KuktqTWu+rm85fbrLJL78NPqRbWAiA5scmf9nRIIeLUtD6QJJruaMcJLpzU5kM7qD61Ef3YIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2490066}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.0-0_1731481429305_0.2081816424028129", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "890bc5e4dccbf57fa2aa723f7db0fb1bbf68582a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-WfY8CTHFy7gQZpyz3bDq0nVgHths9Q3p6xOXxvNmIGmr4JLIe6zm3rA2zATf6unSY5Fk66bh0pE66oKhTLOTkw==", "signatures": [{"sig": "MEUCICDx5To7WtibODn67/ZQusoDd0Sm5fYy5jGFfl1K1ogoAiEApwkNsxjGXrUxb34fzAiZmfFScoxpgjOWCmsiPZRBZoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2490066}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.0-1_1731566026123_0.9145382766117156", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "b94de8d9f3fe3365496214b1e1f8240407e220c7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-CNnqMZ4Yz0Ga0A75qux7DNChq0P9oAWn2S7yjZPRC+AaEF8Ysw5K/1lzT25/a3reJ4V2abcShIVG+tfZHb1UrQ==", "signatures": [{"sig": "MEUCIQCHa/oyqRjYk0WQa/fQHHCu7GL4fjD/3f6O7ejXJY7jrgIgM6dok/CDYDxWO+mGDcoXU2hJoCHfVyhu1hgLNb+TH3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.0_1731667270521_0.6304724441192995", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0c30e0703c5796490ed864b17ae17223c6708213", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-4doaJJJa5h8V+cPmJL7tdNfripHZAVJZFhY8a6DwLmwRvHvXvmL5f1MmsnyxM4d16MSX5oyVxfDIG+z9sAbO5w==", "signatures": [{"sig": "MEYCIQCaLGvIEM3nRzBk477+/ymit+SQqJ/KZBbzXJsWr/1CXAIhANOXo+qxy34wA2S0a6T+kUdKWjChTxO0jdLMIRsiC5nl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502354}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.1-0_1731677325927_0.4511723935812251", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e9fc5421be22a86ca4a8159b45d540e36045c6ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-SSyDpmGNL+NtIHp0VzyWnY6YNYWKvkJXEAIcUZH5eRJMDIL1rNjiBrwDHbUXkceRVrs80myhtvxsCH6FiUpxNQ==", "signatures": [{"sig": "MEQCIGv4QM2bI73hMITOwF9fBhDl7iOEE6gOG+R/rw4hZpxrAiBy3mjdsswPMg0kuO0hzMq3uFdydj2szGgmo3FODTL2AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502354}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.1-1_1731685120572_0.5100789327530895", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9d3f6ed43923f0caffe5f5e03f35eedfce7234d1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-8/s+Qj8bGaE03YqfAbS4SI1imWVJj0NvP/828FO8qGu7nS6b0ur7n+PcM8UOU0+lzSgcO/aUk97EXMaPkegGDw==", "signatures": [{"sig": "MEUCIQD3lY7SN/rXxIzZSUz9aX5h4EUJV+OscMRGqOY7BSWg5wIgEsSZxH0vjxMCuI2KobvKsiHXmUk0KUALu9vx0NaaCS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.1_1731686898832_0.5094388372371743", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8c2df25367b4814727d32d74d7028f901c6288b1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-PaW2DY5Tan+IFvNJGHDmUrORadbe/Ceh8tQxi8cmdQVCCYsLoQo2cuaSj+AU+YRX8M4ivS2vJ9UGaxfuNN7gmg==", "signatures": [{"sig": "MEYCIQCZQYzukS+JZRQoj+SSlENTCULl9uxBkMPH8nrlkn4xbwIhAMYvP4ctp4TtHY0+c8FPQ4/qGAz3wIxU86/+sS4qJVl+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.2_1731691242664_0.7079696162143869", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f13effcdcd1cc14b26427e6bec8c6c9e4de3773e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-/6bn6pp1fsCGEY5n3yajmzZQAh+mW4QPItbiWxs69zskBzJuheb3tNynEjL+mKOsUSFK11X4LYF2BwwXnzWleA==", "signatures": [{"sig": "MEQCICgUC/PV3N25p/a0AVYVUgqGuTXmgfLbFvy3abbtyjG6AiAEmtq8cENjP4SiEfhwe+1kIiJqwiN1Y0/NWJlciNcl8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.3_1731948013255_0.7608121849424119", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a959eccb04b07fd1591d7ff745a6865faa7042cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-Ni8mMtfo+o/G7DVtweXXV/Ol2TFf63KYjTtoZ5f078AUgJTmaIJnj4JFU7TK/9SVWTaSJGxPi5zMDgK4w+Ez7Q==", "signatures": [{"sig": "MEYCIQC2C70O26QEtLnH5YxCfT3OIYlvsUBUn10+7IEElXp1WgIhAJlCug9bxEHGp7JjYICd/qdVGyaPOCBQMahMtw7eNIJa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.27.4_1732345254477_0.9985537608710715", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "065952ef2aea7e837dc7e02aa500feeaff4fc507", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-Nl4KIzteVEKE9BdAvYoTkW19pa7LR/RBrT6F1dJCV/3pbjwDcaOq+edkP0LXuJ9kflW/xOK414X78r+K84+msw==", "signatures": [{"sig": "MEUCIQDnu/ENyWbjr2H2UJjIo7FKzbffftgY1WkdGP62xfjKSAIgd4N3BKDJX67saYhReGEEoh5Erl42Pz39g5xcpQpVaAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.28.0_1732972583021_0.42630530213491213", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "b83001b5abed2bcb5e2dbeec6a7e69b194235c1e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-fzgeABz7rrAlKYB0y2kSEiURrI0691CSL0+KXwKwhxvj92VULEDQLpBYLHpF49MSiPG4sq5CK3qHMnb9tlCjBw==", "signatures": [{"sig": "MEQCIEKZejOrHUeuHYfYQKlYjQJsuXJjefiljZ7Wq80d/giXAiAH33EXik8Il6j7ZUeZockW9C4Rg67wZ4PklbCCBJVJuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2490064}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.28.1_1733485535145_0.4744706348001917", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "29d458048ee84c8ba17a98f84ba36a23f37831bf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-rGB/HtPSGV7yKTPVKA22vn7KaE3layXG1OepVlqRMnUeOglAy87/qTLtbOc41B+Q/LjXtC8fH4/0PljDYHB2VQ==", "signatures": [{"sig": "MEQCIEO+xwZuLYoCNKTcbmvAvwanHrnLiYnBcVV+XiEugtOEAiBW+jzvUQQu5HGS7q5WA3C+Orw+G0pGyYrgtKjR6qXqLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2494162}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.29.0-0_1734331232850_0.4103594105738049", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "dcb31f597070fa13a7a4885cd80d2f81236b7d06", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-bic9KXaJ5XoakraGp9fiUoKw2uQ26leSd8QwAgqxbIGXG1e2MZ5s+ZP/NNWG652ziMR4LJ0OyCT2woGN0E9fUA==", "signatures": [{"sig": "MEUCIQCVaXWB/oPRRuZ8sdPBJn4txl5nYqaXeCsZpAvxiW+sfgIgYAYC1mS31tK21OD4zLmFHkNkSnEBRgZ3sKSyA4rRbqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2494162}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.29.0-1_1734590287029_0.6042192640917823", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0ef44ef0216f0202c74fd127d0517de0290a564b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-JJ9pB7ryCno6mJKoGICrdApyND0vyNJ72H50N3Wek8rpQ7M0Nc9lePaYOExZQTuRt0uqs0Elzfv3nZJPtsp3+w==", "signatures": [{"sig": "MEUCIQDteAdJ3MNRmw/Ls12fXX+h1QshoGtmVAhZXWaWjNUK/AIgGFPbdf4xpmlFk2CxPCs90myJ2zhswiDZdp06E/vPLIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2494162}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.29.0-2_1734677802191_0.4225929799180732", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bff4d366993747af0a17a6488bb295885e32275f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-qh0ussrXBwnF4L07M9t1+jpHRhiGSae+wpNQDbmlXHXciT7pqpZ5zpk4dyGZPtDGB2l2clDiufE16BufXPGRWQ==", "signatures": [{"sig": "MEUCIEysN0mf3pTQ7jL4PhgV3rHoeffVGqRyuRk99IgKPcknAiEA1zFomok3vzK6JRWxJL64stXVmlJ7ShmBCZ3pfq+PlUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.29.0_1734719883191_0.3557573603285509", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f4c95b26f4ad69ebdb64b42f0ae4da2a0f617958", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-87xYCwb0cPGZFoGiErT1eDcssByaLX4fc0z2nRM6eMtV9njAfEE6OW3UniAoDhX4Iq5xQVpE6qO9aJbCFumKYQ==", "signatures": [{"sig": "MEQCIHr9KntZt+r1X6rNCIODLsefYRGqY67lSASRzKjyXpHcAiB+tsizde2Wo3OaV+jNXUMU6e1uOP4km6MG1FmbNv3dSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502352}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.29.1_1734765402281_0.6338187002907865", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5e6d97176b26d7045d0b4531a0d01e76aaf8e37f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-QVLsSZWAHYgfsJPj7oHb10KwsShM3VCbcEZvaYRHq4z5xnda83tVmtV72LGRoeOb1QN1QQfyC+4IacS8rBm/Rw==", "signatures": [{"sig": "MEYCIQDRUtquKD6wdxccVJFwFSNnLHHtXdD7imkwrpDVPpZoZAIhAJxgGHVfUsDtcvJ7eQ/v36zFO3XJqDPAXHwzqeRQBkMP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2502354}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.30.0-0_1734765473788_0.7936178449790914", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "de2194a578faa6f37128d1e1fa9b5c1410361962", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-oPfeNoz/M/VTKk1RYRFh1KDzeiKHhR4aNSdxXslbncWF0BLjzj0mYtke/jzYsDZkqL74OGUI45bMqBdYthYxvw==", "signatures": [{"sig": "MEQCIGlAPdJUqeEyhXH5IaZMREQqL8Zv9F4g5A850fLdB8jWAiBrqvMQvAbelVUT2cBwHv0g9L+uLdtAq1ed/vbPtrGyDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498258}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.30.0-1_1735541576694_0.2894029042450945", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ae27f8d26c02d8ce6f84275860e99678b9f3e932", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-ckBBNRN/F+NoSUDENDIJ2U9UWmIODgwDB/vEXCPOMcsco1niTkxTXa6D2Y/pvCnpzaidvY2qVxGzLilNs9BSzw==", "signatures": [{"sig": "MEYCIQDjxvAMXTOkubTxYi6S6bHjJoeJtuZWF3ta/I34QZrsvwIhAMRptNcROfDDeij5HQdsjx8dednzGxhcoYxFIxJQtQTS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498256}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.29.2_1736078903218_0.34866800717618496", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3656a8341a6048f2111f423301aaad8e84a5fe90", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-laQVRvdbKmjXuFA3ZiZj7+U24FcmoPlXEi2OyLfbpY2MW1oxLt9Au8q9eHd0x6Pw/Kw4oe9gwVXWwIf2PVqblg==", "signatures": [{"sig": "MEUCIHowo5cWEgVf3G3QesGPw4JTgdAihgJxOBlSxsovLXNZAiEArXegpxuz8LatZ9D7LFQpmfnhQ7zAzl8xHyMpO6F8I/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498256}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.30.0_1736145435791_0.5009360675081926", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "183637d91456877cb83d0a0315eb4788573aa588", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-UtgGb7QGgXDIO+tqqJ5oZRGHsDLO8SlpE4MhqpY9Llpzi5rJMvrK6ZGhsRCST2abZdBqIBeXW6WPD5fGK5SDwg==", "signatures": [{"sig": "MEYCIQD8NR2XYb5+cV/sA9GiNo3m+0L1QaHXb0qgeGxwINyEyQIhAKxOVOP0jQss+KCjh4MGWIQ6yktw7RBMNYeYK+ebpqO/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498256}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.30.1_1736246191353_0.8467478918700682", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ac33ab91224594896abd17867fc781e7b93a69b5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-4xfIux4fgGu6r5W8kp7fF6BATP5icAnDx00vAq+zv5LGCG2USkGkb2oVUyT09cnfMWSqMaUCtxWfi4cNUpHD0A==", "signatures": [{"sig": "MEQCIEh2ZbSVoUSo1TszSvCL09X+fpJHPw3r6Z6mnyCo9Ct8AiAZUuP807kA+PgYitMzXWOw+zlzKHv52wVgwTvLOO+LrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498258}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.31.0-0_1736834300398_0.8513991552824784", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c6b048f1e25f3fea5b4bd246232f4d07a159c5a0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-zSoHl356vKnNxwOWnLd60ixHNPRBglxpv2g7q0Cd3Pmr561gf0HiAcUBRL3S1vPqRC17Zo2CX/9cPkqTIiai1g==", "signatures": [{"sig": "MEUCIH67WGDJb+7rwqay7zN4a41QwYeqA9HAfzTvLJe6wyozAiEAv4fUdT2GYfhnJgpBJtyAmED9OrwWyfyjbsOrL2d9WKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2510544}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.31.0_1737291446744_0.028145423304158923", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4e8c697bbaa2e2d7212bd42086746c8275721166", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-VDzNHtLLI5s7xd/VubyS10mq6TxvZBp+4NRWoW+Hi3tgV05RtVm4qK99+dClwTN1McA6PHwob6DEJ6PlXbY83A==", "signatures": [{"sig": "MEUCIE/vKp4TAeBg+4csydm7jZyV1CbbzoN/d3ZRR0IRNtdtAiEAgBDIUgXumGFeFY/YXgp5jmSzNsNctup7oX+hz1YjPDY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2518736}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.32.0_1737707292665_0.027601412658114333", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7485b2d9329171aa9355c6e9239b00f340cd16bf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-9cdaJFg/SrUXQbouPn+loUa9NXpUWa8PQ+DsFJmJESF2JfCPtbsFPtFqH2C8iwjTSzAEJtwlji7LibIrvxErmQ==", "signatures": [{"sig": "MEQCIE14DdJ4KEHpMfXbyQ0ae65OSJidYt+bsXPUaFhgKAxbAiBMgsEHErqpqWHPrje8WQ9eNjeUbqp60hP/EktUYczaQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2518738}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.33.0-0_1738053046870_0.8624199697414521", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5c009c264a7ce0e19b40890ca9945440bb420691", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-WQFLZ9c42ECqEjwg/GHHsouij3pzLXkFdz0UxHa/0OM12LzvX7DzedlY0SIEly2v18YZLRhCRoHZDxbBSWoGYg==", "signatures": [{"sig": "MEUCIQDKgJcQyg/4dRGRCMCLvmmJfY3HLJx4psvWizeHJhwcpAIgLCeaW+O/lhLz5fRFZL+Uugh5SY5itHnmJ1khUKMNycc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2518736}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.32.1_1738053233672_0.841428337314359", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3994c0d01b4a184b87ee50230dbb99f936b8391c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-11lI0xOnzSZUG2RuH8Eqlj3obnu7BGcTnm/sb3LwCGCHD09PLjaMVCaYx4Pf+E0K4AYMPpHKDP5TTKG24W+JXQ==", "signatures": [{"sig": "MEUCIHwJyPRCQmRYQhely/p7fy1ddUOMk+pWxl48fUnUgSBeAiEAkafGDAryUTzWCOKmzweIkSZZw97uvrJaT0kpUDXUCeo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.33.0_1738393959817_0.10325217895100058", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5b5163bd4e9c2ff11f8230252962240452e18b4a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-chZLTUIPbgcpm+Z7ALmomXW8Zh+wE2icrG+K6nt/HenPLmtwCajhQC5flNSk1Xy5EDMt/QAOz2MhzfOfJOLSiA==", "signatures": [{"sig": "MEUCIQDtjGyAPVBvxBa5oyEsl1PVQJdUfEx3HCznztz1SYikWwIgSC7JqBBo29j8xdyF3R8E4y7fDgMt5YeKsHatNiMoz/o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.0_1738399259909_0.6411533708178501", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f5fba18eda523cbe04f82cbf3e0685e461e71613", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-hzpleiKtq14GWjz3ahWvJXgU1DQC9DteiwcsY4HgqUJUGxZThlL66MotdUEK9zEo0PK/2ADeZGM9LIondE302A==", "signatures": [{"sig": "MEUCIQC9fpsd622QORif3RLTRHep4XMYArmkliGjXUbdbq7fogIgLVRJzy4E1yg5A5rpyLObpbqEay+2rNWV8iBp6oppM1A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.1_1738565930679_0.36930210840100464", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2a59f28be83f134f600c9bc41f7a343e29d7ab80", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-j+jFdfOycLIQ7FWKka9Zd3qvsIyugg5LeZuHF6kFlXo6MSOc6R1w37YUVy8VpAKd81LMWGi5g9J25P09M0SSIw==", "signatures": [{"sig": "MEYCIQC2IJgvGvOeV7vognLtnmc1cN/buHpYcsPzirUDCEvbzgIhAPxv/1+Seo+VtF81prskXD0+JEmiGIlLBhNzA5D8fUPc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.2_1738656643273_0.09996386877884755", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "017bb2808665d69ba55740cae02708ea8cb45885", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-lOyG3aF4FTKrhpzXfMmBXgeKUUXdAWmP2zSNf8HTAXPqZay6QYT26l64hVizBjq+hJx3pl0DTEyvPi9sTA6VGA==", "signatures": [{"sig": "MEUCIQCaR+1C328bUBiocw4wXwLMd8yl8wYHOhwzgErFculUxwIgTLNm3+kqK2U5+CqvtIz+J8Yeotbd+DN4rUF6ZVg/U4A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.3_1738747364787_0.3723365586278846", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5d58ee6c68e160754a9b5950156308b8a5eaa18f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-JGejzEfVzqc/XNiCKZj14eb6s5w8DdWlnQ5tWUbs99kkdvfq9btxxVX97AaxiUX7xJTKFA0LwoS0KU8C2faZRg==", "signatures": [{"sig": "MEUCIGJApYblAwN0/WiOqAsmAdGrbX++xF2Jq7SsPTWobaLXAiEAlJLNK1F65OQ8RCdWnpoVNA/Df4Cmnhd11d9tFYWQldQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.4_1738791110027_0.6077244389953944", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "471a161af5053b5b220f6b4e11154fccfc6be27b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-SkCIXLGk42yldTcH8UXh++m0snVxp9DLf4meb1mWm0lC8jzxjFBwSLGtUSeLgQDsC05iBaIhyjNX46DlByrApQ==", "signatures": [{"sig": "MEUCIQDcGRHs92AmYtk0dkc9OyctQQ0geD4R8qsmi5GeYo0rKAIgVtqVcsw9EnUevTJJHBrKMELDxMYtgpLYN7WqDbtKfkg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2504384}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.5_1738918423021_0.8006126778155995", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "aec8d4cdf911cd869a72b8bd00833cb426664e0c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-Sht4aFvmA4ToHd2vFzwMFaQCiYm2lDFho5rPcvPBT5pCdC+GwHG6CMch4GQfmWTQ1SwRKS0dhDYb54khSrjDWw==", "signatures": [{"sig": "MEUCIElXJ5ZNnaWd8Bi1EEaTNDWufdF9tq1cOj4YJ2ypa6yLAiEAjS1QggqEpB1Mp46ynobe12mZF8IG5k84tkBsG/RqGnw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2483888}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.6_1738945966924_0.6379780995083257", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f27af0b55f0cdd84e182e6cd44a6d03da0458149", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-9kPVf9ahnpOMSGlCxXGv980wXD0zRR3wyk8+33/MXQIpQEOpaNe7dEHm5LMfyRZRNt9lMEQuH0jUKj15MkM7QA==", "signatures": [{"sig": "MEQCIDnDYjnLn6wd90S5jhDgKNZPdjGRZ/Zv3XUlMPLpb6kpAiBvNlI8EjO4dtIftm5L7NIWFE8bJ2ErTTRXAiukxO7Uvg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496176}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.7_1739526877346_0.6942278295068955", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5783fc0adcab7dc069692056e8ca8d83709855ce", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-8y7ED8gjxITUltTUEJLQdgpbPh1sUQ0kMTmufRF/Ns5tI9TNMNlhWtmPKKHCU0SilX+3MJkZ0zERYYGIVBYHIA==", "signatures": [{"sig": "MEUCIQCG3VGLy93sa1ua4nWzocqiZXEPb629yUV7WmIeNvxMYAIgeteD6wiNqpmLojAQ9L/kC3kcqxlMT4vB0HGp+60Cckg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496176}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.8_1739773623234_0.5918976961368188", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7193cbd8d128212b8acda37e01b39d9e96259ef8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-FwBHNSOjUTQLP4MG7y6rR6qbGw4MFeQnIBrMe161QGaQoBQLqSUEKlHIiVgF3g/mb3lxlxzJOpIBhaP+C+KP2A==", "signatures": [{"sig": "MEYCIQCkWci2elasPATSQcvolyB1GR0jeBGDyiUZLVhoArJ5YQIhAIuSZs5tcQbWTkT27lKWHgLBdlCVA5sQFk+QV3YVp518", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2565808}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.34.9_1740814397526_0.4018821320804329", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4f9774beddc6f4274df57ac99862eb23040de461", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-Pim1T8rXOri+0HmV4CdKSGrqcBWX0d1HoPnQ0uw0bdp1aP5SdQVNBy8LjYncvnLgu3fnnCt17xjWGd4cqh8/hA==", "signatures": [{"sig": "MEUCIQDwlTjlj3DWEdMWc4vGonxLdqDJArKn1FF97Le7lHY8SQIgSpxEX2KpsHLEvxcfFoPgSMPbQBFGa5N9AH5swNtH4Fk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2565808}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.35.0_1741415129513_0.8630143449432428", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d2e69f7598c71f03287b763fdbefce4163f07419", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-5KtoW8UWmwFKQ96aQL3LlRXX16IMwyzMq/jSSVIIyAANiE1doaQsx/KRyhAvpHlPjPiSU/AYX/8m+lQ9VToxFQ==", "signatures": [{"sig": "MEUCIADA7s2eT/KCuBIXla8sNsE6N2twBwE/7LHasCW8+FqlAiEA/y282QUwFeHB9bqxW8MlDTYPcWokF+61ip2FlZP+jF8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2565808}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.36.0_1742200586689_0.6500611697983183", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "52c27717d3c4819d13b5ebc2373ddea099d2e71b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-pKivGpgJM5g8dwj0ywBwe/HeVAUSuVVJhUTa/URXjxvoyTT/AxsLTAbkHkDHG7qQxLoW2s3apEIl26uUe08LVQ==", "signatures": [{"sig": "MEUCIQDPZTKM9FgNlMuJIsHibmrPtyDQA9sjT0OXWEW8dfKDZQIgM9xtg5okEEyDhr2vjTDgNtgGTr7CwvPsCgzIXTqoPt8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2561712}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.37.0_1742741864621_0.3444132473976709", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "11c32c463e68a86e279cda090a9405a7558f9406", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-vPvNgFlZRAgO7rwncMeE0+8c4Hmc+qixnp00/Uv3ht2x7KYrJ6ERVd3/R0nUtlE6/hu7/HiiNHJ/rP6knRFt1w==", "signatures": [{"sig": "MEUCICcv7FCMKhNh7/p7B97nAQ9rNGul+IpAKZnb3c+ALccFAiEA28H6SdZktzHzywiOYUNpCy3OU1b75LvPz5gcqgCN74s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2569904}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.38.0_1743229791444_0.9352631536480507", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "00825b3458094d5c27cb4ed66e88bfe9f1e65f90", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-t9jqYw27R6Lx0XKfEFe5vUeEJ5pF3SGIM6gTfONSMb7DuG6z6wfj2yjcoZxHg129veTqU7+wOhY6GX8wmf90dA==", "signatures": [{"sig": "MEUCIQCdNY5qzXbJ5aucewXjut8G7QfS9aH9fDhvHHL76kNaOAIgdn2n1bW3FafW+OZJqfaAhGZ2oMgHpxBRXOZI332Bgls=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2569904}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.39.0_1743569415512_0.8432019065102847", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "68b045a720bd9b4d905f462b997590c2190a6de0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==", "signatures": [{"sig": "MEUCIBWzm6npYXLok1GJJmAnXKlN2E/xDGIfi+6JqmLyVg+EAiEAq+ZqkI4Mln7sQ/gMPQdM8qlZuSpJTlw1GKNS2bVBufI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2583520}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.40.0_1744447217518_0.5486668317234278", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0413169dc00470667dea8575c1129d4e7a73eb29", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-XiK5z70PEFEFqcNj3/zRSz/qX4bp4QIraTy9QjwJAb/Z8GM7kVUsD0Uk8maIPeTyPCP03ChdI+VVmJriKYbRHQ==", "signatures": [{"sig": "MEUCICW0Cas4VzlCQ37dtQkluQlSSBNS/vJTAfgCfyvbz8aVAiEA/X3SMHkUhEHEsHjjg5rHws1aXrRFjTyBki6PvvN2L+U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2616304}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.40.1_1745814966909_0.09104967717216783", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "90993269b8b995b4067b7b9d72ff1c360ef90a17", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-lG7Xa+BmBNwpjmVUbmyKxdQJ3Q6whHjMjzQplOs5Z+Gj7mxPtWakGHqzMqNER68G67kmCX9qX57aRsW5V0VOng==", "signatures": [{"sig": "MEYCIQCmV7/icKKJlSJleFKubAQVT4idqUOYJfjeuqWSUVwjNwIhAJGfkHav1R6cHo2scxShqtzEmea+TSithRqHTywQp1+N", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2620400}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.40.2_1746516457329_0.41476318213025754", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c3e42b66c04e25ad0f2a00beec42ede96ccc8983", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-XMLeKjyH8NsEDCRptf6LO8lJk23o9wvB+dJwcXMaH6ZQbbkHu2dbGIUindbMtRN6ux1xKi16iXWu6q9mu7gDhQ==", "signatures": [{"sig": "MEYCIQCXHpbUPEMSDHGJCka+XTGwlWO0uj96IU5ZpNCAvDf2TQIhAOnb19azTlxxlDPiFraTnV/7nS8ke9dHJtYPW19FCRLF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2608112}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.41.0_1747546450170_0.7904918150324522", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "01cf56844a1e636ee80dfb364e72c2b7142ad896", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-cWBOvayNvA+SyeQMp79BHPK8ws6sHSsYnK5zDcsC3Hsxr1dgTABKjMnMslPq1DvZIp6uO7kIWhiGwaTdR4Og9A==", "signatures": [{"sig": "MEYCIQDG9fdamfbJImPtTonN6YwSHMBNiD3ThHzrg3ADZlhabgIhALTXqennjZ41N0vX5DcVYhDI+Fj5FsWto/ZE0JebVGax", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2669776}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.41.1_1748067322200_0.13598750204327414", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "61c0c962049b2c14a3eaa0f1037b3bde0150359f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-F3fxs7ajUNny4z1TsprdWB9gg8QRwSNSSILVfTmG8rXdeUFWuHEf3Uf5ltrdii8CkzZS3kD/VFPdNVdH3BOpIA==", "signatures": [{"sig": "MEYCIQDvwHKaYP3b6HaUg0KDT/uzAcKYHVrngtoXNzMrTPQVHgIhALWUY2bbSkwEHnz3orywLJTvZy/5HIqAbwk6TjS7GzqH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2669776}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.41.2_1749210076083_0.2876876219565181", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "45aa751bdf05ac696da417a37fdfd13f607e1fab", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-Gfm6cV6mj3hCUY8TqWa63DB8Mx3NADoFwiJrMpoZ1uESbK8FQV3LXkhfry+8bOniq9pqY1OdsjFWNsSbfjPugw==", "signatures": [{"sig": "MEYCIQCV9hW6pYa8GYnlj9WaQbDnHy1yL1kqlXcWf4VtW7twywIhALonji+sc5cNyhW3qlE/YEHfGSrix94JSvvpPHSFMKkt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2669776}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.42.0_1749221336461_0.9182395605791649", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d16a57f86357a4e697142bee244afed59b24e6c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-jAHr/S0iiBtFyzjhOkAics/2SrXE092qyqEg96e90L3t9Op8OTzS6+IX0Fy5wCt2+KqeHAkti+eitV0wvblEoQ==", "signatures": [{"sig": "MEYCIQCfbPK7XiJjxRCPA1KRHN2JddBuMJSzmto+qc0BYRFvYAIhAKmHwvUWiaj6x7zhaDQoBThGnFMGePkrTLWJGgPjYJcD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2669776}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.43.0_1749619407774_0.9344665708906101", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "597d40f60d4b15bedbbacf2491a69c5b67a58e93", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-iUVJc3c0o8l9Sa/qlDL2Z9UP92UZZW1+EmQ4xfjTc1akr0iUFZNfxrXJ/R1T90h/ILm9iXEY6+iPrmYB3pXKjw==", "signatures": [{"sig": "MEYCIQDktbgShO4hTXADYyQj2Gd/dc+sIZsCS6spZplHUfwqVAIhAI8XjsODwgfnGtnN4dIEoK6+cJrVu0o4EYvchmpDmlI3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2661584}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.44.0_1750314223430_0.18136495397189245", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-gnu@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4b211e6fd57edd6a134740f4f8e8ea61972ff2c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-EtnsrmZGomz9WxK1bR5079zee3+7a+AdFlghyd6VbAjgRJDbTANJ9dcPIPAi76uG05micpEL+gPGmAKYTschQw==", "signatures": [{"sig": "MEYCIQCvdowNN2O8sCngSAk9VnGwKQlD9K9wYsZ/2XDzrdtWygIhANoQICSS2SaxGj81616UHayI9sPQaQeCvh7bBCVvVjpi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2661584}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-gnu_4.44.1_1750912501769_0.026520243985280523", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-x64-gnu", "version": "4.44.2", "os": ["linux"], "cpu": ["x64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-x64-gnu.node", "_id": "@rollup/rollup-linux-x64-gnu@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/bXb0bEsWMyEkIsUL2Yt5nFB5naLAwyOWMEviQfQY1x3l5WsLKgvZf66TM7UTfED6erckUVUJQ/jJ1FSpm3pRQ==", "shasum": "0699c560fa6ce6b846581a7e6c30c85c22a3f0da", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2673872, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBSbBGxpGshieRa4yeFIVl1jJIAv5hT/cVqOzjLIRFXaAiEA0KKTZ/b0konNyD9ZM3FVQzuZLaZS46Jo8RN/zVqFJY0="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-x64-gnu_4.44.2_1751633812845_0.5800804870916989"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:48.510Z", "modified": "2025-07-04T12:56:53.320Z", "4.0.0-0": "2023-07-31T19:17:48.719Z", "4.0.0-1": "2023-08-01T04:49:13.446Z", "4.0.0-2": "2023-08-01T11:16:50.028Z", "4.0.0-3": "2023-08-04T08:17:14.164Z", "4.0.0-4": "2023-08-04T11:37:01.447Z", "4.0.0-5": "2023-08-20T06:57:07.179Z", "4.0.0-6": "2023-08-20T07:51:54.817Z", "4.0.0-7": "2023-08-20T10:33:59.199Z", "4.0.0-8": "2023-08-20T11:22:51.111Z", "4.0.0-9": "2023-08-20T14:29:36.551Z", "4.0.0-10": "2023-08-21T15:30:35.137Z", "4.0.0-11": "2023-08-23T10:16:17.212Z", "4.0.0-12": "2023-08-23T14:41:06.776Z", "4.0.0-13": "2023-08-24T15:48:52.949Z", "4.0.0-14": "2023-09-15T12:34:43.532Z", "4.0.0-15": "2023-09-15T13:07:10.671Z", "4.0.0-16": "2023-09-15T14:17:36.264Z", "4.0.0-17": "2023-09-15T14:59:26.013Z", "4.0.0-18": "2023-09-15T16:11:05.929Z", "4.0.0-19": "2023-09-15T18:51:16.662Z", "4.0.0-20": "2023-09-24T06:10:56.206Z", "4.0.0-21": "2023-09-24T17:22:42.840Z", "4.0.0-22": "2023-09-26T16:17:55.174Z", "4.0.0-23": "2023-09-26T20:14:45.089Z", "4.0.0-24": "2023-10-03T05:13:07.344Z", "4.0.0-25": "2023-10-05T14:13:35.342Z", "4.0.0": "2023-10-05T15:15:14.023Z", "4.0.1": "2023-10-06T12:37:04.405Z", "4.0.2": "2023-10-06T14:19:04.823Z", "4.1.0": "2023-10-14T05:52:34.548Z", "4.1.1": "2023-10-15T06:32:08.979Z", "4.1.3": "2023-10-15T17:48:47.256Z", "4.1.4": "2023-10-16T04:34:30.135Z", "4.1.5": "2023-10-28T09:23:53.476Z", "4.1.6": "2023-10-31T05:45:36.653Z", "4.2.0": "2023-10-31T08:11:03.847Z", "4.3.0": "2023-11-03T20:13:27.021Z", "4.3.1": "2023-11-11T07:58:13.112Z", "4.4.0": "2023-11-12T07:50:11.034Z", "4.4.1": "2023-11-14T05:26:07.176Z", "4.5.0": "2023-11-18T05:52:29.679Z", "4.5.1": "2023-11-21T20:13:27.967Z", "4.5.2": "2023-11-24T06:30:08.887Z", "4.6.0": "2023-11-26T13:39:31.281Z", "4.6.1": "2023-11-30T05:23:28.947Z", "4.7.0": "2023-12-08T07:58:22.566Z", "4.8.0": "2023-12-11T06:25:17.666Z", "4.9.0": "2023-12-13T09:24:37.465Z", "4.9.1": "2023-12-17T06:26:29.922Z", "4.9.2": "2023-12-30T06:23:44.983Z", "4.9.3": "2024-01-05T06:21:05.457Z", "4.9.4": "2024-01-06T06:39:19.177Z", "4.9.5": "2024-01-12T06:16:31.808Z", "4.9.6": "2024-01-21T05:52:37.273Z", "4.10.0": "2024-02-10T05:59:01.469Z", "4.11.0": "2024-02-15T06:10:08.702Z", "4.12.0": "2024-02-16T13:33:06.333Z", "4.12.1": "2024-03-06T06:04:02.075Z", "4.13.0": "2024-03-12T05:29:07.235Z", "4.13.1-1": "2024-03-24T07:39:51.325Z", "4.13.1": "2024-03-27T10:28:09.824Z", "4.13.2": "2024-03-28T14:14:11.134Z", "4.14.0": "2024-04-03T05:23:35.160Z", "4.14.1": "2024-04-07T07:36:04.083Z", "4.14.2": "2024-04-12T06:24:05.172Z", "4.14.3": "2024-04-15T07:18:57.865Z", "4.15.0": "2024-04-20T05:37:44.776Z", "4.16.0": "2024-04-21T04:42:59.591Z", "4.16.1": "2024-04-21T18:30:34.311Z", "4.16.2": "2024-04-22T15:19:52.163Z", "4.16.3": "2024-04-23T05:13:00.663Z", "4.16.4": "2024-04-23T13:15:33.398Z", "4.17.0": "2024-04-27T11:30:18.610Z", "4.17.1": "2024-04-29T04:58:19.733Z", "4.17.2": "2024-04-30T05:01:20.034Z", "4.18.0": "2024-05-22T05:04:14.008Z", "4.18.1": "2024-07-08T15:25:41.345Z", "4.19.0": "2024-07-20T05:46:42.876Z", "4.19.1": "2024-07-27T04:54:27.653Z", "4.19.2": "2024-08-01T08:33:21.737Z", "4.20.0": "2024-08-03T04:49:19.250Z", "4.21.0": "2024-08-18T05:55:59.513Z", "4.21.1": "2024-08-26T15:54:42.573Z", "4.21.2": "2024-08-30T07:04:53.638Z", "4.21.3": "2024-09-12T07:06:19.318Z", "4.22.0": "2024-09-19T04:55:59.550Z", "4.22.1": "2024-09-20T08:22:20.748Z", "4.22.2": "2024-09-20T09:34:13.620Z", "4.22.3-0": "2024-09-20T14:48:27.902Z", "4.22.3": "2024-09-21T05:03:39.324Z", "4.22.4": "2024-09-21T06:11:48.833Z", "4.22.5": "2024-09-27T11:48:47.250Z", "4.23.0": "2024-10-01T07:10:46.621Z", "4.24.0": "2024-10-02T09:37:50.998Z", "4.24.1": "2024-10-27T06:43:38.596Z", "4.24.2": "2024-10-27T15:40:45.705Z", "4.25.0-0": "2024-10-29T06:15:47.471Z", "4.24.3": "2024-10-29T14:14:45.979Z", "4.24.4": "2024-11-04T08:47:43.924Z", "4.25.0": "2024-11-09T08:37:59.153Z", "4.26.0": "2024-11-13T06:45:35.204Z", "4.27.0-0": "2024-11-13T07:03:49.539Z", "4.27.0-1": "2024-11-14T06:33:46.332Z", "4.27.0": "2024-11-15T10:41:10.788Z", "4.27.1-0": "2024-11-15T13:28:46.178Z", "4.27.1-1": "2024-11-15T15:38:40.734Z", "4.27.1": "2024-11-15T16:08:19.192Z", "4.27.2": "2024-11-15T17:20:42.940Z", "4.27.3": "2024-11-18T16:40:13.483Z", "4.27.4": "2024-11-23T07:00:54.721Z", "4.28.0": "2024-11-30T13:16:23.301Z", "4.28.1": "2024-12-06T11:45:35.407Z", "4.29.0-0": "2024-12-16T06:40:33.030Z", "4.29.0-1": "2024-12-19T06:38:07.262Z", "4.29.0-2": "2024-12-20T06:56:42.450Z", "4.29.0": "2024-12-20T18:38:03.454Z", "4.29.1": "2024-12-21T07:16:42.451Z", "4.30.0-0": "2024-12-21T07:17:54.006Z", "4.30.0-1": "2024-12-30T06:52:56.901Z", "4.29.2": "2025-01-05T12:08:23.500Z", "4.30.0": "2025-01-06T06:37:15.995Z", "4.30.1": "2025-01-07T10:36:31.576Z", "4.31.0-0": "2025-01-14T05:58:20.611Z", "4.31.0": "2025-01-19T12:57:26.976Z", "4.32.0": "2025-01-24T08:28:12.895Z", "4.33.0-0": "2025-01-28T08:30:47.231Z", "4.32.1": "2025-01-28T08:33:53.864Z", "4.33.0": "2025-02-01T07:12:40.022Z", "4.34.0": "2025-02-01T08:41:00.145Z", "4.34.1": "2025-02-03T06:58:50.908Z", "4.34.2": "2025-02-04T08:10:43.516Z", "4.34.3": "2025-02-05T09:22:45.290Z", "4.34.4": "2025-02-05T21:31:50.281Z", "4.34.5": "2025-02-07T08:53:43.272Z", "4.34.6": "2025-02-07T16:32:47.200Z", "4.34.7": "2025-02-14T09:54:37.567Z", "4.34.8": "2025-02-17T06:27:03.521Z", "4.34.9": "2025-03-01T07:33:17.756Z", "4.35.0": "2025-03-08T06:25:29.795Z", "4.36.0": "2025-03-17T08:36:26.934Z", "4.37.0": "2025-03-23T14:57:44.849Z", "4.38.0": "2025-03-29T06:29:51.728Z", "4.39.0": "2025-04-02T04:50:15.762Z", "4.40.0": "2025-04-12T08:40:17.715Z", "4.40.1": "2025-04-28T04:36:07.141Z", "4.40.2": "2025-05-06T07:27:37.559Z", "4.41.0": "2025-05-18T05:34:10.440Z", "4.41.1": "2025-05-24T06:15:22.459Z", "4.41.2": "2025-06-06T11:41:16.287Z", "4.42.0": "2025-06-06T14:48:56.692Z", "4.43.0": "2025-06-11T05:23:28.091Z", "4.44.0": "2025-06-19T06:23:43.684Z", "4.44.1": "2025-06-26T04:35:01.997Z", "4.44.2": "2025-07-04T12:56:53.080Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-x64-gnu`\n\nThis is the **x86_64-unknown-linux-gnu** binary for `rollup`\n", "readmeFilename": "README.md"}