{"_id": "esutils", "_rev": "35-8140b6bcfb841adf6f6d334b04f7f575", "name": "esutils", "description": "utility box for ECMAScript language tools", "dist-tags": {"latest": "2.0.3"}, "versions": {"1.0.0": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.0.0", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "dependencies": {}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec"}, "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.0.0", "dist": {"shasum": "8151d358e20c8acc7fb745e7472c0025fe496570", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.0.0.tgz", "integrity": "sha512-x/iYH53X3quDwfHRz4y8rn4XcEwwCJeWsul9pF1zldMbGtgOtMNBEOuYWwB1EQlK2LRa1fev3YAgym/RElp5Cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDw1FQm/LqI8WW0LyRYguHriCagZHSEppLs+5IiA1DR5AIhAMtnq72eG7qLntQgLqZssCYiDsTw3qYoitvrZYYgRtnp"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}}, "1.1.0": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.0", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.0", "dist": {"shasum": "813a780e504026b7c82ba9d8ea8f2193391ca510", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.0.tgz", "integrity": "sha512-RPKSzkC9mu5cXqzET9yhLWxfC1wR7dzbl/F4F8Nej+w8kPTVVxqnxzIET0+w8c/JNdiJ9L+clYyjVvuMWSOSPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0hbd+slRbvL6vXCw7Kx1ff0CWbp/4pbXcfhvViGuQfwIhAKKcjlBV3OrN/5wGSbQn7t/1tvJ59wfulK+dgkPNCKiP"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}}, "1.1.1": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.1", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.1", "dist": {"shasum": "222e7b2d8f9c2a0c888d9ee16c1d2d1a9777f12f", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.1.tgz", "integrity": "sha512-r996KyDs0HUnl+yDP2QnH9Ds8iiO+qnco4yNc5jUBXyyO24fvc4lzRb/iwyFz0iJO89vOyt44/oQQX0rz3UEBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4b1GzPIdwB3K7rveohk/aT59Pcxh1pMislcSnlxRgoAIgaCyOeXvX1L6PPjjnhlXPXJ2kGjXTg73V9Z9GGfM7ILw="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}}, "1.1.2": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.2", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.2", "dist": {"shasum": "f121b7e7e6813d3f4332d297365c2d931cf666ad", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.2.tgz", "integrity": "sha512-5ZJ7eBbNn+cACkjGC4pwy9ffdESbS78e38bJ6TzdvEoRbC2HYxoA6QjYmVgJCZ0m88/kFmbHtW4B2znqRQWXlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkNCxMg7qs1wBUN+vOaFOw0/lZRdpUUls5lyUOPqCiVAiBkHuB+nwjQ9GznVHmFYJQi/uq+jpfzLW9bY9XUYhVqDw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}}, "1.1.3": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.3", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.3", "dist": {"shasum": "837448ccd7fbe5471f7c960df60055983137c95a", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.3.tgz", "integrity": "sha512-dplFKmNz9NRi3f+BhGrlDhqvwAciscB41baioiqlqQF7YML75XyL0iwgxghKnW611aY6Tc0XbuoiAM/1la/FZg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIER/bkmVCbryzgoA5TZY1gOlL0eya4xIAQz4IRMei9KyAiEA5qu8gBj4cSS6imG8GzUQkYI4uApYDtD8iBTCIrFuZpg="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}}, "1.1.4": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.4", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.4", "dist": {"shasum": "9d37c87e30bcd2646a387c1d0703021c9caa660d", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.4.tgz", "integrity": "sha512-Pp2qijjgDYPWo57Ap0Eik5qWiAfIx6CBoKC70Sn8HYcmMHQRbkzhgi0tHLjwK8o7eAEXlyj9f0rI3y/BWmvddQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGJnz9hxqG53RXxsoNBPBkBH3bGQGh8mBagGHW+uEPAyAiEAzcHxhPa3ZbuxJci4L4kjA6PNGyZ0LnywHzji7wGSC6w="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}}, "1.1.5-dev": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.5-dev", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "e40b337ee770a787c38dfff58533f8f2a4152306", "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.5-dev", "_shasum": "6445009d6979a4e8322250c677ede7b2b9e39c69", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "6445009d6979a4e8322250c677ede7b2b9e39c69", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.5-dev.tgz", "integrity": "sha512-CVK399sRfDX++2Yp/MGl7e05pA27qOxWG0LPy2uipupEf9PTynXAfypnWvL5U5ruTJO7ueEyihFM6VXqF4uPOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzBD1qqTTEThnC5aE1XMYVQcUnBGJLEeBHnUrn4OHlwAiEApAXft/qzCJB8Sng4uWruZCRF9o5uG2QdhbvHBshrpwg="}]}}, "1.1.5": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.5", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "e40b337ee770a787c38dfff58533f8f2a4152306", "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.5", "_shasum": "91d7a91378288910d02cecee4f02329afadbfbf4", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "91d7a91378288910d02cecee4f02329afadbfbf4", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.5.tgz", "integrity": "sha512-SlQX/1EeZLCJ2DCJDSvk/jpXewrXWKePD/WpbwuzG8USISwFwODyMtXPl0IoICVffRkd3R8wnc1l32SxbPrKeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBh1JZPeGh0ZkEpohfg6FR/JoPrWyHra7N3mcnvZJ8lZAiAhOiZJAMvWgK1DRMff0jTt38t90MGsOrjKpXVIyN78pg=="}]}}, "1.1.6": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/Constellation/esutils", "main": "lib/utils.js", "version": "1.1.6", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/esutils.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "unicode-6.3.0": "~0.1.1", "regenerate": "~0.5.4"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "a91c5ed6199d1019ef071f610848fcd5103ef153", "bugs": {"url": "https://github.com/Constellation/esutils/issues"}, "_id": "esutils@1.1.6", "_shasum": "c01ccaa9ae4b897c6d0c3e210ae52f3c7a844375", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "c01ccaa9ae4b897c6d0c3e210ae52f3c7a844375", "tarball": "https://registry.npmjs.org/esutils/-/esutils-1.1.6.tgz", "integrity": "sha512-RG1ZkUT7iFJG9LSHr7KDuuMSlujfeTtMNIcInURxKAxhMtwQhI3NrQhz26gZQYlsYZQKzsnwtpKrFKj9K9Qu1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBbnXu6CsPtEw/Mq9pmr0hdU7JKl01pKEzYP8Zp34l1oAiEAmheH7OQrhhsAlylalkF1/EXkQRtsCZnEPRrviOub954="}]}}, "2.0.0": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/estools/esutils", "main": "lib/utils.js", "version": "2.0.0", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esutils.git"}, "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.6.3", "mocha": "~2.2.1", "regenerate": "~1.2.1", "unicode-7.0.0": "^0.1.5"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "ab491753eab7eff99bdc839d3224632de95735eb", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "_id": "esutils@2.0.0", "_shasum": "6ab3d92654bedd456ec7fa172946c8942c7dc965", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6ab3d92654bedd456ec7fa172946c8942c7dc965", "tarball": "https://registry.npmjs.org/esutils/-/esutils-2.0.0.tgz", "integrity": "sha512-3hjaqdn6zmcnPUxAs8wVYL/iHPna4x/8E8wGIpGduekGqRs3KvIypea9FrRNt3xZMSMFIMtoc89q7CGIQx5Jdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLJiGI9l93voWRxv1bA0TR91lY4tobbURkC6OZnCK2rQIhAO56CRNR022Z2D8g9LdGVdKmF/k50AZEAeO5Q/v4uXBE"}]}}, "2.0.1": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/estools/esutils", "main": "lib/utils.js", "version": "2.0.1", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esutils.git"}, "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.6.3", "mocha": "~2.2.1", "regenerate": "~1.2.1", "unicode-7.0.0": "^0.1.5"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "c884ed8638de82aa8145a194a29644ea0b9f4dbe", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "_id": "esutils@2.0.1", "_shasum": "090a6d6a61d1a769da9f403792f8deae9da45167", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "090a6d6a61d1a769da9f403792f8deae9da45167", "tarball": "https://registry.npmjs.org/esutils/-/esutils-2.0.1.tgz", "integrity": "sha512-F6JYDFU6uazUxtpTQOAYjmiwufAqESbSokBHYNVXDWnXnPwn5/yb+ZoI6+vpojdV3RDpPZC/MAusnNDksIfQuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSAOiIRBJeHh0d5yYj6UlDGOCaeD91hmkdeCeVXl+N+wIgR6TL3Gojr3FiR9m6KMF4p+ooJlULAIovSCB2NJCvQXo="}]}}, "2.0.2": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/estools/esutils", "main": "lib/utils.js", "version": "2.0.2", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["LICENSE.BSD", "README.md", "lib"], "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esutils.git"}, "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.6.3", "mocha": "~2.2.1", "regenerate": "~1.2.1", "unicode-7.0.0": "^0.1.5"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esutils/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "3ffd1c403f3f29db9e8a9574b1895682e57b6a7f", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "_id": "esutils@2.0.2", "_shasum": "0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b", "tarball": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha512-UUPPULqkyAV+M3Shodis7l8D+IyX6V8SbaBnTb449jf3fMTd8+UOZI1Q70NbZVOQkcR91yYgdHsJiMMMVmYshg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDruEOH3l2eWhHW3fhb3wagY7nOwQ1tTSZgjgGinOfgOAiEA00AvIQ/zs35s363T6DMHv7ANsZTn7SGFFPYddD3TqIc="}]}}, "2.0.3": {"name": "esutils", "description": "utility box for ECMAScript language tools", "homepage": "https://github.com/estools/esutils", "main": "lib/utils.js", "version": "2.0.3", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/esutils.git"}, "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.6.3", "mocha": "~2.2.1", "regenerate": "~1.3.1", "unicode-9.0.0": "~0.7.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint lib/*.js", "unit-test": "mocha --compilers coffee:coffee-script -R spec", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "8c2741c0154723fb92da137a0c97845b03b0943a", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "_id": "esutils@2.0.3", "_nodeVersion": "12.3.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "shasum": "74d2eb4de0b8da1293711910d50775b9b710ef64", "tarball": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "fileCount": 7, "unpackedSize": 50582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQOqeCRA9TVsSAnZWagAA9BAQAIRtK/0h/bvxyarkggdI\ngTw78JiPnSlIVpbPLmFK81XKuAjave6pFCV+3VutvdyK6yzZkcL2obuGgm2O\nSfxepqy5DyjAcbiOGC2fgtTTT3OExeY4ssQqkEb2/tdmr47bJLZFAQ3w9kBY\nNb+aYV/miowpmv9feglfuEBQ6bImR2Xe5a7wYh9AREWoz87GA1ylIXhwoeAq\nXXxYhKo5K4xuKvkz/tqYXXHiqoCGZux/OqA3PzlcYiWeTpXuTAEwXFUcmg03\n2vMGZxO3NX8mawotJ/KePqBLyxFVCXU1TC0Tegkj4fTqiCNqqoi4AV2lAVUR\nUyzQBE1jbmQgPiAGAf4AkCYjURI3TdwaqG0IBTMcfZd26ARxBUlb9BdM4ZlV\nKDr04+qzGZlDeqVWdzzh0PUgt/GtJ4qKYsMLpqHUO738tERu5wm5bzztZpoU\nZ0mTo7bL9+yfP50V94UY67xNNAhKqxhyv5IpcY+z2EMbIEgwQq00cwAFjBRZ\nA1gz7KkTTj9I5sw3FfTpFktOJc/6fUR3aFPfsZSGmzg+UHfB+e9pYTaeHUzD\ndkEzPVTC6ybl8lUWtGmHlW9cw+I5fBnwZro8lKbjU2lS2SVj2kUjCuhOYUz3\nVavCLBUw1smH4gmaFvylTQddYOU1qosahu27XFh7tYrMACOu7qdXk51FCUoL\nkDF+\r\n=3c8Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzWDs4YNzQ6DMOdX5sAWeI1roZ0Bw1p9ehywx8uBwDCgIgMal139vLKxKBLfIq1PuqH6oZfKH39xbO7B31ilYSulU="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esutils_2.0.3_1564535454027_0.6623171490272113"}, "_hasShrinkwrap": false}}, "readme": "### esutils [![Build Status](https://secure.travis-ci.org/estools/esutils.svg)](http://travis-ci.org/estools/esutils)\nesutils ([esutils](http://github.com/estools/esutils)) is\nutility box for ECMAScript language tools.\n\n### API\n\n### ast\n\n#### ast.isExpression(node)\n\nReturns true if `node` is an Expression as defined in ECMA262 edition 5.1 section\n[11](https://es5.github.io/#x11).\n\n#### ast.isStatement(node)\n\nReturns true if `node` is a Statement as defined in ECMA262 edition 5.1 section\n[12](https://es5.github.io/#x12).\n\n#### ast.isIterationStatement(node)\n\nReturns true if `node` is an IterationStatement as defined in ECMA262 edition\n5.1 section [12.6](https://es5.github.io/#x12.6).\n\n#### ast.isSourceElement(node)\n\nReturns true if `node` is a SourceElement as defined in ECMA262 edition 5.1\nsection [14](https://es5.github.io/#x14).\n\n#### ast.trailingStatement(node)\n\nReturns `Statement?` if `node` has trailing `Statement`.\n```js\nif (cond)\n    consequent;\n```\nWhen taking this `IfStatement`, returns `consequent;` statement.\n\n#### ast.isProblematicIfStatement(node)\n\nReturns true if `node` is a problematic IfStatement. If `node` is a problematic `IfStatement`, `node` cannot be represented as an one on one JavaScript code.\n```js\n{\n    type: 'IfStatement',\n    consequent: {\n        type: 'WithStatement',\n        body: {\n            type: 'IfStatement',\n            consequent: {type: 'EmptyStatement'}\n        }\n    },\n    alternate: {type: 'EmptyStatement'}\n}\n```\nThe above node cannot be represented as a JavaScript code, since the top level `else` alternate belongs to an inner `IfStatement`.\n\n\n### code\n\n#### code.isDecimalDigit(code)\n\nReturn true if provided code is decimal digit.\n\n#### code.isHexDigit(code)\n\nReturn true if provided code is hexadecimal digit.\n\n#### code.isOctalDigit(code)\n\nReturn true if provided code is octal digit.\n\n#### code.isWhiteSpace(code)\n\nReturn true if provided code is white space. White space characters are formally defined in ECMA262.\n\n#### code.isLineTerminator(code)\n\nReturn true if provided code is line terminator. Line terminator characters are formally defined in ECMA262.\n\n#### code.isIdentifierStart(code)\n\nReturn true if provided code can be the first character of ECMA262 Identifier. They are formally defined in ECMA262.\n\n#### code.isIdentifierPart(code)\n\nReturn true if provided code can be the trailing character of ECMA262 Identifier. They are formally defined in ECMA262.\n\n### keyword\n\n#### keyword.isKeywordES5(id, strict)\n\nReturns `true` if provided identifier string is a Keyword or Future Reserved Word\nin ECMA262 edition 5.1. They are formally defined in ECMA262 sections\n[7.6.1.1](http://es5.github.io/#x7.6.1.1) and [7.6.1.2](http://es5.github.io/#x7.6.1.2),\nrespectively. If the `strict` flag is truthy, this function additionally checks whether\n`id` is a Keyword or Future Reserved Word under strict mode.\n\n#### keyword.isKeywordES6(id, strict)\n\nReturns `true` if provided identifier string is a Keyword or Future Reserved Word\nin ECMA262 edition 6. They are formally defined in ECMA262 sections\n[11.6.2.1](http://ecma-international.org/ecma-262/6.0/#sec-keywords) and\n[11.6.2.2](http://ecma-international.org/ecma-262/6.0/#sec-future-reserved-words),\nrespectively. If the `strict` flag is truthy, this function additionally checks whether\n`id` is a Keyword or Future Reserved Word under strict mode.\n\n#### keyword.isReservedWordES5(id, strict)\n\nReturns `true` if provided identifier string is a Reserved Word in ECMA262 edition 5.1.\nThey are formally defined in ECMA262 section [7.6.1](http://es5.github.io/#x7.6.1).\nIf the `strict` flag is truthy, this function additionally checks whether `id`\nis a Reserved Word under strict mode.\n\n#### keyword.isReservedWordES6(id, strict)\n\nReturns `true` if provided identifier string is a Reserved Word in ECMA262 edition 6.\nThey are formally defined in ECMA262 section [11.6.2](http://ecma-international.org/ecma-262/6.0/#sec-reserved-words).\nIf the `strict` flag is truthy, this function additionally checks whether `id`\nis a Reserved Word under strict mode.\n\n#### keyword.isRestrictedWord(id)\n\nReturns `true` if provided identifier string is one of `eval` or `arguments`.\nThey are restricted in strict mode code throughout ECMA262 edition 5.1 and\nin ECMA262 edition 6 section [12.1.1](http://ecma-international.org/ecma-262/6.0/#sec-identifiers-static-semantics-early-errors).\n\n#### keyword.isIdentifierNameES5(id)\n\nReturn true if provided identifier string is an IdentifierName as specified in\nECMA262 edition 5.1 section [7.6](https://es5.github.io/#x7.6).\n\n#### keyword.isIdentifierNameES6(id)\n\nReturn true if provided identifier string is an IdentifierName as specified in\nECMA262 edition 6 section [11.6](http://ecma-international.org/ecma-262/6.0/#sec-names-and-keywords).\n\n#### keyword.isIdentifierES5(id, strict)\n\nReturn true if provided identifier string is an Identifier as specified in\nECMA262 edition 5.1 section [7.6](https://es5.github.io/#x7.6). If the `strict`\nflag is truthy, this function additionally checks whether `id` is an Identifier\nunder strict mode.\n\n#### keyword.isIdentifierES6(id, strict)\n\nReturn true if provided identifier string is an Identifier as specified in\nECMA262 edition 6 section [12.1](http://ecma-international.org/ecma-262/6.0/#sec-identifiers).\nIf the `strict` flag is truthy, this function additionally checks whether `id`\nis an Identifier under strict mode.\n\n### License\n\nCopyright (C) 2013 [Yusuke Suzuki](http://github.com/Constellation)\n (twitter: [@Constellation](http://twitter.com/Constellation)) and other contributors.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n  * Redistributions of source code must retain the above copyright\n    notice, this list of conditions and the following disclaimer.\n\n  * Redistributions in binary form must reproduce the above copyright\n    notice, this list of conditions and the following disclaimer in the\n    documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\nARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\nDIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\nON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\nTHIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-05-10T18:27:50.347Z", "created": "2013-11-25T07:21:04.038Z", "1.0.0": "2013-11-25T07:21:07.243Z", "1.1.0": "2014-06-30T04:09:38.261Z", "1.1.1": "2014-07-12T13:30:19.256Z", "1.1.2": "2014-07-12T13:36:53.753Z", "1.1.3": "2014-07-12T14:04:04.545Z", "1.1.4": "2014-07-12T14:17:51.275Z", "1.1.5-dev": "2014-11-22T13:32:42.793Z", "1.1.5": "2014-11-22T13:32:54.902Z", "1.1.6": "2014-11-22T14:05:52.024Z", "2.0.0": "2015-03-12T19:02:52.788Z", "2.0.1": "2015-03-14T17:01:06.068Z", "2.0.2": "2015-03-14T17:12:36.530Z", "2.0.3": "2019-07-31T01:10:54.228Z"}, "repository": {"type": "git", "url": "git+ssh://**************/estools/esutils.git"}, "homepage": "https://github.com/estools/esutils", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "readmeFilename": "README.md", "users": {"allain": true, "kaizendad": true, "shuoshubao": true, "usex": true, "flumpus-dev": true}, "license": "BSD-2-<PERSON><PERSON>"}