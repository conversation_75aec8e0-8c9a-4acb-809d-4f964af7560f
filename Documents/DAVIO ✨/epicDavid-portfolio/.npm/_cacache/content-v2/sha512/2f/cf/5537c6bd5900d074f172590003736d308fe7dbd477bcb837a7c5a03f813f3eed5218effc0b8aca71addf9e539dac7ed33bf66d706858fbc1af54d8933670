{"_id": "leven", "_rev": "41-c4adcfd36d7a1c6770cc05e985ce73f0", "name": "leven", "description": "Measure the difference between two strings using the Levenshtein distance algorithm", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "leven", "version": "1.0.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/leven"}, "bin": {"leven": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js", "bench": "matcha bench.js"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "compare", "comparison"], "devDependencies": {"ava": "0.0.3", "fast-levenshtein": "^1.0.3", "ld": "^0.1.0", "levdist": "^1.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^0.1.0", "matcha": "^0.5.0", "natural": "^0.1.28"}, "gitHead": "7e2b14a90a0712aa84b6a3e13e081b042610e4d1", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven", "_id": "leven@1.0.0", "_shasum": "ab55e2765c6434485e13d1603bfbe4b434412908", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ab55e2765c6434485e13d1603bfbe4b434412908", "tarball": "https://registry.npmjs.org/leven/-/leven-1.0.0.tgz", "integrity": "sha512-/tB6kGud370orPITxKx7rODYaCO7oPmouPA7XXVJRCDFpW773Nz0npXZwR2RdZv4DJt3RKQV4qF9HWMUssy/4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+cKiSF7JdEdaNjxUQMeyXOtCXWak47AYVmBFZNCLBPAIgRanA5vGe8SjIsTHkBfuumIRgBc3KyKMIQlS44bWkvHI="}]}, "directories": {}}, "1.0.1": {"name": "leven", "version": "1.0.1", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/leven"}, "bin": {"leven": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js", "bench": "matcha bench.js"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "compare", "comparison"], "devDependencies": {"ava": "0.0.4", "fast-levenshtein": "^1.0.3", "ld": "^0.1.0", "levdist": "^1.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^0.1.0", "matcha": "^0.5.0", "natural": "^0.1.28"}, "gitHead": "16bf343c490aafa597aab1b00a5329a1bc3ef46c", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven", "_id": "leven@1.0.1", "_shasum": "98944f5e868c8c351797bb23e8b6752852fc8ba1", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "98944f5e868c8c351797bb23e8b6752852fc8ba1", "tarball": "https://registry.npmjs.org/leven/-/leven-1.0.1.tgz", "integrity": "sha512-NVHMc5KNOU1MFREfbQFs3t7w4wP6ok0xPhuJO7VQeAPaWMrJQuWw+gsJZ5qLdZi+Uyxjr/6Vbwzafaz9PAlCrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEV+EE50XuvbuErpMWYPcBFLsnabcnUrKXAHFXX2DSXVAiEA2kN3TBO9za6f1vFstVGvz0FtlaFZ7JSbcCU+vDf4NMI="}]}, "directories": {}}, "1.0.2": {"name": "leven", "version": "1.0.2", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/leven"}, "bin": {"leven": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js", "bench": "matcha bench.js"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "0.0.4", "fast-levenshtein": "^1.0.3", "ld": "^0.1.0", "levdist": "^1.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^0.1.0", "matcha": "^0.6.0", "natural": "^0.2.1"}, "gitHead": "c5035efd44401a8a43dfd0bead24c51386499e4b", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven", "_id": "leven@1.0.2", "_shasum": "9144b6eebca5f1d0680169f1a6770dcea60b75c3", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9144b6eebca5f1d0680169f1a6770dcea60b75c3", "tarball": "https://registry.npmjs.org/leven/-/leven-1.0.2.tgz", "integrity": "sha512-U3eIzC2mMAOMOuoJ25sA3eyraoBwndpQyYgBq5dyqrMTpvMg9l9X/ucFHxv622YcCg179WWqleoF7rSzfYrV+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWfy4J2igWvKBcRfPH52v02BV663eVR2/DudyPK30w0QIhAKbek4pZMgumOtdgTgqA36PbRKIkjhkmAn352akZ19Ce"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "leven", "version": "2.0.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/leven"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "*", "fast-levenshtein": "^1.0.3", "ld": "^0.1.0", "levdist": "^2.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^1.0.0", "matcha": "^0.6.0", "natural": "^0.2.1", "xo": "*"}, "gitHead": "cc8d61ca30cbe61b8a5d13741f8aa2f4901b1395", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven", "_id": "leven@2.0.0", "_shasum": "74c45744439550da185801912829f61d22071bc1", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "74c45744439550da185801912829f61d22071bc1", "tarball": "https://registry.npmjs.org/leven/-/leven-2.0.0.tgz", "integrity": "sha512-vEUhG2rMaPssbENCz33fBBnsrVkaEechJ0FATfXm8TE9owwt16XVE5r4GbU9Eg/bcddNfI0VIHkYmI2Y8PelDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE3GHGHv44UN1OkB5AVLujljblCwySDfoyGIchdOsY5mAiEAq+xRPATjEZ/j9rOw8cn+NXWbdWdVuySoj1NgPn9rJac="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "leven", "version": "2.1.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/leven.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^0.17.0", "fast-levenshtein": "^2.0.5", "ld": "^0.1.0", "levdist": "^2.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^2.0.0", "matcha": "^0.7.0", "natural": "^0.4.0", "talisman": "^0.18.0", "xo": "^0.16.0"}, "gitHead": "0630566a69b5a73aae2e52bb47ea863892a4b5f0", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven#readme", "_id": "leven@2.1.0", "_shasum": "c2e7a9f772094dee9d34202ae8acce4687875580", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c2e7a9f772094dee9d34202ae8acce4687875580", "tarball": "https://registry.npmjs.org/leven/-/leven-2.1.0.tgz", "integrity": "sha512-nvVPLpIHUxCUoRLrFqTgSxXJ614d8AgQoWl7zPe/2VadE8+1dpU3LBhowRuBAcuwruWtOdD8oYC9jDNJjXDPyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHzh4Y6yOJBKZeHVv0D0wW53rmZrw7Zv2uX8E3LLUf3pAiALGaEyUuk+mDzU/zQ0PpvzbsuSyIgBGQI4sY0V012niA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/leven-2.1.0.tgz_1487405796149_0.5572073105722666"}, "directories": {}}, "3.0.0": {"name": "leven", "version": "3.0.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/leven.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check", "bench": "matcha bench.js"}, "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^1.3.1", "fast-levenshtein": "^2.0.5", "ld": "^0.1.0", "levdist": "^2.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^2.0.0", "matcha": "^0.7.0", "natural": "^0.6.3", "talisman": "^0.21.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "254cdab92b2a3154dfb219ec0fcaa9645096e0f1", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven#readme", "_id": "leven@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SXxMC8wC6BkBibsNUC3CQPT8Ui7eA5D3FuYp9VTzB3TMufAcKwjuLZd6RzVKfHBaWmZjGnH/aGmOmOk+Ovd1dw==", "shasum": "04e58837c50a6293af03b4dea25ad86ef2c19637", "tarball": "https://registry.npmjs.org/leven/-/leven-3.0.0.tgz", "fileCount": 5, "unpackedSize": 5011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch0sUCRA9TVsSAnZWagAAXAMP/RogpNZ9dylQWgQArQji\nXq+vZg/foH58jf6GwfhbmcgfUq94Kjv/E29Gf684Sx87pL5hZM0aM9BDTvyl\nd2FJhP4ZUfZu0WXV9KRussjCjGFkdBrd7qA8ocLo9MoZoI2/rCrG/5bbACWf\na+YWonfnB9HJfnHR1/pPgYSeLeL56AS5GRTRYL1QjA0E73sXWdQ9x4Ygcb5v\n+10Wh5K7HlimUD2HSaWugGEeP9gYlJT31eKiUoY+uBK3jvly6iqhVEqMQP4i\nYKj+b+G+8sKykFNzdQ973ULySrjXWSl0Lwsdmt49fJUv/MvxiT9Tnyxyh7Sn\nqy8rT2AP2suy1c5eXrSLkvFlEQqYgIurPppWCTio5P9QbpVOVE9KwnUhuuRT\npa+R/rCsmFHo+bFzUiLvHYZEQGWyB60qZ1VqQ/DiYMxqPl5sx4DBKHtH6qCv\njSvMC8NRdIrPMRwyANLmrGM4PXomyoOwl3tcei3VPXPJmpNV/2RI/Izy7Q22\npPlvr2SgwO7brsMm3J/EYSFbO6g3ZRJ8fTXWhklnP+qOCkm0+JTuEN3+l/XE\n3hW0N7oqbOxf2BB6Zn3qjsTf14qHaTcO9wN9AY7t4CuTAeaM72FQp7RC99vE\nqkwiZ05FicYz4Poy5Kc02Qj6JJhoEGtTYeRKkz5EPWcejNingNLkI9CJ2KjT\nDf33\r\n=0NpV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtdRxku0lT2f9fxBoD/lNgcK1VoNVhZhSL2DhshRrknwIhAM8DmR73DpsJIfdyrIOfW5ugNYNPxJH1ubEgzc7CbcMb"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/leven_3.0.0_1552370452233_0.47335064155668505"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "leven", "version": "3.1.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/leven.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha bench.js"}, "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^1.4.1", "fast-levenshtein": "^2.0.6", "ld": "^0.1.0", "levdist": "^2.2.9", "levenshtein": "^1.0.5", "levenshtein-component": "^0.0.1", "levenshtein-edit-distance": "^2.0.3", "matcha": "^0.7.0", "natural": "^0.6.3", "talisman": "^0.21.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "749476f3ef2cb958cace9ca1327e1928fd8d10ea", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven#readme", "_id": "leven@3.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==", "shasum": "77891de834064cccba82ae7842bb6b14a13ed7f2", "tarball": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "fileCount": 5, "unpackedSize": 5342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpY5ACRA9TVsSAnZWagAAuJ4P/3K1gYYX1x1zcCX3nASD\nPG4TR2BkTfrUVr2jAxmoiUlak77VT4Np/wAcqWBaKfKJ7aNVueCFKxNusXKA\nVQNmKP0JwFuZ7eq9TzBUbFPCdPXhsIv1wv2SAdH2T0C3U+F9brE6YpQHIHMS\niE5teQYH6PPuZldNOJyyHCV0sKUriihcmqHV3kHvme8ToATiBqorLnV6oQoX\nlYY6PFOX4kUWl3FAh+RNElk7C9X9LxeNr1LvYwV8WEr/rLB//MQjzG8+34LD\nZabbdXy6eCE9IHe+OyUqNRNz7q/yITap34G0q6OGz7fThyYpWdZVzaY5yWry\nMTTit3qK6fwuzsADhUTv9m4H70OKcJaKeb0Z3ahre+JcNfvpA5sRJnxYxh88\n0lqxPjp2fGqpxritXLuDf+CIQJNVL+3Z4x8stNqFCW4SqRolZRvH4+Nh/REX\ntzAfbGIlp61VQ0YyWufVsHps67itHjwe6z3sE6jiR0UQUR0LELWG2jTLluld\nnmUL8S4p5dn4fE5rOloa7hewz4RHQBU2N9mOTbn3LAqueR+DiXp/8Qxa7feY\nRP72chzHD0l39JfWGeTuWHuAXQw7gttv34LmMEuTbZCFESfLd8tV0yQ4qQy+\niAHp16TJiQ9/EZebxdlJxkBXnqS24+8bie8fgKxP4ww0dKTihqRMZ3BcxTJL\nrLBF\r\n=BC8g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEu2uyL+hIso9KRBY6iBNq9LzxNAhwgDLLYwUTCwEmWLAiAnjDSXTnUfQHz65kOshaoBv/rtANu77n7atqU2beZ3QQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/leven_3.1.0_1554353727812_0.5760859331516206"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "leven", "version": "4.0.0", "description": "Measure the difference between two strings using the Levenshtein distance algorithm", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/leven.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha bench.js"}, "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^3.15.0", "fast-levenshtein": "^3.0.0", "ld": "^0.1.0", "levdist": "^2.2.10", "levenshtein": "^1.0.5", "levenshtein-component": "^0.0.1", "levenshtein-edit-distance": "^3.0.0", "matcha": "^0.7.0", "natural": "^5.0.4", "talisman": "^1.1.4", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "447058af4df87e45724ad608c8892eb263aa1276", "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "homepage": "https://github.com/sindresorhus/leven#readme", "_id": "leven@4.0.0", "_nodeVersion": "16.2.0", "_npmVersion": "7.13.0", "dist": {"integrity": "sha512-puehA3YKku3osqPlNuzGDUHq8WpwXupUg1V6NXdV38G+gr+gkBwFC8g1b/+YcIvp8gnqVIus+eJCH/eGsRmJNw==", "shasum": "b9c39c803f835950fabef9e122a9b47b95708710", "tarball": "https://registry.npmjs.org/leven/-/leven-4.0.0.tgz", "fileCount": 5, "unpackedSize": 5138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEm4/CRA9TVsSAnZWagAApXAQAJenqPCn0bfPs/3K2CBi\nxtwcMcn66zToMW62o59d9SWeWqNwvglpIbI7VkvYBDyHXZFqYHlj5nRdecyZ\nl/0II6edWfmhNlk5FFv8QCGqmjZOxKVWFRIdv4ilbWtZif2Qu4sHHPV0074p\nMbkU3hut1DxIAroCaEC6XMvLjsHC0mnSYGKkhCAWw0zY3uXyyH53YOc8Tvr7\nX8W6bX2qnkTaJx7f+Zn1Ck4xq3gwnie8+fp/DdjJ6Rdt18s9wPRHQ0CCNKvm\nK/Zu9pUULolp5hrpXGiBSQKU4bX1o4D2CzA/9YZ3wkBndsBb6JEwr4iPFPz8\nrV3ICVdxJd+9K8zSA3l2h71poQW90Ne4xkIjhq9VRdFfoPoHNKveoKZGv9xb\nsP/bm3dMovjGPcAj9DnjunOeFbhqOKlQQdI0oOiLula4+afR3CJhta8zgBk0\nEjlWlE++flmQQ+VXCIywAKd9ilzZuVve5Wff0MLaA0vj+6lZf6eCChZDIs4G\nfzQ3LX6xjsIBpiXVL9DFcFyZsX6Ih2jz4J34pp0+Pp+ZHvKIQPTN9UR4kAZ5\nJ/bnyYUGcvYy1FwDn37qHwsG2cNw8ve2giuNSzMwU2S1agRkH1cdjpU4uccS\nbGEIztUfbwSILBnoSp4u3CiO/e/kNZE52IRHrtLvMPt0165ye1xzNdXoYt10\nbv1c\r\n=8+EM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5hw38DxrztcRAU2amGLyZnfGdI/XWJJuF01Z1pMoDXQIgEIEBcyQ7rdySEnlBAhP/exFTJMewGrinnKsM16uFlEM="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/leven_4.0.0_1628597823183_0.03613786828444443"}, "_hasShrinkwrap": false}}, "readme": "# leven\n\n> Measure the difference between two strings using the [Levenshtein distance](https://en.wikipedia.org/wiki/Levenshtein_distance) algorithm\n\n## Install\n\n```\n$ npm install leven\n```\n\n## Usage\n\n```js\nimport leven from 'leven';\n\nleven('cat', 'cow');\n//=> 2\n```\n\n## Related\n\n- [leven-cli](https://github.com/sindresorhus/leven-cli) - CLI for this module\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-leven?utm_source=npm-leven&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T11:54:02.407Z", "created": "2014-08-09T20:36:43.382Z", "1.0.0": "2014-08-09T20:36:43.382Z", "1.0.1": "2014-10-07T17:23:37.460Z", "1.0.2": "2015-05-16T03:31:53.449Z", "2.0.0": "2015-09-07T08:47:31.501Z", "2.1.0": "2017-02-18T08:16:38.032Z", "3.0.0": "2019-03-12T06:00:52.366Z", "3.1.0": "2019-04-04T04:55:27.958Z", "4.0.0": "2021-08-10T12:17:03.365Z"}, "homepage": "https://github.com/sindresorhus/leven#readme", "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/leven.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/leven/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"finnpauls": true, "edin-m": true, "mimmo1": true, "j.su": true, "jtbrinkmann": true, "tur-nr": true, "itskdk": true, "ethan_": true, "danielkalen": true, "farskipper": true, "heartnett": true, "dada1134": true, "wkronmiller": true, "zillding": true, "thuanhai": true, "thetimmaeh": true, "restuta": true, "monjer": true, "mysticatea": true, "simonfan": true, "shanewholloway": true, "wolfram77": true, "keyn": true, "seangenabe": true}}