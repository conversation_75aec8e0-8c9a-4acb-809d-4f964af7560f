{"_id": "csstype", "_rev": "90-160788e412ef35c3fa210bbf9ce3d977", "name": "csstype", "description": "Strict TypeScript and Flow types for style based on MDN data", "dist-tags": {"latest": "3.1.3", "version-2": "2.6.21"}, "versions": {"1.0.0": {"name": "csstype", "version": "1.0.0", "main": "index.d.ts", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.2", "@types/jest": "^20.0.3", "@types/node": "^6.0.0", "@types/prettier": "^1.6.1", "chokidar": "^1.7.0", "jest": "^21.1.0", "mdn-data": "1.0.0", "prettier": "^1.7.0", "ts-node": "^3.3.0", "tslint": "^5.7.0", "typescript": "^2.5.2"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "prettier": "prettier --single-quote --write build.ts */*.ts", "test": "jest", "prepublish": "npm run test && npm run build"}, "description": "TypeScript definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.", "_id": "csstype@1.0.0", "dist": {"shasum": "61976ba61e6fe1aadd00a531cc86fa10f190af52", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.0.0.tgz", "integrity": "sha512-e2RXEbQeTyl1ot019KmcvNOBDa6AB4ZTl8f5k8xfy9xXQV+VLaR5GFzLcLtQU19pL/V1X8t8PameUwVt85Zjpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/2CI+fGH/gQbmO+z2feLVhQhZEos/h+eG9ZPwsWmCQgIgczx+NqavQsV4rMXXa2o9QskejAD3GYQwYBhk6T31QNA="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.0.0.tgz_1506291265016_0.6202688971534371"}, "directories": {}}, "1.0.1": {"name": "csstype", "version": "1.0.1", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "https://github.com/faddee/csstype"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.2", "@types/jest": "^20.0.3", "@types/node": "^6.0.0", "@types/prettier": "^1.6.1", "chokidar": "^1.7.0", "jest": "^21.1.0", "mdn-data": "1.0.0", "prettier": "^1.7.0", "ts-node": "^3.3.0", "tslint": "^5.7.0", "typescript": "^2.5.2"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "prettier": "prettier --single-quote --write build.ts */*.ts", "test": "jest", "prepublish": "npm run test && npm run build"}, "_id": "csstype@1.0.1", "dist": {"shasum": "d9f97fd4d93e613b6881c29639f1745f7bf5aaf5", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.0.1.tgz", "integrity": "sha512-InYpYUdOqZSlqXBf0S3AUHFSWkUn7Sz2XCHi7BECnrgj/4YDqqQc29BOgqKzpwAY4KkcbOFLy/FNqiX+vUbRtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWbhDv2Uekw2O7jutuBXTfIorKi8uMxyTnPa471h1khwIgXnH85EYrT5FNG4nelR0JwQK2NOOM4Q1IDOygVTSE7rQ="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.0.1.tgz_1506365446641_0.6749728270806372"}, "directories": {}}, "1.1.0": {"name": "csstype", "version": "1.1.0", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "https://github.com/faddee/csstype"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.2", "@types/jest": "^20.0.3", "@types/node": "^6.0.0", "@types/prettier": "^1.6.1", "chokidar": "^1.7.0", "jest": "^21.1.0", "mdn-data": "1.0.0", "prettier": "^1.7.0", "ts-node": "^3.3.0", "tslint": "^5.7.0", "typescript": "^2.5.2"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "prettier": "prettier --single-quote --write build.ts */*.ts", "test": "jest", "prepublish": "npm run test && npm run build"}, "_id": "csstype@1.1.0", "dist": {"shasum": "504fe0cb1b2ede48373beb42fa65255b2ae17828", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.1.0.tgz", "integrity": "sha512-2pERbeHV3SqwP0Izje+T332tSoXQtySzu5AMJcSlCmILH88/oGRhctqpD+ZcF1IuW9K81qqTuAHWT0MqHpz4Rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClZ3ZCjswBEaG7t3YjU4h6YS/Zu+T3mbv2PJzfPonXbQIgNy4qfw4t/CEqPwng8pTa/wa8XaG2F+lIBHOwXHQUK8U="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.1.0.tgz_1506379016650_0.6128593061584979"}, "directories": {}}, "1.2.0": {"name": "csstype", "version": "1.2.0", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.2", "@types/jest": "^22.0.1", "@types/node": "^9.3.0", "@types/prettier": "^1.6.1", "chokidar": "^2.0.0", "jest": "^22.0.6", "mdn-data": "1.0.0", "prettier": "^1.7.0", "ts-node": "^4.1.0", "tslint": "^5.7.0", "typescript": "^2.5.2"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "prettier": "prettier --single-quote --write build.ts */*.ts", "test": "jest", "prepublish": "npm run test && npm run build"}, "gitHead": "8dc677750f5a5500a6589bca8f2238c0e822bddd", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-CBeFdD7f3NlyggFnDdHrkck8KkxHfI2TlIwrQoCHptJZ9yDi1q/CVGC4pRMf/TijqEJ37bD0QNNzF+11BfKSaA==", "shasum": "ae6d8239a51ced52fe359b38d8b19a75747fc8cd", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEuKrOcf9VfbhrL+iBFk5d/oxLMGW82E+9ENBtIFjaWvAiEArpQlPTcGVQpVVVLz/X8r5lz/L3d03A5rj+IpbpSLlcM="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.2.0.tgz_1516034182500_0.2088893661275506"}, "directories": {}}, "1.2.1": {"name": "csstype", "version": "1.2.1", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "https://github.com/frenic/csstype"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.2", "@types/jest": "^22.0.1", "@types/node": "^9.3.0", "@types/prettier": "^1.6.1", "chokidar": "^2.0.0", "jest": "^22.0.6", "mdn-data": "1.0.0", "prettier": "^1.7.0", "ts-node": "^4.1.0", "tslint": "^5.7.0", "typescript": "^2.5.2"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "pretty": "prettier --single-quote --write build.ts **/*.ts", "test": "jest --no-cache", "prepare": "tsc && npm run test && npm run build"}, "files": ["index.d.ts"], "_id": "csstype@1.2.1", "dist": {"shasum": "66f56173990062e7cbb16245c7a2736f236e5b1f", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.2.1.tgz", "integrity": "sha512-t9LE9jHFdcNSWfIXAgUlUKv3n8VHh2T3F3O5s/nUY1l+/JnRr0RUmlbSgSSGBqbpxfv5bRrdDmxXBLqMhKepyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHx3m2NF1gEXR9fMjeUQSLAHOTT4qEHMAURU5GLyvqWoAiEA2lglSiOlak2YvK1/FLOSvpr+0pAYwby+B7OYOrBNraA="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.2.1.tgz_1516051878357_0.4801400254946202"}, "directories": {}}, "1.3.0": {"name": "csstype", "version": "1.3.0", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "https://github.com/frenic/csstype"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.2", "@types/jest": "^22.0.1", "@types/node": "^9.3.0", "@types/prettier": "^1.6.1", "chokidar": "^2.0.0", "jest": "^22.0.6", "mdn-data": "1.0.0", "prettier": "^1.7.0", "ts-node": "^4.1.0", "tslint": "^5.7.0", "typescript": "^2.5.2"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "pretty": "prettier --single-quote --write build.ts **/*.ts", "test": "jest --no-cache", "prepare": "npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts"], "_id": "csstype@1.3.0", "dist": {"shasum": "d4c69b4ddf3c096a6e5528aa5b997e9ecd0f4c42", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.3.0.tgz", "integrity": "sha512-mDkp0PzjTZ0vdwzr5dtzem6xNlCgtYX7L+/k0rkNLz8nwum0c22PetT4bCCK8aG9pjXvT7EXuW+meZ54b0NdyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhWrnwre+IB1U1i7isNqY5kcxlB2VFcT3dznmBe8TmUAiEAmTc73LDqgvJzGLulLgUzoFvmaMoTnpDwnOWIh0NRwR0="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.3.0.tgz_1516263262041_0.07712730811908841"}, "directories": {}}, "1.4.0": {"name": "csstype", "version": "1.4.0", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.0", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "jest": "^22.1.4", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "test": "jest --no-cache", "prepublish": "tsc && npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts"], "gitHead": "aec469f18db94f5c5b90aec7d5145cfd948481d8", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jg3JxdFNyJHL4L6xcHP2Jf/OSWNcvQlrvhFYmaRdz0KvknN3SZytaLJIx6/IukCATZyEDyD7Llbet42Lp5qo0Q==", "shasum": "96037936eb79a6010c1fbe0f3230fc2e307e6474", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.4.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH9cxIU/Y2QcPA0250HddoFEPNeYKRYsZz/1jMoNQ/lSAiAP0IWwJ5IcrlQsaZ7wO/2bOOS0ORb8AOnwAJzNdLXJKQ=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype-1.4.0.tgz_1517617385146_0.1266497978940606"}, "directories": {}}, "1.5.0": {"name": "csstype", "version": "1.5.0", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.0", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "jest": "^22.1.4", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "sloppy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "prepublish": "tsc && npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts"], "gitHead": "9bf9714d6600a8a2a8ba68b096829e1433b69047", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-m/JGQ66q9wL6esUV9eyIyEx7SizCGz6SvJFKWKA2gg1UpdkYY5zHDE8N4153NQsNp/8dGlCWbWddNqCSPbxTrQ==", "shasum": "8a5e76e198dceb4cbf02f21e5ed5c73295351dec", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.5.0.tgz", "fileCount": 3, "unpackedSize": 118735, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgCbJl5VRyBFJGiP3S2/8rZgHyJLEv5KcsVJqVAHibxgIgLviTRSmtkSA0wHjA/xNTuIwT1zl1eJ80Uj1AII02egg="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.5.0_1518036715095_0.5685212883533706"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "csstype", "version": "1.6.0", "main": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "sloppy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "prepublish": "tsc && npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts", "flow-typed/"], "gitHead": "e172ec5da40a8d98844d2fdf8487e029bf1d6fd2", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.6.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1A6eqoDM9P6w1BS7Q7uz1FiFcle6KNirqzK+PEweWQTdQWq82fRrFfpPYqqovEs4ckJLDDgKPtmiMKVzZY/OfQ==", "shasum": "130ca7a80a7488fd46e383bf8be90ee82cacba69", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.6.0.tgz", "fileCount": 4, "unpackedSize": 257475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDy+nnlpWIG3N1EfVAeQmQ6e4gggDOpE/marqpk5svQWAIhAP1J3sQ1WbPyj/WFk3r/hE7IQvDew/UFt2t4lPK4aMv8"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.6.0_1518447005089_0.28686430113910144"}, "_hasShrinkwrap": false}, "1.7.0": {"name": "csstype", "version": "1.7.0", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "sloppy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "prepublish": "tsc && npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts", "flow-typed/"], "gitHead": "71763c0848f6487b9a04cfb45ff638bbf2bfbec3", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.7.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Xb4HqrOem2uyre30MzlvbQRzAH9nk9VC9wxC+WJhrjTkjvHXueRc8aLUFUOl/GY7hbVfbRyNjqpIMlXQt4j3XQ==", "shasum": "3bab7b4d8c1af5cc2a75cfc5daf3cb31f1d0cc30", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.7.0.tgz", "fileCount": 4, "unpackedSize": 290054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDC5Tv9DWZRydp9L8jiX9YZkZlRIdqsQPNvQrNa2BiZsAIhAJ9wDeXL1RpUdwYiOee7JfLvldCBrxXrhHpVcyx32Z4z"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.7.0_1518561171689_0.005778479531661107"}, "_hasShrinkwrap": false}, "1.7.1": {"name": "csstype", "version": "1.7.1", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "prepublish": "tsc && npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts", "flow-typed/"], "gitHead": "34da3dd43f366fa3255e54d576b8f9024346995e", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.7.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-++gHYRtfmVki16HtN+HMj2IuXNYHbH4FEZWf4b7jAfPf0XicXY4xL8POVQwUmhIutqpoRZiQJdlwlB/AI5B4oQ==", "shasum": "63b719b14f196b9049bed35b24aa938bc9cd10d3", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.7.1.tgz", "fileCount": 4, "unpackedSize": 290750, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/ciJ5Ebpu1IZoTMfeYQj6L+i47VAGe0uVLau099FSyAiAMCwaf4/Ys59AcwW8Gz8znitAL1MXcKcKXLp2TM9vcHg=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.7.1_1518703419206_0.4041801655711339"}, "_hasShrinkwrap": false}, "1.7.2": {"name": "csstype", "version": "1.7.2", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "prepublish": "tsc && npm run test && npm run build && tsc typecheck.ts --noEmit"}, "files": ["index.d.ts", "index.js.flow"], "gitHead": "c85f57973cb7c81513d6080718b84b6805ee04a0", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.7.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uppIo/duELe9wLf7HF1adTpXbaD1dyzjuXRzQCmk61PUuuHpHqK54MCT3pqr9SkwO6bsFCjYlqMFEDP+7WqvPg==", "shasum": "aecd408172a24f02114c1fd98322a9b595e13b70", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.7.2.tgz", "fileCount": 4, "unpackedSize": 275493, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChulvfIgn9yPPVBHuvPb+l8dPCQWr1b88F4JOXMXt1WgIgSzKeTLatLCcxxS0wgR8L2dwmoY0T+tlcoaWxeWZaNag="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.7.2_1519024716924_0.035244845988325"}, "_hasShrinkwrap": false}, "1.8.0": {"name": "csstype", "version": "1.8.0", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "gitHead": "6bd103a1d97a61320ad86f131a362edc71031b45", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.8.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/Fc/D7lAIllf7zT6ldeSBLFnI2EgUqIQFuGkRmtnecw165IYzg4qr/g/8rVr+R19K8btmJY8U9PJw7RfTiCRCg==", "shasum": "0c93ebcbfe8f42d1ab835b88bc8f3046de6261c5", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.8.0.tgz", "fileCount": 4, "unpackedSize": 295416, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEvHCALp4OScUzOkkrE+WFYbkMRohHeoaM4wu/Efn18AIhANyn4TUkFteHoBcMhLJvV2MtucnQutC+khWIo5mJFsAP"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.8.0_1519294084773_0.9674745125803872"}, "_hasShrinkwrap": false}, "1.8.1": {"name": "csstype", "version": "1.8.1", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "gitHead": "bf399b5414c55b7ec4936e434b2e5aa4a10eb877", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.8.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HwdDPxRqEfXqKT3IN8iY228AbZ9xNcpTyACtxm7TGu5d9GoL9ve1W687dEhNHY+qlgXN65EeRMCHanROpVb/dA==", "shasum": "90c57913cabd3a6e53140f3545593729e6a80cf6", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.8.1.tgz", "fileCount": 4, "unpackedSize": 295048, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQTiKa+il8RAD+ptjQZbOQ0HTwN98BbDvscTxh+NffxwIhAJipk54WeRi1W3kM3htxSpMLOuBV4bFAM8izLktR51Wp"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.8.1_1519908301137_0.02690229790153209"}, "_hasShrinkwrap": false}, "1.8.2": {"name": "csstype", "version": "1.8.2", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.65.0", "jest": "^22.2.1", "mdn-data": "1.1.0", "prettier": "^1.10.2", "ts-node": "^4.1.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "gitHead": "93369fa5252d801d1f1d79b35e1913a26edb541f", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@1.8.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZHl+k5s5MfzyruSVuDj7l51AQXPdqbzHUEplasMBzcL2sD/jgmu+QZyCjG1AlcxjUGcsuPJfzyRl4+9GuzHAfA==", "shasum": "2c0f16da08b99f13fe7fbb242e87d1a19dbe77a7", "tarball": "https://registry.npmjs.org/csstype/-/csstype-1.8.2.tgz", "fileCount": 4, "unpackedSize": 294902, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBRn+N2B96Fx/+/PegEznVBUebrvNuBuJMTDLspArSdgIhAOOLU2FsXyxo6Sg0mixEcqF+tiMqtw3qyp5VC5uAc2FS"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_1.8.2_1520031581299_0.6970851740495649"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "csstype", "version": "2.0.0", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.68.0", "jest": "^22.2.1", "mdn-data": "git+ssh://**************/mdn/data.git#7905ee69f51248a1f3b506f31078f08f1fd7d5c4", "prettier": "^1.10.2", "ts-node": "^5.0.1", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "gitHead": "7da722549ea31afa6ee57347bfdd046f687b34a8", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3e8c5b/Fyhh5iv+QV0y9kCAcWn8tw/QxfN994GfmTj5u9oJrUH/Ktg1OZsMAzOviL/MqS1mJnlnia4QvC9WI/g==", "shasum": "7d199dd8dca409077e81569eca0c71a74c4f4158", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.0.0.tgz", "fileCount": 4, "unpackedSize": 323303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtUw7aLMdQkqJlGw4h/Gy65OLOH54nYkezkf1kxOLSLAIgE8Qx7UNyu8IJOUuCrXAOTjb093c5nk2TzHaKJVRDkAE="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.0.0_1521451430325_0.44692611947077765"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "csstype", "version": "2.1.0", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.68.0", "jest": "^22.2.1", "mdn-data": "git+ssh://**************/mdn/data.git#7905ee69f51248a1f3b506f31078f08f1fd7d5c4", "prettier": "^1.10.2", "ts-node": "^5.0.1", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "0ca815ad1dccaac9ca438da87bd287dde624d886", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+7+cT9jVBoYMTcXH2Sc4pQlGhsDYuZPuSA+Ay9zuZm9UuSIFrkj2mZaYK5ty1hZSM0ZCuzVEKtZIF0cdGl84og==", "shasum": "4a84bba2717d06549f391ac4eb7651aa1b7d0976", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.1.0.tgz", "fileCount": 4, "unpackedSize": 352772, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHHH0JzCxU8+cgB2lCqGammX7Lyd+0/pdPcuq8a5Cm8xAiEA+WzGDVFtwi1b8mIvN337NMREjmJWXKVaktC2mfrbKGI="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.1.0_1522866392449_0.9131601882064218"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "csstype", "version": "2.1.1", "main": "", "types": "index.d.ts", "description": "TypeScript definitions for CSS", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.68.0", "jest": "^22.2.1", "mdn-data": "git+https://github.com/mdn/data.git#9513169e8dd59e5c076e879ac462af2d386b4699", "prettier": "^1.10.2", "ts-node": "^5.0.1", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "0fca3b4a57d1eb989d51f06b4190180726ba4320", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YsNVkaQtmsauSmlwqr/3EhJamZIObOcqfOgOmPuQxEXhsSvt/1/4M+bqN9xpsSEJqT2TWfTs2mPWrmwp0iQX6g==", "shasum": "37b01a7a9958ef0b88167ff6678deccd732e0ae2", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.1.1.tgz", "fileCount": 4, "unpackedSize": 358450, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCizY3rL0NFlfCqiujFpfz9skrHZpWt3PjpWXhdape42AIhANJ5tiLsZ6sd1TES8xqKpSw7HuPeP0d1d2f6fiBi4myd"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.1.1_1522912139642_0.7326423885381992"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "csstype", "version": "2.2.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chokidar": "^2.0.0", "flow-bin": "^0.69.0", "jest": "^22.2.1", "mdn-browser-compat-data": "0.0.30", "mdn-data": "1.1.1", "prettier": "^1.10.2", "ts-node": "^5.0.1", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase -s recursive -X theirs --exec \"yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "60faa74538df37fbb1ff4c1667812ec18ff7ea1e", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5YHWQgAtzKIA8trr2AVg6Jq5Fs5eAR1UqKbRJjgQQevNx3IAhD3S9wajvqJdmO7bgIxy0MA5lFVPzJYjmMlNeQ==", "shasum": "1656ef97553ac53b77090844a2531c6660ebd902", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.2.0.tgz", "fileCount": 4, "unpackedSize": 469044, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID3dsqud6i0MY1X6Py0F1Izs6jdvEyVybkcylQWB3uvSAiADkJIW4bM4OZMwBHP5mHQ5orgeqCn4kLIbQRPKDq+1BA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.2.0_1523432920923_0.43559655594650004"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "csstype", "version": "2.3.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chalk": "^2.3.2", "chokidar": "^2.0.0", "flow-bin": "^0.69.0", "jest": "^22.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#3ab7b502352239d2131571d7463e098d2317532d", "mdn-data": "git+https://github.com/mdn/data.git#c5fc95e780d14f4f949e9f26675bb6f0ae376ea0", "prettier": "^1.10.2", "ts-node": "^5.0.1", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "07e6101cc54c97d4ce3af19666ae90a027d46458", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+iowf+HbYUKV65+HjAhXkx4KH6IFpIxnBlO0maKsXmBIHJXEndaTRYPVL4pEwtK6+1zRvkXo+WD1tRFKygMHQg==", "shasum": "062e141c78345cf814da0e0b716ad777931b08af", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.3.0.tgz", "fileCount": 4, "unpackedSize": 485413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa18ZpCRA9TVsSAnZWagAArHwP/jUrNo5tEW7gOvOmKwfM\nKtxfd0gW7x9urCs0fgfmMezsadgx/+1fYYQtOLTFTOt3QdMAWDF38Nyzyr7z\ntYhDYnyn1LUP0tsfUmAhsV5LePnhwUK+uD+bB3jcNtyw4SHOWgVm7iWwGcNx\nL7rFhSQEGR2wXP0aCcJXcT8fSrwiPDELFNimX1WFxq38jgCF3jkED3mrEd+a\nzS6ogiwlqCjJxnZ2Z+dIJhy68wRlPd5IkTL/yLiuJnG8ObGw3FOifYtOl5Ez\nsQs8RGU5eYvK6eP4V8d0De6Gz8npIfqTtib6RTps4Rg1S5MSmJD1H0UkHlw1\np1OWIWfOp2OaJDpI8PQnUzi6YRIWAGLZsJ4xOS+d+upkU05j7lUwr3wWckqw\n7RC4hVnAkrhVmI6H2Dgap4LpbrV04b+zBImN+BOowBo9a6HIpwW/uZEoUoJO\ntbY4qHQjle1svoOkCX5UxcDjgTOtRgK7+810tW2RMWMMyr+FffDuEpQ//8t3\nC8KDuzNH851ZC41fx6WuWpI1HAOTM2GxzOUfl7wJ5NIyZ1offHDdcEF+MG2H\nB+Hj4vLUiWBz3vy/LJ+Po8PLIlaJvK9Syez/CIx0e2AWp1dnL1Ofp2K1PSVB\nXCkepfFGJl5JNBZupGteTuFxQWqSwHAvZdHgV6RNYvJ+rRviwMA2Y90OwIJZ\nF3bh\r\n=Ul4l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6lvgHdO2YikeBytPT9K/EL47nUS6hYgLWFK8ks88e/gIgI20uk0iYJ5xCeY4Qme/FgV0wp1npoLU/Gpf+KiPvO68="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.3.0_1524090471874_0.516894233434505"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "csstype", "version": "2.3.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.4", "@types/jest": "^22.1.1", "@types/node": "^9.4.2", "@types/prettier": "^1.10.0", "chalk": "^2.3.2", "chokidar": "^2.0.0", "flow-bin": "^0.69.0", "jest": "^22.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#bdff84a8265089c04a56e702c4a6287cea734b53", "mdn-data": "git+https://github.com/mdn/data.git#aac67bea6ebf68e752a71ef0aca20ff838168101", "prettier": "^1.10.2", "ts-node": "^5.0.1", "tslint": "^5.9.1", "tslint-config-prettier": "^1.7.0", "typescript": "^2.7.1"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "8a84b9e077f77fe76d2c440ef8a28bbbdcade77c", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.3.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-riPNx30TGbUsbEfJ+2awgZwfVDuierkfe2dCVh/cO97d3MOSGrHoyqis3T26xRZWg0S7w0+55RODABT0nsUqLw==", "shasum": "3ee8e0110cb414e636c9c53c5e61d3118ccdec87", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.3.1.tgz", "fileCount": 4, "unpackedSize": 485526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3taXCRA9TVsSAnZWagAAHQkQAIlPKefDp0VehY2gzKXL\nK+mTlnTG1liEGOIZ99ZU4ZBqfVt3zXHtuOEZNWylLT9ARzrsRERM0CczOK2q\nFAXNIbqBXBTZyuELBg896PnBOHxq/RpQfvtUc4K6o1H/xywvT3uO2YvWJFrs\nAgzTIftUkwTl/mo0Cy6CbjIVGNapb+JaZ4qDsJrHrUG0pDqHmfrTjtBAY6zS\n0cMUdQvXkuQV3yAdePWCjqjlUeKm5jaIyC2FNuYa9uIoHXbla1t2dhJyrwBe\nJK/v9nxdCWeXeVJo7iJGpidrWTGz/QJTxFfxTM3lhCGPk5bfQiH6MKazaJPl\nPbf9mQnLwrH7YbcsaNIrM9v9fFMSgs+uoi4D5ShThfKSBAIXnV1EiZv/FN7Q\nK6ki4t5FpK9RByAAvX2rkGbYVESu8BuvILJwCYKLIHFWZn50Li2FNZHd/CEz\nXP776lzLEfAR07UWaQw/AIYFFihRra8mlUC+bD9mBLWviHLbBJbRUilD5COo\nQqI0x/+tnbx/HZtjOuxKPa6yo92bSVmFVIx5OoJUEPRK2HFHOL/cxsEK1SEB\n8zNg1BPGxo1gqgYPgZnKnxdoyQIsY10GOFmmZsHCtEbXPsi6d/PFDhd1gE+J\nkXWa50rOAickfNNQ/UykR54xpzwgy1uq/A+cHb712ndVbhkSP0qlP9WK9HlQ\nuIYa\r\n=rAJj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAnDNCMKNZf09Ag65XCVRdQ/JADV0/brafBiZGU6SqbZAiEAgzCXFccPw5hNj/aqxiU+kL1xvdZE4UJ/1ULl3VkCI6Q="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.3.1_1524553366008_0.570348340563219"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "csstype", "version": "2.4.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#bdff84a8265089c04a56e702c4a6287cea734b53", "mdn-data": "git+https://github.com/mdn/data.git#aac67bea6ebf68e752a71ef0aca20ff838168101", "prettier": "^1.12.1", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "typescript": "^2.8.3"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aYpteaa5Q+HqL0xmwRbqeqXPrwOXGiAMvnkNJi4dHY1c6x4LBltF/chAGYC4eICgH9iqjf7KeLgAPADdc6PNmQ==", "shasum": "6c7d711cc135dcd90c812a80213eab006fc1acff", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.4.0.tgz", "fileCount": 4, "unpackedSize": 893243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa34ErCRA9TVsSAnZWagAAg+YP/RAZH/J9h6u1qTG+/fP5\ndn+giDgJWE5M+FTV24oN6rN71DmFzr8TN88UurOuPBMIS3hv+YkMMLJAt8w3\nzH87Hc2CB1m5MzAm5bbsDqqgkocyvLTcB9WFu7Rx3gshGSJX0rTe97tKEvIT\no1UIkfkqdSbxbDKXUCAL8ViX0LWD8fV38uzCEmkuUUM4Fr4u5rmaBp7mAymt\nmuLQi/UTGoFuKuLhVmHIBV1vk7ytfoIvvfaMqZr1u3COVOgrfgQf5Fiia7iI\nSkNqBqAZcoMJ32hr5JO+CwytSxpnCiNAJuQEEz8vpWXkQDE65UoBfsldWQC7\ncOzyvx4gAs7fc5OrLVV797O71GnzWJ5ImbC7iuQtAnosKjW0F/JUt6iPndnN\nNhhPY2ZOe7TdJWJVuSN6M15ePHkFJ2DVayOtySd/Sap78JBZNh4aZT3gshCw\nRn5K2dWtd0X5df4ohnUH5inkQxNdSAKWhV0D4ipBs/WOZtH+Ixa1wFpJWQ8l\nlyR7bZnOC8PDheELni0Iq4hKb6tHBcafZ27+kDdU8Z3KrPtCzHe5VdpAskOa\nKRDEI3L4bid1gLvFbi9fUYR51Ea6izgCTleNSHWtooCHciq8I1ntkTz1Y8fm\nbq6a1ilOn/1uwo7zqm3Xb2K57S+CiaF3+KwREnyaPZq90ufurRgKFFENxyM/\nKd2C\r\n=nDsL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDLbwidmEjgQ90lzyYlisHwfve0k2PPAZ9Dyxf+WDCJkAiEAlTj6wy3WwSmYIE0d14fvuyWjkoR/MgW3GgcX9KttD+4="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.4.0_1524597035078_0.41999595863468064"}, "_hasShrinkwrap": false}, "2.4.1": {"name": "csstype", "version": "2.4.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#024914f593d04fd16aa39b3101db24e0e5b6b978", "mdn-data": "git+https://github.com/mdn/data.git#1fa9c92c235ed5c890bb31bbf7eb34dcdc45a6d8", "prettier": "^1.12.1", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "typescript": "^2.8.3"}, "scripts": {"build": "ts-node build.ts", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "f9cc3fc6a365ef64519cc60fcdf85f779f3caf8d", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JuXYT9dt8xtpc4mwHSOYnZtQS3TmYVhmZDyXbppTid29krM8Eyn5CmsZjIDTSvzunvutYOBwQmnziR5vgFkJGw==", "shasum": "ba35a94259cffc07ed022954737a1da690dcae2c", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.4.1.tgz", "fileCount": 4, "unpackedSize": 899123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4YncCRA9TVsSAnZWagAAm48P+QAv3sSzgqZWw68AMTR/\nQqei0yOr8Zz0To+b2H51b3XyrRfN9ZOzEgfvUvS8yJLq3T6BTLB+MgFg11ir\n7LkUPn2wVSK8FF9+FQRXLnVRPr3Q+QCWf4uBMb9w0QiaXSEGytm8+zDldyFT\nXLArrsJlVfMf0ST+pzJ7WM7BPDZSUitYZ8PuLBN9PtrS7l58vmOZWn8l9Slm\nstP/x+qK6gtj6azSD9edptgZP+nqTJpmfLQXj+Mc/PQ+9PmKMyfgZBhTJqG3\nenSarpL167rbujaFMmoxVcwm3NcRmfW/OnXj6RujS4eCA05Oj7dWIRZONi4I\nI7Ms6zBxhQOVYZ0KXvvAzRORY55HAlGigzRBp4OnE9E5q+0kZFUSNhvskw3K\nSipTfIMjWHP1BfsUsGf95hoWtB/GSZFF5uTsGVsVkZ4iaAgROQ1jcQcfli9f\n6J83HVs4E2qRtJCL3n+7TQ6YvPMAqrWjqAGHPSEFO0Q2Gl08SIRpf/1rTpjx\nt8lQA/0HAxz1zJ66xnNN9xHBj9OBz8aO8wQ03LZ4R3euPfmTd1UBFC9elxBs\nPLFnarvQvDB5T6HJ8Jvj1oJWUK49BgUbcjri92NDBJq84QdsrtzdGc6J1khB\nruN0/uqurEL7i4y/Wrgrrz627wkpjPGcoCn3kEcSmhHP0ORdwgK1WTCSPcpN\npUkw\r\n=Gr9e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEnVj45+vyb1A4lEQeR9pcLGk+9n+tV7cxCSBhu3o77dAiA7s6LzhONqayzU1L1zsriVg1kAh8GJFUc2W3kd7FYfXQ=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.4.1_1524730331265_0.9408894763788469"}, "_hasShrinkwrap": false}, "2.4.2": {"name": "csstype", "version": "2.4.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#b8c6f75d6e80af946a4416e04cc1decb6eb9a6e9", "mdn-data": "git+https://github.com/mdn/data.git#33482ae3cba9a1a16a1e18f79921b97b4d8ec6e1", "prettier": "^1.12.1", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "typescript": "^2.8.3"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "cac649de59655a88b7155efc53507419b5d65d63", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.4.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1TnkyZwDy0oUl//6685j2bTMNe61SzntWntijNdmmEzvpYbGmVMZkj204gv4glcQp6z/ypg+YRziT91XVFmOyg==", "shasum": "158e36c69566bf968da63d0ba14eda1c20e8643a", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.4.2.tgz", "fileCount": 4, "unpackedSize": 899326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5CV1CRA9TVsSAnZWagAAaKUQAJoEzvagT27ILpbbdVDt\nc6q96w4bsRaUvfqCHmLkFVuoDlOy8Nsf1FQfwn4cEj89Al48zkyeB8U6umzh\nVqtYbxOliKlRBLvc002qwEx2NQ01Mcy9Up/RGSRyhkl/UQueKOCalZomTirW\n88kRTlhY+GbdrXY8NY168g/SqgyvH5E+xymtIoSQ19A8MnJZ1fNgRnNoOIMV\n9u42Cz2nfBY5wfyx3bAh6QHknOghTEgYwyihPfyJix4QZRBSBR0TjvqNYVGh\n6BqEo6XSozJ6umqDOuPU6R+zv4Y//bM8QGjZo8uSE07fLw6J7bvvQ9/90mYr\nQmPy65BQiqNaANHyBjl4aeYjBo4l7G7x7lPzf2ogE05MBvwOGCY9qdJ9ri+O\n2s1xSTIMcG7We+9//VZc2dA0VuhxJngMNNOVONqlahQCzCof5KgjI9WFIhNa\n5D1JCxj1ZuCsQNHU0gxtKN/cGaLycrS7v+l/2GqiFi/i2i9nkHp1qKNcjets\n+WGbCfrn2I6EHRevxJqviBKrO9krC46THxabvJog4WhmfkH4vRNda8/OH3IB\nDHiWSfV0fwp8M6l2ABogRjY5LuZlDS3smvjqffryrSGim62PymBe5cQHYXU5\nN+xmn+y6ZBL7l1MvqCjJi+9/4G+uAgUCTqOC5JQtz7ZycD1/RnyuPAd5mZwI\nTBiq\r\n=2pFE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAC8BV7fBudfsxLVQPmfZITxsvRVJHO5CRinXQJwUsxeAiAQTOXeiJcmAi/N7odtRlgRQyvuKe45CsBXi4M4gl/X6w=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.4.2_1524901236715_0.3162052634104653"}, "_hasShrinkwrap": false}, "2.5.0": {"name": "csstype", "version": "2.5.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/jsdom": "^11.0.4", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "jsdom": "^11.10.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#b8c6f75d6e80af946a4416e04cc1decb6eb9a6e9", "mdn-data": "git+https://github.com/mdn/data.git#33482ae3cba9a1a16a1e18f79921b97b4d8ec6e1", "prettier": "^1.12.1", "sync-request": "^6.0.0", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "turndown": "^4.0.2", "typescript": "^2.8.3"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "399c3eae98ad44aab8e61408a7c06cef25ad43ce", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KyGTo7/Y1lIK+JIfZ4LNEFdjN5lYwmJPpiP2fJbZF9LL95sY4CvUibobzzyXVsiuiD/oKcCzGZfT7WWZxHvkCw==", "shasum": "53e1c242f422d5d976b81f42707bd9b8934492ee", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.0.tgz", "fileCount": 5, "unpackedSize": 1152896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa822JCRA9TVsSAnZWagAAMVoP/jtCTRF5PMvhtVIn6lgX\nt1SBUaqaYenv+LDoK+Y8srwY5WaJq0HZOg/Mm6dNTkbQGYBwE2rHjaFESmrS\nvu941If8uegGihltiwFQ0u3SoSF5IH4OyQP4poTi7R264g4UJV7VyQd6bG9S\nJTtvxn+qLJ4A4og7G5/meGpFOf1pn7++gLVbhf5Oosgnfk9fwK85/TLlm/KI\nf91xVf8ghtcuxaQCGMPJGjkUXPTDO4SmXHEnYU7y8fwoVRHnZXUL4HSvBscq\nIfaki7a+AbRGnqajAhfmheJvGf2UCfMvJM8uYaN6T5nah/eFf3X5Sac7Vepi\nqLb4w0rJT4Sp6J0d4+6NLcorAidlOM5g8BR1EEe9HXst9h/KEHU47jJAv7GD\n66JXhPWo1tRq89K6o/TdZssfWyDkDQZPVk3AUMsmFsD5FQwLxHfvfdp7iQN1\n6bvpu/2JgCocDrwu6MDtvyPKk5SXge+W8wvAAeWt8eJgsirb/kW1Qk8u4O2q\nphNXIvKqE5/QF8O7twsNlrlKYNCW6POJOZ7Eg1J4fre2KHFiAH0ahHsbUBhL\nJwR26mDx5kEFunYLw5/u/hoH81+7otzh+3G+NTdWKR2cSSGYYK09My7S/EU1\ni/t32ZG5O+7mPF8B0hw0c+R3GAXqzHg6f3Te7xImMGX8+w917trT+ZuTHJIM\n2z+V\r\n=jJec\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS83T9O0n0bfLTlRQyUi7nYKOnlCSSL8KePIKyywLFAwIhAL5bD7kY6K/V6xokS9nqg0bkwvJUcSwV5wDWOFH16Gdn"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.0_1525902728320_0.2070916812571575"}, "_hasShrinkwrap": false}, "2.5.1": {"name": "csstype", "version": "2.5.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/jsdom": "^11.0.4", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "jsdom": "^11.10.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#785bcec91fda2fe1a794d01142cbb45601da8e23", "mdn-data": "git+https://github.com/mdn/data.git#8f876a5637d488668555a366b277f8f09ba762f4", "prettier": "^1.12.1", "sync-request": "^6.0.0", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "turndown": "^4.0.2", "typescript": "^2.8.3"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "c3fd5dc48316ced76fa7ce57f190270887176515", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qfG5lXkiUKz3kAuABSlpRxL9QL/U8ViJiXC6hvk/7tEJaCj7a2ZOW2kVtSFGpETOfQR7MicXjf/q1bmO1iShiA==", "shasum": "654231d1ddddfc3eb93da281a1144e7c14fc0bdc", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.1.tgz", "fileCount": 5, "unpackedSize": 1157186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9iJ+CRA9TVsSAnZWagAAQ1AP/AlPAU8L/5ii94F3QeKu\nC5NjdDHkcwkfXCobC/56hiXEhrw3o1Y54EaexYkAnjxcPm8KNJs6frRBBiEA\nEqDfYgZkgOiUv9/7rQtIbqpgoVY/VJQnc/ydhyuYK06lOB2/0f/qIiUoqagQ\nDKgpdRVpMh2uD1aRulu76wBhBosFF0dhXdWSMqkdeWgmPydlLlG+nhxtY0Zu\nhAIRbAXk8/izarJYMz4OEjyYN2+7Uo3JEmhI+LE4Uen6nXib6ggJnsGiH0Dt\nMj3ORysxlcQHWUHk2t73Vo9IlhU92Az6CxrMlAcqFjzBWLN1sgBDGrPgyEH9\n1Afplmw4J2KKnfsYyhaMxR4hFEAFkRxuuXzEAT5FobAOr9/Q19CD52a5P8t4\n1HuYn0Gx+KSdSoJxtFsdvwkSql393LHiqILTPF/vq7Y3eWSyrLHo4wwQcCCI\nqMeuTQfCFvdm4kI/8G/DVijUPvdA4z3DhIWXgd1vGfUj7KbwcmMETUwKBiZY\nUt+srIWyAbkyOUV7TVpgg6LzoIEPtSgtSpV7/A3jrqo0p4FS3ZHdJo+Dmi5L\nEy0ARS5M4GUkoBbt+42/MYbhAuZWSyZCRt41Ak5F6vzq7hdMiknunIdjREqg\nMJ0dEBn0W03qeTlFX9VW8SKjYdejuhaFr15unCyPeMBethTtivqWRi4K6Kwj\nkTkW\r\n=EjXx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCw//i9oD8Fonosic1e/m1HdKsufDtwmhGHcgTWq/oz/wIgRE8PMfNwbUuRvpvTv6+8czMmbuLB2nWTNQrRBxbg8vA="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.1_1526080125995_0.5940595832158813"}, "_hasShrinkwrap": false}, "2.5.2": {"name": "csstype", "version": "2.5.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/jsdom": "^11.0.4", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "jsdom": "^11.10.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#98ac7ae6806cf83e040eeb22a8355919b8be87ad", "mdn-data": "git+https://github.com/mdn/data.git#e19d1665200eaf7fb173d5e5f3dcb3890402eb3c", "prettier": "^1.12.1", "sync-request": "^6.0.0", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "turndown": "^4.0.2", "typescript": "^2.8.3"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "0fc0232af94a5b7dcc32cea713d0b8f89c0e5720", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-epoHrKPywwUhFTXadQNA5PPx4GChtkM03SkBeFZTaFtJcn6QfXpBkX2IAc4J9Oe18nha5NrTOo2OMOge+qH1mw==", "shasum": "4534308476ceede8fbe148b9b99f9baf1c80fa06", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.2.tgz", "fileCount": 5, "unpackedSize": 1165424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/K2XCRA9TVsSAnZWagAAz80P/R/naWKZ6WKnM2wLRFWS\nP8qqQYLh6jw8xRddOgvVchsL7zLP2KF043vlwdXKhISBPHgjP6N7U2bM7kWa\nx+2B/p0RuVw/FecqyoUkRzO35q8hLuJMZvIWm7fs3EPw4a2WJz0YL0M0cyQU\n+n8Pa26aoICCTue19tBKE0Iawp9VgVBsaCeMMj48B1DgWW6KjSnPSLTVV1rM\n0c6nMq2X36hPkFrhC19RK/8yeKu7YbIMRxIup8XiT4B0dHUSchiNudlETW+u\nJ2cOB4TuCFGltCmf5TmdvJr2xA9OXMVrgPhH77xpj8kkAJCZHa0xU62hnPlx\n2kMQ2eKgWmgIwgyy3uQ4v/pGkQT8L6Kn8dqJnVTzbXi2NxeSJY4VNIY9AXIZ\nu5NCe5fVcGWmIC+vsMYapl8oXQURMC1ihV2Opa8m1nqrtVx+5zPSsluMISn1\nKe/nhK7XPXknMH+bQB306eTvwFnNIzLEeNSOeoiwk1GBVZhfpKOiOfWLTTnD\nKtzYFohKpjNtyu+A9XkfbOjtdZ3AMYI9q2xsGzyabY4V+TnOSbcmTrhimNFK\n63v/ISnIGUbi7tH7MVVdVpSjkCCk0jxUdkWZLPrMedOi/GanB+BZ8QEAjBwp\nhhJqImjI0M5Kz2qbPTaaLkLRWa++KdT/89x2UdLlfmp+TpBuycd9YIM0VNNv\npU9G\r\n=Lm46\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFg3LJd9dQ/iKezuNzZqkssEA8CuAFwwhDKvc6IRDllAiBcWCDIV2/DxQQBOAwnFAI6DRYdAkxQDg+95d5NFc9MhA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.2_1526508949447_0.9878488712911087"}, "_hasShrinkwrap": false}, "2.5.3": {"name": "csstype", "version": "2.5.3", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^22.2.3", "@types/jsdom": "^11.0.4", "@types/node": "^9.6.6", "@types/prettier": "^1.12.0", "chalk": "^2.4.0", "chokidar": "^2.0.3", "flow-bin": "^0.70.0", "jest": "^22.4.3", "jsdom": "^11.10.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#31277939241546b8963a78db4fd558efecc16feb", "mdn-data": "git+https://github.com/mdn/data.git#cc2b997ebcfc4e05c8c4f0917379da19aa27186e", "prettier": "^1.12.1", "sync-request": "^6.0.0", "ts-node": "^6.0.0", "tslint": "^5.9.1", "tslint-config-prettier": "^1.12.0", "turndown": "^4.0.2", "typescript": "^2.8.3"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "57bad5f268372c9c0609fd6778d653094d421e80", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-G5<PERSON>noK8nOiAq3DXIEoY2n/8Vb7Lgrms+jGJl8E4EJpQEeVONEnPFJSl8IK505wPBoxxtrtHhrRm4WX2GgdqarA==", "shasum": "2504152e6e1cc59b32098b7f5d6a63f16294c1f7", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.3.tgz", "fileCount": 5, "unpackedSize": 1169800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDUdnCRA9TVsSAnZWagAAN0UP/jxfiFk/T1H0fGMCJHoI\nC/VmxLoIoOL+XaoR2/woyVCgEbLacVqzdWTt4uY3y05mcuPNzNHo7TKpEd51\nW2LnQlrOdUBxJ/4gwV9bgSxBsmYmIAl1Z+A6NhNvCQ4C4rL0CoIxUruBlpDb\nTfD3HFwKXf3+4/Dt1IQnos6zc4s/l3avGsxPtf6mi8wt+n8y0aZQJSeSeltz\n3q+/giHaPukHiwAIW4yQW23qyjwm0MEIrOHk8R0G1Tsw49VyGdzLdaIIL79g\nc3UdHxtOCx+J6v/UdCjfkxL87LI+e/FpDc9q5uW3mhX+IjvLEFKxf+Zxch5B\nS3U2jmWzQ8NmGxIWdmVGbnZENKtIjoRdJ8eec3coTe1srP3w2BZvWkarNYH/\nLX2kGgEXwSroCoCP8J3ZdmPJDtKU4M+QrCTwqYLhNksGt8mdiJNk9iU20HIe\nYiK8bcBArdMElF1Rt1tBWl83Bl3/WCmmKYRCxVirky8Xy1K1FZ92rpU0epbH\nmN/eAm1CUvmavFn0FLPKqfbLMLPrpdV0buCDHMF4QIGagCjRQA68jV8MIXuD\nI4sLfpeEaCrKO5mpsWGi6Uv3nVhiRkXvzfoe4S0kNMQyNKb7aPe+/m0TEaSL\n3nQ6dhlaxnfoAsA9Ex74jeZKWOAAqaVBrc0qiSglZv92bJgnnwVT3CaLb2ch\nVgVB\r\n=52pg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClRat4roBbwGrZTHSLxK2rpIzqsmz45OW3QJML2LrVEgIgGMw/P5gnPA8YVP9jr6LhlpPcCvAMkRHO1QIaJgjSq48="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.3_1527596902577_0.9898365887141232"}, "_hasShrinkwrap": false}, "2.5.4": {"name": "csstype", "version": "2.5.4", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.1.0", "@types/jsdom": "^11.0.4", "@types/node": "^10.3.3", "@types/prettier": "^1.13.1", "chalk": "^2.4.1", "chokidar": "^2.0.4", "flow-bin": "^0.74.0", "jest": "^23.1.0", "jsdom": "^11.11.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#0a0276c9648467dee50981caceba80859ef0e0cd", "mdn-data": "git+https://github.com/mdn/data.git#64fdd9b418b6beb5a424b43106a3fdb7b02a57be", "prettier": "^1.13.5", "sync-request": "^6.0.0", "ts-node": "^6.1.1", "tslint": "^5.10.0", "tslint-config-prettier": "^1.13.0", "turndown": "^4.0.2", "typescript": "^2.9.2"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "f0ef1735c00833526cb007542a66e709eee2bac7", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-08qJTcCThE2OqIUMK2DIzimkKoGRxS8zlpvuRwPlUagljXPCua0/D6Wf1IIatqDiCvFXatgKy5emSbfPt1XDgw==", "shasum": "93850187209b6d0a71d30a85abc95a6ae3f3e8cd", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.4.tgz", "fileCount": 5, "unpackedSize": 1175996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKCuvCRA9TVsSAnZWagAAz/oP/1Wz2t247lagOQqJv0Mc\nV31Xnz2PiIAgB3wY3IbnXzY+X+LIc0gQiMe1koxVI+Om/APc1WBQUMHCSiNA\ni6+G3Nv/pSfrs42gGRA7hGAuHvcPai4vfxUvXIgRnlmAmPJPJ8Ey/yXHQHEX\nh97YbonkKnVausFBAkXdqxaESagmtp+HFHkK3JLJ6OsbUSTaCy2NdacuI5Uw\n/YJNHB0k1SeEMFF2kPmb5Pt+C08Q0EybsL4iNJ0Htlgr3nOoX0awAjxSRG1N\nkaeVm+6+kicBl8cOxgp13KCWHXqjGU6nr0cD5sjlq9kIDGE6lE/xnwd77hyb\nsZ1Kcdt94/kWKbKVkCApHZ8bGCGnMAdUgGl72M9gw28EnZonE4atGgtuhvyJ\n0GkceqNVoW2EhkES5Q0PRBYvxmFknRRsCJN3k9SynThb/TJXfvw/s4DPV+0L\nhaL4LJbKOOzaMIt2E9+Z2zOTjhL4Eemlgf+LnBrRc4I/PNOBiPibopdH7PnC\nV4eHyA3CdWAPdQrdFRWNZ5pQ80zlJ34HvFfuOPUO5TX+ecdCU4qrT13upwmY\nY4lXFQQsNg7fjIW9r3lsInjuOCy7fILaU8GxJVWpBqB6e5h5oprJgGWZrFRk\n41xBQO93h25TOKYpvz7plTlkpNghUb0qWwm7eBSn+fVFqoqgraa2kC/kVHIS\n7KB7\r\n=dgNk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBchyvG3rxC9iljkDdAhM36FHpuGQ8jhhRTbxwqKsBlxAiAdueIxBOmITnGX849fs0X4VVkMGacpIFLoGhHHZJgCFA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.4_1529359278200_0.31258767674464116"}, "_hasShrinkwrap": false}, "2.5.5": {"name": "csstype", "version": "2.5.5", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.1.0", "@types/jsdom": "^11.0.4", "@types/node": "^10.3.3", "@types/prettier": "^1.13.1", "chalk": "^2.4.1", "chokidar": "^2.0.4", "flow-bin": "^0.74.0", "jest": "^23.1.0", "jsdom": "^11.11.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#db830230ee3c7cac7d590695a74cead460850449", "mdn-data": "git+https://github.com/mdn/data.git#f9b6921eab90a071fb592221495af36ed0c761ce", "prettier": "^1.13.5", "sync-request": "^6.0.0", "ts-node": "^6.1.1", "tslint": "^5.10.0", "tslint-config-prettier": "^1.13.0", "turndown": "^4.0.2", "typescript": "^2.9.2"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "263a495b04978c5cee1ed8db3665f53d80384f12", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.5", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EGMjeoiN3aqEX5u/cyH5mSdGBDGdLcCQvcEcBWNGFSPXKd9uOTIeVG91YQ22OxI44DKpvI+4C7VUSmEpsHWJaA==", "shasum": "4125484a3d42189a863943f23b9e4b80fedfa106", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.5.tgz", "fileCount": 5, "unpackedSize": 1175642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKVHOCRA9TVsSAnZWagAAbZ8P/iYwZyn4Nn7iZb3YJXrj\n53wV24mJ2Wbyj3uWDQxJktg0Y3qekkkAtYrBvddJcs2Xa7YDn9PrfffqF+c9\nNDKXg2nuQW8j3FmqAwcEz05sz1/GT4bzQjeKKqDEn4z/gUTBskP6v0efKKvi\nwOgpm1WbJwYvhSBmk6+hYH4l6k3NIzw4ENHUysViOuIEerLzbwrln8t9enu7\n8P1jG/0ZRvn4aRZ+0E1imN4l/nvPBV8f7NYGN86mclaSxhdH541zfVWQCsLO\nn46AGLnslykCXxPH65LbVk5JSjDADgxUdNL1oe8iDhAHRbD3DMjufVc5vHFq\n9bLY4jAFPpPFiULxbkuuaqTwu/tInE8wAEoet6XB+rW0YFYgUdgiH3bTSqrx\n68TnNnniw58N+i98G/xuwwfej0L89IAdBVVQi2xuCSWuEu4Ipx9VO+Q2p2x7\nJvJtqPhrd7Ikd286D8qfRb/0yRfXD36TM/kayRZWhd1NqdZK2y/XLAl80+31\ncWejoEFllqeGSFCk5baErYAMJMkclobxrPy2+II0MXqzt79J2yZtshzRbWoK\nQYllZ3KJQ5dq2/bab4au2EKE+77sJK+G4J57uYR3cU6X9FMVygRopq6CZUW6\nbMNyneUHxLhofLMhilsHzlQg8iNwDgnk8+83Fyd407DpjE8DfTABjAOy3Ord\nDSJu\r\n=0puR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNnMTTvnBZbivrebUqazE34FvQb54jZamJn28W/nEinAiEAnqVo/3dYuCAhggmcdIq0UpriiV1aUJ1xPMFSukJ2tNo="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.5_1529434573004_0.6159816145804999"}, "_hasShrinkwrap": false}, "2.5.6": {"name": "csstype", "version": "2.5.6", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.1.0", "@types/jsdom": "^11.0.4", "@types/node": "^10.3.3", "@types/prettier": "^1.13.1", "chalk": "^2.4.1", "chokidar": "^2.0.4", "flow-bin": "^0.74.0", "jest": "^23.1.0", "jsdom": "^11.11.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#d678f5bb0ac426c833ccdaf55daabf472e6c0ca2", "mdn-data": "git+https://github.com/mdn/data.git#893a6049355daf5e65361d2858ed08816d58948c", "prettier": "^1.13.5", "sync-request": "^6.0.0", "ts-node": "^6.1.1", "tslint": "^5.10.0", "tslint-config-prettier": "^1.13.0", "turndown": "^4.0.2", "typescript": "^2.9.2"}, "scripts": {"update": "ts-node update.ts", "build": "ts-node build.ts --start", "watch": "ts-node build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "71c01c6a7fd1488ccb69769440f7d9418237e21c", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.6", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tKPyhy0FmfYD2KQYXD5GzkvAYLYj96cMLXr648CKGd3wBe0QqoPipImjGiLze9c8leJK8J3n7ap90tpk3E6HGQ==", "shasum": "2ae1db2319642d8b80a668d2d025c6196071e788", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.6.tgz", "fileCount": 5, "unpackedSize": 1185787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbU7h1CRA9TVsSAnZWagAArEAQAIjkUV81h6c2+dssmQMW\nv+XVjV2Pc47yZpgRj/dG6OCZ/i1td7LHOHObeVfU0sk4UXBbz0MwmXfuPWCV\nXRLNZeqZKl9Zb415l8tzij7NI8DfNm4sWirLc1aJYLEfU5bo5NTvVltU15dL\nAiij13q6gflq8RJLf5L9qGrSYNGUjuC0x+L4dpQ+m8iujJju/sj8TgxYwjjx\n+n9PZb1TAbBZ37MTyXF/r2UyiSsJaX4GwI+sran+iFqecDlPQ1zITiiZsG5V\n5QivXQEb++l8/NNlB6xcngYQA3V6rOp9axWMloGWhjecHCkQPP64M1BD2Vh4\nU8WjOrmyOHJVyQr0DVCcnhgfHMPQdGZAAKR5zF5yeygae38vFQwBGZM3y/gp\n4ar23zjJ30Vh5VkcyAVQUnFsgisUc7bxeDnMrKoqpDRHNc/LnBQvE002sxUr\nDPboUiXbtoZU9CsHiw5Di7K9g+hw+s79u5ncqR5vhZn+q6sbjc98A5C4qSvM\nZmnYbgMg+cdXtaeHDR+K4X4TjqnAomZczr1+ckRj8Y5gYuHzMgdQ3YFKhwBH\nzBq23PogJMgFZE5f2yo7ugCP3VNePCvJLptOiZ1Fhbv6h2vHD4hwvtRg8bZG\n8h5RXl27khhC8ltAnLvQAPBOhFPy5MdY+M6QDRp6Sda+7WfKrgk3xD9Goc8v\nlKp9\r\n=wR0v\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF+bKyEEOV2Ox2rESfpQTWV1rSvheoJMZLCiTSoafPSqAiBoczOdNgb01Whhv6Xl+Yy7yeqkdpuWZU6jpSE/+b2mYA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.6_1532213365314_0.3155536494837219"}, "_hasShrinkwrap": false}, "2.5.7": {"name": "csstype", "version": "2.5.7", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.1.0", "@types/jsdom": "^11.0.4", "@types/node": "^10.3.3", "@types/prettier": "^1.13.1", "chalk": "^2.4.1", "chokidar": "^2.0.4", "flow-bin": "^0.79.1", "jest": "^23.1.0", "jsdom": "^12.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#370be97bf066b9cd8a9977967deb77b6b1ef9eae", "mdn-data": "git+https://github.com/mdn/data.git#70ef51ce10f76583b3a52918bf0e9ee2688bfb28", "prettier": "^1.13.5", "sync-request": "^6.0.0", "ts-node": "^7.0.1", "tslint": "^5.10.0", "tslint-config-prettier": "^1.13.0", "turndown": "^5.0.0", "typescript": "^3.0.1"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "3f3298afb9540f219d6731b582b395f756fedfd2", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.7", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Nt5VDyOTIIV4/nRFswoCKps1R5CD1hkiyjBE9/thNaNZILLEviVw9yWQw15+O+CpNjQKB/uvdcxFFOrSflY3Yw==", "shasum": "bf9235d5872141eccfb2d16d82993c6b149179ff", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.7.tgz", "fileCount": 5, "unpackedSize": 1485318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmguzCRA9TVsSAnZWagAAsgQP/igpE5FIQC4YEAvSAlf2\nOZ0FF67E28zXUKDK0c13U6KwD275cKK0u7RmYPdeEfB9SKe69RN67I1Ro64I\neXIjG8UjdmfXK0llj3H48m7h4rqxWl/frwQvHrVkcjUxYai0Vq57h0CcqFQn\n48R4LM2sev0XWuxbTFPNTeUOqk2+IiDmhSxnJa+7rJi4/4ucXVQTpdIdtNJU\nKRZWTxDsA83fTQ5yo4za7RjroCElz1oXe90srzE3hXV/cEo6Nec6ELeBX9Jl\n27LQAWGRUFx+e4znFQzdTbtXiQgbRyr9SxOunwJX23YRhl/8yNFtZbj39Oa4\nz04k6IGXbyF5KVanQGpkFD23FXsQqluqhzPsHlwet2bfXcVIKV9kFi7Insej\nzlhSh4QpgMbtydSfQb+G7RMKsczDFwDRNLTsariNGJ01njcQvSwjBStSqpx7\nKWD+uSDLJnBloJUT66r7WxxZUT56MImBvMcVpZoEQyGGZ3HswsF3pqs3AfoD\nFDMXOQT5Kg4u65ow/vPFAp/cYuwb5qh+Qfkp1ROtAluWRNPbet13uGHC95yp\nkZLJHkxiDqsoEO9TL2uq8cuvSA24OiplS6MUgayVMY4bPJ6E+9g6g3VXu4++\nOvodzScDv53ZhTImuF3RpoliX7qZYDDJGH5SGRzBnYM6J/IDW3SCKJpNQQrr\nM1B5\r\n=Ur2x\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJeHEtwMN34bAiikj8ZpV7CBvee//6N1Cpr/NbRsuOgwIhALWwD+RmVRU5yfdljA7nIpUG9wDtURBb7DC6fUrSP8y9"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.7_1536822194030_0.9022711824930238"}, "_hasShrinkwrap": false}, "2.5.8": {"name": "csstype", "version": "2.5.8", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.3.5", "@types/jsdom": "^12.2.0", "@types/node": "^10.11.7", "@types/prettier": "^1.13.2", "chalk": "^2.4.1", "chokidar": "^2.0.4", "flow-bin": "^0.83.0", "jest": "^23.6.0", "jsdom": "^12.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e4ee665dd1c068a6bf74761ad4a8ece85c7a97d4", "mdn-data": "git+https://github.com/mdn/data.git#a21219fa8363da2b3813c8a480616ccced374f6e", "prettier": "^1.14.3", "sync-request": "^6.0.0", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "turndown": "^5.0.1", "typescript": "^3.1.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "d4e511c781d9ce3fca015145fc27ad30b4ef3f90", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.5.8", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-r4DbsyNJ7slwBSKoGesxDubRWJ71ghG8W2+1HcsDlAo12KGca9dDLv0u98tfdFw7ldBdoA7XmCnI6Q8LpAJXaQ==", "shasum": "4ce5aa16ea0d562ef9105fa3ae2676f199586a35", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.5.8.tgz", "fileCount": 5, "unpackedSize": 1554302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBOJVCRA9TVsSAnZWagAAKO4P/11FLKMzgdKdAlhXTAjw\n3j7kcLselmwWtxTD4Gp+zTbSf4dzO1BuN355F3WthUeSh0YSnWLKqZFwbg+s\nZ97Dx91KcuPZS5wTIVlbkQajUXrVQsT4cyuWPiT9t6WZSSIJ/WXdQxiDOVyn\nDNw169qjvYWBPu7L+Y86pX3DxSqBuHXRrdssZsYuTsDFZPUjnlKZN08nfaF5\nG/3/Tmbj0g5B8Ywqrr/v30S9CxLdoJpABH+E48p2A5Q1BvrVlNkLosAVY/35\nS74YTuLSdtFbs8Ho0v4zup5k3tyjUFi0P8DqTLX+NE3FXJf4mNbM8VAlF4OD\n8O3dEppEig6qh0n0hlMulekTZpHAJEW9Ft160jQPvi8vJ16/BfLp0Pt3XJh1\nH4DvjBLNvc1hmRdk+PZXg7Qvz47ksSa/8FQ3SNXnWC+qewhV7wY5BUdd9zc2\nWdmC2R/mlT4FYUrS3HQgXuOYghMEDvQnS1SxMQAkFXToyYPEvfLhTNLwap4R\nk5GxKZVRbeEFXR8i9+Lrer5NJ3/d+uK7asa/HVRKrmXPqrgQSKC3JQlHk9Ct\ndD6ZS+AGlwpMSuYcLTDZXjEQX0X59IldBhb8LPptqiMuUsrwnV2wSVaE3Dwz\noZYlu9I3SXBhb0bHfnAsBCgDYVYgHrX/SFRHCZCZU85K5Pwnund+JHMamlSf\nJU1s\r\n=jfpj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTFN6BTWKyNVoZ+UgoBnrAAFnvb3gyr6YkMrh2T7y6aAiBcRdwWtdvzsXPPjWPKfDjrPRmj49yGo0gQRyzU0Hn5eA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.5.8_1543823956075_0.02251524475013067"}, "_hasShrinkwrap": false}, "2.6.0": {"name": "csstype", "version": "2.6.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.3.5", "@types/jsdom": "^12.2.0", "@types/node": "^10.11.7", "@types/prettier": "^1.13.2", "chalk": "^2.4.1", "chokidar": "^2.0.4", "fast-glob": "^2.2.4", "flow-bin": "^0.83.0", "jest": "^23.6.0", "jsdom": "^12.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e4ee665dd1c068a6bf74761ad4a8ece85c7a97d4", "mdn-data": "git+https://github.com/mdn/data.git#a21219fa8363da2b3813c8a480616ccced374f6e", "prettier": "^1.14.3", "sync-request": "^6.0.0", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "turndown": "^5.0.1", "typescript": "^3.1.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "c4b331934bfa3d1903bec1cba06b9f293d2e3b8c", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-by8hi8BlLbowQq0qtkx54d9aN73R9oUW20HISpka5kmgsR9F7nnxgfsemuR2sdCKZh+CDNf5egW9UZMm4mgJRg==", "shasum": "6cf7b2fa7fc32aab3d746802c244d4eda71371a2", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.0.tgz", "fileCount": 5, "unpackedSize": 1571127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJKefCRA9TVsSAnZWagAAKLkQAJMX+HkSvkMMFJ0MrmT6\nXw72nbsosksaa72TgkghPcT4sE/XsuvGPBKRKeIlZX9/Ux+6mpsVGMCiswwY\nvKneFyRmGW440TXGGjOhNEheAkO3lhXqg16qgEsJZBDuoTL+41RRK486qp8f\nVsnjjpjlHOtcNKPF+82Amxlt3pObWDojJKhiYBF7X1m8lRoXz26JUA/1ARMw\nB0EvMDpg0XBHcXQ0w2Pq7YRpqfvgIvH9BWa4sH07IW/Oh5MQVmcVj0N28hgF\nuKseMmEoyLeStzRXLWYvCVddNH1sBbnfr4OI+PWapI7+UDS4vtQihUkLHd7O\nTGfp9JKRBNzN+GeO3v3agx9CjSkdl3yg0u3B+O5CHugr8VaC4uMXVrKKptb1\nsY0d3Xp4rRQ9aSCOYy20RK3mU4RZZTIIa3/fIkb4v6XxudDumm0XmLmLtcrs\ncrmXp+g306n1zFarrloopJaRzbq4G7W0pfoL8NsMhrtIMofOtouuF7E++EC6\nCiHCvT680hxwEAlgLeM5XbDhZyLkhabYsOAHkvm1+Fome37au9lYNcYunmK6\nBxhmZz/z0N4fof1bB/agc32b2siLJgMnWnWXBLmO9YYuQFbRwMJOCQBrlLjs\ncY0JdDGoA6iSpbdSI8IguLhNsjlV4zQ36efIEbEzk1uub7u9glnjmq/eY7Ct\np3P6\r\n=cQNi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICRuC/M/pFw1eRnxo7Yf8Q+rfsxBaCOnLzUkEsc/TS3vAiEAutR+zDI4AHdSQDWq43S8wsm58zWsEl9jY/CCch25BHo="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.0_1545906078617_0.5339697899760636"}, "_hasShrinkwrap": false}, "2.6.1": {"name": "csstype", "version": "2.6.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.3.5", "@types/jsdom": "^12.2.0", "@types/node": "^10.11.7", "@types/prettier": "^1.13.2", "chalk": "^2.4.1", "chokidar": "^2.0.4", "fast-glob": "^2.2.4", "flow-bin": "^0.83.0", "jest": "^23.6.0", "jsdom": "^12.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#bb77b48b4458595c3885fc099379bc03d231b00a", "mdn-data": "git+https://github.com/mdn/data.git#46cf898f4258f19e1116bb92b598225171423d1b", "prettier": "^1.14.3", "sync-request": "^6.0.0", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "turndown": "^5.0.1", "typescript": "^3.1.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "82dd97ce438bf3e4684d7b9b42c80ce635c6836f", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wv7IRqCGsL7WGKB8gPvrl+++HlFM9kxAM6jL1EXNPNTshEJYilMkbfS2SnuHha77uosp/YVK0wAp2jmlBzn1tg==", "shasum": "4cfbf637a577497036ebcd7e32647ef19a0b8076", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.1.tgz", "fileCount": 5, "unpackedSize": 1549053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQY0oCRA9TVsSAnZWagAAjv0P/RSfp9CvB26B0FQiTUEo\nghsdrjz5n/QeORVL61+nK1J0r/C3WKwGVbg2BnwpSlRAnRuC25/dFXCjdmcJ\nbm3g+88f3id98ZILFHXJn9VSXzZKYkIRYfD8hbmzUQWhHGe05l6Bie5+HlWx\n7nu7S4Feu5xrHpJ9TBY3n1yrWX1wvniXbTFzMIcOc2F5z7lZB0vXpPBVNfgx\nEgGYKhTFGf9bAOg6HnicZQB/i8SUqW6EpIDDJovxkxKSUHuqssc65Dqljybm\nuu+mbqgtYxtmqnXuCit1kMZ8NiHd5LgcmbNSptIBaCWirwyAQSJ2XBBFm5Hp\n5EJJuqiwRIXn3gHhsvenLlVk27Q2SRHkgM8zCr/TUC13TnvbHoYZoTnQTWvl\nXj3smuV20bmKcUnWUhKBATjivqjF6TtxUS9ftjerja7E3YJ0fJQpr6+bjE85\np8+WEsijdL4PBf9UdgPAqSJ207mSzqfD5sR0O4ojjp4vEBrbkdVJLrv5U3Hc\noE/NzRKqJc+xPblHWvLZ2MIdyFTH5X3KpOgqN2o6eaOYYqqOn1WgSMQK9Vxo\nKDioIKXy9hf01/WPcSmNDBjG/MWK7J6JSEvtA8WRdKOdlxw+0GyiOGUEtgzw\nohXkHysxOuJfMRt6oLxwEQgBKr/4S1sqIXdeEsepxD/4RlELC3Jj2mi4FSR9\nyd+m\r\n=tYvL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICgEoRmP1T6viFYScy0oMisPDBoJdISDRyngDJwW4MGHAiAb1w4QbasKI6pCqiHjNoJKFF5bQ6j2mvogWDv48g7/hg=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.1_1547799847979_0.08038852375448013"}, "_hasShrinkwrap": false}, "2.6.2": {"name": "csstype", "version": "2.6.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.3.5", "@types/jsdom": "^12.2.0", "@types/node": "^10.11.7", "@types/prettier": "^1.13.2", "chalk": "^2.4.1", "chokidar": "^2.0.4", "fast-glob": "^2.2.4", "flow-bin": "^0.83.0", "jest": "^23.6.0", "jsdom": "^12.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#5cca5f8c00ef2f04bb2e546100976e381ccd72b6", "mdn-data": "git+https://github.com/mdn/data.git#6144c1e5a2aa1aa2730bf0b1b439955f1ec4092a", "prettier": "^1.14.3", "sync-request": "^6.0.0", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "turndown": "^5.0.1", "typescript": "^3.1.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "773e046af40534f1b9525287e94572448d3cef87", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.0", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Rl7PvTae0pflc1YtxtKbiSqq20Ts6vpIYOD5WBafl4y123DyHUeLrRdQP66sQW8/6gmX8jrYJLXwNeMqYVJcow==", "shasum": "3043d5e065454579afc7478a18de41909c8a2f01", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.2.tgz", "fileCount": 5, "unpackedSize": 1560852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcULuQCRA9TVsSAnZWagAAjbQP/RX2EiFQAKh4X0TqyjnU\ngO1IMStFvM1O4PjaJRGaJZEToH3CjLn5OKWmL+iatBedr4zZ/AaE+VCLOTpk\nvJk5aMwQkqGja88DtdFO0knHvNkILA9K9UB2QHgjuYWgPtCH+D5pWFzz7SOR\n5KjMDgHqVnQTHoPG83l0/SpjtUiK9lkXPs4X+guG9CfAD/zXHeppNbyiWRJ9\nILqCI7KllV8/XrwYUNS53Mg0IZkNpQpaR/4fnujwHWCU0DQuCI1x4qdzE9pX\nZR+FJ1LUNWjJrZfMnllaVvCNwFdrDzCUeMjQ+lOboFJrsoRqAAsihESEFiwz\nVQ9JBkVhfTY5i4ooj1uotX1MM5r4gGaEsSSOthvXoxzTVfp1TsWr1pIDRNKH\n7xNWdwNHQVrKX7elI0PCe4SuV5uhupjxUxceRNW+0iiHCrglMjvq6AySjw58\nW5o/BTOd8jydCbgyrBIbO7rwHbt8L/5E+K2xTKR0cDsB/uBLjsouQorgRPot\nyjvxAbhhUsAtpHznZFlcTcsaPwpH6OEZH8Cyewk89wt97BLMdAvV0+Dpa1eV\nRTVLlCjop59KUzgAKZ4jPv0ONdNVH2LYVOEqc+LYd8EQtO6fnrj+tDLNKkvV\n0HEQNXeszxOJn7Yy47nHTGLyYArkSvT8MqdFKSJYg3+9nGq/vX7s9ZpFtahv\nAhym\r\n=uRFN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAD9e0YvBt7+RGyhiZPXo9eHjrdPxisZo4xLeuovTrUoAiAvcKXupo+QE06tgj4mfiy/o7bCLhDptxnx6i11Z5AEdA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.2_1548794768052_0.6644695854154405"}, "_hasShrinkwrap": false}, "2.6.3": {"name": "csstype", "version": "2.6.3", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^23.3.5", "@types/jsdom": "^12.2.0", "@types/node": "^10.11.7", "@types/prettier": "^1.13.2", "chalk": "^2.4.1", "chokidar": "^2.0.4", "fast-glob": "^2.2.4", "flow-bin": "^0.83.0", "jest": "^23.6.0", "jsdom": "^12.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#f9f1b8375fa922b2a30489f6c7ed69d62894a433", "mdn-data": "git+https://github.com/mdn/data.git#9ec55b3a1f609f2e4dd5ec6603fe24e27c28e9a4", "prettier": "^1.14.3", "sync-request": "^6.0.0", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "turndown": "^5.0.1", "typescript": "^3.1.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "0f57f01cbba7755f0d9e7db8b4c84baf76c85fcf", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rINUZXOkcBmoHWEyu7JdHu5JMzkGRoMX4ov9830WNgxf5UYxcBUO0QTKAqeJ5EZfSdlrcJYkC8WwfVW7JYi4yg==", "shasum": "b701e5968245bf9b08d54ac83d00b624e622a9fa", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.3.tgz", "fileCount": 5, "unpackedSize": 1611895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfmXbCRA9TVsSAnZWagAAiqQP/Anew8SXBBc5MkOudqid\n1SnsGJoVg8bkgs0EkkKfo0rrEOiAqt8JYVzx6uZI+qjjqeM2tcrsoazE9if5\nOFDSCkfEHvDtHXnnxkTmzHYQPNWJr/L66+S/jv8JfTPZ/Z6e7VU2GyTpo/3F\nQAHXeEgu8Pyv7llgn3Gc+ramhQpcSxMYUYARNp8tL9VWY6Fd3YVvhnSi17Ti\n/Ef35vcy0Vb9TEOdO+LICkZc3s/ThNbWs7wp2MyeGu4Fl9dnVyAbawEMge+D\nIYa5hOmgDLqfHGsEvTYdshhbTM+5Qnt+yT9sG+oEiKQN3QiTf6ulLAY/EcB1\n9sx1t7+RyAuqwj6UvwrGJ4fhujE3wb7Zi5b0h9SNn+DF7171PzsGBqwV2CiK\nfyyecdlmq9ckiVIvUgxlEwKiklykJkPyRWa9NrbBrR5tOKufkTuYNRWZV1g+\nGOj4DPWrjkFqyPuF0NCzEUHiN1MxTKXOCKfZ003MFNn0t4IoIFmcJU0/n87+\n0yTiMyY8SoYteuNxq9c9tHBIxGwz4HtYcNRNHjL+hvjyRgadng0GrkmV0vY8\nSxHm33+n37RmHMkfWHKTCHickUZlhhAwn5PN4FyYfcgreZrCJT2oPqGSUIOt\nS238HZ1ApdO151L2lHie5H2iXsjj3/7x39hPHfbbGUK7oV9minWONPePEJSC\nJLJb\r\n=w12j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKOJEpYdpAibfOamtJwPkTQy8UfHi3VmZgWpgUHdoWMwIgQzGZJqnYiifPhnJYITzW09L6jwTD98qgLmX12zxnLCs="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.3_1551787482557_0.8345260757591701"}, "_hasShrinkwrap": false}, "2.6.4": {"name": "csstype", "version": "2.6.4", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^24.0.9", "@types/jsdom": "^12.2.3", "@types/node": "^11.10.4", "@types/prettier": "^1.16.1", "chalk": "^2.4.2", "chokidar": "^2.1.2", "fast-glob": "^2.2.6", "flow-bin": "^0.94.0", "jest": "^24.1.0", "jsdom": "^13.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#cca8064adf09a9e95a8ff0d2abe233798912b5d0", "mdn-data": "git+https://github.com/mdn/data.git#4547ad7194b75b3837af0c6fb9228f848020d8e6", "prettier": "^1.16.4", "sync-request": "^6.0.0", "ts-node": "^8.0.2", "tslint": "^5.13.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.3.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "fbb202231c4cb53e3474679bcd736010b4664de3", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lAJUJP3M6HxFXbqtGRc0iZrdyeN+WzOWeY0q/VnFzI+kqVrYIzC7bWlKqCW7oCIdzoPkvfp82EVvrTlQ8zsWQg==", "shasum": "d585a6062096e324e7187f80e04f92bd0f00e37f", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.4.tgz", "fileCount": 5, "unpackedSize": 1629827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctwukCRA9TVsSAnZWagAAGU8P/RXNV1ngilFm+kHuvac6\nGfY+21Ujs4HX2ONbivCPup8Ewl+ULoT8bVm8TEFXPWuTkFBQMQueCcsfPCXE\ntdc2uATzQCnkitP+rbrdM/AdEcPyKiZ/JS0j5R3G0aOqhg4vglaC8tsPHi4S\nfTaZmk77iLmvSb6Z3DOEWivdnBhfiUuvzhcKuyg6XrUof7F28TmRx4yf20ZI\n5PhFJmfCEegJLm/8QzuAvkt9cfr/qfyHIJ1ovkxG7pnKi9J50UunMHaH4QPk\nKKrytZb41snfqIt1uea3ONKlbs2E2I1qWwWRdlviqbt2G4WtUYq6e8YQReBy\neNGTAHYZ1WIXp+86jlDUaFRUf/6NaEGd5JY9xIkFkzxEluQDAhyC5ozamLoX\n4SVixs7Ky+3dAGMFnAxeDKEzBJniYcus/yjeFtd7/zP7dfVcfCvf7BHve2Po\nFgW96OoVj2gB7j7a/b5gha9/PzEwieM1ke2ZB3ibOJRkjrF58ptbV5UUvDcc\njSCR4nFc61914mOh0AeaN7GebCrJtArk4Js817HzkrAWsyourX8aHkyq+GAP\nnvIyGl4RXFh+rE8UR+U7MzsnP83jXPNTDJZjuMnuVgFPn0GbIFtIeeJpAzWS\nlO0DN7EU0h/uAly0a3joNWyzo2HstEy6RtFr+KZrUWejR/h7R6x/iaTQhyIp\nv5Ze\r\n=Z24V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfzMyFQWnIKYgEgzedqlaL/Eo5xvOuTjIrpi30Il/9MAiANGGZUeiaoAj3FAtO6n+jCilaZhFJNC4KqOJSijxyEEA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.4_1555499939405_0.7022017094631863"}, "_hasShrinkwrap": false}, "2.6.5": {"name": "csstype", "version": "2.6.5", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^1.7.5", "@types/jest": "^24.0.9", "@types/jsdom": "^12.2.3", "@types/node": "^11.10.4", "@types/prettier": "^1.16.1", "chalk": "^2.4.2", "chokidar": "^2.1.2", "fast-glob": "^2.2.6", "flow-bin": "^0.94.0", "jest": "^24.1.0", "jsdom": "^13.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#fa4f4af22b01fba5d4745f01050d6030ea241d43", "mdn-data": "git+https://github.com/mdn/data.git#de660a687faf905ef346e2840b1c14e33d660bfd", "prettier": "^1.16.4", "sync-request": "^6.0.0", "ts-node": "^8.0.2", "tslint": "^5.13.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.3.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "ebdd0005a13cc677622b259e468c2323343fd3c3", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.5", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JsTaiksRsel5n7XwqPAfB0l3TFKdpjW/kgAELf9vrb5adGA7UCPLajKK5s3nFrcFm3Rkyp/Qkgl73ENc1UY3cA==", "shasum": "1cd1dff742ebf4d7c991470ae71e12bb6751e034", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.5.tgz", "fileCount": 5, "unpackedSize": 1641319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6+TlCRA9TVsSAnZWagAAG2wQAIw/cdsqu0fQUqIm2wwq\nO5K/F81yZXYbASiFFXnZ6m2XAxRshlW98knpOzlol/p5gEyRC+WlExCeUSa0\nRUw8KyW2seBv2E6L5y93FqMg4yE5nuPk1Q4j3A36fhLhd4cwwrDB+j0oa6vR\njsRYZ7fKN80US+0ShJXdbSi0iI1zTevRGGtzV43hbVdkd4/40+n02qrFXtSz\nPEFUNhUHoyRAm76einwR6v5WNHgAZe9UyR6d6jWGMwshffTuMnxZHjxTJTqC\nXkzC624O5/9PDLwRhYn7woZjzu4TO4oUypjOUix02R59a/qE1YnjMJ5u30Ob\nUAR34p/PrpO9e8BzELTiN5U28OPpUiS3FLVTEg8Tcr0YcMj2NNdVcn54ZLqj\n5cKhS4Id2zRK6dx12hWAbtGeJA5wDnU8DTxCEZMZO2tjuaAzp25aDoBYx7GC\nhTGMKqbTUdtk0y2R6fO3jv9iNBHLRxwIi7+1hMLtrgJjyLXZU0nLHhX8rFVf\nptav5/xm2NrbaUK5h5sjt71qwpBRnDdTIB8PPyEbfRgctc/2VgqHfnjO6HU/\nB+khGXDJp5jLv1TO4dgFTBQvlUIcvVWSd5somAC0bWmu+Ixqb3cM7FF1tILc\nbKYCNkfehTeqx0xggc84ftxpEIgkIBQMR0un9Dpyko9w4rARDZ+7jkYGVQN8\nG2iV\r\n=8Bxg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3KC+rjIGpxJBg2+japMktAda+7iWC/Sp76ierOSUpFAiEAk4+FbYdKkE+ZtNPo4KFzpIbNwxS5Hf7c1wF1Ysf+KxQ="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.5_1558963428468_0.7942649181968229"}, "_hasShrinkwrap": false}, "2.6.6": {"name": "csstype", "version": "2.6.6", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.13", "@types/jsdom": "^12.2.3", "@types/node": "^12.0.7", "@types/prettier": "^1.16.4", "chalk": "^2.4.2", "chokidar": "^3.0.1", "fast-glob": "^2.2.7", "flow-bin": "^0.100.0", "jest": "^24.8.0", "jsdom": "^15.1.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#0d355f21a6d1a22057a9f318b33ccd7eb7ba6576", "mdn-data": "git+https://github.com/mdn/data.git#fcd2fd333aca00c08ab3f876adce10e875b95d7c", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.2.0", "tslint": "^5.17.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.5.1"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "5874b0637f9ba68eb3db8ffd5a55ecba84e0290b", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.6", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RpFbQGUE74iyPgvr46U9t1xoQBM8T4BL8SxrN66Le2xYAPSaDJJKeztV3awugusb3g3G9iL8StmkBBXhcbbXhg==", "shasum": "c34f8226a94bbb10c32cc0d714afdf942291fc41", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.6.tgz", "fileCount": 5, "unpackedSize": 1650450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIxErCRA9TVsSAnZWagAA1fgP/it4Y4cNbe/jNKYO6DW0\nWpaN3OQtyNway+rohwqx4LyxIxshXw28I42xqL/OZ/pPNuB9U3T7tdLkDvpy\nxVam9SOfJJIsKzQn13/MORbTtTseNqWJrjXNHxihZI3p+ivrLDxFoIp6NtzZ\nGYsfnhrHw0MO5bJxAcHO1Cuv8uWKRkckMzvttcx5yWuElMWuqxoBtmBgq4RB\nxu9clDDB5VloU0tq2w6HkbGsIduW8ACYrouyyF2kOyYZ5InqqeHc/pQ31p5x\nYG7vNHMyYLCbGjanW6T5DawTgi9vK06UU2JcwS2zbvU6N9x+pZWbh6nD0hEL\n0bOqWc8NpM9Aw+nlLx5Ki9y8h8SWmHfPellKyvBg2qetKhfbCCuD8JuvfeSU\nj1c44aAtwH50g33KjZobEFyiZ8yJ27SiRN9rewD8R1j8iJVkx6EM5OR7KiQ8\nJUWCXX42lY7Qar90ammt+AWfZMJTACSX8EyuyH6JgFNfga5LBu3ZjbJyPNtA\nggel2ho9tZIbvaN4xm24EM95WxdcDSmIPSULIXROEr/mDlOyS4sbjsVuV6Xn\n1ossQwUVaSYjQoHOu8zePZHBIHVyBFL9P/67to7Jm4qrNYAJJRyA65Uv4L2l\ns4OA8Dc+FnuStLcAFDZqF5xlDfEvO4AqcBmd4yxuthnkyu7Yg5QxFCfr1yRP\nMiXO\r\n=fYqj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIERZXFFilTU9l7EUOQtHmuFbftZhWyhy4bZswSbt6zZ/AiEArCEVH4y8DFXitY28segBLk6pUQATyz9pomb+YzjCu9k="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.6_1562579242684_0.19430006458351"}, "_hasShrinkwrap": false}, "2.6.7": {"name": "csstype", "version": "2.6.7", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.18", "@types/jsdom": "^12.2.4", "@types/node": "^12.7.11", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.1", "fast-glob": "^3.0.4", "flow-bin": "^0.109.0", "jest": "^24.9.0", "jsdom": "^15.1.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#18f08ca5304d0e7d413eb478f603ccb856b811f1", "mdn-data": "git+https://github.com/mdn/data.git#0ede72d90cdb925d9eccea8806ddf9dd87ab2453", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.3"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "63d4464771a8c6702f8772486f45baf8eda0e116", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.7", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-9Mcn9sFbGBAdmimWb2gLVDtFJzeKtDGIr76TUqmjZrw9LFXBMSU70lcs+C0/7fyCd6iBDqmksUcCOUIkisPHsQ==", "shasum": "20b0024c20b6718f4eda3853a1f5a1cce7f5e4a5", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.7.tgz", "fileCount": 5, "unpackedSize": 1660455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdl5LkCRA9TVsSAnZWagAAaz0P/0Xd3tg1YTooE1whWjHt\nRmaAqdYMrCRbymv1P9VyxIlsaO3MIkklG1HWXboX5VDU16cx1P0JHrTWipIa\ny+nnJWI2KZ6S4g2LLAD5XMqoGrGyQj86t1jxdSEXJfecn2VvZdbtlZ35FZwP\nqRJ+l+Acf+jItKRR1IpsIQlHKxKAcpE95X6EExdGVnaS47a4uz0g8bpkZNXm\nX7B4KyJ7frRGC/m0AmGqztkpzjza1VbvvWNAAOmAI4GY38dSNv0wnl/rkOBM\nAxp9q5Btt7FnUOOGo4bbtf6Jn3YNTv0F5cmH64SjGxNIgPgVpX1j5iKi3Ju6\nM5Y4p+UnXAQEHQzlcQBMnXuJd2a8/Rvyl8f2HLX/59ri23B4cLXWGsAmWqpo\nfmAVbnlAJHUPouvYqkLrC6b/i2ZLsRkYHRSXdEhVY2D4ovlTZB89pW6afc+B\nu549IdzxkMv4d8987sfUFMezy4j0OyH+cEarQjlkfqvJ1nPj2MIex7o2Fp1w\nziXRQc5mPHOFWSeO3VjCf9YMNvL0wJ+25SKlo/ykRD6Nm0LXSU67INOtPa7+\nNmvDk/juGSgGP1itI3xZN//eCOFh0m9QEmgFcFG9S2I1/f617ocC4LuMcdDS\nOpScJ6Aw9uK54PYbAzBjjFzUq1iIWrZUK6l1gWSRXZrBuHkLMOKlbDtRapeC\nK8v1\r\n=9wY2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEq4SqPFBotG99XQ2+AIBTVgsf7LTlkaZpp8jsuR6nQ2AiEA1dvkL4XWe6qvbOKgzr1I37QphTKlxjxCnq7ry1m5h7E="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.7_1570214626027_0.24174905927855095"}, "_hasShrinkwrap": false}, "3.0.0-alpha.0": {"name": "csstype", "version": "3.0.0-alpha.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#29c9fc4a532849aaff7807acff892a251c303d1e", "mdn-data": "git+https://github.com/mdn/data.git#4b8d343b3e6e724ce58f1b156a3022ba54bc5e8a", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "test-src": "jest src.*.ts --no-cache", "test-dist": "jest dist.*.ts --no-cache", "prepublish": "tsc && npm run test-src && npm run build", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "ab95fd4f571711ca9413362b641f40e4a0ec0669", "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the [only unitless length](https://www.w3.org/TR/REC-CSS2/syndata.html#length-units). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-alpha.0", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-w85ft175+ey4vYpiNFsqxh5L47kCtjsEksm7aHC6C0TZPZ+CdEbz7HrlkhQsVtkIYI2cx17R8MxFZG64qXvhOQ==", "shasum": "2bf992030f30cf1c2a3cf3db1bc9f6683c61e4a6", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-alpha.0.tgz", "fileCount": 5, "unpackedSize": 1713793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0EobCRA9TVsSAnZWagAAZ1cP/0DRUes8q32cyX8k2rdW\nJlqVXXHcxx9af8yBFUYgsQWyh5e+nP3nd/j8SI31KipYtP6cfCcChe3KbBmC\nS0KLmhA/fMJLm/bJm+LXMBq0TcHyMJy6TRmqatL3wEwVneIWieyxNgdUmaza\nHoJQzBwmPKQ7KHh8HryuGmGiFQHdFr8ZSB/N+rjkHhm6MyD3hRy8WbjJy/Vn\nb/YO3TXcMdYAlb2eKKu5p59WOGYUIQwpGpxxkGFZ2eVambZb8g+ALKWTMUHx\n7Wm2wdFv2XU8L9R4UHrcewCpOiKxJxUUvZ7GM8tA/divAwVAOXEY75jwMH9t\nwZht6a1h+069Pm3itlFaRAqba4Hl4lwmYsXLrteyqRqK/vvazGgvZlVQV2fK\n0ypRZbyrJKqpGD2I67P2rOTfUWXObYHj7qs01zfuysmiKkQbBJQbYW7vi5q3\n3PHIvz/XtViYvTN92HdGSgj+ftLwVDRkEnTDwFU932iMlq9eoYQyx4E7u9sm\nTjuZ2GShk6BitCLrjQwF9VjtoWBwyk8zYubuN86VdDRDuTfs0yI1RpNF/qUs\nT533Z3Oqsb/VV3jT5DqFkSma0i+bcL6atZm6CA8ZsGuSqL3WyAu9+7E4Sey2\nFdl3bezGKRVMVW5RvdSDQJLHYzVmtUgviIA0nHckOTJEpmAZi0ul2xgIl2bG\ny01k\r\n=K8+v\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFuQcgxOF9NIN9RtO75HQxeBo2Rm8IJlESMaYnW2fuf5AiEA7dYMHAAoytjk0lh+hVygFitzUBc58O0qq9byOQcKt/g="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-alpha.0_1573931547044_0.3195839117421029"}, "_hasShrinkwrap": false}, "3.0.0-alpha.1": {"name": "csstype", "version": "3.0.0-alpha.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#29c9fc4a532849aaff7807acff892a251c303d1e", "mdn-data": "git+https://github.com/mdn/data.git#4b8d343b3e6e724ce58f1b156a3022ba54bc5e8a", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "yarn": "^1.19.1"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts", "prepublish": "tsc && npm run test:src && npm run build"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the [only unitless length](https://www.w3.org/TR/REC-CSS2/syndata.html#length-units). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "49bd5630ab3fdbc9272f40b23c610f01848cb355", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-alpha.1", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5Zmp06NWOB3CCXE3D3FI/zlboGaFlJ6HeTd+GIlxQIKbYio7t9277EWPu0j6+zTZlLJS/4+9w5NFaWM5/SHEMg==", "shasum": "ac80e49489b102c872fca244e2b206aa0d3670bc", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-alpha.1.tgz", "fileCount": 5, "unpackedSize": 1014378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2DLSCRA9TVsSAnZWagAAlZMP/iuW2l67js3Sa28vTyHI\nU2MUrFB5vgvoiIaF1U/Aho/sPAMGm+m1sjjNARUKt1VeibTetXUA4XPFV9j8\ncyw0Dx974fSZCaqaDcxgDMeQwzuGI7mzNcR1k5lJdrlfVn1ZrZ/8IjpN3d6A\nyGyI1DJQOlJoc0htG5tUhBfUDaPIy+rkFGL2ls7q9cX//15K11eekgk+eDzv\nEMG1j/jfgfV8X0Y/BJnLkpiRRG8TLHSKhQdhr0q2x+G9mCIq5QGTVVOf3/Uz\nz40OCR7EwOe1t0Cp1wbWS31ixcgh1nAWEcJuuQL9ZsGzrozB0oE8EH6AytBc\nJlNL4JrHU0r+4Lm94NSkxhh/21o73i9pgArtY4HUIVEKTKWFv9jdWZpDkRKZ\nDJWSro8gXTTwKPSKQvkkO2iRNtiazzPeZDyW5gaUcEhDffFuuFmKl4kcBgzu\nKZ+JgwAJRHr+PFwtTUTeY8rK8sEPHJrlk9uUdhjbR1FJA2IiBaxDa7zFDcUw\n73KpX/FuGiJ7o4GbjI+zn89O4TvgYuJ9YjKed78JN76c+hALO4rDldT26glw\nGVAlbcaNVNF0goy42j42zv6ogHDuOPdTGnXCh0O8W9LUW/ycMdB+3dSnFRBW\nKw4rZdobNGZDs/fDRB/pdHgeib/EsTKR4kiTYz4gVCysORCgGAqM5jpnVY4+\nSJ0G\r\n=2Pz8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRgyNxvqA3FY91/gs6A9xbpVGCdNxNc1qqFBSPoHbEAgIhAK5sv5WmAMj90s7j0Hy9YAlBa+JxvOrlnWWwcWQmtZ1L"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-alpha.1_1574449874061_0.08814888270525767"}, "_hasShrinkwrap": false}, "3.0.0-alpha.2": {"name": "csstype", "version": "3.0.0-alpha.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#29c9fc4a532849aaff7807acff892a251c303d1e", "mdn-data": "git+https://github.com/mdn/data.git#4b8d343b3e6e724ce58f1b156a3022ba54bc5e8a", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "yarn": "^1.19.1"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts", "prepublish": "tsc && npm run test:src && npm run build"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the [only unitless length](https://www.w3.org/TR/REC-CSS2/syndata.html#length-units). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "f1f695667d424cbcf0c03a869cad6d9b0a5245e7", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-alpha.2", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SO+m7t1itAZxQ6srqRUNoS+EnDqTgU6+zfL8qCHGjn8KIWoKpt9alWF9UNswPnx+log75YffzvtKOP/jbP3Cxg==", "shasum": "454148665cf8ed9667de92360e7dda63c682896a", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-alpha.2.tgz", "fileCount": 5, "unpackedSize": 1014502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2EKoCRA9TVsSAnZWagAAnzwP/0miXUDXg7hENQfTIh6G\nTMrVaWuu+SvalQOKJNikWL8mEMthL706e6h51q6BSvBYickclyMOaSD/2jSf\nrkuWVdSbznM5op1a/dEnZPngCDHfa38tlEy/5HcY37ZHCHphPPni/VPXamd8\nMoOGBAqr+VmcKYkVWQEi62y1Zd9Tkql4HLel+GSudi4Aq8BC/KXlvkc0ZxwP\nPT50sA8VkS0blulozdEAyXkxOvdEiX+H9P8q0bCKmnZG66zJvQ8+h1VJQRJl\n9CAewREUydDvjzQsVXnfAgCykLVebUlNwsjnEIs2XWVN84nzp6hptxdz30Y5\nigEyjRCarnySbHfM6/TvPW6tNNRSNBooDVb1T2XTuq2VfvOQ3Krj+vnu4LX7\naoXEA4StHXoXHwG/32lx2AoGgvSd5R0Yv7ttvmEcAeWFbdjgaKyOa/yxtosN\nesUxW/D1+0z6rtjOAVWyeA5lg07hvPfTjPvg214OG5sEJl6IOGNm2dHtoj9y\nalnKiAPFnfJRfmGEwq0CAXdOmv9f/65VXdVfzDb1N490fguLmvTDOJ9elsEL\noWzfen04d7XY63Qjy0qQYg7aScyYODFnc+pSJ/1KQfjgJuJL0LaWyoUX1EQX\nD336CVSG8F+WR0nk6aRIqtQDBJ/37MQI/p4PPRjUpNd7cKHdAj++cAL+HPJ6\n6BCb\r\n=lJd0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEoh7LXOhLvSd82aQcYgmb+/8lhi0myG6JTWF2h/QlTpAiEA8OG+j2PzkYhp/bKDCcf9+4VATK3iU1YaQ7pDkF1A9p4="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-alpha.2_1574453927989_0.7621334217591116"}, "_hasShrinkwrap": false}, "3.0.0-beta.0": {"name": "csstype", "version": "3.0.0-beta.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#29c9fc4a532849aaff7807acff892a251c303d1e", "mdn-data": "git+https://github.com/mdn/data.git#4b8d343b3e6e724ce58f1b156a3022ba54bc5e8a", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "typescript3.5": "npm:typescript@3.5.3", "typescript3.6": "npm:typescript@3.6.4", "yarn": "^1.19.1"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts", "prepublish": "tsc && npm run test:src && npm run build"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "4ec354b0db846df28ee4e5ca46fe2a49c6e00b55", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-beta.0", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c4/v7/t+8li6eGJDygw+C8M/CgePPm7zAnLy5Hibz8gobrJ/lLqktDvnC+HAr7HUB8IyCCdNN0vaBNY9ThxzOQ==", "shasum": "ee923fea497c54d0b0c22fa34e6c7555a2c8769b", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-beta.0.tgz", "fileCount": 5, "unpackedSize": 1019717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5BXVCRA9TVsSAnZWagAAioEP/iXj/DVQ/PhQ/toWDuTZ\nfq5B2c6uCfSt2Yts2J416e8eYsR6NYNQZ8s5dmRdZjcjkZjPlWOPAl6/MjMz\nZQVYfBZ/k5FMDh3FdqyMW+neSgr/hSnfwuIdcjg7pZfedKwGIkO3VvDqpJAY\n1DtZXyou+sw7Sio4My9cQbYKd9v82vMJLPB382uwQn3LcQfG0Ab0uG9jH+PV\nPav1c/q+2hvkMntHQoja6wQZsrEcRdZZihaScrO34Wwxjl6hoZ37gVeLLo5t\nSX1sNJoZ0sW/1ASBks7mMgHAFCiCDN3GM584LsGgRsFCXC6bMoR7vA+AREhp\n9iW3LyknWvu2Mmx1VNoMyWCFRL3yrEckkBo1HXh3QlO5m0X6LxnFGBzyZ0H7\nnUGw2q8pB7OPDaP0sZTwnQKKgkOsiSBU3ls4Q4d0pDOjXsah/p30rlC4FFS1\n8r8z/QM2WoE5wKM4pvLiYPkKsLMABVSyVugdjA8KnwNoGg/KTz6Zg5xSJeOS\n8q53aUNIY/jtH+28965n52N8hbCU37t9HOqrvKV7zIXazpnk3wXOk0gLWmS5\nPcBVUcwoT2RI06yH8NIdTa7ITnvn1DkuBofnIsxjE3/O4LkHHsPYO9HpxZwl\nx53k25ZWxqsfXwUgFH9D5V9SjVIOqs5qctKmx/fDxt0e4p3/AFUpG0b0mtlM\nGCPZ\r\n=M5yy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICzjb4kUVMT0sDzbRKL7wojAHaNPe7of+G8+dELbUaCuAiAFcc2gUsGvLTLXxAPvLFBBzNxyYRrgBKwahor2M+6ljQ=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-beta.0_1575228885402_0.598169076839909"}, "_hasShrinkwrap": false}, "3.0.0-beta.1": {"name": "csstype", "version": "3.0.0-beta.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#29c9fc4a532849aaff7807acff892a251c303d1e", "mdn-data": "git+https://github.com/mdn/data.git#4b8d343b3e6e724ce58f1b156a3022ba54bc5e8a", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "yarn": "^1.19.1"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts", "prepublish": "tsc && npm run test:src && npm run build"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "92d838e6d8e6f08db50ae3fc3772ce86d98c5f13", "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the [only unitless length](https://www.w3.org/TR/REC-CSS2/syndata.html#length-units). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-beta.1", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Kydox1cFJz3Jm4sAf4gbnEvpmR8m0sGfN0T8KEdbaVVkn32lhTwsXLbJlUTlzX9AHmzYnRa1vplzWV9hN1PCBQ==", "shasum": "7f184f2e70b135d1b102239698d9426b7689e819", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-beta.1.tgz", "fileCount": 5, "unpackedSize": 1025379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd57+XCRA9TVsSAnZWagAAZu0P+gJwI7JE/DPuFZVXDrB6\nN3UL3drS56dDb9e8HokEjoYONX5aFCmMdK6DKz+WqRReOaVCimHDL9dXFI+8\nk2+ak8NhzDL0ZAdEZIprVfBuYRz9T75LFboRAe+fTsdXBFg3vjoC6WjLZ1Zy\nLnrxOxrk+5c2dWiul95ePeCIgqIjEZ28czFIH72aH86bC7Vlspa2k4gzBZUC\nx/oBX4wuasZhUyofYcGdeJKulWq8IkgkgIYaLM+k3b2KmXPbF8s0m6WZHv+i\nkaWwjNAwDOGmrDQNzNPdNgtlkM0IZyieae/G2uqOQ15AyM34VyK2iar7uUf5\nKtuxn+aaeF5N7LCSOF+6MKFqzPAjhbqwi/wJra/GTqVWLy2tFYDhXOMw5hyV\nicY+fpZHvNPSBA69ePa6ZAl4od3SAIzBIVthQqyOvttjeS0UK5RaFNdNIHa4\ncmZ611zCjnJbGeGnD8Z5LwS5P0r3cEkUIWaAu1xXqj+z5DVPOATGh4AcLjWe\njcVZtsMmezjY3qvT4Lh/ZgPHINm37pULAv4zN+ZpNClfYTPyEClt2cO/P65a\ngxsh5p/KzKIB0bxnRvF38u3Jux1XHvlnT7qs0uf1CCuCF0rLSklGJtECnsbt\nE+rt2TfsJ/lOuKeckHQj/me3LkLL+W892nnhF6VmgyvSa4KZ8MxMuG7I1ifI\nFH3z\r\n=eDfH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzg4tbEfCEp9CcRc8XVaPVW/GVdxzHKhp4B/iIOoG9fAiEA9ImaG6NqMAJCzHAojFV3w0rcI9Aopom3KUh0fN6qayE="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-beta.1_1575468950526_0.1714469915847665"}, "_hasShrinkwrap": false}, "2.6.8": {"name": "csstype", "version": "2.6.8", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#240e1c930c5de4e632ed82a61d80a3c72dfc4d54", "mdn-data": "git+https://github.com/mdn/data.git#4400f8133582f541ab72b2218ef2549df3e53c4d", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "019f3524660f76764cded3fd4e429a353ef3464b", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.8", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-msVS9qTuMT5zwAGCVm4mxfrZ18BNc6Csd0oJAtiFMZ1FAx1CCvy2+5MDmYoix63LM/6NDbNtodCiGYGmFgO0dA==", "shasum": "0fb6fc2417ffd2816a418c9336da74d7f07db431", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.8.tgz", "fileCount": 5, "unpackedSize": 1661408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9y6kCRA9TVsSAnZWagAArO8P/juFAaEwJjxcstTlr0gn\nn7c7UJmPom+Drc9s7CTSA/eBsA+XF1ImlDsfGKiq8+Pqb9iGzSybJXxpoOhO\n++GoW4RYZWXNbiP0VrKGyj8mjnJNwzwTdL8xp0UoCEyov0U51aYcydsZ5DYP\nutVbx43rVZmQvgpAnwA0cHMR1j9uqpUzfhaVGl24c0Fl4X3pJsxoD5xkDk05\nXNFAGO89gW7Rr+2zrJOD3gnGqdWzMQvfFCi3+NMUdOVN+Pu91db5zn9pnKby\n1mN2pDiN/7gL/VKR/tSOdQJBYF4ifp3kzbiRw3fmTeL0ZxuhfwGOdZl1utwV\nNaTWFeW40aF09dL3BCXRvZiQrfnDjmezWoTnhnAMgo0o+059d1MsIrAWgAbs\nMxsY8H62qelht0bTJkRXfg2NLRbj2GQ5MP+NLx6+OCDOregL0l8Vu6C40fRM\nVN2YcxjtdFUCR6OaKjQF/gcDq/bLXofDw+bRHUhkEXpFxkt/60lAfjm5xopQ\n+1eaD+JblzG2TojO9hHnSl/r2lPGdmte+ZSH75678q/ZqAWk0ZJHnl6A1EKY\n14lMEKYblYKvoDrSTr2j3WvcDH2vF0Z8G/AwtJ0u3n5611rVnrZ3unnZillZ\n92pRigqcAc74b4hgry30Sv8ZGUiqxtXy5sBmsA5FjpolgLBH48tHVL+W9Lwy\nt1G+\r\n=U75h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEjTbus8wy/PjwfqXCA6JVo4cBGQ+Y/EBqswj0N/k4vrAiAuQDxjtC4tkY9QhyQApY4Jlf9x5d+m3ZLbk7xrJnXxow=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.8_1576480419764_0.12328714712750855"}, "_hasShrinkwrap": false}, "2.6.9": {"name": "csstype", "version": "2.6.9", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#1898331a758ceabd938ce7ac68427825a70f8d7b", "mdn-data": "git+https://github.com/mdn/data.git#c3e407904413bb5c4b823ba1684cde2a3c4f11d0", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "d95f1a9403f1af3ae10a480f0c3d72c6eac36d6a", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.9", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xz39Sb4+OaTsULgUERcCk+TJj8ylkL4aSVDQiX/ksxbELSqwkgt4d4RD7fovIdgJGSuNYqwZEiVjYY5l0ask+Q==", "shasum": "05141d0cd557a56b8891394c1911c40c8a98d098", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.9.tgz", "fileCount": 5, "unpackedSize": 1668730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRjzuCRA9TVsSAnZWagAAMjoP/RcKL14me4GjAl+f8k/D\nyMiiQR2nZqU16Pldxi6FnGcA1Q476n4Uk9MJSw0rUmTPqZNnMlWgQfXpPKdW\n+oh9io4Um2SnXp2hWGIXUK5SJEydxavFfjgMuPBN2MzAGvteDd+Vq/RFmpos\naI6Mpe+goCNAGcgTy1da1IG20MyJW+7ClFtfSjOBF/Fq/7k7OYkIVgNtDZ9c\nIq3ckiA80KFsw723TYk/n8b5bEEPcXs6UMTw+TpnTqLLyu+sA7rIq2w/yKdW\nLzYD+BmZvC2ljJzedQ/jZtd4Qi/IVE4GLzMYo+eCJTXW2nYoh84I8mKXbyRr\nfR+Q91vB5KyO7brynq0smu8s3S6nEnX/WR2jaxLDUV0Tq4HRGK4JV7lWCHa7\nCAMbd0FwbHppBt25mrJqaeCX6nY8fF3+u3mIXDGLR6Xd5guQyT/QMQVZSR3U\nX8zzNh4U06iB5rQ0H1JsQzBfL7ICnPyqobfk/JbZCsPt4vVGcTSAnKi4xzyq\nEWPL+phUshLZSDCkpsW9kQmeFwpjXTgkEaRnHCEow7cISDZOiF/f+xeh/+ny\nkmHUjaZm3/izfhPqdaSZPzQWeUbEEjePcwdrnQaE/odvJa0AKevgCJoVngjc\nHIbdX4zp2TZb2BamBHFARFU4fGOwudOdsXE2zfVskL6RfEuMdcSjPoCsS9Jk\n3pME\r\n=Cu6e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW+hT3i2ye0Ir/gTEwSuGmCQt0rpJMEu0h5mXgLxKQ+QIgOaWFMqIKUCPGUq79FPMrFFInTW2GaBpBrlmDfwnPa88="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.9_1581661421773_0.8643065330839861"}, "_hasShrinkwrap": false}, "2.6.10": {"name": "csstype", "version": "2.6.10", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#a78f891eaecdea55d6b24c779dbd3172a92473d1", "mdn-data": "git+https://github.com/mdn/data.git#aa85883d41ae4f47fdf230466cc138976cab8b38", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "b7f124835030346fcf2f4a2861bb71dcdbff8676", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.10", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-D34BqZU4cIlMCY93rZHbrq9pjTAQJ3U8S8rfBqjwHxkGPThWFjzZDQpgMJY0QViLxth6ZKYiwFBo14RdN44U/w==", "shasum": "e63af50e66d7c266edb6b32909cfd0aabe03928b", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.10.tgz", "fileCount": 5, "unpackedSize": 1876301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegaIaCRA9TVsSAnZWagAAWWcQAIO410NUyCidZitttGSs\n5ulu5BIaGbhWLvzR1u+EOzYyEa4IqSQixNDMghgnyH2+sg90EnbPLundrR/P\nvUllqUVGZYEsJRwIlp+MqApQLR1wxFYQlr75agugSVbrbEjAzIdTVUhBh8Ib\nHdw1ctNoPXLyZLqaKfuq2iesLqI5FhmcdsuGtjqRM3BvJm7x9FKohNfyTauH\nXoOd5E6iWpdKb8R1OlKBdiAUDnu18OBhIs5oQNqPEEFdB72qrElT7eQxJXEZ\nBFmtZFs5cgUYN9YIiQU4M1E1T25PnFcD7Aqt6BsHI8nHngm2OYgJJA1GMEU7\nkjudDyp5DuSxlqDj6fXsQJqxaR4qoSQ1zEgX5C1Fyv5k9IEOsGDD6WDdHOLl\n99u8eO+uO2Q62I24w4v15fFfZVN/fzDpe97tkcvJ+RlUhOBnWAdnMiy/9+CV\nuDvsH2odt9H+8N2Pk5VEgWKAkJ2tSAdh81HWhQuAPsKcbImRFvk0VTSRWjmG\nuwt3bA8ZtoDB9YdvUNcSY9YRMPgOqWi1TRDe0rOPFy4NN6OQ+58HcX+iv3ZD\nj5wLNACpH5hFfzbMXQhCCC9c7VtDDXnXLYeVqIxmxQebeVtD4l9cJi1Z8yyV\ndB7tQ7md6lVLh98cITSY+nwwci/9p91SL/p/IwBmIFedSzWZyq67E+pWgUiv\nzRLX\r\n=9d12\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWgIVrEe3QKJte8sU37XBHd376hlF8s+xNmkHc401t3QIgIGHUdPaMrlhGITpWCUzs0x5fFhm30Bq6EEo6On4iOX0="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.10_1585553946100_0.4236104377531007"}, "_hasShrinkwrap": false}, "3.0.0-beta.2": {"name": "csstype", "version": "3.0.0-beta.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#a78f891eaecdea55d6b24c779dbd3172a92473d1", "mdn-data": "git+https://github.com/mdn/data.git#aa85883d41ae4f47fdf230466cc138976cab8b38", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "yarn": "^1.19.1"}, "scripts": {"install": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "05ea273e6e9996fc110ac1a717c9f474b765596f", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-beta.2", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-i2ciRQTavetsoweVVdpJZcu6tZRTf+SPLHSOAwQpTWszJU53ldz/Jxos5bRg864t8/otLR1Waol0YiAPV6DNSw==", "shasum": "384611644ce16a3019f6ad192b4fb48967f3ef47", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-beta.2.tgz", "fileCount": 5, "unpackedSize": 1132250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegaUyCRA9TVsSAnZWagAAyzIP/0HUffByVHX2Ywqwb9M2\nvvL4kJWWVe+2g+a3Nqc0OzEiQLklWJ9RlExF21/EXoBmJkMRu+8KYgFi3WBU\nCvlYr5pEVpkgTHrjLsUzSL3C0Td4A+2rDZdyED4u1TC6cuxI+Qy3GodN3Fb8\nUBT/0VWGFB4LdvpOR0lqKpE61Nimh8rSOjFjOIvv3jhHIhPRo67iH3kpDrG3\nhlIr/hJFAe2Sata/vlBnBTYf46tdk3GUSSSNB6qOxaCYtUEi6da7O3lJ+spu\nNJxL4Cg1qPvA1sGe20NicmG8gbqoBV12DhkJlR1YY4uic+2rFhn28RL18aDp\nX2tJQ0cfELI0nByM8lsZ1T6cBl+uLJaQl39RhcVpVh9QcbnNGLScyacPZUhO\n3R6UPa5nWWZLkZ3Yu2xdQlZ6963uln7e8f3qPxp4cp+yiVfLze4EQNbTON8r\nwQklEP5n9Y1vOrNQ5Rz1eCWU+OszDN5affrFnWIZ/zSsWOhgsGdE0YPwDLJa\nE6WZEjv6lfl+0I1+WBgQauA/NJhawixxhRpVRqhCjVpvwITO2ppIOdvKRffg\nAYcboHfduCqk3I+v0WHWH+d7Oj8Wv0qvdP2xu/kcM32cEdCXK4OIxtqDKzhG\ncvegF5OUl7En7p3lRFrkfRTFGZ/bUHn8RksLBg3Ivj9BUeXELZyI8TTxGfgO\nRlAK\r\n=CexG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICAibJRAIAEmJte3JezeIqWVzgu9nbAV4gSF2dVLM2X2AiEAklK/wBSfgGfdTkiRCkqAimwY27UVsLgTC0kxuBf+g8w="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-beta.2_1585554737944_0.47694441100552387"}, "_hasShrinkwrap": false}, "3.0.0-beta.3": {"name": "csstype", "version": "3.0.0-beta.3", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#a78f891eaecdea55d6b24c779dbd3172a92473d1", "mdn-data": "git+https://github.com/mdn/data.git#aa85883d41ae4f47fdf230466cc138976cab8b38", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "yarn": "^1.19.1"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "192058f98925049b348e4b133277fcad81c4a3e5", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-beta.3", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PsRIGyaryjnpKM0+HJBxBTW3YqQy94YfJAP06PVW1NzG+C98K9xfeahCl+jlkuusE7sr+IV34lKWEotMJuNTbg==", "shasum": "1f6079b2662438147402c15c85717f4117d88160", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-beta.3.tgz", "fileCount": 5, "unpackedSize": 1132253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelrZhCRA9TVsSAnZWagAAw/oP/jFUXwt+gRlfXLC7TWtF\nov3AjtZDogFNGsmQj4ZpG4jK1rFt5sXjH6KAB2nbZhrFrxV6okYUZZNJibJH\nFCcPAnz4+5u03wQVLR2Lg0R32BFNudKaMJABdcxc6YC+pBCZUYAey0EiBj4d\nsh7HjPRsEMCHJqds0MHdpOc/TFwVH5go8uVEcvJOHCnMJXdzmXMGxETD2exQ\n+1izi/2JQp8SPObSJHip6rpFeRAJY/9d5kvk02I7diaLDRlRHUY0yhnZhuDd\npBV5w73+6KoE1a2xqOxKvwUHN9escCYdmJAZxFRxqErweJ+IodrQMnXIr2cM\nR+KSyYfris+/B81+FrZu7TB6Fg2XLicXish8dA+Cyta0RzmyfXyWn3hG2pWt\n9rc08j3rHWrzXn8MdCHjpxD46DX9QPk16OioY6B2rxKPBEcwXWXMp1zCyRx/\nLt9UIskGjM23I8zck5jPETOCOuLROvhrkoFoe/qYKlH/KYT3si9FG/9XJ6xc\nCLm6JbsrNAZucRpOpwSqcWE10j7B0mco6dnzfBseN3ks/aE66KKpWanmBg+J\n+4NKUNRQjq1JzgwrCyat28NPClfTZ0JWq1E+mcsjwyo4lNr6lzLGXcjFGCR/\nNnW/jstoO4eCdheQwYSgGuyPu6cxKTrCAB4Ubxu1JoxGgMBh/UqpiS8edZg0\nWofp\r\n=X2vu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlu+R+3G2zFTMs2lUXEiqNMPo2wDGymgSu5imXcrI2TwIhAKGafu4n/Xjp02sLx7udg6RqIBitJBylVPJQhhUgzuff"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-beta.3_1586935393177_0.9887764375908468"}, "_hasShrinkwrap": false}, "2.6.11": {"name": "csstype", "version": "2.6.11", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e74deb234e870896575ef442a57d252c7677cb07", "mdn-data": "git+https://github.com/mdn/data.git#bee2448507748e522b0e1158e420a647628e1fe4", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "2ed06d516bba6b621f373761445a8a80b5bbae04", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.11", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-l8YyEC9NBkSm783PFTvh0FmJy7s5pFKrDp49ZL7zBGX3fWkO+N4EEyan1qqp8cwPLDcD0OSdyY6hAMoxp34JFw==", "shasum": "452f4d024149ecf260a852b025e36562a253ffc5", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.11.tgz", "fileCount": 5, "unpackedSize": 1877917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/GuSCRA9TVsSAnZWagAAwkUP/1GqE8ZRDdlOG+uuji/X\nX9Zq0fTApl+YEcee4JVqxDltRvD8TprBqTY+9UPt3hfU4S6P5N0zbg76Quog\nupG9jAsoy9tkewb13wz4wodOCdDGgX2Pt7+bU9W/rUnFFOU6A5/cAmraYWOS\nNC/fzXPufG3ZhpRNlIUaZINC8h3Co3gmHlC6YewkTvNuVCkmdEG3DiOg53PP\nuR9xNowCZFgdfMS9Ilt0kYr5GG6ehszfoZh1r0YlGX7TEbmga2ylnx8lXxE2\n09tQJS2Snr9guc4Kuftji9u1+8gcx0BLOrLU0R/DPxctkWxsWq5MLoxFo5/x\n04dqNYh3h5+r1pAdYVU2oOMV8oxYHuj8BC8oFAgpxnjFC4012aoOaJteRClW\nJpLFqpNyHzzAYAuTMDifzHw7B443BouCZ+o/L1UBfzerpZUWpadRNUhXczMS\n6a8q6A86ZN82IJ0lAeZAQgxtamclav2stx1YJ2xsUqeXbJ5WTktVLz8q5rKC\nGmLUKartDjU0VSXtdhglCD2bVuhAcEzG+cLGWGk1TIBIKL6bZunJ5QyOqUhb\nKGFB0yrGZ7WaW5qldwVTKZ0amMPYRDEDiI0VvpHFqSmYm9oqvzaEqW2UkKLD\nB2zh2r9UJcufzEia2aqIKuGi2X1O1+Mihi6cPvOLwZNpe7NITvAofV9XQhyl\nG0Dc\r\n=Vgi7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6ho2xujIvLUmBbxLJaC0N9VUJSvJqiTTfRXtiDJmjyAiBECEMTA358GAboAonyiPX+fxQrVCrSuf5F5kM7yJi8Cw=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.11_1593600914526_0.6085008463243302"}, "_hasShrinkwrap": false}, "3.0.0-beta.4": {"name": "csstype", "version": "3.0.0-beta.4", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.23", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.8", "@types/prettier": "^1.18.3", "@types/request": "^2.48.3", "@types/turndown": "^5.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.0", "fast-glob": "^3.1.0", "flow-bin": "^0.112.0", "jest": "^24.9.0", "jsdom": "^15.2.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e74deb234e870896575ef442a57d252c7677cb07", "mdn-data": "git+https://github.com/mdn/data.git#bee2448507748e522b0e1158e420a647628e1fe4", "prettier": "^1.19.1", "request": "^2.88.0", "ts-node": "^8.5.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.7.2", "yarn": "^1.19.1"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "68ba53f2bfb5ca0836582dbc364d06166d1eb810", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0-beta.4", "_npmVersion": "5.10.0", "_nodeVersion": "10.15.3", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g6BwpYNogM3LqFlfC4263ZM9Ag8LqGabfMEHD6Jn0kk/RDIbOaEp1iAwLwsQ2VOVrIcli3OHALWIfRbc3T3i/Q==", "shasum": "f03dab508a9b47c8d582e653b7d43bb8c42002d4", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0-beta.4.tgz", "fileCount": 5, "unpackedSize": 1133041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/G1ICRA9TVsSAnZWagAAEo4P/iRM74DKaWzxDWOqdj6I\nuwwlVL66pBUBIoy5vyz2cQrwe/BOFqGx7plqfHQWssViQzPW6x2jMsJRzlub\nTSIbhv1ea3lkK7+ozFto9S/HnH+5s+P/2lzaTKDLdkY35+vGYfXYFedbPeti\ngywjDhl4pjMQ2agdUVFqzUzIWbSbgCogx87RZtrGzmfPLbdays3kWiwoRf6M\niU0bhQJD38H+xCSqGvlatXW6nOokNsrjSaqqDOfQ75IxmyluVhi9MtNcXu7e\nlW5JK/LvW+B6O2x/nn0Z6uhRiiorfGs7mKowNGScsziEylTH2gOuYuum0S9S\nw0Kw0TPwqpOyKCQzEMeASsVeocr/aluL9akuuN7tbg62tPzb0yda4AhRUpUD\nCCqWDTz1wcrlr2cqq05psAIXlX1pyZclwZwOu9phKJ1T2KJ0s7eNHkZAS6a2\nFOyWsvMkcwtZOLxZlcG0AgyIKR5Mth8g7czHdL5Rbidy/KjOxWzuUIY1Dfam\nJ06UTR8xWqRdUtrghvrg7Lt2t0in2g58YRKDPPWzVuQIhrc1k3pRuiuqf3E0\nE6LdgTHsMWAvqft3wizctFqozn7nZ/3temjKEPaKXBr67ACQ0QteCtUo9jnu\nPZFXuwB9HetvBVL8gk3uL+Ivc1GRtDrZgHVAHeBu+jWz2LnR6gf5P8V5cLnN\nmPBD\r\n=cJoe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGrzpLKZGfoov1x37NVmF6FGfZqrv677PYASHW+7e0WmAiEAlbl5nEMyv2I6Au27H57OKrQnCRBdCGmC+AGwFt6ExBE="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0-beta.4_1593601351738_0.03250014001311019"}, "_hasShrinkwrap": false}, "2.6.12": {"name": "csstype", "version": "2.6.12", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e6e952bb934ba31a276237453cf892631ba224ee", "mdn-data": "git+https://github.com/mdn/data.git#9524ff7200bc5fce9990e49a80e39ddddf759ae6", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "91ac4420949c99bdbe66fe04a7f81e9fd653deaf", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.12", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DnvwpyMPL7qCwG0JCdm/Kv0z1TYQfDSAJe1K4bb73PqsOTYwix55Tyju1YNePMdNcr4ornwdp9f5FzBebY2mUA==", "shasum": "970d1c66b88be6a3556f4b7e96eea58ffca03831", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.12.tgz", "fileCount": 5, "unpackedSize": 1877389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIcNOCRA9TVsSAnZWagAAwpcQAKNXWg+iqL64ok/9gEy/\niDJuCNLLwrhUfU0THBQz+sOBN9mi+4B38JrosN+9fMs88naRImT/ja74Sawb\n2RDdaomeSOG+lGCSXulg8aIDi3NGjubeZ72mkPFCiTy7QYflA2VR0lnvIMJ3\ngNTB0axAlkaQWpzA4MDB22RghkqVTeWDhOb6+4bOa9+IFZJ8HgliN0SaFTds\n4ULn1BsgDcBrHolVkMNQ7pL3xe6wouJeTUn13D4nCiBfULd4ZIJ9MrKrYfF9\nkxEn2t6BPYswykrHG3cvoO7huLztfJHk27oWqtFvXc1pBBtuq0xlP+lz5Acz\n7EF+EDXwVEfuzREBBIAQ13ii9nEOZHpTE1feI/ZyC5PW+uBDGqGMyYSG2Oso\nx2VCBgFIDuv42UgKTkpAyWN72oaW/vaC2ymBfYZPSv6rMiwfvQmZyG3MaEpp\nnCZh/jr60M57rTu9+RFaYLhWFtE3/+MUFQGeD+31woUt5Y118F+Y9bRXBd5R\ng9QrymKXQWi134ZnhRKcH6/mtIr2tlETSpgizUw/zKfx0UavZ34Y3gy/GaVf\n54zcEnqqckHJBQDmNMCJWMRmeOc+ERVosGWrOEv6Av0LLsIx+zMR0XzxwDQJ\nwFgf6Z8+JzGl25ETPRPXb5Z0tZT9McTGdlz/IhrVtsVD8jeIRRV1dz0FYVKB\nUdsg\r\n=x1hl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2hU1OPyNjstpVfUdOtR7hf1JwATkgOCiXTgPPCFp7rAiAlJNY1Ys4YEkLaRxpeq4+TgKnTri+j8Arn1Hvb2W9ObA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.12_1596048206194_0.6570611966164819"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "csstype", "version": "3.0.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.7", "@types/jsdom": "^16.2.3", "@types/node": "^14.0.27", "@types/prettier": "^2.0.2", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.4.1", "fast-glob": "^3.2.4", "flow-bin": "^0.130.0", "jest": "^26.1.0", "jsdom": "^16.3.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e6e952bb934ba31a276237453cf892631ba224ee", "mdn-data": "git+https://github.com/mdn/data.git#9524ff7200bc5fce9990e49a80e39ddddf759ae6", "prettier": "^2.0.5", "request": "^2.88.2", "ts-node": "^8.10.2", "tslint": "^6.1.2", "tslint-config-prettier": "^1.18.0", "turndown": "^6.0.0", "typescript": "~3.9.7", "yarn": "^1.22.4"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "58819596e76a44f3b0d4dc3283613a15de478f80", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.0", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7PVEkxNUupmIdJflngQ4EHLYIrAiGEeFiR5gcv0QJRLPcPIUY0m353C7i5xwWgZyKytIaqrIMLOOh+OhUs70pw==", "shasum": "b5cdbf2af48449e9266f6dff517f793187ef6103", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.0.tgz", "fileCount": 5, "unpackedSize": 1132738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIcRlCRA9TVsSAnZWagAADfEP/0WBGYt8lNoHmpUaH68S\n6kKsGJx2z5O+iVIME5tulzbiiZ0Va7c+0hWbEt3mB1c5XdkbkpAAmo78WHwq\ntkXu6ciSCWy5baRmC1keqxGQMRD/E/zreg79oEJLkgeRlTPBoE+G7GwjeMjh\n66jVZjfZpHL95zPBJqw91x73eOZhMdweE/JUlwrjysgBmSKcmCWOhr0lzATc\n0wWq7M3BHhOIIY5jV3XYA4q5oXGyE2wFW0nlk9yY+wBKkwAq/5DbUDpzFKb1\n51fR8F834Brtoa8C4w1K97P8x51ZdnygQNAzPkm1EAhE7PQrdz/oCZIKl54J\nRbDorSD63ez3Uf9R11lb83+TQDfLGDaNjfCKJKg2BsXwHY018HX2STOMRkma\nYclOnG1XFzW2rKcOphFlc0GmDYBx12fByOuhG6IAamGVY2tzVtFoCRhyifeI\nVpGw8X+D1GRqG+tY/TtWm8CM894lBHueA8oxhdUNUEBU8Q901APpKVvHKStJ\nN5ZIIaqKo1AoiovRyujVJy0maZ0QHy4d0MS4eHc2QmMrcQRBz3IJ8bw5sGSH\n/U0+Van8+HDnZr4Upja9jWqg+T085y0T/kbZCZpwaN5n5xXT2qfh8x52t89b\n2TWNVzsTopmAPwnNWbmo/V6mV5yu/xDH9gmg8EOOA3oAZy8dLGW39p/8rOLY\ne0uE\r\n=9E+2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzYYHlcEZNCEUr+Ij1htckyKFozxoW07DT9CwoGyJr7wIgeQc8/Jj839om50jp4p0FVEzz385h/8Grwvl03EG8BZg="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.0_1596048484730_0.8915964168945911"}, "_hasShrinkwrap": false}, "2.6.13": {"name": "csstype", "version": "2.6.13", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e6e952bb934ba31a276237453cf892631ba224ee", "mdn-data": "git+https://github.com/mdn/data.git#9524ff7200bc5fce9990e49a80e39ddddf759ae6", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "70d54be303e56a9a7d7ceb55c7603cbf69a14967", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.13", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ul26pfSQTZW8dcOnD2iiJssfXw0gdNVX9IJDH/X3K5DGPfj+fUYe3kB+swUY6BF3oZDxaID3AJt+9/ojSAE05A==", "shasum": "a6893015b90e84dd6e85d0e3b442a1e84f2dbe0f", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.13.tgz", "fileCount": 5, "unpackedSize": 1877389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIdIuCRA9TVsSAnZWagAAhScP/2FxAgTaI9JLrJ9RIKF0\nCL8L81Ibl1eG4gdVWrnEma+s9aUmq2yYtcGq0tygDANY1+O53pCeu8Kd2o5j\nEJWm4Mnw0G2cJ2zV0I+e7F0jy6AvcTVfvMC00oDA9QvVr4z064a/vWuqSoCV\n/2NuAqazWbQJiHFycjNirEADJ0l2F/Nli90lXBMDUpYqZLCH9slj5kO5s0M/\nWUSa9UriIHDhpOrGJPdR/8XzLYEUb7ghpgDZrRVO2Km4jk0+tEirNU0SjEu+\nSxZhcZrh+8EFXs2j2M14c4rXxSfviBGWJ+fWu3htZ7Sa5HEKrn+dZ5YppxMX\ne2txBAsjxsDoPiwkcHk8sOspXmSlLawjNVbnUko9eLaepwPjqRZOjvyA+5Bt\ng3TS8XP8NhUf6wXUSS0w/Wnm9rx84Gu6RDHvRUFnzBBoSeaE5ge2jdnA3ULk\nTtKetOtBWqbFerL1Apzsc5dqT4+ipz/ysFd5jfBukIJZTTkjAyXR75JGiy88\nQwswQTpVOwOfhZdmbxA95tV6gY73TRYEUjeXkjRJgjdmBV6nw8DMYJ2pcAYL\nKIirdyGT/I6uCblrJhyuP9R3AUbP7JpgC54a4HI1XXj1QXDrugv29fxKeAtp\n0lcgrUuB+eHfgxZyPlx8Wuruv2KPyHsnO5AosbPjlLM2wA/+a2XVWGjozirt\nEQpa\r\n=WsgE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpnocef72yUN57LgxOutRIQcsnHH8Ce9vlUuW3RstEYQIhAIq9/0XemunF/DzV0qXmSqkv4DpeH/8ku/Fws0BHki+r"}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.13_1596052012326_0.13996552171727417"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "csstype", "version": "3.0.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.7", "@types/jsdom": "^16.2.3", "@types/node": "^14.0.27", "@types/prettier": "^2.0.2", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.4.1", "fast-glob": "^3.2.4", "flow-bin": "^0.130.0", "jest": "^26.1.0", "jsdom": "^16.3.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e6e952bb934ba31a276237453cf892631ba224ee", "mdn-data": "git+https://github.com/mdn/data.git#9524ff7200bc5fce9990e49a80e39ddddf759ae6", "prettier": "^2.0.5", "request": "^2.88.2", "ts-node": "^8.10.2", "tslint": "^6.1.2", "tslint-config-prettier": "^1.18.0", "turndown": "^6.0.0", "typescript": "~3.9.7", "yarn": "^1.22.4"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "d1ec6ed697f8c335b887bab4e4fc0b014875ea0d", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.1", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+uybtDModq7O4V02fzooARGQfreSnxf5P7t9rs4wfWVY4MQ1E4r8xDhEY4/XidOFDTU1C3GWSMso9JRmpRW4XA==", "shasum": "50a573509b8333e0e071e65d806270c2abaccd32", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.1.tgz", "fileCount": 5, "unpackedSize": 1132738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIdQJCRA9TVsSAnZWagAALHgQAJ1zELvmZJahnWwYn5tc\n1ApyLuMZjSwyNZ+gcoLRaGql3PKYvgdB3pkUUG2TJf0XTpdhN478kNPRjijY\nR7oFKqJPUb2G+rqge6HQ7MafUCBS4O/JMV+GjCsjF5+fo5PR6dcQkXp5M7BK\nXBAo8Jl344IIpV3mM/cTQcfiPk87TJ523+HdpICuH/poHkkcFMH3lgdvMKgR\n+sQWyeFAeTcwrOFFU9xI23MAeuQo83hW3mIBwIKuSPEmb/+ZmrS3ueQvRn8V\nlhXyp67lHZVivs9eRfpRnkEfnzNaq/UDSWondroL5f39oTGm+kmUcDaN3k7S\nt7CH6zVBZ8ew226K4MKQnya+AX/+G0iWr285Xads1rVu9cWwAZ4p0VfBYF2D\nBSp6LdID2sYCA0jbiluQi/i71To5cPdQdZn6G4Q6PzMMeLbg+y7+sj7lMn/p\nnj8TOejOAy4+UPVwTagicmxqRapovTz88wc+LRIPiwN4KibLbtITFqtYVkG+\n9bNxTysw9oWtBjqx8LUPJqV7TNiQgaMk4qLDuYuXwgnEyraba1EUyuWo5blu\nxnpf8tVh7rOFmUXtMp4jPqh8/aDVBZwpx3YUhcNdGDkK2P6Xr1cTilNbE+5M\nHlPUIER/WDSo7vollZFg9+VOYD4dokMp9R00Q5R0nAJ77A2UeOgEf53zRpG2\nOkb3\r\n=OkFh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICmh2sAWy969pVCCnGhxynyFIPj69Boq6Q3ydIzOD35BAiEAzpdMZ3of9c8WT7GcJdNbz70pBHf0UduFEOeYCYw7iCM="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.1_1596052488660_0.6629811505232239"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "csstype", "version": "3.0.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.7", "@types/jsdom": "^16.2.3", "@types/node": "^14.0.27", "@types/prettier": "^2.0.2", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.4.1", "fast-glob": "^3.2.4", "flow-bin": "^0.130.0", "jest": "^26.1.0", "jsdom": "^16.3.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#e6e952bb934ba31a276237453cf892631ba224ee", "mdn-data": "git+https://github.com/mdn/data.git#9524ff7200bc5fce9990e49a80e39ddddf759ae6", "prettier": "^2.0.5", "request": "^2.88.2", "ts-node": "^8.10.2", "tslint": "^6.1.2", "tslint-config-prettier": "^1.18.0", "turndown": "^6.0.0", "typescript": "~3.9.7", "yarn": "^1.22.4"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "fa81ee5bfee1ef3c0148568f453149720d9176dc", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.2", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ofovWglpqoqbfLNOTBNZLSbMuGrblAf1efvvArGKOZMBrIoJeu5UsAipQolkijtyQx5MtAzT/J9IHj/CEY1mJw==", "shasum": "ee5ff8f208c8cd613b389f7b222c9801ca62b3f7", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.2.tgz", "fileCount": 5, "unpackedSize": 1132750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpqQCRA9TVsSAnZWagAAQSUP/0uPW/Yz79HoMM+xrYCw\ntgPqdxNmD5DH35fC7cxXzpbAMz/6ByBVrI5jM1BkR/AgKWyJspFWpuPAnlGI\n1PdEuaUhQriZSjsOjIy4r67vT51oRn5GO99z4R1lipK6p40UV/tZZpbbPWoX\nMalAWQOWc6SHgPDAiG1AveeiLaqZJnJ0GSopUFpcmdp6o1daB0ZBQMsMwSFD\neCh6Qo2Mt+DWE/JoaKTX51nfkwu6LiY/fSNOldoIpBlGBAMTw+IivPwSR4wR\nV/+LYt8wIkP07oAgTDyYv0XQ6CLMzLmQRvE8c6gkqP0qSbFBQBkZBv/2tygA\njn5GAuGvu5Cz10gZczAVYd4MVA4UkrW2bv2YJapY0dZ/SDpdklKbFPuaSKgE\nwk80OV2kFM9SFdrrJzKVKOaG+dY/gOZpIyG7FV9zPSWeKIZo/jod2LpapplW\nTsQ0DW+iDw5WdzwboxTw/KG5CjOKBpY/Yf2ZP+YAdDBcPhLXvVzVEHv+gCri\ninBn91oHxGapOyDt7jq/PLC2i0uTpIlewkZ1UFen/iGndwJGu949FXFQR1lU\nGFyqw8kb9dx9musaco2EY5LptYJT7As5opBkpt2pfA2RUMX1eRQrjA51z+cP\n0MJvdypeAl0dmC6t6Jbl6zV8THzPJccl9oJViXprU7IM3z89YPcCEnJxQs+H\nBQBH\r\n=UoEf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD68ovSTqxffhax2fjuynBnvFRZf25wWnD0vQAnaKfOzgIgJMrVYJRcm0UVemUUYiNZXblbf4fhNU9o8zISyJsybP0="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.2_1596103311582_0.41550136452405373"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "csstype", "version": "3.0.3", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.10", "@types/jsdom": "^16.2.3", "@types/node": "^14.6.0", "@types/prettier": "^2.0.2", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.4.2", "fast-glob": "^3.2.4", "flow-bin": "^0.131.0", "jest": "^26.4.0", "jsdom": "^16.4.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#bce11b3da31a3b3425280730360ee42d7cc55328", "mdn-data": "git+https://github.com/mdn/data.git#3191d4b889364bca969dfcc84e490928d84710ef", "prettier": "^2.0.5", "request": "^2.88.2", "ts-jest": "^26.2.0", "ts-node": "^8.10.2", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "turndown": "^6.0.0", "typescript": "~4.0.1-rc", "yarn": "^1.22.4"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "f29e601571655db7c0fcd0d89abc30e12b498d01", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.3", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jPl+wbWPOWJ7SXsWyqGRk3lGecbar0Cb0OvZF/r/ZU011R4YqiRehgkQ9p4eQfo9DSDLqLL3wHwfxeJiuIsNag==", "shasum": "2b410bbeba38ba9633353aff34b05d9755d065f8", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.3.tgz", "fileCount": 5, "unpackedSize": 1138491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfROJ7CRA9TVsSAnZWagAAk5IP/iOeLV8YpBXLIBDKcvcI\nR8TMCxDWr9XDrgw4TXnufr/qxGhnLaQ7/qhWPRMN51v06XSelp9LCFMLT7J1\nMM/AjZGtVM2+j3xRz3ImHTxp6WBQoBuZZ3YR5EOXXzfsOhHG8zO8mxAv9tiz\nJzvK1vx1QW0dHtj64ibIAqCml7upEVbGPAhQVwBoZvcFCRJUMGPlmIV+a+c3\nRErUYm0LCtABoh/3nGIrhWjG9ofE84V1D2UzExwpFAwmHpoI0zfegV4cMQSl\nircXuvi9s1+ItbvWxyt6C1QGhoAdzj5H7lbo+y/ndHg9UWUD1+gRGF/vPcxx\nNpHJ/Y9WH2BrCQ6tnDpc+8QUSv4/9r7vHOFYKldDb1ngzOU5AjYUxgy4wVSI\n9/c4V7lQX6UcIu3DpwWUAfLH0kuw8EqO86daL7FXTrahemgyLm+fZz5RfXkA\n/tB3f+CyBr1bdBJbL1ah8R01D+BwrRcr7vqe8WuemT8S1D62mgdsCZ4ZH++r\n04ROaZ83BOMKBzT3ZvGFA/Ndv8EW0CUPr6zX1yR39IlsOQVyTxZTYpT2kyy/\nVTpEwQmQYARTr4IvT8AoROW3ELBLf9LEOee+PoEGGUnIMO4rX1a1inus90JW\n2qMueppjkuLYZPAnNESKxm3ViglginEvTiSv1RVAaqmVCM+VtDyNNWntbjGF\nQBa8\r\n=2Cv9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGaH1Ry+UkNtYE2b/IfYRwRxBXYEZ5tpUz7aZgIw32KyAiBTWbm/e7ncQ3CKuPl+bXQyvbL1dYCqrUd1y28IHF6DrA=="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.3_1598349946805_0.7369496333532413"}, "_hasShrinkwrap": false}, "3.0.4": {"name": "csstype", "version": "3.0.4", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.13", "@types/jsdom": "^16.2.4", "@types/node": "^14.6.3", "@types/prettier": "^2.1.0", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.4.2", "fast-glob": "^3.2.4", "flow-bin": "^0.133.0", "jest": "^26.4.2", "jsdom": "^16.4.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#d2401d8ee142ea7e64833e38fd44f1cff9618198", "mdn-data": "git+https://github.com/mdn/data.git#da49d88b8a5bde681ae94ebdd07482020277fe39", "prettier": "^2.1.1", "request": "^2.88.2", "ts-jest": "^26.3.0", "ts-node": "^9.0.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "turndown": "^6.0.0", "typescript": "~4.0.2", "yarn": "^1.22.5"}, "scripts": {"prepublish": "yarn install --cwd __tests__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "09fd5aa4ad3fe35e84e064ce4e6d2d64ba66f71a", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.4", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xc8DUsCLmjvCfoD7LTGE0ou2MIWLx0K9RCZwSHMOdynqRsP4MtUcLeqh1HcQ2dInwDTqn+3CE0/FZh1et+p4jA==", "shasum": "b156d7be03b84ff425c9a0a4b1e5f4da9c5ca888", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.4.tgz", "fileCount": 5, "unpackedSize": 1141316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfko03CRA9TVsSAnZWagAAigYP/iGK67RnXRvqoJ837/hA\nhc2P0pP2fPUMEcoPROUmTm+iLm6j/X+4nkXNLjmWmlEqDvsVLaeLZWcDQJfv\nrRFv6c/Qd2yzUW/dmi70r9UFAWcd4O2xlkMGfBwExyS7ftWA1M+KZql9Gwjv\n3y9NuhnXQ2PyEfNclHUMuBgxxtRpNXyZJoaPzzK390DbefZQtdq/G2v6XaE9\nfTSfWpCEcSWBq5WzO9Ot9ZskWWb1oPOcwNxw3OQAe1QXTjcQ8UNi4a0JH5fR\nHVewM4jvXjKfuG8nB8vjshURSueoLE6rnwIxON4NkEIBnfXbuR5pIpPI+mi3\nL+auW4N1iUxfplF4IIM7mv+q/UxYqvBdUpsEJYpDS26z/Muwr0pn6+jC1fii\nCgFeEcmcr4LtE3EhL8vLfms7b0l10b+2ri7Y1/e3hgZL9CxEd8UDVf2PSNZ0\nsh12IMlKdxTtAH7N2YLMH8cSZXmx3KQ+yIAt6hTL22PZNsrIha79CZt5g11L\np+sLBI+qalomoQwzlXRApc4qzvUEymHYmKBtHgLtQmgGGOm34y4Xd8J/6kZm\nBDo0dUpRjG5FBCIwLHffNyzAUOtErAy5YqaqgMZouxxtQ+OcPzmhkUGPu1W1\nXJSEm7/LSZ1JAUlZts78F8YmQVYsJjqZMJdngZVg6LFqQraLNJnnUU1bDEPs\n57kE\r\n=aZk4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZqIakDzyfOJ2bHTbF+vcUvdqzl/qKY62z57wnysR02AiEAk0YEJol8/qWmGAh2AFTm3jf+BCvrFAhmOVeTpLlCFu0="}]}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.4_1603439926644_0.23813520066593075"}, "_hasShrinkwrap": false}, "3.0.5": {"name": "csstype", "version": "3.0.5", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.15", "@types/jsdom": "^16.2.5", "@types/node": "^14.14.7", "@types/prettier": "^2.1.5", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.4.3", "fast-glob": "^3.2.4", "flow-bin": "^0.137.0", "jest": "^26.6.3", "jsdom": "^16.4.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#37f0d1cb4765494ea388adc0d38c1e5eeb6a60e1", "mdn-data": "git+https://github.com/mdn/data.git#29e9e61ddc8299bffc92545446bbd5d2b35ed1d5", "prettier": "^2.1.2", "request": "^2.88.2", "ts-jest": "^26.4.4", "ts-node": "^9.0.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "turndown": "^7.0.0", "typescript": "~4.0.5", "yarn": "^1.22.10"}, "scripts": {"prepublish": "yarn install --cwd __tests__ && yarn install --cwd __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "5de6cdf5c32f24ee0d8a05f40a44b0860b537aa2", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.5", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uVDi8LpBUKQj6sdxNaTetL6FpeCqTjOvAQuQUa/qAqq8oOd4ivkbhgnqayl0dnPal8Tb/yB1tF+gOvCBiicaiQ==", "shasum": "7fdec6a28a67ae18647c51668a9ff95bb2fa7bb8", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.5.tgz", "fileCount": 5, "unpackedSize": 1151083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrklcCRA9TVsSAnZWagAAXOMP/3IDvS1hp6zjPJuBgDjG\nIJ9wKuVAlAmpB+0jxpRmbOhv3KnZpMHdIS33iHLikJnNIvPNO7565YjmMUTv\nFf9/pSdfdrZZK/Gk3T5T40F77eWU8wHsmzeYFxkPOb3bfGuByXc0QszHY1nJ\nf7aZ8U9h0ty/AZ4OCQiz2D2BvZOyH3PfSP4QgvxHFv8gv30tlVvTxA0BOChd\nkAwJi8xMBEmtLCSVSwSkJs7sIziCHn6aH7EzPLTaooI1tlrDBNDv3me9sRlN\n74QsqkRiv+0Q+VSi4tGO7NltSqyFhIk/YJnaf847zm8OBkzf+NpD1JgxACyI\nGoMdC0lSerFwLo8Hh8iIfxu3yUnoXrJbayUk+Rl5zG4A+LKCgVt4cBSwxHZX\nQtoKYmFN18UcfYReTP+8bO0jLqtb5wbMQiRKgGgKakdF5e1krDMTVqiyAGBh\nxfYZH8bxM+DC967P0ez8mIizCNT1CuQ1TMPGAl6025nUv5hjlP0VlWEVpwHE\n5+9Nor9SwFsr0XSKlGAEmDacoNLi1AN+h36NAoc3K1i0cJxTtOCFPePWzI1H\nW9Q1N7Je1eEpk7wUoxyq6n9e9dvhVPOgv3Xrcge+6J92YrM/qBPYiEbUR0iR\nYVNmlYDiM/aLQrCwuD+aNx5wqU8vzE5HVY1Bz2dXimR/XVF0u7KmMzxtCPRe\nGQBL\r\n=ogJI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDM3OoA25Olrnchw+bos4kggiSFVxJ+1OK4BZK5Kbo2zwIgK0Iw9M4bi8Nr5YT5Ae5TZymNeN9Onn/piamWr81apuI="}]}, "directories": {}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.5_1605257563617_0.2934976338337425"}, "_hasShrinkwrap": false}, "2.6.14": {"name": "csstype", "version": "2.6.14", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#37f0d1cb4765494ea388adc0d38c1e5eeb6a60e1", "mdn-data": "git+https://github.com/mdn/data.git#29e9e61ddc8299bffc92545446bbd5d2b35ed1d5", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "4efc82cfc0b75599dfa3087ed47f4b254b0d2fdd", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.14", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2mSc+VEpGPblzAxyeR+vZhJKgYg0Og0nnRi7pmRXFYYxSfnOnW8A5wwQb4n4cE2nIOzqKOAzLCaEX6aBmNEv8A==", "shasum": "004822a4050345b55ad4dcc00be1d9cf2f4296de", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.14.tgz", "fileCount": 5, "unpackedSize": 1902647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrkpCCRA9TVsSAnZWagAANdoP/3KJn08TrXWYWWRvXtb0\nzinQzlj8wY9KwLyE6DPbGBDHiI/JUS/pu5uShgogdUBvXsMiU6bvONAr0W2F\nYVrINHPmCyngfKZKStSAL02N5FirCbbgumP+MBPNVnmAnzZZkXYpAQiUG8CA\neoXoTuVAizEZeuMvX1xaVKI3rvTTQfyensK5nngMFKHxXqndMAT0jhpOgWAK\n91aruZjchKHJg+lLxdUzvodUVTWq7pVy2lhQwz3vzqZrCxGj+78NYwVHbgGd\nUwS51OU+zExC93mnEdodqfCuVU+2lrzNQIFplVIPK2KsQASObL8k5fXKZ1p4\nYNFViz7NCgf+zjDaR/k0qev5Duva8cF9I5SE197GorN0l4vgz2dth1iAGl/d\nA8fCJyKHhFcf7Ga35L83TEVa1ec5RcNGbz1HNetw7WxUo5h6mLg0wyGOEavy\n4Ef3G+tcv4yemkSjLlbWPLWEe3gAgz+VJYEFy8Vj6XpmDd1rXtgfD9t/bLtK\nMtLJ5DA9iSmCruuRk1m4iNVxfnGhMe7Bjqvkben/okKfYrqvAtmU9fU7SOoM\nfFMcskjNKTp/hsae8xsxM8WOmLITijXIrKMT4KOrtxl2yd1+3UKFVRPznvLz\ns+L/HNPfRMj9ZoIo9lhPP33lkJ4kf8EvWQg63X8mZhsBrRF9GLV8WdCWTzuo\nYZeJ\r\n=0Cin\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBJaogyHFjsRwSqj50/QolGQuT+EHPuytVWlurP2Rzw3AiAnTnS2b9PX2FLDJk+fixQYhXP8Exqb01Bhj1jMQ7iDYQ=="}]}, "directories": {}, "maintainers": [{"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.14_1605257794043_0.8989366893708906"}, "_hasShrinkwrap": false}, "3.0.6": {"name": "csstype", "version": "3.0.6", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.20", "@types/jsdom": "^16.2.5", "@types/node": "^14.14.20", "@types/prettier": "^2.1.6", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.5.0", "fast-glob": "^3.2.4", "flow-bin": "^0.142.0", "jest": "^26.6.3", "jsdom": "^16.4.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#50a17da8e7568cdd6f78eace6f9250c1a7211810", "mdn-data": "git+https://github.com/mdn/data.git#75f9f8f97d0ed35665885478828276b824f9baa0", "prettier": "^2.2.1", "request": "^2.88.2", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "turndown": "^7.0.0", "typescript": "~4.1.3", "yarn": "^1.22.10"}, "scripts": {"prepublish": "yarn install --cwd __tests__ && yarn install --cwd __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "00302aa453fc130a4c660003221f675286f29e43", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.6", "_npmVersion": "5.10.0", "_nodeVersion": "12.18.2", "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+ZAmfyWMT7TiIlzdqJgjMb7S4f1beorDbWbsocyK4RaiqA5RTX3K14bnBWmmA9QEM0gRdsjyyrEmcyga8Zsxmw==", "shasum": "865d0b5833d7d8d40f4e5b8a6d76aea3de4725ef", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.6.tgz", "fileCount": 5, "unpackedSize": 1152364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+FAMCRA9TVsSAnZWagAAKPIP/2zYsQhAOOFEk3DR2/Hp\n5D2KwZW8G5emWcqnblgYI/9aXHAKhONWnDQhGVzjfbqt30jkDb5SI9ggPg0R\nSX+LWZxtO7hyquhFqc2Ua57NACxsyCursLETHjCO0ceHxK5/iG9EF01j72t0\nab+M5o8/1LZQ+qoI+8DM7nkBgw82gAi4RMIJJKiiRRMrkbGX0GKfW7Wf8JRY\nzQfLuFW8OGoKULmhO61fu77EIrXmuVojMrxtwmEH40XJRlh4juuliJCL0gaS\nc4GjdW+heG46GQ2Lf5z3TDBc5wyd+0FKczNcr0VUiomDVLvcHThY/1JHLk7q\nf+3G905YFOIKIh6M3/xcU12MIIr+C6eHjZt2yiJU25Pvc6rPDyyl7bfOTb3g\n6ufGQ3gCPjxfGy8pzUByEHsdv2GrhjO0jPO2j6nH7+FwMzjJFX6mY1FW2zLZ\nmBO8SdT+VIVFmtItrMSxniLLnTP+v6lBALfw+11z3XwRisl71mP6WWrAw/kk\nrhl5BTdXzLwyLKgOcY/v2nwdTQUhZAos7ROdQrTLjwNHL7aunaLrwXGv+kzc\nUzWlcVOmX7GrokWiR89OBSiLyTX7yjMzsbE6efHBci200sBX6INBBP0OQc8Y\nV0OrUgeGCMxsHiQtfojqCnrTQdqR5XyWP5j/IoXJbEHog3MqNDuYgtx9EUVB\nPE93\r\n=rji8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCttdvxKH2U6pOzlxxK4ruoUCzvagrzRh6he5PKOijh+gIgPn1p/ryB828KjjfqCPqRQ2BtfPZ5vllJaWpJ9OtL3nA="}]}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.6_1610108940456_0.7392064657288013"}, "_hasShrinkwrap": false}, "2.6.15": {"name": "csstype", "version": "2.6.15", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#49c5367a7c558e59c9e14a3250ba6584786d343e", "mdn-data": "git+https://github.com/mdn/data.git#f95b275532ae3535fd3552cb47dbeac9e702fb24", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "61c150b2fd176ab9008b3904b5778b426aadaeb1", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.15", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-FNeiVKudquehtR3t9TRRnsHL+lJhuHF5Zn9dt01jpojlurLEPDhhEtUkWmAUJ7/fOLaLG4dCDEnUsR0N1rZSsg==", "shasum": "655901663db1d652f10cb57ac6af5a05972aea1f", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.15.tgz", "fileCount": 5, "unpackedSize": 1928329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgK42jCRA9TVsSAnZWagAA8LUP/jVXmHvDY8g+/GJOaVia\nmP4nmHkrIaWJkB5TSodp/cRljNpo6o0oCh41EpiQliVzEZDEYIqtLTJRUieX\nVVMRLtWlw7WPhBh+aS+J2jCUGeRxBnm1DdOvmNXGNxRRr+cVLdcI12fQMQOs\n3yxnXI97pZqOAxM+YPs/OYMMdFosb1KnmHnMAvTl2a5bW9Wzwq6rM7xYkUSf\nC39iXkqQefpIMa1G4ktVO+hHhrWZHQKhyaAdH2QgZ3mTLzw1ypj1JZJkATt4\nBSQQrcCfCXlPv70NTRKnOT/ieKiTPi4U272LcSuxfMQZWniLVXDtn8p+vo6y\nvsXEBGjBHkch9h2CJiaKYRA07yCP8u2xACbjtGpZHwrfm1Sq1MGBmpAeqcgp\niHy6P6ltM6iYB6HuOJVEQ324gGIW7wdymXyng9XmOEjJhBn4XKbbpC0iqZsp\nkklT7mRj40YLhtHGA1ILxSEtKAEf1m+FyuNiVi8mVi8Qbi/1DfAc/5MFv24S\nayorNXX5e55TZWBjD51risEKRtFXs7jV4G/t2s7wLaCw0QHany3q1zgonkey\nAaU9Iw29HSe7+gJuRhMWC7m9CO3tzBOw4BkUoK1V+uU+wQEdM6GG+sbO+Xnh\nbTovHmn6Rp9jOLCrInAx4jEz1Yhg/B9RIg/3wd2AUf5ReXJvqqzerm0FJdJR\n21NL\r\n=+yx8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYx8On1Ufo5+hkw6pwvGwrJhmUqzY21HFNjNlrh87UGwIhAPLEuR2vaYB6++K6AR6Gte2BieaXQ7SEWFXMseJiXw77"}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.15_1613467042941_0.08727765622964778"}, "_hasShrinkwrap": false}, "2.6.16": {"name": "csstype", "version": "2.6.16", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#f58673c23654744b36c3cae47a115aa474965566", "mdn-data": "git+https://github.com/mdn/data.git#bd41d1aaae1eae7f86fe6a5e4c708a51f021f0de", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "434f49960c6ce545bdb7fef0f249b43c580a9646", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.16", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-61FBWoDHp/gRtsoDkq/B1nWrCUG/ok1E3tUrcNbZjsE9Cxd9yzUirjS3+nAATB8U4cTtaQmAHbNndoFz5L6C9Q==", "shasum": "544d69f547013b85a40d15bff75db38f34fe9c39", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.16.tgz", "fileCount": 5, "unpackedSize": 1930635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNhz4CRA9TVsSAnZWagAA3U0P/jgkmbcqN5U/tmyemK2j\nZphXj7QjAHsJlqT1wykzx6oSwVTZt8Y1XKVKLsIAoaw/+1dM/enUZM+Tpgr4\noINxDbDz/pwWTGlq9ipCVjrOk1gaLmA8ZS/F86cmIl8eDUAP3f2AdwXNbR1g\n+Xf56tVutyYJoFALbi/apZjZBO38pecDbTvgtUUCIWka6cpF9U7VTQ/qrJAQ\nQPDxJHqm33dmry+KgQYcUrZaILkS8W/uabuLyFnKLqpZUbrUW7wIC2E37Hl6\nYyhnnZlX7EUDN2Dbep8g0Jvgx5sMw7DIcfkzZjJjYJ8pRfCHiLjlvY4KRVmz\nZuuIeU8nDUxlNvYuyiIL7snieUtDUZQijcd5548JmINtLcbYpROjjL1SauLh\nm13WZo9Xqie/gZH93WkhuvxKXV7WnHv0VO005HmGb3w3tAu2JrTP9S+hLDd2\n2bD50UOxg4gLxWSyCPO6JIdFVFboQFOnmlbrhetcToAAwvDhn39fh8387A4C\n9XdPTw1sqol5DBb9JNanL/OFn87KZaAi5jmrA12toj/K0uw93H5wKtBC+UYO\nCTCfWMu1VCy7iqk8wosRGPgHEc5KN2nFG1q5xY050PjYJvsbzjKwTTPa6wdg\nP8css7qIEK9DEEZV00RVzqWA/WTODkuFkElnhUltsvU5NNl+NoHcaes8igZX\nqwpY\r\n=Sn1V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPKGy+NLTHqh0tvm01WTbu5zryP7pnMpXwpsfoLnghXwIgDS2Q8l56oyzumlp87FWSzGnBFVu4NdqR2WnVtg0oIrs="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.16_1614159095622_0.741988605187976"}, "_hasShrinkwrap": false}, "3.0.7": {"name": "csstype", "version": "3.0.7", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.20", "@types/jsdom": "^16.2.6", "@types/node": "^14.14.31", "@types/prettier": "^2.2.1", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.5.1", "fast-glob": "^3.2.5", "flow-bin": "^0.145.0", "jest": "^26.6.3", "jsdom": "^16.4.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#f58673c23654744b36c3cae47a115aa474965566", "mdn-data": "git+https://github.com/mdn/data.git#bd41d1aaae1eae7f86fe6a5e4c708a51f021f0de", "prettier": "^2.2.1", "request": "^2.88.2", "ts-jest": "^26.5.2", "ts-node": "^9.1.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "turndown": "^7.0.0", "typescript": "~4.2.2", "yarn": "^1.22.10"}, "scripts": {"prepublish": "yarn install --cwd __tests__ && yarn install --cwd __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "91c373246d09dadb75b67a885ecb0a7e912d21be", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.7", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-KxnUB0ZMlnUWCsx2Z8MUsr6qV6ja1w9ArPErJaJaF8a5SOWoHLIszeCTKGRGRgtLgYrs1E8CHkNSP1VZTTPc9g==", "shasum": "2a5fb75e1015e84dd15692f71e89a1450290950b", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.7.tgz", "fileCount": 5, "unpackedSize": 1166891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNh8cCRA9TVsSAnZWagAArCEP/3aMlB+fLdWLbPhpt1Yz\neaA0jovE2rSzS6KnNr2x8PO0pEnIiILi/3cdWrDKEd8fQv1mWlqFAnNawiyC\ndboOxW3w6tU3fHIoldZ16vsg0mNObiO8ozb1EjLh04toZmURnljDIGTfDex9\ni8ImsX9jT4gFL7QY3WhBULnCTGIMBqcbI3ZI35sYU9Hrb5inylGbtMyNeRJk\nG47QbiXiq3R7+2yKqQs9Jpc5bMpBYys5LImtbQmwVNfehUZCQfIeGo2MGgb4\nV9u9KbQHJs/Eot1g6+Jvp4YGSsol0R3LMMNQjVpVOctvtjodnOxQbwWJoxz3\n/Bbocyivntvm47AGL6X8UpX1bYYwFIf3iUQaxgJCoKJy2VbTJ96nb+jLaH6a\nQ/Y88Nrh4jnoKOKJlvStxL10jb1M/TaccnSVF2rCQ2oD0dhTVTomnS2TbKGW\nGKLgiNN2YpifwJ1F5uWbG5/pmyEIsHAF4k2ESfmK5Ai/SS+mx6QsRNOp2OU4\nGXD3doanWmQQNLnYd/FfOd9nXCe1GcE3iPMRlCCu+zpLz7DaQlv6vkP6COUV\nF2Imc0hwz4ATV1AJiki9ZYFB/UbMXNvrMg2dYZ9PiNwPHnACxRQ9OWeBk9V8\nlE6479pnJ+1LGk123EXXGd8noAlgUyqYfCaG+C0+eDXKmbA2cm/3gFNDCYFg\nWFlr\r\n=Di31\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBQTFsxqiFhJJtWvK1wm244N/yNpo559Oho4sW7/guXaAiEAg3/Cad0mcJ9yH/Vtq0PD+6ptogSKy17vcIpfynqsaB4="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.7_1614159643953_0.7225185410677935"}, "_hasShrinkwrap": false}, "3.0.8": {"name": "csstype", "version": "3.0.8", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^26.0.20", "@types/jsdom": "^16.2.6", "@types/node": "^14.14.31", "@types/prettier": "^2.2.1", "@types/request": "^2.48.5", "@types/turndown": "^5.0.0", "chalk": "^4.1.0", "chokidar": "^3.5.1", "fast-glob": "^3.2.5", "flow-bin": "^0.145.0", "jest": "^26.6.3", "jsdom": "^16.4.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#60214baa97657c798dd7eac44b7bc73af4968033", "mdn-data": "git+https://github.com/mdn/data.git#7f622300bb7e285a2cbce7db6f8ecd8f964a18eb", "prettier": "^2.2.1", "request": "^2.88.2", "ts-jest": "^26.5.2", "ts-node": "^9.1.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "turndown": "^7.0.0", "typescript": "~4.2.2", "yarn": "^1.22.10"}, "scripts": {"prepublish": "yarn install --cwd __tests__ && yarn install --cwd __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "4af33d09652453c494131b01daf6ae3ada0f5732", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.8", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-jXKhWqXPmlUeoQnF/EhTtTl4C9SnrxSH/jZUih3jmO6lBKr99rP3/+FmrMj4EFpOXzMtXHAZkd3x0E6h6Fgflw==", "shasum": "d2266a792729fb227cd216fb572f43728e1ad340", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.8.tgz", "fileCount": 5, "unpackedSize": 1168125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfTKuCRA9TVsSAnZWagAAWdQP/3EcyOAYuJIuARmDLMMP\ni3e97nsxiew8LSDxo9FUZVpGf2q5pkdMP/P60DVXn7sUPmKSjTNBs4qnxZdx\nBYubvISB3ky/NRgY5UHr0A4Bk17Woq9T0JM60oeKIUzguLARfzjFT9Q7jWGL\nLX//5NxA9hK5KOKlc9764ewYrVwgAfMCYGmUSXhVB3fnq4JWRW/fSQLxEkTP\n9AqT+VS2iNQGsrjgH1lelkPA7pRwwtmDwfaaMAvDV1LeKjXl+f/YZzjptKvw\ns03jbJLDgN3xF5rqGvThzLmOL3jdKXPqaMNQbdMFWydcbxE0dZuj79eH0HSs\nMr/h6W+/zKl3pxKtLP1jdO9hKvgfEFMByIwce6S0kJ6P1iVRh8XgQChuiQGx\n/VD4duS00RwaYv/n546JvNjL0AekNhC4GsIafgEaRiguk8cwhCRrKBMOt0mg\nE2WMBt/2Hviz+fYzxX99+6l2qq/beDvGBsaD+hBzsnOgIdrHXRFgZ7HUk0zF\n8OrZw98GAvAbJW4gYTJz2nMPeKuK2R5YQtNk6KW5KOYJCfuRbqIrf2QTgfKg\nzkaYlMLFM9nhLe9oDFhjIeKc8q4rX2iVj6hxwEG0imy1X5ClXFjWjHlZkOid\ncjRjIb1b4Aq4uL1NK1NWCpZpiXYZaY4Ba2Y6qydF0flQW4BUyE9F+KmXWcuw\nOJLH\r\n=vVBi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsEExLfsjfx5ALsM0sarcAKRG3LtGQwsTOoNJBFkp87AiAKY547Z+6xmfGH57L4wV60XmKfuCM1wDKqkpBlO7lGoQ=="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.8_1618817709541_0.9875773370999277"}, "_hasShrinkwrap": false}, "2.6.17": {"name": "csstype", "version": "2.6.17", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#60214baa97657c798dd7eac44b7bc73af4968033", "mdn-data": "git+https://github.com/mdn/data.git#7f622300bb7e285a2cbce7db6f8ecd8f964a18eb", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "8eeee0d557e5c1df6e1b83fcfbc53fee52ca4f68", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.17", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-u1wmTI1jJGzCJzWndZo8mk4wnPTZd1eOIYTYvuEyOQGfmDl3TrabCCfKnOC86FZwW/9djqTl933UF/cS425i9A==", "shasum": "4cf30eb87e1d1a005d8b6510f95292413f6a1c0e", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.17.tgz", "fileCount": 5, "unpackedSize": 1932119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfTVKCRA9TVsSAnZWagAABOQP/iUJNvj80kcUeFdWoQds\nRud3/MuHrK2vknL02eKBQHAZH7QHV3zrhmRORNluwlmXJjOx4ULlgW9tjHDM\n92MgKNXLH1AAZRWLdYZCjK2crTMaF4AQUAh6OY80e7aZONQEGcH9POvPVGJL\nDpVxOoeUy/bvXljUc9eL53KaiNLGu695eb9lN1S0IUqIf+qxFl9B4ig4HUqe\nYCi+cFmQpMBk+sssRdfQ9Ll1MyAo22CYaLrUgOqR6skN5ihoxp83Ldas6gjd\nIyIl/q/e8/YyauKB5fhCbLaJGZp3TvOlrEpCwJnrn/1zF0QbcC0LpQVde8Fg\nlsC4+3OYWC6csA07ijncjzQfyXeUWZy8zCOV8crYInujjdbhIb0Nln+GrNaA\nlz+5G/96Fk4oP4KLh7jT7mC8HR8RJ+fDAxd+HS0c4dmadhHvETof8zamM7QT\niV6nkzgIxEYs5nTR/zEeWwoXvhvwW3lZhU7GS2DYSEt4VNZ4X7wT+ZRNswqp\nX3auBqG8lSlQpZ0PA/vUqDcmjLz3OO/nbmiZr1bQpjboY18XbowQwGdRir3H\nIxKu6b/Z6PO47xDs/+4Cz9ZS+E0X88bRIJNzN4KLsxFPAMI8F12flnR9EPlU\n5CeLurjHrCL4CZc61PwLiEHfUHShGe1NV3xB7gBTDxi9vmXodaUZvbioJWpt\nwH4a\r\n=niyj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5UJmpTeFohDcxYOaH6N7QrVtScKkI2O1SS7UaWWR/YwIgDerafxdMqaOhbxtJUQPdyzAr9lgk+HqTkx8DTU3Ay90="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.17_1618818377775_0.780896034453332"}, "_hasShrinkwrap": false}, "2.6.18": {"name": "csstype", "version": "2.6.18", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#9660656bb7acc7eee075d2b1ee589cbc2d8da1d0", "mdn-data": "git+https://github.com/mdn/data.git#46942543a1ca2160b85a19e55dfcb33a964daecc", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `yarn build` Generates typings and type checks them\n- `yarn watch` Runs build on each save\n- `yarn test` Runs the tests\n- `yarn lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "9a29140de3773f7451eb6e6847ff6e947543cca9", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.18", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-RSU6Hyeg14am3Ah4VZEmeX8H7kLwEEirXe6aU2IPfKNvhXwTflK5HQRDNI0ypQXoqmm+QPyG2IaPuQE5zMwSIQ==", "shasum": "980a8b53085f34af313410af064f2bd241784218", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.18.tgz", "fileCount": 5, "unpackedSize": 1938207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP1SNCRA9TVsSAnZWagAAAicP/2oAzVIu5VrCHtKue4Zt\nQZP8xpxBFb6aGQR/OXisyH36jCN6ZDrr21YDzBgwoabFL29XVxC2ZvovYvup\nYaW9iZeABDBeYR/h+PjpC90RuD/YilivhkPcHUooNP99qVGJPzwAEOqzQTsA\nGx+QHn3AVlac2bf9q8WLJJNFuBCjhHgR9vcga+UOMY7UrPZ3atl7LW+II6XL\nYXGaTVOaCcvPLFpVrYEvV5o6F2JwIS+UpN2kDl8rOdsTsNRc72DxUg8TlwWk\n4/jRZP/O1IDoZ3qrgVjNEnMEXAJHYYx/h3sLbgmKMW44H9J9IAve9ePoanMx\nu+xqzuB/Ofatitxr5tdN2E6DZEojCmREz0FZC0EGmdu41YuOhXusRGZisCOG\n04qToeqs2rtuBRgSw6IDuEIthBNNmmHGwQM7YSAuX8K1yhThFKXa1HOstPaf\nYap5tZj0du3nrNHzBiFcgtEbC3lYVS2eGWm6BH6lvewvUUpUqzD06Ll+yRvt\nXYJUAsM/1CfyG81xQGarHYyoXUwCU+0cHQBdvh/vpIZyf5x0c/cFdMEakNUr\nVsDa6gas8bRdROfd0kl5RPO3BMkHrKN7Sip23aiVgyVLL/Y4P1pZhgjtyMWM\nwwJMlMpG2ILjGL7vl1x31FAfG/F4Sc8UL4rYLkL6NK5L3rl16spQCmb+4pFe\nZrf/\r\n=mknU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1eM8kdSU0yQOAi6+dLt1Qr5gWQiRbbBemq2/k6VfEcAiEA6OL2xHNRHnfDnrJV27SO7Hvp8jsfTRI/RTUFhyTNT5I="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.18_1631540365456_0.32990372456363404"}, "_hasShrinkwrap": false}, "3.0.9": {"name": "csstype", "version": "3.0.9", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^27.0.1", "@types/jsdom": "^16.2.13", "@types/node": "^16.9.1", "@types/prettier": "^2.3.2", "@types/request": "^2.48.7", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^4.31.0", "@typescript-eslint/parser": "^4.31.0", "chalk": "^4.1.2", "chokidar": "^3.5.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.7", "flow-bin": "^0.159.0", "jest": "^27.2.0", "jsdom": "^17.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#9660656bb7acc7eee075d2b1ee589cbc2d8da1d0", "mdn-data": "git+https://github.com/mdn/data.git#46942543a1ca2160b85a19e55dfcb33a964daecc", "prettier": "^2.4.0", "request": "^2.88.2", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "turndown": "^7.1.1", "typescript": "~4.4.3"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "6efe9255f529786fbe37405fc608fb885eef67e1", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.9", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-rpw6JPxK6Rfg1zLOYCSwle2GFOOsnjmDYDaBwEcwoOg4qlsIVCN789VkBZDJAGi4T07gI4YSutR43t9Zz4Lzuw==", "shasum": "6410af31b26bd0520933d02cbc64fce9ce3fbf0b", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.9.tgz", "fileCount": 5, "unpackedSize": 1169530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP1VUCRA9TVsSAnZWagAABYAP/A7j0KYB7vLZLfzMPw+1\nYqtB81bY2kTo0gNorgQ9cQ0PLHKQYthfRGUnUW8r6xcBvXQzb0KmKvYxRVOv\nsizg/xSUySzRdl1IEpyQTOZDe3v1VrBBtBbKbVlqcP8XctvCvNP0Xu/zRNLo\n4E1npB8Az3MVHVMXHmTY5t7sXTE5KpXQSwmb2awNazvTd2GVvvcory0eGWKF\ngbVtsRNP1EFKWIMDED4AggC1CZZfIxo0UhowLT+Cewc1AEkOX/x+olwPTy+H\nvm8PPhJm7PSLzyth+828F3b1qsTgyKJcjM6cYwMXfoY/FDT2uA+B1Ks+71XN\nlRV0+H4MZ7iaTCVVbgLLXjT1kV58HZcsIYc0urjJ2TkB4pvumNpdIHg25FCZ\nDroWx6LKOGyFShI4EXstwcF+HG6IfF8JjbEXCYAZRkW9Adu4OJ27Ye0Po6Lm\nbmJeqaLgDyeGEIzeVQ9Erre1vSCI0euWT9YSHPKBVGC5ny70bEZhmbZJpCFt\nS6+wbk6Z8Hi/jcCx3QUId28Ku88munoXr2Xr0PPbndXOI9opDo7U1aFOg+WE\ntWdRkldCV41HGiSgq3dWuv+qbr5g9JxWEPeRA0GwcI+rr3K/rcYi08hM56Y2\nar1PGvRgI3+NHqe7lLHke+8VWxzNwND6p07zlqS/HUMo7i50CE7rx7iS+NkD\nnIO5\r\n=oXtJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2wwCNQttOBlGDGgvKQqklH04qioxKwbaqnsZdTi7SwQIgJ17bdybDxqWr5wOc3yio3zoNqGDTCmmtU2zY71x4zK0="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.9_1631540563941_0.7646320618296603"}, "_hasShrinkwrap": false}, "3.0.10": {"name": "csstype", "version": "3.0.10", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^1.0.6", "@types/jest": "^27.0.1", "@types/jsdom": "^16.2.13", "@types/node": "^16.9.1", "@types/prettier": "^2.3.2", "@types/request": "^2.48.7", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^4.31.0", "@typescript-eslint/parser": "^4.31.0", "chalk": "^4.1.2", "chokidar": "^3.5.2", "eslint": "^7.32.0", "css-tree": "^1.1.3", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.7", "flow-bin": "^0.159.0", "jest": "^27.2.0", "jsdom": "^17.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#a9a17ff717b73cb9bb7072357a080509b73e22bb", "mdn-data": "git+https://github.com/mdn/data.git#ff55c39c1da3f1519e3a8f890a7cf6e6339a4b87", "prettier": "^2.4.0", "request": "^2.88.2", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "turndown": "^7.1.1", "typescript": "~4.4.3"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "15aa9947fc3d2336dc3eba7ed5e61d09909dd37c", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.10", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.5", "dist": {"integrity": "sha512-2u44ZG2OcNUO9HDp/Jl8C07x6pU/eTR3ncV91SiK3dhG9TWvRVsCoJw14Ckx5DgWkzGA3waZWO3d7pgqpUI/XA==", "shasum": "2ad3a7bed70f35b965707c092e5f30b327c290e5", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.10.tgz", "fileCount": 5, "unpackedSize": 1191670, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDps1iOK4Zvu5FgLq21EJMcM2YUIPPtgGuY3HZbpaOz5AiBo25fY0/fFLZjMK7pObj8/3+3+AEDaFudtK04lk7VH+g=="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.10_1636974372049_0.9269587373251658"}, "_hasShrinkwrap": false}, "2.6.19": {"name": "csstype", "version": "2.6.19", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#18d40bf8f06585dd7c0cfc0a1512d1d9c4a91c04", "mdn-data": "git+https://github.com/mdn/data.git#ff55c39c1da3f1519e3a8f890a7cf6e6339a4b87", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "bd0d311b891caf93f42aff75d20c6688cfd63665", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.19", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.5", "dist": {"integrity": "sha512-ZVxXaNy28/k3kJg0Fou5MiYpp88j7H9hLZp8PDC3jV0WFjfH5E9xHb56L0W59cPbKbcHXeP4qyT8PrHp8t6LcQ==", "shasum": "feeb5aae89020bb389e1f63669a5ed490e391caa", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.19.tgz", "fileCount": 5, "unpackedSize": 1940006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlg83CRA9TVsSAnZWagAAlIkP/3aGUbWyikxSHCOe5Kws\nEehSbvbnZnNWhLyE0z7Ta9/ql57RhgZgQsej5DQzoexdJ6ibwlvlRWt9e2Tp\nC10jwzdL1CAIHCqAgYsNh+YiTE0ovVC5QQr5fpzigSRIHRs2RnEhXszHWrnf\nj6QjD4niFuh/2uqyjkirAjtGEX47tPmjI/MhwlVdaR7XGriCQa4oE7Mhfian\nTUQ1l5EUN1tdH6XNrtfE8XqKVp/w0gQWR6MdLJgxKyjd0JF57/zzzZqQZhve\nDw57Su4+NLhIC2humo62eWzeo2G3Z8p1ccuBQ/bOxqMVjcKE51YKqTpcpfGk\nAIRXnN/BRTmkcPmNHZJsUYMs7nTAnP9gydTSfmUmdbJ6jXpqpW7zyD00L9Fj\n3aXQkrdT8RALCh4mPWa48Lx6cr1plaGuj8991OdLQCs5wXsefkQbYx+msKJk\neFM8iw/2ggxES8RnJXbIjpG59fpgL7ZnBmzBeZq908iFer9YjS3OYnI+Cuqk\neje1AeX+kXESNnFRrmbVDDarDanjLDveBZBjaP2fgDCEVx1v7LMKfPGdH6sP\n6L4UuKLDpaJYFp9xYCIglV/kNJPWLtRVUdknM8GT0ghS8Zwp8zIcNLtfw6+9\nMVpbzSXlTSuFUnRN8jJzQz7gqaoF/Q25toj//Tl6AgxFdQ/XSPo/UuyJGPl2\n8mSr\r\n=9esb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqX4im3yTi4NBIfXvenkyoF9wxzPqyg3Fx4Pj8ODmq/AIgVQX+zT4V3Owj1JQ9dUcu7SHF0NsOhu7jtOxgGvs2FPY="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.19_1637224247575_0.7187203919630194"}, "_hasShrinkwrap": false}, "2.6.20": {"name": "csstype", "version": "2.6.20", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#92bf4cb8c5594edb314995e53a9cde79d5fb1b1e", "mdn-data": "git+https://github.com/mdn/data.git#cad93443ab28ee08b1ba490b13cf45eafd1f008d", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck", "rebase-build": "git rebase --exec \"yarn --ignore-scripts && yarn build && git commit -a --amend --no-verify --no-edit --allow-empty\""}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\r\n\r\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\r\n\r\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\r\n\r\n```ts\r\nimport * as CSS from 'csstype';\r\n\r\nconst style: CSS.Properties = {\r\n  colour: 'white', // Type error on property\r\n  textAlign: 'middle', // Type error on value\r\n};\r\n```\r\n\r\n## Getting started\r\n\r\n```sh\r\n$ npm install csstype\r\n$ # or\r\n$ yarn add csstype\r\n```\r\n\r\n## Table of content\r\n\r\n- [Style types](#style-types)\r\n- [At-rule types](#at-rule-types)\r\n- [Pseudo types](#pseudo-types)\r\n- [Usage](#usage)\r\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\r\n- [Version 2.0](#version-20)\r\n- [Contributing](#contributing)\r\n  - [Commands](#commands)\r\n\r\n## Style types\r\n\r\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\r\n\r\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\r\n\r\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\r\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\r\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\r\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\r\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\r\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\r\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\r\n\r\nCategories:\r\n\r\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\r\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\r\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\r\n- **`Obsolete`** - Removed or deprecated properties\r\n- **`Svg`** - SVG-specific properties\r\n\r\nVariations:\r\n\r\n- **Default** - JavaScript (camel) cased property names\r\n- **`Hyphen`** - CSS (kebab) cased property names\r\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\r\n\r\n## At-rule types\r\n\r\nAt-rule interfaces with descriptors.\r\n\r\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\r\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\r\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\r\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\r\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\r\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\r\n\r\n## Pseudo types\r\n\r\nString literals of pseudo classes and pseudo elements\r\n\r\n- `Pseudos`\r\n\r\n  Extends:\r\n\r\n  - `AdvancedPseudos`\r\n\r\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\r\n\r\n  - `SimplePseudos`\r\n\r\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\r\n\r\n## Usage\r\n\r\nLength defaults to `string | 0`. But it's possible to override it using generics.\r\n\r\n```ts\r\nimport * as CSS from 'csstype';\r\n\r\nconst style: CSS.Properties<string | number> = {\r\n  padding: 10,\r\n  margin: '1rem',\r\n};\r\n```\r\n\r\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\r\n\r\n```ts\r\nimport * as CSS from 'csstype';\r\n\r\nconst style: CSS.PropertiesFallback = {\r\n  display: ['-webkit-flex', 'flex'],\r\n  color: 'white',\r\n};\r\n```\r\n\r\nThere's even string literals for pseudo selectors and elements.\r\n\r\n```ts\r\nimport * as CSS from 'csstype';\r\n\r\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\r\n  ':hover': {\r\n    display: 'flex',\r\n  },\r\n};\r\n```\r\n\r\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\r\n\r\n```ts\r\nimport * as CSS from 'csstype';\r\n\r\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\r\n\r\nconst style: Style = {\r\n  'flex-grow': 1,\r\n  'flex-shrink': 0,\r\n  'font-weight': 'normal',\r\n  backgroundColor: 'white',\r\n};\r\n```\r\n\r\n## What should I do when I get type errors?\r\n\r\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\r\n\r\n_If you're using CSS Custom Properties you can step directly to step 3._\r\n\r\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\r\n\r\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\r\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\r\n\r\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\r\n3.  Fix the issue locally with **TypeScript** (Flow further down):\r\n\r\n    - The recommended way is to use **module augmentation**. Here's a few examples:\r\n\r\n      ```ts\r\n      // My css.d.ts file\r\n      import * as CSS from 'csstype';\r\n\r\n      declare module 'csstype' {\r\n        interface Properties {\r\n          // Add a missing property\r\n          WebkitRocketLauncher?: string;\r\n\r\n          // Add a CSS Custom Property\r\n          '--theme-color'?: 'black' | 'white';\r\n\r\n          // ...or allow any other property\r\n          [index: string]: any;\r\n        }\r\n      }\r\n      ```\r\n\r\n    - The alternative way is to use **type assertion**. Here's a few examples:\r\n\r\n      ```ts\r\n      const style: CSS.Properties = {\r\n        // Add a missing property\r\n        ['WebkitRocketLauncher' as any]: 'launching',\r\n\r\n        // Add a CSS Custom Property\r\n        ['--theme-color' as any]: 'black',\r\n      };\r\n      ```\r\n\r\n    Fix the issue locally with **Flow**:\r\n\r\n    - Use **type assertion**. Here's a few examples:\r\n\r\n      ```js\r\n      const style: $Exact<CSS.Properties<*>> = {\r\n        // Add a missing property\r\n        [('WebkitRocketLauncher': any)]: 'launching',\r\n\r\n        // Add a CSS Custom Property\r\n        [('--theme-color': any)]: 'black',\r\n      };\r\n      ```\r\n\r\n## Version 2.0\r\n\r\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\r\n\r\n- `msOverflowStyle` is still `msOverflowStyle`\r\n- `mozAppearance` is now `MozAppearance`\r\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\r\n\r\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\r\n\r\n## Contributing\r\n\r\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\r\n\r\n### Commands\r\n\r\n- `yarn build` Generates typings and type checks them\r\n- `yarn watch` Runs build on each save\r\n- `yarn test` Runs the tests\r\n- `yarn lazy` Type checks, lints and formats everything\r\n", "readmeFilename": "README.md", "gitHead": "451d62da7ef1b5e73907bb12fbcc1d2a0106993f", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.20", "_nodeVersion": "16.6.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-/WwNkdXfckNgw6S5R125rrW8ez139lBHWouiBvX8dfMFtcn6V81REDqnH7+CRpRipfYlyU1CmOnOxrmGcFOjeA==", "shasum": "9229c65ea0b260cf4d3d997cb06288e36a8d6dda", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.20.tgz", "fileCount": 5, "unpackedSize": 1953772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIMu8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmot6A/9F75bkIR+Vla5zqeTXrV4KxBDepWCI4qGFMqarW8mh7y11kjy\r\nRS8isnssmSXzjthbAqjBZ3r4WqbTqL1WIKKiaDP0wF9rBeyb77oDq5RF+p7a\r\nyNwdJCdBS9H5Nf6PoCfdG3hIyw6iWjVa4ksDXoqm5kbWDwKBylSDyUl7JgEK\r\nViQelNTfvxRzxnPFjc/ZbLMstCYyubxZSY8xAZvKEsp2xgZIQsbJEt6agjP/\r\n0zXmsD6WIltlQJfMhfqUpEvRVRDmpx0XxrOcUAwttZ13/nT05uvdaYG1fJsV\r\nHhgWgIcuHuC2c9qi6k44HJYLskcfN6QCuYnKUUIk3vW/Hzy+/UqHCs3BOnSA\r\n5fCjgmTjDdmwnuEhtzi48xcU8ZjmhYek3Va222fOmalGE1W9MbPRaM0eJVrA\r\nftNM1EjkOxjoXxt2skzhYkBbSexVPP6Jk8fVfCRA6Pib8GTDtdTghzUjZ6PB\r\no6hlWydM7d92ncoIzjGB0/hY6pRiCL/UjPHCXd96HN/IUnNGJqvaNAhUxN56\r\nq8yBaKBEyaFAtWr5xYDgqz17GGzFrupd4z2g2VDreL2SdDIa/kQ29ET5OhE1\r\n8XEn1ukDmvp1cF8mdUCAhaicq423B2xLFo2vjNJyou72MBj1BkduQIfmJfuZ\r\nzV2wJBAf2la+BaP15g8OaUUGr/G+VS1wdMU=\r\n=+EMb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9rD6kWkhLcx9bm3xNsd9ClHYmYOawd/91j6ZJzH5OjAiEAquhV6qcStUcs2dvQlINIbnegIf2MXYcnP50dEEXs+JE="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.20_1646316476328_0.1936687231286518"}, "_hasShrinkwrap": false}, "3.0.11": {"name": "csstype", "version": "3.0.11", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^1.0.7", "@types/jest": "^27.4.0", "@types/jsdom": "^16.2.14", "@types/node": "^16.11.22", "@types/prettier": "^2.4.3", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.8.0", "css-tree": "^2.0.4", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.11", "flow-bin": "^0.171.0", "jest": "^27.4.7", "jsdom": "^19.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#92bf4cb8c5594edb314995e53a9cde79d5fb1b1e", "mdn-data": "git+https://github.com/mdn/data.git#cad93443ab28ee08b1ba490b13cf45eafd1f008d", "prettier": "^2.5.1", "request": "^2.88.2", "ts-jest": "^27.1.3", "ts-node": "^10.4.0", "turndown": "^7.1.1", "typescript": "~4.5.5"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "b1318fd65bc177ef7546fdd087aba9ab449a021d", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.0.11", "_nodeVersion": "16.6.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-sa6P2wJ+CAbgyy4KFssIb/JNMLxFvKF1pCYCSXS8ZMuqZnMsrxqI2E5sPyoTpxoPU/gVZMzr2zjOfg8GIZOMsw==", "shasum": "d66700c5eacfac1940deb4e3ee5642792d85cd33", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.0.11.tgz", "fileCount": 5, "unpackedSize": 1199199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIM0RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2IA/+KM65iNZ5YO1C0LUgshbO0gFeIIsZVYnsmjSTXKJMSyCAFRV5\r\nvnf8xwFUBgo6oquq+q6KenrihL1XYHFnsm3BNqwVtZ7XW9n9Qlrd2MbDmA99\r\nyHYm52jUFnrK7GmpjNmFY7DCJo+6Aate9rve7tqqmJyXBD8+TgXAoUVa6OOY\r\nnDYistPMfk5d3hHksCrDhM01DmGFpGVPqjYhOsKO0cVAiC2u2g8yYjaBFU2c\r\nkWB2qYp2b3Ne5F9ELVbdrHzjf6cdJghJ/NApYTGuH01nnW37U6BRNM3Tuesk\r\nTFhU9AYRqwO9Bt4+yq2YVMRIXS0N0KACJQ5YZx1VHnWdCJ1Xlj7UxLeoR4l2\r\ndd7I08m1Y/a4nWOqleDX1z/ENV38HNVGqL23LhDx38o7FfctiTymxrw2m29+\r\ncg96btBruUhBUoBRFCxBmrDvLBVH7Lc40fjFNzjGoORr+SDrP9R2usbRzprR\r\nLQzsCui/bUsWIr1ZokLg+J6SZ0OsWa7+MEztn9vbzC3qrL3ut5r9Ki3pf/M5\r\ndRsa9HFnOP5kGtCXNuEjM4QO96GNXkSZ3OomA1fdsPPi58lA/TqfkX1B8n5k\r\nRyy7AllahbOmIrO/ggaQH9RLfXDsdAlUzQBEeifecYIMMSrcw6FFuRXaXtED\r\nHgFqtseiRHr44XA8h1SzHVG40vACHHFz+us=\r\n=zacB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEWW4WaXAzpRh/K0jbkUEhwBHhvrD5zTvgI3uxu34tN1AiBxvIZUPgkzOvXSv6J2dF0p4hoF2209YZ6pRIZtFM8dCA=="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.0.11_1646316816863_0.33415982579038905"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "csstype", "version": "3.1.0", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^1.0.7", "@types/jest": "^27.5.1", "@types/jsdom": "^16.2.14", "@types/node": "^17.0.33", "@types/prettier": "^2.6.1", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.15.0", "css-tree": "^2.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.11", "flow-bin": "^0.178.0", "jest": "^28.1.0", "jsdom": "^19.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#ca60283f0d680c08ad23e3c81f89225d5b016212", "mdn-data": "git+https://github.com/mdn/data.git#7ad63b7a52b16415e9a67af700a0006300b69b7b", "prettier": "^2.6.2", "request": "^2.88.2", "ts-jest": "^28.0.2", "ts-node": "^10.7.0", "turndown": "^7.1.1", "typescript": "~4.6.4"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "44591bfe82e09a466398fc77305e8421f41f68ac", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.1.0", "_nodeVersion": "16.6.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-uX1KG+x9h5hIJsaKR9xHUeUraxf8IODOwq9JLNPq6BwB04a/xgpq3rcx47l5BZu5zBPlgD342tdke3Hom/nJRA==", "shasum": "4ddcac3718d787cf9df0d1b7d15033925c8f29f2", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.1.0.tgz", "fileCount": 5, "unpackedSize": 1199941, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFLX51V4S47aN3xswKXpvqT3oXMkXdUKttTLgc9JAHWYAiEAg9X/JBpO9bxxUY/JXS7LL0kh4zm7dWqsCQ3rAx2Hd7M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiglBbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWNQ/7BAZdI02i2Xz8Ar37TsHx0uXcsVYNN+4U593eNZtkfQJ2bLUc\r\nmi3wFE4XFpu46n6Q9v38Co21+5msj95OWxScz7cQahQmGYE/rYfIpd2VlsKc\r\n1OQy4Y8jcERjTNZAgD+z81uRiLgCaFE22otryzlaZL4fMiS8ZE4WB5GQvHNZ\r\nL4VWeQkvkHJA7Uq7XSUDmw3aPKFsPEzXdqXLsyIOPmYOJP6d+VstbQZmQ7tf\r\nQgB8rZ6NB0E2+fVp/ez0HFeG3gfoWpEbfnb8yaTO2xblB61opPogqeAsAEeq\r\nsNJOxkO4q8OqYm7lxiR57t0+Y+FT9TROJEcUzTRtmR4+NRbouD38IUmsbRj+\r\nnP0Yc5327sWD1499D1NndYHOolc16urU9oVuHKimlFh5CdgCQVkayCmf2OtX\r\n+kZgLLChe82XE3SlqGKPXsAFW9ybjK3PgXd3HK4W0bEHJ6CDgWQuT/x6BZXd\r\nu92AG7RAxYLxAiB5t9Mjmm3JPIbNOSfUsz8UdlN1DDKXPLFccFspbI2WfjLj\r\nPBuLZpYZSyAWP4588A/o4Xrx+vUHKEb0Hc9cJqDLsjve075ZL23ivUtFsoje\r\nmPMHfYaO4HGCQcVIYWCjnLWWPP8Z9l8el+xgYaTrioHTSwX23vooKIGhIyZk\r\nTl8CgIl3UZ4sqScrGEC4hvPhCETopk1UnBc=\r\n=aQ5X\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.1.0_1652707419320_0.9314072816200318"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "csstype", "version": "3.1.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^1.0.7", "@types/jest": "^27.5.1", "@types/jsdom": "^16.2.14", "@types/node": "^17.0.33", "@types/prettier": "^2.6.1", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.15.0", "css-tree": "^2.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.11", "flow-bin": "^0.178.0", "jest": "^28.1.0", "jsdom": "^19.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#0bf809b28f3cf5007a29b59b885dfada560ead47", "mdn-data": "git+https://github.com/mdn/data.git#9fc21e14442ed6f71626e763e9f5e5370c533380", "prettier": "^2.6.2", "request": "^2.88.2", "ts-jest": "^28.0.2", "ts-node": "^10.7.0", "turndown": "^7.1.1", "typescript": "~4.6.4"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "6e1374a40d81e028ffa5974d286f4721ccec1eb9", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.1.1", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==", "shasum": "841b532c45c758ee546a11d5bd7b7b473c8c30b9", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.1.1.tgz", "fileCount": 5, "unpackedSize": 1185259, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICETDANbzchbIxKAwOiuMMCwJ8qU7RqyjwbxVhw3xvFJAiBRWpR2nBOZ91vI04GCkrIV6u+OxDyXNVJ4gQ9PUIISeg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIDVfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdXhAAg3WwDxN8blqnbHO7O3lxXDD0UjF8+yzp+JMC+1CoMlpH87+W\r\naIhDKGybAfHo02b1pwvr7kogVV/OMj7E3cijoKMlrbgWbakwJXhUKaMc9mj3\r\neqUwO2Kck2GQm4fUF7KIRcySqrY3RXwiEFXiXkCgV4kRJ5slLs8FIRzTNzgm\r\nzx9gUB4WKmDCPG3CPQMiKfS1KVfRmqsLcxsMcUvmcrPHkfMbh2cSChthaetC\r\nCkQUwePRirsJfMQ0iy/3L7evTmxsDIX1ph2yr6ytxylPt8iwSzcqLfv2gAKw\r\nUK6G66voBN5EtHPqROyCinWlqvMLHdp0hhQY0OJiAGwg9OJWclI8JwZlj9dm\r\n4qQGpU2Z+mRzdkH2S8hpQWrm+bflmyE0in9CkeB02PXONZtoylyGVMEElAMT\r\ndHW9Klie1SJ/WUYkvgAjyug+UATwXhaVjkSsbl9TRMQTqGaM66hbinG/uMj+\r\nITFlxLnHFgjy/HOY2aJyLGRQDJBYxNUGp7qEuxhsxLODtgs4OgOe2Vu2K6iB\r\nggQ9z9nnzmdp8IEiqw1I0CD3rJ9xFckYxJCwJ2v0HwGOOaPDMNIdbiJr9OQg\r\ngETjlR54age9SeffC5oCjXygv8Oh5Nmtvm7GHWH+rULcrcrQo5OUNHKoCbqu\r\nMqIzHUycB/dcj3YDBC/dXbdSZfU8n/xFriI=\r\n=qzfm\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.1.1_1663055198885_0.5123409065656568"}, "_hasShrinkwrap": false}, "2.6.21": {"name": "csstype", "version": "2.6.21", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/jest": "^24.0.21", "@types/jsdom": "^12.2.4", "@types/node": "^12.12.3", "@types/prettier": "^1.18.3", "chalk": "^2.4.2", "chokidar": "^3.2.3", "fast-glob": "^3.1.0", "flow-bin": "^0.110.1", "jest": "^24.9.0", "jsdom": "^15.2.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#1e8eedcf98a3c282cc745d274050091fa7602490", "mdn-data": "git+https://github.com/mdn/data.git#fea9f55167435f34fb39912151e1cc4288e7afb7", "prettier": "^1.18.2", "sync-request": "^6.1.0", "ts-node": "^8.4.1", "tslint": "^5.20.0", "tslint-config-prettier": "^1.18.0", "turndown": "^5.0.3", "typescript": "~3.6.4"}, "scripts": {"update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "tslint --exclude node_modules/**/* --exclude **/*.d.ts --fix **/*.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint && npm run pretty", "test": "jest --no-cache", "typecheck": "tsc typecheck.ts --noEmit --pretty & flow check typecheck.js", "prepublish": "tsc && npm run test && npm run build && npm run typecheck"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n## Getting started\n\n```sh\n$ npm install csstype\n$ # or\n$ yarn add csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 2.0](#version-20)\n- [Contributing](#contributing)\n  - [Commands](#commands)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\nAll interfaces has one optional generic argument to define length. It defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@page`**          | `Page`         | `PageHyphen`         | `PageFallback`         | `PageHyphenFallback`         |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Usage\n\nLength defaults to `string | 0`. But it's possible to override it using generics.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<string | number> = {\n  padding: 10,\n  margin: '1rem',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/typescript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 2.0\n\nThe casing of CSS vendor properties are changed matching the casing of prefixes in Javascript. So all of them are capitalized except for `ms`.\n\n- `msOverflowStyle` is still `msOverflowStyle`\n- `mozAppearance` is now `MozAppearance`\n- `webkitOverflowScrolling` is now `WebkitOverflowScrolling`\n\nMore info: https://www.andismith.com/blogs/2012/02/modernizr-prefixed/\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `npm run build` Generates typings and type checks them\n- `npm run watch` Runs build on each save\n- `npm run test` Runs the tests\n- `npm run lazy` Type checks, lints and formats everything\n", "readmeFilename": "README.md", "gitHead": "eb1e8e33e7e9769d60e9ab57c783f32d67192a23", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@2.6.21", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==", "shasum": "2efb85b7cc55c80017c66a5ad7cbd931fda3a90e", "tarball": "https://registry.npmjs.org/csstype/-/csstype-2.6.21.tgz", "fileCount": 5, "unpackedSize": 1925075, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCT1fG0UkqnqpzUg8U1d4ZurQlPzkihsusz+QmcNpuWYAIgGZtCtF+zUL46HuHOpJxDX1YbyY4DTGWY5Ugi7VXzSdI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIZYCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLuBAAiAu719rdAfGCCJfWNwQZzIeqtI7HTIZqSDWJK8vH9xLIt1Cd\r\nG91D+OyV37XGcst3O2p9Sk4VPSKrnjJmtGyiH0CwVRDzmYGTHA3u7Ok5eJLN\r\n0JTFCxlVX39U1GGs1E9ld7PL9c6HgiuuXLs+XalgT8OzKNVBCiHxq6mFKjdc\r\nEUWAdrz+SB0bAXEohENbyucMGE6VB6Ye2Ez9IinyLrZzcvezkQORJL0RXu4J\r\nAqCqGBxpakDjAdSE26zfnlqciptYxnqajEAq1fQSUpRYgRRcjoK9MgPnEqZd\r\nKP22PsU69VTuNqBYScPSiT/QD+hY75LvkbzZpJH8uQied+pJqPzhqCEE2qls\r\nGiKa3YvoD+UJvW5+xBhpBkiYT8o5I0nlihHFnxqavwxMu+Uuriv4QFlqFXeR\r\nR5Tp1kfpBbC4DdlIXZJ7VSXeUEXWgigCCEextT4zrBzWoDwjXnVIpYXiftqV\r\nfTz5RaW6DCSK+561HauZbT5tfw8ic97jbT3dnQmI/CTjSdwxw7D4EmRmnCKn\r\n0tPpzg6mIkEMw1RW8CQWtRsh++qPs4q2rRn/Pw1hAMEbzDgyU8cB7CDbJJ4W\r\n9GH9BzERSPn6CMrj0kzXpVtt43DSwyemexKt2gF17l2UV3MUKNiq6Kok307d\r\nCov0HRjRhwJgodc8Bdi2aE2HNykMighgGAg=\r\n=cd8m\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_2.6.21_1663145474321_0.18242191197131352"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "csstype", "version": "3.1.2", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^2.3.1", "@types/jest": "^29.5.0", "@types/jsdom": "^21.1.1", "@types/node": "^16.18.23", "@types/prettier": "^2.7.2", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.37.0", "css-tree": "^2.3.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "fast-glob": "^3.2.12", "flow-bin": "^0.203.1", "jest": "^29.5.0", "jsdom": "^21.1.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#22e0f5f1707f29f0c646ea89f5ba5ea7734fa48a", "mdn-data": "git+https://github.com/mdn/data.git#d0e0fce2abd53e5c918a2f6a38e8c4607027768a", "prettier": "^2.8.7", "request": "^2.88.2", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "turndown": "^7.1.2", "typescript": "~5.0.3"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest --runInBand", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts --runInBand"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "931aeba89da71467bbcf2c15d24fbac71363a723", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.1.2", "_nodeVersion": "16.19.1", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==", "shasum": "1d4bf9d572f11c14031f0436e1c10bc1f571f50b", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.1.2.tgz", "fileCount": 5, "unpackedSize": 1216255, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHwwDoqFAqA+IVa5XfIm5F9TwBbO5FHduErwNxnNrSRdAiEAxRDrYDUSEkbDU1lfMKajvALL53e+vlX26mlptPvHaH0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKJjLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrE0RAAizyFo4n7IiI2H5zXRtokoem+QtwWdaiAwN03h55tAP8jKSbx\r\nMLhrG49XZnz3LhVBsiCH0/ZkNhmvRI7qww/emBVJi1XTi3wvEtwIsKKfx8Xt\r\ncbWRppW+3c+/iJKmhwgCA+H+aJM/VH7LzaXDL831dEup88zINobNelTjtElm\r\nt8Ja7KT48WO1u7dn+K/32TovpQHPlLZC15STY48jZKIi6DQXqMTI/JUVZw28\r\nulUhPz43vN8ACpBUDHGUtZ5M2eHC4pz6cK/edsqva6utkqQ1QEwCO0FH4+ZF\r\n0AEagHZPAeXvjW2qWeyFyQXKE8VB1Xxhnchm/TaRlPZdDGfntDpgmVcH+iYc\r\nJYMvudtB/1RE9dtVLkJaoYpaDH0fxk2Sxer7kQL3eiIY1DdJYnMapHjzWIP1\r\nPzcVJ6IaoxffL3/QgsE3Hi30av4JQuAKag7YOcB9UgzOwE4DQUoEI7fcL/3A\r\nYVM4hZjEN3pL+UeybZTMwLvrwCABI4e2u1D9HVFd5Nf0zsw2f3sNA11nQz4O\r\noNnqilKVqOr5PPkd5lOWfTEKz86A6Tlf6mmmarSGOFFVGHhncJC3xpUmg5sW\r\n0Bg0Y8mzAtej8qRYmlI3l2QN/pCHTT6AJY8bWJU6oxlK972EG+utEFWBLxP+\r\n+TZ75yAuVjlcTwrGz5y1XH3IRrbzHccwai4=\r\n=e/RG\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.1.2_1680382155047_0.5843843285404755"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "csstype", "version": "3.1.3", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^2.3.1", "@types/jest": "^29.5.0", "@types/jsdom": "^21.1.1", "@types/node": "^16.18.23", "@types/prettier": "^2.7.2", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.37.0", "css-tree": "^2.3.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "fast-glob": "^3.2.12", "flow-bin": "^0.203.1", "jest": "^29.5.0", "jsdom": "^21.1.1", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#1bf44517bd08de735e9ec20dbfe8e86c96341054", "mdn-data": "git+https://github.com/mdn/data.git#7f0c865a3c4b5d891285c93308ee5c25cb5cfee8", "prettier": "^2.8.7", "request": "^2.88.2", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "turndown": "^7.1.2", "typescript": "~5.0.3"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest --runInBand", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts --runInBand"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "gitHead": "fb448e21733ac5cb52523d3b678fdbbe1f9b42f2", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "homepage": "https://github.com/frenic/csstype#readme", "_id": "csstype@3.1.3", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "shasum": "d80ff294d114fb0e6ac500fbf85b60137d7eff81", "tarball": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "fileCount": 5, "unpackedSize": 1246074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF2zTnkc6R7cr7euidncjKp9gnQSpUmoKPzB/pvL4rsQAiB4E1mDQEtpvu9cjct0kaUbtTAQc+rBLAO65gAk+ykfig=="}]}, "_npmUser": {"name": "faddee", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csstype_3.1.3_1701944301555_0.34612030979146424"}, "_hasShrinkwrap": false}}, "readme": "# CSSType\n\n[![npm](https://img.shields.io/npm/v/csstype.svg)](https://www.npmjs.com/package/csstype)\n\nTypeScript and Flow definitions for CSS, generated by [data from MDN](https://github.com/mdn/data). It provides autocompletion and type checking for CSS properties and values.\n\n**TypeScript**\n\n```ts\nimport type * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n**Flow**\n\n```js\n// @flow strict\nimport * as CSS from 'csstype';\n\nconst style: CSS.Properties<> = {\n  colour: 'white', // Type error on property\n  textAlign: 'middle', // Type error on value\n};\n```\n\n_Further examples below will be in TypeScript!_\n\n## Getting started\n\n```sh\n$ npm install csstype\n```\n\n## Table of content\n\n- [Style types](#style-types)\n- [At-rule types](#at-rule-types)\n- [Pseudo types](#pseudo-types)\n- [Generics](#generics)\n- [Usage](#usage)\n- [What should I do when I get type errors?](#what-should-i-do-when-i-get-type-errors)\n- [Version 3.0](#version-30)\n- [Contributing](#contributing)\n\n## Style types\n\nProperties are categorized in different uses and in several technical variations to provide typings that suits as many as possible.\n\n|                | Default              | `Hyphen`                   | `Fallback`                   | `HyphenFallback`                   |\n| -------------- | -------------------- | -------------------------- | ---------------------------- | ---------------------------------- |\n| **All**        | `Properties`         | `PropertiesHyphen`         | `PropertiesFallback`         | `PropertiesHyphenFallback`         |\n| **`Standard`** | `StandardProperties` | `StandardPropertiesHyphen` | `StandardPropertiesFallback` | `StandardPropertiesHyphenFallback` |\n| **`Vendor`**   | `VendorProperties`   | `VendorPropertiesHyphen`   | `VendorPropertiesFallback`   | `VendorPropertiesHyphenFallback`   |\n| **`Obsolete`** | `ObsoleteProperties` | `ObsoletePropertiesHyphen` | `ObsoletePropertiesFallback` | `ObsoletePropertiesHyphenFallback` |\n| **`Svg`**      | `SvgProperties`      | `SvgPropertiesHyphen`      | `SvgPropertiesFallback`      | `SvgPropertiesHyphenFallback`      |\n\nCategories:\n\n- **All** - Includes `Standard`, `Vendor`, `Obsolete` and `Svg`\n- **`Standard`** - Current properties and extends subcategories `StandardLonghand` and `StandardShorthand` _(e.g. `StandardShorthandProperties`)_\n- **`Vendor`** - Vendor prefixed properties and extends subcategories `VendorLonghand` and `VendorShorthand` _(e.g. `VendorShorthandProperties`)_\n- **`Obsolete`** - Removed or deprecated properties\n- **`Svg`** - SVG-specific properties\n\nVariations:\n\n- **Default** - JavaScript (camel) cased property names\n- **`Hyphen`** - CSS (kebab) cased property names\n- **`Fallback`** - Also accepts array of values e.g. `string | string[]`\n\n## At-rule types\n\nAt-rule interfaces with descriptors.\n\n**TypeScript**: These will be found in the `AtRule` namespace, e.g. `AtRule.Viewport`.  \n**Flow**: These will be prefixed with `AtRule$`, e.g. `AtRule$Viewport`.\n\n|                      | Default        | `Hyphen`             | `Fallback`             | `HyphenFallback`             |\n| -------------------- | -------------- | -------------------- | ---------------------- | ---------------------------- |\n| **`@counter-style`** | `CounterStyle` | `CounterStyleHyphen` | `CounterStyleFallback` | `CounterStyleHyphenFallback` |\n| **`@font-face`**     | `FontFace`     | `FontFaceHyphen`     | `FontFaceFallback`     | `FontFaceHyphenFallback`     |\n| **`@viewport`**      | `Viewport`     | `ViewportHyphen`     | `ViewportFallback`     | `ViewportHyphenFallback`     |\n\n## Pseudo types\n\nString literals of pseudo classes and pseudo elements\n\n- `Pseudos`\n\n  Extends:\n\n  - `AdvancedPseudos`\n\n    Function-like pseudos e.g. `:not(:first-child)`. The string literal contains the value excluding the parenthesis: `:not`. These are separated because they require an argument that results in infinite number of variations.\n\n  - `SimplePseudos`\n\n    Plain pseudos e.g. `:hover` that can only be **one** variation.\n\n## Generics\n\nAll interfaces has two optional generic argument to define length and time: `CSS.Properties<TLength = string | 0, TTime = string>`\n\n- **Length** is the first generic parameter and defaults to `string | 0` because `0` is the only [length where the unit identifier is optional](https://drafts.csswg.org/css-values-3/#lengths). You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n  ```tsx\n  const style: CSS.Properties<string | number> = {\n    width: 100,\n  };\n  ```\n- **Time** is the second generic argument and defaults to `string`. You can specify this, e.g. `string | number`, for platforms and libraries that accepts any numeric value as length with a specific unit.\n  ```tsx\n  const style: CSS.Properties<string | number, number> = {\n    transitionDuration: 1000,\n  };\n  ```\n\n## Usage\n\n```ts\nimport type * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  width: '10px',\n  margin: '1em',\n};\n```\n\nIn some cases, like for CSS-in-JS libraries, an array of values is a way to provide fallback values in CSS. Using `CSS.PropertiesFallback` instead of `CSS.Properties` will add the possibility to use any property value as an array of values.\n\n```ts\nimport type * as CSS from 'csstype';\n\nconst style: CSS.PropertiesFallback = {\n  display: ['-webkit-flex', 'flex'],\n  color: 'white',\n};\n```\n\nThere's even string literals for pseudo selectors and elements.\n\n```ts\nimport type * as CSS from 'csstype';\n\nconst pseudos: { [P in CSS.SimplePseudos]?: CSS.Properties } = {\n  ':hover': {\n    display: 'flex',\n  },\n};\n```\n\nHyphen cased (kebab cased) properties are provided in `CSS.PropertiesHyphen` and `CSS.PropertiesHyphenFallback`. It's not **not** added by default in `CSS.Properties`. To allow both of them, you can simply extend with `CSS.PropertiesHyphen` or/and `CSS.PropertiesHyphenFallback`.\n\n```ts\nimport type * as CSS from 'csstype';\n\ninterface Style extends CSS.Properties, CSS.PropertiesHyphen {}\n\nconst style: Style = {\n  'flex-grow': 1,\n  'flex-shrink': 0,\n  'font-weight': 'normal',\n  backgroundColor: 'white',\n};\n```\n\nAdding type checked CSS properties to a `HTMLElement`.\n\n```ts\nimport type * as CSS from 'csstype';\n\nconst style: CSS.Properties = {\n  color: 'red',\n  margin: '1em',\n};\n\nlet button = document.createElement('button');\n\nObject.assign(button.style, style);\n```\n\n## What should I do when I get type errors?\n\nThe goal is to have as perfect types as possible and we're trying to do our best. But with CSS Custom Properties, the CSS specification changing frequently and vendors implementing their own specifications with new releases sometimes causes type errors even if it should work. Here's some steps you could take to get it fixed:\n\n_If you're using CSS Custom Properties you can step directly to step 3._\n\n1.  **First of all, make sure you're doing it right.** A type error could also indicate that you're not :wink:\n\n    - Some CSS specs that some vendors has implemented could have been officially rejected or haven't yet received any official acceptance and are therefor not included\n    - If you're using TypeScript, [type widening](https://blog.mariusschulz.com/2017/02/04/TypeScript-2-1-literal-type-widening) could be the reason you get `Type 'string' is not assignable to...` errors\n\n2.  **Have a look in [issues](https://github.com/frenic/csstype/issues) to see if an issue already has been filed. If not, create a new one.** To help us out, please refer to any information you have found.\n3.  Fix the issue locally with **TypeScript** (Flow further down):\n\n    - The recommended way is to use **module augmentation**. Here's a few examples:\n\n      ```ts\n      // My css.d.ts file\n      import type * as CSS from 'csstype';\n\n      declare module 'csstype' {\n        interface Properties {\n          // Add a missing property\n          WebkitRocketLauncher?: string;\n\n          // Add a CSS Custom Property\n          '--theme-color'?: 'black' | 'white';\n\n          // Allow namespaced CSS Custom Properties\n          [index: `--theme-${string}`]: any;\n          \n          // Allow any CSS Custom Properties\n          [index: `--${string}`]: any;\n\n          // ...or allow any other property\n          [index: string]: any;\n        }\n      }\n      ```\n\n    - The alternative way is to use **type assertion**. Here's a few examples:\n\n      ```ts\n      const style: CSS.Properties = {\n        // Add a missing property\n        ['WebkitRocketLauncher' as any]: 'launching',\n\n        // Add a CSS Custom Property\n        ['--theme-color' as any]: 'black',\n      };\n      ```\n\n    Fix the issue locally with **Flow**:\n\n    - Use **type assertion**. Here's a few examples:\n\n      ```js\n      const style: $Exact<CSS.Properties<*>> = {\n        // Add a missing property\n        [('WebkitRocketLauncher': any)]: 'launching',\n\n        // Add a CSS Custom Property\n        [('--theme-color': any)]: 'black',\n      };\n      ```\n\n## Version 3.0\n\n- **All property types are exposed with namespace**  \n  TypeScript: `Property.AlignContent` (was `AlignContentProperty` before)  \n  Flow: `Property$AlignContent`\n- **All at-rules are exposed with namespace**  \n  TypeScript: `AtRule.FontFace` (was `FontFace` before)  \n  Flow: `AtRule$FontFace`\n- **Data types are NOT exposed**  \n  E.g. `Color` and `Box`. Because the generation of data types may suddenly be removed or renamed.\n- **TypeScript hack for autocompletion**  \n  Uses `(string & {})` for literal string unions and `(number & {})` for literal number unions ([related issue](https://github.com/microsoft/TypeScript/issues/29729)). Utilize `PropertyValue<T>` to unpack types from e.g. `(string & {})` to `string`.\n- **New generic for time**  \n  Read more on the [\"Generics\"](#generics) section.\n- **Flow types improvements**  \n  Flow Strict enabled and exact types are used.\n\n## Contributing\n\n**Never modify `index.d.ts` and `index.js.flow` directly. They are generated automatically and committed so that we can easily follow any change it results in.** Therefor it's important that you run `$ git config merge.ours.driver true` after you've forked and cloned. That setting prevents merge conflicts when doing rebase.\n\n### Commands\n\n- `npm run build` Generates typings and type checks them\n- `npm run watch` Runs build on each save\n- `npm run test` Runs the tests\n- `npm run lazy` Type checks, lints and formats everything\n", "maintainers": [{"name": "and<PERSON>.e<PERSON>l", "email": "<EMAIL>"}, {"name": "faddee", "email": "<EMAIL>"}], "time": {"modified": "2023-12-07T10:18:21.961Z", "created": "2017-09-24T22:14:25.900Z", "1.0.0": "2017-09-24T22:14:25.900Z", "1.0.1": "2017-09-25T18:50:47.848Z", "1.1.0": "2017-09-25T22:36:58.659Z", "1.2.0": "2018-01-15T16:36:23.346Z", "1.2.1": "2018-01-15T21:31:19.227Z", "1.3.0": "2018-01-18T08:14:23.007Z", "1.4.0": "2018-02-03T00:23:05.971Z", "1.5.0": "2018-02-07T20:51:55.931Z", "1.6.0": "2018-02-12T14:50:05.920Z", "1.7.0": "2018-02-13T22:32:53.327Z", "1.7.1": "2018-02-15T14:03:40.090Z", "1.7.2": "2018-02-19T07:18:36.997Z", "1.8.0": "2018-02-22T10:08:04.873Z", "1.8.1": "2018-03-01T12:45:01.240Z", "1.8.2": "2018-03-02T22:59:41.357Z", "2.0.0": "2018-03-19T09:23:50.404Z", "2.1.0": "2018-04-04T18:26:32.502Z", "2.1.1": "2018-04-05T07:09:01.050Z", "2.2.0": "2018-04-11T07:48:41.012Z", "2.3.0": "2018-04-18T22:27:52.004Z", "2.3.1": "2018-04-24T07:02:46.073Z", "2.4.0": "2018-04-24T19:10:35.271Z", "2.4.1": "2018-04-26T08:12:11.967Z", "2.4.2": "2018-04-28T07:40:36.879Z", "2.5.0": "2018-05-09T21:52:08.425Z", "2.5.1": "2018-05-11T23:08:46.112Z", "2.5.2": "2018-05-16T22:15:49.595Z", "2.5.3": "2018-05-29T12:28:22.761Z", "2.5.4": "2018-06-18T22:01:18.298Z", "2.5.5": "2018-06-19T18:56:13.207Z", "2.5.6": "2018-07-21T22:49:25.449Z", "2.5.7": "2018-09-13T07:03:14.369Z", "2.5.8": "2018-12-03T07:59:16.234Z", "2.6.0": "2018-12-27T10:21:18.799Z", "2.6.1": "2019-01-18T08:24:08.156Z", "2.6.2": "2019-01-29T20:46:08.213Z", "2.6.3": "2019-03-05T12:04:42.724Z", "2.6.4": "2019-04-17T11:18:59.647Z", "2.6.5": "2019-05-27T13:23:48.618Z", "2.6.6": "2019-07-08T09:47:22.841Z", "2.6.7": "2019-10-04T18:43:46.270Z", "3.0.0-alpha.0": "2019-11-16T19:12:27.226Z", "3.0.0-alpha.1": "2019-11-22T19:11:14.279Z", "3.0.0-alpha.2": "2019-11-22T20:18:48.155Z", "3.0.0-beta.0": "2019-12-01T19:34:45.590Z", "3.0.0-beta.1": "2019-12-04T14:15:50.715Z", "2.6.8": "2019-12-16T07:13:39.867Z", "2.6.9": "2020-02-14T06:23:42.007Z", "2.6.10": "2020-03-30T07:39:06.273Z", "3.0.0-beta.2": "2020-03-30T07:52:18.356Z", "3.0.0-beta.3": "2020-04-15T07:23:13.382Z", "2.6.11": "2020-07-01T10:55:14.670Z", "3.0.0-beta.4": "2020-07-01T11:02:31.902Z", "2.6.12": "2020-07-29T18:43:26.352Z", "3.0.0": "2020-07-29T18:48:04.859Z", "2.6.13": "2020-07-29T19:46:52.454Z", "3.0.1": "2020-07-29T19:54:48.798Z", "3.0.2": "2020-07-30T10:01:51.820Z", "3.0.3": "2020-08-25T10:05:46.976Z", "3.0.4": "2020-10-23T07:58:46.941Z", "3.0.5": "2020-11-13T08:52:43.932Z", "2.6.14": "2020-11-13T08:56:34.242Z", "3.0.6": "2021-01-08T12:29:00.640Z", "2.6.15": "2021-02-16T09:17:23.138Z", "2.6.16": "2021-02-24T09:31:35.839Z", "3.0.7": "2021-02-24T09:40:44.118Z", "3.0.8": "2021-04-19T07:35:09.726Z", "2.6.17": "2021-04-19T07:46:17.945Z", "2.6.18": "2021-09-13T13:39:25.643Z", "3.0.9": "2021-09-13T13:42:44.181Z", "3.0.10": "2021-11-15T11:06:12.378Z", "2.6.19": "2021-11-18T08:30:47.806Z", "2.6.20": "2022-03-03T14:07:56.563Z", "3.0.11": "2022-03-03T14:13:37.056Z", "3.1.0": "2022-05-16T13:23:39.563Z", "3.1.1": "2022-09-13T07:46:39.111Z", "2.6.21": "2022-09-14T08:51:14.521Z", "3.1.2": "2023-04-01T20:49:15.271Z", "3.1.3": "2023-12-07T10:18:21.772Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "readmeFilename": "README.md", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "homepage": "https://github.com/frenic/csstype#readme", "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"]}