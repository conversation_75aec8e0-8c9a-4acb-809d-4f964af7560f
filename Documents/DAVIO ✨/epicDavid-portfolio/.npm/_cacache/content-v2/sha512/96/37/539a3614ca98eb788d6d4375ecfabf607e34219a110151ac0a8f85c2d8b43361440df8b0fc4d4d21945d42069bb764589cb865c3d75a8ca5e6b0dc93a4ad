{"_id": "babel-plugin-polyfill-corejs3", "_rev": "53-d6ba5cdb68a09c1db97b2be9169f24e7", "name": "babel-plugin-polyfill-corejs3", "dist-tags": {"latest": "0.13.0"}, "versions": {"0.0.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.0", "keywords": [], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dad7ac660be2a1785b3a5319429ed32c392eb936", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-vnZvkZoyH0LbwY/zeY8QrmeHtHYvfIZvHNcL/q9mmYjo7mNxw+luTVFpdNFNpJDacNnb3SloOn4BGIa0F+n4DQ==", "signatures": [{"sig": "MEQCIH8R9nquOlpDv+xiyy4aWZOqaN8DROy4Bh7qfqRSeqhVAiB/2WNdsN98zZayBVwK7gnvd/5vD7pYzNy62mXYPUV1gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejd9WCRA9TVsSAnZWagAAC9oP/jVyo5adjGvtbybuKUwn\nzTzNPnxB5lw3IYZAtxO+cMmm0ejbMWrBZNXJRlFatZu6ZbSCXsmpa7CsTYq2\nWC/0Dzms8G77BXaUZBLVNu1LQBnIg7uQY3HVU4+8Gxjc3TexEKvr92pZAXop\nTAO1+dWdluJUGYxSlR73KKFKPEhnHyuknlcozcLoV2gHNATYFTRIuT3RpsgX\nHaOwItYj4wj5STHHqwxhY5XW6LO6/popQo59MNwWHIDQdlnRGFL6Lxxiwx2d\ns+JZwLZ3WUkmq+fZVZVMW76U4biDqtS3vDjv7llqErdxm8S8yevDji9zfuXo\nq4ID0FSbwea3hpg7qDYPbe4y7A1KL4yrs6HGKhwT06KSt+xH7Mx+fIpVFk1Z\n3A0t8wrHvx/N7BN+P5FyXW7pon+f57xZ3j4y0PiGxrLTA2WVw0P18vf5Zw2R\niK2Hw2y7tVxT405dE/XNOh0sYbT639zHA4SaEp1Lw1sQ34Y7VNsrgow61wsT\nJvgqG2PcFMiVKlKodkyO+mkiFh66Kcku50OIzkzKXsTO5VQvBuarrCkFEIon\nk1+LpY1+TA7JVD1n/PaOV1YPlSmudiemABykNtJkQ7Apr6UxSmQcMfBhZBYC\nVAPmTrW3sPAkmjs9ufVZqLBprLXn2fMOtr3PAfK5t3Gy0Mm9+pLfeHYEhGeQ\nBDPJ\r\n=bOSl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "_npmVersion": "6.13.4", "description": "", "directories": {}, "_nodeVersion": "10.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.0_1586356054284_0.18713437440093728", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "774034df3374304375f675e9136f38e2b717a4d5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.1.tgz", "fileCount": 7, "integrity": "sha512-J8KVdcgdl7bxr23o8JD5Q5is6KQHnL37am9gv+gxSZDoD2934MsAKhb7KvJdkH2WzEqNiyzR0K2I0cSoPEYEMQ==", "signatures": [{"sig": "MEUCIHjFzoKOiUpNL3aGOiQfbeyt1BjzXa5y4rJYkl0ajB0bAiEAogLzXY8MsncVtWmihpoMPuXn4SjBvOaoFO7af/Sg/tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezX8xCRA9TVsSAnZWagAAV58P/ioKYYDjRvI10nPhAzCj\no8WFmJDYqdKLuO5ZGrhwN1woht3cCO5QQhcWLvK2zLJgHEN0wEOA2BaynO5c\nw/tSN3vxFP9jPdnsP4S6wtl9vi8pNPSShGuGOaHeXNEd5tjwyYP4QSdhVU0I\ngpZvWtGMqr6M3smVJOjZHINoR3GCekJrQ+H53+mc7j2PI2N2BOhjyssXGCpP\nlLkAeD3YIjTFr74m9LgYrTEmsw1h92EBt7/FXoDw9xPhZ7ZyYL/+IaaLbhbs\n4c7jFs6Ptt/m7JN7kGCGCw8tU+rk4XfN1ZaUYYhTpjgSTQ/kl6ssMWDvEc6B\ntkVjFkgECLci5sjVkMUXy/0y3SOrr5CPosgq3IZ9aszZtLmNPd8ZZrSGw3Ya\nSRkMbrbY/eX27Z99PWJ2kxGry4S1qYpOTkTCanue4ocvjysarpPJ9kKFe/vH\nDpw9uUCdH2ZV96L+kY0Wp3TwHrKStRrJtJs0emb8JFgjshf3eG9uNZ3+bMVa\n5poZrgf53TFlhzYwPoybBIDGPNi1JnshdlEZQPSdy1jPE5Unyq8er9iYLBhn\nTXoJijYWe6t6UtTJNjDRkrm7L872z/gYCHJdWToYZpL7S7mYbgk3O7xqww3T\nSHSc5slRYRdm5spAxPlypafPwHSNmb8Y82zxBWtAilNoeB8+uStWptlpxUEH\nVzzt\r\n=Nm6c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "82bf8ef0c6f375d290434a1c083983f0b8c6dffc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"core-js-compat": "^3.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/helper-define-polyfill-provider": "^0.0.1", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-define-polyfill-provider": "^0.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.1_1590525744732_0.318909456173869", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ce305d25466fdae335b3c2c813abe24ad81c322d", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.2.tgz", "fileCount": 7, "integrity": "sha512-Y6b8w/lkoFYrpDG/mmtbAFSsalPh9XX4IpOdEA62XYsbqQM4CjvMR4VwuxPiae21PkpNQ5tyjuhTCG+yrq4hvQ==", "signatures": [{"sig": "MEUCIQDKM8mhTHilyI3devbZ21zjFytlpB3BLiV9EbxMtycligIgZMXS0XfTHi0jcoH8pxUoGe5/vzveHEf3b1ttQTSdlTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezYOCCRA9TVsSAnZWagAAbOIP/RzWJH8sZ0K69zsP7PMj\nYzIiN7c5HQ3Lq1wzHfT0cxRBJfTLxT8ucL7PtOFwf6hM8aTY3xD+xXwid7wr\neWarJbHIR+yUPiIY1fUZMubmXpdKqhpVpTjdJaFIa27rNeA3UZ+iEKPKUcWL\nWs329PPZlKgoXU9GfZkoX0P0fsPwOyAC6oJykbcy917XfdgvE28UbSjy1hyZ\nwmJNLIwURgtkDabdhfTmoqbbYdClMHL08BZ4p1YRP16FKme44hMv8ATjrPER\nTU5BI6gHsOqKMO6ys1PG2mKrQ6dkcX+aFi/2THnq0e+Cy/cSe0CYrCQ299S7\nx21udYe/j9ZVP+o/ZHoHgx2j3RV9utatrPne6kGIu7E5RVOm1VOhW0s3WVJA\n+3k5MDQY2quwn3511j2Wh2N7XR5GI2jwN/MJCgdhqWGHdbVwPKKQMTiHoomX\n5B5OrytNH+aJRm15zx7o9FbLSMqYX3yclVTIBuklDIOpThPTBh6eRVNzZR1n\nkTSpCOmNEytpmk1Ogt9B7dXm+YtI07hAyUia5eNo6FAR1frx7wOYi9iR5PHq\nH4aUg9UJ0bu4vFyj3QAR/ZSBTYl5uN+IJO0psAFBYrTP10c/dABlaLPCZ+uv\nDpWQxcosEsy/Yb+BMJ5IhCx/3BHrAd61eu0nDlcF9Y4nFe0J80z8dG9LBSWh\nwi7x\r\n=Ta6w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f014169a707ba3f7bae1b38ccdc834f0b904b4b1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"core-js-compat": "^3.6.4", "@babel/helper-define-polyfill-provider": "^0.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-define-polyfill-provider": "^0.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.2_1590526849861_0.05078876491707418", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d6e34c21d3f8c3b5c46042a7c14a0b35551fdae2", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.3.tgz", "fileCount": 7, "integrity": "sha512-ydYyGwjxaevrklWL193SKlM+c3EAW8DBRCKSzwkftDpHcdDJQknd6Ntr8dzmkJ+/W7SJ4TeMBR8b59gUso0mGQ==", "signatures": [{"sig": "MEQCIBeswnevMQnzogQHfxo0JzSzTAP46UEevU/ArQV5VxbIAiA/dMfm1coUcEofazbVhA3Clf6/VKMOHZiRD+zyKVrJdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCN9ICRA9TVsSAnZWagAA7v8P/1HPHLSdpVeUJH1lD7W4\nnsBDsotHcJ/ojXBPYQVyCe0eZp41l6P1uAAryfxusqhB+YWV5XeGuiOEmcrm\nC5yNKOV1U31/0BPb5FnhTBbunOCi0aAyFsEhdMEx/3HZ2p7HUjuOIfr8VKfR\nJ7FqygBs5wjxPoaF16Gh85+xQ2DPu/G44R01IdPik3PIBSfBW9U8tOZCFx1k\naY09x580zqJlbNJSiPC7FVLeoaj+8L5ZgncUZmfsvH1X5AB6+jgVpGbxGcrf\nze8W241BB0leTLl+mCVMMT5q/dJvGbMHzpYw6lVBk5V1tB1jVh1cpYh4HOXG\nrP7yVvOo3dlf+Bd1vqMx/w7FmHDArFakRgcj8b6BxbP5fmi9+FZ/4aw9R5z5\n8RwsNFrRjP0QCLkVmtAwQS7T3Kt7A0aCqdjsilIgzBTqJEbsBhHQDga9jbak\ntG6FkcIODwPO+yZcmkYeV/JIuik9xmW7QfS13H023jKrq8+qZbsDMdiykOe9\nyBwmQV/wGX4yRFApsGUhwsWFFvsVXHvBj8cTV8NAmmE8XSG26Ta+tAndFsun\n41LTbI2e98xzW+t2KPn/GN1hxHvXiSj8I5qEj6iKa1l9fNE5dQNNN+MuSdIL\nsBNF3Ga6hwpOdBMoB4+oiHD1Rpf7Msk0hot4G9K4813GnDcdgLxkKs27SX7l\nWDQb\r\n=Nwm2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "884798ac0f8dec9d8a4b6fc18967acdaf6596836", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"core-js-compat": "^3.6.4", "@babel/helper-define-polyfill-provider": "^0.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.3_1594416968280_0.5364066685197588", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "c1897bb446ee1b7fd2ef1c8d01f2050c50ff667f", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.4.tgz", "fileCount": 7, "integrity": "sha512-KrhfpiWV4/L7YV4GCGTY3o+qwLODhley4h0BOE7IhTYYMH+avbKOXVP5d5Zy46U0nvHL/hhVz1e2XOm6oKmXMQ==", "signatures": [{"sig": "MEQCIBpnxXWBrb4gQo2XLUX9Qx3nOU1NefGD/jp85Y4nQ/T0AiBCRvdCnivMCfcXGE1kmAtSQSQ7xNd6BQe43nCYa2b3kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPFWKCRA9TVsSAnZWagAAdBAP/0hOyOHSgvCqpn0e+KC5\nbH6ltAElOHE5iFrwKi1RLd5jzUZgdGSGKsDSNpeKnoFOzMD4UXURWSjWD3d1\nPsmeElvCapaqPimqtt4h6h9AObW2/lNz1rZNBhM7/4q7JlURSnUY36sYdjB0\nOQu4yQjwDeFzeeeAc5yVt4IZla3nrtc289EV3E7/8/Q6aVWtEkweJ8Sab071\n8QEeTTGNdWfVhmdfOS6KNrIQTKwEvlIzYzIpv79VxdwqMGeu5hgJGYoDiJL1\nvDyOvNl8/kSKM7Yj5TcTy/Nai+SKyFhP2UBS26+pDQokukW5QOBhtW2Kf72H\neImT1AT3LFGo7mehFaQvWZl6c7Te7WYmUOyROhaqoAeiGLg5yMvmIw+k4UGM\njEYqhNI2McbNyqIMei6bpAEaOcne7kEjxcvCSOkB38b9JpwjKx6yLABmNe8A\nbDfnr2nQrY1ToYPzmoFsD2Bapwh2edA2u86rPDUnVw7Rr6YbPEkEH7ZvXEXe\nDUxH+vPjARXb+/l+elGcSORkl9dou18h2/rNiWF6W0O9S0o7BTLroYkSALWp\nU6mezBqQBsBGBNFE7nlRykjt8M4Cc3WECwfMl7wbmGjPM9lKnLTDK+dg8BKK\nO6HGpjyj6VBug/oP+gHGfcrrqFfwsg5NpHGNKJ6dP2Atv9WGjwObgNjBM432\n7R4N\r\n=x9+4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5f4821a038ce49e599add9b40f3568ffaa41e7fd", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"core-js-compat": "^3.6.4", "@babel/helper-define-polyfill-provider": "^0.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.4_1597789578203_0.667741216094851", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "3c848e48a7c47065bdb6491ff23817079facd139", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.5.tgz", "fileCount": 7, "integrity": "sha512-fkGb1ERF72SoMzlWGu0wnRaFlKTJ7KbroOgeAnmi3sB5m7xi9U1aYf53vYuOP/LqHyD1ksDlXMIvphQdA+2S0g==", "signatures": [{"sig": "MEUCIHEEtU1wGZ9d1CAuXyfOIRmcLf//H6iIWsKad1AD86iUAiEA3iFxyBhtpaYqszKD+KUaHTvl2zTkCO/0Yc/NHDRpIBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTYDiCRA9TVsSAnZWagAAkX8P/0IqcFj+dholo2MtsgpC\nehcZu1YdutQ+BeNPnLcqXt3BUB4bRvc52e1qZ2myGPtvbYfmc5ijMyLtDxY0\ncrKrTn7s5c6fsRpmtA+UAgONXv11ltXY/8/Xt+O2CSj6yYyxvDpWtbd1GsR7\nnV0uMCIvGN3Jg6dbkvQcBTN0im1fy1rNV5DUXtq+p1FxAihMj5uvbzjBkGC2\nk4zltiQC6xDQWxt6ExRP5sbEHP+tgZFGu748F0ebb7aV4aBzWCAzeOTQ1Wpl\n/31sRLJCr1BXCzQyUO4RfnzWqwMxhaTVC6Ve0iGsL+TUc4LG1EHRuNq6020t\nfv8p0MeUMPxHUnoJLCTgVSwi2C0ybrny+a82bQKS5KFwFjDzJ4EKAnY9JJpR\nvMGc/XplNqnEVrkdwtlkCeTnz0TRorp8iy2iwbZKjOTvV6aASfbFSQAmtvyw\nJcTaJgscx5yHQFefD1VoUkCNBamlYwhWgZWLWvwAmKQlOwgt0WcqaORzpHhV\nuPfA18T5LcZw+vQr5KvxQWBJAuUT/QOwPL/2cx/P9ZlEp4osPvGsSRzxgzaw\nBlR0E2GmOZY3OEA0UP9DMQ2Le8Z9L8PncCtejeHcjNNGv4WbVjLRAwHtuCFP\nJiBvS6CSd7B0Hlx9X2X0IH91OJiDSkvBRNl+0T9Ln6evgZgaug8MMEHe7dDP\nf7Rb\r\n=d5It\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "84d6f5b367b9044dcf25d8539a638531effb5add", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"core-js-compat": "^3.6.4", "@babel/helper-define-polyfill-provider": "^0.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.5_1598914783526_0.3188511857051204", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "5c8a634f4c9634ae658e7995bc4ca3bf9988e79a", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.6.tgz", "fileCount": 7, "integrity": "sha512-aX9T32mkCqe1d+OFLQkvWz+o25cqsDOlYIogxRWMt8+kXJ/N8DznseOtHBemfTaY0j0ICyqbU3sSdZU816R2eg==", "signatures": [{"sig": "MEUCIQCzcPqObHxRBIotZbzRB8FCOmKV9YOCuWPe8x0OMlH1UQIgQVRRq47g6O4lJOHImPLdv6yr/84o8+cHPt/j0VSuwqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeFhrCRA9TVsSAnZWagAAJvkQAJPGLoad0W/2wefLMMAK\nqiXg4W/h0DpZk6fXPflI0703uG055ucRSex1CUXq3iCU4CR2i0Vzye5k1MsI\njlx2gwHwFl3Dw9zuPz6txcauctyafDafmkH1O/zLmEZGa4iedowrntHxIOB0\nhu3bwmNf5Vgyf01SHFpHTmSVG/XQQDdX1IwTSlti9OATJdQWVJCZlF/7cm8R\nEDVdXspdIH1wF2UpDvfcGJElTdQzAOUMv3gz1Z4ORWz2zBFtgMNnoWPUyqKm\n+d2uHxtYxz5fPP/Mld9k/uUySh+0e3E/u7lMVn9cEL1k1OFFSOI0vmlZ2DIv\nKV6W+bHZ3IgviAwmx2lWQimUFMZ4LU0Gg9Cy57PjhnCNNbf0Tk+UZ1Wo/2Ey\nH5eOXb2nioF/H3h2ffI/FR4LvjyKv7hQJW+L5HbMN+RZhmnVpyu+sGblKc/V\nxHIfkBFmKF9KPeLOsX9cvfIXmgPmHaO666tKfS0h57tEMmvmDuKccNXgiUQx\nhZT7Gkc5dqTgV2+zdYbRzvHUJDb84r7VuRHJim0n1x2ffhPDv6QkDaiL2NsG\ngCkXXHGBHCPdZOoyV0us2JdQo2cJM9cIMS13V3i3Fk9SFMr+4l2BCMG2CB5T\n4vmMAC1smLlDF6bY7JboW6N2lyYbDOhp+vuZUes+YebZ4JlQZ3oDCJM9qODk\n0Ztr\r\n=6+CB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a3e08229b65e7224eee3d822322576ec922a79f9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.10.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.10.1", "dependencies": {"core-js-compat": "^3.6.4", "@babel/helper-define-polyfill-provider": "^0.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.6_1601722475142_0.3850628620093721", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.7", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "624aa59510c2db41df5b79de1d304b9b05e565ef", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.7.tgz", "fileCount": 7, "integrity": "sha512-aCTLXh9sTc+ZC3GFi1buLDkmXGaeenTzGZjXcD5b3Zqf1xhgoW4vX6H0QZUGW/u9QjKPafeiZSkaiVjL/U1uzg==", "signatures": [{"sig": "MEUCIQCQ9kpVwevJFooqjCCb1wtMq+upif1Ao2vJmz27sZ9xLgIgPYp3Afj5bGLRo15wJIORyzLeJqTtU+n2Tvp4q/kdMcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqVQpCRA9TVsSAnZWagAAls4QAJkxVv7w3J0UzQmf6Xic\npy+DpbFMNCz4z9pUqskxSyYYDvJZ2I51Zi0V8USGEQHSjW/kUGZFdhUQIQj8\n5FxI59htnfLdDZTEsV5ZlZbTyk2tDZBoIR+5gHelCfc8u+7cBNgz2DWgSFZ9\nG04X0SH6qb24bFxjWEHyYxKKpzhS1In/ThYDBSVtql/tqB7nw9ZTQIqRkT7b\nzwzBfBQggNq7aQUX7D5eEDvMVAKhHxtLiFSgytvTCm2+RdqZeLlRbxJenkCx\nUAI+KT6biuSwzSiHUl+iYKt1gW9XuivlIxMHQgkzRSQPCeYoUjiDceGmS29q\npQPRKp5aEmnulIG/PYfZOD2SZDwRo62QhDFBWspetaddu+C5l0evoVkQ4zT4\nLcQ9CacmIOdvD0abmIx0XtyAPalHXhKRoVOqMlk3tjc7jAtq17fJ1T7DfsSj\ng8AJmdC3KqGOF2irRBtAATlmQfPfRNhr9mpY7Aw0c9o+Rj3ky8xAe85eHHy5\ns4gsCB/8eMWrvyABYCT66i7AM21Ec6A2tX+0WDc19H/wLoGVaHzzT9PYk23v\nJUjA0+kM0Z/NR1n6dYl4WxNaUB9Hoih8vXspKtaAphz+PjQaP9V2Ij6HgZCy\nT5WbzgZYjQqqX9/BPTzQEnCKgJg0x8koZDl+6mpLMKKVhi9kOW0RpjOgkOlT\nOGUC\r\n=0h4M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ab1ebbd308b1111920327d95dc4c66a5ae4518f5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.1.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.1.0", "dependencies": {"core-js-compat": "^3.7.0", "@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.6.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.7_1604932649393_0.3293069319019548", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.8", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e6d336b742bd6ad713b59a91ea827bfb966505d0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.8.tgz", "fileCount": 7, "integrity": "sha512-0h0iZJ372B9R05Zs52LjSajDPCHQ7Lj3CSHtHVHUq4qjnF5riMikt6jogYGbuEByjUZclRPMPmeT27nw+qXPrw==", "signatures": [{"sig": "MEUCIGDhUYDaIyVGHcjF9sWgkv7NYNaZuAi2f2rs+UiZL/JHAiEAvScXQjihMopWzvap5mxGvMY+XV6pZb14QqArIk4jrug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VckCRA9TVsSAnZWagAADdcP/RIQRl0JbHFPEJ4O3C8Q\n3Sh92T+dhS9T8aokBcPQhmsqe5hfO3zKE9N76tmqI+MEeG+8+/lhKlEGWSV1\ngI1YRNetlyZY7O+uMPjmwi8L4W5Wm/A4nTcyxy/a49MvQGqH/fI/4/zQDIyM\nwKppgkFJVL3kAhjvINkU178gcnJwQdk8ypMol9q6oLe8DyAf8cJRN10nnNqR\nng8u2yh3C/lix9UotlvCyn7qd8waQBrbwMUKMT0jCHIdl8emcywEDWlPpk57\nS7TBgAdwWXoeIO8stKXmxJ1FyUkitS67Tl82fG529uuwCEyFbgBg1jMzQr3c\neLdinvBNq7prW88QUcFNrDtzGF1YDCmBX7rLId9HvjnM1nZ5mj27QPmaQdKk\n36Ej5FiB3FFBx3JZWIXO68mVaykTKgcLR+ARk9Hy5d4bfGrSmC+tHJPSNX+N\nv20mA93HMX0P8+QWxVOGJDWUn84MQmRzIuvk+c0yAy82kNWFA9EIax2oP0eI\nq58qClE8R2pPLWszM2GeE5vqYyGPj8DTNiFrIlJzxaqWywqzVostsjPRr1PV\nqZovfoxKtWmnl+Dxbl+KDDGFv8X39WQxcBPWKaV/g6ATQbUB6WTbNadrH30O\nCc6dew8AsoKCayb6xYSEcq8PrtQzDe0MEVwPMuDoxAcZH+UP+O8apoTdjs0b\nUsQx\r\n=IN+g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "740427dfa150fbce1cad77602c35cec7cfe69605", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.15.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"core-js-compat": "^3.7.0", "@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.6.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.8_1607554852394_0.6973432610448229", "host": "s3://npm-registry-packages"}}, "0.0.9": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.9", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.9", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e8d0d8a77eced782d71f93b17fa62c69a12db30c", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.9.tgz", "fileCount": 7, "integrity": "sha512-+3xhfNXQNR6mNbcDR78H8HH425mgW6cNvrhgOalr9LlqDkrs86QXWEvkmiG6BsiCO90DXSx46YUAG2IL2mVj+A==", "signatures": [{"sig": "MEUCIQDx1Onjhe7sOgg8GM+GTEzIrT20QseKUAYJIeG79Ox4vgIgRvxPgY5F8fO69nRedHDayI88VY0IKSZK6DkuJHDEUxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UOfCRA9TVsSAnZWagAAj1sP/1m4UOeSqkMgIW0vXpDV\nuPMeM9wQV48Y6sp6OjrzpwQQQlsyu3J2qaJjR5HjWCO9GwtCJvjXDMPLKPYO\ne30PEfkf4QZxSVYIVML64ZoTX91UvpWjz89xVMju2oSlZpwTJgUnm+O4kQ/+\n7Svekid/t6V8AyQzoeH67MXz0Wkg9qGtU5WfYcg9bIVBEMazJwOPwAU3V2KI\nID6Wyf6JRdqD01dIUP84ZuAdn5yHKA05EQxK5/2BhJLux3XLa1GqzuptW1JG\nXt0zhsJ0H9cGLPU7i8EKqS/qF8LcYgR6g7CxB1A4UA6EQCD1BjOlqShEN6Lk\nOt+ji14xm6ezl1rZfXggNQ6XuPqT1AEiHAVBSHvY21yCGjREXdoABLpIbMkq\nHl7MGUMYMtHflwhyhdq6uHfoweTNGnVby0Q+lqhYR3ldKGqHjiqn2jKzwNP5\nWkfKYedzSb+QsyNOba8/wqmf7Ul5/7mjGdEZr99COynDRnOdFlYkQG+ukjtI\nJHjbFHdvE7+VixKuInBlkV3BQU5lxbrpyNXw7cF901PvfUNtkOjLGMOEqtWF\n32tkpfLy/vkH22kg8DZoSl2nn13chTtLIVyMx8mOApXrzKgd5A1rchSzDvPH\nyBvF0eDTHGv6+Ua2yGitV+azfgSc0Hvho40WBOgo6JRlzVe4a9z9Yhfy7yA5\nbN/4\r\n=+qgj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "04e366d5948db64ebe1eae82c3df42bb062c5071", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"core-js-compat": "^3.7.0", "@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.6.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.9_1608598431514_0.23753741260028338", "host": "s3://npm-registry-packages"}}, "0.0.10": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.10", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.10", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "64113c495e25e177cb418ad4e237780d3484d0e0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.10.tgz", "fileCount": 7, "integrity": "sha512-4NF0Q3NViMKWVE4zhQfDH9rcZ3XfQhVxoJdQrIqbffP5ELxq9G3zETPkrT6B7PTxTG+S620a7bt11uJWO1dU+g==", "signatures": [{"sig": "MEYCIQC9j9APfUZO3zAWd/BzDoFTsv6UDm9kfpHH42wpPnokOAIhALJjUSaDJYQoA7rC3a2pKZ77YW7u8u4hiZUonCNn9o4b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UWJCRA9TVsSAnZWagAAeVoQAJlyxT/LpQfnbZzMgWcD\nWIxjS6fDWQ0r7XSTq6FUnvirELJnYHdOcsx01P+QbgrKmGQU3UxauYbBwR1V\nxuI/abRUZRhwS/X+m/0aJ+0s/oHN6Lwt6G8cyNBgKTuIpMgebq3QdGGLz94N\nlc/DPOkLo2CPeud68HK5LoIANQx4qSr0eh04aXQAy7pynvSkapbPL4r5nmbD\n54x9y9D7qjr9AMqhKaYspcsaCBSeF1p+EP/MDiWeKAW2Uw/dqKagyAKNNJ1d\njyNgouptMS0GEnu+TG/4vyiXAFWZpFilDcV+Xh2b9A8cyYV8J4dAnSr4dvmI\nWeC9KHzXJbaGWAI0zJ1b/rIQfcUD7XTkA6/AoMOwVMnjM0/NZgChiLa0m1qJ\nVW8UmKVIzNqAgkF7qTL8szztXn3nY8kEtuf2EhOFPV4ANTPX5Uo51h9iQlxp\nFmSPj91YZ8osUU1tSR4i3RyNYLQtaPbgJgMQGRKq17dCm2yL1j1vOztYc5Tg\nUJgbzhZFKX6s7/2OXtyhhYj5uPZ/17ofN54h8u02tUK6F3OnZhryt45UBR5h\nTnszZ/xflFqFqr46uu8Zilf26EzRzyebmC2AeqJrvwAMIXbjMjLMQOJsxNzk\n7QsNRwaBm1ayU9+dmijCBE1Ff3emmJTZD7cXOZA67zlVaVKbnxZc409DgItN\nF69X\r\n=NJZR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "762a528b570a87fad72ebc24e7565b354b4bfbba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"core-js-compat": "^3.7.0", "@babel/helper-define-polyfill-provider": "^0.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.6.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.10_1608598921455_0.8115042640994286", "host": "s3://npm-registry-packages"}}, "0.0.11": {"name": "babel-plugin-polyfill-corejs3", "version": "0.0.11", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.0.11", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "5dc48d77fef2758368e7c15f3fcb72d06a50abf9", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.0.11.tgz", "fileCount": 7, "integrity": "sha512-fBqVJl/E464HiSxEO8KsTH9xyUtKQ8p68aS/hdD/de3jAB5MqK6QwyVI+KRoU/bsi438dD28d3IkhLCTb0DqTw==", "signatures": [{"sig": "MEYCIQDmGC4jsDHz5gK/fKDci0ThWzs6ISUihWn9xMwYSczsRQIhAK7F7uLuwf4aPvDmlb6vNfYZZOfg9J9VV0bI9RNE4cY7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9yNfCRA9TVsSAnZWagAATCYP/3OGPWYnsop3baNlecaW\nE4tKeKFNbUy2X6Rs93SjsOgsG3Tvz9oNlWKLfuume+r4QTv9Chm/O2LNFWFG\nc0xT//PuzhG6yIOElPs46tZwtvk2bOJ2/tE/1qc2Qx/uX1wpzz40i+q134TJ\nDSv0EofgiiBw/rq9xalz3nDy+YyjEmu4qhbmlzV0Iwn0McNs1toxb8rrDVvq\nODJ0/pLtdJIoqTEIJGqfdVALFq0f8WAkxtNtv/z7FqSW0u0PocK5ucFgzKpO\nOUkQPgSxDgyKvbUwU1N3vu9houOX1YjCT8aatD+npn1T4ws2PSCfAehZ4R7m\ngIiQ8HJ6jUbtAtIplil9NKrfFLV9T+q6rOPM6MA2fjUHbPuznI0SfhSO5SUY\ngR7y95g1r4Pm1rV5GeqikuJdf/Tk+TG0hlwqgolAEfajLMwonGiv/kN3KRcK\n0A3gxluLye3Tgnejj12+oP6yNEGkw/qDlZuMeOMy8BLVTcKtEM5GYHsz1Ag8\n0DtijhENdzIRVaYUlY9m4Biv5YpKTByduXskJStzfNeN9suX/XH6I3Cc3l+X\niFPQ+aoaXiaqjQxDbOPNGrD8Gn0Dmhz4Jo5bl9lC/HcW0h5PE/0s+2btBa7s\nZcxd5WswmSNkjcxIS/h8/E8p4sVPnEAda9f/jWF4XU9mZhU9hHzKFLpr7QHM\nh99u\r\n=rs1l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "bb9300ed54b6cc33e09a89764efbb2bd2949ccdf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.0.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.0.11_1610031967187_0.5073834189257795", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "22c4b0c058f1a13b11db72dfe8df01f320c91264", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-1xoNvGbuJVXVRLyalANHp4xPMvlIW5+Gme7QHNZNIXCRZ8YUY1muQjaDkJTAgbkxFvtnALGtnk/v6RfrgKTpGg==", "signatures": [{"sig": "MEQCIHVBIKWZekKORn4rWzMMg7Lj3pIERgl/opsY6U3nRzCzAiBGAWd51nbw+TKVkIaR07CtCNA6H5EnMNlqrI42u4rFrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+bkaCRA9TVsSAnZWagAAVeoQAJzAFQV9/4VmXG8QYmWL\nijZ2Bbm2XXsag9kojhh8yOmQPzBmpqzeiJarhPgwfkJ73wu1lIy5ponI3p4a\n1V6NY3Abqy7h+xIfm8tMkUJ1DiSiEoBWZt7VYtqr7YFqYAq22pRv29LRIHt4\neX8b4wtNOdbXVSv9MzAOKnQTSq727S6Xw4F1QjQuiwV8IfTLQm54jjh5MHqz\nRQyczxoJNjxiaFbgxhgVX/GKQZafXL7s3BaT01DGFJu/Gdtn8e2GUcH9VHsv\n0Rcjl0s4VzIb2xLq15VaKhmU3+Zb3I88VbOkjpQFHID8nFVqV3Lo1b97bNpX\nfv2IgA8bgEJo8DyiMs9L1hP106cNMqQ0dFIr6Mz9/pvowQA4o+USO2BqGILE\nmYnfQhJMks2ck05n7OJF1zQ4yZp7gzQu7QrwdQb1ByMynckJHLxRxP/AaQ+0\nja8uFXa94knl9maQx5Eiw8WRRLIgf4rvevCm+n+N/bdKAwh0u4KwYyZStx1t\n1FMuLJQ8AhkkIpVWDZ8ia0vaaBhW7zg4AfQEnNfPcn+t0KW7AwWC1DDESyRf\nzPPmnNZmfjF/bppShyS2MZwaDx7xazyalC8/dSBGDnRcASdiFDv7xR585vZF\nsRo2WLD/4WIXXkpKXkJ6+WgMAKnBwN11w/YJGIXayjLGhu4XRk6eVVxw5ets\nE2ye\r\n=ED9p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "440228dfbaff5d0632a02f295002579fe862e614", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.0_1610201370037_0.1130489316546388", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "4c0256c433a019c61bd394ce4ae0b3264358f742", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.1.tgz", "fileCount": 7, "integrity": "sha512-HgEgILx6Kyuj7YwLCgujZecvXh3g/O/9jKGtYZ/EZAJD1V175FF6ynyorKFBZhyo79+YYKYsoCP8C7Ny71ZZ/A==", "signatures": [{"sig": "MEUCIQCi3p5+sbwbR5vKvNpeeFBPf2ApN3ZWOVfn8C7LW3aLUQIgKcFQh6jiU2PyO9J5HxBXuQwR7qd4iFIGV0cVRtrQOb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMwFqCRA9TVsSAnZWagAAbz8P/iODIw1tPXx2z3HXxADk\nB2FDIfj+OqxJqY72r2PQ7dCs81BDTrsPq1jT7fLXgYfmeUoiOkUyKpZK1BoL\npPQ6sbNppYEIAqmNpp6fv5kI4ZEa7CVb/niTGO7jNVY1yuQdNT1oPpmVLipO\nYHunqk/hCkj9BwoJG+F4QYAdhppDeHomxdDlUZicZgRSVbTKr4PJdE1UIEpx\nMP2ZLvQrgDBg2sNiXdH7WwI+27nhcgsRaMqh/5S4k0UIrbnydrbtsrlzXSQ1\nbxtYvt0W3Ie+HCcLsCxOkN4CqtUgCrAUPytsviLq0e6TqzIgq4dSs6C3vTTF\nFXog9otEXS7sKsxr5tKtaA//Q/lm4aNbRQkd//4kf9E9n5vwpu7Fk/rpSmQ7\ndhHvGcluLL8hVGbhGU00bBQLk4J6DHP0bY9U5L3Dff0bTIhIawrCjybJLvga\nlxUpJDvokWRQd+hlTv6k3/gx+BwAW+5A9ggApLDTwNBFwMpFZtF+ESP+KUa/\nu1es9LoX7FSBlK7/wDBZc+kGzJhm5rQ0cuKY2x7aZHbgQ6KEbWOKJ3UvlH5Z\n03TF2yuUR/moSr/HEh4HWtO0HMiWalsPhmel879kMhkytWDMTjIatvrHEVfg\nVPDNyjnRqOGk708pvMrv7DLpdI8pQCIVY5movbAO84qn3aUlroti5Rlaoxxr\n01fA\r\n=xasD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "57edac90ba049d5ac2386cfaa0be131a68030d21", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.1_1613955433553_0.3622342249210677", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "39b0d46744360738237bd54052af852c84d74ebf", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.2.tgz", "fileCount": 7, "integrity": "sha512-7neRvPKcdvZ4X9IpYBCmT+ceVUKC1idX7DJN5sRtiPP9d0ABD6k5x/QvXTiz/9wMp86FBDX6Gz0g7WQY2lRfgw==", "signatures": [{"sig": "MEUCIEoLlxRS7nuHGgwPMIYQjlvzfx6uLiR32vl/aP526VX6AiEAkKV0WJHAIttaHj0X1dya6yDQXLqp6pQ+A7ZMM6Vos3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNB6TCRA9TVsSAnZWagAA2FIP/iMxaUp0IG9BCQGj0Ns/\n8naGGKbvFUhcxSQiFsjiMiqnTe0MBiTKW2oE5hlctdO1Tp4ETtQ37okm4LV5\nLXqAGoPo9HQlRmXcFBQtIQQGy5S3CUaSRMbQTjj4vI8iZta1kdlm2UsMF2KZ\nu33vodCU/Rndj8On1ulLCNvpOQSck/TJrEDXbNCEsjG5ONJAxwOESsa3wZa6\ns3TpGQj2WIen0SWq506Zx+N3XwlA2G8Om43iKi3YMkC9ayiJRxG77sLb3u6H\nLU5zbBokUa1WbjAo8g7uAxus9O64zd8QQNzzNFUZ7qPeNmd1fmLlYN+QVMbn\nbC230YPW3592RP2oslymL2ShqC6yYBvvP50Gb+vtzQ7vEDeihj69MsnOpxx3\nAlHtUqlK4badNKHkL34dioHGV0plUnMYG3O9Ty4MiVpWlIlwhOGgJ5Gr1+aO\nK0Tk5J+VC6eBbKFVe2asEwYF42QEXzcnWwqRf6FcDffHWI+51ZjvsUq/SalZ\n6mLqIyzSueUnuEE2QYKYEtZt2p4JOIVNkDzKR31CV9OKdEb4KfVQXzAftjWG\nhVXUSV8gWoiyKJr4rxFy13ECGG77Nte1bHZ11hGtajd6Fk51GRJun+1k+U4B\nDslQmz9eI+/i8WrjoiIEeg3GWRcZR9E1mCqlrrFUz1o0SK5JH3k6S/z+iaw+\nmaRs\r\n=iVsL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b1a6b262d1de56486a2ab662ecc25834be6d2ad5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.2_1614028434583_0.48742320029017705", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "3521593030dcb025e3254307d571309fa309e103", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.3.tgz", "fileCount": 7, "integrity": "sha512-c4oUlFXacRysgM0B8Ir2z/FW9FbuGvfyLSTeeehBkcaum0b0yqWsvP+LGGKgUhxMJB1FeyIfxUdE4D5Qml5sJA==", "signatures": [{"sig": "MEQCIC8tSqMdxrf/VpkVVdn0mdsoAyyoSetdJJSRavLcoRCoAiBx4GbUF+RFF2MigEKUI8lJf8JyYgdkUM5ueUlBpPopCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNOWmCRA9TVsSAnZWagAAqFgP/RZIFtSqFNnzxtZvNIQF\no26RRD644UBqCokIL2ho3WFjfgcWFwXFbBxU93ALLBn5ob+KFlxD/YKA96mh\niBlORgLvTFf3QUZsqOUFMOF1L1/oaCu/dmnznxMgwNBe8vgOG31VYs1HAXIy\nW9vxfZUOdEIl9kRnqX7rn/ynthAxi5uvCMJwJfs0X7U0NwU3Rhu1cAga8TQZ\nIiRbSIuBgvpa6RlbymKu0n7ti+D3A4k+yrvSLUmcMsTB9R/exq1gHyaBZz4j\nAXUNzKirqp8zYOY8m+pl+EwZMZDAjGxQ1/P3mJYNIwvONilprEeMYnNPPb9t\n4bvgwue1oXgXuveP3264hz0bFfaTXhggZFBmqSNdzPEOVbZdbrXm5vdNZQ+L\nQWhfIbLyCJj/HxQA5mPU+no7IH4A1IkyloH8VGEZLYRjfRbqLvvgEZlTtYB5\nEEPeNy4njqaEbYTRQSkqzguS7GSZTfHh7Ax3mT7JjjbevnzFTsLdl/3tCK2j\nWOIqb3/aNOEcvA0IO/yVXQ1lR46UUpBmvhV302SXCrvYUFdRCw4Faen9IUNB\nXszNosFvvYxv80Njmx1j+wVm3QZLZRag1AqIryWITKyIHkkQ1V05Shcf3sxv\nTkHh4H2fWTdpZFU9M52sacciR/KPjindGDfhxCQYUXgLy8kdtUTfgUNITDWW\nmwWV\r\n=97tD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "187944845010adf2dc1a673af48d0d9e6d06d6b0", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.3_1614079397806_0.6149180298253072", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2ae290200e953bade30907b7a3bebcb696e6c59d", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.4.tgz", "fileCount": 7, "integrity": "sha512-ysSzFn/qM8bvcDAn4mC7pKk85Y5dVaoa9h4u0mHxOEpDzabsseONhUpR7kHxpUinfj1bjU7mUZqD23rMZBoeSg==", "signatures": [{"sig": "MEUCIQCH/UAcSLls5QT2H+L+Thm3bFlo1ffvY0dt6EuxXT4caAIgOW0n7LJBjSDVTBAulsdp3LvX7HrB6laceaWnFurLPEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNQFQCRA9TVsSAnZWagAAqiEQAJ1X85fLoeuQS5SVWucv\n9VsVk4JwhkF17qgjP+H9QI5u5aCZNbGHDicnuOx232Z15St8ZCJAsJFaWO8i\nnaWNvNYvNhT6YeOdmSViYcXMXPLu/PjeRfxBzGXmUER2LHf6t79gLIFPDt6g\nKgFtHzfkXhflTpAKNNezxWh9IVDLLyWWslRPLLXYXknLDYswpgEWUwW6OPi5\nNEVOWUbcEpdV3tyv9qE1hlXaG+8twnY6lWj1mdv/9U53UoHj48Uj7tbeXq6V\n9hVXKPcW7n4pzssSpT3EUmA/ZNu8apilcqYAFkjkv+y+h5mPYy5qGwITHc4v\nYYtPEuisf8nQh6+MFL5MYn6S4faHNzkSPJH2DJCFgLR5JajiaSuy17nKtOCx\n6fmrnhWniY5R3C8UIUzJ5GavQw0CghiH5Azae8sf5BoCRTghMMoKuOeVJmBx\niDQh65dGt9aB6Opanu3iM9KC2VF+UONzDXgPuMJ2g6/Ior4v7szJAEiYgF2z\n0m6c0PmAPbPv/Rri3DLgVN2L5CWXujXFZaODEu/arMeciE2W7u9jZ3DKBZOa\nJdl958kq3qZtRlfzYFR1eaFj09m6Yarp/OVjtltrg736zSNt3HyRRVEIctTB\naZUkrfhEBpSoiMF+WlmfdsRKni8Z8G1m8bbYYUjpZUH2sd1p59mw/2RD2wj2\n3GHG\r\n=pFsJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "55f582c8ebc4b8a181a51fecaa92c89158cc7ac9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.4_1614086479942_0.7256810868904644", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ed1b02fba4885e0892e06094e27865f499758d27", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.6.tgz", "fileCount": 7, "integrity": "sha512-IkYhCxPrjrUWigEmkMDXYzM5iblzKCdCD8cZrSAkQOyhhJm26DcG+Mxbx13QT//Olkpkg/AlRdT2L+Ww4Ciphw==", "signatures": [{"sig": "MEQCIEFnDzeLqPSiQoz1fhA5GD0X2ci7wcD6RMi5X+HxQPerAiB9lSiF+xHR2abhq4cxuABo1RJlbsks+LUImaBH2yX7Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODAoCRA9TVsSAnZWagAAzCwP/j5LA0aTqqHNpxl0qOeM\nyGrRoJIvZbzsP3YXovX1EiPXvgHVuCNl0+f+2FEnq3B0gsE+eFDBywz+pAnO\n8emV/NuZpk4VUXn/cqRKg45LFRvrEE9i14Yoc9ziy8C94Bji+eGyTuHQ5ve+\ntBsnlLDp6iXlMBoXyvogIhsmmBu5FDYKi4l5S02L0hxnyJMzBb074PDYHycR\nGRX31sECnLnXThlOrXQ/UOSwdvTrJ1OiL6KRO/cVvnixFeIUAhpYYqTQ/DwE\nxtR8GOkwdQIztGiXPv6sr14hriWAaohwRFhrY3kUjKR+cNpZN4+m9GeH4o7l\nDo+XafTp0xbJz5HvjgjD9b+E5KykTVYhGfNO6kV4fCQDdh53MKGUnNkk9GdJ\nNXkmluhXyEL7evkRH7j++QxYjpQr7w3WN1etDwAfoJizvCD9XnNb+80p3GZp\nxvJMLTKuO5nvqmOKmQy4OaN6Y7Ksawzzdp8rJTB84GJxszF9yPj8fcOB8J/C\n9e0Ahxxup30APKeMOOnArbteEOinw7QrwaH/MXZw3cbqifqYhIdQIF+N4bgM\n0oXW8xFI+brUi/XJeNSE8NQqirRT0ijReMVzkxHNrxmQ5yWcmANqr8FonXr8\nWB5mOPnz/EcUYW8rCMwDl/Azx7nW+rrVUx0LMKoG/qn8Jafn2uPgJ5qe8o7D\ncSnb\r\n=g7qi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6e6c7d2925b0f512397518d9f5ec35c925ea9960", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.6_1614295080515_0.811263894952766", "host": "s3://npm-registry-packages"}}, "0.1.7": {"name": "babel-plugin-polyfill-corejs3", "version": "0.1.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.1.7", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "80449d9d6f2274912e05d9e182b54816904befd0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.1.7.tgz", "fileCount": 7, "integrity": "sha512-u+gbS9bbPhZWEeyy1oR/YaaSpod/KDT07arZHb80aTpl8H5ZBq+uN1nN9/xtX7jQyfLdPfoqI4Rue/MQSWJquw==", "signatures": [{"sig": "MEQCID9puLrE1tzhvz6G+ffPsAkGOGAS0+Q1KiRsrs4V+BDbAiAjujV1xDIQwM8dKr5VzoZZIPg7zqalQZOJ2N5xfIy3hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPfmbCRA9TVsSAnZWagAAxLIQAIGnWHdcD2WLg5QbVcJ+\nyHl45jCF0IoAYdHo1H7fjvhj5gorfXwRdYTOkPXJYIjviR/Ss+iVeA+QBp8p\naaAS9zDdLzHi8dOTN+fOymCMgQReRnflh+REPYSl6rg8+lfaFkgop/Sw/YD/\n2jx/8VKn2DXVIoF8NzP4S/wTs2LIfxhZsfhUhNrwgqkYU7rh3Pw6T4/nNvjv\noGXNMpcIIN4aEPVLuLVLBvX68b7UklwAJ+E+wLVVD0Pa8LzPdlYxgdh33xSH\n40hog+s/aF4Oovh17mBQy9c34JJmt+NT8vEslJBCMXrU2yO2ZzINnkMqJu7m\nuscKc/uySGJeZWJedkIS+R21miBNNbBmqi3WDHfF5+M0g6FglKQ3dbxdDwWU\ndPtzhvYYejzlPf+RP3SOU7BCRoqaK1ECA89ivt/3oi5vVKOsh5mrMITeWJ/7\nFxAgvbSOGbrS6gxtTpHrWmvWhHcuGKP83qomA6Z46ahysdqCf6Bqj0Y38YTe\nhN70THQu3WXw3F/0+7Y2yBSBoIlKo10FMcUPKhgrPuF25hDwed+SsTgD8pzI\nV+eEP+c53Z470ZpkBJGjy3xM1b1+R0EsxOvVri92y+ewZkDGZn2OYkjXoV+6\nmVlFAFDUvb5RZFQ7cZ4Ky/SlOo27AnRk1Umzq28r3zNxtUp9CYhVybNtnkcl\n1jgi\r\n=b54C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cca90e89debf689aff23e982f8a30bc3b512c573", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v15.10.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "15.10.0", "dependencies": {"core-js-compat": "^3.8.1", "@babel/helper-define-polyfill-provider": "^0.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.1.7_1614674330814_0.6559945205402595", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.2.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f4b4bb7b19329827df36ff56f6e6d367026cb7a2", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.0.tgz", "fileCount": 13, "integrity": "sha512-zZyi7p3BCUyzNxLx8KV61zTINkkV65zVkDAFNZmrTCRVhjo1jAS+YLvDJ9Jgd/w2tsAviCwFHReYfxO3Iql8Yg==", "signatures": [{"sig": "MEUCIQDW3TXWXsRoGC7dCvXBSw4gMmf6gdN7TIpvLRLfzhFSxgIgDx28eResLQRqbC5Zv8/G+K0yN85TQpu3x+vSct6aBUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZJrZCRA9TVsSAnZWagAAsIUP/0hoG/ejWX5quUzm9lfb\nzuqojq8aUsr5EeeJUAtS03tZtYqymaPXgDYI8nOqSmiu40m8xBg19xJWP+K8\neCkniIEQwH2Xw9M1kcPVExF+bsmBr4rmV3MaVS8AoMSfEpLGd1AIdhZ5KkPK\ngehuDcJyOQIiuh2a9TQFVsh1WMt6e7RGQCV9efZgvQHz3RMBGUKN9qRFj7ri\nhbeHqqlZAGegqcsiU+ceUHLj7yxh3MH44uC5klWdTpAWCoXgtympTZpxRbLx\nZesGoY0fpMrjcMC/jaJjs1HEAI/3UjaJekZ/+o/7fEvgoTdfdO/eO4nkESA3\nJtySaPSnAHkSjOZDaUJo2Vrp3tGb04LYQkk9eeVoaiBDPJZP+ypMgtKEP0Qv\nwd7gtHt5N/qXeLAUjKJDr1qfYTNmzrRh7sUOxFjM9UATPRyDsjb0jN6DkoMJ\n5zRnKIRDQFZdVPb13UD7TLJQ7PasS5KD1MM6+aocwOAWM5xNp4CtnaZAYijR\nJrjmravokPt9mAmzVwP8hOKGJbjSVD1e+ltYl+tNdb3XrK0J06Ipps72B4Q1\n8Vpcz/QNIcVwdiGQiIORWRkBscVIge4NZ0JXEEdHEvZXaXPOH9dbCeh1XxW9\njqUfF+POlPzmqxEAufsc5Fiq/luxP6R4+uETrMlI02r1Ma9WL5dOtGs5ARE7\nJiBd\r\n=C+R2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "841e713e0002eb8aa167553fc43840e526fa2d8c", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"core-js-compat": "^3.9.1", "@babel/helper-define-polyfill-provider": "^0.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.2.0_1617205977412_0.5202822319036158", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.2.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.2.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "786f40218040030f0edecfd48e6e59f1ee9bef53", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.1.tgz", "fileCount": 13, "integrity": "sha512-WZCqF3DLUhdTD/P381MDJfuP18hdCZ+iqJ+wHtzhWENpsiof284JJ1tMQg1CE+hfCWyG48F7e5gDMk2c3Laz7w==", "signatures": [{"sig": "MEQCIEF4AFBsTsncdYHaZxky5Jpq4FLRA81Nrc/pZPP2HSEeAiBj7zYYzU+kf8CESoiJala78xinzBE2bVedRJOiAQbdaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqDI9CRA9TVsSAnZWagAAjI0P/j9EYf6jCLRl4WDJ1nMh\nprGsygKXhnHxwCHD+yUOfnA+XDF4BqWekGEf8v3VeVBl2ML9LkdCgFijuByz\n5Wr7pV8WOFQCtjLEybJ0gMyoS9WHnkUOnuouk29gOj4bkDFtwFl5PnyGor0g\nbCDu5SDy1HNq+6P3CSc+CUcgTCI7rR+Bl7ZKTKFSPLnvkgiSySKcYNYlVGRb\no64Dk6q23bDEr22U90Iu97oowbFhffoJJqVa7In3bZVbibXYd9O5pX+uuzgC\nDBaxeNF4fOEdgyZS8U/Wx3vzSncJvOSvcxicrX7HG6J+J19mdIv9gLMiHjP/\naQpd2UOm1QaMKVwHetdBz3xt7J+6tBAOjJe5WQjby1chbxn0CbyrhKh+5tvA\n01UEUkQ3ca7WRnA72CfVzgKBNgChhs7s7iTJHTezfvnKd68R/07bdrIMdEb0\neFMCEISBTzOX4QAdOJRWFlqwvV4iP1BiHm8OGPYgZ2lZvPGlrVN/xlWj1y5c\nB1r54APrPCcv+hWYcVIY/LtgSp9JmQhyS6AoQNJknRNyHF152UXmKgxU322A\ndRB0R1DZC1RAkQNJhNmWGNhC26ApCT4/00hj9cRI8+iSPl9DFcQPqkg2TesP\nLVcnWkb9+qfXKaIvy6RCMaZTdXUORAi0P1Jy1wCJLZZVWbAHFgAKyFfEPZLR\nkN5i\r\n=+t/6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "a82aa2032d84ece15914846fde5f1544701b0a04", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.1.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"core-js-compat": "^3.9.1", "@babel/helper-define-polyfill-provider": "^0.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.2.1_1621635644756_0.3231303433912389", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "babel-plugin-polyfill-corejs3", "version": "0.2.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.2.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "7424a1682ee44baec817327710b1b094e5f8f7f5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.2.tgz", "fileCount": 13, "integrity": "sha512-l1Cf8PKk12eEk5QP/NQ6TH8A1pee6wWDJ96WjxrMXFLHLOBFzYM4moG80HFgduVhTqAFez4alnZKEhP/bYHg0A==", "signatures": [{"sig": "MEUCIGmljcawM26Uicxr1zCeVxVoRY/ViY6X3AW2u/zTMvQUAiEAjA8qZtBmDhFAY23xGvAXh3cpDEucz3HCk278fNGCP6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrh6YCRA9TVsSAnZWagAAfwkP/iXmbhJ60V4TW2SW1089\n2K2T8a5sQnf/+3ebEfTb72B5SzZ8o4DXIwp16q674+TclF2L9si2YSBqux1O\n7CEgFySYmvXOgXSqtPXMBWfXrqsZQLB8XOrfqLjg3Sx7NSd7hB3RBYU+3gi8\n5e/M2KWgzu4p+9hjJTUzEQMVJeU2XfRAW76WNgIeheVLR2s+2yqGxiy/tnOg\nB87UU5riLb5HAaU86g6ToWrBQNGbeu5MKo8B9lKLzmzmPDoH68s563Gwz2mf\n/k3VMDLzKgRBG0CobpbFTDr/KSJYyCOVrSuBkZ5YvGVa2tHu/CMXNoeNG0EC\nX8rqXO96x8ryj2mB8hFvg11HmCA4IAR1zcfu5IFzCs/j/kPLALmDzVGuVDf0\nA87TJPfGSoYGDR8CIOZJE+eb1iAhVlwrlsMFSVMYgueqYonL4cSxkpnEGXjm\n227JWRvEsW3QBf5kpOhBzxp/ZnZ8mJXu5pJ34xqYdUaWZMOeP2xRW3PG03ot\nP9UZfJIuiAlDiJFr9hdQraWNnoUxhWgxLEF4b18Es1mpGfkQ38PWmdVB/l51\nL/hLMEXHwFwOGkQzZoZwJ1U+t5z2yIvwsU5Y4joP3quf4/6Z10iVTLUXmBsX\ncLtXfou4DiTZnsf6Xhsp5LTqBeK/mp+eVKV/YyygrewgMjIgf8su5F8e9sqS\nZmH1\r\n=nOC1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1db1e16a7e6855094c52a6cf9b98410e3f0e80de", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.2.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"core-js-compat": "^3.9.1", "@babel/helper-define-polyfill-provider": "^0.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.2.2_1622023831605_0.8595221990879927", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "babel-plugin-polyfill-corejs3", "version": "0.2.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.2.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "72add68cf08a8bf139ba6e6dfc0b1d504098e57b", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.3.tgz", "fileCount": 13, "integrity": "sha512-rCOFzEIJpJEAU14XCcV/erIf/wZQMmMT5l5vXOpL5uoznyOGfDIjPj6FVytMvtzaKSTSVKouOCTPJ5OMUZH30g==", "signatures": [{"sig": "MEUCIQCCvKo0KocS0sbByWQ/nC//bm4A2pjpV4tA1BOlW7bo7QIgW+SyoWnjWjvLCdSjI+ajNKEvFAaJOxxn5/qZX+hjpk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgymltCRA9TVsSAnZWagAABjgP/2tbqWe+C1xjGV1IyIuT\nGvpYMhLEv/Nm45LvZsJleQZ2CQjMQxlIsWP0Rz7/tS+qC46xXzzvKOPv0fUF\nguS/WYQDNRCDpCHQ1v+QPdEWZxuuk9bfWo/Z24gJUxUD3bRJk2kokMOpxcOr\n8GO0S63IoW7CM1W6l8xKE9qwKWa/Vr2w+EeOCqoMG7Zhrw0/5QjGr9aFUE8T\nsLwqjqPzc8dfTn7C9px9L3Gvmd05hArKSysxVQPYDCmj4qu0ve6AJxgdky2y\nPd3b51whYDjmZz7cm5fMZWJpBOpet1+0obrhSiAsLmcPDlSXoXQQpujo5CXy\nQY+T9PsPLtGKnPPf+AjTSeLn/CXI3ex3jA75Ufnk7tZ+5PNeR3gubdYijhE1\nhjuZi3aJ0HejG4qD07RSQtC8TuIn5ZJ8mEQ33IMyz6/8CVIgKCEhOio1TFcs\nux3kR7XkQotibgpbgpzUxGior2GjWDMiy+wnZOtkkNmbwm4Fvn7FnjaB9SuH\nQD5Rn4nb5crRKh+s7TtP1N4HXHMuHAM1GXFAon+zoLs0FGZe3snv/EDNZGva\nVIZHm4oNCgKQJE0AXXIp1zJdk7gSMOZvs3Zk/T44yZX1hbjqaePysbDnz8yd\nQryHp8TV/v1+ISXPEM3a4vyZjKCVZrU0JJM0r+kgbBZqNFegoeMiX5gnZDhL\n4JED\r\n=rMWU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "c54d3ee41e8e43445aa078f76894ecc50c054351", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.2.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"core-js-compat": "^3.14.0", "@babel/helper-define-polyfill-provider": "^0.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.2.3_1623877996995_0.8343043444518059", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "babel-plugin-polyfill-corejs3", "version": "0.2.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.2.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "68cb81316b0e8d9d721a92e0009ec6ecd4cd2ca9", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.4.tgz", "fileCount": 13, "integrity": "sha512-z3HnJE5TY/j4EFEa/qpQMSbcUJZ5JQi+3UFjXzn6pQCmIKc5Ug5j98SuYyH+m4xQnvKlMDIW4plLfgyVnd0IcQ==", "signatures": [{"sig": "MEQCICZ+QXyE1o8Tu7pvNUogURwr2IpV4N7urEX1Rqhq/QrHAiBM3GZMnYAsisT4V/85azbfBUE8PPNSSGXrB09fMYS+4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAbY5CRA9TVsSAnZWagAAXaUP/16Nnt2ZCohzjM2XovCj\nq/aIzOC08V7dCPkjrqRcBrwxIs7F51vfBl7lV5e+bhAtLY/w8P5aaYOFtvl8\nTX0xPUVRpbPuqRFcQyngm8LprKQMWiuJuSYtQxuB7le1yo7su4d9118x58hu\nclHPY0LGPPApEFfn7hXCvY4/LqZKwRHDHg17uCB2wAMq7Hw5WzxWthrtX8Mk\nIlzZpcamignSVpibbTyk7b6tCVJqCkNYhjbxz5u9stu8MmGh+8+FqTADpv4v\nKnCo2tpkUw8D6T42Bqzzkl75CfO0BNH5h44CqZFrAPqSIYOZylRY5igBMCoH\nz4o2ySdMOD45VFAbR9QTJM5hwf3bEx6VBt3WpX0GR9sdm2mtIf4tPTs3NA76\n8EAYPk04+88wcFUaYVGB7JLvEh8eu126+md+J3HQyCZYUKuqEejAx4Q8B11W\nSa3aMjuNQAsuykZcK1qYpU4P29+IqQ6ut8/OJ/pqkieI0GUFquuTSNRRsgFz\n/+ATtc4t504VcLJaeVt7GX2RJG5od/8M8DCUkS8GwU84cvfeoWbhNS/e1N2x\nf1U+B5YNfl3yRsfihQp9DTYBbazm/5dfyeKy6RuYn8/C0AgcAiQwzXOMeuT4\nxSlBrQ/w2Fyag5K2zfETGFbV2L4pIbk3WvvYGCmdprDya9alWttkYiPXLaRP\nx435\r\n=gyrK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "174ac7f25c504a8218956e7be66b691a631a5bb8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.3.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {"core-js-compat": "^3.14.0", "@babel/helper-define-polyfill-provider": "^0.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.2.4_1627502137404_0.14674999732122673", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "babel-plugin-polyfill-corejs3", "version": "0.2.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.2.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2779846a16a1652244ae268b1e906ada107faf92", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.5.tgz", "fileCount": 13, "integrity": "sha512-ninF5MQNwAX9Z7c9ED+H2pGt1mXdP4TqzlHKyPIYmJIYz0N+++uwdM7RnJukklhzJ54Q84vA4ZJkgs7lu5vqcw==", "signatures": [{"sig": "MEUCIQDOfr/J13y7ZCD7NfzXO4pYXK6qkFxbfsZELimDEOY13AIgEx5Xlh+Pqmy9biw+oCPuIyaYhXFdB8fbOQop6X42TsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132729}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "a8e0ddc77ee775ced7f441d723f8b66c6d631f30", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.8.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"core-js-compat": "^3.16.2", "@babel/helper-define-polyfill-provider": "^0.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.2.5_1632330509392_0.06438279578331274", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.3.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.3.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "fa7ca3d1ee9ddc6193600ffb632c9785d54918af", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.3.0.tgz", "fileCount": 13, "integrity": "sha512-JLwi9vloVdXLjzACL80j24bG6/T1gYxwowG44dg6HN/7aTPdyPbJJidf6ajoA3RPHHtW0j9KMrSOLpIZpAnPpg==", "signatures": [{"sig": "MEQCIEqHfVj8qs+bDMZ/JWwGQ2nWSZlZH/cggyZMIpxL9EZZAiBciQlVjtQ1TwyJPcdK0TtCD0jkBlASgsL+AGId3LGc6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151781}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "de48880e867791eaa94c00ce7132dc259f3c777b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"core-js-compat": "^3.18.0", "@babel/helper-define-polyfill-provider": "^0.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "core-js-pure": "^3.8.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.3.0_1635546017950_0.4952098766675328", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.4.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "0b571f4cf3d67f911512f5c04842a7b8e8263087", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.4.0.tgz", "fileCount": 13, "integrity": "sha512-YxFreYwUfglYKdLUGvIF2nJEsGwj+RhWSX/ije3D2vQPOXuyMLMtg/cCGMDpOA7Nd+MwlNdnGODbd2EwUZPlsw==", "signatures": [{"sig": "MEUCIG7Ry7FMmTDQqaM1SRcCr98PDmXxCrcmcVZ4VrgLAfWrAiEAuA3SYrw87qYRPy60goiW9C2S2s0wyXGF/+ZZVRPwzkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151354}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "b2688bbffcae0f0c6ef5cd6ddf1abacc574060a1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"core-js-compat": "^3.18.0", "@babel/helper-define-polyfill-provider": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.9.1", "@babel/core": "^7.13.0", "core-js-pure": "^3.9.1", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.4.0_1636799701162_0.5690093792974336", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.5.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f81371be3fe499d39e074e272a1ef86533f3d268", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.0.tgz", "fileCount": 13, "integrity": "sha512-Hcrgnmkf+4JTj73GbK3bBhlVPiLL47owUAnoJIf69Hakl3q+KfodbDXiZWGMM7iqCZTxCG3Z2VRfPNYES4rXqQ==", "signatures": [{"sig": "MEYCIQCPYwdNkMEox/FdxZhqnUOSHL3S1Os7ML0WfwYESV3DHwIhALvApBM+ipqxFWs1Du7sFCJOSat4ez3LNxk+PNQIGYNu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzzzpCRA9TVsSAnZWagAAuisP/2fyh+JFcnq2nQOBqw9R\nzzlKkyhoYZDZ8GivPEKVbnA+D4aPf6rBN87aENoFGbG6/+crZr2ygU6Bw5Es\ni1jFhVPedVvaTEAh79jeuBLeJRr32AP/2ztSj87jFs6ZgwVo6MvyB4IoFVKe\n+IBZtXDMaqeOpBfAPyT37wE84L8aTu6eG10zZop6C/FZ3MvGC+zu/xMRkinD\nQkTVBcKRjI7sNOyCg6gkiB2zr3rkIydzV2qWiUJwSShrMp1BbVqnH6R4FuSn\nP/G8+KOr6UqJgivFh7u7YPe7tzU/mEEYRibhJLKp2bH9xuDmp2xTX0wz6/qM\n0NFmWvd637GxsR/+bZhFSZUXvdJH5gUoSYzCldieSMdMxGgKqSfd8mYer4ZL\nbVR31fgTwsgg7+/JF2szadfwm+Oxqhb6icM9ge4XIVKqyGCZ4rF3hbTQ/uzn\n7eMATli+QvrlMEOigFxCs1m1DD8bgBMh5UIB1z54/SfPo7KJdMxqT8IkMU7r\nsQp2I8ocJxl78Z2esRs0DEgLOYGzW5eChwf+jf3MNlQDbtxD8CLRNQj7AuxH\n7PPmKwaezSyvytQzaB0wHLDYsD5mIU85qbpku9CF1P3UEYG/mdVzQXa3FgJR\nc2jd3P9GnKKv3m8mleN7PAMpdAuldpAG8m0Ib6ruoNmxD4RjtLseq05w8PFl\nZepQ\r\n=en+M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1f286871243133749a7720d79027e3de7c17dc1b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.13.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"core-js-compat": "^3.20.0", "@babel/helper-define-polyfill-provider": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.20.0", "@babel/core": "^7.13.0", "core-js-pure": "^3.20.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.5.0_1640971496920_0.1731941563863757", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.5.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.5.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d66183bf10976ea677f4149a7fcc4d8df43d4060", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.1.tgz", "fileCount": 13, "integrity": "sha512-TihqEe4sQcb/QcPJvxe94/9RZuLQuF1+To4WqQcRvc+3J3gLCPIPgDKzGLG6zmQLfH3nn25heRuDNkS2KR4I8A==", "signatures": [{"sig": "MEUCIEyjiVRtRGuJ5+2VH9ZCZzsMPGwOoGESJXnJAf7P/SFQAiEA/m5ZizelEBXv2rRvfrPWjkqT2vu0NFF4GSaJKJgvBg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4uKOCRA9TVsSAnZWagAAA8cP/2w00p1ASixiovvfU9qN\nXezU6Ej1RJ5ounZSVxpA9e/tT18+JGooZeJizTuXtLk1XQt9zpRW7b76yQqq\nTFje4uHi38eVSL509S1QZBHVVPmco5OEhzVkhVoVBKgC8+x8NUDZLydml1Kb\nhK/NT80jsQYa0FXDXsRNEEJr+6vtnFMuazVndgME3TPZNOSu3MQqfD/41a6/\nk1MahHrnERlqzp/Y8Vax9hPq9A5qONrSUDezxKleH5NjZXfv0yc743GLy1WB\noBkMPVBz5r3Ck0z8g0CKQjLwtG5wzQXT9CVYZIVfwAannb9EkrZTlmP4fmua\nJxdzjDlsjqI3Co1HhF4tt75VO4g1gyy8JW/8ztnUgW+dVbckT25wAQZeBovp\nseAK7UYMWKVwQAsC9rLBV0MeDWTTbeJLHbGqQ2Y3TtVCxSHPb98KWezie2q4\ns1SgYnUDdvvTXWPAkiVEN939addRgYMfS2Uzj4rr61zKefEqeqkmLGVBHSjY\nFN9jQLtEMxZIF9Ow1SU7yOlGoMsOc+E7n8jpr2SW1e6DIhy3Gg+RoVSfv4CE\nVt4iql01vdWsgIQdNqWvX5oDZeNzR87DZtHg5Qxpo1R9dGjxDa0vEkgdclLj\n/aMO2Up/zjRCDubKQvR98uQ6CBhTh+MokPy2DpwPSGbYHQtTld5+Bai3xKD7\ni62r\r\n=rwjo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "f5eb1c6d74fa29afdb0a3a533dfc5742557197bc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.13.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"core-js-compat": "^3.20.0", "@babel/helper-define-polyfill-provider": "^0.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.20.0", "@babel/core": "^7.13.0", "core-js-pure": "^3.20.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.5.1_1642259085909_0.5879185937933131", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "babel-plugin-polyfill-corejs3", "version": "0.5.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.5.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "aabe4b2fa04a6e038b688c5e55d44e78cd3a5f72", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.2.tgz", "fileCount": 13, "integrity": "sha512-G3uJih0XWiID451fpeFaYGVuxHEjzKTHtc9uGFEjR6hHrvNzeS/PX+LLLcetJcytsB5m4j+K3o/EpXJNb/5IEQ==", "signatures": [{"sig": "MEYCIQDT0LWkEc19g3IW9b790cRqfTZFeQfZgqyCtt5/a+g8HwIhAKctHp2xZ/xa0l/9087ksIzvMxJRz2TrppQfo1gczLnM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+ccpCRA9TVsSAnZWagAA/1AP/04NcSnRlzNKGGuXOpps\nZ4sQFaALjqAnGNKgHOZVRTxPMKVCwbvLsC6fOolt8cXk7XjgzuZPwXVI/Oyn\n0PEPsP2UROTb0gOWrqW+ljktEGQ7ocqwVUQjHg2umZ/fzptsvUKefWX/b8Qm\nMpX2p+BPwMcG/ouT42GfRL81kygZEwLObM6Tusw0DOfdQ5l4f3z+KrM1PYdl\n5ouFxJk9T78F9PzLUWuc3m4YYsfK6I/ejZ023zWCxrwMwgmgD8wA/5n2wTRj\n/YusXqAuqT0c3cA7qko84vTw6O2PHoKifkwqmtbgxGFGWrzBM+efY43Gbs1M\nbHxodqzTG4Wdjv3W2DG4fMm3qsOK8T1ANEHjZWyU54cjq+nlCWb8DhRPSdKy\nPdliS4sqPCwz650VKD8t3rkPHswdKhOCL394+alIjdfZ2ZuDnWD21JquoXEq\ntyuU/8orbBsD0zTVi3XzOnepY6VsJgRH1DFncxJh4hw+BCJkbU0kM7qPW8kU\n1L8tv/colK9DmqO29F/5TzfadOMal2yW41wKnBC5KefK7iHzQCVpXzMnx73y\ne7nfBQcYPN/tl7cZw397blfCVp9oFqiSRbCgeykh396N87U1jgf9rlw2M/RB\n96qpzt7AWFKO6gSPHJjvbGJRXLsW/r9fw/z7fOSUInWNgRdrnI++K0zirlWs\nPMwW\r\n=r6Nr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "dfc74d8a78cb3f655dacf8aceea13a2a6024dee3", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v16.13.2+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"core-js-compat": "^3.21.0", "@babel/helper-define-polyfill-provider": "^0.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.21.0", "@babel/core": "^7.13.0", "core-js-pure": "^3.21.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-spread": "^7.13.0", "@babel/plugin-transform-classes": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.5.2_1643759401662_0.29922974539973435", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "babel-plugin-polyfill-corejs3", "version": "0.5.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.5.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d7e09c9a899079d71a8b670c6181af56ec19c5c7", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.3.tgz", "fileCount": 13, "integrity": "sha512-zKsXDh0XjnrUEW0mxIHLfjBfnXSMr5Q/goMe/fxpQnLm07mcOZiIZHBNWCMx60HmdvjxfXcalac0tfFg0wqxyw==", "signatures": [{"sig": "MEYCIQDm3JwxA3QYOqN/smDXQjoz8ogWMoJOUDcOFyGIPAvsKwIhAP3A37guDl02GnHVvvorUY7TGkwwqwJVvxWg2jM0JiOf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3O05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzfQ/+Lgx/Rl4ZvH1tcaTz2XM0paazQL+RQsiZ0hk5bM4g2cxOMoRg\r\n1vAY6/9SmvLrqiE2+RTIoEBxIL3/Qj5K2MAMhX7GINy+BsOvhKha9u9g2k6u\r\not+R1ua0mNzByFaaUXH9beJLYyWrRKhk5miILfgc70n9LePiibGJvEhThify\r\n0aeA7Shf8VQbbI2GNdP2ivO+8TDtm6lmVX5zBcStWJgo+5kZsw3Id9RR9X0F\r\nujmdAQJljH0M1NDOvH0NBSyaZbxMwJcm7/lZAWqO9t59aI/q88WR+x5lhFW2\r\nEbVvQFt8qH5B1WJkeMlH6o258fg9hVrZuFofhRMgpg/yPRdByoVAFQiKJZXj\r\ngtEmec5EQ8x9EzXGL0mt+EEe4T2elWrckdIOzf+fDoJnGFE2NvHfeEg61CAE\r\n9H/EQo2PNhhJBQtnwD3sY7L1GNngwNt+84O1Rp8s03j7M5siuU6M2FxEACp4\r\n99uHtprqfxbHH11VVyCoQ9RunyXpA3iPeoEaAPbuBw0VJ7/DXlmIw8c4LIwj\r\nEIQ3V46WawtNN/n5WSdd3FlXZXQKZ5MDWcG1AJDAcTAzhVfPDFm9XK+cUrXT\r\nKmLv5nvYIP6sVFJ/sKiTJCYTvtXRXUiaDBi3Orai9yOXkpsT9iExULFo3M0Q\r\nlMXELmeufknOz02BTVaBWwMUzpH1SORojbY=\r\n=WKYy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "62b9025beeec450a1ff5d61fadcf63963aec5015", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v14.20.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"core-js-compat": "^3.21.0", "@babel/helper-define-polyfill-provider": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.21.0", "@babel/core": "^7.17.8", "core-js-pure": "^3.21.0", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-spread": "^7.16.7", "@babel/plugin-transform-classes": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.5.3_1658645817574_0.7832462466031516", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.6.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.6.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "56ad88237137eade485a71b52f72dbed57c6230a", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz", "fileCount": 14, "integrity": "sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==", "signatures": [{"sig": "MEUCIQCaXB3/c1+T/ZeV2nBjlKZ8uwbdXI3qW2z5NNJU98R0WQIgDK4hNSnXK6Kma/sgxhjk3IBRYA8KcGknvSJd1KiBem0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH+hjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe1BAApI9bDiezJ3K83hjcGryR9bnerkEHYUSEVMb6ulbSSEbO8Fyg\r\nG3knjrhLMJ165kQ898mc4UIR1fIvUAPAzqNz4N61K4unxkMHVj3kxz+Hlvbj\r\nvwY7UWyeovpH7QQ5nBnyHPtzi9qYky8z86RTNBfR4YKsys4b8M3mHestG34z\r\nzGrv3rbp74DkL0N1I9ZgBLtTkk3zSzaqXBr+/Ggk+LcufxsivyoQ0xYeyz69\r\n/dO9WjWlwBgoZbLzG9RCqdXCzcokhxuyZth4dU3zMFL9x0GqKROdwr/o4/9k\r\nKmp9ZeM0V8DSnHHkkyvHQLJhRKXOHrnPKx4abMZ9oNh9e/0d+kTVWhHkmdBT\r\nEXwPJ1kHUnq+vJ1kGPqq1qwN3tivQ+udiBgVIRmRgaVkaPzL9ANqMU7s7bXA\r\n9ytDNCpjzAFmQE72IM8zylp5yvqy8H4s9PcSbgFEOOYS3hqFWOQ7TROggCvN\r\nuN+mnE8KqGAZTbl4A6XX2fnuMQ5UgsWTPiAONvFWxQBDd2pDIkoZjpgyKReU\r\ngyfzrI1Ee7SWK3OZn6xEM7UEgvCYfh0gUnH0Qj87sLxYnwy8LbnE+anhi4PT\r\nhDLilJp28TztvdKGSfpB0MTt+MRvLjSwZSJyi5HzmJWmZhN/g2Jk3OUa0eRb\r\n/729g9//7S4wW2Ad1ecODR7dypj0a8qlrPc=\r\n=o7lt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "42b9477c199c0c5420b45cfa8c9bb892d94a64af", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v18.7.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"core-js-compat": "^3.25.1", "@babel/helper-define-polyfill-provider": "^0.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.25.1", "@babel/core": "^7.17.8", "core-js-pure": "^3.25.1", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-spread": "^7.16.7", "@babel/plugin-transform-classes": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.6.0_1663035491209_0.7725360136770238", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.7.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.7.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "6a7733e002fd832be341d72302cf70e81d86eec0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.7.1.tgz", "fileCount": 14, "integrity": "sha512-SczVtF2EPqwYCnokNWzsEibkGEegOIFA5nEhVRHuZF+L4Guar2/QcowGiFcDz0C5zdRl9XQNKdeQTsPdx2W4BQ==", "signatures": [{"sig": "MEUCIQC9oM5RKKlT46ojHSaG8VgiKpuc+etjAfC2DeZd/omVpgIgeWXSIsXJzmLrgNG5dRv7x1XWX6LbZcaOnBGFlmZrjjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/0EjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY1BAAhVnsL/3fuCJ86wQxcAk7CyWRcN2O6Cdw7aDmadHnkDYJPFQW\r\nJ5/EiZiTU/6JVPm/n+zZks2EwOJVbDWn+egAwJHkfMoURiWGG5PMlL339jI9\r\nmsw0eDDPHs8KVXa30cAlu5CKKUZKU3aeE/iY2o+1Kg2RKbO4CdTVjGQ+Ku1P\r\ntCy40VpdkRUBOLf+4tyu6PlLQ6wpH9YPHZmtTzTqjXiz/L8CbvP0ySHm+es2\r\n0/auyCldHPsi+RsjHnXfZNjl3hXE2AliuXl7NmNKgJzHPpmQsK+8dx4bWezL\r\nfv+o5Fypb+MC3HcJozR0rONfHHe9DSI9EypYrJmHOCibeoV7SgJmYFY4KyzK\r\nFzUaV8PiZhvFD0sFuKs3zboLxzTjqv7z/1mg5e6XkI90Sa9TGnVPQ7OX9rMt\r\nQOcPFPLKz4Y0Rap0BDIbZ74v8qC1enHDIwGkqLrN/PdFKMJCa9724BuMAQDl\r\nfeQF6jDJ2qFmUxdMGw9zGPg6hpm4bZ8vMc6yXt/ZG23KdqfAhvvEcLtXmK3n\r\nHYXliu3BxlYJ3V1L3aJ7kqPZVxdtWrtrhEAlGRLvZbUm0M6CjPs7wMI3YU2w\r\nsZMyxYwgBCQo2SV5wCRBKfMMnPtUjVonRgiM5PeYnJiHGKHvJQYlOqPoPWTx\r\n/0kTS4CkcPnp/KYuvUV3I3yHlhdJ9pPXjDw=\r\n=fdZz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "f9d6c6041bfaad1f89b1dce6f23a919636229008", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v19.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"core-js-compat": "^3.29.0", "@babel/helper-define-polyfill-provider": "^0.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.29.0", "@babel/core": "^7.17.8", "core-js-pure": "^3.29.0", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-spread": "^7.16.7", "@babel/plugin-transform-classes": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.7.1_1677672738944_0.9974118384765416", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "27667065ee0402af0da508ca13d0f5f69e6cd1a4", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.0.tgz", "fileCount": 14, "integrity": "sha512-0qebvb4DbbhQxEf8XipuzwDiqTXGYbfUISG0AgkR0/VZQCJgis5/BZOADAGwUSyAvgQuJJz/ktZQZv/T8mSPMw==", "signatures": [{"sig": "MEUCIBPCSwAgBNemaynLsZfRtSv6K+K1N6E861sFZp4mWXPtAiEAwSnAJI6A2SGXbV8czJIWPvt23xZiCBjdwBBQWhOaNaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180545}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "391a1f4049fe1d6943ca8e91cf7e2e23f3f1ef73", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"core-js-compat": "^3.30.1", "@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.1", "@babel/core": "^7.17.8", "core-js-pure": "^3.30.1", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-spread": "^7.16.7", "@babel/plugin-transform-classes": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.0_1683713941908_0.08429865278580184", "host": "s3://npm-registry-packages"}}, "0.8.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "39248263c38191f0d226f928d666e6db1b4b3a8a", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.1.tgz", "fileCount": 14, "integrity": "sha512-ikFrZITKg1xH6pLND8zT14UPgjKHiGLqex7rGEZCH2EvhsneJaJPemmpQaIZV5AL03II+lXylw3UmddDK8RU5Q==", "signatures": [{"sig": "MEYCIQDXCjxoMbDwsZdBPkckZtFvXWrJO635C48xlL7HFFiwzQIhAOR107n1HDsgl5RTHrg/gTLV1ALxUZOjc2vrqe2QLXIa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181164}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "4b3c67a6a946df756123952a0033dc4426696131", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"core-js-compat": "^3.30.1", "@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.1", "@babel/core": "^7.17.8", "core-js-pure": "^3.30.1", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-spread": "^7.16.7", "@babel/plugin-transform-classes": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.1_1683716132774_0.33387660683850995", "host": "s3://npm-registry-packages"}}, "0.8.2": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d406c5738d298cd9c66f64a94cf8d5904ce4cc5e", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.2.tgz", "fileCount": 14, "integrity": "sha512-Cid+Jv1BrY9ReW9lIfNlNpsI53N+FN7gE+f73zLAUbr9C52W4gKLWSByx47pfDJsEysojKArqOtOKZSVIIUTuQ==", "signatures": [{"sig": "MEYCIQC/mIuctWbTGnsHi7VGtim1zNA3th84XWjSjsi3//3OVwIhAIkkl0WJhioZZ6HMv9/yWxMVMMJadxv3Wilwgz2t56lv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182134}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "74956db5d547985ac8e60bf1af56f4c61af12e4e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.3.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"core-js-compat": "^3.31.0", "@babel/helper-define-polyfill-provider": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.31.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.31.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.2_1688546289306_0.05869445259270423", "host": "s3://npm-registry-packages"}}, "0.8.3": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "b4f719d0ad9bb8e0c23e3e630c0c8ec6dd7a1c52", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.3.tgz", "fileCount": 14, "integrity": "sha512-z41XaniZL26WLrvjy7soabMXrfPWARN25PZoriDEiLMxAp50AUW3t35BGQUMg5xK3UrpVTtagIDklxYa+MhiNA==", "signatures": [{"sig": "MEUCIFSkQ31gJyZvTeUpNEtuZv9DenQBVbwjV82tqB9BklE1AiEAw/ilG6Az0G/BA6EFT+R2wvf5cNTHDV4E9OqzbUzaECY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182151}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "0e8cfb85899c8fb01728199d81fd37108e1668ab", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.4.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"core-js-compat": "^3.31.0", "@babel/helper-define-polyfill-provider": "^0.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.31.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.31.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.3_1689930194547_0.4248423786299227", "host": "s3://npm-registry-packages"}}, "0.8.4": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "1fac2b1dcef6274e72b3c72977ed8325cb330591", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.4.tgz", "fileCount": 14, "integrity": "sha512-9l//BZZsPR+5XjyJMPtZSK4jv0BsTO1zDac2GC6ygx9WLGlcsnRd1Co0B2zT5fF5Ic6BZy+9m3HNZ3QcOeDKfg==", "signatures": [{"sig": "MEUCIEargiEVC2Y+dBww0ocuEiQd61SoqS3PdCUYZwqXyXkYAiEAg/kGUEMew4E2bfxpI3Wc/Lij43yTRDhW0E21xVwv4Gg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188194}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "67518e8df24fbca8537f5a8157443a57eeb7952a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"core-js-compat": "^3.32.2", "@babel/helper-define-polyfill-provider": "^0.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.32.2", "@babel/core": "^7.22.6", "core-js-pure": "^3.32.2", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.4_1695454972432_0.35152383091373096", "host": "s3://npm-registry-packages"}}, "0.8.5": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a75fa1b0c3fc5bd6837f9ec465c0f48031b8cab1", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.5.tgz", "fileCount": 14, "integrity": "sha512-Q6CdATeAvbScWPNLB8lzSO7fgUVBkQt6zLgNlfyeCr/EQaEQR+bWiBYYPYAFyE528BMjRhL+1QBMOI4jc/c5TA==", "signatures": [{"sig": "MEQCIFPAfFwmt63qC1gt+qljaqlwcG27DYgpDd3AxO76/qcgAiB2/ELjM+X00PDHQNvGsxaE6H7gqxH9/oIrt6fIYltFYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188214}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66a6819f44a57152798cb3b0a9272c65752bae86", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"core-js-compat": "^3.32.2", "@babel/helper-define-polyfill-provider": "^0.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.32.2", "@babel/core": "^7.22.6", "core-js-pure": "^3.32.2", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.5_1697007741723_0.7109224903545783", "host": "s3://npm-registry-packages"}}, "0.8.6": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "25c2d20002da91fe328ff89095c85a391d6856cf", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.6.tgz", "fileCount": 14, "integrity": "sha512-leDIc4l4tUgU7str5BWLS2h8q2N4Nf6lGZP6UrNDxdtfF2g69eJ5L0H7S8A5Ln/arfFAfHor5InAdZuIOwZdgQ==", "signatures": [{"sig": "MEUCIF9z85+7V5h3EeBehh7K1p4nHKo211QPi8BL3fpTRvo5AiEApNVkfkkI4IqLJL7tbgasSuYQmhSGBssMQTuR7v+h+rE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188575}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "2a85015c295d2e0256e5d60f595c1f6368861f04", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.8.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"core-js-compat": "^3.33.1", "@babel/helper-define-polyfill-provider": "^0.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.33.1", "@babel/core": "^7.22.6", "core-js-pure": "^3.33.1", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.6_1698058404499_0.6517039065571291", "host": "s3://npm-registry-packages"}}, "0.8.7": {"name": "babel-plugin-polyfill-corejs3", "version": "0.8.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.8.7", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "941855aa7fdaac06ed24c730a93450d2b2b76d04", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.7.tgz", "fileCount": 14, "integrity": "sha512-KyDvZYxAzkC0Aj2dAPyDzi2Ym15e5JKZSK+maI7NAwSqofvuFglbSsxE7wUOvTg9oFVnHMzVzBKcqEb4PJgtOA==", "signatures": [{"sig": "MEUCIBhH9LT9GkdaHsJuflA2IMR005l6wIn1+4nxSVqB3SvNAiEAh7E96jK/O37+MiWXcdGGLxz6wXF4TAJR4s1su/HQUF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188575}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "984c56c59568283889c3f0f89e58d370e4fd10f8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"core-js-compat": "^3.33.1", "@babel/helper-define-polyfill-provider": "^0.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.33.1", "@babel/core": "^7.22.6", "core-js-pure": "^3.33.1", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.8.7_1702304408841_0.6361212260355946", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.9.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "9eea32349d94556c2ad3ab9b82ebb27d4bf04a81", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.9.0.tgz", "fileCount": 14, "integrity": "sha512-7nZPG1uzK2Ymhy/NbaOWTg3uibM2BmGASS4vHS4szRZAIR8R6GwA/xAujpdrXU5iyklrimWnLWU+BLF9suPTqg==", "signatures": [{"sig": "MEQCIGk1qAKoLrS65KDS5B2FuE18WKhwxtdC7CnEmt8TQzgVAiBejl5qL1/tCyd1pkdxG2DbikeVJ9tm3K7fliISEbyL7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190034}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "9738ea2a12643376a52c9be30c20ac19426a88cb", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"core-js-compat": "^3.34.0", "@babel/helper-define-polyfill-provider": "^0.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.34.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.34.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.9.0_1705571831624_0.13843392247730724", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.10.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "41d3983363607a00af09adb5e86ecc8147053594", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.0.tgz", "fileCount": 14, "integrity": "sha512-zdSqqQU/Xj7OTlvNOGJPlNJQVGjD5pu71m8BTda9B9abYP7aWuoklXtyA/YCEudSUOWGWycaU1lrdqZ6rA571w==", "signatures": [{"sig": "MEUCIAZpPCPqtnz+rIQvwUAqfVhPm6jWQFxli4DI3Pvg4XuJAiEAvW+yNF+RXUT1L+tZVspCX1FT/3cLj0Da13TBwGRMDcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192423}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "58703f07c9cff9f27d145215265042094739a175", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"core-js-compat": "^3.36.0", "@babel/helper-define-polyfill-provider": "^0.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.36.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.36.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.10.0_1709919060935_0.14404688015851086", "host": "s3://npm-registry-packages"}}, "0.10.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.10.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "cd8750e0b7da30ec2f66007b6151792f02e1138e", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.1.tgz", "fileCount": 14, "integrity": "sha512-XiFei6VGwM4ii6nKC1VCenGD8Z4bjiNYcrdkM8oqM3pbuemmyb8biMgrDX1ZHSbIuMLXatM6JJ/StPYIuTl6MQ==", "signatures": [{"sig": "MEYCIQCK1iQL23gqyR94G3d2kVE7M7Q9cdLwkY3mW6Qgi4ZHGwIhAJFC/1ANuD0IJBShZEEtiwpDvDlJOOsNcygunGVJiWrp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192423}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1ce88db2507db2ef3d2ed2a2f920a3cf0b9364b5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"core-js-compat": "^3.36.0", "@babel/helper-define-polyfill-provider": "^0.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.36.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.36.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.10.1_1710259199721_0.9296635609078989", "host": "s3://npm-registry-packages"}}, "0.10.2": {"name": "babel-plugin-polyfill-corejs3", "version": "0.10.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.10.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e2ce97afe205e4eff0474af31761ea1672a867e4", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.2.tgz", "fileCount": 15, "integrity": "sha512-CGzpM0M2pqfv9k91F68TVsDRfdbrHLxc/RFA3r9xO3NNfZ85iN74E4tt1BL7gdlbhbzAe0ndWTwsZJiG/lCMtw==", "signatures": [{"sig": "MEUCIHHwzIDnUwJFrwkKAa6v6c5G938+dEbryDk/Nxi0vjAhAiEAke/7nXdDA/DYL9rVvxqK68HIzss1rCb0Ch5vG/vd9fE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205813}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "ac9817f22bbb4e58929949ca31ce1f5a9b4db093", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v21.7.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"core-js-compat": "^3.36.1", "@babel/helper-define-polyfill-provider": "^0.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.36.1", "@babel/core": "^7.22.6", "core-js-pure": "^3.36.1", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.10.2_1710933720390_0.1944739936165769", "host": "s3://npm-registry-packages"}}, "0.10.3": {"name": "babel-plugin-polyfill-corejs3", "version": "0.10.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.10.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2d69deb32e87d222ff2fca9c67ae93c322472bc0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.3.tgz", "fileCount": 15, "integrity": "sha512-ndDWCGErvdftXCO/7fSlD7EXDiAJV5IJfotN3iO2WfLdGh1DJfMjpjiWJeL7WP4QHYE+OB6WKgdW/AmloiF2kA==", "signatures": [{"sig": "MEUCIHbv2DNe6nnCbSFFk7hKbE4S0xG4rSB/uMKlYX+cXU7PAiEA6AQIzvo8PAHhBm0I4rFkNNinWFWv+HwC9Pfhf1dtgCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206953}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "41258f9d5d78e0b6d7df6550a69c77d990ad047c", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v21.7.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"core-js-compat": "^3.36.1", "@babel/helper-define-polyfill-provider": "^0.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.36.1", "@babel/core": "^7.22.6", "core-js-pure": "^3.36.1", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.10.3_1710934476983_0.49557554648074253", "host": "s3://npm-registry-packages"}}, "0.10.4": {"name": "babel-plugin-polyfill-corejs3", "version": "0.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.10.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "789ac82405ad664c20476d0233b485281deb9c77", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.4.tgz", "fileCount": 15, "integrity": "sha512-25J6I8NGfa5YkCDogHRID3fVCadIR8/pGl1/spvCkzb6lVn6SR3ojpx9nOn9iEBcUsjY24AmdKm5khcfKdylcg==", "signatures": [{"sig": "MEQCIFH8XTvC+/qMM3antna1avI1sWkJ9tPJ5APneysNS9yJAiAu7e0CCcl3hv2QqczqC2x4OKskrG4ESRyR6MvZIbWs7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207013}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "a5db9c31c5b5474b4018e6178bc40882fc3eb5bf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v21.7.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"core-js-compat": "^3.36.1", "@babel/helper-define-polyfill-provider": "^0.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.36.1", "@babel/core": "^7.22.6", "core-js-pure": "^3.36.1", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.10.4_1710934659100_0.09152483674886858", "host": "s3://npm-registry-packages"}}, "0.10.6": {"name": "babel-plugin-polyfill-corejs3", "version": "0.10.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.10.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2deda57caef50f59c525aeb4964d3b2f867710c7", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.6.tgz", "fileCount": 15, "integrity": "sha512-b37+KR2i/khY5sKmWNVQAnitvquQbNdWy6lJdsr0kmquCKEEUgMKK4SboVM3HtfnZilfjr4MMQ7vY58FVWDtIA==", "signatures": [{"sig": "MEQCICRyxyX6HTaPATb7YZZYAlGAoapMKfPNujuBldQEK1I4AiAFmUaJsEMHaQLRYcZ6VNDi9yJw8BpVVJlxU4rUsEsY0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207286}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "3c3bc68dccaeea18126e45751beb6691748bb7ac", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v20.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"core-js-compat": "^3.38.0", "@babel/helper-define-polyfill-provider": "^0.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.38.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.38.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.10.6_1722804434493_0.5533430886870052", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.11.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.11.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "0affeda895210425925d90a79613f48720308854", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.0.tgz", "fileCount": 15, "integrity": "sha512-5Kp2G1M3qTECkShvOgvDwBNm+5eh8XaDswzkncgrWPmbhFmsS1FO7LKhnAXSBMDc3Zw3iBHsF5xeHV3rNM3SuQ==", "signatures": [{"sig": "MEUCIEZhJFeXrdkSz0yKcsKYf3eEdkT9dvVWH6vQNnoe58cSAiEAzDeh7yu2K/RSh7YeoWj0kTwLRBRrcMvaG+z6WIpQTwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219687}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66340fb145086a826c496f008f67488367846c09", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v22.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"core-js-compat": "^3.39.0", "@babel/helper-define-polyfill-provider": "^0.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.39.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.39.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.11.0_1731351014308_0.461181273270282", "host": "s3://npm-registry-packages"}}, "0.11.1": {"name": "babel-plugin-polyfill-corejs3", "version": "0.11.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.11.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "4e4e182f1bb37c7ba62e2af81d8dd09df31344f6", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz", "fileCount": 15, "integrity": "sha512-yGCqvBT4rwMczo28xkH/noxJ6MZ4nJfkVYdoDaC/utLtWrXxv27HVrzAeSbqR8SxDsp46n0YF47EbHoixy6rXQ==", "signatures": [{"sig": "MEYCIQCh6ALK4253ST/wWdmjrOBLnwG8XjXzodc8mZ/lAyD5iQIhAIki1MA5CmFIivgrR+KAsjtyMzxUA6kU8fO8049B7OjG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 221237}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "786a49e4fc05408168aefd9df018a56dcf97c450", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v23.5.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "23.5.0", "dependencies": {"core-js-compat": "^3.40.0", "@babel/helper-define-polyfill-provider": "^0.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.40.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.40.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.11.1_1737718711318_0.49940643914754324", "host": "s3://npm-registry-packages-npm-production"}}, "0.12.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.12.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs3@0.12.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "49dde009355519caa52ac1cac690006e20c1f6a6", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.12.0.tgz", "fileCount": 15, "integrity": "sha512-S95J8gvvIIBanFi/mHvZ6CIvkpHKPICgaDNfwpDZfl5xcNnmX4Y3T/+HnCRSddBkuFmX3DkBzzCI4anCl5Ql7w==", "signatures": [{"sig": "MEQCIHSWA+WQVsoh2OWIFJU4Nk/0uXAopg3chOdywfpG8mfiAiAchr9f3ZNfFmGPsK4RAGQ2MZus+2eDzCbn6mq0SmPhVA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 221168}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "_npmVersion": "lerna/3.22.0/node@v23.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "directories": {}, "_nodeVersion": "23.6.1", "dependencies": {"core-js-compat": "^3.41.0", "@babel/helper-define-polyfill-provider": "^0.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.41.0", "@babel/core": "^7.22.6", "core-js-pure": "^3.41.0", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs3_0.12.0_1742390649312_0.7936008925235283", "host": "s3://npm-registry-packages-npm-production"}}, "0.13.0": {"name": "babel-plugin-polyfill-corejs3", "version": "0.13.0", "description": "A Babel plugin to inject imports to core-js@3 polyfills", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5", "core-js-compat": "^3.43.0"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.7", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/plugin-transform-spread": "^7.27.1", "core-js": "^3.43.0", "core-js-pure": "^3.43.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "homepage": "https://github.com/babel/babel-polyfills#readme", "_id": "babel-plugin-polyfill-corejs3@0.13.0", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/3.22.1/node@v24.3.0+x64 (linux)", "dist": {"integrity": "sha512-U+GNwMdSFgzVmfhNm8GJUX88AadB3uo9KpJqS3FaqNIPKgySuvMb+bHPsOmmuWyIcuqZj/pzt1RUIUZns4y2+A==", "shasum": "bb7f6aeef7addff17f7602a08a6d19a128c30164", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.13.0.tgz", "fileCount": 15, "unpackedSize": 223947, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD/VlrDMUW81H7rw1fzMaNTM7E35j3h80EcgXuHR77ftwIhAO0QYiJCf7katUrSs9pLG5APzQK7POzyusRNMnX/0fbb"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/babel-plugin-polyfill-corejs3_0.13.0_1751042269398_0.910126558774869"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-04-08T14:27:34.284Z", "modified": "2025-06-27T16:37:49.820Z", "0.0.0": "2020-04-08T14:27:34.383Z", "0.0.1": "2020-05-26T20:42:24.866Z", "0.0.2": "2020-05-26T21:00:49.970Z", "0.0.3": "2020-07-10T21:36:08.370Z", "0.0.4": "2020-08-18T22:26:18.375Z", "0.0.5": "2020-08-31T22:59:43.647Z", "0.0.6": "2020-10-03T10:54:35.261Z", "0.0.7": "2020-11-09T14:37:29.486Z", "0.0.8": "2020-12-09T23:00:52.506Z", "0.0.9": "2020-12-22T00:53:51.631Z", "0.0.10": "2020-12-22T01:02:01.588Z", "0.0.11": "2021-01-07T15:06:07.299Z", "0.1.0": "2021-01-09T14:09:30.168Z", "0.1.1": "2021-02-22T00:57:13.705Z", "0.1.2": "2021-02-22T21:13:54.786Z", "0.1.3": "2021-02-23T11:23:17.948Z", "0.1.4": "2021-02-23T13:21:20.105Z", "0.1.6": "2021-02-25T23:18:00.647Z", "0.1.7": "2021-03-02T08:38:50.984Z", "0.2.0": "2021-03-31T15:52:57.570Z", "0.2.1": "2021-05-21T22:20:44.887Z", "0.2.2": "2021-05-26T10:10:31.779Z", "0.2.3": "2021-06-16T21:13:17.179Z", "0.2.4": "2021-07-28T19:55:37.536Z", "0.2.5": "2021-09-22T17:08:29.555Z", "0.3.0": "2021-10-29T22:20:18.344Z", "0.4.0": "2021-11-13T10:35:01.408Z", "0.5.0": "2021-12-31T17:24:57.092Z", "0.5.1": "2022-01-15T15:04:46.146Z", "0.5.2": "2022-02-01T23:50:01.828Z", "0.5.3": "2022-07-24T06:56:57.789Z", "0.6.0": "2022-09-13T02:18:11.359Z", "0.7.1": "2023-03-01T12:12:19.167Z", "0.8.0": "2023-05-10T10:19:02.107Z", "0.8.1": "2023-05-10T10:55:32.969Z", "0.8.2": "2023-07-05T08:38:09.423Z", "0.8.3": "2023-07-21T09:03:14.777Z", "0.8.4": "2023-09-23T07:42:52.631Z", "0.8.5": "2023-10-11T07:02:21.973Z", "0.8.6": "2023-10-23T10:53:24.728Z", "0.8.7": "2023-12-11T14:20:09.054Z", "0.9.0": "2024-01-18T09:57:11.809Z", "0.10.0": "2024-03-08T17:31:01.119Z", "0.10.1": "2024-03-12T15:59:59.890Z", "0.10.2": "2024-03-20T11:22:00.566Z", "0.10.3": "2024-03-20T11:34:37.192Z", "0.10.4": "2024-03-20T11:37:39.279Z", "0.10.6": "2024-08-04T20:47:14.661Z", "0.11.0": "2024-11-11T18:50:14.483Z", "0.11.1": "2025-01-24T11:38:31.515Z", "0.12.0": "2025-03-19T13:24:09.514Z", "0.13.0": "2025-06-27T16:37:49.630Z"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "license": "MIT", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "description": "A Babel plugin to inject imports to core-js@3 polyfills", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "readme": "# babel-plugin-polyfill-corejs3\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev babel-plugin-polyfill-corejs3\n```\n\nor using yarn:\n\n```sh\nyarn add babel-plugin-polyfill-corejs3 --dev\n```\n\n## Usage\n\nAdd this plugin to your Babel configuration:\n\n```json\n{\n  \"plugins\": [[\"polyfill-corejs3\", { \"method\": \"usage-global\", \"version\": \"3.20\" }]]\n}\n```\n\nThis package supports the `usage-pure`, `usage-global`, and `entry-global` methods.\nWhen `entry-global` is used, it replaces imports to `core-js`.\n\n## Options\n\nSee [here](../../docs/usage.md#options) for a list of options supported by every polyfill provider.\n\n### `version`\n\n`string`, defaults to `\"3.0\"`.\n\nThis option only has an effect when used alongside `\"method\": \"usage-global\"` or `\"method\": \"usage-pure\"`. It is recommended to specify the minor version you are using as `core-js@3.0` may not include polyfills for the latest features. If you are bundling an app, you can provide the version directly from your node modules:\n\n```js\n{\n  plugins: [\n    [\"polyfill-corejs3\", {\n      \"method\": \"usage-pure\",\n      // use `core-js/package.json` if you are using `usage-global`\n      \"version\": require(\"core-js-pure/package.json\").version\n    }]\n  ]\n}\n```\n\nIf you are a library author, specify a reasonably modern `core-js` version in your\n`package.json` and provide the plugin the minimal supported version.\n\n```json\n{\n  \"dependencies\": {\n    \"core-js\": \"^3.43.0\"\n  }\n}\n```\n```js\n{\n  plugins: [\n    [\"polyfill-corejs3\", {\n      \"method\": \"usage-global\",\n      // improvise if you have more complicated version spec, e.g. > 3.1.4\n      \"version\": require(\"./package.json\").dependencies[\"core-js\"]\n    }]\n  ]\n}\n```\n\n### `proposals`\n\n`boolean`, defaults to `false`.\n\nThis option only has an effect when used alongside `\"method\": \"usage-global\"` or `\"method\": \"usage-pure\"`. When `proposals` are `true`, any ES proposal supported by core-js will be polyfilled as well.", "readmeFilename": "README.md"}