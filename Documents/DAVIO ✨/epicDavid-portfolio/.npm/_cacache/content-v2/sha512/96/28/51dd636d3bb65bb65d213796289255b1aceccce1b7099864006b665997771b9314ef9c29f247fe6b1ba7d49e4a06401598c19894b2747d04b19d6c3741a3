{"_id": "@babel/plugin-transform-async-to-generator", "_rev": "122-5611fed50a0cd32e529c3ef4ff87a5bf", "name": "@babel/plugin-transform-async-to-generator", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4ef8a61b42d1e44e9dcbb19dba510c08a1042485", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.4.tgz", "integrity": "sha512-y9PcxV6UczNo6LzdRwz3AC/f6lz1ZsuIS3H+f2XBBd0IhXAMExtCNMme5GgQmSvdiq9JNsWfXeTT6kLtqErspw==", "signatures": [{"sig": "MEUCIDQGcewFRRePKhjN35+G4dX6mMCxuNSt5WmqSAJ8ajNjAiEAjOn8GW+0dJ0nKAh82mU4Cto5SF75/mRcshbe/EZMhTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.4", "@babel/helper-remap-async-to-generator": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.4.tgz_1509388597952_0.779145248234272", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4c10e102374a1b345538463b9b67554214c8ff0f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.5.tgz", "integrity": "sha512-l/k11JDCxXKnhXSNiucQyD7bSGtnrB6HHCv0GlMOeUEV6xh4FK20Wp2gmvAtwQlotbzrqU4CjCb00paJkL2btw==", "signatures": [{"sig": "MEQCIDN6wpDntFgTmN/eIM201bDjP+LQYsVQISgQizYCCkoMAiAIWBEKzW9SydsJXyIFdvBmZ/azf2ZQ1wgDgr4rOff7Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.5", "@babel/helper-remap-async-to-generator": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.5.tgz_1509397097614_0.3522419291548431", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0c65bce8a45786d0ce98c9859f5368e3f7b42465", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.31.tgz", "integrity": "sha512-S9Uf+nJtokBGhYjeun2wWP2L7F1mKedlKkFSWW7dUFylij9M/rzsjcECHWY1wf2AnH3jAdjzmB3jGpWKA+PyyQ==", "signatures": [{"sig": "MEUCIEsjN6hiQG5vNCMBYIZmlyWM313RliNi5/Yyy+YKv1/fAiEA5/MKCqAzeTgo8qjxbx0j3mQOd13LK3lAXvMfCS7rEuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.31", "@babel/helper-remap-async-to-generator": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.31.tgz_1509739481623_0.4291738229803741", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d8b521b878898f9b57d0d5953ba4850550458728", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.32.tgz", "integrity": "sha512-OsQskJJA3GuKECVEzUMQ40b4/ZWZwCQLw3qPN8Dvzl3jJ577JdPRmh38EaDrYhCRv34R9RfdWEWLfaUoYbocLQ==", "signatures": [{"sig": "MEUCIExsGYLhrTiZeeK9XvObAu5sPbjcPujBvu7bTMvtnN0dAiEAw71llZtQ9vYgYEda26JzS1ErpH7qloIUdR3efc4PxU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.32", "@babel/helper-remap-async-to-generator": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.32.tgz_1510493650922_0.09526892798021436", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ee7647aa96c8c51c05a0c35da28adc4a5b7a3aac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.33.tgz", "integrity": "sha512-Xay1tlDd0j5tm3JJfUS4iH90Fz4YykkEirWXpNIlmCDXd1XAZTrdwhKu04CCwpMGwLHIw71Zl6wEAvR2mHCuug==", "signatures": [{"sig": "MEUCIQDBMotGgzux7ZQbZk2brg5YxMiRZzUyW0KdIhTMbCw+vQIgVTSlLU322bpNRQtJCSTWFb2si327eUpEZZISE+C0d4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.33", "@babel/helper-remap-async-to-generator": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.33.tgz_1512138569558_0.015908944886177778", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "889c1d75307f12523911b3eaa2932ef91f5b59c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.34.tgz", "integrity": "sha512-pl5MtQkMq9Aj0Vk4wdTHwVjw38d7C+OceVlcTnW6epscpdG3DJgzeFviteBfZg/IzULvYCQ4p1leN+Kw3PMLag==", "signatures": [{"sig": "MEUCIFo3M/5wBNP/i9zv0/hrbfsD0PU3a4b41gmaawNepd4ZAiEAsbkJb/Magy615OjX5GY8M2d3nwD2d+QHDjpXiZP12jY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.34", "@babel/helper-remap-async-to-generator": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.34.tgz_1512225628553_0.5771008271258324", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fd83aa60c374f91f549f1ecea39a766702cb15f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.35.tgz", "integrity": "sha512-vPlHoCv0DKJuawuk0/1GdqGwKNkVDVsbauVJTrdccSG0Vll867RRvBKWXb6fV++2kvXwVFEzBE5+1FHHUaWmOA==", "signatures": [{"sig": "MEUCIGMVX79I8PfnP0+cjkMMa5yFuPN1rAdlNK2QOhhmVoYhAiEAj8v6w2zYUNBG3hpLpU0Nf/ap/A7nTvRBvwjyx99dHSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.35", "@babel/helper-remap-async-to-generator": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.35.tgz_1513288114711_0.0550991203635931", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "da4530848f2fb595ad5ca89530814c1416a572ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.36.tgz", "integrity": "sha512-2OmUmLZOGdVFRJXpisc4VSSwbAXoOvA9s8EF8IiKEzHbFs+GVmjadDMnkNbMK8w5ATm150jbwRvNPMV7Ztfavw==", "signatures": [{"sig": "MEUCIQCH5wgFg+5MVe4F9r57aY2CXA6YbkAI56IVK92fjnbrFwIgH/vV1GH/CWRJ6Fin4jLID8zJJecAno2LTAF+IQktjU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.36", "@babel/helper-remap-async-to-generator": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.36.tgz_1514228752423_0.6365339681506157", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e48e1feb9c70cb538b1545d7e360beb2cb6962ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.37.tgz", "integrity": "sha512-MZCbeKU2lHtTCdZj29mvmI4vEtuPrh0gngm17XJrm7DvnSAg9nb3Ys2S2Lhqr8DwuZKtIZD8E/cDdzVU/OyUFQ==", "signatures": [{"sig": "MEUCIDFUCp7/svhgGFuS1UxpbRJS51PvGIC6wyMVOLfcx4o8AiEA6AOMqx7+828V2hWiHODgEvIkoqKv/X6XOX3EsMq7JkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.37", "@babel/helper-remap-async-to-generator": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.37.tgz_1515427429753_0.6148808209691197", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "54eb524d5ad7ca4309d6df0c9b0f66faf8ffb8d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.38.tgz", "integrity": "sha512-l3A58LHNzp/Q1l8SdinAoSNsCwR0Fe3YG8y+N19IAYG/wD5NWyYobTv19UXePe9RmPLg3dUBSTwGAcMXwZrxFQ==", "signatures": [{"sig": "MEUCIQDroN9buE7Gyk8alku3CsUxbCXC0lQ2yLcTz6ttyJySZgIgfEOmEU3X1SVNsKPuWPf2WMV82zvSEO0thPexuZ/cW+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.38", "@babel/helper-remap-async-to-generator": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.38.tgz_1516206770286_0.06631930312141776", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "25d05e410b3f6f3106781318e10952cdc10ed9d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.39.tgz", "integrity": "sha512-Oz3HcGFHAvj/Z+rt2yIPjtl7k2r0qiX9Wyv4uEZP0SX9l5rCYjs6hV+GAyjC0249IrIWBDJBg0e8df2EiVUTTg==", "signatures": [{"sig": "MEYCIQDDuL45kNFst1qXtvGZ7yO0GA7pJp5y7+IT+wbGu0xbsAIhAOGf6xnOQCd1qXkjCPD0lCFhFWaMsK4/4o713txbLdpc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.39", "@babel/helper-remap-async-to-generator": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator-7.0.0-beta.39.tgz_1517344130893_0.5877271164208651", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9195e2473a435b9a9aabc0b64572e9d1ec1c57cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-4yTmjiZQw0S6dpnJqj0os0hom2czOAFKPhAuPplDay2zyqzDjbNt3zHFadIRTU4ekTonMb6ghTbMO1vlKcLMiQ==", "signatures": [{"sig": "MEYCIQC9t8U+JYmvwnm3ssHGcTamYCFaC3/tzFx0fqZ4NhOvxwIhAL0QrtVphNfh+HScO1tRE3xANkWuDZ3SZ71RCqBwbULM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3377}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-imports": "7.0.0-beta.40", "@babel/helper-remap-async-to-generator": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.40_1518453780096_0.982932016491598", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f400c3a67b05b475cccc3d5319c71e48525346cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-BanNd6iX+l64ANuat0/IY44bQ9pH1dKzFI23prM2eQ+r4g2tL7VUyCLKzkH4FIKEFmAmbSiXyUy7lEjNoI9xFg==", "signatures": [{"sig": "MEYCIQDmxkSymG1GJe75If1rN85RvyJDNzuqZIOGKLQT+D3hMwIhAPMFTpnW8L2t2I9mfbNmW5MkEqjYFpT4gHu7V+6i0VJo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3561}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-module-imports": "7.0.0-beta.41", "@babel/helper-remap-async-to-generator": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.41_1521044829608_0.8515647157854505", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c74e278b9722efeb7f2c7da5fbff7540c4a7f353", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-TTu8z0xz/mS8H98nCVw3wp86RSzdvUhtM4hX32sItrauJX1X8shVZDKbDtG8ZdA1FMKLgfvmn/hxtPOqjOkkpw==", "signatures": [{"sig": "MEUCIQDQZRMyvA2s17wjoAf7cgJnrxXvtbfvY9co/XdkIG1E6gIgOs9wHtZvhs5iyEL+mfhhc6MrY/VSxcXmvK6w4sl/TVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3561}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-module-imports": "7.0.0-beta.42", "@babel/helper-remap-async-to-generator": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.42_1521147139451_0.13595836658832328", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "85214c9e0a9f7d43ea148ea3867975ffe01c48ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-+MRAvHqrA5AIV9YQgjxMcBPyhXfBP5bxsZ3rKgvKS4fLEF01Q02xiGkfFdAn+gKyKW+BxW1IOm+himyGurSOhA==", "signatures": [{"sig": "MEUCIHcm28a53fgPGfE3R3MMAfkM0//q3k5lKEBURDdHgSyVAiEA2vlFiKa1UGLErh1DRh/1gvgZTcbNIRv+M2Kc07VSGgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3941}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-module-imports": "7.0.0-beta.43", "@babel/helper-remap-async-to-generator": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.43_1522687746610_0.4113341635776917", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b91881aa6e1a6bd330be31df43a936feeb145c29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-+LTuKGnAd8w7FV45N+CvNE77GdcVMDT/w7so9++3jbG28w5id6VtQZ9cpFbqdvkZlpTy68Saw9zZcQeRZV3bmg==", "signatures": [{"sig": "MEYCIQDtZ0/3AjXUSsVSkViOr5ZWUyCPTDU0gCj79ElmxU794wIhANMYtCmleicDJm4O+34G4F4L9bxSpzrGwf2DktU+ew2C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4071}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-module-imports": "7.0.0-beta.44", "@babel/helper-remap-async-to-generator": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.44_1522707646566_0.10196714091632653", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "74e8117d145fe8066f74b24a803876cdd72f06dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-Cc2AQpFaZ+sIbf5P/PB8QwKpRuHp2HaqOWyBlLHoxnqlXmK6Q4kaRNEJR/29s+uJqXgHHgDRMs/URxEiK3/6Yw==", "signatures": [{"sig": "MEUCIQDm6Tt05r+XNBeEffWWA6sRXhsXTelh+qjS8b+QBXA9+QIgdLiRAtu+EaLTGjpoYfV0M8pN1umE8rTIFbLtxUJ95B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3oCRA9TVsSAnZWagAAW8oQAJ9u7NNO4zdjOjZY0ssB\nvWxIg/IarPaBw+Um5d9Q5qUUxUUkshxNlEV9t8Gh03+BXEqhxm6UYyaZbGeg\n9XSJy/sHrh0LUS+LL1Gqu8sUCaOQtD/Tvq9xBWZdXelwhMy88n32W7ZIgYrj\noa5OaBApxn3WyG+TWC0BQGnyVFF2958sQnWfxkkfdX4cZLDqEHvXZeXehXxW\nSnRXxuA+g9Sa29/ANknncmanUhc8oxk6NDbmRugwU7dVaT8ir2SjWxzZ1v6x\nNzxH7i0GcJmaNCUbe1o3im96fDiDNzY9uCxgV7SF/PsOvQsDY4bX2wxAJ/U5\nxtI068/4r3Z98Zmkts6sihkAL17igCD6Ie6U1dCzj/ExPzvbrBBv3Q0yBABJ\nDoVYL2YAMtpXmpERn0OIwgWLdwAoBYM3+vzYyfHgyCAxCcMjEyBxCGBH5LAt\norfeM91Tv/pudSYk28rG9x0aQIZK1w+nNteN0kAmMbJFSN4olUG3dCRryMJE\naTJzI+DVTImwIY/GIEoGhkIaYCOzt+ShTRNCTnArNtmyiPxQUmku59EROCL5\ngHB8enfclfx8N/ZIBDT7QNeMLoy+7D0DTvdH31zGGRzAvtuvNWNi/yn+qXY2\n6QVYHpcABQT0z78/2eeJ2kbD5HdeNgE8pcdXJcawKCz6Ey9xvJNzuOfM2rDP\nbZUM\r\n=Mz1T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-module-imports": "7.0.0-beta.45", "@babel/helper-remap-async-to-generator": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.45_1524448744233_0.5557483903538916", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "29fd5967f5056ca80f3a97db4d2ffa38a0dc2dce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-obykYLqAd3tujTjHYE+dln5+nDhm+R5FmUcxXFr/Mx6LK1NgrTQ9TdPPOcMCD08r8SDljFpMopuz9upN/xJlbw==", "signatures": [{"sig": "MEQCIC23bJkr+tYL/IWQFhRTfjniuHEMvpafB66XGkTubaTWAiB7BD6rRzWqIaDjhGM3++rjMKcpFZnazvB7l+w1D0bT6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WIOCRA9TVsSAnZWagAAA3QP/0Uip/yiobm8QrQEF6SN\npPeQ3sRdIz/PJXp+hYAHrNOBXMI6xxTa+sf41t9uM+pVu/u9CJhqCdu8o/F8\nZo1ipwDYrjFsuh/zE+G3GwMtv+bBSSJjGlSzwx19vEAUd1CGC/VsxQigX42S\ndTNtNkBFxOizkqFlbPP5Bg5tNwzciRjcnbdR4zKLYNAD7evVHRYXTVuuU+AD\n7aU6t4F79mi0fmoaVlR1rsu4qlIPRxn1UIz1EeG9yDJ3JYNsvetK5GbzXoPg\nxANGUjd3kRP2ME+JhAZQamEpXYHrPva8X0Ag2rS6G2SAqza5WyODPTEnKPrF\nwyxTlFilL0IFrwbvix8QTRvSlRV5jmJMPo1Am630CaAoO9fQGfM+8/ZNXcDx\nBIqUVpKgj7tRArcH+BkcZn2pk72KUD+EqBbLmQuIMnDHXGWeH9g6Y7MD0qWJ\n+zK/JROjWnHslvRXPKqfW+lbaP4tixpeySjK5qEA1BlPWHRUDELTaiMtE8eK\n+Wu8Q9L0brslfUUDhYQbiheEZd39I94G/CaBCd5svi/3QL2tWpzxcyozVF/v\nrSK4j5bywQ23snsHevKzc6vbALnPtCqEhv2sJ1bvKVV8hTt8Tyeer6YTh9vr\nzpxASLl0CCPTXc7qWOxmtRc1nEBRigLuhDfGH+fheRVNo15FbnjSABsmGfEd\nC+hu\r\n=GfzC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-module-imports": "7.0.0-beta.46", "@babel/helper-remap-async-to-generator": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.46_1524457997998_0.9845652856714857", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5723816ea1e91fa313a84e6ee9cc12ff31d46610", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-/TXK3v6ipvmhMO81Y2Vjc7RYROkS2PcmRc+kvmU3CWA7r5I73KWg10UEW/fpWqCuoTCHHHXu1ZcZ5u+nduJeFw==", "signatures": [{"sig": "MEQCIHDXas8+9Z2MlTgWcpI0HESBXg8FmKfv82jjSxFHGFC5AiA9ow+EjGZQV119loHtwfn/DacKRgivH3U6DnHjaXEnQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+idOCRA9TVsSAnZWagAAuEYP/itLXO2abpgUtAg6sCL3\nvEvTLkJpjRIdNTkP+73oMuAKa36L/QdSc+1tCgnEEcUP707ZLrI7S40R4I+V\n8r8vqaw8Vh3QOOKn68hsSZKeXAPe3H6psrr6FCmoLJBQkNEvoURdZZSzq9qL\nQTEeby3Ew2u4mGND/XsYmJQHiPWrFyNMrvJVJZoGc241UxGXZ6axwDqYsgR1\nMt6wtvT0xmc9PmMUZFwsypVx06XeaxFnfxucVf6OZ44yQuluIq2MG7BdGynS\nw176rFeMLYL/9j+phTAiBe2+2np1IDRefCAncG3BTbe4nliS6DPrQcrfUdTE\n1U0drz82OC8sUZh3I260hvI4aRrDSCppHA2upjV8xPMbG1KL4nPZHTv8TpHa\nhZXgBbfhtgw+8x6hYLLWvB3c3hWihYM4Ncwy6dSA1L0fZ5VIR9Ie0/0z8Fby\nMmmJsr2Z5YXFUEKrfHU3I06MRQYB2Gh0eQNJJWiLl1lhgnOuAZDGdRklGDgZ\n6CCjaR6Sm5ip69cYCwWFl2Pm2m1nMnB22YTJMRF1ubpiqRPd+YZCNvXzXM/m\ne4VgDozSeani4YlcCcMqYPnB7z69/X3QzqTR4qFLzibTzPFDRDNXeZESLsXd\nJykSuCSBEqgyYzQ8aPJJ9sJMMOfH2VOSM6QJpHBkqc0wAqUZvibn3pisldVY\nEgpR\r\n=2I/Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-module-imports": "7.0.0-beta.47", "@babel/helper-remap-async-to-generator": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.47_1526343501486_0.8460529594647881", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3b76d95c3128e8de1c336d39944a6845f3356d42", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-XMwqQtmAYT1luf1WeOgRIIMNmvdqpPoWf3bdcqT8hGRJHTFxWdfkgIW/Zq6KqExnSOHFKmWF9cN7jqf/flQhGQ==", "signatures": [{"sig": "MEYCIQDN6na9z3Bxnh27h7QUjdwEfPx2vwgIGngKKixdgYhKlgIhANRpkyH1KL1cF3oTObnRLF9QpSVoHMGX7VkVTJSnsjQU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGbCRA9TVsSAnZWagAAs9gQAJnkUOpPyj659LRZRyUr\n2/vsB43Kj+iDu74W4ws4EcVw2TOG6zQ8JiIGO1EyjCoTrHoVaBCWsl+yEJnk\nydjkR+lGvIkJGLrPrqYZbzNiSFHEA2QxoxrAH52ePvmKI4FIsmbWsNbVHrT8\nj2LHjLJ5vrsl7nVU06fsUtwsKtYyAgrrATFlD9Lzw+gOyeUktJ9cIsgBOlHU\nIBUI22MZkkRcZlAv4jGTyhbOKnju+xJ3dxdJsRPZr1ylC4Mhns754IUhaZs9\nAHz/x5S1OHdsql9pb+xQqrYvFridPvg6g/WXqG9oa3e7fVHGt+H6hmoL7GAx\nsvpZfdyx76vK05aVS0RIl7B/XOggwiZ1Mt0V2lc/wQve7vLr1eCEVeJFA5c3\ngkYlpXRT74IFDzh+i/IU2IbcWXLTC48Q/HipDe5tjKUusSPpD0hdzId7vc8X\nFaBs2XWhDJreWtqxkI5O//6l4ks9Aw4yHp0LMg9LURhbrzazjzoFjvLIN0Z6\nZXxsSi+hfn4uLgjEs1TcrxVNd8vDsitbQExysx4UHkBHZLvZ+tWRlTxxVruP\nV/a2kWK5OOvzSfK4ukcvf0GpQwZjbsLnGhNRm8IaNUt+7+rmQsVwNkskMCkU\nsAiKgQnaxL7louJe5yIMzaF/6bRmSHmvmf0jWn5VeGmd6P7Zm+9e1uRkgRpU\nDvpJ\r\n=QviQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-module-imports": "7.0.0-beta.48", "@babel/helper-remap-async-to-generator": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.48_1527189914641_0.5939678675292597", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "911a40eb93040186ceb693105ca76def7fe97d03", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-Fknpx9fSJGMrGFd/G2970CxPm0qm3XyF905qwtHw2j3B3BHMTq5y0DbYm5jVfpl7hcNROhrhz3D92kLpt0S7ag==", "signatures": [{"sig": "MEQCIGA459FaMGq+vRrUN5i931IYQzWDHDzq4scrjE5ulB3kAiAQueXtW85KKUKQvMq9QJxl6GoW7kPd4kJukN+fyTvzSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQ5CRA9TVsSAnZWagAAg+EP/2O8qujmtIAXAS6Z7ps0\nG2jmenmOkCuST/aUQI99B6z8AlTwJCN9TNkOfH/NtJs/iCb7oTUM6sWYZ+E6\ntIl/2MaNVDp8k7yPaEr7nKT0bKP6UYIrykfIiqHqHK02We9/lVnzExF4d1ec\nQcIAc1NQsVfPbkd7fH7eI651eZxKxK+LbixKUC9uBxoC/PDwXEKt4iaTfMsB\nBfrhx6v4bKPK2VMYtSI7aCjaB9sbhH2qKhGa3csDQ2G0e5Ks8aRgsWM+ATZk\n2OPha5yDeqfsr2TT0dzlpTiWIP79PDaRj07KvHl0zqoorsZgmpSZb1YSFkmG\nsNopXxCl8oNMlB0VS4wypxfrGHVLxMOH1REVhbH8VVIGBl6bpnoX9ZklX9Pg\nZCr5KSr5tGI0SaVzsUSdAu2zNO8RFfWUEkEqVZ190q9F1vqBBfoD1pRzvVfA\n3f1vjkk5/gPD6PiZXg+89mFUd55YBAccOfyRA7xHjvS6Ek82IN6piqxcy22m\n1XzigtB94z5yx5d08JdbFV8mGKF82U4y0EL33cz0LKtVJAuSyuYNeYRS+aL2\nbYjkjCaQDDUruvOZx5auR+GEWdrGyhiVQmCnbz3NdtdlS3ZrHpwNV8qoa89O\n9xSRZisesxnM6OanccZxtS8HgTgGzvFtDedDo4pX8hG50JHVOrmsMh6TZbC/\nx+ml\r\n=LhlH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "911a40eb93040186ceb693105ca76def7fe97d03", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "3.10.10", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-module-imports": "7.0.0-beta.49", "@babel/helper-remap-async-to-generator": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.49_1527264313263_0.7339709786870166", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c0082d9dbde515ccf597c229bdb07a59c06975f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-TKoQJFimjo+7GzkhDPJq0nizlz3SOAxrI/DF3O5CvYFrXzSLaMMTFiBG9vMAaCYi18AqcXrJLTunouU8r1qkEA==", "signatures": [{"sig": "MEYCIQDIFELy1PJmMir5tCxBOac7+i/Y5ZIx5nYwUR71fWfskwIhAKbLBBMAOmMvaQFHpCDTDNQH2NacX0oUMHbKMUG2drrT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3048}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-module-imports": "7.0.0-beta.50", "@babel/helper-remap-async-to-generator": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.50_1528832903885_0.14760492511191892", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "945385055a2e6d3566bf55af127c8d725cd3a173", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-cAuvGXUZzbrltLLlCt7gTzEhN4hMKsD+8/Mhwwj9FzlllqBTM5yUHoFkYpfEaVZpDjPB6vtqXxw07zQFQy5uug==", "signatures": [{"sig": "MEUCIQCE+f3osa9xD9OUMEkaAY/hDEg61t9bQDZxcepTXkaIVwIgKme878/lPc3hzFuuyqVNXDvBcB+KHxGjiVWcNI7K7zw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3062}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-module-imports": "7.0.0-beta.51", "@babel/helper-remap-async-to-generator": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.51_1528838466519_0.7785623443103167", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "990dc0864a1734d63f138f8e44713f30ad68af3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-1UaiO6n6Ru1Ygeh3h0NlpKD9qL2pwPHiXuWz21SulY3Nf0qhm8PWzhqcsWCZxJyPn2Lz7W9uvm7ZrheH91UQ4w==", "signatures": [{"sig": "MEUCIAkhfBEatmM1Q7Jg7VEK6lwi5bCYDOs0IIIqdOYG5hj2AiEA8g8TTlCYHP2Gzb889M8MuZN1zJIdVGBij4/gIaS9V4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-module-imports": "7.0.0-beta.52", "@babel/helper-remap-async-to-generator": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.52_1530838793795_0.7763562754234954", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "444c761cc4215c97a9b556ff58ca7ba7df5d4153", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-CQQJkRgY/Zt6ezbk4yk1offtvjGzX8MsZC8q39PMj3zKfmawzfRqcBO5O00vw3MgoeAju4/ZZ1gtCzhhNqtgYg==", "signatures": [{"sig": "MEUCIQCAgfjA2JiW1feNuEI+DyR++g7OU/ZkNqTxWHFPzfmArAIgYkH2DJT055GwCU8M3ZhnCXENFn9fTiZoTRNjer2nRJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-module-imports": "7.0.0-beta.53", "@babel/helper-remap-async-to-generator": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.53_1531316456996_0.6401256828333293", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d035e65c50884937d64dbe68d16498c032f8bbec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-dme9ZORc1qxD8y7EEvnbEQgAPyVOKy4moQAP17DwNkUANMgrIEKfclmwdgW/aP/CJb9B8A2qiD5T9GHuHj46AA==", "signatures": [{"sig": "MEQCIELikz0GfI1DOZhphlfFmAEVaU8Rvro5STLkqd2raJsZAiAjBmXkk5e6t8iU4u2TTxxuB/+OsBGhVApMlZ5eCwmisQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-module-imports": "7.0.0-beta.54", "@babel/helper-remap-async-to-generator": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.54_1531764034153_0.7424832097505645", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "490a4715540807bd89f5858e8aac30d1561bdd65", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-oC2KlgbK7aEHj4msythYYlr7i/D/JKqJjnqnWWX4SYQOUgQ4W/9ZRfafby7YW9V+LugPI+oT2JgKRNO8dzilaQ==", "signatures": [{"sig": "MEQCIDPRvfZypvCmm3AcEFBEuMwIvM9c0swsWxhUPDm5PYeUAiAx846oQ8SdtSxpDKMkCyEsifB83wyEZD3PFdl0BBOdMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-module-imports": "7.0.0-beta.55", "@babel/helper-remap-async-to-generator": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.55_1532815718577_0.7809249114863865", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1ce419953fc3c4364ab95442653e4bd460815ed3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-n34TSk3nFLCnsuC5f2PSb+jdOVXv1UzENnGN/Xu9/epSFVVNLfNR2td0Gx0Rh4CjIef5JZ0tFounyPWWMSh/0Q==", "signatures": [{"sig": "MEUCIAqg7b55BM/rP/TPgTZNvu0e/qzjqo8wXbiYKT8BlE+pAiEAjN2GQR51DO3EWUSL2vqoP7cGk6d3CvR+Y76ERQhpywU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPzICRA9TVsSAnZWagAAr0cP/R9fa0URiBeg9Xa+YlNZ\nkcCBb0rQrRA9Uv5ZkKCTjAnut2NiqGL466YvFW3jz1iHGV+Q4hryZlK4VRZc\n6QgAen9hBi7m3pey/KZK0VjmVQ11LP88giGZ9jPh6zwJZmlBIdm8nZzFZh83\nwZLZjPdNJCtW0yjLcDyIhpcCFEKJg5kp8qe/9vd+gy4vAxjo8ApEJRolEKgZ\ngxAuDxc2sOe64072vWjdzgFnn6yv+NWwubsBnR7TP8UFAI2qhAmqjhWojnwJ\n7wGUiuzCQuhIRFmjMGnZ0vAh5Gi01He56Mn99aSjzuHg1SDOaldmJTfCoLTo\nHSXH+E5qrriNdXYK5AOJZ+unt8gQXm8MiHjjiOqWFQ1hz8EnyguexBHxaTvb\niY5tFrS6nueOdIK5MOdkMOMN/M8s791cbhJ/RVaVtnUG02D1/j9FRKg2cNo8\n02kdmMug3k8Bb2/sLEXFrCwhJzzctXntvRiVdAcjz/bzJUJxuAxPHo36TFPc\n3muL7iIdIg+JaAk6zQkD0VcEETccX5JhbvUHT6a/jUCbxmr9nvJlfj6a3y86\nDm+KQaF5YUQ6dhYBB/riwwPTtrN/kIJM/UDcDfibB7NVa8g1gi921SyQobcq\n+ZBBPp/PxCf2M/YIwGS53EUUYPdeWg8PAoV3p4LWvzdHspDApbZBGI/86sVK\nMK9W\r\n=PA8D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-module-imports": "7.0.0-beta.56", "@babel/helper-remap-async-to-generator": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-beta.56_1533344968342_0.17299150043872502", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "39c8254e86e40ffdaf6edb8ded38f466ba993489", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-nG5imb12R6WZkAA/eYDS9QMRyBbmH4+Nt9AlIUP0atBlJf3LF0Oz9tT9MODpiu22/nBSKLkh789QTM8V2x+g9Q==", "signatures": [{"sig": "MEUCIQD+pm8JhNzl+vc2knHlV20I9gsOHB924GRH+W4QbI+ddwIgYaUxCUwEAzPYMt6iaTV87rDBFlqIuzAHkYgmihc7D3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUxCRA9TVsSAnZWagAAS40P/iz1dSh7z+je/wEaqAll\nr3CeWk3k9uXVTmCTZ4fFfP8UGhpXOyXC3hWi9/S6/8vAMXtT7xkH7FOYEr+C\n10apJFvOUYXaIzq5rjhg3pLy3kfchU56H7Fegf8wkK4IQ3D41HB0b0/XhuZn\nN7pXtlyIwbn+SEr5IwQZ0VrXf/WyWFCPZYIpA0J93p+Rktb11KR1cE1nbKqs\nx6PQOJTquDks1rX/1cqHXCAVZSuctFV+Y1Y1Xa9tEfCquR0Dh5OQUXjdxVSD\nTUDA6ziqp1u3D4PURyQL2GlsxEfu1tP5oEH51IYUA0QM5UqjNMn0REox4iY4\nU1lmeoD5t9XPBpAPDDnxwphcbd6Egzbp7qdB+g1a/1Sd0Bp8ZK5SKGPg83x1\nnBLb4ov45RKjFtPR/dVcpOnEGjShTakFA+FCxVk9NCUZ54HToZuDE0uusuUK\naYM3oVB8Bh84AAOf8Xg6TXFjgQVeKAX4rop5Re2FuxEc4xQRuRJv7idHGPCB\n65waA8qyWHsYQCRpeuQrvqYtphPZt/Wm1Ic2JCAylA5TQiD35dosfrwH+F+8\nboIGXBF5Mqvl/VWQv2/Xkx9d3dZTT9UWxqGjsNk8xk9p6BXR48CbTj7XHLP7\nXHP9s0uxqvLOzdbMZ3swptpR9W7F8Gk8ala9RDhwpOQOA9dr6Gu5MIwj5U04\nTaEv\r\n=4G2M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-module-imports": "7.0.0-rc.0", "@babel/helper-remap-async-to-generator": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-rc.0_1533830449028_0.9632335677275607", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9e22abec137ded152e83c3aebb4d4fb1ad7cba59", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-8oE9Frx07ILINop9hOejXgcDVhmt4FuB3ZjXnIMcSMkAuiT3xLrxFMDo1Qo0kf5mty2jLlnOO6tbbH0kiIWxWA==", "signatures": [{"sig": "MEYCIQDFb3+J0rqia4yT1A+k2aUSzCcUNP5G9exa5GtizUCzygIhAODMDkc5M/KaiFn+1tYnSbfHHUFzq06qyJ4fHiHBuvnK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+sCRA9TVsSAnZWagAAv1MP/iqoV3cz2/DXNKjvq4on\nSM/LE/UM7/ydarIolhoFcT4woswTM1o2K5WIv0gMcjPAgFRrFw6JEtnJxta5\nT90s8QmXoHbfJOqgkAEP9UY7I53Lhw4kKZaKVVB0zq5ymJAhr32fE9LTzEKd\nvJy7nQK1CpYnneBiyXhTFyIK30zhQwz8m5Pg5ofMnGxEy/SJDbeyi1Wy3jEY\nY7kH6FBwYfFGS6T6vaGzWQAHHSAijXtduJm02SUJdwOt+zNVo05lDIngGeqP\nmqWojEhnPUS/HA4Rq/4uqt4kIgmDvtZhuS9FmEgndVwj4SiRiMJa4BNnUkdn\nfJer6KYCdHR6/WQSHqnQM6EkffDh9igWblUBx0IUxx3ocXDUWfwynv4foN3P\nuavrFDm2N//XlE6vtEQ0zHaWe+zywi/2bm5O1ayEI+Tyn80inQaaFEyuIWX0\nJXXvl691Q8JEBOpAPgFY273s7APSwCkeRv4429B3teI2qcxvyBOfXSkvmvbr\nSs5SoUT4aa7DVWxpM0PySwNBOwgpVP1RfvKFFI/NyY6DyQpsyg6VOiBo2lVw\nuEmea9UZ+mLaVwLtZXe1VCYczEnyKB4PX+Ks5pHOiNqkKocsXQPMyFHq1mTB\n49CT/cYQogVC0AfC44yKNX2AzuJle9hMi6+F5HXKS3pEbPX4ph35N+jHY+2k\n0f1F\r\n=drvM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-module-imports": "7.0.0-rc.1", "@babel/helper-remap-async-to-generator": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-rc.1_1533845419869_0.44819339994222007", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "44a125e68baf24d617a9e48a4fc518371633ebf3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-NIRF0AbXilqtr8VuTNqhQUgKGeOVu2AosUxJxAQ0e0NaxnNMRWdVTVj4p/Pl1FuSuA5oftDbSGeQYi3VOFktpg==", "signatures": [{"sig": "MEYCIQDHHovBdyglfORnBkqoxnkbkF4s21KoVdrIRAViMk/9twIhAKS/Guz+vvvamn+KUp4QRBeCd8LvgWnnFdfKX3QHEeee", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdiCRA9TVsSAnZWagAAKFIP/iDpGPbb7i3A7KA6zNq1\nJ9nFb9cpBDAXUu29oMZczzNjpZ3rKjNUFfCelzqn9Rk80lPQb7MTN5tIlOAZ\nksfs6HpFjHWk1BzpJxnsvu8x/gpZD2J9xD7lGZQTnRqEQ2TYx9QyMe8Sjk4g\nK3E82AZ/JLDQV4h50Ezp61lpirdpEitCQdBnDKItd3caAe85D3mo4ruCG4+a\nYWh7LOVAF2QkwzdQoCvjy1QYoQ8pZGr4gyoh6VbyGaPE+qHqDolgQU2g/0la\nFKSDGxLMggx5Tmk342oUfEik87x26qhLHcXnkzxrPG4028ZTXkdpx8oGVrIg\nXHpK2XcuiVJY8JAtau6BTlY9kw23SisEbNywLbbuu3I+nu5wzGnkaHBOMqWf\nFvhRyYCmrsVFm5EXLtyD8wyVxZwndpqaB6Dl9M4qLW9r40d300n0+1qD5sRt\nArOYBi/wLmMyCc3DHrcn/Maoz7gRVzqsn52d8rSdiKXV8FQ9finhmHXQFi1h\ndmgHLc76MU+eRdhFYMK6SqMWYR1VdsABFEUlYKXMOAYqIbxWkumkB4JP9LjR\nbNEq4UTmyJaumk7mI9UdnLRDWL8+Ic2FZg5X+l7A5jjDiWKebROTVKfbfr2t\nGgIyqCqznyfl+WVAJ1jGfhgFpmXtQZ1p6HYAXzuLMRuG6yzHbDTYs3JW+z5+\n2HRk\r\n=tK8l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-module-imports": "7.0.0-rc.2", "@babel/helper-remap-async-to-generator": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-rc.2_1534879586179_0.15106771095491434", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "df63901f6071324f1b2ac5b5b36fc79108772a98", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-30r5IHgbRxqti6UKqm89oi4p7fMJv2LI8z8lYjgfcl5FDbRk3Rmho1NMzFmzk3VgaKJ3wO+NqTNI9C72VV7WJQ==", "signatures": [{"sig": "MEYCIQCIHc50EBXmiNXDvjEMY53Asm47tcFV7OI5+BDE7hfLvwIhAIXYKxFPKjhGTsviS2gy8DaCPIeEnqU4aJLXAjVqztx9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEoBCRA9TVsSAnZWagAAg74P/1D/Cv1KIggJZdYuWHw1\nkIwYjJd6jvhQn1z0OeR246zf2VPgYBMhzlJq15YONqa1Jd1BiedjiLBhnWuF\nX+vYNLpo7Jc4B2BiXwUSiQkskjFe8QhAdbhr1F8e+gbZmnVDpVB5s0BBwOfb\nD5/m9dGBgUan56uqrPjxRdTpoVIgKQVPwx7pUIUq3y+j5AkRbLEc5Gbfgcpd\n6neMQDx2RxDBvQ03qLFfV54lbFJtz7AAZA6MyrxPP3a8wwzli5JjdBTj07aR\nvtP7sTnXtSj0bo8qEHotBUPd0KOdnrjl0V9jo87xwMfm49tyGP5HvTco/+/A\nMNJnlpQGEq07qIeraGHWDafkmZiqZ6riv8l0SVHvZIp74NU2wVSZ3PAV53ku\nU6v4qJmixPY/bUXIw0HWm7hv+jnr7w5T7gOKmVuSlpjoz63mbhRDhGvUkY4P\nzGQ0MD7pw2PnrWMlITtzxIEMwX0kQ8q4BJLf7f6KLvLdg1EDvCGnwaxheBaE\nfOTRGLv3BHaH1mZ3oeW92vU1haEUS76DcqSPETRQrL/o6rDyee3SRPjMQhWT\nJt1NRWgLYTE/mB/lFa4IF1jszsnSxQ/ALcJQItdO2F53ZIuOrdiGBfiTxyTv\nrPBSRb7NFo64YKQ+bjXi2D80KIFQVZmmpA/YnpX23tJmKEmJgMAiXThEHno5\nCJp/\r\n=UuQ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-module-imports": "7.0.0-rc.3", "@babel/helper-remap-async-to-generator": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-rc.3_1535134208856_0.8798469814917118", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8b3ea951621eecc4702ebec465484e566e98ca58", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0-rc.4.tgz", "fileCount": 4, "integrity": "sha512-AivcvSWzUQd/iWAgO1S+GB5zJeiDDgT0zPL1sjuSTmthQsWQg6HTneKF6d9IEAJ9m3WPVlmKHcASPHSfO0wU2A==", "signatures": [{"sig": "MEUCIQC/xCH/hchLQDSEETF3VE/mBcH3iLrzTtZuTi19Rs5CRQIgW0t9prCTpPteOZuBrMA/l2BlVAOjziimIkOjOvq6E2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhC+qCRA9TVsSAnZWagAAjOQP/0BPltZyaI2y1gNv3au7\nSb8TfxHno7xBg4r+HwoKToHOZyICr/1/xEJ4mFyoCBFeIbe5OQuvsDN5kO4G\nmJWXwpX9iLfkof0ZSHTiAfOSUHFcCo+d8yTVAhrDG6h3+NrjW0K6K/4tfts8\nTwsyrshF/T/M55lRl5rW79+KqYQylGbv0P0ewFaShilqVo2FKwcX8PoJZxze\nB7qaR6YCQ+Ub0FhNXQ7Fwn0H+8jZm0jcH1eTllaxy09FuDismuXwWXTtMu4h\nM44hzBB/mVGHy4l2aBFmArfblsD/3of7rcO96sQ0DBXGm/1lja19krNai2Gh\nCG3nNNfW6ScdCIpQEpXf2LYAE5kJbZcQ9WjFiIRZHLnQTILXlfsC8On1t75C\n14Hwms46125/T24zU3JKHeUkxbxkQPt0UkT16LshRGai8B6vn7Ef+2kZojWH\neLAp53wjN3NHa45wbbrLDixUIVnpf3bJE9gtzVIZgzQPsy6BRr2QULGV2uMQ\nO4IXD2JgxlJkVgzIaJnBn+gL6/muEmcPQz2sCk+vzVLyg3ImCmyqBx+34faL\nOh6mGNmUAmJObgK+8A0Z2/U58AqBxvFB6yHfSSgaVlzXV9jsziRPmn1Y5SEE\n5jeMq+mOHSssfnqg67JUUVd7XVy9sAe8iDvwzOmZkGCK/DWmfLtT67IdON0R\n/Tut\r\n=eZgs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-module-imports": "^7.0.0-rc.4", "@babel/helper-remap-async-to-generator": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0-rc.4_1535389609507_0.515149979545142", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "feaf18f4bfeaf2236eea4b2d4879da83006cc8f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-CiWNhSMZzj1n3uEKUUS/oL+a7Xi8hnPQB6GpC1WfL/ZYvxBLDBn14sHMo5EyOaeArccSonyk5jFIKMRRbrHOnQ==", "signatures": [{"sig": "MEUCIBKteygpU/jIF1/faGeUV1IXF3gfasLSQUWz3Tkvxw9dAiEAkTPhWAYOFXvbSp6X79M9qPxsOmumbIEEg3kgJ4eZ6zE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDzCRA9TVsSAnZWagAAyjEP/j/MbAonTGLxPE+6nnN8\nJcrombq3zVm7FTtoljmZkgYVZjO+UbgApll9dYsKrGb+6VZJd6kRjrr2cn0G\nQEFWUaawrWaV9zkXIM9G9BMUAma8r7ZgP/PuVCShvN5p3NrXVtrQ3Zl+EuAq\nd218+LUaO7EReIaSCJZ7dtCozGoNljuQ9w6jk96iI5R2CV4yMVEXbJPo7ZO1\nTf2pU9Ulo57Zwc2a7an3yjK/EDKCWhew7ldD8kuagylRuQ32+gRBC07EfuVt\nQIRYL4s/QhvBrpwAkgvqcWGymOpIfm+a9sVPmVnsWZ3e4bDPEH5GJ86SVbsC\nN7RaURyFDttmANCnoDXHxSxFeO4Rk9aFTYmSthWUnDDqZl/wat2nOkVK9inw\ngViWH6DE0DbwGEB4ahAUhfn1gEvGtkaqrPCfZXu3jmTpuCTVKYqIAqEdJ8pX\nF3O0mrMcLfiVmZHVA6qKDWktmgGdsM1cwP7UVI53GS8B3gCrjz8g2fh5uC49\nzeyqtdhE97ChDlu1lKcSBlvmI85jHwnB8ZqO5DUL6p/muLNsMEjwj0HCb7iZ\nm70GoC3lN80JP263uOqnXvLZ7vzn96YmkRgm773/f6UQklb9odOWAb/TUPDz\njqSYOfVCx/r79xK4dEUaSe8Rxf1YLSQpq20/nw8hMnyacptTjTJtLPz2RV5f\nkHPM\r\n=zBkV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.0.0_1535406322377_0.5794465229131087", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "109e036496c51dd65857e16acab3bafdf3c57811", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-rNmcmoQ78IrvNCIt/R9U+cixUHeYAzgusTFgIAv+wQb9HJU4szhpDD6e5GCACmj/JP5KxuCwM96bX3L9v4ZN/g==", "signatures": [{"sig": "MEYCIQCZEh7oUWRDViedIRdFNNvwW/iZM37zrPc+opwl0APLXgIhAI27Ex7UjIVdrlssaFlzMIpCedqIJq1bLUAN9eC1raM2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAEiCRA9TVsSAnZWagAAkpEP/1YhBBC0+EjfBTWuRov/\nlFRIiby+zNv4N5rR5iDKUVtVF7yUF3hrtybGMsRro7H/H3BhISveh/rR80Co\nnsEh1QSIHD5X/t4TbyVV8IMWEYTci4V5a+PqRiK4TXRm6peK37mkNyT8zOZB\njWQOOzlqx3PCAmIOGeSDOl+qot9tqhur9fo8M0NQy1nzJdB4cjfkqJTYe318\nemI1YDLlxcP5lJ2ppzQVg9QQhHwUUus6NY1OuCy/x2MFB4/hTrbVgHNEzkce\nP8G3H7rtxEmDbnUvFNC0Ln12URXiWCh5Kvu5mRraB0GrEXrfEgtKPC/doj+i\nEKfcz9A4tiJspCcxW2Rx5wBsUxMZ8fFdTL0n4evikv/5cpDCm0Pmlt6EMvBX\nzSMWkYIx9uKyOD83G7n9J+R+YlL76XdTp3bDdZMLV8wiIV4h+Vf2wyyuUPr4\nnA5LI17naWk19fYpUizmcBve4gkI6woykaVC497OFm6dU2BmzCDiyusRGLSf\nulOnlKdblZCzrzF/cRlGj+x/YaGy2coEdUciCaDP5Tb2mgF7iFZYVbNMVAwU\nnk5vu2WjHfwXb8Dz/PdwhxuP/+Crr0dLp52QajU+UviVSHBBfvbhZgiFwvpB\nfI7GwDQhhkhueyXBTEdrcSwSuMKF5dbzVZirz0V2PCfXg8nFNPW8sXo9D4cn\nIMr6\r\n=V9kb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.1.0_1537212706029_0.008988289785830661", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "68b8a438663e88519e65b776f8938f3445b1a2ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-CEHzg4g5UraReozI9D4fblBYABs7IM6UerAVG7EJVrTLC5keh00aEuLUT+O40+mJCEzaXkYfTCUKIyeDfMOFFQ==", "signatures": [{"sig": "MEUCIECuWnIP1YhDK29WYAU7S1pknsffdI+UTtgSW80YAQxZAiEA03/IN81QkNXNIrKKSlQA1/jcac2B9x+MOSSFBt1WhnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX10CRA9TVsSAnZWagAA1hcQAJJaJ40p8K7CdSNeBnOD\nMlQfFfVV/PYWw+zwlgoyYwEeIBf5avcfg0/m9dmj+rpaJQyhdGZduIXcWfDD\nc2ro/VtM6/dn96mqZgOw06VvL9fmFLoq2C5B0gBECXNBQOb7Ara35ptOJR04\nYmrOjGwdRn/EvESi464fjOubE398go86i+E9G2YlBaKJkieMXO0NwJsCgPaR\noOp6JjFMmkHVuMCUdxF2mgR1DD+oZboS2rjHeHUEOj177Rn+rIh53sBteGmg\nqwHtnRpw5mvjXOVJsIqWaLOcAmRrvzBe6Su4zaZX8F8YQ4iNo2pj5dC1MgoH\nxl/MdSyHwIeSEjDKo1wQQna542wpd+7efphGxPLafd2ihclXDQYPa4HU7K+i\nPAAtt7pWtg5yLzSlNKaYeBXK6zuj9tKP8wl8FB3Dm+S177gT3iEPzMO4IZI8\n3MmA6HoEgF/SZhfx7GR1/WizJyWHr5AQBhL8SO8jaHJPMbumFTeMOwu+m3bg\n+r85XAZ2C0X9V+7aZB7bQacTZ9wq6qPxcF6S5D5OUDXVwyk8w/LWuEaa4STh\ntgaNmyjAtgf3GKQSybq5TWTm8Ff/LIggtEy32fQTAenm9R2QTNlSfk1O4s2D\nxfzyqd9YjN3IXAoMbPvvvfwWfRtygkwn7WNn/J+m4aDQHOG23hvCHoJhjwAv\nRmke\r\n=gvUf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.2.0_1543863668211_0.06733224392659687", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.3.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4e45408d3c3da231c0e7b823f407a53a7eb3048c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.3.4.tgz", "fileCount": 4, "integrity": "sha512-Y7nCzv2fw/jEZ9f678MuKdMo99MFDJMT/PvD9LisrR5JDFcJH6vYeH6RnjVt3p5tceyGRvTtEN0VOlU+rgHZjA==", "signatures": [{"sig": "MEUCIQD116bJu5kNAoijZvsA13w+7jXpr7eV5qZZxbVnP418TQIgN1gUYRMUu9cDmV1RzX2TPuN+QQgaSFBNUr0r9WcCIzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDVlCRA9TVsSAnZWagAAohYP/iTveoOBy4mKPbe+V07C\n6jaBjPcSDPcq1UI5CQCDAAMiOC+by2GemGLIq0CXlmpTzoyLdMkMrgj0l57M\n2r2FLCh4fy2//mzOuUPsbp5iTuiXk5FYsVKA3T86D2o7KHd1BxEf5mIBNFa0\n6pnnvUEChcqcaw/kg4oq94jds0geFOnbB8WifsWPMWpMyOGCaYzfXqTCksXm\n/NBofT+WPoNZOVzkrco0jmjKATgA3eP+RmkwMZ/1XzWY37uXz9lbWLb8+CCh\nXHwUM/0Ov5mkIKDNHoLjp8wWQPZ91MTAzNMDvfJhOaV+Arg0KJpBXW2lvDME\nA8mDidhpDXn4cYIGyCKcwFN0BvqOKbSVReYcF+qYyPyF4A6DxJ1hy/owRQx2\nyef2IwhynZHz4ds7xzJHfg3d6ilfEuNAQ7AqY5J8ccAAqKLKX46GXf+tudAQ\n00K6Z4Iu7URHjSZODanPUU4wU5XyZ00SbR1QBhp2D5/n6iCwU4uldsxDFERe\ntRJ9x9zc2eyNgdmJH2a1TVKQePIb1DouninMXNtSbkHZYX9lA7Y3hETbvY/g\n1/MsRy2fDXX9T267/WVwvL3a54jKsYtY/aAmVynTKj33GWCMG87bHvUl5tiQ\n2NGPFc4PnrJgrHDrPaVFt6akP/GQs278w9fGfpj1HW0PPh3gdVJEN8aq4Qke\ntXhu\r\n=vrhT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.3.4_1551119717008_0.5378310236783115", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "234fe3e458dce95865c0d152d256119b237834b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-EeaFdCeUULM+GPFEsf7pFcNSxM7hYjoj5fiYbyuiXobW4JhFnjAv9OWzNwHyHcKoPNpAfeRDuW6VyaXEDUBa7g==", "signatures": [{"sig": "MEYCIQDIWQdW+0fTVXGDlsiaIotUv1clFPcRDkFBFj1GxyTQnQIhAJedGmIVDabVYKSl3OKCyM4FRY0xPJx1rh6Zi19liJom", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVSlCRA9TVsSAnZWagAAeDsP/1ThVZa0KNiEMDD2AnBD\nfx6w0MtV4UEld4ajvZzSCabop4wj/yg7BdIaXHdbLUbIepPg9+L0gGJf0l1r\nB2fsDVo9U/r5Xs435DzkZHd8NeGBd6CzBsAEF902YtUUSUVLVfe4vmeJirSn\naO8H5jxqSbEBxMvF4p6Fjc/jd79r5IcjEXRuwaHZ2tz2x6GVoVIZOTleG3oK\nNTSF+AGtDkcWIJvlWZTEM/CGzxIym4gLCyiZD3O+3MEdRK89Mf0hZZOv6rcZ\nVI54MQnsEebvt+KiDDPq+pn8hi9Fv3ljt+qfc9nf9fh9IQxia4KIuvSY8ZK1\ne0ZYjPdS+MJHYTvl9yJJtnXRVcMWZMnQmU1/z1bS+tUY49vwbv0aJGCfiJ3U\naQ4JPiiD1OkBtuiD0GERq00nicvMr8fqL8Agj2wDBwDoh/knhYuf8vcmG32J\ncfyutoPe/B+BAiKxEtwdteYJYlrEJhAjhUzPWymVPZUO0ehcPzZ9GBFUshod\nU3QO2rqoER0JpsdxvBu3AqhWAKWUSKaW9R5kboj/HgrnUJJeTIFJOfm/o7Vh\nIDq33aHNqPJx5HjOjDwP1lAtYU3+v9LDSdxGqK0Zs4f3F+sKJjdetxZ/K+kr\nBHks/jg9DFKcKahBxQjR0D4oVrVfKPRitWVuszw9WKgQsqsfxP36Y9oLpfvK\n9CQX\r\n=2S2l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.4.0_1553028261064_0.5695420815462426", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a3f1d01f2f21cadab20b33a82133116f14fb5894", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-YiqW2Li8TXmzgbXw+STsSqPBPFnGviiaSp6CYOq55X8GQ2SGVLrXB6pNid8HkqkZAzOH6knbai3snhP7v0fNwA==", "signatures": [{"sig": "MEYCIQDMGc6zTHnw7j/emSWeLDQGF5kKYfrpiGLK8kvmZFsh3AIhAKjBD/tt/sdfLPs1/ZUgAz5sBz+NM6kv1eDb0fFAyHlq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3I2CRA9TVsSAnZWagAALxIP/1UgxhX9opN8Y8Hj7rsz\nqNLyy1/yGyilqg7eIJ2OEbFKaRrfiMv5CmNc1RmX9TG0PE7M9TNhb4gSKMB7\nycdduXSJHbso0r7GEZdXHqv3BxD9UEXSrw/ylH4CXxs3CsbaY1j17/OMl9Ob\nv4LJfw+6tzugXP7KfeneXIq67aXzbr7kFRUet8NQtQMlLmDA+BdK30kzr5pw\nHYqR4T8xRY8i2lr2vJ7p02gnEnjP/13EkTIIqAPQ8w6W0C+aZ7VKK7w6pPf3\n32lei0WhGv/RqKefpHwqXY8Xlmf+c6JW4F5obHDZ0nNB5bd90YsJ1O0mHzaf\ncPBcwQ3Euk72K5OB5kGI/0d2qItV4/41/wpEGK8I/pPM9/EMwVqBqbRx5Noi\ny8x4Po51bZE7dbAjq8ggEOjF7H8lgYqAB1NjmOx26tVH4nexv3ekNuSDpCP3\nTwUvheLTWEHRaZZ6oHVMR95PaBPVFwgWcJ8MyruNsUOQb6YTz4d96eLb11cX\nLAvTFThNnF8pG4wo5ZufP+SYDFIPYgHG7cr+BetNdt6MsTLPqXT3C+ksY/x8\nrhDG/GUoYhfq1u2Zy4IOVkysy+A6Hlps3giRoH7OtTxQ30+Lzwqhyy9NRPVs\nvJDto5k6F4PwpL8NldI3iHYg3J1KqO9Z1oyB6C90sMaoQhoa1pBePnxgeUsE\nSJyI\r\n=yr/K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.4.4_1556312630221_0.10712700147993348", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "89a3848a0166623b5bc481164b5936ab947e887e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.5.0.tgz", "fileCount": 4, "integrity": "sha512-mqvkzwIGkq0bEF1zLRRiTdjfomZJDV33AH3oQzHVGkI2VzEmXLpKKOBvEVaFZBJdN0XTyH38s9j/Kiqr68dggg==", "signatures": [{"sig": "MEUCIBziRq/oedAzBEiIM8MSVRF9R+jPOiy7AUQV5ooQ+etBAiEAvemM8yoEovEAPuQnwp18ur+0tnrDsHo5eWOK+ZJylfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffRCRA9TVsSAnZWagAAOXoP/RwZII97r+NW80Gc/AuB\nIOxX06MXOqu5vm2PhluaP+/F0uJ3IPXPP1YOegc2nS6Xg4TinSin4MxbXQoJ\n0v2MsxwePCwg0z3N8uxLDqLPOAbMfKMlMxMMH3wItmwoCR2RSGWaOvLgTXbB\nTCOaD+BsZ/f07wufwzpzDma/dFCPllIzWdjRjIWR08bNvMIAJluKw/jt6Cn/\nfaS7Q2NC94WhOZJqjj2EKztRKlL9HAJU7bLgg8ZLMYgWunqeJeDZUktL46BU\nwMkTtowsNkJjDLU//JK9b+h28LWZkC0fpAiihwR64mJtcs4hPen7q9Jkg<PERSON><PERSON>\nYaPQeUr8xoAsLvEzrSvGMDgq2tRWQyo5GCYm3QahAfUCqa4DSkMxD41c0aOu\npIudEohDH4eeMi30q93ye/GcJsIWZQVnWCbP8jlAfmEWstyC9kqQXuB8Jwma\neZ78WydcHwbg/R5d3BA09n6PohpMO0tW1SUQxeFm4mTqQsH7tVhgsH3Gu8zK\ndkGJGpL6RvBBcVIOUX28JTSf5G+aagccFlENExwZKeSIzFOrxamFNguhZWFL\nlK9Zdkc5KWTqrrMyE1KS3R3EF8DMfpD2XS8U4hk+HmYD0nXDHC1styeiFCUU\nNPllqUGLzJ2fDR4NnneLtUbb5g5M0JkZyulBi8Krq7sjcW06pb5Bai/zUBcV\nnFh0\r\n=mgHG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.5.0_1562245072591_0.5732494146236902", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e2b84f11952cf5913fe3438b7d2585042772f492", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-vLI2EFLVvRBL3d8roAMqtVY0Bm9C1QzLkdS57hiKrjUBSqsQYrBsMCeOg/0KK7B0eK9V71J5mWcha9yyoI2tZw==", "signatures": [{"sig": "MEQCIEKYvyPFU+5+Qx832F5NTuTS3Q+WkboN2ZckpL95T0TOAiBGYsKbztr7km/3gmqAph3OXsDXcJazcYcnUDCJGTJqvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTQCRA9TVsSAnZWagAAc0YP/j8yNShTLNl42XQxOO6K\nXZxfwEFm4g83YieAbWmQpuGXPU5HtHiqCAFN8/hFpbFCEsf0PPme/npMMgnI\nP+4qsLV1in5xL4bUw3Ud360FyYizL5LDDWsDHv1yPmiuMwSg57ssESp6SP1X\nZCACOaCRnfCLyQRuU8RPvSvpsUeEynuyx4TIwsWcPM98buERlC1VVPC/786m\nmP9/ScwIs/s7QcO2JZCeB4rSmTgBkvMwODgA4xU7LTep7jYjbOb2gFLH2aUm\n0aFAuh6G1ZIWgWYdvGXDstupzRxOmOiIpNPNuQJtt/NgDNN6U34jZvn6hlaC\n713G7faGacBSI/FfBP1m8S8FcL3RIQrBYQIY32MUGxZC1SfQJ0jORpR/Sc0a\nhztXKwDepqx+NoxpbYd2sUPKJSM1hzyVz5O0M8o1bhrILQ7FVft5sv63WFzx\nsYT7b2xl1bpIm6wWARasYT0T8aiVPCVQIx1HOi4j5P4iQh5Ryoehnbr1LXD2\n6E1CNJcfOauo7le8EiO3w3X5NKPMLpOGM0RKYpk6CQBRj9sxJwcCx6vbnrMV\nYZt8+/h4Ij/tb2jAGdogPPRgN0uHdoHXxEjxw5e/ffzUwtuTvmA5BGHRXyVh\nWncLzzKe2lTG7xoFG9IN0woJygZegH+yOrwbKYG5L+0PxPy+bOfAmGh/6Yy1\nDFin\r\n=Ga1C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.7.0", "@babel/helper-remap-async-to-generator": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.7.0_1572951248154_0.48421932404098866", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "694cbeae6d613a34ef0292713fa42fb45c4470ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-zpUTZphp5nHokuy8yLlyafxCJ0rSlFoSHypTUWgpdwoDXWQcseaect7cJ8Ppk6nunOM6+5rPMkod4OYKPR5MUg==", "signatures": [{"sig": "MEQCIAKMqWv+RqozFWnMW2AN305cKPmipkIluaqLXVlw0JETAiBF/IRmOzLwfu00loJJaorqjZrDB1ZbE8JwkM83rjr+3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBtCRA9TVsSAnZWagAAVmwP/jxKq4dOQEcT6x1dRDr4\nrHcitOhbjEYdhnJiLIaipJQAdKUeTGk1GnxyZENAGAN48igFki5OlWgwEi3G\n5ef4aueCbsklTpz6KlV9C8ssiqBFy6XqYw8vEn6KEdx1Zr/O37e1Pl60n9Xu\niyqh42255O+PBxvPTpmN4NJTnVI42cqB8mMfZCuaBTZwlLRG7f0AjcQlZksT\nJUPVQFT3hzxbyNQVKragU3vgUGoAE/nvG/k9ORz7NRH5cUeMBb/+DyGGPMhc\nf9Vz7LiNFRCUVJDVg6dnIh9BLRHF1fDEmhefasjQ02V9PSgWLMjrJkSPxqWO\niP6zlegKSBdDV3G73p0ioB2WM/Q9s7b/veesvgYY3H0cWKz9/SQmN3OTSAyo\nD5cqFl1q7SFI4ZFPzpJXsnfmpKc7qMtm+LBdIUIRXeM7SRf2bZP7pdLJKh8y\n8EOh6b0f4SkTtq2Vwk458PVTpg1MEjHoG1+pQZQIe/bZHh3uTSD2+PR82W2P\nZwF94VamyWcMiTJr1XDmqol5mEm6dbYU2jjKYXlnpHP1Pq3c/75SCTxq7zSL\n7ZtrHC4GmAMKJz4FavgGbGHPSdhQsCvKHU015KBWh+Gw8Ki4NWzVnvfCbpv1\n0Cf8sxlsHYFe3XwjLNkMIkK1aLegie4rEWzfUxVuPiCJhXqSsjfacnqPGbia\nRa5m\r\n=RB8Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-imports": "^7.7.4", "@babel/helper-remap-async-to-generator": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.7.4_1574465645536_0.9351964151981373", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6561fb6445d89bc20b72150430944cad0e501e4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-9dvBvJnEdsDWYMrykoMyLNVRPGoub6SFlARtsYgSQ1riTjnyBjhctihSME4XsSku86F59PDeFpC9PCU+9I154w==", "signatures": [{"sig": "MEQCIHJPdBbi/ZwLudrx1/pkHzu6eSZd/xdBiWqk4Sp6gB7QAiAZGvMwlMU6NnQktzGfx+nhNiqB+2OB521hPDUGj1Figg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWnCRA9TVsSAnZWagAARQMP/ApYwDjNgoOKWLEqB1RU\naI39B9Hl6LXk7jssamPdBtk5xWFwYPOS0vJdwUV0BZPFNN/C19IvEhqdQa7L\nP6YA5FlwqmOxKqKwrhzd5B0clQNFOYwEwacj8SuJhrqI0fGUEwtxFqAQ5ixm\n27lNtl9fHZvScfnYz/p9kMztF1l93+gJijJAHY7tbjpU1ItW0n02H4AI2CMF\nV4Lohb4IduFQSzmCwBtgKoXr70+ZIq7N8YvpkquvhwZMkxWgthsihAZPCy6V\nNRY11GgOsmdK+GQtzytf6x6BpLyx51dXQnqshXL6wI6QTshHBAq0gNKm0A5F\nh574S4ljRomUW3/vg6lshtPiJwtgyx5UwjbvTqhnort2it/pbvYST1ciGK3I\nXYpNcS0C9UBRX2RhPTv/RVZfZMoESnr70ssD1U5IBT1O51xczARBWliVGkhq\nHzCCXJLWk2VJg+i7lhEtbfSipEGvXs6T9AEhpPAsST3BEEwoZcnzWb/xsnj2\nuhpSJnAzMSKFePMzrwuAU1jCGzQsczbekYvx/Hz/7rOPhQaYs1nYh8ax4dQT\n1xDBVTduMxzvKg/N2LThM3zkTeTTsUdHGek4MykkuDw+hwky9bWXYSUV1wLs\nG+0hNkBqCVEd64qjcBfghTaRGhLWHE08PZ44aFy2HKqhMCwITbKn/vuz1dfm\nvRQH\r\n=yf3v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-module-imports": "^7.8.0", "@babel/helper-remap-async-to-generator": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.8.0_1578788263359_0.23797939765309817", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4308fad0d9409d71eafb9b1a6ee35f9d64b64086", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-imt9tFLD9ogt56Dd5CI/6XgpukMwd/fLGSrix2httihVe7LOGVPhyhMh1BU5kDM7iHD08i8uUtmV2sWaBFlHVQ==", "signatures": [{"sig": "MEUCIQC8KtfPc/or8vrchUkcpMU8T2+lRP5p5qHORyL4aSKU4AIgdos9I/9BE/KD0V6HczhqOZdp3TWjmDOy+nz/zgEJpEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORTCRA9TVsSAnZWagAANK4P/jLPJpN7qwFIVEwF3Eh9\ni0gIDg8Bf0M6ZlerSleLcI+WesFm83muWo50QFiRtVSDsIaBBnQQ7Obw8b8e\nN1de5LSh091ZzmPgiwQ1ICpMp04BR91UR5mkRbyaXH2Zn/wI2NWu4PJstGn2\nN3T5e1od5EQR7tj/WF4Eppmr2gtus7P6aCYBGP2L5CTg9V75CM+LHXrpWkpc\nGsesb0XiJoGJVszj3r1GbtxcMKFaJhOqeaZzzF7FsysU8LmJLBABzABXHf6N\nZ/ZlHm3very9sIcZ4m+od+MNYW61SkZQX9p7lt9X/CgaMTlZ3eB/YYcgwUgm\nyesN4iBHWM7lL4+e24vNnHwVFk0036V+Q1ZiDR/P94pNRvKu6AnVBxnxAZA8\nUT4G+14i6Yl59wnCq3dqWAUwoZM4SjLlJyDeNxzgXTv0f+5ae1VjZ15YhvUw\n9T2TImtH7jEKne7n5wS4/Xhu1nGwLoLDE/SJNEcsTkYZmw+IIw9VLr8hh5nn\nxekbweEHfk+PZDJO0Z8MAD96aCNgK40deLdEqOqJzEup5uddpbvKoFiLwBf/\nq3yLFxN8DSwH9MA3aqCBObVPrr7+eJ1dLM0Ew/2lYPU2pooAobYhyy2cKqFp\nnN+1MnJU0AZVzLxEnRzPGk4KIeK0b12OZxleFcuApQOBjJPgVM5f0SWdv2+E\nJJDG\r\n=peBH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-to-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-imports": "^7.8.3", "@babel/helper-remap-async-to-generator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.8.3_1578951763574_0.6897127083244603", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e5153eb1a3e028f79194ed8a7a4bf55f862b2062", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-XCgYjJ8TY2slj6SReBUyamJn3k2JLUIiiR5b6t1mNCMSvv7yx+jJpaewakikp0uWFQSF7ChPPoe3dHmXLpISkg==", "signatures": [{"sig": "MEUCIQDTETEHmSRPKW0a6jkkzUq0VhA4nOfcIe5L2Xisxw3IWwIgVLXTdEMqYjLoCgqohzvVX3gKkuRVITQnk8hb01kF+6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTbCRA9TVsSAnZWagAAMZYP/Rex52UMhKg1UodFZkh3\n1nkeGH6qNB4pEPSaRPxcH89MHL+ND4iua0PUvjC0GHtLPxoBy2gBgQmxAwxa\nQwXpouqL8vskYJSKPng8rsgWpSqEKqr12DiyTpkKvinXth8LnX0ZmiJ0FyPs\noZkS/1BLulGxQPkaciayQRydd5v16+ZpwZmApJLXYt0DuUaIaFWvEWV9k0HI\nGCEC68PZ/rOS/c2qrWibwPWpl1BpU71dG3YClzkEdX6xT2QRb06kZNsjjJds\nGrjIAu+gwCpEtWcf+aqFqUZDkHD/v0j73af2SqLENu8E/J5VzA3TNxi17vK1\nLoXPhi9mGQlvJXjabIqofFkp0rR2Iro6F84MVgU6i+mQWOdxnFaYC1ObJltC\nH3ktuIG3J+CUYBegTjjfnFuYHZxLmwrI+8vNw/b1denZ4snBY5pIdx89iy4Y\nj2A9qS2L7qYy3X5YYF+KuxeMShVDLIM13xCKPuIKwagm6EkCjmp1zVnxpET8\nkWieyN1hPLvBxcCJWRH0qH3lpoOc7aCjekvM9C3dn5O5kDpDP6VDIJPzOMsh\nRp0k6LYAp8PbgxxZ2BJzLrcImAEOdxAMxB3WiwJhYfdOOtpME7u7B25ty4jV\nk8PHxtPoqiGzpFDrPyq+SrGsEN0ZSMksKe8L3/uf2mqnJuE5OJZewK1dRkyc\nUezh\r\n=KPF5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-module-imports": "^7.10.1", "@babel/helper-remap-async-to-generator": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.10.1_1590617307034_0.7318104084269235", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "41a5017e49eb6f3cda9392a51eef29405b245a37", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-F6nRE<PERSON>an7J5UXTLsDsZG3DXmZSVofr2tGNwfdrVwkDWHfQckbQXnXSPfD7iO+c/2HGqycwyLST3DnZ16n+cBJQ==", "signatures": [{"sig": "MEYCIQCcKu4IO8ZprxT3GyhNJydJ3K2Grt4d6bPDBxbcAMXfWgIhAPS/fniCGQBBVLhbbAdyj1LHgaHqFB0azR14K2p75LmL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zqBCRA9TVsSAnZWagAAWt4QAJWS6eNLgSOhwz3Vy+hI\nEYKKmEp52Hdk5YXXH8x0oxJtxI3pj8E0E4JOQTWLAex+1st6IQPtmxuu23zI\njhW7OWcKtbd8mhdChkrWN3f/t2nNSlLIJe6kiIk0FBRn1Z13wLSJoLHJIqXv\naJRc8GptJKbBPxH+0E6yanpPTaZ9nxVmtD192ySkG3GKf8tYQCoj65bR+27O\nmZajoBmQwVwl4coMDyNDLZvm/2WthWvdyTmWX4oY8dyb/YGjI4QblioNZwCH\n6qNfnddhiLRpTFy3oKcESI4Km1V3ZC3EIsi4ccGtZQEsHtiluH9zYPT7jqNJ\ni2CExpprFRPmfesXkd6rlsdS/2X10u0ULkrRPalbP60GSqYnYZNYDgl2HU6/\nvs6l9iv3ZQA8lZlojq0GcZyDopYhWYqjpUipcgypapqUpcRk0Gqj5qnTI9dE\nYk2npUe8xy+140tMZkG9TBI2jNw2q2c1izxNGm4AplPqieMQd0ej2I5XhCOh\n8hXE7x2U+kcJfG68VzFMMZJzjMBRs1snmOUqQGlPkP28U3Uchyve8xbbHg6y\ntTe411/eTt7Jc3JanytSPdX/ivsMmz1J81UVVIgNtmQ2IfuTwdzP9EOSyHCz\nC99wkZBpo+Z2q55Az2JC7+ddLBOOVRvHKgept9pBGqiY31itjZVVLntH+cmW\ncT0b\r\n=PVxl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Turn async functions into ES2015 generators", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-remap-async-to-generator": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.10.4_1593522816663_0.17966551772627426", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "3849a49cc2a22e9743cbd6b52926d30337229af1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-SDtqoEcarK1DFlRJ1hHRY5HvJUj5kX4qmtpMAm2QnhOlyuMC4TMdCRgW6WXpv93rZeYNeLP22y8Aq2dbcDRM1A==", "signatures": [{"sig": "MEUCIHy+HG/LyClUu+rKgImShygeUaQiA8tlVzEreknM0/uHAiEAhMHNkeXLd/7tz1ofI9T1nSMv/nRZKBVcT1EBe26bhOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAVCRA9TVsSAnZWagAAg8gP/iijgSk78aOVHwLgpAw+\nocyIo4XBuCROl25QH3crX4nrvQOjJ6kjHFpA0thEtk6VRuIwHZxH39Os0w3+\nCxvelyvJ1EDEYE/oCrw69f+vjl18Tm9usH3A8t8SNQPJAr6R580eACAfPNDl\n4o1i5Sn3mg4dWmjSpaDq1/dVAhx1WL2ZaMDBVJzy8vawjPEoTftlwZ+kiOY7\nuL7quxDMDtROrln0a98hEmVDv0eFJvFDDOB3kUwxswrr2I1dX8Eky7722Fst\nmQKRov4uIotP4ae5N7rPeIl5RhcLDhscxLgKACNyzJ2C9QFHTOkBDhHmvJ4B\n6e4A+DI7dLSQux4qVYDy3ZRc5ypbEm1s9DPRYOKtO7SMk0Nc66QxKBzzZSyu\nq1Cf7NQf6jvMGjfOqjN2yNXbqpDC27cyL8+HXGPS3nYxaFwuDqZ3s+mmn79R\n8lUZH/7w6YdazKSyOhrg85TOIV06vXCjMnon8zHCNTJXCO7GiEePlhqAqoKz\nNQ2awuOVFVwRhBUe/o+62mlhZqzSUh4S2kPh0ExDzfGW6bBe5i1R2xtAyHBK\nQZo8vPMBEbTLUhZgclWQcNgzi2Luy32tSF8Wu5SdD9tq3QrBINdF1n39LEvH\nMlBbyazwP/VkaEjOT3va5skIXNR4F/iekfFws5kppgIPS36Hv7YpLqb6buD8\n1i73\r\n=QeYQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.12.1", "@babel/helper-remap-async-to-generator": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.12.1_1602801684666_0.6466477679458347", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "fed8c69eebf187a535bfa4ee97a614009b24f7ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-psM9QHcHaDr+HZpRuJcE1PXESuGWSCcbiGFFhhwfzdbTxaGDVzuVtdNYliAwcRo3GFg0Bc8MmI+AvIGYIJG04A==", "signatures": [{"sig": "MEUCIQCsXwAEeRC1EwsFJIp83zGi8EtMzHcWHbqBmsV8LmtmLQIgN56EViXi4sZ8Q/cOl/MwTrmS74iSaXWcUAnLavtBQyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhvCRA9TVsSAnZWagAAh50P/jCqzFBYmCHbyu1VCBkX\nLQKVdIPgOv54woadm7mwwsF8CZFmIsSoUuDW05YNuxZwtDAz77Qr9uTE3E7k\nxpy0NtQv/+RXZ7XdOUCUWRpSrbf/NkVZ5SFQHvm9RHG74QFyVXXvzKwMUzc2\nu/+04uC3K5rGlN77aIWAqv1lJPAktPPWKtftuttcwnQ5VQ1mg7sVNDn2QHkD\nSqw921VuHaLAHOEEJ0Sk0jv57zPheOD+2k37IJis22qINb/C9zMluakM0NPz\nQX2l/CTrrpfrwjoxZf6MqcvqX7JdwQNVnR4iuqZjpXIBeS+u+hJfF0zUin4M\nKoWthXAYZR43j7G+UlIzwnbhuDChBn8p/4D9wBV9YLj+wrKeycNEYFUy7BMa\nAFftAG08wmO3HfMbPi4V+KkHR6MgAo8/XNO3vq7myA1r/4qb40Ev8F4pfixp\n8wfMZWsfX0fswWctRvidsF3HTEZA5KQtHrXXN8gCXrbm8Gx8dd92HAvGdlm8\nVr1pf7ex4FOTX/yEieKzG1RY3jjveiEfPq65of1aAKVjrnwcyAg6VrBGdiCj\njgXLPoRUH2mQHPcg/Jqs+9jw+WjwsAJwXyBU/+aXtIxtoqyhh4tfjJPZV1sZ\n3xx3h6TKpxPIfMUuZd46WBaMC1HGcUEvP7TQBjvURSViKFyKDRkyK4O5i23B\niw4I\r\n=tyo3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-remap-async-to-generator": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.12.13_1612314734983_0.5875677790452205", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "8e112bf6771b82bf1e974e5e26806c5c99aa516f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-3j6E004Dx0K3eGmhxVJxwwI89CTJrce7lg3UrtFuDAVQ/2+SJ/h/aSFOeE6/n0WB1GsOffsJp6MnPQNQ8nmwhg==", "signatures": [{"sig": "MEUCIQDLlyT8X3TFm/lliLXT8Gwcaz+n4eP9kOtYPu59u1idrQIgNS87yaZbeviyGd/GkJJOu3OD/7Lh+7hCj/xnbht8q0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDU3CRA9TVsSAnZWagAAmyIP/Au9fMxaaSyrwYwOfK/I\nE3oYkGrLthAKCwwegbERULeH0g+YwbCknuHaQVqXR2m8GFzBj1EXu0iveMMa\nvzORpcvHKLL+I0fSbgeUOOhZQtFM/2TtuXPml2syQgMqU4A+sDtujlcZhtDd\njD0Vy+jQtILPv/z0QLeJwxuU1XUbdFP2n4NoIVIQFbOM9c9Sabwu7rzwXveg\nVxeWRPDCM40xJjZ3uzNjpcqApY8uk820w+ttMAl/dbOHiidpbk4iWkyzGFR1\nMV5J3oaqfaRsc0QKAC4pHzYLWe4moImDb85Hv53jReWefnVKhVCVPgFMeRSN\nJ6QeZY+oiVhrXa2BDjaZkCmrBBgNlp3YujOUqOBUOLumKXPy2ev7Xn7xvNys\nQ3RXyq5jyZ6frdzBHz7waiKxguhfLzhHvccHXpKMSmy2SnKv3/W3tdP9B/aP\n6S2ZUawD+JHkn6yZ2HW9P+AUNHXSgoN7D2PpEXdy9NihQWIgQ7pXJry2Y1a3\nBXPcoFU6QknNES1vNk3UsNXl6eV34+VvL0Z7RrrIR4YiByVlyzUJt2bhXxKh\nSrOUW8LrIT8/9dcgFPrt5CLfqwheIdcfTPVQr6gVdUI/UixgenjCKqXeiKKt\nECwc90PxpPDJIPU5tv4ZJeGEEr0a7/WCTq7CHUzo1DcsokasZPxBOF7yoIX7\n3+eq\r\n=UmoF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-remap-async-to-generator": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.13.0_1614034231176_0.1741005351962439", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "72c789084d8f2094acb945633943ef8443d39e67", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-szkbzQ0mNk0rpu76fzDdqSyPu0MuvpXgC+6rz5rpMb5OIRxdmHfQxrktL8CYolL2d8luMCZTR0DpIMIdL27IjA==", "signatures": [{"sig": "MEYCIQDdUpralUtmJWG5Dtj71J5/xCffcjgjHJdtypDCa1CvmgIhAIxo9Jcn7nRWCTwEwluxe0eytjq8UE398MJNDih/r+iJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsOCRA9TVsSAnZWagAARYUQAJdSzhNYKMedRniGdCO8\nwTXGqjfmC1M/LNpGA72A/UIVe9ghsECwrEmN8e1CPxze8PQNvo/7q3Pt6YOT\nIRPtSA7Ez2zY7yrziZ/9TACVDI6O2AhDWiO3Jnx7kWcOb5EDVrIs1wwfFNlK\nn0Xu3PMpSwXPLKpk+cEH24UCMGWNJ6Cph0aLVa3c4NSpgrwD1mM8ctTiTlo5\nB/3PO0HAMaNLW+Zb1thXOAbSG2Vl+WiizvjBhf0/oUMnrEy2TTHS2s/Oow4t\nEyNO3Xf9+XOBdYj00ZylLKGe1LY3/ed3twaKz1JwXBF2g2km2XiBzpsIeAEd\nEwUs/t79Gu4KDkIl8GI5KjsOJAzFrHTMU0V/b3AhzWnmP59T7T9gmo+PXmKm\nucz/Nv4EBwcCOH1DgPnIYNdamonjSwSprIAV/vqIn8z35WJNn+onsxx8xUx5\nFalxkHww0LiGKzm+hJ7vcUOfOLX5PAx8r7cgRrgZAU7YjnEY7PRjIcsn/HJL\nf7tgKhYjMt3tI00WFZe7zkL1cc/lTk+ARkdasZN1pPxx+ueXulxI6RkXvKDw\n3h+VMFJULkKJSkILmITQmaQXbuKwJp1Xs+e5Q4do/ctySXVIZherSu/s7eKX\nFjd1sc+vx3ycroEZSuHLGbPNrcFcJVeABGPXho4f9vu5ClouYN4pxXzqGUzX\nr1k9\r\n=VIrc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-imports": "^7.14.5", "@babel/helper-remap-async-to-generator": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.14.5_1623280398446_0.779265014165134", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "df12637f9630ddfa0ef9d7a11bc414d629d38604", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-PbIr7G9kR8tdH6g8Wouir5uVjklETk91GMVSUq+VaOgiinbCkBP6Q7NN/suM/QutZkMJMvcyAriogcYAdhg8Gw==", "signatures": [{"sig": "MEYCIQDKLLbgWcO2XB0BJCNvCC7MmYCBp/xPR/voUPlT6mLRRQIhANC9NkwXcsoIfy/kDDC7LBo7HtcxiFtFibPbCqtI94Jx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4021}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-imports": "^7.16.0", "@babel/helper-remap-async-to-generator": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.16.0_1635551279371_0.91954837696376", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "89c9b501e65bb14c4579a6ce9563f859de9b34e4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-TMXgfioJnkXU+XRoj7P2ED7rUm5jbnDWwlCuFVTpQboMfbSya5WrmubNBAMlk7KXvywpo8rd8WuYZkis1o2H8w==", "signatures": [{"sig": "MEYCIQC3MS8WvCV/7CVHAqcri/X1LtSfSQ+1Pz7lj+Ul3OBA5gIhAKaulqYL7418EpmrdQCE9auF1eY0N7NfZCKiST++jHRy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kuCRA9TVsSAnZWagAAeLEP/3ESj64+ImbZyZJdqeX2\nptudVHQbm/27unM5IKMcUeFeCxJiCNFHcti7vwLL935/lge5OPCB+bkCQ5VO\nYJIMW0alXWfwJXnr6olZkcovmK+UUrFniQZamVJTTIOCNwln8s9TJ6780fWR\nUSpycrzZmxW9x7SF5saF+aGWykdCFz2t8/rnVLQlY69NCywK5XmlzkG46UrI\n14zCQ2j4V1AGQBAo28fLONPu5dCK2PS4YgIFF5PyuewqjXViMuVbuO+V6atK\nJnt4CqGNKhT2QgAG5q1WwZ++job2dyaDBE/NFZTJAyH9ENZt3B0NFyjRadF1\nXe0lLNItLRjZgdPSzuHVx87vk28CMDRvYh6M2WdG4W831M092i2WCgldXbpg\nDq8g1UxN9AM8HYDp7U9Wx7c31+OqJQ6nT8rUhf840qpcxRg2VhbsLmM/vSQo\nwKoaXv4lwyeIItU0EtnWCldj/O0YV6ygX3uayxWb1jG7SFkfaq54CPa1CBw8\njzT5fhno93iH8qOBfyc7SZ8kMHZIDmr1crPa2q3Q2CeBC2UV3UifOv+x0E/c\nxK3iRkXqyEbXGEe/44jNJ9DHITbV6RiYozlxnxTQvs8H6DEwyx96eDaCUie+\na8Y3LqYw80Gk8hnHI/jr22bUyKfLagbQtMqkiAlUlf0PsaRPEEBmXLos1GZK\nR4xk\r\n=Nek7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-module-imports": "^7.16.0", "@babel/helper-remap-async-to-generator": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.16.5_1639434541870_0.004903372029920083", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "646e1262ac341b587ff5449844d4492dbb10ac4b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-pFEfjnK4DfXCfAlA5I98BYdDJD8NltMzx19gt6DAmfE+2lXRfPUoa0/5SUjT4+TDE1W/rcxU/1lgN55vpAjjdg==", "signatures": [{"sig": "MEUCIC2356qIplOuqsgZnVbkSlt4OJqWfL9eMUE4Irz9JFsLAiEA+P3L9yOCZt3f2Qzn95Z4rgoZ7uvaA7w/OL1VK4wYlIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1yCRA9TVsSAnZWagAAHSMP/RDodKk602rC0zWdjArt\nHnHPL+lq9HBjr7XzrXgoG0BNy3klxtaE2ga8JFpP384T5mprLPr5kb2HH1rV\nk3z/OFEQK1FRvqWzz0m7k2o4t9I8ymoUqqWC6nDWaRj5gbhSI9RivNNhLSGB\nJ4D8UOpk8xe/Pte4F0qoP0YoMBLoNXYl92EfXVHVgX8VoD0yfJDNMvj5ntU8\nrHHy/31/OSr5E4HRyQRoOUTgOOxr5MM66ulNXKmVXh/KLxG/ABhbzRhGaA8g\nJb+4tXE81QCk8xe8zZomYpDP/bExM+L0LDIJsNzB2AN9yNW5l3Zms8UU4Wi0\nqoUTQiQqCT3kXqexbZXlnoH0ts0UpAeRxi8w48XKDxtBUnJ3vzbLK50TdLoO\nyAjAclDa4DER4l7dbG8ZdMeb9uxgcWRl0dnkgOkgDLANMuvGbTAgbUh+YDYe\nt7KlnUo02R8utEnQIF1rJ+nd6l2DMu6sXepj0GwkK/rR7l/FrYNGH/PDWf3B\nWuekikzyVnwAoXWciXV9yiUZeu6lI4ZBYKrS12J0e3m4soY5XM/qmwBkHBoM\nK9VODEbNJXWo9OCjiS4t13bcICW/KiwgMq8i5Tc2PDza8blTtfxgmDxqauNg\nMd1nbgLnlwP3GMTIylV3ougt4IcXO3eVhib4VlFuWIj9koOZH6ztnh8wRjBu\n1UuL\r\n=jv3B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-remap-async-to-generator": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.16.7_1640910194213_0.5718787377287424", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.16.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "b83dff4b970cf41f1b819f8b49cc0cfbaa53a808", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.8.tgz", "fileCount": 4, "integrity": "sha512-MtmUmTJQHCnyJVrScNzNlofQJ3dLFuobYn3mwOTKHnSCMtbNsqvF71GQmJfFjdrXSsAA7iysFmYWw4bXZ20hOg==", "signatures": [{"sig": "MEUCIQDCHSpynkX4aT3LXdiRboLkscDCEIhdbQ8f/6VjYisKogIgFWA5A+pWzdotiRrZRVFsHDZp1mb2Ap/bh58v6nigF50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KK0CRA9TVsSAnZWagAAIBUP/3B4LDV9ckU4YcRDbafQ\ngVRCDqkncPHIoM7z8oJKkjmxQ1LxvFHP7UoLk6u/btJLorDyFI4DTjO3CsAv\nXyHhOzST6fqFygqyr8Doc48q+e/S/BaVKRbrOlx4JjF0qy72b96dZN5RNCgE\ns49OlIb9KEaxRZSE2SMlj2mB5D82XxOg/AvqMlf+KPJyOBq3P3ANMM2MgEVh\ndWJ0vHxIbtFbD5dmVca4HdyKpzEEzlhI8wLzAW1pz49d29ON7LPksLYOiMpe\nTe3YCW79X78FoXPK908lyFmAKvDVFT/THi9T58ZuyGT+tvPehR2LW2qg9Slg\nNWyVajIswXRYOCNALo0lqc/HXip0oaYv0EeTpKzt9ScgWNA5hOch8sHWAjlN\nwSBYSJXh/Bnn5u3T85N5izH6rX9KdPVysHdIAH7yuqszusF/DnZhPfhMwcZ6\ntOKT24JDIYnJK4DK4SasscSavMIG9VWfO7NNj4ntLZmRU1nHHzNqD/ypFIxo\n0VrK779Awl8IOFEFh2ptZEIOdgdbpS12LUlPv198bRcx3MgVHLQUmDpPOhkr\nxMNBcVKbEPXqIYby0r0Kw1htkxx9IJooG9QSAHWOUyg4Dgs/wa6nO1XcX0bf\n5RELVKaIjyVfwrbLWJIdNtp4fewfRZWXVlzqYLnA9hS+KHPkq4O+1LOjsqMQ\nTeI9\r\n=kt6N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-remap-async-to-generator": "^7.16.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.16.8_1641849524350_0.8644636487903106", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "dbe5511e6b01eee1496c944e35cdfe3f58050832", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-J8dbrWIOO3orDzir57NRsjg4uxucvhby0L/KZuGsWDj0g7twWK3g7JhJhOrXtuXiw8MeiSdJ3E0OW9H8LYEzLQ==", "signatures": [{"sig": "MEUCIQDfEoqSzpuE2aSJh7ZkXXFAkeLHtcUc/8mHR7V+MZn38gIgL7vS2UrMCygzYnk2aF4K5a1zOSiBydAo92H3XcwDino=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK5Q/8DmeL9Bc4ejYLsc00QQpEhr8dWmHhf0j0MJTL6bU+/pF2vv1v\r\nNsKRKx5k5bgMspaVoCA7V91WyII8bHLmA7O6ZHjIwk6YmR1TvayoqGT0ILRk\r\nVvXgZfgJzggGYE8my9VJMM7H2aisQE4fWv6loRZ64GKNoxKt4vQOI/VozPh3\r\nHMhGTKnvw8tkrhkDE1P9XsTy28S/dItCTKoYtg6Ibm8Xvyp7G9UPoCCgn340\r\nGSeF+zgSTPcHH9ZlofBW4DADAhTUENFg/YFZCa2ih1mAYWooGZgVpAk0uTcW\r\n1xVuxG/HcCEsMMs/yELtcqpVifHiEUi6fjueaEaKRWgq4FRdY7A9QdzDDPYv\r\nC9+N0LCJP+dsXznw3XIMwfO01gk/Y9Q3c/lzkFp+l8Pgira8KnumUsl6LMOA\r\nI6QgVzW156SIbOpw+LJp5Yj0CxqH32V9NKB0mY96Z955iqIskj1gEnhG3brm\r\nt9jM1KvNiAOaX+Jn8BmQL7p4xsvTVax0vd+se/IQPvt2dH7PGJBkwu1DYzqN\r\nM87zYpQsoGM2XXxQ4Sh79HCza/UW+WnHBw9kT4p8hVByHSGeMxKVFd6P1g5J\r\naq3VpklxzI45dmD6sLOLZoT1Rv1XU9jEsCg/iGWyGxMBUbEUxyFZJ79cYemn\r\n5B8Pp1bAKUB4G5FzZZSsGDserWT5CqNd07M=\r\n=kD9a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-remap-async-to-generator": "^7.16.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.17.12_1652729564558_0.6330263382318486", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "ccda3d1ab9d5ced5265fdb13f1882d5476c71615", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==", "signatures": [{"sig": "MEUCIFv6r2y9IjhyPI5EfZxqDWZyGKNnnSXvPRAOP8QeCKV3AiEA5P2uFqnGobD25LhyKydcnyIlTAAhhey4ELoyDgorXow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3Lg/+Lo8iOZ7KQcG35Bs7xRL8VJzMwKonRm6Mg2aakfzpHS7ZRoIV\r\nZXyrWR5QUPKnz76CENcwtvP78GDsLj1UAuWpiKqSQtwItjCZidlGZu+R7YGU\r\noyHWCcgzUF+4omkpJ9RuAk91pJK9C2UN6IMTZY7YruoNwrsE9dWUMjrsDA7P\r\n8tz5N9y7/UH/7O3tlXNa4TxNMBY/p5h+W7YM0okME2IKt4IkOTzGMaPUQsnG\r\nr8DocgbRh95DW25hDinAmABxEEDf1k/0dS0GuZjk//+elSo4C3ugUWTG1J1m\r\na1sJdcBSkeSXXekhS/mfwlrHxI6iPXGLIVCde1cQS2eD8Xwzd/LBBWQnV9or\r\nFtG/jwXUhNdzcLaqYpA2rxhD2ddgTWJ4iKzt5+A3alHBqGCK65it2hjnRCAF\r\nWmwWYt5KT29WhaWRmCfIySyMQhsLjsTEGiZlJLkiqYlq2/CQWkPZCVv52VpR\r\n9UkccSzWaTdjZ/1gkV9kzQTNBVMK/G4xPlupA3DnVbJmJdUKjZqaKZrpirI3\r\n3VS9UZHdnxaTASgw5tW+ZTDdxUPe8rk8IFEmUoe2T/7l0Af/17gQjQZwkx+q\r\nQpzU0v82khU9+d/Wetbr1FuqyS1fOZyg5rj8JqZyP4hWoOtp2G9H+Gg8m7Ep\r\nChO4teOcqUmf1ihJsak+7plNiw3pQ2fRuwk=\r\n=ygk7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-remap-async-to-generator": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.18.6_1656359448602_0.5228927843375681", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "dfee18623c8cb31deb796aa3ca84dda9cea94354", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.20.7.tgz", "fileCount": 5, "integrity": "sha512-Uo5gwHPT9vgnSXQxqGtpdufUiWp96gk7yiP4Mp5bm1QMkEmLXBO7PAGYbKoJ6DhAwiNkcHFBol/x5zZZkL/t0Q==", "signatures": [{"sig": "MEUCIGh4sYMGxCXg65WhWSc7oW9HRzJH0XyonJGFNCk7gpgAAiEA6e24tmbo0+Ikork5AyGIBav9XwpYyEDvRyxxziV2/Lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCcxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5bRAAkdol+UfNs5L4rOXtxR6OcrFoZhj4SijY2zYeERbdOYr9Evec\r\ntKy5yt5TQUiGst5ynu+JVg1XIzrkhr3GJxAFyVVkhIkSNv4Wvvdfnf5LW/FI\r\nIdxencMLQYbTwppyjDagg3lrySbyST96ZrmDYWRia9X3VOOwSecIY1JBLCJU\r\njqycCfq916P4DjFJsODM7AWaCCqOHBhdCBrPl1eEvzaJxpyQJWG1NlrGskuu\r\nX7nL/d1Ji+8QxOaaA7l0XnT8TBl85YiS1+2zKT0EyotPzw2yBT2auazpv0cG\r\nFwQcfz6DWWCk6f553hLBO+3ct3XRGhVTmD+lBgtvnuhBSTquhk0zj0Unj2+q\r\nhClEi8a6J+3khk/PJEwgegk9pwXlW1kKg5CugYBSbfYRL9OnRG154rsEv4EZ\r\nOvx28tstvJXP4FhAPLR82kpw4WI3Xt0BE5EDoLvqZQx8qBUNe2unzf+LO35Y\r\nHYS9ryNixt/DB39l3cCcwe1P++WucdfR9YN4SqSpMQkDDJAQs4EjqRdt8BvA\r\nO31SufPZfeIJE0ZRpz9aTjiB+t5hXATtW2B002rOBgJZ58/weKfteRPBQKjz\r\n8z02S/EN0GUPPaf2iuk0eFU3Dol7pWAH8oTsxUOm4LVhh8GU8xUrVYJ6elMF\r\nkdC4dG09jGHpEKpyAHQdBezksUHrDDHV/dk=\r\n=zizZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-remap-async-to-generator": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.20.7_1671702320952_0.9693793230872683", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "41231139d2df1b4ed8a5a278028ea5bfebbc6fce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-kh/6cC+kTdq9p2ckEpK6Nm4erGlBWXwLX2ENIbrMFolx/0mA+DCwPK4h4ZW5KxQoGNMEYX2Saj0k7xN+JJmGXA==", "signatures": [{"sig": "MEYCIQDRZL45if+8Vyz1QkMMtzM/Ouvw61iWeRPAynDb9acZ4AIhAIWRL1l1IOFKU15KSd3BSXcAJxZh3Kvspn6L+Be3Xxfi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNWA/+IMu2EbZnMpwL81oVoi6uEF5Y/c2CLDtQqMpNjZIIkdiODzMB\r\nNLFL6evi6ck/7ApgsPoi/UWFlYskcCAbAZ8cY4RwewpixzXS2VCpRhRKAm3p\r\nKPHz1lq5TpWXveFup6UB2RhbD6xvoMQdHDdlTpj86DxYgsvxijVjCXkD9mna\r\nBi9piP7sSGhyuFFyU3DVnrAGeUcKG1HxA0tajbURlRuq91JWFBTulPF5C+EW\r\nMHkIo7wrl1k7OaGLXjLVYPxkpN5MZYoUFjCg6BHpZMIYLNNpbtkY+kJxSwdI\r\nyJsrUaZhsXq83K8W5YTJJ9XfbQd9WCts+PFZBtTRQdZJmCgbapJTo8ZnS3Qc\r\nF/theOewUModsaVO+Vnm/RCixQx/qx7JWyRHQNxjtz6yWRlnFo/eKX1sRoe4\r\nVBXwAkvl5Q9r4sCXKp90WcfjACADb5nZ4DLX9kimq46cfykeYCeJp/wlHvTN\r\nuugZGeBPX2jgBFlWsQeohNcLanIL4MgGz0Sn2fO3/hxP7a4XNhV/ZjXu677t\r\no3QiiLDWRSZkESMkDhvxDlwuy7XSg0DNL8RYEi/wl8I5WlDr9PiwvmTQHqL6\r\nK7LIOQQVy3CN/g0M6qYPNGq47JvqERPnOmEINYzrhfTxOi1pnzekNYPAF91x\r\nWP3P1kLRdHhjP296X/LZDC7psq5ctbbcqFo=\r\n=Q6On\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-module-imports": "^7.21.4-esm", "@babel/helper-remap-async-to-generator": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.21.4-esm_1680617402011_0.046677306922163986", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "3a357b7133fffbe57ddce4ce4040dd2edfc20b62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-E4IerMHN0VFOoj9qG7yDSTZ3pw5FmQz0REtPC0zi+t4wxWcnqhjUrdLjMe4s6Xr90+ut5bf1OdAQ7BvD9GBqlg==", "signatures": [{"sig": "MEUCIQCgHeHXJAiI6dmMYVvAyJaU3+OTn+D6b1sMR2a09lkTygIgTSghPmWdI9WyLJxjvOWphJJjbrV4TfbcbCje+u7IBWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7hA//WmHee6nUCJYjdHuP2GrkkcHTH97RtFVzppA17iICOPuoptR8\r\nDkb6WiewOWOLTeIgAqaqX/fhTVUSgsT03J+Lhzf0iShBJupVdQcBAarMb28V\r\nEzLN6K0A+ZfdeEV7fCbKD/u8MkW7nMHWMZycKu/EWJSVf3cedIlVcZgRAgmu\r\nLE/49P55tLaLSw8ORftSapoQbZZvi/UzE90aOPUhfD/SczW4tkXR/fHth1CH\r\n22qs8o8SsRrBKnpYqUb5qaRCB4LPi/q/DOYD0yOhiICsn6Jmeue0E3o8c5Vy\r\nfvVFY7e7LVAadBjGdlW8erc5FPhBX2pkQ5a3e7GAZkHD63xM8XtXnbkUAQV9\r\nw08eNFCgMVHZqaz58QX73RaGy56KVIDXO5eUDEoSbTDaW7HPXKJ+M6fyUoUm\r\nRmymz8AdsRxLi1OX/iyPdOpcnCLk0SyRbFYz5WZr2D8Iz+a4lxwNTaRwEUnL\r\nGapHEERH6SOptW/AiuE7SaJEnNhfEJ1YCaD6gvojEQ6YRMGOYmCCuP2Th2rr\r\n+u1woe13RKt2yynhhQO5WJ/M7RlWdNBAlDJoiT43pkSCqorycQAl1R5UjEts\r\nDUhqddw/G+R8VAvCNpnlv31NQnYx9kbD5wLtmCD8qYrFfRfP0XkUyyDsdEGa\r\nywktxtgHwDnstjYE6BtKPbUAyDwSS/+EVew=\r\n=lCuy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-module-imports": "^7.21.4-esm.1", "@babel/helper-remap-async-to-generator": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.21.4-esm.1_1680618119526_0.820798791636806", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "d3377743868a284184865b69cdcb812d55de6691", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-wDQfUV+g1zEXEGPqN6V0yO1yIjxYkG3q5PG6etkgAKXPZb1ltwQw4YdoxG7oNa8BD4X8+dVv/hQb38M2NW4ywQ==", "signatures": [{"sig": "MEUCID4m5AVG82/WPbjcrVm8e8tJR9SFGMy5lA8WSWL2rxi1AiEA2hs8yEk1kEfa+uO2deIsgHadZP+6QHdV9pYkDHTaOUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzqBAAllupdBDswNln0Wxn5Za2Q9lUjNYbhZjiOyveolaoXkpEM8lZ\r\nexlAXJ0VOPVWXu1mfaSgKfcYSKBsPUFg1f35dL+7y8IUbgqH5M0WJb3SrhkP\r\nE9u7lbo0m7obbKwKQQm5DT0onIj7mXE90s1ZDqRPVfYV9U4I81BYo0yBQ7kj\r\nc/xjiWhtNXOv5Kqn9grXqAEXKwSlTjbaihFIATnlvTiYlMM726CKYGHcx+i2\r\nnN4nORLO+U9i8KihG1R17hNQdrHRVuWMV8Tt2LEwBlWUY4oZPPwVnVWfPkxp\r\noq9kJAWumEaJuZp7h7eEok6S3SkojhJmRrtL0ipx/GIcc5RiQfTyw+TL9WuU\r\n2nsxg5lW4QgwLw7Y8sWdrpx89zW4iVb6FhfKcW553Fro5Ryhch9Xn8PlAHtn\r\nmuDNbBH4DjLq7Bm1QWEZALVj4f2u2bwP6pqo392+8TB5aFPKSUXSKRJvennN\r\nwxxsY2t+Hri/uVs/zaXGl5KhZRnrb3B5BnpKWDJsERs04LrC6ibQz0MO5VpK\r\nWHRw+st3FYUshpKHzjKHth1UXMK4rn+NKXtzaW9Z0s4x3yioDtuR3AWjZXVm\r\n0doK96poQoXM+wS7HBkA8S5YI3iIDDq0FAAh7pQAAhb+2caMJJ/kWQNMKuRU\r\n9OvjdtdLVCQ1oM5LGiuhMZ6jTt/b9g4Yvnk=\r\n=bQfy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-module-imports": "7.21.4-esm.2", "@babel/helper-remap-async-to-generator": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.21.4-esm.2_1680619205338_0.5192314957425919", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "a8b6376d83440f3032ebbdc794cfebb4f627109e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-a7jyG3oNEjkIS2MLuFmNc3L9/uQR7lWjiYWXx12OPaSc/TnGnLB1VRQOJbL8IDrlM2JxV9E7ehe+ZbJBz3ry6A==", "signatures": [{"sig": "MEYCIQC5rIgljQyoMq4V8lIqPB88I68SX9YCHGkXQvi9CTjGDwIhAOzeFEMkQaU5Bf8KTxpY+F/AJvWrjL4jtB0injCP5OXp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooAA//bKUt5R1I1Sh6TVQJnLT2SFhpHnrhBntSYK8P/dwjFol1L5G4\r\npnJNst7R8Fu2bPfikZtv/V7bLBN+nJf9AN813yN+mUZvZfvp3AwysjWxw18A\r\nK+Kq5q7wsxQZ4dn+Xm26ZZRTpMhfKmk45BqwjUznG80AhCwKC2L0aBpBgoSX\r\nUugi5wwOnUJtberTLRR9gH29s3LCVHWLzAEuXJUiyIAMsIJCl1caHUARjjpi\r\nAYv6D4GBd3ST7iuShZAZadej72gtAKw+ZstU1GJKbNEf0MNMZOJaGtVPlEmz\r\ngfImBiY88nfDflGVamzYqy5PzZkeHYvO5gwb1NL3jr72VhSYpce2NmHGSzqM\r\nuLQm8j5zcO8bEkArTrefCys3XNCoZ9pnab2wd0akb7dkpT4M9JQ9qu3lBxUe\r\nxUjDhfxFzE9h+d88SYJhV+T+SpT5uVMD1R/H3ZhI+Y+4h/gx6La9fsq9t7al\r\nurIQYhJ2oOrAXHB+sO+Uvt87EH/rAYoX0zkvZ5Snpbv0dyZw1A98bDYAS4P7\r\nCS35i542hMURIgm7SF2y/bNKf79snv1kvRaaKgA2PsfnRwvgxv5mw3Gy0OrN\r\n6OVu5iWhqa/BgxXV1TX6MVjGAoLCADI76kiACAh552CCWkLjDv5+8C/CzRiK\r\n16VNu4FQMKuTj0PgIk1bvyFyeY+aySjcxcs=\r\n=ujAN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-module-imports": "7.21.4-esm.3", "@babel/helper-remap-async-to-generator": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.21.4-esm.3_1680620206898_0.0011148913553882256", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "5cc4aacc843f3749f4e1f5adfdef50522f5f5ee6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-vbUdc0MCVLWi2NPBlguQmkNtz+gjhG5yKSvWwLlgpNwGmlY6lEHXUafnfZPqmVjDpOpSOMSnzmULlgLg2KeGbA==", "signatures": [{"sig": "MEQCIECDHG/VBv7dclbbdhvtQGc/LZjGdnINsBjpbaBcdwzuAiA1iDqB0vjnMkxR5PKeKK5I05+wgB0BtXovEv8LZPEdkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3CBAAiRYYK+ktfXYrilwbiC70TcVLzG4+cyCN5J1Tx7kFSuD0pECS\r\nHlFDXCjq3Yf4tWLqnyhJXK3A4dRGx2X1ci+KyACNPn8uLye7APbw2+n63l67\r\n2XGSbwc4latRKiRRFzA71T9h0jLNUJ5Fnld9Ciy0lhy51l2epKQm9NuDddHE\r\nN4lppdxzoVsv02H6Km6HJwXK0K/IzYkBbH5sfOE7j5npFlIoViERAQ50cF4S\r\n1IqxG6SUNnL20BHX66eKQ/GepHoxChzMwrU5XbVu/m/OS0GFR9+bEycmbgiA\r\noFOGArECF12b5crImvYSmlHTBTnLMQE/Y/wc/OUuYiv60oavIFYRbWPChIxE\r\nL437P4Mihz9oC5WqyB97a/BCm2xjAGEwWm0fvv4U8OOM/QX7mRAgEMFS1TtK\r\ncBzTOHLXVMYykPwnl46FxJJMe3UkHWPGiQUmt88YUev55n2YoKMfa1o3xF4w\r\nxQpfCC1oBrqMDzwlG8kvt0HUIuGGPItx/r5LdJS5Hrqm7Au72W8kUCzt2qXb\r\nE0Eew/eTGUDQW5XLMae6cNr00WCi29bpxvJiyThCnJULVTgipToyQMlzibHj\r\nRnsQQQIJdR/QQROP0Ri2C4DtUFypXGxKq8kIcqqa8wnwllaXBIEunr3sYkaQ\r\nnVP+j+IJuCIhGC2ame7jQL6NJLLahdes9A4=\r\n=eA/E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-module-imports": "7.21.4-esm.4", "@babel/helper-remap-async-to-generator": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.21.4-esm.4_1680621235422_0.9660155195316533", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "c7a85f44e46f8952f6d27fe57c2ed3cc084c3775", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-b1A8D8ZzE/VhNDoV1MSJTnpKkCG5bJo+19R4o4oy03zM7ws8yEMK755j61Dc3EyvdysbqH5BOOTquJ7ZX9C6vQ==", "signatures": [{"sig": "MEYCIQD2kBHJrHQzYVc4/v1DAQyW5GPZb/NJSjFcR8uUY+DYIQIhAIUTLvKxwELn/G8KGC5Ib+4V0Szh2ePCKRHMqZWet2/+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7962}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-remap-async-to-generator": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.22.5_1686248508035_0.3475240788274885", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "09a717978d562a73374412fcb9761696473e63d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-nI92KjmMqd4iDILCa9Go3yXymHXqQUsw5egNJaYqr24j/0lvarJ+vjg26a9BJoperJxQulNhKkTNYAVu7L0XWg==", "signatures": [{"sig": "MEQCIEX24wILlzSBD6xUC+oPQ2u1Jo/QULFlQIYswcTrgt3pAiAgN4Rex0Kn5Me68hwcAMPbCneVD6YNHDHwuIMY1ZiiRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7549}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-module-imports": "^8.0.0-alpha.0", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.0_1689861634457_0.6411432179971273", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "1c3fdddbf2d08df711425d59707403d193d3960b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-h2fDgEY0w2cHhI07+70fIkBNiPpTRfrNTIjRU//ejRusp0h9cR9XrXL6IusS2khqfGV3XcKPTBQ5MjpmsIWUiw==", "signatures": [{"sig": "MEUCIFWSO3i5mRx5GipIpETy9IwsUnpVgCDkvfQ3s1+bRgQxAiEAxoftACnm4CUafwRYNIlzGseZYWDwZZ4sio3c0BrI8sI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7549}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-module-imports": "^8.0.0-alpha.1", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.1_1690221189991_0.3905766062109177", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "e3b6473dedb78474745ceee54c96cdbcffdff1b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-MuvtwFFKKLpwSPER48/fS+DFX7BIWUmpuqUYFu61zGeQDJfz2R98aVH2jNgYo1l/n0ua0dirjTLQQ4pybJErZQ==", "signatures": [{"sig": "MEUCIQCvElj6rqEyf8MVBrXzoTW70yaFkvsVk9tN3nG9nk4jxAIgd+i66NSh152WM4eSayDt9Dl3ciDsI2/6tmU3xF1xfqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7549}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-module-imports": "^8.0.0-alpha.2", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.2_1691594129827_0.28129090935988255", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "e0eaef9f80b8f5f856622ca3757ad499f30c74be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-maMvvKVv9l6kVedmnjmpV4x1HFnG7a/127u2eb6FthGUdrocJ3HEF4jA7mRNrljuMBtSRqEoJ89kRTmM3eDMtA==", "signatures": [{"sig": "MEQCIHDd2Wp9gqBBzBh95neh0ACePxwtraaEZdQFcKDjbi69AiA20qmsaqKicpFt3sfxiMe7R44E+Wp6FZ4Vjrkw5GZJzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7549}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-module-imports": "^8.0.0-alpha.3", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.3_1695740261962_0.9569981770325469", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "b2215325905328a6bbb6a5946314f1f970aa624e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-rcppwt6NfpArEkeTXlhzFQa2kW0WCXozacjp4PWDyuRarjCxRyC/wMNUif2wgJKfF+FTNCGG2YjUptnIspnPMA==", "signatures": [{"sig": "MEUCIQCU35vLjKJdHBfvrtPx0f3ztC4UM1xnQsntHZ+UVVXvpAIgRO5sur41oErZuGpyDEpJPPDTgwfNlznYZXtHdFYlBUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7549}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-module-imports": "^8.0.0-alpha.4", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.4_1697076415760_0.45826785283550464", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "d1f513c7a8a506d43f47df2bf25f9254b0b051fa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-A7LFsKi4U4fomjqXJlZg/u0ft/n8/7n7lpffUP/ZULx/DtV9SGlNKZolHH6PE8Xl1ngCc0M11OaeZptXVkfKSw==", "signatures": [{"sig": "MEUCIQDt3VojQaUDvlEIMJAWCQkz9y6znr5/oNKGuplWBuG0YAIgZMgUN7JZp224XRTiFwQPGqtR8vA4j/JRhPrB90+42AA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8042}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.23.3_1699513425500_0.26913861473785605", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "fc322b6f1978cd031af85f830bc3e6f3831afc17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-HZmtnilinE5huVU+vjj0pYlN0tphoLcK7RODvyS/1P4dvtpQi9bY7jxv/TnSuFsVl25RzBqAmDhhRPeiycymrQ==", "signatures": [{"sig": "MEYCIQDVy43a4H6BT25CyTmwkkju/je94hyuyBqWfUOXpa/XDQIhANVDx895J5kETy/d8tUOboE8z7YGipyHF5e6oRTmFcxu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7662}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-module-imports": "^8.0.0-alpha.5", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.5_1702307998290_0.41995714208626245", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "cf521df43f0db7af37d11eb95191f1aef03cc979", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-AK23iIG7Cs9MzdK2X4g74k4b2xEyAGrLoMea03qTSOYDA5DPP/QRvnnDR4Qv0GlbXi5sb0HpZe7fDPQ+XqYl8A==", "signatures": [{"sig": "MEQCIAxtAxT8LGbu4AoKD6ohFNG3eSDyJ5q8c/36oGtgIWxMAiBo+ZezSyakjqo8NW4OBFIH2Tuy8Y40Dn1x/ZsrQcfisg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7662}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-module-imports": "^8.0.0-alpha.6", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.6_1706285697196_0.16659911877028066", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "7e819a54efc74bc841654bd5968732c7715bab6a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-+7MAFmvnBaLIj3ZQiLYG+DrKvtgM9WTZxaNlGJFaXn6m5R50IAmvRodK9Ds25HJ0H+1vsiIQNNm7eB5NNMERtA==", "signatures": [{"sig": "MEQCIFw3J+4lYjallMO4EdZXC8CLBjEBHc26e9NIRolHBW2UAiAVv7fKJes1x1GSHfA26kR1JpZOGEnETGSXQgptyBn2ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7662}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-module-imports": "^8.0.0-alpha.7", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.7_1709129153813_0.1712689750212577", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "0e220703b89f2216800ce7b1c53cb0cf521c37f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-AawPptitRXp1y0n4ilKcGbRYWfbbzFWz2NqNu7dacYDtFtz0CMjG64b3LQsb3KIgnf4/obcUL78hfaOS7iCUfw==", "signatures": [{"sig": "MEQCIBOXqFVPpVja8CFDjFy44qO2WShpUbBs8YGGP8h8PaInAiAOdQFSiosCfpIPFkyxZAuQPUdbeFPSiLFCOG63jmYYwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7972}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-module-imports": "^7.24.1", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.24.1_1710841766036_0.42669990488662024", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "a45e456f0861e66230d9a225b4057cc01286c1b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-/ti1qY7GOxR4eWDVbP0zVdcz71baHEEA15mIRGNjOffY686zImh71Dq6EqaxYmhlqUTK+4GtuMZOFeNz77nPoQ==", "signatures": [{"sig": "MEUCIGT4wASeDM0eHPGj80cWPc/nqwBqdJIj9T6FsOgnNNUvAiEAg8aTFjfOYamxF84mNv770BjuZXEJYD8wASRzdA+Bc0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7576}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-module-imports": "^8.0.0-alpha.8", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.8_1712236825490_0.05961948192824096", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "eb11434b11d73d8c0cf9f71a6f4f1e6ba441df35", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-NTBA2SioI3OsHeIn6sQmhvXleSl9T70YY/hostQLveWs0ic+qvbA3fa0kwAwQ0OA/XGaAerNZRQGJyRfhbJK4g==", "signatures": [{"sig": "MEUCIQCOlZ+7AFOBXUxq+zXhiP1k0O7GD7AAIaguYdD8AQS1oAIgZMlodqpOFaA9Xhs+EGNZyRhnht0tHWYzh8Rhen1tPJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74723}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-module-imports": "^7.24.6", "@babel/helper-remap-async-to-generator": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.24.6_1716553515842_0.8839335641582708", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "7d066acd4ed3f562b2c7e389edeadf9ee7a942a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-UKqnJdCEgVf5jW8Ai/GB63X+kHmKAVORc9qQnaHFqHbIkQXM56HIJpAtRk0u/y2fkdXO3sODJ1f5UD1ZsEBijA==", "signatures": [{"sig": "MEQCIGnxX1Uf+s5HiDq/FFsKGwlIc4WlVqQ5Tr2Hm5CYbVYNAiALzcR3uLHd0o5vOTL40IOtzeMQqJvShetnsOazFmAvow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74747}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-module-imports": "^8.0.0-alpha.9", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.9_1717423541394_0.16659805363637314", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "2119beeb920d19c4416b6d85e9989d65cac36ac4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-WaDgEVSWqhijo9Fk3/jJhLpAKyUEtAHqnc/X9unqnO+x9sh2LLWq0V7bZcCE9PjvtoM/h7NGoxUSxDfU44OKvg==", "signatures": [{"sig": "MEUCIQC5pKEfTOknhjzXXuSvZREKMGzKMkO/kHqoOSJkBJY29wIgfDa1N5uNUDbDrBtcLmHQSx0u7JiYXL9JshT5yBG7ctM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74756}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-module-imports": "^8.0.0-alpha.10", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.10_1717500041260_0.2744346975452201", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "72a3af6c451d575842a7e9b5a02863414355bdcc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-SQY01PcJfmQ+4Ash7NE+rpbLFbmqA2GPIgqzxfFTL4t1FKRq4zTms/7htKpoCUI9OcFYgzqfmCdH53s6/jn5fA==", "signatures": [{"sig": "MEQCID9Xpczq0rWA7lD5Ia5PGyNPm2aUP9EfoVic6u/oP9orAiASd7nEwOYhstrkG2u9hTbBQkHepmbyMz3k75Fy2auTHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74691}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-remap-async-to-generator": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.24.7_1717593354648_0.4493473297419295", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "011027a7d225fda0e93720b6ab44ca40765f7458", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-vyqubhBJU8mNWtOxCpX4H5sEut8FErxJOEIzSUh70ccu5U3eyU5CqFDORPbCxMEG6HoggisyhBRit+VPC37Bgw==", "signatures": [{"sig": "MEUCICq8YVuFU2nsHB/IF9kHGHCV1UqfG3OdhQjBzfqCl09EAiEAuY7EN0H0Kpf5dRYTor6yglw4xeZ+nR+V0vTZH8AaIMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74645}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-module-imports": "^8.0.0-alpha.11", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.11_1717751765205_0.5296213730248518", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "95757b9991595e9d2901a894f516bddab443c30b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-aiyTZtQpc/rADiq5rwAssAZ/zwtCtwCXmOX4S+c5+AmId2ckyquL00A+Hmej48WoAtT7N9HxWyy4u/L7wcL9dg==", "signatures": [{"sig": "MEQCIHrNirk61ZlGtYYMVQBUT6k/4qHZGvwGl0dkrzEK65dTAiBOAWEjX0PhEkP0ju0A5tfavbOnWmEW8cYsoP8Zdtuavg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-module-imports": "^8.0.0-alpha.12", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.12_1722015240047_0.40921808286785155", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "a44c7323f8d4285a6c568dd43c5c361d6367ec52", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-ZUCjAavsh5CESCmi/xCpX1qcCaAglzs/7tmuvoFnJgA1dM7gQplsguljoTg+Ru8WENpX89cQyAtWoaE0I3X3Pg==", "signatures": [{"sig": "MEUCIBjsne8psSYE+E/5KBeRtpMYWwc96KGQZMYYWnyqsS8tAiEA+01lRTU9c9dayc39h9ToQ4tMxAoARQOh7GP17brR8XM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79248}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-remap-async-to-generator": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.25.7_1727882129236_0.8955069818601213", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "c80008dacae51482793e5a9c08b39a5be7e12d71", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-NT7Ejn7Z/LjUH0Gv5KsBCxh7BH3fbLTV0ptHvpeMvrt3cPThHfJfst9Wrb7S8EvJ7vRTFI7z+VAvFVEQn/m5zQ==", "signatures": [{"sig": "MEYCIQDbUeJ9IY2R2aXnqUpauego1diLkcPNvkYNAGXA8zjlewIhAMEkkCGFl8Ol9CKZhM1JI4jqm6OcR3Y/l9fl2CL++X5m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8004}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-remap-async-to-generator": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.25.9_1729610505455_0.15544471486314682", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "9f296a002cb5c75c344a27724e96311fc2156283", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-B0Vi14mQ5B89jFDfv92anesofUkt8+eXMV0vRLPR0deUOxMscRArKMQe7ZTSA4+MaYTGJAHVBDJefD1cD5x+rw==", "signatures": [{"sig": "MEQCIAGHOowivj99lfkTfbfro3qfEHKaxojp1c6KSVZ6vA/SAiAJLw1enZkKhHuS2F1DBzyYsxD+Id2iHtzTzSsZGNFrTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-module-imports": "^8.0.0-alpha.13", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.13_1729864485581_0.47062617304725096", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "406c252a0ec2ed049e2172e1b919aa3315a8df99", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-V1vSqGJh4GQ8iuJpAggA5Be5kPlnO1pYVWFpiBoftc5F6q93PTazhYGRinmtkvkj5p64+GMJMkCGZNSBmDaAFA==", "signatures": [{"sig": "MEQCICxVhxiRrNnZDv/9tg/jCl8q565V7cH96rQ5V+qhHTSfAiB8DRQDltQvhw4rT+c3rD0hiNLgWTpwyku7+fldLZbyFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-module-imports": "^8.0.0-alpha.14", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.14_1733504074417_0.9883688848007921", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "dfbee1c4ac0a13d894869a2b6efcff83f467bcca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-iOwLxMcxkkU1sigarOz4ICVI29v2GxcfTiNaARxQ4s9FQs+CbpM7/vuxegQQl3HY4i5HCjQ1Mzb/Jjual5a0hQ==", "signatures": [{"sig": "MEUCIQCJtA2YKLR/rClipUeMRScLnGC+OkuxYLX6ylwYVJzlfQIgXjaVKsNjHc4ZeC4F/HIaTXADxbZA1sUns+KHxJXk5ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-module-imports": "^8.0.0-alpha.15", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.15_1736529903767_0.19894219267121138", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "b6768f06c5106cd5e96d3bc46797d7a25e7bc075", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-iiNgeGJnKimlMaZwpXurADJLNV6F771vF1eUf2SQiV5j7Vhv0c1nd9yUB/amyZZlVk3x5SRzHkZolSeTNAwSqg==", "signatures": [{"sig": "MEUCIQDvXoS7F3KMHSS3JVmoVjNGt9h/47ZRELNyPqDN1BVxAgIgVfrO0uQlonNxrQu4nPoO3y6lM3FQaN+x3JcIZada0CY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-module-imports": "^8.0.0-alpha.16", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.16_1739534378456_0.4063071739017994", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "1e7fa3a6db6aa6be3677ff14cbb23274eb80abba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-3nKH8BseofxAKpNxZJkzzdf4TqJVXiIa2U7h+ZHSRxB743wnRYKjl9cQiKoBQGWKekL1v6Y8bQOENlp328hUeQ==", "signatures": [{"sig": "MEUCIHl0RtCG6Z248nM95wAogP74zOITjSU1k/uGG0NihptXAiEA3jjS/JtWIbQuW5P/nQBfUm6/T4bMu2wLAtuHZ0NtQLo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8077}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-module-imports": "^8.0.0-alpha.17", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-alpha.17_1741717532303_0.44598396994914125", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "9a93893b9379b39466c74474f55af03de78c66e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==", "signatures": [{"sig": "MEYCIQDcH955ueNx5BgTT2WjQfx2/QkLgqec6+wRwKX9ii4XVwIhAMBfEYyR+5dvD3gAYx59Snq5R2+u4DgxoJMJWRqnbXSH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8004}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_7.27.1_1746025767182_0.4727140483879271", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "dist": {"shasum": "7a6cbe2854496a0f9facd0822e30cfc77cc5d102", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-t0FDcYtRxToNE4u0VAftwLVKA6EproVHRNn5P9+XRYH6tI8KF51QXeosJbEWw9F27O8wzXJV4WLUXKISVpU8fw==", "signatures": [{"sig": "MEUCIGazkACg+dvz1kx4nCFhrjuPVtPRVYcIyfdJUjI4u0eAAiEA/yp8RT1gLCe4VskDWOzfp2FG5WY20JN+VA4ca3sHovM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8049}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-module-imports": "^8.0.0-beta.0", "@babel/helper-remap-async-to-generator": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-to-generator_8.0.0-beta.0_1748620304410_0.24856842597168405", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-async-to-generator", "version": "8.0.0-beta.1", "description": "Turn async functions into ES2015 generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-module-imports": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-remap-async-to-generator": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-async-to-generator@8.0.0-beta.1", "dist": {"shasum": "027240d2d1deb3b9bf3d4a3ddaea093884a6ac08", "integrity": "sha512-VFBQz/JE9BKqJJAjWYMIf2vSd0UwS0TyHo0e451ESVpAu1U36TrmtVU8rHO87uRBI2MAI7oWvFEDD5edUdxbqg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 8049, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD6PfRGY2w8cacwn9ozaC5V36buEJeQcXUgAdK+bQJXSwIgLsfNSX4rDKjWg5yduQivugwEy3W5ZRdji6ZPxQmjkYg="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-async-to-generator_8.0.0-beta.1_1751447086962_0.07294020227095777"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:38.152Z", "modified": "2025-07-02T09:04:47.396Z", "7.0.0-beta.4": "2017-10-30T18:36:38.152Z", "7.0.0-beta.5": "2017-10-30T20:58:17.694Z", "7.0.0-beta.31": "2017-11-03T20:04:41.715Z", "7.0.0-beta.32": "2017-11-12T13:34:11.844Z", "7.0.0-beta.33": "2017-12-01T14:29:31.098Z", "7.0.0-beta.34": "2017-12-02T14:40:29.539Z", "7.0.0-beta.35": "2017-12-14T21:48:34.779Z", "7.0.0-beta.36": "2017-12-25T19:05:53.349Z", "7.0.0-beta.37": "2018-01-08T16:03:49.840Z", "7.0.0-beta.38": "2018-01-17T16:32:50.436Z", "7.0.0-beta.39": "2018-01-30T20:28:50.991Z", "7.0.0-beta.40": "2018-02-12T16:43:00.221Z", "7.0.0-beta.41": "2018-03-14T16:27:09.659Z", "7.0.0-beta.42": "2018-03-15T20:52:19.510Z", "7.0.0-beta.43": "2018-04-02T16:49:06.683Z", "7.0.0-beta.44": "2018-04-02T22:20:47.376Z", "7.0.0-beta.45": "2018-04-23T01:59:04.276Z", "7.0.0-beta.46": "2018-04-23T04:33:18.182Z", "7.0.0-beta.47": "2018-05-15T00:18:21.547Z", "7.0.0-beta.48": "2018-05-24T19:25:14.717Z", "7.0.0-beta.49": "2018-05-25T16:05:13.354Z", "7.0.0-beta.50": "2018-06-12T19:48:23.947Z", "7.0.0-beta.51": "2018-06-12T21:21:06.858Z", "7.0.0-beta.52": "2018-07-06T00:59:53.847Z", "7.0.0-beta.53": "2018-07-11T13:40:57.082Z", "7.0.0-beta.54": "2018-07-16T18:00:34.611Z", "7.0.0-beta.55": "2018-07-28T22:08:38.642Z", "7.0.0-beta.56": "2018-08-04T01:09:28.424Z", "7.0.0-rc.0": "2018-08-09T16:00:49.150Z", "7.0.0-rc.1": "2018-08-09T20:10:19.949Z", "7.0.0-rc.2": "2018-08-21T19:26:26.265Z", "7.0.0-rc.3": "2018-08-24T18:10:08.927Z", "7.0.0-rc.4": "2018-08-27T17:06:49.595Z", "7.0.0": "2018-08-27T21:45:22.481Z", "7.1.0": "2018-09-17T19:31:46.232Z", "7.2.0": "2018-12-03T19:01:08.347Z", "7.3.4": "2019-02-25T18:35:17.149Z", "7.4.0": "2019-03-19T20:44:21.204Z", "7.4.4": "2019-04-26T21:03:50.365Z", "7.5.0": "2019-07-04T12:57:52.719Z", "7.7.0": "2019-11-05T10:54:08.310Z", "7.7.4": "2019-11-22T23:34:05.652Z", "7.8.0": "2020-01-12T00:17:43.515Z", "7.8.3": "2020-01-13T21:42:43.714Z", "7.10.1": "2020-05-27T22:08:27.142Z", "7.10.4": "2020-06-30T13:13:36.848Z", "7.12.1": "2020-10-15T22:41:24.838Z", "7.12.13": "2021-02-03T01:12:15.105Z", "7.13.0": "2021-02-22T22:50:31.312Z", "7.14.5": "2021-06-09T23:13:18.588Z", "7.16.0": "2021-10-29T23:47:59.524Z", "7.16.5": "2021-12-13T22:29:02.050Z", "7.16.7": "2021-12-31T00:23:14.377Z", "7.16.8": "2022-01-10T21:18:44.497Z", "7.17.12": "2022-05-16T19:32:44.691Z", "7.18.6": "2022-06-27T19:50:48.798Z", "7.20.7": "2022-12-22T09:45:21.104Z", "7.21.4-esm": "2023-04-04T14:10:02.213Z", "7.21.4-esm.1": "2023-04-04T14:21:59.671Z", "7.21.4-esm.2": "2023-04-04T14:40:05.517Z", "7.21.4-esm.3": "2023-04-04T14:56:47.087Z", "7.21.4-esm.4": "2023-04-04T15:13:55.590Z", "7.22.5": "2023-06-08T18:21:48.194Z", "8.0.0-alpha.0": "2023-07-20T14:00:34.651Z", "8.0.0-alpha.1": "2023-07-24T17:53:10.174Z", "8.0.0-alpha.2": "2023-08-09T15:15:30.074Z", "8.0.0-alpha.3": "2023-09-26T14:57:42.127Z", "8.0.0-alpha.4": "2023-10-12T02:06:56.113Z", "7.23.3": "2023-11-09T07:03:45.684Z", "8.0.0-alpha.5": "2023-12-11T15:19:58.614Z", "8.0.0-alpha.6": "2024-01-26T16:14:57.372Z", "8.0.0-alpha.7": "2024-02-28T14:05:53.980Z", "7.24.1": "2024-03-19T09:49:26.191Z", "8.0.0-alpha.8": "2024-04-04T13:20:25.627Z", "7.24.6": "2024-05-24T12:25:15.999Z", "8.0.0-alpha.9": "2024-06-03T14:05:41.555Z", "8.0.0-alpha.10": "2024-06-04T11:20:41.419Z", "7.24.7": "2024-06-05T13:15:54.853Z", "8.0.0-alpha.11": "2024-06-07T09:16:05.369Z", "8.0.0-alpha.12": "2024-07-26T17:34:00.193Z", "7.25.7": "2024-10-02T15:15:29.481Z", "7.25.9": "2024-10-22T15:21:45.661Z", "8.0.0-alpha.13": "2024-10-25T13:54:45.880Z", "8.0.0-alpha.14": "2024-12-06T16:54:34.585Z", "8.0.0-alpha.15": "2025-01-10T17:25:03.963Z", "8.0.0-alpha.16": "2025-02-14T11:59:38.614Z", "8.0.0-alpha.17": "2025-03-11T18:25:32.479Z", "7.27.1": "2025-04-30T15:09:27.356Z", "8.0.0-beta.0": "2025-05-30T15:51:44.595Z", "8.0.0-beta.1": "2025-07-02T09:04:47.164Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "description": "Turn async functions into ES2015 generators", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}