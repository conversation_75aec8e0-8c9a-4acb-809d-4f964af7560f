{"_id": "domexception", "_rev": "9-6ab24d750495e3fda92820cb78e00097", "name": "domexception", "description": "An implementation of the DOMException class from browsers", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "1.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "main": "lib/public-api.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "eslint lib"}, "devDependencies": {"eslint": "^4.3.0", "mkdirp": "^0.5.1", "mocha": "^3.5.0", "request": "^2.81.0", "webidl2js": "^7.2.0"}, "gitHead": "ddf4e33b53d5ef8d77d6e06f7c38030aadab1440", "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "homepage": "https://github.com/jsdom/domexception#readme", "_id": "domexception@1.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WpwuBlZ2lQRFa4H/4w49deb9rJLot9KmqrKKjMc9qBl7CID+DdC2swoa34ccRl+anL2B6bLp6TjFdIdnzekMBQ==", "shasum": "81fe5df81b3f057052cde3a9fa9bf536a85b9ab0", "tarball": "https://registry.npmjs.org/domexception/-/domexception-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB7wn7sWOnJU1oIOGQdy1GEJ6Y3gC3ulHIEcKcJjrEgKAiEA3xjMVlSU/909QMi8eB5n1epjytIWwCenrwgSMUSK/nI="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domexception-1.0.0.tgz_1502680599604_0.9427031551022083"}, "directories": {}, "deprecated": "Use your platform's native DOMException instead"}, "1.0.1": {"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "1.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "main": "lib/public-api.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "eslint lib"}, "dependencies": {"webidl-conversions": "^4.0.2"}, "devDependencies": {"eslint": "^4.3.0", "mkdirp": "^0.5.1", "mocha": "^3.5.0", "request": "^2.81.0", "webidl2js": "^7.2.0"}, "gitHead": "5995e990587d4e3d49beb1d9f8a1e430f4be75e0", "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "homepage": "https://github.com/jsdom/domexception#readme", "_id": "domexception@1.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-raigMkn7CJNNo6Ihro1fzG7wr3fHuYVytzquZKX5n0yizGsTcYgzdIUwj1X9pK0VvjeihV+XiclP+DjwbsSKug==", "shasum": "937442644ca6a31261ef36e3ec677fe805582c90", "tarball": "https://registry.npmjs.org/domexception/-/domexception-1.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+WBKnz7FD2sCFWYJ4fwgf7JcL15Q8okU7cgKL89tffgIgMYSNqYvj96r3PMvjAftGEwim7uV3MNams0yV5iAeqgw="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domexception-1.0.1.tgz_1516581145452_0.33150808280333877"}, "directories": {}, "deprecated": "Use your platform's native DOMException instead"}, "2.0.0": {"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "2.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "main": "index.js", "scripts": {"prepare": "node scripts/generate.js", "init-wpt": "node scripts/get-latest-platform-tests.js", "pretest": "npm run prepare && npm run init-wpt", "test": "mocha", "lint": "eslint lib"}, "dependencies": {"webidl-conversions": "^4.0.2"}, "devDependencies": {"eslint": "^6.7.2", "mkdirp": "^0.5.1", "mocha": "^6.2.2", "request": "^2.88.0", "webidl2js": "^11.0.0"}, "engines": {"node": ">=8"}, "gitHead": "696792281e474a68cc8fcb4db13dec4ca8a0762f", "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "homepage": "https://github.com/jsdom/domexception#readme", "_id": "domexception@2.0.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-OVnz+UpkJ8zN7pDNROCfluq6mfq9stIRNqNGps7X6GSzjTqQPKmAxANDbQrFTQSmvHKavwCm/nTT2yXtNqAPZw==", "shasum": "914a1dcfa9a9ae57cfe7223372f57b25861cc07c", "tarball": "https://registry.npmjs.org/domexception/-/domexception-2.0.0.tgz", "fileCount": 9, "unpackedSize": 15290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6+7/CRA9TVsSAnZWagAAWs8QAI2qCP/Adig9YgP1n42T\nUCGO11C5LDamfHB8HvhIpecFWSpbOyNA/3ORUw0/lTxF76LtceKsHKEeWytz\nRhS0l9vOjL42RkrSdA29r/9lldUOTi4qNPi34kZl8lfWVdS+V6/YfbZibhAt\nOupju75D3EopRVH7LoRLj9xhxDGBF+qoYozEEHEyJi3ZTnsZIr4+p5c/cD2u\nLwbEPE/L5l7B3kqK9bOePqjbcVH4RM5H2uJJkc9BtMiFAc55RKe2q9Xxd4d4\njkr9igoPWp2HT5gd2CXG1KEcrQ4u5eRA0dwuMI4jv8z3CSQU1yf1ADIlvqOu\nTo4MpEKw/zVHCdyoW4y0mORT9R5P24bwO7MZRY19QeH58iTCVVo+vMPSwyj0\nmL2aJDMXgKit7tvPsNk8yofVgHl1undZ93yH3QuWYOUU6pFPtPbcJqj20r7W\njvFc/5Cpz8WEWC+n73mQyOHznH9Tu9pFcE6DO6lbihIYS+3Jv66zlOuWAR0p\n2OUAuT7nUA8run5u4ENj7CJzdFYwcklmW03SIlyVJBkkzwQU9lbIInf9F2cQ\n75mABFMnZzlT4A5eIdm7VX6QcqN0MvHYcjZdEy5iud9OKzk0VTL3aymCLz9+\naXEEwQ4Ei6L8vssysmScxTlA6E0eFkiynHKLvu/2juOsVr7ItXhtFTiX9ePF\nck3s\r\n=DUTm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDH6Xtbq5W1Kh3zTPb0p/Vq8WK/poZ894LgiP6DKKTICAIhAIdVLcT27ju4gkHiB36ktAkaT7fefuUwJ+t9uS9ASIXD"}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domexception_2.0.0_1575743231412_0.4986577227494411"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native DOMException instead"}, "2.0.1": {"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "2.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "main": "index.js", "scripts": {"prepare": "node scripts/generate.js", "init-wpt": "node scripts/get-latest-platform-tests.js", "pretest": "npm run prepare && npm run init-wpt", "test": "mocha", "lint": "eslint lib"}, "dependencies": {"webidl-conversions": "^5.0.0"}, "devDependencies": {"eslint": "^6.7.2", "mkdirp": "^0.5.1", "mocha": "^6.2.2", "request": "^2.88.0", "webidl2js": "^12.0.0"}, "engines": {"node": ">=8"}, "gitHead": "58fee1913c3586b297ac340f0ea30b26d005f8a8", "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "homepage": "https://github.com/jsdom/domexception#readme", "_id": "domexception@2.0.1", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg==", "shasum": "fb44aefba793e1574b0af6aed2801d057529f304", "tarball": "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz", "fileCount": 9, "unpackedSize": 15542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9Tv9CRA9TVsSAnZWagAAessP/35PizgLrtUzzbaWkAjB\n9CEVNi+e+w38PSPao6ybSY1BEUOEULghPEpCvBosiGIeidl/u6HKQSKHBN/0\nuQv1D3fy1JMjEQaj3KZ/DGIoBaiZKL14DwiiTOs3YVYBxuB+zLW1/99FdAIq\nSyesAiagoFmZ8ZuUn3aS5GtiikpkmF39/N6XT9Wn0nefdXxOnN0Fs3IeiNvK\npBiBUTa8IWV8M6eifccQbxzqCZYr2FD4tG2Jwl9/zYNFvViNMM09+2fnDH2i\nswBQzlbqTtmd74iVst/fmvHyVbC6dfjNYAmYAuf2JvOrqgo+FVdEeGrf9RTE\n8X3rMgEIIz4hHCaT0OZt+F2w4Us2GReQGzc+P5+9FPUez2OZU8u6gqD5ubOx\nQ/BnKyXQBsd5qAXGK53E1wmcNXsnbVAEqv7QZBgBvTTEJnLZ0Gvpeg0920BY\ngERAhEyHwJMfL69RlrcNWtZdRErzhQc2tN1nuPrXo+7qeuHsF8304k/j0FPm\ndttqx8o6TK5WsMlM5TbFMVSC5l0iesg7olN/oOxA/npWMGYJYu3QvShhug0J\nueyVGf03rpqT1XAa5zIkHiFYKLdV6r17/Z3yY3EyRih4vwvjzey86bylY8ou\nksYJCFrPrn6AcxjwR8K4hXvt5DYnovPN0KHopRdrM8Xu0kuj7gdURgNisPvl\nE9EF\r\n=ludO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEus3VV/dVlvtjE7Ln6ZUequm60QBW0PQNYTGIGmtz5PAiEAnSXjPmut4mEPMi7ID97krWFNFsXn1WYTBBGbnuKS378="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domexception_2.0.1_1576352765265_0.9754469431379906"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native DOMException instead"}, "3.0.0": {"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "3.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "main": "index.js", "scripts": {"prepare": "node scripts/generate.js", "init-wpt": "node scripts/get-latest-platform-tests.js", "pretest": "npm run prepare && npm run init-wpt", "test": "mocha", "lint": "eslint ."}, "dependencies": {"webidl-conversions": "^7.0.0"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "eslint": "^7.32.0", "minipass-fetch": "^1.4.1", "mocha": "^9.1.2", "webidl2js": "^16.2.0"}, "engines": {"node": ">=12"}, "gitHead": "a42eab52a4f9b81ab0ccd9bf16a4722f74725fd7", "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "homepage": "https://github.com/jsdom/domexception#readme", "_id": "domexception@3.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-jq/2HAjXFaOSzLh7vQB14jmaxm0zoAOlsejIhHiCemUBEx1qS5Vvradng9Rx4PLIglpKHHYf4RZT3SLK8DO7fQ==", "shasum": "55a1b652ae76b717679cc13cb8447bba2aa0271d", "tarball": "https://registry.npmjs.org/domexception/-/domexception-3.0.0.tgz", "fileCount": 11, "unpackedSize": 18658, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOXIpMCRSYINJacEjhDX5BScCR44UIQPScJ0QzRd9GXQIhAODZ/o0QJKIyqnbf5Z8EWIfZx56+8r9raQTgvZpozsPG"}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domexception_3.0.0_1633301406134_0.7735417301627232"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native DOMException instead"}, "4.0.0": {"name": "domexception", "description": "An implementation of the DOMException class from browsers", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "version": "4.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "main": "index.js", "scripts": {"prepare": "node scripts/generate.js", "init-wpt": "node scripts/get-latest-platform-tests.js", "pretest": "npm run prepare && npm run init-wpt", "test": "mocha", "lint": "eslint ."}, "dependencies": {"webidl-conversions": "^7.0.0"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "eslint": "^7.32.0", "minipass-fetch": "^1.4.1", "mocha": "^9.1.2", "webidl2js": "^17.0.0"}, "engines": {"node": ">=12"}, "gitHead": "29ea43cc590749551ee92582a58f9517074c543f", "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "homepage": "https://github.com/jsdom/domexception#readme", "_id": "domexception@4.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==", "shasum": "4ad1be56ccadc86fc76d033353999a8037d03673", "tarball": "https://registry.npmjs.org/domexception/-/domexception-4.0.0.tgz", "fileCount": 11, "unpackedSize": 20182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2oipCRA9TVsSAnZWagAA3PcP/jo/GvmLt259Cts5LDur\nGL7WjY4IR9TN+vHQt6ZjyZvQPAM3TLy6fwhvfRdw2FNqeKjwcQtzt5uU+l2n\nGblYSF3+bibmfJ2zjYBfFzF0deY4KB+xYRkw4gzMrw0DbWP8Q0g2Iavl5sxj\nnwLzlfjOaH73Z+kjFWHQnHi2pleirEWiIbWEEupCSmM1d02IIZKV3Dc4gShM\nN979Jlf6PGGGxfg+Knz4PEt82DRlZZtp4tzlsvDowI+EPzordsKkjFqDg2mh\ndpOSbVeMPF8A91cG93tPxfqcPrRI2vuvrmybk8zhTvmCSvmzXU12r74clFNd\nlQisTfYPce7MDlUC8PGCrlLGrmLXawLSlLPg78vVN/Tgc2fWqG4FPKwAJ+xh\nVlTUNKmet66396ViTZ6P5nHoOqgGF4m2RJxema8yaF1sfRqbAsnKGkKA10V2\nhgn/1XwfXg6I1DmK/b6wdr+Kj07wkZu4pmgfq5iqlyGwx/NzEsLq7/4xoHH3\njPLk+A17BaFaki+dFcPGkC2NvY28GQam7qihymoX2+ycKdfkl6CEOe/IsSz0\nW1ex2qMMTSe/Ls40fpAJr30zFkAsslWPzS8k+rye4/YwOM4Gyribsn7Pr3HQ\numovK0J5LapcBUFV15WJS6xZDhZItzGNHGvAWF0hU7j7nADbXPrkFbUIrR7P\nKAAL\r\n=OL/6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/xwAWtjglXiywO7ecqj58iFZMtuiBTkgRguvN4PkYQAiAKudMGIRB5l4sXN3jEbRc+skV1ctDExd5epapmfLdIeQ=="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domexception_4.0.0_1633538689390_0.5470600130309544"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native DOMException instead"}}, "readme": "# DOMException\n\nThis package implements the [`DOMException`](https://heycam.github.io/webidl/#idl-DOMException) class, from web browsers. It exists in service of [jsdom](https://github.com/jsdom/jsdom) and related packages.\n\nExample usage:\n\n```js\nconst DOMException = require(\"domexception\");\n\nconst e1 = new DOMException(\"Something went wrong\", \"BadThingsError\");\nconsole.assert(e1.name === \"BadThingsError\");\nconsole.assert(e1.code === 0);\n\nconst e2 = new DOMException(\"Another exciting error message\", \"NoModificationAllowedError\");\nconsole.assert(e2.name === \"NoModificationAllowedError\");\nconsole.assert(e2.code === 7);\n\nconsole.assert(DOMException.INUSE_ATTRIBUTE_ERR === 10);\n```\n\n## APIs\n\nThis package exposes two flavors of the `DOMException` interface depending on the imported module.\n\n### `domexception` module\n\nThis module default-exports the `DOMException` interface constructor.\n\n### `domexception/webidl2js-wrapper` module\n\nThis module exports the `DOMException` [interface wrapper API](https://github.com/jsdom/webidl2js#for-interfaces) generated by [webidl2js](https://github.com/jsdom/webidl2js).\n", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "time": {"modified": "2023-11-26T15:13:29.937Z", "created": "2017-08-14T03:16:40.561Z", "1.0.0": "2017-08-14T03:16:40.561Z", "1.0.1": "2018-01-22T00:32:26.351Z", "2.0.0": "2019-12-07T18:27:11.550Z", "2.0.1": "2019-12-14T19:46:05.375Z", "3.0.0": "2021-10-03T22:50:06.291Z", "4.0.0": "2021-10-06T16:44:49.514Z"}, "homepage": "https://github.com/jsdom/domexception#readme", "keywords": ["dom", "webidl", "web idl", "domexception", "error", "exception"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/domexception.git"}, "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/domexception/issues"}, "license": "MIT", "readmeFilename": "README.md"}