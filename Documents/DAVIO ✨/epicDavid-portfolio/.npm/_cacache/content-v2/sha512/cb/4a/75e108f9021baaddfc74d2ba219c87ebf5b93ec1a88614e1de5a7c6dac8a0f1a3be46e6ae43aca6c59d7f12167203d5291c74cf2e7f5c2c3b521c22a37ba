{"_id": "@babel/plugin-transform-for-of", "_rev": "129-3c50fc37802833bc7e5cd9600de2a9a3", "name": "@babel/plugin-transform-for-of", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1c467672803c135dbd06eeedc92e9c48c175dfa3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.4.tgz", "integrity": "sha512-Wj00srmy2O+uaCJ6uxy8X+LvV/sX59pof/z9mTKj3NxngiObHU6kACvzo2RYqcQAnIMLRtzFHUnu+rI3FQ7S4Q==", "signatures": [{"sig": "MEQCICtIo52Jga9QqVf6ROebf48N67EBT+edzIshG6UYEL/yAiB8zBWjAnkiifQDigSDfmZ/CDqwiwzfaw9wjpdRBpCJHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.4.tgz_1509388475621_0.8676948146894574", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "13f7fdc1968838e47ef12308623ed5fc5feb954e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.5.tgz", "integrity": "sha512-4JepXGtZjLjW4wV8GUvCz5OVDymJjKxMIIJ61G3Rbgl2/eURPvXzR8lIZsC2nzh5haiyMhLoiq69tUr2WdU7Ug==", "signatures": [{"sig": "MEUCIQCVlogrXzfk59tCTv6/d68aXhOyEHjsyw91bF5lOSYnhwIgCiHN+l2wBvyFFykeNwNyj1G9iSsvhY2zNDyKKjw9Jco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.5.tgz_1509396975057_0.9072201231028885", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "238c3157eb95859bf3fc35fe12b92869264f4fdf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.31.tgz", "integrity": "sha512-aDO6QokC3emLJ7eFVMr0xpVTu7l879xCykC89Ig0lulMAn84IeyMq3ZdzEQVv1i8uMkNXUBmprVZL/BCSkj98g==", "signatures": [{"sig": "MEUCIAGaMctTjpe/s5JEs0MlyFL9ry2cBzTKQAFfdU3IUzrHAiEAznhl7LlOYi+8XykzDQPlL29eKDp2pFbu+VAWLfLnECI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.31.tgz_1509739403210_0.01482198340818286", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "73fefd71f1518243466c6a0f9e9bb2e4c305a61a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.32.tgz", "integrity": "sha512-ZtqM+YVsF+AcVo+lFhkjObBrifD/tRFbjHVGCw79Y9imNqn8KIlC2NAN7dcz/G5A5Qn87KYwug5kyJ79PHoeDA==", "signatures": [{"sig": "MEYCIQD7+nkYzKy5ggPSwYDGYhT5oMv9hHsKpWhNF2APOlO34gIhAKKoxVV/C+KLtPpe0JB3V7Mncg+kEeRfLdVqBY3Lzun6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.32.tgz_1510493593355_0.48983508138917387", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1405d6bbe7c48dc0a125ed57e1bfc98c6e06dc8c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.33.tgz", "integrity": "sha512-gwMsIxxLWpWjewu89CHwYinlwtv+uRjnJGQNOUUcSlgn3KGtt5inTjvkZg3sWMlOCYkEkEuZfdypfHR6aVa/tg==", "signatures": [{"sig": "MEUCIQDVDoY38r+WdvcRfPP+FaH1lMTPlPjZsbakztJF6g8E+QIgFK/wRpZ4c1Jky2A/IusV2uDekdK0nQ4GGwVx+l+cy58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.33.tgz_1512138496153_0.7918374335858971", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "046d1ab80157e70fb0bdeed01443f26e48fbfcf4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.34.tgz", "integrity": "sha512-BwSxmFpZJ0pWjokMiXdhuH+oApTkrn26m50LMQk346A6Zvu+HJ/UdmaS4zw58809twrggOMaEoip0XuPfMPLUw==", "signatures": [{"sig": "MEUCIQD17cegjT1RPswOgXAfCb+4UGyk+0HyU+TrCH0JYMf9YAIgR/rQM0ELPpjDyLTMhkoxNeqIVjmV2l8TXh3E/Wju4L0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.34.tgz_1512225556438_0.8974282045383006", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "50774c6c1cf68a38493ee76d34acb7b55470e05f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.35.tgz", "integrity": "sha512-iBefZVo6LLNILCaaa5530OANwqQHXjHtu3vdiL9M9A5r0ULj2kAqSNKkMDvQ0Yaxe0M7VzUOHVsWoUzrSDH6lQ==", "signatures": [{"sig": "MEUCICC1hOekPb1Np0r1xnsBQyMb+ThjiSL1DrEbhNIna1zUAiEA00HAankm1fxx5VWShb1Vw3vDZdHqlD0l3uA29vfSJKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.35.tgz_1513288063602_0.06614756071940064", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "71aa5f727c8e9f458ab700e5e9f92ef868a70093", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.36.tgz", "integrity": "sha512-KuBEFkwwp1Rwb782nBJi0FpObFyexTtclNIbqzWgMna7HQsMpJtmVBBC7encRK31ZpTQnWKHz1IyHnVTO2FdQA==", "signatures": [{"sig": "MEUCIQDVlsUu8/U48gBK9GV0OIbg5Nxi86tisEjVyAQbtn03PAIgBZtYZ3CxZxzMVM9SqjmcsmiGEg07fsS44smPtA28YoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.36.tgz_1514228675042_0.1061358917504549", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c02c9c0fcb8dc2aa928e9595d831d590e28fa765", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.37.tgz", "integrity": "sha512-ULsgsAAj0fGFVDdEZeajaKuTEXAoh/tza2VwAPC6h0qaflB01k7DojlTHEiap6h0Fs3m+P0Ax/rcH+sYeJx1Ow==", "signatures": [{"sig": "MEYCIQDMJH5YDdcV0+ocaqh6EHYuQOaIDA6Hj1B2xmcrqA63BQIhAIxl+TLaKhDfiWCbozLqExJwUzT8EpOc4H45/GSiG3AY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.37.tgz_1515427349350_0.961452568648383", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fbca88770877d45c6bf26b492e4b126ee43b8111", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.38.tgz", "integrity": "sha512-ui9oNgRvVNYzJeYbOmNCL1UIr1++7twyiYIuzOuQ9ArSF1WOaO4fluQa43zccgWRd610CRkljNRszag0aKB4Wg==", "signatures": [{"sig": "MEUCIDS1xxVOXR9LWuZWBFJRz0ciit+QkSDc/BKqTi+kEm/QAiEA91Bzo7zGpZnOfG/1VntKedT9SEhj24M9FVsgUduPp+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.38.tgz_1516206711515_0.9692230201326311", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "77d11bcc9a21267c5848511431ad28b1aa1401db", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.39.tgz", "integrity": "sha512-FwM6eMvqAXHlwH6mn4fWxgaEMka5tTz1eD8sQu4xSeyLVc+4iym0x1Par7TicDwHLDqcSy4E5Mlw9JajMY9k7Q==", "signatures": [{"sig": "MEUCIQCrHNlDcsCWjGw6i/8T9bYfNLpAbxtHRG2axZz6OqsliAIgGgCEkhdwml2Fsb6fjUg8JRg6Xj0bydUxRQjBOFjg4XY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of-7.0.0-beta.39.tgz_1517344052379_0.24714867235161364", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "67920d749bac4840ceeae9907d918dad33908244", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-ArDbLAGMzI++G5Ut8HIbLvnAxZNOC5tFzMXiud51JJTHRaeFB7AwX+duY9x/Hu/KypISXjels3BYVYCV/EH+ow==", "signatures": [{"sig": "MEUCIQDBW7ZPAcWplwdMvb4UG/2JG7LAta6DisXl4YCQaMiSoAIgezdZTY1QK2ir67DqoywBo/EUHRIHyXQcdiCb3EHqF68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12222}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.40_1518453693855_0.08171490206906795", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5f3519a5ffcca00eaa07f1c0faa83f35319fbff7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-t+ESNeYMC+Fff5U54ODMaExhOEmXPHJj6gOPdtrgRSH0VRRuZpm9rbieNqnA1+f/bhRei2/kE24Y/x1Pz3CRBQ==", "signatures": [{"sig": "MEUCIGz9xZTWioPjbEy3Je4myTVXbheFzTxDWWZd6h+hlXQuAiEAvLCuZjz2QRmuG9MWLDoFLqLNpe7xkmE5wsimWE5nAyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12454}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.41_1521044766916_0.12110377022725838", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "acf51c5986050e1aff054c8d2a95ef3f6bec153e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-pn+fmrr6pfXeEUMyx7JrKAOG/XCiABvXFOmQYqLoCl0POcW8sE75r8w/Lu2wHFayrNgDqR7/RCb7RW4h/U2u1Q==", "signatures": [{"sig": "MEYCIQC+tmsboX2SkFPESKC+y6FlEjoNVNbT6LSQY2goU2PJmAIhAIu4mm3JFFSt/PJuMRv6cPUvulrHzchx17fK88LPVwqM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12454}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.42_1521147041934_0.2690711323401709", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f309f82b4ab3f2de44c119ce28fd0535cab31623", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-x1dZhT370HW0R6/+X01dxsDR+x5bejHtuM+qMkDdzVsQZI/nPWV/KLf7dCuFvY0cZOmP9j2Of6hzVYAD2ODdxQ==", "signatures": [{"sig": "MEQCIAEd4U0jh2ogrmrVIyRHDSOs3QN32c2mphp1QFlyg98ZAiBzigpsTVPHpW1kQfHE38g6QV+8TYo55BnqE9zDnR6H6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12725}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.43_1522687703664_0.08516388528929375", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b157e38e74c07beacbac01c1946b8ad11dbea32c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-910UuvEuN6CM3G7+V3fHnYFBbw/YZUGgQlXpdlQnzN43uny2Xy33BxoFlWq1dOS1Q7xJnsJNEb2mm2Eks2uTvg==", "signatures": [{"sig": "MEUCIA4ajV/HKKSnNDP51rYTt/sf3D3t8f83N0Kx1D0KIQ8ZAiEAi+rGwlasPIm55ac++xkUaxkdHuP8mxG/GprcIdL0sO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12858}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.44_1522707606244_0.12454945990879862", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bf0095583d2951ce88f7aa017622cb3e92a71f0c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-iV1yH5vEDKBodp6GnUNGMm/7qkO/nhi0uKSCxX31CemluQxDdlHVHwS9F2Ie7nCrLOvBhD6TIh8EeFd1w61wsw==", "signatures": [{"sig": "MEUCIQDtxuYqKaTJk3g8g0zndyqH4l2bjVdI44YYz879oz02oQIgLwkTeGSP5Qo7FThVTmpeOXOaGwqyCeY06VwFQWg7mW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1fCRA9TVsSAnZWagAA+UsP/jNh669FFS2HawKHp2Gq\nc8LT2JF5OkMPI1bbFzAHlZTBxPCUe1iX/1f2rbbChLN6Bc9LZm9vJisG4kCd\nuarlUTRcdR6WmaT72+Na/ttlTyOrL3rzYYNwaQy8v7lNZ8OEVMl9SdYF2VV/\nn0jr/CwEDeUgwBzRzoeYo/LVr08e2DRbwbUO00VEpQHcuPNs99MIyNOxSgIn\nN7PZDaRVhhZpSYaM/+DzPGgELRoRCw7WtZW5Hin7pA7njvF8Qzj907kqwtyS\nRLfK7mCa7cwtyIxyVJPLf11UqmrNh5FnwFyszVFLeOzAiNtwkGG3pGexruDg\nojFjmmTlZQ9uk/RjTVjJ+KQyvaUjsOe9EAnAWvo3WgvP4VLs0GtnEe7NMAxg\nyaDYgQNjlRcnH40JYMw1mrBklxEVjSrq0knfXnqGOnKxNNo3wi15+Wj4SR3V\nJfu5I6NTO0pMDb86gGZNYT9cJE1/0S5FJ3rLbtR4dyfKG9ZpTLVEicdYgztY\ns3i1sbUtwCje18qbLrS5Ads5dStHe6n3HiwOl0O8JnJHj5Llx87/slWHbO4O\nGkoMKXGlehd5ZOZ5DOHTorWtZe6TU7iYTnM3B9WYxy6YHVGhkVcmaiyOODsX\nl7C1pju947q8aHQlJ+pEgXS8fDvCYbGRnZ9M5obKiQr1xBziX46wMzWpusqn\nvfc/\r\n=v+eM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.45_1524448607108_0.9717994194133148", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ce643487384c96d1bd1f57a112b2ccba6c34da5c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-a1gpwuO26szyz5K2FrRrI5nUDgvkaJfZ7GeDtFAH8XyrK/pNdtpW/7DFCf1PdQc6SbEMM/1QXsH7Y2YRkWoTeA==", "signatures": [{"sig": "MEUCIHTRj94B35cePorL4HmZoXTbaQlNUFne235gRzpdLIhBAiEArjTyz1SG1sgt2i1/WbrJn65Rmi5F2wGQXL4ByGGalMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGNCRA9TVsSAnZWagAAbekP/3SzpezCy7Ea0MGwaH40\n8xsdk+MWyTHz7nYK0Md/4cUqUy4yixcnxOz6BHRp3UIqECVG5wpmziouXK7E\np0tOpybj0DptrecAxRK10TEFAGWwDGWJm2kpUz9gFc6g4bVRPehBzcFpAnr6\nGNL6DonvVkhMUGYJ9HFuQks04/QI3KdBPQiwxZoEMgjM+6Ray+2MVHXDqCJk\nqiYAYsjQegdBj2diQAK9YtjD2ZgpUZUv3xNJqYnPHt+JGbttmsPv+R/NlkDb\njtxaGiUU/HinptuPVBaD4NPVAW47HAg5RI0i8y5uxdGs9Wq518x+fyoVT/dJ\nyTorfnA+L2bm7PMnWy2PEtexHk9E/dcE2wkyogNrjWHmeK8JmcGq40T38Azv\nLbIdW0WvJh78qMFGm0EDqd/YRbsr6CgZpVO/wRuPDfUIO3edCpUVfL8qG9vp\nuyluyyFz3NkD8+2q5VtHJxE5y/A4FDWSwcgZevOuSZYUPJITTx39sVz112qP\nblpUCMDt7dV/AIcSFthz5bSpmY1IgsyJNXnjmfdtbESV3CmSOVvQR8efOyde\noofDPi+FAIaap/DG76NPRjNp0GmJXM55KsixzixGWiH+TQH4lmyNceI/tcL0\nQdgOdriphzVfsnr4lsBhIK/W4K88t97VnxPdfv5C7Fka/cz8Y6I9U+WyYnm/\ng1aa\r\n=qq1D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.46_1524457868755_0.7834453689668464", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "527d5dc24e4a4ad0fc1d0a3990d29968cb984e76", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-tfH5OMzV9fWLYJTzWDhoRJKr8kvBZWH26jiCgM0ayNq75ES/X947MqMNAgBjJdTAVEV2kOyks2ItgNAJT4rOUw==", "signatures": [{"sig": "MEUCIEB4BiW96P75ao344itrseMU5+p17jnyrRE9d2HMBHBcAiEA9c09eVE4sd1teRIAs9tjaG70sDXYuFV0S6s1j3Awam8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUUCRA9TVsSAnZWagAAQW4P/j2np5t/XXW658cJS5zA\nUb1UDJ7V3KKgYKmPRXWV9kNWSVzdHiOQDLuahJdTb7I73GQmZLA6DAQFPfXl\nfXl0xwwBuuAP2s7AZ3Yr7cPYBBCw7RGAOhpqlgtRa6KmN2ny7Q8KFck38RpP\n3PqTZzlL6RMcbl1XwJH/D+XYOV8RR8ms2GvtXFadpwifD7vQ9jWNSiJx53wy\nzJaItXZCXr6b44KcCVxtUBK85mYK9xm/FVpRK8Ixdt6ZCSH8A7dHl9VQ0Gcf\nZC5jLm7VDpBsyz/dVuPwrJmCWFmShMjWsAh9r/V26s2dTqnw2J/AaaV8xrBE\nQkY60oHqVYIuqhW90OsYxD1vix6R39htimQvL10yBSPni+ViNDaoeV5T/eAn\nlLyNE2g0Bw1xS6jCAA4Sv16DTXl7ExyQmCOEdiOJ4itJKwLSIBxP5Emi5t28\nlHA8jcZGcLqsOI1UAhXZm35LtpAYMjCj+t1xVgICXehVnNt5zvlqcshMyb0f\n2zKrnBzUbl8v+i7D71vfiN3HMt8BeffNvgMG/KpJbbHI4TLypFANwpGq72eY\nxuSrbuBB+UMYWgiaPuqHUyENhhVoR34Vu+h1zMA4KT+xINNHV0ChIhvOP6/U\njzavoSiUVler4UilKsCsb8hW/EB4gCGGKAlUqm1wtjGWNvm0g8aQ5hH3sPpN\n5vpA\r\n=CfHS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.47_1526342931799_0.7222557455878678", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "654e89c230a03132ce690dcf05d03a8fe3c1cae6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-Pm1iNyT1bZEB0aQs+VvMuubOTPEuDCbonljBKp4/VxP89S8nmyyrw1jAsxUOUnZq1RlOzcrhKFmd+gI/C3DOoQ==", "signatures": [{"sig": "MEUCIQD3OB8UWxE7vVTz5+EgreZcFG5IONSqWOun726TAl8T2wIgGOl2dlvr4AmRMFswI8gF+uMtSCY+etJTd9cSBPf20ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDtCRA9TVsSAnZWagAAZ5oQAIA1Xy3MxLrGUW5VOEEm\nBQzNC73aVret5IXYsRwdLZXhr9Mg5ubRobJnUFIbI/HjCsSvK1gyej7WEgCS\nbFsqfCuwOlA4pmXDpJNwezUc/Pz1VQQcr402VOs7OUc7xnEs4+M4wQMuvOGh\nlSpIFuPSc8GWGOrZYT6VTFy8ckwAzFuNpHsAe8fhQ29dBgIn5J66cwQqitTg\nb+NPVAnQyVg9OVArngMeH0gTPtg5rxhkC3Ieh4ZjkvvRuXIg63e97mbAe0kL\nAL73/yHqOTt59NQukA8fkJ+T19bCBu/53Qv6RimGfxyrcS+MS0OTEZAWx4Jr\nwkiJcynAcjeHExos67G0NbFV4LQ3pWhJwiv/Jjzz+wpFnOvWLLrLE8rAcNpL\nNMeRyhIpYE0IQtqtBplL30ChtyKGB6+nZ92cEf3F3Y4uwY8GPK1hkwDAak9o\nqLcIncFW4EC1dVLNg1kOK/2v774tMJ2Uiool6nqIWUKMF8Laky5iUcTK/6W6\nDnltdzA+6/b9oktnqbk/plzIdM2GZaeDYCkhZc5bxwCsAwT3EvGcOikJYKoK\nfSGnITPbJJjLUNiYALi1wwCa2HLqmcEKTnAj2H/bHWMZ6tyaGZDRIs7bhjwd\nfqubFYQJnfp+bLzfd0ZNiGiEG8yRettZltUht9zOGGi0ZJ5yjvlsZ5jJuS0w\noohA\r\n=siMa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.48_1527189740504_0.15769282586777367", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3ec72726bf1d89a0d4d511be7a9549066f57aade", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-2kC7worSE7HiSR6z41iEQmMNM84XQafDl9UYn5anmaCLBPvdN7GMLgmNIZYr9TmF3zfxAqRfDNl42luIjchU5A==", "signatures": [{"sig": "MEUCIQDP2MtdqFb/sL3qH6AJtYfxqABMf0WeR/iHp/d2C1KibAIgGkBECon39D3RQMYbd2cSvJV6t/mvrHKmfnQ0gGULcxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDN+CRA9TVsSAnZWagAA39QP/2/67V8uC3fIjs+4Q4Na\nnYcZ/k2Io1I5kQ6jo19OStHxS3I6kaNbhycgBHcpo/0gSD3VPbkxwAJ/cXsD\npo90q/9DTOGWLgQ/Am40AS57KP5HPftzArV8+ryPN54zA0MyJbu+dKAr95xy\ns4tqxb1llLiVXYcuW6N2Adppy7fVGtBm5b+P5dGxV0hVvQjRdb72+3l1Dlbn\nh7/XMeZvlrnWZgGXeRx2HYld+ZOF5atvllBh9bWSh4rV0thttMfmki30ANoK\njYsr4A2o4N2OmfiaZbwm8INkHrqW7LWlwJxB+d22X1Kd3WQiSHPWSuOIdC52\nxrSosj6G58S37PoPtIT8zQtdn2nZ2KSdouACKRuJ1QkZEWEqOLKc88srbiGT\ngE2Ql+n/zD1tic+EaEj2EsEKc2SLxBm4MiDPdSYgpIbjAyvRneHmj4Da9U9p\nO7kalp5Oym1jk0o5uJJLjf3+QUEDK9t0cW6Of9GUXzNIzZN5WHsT+GFBEX3f\nashUMXRujCg3Pjqb/9E5mPBaig13F7k+245dt8hsZzB34ZCY9xzxeqzyB5RZ\nDb5ZJJ5TBHmdZhgJY+MALoPa1LtMZo9COs64RSgYec3t0FXCLxFO/lAidRBS\nFP4jLi0jY7BUAx7w78MuRp2YiX58A/IurzeWWUiP/cwVM6BJcK838yMcZplA\nNYdL\r\n=bS9y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "3ec72726bf1d89a0d4d511be7a9549066f57aade", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.49_1527264125810_0.9107014660360366", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "eb73bf473c563e7cc744abe8e531d4f0bfc7d613", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-G6lkLUCqEunwTS5vB0uikUeoC/WUoCo3Od8TUzAdORMxdFOZ1AB5oy3XUw1H08J/mJ7Gxa3ccIBftQtwFTsS/A==", "signatures": [{"sig": "MEYCIQC0oDpKS+byFnVuqa+I0CVTPVsQuJ9WN2Y2MIP33WX2MQIhANqwm638THmacNnpYGLb9ZR94jXKac7lZPEgvklkWinV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10337}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.50_1528832835534_0.8383584830966502", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "44f476b06c4035517a8403a2624fb164c4371455", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-AjAH2ZFl0hXahP0eenk9NCyRzZdTRpxj9tIkZEYZNYiCgVR9FHYOg+d648VlcabZXprwpUwJboFthVTqoiceeA==", "signatures": [{"sig": "MEQCIFw15LMO8/mChCa6t8F3TyhQOgu9pqVfrJUd/WfskgRFAiBSqoozsrUC/XB2xPzXr4OD657haEHsD0dhrKmJoo/l4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10351}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.51_1528838385998_0.049636161814268664", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "42e678de92b39387e7bb3a5e784b00b7ffe85ea7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-nIXeH3gYOsTV6OAczzm6qblIT53yFNhaelZQ7XpqX2jKqBH1V5YIYpPz6mNvk1KQjR3AYHxK9xJQyiNImikQKw==", "signatures": [{"sig": "MEUCIQCpF0eVlpYcpeamLj4udP5FicFcQamNaGw5yQ6Ya6+gKwIgC1Gfr4oi7VAN/pfrXUOZBHq7Q0gMf8dXChVZGL8wXik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10350}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.52_1530838764582_0.5839603479814255", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fa065215e18569c8f74dd524b5721e11dcca973b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-7DD7gd/ywy3fTBZvQ8CiulD3SsUZLNrw22zD4nmH7LS8mTFcUnAsenbzHMDKmFs02ZwkLaAOS0lAQZFuSA/bNQ==", "signatures": [{"sig": "MEQCIFZZHzp1CKB5Ufz/++30k1usNtaKgb1gu3zLxCBor58GAiA7K3FehZYHmQeRUibXPhk348TQqzEiSCH6Z+q76jfEIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10350}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.53_1531316414981_0.6816357317919763", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "261d2992058a9e09234b9ff67820054ffc55f79c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-e<PERSON>eelnU80Z3Qbn4TBBCwq3Dq/DTJsYeRsXu68b0bWR+FO3kjRANVPsKK/gWZeK/2wI1aUrwW5fgMJHMTj2ERxA==", "signatures": [{"sig": "MEYCIQD0hs6cvA5yj5L0gl9BQRzt9UBxJzFuQEK9cMnKRfj/twIhAIXh/uwl+z0eWG3sCG60Gz669MMefPHers8Mlo9WwOxZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10350}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.54_1531764005786_0.786881754463493", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cf3058c6d81a3d69e5df086294688dac28a42710", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-io5ck0mY//Q082nZO/svwNNWff6T30BRdfCIOyL7EY/+mhjBBB6zQxlvHYeoRbMIi5cf/HP1FoHMW0L4BSzhvw==", "signatures": [{"sig": "MEUCIDlSgXfdHbMizgVu2w1tBE8THM8gZpVqRJeoyIeUXmVFAiEAs8zmjdDYKxFP2k19UqGp0SC5w5a1PL4acElqf0y0GW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10350}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.55_1532815635301_0.23165469035904285", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a95335820f8dedf30b96ec03e4cac09a5bb4edf2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-z4sift6xY65vOpFlRyPrcKNgusCV9NZZGOR9Dxt64XUEWnyxpabHZ9mGe0B3mqJbm168cK7sbxruvnyfyrO2fg==", "signatures": [{"sig": "MEUCIQDb5uLnYLXZqqDA7jv/l0/Bg+OYogrMbcPS/kRK2KubsgIgbwfvnaKXaij2k3PrMuWhjvfBsV94z7tgTSXNjynhmXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvdCRA9TVsSAnZWagAAfRgP/RIC7JOtmHdYRIes+Bx8\nBFL58xCMv0jbNKoFsQe3GLaPZYUuxhBKrIfF/YXtEwHxRxX0OGHus0mlY4Tt\niSn70dkV6Dx93hnmX/umpg4aF+z4b9N48IWbEFJBRseV+IavZ6QP0Ln/oJQ8\nVQz+r0PYhqCYa12BKqpAdWnL98bAFqJDyMHXedPtfC44K7Msh0gop8MgKg0Z\nuauEGXvN8EOX2GxySybMtx/+pjHXtj0icacECk+6ZWWebSEpP9/SWfk/MqS3\nCs6wKuGvkemzbpjhLo1qun7gTX/lJJJRRanDsB7KnA97FFHoOBzwXffQtQ/Z\nUyV25nqBmgy/bb+fjkUrOjRLFnCp5npShxLB0veYU1OFGSt60BJTO65AsKLg\n/k/G5Yawdwiunfaw2WGslBftUj4KcBtm1sYIu5a9FELdt7LE4PtgYwtn3wfN\nrFhmCHyjEKrbcew/FOzqRSq7zxAvsalAxHAZ9qL+nXn6B+1niyheLo8jWS6p\nInRPvoR8EDaEBHV7qVL5fJecl3eoryqMe8xEoGEtLco9epayQwoflydac2nG\nr/a3ueaacLcvpCzC9YtnKspPLVW8xYDoHVaAs5wBwp+O8h03MrOU1dn1rqiV\nDAnWpb3VN1UuG7WkWUzLA+rQBwEYHlykuccLbkBiwJ3mhcvmhYLaqVMsFaP5\nfVA9\r\n=Mig/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-beta.56_1533344725972_0.7623907861468826", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a54e9ac190f379dabb673405730f1257c75e2a36", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-mUvrRWPWwtHmhqg3rwkGO8wkgynSxOexECVXsGm7WLclHQlodvXce5DcYO3c/cL9dXyiHfX+45UqcMHEJimZog==", "signatures": [{"sig": "MEUCIH1ob9m17G779GWHONUZNz44d4+bTTjHOoc+Tm5/IrkLAiEAicPeMZGGa1BTsp0NDkB/ICHf19NCsjvXuNNQqh7XONU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSXCRA9TVsSAnZWagAAz54QAJnmK3/CWP+Hz6Cu3970\nmggS7pNh/hvQxRs1t0IJPljOjC5sU/0fFiRzLVzqmwdVt+g4WVcbUuGR4aUr\nZaviSLwrvxlULZ9rJs2te5wlYefamWi0FXo65sIr+dqErhXNUwAuQtjxwBid\ncexYlBWRG8MaoadhI6sbvFVTtS6fw27xyODTUUx+mz3zaTXivzr2rgtIpQKV\nIIYn8C4vDWOl+WwxRuNQFm/pKzs154X5LKttrFi9YCCL1NINvWEetD9gB/WR\n9fgY09eAFyOvcGvADlfdu8HM6QI48bnvG1SLJoZ3beNFu6gQnrNwhCJatamd\nMJZr/1Znw+p5hqx+R04YhG1QL9npFSvWGeDbOfpeJ67IaPFNiQfndTnHKcug\nzyLw3XijrwyMHPwtnKKHeOi0zWm+hp2Aj8iNH4TYf0w/NQtLQvK9rrClCQD7\n2OiuTQytodH/U+fyt3KksgpKltIJUxjM5xvWZuwkP/FbePbcc91cLzhRMEns\n8E7MZspcuwln+TlC16TnGCo93GjdW5XVHlUxttF/CDqUNi2d7ftG/KxblgHy\nfq/JJOt/3HbHWth3GypA8gpKs87e4sbOdq64VirJDlg+xUQ/I9dEwl5r2fdb\nn94tLAj//Unsty8++f+ZuUmf171e5UBnJ2NRpXdX4+0WJNRPl0oc1DJmIBvR\nW4Nu\r\n=T2hl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-rc.0_1533830294631_0.3012329144335584", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1ad4f8986003f38db9251fb694c4f86657e9ec18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-v09o2ywKHu+b/vkLknjKPV9QXCxuU2cVFxkWhBqcKwl3ERe3clhiab7a/8T9Sc332o4Im6n/LLugKMtpfxqRsQ==", "signatures": [{"sig": "MEUCIQDqkyNeJqAaMpiFJ1XbAyvh+ozKJjTZi+3ROq3zis7oMgIgJFffXZFus/OVbuuSCSVVd/9HShJMN/IWqiqgi3hfLP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8bCRA9TVsSAnZWagAA9h0P/3utJLV8fZaIDN1RQ/UN\njU5U8tqVukdXwL9c4i2MS7Tx4886APqV127NiZe2jFKaHFh90+1npKYdHWWn\n8EZNjyKqsvDt1ANYEL9QS9TI4EeTghKw8jZJ4b+i5SfPSzsXEEejxCBJI3cM\nXQ+V6OgD4lwavmXm8FGk05M80LNMoyKfmYsoexh05rQuIDogJznsQeXQsrfr\n4wMLfciFxIA1SOe+cPjgfkmXwCWomSWM/ZTXHbdjzUgQjcm8cVdxdIZn2R9G\nbs7j4Gxf76LS1Hd+ulaZEbx9vDJePCzHcD96Z3OL6yG08V5JvF7RLBRTYXkq\nP2wzxVpZOZEuArJ4aa+gY+vNbWAU54g5+kmt+BfVVsKZRso8/rxaxxwGXEfp\nyKc+PWlg2+SLngtH78fb7GEcNaQnE/CJkWUFyoLFJzy3NsPt0IyzBbnk8fDL\nWMb9XNnU8ahHflw35+DEWTBKGrQU5iKtqsiBJNtenOkZC+m9O56gsqpTmRJg\nn0Ym09UXp6VrENxQjcuCayDRs65IqeZt8T4ZNSYz+NzPtgeLx6zoG35/XBKf\ne+78WqQR6LW34iJwe3qcymzTqpwX/mFIp6cNwD/G+4lSH0uWxI4rCyJ8LG9V\nI1IcBKKPE9j1zuloeF57E7KdRJXGITc51/M6mt714zbP2gyHKodzLfdyUEUU\nEWMw\r\n=93V8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-rc.1_1533845274859_0.6539571395229333", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2ef81a326faf68fb7eca37a3ebf45c5426f84bae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-6alRFzRGQEYlsYsm/WCOZ3+XND9f+/Pj0wkM4+uWPy7lmYoUHLVESGYJc8wFXjk/diNYzjowrx9JZn6sn6lXNw==", "signatures": [{"sig": "MEUCIQC8Ch1z61jxWY5MmHjGEMAd6ynvXsVFXtgYnNjFOJKuWgIgVRDXjYzkTJbzUBzJYbC1J8yELLPmOnDGM0ZB06o/h7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbGCRA9TVsSAnZWagAAe50P/3q/JRYIimXA/2NPC0gZ\nfjqhXFX84OKSO0YBidZbmiHX4TuQHMSq5nKMbl+U9zjhUQ9VjBwwpqkPBk/F\nsJy88utTBbXFb+A/LrFhUptKAAnNQSHj+tbm0BhJVBmTOMzgM2osdLtQaqQr\nogyIbSKp11tyofW0VRPOf5+1AmTeKCR8Mf34Kl1cHLbqC9g46bnPL3tUjLqV\npK9iM44VaA2LvZ8NIJ3y3uraEK6Oi+t4nEctIT0A1IoYyCOEd0JNaB8FwvBI\n6+S2X8UFcNK+Slbkk2mzvw3d9BRM0KykCHELVUoE4UT8/z3Y474hHx1OLfe2\nyebP190s3m2rLy8nccmugCS5VprctQp595i+bwegart8j0dyvtrZNTtBz6DD\nTwZzuq/rCWxyW5NDFgCnkwGwCVm15PxLRuW+C3kwYG90VRtDdohAqd20QmF5\nmt1DaPyYcp9sqnoX4B3XiDUrLGDzqVFIra9oksNjiNstkoKYYw5FiMA33Ggs\nZprA1Bk4ge8yA5r7S1jxcMJBlR+suC+g2BsAqIMs1is1WKmqnQUpxLWfgf+n\nK+cZZK5MAtVWvZ7HLH0VB4pBTiQ81wy2WJ/mpT40Ud4B9fUCarGIN/UWcZ4p\n+Rzf7imc7pdaWzl2xy503HqBE4soyJYi9UwlDi9QYJ5BK1uLIq2J4pcK0CR7\nBFNe\r\n=X6kW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-rc.2_1534879430011_0.14939680679890444", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e4444180fe6f4a2a5244e9398cf8cfd3b7b9b1ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-gYMyNOFt+J6ZPsUm/tY/suc2oySv0Klp5MUqUgvhOXvfMexzS5WFzL3gG2aI3buSoi2GswC7mULttx2GA/N8UQ==", "signatures": [{"sig": "MEUCIQD1sGsy/XiMsWB35Yz9MURYVBbFXHdFULkmV6O4ugxkfgIgB6XJokN6mRLw+wep73dxs2TAQiTJGcNFscospiKsOnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEl8CRA9TVsSAnZWagAA1eYP/3I1nJGL1LfgdfdKmVib\nTTP22Q7IoeXgJxWjtbQnJzysudxranxyiyQcfpG8aDWybDBrCNOh5SNNkwXq\n5g87QoHLwR3tABwWhSGJhE3y01Kof4yZ8V+emTO8m3yVF5RuHSm2y7mqBMxA\n+NhoboYPwBnPbfPBX3KGuDfOokcgzWpjGRZsaK1+M0SyMNXLbcszlBLZLgMV\nCTSCdAYbX5HCogzW0Vwm15Yx/e9wq7eLZHi+kmMEqDTMTOUZFhkaJ7XTZd7z\nbNiFOtjcuNVuxKP8UXZ6xq2OBmP5MxX/BEVbBqCIb1aLjRVX7MsqktSC0fyo\nZ1lQI3kVInyJkQra2C8ipl/j6ORuIGL8TrfSQymC6NQMMp0okqcCo5AYVUcV\nThLb3FGpMLh1Juu9cxu1f1zZ+hXpdxOIcz/jqrhV0G/6WcH2XwFPesAmJ9EG\nRt4yeZllJqIspkr7W40MtG/qvc+JxYD73GajY2nT3yHipkzBOW08A7EgmQKr\n6SA8aW0nPm07Rk2hGYVFz3xxqyB84E/URBGFFsJsBibEh2vRYTC9RNDOjIRw\nJ8VIMl9aSkaFulZ/XbXG6Vpp+d4Z5Ju2ren6ocPFH3uzCCDT71U8XHYnNUY7\ntT0Ly9h0I6h3zocA+EE90azxWeoYp8SEudVL7WqKKagIk1m4yoIC6TKffZ0P\nyESi\r\n=kP6q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-rc.3_1535134075723_0.412150817873832", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1c027b8047f39cd8fd829cbaff8066aec35d0e30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-8KNUC8vcavZc+1j9L3JQcdsbMuD1/eTX5UF+7C7nCCntTx4v+epwfsk4dHGkZ0+qK35JotYX+oCXaRVeWqN84A==", "signatures": [{"sig": "MEUCIQDdLgkb6pQ/UYdSdMhDxy+0vGO5WnKANl6+iqEi04aRiQIgOxcOgRomD8XiV0zdSvayAHuYbSJAvxBNUAOdLZ164So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpZCRA9TVsSAnZWagAArpoP/R+LSJf6/jsb7ltBGPd/\nYN+xGKjmgJP/tDK82Hi5Cj9yLs/7jRH3EUMEA0XaLkM8Gdw/SaxCpAiGNzbv\nwiQIcYDIeF7/U6gDiK4Qjbqz1QaF97W9UnNzsyZ5upDfoZH01CTNDiUrPDz5\n3WqiD18k3E3Yd62APi44/piEYPr+LCAGSOzjBBqnTzoFSROmPV0JTngguHhz\notfu4L8SE4Y3boeqTKhDWQNBNUjCybVvOcrriqP/2no3lv4BCEdm/SqCrheX\nXdFVT48uBdBOe+cx5HdRb+JS8S0KYmpawl9c9kZRA2BqTuC6njYBewWColir\nw8pbSf7v1crSWgXYhlFKgRXy+h8GSAD8/flBv+xohkjBRL2XX07vUOWFt0UA\nxukrCgfvXGqCrKfL2EZ6/1tWWaLYHi4Hekbz9Rux0ZZyE5/Vnt1MBnWVe37O\nvsgoEKPz4AeSKUNt/vRRC3pDZ5COELK7fS2x9pSj5luaGVttvXDS0ruOHZGV\nW0/I5AdzxvA5VWdkhX5QzV53dskT8fBykEYEkl/fxAk0/MSwjAVYd1vpwnFW\nvuLHAm3mzWUPnfIUbnq8SkU8ZE5iwto5EUmNp5RQON2BLqPckLtpoNIVNl+G\nqfcgBXzhFabVCocKjJKHJG9jKeGU5HO+vY5Ikmc7tJGLqt3M0Cqa5236MomC\nuyc1\r\n=bs5X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0-rc.4_1535388248969_0.2760870451707249", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-for-of", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f2ba4eadb83bd17dc3c7e9b30f4707365e1c3e39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-TlxKecN20X2tt2UEr2LNE6aqA0oPeMT1Y3cgz8k4Dn1j5ObT8M3nl9aA37LLklx0PBZKETC9ZAf9n/6SujTuXA==", "signatures": [{"sig": "MEQCIH11/T3lUJmN42HhHHN68juOKHhZiDiaxVqTs/dbg2sDAiBRRrs+6lkFhMpyHH5VtxCDMtwdboXHf/05dTgjZMMcfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBpCRA9TVsSAnZWagAAX/8QAIK11n4qI2q+OoRdSl90\nnIPq4lv7QVntl4U1tMViXSebgKbxafzifTCZhZHZfXrkFpatRlr3rCdCyt15\ncJVYDiOJqWJUiDabOkasOCKjpV9qVgF9zlkFxhZXtoP6vnQI+iAsWRrL78uM\nDuvpJf9FjZgJtnZfm+/aPP558w0guQ+fyQzSMSmEfP+mIiIUe02olGVx12RI\nrRNdVy6CRAHXzadbvAp5e6VHMowhoz+mTxawjRIeO8fHMnt876xfsOGXE85/\nSsx0Nn6iex/MQuapAGKJbl1lD6TeKJ/XbhsMM3GeDfrVN5K7QXKq05Zl7HCG\n7T4bIwWaKv1ocKP29O+XQKteldEWyh7M0rTA2zCPMGSIPJd8Jr5GTRzQDF3D\n93s8Q7Bow40FAVBzgNHrcQtchadYbmLTliJR2Hnz4fxx8oGqE9AMGz/WW3Pa\nMAj+DOjaur+tJgRWC7AgoxZJoeZKvSelk2hF5yhNGSavEmdBnW8RX+9u2By7\nfX1scpgakNsle6HoasLBPv2aarnG3qc0ojcvptaKL2/UVBx5sK5nieIOWcBu\nIjk11XCCWRRfpieVDRIjT1uzfI+CohFhW5wcZW7tuVSqlUn1GiVWl2tXnqqw\nKgIEnGaRIpcbFMVM+zBkaebSdU3dbelp6MoECbeOkjEgw2hK9f3dcpa4DVv0\nkvHw\r\n=HsTL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.0.0_1535406185381_0.1043565640475772", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-for-of", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ab7468befa80f764bb03d3cb5eef8cc998e1cad9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-Kz7Mt0SsV2tQk6jG5bBv5phVbkd0gd27SgYD4hH1aLMJRchM0dzHaXvrWhVZ+WxAlDoAKZ7Uy3jVTW2mKXQ1WQ==", "signatures": [{"sig": "MEYCIQCNex6RQAsPfViJiYIZygyQp/hdMZq0YVPo9EWRYbRAmwIhAJ60EipPksMZiHQg4RXxIKd/qCYdqnpub9alXDZpYngN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2BCRA9TVsSAnZWagAAXEMP+wZ6uYxyaMg5NLDdiCfZ\nIG80LF/MExlEiLycM9ypnoCPIAwp1hmXlyOi3+e788JArCFteCynUroaEK8y\nm146Ro96xTi6agmfHBB4+r/aHoMwtVhy6j1lvx+YL+rwv8lrIFmw9lxXGeVb\nukAb48/hCg2hnCKq+hacvvXKxPZScdIR/frc727GpZ/4ECLw5yuZT2r6xHJ/\njnbD5QmsVrrM9sBOxCnvPTliEs1dlRTBAZDA6LCG/0kyxiUIszTC7YiugCtg\nIhop37AeF2hVHxuiIH6WHvtulSPYX21GpQGGs33Cwyqzi4QY93LQ8L9ho3IC\nByXPI/qdWbQWtHme4e2Kc3mXptoTnMUAeoNEb/DGuNWcX8XRcWkIMpZtclwn\nA0ai4QDyB3qJhYhlSreDEAR1Nc3PxjGkcnBUb9Jj6GJp7Dy6jUP2ngIMcsim\ne5rZE4toWJ/LvIKSDAwwnFS45NmRlY/9N99a7iAYNYxvObStrXsp4o6VLEUp\nBljNvh3NrItG7CeOTURyDwe8pXdwx0o4e0y7fWProYl9cRpW5dbF97RMcMdW\nRcs39d+VZcJ81F4JcHJcSpwmselNi3oS7Rp8SCeXYpSs+NoogcL8gtWU24xI\nRL4mFrfz4VQdQC8cLUXHSeLQmKdEpBS6gjctC0Gn5Gc9lirJfa2LRENo5/ra\noy2P\r\n=+K+a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.2.0_1543863681235_0.8350892903971929", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-for-of", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "56c8c36677f5d4a16b80b12f7b768de064aaeb5f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-vWdfCEYLlYSxbsKj5lGtzA49K3KANtb8qCPQ1em07txJzsBwY+cKJzBHizj5fl3CCx7vt+WPdgDLTHmydkbQSQ==", "signatures": [{"sig": "MEYCIQCQGfNr4agVCpDL4cfzN51nDz5XeTIHcK2YK9jE7xmVsAIhAOr1OXZSjHioJj2TejBx7pLvcOmokyvoPJAOijQbtVZl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVSrCRA9TVsSAnZWagAASWwP/1QljcGrPQ95V7Hk6TGw\nI1swST2kN9yn+yJVu/quHNR2nEDkfGDeQ3xR5ZLtNK7UX6rygfk7DU52oTNe\nDUUWRGefyQDLaNDIX/3tZPyJNPGXbnAKYCa6Cr1xV9JYi/SBp6iTNpZcSlpJ\nCY0vvNxVEUM9cHsdYf/gtM1PxNiJxoTPqTypWMZf0EM9rU1CscwXy6/bIG78\nZMIceu+GpSHR7c8HcNv1Aanuc8b/U4vHpW9C3NIrWp46niDfxTpojGzvxKyt\nAGZqiL/V6ns7Is0qMgaeshWEqLijmLWOlS2d+z7MugxzFRe4LKWpUz7gwogf\nwWDC2k47AN1zh4lVedheWLwJCWo2z69VuFW+XC/mSpQ3/Mqp+52qmvXkpAhL\neN1JHlx6hcjTKQq/8kM3399qQ7n7fHcZdYfDyZStu2J1Hi5UPTP7rVTOFRqf\nI7Bod9f2pyrY/FMJxFptMGwOCKKwLQC7wZQpfAA6t0m33J4DjKgzf5zRc9a8\n4uPlajXQUleJX956nR7CRyBdTEUA1bBKZkTFEemw2SmyuvhO2olH6CUbhvIa\nMzOvzooZ/BHE5caiKeDhUH8UNdJRr+R17D8jGAs/uGxF6RMFBNg5ldkg2exb\n4XXBLPmFzpcYTCspDnA/rR/i7R2UDQh0iM65rFp3W9WlXBtYcpWyg9H7CErL\nEd9g\r\n=5/4D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.4.0_1553028267237_0.011536193703352016", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-for-of", "version": "7.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c36ff40d893f2b8352202a2558824f70cd75e9fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.4.3.tgz", "fileCount": 4, "integrity": "sha512-UselcZPwVWNSURnqcfpnxtMehrb8wjXYOimlYQPBnup/Zld426YzIhNEvuRsEWVHfESIECGrxoI6L5QqzuLH5Q==", "signatures": [{"sig": "MEUCIE4y3/wAEJD4aOXnlLFEHG/oUNUX8e/VMx73In7+knpOAiEA08vbmLILAvdhREYZjWLsbjTb/4PbU2QXGtYYJ9B2/Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75GCRA9TVsSAnZWagAArDcQAIRm0DqaHYLREhNj8+R2\n7zy2THNnbsE5wa270qFVITLbvAPIoYHKoiRWZXQ7CXjfOY6wIWpHC1knKOUB\nE61pBnaApNLdETBDaewgY0dl/57zb1d5/t3lcajRxffWzfyM2O7JAI4AI+PZ\nnT7vBVFPZ+ZVe9qBwiom+VcPJsvORqFZ0eiJbtaKdMMk6QGctCD5TIXN6sbF\nVBg1fQRBHE5wWhxUdxtbDSR6FQzhfOjqjIY9/UOoI6NPqsGU9Hdkyc054vSK\nFxsxkdgh9vJZ/y4jMt13SB1KuSVvn3LYHgr02sXV5qkoIfEs6zm19C25knb2\nmUBtQUBmb443SU1P7U6BQNkwKypDfL/ycNIycJpP5TJZNMdJLOWwL5M4fGlZ\nS1ctC9xvrqi+OuuSEEz+/L+yo4uEemh5EvWmKF1h8ddkdgHmn8QSdOja/GUg\nNmoo9v8Tfh3Ib3mbxp/q8GLy+8oKFy5HCWn+rV0JH0fwopcJn4rCYmdAWXA/\nRfofujSWiJW7S5UmMRj7+e1ZaIwZGa6HG8oSnItK856ruYuy8WoVf6ls0hH+\nbWi5c1P+7p6M5YLFa7Pd7KcO+zHcpLZe98yqukiZDJdsGErzudp5dv6JTpFc\nJNjy8XQ9N6yUxB4DTxQ5EH294aAtfM54oBDCxCrQ0IXIrQ71SpqtB/dUhtBn\nkg3b\r\n=MBgY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.4.3_1554234950112_0.10458295194777478", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-for-of", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0267fc735e24c808ba173866c6c4d1440fc3c556", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-9T/5Dlr14Z9TIEXLXkt8T1DU7F24cbhwhMNUziN3hB1AXoZcdzPcTiKGRn/6iOymDqtTKWnr/BtRKN9JwbKtdQ==", "signatures": [{"sig": "MEYCIQCl3Icv4T6hgq4yYgYFTemPamOvK92yGIGRuaRUYa2nkAIhAOCSQwVyBfEUU6e/jhXvyCmO0UR+CFuoc+O/L/YKU5rc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3I/CRA9TVsSAnZWagAAM+oP/3gUOQ3zi1Tj7bpoA2LB\neuyLNLJSdhkuJ5vXDVEpJChz57V4VjXRIaVin25NzUYzmii41SbYO93A7ciF\nZgVFKdmFgJ9J2OoUGIFK1Oi/qXoVgEM3sgZ3HyLUShsc+8szDC4LOf/3f0p9\ndQ9g1XugPer+UWR0W7UPoBkgejZNQzUAbES1m8qs2Ofao+gmpLvGkHHJphmE\nwIILGly6w7mu/87TChjmH76BaCDaVzftYGbkJ1pIt6QFoPzC6pROHqstJP4k\nr016aKS9gt4iY6Unr3i+85s2dvAuA8S2RO5iqoQV3JLAATHLgw2rD+Yay7IX\nFfDFakbVc2WLxP29/n4lsvC9SRYrxoBT/8B3wmsjZjIcopoiVuEnJr2yV6T0\nWF3NP0gMKz6hjOuh3A+rHXlXdjx94TABlNIgz8befnlin/PtJBQiU8+lJP+W\nvXf4/GCP9iUgnLJy7FXBI4u8OFQEoULwbl0VWdcPJaEhl/MEPNaIzpNwe+9J\n4Cnb8mu/OVmJ3MSZx6gcKzDZHBVgM5RSa4xMFrr4GcC/4KrEAKElkEoQzQWH\nY6A4v8EDUU9CD/zx871h+KrO/DkLm+Q1A+KhoZuk+t7hBd4Qe425+hsb7Uan\nda1jLR8wliNnFqIANVOISSBgsqBISodRscL/1c65AAhD+JEUh464q6CyGdHk\nYiWv\r\n=0qkl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.4.4_1556312638536_0.7484554531929701", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-for-of", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "248800e3a5e507b1f103d8b4ca998e77c63932bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-zZ1fD1B8keYtEcKF+M1TROfeHTKnijcVQm0yO/Yu1f7qoDoxEIc/+GX6Go430Bg84eM/xwPFp0+h4EbZg7epAA==", "signatures": [{"sig": "MEUCIQCdtAMlYLuae3ajxnom9r4l1C6d9SUVpF7MmXesGjchogIgf/hfJfN+LWBnvvY8EWPwi9AIaI4229uBtHcMSgb/nDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/5CRA9TVsSAnZWagAA9AsP/2S8mT2cBO0iInMb9tbA\nZL+8wuZSaotWSTF+vxmA0X5CkGBdHsr3nL9GDg9IiibeL7TPsw3mrf8QeYsJ\n2h4OWLDX2WcLv5us/NNXAL1sKonu+Oz201E9zUmUiBiALLgRH0PvS0+3HgVI\nV6lAkzIhbDSDqGK/cg1Oa4rMrzwVjjRRTeiEEnO4KKgSJLUilqVKyeD3I9Lm\n0RDR+GO+JV3jY4iIAUzFuXrmG0ymv1kOGEiIkMe8ESTlqgIKG4EBxq+oWTbS\nAEq150FSsR8pXZwMsn/9seNn4XPghbXKnrgdKwYhkO7VI1wvIsk2OCcjVv76\n/mS/sWASa+pz1TLLvYJrATnQ6J+BkibCheBZXYH7ZemflHWe+dunpWPSusMx\nUV/UxnJAdt20v/QwUWQfhew2hjmTCtja5iQiGs/nkM3FB8+eFqGA4NTb90kK\nPTTLCuvziM4ZDOPqi5fsQkAdWBEYeGWda0FTmShB6vIQy1v5ZGZEaHsqQLj1\nxgn8go6HHeYJ4TsJ0aD9RIye42p6D+0Tni7oDc1E61aPqdCNhLbdLolooJNc\n2c5rVI6LYhKwjzEuDeNJ+K9twwT8R8pC4DTV0AaY7VUxO45nZHOFG6jiiX1L\n+/iQHqVw9/psNse5QmIqNwvCDLwiYHDV8nJ+nNVCqcYW0UUHgMmR43S68W4J\nLkq1\r\n=bMnH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.7.4_1574465528840_0.5392563427564439", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-for-of", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4a2960b76d42f4cc8a821edb66f4a7eadf6042ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-9j9g0qViCAo8E5qCBSaQdghymn7A9bRXSfS9jU7oLpYccYFZg9A+1KO8X+HV7fhJYH6mZ+e7MRg4p3sLo+RG6Q==", "signatures": [{"sig": "MEUCIQDx0fErO3jMttaJU36AL3xw1jhUx+D+XopiFcHzuBYX6gIgPhYp7GlwZ2ql1Q8l4WOOkDV+lm3WdLYM7tbc19qnj/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVfCRA9TVsSAnZWagAAxj8P/0iu74nAa8hHigjgLEaz\npqWAfoswCtxJJcO1q/IA2x5SreHylYIGB9sPaatfbcFAF9S7SbDJZWFV9FiN\nGkrGPkGllxF1rmzvClTDzdIz2ML70LJLmCPUcsbCCJSA/B4PqB/Znr43P8Vo\nWUulDxHAIOO8iZ9ifq8hmAI29BIc8zT7PwzNz8CZ6ogUrQbv83oUy3dbDgdf\nYuq6ojBiigf76xE/Ku8NbxSt8mVE5NjibRplvgoCQP1y33Am+8PvOST24J6O\n2pnDKXy8fxYhv7MFMNy10lhkd3kZVgNfG7FU0gwpejsVmAsuEdieWV50wGrN\n7yWsgW2wA9PrU49pvpXR/NG8DoKufxuRN3U3cahAQCDcgpQDwQ9aAYu8vpkC\nmhc6z252/+szbpgDostS6RQvUec43lKMXd0T1L+3/DqLoZTMZz3nTecjxxRA\naa+DnEx4z2m5DPRuuZTF1iGvBf4Tg96ETTNnxNa9KiqjqrFaAAM6VfHvT1nS\n1dZFvfkDIEps2MsYdqfJlbxpJbaDJz6jTmTqzw3WZkwbvS78Jtnahg30msNK\nagkJFmXxJXK3UggbOGBiMaVbZdfUuz2EjI1FGXDllyImljrZiQS/EDZ6ORVf\nitnUMZZUt7jNh+b61W9wD3uuWim8qFG5u3/nWdMdn+LAFP7D8fsxuLBjZ0GK\nSJuK\r\n=BNKO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.8.0_1578788190752_0.3445309851604921", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-for-of", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "15f17bce2fc95c7d59a24b299e83e81cedc22e18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-ZjXznLNTxhpf4Q5q3x1NsngzGA38t9naWH8Gt+0qYZEJAcvPI9waSStSh56u19Ofjr7QmD0wUsQ8hw8s/p1VnA==", "signatures": [{"sig": "MEQCIAaw4JvdZKqdky2ax3HZEeZrTTU1RgDzDONDW1AcgrNxAiAEx2N3gBcwxn3Sb+E5gEIFkpWluy5MtqzYDIvwvbNsYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQPCRA9TVsSAnZWagAALe4QAIz7nWLsB+zXEWNEOsCt\nzJnyISl3uPAsDRGIChT72T+NjSHEkxTzTbuhZyfUUj5lkzCCFSSudF08yVRZ\nsXXPWMBU5ZSClwUr+QHqqutK24hEx+DyTTfov2jX/tZdaWOapRvCtC4WMzQq\nQ+Ri5zWiC6WOfcu/W36V4iy1S6b+roEZFOziXtx12Wsy6PGNCU6iuyGjyHc9\n5CQUPsRb0TbfsmTm6pboqYX/oSaQz8qnAfUt6s7EFFSHbAmJMCFAKiIXH3An\ns5FCxt3v8kFUzq6QhQS8a745AWNOcjDMSSzUzmfCQbRp5Ba8iYmyfNfNy/V/\n/MdfZe93HIa7dt46+vMbLmyioVYCqRMGudEwwjBZU+orxnybmHjBo3kwHAdf\noZt7T1vrXG/ChDadsDz3SJqsHJyJMkDURvjRovcoHHZjWxK+FPuF/ANSKo8A\nE5Oa+akIRDHHvaHQyWaZ0V28Wk21wHsUH/MsRTGCgEfrftmrOfG0ePGM2+QU\nUlvny0asMJke6Bi4ihJ+zXuDFByELvoRHrY7y95+bbtMn56ot4v8F6YcyCW4\nSKvqF6wEkRwFTOGro06sL016R+tCOQsCujmx4FSbelUbKo7sFgpbQ47sqP9l\ngqIF+U60MYJX22GGQWpTPKp/eM8Qs2WdPQU4VEjNtLmzuLS/66xBKEWDKQuB\n9CY0\r\n=qA4M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.8.3_1578951687917_0.07701180094929261", "host": "s3://npm-registry-packages"}}, "7.8.4": {"name": "@babel/plugin-transform-for-of", "version": "7.8.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.8.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6fe8eae5d6875086ee185dd0b098a8513783b47d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.8.4.tgz", "fileCount": 4, "integrity": "sha512-iAXNlOWvcYUYoV8YIxwS7TxGRJcxyl8eQCfT+A5j8sKUzRFvJdcyjp97jL2IghWSRDaL2PU2O2tX8Cu9dTBq5A==", "signatures": [{"sig": "MEUCIQDCR3Qpo54fs4Vdl0p1mYsi6q8qPrrYS1//KZl81gtZwwIgIqdt9KUXM7aPQlrq4SUMX08x0L7UhVZRLhhB7+MRl6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs3yCRA9TVsSAnZWagAARv8P/jglLdw2cqlfsBGUZk0j\nywg42oFaLjiTYUBr40OEEeWreHrUw4HkL+Q7NA10YrSHJd9hV5wosTRVZR8V\nV4WwLxI0ZyMGS4Q7sXnNkVUVSO7xK4nkesdcXKpr7m6icQxWVDnTCqll2ZoU\nGvgH1OH6Z4/bOBeE5Zz/2RfnsypePhFsDXtGGDqKrT62m7LptBb/hfgoL3cA\ntPlFAehgm52l4BmL02dCY1AIld6QBygEnZDR/NE7jORJseVVcB83lho4ELcX\nQrUJsTmT7I125mBKU1lyja3TK5h1ONKC9HRmgEHNeYBrAw71LtUzNEHQ2qIq\nC0DwS/OuqN4vZ1t/UHSD/KwE8Y6FRFoUkbE5KXpHXsH+GUdNUIWkzAAHvbMX\n7c0S/T+ZEsW3ljXaA4U6++nOa+L/Pfaf8DFhqIiX9OLNibs3Ac+dU8+aJ9O4\nYXOp4mxdRC0tkTtJBZxwctsWsTYiyRGr8h8SYvHhcW7tnTby+Z9GzMj4Uo9Y\nSHvjcZhyirGz/TDvOYZMRjWF50Hg7PE7U6lLsv50M3eojRBoqswEgxOwEAgi\nxGYsQGRQj2ByPz4bHE4eW4JDe/W+EdoFSFgXG92ZXT/l89hreU/D2ifElLZj\n57uxY/HqSVM4SKa/vykiXCpy1OCJRDVvPFURa6qEqFujwi6EA2RZCmtiftbn\n3XJh\r\n=rTMi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.4", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.8.4_1580387826565_0.10239162889536879", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/plugin-transform-for-of", "version": "7.8.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a051bd1b402c61af97a27ff51b468321c7c2a085", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.8.6.tgz", "fileCount": 4, "integrity": "sha512-M0pw4/1/KI5WAxPsdcUL/w2LJ7o89YHN3yLkzNjg7Yl15GlVGgzHyCU+FMeAxevHGsLVmUqbirlUIKTafPmzdw==", "signatures": [{"sig": "MEUCIEEqG0xY6BNsI5yM3dOKyaSxDneJKpXUcEC73tGgHMYVAiEAq1avS134nOhvLXsTvw3xni6evQbEfeQmj81ze+hzprM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RBCRA9TVsSAnZWagAAzxQP/i9mQ6TUwixTDrIWBXh0\naI2YkWFNX7akOqjEkOEsQnBGDVfiSWmQYL14tPlAi33gkNHnLO6jcYduzVoV\nHxFZ9/TxLmvPI5MogDtZXevoXtmexFl97byhLZocS0PmtiKwcoGGV1xfQ/Db\nxaTInzQoTF44U7IIhkxprove8gBK+zXphwzlsi7v2OzJK8ON3LxgjDBZBKTq\n3WXj2Ulh1/4sgbHt26ZNfdMDGJJowWPuQs2DUiX/eyWpK9b3GpTpa5x3vEPU\nA/vBLGd6MdPnbR3BfrB7BXcNrreFHoOA+2m1urvpcXc4TMShR2jv5kj/pQaU\nuHO2sCxBgnF9aeu2hsPb1oLAF1M1yu+om76mI1nFxMNKiFzDTVOeUV2Cp7ai\n6DkPk8Yleva5szeT9EWhEOInMLhMSqCTbswJprsIUKGwx6o2g3GiGPwLp4Mq\nU9+Yikx7QH6FnU5Uv5QRa9t9hCarg2MSxRNjId0A8/KOavQL1ayClubxsbM0\nVYkvnLeLgkx5PNb0pPmFz/2FO/A9slRZRzfj5N2tVgGhIXtYNLWXGJfiAIWT\ncG+WTf/tNs5SBsLfgvJAZu8zt7o+ZAgAMsbvN95CAITikJYASazXlsiNq0Qf\nWEAYA13lRpFdKQDQAK/X4K9JOhckbvR15mmduLiP3cRzCXGQNkl7dDYTQXkl\nJ/KJ\r\n=J3tM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.8.6_1582806081476_0.3466205436190555", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-for-of", "version": "7.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0f260e27d3e29cd1bb3128da5e76c761aa6c108e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.9.0.tgz", "fileCount": 5, "integrity": "sha512-lTAnWOpMwOXpyDx06N+ywmF3jNbafZEqZ96CGYabxHrxNX8l5ny7dt4bK/rGwAh9utyP2b2Hv7PlZh1AAS54FQ==", "signatures": [{"sig": "MEUCIGj9zXvsa8y+pXzzWhJfoixTtRQ8zuI47OUjaXL0EI28AiEAtypdnZX/QfY4JlohiIMmJUiQUMcV7BiGK+RYCfFdcmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOsCRA9TVsSAnZWagAAn50P/A9SaMZDpjrn43C3pC5M\n42prl/yZbDSdUPCw7KVsb0yrK68Bsna3aQjU32kkwm/52XX0vHXf5mrF5X7Y\npCngFos8QPXIQQ3Z31R55JC+Ay3kAmCl6TZDCaC+xdx2fVUXiFlKjqT5TyvP\nOgjd10De5xlzQRRb/94/nehvdu+PJzoGinGyOVq17o9VM18fe/fU5SYUp0QU\ny2m/jd9xC5Fk57TNmCzipqvdRG11nvEbBxEHCr9TUKLTTyB8cOFylw02DoKC\nE/iwbPKdTTTJzLMXSXOpOuzbsOKSYOR0yXnPumawQbiw5yZPidypg1+uWEIJ\nHneAGF7EhV4w8ielWQiuIYk13cypTp02ijykoH0OKKTI2PQGtyJ/ljJsnXoR\nVIMngaByIJN30SqCdcXYQe5aOGhuUEdmVMFaUR125o3wRSsLOb4XjNyg6FRQ\nfrPKLUA2+N+rslM51KNomS9b68z8H43X2VWXp34JdTIGG+zsSufvaB2nxh1k\nzE6CnP5jcAlno28zJh/eSMKU4Up7favnnAqA63q+PlaM6o1+x4Q80IqZ5iIm\nDTscyY5Fc206JDi8ZKnVfaiz0OIlQY3DTP8q7ds/EbRU2DFKqZfD34yygRjP\nbLLMdLWZ92L1rD1kVn7npZ6Y4burk+wZsoEg+h/lv2jjxTJc5wNAvFvUFoJx\nfEkW\r\n=jNwS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.9.0_1584718756400_0.5522189587191002", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/plugin-transform-for-of", "version": "7.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ff2bf95dc1deb9b309c7fd78d9620ac9266a3efe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.10.0.tgz", "fileCount": 5, "integrity": "sha512-0ldl5xEe9kbuhB1cDqs17JiBPEm1+6/LH7loo29+MAJOyB/xbpLI/u6mRzDPjr0nYL7z0S14FPT4hs2gH8Im9Q==", "signatures": [{"sig": "MEUCIEhjcuAJ9ToVet+byIAqNQMTaRKCzoouQSKN2ZDZJK1WAiEAmieyTN7LHi3QUDJPysA1JAr/XEQoOgklcV5yAbRYbho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY1zCRA9TVsSAnZWagAAXdQP/1dG//ZgDa7pQfMpM2wR\nJN6kz4sPn5CCB61VH+6fBiFP6p77YBy6/OJxu9g28i2UTeF7q29L+IA9Ymcm\nu7wQC5B7F3zEQcSLw+RxJ5nHqFl2FQKhI+TH/irdSuGD75n3azWgcM2JtpUm\nZ3cQ2O2VvctswRNg4rp0qmFhSh+hF4xoIUvWhVQ7HmXRyU8FePvG0t2tOXyU\nosZozGu7sAP13XUlUJluE76Z2qiucqdWO33ynl15JqSZXoHl4yAj5VqtsC3I\nRC9hy64rEQ<PERSON>38tdFWC5BtwpXuJDCAsFFMUWbbtu2pApJUN1UCFbY/dqTo/QV\nWU50Hl/xxkyRFtwDgMOa+5Y7uRRrd6v56fmi7uM0UCzQngNUSF5wlfkJKnIS\naTUc6QNZrcUmU63cANhtgbeb+dO2wptUEp5/Vwt/zbpJOOSYJpiZtTL5cUQL\nI4otgKjBYPnZxu41VkOtnanTiPI2yXWoHjt6EAUfmAECjraYXC5bgfSIMa6v\nvEP4XZd3kBfmZR44CwbIRi/GpkoK0o1eHmku5v1zW45GOTiAJng2RSViPJS7\n71lPx8a5v/LIAk+zB6nL1mB+n66pg5wF5hDP1CYPSsWVw7QUku8eZHkEbUp1\nkXa98us1hDV+mKncI/vEA49Wc0FZx5FrlioPst+h3qq8ZxGuQhbSEA4am4vX\nTkh+\r\n=RMRT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-for-of", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.10.0_1590529395245_0.12224455338495566", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-for-of", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ff01119784eb0ee32258e8646157ba2501fcfda5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.10.1.tgz", "fileCount": 5, "integrity": "sha512-US8KCuxfQcn0LwSCMWMma8M2R5mAjJGsmoCBVwlMygvmDUMkTCykc84IqN1M7t+agSfOmLYTInLCHJM+RUoz+w==", "signatures": [{"sig": "MEQCIG0wqJS8sVRH0w6wBJZ7ci/jQJLqCfEk6/WDNagslz6hAiA4jjazJjfEVb0gmgaZAe4ptwl+bVCi2QIohAZnHpUGBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSaCRA9TVsSAnZWagAA0QQP/i/+3fSAMILV9pq2YTDY\n/F1+NvSQ6rXQrjWPDLtxidEfXj6JRG+gkw7lN6UtSyzMKEuUofOm/+Jt7Fk6\na9B97pcORMaYNPlD5uPREivjG4JSOeysjROgxIOHXqgLR7pjtZnhjoaEw0q+\nI16xhSrXuN1L16VIGJ24bPoCh0K0JEi/0+Q9wr9uWl2x9Zo70y4LckEsGMrx\nLUCuOGTtW1dB+0FJZu6NJEx7bLZkYhnRfQwHwglkbBpFYJNHahZvYnCSCS3t\nfnnbc3W+kd5kvAuXBkfZNC3u0j5n2SZ8qblGef7pOfsRPB37JBc2JQHW64Qw\n7ozrqINUIyCcAf4OskULOZ0oZ2kbOJliIo57djHlzS7N5v88SmTq1ZFY+xgg\n05iG84PJHVEsHX4b3khsiFeWOv1jx6mADAZfZoP16Cdi+hulxcWZvz4N1A/5\nU3AzXQsh1HFs1E8Ps1pIJMgO77QRl8za9RLsXBJtk6WQK3ccA1FsatSr+8kU\nT09yu3Jz8c2pOmzXAw7J78iIpvRZvChQB87HwZWgVg0cZAtIrWl1NA16Ddu+\n0k5Sq9WeWphpX117nZn9IMAud8VPaLQrDQCKzH+TP6xcwcyE0QDlDCHjn2WP\nH3q29y77tT8gulrJJ1mdapLyHZp5BkkNLjP8f+hzjOtwR3r0x9wJnkXv9z5Q\nA1r9\r\n=BsF+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.10.1_1590617242394_0.8774009508527967", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-for-of", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c08892e8819d3a5db29031b115af511dbbfebae9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.10.4.tgz", "fileCount": 5, "integrity": "sha512-ItdQfAzu9AlEqmusA/65TqJ79eRcgGmpPPFvBnGILXZH975G0LNjP1yjHvGgfuCxqrPPueXOPe+FsvxmxKiHHQ==", "signatures": [{"sig": "MEUCIF/X7OTX/01IyvKwOEjGQhHe7et3aWj7m9wuPEF5JyZdAiEA8qw7HKvSE3mj3pkVle5gw/M9OqLlW/ILZnfrw/O/6xs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zogCRA9TVsSAnZWagAA/cQP/2oBxXOvMZvGCJnfLzKK\n3O9M8IGio9mPeFgKma2wmu545WClGcLVsMKDHvWq+B9xQtTSOHTd0kgUFg2n\nReQVjK0hXyy66H1RzL5tWHnl2avMl5zrRUStCowy9o2L5menmRMYd2ZpdQ3r\nQ390OJY1CMxkAdU9Ru/ov36FA7d1g3Ef22Ve5b9wxXxelcDyyGEKT3J8TOJa\nXlp/LKo0OKe0ysP3xYsUhpG8iPnhAQ0yesAbe1SsKh1gNgFJWZBTt7M7pKYp\n6bwXGF4hMog2snWUj9ZGuXAnKZmeSwO8VEL6R21YI5UIEgIE1kaIIkm9t/Bo\n6ctBmMk/x8e/QIG3JhkYdTHcHKUU/dLim1Fafrea1nXkR6fmd4Xwme4OnEl+\nzI7x5KhCfwsKwuACAlzzDtmR3XUiJ3+zprXNHlK969aIU6Oy9EZ4v/oonRZC\nmg9nUF2k0tfdgyHa6LEl+NpVx47NpRt+ocfeeq2LSC5BjwMb7ST066TcCPYX\nEkOn8Tfvg1j+/+b+X9HSIngwWgPhMW7WUFmogmJShMpedbHW0N2i33InoHAD\nyJdWpA6BkCUsIU9V6vj3taSIbx+gP7QY8PhtdMCbBmvrcCLXyMWaIZAZyFsf\n66tV9mZgc2hsudpq2xh1wv80NCTmaBvzs4sp/RB9YpMx4DG5/1dvLfhDz9xu\nL5mn\r\n=Baoy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 for...of to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.10.4_1593522720052_0.9906906649106884", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-for-of", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "07640f28867ed16f9511c99c888291f560921cfa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.12.1.tgz", "fileCount": 5, "integrity": "sha512-Zaeq10naAsuHo7heQvyV0ptj4dlZJwZgNAtBYBnu5nNKJoW62m0zKcIEyVECrUKErkUkg6ajMy4ZfnVZciSBhg==", "signatures": [{"sig": "MEQCIDZhhx5tJXfO1rSvuoRrU4UhlhqewpYuFsRD3Jyk4kO8AiBAueW0GyGLpUsK4xabHTjoR++JRIdBXDlBPJ/WIukrGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/FCRA9TVsSAnZWagAASMkQAIZxpBoP0xEVM2+u2iks\nXLipqId29Ncfj7wL+ixxDNy5p4aniWwEPN9JkwLGAOdXLmtjVI2Dh/5+vNfS\n1z0TeRbsJBD+A8HKVzBctep7rM5LD1Y57eM+92YI1VIDCZ/zBe09hrb/sPRN\ndVsjjg5uhqC+YY5pYYpLqY/c3dcEBTn4H5E7sOuTlW62//tTGzxUXvk9j1Al\n2zv6qeoufSReQK6jxMVvDJnH0vNmhwH4/TSlh7Y5513fJnIYmNrkjnIE/vxC\nxovWzGoUXUraR8OjGuiY0pWOhfofqcbxKTU/9WUTaYD7wAY0XbsjavlufT+w\nwMOlzAA850YzsE2y+ZqyxDSC4g/Uq6iFlZxuP42k/LafVfhW8mH1S7RE+9Wf\npl058im+VwZPqvf52atwa+rUyKiUBUT9Wxb7vwSLv5or/AMpipXB+S2MRGqc\nBljXCbEUEBgrXWeUBNexv3SSnXKr4uvzj4BvpgzUf+QBGQZfzulexV95M2BE\nfyX3QCT/LIQ9me5Ic9M6Eqrwo1YbZXgYXscFsXciqGWBO3xmX1APM3Hqw8yk\nSElwdnsLOLQQ/dwHl3PIjQSHn3fxYPbvwnx+ujyItquXF913Hu5zjFgv9gWg\nxyY07DekxTzCRp4oxFrCCxSUw2/9CY6xIcugOCqywZQkW1pwNxlgFegzYJ2u\nwWPH\r\n=Fgxl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.12.1_1602801604588_0.5531953560860225", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-for-of", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "561ff6d74d9e1c8879cb12dbaf4a14cd29d15cf6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.12.13.tgz", "fileCount": 5, "integrity": "sha512-xCbdgSzXYmHGyVX3+BsQjcd4hv4vA/FDy7Kc8eOpzKmBBPEOTurt0w5fCRQaGl+GSBORKgJdstQ1rHl4jbNseQ==", "signatures": [{"sig": "MEUCICu8g8Vt+J8AGCw+sPf7Lmo3Wzs3yX8Y4Pdmtw9MxGdZAiEAt5M9k2PHzL3zLTfHlUeRbt6j9ETvpQSzJJM3g+yGe1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgbCRA9TVsSAnZWagAAdwUP/0Bv7MFOu8SjWGoDOqL5\n/+gtq27ZpzON8XGAnSTyv4zImHVwRsl/fxwsYf2yOQ7S2ANmTfLF1UiAVltX\nxN7YVXg2sMv35JIW5CKa7yAHq6zuUGx/RgUhPksHC7u8o/FLLvWQhZsS8kDA\nYbDoENMqKVZVoOvjqWOvFv5WTIl4HpEgJ0Y0qnYG7HUhjNxbn7L+tnr2lTIa\nVP21YfIexkeGmRQX+LbHCfM5L6oewKT4wwNgeU9ffESy0b846dW8b8nR1NJu\n1L5VmJnYHNqFCPYlWT6Ft7RDIPYzPxFFckcGBxO/5JBKaPXLFWOnXn/HP9yn\nUscZF0H4cS/Nb4nMI7uPJ8Fo5sSZFXnKSyy4QCz3GAgmjCY27UD9WksCpU6b\nFAwrEIIJPKdE1v/Id/l7hpkXHfq/CzDj2I2f14iEASxAC3Oxi5ponMPXIDsX\nEjWpGMRf/8c56XGdYRxYiT+/gAxHLqV0/8ckkceKMw6FCXvtF6dpSP9zcKXt\nH7/frbwlt7P6GPicLVeshEPChWXP3MOMSdnn7MYVNkZO1HvWGYKwcOVpNG9r\nxva+RZlcLDAT6n9X7l7To+JRojjC0veOAU45kE6M5rlm8c12H9T2t5Lm/kHt\njdCZ3Hf+dlKkG5EgnPdXHq/FVNXdytl3orugdl4WUHhS12OFMlaf9XxR4nG0\nN9Dp\r\n=n0gm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.12.13_1612314650722_0.34255556779265484", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-for-of", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "c799f881a8091ac26b54867a845c3e97d2696062", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.13.0.tgz", "fileCount": 5, "integrity": "sha512-IHKT00mwUVYE0zzbkDgNRP6SRzvfGCYsOxIRz8KsiaaHCcT9BWIkO+H9QRJseHBLOGBZkHUdHiqj6r0POsdytg==", "signatures": [{"sig": "MEYCIQDAepMCOR0yPWYRvTaodegq6qW8ufJwvBElccIMXqPsngIhAKmIq01DmtTys+m776breHrIOsAOrQeU8tD3QKE4Q3ml", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUWCRA9TVsSAnZWagAADsAP/jWfst6Ek+5Bf6sboXmY\nZU01PQeGFqeUj7XLqBNVasZ7a3D9OsAovWY6IGMxK1R2mia84dAL46U/P10W\nJA75hFdnspw3qdKA3U+xFxoq96/TaFGF8P3dgDFhz4Q3YsYoEhHMRmiHm/P/\nFoaoBFIJT0kSbIX4rJm9g4KToOcPjprS1sqgifRU3GXIJeKjFxfQNwdBwYIX\njpo4Ig4CrtXihCN9KxxcFqjP7Cz0PcESI9W7+3iYWG/bvYclIm0I11bHcQTa\n8J8Brumb/Sc1faPmFtF8vgNu98r3DicV5UWd12oce/N860GOa8MB3VIMn8qx\nYTPnnZ6x7tjHJHPABpVUc/7+tcxRbH1z3RJM/EMRByr1ntA5z6SuOYuaaH2x\npDfmN2pm+XuS4P2fcpxVj+XtwvKto10jvf3POX5sWZvIgvbZq/uNbYQussMC\n60FE2lmWcsLDq4cDvKr+Dd/sG0B0fGGJ/FWNZ0qKUX5KHVWT59LOysAsssJw\n/yncXEIzsoZiezi1GMlwteovTbFxj+dvkfL2knuFiPVvn/iRVFBDNDd32LTf\nI2mGkZpY3Yw51r5OD81CZkgyk812paom3GdLT1sPobroPKnfuqlw7CdIJVEB\nr6RvQWBx/ZnZrY25MUMFL44WzODQ6lx6a4qyQwuiQBQIQCICQh19CO7P7ViH\n7NOb\r\n=ajbx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.13.0_1614034197989_0.43626495108514085", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-for-of", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "dae384613de8f77c196a8869cbf602a44f7fc0eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.14.5.tgz", "fileCount": 5, "integrity": "sha512-CfmqxSUZzBl0rSjpoQSFoR9UEj3HzbGuGNL21/iFTmjb5gFggJp3ph0xR1YBhexmLoKRHzgxuFvty2xdSt6gTA==", "signatures": [{"sig": "MEYCIQCj4YVcKt8ZE6gQGmRcqVza1ohczjEV331kyh0nS2C/ywIhAO5MEylw1pdqJfiLMpbV+N1N9ag4wT+gs3tVyewfmop1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrBCRA9TVsSAnZWagAA2vUP/3xvcBbRTkYXPPg/17K0\nwYru8asvA2brR4SyXdQXRHENwqq6ud4mLX5ZZRV16G9F7M7Z4D23jdqVqcf9\nLyCXRMyloI+zlevmiPgvpfc4HXgkZMMLFWFPh11qZOI9IXkDMOe2TQdxIec3\ny+VeBLenwgplDlPDl9OOV+Shnb4K6klBX72YIdT51WipFzatWZpYEI5JRhp4\nPq4U7RSGO9E80pgHIUNcch2r4/8o1DtIJ6QHIFSl3DHERHeZdhdmylckPMPk\nFT4q1uL90vBtHEiH0M0Df1Bu2iVpg/ZUdxoMzcEJ80ZYVa8N6XJKtyVafFpj\n07h7kr7tcq5C3XUqJEap6TC4qpRkZiSGNrh9GDGtUgh+5XzzOEnMfEz504zW\nQoJxenUD/QPcK4HS0TXvtFOA3nnUaOHra4fVt8zHxxTdMj46mZrW1cIOWT8P\n16BdUcEoAAl+2mQJZGM1Xb3z3j9dfsQGSMEDngKuVx0foQEgrSpRghSxrf/Z\nRTatEQpuH9qnk8I6gLlQoE1yxI6mz8bNx4/vIY5E7Xnn/rv4jzx5565PfoIC\nq1R3MVEFaFyEDRNaBjxuzyJo6LxAvlS+0nSZvZR/kpFMS8XcGYhLaqfSDUZL\n+yGnpVcVHsYrSqyOzdFWwPLwdC+XwEsphnYPiTAtHYH1NwBAyh+CZ2b0cOAR\nPjqf\r\n=gop5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.14.5_1623280321484_0.13253608668299321", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/plugin-transform-for-of", "version": "7.15.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "25c62cce2718cfb29715f416e75d5263fb36a8c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.15.4.tgz", "fileCount": 5, "integrity": "sha512-DRTY9fA751AFBDh2oxydvVm4SYevs5ILTWLs6xKXps4Re/KG5nfUkr+TdHCrRWB8C69TlzVgA9b3RmGWmgN9LA==", "signatures": [{"sig": "MEUCIQDVlJ3CGGJsTqxAn6WFIaie3CBVcAsUw9Og8/x4TZSzLQIgN7u/scnwMukBSRIS29PVxEaFDZXae9+mUCjXcSc2pXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSLCRA9TVsSAnZWagAATykP/jZUzUGn2X9uaZ8l/Gyz\nzL3PzvNedukMDPePQVb4ciZ1LY0d4cOTg3vovbOCKgtVu0SZ4TXOqpwKfvYd\ni1eTDkWKjvpG7vd2kyUZb928wZ8RpOKUbsiQUIR4/ppAHBASvOr8CbEVXbMa\ny3qHAs2g2eF+Y1ql5HugqTZ+3vP6sJcw5SKo2X0KIsABdM7Vox/mC7POep7N\nSll8MMoFBqi6wQY0nA8J/Weumx0+RpppJG2w96sMXbNsp9tsUUMj+Ou/I61j\nOQIn2OyoQMp+lSWZ1r5zKSLF5Tx+5ty55S5vW/ME/WnrADPXC9RGnJccHqfb\nHXLSL73e8QYw05ThOB69SQqbaMfBdBUeGrUh6ag1a4rmZmkOJEZhrOoq3VwF\n3tURVP9wmoFHg8WZuJKuSCxWxLUaFShc326e9yCl3iaOB6TZVqDL8UtWTWaS\ncfWilIiOTROPBU1vMSaiWsh4R6e87cMLA7Ty3DhMekYUvZiSoGoz2oz3CQqm\nTt7P5J2sUFZKjNl++DQSyszp7HJcXiI36ZEmPgmDw7yKUSpexAsYzL3ARNIF\nOFIwdMpsehEF+eIkvIQxvjoKHvPHN5AhzIgFc6u12wAqeNqtr3O1RJSfbviG\ndhmhyVBU+UJroxGgvDnoMRervTbzANMq2rZhCWJFkjfhLxJ5P2y4q2OlA+12\n094P\r\n=+5xd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.15.4_1630618763393_0.5548717115300457", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-for-of", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "f7abaced155260e2461359bbc7c7248aca5e6bd2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-5QKUw2kO+GVmKr2wMYSATCTTnHyscl6sxFRAY+rvN7h7WB0lcG0o4NoV6ZQU32OZGVsYUsfLGgPQpDFdkfjlJQ==", "signatures": [{"sig": "MEUCIBpnFJ1v4BxZmng9f8PLCF4OivwoALp18yuo2QwT4gXuAiEA8VIUpR5gfbJwrWesz5D09GifRFftLiyCaKxw16dRBiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14722}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.16.0_1635551249439_0.9079073888298603", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-for-of", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "9b544059c6ca11d565457c0ff1f08e13ce225261", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.5.tgz", "fileCount": 5, "integrity": "sha512-+DpCAJFPAvViR17PIMi9x2AE34dll5wNlXO43wagAX2YcRGgEVHCNFC4azG85b4YyyFarvkc/iD5NPrz4Oneqw==", "signatures": [{"sig": "MEQCIAgvAbPpZPQUwsV2stj9iAtpuphV0gQsJHJ+jLRGnMtDAiAfUEMsd4dagjs/YLAMW1SLBPX9kX5fObbPiegBlCfvzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j8CRA9TVsSAnZWagAA73AP/0pB20BbpxW+eV14cbza\ndOnNAgESxV+zE85CLtyZb1Bb0kvBALAIk29nfxrtw54KvZ3Blrgmo0sVLiNB\nuuMG4e1qwfXZO2Ccffe7LCvAoCdF2kj5zRVsPdxfBuB6b1HpHaSbrUzCWoJ1\nf8eBrDgdFtf/eSUGenhXm9kquH/h/4LbtcFRj3hReQINnb3E/Y/4IO1kGL+D\nYcsk/Nh7GLRFG6kqrLSKbAYSikhqt+bXO3C7FLDxFHGxlPvLT6ydrCJIN0Hb\nFeRB54vYuiVUKJbUfzW9/yPe/j7eFPis9Yjk4ZjGacGmpsB5AB4F2I+zlsk4\n6/lcqwovPYCiaNOIxV6dwY2H/5QMd6zgaM0Htp3lHQtNG7/IjeND2bLmqUVh\nNJ7niZteOLyqizDLzEnWf+/WiEcq86mcVivZUSgdnOGhIAqsd24eMUve139y\nyjtszQ9CknPGFh3pvvifHbCt/mITtolSAPfSlHYl37wRYFmKsvaWB10R8DHC\nmFRf/MER/tgNoZEUrZdGp1Ctd9P/IX0MLtK385n18TdeONcy35eoOh5nPMVy\n6gg6rIQNlszYYtkJu7ZIfdLyhMgDD0iHmZJBbIN8bO5IDY109lGwYnmfmlXu\nEM4OET4aHRVe23ASx8W7K8BTxQ1U3w6DxOc9YvfqNWGxT+C2EppaOV61OoHV\nJyWY\r\n=K1ju\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.16.5_1639434492573_0.36622274449426473", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-for-of", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "649d639d4617dff502a9a158c479b3b556728d8c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-/QZm9W92Ptpw7sjI9Nx1mbcsWz33+l8kuMIQnDwgQBG5s3fAfQvkRjQ7NqXhtNcKOnPkdICmUHyCaWW06HCsqg==", "signatures": [{"sig": "MEQCIEFl4T7QZPRZI9nMJqH800JXlnDcsdELOx39AzoEGi/XAiBKwTZ3EPkC0wAZPkOFn7SC6Vd6KSo29rKOrDUewqpZpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0lCRA9TVsSAnZWagAA+dUP/1yUXErn1W1Xwi3ZoeF0\nuPVrujpnsYOpMRvdGlStfeMxw0GFd7gz9GngwDXF2ViwYkD3TrRl8iJjQAzb\niXkPZtYt/W4tlgcA/0WPobfRjOMDa8S50gl9XUiqlXSjedHIj8A942Ctzfmz\nwY9FQLYnAjOyOKghFtafOhvqAsO8c9SfrgMTmVkKnuQFXnuv2C8K7IiPzSlY\nZegbc3emEI4epkTOoyQ+GVyfORBJ1M25IWT8BU8grvM6d/PxipRExwSYmxL0\nGdaJCjBY6keBocPE45qEWTPPsZvHLMq60PbwnIQv5l02HhKzMRoBIDYcTNGf\n/jM7GziYfnH4fR11fjSTG2QEf2mu4e92GVhfOYuW0o1XLbx6XAyflUU0qY+w\nlCivJbHxR7w6S5cu2f/qZu6VfkvXgrzb+ERqNAUS//JGzgJXAUtrOXMij0h/\ncE/sEyDRZfwyKZzJldELKU/f6OSfinR3+zTq8kfnV8Bto6jGn+8uo+Ylpcrm\nHfZgBy3H9IHQzksBq0Kuq5QQz1rYPda4Cg/3tto6cPm1W3+ceFa+sJMOXMw/\n1FzS8JGnmPDBOOYmjTdNxC6BXbaFthiuu+xj1JVC8Vt1hyV0alOL2lwkKTg3\nd4cA8vsCoX6T44uqG3/GiEmfeNlhWd6LKg5oYVOD27VKBm6v0XtPovlq87v8\nhbjz\r\n=OJRd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.16.7_1640910116711_0.9232052548344527", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-for-of", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "5397c22554ec737a27918e7e7e0e7b679b05f5ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.17.12.tgz", "fileCount": 5, "integrity": "sha512-76lTwYaCxw8ldT7tNmye4LLwSoKDbRCBzu6n/DcK/P3FOR29+38CIIaVIZfwol9By8W/QHORYEnYSLuvcQKrsg==", "signatures": [{"sig": "MEUCIAy66rHU4MgDQ2SHmaNW6FhWKdCmzCdL6PSVXcKr3xKBAiEAsbkXRO7ShtdFJYMWGyuwAJWOAV5XBU0DRCKYEc+f5OY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqblACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrRQ/8DgJcTKGkql+qwBKTYIiiy72KnO7P3SOQ6yIYJzMU/MKWNCFY\r\nTVMBDip1Oa+jNu+IjkY/dBTl7kV8c+6ZXOHVNFVKsIjwQtx7t/VP9aTzP+FC\r\nXWWAKbpTm0l805aqxMRZBsnOTOzqVgIp4lLWDJb4jEULSWlmC+/Aavj1o7Ew\r\n/BMaD6rN98VJITa+Dx/vTKIRpgwWgP0b9EYFfiHstmzXGXH1owAaIH6ViefH\r\n67SOZ7yOCAkKsXJnLkTLACjcDWZVPslOjIpYXga3GMC9kQEoh0K8Pluugd49\r\nnHElz+vyc/zOICuQJot5RN8YgaPyh4p3zggoimlk2mgvuuTFVJwOpd+kBqjv\r\nNH2fDOCh18J8HoCqKRD5IrBEByRI8qFJHlat1MQ8ufg9uy8Y/ohX3CWGCxGJ\r\nxA8wiq1Mh+PZyXT7gbOMWRFtqeZTvUajJYjTGjja8RlFDay1eWroNrDC7u87\r\npaNibeVlL0GW8WSk67kl5YDM2Hd4EmRMzjQ61YC6xm37VQTRK9wMQFi8+HVw\r\ntKlmJo7BoNDOoPX4bCNTVSc9UtGw5GLNwkg5xD82GaQRsVoPFalPwPjKkUiM\r\n23BgARIA13K1moZdSlkK9BJq4C0FPn+CiRLcAfnzdTR51kjRmO9wPGlhQl/n\r\n2lRjKjifXl3q9mBMhrtdiVotf94Mxkllhfw=\r\n=433l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.17.12_1652729572998_0.432095878810282", "host": "s3://npm-registry-packages"}}, "7.18.1": {"name": "@babel/plugin-transform-for-of", "version": "7.18.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.18.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "ed14b657e162b72afbbb2b4cdad277bf2bb32036", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.1.tgz", "fileCount": 5, "integrity": "sha512-+TTB5XwvJ5hZbO8xvl2H4XaMDOAK57zF4miuC9qQJgysPNEAZZ9Z69rdF5LJkozGdZrjBIUAIyKUWRMmebI7vg==", "signatures": [{"sig": "MEUCIEhc2bpoqDWZIZ01JdcN4KTQ2YOC+ULbJRxHY/YxpYxDAiEA4vp8PUgThWl2x2smiZLUyQ15BuRJS5N1LqIiNItxE1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihqqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6Wg//Vj/BHONimPzW2BvipUQJ7nICKFaTkk8Js9JbPBPT5b5pYyD6\r\nUgWVHLP0Dfb7NrsdBqCY3Be9noOTnPlnsSoxb0E5ig3NORbumIr/pSPx5JjK\r\nwQVfmNLTAbmVG4W7yI+kMIObi+8Xpt+379ZTWRjacFjEzi3kylRmY1Fp03mm\r\n2BciNMatl/d7d9bBG0iONp6Xvz7AR36u428vvwxFkQOHhkhT0uNBGER2KLxI\r\nzs3To0HKOrlwmJBAgsI3Rxyq2pvDKspaktCZ/eX7KkF2MM5/yoIY2gDNf/Cu\r\nevxtfgjDZE4FwHcPA+jYkBAoSMVsVyVKG3xa9pRDZ2maKwV4BbmyJpH0LEU3\r\nNheyKe755X0VyQAsH/MeRG1LL8G+EAUZYx9c5V/3RHP5IAKCvoqhE+29ak7p\r\nMcHoOd2yBkyrtgW04ne1wyIoFQl6QQaMD3Yj+vywb8N4k+zsZuS70eECPdNJ\r\nniEmR6wgxVGEdZ3SThge9BsXMSFFQwcVd+xJ9C/iOjLGlspCSPGyhrqM+4YS\r\niHwvkbzmr2EOdvr8Xr0W4yifSv0rTvE1t4b9luMu20qfqA7VD2Ql6fcakE4Y\r\nhF7+rnFbioVzOCaXotHB4nRly3PPZ0ZjjEo5AmmaXixHqalfgfmlydQwtMvn\r\nN+Q97mKOahJMHlESvZM9RPQR3En9B6xklDs=\r\n=D3r1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.18.1_1652992675162_0.7074988387041634", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-for-of", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "e0fdb813be908e91ccc9ec87b30cc2eabf046f7c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.6.tgz", "fileCount": 5, "integrity": "sha512-WAjoMf4wIiSsy88KmG7tgj2nFdEK7E46tArVtcgED7Bkj6Fg/tG5SbvNIOKxbFS2VFgNh6+iaPswBeQZm4ox8w==", "signatures": [{"sig": "MEQCIFR8mKKHHrkWS/fTc3LSw8L9u0jPS86t2zyEqlqCNtFBAiAog46TA7/pC2W7olhJ9KpDdZIKiCLJ2M17YbIf5jSSXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6/Q//adROK4Nmt87coUerEZkNyGiChpkhqs3JEaFe6fldiys399ID\r\nqlbix9tUJbzMF5vuQK+P+wPaHfs9QVhRWBm/TFGlV1jPaen6Z4KPKwVnkS3J\r\n9cJ0TL8hjU3QSMQl/Ybo/p1XDtDlGAlZULuWbQXy9MeNmtUfUJY6Z/WVKs1H\r\nGp+bpHQojKT1faLnoeKzoJiPZAKOLxORgu+KH7xShHReWRdWhprD+62iPYDf\r\nfpfH2atVY120IX4R1PwL4v47qs+6j3TJuPEmln3UL1IW66ZYTJ2IXcvVKt91\r\n2SfnyuYtsregAhTBRqWz8mbBawWgu8WFE5zw5Of1KvRCsEB2JChpSHNzdE1N\r\neFuNqNkCZ624YdXrnK9R90gktuSHrBfMSNyLBOD+8P5Deip0GT4YmuETtN3r\r\nTCpZY50nXZbX/vnN9m09i4MbYgpwzqq7lqmx56E61+KCXF3cx1gvkZo713aR\r\n+mcUD8sZmX9UtBBEVItYiJme24+CURiLBFPweVhnkUs2LeKpeVlR7HDsNvFu\r\nPLctUWTFeSMoJr8BlNE/U0UxjOREzahVSLI/qsi0Arw376/2eOdqyyhe/o0T\r\nZM56s4lWvoS1/JTAh3tZBDEaCHcbBDXhtG+XKzufmWf7qlvXFN0pQcQ96JaB\r\ntoXbdFK0ST1rVz0yI2vcwzBYN+q6xFNnI3g=\r\n=am4Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.18.6_1656359399448_0.693064744824035", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/plugin-transform-for-of", "version": "7.18.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "6ef8a50b244eb6a0bdbad0c7c61877e4e30097c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.8.tgz", "fileCount": 5, "integrity": "sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==", "signatures": [{"sig": "MEQCIDEJYWCvWMFRAjRE9P/0pwK/sUm8ZljjyPeDYdkgnJrsAiB2x025b5sxS/pzVSuipWeeERPKhliiXIQyJ2ywt7XkHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/myACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsVA/8C5Xo8a2x1aguQf1qFkfGb1glNNm41VX1b4KYI0z/wpNOcjBt\r\nsaFMu09dj+84QzPurMC3iicFpbF1AkTglyJqxuFrr1XOZ2m7ugeXi15achZ7\r\n0kVTGVb9kb38gL0m6g1p+38vwBIMEtjweY5NIBv2ygT4tOwAKwMHowhIPbbJ\r\nShNVDhA4XdzQmBuPixSoAEpAy07qjwIPIJ7wtyykRjpgGZYq+KmGl7F2FS7D\r\nSS9WIjhZtnCj0x93nBeTluwAVSqRtfbnSTAwq08rNKOyRpy2sQRivCbN7CwE\r\nvoZjYUfPfXnWYvoIPbxa10psB965CmJkqh0l/no+sA7U9n3QOFsRl6UCfNso\r\nqOVSAdaDNiLxSYtUfQ/noyX12Le7PzxjQ1qBNretPtYF6VJ0e6etyTvWGf2g\r\nuo/BHm7fY/WI0wVbT8qIIBcP44VjXQxms/ysQ9tkZdDTGaQP7XLaqfikjOzN\r\nZytLIhn2xLhe2yeUFKNu2S9E7j7gWyRh4vGNPFCwbYR+3RgCyyK8GW4I8T+o\r\nrN8C8G5kwhyegn2c0cmxiIaGPzp2SILxqm/vI+6gxOcVRaHuD4bsl+UCirTE\r\nKAMgb/yD7ZTSxCAcvU4qzE49tIYfD62367WcNPt8DBu+IuSu3gYOQGThRB88\r\nNmSfJO9ucv1ZDRqekm9Xg+x6bqvvB2gilQ0=\r\n=cccY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.18.8_1657272754788_0.48070327014928194", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/plugin-transform-for-of", "version": "7.21.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "964108c9988de1a60b4be2354a7d7e245f36e86e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.0.tgz", "fileCount": 7, "integrity": "sha512-LlUYlydgDkKpIY7mcBWvyPPmMcOphEyYA27Ef4xpbh1IiDNLr0kZsos2nf92vz3IccvJI25QUwp86Eo5s6HmBQ==", "signatures": [{"sig": "MEQCICpR3J15qOEmm5uFwpSnHSt7ouvhjVYr1EdGF/0ZAs38AiBO/hkEhJPqWfrfJDbwc05ESg+SniRgmEItOPmq76cxNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXdhAAiVnJ7dSXIl3Cn6uNUF/epsMFeE22MgpqbQ+hKUwQN5Ci1vTv\r\nMQcHfG6xhGELbo+nBumtgPjjC3tLqZe+eAvO4RD/rGCQm5pc5EVw0Zdhomym\r\njgUqhhhBygfezlgGpWMxwqwmD9ksCGGRWcnU3WjW+ddHc6b3czF4zGwpH1wZ\r\n0SPWGH9Z0r1puhKyw9WPI9WzZmzKvMJzQPeC/ktRfycWLYtl0odtP18XgzXx\r\nVzgdsvbRUSHEjgpSqfntE9x1uLQj7EvnC9H7xxdAO7frL1RujIvrCPNaysC4\r\ntFi0IAttpxfMDhAsmePCTS7SCgEfBdtTNDkQFAir7aO3jJw3JRsi4+ftAj0h\r\nQ1KsCMM5VetDmuR2lX9dZ3ud72UQHm4czmNsQD435wwL7VfGilkD4Gn/KdoD\r\nn91r5uFu6co2sgWMlkTNydkv7v1xvR2S26gK1JKayap59Xe3/04ieWeQaQvI\r\nWODTp9/SBqST3Wlo4GWIN0OgU2nvXQeYV7J5GPvUyxJBlJMwJ1eUJcmVvzGj\r\nK0/CuApRuU8AGArXiIZa0vtYne7fcrHz7YD7XQ1Hjo5r0NWvC/XxYKjgRJFZ\r\nmURnkAvIMbsL4Tw+D74RFF1ebvi012MeYc/HombnoSZQp3jYEa0zB3/jlv2r\r\nZo78EGwWdi8JfFHjrnsK0eAyuTWAFgtka+E=\r\n=Ox/v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.0_1676907058359_0.8199002440177672", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-for-of", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "3810580d4d00999f187317ae0fa1ac807294ea02", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.4-esm.tgz", "fileCount": 8, "integrity": "sha512-qQzZinUQFYFiYJo0Ezdo5SYbcavBc3GVymf5LJ6DgEbFVzNkeyUXatkf8qp7ADyQQbkGmtJYWTkeFPOtJihLiA==", "signatures": [{"sig": "MEUCIQCS2HrXLzx1KcYXMAi8wSNfe8CO/K8SQX4+XkO6aNTxlQIgQhTgFeYGqfx9Zd9tx5YzZ8pz2D2YKgYwcCT7+CJUnmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol8A//Ta9fsxRDnn4Saxu5gzGWJ22wG5aP14DlBsEahYr1A+CI8BzN\r\nq1PtK3fsWUxqFZde20Xc/w3hJD1fpVUqPE0IXHG0JZbmnj/fljyUy8hlf5xa\r\nqV9GlHD2pN51K7iNxGuMri6XDcn+rzuOjBW9fiyEliK+UsUS4fEkY2PdYuWb\r\nuE8bEYFnzccN/YMs9MM1gjd6wCtyAyIjHFC9G/DPE+o4MawSqEeAFuIJ0o6P\r\ns2rhuRLGE5acBPVwLlPIHch0ixPQm+jsXDQDNE8UG/wipgqj9Oz2CLAjElt7\r\n2tKtp4q/wVzSBNe4TV24cROzakUHAuVFEIVUBCs+DeDXZxGQLcp6LtnMwUdA\r\nDWxuIQHbQYFeRY4SgVD5g1Pjyq0yp5Ukujcu0i+7k7m9W1tUooXb4zfQrpt8\r\nJiRBHhfZa+8Pw8IBbwHEPN/l70NMj02ARFTr7l4olqE5gdbRsIo/ZgMyIkD3\r\nYG9XhohAdbsc4XA/Tx6j14IT+NaL9oVtSVitSmE30JanEh3z1IzDZHnYQkA5\r\n91lqP5tYfT+6eurw0REy55lC2VYbwKHDy9Qol70dElrDyb7W7tc8eOlCbIu5\r\nv6I6P/62c+91Uvvl52FtsKOvtbSAlyyHfnJrcCo+279lB5yL66WD34Zet0L3\r\n+5wlV9s84qXvOmvggvdE6keMxyZMnP7FqvI=\r\n=kTHT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.4-esm_1680617362651_0.9854582576418609", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-for-of", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "c94584d9ae23f28d4555ef48a1e32a6399ad5934", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.4-esm.1.tgz", "fileCount": 8, "integrity": "sha512-AgcD3K4rNWSCWxTfRHp4hnzCGta1ULJ4vUVAudOxaIjBTUcHjg6/V5zbEc5MexNJtcf63m91W2P8y+za46iCEg==", "signatures": [{"sig": "MEUCIHbYAdQfB14zb1gd7M4zcp6N2HrYx9NJmaauQclFD3J/AiEAw8mTAG1lZ67VgIfjtGe/mM9R0ECY3v6bizd+8bqZmT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWZw/8CXNGJDXRp2snZangejx977/teKShtsrKV2OUQb0vXXTFf7e1\r\nNHYZnos6T+skhfINH/ECj/RBXYnYv+v6PJnhft9+XERIjqYXlFMSgjCz99Zx\r\nKnmcbkkbknXk1jhsoQpyFoj80COU6dV2BYg4JONzdEjvXXEwz7OXOHKPbdbf\r\nyxjh88GLJ+85ed+xGxhGVgWePYtoVasokXZuUSYIZGOSur1+8+qXXI6U2Tv5\r\n6mQeah6b1G0dwPpTPpyzGO/vTh/ohPSAtPImvYoYku1LYyguoEsWKDw9lRMI\r\naBLt8pODTZ3Q7yFjUp99WLtaM8WJ/1Fg2W3Z/WN8KUR2GCjmAOjgoqrc9O4y\r\nbBwdCC3KBF50bugWPxPZqRgWy+y5SB+ERIue/iZJKXsOYshFXKuOaQ17+H0V\r\nsfXLVSQ3K2ZXd8q5uBZV61nPUB0vSlj46/ngjN3f+tThVLkdi9A6V1L8Uy/g\r\nx8aiZ56svAa/GnufrpnSKtmbdVO/Omd47GJR3ppL9AbeaHcfEtL/xUlRL9fl\r\no/uRHgLn5k833KT3DFA0HsKyZvO45ZlVkAF+jqfOEVjDcgDPhBVeMkBQBD8G\r\nIpst1r7i3lhhMCsjFP6qybvYXlRL354bdIDAHoxo7wtqSTNnM1lpjpwxKu5L\r\njVX+pfVB1oUN9UnsyNkb6Xm30Bl4u5oVmxA=\r\n=ds+a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.4-esm.1_1680618071553_0.5797020888868121", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-for-of", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "b112a4dd9ed0dc1bac970efd57727d106d69ea6a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.4-esm.2.tgz", "fileCount": 7, "integrity": "sha512-rHap5tVEUsnqFxbonGdKIo/3MEYmDqEKiccfB+ciOukU/7xKifNGYjBixlw0kgHj07YieLyIi8iqG+RWRLrj0w==", "signatures": [{"sig": "MEUCIQDCHkwLYsrQyjjHEAvIf6C7w2LkOht0a3uEFnN76/tEAgIgWVxHj4nJwxmWqtFFaX13a+T0spKglc7Q7YXPnfwTRHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuKg//fjmCugH8lsDP5IqTrDANKB2rAXw9e8drrt/tclHUlJTHzU9R\r\nB2XhmMZya4cnsLNeA2stV8y7IKY7jPZuHA92blsXRjIZyuqtuTrrXI3eAC59\r\nFPKLUKJn1YDZr0jOVCHbBl2qw9fe0146Y3a+55ADxWhFKIBq5wCnG26lCRci\r\ns4B/u8M5g6EPR0wMfwimjkaKfO8opw+kqLctHBQnVNTXGNxBcMWitCLvB/pJ\r\nNqSV14IISW7ZZFnv1VZeJus4/RL8yYbha4cMqZ9Kp2sRxn1ntJJqgQfgzvv4\r\nQHe0+TKRNnYYR5IAlY6Y12Lee73xljmI4CHpnhpA6HvIsGkOpt3MwYXkdmGP\r\nwzc/pDxmQdYhzBktrXZijDVEyIDRvkPfbwdMemxu8PSPAFh02a9SZF9b9ORa\r\nR9CAo+qMSbuWbSn+pCyuB1sbs7G13Sf+kGk3LUvssJrMoJaF/W19ry8p+dJV\r\nGpwzMdR3DiO8bzXJ6bYhLe2H7Gw9Sixr50w57BhblnnsYZyZbIBuwMs1aJyE\r\ng9adOW38Oy84c3xZkD/8H9o5pwzAk7MnO4GLmo/VaI1Gu5E6xnETowrSrPUk\r\nuqehNVDESzp7PTcwEIoyiYjJjamwX5DAF/+xz80bZbNN5cCich+iqAxu1lnE\r\nuq+0wXx14oh2/NWv+0lUxPgJcn5NIHzIeZM=\r\n=K4hz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.4-esm.2_1680619155015_0.8724062591246533", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-for-of", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "c44021da24d8401824be98d85d441aa87d0835d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.4-esm.3.tgz", "fileCount": 7, "integrity": "sha512-i51uGYNRJ44HRqHs8acG/1/lLTFa2RbA7JiFYYlEcSARnofktZXoKuRnciwcHmNKkXeASEBGIxdUI4Z4xgOB+Q==", "signatures": [{"sig": "MEYCIQDJxLi2tQo/x40BNZnNm0gPrW35Q1VwX7MWY3Yp9MidPQIhAOXoMvg3GjqamRDpq0smTfLI8ImNPS5xR4WsKJ98ayuA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxjA//Q/toVmppUeVPuqL3zpEc1Khl/9HmAfYTbouZDb1K3VUFe6xa\r\nGA0dCkpN39sCyO3101dY6BbSbagGOUpF1EFfFbuTJt0lA4cP26BtLMxdyzon\r\n8wuCgDafr0Sa0cjPePkRd/77x8Vo8v7VALntZI8ROcXbQp3oBNiM3B5o6sJE\r\nSD0Y+7BxaxnDu1Y/VmR7Qkl4lJB0wLyL8NDU9S+5yDHtArD5T5/SDPB2Ntdu\r\nlYj0uDT+WuK0i/mHpkG+pqaZ7O5QublQi9V8BDXNBlFkMC5QI9A6ppjoXJLL\r\nuct2l63rYhsTN3GFBFo1yWebXeIggkD4uH4+SHslt2qFPaCBxMujgL2OrtyN\r\n05g2cUw7qLlnrZflZZsluxDtXVjPbb2QzuYwOLKqAHulPM+mDj7vAL6gnSne\r\nMEj4RecvivLEzLr+DQCL1SlKftsaRQpTdTIRNEgA2ggi/4vt5fTXZlg0Z8uf\r\n0RadKt13McDDJiU5i08inMLN7S4oTe5W8u3ikNYjY6Yg4lj5YOMdV0jg3JKc\r\nHrRgLJlUq/OcFWKEGlmOkCjba4WdCl2bWhduI2FODGIfVH8CiCjGtCs8J6fe\r\n1a8gN+78u0haA8vm8sgZUu/zXVMFUmuMxY7iGUEhnL0BwIadqQ0+rz4dx9NZ\r\njrsgSiwaIHkJiXaPPrTmxCLq0FY77Rkzxuw=\r\n=ArL/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.4-esm.3_1680620166042_0.6135704166071181", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-for-of", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "b10c6b6600a1fb9a9d2bc6454c7a687d8426172b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.4-esm.4.tgz", "fileCount": 8, "integrity": "sha512-aL39v3/5lWI8TmzvxPl1fN0ftefcrXa9OoIFnRcKwCIj0gCiK/oEBJMC/JPKQIdrTNCTdwM7ZCM6I2n2xMiv4A==", "signatures": [{"sig": "MEUCIQDsHf2aKaH71HfFeuTnsQiGq3nRW/GPsIVKlm3JxPL7LgIgS1in3bMkXpiTEFiAhBuzoJVsdqE3jDQ+vvKVMaeJcPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQfQ/5ADLE0c36PTy3Hww8uBQIsvVfLYb9I82BRDb6nzFO9Vs/kHav\r\nFU5eRYFVCs/YGZCEjhrrnNm3OivBDHmzF0i0uOJxf3QZlRnypSdJ8ll4RMse\r\nTc8zIQwFNlSoq21FP3k9gBi+CEdoMCLG0lBV6Q/x2TI4fZjNeW+z09Zuk87m\r\n0RgdtZVzF55Z/QZA5kV+7uSVeKVKbrfRlnWSMtrNLp8nqzOYRLlt3545rNHv\r\n/C9a3c6QWDBb9eKG3XWUpWpnuszFH2kOKmYKFc/l+WF2zPzqPhhhdxjXS1ca\r\nZCnVAWG5Ls0y/2IU7eLn+nCq6cX7dGb4rujpBRf8nLJ6ijDGFpdKvuUnqOza\r\nZnxX/jDWoTvLojBI49LHkshLLQsaHpM5xWwV/u9AEux5WKTKi/e6dnJziyBg\r\nGCYGxxcMUx8Lv9t9guV1iPmLFQY3UyoPbCD1iX1Bg7S3WVwsL8jfyB/hHbnS\r\ntaYk/9QS9JAzsyADi7duiE+e/JAeZjCsPPdbcsHMT3840i1yHQLgnz8d5tgW\r\n/xTJ/IkG92lWLXpjX1KIQ3DksbmocTmBH+SzaCxKQkxDwXbhvxak1upe2pyc\r\na5omd1rNJCBmZORcQ+NBsU942k06IxbfTaOz3saTw8/bi8cy3EnMF4vI8kFs\r\nNH1EYnZMjBgHIeZQ+QXQIVHBWOQ1RRd+mc4=\r\n=KiIi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.4-esm.4_1680621198469_0.9691783404072933", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/plugin-transform-for-of", "version": "7.21.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "e890032b535f5a2e237a18535f56a9fdaa7b83fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.5.tgz", "fileCount": 7, "integrity": "sha512-nYWpjKW/7j/I/mZkGVgHJXh4bA1sfdFnJoOXwJuj4m3Q2EraO/8ZyrkCau9P5tbHQk01RMSt6KYLCsW7730SXQ==", "signatures": [{"sig": "MEQCIHAkAuZ2kg9nSs9Iy7E9Yw4cPY5SXCcTOtJvMsn2EkrJAiAwrcPdm+axJKvcxfk3XAwiBC6y/+kKnYWrpKYQvzlOgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoz0A/+Pr3bfRvebrS4NCPNVeMfsOdw3ydzm1+Jwi4OAmplFnuIYI7A\r\nHC6Lf+pBLufW1Sw5LGSqIgMnu7ZAUgVA4rc7Wwr/mj2YWM4k/TBZ91eEjJJZ\r\n+gFgWVc9AMhUnsNoxKR7qsjQU6jNeo636UbQX+RqynYJ/SAtCfNWFc/3X/pC\r\nP+eFu+H7KMowG5moOOBUhKZpVyNdaHkc4d5aspXuzRB2ONb6JqFcwVei7v00\r\nPvr5nAqzvRmltpfxjq+aEiTJrdo7yL7/rr+Lc0SVGOBA73PWezjPPxCGQG9s\r\n1rEHlDrQQOJbQA61vW99qoc4kLx08Mm5yp5zSKJDvBHhQp+tqOUwO2V4aaIf\r\n3RAICHKPzhtNKkVffTgrUeudok+F+ELNFdwBqSuSnrpolW5nUf3W5cZNQx4V\r\nHPEp3GCA6Rgr9bLJDMpF1ZUI48krS+pzLA4ohkH6QPNltcKEYYRQr5W3YB35\r\nAkdSe/Skb9+f6cU8D9tsaIo/zfNXW1899FZsggoyGxbJZgQ9AvzrTV1O01cH\r\nYAPZMc7V9+Rea3KGudri3Kaw+lak1HlKHsHqWNfqnAVkg5pZ4NguIYONcRpb\r\n0D2iDRWZHGrN5efU41XYiFAcLm1OprC9EiFiqp0v7FJh1DcjgXe0bu+3/KXM\r\nFHI0m2fgqEuHl6bcGG8yWIpy1sOqFNbqAOc=\r\n=jJ37\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.21.5_1682711420080_0.4349688025576295", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-for-of", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "ab1b8a200a8f990137aff9a084f8de4099ab173f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.22.5.tgz", "fileCount": 7, "integrity": "sha512-3kxQjX1dU9uudwSshyLeEipvrLjBCVthCgeTp6CzE/9JYrlAIaeekVxRpCWsDDfYTfRZRoCeZatCQvwo+wvK8A==", "signatures": [{"sig": "MEUCIQDFk1/aFHxUHWPn0hAnq6PyU0bdxAi1198PEazZT7bdMQIgbZh0MqmVORwnprSnPsxdLeOtHHdQw+iFW3VpdMX8PJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43251}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.22.5_1686248475791_0.6881736365594162", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "c07ff41feb90a73f6e077694fa0f9a1aef97cdf9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-z41+gi3wgSt6xAY6Pge7jfnCiH0CCeYVyFOie8XwHgSSr8O61W7eFOwAZFpjW+kxItdoRjwGy0E1C1l1UY2Olg==", "signatures": [{"sig": "MEUCIBhRTIxbsPohFDLwTye2aKt8YnQdwVI4brHV2XuukUjvAiEAvyMKi90hRYaigBdQOYTzpWCBy3TRALalhI1i7JktGiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49042}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.0_1689861590127_0.7909672719113279", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "cde841e0e080bcfd9a2f663fce3e72c37f2ae0f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-Ju3yTNJ+8vWlOCubuX28NkI3aYTQWW4Pf96kwL11EIIBIuCjNbR4oLBqIFal67rHps9Thk6w314vTFsIDxCT4A==", "signatures": [{"sig": "MEYCIQDPmM8H1C6PD95hM9tTKMabeH5juwBbTtCfSki9267xrwIhAPnAxic6KnZmV3PYfq6dljIH3AMSn9PSUieotURsY1Ii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49042}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.1_1690221098995_0.005655364802212404", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "93421451a760308b29a52fa17073465075f800dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-pPjTYj8CnvRSscJKfQjB0IK0mVE+GTDnHWTwG33eBjRY1MKmxbs0pOOFZUJLGN+c/y2wxewJyeI8co40tB9JxQ==", "signatures": [{"sig": "MEUCIQC/6qRN0/QPqMC9H5Lojt5O2nx3gji0yEES6TLnZKDFZgIgF76C2BL9smWG44QLbP8qqVoUwBsZQOH32l3KZjORCVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49042}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.2_1691594090100_0.20897750607608745", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-for-of", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "f64b4ccc3a4f131a996388fae7680b472b306b29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.22.15.tgz", "fileCount": 7, "integrity": "sha512-me6VGeHsx30+xh9fbDLLPi0J1HzmeIIyenoOQHuw2D4m2SAU3NrspX5XxJLBpqn5yrLzrlw2Iy3RA//Bx27iOA==", "signatures": [{"sig": "MEQCICinZAbqHAm4PObUTFiYhYCvcGbYfnAmghfZClcv3lqMAiAsa35omBfB6rwPDCZQw9cCwTCwWFrKaAIsBbwYFMPBUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43256}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.22.15_1693830305913_0.6999651182939939", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "5095b75bbc8949a8f733a3046eb14df2fbde471e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-WmqEnQSabVgVlXBp2N6UhuEflQ1edzJCjIIwVUUFcqoPXHgJ/e7WyVIEcT9MlXz9h9f6rMChE09seSm3Z1lDwg==", "signatures": [{"sig": "MEQCIBjLgkQZj1GroU+8UA2p27WRtiDyHiys5bByU5XbLRN3AiAXO+ogBqN0D0sWq9PwrYi8j0u3Ol1Uz1HWKjcQyOyPKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33211}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.3_1695740206215_0.9752990247522308", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "f189ee37fa4cdf34cae7db8d9347e7572e0a7cec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-kgQMQErE07C0ORlYUcFRDmcXtASWR/MRJw6X3emcALm+xqOowqLAC6RlaiAKK3smofTMY+Vgmu2qZhEImijeUQ==", "signatures": [{"sig": "MEQCIDeuRlou/9HvkR8ZBgPk8YSBSsyIcprR72wh6n4dHsYXAiAxureaRvha090S2f2nxrRj/o/OiEwYvJqNXcT4R6zd5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33211}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.4_1697076372379_0.38846797975006897", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-for-of", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "afe115ff0fbce735e02868d41489093c63e15559", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.23.3.tgz", "fileCount": 7, "integrity": "sha512-X8jSm8X1CMwxmK878qsUGJRmbysKNbdpTv/O1/v0LuY/ZkZrng5WYiekYSdg9m09OTmDDUWeEDsTE+17WYbAZw==", "signatures": [{"sig": "MEYCIQDxtj4yZwfwp0XqnrOYzXxtRml5qLK0HZhpUXwS5eJRjQIhALbmIy+necALr8lG/eGVNaFh6xx/pBrZ9KGK9sZYd0Zd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43335}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.23.3_1699513430414_0.17179091181560224", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/plugin-transform-for-of", "version": "7.23.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "81c37e24171b37b370ba6aaffa7ac86bcb46f94e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.23.6.tgz", "fileCount": 7, "integrity": "sha512-aYH4ytZ0qSuBbpfhuofbg/e96oQ7U2w1Aw/UQmKT+1l39uEhUPoFS3fHevDc1G0OvewyDudfMKY1OulczHzWIw==", "signatures": [{"sig": "MEUCIDVtHC33R6XlqrqI98qkeXDj83EFI3sE+XJmSGVHoXPAAiEA2fM4yIbKTUfmFDE4JpNv57liA8RFr1k1R73guxGX/+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44729}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.23.6_1702300196558_0.5966197755648572", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "33a7a73de389fa01ea252cd4fd137d10bf10ec6c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-kW3hiK0FMsLQi0A6VEqpRmRqcmcHc36cTz1/Zgg5gLrOZbr326VlFUkD8EYzHm6dTJ4Twuuays6mHCBnplcX3A==", "signatures": [{"sig": "MEYCIQCdTvHRoTpDrgMLgqzvU+jIwYokdD1f9MUeK8OmI5HXlwIhAO+7o+saWWC2mK27TGB/Fc5bTo4wUY6/ZXfn/cefcwFw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.5_1702307966491_0.26328563518887993", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "823da5cb1c3ca2546f7b30d49efea5067ef113a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-Ohk8PI35UhTx0mBgdKt/zijvjH1OgoDk2OQ9gsVlN4zCzhw4SBDyfqxdRXaHZR3FXsIbH/5pgjnQzl8v9OqMFA==", "signatures": [{"sig": "MEQCIGt+zjpEzw0cC3b8S2EcRIuM3N8G9yRFbTN7UUSumC52AiA24BZwboipULA93YUeDM3fvk4Jz+569y+ELntsDKosoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.6_1706285665442_0.776468494175931", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "0013b09809a1987a433b01c2473e0e0e097ffacf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-Hm1+G3ep/KAuUbBk5L3usvJ54gmoO1thnMGxelHOpVYGJU7/1pQQTL/5vo2HBB2Iv0jWfykc6BkeX7GidGpXgQ==", "signatures": [{"sig": "MEYCIQCuzpf6JP5HvT/BTtm/TIPMc7Af6zF/WHL7ARbUgoP0mQIhAM83TGkQca5dXr0zqPnp4WJIW9dRA1G/GGiA4Eq1209d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.7_1709129124587_0.8975106008323492", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-for-of", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "67448446b67ab6c091360ce3717e7d3a59e202fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.24.1.tgz", "fileCount": 7, "integrity": "sha512-OxBdcnF04bpdQdR3i4giHZNZQn7cm8RQKcSwA17wAAqEELo1ZOwp5FFgeptWUQXFyT9kwHo10aqqauYkRZPCAg==", "signatures": [{"sig": "MEUCIEoyHLZEHmWmI5+HvZH+zbsDK8LJ31iUtiTiW87tmR7BAiEAp9oCOS/EBBs50XnaHJ+6LwSoDaYRZtQ/npO85TFI+p8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44676}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.24.1_1710841716372_0.6378033706012611", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "e06257a70ead27ad11819886de954c1d0f315db5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-MwXkL9jFhSN1mwjpHjRN6QTTMjWxDx6Xj9UsbrW6AP5CqEPbd+fe4a7q7lVcsgW+W4RSG+osYKghLEDVsGjo4w==", "signatures": [{"sig": "MEUCIBbiXE0+b/mbhKcahZcgzPPi5OQgaODj07LO5b9OYlmRAiEAxbjVISi+Yc2eeB6tuGg35bKB4mP7k+/7SJiNLBFwFDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34525}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.8_1712236806883_0.614193069709206", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-for-of", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "7f31780bd0c582b546372c0c0da9d9d56731e0a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.24.6.tgz", "fileCount": 9, "integrity": "sha512-n3Sf72TnqK4nw/jziSqEl1qaWPbCRw2CziHH+jdRYvw4J6yeCzsj4jdw8hIntOEeDGTmHVe2w4MVL44PN0GMzg==", "signatures": [{"sig": "MEUCIQDZjn6DWmmrjHWVQT1VPW9uwgIUcTYh21uBAE6Dyemi0QIgZgZQk15gGKYT+qLNuRKb6rArpYZLv2bzabVxTvW2sT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.24.6_1716553497074_0.7744069363358057", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "edfaf51b32a26e37ca12d141b243388cf5b39ab6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-ikMX3yRNWV+sakVtCFWKn6i9YpmDHm/ua4a1uSbnyNLjzv7rShwmgpRaKkx4dgH189tm7aD2cyO4HdQOoIqfWA==", "signatures": [{"sig": "MEYCIQCAk2frRcGUWuOXL2KZnngJzQgKghkp4TDAfG2eGZBf7gIhAKfRk67NIBlkUGBdGmw5VYr8y+77/dLWq1QTqvCs0etV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101290}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.9_1717423523658_0.20487103767207215", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "33106dcb916eefe0c21bf894a896ab67de8f04b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-+nS1uiejS1KOYRW7U1aWE/WvjaBi9Sg4fBvJhE9l2gPVdXOlKqMawCMCcnRYW9iZF1WXFVIHKCIhroZpFFZNtQ==", "signatures": [{"sig": "MEYCIQCMyv2TASGYdsp7uqNaQxaMqc5kC+tRfIIRvsWK7+ogFQIhAL0H9IDC9/DDnTym3PhmDG7htpAOtlFjQGBgJo5IDcuE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101298}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.10_1717500035911_0.2314630412476153", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-for-of", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "f25b33f72df1d8be76399e1b8f3f9d366eb5bc70", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.24.7.tgz", "fileCount": 9, "integrity": "sha512-wo9ogrDG1ITTTBsy46oGiN1dS9A7MROBTcYsfS8DtsImMkHk9JXJ3EWQM6X2SUw4x80uGPlwj0o00Uoc6nEE3g==", "signatures": [{"sig": "MEUCIBdFv6ZullA9ccjtrBh51AVmfqXhDLG7WZI8WjmmQBvXAiEAm2SzkRofE1tKQmIKXEFOY2n28KwieXd9sHTf6TkYLUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111044}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.24.7_1717593350059_0.8730254588321089", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "bd925d4c83b25e5144cd73ec569697053c6dcedc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-7nfRb3lw+JcNKhqiTzl+nHTdYmVFv9CXm6L7z7MyC4v85DEEHkOfQMOmoH8rUjTBAJqR+qSWAv5VMUcYOKqHtQ==", "signatures": [{"sig": "MEUCIQCr8ubKRk7VyZtRvnKOcuOuhNvAafZ4f4ELvncUOoK4vQIgZUp0ejfqZkiUUDZcAx1u5gQhzDwK7oBplcJlT3SLxak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101187}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.11_1717751760480_0.10654467354464536", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "4d2a1209d795c128bbbe4cca39634759304a9f34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-/l2MCzrjQ2IP+WcSFdbwEcp7I/rUd/HY4gvjbyj/DuRVriLIsWtH4h4jWZKvj+bbmX3+mKDFl6jHAo6gcT/UkA==", "signatures": [{"sig": "MEQCIFe+TO6BnwYTyf1TClO7LwLDfByxhJocqcJ9VR+64PLHAiBGBN3Vlu8ATRh6uyD2MvmHwCuvxinU4oJaPyEz2tE6NQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97953}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.12_1722015235113_0.08781609810963964", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-for-of", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "0acfea0f27aa290818b5b48a5a44b3f03fc13669", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.25.7.tgz", "fileCount": 9, "integrity": "sha512-n/TaiBGJxYFWvpJDfsxSj9lEEE44BFM1EPGz4KEiTipTgkoFVVcCmzAL3qA7fdQU96dpo4gGf5HBx/KnDvqiHw==", "signatures": [{"sig": "MEYCIQDGyUveDgmHr7WS/+T9nm+N77BYrod4hAFv93tDiHJAvwIhAItP0q+rQes8uCwnlmaN6UI4r+a2tTTfef1mRdyQyNGs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115551}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.25.7_1727882123719_0.6257725350365302", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-for-of", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "4bdc7d42a213397905d89f02350c5267866d5755", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.25.9.tgz", "fileCount": 7, "integrity": "sha512-LqHxduHoaGELJl2uhImHwRQudhCM50pT46rIBNvtT/Oql3nqiS3wOwP+5ten7NpYSXrrVLgtZU3DZmPtWZo16A==", "signatures": [{"sig": "MEUCICm67gfGlzzO7yCCniIeu5rxbbPm5Z5kYJBNTXvjWj1zAiEAzJHi+CKvpYxxrQ3RNBR3ZpDymT24OkxYh2Oiat9ohSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44625}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.25.9_1729610499458_0.562382942110148", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "4a535166feb6abfc1159b2d889c74074edf147da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-S/ceeNzAN1zTWAulbdR/apaWK8hKJ3474Z8XLYp3S0zQ4zQeqfSbmjCEGDON+t8lUwiGGFXP6TbHon63e/oJvA==", "signatures": [{"sig": "MEQCIHWypTTlDo0vKG+mDb6LHi5Tco5yTxdxiOl5tgHxOa1TAiAjQ5WotuilkAovvKq9GeackDyJRyLqpPoi5lilnlaoZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34897}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.13_1729864480237_0.2777021355465301", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "3a749e2eab69f494df0026e54c45d417b5502a5c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-ZmcyivQ2nw0zHn9xSzDSdd0Kh46dac4x+efbpqsQUKFYXSSTOC0x+Y8i24SSWbB1awAwmYhlsg1EB7rhk7Yh0Q==", "signatures": [{"sig": "MEQCICXIQqH6+j6tPm3gFIhIa8uHrgwzcIkGC40+d3t55IfkAiBu/WefTqphlx1z/szJbK4zm3XBl584qXsYf7ja/5flBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34897}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.14_1733504069550_0.0064475806267498825", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "3173040feb5353fd80eb4ae971869f85e9103b85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-94PDGRqD+lO5Pay7vIumgDrbZM+9lhhaIT3fJl1xcrIPe3Fgwh0+xa62SlsfDfEyqEXHPguVHEROduVTxxbgSA==", "signatures": [{"sig": "MEUCIHvbGVUXrP14UX/AFEAGYug2KN7EB9VtpNXierbmPbwbAiEAwCd1lNYwWnWBlKjxAz4auExXPc96Gn28US96V6BjuJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34897}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.15_1736529897758_0.7991379951207491", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.9": {"name": "@babel/plugin-transform-for-of", "version": "7.26.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.26.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "27231f79d5170ef33b5111f07fe5cafeb2c96a56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.26.9.tgz", "fileCount": 7, "integrity": "sha512-Hry8AusVm8LW5BVFgiyUReuoGzPUpdHQQqJY5bZnbbf+ngOHWuCuYFKw/BqaaWlvEUrF91HMhDtEaI1hZzNbLg==", "signatures": [{"sig": "MEYCIQDIhD9oW6kHWLJYmo4Ns8Nk1VoxFtS3jC5iEwcwg2jbSQIhAK1k4UNmmnaJ4kMOJI0wfw4zLJu1beCX9YQD1+pZyzDN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44850}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.26.9_1739533679861_0.6400342593915846", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "1e581c5d0c4dde03188d3b5554c6df1efa35dce6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-W4QbzdDwGsq9uoeIx2Q19PUJ+uUXWbfgEj6rxN/8JYj4zt84aZ9QZyxoefVsjA96ZqDUDdskHi1RTy2M2/FVIQ==", "signatures": [{"sig": "MEUCIHDmvViA3u7GNrwusXEeHXBDXa1L/y1AxjFPSZgUG63fAiEAtmHlPdSVAVQjWSyM1jekm+SXVu1oZa+cdRu0nndhHWo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.16_1739534373784_0.7036650291535604", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "a87ffbc6f9c76b6a808cdcdcaa1072f0fe16fdd7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-CVU9xLoQVY4AdheUZrlLi12lrEvydAyXJI6UOyTpZydpiW+tO2PNZfvWS+jicmQKfb8X/H+N60D/cxh0k4PxMQ==", "signatures": [{"sig": "MEYCIQC8O8HYWsTkNUskcxaP2iUhw5SYqpiM0wgMsqCKDmQ7SAIhAO5//UKqYmdLTLtF0F11/k4YNWquX3SFoQsGJNRMNsBo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-alpha.17_1741717526920_0.3553125277083107", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-for-of", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "bc24f7080e9ff721b63a70ac7b2564ca15b6c40a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz", "fileCount": 7, "integrity": "sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==", "signatures": [{"sig": "MEUCICbeLTuN7z0VxlrguGc8EDvr6mXYZHINW/kHgoFyFvhLAiEA6TTt9yRUvK1x2GUEdj0Xh2FwVrAPt8g6Azfu89DwUcE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44850}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_7.27.1_1746025762171_0.033362049051799625", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-for-of@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "dist": {"shasum": "06c216cf2aa3fdfe1af899feadfa4bf86701a63b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-UVQxMf81LIpWKDxG4gtV6zneQApP7T6LYiYliTCmumTa+JTE0EUTMWFRAKDXrU1HPYW4Kb2rtV0J5D19lJgPEQ==", "signatures": [{"sig": "MEUCIQDuAO3O+hWPlVaVODYBdtYXYncmAv2Zk64UIUi8cOSqJQIgWyxTQ6Pt36dhyW63j+IysgXzcHX/b4JmsUIqEtg1gLg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27831}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-for-of_8.0.0-beta.0_1748620298714_0.016820828256260745", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-for-of", "version": "8.0.0-beta.1", "description": "Compile ES2015 for...of to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-for-of"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-for-of@8.0.0-beta.1", "dist": {"shasum": "aff2de43ec08987f6275283fd7677e2f92a83c09", "integrity": "sha512-ZY/vFVnLa8y5NscoR0igOnHWP3tlTEdTi+nig3QumTfgUK6X0glUFR9zUcrGnzJY9LojaJkNZLkBUZvIRH3f5g==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 27831, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDY6BT+LBOkG7kDk6ZvNgOrpCAhFp/dk8whug7Tc9At7QIgbUkWkHSExjxmbXGW37M9YdDouFOhxumzGmGvzSVixJ0="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-for-of_8.0.0-beta.1_1751447082772_0.24661044183313074"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:35.676Z", "modified": "2025-07-02T09:04:43.129Z", "7.0.0-beta.4": "2017-10-30T18:34:35.676Z", "7.0.0-beta.5": "2017-10-30T20:56:15.497Z", "7.0.0-beta.31": "2017-11-03T20:03:23.273Z", "7.0.0-beta.32": "2017-11-12T13:33:14.356Z", "7.0.0-beta.33": "2017-12-01T14:28:16.286Z", "7.0.0-beta.34": "2017-12-02T14:39:17.471Z", "7.0.0-beta.35": "2017-12-14T21:47:43.688Z", "7.0.0-beta.36": "2017-12-25T19:04:35.966Z", "7.0.0-beta.37": "2018-01-08T16:02:29.466Z", "7.0.0-beta.38": "2018-01-17T16:31:51.603Z", "7.0.0-beta.39": "2018-01-30T20:27:32.475Z", "7.0.0-beta.40": "2018-02-12T16:41:33.930Z", "7.0.0-beta.41": "2018-03-14T16:26:06.981Z", "7.0.0-beta.42": "2018-03-15T20:50:42.004Z", "7.0.0-beta.43": "2018-04-02T16:48:23.726Z", "7.0.0-beta.44": "2018-04-02T22:20:06.438Z", "7.0.0-beta.45": "2018-04-23T01:56:47.230Z", "7.0.0-beta.46": "2018-04-23T04:31:09.300Z", "7.0.0-beta.47": "2018-05-15T00:08:51.867Z", "7.0.0-beta.48": "2018-05-24T19:22:20.574Z", "7.0.0-beta.49": "2018-05-25T16:02:05.906Z", "7.0.0-beta.50": "2018-06-12T19:47:15.621Z", "7.0.0-beta.51": "2018-06-12T21:19:46.055Z", "7.0.0-beta.52": "2018-07-06T00:59:24.689Z", "7.0.0-beta.53": "2018-07-11T13:40:15.025Z", "7.0.0-beta.54": "2018-07-16T18:00:05.833Z", "7.0.0-beta.55": "2018-07-28T22:07:15.385Z", "7.0.0-beta.56": "2018-08-04T01:05:26.033Z", "7.0.0-rc.0": "2018-08-09T15:58:14.717Z", "7.0.0-rc.1": "2018-08-09T20:07:54.962Z", "7.0.0-rc.2": "2018-08-21T19:23:50.095Z", "7.0.0-rc.3": "2018-08-24T18:07:55.814Z", "7.0.0-rc.4": "2018-08-27T16:44:09.043Z", "7.0.0": "2018-08-27T21:43:05.476Z", "7.2.0": "2018-12-03T19:01:21.368Z", "7.4.0": "2019-03-19T20:44:27.356Z", "7.4.3": "2019-04-02T19:55:50.307Z", "7.4.4": "2019-04-26T21:03:58.623Z", "7.7.4": "2019-11-22T23:32:08.976Z", "7.8.0": "2020-01-12T00:16:30.850Z", "7.8.3": "2020-01-13T21:41:28.088Z", "7.8.4": "2020-01-30T12:37:06.662Z", "7.8.6": "2020-02-27T12:21:21.656Z", "7.9.0": "2020-03-20T15:39:24.048Z", "7.10.0": "2020-05-26T21:43:15.354Z", "7.10.1": "2020-05-27T22:07:22.537Z", "7.10.4": "2020-06-30T13:12:00.152Z", "7.12.1": "2020-10-15T22:40:04.904Z", "7.12.13": "2021-02-03T01:10:50.852Z", "7.13.0": "2021-02-22T22:49:58.126Z", "7.14.5": "2021-06-09T23:12:01.610Z", "7.15.4": "2021-09-02T21:39:23.570Z", "7.16.0": "2021-10-29T23:47:29.585Z", "7.16.5": "2021-12-13T22:28:12.715Z", "7.16.7": "2021-12-31T00:21:57.073Z", "7.17.12": "2022-05-16T19:32:53.201Z", "7.18.1": "2022-05-19T20:37:55.320Z", "7.18.6": "2022-06-27T19:49:59.677Z", "7.18.8": "2022-07-08T09:32:34.931Z", "7.21.0": "2023-02-20T15:30:58.606Z", "7.21.4-esm": "2023-04-04T14:09:22.844Z", "7.21.4-esm.1": "2023-04-04T14:21:11.730Z", "7.21.4-esm.2": "2023-04-04T14:39:15.159Z", "7.21.4-esm.3": "2023-04-04T14:56:06.147Z", "7.21.4-esm.4": "2023-04-04T15:13:18.650Z", "7.21.5": "2023-04-28T19:50:20.240Z", "7.22.5": "2023-06-08T18:21:16.018Z", "8.0.0-alpha.0": "2023-07-20T13:59:50.337Z", "8.0.0-alpha.1": "2023-07-24T17:51:39.212Z", "8.0.0-alpha.2": "2023-08-09T15:14:50.300Z", "7.22.15": "2023-09-04T12:25:06.236Z", "8.0.0-alpha.3": "2023-09-26T14:56:46.385Z", "8.0.0-alpha.4": "2023-10-12T02:06:12.673Z", "7.23.3": "2023-11-09T07:03:50.629Z", "7.23.6": "2023-12-11T13:09:56.797Z", "8.0.0-alpha.5": "2023-12-11T15:19:26.769Z", "8.0.0-alpha.6": "2024-01-26T16:14:25.609Z", "8.0.0-alpha.7": "2024-02-28T14:05:24.779Z", "7.24.1": "2024-03-19T09:48:36.538Z", "8.0.0-alpha.8": "2024-04-04T13:20:07.016Z", "7.24.6": "2024-05-24T12:24:57.244Z", "8.0.0-alpha.9": "2024-06-03T14:05:23.902Z", "8.0.0-alpha.10": "2024-06-04T11:20:36.047Z", "7.24.7": "2024-06-05T13:15:50.207Z", "8.0.0-alpha.11": "2024-06-07T09:16:00.694Z", "8.0.0-alpha.12": "2024-07-26T17:33:55.293Z", "7.25.7": "2024-10-02T15:15:24.006Z", "7.25.9": "2024-10-22T15:21:39.633Z", "8.0.0-alpha.13": "2024-10-25T13:54:40.436Z", "8.0.0-alpha.14": "2024-12-06T16:54:29.737Z", "8.0.0-alpha.15": "2025-01-10T17:24:58.030Z", "7.26.9": "2025-02-14T11:48:00.096Z", "8.0.0-alpha.16": "2025-02-14T11:59:33.948Z", "8.0.0-alpha.17": "2025-03-11T18:25:27.113Z", "7.27.1": "2025-04-30T15:09:22.377Z", "8.0.0-beta.0": "2025-05-30T15:51:38.859Z", "8.0.0-beta.1": "2025-07-02T09:04:42.935Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-for-of"}, "description": "Compile ES2015 for...of to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}