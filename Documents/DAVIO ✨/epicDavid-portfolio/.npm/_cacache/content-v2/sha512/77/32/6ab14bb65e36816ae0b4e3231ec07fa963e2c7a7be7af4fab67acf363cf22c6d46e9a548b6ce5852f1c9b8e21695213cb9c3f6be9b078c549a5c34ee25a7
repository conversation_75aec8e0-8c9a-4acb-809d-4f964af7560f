{"_id": "yocto-queue", "_rev": "9-3a8405ee8825ff18f2b0b4e4723a3497", "name": "yocto-queue", "dist-tags": {"latest": "1.2.1"}, "versions": {"0.0.0": {"name": "yocto-queue", "version": "0.0.0", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yocto-queue@0.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/nano-queue#readme", "bugs": {"url": "https://github.com/sindresorhus/nano-queue/issues"}, "dist": {"shasum": "97d0fce807d9e50873aec71074e895788e327cf3", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.0.0.tgz", "fileCount": 5, "integrity": "sha512-QbJ6TKmdZpVKysgkTWf2m5Jp7q6yIsYpH0P5J053mEPgMsu29gLx1Zdvy+xY+vtU+nWmMVC8mPoD+4hDrmedFg==", "signatures": [{"sig": "MEYCIQDprlSwOT/KUvu3RWJ0jHXUh8yFbi0R0SKtTR1NfSLqKQIhAOqLCZjAe2wf9MV9V9zib/GeV6fg6Kl9lmxUH9Gkmxam", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvKFVCRA9TVsSAnZWagAARuAP+wezd/duXRl1ljnIrUN7\n/w5lNtB5wefcWi/Ro5LLeiYUkFhxAesT7SrMixtxLR02PBQBldg8VIjYHlaH\nGVx8xtE6nItLGHcUVhmQ1teXRuVY84PXsWzo7VWowNvh7oMeGNkrv4P4k8BB\nEnFx9Enmo5pcFwFjwq4+aAH/vltL7KeNeCoJWuxBd/DFowLUHzU3MV0c+UJB\nZogOS0Nvj3lQ2P7o6NJqkLKVVrvAptjOK93eQc42J6mkACLZ1n+CKkn6xd9s\nVkQooZiA9dUb5856Fh1mYOzYKuEKsxxFyxYck+3vtC3H3I4eBhvcVNRHlUiR\nzjzv9RKvgJRpfnU63B1seeb25mriKqN/f0cGA9ekLed/2iOLxON7BDlq0+Zs\nXcBA/gN5dRS37U7qwykzHfiZXec+n2P/RJmfetKs5hJeqoS6tRFpJsXg+031\nrtQjqtkV0HYP0TZXeU2QQWt0CapXfb5nwdz90ud/cLVjLfmGoeUD/Wiz4UYT\nGL1yLP319WAKYVJE6YJFZnyY8WBYhVwMyNdvOksMWGDNChi4DHx+FBcixuSy\nK0rYx5qHdU4ALl3wkTKK5I1QHYtrVeRxVyOYjUvPb8ew4ZcPaSdlShaunQiv\ngh+12kjSufkoLlN2Qj4LW657OubdQN6nASn8AT2BPUVrs6wC2P73mvnZx55J\nYix1\r\n=Nth0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d0c1a94cb05a698f8e2a99555f8cf8585e9869d2", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/nano-queue.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Tiny queue data structure", "directories": {}, "_nodeVersion": "10.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "tsd": "^0.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/yocto-queue_0.0.0_1606197588704_0.1585882120213571", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "yocto-queue", "version": "0.1.0", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yocto-queue@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "dist": {"shasum": "0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "fileCount": 5, "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "signatures": [{"sig": "MEYCIQDT6PTeFAw+w2P0CanG3paScSDLRLBiZjcK25Jh2KvzfQIhAKTn5VxVaUWC1f0cOT5b/wdosBeinflqpF9rUUUuiekj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvKKwCRA9TVsSAnZWagAAGXMP+gOOaFATY9WhVgSiN4ZI\nwyHaog6af9hieXnM+eZf3YLHnC5caM20SxdnHcvILsuf9K0pqkMXtzC9Ghgs\njnFaQ65BLvEwz7wByXB799QoCNv3YOoEKEwvqg81fTEJwVpPB7/1LD/phu2j\n3wcWbfyUBYQUHrFqZeb7eR1CnHDxov6odtBxLs84USK+0+8fLmO4wn30Quwl\nyVGrlNm3X4NmuJFetHLUhfFjIL3cBlfG191/DeNPPhqVBxx2Uj2l9oUX5eVW\nIVkL0s7OxQn8L8VN/YxOqus+kitnku2Jb4hVo/yUzZcx5w+KPMMNBnHqljNm\nGUbrfQBHtUJiZOB684vsR8TFAc6ZWrIAzXKtcejof/qEkGV/60iS63xANr2w\ntEYoNsAlDVBZYqXXbV7rwyBAIEM7SvrVsvGWMS8tAgnMUD0pf2TO1mOP7ph8\nWGlMuJegqEBg9HoHdXtfpQEIq8s76XGnROX1Y+OFXdHiWCYo2US9PsIMXRQ4\notOcWRV/DIXXmKn+pFM8CaE09NpIJBPLCeMtxHSR2qCCS4dCTx7kD09oFdF6\nMETv0Y0/5nPZjUMLsY2xBjp10ntpzH+4iTWmYYhBlNrcNpc/eoZIBMyS3+Ot\n3ZE7p9eNKNW6sdD/pQycDV0S1565FymQaOdB4RAUR26/zayYuZFlqva9hXWR\nMel2\r\n=rCXP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "af91c37f51aecf607ca2261b907b9d86b6a69e77", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/yocto-queue.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Tiny queue data structure", "directories": {}, "_nodeVersion": "10.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "tsd": "^0.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/yocto-queue_0.1.0_1606197935700_0.6375493777344527", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "yocto-queue", "version": "1.0.0", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yocto-queue@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "dist": {"shasum": "7f816433fb2cbc511ec8bf7d263c3b58a1a3c251", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-9bnSc/HEW2uRy67wc+T8UwauLuPJVn28jb+GtJY16iiKWyvmYJRXVT4UamsAEGQfPohgr2q4Tq0sQbQlxTfi1g==", "signatures": [{"sig": "MEUCIQCgpquEkkrOvziIIdkPS60Zmbl1a3cy9CW+fyss6yD+gQIgex85tTH7ZLeMp/djFhHgZ7e9HvigzwOiEE7hUPg+4rI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFbNRCRA9TVsSAnZWagAAyWcQAKEhMtwhQLcTexqOYTrf\np094W6yti0MY6bltGszSDhfoXBVgMBlou8YSk/s7EF/Fbl6356tMK6OClWWf\nvU2kaUWeSdlngv+S5baQ7fk9liA29jIGOHKXgt4h6XFzSfLA9b5hVbmQ0QBR\n37Fj96bt0756N7N/8J2lXREiPvOwkZ7QMhv5l1W8WV11j2QWn5Z64vCehbXJ\ns4M7leEOBA3/+HZYSdgBhK9Xm42+TJP3VtiyXZMnz+evPlydAvt1DmwPDbIx\nVU68PvQIA0/+RgtjiRD6rAX3LD2CG4g2YmdjBXYgq+xlTW76EM11aZbqCkkI\nfv/qevquZsYOn2JWwYHd2dRs7/xTm+AcLV2sdV0hS/bVeQx4AqBWwyA9uk2E\nOiYZ9KcfkQxMNae7+1Bw6bPL5dwpO5vim6OZ7+Wo7Qa83Bk/piK4r9cAZuOK\nYq4F0yxntA6+rHMSQ4EYluiN+mrqVqn2um7WvAO857YRSuOQf6cBt+zfEq3N\nnR3Iqm6V108sjxv79y3U1REAV7l2YZGGQH3BsM+XjVmjqmvlTxWDm/Uyhlzj\nMhw13HW32uWvkY8bIN5iEYKuaT5eDltFSVN83WFv0+kOV1hmvm2b8u4ZDFCS\n6n8rREm+yYSpgaLXf+VA/cTzqPmkOqp8qwwfk6DbJDNhATWdDt4h4TazXUrX\npIJG\r\n=Vcs5\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12.20"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "2e57f8c95e5e1f40379c6f85d18aee962662eb22", "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/yocto-queue.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Tiny queue data structure", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/yocto-queue_1.0.0_1628812112939_0.12892570466581454", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "yocto-queue", "version": "1.1.0", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yocto-queue@1.1.0", "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "dist": {"shasum": "1c567759666a1b3bd4af7897551bacb488f482b9", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-cMojmlnwkAgIXqga+2sXshlgrrcI0QEPJ5n58pEvtuFo4PaekfomlCudArDD7hj8Hkswjl0/x4eu4q+Xa0WFgQ==", "signatures": [{"sig": "MEMCIBpz2l02dc+IEJeIXYkEL7q+UfXMrMpZ+GbFeMM06ImMAh8U5YQZMtJ0jzdmnMjh6lMEOBoDyzkJnl24jy6esEij", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6782}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12.20"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "812e5ed7f3334637922f4cd6ac6c2ce8f5c70c75", "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/yocto-queue.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Tiny queue data structure", "directories": {}, "sideEffects": false, "_nodeVersion": "22.2.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/yocto-queue_1.1.0_1719607810647_0.20351033896965798", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "yocto-queue", "version": "1.1.1", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yocto-queue@1.1.1", "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "dist": {"shasum": "fef65ce3ac9f8a32ceac5a634f74e17e5b232110", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-b4JR1PFR10y1mKjhHY9LaGo6tmrgjit7hxVIeAmyMw3jegXR4dhYqLaQF5zMXZxY7tLpMyJeLjr1C4rLmkVe8g==", "signatures": [{"sig": "MEYCIQCdLjdFMTJ0o0roXbP5W2/Csjm7qpUPvIYcpMipNBCxawIhAN1Xs1rVm3mtAOEjVbsV/Uw34t0K67v8yCarb+MZX+id", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6873}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12.20"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "0ac610dfa4e5cbd929b2e9b8fc34f5417f2f788b", "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/yocto-queue.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Tiny queue data structure", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/yocto-queue_1.1.1_1719699170246_0.38023407968029876", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "yocto-queue", "version": "1.2.0", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yocto-queue@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "dist": {"shasum": "4a29a93e7591328fa31768701e6ea66962401f79", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-KHBC7z61OJeaMGnF3wqNZj+GGNXOyypZviiKpQeiHirG5Ib1ImwcLBH70rbMSkKfSmUNBsdf2PwaEJtKvgmkNw==", "signatures": [{"sig": "MEUCIFV/r/0QkfoMQhCpy6dY7VOJ/GZR8tIfAavJlnHTj73cAiEAkCUkWm1S5nJ7UxI4ThQGFm0F8NCY89iPK84N1RloV/4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8047}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12.20"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6f20a6fad9323285e9f7c04dd1232ccc5e931e7e", "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/yocto-queue.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Tiny queue data structure", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/yocto-queue_1.2.0_1741264180240_0.4540003960919774", "host": "s3://npm-registry-packages-npm-production"}}, "1.2.1": {"name": "yocto-queue", "version": "1.2.1", "description": "Tiny queue data structure", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/yocto-queue.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=12.20"}, "scripts": {"//test": "xo && ava && tsd", "test": "ava && tsd"}, "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "_id": "yocto-queue@1.2.1", "gitHead": "ce72d41de87b2a4ec7c50e10480300bee674d845", "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-AyeEbWOu/TAXdxlV9wmGcR0+yh2j3vYPGOECcIj2S7MkrLyC7ne+oye2BKTItt0ii2PHk4cDy+95+LshzbXnGg==", "shasum": "36d7c4739f775b3cbc28e6136e21aa057adec418", "tarball": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.2.1.tgz", "fileCount": 5, "unpackedSize": 8009, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEyKBZiu/jOlIsCxPvNN2SvDlRNzBVV13SSBlIWKwEKCAiBncBhwnNx4BEqBBp+AAqC7l3tOyHiVtiiazKWlTJiWsQ=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/yocto-queue_1.2.1_1742622578234_0.5609364464617448"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-11-24T05:59:48.704Z", "modified": "2025-03-22T05:49:38.583Z", "0.0.0": "2020-11-24T05:59:48.904Z", "0.1.0": "2020-11-24T06:05:35.875Z", "1.0.0": "2021-08-12T23:48:33.113Z", "1.1.0": "2024-06-28T20:50:10.833Z", "1.1.1": "2024-06-29T22:12:50.399Z", "1.2.0": "2025-03-06T12:29:40.450Z", "1.2.1": "2025-03-22T05:49:38.400Z"}, "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/yocto-queue.git"}, "description": "Tiny queue data structure", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# yocto-queue [![](https://badgen.net/bundlephobia/minzip/yocto-queue)](https://bundlephobia.com/result?p=yocto-queue)\n\n> Tiny queue data structure\n\nYou should use this package instead of an array if you do a lot of `Array#push()` and `Array#shift()` on large arrays, since `Array#shift()` has [linear time complexity](https://medium.com/@ariel.salem1989/an-easy-to-use-guide-to-big-o-time-complexity-5dcf4be8a444#:~:text=O(N)%E2%80%94Linear%20Time) *O(n)* while `Queue#dequeue()` has [constant time complexity](https://medium.com/@ariel.salem1989/an-easy-to-use-guide-to-big-o-time-complexity-5dcf4be8a444#:~:text=O(1)%20%E2%80%94%20Constant%20Time) *O(1)*. That makes a huge difference for large arrays.\n\n> A [queue](https://en.wikipedia.org/wiki/Queue_(abstract_data_type)) is an ordered list of elements where an element is inserted at the end of the queue and is removed from the front of the queue. A queue works based on the first-in, first-out ([FIFO](https://en.wikipedia.org/wiki/FIFO_(computing_and_electronics))) principle.\n\n## Install\n\n```sh\nnpm install yocto-queue\n```\n\n## Usage\n\n```js\nimport Queue from 'yocto-queue';\n\nconst queue = new Queue();\n\nqueue.enqueue('🦄');\nqueue.enqueue('🌈');\n\nconsole.log(queue.size);\n//=> 2\n\nconsole.log(...queue);\n//=> '🦄 🌈'\n\nconsole.log(queue.dequeue());\n//=> '🦄'\n\nconsole.log(queue.dequeue());\n//=> '🌈'\n```\n\n## API\n\n### `queue = new Queue()`\n\nThe instance is an [`Iterable`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols), which means you can iterate over the queue front to back with a “for…of” loop. Using the iterator will not remove the items from the queue. If you want that, use [`drain()`](#drain) instead.\n\nYou can also use spreading to convert the queue to an array. Don't do this unless you really need to though, since it's slow.\n\n#### `.enqueue(value)`\n\nAdd a value to the queue.\n\n#### `.dequeue()`\n\nRemove the next value in the queue.\n\nReturns the removed value or `undefined` if the queue is empty.\n\n#### `.peek()`\n\nGet the next value in the queue without removing it.\n\nReturns the value or `undefined` if the queue is empty.\n\n#### `.drain()`\n\nReturns an iterator that dequeues items as you consume it.\n\nThis allows you to empty the queue while processing its items.\n\nIf you want to not remove items as you consume it, use the `Queue` object as an iterator.\n\n#### `.clear()`\n\nClear the queue.\n\n#### `.size`\n\nThe size of the queue.\n\n## Related\n\n- [quick-lru](https://github.com/sindresorhus/quick-lru) - Simple “Least Recently Used” (LRU) cache\n", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}}