{"_id": "@eslint-community/eslint-utils", "_rev": "21-1fbf201e14ab34f229cf747c5b97d97b", "name": "@eslint-community/eslint-utils", "dist-tags": {"beta": "4.0.0-beta.1", "alpha": "5.0.0-alpha.1", "latest": "4.7.0"}, "versions": {"3.0.0": {"name": "@eslint-community/eslint-utils", "version": "3.0.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@3.0.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "05965cef4509e8143ff32dafc6d8bf18b7616134", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-S+tkxFezSC2ZtlcjAC8EnnQWPWJqEnZiqC1AmCZ9uUN2ZT46T309MczkVHP0bhO2hM+aeUFKKbtruREdI59jng==", "signatures": [{"sig": "MEUCIQDgPkZ9fFsBTTE0UBFxlcxuxk4fiRKhfsDYIfqphwBQJwIgQOHmep3em2PrdXC2qnNKZ00zk5DJgebLtBJM3AesR/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjeniFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3yA//fkOjz3/oJ/c2/ChHmUDgSABqQgCk99m6CA2OWkehKGDOmnQm\r\nQOt2VASH/TYjq+/55SueVfHtjYdZVubkBt54LCFVuO7HLgqHCq7fLBENqkp7\r\nvf9ZfkjAYgfFhVnQ4a+9tB5CF80/2sz50SFqe6BKeVjrvfZ8H5q/CG3lVMFX\r\nc7vcHv8HJV+r65ZamL/PHgxO/ZpbK3Y9f8DPNxc15F6hKbmo5LQUOazQJqBr\r\n/Q+9cEzc+Wib6ar8qxUdeKnOKyHnrhwCZSOIrdLtyqYBPv/m/ka6vSWwSAPx\r\nKDSPz3vJsSrBhuWz5i/OlMTjE4W4IzqUUgDxVyRnOiXN26iWjy+9dAJINdS/\r\nN8XQG0xENBWITrg4q2y1ekXCcGlNAVA98Tzd0cpoPmyLlwgpgef64IZfICHf\r\nWn+Ne+leZwgD51UPRCGl67sRy59V/GiXJlPcUFhDD+X0dV6Cqv+OsePZFNXG\r\n1W1Z30GQ2EM0+MucWvwib01M+EbVzJWWHShySfLIJDBONrKO2IIkAeSyjT+4\r\nhDG0stTghsxLTWFCHvVVkd7KZvG5MmDMzLXGmIAK3aj/QDqRcLRuiyZkkA9d\r\nIDsEREI3Eqa5GkGg04lARGDqk7jxryqpQ485myTjw4Ow25QKtyO/uQ7XNkmf\r\nokdo+bAhCnrJBrRaqXBB3tJiIfnYKrZTSKc=\r\n=U+5s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^10.0.0 || ^12.0.0 || >= 14.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "f1b34f632fe440e165168a56d76b4883b209ed73", "scripts": {"lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "test": "run-s \"format:prettier -- --check\" lint build test:mocha", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "test:mocha": "nyc mocha --reporter dot \"test/*.js\"", "postversion": "git push && git push --tags", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.2", "dependencies": {"eslint-visitor-keys": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "mocha": "^6.2.2", "warun": "^1.0.0", "eslint": "^7.24.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.1", "rimraf": "^3.0.0", "rollup": "^1.25.0", "semver": "^7.3.2", "codecov": "^3.6.1", "dot-prop": "^4.2.0", "prettier": "~2.3.0", "vuepress": "^1.2.0", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.4.2", "@mysticatea/eslint-plugin": "^13.0.0"}, "peerDependencies": {"eslint": ">=5"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_3.0.0_1668970628824_0.36637419791676984", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "@eslint-community/eslint-utils", "version": "3.0.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@3.0.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "fd16015d6f433d7677d54edccdb7c32d1fe74c34", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-XEwk9oHZtFWcXg6frEzN/T756NcH28wVkhBzACbmv/RFnuF0LyISrUen7HeggeJFfdqFgkfT3nh8r/PxAADzrg==", "signatures": [{"sig": "MEYCIQCyySh2YlMOCv5eAISek1fxDp/6KhsOpmfRsnxtFSzphQIhAOgMHCJNeXt89o4X+jks++KeZMc7Q9tnNBT96XN22qJ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJje+J+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNFQ/6Ak8W5Jkk58eTQ7eiaL4AtD/Y1Neqky9g7mY/eZP1b7m8kKAg\r\nIgCY/irKKw0wWzGLZViNrHdS7raRvPluK+Op0CfH25wHL6cXXSP5XOwjml37\r\nD7Ba2jgm6Dw1lPCND7e4xK6fh1Kw7FxbDRljFbEFFgAgvijxoidzsvVep6B4\r\ncUgtNG5Xm606ZG3x7tzeK5deympK+8wwYI2YrpwHp/Fr9jwhP8yi8KJu1PTQ\r\nojbkWepZiSkYe5baf6PYkp3Jdhi9jf/BEOMtN+SctbqCigWKWcSoLXnO1YyM\r\nIXoPAlrm5xpxNrm/ZI2aG0W/fjzSZqOnNOilO3ZFg9auMa6ARE4/4HD9WWm3\r\nkR0QEwFP/f+kr76xZVj3JDB9BkHsLolsMVkKzGziLMK4MDs3X9X2AYB2o/qC\r\nHDH7t3nLX7f3aXuA6L26Ar938Lo6OsWm9i+5cZsvebhxVBXhlX/YgRLZ/uDP\r\n4R8KrR4yzmboDItLP0Y5DCWhCx5f5Qpchpk46N9i4ofXjhtl5zkGhPcLjx0h\r\nJZUwjE/CoXPlns0kJK/b2omF6Ab+B56yu6UPKP4Rm+f4WA6MEHHUPfHUJZnq\r\nx/E4z9jDFK8k+5afy56srN8FIbvAgW7S8zEJuSgy3OHwSNfj8JXS+cj7/j2J\r\nrCQrHuoEoJcjuT8/RaJc/BidBoG8fPQgREw=\r\n=E27U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^10.0.0 || ^12.0.0 || >= 14.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "771927523e009032e8643029bfbb0443b979b646", "scripts": {"lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "mocha": "^6.2.2", "warun": "^1.0.0", "eslint": "^7.24.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.1", "rimraf": "^3.0.0", "rollup": "^1.25.0", "semver": "^7.3.2", "dot-prop": "^4.2.0", "prettier": "~2.3.0", "vuepress": "^1.2.0", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.4.2", "@mysticatea/eslint-plugin": "^13.0.0"}, "peerDependencies": {"eslint": ">=5"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_3.0.1_1669063293723_0.3969223929339447", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.1": {"name": "@eslint-community/eslint-utils", "version": "4.0.0-alpha.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.0.0-alpha.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "23029d679ef91862014baaa867570d71233bffad", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-XhySQiVu0IYdrQvWV7DHR3ljjrBg+AdbQA7PNvlcDfltWFMJTlnrtxCHYjsgKAKi/PF2j2iiy8Dr72YtUOrlMQ==", "signatures": [{"sig": "MEQCIHq4KYoryZkNZ26FYu9mOj9BNG5ybzl3ADHIXmXh3inEAiA5SyyFsG8AoIO+9Hkn479405yEcpIyv3WpUaGMHX/60g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfDAFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/URAAi8HDPYWfDhjtzb5umDzup9JiSTf7Djqxi6TIPzayHf19+myG\r\nTR/jp5aUEoyieqhc+Yi2Gm/QCXUrw/uxEKf3IlOEiPmtrPsprThS0TMWwMQG\r\n3FvZRrUVJ695vvI/Jg4t4Nk11XSF//9AlbCq5Ih5+2f2S7nQIrnGovadXfp5\r\nJDbSc0ZsgqieRN5cOSRYRqGjknu4xWdN6iIXrzKt4V6eHiL9rIkQvfuvXxaF\r\nwBCjDXJjt0/st+t9IsRLSyxmWM9FewT+A7OX8vtSo0Wtwb7I8QZpUhN/G3Pj\r\nrIXvdkBZH6pOXwnsDVZfcetadwm8E3VwcvAqR+cynd4QoKOVwSTLdfHVpOKR\r\n3pJMVpbPllVVLKgQKT0AdGyw6Eq3qzOyHnw17+4bRlFPWQq6HFrrVKDOoKQg\r\njGaeEpJtmcc5g7Qf3TbI7u5lswuxCr95ULuLhhjVYkBIm7VKLPsd32kB6E7u\r\nRhvfRoYKS9LwAxDZAZw5F6nDCwjWQPrrFO5+At0SACa1RNY8r8/LaHdRxdqR\r\ntqjv3M3xmJQ3u4BD9sPoSWD/NVg/P6/DuCEO3OZgtzqihKcXR4/tCORoQz9E\r\nqm3m0SR8QdKiCXEhoBeQHy7BOm7xc0cCUXVUrFpSBQc8dJ6dSNC4pbvFHry/\r\ne/GmZUxbLX82XOjcER9dop5wB47TNbC7jRE=\r\n=Do4V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "e80fcab916f7918c7863946ca55490e8da378e9c", "scripts": {"lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "mocha": "^6.2.3", "warun": "^1.0.0", "eslint": "^7.32.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^1.32.1", "semver": "^7.3.8", "dot-prop": "^4.2.1", "prettier": "2.7.1", "vuepress": "^1.9.7", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@mysticatea/eslint-plugin": "^13.0.0"}, "peerDependencies": {"eslint": ">=5"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.0.0-alpha.1_1669083141496_0.6848684347580558", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.2": {"name": "@eslint-community/eslint-utils", "version": "4.0.0-alpha.2", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.0.0-alpha.2", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "f00cefb73986c5c419cf9aa86c9949bbac81eeac", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-MTfr7NiJ5lT9bSLVOuS4U35/Tj3ax37TalAKYjB85xVuhlLi9u6qAWHKyhwt4vJ7cMNjAI0JpukF+2mJHFfM6A==", "signatures": [{"sig": "MEUCIQDhDk5Z8d+X6w8fCFrzP/ivUdJf5OiyN5Kflu+9qcziYAIgRH/SwcIVVx+QbeZmpVzGs0/wnTPgm+iSnhssjlvhICU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjggJfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW8w//b3pGOHy/Y0C7e0/4DDs4rlxmIl7z4jy8JvR21dEBwCeIK3Nh\r\nBRo9p3mAQM5zBKSHym8iICRDlE2lLYnAh5SbAqBUzWjiFJ5mWJmmHoNK+b0K\r\npAbtxLzEMOCtddm3yziz/2upheDWFU2TuJDle5fwjLV5QULsB4B1KOSBns81\r\nhcwXrp7p05rzTIC+N45AFD0kiaBz4jZpnE+iMRcLB0rpBv3OUkPsO/HRlnp3\r\nLTfiaA1wfalxKyLUBxSvQAV0g1cowz8mvJUv8qaTfR0DmTJcTOlWNYOM9M6F\r\nhbBN7mDqR+wrkssfj/27UOvwkI7to4z7oYVnYjpIOWVC+22smd5ES6Ze8eAT\r\nYgN3PV9k6mPeUzz9PMs7cORjgc8OgBO+MAyirwU5D7Focnae33UDKNnGyzSq\r\ny3lMfW64/3ioGFD/lIB0PKV0VdV1PIc+31nYSELL6GF9qccwPblHCf54NaTh\r\nQvqaIQFN0vCZSVNWDJfQ1UGzOjsX8w6d5zte4s1YuwSkLt2rOJrGMoAB3u3O\r\nFb2BwHF39Nj/kitFjBWAyfoX6QlNVsL0XPzvaxzX013A/iBN0NK8rVIZ8u4v\r\neWB3llIKSeZ/WtOOa+QVph6aps6LcmjIrv4Lr6kmNYrtijEzExOMW64/r9sq\r\npJWhTD3mIXa42HwCakdpGtlR3xY/LIUnrdo=\r\n=uFBm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "5d65b26ad03e16153c12a5ba5db7bced9c4e1a67", "scripts": {"lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "mocha": "^6.2.3", "warun": "^1.0.0", "eslint": "^7.32.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^1.32.1", "semver": "^7.3.8", "dot-prop": "^4.2.1", "prettier": "2.7.1", "vuepress": "^1.9.7", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@mysticatea/eslint-plugin": "^13.0.0"}, "peerDependencies": {"eslint": ">=6"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.0.0-alpha.2_1669464670970_0.6741963471438142", "host": "s3://npm-registry-packages"}}, "4.0.0-beta.1": {"name": "@eslint-community/eslint-utils", "version": "4.0.0-beta.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.0.0-beta.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "89d8aafb3891deeb34ef6260b9008b78121177f5", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-EY799UNGwLf/RYQPv+MHKf9ucGCMR7+Jb6YNSwP3z2jXmw2/dldxBZe9peAjt2qjSqtMqL2DC5/iMGD/lbzqXA==", "signatures": [{"sig": "MEUCIQC60gxjzdTDYKDyyx7j6CFvi1ey7e0ZtaC3Ev+0NNWK5QIgZS84Y6fvPxsqDXvZsYQTPEtzleL7PI1d+sBkHQticMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgmSwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqEw/9FWXdjukxpPBlyTyYmyNyf71VwaP+DBEzz7rOl3Aj771bgsS0\r\nB2gtrRzqBuP1AwWdOfwrxdoikqKh9Hb+Wak/cAr2TvQanMxkTuVfv1FKT/+L\r\nMGAYmuEP8YXtEUz1yuJQ+f3GAWg7Gb5ugyrv082IS6IXDC9qjvWlbcHGnbM9\r\nSVuCMMG/U+kPJsTHnvTWK17ntfsPBDoTvtdR9wkGrB/dFJxfXqynDxc0OwWK\r\nk10FMaxo2SqkpU53u/NI0lll7QeGwmpcj/jfK5+XZOp8BDnqBbt3U7EvIBe3\r\nVl9b23c1jALh/0iGcF1TQEHA9ajIV8Pmp6L0IV6E+18xu1rXAooIYF4m17Rc\r\ndPX7/v+Yntw0aVzTfao1WHs4Ld7um+/EAtqQ/BrdeK/E8MOz9oCHBAhXCbms\r\nEwOrjb1oQH33SfY7AsDKbXzretlzrBBcyRfgA74bdryTaRDf27T///NICGW3\r\n5fs+7oxWAHxq53BR6f00iRwRl9WBkzYgGKePvqd8vUhU9KwFMokGju374NhG\r\nYKiENKgDbaGW+rqKQwNiAB2Z1HLOJUw1cavz0/VQA+fmoFHOPjPs9XGxPoN1\r\ndRoB2Q1qROlQxJKQYdcs75fFLYFGnk8NzxpNRpCYNdoz5oiHPzJ2qmrX8Da3\r\nJEUd3T2l1wDmjncUY291UTw6ovzXZEB6ll0=\r\n=JCIF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "ec558b57b6c6f5b885a00c19fe192579a572b75e", "scripts": {"lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "mocha": "^6.2.3", "warun": "^1.0.0", "eslint": "^7.32.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^1.32.1", "semver": "^7.3.8", "dot-prop": "^4.2.1", "prettier": "2.7.1", "vuepress": "^1.9.7", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@mysticatea/eslint-plugin": "^13.0.0"}, "peerDependencies": {"eslint": ">=6"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.0.0-beta.1_1669489840079_0.39461971494886283", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@eslint-community/eslint-utils", "version": "4.0.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.0.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "4ccb96aee900e11d41af2139e102d29622a0e109", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-QgU+nLymQpslo1qxNliC4ADlwKd1r/eCtD3BIMh3y0z/YMiX5ebyiV/x2y5NDxvFHwh4s2r2vk5bZK+RgfFxng==", "signatures": [{"sig": "MEQCIGpwmoqRQE/dohIQoeuPLP5+AxkAEy0vyMPl51qRO9vlAiBP7HNU68LKowbzS8VJ9BB91KMp9ftkGuWb2Skin2ci1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjguOXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvNQ//YgcPslotzHGCNIM4JmAKqb39KTpaYyvnNHdV9s8Wzw8/ROg0\r\nYXuZAJl3rd595YI9QHbhi/WJf9Kc3GD3TyCSyMrggZPiAU2ROEPy/WT4lbY5\r\n0uzWnivjXmd1ZRNadGUvfnvm/xmbxWxEWiL6jWsmirWInR5PXYe1NRAZIx0l\r\n9uvwc5vYFY22ky1W9RtOjJ/fE7smNAUQMajmA8T1OJ9cB4JfpN26yRdTc1/D\r\nN1Fh9Q29Sz1XqUFWP5JYEkZYd9sV3MwcWGeb1qUNZ4lwipqHXvMiIFdk6E17\r\n0Mj7hQWlb0bmbbbSH+pmrRam8L8UVOAUFZFAxDE/B6/pYS2r5ozWSAJIVOk7\r\nEeCLd0m/H5Q2fUglUsliGw6leJxcktRTKtjhwCvyh68dSdtdLxRaDchLQLp4\r\nJDkdNpjkd8fZyDBB+lAepzTc2GmyAvz4NtjD9Kwv7UbVdBjr1aMWAwuxTwQQ\r\nMLY9Z2OmO0IIuKFZ+rEw/gSP94y3rL77ezT7jt5BUqeev31gD/G3LFCxQmHX\r\nIYQhJLCyIRfKh8S2/qTTB0N1qEY+ffKsWNCtHoexOrO+JeZNMhPkcs7Mz4eY\r\nnx1bre3deNARNx3wgBjdN5CZLWkL2Yn75jvHCqZFnVvF0oUwsyLowpsxMDUT\r\nyU45h+amD+mUC6N1c7Uk8/qngVS0J819Uxc=\r\n=aqed\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "ec558b57b6c6f5b885a00c19fe192579a572b75e", "scripts": {"lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "mocha": "^6.2.3", "warun": "^1.0.0", "eslint": "^7.32.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^1.32.1", "semver": "^7.3.8", "dot-prop": "^4.2.1", "prettier": "2.7.1", "vuepress": "^1.9.7", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@mysticatea/eslint-plugin": "^13.0.0"}, "peerDependencies": {"eslint": ">=6"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.0.0_1669522327747_0.1734722309026051", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@eslint-community/eslint-utils", "version": "4.1.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.1.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "ce3a57e832bc13a7eb6df2a3b6461667be6aa8b2", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-aDz6JRRb/YgohZ3h6jU/zbuigcbQjfGNVS/vxyW48dIeFViimmhb6fYuseOtYuh1XPCuo8WB3VWKarZgZxgxOg==", "signatures": [{"sig": "MEMCHw8aHbzkTA7v6xQSFEkVmkTzitddoDar/+i3LwW1ZHECIB3Ahv/1e4AJtPOLGv1akKFeqFjYcxCaSf9dC4hDadAk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjg/QdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmplkg/+IujowUUzxlrpTG3jydR7c5IoRCMomuGtFr6TNBtROd29I1Dw\r\ntyKd3fNoMKnRVTzyRs3RN3pEeqLirjgFEdki6YJbHCuJOuqcRc/LNqXb+/09\r\nnYvehu/HWKYkqXpnQd0VbS74eYZUvDsjnvoME5YrrDvkuBsW5nwv9s0gWXkx\r\nUVs0v69Fhy0KuXsUiFzO4HMPzsak2/T0SVnh9nifsVDQVx1PrDIdkPh2ETSk\r\nhKJfXQbZ04Ap/dHnuwCgn8G5lFhCyjaEJvLs+dysuvT96xvaIuV95frlePdJ\r\nW+B7LojeL7J7X3gE8gIMYkwEzUtaGwgDC6WQzgPIMwaZ4HnmLA0hfDBgDgqd\r\nH9zqruY0HARJvPGST2d1TV3SBHyGEFCXtde3+0RZCaKBgxEdXfpxgs0SWE/i\r\ncmnuR0wV9OKOcMwICmUnETc+3V6wdxjU9qNS9qC/ROjgo0KP4qLOuKEz+kJM\r\nGYmhqmRyk9s1KqOUDsUWyQweUn4qxneQbR/35SFXsZmMNDFrfGBbXvm1uB8L\r\nHZt8eKkGdtFrvkr0ScOpfbqhLq/VV3p/Ohk7cdAKBPEu1v8GQZwQa0pFg38G\r\nIctkVcoYjwW0xmK2DZhOGL04NRNPf53zUHDEDbQQJA7U5Lbidd0kCegOQeZ0\r\nx1psU+lPrFnSkfOka6yhpFDB6c/ugJzjk/Q=\r\n=izvO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "0bd0441cf6cc27a2e8cd8d6619b9a3d6a08c33f2", "scripts": {"lint": "eslint .", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.4.0", "warun": "^1.0.0", "eslint": "^8.28.0", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^1.32.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.7.1", "vuepress": "^1.9.7", "@babel/core": "^7.20.2", "npm-run-all": "^4.1.5", "@babel/register": "^7.18.9", "rollup-plugin-sourcemaps": "^0.6.3", "@babel/plugin-transform-modules-commonjs": "^7.19.6", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.1.0_1669592092999_0.30877258257725315", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@eslint-community/eslint-utils", "version": "4.1.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.1.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "6d6cb199046a03bc96f81812a44a8d6f6f1379f8", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.1.1.tgz", "fileCount": 7, "integrity": "sha512-91DU9eITrGPPlcE49VHiIZIHo4fLS+SV/dJd5WUZqWEhA4nhtunrCtGh7ABbXVDJx9hCE+myD4V+l6ZYBWuXBg==", "signatures": [{"sig": "MEQCIEU38/NFs57pvzFK0zDp8Z936gXII/hEbMfBQtTU8fQ/AiB4LvpFixbo5FgB4pc0iBjrwY81b1PoNzw9dcos4gs9iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhIKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYPg/8Di+0tKEcjOLfsgpKe8acg6Ohrzy+YmLzS316oz18UkEuoLFd\r\ngJ/96oI7HcLlCn8B2Ajt0zC2uYGapQM5OtZWabe9e6/DHNMpvzDcj3uWj4nr\r\n0nqSYp8uGs3I4HT+1bTvGZy9au2LUi8oh0VOSjmjcl3PSL8ARYOqcXxBtm9S\r\n3dQkOshX3GI747Taiwdfo1lELz8YdxxFHBfSE+RcNhzX0Vzs5o8ucjoJQoOs\r\nQq7cT/fTFJY1L1ElCn2lq1zfi0SKbjhRa0dQgPMxEEzuwZ47qjFsjzWQCv8+\r\n+TB2w21/iACoyrA9wSGOBvcvCqC4X5AVAFn3qQx0PKBFhnanQht/Pir40Bzg\r\n8iGKJ9n7JKHLnzdtfR+1kB/NPwgzUilXvAX2+5bhohgPsJW9bj9R6xB3WChs\r\nM6dKzZx6VFEzoaMZLZlHabwNLDhvsVsbwEA8wdvfSk2WnmHTeLohjgNs7PxG\r\nfXOSOCVH1+SnTxtRhSQR0m3vEtu8oiK33GhFiMfzErAigyYUk6NFH8QLlu2U\r\noMrU8cDp5I2xXyz6YEbHvR+MtcdG3IVpCPREBJWVIOUs1UvvAMviDVIXjU3b\r\n/nur06LwVr4b7cX66mqPJLuQVIBZKUkBflK8xRX/55IuZ3FyUFrQRWJqdbmC\r\nvTObiEW2kk+LR6sRvgigmKViGO6W2Zj1yfA=\r\n=Cb8O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "9be40ab5c41be36a235da15bbe594007280b6b0f", "scripts": {"lint": "eslint .", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.28.0", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.8.0", "vuepress": "^1.9.7", "@babel/core": "^7.20.2", "npm-run-all": "^4.1.5", "@babel/register": "^7.18.9", "rollup-plugin-sourcemaps": "^0.6.3", "@babel/plugin-transform-modules-commonjs": "^7.19.6", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.1.1_1669628544168_0.7694584976706649", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "@eslint-community/eslint-utils", "version": "4.1.2", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.1.2", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "14ca568ddaa291dd19a4a54498badc18c6cfab78", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.1.2.tgz", "fileCount": 7, "integrity": "sha512-7qELuQWWjVDdVsFQ5+beUl+KPczrEDA7S3zM4QUd/bJl7oXgsmpXaEVqrRTnOBqenOV4rWf2kVZk2Ot085zPWA==", "signatures": [{"sig": "MEQCIEpFLxgrwXLyVEMQ/6Zh5yr8YKZIWhBImhM2Ycjl+Un4AiATGmbKvcfL2QQk/QjjtD8uQyc0f3m8/LoxT/4qz64Bow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhIRfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9XRAAkZsaeI4JhkiWuNb0g23KEpTif+5tbd3vlIoMnm5zDtpJe9NW\r\ns/i9j47tyBRCU36Xc9afcRDSXWULo+DkRIYw8ZQrblcg8L8qQkBhNGaNSuES\r\nHn1Q2ybc2z2lrCoMIURFcLrWkyOBNTL0cxpvlMKlqfOtQCdKqgrsLa3tdGZg\r\n7JifAjeeVzmGUiwapvQX5DjcaBrIV2uvcAgbtdGgOs7elG5z9++I0EdXj7s7\r\nPBY1SshaDRzsT/fb1+ssXWtSYcnjvzH/S2dEDi8VCwco0c98FrJ8zJe2/leK\r\nXsR0xyLtgO2KKwTBLM7yNQjIiSCCKE+PDcbz5Xv5zN9tZ7/yATivUFdyf/pm\r\no2wlBxeeOzB8IhsOekybGWCcOxC9vs0qraCNr1S0ze9LMxvVtxH1MC/Ulr+o\r\niJLe7mnBR2hPmSvWgitCcklZmxjyMeNpCsy/mwQv99XbLPupqtIP3Uzehik7\r\n7nEJrlHCi81GdPYVvHrmAFcySTXxNuxWTrVOmPhVWe4cftca/683byZs/4sJ\r\neOeRr6kz3Pe91cEJpo50ArhF4L7rNUgUZdOizQOMZtO5g1ZQ9WrLEqTNIk2V\r\ntxjfniqzDlTiDnsoGEZSb1WFejSWPAnKJ667ykYy+LVOH7frf7qEVO5ot3FJ\r\n2dmWsTGqFyQHgxqwFxIsowFhnXmrFVNnFcA=\r\n=mi5X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "f53ab0632bec11b97a4d5bb3ca438f1df03962bb", "scripts": {"lint": "eslint .", "test": "nyc mocha --reporter dot \"test/*.js\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.28.0", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.8.0", "vuepress": "^1.9.7", "@babel/core": "^7.20.2", "npm-run-all": "^4.1.5", "@babel/register": "^7.18.9", "rollup-plugin-sourcemaps": "^0.6.3", "@babel/plugin-transform-modules-commonjs": "^7.19.6", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.1.2_1669629023024_0.2432947381476751", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@eslint-community/eslint-utils", "version": "4.2.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.2.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "a831e6e468b4b2b5ae42bf658bea015bf10bc518", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.2.0.tgz", "fileCount": 7, "integrity": "sha512-gB8T4H4DEfX2IV9zGDJPOBgP1e/DbfCPDTtEqUMckpvzS1OYtva8JdFYBqMwYk7xAQ429WGF/UPqn8uQ//h2vQ==", "signatures": [{"sig": "MEQCIBQpQWZ43ucBEG2gI8Pdlq52yB/idePctWHUUhBIOz/RAiAwGsvDsxh8jWd9z89pZ85hPHMO0KtUyD+K7b06lAyE3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAbfNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLMw/+LXAo19sUrkrBbdjsE9s9Z+x0W4DBYbCgrJDYSDdn0ib4969h\r\nbBw5JS/vnpYSupcAhjhiJrl6UblOuYf2To/y/RpWwv2DU5GWTtetMguT8bAP\r\nHusrzEjppJVcND6/BEkiLX0qcYEs9XBok8KxU8DRD2nwwSfAafie5S1bXbbU\r\ny/llX6MoYuyG/QN9ejSCCdE/sY1V3R1/kZwozNzhg8foDwITNHxJJ+89Ri5V\r\neP5hsFc4lPgeKOEVExNCFEw2NhIo06sMNu+8GCLu80eOC3baBUsO9wuuJK/B\r\nlgaqjDruobnj0Z1tz0obXe2Z499SemkOtWjhZqLIHcfq87V83DWFZ/zaAcp8\r\natr3XhP3786LbH5muuYPZizuShlIWFgQEGm6J3hCzVbjuSkex8T4o+e+xSYV\r\n6T4w+M3o/JkGFnIpP/K3FupNYqvXptTuiAoLDQWzkh6GFk9HZedAmpp+++i+\r\nhggB35nAR+7je4iGCVETj0V017iXGWjdl+aIaVfEEt2b1THz9inpJMJk0u0x\r\nhcUbI9HsPiG3uNkG27Nj1ZtXhh6BZmuRMbcEFRV4GfztXtlxzP9wi8Ks2pM0\r\nBIF5MUyKeCHn6nVuem6cP2fbZxeaepJTncMnyBGfH7v52adxQDooEMJtRMxT\r\nGdTEtSWjAelPIqt2HjPbbHwEZTjOjo7M6XE=\r\n=mvpx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "57693bec88e12f729492d1ed0f7515cfda69ad99", "scripts": {"lint": "eslint .", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.28.0", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.8.4", "vitepress": "^1.0.0-alpha.40", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.2.0_1677834189172_0.19488236971079576", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "@eslint-community/eslint-utils", "version": "4.2.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.2.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "dcbff07808d38b4852fb5a5fe6ddf9184b65c307", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.2.1.tgz", "fileCount": 7, "integrity": "sha512-494d1hNrghSUUYuPYcNvKZewMwSMEDaOdAq/Q0HbkAaLuElBYkz/1xFyR2SqKpMsrb7C7MtXaOLX3H8J5MKTug==", "signatures": [{"sig": "MEUCIETm3EruT10cMRugxV5AmvUg1hy4h79eYzBGs74sxoyyAiEAm8j3+VyGbxsqOwSIyfaCrioYovUIcPxFT0ba3gxJIpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkE8zrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjRA//cq2nZvY3ChvqcPuimCIczfran9Vjnq/eBI8rQjszNMeKgNw5\r\n++Qn7GiOsikH7mlcQzw5HEnHHURG47FfVxCFq3XKN2QTOogefvNVtihqo8+4\r\nuPBY/9qKEXMi9Nl2VRVb97X0WPuKppKPoG8puzmNxy5xAFTYoGA2xw5S/s4g\r\nnQhaeC3KluoXPTYujidS6lj5c8g9qQTS8NnqLlO1t/inpLiNoelKwhEd1+co\r\nPcnoD8WLCsfCUgB/gF9A1m2yZE1Ad8saBImJXyK2Fr92VHXt1860ydeSYZXX\r\nJyIqTJXiHHpwErUrgcIwOa3U4rq2OaAHiQ3dCcWxTVymHBzklHgHgkEhb5wH\r\nc+eLm+WtCjtBOSjqViKUdnWPaDRL8kVEUgRmWwH7b01mvKISM7kgojormIKv\r\nfkcuhO8a/L/6OtaFRE2xK0oSdNODDUTIrymGzQJydCQuC6TkIey0eNf+//g5\r\njqpbLbiHn9H79lE2hJR5YP8LB1AMXXZM6ZSNosjGuj+e2tFJrm/Nc7YUcmWd\r\n5cOkzY/ScpyhzV3C2qJqsPlsxT2kMgf3pL44xN+Q1h0yQSc9tNZMR6Am0S6F\r\nNQOLJHnRXOjvP/jB0BTfNhsqsg5ywyL/RbXdcEkze0tGvKyr3/ukHF9l/NVq\r\nVfrG5Zeu1FfYFhqFSqjYs1pwpW+Ew0zxLAc=\r\n=F2oe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "5b1d059231d2c3dbeaf47a53a45ccc80a1ccb4eb", "scripts": {"lint": "eslint .", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.28.0", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.8.4", "vitepress": "^1.0.0-alpha.40", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.2.1_1679019242870_0.0046186266229228945", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@eslint-community/eslint-utils", "version": "4.3.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.3.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "a556790523a351b4e47e9d385f47265eaaf9780a", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.3.0.tgz", "fileCount": 7, "integrity": "sha512-v3oplH6FYCULtFuCeqyuTd9D2WKO937Dxdq+GmHOLL72TTRriLxz2VLlNfkZRsvj6PKnOPAtuT6dwrs/pA5DvA==", "signatures": [{"sig": "MEYCIQCSUKlJo2qLI4bRcIH8y6/pvxSTpTMibcAKtACrNKW+SgIhAJeDHuJ0xp8NSoL3prGnkCJF9djgfsX0rnpymZyfORbq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkE9FoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroMRAAontVQd/HP0CSCNC0/lpgSqgpkYAgqY8yWX0AVWH0A03qApcX\r\nCzIJwsQVC0zObvrhvLLeLO0lEtD8JhomSfoludq5E2vS0sn3lN96Ct/Sees9\r\n6UnmnqnA8YS8bVYYIw0xgGDWNnG123tbj0rWz4Thv1znS3rkzBexG3nETgc5\r\nonASQrP3Qw/myYnM6tImWdlQUmQXTx7dDRLxySuN7gtNRTrfrUWg3iFgkBlz\r\nuCm5Hwgbd+6ybnDzBh8V0kS0pgMx7hHW0pMvcGt4C8Lv22+vpEII1ZEsRtzM\r\nVhL4udgTNxuLnUF+si2GwBkkxWtDjIaB6bqMXg6aj9NNqYinakyi17oLXXSA\r\ndAvZOXgkuvuOcMDNNr9Bm0cWoFq93t63kqqOG+o/0QqF/X6EjzX2tzMQxZZ2\r\nzCXHY8Oem2UsRJEvPdDV4fPHcVsOcuM6ODFcIhkDvgyIfeKEWEQguSPVnBKR\r\nEmbv7cDcHOCZzHBKWCeYY5WRtlhuLVpujGx82SDMnVHK4C8gElOjAVM36zok\r\nhfhpjsALA/Wa/RTOmd3FpEi7ZGXlee1EbSzz23L974N7hADmJsFpHbaH1Q4r\r\nHaCqQ6fZTn013cLLikahrJ1tHGsc/J9a5Fz8wy9ihgdc6dMEmLF0JJvg5v80\r\nJgCSBasxdSLn+c4O03Zvu1WNxnjLF3dyALc=\r\n=trJW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "80b934e8a98cb5222848b2f671409baab71052ff", "scripts": {"lint": "eslint .", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.28.0", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.8.4", "vitepress": "^1.0.0-alpha.40", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.3.0_1679020392004_0.44951593278412827", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@eslint-community/eslint-utils", "version": "4.4.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.4.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "a23514e8fb9af1269d5f7788aa556798d61c6b59", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz", "fileCount": 7, "integrity": "sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==", "signatures": [{"sig": "MEQCIEQJ04YIVJD+YnwsGPVs74xAjp33dsqZNhHqaE5H5yRmAiAoDme9EWkVgi8ogHxBGKhvmwSvrUfHwwvCQ915/5B99g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHRywACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBbQ/9Hnttk6zjSabZSBFPD2GSXQu2KNSyCt6ALyDudUyaaJIl//yV\r\nosi73fAPiycSOv9NYfGMzb54l8fQ/Bkf6ZtQGISZMI8LfNljIbM4x+eem2DT\r\nP8KqPk7v+18kkMFUGRUf2xWvfOI050PW35LX+FV5wtFuHJyPTngHfQAHhHrT\r\n9kaltkVc+G96TB6jTfO0bJWHv1UdorsfxOvqg26hfMOjZuXbEGChrKYuaBwP\r\nqoJestSuP2bNRa88WdXY/bp9vlGo5t4UrzplyYZKRh/K/Z6wz9KUIbY7pTBm\r\nPJ+dkI856hq4trAZpgVpDrQVkw2s2hf2Rt03XoILwKraKPe16IKlfR9IGs1J\r\nBtfB5YRAIHoN+nsAt24cyuBhdxVvuO3zZhHZrL8MKA9MUOllqmYtND6bMvfa\r\nS3HeGYTaQGylBH5yyHbXGcDMzfv+GVyGARHWgMWnNy/CEV5qUNv1cJf5kMXV\r\nBRPVqUg4iv/tfWwloFUjM+M/Qrq8g0TYZ+Jj+UwUB2YU8E126xpBwkM+0A6r\r\nDCnpguFDpEL0DXhy1ds3oLpmeJsDcTsaUnHf9nvhIom1u4lVE286BbhM5HsQ\r\ndDxxsUJAOsaucCna8DR5F5wHvwucpuA4M9rGUrK0cQ0DqRyp4Wt3Oe8MLD+7\r\nNs+VbZRDMQHSXxJPI2h0h5tFoYcac+WNzI4=\r\n=kr2/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "0540eb3435a80cce4a02c663833d800812dcff51", "scripts": {"lint": "eslint .", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier ."}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.28.0", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "semver": "^7.3.8", "dot-prop": "^6.0.1", "prettier": "2.8.4", "vitepress": "^1.0.0-alpha.40", "npm-run-all": "^4.1.5", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.2.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.4.0_1679629487786_0.7981882054168365", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@eslint-community/eslint-utils", "version": "4.4.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.4.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "d1145bf2c20132d6400495d6df4bf59362fd9d56", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.1.tgz", "fileCount": 7, "integrity": "sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==", "signatures": [{"sig": "MEYCIQCXenW3AjKMse5+95HBdqL2amTWU6IQbhPdgqCpBc7lwQIhAKNhYwdO5u7jdB62b/LPdx5BpyLOd4+RxfrwcuKWUAPe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@4.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 378162}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "fd0f08515e522a37c85c28a1e99cb6a2277ac1fa", "scripts": {"lint": "run-p lint:*", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "lint:knip": "knip", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.57.1", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.2", "semver": "^7.6.3", "dot-prop": "^7.2.0", "prettier": "2.8.8", "vitepress": "^1.4.1", "npm-run-all2": "^6.2.3", "installed-check": "^8.0.1", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.6.1"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.4.1_1729899328344_0.2073938233116175", "host": "s3://npm-registry-packages"}}, "5.0.0-alpha.1": {"name": "@eslint-community/eslint-utils", "version": "5.0.0-alpha.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@5.0.0-alpha.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "6689e7079273ae601c0bb3a29a51dc4a9cc818e4", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-5.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-c<PERSON>+ATZklWamMX7fjCzE0X2OUK6eUB2SOiirUOn/lTwVDMc+5LNqb8y0cRWv0fA9xslOMgh7DTaAIvZevpzFHQ==", "signatures": [{"sig": "MEUCIQDLPLWsBuuWSYamXPRIbIicaoJfwysblK4CMSfflQfctQIgHT1EsnKcINILD/e35PXC9UgotG8L+2uxVBPujEqJxQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@5.0.0-alpha.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 378159}, "main": "index", "module": "index.mjs", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "71785565b1e4669699d64c4f9bad4113a412af81", "scripts": {"lint": "run-p lint:*", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "lint:knip": "knip", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.57.1", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.2", "semver": "^7.6.3", "dot-prop": "^7.2.0", "prettier": "2.8.8", "vitepress": "^1.4.1", "npm-run-all2": "^6.2.3", "installed-check": "^8.0.1", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.6.1"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_5.0.0-alpha.1_1729912075804_0.7503444907445802", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@eslint-community/eslint-utils", "version": "4.5.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.5.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "716637f508a8add5814cc64c56e58cce57bdbe93", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.5.0.tgz", "fileCount": 7, "integrity": "sha512-RoV8Xs9eNwiDvhv7M+xcL4PWyRyIXRY/FLp3buU4h1EYfdF7unWUy3dOjPqb3C7rMUewIcqwW850PgS8h1o1yg==", "signatures": [{"sig": "MEUCIHNEoYYcHKYNPd7hOVVY1jhRZIgdqulh8Zy1cl8sobJOAiEA92k+BTUW932Ffw78U9/6indx0uy/BO5pnbO+VbS0SDg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@4.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 380386}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "324b445755f212b74dcce02fd91dea484a6e1664", "scripts": {"lint": "run-p lint:*", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "lint:knip": "knip", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.57.1", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.2", "semver": "^7.6.3", "dot-prop": "^7.2.0", "prettier": "2.8.8", "vitepress": "^1.4.1", "npm-run-all2": "^6.2.3", "installed-check": "^8.0.1", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.6.1"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.5.0_1741659761034_0.11372054476154414", "host": "s3://npm-registry-packages-npm-production"}}, "4.5.1": {"name": "@eslint-community/eslint-utils", "version": "4.5.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.5.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "b0fc7e06d0c94f801537fd4237edc2706d3b8e4c", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.5.1.tgz", "fileCount": 7, "integrity": "sha512-soEIOALTfTK6EjmKMMoLugwaP0rzkad90iIWd1hMO9ARkSAyjfMfkRRhLvD5qH7vvM0Cg72pieUfR6yh6XxC4w==", "signatures": [{"sig": "MEUCIQC0g+bFyUTCUwbW4lgKBOF4oaRnxTHD4In8Tmxxiau5AwIgboE249JQLaLNEI5Vna4oRK0tySpDd6UM4J9D1N9vhmE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@4.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 381246}, "main": "index", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "f3bc7a43a91e4016e4751b0a2ca9801b0588f1aa", "scripts": {"lint": "run-p lint:*", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "lint:knip": "knip", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "postversion": "git push && git push --tags", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.57.1", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.2", "semver": "^7.6.3", "dot-prop": "^7.2.0", "prettier": "2.8.8", "vitepress": "^1.4.1", "npm-run-all2": "^6.2.3", "installed-check": "^8.0.1", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.6.1"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.5.1_1741867820353_0.14644444740005946", "host": "s3://npm-registry-packages-npm-production"}}, "4.6.0": {"name": "@eslint-community/eslint-utils", "version": "4.6.0", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.6.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "bfe67b3d334a8579a35e48fe240dc0638d1bcd91", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.6.0.tgz", "fileCount": 9, "integrity": "sha512-WhCn7Z7TauhBtmzhvKpoQs0Wwb/kBcy4CwpuI0/eEIr2Lx2auxmulAzLr91wVZJaz47iUZdkXOK7WlAfxGKCnA==", "signatures": [{"sig": "MEUCIGK7vrBw2ll2lbytYmxWPrQ79lYVQ084c3dfeO4+7uZvAiEAjE4wu8/0dQTo74Kk4/IyYJXpSFfZzUd7xqP+SeJy+eo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@4.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 476797}, "main": "index", "types": "./index.d.ts", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "d84fa5504deadd81ccdbff09e6339fb29168a251", "scripts": {"lint": "run-p lint:*", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "npm run build:dts && npm run build:rollup", "clean": "rimraf .nyc_output coverage index.* dist", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "tsc -p tsconfig.build.json", "lint:knip": "knip", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "postversion": "git push && git push --tags", "build:rollup": "rollup -c", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip -i rollup-plugin-dts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.57.1", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.2", "semver": "^7.6.3", "dot-prop": "^7.2.0", "prettier": "2.8.8", "vitepress": "^1.4.1", "typescript": "^4.9.5", "npm-run-all2": "^6.2.3", "@types/eslint": "^9.6.1", "@types/estree": "^1.0.7", "installed-check": "^8.0.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.6.1"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.6.0_1744466464802_0.204048992243564", "host": "s3://npm-registry-packages-npm-production"}}, "4.6.1": {"name": "@eslint-community/eslint-utils", "version": "4.6.1", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/eslint-utils@4.6.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "dist": {"shasum": "e4c58fdcf0696e7a5f19c30201ed43123ab15abc", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.6.1.tgz", "fileCount": 9, "integrity": "sha512-KTsJMmobmbrFLe3LDh0PC2FXpcSYJt/MLjlkh/9LEnmKYLSYmT/0EW9JWANjeoemiuZrmogti0tW5Ch+qNUYDw==", "signatures": [{"sig": "MEYCIQCXIJdgLHFQt8YSYCD4CPHafcvDnHkc9RgL62YT9M3LygIhANoLDFzKvbRR+Z5fcc952jWbygHHoFgsHMwcSo6c4RrO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@4.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 476789}, "main": "index", "types": "./index.d.ts", "module": "index.mjs", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "9c1dbf8b89a8e6c01202d3426c70c0caaa243ef5", "scripts": {"lint": "run-p lint:*", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "build": "npm run build:dts && npm run build:rollup", "clean": "rimraf .nyc_output coverage index.* dist", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha", "format": "npm run -s format:prettier -- --write", "coverage": "opener ./coverage/lcov-report/index.html", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "tsc -p tsconfig.build.json", "lint:knip": "knip", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "preversion": "npm test && npm run -s build", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "postversion": "git push && git push --tags", "build:rollup": "rollup -c", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip -i rollup-plugin-dts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/eslint-utils.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for ESLint plugins.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "warun": "^1.0.0", "eslint": "^8.57.1", "opener": "^1.5.2", "rimraf": "^3.0.2", "rollup": "^2.79.2", "semver": "^7.6.3", "dot-prop": "^7.2.0", "prettier": "2.8.8", "vitepress": "^1.4.1", "typescript": "^4.9.5", "npm-run-all2": "^6.2.3", "@types/eslint": "^9.6.1", "@types/estree": "^1.0.7", "installed-check": "^8.0.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-sourcemaps": "^0.6.3", "@eslint-community/eslint-plugin-mysticatea": "^15.6.1"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-utils_4.6.1_1744806823644_0.9635382622988584", "host": "s3://npm-registry-packages-npm-production"}}, "4.7.0": {"name": "@eslint-community/eslint-utils", "version": "4.7.0", "description": "Utilities for ESLint plugins.", "keywords": ["eslint"], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eslint-community/eslint-utils.git"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>"}, "sideEffects": false, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "main": "index", "module": "index.mjs", "scripts": {"prebuild": "npm run -s clean", "build": "npm run build:dts && npm run build:rollup", "build:dts": "tsc -p tsconfig.build.json", "build:rollup": "rollup -c", "clean": "rimraf .nyc_output coverage index.* dist", "coverage": "opener ./coverage/lcov-report/index.html", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "format": "npm run -s format:prettier -- --write", "format:prettier": "prettier .", "format:check": "npm run -s format:prettier -- --check", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip -i rollup-plugin-dts", "lint:knip": "knip", "lint": "run-p lint:*", "test-coverage": "c8 mocha --reporter dot \"test/*.mjs\"", "test": "mocha --reporter dot \"test/*.mjs\"", "preversion": "npm run test-coverage && npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha"}, "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "devDependencies": {"@eslint-community/eslint-plugin-mysticatea": "^15.6.1", "@types/eslint": "^9.6.1", "@types/estree": "^1.0.7", "@typescript-eslint/parser": "^5.62.0", "@typescript-eslint/types": "^5.62.0", "c8": "^8.0.1", "dot-prop": "^7.2.0", "eslint": "^8.57.1", "installed-check": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "npm-run-all2": "^6.2.3", "opener": "^1.5.2", "prettier": "2.8.8", "rimraf": "^3.0.2", "rollup": "^2.79.2", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-sourcemaps": "^0.6.3", "semver": "^7.6.3", "typescript": "^4.9.5", "vitepress": "^1.4.1", "warun": "^1.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint", "_id": "@eslint-community/eslint-utils@4.7.0", "gitHead": "38c229b5ceccfedad80a6ec539b621dacfbbfcef", "types": "./index.d.ts", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==", "shasum": "607084630c6c033992a082de6e6fbc1a8b52175a", "tarball": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "fileCount": 9, "unpackedSize": 482928, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2feslint-utils@4.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDXUIhUxEcV5rngL8p80A2Tm03WZZdc6prW6gEiEiKtVQIhANHu+CLlWF6bwgjCU49qrN7yz5KesKixdSP6AAS+Z4YD"}]}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-utils_4.7.0_1746172334133_0.6787401435671212"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-11-20T18:57:08.771Z", "modified": "2025-05-02T07:52:14.764Z", "3.0.0": "2022-11-20T18:57:08.989Z", "3.0.1": "2022-11-21T20:41:33.971Z", "4.0.0-alpha.1": "2022-11-22T02:12:21.659Z", "4.0.0-alpha.2": "2022-11-26T12:11:11.154Z", "4.0.0-beta.1": "2022-11-26T19:10:40.289Z", "4.0.0": "2022-11-27T04:12:07.923Z", "4.1.0": "2022-11-27T23:34:53.236Z", "4.1.1": "2022-11-28T09:42:24.367Z", "4.1.2": "2022-11-28T09:50:23.243Z", "4.2.0": "2023-03-03T09:03:09.414Z", "4.2.1": "2023-03-17T02:14:03.084Z", "4.3.0": "2023-03-17T02:33:12.192Z", "4.4.0": "2023-03-24T03:44:48.001Z", "4.4.1": "2024-10-25T23:35:28.612Z", "5.0.0-alpha.1": "2024-10-26T03:07:56.003Z", "4.5.0": "2025-03-11T02:22:41.221Z", "4.5.1": "2025-03-13T12:10:20.556Z", "4.6.0": "2025-04-12T14:01:05.011Z", "4.6.1": "2025-04-16T12:33:43.814Z", "4.7.0": "2025-05-02T07:52:14.328Z"}, "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/eslint-community/eslint-utils#readme", "keywords": ["eslint"], "repository": {"type": "git", "url": "git+https://github.com/eslint-community/eslint-utils.git"}, "description": "Utilities for ESLint plugins.", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# @eslint-community/eslint-utils\n\n[![npm version](https://img.shields.io/npm/v/@eslint-community/eslint-utils.svg)](https://www.npmjs.com/package/@eslint-community/eslint-utils)\n[![Downloads/month](https://img.shields.io/npm/dm/@eslint-community/eslint-utils.svg)](http://www.npmtrends.com/@eslint-community/eslint-utils)\n[![Build Status](https://github.com/eslint-community/eslint-utils/workflows/CI/badge.svg)](https://github.com/eslint-community/eslint-utils/actions)\n[![Coverage Status](https://codecov.io/gh/eslint-community/eslint-utils/branch/main/graph/badge.svg)](https://codecov.io/gh/eslint-community/eslint-utils)\n\n## 🏁 Goal\n\nThis package provides utility functions and classes for make ESLint custom rules.\n\nFor examples:\n\n-   [`getStaticValue`](https://eslint-community.github.io/eslint-utils/api/ast-utils.html#getstaticvalue) evaluates static value on AST.\n-   [`ReferenceTracker`](https://eslint-community.github.io/eslint-utils/api/scope-utils.html#referencetracker-class) checks the members of modules/globals as handling assignments and destructuring.\n\n## 📖 Usage\n\nSee [documentation](https://eslint-community.github.io/eslint-utils).\n\n## 📰 Changelog\n\nSee [releases](https://github.com/eslint-community/eslint-utils/releases).\n\n## ❤️ Contributing\n\nWelcome contributing!\n\nPlease use GitHub's Issues/PRs.\n\n### Development Tools\n\n-   `npm run test-coverage` runs tests and measures coverage.\n-   `npm run clean` removes the coverage result of `npm run test-coverage` command.\n-   `npm run coverage` shows the coverage result of the last `npm run test-coverage` command.\n-   `npm run lint` runs ESLint.\n-   `npm run watch` runs tests on each file change.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}