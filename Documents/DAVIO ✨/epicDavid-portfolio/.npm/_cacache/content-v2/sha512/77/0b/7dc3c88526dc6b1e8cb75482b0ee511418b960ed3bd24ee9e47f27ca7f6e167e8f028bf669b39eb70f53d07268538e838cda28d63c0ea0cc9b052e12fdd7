{"_id": "callsites", "_rev": "21-3d9ad942ea50ae35202e7c71e5b7cd52", "name": "callsites", "description": "Get callsites from the V8 stack trace API", "dist-tags": {"latest": "4.2.0"}, "versions": {"0.1.0": {"name": "callsites", "version": "0.1.0", "keywords": ["callsites", "callsite", "v8", "stacktrace", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "2b856278a6948e4afe55f2600d05f89bd88b492a", "tarball": "https://registry.npmjs.org/callsites/-/callsites-0.1.0.tgz", "integrity": "sha512-19Ba2Cpozc/sEy3Gi6jfmSfisu6tzpFkDotpvSQX/BcRWUhY/h1K5e16QJ4Z1+/C2SBs0LnTQLCNjn3ZtetJWA==", "signatures": [{"sig": "MEYCIQDMyltBRpAFi0gnQtPh5MuQdGywei2RKioOEH0xbydKZwIhAOyL0mA3Kd14uX/mvrBsrS3MtWm0V37hRWXb/HI/ljFQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/callsites", "type": "git"}, "_npmVersion": "1.4.6", "description": "Get callsites from the V8 stack trace API", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.2.0": {"name": "callsites", "version": "0.2.0", "keywords": ["callsites", "callsite", "v8", "stacktrace", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "afab96262910a7f33c19a5775825c69f34e350ca", "tarball": "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz", "integrity": "sha512-Zv4Dns9IbXXmPkgRRUjAaJQgfN4xX5p6+RQFhWUqscdvvK2xK/ZL8b3IXIJsj+4sD+f24NwnWy2BY8AJ82JB0A==", "signatures": [{"sig": "MEYCIQDWPv30DVaYziF2Q/6C6QopJpeoQdbhPTa6i+iZ6eu1iwIhAMW39ElYNuO0pPqaH6n7hfSExhOGz19FJO58eXST54Se", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/callsites", "type": "git"}, "_npmVersion": "1.4.6", "description": "Get callsites from the V8 stack trace API", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.0": {"name": "callsites", "version": "1.0.0", "keywords": ["callsites", "callsite", "v8", "stacktrace", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "02563b10ed31f2ebd95893a776f0b3d95a6b15b8", "tarball": "https://registry.npmjs.org/callsites/-/callsites-1.0.0.tgz", "integrity": "sha512-4+LhLI+oT2ICgQn/ExNzvoCNNtO/fSxrWbOdG2DIP4YIn68jBjaeZ53amhToAh/ScYsvd5q7hfOXRbg4mI6eMQ==", "signatures": [{"sig": "MEUCIBXNduftaujmGLZ0Zc2D7eurggP/NIME4QgVM8utrkiDAiEA1WAKNE+tD6IEBZwWC95gYKLYJlHslFe+y9xGauENeZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "02563b10ed31f2ebd95893a776f0b3d95a6b15b8", "engines": {"node": ">=0.10.0"}, "gitHead": "a5f41b0b95a6b8763cb392def8e07e7a88670b02", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/callsites", "type": "git"}, "_npmVersion": "1.4.14", "description": "Get callsites from the V8 stack trace API", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.1": {"name": "callsites", "version": "1.0.1", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "c14c24188ce8e1d6a030b4c3c942e6ba895b6a1a", "tarball": "https://registry.npmjs.org/callsites/-/callsites-1.0.1.tgz", "integrity": "sha512-4pzUzADrTwH4QwhsYEY1yQYUYXb4Lp3CB7FVknTNtWNCFdcQkRV4ICEGnMQ35DcJhCVlAB1VD3A/SoWC0O2kMg==", "signatures": [{"sig": "MEYCIQC+SBNAxXDsHzZFAWsOb3u3pQyunH37djmVwMbSQnxgUAIhAOpWCs3Y0anBY2oL3hu5XBdjOd2prSgaw8Q9Tx7qwG3p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "c14c24188ce8e1d6a030b4c3c942e6ba895b6a1a", "engines": {"node": ">=0.10"}, "gitHead": "c18e4ce8ce38fb0e628f9a423471c9bac991e42a", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/callsites.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Get callsites from the V8 stack trace API", "directories": {}, "_nodeVersion": "4.4.2", "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/callsites-1.0.1.tgz_1462973676632_0.5280761285685003", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "callsites", "version": "2.0.0", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "06eb84f00eea413da86affefacbffb36093b3c50", "tarball": "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz", "integrity": "sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ==", "signatures": [{"sig": "MEQCIAxSF/VDKnUCFthb7m/IoDHPmKCy4o+HzhHUje5Ms79pAiBd+a7PmgGZzDymLo2LndXyPqGTNQkuz/Mu+gjcDjQU+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "06eb84f00eea413da86affefacbffb36093b3c50", "engines": {"node": ">=4"}, "gitHead": "76081d07c93d0ec3657d2be3c300bb21f45cf9db", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/callsites.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Get callsites from the V8 stack trace API", "directories": {}, "_nodeVersion": "4.5.0", "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/callsites-2.0.0.tgz_1472344208744_0.7568928482942283", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "callsites", "version": "3.0.0", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "fb7eb569b72ad7a45812f93fd9430a3e410b3dd3", "tarball": "https://registry.npmjs.org/callsites/-/callsites-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-tWnkwu9YEq2uzlBDI4RcLn8jrFvF9AOi8PxDNU3hZZjJcjkcRAq3vCI+vZcg1SuxISDYe86k9VZFwAxDiJGoAw==", "signatures": [{"sig": "MEUCIF5rO4FG1CqlNhVdwWY7cBUVzABbmd5bRix5R7suqQUEAiEA006kMuh6PHXVHniQc5DL/kPIPUb9uoQjpzW2GcPhhnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8vSxCRA9TVsSAnZWagAAKAQP/0bfgzITyqauvJhicDT4\nvtmBngqgOfnQOg8wFQRh2iSr4LVuJk+pYPZf1w7ocKh9W2uLklsEzeqZ0z+j\ncMpMbchlIlnwFCV0ki/26QecxkdDrSYBqdyMvWDOSjNah7WrjTGSobUI8Y1V\n6q+0i7mTCpd5KB6b7oYrvH3CWyewxSLDNEE/4PDlMUjfn1/JVhOUBKr0heIJ\nj4HHiUTOd/SI3bgNezusuMAsrFd6UOu3Xc+vC7qvNVSCd2jedbx5nYQZZguy\n57piqcPtLc4fsyV0KBxVtHqhAg8t5mHLgT3lmOMPM1P6EYCkrcBTRl2DNhGG\nNoCfk5rnKwTF+c47/YusbUZuenFcjMRoAe6zAifylHjh3jp/McHsPqlYJfXL\nJtiVMhE+NbdJyAFqlzaaPRFo1q4py+gqcfP6hFop808K03fyMQmgAynhPnXg\noM1eygHnGiQ/o7mMCH7YbLyEILXegKNG5hsuq50oAg1IGCdx2PWgIFcfnr58\nsomuOfpfDhpaHm5WKRiwlSBu1jYfcbKbNijHUUck1G+48jSe1NCV10n+T7xD\nhYk2/TY6tyl00CiTHCG8UGfvkIIEKiCAu0SNrmtlLwfGXnCGRLWlpuL05wpT\nRw3DjmYgxvILyRYGRjhHN1yi3ywox2fzzD8jALkG2IfuY/7QlCDcj17ioqIy\nwiN8\r\n=qA6C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "3bc8adc7a7a9d3c4c54ec935c8890c31db165fb1", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/callsites.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Get callsites from the V8 stack trace API", "directories": {}, "_nodeVersion": "10.13.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^0.25.0", "tsd-check": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/callsites_3.0.0_1542649008789_0.5312700033194684", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "callsites", "version": "3.1.0", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "b3630abd8943432f54b3f0519238e33cd7df2f73", "tarball": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "signatures": [{"sig": "MEQCIA/lrZHX7vySLwruK+iPJt0+tyTw4L+oKxximC2ajTTuAiB1J0Nelarxi24N8BKtp5N0OgH6TvaHu3f1MB5IxPgaMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqJQrCRA9TVsSAnZWagAAcRoP/RY2HQ1BswbNXAS2M7bm\nzebyqQswmP3waLE3aAesF0U93FLfWZGocpZX1uQDqsUdazD1ZjpTMUrEN3jB\ncukO9E0J8JqAFS25KZeKArk2jS86VzeslPSvVxye/LJ9tB115m/TrudLG7jV\nvEgR3/L1Onh7Jfz/hGqB9RpO4D6OdQsK8UmXt7wgXDnzazMS+Zi/x4woQR9G\n8BfVor+UCfEtm1UOuJ9STti17MZKdtk8F0kHBQGpcvcntsBIPGJN4hUv8sUI\nOXMSQv3JDaW6t0on5d7cbMdFQ1+BCB2+audLXgW0lUzZsWXUMRZLBE26LbAf\nXy7lIbtLy8edyLqsyTa66TWEnjSZrAqAOGuBBGZi7jBtPKpKVz9a47m4ziPe\na5pRw3ZNY2mdSoahYvuaIJMrlsj9xFtaqAAP+N3Lqlb/MxmHPGMMdxdECr2V\nJys6Gb2ncM2vtW4RvmWrQqUlK/s1HbCw38UhkmQfB0r2/K6YIwpPdDIfmvNK\nwgYRBIAFAIxPTNIQ2u0HzcCzKMi893dhwKOtJOhJF2IYc+1E9/AiwGOeru7w\nNNNPEsxwczNfN4cK+0/1OYO0w7j+ybNKtIRhq37ygZZPNnOZ50sYsfie+3Mg\nBWHHRcJIE5vt4ju3qsU39kTBJyOmskw077XIdpKb6PfQ+afntDD7VThWR/3Y\n4YTc\r\n=TFEa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "f89815af2e0255094283c86977f1e679a8fb411b", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/callsites.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Get callsites from the V8 stack trace API", "directories": {}, "_nodeVersion": "10.15.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/callsites_3.1.0_1554551850429_0.3545214734540718", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "callsites", "version": "4.0.0", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "8014cea4fedfe681a30e2f7d2d557dd95808a92a", "tarball": "https://registry.npmjs.org/callsites/-/callsites-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-y3jRROutgpKdz5vzEhWM34TidDU8vkJppF8dszITeb1PQmSqV3DTxyV8G/lyO/DNvtE1YTedehmw9MPZsCBHxQ==", "signatures": [{"sig": "MEYCIQCd2Y/MGUEMXq4lXU2dQ45sFTrHMYfSnb0h/xu+aS2jGgIhAKv90VWm7ax78ubjAEY5vclEJ7QngKmdogkoZLrXtQRJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEGX4CRA9TVsSAnZWagAADCcP/1wwa5eA0ZVULyXdUwUL\nf6zgChgGXNjhPD9+q3E6ybcBp6WQOVBHxC1MuaLSZWLKJMbcHSP7soPnwPFL\n9/kS2Gk1NR4KQdeC7/DsCxTOdBt/UfmC5ADf32I9kIyPrCf2p4Czg3IeMpB1\naP6el1EGHHc/rHdMGs0g6bF7M7W+WXCIJ1dfykbU5/hSwki1ZkGMHmpTSPYz\nAQtTkqGwiUGrXtxZOyVPI1cRL4M6XeYMqsa0TPNZJgBodk9PKAlwxexzpBr4\nVuQY7fs0VgquzRvlSMqiBzXhdh7qCTZNISMXCdX0x0YtqpGs+YxV8Il28Xwr\n8bgcH4r/9+YLPNelf1wQijjkSWaB+m7Y1xnnpSLRnTt2zRzk6lBYEL1g5/+e\nZLk8Z937Yz6K7iF7sisNwtR9+EPRuY4I3pq6a0q6sRqPcDt1Ast+38lokksV\nahwpS0SsDWN7c26z1vYSeSVefY/ovZt4ZpEK5aso1J8wR39Y7Lwpfu8ZB82T\n43a5uvvjgYLs1mDThVuc+5GtuOfzip8UGKszkdHJlBdHtBV+Pv3+jGX3OaaP\nwSUn8xAFXwiSwrN6wvk2Wr6xuVLuohvTAJ0B7VpXtpxdFXS7Cvs17stY1KIy\nMH8KZh+JwsXi23Dg/MOL0mBsQi02++XBzWeJIDBAtv49Ogdymv6rHGS7xmSN\nalZq\r\n=DH9g\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12.20"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "467c1de91e4e4d6f4c540c1ab99a7d47ee565471", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/callsites.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Get callsites from the V8 stack trace API", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/callsites_4.0.0_1628464631967_0.9082832754794896", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "callsites", "version": "4.1.0", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "callsites@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "dist": {"shasum": "de72b98612eed4e1e2564c952498677faa9d86c2", "tarball": "https://registry.npmjs.org/callsites/-/callsites-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-aBMbD1Xxay75ViYezwT40aQONfr+pSXTHwNKvIXhXD6+LY3F1dLIcceoC5OZKBVHbXcysz1hL9D2w0JJIMXpUw==", "signatures": [{"sig": "MEQCIG70mMWzCmmnMHuG4ZY4nQtHf7EtEIq/JacJqFCcXvpYAiA5XcnDIIVryrWSIe/u147hUZlhwIjCwNhHWn5zFRev/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6527}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12.20"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6bd2cc07b0710d6ef9e0b09122745b1c275797f9", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/callsites.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get callsites from the V8 stack trace API", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/callsites_4.1.0_1691850487630_0.5614972985982614", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "callsites", "version": "4.2.0", "description": "Get callsites from the V8 stack trace API", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/callsites.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "_id": "callsites@4.2.0", "gitHead": "d782915ed4ef09a5cf44dddff7a55765cfb865c6", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "homepage": "https://github.com/sindresorhus/callsites#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-kfzR4zzQtAE9PC7CzZsjl3aBNbXWuXiSeOCdLcPpBfGW8YuCqQHcRPFDbr/BPVmd3EEPVpuFzLyuT/cUhPr4OQ==", "shasum": "98761d5be3ce092e4b9c92f7fb8c8eb9b83cadc8", "tarball": "https://registry.npmjs.org/callsites/-/callsites-4.2.0.tgz", "fileCount": 5, "unpackedSize": 7134, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/RK0/w4TMffsVCKp9/K9Cl+MfZfN8t4aOyIXCTwwK2AIgYyKgMYQo4nbLYF+tGjvwdTB5RM1ctGn0dS2OrMwKW/4="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/callsites_4.2.0_1719668784407_0.26992061774254306"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-04-19T10:11:43.316Z", "modified": "2024-06-29T13:46:24.735Z", "0.1.0": "2014-04-19T10:11:43.316Z", "0.2.0": "2014-04-19T10:12:18.747Z", "1.0.0": "2014-08-17T18:47:35.372Z", "1.0.1": "2016-05-11T13:34:39.471Z", "2.0.0": "2016-08-28T00:30:10.498Z", "3.0.0": "2018-11-19T17:36:49.033Z", "3.1.0": "2019-04-06T11:57:30.657Z", "4.0.0": "2021-08-08T23:17:12.301Z", "4.1.0": "2023-08-12T14:28:07.954Z", "4.2.0": "2024-06-29T13:46:24.572Z"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/callsites.git"}, "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "license": "MIT", "homepage": "https://github.com/sindresorhus/callsites#readme", "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "readme": "# callsites\n\n> Get callsites from the [V8 stack trace API](https://v8.dev/docs/stack-trace-api)\n\n## Install\n\n```sh\nnpm install callsites\n```\n\n## Usage\n\n```js\nimport callsites from 'callsites';\n\nfunction unicorn() {\n\tconsole.log(callsites()[0].getFileName());\n\t//=> '/Users/<USER>/dev/callsites/test.js'\n}\n\nunicorn();\n```\n\n## API\n\nReturns an array of callsite objects with the following methods:\n\n- `getThis`: Returns the value of `this`.\n- `getTypeName`: Returns the type of `this` as a string. This is the name of the function stored in the constructor field of `this`, if available, otherwise the object's `[[Class]]` internal property.\n- `getFunction`: Returns the current function.\n- `getFunctionName`: Returns the name of the current function, typically its `name` property. If a name property is not available an attempt will be made to try to infer a name from the function's context.\n- `getMethodName`: Returns the name of the property of `this` or one of its prototypes that holds the current function.\n- `getFileName`: If this function was defined in a script returns the name of the script.\n- `getLineNumber`: If this function was defined in a script returns the current line number.\n- `getColumnNumber`: If this function was defined in a script returns the current column number\n- `getEvalOrigin`: If this function was created using a call to `eval` returns a string representing the location where `eval` was called.\n- `isToplevel`: Returns `true` if this is a top-level invocation, that is, if it's a global object.\n- `isEval`: Returns `true` if this call takes place in code defined by a call to `eval`.\n- `isNative`: Returns `true` if this call is in native V8 code.\n- `isConstructor`: Returns `true` if this is a constructor call.\n- `isAsync()`: \tReturns `true` if this call is asynchronous (i.e. `await`, `Promise.all()`, or `Promise.any()`).\n- `isPromiseAll()`: Returns `true` if this is an asynchronous call to `Promise.all()`.\n- `getPromiseIndex()`: Returns the index of the promise element that was followed in `Promise.all()` or `Promise.any()` for async stack traces, or `null` if the `CallSite` is not an asynchronous `Promise.all()` or `Promise.any()` call.\n", "readmeFilename": "readme.md", "users": {"alvis": true, "zuojiang": true, "servel333": true, "flumpus-dev": true, "wangnan0610": true}}