{"_id": "ts-api-utils", "_rev": "46-0d7a9c4a1fd5812f9a9799a0ebef8b2f", "name": "ts-api-utils", "dist-tags": {"beta": "1.0.0-beta", "latest": "2.1.0"}, "versions": {"0.0.18": {"name": "ts-api-utils", "version": "0.0.18", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.18", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "26d142c278291f12bd12951201e24b57de9dff32", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.18.tgz", "fileCount": 4, "integrity": "sha512-keFvoazQFMjYntBfBXYun1v3OB0HZP4mQcIC1wGi4hyPirlCAqu7Xm1rywK0XBx2kTZZrksfcL4AlBzC7YhPIQ==", "signatures": [{"sig": "MEUCIQDSiJmrombofqRLGPhGgWekq9P2VtSCZB5to3WaTG618QIgRTXC+u3yJOKCwQ26dlMuPEuOj5kIREfG1fXIAt6QR98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4mwPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYVw//aVB0KpI6HK5ZyYivebRr+FLPyAqeI108eO2wpp+BAociuMAT\r\nfZYYX8jBZTwp0di8t4fZP+ydtbr4rQeIrgP+x3FnlS6spTmo4bnWFEdsBzS9\r\nO5AZmDsP7EoJzzQFtWe/qR3Syf4eVBM5NNy6APbAkJS5DQy6un416avyt2vP\r\nv5mTgAYekTUG6b1zHtxSIiG3xzKRZSDZ4EWfO1wWIZZs1/Dl5LQx6wMpJpr7\r\naf5oNotFcVGGS7Ab7+aJAfUSXnHH/2aOO7HnD1mN+BvreO+XbJdRsMWzqAmz\r\nlnOGTBhNYODUY3l2DfZpfZ1qmI/j6kgmcZ34khMk2AIALtBA/bdnK+YNlsDX\r\nrFA96FLwFfW0jhrt8VqLgqrtXEGaNe6V14yI5mDImmkXbfZz3BJ/ZyStKyZP\r\nWak1EqnMo0dd7HDBeCd5aiPEQI4QrghHc5nCvb7OgQcIemntrRO69oRYowTr\r\ncV8fqbbJlpwoCBXLF1TAG8SwjaanD/PQF1IMsRbQUAn6OQ/Fn2PFKcWBzPhV\r\n2eOiNrO3V7+iLPfZcJPyaxkJWf6WbzNc0Y6PL9Kb6G3aMZ5yTzSz5cBjBbHg\r\ngH/T0cqSk8OMs+DbDJTXJx9iQSX3JLCJFIzHuF85sTFHMxBnLKmLtVZ3o3KZ\r\nt/8yVkEy3YZOgHoMeWKmwqMVuLTwA0jMKpI=\r\n=P+l2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=18"}, "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "0138ba6c066ed7a35d4db375ae078c973b813ce9", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsc", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "build:release": "tsup src/index.ts --format cjs --outDir lib", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Utility functions for working with TypeScript's API. Based on the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.26.3", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.32", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.3", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.18_1675783183787_0.8240179054525356", "host": "s3://npm-registry-packages"}}, "0.0.20": {"name": "ts-api-utils", "version": "0.0.20", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.20", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "7ab4bb781869ea28bda13e5688ea2e9153520378", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.20.tgz", "fileCount": 76, "integrity": "sha512-b4gjNeZ2t8rfbpWzlVfrUvIw23H9h/ko5ASCJdHxi8GP8jlmg1xhkfFP+QfdVnGuX0XL1PXa4Oat/mKbjrAkdg==", "signatures": [{"sig": "MEUCIQDegYECAhvltsPW/g0MkLxhit37vfuTz7R1bhVyv61iEAIgSx6MYcS1obuQno3ND1SkHv/NNyplgB+YI/MguRu6138=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4n3dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNvxAAlturzJymbhdYaYhSvv6ydegqlj94jhXdjGcceALzD4D4Sxrl\r\nNLxclMC4inM2SeZnztoS4M7fQ3NwVdbh7C3yu31n0AjYSQdqYNKgmIf8tphF\r\nfF/bEcBntOlZWi4EjUm1YaadH6eJdCrOyGKCCfdbIzsr+SLYnRhnfwvj7T4q\r\nP8e0lsrs2fuyecw4ZzG5Z5eY5OUcD2eYTgOdvgb9CyUcAsU911ATqdS4gwIw\r\nP5UefvjUjCM+y02JaHC1poadmpc1wQeDoxJVEX4spZJCNxcmvSkMo9wvB9hw\r\nqkS6SvNMBmneVtklfHHyl1c/VR55ac1mnrLCYmdOLpaxjVqsm86vBUCw297Z\r\nOLid1BqNN0H5A+42msRZwvMcFYdljU3QnfHo73cmKiGBrt6Yl8g7In4TsVBP\r\nUIM5LSOP4qcsuYwEq+xc+HYpRCyzOpUqz+hMerzs/drCButbAE45O0rFLqTa\r\nDXP/OeFZxaj3b7opsXhrPb9fsRjkFDds5zlPklPZQgOZruECoYXfUlegs9oW\r\ngOD1hCmg+zfGnxT1Mc7GvvX7SWJljrpIipOz/5FBpukmqV/rHBfUu5yuJ+EV\r\nusKiHqNwO8vpXCGFfxOpzNf6xdwFwAe/IuZ0op1tw3RjjqG9bl0hqYRZuEFf\r\nHoCED8Uv4iubG9eyIlM0acJkGACeeEw/jeM=\r\n=OUkE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "12c2de065cdbc61128807b1435e81107f0bd97cf", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsc", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "build:release": "tsup src/index.ts --format cjs --outDir lib --dts", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Utility functions for working with TypeScript's API. Based on the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.26.3", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.32", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.3", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.20_1675787741653_0.5059591800697643", "host": "s3://npm-registry-packages"}}, "0.0.21": {"name": "ts-api-utils", "version": "0.0.21", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.21", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "d5e58e3200f848123a89f3987d6ee9b613642d01", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.21.tgz", "fileCount": 5, "integrity": "sha512-Dr9RHp5+4jLF+2wARhwQO1Z/6BFVsKigZhascnbsbyzSEDKO9qGlN7RgsquqHwP0lHiQmLJFYiGCLXTmcDC9Wg==", "signatures": [{"sig": "MEUCIQCrjbASWKVO4u2lJRg1eXB0ZzHlZA5COjE+fg7jM3EirwIgcCG8edGCwvLrityA8R6C+sGGBv88oCe6RI02HJ2VUIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4n4KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpy8w/+KYsGS0BvnAvnEYTbDEITllA8Up/DdUsAUithqRhbNP+HRXBU\r\nT3H0gddn+VBqK7IIaL0lExB6sqYXm4mOyEVAFnYiEfkNHjhbjdeXEH4fxDRd\r\ndQLZZUe6Xd5I53wpUcWnsdLPmRfNq2bhLLrHCbWLwxcAfkT1GftUyaB/YaCz\r\nYqJkmKHfKdDU4YlSF6q6asHb7sd7yRt5cRx+fSbiZofd+vf8yp4vo5wVRznU\r\n8vzmCyQWr6dC+UJv48cDuSMoh28k8qzf1Vut7VO2G6nGfZ3PRZ1FpEm5+1m6\r\nqX8TSPYh6SGryNV7cGr05LvLODXUBR+KOUfu6IQQ9WfAy032mc4iMVnMuyu7\r\nk8YsBVgv+ZnT9C0WpPJfeMG7/rEfjSooZhAAJ/d/fascMQoYOyuMHugBMuk6\r\n6jcnvHruP0YzPKK4MqFJdAWfbVEtmKvMsG6bZs7APh8EarT08YU3Qxio5m3W\r\nBki52XLG6nDeETtxTQMxHJwv9vNo7YxRtqFdRt1jouU59PkD0rHKsLGQZKoF\r\nUmsPvMApyXSBaVlUdNks9kgeb/CF1sVotlyUAKwnt+AmO6pxRutgqVZ/a/qR\r\nWOVsRuunaQXc66wKd/E312Q4SJB4f3qLfSOPMPa4zM2Gp1VLPhy9uFx4mZXt\r\nsSiLYrO3z9EpNBj8S9hB+AQi1yDcKjGYR4E=\r\n=3GUx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "12c2de065cdbc61128807b1435e81107f0bd97cf", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsc", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "build:release": "tsup src/index.ts --format cjs --outDir lib --dts", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Utility functions for working with TypeScript's API. Based on the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.26.3", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.32", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.3", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.21_1675787786030_0.9900894531555366", "host": "s3://npm-registry-packages"}}, "0.0.22": {"name": "ts-api-utils", "version": "0.0.22", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.22", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "c58aac346f3990e6e164b4907aca57f54d81a2e8", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.22.tgz", "fileCount": 6, "integrity": "sha512-XrQNMP/CQk2gOa+NfNIxNSf60n+RsC7tAkyCxhwnkShxUFpitvwNDfDxdMIZxHtdKKqqeRi94T191sNN7pFSrg==", "signatures": [{"sig": "MEUCIASE9GYzp2UwSn9U53IIPVU1jocf17GpF1omZ8HiyiRJAiEA3eiAX02v13DKWmupVwWcOXjuylZubDRP9w+wKDXg0Ds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7AJ+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIOA/+Pua1cJrxzrUoiQzFBfFYRCny0Q0IJupHErMQ1P6ZM4hZgHSW\r\nMTQ9q8LHYwDjNc77TrYgoLKjhhBG+RsGzd0BIGMOfgklyaHRaWiuqIlY0+VT\r\n0l7kqajaUysLlBKmsHjrTdNQ/5/EjbJbk64vzQ3hHdYir2fQVHObkGHlrd9s\r\nQMGsbawYD4y3hEUpbPADCZukRQP2uUKx0UFY+43TGZM1LCD3aOpcwZ0kghmL\r\nAWfTCZYq8ojp7I0k3EZBAk1Ad8aKK04fSJbtc0pFZq/OjonMaN1BiqR17lV1\r\nMQn76FewoBwHw4i9xmdBFx1PswPF4a/J1otahWFJxhguQnEsokMuOjBeUlBt\r\nN2+pB8pxrQaX0j8f1m3ZAlj9Ul5MyzIaD/dPIz/3VkFYqiLmN3/XJCCXC6nJ\r\n2wP+s8aCGDygPr0o91DuholiQBRG95IIjrg/HwlcMbDeL6onda/QWSUrK1gb\r\n8wfxSQe8VFwI8WxgJpeit+m8BJ25g/tetUc7V/qP6ICbSXfY6PYjm4313zAj\r\nz4I6KxdqoFZXr6b89uRBUrOuE6Vij+d5Rv4akiT8PM5IcIHCq1GqBu1MNXOK\r\njlHIVGTGfckRHjaPTwC3S7vAfQtXuSxeBXA6wXMP5/nwHa+YSSvgw3zglAHJ\r\ncEGC+9yt627YocIvNkH5djOQ6480En2Dga8=\r\n=K6mD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "c5bb9fa5ee8bcdbd5e4dff6dcaf6c21bf81a771e", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.32", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.22_1676411517999_0.3448344997524644", "host": "s3://npm-registry-packages"}}, "0.0.23": {"name": "ts-api-utils", "version": "0.0.23", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.23", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "ff56a8ef9ec5c25a96bc520466e00824d453240f", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.23.tgz", "fileCount": 6, "integrity": "sha512-cwHjNlkqFnrACCuHscpPkIMRG/9NAi1S0fuHvR8DoGxtj4axt/GuDU+tjb9hcrVScDH6gs0NOzCk0ZZAHAdmgw==", "signatures": [{"sig": "MEQCIDdJPItHipHBhXPKxDjaxqawcQ4Oqp1pWfNeeeC8IfIZAiAb7Q4te5djIthabOelDqdhhlD/zruaBZHAboMWp+yhEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7DwRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+Hg/9FypacgjxGVvm/fXMMo7U2aDAfqHiruRyEpJTbncUYrhQuV9M\r\nfIb/OJwMQ+/p9wVI5nY9B72CyoJPAgyyLV/dDqC5Zw9rXN7HRV4afMFykXlb\r\nZb/+bAxg9/zLvPpqPjZfQlrFDaVRrfDAx4n9oO2hKh0mANwjF/4OTlhyNLXh\r\nTHjZHTi3bO3+cYu097YvyQTKpqkej/AFCrLjj6Bxi7m1ASuLwHFkKEik3+MC\r\n0nEsoWIIYgTN2lo2ER2150KqgPHCIjMTrePGbb0oQztdFBVxUXacylH7Axl8\r\neQuTslQ+0n7qMmIB5TMi0Z8DFUnmKhlsPHXpKBRE6h/jMseAAOhKKhsupQYM\r\nXE9RLgbwZJSPuAZaTVrOMW9qhvQygQNEPselpr1zOj2MupCHexZ/a19IU3up\r\nUnRuVs4awXhtX9hBy6GfNouerfzvjYDzHWBEdgvsFyjMNXDd2VXYlmn5jKq0\r\nbnG0UN0QKAxHBgKmbMPpdgDNKUnuy8es+gj2rBojcKURH5tvfitNm0bKDdSK\r\njQMA97stH5YKgJ1hGoeIpiSH1ear5G98wXn525LqXsX0xSYGU6NMGLx/N2cI\r\nuVnkYXa0qGN1V6KsaiDu8i3FakWCh6DOTMwm7a1LP1xNpOUTkF1eY8oU7xU2\r\n/HYFP0efIIe22ssw5U9NYnWDBaI8uJsF5S4=\r\n=uv2X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "405c475cec5e1fb0df2a4a85f4834e9ad5de6221", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.32", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.23_1676426257242_0.6112835750804391", "host": "s3://npm-registry-packages"}}, "0.0.24": {"name": "ts-api-utils", "version": "0.0.24", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.24", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "39732cf7d65fd136ef0b17325a5c24ae0c775fbd", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.24.tgz", "fileCount": 6, "integrity": "sha512-kQceFRJRRbFCdr9mtgbpFrADBriNwaSu5pTke1m4xjLraadUPb5/XSLndSft621ffBeHy276+2R8fJOFSEO5fQ==", "signatures": [{"sig": "MEQCIGDNxCB+C3Cf1psMuqGhG2RV/ZGxqQdCcjCdIJrRGlxHAiBRPQmzCwVHAoM/8l38f/a6tWR/nVcGaJWrpWOL6HKOnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8S/TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+2w//S6TZhgO3vMJgMNTJTYfVpPh6/3YYu1nQ5iFaOZdhNkSYCTgu\r\ndi7FuQG2IYbjG2Yr6X7tzEC852GpMlSenyOQfs8qesyMCkpHUQu63qFFGde6\r\n0HOvwiI2nwUh9HPvgrCY0ct3g0RuSN2EIncM6zfIm3uNRgcEpHsnSKqhcS4c\r\nNNFWGBecZbHSG7MCveggcBdx88xI5Joboac9xgg4rryYmrJWlnfXPMa4/pmT\r\nfTZdiu2I2nSbmkptYP3dRp4SVEAN0Of/C5nmMi7yd/Uc1JCWL4PmgDr8IRbQ\r\nYFGpTLTEMUbMli3wDnn3DkwTk1DW13L/i7cuIJbxAo23bSZbhKa4YISgBIt6\r\nBzfs175XsZhO+aGkmtEkYcGX0oA2WTqNiyuytGb2vekQD+yV0Kx9wqZ0yPt3\r\ngq0ltRlSEnWKf6SzIMWZr8YEwwnvvOu284DSpJdtICuwk1wMXHrtsz3RRTRz\r\na/g9qbmBuL071u3kXhVA6cWLe6Y4LbCi/u8vWSc5+goPAMKhNYqcuvUfJAV2\r\nmdlc3aZqbvVEoL067H/57dH1B9L4RX7pIJTo+UnekBd+iFzw91HZkehL2FxY\r\ncwV5GNkltc6YoQeOhgXFhmFtoozTVKjxADatrOIk8zrxz6zvCyMRGF5PUmvb\r\nUG2CC9TlkTYhmYTCNNpZUu0ia474IWmTTk8=\r\n=QnLD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "f81329695d4ce86d5243f00268ab45d47d7df438", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.24_1676750803229_0.5321418558524109", "host": "s3://npm-registry-packages"}}, "0.0.25": {"name": "ts-api-utils", "version": "0.0.25", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.25", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "44c67a8f22db8d86ccfb8728cf4ca78768145e13", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.25.tgz", "fileCount": 6, "integrity": "sha512-qOOXsgVjavJZBTaXmd4NLucnwJGAzsaYXeaY5WknhTSKeb7gKP9nVknl1KDUOvwQDk91EckHP1at1TRN8o+n0w==", "signatures": [{"sig": "MEQCIBgXQmSZIJO+bIHjoSJiVCwF+3vV2FMkc3SOXP/bvmW0AiAY8fCxlbJQWcc1MRYz4vxy2XiCS00mceZHSI06bG4kVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8cl3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXWw/9FM4E/+JyNqp3S2HWML0Z2DvJaf4j5cxrf6dJPypuENx0Fbri\r\nrAP7uMk7yelE/q9XNPl4NFA6i7q7wjW9fthiQi4g9zc3vNSzllj1JUWh9CMs\r\nZUiuL7FHM5kub+6zq2C/okV0p1qW5o9cLGzSh6i/PlJSZaX5Sp0au5CKOzyl\r\nKSm6BfxSHdoUVA/db08v4u52ghgsQWJcZdVtkAfByDyathWuP1OVthpdul3R\r\now7NMCQL+fiTsfN33tqQLgrk7r/IcA08TvwsJ9jQg2C/QZhLMWAlHG28DY82\r\nt3EZGtTze64KHm6kxdGz6CABspo9Lo/e7MtXd/ZqiE6suabjVXtL9rDqE2Vf\r\n8+lySTHMW+BRLmzV8eGjqMdE+ct02xNuI/fZj5zx5FeUPCu64BJsQZSHsOLW\r\nOjPnnYtrtPKqVZv6vJ3XiNYlz0cIeSPRtdn7hnYzJqDzuZLaVOLJ1GAEDa+T\r\nRvBV8VLsJXKwXCEOa0FgQgZVLk2B+ihE5NWGEcoBfDvckmJSKsglZPWmuFSR\r\nXa+Z48IrB1iYZBq8OQultQtqgIyBiRqmbRRUQxjIiey1Oy8BkI4nHOfzbo2l\r\nezuAKAD2JwDiMvEKLf6Exsmmzf66FKnGgfwC+acr5BmwHFYZxWlWoHUuV9iA\r\nKvtvboT8C779WTjVSrOL1/PaRfg4b043U7k=\r\n=J2ve\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "2534435ecc53c30e4c526ea5a3c2989aedd93299", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.25_1676790135635_0.1382001466484586", "host": "s3://npm-registry-packages"}}, "0.0.26": {"name": "ts-api-utils", "version": "0.0.26", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.26", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "371b60c91f481e3783d95724e31fc81e42d053c9", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.26.tgz", "fileCount": 6, "integrity": "sha512-a92cOJ+l2OGB8RSfuhpoLdi1FUgM4WmCFkwtw2+XX10r/LI1kn/9y/gef5Vsbl3LUXFnKG0r50XPDJxIogq1cw==", "signatures": [{"sig": "MEYCIQD/JqHG5pIdGai+N/1BINVuFiOhLywK9pZd9ReSV1w7GwIhANBRKqrQCW+Y0pJ/dpOILWja4dJJ7Fa2bdtTTVXmDVL2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8ct8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocCQ//aOOUr4/KHDBVyaIEjfP8Rqe7afX2u3LF2rh5dftrKZBlrh0M\r\n4FpKHtqN2GwxQ1mEo5b2B2Sdnr7T3Jh7NU8ISJdLFOtfLCl3gOjQgcFb/NKP\r\ngnXZlUUE1SzTcrxH9L9h/fQUXuGWaolOc+sGq5IWvJOriCdIAuW83aWHAseg\r\n1vKJtOJVdxzahzEHw/D//YGdduDroxMQUJzc6uwQm23MSPbhYL5angrrTOoj\r\nkkX3xz1ZETUO4o+rNsCDkh+BE98DaVr7vlAOpwSjupXs4qwd+zFOb9dCxwug\r\nPLFNyS2FvZl1TJzOXEHxFCnqHKBMHbQEoIFHlJRFhaSSWRiOyYX0T6/ORKkh\r\ngtudNionZ8kLncClctAnRWISsnc1PS1sEeFOg0gbyge+TupVobnvq2y1htWz\r\n7N5u/SIbKngaBahjdJF4Pua6eInpab9fskE2bvYM9pg3TTwTKZBf40Otbhhj\r\n5wbYg77EPpiatCrmRpxABIKpjp2svHqptqqC8En2iSubD15I+vrfMZoZQkTJ\r\nhOxxJg161VVReu87IiaHf+FlG2ilzi48yidURKrHZC/y0nm7BfAvCQxinUzq\r\nk4qUsWT3dWEKSErxSX+5yw5klF6JKqKyyV0P+I5VK4rg2PkuSGLnlO9WuaL/\r\nuh7bb1r9HBilJHW2V1EzECu0N9ALJpRJLLo=\r\n=9dDt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "e37d29d127a801aa9e876b84e5b9f6fc74b54d1e", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.26_1676790652723_0.47135340572928874", "host": "s3://npm-registry-packages"}}, "0.0.27": {"name": "ts-api-utils", "version": "0.0.27", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.27", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "289bfbd58e9241ea6a94262ce1d03f7230f79ac8", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.27.tgz", "fileCount": 6, "integrity": "sha512-O4lauvCUxYna+F5N+gFFmSXe+F/EgK0qkgGVPXk3TsehPDrT9B4lcjISPZtJyiooJym/VRO/l5dkc7A1RwUFBw==", "signatures": [{"sig": "MEUCIQDXh5KLa/r8cCKPotnHaLyX7NsKDpqiKTzJYtI9xyJXkgIgQoRnTKp6KEY0s5JndwjlxABtd/FwUCxPNmFfxz4YkTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8c01ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoN/w/+JcEwfC/0W/OhPwX3BEqXAahHtD2J6Net0M+Wswc3iuR3X5l2\r\nADeiulMUn0nvsdJK+gv/C57h0pflB8wQcCKYtZFXzmhBFFpzLcllhC24idFL\r\nSzAmszM9ynFqcUmQ4bbDa6tFuNLEcb80d4frcMjr0E1p5tgdq+Ag2aE5CXaW\r\nFf6Iok+1aypZZxQI1+dSDy05DDwRNgHLYw+c0WYZQvSX0GkyfIFZ/8QB+zs6\r\nPkkitHJCEsZuSaDCzICcvhgrfJLSJPjQu6B+Qutr70wQGVJPS0iC/Uqt3lfg\r\nu1ldFa1dVr2pIELY9OfIy9z9Qrj08KcejlUeMwWiSCC0C3EqQ9EMU/0lbZNW\r\nTpQRvWrC2oQahIGNYcBS9e3XYoR00lZHrRvDeOF/ONgih53s8Jz4T2jHZb+I\r\nfMb4DVKREJqsCa4rteuZuB0Ry+kP5d2+fCMd3zKzHIyzf7B5JHu3TO59h4rL\r\nPXNU3kkQrDovTW5Xc4A7rw65XCrwS7dm+Lk5U+PTl4uXN+3ibQerUnjYuAFz\r\nbFRv9Nc5vjW0YZalkZdXwlIBC5H8A1zSR3MWmkj3yP7i/vhfYIbMPPSZIICt\r\nraVQuXD6cIc2vKtHoj9O3Vqu26BCbPCGvWZfHiA+7/ufoayJVlR3cXEeAahz\r\nECHj80iBSlVvSR0RuIolPMw8ysz6Yr/kqp0=\r\n=tj80\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "129563e10c28f3f922196bcd4011a0e9acf01604", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.27_1676791093108_0.30303423762844184", "host": "s3://npm-registry-packages"}}, "0.0.28": {"name": "ts-api-utils", "version": "0.0.28", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.28", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "be4e70b40ec8a63482bcb63c30e2646c669d2f23", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.28.tgz", "fileCount": 6, "integrity": "sha512-90VNjxKwQmBm3JyfkitDXVzG3WppJYeCDUghzi7w8Q6MK9vYmkScg4Bg1LSC2qXXJz1ToyleQqmXbLhVJW3t5g==", "signatures": [{"sig": "MEQCIB8dPn9BkMUAxsMqeUCkFqWoGzcRiFUcmbuym0cqZeX2AiBkAj6TOwZq84y3Y09b+Y5n8I3zw3IFY63SuuguTsSjiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8sa0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoB8Q/8DD/tnlg75pLh2QzhWRSl8Oszh3lNL/5Qha+GffDYOKetaG6o\r\nj8/kMxeHGaJ+isTHbkRlJbYiz+y5+gPwqWfwHMmuQGJ/HQ0Zt07MmVI1X1VG\r\n6cboY5ak+iyXQyBrYiJfuKtr/2fE3qxHjTFFmuSinYzA/y1fwNj7d3/J0r+6\r\nhzfUf9NRTpj0PQDxyFwUPT+JeZ7oYoQwHYa6+GktPOFUZp9IDcF1X8WhAcAz\r\nGHe0AEluY8jer8rRenDI2j1zXuxFz1sRCYYVJpm134LkDE7Ox1sT/SCpHs8o\r\n6scY7QB6YObBJl+rRre9xQD65oRC5e3mEaloy9K3iryQ75lDMVqdxTaVP2wN\r\natHePcdgCY/3JiGgBTCblXnsxui46ZazlIHg702Pt4yDW9+96WDI09WSREJA\r\nrZSUB225ALRF7EOB/Zjsn+/k6VmTcZ+GW7aJxNXscUVgB4VLEKNX6Usoa2x7\r\ngYprxIEiMkPHmlFjVr8HuUdz18tfwY47yxPVZDHpAE80cb9MJtZF7NetVBKn\r\nRAthVhgDVAooXdG9t5O/bomq3stY09xK1Ab2T+mGUzMkEQWNd95FLOZEFUaB\r\nID+g3N/CTEZdITRmX8aMES4R5fQA9SgS8FjUR7lCIFgwxlk710AoY891bqNf\r\nbAkAxdGxSr0TNdD14tjiBTmNlsO/foc3oW0=\r\n=yvN2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "e8af214fe08e2258f95aa9428543ba72c9f47c06", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.28_1676854964095_0.5633566227772595", "host": "s3://npm-registry-packages"}}, "0.0.29": {"name": "ts-api-utils", "version": "0.0.29", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.29", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "a1e0d2ae72630adcbf784aa3ca782ecead75b57f", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.29.tgz", "fileCount": 6, "integrity": "sha512-m4IQ7tWevrDVbG/aSrullejBWGzRv5GaooB6JbN7ARohbdfrackiNuSUwC9TBZIUXhgD05xKUd4RJeRWzXcxcA==", "signatures": [{"sig": "MEYCIQDnil7R9emZxZ5HLU9fR9gumgoHa9vkzCPl00YwypBNAQIhAKWNkdyLQU12UnyOPk8txjHuhoMVTltbiZY6TgAHmYU3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9OcQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT5RAAgnM9bXxFzb3NibFMz+2fCg7qTog/wG2pXUvfJfK5RUgp8AUf\r\nKr9ajcgR8XtsR4JZxBxVRV0KRyNpBfmS1NWKfr2McJ5b5pHDQjIPrXaAj+W/\r\nKnye6M9yoHlduY8Q5snv97wkFFCI81rJEAWIZ7KynNpBamUo4sTumpB3f882\r\nGBqzdE5NMq3Oj4FxITwnoaywsqDVakS1Rk9cNLiRgQ4YDYzI8KtbKB6EPZrw\r\nfidosRZfyPnvfwu7ikrRJRoSKQl9waUiDKzG47W+Frhd9kXamZ0Zmdf6dbZA\r\nkCqv6dFP/ruMKkfaaic4XOl3bbeqN+kYY++PzNBqPlW0CL12N8L6v+bnw4Gk\r\nYxO8vHglGwbgtI5UxzQrx+FOQQ0DihWAYPF6hWuG+LwGAmzn6sThu8znJOrA\r\nIj3bi8Dklu2u0M2hSwFNymE0TKMYKpKb/YkIO6am8i7SjMST5tsDcnVB5/c1\r\nEI6WAgy0h7KErByFoGpO2Q4EZODAFZfVNUvCuAWPy1JMfiRNzHcmhnHlXpG1\r\nFAVMYLJBz2kUFQCMg33UmaSnzE48uEGZ6RrOOMCGnTIiP1N9RVXGb2JOOro9\r\n6IBpkzKjiGK21jV2VzqZSrnpLNOaa+LcemCLmwkUBRZhH+0YQ68r+/PBTlp0\r\nTsHPhwsfwZ6HYoRASbYucBO7MmEP+KhCpv4=\r\n=2zso\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "598a1ae4aeed1adbf4021a93a3246d0621febc77", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.29_1676994320192_0.917644294984119", "host": "s3://npm-registry-packages"}}, "0.0.30": {"name": "ts-api-utils", "version": "0.0.30", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.30", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "fcc44ecd4470911f23dd114e282dc4cdb81c49ba", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.30.tgz", "fileCount": 6, "integrity": "sha512-7WvwqwKa0V2niH84iUAN1hyYqmS3Cr/l5Th+Pg9NT97UMz+wgZP/lQF86O06EvHEB4oqzs8vPvV7+x5PNo6Gng==", "signatures": [{"sig": "MEUCIQCpqLhuoDj/b9oW3hAcCatfVvtE+5jKGJptI2Ae7CEbkAIgZbQW01lHFo3r49u/LxTk5fs9PDhyleecOojEKWdS5WY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9XKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNSxAAkWiQApW5nXMENzk0B2+7X9XAyQ6iBS2Tm5Dh/YPDx8FGQTA+\r\nbtsfmYirzsATJl+HIIpkSpn3XkQ8E4++Ebd3wdG2o4bSCqOWv0fAfqOb/BJV\r\ntqtVmnx8FdRdquLn8xgkyaVjrdXrtH8iCNzGt5x2uaMC5gBddZJ8yPUYjgDV\r\nxUxNcObt+FjSVbNNnA8DvNaoXWVES3qnV2GLMwjnRb1eHok3wpKJaKvv5XHZ\r\nUUtd98wyRhRPXK89EAEJcbGLl30nyGVjNghuJIrU+cAMDSKIZ+2D8nM+YNO6\r\nYvtcdF9j0M2QZFA0vVpOwLGG/TogNhbEJ3C8BvhnRHQJ2soNBn2XgbIgMsvY\r\nmP4qEQn1BHveF8dX7ihB4czxeyf1y9VPu6mYl1tv48uI1GS7570cRLq9mCfk\r\nRjsq6EihQ3J2+tyjfs2A+dlaZZJ/5wlHFQQf16I13kSr3NTel090xSAFGWQP\r\ngosk13lk2JI9IKl7pkwFMNv6Oo6BDJZH8i/vKjEQKz1S+wwC5seS4NzJKU2b\r\nu/VV9+R7szQgLy6a3ah+PLrvmC59ZC1+1HUGFGgM89A9t5Mj753ExdwGvrp9\r\nh7E9oy20pZuDrORROSc0TDm7b0T3Dkl/aPE+TRdBmNTZKhuJ22LKsfkNYrim\r\n27l1qYNMJ0Q67QZHjaiTx+w8QM5QoFLJlBo=\r\n=8zNU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "5e6e2722de1bbf2b3329ca32ecea3e82f11dad24", "scripts": {"lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:knip": "knip --config knip.jsonc", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "prettier": "^2.8.3", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.30_1677030016758_0.6002168644693313", "host": "s3://npm-registry-packages"}}, "0.0.31": {"name": "ts-api-utils", "version": "0.0.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.31", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "709705026853e21118592f0af25fbc154309f33f", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.31.tgz", "fileCount": 6, "integrity": "sha512-Ku97IyJAhtryzxvuuT+qHKWl5gVymGppaAY32drZH3wzLOKEOKSLmcjNUKaJFyY3s5y0XETzbfhk9WSBkPWcbQ==", "signatures": [{"sig": "MEQCIAKmsxW9hd5rJLIUbL9CNklcNvSAWQxDFyN3P1hzkoXHAiAb/bAByO5BQ0wOaFu1fBWNHHuXLekXiFmryiy2BX59DA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9YZNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqqg//RVHyl9NiNh5MsWpi8YicsSK/R9vdKfU/WDb5PMiMCnBBjXcR\r\n6s3YX3Cjb9KKzzlpnbQ0D4nhE1sp9ru/UI26AvO8elGBvBvFvCVfIW2+La/u\r\nbDMmvrY41z35qRgsUpvt6uyK6b8BFOZDR8QdlF6VMsTUkUPKeuVhZOr5B2fA\r\neuBiytx1da4Lwb1R8fK8lk3JtuAWUb1kRaAziueG+8dRBXyRDbTOCao9ltbj\r\nrr/+lHP/r4hN9E6xCpNC05bPzOeILlX6Kb3qU8jO5sqeFOB511lRtBnNdUeQ\r\nic0UjSPQ6vAM7D4NtqHFyIRAby+KAz0HTNxrVXEzwypC/1UZR8lDsK4RK++T\r\nGrvdD1QH1C311nWdUPCD8pzjlf7fx0PtLkIh9Hz9kUKHNNi1njt2zGB6Rr+V\r\n6Q5rXoy52CASk8o2LxKnV1m9sF/X0dZhl1UVCExRn8O4IjVhKIb5rqRdx0xh\r\ntsaqHDAD29sL4RbANisEjxsdgPnN/a/qSzJ2MRf1QCJd7ORJ+lYPiwix/Qbo\r\noQUuccBMXvg6xoqyyY0HtCZsndqSXlD3nO6WCr97ON5GIpIcTi9vjYaJfcBa\r\nndPB6gYLSkV6qVAjIdSylSrR27Zm+DBiInvuCyLGtvvSvaUjFo0AvoPztbC8\r\niQrjChKWUo6/I839R59i561Md3+bVvR2Of0=\r\n=i164\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "b2f1c614fe208a2a04cd5753640168c0460590bb", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.31_1677035085821_0.344967719495169", "host": "s3://npm-registry-packages"}}, "0.0.32": {"name": "ts-api-utils", "version": "0.0.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.32", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "4b699970a9b499501f2b1ae7e77ea147c4efde79", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.32.tgz", "fileCount": 6, "integrity": "sha512-CszB+Q+x33+nyaBeK9fSDKAVYPHc4lFhAbPWGe7AovMTYvqOsjkEiZ4448Gc45ymTFmcPNNlnrjauT75wIDS0Q==", "signatures": [{"sig": "MEUCIQDWUWdI5eTiRdjPmc3Q/lqwCio+xvRo06kEVkX2L4Bc8QIgf4DDXRga2UAhovu4BxZDiHIqCZ5MtER/koB/OhCMi+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9YjyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGEQ/+PfXrMQihIPT05/+q7d06VFFFCxYWXbPN7MlQcSapqYydBjzc\r\nvg6pS8PPAZJ6HQ9Oe2axk95j8tHyDVHJ8LBzJZWrjAAXjegQ/Y6G7m52lPuP\r\nTffnFzRZxzd4rvZOeT1irfouEoRM/3wBWr9e6VSEofpMKP9Nw/SGdW3gDpE4\r\nPq4EfshAk6zaV+kBsCpp0nH41EyVBtdIikfmyziiBWXGShiOrn7LGyCugPgj\r\n1rWdGQt2hVUnKEJr/zivKHPPockUc9kc2fYjDew1a52y+y5guJ8oSlKDdl8q\r\nySOvpnMQOLqZTnZ/RFXAYJZcy8yh1378GpOmAu01aQaQb3o4qox1DpZQsmF0\r\nX3jJFGj49Q7HCDsplgCnsoiWJhfgLABT//etIuPNCSioQHIuLZ7w7QrBlStc\r\nRZJd+9orw5AiwX4Mc+yKYiQmqFnKPERtToueTWhYqmspoYhkPCOgHfPsYVXm\r\nLHn+vfDK+bTLV/53fzcGft6JDY5Q4iueQ/SUGeOejJK2g7rt7pqEwrJk3BFc\r\nOLVmdrFuFI+S92IGE2GNlftzdRogy924fKEuJ/NVedERXTanIqduWGoCg0HG\r\n5qPKaaVNkgC3J6As3SY+a2l/cX7QBlb2sPTbQ5AxryznU15KnMvSN6QAQdFr\r\n97hzqhJJaxJ6X10jWnsJXkroLCo3A50V14A=\r\n=vDcK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "022f57edf20702c1003fde3954c8b0f48e944a44", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.32_1677035762352_0.8951573257845027", "host": "s3://npm-registry-packages"}}, "0.0.33": {"name": "ts-api-utils", "version": "0.0.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.33", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "cd9ae5a34e6eb2f39538a500a542cd8a0e090b98", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.33.tgz", "fileCount": 6, "integrity": "sha512-gtfxw7dAqU8oaGtOq2EVB5XZE/Qgy1kKzxSFNcX38xVVl5blBCzCnqEQ2I3keUohNaPnCY83RQTBJTPxseDhdA==", "signatures": [{"sig": "MEQCIGBFyBAA9QAycpPZUA9DOjaUn2SeUzNMx7v0FtV9d9DwAiBQDxh0R7RzMUuiQ4PvxBFd7a/6DHAPd7g4nwY0EETlZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9brHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+8w//W40BYIkqKPUYcznYEr47zxeJxWCru8Fy1v8xSoTQyFJNFFjx\r\n1K1sFkMlKw2Znr6+yX+DyhKIcKGuMDg0FYXrDtPm2HL4KUDU4/NPbl6zFMeA\r\nKy9bKCx4zh2SUDy/+sO7lsSHUzPkidNGTjaP5xtrMh3ph3RiAB2q7zbJlK2j\r\nngOEZiNOkqSNpyYUc0hXZbT8wcvZvZT65Nbb1KnakWNmZuL3ihIFE2qFelk9\r\naslZH9x/zYfUKPSB4Ux8GDQBqB63BfBv2HWJ33uiCFoI7c5jvmIwg+g9vVS6\r\nysDEXOWnLGWikd3qlNgDBQO2ZtCO4h9NF++EA67OFYAiVnKLzP4ETHAOOwk+\r\nB+TFA8YJf529lAzhm6tbioy1u/FqHtBoyZZpxBeG5qVcELJthu8nIxFT9aYF\r\nUNSjYnSSAxOnr82Pr+IqTm7w55X/B98V7W1v7W8Du8NDH2ZG1pm1hvKOEnPh\r\nxNiOgOV27bcvP+NlrgZM7TxKASFmP+V0Dyoij/ArEyCEoY0uZN5hZftolmSv\r\nJ1fnr+OCIH79eDFdhJ8QFQVQ8rD2hJcYow7eikIyjUQ3Gv5v1VCgNSX4J+LW\r\nnP03GfjBbHwHvHouUw01xFVOKXstmCT3Ud5YVjMwBADOPd40n96rdf7eiPE7\r\ntD3JmQ1BdSs+diIhwVmIlSeGxYu3h/+IAEE=\r\n=Lsjw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "9a04aed04d9a135fa9047b43c8c07494285e2d28", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.33_1677048518943_0.9055216815771572", "host": "s3://npm-registry-packages"}}, "0.0.34": {"name": "ts-api-utils", "version": "0.0.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.34", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "02a23eaeecd690e6df38f2ee1941eb150dd90e8b", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.34.tgz", "fileCount": 6, "integrity": "sha512-pnaCt5AEhJTV7gOLdIeRdGkMn6xLLLMN2T/RIe5nMxLd/XiW8cdI0pG3v2Ru9QnB6QZ0A1OPO3PqJlcNWx2KSg==", "signatures": [{"sig": "MEYCIQDG94Ifm61F+ycvtFjHo00IHFRvK9vKR20G3g7w4KGrGAIhALhSGugfVeGzBpFqBdj5+pjXS5dxX0RZQZuwscYlD+IW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9cOBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIpRAAiocJ2VTDkafeY1j4+Q56fe2Iaci68c4cutwA+eaI+BXA3Zuo\r\nErIT4wtamsnfHSOcOQdgoXkJPu0D/0MRjm5GEps5wbEw9CXbkRjBSjubddwj\r\nxyxyxy5W520A34sz4XFW76rHY6xaRaqEj6NIRZ7WbzpTJzJgamr5o4uUHkG2\r\nffCz2npSR/Ruc7+jFcx7q7Rc8+eEiE/2wKDUWaIysTctPTG+Qv9dDFkUxZeH\r\nAmXL4MP0QGguXtCuSd4A9B1lq+Cdmmgn2KtaDM2GftV+brWTNl7jdrRgM5QO\r\nBWBi5MvDfX289WD03BZ12aAeCl0D5VapEeYkWO/lMMrjsGcSXbAHAK7Fqxbf\r\nWT9y87+dtZEvsYPwMp4OOsSGqqIIBudHjhPjHmU/19uC2dT23uCDARqhk2Tv\r\nyCAP4Q+Gr70gDKb9TE22BoHRnW6Y4StRSq6zqXZCRz31ZmETGxrw9rhzCMNc\r\nk5FbStal1uEojZdNy6m5AvDnQcIPlITQpTNxkinEPLSPO9GqMg19INMljuFN\r\n7xuDNqRTIFHcdTaenj7UOh3sGOWfV4BjmQA1kOwEp0YOBvmwd7xk/jGKE6i+\r\nXiVE4CovmYfB/KmKpY/36nFAuXNGoXIIGtzkI5E0tni+aSei2yCOQ+Amif+t\r\nAAiRDsq+WR8YpEDh8Ns55/7ILgEeL3PfAiA=\r\n=nuuL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "559bd8c8db4856adda8a7faf73128af82233112b", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.34_1677050752899_0.9160992972768081", "host": "s3://npm-registry-packages"}}, "0.0.35": {"name": "ts-api-utils", "version": "0.0.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.35", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "79cbe99f1f838b43f76a512b650a0ee1f5a80448", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.35.tgz", "fileCount": 6, "integrity": "sha512-0SBWUtlzebrWknYgeDj0FbtZRU454uh6xTog6NO+B05Gjkhyc0ivFN8v3xi9PiFgK+NtQ+an3DTQeopkN84KxA==", "signatures": [{"sig": "MEUCIQDsQ4Xb32qekn6FAcR/CzZzTVI9ENLL6w/NGh3Lwxo3+wIgbdoI6k8OTwZphHGPBRIQdXnYS42ZX10QUXEdRM3XDyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9cUQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvWA//SJKuUkKAFmsl+ls4Zr6NiXFpYAbO1dDcVP1CgikG00CB5je/\r\nJLN3+PEX2BpEWh7wKKhjt/STQjITJBRk0z5tLlCmTOv0JOzxWhzw4QnOHgZ+\r\nyp+acPXICtJ92aSWpU+HCOTHE+K+ZPh5ULxmiUJzeFfPKZP53biixzx6gp22\r\nMOr60wvAx3eZ+u9GMGUIgJumlut4bzP+SIG2zVKDesBYKB8OfJJzgg/2gyWK\r\nOMnuj7PVuQVJ99qFk4dV004gca2vOSJ1UT+FKNcpA8Yxzp/vtFO4vo4htV5h\r\njGgGI32Mk/Nlp5k0z7it+F4jNk9sfYOkfaf4m9ssrLJRQuDeMurYxBDFIIpW\r\n6iEuuv76ZwtFOgJsZlplyOWpYTIQ6JGIZAMv2XWJNoN0KczsDavA9mGItR5r\r\niLTgSVS0oFxLM+W3EqtVv2hBvRTsHn00iXwRJHjE4bsSx3N+4u5w5zroyaj9\r\ngJN8T45WjuC1j+UurSJ9UyjyevqqjuJoxD7Bj2m6myoGihcyk7+Yv8v1+qHo\r\noFle3/q/LiJ1PpB9hZZByRX3eeZFzwVjeLYn4RgfLg4O7wZbMSQshWaL6HMH\r\noJX7qLGwuHpv/cPluXe/QBPbbn3yIjtF8AT7cQ15nGKVfkCKW8AG4SkKJVQF\r\nHjvWpUosA9tputcF/jVG8TtidgeOFIwfuVo=\r\n=UFDG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "a7ff587c8712d924f333950bbd206b5c04c4a700", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.35_1677051152359_0.026528310821133605", "host": "s3://npm-registry-packages"}}, "0.0.36": {"name": "ts-api-utils", "version": "0.0.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.36", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "0da964671cd013d8e5975289e3d9734bbf2d834d", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.36.tgz", "fileCount": 6, "integrity": "sha512-74A2X3WGPap/yUIMh/NV/pGEx1MnfV1jjDFvBetfOOPDDYX/5g43u7iGvptRg5Xga4TVqPUTtBu9kBCL0fUb1Q==", "signatures": [{"sig": "MEUCIQChg3QiLv5xTg/Hqz+O2jQEX+Ig+a20yx6Ymnx6wh/ggwIgKTk+gyWSoYBFZaPqpfoiybTcNsGVwkJTi08K59Qhdt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9c7jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5sA/+PWnZ3vzwevhUBnaGnsZyOQIZ99+HroBz1U5cHkKsoToEvqX9\r\n6DTidOtvskmlDXlCP2ggMCq78SC1vAwT+RbAhuG/x+6O+kv2ds+gxH1Pv1kg\r\nPlMFEO/ZkDN+1/ybyZS5kIBQMgqX0uH06AzgVPGcEW5FYRBgnc6N5cgNqcWA\r\naUK2qRz6dPx1oZpoYR7aV3cXO0xyO4BvtUJJaFYXZpszKq7mWvYgp7BAGvV9\r\n5Ksz7EflxGYnMOTWXJlyS7uwqmrJpBy8sY6ljGErEFxYJuImk55KbXW5YcTw\r\nYeKVLRcbEhyZEin/nKfiWflnQGg5JMatUEScjOc3qrjUziNB9OGkEE6dCZR4\r\nA74YvEKznZMWAp+Ja118bA6fe1dxCn8U7rBGHLrVf9ady9LrPlCJgG+GQS7Q\r\nBXCId1p+BM0wstXJB8pctyW5YoiqWMXNE/VXVEcf6HZ8gTmslB3KFvAPGegK\r\n7RjlNGkvnQOwg97N9bSTmo7hzIiFG9gxgxp3lgBnu8O2ZRpdKzoA0oozAxG0\r\nAvHgEoduOwJ0PViMxVFUzgHW1NXkhvpLEdEkem3kIh8JFSBasb0fBuIwt5Ag\r\nw4OEu5pihzT+dSzD8Pmp6UKvRQlVl2VCX7HbxpwGd7cGrgH4eiu8MeMrcQeL\r\n0ssctKmFFMiaQFFfYNVN91P0pkP7FBMti1Y=\r\n=lezC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "b36740e13b3c529f92d025b2372d480a26f0cee1", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.36_1677053667512_0.7442339712295691", "host": "s3://npm-registry-packages"}}, "0.0.37": {"name": "ts-api-utils", "version": "0.0.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.37", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "6bc34a4d62f299285a82fbeb71e125b9cfa5690f", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.37.tgz", "fileCount": 6, "integrity": "sha512-wO<PERSON>eeGcPTBonS8kBpd8wnqs3stJgMS0DlbE2KosNUW3vNFlcxNJCWRllugbZkUQwETJK2p3BPZTIOCUFTnxyw==", "signatures": [{"sig": "MEYCIQDSbnZe6tJoXEmzQQDgcZ3INZ0q9v56+rAEphBRyumbbQIhAJT+ibaO+okZ4a5FlOahbdhG4V6eQdULmILKAp6pEWrZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9dIFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Pg/9FQNCadE9L2yiTS4rXRQZJmEVhPKwAADzLuvaRLPVYwVEb732\r\nP43GpxZ+WlqRGjxLiQ/mRqpHB3JL8fZMEQgpuosCmVRfe8iIQLAiaGvnxnv1\r\naeS2MnAcj/jAO/NLbFW6XVSStpw0PBK4rbwqdGGUfM2PXa190u2L1uPspcZ0\r\nkig8cXcy3mjuepHELzWtycgPijfOlkRzBRA8YEVqOZqCBcht0bUbGtnXfgs3\r\nteyeWpiACWMIlnn8/QpKzXiL24Pj2Rg2Mu0rcNfrckZxygn6JCgBjQJzmCIi\r\nNQ/E2GUZf4QSRozHuneQulPna4aE9nY69GT6BdiVaqbmNMTysuDso4KQCwbn\r\nfTpKTkc5YQ0Ubt6XOM/RId2IzuFL+QHGlRSz3d21wnTC/2l7XWn6wCPX+Nek\r\nJFvMjT0wfBijihYcNdsx/x76JUc2rEp9PTYOsfAcE6PX/Y7FDOVD95OBrOaV\r\n/sTJibVuf6ld6sLJGeU3rEvin2scpfagNTXs7Vg89oi7Oy9W/FMYmafDRObn\r\nY5DoRwYGANZuTVEL/XhGu411ameb/SpSJRV7jtTMkgi2mGIDB3FlkQB0mzel\r\nvvSeZLUkN8PCTR+qSxzQwNFzzwgXDIekJei/Zo5I1JoW6JMAwPfk/mVKxllf\r\nTcOzPAY48lkYTX616RpVHvPHDB0LbGtVaNQ=\r\n=QPh+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "dcae97477a969f2e97de5a73909f0d3b5db45515", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.37_1677054469619_0.9802672564475288", "host": "s3://npm-registry-packages"}}, "0.0.38": {"name": "ts-api-utils", "version": "0.0.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.38", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "f308292bc86a030b9714e138ed803981b39a582a", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.38.tgz", "fileCount": 6, "integrity": "sha512-DAXj54DsqWCQd8kLhB/JDYRbhpx5bENWSxKv739sTiE7XtTozR7ge3n3fZ/xooirKITEd/4N/gMINvdPoI8nnQ==", "signatures": [{"sig": "MEQCIE4lgY3RV89nwwb5W2oxHk9AdnOvnroT/1lU41+9FccOAiBuKcTqsVnv+025a3cQiTrA7aOLFiX77lU02D9rcEOtIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9dPFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqt2Q/+Pr+x0oEX07ZL3pin3yppny0RhDgMEiHFw+PJfSOGSu8v4wsP\r\nWwTKcg8zLnfcmUZwBzeAHLzWRZnE+YOUjVUXa3KtnisYGhcvhUzcbgp0bqFO\r\nfxoafcbmh7mdvIgNrPK2tXCh8GAnPdn2E9i+1MhidWu28NZCTn1K7/uC1FZ+\r\nkv3sKqDQOPAzFl2c2AI2X5P5l8q+Ee4ncHpqYqLKjq3axgnvbL/PDaEaAmZs\r\n/g27J0pVVNpVlY9nQ3pL2viB8/t81X+cVRMV6aqBg7XCj/TspTp9nNzVsuLr\r\nsSw646zQQukU+p9RbVIXdsAuBdSvw//u4PEjqb90Js7OXEfCx2x++1Teb/Dp\r\n4jz8jgYUv4IOkwhMPWSWT/RHydQoLNCngfeEb8mIILXfx48rqXV6VscovEfj\r\nvUo22EsnQKLHpxBozAIWm8ypRVAxwDy1btdToCXPBklo1Lrk0WQLfPsjAZ3/\r\nZdbXW8zK2m5uG1Gwantmu+J+aBHsOB09v7Vi005QkKh/OqxyNGwcFf3pSoBQ\r\n+H0qQHYGL9ay2w7p4SEK6CYAC9pEKx60QKu+r/b8Y9YvW83ZhWGI3U2lugNC\r\nnUo4HsiJferlwfBpqgJZNhKkB7WXqUrqUC6xgJNo6xbbb82Aot6eMwc6FYse\r\nY7EuhKHZq9tODD9rf/qZtdjhz89mPrOXRHY=\r\n=HFMr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "2c5cf36cd99318c2494d98c29842319418f192d0", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.34", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.38_1677054916871_0.6346795169188688", "host": "s3://npm-registry-packages"}}, "0.0.39": {"name": "ts-api-utils", "version": "0.0.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.39", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "cd3d2dd88a68cbc8259cf9944fbd4dbd19f1d69c", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.39.tgz", "fileCount": 7, "integrity": "sha512-JSEMbhv2bJCgJUhN/6y58Om6k7rmZ7BwJsklWwv0srfMc7HkhnfHA1sGpGltS6VSTMT4PVEqj/IVsCAPcU1l/g==", "signatures": [{"sig": "MEQCICA6G2F9D2XggEKGuVWtxiVepm6eh9SvNm3hPcIsMl6XAiBlrPlIgqlZ5+8JnXEaUUBjdMJkruH5DZ+kJ8dJTdf80A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9jcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovMBAAm8NhEe45szEhyRov9IOxVsdkB4hlDB90fDxOD16flDDglebP\r\nI1h9R1cvhY6IF7pZC9XH+lU5TXowoD0ABqYNUPDJS2D5SszAfph5QCRsykfS\r\nEQBW9lFLscrCPLTqIhQYXwXA9E/EuVgMyrkFUoboXLWducQLjgNvn8lTW9DU\r\ndCZ6ImcuthkYElrWs13vTk2jY6BR1m7SO0iBKRGVLBSZCTuko7E1TXwYiutj\r\nu9eSxr1xZNEp49JFcsVOh6b6oTKP1DjwH0BWvrQslJYaDQYTSV+d0ZETfsLu\r\n3wgq9/CgmRkIzPj8O4NIc/pSWb85a7KJR/x/5Dz1Du3G20IGNtnl/+LKvHh5\r\njq5Jp0/BuK5VwsemgDDbW798EYhX4LBlC5IxcDRlxhTq6VjgkB2NTrXyJWnS\r\nppJn4LXywalDKG22uVXgNfwN6ynIlBMuZXx9nfxk5AcmGqvfUM82ntYVWDZT\r\n1mVA5158KITkHG/v619QHAdWhvfmkWTRbKktsAe0UzgHld/DZUDwW5pAm/uQ\r\nDAbYu5UCznNgCEIcwqnqrhyZ1WC7CH4QzIel9gWtBqoBDxXcG+l9SSn6DRv5\r\nohWj0oY+qXD+doG+6DPSfVzYmlQ78H9Wg0EkYuJ0//Thy8WJ52jJaPq7P3R7\r\nKO3fhOoCXOrp2l496ciCaCth27AcR8OpNVM=\r\n=86Ap\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "329ff55810b2e6d8c96ebd7b76172570cfcc9840", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.1", "devDependencies": {"knip": "^1.12.3", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.44", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.39_1677080354751_0.5593463366075684", "host": "s3://npm-registry-packages"}}, "0.0.40": {"name": "ts-api-utils", "version": "0.0.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.40", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "6bc8009ac1026029c9f8084883e572706ca6d2f0", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.40.tgz", "fileCount": 7, "integrity": "sha512-08HZoQqNVfEsy1+nfEps9QJPbeb/PAFDyFZEw21MqXzC0Tf6hsMe/Ir/uDuuzBtzMLZNlG5AMqUPJ//EVlSVIw==", "signatures": [{"sig": "MEUCIQDeyrEO8ideXArspPOQdG3PQ1JfHrEwfs6do4KeWjg28QIgWyro9M2BX2P483O+/AR8Yb8rILUY85f09kZPtYUeyXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkASA1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSKg/7BgkvyM7wJVAAmfmaUdZ1XGTdZgsBOVCWSxoN+tMGtXoIMww8\r\n+OoVZvapk3/1hDuZF5hhIoEFgDYhOL5iRsrQJIBTcW9s0qZSM/ncsj8cn5k3\r\nfuM11nbas9wHlObl6ZOFxQ4M52tAjSXtr3xozXoIvrv3+itVTdPDPhXfjEA4\r\n8ktceecKhBGjI4dj2vmdvhcEIbDEuWkf+K81BKqohfew2PjFHBV+noFbkieh\r\nS/d2r+99PSXASPECQZgUVCrjyVP2gOtDl+myynzOrvpm4I9h+lOv2ByH1uqo\r\nBRbDFHLZmT4IQr5KxztdZt7tbW4hP/aTXiJqctiGtMem6mdmGuaKpbOox0DI\r\ncpJXfErjECEfvm4L7cEe5spNz2EbvrSrgL/FD46BjW+5IF8PojgjLAgaYz5F\r\nFDN1mVHpoOhlQIcM7T+GP9XM7QmEge2Ta2kcV2zjrsc5eAwz9cncePpw10fp\r\nsZo/76xS6vhzJhXBsybx5rvaHAIiR/fWiIphFRlvFsjC4VR7K+V0bRT5tUuN\r\nClHKPC2E8iJGbET4eiwR6YHQaaKbJPeCZFsDK2TZhsmFWw/YJ0WrjEN+MxQR\r\nUSsMICG8yNuONdVCzLj1sB4n97IAVwLAMorywNe7KVZrWhj7yiBfOKEuRwkN\r\ne3Vv6M3WfY8KJQrrUtVFwf5mCZ9Lt2criS0=\r\n=1apZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "f334104bd0c0efb59b51287c939351cf592d61e7", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.28.0", "devDependencies": {"knip": "^1.14.1", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.28.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.48", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.40_1677795381694_0.5941409242807714", "host": "s3://npm-registry-packages"}}, "0.0.41": {"name": "ts-api-utils", "version": "0.0.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.41", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "59e2b628bbceab63b61e1a9b1607548320695deb", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.41.tgz", "fileCount": 7, "integrity": "sha512-XMhA89YIrbhjU4bkHwUv+gW6k8KdMApHgCP+5tZAB7lwsw+6OTBk0ovdJ/aZjpkD4Y/V5bI8FzUvUwW2aVN8Eg==", "signatures": [{"sig": "MEYCIQDRYjzw/Lhk+x01jorAHYHjjC1DKHqeZmUttJbIPIxwfwIhAI/llox72pYD/z7XeLNf2duVR/UXnuBJE3rq83RUgJyz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkASPoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxRRAAkrbulMpvW/nCEfbjjG0BWwp03QldFGF4d6isCMpJLsszv4G6\r\ntM1OoizK1h+ibCN0txZtRV1iSzVZV0IF6SZ0IQgdyDvsCGNaU+aXG7Nmckzw\r\nuiyrHfb5/OO+UityVjF0ALv5/4zoBuKlC23D1KxZjPSYI9qWmcMj4eyTLuDr\r\nMigkXuohz12xZoOrn4kL5m/9wtJWFwFTpvKbtUfVknJT900x3WlI8hMTAX4j\r\nRvYTtMb8Hg8a9va0lrtJNtHTKIpyiqL6cu57Mm1khqx+3VRxDf9IXqxTl7jO\r\nAjTEO7mnweEvLYiB5DsPu8q2hHN785I7ZmqDx4oHShdxqw4/t5FkGOnHAPwX\r\nxTJ0rG7L4Pw5OMjmY6Vi/1oZ1ibi35OEZ87VMRG8WjQAI7IBRv7Ds481d1ox\r\nY19D0Lymj4ehzoIwMekUBFX+OA5v//Mp61fC7R83/bnHKNZC3Nuk58xSyAZy\r\ngNaU52+XtrxZEURaidJ8SOuQCr7B6v4EKDv5SPFDUdX8bN+yefLCxMVsrnqy\r\n43qfreSefF0CJ+K0PwT9myHbQUYBuv+4L87N+G1FDq8p7nr36DPaKxKYs2z1\r\nPpBsTEk24COP+6ttWDEsaedxjASR9UdT/jnsWdkBPPtfVUgvU7k2B2TQNRTk\r\ncayUeAyvjFsfpEWvtChh2IM0t8fhucRAkh8=\r\n=3oT+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "3af65e18a58c4c050a587f11d62605d2470c4f9d", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.28.0", "devDependencies": {"knip": "^1.14.1", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.29.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.48", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.41_1677796328347_0.4193430668159399", "host": "s3://npm-registry-packages"}}, "0.0.42": {"name": "ts-api-utils", "version": "0.0.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.42", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "9d6582fd23bfc1ec141d289c717b088339a6c2fb", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.42.tgz", "fileCount": 7, "integrity": "sha512-8VsNoVhpJws//7H1qCyjUBYu9pQloso+/Djr8OeePu8lq/qgd7TFWTEvfXp+k8P4dkF97GyT73eve36z6Oc7dg==", "signatures": [{"sig": "MEUCIQDUFa9vkvlOVeJoc2AhNpYZoJjfCiukmXsdJrTxanfDOgIgVcAJd4IAlXh4Njvq9U3weqkvkfY0KXSaOPEIRj1whSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkASWcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwZw//R3DPv/WnWnpFbkV1ZTZmk0h7GS+9LQ5ukY0QMUN6Nv9y0JiK\r\nxLAo0NAE/pxb4Y0U2WQtYJuN5fIAYkEAvrKHauRZpBLOGwewt9DX6sx+T1gt\r\n8Cyp7j7F+xLP3wI3inEfqihIH0BxOkjiOW6FCzE+DJIq2bpcf7JC8wnGk6P8\r\n4MEmrc7HdlVP5xXbq9N4dZsnePC6mnaPEu/RJOFV5CD0u8VxjzE5NpCo84am\r\nxnI7ZDf891wENagW6eO+bavnAuPX5nGhin7axIYJPppOzCZWxyiHwyeb32DA\r\n/ncfNjmX53InuhSqfjLL7rx1Oa9aKwK0aBcc8Rz0XDyg1s3Nr7Elh12lp2VS\r\nchdKCtnAb4raG8Dkj3SWc6wQpq5kpoUiWTT2lWVfR9FtFm5wlcHd9iZzXv8a\r\n27eF0WE+3/F9TJxT5VpoBOmR9/rr1k2YaKiYToAKmAqdGUfZkQcUdUHGa7lP\r\nExG05idPYkmUgLazCMxJdcS4Hl+Fs031sO5KQV2piEWDnuMVKLayLVZ7/Ww8\r\nWZlXB2FTQ0SYrN1vjpzqy5KISmK6hKP/SLSg6APqhu6F1kHiLMWy4oG2pOI/\r\n/woTrVyxOJmi+M6HxodhFRU5KFxepVkfaN3eXya4XDdKsieEPqhEgGeYFdY4\r\noXdpOotp580LHca4kwwAmEUFHJBaO6Njrc4=\r\n=WQYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "a3359fde8153d8f58e365ed12e4c1ecc2319b53d", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.28.0", "devDependencies": {"knip": "^1.14.1", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.29.0", "typedoc": "^0.23.25", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.48", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.28.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.42_1677796764762_0.5532685925715233", "host": "s3://npm-registry-packages"}}, "0.0.43": {"name": "ts-api-utils", "version": "0.0.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.43", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "41e47fdcd8fdeeaa47dc990fd099524f7e95dbd9", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.43.tgz", "fileCount": 7, "integrity": "sha512-CfqnrD7A2nJsozQ0WgJ5dMLQ7M+5/LBlr1Wgv1Wp4GKKBQwIVr+eTSTvrvWyzqB5H5V8vKsSDd3DcNxluIwE9g==", "signatures": [{"sig": "MEUCIHKI5BbxNhdQCW+L5PS4z2yRVALJqR/L8W+NY0cv7j+kAiEAgGnbC73yxzSITzRG7ypXsezPHt4Sb+Lvk0aenq2/MgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 256149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDqDKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnqQ//d7LlNngohwCG7yxhNWP/roIA2kcn/qtTVkwVFVEGxczwgVku\r\n0KP7U14V+yoRVXpXBG+CGynh1bUJ+Brtk2IgkXuHLZhHb2Gh84HsVSsP0KiA\r\nuRg/Fb+l8k4+AOlHJ62tNkcbZLy5XkWKBK3LHuL8CwNlnYFhp80432bk9MtP\r\n/YAS0KbjoSie5VozZxx5/le4KDRYhcs525dPw44K1NG5wcgcqgkY4fv78dBU\r\neXUWx65ajFQpKJt/hpH8QM0QOqK3YlrAP1cVzT0wNBdefSlPkl4+tdG9bBN1\r\n0t0vaZNW0gufrpQ97w2N/RSR64wS9MyJWGyC91LqKVoQpcVnQeuQSHXk6AYk\r\nhm50tUhIOnVBaM0m0V3j+DPb8hhljwVvvNXa7Bu00AlypFUj7yR+1AoVY1o6\r\ntjIVe2+RabqbCU0wZ1kcGd0xCw+9Uebt97TVaCZ3onIbKupLiC4Y48LNzoKs\r\nkLNNgI5muP7jOtgDK3MvytkKhTtjfxtztl+d526zO2JxXIyJVDbtByqH0PTl\r\nN4vIHENgy5SsvHiMzbGQl0ZygJJx6C886ueT3Twf4IAGM+dv+O7K5VVOUAN7\r\nxFNVZpURAOiub0sIv7YSb1Rqrv4ZtIg4KDxDw/CZKhXMsFmiL9w614b9W9Pz\r\nrC0hOSNFrTAL1H98wVZ3GdNiuy5N1lH6qRA=\r\n=v/xh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "cf3a665330db4bd24e9f69080b7ff8d1f9723451", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.1", "devDependencies": {"knip": "^1.14.1", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.29.0", "typedoc": "^0.23.26", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^40.0.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.54", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^2.0.2", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.29.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.43_1678680266734_0.8997180341969773", "host": "s3://npm-registry-packages"}}, "0.0.44": {"name": "ts-api-utils", "version": "0.0.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.44", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "4e6582ecad2fa141cc31d62f22ac590a5ff6eb05", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.44.tgz", "fileCount": 7, "integrity": "sha512-pYeAeynJqwgxewln4Ok+nVtiYqhfQW3MI2VFB+lLmhuf1fGjAJ5fSOcrqjwD6+lZjeGpMdVg4vKQsPtI0Mlikg==", "signatures": [{"sig": "MEUCIQDNIT/eobG7K5+My2EqPOYDMdgh5o15X78GVkzRH87cOgIgNge7Oi9oQ5lVZcG6wz45oWk1Ri+jgcX9VOMSgOzE2m8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEw/MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgZQ/+N4gMIJ3U7Vb0hEVpLXffCTltM6gmdHjJKA8zqij9ZQwp84Fm\r\nV6N4mfFCa6TAtjTBVda1cWQ5i8iKVtN7Kv3v8OTlJdHBEa05GMAG8LR/F9/M\r\ng8mJHMINVNB4Ykxa1N7vroGe+PSMC09OrKgHwYAV6A0et9PqApLacKIlJFha\r\nblth0TTln9qWDnUqU2IEOzYPkblH26AmxGVlgDvA2/j2bKlQIFyBd3e+ayfT\r\nDjaIjhjRn2BsXphP2AYCmYVgwYLPXKYnqKfBx7nxAcTSnL5pQFJKJLnr/dne\r\nBnD8+WJoVQz4LpAUVk6DbuOGWiA1XvVdJ+FGOplqLBiJ4bnyJKbqjLZtMP5k\r\nyD2tUB/TgqaAbwJQ3j8iYbdaUNu+iAMWvl9cGKKaZ8Ci64p0Am3Co/CYX9rq\r\nqEIcPjLn/b4Xn6h9R90R5J0JGIN+CwJ5gSjQ+xUjMmuDqBtwMkDgdvN/5xEm\r\n7LYAvw3lS77UGf8h4AnFFTBdN6b6yGL47+DyATFb7rdNp3zoeCBzgmYM95UN\r\nubIvFv1tuNuqApzDTmkpPREDqc0u1cNoMAvGLAVjeKoz4r7sSiSNosmAVYE1\r\nWwSqv3aPt3R/GrbTE3+4Jwu0Coxr4LosLw4PkbnlYjB/Ib4lHRU1B6MWMT6k\r\nj8i6AHtQsX27BEUI+R+Dt5gZsi5XdAbpOQE=\r\n=w8il\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "d05db9a355c4dde500358952981e8dfbbb950c5e", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.1", "devDependencies": {"knip": "^1.14.1", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.29.0", "typedoc": "^0.23.27", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "4.9.5", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^40.0.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.54", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.29.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.44_1678970828242_0.755819741286968", "host": "s3://npm-registry-packages"}}, "0.0.45": {"name": "ts-api-utils", "version": "0.0.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.45", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "0053f357c1ac8837569cae3b67d4614055c70bec", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.45.tgz", "fileCount": 7, "integrity": "sha512-N8znOZojKVoK8pIJ0H/Rkhzb1Rm2JGLqNHlkqLlAur9qe3/pArjparrNg04w54VHRUhRUrAtUwCy1wmoueRjMA==", "signatures": [{"sig": "MEUCIAEvV5U0hksxY8Dt8pCLlsi7kt2w2p2sP5D3Uj2rX6k9AiEAnS+PW0Iw0ZYssLKX+U6UnekX0w8XT1GDzzC4I6AmTJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGFDwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpViw/8CvfzkGJ8PuxC5aU4QUGmX+NyHPeLVqCTMXKeZFDqqj0GL6k8\r\ntmwYWcUSByPdnOzxAgAK7k2M6Y7+XDlIAvdBxj/6U1d8+ZjWqiJB29lTMl6i\r\n/KJ+AW0kgQ58tvlNb+XnoMsLg6JWvaves0rfmO4j1vNjlZPC7EuhQM4dbv9l\r\n9O7ywcnW6VTSeB3qpGKWVy4tlHsUJT34L/e/setLV1jyRSaliFVvtN7hdAPx\r\nGL6osXeSz651Hmy8jDy/M1mv+cDX6RuAfGXgNtv2d5cTQS/ZsRXlwbIUfImC\r\nwbRc7rLw7ghawJpdRvhYNy3/dkMEOBLojzttrWzLiPB/xyW3vXF6+8YV/Cq6\r\n/u8A0Bh1fmXjg2sArad7um6212b4SlNjjkgEGbc9XReil+1VoinC6qmGX1Wz\r\nAEQ3/CoyTz/nca2fft8KZ6kdX0Xs2eSMg5ec1nWBiJPDOj6+FdoaYspNjOiZ\r\nnzY4gVefhKqQLbg/cSOv8QVrlc22l4LXJ1WtqfHRN4ucAwf6+4TvYtoRGdu5\r\nL8g0UvwWWv33cDiJj7r2u808pf8+dJ2qe1M+bmj/PrRPYdFGncdEubxJvpN9\r\niq+PeFU+3kK1sPIFbUcH5dRDyouTE+nMFKwzIRNZrioCO8cu6U5z/6IHUx5n\r\ndqmTArA8nzErZR9TmjgkB0Co+jqcfgcTcoo=\r\n=BRSt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "6050cfe20dcd07ac37b03d69c38801d1ce95ccb7", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.1", "devDependencies": {"knip": "^1.14.1", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.29.0", "typedoc": "^0.23.27", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.27.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^40.0.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.54", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.29.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.45_1679315183982_0.21787371522422738", "host": "s3://npm-registry-packages"}}, "0.0.46": {"name": "ts-api-utils", "version": "0.0.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.0.46", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "4458cb3f8fe2c68409043bd3f9d2fc1c6cf155ae", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.0.46.tgz", "fileCount": 7, "integrity": "sha512-YKJeSx39n0mMk+hrpyHKyTgxA3s7Pz/j1cXYR+t8HcwwZupzOR5xDGKnOEw3gmLaUeFUQt3FJD39AH9Ajn/mdA==", "signatures": [{"sig": "MEQCIE3gU2U2DLzPSO4AnqxYHwRmAvPpl8iFIN+gBNuZNTV4AiA30IJVoDTOmUnmYZGpfYnqthUU9zlSLEsjWrRre+cdiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJ9IWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBvRAAgnj3H56YjPp3sB4dsaXOHriW8utiN+4OyVpGPN/rG/w2/cHh\r\n23CufloEzaKnRcvBmFlP7uS65T0y+wkCl9xjGfs8QVSYqQwYindvhQMyagsy\r\nAfCbVRCORgUzHIg6ujqRurb4lk15UI1IY12scr6Lg1eHIzjiJJh6CRZX2VxJ\r\nYvweEhWJanUISevzpyaVmiGwZX7Fquelb4zFitrO5Ha2VkjOH4JRpED2bsr4\r\nlGZApjSOQeJvdgzLFsYDXl1gJWHOXU3PWf3icefSDqnqFgetOv5B0p8su3/V\r\nqike7mFuGBJUx2nhp7NJXGTrJHyUCXG/RrLUabENq4VZ+BTWRJBMXf9GE73T\r\ncZeiR5f5LT9p3+GfvjYFH42O7G4bCsmmdX8IXyLRfCadhTsJI9xxTizucUiB\r\ny/+MItuRCVUndzJsDj3Q/pUaw02AVL13XkqyW9zUmdhvdTEZjFttlR+tqPxx\r\nmQtltttnRf1guo0zejTGiULOORh8mkGfcwGQRj4FNYgzK2W/ts2MTOoN4fXA\r\nQbpX55XgGDIlwApdvA3ggf39pnUar4VE/NLizLoCKPqoakMwveSS/MVdqAfE\r\nXY2dUdzE8FGe6mLe1bsMJJlurXGpz1VF9xz+YxJq6dM3d1IXeFDoYsZNU9Z8\r\nBXz/l+JwRzEP/bfQaCvGTyZaB7o2g2WJmVY=\r\n=yKuF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "4bc7868147ed4916c65ec95936f84165eff85b5e", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.30.5", "devDependencies": {"knip": "^2.0.0", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.29.0", "typedoc": "^0.23.27", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.28.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.33.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^40.0.1", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.0.57", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.0.4", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.29.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.0.46_1680331286653_0.0008598181750099076", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "ts-api-utils", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.1.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "d933a9b69aa0842f9cf3a068ca08d65bdfce9380", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-rIBl7rFfTuj/gcsi0AU1UkZcCaoIGKC/tkHMBbbpTeHOaKaXxv4B8Ddy6tvRw6GOl0fsFz16RAM5w/o3cC84Ag==", "signatures": [{"sig": "MEUCIQD4qpk40WoR56W/5SX+9YoihLmNAszyiF/EnTL7p/S+4gIgM2wWMlr7u5q4A6l4L0H6Ji8CkTan5FnPQrQAt8vD5C8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283347}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "ad091f6b127828ba49b1905fb4f46ca331a888a2", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.33.0", "devDependencies": {"knip": "^2.0.0", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.32.0", "typedoc": "^0.24.0", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.29.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.34.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^41.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.2.0", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.32.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.1.0_1686848813349_0.0037421007769253833", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "ts-api-utils", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@0.2.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "1288a3bb46c852434ba2b2db43ec9a4798f4782d", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-y2FrbOFgfysH8BaNPF08PSeyYiZcKIb8gqv7up7bOMUy1Zk24iRm4KJAOmgDEH3VzrGYfPZWHMSElK2aZavfRA==", "signatures": [{"sig": "MEUCIGJDGyUyn+hJWpZ2amVvgs00nyHxHs3P7b8EFD9d1SSdAiEAifCzvFR+2FZm5B8Au30IBMzsU3UhZQTieHZcRKqrVg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283259}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "279f843985fb75fb938eaf98d27dcdcb8a612a2c", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.33.1", "devDependencies": {"knip": "^2.0.0", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.32.0", "typedoc": "^0.24.0", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.29.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.34.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^41.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.2.0", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.32.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_0.2.0_1686848877813_0.3026286401435798", "host": "s3://npm-registry-packages"}}, "1.0.0-beta": {"name": "ts-api-utils", "version": "1.0.0-beta", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.0.0-beta", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "bb2a35e1634164e0faf090a96ef0ea99e7403f40", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.0.0-beta.tgz", "fileCount": 7, "integrity": "sha512-8SyxpF3or2r5NeAzvdAEAHFAonq/GjcKsk7FYuCXe9DLZyDZcxSdTLyqW0GTu07/z34XYTpZ3hIFDWcDaUUagQ==", "signatures": [{"sig": "MEQCICuJKcKfPKdHcfxj8HU8VfLfrykoAJEZhd/nJ1XFy/6iAiAVYyfkXnJruNwdJ0K69M/rEszrVL0JsAGSc4jBq5E2wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283264}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "503f9fdae423e5e416f082e14dd0fff70f088880", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.33.1", "readmeFilename": "README.md", "devDependencies": {"knip": "^2.0.0", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.32.0", "typedoc": "^0.24.0", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.29.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.34.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^41.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.2.0", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.32.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.0.0-beta_1686849248961_0.7291629227789467", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "ts-api-utils", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.0.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "bec2b0f3af409e5acd547dbf1d14e8261459bc42", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-ycbj7cbgdeLc5i7xhxewYjWOoMzeVz4PiKvkWC/fVjfbt4ToHCvotIzD+GB1iYn1R+kaQG0JdET1ZNZwl4nXUQ==", "signatures": [{"sig": "MEUCIQCKcD7R2+k5KL3z1Qzn3FzeqqUqpFLZxNCZPqQki2EJ9wIgT04D1HG5ijTCwnoa76g/jfXMDwVM72wh2aAnTN/PZqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284379}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "a9b152813dcee3409fdd1f3d2d5e1485bb0ea073", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.3.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.33.1", "devDependencies": {"knip": "^2.0.0", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.32.0", "typedoc": "^0.24.0", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.29.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.34.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^41.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.2.0", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.32.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.0.0_1686869185464_0.8935170834866373", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "ts-api-utils", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.0.1", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "8144e811d44c749cd65b2da305a032510774452d", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-lC/RGlPmwdrIBFTX59wwNzqh7aR2otPNPR/5brHZm/XKFYKsfqxihXUe9pU3JI+3vGkl+vyCoNNnPhJn3aLK1A==", "signatures": [{"sig": "MEYCIQCy3Bm0T1hPkUutdJdrj3iwmcbdmyghNmYMVfFAS823bgIhAIuaXYUghAMZshMBhSxeBEk9iDJ6ATW6btPc3eS/1fBo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286339}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "b6233388814c54373d06bb362d800252a9eb23c8", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm-deduplicate --list", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.3.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.33.1", "devDependencies": {"knip": "^2.0.0", "tsup": "^6.5.0", "husky": "^8.0.3", "cspell": "^6.19.2", "eslint": "^8.32.0", "vitest": "^0.32.0", "typedoc": "^0.24.0", "prettier": "^2.8.3", "http-serve": "^1.0.1", "release-it": "^15.6.0", "typescript": "^5.0.2", "lint-staged": "^13.1.0", "markdownlint": "^0.29.0", "@typescript/vfs": "^1.4.0", "markdownlint-cli": "^0.34.0", "pnpm-deduplicate": "^0.4.1", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^41.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.2.0", "eslint-plugin-unicorn": "^46.0.0", "npm-package-json-lint": "^6.4.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "^5.48.2", "@vitest/coverage-istanbul": "^0.32.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.0.1_1687091226520_0.7377519257745311", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "ts-api-utils", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.0.2", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "7c094f753b6705ee4faee25c3c684ade52d66d99", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-Cbu4nIqnEdd+THNEsBdkolnOXhg0I8XteoHaEKgvsxpsbWda4IsUut2c187HxywQCvveojow0Dgw/amxtSKVkQ==", "signatures": [{"sig": "MEYCIQCxwRJZvlcg4NPO57JyMv41ank0h+q0hP2H0iCUNzkAGwIhAK9/zXHXHNqutNhODOlgCbTIvgJob0qO2Vk55puBOznS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285154}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "116f976f689a097cb68d08ec5957d600a9707132", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-serve docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.5.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"knip": "^2.19.5", "tsup": "^7.0.0", "husky": "^8.0.3", "cspell": "^7.0.0", "eslint": "^8.47.0", "vitest": "^0.34.0", "typedoc": "^0.24.0", "prettier": "^3.0.0", "http-serve": "^1.0.1", "release-it": "^16.0.0", "typescript": "^5.1.6", "lint-staged": "^14.0.0", "markdownlint": "^0.30.0", "@typescript/vfs": "^1.5.0", "markdownlint-cli": "^0.35.0", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^46.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.2.0", "eslint-plugin-unicorn": "^48.0.0", "npm-package-json-lint": "^7.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@typescript-eslint/parser": "6.4.0", "@vitest/coverage-istanbul": "^0.34.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^2.1.0", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.0.2_1692388324703_0.05607128902981473", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "ts-api-utils", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.0.3", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "f12c1c781d04427313dbac808f453f050e54a331", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.0.3.tgz", "fileCount": 7, "integrity": "sha512-wNMeqtMz5NtwpT/UZGY5alT+VoKdSsOOP/kqHFcUW1P/VRhH2wJ48+DN2WwUliNbQ976ETwDL0Ifd2VVvgonvg==", "signatures": [{"sig": "MEYCIQCHGv5MfFhlFGcHIfpMkQgP4N9gmQg4uwdmwOg+qqIjAQIhANOMj9KCxvgh/uhXtgx1mjPe11t690akNXzt/+thWX+n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357696}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16.13.0"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "c09d7320bfe4a7b5a359f0f15dc74f857ebeb608", "scripts": {"docs": "typedoc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts --clean --format cjs,esm --outDir lib --dts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip --config knip.jsonc", "docs:serve": "http-server docs/generated", "type-check": "tsc --noEmit", "format:write": "pnpm format --write", "lint:package": "npmPkgJsonLint .", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils.", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "20.0.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.7.3", "devDependencies": {"knip": "^2.19.5", "tsup": "^7.0.0", "husky": "^8.0.3", "cspell": "^7.0.0", "eslint": "^8.47.0", "vitest": "^0.34.0", "typedoc": "^0.24.0", "prettier": "^3.0.0", "release-it": "^16.0.0", "typescript": "^5.1.6", "http-server": "^14.1.1", "lint-staged": "^14.0.0", "markdownlint": "^0.30.0", "@typescript/vfs": "^1.5.0", "markdownlint-cli": "^0.36.0", "sentences-per-line": "^0.2.1", "eslint-plugin-jsdoc": "^46.0.0", "eslint-plugin-jsonc": "^2.6.0", "jsonc-eslint-parser": "^2.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.12.0", "eslint-plugin-vitest": "^0.3.0", "eslint-plugin-unicorn": "^48.0.0", "npm-package-json-lint": "^7.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-markdown": "^3.0.0", "should-semantic-release": "^0.1.0", "typedoc-plugin-coverage": "^2.0.0", "typedoc-plugin-versions": "^0.2.3", "typedoc-plugin-mdn-links": "^3.0.0", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "6.6.0", "@vitest/coverage-istanbul": "^0.34.0", "eslint-plugin-deprecation": "^1.3.3", "eslint-plugin-no-only-tests": "^3.1.0", "prettier-plugin-packagejson": "^2.4.2", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "eslint-plugin-simple-import-sort": "^10.0.0", "typedoc-plugin-custom-validation": "^1.1.0", "typedoc-plugin-konamimojisplosion": "^0.0.2", "eslint-plugin-typescript-sort-keys": "^3.0.0", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.0.3_1694129664902_0.8192135196530634", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "ts-api-utils", "version": "1.1.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.1.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "fc8617bd91cc254805bcc7406580a91efc821aa4", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.1.0.tgz", "fileCount": 9, "integrity": "sha512-bcczrNe+mzZK1MNAmiBS2BpGLYrjkQ1gTctTH5QlQYIxhTN1gr7znSy5RWTk01ArhxOxhjkEediDp7yOm2BC2w==", "signatures": [{"sig": "MEUCIQCNR2thbtdPhR9pobuJaOv57563FVqGOAIF7NmDASYbUwIgcg1qBjUphEd4Vo4UQVkvDcti5sbKUqgEPxYv2dcuOWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 792045}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "99037c29f04c1b43f0e0ff51f7e9d6ec7d768ea7", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:package-json": "npmPkgJsonLint .", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "21.6.1", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.1", "devDependencies": {"knip": "^4.0.0", "tsup": "^8.0.1", "husky": "^9.0.0", "cspell": "^8.2.3", "eslint": "^8.56.0", "vitest": "^1.1.0", "typedoc": "^0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "^5.3.3", "lint-staged": "^15.2.0", "markdownlint": "^0.33.0", "@types/eslint": "^8.56.0", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^16.5.0", "markdownlint-cli": "^0.39.0", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.11.0", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.1.0", "eslint-plugin-jsdoc": "^48.0.0", "eslint-plugin-jsonc": "^2.11.2", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-vitest": "^0.3.18", "npm-package-json-lint": "^7.1.0", "prettier-plugin-curly": "^0.1.3", "eslint-plugin-markdown": "^3.0.1", "should-semantic-release": "^0.2.1", "typedoc-plugin-coverage": "^2.2.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^3.1.8", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "6.20.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.5.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "typedoc-plugin-custom-validation": "^1.1.1", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^8.0.1", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.1.0_1707146116514_0.5029553893989187", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "ts-api-utils", "version": "1.2.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.2.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "48c31073e7ae7868d27ffabef993a2de8c2b006f", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-d+3WxW4r8WQy2cZWpNRPPGExX8ffOLGcIhheUANKbL5Sqjbhkneki76fRAWeXkaslV2etTb4tSJBSxOsH5+CJw==", "signatures": [{"sig": "MEUCIQD68pEidzZsgTAXiOsgL2Mt1hMVyHfr61QSgiGJLqlTjgIgbicCSY0Zs3crdC2EAU421zgnr8rBWOumdx+Mm52FnKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 792406}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "8e032767d92bcc722ac58a8540d920a8b049424a", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:package-json": "npmPkgJsonLint .", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "21.6.1", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.1", "devDependencies": {"knip": "^4.0.0", "tsup": "^8.0.1", "husky": "^9.0.0", "cspell": "^8.2.3", "eslint": "^8.56.0", "vitest": "^1.1.0", "typedoc": "^0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "^5.3.3", "lint-staged": "^15.2.0", "markdownlint": "^0.33.0", "@types/eslint": "^8.56.0", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^16.5.0", "markdownlint-cli": "^0.39.0", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.11.0", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.1.0", "eslint-plugin-jsdoc": "^48.0.0", "eslint-plugin-jsonc": "^2.11.2", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-vitest": "^0.3.18", "npm-package-json-lint": "^7.1.0", "prettier-plugin-curly": "^0.1.3", "eslint-plugin-markdown": "^3.0.1", "should-semantic-release": "^0.2.1", "typedoc-plugin-coverage": "^2.2.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^3.1.8", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "6.20.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.5.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "typedoc-plugin-custom-validation": "^1.1.1", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^8.0.1", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.2.0_1707146788927_0.6798493187461359", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "ts-api-utils", "version": "1.2.1", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.2.1", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "f716c7e027494629485b21c0df6180f4d08f5e8b", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.2.1.tgz", "fileCount": 9, "integrity": "sha512-RIYA36cJn2WiH9Hy77hdF9r7oEwxAtB/TS9/S4Qd90Ap4z5FSiin5zEiTL44OII1Y3IIlEvxwxFUVgrHSZ/UpA==", "signatures": [{"sig": "MEUCIQDww8E28gO2tHZzRMqjD1/Xi7B+RZeOogeHIk2a3GSIZQIgHnDec+XyU3Uz+Pd0/+8dIKy0UOOGSBcCrkya1ZZZukk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 792406}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "2857ee968ab401f6662303fedae3acc59aadc24a", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:package-json": "npmPkgJsonLint .", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "21.6.1", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.1", "devDependencies": {"knip": "^4.0.0", "tsup": "^8.0.1", "husky": "^9.0.0", "cspell": "^8.2.3", "eslint": "^8.56.0", "vitest": "^1.1.0", "typedoc": "^0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "^5.3.3", "lint-staged": "^15.2.0", "markdownlint": "^0.33.0", "@types/eslint": "^8.56.0", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^16.5.0", "markdownlint-cli": "^0.39.0", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.11.0", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.1.0", "eslint-plugin-jsdoc": "^48.0.0", "eslint-plugin-jsonc": "^2.11.2", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-vitest": "^0.3.18", "npm-package-json-lint": "^7.1.0", "prettier-plugin-curly": "^0.1.3", "eslint-plugin-markdown": "^3.0.1", "should-semantic-release": "^0.2.1", "typedoc-plugin-coverage": "^2.2.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^3.1.8", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "6.20.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.5.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "typedoc-plugin-custom-validation": "^1.1.1", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^8.0.1", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.2.1_1707226884126_0.16282606593566484", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "ts-api-utils", "version": "1.3.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.3.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "4b490e27129f1e8e686b45cc4ab63714dc60eea1", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.3.0.tgz", "fileCount": 9, "integrity": "sha512-UQMIo7pb8WRomKR1/+MFVLTroIvDVtMX3K6OUir8ynLyzB8Jeriont2bTAtmNPa1ekAgN7YPDyf6V+ygrdU+eQ==", "signatures": [{"sig": "MEUCIB1C+kjx607ODgVff2hddvXvpMMSH9EJ1Pqg/9n5gRYmAiEAsz45v3KeXMK44hPuea0KSrw5YidWEH9U7q6e9UvoXBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 828062}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "2f8c76ad1e7d571472902c142d193a2b17e8de58", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky install", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:package-json": "npmPkgJsonLint .", "lint:knip:production": "knip --config knip.production.jsonc --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "21.7.1", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.1", "devDependencies": {"knip": "^4.0.0", "tsup": "^8.0.1", "husky": "^9.0.0", "cspell": "^8.2.3", "eslint": "^8.56.0", "vitest": "^1.1.0", "typedoc": "^0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "^5.3.3", "lint-staged": "^15.2.0", "markdownlint": "^0.33.0", "@types/eslint": "^8.56.0", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^16.5.0", "markdownlint-cli": "^0.39.0", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.11.0", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.1.0", "eslint-plugin-jsdoc": "^48.0.0", "eslint-plugin-jsonc": "^2.11.2", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-vitest": "^0.3.18", "npm-package-json-lint": "^7.1.0", "prettier-plugin-curly": "^0.1.3", "eslint-plugin-markdown": "^3.0.1", "should-semantic-release": "^0.2.1", "typedoc-plugin-coverage": "^2.2.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^3.1.8", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "6.20.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.5.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "typedoc-plugin-custom-validation": "^1.1.1", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^8.0.1", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.3.0_1709990510549_0.07755705636023524", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "ts-api-utils", "version": "1.4.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.4.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "709c6f2076e511a81557f3d07a0cbd566ae8195c", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.0.tgz", "fileCount": 9, "integrity": "sha512-032cPxaEKwM+GT3vA5JXNzIaizx388rhsSW79vGRNGXfRRAdEAn2mvk36PvK5HnOchyWZ7afLEXqYCvPCrzuzQ==", "signatures": [{"sig": "MEUCIAiYqxyfg5GYcgA8D7dPz5dRADcFiyQALZV1jBsH/HvBAiEAxadBidvuD5eJs93omx2SaGaaSwUjLr0dY/2ns+8aWW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1186762}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "ba0bfdf29f76bcbc3ec5734b68975c8609c053d4", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "23.1.0", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"knip": "^5.12.3", "tsup": "^8.0.2", "husky": "^9.0.11", "cspell": "^8.6.0", "eslint": "^8.57.0", "semver": "^7.6.2", "vitest": "^1.3.1", "typedoc": "^0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "^5.3.3", "lint-staged": "^15.2.2", "markdownlint": "^0.34.0", "@types/eslint": "^8.56.5", "@types/semver": "^7.5.8", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^17.0.0", "markdownlint-cli": "^0.40.0", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.12.2", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.3.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-jsonc": "^2.13.0", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.3.0", "eslint-plugin-vitest": "^0.4.0", "prettier-plugin-curly": "^0.2.1", "eslint-plugin-markdown": "^5.0.0", "should-semantic-release": "^0.3.0", "typedoc-plugin-coverage": "^3.0.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^3.1.8", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "^7.3.1", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-package-json": "^0.13.0", "eslint-plugin-perfectionist": "^2.6.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^7.3.1", "typedoc-plugin-custom-validation": "^1.1.1", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^8.0.1"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.4.0_1730311942946_0.566570356050285", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "ts-api-utils", "version": "1.4.1", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.4.1", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "7c0a304cd446d9a497c24c960b8abbf0bc1611ae", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.1.tgz", "fileCount": 9, "integrity": "sha512-5RU2/lxTA3YUZxju61HO2U6EoZLvBLtmV2mbTvqyu4a/7s7RmJPT+1YekhMVsQhznRWk/czIwDUg+V8Q9ZuG4w==", "signatures": [{"sig": "MEQCIGsjZ76XJwWbldUSvhIsPRGzC4cgLeP2NvWU9b9phLnXAiAwFpF13nRQRLV2n3lyQ7TeB9cB0lcYzXIDvj2xi5gwBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1190699}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "12df29810c4a0b9146f58fdee4a2cbdf28a1bee0", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "23.3.0", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9", "devDependencies": {"knip": "^5.12.3", "tsup": "^8.0.2", "husky": "^9.0.11", "cspell": "^8.6.0", "eslint": "^8.57.0", "semver": "^7.6.2", "vitest": "^1.3.1", "typedoc": "^0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "~5.4.5", "lint-staged": "^15.2.2", "markdownlint": "^0.36.0", "@types/eslint": "^8.56.5", "@types/semver": "^7.5.8", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^17.0.0", "markdownlint-cli": "^0.42.0", "console-fail-test": "^0.5.0", "eslint-plugin-yml": "^1.12.2", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.3.1", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-jsonc": "^2.13.0", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.3.0", "eslint-plugin-vitest": "^0.4.0", "prettier-plugin-curly": "^0.3.0", "eslint-plugin-markdown": "^5.0.0", "should-semantic-release": "^0.3.0", "typedoc-plugin-coverage": "^3.0.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^3.1.8", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "^8.0.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-package-json": "^0.15.0", "eslint-plugin-perfectionist": "^2.6.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^7.3.1", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^9.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.4.1_1732471572503_0.10052745564420928", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "ts-api-utils", "version": "1.4.2", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.4.2", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "a6a6dff26117ac7965624fc118525971edc6a82a", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.2.tgz", "fileCount": 7, "integrity": "sha512-ZF5gQIQa/UmzfvxbHZI3JXN0/Jt+vnAfAviNRAMc491laiK6YCLpCW9ft8oaCRFOTxCZtUTE6XB0ZQAe3olntw==", "signatures": [{"sig": "MEUCIEcIG28Kqj4UFGwg6oQlmDmdksONGMRvySVotnia28k9AiEAylVzqqwA7/pWpIbR6TNECzl5c3H3AH+asYu78GU+mHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.4.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 358856}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "c452cf6483c1bcaf9e7f4f34bf84618d8773565b", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "23.3.0", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9", "devDependencies": {"knip": "^5.12.3", "tsup": "^8.0.2", "husky": "^9.0.11", "cspell": "^8.6.0", "eslint": "^8.57.0", "vitest": "^1.3.1", "typedoc": "~0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "~5.4.5", "lint-staged": "^15.2.2", "markdownlint": "^0.36.0", "@types/eslint": "^8.56.5", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^17.0.0", "markdownlint-cli": "^0.43.0", "console-fail-test": "^0.5.0", "eslint-plugin-yml": "^1.12.2", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.3.1", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-jsonc": "^2.13.0", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.3.0", "eslint-plugin-vitest": "^0.4.0", "prettier-plugin-curly": "^0.3.0", "eslint-plugin-markdown": "^5.0.0", "should-semantic-release": "^0.3.0", "typedoc-plugin-coverage": "^3.0.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^4.0.0", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "^8.0.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-package-json": "^0.15.0", "eslint-plugin-perfectionist": "^2.6.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^7.3.1", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^9.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.4.2_1732597509527_0.5790332345499376", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "ts-api-utils", "version": "1.4.3", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@1.4.3", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "bfc2215fe6528fecab2b0fba570a2e8a4263b064", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz", "fileCount": 7, "integrity": "sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==", "signatures": [{"sig": "MEUCIQD5fGvZXUCzrb/mT8HibVK6qze0w/p1AE9VQM7yjfqBLwIgOMj39oq5YxKn5dgkXPsIKf8xks/Tbz1sEqqTPRpVBNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@1.4.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 359078}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "128237ff3b32e44b3da0040c045dd7f1829fc93e", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . .*js --max-warnings 0", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "23.3.0", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9", "devDependencies": {"knip": "^5.12.3", "tsup": "^8.0.2", "husky": "^9.0.11", "cspell": "^8.6.0", "eslint": "^8.57.0", "vitest": "^1.3.1", "typedoc": "~0.24.8", "prettier": "^3.1.1", "release-it": "^17.0.1", "typescript": "~5.4.5", "lint-staged": "^15.2.2", "markdownlint": "^0.36.0", "@types/eslint": "^8.56.5", "@typescript/vfs": "^1.5.0", "eslint-plugin-n": "^17.0.0", "markdownlint-cli": "^0.43.0", "console-fail-test": "^0.5.0", "eslint-plugin-yml": "^1.12.2", "sentences-per-line": "^0.2.1", "yaml-eslint-parser": "^1.2.2", "@vitest/coverage-v8": "^1.3.1", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-jsonc": "^2.13.0", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.3.0", "eslint-plugin-vitest": "^0.4.0", "prettier-plugin-curly": "^0.3.0", "eslint-plugin-markdown": "^5.0.0", "should-semantic-release": "^0.3.0", "typedoc-plugin-coverage": "^3.0.0", "typedoc-plugin-versions": "^0.2.4", "typedoc-plugin-mdn-links": "^4.0.0", "@phenomnomnominal/tsquery": "^6.1.3", "@typescript-eslint/parser": "^8.0.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-package-json": "^0.15.0", "eslint-plugin-perfectionist": "^2.6.0", "prettier-plugin-packagejson": "^2.4.7", "eslint-plugin-eslint-comments": "^3.2.0", "@typescript-eslint/eslint-plugin": "^7.3.1", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^9.0.0"}, "peerDependencies": {"typescript": ">=4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_1.4.3_1732844805510_0.7815148647279395", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "ts-api-utils", "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@2.0.0", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "b9d7d5f7ec9f736f4d0f09758b8607979044a900", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-xCt/TOAc+EOHS1XPnijD3/yzpH6qg2xppZO1YDqGoVsNXfQfzHpOdNuXwrwOU8u4ITXJyDCTyt8w5g1sZv9ynQ==", "signatures": [{"sig": "MEQCIE6vv8/PUDPDFrq9tvzbeDiZOmrjVRVbqdchCE1Au3p/AiAVEeUlNPuhS/wtspLmGvK+27Y/34EVE7vM64HUzDCyFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 353415}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18.12"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "7935007783fd4475183324379fdb8b76d1429393", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . --max-warnings 0", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "23.3.0", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.14.2", "devDependencies": {"knip": "^5.37.2", "tsup": "^8.3.5", "husky": "^9.1.7", "cspell": "^8.16.0", "eslint": "^9.15.0", "vitest": "^2.1.5", "typedoc": "^0.26.11", "prettier": "^3.3.3", "@eslint/js": "^9.15.0", "release-it": "^17.10.0", "typescript": "^5.7.2", "@types/node": "^18.19.65", "lint-staged": "^15.2.10", "markdownlint": "^0.36.1", "@typescript/vfs": "^1.6.0", "eslint-plugin-n": "^17.14.0", "markdownlint-cli": "^0.43.0", "console-fail-test": "^0.5.0", "eslint-plugin-yml": "^1.15.0", "typescript-eslint": "^8.16.0", "sentences-per-line": "^0.2.1", "@vitest/coverage-v8": "^2.1.5", "eslint-plugin-jsdoc": "^50.5.0", "eslint-plugin-jsonc": "^2.18.2", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.7.0", "@vitest/eslint-plugin": "^1.1.10", "prettier-plugin-curly": "^0.3.1", "eslint-plugin-markdown": "^5.1.0", "should-semantic-release": "^0.3.0", "typedoc-plugin-coverage": "^3.4.0", "typedoc-plugin-mdn-links": "^4.0.1", "@phenomnomnominal/tsquery": "^6.1.3", "eslint-plugin-package-json": "^0.15.6", "eslint-plugin-perfectionist": "^4.1.2", "prettier-plugin-packagejson": "^2.5.6", "@types/eslint-plugin-markdown": "^2.0.2", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^9.0.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.4.1"}, "peerDependencies": {"typescript": ">=4.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_2.0.0_1733018988651_0.8703767230521737", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "ts-api-utils", "version": "2.0.1", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ts-api-utils@2.0.1", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "dist": {"shasum": "660729385b625b939aaa58054f45c058f33f10cd", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-dnlgjFSVetynI8nzgJ+qF62efpglpWRk8isUEWZGWlJYySCTD6aKvbUDu+zbPeDakk3bg5H4XpitHukgfL1m9w==", "signatures": [{"sig": "MEUCIQC2psnoZxWnfyYjfVn26DDYIZZ1fCraiKknnI8W72MkYAIgF5q01EyVfTMP2rE7NLS7y1IjBOcuTv8IQllGrXO/Oe0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 353810}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "engines": {"node": ">=18.12"}, "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "gitHead": "a5e8b1ac0790f201728f0e33625a918fef53bf50", "scripts": {"tsc": "tsc", "docs": "typedoc", "lint": "eslint . --max-warnings 0", "test": "vitest", "build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "prepare": "husky", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "docs:serve": "npx --yes http-server docs/generated", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:knip:production": "knip --production", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "23.7.0", "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.6", "tsup": "^8.3.6", "husky": "^9.1.7", "cspell": "^8.17.3", "eslint": "^9.19.0", "vitest": "^2.1.8", "typedoc": "^0.27.6", "prettier": "^3.4.2", "@eslint/js": "^9.19.0", "release-it": "^18.1.2", "typescript": "^5.7.3", "@types/node": "^18.19.74", "lint-staged": "^15.4.3", "markdownlint": "^0.37.4", "@typescript/vfs": "^1.6.0", "eslint-plugin-n": "^17.15.1", "markdownlint-cli": "^0.43.0", "console-fail-test": "^0.5.0", "eslint-plugin-yml": "^1.16.0", "typescript-eslint": "^8.22.0", "sentences-per-line": "^0.3.0", "@vitest/coverage-v8": "^2.1.8", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-jsonc": "^2.19.1", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.7.0", "@vitest/eslint-plugin": "^1.1.25", "prettier-plugin-curly": "^0.3.1", "eslint-plugin-markdown": "^5.1.0", "should-semantic-release": "^0.3.0", "typedoc-plugin-coverage": "^3.4.1", "typedoc-plugin-mdn-links": "^4.0.10", "@phenomnomnominal/tsquery": "^6.1.3", "eslint-plugin-package-json": "^0.19.0", "eslint-plugin-perfectionist": "^4.7.0", "prettier-plugin-packagejson": "^2.5.8", "@types/eslint-plugin-markdown": "^2.0.2", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "@release-it/conventional-changelog": "^10.0.0", "@eslint-community/eslint-plugin-eslint-comments": "^4.4.1"}, "peerDependencies": {"typescript": ">=4.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/ts-api-utils_2.0.1_1738417760614_0.3438391920544337", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.0": {"name": "ts-api-utils", "version": "2.1.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "repository": {"type": "git", "url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git"}, "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "main": "./lib/index.js", "scripts": {"build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "docs": "typedoc", "docs:serve": "npx --yes http-server docs/generated", "format": "prettier \"**/*\" --ignore-unknown", "lint": "eslint . --max-warnings 0", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "prepare": "husky", "should-semantic-release": "should-semantic-release --verbose", "test": "vitest", "tsc": "tsc"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "devDependencies": {"@eslint-community/eslint-plugin-eslint-comments": "^4.4.1", "@eslint/js": "^9.19.0", "@phenomnomnominal/tsquery": "^6.1.3", "@release-it/conventional-changelog": "^10.0.0", "@types/eslint-plugin-markdown": "^2.0.2", "@types/node": "^18.19.74", "@typescript/vfs": "^1.6.0", "@vitest/coverage-v8": "^2.1.8", "@vitest/eslint-plugin": "^1.1.25", "console-fail-test": "^0.5.0", "cspell": "^8.17.3", "eslint": "^9.19.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-n": "^17.15.1", "eslint-plugin-package-json": "^0.19.0", "eslint-plugin-perfectionist": "^4.7.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-yml": "^1.16.0", "husky": "^9.1.7", "jsonc-eslint-parser": "^2.4.0", "knip": "^5.46.0", "lint-staged": "^15.4.3", "markdownlint": "^0.37.4", "markdownlint-cli": "^0.44.0", "prettier": "^3.4.2", "prettier-plugin-curly": "^0.3.1", "prettier-plugin-packagejson": "^2.5.8", "release-it": "^18.1.2", "sentences-per-line": "^0.3.0", "should-semantic-release": "^0.3.0", "tsup": "^8.3.6", "typedoc": "^0.27.6", "typedoc-plugin-coverage": "^3.4.1", "typedoc-plugin-custom-validation": "^2.0.2", "typedoc-plugin-konamimojisplosion": "^0.0.2", "typedoc-plugin-mdn-links": "^4.0.10", "typescript": "^5.7.3", "typescript-eslint": "^8.22.0", "vitest": "^3.0.0"}, "peerDependencies": {"typescript": ">=4.8.4"}, "packageManager": "pnpm@9.15.9", "engines": {"node": ">=18.12"}, "publishConfig": {"provenance": true}, "_id": "ts-api-utils@2.1.0", "gitHead": "c3e980e9ef52e6bb5d2ea242911020fda1eae861", "types": "./lib/index.d.ts", "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "_nodeVersion": "23.10.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==", "shasum": "595f7094e46eed364c13fd23e75f9513d29baf91", "tarball": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.1.0.tgz", "fileCount": 7, "unpackedSize": 356154, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ts-api-utils@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDvPJ9cAR4aChEo7xeEwFoV1bs0+QNyhZTellqrE5pRLAIgRBEvLU3aaX6st6/B34mwTAK2tvwsJgmnx0odABVJ6Fw="}]}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ts-api-utils_2.1.0_1742473490160_0.027497783069299286"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-02-07T15:19:43.786Z", "modified": "2025-03-20T12:24:50.789Z", "0.0.18": "2023-02-07T15:19:43.954Z", "0.0.20": "2023-02-07T16:35:41.839Z", "0.0.21": "2023-02-07T16:36:26.252Z", "0.0.22": "2023-02-14T21:51:58.198Z", "0.0.23": "2023-02-15T01:57:37.399Z", "0.0.24": "2023-02-18T20:06:43.444Z", "0.0.25": "2023-02-19T07:02:15.855Z", "0.0.26": "2023-02-19T07:10:52.912Z", "0.0.27": "2023-02-19T07:18:13.300Z", "0.0.28": "2023-02-20T01:02:44.281Z", "0.0.29": "2023-02-21T15:45:20.344Z", "0.0.30": "2023-02-22T01:40:16.937Z", "0.0.31": "2023-02-22T03:04:45.960Z", "0.0.32": "2023-02-22T03:16:02.578Z", "0.0.33": "2023-02-22T06:48:39.108Z", "0.0.34": "2023-02-22T07:25:53.104Z", "0.0.35": "2023-02-22T07:32:32.531Z", "0.0.36": "2023-02-22T08:14:27.680Z", "0.0.37": "2023-02-22T08:27:49.813Z", "0.0.38": "2023-02-22T08:35:17.123Z", "0.0.39": "2023-02-22T15:39:14.974Z", "0.0.40": "2023-03-02T22:16:21.896Z", "0.0.41": "2023-03-02T22:32:08.545Z", "0.0.42": "2023-03-02T22:39:24.914Z", "0.0.43": "2023-03-13T04:04:26.891Z", "0.0.44": "2023-03-16T12:47:08.400Z", "0.0.45": "2023-03-20T12:26:24.169Z", "0.0.46": "2023-04-01T06:41:26.808Z", "0.1.0": "2023-06-15T17:06:53.576Z", "0.2.0": "2023-06-15T17:07:58.031Z", "1.0.0-beta": "2023-06-15T17:14:09.225Z", "1.0.0": "2023-06-15T22:46:25.715Z", "1.0.1": "2023-06-18T12:27:06.734Z", "1.0.2": "2023-08-18T19:52:04.905Z", "1.0.3": "2023-09-07T23:34:25.149Z", "1.1.0": "2024-02-05T15:15:16.686Z", "1.2.0": "2024-02-05T15:26:29.139Z", "1.2.1": "2024-02-06T13:41:24.340Z", "1.3.0": "2024-03-09T13:21:50.852Z", "1.4.0": "2024-10-30T18:12:23.264Z", "1.4.1": "2024-11-24T18:06:12.797Z", "1.4.2": "2024-11-26T05:05:09.734Z", "1.4.3": "2024-11-29T01:46:45.735Z", "2.0.0": "2024-12-01T02:09:48.821Z", "2.0.1": "2025-02-01T13:49:20.828Z", "2.1.0": "2025-03-20T12:24:50.449Z"}, "bugs": {"url": "https://github.com/JoshuaKGoldberg/ts-api-utils/issues"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/JoshuaKGoldberg/ts-api-utils#readme", "repository": {"type": "git", "url": "git+https://github.com/JoshuaKGoldberg/ts-api-utils.git"}, "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "maintainers": [{"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\">TypeScript API Utils</h1>\n\n<p align=\"center\">\n  Utility functions for working with TypeScript's API.\n  Successor to the wonderful tsutils.\n  🛠️️\n</p>\n\n<p align=\"center\">\n\t<!-- prettier-ignore-start -->\n\t<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->\n<img alt=\"All Contributors: 10 👪\" src=\"https://img.shields.io/badge/all_contributors-10_👪-21bb42.svg\" />\n<!-- ALL-CONTRIBUTORS-BADGE:END -->\n\t<!-- prettier-ignore-end -->\n\t<a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/blob/main/.github/CODE_OF_CONDUCT.md\" target=\"_blank\"><img alt=\"🤝 Code of Conduct: Kept\" src=\"https://img.shields.io/badge/%F0%9F%A4%9D_code_of_conduct-kept-21bb42\" /></a>\n\t<a href=\"https://codecov.io/gh/JoshuaKGoldberg/ts-api-utils\" target=\"_blank\"><img alt=\"🧪 Coverage\" src=\"https://img.shields.io/codecov/c/github/JoshuaKGoldberg/ts-api-utils?label=%F0%9F%A7%AA%20coverage\" /></a>\n  <a href=\"#\" target=\"_blank\"><img alt=\"📚 Documentation Coverage\" src=\"https://raw.githubusercontent.com/JoshuaKGoldberg/ts-api-utils/refs/heads/main/docs/coverage.svg\" /></a>\n\t<a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/blob/main/LICENSE.md\" target=\"_blank\"><img alt=\"📝 License: MIT\" src=\"https://img.shields.io/badge/%F0%9F%93%9D_license-MIT-21bb42.svg\"></a>\n\t<a href=\"http://npmjs.com/package/ts-api-utils\"><img alt=\"📦 npm version\" src=\"https://img.shields.io/npm/v/ts-api-utils?color=21bb42&label=%F0%9F%93%A6%20npm\" /></a>\n\t<img alt=\"💪 TypeScript: Strict\" src=\"https://img.shields.io/badge/%F0%9F%92%AA_typescript-strict-21bb42.svg\" />\n</p>\n\n## Usage\n\n```shell\nnpm i ts-api-utils\n```\n\n```ts\nimport * as tsutils from \"ts-api-utils\";\n\ntsutils.forEachToken(/* ... */);\n```\n\n### API\n\n`ts-api-utils` provides many utility functions.\nCheck out our API docs for details:\n\n📝 [ts-api-utils API docs](https://joshuakgoldberg.github.io/ts-api-utils).\n\n## Development\n\nSee [`.github/CONTRIBUTING.md`](./.github/CONTRIBUTING.md).\nThanks! 💖\n\n## Contributors\n\nMany thanks to [@ajafff](https://github.com/ajafff) for creating the original [`tsutils`](https://github.com/ajafff/tsutils) ([original license: MIT](https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE)) that this project was originally based on! 🙏\n\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<!-- spellchecker: disable -->\n<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://effectivetypescript.com\"><img src=\"https://avatars.githubusercontent.com/u/98301?v=4?s=100\" width=\"100px;\" alt=\"Dan Vanderkam\"/><br /><sub><b>Dan Vanderkam</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3Adanvk\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://blog.jmchor.dev\"><img src=\"https://avatars.githubusercontent.com/u/110151013?v=4?s=100\" width=\"100px;\" alt=\"Johannes Chorzempa\"/><br /><sub><b>Johannes Chorzempa</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=jmchor\" title=\"Documentation\">📖</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=jmchor\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://www.joshuakgoldberg.com\"><img src=\"https://avatars.githubusercontent.com/u/3335181?v=4?s=100\" width=\"100px;\" alt=\"Josh Goldberg\"/><br /><sub><b>Josh Goldberg</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3AJoshuaKGoldberg\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg\" title=\"Code\">💻</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg\" title=\"Documentation\">📖</a> <a href=\"#projectManagement-JoshuaKGoldberg\" title=\"Project Management\">📆</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg\" title=\"Tests\">⚠️</a> <a href=\"#tool-JoshuaKGoldberg\" title=\"Tools\">🔧</a> <a href=\"#maintenance-JoshuaKGoldberg\" title=\"Maintenance\">🚧</a> <a href=\"#infra-JoshuaKGoldberg\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"#ideas-JoshuaKGoldberg\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://twitter.com/kirjs\"><img src=\"https://avatars.githubusercontent.com/u/2545357?v=4?s=100\" width=\"100px;\" alt=\"Kirill Cherkashin\"/><br /><sub><b>Kirill Cherkashin</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=kirjs\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/kirkwaiblinger\"><img src=\"https://avatars.githubusercontent.com/u/53019676?v=4?s=100\" width=\"100px;\" alt=\"Kirk Waiblinger\"/><br /><sub><b>Kirk Waiblinger</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3Akirkwaiblinger\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=kirkwaiblinger\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/ajafff\"><img src=\"https://avatars.githubusercontent.com/u/11968040?v=4?s=100\" width=\"100px;\" alt=\"Klaus Meinhardt\"/><br /><sub><b>Klaus Meinhardt</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ajafff\" title=\"Code\">💻</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ajafff\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://webpro.nl\"><img src=\"https://avatars.githubusercontent.com/u/456426?v=4?s=100\" width=\"100px;\" alt=\"Lars Kappert\"/><br /><sub><b>Lars Kappert</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=webpro\" title=\"Code\">💻</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/RebeccaStevens\"><img src=\"https://avatars.githubusercontent.com/u/7224206?v=4?s=100\" width=\"100px;\" alt=\"Rebecca Stevens\"/><br /><sub><b>Rebecca Stevens</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3ARebeccaStevens\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens\" title=\"Code\">💻</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens\" title=\"Documentation\">📖</a> <a href=\"#projectManagement-RebeccaStevens\" title=\"Project Management\">📆</a> <a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens\" title=\"Tests\">⚠️</a> <a href=\"#tool-RebeccaStevens\" title=\"Tools\">🔧</a> <a href=\"#infra-RebeccaStevens\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"#maintenance-RebeccaStevens\" title=\"Maintenance\">🚧</a> <a href=\"#ideas-RebeccaStevens\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://twitter.com/ronenamiel\"><img src=\"https://avatars.githubusercontent.com/u/5484230?v=4?s=100\" width=\"100px;\" alt=\"Ronen Amiel\"/><br /><sub><b>Ronen Amiel</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ronami\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://www.fiskercheung.com/\"><img src=\"https://avatars.githubusercontent.com/u/172584?v=4?s=100\" width=\"100px;\" alt=\"fisker Cheung\"/><br /><sub><b>fisker Cheung</b></sub></a><br /><a href=\"https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=fisker\" title=\"Code\">💻</a></td>\n    </tr>\n  </tbody>\n</table>\n\n<!-- markdownlint-restore -->\n<!-- prettier-ignore-end -->\n\n<!-- ALL-CONTRIBUTORS-LIST:END -->\n<!-- spellchecker: enable -->\n<!-- markdownlint-restore -->\n<!-- prettier-ignore-end -->\n\n> 💙 This package was templated with [create-typescript-app](https://github.com/JoshuaKGoldberg/create-typescript-app).\n\n> _\"My tools! I have to have my tools!\" - Dennis Reynolds_\n", "readmeFilename": "README.md"}