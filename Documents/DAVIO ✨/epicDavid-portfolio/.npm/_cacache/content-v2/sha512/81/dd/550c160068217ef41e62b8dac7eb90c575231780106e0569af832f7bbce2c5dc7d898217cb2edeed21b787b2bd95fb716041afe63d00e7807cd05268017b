{"_id": "@eslint/plugin-kit", "_rev": "13-5a8ce5e29b4bef71be63047facc162a6", "name": "@eslint/plugin-kit", "dist-tags": {"latest": "0.3.3"}, "versions": {"0.1.0": {"name": "@eslint/plugin-kit", "version": "0.1.0", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.1.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "809b95a0227ee79c3195adfb562eb94352e77974", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.1.0.tgz", "fileCount": 10, "integrity": "sha512-autAXT203ixhqei9xt+qkYOvY8l6LAFIdT2UXc/RPNeUVfqRF1BV94GTJyVPFKT8nFM6MyVJhjLj9E8JWvf5zQ==", "signatures": [{"sig": "MEUCIHKS+NbhHUUkBmRCVYuiYo0feYcEXPR7LhCTxTxY1lp0AiEA6RJPgxvyDFlMkBgORtsv0qlqFrvNmyWJUoMU6++Mej0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69652}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "3c54b2b01476ff533811d7745e5a81b4aa72326e", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"levn": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.5.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.1.0_1724767144057_0.2155951958615836", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@eslint/plugin-kit", "version": "0.2.0", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "8712dccae365d24e9eeecb7b346f85e750ba343d", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-vH9PiIMMwvhCx31Af3HiGzsVNULDbyVkHXwlemn/B0TFj/00ho3y55efXrUZTfQipxoHC5u4xq6zblww1zm1Ig==", "signatures": [{"sig": "MEUCIHgfWPu8QJU6mh8kjcBGXfGdX9LfABko9FVCrSzghJrLAiEAwtJeKMeMWKIK8b4DzhfsSOlKqzcAzRubvVHSmReYO78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76668}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "8743a5b0f176f548d6f5abd9fdaee9269a06df8b", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"levn": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.6.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.0_1725029177947_0.4214359719661631", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@eslint/plugin-kit", "version": "0.2.1", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "cd14fe2db79fa639839dfef4105e83bad1814482", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.1.tgz", "fileCount": 10, "integrity": "sha512-HFZ4Mp26nbWk9d/BpvP0YNL6W4UoZF0VFcTw/aPPA8RpOxeFQgK+ClABGgAUXs9Y/RGX/l1vOmrqz1MQt9MNuw==", "signatures": [{"sig": "MEYCIQDKovgpG4xeNJadZSua1z09X0KHAoHQzXaYbYkhHqEJIgIhAK9Ok7lBCaiQZaVafDxeXSR0QfO3prvbua6tP1MOm5Ve", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76672}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "80eb5455aa5e021fc8c514a11d67c59c583ae2a1", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"levn": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.7.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.1_1729282314540_0.9960957729052182", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "@eslint/plugin-kit", "version": "0.2.2", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.2", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "5eff371953bc13e3f4d88150e2c53959f64f74f6", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.2.tgz", "fileCount": 10, "integrity": "sha512-CXtq5nR4Su+2I47WPOlWud98Y5Lv8Kyxp2ukhgFx/eW6Blm18VXJO5WuQylPugRo8nbluoi6GvvxBLqHcvqUUw==", "signatures": [{"sig": "MEYCIQCgn8paWiDoiPtg/PpIpu9IlvlDDRB//z5xPX5xsmtXmgIhAP7stDkTzLLdh7YqHxGLUAPYbIuKTc0WRsavBDO4/h6J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76529}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "0dc78d335a98ef680b579851026438473147750e", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"levn": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.8.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.2_1730136263419_0.37028872291303006", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "@eslint/plugin-kit", "version": "0.2.3", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.3", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "812980a6a41ecf3a8341719f92a6d1e784a2e0e8", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.3.tgz", "fileCount": 10, "integrity": "sha512-2b/g5hRmpbb1o4GnTZax9N9m0FXzz9OV42ZzI4rDDMDuHUqigAiQCEWChBWCY4ztAGVRjoWT19v0yMmc5/L5kA==", "signatures": [{"sig": "MEQCH3HdYJmY7M9P0AAcsu1Gnq2mOOaOAJfL/LTi26BraR0CIQDnfetk0JT1HroDc9WK33i0sFPmcbrqFBtjxopTjE7CvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76956}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "a957ee351c27ac1bf22966768cf8aac8c12ce0d2", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.10.0", "dependencies": {"levn": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.9.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.3_1731602808364_0.23076903229568413", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "@eslint/plugin-kit", "version": "0.2.4", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.4", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "2b78e7bb3755784bb13faa8932a1d994d6537792", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.4.tgz", "fileCount": 10, "integrity": "sha512-zSkKow6H5Kdm0ZUQUB2kV5JIXqoG0+uH5YADhaEHswm664N9Db8dXSi0nMJpacpMf+MyyglF1vnZohpEg5yUtg==", "signatures": [{"sig": "MEUCIEaUYGTzvztI1Ol+Q12g/xSQwCkUxnsmsod+E3gmtUnsAiEAmqahROKXkbqzZ8x8TC5gZAQJhNSP645vxrSXqg+Yx4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77087}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "dd8d161c635450f3e37109f833737bf69f54db55", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"levn": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "@eslint/core": "^0.9.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.4_1733347433183_0.03545568386227438", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "@eslint/plugin-kit", "version": "0.2.5", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.5", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "ee07372035539e7847ef834e3f5e7b79f09e3a81", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.5.tgz", "fileCount": 10, "integrity": "sha512-lB05FkqEdUg2AA0xEbUz0SnkXT1LcCTa438W4IWTUh4hdOnVbQyOJ81OrDXsJk/LSiJHubgGEFoR5EHq1NsH1A==", "signatures": [{"sig": "MEUCID8+wDz5jFDOXi+KbLywMFstfgXpyDhjaNjay0UAf9xkAiEA9mbXFrGhyRoFRHn01lzqr/bUF9Qe+kLBBRpnv4DsyrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76754}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "7f291c70eafb050420d86092f7cd03f9c6a96207", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ./build-cts.js", "test:types": "tsc -p tests/types/tsconfig.json", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.5_1736534108125_0.2531063877538928", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.6": {"name": "@eslint/plugin-kit", "version": "0.2.6", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.6", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "a30084164a4ced1efb6ec31d3d04f581cb8929c0", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.6.tgz", "fileCount": 10, "integrity": "sha512-+0TjwR1eAUdZtvv/ir1mGX+v0tUoR3VEPB8Up0LLJC+whRW0GgBBtpbOkg/a/U4Dxa6l5a3l9AJ1aWIQVyoWJA==", "signatures": [{"sig": "MEUCIATUXwx5DDu8h8n0MpK018Fnq5SFKey2ibB0fnN3MVidAiEAxwRBqw/n0ZpfARZshazyj7m0n8nkFdOAZuSUD63vbME=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77161}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "0c293bc60a86245cb68da7e7f2a54a17b4bd839f", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:types": "tsc -p tests/types/tsconfig.json", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.6_1739900117500_0.004577534617633772", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.7": {"name": "@eslint/plugin-kit", "version": "0.2.7", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.7", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "9901d52c136fb8f375906a73dcc382646c3b6a27", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.7.tgz", "fileCount": 10, "integrity": "sha512-JubJ5B2pJ4k4yGxaNLdbjrnk9d/iDz6/q8wOilpIowd6PJPgaxCuHBnBszq7Ce2TyMrywm5r4PnKm6V3iiZF+g==", "signatures": [{"sig": "MEUCICKjgU6UmkgL8qgy9e1nA5qYYwzV8+fY7sG4Pu0JjGgJAiEAof8qZ0wnMFpHk6bRS7HFUi67UGwrpf+jEYIfCmPCuGU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77293}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "3e9b0eb2b87b46842f157421001cc58ba007be56", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:types": "tsc -p tests/types/tsconfig.json", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.7_1740140413435_0.9895175799504181", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.8": {"name": "@eslint/plugin-kit", "version": "0.2.8", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.2.8", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "47488d8f8171b5d4613e833313f3ce708e3525f8", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz", "fileCount": 10, "integrity": "sha512-ZAoA40rNMPwSm+AeHpCq8STiNAwzWLJuP8Xv4CHIc9wv/PSuExjMrmjfYNj682vW0OOiZ1HKxzvjQr9XZIisQA==", "signatures": [{"sig": "MEUCIFHXN0YekCR13a444ijTmwM26+dPnFnqdzIxiAYPLQEfAiEAqZHmP2Qa0bsfl3FLz1QQdmmYejbs8/HVDvzRExKTAzg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76842}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "1615a01d9e5c637dfb4d19bb53968185462fadb3", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:types": "tsc -p tests/types/tsconfig.json", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.2.8_1743532249161_0.11164043315306715", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.1": {"name": "@eslint/plugin-kit", "version": "0.3.1", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.3.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "b71b037b2d4d68396df04a8c35a49481e5593067", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.1.tgz", "fileCount": 10, "integrity": "sha512-0J+zgWxHN+xXONWIyPWKFMgVuJoZuGiIFu8yxk7RJjxkzpGmyja5wRFqZIVtjDVOQpV+Rw0iOAjYPE2eQyjr0w==", "signatures": [{"sig": "MEUCIG+ltl5FJFwVwxfPelXOXQsCaegaNJNJSBjUPMLkf9CCAiEAye80AHJkKK0MzrsxwmRmJsh8svVzj6ByKC69WCBwma4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 80311}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "daa19551d7a52673ccaf7656c726dca6679ebd24", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:types": "tsc -p tests/types/tsconfig.json", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.3.1_1746111761327_0.30130700348861783", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.2": {"name": "@eslint/plugin-kit", "version": "0.3.2", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/plugin-kit@0.3.2", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite/tree/main/packages/plugin-kit#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "0cad96b134d23a653348e3342f485636b5ef4732", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.2.tgz", "fileCount": 10, "integrity": "sha512-4SaFZCNfJqvk/kenHpI8xvN42DMaoycy4PzKc5otHxRswww1kAt82OlBuwRVLofCACCTZEcla2Ydxv8scMXaTg==", "signatures": [{"sig": "MEQCICjIycu4VHHBg8jJqbht/yq8Cte0hYq4mBAAdrGEdmFGAiBKseuzaq/c/bGozX/XGM9rAD1+7nK6pyQ/vI7NjKJoJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.3.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 81834}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "48b1f849476582257e1b6a110c4af55adbbec2e8", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:types": "tsc -p tests/types/tsconfig.json", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git", "directory": "packages/plugin-kit"}, "_npmVersion": "10.9.2", "description": "Utilities for building ESLint plugins.", "directories": {}, "_nodeVersion": "22.16.0", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-kit_0.3.2_1749674205789_0.23135541243083457", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.3": {"name": "@eslint/plugin-kit", "version": "0.3.3", "description": "Utilities for building ESLint plugins.", "author": {"name": "<PERSON>"}, "type": "module", "main": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/plugin-kit"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite/tree/main/packages/plugin-kit#readme", "scripts": {"build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test": "mocha tests/", "test:coverage": "c8 npm test", "test:jsr": "npx jsr@latest publish --dry-run", "test:types": "tsc -p tests/types/tsconfig.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.15.1", "levn": "^0.4.1"}, "devDependencies": {"@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "_id": "@eslint/plugin-kit@0.3.3", "gitHead": "0496201974aad87fdcf3aa2a63ec74e91b54825e", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-1+WqvgNMhmlAambTvT3KPtCl/Ibr68VldY2XY40SL1CE0ZXiakFR/cbTspaF5HsnpDMvcYYoJHfl4980NBjGag==", "shasum": "32926b59bd407d58d817941e48b2a7049359b1fd", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.3.tgz", "fileCount": 10, "unpackedSize": 81737, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.3.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDGb12wVUEazyXp08OerWMegS3XoaM53st1dSftrM8K8gIgEG27vX18iMk+Xa0P/v5tvue/buhBBz8AL8VJeq5torg="}]}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-kit_0.3.3_1750860275716_0.2177414636584878"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-08-27T13:59:03.959Z", "modified": "2025-06-25T14:04:36.337Z", "0.1.0": "2024-08-27T13:59:04.231Z", "0.2.0": "2024-08-30T14:46:18.085Z", "0.2.1": "2024-10-18T20:11:54.868Z", "0.2.2": "2024-10-28T17:24:23.634Z", "0.2.3": "2024-11-14T16:46:48.594Z", "0.2.4": "2024-12-04T21:23:53.346Z", "0.2.5": "2025-01-10T18:35:08.352Z", "0.2.6": "2025-02-18T17:35:17.707Z", "0.2.7": "2025-02-21T12:20:13.607Z", "0.2.8": "2025-04-01T18:30:49.339Z", "0.3.1": "2025-05-01T15:02:41.535Z", "0.3.2": "2025-06-11T20:36:46.062Z", "0.3.3": "2025-06-25T14:04:35.909Z"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/eslint/rewrite/tree/main/packages/plugin-kit#readme", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/plugin-kit"}, "description": "Utilities for building ESLint plugins.", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# ESLint Plugin Kit\n\n## Description\n\nA collection of utilities to help build ESLint plugins.\n\n## Installation\n\nFor Node.js and compatible runtimes:\n\n```shell\nnpm install @eslint/plugin-kit\n# or\nyarn add @eslint/plugin-kit\n# or\npnpm install @eslint/plugin-kit\n# or\nbun add @eslint/plugin-kit\n```\n\nFor Deno:\n\n```shell\ndeno add @eslint/plugin-kit\n```\n\n## Usage\n\nThis package exports the following utilities:\n\n- [`ConfigCommentParser`](#configcommentparser) - used to parse ESLint configuration comments (i.e., `/* eslint-disable rule */`)\n- [`VisitNodeStep` and `CallMethodStep`](#visitnodestep-and-callmethodstep) - used to help implement `SourceCode#traverse()`\n- [`Directive`](#directive) - used to help implement `SourceCode#getDisableDirectives()`\n- [`TextSourceCodeBase`](#textsourcecodebase) - base class to help implement the `SourceCode` interface\n\n### `ConfigCommentParser`\n\nTo use the `ConfigCommentParser` class, import it from the package and create a new instance, such as:\n\n```js\nimport { ConfigCommentParser } from \"@eslint/plugin-kit\";\n\n// create a new instance\nconst commentParser = new ConfigCommentParser();\n\n// pass in a comment string without the comment delimiters\nconst directive = commentParser.parseDirective(\n\t\"eslint-disable prefer-const, semi -- I don't want to use these.\",\n);\n\n// will be undefined when a directive can't be parsed\nif (directive) {\n\tconsole.log(directive.label); // \"eslint-disable\"\n\tconsole.log(directive.value); // \"prefer-const, semi\"\n\tconsole.log(directive.justification); // \"I don't want to use these.\"\n}\n```\n\nThere are different styles of directive values that you'll need to parse separately to get the correct format:\n\n```js\nimport { ConfigCommentParser } from \"@eslint/plugin-kit\";\n\n// create a new instance\nconst commentParser = new ConfigCommentParser();\n\n// list format\nconst list = commentParser.parseListConfig(\"prefer-const, semi\");\nconsole.log(Object.entries(list)); // [[\"prefer-const\", true], [\"semi\", true]]\n\n// string format\nconst strings = commentParser.parseStringConfig(\"foo:off, bar\");\nconsole.log(Object.entries(strings)); // [[\"foo\", \"off\"], [\"bar\", null]]\n\n// JSON-like config format\nconst jsonLike = commentParser.parseJSONLikeConfig(\n\t\"semi:[error, never], prefer-const: warn\",\n);\nconsole.log(Object.entries(jsonLike.config)); // [[\"semi\", [\"error\", \"never\"]], [\"prefer-const\", \"warn\"]]\n```\n\n### `VisitNodeStep` and `CallMethodStep`\n\nThe `VisitNodeStep` and `CallMethodStep` classes represent steps in the traversal of source code. They implement the correct interfaces to return from the `SourceCode#traverse()` method.\n\nThe `VisitNodeStep` class is the more common of the two, where you are describing a visit to a particular node during the traversal. The constructor accepts three arguments:\n\n- `target` - the node being visited. This is used to determine the method to call inside of a rule. For instance, if the node's type is `Literal` then ESLint will call a method named `Literal()` on the rule (if present).\n- `phase` - either 1 for enter or 2 for exit.\n- `args` - an array of arguments to pass into the visitor method of a rule.\n\nFor example:\n\n```js\nimport { VisitNodeStep } from \"@eslint/plugin-kit\";\n\nclass MySourceCode {\n\ttraverse() {\n\t\tconst steps = [];\n\n\t\tfor (const { node, parent, phase } of iterator(this.ast)) {\n\t\t\tsteps.push(\n\t\t\t\tnew VisitNodeStep({\n\t\t\t\t\ttarget: node,\n\t\t\t\t\tphase: phase === \"enter\" ? 1 : 2,\n\t\t\t\t\targs: [node, parent],\n\t\t\t\t}),\n\t\t\t);\n\t\t}\n\n\t\treturn steps;\n\t}\n}\n```\n\nThe `CallMethodStep` class is less common and is used to tell ESLint to call a specific method on the rule. The constructor accepts two arguments:\n\n- `target` - the name of the method to call, frequently beginning with `\"on\"` such as `\"onCodePathStart\"`.\n- `args` - an array of arguments to pass to the method.\n\nFor example:\n\n```js\nimport { VisitNodeStep, CallMethodStep } from \"@eslint/plugin-kit\";\n\nclass MySourceCode {\n    traverse() {\n        const steps = [];\n\n        for (const { node, parent, phase } of iterator(this.ast)) {\n            steps.push(\n                new VisitNodeStep({\n                    target: node,\n                    phase: phase === \"enter\" ? 1 : 2,\n                    args: [node, parent],\n                }),\n            );\n\n            // call a method indicating how many times we've been through the loop\n            steps.push(\n                new CallMethodStep({\n                    target: \"onIteration\",\n                    args: [steps.length]\n                });\n            )\n        }\n\n        return steps;\n    }\n}\n```\n\n### `Directive`\n\nThe `Directive` class represents a disable directive in the source code and implements the `Directive` interface from `@eslint/core`. You can tell ESLint about disable directives using the `SourceCode#getDisableDirectives()` method, where part of the return value is an array of `Directive` objects. Here's an example:\n\n```js\nimport { Directive, ConfigCommentParser } from \"@eslint/plugin-kit\";\n\nclass MySourceCode {\n\tgetDisableDirectives() {\n\t\tconst directives = [];\n\t\tconst problems = [];\n\t\tconst commentParser = new ConfigCommentParser();\n\n\t\t// read in the inline config nodes to check each one\n\t\tthis.getInlineConfigNodes().forEach(comment => {\n\t\t\t// Step 1: Parse the directive\n\t\t\tconst { label, value, justification } =\n\t\t\t\tcommentParser.parseDirective(comment.value);\n\n\t\t\t// Step 2: Extract the directive value and create the `Directive` object\n\t\t\tswitch (label) {\n\t\t\t\tcase \"eslint-disable\":\n\t\t\t\tcase \"eslint-enable\":\n\t\t\t\tcase \"eslint-disable-next-line\":\n\t\t\t\tcase \"eslint-disable-line\": {\n\t\t\t\t\tconst directiveType = label.slice(\"eslint-\".length);\n\n\t\t\t\t\tdirectives.push(\n\t\t\t\t\t\tnew Directive({\n\t\t\t\t\t\t\ttype: directiveType,\n\t\t\t\t\t\t\tnode: comment,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\tjustification,\n\t\t\t\t\t\t}),\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\t// ignore any comments that don't begin with known labels\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tdirectives,\n\t\t\tproblems,\n\t\t};\n\t}\n}\n```\n\n### `TextSourceCodeBase`\n\nThe `TextSourceCodeBase` class is intended to be a base class that has several of the common members found in `SourceCode` objects already implemented. Those members are:\n\n- `lines` - an array of text lines that is created automatically when the constructor is called.\n- `getLoc(node)` - gets the location of a node. Works for nodes that have the ESLint-style `loc` property and nodes that have the Unist-style [`position` property](https://github.com/syntax-tree/unist?tab=readme-ov-file#position). If you're using an AST with a different location format, you'll still need to implement this method yourself.\n- `getRange(node)` - gets the range of a node within the source text. Works for nodes that have the ESLint-style `range` property and nodes that have the Unist-style [`position` property](https://github.com/syntax-tree/unist?tab=readme-ov-file#position). If you're using an AST with a different range format, you'll still need to implement this method yourself.\n- `getText(nodeOrToken, charsBefore, charsAfter)` - gets the source text for the given node or token that has range information attached. Optionally, can return additional characters before and after the given node or token. As long as `getRange()` is properly implemented, this method will just work.\n- `getAncestors(node)` - returns the ancestry of the node. In order for this to work, you must implement the `getParent()` method yourself.\n\nHere's an example:\n\n```js\nimport { TextSourceCodeBase } from \"@eslint/plugin-kit\";\n\nexport class MySourceCode extends TextSourceCodeBase {\n\t#parents = new Map();\n\n\tconstructor({ ast, text }) {\n\t\tsuper({ ast, text });\n\t}\n\n\tgetParent(node) {\n\t\treturn this.#parents.get(node);\n\t}\n\n\ttraverse() {\n\t\tconst steps = [];\n\n\t\tfor (const { node, parent, phase } of iterator(this.ast)) {\n\t\t\t//save the parent information\n\t\t\tthis.#parent.set(node, parent);\n\n\t\t\tsteps.push(\n\t\t\t\tnew VisitNodeStep({\n\t\t\t\t\ttarget: node,\n\t\t\t\t\tphase: phase === \"enter\" ? 1 : 2,\n\t\t\t\t\targs: [node, parent],\n\t\t\t\t}),\n\t\t\t);\n\t\t}\n\n\t\treturn steps;\n\t}\n}\n```\n\nIn general, it's safe to collect the parent information during the `traverse()` method as `getParent()` and `getAncestor()` will only be called from rules once the AST has been traversed at least once.\n\n## License\n\nApache 2.0\n\n<!-- NOTE: This section is autogenerated. Do not manually edit.-->\n<!--sponsorsstart-->\n\n## Sponsors\n\nThe following companies, organizations, and individuals support ESLint's ongoing maintenance and development. [Become a Sponsor](https://eslint.org/donate)\nto get your logo on our READMEs and [website](https://eslint.org/sponsors).\n\n<h3>Diamond Sponsors</h3>\n<p><a href=\"https://www.ag-grid.com/\"><img src=\"https://images.opencollective.com/ag-grid/bec0580/logo.png\" alt=\"AG Grid\" height=\"128\"></a></p><h3>Platinum Sponsors</h3>\n<p><a href=\"https://automattic.com\"><img src=\"https://images.opencollective.com/automattic/d0ef3e1/logo.png\" alt=\"Automattic\" height=\"128\"></a> <a href=\"https://www.airbnb.com/\"><img src=\"https://images.opencollective.com/airbnb/d327d66/logo.png\" alt=\"Airbnb\" height=\"128\"></a></p><h3>Gold Sponsors</h3>\n<p><a href=\"https://qlty.sh/\"><img src=\"https://images.opencollective.com/qltysh/33d157d/logo.png\" alt=\"Qlty Software\" height=\"96\"></a> <a href=\"https://trunk.io/\"><img src=\"https://images.opencollective.com/trunkio/fb92d60/avatar.png\" alt=\"trunk.io\" height=\"96\"></a> <a href=\"https://shopify.engineering/\"><img src=\"https://avatars.githubusercontent.com/u/8085\" alt=\"Shopify\" height=\"96\"></a></p><h3>Silver Sponsors</h3>\n<p><a href=\"https://vite.dev/\"><img src=\"https://images.opencollective.com/vite/e6d15e1/logo.png\" alt=\"Vite\" height=\"64\"></a> <a href=\"https://liftoff.io/\"><img src=\"https://images.opencollective.com/liftoff/5c4fa84/logo.png\" alt=\"Liftoff\" height=\"64\"></a> <a href=\"https://americanexpress.io\"><img src=\"https://avatars.githubusercontent.com/u/3853301\" alt=\"American Express\" height=\"64\"></a> <a href=\"https://stackblitz.com\"><img src=\"https://avatars.githubusercontent.com/u/28635252\" alt=\"StackBlitz\" height=\"64\"></a></p><h3>Bronze Sponsors</h3>\n<p><a href=\"https://sentry.io\"><img src=\"https://github.com/getsentry.png\" alt=\"Sentry\" height=\"32\"></a> <a href=\"https://syntax.fm\"><img src=\"https://github.com/syntaxfm.png\" alt=\"Syntax\" height=\"32\"></a> <a href=\"https://cybozu.co.jp/\"><img src=\"https://images.opencollective.com/cybozu/933e46d/logo.png\" alt=\"Cybozu\" height=\"32\"></a> <a href=\"https://www.crosswordsolver.org/anagram-solver/\"><img src=\"https://images.opencollective.com/anagram-solver/2666271/logo.png\" alt=\"Anagram Solver\" height=\"32\"></a> <a href=\"https://icons8.com/\"><img src=\"https://images.opencollective.com/icons8/7fa1641/logo.png\" alt=\"Icons8\" height=\"32\"></a> <a href=\"https://discord.com\"><img src=\"https://images.opencollective.com/discordapp/f9645d9/logo.png\" alt=\"Discord\" height=\"32\"></a> <a href=\"https://www.gitbook.com\"><img src=\"https://avatars.githubusercontent.com/u/7111340\" alt=\"GitBook\" height=\"32\"></a> <a href=\"https://nolebase.ayaka.io\"><img src=\"https://avatars.githubusercontent.com/u/11081491\" alt=\"Neko\" height=\"32\"></a> <a href=\"https://nx.dev\"><img src=\"https://avatars.githubusercontent.com/u/23692104\" alt=\"Nx\" height=\"32\"></a> <a href=\"https://opensource.mercedes-benz.com/\"><img src=\"https://avatars.githubusercontent.com/u/34240465\" alt=\"Mercedes-Benz Group\" height=\"32\"></a> <a href=\"https://herocoders.com\"><img src=\"https://avatars.githubusercontent.com/u/37549774\" alt=\"HeroCoders\" height=\"32\"></a> <a href=\"https://www.lambdatest.com\"><img src=\"https://avatars.githubusercontent.com/u/171592363\" alt=\"LambdaTest\" height=\"32\"></a></p>\n<h3>Technology Sponsors</h3>\nTechnology sponsors allow us to use their products and services for free as part of a contribution to the open source ecosystem and our work.\n<p><a href=\"https://netlify.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/netlify-icon.svg\" alt=\"Netlify\" height=\"32\"></a> <a href=\"https://algolia.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/algolia-icon.svg\" alt=\"Algolia\" height=\"32\"></a> <a href=\"https://1password.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/1password-icon.svg\" alt=\"1Password\" height=\"32\"></a></p>\n<!--sponsorsend-->\n", "readmeFilename": "README.md"}