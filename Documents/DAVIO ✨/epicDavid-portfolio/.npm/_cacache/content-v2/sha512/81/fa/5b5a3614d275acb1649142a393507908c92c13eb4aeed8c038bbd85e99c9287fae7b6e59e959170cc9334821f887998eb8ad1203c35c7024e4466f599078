{"_id": "assertion-error", "_rev": "23-34496b51d2e8b2a1fcc8f5a2db0c2769", "name": "assertion-error", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.1.0": {"name": "assertion-error", "version": "0.1.0", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "keywords": [], "repository": {"type": "git", "url": "**************:qualiancy/assertion-error.git"}, "engines": {"node": "*"}, "main": "./index", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"component": "*"}, "_id": "assertion-error@0.1.0", "dist": {"shasum": "555cb007e89be44ba73e7b9600c3907dc381ce2b", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-0.1.0.tgz", "integrity": "sha512-5GDYNFgUTLRv4GignYXrUUHcxAzLafgfWYWzWXTeBiimgLrxSW7HTNNAXnHyypP0mS34OOrQ6xpOwe5iePe48w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSOgg+BYn+W1TNWZFSVXnGIW5/6LO+LwqfQJ048hUxcgIgfQxa0B3fNmKfc2QeVPGMRm9IldEcwByMoh+GNLsTog0="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "assertion-error", "version": "1.0.0", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "**************:chaijs/assertion-error.git"}, "engines": {"node": "*"}, "main": "./index", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"component": "*"}, "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "_id": "assertion-error@1.0.0", "dist": {"shasum": "c7f85438fdd466bc7ca16ab90c81513797a5d23b", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.0.0.tgz", "integrity": "sha512-g/gZV+G476cnmtYI+Ko9d5khxSoCSoom/EaNmmCfwpOvBXEJ18qwFrxfP1/CsIqk2no1sAKKwxndV0tP7ROOFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnmhFogTTZa4E1MfbbZn2eDsEN2qxGw6nImAN7NjImFgIhAMbkTUU91blDDpuAzdP6jBBIGHukoDrHhkjD8RtJHQME"}]}, "_from": ".", "_npmVersion": "1.2.23", "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "assertion-error", "version": "1.0.1", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "**************:chaijs/assertion-error.git"}, "engines": {"node": "*"}, "main": "./index", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"component": "*"}, "gitHead": "db10d2fc753f00b3dad24956921056eaf1e03708", "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "homepage": "https://github.com/chaijs/assertion-error", "_id": "assertion-error@1.0.1", "_shasum": "35aaeec33097f11f42399ecadf33faccd27f5c4c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "dist": {"shasum": "35aaeec33097f11f42399ecadf33faccd27f5c4c", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.0.1.tgz", "integrity": "sha512-vSfN0UZYL0hwYkKhFq48vEI7CUJkgELo+xtcdjM/X9o5NRY4GW8PLQUDxQ1KYaugkPpnws9LCxEGzMfYWzL/Ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/H+TZ2RE5BBywaNop0yBuFVklnwE5RXhO0eARDgB4LwIgeTtsnwuVXY+wqeknKvvsN9xBvIzeeAA1gw18VVEvqMw="}]}, "directories": {}}, "1.0.2": {"name": "assertion-error", "version": "1.0.2", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "git+ssh://**************/chaijs/assertion-error.git"}, "engines": {"node": "*"}, "main": "./index", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"component": "*"}, "gitHead": "b36f593951c1487fa33747c9911025734923f28c", "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "homepage": "https://github.com/chaijs/assertion-error#readme", "_id": "assertion-error@1.0.2", "_shasum": "13ca515d86206da0bac66e834dd397d87581094c", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "5.7.0", "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "dist": {"shasum": "13ca515d86206da0bac66e834dd397d87581094c", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.0.2.tgz", "integrity": "sha512-wzQs0MF+xNG9ji/rooK2bLg8XVqP+dn/IYX6qgejMtmDNB8JRLL+BoBrd4furQNgPaWhJaQRXyMXGa48lzsGtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLcWfk28lkeQX/e39Gdc+vmgfvlluLHY0EUV215S9VfwIgWG+WCGpIZrvbJHJjx6GsOxS63ISwDqJjagoxWwPqATc="}]}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/assertion-error-1.0.2.tgz_1465237527264_0.8082898685242981"}, "directories": {}}, "1.1.0": {"name": "assertion-error", "version": "1.1.0", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "types": "./index.d.ts", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "git+ssh://**************/chaijs/assertion-error.git"}, "engines": {"node": "*"}, "main": "./index", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"component": "*", "typescript": "^2.6.1"}, "gitHead": "faa3f8cbbdba74d2760f9d2e95c008ba9ce4812e", "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "homepage": "https://github.com/chaijs/assertion-error#readme", "_id": "assertion-error@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.8.0", "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==", "shasum": "e60b6b0e8f301bd97e5375215bda406c85118c0b", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBLKjetp37+0lhhi9deyZvXmBZcF/3rW0uJJtY/162bGAiA/3giMIaJv7sRAruur386mr5e9OzyX7FBDJo6ugT1cxQ=="}]}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/assertion-error-1.1.0.tgz_1515337170361_0.5846316555980593"}, "directories": {}}, "2.0.0": {"name": "assertion-error", "version": "2.0.0", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "types": "./dist/mod.d.ts", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "git+ssh://**************/chaijs/assertion-error.git"}, "engines": {"node": ">=12"}, "type": "module", "module": "./dist/mod.js", "main": "./dist/mod.js", "scripts": {"build": "tsc", "pretest": "npm run build", "test": "NODE_ENV=test node ./test/index.js"}, "devDependencies": {"typescript": "^4.4.3"}, "gitHead": "06761d175ab7d188c18c1c7ed1ea5ae4a6a4d52c", "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "homepage": "https://github.com/chaijs/assertion-error#readme", "_id": "assertion-error@2.0.0", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-Q0en67fcMTcBp1xn4DTYasTSKD4KAo86UhA6lr+c6neaz2gelKi5ILIqLcYl9JGWkGGbooYLs2iGQv7v1e83AA==", "shasum": "791a05c4b3c3f70652b51dcda070b9ef89da662c", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-2.0.0.tgz", "fileCount": 5, "unpackedSize": 5776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2j2mCRA9TVsSAnZWagAAj3MP/jHlgRzlMC4+uXz+3raT\ntp7ow0G53y7WkQXSou8+deifgO/AKPbgX5osfckjobDllMBRd8KqMvMn6I4X\nIdywrmDkXn2nutVGTwFi616leeXUlnyht7Jst6zhIEjDukPQ/RBZ+qT8V8H3\nvbzrmpjXj1EZdEMHnKQMdPriFe8ueZBE/bzpDS5AW/seaI/BkEAfx97HesVd\nicHcoOvlLgt7SWGfBArUlZC6ufB8eOl/LFpBaFh7fXHjh/kBmDGGEz3eGXU9\nOhgMgpg5k9/shMb3kIf8hJ/31eWHf9vcU7QPv5U1+z3FaKt/hdCaWfrjpkAz\ne1UE0j+qwYOKWYYyrjFmIS4ZSUf4tzP+y1Rz91kZHKZqipvVARhELGRclrSh\nxINCWcqGmOiuqnh4y3F4gC/K1IGN7rth4Ha67u+yXTFOLwMY0eTVpvu/1OrN\n+sEbccNw+uWO7T+itkc4/DuwFdpCa+LRcOterYmxgSyVoCrwsD1/oeYh6tUN\nMhZXXEoN1+Gkc3rsDc3q53rz6mNSmW+otUUhOjrfLOE6fzl6m9pBLCFTFT6V\ndT60KPLlN4Kv7tEBol/AS4bDu320NpXo2IB/QtKLFHlmxXrKEYXvhVHOUpzh\nWDa2bNhBFKkFoiBG6Z8GTGRup4rQvVtbOwHOP0ygHvWuKOujFP5sZMqPP3kS\nbxPQ\r\n=vExu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCb5vGpzCFi2x+1kbn4dHWMTFIn60/LpTEVxMDoTLNFOAIgO/CAtZzdsNwg94EdSSQlwPA2nyk+rmmGBz09JVL3dIA="}]}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/assertion-error_2.0.0_1633447251025_0.35381096573804993"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "assertion-error", "version": "2.0.1", "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "license": "MIT", "types": "./index.d.ts", "keywords": ["test", "assertion", "assertion-error"], "repository": {"type": "git", "url": "git+ssh://**************/chaijs/assertion-error.git"}, "engines": {"node": ">=12"}, "type": "module", "module": "index.js", "main": "index.js", "scripts": {"build": "deno bundle mod.ts > index.js", "pretest": "rm -rf coverage/", "test": "deno test --coverage=coverage", "posttest": "deno coverage coverage --lcov > coverage/lcov.info && lcov --summary coverage/lcov.info"}, "_id": "assertion-error@2.0.1", "gitHead": "08a1f16ece0c5d4b916c9ce1479a776df0e203e0", "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "homepage": "https://github.com/chaijs/assertion-error#readme", "_nodeVersion": "21.0.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==", "shasum": "f641a196b335690b1070bf00b6e7593fec190bf7", "tarball": "https://registry.npmjs.org/assertion-error/-/assertion-error-2.0.1.tgz", "fileCount": 5, "unpackedSize": 5832, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBRJCTrR95tAeT97y/jwEH/TYBmhXoWLCu5LGkLt/20gIgeZjIf2gTu0Glu/2z4g2rUscWL0j6COoTNZdxrSpcXZQ="}]}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/assertion-error_2.0.1_1697652253337_0.3545746301491721"}, "_hasShrinkwrap": false}}, "readme": "<p align=center>\n  AssertionError and AssertionResult classes.\n</p>\n\n<p align=center>\n  <a href=\"https://github.com/chaijs/assertion-error/actions\">\n    <img\n      alt=\"build:?\"\n      src=\"https://github.com/chaijs/assertion-error/actions/workflows/nodejs.yml/badge.svg\"\n    />\n  </a><a href=\"https://www.npmjs.com/package/assertion-error\">\n    <img\n      alt=\"downloads:?\"\n      src=\"https://img.shields.io/npm/dm/assertion-error.svg\"\n    />\n  </a><a href=\"\">\n    <img\n      alt=\"devDependencies:none\"\n      src=\"https://img.shields.io/badge/dependencies-none-brightgreen\"\n    />\n  </a>\n</p>\n\n## What is AssertionError?\n\nAssertion Error is a module that contains two classes: `AssertionError`, which\nis an instance of an `Error`, and `AssertionResult` which is not an instance of\nError.\n\nThese can be useful for returning from a function - if the function \"succeeds\"\nreturn an `AssertionResult` and if the function fails return (or throw) an\n`AssertionError`.\n\nBoth `AssertionError` and `AssertionResult` implement the `Result` interface:\n\n```typescript\ninterface Result {\n  name: \"AssertionError\" | \"AssertionResult\";\n  ok: boolean;\n  toJSON(...args: unknown[]): Record<string, unknown>;\n}\n```\n\nSo if a function returns `AssertionResult | AssertionError` it is easy to check\n_which_ one is returned by checking either `.name` or `.ok`, or check\n`instanceof Error`.\n\n## Installation\n\n### Node.js\n\n`assertion-error` is available on [npm](http://npmjs.org).\n\n```\n$ npm install --save assertion-error\n```\n\n### Deno\n\n`assertion_error` is available on\n[Deno.land](https://deno.land/x/assertion_error)\n\n```typescript\nimport {\n  AssertionError,\n  AssertionResult,\n} from \"https://deno.land/x/assertion_error@2.0.0/mod.ts\";\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "chai<PERSON>s"}], "time": {"modified": "2024-05-09T08:51:10.880Z", "created": "2013-04-07T22:59:41.018Z", "0.1.0": "2013-04-07T22:59:42.826Z", "1.0.0": "2013-06-08T20:41:17.202Z", "1.0.1": "2015-03-05T23:45:54.576Z", "1.0.2": "2016-06-06T18:25:29.009Z", "1.1.0": "2018-01-07T14:59:31.257Z", "2.0.0": "2021-10-05T15:20:51.185Z", "2.0.1": "2023-10-18T18:04:13.507Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "repository": {"type": "git", "url": "git+ssh://**************/chaijs/assertion-error.git"}, "homepage": "https://github.com/chaijs/assertion-error#readme", "keywords": ["test", "assertion", "assertion-error"], "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}