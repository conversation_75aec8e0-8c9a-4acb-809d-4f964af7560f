{"_id": "node-notifier", "_rev": "363-940f7d54acbfd9275b6b5d5fd0d2342d", "name": "node-notifier", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "dist-tags": {"latest": "10.0.1"}, "versions": {"0.0.1": {"name": "node-notifier", "version": "0.0.1", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "node-notifier@0.0.1", "dist": {"shasum": "69429afc2a0396bbac22e500e297a8e75473ea1b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-0.0.1.tgz", "integrity": "sha512-qTCnQgHISZ1WBz4w+CKWTuioKJp/Eh1xeADpiltk/Z3nWIwkFRocooLDgLTAPWred+xQXYdV+hRJ42fD2WUkbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWMP4Q7HCJzr+n524co7bxsFk70T0ev0af177/u1rh0AiEA1QY27n89m69RbkV79gwDiDvbp7L1SumX4g193Z4TX9A="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "0.0.2": {"name": "node-notifier", "version": "0.0.2", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "node-notifier@0.0.2", "dist": {"shasum": "df6aa8625c36d2644f251b015089ebf13c5c1307", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-0.0.2.tgz", "integrity": "sha512-euxt68N4K2+aYARDsKIjwBoJKeswjy2ChvFigEI4HAJu3H0LLfCUFxQXECUuM7Y3o2khJ/0KWppSba1Iwg0f2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHQwwY8I5cF+LTS8eflHGDqFmNKE/3h2qOs2VK8vSCxgIgF3LOTajh4LEgNO1Ah22QvV41HHOoPR8c7YUfyCiZWcI="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "0.5.0": {"name": "node-notifier", "version": "0.5.0", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_id": "node-notifier@0.5.0", "dist": {"shasum": "e722c00b6a60e19ca264088db51f4da1f27e3559", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-0.5.0.tgz", "integrity": "sha512-bJP6TD/6QVfG3RydFeLcMgrJOzW7XTqVseaObone+Ls7G6uou0gi61KY9XB33RUX2z6OjKcRz/MZTPyYY3XIpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUB6BaeNRxa6PLsQdkvySTpOJTrGxcujZb8ttrc/i+uwIgbUyHZ7TkiQWa0jAYZ1wAdcdHSS9+aTurbiNWGqfXgfY="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "1.0.0-beta": {"name": "node-notifier", "version": "1.0.0-beta", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_id": "node-notifier@1.0.0-beta", "dist": {"shasum": "84a8ed23200a5aaf8524f8ec449c830196b73216", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.0.0-beta.tgz", "integrity": "sha512-Tv+V/0xisqkkHoa9Pzf/VDdkuaxo8q/ok+ak2pa5scoO62t6UO6RZuLBbK655ULLNUB14P8IirOgHfcpEFoAyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAGonX76OfBLBPi6l3F3t5qZHmbN5v3uv50LX3ASQzQPAiAsBelKUIO5EIS/gyjUNnKa1wswdsM86Yn4cOgtaTnxgQ=="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "1.0.0-beta2": {"name": "node-notifier", "version": "1.0.0-beta2", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "_id": "node-notifier@1.0.0-beta2", "dist": {"shasum": "17dae3b199766973ff7210431a5a58d3c737a0c9", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.0.0-beta2.tgz", "integrity": "sha512-iWYkOKucspYHVkhJil5NQpyHKepQYm5QfTykVjss4MttJAqgTuBBpx6r1oMAwTnXpODkOImQvDaoTa5td5piRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKcUsVXB2G3bozvweAThJQSJtlp+aTlucWFKEg9vcV6AiB2mQcQrQiGPCidzVkrTRLHgOgK7Ibp0e2gjYLo+gFjvw=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "1.1.0": {"name": "node-notifier", "version": "1.1.0", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "_id": "node-notifier@1.1.0", "dist": {"shasum": "b8e3d32ab2a07ca73f94d4b84bcc17fd62e04792", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.1.0.tgz", "integrity": "sha512-sFNuhh6DLdAPiBpYh4ua0vuo+aGNZRoApnqgvCC6p1PTJbgALLd4HED6KiI2Ev1Pb9lE1HDn3uv3ZsDBuhhbeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEK5p7BERDl2Hf+RyD4zb+Tm57XkuLS4V0KCIPnLXv27AiEAk+IacHQH4/4Z+HynWyUqDakZS9XBHU7WfALTfFrLF6E="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "1.1.1-1": {"name": "node-notifier", "version": "1.1.1-1", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "_id": "node-notifier@1.1.1-1", "dist": {"shasum": "d5abd5beaba7bcf78582ae80a9854c096e60c6a2", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.1.1-1.tgz", "integrity": "sha512-vXymi+J/HEccaS5QwnU26Y81xKo/GYJ+N9Oia7Gwt+6gda+pW6E0EC6RnU/cnrYfjVCvNQm30wJl1wamKy6/3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIERWxzY8JwhDsKgOD7nBpQnKWRdR1wfaM/g1Q4cJdwcEAiEAwXKZfbaXp4PyH+upqkQNF3Qhb/7eQ2dJgFw53N/svy0="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Linux support added and code-bases fixed"}, "1.1.1-2": {"name": "node-notifier", "version": "1.1.1-2", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "_id": "node-notifier@1.1.1-2", "dist": {"shasum": "d6ddcfee1724951fc16387d64abdedbb0ce2ff9e", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.1.1-2.tgz", "integrity": "sha512-KmlFoIsMLYOdTg308vAH+550Vtpvmck6a/i18FJ2OONZvKW4cKCjEtrnoqahw/UGwaQFilVP/vUELzKIgXf9aA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCumkv1xe9/TEaWrc/xI+3S2iTTTGtQNc0dv1BFfSKBTAIgQgt1f+05qI4JBRGqQ3CEvGlj3W06Ti7Pv8mojr2o5Ck="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "1.1.2-0": {"name": "node-notifier", "version": "1.1.2-0", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "_id": "node-notifier@1.1.2-0", "dist": {"shasum": "a2a6c5acb0a3390361753a6bc5ff5f728a31374f", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.1.2-0.tgz", "integrity": "sha512-ZDVbzCsuq2MBvN17UHY+BQDzdkehfOE0vpAkA0r/sdlV6W+oOfBkuIa4YosWrQoJ7CaJsAtYkt727IwsqQhcQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB7mlIBNwlmTXCQCJEdV018jT05pJfBHGD407jdBpeARAiABCw5VoWpSlkhKJN9UACCyUTtDQgJwiuhwzMk7t8QIKg=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "1.2.0": {"name": "node-notifier", "version": "1.2.0", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@1.2.0", "dist": {"shasum": "7ca0a5e60f0ea5193b787177d7ed4d50821735f0", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.2.0.tgz", "integrity": "sha512-ek3Nn9lnFjaeD49Gx4p3d2TmMmtN9l4xW/V+j1zg2i/6AWzLROl7NYwoV9BzkNN580Im7oZ5t1KCVGZWRIpFRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/mFEWopIVawnFcULOWyMlikTt5URb71rjOrFS/H6ziwIgRvYHAnZKqux+kUrfxfLvipeU0wFtcF0xKY7F7FFPFwQ="}]}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "1.2.1": {"name": "node-notifier", "version": "1.2.1", "description": "A Node.js wrapper for the terminal-notifier application", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@1.2.1", "dist": {"shasum": "4dcab5c8b00893203cd6c534bec44c465d96a90f", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-1.2.1.tgz", "integrity": "sha512-aFi0/pzq6xNUfGMLA6XtjqhwEfoj9aeeaUjbb/nWujLRpu/GNdHEPp2giJqgAqlQTeQFlEzCU37T1t0+virlsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGOb3hJx3Ya0T1y/0XBmTAKo6Da4UHOdTLilfHnC6MfgIgf9jv1gApFcpLWJq2gW9j6VJqkH9vlntprbGKqhxumUo="}]}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.0-alpha": {"name": "node-notifier", "version": "2.0.0-alpha", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.0-alpha", "dist": {"shasum": "94d44d7c4e4252ddf912380be7cedbf2fd298bc9", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.0-alpha.tgz", "integrity": "sha512-tNeTpq3NVpOwgJuYOLFKNoqEARw00emVsWm+caxIzGpiB9iO6TvXxISOyGjGSDP3bQgZ8FMbcwhu5bRn+AwPrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBBw5kGTa1YwdsQ8sMiV9/Sp1eVKsPmSNjS9gcTlQqMbAiEAklSfAXTZAcDwfTD+t8rMuAFnWw6BlpFUeC4OGlYt9AU="}]}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.1": {"name": "node-notifier", "version": "2.0.1", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.1", "dist": {"shasum": "429e3a4776aa055a47e24fd149db765698deff5b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.1.tgz", "integrity": "sha512-UV7Gi2Bw17RaAQ9lC/ZI+BCQppbF3oDSVzleTfHj0gd+fvqX8E8/lVyEtIYGkrwRM8GA4fh2fkyVmWhc0cyo2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAd/UysSInrCX3fiaoCDy5/rGzcCH9x098t5YFzOIw+XAiAyEGzVuWB7C3LlNxyFLYCd6GGsrPK4ednka0NWMa8c/A=="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.2": {"name": "node-notifier", "version": "2.0.2", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "^0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.2", "dist": {"shasum": "76b375b61ae4d106492841d3c672799ad36fd06f", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.2.tgz", "integrity": "sha512-FqseYjyRQRyu7H5FeRRdiB798EbhWp3VKYF6ud7wytDKZHuNW6YMQoXDB+Vo/o1iw0IIV1n5qSOTit34h7jZRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqshR/3B5KuNK/96Rj7l0r4qfN55EQ+m/vRF0pSS+PLwIhAPd0oMFsj8j4j2zYUy05B8rMa/JUFeBwU/0OBuXUOtlx"}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.3": {"name": "node-notifier", "version": "2.0.3", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "^0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.3", "dist": {"shasum": "070a80a79ee8d0f8e7b29bf2a20fcff3c1c28645", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.3.tgz", "integrity": "sha512-QSXPbLVQfetrKYoekUgsUHisCVq+0S2Ihd6O7Yl9Z4UWVeNrU2OWjWYUDe6SBS5pN8tLr2GsJ+A3k4pNCRQfQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgfMvqdBCydOTwiutQy0v5N84zcKdP8QK9wFSmc2i1UAiEAiCbOPWb5gPFAd5YzliI34tLxWSYJWkUEdL4P+K0HeW0="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.4": {"name": "node-notifier", "version": "2.0.4", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "^0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.4", "dist": {"shasum": "61db04ffd1c2ea2156c4acf1142ee0424ca0a821", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.4.tgz", "integrity": "sha512-TDsIYwpKVywBd1Bgr6WbPmvvEKeiaIkCbznWFTewNPfffsSbhkTMZM9BCHJ1HrrFQ3M+wlclJFWY8Yfky1nvwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhShCYK81UYsdsxWqbOhO/Kl08AD9pIYBfQ4gnP0Jh7QIhAIjP67NTNarMcgazXvT31NtcUU50LDKaVsBqhdeCACxB"}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.5": {"name": "node-notifier", "version": "2.0.5", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "^0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.5", "dist": {"shasum": "38815cf58f070ba528663505b69090ff3f7cf9ec", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.5.tgz", "integrity": "sha512-Cb1Sb07SLxxww99HTW74PLV/RT4SKBgU5WkPeY82s5GXFevgb9TpdZ0PkghLWhIDbwyJJFVwDichpV4BdiphmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuuHZHZKQx/fF34Irb2CGfvThwVrNcoKkUdBglesOTJAIgAj2jdvrC3WRb/scmTtVw14B4iDcmwhrsFFI0NWcO7CM="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "2.0.6": {"name": "node-notifier", "version": "2.0.6", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "^0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@2.0.6", "dist": {"shasum": "df2104807fc2334a9aa9037b4ef11e726b26591a", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-2.0.6.tgz", "integrity": "sha512-yjpYuhiSNA511HQzLZoRI7LDX7meyIcNlyD/UG5067t8y5iYSYHzN5iS7BXEwKPXBCJwEmw1X36fAe4e3IL2lQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjpJPp1N6YNjqpg/9GXpYZQKOBOFHePb5CdSOkDxYx7wIgV/OrT/JJ0cGEyhvjMgU5YP/lyPc9V8+N1KfnEUF3S+s="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.0.0": {"name": "node-notifier", "version": "3.0.0", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "^0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.0.0", "dist": {"shasum": "6160ce68728fdbf3a2df27dfdcb29cfa2881dab1", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.0.0.tgz", "integrity": "sha512-vvEFX660nVnouFcBkTZ90a9ZnxuQAdGeYMnki53KK5Up0qY3L7kdKRFNqtEElLYy5b8/4WfgcWson3M1FXfYKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbEqfVBL2c4xs1gQ5A9UePTfJbM3f0E4mRLSfV5sk5RQIhAJ8toqywjpG/caw9EokJolJ1UePYGuRNms1DJff2Dg+E"}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.0.1": {"name": "node-notifier", "version": "3.0.1", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.0.1", "dist": {"shasum": "c66c3d9e723369b10499478b2180bdd3b514aff1", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.0.1.tgz", "integrity": "sha512-5WY7jVQ6Ob//J7dIeq7bqhN/FWllr0zkp3cx54WtI8Gi8sbf2QVqwCyb6W3HymzdL20iusDfvc+2JZtwPaCdNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGtLOAgiB3Bryd/M+lyViws3GVMhok1YCWd46fJOr8V+AiEA/xnOxg6UFjoWmZF0+k3Zl+uUkqG7h0x0S3iO3Dt0n9c="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.0.2": {"name": "node-notifier", "version": "3.0.2", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.0.2", "dist": {"shasum": "47a3bfff4138d304282857e9ca9e32d9b5c1ca24", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.0.2.tgz", "integrity": "sha512-/eBasPXFUgfv41IAjRzydX/QRmUZdlw3DE98ssNG8INtLohVXL5pThtBc6bC+hHxY2HXfhKLaggCn2BZLjeYng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEBb99M/SHujMKvh9/TGjtOLaZAGEvC+i0Q3mkxrhkaCAiEA/pem5U9OuJJlSgcl3s89eKhezYAAHjxyfBo1HOHCUXo="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.0.3": {"name": "node-notifier", "version": "3.0.3", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.0.3", "_shasum": "5b3133a4effe89f3d238a0ab8d165a938910593c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "5b3133a4effe89f3d238a0ab8d165a938910593c", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.0.3.tgz", "integrity": "sha512-mB/ugaDfiStn/APpKZyrlg8Z9y41eD9eJhiKF6XV/aYtIjC1Q8w7HVpC1U6/bLIq7SM80FUOb3xw2KCORJCEew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDc1/82AQsGHMURF4Cdi7JDi4Ggv2lVVRLd5xmRRDgaqQIgYjbgvugsMM8CkB7igro2XUQeSSCHb6xtjYYlxRQDSys="}]}, "deprecated": "Bug in 3.0.3 breaks the run for mac"}, "3.0.4": {"name": "node-notifier", "version": "3.0.4", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.0.4", "dist": {"shasum": "d632828367e5f08bf06ddb6b3915dc5b4a9e2b5b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.0.4.tgz", "integrity": "sha512-8wfZmWfSIsMDXleGPAPI0GtCFZY9uh3uRqSZ7AsWfynmHTZEl3YYklmEGDLyXKR0RQdZ/BL5bf08bpvlKstzQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAs87qe2RA1voAgafOA9rDBKnRkSKpBey/av/SbBHUAgIgR8OMsL/5+44CcU9nMzDfZlHowOQVdOyPfykr3JcrJwk="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.0.6": {"name": "node-notifier", "version": "3.0.6", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.0.6", "dist": {"shasum": "ac21eeb74ef245d54255f84e7b302e948a559b76", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.0.6.tgz", "integrity": "sha512-80V+j8edChbW0w4AC8f6cmrynkzOaVGJu3mhqWopQ7tvECIhrgO56bQrzafSe4ZtdgCrk/ej8qBy6x773UwUBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGFZOxioY4CI1Ktg2daZG0XGBQwp26B0NltyB7qjEhI7AiAG0LfkXMCLNmO9OMn3ZoG13qoLoHA8gRHdJILYxyRV3A=="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.1.1": {"name": "node-notifier", "version": "3.1.1", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.1.1", "dist": {"shasum": "3e330ad56aec7c87de68271325d438e8113b498b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.1.1.tgz", "integrity": "sha512-AdzB/Bwbv/xJW1Ngwjl2/OhTv0K6sx+ONufUeNXbBukp5e/Iz5YS2ooECyKhXdVnh1oGL6oYwWmYQZmzETclkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDecHAIBHayan72fJi47HCR7FgGZmEARB6lz/TWMFhJKwIhANbbtXY2Y0aBDU9EfLuQMif3Wl6SoZUspNWWwQhw1nMn"}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.1.2": {"name": "node-notifier", "version": "3.1.2", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.1.2", "_shasum": "d1aa8a181dbbb89fdef2b13f24729ba73fd50a89", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "d1aa8a181dbbb89fdef2b13f24729ba73fd50a89", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.1.2.tgz", "integrity": "sha512-CdKu0tCStP6PenvR9JoFimEqWmjBXu1U/oFFNpxe2+WpUYqvxx2eUSlfjlu6h2KbENppAgfdTVztCLrTiHiZuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDErdcUGc7eVVgbk3hDAlFshcFb7oPUsFqMyQZ0WB70MAiBOOuPfIf4Ey8HafOQiNR/2Qa1aOV0ABxb30IJCVk69HQ=="}]}}, "3.1.3": {"name": "node-notifier", "version": "3.1.3", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "dependencies": {"which": "~1.0.5", "growler": "0.0.1", "shellwords": "~0.1.0"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.1.3", "_shasum": "d3baf432ae4fe5d99bef1198b2126be740eb60f9", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "d3baf432ae4fe5d99bef1198b2126be740eb60f9", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.1.3.tgz", "integrity": "sha512-MWM0Y+8HC3fyTMggP2u4snreVr+qG06guVaew2aOAIdnQBWHPrcOJrt15tt5KhvfQgCf/LWXgkXfSg3wNIPT5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFPnLT6MiaON4a4VtO1qVGTjNXfdgud+CUe/YlfAMOYYAiEAlbmBxC58b4ipOM7H28vkzM/5L6WUVReKEYfZCtOdp9o="}]}}, "3.2.0": {"name": "node-notifier", "version": "3.2.0", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"growler": "^0.0.1", "semver": "^3.0.1", "shellwords": "^0.1.0", "which": "^1.0.5"}, "gitHead": "3e2b9a1427e1ac29a4d170948b18ae1d84c802e6", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.2.0", "_shasum": "c3a6064981082c001091022e2a0a088477506a31", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "c3a6064981082c001091022e2a0a088477506a31", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.2.0.tgz", "integrity": "sha512-SREKOoqUnh6Tg7aKpttumTksiRCRizTNJCY4VCG6LmFEgAtwL8Jmaq//feKXEw2TdSvtNyprDARPQfvXjmslLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGjeZdm57fYSyy9lsfBIGt6jFaaWUGZZZ420Wd/b5a/AiAhfDhQiD25RP2tM1rEWbhPRve9fH2NiUFLyccom+0I5A=="}]}, "deprecated": "Pathes with spaces didnt work on Windows."}, "3.2.1": {"name": "node-notifier", "version": "3.2.1", "description": "A Node.js module for sending notifications on mac, windows and linux", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"growler": "^0.0.1", "semver": "^3.0.1", "shellwords": "^0.1.0", "which": "^1.0.5"}, "gitHead": "06ffd9f61be9fe136019b14c1acc9029c19e7098", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.2.1", "_shasum": "96e1d75f0cf4c7ba4d2a6a6e5a00d465bf4c3da0", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "96e1d75f0cf4c7ba4d2a6a6e5a00d465bf4c3da0", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.2.1.tgz", "integrity": "sha512-zogC2FBES8Wf9JbPfehC6zbi10cFj4q8vSIA2caWka38aCsdpV9azM1HZ4mWj6WMNeQgWNJRTOdoY+sD2HBMbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDf0RZdbzSyZDf/ptp8LztsRKoVvpLZeAjNQflAwto9wIhAMCwwV8XTr+n+TmeGSwjwZYtFJdVIEB5+7Kn5FtTXEhu"}]}}, "3.3.0": {"name": "node-notifier", "version": "3.3.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^3.0.1", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.3.0", "dist": {"shasum": "e61d65e28a0681418df4349ca044557f9e507d82", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.3.0.tgz", "integrity": "sha512-4udBuuoNtZ/Stu+J0mqvrXvEChCD9ApAvez680SMtLvy68j73xOW3OTg2Dl5ROgQt0m7cVsyOWqUEwygv/A+yw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBXM/3TlYw1ltYjeUDYyJnPLkGZ4hYNvzg3r0Embel+zAiBhtRkZF3Il4wTPT1zL4eAdDSIpu99NlIskZ75cb0Z4sw=="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "3.4.0": {"name": "node-notifier", "version": "3.4.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^3.0.1", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.4.0", "dist": {"shasum": "12e3773e62543afc2a3824f64627337d4a601779", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.4.0.tgz", "integrity": "sha512-vMjyK13y/JMe+YzMNqnnT1xQBaaZVbmPTFDZ0lSyiA41c3linfIykRObhIAilhlAZXHJK//fPh97krJOhJC5JA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3EBUthraYgdXn8OeQ+9kEyOMCt53ae/OXn7Mo9gWCXwIgG/Z5ysiOKxtxHR7sFJC9XKkTByVoL2LF6qF5k6kMNbY="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "Breaking bug in windows 7 balloons and growl"}, "3.4.1": {"name": "node-notifier", "version": "3.4.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^3.0.1", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@3.4.1", "dist": {"shasum": "ec0ba696bd6097aaf7c6c3e58b23c849f334bda1", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-3.4.1.tgz", "integrity": "sha512-12XRg1FEb+nmNLMvSOMFz76qyTIFEzZWQPMhNhkxabA0dAJzKbVXTgBZ+Rq+c91pChJYHGHRy4RrQD9HYgrFiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW3D5C/O/Tpq7wmc/m1kBofIBWUk4Gm2FVBptXiZO3DwIgfmhtNRi5kfkhIuVxk656ey4wcVaexG1wK2A8qKNvmrw="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "4.0.0": {"name": "node-notifier", "version": "4.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1", "clone": "^0.1.18"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.0.0", "dist": {"shasum": "d34c4436fb5824beee8cee8a6e9304c4e2e472c9", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.0.0.tgz", "integrity": "sha512-jFG5ntgC9sh9xTaOQLhc4LwnlPQwBhkP2X9Um4LZYVTCv0hUHKAC1MYPumf/hE7CYH23TbJvozK0eXJ5WfjCMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPQeGWmOa5pUdqv3phJmGBLAZCVf6Ky1iDuyxw+hLqNQIgZgXpJZuUrwbtESmbukbmiGuDv3nDnph4JFEPLKv48TY="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "deprecated": "NotifySend module does not work without callback"}, "4.0.1": {"name": "node-notifier", "version": "4.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1", "clone": "^0.1.18"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.0.1", "dist": {"shasum": "0869af5b3632149a82243ff1d87a909c1c475612", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.0.1.tgz", "integrity": "sha512-LaA2sqvyw+2oZrwZDA4k8HM7/i1HpCExm4R5VTaWN+kTTKd1mtXcDLKEMoGyeDlyFGzQDzjmwK+zhSb7KPUpSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCr+Th/wwVEulPYH1anYso2L0KdBUeMQHKfAlxh7bG6oQIgJegTL6PcPe/I6CHZbA9i66YCg51ipgfcP4h6QQ9tmvI="}]}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}]}, "4.0.2": {"name": "node-notifier", "version": "4.0.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1", "clone": "^0.1.18"}, "gitHead": "1b5e62e55fcd95d19a698ce3eb0fc9350b3c7557", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.0.2", "_shasum": "b2a4c85b6888060bbea2dd912b55395bafe8631c", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b2a4c85b6888060bbea2dd912b55395bafe8631c", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.0.2.tgz", "integrity": "sha512-4jPxvLwyarrj9v97SZUF1FhM+c61upZK01/pACMP66GJmEJk0Iaj+ovP/bo84Ab9LKKSxlkLTVJCowTlKkfUbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDSk6lj8oGi4jfycziZo8wHFxf9J2tVewNHFicEdku9GAiBbuycD1Eg9ULpCq6pswzUFsI1yldAEs54L31NXnnOB8g=="}]}}, "4.0.3": {"name": "node-notifier", "version": "4.0.3", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5", "growly": "^1.1.1", "clone": "^0.1.18"}, "gitHead": "8bed44d43d3c463a28b62d75d28337684e0030f9", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.0.3", "_shasum": "d536dfaa80922552423ec34edf00e1fade9da91b", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "d536dfaa80922552423ec34edf00e1fade9da91b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.0.3.tgz", "integrity": "sha512-qA754yfGfJTWNyv2r6GpXM7nKZAayb1kwAt4CQKoKKefhceYc0swLsZhmzLHHBPGeLGL01iok/GCD6IT+QBCzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvdpkRz5h/VVrrINMXcC2WwdF4DdYERrZy7S1ozjeSzwIgGwD843pgeJLC4NI2xMAEweEhz5I+qGDo46gydCRtxP4="}]}}, "4.1.0": {"name": "node-notifier", "version": "4.1.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"clone": "^0.1.18", "growly": "^1.2.0", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "gitHead": "dcf2dfaaf57f7bb2aa56b59957f3dea5d6ec0a31", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.1.0", "_shasum": "936c3839e2f62b48b76a1eb52b237ec07a7c1107", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "936c3839e2f62b48b76a1eb52b237ec07a7c1107", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.1.0.tgz", "integrity": "sha512-LZTVB5Z1GY1Oq6KoaCCKMdoMFcOCrpKPBO0QIrSX86bipz6aYgJvAzavfSGQ+4/Cx8E2GofCgZOMRnmT4LIaag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGRrXbvq955bh0iN+F67pId/v/0jN24TFRVFzKyVbDYnAiBVqvgqAgsXhoFZWfbmT02dRGDjTCFtFiTkRWm3nK65iA=="}]}}, "4.1.1": {"name": "node-notifier", "version": "4.1.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"clone": "^0.1.18", "growly": "^1.2.0", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "gitHead": "df0409dcdefbed57c6afea274bf643bc2810541f", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.1.1", "_shasum": "00acfd75e5326e3676614ab5076c179ae59b02c0", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "00acfd75e5326e3676614ab5076c179ae59b02c0", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.1.1.tgz", "integrity": "sha512-sfVD5utFvh+iGKNkaO7F4f8Cqp+HskzwCmugLO/r33v44fYT6xRkf1XKNz/+ZaH+xspmAkoISXxX6xRz3g7BfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3lrMtZBTwu3s9j2QEcZE+B4P/PwtnD2ljL5/tshGVBAiEAq2b9jYnA2TJaECKi6IYsoWHQeuilJOIPAdAD/S+EfNc="}]}, "deprecated": "Activate/click does not work in Notification Center"}, "4.1.2": {"name": "node-notifier", "version": "4.1.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"clone": "^0.1.18", "growly": "^1.2.0", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "gitHead": "140c9b6d27e09d06b8b3f329bcd03aaccc226b83", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.1.2", "_shasum": "54e40913931155cbd30f19c05e6f3ad57820ae4f", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "54e40913931155cbd30f19c05e6f3ad57820ae4f", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.1.2.tgz", "integrity": "sha512-3uNUME5m0/a7XbGNhh2yDyq9M2sNso4eN97B7lYflFGznMm7B7jCeG09ycI15JVPNgteUZr4Vy57fq7JZ8Eggw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEXdV3TyRT/2yS3Bm4AYDjWipafOiC/PZl5+3zTdKYYcAiEA4ic+5cFmKjtjCzGEXDkKIbwDrXqZIKlgafg38OkEG/M="}]}}, "4.2.0": {"name": "node-notifier", "version": "4.2.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "clone": "^0.1.18", "growly": "^1.2.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "570dfeb0ea90614f5f333327af0fbe842b61fe94", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.2.0", "_shasum": "128192214b7ad8652d352f699cdb118994514fa6", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "128192214b7ad8652d352f699cdb118994514fa6", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.2.0.tgz", "integrity": "sha512-1rSeJZPPvLomzqoW257GHxUpGLwp8z0zgWM7zJwq7trGGXOsYbJ7BbfGEeht2ZwUuTWc4X+n1SdCgC9XQ6nIVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoS5FRtBzn7EaR2jyXjU4cwPQgNrO5PpPiAGztJebyaAIgBRTbFlh3BBmJhn6/y0WaSeMwuhmnOH3/E2ZD8DFsvm8="}]}}, "4.2.1": {"name": "node-notifier", "version": "4.2.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "clone": "^0.1.18", "growly": "^1.2.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "d9bd9f8799f47e18cda345c77f0262c82075071a", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.2.1", "_shasum": "497da25034025e099d1df9044379e920727044d9", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "497da25034025e099d1df9044379e920727044d9", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.2.1.tgz", "integrity": "sha512-rr2zhVVX/fHc7qA1koknKB3X6xBZxb0JpRRbQ3gdHVVmrL1OnPjaEMVdT05Eln61/NB/3ovmKgQBQqUmzCkI/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0zx55o7yV/oOKBg+U6FoX9ro2QDJf/fhe5bDtX1MosAIhANg629MDNGjCKP58h++z0/1H0U7hxVIdiGnT71l2tghr"}]}}, "4.2.2": {"name": "node-notifier", "version": "4.2.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "4bca2d28b5745341533281bd229697c28f98956d", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.2.2", "_shasum": "42e2b76f9cf6d5c6264738583350fa7cd04d6085", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "42e2b76f9cf6d5c6264738583350fa7cd04d6085", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.2.2.tgz", "integrity": "sha512-Cwnco+b1mM/qoyJMWmOLw5vTG+nuhHQ3NYsA2gBsSHm2iflwuG1K/TncMoPX+2ab1K3skiv71e00k6z8/85m0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCju9gOwtuxa7f37vJ9SLJII8Gvi3AiHpS5fgmCyTugigIgEgqETX1WYciiY0AWSEyEfrsJS2CcO2oB04z5H1llQCo="}]}}, "4.2.3": {"name": "node-notifier", "version": "4.2.3", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "**************:mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "84ada1633e71abdbdbfcf02ef549bf42f3941fa7", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier", "_id": "node-notifier@4.2.3", "_shasum": "18ce4e6117980e23699b7595b68233cd37483fbf", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "18ce4e6117980e23699b7595b68233cd37483fbf", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.2.3.tgz", "integrity": "sha512-WFoDARVLzhZpKjU7ru8Q5w8pyGLXgl+78h8AetJ6jV7CHDLog7KwsVZ1p1bStZb7CNhjQ/Q2KKNzypaTa/A4aA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBLFEqXTY+qCB3DtRAQEr3Zq9mRWnaipbTrgLHPk4ihiAiEA2IGwypsZ25kmwdGPNpO4ovMq0T5zOIDdcnVQ/ZNFkJY="}]}}, "4.3.0": {"name": "node-notifier", "version": "4.3.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "4a7e6919721ce093b818891fa6d7bacc39e33808", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "_id": "node-notifier@4.3.0", "_shasum": "cb483c3a18b54d054655d610ce16c88624383d1f", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "cb483c3a18b54d054655d610ce16c88624383d1f", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.3.0.tgz", "integrity": "sha512-sTJ66O4cl4cERv7brwD4jTc6lfIM9Js2j46XNgEaVUg+VoGkGN2oCJ0nv8Lmo/kEcZE7of6MZPhH3/Nwoye2Pg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBF7iPOM8wx5SVCBvoaOIR9Xr/pY4h0m9gwb6GluvdWbAiEAiv+AM+sd+q+LJXDtm0fvGipvVHHA6+2NKpC3utS1vpc="}]}}, "4.3.1": {"name": "node-notifier", "version": "4.3.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "9a8e3055e907d3d3db1e646525ed020fec284530", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "_id": "node-notifier@4.3.1", "_shasum": "94a3e35c388938b4d9fa196fed6b36e9ab671119", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "94a3e35c388938b4d9fa196fed6b36e9ab671119", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.3.1.tgz", "integrity": "sha512-oQ+/0jvUYRW/g0S04wgXRxj2SqUoRbhsnegvPDW7VMOETm77j12Ok4hbdC6IdPDVIRduPfIBGANhgAp1kAr/OQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE3FjBRzMKDebAVumPkYfrlRXZciX3r220EBGqLO5P0XAiEAnXub6Cyb8HHRgF7AqmSme8f6GP3+nK5HVCtg1hlz6pU="}]}}, "4.4.0": {"name": "node-notifier", "version": "4.4.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^4.0.3", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "83eb5c19da16d23318f40f7b1cc1b9ce6b5de7fd", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "_id": "node-notifier@4.4.0", "_shasum": "67a3cf281befc3059e30f6cb4ce9dfd6a2346bf5", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "67a3cf281befc3059e30f6cb4ce9dfd6a2346bf5", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.4.0.tgz", "integrity": "sha512-hLt8LUEcCBUhulyvFlN1/fCWnPARw/QnEK1wRS4hkSbC84fqyQ/vSu7IbZ5UpFvQhd6yMtem3VnsgkDB7t5k/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJQL6KPGnc+doQwarSPT4WmedBIUdhoA/U3R8l2fnmPwIgC8OWeEPa2521ooEItEoyFhnB0XWE2M7dCQQX641/T40="}]}}, "4.5.0": {"name": "node-notifier", "version": "4.5.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^5.1.0", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "905e063c45692a72bba807a9e86541902e5e8cac", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "_id": "node-notifier@4.5.0", "_shasum": "029ee98d7a9bc4e96c9cb51be9d4f361323fea9b", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "029ee98d7a9bc4e96c9cb51be9d4f361323fea9b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.5.0.tgz", "integrity": "sha512-A6Uq/OZS370mBKFJtfJL/6+4QU5p10Q1R6adKefj+eIw6NsjeoEe7hp1stB9nhjEsZ7tq8DZTbduPYJT4hUjYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEVIMrNBbjCXrnd5HC0CiMfGtoKYwjaJbJkaOzgvlJxsAiEA5Sap6txhwfwP+EXmiBzP7ENhLNU6muaH+590Tyyf7Fk="}]}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/node-notifier-4.5.0.tgz_1455114957984_0.7074168669059873"}, "deprecated": "Timeout issue on macOS Notification Center"}, "4.6.0": {"name": "node-notifier", "version": "4.6.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^5.1.0", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "8935e481e1e226e8b1a0d7640f7f371abf4c2777", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "_id": "node-notifier@4.6.0", "_shasum": "d6125d417729782b22d37e592ad5f71e83c0816b", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "d6125d417729782b22d37e592ad5f71e83c0816b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.6.0.tgz", "integrity": "sha512-w0MD3t56VlIdm6u835QNNybwlkMmF/95QQQvyJPwm1zBWMOWZQ2IrHRXPLMfj/yik3BiQqq8FmZqyoqfS/t59g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFxYAUx2tzoaJNdYuOl6KfgxaLM+Ykea3XE5PwTrEvrVAiBe49k8Ht4Hbfb7Q2eTtrYsIoZkP9bN10PVc3r6+w2SgA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-notifier-4.6.0.tgz_1464207691413_0.0929349500220269"}}, "4.6.1": {"name": "node-notifier", "version": "4.6.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "directories": {"example": "example"}, "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "^3.0.0", "should": "^4.0.4"}, "dependencies": {"cli-usage": "^0.1.1", "growly": "^1.2.0", "lodash.clonedeep": "^3.0.0", "minimist": "^1.1.1", "semver": "^5.1.0", "shellwords": "^0.1.0", "which": "^1.0.5"}, "bin": {"notify": "./bin.js"}, "gitHead": "0dc7ea4dd5bcb9584e9dfda3b69e6f3bf2379968", "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "_id": "node-notifier@4.6.1", "_shasum": "056d14244f3dcc1ceadfe68af9cff0c5473a33f3", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "056d14244f3dcc1ceadfe68af9cff0c5473a33f3", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-4.6.1.tgz", "integrity": "sha512-UPTmeIGLTzZSizsf7EX8xr1iN/xXbULADNW3aOHnNf6mSUu2vtWcRTLTKHs/iNfV9Gbttp0iTQfICDNOeqlsLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDEWGkkP5HYcrOTsus1uRFcP8E7EtlDzNZhZPeRMvowQIgHSEwd2xQ/MbuxhTh8/X8c80XmMvhMzUPHwPRiA/x66c="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-notifier-4.6.1.tgz_1471811837812_0.47649690927937627"}}, "5.0.0": {"name": "node-notifier", "version": "5.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^3.13.1", "eslint-config-semistandard": "^7.0.0", "eslint-config-standard": "^6.2.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "jest": "^18.1.0"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.3.0", "shellwords": "^0.1.0", "which": "^1.2.12"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "14b47256a68ce88bd8a1884d3f0566434030d51f", "_id": "node-notifier@5.0.0", "_shasum": "bc753b5c93e4a99fd9765a024ef0bebf2bb6f18d", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "bc753b5c93e4a99fd9765a024ef0bebf2bb6f18d", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.0.0.tgz", "integrity": "sha512-EKnbRoJv1WpupFoLGn2h7b/JiDOXzy8w179vuVq8Tza6gugLFITJmo/igJLOKZgOsyDVC5FHSmerWb5/K00TgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDeJ9ymWMUBP7XexExfqq7WDuQXBwvN/5i9w9u73D6w2AIhANg6YXn9PI0WxJhkBTtVsQjtk2CK9Rj1pi0OKjxpTY9m"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-notifier-5.0.0.tgz_1485546219315_0.16755814896896482"}}, "5.0.1": {"name": "node-notifier", "version": "5.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^3.13.1", "eslint-config-semistandard": "^7.0.0", "eslint-config-standard": "^6.2.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "jest": "^18.1.0"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.3.0", "shellwords": "^0.1.0", "which": "^1.2.12"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "fc657977cd09af9f5fa4fa9e4958ecceee09b86e", "_id": "node-notifier@5.0.1", "_shasum": "710f46d17d42b4e4b761542acdfa42ab26006654", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "710f46d17d42b4e4b761542acdfa42ab26006654", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.0.1.tgz", "integrity": "sha512-gsvikwcbmZY52a9ZuVaF52tVwXp0AXHo7Ai/reBIG94cFALjkBaMAF4aLM36SzbhkqJ6qoyEhCbgCUQ61kh1Ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCw82KaPPr4kh6qiliHl9TdKQhbzdiap/psK6jJuoy55wIgGkvK96TYz++4MV5zAcZLd+c/mm1sFk2Y9icnpDq5aF0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-notifier-5.0.1.tgz_1485548505950_0.41455781692638993"}}, "5.0.2": {"name": "node-notifier", "version": "5.0.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^3.13.1", "eslint-config-semistandard": "^7.0.0", "eslint-config-standard": "^6.2.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "jest": "^18.1.0"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.3.0", "shellwords": "^0.1.0", "which": "^1.2.12"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "bacce44ee678f5feefd3577be977c9327d5f108c", "_id": "node-notifier@5.0.2", "_shasum": "4438449fe69e321f941cef943986b0797032701b", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "4438449fe69e321f941cef943986b0797032701b", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.0.2.tgz", "integrity": "sha512-nmc9wotRDRFGrV2zn6VmdkZjqr2dnzM23KRfMATDYt1XvlfjIqLf+THlXeO1+Wbltz04EeIO9FVVhBzQtVTZCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFIMN+vwqjDIrRmstk6Qx8W80a28vXCmliBUYkPEkFh3AiB89TATGI5sopM3WgETxnPaa8volscKu2GITlyl3i6S4g=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-notifier-5.0.2.tgz_1485625891219_0.8360252929851413"}}, "5.1.2": {"name": "node-notifier", "version": "5.1.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^3.13.1", "eslint-config-semistandard": "^7.0.0", "eslint-config-standard": "^6.2.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "jest": "^18.1.0"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.3.0", "shellwords": "^0.1.0", "which": "^1.2.12"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "0b75bc5f6099bd5aeaac3e6c34e3a7e75edaba7e", "_id": "node-notifier@5.1.2", "_shasum": "2fa9e12605fa10009d44549d6fcd8a63dde0e4ff", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "2fa9e12605fa10009d44549d6fcd8a63dde0e4ff", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.1.2.tgz", "integrity": "sha512-HJp4p6fq5IlsFsjQFfhaZWfbUMfDiaSDZ5Wr/9g9Huwh+KFk0FdW8sjBLjk1bReQc4KPr9xUd/KBcsluCY8L/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGIRgCuTOAzapkl+AoEr/H98U8bY48BLAG9XZfzStCn3AiEAn7Mi/YPhtYw7gRRxQuw65vhVdFYfO9FBFkFMyc1ICEo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-notifier-5.1.2.tgz_1489667065831_0.015753697836771607"}}, "5.2.0": {"name": "node-notifier", "version": "5.2.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^4.15.0", "eslint-config-semistandard": "^12.0.0", "eslint-config-standard": "^11.0.0-beta.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-node": "^5.2.1", "eslint-plugin-promise": "^3.6.0", "eslint-plugin-standard": "^3.0.1", "husky": "^0.14.3", "jest": "^22.0.6", "lint-staged": "^6.0.0", "prettier": "^1.10.2"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.4.1", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "c63e20caf1b2061983242660df1f7a7ce6740ccc", "_id": "node-notifier@5.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-rOX14ckDNN2mJuQc+/6mFBhWJv2MCxJ1Xbp+bluUvC0vSF6tbhod1HGa5Lv1i6bfUb7JmtU+BFxoOAqJHZhXMA==", "shasum": "05f047a664258a430ec2f348875ba5a07c623cfc", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDk9i2BTrScXTP3JDAqPRglKzQAlIhN6GSQcLRnPaRaKgIhALqPaYCM3spNEuhgS5DZ5X/O/BZeAvyckbGWafqwJoT+"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier-5.2.0.tgz_1515846051674_0.24599473737180233"}, "deprecated": "Avast virus scan on notifu"}, "5.2.1": {"name": "node-notifier", "version": "5.2.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^4.15.0", "eslint-config-semistandard": "^12.0.0", "eslint-config-standard": "^11.0.0-beta.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-node": "^5.2.1", "eslint-plugin-promise": "^3.6.0", "eslint-plugin-standard": "^3.0.1", "husky": "^0.14.3", "jest": "^22.0.6", "lint-staged": "^6.0.0", "prettier": "^1.10.2"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.4.1", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "9420a38fc329c7700eefd35594319bf3a96227a0", "_id": "node-notifier@5.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-MIBs+AAd6dJ2SklbbE8RUDRlIVhU8MaNLh1A9SUZDUHPiZkWLFde6UNwG41yQHZEToHgJMXqyVZ9UcS/ReOVTg==", "shasum": "fa313dd08f5517db0e2502e5758d664ac69f9dea", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVEc5wd64zGCUQ2nFEAuTXhyUaHI5QrDLkP7vMmoQTCwIhAIGZugR0TZwapziVQozqmB8G/HyX4LULNnwsWUkQT4+b"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier-5.2.1.tgz_1515866677762_0.4057284016162157"}}, "5.3.0": {"name": "node-notifier", "version": "5.3.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^5.0.1", "eslint-config-semistandard": "^12.0.1", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.11.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.1.0", "husky": "^0.14.3", "jest": "^23.2.0", "lint-staged": "^7.1.0", "prettier": "^1.12.1"}, "dependencies": {"growly": "^1.3.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "886839b7b88a57b939bc5f4a73a2bff2d59b85f2", "_id": "node-notifier@5.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-AhENzCSGZnZJgBARsUjnQ7DnZbzyP+HxlVXuD0xqAnvL8q+OqtSX7lGg9e8nHzwXkMMXNdVeqq4E2M3EUAqX6Q==", "shasum": "c77a4a7b84038733d5fb351aafd8a268bfe19a01", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.3.0.tgz", "fileCount": 23, "unpackedSize": 1358747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyh17CRA9TVsSAnZWagAACsgP/1VRkGqJ4wqgJgjWh4fT\nLm6ankOZHIeG7Ssdr/gaGyAsdHeniNdF3nGyjKP/5qhAeGheyOAM17m9f8ah\nvYFanFZZ76cJYt5FmA4OoQaobRSVsDXP5BZZbIi+XUtjXV7oZjL+cdF7GV/n\nfGOwqRDtMtqMGvb8QarVlFV0Fnm19ifn/3Ic+fpfeyHCytxV1bRp23aaPpr+\nvqk3MQrv+P76aaRPVBndchjz48HOz8mqmnBd2xul8iOupWA+fG0HhQ2tcsI9\nUl/nhe+ceyH7NMF1/QQvG9W2ytG1Vlpda+aBla9+C2Vwojm6qVnGZXtk+rU+\nbhinEhFyKkorj1Tucn+5Yc8WCc3LPwAx2CCjciBm1tn0S+9cZEG8M74Hva02\nVJTpWzKa999b23Bi2FGCBDrqujepG3KY5oTsvJr57XvEV7WUTfYVzPPWUIzc\nOJkQxkcRHuLh59HxZnMgQzL6O6HiuDMszV4iwJr8eujaaht2ccZyP7FboIYW\n9l0UtFL47zVduINpm/tcp75NvnezbcVARc5h26JfkuspyXNmhEe6NDSC0c7G\nv5sSGhUHRrv2yTQRQQawY+Oq83clkxRDWHyZvAXonLoOwNhcGKOSM80ks+Rz\ng9qUedIoDmnaKEJ4l5rKTVIsD5EWxorkv65Kl7R9v8Ztt8vsiJQ3e/CtQUeO\ntaw1\r\n=98jq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEzPYKG0JQYu9uCLUhNpYXPGP6JsXAnWOqzm/sJ6ZlBAIhAN3RM2WX+wXLajVj5ycXfOGKNvEwh4t2hvfqZZcVerDI"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.3.0_1539972474113_0.7134191706934541"}, "_hasShrinkwrap": false}, "5.4.0": {"name": "node-notifier", "version": "5.4.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^5.12.1", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.3.1", "jest": "^23.2.0", "lint-staged": "^8.1.0", "prettier": "^1.12.1"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "2275295e570e9edf33105d4f62e9eb3e7dbb1b69", "_id": "node-notifier@5.4.0", "_nodeVersion": "10.1.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-SUDEb+o71XR5lXSTyivXd9J7fCloE3SyP4lSgt3lU2oSANiox+SxlNRGPjDKrwU1YN3ix2KN/VGGCg0t01rttQ==", "shasum": "7b455fdce9f7de0c63538297354f3db468426e6a", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.0.tgz", "fileCount": 25, "unpackedSize": 1368519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVz47CRA9TVsSAnZWagAAqb0QAJe/BNX7aY4iPlNvMMfm\n5zoNSK9kMwZ9oXnqgXYgrmLMrMyleYIIXWxdHt7xMX1n3EZ7hvnUPnqT6beZ\nNJEJctr8cS9VHe+5VZnv3Bc528wz5EYapl7riQsR/E9jHhLlxnm3xNnMatxQ\n9ky+CJzAHdH9fydoSctzKIlDBejaH1kXd35k4d1IcCBbOzRnwK4+Ie6NvTpN\nLxZ0HoDuGqZmfjvRfFUGhhoDMmU9EbeGewpqakMZoD3AkI6CA2ByrUWK+ZMI\nuxobyqMBLpVRgQyoNC+vxBr/LHaTquC/PRRJsXJOPYooQyWCYM3/8p/8rClr\n/It8vbbot92AhPOOSLR4bb/QiD/e9K6pR8WRLbcm5Dcg7kxWNFbz8mN4iTSv\nVAVaoPHnVt7b2Qb33bNesWvhLGTN94a5YxlZKRFH7/qt1OVh3x+KM0B37I/9\npDKpCjkFi5dhQJj4J6mHrOO/vKXdW0A1KnXP2+hb2JW04HqpM5CUnckIllfR\nRiwYSJ3XNhly/u/CH24PHhcoIToMIVgM4X6BXrmGHIhNYYo03/D1Ov2AprdD\n7idkUzskQ5iMhq7/2rUGsChsxSTDekWIn1M6lKjDGDqabTkRHc0EaTt05W9W\nx5HQUNJpxkEl8n3/+k7ymGlbzVAiTHy/JXuMXbakuCJCMvP8pPJJJ5aFOixP\n8uaN\r\n=4Id7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDXLZiPFKxkuLqkoviuC9kZQQ1LBPnLeW8C+rYfXZ4fCAiA2uj0k5SpsM7OWNqU+w6kK6QJobObSgarePKuhbq/VhQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.4.0_1549221434880_0.5103087788563587"}, "_hasShrinkwrap": false}, "5.4.2": {"name": "node-notifier", "version": "5.4.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^6.1.0", "eslint-config-semistandard": "^14.0.0", "eslint-config-standard": "^13.0.1", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^9.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "husky": "^3.0.2", "jest": "^24.8.0", "lint-staged": "^9.2.1", "prettier": "^1.18.2"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.1.0", "semver": "^6.3.0", "shellwords": "^0.1.1", "which": "^1.3.1"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "d907d5451164e0d08b75d63a6d739d34cbbdb635", "_id": "node-notifier@5.4.2", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-85nkTziazE2dR4pyoLxMwz0b9MmxFQPVXYs/WlWI7CPtBkARJOV+89khdNjpbclXIJDECQYnTvh1xuZV3WHkCA==", "shasum": "a1111b8c1a4c3eb68d98815cc04a899456b03f1a", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.2.tgz", "fileCount": 25, "unpackedSize": 1368731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRzB6CRA9TVsSAnZWagAAocQP/i3GLfdcrVGUEvmMrbDH\nBaukynaNfB2yCNmlovFpKFjxrzwRqSXu5BL4qu1rMRhqtN+b7MeE+XM8GhN1\nlc9mD0OwHP4ipoDEoAFlXjQGyHqWeuhopbQkTm1qjSrdIIPohkAtTgwwI5xk\ni2VYq7yyHFhHdtNXowioqjkPpk7pGznW1IpxZUzjf46r9IxO/bVeZq8vlgB3\nd1RtorFt9Y4/mu2ogI7bZM1iFrIrNONZxcpXxKu8Ra7T/uZHIRY3Bk7yFwbq\nFop4htIHxW1LzXyB3vcadQOAKJG2aCEfR+hKzbku29s38UIpGBKQIoJ6dsgA\n8p03t7CcE5cCICtO/R4xM2DY58/Mbl0Wq7GRv4h/3rPbeRv3eA2A0+CQdZdZ\nFLd+hcoPDGuPcfHmNCeFf48ODWYKAj2VMhZFort2xM0Sw8LwWFUDM5/5zStG\nxFlX2jK6epXnmj7KO6G3IZKZErgdqsk8wcyYO+JrpjwQm22eOVmLFohwshzG\nQay71rBXUZ9dgPnPje2POu1JFg+Vf1JYdA0m9PQqK9nuurmAzMm1oyksSVNM\nTLi54qiIv04r3gVarFMubnZF0srdvWuup1tgbYH3kZ0H3VFlCBKKRRG9IzWD\naHl5ivFe1gyI/V5rJy96Vw9YagKAB5S8Cb9ef07lOh0Lt918UB8EpX2dGgam\nAhB5\r\n=OO3B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxPjXzg4NtKPneZB8SmcTFvzfXbTqyMaj389eQcSIP+QIhAMQzE091weTpbSVw569LNmk2c1pVDMgL8sw95BvC4ns7"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.4.2_1564946553517_0.42300885028761814"}, "_hasShrinkwrap": false, "deprecated": "Accidental breaking changes through depdencies"}, "5.4.1": {"name": "node-notifier", "version": "5.4.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^5.12.1", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.3.1", "jest": "^23.2.0", "lint-staged": "^8.1.0", "prettier": "^1.12.1"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "64decfb480a426565063fb7f15a45282e93f511e", "_id": "node-notifier@5.4.1", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-p52B+onAEHKW1OF9MGO/S7k/ahGEHfhP5/tvwYzog/5XLYOd8ZuD6vdNZdUuWMONRnKPneXV43v3s6Snx1wsCQ==", "shasum": "7c0192cc63aedb25cd99619174daa27902b10903", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.1.tgz", "fileCount": 25, "unpackedSize": 1368452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRzC4CRA9TVsSAnZWagAA7/0P+QDLFj7AaK4DRR2y6/SS\n14PIjSRHHG0oE6pJdDB9g9QHoabB2gK3ZK2FJckjScdwhWHoOx0T1pn6wiu/\nSDbDmCl6Np0GwH6sd1o5rl17P9xhUa5InYTfP8Irgw5agOlxJKo74D3KdMJr\nojxYM4ySDItIQbFOdmj5jVhgU4hcs8IWcXFY2d4vekDotfNdM4rE5xH9Yzpw\nEhgs7tk0JBldZnqcZS/enBO+Vongk4L94W+Fynk7WBZPxX5C9ow3bjaLrWkE\nVif6RVUlJmojl0z0xaKUFIKZdERZaGXcwpDVW0jW6Pq209KCSzy9kp5BNpk6\nA0DEOr3hqDQdrx8VjXRqUBituHH/ww3j5c9a+8327pTy3025yxdaPPEqfUtS\nUy4Rfj+X8MWLruS9Yr/HditgWGGYnPi8DZzGLzkJLGHUdBSKPDoILRwXfxcD\n6jC+s94p5j3+POa6OuC9GlotyAlC6e5ZGzUoeeeM2hTef6rZnmKmTR2zmRuz\nl2G6JBti8+w+PI5l95+nXcBiKM+9ehQl7JXErt/JW7oOFI+cJmXh7ikebZwS\nqr5CuLJlI2yODWeBzihTFKsUur53pvPGxs0zzSysyZWh4K1fW27Ll3J+j8yq\nB9RIWb0oAnbe1pWb/tttTdxStDWxcD0vAvaPdbWP+z70gXOrOhrREnMIxvbd\nqiLO\r\n=mXtS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFSXxnUNtgcpYqoKyO9GY0uXp9zzYQeMZhbRo4k7U4XgIgZJrebwuZlb/hsAIhKvxIrN6A7ZhMH9GQAXh6nhuz2sw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.4.1_1564946615586_0.928460531542455"}, "_hasShrinkwrap": false}, "5.4.3": {"name": "node-notifier", "version": "5.4.3", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^5.12.1", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.3.1", "jest": "^23.2.0", "lint-staged": "^8.1.0", "prettier": "^1.12.1"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "1101e6d7a9e79e32d7995bb05dbf941b3443d018", "_id": "node-notifier@5.4.3", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-M4UBGcs4jeOK9CjTsYwkvH6/MzuUmGCyTW+kCY7uO+1ZVr0+FHGdPdIf5CCLqAaxnRrWidyoQlNkMIIVwbKB8Q==", "shasum": "cb72daf94c93904098e28b9c590fd866e464bd50", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.3.tgz", "fileCount": 25, "unpackedSize": 1368774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWn05CRA9TVsSAnZWagAApkcP/iGdtOk2xdzvimfbrq11\npI+Ki8/S8FnI6CEw19pU7f8egozokNuIXFoEtlwnnqAzdv5QTVuNB6OuJunz\nax60XOvgYJoGfZeq58mXEtwUyx8ONVBx5ERfa5Ytcuhnr+XzEeWgOXY2XjJE\n/3nzH/Yf/kt5OdWpFDj4xDfQq2lfLttS4fHuoi2ahSO1Iys7aKGHgMSIC1N+\nCxA9IVCO1AxO6Zz6XfwtyH0xC+d4nuOrr5gXdsOllTRPkKKuhmEkouZAoSZE\n1kWoeYMcYq94it+0TB++BDEisCfBN1HJZXl/aRG3SgYQNS9hXY5YEFNkusR5\nZtHVgrLBpclSFtOG4pRR0h+Kxx2pbrjhV8dK5M2Oksn18uWL8//F3Iumxm4n\nQX/AvGxdnKYMnw+uxXTpcu5w7yCVedoihW7z8K26jjqoTg6s3qKjCWbbvLyq\nN1KZdhN8peVuUWL7s3aVGVVzlgH6TJdkaTvAv1TXf1SkOZCuwEuciOZrRZmn\nAoWqinmAqsIImm86vvSVe2TZuOig5rtU+uH6LyzOS3jE7vEut6o3WAK2xR4S\n2NQ+XGyaEnxYOAAhR/F9ldN+26prVRYhlYjmUPVDqLaIDDAUEJSgCGSQ/doC\nmB1G8veIEcibxm3Rol306+awBTPlvv7DHf+NuZzp2MPN5+I+2KjNSPHxh2bD\nZZXq\r\n=MN8M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFLehU3w2COL6ROv16ZWrmXz82KNsXWs5qj9xiOD7olAiBgQn2K49JE99EU4oNYozgAu405WqNFGs6UxDA0pW9L2w=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.4.3_1566211384232_0.15466906918232315"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "node-notifier", "version": "6.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^6.4.0", "eslint-config-semistandard": "^15.0.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^3.0.5", "jest": "^24.9.0", "lint-staged": "^9.3.0", "prettier": "^1.18.2"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.1.1", "semver": "^6.3.0", "shellwords": "^0.1.1", "which": "^1.3.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "20ae1a8b5b34c0e87cbfa1bd3262b56ea419e2c8", "_id": "node-notifier@6.0.0", "_nodeVersion": "12.7.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-SVfQ/wMw+DesunOm5cKqr6yDcvUTDl/yc97ybGHMrteNEY6oekXpNpS3lZwgLlwz0FLgHoiW28ZpmBHUDg37cw==", "shasum": "cea319e06baa16deec8ce5cd7f133c4a46b68e12", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-6.0.0.tgz", "fileCount": 26, "unpackedSize": 5643748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiMylCRA9TVsSAnZWagAAskYQAJAcJhB2fxrgVOJ3uJYf\nzQuT7fd2Fvy+7Opdvmkr2uDCkf9uufqAb87Bt9x/VNEIRIUz3WiHLVMNv35c\nRxQOoxWOAeWP2ApCNeGcceWnZKzNbHtqPDksY2vVMD9Fa8jm69C+AvA+en9T\nCyZNU1CrQk1AXa+aEJpJ6KckD0uiytrUR3BvG5n4CZbo8hkV3tZUwjetJwjj\nkWnrJpZyfmuGtSbnozOWxuF61Pqijs72lNLBgtqFJtne/cUf5JPPt8RNFgk7\nRmFW8tp+gMkfXQBKRLbazUcfCWhxxjfd9OKVRKUa+AVKOzFY9eMyJUGNxgjR\nyub8daGINXW8R5DWBHeDo4VuhxFlJn8yPDzgZ35cCprGA+fJYdxuaMtk3bxV\nhxzjOZs/VPK8/rT0uH1Y7ykFwYaCNz7KL42fwPRWwbVc9jODRv7PhTx70XBV\n4LDTdbke8ZUUQyjiNA8/VO94BSOkHtqjZNDsZfSZ85VWALw1gukFx8ZwhLq3\n90HHx0h2X3tkgUIl+rUzUWWLCV33Ve3LldOHLbwhofh4iu70eF9NMC4MHOUv\nlTByYYAeBIPWAIhmMh9oi8Vw+5Qh/eM4PYiC2GHaeKMZY96aaPVw8IuU3wbF\nyfnoQLu4xaxzhK3OP4S6yTQmjv1RJuf3Y6R5V+pYhopRy7bBLCGF8FrhKNlD\nbZpn\r\n=y2o+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC9SCSYcTWb0B7pKT7nQiOLSrvxAVn86pZHGcAWTdZswAiEAu8X5MeBTRTdc0PrF6LAYJjy79+ZVVo9udIgFRqRL6fo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_6.0.0_1569246372437_0.53744968520705"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "node-notifier", "version": "7.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^6.8.0", "eslint-config-semistandard": "^15.0.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.3", "jest": "^25.2.7", "lint-staged": "^10.1.2", "prettier": "^2.0.4"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.1.1", "semver": "^7.2.1", "shellwords": "^0.1.1", "uuid": "^7.0.3", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "d9ed9df800488fb9dcc9aa973f3bd5985062e1fb", "_id": "node-notifier@7.0.0", "_nodeVersion": "13.1.0", "_npmVersion": "6.14.1", "dist": {"integrity": "sha512-y8ThJESxsHcak81PGpzWwQKxzk+5YtP3IxR8AYdpXQ1IB6FmcVzFdZXrkPin49F/DKUCfeeiziB8ptY9npzGuA==", "shasum": "513bc42f2aa3a49fce1980a7ff375957c71f718a", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-7.0.0.tgz", "fileCount": 27, "unpackedSize": 5686036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc8iCRA9TVsSAnZWagAAYiIP/A2T8E0USbhCYi51+txV\nyHsCyQTFab8cQGYhtTG6V6fgwFntc4+FPmzWq8komukAhUaVim9cmFeQ/Zd6\nI9gkjMfHFtmpzFzpRwnsfZcPwR0qWtZzwRmg+hPYxRE64K8KLBrTcFLfsunc\nAdlnbl9h9X+lSShOTmYyYrcFFUqqGgs27W4/9a2ap8iNAq9OeA90htHgKC0p\nnZHLnzHJwWYdquF3wXapPoNlubIIAGxTVPEX322Nq854oDkxAJ1FDSVCsio3\nKX8ZhjOyxW5mrz+8lTkF4BZ5RgfFVcIPkiaIDB5HKxngPOXurIqTH3NDN/9R\nCmuHbpBvVfG2V/0vMDuhvSxQxErAK/Z25gE/k6If4is8Yap/WT0bglzbLJCa\nqZ+qLV9iaD+06fjGOzYdQr7hIaBhEuwL0A+bDmb/gxF0of53sKhh2wtTz44Y\nlzdasnVx4HoYGUVNXSCAkFJUO5BWgsNDF1ib8VwpnrRu8Rdx/Trmsk+EntXg\n/trmldYdhdmWzKOzC/HO3D6IXjGNhUStdFMb1nmd0UUyglUCxB0h8CG93U2h\nKd0VWL1xB8HbiVOTzNveBl3/E3uXNKrvY/ssmf/qbRXQ6Tdssf8oOV2H6YLy\nZF1Ccr11EgD/wul1L9YFE711l5w+ZSZHRIlspID+P0n7we89mwTlzvFBHGMy\ndnrs\r\n=JnYR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrNZdcm5sY0P1/g2tqdaMEgv6BnXkWfT/gbmHXfsHV1wIgNwSn6mRsuXqpq0yeU105U7CzDYr0ZKfVD5ebXeV52L0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_7.0.0_1586351905644_0.5789692950597123"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "node-notifier", "version": "7.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^6.8.0", "eslint-config-semistandard": "^15.0.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.3", "jest": "^25.2.7", "lint-staged": "^10.1.2", "prettier": "^2.0.4"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.1.1", "semver": "^7.2.1", "shellwords": "^0.1.1", "uuid": "^7.0.3", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "303f024973d74b4e3e4eae59921c5aff23d31863", "_id": "node-notifier@7.0.1", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-<PERSON><PERSON>zhierE7DBmQEElhTGJIoiZa1oqRijOtgOlsXg32KrJRXsPy0NXFBqWGW/wTswnJlDCs5viRYaqWguqzsKcmg==", "shasum": "a355e33e6bebacef9bf8562689aed0f4230ca6f9", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-7.0.1.tgz", "fileCount": 27, "unpackedSize": 5686041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeyDx/CRA9TVsSAnZWagAAPEoP/0R29KMQtAbGYe7ZN9c5\nHkAtm6BwFO6/ncbGJP4Eh/90gZT5UcysH/cpgf8tGczmRKD3H2vcPkt0jwW7\n6uQn9lUAy6wjZVp9dInjVom7yWryeKilioY+qeGcRiILJcZ3Ix94XJ0di9eH\n901cvAIvCUdGV6MaYNie00QwC7QrZ1XPH2udwwtcgz3bIsYlyuXpsJMN2EyC\nSXwF8w1ZTWSDGPmKegkD3PHS4xhkCRd8CO62csOKi0e7+2+9LDRB0S9aZxHN\nEON7AmvcBHP34am3Mt9JiTFcqku8SV5FWtPCqRYKvPIlCxNAgqrP2CIufpNd\n21jwm5AM1Fg1jW3xI3FXJuM2QHWXLfCJ4hIZVzF0xhYNm5gWQcR2CAkZgE5V\nSHahh2qXEk/NON246gsSImlM9bzc4iUzqBxsxHgE4RF1UraGxSFepNfVvvEC\n5UUsyH99hzOfZ964Or8Lwax5IX8+EpLRuOBJcJsTshcmprNVpBWOn00Qzrk4\n5IiiHBgjm8iIaZtDNq7fFN9airfpyXlywtEk/9toGMmPbhwn0TrV9hwSJrrl\n5StBrFNWwK59OaGj9V2E2Ds9WIoZVQPqFC7Wfit58IL3kUFTC9KkXQgGbEVV\nt6DZy9d1vVFPA8tVhtEqWe59aSefxtIuzfb5LJqTbIry8RB/aT0nMi/yXrUO\nHmjC\r\n=pjHp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIsp7HkGFVvQasvGEyOKgXQ8IWP6hHjzTqFLjMLhNGswIhAL2Is3BsGp1oioI7F3YdfwZpV0lsm482TzR1ymfITCbH"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_7.0.1_1590180990891_0.10166800159758149"}, "_hasShrinkwrap": false}, "7.0.2": {"name": "node-notifier", "version": "7.0.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.5.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.1.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.2.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "1c74ea953ee5a821db64fe1d498370ca3d6730b6", "_id": "node-notifier@7.0.2", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-ux+n4hPVETuTL8+daJXTOC6uKLgMsl1RYfFv7DKRzyvzBapqco0rZZ9g72ZN8VS6V+gvNYHYa/ofcCY8fkJWsA==", "shasum": "3a70b1b70aca5e919d0b1b022530697466d9c675", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-7.0.2.tgz", "fileCount": 27, "unpackedSize": 5686639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfF1AtCRA9TVsSAnZWagAAlqgP/0gM3nMeWqXMsNRr+d2x\nRAWwZ6C+PQuyZjtTXj3ojBql3y117+SzJjrRDl6n9A+9YtmjuNNQadMRCwJF\nhUsLcpjRG3feDWWPKG/9NmfaUyMSCUtuyiwUkvuDiF6q2Zt3mkfhWs6EBqXs\nNrV8BvhTm+7VpIHqQs3eomnYrjVVVy00oeQKYb4bhtkSzaeEqjLMLssB/KS1\ns1OD/IfaybPDakfgJegGb1EBpcLnkneX9MRNyZo/hV2JztkRFUEK/PFwiEf0\nfxvyLiF4x3q7hTEZCScGdcRS6+Z3pH2Dn2KmgubUTcntakgs50faAFyA3hwT\nlfpaY9MX2Hw8Sxr9MeGX/09m1IwL3F1XEHTwFOfTZ8zzLnYF06UaW3H11kdS\njoZ3CDitggk76FGycBbMlIEc/LJX80z3nN8VvLYlGtThNskzSi6OJGqjd4Qg\nPFQ8Hz8eaJLSMsSxGNNtFKqg11srYedvXAocxEj24G+n2GVx62kNE8HH8MN0\nIgeeQV0BtBxY+srsBrNhARVV/6EeXVqqmoYH9gcoMG/fS/DSoT0BgJsByUJX\npRuhBkBxLugNJALFpvGPHcer9v40rpn+JhouiJ+Sfc1v+WJv1p7UpBhi4ft4\n4iK8gRlNQKJCBzikQj+pqKjpLO+rIdiBX4AoZWrlftrNMtGcljNfWDRwWozO\nS0QA\r\n=NdG1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDURMtqGYVFgn1pqYirpNpjc+XuXVAjVu5Ro9uwQRk/ZAIgWBpcrNDe3A4MEZ09vrwMRDRtoE8TjENtHsy4qk027JM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_7.0.2_1595363372484_0.8964732367125601"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "node-notifier", "version": "8.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.6.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.4.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "c374fe103ce4650b5513c7782ed1464f87ef183b", "_id": "node-notifier@8.0.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-46z7DUmcjoYdaWyXouuFNNfUo6eFa94t23c53c+lG/9Cvauk4a98rAUp9672X5dxGdQmLpPzTxzu8f/OeEPaFA==", "shasum": "a7eee2d51da6d0f7ff5094bc7108c911240c1620", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-8.0.0.tgz", "fileCount": 27, "unpackedSize": 5687619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNQ/1CRA9TVsSAnZWagAAQV4P+gKcILEeS89dWycU11G4\nshBJYCoYmGXEnIOP7rAeYDt9VxBqaWcjoHYxMvPTd7uQCGzfnxEF2iMel0MZ\nqhOJsayimUgTqQsAZfOoZxLcIUTNS0Stp4plACvve/a8bO5J4YsVDJqJ9rI0\nNOrbidpYKEIPdCAasNNR0UaXF9gJDiRvPK24P04Ugte2y4Rz77z57FbWysIo\nH5eArF+7NNlx8Vslq14GXLGV9A2e7xrRewiW9I4e3FZ5Y6oDQB/af3uIXZnX\nhjElIBmkLr05tzdQindQffGeQtwQtpXi5f/cxyXnekQAgcpz5aHrJ8r2E8H7\n6+JZvpQmH0nMFm3AbOGO8uQOthq+Zq8e+noWHvQhRcd9Shl1GNB3FrAJg/TL\n63t27PMfmd0jOXRd9a+zSCWDxYPUYbpkRGHIxjYvt4AvdrRi7MfD0Cqialhk\nEDO3v3rNbSwN0vusy7TldRU7ITU05GDYtDQ3a+qaFY8RwM8ptdxxZ7zvd6Qp\nZEEI5D3Ed7hD8o1dfKF/UeUW9vMOFyHveS1F21G+bxXaEvwV07hEf/SX8KhY\nvWC7OF4sKhNENWIPooBO6C5LUXPFiUdPbn+7t1WY9hbM1L48075viMhVXTKi\nYKwk0ONC2euOKWr+2Tr75ErNTjiEal0z615imnA1sFzIVYhQ45ZE6XOutAQs\n++pN\r\n=Qefp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFZWWh2G26K7UCKXjl4cVxyFQl1fQqBH9smlcmdR2t5AiAds32ujyUNJUlfVguP/ZqovsLNxnItcg66gH/vrsza/g=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_8.0.0_1597313012216_0.9036631832863156"}, "_hasShrinkwrap": false}, "9.0.0": {"name": "node-notifier", "version": "9.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.6.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.4.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "6b42cb3512c87efb75f7b47eb1952ad5be62507a", "_id": "node-notifier@9.0.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-SkwNwGnMMlSPrcoeH4CSo9XyWe72acAHEJGDdPdB+CyBVHsIYaTQ4U/1wk3URsyzC75xZLg2vzU2YaALlqDF1Q==", "shasum": "46c5bbecbb796d4a803f646cea5bc91403f2ff38", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-9.0.0.tgz", "fileCount": 27, "unpackedSize": 5688371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0yJmCRA9TVsSAnZWagAAlYAP/2vOb3JCm5/yjVDxVtYf\nba8p7eAlEqT9JFWlmW9rVgPr1IzMV7guJRC2lpMhfNzIr3q2hRyRhQhlgjes\n8iH+xhadLWmk0+D+NbzoZnOyANIq4krgpV442cwcVeUgzqdrlVTKh+bEZo/O\neYR0XYT4SEIT4/YW3WUqN3EKWYsYhUz7QsWIu1g63NYefjw0+ACtgrnzDkNZ\nrBNWr7qJ4Zq5uv2hWKzG/wvGlgegBhoV5Y3T1w+1++qa4sO6Z4PHhZnAQcz5\njVbDiDziZ+8afK4/cr7zeIQ/60FxN0EXuW6UyIiS42YCT4VTKgYQza+l2y9w\n7WzhQoJ36RCXB0qWxzWFPEqrZ50ZuAGwlHHEnIlF7NzTviljOSUQb5XW77sC\njplYY5jmhat55Oo+32B0xLG3XcxdCxs9tgiklw9khr4Wre8AHaGn3ZwGIx+W\nevC5bUMNIMhENAC5NZfSIwf2m8xwy9Aw+9tbnVAbhAWZMjffMIpyeTDCsXb7\nU8+gy6CDAo7v2TmEAAx5B/dlsQyXqIc90pMGBUCm6QMpx7rf9EwGOlWUCjSF\n4Uoh4eb8rWFiuMqeH0Ude5xXmUmQskG4rJeS6GQ1whtXXwTv0OPgXNZOqnfx\n7s0fnS3vJ9MqZtKXWLwtdDb6xGinUecU0JqKkigNVOTVn79juFZeDod2ilMa\nkk/T\r\n=bB8/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2dK2bQ88yDZTw2L8y+vkFqaEQdTTj6vfLl1n1q2GZMAIgfMcxAIJ43SO57AphAQyOa67y65+iSsu97UVWXcYrpG4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_9.0.0_1607672421551_0.9067388161528154"}, "_hasShrinkwrap": false}, "8.0.1": {"name": "node-notifier", "version": "8.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.6.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.4.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "5d62799dab88505a709cd032653b2320c5813fce", "_id": "node-notifier@8.0.1", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-BvEXF+UmsnAfYfoapKM9nGxnP+Wn7P91YfXmrKnfcYCx6VBeoN5Ez5Ogck6I8Bi5k4RlpqRYaw75pAwzX9OphA==", "shasum": "f86e89bbc925f2b068784b31f382afdc6ca56be1", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-8.0.1.tgz", "fileCount": 27, "unpackedSize": 5687726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NdfCRA9TVsSAnZWagAABnsP/RogRvHB9jAoCYJh5zdT\nSVkz2WbnKXp8Q7XL6hoYFRhkNKyqdoD025lf7/5iV103Lwu2KyrDqHQJmKBH\ntjps5DATSWe5aZ7pSIM87nQdrxG+HOetBlvoUxplCs3AXK4avt+3q1hlOHVQ\naOPjR69ZtmVEzDah0qLTJwXhnBniBQFCr/bJW0+vkDOgqjH/Qc8yVE8c6K6S\nKRfNRN+uclR9DC7cRBVpZcLwa6k0qbFnKHh3LeJVtPupOL4HlI/Sf03t7COL\nMuxaLtSmmFEAebbPp5ry1E6NVSW5Ov5s+q+Msib7tR2TrAFC4RF35hGo6Erp\nmQN4NZJCRO8SvmyTiExOnCAy1S6ETjSUpY0FQUC3VC1wGlFvVZOiwM6KVran\n97EogFgZMfUPvYICkiZCJythLRWMfO6hSLThw5il5ldEw9A2CBAbq9EoU/Ta\nB/kDobdaZyWcvIeSatC7wWEepCYA98vkzmBNak44B+2+xW6QlEj5SjIkLVtO\nb+ysaOyOxUVlFzZQaSdNklAFPd4XXo6MErqH5hMqe4LdAJpuWdFstloREcUf\nM9oO7+cl4wDeyIxtVOZR7BTZvXiVikb6jxu7rrSGO9x+Q7p5wliKelhtT5MQ\nLpdC7jj6tZpzKzq23UXQbBjD9CxKi1EJtdQHq9r3fX4wwL/XSCPbDrgSE7hH\niCwR\r\n=euQT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVBO/BcxSjtE8xKPgaulJqXb+2Hpvj2Xsm6ITjf+JUNAiEA3QI53IwjCA/1rxFa3AZXt0wkujRAX49kV1vefsh8W0w="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_8.0.1_1608046430751_0.5503495917334669"}, "_hasShrinkwrap": false}, "5.4.4": {"name": "node-notifier", "version": "5.4.4", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^5.12.1", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.3.1", "jest": "^23.2.0", "lint-staged": "^8.1.0", "prettier": "^1.12.1"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "93fa026389ce7f11546de09855f2525a67676a45", "_id": "node-notifier@5.4.4", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"integrity": "sha512-9n4gOCcrm/YnnUPZdIGMmrEdT00pONdLRwKhVfrGjsMBnpcelSj5kGTH+vyqtuZHixRYMa6Ie6eP+/bCc7K/Aw==", "shasum": "923f382a171061e63a46713ea07397f86ccaa45f", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.4.tgz", "fileCount": 25, "unpackedSize": 1368817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSmiLCRA9TVsSAnZWagAA8loP/0YLQAK0eWrYjqJZ6VTn\n6atfcgTx7WbGkY/8shBJ8kxMPs6V6y6f7CeN7rKl8MfkjeoeQFGB42wswKkO\nTNuyEPIaZokpaxdrqln6xEkUY5fcbAIWmMn4/97tMCx5lGJ1gYVvtBA5FxL+\n/gpvWvveXtwmMenZ00UZDURoTE1ZqamfbbWqQ+FCtBMl0zTOQH+iFucsKBRy\niD4ve09RDg+Ls2z80Q4ta3EODTadefPH3tIzwG7WdviRK98t3Z8UykslU6kk\n3L+/Eo5Gc3A+2zCZ90ueF+nNKiTMg4P4s5e7MvWJiMiUJ4Dnq/EN/RYl7zIt\nFg4xkasAe9SIUyUkl0CKVqaaMk7aah6ndp+3CU61Hqcq/ToYrh340QuhH0Wq\nQ3ISWHZFgwb8n4Dn+CzJYGK3IFcMOsMGw1ekUh2zatRgRoRVrJhuUYuHSMsd\no8LdujOiSAocwN+7ex2xySJbpd66Yx70oLdXxhC4AUVPM/mJXdQ/4VWyIo60\nxtKFJrI7Pz4lQDX1crq+wIo7DQTDXYLmMRDg7zjEu/VhuRDVuy6PwhW9kVVT\nieUgs2KXX4mXrNAtCdUeyDrFMmpre46Z53gRhZY4G2T4M7F37g0MLGO79b9k\n7WuP0Btu6WOrHeMpaXZstOG+Znqas760iEa5ehYnbdJkyWhxPOWHljnIm0rD\nO2qC\r\n=htG9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBiQpX8P8X8PaDdEheJAujoibZsWi6I6RA2L9zSYoqZYAiBTDpsFoUPkO2mQccUdcQtIIVdtuGy5jX5alG2/cTzO5g=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.4.4_1615489163086_0.8140828456306641"}, "_hasShrinkwrap": false}, "9.0.1": {"name": "node-notifier", "version": "9.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.6.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.4.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "b977dd54160152b504f5cef29f81753838da39f5", "_id": "node-notifier@9.0.1", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"integrity": "sha512-fPNFIp2hF/Dq7qLDzSg4vZ0J4e9v60gJR+Qx7RbjbWqzPDdEqeVpEx5CFeDAELIl+A/woaaNn1fQ5nEVerMxJg==", "shasum": "cea837f4c5e733936c7b9005e6545cea825d1af4", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-9.0.1.tgz", "fileCount": 27, "unpackedSize": 5688913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSnFOCRA9TVsSAnZWagAAh4kP/1NR/SHTbVuf9Bb0tV66\n460+wAL7+wg5a4uWp6ot8zuZI5i8Ym/KGCD5UNhz8U1Nc0fgBKt5Je9Q0FrV\n9WWry66vuIdBnlQnhe7ORujdUo4aU17laGi6YG0+m+LaXJggortMeNKUd668\niIFtF1KPCaM8m2u+IeNdnrI7g4waDatcsSK3sbJQFE1/HCSuJNY489+t4AXu\nXx6ejDtc71dlOeCWkFpQruqEBf7VX92R86BTcYEtK90YBPvz3u3bF8sa5GuZ\nL3xPYoz/Hkse5eax8k21YPtZF94gq3G/C7gkiqTKwV4P69RN9N4UUz7QWYNW\n6684cNgdSiM3G+S/6D8Z0Di/yFFM1oqaSlRLAdrkztZHQXKk5cRM/9BaOIc1\n4hWhKepJhFzwvNYC98D6bjQOc4hYBLL1WEipMq/4YA7piKejCSC1wJwCPhEN\naqWYJkqvLpuq/uPJO4AC+CTI66QgGSgr0GuB1sRatvf67i1SSYKXG4huI6c3\nH4O/aYftUQjESRvB6ABduIsdzUzSLC950b6oDGnGKGYl80j5qkMOxbM6+R1s\nD9gBelx6TIm7EcSxaAjZbXF4SXZv0lMavDMuUeAz5f2tRvPY2K3KYPe8HXGm\nZLUGCf9BrxREHv/j6wF3snuz0ilTrMXZOKRNCHuV7nocfC392tUFESZl9Ks0\nDzqt\r\n=4Tu7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpIrn4WXeL8ySvJRD0Z2MFSbwCQVThOYxz8PT3Ovhr1wIhAOYBYFsAS3Fzl8G06A1Fw1y3WidB43WKpIjMEcD4VyBJ"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_9.0.1_1615491405916_0.01399726108406818"}, "_hasShrinkwrap": false}, "8.0.2": {"name": "node-notifier", "version": "8.0.2", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.6.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.4.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "34209773b84f599a7376aaaf3e85316967746527", "_id": "node-notifier@8.0.2", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"integrity": "sha512-oJP/9NAdd9+x2Q+rfphB2RJCHjod70RcRLjosiPMMu5gjIfwVnOUGq2nbTjTUbmy0DJ/tFIVT30+Qe3nzl4TJg==", "shasum": "f3167a38ef0d2c8a866a83e318c1ba0efeb702c5", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-8.0.2.tgz", "fileCount": 27, "unpackedSize": 5687705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSnIuCRA9TVsSAnZWagAAWp4P/ijmkzTt9i4q2EDkHCLP\nqZCD81sDeKwz6TW+YwE/1wgiDcep8jCTa7jVwDaXdcx/hRYlFiT37mfYx0yy\np+FvtjG1BvSz314DiXWnYJcC2XmGFDM4g47l1DdikW9Sql3ZV9nClTI8Y4d0\nVoFWKNMrhHbEJojcw3MwUOz1L7Vt0CVPe11FT+LezlqQWr1ox6zEpZdr+1gd\nH4rEbq9J8wCKxSAOqLIhjzDUaOISLOp7EjkiJLbzLcnUbDM6XSG4xi/e1ote\nASrmLqv9PYPL9/6pAW2lPgS2Op49om4yq0YuLQksWBZfJ6K+JxX2BKl/0ytT\nqj0xCzHojOESTMgu3uxJ/yxsTkltzq8AdyluQHpS7sv6HTX+H372pqsD43s8\nvy6QgjUX95dIFhyKFG9V8sSc1neeZ0Lph4FZQFonaWZwNdrWIyc4guNhD29V\nxLHh2KijUMExjGLBDF2Tua7emIVRnJOTQBp4q13wl31Iif2Q1UOgsGKFHv62\nU4mG8gjng+9sqmhKbQfcamIbJfyo91xBQpUtDOLlY3UyLeHM/RxgFrKsposx\n23qKZCsXtOIFIesRSPUVWTfkvIsABIruURe0WfliGKXBl3Bq07ciDP3H4Nkn\n+Pod1lO1VST9c3k808qWDibgBA/5136+myc5GUmFg0q/GU4pVi1e4QfIxF78\n23eN\r\n=yxhD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZ0jHIHU3KxFKmqo9tuoIIrwlr2hC5Idam67DTwigGUAiEAqxLQnbdPt3WRiMSLmfT+5WDKZJ6/wXNJL5sv8wGVF08="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_8.0.2_1615491629487_0.5734938584562477"}, "_hasShrinkwrap": false}, "5.4.5": {"name": "node-notifier", "version": "5.4.5", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"precommit": "lint-staged", "pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^5.12.1", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.3.1", "jest": "^23.2.0", "lint-staged": "^8.1.0", "prettier": "^1.12.1"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "ae03eed4e9a52adb23882faefa1c59851cafac7b", "_id": "node-notifier@5.4.5", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"integrity": "sha512-tVbHs7DyTLtzOiN78izLA85zRqB9NvEXkAf014Vx3jtSvn/xBl6bR8ZYifj+dFcFrKI21huSQgJZ6ZtL3B4HfQ==", "shasum": "0cbc1a2b0f658493b4025775a13ad938e96091ef", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.5.tgz", "fileCount": 25, "unpackedSize": 1368797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSnbeCRA9TVsSAnZWagAA8SAP/iDqB1y3ROUOuXntF4cm\n7DDKZl0Cn2oqGYP2cDMhyL/zxWCTOdfCl4EdJ2SJdUW89X5ZX3X/9BHfZnX2\nRqtwrrOaRoODIrxAIP0do8OOHN3/J3Vy3+CBAfRzJ2DcQOvmxSzOuK46H7EQ\nfN8Ufw6PriXnJ4jZ9MTmr9xUQyBkBB0o/yYzPlj8jg8gRzO6NtfQ39oSVQs2\n3DCvDuiawUCUyzPa9CNe7dJG1dxyn/RJX8lcTSiMRUq8UpjG+doNlWt8CpLM\nof2rYlO5HRWmH3BLCVTLIWR9B9cuZCXYfFsWegfBHNVYNKdIcTvtkToLv67K\nWwKh3M0GkOmxO2PQwvd/uW8/j81o7TL/XYMBARs/KmN2U7JTCnei56zChvi/\nf7DZDLFAlJj2HSxTfAyCOtvlnl6/w1jf211UNdKm9EUHuQDaycAOoe7mhyVH\nrrJ1z/XjE2TVPpb5cwnBVNw2r58a37JGjyJdFsZbJa4R+dhuXZcYeBwXXh9o\nmxztkx6Sfq1A777tjZlzL+3yNeaG0OwjkSNZg4uqVcvjQMZHvogyNLbZcAiO\nrcldANXf0tx7A0+x7i3tbZlU5lQjNLadeGPDwPKBch9UQ20PrFx73QjI5Hd+\naJH0019Z5AdNLBn7ZrNnAEt+dHxHWubhdin4M3nOOx5sjZe9fBnogXtXKaeC\nKHmU\r\n=Rr+i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2YNBL2Y7W+rr8aEYFxVjqOIwkFL62BNO65PGIcHo/vgIgePT8g0sI0ZWXbEpTFqFHdmkIUiO5NHSgw0V4o1y5I/o="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_5.4.5_1615492829965_0.33105185550644456"}, "_hasShrinkwrap": false}, "10.0.0": {"name": "node-notifier", "version": "10.0.0", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"eslint": "^7.26.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "husky": "^6.0.0", "jest": "^26.6.3", "lint-staged": "^11.0.0", "prettier": "^2.3.0"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.5", "shellwords": "^0.1.1", "uuid": "^8.3.2", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "2c237b162c6d778013920f74c336e74e48cee790", "_id": "node-notifier@10.0.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.6.0", "dist": {"integrity": "sha512-ZTqP90y1eyb2xAZTa7j4AlAayTwh6cL8mn0nlJhLDq8itXGnJUmQGYOnpaMUvqZVfGo0vhU7KZ3HtDW6CT2SiQ==", "shasum": "93c154055b07b550a33a1966a1b11291c2113e69", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-10.0.0.tgz", "fileCount": 27, "unpackedSize": 5689460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrS22CRA9TVsSAnZWagAAA80P/1sA8QqiNm6MlzNac1WD\nMjdyPVtLM1bw+quA/vB+Sjm62p1LTbNA+uQqm1tYoVYUOBExQ0PRE6eZXhsL\nXUVltrVGdCT1QleduaNUYjwUsqYZx13ogmsMsnAsSakBgO0RqUAMP4wjXM0d\nvCel379L6nL15cfZInP5M9tmKxaASRe8EYsk48Oswzbpb6uS8tEqnkZe+hZH\nAgoAO4cI+jOoBWUmPhP7dd+qs3LlQwLTvtHqEyxlBzLnhTvJTPKsraAvwDgx\nQ/Qcx8GkM8izQ+PoUtG3Xq07M0tbezgqoXO3uncxwmhU6wpy1kluThfFXjv1\nfGi+9ReXRLlcTmNXqPpNjkgYkRgWGkmfv6kMd6voDmXx6qrO0V67hAngn4+f\nBjwD43gxiqLlKqbOIZM1H4MFWPHkgPuHxZI4qcl3bBgy3mU1Q+BoMpjCE9LL\n4F7YQZ2La+p6lZwdDbAaM9Js0EYQOuQQaAg3iXgwNfBnakQmntkPHI3sKMr2\n7h53+UyfhYbTZ4/nyRf/2xW2Br4WvkoDvyNEVkmt00Xx8QgfpKG6nHClWscy\nRblTHT73yOvFhcTrsIpHqRYyItaH7N6PpitF/IkkldzTMszyXxAhsJfq2PX3\nntKzejpu9iuUdXmR8x3t12aEedrUcS2Dv9v2pJ7DswgiZWF3ZAs1KgcN2HUD\nTlK9\r\n=rFmb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSb7kWBv1PM7UiGghOck+9G3u5nQTN4pidRafNcz6FcwIhAI1LGCh33n8g7tqM+Pu1eZQ78Tk7wF+qOT4dHJpVXXKp"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_10.0.0_1621962165688_0.4531767380321905"}, "_hasShrinkwrap": false}, "10.0.1": {"name": "node-notifier", "version": "10.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "example:windows:custom-path": "cross-env DEBUG=notifier node ./example/toaster-custom-path.js", "copy-resources": "copyfiles -u 2 ./vendor/snoreToast/snoretoast-x64.exe ./dist/example/resources/ && copyfiles -u 1 ./example/coulson.jpg ./dist/example/resources/", "preexample:windows:nexe-custom-path": "<PERSON><PERSON><PERSON> dist", "example:windows:nexe-custom-path": "nexe -t windows-x64-14.15.3 -i ./example/toaster-custom-path.js -o ./dist/toaster-custom-path.exe && npm run copy-resources", "postexample:windows:nexe-custom-path": "cross-env DEBUG=notifier ./dist/toaster-custom-path.exe", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^7.26.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "husky": "^6.0.0", "jest": "^26.6.3", "lint-staged": "^11.0.0", "nexe": "^4.0.0-beta.19", "prettier": "^2.3.0", "rimraf": "^3.0.2"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.5", "shellwords": "^0.1.1", "uuid": "^8.3.2", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}, "gitHead": "ce6873d3fdfbbc8b090ed5e2f8aec5c1b5865584", "_id": "node-notifier@10.0.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-YX7TSyDukOZ0g+gmzjB6abKu+hTGvO8+8+gIFDsRCU2t8fLV/P2unmt+LGFaIa4y64aX98Qksa97rgz4vMNeLQ==", "shasum": "0e82014a15a8456c4cfcdb25858750399ae5f1c7", "tarball": "https://registry.npmjs.org/node-notifier/-/node-notifier-10.0.1.tgz", "fileCount": 27, "unpackedSize": 5677839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+ZquCRA9TVsSAnZWagAAS64P/RsJb7hLEBvGnubhmYCT\n94o5jvgVSUrKhasf7MzAteR3WYZ3R+pUOZbKM7GdXmpM8hW38QC8HjCm7N1r\nILlPvlBrWvJWvFbNtdoRTV3zFPVTslfs3SSC9UN/cPCWw346s6+8HSg45Lq3\nFUYj+BC7jllDr55E4PbmbK5TrJ+MVK6EmPvpubkkNd3dfjyGCYykO06g5szc\nG/IvGmXvaRIcsflPM+MYoMtPWgM6+W4XLIz6qP/1gNub5rXlMqMwhNNH62bM\nax3LPzHoQfTOBv9MLupZXjDcTqImmqTQYTxXJtpekJMSD9m6yW26rHFGrnso\nadq/VnOW9SVSRzS+P+ckUHKAQ1A2GWhvvg2FuV8hH3tXhn7OZfGdP5+ckZhb\nDFSQX1BKpxq6O4+xqOSvXcLl41UrciTTCIEi4aqqrHWc7xQXz5wYmSOZragy\nHMlHflN2H24uQR2f0AOhe2KYHHXpP/OrqSyESsWzrLM1xHI3q87dQcRXx5hi\nNMqcwTNGtjlh3mX8O3AjouK7FDy+NkmEYeV2kyXFI2HhhA3EgLH1pV6IIgZ7\nfD0Ppj7SB2Re8tQH3Xzm3obi+Kh6SKeCvbdlbt/oJj+18j7bb30d51unljVW\nJMt3ewOFSxZs1NZGFV01KrHIfuDqLOrZWv4zOunpivoNvZcYQYRo3Q6hjhi9\n7lLP\r\n=yMKr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsGCAwPPkLH1w9VfUvL2Ny+RdP4RiLgBzBhG77MH/PfAiBJ1NeY0VfPIOOWw7gO+jdIwdYUJsk897sONS2WM2IfkQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-notifier_10.0.1_1643748014354_0.6460183425546322"}, "_hasShrinkwrap": false}}, "readme": "# node-notifier [![NPM version][npm-image]][npm-url] [![Install size][size-image]][size-url] [![Build Status][travis-image]][travis-url]\n\nSend cross platform native notifications using Node.js. Notification Center for macOS,\n`notify-osd`/`libnotify-bin` for Linux, Toasters for Windows 8/10, or taskbar balloons for\nearlier Windows versions. Growl is used if none of these requirements are met.\n[Works well with Electron](#within-electron-packaging).\n\n![macOS Screenshot](https://raw.githubusercontent.com/mikaelbr/node-notifier/master/example/mac.png)\n![Native Windows Screenshot](https://raw.githubusercontent.com/mikaelbr/node-notifier/master/example/windows.png)\n\n## Input Example macOS Notification Center\n\n![Input Example](https://raw.githubusercontent.com/mikaelbr/node-notifier/master/example/input-example.gif)\n\n## Actions Example Windows SnoreToast\n\n![Actions Example](https://raw.githubusercontent.com/mikaelbr/node-notifier/master/example/windows-actions-example.gif)\n\n## Quick Usage\n\nShow a native notification on macOS, Windows, Linux:\n\n```javascript\nconst notifier = require('node-notifier');\n// String\nnotifier.notify('Message');\n\n// Object\nnotifier.notify({\n  title: 'My notification',\n  message: 'Hello, there!'\n});\n```\n\n## Requirements\n\n- **macOS**: >= 10.8 for native notifications, or Growl if earlier.\n- **Linux**: `notify-osd` or `libnotify-bin` installed (Ubuntu should have this by default)\n- **Windows**: >= 8, or task bar balloons for Windows < 8. Growl as fallback. Growl takes precedence over Windows balloons.\n- **General Fallback**: Growl\n\nSee [documentation and flow chart for reporter choice](./DECISION_FLOW.md).\n\n## Install\n\n```shell\nnpm install --save node-notifier\n```\n\n## <abbr title=\"Command Line Interface\">CLI</abbr>\n\n<abbr title=\"Command Line Interface\">CLI</abbr> has moved to separate project:\n<https://github.com/mikaelbr/node-notifier-cli>\n\n## Cross-Platform Advanced Usage\n\nStandard usage, with cross-platform fallbacks as defined in the\n[reporter flow chart](./DECISION_FLOW.md). All of the options\nbelow will work in some way or another on most platforms.\n\n```javascript\nconst notifier = require('node-notifier');\nconst path = require('path');\n\nnotifier.notify(\n  {\n    title: 'My awesome title',\n    message: 'Hello from node, Mr. User!',\n    icon: path.join(__dirname, 'coulson.jpg'), // Absolute path (doesn't work on balloons)\n    sound: true, // Only Notification Center or Windows Toasters\n    wait: true // Wait with callback, until user action is taken against notification, does not apply to Windows Toasters as they always wait or notify-send as it does not support the wait option\n  },\n  function (err, response, metadata) {\n    // Response is response from notification\n    // Metadata contains activationType, activationAt, deliveredAt\n  }\n);\n\nnotifier.on('click', function (notifierObject, options, event) {\n  // Triggers if `wait: true` and user clicks notification\n});\n\nnotifier.on('timeout', function (notifierObject, options) {\n  // Triggers if `wait: true` and notification closes\n});\n```\n\nIf you want super fine-grained control, you can customize each reporter individually,\nallowing you to tune specific options for different systems.\n\nSee below for documentation on each reporter.\n\n**Example:**\n\n```javascript\nconst NotificationCenter = require('node-notifier/notifiers/notificationcenter');\nnew NotificationCenter(options).notify();\n\nconst NotifySend = require('node-notifier/notifiers/notifysend');\nnew NotifySend(options).notify();\n\nconst WindowsToaster = require('node-notifier/notifiers/toaster');\nnew WindowsToaster(options).notify();\n\nconst Growl = require('node-notifier/notifiers/growl');\nnew Growl(options).notify();\n\nconst WindowsBalloon = require('node-notifier/notifiers/balloon');\nnew WindowsBalloon(options).notify();\n```\n\nOr, if you are using several reporters (or you're lazy):\n\n```javascript\n// NOTE: Technically, this takes longer to require\nconst nn = require('node-notifier');\n\nnew nn.NotificationCenter(options).notify();\nnew nn.NotifySend(options).notify();\nnew nn.WindowsToaster(options).notify(options);\nnew nn.WindowsBalloon(options).notify(options);\nnew nn.Growl(options).notify(options);\n```\n\n## Contents\n\n- [Notification Center documentation](#usage-notificationcenter)\n- [Windows Toaster documentation](#usage-windowstoaster)\n- [Windows Balloon documentation](#usage-windowsballoon)\n- [Growl documentation](#usage-growl)\n- [Notify-send documentation](#usage-notifysend)\n\n### Usage: `NotificationCenter`\n\nSame usage and parameter setup as [**`terminal-notifier`**](https://github.com/julienXX/terminal-notifier).\n\nNative Notification Center requires macOS version 10.8 or higher. If you have\nan earlier version, Growl will be the fallback. If Growl isn't installed, an\nerror will be returned in the callback.\n\n#### Example\n\nBecause `node-notifier` wraps around [**`terminal-notifier`**](https://github.com/julienXX/terminal-notifier),\nyou can do anything `terminal-notifier` can, just by passing properties to the `notify`\nmethod.\n\nFor example:\n\n- if `terminal-notifier` says `-message`, you can do `{message: 'Foo'}`\n- if `terminal-notifier` says `-list ALL`, you can do `{list: 'ALL'}`.\n\nNotification is the primary focus of this module, so listing and activating do work,\nbut they aren't documented.\n\n### All notification options with their defaults:\n\n```javascript\nconst NotificationCenter = require('node-notifier').NotificationCenter;\n\nvar notifier = new NotificationCenter({\n  withFallback: false, // Use Growl Fallback if <= 10.8\n  customPath: undefined // Relative/Absolute path to binary if you want to use your own fork of terminal-notifier\n});\n\nnotifier.notify(\n  {\n    title: undefined,\n    subtitle: undefined,\n    message: undefined,\n    sound: false, // Case Sensitive string for location of sound file, or use one of macOS' native sounds (see below)\n    icon: 'Terminal Icon', // Absolute Path to Triggering Icon\n    contentImage: undefined, // Absolute Path to Attached Image (Content Image)\n    open: undefined, // URL to open on Click\n    wait: false, // Wait for User Action against Notification or times out. Same as timeout = 5 seconds\n\n    // New in latest version. See `example/macInput.js` for usage\n    timeout: 5, // Takes precedence over wait if both are defined.\n    closeLabel: undefined, // String. Label for cancel button\n    actions: undefined, // String | Array<String>. Action label or list of labels in case of dropdown\n    dropdownLabel: undefined, // String. Label to be used if multiple actions\n    reply: false // Boolean. If notification should take input. Value passed as third argument in callback and event emitter.\n  },\n  function (error, response, metadata) {\n    console.log(response, metadata);\n  }\n);\n```\n\n---\n\n**Note:** The `wait` option is shorthand for `timeout: 5`. This just sets a timeout\nfor 5 seconds. It does _not_ make the notification sticky!\n\nAs of Version 6.0 there is a default `timeout` set of `10` to ensure that the application closes properly. In order to remove the `timeout` and have an instantly closing notification (does not support actions), set `timeout` to `false`. If you are using `action` it is recommended to set `timeout` to a high value to ensure the user has time to respond.\n\n_Exception:_ If `reply` is defined, it's recommended to set `timeout` to a either\nhigh value, or to nothing at all.\n\n---\n\n**For macOS notifications: `icon`, `contentImage`, and all forms of `reply`/`actions` require macOS 10.9.**\n\nSound can be one of these: `Basso`, `Blow`, `Bottle`, `Frog`, `Funk`, `Glass`,\n`Hero`, `Morse`, `Ping`, `Pop`, `Purr`, `Sosumi`, `Submarine`, `Tink`.\n\nIf `sound` is simply `true`, `Bottle` is used.\n\n---\n\n**See Also:**\n\n- [Example: specific Notification Centers](./example/advanced.js)\n- [Example: input](./example/macInput.js).\n\n---\n\n**Custom Path clarification**\n\n`customPath` takes a value of a relative or absolute path to the binary of your\nfork/custom version of **`terminal-notifier`**.\n\n**Example:** `./vendor/mac.noindex/terminal-notifier.app/Contents/MacOS/terminal-notifier`\n\n**Spotlight clarification**\n\n`terminal-notifier.app` resides in a `mac.noindex` folder to prevent Spotlight from indexing the app.\n\n### Usage: `WindowsToaster`\n\n**Note:** There are some limitations for images in native Windows 8 notifications:\n\n- The image must be a PNG image\n- The image must be smaller than 1024×1024 px\n- The image must be less than 200kb\n- The image must be specified using an absolute path\n\nThese limitations are due to the Toast notification system. A good tip is to use\nsomething like `path.join` or `path.delimiter` to keep your paths cross-platform.\n\nFrom [mikaelbr/gulp-notify#90 (comment)](https://github.com/mikaelbr/gulp-notify/issues/90#issuecomment-129333034)\n\n> You can make it work by going to System > Notifications & Actions. The 'toast'\n> app needs to have Banners enabled. (You can activate banners by clicking on the\n> 'toast' app and setting the 'Show notification banners' to On)\n\n---\n\n**Windows 10 Fall Creators Update (Version 1709) Note:**\n\n[**Snoretoast**](https://github.com/KDE/snoretoast) is used to get native Windows Toasts!\n\nThe default behaviour is to have the underlying toaster applicaton as `appID`.\nThis works as expected, but shows `SnoreToast` as text in the notification.\n\nWith the Fall Creators Update, Notifications on Windows 10 will only work as\nexpected if a valid `appID` is specified. Your `appID` must be exactly the same\nvalue that was registered during the installation of your app.\n\nYou can find the ID of your App by searching the registry for the `appID` you\nspecified at installation of your app. For example: If you use the squirrel\nframework, your `appID` will be something like `com.squirrel.your.app`.\n\n```javascript\nconst WindowsToaster = require('node-notifier').WindowsToaster;\n\nvar notifier = new WindowsToaster({\n  withFallback: false, // Fallback to Growl or Balloons?\n  customPath: undefined // Relative/Absolute path if you want to use your fork of SnoreToast.exe\n});\n\nnotifier.notify(\n  {\n    title: undefined, // String. Required\n    message: undefined, // String. Required if remove is not defined\n    icon: undefined, // String. Absolute path to Icon\n    sound: false, // Bool | String (as defined by http://msdn.microsoft.com/en-us/library/windows/apps/hh761492.aspx)\n    id: undefined, // Number. ID to use for closing notification.\n    appID: undefined, // String. App.ID and app Name. Defaults to no value, causing SnoreToast text to be visible.\n    remove: undefined, // Number. Refer to previously created notification to close.\n    install: undefined // String (path, application, app id).  Creates a shortcut <path> in the start menu which point to the executable <application>, appID used for the notifications.\n  },\n  function (error, response) {\n    console.log(response);\n  }\n);\n```\n\n### Usage: `Growl`\n\n```javascript\nconst Growl = require('node-notifier').Growl;\n\nvar notifier = new Growl({\n  name: 'Growl Name Used', // Defaults as 'Node'\n  host: 'localhost',\n  port: 23053\n});\n\nnotifier.notify({\n  title: 'Foo',\n  message: 'Hello World',\n  icon: fs.readFileSync(__dirname + '/coulson.jpg'),\n  wait: false, // Wait for User Action against Notification\n\n  // and other growl options like sticky etc.\n  sticky: false,\n  label: undefined,\n  priority: undefined\n});\n```\n\nSee more information about using [growly](https://github.com/theabraham/growly/).\n\n### Usage: `WindowsBalloon`\n\nFor earlier versions of Windows, taskbar balloons are used (unless\nfallback is activated and Growl is running). The balloons notifier uses a great\nproject called [**`notifu`**](http://www.paralint.com/projects/notifu/).\n\n```javascript\nconst WindowsBalloon = require('node-notifier').WindowsBalloon;\n\nvar notifier = new WindowsBalloon({\n  withFallback: false, // Try Windows Toast and Growl first?\n  customPath: undefined // Relative/Absolute path if you want to use your fork of notifu\n});\n\nnotifier.notify(\n  {\n    title: undefined,\n    message: undefined,\n    sound: false, // true | false.\n    time: 5000, // How long to show balloon in ms\n    wait: false, // Wait for User Action against Notification\n    type: 'info' // The notification type : info | warn | error\n  },\n  function (error, response) {\n    console.log(response);\n  }\n);\n```\n\nSee full usage on the [project homepage: **`notifu`**](http://www.paralint.com/projects/notifu/).\n\n### Usage: `NotifySend`\n\n**Note:** `notify-send` doesn't support the `wait` flag.\n\n```javascript\nconst NotifySend = require('node-notifier').NotifySend;\n\nvar notifier = new NotifySend();\n\nnotifier.notify({\n  title: 'Foo',\n  message: 'Hello World',\n  icon: __dirname + '/coulson.jpg',\n\n  wait: false, // Defaults no expire time set. If true expire time of 5 seconds is used\n  timeout: 10, // Alias for expire-time, time etc. Time before notify-send expires. Defaults to 10 seconds.\n\n  // .. and other notify-send flags:\n  'app-name': 'node-notifier',\n  urgency: undefined,\n  category: undefined,\n  hint: undefined\n});\n```\n\nSee flags and options on the man page [`notify-send(1)`](http://manpages.ubuntu.com/manpages/gutsy/man1/notify-send.1.html)\n\n## Thanks to OSS\n\n`node-notifier` is made possible through Open Source Software.\nA very special thanks to all the modules `node-notifier` uses.\n\n- [`terminal-notifier`](https://github.com/julienXX/terminal-notifier)\n- [`Snoretoast`](https://github.com/KDE/snoretoast/releases/tag/v0.7.0)\n- [`notifu`](http://www.paralint.com/projects/notifu/)\n- [`growly`](https://github.com/theabraham/growly/)\n\n[![NPM downloads][npm-downloads]][npm-url]\n\n## Common Issues\n\n### Windows: `SnoreToast` text\n\nSee note on \"Windows 10 Fall Creators Update\" in Windows section.\n_**Short answer:** update your `appID`._\n\n### Windows and WSL2\n\nIf you don't see notifications within WSL2, you might have to change permission of exe vendor files (snoreToast).\n[See issue for more info](https://github.com/mikaelbr/node-notifier/issues/353)\n\n### Use inside tmux session\n\nWhen using `node-notifier` within a tmux session, it can cause a hang in the system.\nThis can be solved by following the steps described in [this comment](https://github.com/julienXX/terminal-notifier/issues/115#issuecomment-104214742)\n\nThere’s even more info [here](https://github.com/mikaelbr/node-notifier/issues/61#issuecomment-163560801)\n<https://github.com/mikaelbr/node-notifier/issues/61#issuecomment-163560801>.\n\n### macOS: Custom icon without Terminal icon\n\nEven if you define an icon in the configuration object for `node-notifier`, you will\nsee a small Terminal icon in the notification (see the example at the top of this\ndocument).\n\nThis is the way notifications on macOS work. They always show the icon of the\nparent application initiating the notification. For `node-notifier`, `terminal-notifier`\nis the initiator, and it has the Terminal icon defined as its icon.\n\nTo define your custom icon, you need to fork `terminal-notifier` and build your\ncustom version with your icon.\n\nSee [Issue #71 for more info](https://github.com/mikaelbr/node-notifier/issues/71)\n<https://github.com/mikaelbr/node-notifier/issues/71>.\n\n### Within Electron Packaging\n\nIf packaging your Electron app as an `asar`, you will find `node-notifier` will fail to load.\n\nDue to the way asar works, you cannot execute a binary from within an `asar`.\nAs a simple solution, when packaging the app into an asar please make sure you\n`--unpack` the `vendor/` folder of `node-notifier`, so the module still has access to\nthe notification binaries.\n\nYou can do so with the following command:\n\n```bash\nasar pack . app.asar --unpack \"./node_modules/node-notifier/vendor/**\"\n```\n\nOr if you use `electron-builder` without using asar directly, append `build` object to your `package.json` as below:\n\n```bash\n...\nbuild: {\n  asarUnpack: [\n    './node_modules/node-notifier/**/*',\n  ]\n},\n...\n```\n\n### Using with pkg\n\nFor issues using with the pkg module. Check this issue out: https://github.com/mikaelbr/node-notifier/issues/220#issuecomment-425963752\n\n### Using Webpack\n\nWhen using `node-notifier` inside of `webpack`, you must add the snippet below to your `webpack.config.js`.\n\nThis is necessary because `node-notifier` loads the notifiers from a binary, so it\nneeds a relative file path. When webpack compiles the modules, it suppresses file\ndirectories, causing `node-notifier` to error on certain platforms.\n\nTo fix this, you can configure webpack to keep the relative file directories.\nDo so by append the following code to your `webpack.config.js`:\n\n```javascript\nnode: {\n  __filename: true,\n  __dirname: true\n}\n```\n\n## License\n\nThis package is licensed using the [MIT License](http://en.wikipedia.org/wiki/MIT_License).\n\n[SnoreToast](https://raw.githubusercontent.com/mikaelbr/node-notifier/master/vendor/snoreToast/LICENSE) and [Notifu](https://raw.githubusercontent.com/mikaelbr/node-notifier/master/vendor/notifu/LICENSE) have licenses in their vendored versions which do not match the MIT license, LGPL-3 and BSD 3-Clause to be specific. We are not lawyers, but have made our best efforts to conform to the terms in those licenses while releasing this package using the license we chose.\n\n[npm-url]: https://npmjs.org/package/node-notifier\n[npm-image]: http://img.shields.io/npm/v/node-notifier.svg?style=flat\n[size-url]: https://packagephobia.com/result?p=node-notifier\n[size-image]: https://packagephobia.com/badge?p=node-notifier\n[npm-downloads]: http://img.shields.io/npm/dm/node-notifier.svg?style=flat\n[travis-url]: http://travis-ci.org/mikaelbr/node-notifier\n[travis-image]: http://img.shields.io/travis/mikaelbr/node-notifier.svg?style=flat\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "mika<PERSON><PERSON>@gmail.com"}], "time": {"modified": "2023-02-11T04:47:22.561Z", "created": "2012-12-02T19:23:42.872Z", "0.0.1": "2012-12-02T19:23:44.814Z", "0.0.2": "2012-12-03T13:02:02.567Z", "0.5.0": "2012-12-12T15:05:58.195Z", "1.0.0-beta": "2012-12-12T18:33:09.115Z", "1.0.0-beta2": "2014-01-08T17:20:19.065Z", "1.1.0": "2014-01-21T21:25:21.071Z", "1.1.1": "2014-01-22T07:22:59.910Z", "1.1.1-1": "2014-01-22T07:28:01.954Z", "1.1.1-2": "2014-01-22T07:33:54.521Z", "1.1.2-0": "2014-01-22T20:49:42.528Z", "1.2.0": "2014-02-14T06:47:40.260Z", "1.2.1": "2014-02-16T12:41:54.530Z", "2.0.0-alpha": "2014-03-07T22:16:27.792Z", "2.0.1": "2014-03-11T14:47:49.063Z", "2.0.2": "2014-03-14T12:11:25.400Z", "2.0.3": "2014-03-22T12:48:42.246Z", "2.0.4": "2014-03-31T17:31:43.785Z", "2.0.5": "2014-05-19T12:59:27.329Z", "2.0.6": "2014-05-20T06:04:24.138Z", "3.0.0": "2014-05-27T16:20:49.754Z", "3.0.1": "2014-06-03T18:47:50.903Z", "3.0.2": "2014-06-08T17:52:50.690Z", "3.0.3": "2014-06-08T20:05:43.533Z", "3.0.4": "2014-06-12T20:15:53.666Z", "3.0.6": "2014-06-23T19:08:27.070Z", "3.1.1": "2014-07-25T21:11:34.000Z", "3.1.2": "2014-08-01T23:35:51.127Z", "3.1.3": "2014-08-05T19:35:51.031Z", "3.2.0": "2014-09-10T16:45:13.217Z", "3.2.1": "2014-09-10T19:08:06.816Z", "3.3.0": "2014-09-20T20:21:13.079Z", "3.4.0": "2014-09-21T11:09:06.141Z", "3.4.1": "2014-09-23T08:22:44.610Z", "4.0.0": "2014-10-05T20:09:47.138Z", "4.0.1": "2014-10-07T09:09:42.107Z", "4.0.2": "2014-10-08T18:52:18.915Z", "4.0.3": "2014-12-06T19:17:43.863Z", "4.1.0": "2015-01-26T20:49:07.956Z", "4.1.1": "2015-02-16T22:31:32.812Z", "4.1.2": "2015-02-21T15:51:48.201Z", "4.2.0": "2015-04-15T20:29:17.447Z", "4.2.1": "2015-04-16T07:45:59.346Z", "4.2.2": "2015-06-18T13:09:38.096Z", "4.2.3": "2015-06-18T13:32:53.786Z", "4.3.0": "2015-09-22T08:11:10.869Z", "4.3.1": "2015-09-22T08:20:50.859Z", "4.4.0": "2015-12-07T11:25:19.533Z", "4.5.0": "2016-02-10T14:36:01.916Z", "4.6.0": "2016-05-25T20:21:34.193Z", "4.6.1": "2016-08-21T20:37:19.329Z", "5.0.0": "2017-01-27T19:43:40.000Z", "5.0.1": "2017-01-27T20:21:48.249Z", "5.0.2": "2017-01-28T17:51:33.729Z", "5.1.2": "2017-03-16T12:24:26.552Z", "5.2.0": "2018-01-13T12:20:53.254Z", "5.2.1": "2018-01-13T18:04:37.975Z", "5.3.0": "2018-10-19T18:07:54.272Z", "5.4.0": "2019-02-03T19:17:15.031Z", "5.4.2": "2019-08-04T19:22:33.666Z", "5.4.1": "2019-08-04T19:23:35.747Z", "5.4.3": "2019-08-19T10:43:04.421Z", "6.0.0": "2019-09-23T13:46:12.667Z", "7.0.0": "2020-04-08T13:18:25.887Z", "7.0.1": "2020-05-22T20:56:31.091Z", "7.0.2": "2020-07-21T20:29:32.934Z", "8.0.0": "2020-08-13T10:03:32.433Z", "9.0.0": "2020-12-11T07:40:21.812Z", "8.0.1": "2020-12-15T15:33:50.959Z", "5.4.4": "2021-03-11T18:59:23.331Z", "9.0.1": "2021-03-11T19:36:46.159Z", "8.0.2": "2021-03-11T19:40:29.761Z", "5.4.5": "2021-03-11T20:00:30.287Z", "10.0.0": "2021-05-25T17:02:46.049Z", "10.0.1": "2022-02-01T20:40:14.517Z"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "readmeFilename": "README.md", "homepage": "https://github.com/mikaelbr/node-notifier#readme", "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "license": "MIT", "users": {"adamdscott": true, "alanshaw": true, "joshmu": true, "luismpk": true, "daviddoesdev": true, "goliatone": true, "piotraldo": true, "jeffcann": true, "mecal": true, "crissdev": true, "solygen": true, "9minds": true, "ijy": true, "h4des": true, "gdbtek": true, "mykone": true, "gustavohenke": true, "itonyyo": true, "peteward44": true, "j3kz": true, "master-1-": true, "yeahoffline": true, "allain": true, "jklassen": true, "lauborges": true, "jerkovicl": true, "temasm": true, "brandonhorst": true, "rahul_thakur": true, "bhdrgkz": true, "igorissen": true, "mondalaci": true, "narven": true, "jruif": true, "bpatel": true, "amnzero": true, "subchen": true, "clong365": true, "a1ip": true, "remryahirev": true, "barenko": true, "alectic": true, "ubenzer": true, "kurisubrooks": true, "filipecarmona": true, "kikna": true, "knalli": true, "antixrist": true, "wangnan0610": true, "khanghoang": true, "vutran": true, "davebowker": true, "ridermansb": true, "ipelekhan": true, "sqrtthree": true, "demod": true, "devonwen": true, "koutak": true, "arefm": true, "mattgreenberg": true, "ylerjen": true, "mlm": true, "jlagunas": true, "riesco": true, "bsnote": true, "moosecouture": true, "amaresh494": true, "foto": true, "xiechao06": true, "markthethomas": true, "aa403210842": true, "arttse": true, "tyouritugenn": true, "celador": true, "manjunathd": true, "stone_breaker": true, "snowdream": true, "maqicxu": true, "ddkothari": true, "thevikingcoder": true, "trolster": true, "dmdnkv": true, "qisong": true, "dbuggerx": true, "houzhanfeng": true, "mikestaub": true, "drewigg": true, "akarem": true, "apopek": true, "antonio-gomez": true, "shawnsandy": true, "hoanganh25991": true, "wangfeia": true, "wgerven": true, "sam16": true, "lonjoy": true, "davidnyhuis": true, "sundaycrafts": true, "wenhsiaoyi": true, "rbecheras": true, "qqcome110": true, "adamdreszer": true, "jochemstoel": true, "olonam": true, "santi8ago8": true, "coston": true, "manikantag": true, "da5atar": true, "seangenabe": true, "noitidart": true, "yuch4n": true, "egeesin": true, "olegkorol": true, "domjtalbot": true, "juandc": true, "williewrightjr": true, "rexpan": true, "rain1": true, "kankungyip": true, "rocket0191": true, "adriancmiranda": true, "amit_merchant": true, "jirqoadai": true, "dekatron": true, "mohamed_alrifay": true, "brainmurder": true, "in-the-box": true, "heartnett": true, "tsxuehu": true, "percho": true, "abhisekp": true, "kerwyn": true, "broughtback": true, "max_devjs": true, "leonardosnt": true, "papasavva": true, "gggauravgandhi": true, "robgleeson": true, "oz-mursleen": true, "battlemode": true, "django_wong": true, "ruzzll": true, "ivan.marquez": true, "theuniverse": true, "morewry": true, "nuer": true, "smalldragonluo": true, "superchenney": true, "warcrydoggie": true, "yeming": true, "krickray": true, "moonliightz": true, "tambeb": true, "rochejul": true, "babsey": true, "mhaidarh": true, "blackflagcross": true, "nraibaud": true, "shuoshubao": true, "alexandru.vasile": true, "professorcoal": true, "lfdo20": true, "chocolateboy": true, "brandonb927": true, "hitalos": true, "anypossible.w": true, "sonufrienko": true, "chemdrew": true, "wozhizui": true, "zuojiang": true, "hellotoby": true, "s_grig": true, "tcrowe": true, "daizch": true, "lius971125": true}}