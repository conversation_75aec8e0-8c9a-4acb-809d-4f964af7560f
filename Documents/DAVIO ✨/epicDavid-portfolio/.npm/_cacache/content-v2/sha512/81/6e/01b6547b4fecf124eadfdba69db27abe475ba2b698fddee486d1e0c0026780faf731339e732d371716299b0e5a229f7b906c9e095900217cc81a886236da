{"_id": "@rollup/rollup-darwin-arm64", "_rev": "154-cbcc75b0700b68ec7a5ff51cec32ee5c", "name": "@rollup/rollup-darwin-arm64", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "4ed273cbbd62cc95310ef52dd4c05955e5141bf3", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-oD+BvNCoQT2+VTwkLQRZPvOzE156Np/geh+Sc29Ge8aeRdpKgA6/FEclhxj4AzWC+Yg7idUCf3/vIcj+F4yDJQ==", "signatures": [{"sig": "MEUCIQDw5rxxkFjDhC05vf2KEmgqdTiYhhDqj/qB5mNHKEH1XAIgV5TFVU3Wg5otLJ8nCRx5tdl8lALIgw9IIjAiKYjvSvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 717}, "main": "native/rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-0_1690831070379_0.19476884157882246", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c6fdfbef10e34e71a1b53c540be239dabc619d8a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-awU9Sn1PItJQwC9sOZtJfMPZSzF77XGUVvyp+QgI6ymQEKugQDRbd6qw0L2tzejmUtfWkN6L154fTpb55N6jkA==", "signatures": [{"sig": "MEQCIGzj5VxHHTzymMj1jIT8zTyDE96qpZ1H3jxRZD3NpEyyAiBgJeZ7trZxXfGNFXjTFD3dztiPUFrRVMCJJ7tUXHfwyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2177542}, "main": "rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-1_1690865333844_0.1383083802773093", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "cc3214726d680982bd0a87a44bb9509f51534e2a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-ap4Kl30hsDiYHSzF5iG1hhSyRM1TepdEk/4Av7soeroxQbsQedTYY0xkIF1FTXF0MUrKqE916IDkTDBZxDozcQ==", "signatures": [{"sig": "MEQCIB4c0t/B0/n7CdAZ4E6SdWGeCkvRJ7yCDkcXqPZ2su1GAiBHgINgVJ9QuggMaFjWwIll8IFjM5OFWt4aYyv7nEvrLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2177542}, "main": "rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-2_1690888586533_0.6349854630002247", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "659c3e1c6253c083103ec2577f0768288babaa84", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-rXJ5Ky/XSXC+bWx/T4DSgz/CQWyKuRUqi0e7o9jiLv8k8T70Kpg23He/fdlFWG5MAokpHjuC6iArm6V8DkbI0A==", "signatures": [{"sig": "MEUCIQCthmiNSPbiiDW9syz79sYGWmmNowq6jvzZgDRK1OZBwQIgAoScPlt3RuoF3NGQuLkx36BBfPozbyfWImZOsDQFciI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2161030}, "main": "rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-3_1691137007832_0.6671824677718416", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "45077fb537b94758ca824238f11b2a9b702b3084", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-Kq/uDTw9mkzCidFMU4tsvO06NqhGCI8LLHA0cOhBk3qMYaMZ9yjTu57eRdBgNVJjffOohmOliYdJN2h0s6mNuA==", "signatures": [{"sig": "MEYCIQDcFTdfzm5Stg3RLsrHYqO8VVCgVkLQI4JBNICMnR2VzAIhANW3z67HYWd9Q3nxIAklHLHIVqpZk5nFW10xdR7UQgSJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2161030}, "main": "rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-4_1691148987745_0.7779574281379114", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "475b706bda4f8a9df106ab9cfa777d2fa070f8a8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-PYRMO/Fqts4kxcd3swKeTONH2sqhLacXSZik7kP9PouZ2p34F/+FvOwploAJ9Z0CKQ7egHQdE3yAhF8e/bAXnA==", "signatures": [{"sig": "MEUCIQD3nnUFrJ57LfqfOzNxGpuuyvC8UvnCvWLbodyNtEEk8wIgcejbHlYrvBr32a3AVtOVfoTi5RkivxvYTT3hY9DTK04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2526265}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-arm64.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-5_1692514602216_0.14096744493048607", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "341fea6fa6d00cd45de133648ea81c4914ee7e54", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-yeLSA48R9y/Z/wBa+J2Vw0ppHSzpYMZsDs0XzvqbWJp86vNwA28olx14W318sld5nKkx+RlGh4BA5z1wR0Z93g==", "signatures": [{"sig": "MEUCIQDJ7c2ZhaFmM5ljpTeVsaKDeq67P13VLfY2rMU7hX4cSwIgQ0HGO3RXJA7Qy7NKl324EwrVbtbKVMExIjsfblshQ00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2526265}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-arm64.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-6_1692517891992_0.4976225977894684", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "28376176e151268e9891c98c834af567120aad54", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-X+FaLPdfDseVinz6axzon/Mz6XsSqW2fD2Wci/lw3/DZeD974oyEXTVhPIsQbvClNsSfjymPg+Q7BBL9j7Wzvg==", "signatures": [{"sig": "MEQCIFudgGjBmoaXUJbukqzQt4qEnXTHTmGh63Ee+mXW4XpWAiAP6IvsX0WiIjRUWdzcGfm66fwqbZrc/no3aFoIQC4/Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7981187}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-arm64.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-7_1692527594191_0.15427202000801832", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8e2d9e955a5120299908fe83b2e901e8e486fbfe", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-0wr0DPIeMTZtVph1lg5LGR+ZSq4phSbYfgVBp+2BTSkiOCAvZd1B5X37NHTyH1S5/VnrykCmsHnHhH0q090cGQ==", "signatures": [{"sig": "MEYCIQDy9irQJiE5Zxx6EbB1KrS/377xEl5bwnVHwZHRrSTjHQIhAOW97jaRK6fwh0Ry4cKrwjyl0HScSpzaswMkGwpyUPq1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7981187}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-arm64.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-8_1692530524642_0.238961607328116", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5aef5f4b1b7b0dd54e4ecf558d1ac434c7dfa465", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-dG4eSb55W0pBKymyTri8MXpB1aI+5Y97s14Eku01i4VepK74AWFQ/kVyxl5OUVKN1UXG2IAnOEwO8T1yG1C04Q==", "signatures": [{"sig": "MEUCIE7+jt6qvKINQRBn5jMiKmQH39mfxKViC9cEmBagHigWAiEA4FYtlrEwDPj/NkTsuaGDlsZc/KnLFmpRWpcUlgsxVBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7981187}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-arm64.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-9_1692541730550_0.8903905583767746", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "bccefda196a89148e0638fa004f90dd5b80a3e0b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-ZzGU4/BMCYKzM7+kn+4pxgF+YxEiODQw3kAWE4SFgZOhSR1yGlAlJXGplbmnqco2hDU8KmLpGPFudfeyk+M6AQ==", "signatures": [{"sig": "MEUCIAhLcg9fukYbSLf4nWQgyduCU5ESvoZCD3HwpX8ROrcsAiEA5vmbhS2bB9Wpz9P/VI9ZZkXdWp5r+n6qa8iuFOOyp7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7986031}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-arm64.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-10_1692631788686_0.47938371684879577", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "567e1237bf528f39b823d9d037ae691d87a1b971", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-7/cMwv0ae1ZNRtsEGv4eeydN5RV8JSGBv0GkPf3+e/Y3/dPIt8CouR0Zc22s0C5yRX9paWV/a8xaMEkHe1Ld1g==", "signatures": [{"sig": "MEUCIAuJXfUk7ENW1SUlpArmD9gqLWQEvipnI/Zaash+2VerAiEAutCAr1F4evBjWdwxvxaEwuWhx9eaTX3tvLCYU3+zRag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509594}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-11_1692785740253_0.7866403707143597", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "cb0c98fdb4239179bbc490187536022dc345e9d4", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-6EnGJW8woYvNB1gekab+A/oXnD8qvjcPiFF8BIWmpkd4lXlMHNLEWY4p3mQZtUoElJi7Q00RobC5SfFu0pUCXQ==", "signatures": [{"sig": "MEUCIQCvZOoW7jcWmOHDtUUgJZjsR95nwPLiNlGBdQtAzZM6lgIgYLIegz0PjNVFdcWdems0gJALWlIRPCYMJfli2pf++3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509594}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-12_1692801615930_0.6762655679273557", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a2de97784ce20d6a6586c26cd3976466c8889fb9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-elIo+kx1spRx1c37IguMFESKkQx2575WgCQ8OOevPhp8c7JU7k/hU1hgMui7RIjs1Ga9rr8u9OZiWzXfArqsAw==", "signatures": [{"sig": "MEUCIQDMWYhC+4KbH0w0bShrYjOH7otn5+MvG/XY+0J2d3Vh1QIgTIYSt4fex/7yF6/U68PIFYk9r5Sk3FFULRkDU+0/Hs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2526138}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-13_1692892106425_0.8849588128011432", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "da9c06f9de8987c6fc830103e359cb4084fd57a2", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-6dFkwzSLPS5grUtUBRpb5zilKvDZkhsLeQtZN+Aj2d8TEBevv2S5Ptm3WCf/fcAQHcq9kOT1AL+YcY9Iq6moCw==", "signatures": [{"sig": "MEYCIQDZ9SEoNlfyl5J1QP1DSPf9wBDxPs13PH5xAysUXDgPHAIhAL5ming7DYQaeHEqpd1PszOxYnjlANtG/428+lO9aj1P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509658}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-14_1694781259727_0.6752511437560953", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "946a7a32af7ff407234fb0804ee861f0a95017cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-kBeMcdH+h05wHJ9yFKyEMW8U0Eq6297TbQnZbIcuBphtENdG/fw+4NhAAj2bU+uOnaNedehjnybsRrAkO946dg==", "signatures": [{"sig": "MEUCICd9PlPIAauQ8Wws7vltypBfA87R1HLvDz26LDWt1naKAiEAs/bZlLsQ0yD86jn240rH9YC3IZ3TR78G4/sSAebQd1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509658}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-15_1694783206482_0.5765341115945937", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2a1eb5b488f93565e1cf908f21a4d15a34d3c6ee", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-iLE4GKYwmJLOKkdzwFC/ZUCg1izIPJwp9MGUJo4sLWhycHE8LlxBar5X12xH6OYs5JktYk2SmeiZvYy/vBH8Kw==", "signatures": [{"sig": "MEYCIQC5Jdnh81IQ/bGaKv8oeARqHKB/vON2A6LJt+DFZ+75BgIhAPUgAksymX5J3BuJ+OJI6iFgy99Xwx+uVelig7fizF+E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509658}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-16_1694787428440_0.9080898291879647", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a157d0b99153bafff2a29058f0ec96ea724c2a7b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-wjiPKSdw4cll8H84BDC0dokaUjdXuzNpJaPHs5OXH3lcL2JUng/P7jUzFbatz3qSt9PgcG3MukJSSe2Vwk7/AA==", "signatures": [{"sig": "MEQCIBM4sHbvNtbCcFrFWtQEsx+Yf6F2XGk7U7jLOiwwVxNQAiAvX/1Yuvy1Exlb+DuxtBdYjKn5qLgilV7rU9aT7ozXTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509658}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-17_1694789940187_0.14078575835444895", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b648e547a3c53ee7dae4151e6fb2134904b04b1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-BQqXSa6ZpIv/2siiIBdPw5qtdCHIasjIKi1Z9sKjv/1DO4sqPXunDUjotoq1S4qd37MqNmwQuxH1O7dL79QErQ==", "signatures": [{"sig": "MEUCIQClhofqBTLhSEqWwIbqYEPJv00/bNV8nQj3maZ58XFbugIgOuLg3p87eXx5QZaH1Th7yTZT+eoBUK3by/ihf9Z70CU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509658}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-18_1694794191576_0.5156993363080131", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c7e75d1b7c16a0ce5bded2a5fd5352bc1505858a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-Z+76eZICvhe7cpesb5jA4t3xvK9RU8c+bIK0u/yRNpDeFvksaGHvMTYbdNhYIqi2xvz4ubBjR8bVhy3ZWOS2Iw==", "signatures": [{"sig": "MEQCIAGqvS23V9zCH164Fp1DdFb6/QWvI0CPbFNJBwAK1nZPAiBW7tQsulx9/JEb4P6MBp/dWNB0OhgB+fVKH+b1IFZUoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509658}, "main": "./rollup.darwin-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-19_1694803850303_0.2490851224087316", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6b2da730266512269c90c3dc5c1d40230d33a32a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-OHQb2X7tfGMN0m0Q1iKPfo+yVQF/P+yB0fXw8IvMYpcwvbFRJhtxgmONx/rRRp9cdzpeNCzg7j1PvwL/cc+3nw==", "signatures": [{"sig": "MEUCIGw2DFhxfjmQOwnHPfI2r2TLclG3XFwcntLxl1MpiRpqAiEAgByugrf6viLVD75svdHnP2clKzdR5kB1ee7tOXlfhDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509544}, "main": "./rollup.darwin-arm64.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-20_1695535831652_0.2148082009014185", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "def190ceff15663f59c28d90a78a37a8d8087a4f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-+ChiEXP9TwIohDkk76nICSm2jf5E/Q50KKps8bn1ckWaTiKjv4GNosuK7+mj4tZAXYXW6Ldr5AUGJY4++Yth/Q==", "signatures": [{"sig": "MEUCIF6McrhkywgCNTM3AZIgWrH1EOHaEeZ0AcPsZr3coLhZAiEAjMnjyiCt3W5iIQGcvyisnKOXTNXI5mnxl02B9zgK1XY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509576}, "main": "./rollup.darwin-arm64.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-21_1695576137186_0.029831371590647082", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2572a1bc628cc9a4678a8bddcd210b50ffa52608", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-ZKt85S7uUyTfnCp+Ez8nMtg5AkK48MycYok8+d2e5SHVsrvFLQSz7Zptr57BkZpNOCX4P2pa/P0WD+7FjGrUHA==", "signatures": [{"sig": "MEUCIBVGQxhTUTEl23OqBEZQUhETVkj4GwhlcF8eHf2bpNMqAiEAm5lWXzu+lFA9TO4vfRdT0OQObAKcgUaJWxidjdYijvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509592}, "main": "./rollup.darwin-arm64.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-22_1695745025859_0.42429255767460683", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0a5286510fdb7dfe4b15ff0cf46d140f528c21f8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-2Vnxl0XiBxgyk8BDgkZF/AkP17bB8YpfUg73MjLVHzv4Kls9MHUAQLaanDZzqrsyXZpJb0f2+2oALoDK9+lRuA==", "signatures": [{"sig": "MEYCIQC2NW6bZ5SuQ+3N7ds05M3NaX2EoYZYt499DeGGQJS9pQIhAIGRGbFcogk9aDx2WbCWFzsxOBTItgAlzlEuCC9ozesa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509592}, "main": "./rollup.darwin-arm64.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-23_1695759256605_0.5859783916814592", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2bb2ced08a51c1a1cb4379ef7f5c8699bf2018d5", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-wYXWdPbMLiIRHQeTF/r9ZoDcf3k1ROR0Kyd/caUtbs5VEZOBfnpZ/FHQPzXW0S1fzxTtD5W4tXULxARMHAlNdQ==", "signatures": [{"sig": "MEUCIQDOeD6kjibf9y44hOvLb72R8lGAwK0OpMMrtVaQdPFDhAIgQVdNi22QPLP0RqNkVOWPwk6KN0blmEZ2sWfMYyfufzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2525976}, "main": "./rollup.darwin-arm64.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-24_1696309958276_0.13788606891834254", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "f4bb650089a3e7dabbf9184ed44ced0a0763fae7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-TdFeix3OnRTToCuBP5rLhtzxV3CPrfUkGlsC1itCDevbRj9eSAJqnoHWkip0wkqh2OBw5i7C64iF+TTq/U7U6g==", "signatures": [{"sig": "MEYCIQDAfFB2T9UboCI+iSmfI1raSxxYuLALByaUkRbE0/Xk4QIhANuLW1dmuLQRw25CYH4yKDDs/lhfEwst77ay+XZcmq9p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509576}, "main": "./rollup.darwin-arm64.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0-25_1696515155528_0.609525392036701", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "24a63082a59a5e482ef240441257c2a75d6e2d04", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-mOz75DpOOHGk4+xYbh1E23vmSOrOqskTwq9s/e2Z46eYbTZ0+s/UVoS42cLG8dUe6enF2Xh3hTtiIEzLhO9kmA==", "signatures": [{"sig": "MEQCIEvnM9hSFokmCQ3JSooV/NrW3gPXXDShC8ZLWUNG4TIzAiA9ITGhnRQdYL9PfkLYMk8tjvMBvWvFER+qslYoVCnWcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509573}, "main": "./rollup.darwin-arm64.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.0_1696518864025_0.7544191161723899", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2b2c840133948b1fd534d8ba4f9dbc4a9a5cee0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-I+AvpO2lt+ZpXcTIG1JU99fD1kNUqk7ppKBMcBRHRXlE+e+QV1Ewpq7yjD346dNg/O8Tq5qzM9hoUAziM3jVMw==", "signatures": [{"sig": "MEUCIQCR16/RVJUWDyRTnq/xU46ppn4M1yW8DEFo7SVn5rukzgIgDCufLoxz8hrmBsj9u+Vu0KclAetDZdmgz+zaNRWAKcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509557}, "main": "./rollup.darwin-arm64.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.1_1696595792168_0.7061547419362626", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "072f9133f6ff7a4fcb06ed9b718c6882964d52bb", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-nkBKItS6E6CCzvRwgiKad+j+1ibmL7SIInj7oqMWmdkCjiSX6VeVZw2mLlRKIUL+JjsBgpATTfo7BiAXc1v0jA==", "signatures": [{"sig": "MEQCIEZDPdtGfmEsBrTFR1m89wd13rKU9rGtDzTzHbi/1K8+AiBYvVhGo/uyXVphH/pauYZByL2bAvoMc5Y7HC5q+Cwj5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509525}, "main": "./rollup.darwin-arm64.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.0.2_1696601913873_0.7062105965984338", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0c733ee423c1a94420955febdc2285ed781bd089", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-xpZp/bE29sSYoopkOepdDRui/KnlXjSyf/H0qVOOzjTYE8WxqMfDMfwcgb+ORuaq12RdZfA/nLKZ27PL1AuazA==", "signatures": [{"sig": "MEYCIQC4Fm9CvTdrN9/e2KmtTRDJdltiLPBG25Ai+XPeZaWEnAIhAOjWMc+9OfAefrLHnIR9oNOhzydZbuC8By5fSZHBXhhP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542613}, "main": "./rollup.darwin-arm64.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.1.0_1697262727060_0.09836377201513091", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a335a64d60c5f0e34a2f1f9f9630fdbb223db5e8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-P3vZSdUElHFe3pTO1mnwwzsnA/ohddfofdhJWj4TRUe6Ug+lq2VjmST18Cq5R/H9Mt+7iDQSuUAH1TNUHpIIbg==", "signatures": [{"sig": "MEQCIHJ4aAiHU/vuXpjYLpxlcOX6iyuZojLZpxZYo37Lw+2cAiAxh33yeWcp8N+2FHV0fgcfVBDxKlQ5t2fwAUxh01HVUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2642837}, "main": "./rollup.darwin-arm64.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.1.1_1697351495478_0.7691482297103793", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "feb692d4e007e302427bf218883802f207425308", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-ibOYcmg3VTXxQOb9HnrmGCshO/5hWucl/Yw8KSt6DE12oif4ssX9pxBBEodu65fprS6qKy80rSs1VvpzVFRRXA==", "signatures": [{"sig": "MEYCIQDixw+HqV4oQhUzpK4TAL50VrRZbiXt282Qw1DgoXd/pgIhAOQD0sYYwwBW7hS4rGnfHCABSHVG+jbfHfiUrgemhp55", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2642837}, "main": "./rollup.darwin-arm64.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.1.3_1697392098973_0.02472057164593866", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "96b8ad0c21582fe8223c66ed4b39b30ff592da1c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-7vTYrgEiOrjxnjsgdPB+4i7EMxbVp7XXtS+50GJYj695xYTTEMn3HZVEvgtwjOUkAP/Q4HDejm4fIAjLeAfhtg==", "signatures": [{"sig": "MEQCIF6kSBl05vay1ehl69aOkyYh711hBttcMa02oxykILW2AiB8IluxCgiFDnJyubS23W+L2JxBJCSmEbR5u5Tv2PkNwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2642805}, "main": "./rollup.darwin-arm64.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.1.4_1697430842381_0.47929573000007486", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-darwin-arm64", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "7ddfe2dc9f369fc9560d007a5297492e2d289f45", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-lTDmLxdEVhzI3KCesZUrNbl3icBvPrDv/85JasY5gh4P2eAuDFmM4uj9HC5DdH0anLC0fwJ+1Uzasr4qOXcjRQ==", "signatures": [{"sig": "MEUCIQC8whT1zJjFJdxgYQOty1z/bwwwFgx+lO8KSVLdBow/FwIgXDklKvCdTElYK30ul1zHhMQfRW1N+fHI/KfOa+i5Nis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2642773}, "main": "./rollup.darwin-arm64.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.1.5_1698485003983_0.6697928009582681", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-darwin-arm64", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "74769c1ca23f5a04c17815dfa560556aafb07038", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-/peextO9XymEY2SYYoI6d3z5a/IXgBbQbAE8hvMTe2XRjZuP6RrhYlTygUzfz1Xfr5sZ/yPb49zAh5v4mOYlVw==", "signatures": [{"sig": "MEQCIDpGWS6QYR+KzAJDTPPNfmjZl7IbXZmHv6y8m/KCPDZ4AiAtcQ6Jft0q8Vsqf1rmbQYq3MySIoXLv624vYEDlS+SoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2642757}, "main": "./rollup.darwin-arm64.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.1.6_1698731106884_0.051764538836221785", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "4c468e8fce7ffa121c8b2067d5f3feb1bbeefec6", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-IIIQLuG43QIElT1JZqUP/zqIdiJl4t9U/boa0GZnQTw9m1X0k3mlBuysbgYXeloLT1RozdL7bgw4lpSaI8GOXw==", "signatures": [{"sig": "MEUCIAt3PBFQNJwMzV8cbzMAuK11kx5/KV+fkVYn4UmBV3UOAiEA/GSliswB1hSyF+g1ESQC9mWtvFlMr7k6zFbJnRG+Ua8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659957}, "main": "./rollup.darwin-arm64.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.2.0_1698739832928_0.9822730312413459", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "80c4a4dd7b120906d4e655808fb9005784a8bf35", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-dGhVBlllt4iHwTGy21IEoMOTN5wZoid19zEIxsdY29xcEiOEHqzDa7Sqrkh5OE7LKCowL61eFJXxYe/+pYa7ZQ==", "signatures": [{"sig": "MEUCIQC/7/89SLoRChm7QFft7dQF1tV7hKZYl71+ItxnSINdfQIgLXh/Dt0RPrIYSeNkLn/Z5fDf/zqVaUgfKmabOxtgwxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2676501}, "main": "./rollup.darwin-arm64.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.3.0_1699042375376_0.12203011752070303", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "755c1e4210088c0744bd92d66888cf235a20e53e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-F19xNgrLNnLTS/LFnTdlmxYvkIjFttDSQmJ6/oXLRZpGX+LAoYZpFcz2sYk5l/umk3M34Dfgnvt1fcMfTuIjzA==", "signatures": [{"sig": "MEUCIB17GxE1tmaktOEpxQT83CjjM5OG9zg1hNeF8zCummEMAiEAr5yP6tYCzCRZf8ochYgalwjnNK52Ivha4evYzCN+p5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2693013}, "main": "./rollup.darwin-arm64.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.3.1_1699689468178_0.5237016201912217", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "27efac74e681888e51386aa4efb2a24ea6df9c5b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-BYmhn1Hebmkmdyn5mBFy7HptowyjtMALyTpywNSNZYigWwyv4L8WQVr0XvOQE7eE6WoKrupSVxtIcGZW8MgZUA==", "signatures": [{"sig": "MEYCIQCGryfm/ywyZmWDambNQrt0pQWmWkROMqzQro0ld1p/mQIhAPWC41Nivj+Mij8oiPwy4o7t2tYvaP1WIpA/do+gGlux", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2311269}, "main": "./rollup.darwin-arm64.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.4.0_1699775388584_0.37011225841093376", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "40443db7f4559171d797581e0618ec1a4c8dcee9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-nz0AiGrrXyaWpsmBXUGOBiRDU0wyfSXbFuF98pPvIO8O6auQsPG6riWsfQqmCCC5FNd8zKQ4JhgugRNAkBJ8mQ==", "signatures": [{"sig": "MEUCIHjrVC8KOVFmRmxd3NF1/kgyjlu2jgoG0zxryM4FrMnRAiEAzvVWlg0/HfmRmCFnhPyqd1ppQCBXBFhzIYxR1FoGZ1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2311269}, "main": "./rollup.darwin-arm64.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.4.1_1699939495299_0.7501692645837403", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "76be6832eee21dabc28f84f9f54fbfcc66615992", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-L0/CA5p/idVKI+c9PcAPGorH6CwXn6+J0Ys7Gg1axCbTPgI8MeMlhA6fLM9fK+ssFhqogMHFC8HDvZuetOii7w==", "signatures": [{"sig": "MEUCIH75UXczjMl+6TNqksfPyOf9D4mSLni3qbsMEjJKogLtAiEAt3vkSJQyV/hfXhjbPt2LyW7IcTwXKmhvXdOMhMRUYu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344357}, "main": "./rollup.darwin-arm64.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.5.0_1700286726242_0.7587755402812597", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5442ca442fca1a166e41e03b983b2f3e3235c17c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-QqJBumdvfBqBBmyGHlKxje+iowZwrHna7pokj/Go3dV1PJekSKfmjKrjKQ/e6ESTGhkfPNLq3VXdYLAc+UtAQw==", "signatures": [{"sig": "MEUCIHbbrqBro8bZuzB91Jxn1FVu2Pz9oVibLZtTKlNGaj9gAiEA/SLTW90Hvaxlt2V/CSayy1tIxK0KNuh5PoZAOfzsWHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344357}, "main": "./rollup.darwin-arm64.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.5.1_1700597583323_0.6639892734445685", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a8c13123b830aa743f604b732d72b260dd9de0f6", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-NTGJWoL8bKyqyWFn9/RzSv4hQ4wTbaAv0lHHRwf4OnpiiP4P8W0jiXbm8Nc5BCXKmWAwuvJY82mcIU2TayC20g==", "signatures": [{"sig": "MEUCIQCfZFqO5pyBb9IreRlc09ghswTGa79nbGeLLH3x0maICwIgHhWHZVV8HAXCUvX+ci0xUyCtg0dPD+DumH1LQdq3jwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344341}, "main": "./rollup.darwin-arm64.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.5.2_1700807380374_0.9833218487072237", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6d2f53021fbb9fdecf60bfb6fd5d999aef8385e9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-oLzzxcUIHltHxOCmaXl+pkIlU+uhSxef5HfntW7RsLh1eHm+vJzjD9Oo4oUKso4YuP4PpbFJNlZjJuOrxo8dPg==", "signatures": [{"sig": "MEYCIQDsKyvdo5dploGgNKIVoUdII4j6uPxBERdYExm/4tpf5QIhAOxTve2qptsWS9YP21Ahew88dqUdhSAcDzuOZtqz2TZr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344341}, "main": "./rollup.darwin-arm64.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.6.0_1701005948491_0.7966764198361271", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c5735c042980c85495411af7183dd20294763bd8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-cEXJQY/ZqMACb+nxzDeX9IPLAg7S94xouJJCNVE5BJM8JUEP4HeTF+ti3cmxWeSJo+5D+o8Tc0UAWUkfENdeyw==", "signatures": [{"sig": "MEUCIAscK4raj+c1FZbjiemazl5Vj/NsrnftAvCGopEb+3R3AiEAy98iKE2zwSbWOKOrSAxOUnJZUfbkDkAYCrhZuWYk+OY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344341}, "main": "./rollup.darwin-arm64.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.6.1_1701321779950_0.32516166838311467", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "196018fa70b525a893a949fe8e1eeb797d9a7227", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-4VXG1bgvClJdbEYYjQ85RkOtwN8sqI3uCxH0HC5w9fKdqzRzgG39K7GAehATGS8jghA7zNoS5CjSKkDEqWmNZg==", "signatures": [{"sig": "MEYCIQDUW3J6RX7OF6BLZ5Sx6j7wSIihVIyFyIqUOn3cu8JKYQIhAKQYBrsusptQWp6HdgdLLZMSuOBgnONtZlLozyCc/qCv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2360869}, "main": "./rollup.darwin-arm64.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.7.0_1702022274325_0.19465836435143813", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "7dcb1317a8089762c1f7e437c1e1d695b787b70f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-zhNIS+L4ZYkYQUjIQUR6Zl0RXhbbA0huvNIWjmPc2SL0cB1h5Djkcy+RZ3/Bwszfb6vgwUvcVJYD6e6Zkpsi8g==", "signatures": [{"sig": "MEYCIQCBdg0/4epaXpzxs6u18zosdJaaRF8uqMren/Yqp+YHhgIhAMrKsc6v2lnSJNYP6WigjhItcG1RKuN4BWTffvwNgoDD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2360869}, "main": "./rollup.darwin-arm64.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.8.0_1702275887504_0.5905870511188436", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6f3fdf5712db6b5e3d8f62a86a09cd659dd871f9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-u7aTMskN6Dmg1lCT0QJ+tINRt+ntUrvVkhbPfFz4bCwRZvjItx2nJtwJnJRlKMMaQCHRjrNqHRDYvE4mBm3DlQ==", "signatures": [{"sig": "MEQCIFNAG6wvP/kXMx2/NRvs2lI0GnGZ1TgwFLipWJC2yXJ8AiBPc5Y+Cwe3JCqQvin7o69AoA+snNalcbsf9c23djfj6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2360869}, "main": "./rollup.darwin-arm64.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.0_1702459450746_0.2764550909239478", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "9aaefe33a5481d66322d1c62f368171c03eabe2b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-LtYcLNM+bhsaKAIGwVkh5IOWhaZhjTfNOkGzGqdHvhiCUVuJDalvDxEdSnhFzAn+g23wgsycmZk1vbnaibZwwA==", "signatures": [{"sig": "MEYCIQDVEv5jDdHnwDgH8dn3yRQfNUUPGpXvWcJMpe26a9ps3AIhALkJ7K6YxRCH8dJ73K6jp0cmVD2ysrCv8Vpere2Pylsi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2360885}, "main": "./rollup.darwin-arm64.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.1_1702794365635_0.7171265537326275", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "9f2e5d5637677f9839dbe1622130d0592179136a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-vqJ/pAUh95FLc/G/3+xPqlSBgilPnauVf2EXOQCZzhZJCXDXt/5A8mH/OzU6iWhb3CNk5hPJrh8pqJUPldN5zw==", "signatures": [{"sig": "MEYCIQDLXFlBfoa0XG6crRWy4lt9wSVlGjc9ZodKYUvMaY5MTwIhAMT50CmZ3oTP03LaV9GG2QzYAZtQlpyWB0hL4Lfjxcct", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344357}, "main": "./rollup.darwin-arm64.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.2_1703917402198_0.7258231110945312", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "70748717f8c431a0bd27d53b83c4d40ba3064464", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-Fo7DR6Q9/+ztTyMBZ79+WJtb8RWZonyCgkBCjV51rW5K/dizBzImTW6HLC0pzmHaAevwM0jW1GtB5LCFE81mSw==", "signatures": [{"sig": "MEYCIQCYhzdqGEDx2z6MKMgYI+JkSysVv6g+zLh2ve9zZj3+VAIhALazJh3qbZvKOjZeFSCuw4T2D5fZiyUX2Zt18Tbrd3ev", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344357}, "main": "./rollup.darwin-arm64.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.3_1704435641136_0.34489942843170485", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2456630c007cc5905cb368acb9ff9fc04b2d37be", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-1fzh1lWExwSTWy8vJPnNbNM02WZDS8AW3McEOb7wW+nPChLKf3WG2aG7fhaUmfX5FKw9zhsF5+MBwArGyNM7NA==", "signatures": [{"sig": "MEYCIQCVkT7L/SkKurxlGhhU/i3tPi1/CiGWNw9SJnCy3oRuNQIhAI8d10kgrmhxb6WoRBeFZnOyCSurCX6sEvhsO5R95FY4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344357}, "main": "./rollup.darwin-arm64.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.4_1704523134370_0.4492889780045737", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5234ba62665a3f443143bc8bcea9df2cc58f55fb", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-ndoXeLx455FffL68OIUrVr89Xu1WLzAG4n65R8roDlCoYiQcGGg6MALvs2Ap9zs7AHg8mpHtMpwC8jBBjZrT/w==", "signatures": [{"sig": "MEUCIDFlwenc/5fTL+5noMJGg3NSaC8v4M774c642sdUugb9AiEAo/4yik41bX2ne+7UYfhlLWO+Apjjw3f7TsGrKn9Qwrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344453}, "main": "./rollup.darwin-arm64.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.5_1705040167946_0.8389997641765155", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-darwin-arm64", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "166987224d2f8b1e2fd28ee90c447d52271d5e90", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-CqNNAyhRkTbo8VVZ5R85X73H3R5NX9ONnKbXuHisGWC0qRbTTxnF1U4V9NafzJbgGM0sHZpdO83pLPzq8uOZFw==", "signatures": [{"sig": "MEYCIQD8YQI0Y0uEOypRagHhUFOLR+eOWrJTvFvXHVx+x9RdgAIhAOVG1GWqEcyKZ/ZpyNm5S6ZxglCswij3MBh57/EysVM9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344421}, "main": "./rollup.darwin-arm64.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.9.6_1705816333571_0.7671214077075532", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "944d007c1dc71a8c9174d11671c0c34bd74a2c81", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-uFpayx8I8tyOvDkD7X6n0PriDRWxcqEjqgtlxnUA/G9oS93ur9aZ8c8BEpzFmsed1TH5WZNG5IONB8IiW90TQg==", "signatures": [{"sig": "MEQCIA6SsIs3eXBbffySF7RG/OaQMrDIfR6D8Y48tsg4bZrNAiBeOLyS1tP3xwEfEljYlRGl/RQ1TnQETTuNW2isXd2wDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427190}, "main": "./rollup.darwin-arm64.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.10.0_1707544715370_0.6264553398499877", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "cac4aac05432ac684a6c6c5788e8db738e047a22", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-yPLs6RbbBMupArf6qv1UDk6dzZvlH66z6NLYEwqTU0VHtss1wkI4UYeeMS7TVj5QRVvaNAWYKP0TD/MOeZ76Zg==", "signatures": [{"sig": "MEYCIQD2BFMdQ3iLqmwu/snNS4qQrrHPVLq+ZUDWR1gQMyKs4QIhANmakeUSuUbkVw9drPN6KsfBnuS/zZFIdIv6rPNZ0P02", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427190}, "main": "./rollup.darwin-arm64.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.11.0_1707977370174_0.05783503226811271", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6c082de71f481f57df6cfa3701ab2a7afde96f69", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-X64tZd8dRE/QTrBIEs63kaOBG0b5GVEd3ccoLtyf6IdXtHdh8h+I56C2yC3PtC9Ucnv0CpNFJLqKFVgCYe0lOQ==", "signatures": [{"sig": "MEYCIQCXizI/yzsGD7GykAhd0oGLvWN1YEyH8DzElz1zQ6F0MQIhAMD9y9klGR114Vg+OF1R4PaMVfNrC1ChC2CvWPDS8O8g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2410662}, "main": "./rollup.darwin-arm64.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.12.0_1708090328077_0.537334350705351", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "dc21df1be9402671a8b6b15a93dd5953c68ec114", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-YRXa1+aZIFN5BaImK+84B3uNK8C6+ynKLPgvn29X9s0LTVCByp54TB7tdSMHDR7GTV39bz1lOmlLDuedgTwwHg==", "signatures": [{"sig": "MEUCIQCdLXFHPwFpFCenWh1RA6zZk21NyHBorcLXwjT24NeVagIgA0Q3cMAjEniPm+1mysQne4ufqfGRn2nOWuJW84MGOKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427206}, "main": "./rollup.darwin-arm64.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.12.1_1709705006473_0.6412058674756267", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "ef02d73e0a95d406e0eb4fd61a53d5d17775659b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-Ovf2evVaP6sW5Ut0GHyUSOqA6tVKfrTHddtmxGQc1CTQa1Cw3/KMCDEEICZBbyppcwnhMwcDce9ZRxdWRpVd6g==", "signatures": [{"sig": "MEUCIQDFFY2VpGoKb1DrceiMR2w9AxphWJSOcAkfaI0HENTdGwIgDgctW1tCI90qXKkQtWw/TNPJf2aOi0y4viQv3zamxYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427238}, "main": "./rollup.darwin-arm64.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.13.0_1710221303242_0.584208590046349", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "e54481448ee167253963c02faa24c71423a415ab", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-2b7iM/h9dKsLOPZCUqUUZtERejL6CDqvRqSJdftOPeIS5diIYBeLg+6k30viTCchPcIgWAY2fsHfey4mAFRaOQ==", "signatures": [{"sig": "MEYCIQD1ScjfcYcU5wvguqLq2dYvjnB616ztIpQ6wEAYgw7SQgIhAM8Mru61iHkQc2IrO8FzhSGVv1Vd1viGaxjbyZ9CrmGD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2394152}, "main": "./rollup.darwin-arm64.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.13.1-1_1711265952833_0.6869811688791223", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "52e3496fa66d761833df23a9b4860e517efc7d1d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-fz7jN6ahTI3cKzDO2otQuybts5cyu0feymg0bjvYCBrZQ8tSgE8pc0sSNEuGvifrQJWiwx9F05BowihmLxeQKw==", "signatures": [{"sig": "MEQCIFT4bAnF1zLuWfsXYWX44UM3wWTcUq2tcDoukAkusIoMAiA6O4i8XlwQuSi0i2b/OBTbXP6rpPOMxzFD7ncaml1+aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2394182}, "main": "./rollup.darwin-arm64.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.13.1_1711535252477_0.2871605043768972", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "78db4d4da5b1b84c22adbe25c8a4961b3f22d3af", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-mCMlpzlBgOTdaFs83I4XRr8wNPveJiJX1RLfv4hggyIVhfB5mJfN4P8Z6yKh+oE4Luz+qq1P3kVdWrCKcMYrrA==", "signatures": [{"sig": "MEUCIQCaA1KvC1bDt46qo1t3If8sIcZ+BFZHb8OnsaogqPZIIQIgNzqs0AziUvYpwQE7GozdZL8gtDLb4yZmoBqlSIF+hF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2394150}, "main": "./rollup.darwin-arm64.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.13.2_1711635208483_0.8095794656503454", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a371bd723a5c4c4a33376da72abfc3938066842b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-BcnSPRM76/cD2gQC+rQNGBN6GStBs2pl/FpweW8JYuz5J/IEa0Fr4AtrPv766DB/6b2MZ/AfSIOSGw3nEIP8SA==", "signatures": [{"sig": "MEUCIQCqYo/ZZQWzRaLjmp5JPklN+86hAwCnrQuxVBp5t4E+hgIgTx0GoJQfjb9LOK9joutFaCbFL3rC9QmPSFOrkC+OrTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427142}, "main": "./rollup.darwin-arm64.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.14.0_1712121759760_0.9029576515312929", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "02b522ab6ccc2c504634651985ff8e657b42c055", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-+kecg3FY84WadgcuSVm6llrABOdQAEbNdnpi5X3UwWiFVhZIZvKgGrF7kmLguvxHNQy+UuRV66cLVl3S+Rkt+Q==", "signatures": [{"sig": "MEUCIFRexVEacwvw5Lm7DbyTY0wbTBOFnERaYYYl+i6216naAiEAu4DKsTp41amJeDOSQ/Xk42liok1MLAI9WbDufY5nUXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427174}, "main": "./rollup.darwin-arm64.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.14.1_1712475333170_0.1637210493706902", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "bf746c610f337b104408ec001549d825a91eca57", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-SWsr8zEUk82KSqquIMgZEg2GE5mCSfr9sE/thDROkX6pb3QQWPp8Vw8zOq2GyxZ2t0XoSIUlvHDkrf5Gmf7x3Q==", "signatures": [{"sig": "MEUCIFyNOT+HNmG8QV6UScvtyvQ3saTvIMWqenZhtNFBlzJGAiEAmbZQEThZ96xz1p+oO8xMGDTd4jHfvVxBDJZLEKVlITU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427190}, "main": "./rollup.darwin-arm64.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.14.2_1712903012829_0.30294543453594747", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c5f3fd1aa285b6d33dda6e3f3ca395f8c37fd5ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-Od4vE6f6CTT53yM1jgcLqNfItTsLt5zE46fdPaEmeFHvPs5SjZYlLpHrSiHEKR1+HdRfxuzXHjDOIxQyC3ptBA==", "signatures": [{"sig": "MEUCIESD1piULrvJz8y6R5BY69vxrIj8wMZ6VNAvVTU3WomnAiEA56johgPT0MuthVGppOGx6Psxfz4JPDRzCWeZ9XziMjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427158}, "main": "./rollup.darwin-arm64.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.14.3_1713165505383_0.08459176581486316", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "4cb44cfec3068f6f76f70463ccc25f3e245af06a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-hNkt75uFfWpRxHItCBmbS0ba70WnibJh6yz60WShSWITLlVRbkvAu1E/c7RlliPY4ajhqJd0UPZz//gNalTd4g==", "signatures": [{"sig": "MEUCICgMMohJMKFid4Vy8WzwM+Qj955X4cL63Pzz8xEx97t7AiEAmjDspf0DU1k4GEvXyrN5HAXKfJKScAIiM28+eUAa34I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476854}, "main": "./rollup.darwin-arm64.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.15.0_1713591425638_0.13544484877058394", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0b4ee897790b299243603e5c3adce2932038be08", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-UwF7tkWf0roggMRv7Vrkof7VgX9tEZIc4vbaQl0/HNX3loWlcum+0ODp1Qsd8s7XvQGT+Zboxx1qxav3vq8YDw==", "signatures": [{"sig": "MEUCIQCQziFpj9q0rIqVCpMUAf+ZqMlhpAoS2Yms6juelvhimQIgQy8ZsR6qe3u92W/0acNQ/uPF7hqqZeq6UZhvL+ylZ50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476854}, "main": "./rollup.darwin-arm64.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.16.0_1713674500729_0.8432792802473448", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8fd277b4be6cc956167710e36b4ee365f8a44050", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-QLDvPLetbqjHojTGFw9+nuSP3YY/iz2k1cep6crYlr97sS+ZJ0W43b8Z0zC00+lnFZj6JSNxiA4DjboNQMuh1A==", "signatures": [{"sig": "MEUCIB5i+f8Me6qz8Qzc00VRBJmQJyqiCRrEehCl0OHlxeahAiEA2Vyy9TDBMOVbEtzyjIYW7TbziQzXLyxIW7DFXkg75RM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476854}, "main": "./rollup.darwin-arm64.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.16.1_1713724190611_0.6717363379085382", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "be3d9fffbf6fc5b9d5f0642f1f0250e0ecab8d3e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-vOAKMqZSTbPfyPVu1jBiy+YniIQd3MG7LUnqV0dA6Q5tyhdqYtxacTHP1+S/ksKl6qCtMG1qQ0grcIgk/19JEA==", "signatures": [{"sig": "MEYCIQC9a58IbMisnanIuwBr1W8qr92s0VTkQ9vYA2znwFBm4QIhANkq5m518KMNjxgddYlYKLbZqaoptKk8nI0WhRDUIJr9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476854}, "main": "./rollup.darwin-arm64.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.16.2_1713799146472_0.7828201490378368", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "58cace5d05813809623fd5c208f1df2323495c81", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-Lj8J9WzQRvfWO4GfI+bBkIThUFV1PtI+es/YH/3cwUQ+edXu8Mre0JRJfRrAeRjPiHDPFFZaX51zfgHHEhgRAg==", "signatures": [{"sig": "MEUCIQCOEImVsZ+wX6Qu34PJOqCcqetQPDKP2pJd2RuQNnJEVAIgVSH0+0sxg6Z8wP0b8xuGcyoi0Afz54cYWbM7V9uczuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476854}, "main": "./rollup.darwin-arm64.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.16.3_1713849148408_0.06496900690506657", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b2fcee8d4806a0b1b9185ac038cc428ddedce9f4", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-i5d64MlnYBO9EkCOGe5vPR/EeDwjnKOGGdd7zKFhU5y8haKhQZTN2DgVtpODDMxUr4t2K90wTUJg7ilgND6bXw==", "signatures": [{"sig": "MEQCICkGvcI4sVHOtq3DC5YljfqCz7cF218+N41aapX0dBd3AiAgTah/NL/NrdLDwRZdX2EVL89sB1GsHawIzv//ewfVJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476854}, "main": "./rollup.darwin-arm64.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.16.4_1713878100894_0.9696379982697663", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6e829f61560dbae75f0ba94bf345d9608150703e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-Oj6Tp0unMpGTBjvNwbSRv3DopMNLu+mjBzhKTt2zLbDJ/45fB1pltr/rqrO4bE95LzuYwhYn127pop+x/pzf5w==", "signatures": [{"sig": "MEQCIFWJNYmL1/cS16e9ojvY3RKd531/ouQgSAoBlK02uzIKAiAJFxGZJG2kW+7mqeMRmJix60P3diykJ8W9veCBPcbfQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493398}, "main": "./rollup.darwin-arm64.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.17.0_1714217386173_0.2638371801341053", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "d57357c6519ae10605dd5f1881534f490fd1ae3c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-LsZXXIsN5Q460cKDT4Y+bzoPDhBmO5DTr7wP80d+2EnYlxSgkwdPfE3hbE+Fk8dtya+8092N9srjBTJ0di8RIA==", "signatures": [{"sig": "MEYCIQDv2GBhF8lh4SIaKnnaN/Jlo+ZJAI3cZz8leITrzgTUnQIhAJ2WrQgfUsOaEwgJfv2OnqVTi1dBuxiV5sl6kjQeBGwg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493398}, "main": "./rollup.darwin-arm64.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.17.1_1714366665833_0.003503054432919317", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6b66aaf003c70454c292cd5f0236ebdc6ffbdf1a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-kcMLpE6uCwls023+kknm71ug7MZOrtXo+y5p/tsg6jltpDtgQY1Eq5sGfHcQfb+lfuKwhBmEURDga9N0ol4YPw==", "signatures": [{"sig": "MEUCIEvTe7aYWW38g2NtwSLdzdb5Loyn7vNf+BLKetMqoYuYAiEAl+PTlVo4T3Vs8PNV3anXk+b1SXjjr028WPZe//X6MNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493398}, "main": "./rollup.darwin-arm64.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.17.2_1714453237094_0.7636796829917838", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b6dd74e117510dfe94541646067b0545b42ff096", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-IWfdwU7KDSm07Ty0PuA/W2JYoZ4iTj3TUQjkVsO/6U+4I1jN5lcR71ZEvRh52sDOERdnNhhHU57UITXz5jC1/w==", "signatures": [{"sig": "MEUCIGOnYViKKb8qqbgZGtT1dZjUoXd8h7v2PFKUcs1fcyVZAiEA7HIKBQn0+c5ZLPPgewZ2e3KyN+8UR9b7hA9kD7H9Tbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493494}, "main": "./rollup.darwin-arm64.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.18.0_1716354215201_0.8913697148304955", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6a530452e68a9152809ce58de1f89597632a085b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-vk+ma8iC1ebje/ahpxpnrfVQJibTMyHdWpOGZ3JpQ7Mgn/3QNHmPq7YwjZbIE7km73dH5M1e6MRRsnEBW7v5CQ==", "signatures": [{"sig": "MEUCIDJSJ5U/YeMEGvWCYpviK9HVgkYBCL+7IIYZKXOSEbuGAiEAr7FVkPqshg5WGnUR4rBW4mPAge9wjjAqDF2nFOIt++0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2344566}, "main": "./rollup.darwin-arm64.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.18.1_1720452305473_0.7230305782012332", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0a3fffea69489a24a96079af414b0be78df8abbc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-emvKHL4B15x6nlNTBMtIaC9tLPRpeA5jMvRLXVbl/W9Ie7HhkrE7KQjvgS9uxgatL1HmHWDXk5TTS4IaNJxbAA==", "signatures": [{"sig": "MEQCIGfKfbmIRUvtjhD/48+iPi03z/jnf4fVVx4dT0jn3JHkAiAUwN67siPpiBWc7Crb9MWN139OSxI9W0+OVqFe7C7MPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2328102}, "main": "./rollup.darwin-arm64.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.19.0_1721454368964_0.9417851109277655", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "e41e6a81673260ab196e0f59462b9940a6ac03cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-8o6eqeFZzVLia2hKPUZk4jdE3zW7LCcZr+MD18tXkgBBid3lssGVAYuox8x6YHoEPDdDa9ixTaStcmx88lio5Q==", "signatures": [{"sig": "MEUCIHjEnosuPLO7b1zyuLegzNO05GPkK/fakSGmPZJfPQ0qAiEA+gdHyTOWzYlLpZwQTuE6prT1cTYsGGlmxX1drCFd5zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2261014}, "main": "./rollup.darwin-arm64.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.19.1_1722056035534_0.8826193182671076", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8eac8682a34a705bb6a57eb3e739fd6bbedfabed", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-IIARRgWCNWMTeQH+kr/gFTHJccKzwEaI0YSvtqkEBPj7AshElFq89TyreKNFAGh5frLfDCbodnq+Ye3dqGKPBw==", "signatures": [{"sig": "MEUCIADnK4r9GLhrfpYWbPDyxT30NLsDjXXHFjGI8tB+Tr2DAiEApXp179z99omRDxm/QjNakgQkfT+iJAzr1UwO/0z6LmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2261014}, "main": "./rollup.darwin-arm64.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.19.2_1722501167729_0.9775063740482515", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "25f3d57b1da433097cfebc89341b355901615763", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-uFVfvzvsdGtlSLuL0ZlvPJvl6ZmrH4CBwLGEFPe7hUmf7htGAN+aXo43R/V6LATyxlKVC/m6UsLb7jbG+LG39Q==", "signatures": [{"sig": "MEUCIBunYCLJ6fDZZQDw9Zeye3Y/SM3L823eWyB6b7vGy+LAAiEA9DW7qU0DQi6Ei82djjmlV7XzYXlVXo58i0EDos4Mx0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2277590}, "main": "./rollup.darwin-arm64.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.20.0_1722660525318_0.767890379341843", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "f0a18a4fc8dc6eb1e94a51fa2adb22876f477947", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-zOnKWLgDld/svhKO5PD9ozmL6roy5OQ5T4ThvdYZLpiOhEGY+dp2NwUmxK0Ld91LrbjrvtNAE0ERBwjqhZTRAA==", "signatures": [{"sig": "MEYCIQCr/zGEX6+73s4eTIYoaU8TbC/vfF6Hm7o7MaDARkTEfQIhAPt8yppJYEmPlE/8q/v8ymcGRk0EsL3XlxznvRsL4f8v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194838}, "main": "./rollup.darwin-arm64.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.21.0_1723960529742_0.905827164527506", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "e19922f4ac1e4552a230ff8f49d5688c5c07d284", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-AH/wNWSEEHvs6t4iJ3RANxW5ZCK3fUnmf0gyMxWCesY1AlUj8jY7GC+rQE4wd3gwmZ9XDOpL0kcFnCjtN7FXlA==", "signatures": [{"sig": "MEYCIQCwuCwfoaqVz/3FebM3AKSqm2yR5lzovy/ZUzTqvDYRaQIhAKeioSriS4Nt7/y7ELt9UWmpzpmGKvvJwfgrApPFrVPz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.21.1_1724687647716_0.3298494340193694", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0a2c364e775acdf1172fe3327662eec7c46e55b1", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-99AhQ3/ZMxU7jw34Sq8brzXqWH/bMnf7ZVhvLk9QU2cOepbQSVTns6qoErJmSiAvU3InRqC2RRZ5ovh1KN0d0Q==", "signatures": [{"sig": "MEUCIFa24sgQzqxMw6vaowDVwjX16gYE+0WNWGrtj3rYRRjWAiEAwFMBgu7jg+kSYRc6lpvzU2hUeMgSYtDOKevhXmgyUQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211366}, "main": "./rollup.darwin-arm64.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.21.2_1725001460739_0.8434381193958209", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0934126cf9cbeadfe0eb7471ab5d1517e8cd8dcc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-P0UxIOrKNBFTQaXTxOH4RxuEBVCgEA5UTNV6Yz7z9QHnUJ7eLX9reOd/NYMO3+XZO2cco19mXTxDMXxit4R/eQ==", "signatures": [{"sig": "MEQCIAtSWI7qfW5457b2PveX23B3pW2fopTXyLqye3i4dugmAiAO6THY2TNqTx/n8OfopOzbkH1neQl6fFiHquZe2lmBbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178342}, "main": "./rollup.darwin-arm64.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.21.3_1726124743964_0.21248159209716677", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6122dc37d4a09521d8abe18925956d3b46cfbac9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-ZWgARzhSKE+gVUX7QWaECoRQsPwaD8ZR0Oxb3aUpzdErTvlEadfQpORPXkKSdKbFci9v8MJfkTtoEHnnW9Ulng==", "signatures": [{"sig": "MEUCIQCUu89b5H0x+6sFL39ukBPkMdu4Od5P+XB3amgVjMtqjAIgRImwZnHUm4HTu6lCD/a7i4p35Gg/2mEIqqP7WanKKXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178406}, "main": "./rollup.darwin-arm64.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.0_1726721726220_0.6309657693581447", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2552cfad088ed76a5042f626001030929da23e13", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-IwEyoeCZoO1lpY5Er5t3UK/Aq5q2W/ubLmu3pYW4as0htn4NbJagBaVNr1aVhRTXUxrYvcPhxQCqodShnocLdA==", "signatures": [{"sig": "MEYCIQDSkKT3/STytla7gX/BSs2dX+2uOaZec5H3k90JsNxnMwIhAJlIuV82BrTvVpeABX7CAJuezl6g0qU2vqiShMuTFyah", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178438}, "main": "./rollup.darwin-arm64.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.1_1726820508243_0.1224534032674316", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "06dec35316de9fe433d66c849ecc056e221ba422", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-BTHO7rR+LC67OP7I8N8GvdvnQqzFujJYWo7qCQ8fGdQcb8Gn6EQY+K1P+daQLnDCuWKbZ+gHAQZuKiQkXkqIYg==", "signatures": [{"sig": "MEUCIQCRQspwv+4qNqiUS5uGl77t1YmaL4hF9xQnucRLYMjVHwIgCM9jelYMEobxLlMDoO7xe8TFu9sM+cCrjecH74J+IBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178438}, "main": "./rollup.darwin-arm64.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.2_1726824819974_0.4017545608625759", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5715c6b939a50cfa77f40c58e788ccb5a7083b66", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-AP+8K4U02AsSjB+rdSEzJVV2LsUdUok67iH7otM99I9pVWTAE875fVRxwVjerQDuYvx53w31AEO91vA5fTCRAg==", "signatures": [{"sig": "MEUCIQCrEjwWcKjihsBrkaTr8q13OiLexl4WfmGPek6yNj4SNwIgDT3KNNmU2KsoyvSERSEMvmLqBIr2er6TGF80gRzIeJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178440}, "main": "./rollup.darwin-arm64.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.3-0_1726843672082_0.1332364114095128", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8b26796451e45924f479a7546e6d0b586d0bf79c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-3o4PxCB4dQyFXanB/rfaLk37mX/ZKo8tIwULy37H8NLmcQOoH8reWSG2qVuyEKjLqAo14hVPTsRIXxjqciuMgQ==", "signatures": [{"sig": "MEUCIBP34xRAQfZfcgUlAEQ4avqRl4xSQhcz0blsGbF7iJKXAiEA3Drm6FsU1ugU6dGfVtxBoBpfsMh3UyyRiS9eQ7BmWBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178438}, "main": "./rollup.darwin-arm64.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.3_1726894982958_0.9291104043595215", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6d241d099d1518ef0c2205d96b3fa52e0fe1954b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-xMM9ORBqu81jyMKCDP+SZDhnX2QEVQzTcC6G18KlTQEzWK8r/oNZtKuZaCcHhnsa6fEeOBionoyl5JsAbE/36Q==", "signatures": [{"sig": "MEQCIGfFq/ziZA4t7+a5wZL8lHTUoHO741zUx4l71fTFZebeAiAseQCVzyjQWN3iRJ9h4CdfFgDCeiDhrDvM3gaN30Afsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178438}, "main": "./rollup.darwin-arm64.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.4_1726899077265_0.4652874685962878", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-darwin-arm64", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "691671133b350661328d42c8dbdedd56dfb97dfd", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-250ZGg4ipTL0TGvLlfACkIxS9+KLtIbn7BCZjsZj88zSg2Lvu3Xdw6dhAhfe/FjjXPVNCtcSp+WZjVsD3a/Zlw==", "signatures": [{"sig": "MEYCIQDpiYPNPdtSjjSc//SqbN3K502wEA4M3lSb8wrF7/M8ygIhANpbu6cUeQwCliHLxsgIJmRs+c/rr3Qf4Z8ikF/9/Pda", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178374}, "main": "./rollup.darwin-arm64.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.22.5_1727437689973_0.9060660499944033", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "1bc123d4e69920d026f0ffc791bc3c4e04a33b60", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-ZbqlMkJRMMPeapfaU4drYHns7Q5MIxjM/QeOO62qQZGPh9XWziap+NF9fsqPHT0KzEL6HaPspC7sOwpgyA3J9g==", "signatures": [{"sig": "MEYCIQD9+uH7AWct8h7BS4mGB6wG86ANwmAKDprSQDR74p4+ZwIhAIO0NDNfUUSYyoOp2AHctenLmY9mqA+lD5ZBfZHw4F3F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178374}, "main": "./rollup.darwin-arm64.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.23.0_1727766600321_0.08344035004335026", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "627007221b24b8cc3063703eee0b9177edf49c1f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-bIv+X9xeSs1XCk6DVvkO+S/z8/2AMt/2lMqdQbMrmVpgFvXlmde9mLcbQpztXm1tajC3raFDqegsH18HQPMYtA==", "signatures": [{"sig": "MEYCIQDe7unE7VZdokfyygc4RtRIO6NgW0LwROVZVXhUs8P0JgIhAIroFaWuALvtuf/lLroYU8trxbv4zKND2nJXiaVV/LLs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2178390}, "main": "./rollup.darwin-arm64.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.24.0_1727861834054_0.46532671024526673", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6e7583e26dd9aff0c605486de30dfa3002a0afb3", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-K9iOc75U9HpDffjop9qVPwNoBEPXS0Q6RrVSvh13gs38ynurJ2+HuS7NJbsx+fwiDA+eJYfBi7sablI8G2/3oA==", "signatures": [{"sig": "MEUCIQCOvVT/YKWRQI2bUOedu7alsY9ndWRUTu4d9wRv2IrS1wIgTfkhdQzHAcOxrFYJUWCPCcNpSr+JkMHMXk0zFD/kVY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194854}, "main": "./rollup.darwin-arm64.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.24.1_1730011376542_0.47573786995010847", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2b126f0aa4349694fe2941bcbcc4b0982b7f1a49", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-/UhrIxobHYCBfhi5paTkUDQ0w+jckjRZDZ1kcBL132WeHZQ6+S5v9jQPVGLVrLbNUebdIRpIt00lQ+4Z7ys4Rg==", "signatures": [{"sig": "MEUCIQCrzSHqAqX7zecKbnwinGZmzobOMTAv6tm/Gt6XEeBt+wIgZTOPJ9X9kmj80PHJuKgyklB2NF0RHCSJoPjgezTtCSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194854}, "main": "./rollup.darwin-arm64.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.24.2_1730043601963_0.6483396532729482", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "882d8d27c431b8db23cb8f9c3162b25b600e86cb", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-6erqfwNWDy8Kly47WmAaPjl0D6Fh26l+VBkvVl4XchSr1SHYO/Y2Rzd/is9fzFa/oLtim7XtuPpH0EYjO5gnVA==", "signatures": [{"sig": "MEUCIA0yunNs8bDNRvyjb9K3ZKwX01nGrQB8fpsTjRaxvrGGAiEA8+0R1Du3xASdGbqNhCst/KDc6Sr//21qyyynnYvertw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194856}, "main": "./rollup.darwin-arm64.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.25.0-0_1730182504217_0.037863477553603975", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "16772c0309d0dc3cca716580cdac7a1c560ddf46", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-QPW2YmkWLlvqmOa2OwrfqLJqkHm7kJCIMq9kOz40Zo9Ipi40kf9ONG5Sz76zszrmIZZ4hgRIkez69YnTHgEz1w==", "signatures": [{"sig": "MEUCIEwP0/ZiRNLtJVDL3y150JnljU0E+av1eurSwFR4sANqAiEAkiW9R0vOaRpoX9HTKVIXIYb0PImq/g+rb+Hs/XKM4ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194854}, "main": "./rollup.darwin-arm64.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.24.3_1730211241295_0.08065877715876413", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "9b2ec23b17b47cbb2f771b81f86ede3ac6730bce", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-GmU/QgGtBTeraKyldC7cDVVvAJEOr3dFLKneez/n7BvX57UdhOqDsVwzU7UOnYA7AAOt+Xb26lk79PldDHgMIQ==", "signatures": [{"sig": "MEUCIQCszA3Ixakg0jUVMO/aloSG+mCotuTgxj4nbsG4XvACSwIgezWVV9RY6d6luFLeqozzUa7QWIJMq8P1zyb2dcFDs/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.24.4_1730710023538_0.28764968323198215", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "ecea723041621747d0772af93b54752edf26467a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-YVT6L3UrKTlC0FpCZd0MGA7NVdp7YNaEqkENbWQ7AOVOqd/7VzyHpgIpc1mIaxRAo1ZsJRH45fq8j4N63I/vvg==", "signatures": [{"sig": "MEYCIQCbUz8R6N0mTjnXQwADTmGomV3WqwC3QcQNkhf65Ie78gIhANx/JJqItkf3Ai8XnX/x8hcIuIEKtlfoGMu2DFJqSEtK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.25.0_1731141437103_0.8656969750075378", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0c83e5f25adae7f0543ac29a0ebd485a0e7cd3e4", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-ErTASs8YKbqTBoPLp/kA1B1Um5YSom8QAc4rKhg7b9tyyVqDBlQxy7Bf2wW7yIlPGPg2UODDQcbkTlruPzDosw==", "signatures": [{"sig": "MEUCIEVlXXh2EgXpYzf+ZEkKJWE1Z7GHJNCk/zN6aheOU39EAiEArBFfSdJuB+k5UjgZuTj5Wtjnnh2E3wk/6aDF0gDY2XQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.26.0_1731480294482_0.37884487465041206", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "56c3c62a7aa58abfb38f0a6bf3dcc14d39280811", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-w4bYU9N4/ZlFnx4+rU5mhmhJdBR08uv7CjWbcgwBdbA/m9rqqviLijGE+E3vC4HAlkmWomljZV2KXf7MvR6mEg==", "signatures": [{"sig": "MEQCIFVNhBczW/+l9s5D7BrgqLbdDZ1aUNJ276lMgBOjACw5AiBzTSPUHk6+Zu2PrsHw8OmuMllONd188vAerLYjHTArkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194872}, "main": "./rollup.darwin-arm64.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.0-0_1731481389023_0.1336459344818306", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c16e9d8a5ff2f59dea9bd62ff0d908c28e0885f7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-J5GkuFgKPV7DSJX+weEIzBLfE8d/XwxYqtK94TaadKnXCjxY8y3MgU4eKSBlGQJASHoZn+N+wqCxP/eP4GAVgw==", "signatures": [{"sig": "MEUCIGFNgJlUny4fZ+rsAZ5rQER+nBmOsRB6Db69zdXoxfiEAiEAu+PfZXDsxPohHKYYqylFEgBCwLc+vqL8Bq3S1widuLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194872}, "main": "./rollup.darwin-arm64.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.0-1_1731565985765_0.21538281631294032", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b58c4386f2453856c3b516f7d1152681ac95a45e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-aauK2M2ptFQQYdOqbKGYCg1LHlPbm6IxepSnHLLaMIGcd9YBiKRGl2+KtzQL/IkurP+b54EKBkvtZaWXijmzfQ==", "signatures": [{"sig": "MEQCIC8t5p74D9QYJ/Nai0NkDuzAlJ+8eSvPBz0A4uRFrUqkAiA6aC7XjiGGANDzKibWfnwhIlujYo/CSV1luFe6uNYI2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211398}, "main": "./rollup.darwin-arm64.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.0_1731667231092_0.5678780291369219", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "f9de8368fad76ff864fa2a257eac9f08fe587701", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-ER7NJhqgkJkZD+tvdRdxA9jrjP5RtlkYP4qu6vBnJBb0ui4hOHq9rQ/PlswAeq94kgjlO0c9BwX+ryiC+zqoaQ==", "signatures": [{"sig": "MEUCIBYVUZLyDrGUBTiNp8XwEzts/SFr/+fNsIHwHZ4w09iDAiEA1uA1RpeqIZ87yC4wbjGH7GVXZqWKACST+kQfAUjSYp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211400}, "main": "./rollup.darwin-arm64.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.1-0_1731677282342_0.7126395060541129", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8b77528a0296e2c391c8f4c83f5f52abbf387c5f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-LCjVm1uK16Eki9Kdb4PmstRgUVYO7pp36srIP8uoYPKLFH1juohjKdBzPnu85p+jsgfep9OJz4jkAPkx3+jNlg==", "signatures": [{"sig": "MEYCIQDoLucPijv9rSUehXcbZFKbMRQI5l8MKdXPmlqJWG5lZQIhAI01DXAd43cORHplL8IeO4q3CpNIRkByq9XuwdAM2MPM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211400}, "main": "./rollup.darwin-arm64.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.1-1_1731685077068_0.09312284800963799", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "92ae955a484dcb4f228c37aa117d5176b9a9680d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-FkEfMuiAA+utxcauRC23iw3zT/wioQjYv9eBGMVvbNqzOLa4IPHED2qIgPh/W6nrHDTH4AO47T8JvqTDV+aFWA==", "signatures": [{"sig": "MEUCIAW/R+uUsNqPINZWgHRCeVkC8jew+2drEGtlKBeXefePAiEAmomCdKb2YON7Yc5sFJDHQp1RZoHxbWsTq+mcgQ3ZVsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211398}, "main": "./rollup.darwin-arm64.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.1_1731686856354_0.343984260307425", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "03c63fd652bd4d522f366b1550526fd8c09c9dd8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-KnXU4m9MywuZFedL35Z3PuwiTSn/yqRIhrEA9j+7OSkji39NzVkgxuxTYg5F8ryGysq4iFADaU5osSizMXhU2A==", "signatures": [{"sig": "MEUCIQDpmgSorz2lnG11BwYHkJ+AzIYLo0S7HWYkw1RZ/y/EFQIgWI2t4NjaZ7XSDoRQeph91FlnuAKvya1tMAwANEiJf2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211398}, "main": "./rollup.darwin-arm64.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.2_1731691197465_0.5708407083278393", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8c786e388f7eff0d830151a9d8fbf04c031bb07f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-OuRysZ1Mt7wpWJ+aYKblVbJWtVn3Cy52h8nLuNSzTqSesYw1EuN6wKp5NW/4eSre3mp12gqFRXOKTcN3AI3LqA==", "signatures": [{"sig": "MEUCIAHiIAXifVz0Kk0+Vr/4QwTfMVuh5CSmFQLM1Nr234hbAiEAhfNvqwAfes0DousBvBnSpmZGs6GkBnL4P/05WOgFQsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211398}, "main": "./rollup.darwin-arm64.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.3_1731947971036_0.4083808971285763", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "77c29b4f9c430c1624f1a6835f2a7e82be3d16f2", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-PlNiRQapift4LNS8DPUHuDX/IdXiLjf8mc5vdEmUR0fF/pyy2qWwzdLjB+iZquGr8LuN4LnUoSEvKRwjSVYz3Q==", "signatures": [{"sig": "MEYCIQCXRr2VK4M9RkGfJe9pRS/hK9rlKuJo5J4haLYgQRTGlAIhAMTA6ahic/7ykaQrjehsH1FbIA0g+dumSm7nEaABP17a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194886}, "main": "./rollup.darwin-arm64.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.27.4_1732345215921_0.8809147619720916", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5b783af714f434f1e66e3cdfa3817e0b99216d84", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-lmKx9yHsppblnLQZOGxdO66gT77bvdBtr/0P+TPOseowE7D9AJoBw8ZDULRasXRWf1Z86/gcOdpBrV6VDUY36Q==", "signatures": [{"sig": "MEYCIQCEOm58kjPVQVM8G4l4Vuq3v/yx/xcCyvQw9yBsSon51AIhALPQ93mZpdOkCDWjn6mmsawSY7Jqtv0rNStvdGkeSViO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211430}, "main": "./rollup.darwin-arm64.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.28.0_1732972542346_0.08264452896861196", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "dac0f0d0cfa73e7d5225ae6d303c13c8979e7999", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-prduvrMKU6NzMq6nxzQw445zXgaDBbMQvmKSJaxpaZ5R1QDM8w+eGxo6Y/jhT/cLoCvnZI42oEqf9KQNYz1fqQ==", "signatures": [{"sig": "MEQCIGw+yQjCQrHRpfMrN5wXvdn0vxNHgMWAGcT4qkj8bgZKAiAuOfCMkBmeVU/QsC+4xXAOwkoZ74YsorehzNE+1J/54Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194886}, "main": "./rollup.darwin-arm64.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.28.1_1733485491653_0.7484579542050609", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6bc3eebf833c70a2fb01e6e00dfa0a87cc097945", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-Kt1VWicrwrtrgQldWkzBZSRysMrOuZNKPEl8y7UHqh3UpUKKE9NB48AWYLZmi3mBbhQ7gUIVqKs+lpHqnuZ16g==", "signatures": [{"sig": "MEUCIQC/nQXcHD5OZYzoCIUu5J9fLcekVAmLrZy4wOyYLWqJOQIgestiQ/fpx8QP4Yq+NpxwURR/+6nvdyv5LDvgdFI9YuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194904}, "main": "./rollup.darwin-arm64.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.29.0-0_1734331188594_0.36392027056869414", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "d9b4dee0f71e238071306217cec0e5eb288813ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-4j2NphJrXAfbgNIQaD9KTkgSN831mE8Tot2ipVVieKOOnVwB2urZxPQyGJadz6PzdC0wSlvH6OUnJ25J4aVINQ==", "signatures": [{"sig": "MEUCICApjZGZ2+1v9fWdJT6dbDD65C0BnA60xrRQ7yubeONHAiEAq+rg1U74pPrhUBge0Lqj6EZpt7ANWPNX+gT9e/988Jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194904}, "main": "./rollup.darwin-arm64.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-dar<PERSON>-arm64_4.29.0-1_1734590247099_0.24619270132726112", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "529a70b7838137a4e4e8ce13d31bad40e58e368f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-RNnnfJNeb8vdXk7cdJ0C06oez7q57akoIe6KiPUt6uMZv4h3UtUiNpYO2pFYBSTqK1xbpF2FqaSwkzTxFJdJNQ==", "signatures": [{"sig": "MEUCIHLSQDjE0d+MNQvDkQXPFCQY/51eddoZXBSUf8cICbghAiEAp+UzZwk27Fz75rESKNOzxTk/wQlnqFt103paLp4J9Bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194904}, "main": "./rollup.darwin-arm64.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.29.0-2_1734677757144_0.07215246299072797", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "35161632b695bfcc70ca2dbbe219e8e13b9627c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-I1ZucWPVS96hjAsMSJiGosHTqMulMynrmTN+Xde5OsLcU5SjE0xylBmQ/DbB2psJ+HasINrJYz8HQpojtAw2eA==", "signatures": [{"sig": "MEQCIADKg2j55gZc8Y5VfoP7Wx5z4qVmpsG3J9THv65YDne1AiAYyne9G3R9b3wzaJzSzOg4cNta7UTb2d6XHCq7IbSZYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211414}, "main": "./rollup.darwin-arm64.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.29.0_1734719839926_0.34790367567247205", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "bc6fa8a2cc77b5f367424e5e994e3537524e6879", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-2ORr7T31Y0Mnk6qNuwtyNmy14MunTAMx06VAPI6/Ju52W10zk1i7i5U3vlDRWjhOI5quBcrvhkCHyF76bI7kEw==", "signatures": [{"sig": "MEUCICh9L1w8qfhfZ99TdY/eV5V8uddv2JwSjK+llWfdbYxVAiEA9dDN/3ki2G1N8CBP0dZRnTWfLOO/U+wGnj1AH7eWpm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211414}, "main": "./rollup.darwin-arm64.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.29.1_1734765357477_0.6144688220795258", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "25a4986255c366a1bd4fd41e50d499ef08ccce2b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-E6FxLpjWa9bXY9TJH19lROUGVxwBumg2m8bJ3cacqYB2wNl7sM2bCjX65ZXnBp14sK20Y07Tus74WLoFvgIYrg==", "signatures": [{"sig": "MEUCIQC4AywM35fTccLCbbooulS2lYqByGPvH0jTd/OP8R0aGwIgBJuvtbn4DBJb6Y8GRjcBlm6x+FyWvuuzFJO//P1pBGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211416}, "main": "./rollup.darwin-arm64.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.30.0-0_1734765429575_0.5304971984636986", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5cd814c6a43539377a7a4902aec2443a4ed56070", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-Rlp3TCWPspp1YI0j5sOtQyqNkznRQrSm1W51Q/NwqzBa4azWFNXW3MIMm/i+EoNtgi92yBU0uVkXVp3yvAdUNw==", "signatures": [{"sig": "MEYCIQD8bzZuNS6cAuQZ8LAb2wX4n9KQr1Gn1+kfKOzCxqa3WAIhAJGPFOg7jaucw4+tjAS8CaqE+qdNf1LFytiEPrL7OO12", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194872}, "main": "./rollup.darwin-arm64.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.30.0-1_1735541531904_0.9752267036167954", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "d084c6120f96749a7ddc5ef81d8740f2525abb6e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-vJX+vennGwygmutk7N333lvQ/yKVAHnGoBS2xMRQgXWW8tvn46YWuTDOpKroSPR9BEW0Gqdga2DHqz8Pwk6X5w==", "signatures": [{"sig": "MEUCIQDZvOUiy88J8TJRDRZtafVWj4lYfKKxGIVwOdum/ggahAIgezk63Xy/uvllG2rsjlkF7pgjxH5LQiDeEIiubfOcCv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.29.2_1736078857832_0.7669971251807703", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c9245577f673802f0f6de0d46ee776691d77552e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-617pd92LhdA9+wpixnzsyhVft3szYiN16aNUMzVkf2N+yAk8UXY226Bfp36LvxYTUt7MO/ycqGFjQgJ0wlMaWQ==", "signatures": [{"sig": "MEYCIQDfw+IWQARYu19tXk1JsITMyIEO8/841d7Ea2eqxJp2WgIhAN/7NZd7hDF/xMT7542KOwgUDPSufX8lpFFCZPKn4why", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.30.0_1736145396812_0.6279748990806266", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "29448cb1370cf678b50743d2e392be18470abc23", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-r7FQIXD7gB0WJ5mokTUgUWPl0eYIH0wnxqeSAhuIwvnnpjdVB8cRRClyKLQr7lgzjctkbp5KmswWszlwYln03Q==", "signatures": [{"sig": "MEQCICzNqKIoVO83uK8S5ObTxgnGz8ijsiR8VLmYeCew4p75AiBsTYg5iiLufrQE0+R/4F4+6YSaP4I/S8wAOLF/875AXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194870}, "main": "./rollup.darwin-arm64.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.30.1_1736246147837_0.982123481246538", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "25cf17c0554e864a0202ba79541195e6e5765deb", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-S7q2ljTmslNEwEZAma6w3A3unIZ0Z52+in5RtCk6IRLFfXGSjLY785gOhbjOWDkhXKGWpwLd+FVAHmHJZhDmlg==", "signatures": [{"sig": "MEYCIQDAGjWWeakIc3bOj6p7HNw+QLvX4vIEBBX0r3rdrfKKTQIhAN2J5Tbjy/ZJVwLQGP2vTecHithlRV/mUG5Raa6NYkIb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211520}, "main": "./rollup.darwin-arm64.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.31.0-0_1736834256845_0.4683206421652193", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "d137dff254b19163a6b52ac083a71cd055dae844", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-WHIZfXgVBX30SWuTMhlHPXTyN20AXrLH4TEeH/D0Bolvx9PjgZnn4H677PlSGvU6MKNsjCQJYczkpvBbrBnG6g==", "signatures": [{"sig": "MEQCIA3LAy+PDx5w5Q5Ry3K1R2UXCYLHE2XTHXw7gM5VFC8FAiAj6NVZSFY1plvFqbOR+I719yXhQNgci9Q87nVOGg58Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2228222}, "main": "./rollup.darwin-arm64.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.31.0_1737291402395_0.23193198784165303", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "014ed37f1f7809fdf3442a6b689d3a074a844058", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-44n/X3lAlWsEY6vF8CzgCx+LQaoqWGN7TzUfbJDiTIOjJm4+L2Yq+r5a8ytQRGyPqgJDs3Rgyo8eVL7n9iW6AQ==", "signatures": [{"sig": "MEUCIApsKvxiJaik06BIRv7fhaJMmUtvgXLen2/jYQjC5OMMAiEAjkBJ34ipB5jgCQVD6c0LC0YxTvO92yl5nxepXU3S+d4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2228190}, "main": "./rollup.darwin-arm64.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.32.0_1737707250857_0.6867846505994992", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "1f0e77be819d75c9f1a7e953cb74687bfbce692a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-W<PERSON>ZJmUCL8XDF3LJfdr14dfAqhpuhwOmirrNas7tXyTQgS3iFqXP7wwyAjgFTIsx67QqyHMwlSeeFctaxkHMc+A==", "signatures": [{"sig": "MEUCIQDVcENVecbQCRYncsyNW4O2soa7/FNJ4Ex7ZYJPC5wkXwIgGhEksQA1n+lJ9qRyKPdkgFOaqcJVc5Fux/hSYWeVdEI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2228192}, "main": "./rollup.darwin-arm64.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.33.0-0_1738053004048_0.2692988429167842", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "78e5358d4a2a08c090f75dd87fa2eada42eca1e5", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-zCpKHioQ9KgZToFp5Wvz6zaWbMzYQ2LJHQ+QixDKq52KKrF65ueu6Af4hLlLWHjX1Wf/0G5kSJM9PySW9IrvHA==", "signatures": [{"sig": "MEQCIG3/FEdMPkQckvekURYQ0ZA9/aMiFHjAmg+nLxIUZh+NAiBN1OjWQSPVLgZDGL2ABEadcy7rlkh4hhw0pin72PnpUw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2228190}, "main": "./rollup.darwin-arm64.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.32.1_1738053193098_0.44311888613677164", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "862e3bcca88595cbb06853e285b8abd7fc05fcc5", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-0gR69dCAYCUk5e6jgbekfNBB7ZPLAR3R2VSXL3vyL/c8YlyEg2iq3ol+O3XHS5enKCN/sUsBcBAjSE6sLIXtug==", "signatures": [{"sig": "MEYCIQDw1Nsoy+SRwDESXzTY2xzoIXFctGw6bFa7EkSKykjgxgIhAIHCfFV0Y4XZ05WbJYdURZ2obD364pttbAXfEhZCb0Hl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211486}, "main": "./rollup.darwin-arm64.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.33.0_1738393914300_0.3444688748962281", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "7c6c254d1eb8b9348f8b927166312a5714678483", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-gCs0ErAZ9s0Osejpc3qahTsqIPUDjSKIyxK/0BGKvL+Tn0n3Kwvj8BrCv7Y5sR1Ypz1K2qz9Ny0VvkVyoXBVUQ==", "signatures": [{"sig": "MEYCIQD6CccYU+j8xzFZ/gwhhuKAA3g8G3Ipr7ZFidWrQHJnOQIhALqJewrIFQWE0KyYdHtF+ydA7VlidrXdD8vwFIZstTBq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211486}, "main": "./rollup.darwin-arm64.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.0_1738399218967_0.22903195202876847", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "7f5127bca8892d0287a53fb17be9c7af32748234", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-f2AJ7Qwx9z25hikXvg+asco8Sfuc5NCLg8rmqQBIOUoWys5sb/ZX9RkMZDPdnnDevXAMJA5AWLnRBmgdXGEUiA==", "signatures": [{"sig": "MEYCIQCI1LXfTpEcNDryNnBSxPay0vfwMAVx5d/MPg0TUXRJmAIhAKWgUImbew1vfeyYQiovNn3qTSzK5fGlmCCvzMjcMFUR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211486}, "main": "./rollup.darwin-arm64.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.1_1738565889142_0.4327650458160548", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "55507d6a92985abadf2868cfcbd77297e989b8e6", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-PSN58XG/V/tzqDb9kDGutUruycgylMlUE59f40ny6QIRNsTEIZsrNQTJKUN2keMMSmlzgunMFqyaGLmly39sug==", "signatures": [{"sig": "MEQCICcrWqqMPnfb0HUpL2bqDs/9UdVgEkrU85w1j3zDAp8iAiAvnmUJuT5AQWzu/R00IejpPAP48NjG1PSi99TKG+JQ2A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211486}, "main": "./rollup.darwin-arm64.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.2_1738656596468_0.8450007911344681", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "150c4cfacd11ca3fe2904a25bcfd3f948aa8fd39", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-fqbrykX4mGV3DlCDXhF4OaMGcchd2tmLYxVt3On5oOZWVDFfdEoYAV2alzNChl8OzNaeMAGqm1f7gk7eIw/uDg==", "signatures": [{"sig": "MEQCIGUzs4fth00WxWHgxWPK2mxODnOI7xhJNnI+O49eEhmqAiAS2n3AhWKDjSK5OeiqxvigzzsBHbmwwVtIPudR2yL7hw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211486}, "main": "./rollup.darwin-arm64.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.3_1738747319597_0.35693627257009153", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a8a1fbc658f86d2d2283d6b1b5b8f0c2fd117dbc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-drHl+4qhFj+PV/jrQ78p9ch6A0MfNVZScl/nBps5a7u01aGf/GuBRrHnRegA9bP222CBDfjYbFdjkIJ/FurvSQ==", "signatures": [{"sig": "MEQCIEOUc/Akyg9BUvkAw8ADK/uXxyOlgZjmtX+Se3yEQwpvAiAswNFwOF9gx/8H/WANoxO3LW93sZS1YPdFsYvG5MXMdg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211486}, "main": "./rollup.darwin-arm64.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.4_1738791066395_0.12142601651992058", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "76a2ed9d6c11d9a26ffbe7081e080c5a4e2e77d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-b9oCfgHKfc1AJEQ5sEpE8Kf6s7aeygj5bZAsl1hTpZc1V9cfZASFSXzzNj7o/BQNPbjmVkVxpCCLRhBfLXhJ5g==", "signatures": [{"sig": "MEQCIF+ZiijGfNQPtQEO6JP2+xIzN2Hd/moIb3/RRNwOG661AiBgLDqJfqdlSjthw6OIpxqYMBSO7G333aHBhzJeKxnYcw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2228094}, "main": "./rollup.darwin-arm64.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.5_1738918376636_0.09165241388854573", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b8fbcc9389bc6fad3334a1d16dbeaaa5637c5772", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-z9Ib+OzqN3DZEjX7PDQMHEhtF+t6Mi2z/ueChQPLS/qUMKY7Ybn5A2ggFoKRNRh1q1T03YTQfBTQCJZiepESAg==", "signatures": [{"sig": "MEUCIHB8Je1N6YGH9SubEzuQdGIlQ99Q8EBafIc25lygefePAiEArAkd8UhSi5fLBTG6pYuUEKyHQR1+qocWmYapDdxno1A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211438}, "main": "./rollup.darwin-arm64.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.6_1738945922779_0.8086305281377504", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "bfdce3e07a345dd1bd628f3b796050f39629d7f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-jq87CjmgL9YIKvs8ybtIC98s/M3HdbqXhllcy9EdLV0yMg1DpxES2gr65nNy7ObNo/vZ/MrOTxt0bE5LinL6mA==", "signatures": [{"sig": "MEUCIQDBn6bNuJVNVb4E+KxWE4Wsrjs1YOIp5f4jzWnWWY683gIgI90FxHutb5LzmIFyfZM8Y2MeMzWNrTTHGdmfoBjoNqY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211406}, "main": "./rollup.darwin-arm64.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.7_1739526834635_0.6956663997400736", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a7aab77d44be3c44a20f946e10160f84e5450e7f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-02rVdZ5tgdUNRxIUrFdcMBZQoaPMrxtwSb+/hOfBdqkatYHR3lZ2A2EGyHq2sGOd0Owk80oV3snlDASC24He3Q==", "signatures": [{"sig": "MEUCIEUGFtD+iFOF6p5342CQsskCy0wuZxAWFBtOMOkK5h6vAiEA9q20mT3GrM6AQhs+QkEAIjhJJwSoB/hf7eQYmhihly0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2211406}, "main": "./rollup.darwin-arm64.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.8_1739773580442_0.8535380861645501", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-darwin-arm64", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "363467bc49fd0b1e17075798ac8e9ad1e1e29535", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-0CY3/K54slrzLDjOA7TOjN1NuLKERBgk9nY5V34mhmuu673YNb+7ghaDUs6N0ujXR7fz5XaS5Aa6d2TNxZd0OQ==", "signatures": [{"sig": "MEUCIGYYzINArWXOtJ2BUsHc+selu7vi2hy+Vayo/YwZYmbQAiEAo69Fro2UV+iyNv8mHWnzyjKwD70DTR8B7IOoJP9tmz4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2327054}, "main": "./rollup.darwin-arm64.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.34.9_1740814351721_0.09527558001611514", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6da5a1ddc4f11d4a7ae85ab443824cb6bf614e30", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-Uk+GjOJR6CY844/q6r5DR/6lkPFOw0hjfOIzVx22THJXMxktXG6CbejseJFznU8vHcEBLpiXKY3/6xc+cBm65Q==", "signatures": [{"sig": "MEUCIQDrPRefE8mFZ/e0hzHKazFOwlQCcgHPNqo/BFZYZKA0ZwIgbQdCSiU7oQPTW3cmDjO0SaolZmHIchJEOfayJRPpfig=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2343598}, "main": "./rollup.darwin-arm64.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.35.0_1741415078885_0.2617992619957985", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "82601b8ff81f3dbaef28017aa3d0e9709edc99c0", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-JQ1Jk5G4bGrD4pWJQzWsD8I1n1mgPXq33+/vP4sk8j/z/C2siRuxZtaUA7yMTf71TCZTZl/4e1bfzwUmFb3+rw==", "signatures": [{"sig": "MEUCIGNVM8zTgXRyTNFHnGCK1Knfwu/gbMhty1+mbo9j7fgjAiEA318UvdS9d+Q7TZRa3LuaUPUK3bOY3p1MCULK1a68h1Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2343614}, "main": "./rollup.darwin-arm64.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.36.0_1742200537776_0.668341629895502", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "737a7b8be9ff79bd24a7efaae0903e8c66ac0676", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-+iTQ5YHuGmPt10NTzEyMPbayiNTcOZDWsbxZYR1ZnmLnZxG17ivrPSWFO9j6GalY0+gV3Jtwrrs12DBscxnlYA==", "signatures": [{"sig": "MEYCIQCQiDLvqIE6Rnsrs1xdBmvDWH8tsL7iwACZk1QqCLSM1QIhAL8MZwZAs4eOY6mdKGp3iM2BS3FCatrY35ibC+arYwRf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2343582}, "main": "./rollup.darwin-arm64.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.37.0_1742741822286_0.5964781036192117", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c4654989b97bba0de7205cf5b3342984d4451d5a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-buA17AYXlW9Rn091sWMq1xGUvWQFOH4N1rqUxGJtEQzhChxWjldGCCup7r/wUnaI6Au8sKXpoh0xg58a7cgcpg==", "signatures": [{"sig": "MEQCICGV8FGmAkz3AjWPy6xouO60F8DK8t1TEmVkAytJE0uxAiB3N5dgN3TErGDaeJcD1CHhaQoAXPQZhDz9bLCs3lw4MA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2343678}, "main": "./rollup.darwin-arm64.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.38.0_1743229739724_0.5653931224790298", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "830d07794d6a407c12b484b8cf71affd4d3800a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-lXQnhpFDOKDXiGxsU9/l8UEGGM65comrQuZ+lDcGUx+9YQ9dKpF3rSEGepyeR5AHZ0b5RgiligsBhWZfSSQh8Q==", "signatures": [{"sig": "MEYCIQDrTdvGGBT/Tet7QLylv2MripV2/HpFk8Krn+tmB5AJyQIhAOQHOec62F5Fj7NLTjem7YbRk5dgagUxR+o9ds+5FdKi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2343678}, "main": "./rollup.darwin-arm64.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.39.0_1743569366521_0.9399941639514566", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "ef439182c739b20b3c4398cfc03e3c1249ac8903", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-GwYOcOakYHdfnjjKwqpTGgn5a6cUX7+Ra2HeNj/GdXvO2VJOOXCiYYlRFU4CubFM67EhbmzLOmACKEfvp3J1kQ==", "signatures": [{"sig": "MEUCIQCPmpBV5tNosez6QhTmHahzN5Gp6EljyyH8Lagep20JNAIgEIDTbPVEN9a4PPjfk2vzk87pUUZLtgOuS1kOLiq+ulQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2376894}, "main": "./rollup.darwin-arm64.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.40.0_1744447169965_0.7608553647970113", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "58b1eb86d997d71dabc5b78903233a3c27438ca0", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-VWXGISWFY18v/0JyNUy4A46KCFCb9NVsH+1100XP31lud+TzlezBbz24CYzbnA4x6w4hx+NYCXDfnvDVO6lcAA==", "signatures": [{"sig": "MEYCIQCU7G07tQaG7NSjlmbMLii6jHmE2gpNVy7g735eDowc3wIhANeXNPD5qMcUO2UwLPH+9/TCsc2y8BHbzfQn7HojpXFf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2393454}, "main": "./rollup.darwin-arm64.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.40.1_1745814918434_0.8038809659655628", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "1fddb3690f2ae33df16d334c613377f05abe4878", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-Gzf1Hn2Aoe8VZzevHostPX23U7N5+4D36WJNHK88NZHCJr7aVMG4fadqkIf72eqVPGjGc0HJHNuUaUcxiR+N/w==", "signatures": [{"sig": "MEYCIQCeR6fH3LyjFTVGetQ39kFyjf/34xma2UdC4krVu4YHjwIhAJSa2VZM3AzVIGs1hL+ftxrv9oF9HKJoPG2ZOKDOh8b6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2393422}, "main": "./rollup.darwin-arm64.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.40.2_1746516407608_0.8162868441308477", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "45d9d71d941117c98e7a5e77f60f0bc682d27e82", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-2KOU574vD3gzcPSjxO0eyR5iWlnxxtmW1F5CkNOHmMlueKNCQkxR6+ekgWyVnz6zaZihpUNkGxjsYrkTJKhkaw==", "signatures": [{"sig": "MEUCIQDAkVsUeP4C0cW7w56lED311bYGawgEfxoumkXzIhF91AIgSQZ+0hfX04TIUaOtVU5tpOuA6njNOAXmrszHVsrJMCw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2492558}, "main": "./rollup.darwin-arm64.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.41.0_1747546408253_0.996107099896792", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "1c3a2fbf205d80641728e05f4a56c909e95218b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-5afxvwszzdulsU2w8JKWwY8/sJOLPzf0e1bFuvcW5h9zsEg+RQAojdW0ux2zyYAz7R8HvvzKCjLNJhVq965U7w==", "signatures": [{"sig": "MEUCIQDpQ6zZyvP39LF5MGuBkHtPgLMGTd4RKmLn2QePqWc7cQIgOzSm/6nHNW2BmrrUHjWCmwTbie4Shdw6feEY9ITV5JM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2625230}, "main": "./rollup.darwin-arm64.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.41.1_1748067265420_0.24358491728926213", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "bc6484fb3f325e052963fc02a6450ce158ab38ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-w/idsvFiipvVGDcXKu1pdiIEnh9V63NPjpo5E48BbbrhUzEQthVXylKjYKj9lgX+89Ao36BxArkKPxxs7aXn3g==", "signatures": [{"sig": "MEUCICak8Y9s/D12saQ0DSGTHFbvxK3YWiThfd25dJBSnvsTAiEA73tP26aT/iLASown5mE0wwU8ksDTqgeorywgKwaBZKs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2608782}, "main": "./rollup.darwin-arm64.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.41.2_1749210026352_0.6149906924345658", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8642482ac2d21e7747a79b1cc3293d5711fefea3", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-JxHtA081izPBVCHLKnl6GEA0w3920mlJPLh89NojpU2GsBSB6ypu4erFg/Wx1qbpUbepn0jY4dVWMGZM8gplgA==", "signatures": [{"sig": "MEYCIQCr23oEm6T+6ET0mVGNB8y70W9M/TqgrlV970UEGroE3gIhAMlalCCQlpIqxsmitR7RSn3XfE35UUuyYvdYOdadV28T", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2608782}, "main": "./rollup.darwin-arm64.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.42.0_1749221286913_0.6507162425497037", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "9f59000e817cf5760d87515ce899f8b93fe8756a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-eKoL8ykZ7zz8MjgBenEF2OoTNFAPFz1/lyJ5UmmFSz5jW+7XbH1+MAgCVHy72aG59rbuQLcJeiMrP8qP5d/N0A==", "signatures": [{"sig": "MEYCIQDjTcStuXPDbCvLKdjbErAtrX85doH+FWWlM0zELhNVXQIhAII2ai1zqfsPM9R3sJPpDk43EqIp47YsIiettauelDEU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2608782}, "main": "./rollup.darwin-arm64.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.43.0_1749619351950_0.2676471319217515", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-darwin-arm64", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "60a51a61b22b1f4fdf97b4adf5f0f447f492759d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-VGF3wy0Eq1gcEIkSCr8Ke03CWT+Pm2yveKLaDvq51pPpZza3JX/ClxXOCmTYYq3us5MvEuNRTaeyFThCKRQhOA==", "signatures": [{"sig": "MEUCIEFJDPyQRVqHfmkR+6ZQ57NK946YdozYQMwjiJPixpaBAiEAvXhfoGA8shT+dvLHKF8Xe2xAOTjztYNECyYd9tg9BPc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2608814}, "main": "./rollup.darwin-arm64.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.44.0_1750314170808_0.617428686675469", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-darwin-arm64", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-arm64@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "972c227bc89fe8a38a3f0c493e1966900e4e1ff7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-fM/xPesi7g2M7chk37LOnmnSTHLG/v2ggWqKj3CCA1rMA4mm5KVBT1fNoswbo1JhPuNNZrVwpTvlCVggv8A2zg==", "signatures": [{"sig": "MEYCIQDJXjjqN/XZdyJdBBLfK+9gjsG4XJkYJzoGFsref0AWHAIhAL5tfO6GCd6FvhAI5zq1yHWr0HlzoY2tURI4Lyq2kzI4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2608814}, "main": "./rollup.darwin-arm64.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-arm64_4.44.1_1750912450321_0.1618661727170918", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-darwin-arm64", "version": "4.44.2", "os": ["darwin"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.darwin-arm64.node", "_id": "@rollup/rollup-darwin-arm64@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-EsnFot9ZieM35YNA26nhbLTJBHD0jTwWpPwmRVDzjylQT6gkar+zenfb8mHxWpRrbn+WytRRjE0WKsfaxBkVUA==", "shasum": "e216c333e448c67973386e46dbfe8e381aafb055", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2592286, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFCV7H4zfqNrcwa2OT00SEzyd/vpAOGObFrHhiyjbHFBAiEA0J5PjYx5RrEOnopSxQOPQRt62LB+xv24qdJhb3dDsZk="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-darwin-arm64_4.44.2_1751633763197_0.45139794275900114"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:50.302Z", "modified": "2025-07-04T12:56:03.667Z", "4.0.0-0": "2023-07-31T19:17:50.527Z", "4.0.0-1": "2023-08-01T04:48:54.046Z", "4.0.0-2": "2023-08-01T11:16:26.803Z", "4.0.0-3": "2023-08-04T08:16:48.139Z", "4.0.0-4": "2023-08-04T11:36:28.015Z", "4.0.0-5": "2023-08-20T06:56:42.402Z", "4.0.0-6": "2023-08-20T07:51:32.269Z", "4.0.0-7": "2023-08-20T10:33:14.425Z", "4.0.0-8": "2023-08-20T11:22:04.841Z", "4.0.0-9": "2023-08-20T14:28:50.781Z", "4.0.0-10": "2023-08-21T15:29:48.915Z", "4.0.0-11": "2023-08-23T10:15:40.479Z", "4.0.0-12": "2023-08-23T14:40:16.276Z", "4.0.0-13": "2023-08-24T15:48:26.684Z", "4.0.0-14": "2023-09-15T12:34:19.975Z", "4.0.0-15": "2023-09-15T13:06:46.777Z", "4.0.0-16": "2023-09-15T14:17:08.710Z", "4.0.0-17": "2023-09-15T14:59:00.427Z", "4.0.0-18": "2023-09-15T16:09:51.941Z", "4.0.0-19": "2023-09-15T18:50:50.551Z", "4.0.0-20": "2023-09-24T06:10:31.854Z", "4.0.0-21": "2023-09-24T17:22:17.454Z", "4.0.0-22": "2023-09-26T16:17:06.170Z", "4.0.0-23": "2023-09-26T20:14:16.859Z", "4.0.0-24": "2023-10-03T05:12:38.546Z", "4.0.0-25": "2023-10-05T14:12:35.910Z", "4.0.0": "2023-10-05T15:14:24.295Z", "4.0.1": "2023-10-06T12:36:32.450Z", "4.0.2": "2023-10-06T14:18:34.188Z", "4.1.0": "2023-10-14T05:52:07.266Z", "4.1.1": "2023-10-15T06:31:35.883Z", "4.1.3": "2023-10-15T17:48:19.220Z", "4.1.4": "2023-10-16T04:34:02.737Z", "4.1.5": "2023-10-28T09:23:24.264Z", "4.1.6": "2023-10-31T05:45:07.147Z", "4.2.0": "2023-10-31T08:10:33.256Z", "4.3.0": "2023-11-03T20:12:55.711Z", "4.3.1": "2023-11-11T07:57:48.436Z", "4.4.0": "2023-11-12T07:49:48.870Z", "4.4.1": "2023-11-14T05:24:55.551Z", "4.5.0": "2023-11-18T05:52:06.708Z", "4.5.1": "2023-11-21T20:13:03.614Z", "4.5.2": "2023-11-24T06:29:40.626Z", "4.6.0": "2023-11-26T13:39:08.757Z", "4.6.1": "2023-11-30T05:23:00.220Z", "4.7.0": "2023-12-08T07:57:54.542Z", "4.8.0": "2023-12-11T06:24:47.847Z", "4.9.0": "2023-12-13T09:24:10.985Z", "4.9.1": "2023-12-17T06:26:05.900Z", "4.9.2": "2023-12-30T06:23:22.426Z", "4.9.3": "2024-01-05T06:20:41.453Z", "4.9.4": "2024-01-06T06:38:54.546Z", "4.9.5": "2024-01-12T06:16:08.253Z", "4.9.6": "2024-01-21T05:52:13.782Z", "4.10.0": "2024-02-10T05:58:35.602Z", "4.11.0": "2024-02-15T06:09:30.391Z", "4.12.0": "2024-02-16T13:32:08.244Z", "4.12.1": "2024-03-06T06:03:26.678Z", "4.13.0": "2024-03-12T05:28:23.466Z", "4.13.1-1": "2024-03-24T07:39:13.026Z", "4.13.1": "2024-03-27T10:27:32.696Z", "4.13.2": "2024-03-28T14:13:28.700Z", "4.14.0": "2024-04-03T05:22:40.071Z", "4.14.1": "2024-04-07T07:35:33.420Z", "4.14.2": "2024-04-12T06:23:33.070Z", "4.14.3": "2024-04-15T07:18:25.587Z", "4.15.0": "2024-04-20T05:37:05.876Z", "4.16.0": "2024-04-21T04:41:40.939Z", "4.16.1": "2024-04-21T18:29:50.824Z", "4.16.2": "2024-04-22T15:19:06.672Z", "4.16.3": "2024-04-23T05:12:28.623Z", "4.16.4": "2024-04-23T13:15:01.124Z", "4.17.0": "2024-04-27T11:29:46.397Z", "4.17.1": "2024-04-29T04:57:46.087Z", "4.17.2": "2024-04-30T05:00:37.329Z", "4.18.0": "2024-05-22T05:03:35.432Z", "4.18.1": "2024-07-08T15:25:05.698Z", "4.19.0": "2024-07-20T05:46:09.193Z", "4.19.1": "2024-07-27T04:53:55.772Z", "4.19.2": "2024-08-01T08:32:47.901Z", "4.20.0": "2024-08-03T04:48:45.455Z", "4.21.0": "2024-08-18T05:55:29.981Z", "4.21.1": "2024-08-26T15:54:08.007Z", "4.21.2": "2024-08-30T07:04:20.997Z", "4.21.3": "2024-09-12T07:05:44.183Z", "4.22.0": "2024-09-19T04:55:26.444Z", "4.22.1": "2024-09-20T08:21:48.505Z", "4.22.2": "2024-09-20T09:33:40.160Z", "4.22.3-0": "2024-09-20T14:47:52.357Z", "4.22.3": "2024-09-21T05:03:03.282Z", "4.22.4": "2024-09-21T06:11:17.534Z", "4.22.5": "2024-09-27T11:48:10.225Z", "4.23.0": "2024-10-01T07:10:00.667Z", "4.24.0": "2024-10-02T09:37:14.320Z", "4.24.1": "2024-10-27T06:42:56.811Z", "4.24.2": "2024-10-27T15:40:02.264Z", "4.25.0-0": "2024-10-29T06:15:04.463Z", "4.24.3": "2024-10-29T14:14:01.562Z", "4.24.4": "2024-11-04T08:47:03.764Z", "4.25.0": "2024-11-09T08:37:17.345Z", "4.26.0": "2024-11-13T06:44:54.748Z", "4.27.0-0": "2024-11-13T07:03:09.204Z", "4.27.0-1": "2024-11-14T06:33:06.003Z", "4.27.0": "2024-11-15T10:40:31.317Z", "4.27.1-0": "2024-11-15T13:28:02.589Z", "4.27.1-1": "2024-11-15T15:37:57.404Z", "4.27.1": "2024-11-15T16:07:36.552Z", "4.27.2": "2024-11-15T17:19:57.754Z", "4.27.3": "2024-11-18T16:39:31.281Z", "4.27.4": "2024-11-23T07:00:16.327Z", "4.28.0": "2024-11-30T13:15:42.637Z", "4.28.1": "2024-12-06T11:44:51.832Z", "4.29.0-0": "2024-12-16T06:39:48.807Z", "4.29.0-1": "2024-12-19T06:37:27.338Z", "4.29.0-2": "2024-12-20T06:55:57.336Z", "4.29.0": "2024-12-20T18:37:20.146Z", "4.29.1": "2024-12-21T07:15:57.673Z", "4.30.0-0": "2024-12-21T07:17:10.053Z", "4.30.0-1": "2024-12-30T06:52:12.130Z", "4.29.2": "2025-01-05T12:07:38.083Z", "4.30.0": "2025-01-06T06:36:37.035Z", "4.30.1": "2025-01-07T10:35:48.016Z", "4.31.0-0": "2025-01-14T05:57:37.078Z", "4.31.0": "2025-01-19T12:56:42.667Z", "4.32.0": "2025-01-24T08:27:31.062Z", "4.33.0-0": "2025-01-28T08:30:04.384Z", "4.32.1": "2025-01-28T08:33:13.323Z", "4.33.0": "2025-02-01T07:11:54.578Z", "4.34.0": "2025-02-01T08:40:19.217Z", "4.34.1": "2025-02-03T06:58:09.366Z", "4.34.2": "2025-02-04T08:09:56.726Z", "4.34.3": "2025-02-05T09:21:59.843Z", "4.34.4": "2025-02-05T21:31:06.645Z", "4.34.5": "2025-02-07T08:52:56.875Z", "4.34.6": "2025-02-07T16:32:03.062Z", "4.34.7": "2025-02-14T09:53:54.888Z", "4.34.8": "2025-02-17T06:26:20.690Z", "4.34.9": "2025-03-01T07:32:31.967Z", "4.35.0": "2025-03-08T06:24:39.048Z", "4.36.0": "2025-03-17T08:35:38.105Z", "4.37.0": "2025-03-23T14:57:02.526Z", "4.38.0": "2025-03-29T06:28:59.956Z", "4.39.0": "2025-04-02T04:49:26.795Z", "4.40.0": "2025-04-12T08:39:30.217Z", "4.40.1": "2025-04-28T04:35:18.682Z", "4.40.2": "2025-05-06T07:26:47.835Z", "4.41.0": "2025-05-18T05:33:28.471Z", "4.41.1": "2025-05-24T06:14:25.712Z", "4.41.2": "2025-06-06T11:40:26.599Z", "4.42.0": "2025-06-06T14:48:07.130Z", "4.43.0": "2025-06-11T05:22:32.205Z", "4.44.0": "2025-06-19T06:22:50.998Z", "4.44.1": "2025-06-26T04:34:10.585Z", "4.44.2": "2025-07-04T12:56:03.431Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-darwin-arm64`\n\nThis is the **aarch64-apple-darwin** binary for `rollup`\n", "readmeFilename": "README.md"}