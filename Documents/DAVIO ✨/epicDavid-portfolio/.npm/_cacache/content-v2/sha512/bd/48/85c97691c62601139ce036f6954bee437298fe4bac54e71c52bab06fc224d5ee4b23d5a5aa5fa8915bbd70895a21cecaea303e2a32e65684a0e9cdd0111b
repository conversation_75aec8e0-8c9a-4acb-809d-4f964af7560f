{"_id": "@jest/schemas", "_rev": "35-f94b393cefb429716b36132d8d17f49f", "name": "@jest/schemas", "dist-tags": {"next": "30.0.0-beta.6", "latest": "30.0.1"}, "versions": {"28.0.0-alpha.1": {"name": "@jest/schemas", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/schemas@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "651f5d296d7055a9102db0408a86770ebbb35911", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-Btd+SwW7bVYaWUiDGj0iNZsFlx5XR95g4EPy4jeCJoa6SYHkn4xR2+iMY0XyDBnAGrO70JMSypOnTZkTHUjnsA==", "signatures": [{"sig": "MEYCIQDln5ZTPDd1+RkqQNhVOjeIC4HVhqx/JGpu+EPZbOFlhQIhAOswvNqYRLJKI3x+xNoVc5/Nk0VHS0fFR1q67XpDOlWW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqYCRA9TVsSAnZWagAAFWUP/iodal+bL2oEPdQTHvo1\nEqEjiNinyE0Xgc9jitGdaIBkXVTJDOlhTR53Dsk62IfX9LdcI2zhlXlxaQQA\nF+1qcLCoNNzzgxVRL+LXliCcHMHAZ+udL6SMJyU8yA7P3YaFijwfqOU1AuTj\nfKQ5DI+JfyDi/BwbAwy5Lz3HLfNV3v96fpVx4JT1k/aZOSYANrbK10XxabnF\ni+Kh/g5G8J29znsaXXfu0HyJ4zmPP3Itwl3VureqgyGTQFZGezA0fX1rkjiv\ntJkCK8TXoE37I6pRqQ4IDmhoFtltZhKw9AWEU+hUU1HYutmm+/yIrW+vKRv7\n51ARtK0y+E7l99IwHKhTb642CIpRCxtVsk19sk5yNGZ9Os4BzqSKCwZ3+NLg\nWJK2iEaQlsbppBlPodMjeJJQggcUfrpsugnX3rqjpFd95P2a/HXgMWQGepWJ\nri1nuk8puL0ts8qkjN5epcKJLSwONxe3+qlVh9tjK+jNp9ByMeaAsersKqFH\nz8i7Sbxexj168HCVbx7rsQ/t/Fg16XkCv+WvvTcOlpjl1d/ihrTLRGxRCIT3\nO4YPAqicZFjX30hMSfLae5+zRdwQbqdRke2eE+OnSm6NwETByQGyYGSRsx4t\nc2uOETog+vLj8LJPCyDp9oSsdUABNLLIwoxlz8ufF/1hystoBs84f1z5xBl6\ne56/\r\n=cbiB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@sinclair/typebox": "^0.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_28.0.0-alpha.1_1644960408796_0.1663905328612041", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/schemas", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/schemas@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "daa3b514af1edfd2034dd221faaa3fde09d3fff1", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-IyywbWbBegRS3frlMAogGGgrmBkd1szsFS2EvTu6dJBbMv/lGDkrJgUeeTenPPxICQqQeaNM1r1dHGC0vpRVvA==", "signatures": [{"sig": "MEUCIEdRWI5UmeIFp2jwTdnZiTp+P38Wp6nG7s8KTvXRc6RLAiEA9AmidU9r4px9eDCtI1YCj0PZK/j0H/0vzwJ1TyTPSW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5sCRA9TVsSAnZWagAARO0P/3WyMbTCmgydibpdRAt7\ntXZwFCGrkN1EbdUIbBo6mkLpF4PiXGq9dhjPPAhQOsJ6shGfBF4ZcA5xYOI5\nJZYYA2rsPu6nERCNBz0F8pa8llGcvRAXo9nPadmp+ewgiAm2ItOyf60aPNZH\nA3AKw48Ak9w5LFHNbutKyDJlSe20pi4GDUbWlt+RTvIeggbHjAGh3JTuuGfm\nyBSAzQpW5N+jmkQ3B91db9Zoh5gAzhcSSsK4ix0Ue/y0z5/Fq/e2xWopMEiM\njs2iz5kotAQod23UYR6sa3PcNjJtvtUyQy7qPS0Pegd20JEqLh231pqFzW6x\ngNK88cbbpv0sEFqSZjgmBz/OJPL50MKw2dk2TsrohaYqFtolWIc5GuWG1rVN\nsKWw4vIJv3gAxiVufk1PtQaqZxksI/7sDVkolKCjlHBPoy4Sp6P0pmYJuP2D\nD1MqfCPzKvGoyQZtYU+5x1VWk16Xk7hUD5A7YuvZiP2Ca5uCjv1GFmQuNCjI\nV8FAVuR0evyIYlFjxKpq9xwFL9E1kuboc8vSJoY+LIFJWiqz4vb/u68GPMfO\nK4CHOF9klBfXFXc8/YJpIzmc2+UiH9tUMfw0iKIqJ7X8qZybwDE6xV69bS3L\nmaXzwnVPVaiXbu96JFvljty+3O1cH+ueUwE3MOXc//l1aQ58zXntDesxOHJJ\n5rcb\r\n=xiFq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@sinclair/typebox": "^0.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_28.0.0-alpha.2_1645035116369_0.5129727804414888", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/schemas", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/schemas@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "188c3f8c6e7d11c398fb072d43f091c8eb65d222", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-U4BNcqkplLb1ALW+ME1x3+6j2epFxV3Rxi29WttkwSKoTnVimdvapGVDaRJXgO1ieVqb3Qrh3bjtNXFOEIlM5w==", "signatures": [{"sig": "MEUCICoB+CR/OAZw3FMXQKyYtN8w/jeshaeKUs56apGFMqiIAiEA7ax5EdTX81OcxPbv086QKjy33QumgVWe6nY5lyf9hDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogyRAAimcraCdpIPnlmdQeNBbZuo4rVhd4oE3Uo+IMd6kotuJygZcL\r\nMrH/A/ydeA033AIs+ntANWHcgHqfzdoXPwkKb4DIkhlM4fIeCl6CNz7XamrQ\r\nJ3rHj40VZKYfjI9fQQidjLwodb5C3n8m4B7XUGn1xqnlOUNe6+HdvGU7cYrO\r\nt7lC57zyVTR8PgdUSRb/2ROA2rH7uwyVdG5LLh0Vxccy1rIUJ/r5wyqAhtB8\r\nsSKpYxHJppx2rXMyBimfaW9M1IETHpD+djfTgjYCMucKuZWbVAxN4jrf6vLp\r\nUlqtfQCSLYLG+mkKup/QHvCVDkybOmTXOcrzCjY0rGGPm4+v+Mho8kfGl5U7\r\nfk7MiKfAlWDu7NIxhkUkvr0Xm5t2omPJnlvS05MVuD4CyIQP2axp9xSU784i\r\noLqjbOuT75NsXeJBvJJvP//wrjg98gryK+6KUJpDiH8VZFZe5hk3HJuADywX\r\nyobeeuhaqRwyxlcTKkxA2h5TcWCatj8t6lyDnA+1SSg9hjw0wFz7iH/GNz29\r\nSYfkFGkALG8AQjq2WC8rwj9Q5Y2Naq+uN3l9+pNbhoBPovAhDiymAV5RRIQU\r\niBU+UtQChk8XvflNhzX1yUAi8X8gjLYp4lxyx/DjN2wBfnn8iDq5TtOnFJj4\r\nKke8YGcezm5rqkQJZUOvufABs/Ojj9Ydytc=\r\n=lN05\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@sinclair/typebox": "^0.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_28.0.0-alpha.3_1645112539998_0.7693749647255417", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/schemas", "version": "28.0.0", "license": "MIT", "_id": "@jest/schemas@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "55cf5fcc82440a2a39b717bb949570c501fd5410", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.0.0.tgz", "fileCount": 5, "integrity": "sha512-Pap9Jvwr8KYFvDgkya/p0FCVya+jZkWt57lHpwBylfjgmwi/gtXfhyAO/Cw+jKuMafHcXY0beNf2XV2pkcu9vA==", "signatures": [{"sig": "MEUCIBlFvA1Aa9j2wnTCipIWWpW/9difgaYzBLia4kxDw63qAiEApsQwZ1wb5D3430rfrVaskc/5Ctszsgd3Fj2ifBHi7LI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpzgg/9EHx5pRZDQmOvL14V+jc/nDrABAs3Wctmn56Hfnp1KTwK4ERK\r\nTOdcHlr4POXnjBTvAbfduE7nx3u4MjezqJcix4tq1mmoe8CTgxrzamNiFnHn\r\n8vsw25gizfwMeD+0Qxgm0yLrdjXA3GJIwBMJiNEWiHFVAkHZecM2g8nCIxtd\r\ns9WZ1gFt4Djbk8Dv/YDC2wIP1754PFFUH7OeaD70rrP7AT1pP3xtRmZ5Gwy4\r\nfzCvht6LT2QyDbzbF0OGq1JBOTVuAWCHnz4l/i+h0aGQpnT9amlufVZ6A9qx\r\nBuhsDlSyn05cbL3+FHBPPAJAbt2ECjJho27YwOT8j6RHdWpvsY9ym6Y37v5U\r\nnGTg1tQq0rMEkOYbbHYjHEPHzYZ7eBpqXrzgihCTVkCwskZBDy15Kqs2aHqi\r\nfXIEX6vv7tMdiZtkkfkpKl6F3AoPT8dhRGvAyOOHrHgQse3Se+kXYMqwng7M\r\npfBke+pz8z7AqRqLBjWHreP3Et5hT1thg8AYBo28EyJNnPAY08rPFMJIGEUO\r\nfnyoiDZfbAFChPo/fDpwL1/1dvG3GXTOuukcW1lptC0eU88LHDAt/8PCXx5u\r\n+tssLvB9RzDlrZ4OkV/gReMXEpRjCiLFpxntIz87+6t0bR6TC1xtGBXvDykl\r\nvTztQb9c7O1YJQ9qep6RdCquN3tMKieMohY=\r\n=rRUm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@sinclair/typebox": "^0.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_28.0.0_1650888481755_0.6233445653204053", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/schemas", "version": "28.0.2", "license": "MIT", "_id": "@jest/schemas@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08c30df6a8d07eafea0aef9fb222c5e26d72e613", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.0.2.tgz", "fileCount": 5, "integrity": "sha512-YVDJZjd4izeTDkij00vHHAymNXQ6WWsdChFRK86qck6Jpr3DCL5W3Is3vslviRlP+bLuMYRLbdp98amMvqudhA==", "signatures": [{"sig": "MEQCIA2pmLDiEcrHqY2Eil89EIvhAfc5oGyplgCRK0iwPsx5AiBhAce7KtCC8F57xFBY9iGSomuE/V9fudfLDSE1eNRBsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprYxAAhjA5bsVecgdvI4MIjb/oE+KQGSWcToPc9Fck5xBq622nTSDP\r\nckxmrhBbAFReu36G7SfeF8SjmYExEbhDvaHU+ucKQvwOawcB0ET4Palsq0JL\r\nk+nzI7o5CPgltpXJY+tcWK0O6vCO1MrPWTtgk6k8xIjzGiqoyCxIrNp6quN7\r\ncJbGG69tqwU/2e5IZmj/KE6ed6fVm4JgOLLhaOdC3sfeb6G75vT47krj4VNF\r\ndngNBIJ2QwJELx8B1zPCnR6MxNYl135h1XtZp6As1rng0NBW1u899wEeT23f\r\n3Ju16NLnKH4/C+Bn9ECev/cgAYYfrSoJLttkMZK6N9aqlSI4r5kXSPPA+Cjl\r\nx0spsa6toebbCMCzifnr+6sdL4mIwQCYhP+yNQfg7hjtgrj9qJcq2FvUEGg4\r\nmHy/2jFenRVihcdJUXIx2vOUd0InL30ddqfZOFmYJ6PliR+MYbN1Hscf5hC9\r\nJAaUfVfqAsSxHsrbRxmC71OdXR0wvrPDBWxwbHpat1ynZSlbP18FbYzFr+cx\r\nClisAAJqQWIBZaT2LzwyfnuXiRbEFuYySTvHhLHczGZ5SQ3ggPldYwXNO+kL\r\nA6as93WGkKoUpipxIKUwPpLj0iPXzzTyTHSVbUvuvGAMvlY3rj166P+PYN2w\r\nhyVQNOrDkrhzk8RIGuaNTXDoNUbg0w4X5EY=\r\n=0vST\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@sinclair/typebox": "^0.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_28.0.2_1651045439999_0.6725386199353702", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/schemas", "version": "28.1.3", "license": "MIT", "_id": "@jest/schemas@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad8b86a66f11f33619e3d7e1dcddd7f2d40ff905", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.1.3.tgz", "fileCount": 5, "integrity": "sha512-/l/VWsdt/aBXgjshLWOFyFt3IVdYypu5y2Wn2rOO1un6nkqIn8SLXzgIMYXFyYsRWDyF5EthmKJMIdJvk08grg==", "signatures": [{"sig": "MEUCIDO7pSvH5XJw0mXpotmBeHrDKdY1OMOp39Y+emBUr+IJAiEA7Ntg9zPSoklQqYmtM5sKnwRe9wKw1MypwSJ6GaV8RPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGkg/8D2L+Tpu/ZngM3+OSzKtLxl5FHeyydnzhqw5PaN+7M4V5RyGg\r\ndg1N+yQRVcuFNX8Tipnsyghr8P5+HOuUqPproZkKh6KVCD1u+R9YTIQP/5hs\r\nHfkqy8+cz/JKxMplzviaEBOYCOSKH5Ak4GL+atI+38FqooWlQJMXaUjjJb/v\r\nKmdkEdcPzItSinsc8D5cTPtYGc7ObbyR/ua/HJi5uCaHXDiGwqrExl8EEHY/\r\njXEVuOIdytxoZ3FtCMp728XPhG1g7DEzRFEg1X1os8ksEFozAbiK1G3vLBEd\r\ndzwxZdG3bg/ZMc9PWyYztcCudIly1A2/DGqtN+iev6Z0oQFiRJsC/rOFhGLd\r\n4J1t4gqom/kK7Xpt83ZJZk4MM986lP66yGqia8WB8EMFTHsGpC61w47xohVk\r\ntvHf4MbyPXdLebhgbkcbxGkS8qsEq1w5SuL9iejgrd1in/CvMQ5wjJcqtxpl\r\nqCNMSTXxHSgyHf+LjavNg98eN+O73meMuRAuRdE/DF+51EO/v8Ysgm/P2cKD\r\nazJB5VhcMPfxzA1mmstcUGNgg/9cPV7/UNqi9MGMT9O/UeVtFvKfekVH4qoK\r\nmkFWslC1wrnKWhsRdo1b+j1CRKhFLTYsdM6hNa/vCJ6xwNs0w3Ja16xZGv0b\r\nIAwxmMTOvs66LbBPM5UbfiR8hiXLbPDpiMA=\r\n=QeiN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_28.1.3_1657721545824_0.18773199495109005", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/schemas", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/schemas@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6c8597d94456971d497847546c938b2f4ce7903b", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-HeWc7my8MjPsM6KDiDgQEAuNSl0v34WXOCjbDEQNWvabAUreAQ3xN5Fycr2WSuxwSE2zlFjjDPwtoTMrbubUDg==", "signatures": [{"sig": "MEQCIERuGceFIC/q5WIa3zEOZuTb5SNJaKQN6p/nQiTlkeYcAiAk5uSkG5YdPlVt8eSN6r+3d1coUrFrGj7Wf9nMbaQlKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLHw//es8AiLbrLSELps+ftdB1SQK+i2MKfnXUUOoCTtJGM+4JVIQr\r\n8YcaqW79RLB+YQbJbMazzFaVJQZ+9hyo32Uxb8IYONvd2UzUGSZ8w5ATrW7D\r\nOMz3djQLeQjFI8yNemnxwEcT6IhKzsG1NNBgkB5w+N+BfS9z1c3E6x/hDlBq\r\ncEe4YxNSlz+ZY78k2Br2CE7K90xfMETBDfGLEDBSbo+lSxN6XGqzQ9RJvL4Q\r\nCTL8R9nSNBlBqAt+tNOeQTorlh/issNkx/RMOp240BoAgpBV7X7zF3SL4Uwl\r\nLm6NT+5EPA/XKIqvgo2z5iKqK/P3/mUv7459lo4qYtWiqAxprkWLQqb17S25\r\nPJgtlodt4RyUB2Nz1eX3ApKqBGGu1fyqxgBukE9bxQAXaAwsBAPncuh1eApd\r\nevGRsJKynruBUKdX748Tezr3zIWpi8Q+7UjrCpVauyGVIxq61Kw9IfwRQURM\r\nme/1L/xiJI34gduZENZjkUE1bI7ULU8mNNT/3Gg2Ii3IKVrmlUSJytDXfa0G\r\nJnLkf1WJZK+g8DUCJnS/tPHJi2Yv+aQxGLvZ0bCdMYhRGzracfLrHhA7cRWy\r\nedUvDtH5dEIcJY+pRuIbhqSJEzFAdRZJ18POjZG5PHM2h2eZ3EU5eHpUZe/E\r\nxFrzluJtALajjxqPQlXmVtzhTi0d2au8Pww=\r\n=nAou\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_29.0.0-alpha.0_1658095625728_0.276264342268578", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "@jest/schemas", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "@jest/schemas@29.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "833102a80200b8d31156a22d8e9a918be385484c", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-utDqz+Hhn36/efwFo1POW4DfWgHfWZ/MBqBwNFEh9hUrMVoleLY4TJvcN7iy/fjjdfxlHYvIRLV2tEETQ2+NuA==", "signatures": [{"sig": "MEQCID2Q573mdbxCxMkJe2LxhBRXpg9ClHDvPJ/py/O1utKbAiBhQkCF2SZB8Gp1h5TuBx19F9SS0EKPM9tvam6weJZwrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIrxAAnkK28A0jopyehItNtDNYxaaG08/6Amx5RzV6BrVvOqrAS44r\r\nNzk7eJgAh2a09sVn1hnP0H6GqeURleEogekZTxK6XfIUCJsN6cHkXBgTEtDu\r\nxLG4ytw4lSRaUam66/bSg4kPE0zo4a2vqgqqUQGjEjX/SeXD3H7hOtqx1kOq\r\niRVHXVZe/eJ8rbt7H+IaOMjxJ36OoKeVeEf9sI8ttGXGHcN//Ua5GyYACkDu\r\nJakufpyM56/iOFcLz/qvzdZW3dpdNAViDzsgHKbgrQMS3L7UkeJNA3aushyd\r\n3CSsxWjpmpuq7bRFm30VEIiAwze7ODbeKnSiB+jNfFH16hK2LcwYJJM3zjno\r\naO7eIpHo5uk4rehzaLp425Fl7qXz7BMj6ESW5LmYcwOb4QZV75Ym7qlbKidN\r\nz56cNIAjEeOsWKPF3y4N8uXVD7vou8MXmfn8O4dEAIg/0oANK2x6bhGnvKHy\r\nWC9o5eRvAIY6wLmAr95halzdITeIiMI994BgAZABlQH6q1LVvl5I8kFEg3MW\r\nqx0hIfHlN1Rzqg2jNjYrWwKIurHW1ZJtdPSzu8q2KioonHrNYQ487L2KGkrA\r\nSGO0TwKaZL4SrP3ZZpdjzfKTTPCRlE56o4pmDWZ8265dp1oYoqDok3BA44/X\r\nbkNhI980cC0m6Pp1rGlnCb6slAkjeuQ2bBw=\r\n=Ld5P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_29.0.0-alpha.2_1659742345143_0.6025332673691735", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/schemas", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/schemas@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "235bb9d7b69b459616304081285e43d00fd90f72", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-qfCWn5SYMp7tJkzF2wG6eBfugaZKdQoREw/b3bXR8ePHzwXpm1BWKWvZSan6/sQngyo9cozRkE1e45O30HYtzA==", "signatures": [{"sig": "MEUCIFq9K4mfh+Lq9G+cAHIDUwu3jCf47A8j5DDuwGYp0bNzAiEAlcA51d+yDfY0bw6XpXu1FkzxmMPYzRK6Mkx/sjpIUD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ELACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV3A//XW4kyH6JUA4cFLtaORvBixG9NjN2U9Aj6CkK7U7ARkRH2RcY\r\nzn0bNdBa92PjPUFRJihIIumch9Zx4KqyIasht9A41FNBMRYdGPT/qz2a0O51\r\n5a/J57rQMJwftGTbIyNLRpa/aNrvjfNBDHaZtmF7K8qULo/lAm0WjC70ZPwW\r\nuoeE25otgl4a9O2tUCYWGr2qfIMXHPiJEuRNJmlXVz3hmf78l27Tz4hkoHyf\r\nZRJV3qLiXjtISp/asoBRZVOwzlPBM1PUz54sudUrqvU+wNi5iJ1uRUN/oWkD\r\nB8n+6on0R8dwy+XrQHy6H+GfsB9jp8ZedfcVVmsU4OjMpw/ssR0YZ+f6G/Vc\r\nKCJIKS7TQooY93o3DOlvfXce1Nx6WuObBvGRuzqx0usXeKjhYZ61+kgUB+np\r\nvY+p1ekJO8EI5WcczA/cONm4LYOecVgwtfp66DLQtLsdUKXx6LVp6NaLJfN4\r\nWoJfj7Mp9waImMlp5eo16or4aNzVuggxdhL8N4aC0shtgr2u9MIBDQ1dKpOa\r\nIgWZd0Z3rgSaSrB6WG1TEiJXH5btR6K7QGVhT8qNCIovCD7EgRz1GfXm1nrF\r\n6L7dmxQz18gsYMeDcoUZzLln8LTcYNYEq3A6AcLbUUv6On05YFC3eX/LEMvv\r\nohdTNrYPLvlwxah+41nA7FCnjeJsw/d4V34=\r\n=FjGo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_29.0.0-alpha.3_1659879691819_0.12406914770641864", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/schemas", "version": "29.0.0", "license": "MIT", "_id": "@jest/schemas@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5f47f5994dd4ef067fb7b4188ceac45f77fe952a", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.0.0.tgz", "fileCount": 5, "integrity": "sha512-3Ab5HgYIIAnS0HjqJHQYZS+zXc4tUmTmBH3z83ajI6afXp8X3ZtdLX+nXx+I7LNkJD7uN9LAVhgnjDgZa2z0kA==", "signatures": [{"sig": "MEUCIQCQXiwn3LHEOUtPSnOCp9yxfoK1nSgEHO+TFX5gIowAzgIgTXKkY7jXeMclVSrqNegtjfrtg93Jhd8ZFr85BFayH+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqatRAAh3nAOOuk72OzB4sAnGMRvGpXPPRAW/M9WzjvwM5i+litI9MI\r\nJ/uYpZdgxVfWfIgjbtuJUUfdt0858H5s8RbRzPEYzbh/V7plod3qHKwfuxsi\r\n7OHrtT/n41MmIN8ixID2ACWZf0o44NpRPY/zI3/PK1Zv9Mjbgqvvt75WiHsR\r\nWVqhPtPyuqwAV1Y8xKKxlhmRjdCnvLnS0NhbNYR/9WyH9TeFGxsk06Bb6eIn\r\nsM4GE8rjLgskrEo37i8qLNPuc5tw1bVEiMdQ+5hvyPubKp+QXR2ecMeLsUCv\r\nPtRiUfl7Jne/bX4NyunBcjYsceyOon9JbU7zVz302agEI7+QPzif/LUNreGn\r\nF7ePYVrf56ckTv0kpV3wMDsh3LWOXymrPHlWJhCiOsGkmVL532c30deajtGa\r\nOzJfUSTRS1rGYKw8kx0/q2ufBECepY7ISPj/TbcDIj7c5LApGmxtG3okQBeY\r\nMwsPGGfIqgxRpLaTFAKIpwrW6TBkqnO/RjNW3bnDj2E8LWx7jYZCewNU+fzQ\r\nsHvCyszzz471CfvxfvmqPGJReCQz9bqUcCiQ5PbBxZGLB21I2VOF9ACdnEqm\r\nfpdmIBeNL56jcXYFO5Use0Qs7bZkZQpaC3v6CUXzhKXy4nqOhL2Tv/CWjS6Z\r\ncITcIphdjPFaAjMeMxXCyvywGT6+vlcTQ8Y=\r\n=WUq+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_29.0.0_1661430804402_0.551867538374587", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/schemas", "version": "29.4.0", "license": "MIT", "_id": "@jest/schemas@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0d6ad358f295cc1deca0b643e6b4c86ebd539f17", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.4.0.tgz", "fileCount": 6, "integrity": "sha512-0E01f/gOZeNTG76i5eWWSupvSHaIINrTie7vCyjiYFKgzNdyEGd12BUv4oNBFHOqlHDbtoJi3HrQ38KCC90NsQ==", "signatures": [{"sig": "MEUCIHSlZKabr68HDEIkEvtKvsBI0fFRpK4idoNJubsyn4/gAiEA2e4PSu2YJO9Xz9GW60tSQgSoiCTYEtv81yJ0n9IVr6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7kvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqO/g//XqzfXT7GVdSsSeIu+Wzzr8CTT1j8IQB+t5eifXn+aV5tTMz6\r\nUpevlsT4sVIs7Zljak+Tt768o7I4+IicxZhzrQlRhLC42VOD9+kqw2Xtz2Gv\r\n7PV1c6x5pYG2ajwTLYt973U0tOFOTP16hX+ohK7IZl0C3p3RWJXeSesQhW9M\r\nLoxDm1uGDPfsRdKGwi4+NAXJRSe4x/n6eMn62Z6okVrB2qmSnHKb+l0VBvZZ\r\nOOoBlNNfDCS9mLpHF+poSUqxntFK2SCk85T8S1zFWhVDH/IaQc7ZZAELiXN8\r\nCcHQai6JwkhRaMRTZMnCL0ZVebVh8XuRYMiabSaWjgyVO0Ym3e20qnNOTNQq\r\nOEAXoDVFlk3BZL8mh2y7/sI02cT6UwLdHr4nQE97Fmz1jApcyXn0BL2pGa0y\r\nj8gJMxQ5LT9/i7hWcXmhUFhwQYWPNLMhHoutn3Y4YBHrE4FgOBHZQzetm5zN\r\n8lppFvPnpxo2oOTus1rf7j1ZiNNcdApAsDtiOCv6lWbpUIv1CV4UR3UJgr3u\r\nlSEuKQf9cV35Pmp+epsfaRT+1RL1Gev2o+Fya2L+mDRijnL7e7QTTxYaNojO\r\nUK5vJlezMnfpl0bEg/mAk7O5XGxQ1ri5X168F++A1qSuEoBTBa4A6PgVMPAS\r\nxV+swvZxLTNONH9muoV5HRbONJBzTwpC3Fg=\r\n=RcpY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@sinclair/typebox": "^0.25.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_29.4.0_1674557743269_0.21562534140474665", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/schemas", "version": "29.4.2", "license": "MIT", "_id": "@jest/schemas@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf7cfe97c5649f518452b176c47ed07486270fc1", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.4.2.tgz", "fileCount": 5, "integrity": "sha512-ZrGzGfh31NtdVH8tn0mgJw4khQuNHiKqdzJAFbCaERbyCP9tHlxWuL/mnMu8P7e/+k4puWjI1NOzi/sFsjce/g==", "signatures": [{"sig": "MEYCIQDso7ODFTO9jSBRDzS6uKShZerYbZfC0SjVSmZJG6AwKAIhAMFQV3dgeE2PJisAG80NWCevhdMPcNm9Ylm1UcDbNs54", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxPw//W77c8a8AqHdbjiHgpW49uadomKy+r64nEddl3QYFMXM/PG5N\r\nmgNw0h9KBF7YrSqQ7KFiUDAgaRlNevspkR5oUSycSuD35w+EzpBdXKBaIQ+/\r\n7TqBOqQNZiJvrlvDQ/UAx8ACKfVNGimWzMWam4ERgHucFHQ77wIYwumSiCj1\r\nqbR8DF/g3R38ilPmoVZKTsFld1qdMAS8DFX9GyFoG++taDlP9GKEryYg81ZX\r\nYJf95MKtjDw6QUovD7oy/K/AtW/R81S5YnFkp+MHLt3elFMQxs0Lnb7B9DVT\r\nifDoWLRfq2UAc2uF9SeoL4dP3MjvQseDhFR8Yd5bNWILcJoiHp/Qmx8lOkhS\r\n8Gow4SedqJxdqx6GmdxItOPpOi9HTs1e/rppMPIcP+lrZSbVavAOATE6mHbn\r\nEEjXf4egQp83r4Twm33DAN48Z6Q4QxHmG2c87jsOxT+3savzfyy6ClaysVh2\r\niuxAK1M7U2Tb1rLTZY1q+MljmhnGWpZcD3fuKvqb2BQope44NMufMH8ipVII\r\nlkDvBcZi0uijGbkNXzRHyOPF7jY6mdudsrqfuJURuSodRM265v68/1TnWXZy\r\nTfEweMjZaQJLD5e1lkp9kf8yYfRoLXbgse54E9AP0ZItclkRlFapDhdO47ei\r\nCdNsrY38lpbenHVmMUz0poRss43Uqs3JpE8=\r\n=9iGA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@sinclair/typebox": "^0.25.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_29.4.2_1675777521822_0.08730107525572417", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/schemas", "version": "29.4.3", "license": "MIT", "_id": "@jest/schemas@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "39cf1b8469afc40b6f5a2baaa146e332c4151788", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.4.3.tgz", "fileCount": 5, "integrity": "sha512-VLYKXQmtmuEz6IxJsrZwzG9NvtkQsWNnWMsKxqWNu3+CnfzJQhp0WDDKWLVV9hLKr0l3SLLFRqcYHjhtyuDVxg==", "signatures": [{"sig": "MEYCIQC3ircsOntYlNVqE7oNdxpKHNYGlwbYH+pKgo3zAa8ztQIhAIE02ibTqJ43ZyRMRxc87vhoPxXoW5I4uwuY9ULzAi3u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolOA/+NTBXwpubVDTM1NNSiRchI40rjCclbLoXzL9r1vUpU9AOriNg\r\nRXQvlOnlYJkSI+D56wYj0pb0ZuUg/SQTVhuJ/nf7SwN6sBKAKp3757hlwAOa\r\nuBnf+uNPMAUFZwNk1jgTrR81cO+BukME19A5j1GOu+EmyqT/etnmIqhXRa9R\r\ney9UsnAkU53GojPZd5vMwW1MxKf8+fRI+026Jl1JE5D0UB1Ysd8oJJM7A6mI\r\nLaVYMkTnhysH/iNCX1WHOUtHFBHxXOv9gX+kUbirUxRopC7Zgpnkulfv9OeD\r\n0AyWDTu+hvJoW5R/6Nx6ajgkMeTkt+hDlHVfJgQGw+t9XgwCpaYtB4q00D/Z\r\nIjPaUt7XCz2Wd/lyE5vDA3MC0dlu+jpnNXZfPn4KjRHrxzyhQzVSPDPkHK6R\r\n95VR3WwdWwViJdIhPC0mXHsX27x66Th8s0JcnSBYaiGzmQQSZBE6daGKmy/Z\r\nuuXqaZTYcDt8tvXm9pz3NZE6EnrIdZdvxKeGlwyrE4VqX1cPUDDeyuCPeSiP\r\nXeOJZ5AZj+tSowbOPmipUNi3JGsswDzDtOpMtHIRrD+vNiprAQpRqPveKipq\r\n8Cgj+hf1gD4h2OZkC5YMV/9NIRRzcofPexx6SlASmggzVGhn3LJQ4qF+xBVk\r\nonYgIBWWZngmzfS9YU0aRgTZq79KMECVd/Y=\r\n=ejdR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"@sinclair/typebox": "^0.25.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_29.4.3_1676462236224_0.44688458524634367", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/schemas", "version": "29.6.0", "license": "MIT", "_id": "@jest/schemas@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f4cb2c8e3dca80c135507ba5635a4fd755b0040", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.0.tgz", "fileCount": 5, "integrity": "sha512-rxLjXyJBTL4LQeJW3aKo0M/+GkCOXsO+8i9Iu7eDb6KwtP65ayoDsitrdPBtujxQ88k4wI2FNYfa6TOGwSn6cQ==", "signatures": [{"sig": "MEUCIHk4yf75YupU5V/lcm/4fhlnnI6/no/xKg62ba0c/uNBAiEAq+xEdTSQ3UB3D5nDwuxaMRgQK0Ca9fY0+4bHN1vU+Ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6075}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_29.6.0_1688484340383_0.3551075456758237", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/schemas", "version": "29.6.3", "license": "MIT", "_id": "@jest/schemas@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "430b5ce8a4e0044a7e3819663305a7b3091c8e03", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz", "fileCount": 5, "integrity": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==", "signatures": [{"sig": "MEYCIQCa/O3+9UvzvFBcys1LgvRvmdzrjCYCiPSWTjCo9e5lQgIhAPQYc16SH/2G9TLn5hEKpnhhP2y9tSbQPx4VzGbQy3Sr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6073}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_29.6.3_1692621537573_0.03579305689063128", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "@jest/schemas", "version": "30.0.0-alpha.0", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b6033542c32462fe7625737e32262d11625d57ec", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-5PU8yz2FGS4LyZCvuiqgD9XKqcWUHFeWDbgkCB/244YW4xr78eMFwavnHlC1xUsqgKD7Lmnkxnk+0nA2rAA+ag==", "signatures": [{"sig": "MEUCICVIthVFdFEQnLpb4fk12G6pkHfZ7XsXqBLnZ96AF9k0AiEApElGOaxVpnTS4xrFc4g3062Vs2BgNbtVUVTbC1W44tI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6695}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@sinclair/typebox": "^0.31.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.0_1698671619554_0.5136322003036164", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/schemas", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "2876c8a5413b8fbce6f34a8d3f13feac653cfc04", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-mqn8OLomLW0aoCIfryTni9SNe2frohP3VlDCoMkZ1hDfEJn2EJntM3SUWCxSh7QD6E/7mBci88TuB6W4G05W9Q==", "signatures": [{"sig": "MEYCIQD4cWRZdClW7zwxrjrq3p3uzxHwD1wlkBuHgt8yXw8eKgIhAJLyaweoG0fIYyZle+EWb5zvV7aL+94mEIdJFBLl1abo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6695}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@sinclair/typebox": "^0.31.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.1_1698672765974_0.34936139609212047", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/schemas", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "c8cb82e15e324777cc3ae1bc927ed9188b221c2d", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-cmXKHZ2oz0OK1aUg8HR3OT4jAUq+mBLtkOOwFdzmMFKk4gFjGcjevSMN/sLs1daMcXl0TMA1Algh9LVW0+bWwQ==", "signatures": [{"sig": "MEYCIQD5W7QwWts2g6nOPqpM/YqR9hio+PH7leRFIQGCRotFDgIhAPTcL3L/amoR1pO+hJXiyrC8U+Ty9uo06Koo3XTu2Ogs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6477}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@sinclair/typebox": "^0.31.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.2_1700126894377_0.3708041488215732", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/schemas", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "113e33d3563caaf13b15e16a1e37f89b247b106e", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-7<PERSON><PERSON>tiHmJcycvqQcRVIyTHOW45uj63SOnZbRaNRBLMFB6NOfzQz98F6Hgo832G0EfdY0ZuSRnoaXH7Fybd4epQ==", "signatures": [{"sig": "MEUCIQD06qPqV02e6ojDTwsvRjP2TOpO+3DXUf8B1CZ2FfY1FAIgRzgSOaq7JHawpuNT2HLRDsXv0s7ypXYztSCpgsq5W6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43061}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@sinclair/typebox": "^0.32.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.3_1708427327903_0.2307752056880199", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/schemas", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "1e1db12f1794793340619250db9abec05708add6", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-/iFNfQeWfrbZOz4AQ8oC/ZD45tgo5fEutKEDO7G39Mh3ZyzSaRXwo/JGWsRXOlOUfPOQLtvQa/ayeLwZvqM8LQ==", "signatures": [{"sig": "MEUCIE9rW9ZZuNbCzYnhAKH2OpRTPg35YEGcfQn9/JoNPlOcAiEAouUuqz4C06IP7KTwPtI6g4sEnQ9XOVOClJyV0B2aITk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43105}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@sinclair/typebox": "^0.32.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.4_1715550194236_0.09251906834069268", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/schemas", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "57577c9360d20890b32c142f58b4f464a295229b", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-um74b5FzrExOuilDqOEcBS0C3lAhR/2sjyNPjLypOkksjtmUZbn9oJTMJ3UmYW5CHoRKOXVnjU/CrOtbfFqrqg==", "signatures": [{"sig": "MEUCIQCysNpKnjY5OLuvvHa9hHlghOgrPChIKO7+7gxuoNb1JAIgQa9ss89SWtTEpyKjutXp/trGW0XTJS5steCboDCdqYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43105}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Experimental and currently incomplete module for JSON schemas for [<PERSON><PERSON>'s](https://jestjs.io/) configuration.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@sinclair/typebox": "^0.32.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.5_1717073032850_0.23043638863603166", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/schemas", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "5ba845cb36ecd6375a6333a42ce7ffaf3f3fc75d", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-Ukr3kR/VsBq8+JHU92xArhSJeFQHVHs5T1laPO00GrrNzv3DvoHn3/EVVagGn9CHbLeAyJHXFRHYxq3+520kiA==", "signatures": [{"sig": "MEQCIGP+xsukLik95PK6/J8q8KUK6Y7Y8dYHcFlw+IIDmfl2AiBCb3H/+aHkjUDqQ2SZUGpwY9MTLXBB81sA3iCp21Yk5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43140}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@sinclair/typebox": "^0.33.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.6_1723102975861_0.5397625738230705", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/schemas", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/schemas@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "09cf5dd23e14c3822773c9a4dcfe31d483db6005", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-fzJDwdg3E8Gq+Aqo/h+JQc1ZD+Mqbkz7OVqpoUa0A2Btd70zdgu1Hza8AyhJjr83paRQ+ZCHnHepp4/+tpqDFQ==", "signatures": [{"sig": "MEUCIQD3TuzPSvUpNQoPVEFeo0+tWv/ajJAn6rIRxtjo89kAiQIgWn2ItmYCS1xJ4wIysnTk/ix8XSQ2q6mNDexPOfZEbLo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31844}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"@sinclair/typebox": "^0.34.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-alpha.7_1738225702749_0.4810290704979099", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.1": {"name": "@jest/schemas", "version": "30.0.0-beta.1", "license": "MIT", "_id": "@jest/schemas@30.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ec5d5b72165a9f2186524d87e9eec3c3843ed497", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-tEX9KmhFaAzyAEjqhTQtWEPQKUJ1GxdrXtSpTJNGBK6Hlh/6cTs3LAwnlwrrh4SLqEh0LVnuAC+jV4o49lVIwA==", "signatures": [{"sig": "MEUCIAeTefAPOV/MwsXPpHjMrgiVQu4f9v3JUPLX1JUUlgK8AiEApnOO6mH5LxhiU8cja82LRvnYRwEjADy4O9o+89V20JA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31672}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ca9c8835e3c74ec17450cac43c7cd3e8bb5747b2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@sinclair/typebox": "^0.34.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-beta.1_1748306894710_0.9449391213431568", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/schemas", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/schemas@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "109d5786c69f5f3495ab0a5d06e203379f2d5ea1", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-tiT79EKOlJGT5v8fYr9UKLSyjlA3Ek+nk0cVZwJGnRqVp26EQSOTYXBCzj0dGMegkgnPTt3f7wP1kGGI8q/e0g==", "signatures": [{"sig": "MEQCIG5Yy8etKrIwZANbA8pR3iJ4INhifxsLRLXmuFvxXCApAiA4kx1Roy2Yi6r2bk0++wY4AjG5DzQExL0TuJzNG7E+6Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31672}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@sinclair/typebox": "^0.34.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-beta.3_1748309254952_0.4293230029031667", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/schemas", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/schemas@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "130126a86823cfae8c063815e40fe57b4ccf803a", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-WKpxkMaYD5aTDGoOEAlsoIMCd1qRNV7yKxwqj5yfXNorYQw37S1tP24408lTysI7d8QlwBNJ/7S8CnhBe05rgg==", "signatures": [{"sig": "MEUCIQDGWA+Kkn1AeRdovJV5TrfXvLPbUvJFeG3GvowXLMvWiQIgHjQKAYm942Qwb+u7UDgdEV5O8dNnDZqdSvB6R8FqQNA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31683}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@sinclair/typebox": "^0.34.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0-beta.6_1748994636997_0.2596885326782705", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/schemas", "version": "30.0.0", "license": "MIT", "_id": "@jest/schemas@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "427b862696c65ea6f6a138a9221326519877555f", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-NID2VRyaEkevCRz6badhfqYwri/RvMbiHY81rk3AkK/LaiB0LSxi1RdVZ7MpZdTjNugtZeGfpL0mLs9Kp3MrQw==", "signatures": [{"sig": "MEUCIDHQ+ThR219Xfq3briMMvZ8zk78RRwJK2wBR5lw+a/qZAiEA+zBDuuaNmvMUqStMVOqcKdAAHctZAqe5RUzXVUl9Rf0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31632}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-schemas"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@sinclair/typebox": "^0.34.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/schemas_30.0.0_1749521739597_0.3397948018990109", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/schemas", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-schemas"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@sinclair/typebox": "^0.34.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/schemas@30.0.1", "dist": {"integrity": "sha512-+g/1TKjFuGrf1Hh0QPCv0gISwBxJ+MQSNXmG9zjHy7BmFhtoJ9fdNhWJp3qUKRi93AOZHXtdxZgJ1vAtz6z65w==", "shasum": "27c00d707d480ece0c19126af97081a1af3bc46e", "tarball": "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.1.tgz", "fileCount": 6, "unpackedSize": 31632, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBXO5MnxWv8rjdmkBb4a+WD8/NuZ7uJLA9FJgTPoFMnXAiEA5DWoye4UEMgEGz38F1aV1ZXWneGAcr+5z/uQbMGDktE="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/schemas_30.0.1_1750285877366_0.8247101559218961"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-02-15T21:26:48.741Z", "modified": "2025-06-18T22:31:17.856Z", "28.0.0-alpha.1": "2022-02-15T21:26:48.943Z", "28.0.0-alpha.2": "2022-02-16T18:11:56.509Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.121Z", "28.0.0": "2022-04-25T12:08:01.981Z", "28.0.2": "2022-04-27T07:44:00.188Z", "28.1.3": "2022-07-13T14:12:26.001Z", "29.0.0-alpha.0": "2022-07-17T22:07:05.901Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.313Z", "29.0.0-alpha.3": "2022-08-07T13:41:31.947Z", "29.0.0": "2022-08-25T12:33:24.591Z", "29.4.0": "2023-01-24T10:55:43.429Z", "29.4.2": "2023-02-07T13:45:22.046Z", "29.4.3": "2023-02-15T11:57:16.394Z", "29.6.0": "2023-07-04T15:25:40.566Z", "29.6.3": "2023-08-21T12:38:57.773Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.736Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.190Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.595Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.063Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.393Z", "30.0.0-alpha.5": "2024-05-30T12:43:53.010Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.009Z", "30.0.0-alpha.7": "2025-01-30T08:28:22.926Z", "30.0.0-beta.1": "2025-05-27T00:48:14.885Z", "30.0.0-beta.3": "2025-05-27T01:27:35.196Z", "30.0.0-beta.6": "2025-06-03T23:50:37.185Z", "30.0.0": "2025-06-10T02:15:39.776Z", "30.0.1": "2025-06-18T22:31:17.540Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-schemas"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}