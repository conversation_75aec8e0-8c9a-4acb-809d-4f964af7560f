{"_id": "@babel/plugin-syntax-typescript", "_rev": "119-8afd7787cc5a7d14d5420eec5437c65d", "name": "@babel/plugin-syntax-typescript", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d850347e4f4dfa520a0bf9460f7c3c9f4f3e5e63", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.4.tgz", "integrity": "sha512-XQyQ3Ngl/hQMm5b9Pup09dxMZ9aPctJgpNgGJcFgwOlzbn5k1d//C/PGiNstr7pSc1KpG12DvlukmRSAEh4QWw==", "signatures": [{"sig": "MEQCIBq++n9gw6KPw5aeo9EI3PrTlGj2+gdQtZcuAvW8SkDkAiBjJ5JSZEJXYacu/H0lOe7DGduz28StwqC3rTeL6vXRrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.4.tgz_1509388470028_0.49570025061257184", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.5", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2784e1d8e6f1f10904d18c2769ea055a934bfb3c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.5.tgz", "integrity": "sha512-kNAN6FC02Z4l8Y23Zkw+obBKvAmow4+xbE5U//xGUGi4sl2g/iLusKRPS0qdooRMv09WLU4Kwy91FSfoejU0xQ==", "signatures": [{"sig": "MEUCIE/H1HCpvJJTtCMGywGzxPdizlASl6SaAbhBRIDpRGxEAiEAjyT4TDlGInlHHO3mCsJDzyWgYQ+Pk01D0C/hnY/4YPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.5.tgz_1509396971249_0.6539474672172219", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.31", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "77369b541e2b6e83892a3476b13b8744e892e527", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.31.tgz", "integrity": "sha512-616cxSKNVYDU4nteZEbMLzVvrY3f5ooVCjCj/CUgcz9k4Ohd95domNpsvgkSoiISZ4Mtzr171y3bwbI2Z2d7rw==", "signatures": [{"sig": "MEYCIQCuqLHbTAz8UnvOdQdoT92u0ceNxd11LNLdK0Zt6J+m+AIhAKieMSFgKcBG7IhZ2Cy5WtgvVRSsVIO7WgUN4U1d+MRq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.31.tgz_1509739400480_0.471153850434348", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.32", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c2791ac1bd865ce3240493351be07e45b13122c2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.32.tgz", "integrity": "sha512-A5vcEi0YBc/tpmobridyBfcxFg8yYxcjs5So/8ll3ogqHEghM7BPcegWmO3j+bC75o1sspjqmhj8teyHqg18/g==", "signatures": [{"sig": "MEUCIQDW1+SvTWGO7DWrUAsHm9ydAuyF6GM5Zmri/P6ChGZdRQIgPirVh1eZsfixZu/n0QfFUpGxXq9q8Bgj2kFAoQJwTA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.32.tgz_1510493588389_0.24508299096487463", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.33", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c20ecf090a097966c69fa4de079f30afa8f8ce9a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.33.tgz", "integrity": "sha512-HJixkVCZD3zQjQLQNFr4PmFGYIPOUxqXC5ZE0Zyo+vwNxgGhG7XBVIXTIBy6GYSMiOPaZpKXO1TTXNpzXzBqjA==", "signatures": [{"sig": "MEUCIGAv7qZOHAWmX+G8yM1At2K31auCXlkstiFIvpIORghqAiEAhr78saC4nn3Thl4TfRePpOxyn2ZjYEVkwV+V2HNXIMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.33.tgz_1512138490954_0.380929579725489", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.34", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a290cb23a8087cc2c8a124bdf01e8d36ad863000", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.34.tgz", "integrity": "sha512-NdCmEgdlyDfX0H3OMz/DuwZdHAxKBWJEdr3kcN2uqpP3W2ciZbu8GhQ+Tz+uDgrj1pwOSgHpE7WFnsXg+XSzoA==", "signatures": [{"sig": "MEYCIQDR3QZps2uKReke0XtK/4hqQ2Ha64MfkYgrmmHGEJUBvAIhAJKlvRGmla0EsjS8Ogo0orvELjnCzgluNVuyyFrBaEP3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.34.tgz_1512225550849_0.7169859451241791", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.35", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f47c5dea85d3bfa992de9e3070b6b19be9c42a0e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.35.tgz", "integrity": "sha512-OufioM3m7MOkjO9Arz8H2cdWtirtBewWPiJvzgcfzIEICTbIgtBGTu/9D2CCrUAj7oyTHV1hiiqsXrZXnQOWXA==", "signatures": [{"sig": "MEQCIGuuM9/EqSzeqFmOFpAe2leClCYbp+imAvseAMF7wCnQAiASUNCUnYy+47LgYxet7l+c1SbTaGIkKDApcIBYXi8K7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.35.tgz_1513288059022_0.9903956514317542", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.36", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0b29c1cfa090922b3d05e0c985d2102950da15ab", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.36.tgz", "integrity": "sha512-cAtquXrQ1SXPXwkPPjCpzcsSF7psE1I/vrrBM6fQripfCs0Mx4c3YP+N1SHnq2fIPZTJA+fjtNDhDvmS4sjIjA==", "signatures": [{"sig": "MEYCIQDgTUCy1gNiyGg62IylJLfciykkfg3geR5/0+c8MZLHMwIhAJyJdKjcDeIwAG8uJw+FW/a/BlGSYiBQB5XOR1FkL0eF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.36.tgz_1514228669712_0.6584230551961809", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.37", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e2dcc2bdbd30e4333314fb2003fb5a7b34f799d3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.37.tgz", "integrity": "sha512-dNRfnIkxnCZndYGOqpouKJPxNyJUyJdbjw7CyReTu9FXD04spigRulcDpeIBjCSeaLwhKb3+N8XT0gvJ6BdUYQ==", "signatures": [{"sig": "MEUCIH6cMl1vpQIF7UvAg5Nvha34QTrTURCvE2dh9ymert5EAiEAzdoPPh4rvQKrUA6w2zg88tAf52S0I6v3B0Ngnus0r3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.37.tgz_1515427346324_0.4482850646600127", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.38", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c62b1148da53a071ac8e8013797d2783eefab631", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.38.tgz", "integrity": "sha512-QA9XUG4D1UEywrOxBds5RhkL6hZ7k6MvxloiTQBpwLl4yt3nSZRzOE2E/L1yK4dVnH15+Q+8l1uz4B8q4noGeQ==", "signatures": [{"sig": "MEQCID9YwX9ykvJYsxZW3qWzPvNfmn8WoedoZGgUwGWqqmwcAiABC7QH7FmP0MVg/bNXsIefAKdc+rPgT8kEfDzUhkQl2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.38.tgz_1516206707117_0.5201111796777695", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.39", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "899d8c6e0c243f4b1d4ca4b687492b0b12314063", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.39.tgz", "integrity": "sha512-kFR7hGT/EH1ssVmCMzcqDICZZPVCk2Iuf4aUjZ2JRWdVz8lkLId1wjmW/OsBLKRByutgj0xkgSeMxvdo1PBRqA==", "signatures": [{"sig": "MEUCIEQQNRJtkF5CcqZGyhlIcSUirEYfTeyf4Or/rvG7xLDUAiEA4Nm1MBSE2mBtkt7uPrs6qF6CkheXAW5sLvaamgwQO0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript-7.0.0-beta.39.tgz_1517344049458_0.6172956216614693", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.40", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0cc8a5f8d03e5f5187e759ec8beb44ff251992a0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-o4xi4GbvyV5sG6vLMahVMQbx2qQYpuaC4KCZrwTko/Pd/1ZTO3cSezTgWeTQpCksjt3ngkuqbvvREWwles1GEg==", "signatures": [{"sig": "MEQCIGGJmFq+dPuZabbkmK68+W+KyHZSO/dFoUxWsn/5WgXfAiAEL2erud2quOZuSKrszfkGRZusqyJVN0o9RnrH3FoUgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1190}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.40_1518453688891_0.4701336816592141", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.41", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d188e0c2cdf37b60062c1a81176b05e795c3e5ff", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-5aZjjDIG3HXWCHtAF6Z+3DPqCjcpKdnaY6jMA/XCQko+zZ2XG0gwO8S+c1RW269FPolWES1E37CI12TsbYBaNQ==", "signatures": [{"sig": "MEYCIQC22oYf+S1yyhokw2QuOJabddnAWKpIkMrr0nYlt0XsUgIhAOUPC8PaDisVYajEsz+FqYDo/9ewa5J7brJmU7YFtTHP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1425}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.41_1521044761158_0.4607642993336398", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.42", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ffc42945ca15e5ab369de6b9f5d9324499c623cf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-Z<PERSON>ucpOPi8bPOtszKUeOCzFKsH+u3HsMKU87dP7obMzlQXuOvLbs20euk3GWkPY+OQrGzDo1g2k3SbwLgoTRaeQ==", "signatures": [{"sig": "MEUCIHOseR0O1HYuWnEV7+GueIBOdm/Ai0pQ/1icUbEbvAfYAiEA1H5m1U6AJaEf9bF1R5OR6pXm1J1N0eFmuir0PUmAsYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1425}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.42_1521147037268_0.21762502885538804", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.43", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0e49a3c826af0f180f87efa9bd63105d33c5b4ed", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-S3fcel5Clz7l3JfW6VnRwt4kvgsQf3BL2QDebD63sd5AsL4yoaZV2gVxuR1UyaIzmiXeZhbms2IdF8GVq3sD8w==", "signatures": [{"sig": "MEQCIGgNM0Q7GtW5Ht8DORfD/oSjpECHtKC5s8CukN/C1i/JAiBRHUoOdL8VWc3fqkjRQzXNIBkX4g5TWtrmjWTFXPmTDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1530}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.43_1522687700582_0.582140674449076", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.44", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a2f1c4963be673ad8de792ba2a940a5e0c0e598b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-3e/R7HoWD30qgUHSGOX4nZCvvgecep5/Gw7BOjM318pp4myHMPTiZeLvcBaySHpihcINUTIC7iqqlfdcA3YW2w==", "signatures": [{"sig": "MEQCIFnNFZ607hjU4yWA+bhjbvglUkcKpF8BW3uPkPSml4feAiAIbx4t1l+rd61YYloid2KRs+uVqBxo9CH6ea7u3x06Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1581}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.44_1522707602653_0.6059350045773932", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.45", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "40dc0a8929255b5785f43a141f40ddd347535bb6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-LbqodKbfB+QZjBHJv/w6NcE6u1QqoiiTgYhxnkWKdRJW0vwywq94oY1G/6H4d53wnNQq7vlgdSfp5Dm64Yw9Tg==", "signatures": [{"sig": "MEQCIFLe2iRye24mcuRKgcKnJTrVs+m9WF4aZuLrRpzXPuinAiBmsR+4oPJdkS0dKRk5hImI4dRWBjWLPJhQ4RM59OXMNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1581, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1UCRA9TVsSAnZWagAA5JUQAJSf12Z1+dTfhbmcDF3m\nsOKScGHGg2xhRAmN65/2Tmm3QKxDSuT6lSeyFCwY8p+KdvzgfzMGXuBVr3Jp\nzpaY1ZinRyJgxDYiyVMkFL47ZpDcy0ii8bRUuy0W8XRkkmAtf9ICTCqLaemP\nMGZtG2X2a7ca+TMGZWm/IIE2n2TbsFmLz3GWavWBlXliYdzgjwu3uaQRs0og\n4vggNWZRoPvOWmLZr1I4rHvTC9qsGDv4jMSu90nihXypGeTap92aAB+80pW0\nWDaHFGvcbmABjyT68dBHzSs2OZ8QFLTg7dDeX1/wZyxaORFumdWTkSua66IY\nZ2HU/CBiGzRP3kHUk/Nm2sDDnafuCrPhK4v1VYPsvPMantiPq+e4PE5ardPR\nVfMa+10rVs+cFiadBkYeOQ4l6SGjVn5o/Kmb4IZPTUb3830vY3nL8JrBxlmC\n2lH3MKOAD/0jSkQXt0gz2Sjgi0j9jP6i2F1syC8TXumOo6qgdc60a9Eg62MU\npMWAphfAtwDCLEl8hfx4EVnSIyyQsHNIrRptMeqnLnNANSgIH1dXxVjpXg35\nftADJM4BmfpA0AxhEVjAEI44TblCLFgm2V1dfIqpDiuVzx2/8EJcVRiCNvlt\ntOlxZHv5CPRazQx1yKoz7+kcKLc8u+DT7FgmF+FgJTArG3xsXv53Pz28nGbu\ndZCb\r\n=6Ovv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.45_1524448595915_0.549776525413334", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.46", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2a9e0e1f3bb3bd918571c5ee4db97bb2b00e8642", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-TAnXKBGD595lhW77JYj2zY/YQDzEnMnHYXxAyEbTar0/5yhV+Gx8fAgVzPy1Wm4TEKzGATMnI9xS0mr3kJiFZg==", "signatures": [{"sig": "MEUCIFjEE7zqXcjhz1lcQsTMc4zeNSWc+zfsZJWxlsqxLmR5AiEA3fdhwxGYQY9cfH0qx6LUpwaSUxcqaZvg/Hnn+IBobDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGDCRA9TVsSAnZWagAAnPQP/ApnQC9B9jNFUrOrkzRP\n9ri34h60cnwaStoTZVWnfA954Glf6n6mQLIVy3+TRyfiCv6eUMoq5JapqKFZ\nRIZ4jI5Ldz2fNbO0Bvr9Y/gTPXwmZryle0kVi9xfyFasv98HIjmX4kcw4lBx\ntG5jjWTkd2eeq3uBfIyRrNIc9S0g+Xcwdk5NilHR5lAcrHi+ylM2YKSmny8C\nWm1LDqLeKq9R7ZxtNoOw5GnBp8G6JCXhsgVQwbEeKAa6+uY3qc3NnrN+p9dz\nfPK3q0aRJHYtJwxsI/cR21rgu3dd5o+lTolWTJTcOS/ZSVpUqjfCST6dm4pS\nBs4WHyHRJSp7FwawS2OQXVFsfPyOje6TZFk2HJMAADVJ8qMyRs0/O9uejRFF\nvTkmd2uhqlmLj7dV6l0SmLPyauxmxkC7K93vAPuFcKM2KypyV+Z8GMAhYRgf\ns7VFnMbLIXSvRMKGrdDKXJPdZtCSn54fNn1o1vokn6VGQuyl6ieuIrIqXIVV\n95d0MQhaax/0/DkiRC9RZdZyqR/ook+pBWxND3hawaFj9QknjihIeOeYcpHX\nJo6TGPKGaVKDXc0uReRm8gbBEY48ij/PNgnOm0oLwtwmRFIsHjJ2ufgpqumV\nlFmA63Esg4ntNk4LK3mLxi/LpmYgd1HamlBaLZrC2ydDB2dPkhgpSYdskNQw\nk2dk\r\n=eokx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.46_1524457858792_0.10891449129979902", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.47", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "108d4c83ff48ddcb8f0532252a9892e805ddc64c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-Xjq5oM92Cb6R1EHBYXPV4v15fm7m4uhBiBgihhixnBRjqVlTVHkEHr8YUlUYF0fdO2plU5FMT3WKJDVKKyl/GQ==", "signatures": [{"sig": "MEYCIQDP4XwADLBssWeEZOMWGpMbo2gmsCdLPwhv4xz+NyOB3gIhAMVlTrmgpzFEEIXXBXuBUJuAAfwQBbJiy3ZFG6xTEv/1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUFCRA9TVsSAnZWagAA9XUP/jKzG+UveByuj8jWkv1h\nCnUWOslZmDoF8MoK27BWKOb7npuzu+4z66h6v395mY0xIuthmfWYfhjGfFlw\nbB4DvunU8h+oJjE3Oh97mNWVu8AjpTeHyec+imhgfQUA4gitgl8UC4cgdW71\nWCnpGaoD8a2JVFmeTilH8TsnROHc1kS948n3boXNZcYDH9/8zEZXfLqD9BB1\nF1gVeJFDh1kqfW16GiB3e+Zp2xe7YpZYpuBlRcXcjk7xyf4vC/BmW3d1kPHF\nCwip8pPVxzEkM49hzT4XzhY5AeUpDXK6wZbYpC4repw45USmhsVW6KgRATQS\nav5tyzL4VuAPqr5G9XjmJFokdrB1XZ/PAt6Dk+ipQsTCW521nSelgBP3Dc/y\nTls2UjwHZbk72H87FHeIfLuduDSGbPlR00Id7tZtLW7gBzUVbLKQN9OX/TRK\nv2PXqje0AicRSN7VNrsqS6X1blmGo50H1vaAG84TJGNfOulf3fvVYtTa3ngD\nUqvKY7XuWYW7gsitXlM1sJoKPw78GFiA1DWY3hrlqDkSWupRlZ89CGny1jkU\njfqcwR2ThB5vqTl2ah8XzbUxsXR56ENyt+NTSPRS4m7PZ73NLV8H23KtDDz2\nIxaLep9gGg1nlXOkcB61kJgQOijPK69fjELpc36h8T4/cQPTfCCtDfHWFX50\nchU5\r\n=9uDA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.47_1526342916793_0.6298246075185565", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.48", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "21572159767506d2de96e41288dfd204fb060242", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-HvCKpXvk8AYwI1vOLF4MhCqr8wbEigPrC/TtDWXZdVgAR73aE1+WZAvF670cP5jVEZF2s5fajsCcCZb9EaCmSg==", "signatures": [{"sig": "MEUCIQCgL1SZliKZGLcmGwzJ+CD4C3UXnZ4eaUjWSisjRHsISgIgBZcwWdSIn4nlk1+nixTE6+GwV7BG9asoIDpwDXq2Vtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDZCRA9TVsSAnZWagAACUoP/jkz18BpsM3HeT1Mgpxt\nwEixuF4y30sLSHFDznpivZIXv6RpJhvOqhKY9/grI3u4MlD9tKnuOKYIllI9\ngmEFB38PFy/QIBn/5Jf5sXHexa8SUfP1j7MGC5dfLXeW2obEcLO59KmtHeg5\nPuQ5Fsfx607UzbkObuDEpiwcVPV1TH0eieQ6cnHdcJhDPsSNfwMs+nDKY/gN\nXmn7Z39HZFWPcSm5SJOZHNjxq+966DE7QqWx7nVe6YMAlarq/ci3FUbw13/p\ns4sxVTgYw+rNZMHhhOiIEwFJXCM/se0fKq5WKuuQaqVoWp8O/qOF/LTetWaN\na9pFUiHHeIR/QLZTasFu8KivYAD2fV6hhpb+mTPaVGDfYmiKlsY00mgNOKMR\nXXdR1BIu/xdCVoz0twb9VDfONGRbC/pWbjxrKhThdZK/99s3e7iGpFEK/Rzd\n5tQly3gMN5vaQNldojZ5rtFXfhy8oOZXWynNXyXKpQEu4GkE/rOOZHv80iVW\nyaquU3Wo0K0K7T79Im0A5G6zbFD3pPojcu7JiM1Z3EOzcvG0f0q+/s60cPfg\nCy13rHMcdYAx1P9lZPloS75q/o42oHDnpmUKRCq0c2IJgVRuzu54EBsxJqR8\nQ3EkbBx5URSTeiywwnKJXnqgkDnCIu2Mh3QMvuaDQJrT6DjfeIq538LcJg+s\na+Aq\r\n=TxZl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.48_1527189720793_0.23284949243241093", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.49", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "332b6d17c28904981465f69f111646ef7a5ede10", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-HdA1fH0NemDA229WnzHkmxkpkbwVyy6c5yNwQPGnVL8lphtU5zM17LIEYmI34DMvFYHMPsACRNLT4SlMDfHivw==", "signatures": [{"sig": "MEUCIB1Pq0zLzcgK0riF7AIVYAqtF/0ZbewJq+PyHf6b6TbFAiEA1fYrSE/3ifqktqirbqEPUeU/oOILpnsF8aOGOZB5ZyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNoCRA9TVsSAnZWagAAqVwP/jVy7YETZCmG/2s62dAq\ngNJEFdwtWF4yjYHbOM742GTFoyuh5ugNkcgjsrwcaAKP82mNtobcctayhi45\ndwUky+pUZq6oqruOwp59YWLxNLJVN9pIdPXTGtDdixOHglH8Ya14mCYIZw04\nVtvtUs53gQ19dH3sSMuLvofr3upF63kI5LI4nkZIAUyMZnKCuHU/NZvVu9HZ\nG2t1CJulg7ij6wj7pk+mX8qzAfKbpy2TSeORjkBZFHa69yuTsBNqzrZrn1m8\nme92Ezr3wkiX/bjw9Yi6Q4aFgBE+CD80e/j5qv49xqMvnzeSOwJ+TXvXu6FC\nWJ2rDa10vzopNbra9ewFSrrDNoM3Arf6yGafkcXOFEL6kJUavBnIhY30vp1L\nxCQlIAYGZjHMvtEjsWJEQdIjVv/lwSFYf7SUtknkYcYSBYhe0v01JqODn8Ob\nEJUnaW7meou2iE/A3NuqW1J1y70yei3I96rCrQdTqDuniiau91+3v5YPcgro\n713tx1Dq4tYZ9I7IlDa4sl+GaFFvLxbj9lMoVQw2Sswf71Orj1EjDH9XEEUI\n52nDTTh7ny+R09OXd51phTUjW8SgCGp3YyyoJlWh0Hj4NF1mWRhlGqhaGRF/\nG9zy2i8xCrvrkwZPG+tV7UbnHPuu3nTnbiVESyMrxF/GiWf11jRMRsmvAsmw\nJ5mi\r\n=Cbhi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "332b6d17c28904981465f69f111646ef7a5ede10", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "3.10.10", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.49_1527264103781_0.9530370860124451", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.50", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "478c7bfdd16365929d02110078633e93cf4a6752", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-X0GBKpdX9YaXI9IbNsKXcokHe+HQGQhhGRLyHHCOHoCWnm9sKBM2yWmo8xkyV7iIMGl7hHjsG10kN4G/j81t4g==", "signatures": [{"sig": "MEUCIQCA9T803AKPdgqiaf13ls8/7kvcDbuCDOBtbimNEZlj7QIgP7rZFA8UIOzMuM65seBBTXxxS5d+gMyx/lVUPnL06i0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1941}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.50_1528832830717_0.3806837734953663", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.51", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a2f2701ff65d006268cbc84ad1bb8eeab638b0c4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-LKCWiqzwm2l8CeBdl/JiNE7N62VQZ0HyzdKc6V3JlW4oaoiyT643LYS0UshKrzDujKv3s5SLSBQDjvtAyukOGg==", "signatures": [{"sig": "MEUCIGTQ4mBWqax+UEePmcqSUkoab2jwfSIXAsX7bLt6ozixAiEA+9hNiI3frnbyF4OQJLnZXsugy+mUDpXTNzlxYgqo8vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1955}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.51_1528838379848_0.3235461228681009", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.52", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9864f409a692d051a76e19828036234325c752f5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-XJzJsWrev1VTZJL01yLOtM/LpGNFvITktgoGG6Nml4nYbMU5ZUkom9zkdAXWDjNAh71jt2Mq1d4l1AEn6ao3Xg==", "signatures": [{"sig": "MEYCIQD4A06eaKDmz4kbd/dn5wiUHRm9QmTIENdenOAXKNWx2AIhAI3g/PTg04WFTTOnfmDC0jD9DOg0xsnN3x9aVhiYsBAV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.52_1530838762172_0.08060277816542327", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.53", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "575a163cb144b094b4f7240e434badad831ec455", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-K5Q02ydG7eHhclOw6S3kBn6OpobRka8WxvX/n+golXW2SgBLX0flQda2BUXv9feNfqnzhFRlDfBHzBC7PUGquQ==", "signatures": [{"sig": "MEYCIQCbyt54n0AA9Yq+ukFoZyEcgdYNpxTEtYEWeVupkrgLkgIhANcQsKJM3kqGIHnDAJEWUhE/SUCKjExSBPQfvg+Z/QI8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.53_1531316412356_0.969003749903635", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.54", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7b01ddebccba8f78693bf2898e1f695bb8a76a7e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-GnkoBoPlKSK/3Ddcx0X6ovBMXEZlrORWRe5l9Y1widRMPEKqG4sYKad3JwwRLtJB8Xje+rOLGOjBP5+9O9FUfg==", "signatures": [{"sig": "MEQCIFNYixtQeECLy4jTjnr+6+WgcMqleH7cxIUb2iIpSpCqAiBvuJjrfOnPZBlRbEiyrkaMcXUPk8MoxoQN2JaurZcj+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.54_1531764003211_0.1919436053660426", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.55", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6648707176c44da87e4c409a1637941019a681d6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-e+cEYCqSBaq5NuHPg6+yXCG2XfB7HsAa9hSBS/8DNrK0MNCUXN1GzyoR7snPHXiTkdIvY9FsBmEdEhiTkcSQGg==", "signatures": [{"sig": "MEUCICaC/t0SWkxwrBUE487a5QSN03dYR0+MMTJ8wdsPc0auAiEA4yBTLnQW3ixR5yjV3xg/XCBFBhlXC3wmIQpwTdnRrY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.55_1532815630615_0.5821593715447189", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-beta.56", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "836a166feb51f955540f815bf936f99e17a2681e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-mCDWTjWAyen93aX4mayxmkmhsSv5TfXNMtIphFfyjEsrqTYBgIiJabm+vTzgSU2rtHuS+TAgEE6RnFTuaIh7Hw==", "signatures": [{"sig": "MEUCIQCxECvb0P/N446dv5K0xP59fwhlOU0otM0gBXwb87FQ1wIgf4eP2vD+S1AkT13zjX+FB6qd7lLOOIQDHC96ZbvFAVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvDCRA9TVsSAnZWagAAOL4QAIqSZ6rY+MUtSobmw/Nu\niObxh9HvOHMiSdlfq3JQLaS7lk0WHl1dhOyRB1dM1ynZK7fS4QjwvlPBn+XT\n6i6WbVe8F+dUaUYzkFSMjexLErOl57PiWEi35XJ74Po1fX4t+azeIUb+iYKI\n95sbl4FgS4xwnNcWuPRDJJFIvYk6hXpjC9EjnymkWWZC5+xYsJInhFsylG6J\n/dDk0st0miiUh4RW2evcH/Z1DIzfkDcvQozsqL/vpwjsMbNDPLDLAyKTBGph\nI3l3n0UXN0MvorsONb0ykUprXJ3Gwzt3Q5IbYG7hQiitG1Zil8ku2xmYbCD9\nFjF3CQ5+mzynibLnTMSqvuh4wokgiUDR8N+vQQl2FFb8LjyEF/kpNNizAIhO\nH1cXTvkSG2OqD6XuwdE2f/GlFPTfBpyS0BTF97866uEDFTpmnaroXHXWVML9\ngOBBU95tg06iHi2iDRvP3dkTCQNebrCgngPCu/TfKLHj2jrzh2aDxKjPaxGJ\nI3li4csVRlMygZAWNaUJMWhvURNvgz9CFfAFs3F8YEKf5+agvvatuglVK3cc\nY65ZcVbYhCZmVw8bguJ2+sXFibjOjwEY4SXjaiqsNBd/cfbOqqxUoBPe9y6B\n12FsXId3kcG1xuUE575AgEwEar1ZPPthhdtsy9m1v2/CUSjWcfBj83NTXbHG\nPVAt\r\n=CHaT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-beta.56_1533344706687_0.6770163593820011", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "aa5caf5e6230da4b1720944f3698f94e3b369e20", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-W45fw4oCvn21dQVx8xzQ2x0X6FuhWh5ihdFR9MgPvMQes7OckiiSDDeb2TgkflTh5ZVBEi+kmwHTe2H4Miw8yA==", "signatures": [{"sig": "MEQCID0r1MAa+ELYSJHLjAZJPeVwu+E3rO7Mj/lx0zkua8FfAiAw77bMHFBtYK3SuMOKKaudu4A3l3Ul3bFYEwiW6yqYRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSGCRA9TVsSAnZWagAAcLQP+wWlsuaiBG5H7gJ4Sl7X\nk5R+hRFqyBjtTPghXmzz5MIhrciroP+X3Bzh5oXSntRtcGPrYVaUIRUleq1J\nrSJ9RsgblsATUCwXadxOuaoEFP2EeG+7h3eQqEHP6jQEA8h+xfJPImucIBg/\n3Mzgt8OUkH6NPsu8N1ceeQNUaF8KEhggUqdpQ+DS7sYINHJgD/Y6XROHohWJ\n6FR6uVAPjLyx5CR3xAYSJVmz33/IoV26HAg3JRm1+o+Js9VBbsfWUrcOL4M7\nfzRWsgQ8pYGgrO11SAutDjSeD3yTblbFInG09EA3lEdDDU4v41F8O+zQ+f3J\n5TgrhHAsAW3ZtLTouaPs0r/To8aUk748F+7JGj4Qb4RQEKzjm5hz2rMMeMGi\nd+6o43iagNVuFEg/leOLAkL43Zjh9elm3SGo0PkPuOJg6RlgAHDp2Q6dLupG\nBdFsIWOFyNTS6VVuxJ1ra01hPnn1C63c2QBu7J8bXPS6K8ItKgI974EkY9A9\nyGSfbVHbVLODqDvLJjpH6zP/mCESJGi3sxk9jXHMCpvaaIkO/j/9ozEO+pu9\nH3Cpan6XaiqRMahUXVuzYsk4nk9ZYR28fyZRKs+Fc23z0LDfUhlcdadjn2Zf\njB9SHDNblhKNyAR3HN6tLfNaZxPMNRlGmuBgBU2txzbm22cf9Rkk8VnPsD31\nZLe+\r\n=Hw6y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.0_1533830278238_0.1187793290297865", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f1e0afa67b62aa9659cf9995a8a46f0638faa05f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-eXhu1pl+xvQRdMdZ2wSwZ5KWl/ZmqkJbAD2CJwqJzrJNXjMWXxsBUZBrVtXZ6OKlIO7sBVh/eXu4npUj3sCnJA==", "signatures": [{"sig": "MEUCIQCrMUe9oIAOimBK6tKKB9k3nZfFkYVmCK8OFggbYG4yHwIgWJ3dMc6LJHYN80Zv6j/8Ng8rQtqk0urKKu+EeEIg/ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8QCRA9TVsSAnZWagAAsy8QAI2wgjDA4duIjPF4y+bJ\n5lrg9E4wKTakG+hr0vHw1kgxhaJb1WBaZX3CoLapGikkgOskD8Ac2+Pw1Qiv\nsPZnwc67uyYjx7prvQ2vjPzjE7Bs9o22aY/15nwnxy5zT9T/ls7r5hzy15y+\nhVUX6jCDGtGUxbcJcB3ieztjDCR11ZeL/WoANwTS7JORbv8ko2S9nKbKoFSc\nA7D2qqXy9IGzNeR+3YQiT6Wqq1njck2FleryAeOtDmA/ERWU8aQhrGpl8sNu\nCMqzTG10zJRsvPGi1VERdcu5Q/63MBHcNTSkMz9Hh5KdgaMi027sDOh+2Mh4\nEWbIx19+VaJtsqBtTjGsRRk/1EgZ+f9sKilgZrJGOBrRsrwk7Kv2vtZh8JcW\nKyKD0x3gz2sPHw+ett5jhqXlmcY2jUiy6nxVu0ZtDmuLh2GmzrK27V9DFlo5\nDsjTXv7knbH1UykxM7/tS8+TMxIjUom9KkdjFOK+myVarKc9vV53e9CGMRnv\npElEhiNivi7VwFw1U2tIu/7o/DcBXJ3L9R8ettQsCAUCo+tWxIzyVUv2/8FJ\ntKnlulkKmuKnpjIrlpDoag2eB40726wyqCr4LbuzPpiDLmRdY/TTtIa375vz\n83I3aftXwkTaYoXHYXnA+cDtWyOJSGwESgiXoK3dR9TDv26SrT+c5ndkM4nz\naC42\r\n=3FKA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.1_1533845263568_0.36993196809559814", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.2", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "887e16d19dab3ab579bf63464dc2ef5ffb65b37c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-d4IDGO5D6vcVHU7BRoezHCXpXc+8mmNV9AN5iCBTNjyNlNdozkcldRt512jwdS3fxig6dKIYZ1DeVpnjg15mPA==", "signatures": [{"sig": "MEUCIQD5My+Cf69AGww5Ze1ozkksyajDDYS/I0LtMbrwykGW/gIgYtklITI/LxbxZ2xJy3YP3jGI7Y9f1VdbPzp/o+i93p4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGa8CRA9TVsSAnZWagAAVEUP/28oW16c8L7FfgaGAeCB\nsK5Sa/AAHNimgwo6eAasqyo0QiR1wmUFmd2ca4YihJHUFIGxTVUwzY6jHLC5\nEnOVEnVGfPMlVxuHFR/a3rKIwPfO15gKyRFIxihvd3ERxNSZaw6Q97Z9hlS2\n1LG41PTeW142xtacYAPXKRWUk/WezjZhiOlVXPewSyhhGZ54IYt8en+lINnn\ni0BGxSx0D1fFcJC7MAWudRBLeJQzjhPxqla+FCnsr1iXe5KwhAzg52TXYzZd\ny49mzpOhBa8PO78Tn5foNXHwP04mby9LY7hj4Oi65bFb0hQthxNIje1SxxR3\nK733zN1KTB0tPKXqU2tb3h691nv5QpfElp6b05t8sjPybFESh2ZIQa2D0qZ8\nPD+w+RE50a6Xt1Z9LQettc05Yk8C4c+SURoJ+fc6ncTUqzcKSUEhc0KeJpbA\nYFvCLrsOWuIz6wRJJRGDM4v7f3jU7//UdU1CBaLgyVhJTHf88zKBS3r5Zr1N\nXLnY+QhZNGtvA+2zF2cTDL7OC2cLqXG8b0Z2u/H3owxKCgVty6OU+AdGK34u\nqa9wNXx7JV7ujLIFZ9XSFTvhd1NJoHpjrZv96M1Iwo14qKr+1gxUwd4PSB6P\nPZ+qSWBZxfvbt5zvQVRe07mMd87oQCKWhgkKSv3BLzX9x+TMz8oNvRpu6eQv\nwcKi\r\n=Em9t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.2_1534879419613_0.8086860742157076", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "71006af99e419768089243b205b6f8792cab37f3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-R8363Wwh+mtRrI7lgRY4N/RLYopyMFPJ+pbzsoAgBWFVxHB5g6WNQipFrqBBBVS/HpOB33hM/9LySFhLg96otA==", "signatures": [{"sig": "MEUCIELR4jVMRTqzwWTZ03KNNljMonpmvx471zYTvAK0sWivAiEAyw0UVZUmaXvXr8YOM8b1VxGua4cpy6v04Tb6JrgSwZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElqCRA9TVsSAnZWagAAiOIP/2vnXnaK5GUSq16sCuP8\nFkXR8+dXWyShVx9Yc7puBZEA2KwdQFiKZL0l1VXH1iWsTjySs/5oemYecsaf\n7qjmVYHLWrlv1w9BnnSxN5j+KsC7rudOpqJmLeMQGdjOTRAKJZEpxD+2EsZY\ndsvaEaW0in3c+B7oEmLIj4P6iO5V4tY6Wq0yXvmtqEWissd0XNY/4AOfCtBW\n+buS2ikCPS3+Q11QlGnAw3puWmHx5qa145R0TdAI9JZxvIQ8Ok9X2knKsW27\nSuIsSWvqjjsuhqZ+US4rn35fGqC3MdNCOhmOrIg+kg/oaH/okpi+8dk4rVmB\ns8qoJ8i6fRathyrEwbpeE0xVdX9Y+61V+dUtA6T2CA+UqK8Fp1n5xL+1aBRs\n0dZ6FqV+zEeE7/PSGvzaXsMmwYAk2bzrVd1Lv+3PN82cixUZmFYNvHBa4Qtk\nkqchZieGbTwHFxTEzWGPdguONmKdDwLFEf+JH2MTlbbgjy/Y7NZ+zsMu/4dz\n9uYjqKvIplsKZcSheaePpgEPfS32Zy5rF2ANrI0CnkuO1kUN2J81T1AoWFZZ\n4CKE6mqqjHtgmu2ppfwAuvOMZox6ca9JoSqTdTIUtMTvLg8HgqAWQ+6ISjht\nnIdL/VxbZhu0phOd+TJR9tQ4qztSnDt8tdG9eMPg2yM8nT1amemlwGSEAnGE\nD/hn\r\n=FDGB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.3_1535134058119_0.8146035889670094", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0-rc.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "763f0c3ba6d978f3437e6a7bd560671706cde3ac", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-6cUXVrpLUTIYsEmkW/pR55ISdH28VIEmojirMWlmL88lLkW3eVeVh7Z7gDgWCnCiWZBptidnqjv3jdsH5y6g8w==", "signatures": [{"sig": "MEYCIQDNAzfVQxjnDpM+v+v14H0hURR5fEj8lhf6FuJFEx7ZCAIhAIhXSsOchs2ufeAfNFwu6Hx8hWw8EqMc7pzbl93Iy9PV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpHCRA9TVsSAnZWagAAW7wP/1nlGHwQEbRzJUUxX3Od\nWLiIvnfxxqwFhMsuldLfXbmJ7UcUi3Vy0zo+Z2z1QjUoswqEghaRcNbp5vUe\nqcDHHM4hRRmD6wWF1T0eJ6OliGeEQxym4neAr0T7QRh6CjBSnHY53yDtvXWi\nGAEHfOMY5fGfGOsYaSepo2+09daNHUgF88gfm3JSuSAB8fa+hSb4JQMEePrM\nZE+2LpOyEzJDMpy4fISvi5tLUJGdU6wlWppv2qGouaTK70y8s06oSCdFSCyG\nj+1rHI0Yfj9FCTY4+/WJFI+5CTyR/f/RB97VVK9JIqqPgG4itBUknZ8oLUMt\n3akGhbAyaVEk4n4HrDChqppkFsTsVfbi+qACg33Dy1qnxxts30nzFQIhRZuQ\nytKrTDET53lLeh7I3J8zbKuT6jPXo7/0zwuokmY7G7Cs9/mxG9voRocvfguu\nSQd3N8aZOdnrUGylRL/jb9rO774LjSFxgmLiyzrSG06wUXK/6jk9Mmz3X0aA\ndl37dPEPdq7w9NF8WEv6IPvBDoFN6Ugpx1KZ93hckwdwrQEK0+G9C1PW/0zI\nU9YxxasFF4MVZVTTowK+HuJ0Nz+by0OMUXwLai58Q9aJaeK3NpsqW1Hxt9Lo\nxLssy+8VKTkQi6e2QpSvR5B708iuppo1YCKYfbP0HsT1hVDrjujPVLq0o8mK\nKn/C\r\n=X2yX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0-rc.4_1535388231117_0.5691733191636408", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.0.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "90f4fe0a741ae9c0dcdc3017717c05a0cbbd5158", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-5fxmdqiAQVQTIS+KSvYeZuTt91wKtBTYi6JlIkvbQ6hmO+9fZE81ezxmMiFMIsxE7CdRSgzn7nQ1BChcvK9OpA==", "signatures": [{"sig": "MEYCIQD2tGyqOZ5EDoJgLDbHe5AD73PaPuR57Woo8N/ncI190wIhAN/ydF76GFRZ/A/cLX/ulZSF8hDg1odErlsh+ZeVtJJp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBeCRA9TVsSAnZWagAARJwP/jabqxuRyh0HTmRqFF9i\nCjcQB/WB05QSU6WfIOSLJh0YoueqfINGb7xfTQmXJ3QlACIgkD2ENIdPLXYe\nvdjBXHqNnVVmGbRchHjwia5ghd49DYzr/jqhBimKvaOQ1ReplljeuUMefjpK\nftZpD2MrpTz3kXafYhIMixORgV28jeP/9PABqfQKZSJGsJmNVgFQKaFZfCpB\ndr81BcdwrRneIoNRDm5CbwB2+1sJzNzrdL4lvUpSrixA9gs7+W55trVSt+NK\nHqo9FaHud7Qxx4xMzulbnsrZFDCof8YYwiYKLI76zU/1trMoLmZ8N+GT5Qcc\nrsdD61krjDbrokm4FKm6eYbFCA9qD3++YhU9+kCT9cL1I0d5EcgLzCMQvkQ0\nVW4VYJjuYeoCTUYdZO/99hSKSDUb0ZDCePK+WogiHyHZZsankTh9N0pRZVzS\nstLbfsB7bEdfWXAuWK+Lpt9e3dwuR4Q44nnnv0mBMzssLuf1KILAzeKDrILj\n9ARfjVurd5LLoQy+XJqkaFaz1+IxM1p4NjFmYjsDLD3Sr+qaPjS3IWOBeQ4s\n9d0fpEnZy4TYjtjwiqOb/tHfuE/yhRd7sEOilHU7oFIv1aZkHSa9RP3JnxE1\nmDqh87KXnoqQoTw/wtWIZrPkd/JJWxcc0MwGrEhiWr7gz7KY1knRVfDKqYba\nelvO\r\n=Rpj8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.0.0_1535406173948_0.635398411458149", "host": "s3://npm-registry-packages"}}, "7.1.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.1.5", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.1.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "956a1f43dec8a9d6b36221f5c865335555fdcb98", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.1.5.tgz", "fileCount": 6, "integrity": "sha512-VqK5DFcS6/T8mT5CcJv1BwZLYFxkHiGZmP7Hs87F53lSToE/qfL7TpPrqFSaKyZi9w7Z/b/tmOGZZDupcJjFvw==", "signatures": [{"sig": "MEUCIQDEiRrcVEUkye8iFE5AKf5N122It2cs7VvqzEl3QOWOhwIgWUJPEl1q6Q73A9v1lYh902iYCNtUxadtGPPHQKrFwp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4hPlCRA9TVsSAnZWagAAkZEP/RbSKB5oIBhEyR3BFdVM\n2Ubk6ESXW594ADkDEmcjpPSNvOhxjD2tCZ4EeWLxu2q8ZKNtdf41zEzx+yP0\nlyyxVuqUF9x438/3thNW8kbpi2IR6INXV/70gs0wXCduzqGLcpTdbMFjk3KG\nMbnabTPvdwBZAjQin/ZZzc9PMkm4Qm6iIWhnyoLeD7ABovA52doI+V+AWE/B\n8HgyK3xYMNrd1NLewEDX/K1G4i8s8JPCuVJK1uOl+/hZj8B8UA+tXpg0ir5Q\nw915QRImaSaRtIfiy/4w1s8ftzr4krbuB6fEcJkqFOOn2pQAwSoF1bk11dtH\nFIx+DNPmAVEAVUtpHhVWm/OdWmZlx/0VTJ6dW93sQ/VUzFWh+nY5lyCI9Bs+\n39WyjgZI45q/P4gCGYwf4cfVtlkCRn35yab726QKqM5GECY6/NZIelJS5Hl8\nfKJ3qzLMP6cmK7elrKFB8IQi5fLm8MBanYjJMnCdGNUy6UUqPAdjPzaW7Pi7\nta6RFpZW3w1OWyHu+VKcnruG8opYxZzVGFnr/qZZzYWIqDf789gBubocB+qC\nAsuztZuyu1RpfacvMMbfhKRT1AxgLSDZjpQvQR1qrdMHVSUzok+gfRM3FieM\nDRsrlgA0quLqxdkY7cxm0zgzSf6ZpQQTEijk9J4iwyH8UxQ6mMx8bN3wZuT3\ndbs5\r\n=QRHU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.1.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.1.5_1541542884646_0.7033024187772186", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.2.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "55d240536bd314dcbbec70fd949c5cabaed1de29", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-WhKr6yu6yGpGcNMVgIBuI9MkredpVc7Y3YR4UzEZmDztHoL6wV56YBHLhWnjO1EvId1B32HrD3DRFc+zSoKI1g==", "signatures": [{"sig": "MEYCIQDk5DOZlkCp1fF/OWvQAvKr5Y9BSgegJlLyzaOzeNV3ngIhAPHFB0N2BgR0X83jOGUSLOgsC8pvk85M3MoDzEMXH6ST", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1zCRA9TVsSAnZWagAAyjAP/0FTeJVUz9kKqJSH3sMr\n7+JsT/32LvhSQKYZ7Rmmhcp0x/r3KLXoA5GXkxRflcGL9oiR7hZpVW1XhCp3\nDIqQ/2kQNHrZTRjeZ+x5oVzyAu3jca3vNXETS1yoiuuuI+DaswLrSjdrR0gE\nibTxs9wlSxh++rYE9U/ZWuTzZOYRI+kimI6WwD4E3lR1fKbNLXdPNnPLlbnJ\n7KyHSxgM7o+uoc2IWtzRr2ePS3Lg++goVxObANVXgnJL6rq+V0VH+losTNIc\niPzyRWpcr2PU3Aoqe6GYY0eVynGZWiNHcce31vrncC+dvVsySnnbh6ePGmY5\nUuUcbKgMZM+Fv/r58y/0P1EKYmiFwXfHZxIszPj868Kolf8oZxXC9bIKnrqf\nxM12P4mA60cpEIPrqLwALljbTCxIVqDD2B/B5wY9ZK7zECmLMPI1RutgdgqV\nVoCot1zTNxgkzC7RcoDQPHR8HwOyz53mfj5WmVG0VrtpwnTfkirwlq6zxxH3\n3kwU5uUfdjFSBaaQNtkr2WA2AGpxf9n2Sav3KIB5eRVcQgRBqflZnzGgoKZv\n4+40oVYPo4Q9fUFDv73lRIJaInqtq3MKUuu64B8Bpopf2qweOtJSnUmxrZKS\nCVX43eKtQMsfA1c86k+l5TTSHjBqLGOeD1HEMBVKhWOQY+TxcZegjUKmO4eg\nYvqw\r\n=qAMf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.2.0_1543863667327_0.17199273166895135", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.3.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.3.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a7cc3f66119a9f7ebe2de5383cce193473d65991", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.3.3.tgz", "fileCount": 4, "integrity": "sha512-dGwbSMA1YhVS8+31CnPR7LB4pcbrzcV99wQzby4uAfrkZPYZlQ7ImwdpzLqi6Z6IL02b8IAL379CaMwo0x5Lag==", "signatures": [{"sig": "MEUCIQDYOtyyVjSscQhlAbhpz3PO94MkV2+Lq7AvG/a/qAwGdgIgMatIxe5op5FeTG3zpFV8EqX+iv7Hw1HuDqIfITNynMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyuzCRA9TVsSAnZWagAAKDkQAJaaaXXvRJkXHTZHJGe5\nxu/GiiOvqtCMbwF8Is/OSUYdI1wNV6nWGH2FA3+PY98HQiIgB3ZBwbVh/N3S\n9hYv3Z6/3RF6e5fa0GCF6oa0Faq7uvyqW46Qv6wuOiDTO66otVrVIRbsy6TK\n2SbgFXq/qj6xQg8MKOCYQgYYLF48xCr/ysdQYYH8lOguaB5MEU5nGOt7qVPO\nk1FdHYxPBndHydRiq0WZ0E1Pf+JM7CthKit4dEh0Wg9gJlajTcQ1j9wyx/D6\nxYzfpYuKNDCWBdBySIKvFSSRzMaU91IY6TqO5v3xpyDGrVUAXqnoknaQ080W\nfQbCSobz3a41zgvEF/NcubEPSZqKS9Yv02vfPNY+hBoDt/2MprSsDZrrnCKB\nKUtwCHAL5oEd6RrxLb9vmbx+XRizc9L974IzcKUeIU318pf4MBaNRgd2M2E+\nljlwDLDjg300G5uyt7lZxEuAroBwI9ORLetrBnXiIrfNLhF+GxsT9dhCP5hq\nEn9RaJAIVwO6eLa3SIl5ZAoFrRxw/ewiAaL35V8y4JTJ7brRt4wMF46clZ+f\nJjWELFmLO0hM8htINKNMit3TZ3Nrea2oMBcGlJOLV5u6k5w8aS7RtyjQvrPR\nrtFWLPBNC28ONsaNrTPledenfD4VipYeJpesPV7F+nWAjRMAZbgrRZS7apyJ\nTGUz\r\n=WbO0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.3.3_1550265266928_0.8381433755770451", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.7.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5d037ffa10f3b25a16f32570ebbe7a8c2efa304b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-77blgY18Hud4NM1ggTA8xVT/dBENQf17OpiToSa2jSmEY3fWXD2jwrdVlO4kq5yzUTeF15WSQ6b4fByNvJcjpQ==", "signatures": [{"sig": "MEYCIQCdxbXgBwVf0ljUBs7V+jt22kvK0P2DoKLzFNHyQMo5TgIhALJoktB7Bn9XhI3kd0mIoD+dXEC7fzYaCO5jIzBtmBvV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/1CRA9TVsSAnZWagAAXE4P/1529HX5pMJX90WdkPnj\nbla+AtCXf/9g0y4Dw7Cozx9XokO1nwEKFbqSEU9EfLdx0XL0QbRT8RGINsng\nb6qlsKcbSjo4p9GyJCr1QBbmvXKrv9eynRzUtcVa9R88UvD+UWXEeYGs/MFD\nYNGnzDftVXJ2OyK6OAux7osOvFJ511sVUb/jp70pMYuk3fwconboqo9KXoux\nrWB+w2FRmgYXLZNTLJLWxHqDMPGm17unZCcIPxaQAOGRa4T2A5lAMb4Rk8Ge\nBf3u+6rwQWbpjPbZtEmUNugqUuBriyWKDsMlkDlQWG5/aPiO6Fc7S4C74l1E\n/D6ceRDUhUxklLbmtzEWtTNMu2xkbXgHSTM7a3MnEEJo6WbQBd5Y8Mm9qJvL\nCu9wILmoC2l7C7FOVmukETHUI2HoEKcFikGDbSyQAUKQpDoAVrKDZkxEXr8Z\n/LT1w4PleSj62YXnk01oOqaIWRpfrKMRjIGk+lz3nHh04G5RbWfFq84z1r2H\nUIJ4+lZkviCHNECok0Yoep00TuRzI6gEO3bjXSk+qYucRdCHGBtTWCCAiTdX\nmjkE/fvNCe+mVLbRxqF39e8hEfB+FUGD96CJKxMv/4Uwt7f0Cgh3h6VC3Q/f\na5c4jyAs3TBds8JApLAilwcFLA9RLKbjnvlUKXLs4i1KFYbNnLhNm7zMWZIQ\nuLgN\r\n=jE1+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.7.4_1574465524677_0.9778952332036182", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.8.0", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "8bdf202f573cd0e1231caea970f41fdf104f69d7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-LrvVrabb993Ve5fzXsyEkfYCuhpXBwsUFjlvgD8UmXXg3r/8/ceooSdRvjdmtPXXz+lHaqZHZooV1jMWer2qkA==", "signatures": [{"sig": "MEUCIQC46C8Uzwv47hgQCNyGY54crriJ1vQhA+TbOUcSIHJFDAIga1dZhcIdgwayf0ah5XCTX60w7tJMGrxTo9vbe8EoT6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVbCRA9TVsSAnZWagAAX/YP/jsb4B3tnojsc/8iCD0J\n9eJDVA+fKUWLezcxXCmxlS+dCmTo7GAMfBj6iCoeVcxnAoogxy34IiyQHqzd\n6IBUOfKP7JhLhQgtwfhUmMct0y+FfuebJbhvuXOXIhLA+NeN3kABcSXHAFv+\nQlPTs4RuRCsXhuq4vNYU09WtYat2Du3pNMepvnsUQdU+HRNpwYGNyzrk5HR6\nZ2t9ITVDpfiCysPB55HJWZXsrxj9lHBn5IqNHRQe9HgXpnfyKaPlJczbqwRo\nHMJBopGokllHjVi8lERbnoVowBz7c9IKLIz5Otm8Vw1qW81k85GLWeCAAdxp\nb0RCDJIZ26l1uA8Pmxyf4EP3HRFonlo47TsaAb7AkoeedVNp2kDs3Fs+ngdp\nhosuhBC32OaIZG0UrWBE9sQ8O7IFxYpLrZIMWAsNRTgDPDVX2z8OguGvDdus\nO4yKmcDTMYUcUgCrCCYSXG795y92X22XWCdOZWxcP66x03F2erdKsfuf7u4m\n2If5qSkOvCM42XVm6Qlc9ZIHJ63cU2PJC7imBOk+05DithjVnm6dsrnbSZn7\n0UhP8QiSwJAIRB3Ljac0UeUBSPYRuYUSpn+Vez6BNzGp7DC82NbHXH/TGA/P\nRrU1UTRA92AP1pmsAJ4LZYexfsQnWx6NGI73RfuVkflsZMd994eclJbGt4EJ\n6sIK\r\n=Kg3V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.8.0_1578788187401_0.6107093638120846", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.8.3", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c1f659dda97711a569cef75275f7e15dcaa6cabc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-GO1MQ/SGGGoiEXY0e0bSpHimJvxqB7lktLLIq2pv8xG7WZ8IMEle74jIe1FhprHBWjwjZtXHkycDLZXIWM5Wfg==", "signatures": [{"sig": "MEUCIBvRGI93A266kIOlgltIzAP81HptqIemEMQbMnI6GbfhAiEA1Uq5lEJeMd+uQVMjJbRfnYBrxEYL48zTon7UcJoBsP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQHCRA9TVsSAnZWagAAxNwQAI0qZjvA3hzSPm4EwGEC\nM6nvqccxxaSvdNIdHODjy2CXa/9GBOhLkxkZvyDQMIngQodv7GuhlY7ux6ai\nzldTlJkXYO+VjLDmxh489F7P+HrnBsg496OVxAmkjjsaz1wbXfF86h56Y5Oo\nqFyzpO1U8UofR1i2ChrcvSUrNSa1r4XWjwrZ7gwaO6AVYs31ANySfjNzYYS1\nL1q2mt0I5R0v8x/KghxEWW0IZY2+MLx6/ILDwY2MKQUBcwy0karQu/QB79sC\ns8l74ktha/uGuJYYcJQtT7KeYX4E/FurRdfLLU2OzWqBK/YWxOByZswIXcDq\nl+fQeB0SLNN8AWhJHRPF+LmXyhgqv0GZMe4AbkCqqIg3iy5M/7puaizET+Wg\n+Agc8cc0Zpy1gnENGxIns4g13ZxPEZLnHrWk+Oed6zecVIYHiL97SSYMCxSV\nFTWJGY/eQ0rvLC9ONu8Q72mbj0qkecsqOYijedK66zDRPI5FFkFbCssqEz/W\np5loqeVmgynG1u+aU8QiD0PzOg0Ay00mD54rUfrnWoarVLn915/JviVwFcr5\nwuu1YvqCkbghLoclqkcY9JHRQC+vS7iFC0EuZPwlvw4vIYMtb5+w1slnp2eR\nVe7n4CKSx8Qk6s6W5BwOJlMUz2bMD8Ci7ZkhTe3oxdNE2bTHcnA2vZ+kn/0G\nsHNv\r\n=ffBF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-typescript", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.8.3_1578951686856_0.18047829097403745", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.10.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5e82bc27bb4202b93b949b029e699db536733810", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-X/d8glkrAtra7CaQGMiGs/OGa6XgUzqPcBXCIGFCpCqnfGlT0Wfbzo/B89xHhnInTaItPK8LALblVXcUOEh95Q==", "signatures": [{"sig": "MEUCIQDnXJ8yoCNjLgq8VpUhS6Gv7nvnSD8BD4z5YdlYAlBiYgIgfQ2jwAvX4SvpxpxKzjoVfmn1ZuKF6Pd+8y4+ZbMuS/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSTCRA9TVsSAnZWagAALYkP/23PsTCWSKhJ5xOWZGpY\nc2I8xTpPhlOBeL0vVeZPaUb6ME5GIq3PaBxDsuwF8OxY16OHsX4CzhmS8w1g\nVV33g8zaRM+YwVpSImq26MDmukPwhLfPN48Y0Kb+1EhY4CCtmUU+0J4jdENT\nl0IaqxihkeZ+982jg5pz9O/jjYG0gtTOuQ1KbwckfL92uBWohAIK8X4u5eU8\nbEVspijRGZvt3A4b7d6p/dIV8/Jr34KgexVhvDXRV9iWWuBDMleXNUK0fiqN\nNjdTCCOMLbXVnQ9ycYcyVU985z/wgTHHFRDZoLn2yvo0M2FAUoNLViWVfU3s\nRC4wai98csSl8UciMVz2vrbaC2iZ7lG3OYJ66b9nHyH2Bq+XEPOJSBzZ//P2\nKvq9VZoKFyRWkh0CfRQh9EJY/qPjCV0lUSpC48jd/cs6sP+C8trPUT9mku1d\nCxkqNor/eArOlVUd62tEV675LvI2diQa+qt1PhbjSNJaYnkAiDkOAl0Fc6hQ\np9UdnU0IVpxh0jcW9GU/VikfJcjsUUYzeQFxNsmQHaPdyQ2By9lSlWsz63ZY\nLFXp6wWF1ZIrXljJozPw8Kf0iN+wKmsiXSk8eruAlFIBUrxaNP9O7NOUspaP\nK+7yVXbSSp1o5NnBE+3VuYu/D3OI5nF3+HmTOtX38150MwFVaYKyvGzA/g1T\nXGhK\r\n=ZqwL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.10.1_1590617235415_0.6185947997171151", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.10.4", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2f55e770d3501e83af217d782cb7517d7bb34d25", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-oSAEz1YkBCAKr5Yiq8/BNtvSAPwkp/IyUnwZogd8p+F0RuYQQrLeRUzIQhueQTTBy/F+a40uS7OFKxnkRvmvFQ==", "signatures": [{"sig": "MEUCIEvN/gOBg76X0h+5TbJ4xkTA8GhmRUfbKZ1GpON7w9ARAiEA5sJzVDp9HK7Mi0hGvRE7FBEoXGsgDDKJLlmPujlhNq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoaCRA9TVsSAnZWagAA17EQAINQTb0WuYpH2opbMyXQ\njwre2ZoQlXQtrkr7b+xxcBsIcrNfmq86M8msqrRh/SasfBTI5IyUhLjLhhqN\n5qyTqGsbquzHAE21Mc0G1W8py+7pHGzH/kB/t7Eip4mw/Bzd5wLpOxGHTvA7\n+7UiVb8GeE2J/8QzpDdCvYCP9Ba3oYYbor9KwYp0LhfyJkRv4S95rcbOu2QE\nA03aWOD9DP/OJS4zdLDlpNQHRTYpv/J85n19Uj3Go9mwNqR3n7cvWjCkJ1Vh\nq95zV5OJjO2V47nTB4ztqZ9kkeRmxJst2qH8dVWXM6g3tWI84hmTkO6ScNbC\nG/QsRilLHmO4WC/UBw6z2HdZO0I8CQtN10idKqOybyf/0KqPIijBbRdFe9qD\nrR4/g0Tlsbf7KtRLRPpeiMVPaXeF/QyNKcGDnmwGvSJqz3nf3Ha4pJB8MBcI\nWz+gh/6IkZ56KMOwHTSJP2CD56wsaJYHgG9/AyWe+uKMCwC2yGh7f7q1YNXY\nxb8H2oQnUBbklhio/3uDoc/2uzluypQF0mA49ZVsyG7AYneHQt7Rim2dET9u\n2A3MDegQP+ZX3CTLS78LqMtFFaJG4lrMkBBFcH49rYThY9tmVSFZrHdv6Hfz\njNbl5pEZXwMHrQ3R5TV6KtP1EjnX5IFswUH4EhPeKdGvDook1fDk3JNUfgHl\nv8z1\r\n=Nh4s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Allow parsing of TypeScript syntax", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.10.4_1593522713722_0.7295866277888756", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.12.1", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "460ba9d77077653803c3dd2e673f76d66b4029e5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-UZNEcCY+4Dp9yYRCAHrHDU+9ZXLYaY9MgBXSRLkB9WjYFRR6quJBumfVrEkUxrePPBwFcpWfNKXqVRQQtm7mMA==", "signatures": [{"sig": "MEUCIQDtzjPdxArOWPyzWlZUalhhLBsqP9bi5EutdFUTruoY0AIgKknschaEjbJISMaGbS4/RuKSEZ1saRv/Zpe2EfT95bk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+6CRA9TVsSAnZWagAAHBkP/1Z91UiKZF7BEJ/ch4vM\nQWhi19T8517jMLMAQC6FCbg+nllE7F4+j0rNVi9b26WUYxq7wfI7X9Xl+Pbo\nK6wY2jXHg+tGv5je7WZjpwNbrbjUVJLIcmUaDcSFHKrhp8vw5waGl8g7U7Oh\nWdOpmKQDp67PLbDOVnycbceKh78asEyb70hVpXjSX7fOG8BM0wd+cu/oKZW2\nkCnJO9YLh1s0Y6xf+keFKwmRAzzOnZpvadZ1s0U0+yqwkCiEKrJixGLlKNyw\nxUtIByiki2Wgb8luvobl3uDu4ZqbxJ9dS+PdP7jZhOwW7UXsdtnCZGFo1FQc\nphVlCISPBkXuE/DExABuz9hW79dflGjTQqwrxy/F1X8XtZtkClU3vxpQ72JE\nuGMXydSCAfJKEnRh725WHZQrK5puKua62D73EZ4/8nolFe/h/mhft+HHMqDx\nuCzKfL5hEW6WeG03G5EWbYXIf8gsi1WxHClqAF5kEXIb0es+FkX5KOfqP/D4\ntVvW8t3BOim5lzMEhuVmUcBoWdzCwa7zVcL0xobDw+Q8y/1i4fj62pguuzso\nxGY+zgqeX6QFg6h4RRFx2FD7GGOhguJvskxwwpnK7Chg2v9+T1geOkAaojnP\n55j11x/jkFCLibY4vJarSl8nEQXD7tFKJyPzq+yD0CgUojaIEJ15ZciO8auH\n1Hdv\r\n=2Ec0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.12.1_1602801594013_0.4010102423089741", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-syntax-typescript", "version": "7.12.13", "keywords": ["babel-plugin", "typescript"], "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "9dff111ca64154cef0f4dc52cf843d9f12ce4474", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-cHP3u1JiUiG2LFDKbXnwVad81GvfyIOmCD6HIEId6ojrY0Drfy2q1jw7BwN7dE84+kTnBjLkXoL3IEy/3JPu2w==", "signatures": [{"sig": "MEYCIQC6tJ1nCaDy8KHAmiWUdhQ5LRjXdveyw3RAWpY3Ww2vUwIhAN0/GopmmAhW+TVb76F6dlO8P/fkrDtyrbsdY6Gunbzv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgUCRA9TVsSAnZWagAAMP4QAJsSR98g8P6/3O3T6Amv\nGVdjFvRnqa4G94p7PTCU9vY6lTRZse9Qcv1vGYQxl2LFpudN6QyANmulHbE4\nb+h7v3wwvRibxT3xu9BmN+pvXVEFJcuS87AGxsBcCsWx2Rp9aEp4CshaPraz\nzoKF2UV0ADuq+wWS/aNa0qSNOUttfrenfmvmBpvW2+mM9SYarXFCmHo0IUb+\nX1/iEZlQ327S5yYwe6EHrX3ScJQaLYLEgcHvOQsdJ6A7KrO+Jk8RxO6LN82N\nvSHIiP/CMDhsVkeipHlV5pukedOVSc7B3+g1g3jAG70JIMMIkXHCUGtYyN0b\nC079bgaboUGytdptgBPThxh4uOib8tKMM0Xc+XSEIYbdD3EbJH6Oh2YcE0Sk\nNKZ6Heqb3N4kpxX4TL5mDchQQXrSYs8+lN0JXc8yP0rord7lsr6Bx1mRlwxa\nUqPQZQ4W+mCJeLUvIybqmvk4/zRkQmjANOrFJWEoSD0zvDltZgPmE/5F94KL\nGXv40TsMaD4SuLGhL2tBVKTpBV7hjqJPGq/oacLtqhGmeMse/Nmr9Xl1DHKn\nrMFDmWpuS91kBbabkdhWbyyd6w+rDGlX1L35Q++yQkqqGXF5lpJwNPxpA4Uj\nUNOW6Q13fjtkJjvOeUC2eu5oaGI5AdQka7bEYL0xAdzePEIKp8w/mYNsqcTN\n4sU+\r\n=7QoT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.12.13_1612314644134_0.1504054991481807", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.14.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "b82c6ce471b165b5ce420cf92914d6fb46225716", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-u6OXzDaIXjEstBRRoBCQ/uKQKlbuaeE5in0RvWdA4pN6AhqxTIwUsnHPU1CFZA/amYObMsuWhYfRl3Ch90HD0Q==", "signatures": [{"sig": "MEUCIQDBblrVza0WRMEmP+C+mvIPJOeXIym+RpY6Yef5eKmmfAIgUfDFciLnTEPZtfGWCF7kTVI5/0K+tBnc4ThxWfCMjWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq7CRA9TVsSAnZWagAASdsP/0ABjbYMw1OEXrKK+d4m\nPBHE6/KBQpmkcaMR8x347HtLGL6xSymkD8TDxVbq8xEBa4LkdmLYztER0YpK\n/AYqsBi3AdEpyBHZlHNQNFha8UcxzAHn2v9eSv8NrIqiXcauUF8cQyeVpnWE\nMIHh1ILEQVqtNWu3Hx1tZvqYE7uCASHAjmOXi/457v2MLs9rZftTEqfVWSww\nYRjfnFVAJuVaNbzKHXdGXGsPNCqMdrPiY2FnIzB17gBWhW0rqYoA5o2VhpXN\nYnvbm3uKwb0oMmW5LusYwHU9RDyr5uA2dm7o4NEVIrUWlRYw4kZ4NDxyTWJj\naq2qmstP9VuyiE2TEyCXs2iLGL57zm0FTLSkPpQzkwdQ8NNwQ7oSl63SFSpy\niILCbzZhZBKBNaYnJ9ifEfqtlMIKP5dJn6iMjji0bmbQsix8xnEz/+y+6ykT\nLYFvSFWxDJc6j/eBzo2b8OZy9fu6CZ14uaL/tzWGLmGEZHq8mFVGoEGLNZ7A\necjR3AyoBcq8ecsGuPrp6fKejaa02DpwFzKm6DW0hvmpji43QOMYYJe0TS2C\n861vmLF6tWzy6+K5nF/uV5MoMt+wXmTSu3BNBHzgtB7o0Z3bms+hiHtSfh6b\nNaHOXuy4+qz8Y3CopUv4BmY17MKa+yrEPyqbY45v8QHZsdWuM36RMf7ze68y\nOiJ+\r\n=EcC6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.14.5_1623280315659_0.5891920793000249", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.16.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "2feeb13d9334cc582ea9111d3506f773174179bb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.0.tgz", "fileCount": 12, "integrity": "sha512-Xv6mEXqVdaqCBfJFyeab0fH2DnUoMsDmhamxsSi4j8nLd4Vtw213WMJr55xxqipC/YVWyPY3K0blJncPYji+dQ==", "signatures": [{"sig": "MEUCIEaainEfhnH1RZUiMZfPrOOOtG+OuRNHdhflHmHlmkQ1AiEAwIcIPC0wkTHvy82A4j7PGY4kKEwG8SgtaOaLDOHbhxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3891}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.16.0_1635551247845_0.1252926664445393", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.16.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "f47a33e4eee38554f00fb6b2f894fa1f5649b0b3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.5.tgz", "fileCount": 13, "integrity": "sha512-/d4//lZ1Vqb4mZ5xTep3dDK888j7BGM/iKqBmndBaoYAFPlPKrGU608VVBz5JeyAb6YQDjRu1UKqj86UhwWVgw==", "signatures": [{"sig": "MEUCID7L03GXKkhxUXEZpA93duKOKcX9kKnMhYgoPAQo4Y5QAiEArNZsMOs7iVc8XBdP2UjSLPuymnnLJR3vaUHZvV1ZRuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j2CRA9TVsSAnZWagAA370P/0XQo0tq++5cJ7OCVXGx\n/YmikEuGDh5PXMbQCb9ejbQb1AIpyXiYCnLxWnINZkzFQwKmnZB1Y0EVoY4V\nodi1uc7pwrAiJdYkFXqlIrmtP50zG02Ce0jbR14tC6xyqNov6SrRfJtQjOX2\nmFzomVsmj1+5VG9ilE4Pg1Pi1xhazUKQUYp14Y8knb/58L+YpyV6i+nMixJ4\nW/hFkmnNVs2knsGguOJX/vdLvYTvpJeI7HwubQSYW8YDySnSCe7AsPg71TMq\n/0ULK9G4Om1DcWbm7ACbRxrlQ0B2OfMmAa8QPEKC/6egSFd/vBjH/u95WTtj\n+FXMn+uCe2hIGbhGddsmzln++/ugASnvl7AoLKGGVpnu9GTlXbauQUA8kDms\nacHP07a+suHEuFJb0MLBEY7MAcpSMFTWXpJgXuZDid4dXJeSs8t/GrsHTLwp\n4jI+5v8tWkOeMCQa8Xbi+w3iCAvpcOlhUuXXVS3cAy2a1bOSIvegFmJnl+SB\n1bG14t4LmOfI7RU7ynwb7MDAypF7kYCXnwq9oxqaP5Un2PXy+NLQ9i0UuDOv\nklAASLgV7cViOm+M1cP/ppRud+D2jv9esxlexG7WcnCmgEn0hjJWeRV364+s\n8hherRVSgzvvXJCApfotX/y0V+MAvrh08zFKj2062SubhuxnXkIgLSxt+x6A\n5RTu\r\n=KpoM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.16.5_1639434486472_0.15860421647114586", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-syntax-typescript", "version": "7.16.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "39c9b55ee153151990fb038651d58d3fd03f98f8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.7.tgz", "fileCount": 13, "integrity": "sha512-YhUIJHHGkqPgEcMYkPCKTyGUdoGKWtopIycQyjJH8OjvRgOYsXsaKehLVPScKJWAULPxMa4N1vCe6szREFlZ7A==", "signatures": [{"sig": "MEYCIQDegsIu/2F6bsezd6JUaM1sH4i47hGUjfLyCNZppUqkWAIhAOR/YSy6vJsRhCEzlfXQhKs/L5Wq4F7m4aLytivCTxZo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0fCRA9TVsSAnZWagAAk2UP/R1fHFGnxWjpjH63v/vi\nAQRZAFamcfBeEjZ/C8z90eDWc5/rDVpeoddoQT15Odu9ahcF3VxmgMRZaqqc\n9WRJSIg74FgiP1e2k4jk3Fb4bQMGN8rMTfyJZCH2GnzVUEYGxAR0ys6uX+9q\nHJjYNxPh1b/fdH7cYnLTQNVj3OhijjVP8U3hPXRdudVHe3KxJCMiBDmt8SO7\ngUTjR5AKknQzY+edqsi+1IIK6pOcOI2ZhqQa3Vm6kCoYCn9uaVKnRiDeWpO2\nGwUCRn2pWzwG59QVNCn0opvi21MzLPK+Tq9RxWHnbA9r2GJft3m6Xl5Dl5nH\nSW0Vqj9sS19iRhg0dHHQR9x+iTciWh9Hy88aKAshc3V/wVUP3sm4aYJyx/IQ\nKAlrj7CQE4RzS+wCTasToNIj67sMHUBIBb7cErvh2H+TW+RoCJ5B9Xd1/POY\nO0/Wx/RLnAA3EA4O9KoXj7wjHJEGwYUgoKxI40axmZ6Rjzo2Ix7WL8NoxEGy\ngrDJgKPTJ+CuVps5JAklZ5nOtqLdLVk0OdVT4lqmE22ohsM0eheCFaM2/7v4\nAlcD6mX84ShPyL3p1ez2STku/KQy7KG9LNdrEjBCfmzilOXxD7439w2DXG4J\nGOFOALZIMASj5Y9lCMpToPFW55TLBikLU52GArTOtdiL3g24MLLgZYyj6t8X\n5QDg\r\n=AUTA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.16.7_1640910110923_0.8847211484265585", "host": "s3://npm-registry-packages"}}, "7.17.10": {"name": "@babel/plugin-syntax-typescript", "version": "7.17.10", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.17.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "80031e6042cad6a95ed753f672ebd23c30933195", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.17.10.tgz", "fileCount": 13, "integrity": "sha512-xJefea1DWXW09pW4Tm9bjwVlPDyYA2it3fWlmEjpYz6alPvTUjL0EOzNzI/FEOyI3r4/J7uVH5UqKgl1TQ5hqQ==", "signatures": [{"sig": "MEUCIQDm1J0pg6IiWKkPbNjAFN/+acCMZnMbBxJJZgBoTC0YkQIgB439M+bao52VUoAEZJ83A2gxHljwGZv2p4qf4s52omM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDQ/9GNDMmdssk8et7OkA1fteAmMUH4P465NfvUWdG25MJUItdwKZ\r\nNF72HUMMCHBoIDYYW5O8KUES0RmLpKrDSnoFwVLUkvQLgEGk5+5D9C9nuI/A\r\nvYhLouKJqGX2Dka6D+Wc4CvcPTnBur+WnGIE004mCxeCch5b0TI4XY001I6t\r\nDGOEi98f21u4C9PZZxVS1aYbTFwPjnSPGd6e/yYjBzSD8jAhBPkDZLFC1s03\r\nrxgff8wbRQycZOCd1gT2ad4sfsBvuafG2qMITa8/HoPxXTK3FvSf36LWW0e/\r\nhdTouACkYAe3Q9Uwnb4ciMEq7JdXFI+zdH+3XMCptF4c2UnQ+teIh3gikeJY\r\njEH8K9vlOCDevJaw29YEL3/tyg6Gwk9i2nBUagTkZI36kthzEQfZmxHy13s9\r\nBqQtuSV6Utpi2tmXILFhtcFiNtqkw5XjC0/Yl+Bpk8/AmjZIIhSRA2zYRjjb\r\n39feVt7Usc1qJlONU17QT67BNjIE+1djgfXhpv1geCczOzJLWVll+yeyMu23\r\nVtPoeFIXOwaH4k0rAHxiHSe1BNwQRRX8Y3cRlvxk9YBL9B3hA1bnY8ILGNpB\r\n2r9W0OuIR1IWeJ8rGWcaayxOXVaDXHGkcrk+FtmpG9NINjPk0g3bvhrYJ3Z8\r\ncmUb/Ajie0X48CuGAtF1LleRg1e2QWTmsvg=\r\n=kbE2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.10", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.17.10_1651250257909_0.8376504186882019", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-syntax-typescript", "version": "7.17.12", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "b54fc3be6de734a56b87508f99d6428b5b605a7b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.17.12.tgz", "fileCount": 13, "integrity": "sha512-TYY0SXFiO31YXtNg3HtFwNJHjLsAyIIhAhNWkQ5whPPS7HWUFlg9z0Ta4qAQNjQbP1wsSt/oKkmZ/4/WWdMUpw==", "signatures": [{"sig": "MEQCIGFyUrpVBjOVlff+9kIqCSg38eEkXXGpQz5r6ILJ4PYyAiAP6kmlO+TbYmb6sBQAQZFRH6FV55VFI9d28riLpJ+GVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb+A//WahPkUFnSoBXWmC7dGt/j+rVV4ly9n3yWfPypLKJM6R9VKG8\r\nGVHgVMLOspYjjFrfWlzfXAQmOSA3mhzDksk5e6somm/T9Jyb34n02xasgBK+\r\neBdv4BlgBWhywMr/fIScxkZ/Xxlo2DWMNB+MhQ/HlJCqU9YoC8i/7ljFxMIm\r\nEe8jGfifVS/o7FhfniN8ntXMQRlPfZu9J6xA4hSIO3bZmIWNX85hUvyKMjWF\r\nWSm5ZfKvwViNj+Vl/CP5ZXlIe/f2N6i3lOfpeqxn+ROx2kyWlLnQiFWo2P3v\r\nARZ632HZIcQfqNKRmbAwfOfGsezNoBkkx7nVWRgwQOXVB3fArzX0Rj0wOWrA\r\nKXkc3yi2jFIQO0c5Hfa3urSnW1wkOfqwBb6SmSW+CWxGU08wdSSCAJAFalc/\r\n10iOS5Wu1TNioe5E1vrWlvOQstTIbcKtH2EmIcElRwTigj/VF6YO5/BYBEaz\r\nqB5oBRZwkre4FZB59JwxX8M33TuXlENifd9wpl46LMbgF7VbWBAtDBxG9u8P\r\nisegjV24bVFUWKUmFO0MspUPE4UPLs+J+Yurm22vZKDSYN6hatcmlvwUWCn5\r\nftFW0CRHMwXzouu4LdHB3mHKO4BKsU3H1KVL3lVJR0g2rRFr3KlYSQqMZQmm\r\nHeZfyzTaFmjskSCacpJtl2k81gLYPPELtPQ=\r\n=+RmD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.17.12_1652729564016_0.8555952813990113", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-syntax-typescript", "version": "7.18.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "1c09cd25795c7c2b8a4ba9ae49394576d4133285", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.18.6.tgz", "fileCount": 13, "integrity": "sha512-mAWAuq4rvOepWCBid55JuRNvpTNf2UGVgoz4JV0fXEKolsVZDzsa4NqCef758WZJj/GDu0gVGItjKFiClTAmZA==", "signatures": [{"sig": "MEUCIHZCK5sDuyi/KhXnx42LiM0xA1X9NH8QjPYd5aorEj/XAiEAu0AtCEukTrNEJ7HbwbxoK/FqpVwZbEj1yZbJglPMTCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS3g/9G33CKiq7eQsoG68+/9DbGJgtbLPGyWyNKNyLmGHgDEj/kWhx\r\nyGLzvIQNgHg6+B6W/Er2FGxg7H/voNADnxbdWs3mcNiZm52Af72tOLiqQpjA\r\noLdcN/BxhINZRD9vZr+ZvarJWlZHq3M1SsQVxknYs+Z5ZRYwa5uX1kVZZgT2\r\niuYh5Da5lETeTgEmoMzi/67UbxlpVGV7rYDsvY+NQrgEQ/ZcIL6rFpXdEH8T\r\no88trUdvbuXvdZs1CaArqI82E3l7KaN8EYOOR7lduJsqGPn2IUpPcqi57mFA\r\nVSwVUcmstp2eNXCcq92NU96o1sq3ZAyClKxkvltu+tWYmCU3LisYtL0y4JVE\r\nT8UUxvyLAcfkRmGN9mkJX3EQv9cpVicV4zXfMz3FuRaBPdYsg6NGNkN95UpU\r\nB9HSDQ6TPdhHUd0iucht6QxRAs/YRdTmMVLXfbf21SFSjcIryKUdS3RoOq1S\r\nQfdOPdhqAFjlZE9iMmqBXtqkjS8aHYFTVImcS/WqqM92dA20HghmYPgkiuRk\r\nxwToIgKF5gaokKuJwSSQlVUauLUyWZ+Hu5CDhNaqUD4ab+MnSPNp3wtGSMAt\r\n1Hl7Y7aoNobYXL4NvKhi18tvtWxKdnSnplD2RrqlbnyLyOcgpIKZmSEmBQsY\r\nTDHuYpUlf1oRi0M1xsv5h2cbpcJ+YNXu5mo=\r\n=B9kc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.18.6_1656359397666_0.12786278988672328", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/plugin-syntax-typescript", "version": "7.20.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "4e9a0cfc769c85689b77a2e642d24e9f697fc8c7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.20.0.tgz", "fileCount": 5, "integrity": "sha512-rd9TkG+u1CExzS4SM1BlMEhMXwFLKVjOAFFCDx9PbX5ycJWDoWMcwdJH9RhkPu1dOgn5TrxLot/Gx6lWFuAUNQ==", "signatures": [{"sig": "MEUCIQDIgoaApWsrh/+N89xxL95Dou2T7hpZnohMtVrU5Oa4uAIgAITVLHY5RFhT83Bpa95epJ5rK5GC1dbnrMlIw1H5RkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqluQ//Sir4DFr5AzBbXr6VK6xZRU5IIJSMWrDJ+1D0I02stltIt0tN\r\nl8Quw+txXNhKwRLl/godJTuI1L7z7CV7K0cWieslLA+tn10tYPeJz8gd7GXy\r\n2ex+SXxCBOQuPUQ5myRhr5Ka52g9DIFKdWVEa+Xe4loW+Vk5uUbzvvfrTMRf\r\noSAiIIgQmqbz8qIc65mRFZ9lzor2N7qKCah+chLfjsnATY+xTS1qM9CFX9wz\r\nQIkHH+AB7F9hl7DsOREZht9CN3jvKSQmEFi+V0DClI70wfZiR/NzjFNOBfpa\r\n+6BsY+fYar0UvCpsgAAroKAdVa+sLTrcXvbdL3uLfi4FJwHy5wMNfXGlTalw\r\nXcCKwX7AgOJP7e4FFt4/nvYZXq5rZeAxgY2bq2ciZToCE0gyblVyukbGQU/g\r\nemr+3hPfYuvp4dgwp77x8Mt5nT3280hXE86Tcmh6xitGVVwJN2Acsq3OByt1\r\nqio17jz9ZghogywlEP2xHBX0lCWMCYJWuO9HzzNP5/x+IQrnnLjwnMm0J3dt\r\nnvXjlENoaIbsF8oPud05yaz6+cs1h3FhlRmD7dPqUcHJmbsiXKXrXIn3OVbP\r\npSUHQQYpMnhN6XjVaafnEIK17lht2NRzGoIGQDGEN6oMq0z5X6AbqNOvo/+n\r\nk9iLuK7020wj/Hd6uP6ihd6LmiY5ftzkZns=\r\n=PtS+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.20.0_1666876750964_0.45208791510146207", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "2751948e9b7c6d771a8efa59340c15d4a2891ff8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4.tgz", "fileCount": 5, "integrity": "sha512-xz0D39NvhQn4t4RNsHmDnnsaQizIlUkdtYvLs8La1BlfjQ6JEwxkJGeqJMW2tAXx+q6H+WFuUTXNdYVpEya0YA==", "signatures": [{"sig": "MEUCIQDGIPPZpTmYjudMVtStC/I/2WtwG321ayISyt/tBFDUZAIgadmcVQgz+lHCvTf4pj59HXfrbBTOTcCWCXng2MEqWAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJjg/+Ml596pT3fYIYmLIvtbOPdKzxn9wSNaGjUcpvpTJfNMcBApcg\r\n/6X4Q+pag4j6fdOEmmUe5Wt34QpYqOXITITBAorze8ji82PjUa5AUZSw9vxj\r\n0HoXHJujmIYvbmjJQ3818hHat/A7qmmupvEcjYzNpeeko1EIhb6DPz8u1jfO\r\nKC+lV8ym1yWipKeofw0/TIwGu5xg+wzqPzcPRd8JR9DF0A4t9FczntfqC43b\r\nPUAlE7tRylkRBaypGb3bE1iM38EBTFeOwM/3/+FYBsrTuXQ4uCGjgNFPVrPq\r\n5NXgUi7S4LVMaYVxYtudSdqpmSBlgO4EY2E1lVDGI8mGsT5R+Tbn4p0+dUUz\r\npqxNChZj+pvpQzJcrpCSw9L+ZMInnjpzlkQAOJcnPnO+rB0eHyFQt3JbCxjY\r\nhEx8bvRkR2n52n9EKLeN8T6APLkS28twrWrvUGGGFDoDYPsaAmsr9ICNABBz\r\ntNEl8a1eeCZeDTJzPngq/INS8nfCKgv4VTCaReFzQcvl0Rk0riAHZj4dKVE2\r\nXBzNb6c0MPrlis9nTVr4gQ+E8Y5SVcQjpQ84flSmEI7KTXEEjmiYSkJBIzt1\r\nAseujUqa+o709CFbGrGR14Mqaf6GfhQ/cltZ7u/iAoGlTHMADWcL+lalHU+W\r\nCdrorbkdAx7i0O7Ndh9ZHJRD16vyqRXALCc=\r\n=yPgp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.21.4_1680253313250_0.7355961887422251", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "ef96b9187ee49c891ec391b02ce8a52cabe48125", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-j8vYBkQdDnu1iwYWEHph6k2JIr/s6Sl6hKlAibgYSCKsHOMjFi+wD6NzDLit2ThHrMtBxuFHbVGpmEEEPqYUZg==", "signatures": [{"sig": "MEUCIQCzGCFN0YHMsUDc0uREiIWXOR4eWmo/tKp9znadBROPtwIgCJL88ntP38p9q+5L5ryu8PAAW4LOKY9tGBwKEtTlMRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqyqw//RomsoHfRZNcG0p7PWEC5MZnTwEbWzEWS/YLBx9MCAgAxTXYI\r\nsRZ1/1IaSo+0emkvPDUGnEUzy/emGp2nGllYG2l7x/8QOXZriJG/VDKlO4oL\r\n6i6s4p46t1UcNXZMPtIC8uVfcjiBVMkh450OjhHDWokHQFAmtOKW3wpnJIR9\r\nwS/GlIvZ92F7updLlraaRumfEaJ8KF/yB8fgeWMh5OJZb4euARuh2R0MFc28\r\nykB5f6k+XbXyVf3cn4wSFOqcCaCLXW+rebdjHTQPJFu8xkQV02RwFW/FPHGe\r\nDlfMYnSIxLDOr3bdCDCeGWOwc2O5HGKAv97tZxoiM1tcobORD38jNW0RyFmI\r\nMzGQqYHG0voASG5qAbFCy5wLKugeYGk8BJnewLEQx1ccPdGqDz7sRUhMCKN1\r\ngXZUnufW3qHqeFbTFWfVkKjsvPP7rrmrK6S3DXwJtELOnKxmwAH1hQLw0zMr\r\nPceRdDtdCsLgDg13E2/m3OzQpovGjfiTovCRwa9A7zFIuAhfhbtRDMPXFnb6\r\n5JBOzTMMlUwFsihxb/oMFi7pzt7huaV6Xeg2IrIeRDSrlOpmmmnFW+B3rzAW\r\n4Y/N3WO7kS4+Z4BB8VOfLEFkDWikKGOYr1gDK8jiW6zM9jqtw2UdhYVF0ZHs\r\nO1k9p2acymLaAafWK/0TEuZnP5xjbpuwd/M=\r\n=ld0d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.21.4-esm_1680617360138_0.9752761936396166", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "7a691f388943d9d52dc389cd6ba0db4070bc6ec9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-S<PERSON>eueZ3EXREh7mNGItmH9bA2ZbDokxdJs2Y5y/Wvqx19VAZVpZuLZhJ+UbM8lygFid31DbnQzSOxM5tB+omLuQ==", "signatures": [{"sig": "MEUCIE3ZGi0NFLKUzvYJwJbeXnyYcY33hJ9n5FOIPeSXX7WzAiEA6AeYVL0iTtyvGxH5QIBXt2mZgvVLMF+iimjh+gD8/nM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAmw//eizUAm1f6DEVxiBywKYeS6QDAX7C7A0yjLTz0ccgDZ80CxdW\r\n3pVxkqspQyGhgokRU8seA8oP4ZxhrfewPhC2F4VxxyURaznYBtkr8wlWQj6C\r\n7VWg5TKF+esTulo/oZ1YrbsdrWmuCzhsQtDAcmf4c7zS7Tv5jbnOPoLncoXG\r\nLd6NdsC8I38/RLU/me7Ftd6nPYzuCsxgRB/6oWkTZ4ObEblvzUQTrZvcIswa\r\nErVTxAzxycPKhKQtw8OWtu71T3tkse2VPuwPrzn8GuHHJIUlejuo9trZb1V4\r\nxrLKaa1ct1vkIeB8gnCI+Cb+MuNQkGgbhs8Sw/A70+2LoFiEdD4PMYbe1nG7\r\nEuTybo+vnk4aMSVgUztBlwKrJ23edpFjKoWOaUdnUfSMpzPyg7p+7/F1w7ff\r\ncRCDb/XI9XxoMhzsWkORHL8EzfcNq9Aag4k4vwRRTOn4Bcu5M90GcC3aB78a\r\nW3WVgcdWMs5cek91TKVPbWFZ7C/3TiC8VlLN7qlo8moIud0l6A1E0yFKfvGa\r\npXrajfAilrYnS8hfpTCoPL9W/DsCOKeLSXwPluYds9n0lpGmCUynyFUOISI2\r\ngrjt83lMQQOLM41IIyyZOz8aIA5/tVXvr+liEb78PfUo/ReBwU9LmYiRtcj5\r\nhOGR94C6lgwb2U+ZePzFn0mlBRoDe1wD3hg=\r\n=9gTY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.1_1680618069684_0.3724214047905283", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.2", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "51cc9188a50146bfedb6c4439c2c8117781eee7c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-0w8vFDfwxBi7K6gVq0NRk3IIz2Qg4X6UzEroVImrzpWcvW+EP58/X+PyowcFC0Vk5reT6ZGG9248KBzJOiREMw==", "signatures": [{"sig": "MEQCIEqXpdfdPuPwuK770zkNxPmjkmuVJv3yvILzvwRhMpUQAiARhDAJwO0mn+KlfofCMDfAYhKMbTuC7RJ/1GYe2Do6FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1fA/+JqwWbFKU9GoEx6D/9a/t3kl0Foe0WmJEcHTjDxhD1BS6f4oj\r\nePcT+NAuIK1EiGBy3OxnawDSnVTqoONfKB3TcWNi5W2+xIqIQoImC0dTvldw\r\ncqlxjkbIYp21kR/D5apc401vDhPpUWA1ZEvoXkCdLT3g4hDzBtJ+jz4Fj0UB\r\nYaWUit4EmenmwV4tgEk0TpO9qSIosMTfMqzuHhhi2rDa5cuuty9JfgpD1fCb\r\nP02ExJYMjGC4TNBkgfEpluGCHbuHGQnQ5H5g2jyCGVPXnQEzGy5t7FDSWk6m\r\nKnumGHgx81fv3IVwgBEDtTcOuNoEVsVwUsDN21N+Gnv2Z6qih0Rl2HdMTEAn\r\nWEPBrHgvgCpw2PafwhngVsXyl/PGcto8Z46NSdHKgLPgFN2Sg/AOHlUwGNex\r\nCptCjGEjbmmEoMikghO0UzjEutEIo8HOoJdn8pUO2xZ9CF6eLdQ8Gi4Ooj9e\r\nnQ2dRVBwXwgS/rFiFZJL8gE4zYRPP5TUS8DHFhPyUEwPIpNwZ6rsk6b7bw3t\r\ndJi8kvFf8oeEXfJrJ5xzE+h8b8bFuGs3C2MYYtudjKlsHuvcW7bhGnkhjOT1\r\nOKVsrzs7HvwpKJFz+a9JuCwQ6drN2ZuVFMPK4vRNWgcvZcYcYvUigE4mET8+\r\nCtyoeRxcZ9JsYQI+wlpILAGAgIFjj2/cjAg=\r\n=EQxu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.2_1680619153109_0.2675426930567455", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "0e1d36a96a640507655752245727f0bfcbb9f154", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-x35LLzLUZm5svEj3hyXr3ZSfBrSsARd5TAfvKglGta8tIm0GmAGFlaJKkzSF2rkPseTKrYrt8ysl2gSBShtHcg==", "signatures": [{"sig": "MEUCIQDh/zUX1rbG1zgJ8jdpdQKZcUYtu14byIM7gFheRuFscAIgCwzs4WcWIc+O2TvHrkIjYlnXUdBZvCe4TAtKpYjYmLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq23RAAg97y+K4d9lsLZXD+4HHz20svzm0JPOqYhTbGg7YPtbpfo+YW\r\nDJPU6sPca7K+anhDEpap3vpusQF5Q4G3O1Qjwci5sMAVMt3e5f33WKK8tOHH\r\nCqOe4ndGC/uwxr9SGX5RnbbRl5erfKMSaTqbGqD9Px5+WaJHaZW+EuOsaEV4\r\n65kYIf3uzDZ724qXwgRmNXHU1Yq5t5wrEEx5Klo1dYR7kaf5TL3BNzDcmCrt\r\nQpFQogLSz4LVxl/UUuSCaI37ZaWnCyh5jQYH3ngs5Ds92GF6Lqy/BJvV6z15\r\n2lSGjQi3KCIVO13MfR3+Y72I5jyR+nSPiPSpegWx4JdyIGH2oo/PGTqG/Npu\r\nPLGef0sLnjnlPF1jBKJJZRXFi+6MXrhdtm0K2HUz6THJ4skAvyXuw+sGDNR8\r\nL+R0l+vmV724T8jgRnhlAChZgf/9oOjN4dVZBMwAB12LDRjvmeAf62mK1sIF\r\n0HFtLjwsrucysiriEBk8QeIekGvuqUJVJTEfvErU0EcFznmI98uaIXxcGLtV\r\ngbeaI3ADGpwBhpUWvygF6jm8q5//gM3ikOtnDM2UYHZ00lvUYBbSRUV7v+JD\r\nkosWGfCjCy+IbBmq7eNTKPQMHel+05tHvlL5C2os5MTOyV4r+cuDKXbtTwgK\r\np+u/bN1VNJEAcbQjVOEQT7EBY6KW/S4w380=\r\n=j3G6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.3_1680620163960_0.3132111669555009", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.21.4-esm.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "ba99276db7dc8c2a17c88d5de752cd6ee18845b0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-ka<PERSON>lloUc9Tejk3ZoPvs6QsFqJF/OsMCyqfqVCQwwI3+YaRW81bRs7Vuc9/wRnCqyKRctghPLOA4+Nw07r49Cyg==", "signatures": [{"sig": "MEQCIBpK+lX9pffzoi8+/y0kslZ89TnhnpdIJUfd/1bc5Ll7AiB+WGCu4wxvBMM4fTTiKnjKOMAgWlMdwnZePG+aWg4hZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorNA/+MYBw8ikeXdShgUI46Aps5yUUkggby4YT3P5tTX5HVCfPEHGK\r\n9Sib0OUlcNagKaPi9AzheJiHwljQ3YoDMk0FBu0jqatndkp2R9qLgmpZkBgd\r\nID77D+4fDnpfg+izjUU8UryDOX1MWNQHtCHrQ1ba51PRRyY2EnuPa7SiFCQv\r\ndLc6iyZIy9lZqucSitBBzN9Ymprtrxwm4SyoVGnx46XXx7/55kWFwUIQVAaX\r\n4sWd3OREJuWkWHSCkGVsFsdmPEYLzIcNYqzZXAsNUMkjP1J6OPGzjitFtOd6\r\nm0Ax2pvtYGUK6HzYcGrSlFrkuvNDoAOFV5FZK4jxaFo608cTGl3fe4KExSTa\r\neBT+DeH1BBHmCfSy87q6890N1s87p37TkQMFIgUm8zhLQ2+YgvbqvIkWQbXv\r\nC2p+1P7ZXnmOSCZchD55K82qIrmL9UWXNkbOXBY8pD5cDU/h2gt1Jz5dnAh/\r\nd8SggA9ybxSWks4fFoFqx1foP8bkAe6974bk7nKk2s2t7QvYbwHlQTom9Eut\r\nuMBhRaGpq3kykVw053DSV+tC6DP6wSyO/UhVWetsa8G5vCaKIg5meOZG0B4X\r\nhMQ3nUi2edUw66ZZYOtJ7XJ16YZXohzoY0I3dUf9jEZxZGehrANOI2wNRC6A\r\n/VpeHimcsSU+g5adKvnq+FPnYE6v5JBZScc=\r\n=+Ivz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.21.4-esm.4_1680621196715_0.023865083059742442", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-syntax-typescript", "version": "7.22.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "aac8d383b062c5072c647a31ef990c1d0af90272", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-1mS2o03i7t1c6VzH6fdQ3OA8tcEIxwG18zIPRp+UY1Ihv6W+XZzBCVxExF9upussPXJ0xE9XRHwMoNs1ep/nRQ==", "signatures": [{"sig": "MEUCIQDfJPQaysWNAA0jXKehOpBhMIsH4yWFa3ZGkQpuZ4IJ4wIgL9l8DQlt3fzAyFgdjg/2DwT/A3G0JaCk0EfHIe6XYPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6856}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.22.5_1686248473250_0.769872511610352", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "6d1e8ca14cfb52c8aa4e92d7541946b572bd9653", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-VX98Ww/P35dRg6YFd7j0AH+pUfvyrd0lFSnI67f/MJ4CaCJH9q9wnriZQkyKh12YvxzHQwjGkUYJ+Yi+0tp1Jw==", "signatures": [{"sig": "MEYCIQC5vNJg/3BNGageo00zAvS7R3TcfHIZKRSI/35q5qO7rAIhAJIfkqrkjB+SEzV9Ba1e5HrXk6KJZ4sw5js4j6Vd+8X8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5345}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.0_1689861587412_0.5524486151264669", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "a94289f910b2594720af126ff75fe671fe9b5684", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-iu/mszkJEcr09uIit7LkZOIryCshEVN41NTHBoj8KD7cVN8O55eSVTq+aG37W7dXGupap2irqkEXKYYzm+9mUQ==", "signatures": [{"sig": "MEYCIQCoksV/oYDcnIq5vQoW6a+y3ftQSyBD7mJyhPNzbgUaNgIhAPW0doLCvdRmZM27Yfz9KRmCv87MAwVv8ySL8zt4ucNa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5345}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.1_1690221092056_0.46698546725391754", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "6bcf314ccfbaa6c21cbd701e894a9c9741c7f12f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-ZlHEXuZB6fgrHtVnaQ3NVe2UcpPvMJfstp8xz8+qv7jsTcK3TDJF7nFw3mzKQ0yEGFQUMnnytuoGLTULy5UqeA==", "signatures": [{"sig": "MEYCIQCruYV+afuFqQZWn/EVjKS4WqGUd6ZdEmirucOOimdZ7AIhAJj2M+pySLszBqipj1gZow8kPhlJtueXIAHUPGG9jpt/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5345}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.2_1691594087398_0.5912882236535884", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "5089bac312f717c13543380e4cca695d20975993", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-OHq+uMOUy+20BILe17SkdjNmZLSu8QYyk6201Qo3GNIcXmBFtEzUUq+VH53GpOIdqHILxJwHTpdwUlRN3SmFYA==", "signatures": [{"sig": "MEYCIQD43iiZZUAtuST7JHS2agiRE0tZoIu1lmsMo51oNxVfQAIhAMVdX4GvI7YDTp5D7vB93rlpSV49JozH6xH6ea/iZ2Xo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5345}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.3_1695740202825_0.8595313135074671", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "7a13dfb205cd35c5973b5a9915f38b03312e6fea", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-2irZKx1DMic6epGVye9ibYemwKdpndTq5WhsekTQ6GXmUwL0JR+HFPCdogxlbhh2KNSXPpEj8EEL/L9YnE6/kQ==", "signatures": [{"sig": "MEUCIQDFYJhX3ktIXSbdI89uuWADSybnhPCqFnNK3HsprHEkVQIgYNer8AC/ZLhKckpqbaAiS3C85MkwRt32xzRdM2kPl44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5345}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.4_1697076369017_0.7465110718109056", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-syntax-typescript", "version": "7.23.3", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "24f460c85dbbc983cd2b9c4994178bcc01df958f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-9EiNjVJOMwCO+43TqoTrgQ8jMwcAd0sWyXi9RPfIsLTj4R2MADDDQXELhffaUx/uJv2AYcxBgPwH6j4TIA4ytQ==", "signatures": [{"sig": "MEUCIBZqy2vuBFYjAYJr0kEe6jUGinByXKV7XytAwCSW4H00AiEA5PXOTIGNpCbDeccjBS5oJZxYK0JTPW7c4Oux8AdVuHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6936}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.23.3_1699513425155_0.08160154339177339", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "e76b5a6ac17211bc8b57100c940db2601a2d525e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-9C8Li7VQnqKxRHhys2BsxnIeD77G1RhMo8ZLhw/B0puvp9eJGZEm6GtyFmejPhJ/lp9YnTRF7ScUj8USUXC9RA==", "signatures": [{"sig": "MEYCIQCYkbdzI/rbAvd83adaURdjwnorwPJ1VoDHfrKZcMOrKQIhAPHCANHLmM5wv3sznHmSlsNEOLhNLO3Kc8Bm++H8ncLe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5458}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.5_1702307911507_0.12779459668361604", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "0bcd77251a7f227e6b6b91901b3a9f44b1389170", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-Bb4mY9lHrPyeLB8LbBBwTyQ40oTP/LZi6Zs4+MwEzU2PhVg9gu264bq0nv7FSQY9G6juXp+AWvYhhOnTteuQUA==", "signatures": [{"sig": "MEYCIQC4iTStsh3KX/8skPzoGv0X3Se6wIoM7XbA98D1+/tZkQIhAIDYfnJGsl6/GGs454mw0mO2VATKJJY5BXaYa2PiWugt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5458}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.6_1706285633301_0.5503814703813763", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "843b69b5dbe11822ae3ccd793d2445868d3cdbf3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-o2wzteoEa5kYnVL+fBVhGAX88pUt+KTi9EWuN24xOhl7/wD7Qz28aCItTaDRm6oUHTyfwabq+/jLNWftdHdmfA==", "signatures": [{"sig": "MEYCIQDsFapLa4+IezecmvICBoeI5DNwyDk8VKI/3d0jt3fAUAIhANARCwfK1WZ56IOHMGZp+sl9IHrWFUYnaeWEdYhcTCmo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5458}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.7_1709129081510_0.25953897008351023", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.24.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "b3bcc51f396d15f3591683f90239de143c076844", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-Yhnmvy5HZEnHUty6i++gcfH1/l68AHnItFHnaCv6hn9dNh0hQvvQJsxpi4BMBFN5DLeHBuucT/0DgzXif/OyRw==", "signatures": [{"sig": "MEQCIGJtF/FzAPvLP+37q/O9Ic2qHr6ZBRLWxUuAZbuLxOGiAiBZr5sUgnc+Wv6swGL4z7BzQo30S3RGbMkLZ2ZzQCwp4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6867}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.24.1_1710841705955_0.8545301386829289", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "6ab158d97a25c61cb614f1824588433c65a08f56", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-RDB1a9YOsqWO2DUFC8kr5P8GcsHED/DwSn2uv/2GlrjJ8unDLIKdbRgdvwCQkGOnrwjbd8bD0oaPs1WXCTdeew==", "signatures": [{"sig": "MEUCIFJlR9RomtAwXIVv5oXukIzOXTPo9p/NfBDbVRoR1H//AiEAux6p+SXJmFDCprB6Gleh7fjnh9HzlM2PNPM3dJVyq8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5372}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.8_1712236782980_0.6857685756628236", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-syntax-typescript", "version": "7.24.6", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "769daf2982d60308bc83d8936eaecb7582463c87", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-TzCtxGgVTEJWWwcYwQhCIQ6WaKlo80/B+Onsk4RRCcYqpYGFcG9etPW94VToGte5AAcxRrhjPUFvUS3Y2qKi4A==", "signatures": [{"sig": "MEYCIQCoCkapLkTKQ3BwLg8iuBzf43dZy/sIhGHkhMqTNRShHgIhAKzISf798tfXvAKsJZY/wMorBxNyJTAKvpBDT25u2rAA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72774}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.24.6_1716553464267_0.9311300149635915", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "0fa0d913ce7bb9508e1f05d34b47950787c74a72", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-vFA76I6u/MpsAKS7DYjyHJh0FkY9mi0l5dchzyrzun8JztilTnqSWB1KK79yUq7T2eFWKNG4mqvrBgTX3zq6YQ==", "signatures": [{"sig": "MEUCIQDQwgE0UbCb7cNa5lAsVRcLGG1Uvr23x9r+75CxMBLm8AIgOZc4ysL5VVJK2yFqHxPxVA1qLU5cOjx/372e1VKpYc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71674}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.9_1717423443026_0.2195643674506882", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "b61e14d56d4d03a4b6a333c0c316dc13fd9d5069", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-ADjQwMxEKhebJUjdWYVCwt/2fwLTQRAJjRnA7PdCLUxMPV32WDTh1uEBb4UNyFjHPf78Tw3qHn62Z1YW1Hlxfw==", "signatures": [{"sig": "MEYCIQC3Kw63QqwQr6Mzmb/AlZrlfRCANTK4hQdkDZ6nKAOTkwIhAK6toBGErI/VlAZex6zUKus0K7kIhweXFciQ8aEwQriS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71681}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.10_1717499995068_0.9117603805365941", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-syntax-typescript", "version": "7.24.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "58d458271b4d3b6bb27ee6ac9525acbb259bad1c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-c/+fVeJBB0FeKsFvwytYiUD+LBvhHjGSI0g446PRGdSVGZLRNArBUno2PETbAly3tpiNAQR5XaZ+JslxkotsbA==", "signatures": [{"sig": "MEQCICXFQGe9Z2FrtzxHcJDVtwSJ3nTl+V4+O4ZxmIO+VbjIAiBjblOd5Gyd5VNUMPVF0AYK4VvDgcnz6KXkHKRXd/GG9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72770}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.24.7_1717593315413_0.38629963790474897", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "e9c8fff07d840dfa4cb9de7f878b6e24fd7547f4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-BXxDfqD99hJlSU31+4Dnwecz2rB8IbilBKtuGca6olgKsgqzAVrj+pJ90HO6wK/X/6x8S/KL8XqdzNCJkF08eA==", "signatures": [{"sig": "MEUCIBWaPZ1k2zkfkOB+Ic8pUHOkaDXF/r6pjpvAahFyMnnGAiEArpFP751J6VBhDGbp4WUFQc4dpc8NLTr9rXvTIDatKuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71570}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.11_1717751724087_0.9962401357870776", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "86b8b0479f5d4458ebb674f0bf4004e856139e54", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-5NRbx6VPWVqXNnMWYnsV/iuwuHNNw1+XAkeJri7Wn7pnoPfWhz4Ru2nzlDD705+JVq8mcj5tkZ0Ex+EDU8GVRQ==", "signatures": [{"sig": "MEUCIFZq7ky6rZl4aCOUy5yI0pVSOOoNgOqowgDXGdPh3ouVAiEAk91hHcMAY6bkzdNc3zi2lsh9WMULuMMAnqbBkM8hrKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68362}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.12_1722015200401_0.44574376923825443", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/plugin-syntax-typescript", "version": "7.25.4", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "04db9ce5a9043d9c635e75ae7969a2cd50ca97ff", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.4.tgz", "fileCount": 7, "integrity": "sha512-uMOCoHVU52BsSWxPOMVv5qKRdeSlPuImUCB2dlPuBSU+W2/ROE7/Zg8F2Kepbk+8yBa68LlRKxO+xgEVWorsDg==", "signatures": [{"sig": "MEYCIQD3Ath9F0fL4r4QzDgJRR9n6F5hC2ZP5YWCSaRj+uI3UgIhAKq5rOAp00ch3g95sjqtHFe8epOtRnN0atAATchgbyDP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69677}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.25.4_1724319266470_0.013396826971339593", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-syntax-typescript", "version": "7.25.7", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "bfc05b0cc31ebd8af09964650cee723bb228108b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-rR+5FDjpCHqqZN2bzZm18bVYGaejGq5ZkpVCJLXor/+zlSrSoc4KWcHI0URVWjl/68Dyr1uwZUz/1njycEAv9g==", "signatures": [{"sig": "MEUCIAOz04mDTn6NueB2wMKXU0L7Y0V5L/HdAw3o7Qa9t5yrAiEAnkLZYN5zcxkUn1/nvh0eMJIf0IUh1RnGqzbjGm8a17E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77480}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.25.7_1727882079404_0.9431833436388812", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-syntax-typescript", "version": "7.25.9", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "67dda2b74da43727cf21d46cf9afef23f4365399", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==", "signatures": [{"sig": "MEUCIE+0jPkqWDlDvRcZEwagANZPWRGBd5Rh4DtotlXGzZCDAiEA3jpc34h5VxT0vq+efB3ugj44BpuVR2QmZwB9dudV9Ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7039}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.25.9_1729610457355_0.97506534685597", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "b6e36ecf00f8a32f3b380838d289c7eea5e175ee", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-JLVXI1FxjMV5LIeume1kOX7LwlIC24NM2j4GX+UuBBvcuZrDMbEHVYziabquxbE1HvYTIijW5A1RGXFjlM/Y1g==", "signatures": [{"sig": "MEUCIGlVWaXiSDO9v0Dl24Se1CvPekiAoPkCpIsMc0ART8z7AiEAzz2MThXYtRJIxASakNJaXH1GvCah+qAaVxpYVNG8qME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.13_1729864441137_0.9963781947492221", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "c396a77b5e0ea7c60c5b06c53ac83151aeecaba6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-H2IGgHidNtLSR22oFO5ttXz+Puqx8a4qsQFMaLpE9HmbbzqUJQZwn0wOOM7j6KV+BizAeBKLs0dzL8sTBScEvw==", "signatures": [{"sig": "MEQCIEAKO1pXlswlnGTrGqXBLnJCGhrC0Rv9iclChJ9yBUc5AiA2kA49RmhYx9TeCmHdr6pCHnFIds9aizzPtZxG60yPqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.14_1733504032529_0.9472153113271546", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "f29a31bebf4bcf9b2949b475bebaae5600372a36", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-xbV7jQdZ8QFzbT1Vf964azfhPcjUFSossFvHU0Q3ePyX4tYN6LjCf3s6MeOGNF9W3/VUSG2LsrVvLDeW0+odrA==", "signatures": [{"sig": "MEUCIQCtu6wVvFd/FyxvPWwhVM9lHGdjD6kL/H5a5UMJmmi8dgIgTWnrJ1+YbvpV5W5swj5bsFpLZa60BUKFeIbVZqqsq/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.15_1736529856721_0.27858015026004423", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "dd51d10d1ed6774def4c3de33b181b6b72c5506e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-oJo0qKKzDKWyJf/wsZB5tS4GyHf6b+WMBpWRWa/Kw78PU7Eypj5obtxcR4aDuXuE9pkY8+QoDpbYZJHnDQjMsA==", "signatures": [{"sig": "MEUCIQCLlwymBYaSI4DzWQ/K4iWdOuWevvJdP8td0IMY64lk6AIgT9MKfVPEQ60G4Jmu2NfQac+r3GyQDaCDloiKrNAdfVw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.16_1739534333985_0.8033350810558213", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "441f89c30ef51c8c391d9551b6466d72b248990b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-rgruFOxLnkyi6YuAQfVqgrDkcOgx6QttZnTCR7mB8QRwz+ezn+pD2VstsajGhwRlJZWZ5/wyYNkH4rAmsoTGjQ==", "signatures": [{"sig": "MEUCICF3WJbGlfPR+BGqp2XikTBrMqg/UfBg6azVDKfIqxSWAiEApSG1gVqVvwyoax3Ex1J39TYlVRkfv5pBX3rjOmAkrIg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-alpha.17_1741717485757_0.220863639284693", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-syntax-typescript", "version": "7.27.1", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "5147d29066a793450f220c63fa3a9431b7e6dd18", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==", "signatures": [{"sig": "MEQCIB9phZ0xc12OijvnTMpxjzf5Ba+tq9oZuZOgaZUmKcOQAiAs4NbCJvxykGA8Eato/4Fjh7YciJ9bhYk7SruS2+re4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7039}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_7.27.1_1746025723609_0.46483866299970056", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "typescript"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-typescript@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "dist": {"shasum": "7d32d905a9cab7efb462f99942cca6b9b6ff3025", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-xx0V90wBW99b5MURYA98CO+9vMXGK4U0Ld1cdiZNS3+Ez8aqdY01e0/NeG+bm0AogOsRMjvwoSn1B/+esoOU3Q==", "signatures": [{"sig": "MEUCIQDtUIEOAhcFjTrcBgthKu7c8dPyhdUBcCZIGPmOElEJUwIgJM8D/lbAHkymsf47n5gCpmuIkSBM6INsdJoS4lxsAq8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5907}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-typescript_8.0.0-beta.0_1748620255696_0.48154605135895134", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-syntax-typescript", "version": "8.0.0-beta.1", "description": "Allow parsing of TypeScript syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-typescript"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "typescript"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-typescript@8.0.0-beta.1", "dist": {"shasum": "718639238a062974c72e032d2c756d1271e09530", "integrity": "sha512-ZgfSbsSjOvdyPZOIC6ofjb8oukVgYncC+D1/SvahKtVDW0V0gI5KzZCYVNp6TeYrC/NZEgI9gEdyemv7srnRWg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5907, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHeCXXNw+KE+KpeJ0aOosJWfkjn10w2z+Ozdyb5ICIu3AiBveiXxQ2eUzdUdpQpqwwr28kyJRvG4qV0jdRvTD0tL5w=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-typescript_8.0.0-beta.1_1751447050192_0.9333325802847896"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:30.095Z", "modified": "2025-07-02T09:04:10.547Z", "7.0.0-beta.4": "2017-10-30T18:34:30.095Z", "7.0.0-beta.5": "2017-10-30T20:56:11.462Z", "7.0.0-beta.31": "2017-11-03T20:03:20.538Z", "7.0.0-beta.32": "2017-11-12T13:33:09.343Z", "7.0.0-beta.33": "2017-12-01T14:28:11.888Z", "7.0.0-beta.34": "2017-12-02T14:39:10.946Z", "7.0.0-beta.35": "2017-12-14T21:47:39.090Z", "7.0.0-beta.36": "2017-12-25T19:04:30.562Z", "7.0.0-beta.37": "2018-01-08T16:02:26.435Z", "7.0.0-beta.38": "2018-01-17T16:31:47.288Z", "7.0.0-beta.39": "2018-01-30T20:27:29.530Z", "7.0.0-beta.40": "2018-02-12T16:41:28.955Z", "7.0.0-beta.41": "2018-03-14T16:26:01.199Z", "7.0.0-beta.42": "2018-03-15T20:50:37.304Z", "7.0.0-beta.43": "2018-04-02T16:48:20.652Z", "7.0.0-beta.44": "2018-04-02T22:20:02.742Z", "7.0.0-beta.45": "2018-04-23T01:56:36.032Z", "7.0.0-beta.46": "2018-04-23T04:30:58.848Z", "7.0.0-beta.47": "2018-05-15T00:08:36.894Z", "7.0.0-beta.48": "2018-05-24T19:22:00.856Z", "7.0.0-beta.49": "2018-05-25T16:01:43.855Z", "7.0.0-beta.50": "2018-06-12T19:47:10.761Z", "7.0.0-beta.51": "2018-06-12T21:19:39.900Z", "7.0.0-beta.52": "2018-07-06T00:59:22.209Z", "7.0.0-beta.53": "2018-07-11T13:40:12.465Z", "7.0.0-beta.54": "2018-07-16T18:00:03.257Z", "7.0.0-beta.55": "2018-07-28T22:07:10.688Z", "7.0.0-beta.56": "2018-08-04T01:05:06.732Z", "7.0.0-rc.0": "2018-08-09T15:57:58.344Z", "7.0.0-rc.1": "2018-08-09T20:07:43.667Z", "7.0.0-rc.2": "2018-08-21T19:23:39.699Z", "7.0.0-rc.3": "2018-08-24T18:07:38.239Z", "7.0.0-rc.4": "2018-08-27T16:43:51.196Z", "7.0.0": "2018-08-27T21:42:54.027Z", "7.1.5": "2018-11-06T22:21:24.800Z", "7.2.0": "2018-12-03T19:01:07.431Z", "7.3.3": "2019-02-15T21:14:27.075Z", "7.7.4": "2019-11-22T23:32:04.817Z", "7.8.0": "2020-01-12T00:16:27.540Z", "7.8.3": "2020-01-13T21:41:26.954Z", "7.10.1": "2020-05-27T22:07:15.576Z", "7.10.4": "2020-06-30T13:11:53.874Z", "7.12.1": "2020-10-15T22:39:54.154Z", "7.12.13": "2021-02-03T01:10:44.295Z", "7.14.5": "2021-06-09T23:11:55.787Z", "7.16.0": "2021-10-29T23:47:28.023Z", "7.16.5": "2021-12-13T22:28:06.641Z", "7.16.7": "2021-12-31T00:21:51.071Z", "7.17.10": "2022-04-29T16:37:38.053Z", "7.17.12": "2022-05-16T19:32:44.221Z", "7.18.6": "2022-06-27T19:49:57.789Z", "7.20.0": "2022-10-27T13:19:11.103Z", "7.21.4": "2023-03-31T09:01:53.405Z", "7.21.4-esm": "2023-04-04T14:09:20.325Z", "7.21.4-esm.1": "2023-04-04T14:21:09.839Z", "7.21.4-esm.2": "2023-04-04T14:39:13.315Z", "7.21.4-esm.3": "2023-04-04T14:56:04.107Z", "7.21.4-esm.4": "2023-04-04T15:13:16.863Z", "7.22.5": "2023-06-08T18:21:13.503Z", "8.0.0-alpha.0": "2023-07-20T13:59:47.586Z", "8.0.0-alpha.1": "2023-07-24T17:51:32.225Z", "8.0.0-alpha.2": "2023-08-09T15:14:47.579Z", "8.0.0-alpha.3": "2023-09-26T14:56:43.047Z", "8.0.0-alpha.4": "2023-10-12T02:06:09.308Z", "7.23.3": "2023-11-09T07:03:45.369Z", "8.0.0-alpha.5": "2023-12-11T15:18:31.707Z", "8.0.0-alpha.6": "2024-01-26T16:13:53.483Z", "8.0.0-alpha.7": "2024-02-28T14:04:41.651Z", "7.24.1": "2024-03-19T09:48:26.115Z", "8.0.0-alpha.8": "2024-04-04T13:19:43.124Z", "7.24.6": "2024-05-24T12:24:24.427Z", "8.0.0-alpha.9": "2024-06-03T14:04:03.210Z", "8.0.0-alpha.10": "2024-06-04T11:19:55.251Z", "7.24.7": "2024-06-05T13:15:15.625Z", "8.0.0-alpha.11": "2024-06-07T09:15:24.251Z", "8.0.0-alpha.12": "2024-07-26T17:33:20.609Z", "7.25.4": "2024-08-22T09:34:26.647Z", "7.25.7": "2024-10-02T15:14:39.601Z", "7.25.9": "2024-10-22T15:20:57.541Z", "8.0.0-alpha.13": "2024-10-25T13:54:01.328Z", "8.0.0-alpha.14": "2024-12-06T16:53:52.682Z", "8.0.0-alpha.15": "2025-01-10T17:24:16.918Z", "8.0.0-alpha.16": "2025-02-14T11:58:54.163Z", "8.0.0-alpha.17": "2025-03-11T18:24:45.945Z", "7.27.1": "2025-04-30T15:08:43.818Z", "8.0.0-beta.0": "2025-05-30T15:50:55.850Z", "8.0.0-beta.1": "2025-07-02T09:04:10.349Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-typescript", "keywords": ["babel-plugin", "typescript"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-typescript"}, "description": "Allow parsing of TypeScript syntax", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}