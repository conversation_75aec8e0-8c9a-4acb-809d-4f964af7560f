{"_id": "loupe", "_rev": "62-b11ab43770395b2c36f6da542db067fd", "name": "loupe", "dist-tags": {"latest": "3.1.4"}, "versions": {"0.0.1": {"name": "loupe", "version": "0.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@0.0.1", "maintainers": [{"name": "vesln", "email": "<EMAIL>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "883854ffa4ae1ff19f29ab862ec5d14054fc6a7c", "tarball": "https://registry.npmjs.org/loupe/-/loupe-0.0.1.tgz", "integrity": "sha512-WOP4GRejqAlrvPm4zQq9N+uivMOfJv4eaMRayde3CJeXo5MXqZ8Y7XVzhVYhA5zHw62oKDDbrNTC0/NlJA2M5A==", "signatures": [{"sig": "MEQCICdIxwHhQoHk67Jmej+9gy7VoCJD8XpIiWXCJw/BRZnqAiBpFQScAg5Qtz6Do/qJ3O6mLp+MJsTCkVGtO4X93jTVXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/loupe.js", "_from": ".", "scripts": {"test": "hydro", "readme": "jsmd README.md", "coverage": "istanbul cover _hydro"}, "_npmUser": {"name": "vesln", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chaijs/loupe", "type": "git"}, "_npmVersion": "1.3.8", "description": "Inspect utility for Node.js and browsers", "directories": {}, "devDependencies": {"chai": "*", "jsmd": "~0.2.0", "hydro": "*", "istanbul": "~0.1.44", "component": "*", "hydro-dot": "*", "hydro-tdd": "*", "hydro-chai": "*"}}, "1.0.0": {"name": "loupe", "version": "1.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@1.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "08a9a9fa6ad3e4142236424e985390b7e2be2530", "tarball": "https://registry.npmjs.org/loupe/-/loupe-1.0.0.tgz", "fileCount": 34, "integrity": "sha512-fYpLePicpftHDWJXhr4YOpl56LCIH/si1u7G22ouay7M4rnoGW9palHqn8gJBpVmgqURkVIY7DoxPjgZBy7nrg==", "signatures": [{"sig": "MEUCIDG8omMQw0xDX1RiufcRvZMGVIPAlVMIBB4ISOvDlX/uAiEAw/iUiJZEtlIR0MBlGQvM6k+4XdRNfRdGtEo9so/IjSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2195084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoB1LCRA9TVsSAnZWagAARLoP/2sNGk3ZXRAVdW14fnUw\nJMwpRica3qxAWwLExud7NFYa0YfJklNmlbDY5dQ8kQe1YL3d6B/Be7wJu4W8\nq+crwaz3fRuwDVkz9gqTmJ6XS6gvrOe8O8mOhlspDPOLzmJZNCbjnzu5W4V3\nppLMuSIc6L8CJAVa4KxRBC+cAdBiSl93jSsD6uaeYrm8MtIlK/djQeMqv7h+\naoAv8DvZ7qnpo+v97AL40CCihGmqwIcM8KU1rraeCJCpT83vr6rQ1jY1uuD0\nf5sZhQpsJj5Q/uFUkBI35u7+8PkpagkRDpC3prxJativSsfXNrD0EfaSeF6e\nYXcK1x4fli56KmqPF8QPzZNvHOzoG0T7FFaANMEfZtv2hnI2zKxPssB+69OK\nDJDotj6AG1lJRcRr52EbEoOk3XFluBD46bO4ke2cHNyqqnqwQuNUBTozrX5F\n7gz6zPa9tKqYXsqD0NJyu2tN0c7xwHb/K9jtPyVdKgPlg9GJ+dYu278jPWRS\n4qO8MVf5+UzCaXlRe6Sc7J6vG/UbCsXH7wTwyQkIThVthh2WaDkWzpq+a+Tr\nFaMqOSUF2lQ+c8dKX8M5kvkP2oi15tuJFl1C3Ou/yLLr0Ggp0OX1b/G7AkQt\nfJZgoWJszDjO5/U3pelkDQr+UWRag1Ji8Ts06/hnQflGw1umng/oz3X7AywS\nmes/\r\n=zHgU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "71b5f78d98381479b48e61c06dc44b37bc201da0", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "11.11.0", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "husky": "^4.2.3", "karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "rollup": "^2.1.0", "codecov": "^3.6.5", "core-js": "^3.6.4", "prettier": "^2.0.2", "benchmark": "^2.1.4", "cross-env": "^7.0.2", "@babel/core": "^7.9.0", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.1", "@commitlint/cli": "^8.3.5", "semantic-release": "^17.0.4", "@babel/preset-env": "^7.9.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "karma-firefox-launcher": "^1.3.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^11.0.2", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^8.3.4", "@rollup/plugin-node-resolve": "^7.1.1", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_1.0.0_1587551563465_0.327036733847607", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "loupe", "version": "1.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@1.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "890fa5e72400a3b89ee7d176bb27292c41a1c670", "tarball": "https://registry.npmjs.org/loupe/-/loupe-1.0.1.tgz", "fileCount": 24, "integrity": "sha512-tSkUhtz01duSpUy0AmOIM23meH5wxTqpHeKLbFy5LOAQ7ebS9eThSLtaiymHWqz5kYp5ySJOSI7hxuRMlGUBXw==", "signatures": [{"sig": "MEQCICwJtYwKZRKLPkVW4+W2a6s+YvxTXApy7vAlfqy7PhHtAiBRNlkl9bY2+Ae6cLakiet5+JN2OuSU5ywOXHnDj4ZebQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoC8hCRA9TVsSAnZWagAATnkQAKUcZkE9V0Yh+KqJs209\n0oUk10vQHHQYTLQHLwUOBsXS2lkY3pMgfBwW9ClJI2IcEwym4ORZvc/ACrMQ\n0fmXGHRwz1FR52aGbNG/0liXewHwFjO0knHW38hIh5oRcMrnibX8Gm3IK5+I\nJp7C3fCurCD706+BtqE+rrgGywk0GsOe8UDYlUUSwLqiBabC/+gckWGC4nuF\nTWQxmW4+21/Hm5V4Lx+7XAuKLJ2CH2gP7nWvbd7BewANTcKba94nwZbjyQMV\ntIOvFUsqP6SKlgCUVMPuQ/puaZGNgkc+Y/cIw5IvyQJQI2wPPCZ5gnPZGAEM\n+K2hcNTi+lGeiKR0alhWuZSFeFev5eQ8vsLjEMieynKfTA5xiLgydhs/M6vo\nAmmt5s5VZQ8GJNKoqdNtl/Jyfnh0nsbe2QiWSKVWVgyO/mdLdCkY4RUI2h1Z\nSFzkFw3oeN6PfxaA5UCOaiZ6USL+dNu8FriTpk14QrzWpYX5fiKNIEDQKMcz\nGUIgW81XeSmr02c4Dwm356gtptFZtmmmJEQyQrOj+9VPmCXVB7uLBqZCgWOl\ney+ddA5UDKrPwnAsc9JDMUwEXK2P/eROgfvdj4uhUWqA17JkHWJkhxg4d9Dp\nfE280+//sIsXTL0+0P8PuGYSTNjofEt2hjoHTRU9vaG10svls8YKtuKdnxlS\nx0PA\r\n=aSXO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "e69a6b278d7f12ec66339a6bbc0862b320326723", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "husky": "^4.2.3", "karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "rollup": "^2.1.0", "codecov": "^3.6.5", "core-js": "^3.6.4", "prettier": "^2.0.2", "benchmark": "^2.1.4", "cross-env": "^7.0.2", "@babel/core": "^7.9.0", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.1", "@commitlint/cli": "^8.3.5", "semantic-release": "^17.0.4", "@babel/preset-env": "^7.9.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "karma-firefox-launcher": "^1.3.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^11.0.2", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^8.3.4", "@rollup/plugin-node-resolve": "^7.1.1", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_1.0.1_1587556129361_0.41564494473251723", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "loupe", "version": "1.0.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@1.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "3b0c5ecfbb1bc8560e9c53a5bbcc5908ce956a77", "tarball": "https://registry.npmjs.org/loupe/-/loupe-1.0.2.tgz", "fileCount": 23, "integrity": "sha512-NtbDKfRIfq8T0p11kHJ/Wk6/2Uw/pvvwAawHWpYY+tl/ptstrSWpOKnK8BEw2qp9XvIl2K78+NdjpcHkmAiIFg==", "signatures": [{"sig": "MEQCIAX33J8+gotkALz4JktLj+tPvwJBgKK1/qt8xloI9Pm4AiBcBGbYZWyCwQKGW8xISbzA/cE/t1+Zu1eHad/ps5US7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoFImCRA9TVsSAnZWagAAi9AP/19fu96kD7erPPA4W6AF\nCIObVuMJb/zDPsIeubW5NVCSOBil2W861OUkH3sXOM3qI4SuGiRvtfrIycfb\nN9RKQfyLvqlZtV4IXz8MMT2jWU4FwfoOc/CXr2i3BHNeA1nF3YZS2FFxtTSf\n862fKId4QXInvrYUZy9LBwvOZuoSGFIdBtMbwt1n/xn2Y2CoMDbfuv709jSU\nSW4tNLZQpiul1i9WmtYNuD6pVpVicabW4uyIY/3hxTTftdRUPymH6d/6dUTj\noN0YwfdoiZE7hNWASo8Ia5K6EFXx1iVsCYvReDr9/AHWhBOAr9AaYcfJ03qs\nv9Eev/gFhAD6CMQHshZ24+2+SAnZiYfHpMPKmoticAKr3LQaqCIshRGdFkwN\nV4NUe9ZiU7dkEjR+gDkdlAaRjzZIGlYkZ6w+vFr1BMAs77WQ6Oh7Jugp62oM\nRfftuZ6G+73pFnmRnEePoJe/ZnslOfoHqbzpRuggI3T8YKDZpNlYMnPmRoHy\ngHhwM4Rp2/Cv7cxmRwjmV0oO+KzG+mfKqlj1VIiwUP825lxhtaXc7pc0eQRO\nzL6cnCQ3qi6Hy3HOssHtQysZz1m/39suY8sdQrs6gKIM32YlZhtwXc5DUUD4\nIcF/IUzBwpbI1d+q52bdiZc8jnpHWPcAw0aeW8RW7Ece1krjc78VtdpXj5A5\nYlFa\r\n=9UVj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "20087b8deadb62b1fd7d3d6c21819380158861d7", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "husky": "^4.2.3", "karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "rollup": "^2.1.0", "codecov": "^3.6.5", "core-js": "^3.6.4", "prettier": "^2.0.2", "benchmark": "^2.1.4", "cross-env": "^7.0.2", "@babel/core": "^7.9.0", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.1", "@commitlint/cli": "^8.3.5", "semantic-release": "^17.0.4", "@babel/preset-env": "^7.9.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "karma-firefox-launcher": "^1.3.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^11.0.2", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^8.3.4", "@rollup/plugin-node-resolve": "^7.1.1", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_1.0.2_1587565093777_0.6857054243052472", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "loupe", "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "85f829a3ead5e4bbe848be2f1ea442f9bb96e084", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.0.0.tgz", "fileCount": 23, "integrity": "sha512-DVv/zlnuBB638A4OHfknWN2zeGHvEEm3XYkFH/CeP4QEagtQoKHsCt75GQ7aqhAOLRn8edvYNGULD040h4vcVw==", "signatures": [{"sig": "MEYCIQCLlV0VeUJkR+pviQBXnu7AlNn39zwkbzjDdtl8rOSMuwIhAONUR7sTeWqhT87PlaTvcIPMasSxfbtkGVjL1DsH33Sv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCMMMCRA9TVsSAnZWagAA4jgP+QDvLS1sG7v8vHszC1bL\n2Gkh+F3+2SiEUEAqxI1K82UKBhh0BZrQ3H18HBooQdir5Mb6sQVektpX6dgh\n6dHIy5GWAdmB+REL/+ZSoDXIolkJp1EkyT7j2/5yfT/0GiJVnFIXbQSrp5m+\nlbWmI8zW1HzVm9gywo8Y+5ShUXSgB04KkkH30rnDnlWvvnTEtFUWK0p0+tKu\n8y3bQ4vXXdp6ntwc0y1ni1AV7UOPFGidCiDU2ooo+/CasfPHiCFZt1KDXSOg\nieUWVXGhTCIiof3gDP9U2cwBvEH1KY7Qf0+BexSIqYDuCryq99rSKnP21v4u\n0iylMuP1mIWajxhzsQNkkCg7A+K8W6PNMNIxPy2hJM//IYpWBf2qvrhTaed6\nZYiW9yiwiMlsh1D6cWXbhhfzOHvzFLFTdPcvYdm4eOsIV9jLZk0cIgHJnHXU\niSrRjTU8tMpPrcORBkd93x0X6yFWRJatiZhsTVy+nA0yMRJ/W1yYSpqGp7Pj\n05aY1YwCP22909SgRJ+PF1QFsG/gUoL2wB1Uj7NX8xTXw6GCsDMKRkZOVRA6\nPykNbTXa/iu+BlljjBoQ53Gx73UhnI+iyrK3NIdT6F/V5NKf+O4IScd0q6Xf\nTrJHVWMHpGyoGnJ17Mbv8eY0S9dYDPbLeA8X564UxOaXPog+cFk7qmj86Xxf\nCPMJ\r\n=sUV6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "7f77faa0ca815f6cd456b227b535e319316db14d", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "husky": "^4.2.3", "karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "rollup": "^2.1.0", "codecov": "^3.6.5", "core-js": "^3.6.4", "prettier": "^2.0.2", "benchmark": "^2.1.4", "cross-env": "^7.0.2", "@babel/core": "^7.9.0", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.1", "@commitlint/cli": "^8.3.5", "semantic-release": "^17.0.4", "@babel/preset-env": "^7.9.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "karma-firefox-launcher": "^1.3.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^11.0.2", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^8.3.4", "@rollup/plugin-node-resolve": "^7.1.1", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.0.0_1611186955790_0.7399785473831495", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "loupe", "version": "1.0.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@1.0.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "3b009138abbf57c0c8ad9d00a72b8835c90d93f2", "tarball": "https://registry.npmjs.org/loupe/-/loupe-1.0.3.tgz", "fileCount": 23, "integrity": "sha512-AU6CTW3ZQJENjsAblp9Pu5WLuaUY+f5Jy+zrwKCbvuZGRJZBY6Mo9anqxv1vMR9Lqs1MHz8fwwRbdFeOkgAaXQ==", "signatures": [{"sig": "MEQCIFeb3brBlVICiN3LfrFZ99BiDs7XOq86Dd61wz6M7baGAiBl2p0HXZDiW9Djx5at9CpynmCfqFM+TOj5IbpkjN71zQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCb59CRA9TVsSAnZWagAAaC8P/RBrBtqXHRn7Xu+jTOpy\nzi7BC7Gfk1Z/Oj5fW36ii41Kbi1XBsXDeKkTAFabtkBZkYwuXCtW+LxEWqGF\n4ZFniEBRSY1Jlg0EiLMxnu1XNzdMSYllqOZCGmgg6tlEp49TfrNHjaEbHeke\nHtKuXIuWAsSCFae6prm1obQ8S52aoDlD8OO6hF/2CWL8TrHxjc6OyTOlql2Z\nrCS0rpT52QQ2fSUmN4fIxBl2qgZttyt1oVOds/yC/P+ZA8KbGU/eHhsdaA8O\npccbG1iZkPruP1znHf6wC5ylVq28mp13te9EHwsn6sjN1T6vyAx/tbHNkNyB\nG1IfJEhItL/pBWS1iXzg/TM1VDqqNdC235tDja44O8OfVatOmRaryWJ2RC0z\nJYZqSEKRiQiPBTYSaZ20vJEAqJu0BwKPqVhjrz6Sg+33gD5u7S6qvIsNq3S3\n+c7jIO/jaPN/LNMZo8qcSOANTvLCJAR9XwiSej8Wo31cjAYUq3/KjAJRkPKA\nSRbt8aLEMyx+2oVruEGpTgYtcWX9sxc7rkXwbNLlZ0PfaXZgKiogPF1cJzd0\nU6vjl/g+iBWMpawN2a4hGobrTyE+anBeS37FoVmrtRdAn/30quFkTpZ9oDpc\n45USD4OBPZK5SPUlLusS8G/F8RiFXwjvDvGkVcqMiGkyA03NEMv8swUJnuMN\nmEVJ\r\n=5Chm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "d78096ad2fb46a9c4e9f10992f6142246dc96541", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "husky": "^4.2.3", "karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "rollup": "^2.1.0", "codecov": "^3.6.5", "core-js": "^3.6.4", "prettier": "^2.0.2", "benchmark": "^2.1.4", "cross-env": "^7.0.2", "@babel/core": "^7.9.0", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.1", "@commitlint/cli": "^8.3.5", "semantic-release": "^17.0.4", "@babel/preset-env": "^7.9.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "karma-firefox-launcher": "^1.3.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^11.0.2", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^8.3.4", "@rollup/plugin-node-resolve": "^7.1.1", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_1.0.3_1611251325404_0.6602571333507568", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "loupe", "version": "1.0.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@1.0.4", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "17d3f501537889f5d5156b862e93704316d4d6d9", "tarball": "https://registry.npmjs.org/loupe/-/loupe-1.0.4.tgz", "fileCount": 23, "integrity": "sha512-2gKbsRjAFUsAg3nj9UZR1LBwusxmJKU+LDhzpft50YlUMk34/qv9LEZSN2vpIW5MluGmwoMV66doXVZI6KPKZw==", "signatures": [{"sig": "MEQCIEB7nzDI8YSvqf7sjb3gphO83N5VzaFCpWMQcyuYb1dyAiBESp2bvNEADef/ihq+BXcwWeH9VE5clCzdumiLa41L4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCvX9CRA9TVsSAnZWagAAAJoQAJkiO8tdmhD8ZcOMUZ0g\n06W4noImKA/A0CxJpS0F+9Zr0M81GEPI3MMAYtk7qg3eLRBRVt7WafgfTnpo\nJ48lA2J6MhnOvIGWK0CTLwDx4asIUMXxxE/4dunOdzR5xDqknKlV10Xji1VK\nKUGUajRrVTz5IlotDbQYX90YU6J/0qJ1T/lIpaQVVGfSkhZlHxvQB8o7dMD5\nFZSrD9DxgEeQZVWdfQKI2AJJlPIqtMnOeB4zcYYVnxlftakiVTLMukIxpv1y\netriXwj8B+/UC7WdUfIrnQEUiG5ZIhBsJksirRcwMv3MuOQWvGD6Tezf+v1M\n/5vLHgsywZ6aYu986w2yFlQBjmHTrApeve5C9+HoRJ9Gpo/IgIWzSZ7m4mQV\nuEilc7XTVkA/fiQIUp7M8F9e8sE40tpzvmiBuzyN1X2YJbZ6sKkQXkD4Gv4v\nsTrMpfIqw241cST1fHrHZayGnm+44GmD7slAqnioXxoGbQ+DJU81NwZnBCCc\ngozckc/e2dfk0HC1VFhlfqZpwPcyCKNJQhv13a+Zf1SkHQJoHYY0/OE1EVGb\nVC9wsgrkVGiZlmX4b1Wyg2xEY0UsZyJE6iTTV04NOyz5Yf5jTEB3Gs7G3efS\nwtCdLRw3PX6atfS68i3MPL1cvqSX5iqVEyoDtR/gDTGPh3LC81XLluE/ZEF5\ncUf9\r\n=NQNM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "****************************************", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_1.0.4_1611331068695_0.9445793215602492", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "loupe", "version": "1.0.5", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@1.0.5", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "2811cc9b083b8228c37489a2ee7d8ea7c7807c62", "tarball": "https://registry.npmjs.org/loupe/-/loupe-1.0.5.tgz", "fileCount": 23, "integrity": "sha512-x1DKw8NBoUM859Ul5rBvybT5SIheB3t8ClJohCxsiQZTCNbNKgJZGO5zfB6MHqe4cY/ZiuhFUfOrhCjHSrSTwA==", "signatures": [{"sig": "MEYCIQDiY7WoX728C+QwmVqAuGCroScgrDsqAh3mKdG+bCljqAIhAIegym0ivOdB8pwwqmEzVIr07QpBOBVIlSv9eZ0PesWH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETAMCRA9TVsSAnZWagAA2qMP/jMfizVnM0L+TazN4grk\nLX23HalhE4Zta81KP/nMfdE1ZTY14LPuEtR/XnUdpcEXkorcke4LolYdpBNx\n5706h4TKOuYn7McgJ/ZlFd2AUzOewdnGyXHEU2eDZUtP1OXV+/5rLzlYweTh\nnSQdvj7Eif3wCQb2+n1Icmfp9GeXKpR9TaKsBWT+iFyJ8osYO5VP/FyJHJ15\nUkiMX6tyVySVGcG6ks1rAoSXCgGycWemRPpjJThM4JUoiEkX1a6NkUEwr8oQ\n3xgoAnNgBWrgzJGuAwLLOYcEW+2bI/4TPrDg5D3TwJVqgIgmr8V/Wv193mxw\n2S3qcspnQoLV6FaDYKyRYeXmlH+2J7FkPdtPBmQhgIZDusT7Oc3qizLamG2o\nDNTarXXQzdyY2GqdHtrERW9kyXsU81NkdKFYwrlRM/805f9PRJmOIWFM40GY\nQdekYC1/aPhMiBwKFxdbMcEXbnRAH8HRtvRZp922XPFweMGIAhBOZvB3cEaU\nMpjIW+IIbuOZKNYWb/dgIWgp2a0AWQ1ASFwi57sZvyEDqFuuqq0lOHj3udzw\njPVAD1pU1zNy3/sThsPIrse3tH8lelYinRmRhClEhcIUGU7vKBfUtEIfYIME\nfwRjXnRctKqDJrTs59DqO5BeB0KSQF1xI98OOQyIJhugzAxRGUBmK+kl95/4\n6gat\r\n=Aqn3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "20087b8deadb62b1fd7d3d6c21819380158861d7", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "husky": "^4.2.3", "karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "rollup": "^2.1.0", "codecov": "^3.6.5", "core-js": "^3.6.4", "prettier": "^2.0.2", "benchmark": "^2.1.4", "cross-env": "^7.0.2", "@babel/core": "^7.9.0", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.1", "@commitlint/cli": "^8.3.5", "semantic-release": "^17.0.4", "@babel/preset-env": "^7.9.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "karma-firefox-launcher": "^1.3.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^11.0.2", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^8.3.4", "@rollup/plugin-node-resolve": "^7.1.1", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_1.0.5_1611739147419_0.6865725551401052", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "loupe", "version": "2.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "7fa5337a0159052f4559b8f219bb1bdbeae9e0fb", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.0.1.tgz", "fileCount": 23, "integrity": "sha512-HcmmOAsQncNgeMqLG3gmdS/RnSC22wjz+EQWfxsNfFsBBZXotDBWV2V8Kt/O3t4wuXv+VuyCe1FM06oqruLaBQ==", "signatures": [{"sig": "MEQCIFe5Sp07b3l8C6oniRCoU2cqxpr+kGdRkPMESbyhyTb8AiAbUw4XM9KUhl/Il5hG6EBqCfcwolqtJFbzhBkDuKA0Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETHJCRA9TVsSAnZWagAA1swP/0dZWGZ5m5s23Q8MpoCy\n8OygMZ3oz64gQXlpKeuh5C2NHkeYn70Wt5lrPPoaAiu59iD/YpWHFwD9JZnn\nWN/j5KM0a/eD1eQGCnBPRAJquZEqWEjBHjstrMo0qmyxM+4ekVFaGoHRpdUO\nlrd0Riyd4x30/nyCXFqHjaU6ZLmZ/2zzJB/01Du4WslUxyLf812F/rRhKliY\nLdz/S+LPRkwAP7a4cvrsirs5r8dOVDvM3qfhQChBQ3lcFiJ7QLIOVdU00AwJ\nT+syU5GPDzT/ChR69wT0JDsR7xPakrMKGNWGP3PAvdcuRNIkZld0Z/6Eq7q3\nhyxLAyNoNDCPZuWjoVUJTdzcnIRaqaLAT3ltj0qk52czF+p43MOcKhCm8SKi\nsdZ03GGZ+6wR8DAyKJSGB59h3Fa+t0BZOnjXkjB2PRTd0xjrd408ZGMyfC4Y\nw4hTsew/AIHB1uZsYtxC8w2jWbdO+UmuqxaZxnuKDfrHH3sFsl/NOoDk0q0J\nuoqan34Wdx9boNFFZw8qaqyrCqoKfzg8z3b/5xQSJnKh5IW/gfCv8xBA9PEa\nppbxCMWLeged6wV/A+rVZe1TYFjzLlM/FRJgV62i1CTtOeoqbfCkyjAhJrzt\nevi5tscT4a5qT1hBd5fKG4TTPWqvEHvbs530ooOqzCMf+VF4TyIOnzsPdwru\n6t6s\r\n=6RNG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "****************************************", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.0.1_1611739593071_0.9997111356204664", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "loupe", "version": "2.0.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "8ea977cc7d3c6990de0f83915e00462d5b596dfe", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.0.2.tgz", "fileCount": 23, "integrity": "sha512-yorUFgaw0c5VcSL1gvzhqz4Fg2NZRvToD9rmPRwE3c1eWZHVMhORQAczxuWOlzShzPjB5VnV5dqIUc+DY5Bb7g==", "signatures": [{"sig": "MEYCIQCf+gYMmmIgidnzTVxB34XNZdQbCDDOeNPTD+xRE+VlZgIhAOj6oEp9TTVVgorL8RHkAbKmyEc19GEljA2kGCys2uhX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGTnOCRA9TVsSAnZWagAAC1kP/joQeVRiPA6MJ67qcKVX\n7MaCZlM2VoNDw89ZAnIOjRDDRovTa7s/MoZyyoCvO1r+VqyyZKeYPdrq257+\nc/1oUWLnfz13FxQT34CqY8UMJV0CVngHt5O8khIiky9ufu2CECZGaFE4Paop\nhuQubGyngQn8033zF4llJ57JrVj6NhXt0j7UN+bH4L/tjfWXpGBvLJCdGvFv\nB4YKWdcJ+YuXoxCC0DJtxPjYNiB06Zqf30h4tWlRZjToLJWY+1Q5WIjvYe3F\nnREsHOr71XBnBYhhCw7bNg91jsHgdnp+xnV5a1PjYW2pDwKJHiAGpsYr+hFP\nDo1chXdQVgcAxiTSqNXC6Wf/Pwc///PvFvrXdYJMD4JAvRUXBxWtPYeJlVRb\n4sfC9NumVxIMwt7/zSWMkpxqzJ/M7hixMHQVK4YRwiLowtoaaowz0ec22Bit\na0jQdaSspx0Q/LLoA9224GgYzolMGUTJKJbxHJFJjlXNCWC6QsB+5IfJU5bM\n8Qj9qwbVu6K0hKKwcrRLy/ussGd3rGluRAEDIySKvxj0OwRP1BVMB6E/xtVP\nmJXe01KpuLyD8V6S+k4NO+zriDUpbbiggg1fIrCdL5ywOB1jd/vPcZgHT+Jw\n8TuTIpfxNMXg10CwaBDkAECHBgtoKJyCgCVL5zWq27S8ZEbtovHQ7iCdU1O0\n7xKS\r\n=Wvme\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "8d6cc8347102a3b6af0b50c97ef44febfffd187f", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.0.2_1612265934052_0.40020565768809746", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "loupe", "version": "2.0.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.0.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "3d93edf6e87de64591b5be340d94bd34a55e3486", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.0.3.tgz", "fileCount": 23, "integrity": "sha512-EO23oee3x9JYZVQM41VrrNhWYC3gpn3gvXhNvDzX4MUK2v2lrJUNpbt3G25SASqW50nlltu7NLc/ERJTtoCWDw==", "signatures": [{"sig": "MEYCIQDlOrzyVcZ+a7RZ3ONLBjR4qNhrCjGX3Ut2FRNqUCawKQIhAO+Q3vpvpY6s/9L2Ga4wpWhuy9SW/Jzbcg6kl12JMV2j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHDxICRA9TVsSAnZWagAAahcQAJ0OBEqIbuPCtPS1R5ik\nPMbyYiw2l7CTgHqSzK11VAAaB5idMRa1bWchzMPXqMhTXWn2bmdtXLg44yZX\nEeBYHf+PVciJXUH1ljBcPcdkhf/ctQoiD/YgRvMozHRayNaMvuPRPaVNjLDO\nqHuyh5P9uhWee4Ausgip37H5v56hApx6SzLbcg/xU5xTyMq+ASnCzOnjNiO1\nJOEiparU2aJ+EevtYfb/B5ha5M3UhyKfgtLy60cPnKI7uJneKZjPw4z5RDlf\nPZ++hGug88mlL2OAyyqPou+UoRQxooU8Y6sUNST+dXjLcdfRz+Az1AK4mTMP\nELlBc6eF2b0EzVCH66+6L75b5dr+2vTwkc1yHGChLcyLCzTx+QWk2Y2pvuSP\nPgQU2Z6BfsV7WaLKgUwVGM9OOnL3eedZUx8nrlv9tQLNlI4W62xDbSRAi+iX\nHiJ+mwaCD1A3FaNLpE47pvjp+RkJBbHPtWPZIAlMoVuYykU3LfVp6BhuLVll\ns8zxkoWU4N9NnFRf99Xi3mqEFj+ySLh9CX4O2XQiqYb8xRphINO77f+k8x6c\n1zoSqbbblP3sPQxSl13dR9w5lqXxJXTiTDuiIosnsL49AS2aNDBD3PjEtW9w\nOyFAqinRnfH1l8LvAvUhOyO9F7UEb7vPacZC53yKdUa8Smfi2RSTDgU/81r6\nDwm6\r\n=LpMc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "c66dc49438116a49c712bb5d6aeb252214b625bd", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"]}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.0.3_1612463175126_0.6440517365927483", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "loupe", "version": "2.1.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.1.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "7baf9295bef28cb5d00aff0f81774f8918e330a5", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.1.0.tgz", "fileCount": 24, "integrity": "sha512-CZvDn5xhu7mBYaADNJDW6P2j0bxKwETa2Eh/JxHov2XSY6RkY4QSkPxovt15SKGznPv+2VVrrHlq/5ECLCi2hA==", "signatures": [{"sig": "MEUCIQDbGi2JcX0KSKBL7lylPMe/MxeYlimp9QxH6iHptTNUIQIgK5pbevfXQ2PMiNe11M1vS4NvwoR7DrxO3qKdor5kZek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPQQ+CRA9TVsSAnZWagAA/h0QAJ4L0AlvpNPW48hAnPtT\n3Y8XYDJ6Cf0F9Q9QKo4JZZUw1WKPJ+8GfewwvrYt6PHt902ghGlhYgHHxelC\ntamnigX6YVffIY1xrLKtU88fUtWIDM0ZEC72O8OGUR0ct5ba+0pAbNPKVfPz\nIcW4sOkmxQtT1dJXqNvLAGhMhR1YrdCrmbk/hzzWYjExNdrAzNaSRGcDocnI\nu9bu5LaJ9UADqsupQGofwN7jOaMXigcQ4OYCvSIW0CQtY6AjHKQ517mNefJg\nmG9lRVQNgO6iCD/PuQLH5Nx1K9MvC2NikHfcGbpJQT8h/ZuE1Ljbeiiq2hYT\nVldFlMyCXU5OycvY0pa3ADdXNxqHlohs0M4EwXY3dL5KR5XBabsLPFLQaB3V\nBCagTOuhRSBEtdcdttwamVCx+Ar3BU0sb4h2qgnz+G0nJByn7lpZQz0ciiVy\nV9sN1LSZdFy8ICM6dT4ryUKFvCKkyNz01hPdm7WlG4b10cKqQsQHr9xejE2l\nyqXsFs5b+QDdzcf0KL46P/Q0tDRGzrFG+GahNoAX415Y6QgKskhEsPaGn1kT\nUBBgcCtCbuCoO2l0R3vKcBLU5fZ2GA2Bcki8sRYYX18s7IJp7xvXCZZaN5NK\nlNAJeqmm65Tnm5eLjj5xcxC41+BxBSdcoqO/pInDBmluVipjQQnakGHgqAxC\n/iR5\r\n=eKoi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "fcfc58d95677a2829bb4cadb1840ba49843cde0a", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.20.2", "dependencies": {"type-detect": "^4.0.8", "get-function-name": "0.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.1.0_1614611517622_0.0858290262939676", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "loupe", "version": "2.1.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.1.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "9550cbd24728ee811421d8fbab0ea6064cf33ab0", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.1.1.tgz", "fileCount": 24, "integrity": "sha512-C6Hqvejc5VLgz6L4gf1Dl1bBLZ1OTkNG70K4jtQkV/+y2JJlJKHGxe8roH0+hsO2wgZGo1p28IlMyFFtF1UUQQ==", "signatures": [{"sig": "MEMCHw2qkfyMdmMOxVlMuixGD9p8aru0dcBE4i47nAkEBLUCIFrZAiw4Z9ygPJPylRwnU8bafPHVPUeMnni6WVDwfvWf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPTH2CRA9TVsSAnZWagAAVtgP/0jSUd8GchaoEWGXsIow\nPGHSlILt8EliwLQkYyOV5j2lJ9q00k2sUSYBvowKbEWK4SHO+/DljKrV+QnG\nCFF+NdGtWpU80WiIZziHLi5thqXGyuk2fcrLh9ZC5GaELPmR9AjT1+MJpFHH\nN6OR2qoPmWqqhHyTxqY5Dz20adLEmMnvVtHzPbKQ1RWOnPXKDrhz7zJuh3xy\nhvKaLd1i/3TSj/WKnuSlksW3wdQ36thdVYXM8sYcn+YPFFY/tnbE1r4gEMb2\neSLLuzYoQ2oUgI8OCoiLwxgARBuXGUaFqOeneaEx5315utiGO1ml/KjO8u+r\nYGpFsTeHMN/s4Ludsa1Lw5GcLrAaxCHkLTG4Dze1l8GwP0xe5rtBwV/H7URq\nKJa0EOkc0u/Rm95v59X5EOBXB2RD6xxP/wYKpJbcbvS7bkb7TuIGTQu6ZSZt\nFAl/XujmnNPETHD7fIzn5JJs21WAe2DaJFCWIg9kXbgl6dmfROj+Py4F6QAg\n5kKPMMiDJBBpXwidOSGjXoGrFr4hI5UBwrXJfCghxjQDxpXEkWGArXhM9/02\nYL1gO6GtiXZpaxnyIlflM0ojH/3kPJvimTphWMsyILmwJhN3U2ZhC+YjZAoN\n/YHncxlDtBIMpWFdKPhXsXfj4V/MdY91a597Oa7hSGNuCVYUaChX0gwixcM1\nvFsW\r\n=8QFv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "5db74c7bba74ce393a1a1a1bb46e8270187ba0d5", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.20.2", "dependencies": {"type-detect": "^4.0.8", "get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.1.1_1614623222201_0.4943159000699524", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "loupe", "version": "2.1.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.1.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "dac5f1596f521a80f099464e1cbeb61cb7e6cbe4", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.1.2.tgz", "fileCount": 24, "integrity": "sha512-H2KMZkLZwpGbMD7xYzm1p+Jib1mNoK7modoZ6X6FcSpaUh1Dc/be0J9uDI/6+gHn4BjHS46XDW54qHy89ttvvQ==", "signatures": [{"sig": "MEYCIQDLv3DZugmd6jFTR8/uH/6c44ISrUbXJRRO7Ia1enkcVgIhAOpsUnZeK5rf67HAn3CLXmfhFiuDrlOZqupznukPSKoB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1PIrCRA9TVsSAnZWagAAJAQP/RM2ltlKj6mHLqU8XFjo\nc5hvUfxxK3JKbUunqbeSmDtBaWVCvOLds2cxKMXJXAfGch4tSUD5aZ4x+q6s\ngCKZ//yC/zCHQvDBtlIdtPtsiKMUKT+v8Ci4Au3dHXUAh1QPGJ2UG7+CG7KE\ncbkhoz2JnBw8LKeOEcSP7vad/TOkGIlIkFFinTQRnva6rLUjsh+h6Qjxqa4e\nq70SIdDSipp+aOriY7WjUHHvTSKj5Jr9UGmLAv0lYk5grRZZDcD8/KBs6ZOI\ntuINxHl0C0n18ejrqB4bqn4t+HQlzPvonZYwoc0QkOpJxOwwoQnntI4vIplO\njokNwSg7UHZdR1msbFSQDwmzhufLRJUUujw8dsbhwWKFf8tEpkogyF327/74\nsPk3vWHt2IWVCT9hpsLtg5ndFqnzf2b/TN2FWNQsbVyJJmoqyyRRHGIroeCM\nl1K9DkzFIylVbpAbgBglC9yVfU3YZcbcrjlg1Ml1Ik5FSyhYdra3wGl19I7P\nKIg2RFgBlBGYO//E3VtHngvnc28kogQWwIRzYtrsYlxSIYvWpdMf4tr8ADsV\n6BLftvzBGJv9cAD+pUcMWlVh0K7V13Pl7D3Hkb7IxyxKXdU0yHmqqzX43DlY\ncIhg2pDuZVzVBmyXZ6sBIiWdRjoY76duzKU/msNo6vjLH6O8ffbRA5YAp6dl\nbsL4\r\n=fOyx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "5e7683061f5bb4b98b4523bb3683a96582deb051", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"type-detect": "^4.0.8", "get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.1.2_1624568362969_0.2623603514368882", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "loupe", "version": "2.2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.2.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "87e71a9c3eb933dec9b571a6388fa0b5c154a99c", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.2.0.tgz", "fileCount": 24, "integrity": "sha512-hOtIqQUmfehWUM/tO7dMGPCL6NPFh5YpH4NC/5WjUJ32BH7RrAsNaglk11++X09naSoKOe+v8d+mPYYsl6rmLQ==", "signatures": [{"sig": "MEUCIQCIyhVWMWToaaMF8oDOg+XnDG8D1nw8FemmNyGPV90f8QIgI2Ceecaxbe2edirKsQTltfW+E72qVnRjLltg6eipXNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1PL/CRA9TVsSAnZWagAAGjkP/jPRzkGxQiCnQnopXfHN\nbBiC8RxJLLL6m4y8MMBEDhHumoX/1eSPmK/A3B+2bKP1l6eMpi9ueSaU5/Dn\nLDcCGgMFU7xmM8z6I6tzkhDy/cdh/h9hto/oADL5uZVEHX18B31ESARO7YAs\nVPaZKXpxx2u9rkdrdgc4z+O3ffXs2qHYM9GCrKDNBldLW2dTXT3sACLGvnCF\n1sZWZ3agZTWGxULxArpAfoPAdqful21tcj7uVWPgJRgeX+E8blbb/xweYv8t\nAZwaAm+Wi2pjVS0Qs1IImebKsMQz0SUTGW6FHs0Ml7Dgsqci0ZOK/VqevDlu\nqbj0qC6jWENcKNFjZ66uwMQC+d7oaytUH45yiTMuybOeGDj2b1Cmk/bRqu0g\nC2m+HSYWLQfatrVEeHxqP73xXJUMkHuuUj/hE1OLSoyOyotRsym14DJmv52N\ndkxU9pK4yy1919mmu/hIzmxp48Kry1Zdi5tu4+zQwX6GFMGEzgvFG/+mx+K8\nyIBCUQBRZT/CFEci8A/6l0FazaafbhRxJ6XwebYDpVIu1Fojovshr8yPLvwb\n/3rZuFH8nI8Ft6Ji3OKLY0yzRufJesM4I8KogWiWsz+4mblOK2CU88E3EtZT\n0s/VBcpqn4fEDHW12GXYqm7SRl9+aYvk+858pvw+J2CLsMmkj+p3HirxsUsD\nw4sz\r\n=nJ/z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "1664d708cf270a107c9961e4f02c71964c93cfb6", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"type-detect": "^4.0.8", "get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.2.0_1624568574777_0.8798613061047276", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "loupe", "version": "2.2.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.2.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "68bb8d321ac0275632ceaccdbcc5379b779ab6c6", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.2.1.tgz", "fileCount": 24, "integrity": "sha512-dxMLhqu8M3JfAOyE8CxUdIewbzzJEvk2mqQ6vurMJNrb5uWqv65Myzv8znqQnKJml4TcP+fyQIlCBbcP5Bt+xA==", "signatures": [{"sig": "MEUCIEw98BglJ+oVfH3F4Nwf94DabunaU+kzZDE8ekjqrCzmAiEApH1NZ6MfLsIajGhC6eCga0OqYvcunw/B4SKr2mFfDjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1PW7CRA9TVsSAnZWagAArBcP/RsvZ5jcxZUCcsAQP9aE\n9CsntNu6WFNZHwUJQQ0KtHShrVxe1Ypg7TWB9AI4NN/2yCvII2bb1CwW3wWl\n0ZWmJVgjdJSjPX4EUFQcPp8mzwJYqQMX+Z2wX4WcRif39wjeobBY6bBdkTmg\ny25TmQq8w/vA7j1iVaTWsQmnFQP4ruSVtPHdy5B1RazY4BRXik0sXKOSLhRh\n4jZZhFPNuAAVdwJdeXuwRbAmrEhDqaaf4q5zzvcgWlDl9LCp3SmJ9lZrRFqV\nL5gEWlGmfOZzWNrJbkOTR+ukwCyXw+II+V4XzWMrntl2wC0WwIqAOxIgch95\n2z/5ctMStJdkz/N0urcFqgson4Mgo3Jsa1/zTykSt5wsejKpiSidljtWWpyi\nCjqszOA4E0ArHoJ7+pcrd4qeLm4uZaWueVZI85Th/iHnerE2K0YJVUyZNnhG\nW4oJv80fCJhyiyt3gfSKpoNB+pd4oBfyfHX1XnlW3apdDfjKJ3K52i0eb9op\ndXv3buJ3IR3+hfUruZC+DxXSg/WtSJSWgnTNwShDYKw/lkGuy6aH19ZMrGxS\nudTLo+iTxFUwiCpIr9Al2DMxpLEqilF12EU2Oab1DUb9lBTFGyhOyKEVDFOV\nb9en96klwyGaKtmCMiBRNjogJaexzDZ70X/NZJ9yLUoqn+eOLV5JlHdRqCXG\nj5nQ\r\n=Pck0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "917757d14ceb0b4492e32c346c8503c375497c08", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"type-detect": "^4.0.8", "get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.2.1_1624569275212_0.28957278166232747", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "loupe", "version": "2.3.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "cfae54d12853592e0ec455af490fd6867e26875e", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.0.tgz", "fileCount": 24, "integrity": "sha512-b6TVXtF01VErh8IxN/MfdiWLQmttrenN98PPGS01kym8kGycJ9tqBXD6D+4sNEDhgE83+H0Mk1cVSl0mD1nNSg==", "signatures": [{"sig": "MEUCIETRWZSFQSnSAmibPqogJdwMDdqQTosQxmjeHJIZv3fmAiEAjdriW1FJQdak8tMOUzGOwHzJ2HjUCdmRRD6o0Op6eGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1YtACRA9TVsSAnZWagAAOFYP/2YYSaojjLrg7MXFyHGE\nlxR3AUARaX9VZPkeKNj9i06As/jg/GHGppVas9d7WpK6J7iyd9a509Hli/U7\npi3B/2LGHYFu8MEARaY/Pt65tgIxULEbOLvsETMVQYiDMnaWI6E34ZU9n+cJ\nW/C2PE5nQnfJf6jdirAfNqYW8RWB5HoCTpknRP3ibypFt+rqKfEEWG2miLcR\ncYS42KPnZOI2JTBc1IkxPmk6ZbeZB6BGscU3GpR/XNVxnfPwLGPz5XygTiV1\n4Ce4bthZwuK9OJEpfFfPCz6N266YcRTmXMzhqQSNHROOAU9pqXM7nn/yIW5I\nEq2C9l+hI68FDpW5INtnnhtOp+gvSJNkysdZr0RRoOvWj9gqP7nJC6yfhuGS\ngXsqd79xG7DzqKjKIlO1K1sletl8cIsdOTCa8HAlX8zt/ToGBru1v5NZ6O/P\nCnsMup4nTm7IgF7GwURWfnFcCE3Ec8O2UIzbFMvwMKKAY1eDNKWpGXNAi5Z3\nt4o9yCGMGL3g8BoqZGYi9jthZmswCrhfvD8DwhYsiWrBjJeooG4hsu9RRO4W\ndEeqjr3bmqwVZn6k5iAH7ec/W9G89sS+dXN48gEwngXhPOnAoJ1GLPj71P/H\nnIbjqQ3EydSp52oSkejW3Es3mJMHc3HJwC7P4KZUNAuZrghkVjDH7YeTAcz6\nAIIm\r\n=K09M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "gitHead": "b985e4f00ddd263b77b559fe1ec1de5fcee1d436", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"type-detect": "^4.0.8", "get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.0_1624607552274_0.7503677973456884", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "loupe", "version": "2.3.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "a2e1192c9f452e4e85089766da10ac8288383947", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.1.tgz", "fileCount": 24, "integrity": "sha512-EN1D3jyVmaX4tnajVlfbREU4axL647hLec1h/PXAb8CPDMJiYitcWF2UeLVNttRqaIqQs4x+mRvXf+d+TlDrCA==", "signatures": [{"sig": "MEYCIQDxC9ONCXNOJdrJQo4t1Zdsc8VMVhpxLvnS0kVk+tE27gIhAIZTGQPjcs4J4NIYM3Ns5ySOdWqRGz0Lc9YLBQh4ktDz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8UKrCRA9TVsSAnZWagAA9HkP/3a39jJJcGT3j+FO3Dng\nKFFrv1la81bvHeZ9sIftJit2DSQOINl2eeF/Z6swVCxjNs8wSsuu+UYopZ55\nxKE/4ab9SZUHcbKvZ6KPDLCgSlAIN8U/rCfVxpGJ0QGz7glCeLVGZiZr3mqn\nFwEvOJ1hRLvaDbwtQPgasIbeITCYdN/f/tnWmxEChwld856VRR/tx1tl7i/s\nLNFOaRhl/i3OSPV/Xot23Ni4w84qTfI8pPLaiIOBgD/6GHqTP2zw/1sr4ugZ\nmQDIYdTm00N3hnECAZ3Hp0UssLIogZF3yFVWX6JJreL2XhxsY5w9L/HXzN8q\nmdYsutzejw+R2baAQ1rjIrVwWQv0+6u/dLBS/ZcEeNFI3RVWqrTXGjeduvrP\nKcHde5Vkh/xIil9LDKHVwMFc3GfhIc+dphSeiLji54PlBAFHZd1sU9P2x0J4\n/4InOFXY71SbnEUFJg5xtHK9THu/z9xke1RMgDlV7qfs1dPwSI+PUWMgHZIR\nzbgubOOK/bd32HZPmnGpjSOFqDfWT68DucoXGmIqnj9ORgleY1EoWRyOMrko\nPc1lzd30fzacCcz6Se9GVEa3vmEXlh4M1UOn9Lxeelw046aaeowKMIzTVv8V\nKGEHDb+AfzkI4RLNI1PYJGQoLrg+nb2RnRSBfMVvurXi8DMnX5pikr6vMjRi\n3rPH\r\n=Vx+o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "browser": {"util": false}, "gitHead": "30e1ac751327dcc5b85c25c2fade1e9932b04ca6", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.9", "dependencies": {"get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.1_1643201194901_0.5683960917899227", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "loupe", "version": "2.3.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "799a566ba5aa8d11b93ddccc92c569bbae7e9490", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.2.tgz", "fileCount": 24, "integrity": "sha512-QgVamnvj0jX1LMPlCAq0MK6hATORFtGqHoUKXTkwNe13BqlN6aePQCKnnTcFvdDYEEITcJ+gBl4mTW7YJtJbyQ==", "signatures": [{"sig": "MEYCIQDXaaDuqRxyF4V5OII78olx5QUw11MKesDYQvEkYJ5TfQIhAIz65Nm057ppkU9bibtIP1H5nBA3c99veakukHaFBmCX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rviCRA9TVsSAnZWagAAH18P/1aGHbMDNhOGrSZ4PNNy\ny0V7wrkVW5v3Dptvtj+TDICTAWzgJvxBqKSUYRXA7sbu7OsD60FK3Fd3jBFM\n/TtcHD/1B6EWMAKKbuJrC/I7b+VXXNniRkriErAmpPYVA2nfNE9pK5HQe2YC\ndQyJvFVA7ciWz8GzdQz9LccCkDsyCHA5NLWKIw2SBxiDtEHkbisaSq5jqC0f\nX4uE8zLH74xV2+TylSqzUbtOF+hQtBALcyl1ivoLGiESwaoLFnL3RnK1nNZj\n81Ja+tCxVTTZBdPwK1YQrE3HmGVrbWfMDyRm120hpJyis52N4gVcHDcLfUIh\nizU6clXefgqjtHr7VX0c8RQhS78/ymeg1ci8I9s43FnNRSQApinrkuwI79xR\nlUt5cchbO6kmtB0czP0AOQcFZE4AhuCEXKFWSxyvRapsU7RjFqQbN6X3ZWfF\nexQ8IjzeXAQbvjiVKOXUECxxQ6EANgwHmxzF8jvaoyqlopGwwpgPuVR9yIuL\nk186dF988WInoCgmu3CkiqcC+meJrLcHCo6mxIEHPtZoBWGKLleAHxaDX4fA\nLM7M3gGb9a1OC9d0Zim+EKF7H2M40l1rGdnO2zqQ09JmcZZqbYEdhBD4K6/V\n045+6RPDttRo6AQImdTKIGGAaGiO3ozfU4rwwPq7HzoSNvOFOrm0UNEpQjYt\n/mVW\r\n=zHlG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "browser": "./loupe.js", "gitHead": "c83d7d0d4cc4d0b316a1df480e3a6668d767eff7", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.9", "dependencies": {"get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": "2020"}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.2_1643822050049_0.11947835885602842", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "loupe", "version": "2.3.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "5a92027d54cfb6de4c327d3c3b705561d394d3c6", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.3.tgz", "fileCount": 24, "integrity": "sha512-krIV4Cf1BIGIx2t1e6tucThhrBemUnIUjMtD2vN4mrMxnxpBvrcosBSpooqunBqP/hOEEV1w/Cr1YskGtqw5Jg==", "signatures": [{"sig": "MEYCIQCNaM2nNqD1KrkXELuvd0SKzmIYYlMOAIT1XuMyQ5dAaQIhAKaYGAJievznFbOYJcYLMFPVtl8W4wmzA9VgLchnLzwt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/lUWCRA9TVsSAnZWagAAR0gP/RmxofH0IGDme527pSlb\nVdK8e+tOQqcnZEguF4hjET7Gu0tk+eR/zqt3p4RRT6oRAFWJ9xyv8YZ76/XU\nCndpr3ObpYcp6bwmfwyH9433Xh5zAIQZyIO8OAJBh/qCo0Jg98cY0ivh61lw\nDeVfWVTdoyGRGnlgs4B4FEZGgFB205zbwo1dMYr2ovtB1RALpIXl5QErHqy5\nf+ACtOTCSpzaddLpx35z8YaB0wc/UFHIjch1i0ljUtj1t2Lb0W4c6MZpBSSS\nNqPqkTSf65wfX2q+2o779wd5Um84DU7Npt8NYQP7NSfuJ6w8L0Nq3Yr1Zzvz\njkYCYFfFShSfcox15w7Q7OTWs0DAg2ktPTdQhMOXlpAn6lSlT2Y0rNPPpzua\n2b1aMxPFULIJHa0Q9fqSaixB9zfici1QAW+NL8eg/Ad1RjQcE1C8ZoACjtpq\n28eVNESM/oksM2ecjYlC4Btu9aHxnUL7zn4+zNF7kF0lM2tLPU78MbqLZtIw\nKwseyOYEPAe0wqjiXG/ZzXeP3/iYbkq8+xCfycZOuPqe8Zo+Bdrdf1+U5BOT\nXN1USSOTpMK5Eclhm6KOwbUeieRNKilD97vtyN0afizojpB/kfTE5r6tG6wE\ntshVtgUKu1hTHf2vc+B05btfRs5Bnaff2Gms8VPpFTr2P2nt1xP9sEa9gDgf\nNnjG\r\n=lZ4T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "23c7201ee5ff50463122bf9258c0fb159dbbbc3d", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.9", "dependencies": {"get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.3_1644057878417_0.5395898656751641", "host": "s3://npm-registry-packages"}}, "2.3.4": {"name": "loupe", "version": "2.3.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.4", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "7e0b9bffc76f148f9be769cb1321d3dcf3cb25f3", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.4.tgz", "fileCount": 24, "integrity": "sha512-OvKfgCC2Ndby6aSTREl5aCCPTNIzlDfQZvZxNUrBrihDhL3xcrYegTblhmEiCrg2kKQz4XsFIaemE5BF4ybSaQ==", "signatures": [{"sig": "MEYCIQCA3CaA1BAoAU59PGrhQpQxolxvBV2eSQKCoXmsGHPwpgIhAN2osnbK131i8NkfxAmYsFocDdtZxp62muOWECWapmSg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBSvXCRA9TVsSAnZWagAA5GMQAJNuCRvAOABV3NaJwrSn\no46XwR3XBgbKgQUgNqjdZVytQrG7W0N2fFIJEsxrLh8FmV7NewVwZDmUW5ug\nzUDWqnJ8YubHw2+W4b0EJM39nU8iUwR5Gmt5dbbOCGu1QMfVhugBpxFfPr/Y\nkZZ3JToYqjZ523+gL2pollIHMMzJ87o9gRZtWRdiIk9anNHgmhZlB2tPflQ+\ndHSEwpOAHZEWZSNVRoBvFVPagpFqfLP6oiuO1qd8j9xkEu5OHui1A7fUFoiR\nZACUVy1hpSnAM9JjVoUa2saEHWlgprMiww4t7x2eR4x5bB2IYVbN1BAJGYhB\nmzMYJpVT6f0WcNHY82IvKgEZ8bpCxjrYHSLJLf3gP0PYGkkGy4MjtvNmLYIn\n0+ORkJpS94XwS0+RLRkc3mET9Fc7v5tTJVcf+7PGBoLoFJFMq70i3ufhT/+p\nrNlR3CKOImKCaVo4R58uw/zwikkDJEFq+DZtWsq8pJqMuvSTAQl6UEtdKXlw\nDPm+Nx0yXf641CvpUZJ7Ea8QxoOiNRZxzidXqI0rXWZYmTsS8+rvCIgNuSd3\nXhWJtZGG2yY8HSHMFUPY/GVV9Dh1fFL3ZwYLLa/3FhmOY32zlOOBqCQ1onQl\nRbjsDEsUJCgQ5c+vHCncEc+NADSrqNlicMw/SFqeAvbR9S+ZtX+83kw5sq5M\nyuWX\r\n=GAB5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "b4ee644cedc7d394d979d043da568a999c8a59b1", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.10", "dependencies": {"get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.4_1644506071817_0.5329298460994689", "host": "s3://npm-registry-packages"}}, "2.3.5": {"name": "loupe", "version": "2.3.5", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.5", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "b660be4f26b2be00834cf43bae4477aab160fd86", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.5.tgz", "fileCount": 24, "integrity": "sha512-KNGVjhsXDxvY/cYE8GNi7SBaJSfJIT+/+/8GlprqBXpoU6cSR7/RT7OBJOsoYtyxq0L3q6oIcO8tX7dbEEXr3A==", "signatures": [{"sig": "MEUCIGgfy9n23bTXHHJpZvsLfBKvyq0MweUIcFSu/5przZ8QAiEA9OGGJ0nYLvLBUzgLwyEpVUkC+HDo/mC0zcydnP8IkFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZrFzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZdQ//eLpARB/WOWwe8Q4e+i44CDr+yZLGpQ77A4Ww4HRXD28iTMgs\r\nzMKdVP5OVZuLGkdgLNLccKfUTHmXSenvRyMgzJ8AD9PZpdwjcxvZhVrGKuCQ\r\nfifgss92bB7gt2BI3qwqaBsO32Sdd/OcGqxd7u0zTPrKFPleM5Qz6LDlGK1Q\r\n1iwyTgTKZL4USgqda/z1fW5VoaOh4dSzqdxuYPurT2KHftmOTrDqQVUQVvNm\r\noEZIPcrbF240pjx/9HBIFA5iv9wU2IQbTX7Z04BHHZBKSqHaEsgUt1C1kjQZ\r\nOCkONNINEbDyJHwbPZRgPlFX7oJilwFo2xvc8RCEF6QuKi7cvDSK8/nAcyfG\r\nH4FCdExu9M3yaWzqHfbf128oU7+hbniX6HVI0yiYALRkT5/p+g9xYwxk0HgX\r\nAmNCu0iKHLWvS/z9pt0Fm5NICALsEzxbMpzsGXwgOkoSHsDSHDb4xUiMbUWM\r\nNOPvy54ga34EAvcv2kXCfmfxZiTD1ZKNilGjbHUiQws6HQ3/L2nMYE1Ce4iU\r\n1QLNzBgr6cs7K2nK9l+pueuPNoqhLmHBx8Ik5pW0M5RfeJwUr8mCCAH1LLsE\r\nLjwvS6Ne+1gyXvhaUeD9n+1MZnvMhIrD7bktXS6IXbyPWUhb28jLpT8S95PG\r\n1Cy/NiIE150YaIB7Leh68PzCe0iF4VTUnLk=\r\n=uj5S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "3747fa8041a1fd7e38e2e2f0249863cc4b7481b8", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.5_1667674483755_0.934405362323971", "host": "s3://npm-registry-packages"}}, "2.3.6": {"name": "loupe", "version": "2.3.6", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.6", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "76e4af498103c532d1ecc9be102036a21f787b53", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.6.tgz", "fileCount": 24, "integrity": "sha512-RaPMZKiMy8/JruncMU5Bt6na1eftNoo++R4Y+N2FrxkDVTrGvcyzFTsaGif4QTeKESheMGegbhw6iUAq+5A8zA==", "signatures": [{"sig": "MEUCIAkMEiSSCkYkHAzKITBVVZfZLuijwj+ePozt0GJoact1AiEAimEq6//A8viVPZL/tEbUWoHyjAFu8Gvdqp5mJ0Zycbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaPCTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkrRAAiHySlK6h2oTnwOyp/jbCIv0XhbfSQDklfGxiUO/dAxu+vfVl\r\ng5HVFnoQSwbkNQ+cFKBqh8EEsIyrBMeqptogDs0MCKXMeFoWdfiwiyRc0Bg5\r\nwuWVACypAWfaSbw8bYHjLja2tCLtMFB1EVp3HkqGV+y41PI+NX4sa8CBFBOB\r\nGcFLcGRdZz2VnAtdtAFy0js1PK+9JXZc6BM7La5VdXliqY2P8ONnVHy6lTOa\r\ngfPGVTA9rk78iZUJiahSb/izBBlhNsocnhmPosQC2Syy/RRyBfVMpJ9o9BlX\r\n9A0HqtxHQwpzCmzVv/W5hs84CZzT8//st8dq6CTHA2oqCTqsqxbqECMOun0n\r\nvlWLDQsAF9iVX7TA4hz8M7r+LMqNarQrTBzSL5IlVHvzgITAf3HPNdAWpP3Y\r\ned/ARxZ7tSXPs7f4F6G/7FH9MbrY9WG9r8o1ewwCy+FWuxrtUITrttbXJrxa\r\nu937rtB3p9fmsm0wNf9YRsfEDZY+kQqHJBfCeDxPyyV9V8wUJP8amQHkA5ep\r\n2eqn5hB9zsZr2DQ6dG3CAjBZOc58rIAMe2eL8Sh9AGvOeyHN1Yh6MBhRyJr5\r\n/rKiMLbYA5cjGtqn/pnqwrH+pwD61HksTF2j0p8LhIFrtFFz18mivTYWOnyR\r\nS5IGNLblgbNXH2IvpgAdc9NIbtvtn+1Y4YQ=\r\n=bq9q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./loupe.js", "module": "./index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "edd931499b4a2489477f9c753550a62a3d038cf2", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "deprecated": "Please upgrade to 2.3.7 which fixes GHSA-4q6p-r6v2-jvc5", "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"get-func-name": "^2.0.0"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.6_1667821715279_0.15927872859499126", "host": "s3://npm-registry-packages"}}, "2.3.7": {"name": "loupe", "version": "2.3.7", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@2.3.7", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "6e69b7d4db7d3ab436328013d37d1c8c3540c697", "tarball": "https://registry.npmjs.org/loupe/-/loupe-2.3.7.tgz", "fileCount": 24, "integrity": "sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==", "signatures": [{"sig": "MEUCIQD2w0czTolq/TRE0DpCgu8n4KZnnHKHdp3DG2KnEyLXXgIgJCC9kUuPl47tsz46ZV+tvyV0V5Bg4ec8yiGlCLEwNmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58140}, "main": "./loupe.js", "module": "./index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "247f94876d6972cdb2e9a4d87015a6232dcdbfd3", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node -r esm bench", "prepare": "rollup -c rollup.conf.js", "test:node": "nyc mocha -r esm", "commit-msg": "commitlint -x angular", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "npm run prepare", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "keithamus", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "6.14.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"get-func-name": "^2.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.1.0", "chai": "^4.2.0", "husky": "^4.3.8", "karma": "^5.2.3", "mocha": "^8.2.1", "eslint": "^7.18.0", "rollup": "^2.37.1", "codecov": "^3.8.1", "core-js": "^3.8.3", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "@babel/core": "^7.12.10", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.3.6", "@babel/preset-env": "^7.12.11", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-strict": "^14.0.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.4", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "eslint-plugin-prettier": "^3.3.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.0.0", "eslint-plugin-filenames": "^1.3.2", "commitlint-config-angular": "^11.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "karma-safaritechpreview-launcher": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_2.3.7_1697196675294_0.6148123937105874", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "loupe", "version": "3.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "3f520c89a6b76a79c3e53fe7cbe97ef7e62a77a5", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.0.0.tgz", "fileCount": 24, "integrity": "sha512-eDW5UhrXw2b2pCht9KJXPooZwdDudFfJGTbPENJ4J0vxLtWO/+EEg2lJpMJuv568rD3+5X2UroJqhZHjQG0GqQ==", "signatures": [{"sig": "MEUCIQCko8o1HxAZS0pd34Ktqp5q0nBS06UEiIZf8L7e1FBblAIgO8GLuW3OOCOAjXcYpQkzQydBnzjzVDDw7GcSV1oI+xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 46181}, "main": "./lib/index.js", "type": "module", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "21d183b1e4f24e071086e440757d6b6de31dbcb5", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "esbuild \"src/*.ts\" --outdir=lib --format=esm", "test:node": "mocha", "postinstall": "npx playwright install", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"get-func-name": "^2.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^5.0.0-alpha.0", "husky": "^4.3.8", "mocha": "^10.2.0", "eslint": "^7.18.0", "codecov": "^3.8.1", "core-js": "^3.8.3", "esbuild": "^0.19.5", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^1.0.0", "@web/test-runner": "^0.17.2", "semantic-release": "^17.3.6", "eslint-config-strict": "^14.0.1", "eslint-plugin-prettier": "^3.3.1", "@web/dev-server-esbuild": "^0.4.2", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.0.0_1697802739194_0.13622045461170873", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "loupe", "version": "3.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "3b66a94887d7af003946c147cc664c3c9233270e", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.0.1.tgz", "fileCount": 24, "integrity": "sha512-phbaE2fPsRe8cQ7Cy5Ze5p9JLmpiaqGT45RCUWYYjZgYPBoeC3vqrlYPj4BQ82ln60ZtM3Iq00PPC3FyUdS4Kw==", "signatures": [{"sig": "MEUCIArGPJBh923emtpzlNeQJ9eXUUnXjFBDQDhSwO6/ADHDAiEA6HRWsowWf8ezO0/BrApE2AiYXvgEJ+BYxwVV/6utfF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 46162}, "main": "./lib/index.js", "type": "module", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "11c9aa910efdc1765f52ba56b06573b719adbb1f", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "esbuild \"src/*.ts\" --outdir=lib --format=esm", "test:node": "mocha", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npx playwright install && npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"get-func-name": "^2.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^5.0.0-alpha.0", "husky": "^4.3.8", "mocha": "^10.2.0", "eslint": "^7.18.0", "codecov": "^3.8.1", "core-js": "^3.8.3", "esbuild": "^0.19.5", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^1.0.0", "@web/test-runner": "^0.17.2", "semantic-release": "^17.3.6", "eslint-config-strict": "^14.0.1", "eslint-plugin-prettier": "^3.3.1", "@web/dev-server-esbuild": "^0.4.2", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.0.1_1699026358673_0.9506600869401585", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "loupe", "version": "3.0.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "13435bc5a2751a4da74c0ec4841f56970a3387d5", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.0.2.tgz", "fileCount": 64, "integrity": "sha512-Tzlkbynv7dtqxTROe54Il+J4e/zG2iehtJGZUYpTv8WzlkW9qyEcE83UhGJCeuF3SCfzHuM5VWhBi47phV3+AQ==", "signatures": [{"sig": "MEYCIQD6rRwzJmlpD8mJ/Uo6l3Yw6EoDi0lEcSkNtZ/oQV9aBwIhAKbMlF9GjxRmkQHFy0lsQ1AQ8ZnpCcMyju4E9epo2YGn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59770}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "e732123289b27a0422e9ff9539dbb6782e3af08e", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "tsc", "test:node": "mocha", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npx playwright install && npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"get-func-name": "^2.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^5.0.0-alpha.0", "husky": "^4.3.8", "mocha": "^10.2.0", "eslint": "^7.18.0", "codecov": "^3.8.1", "core-js": "^3.8.3", "esbuild": "^0.19.5", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^1.0.0", "@web/test-runner": "^0.17.2", "semantic-release": "^17.3.6", "eslint-config-strict": "^14.0.1", "eslint-plugin-prettier": "^3.3.1", "@web/dev-server-esbuild": "^0.4.2", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.0.2_1703880769782_0.22477576654707465", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "loupe", "version": "3.1.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.1.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "46ef1a4ffee73145f5c0a627536d754787c1ea2a", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.1.0.tgz", "fileCount": 64, "integrity": "sha512-qKl+FrLXUhFuHUoDJG7f8P8gEMHq9NFS0c6ghXG1J0rldmZFQZoNVv/vyirE9qwCIhWZDsvEFd1sbFu3GvRQFg==", "signatures": [{"sig": "MEQCIDUuQ+xTLwIIAvUrxOiYjLwjrzLLclwdA1omqOyiCTCdAiA5gSKUuvhEz1IkIi91RyzxpxsEh9o0UQyD/sk+W2tdjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60097}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "9b8a6deabcd50adc056a64fb705896194710c5c6", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "tsc", "test:node": "mocha", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npx playwright install && npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"get-func-name": "^2.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^5.0.0-alpha.0", "husky": "^4.3.8", "mocha": "^10.2.0", "eslint": "^7.18.0", "codecov": "^3.8.1", "core-js": "^3.8.3", "esbuild": "^0.19.5", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^1.0.0", "@web/test-runner": "^0.17.2", "semantic-release": "^17.3.6", "eslint-config-strict": "^14.0.1", "eslint-plugin-prettier": "^3.3.1", "@web/dev-server-esbuild": "^0.4.2", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.1.0_1704705064145_0.833372208260188", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "loupe", "version": "3.1.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.1.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "71d038d59007d890e3247c5db97c1ec5a92edc54", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.1.1.tgz", "fileCount": 64, "integrity": "sha512-edNu/8D5MKVfGVFRhFf8aAxiTM6Wumfz5XsaatSxlD3w4R1d/WEKUTydCdPGbl9K7QG/Ca3GnDV2sIKIpXRQcw==", "signatures": [{"sig": "MEQCIAdlGoGcJpnf/GjG/3ECGjAyhBD2sI7OTxwCv9AOSHWEAiAB50E4RZNNUNfd/F4JaMthI2Bcsmf19Meh2nMPdjwa4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60483}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "e02467ef36257859ed29a35f672bafe24df97c66", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "tsc", "test:node": "mocha", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npx playwright install && npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"get-func-name": "^2.0.1"}, "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^5.0.0-alpha.0", "husky": "^4.3.8", "mocha": "^10.2.0", "eslint": "^7.18.0", "codecov": "^3.8.1", "core-js": "^3.8.3", "esbuild": "^0.19.5", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^1.0.0", "@web/test-runner": "^0.17.2", "semantic-release": "^17.3.6", "eslint-config-strict": "^14.0.1", "eslint-plugin-prettier": "^3.3.1", "@web/dev-server-esbuild": "^0.4.2", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.1.1_1715434104591_0.22860763629638226", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "loupe", "version": "3.1.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.1.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "c86e0696804a02218f2206124c45d8b15291a240", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.1.2.tgz", "fileCount": 64, "integrity": "sha512-23I4pFZHmAemUnz8WZXbYRSKYj801VDaNv9ETuMh7IrMc7VuVVSo+Z9iLE3ni30+U48iDWfi30d3twAXBYmnCg==", "signatures": [{"sig": "MEQCIFsJ7Hoi98JiIuCzn3Vh55m7TbZ8ZtOgpDOhzt6bMpNWAiB0gVJTII0HnDf0zaMz7i399ijxYE2GcTJX2jYczg2E1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60721}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "983691f2b9debbf3c1a468365f5091f9a6d659dc", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "tsc", "test:node": "mocha", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npx playwright install && npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "18.20.4", "eslintConfig": {"env": {"es6": true}, "root": true, "rules": {"semi": ["error", "never"], "curly": "off", "quotes": "off", "func-style": "off", "arrow-parens": "off", "comma-dangle": "off", "id-blacklist": "off", "no-magic-numbers": "off", "prettier/prettier": ["error", {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}], "no-mixed-operators": "off", "prefer-destructuring": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "class-methods-use-this": "off", "generator-star-spacing": "off", "template-curly-spacing": "off", "space-before-function-paren": "off"}, "extends": ["strict/es6"], "plugins": ["filenames", "prettier"], "parserOptions": {"ecmaVersion": 2020}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^5.0.0-alpha.0", "husky": "^4.3.8", "mocha": "^10.2.0", "eslint": "^7.18.0", "codecov": "^3.8.1", "core-js": "^3.8.3", "esbuild": "^0.19.5", "prettier": "^2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^1.0.0", "@web/test-runner": "^0.17.2", "semantic-release": "^17.3.6", "eslint-config-strict": "^14.0.1", "eslint-plugin-prettier": "^3.3.1", "@web/dev-server-esbuild": "^0.4.2", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.1.2_1728294596132_0.6894313854092278", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "loupe", "version": "3.1.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "loupe@3.1.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}], "homepage": "https://github.com/chaijs/loupe", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "dist": {"shasum": "042a8f7986d77f3d0f98ef7990a2b2fef18b0fd2", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.1.3.tgz", "fileCount": 64, "integrity": "sha512-kkIp7XSkP78ZxJEsSxW3712C6teJVoeHHwgo9zJ380de7IYyJ2ISlxojcH2pC5OFLewESmnRi/+XCDIEEVyoug==", "signatures": [{"sig": "MEQCIEmyuvUFqOCRACSZyPX/7jFccpnc3rfJE9UY1kzqXVBFAiBVkXG+uWsC1dJktrYTBk2ZrHJEVarR1vGE25FVKHHy/A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59533}, "main": "./lib/index.js", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.js", "browser": {"util": false, "./index.js": "./loupe.js"}, "gitHead": "6531c62f4503b01c6911f765ff4cb67efd98722f", "scripts": {"lint": "eslint .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "tsc", "test:node": "mocha", "pretest:node": "npm run build", "test:browser": "wtr", "pretest:browser": "npx playwright install-deps && npx playwright install && npm run build", "upload-coverage": "codecov", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "printWidth": 120, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/chaijs/loupe.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Inspect utility for Node.js and browsers", "directories": {}, "_nodeVersion": "22.13.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^17.1.0", "chai": "^5.0.0-alpha.0", "husky": "^9.1.7", "mocha": "^11.1.0", "eslint": "^9.19.0", "codecov": "^3.8.3", "core-js": "^3.8.3", "esbuild": "^0.24.2", "prettier": "^3.0.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "typescript": "^5.0.0-beta", "simple-assert": "^2.0.0", "@web/test-runner": "^0.19.0", "eslint-config-strict": "^14.0.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "@web/dev-server-esbuild": "^1.0.3", "eslint-plugin-filenames": "^1.3.2", "@web/test-runner-playwright": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/loupe_3.1.3_1738071861899_0.1295980700109005", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "loupe", "version": "3.1.4", "description": "Inspect utility for Node.js and browsers", "homepage": "https://github.com/chaijs/loupe", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/keithamus"}], "type": "module", "main": "./lib/index.js", "module": "./lib/index.js", "browser": {"./index.js": "./loupe.js", "util": false}, "repository": {"type": "git", "url": "git+https://github.com/chaijs/loupe.git"}, "scripts": {"bench": "node bench", "lint": "eslint .", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "test": "npm run test:node && npm run test:browser", "pretest:browser": "npx playwright install-deps && npx playwright install && npm run build", "test:browser": "wtr", "pretest:node": "npm run build", "test:node": "mocha", "build": "npm run build:lib && npm run build:esm-bundle && npm run build:cjs-bundle", "build:lib": "tsc", "build:esm-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=esm", "build:cjs-bundle": "esbuild --bundle src/index.ts --outfile=loupe.js --format=cjs", "upload-coverage": "codecov"}, "prettier": {"printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "trailingComma": "es5", "arrowParens": "avoid", "bracketSpacing": true}, "devDependencies": {"@web/dev-server-esbuild": "^1.0.3", "@web/test-runner": "^0.19.0", "@web/test-runner-playwright": "^0.11.0", "benchmark": "^2.1.4", "chai": "^5.0.0-alpha.0", "codecov": "^3.8.3", "core-js": "^3.8.3", "cross-env": "^7.0.3", "esbuild": "^0.24.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-config-strict": "^14.0.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "mocha": "^11.1.0", "nyc": "^17.1.0", "prettier": "^3.0.0", "simple-assert": "^2.0.0", "typescript": "^5.0.0-beta"}, "_id": "loupe@3.1.4", "gitHead": "01498de2d5f8215576d4b092432bfcc07b22dcf4", "types": "./lib/index.d.ts", "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-wJzkKwJrheKtknCOKNEtDK4iqg/MxmZheEMtSTYvnzRdEYaZzmgH976nenp8WdJRdx5Vc1X/9MO0Oszl6ezeXg==", "shasum": "784a0060545cb38778ffb19ccde44d7870d5fdd9", "tarball": "https://registry.npmjs.org/loupe/-/loupe-3.1.4.tgz", "fileCount": 64, "unpackedSize": 59520, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/loupe@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAbIsf7/Mrz0wCBZu1nxePQYfkgyU82UsOsV/YUhc3wZAiEAhTmMb3eyfmdF1RTkhXEibU9jnO0DjLJAEwy1NqpX1oM="}]}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/loupe_3.1.4_1750072103379_0.323519219525386"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-12-17T12:37:05.737Z", "modified": "2025-06-16T11:08:23.910Z", "0.0.1": "2013-12-17T12:37:09.405Z", "1.0.0": "2020-04-22T10:32:43.680Z", "1.0.1": "2020-04-22T11:48:49.508Z", "1.0.2": "2020-04-22T14:18:13.923Z", "2.0.0": "2021-01-20T23:55:55.900Z", "1.0.3": "2021-01-21T17:48:45.625Z", "1.0.4": "2021-01-22T15:57:48.824Z", "1.0.5": "2021-01-27T09:19:07.647Z", "2.0.1": "2021-01-27T09:26:33.226Z", "2.0.2": "2021-02-02T11:38:54.404Z", "2.0.3": "2021-02-04T18:26:15.256Z", "2.1.0": "2021-03-01T15:11:57.803Z", "2.1.1": "2021-03-01T18:27:02.359Z", "2.1.2": "2021-06-24T20:59:23.133Z", "2.2.0": "2021-06-24T21:02:54.876Z", "2.2.1": "2021-06-24T21:14:35.410Z", "2.3.0": "2021-06-25T07:52:32.427Z", "2.3.1": "2022-01-26T12:46:35.067Z", "2.3.2": "2022-02-02T17:14:10.249Z", "2.3.3": "2022-02-05T10:44:38.558Z", "2.3.4": "2022-02-10T15:14:31.961Z", "2.3.5": "2022-11-05T18:54:43.946Z", "2.3.6": "2022-11-07T11:48:35.432Z", "2.3.7": "2023-10-13T11:31:15.506Z", "3.0.0": "2023-10-20T11:52:19.336Z", "3.0.1": "2023-11-03T15:45:58.897Z", "3.0.2": "2023-12-29T20:12:49.951Z", "3.1.0": "2024-01-08T09:11:04.298Z", "3.1.1": "2024-05-11T13:28:24.758Z", "3.1.2": "2024-10-07T09:49:56.282Z", "3.1.3": "2025-01-28T13:44:22.111Z", "3.1.4": "2025-06-16T11:08:23.535Z"}, "bugs": {"url": "https://github.com/chaijs/loupe/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/chaijs/loupe", "repository": {"type": "git", "url": "git+https://github.com/chaijs/loupe.git"}, "description": "Inspect utility for Node.js and browsers", "contributors": [{"name": "<PERSON>", "url": "https://github.com/keithamus"}], "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "readme": "![npm](https://img.shields.io/npm/v/loupe?logo=npm)\n![Build](https://github.com/chaijs/loupe/workflows/Build/badge.svg?branch=master)\n![Codecov branch](https://img.shields.io/codecov/c/github/chaijs/loupe/master?logo=codecov)\n\n# What is loupe?\n\n<PERSON><PERSON> turns the object you give it into a string. It's similar to Node.js' `util.inspect()` function, but it works cross platform, in most modern browsers as well as Node.\n\n## Installation\n\n### Node.js\n\n`loupe` is available on [npm](http://npmjs.org). To install it, type:\n\n    $ npm install loupe\n\n### Browsers\n\nYou can also use it within the browser; install via npm and use the `loupe.js` file found within the download. For example:\n\n```html\n<script src=\"./node_modules/loupe/loupe.js\"></script>\n```\n\n## Usage\n\n``` js\nconst { inspect } = require('loupe');\n```\n\n```js\ninspect({ foo: 'bar' }); // => \"{ foo: 'bar' }\"\ninspect(1); // => '1'\ninspect('foo'); // => \"'foo'\"\ninspect([ 1, 2, 3 ]); // => '[ 1, 2, 3 ]'\ninspect(/Test/g); // => '/Test/g'\n\n// ...\n```\n\n## Tests\n\n```bash\n$ npm test\n```\n\nCoverage:\n\n```bash\n$ npm run upload-coverage\n```\n\n## License\n\n(The MIT License)\n\nCopyright (c) 2011-2013 <NAME_EMAIL>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md"}