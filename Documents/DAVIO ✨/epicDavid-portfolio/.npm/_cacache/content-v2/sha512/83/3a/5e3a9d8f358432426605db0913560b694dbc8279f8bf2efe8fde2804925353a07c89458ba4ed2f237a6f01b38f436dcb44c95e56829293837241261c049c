{"_id": "regexpu-core", "_rev": "59-5bc1c4d65523bf6b983f56646fe6a6ec", "name": "regexpu-core", "dist-tags": {"latest": "6.2.0"}, "versions": {"1.0.0": {"name": "regexpu-core", "version": "1.0.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@1.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "86a763f58ee4d7c2f6b102e4764050de7ed90c6b", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-1.0.0.tgz", "integrity": "sha512-Ci+lDRlvAElKjFp5keqmVUaJLqZiHywekXhshT6wVUyDObGPdymNPhxBmf38ZVsaUGOnZ3Fot9YzxvoI31ymYw==", "signatures": [{"sig": "MEQCIHmAye62tZiFX98pxNf9w1/4Hv4yiL7ZZ32Ga8qUwLZtAiAK1FyC8lBOiDK+8xAS78H9cGHQNYyUDs3selhjiruhxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.json"], "_shasum": "86a763f58ee4d7c2f6b102e4764050de7ed90c6b", "gitHead": "724e31ca40aa88a451d3ba9388a0993192dd428a", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha tests/tests.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"regjsgen": "^0.2.0", "regenerate": "^1.2.1", "regjsparser": "^0.1.4"}, "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.2.1", "lodash": "^3.6.0", "istanbul": "^0.4.0", "coveralls": "^2.11.2", "unicode-5.1.0": "^0.1.5", "unicode-8.0.0": "^0.1.5", "regexpu-fixtures": "^1.0.0"}}, "2.0.0": {"name": "regexpu-core", "version": "2.0.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@2.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "49d038837b8dcf8bfa5b9a42139938e6ea2ae240", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-2.0.0.tgz", "integrity": "sha512-tJ9+S4oKjxY8IZ9jmjnp/mtytu1u3iyIQAfmI51IKWH6bFf7XR1ybtaO6j7INhZKXOTYADk7V5qxaqLkmNxiZQ==", "signatures": [{"sig": "MEUCIHJz7HvQBrxJMkpuLSx9DyqEz8GS6XumLNf5hKgoF5R9AiEAkCilTKSSegG9OIy0RleKs4Ou8/n/0TFmmAL8PtQJw7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.json"], "_shasum": "49d038837b8dcf8bfa5b9a42139938e6ea2ae240", "gitHead": "ae4efe52b72ba73e9a2c0f35e11c2ff1d0e12dcd", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha tests/tests.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"regjsgen": "^0.2.0", "regenerate": "^1.2.1", "regjsparser": "^0.1.4"}, "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.2.1", "lodash": "^3.6.0", "istanbul": "^0.4.0", "coveralls": "^2.11.2", "unicode-8.0.0": "^0.1.5", "regexpu-fixtures": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-2.0.0.tgz_1454960202073_0.38952653063461185", "host": "packages-9-west.internal.npmjs.com"}}, "3.0.1": {"name": "regexpu-core", "version": "3.0.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@3.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "7010a520a963cbfcfcbeaf515902d7d6369d63f1", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-3.0.1.tgz", "integrity": "sha512-kSX9ajWXUO035Sy3/VmGEfV+A3jpW9liLPkSl94gOf6m+4VL4ETCV3fHAykuRxIC0A/hCsFwGkykehal3G+oag==", "signatures": [{"sig": "MEQCIE+CZjMYs2Wjm83ZZJwXOReU10N46Nio2LTKZzrRQCTgAiBfRU76EQrEE4aBME2InR0r/CDhAWP3/yx7EKJPCvth7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "7010a520a963cbfcfcbeaf515902d7d6369d63f1", "engines": {"node": ">=4"}, "gitHead": "fa0262270e522452c13f50bd999b8c5e3275d3f2", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-loose-match": "^2.0.7", "regenerate-unicode-properties": "^1.0.0"}, "devDependencies": {"jsesc": "^2.1.0", "mocha": "^2.2.1", "lodash": "^4.13.1", "codecov": "^1.0.1", "istanbul": "^0.4.0", "unicode-8.0.0": "^0.6.0", "regexpu-fixtures": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-3.0.1.tgz_1464849473954_0.693929887842387", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.2": {"name": "regexpu-core", "version": "3.0.2", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@3.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "addc533452c27804db121748aafa23f598e7255e", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-3.0.2.tgz", "integrity": "sha512-iGyDNXzJlxRgMDoYofncG+K96QPpEvxEC+OWN0PEtvQc50ckmaSXm8vg2/OM2mJW2VfiaskHzebAPpLmrNTBUg==", "signatures": [{"sig": "MEYCIQCSJLL1nqcPbm9js/wSgEK6KOKbC2BqXnnvESL5kPPz/QIhAK0euFWvgxOLYHslwKwXF+HpDkcTGjn0hc1zkufPACHR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "addc533452c27804db121748aafa23f598e7255e", "engines": {"node": ">=4"}, "gitHead": "cd6a9bea1fe86775e7232e2b95aba5d41723cd39", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-loose-match": "^2.0.7", "regenerate-unicode-properties": "^1.0.0"}, "devDependencies": {"jsesc": "^2.1.0", "mocha": "^2.2.1", "lodash": "^4.13.1", "codecov": "^1.0.1", "istanbul": "^0.4.0", "unicode-8.0.0": "^0.6.0", "regexpu-fixtures": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-3.0.2.tgz_1465916907084_0.49383915588259697", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "regexpu-core", "version": "3.1.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@3.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "9f0dfaccfe75271ae1b711ad7978a1692bfb0454", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-3.1.0.tgz", "integrity": "sha512-W+/X2g+w8H01wCKim+PiD6EbVMIkFe+WBzfVfvF4D0UZDtx2qiuhhagmgk1qXlF4HP7ZpXPvFsCEpfBOWFnaQA==", "signatures": [{"sig": "MEYCIQC27MuAGN4cVhmhOq+yFYaibVFfTTWEYl9LFvmxW0TWJAIhAO68Q/58hDAFaKWowKGi5eIuZmxR7bQdZIUXGA/qSLqr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "9f0dfaccfe75271ae1b711ad7978a1692bfb0454", "engines": {"node": ">=4"}, "gitHead": "d7072645364f112e27fcafa2be464901590fd639", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-match-property": "^0.1.1", "unicode-match-property-value": "^1.0.1", "regenerate-unicode-properties": "^2.0.0"}, "devDependencies": {"jsesc": "^2.1.0", "mocha": "^2.2.1", "lodash": "^4.13.1", "codecov": "^1.0.1", "istanbul": "^0.4.4", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-3.1.0.tgz_1466537396239_0.7026972598396242", "host": "packages-16-east.internal.npmjs.com"}}, "3.2.0": {"name": "regexpu-core", "version": "3.2.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@3.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "dbd1629cfbb9403360d9996c8926162b60f04b12", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-3.2.0.tgz", "integrity": "sha512-mbkDlzY0l1WW7kYspJWIZE5DcxnYATP+1luh6UinJv1hFEbwkdYpNo95rFYa2qwfC/RY8HqFYTvTaG1IBjN/uA==", "signatures": [{"sig": "MEQCIGOmooOUaXPdZ9NKTP10MmEbvtbPEO0xuZ+dQcEDgRq9AiBGsSsDn++jVGOYTO249OYm15Ooddko8vehN2zIIMbH5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "dbd1629cfbb9403360d9996c8926162b60f04b12", "engines": {"node": ">=4"}, "gitHead": "a9dbf91e72f773af4dcba6023877cd18991337f7", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-match-property": "^0.1.1", "unicode-match-property-value": "^1.0.1", "regenerate-unicode-properties": "^2.0.0"}, "devDependencies": {"jsesc": "^2.1.0", "mocha": "^2.2.1", "lodash": "^4.13.1", "codecov": "^1.0.1", "istanbul": "^0.4.4", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-3.2.0.tgz_1466676885932_0.9591239078436047", "host": "packages-16-east.internal.npmjs.com"}}, "3.3.0": {"name": "regexpu-core", "version": "3.3.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@3.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "edf000214e28b117b7f092c8400ccd13dc74f754", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-3.3.0.tgz", "integrity": "sha512-yEaSrwX2BARhdZxw4bWUWbYQWCCCgngXKV/0eWS7cqh3dY0pSP6h91ZvnpLA/WscUNwFT5y57O/C5AkPJVWYKw==", "signatures": [{"sig": "MEUCIQDRMOlrRZFyJti/6oIp4SBUxQqssPrhu4g0+hWz/ZN4VgIgF68qbGvzsiRMB/IKrqLdLqmznfC75gbbZpztEzGksC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "edf000214e28b117b7f092c8400ccd13dc74f754", "engines": {"node": ">=4"}, "gitHead": "af78d55c7e38584f508689599837cac26f9d16a7", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-match-property": "^0.1.1", "unicode-match-property-value": "^1.0.1", "regenerate-unicode-properties": "^2.0.0"}, "devDependencies": {"jsesc": "^2.1.0", "mocha": "^2.2.1", "lodash": "^4.13.1", "codecov": "^1.0.1", "istanbul": "^0.4.4", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-3.3.0.tgz_1471418562110_0.25034575862810016", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.0": {"name": "regexpu-core", "version": "4.0.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "d3fa6c3ad1a9e32b9e2d8f6132e9079ba9ef1c5e", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.0.tgz", "integrity": "sha512-EX/80tRgYoZ2B5J0pbvVzepEaOTs6EGD0XHX1HgYtbzCJV73JNbU8PaIhE3A+OxvedkeQppn7EJ3T7JiHmTiRw==", "signatures": [{"sig": "MEYCIQDgE0OXPt5z0TvTgCORsbFC+N2UTljmT0aDwMNBELP0bgIhAITKhB+wknKJrbJ/cDMjgVXmCdEZQmIR335Rx9mFuPp8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "d3fa6c3ad1a9e32b9e2d8f6132e9079ba9ef1c5e", "engines": {"node": ">=4"}, "gitHead": "fcc9d5e555288ea4f9c0ca5c05c5e741ffd77c0a", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-match-property": "^0.1.1", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^4.0.0"}, "devDependencies": {"jsesc": "^2.2.0", "mocha": "^3.0.2", "lodash": "^4.15.0", "codecov": "^1.0.1", "istanbul": "^0.4.4", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.0.tgz_1478088627418_0.6599641791544855", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "regexpu-core", "version": "4.0.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "6e7ce7dd475c61973cd4a65721e8c4b377f973ba", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.1.tgz", "integrity": "sha512-UlweNdYuwNMIsszgV1RNQ5rW2idLToTQv6PsI4lVINQAnHkNAzFilbQ/JMCzYptR1OjyiaoQTyo+myCEQ9PIEw==", "signatures": [{"sig": "MEYCIQDOMSRo9gqI400iHcjtxhTZjJ20/XurHcoKPZzFtu23mQIhALMQvo7+pYfh1DXP9j/6ePsjV0lnpoS33NpZwPL5BjXA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "6e7ce7dd475c61973cd4a65721e8c4b377f973ba", "engines": {"node": ">=4"}, "gitHead": "a92b960ab297403ded4f2f084532c6526941a1f7", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-match-property": "^0.1.2", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^4.0.1"}, "devDependencies": {"jsesc": "^2.2.0", "mocha": "^3.0.2", "lodash": "^4.15.0", "codecov": "^1.0.1", "istanbul": "^0.4.4", "unicode-tr51": "^7.0.1", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.1.tgz_1479493114322_0.9338014365639538", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "regexpu-core", "version": "4.0.2", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "7385bbb5ae57d3d3b422d071385f54e91947c9d8", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.2.tgz", "integrity": "sha512-sLMdWeTaZ5HPCFT3zuthRdvndIL7XIGdRU3ihZ4ST63An148wz8OStpl1rsMaqcu3iXJf2AesL9XtyTaFj3D3w==", "signatures": [{"sig": "MEYCIQDM3Rst6G2Daz9RrDP19mJ4ujPSQDp0Jgb7R4kTud5CLQIhAOOn/10QYDEyscQ8ZCQCCY+hQEpgv2yzBFeMVTaNLOje", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "7385bbb5ae57d3d3b422d071385f54e91947c9d8", "engines": {"node": ">=4"}, "gitHead": "88030bdfeac6aee5dfd64eda3f7dd1ce1ed0f3e0", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.1", "regjsparser": "^0.2.0", "unicode-match-property": "^0.1.2", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^4.0.2"}, "devDependencies": {"jsesc": "^2.2.0", "mocha": "^3.0.2", "lodash": "^4.15.0", "codecov": "^1.0.1", "istanbul": "^0.4.4", "unicode-tr51": "^7.0.2", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.2.tgz_1480403048362_0.7110750968568027", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.3": {"name": "regexpu-core", "version": "4.0.3", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "15b61928a28ab689b922a95555e3c1c652377159", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.3.tgz", "integrity": "sha512-AFrCo/2oop/sQMsTHnA+97Bc4OehK2DXK7VI57Otw/vs+vRIXCpY329/KrD1iudjllTqxNvika1bpLP2TJzyXw==", "signatures": [{"sig": "MEUCIQDGOretq/xmqaquKWcQ5frt6NLIA2G5IJtnK1EweV6JdAIgKLSROwQw6Xgd7edFv7TtUtFOIKAoF1+3Z8BJmBFdCKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "15b61928a28ab689b922a95555e3c1c652377159", "engines": {"node": ">=4"}, "gitHead": "22183da33d980ab302769ed784dc847306e5cc02", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.1.2", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^4.0.3"}, "devDependencies": {"jsesc": "^2.4.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^1.0.1", "istanbul": "^0.4.5", "unicode-tr51": "^8.0.1", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.3.tgz_1488999981906_0.29040053440257907", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.4": {"name": "regexpu-core", "version": "4.0.4", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "06cfa87a883545cc9fd6638d8024ba02b9ebfe69", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.4.tgz", "integrity": "sha512-b/w1NefIchQHs2g6ekXNbdJkB+HmnnxIQsI8/McNHso1NPbmfapHX9xvqBBIwoX7FlNOLw6Xl7IZfaFsckzMYA==", "signatures": [{"sig": "MEUCIQCZINqJMDZtoXEGLrNHQCOzRKPEHgZWeYfmUt7frR6JlAIgfM/64uu2QCqD1CUAa49ycCuDs+GJeu5s2ZICdo6mFwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "06cfa87a883545cc9fd6638d8024ba02b9ebfe69", "engines": {"node": ">=4"}, "gitHead": "e2d35878ac174d3e19d064c3e69b5d40ba3b5f7b", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.1.3", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^4.0.3"}, "devDependencies": {"jsesc": "^2.4.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^1.0.1", "istanbul": "^0.4.5", "unicode-tr51": "^8.0.1", "unicode-9.0.0": "^0.7.0", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.4.tgz_1489000892673_0.03097838768735528", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.5": {"name": "regexpu-core", "version": "4.0.5", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "b5c477457506659a405158be1cded1c8130b5237", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.5.tgz", "integrity": "sha512-b7adnNxLRTNYXniu1bTnrZqgwWaok4j9dMiCOJF7g5nuGeYxfxsnnqeVJVTE/QeR0dRyakS8M/DzxixxZvO4ow==", "signatures": [{"sig": "MEQCIAiBfa8/fokuCfaMV0Zpg4nYWBQzyeipszOR9zNLPlDoAiBpnFcjz4PB2UY7hvXYCdUnxksEXOmCu4tcNUWA3JJqlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "b5c477457506659a405158be1cded1c8130b5237", "engines": {"node": ">=4"}, "gitHead": "85a75b3d09ff673dc58c172a3ff9af0b1cb23db6", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.1.3", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^5.0.0"}, "devDependencies": {"jsesc": "^2.4.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.2", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.5.tgz_1491912202000_0.4386716184671968", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.6": {"name": "regexpu-core", "version": "4.0.6", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.6", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "369bbeb9ef12ce93695e07fed3a727170d323706", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.6.tgz", "integrity": "sha512-DctmLL6x1BT6XCQ0HNzdpV7lPEnLYU/mj56sJHIkjz7GQ5hD3pplUklsZzWtEwrxW3iWDQcs1OBzQC+XeH9nyg==", "signatures": [{"sig": "MEYCIQCHi4E7IZt9L/4HKLdqF25J685hscazDQQLukus2yad9QIhAInC7cpkb3dSFYXSr/YuOSZY2fNaM/WikU1MmFcaoA68", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "369bbeb9ef12ce93695e07fed3a727170d323706", "engines": {"node": ">=4"}, "gitHead": "d89a66b1ce50c4b26ecf287df751c6d5289dbcbc", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.1.3", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^5.0.2"}, "devDependencies": {"jsesc": "^2.5.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.3", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.6.tgz_1492077924673_0.33175376569852233", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.7": {"name": "regexpu-core", "version": "4.0.7", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.7", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "9d9eeeefeeb81c244bca8036b35556666839fe4b", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.7.tgz", "integrity": "sha512-IRwzM/FtvgKSfEhDl/Evh0/7HMzGd3OlIL0lkuoi2SFZR8l0jBt0tykjzpUSwWTWomfRyD1ARzYWIERucm/m4g==", "signatures": [{"sig": "MEQCIDKVHPqtWQyXlafHEw3Q6uDeJdzGTtVI16T47zZRe0XdAiBcHn3O5IdbDpvZm82J90f0TeYM7KMwDdEEUJbM/IkNzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "9d9eeeefeeb81c244bca8036b35556666839fe4b", "engines": {"node": ">=4"}, "gitHead": "ec27332942973fb96aa99a0180007f60aeb2de37", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.1.3", "unicode-match-property-value": "^1.0.2", "regenerate-unicode-properties": "^5.0.3"}, "devDependencies": {"jsesc": "^2.5.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.7.tgz_1492163818052_0.11806377512402833", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.8": {"name": "regexpu-core", "version": "4.0.8", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.8", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "00d709708a3ac2c54bc607cf36dc1afa792c1f2e", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.8.tgz", "integrity": "sha512-ru20Y0AD8zwG+9fp3iySI8yGLhaQX91nnZPvx6wXA1Wgx6x6rcp8gojL9BRLajogzNqQn0UaohZgaZ0Vs0/EpA==", "signatures": [{"sig": "MEQCIEYQDfLSKt6whxQbvyCK1RoFCAHTmQZFYuilQpz/VAOuAiBOawrZPYUma9KL1fEi8z3kxGv7W5x55GnCjhBLewMRbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "00d709708a3ac2c54bc607cf36dc1afa792c1f2e", "engines": {"node": ">=4"}, "gitHead": "784f959881985755d950b79f55ecd0326fc0b924", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.2.2", "unicode-match-property-value": "^2.0.1", "regenerate-unicode-properties": "^5.0.4"}, "devDependencies": {"jsesc": "^2.5.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.8.tgz_1492253394612_0.8791960158850998", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.9": {"name": "regexpu-core", "version": "4.0.9", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.9", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "13b66a528f4fcd00417a096392e71cba1eac3782", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.9.tgz", "integrity": "sha512-dWJQnLlb17ln872MJvdbvwNlp70ByuPd0k14x60f0f3ozcgfSJYqzBkDZUBeRwq+XP7uQPd1eWDGEo1PxAdO1A==", "signatures": [{"sig": "MEUCIBeITqpjSP5ZrsySc5GVwiTgqR3O2grR5oyKyfVqv9tqAiEAiCo9FgViJQQdy+tpeamQAndYsFL4MSgI50ypqS7WAFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "13b66a528f4fcd00417a096392e71cba1eac3782", "engines": {"node": ">=4"}, "gitHead": "b9eb1bd21595769e7d38ad2276152ac5f8acd15b", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "unicode-match-property": "^0.2.2", "unicode-match-property-value": "^2.0.1", "regenerate-unicode-properties": "^5.0.5"}, "devDependencies": {"jsesc": "^2.5.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.9.tgz_1492254251446_0.6973175636958331", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.10": {"name": "regexpu-core", "version": "4.0.10", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.10", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "6962f15ddf94f314c9b1151838dc07cca9c28ac0", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.10.tgz", "integrity": "sha512-3rsnVyyOBekhxVBoo7XNEU9eENG3wGrMjDwj4Iap5mKQytvhrutqDI88lHHNbZ+mrdowBzmvO+guBixLaMRhPA==", "signatures": [{"sig": "MEQCIHUdTlpHwdu5lizQSRtaSgKdUdyQvoBLrnUbbofLH6EpAiBgOm/HSnVPxy1Q2tnK/tGsAIVcYeXbsKilA/9EzQvn4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "6962f15ddf94f314c9b1151838dc07cca9c28ac0", "engines": {"node": ">=4"}, "gitHead": "4d6646ca5a67674470b4eaf4afefd83e78ca0a7b", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "regenerate-unicode-properties": "^5.0.6", "unicode-match-property-ecmascript": "^1.0.0", "unicode-match-property-value-ecmascript": "^1.0.0"}, "devDependencies": {"jsesc": "^2.5.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.10.tgz_1492261643572_0.9921895058359951", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.11": {"name": "regexpu-core", "version": "4.0.11", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.0.11", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "798f9e9986f78c793dde403f1b3f34c007bf73a9", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.0.11.tgz", "integrity": "sha512-Pj5/A1+0b4wbmDQ8IuP0yPCFWyFS5TPKATC3r0Fkx3qjvPsWtOyg114JDMMWOcdI6Qhpg2ZLQM4/IKIzgMQ9iQ==", "signatures": [{"sig": "MEUCIHhpQuo6yG52Xbxr53tzKJSxTZRjK9d9tXCgPdilCTGAAiEA4AwbEvB/vXdZfqwKAsyclXLamhafeDbj+0lQ7jH6dKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "_from": ".", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "_shasum": "798f9e9986f78c793dde403f1b3f34c007bf73a9", "engines": {"node": ">=4"}, "gitHead": "7b694ca4f7c985e4cab461d99c6d60ec48445e76", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "regenerate-unicode-properties": "^5.0.6", "unicode-match-property-ecmascript": "^1.0.1", "unicode-match-property-value-ecmascript": "^1.0.0"}, "devDependencies": {"jsesc": "^2.5.0", "mocha": "^3.2.0", "lodash": "^4.17.4", "codecov": "^2.1.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.0.11.tgz_1492261908100_0.05818845774047077", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0": {"name": "regexpu-core", "version": "4.1.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "98427c75d206542e360389fd049379fe1252c1ce", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.1.0.tgz", "integrity": "sha512-dDHZmOOp4ECxzWJMaLrZZHLLxmVMOEiApILlWnm/fIxDOvwU0k/aIMulaDKLhihKUViES/xvNnYUveo70fr2RA==", "signatures": [{"sig": "MEUCIQDMpL5idtw/G0lOu/N6QPkiyOm+Pe41nUmMvQorNZmaGwIgePcTr0KDxmvWp1KJhmmNZ1V1jdDvQ072sVC8xY7jJjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "e7f3fa8a7f3bb04164dbd9030a9bb4e3ae075c50", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "regenerate-unicode-properties": "^5.1.0", "unicode-match-property-ecmascript": "^1.0.2", "unicode-match-property-value-ecmascript": "^1.0.1"}, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^3.4.2", "lodash": "^4.17.4", "codecov": "^2.2.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.2", "unicode-10.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.1.0.tgz_1497978595157_0.40178445843048394", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "regexpu-core", "version": "4.1.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "5877950259cc9f923d8393c9e39d26c3af47a9ea", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.1.1.tgz", "integrity": "sha512-7GxoLPMhj0ng8DOtWHeTFTXna911T05C3Mm5z7W7ODgX6vIAMfCgnWpmMplzraOtv9Fbu5J4gHhuMCKE+Xzsqg==", "signatures": [{"sig": "MEUCIQDSlWfK/X9h4KUK2IJP+LwWRXh/kqSAoQwJJiLYqg36RwIgFk7JtkO4ghs3hARVZkOKRdKCdU0M2CsYzGAziU8defs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "e18f8b2f737366f529be3ccbdc253247c74db82d", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "regenerate-unicode-properties": "^5.1.0", "unicode-match-property-ecmascript": "^1.0.2", "unicode-match-property-value-ecmascript": "^1.0.1"}, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^3.4.2", "lodash": "^4.17.4", "codecov": "^2.2.0", "istanbul": "^0.4.5", "unicode-tr51": "^8.1.2", "unicode-10.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.1.1.tgz_1497979513782_0.14703182457014918", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "regexpu-core", "version": "4.1.2", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.1.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "1710857c567828de28a43100df4237670a0a0dc4", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.1.2.tgz", "integrity": "sha512-qNMZCn1PVlV/T+xBwkHywF7MnOQyUT9EaX4MgAtxOti2hpVZ/8RG+XrVSilTR/5SLH5f3CwB0TtLaGO2M+gUlA==", "signatures": [{"sig": "MEUCIQCVYDsZoaUkEObvNuVIyA7utMT5rHALDviQRyYWhHyfvAIgT0DJJA9XXVTDfD5wAJjCDVxzpwRAaFFXYSZlfCJx9Tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "67b2195ce317fe65b11d32d8fa7a55838b257a0a", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.2", "regjsparser": "^0.2.1", "regenerate-unicode-properties": "^5.1.1", "unicode-match-property-ecmascript": "^1.0.3", "unicode-match-property-value-ecmascript": "^1.0.1"}, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^3.5.0", "lodash": "^4.17.4", "codecov": "^2.3.0", "istanbul": "^0.4.5", "unicode-tr51": "^9.0.0", "unicode-10.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.1.2.tgz_1502965973711_0.153056574286893", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "regexpu-core", "version": "4.1.3", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.1.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "fb81616dbbc2a917a7419b33f8379144f51eb8d0", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.1.3.tgz", "integrity": "sha512-mB+njEzO7oezA57IbQxxd6fVPOeWKDmnGvJ485CwmfNchjHe5jWwqKepapmzUEj41yxIAqOg+C4LbXuJlkiO8A==", "signatures": [{"sig": "MEUCIQDF0CSdAbDSiEMWfVOzHusCJ2VahbYW7WTmNR+2sGn02wIgCCyHuBrJgxTxDCgCLsao9vSA5h2tLkASmnE6OdaGp7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "ca9c3fc44638e4d7ba06bc282523add58b83f698", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"regjsgen": "^0.3.0", "regenerate": "^1.3.3", "regjsparser": "^0.2.1", "regenerate-unicode-properties": "^5.1.1", "unicode-match-property-ecmascript": "^1.0.3", "unicode-match-property-value-ecmascript": "^1.0.1"}, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^3.5.3", "lodash": "^4.17.4", "codecov": "^2.3.0", "istanbul": "^0.4.5", "unicode-tr51": "^9.0.0", "unicode-10.0.0": "^0.7.4", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core-4.1.3.tgz_1505745432625_0.07626100396737456", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "regexpu-core", "version": "4.1.4", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.1.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "f6c847261ba4cf869a7874442f9d25251b713fc8", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.1.4.tgz", "fileCount": 6, "integrity": "sha512-EDIJYxIE02+p38c5pD/AaHXrEpOjagp/a53/R/nB1cZxjbMAMxH1ywHuKJ/DDHAvYHS40GrIgeGPh67XrTI67w==", "signatures": [{"sig": "MEUCIDggfBOF6zoKKrwuhEbs5mOALoZSXry7+JVAsxVSjxALAiEAhidX0Tcs3P4ZrQafuGPF6seWRRyKkDbqOfbMek5wc74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa93bACRA9TVsSAnZWagAA5hIP/AmBjCMToJ+5YwyGSg8Z\n94SKcNJtE0fPSy9QlZVQogC+4xgIbCX1Sg5AsKcpSjh0Z/H+KLzN7byUdDTD\n0N/1/6Hl1Ni22EeCMbQBfE4QG41hlFPz0YS38EjwJA8+HzonH9oeMizTMBWl\n3d6NXINBa4reofXbLH/oEBlV0IVsAY50xrUuhLfAxOQ2j6IS1xCA6JKRyKUb\nADkBgbDD2202a8VYckXfEstVqvdAM0KiYfRCJCVrX9Kkrjv6wpPR/1jgYRtY\nsjqT6zM6lAIP30VlfaxOcQPOdAMBPvsHgykfyZyezoIRmjV4jK6Q+3BAjzB7\njdMsjOV2gDf8kucqbpQHTC1TjHLnabpb83MrcP8RLEavHKxFdj3eFfxu2euO\nE0uQA22ZP5Qvz9ASEqc5A/L0d1drbeP5fK3fVTRspgNaQx2crAhR71qeUUZK\nVp5uO+l43Zvokkvw4pbA9rpxEBkCdf8l8/5Oz4UYIwCQ9fyz2fwjc9eAzKGN\nvlrq9rYrHNPDx4jjdrrASyYXDIWzjY8HlcOOKdOz/aZ+DvDy47zsRFlQXZ9J\n89m52iJtisW9RHN01nFGxgCte0JonCJeHdYLnv8bn8f5g8j1onX0aVp7Y2mN\nYiaa5gaFmvejnqJ40mGIrYMwaYcfgzQVlq1w2XvHPv8MIMfVYAsoQyp9spAC\nbJ8o\r\n=a2QW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "930e97a7fcba7281624d5940fdffb11e65791f7f", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"regjsgen": "^0.4.0", "regenerate": "^1.4.0", "regjsparser": "^0.3.0", "regenerate-unicode-properties": "^6.0.0", "unicode-match-property-ecmascript": "^1.0.3", "unicode-match-property-value-ecmascript": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^5.1.1", "lodash": "^4.17.10", "codecov": "^3.0.2", "istanbul": "^0.4.5", "unicode-11.0.0": "^0.7.6", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.1.4_1526167231318_0.5262597483458595", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "regexpu-core", "version": "4.1.5", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.1.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "57fdfe1148f8a7a069086228515130cf1820ddd0", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.1.5.tgz", "fileCount": 6, "integrity": "sha512-3xo5pFze1F8oR4F9x3aFbdtdxAxQ9WBX6gXfLgeBt7KpDI0+oDF7WVntnhsPKqobU/GAYc2pmx+y3z0JI1+z3w==", "signatures": [{"sig": "MEUCIQDqJzC7TJE2pNixMRaWJi1ELmdA21WXxgm0bjxvF3vleQIgNo/bqstKsckRpy4A+rfZbZaCm8oLJq0UpVhJ0ZgpC8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+fROCRA9TVsSAnZWagAA6ccP/Att+i4fFDVJ2p9PdQhl\nnTLMOrWwj+WfuZiEf8fr8qO3+I0s/a8lEIJi6FH3MCgxcJA/ewzJAmGSouWE\n9hiEbe2D6ZKqU04u2RTjzD7Cx0EcBABXydmlUJVYlDgBW40sUu5IF85bJZ4V\nCb42w6CeTVqdpdJL/ubVhW7/bomLYrYTLDRe6Tcui3W5Kays4DAW/gGziBnQ\nL8jboV84cE7quqrjvn3rgJsJcRTRWwkffvs7m+By9NGzu02LOB4Thudvof+q\ni1QgaTM4AxjUfDceL34c8a6Tbx14Cs8IH3juC2VJGgHneH1ZZ4fxcJKzIftb\nQra4nvlDfbmNTmFO+sbKPRbz4vHkYHQZWZr2bK5ENT0YAHlDANjkggdPq4A5\npfvuz6SZgNnWwMBMif2l6kV0MkelA82oitjvKy8oWP7ntc32wY/x9fVJnJWT\noJT3edW9ncNZuXYvQilHOEfVus1eqOdEGqDuahR9jOVEwd1QiDxMpmQfWHGw\nqZ7WLgrF7t7ivz7M5n4f+RkzAR3lb9Jm5RhTi8/j6uyjNZytpno59UiBEWYz\n/S6EXKsZEguHu+tzjbcNSoMUAMzvc5r91C2j911d693j3sz1fZQT7HnuEmtO\n4vzAB0QnaxQ9LcsZLhNXT+SqDvDO+zEsaaAAygZqDOz2Z4s4SHtJYGO5ONoe\nWJMN\r\n=motg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "5d9a34e7854a27b0feecf7282a25726cdf12930e", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"regjsgen": "^0.4.0", "regenerate": "^1.4.0", "regjsparser": "^0.3.0", "regenerate-unicode-properties": "^6.0.0", "unicode-match-property-ecmascript": "^1.0.3", "unicode-match-property-value-ecmascript": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^5.1.1", "lodash": "^4.17.10", "codecov": "^3.0.2", "istanbul": "^0.4.5", "unicode-11.0.0": "^0.7.6", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.1.5_1526330443918_0.05320040248530544", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "regexpu-core", "version": "4.2.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "a3744fa03806cffe146dea4421a3e73bdcc47b1d", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.2.0.tgz", "fileCount": 6, "integrity": "sha512-Z835VSnJJ46CNBttalHD/dB+Sj2ezmY6Xp38npwU87peK6mqOzOpV8eYktdkLTEkzzD+JsTcxd84ozd8I14+rw==", "signatures": [{"sig": "MEUCIFVN7p1QBffWcmiJMUoznzXb1NHtmIIPkE6u5+Iy7LbHAiEA7g8TFy5wOjjTy2Nxr8PTSWKxTg48065cEBsRv0f3QQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGTD/CRA9TVsSAnZWagAAJ88P/1z2fTDw6AppStNwKg80\nyI8rxwzRSfWMVq4M+9XqO5wter2/Y04yyfAxaGiq2+21sFRyvUnqPkb7kFuj\nw26ls7pIy97datL3TD5sJySk07SFWBhjUvMkGe6q3glm9raQKHFDA9IvPubv\nFhZxjIU8ozrC3H6rtrxPMuK4h8COn98hAkfKXI75KdmlNKb/vCeBbL72hOJD\nAirxKfHNmUfLyvR6PLaTGVsfez2mRXKjZqu/gXrcpLrDxL98XDhVmkcnkybp\ncaRefTQWz94JiW+VW8NYjCKbr2T05XJTTlHsUVVprtjxTm/DK4OjZltjcVSV\n6EwD9grIcOPeKWUUA80JBVoHzKujpTWJdF4C0abR2SJ/5O5zkeddal7Kxu5A\nzonmGZaITQa1VrJVCr4SUCgcoh15ExwBz0ExsyCbgOiK3vtl5Gp8M2pVIXCA\nlnbGER7wO7IRO9MBmHjpzr18CExZsm8R0Pb5y0WJcCQknB7TOuK1k2E9WXRZ\nrgnWZUuCdnhu3i0Sk3DOb1sNcqcf2HJkJhGd6fLgjNqsP5FE/IrGLO2Y5XtV\neZdDTh+9ZBGisqEj+1O0PsgRmg+J7cvzoF5Oa1Ws+Axs80NBUzq0GRVp3IQP\nBJ/sMeezSSA5+nJLHNvxNEPNolFlru9S/htmgawIv9qS40s35IgpZ9Ut/UHS\nEh4Q\r\n=w+bz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "engines": {"node": ">=4"}, "gitHead": "a8ad42e7162308dcdbd9e28b6ba4e0161678d18b", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"regjsgen": "^0.4.0", "regenerate": "^1.4.0", "regjsparser": "^0.3.0", "regenerate-unicode-properties": "^7.0.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^5.2.0", "lodash": "^4.17.10", "codecov": "^3.0.2", "istanbul": "^0.4.5", "unicode-11.0.0": "^0.7.7", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.2.0_1528377597719_0.10544675459727637", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "regexpu-core", "version": "4.3.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "256cd550eadea9c9f37933c8a60ca966c8c1bc95", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.3.0.tgz", "fileCount": 6, "integrity": "sha512-pniBuFHJMDy9P8jnXGBvfLQsQqzGsZuhT0KezuTWn1vMdJxuhsHauNdIvzMsiORmYPgLbPGwWbq3czCKa5RsWA==", "signatures": [{"sig": "MEQCIFsg3D2DPuoBpjQeTuahWolHBfL1fE0rvtbHXYIFpj/JAiBUeW7E/MD++IAbid2c7Y5TGXtPyMO6tqdCHPdh/V+Qhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcB522CRA9TVsSAnZWagAAcggP/3+yNh7ttPyzzVdw8a4v\nBsVUhXgXGFxhv8lE9cwwJ4Ln9FnLOM9sOpYwIbuJrlI3RJ+2ZYy6V3MvYjzu\npgHU6yWiCzUfyz1EtD/33GSAK2+nqwIUUb+LLV9kds+25EuNKOK/QV1M4p8u\nTmsB9ddZbMrpLZhr9sxRwYuYyvwa7uVrBQOyQwg+W+6zsMiRCc+XKQS/0aO4\ncLIVa8X55yMIgzZqGEjzt6rRoHcTJ9rZtDACtCZfhgRipb3gt7/XjORMfc7N\nsxoDazWiap9zyNC59YMdJ6Cy9QdhaHrR9TqZALrL1SCPnx6r5OTec/U3fbDW\nPhyEVPB4QU4LsjhgrFTwRGm00zrKe+OKfSsUemZ5cusw35AqZpRSB57/naI2\nMjRQ0ahZz+u2hV17itEZllCHmVs4i7C7mW+7uu6Xo59EGci97Yy5ePSV/I2+\nepECzSszP69iz558UOOTWxktkdJXqhHSJJo35W61Iyp+F3yvAHlQG08dX9yH\n2E2TBi5w9alPgc940he2IUsAOsSdSTgcNtsFFfqBNV6wHoQUsrC2ARb025+d\ngxgogvxmMfTD6gi2XWv3Abeg9GM8YPjpICdKQlCO3VoB/5nLauj8FAJkZ4eN\nzwD+7AB3RxwQbKkESzIjVQ6Ix3U3npKV/dkyZuJsEbCVkRZLL2oD69WAQau4\nGWMY\r\n=Y/p5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "3026682b8f2f97f3e0acb5db55b80d3173f8c251", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^7.0.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^5.2.0", "lodash": "^4.17.10", "codecov": "^3.0.2", "istanbul": "^0.4.5", "unicode-11.0.0": "^0.7.7", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.3.0_1544002998178_0.6804099314582548", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "regexpu-core", "version": "4.4.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.4.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "8d43e0d1266883969720345e70c275ee0aec0d32", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.4.0.tgz", "fileCount": 6, "integrity": "sha512-eDDWElbwwI3K0Lo6CqbQbA6FwgtCz4kYTarrri1okfkRLZAqstU+B3voZBCjg8Fl6iq0gXrJG6MvRgLthfvgOA==", "signatures": [{"sig": "MEUCIQC4H58W/LDIh8hU+iXnE005FLVKQXDhOvMcSqwUwTBZHwIgYlqcWWZbeM4jy1Cxm7264pK4BqwGAG1pg4bFW8RG9xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcB9E5CRA9TVsSAnZWagAAy0wQAIN441jfJec7ncbwKEw4\nfVMjuDr7YxnTNFqBZAuhymaMikBWGX6Sdr2g0kHvBQpbFMwKYQkk+g/5bnaD\n6FTcNZ85/6879lINuG0E/OfORrfe5DpWyzt1AdlbnvCTDdWEeLcM5DNo/274\nJ3Y2944NMeMeRdid1qOFDODNPhy+V6IFhSip07kGiZBKniLGz9ZeglBajKN8\nXrko+PiF0QnzaCgz6ut6JtwCyQrQPINfoZdT5C17nviNvx5d8kcC1RnXSncE\ng53kZrkyj9wOSXb7ZROzBpQuDYIul2Ss1alpL5F/JznN3/bFPaSQa+FHnWmX\nCzsetjs1iH+IBbh+BRHcv5wy98+mnPqMv8szG1Z5A2giR05dXFTYBEyKdjBL\n6GhTrO/GxudSovKjZ3TAvLTYOPGw5LylEqAuC3Bq3U65MjTRGjihAbo2llF4\nibPmiovhtBL/0bEMnxLpA60/szHa/00mowbZ8EwM4lKcChOGGdnl/BFoNqez\nhLErkKoUMqA+489e03e5tD9+A8pCAdoxN82vbZ99KIBSLFHchh13TGyAJwaN\nO5QZqaFG9MhfB9rpkaks4pR/G53SCua1qCvIuL6PTSPeK+MpDI8NqOBrGp/4\nQqGS6wMRRcJhxUj3DJ8cm2iNJB7DkL6GSxI4z/cE7/xWE97+c9uDOIuJdcbe\ncH8a\r\n=suSd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "2141f442c152b5316882a221d2e7fca22f1fffb9", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^7.0.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.1", "mocha": "^5.2.0", "lodash": "^4.17.10", "codecov": "^3.0.2", "istanbul": "^0.4.5", "unicode-11.0.0": "^0.7.7", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.4.0_1544016184128_0.7869368676067257", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "regexpu-core", "version": "4.5.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.5.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "5aad38279f3b6d6b314a4ba75c71c90eed63ca8d", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.0.tgz", "fileCount": 6, "integrity": "sha512-JpJJbvCkFtlGj7kV5sIvbAGVDglA81nFIknBiN9VIccsr6hMWiT/bVsTSU/hYFC7h1mnrb0Jbhb2eS0GrOKbEA==", "signatures": [{"sig": "MEQCIHSFUZExrd6KlhhTE2UQ/TzwwgB9b5JCCvW160mfq6VrAiAKRUgxUS048Wncu6tF+qVDW7TAHUiYpyDJYpDmbtG5uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfm6zCRA9TVsSAnZWagAA45oP/0TI7bcE5ZutmGllDa2T\nGGaM0EarPQuOBmUwwLR8Amu3dLZtcZRAP30kPUE1falqjlONSwgFdEBM72XG\niA/KJneXKA87k+9FEuMVhZV3hz1nb+qnoPKYFmFkZj912emw7aBVRp636e3c\nItQz0jinC9BmKR/I8F5WPilgNwoweA9QpBXDiR3Pwiyic1aeWVVvybDXsVzK\nn5et5jyYpGnDRxepZDFP0E93UynAWH2FVqLWGPWCOQajsqxHYOVMdKBnzHth\nUDbpMyH5TXdqtqaplBmeRbv7fvEnx90a1tyfUZVliERjeWLDrDrFQww+P8pz\nDK6N7UD+vy7Y/FsmypPCo943Gk6Mp53QRyVWV1kALtXB5+mgNUC4bZWXsjQa\nx9Vlrq9hiYtZAfEU5gYbdl6/uHCCzptdmaotYCsZQ81GtgfdTAvG97aQap95\n5ob4eRs7DD7MMOockUHwIqgOwoeto12+uHzdREXWS/Y/8Sj1+xIhqmqfEJ4K\n4pWT0RhDMhfy9l003jWn9UkUYDpwMRRzU33ZULCKYUnQGxuFcVp8VMC3qdKJ\nDGZ8EsUkdYgxWl+RGGp4vrbHGBe6CO1CvlrF967sisHQcIgbSpnkqfMbH8yt\nBAxKMELJuqfiL5RvoLMQw8NsEtdoZVnlWr5LaI8gaQWJVZsGnJ++U6l3pE4n\nF8/Y\r\n=RHHD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "aa9a9ada8a50db28c265f09fd538a0d2b4ea5220", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.0.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.0.2", "lodash": "^4.17.11", "codecov": "^3.2.0", "istanbul": "^0.4.5", "unicode-12.0.0": "^0.7.9", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.5.0_1551789747108_0.34752929510973063", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "regexpu-core", "version": "4.5.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.5.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "4d0d902156995d19c5cd832f840632799122f872", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.1.tgz", "fileCount": 6, "integrity": "sha512-+z6gQS3hNgmyYecCpEbW1srT+RPKmWxNK70KdVgLgVxkgMla7i0agNiuBTm9X6vko+OQBhwTVMlFUvIrkDq7qQ==", "signatures": [{"sig": "MEQCIHNLuFZhF7GK/K7lse1ZvvfOrIQ+nnaZqY5FgHMuDq+EAiBcvKmlmv9u3qR9t9V4qOIPP0cCrxfhnXWxjkdDL/PlmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfnHLCRA9TVsSAnZWagAAeYkP/0ETesymYgwAljHAIxkZ\nkksVH9wtqY0wrPhKroBfUHrhVToV+pHxVP+61GfNJrnD+G7PP7xRzJuhGnce\nT2sefifzaEu1WJk5aIzoOdtu5OT+lWg4YmilrPu1p1tTNStv+uAH6u1w121l\nBx6N74/yo38fe5+Eq3vviFarN4bu115c8Ialvhp+YuUQosza/rhsn2pIehLJ\nKcySfmAIrznaZuCxW6wibExM7EKJ84N//vPmazp2LJ7f+Z7mEgE0is7jr1LF\nzpsrhG9oW2l8vr3T8yDqVpJtXaKSwaJPwW2joUJR5Or5/oKM8GD08MyA7MTk\nFbQlCW8haEs/KYNOGnwpCxglFEeMaG4PZ+LunSeYKX3AS0ryF8wGoF3+OOEu\nhe22X/HvE1nuQW/V7O0XNn5l1c4CLzTn3twZ7HLj/VKgmkFD3ViVyKIaxVk9\nAkXJWtKKOgBYYjouLSOFyC41mZL9WEI4muoTM/6aWIVy8LJBlrDwRZCr9+4Z\nXI5/MDGBaf7dbb7nkxOnEfPnGCBRX6Jns0Dx77BZETf9GnmedI7oD3Kg9HbE\nco+YPy6J2DVH67Y2xpanCQLW8kkW2ram1yqb2lRzNBh5hnqs8LORcPdVFqnT\nq8Z9Qj5BjjoAy46PpyoN81Dg2BlYU1nPGvAjW3PF9OdE0VDgpOyZ9WgNzbZN\nzq1r\r\n=ebxH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "89d2e9416f8114e6253488fc4226b4ed81a37f7b", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.0.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.0.2", "lodash": "^4.17.11", "codecov": "^3.2.0", "istanbul": "^0.4.5", "unicode-12.0.0": "^0.7.9", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.5.1_1551790538644_0.9876242108140836", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "regexpu-core", "version": "4.5.2", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.5.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "9fcee72782f4fea6f8f428f7f14c8f242ca3dc82", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.2.tgz", "fileCount": 6, "integrity": "sha512-CgGxXmuX0Cf57z7KahSHe4kaNY8gBRCFFEretQ5AHsnlLx/5VdCrQOoOz1POxLdZjPbwE5ncTspPJwp2WHPcHA==", "signatures": [{"sig": "MEQCIAsPbJhWnH5xs9Q1zpz0V7iY8KxGPZnxM1FMQ0VU6o+5AiAwYakA8YpbwMjfBqbYpg7Co3FwWAJjniPueZh7FITZyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfnztCRA9TVsSAnZWagAA03sP/3gRB9TKCZxOQRP4ekkD\nMEmcKamJ9+qDItl+qaywEbbpJH4qVwbBnm0uPgadsPqaQbgaZh4Nd97geEQ4\nXI5ma1r+iKCNgim7tldL2EvsQemquYxg79qNZMApPqfWzZ556FhOSkuZVE3f\nqPx7OYxJMHQqfE16rPxuUtoz/IYQjtC8AV8KkCmNq0jrTHA4pyk29Zza/5XY\nVPdG09p0uJyeI8FrwQiZxgbmeqCj8lHRmwmSEsJ73UAFT/imOy9FJXG6IsjI\nXquk78ssf8YPoztW5Yz3jokE2gUDfSHtq4E1MkvObDI9l+6ucoVUCoDYJk8e\n2wAmrAkn1iaazgC9LbNFqERtpGfmyRZHJ5eJupuFI5GTNselPwEafQhabtfN\no1Hl+Lrc8DDPHIVJvVqM7Z1BNv/cBWW02fQk0rUIWLRtbu9zf5ge5kIw9KmN\njTacsKs7wZkrnukAvRjtsjpKCWaK5VcrkYSHVf1hr1IHIWUQr9CCAGZcMoWj\nUst0Z7qZa6QLeciWky+0MRLkEygl6hj4ZF28Z7ifm1rmDslkzcOfaZoXdxL/\nUNwX5GAFEONaRhXX3dpbSkXjiTm5Ff8V2gY0CidP4dBFjQ477feyl2XvQNzi\nSA5i1Y5c0xFZ4DAcIt9ht9k6tzFBuBt/KdiqDEGHCclAZENKU5z+EyeSTnRZ\nHXaN\r\n=GJIi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "cea475528b64428187e559f9ad2978ebc0e2c6c0", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.0.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.0.2", "lodash": "^4.17.11", "codecov": "^3.2.0", "istanbul": "^0.4.5", "unicode-12.0.0": "^0.7.9", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.5.2_1551793388485_0.24194856977826795", "host": "s3://npm-registry-packages"}}, "4.5.3": {"name": "regexpu-core", "version": "4.5.3", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.5.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "72f572e03bb8b9f4f4d895a0ccc57e707f4af2e4", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.3.tgz", "fileCount": 6, "integrity": "sha512-LON8666bTAlViVEPXMv65ZqiaR3rMNLz36PIaQ7D+er5snu93k0peR7FSvO0QteYbZ3GOkvfHKbGr/B1xDu9FA==", "signatures": [{"sig": "MEYCIQCSFkdNlvOBRwOh4vVSNoPnoej2rpRjHPs6lh56TPqz0wIhAPuUJU7ui9fH38EADgVIMNd4FIdM/LMbREDAzGsW7xR4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcf6BNCRA9TVsSAnZWagAA+yMQAKBN26J1M4f9G171Rptd\n87b2fhppWlW83wLoIGLdpc19l1SsH0CW8IBeh4UFd2nwrcttvf0nUvbc71Bf\nqEBF7LysRhpiMS0g6d0ccthbhitHeH/8EkkBjvShSvL//GfVHIuapqCfq3uD\n8aiitk78oe+KNo/QVzEPk+ilLfTbLZYYetcVhG8wy5I22Jz3X3hogXXVQfCV\nxm285lCw9hx3V8P5T1L4qbHevOMwDpxuMTVzrHG5wVksgNhJMvmJP4LamR6S\nDNQgVhpk6UPh4Hx8WxVu68OBowGATPw7uvF7DuXqFidEiMXB9fuOVtq+MN01\ndem7J5S29ksUrDSqN2bB+SpQv1Ow1gpBkOLsADMPS5x9XG/ckK5qUfxoJw4K\nNxIIT8vwcKsY0r5LH9quG9pxl34U8K5nahpaL/JzJa9O+sJjkxuS9/Frsh2Z\nFFJAMc5gtaLqFyQwytyuMCU+6oxR1EfAvb6fI0ZievIu/O5n3Ck5A5sFLO4l\nWB3mlDjbxmHDJUWbO6zCfFQd9tLNQt3ROWQ6JVOueZ1wUIP5CfA6rJFQeLis\n6cJ0m313uaZ5Al3Z8OOzPJngVQk70zJiIUZvV9ANl6Kl2Hl2DxWuib8SYK5H\n7oTv17WE1CxfsuhH9ByrPKhxMqhc+7gScex+495uc4YhGzlgvZYQGfZVLbg9\n6k05\r\n=lKjE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "677f7d843d0ab182461a38d59e835958e40de87f", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.0.1", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.0.2", "lodash": "^4.17.11", "codecov": "^3.2.0", "istanbul": "^0.4.5", "unicode-12.0.0": "^0.7.9", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.5.3_1551867981315_0.8580200641566542", "host": "s3://npm-registry-packages"}}, "4.5.4": {"name": "regexpu-core", "version": "4.5.4", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.5.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "080d9d02289aa87fe1667a4f5136bc98a6aebaae", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.4.tgz", "fileCount": 6, "integrity": "sha512-BtizvGtFQKGPUcTy56o3nk1bGRp4SZOTYrDtGNlqCQufptV5IkkLN6Emw+yunAJjzf+C9FQFtvq7IoA3+oMYHQ==", "signatures": [{"sig": "MEUCIF/iJrMHS+PhZtBehGamgcaMr2f3EOF5K4GYICdfqRdKAiEAgwZCCl5gzkbWDzUEc5jB9Gyk+94iFGvmeqkIiB3VwGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchp6nCRA9TVsSAnZWagAAfZYP+wSI0f+WXyITBRpzD0dn\nugR21Gh3brqBSY6UmF2S1QthoVe/jsLDSGGE42BlxywIbXhIjvpW5qnVNIEa\nWgcpIAK/LLQhMpI1cxzuDVverOXd8Y65OFqbCEinCU89CB/WPiyktQena0wt\nFlFCggCz2SAF9toZ5JAE9xzbD04Fk9tw+vuyKqul5UQZKjk2C1PDFEq07/Bu\nZSMSstD2S/HOK5uq2jSKVaZoGNHAXTjHE4xRtCG/pLb1LOVpiSJ87olE0RaM\nn+4AaWcKb6kFqjC9BG5JIAboiegaFxMmnp7qGpDi0+tYhBESaLsWZ+9qr8BQ\nFrgfuook+K2Q0l2Ao+Iuq74XE6UkeoXC5VnIN2TNpKS2VDnGo+f4PDy6uw4s\nJwZx5pUASpn6Hu7c9TdxDm45k1JH3LCLQxqTr4Ln7la9h72bJRIgdMqYXTHS\neB6PNrH7c4frQNImJdnILClMDhNqaPTt++yNyCyiQPKxy1RWzTlGwT8C78f9\nNo8G1MCM5DW28llFOwFAUU/mf0UnWyYwbS+QRAvRl1Qkh62xQUPP5EbVhl+S\n6qPlN+XrFH9S1XIgLFP66NwTLR/Exc1yOrraXcgwSk2XoWI/2z04Hb03jSXY\nZ6h5XsWexbZPRBvSwrF+RInG1SUvVSDnCNhf5UUE13fv9w+FOe2gaDvif5yL\nm3LO\r\n=qUXQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "a20f9142d87013822441f417bd8c5b14999f2353", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.0.2", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.0.2", "lodash": "^4.17.11", "codecov": "^3.2.0", "istanbul": "^0.4.5", "unicode-12.0.0": "^0.7.9", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.5.4_1552326311006_0.48963301036520757", "host": "s3://npm-registry-packages"}}, "4.5.5": {"name": "regexpu-core", "version": "4.5.5", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.5.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "aaffe61c2af58269b3e516b61a73790376326411", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.5.5.tgz", "fileCount": 6, "integrity": "sha512-FpI67+ky9J+cDizQUJlIlNZFKual/lUkFr1AG6zOCpwZ9cLrg8UUVakyUQJD7fCDIe9Z2nwTQJNPyonatNmDFQ==", "signatures": [{"sig": "MEUCIQDNMG7Hj5VAWBt+HXXz6y9F6QYRRDi7k5ZhVtEVj3myeAIga6l6UqbG3HxeDYcoe0cG326T0CosYy+BakaSCDa83+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTwCFCRA9TVsSAnZWagAAxkwQAJVbqbYnT47OE+o9Z4fW\nDy3wc+FYT4CpexKlGPmx9l7PSXR5exZJn2qX1SZrnBxv4j8Nu58FMRaDULwu\nR7CF8BE8YMBjNnHJoBPfX/OMD8wRVBbIdFcTGvUK1RHGy7S1z5KUlqYFe7if\nQXDKKQXx+UWsjU1vUqpUuoMFKrxZC3ML8nkyFlihMMR5Y7vzldUdcPZmbKNw\nFUg+bQx29Fa0OHHcjp87ltH7tIxTrFE50cNRNw//a9ZWCZEyXeTsYWVoTiy3\nzNTPFQOtktEF7l12KUDrka6CrjNlcJwU2d5KYkCXMRrgUaGJ+apCzi8o/pz4\nDU0P83Q49Xb9UMSeOFbaKOOBUs/5gbD5LhAyRYLfwmlpjQGxqUjCfnMS+lj0\nSrph8pba+CM1zsRwSH6eG9OAobbdUGlbwXfckExMND4KhG3lOEoFKC3JBGmD\npGYBN1FlExCpu/wlQFXxBUVAXC3pVk9ZGqVmv3Igewb0+WOYXzmGSy3SxZmJ\nTFzvyGU7pNj1OGaNLBsCSACjrPnX+Bwxjtck/OHA8yMS+g/2M3KLgJPFW9l5\nIDUpw6fuqujIXrwGzxj7FC1B1lpO90jqyondX5anPh7ouLYiDO8LX1TH/z60\no3ormHgt1Ka4wK5FBH0n4gRyJYvBc/lj5AiSSiWuwbFMRvKnGQcdY2epzrXf\nhVHZ\r\n=C9Xt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "e32fdfecb5fb38e49ca6bba2af2a809001f98d93", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.1.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.2.0", "lodash": "^4.17.15", "codecov": "^3.5.0", "istanbul": "^0.4.5", "unicode-12.1.0": "^0.8.0", "regexpu-fixtures": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.5.5_1565458565211_0.4374305251268529", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "regexpu-core", "version": "4.6.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.6.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "2037c18b327cfce8a6fea2a4ec441f2432afb8b6", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.6.0.tgz", "fileCount": 6, "integrity": "sha512-YlVaefl8P5BnFYOITTNzDvan1ulLOiXJzCNZxduTIosN17b87h3bvG9yHMoHaRuo88H4mQ06Aodj5VtYGGGiTg==", "signatures": [{"sig": "MEUCIEJ5VlDKN6fAiCisNjmygNk4dS3rgAuvvbE5PY5QMWU/AiEA2XHylxuNpw5nHWmzUbi9p/yAKdBKG0FV3QMKhZObNz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJde4CXCRA9TVsSAnZWagAAvvIP/3gcvaYTBW66Gj7VYrux\nbvncUe7t4si+gnZ4w7prA4qjfB/ulDteVBY4B0A+NBXAqMHWqHPJWe9GGh7p\n+Faj4l6RrWzRThEYcpZddLJuBtog87svSMKvzKKMKWTCTIqXsNXhLrXToYqg\nGH2LRpyIYQv9BRJeXuuKDx5enCqJtqNiRwmES0oaKv+9ew2cNynhU2opNpUL\nrS5jA0IsFfd7oDevEkN0iKpC+wbkd9mEf8++TFuYSj3m1jHiv3gmGiRg9VHf\nrfhnHx4YFYaLjIDfbcbibk784hngdpWoTMc7SYxTLGTImspoRW6LgEmmxu1A\n3F1JisOmseO9kVipLuI16axA7Gn7BIRPaTWZYFF9dNvMzdn383E47M7OX+Sv\n/mDU+e/s4ffl/ZNLHMy2neUCqSMESmHR9Le0McPKyaHpN7jFc6cWpMKz0H6M\ntGEb6rEik7+KYX5NP9UJLpawojHP4c3ZeuDNPZmrTeX6FSavpZHTGKQTKFr+\nsJtiFsX8y/vpBmf6H69jWklOxq0MEVU8rNbNvU60umeKCrwYn6cEse6obu8c\n4/D6R2JQnfN4kZv3x7sKfeS9rExi2g8FO9cs1MmRe15J8Fs9cOHupNpeZBUh\nIoW6o2jOuanPbtHIkRE6BjoaYpafyUWCymxhkaR5nGqQoWk671xAlHkwZQSH\n11Lu\r\n=qHD3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "6f20f824ca2e36d491c3f66040efbbdd6b433c86", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"regjsgen": "^0.5.0", "regenerate": "^1.4.0", "regjsparser": "^0.6.0", "regenerate-unicode-properties": "^8.1.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^6.2.0", "lodash": "^4.17.15", "codecov": "^3.5.0", "istanbul": "^0.4.5", "unicode-12.1.0": "^0.8.0", "regexpu-fixtures": "^2.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.6.0_1568374934836_0.38375614757696574", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "regexpu-core", "version": "4.7.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.7.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "fcbf458c50431b0bb7b45d6967b8192d91f3d938", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.7.0.tgz", "fileCount": 6, "integrity": "sha512-TQ4KXRnIn6tz6tjnrXEkD/sshygKH/j5KzK86X8MkeHyZ8qst/LZ89j3X4/8HEIfHANTFIP/AbXakeRhWIl5YQ==", "signatures": [{"sig": "MEQCIEt0nNK1GMgB4L250+6edqboSMUsHAPiA0JFtWfeFwoGAiA6wG+Pay7J5zvzSq7RrvfEaR43r9O2u+ys3u9Ux3JpbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaSY5CRA9TVsSAnZWagAAqeIP/1LyBiW64elfj87uzdVU\n3pyE7AZkJa/DlHt2+DAP6HYP4HD1A6iVG2crc42gKyQXyfL3E4h9NnBOa3vM\nhb6GjszBtusattkk5QwKDtVr2UGkWToRDALtU3SNXYiC05A0dJqqAypF9D9O\nIE52xnjUnDM/urEiMG5lfdphyOOFKlk6ebE6LtFIV9Bgp8algFmV/mHCAazI\nsFympD5/MQtY9wT5McR9sddyFB26+ClOIhgG+7SSrPIfpi3VljzfOKL3diKx\nzR6q9WpWu0CTVvULuFOVkj+S+iFMhoCb9fJnct4bB7RMheynqVeco9M7Jwab\n5u9Hn02RxGHOvpEejHTJ+XNuHguXIOPcJbTQhj3whXi55AMsVL2kjLDVCiPI\n58SkEbaRQFAI86kGYsLaKwDehdT7wjQkfrowxEH0WtdcR6scf69TgSNJOS48\nCcaXRfWV+Tsyu3+AU0fb99sgbs3WYsLRq62tH6UTsmlJVm50YCen6IIlJG3x\nVWGdlk3RrbQHeBl1TIcSpEJQ3JWtBzHJhwAAwRJWV/mJMrAayfMETkzl1Mww\nozwqTH0Nx2ZRn73MuS1ACf3NlhOn6q1Nxln9/3g0LGk4dJuobYryHuDEQa9p\ndI1AZJUUEmOj8TiQ0NFLtzJbOafY/btnsq9PyWSzWz80MKQJ8hTk7zWpVxbM\nEkOl\r\n=px9c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "f68eb2aaa74adb491d3fb3a1991c224dd9c3397c", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"regjsgen": "^0.5.1", "regenerate": "^1.4.0", "regjsparser": "^0.6.4", "regenerate-unicode-properties": "^8.2.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^7.1.0", "lodash": "^4.17.15", "codecov": "^3.6.5", "istanbul": "^0.4.5", "unicode-13.0.0": "^0.8.0", "regexpu-fixtures": "^2.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.7.0_1583949368883_0.437638677884463", "host": "s3://npm-registry-packages"}}, "4.7.1": {"name": "regexpu-core", "version": "4.7.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.7.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "2dea5a9a07233298fbf0db91fa9abc4c6e0f8ad6", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.7.1.tgz", "fileCount": 6, "integrity": "sha512-ywH2VUraA44DZQuRKzARmw6S66mr48pQVva4LBeRhcOltJ6hExvWly5ZjFLYo67xbIxb6W1q4bAGtgfEl20zfQ==", "signatures": [{"sig": "MEYCIQCJjdJBtLqrL2X57eTor0Jg9Bvpr4CwhcZHOCWP83iqlwIhAOjIho58rbPQEJJ/GbUzqqnFiuKDuZg43FR5/oTdeN2Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZGX8CRA9TVsSAnZWagAAwD0P/RZgwtZBj5W1G4rk3umn\nmCbUpJAgeopz7s6wHP+5gpKdF4I1+Sd/JpIE/Y5aRuWUkEg4k8IhEQxv2hLX\naHzwIc8beA8rby+Q2nfcRAzbmU3IGliAcf6QbWFcgvnzmNLB9LZQYDXKIsZq\nprrTJcBhm7QlV8uopuwsmtRfQ0y2dBPi7lQdNJJKeYNRJTECd+nMxuWbyHMC\nQj1TDdBfORz8loi0HCa9dacUfFgd+snv5ZDMC2Z8JMv8wAKkf5VN/3j+yHYi\n0Ufn3zVlB2fImdZ52vi8cwoEp/nln01Ez5OmU6otqJcds52ns6zSneWuGFqX\nif6nCVuwdLomkCmPcvdy01PiT4HjgPUEFAqniwgV9UFeQhH/q4k1JsYZsMZQ\n+X/ANgBw28Q5racOGZjHn1NO2f3zufx0Ud9OxZZjaIbjfdr29Jtim++5Wm3n\nK1HtJ2dko7tUF6MozIm63Z1VRyeOgUy5tmtpREp5HxKhisreerioEuhJcbvs\nj0w0l8Pl1B508qZSib/ROxcdSu6Tm/CAc7A0FSuIMYiA29ZgT9zIhNx/Fu58\ne1TlPVlBr9v/FT9OGF5xAarfm6ie7d3fO6kQI5Yd7sihnzISzW+bxnd8/B+4\nKJx4dDmDlpLKaM1EZ+b5N6uRyyBmRiX14lQhZT3+9QFclZyKkcoOTbxJ/VU7\nH2pl\r\n=Um3N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "bd9396d915117826959d478e91b73b066caa8a55", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "14.11.0", "dependencies": {"regjsgen": "^0.5.1", "regenerate": "^1.4.0", "regjsparser": "^0.6.4", "regenerate-unicode-properties": "^8.2.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^2.5.2", "mocha": "^7.1.0", "lodash": "^4.17.15", "codecov": "^3.6.5", "istanbul": "^0.4.5", "unicode-13.0.0": "^0.8.0", "regexpu-fixtures": "2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.7.1_1600415227705_0.3942876457994344", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "regexpu-core", "version": "4.8.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@4.8.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "e5605ba361b67b1718478501327502f4479a98f0", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.8.0.tgz", "fileCount": 6, "integrity": "sha512-1F6bYsoYiz6is+oz70NWur2Vlh9KWtswuRuzJOfeYUrfPX2o8n74AnUVaOGDbUqVGO9fNHu48/pjJO4sNVwsOg==", "signatures": [{"sig": "MEUCIFYdaVnlg0X/EaM+U6WcawvKHJhz21HGdSmzxGPT+oQpAiEApIzH+Dz1R6xPq0JBftdctOjKNy35wGM6syk/8X/Vxas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIgKCRA9TVsSAnZWagAA5k8P/3KmvUhGUwpgpXd4fvBE\nS/FRNruAxVx3S7V+PChrVbtlHJ9h7vB6/Z7AecqNYckRstZwKQbUwFJrjt/0\n3QDdz1rBpzQMaVyqQAEaQ3acMzNe/ASacTP+RdGozNZAORnIt6/lpkfDe/mO\nfrd+8CzrtsTFrlPwuOL4bAYYSBvqBFSE4UM8ltBc8cSkNpwHJ16xsCgR//7V\nCGHpoyu3KeGaNS1R4Oow513Umlp1DZ6vWyTxJwkF/yKy3K1h4rFJicnvUPvK\nVqKTpvjZArWvrclADu7Lo8iu6j5wxEXA3khgXAW81yI+mdU1LqkQEdezRIgK\nMiK6M67SMmCdrZr89qpoWtxTKR6aLkqFkL7ppFG1d1l7aEkEahJ8xki5Lsk3\nzbRvPoAySBNqwQlWohTLt9R72gzlUrNzmwX8qGPE5YIukX1tNfLiBZ8QYoHY\ncsH4GjWop9PwNKYGKukFeAeYDvq0MMH8BHF1OgM6l1aEgJ8HnbReBAwoatF+\neyhHSJuMQoS0I3C3e3bSqcNOt/UDtkN+c2UH7vocjt7Ep4RzzvKsnnuZw2CR\n2jZgdyL3hPdAYNJJj++qcfad6j+GLTTNzpxeR4Ku8F7Urh72aP+DnNBB5YMz\n3emLYFDrkgkVsBdx4a1wYyhWOu0vHw5+p/Pk7dgBSJgCXxUn7k40jB3nCRF8\nWfCu\r\n=bg4O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "ebf17f04db64a20ce74902c18144e915b82183b9", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"regjsgen": "^0.5.2", "regenerate": "^1.4.2", "regjsparser": "^0.7.0", "regenerate-unicode-properties": "^9.0.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^9.1.1", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "2.1.4", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_4.8.0_1631619082464_0.6544393656268634", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "regexpu-core", "version": "5.0.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "b207f96e06f3433f2b2577b15484df38a9c956e8", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-gybbrLKk7gxwYygiKbfv4iw0KrWUr7EeEKBjrQ+kTRmLSswgEONxESpMZRlVO8ztyrVCCWfL6QtmYXmIbcJ+aQ==", "signatures": [{"sig": "MEYCIQChiKbXorJjJAJP6oCd9T2HoHi0GmD6dnKeAV9pktWiPgIhALBxqzh440lxzOGi5rtcwhD4hYO4AAVsJ0+3ltXsTh9/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2fIjCRA9TVsSAnZWagAAUuIQAIJRWOq4rbqfURqIWE8C\nqxP1HL4+ABkLDV7JGNvaigQk2q54orMgQUt93S2KP/4+lGRFNJyGXUonbzOX\n7L6pKbAZynEAkCs+wenVlsxqf+8gENkWH2lZgT73V+3v4IJc9laa5SzefGbl\nw9GNJ92xLMuQ+vPL9LVgYEGuqR5GKSSOqJmIP4LDpprXUhq0xR/VMukbWbkq\nqK/hyKAglLM/89LeT9GyRBpYxfVBicpv6hmqk4FsDCnongmQlHeRMKEIfFZW\nQBJipWhxMkDaq38NtNJxRh7b4vHX4dC48yL3al2cj/UCJBSmj8Z5ovww0vXs\n9DAZUqI7CRHge1eFvtTnQurpx+HgYNQdLt8GzvpoMNKmomPnkh8438YTNzwf\n0xxObbPXpOd7rc2k10rPInrmkcMU1ktYGqH8PotzP39ihozYSvaH5T8sIThY\nYmpiHGUlS3XPl3AoCl7Qh5PY+RsUv/tPQ510FjiEp51N2D7PfwjWsDWB8cFz\noQ+GawvcilpMafcxEUg2ZWKFHj5BuBaBiMcjiDzMIq0/8SZJLXrkazMj8xIS\nYJbUQEHYsKKSQfndTBuIP37XLZ1ViJmoiUUeKGVI9tquslXE/PazY3VWIhtu\noiL+TRv33nRzsJXsW3Ej/odFECPreAx4J3+rxoa8TDpVKK02hDsZgodt5shf\n3bwb\r\n=Mg1y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "cf52719c383b6b0ffe5ff938ac14f12b84321e0b", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"regjsgen": "^0.6.0", "regenerate": "^1.4.2", "regjsparser": "^0.8.2", "regenerate-unicode-properties": "^10.0.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^9.1.1", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "2.1.4", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.0.0_1641673251337_0.3762769445267331", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "regexpu-core", "version": "5.0.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "c531122a7840de743dcf9c83e923b5560323ced3", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.0.1.tgz", "fileCount": 6, "integrity": "sha512-CriEZlrKK9VJw/xQGJpQM5rY88BtuL8DM+AEwvcThHilbxiTAy8vq4iJnd2tqq8wLmjbGZzP7ZcKFjbGkmEFrw==", "signatures": [{"sig": "MEQCIGr6xyJLFtsTlvljKnaeSu/JA1Tk5aN1UeamWGVmlyDgAiAX/vFu0hBINbNAKEa12d/h3LGnjqAxTV7LrkqX0nlMHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3G8DCRA9TVsSAnZWagAAmJ8P/jawg+mV5JWyqAnSmbs1\nV9JUj1o4xdAsKm8WwJDxsBq61D+xkzXVJO2hckNZf7WRg1SJjJpE2EzfdmCk\nrZ6ta9WZsfxlTpvtpgkpg2DYqJaXtUzK2Ct9M66il7Ff/C2el6g3GHrFlqBL\nXZH2YtYIx0TJFA/9GMq+mq7G8ZSr/8I5kXz3y37hiFlfP07vTCKZXPOOpsvr\nEJE+bAD9P/SCujkAFo0cmVOtjSt+v7XimauN0HFlhBqyenmIcQlFQQ7ciy3H\n3oIwwOhGTehyahaR4PODYuEXZGs1wQSP323p9Z0CJdhlXNdmJGPOreMjQXYu\nvycMrE0S8EmyvlHnONsGrWCsFhiH6Wbtg2gZZ8IqFyGFI5uqdKXj+DLeqkPZ\nR9nxsPhC2LgO9VCZy8KTIjXtqm2+0G5Tsl7yIvVB7Y2X73vLLRGcULfpcC/W\n+2HJsbA53aynBlqP0uZeLY6oHmbueGu6WSyuoqmnzOeQw5bAWCBVZYbZx4G0\n/YakRESiCqJ1pdfglgCD3fKOySNC+JB6GktxElZ2Zib+mYGVwlAtbrhZO2kB\n6xq5WSX3adjBdcnyCMLVkZwK9gZi3TWrl5+MnnD4waUNJqt9UdIPiINWBGpY\nsrh2Ah/bQOzOr/nIMFbophmNqTycS1Go+S6TiDVOv5gwDRUcolzKReOW1187\nBipr\r\n=sEPX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "2c9983be22a6117c4fe6af581f3307eacbaf1599", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"regjsgen": "^0.6.0", "regenerate": "^1.4.2", "regjsparser": "^0.8.2", "regenerate-unicode-properties": "^10.0.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^9.1.1", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "2.1.4", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.0.1_1641836291602_0.3697975007397343", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "regexpu-core", "version": "5.1.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "2f8504c3fd0ebe11215783a41541e21c79942c6d", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.1.0.tgz", "fileCount": 6, "integrity": "sha512-bb6hk+xWd2PEOkj5It46A16zFMs2mv86Iwpdu94la4S3sJ7C973h2dHpYKwIBGaWSO7cIRJ+UX0IeMaWcO4qwA==", "signatures": [{"sig": "MEUCIQCw2skqPLchxENfm1mLNbJeq9BuJrGi7l9LAi+iGPtgagIgKrFb0/akNKzeGGXaGTh6YY40BpQrDi13RSAVkc/CBAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuf6DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0xg//WzKU5BoMv7AjpNVMfpGfxvdpEUYWeAYspyGH6ioEzSIumOBI\r\net+dkvrPIgy28fvS+3vKqQx1lj7ai5nKF9+2BhSYRPHbWGitXNaS9Bq1cO8L\r\nizgg3oNSb+Fj69RjHiVWDWe2KW4ItLMEukc5LRFBisOdeOA4f+vwvcG9G+Zu\r\neBArk+Sv1/t2Ekk1mQSFImf8ymhG1cu37ZWEnWFqI/CwNxx7KZps3Yp3j0E1\r\nMtorkBRK7rtjNFz8+XFnKiA9kBoxqEgs56PknXT3hAfNOy3gN10Z9zf+/9m/\r\n9lmW4MMCz8WDryP5S6Rx8XO1mQNviWEO56/8MtRkQcgTdgU8w8lzi5MEfSjM\r\nYKZXoDJ+T/Zr1w3XKgdyXMRbyWM+ddIPL5tBeKvtUolUV7xhhrWuSfsyc9rs\r\nxCChPlKEQ5UiGYfdM8uLkGD7cln6ll5Ci830Lz6/8U+9c1iZvyEJ+5rrg/Sf\r\nWWoPwwhlX0M6w/VbDMYbVH+UlLN0cumY22OtZ63/jX+gFEnfTIOGEZsIqgNe\r\nPfZ0aJJmw008axmicFyKGj4XKTZJa1826EID+nvoGkwYnDFRECg08p+oXBU3\r\nOMlrmgolDBVWAvimtW7PaL7G/H7iy99NIsdO/n9f8f+P8kVhVwL76fLQR7K9\r\nchHAznETqjRlD/pQ739duCy//Zj+bxB8VaI=\r\n=H93+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "455d44dad3107d389110cfc059140b810031c69e", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"regjsgen": "^0.6.0", "regenerate": "^1.4.2", "regjsparser": "^0.8.2", "regenerate-unicode-properties": "^10.0.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^9.1.1", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "2.1.4", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.1.0_1656356483577_0.09276636709535002", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "regexpu-core", "version": "5.2.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.2.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "a69c26f324c1e962e9ffd0b88b055caba8089139", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.2.1.tgz", "fileCount": 6, "integrity": "sha512-HrnlNtpvqP1Xkb28tMhBUO2EbyUHdQlsnlAhzWcwHy8WJR53UWr7/MAvqrsQKMbV4qdpv03oTMG8iIhfsPFktQ==", "signatures": [{"sig": "MEYCIQCIcwqkNg6kwrs+aXOaXRCfcvWuLK95h6NP33V7Owa0yQIhAMJ+ItzbXhy4BeCRshaEoSl2tOm5uMi5ax2moJHyy7bZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIgeXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3IRAAklcP2kvvo5mXtJ6Thkc2FR3Ghfwy8W407/omVEPx4IDWAvJA\r\nK8LjV4e4ZBIDr0jCKOrsq2869NgO3L4DQJh9J1PC9OUdo8yIw08cisZSPeP5\r\nJFdQxysojt3q16nfvnzkjRoxtGTqG6sDOxlY53FC6qFmaR+HKlfBfTO4mRoq\r\n2rmCvtMb+kRuPlWJQwQTD5KiDHIFtnUQEjPTK4veX8PnppU3T1DXXQ4Hj1Y0\r\nOHflfPQIb1Te1xlxtxeZTJyNGKlOeir/dAD1CXdR7ZNwd0727ziZrgQa2QqF\r\nq9deBDJXccOprveCJHlB5dLqnFUYbl004TjQgt1zxmkweMKRZSI3Hn8BbKam\r\nm0vgfbmXSFM6v3RHR/Fj/99wwP2s6JhwkJFP7HPlii0JsO0CZEWSOqtxDxN3\r\nKv0KDtUSTJGAmtxK+GqhuhW1BFKkVXsvILF2nyqUZ4lbwgNXy9AtY5wGR1tF\r\n1kREYasWVSapHPJpeqxmK3aLFH8OALBRg3j0gUiyNsJ71L9MVNnpi+S2oOL8\r\ntfprT7GS7YQk9F6Gn6i80EEpRUDv7pXm2i83xgE0qoijGJing8RpFpbae7SJ\r\n3Il/pVZi9EPJmZdCIJxAYffFOimRVKhHL/JCoQ+T+koP309tF/2XLVJxWGiu\r\nAmgRTgeln66wh1fQ6R3Lb6qQVL0TQb/IBaA=\r\n=nu9e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "a6d0b8d42347490fea8cfded28451e8cebd5aea9", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"regjsgen": "^0.7.1", "regenerate": "^1.4.2", "regjsparser": "^0.9.1", "regenerate-unicode-properties": "^10.1.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^10.0.0", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "2.1.4", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.2.1_1663174551530_0.14443303814911324", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "regexpu-core", "version": "5.2.2", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.2.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "3e4e5d12103b64748711c3aad69934d7718e75fc", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.2.2.tgz", "fileCount": 6, "integrity": "sha512-T0+1Zp2wjF/juXMrMxHxidqGYn8U4R+zleSJhX9tQ1PUsS8a9UtYfbsF9LdiVgNX3kiX8RNaKM42nfSgvFJjmw==", "signatures": [{"sig": "MEYCIQDMwT5hMsNW40wq5ivnbJhNHUP3atuXDGHDCTHfWaGp1wIhAKdd8KmeTo52AEY7Ecm7gHdwa1doiF/VbR/WttnCu2Rv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcqiMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVEhAAlDnysCcas9/r2TOqgioZW73Sxrp+7+kMj9UsXs9MJugDyEQe\r\ncIT4ObOOeb84rYVvhZrfp9Cmc7DoLveoB3VTA1eefZ+1YXgbG6quwlNXpUjG\r\nIpxGxc41ZWpdZkvsUzN571xQg1SpnSODyLePr+2gPkKn+xtf3nJCZOu3Ezu1\r\nV5ykEXyAlmKojXLkZrDPq9AKEsHgdJv1iAPqwoBY2pzaX+EjThKloDH5QEnm\r\nU9/sY70iVjo04PhfXf14cX+AjQlOwaOCcDdViZvWtZEqmSw7X1RMOG87H0Lp\r\nz7ilUNmslc1kD51P5Cu931mD/TUbbWNG5SQ+44WiqYbQpZHYEWl8buw+IHvw\r\nGkLs1GTOxsz8O9Fc/Iqzl8jE61wkB+IELHP7S/LeucJbfgVIDZ8U7VzcKP2t\r\n0LqyynZavdjemrRY2yxxO8YqbX6TKYuiBTzAT6LKKZJxSJQUHU3aVhiV68vG\r\ntHMSkVKOVNxzkKcNoO7HwT2Jgdab09EiYsutAtXUEiWUsBgCAMbyhioTbw/7\r\nwQZ9ngvwA6lQ/QrkC1wq681uTiW8vG+k1rTGivbx3ELJ01m401vzEUmEe/2Q\r\npioMGiVEq2DmXuvFQ4M6nmDjFdl10pVO2Z53lqTPHhawlSJ6oRF8NtXXsnjV\r\nvgcrZ8rWUqoiC6qhslBCTLI+AglW9tyuQLc=\r\n=h91F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "3515c6ba6fa36851dfa0210fb6fe9a95cf2c0494", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"regjsgen": "^0.7.1", "regenerate": "^1.4.2", "regjsparser": "^0.9.1", "regenerate-unicode-properties": "^10.1.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^10.1.0", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "2.1.4", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.2.2_1668458636700_0.00183225641105067", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "regexpu-core", "version": "5.3.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "4d0d044b76fedbad6238703ae84bfdedee2cf074", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.3.0.tgz", "fileCount": 6, "integrity": "sha512-ZdhUQlng0RoscyW7jADnUZ25F5eVtHdMyXSb2PiwafvteRAOJUjFoUPEYZSIfP99fBIs3maLIRfpEddT78wAAQ==", "signatures": [{"sig": "MEQCICskKvJ7Rfyt54/3Epjxwo/X8gcetYtI0eBfHa2Umf9aAiByvMj64sEhJLtzy7L2VHQd8GFoOL8qa0yOj2KhL8s+uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4/1sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbwhAAix5hA3fG2CjMh7tuR5CxwFfHLe7ZgWSPLC/gRHvb6aILL+eu\r\n6uWSsPf5wTfFhdX8N1Rj17or8bvCjArFvhuMyL9tXWObIMtUqPNfU6YALM1t\r\nTPxI7AlOycXw7avuteHi4vKACQ66jy7af3+1sM1BzuIE7VO5wFPUirGDJDYz\r\nYz4+b9urCbwi5D91+CJk5kChq+g/gd8U6NIkCueiS0hIXoFkZt3nF3M6oLnf\r\nVC45lLHJ7vPW9jtInizbYwK4+c34xf1pc8lBqFoQusnpBnN+D0S+pZlW9BpH\r\nN2dwZXXhZs3yO+JtFq3asMsgJb1tN1q6aQ9xG3qsKr+FnJrTWreBDVAVJXOp\r\nEGWhOc9jWMy5Bcwt0aeGMypa7DnHXMblyL1HnrBRDUmMV6FcKfOf7NnkF3lM\r\nbDLT7SbwNbAdXdHVbPdmgzPG3qCbA219PhopH+Ce4u6+SIDbWp+d7Ngxe58i\r\nzqkga98U5HjhlpMNSPj0cUpG6XQiU74370H30yNi/0hLnVqFwg7y7K+d9AuR\r\nbPbqBRAraAcxtWFueRO9uTy18MraQiccLPQ5AJA4yq/PqUBWW2ilp6JgQz5f\r\nsXONAa7jNGZwDbHNxQ9zom/HgMtwTzQyx0kReTNlKOXFODziACXwZN5zcjCy\r\n//Q+b1Rjjsx7Pdrq+WV+CiW0WELPSPLc7Co=\r\n=UdjM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "54544820b3634563f62cb0b66d26efeadb6a886c", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"regenerate": "^1.4.2", "regjsparser": "^0.9.1", "@babel/regjsgen": "^0.8.0", "regenerate-unicode-properties": "^10.1.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^10.1.0", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "github:mathiasbynens/regexpu-fixtures", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.3.0_1675885931871_0.50522362052809", "host": "s3://npm-registry-packages"}}, "5.3.1": {"name": "regexpu-core", "version": "5.3.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.3.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "66900860f88def39a5cb79ebd9490e84f17bcdfb", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.3.1.tgz", "fileCount": 6, "integrity": "sha512-nCOzW2V/X15XpLsK2rlgdwrysrBq+AauCn+omItIz4R1pIcmeot5zvjdmOBRLzEH/CkC6IxMJVmxDe3QcMuNVQ==", "signatures": [{"sig": "MEUCIC4YWIv66mVEkLKCJdzVltgzY2JGymBxQiKporR+3WR8AiEAkQWUVu6R42f5E/z9PVMlSDywNrt9yEwaLXfl4Q+lLCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7kZZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoc+xAAonI0IizwFsbK/hjSVcaMQN90JJmVxRkFSd6YwPGqLnm3MaZ+\r\nhmeMNWIftZx+0vdt8qzV0O34BdTGRsx33jhAENxdlpJX/Fe6axznJ2V80Jno\r\n9v/dBPWh8rs0dYHum9FFBc3jwmWsZ+jVnzVyrhMtLzHajA+/9lPjMcWAfN9x\r\noC1097pZXHjDonO5YB9RCjrD7yV271KOqOeBCnRxbYOvUKbCg6Mn8KtIm+xW\r\nS3UjiGgsNUr0Un41wBsCN629TKLR01Ycw7SrFiHIM7Qjyq5rQz85f0Xh4ogr\r\nbGMAOpm+WaqLeIl1bC2BH4VkqAndZ6PqWw4/8EXab9rHnUWDSyHfID5cABZR\r\nIRJJYX2Y2Wr6BjJ/p93VbUAAj447iXHVb0agRm25IklZJhHkL4tF0QPu5bFn\r\nY4+nEUwBcYl8sqtsweZ++Qnuey2i+mf8nwq1w3+LBpjT85huIbq+UHF4HYqg\r\ns+4GyqtbNqHwrO/676f1xs97jpoYw8j0ieb55eESrHZ8YKToqZwmDK4oz07w\r\ns7BiqQiIEFNt1+BNEKFbOaWWFOQGnY3qt7607k8ut64XZsR4Fi7lPP+SzIgw\r\n/YSgtxckuJ5TmO5lk66EQ+tz+pdi3045NwVgawSiXJ9Xz8mvTg4OfX543m8V\r\nFdZ88ymSXjzTQ04ja4nleXtMlj+80r0sK2I=\r\n=YYhh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "2f0938599e9f30dc246cefd8a8694924aa591446", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"regenerate": "^1.4.2", "regjsparser": "^0.9.1", "@babel/regjsgen": "^0.8.0", "regenerate-unicode-properties": "^10.1.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^10.1.0", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "^2.1.6", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.3.1_1676559961427_0.7667451666434291", "host": "s3://npm-registry-packages"}}, "5.3.2": {"name": "regexpu-core", "version": "5.3.2", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@5.3.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "11a2b06884f3527aec3e93dbbf4a3b958a95546b", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.3.2.tgz", "fileCount": 6, "integrity": "sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==", "signatures": [{"sig": "MEUCIQDaThV/wg1cI1Xnu4FA428T8TJQcnaGrPcdzVrHJQX4DwIgSXm9l97WtsgBjo1GKx1IsXuIrCg9O5jZCnTzs3rxY/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC1rgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmre9Q/9FTj77miI53L+St7IiIzxqb/XCgDE5UKZd7Q9j40IeS90+aW6\r\nYGS1fmu9wtD2LZhNw/81OfMHmfEQHLT5XRAY07EwB+IAqTDjkd8VMLwWqL1S\r\nGe4xSi2GmFoo0bzknrmXNZppJ+H9n3KIPXZjVztiKMdXbNxqhDf0+MCqeSS8\r\nvv/8ubiq2TcVXLZW4OXjWp1P3Mf7o0uxWiOGCIW7be3YT2qnSCWs+8kLgGIQ\r\nkcbCHoA9mMrZZXFXv1kwCGiOGCt7E73F17h7A6Nuo2Fl8b5gpZ62fqbMBgqM\r\nPmV+WcehfiKEN2mUvs0g5wvLm7DQ1yVKMsvDjp/H/AW24GL0vWwbZWQgAL/5\r\ntK4PgLyDTDnHbWoG9ldX/KYR9TlSq5q1dBy5JNiPaluga8kfQRGUf4/hIQa+\r\nQTkwN65CyXCFG5Drqd8v3rnlu3EZTIH8dWlE8uGDki7BEIYwrB5m/uSbC49+\r\nXMbN7GZuGYvhcEJFcXdar32mdv0BtzakwGX0owkbFEwLX2oCGDtORVaIMAQZ\r\nOO+Nv0l5iOifYP+O7SIgB/zqsBka8i062Ap2ylRu/rQ1Vy0JQfXVJjt8B6b/\r\nXucyhKKtqI+XX0geRhhPpOKksyxU7tppUoOQx9M69pXMDUJrCm8VdmJX13KQ\r\n/ZFKZrVxkXP+TTkYdiWEH9zkEKdysaezzkU=\r\n=+18t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "16606f73dbf8fe48f92defa9583ab8eccc709d23", "scripts": {"test": "mocha tests", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "16.19.1", "dependencies": {"regenerate": "^1.4.2", "regjsparser": "^0.9.1", "@babel/regjsgen": "^0.8.0", "regenerate-unicode-properties": "^10.1.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "mocha": "^10.1.0", "lodash": "^4.17.21", "codecov": "^3.8.3", "istanbul": "^0.4.5", "regexpu-fixtures": "^2.1.6", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_5.3.2_1678465759931_0.12821745493067405", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "regexpu-core", "version": "6.0.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@6.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "2706fae3c6599c6a0d09933601eeeba6dfa00aa7", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-ZOZKiuXx8X0IZLr3eyi5AfJd9Dg9YIdSNrxNKjx7V8kqIRf19NrmG2TLyUzzOLIoiMfbIqzw2LRGDxs51gLq9A==", "signatures": [{"sig": "MEUCID3FdjEXMVzT7vRE5cXnhxYwkxvhwZfTez8HD1B4i2BAAiEAmJJmaKeliC4wts5upBeoCfIyIPxxj61F3txa12rXk9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54548}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "b278ed4b6518fda558ea9836a1181ebb22181a7f", "scripts": {"test": "node --test tests/tests.js", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "NODE_V8_COVERAGE=coverage node --test  --experimental-test-coverage tests/tests.js", "test-node6": "mocha tests"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"regjsgen": "^0.8.0", "regenerate": "^1.4.2", "regjsparser": "^0.10.0", "regenerate-unicode-properties": "^10.1.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_6.0.0_1695622126752_0.4185815733820837", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "regexpu-core", "version": "6.1.0", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@6.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "3a8b98fd59530b5527dc204e13a22bfe686f738b", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.1.0.tgz", "fileCount": 6, "integrity": "sha512-OXuyOsJFp1Zm/VY6yy+1SGUnzDV8pCA+3c2n6P1WhYQD1Olgk2Cmg/jNPyzmQwbM95MEbIEHemGk6ZznNFBwLw==", "signatures": [{"sig": "MEQCIBxJULjw2ybqtboyJr23Gm912qEw6PH95/uU3kWS+L84AiAGi5XPUN8Zd5LlDrkZIWdyArjQH58uWE4QV1ibqOhfMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55752}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "71382026bf77814d1df543c7abca027537541c76", "scripts": {"test": "node --test tests/tests.js", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "NODE_V8_COVERAGE=coverage node --test  --experimental-test-coverage tests/tests.js", "test-node6": "mocha tests"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"regjsgen": "^0.8.0", "regenerate": "^1.4.2", "regjsparser": "^0.10.0", "regenerate-unicode-properties": "^10.2.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "@unicode/unicode-16.0.0": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_6.1.0_1726674846510_0.2982139289474206", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "regexpu-core", "version": "6.1.1", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regexpu-core@6.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/regexpu", "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "dist": {"shasum": "b469b245594cb2d088ceebc6369dceb8c00becac", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.1.1.tgz", "fileCount": 6, "integrity": "sha512-k67Nb9jvwJcJmVpw0jPttR1/zVfnKf8Km0IPatrU/zJ5XeG3+Slx0xLXs9HByJSzXzrlz5EDvN6yLNMDc2qdnw==", "signatures": [{"sig": "MEYCIQCbOOAMF+yK5GgvaK13niZO1hb0eAcaM4A7X+VReNu9xgIhAKWSkkd1T0zVBhCEW5RNZGGJc+DZ8za2MBBIfJG7jySR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55752}, "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "gitHead": "983febd7687529c0ebaa5fb78fb5f5cbda939b57", "scripts": {"test": "node --test tests/tests.js", "build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "cover": "NODE_V8_COVERAGE=coverage node --test  --experimental-test-coverage tests/tests.js", "test-node6": "mocha tests"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regexpu-core.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"regjsgen": "^0.8.0", "regenerate": "^1.4.2", "regjsparser": "^0.11.0", "regenerate-unicode-properties": "^10.2.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jsesc": "^3.0.2", "@unicode/unicode-16.0.0": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpu-core_6.1.1_1726854894387_0.5534272896390129", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "regexpu-core", "version": "6.2.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "homepage": "https://mths.be/regexpu", "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regexpu-core.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "scripts": {"build": "node scripts/index.js", "test": "node --test tests/tests.js", "test-node6": "mocha tests", "cover": "NODE_V8_COVERAGE=coverage node --test  --experimental-test-coverage tests/tests.js"}, "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "devDependencies": {"jsesc": "^3.0.2", "@unicode/unicode-16.0.0": "^1.6.2"}, "_id": "regexpu-core@6.2.0", "gitHead": "235596f34785fd0cea3e329e81a7c7a063d3765c", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==", "shasum": "0e5190d79e542bf294955dccabae04d3c7d53826", "tarball": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz", "fileCount": 9, "unpackedSize": 135551, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIELsy8hj/dpPRJz09GDxKs264cQOrP8+okX50ktWrCZYAiEAzJIX47nfowTVdK7B4md6soqxTOz+NT6G2m1S7L3Rl/Q="}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regexpu-core_6.2.0_1732220053720_0.7916893680780639"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-01-11T16:23:08.754Z", "modified": "2024-11-21T20:14:14.120Z", "1.0.0": "2016-01-11T16:23:08.754Z", "2.0.0": "2016-02-08T19:36:45.542Z", "3.0.0": "2016-06-02T06:34:12.783Z", "3.0.1": "2016-06-02T06:37:55.248Z", "3.0.2": "2016-06-14T15:08:29.527Z", "3.1.0": "2016-06-21T19:29:57.773Z", "3.2.0": "2016-06-23T10:14:48.025Z", "3.3.0": "2016-08-17T07:22:44.907Z", "4.0.0": "2016-11-02T12:10:29.321Z", "4.0.1": "2016-11-18T18:18:34.542Z", "4.0.2": "2016-11-29T07:04:08.896Z", "4.0.3": "2017-03-08T19:06:22.483Z", "4.0.4": "2017-03-08T19:21:34.682Z", "4.0.5": "2017-04-11T12:03:23.834Z", "4.0.6": "2017-04-13T10:05:25.243Z", "4.0.7": "2017-04-14T09:56:58.846Z", "4.0.8": "2017-04-15T10:49:56.404Z", "4.0.9": "2017-04-15T11:04:13.362Z", "4.0.10": "2017-04-15T13:07:25.496Z", "4.0.11": "2017-04-15T13:11:50.107Z", "4.1.0": "2017-06-20T17:09:56.141Z", "4.1.1": "2017-06-20T17:25:14.742Z", "4.1.2": "2017-08-17T10:32:54.555Z", "4.1.3": "2017-09-18T14:37:13.571Z", "4.1.4": "2018-05-12T23:20:31.585Z", "4.1.5": "2018-05-14T20:40:44.605Z", "4.2.0": "2018-06-07T13:19:58.039Z", "4.3.0": "2018-12-05T09:43:18.334Z", "4.4.0": "2018-12-05T13:23:04.345Z", "4.5.0": "2019-03-05T12:42:27.292Z", "4.5.1": "2019-03-05T12:55:38.819Z", "4.5.2": "2019-03-05T13:43:08.664Z", "4.5.3": "2019-03-06T10:26:21.408Z", "4.5.4": "2019-03-11T17:45:11.124Z", "4.5.5": "2019-08-10T17:36:05.353Z", "4.6.0": "2019-09-13T11:42:14.969Z", "4.7.0": "2020-03-11T17:56:09.008Z", "4.7.1": "2020-09-18T07:47:07.839Z", "4.8.0": "2021-09-14T11:31:22.583Z", "5.0.0": "2022-01-08T20:20:51.494Z", "5.0.1": "2022-01-10T17:38:11.828Z", "5.1.0": "2022-06-27T19:01:23.870Z", "5.2.1": "2022-09-14T16:55:51.680Z", "5.2.2": "2022-11-14T20:43:56.902Z", "5.3.0": "2023-02-08T19:52:12.053Z", "5.3.1": "2023-02-16T15:06:01.679Z", "5.3.2": "2023-03-10T16:29:20.145Z", "6.0.0": "2023-09-25T06:08:47.143Z", "6.1.0": "2024-09-18T15:54:06.683Z", "6.1.1": "2024-09-20T17:54:54.626Z", "6.2.0": "2024-11-21T20:14:13.920Z"}, "bugs": {"url": "https://github.com/mathiasbynens/regexpu-core/issues"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "license": "MIT", "homepage": "https://mths.be/regexpu", "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regexpu-core.git"}, "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "readme": "# regexpu-core [![Build status](https://github.com/mathiasbynens/regexpu-core/workflows/run-checks/badge.svg)](https://github.com/mathiasbynens/regexpu-core/actions?query=workflow%3Arun-checks) [![regexpu-core on npm](https://img.shields.io/npm/v/regexpu-core)](https://www.npmjs.com/package/regexpu-core)\n\n_regexpu_ is a source code transpiler that enables the use of ES2015 Unicode regular expressions in JavaScript-of-today (ES5).\n\n_regexpu-core_ contains _regexpu_’s core functionality, i.e. `rewritePattern(pattern, flag)`, which enables rewriting regular expressions that make use of [the ES2015 `u` flag](https://mathiasbynens.be/notes/es6-unicode-regex) into equivalent ES5-compatible regular expression patterns.\n\n## Installation\n\nTo use _regexpu-core_ programmatically, install it as a dependency via [npm](https://www.npmjs.com/):\n\n```bash\nnpm install regexpu-core --save\n```\n\nThen, `require` it:\n\n```js\nconst rewritePattern = require('regexpu-core');\n```\n\n## API\n\nThis module exports a single function named `rewritePattern`.\n\n### `rewritePattern(pattern, flags, options)`\n\nThis function takes a string that represents a regular expression pattern as well as a string representing its flags, and returns an ES5-compatible version of the pattern.\n\n```js\nrewritePattern('foo.bar', 'u', { unicodeFlag: \"transform\" });\n// → 'foo(?:[\\\\0-\\\\t\\\\x0B\\\\f\\\\x0E-\\\\u2027\\\\u202A-\\\\uD7FF\\\\uDC00-\\\\uFFFF]|[\\\\uD800-\\\\uDBFF][\\\\uDC00-\\\\uDFFF]|[\\\\uD800-\\\\uDBFF])bar'\n\nrewritePattern('[\\\\u{1D306}-\\\\u{1D308}a-z]', 'u', { unicodeFlag: \"transform\" });\n// → '(?:[a-z]|\\\\uD834[\\\\uDF06-\\\\uDF08])'\n\nrewritePattern('[\\\\u{1D306}-\\\\u{1D308}a-z]', 'ui', { unicodeFlag: \"transform\" });\n// → '(?:[a-z\\\\u017F\\\\u212A]|\\\\uD834[\\\\uDF06-\\\\uDF08])'\n```\n\n_regexpu-core_ can rewrite non-ES6 regular expressions too, which is useful to demonstrate how their behavior changes once the `u` and `i` flags are added:\n\n```js\n// In ES5, the dot operator only matches BMP symbols:\nrewritePattern('foo.bar', '', { unicodeFlag: \"transform\" });\n// → 'foo(?:[\\\\0-\\\\t\\\\x0B\\\\f\\\\x0E-\\\\u2027\\\\u202A-\\\\uFFFF])bar'\n\n// But with the ES2015 `u` flag, it matches astral symbols too:\nrewritePattern('foo.bar', 'u', { unicodeFlag: \"transform\" });\n// → 'foo(?:[\\\\0-\\\\t\\\\x0B\\\\f\\\\x0E-\\\\u2027\\\\u202A-\\\\uD7FF\\\\uDC00-\\\\uFFFF]|[\\\\uD800-\\\\uDBFF][\\\\uDC00-\\\\uDFFF]|[\\\\uD800-\\\\uDBFF])bar'\n```\n\nThe optional `options` argument recognizes the following properties:\n\n#### Stable regular expression features\n\nThese options can be set to `false` or `'transform'`. When using `'transform'`, the corresponding features are compiled to older syntax that can run in older browsers. When using `false` (the default), they are not compiled and they can be relied upon to compile more modern features.\n\n- `unicodeFlag` - The `u` flag, enabling support for Unicode code point escapes in the form `\\u{...}`.\n\n  ```js\n  rewritePattern('\\\\u{ab}', '', {\n    unicodeFlag: 'transform'\n  });\n  // → '\\\\u{ab}'\n\n  rewritePattern('\\\\u{ab}', 'u', {\n    unicodeFlag: 'transform'\n  });\n  // → '\\\\xAB'\n  ```\n\n- `dotAllFlag` - The [`s` (`dotAll`) flag](https://github.com/mathiasbynens/es-regexp-dotall-flag).\n\n  ```js\n  rewritePattern('.', '', {\n    dotAllFlag: 'transform'\n  });\n  // → '[\\\\0-\\\\t\\\\x0B\\\\f\\\\x0E-\\\\u2027\\\\u202A-\\\\uFFFF]'\n\n  rewritePattern('.', 's', {\n    dotAllFlag: 'transform'\n  });\n  // → '[\\\\0-\\\\uFFFF]'\n\n  rewritePattern('.', 'su', {\n    dotAllFlag: 'transform'\n  });\n  // → '(?:[\\\\0-\\\\uD7FF\\\\uE000-\\\\uFFFF]|[\\\\uD800-\\\\uDBFF][\\\\uDC00-\\\\uDFFF]|[\\\\uD800-\\\\uDBFF](?![\\\\uDC00-\\\\uDFFF])|(?:[^\\\\uD800-\\\\uDBFF]|^)[\\\\uDC00-\\\\uDFFF])'\n  ```\n\n- `unicodePropertyEscapes` - [Unicode property escapes](property-escapes.md).\n\n  By default they are compiled to Unicode code point escapes of the form `\\u{...}`. If the `unicodeFlag` option is set to `'transform'` they often result in larger output, although there are cases (such as `\\p{Lu}`) where it actually _decreases_ the output size.\n\n  ```js\n  rewritePattern('\\\\p{Script_Extensions=Anatolian_Hieroglyphs}', 'u', {\n    unicodePropertyEscapes: 'transform'\n  });\n  // → '[\\\\u{14400}-\\\\u{14646}]'\n\n  rewritePattern('\\\\p{Script_Extensions=Anatolian_Hieroglyphs}', 'u', {\n    unicodeFlag: 'transform',\n    unicodePropertyEscapes: 'transform'\n  });\n  // → '(?:\\\\uD811[\\\\uDC00-\\\\uDE46])'\n  ```\n\n- `namedGroups` - [Named capture groups](https://github.com/tc39/proposal-regexp-named-groups).\n\n  ```js\n  rewritePattern('(?<name>.)\\\\k<name>', '', {\n    namedGroups: 'transform'\n  });\n  // → '(.)\\1'\n  ```\n\n- `unicodeSetsFlag` - [The `v` (`unicodeSets`) flag](https://github.com/tc39/proposal-regexp-set-notation)\n\n  ```js\n  rewritePattern('[\\\\p{Emoji}&&\\\\p{ASCII}]', 'v', {\n    unicodeSetsFlag: 'transform'\n  });\n  // → '[#\\\\*0-9]'\n  ```\n\n  By default, patterns with the `v` flag are transformed to patterns with the `u` flag. If you want to downlevel them more you can set the `unicodeFlag: 'transform'` option.\n\n  ```js\n  rewritePattern('[^[a-h]&&[f-z]]', 'v', {\n    unicodeSetsFlag: 'transform'\n  });\n  // → '[^f-h]' (to be used with /u)\n  ```\n\n  ```js\n  rewritePattern('[^[a-h]&&[f-z]]', 'v', {\n    unicodeSetsFlag: 'transform',\n    unicodeFlag: 'transform'\n  });\n  // → '(?:(?![f-h])[\\s\\S])' (to be used without /u)\n  ```\n\n- `modifiers` - [Inline `i`/`m`/`s` modifiers](https://github.com/tc39/proposal-regexp-modifiers)\n\n  ```js\n  rewritePattern('(?i:[a-z])[a-z]', '', {\n    modifiers: 'transform'\n  });\n  // → '(?:[a-zA-Z])([a-z])'\n  ```\n\n#### Experimental regular expression features\n\nThese options can be set to `false`, `'parse'` and `'transform'`. When using `'transform'`, the corresponding features are compiled to older syntax that can run in older browsers. When using `'parse'`, they are parsed and left as-is in the output pattern. When using `false` (the default), they result in a syntax error if used.\n\nOnce these features become stable (when the proposals are accepted as part of ECMAScript), they will be parsed by default and thus `'parse'` will behave like `false`.\n\n#### Miscellaneous options\n\n- `onNamedGroup`\n\n  This option is a function that gets called when a named capture group is found. It receives two parameters:\n  the name of the group, and its index.\n\n  ```js\n  rewritePattern('(?<name>.)\\\\k<name>', '', {\n    onNamedGroup(name, index) {\n      console.log(name, index);\n      // → 'name', 1\n    }\n  });\n  ```\n\n- `onNewFlags`\n\n  This option is a function that gets called to pass the flags that the resulting pattern must be interpreted with.\n\n  ```js\n  rewritePattern('abc', 'um', '', {\n    unicodeFlag: 'transform',\n    onNewFlags(flags) {\n      console.log(flags);\n      // → 'm'\n    }\n  })\n  ```\n\n### Caveats\n\n- [Lookbehind assertions](https://github.com/tc39/proposal-regexp-lookbehind) cannot be transformed to older syntax.\n- When using `namedGroups: 'transform'`, _regexpu-core_ only takes care of the _syntax_: you will still need a runtime wrapper around the regular expression to populate the `.groups` property of `RegExp.prototype.match()`'s result. If you are using _regexpu-core_ via Babel, it's handled automatically.\n\n## For maintainers\n\n### How to publish a new release\n\n1. On the `main` branch, bump the version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push --follow-tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n1. Once the release has been published to npm, update [`regexpu`](https://github.com/mathiasbynens/regexpu) to make use of it, and [cut a new release of `regexpu` as well](https://github.com/mathiasbynens/regexpu#how-to-publish-a-new-release).\n\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_regexpu-core_ is available under the [MIT](https://mths.be/mit) license.\n", "readmeFilename": "README.md", "users": {"arteffeckt": true}}