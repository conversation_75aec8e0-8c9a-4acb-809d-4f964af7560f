{"_id": "data-urls", "_rev": "12-6d3a316c073b0b533c22d51104ba315f", "name": "data-urls", "description": "Parses data: URLs", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "1.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "files": ["lib/"], "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "devDependencies": {"eslint": "^4.13.0", "jest": "^21.2.1", "request": "^2.83.0"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"]}, "dependencies": {"abab": "^1.0.4", "whatwg-mimetype": "^2.0.0", "whatwg-url": "^6.4.0"}, "gitHead": "bd0705722c8992f48c9a9e2aa15e24189164a9ed", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@1.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ai40PPQR0Fn1lD2PPie79CibnlMN2AYiDhwFX/rZHVsxbs5kNJSjegqXIprhouGXlRdEnfybva7kqRGnB6mypA==", "shasum": "24802de4e81c298ea8a9388bb0d8e461c774684f", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcpX2fU23A3r6St5k0pxP5F0I66mzZbdfIt/XnzC58IAiEA6EljARcRtF8Fvh9TRBLg3Tuj0xT+v57VXx9spkqwIpw="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls-1.0.0.tgz_1517437914250_0.16816337523050606"}, "directories": {}}, "1.0.1": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "1.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "files": ["lib/"], "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "devDependencies": {"eslint": "^5.4.0", "jest": "^23.5.0", "request": "^2.88.0"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"]}, "dependencies": {"abab": "^2.0.0", "whatwg-mimetype": "^2.1.0", "whatwg-url": "^7.0.0"}, "gitHead": "ef1f61395cf0afdbbf04274509332becdd7eb4e1", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@1.0.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0HdcMZzK6ubMUnsMmQmG0AcLQPvbvb47R0+7CCZQCYgcd8OUWG91CG7sM6GoXgjz+WLl4ArFzHtBMy/QqSF4eg==", "shasum": "d416ac3896918f29ca84d81085bc3705834da579", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-1.0.1.tgz", "fileCount": 5, "unpackedSize": 7944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeI4SCRA9TVsSAnZWagAAag4P/jZ9Q8burJHBtUaco0Yk\naKXIRXZBHSxCRwC2w7Vm2AuAK/oXqlHVxXcgQmaCodTZHuw48ssXVWzuEhLn\n13TCxbGhsvEfn95j6vJY8z51DQRjTw0iM/Gc2ShLl6nCO0WWsgKX+8ocPbCq\n5TcCj/5QsVvEfTUAuKs7/6HOTBycTJ90qfImiEx+bRRt6NaGwzSy7/46ugTE\njMzTBZHosOlfzpvI+nJ2FAjl0AUNAhH5uH+rYEsaKUOfKYldCEYe9oDRf9GN\n2KPuBLjkTNd3bLFnZLlefuFYmu8mNhfjGuJf0IpanecXr5/9EH1iF9+kAzqE\nAIpiKo1/+AYLoUXeyp0d7qjUPcGQ2OA2JJkS47EJXmJlBiXsIY7p2XG0ey3u\n8988l3qU6xzl5aID6fUSm7ExIelClko//FVURY5gi7gq/sQH3fRuhn0D/KB9\nQQuP8FfJsYxHQWkTOHmLVfc6FC0p7egW3VbUwTjLyXbY5G6UFdiKDmIIAZxb\nj8X0lukofmWKwmJfemPOnaAop9cx2Isi8vB9KnOhoH25ytpL3tVo/cjdkilb\n0yvxPmLfhAJzM+6FcLB6xm6CPGUJLdfhSa4WHBrt/v1JEQth9ka4iAIJCYpA\nMCcdwqpLqO32XmT16sNXSI8J3z/FUqoV7Q+egyXRJRhTNjpVJQO0ZJ+H+VBG\n/VGc\r\n=r1Ht\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKUpLFmDQjnSq6I6w694Vu4SNQiyuBYt0OduQ2avFfZAiEAqjxvNQxZdzXaczBgNBH3vgrxlKphbMziwPF6uFgiZeY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_1.0.1_1534627346113_0.8636024731783971"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "1.1.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "devDependencies": {"eslint": "^5.7.0", "jest": "^23.6.0", "request": "^2.88.0"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}, "dependencies": {"abab": "^2.0.0", "whatwg-mimetype": "^2.2.0", "whatwg-url": "^7.0.0"}, "gitHead": "f42daf4c31a7ed00295993990ebbd2901bee168f", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YTWYI9se1P55u58gL5GkQHW4P6VJBJ5iBT+B5a7i2Tjadhv52paJG0qHX4A0OR6/t52odI64KP2YvFpkDOi3eQ==", "shasum": "15ee0582baa5e22bb59c77140da8f9c76963bbfe", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-1.1.0.tgz", "fileCount": 5, "unpackedSize": 8042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0xEZCRA9TVsSAnZWagAARWkP/R3pUS9sPJmYgzW/lkwu\nIvGEdJMr2Fq9qd5Jh+U2++gHrh1lse+95cvlebiHxSNcmf84IvfVZ5J9Zlun\nSU/+kkA9mlGZNb72SEUsQIEl6qyYCH5moGboH3zoEuu+1h3vel9l897Z66w4\ngWXa2N6HOZrVL8h3ILcq2egmbiUZ1lJ6VF94ggecFrHBkssUlZYKur1jM2eJ\nNo5t7Aasyi+StqbudS5177QP4LxbmOuFBwRW1fMPDo7Yv82gPqbAirj5vQnA\nVKG1pL8qu/4bZH2PgicIfERxQLPo4IkTTobf/WMBZ0OKVd8FuVKMZdEfZVcG\n7WE9FnL1TjNFlm1waP6DCXApZt5dWD4wbniuIy4iO3e1VzsWwyj5ZwOeeobp\nQiqHnKCsqLi2EmEHHt5F6vVdpyYW46tZUlXOzCUzAWCv0IWlR+8Hw1i9C7D0\n+l+RAC2XMmC/1LC7+5E4BGkN4ItkHZ9brsGAJk2/VBBU9F1ebueNwZiy1fi5\nIVafv/7Xd5vB/XZCDQc+pArXxVzooWIHGRol1U/CpJe0A1GaDPRSd4mmLumP\nuVjgeJdm4c+9x2KMUeAnVDXYD107cK1xkaaF5huMB6iFxs3q+AxfzFcKyHm3\nsQiQJ6dH+05hX5X9nVW028Ww+8auHpmAmA1zFpskFQaxMBAYGrfdgnHO0qOp\nRlEb\r\n=pfzp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDcgSsGRv/3KHXhTzsb3UKVARNaCtv6d6ryzue/HfhmeAiBkjN1SBdisX0IKbNDxAZ5ilM+j88VvI0POTcZWao5tQw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_1.1.0_1540559128977_0.9896566440878478"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "2.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}, "devDependencies": {"eslint": "^6.8.0", "jest": "^24.9.0", "request": "^2.88.0"}, "engines": {"node": ">=10"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}, "gitHead": "f7d60e8f5f990ebbddbea956dd561870b5370445", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@2.0.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ==", "shasum": "156485a72963a970f5d5821aaf642bef2bf2db9b", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz", "fileCount": 5, "unpackedSize": 8084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDlO4CRA9TVsSAnZWagAAbPgP/jXgS1Xf2UR+pl0spqr/\nkNEaU3k2PXncO/GW8tJCkTVKczApWDSRY6DgrU+EP4G/EYl8lM5uS3kWR4ER\nQnhRloiyw9MJ+C1wlyjVem9dBfpTtZzXg13RdHSAl+/Tuet2tCxkKiC+RSYY\nrCJnzjcGheMY4SXlXndpy1aPD/BkyriE7U4dFoYd7xu1+hdJfHV/aHJM8Roz\nQUHx1oYVZll//uyO+H3Os6BICKE7zrHpV2/9tprDgQddMR4PDqf40K6kHTvz\nrC2Jy4oMdbHHLsZuDapLdpetBe6yysHI7Ftk+Qekuww6SPxmMKTmpyRAlBk6\nWCp9RO4Bzk/ogvknWcw+JWThf74Zm1i+fqNoafSwsNUSy2m1gXmaIhZaWFQV\np7wowx0Le+n8fgzWCwR0PRT1OWPTULBDbrpSOskAOn67FJ5eIfpCudHq/kFF\n6FaDKGTRdqW0Yvu2ALjg8sRaDzoHOyzfxOrPfX5ay7tJ+yG24wahhPs8RvJ4\n1lw6P5D8ZTiePbmt4OGcnAtP2GHbKf3SljKBjDM/WTFnOCAe1F/+WAm9ALbv\nu+Hc6u0hSsLGRPcAdGT7d7mD4bmy0KImkmsluydZOMDG8OZ0O4fe7LJR2stG\np5LM+OeCRzqH1XgesWy/Z+N1G8msF9keZwd5cgoqydy5pFHXttMHf5YgawHE\nJva2\r\n=7TIT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHCmfxmg2bUZbYSqG57eDePas4y3V4ShEyeg9NaiMytaAiEA6AQCyZmaSByMqa0XV4VTq5RQkJfCe4pB03cGR4McWB8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_2.0.0_1577997239716_0.9784558077583854"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "3.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^9.0.0"}, "devDependencies": {"@domenic/eslint-config": "^1.2.0", "eslint": "^7.29.0", "jest": "^27.0.5", "node-fetch": "^2.6.1"}, "engines": {"node": ">=12"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}, "gitHead": "32acfcccdce72dd5e3932935296e3778ce62aa7d", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@3.0.0", "_nodeVersion": "16.4.0", "_npmVersion": "7.18.1", "dist": {"integrity": "sha512-4AefxbTTdFtxDUdh0BuMBs2qJVL25Mow2zlcuuePegQwgD6GEmQao42LLEeksOui8nL4RcNEugIpFP7eRd33xg==", "shasum": "3ff551c986d7c6234a0ac4bbf20a269e1cd6b378", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-3.0.0.tgz", "fileCount": 5, "unpackedSize": 8076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9c2BCRA9TVsSAnZWagAAt2EQAIfKoQ/dfLTls4JzxhM8\nDdvCvAGhKPOIhjFDyktiIAQGktCXX5RQ11wCGfDNqu5Q/NftPxPOUA//+c0w\n2TunmS1s8lQlXj4So0d74e6nUMIeFYVedexCjvSkc1yJhZCy0DCTvKIzvw2D\noCumiwov9Rrlyyirn25QAO1AJFo1jG7X9saSebxApsZR80LrXnIFvIJux75j\nGjuBKDyT/VnT2wURGwDW+tQwaUqVBnaLQfy+GAetZhoN3oLB8+TxEmNKt69I\nH+ZqfAlumRqnlx3iHjO8bEE0KlAPERj7zJzYeJw55A0C2dqnXpFLCvA5kZqJ\nLESN04eNPbd9wX1yTAzNtKiQkHb4KOCD5AYZG8jsXYqzK6vNFKNAj6WklB0O\n91fva4bw7ZAwfeGOTkb5BYlPDZvHUriSG5fmfWVri5rB483Ym5pyHfmNwICJ\nzlAN8lJQi9BeGzC18Eh6IC9C8OBib5EBa42Q6NsSoktk/yXV+oh5lOAfRNDn\niROxbc/FIcXjW2IZn1Q2Wlvc42O5XP759jrPs2jxbcFkXnOO0hj4gAdwayU2\nc0ukya2NLt2ylOl0/iJ+H7bMSLxIF9Xqf1E+welA8xwk9waZn1QO9sSX2pzD\nxMP3lIQviSjmExiqF+XkEDsnXkIlo+haEg1be9ceT3QmpMV7fQiwKnyeAaVq\nSSNQ\r\n=6DZd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGY0fe1bz3i0rGHZjE/b2FW/zSwYS3Crw/xh+QiJqLkhAiEAnRqVm9qJSU2mwzwdsGgQcphAlkIg5w2+dIYc4Iso7EE="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_3.0.0_1626721665395_0.1851254701290712"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "3.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^10.0.0"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "eslint": "^7.32.0", "jest": "^27.2.4", "minipass-fetch": "^1.4.1"}, "engines": {"node": ">=12"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}, "gitHead": "fb6402d4529a23ceeebaa82876cf6d473c3f936d", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@3.0.1", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-Ds554NeT5Gennfoo9KN50Vh6tpgtvYEwraYjejXnyTpu1C7oXKxdFk75REooENHE8ndTVOJuv+BEs4/J/xcozw==", "shasum": "597fc2ae30f8bc4dbcf731fcd1b1954353afc6f8", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-3.0.1.tgz", "fileCount": 5, "unpackedSize": 8069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2nsyCRA9TVsSAnZWagAAo/EP/0j8luZtw7Lj6Ia1OYIG\nI+s2r/0ZLdY+UJ43AY1ynxm/X7+gwg8k474evWH+yGqVVpaXlYpAREUkyvxV\ngsZsawmpxJ+5849ciLeidoz6ilhNu2GGYwSkimPFT6HDGCmLzGro7PPcTRzF\nLqCjqZgdqDC8f9BFXH4LvQiMa8ebf/q+40ha6TJFdP2YlMCXt8DdmLtx/j10\ncfx+G24m7X1zZzgZDf834PKKI7/uumnK9ug0TF4NL2YE9c5fz0DimOOZTugL\nUjdpQofypxrjY87Yxbbbn/qerWcb1gftdkl5zg9EhHaZNgNs0dH2HPLP7vIT\nI5pJVE52yEWvhiIi1wO/WYY7Yns4XTtUXNudUvk8BJLLrdZCDIeFCLvURgqG\nhBu+w5VUMhxytCR+U5VgJVpm0L21+ka4Scblv/1xZklUhUoTdQenGlYiWzHU\nwhxlk8ZPBSsx09Pv207GUdlE03qjr/bjZJI+1O9xu2Too8wlUD89LRUZZykd\nTeU7ozIFUekekRiFlY6gfwxRAeXuL9Xw4L3nXoFqrb1Fmv44pEjhSMm76epA\nxOt9povW5rsbxn3xHugNIWf6XuThqz4CCEn39GfKNbK7DpB/4/fgujaI5818\n8VRqKJZlL4WssifB0NwwzCkGThqMM5n+oWNjCnRzxXZ2E9zbQUqffO7lMH6A\njbIB\r\n=0BRF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH8qr/hWceqlklsR3GPVSa1WZkls0qbvCSoO7xqnTEbeAiB5kd5dzEMNgGUPdT+ZrOAy01Tgr+79suz/+MMCgLtJ9w=="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_3.0.1_1633539780228_0.7045257317898264"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "3.0.2", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^11.0.0"}, "devDependencies": {"@domenic/eslint-config": "^2.0.0", "eslint": "^8.14.0", "jest": "^27.5.1", "minipass-fetch": "^2.1.0"}, "engines": {"node": ">=12"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}, "gitHead": "f061877a7d517b3160ce7a7752fee0262308886c", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@3.0.2", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-Jy/tj3ldjZJo63sVAvg6LHt2mHvl4V6AgRAmNDtLdm7faqtsx+aJG42rsyCo9JCoRVKwPFzKlIPx3DIibwSIaQ==", "shasum": "9cf24a477ae22bcef5cd5f6f0bfbc1d2d3be9143", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-3.0.2.tgz", "fileCount": 5, "unpackedSize": 8069, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDapTmcpmhVYa1kopsMudETbQCH21bxI/XQA2W/a7ijHQIgdKfZWPAXbjgQhw5esqG2vsvRN5aeHGrbAlyolPTTt0E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZe8bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoX2Q/9F7Jav620sOiyaITVnRc0VthlWi+URh1SFxni+tfoamOkfc8A\r\nGAkKqtj2xlVRLRIl0FjAdn1fhP46OPumzujqhwRGzWI9Kv0C+bc6GFdmGzHu\r\nP+s6RHs84UdkQgdD1FlJMV5ApI2wPG7Yce5CMZPrAy+m5FiAyv/4ox6aZQP6\r\njtoRv6GXPdGkDrbL+YIYRfs/xm7Psrr2rZWUtt7MkJKLnJdwpKAQXgoXsUW/\r\n93N6BW6Ec/zrIjQGlp+m6gw3LmRNsDQBmGAuOlYknWoOJLC05eSoUpWUeMS4\r\nWLZkmnTGNQtyoOzRUslk4OiOSzs8HHMAYAvnUeE6FtzVQe+B7AhlxTniKLsn\r\n1gNIilZdMiGFf2493lcFwyQlSJ/DI1QNE+yrOu8rbdpPfLjSAcZHuZClweDQ\r\nlIAGqfUb//sb1YcB3adZ5Uj0KAuDBIrLXrrpwbqFjerhuk/vrG08DololgW7\r\ncyHjbSGOBNbSa0H0G3u7aPfcOatXQzRmVe4q9qYvY2rG7KyTWuL0ahTmLcw/\r\n92BbgTmmywFq5fEAS9iUmyHeASpWRQPegtchWMoxoZRIAl1UpacxEDmitL9E\r\n3uRG9cLXoNy/o3bYcyNb3/tf0iPqoBChmyoRB8suOTp0Eg87SDC1Sx/4xT6R\r\nN84COIwPRrO8DsG/5+667AoABR2Us+G8a3Q=\r\n=Bqdy\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_3.0.2_1650847515533_0.35206335376716824"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "4.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^12.0.0"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "eslint": "^8.31.0", "jest": "^29.3.1", "minipass-fetch": "^3.0.1"}, "engines": {"node": ">=14"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}, "gitHead": "55b4e54384921231893545e2c592afdb5685a986", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_id": "data-urls@4.0.0", "_nodeVersion": "19.1.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-/mMTei/JXPqvFqQtfyTowxmJVwr2PVAeCcDxyFf6LhoOu/09TX2OX3kb2wzi4DMXcfj4OItwDOnhl5oziPnT6g==", "shasum": "333a454eca6f9a5b7b0f1013ff89074c3f522dd4", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-4.0.0.tgz", "fileCount": 5, "unpackedSize": 8069, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyKMELmc5FXfHFelJBCVUNj/lusuPttsIPQjLZaHj90AIhALQ4IzNfhD8fftMKY9+GLpjI39ao70O7XH/LanKXrmWj"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs5KlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnYQ/7B9QSZd/A3y3XOqcUOtrDAj6+obsFozGDytRiwO1W0S4NVlA0\r\nQjqV7L15JRh5+7TkwmQUNKSWneTqnjvwXYZvUgI0maeVNttonW75npAVcLvq\r\n6e1KzMknCdrCfUMxnfrWUaR4v+sz59eJegUYehAWNoOOUQxhPFCuE+fSpWf3\r\nVHwegwo9ni+O0yRrjjfvzvHbvHHSPQsBin1+n29GJ4PbchqHb7IALhKhMbST\r\nKN2zYYvtoHw9YAEs3NmYM421sF9cNHTY3ofsYBFSZ5ulJ6re1zGbtr1wolvU\r\nJHUxQggrMpwUGuSCdmOqyCe7KTJ4AhC5Kc04qoFCe85sPnaV5ZuYV/U3Uivb\r\nsSjJjPsO1oRejJA53QZp8A0kOfiguuvF3QA/6ItPmQsh/j5+x6UOltZmun+e\r\nAJLI95H486LBEms8k+G9mF+IcVjTrVgSRNaG+SRT/D3m+i6vPq/JKnOr2O4i\r\nyYX36I0KNL22GtLoL/SsU+vsYtCKYw3B6RJVqt5llHjXaE/skoHFvvE7+8l8\r\nQ6k/5fpVKbCMdQl1fQzCgTBX6TGf8s54y7SdDbbWJaHehPeF4wHHaDKpEio9\r\nbZK+NyWLcw5D7Ag25X6EPo09BORwPGrgnUaVBvdGgK1/5/7LZ0FtsPuhdWB8\r\nZjNrKPrHkEFOOX88Qoi0xHksny0Ca+/1xCk=\r\n=ZKqB\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_4.0.0_1672712869607_0.6585516810430649"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "5.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "scripts": {"test": "node --test", "coverage": "c8 node --test --experimental-test-coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "c8": "^8.0.1", "eslint": "^8.53.0"}, "engines": {"node": ">=18"}, "c8": {"reporter": ["text", "html"], "exclude": ["scripts/", "test/"]}, "_id": "data-urls@5.0.0", "gitHead": "3ee6d206c41247593507d3870accef2c9e950e60", "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "homepage": "https://github.com/jsdom/data-urls#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==", "shasum": "2f76906bce1824429ffecb6920f45a0b30f00dde", "tarball": "https://registry.npmjs.org/data-urls/-/data-urls-5.0.0.tgz", "fileCount": 5, "unpackedSize": 7834, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiU2fbaou9VtyS7p/6ablKDzn1/3DOHa0N0P0QIn6lbAIgZbTSJ5+0F6xnQ+ElkHwERVGq8otpopWl9cBtGCQWJfE="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-urls_5.0.0_1699691218155_0.16610002353173003"}, "_hasShrinkwrap": false}}, "readme": "# Parse `data:` URLs\n\nThis package helps you parse `data:` URLs [according to the WHATWG Fetch Standard](https://fetch.spec.whatwg.org/#data-urls):\n\n```js\nconst parseDataURL = require(\"data-urls\");\n\nconst textExample = parseDataURL(\"data:,Hello%2C%20World!\");\nconsole.log(textExample.mimeType.toString()); // \"text/plain;charset=US-ASCII\"\nconsole.log(textExample.body);                // Uint8Array(13) [ 72, 101, 108, 108, 111, 44, … ]\n\nconst htmlExample = parseDataURL(\"data:text/html,%3Ch1%3EHello%2C%20World!%3C%2Fh1%3E\");\nconsole.log(htmlExample.mimeType.toString()); // \"text/html\"\nconsole.log(htmlExample.body);                // Uint8Array(22) [ 60, 104, 49, 62, 72, 101, … ]\n\nconst pngExample = parseDataURL(\"data:image/png;base64,iVBORw0KGgoAAA\" +\n                                \"ANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4\" +\n                                \"//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU\" +\n                                \"5ErkJggg==\");\nconsole.log(pngExample.mimeType.toString()); // \"image/png\"\nconsole.log(pngExample.body);                // Uint8Array(85) [ 137, 80, 78, 71, 13, 10, … ]\n```\n\n## API\n\nThis package's main module's default export is a function that accepts a string and returns a `{ mimeType, body }` object, or `null` if the result cannot be parsed as a `data:` URL.\n\n- The `mimeType` property is an instance of [whatwg-mimetype](https://www.npmjs.com/package/whatwg-mimetype)'s `MIMEType` class.\n- The `body` property is a `Uint8Array` instance.\n\nAs shown in the examples above, you can easily get a stringified version of the MIME type using its `toString()` method. Read on for more on getting the stringified version of the body.\n\n### Decoding the body\n\nTo decode the body bytes of a parsed data URL, you'll need to use the `charset` parameter of the MIME type, if any. This contains an encoding [label](https://encoding.spec.whatwg.org/#label); there are [various possible labels](https://encoding.spec.whatwg.org/#names-and-labels) for a given encoding. We suggest using the [whatwg-encoding](https://www.npmjs.com/package/whatwg-encoding) package as follows:\n\n```js\nconst parseDataURL = require(\"data-urls\");\nconst { labelToName, decode } = require(\"whatwg-encoding\");\n\nconst dataURL = parseDataURL(arbitraryString);\n\n// If there's no charset parameter, let's just hope it's UTF-8; that seems like a good guess.\nconst encodingName = labelToName(dataURL.mimeType.parameters.get(\"charset\") || \"utf-8\");\nconst bodyDecoded = decode(dataURL.body, encodingName);\n```\n\nThis is especially important since the default, if no parseable MIME type is given, is \"US-ASCII\", [aka windows-1252](https://encoding.spec.whatwg.org/#names-and-labels), not UTF-8 like you might asume. So for example given an `arbitraryString` of `\"data:,Héllo!\"`, the above code snippet will correctly produce a `bodyDecoded` of `\"Héllo!\"` by using the windows-1252 decoder, whereas if you used a UTF-8 decoder you'd get back `\"HÃ©llo!\"`.\n\n### Advanced functionality: parsing from a URL record\n\nIf you are using the [whatwg-url](https://github.com/jsdom/whatwg-url) package, you may already have a \"URL record\" object on hand, as produced by that package's `parseURL` export. In that case, you can use this package's `fromURLRecord` export to save a bit of work:\n\n```js\nconst { parseURL } = require(\"whatwg-url\");\nconst dataURLFromURLRecord = require(\"data-urls\").fromURLRecord;\n\nconst urlRecord = parseURL(\"data:,Hello%2C%20World!\");\nconst dataURL = dataURLFromURLRecord(urlRecord);\n```\n\nIn practice, we expect this functionality only to be used by consumers like [jsdom](https://www.npmjs.com/package/jsdom), which are using these packages at a very low level.\n", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "time": {"modified": "2023-11-11T08:26:58.569Z", "created": "2018-01-31T22:31:54.529Z", "1.0.0": "2018-01-31T22:31:54.529Z", "1.0.1": "2018-08-18T21:22:26.198Z", "1.1.0": "2018-10-26T13:05:29.091Z", "2.0.0": "2020-01-02T20:33:59.814Z", "3.0.0": "2021-07-19T19:07:45.540Z", "3.0.1": "2021-10-06T17:03:00.337Z", "3.0.2": "2022-04-25T00:45:15.708Z", "4.0.0": "2023-01-03T02:27:49.741Z", "5.0.0": "2023-11-11T08:26:58.320Z"}, "homepage": "https://github.com/jsdom/data-urls#readme", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/data-urls/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"nsfmc": true}}