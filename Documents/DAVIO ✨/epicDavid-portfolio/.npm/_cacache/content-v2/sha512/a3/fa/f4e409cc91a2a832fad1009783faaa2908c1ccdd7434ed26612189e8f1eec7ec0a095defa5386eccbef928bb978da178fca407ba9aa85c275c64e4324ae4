{"_id": "babel-plugin-polyfill-corejs2", "_rev": "45-c5dd24a4dd10264a2788c7226fafaf85", "name": "babel-plugin-polyfill-corejs2", "dist-tags": {"latest": "0.4.14"}, "versions": {"0.0.0": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.0", "keywords": [], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2530bb6ecbb42661d244329b5ba8f6f538f2a393", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-rxKDIdH38web4lBKwgCe3iPeQPqxqkN/otZ7DvUOyWxZqWr7yLBRi/L1HUVRAnO6B/39lED2yUyykjnNzxdJyw==", "signatures": [{"sig": "MEYCIQDcfI4/vy/+uEGvzjZH0xfJusOMQWPnZ688cqUm1B+1JgIhAOasMJzMf8RsM8p8KjZxDXjTNA9yjHOCA6rz9DZV48VL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejd9ECRA9TVsSAnZWagAAw4YP/Rjtc7PPYJu1VMbS16gq\ng557vNTnlshEFZdABCYN2u97K0wZrS76yZUf1P1B5p5CyBCYSnmE8dlxRezW\nDj3MRX+Stlk353SGPKgBITpZUr2pYh7sUM4/u2wnRksh1cj7VgmZqowEMZi0\n2v4CRd7N+h19fJ2LELUCBy7xFyXHUnDf4GPufBbguw1yBM4cyInFtxf4itwp\n5yCFaoqY6j4VgYkb/XNQHZCgQPv/z7UMzN6Fz6zpPHdLqUP16X84ybWkiSVz\ntcIfHEMjFMsXOs/IrslMaXUGwdnSyi7xU5xCeJO7NKlGesZUOS9x2Pam4T4O\nIMHiHKg4IuecjRCoTyBdhKAPn+X7pdJv62hzbV/mEBnEFiLVerTcDi2XYMZE\nDfXOEiOiLtWuT03PvGsv39ZMBcAaS0zMTIXoX58E8xE9ic2GX1ik06Js16vR\ng8as6JXQwYLZOfy8H3MLbEE6TdZJQv/Q2+h8bfRNRMsphQLIMFpEuRsQdgJg\nLYZHfWdyZRXftGhQtkeWJDBpi8DiHWgcq1S/oUSwsm2qWHndGSwkpP74oOZm\ncVWht2erLeTjiTqiN3Ug94FAqAQTJ65byuFGyzVB+vHnkaBGNJQMkyUSFRxi\n5ZdYdpLyq5j5L7tPhK/5pubdu7piSyzz+rNr88Hu5HeKtVpJsILZA73H2nlW\nxAPL\r\n=s3Sz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "_npmVersion": "6.13.4", "description": "", "directories": {}, "_nodeVersion": "10.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.0_1586356035557_0.21909201133335565", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "c1ee5215e1fd318c255cea59a40ec0e2315236a5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.1.tgz", "fileCount": 7, "integrity": "sha512-a5j+rhrywVmMx54xraT4oput9TFJxngjKPqHFz9KZhjWo3RnFgN4a7iVWesPbLeis5Q+9QIPdaUgc89BbZW/jw==", "signatures": [{"sig": "MEUCIAF5ETzJ/jqlHXHmCZqdvVGUuX0+glx2YoekABLagFmpAiEAv/Rlk59NO6ZUmIMeltNpz09XDvFb6HvbVdoJjvInFbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezX8xCRA9TVsSAnZWagAAUkEP/RcZ0FGhfB+H3zZcvsTp\n7CJkIYyPFO5ZoG/IoQBninKIPgAK2qcb/rB+f+JlsiiZITc0qRsoauYV8toS\nz1YOJDTiGpfr+OAAE65gBO88YiM8WOWC29z76v+I3Da6ztERDtxfjLOILRJa\nNyZVtuIv7d+TTaJWtP32LwGm3JRJv19WMOjoftx8qBmeHN4iiiAmpQRQNrrM\nLIaQC/7evQS/vBfh4LqZ+78mPrTQk4rhq+iuhdPbF5dvkzF295lHndt5yCvo\nnUFgRTrP7tr0Ydm9JjWVaiLqpUSx6vbpz0P7gzVVh9wqQstMTRA9HOOF4Fm/\nNSbKpvnHw5IXeSWci+jX+KdWFdIIFR3gIYHOmomIbzUsSVLf5doHZgg2LFqs\nUVd1qD5UL8bG5Bmb9DLLuBgpB82kVjpPVbFLzF8PUsMzS6IoSrnZWvZtbTJc\nhH0hd10MvgkEs7uk+XuQ7r9fp7elFSZuqMLAK5dybvt2yn9pxhDz0TjAGoMJ\nifem4XkfaAVZYGs3HGQKHqYQOA0H/gx9hddhn7Mg3Pn/6hvZpg7yOv9/QmYV\ncDvpRD8PBmNQBhg38SUVFefPL46w82VFffnhhTVv6EUkvqOyaTBq1ypeLf2/\nvaVpMIKce+irapxk/p5wm48FuSW9DtNXQ+TPPCt/qRlqPScyzn/tuJs5z8tP\ndjxH\r\n=vZE3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "82bf8ef0c6f375d290434a1c083983f0b8c6dffc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/helper-define-polyfill-provider": "^0.0.1", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-define-polyfill-provider": "^0.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.1_1590525745110_0.7780458400075438", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2217f2864b240b94c0d0424413090b77b49a7bad", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.2.tgz", "fileCount": 7, "integrity": "sha512-9Dbu6zhRIfUyaETAznFVJiWCAKi/j9WVXspdCTMtwA9aMM+Oz6JKopIf4XFBd/4vkjKtxSgMt61PWwwY65oc8Q==", "signatures": [{"sig": "MEUCIEkyE94vbE2oMueBwQPG24qADQI7Sf1DUzCI3Bc8Q3HbAiEA6TM00IvLkzhAgFtGWpaMMXn5XX+ugHyCSeYFn6fAhzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezYOCCRA9TVsSAnZWagAAbKIP/i98RL9b0rnfE5UYSOo2\n+F0lPE5hxYwTYEOIIMpZZnm8yU5nqP9Wyu0zP1t31ahAy7zOF80zT0l0dtWm\n+EWKeDlX7+OiJPLTWO+1m2rejnoO+XtQP3ZwPoHC+/MoWuA5J989zh1a6fZc\n3XL/vnVg6bh0kTKDdOLLasO8lRBiKGpLu6RDZKN5fFp8J80evH0BqpH0eROh\nEZ4umligGjbgLWr6b7Qu6zx4EEArCpDiP2/hAqK7LucIqK1MoQdKaIaUYpZ0\nXK6zSU114cILL+fgz0Tht/b8ILNmgqi2sMwSeqXVCL7zlDYi/MUaVfS+E29U\nGXBZ9aXaZJiwufaLAB7NVQnqW+tBx7cmrgsG3uIi+8wUlZM9dlLTyCQ8+Srm\nIOrSaE3JGlGTNi69dpsHNeWOncZtreP7N2gJKovn/7WnJWmGmzuDo1MPf+yA\nv0DtxeiYDM1s9A5pMMoaraZzqxIel2b4TwZ/KqxthjP2f3guXZrND7cnDx4g\nW7VBYgvP37amXpVHQNELPRI+RGA8PD8jNTI1/Opv1T5Bjhv/4t07tVb7MpgT\nb7I7L2od0E8fMRVeU7p6q8kUuFJtMrBuZsyhl7LN7rqPnmG8ayQRmmXfT7CJ\nlnu1+RAWFrynaLacYAr5raIzoH6Ov/y4br2VHP1gHV7sRt16/ZVE6F+toV9K\ncub/\r\n=7rMy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f014169a707ba3f7bae1b38ccdc834f0b904b4b1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.8.1", "@babel/helper-define-polyfill-provider": "^0.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-define-polyfill-provider": "^0.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.2_1590526850555_0.10750836273300379", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f49aaa8e592e38ae93aa2aa7fcc896d37a647af8", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.3.tgz", "fileCount": 7, "integrity": "sha512-x0ahkBH4SNGSIaagSnms9FUW7vYorcr+jB43QTV5l4BDyduW3wcba78Cn8PzIV/u+z+B6FAGAxvuRO4JEZlbSg==", "signatures": [{"sig": "MEYCIQCYJ5b3nLBUvfE5+tKR4wWWOIs37dEQpz7DAWh/tlogbQIhAOncy1p+1gICYsxTBaY9ErQWtXXfbXFEYoj0HBCEBqd3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCN9ICRA9TVsSAnZWagAAm1oP/1YNbu7q6MMurCNwqjcI\ntiaKdpGAUbbzzVpx0K/zhCw4z2xQo/arwm4w3aG8czpAfSC2qmq+x22GE/zt\nKo6QBAN8aRKfCK+x3n//q0dZYItSlhSzagIppF/5QPF/LzW541hYtiKB4Qsd\nVGuzzXh0mmBnyRIrFgdcjN7935b+4Ckh+624rU58vghS2fzjYbLMIlQi/Hsr\n04b9WbR+sEH5jf/u3P6fuTj/xaKxxbP1O5rzsOHGfcbAIzxEFpnBYvHM8dnU\nSwrpNvT2EQXornuqmf66RS/zTI0UGTMW8MPS3dmOseL1z517YUhGVciTK41F\nMqJ+/I6zEk7TGGXCIOWVVmUNmeNIygIiKdF2xLYLoydYuOf/p3aiWLlPIJxw\nLAXRZvdcWoUxsnuSQlGnlYCBGq/EA/aE43IEW4QVbBoEzQQdEjrzTRvqbspM\n4t68Bq987+oZeChENrFwpkRY7bVDU+BIp0SGaI8fAJd+os6gHXWNzkB1uEqk\nVPondfFkqBxpUfn18fNmbOpXEL/eXTq78cHdilYGhG76RBIRg+uzUMHflsrg\nvuOk3yC4/0GpiccDfY9dxQaFPHxPKSRPjiXQQCvakSCd3v1hlThlZniNjN3Y\nqSZku8vys8Oqqbmn0YSIwp1uiIXtbsbOX1fUYhSuDBalyaxd5jpArgidptcE\n+rO/\r\n=NWq3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "884798ac0f8dec9d8a4b6fc18967acdaf6596836", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.8.1", "@babel/helper-define-polyfill-provider": "^0.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.3_1594416968128_0.7402763575041178", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "4ff752c9d19b8db4540ad9e7a629435f933793f4", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.4.tgz", "fileCount": 7, "integrity": "sha512-9qkLFw8MOdoKP9ax6qLh1s6WP9RUrKThl2VMrLXcIMJeY5KEbBJ+Oj2Ol0aWNO1DEjCzgT9Uw6boz96r5Hjf3w==", "signatures": [{"sig": "MEQCIGOz8D2MfXIOjJ1cjvH+uZaY0rNOKCavYh8oZ5bkuX1vAiA6wubnNJ3MdncerYJSuLg0TEhUfwyMI9a/Fd8jHhdNCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTYDiCRA9TVsSAnZWagAAkPwP/2fOvm/5fDVlMJ1K2EHv\nuvXoVPIowH8DU05UDUgZfb3PR/nEpxmeyYcORGBSqY+n3lKFLhHJLig49a4X\nS8zwNUDwmT79sTdw3sec/hiwUVYILDOCUWK3G3h45+Ufco92/dPquyOT44yl\nVAtSSTZxQnu7SfHwzYo7TCkaill3rZJONXOxbwxYn7MgfxLtXPV8hjsAH5pn\nAdyHCS15S5r2ojhdg6DdpxylWpXJFlzWhGG5sMcEcMMlPhHvatyhFE9QRiD5\nFGs42Rv4aaY45pdDGnebM5Mfm9h4KZBWRpR5xqmd8o/+Gi7ds+Pr+jDzvGz9\nHoaoT0HCE5IeAQzYtzu8br3n6/g67pZRQiElXVvbmcxewpn8ZdL3MFTQitVT\n/qVMNPAWFAPmdz+2BxqRmCoinkWj7yI9s60oM+4zvfXRHR4cBooaycbFVqIl\n0O6PpjcrGkpAyeGKfAgSxEHKp66cZfbps8R9A+varvwQQ5SUDHUlFJ/41zRh\nKRTvaGQaQ3jTeciziGDqZkfzfBRXvGgNRzfgYkYbUftOz8EVNinSMvYl9D/M\nl2UkVMVlbSTUD30/KlHnnYAb8EZqZDW0pBBUCtk+SrS2mjsLiN7nBW6Q7qU9\n5SrmI1FaTau+vcjpBg40eEkOAAIr1CMpNsmSUQ4G+HOtHqAAG9TDbMZ8qRCy\nVnel\r\n=LgcQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "84d6f5b367b9044dcf25d8539a638531effb5add", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.4_1598914783514_0.35381483935937275", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "09407ff54ffe89871a012360f359b5b1e44d4777", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.5.tgz", "fileCount": 7, "integrity": "sha512-Ahr6llzGQwf9CsqjKPV/gNyTfqPPJLi2s4kh/NC6oeF4kil2apsRWXFGOanbbZY8d9usteFmlZaC1F3u8AEhbQ==", "signatures": [{"sig": "MEUCIQD/7KxytTH/GPD/aLGvrwuJ4nTJA/t8DTFyZr49f8qaiAIgILkABxoCmuh9JWKzufIhx+/wUrVzzA4sPZZVc6OFVjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeFhrCRA9TVsSAnZWagAAjl8P/iQOoM+FQtg48g/XVYdK\njIYD+/renRt+UKCil0i4CFi95pOn+wHMiJO5OqzbDMpSdaDpu6DvSwE2sAE1\nC+LnRx6LmC8445Djs/mqnDj+tcicHfNPlO0sOPCkwOpbVEMWyPT/TV8xHu1S\n8uy99L8wRcLQZmzvRGBQgpHtOjoYUSMQeP8pgVe4Il7mPQg4jMrm28XqQRlJ\nlxkL5Xgg8m5mmfjU7pnoLyCIxx1FijaWcu7H2uxV+mDlsPOE1o/EhKkUUWf+\nGS65PQOiS+uDnRfj0YN9RxROLNh6JP6HPw3LSMvJEorZOgcbDIIoxaJfqGQU\nTyCTqBNGPmn2a335zPK0/2VnGs6YBEk0UTl5L43pLHRyuqP41hU+ntwTc84F\nR7yGIpQqmWszOuc2GgpAAvV2PZ2aNBzCAYWpAujvvPLBsy7Ov0pEfepgOlsP\n6zstMbAWfP/tJdHrkTvUkO4VqxiF1VlhgghouxgtptcUlQgkiwGSlzXNM+Bh\nWmYBhou9AD/6/I1IeJb67jsIxwPv54dbTx665TODaEdWKrtUfbH1BpFv+mqQ\nnGk+oiwS6WkAxSN5Va3LBSpaNddl86ZTW1RTD58p2q1BL7GOfw2jDl6EXopA\nzK4holGRCTflD3+G5lY27ZwmYJEGqZku1rPuddPGVONmH+bJhy6uVsMdCb+a\nVlmg\r\n=Ce4g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a3e08229b65e7224eee3d822322576ec922a79f9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.10.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.10.1", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.5_1601722474954_0.3480833618176351", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "69b53f99d2afd8accffd2f54434c92d25656af7d", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.6.tgz", "fileCount": 7, "integrity": "sha512-PgmZh+K4M5kppL7n7LqiqVbtP2xLrC0Xp53cjwky5QvMVkqwb8D0j1n9bn2ak3zQMu7g6Tbd8nZZP5czZodEJg==", "signatures": [{"sig": "MEQCICstQeOKiXBB0i4iywQJFUptYodNXtVzWtL4PSkHS+DEAiB+h4EByKXAL30GqhINmD8m0Q0bxWxYkNZQjcTPR6pgzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqVQpCRA9TVsSAnZWagAA0B4P/jXUgfDCdyPPGAj+XwaB\n8zO5x6o+UW7q6mU4uiIifrpeJPbgPno5AAQsuALIUKYN/YXhRKoxWQsBaTH4\nC/fSm5lfjbE4C/VWEbqIC4OSbmH+6wt8IO0Jnqoa+PF7RkfNL4fqdg5jrC7/\nJQ1GXSy7xBisVH7kV/pYmqZ+dDq4mDrImwbK2h6yzgGtUApHbh9AhlsFXOPi\nzKDPVDnKtAUJkUoLWo5ZbdC4V91kEhuH6S+cUAhFahAXj9xNLqZulqBxID4h\nPi/Btp1zNyx+U5Ezof1Bf5T7bJZFCGVnHn7s8+F8jP3AHs1I5GGwSENcSEYS\nwC7cV6O7c6rPO+cFUv7bURztgESjBWIpQgc4GRTWDyL99CAbeWULy6mbhkwk\nSl0M+EmpC7I4W2azZ4DJFYzcIZ6lrYoo/M5f7L3iq1hclNSMXbg7etH5jkme\n7T6aJzGSyFpiq4OX+UAIVOCVKr3lax/Zziki+6mHw2KztWlxyv2QfRFgcLvi\n7GmhdBOeI3ARWnXDo/nuogf0873RNOvVf4skR76RPwSdA/RFNlgggzk8Qg+P\nYLHCqdVEqiV/2fUOArKfPFUwiEePPQXlT0vsJGOkcUKkqYVpAOk1+2tznBhq\ngUgQ/vCPyFK7DiMaetPm2LYknHb7Boa3kRI3dAZYrHBg+BhA0dvUd1A51BPT\nuiRR\r\n=8gi+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ab1ebbd308b1111920327d95dc4c66a5ae4518f5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.1.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.1.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.6_1604932649402_0.25273008939808594", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.7", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "23ceee29b51c59188972ae631383313d40ef28c0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.7.tgz", "fileCount": 7, "integrity": "sha512-ZgAd4g5+o5GmPwRB/m2CQv6j0mfT4CSsbdnxcbtW3yF6hwUQbNt01N/IYfuaFzy87bV3ah1tPApicf0OXOcPkQ==", "signatures": [{"sig": "MEUCIBxjNv5MGOZ8fneEIoh0h/UKGFpxgNHkMAKfrEYEH2XWAiEA6Gj6x7My1V2xy7QUfk2z7OmW5tP0eXsPa74XB25wFnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UOfCRA9TVsSAnZWagAAAKUP/2YfkB2Q8gay99FW4BYi\nPdUY9QDetbNns9FtwSFOc+h8W2zym63i7oru/5SrB5JuNXywcAh1PXyYO+Yq\nfFZgbjBBi2nH7SlxUaK0b1O1pH1it+7/eVZxtIabrXS91IiQlckI3MhgUF79\nOioevxwPzl8NC7A4er2BSgnbz0SfIoUlrtLIMqeEEJP5wPcOmezDUC0sBHgO\nzQbvaNNV9vMzgqiGn353AXiC7HZe0yJOrdEKLdCGkDs2mV755iJ/Ti3TZoq+\ngi8Wk6iw0VbIqFd8EQc8KMSN5009LONL4jwBEyyxAxhej+VwA9HU0EXVQMqD\nastVnrPeKgbJ02a/FJztMYoNYD/ZBo7HzSeiuREVjc/OG6Z7SHpppG+3sPDa\nYnWlHrW7aGjVemyvMLHGCWciJ/SlmjcsDhlAPE9I5E1H0MlN8xO1ue3BWli1\nKfq927E6hqiOtAxP5fHgfXJs0efomxFtp3Hc6Nx7A23LRm4N4QYmTCRbpdL3\neuobYsjnxsoW8Bae+FQ5aHV6E+V6NGvRia62hNH0WKEfUDPIrk5uWUF9JO3S\nIDqOQQye0JeXKy3Sy+Bwuxi2bZhV36Yvbr4UyShDKfFfgV4fvenY+QZ0QoiQ\nVu9bpmu121W3uzG6FaeHWQs1PQyR1UGPuag6CdFWFDvgN2I5r3hRiTP26Cvt\nE7cm\r\n=qEA0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "04e366d5948db64ebe1eae82c3df42bb062c5071", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.7_1608598431481_0.3303149110685548", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.8", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "6b4fbdb31f73f74ccd7e310eedd3b637ddb7ea32", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.8.tgz", "fileCount": 7, "integrity": "sha512-lB2tN5RKr4Efv41Q/XIvUAenk7QqqRizUOOZLW/XldvsI7lmCpnC3lvKiWX7cDw0dnCJuLgIGvEegQ9nAUe9cA==", "signatures": [{"sig": "MEUCIQCWPe6RIM7ub0lVhkmHSkOEQMbUrOBXJhtmhTL8lRgGMQIgOxh34+kCDqIl+meRBcXaVkYTtVsFxAfR1+CaumK8P2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UWJCRA9TVsSAnZWagAAzg8QAJu+Gb8SEKRVzfZysqTv\nNOoFuRKkw3jBE+o502pmmo48jD48/aSuDFQPWsAbIFMXf1Rmfr7LbwowEqcI\n7OdrLQkORxPpCTKL3WtsgMT3eEdLO75cztrH1IkIpXgkQAZB+npLk6QQxbDj\nkoachGhqpqFfgvIw/yPbS9L+S0EZcawa7+pHIz8QgynmyZdYOSb1CeWiyczy\ni5teXthNwwJttGvPVX6fak1N2JshDcEVhVtLHBiuWY5X++Z09b41iawTgf+7\njDN+R9yVZ+zlCRzuevmmjwO8LJX8ZAfjsIrXqU437CLAEjyzNF4GHrWKet0S\nr7BA8WnWQ9difwoU39RSQAR3zJxcw4yoL1g+XdNFkoVLhywi57ThUxfGgy1+\n5dnbjqStn5Uz0xMgmPOY29I5dIqFBnLWivEhu/uOW8QkpkycdYuRzb3DYEmt\n4ufl3NjegEu8fEfP94RLpm0Nw8rzP7xg0suUm5V2XXHMH8kQhIbvVJc3hrfg\noCiZdTwYcdsBDIHZzt5BodzVLN+ZKm0ZndipCu3joxXbUGO1aI+iSBQ8PU4w\n9YrImHOfyRyXEJ7YP8rvzJucwaPdX06/PzRGFuupULGUsqfltEdOQqauD/qr\nHYoBO14ZdNwthQltdqEaQl19YckqzwHjyB5spLPM4RlfVZsvzP2hIB8hs4Fo\nhbPo\r\n=P8QL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "762a528b570a87fad72ebc24e7565b354b4bfbba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.8_1608598921185_0.9268108801590416", "host": "s3://npm-registry-packages"}}, "0.0.9": {"name": "babel-plugin-polyfill-corejs2", "version": "0.0.9", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.0.9", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "3b057ac620c7752014f6db537ff7faf5ca8463ac", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.0.9.tgz", "fileCount": 7, "integrity": "sha512-y+nsTOWXVQbCoEwB/s5tRZVAsGXVyQVBVtMmFvvZs0EPE5hUdJ+EdMq6AwGAOtYbr8jFxuPauCAF1BgiOFGZvQ==", "signatures": [{"sig": "MEUCIHzfG8Em9vCPTL0aURGlpSIjdZbtrqLM9/tu7TpWNsYTAiEA1DGv9yHs2nLP6Viwljj1aENNIAxb20qB9WSZ5zhXDKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9yNfCRA9TVsSAnZWagAAWM4P/jaQFKb5uSG7yBGQQKKg\nuFgMH15nVICoo6yCIKWv5VOF/4ZLDTsIqeXo+z1LMBEImxX4dwzRWKxhXFup\nfnWAZVSy9M2Ne/bUBC3yEs5tKZdVbUsCRehPds7EXGsKJdOncxLd3ahR0Trv\nuNf7XRNFntNY1y+nDEHtO5ilYfrcoOlAz31OBm45az9MdXBiX1NF3LXAjshT\n19wMbMoSCS8nGqOJo2sm65WlEn9Q93OZuyLJcsxbSX4tq9c1SSah9UhyGd/A\n9gMqVvoVpEq0kafPdQqAiTX5tXgQukHlCkS0pQZc0IHsvuEvclTfBttilRdk\nFJ3NpoHXVGH/YolqQZfOmAl2pR2biZGMsvqnIdo7Teilz7GNRIPJhtSEqOLl\nPlKO2GmFBkp8kXNGV4RGu1A+BfrD+YOu/YEo4RB6TsQKdzBEixN5VNKrE6EI\ndyWgU6fwW8CxVmFrdd7/4UK+QQTW1i2RpHYHLncsvFqN7nTHWOrSUVwbbBJB\nrktP29ubJ5H5tFfAg7ikg5JlVAo823N3j9Nx+9/wV0ogTgCdw2MuYYDj4ph0\nE1RDxod2ntXijVnkNuPZq0CU9xtgUCx8AvPt5L8gIe2JVAXVGFttYIbfEg8f\nkrK3btCk+JCw1pIdTdsfx8xJ8goMLmnyVrBJEw5rG+8LNxNTNUeoiSm0f7HS\nHOPg\r\n=32ni\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "bb9300ed54b6cc33e09a89764efbb2bd2949ccdf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.0.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.0.9_1610031967047_0.6339416937982192", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "c619f2e5de9552184376b323145f17235006da6d", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-+7fBEAHH6Q/+aNsBzbUOc0FcW4asPquJsZ2ZGaYZd338HTPL072iFXNs2PsUeVSoVMXkp4FJA98nnsoAAHblRg==", "signatures": [{"sig": "MEYCIQDeLLQ3QmPeFd38Pqfc3K2zGt5b46TQ2Bi0sJ9Qf2hW7wIhAO/yWtSS4TB3ZKEXg4/b+S56Bd51J0gBD2PQCzC6CwFe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+bkaCRA9TVsSAnZWagAAoasP/Rr3ZWK6niTOytackHEn\nRwzOfD2QoZP4vkPWBfGyyIilZ0v4XWAJoNH9VcrfPC1w99grsYavGHM1mjiH\n/IPio5ChTrRXPC33fKTwevckNe8Htz751ethhlA9vjbTld9xF1oINI5tBhLP\n3uqEAY2Dme5Qwi5QVUWaKL7+yRjSNwCGJcK3UEdyNWhd9VZKh2trhL2gqy3l\nLyJz6lAvmXXBWpsimTL5yXgI9FqeTXCHVZRtMoa89RJlVrtN/cPckVeo9yyE\n1gf0dIo1gU5xpVsDvb5LlhFu1Ps9rrMv2rMwHjsClbDWD81sdmDYAhTivj/R\nxnpWUHRN2CYMoFQmjVMUgQ74cvTqhIpNyCSGIne4jPPWJ3DH8WsEvh/3RLVi\nwmKEBlV3OIv5cMci7VQ/FFHAeJ+5A2N6n1FDsMwI12IpnF0sLVkbIYmpMLYd\nv4fUU9SMOwXs9MKeNjkzB/obNhlxioXZHisexNvcQddapkFzmUihlvCb9CLv\nUwIsU8/6ilaHwRqKRjeIbvac7hkWdRmYhxpfjmw4GElPxCs1b9CQWWjPus3M\nTNXtfacjyr9R8uCBv46iY+djy76TlW0PKypkuHlB1ML0dECLro53kVTwQ9Di\nLvXT9ozNIjdVACo/7ofklEO7Xwerh+CIwcmdcOUJBY+yQGwnRLhkHEvkJxGQ\nXRz8\r\n=Zmpd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "440228dfbaff5d0632a02f295002579fe862e614", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.0_1610201369890_0.4731651166344235", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e4234d351139a2b82e87838e48d0d4fa3e6ff8fe", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.1.tgz", "fileCount": 7, "integrity": "sha512-NrR72S1jZNQl/MxuVmD7QSwpE6AdQUs2nnKzoXyuW6FBJHbu0pOAQu1RguLAtxGb9k/9ls1IQ46tSgpRZXgXIw==", "signatures": [{"sig": "MEQCICRsYzWAFloSNzDudamA5Su/ZLSgmaz4wfNenW/FLoNLAiA7m7rpYjNqk8zq8GP5Wwa3Thl1qiKK4jgcwR/8qUg16g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMwFpCRA9TVsSAnZWagAA3YgP/ibgnMjhh/ObzRfEtbwT\nCTg6lQXX8EVlHvyTcZv4J9DVLAn1AnPdROP7rR0v7zmpqJ0U5HNGaE3fg2Fp\n0rLtIxnlT5GfyAmjUJJg9AclLEGLEnBRll0qDEMLIXRDjt0PNpjmLaf+XGvt\n09t7SsLILB1Yk/fAYutfpfzN4Tr0nLg0p1nulK8BF92DAEY9HU1PisG95rmn\n2NVBz1L6D/QDsxfIw4aPZb2kBQqqcYBLK6FL2igY4Di5xwd6gwbSo8MrIaWg\nxLsGDMn+4xzsvAJmOFSMUMPDZDklqqLHohcYzSMzO7KeZM3Jr4F9SrsyjhOy\nHmh8U7/ztydvLJfkCUbrAXm4M2BnnDJaySY6JqR8Qz1/q53Va/PNguN+SZ5+\njFzXS3j55d/hDo9RmdCrK1DjekD5PgxnKUIOZsvyTtlPIzKDngUCGKtdHwFy\noNwy97kttLzw2ArhHV0aw8dkzQOQgg0NdVQigjY80TyiduAt6tB6nMpDmZV9\nkZfnfAtgS2l4Ma1WkrxTfFb0joVdcKDlTudxeDM7ArNcFXjW0zZQwDos5V1C\n/wA+HkG6LuF4wGyNpWLtVM+3PA/Hb7Xo1EsFSuHis23fz/Qn1iWOA8q9Y42u\n6vuTo6/foelrFxuAl4JzQBmDwqvQtl61lhEL9yCmZM9dJzRBXQnyi0lcFG2X\nkVtO\r\n=N1Hk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "57edac90ba049d5ac2386cfaa0be131a68030d21", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.1_1613955433417_0.7392720505455121", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "3e5e7ef339dd84ca34c6a20605e6d4ef62aba3b1", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.2.tgz", "fileCount": 7, "integrity": "sha512-sSGAKN95gTnXjg3RtGI9QBW/xSqXy5fo2Bt+W5WkhconIO+QxuDZChyjZYiY90xE3MQRI1k73Dh/HAe86cWf8A==", "signatures": [{"sig": "MEUCIDHDT9j1fuqQlvh2wCldB1rEXPz70CpHB53V8HwFgcD7AiEAwvmxSsWzdNrwBjWm1hnTW/uvEabwW6Gv5g6l55R13qs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNB6TCRA9TVsSAnZWagAADTQP/3Kac3JgbnOHX20T/ElS\nrMrqlMHVCDCztYAETxwbh+0AmvQqAd+uCXZNrY/hrasr3flp48dLoKEpBaJ1\nCPYhzEywnSPouYmHH9J9QP3nUt+m1v1Duk5AJI3G0ExiovJq8vEOEsqy6zGl\n9RM6xh3JAi+nPtRPtf7JEyYDuTukTMuW+unSnvqtHYUjpeJBTHapQyMyyTOo\nw+V2o3i62y5Mg245ghQecmO5TQncm711wQDsl2w/U3LlNI0d9nTPpB5dwdRc\nPmfeC/E1GJ6Tavg83fc7GrQJikIzrnfbG1UgoEkS5+tgd3q/664G0zfu0yNn\nrDgGiIBH96B4RPrbsTKiaD7p50i3oZkpeQaVmiyXz7JvGUiE3dP9pAFrG+Ps\nzmJw8eKGl8pvzWj5pQjIg490l1Y7sdIKlylLAoTaYksJMzsJOZMSTQgK5mB1\nouytGHqRUwLgOJOcrXiOc3MzNX3rqznm8+isR1PylQXmVRTyzCpvb1FGRYp0\n2mYgbXK89oeZGDn9cmXfXtjkg0iHnoiuWX/9epwW+JZ0NCxC9AjR3jacgXGx\ndGR0TMTsihHS28bkSGfoqeBvLFb4uXtKSFf+F/NSQUlgcX0a/qmnxMDjt/Gd\n+MWQEqMxanpJ9XCakiuPT/nmaRRKDRV6TvBGGqZKlI+AyKip3Ad5kNCHxDG6\ny0RG\r\n=bwvS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b1a6b262d1de56486a2ab662ecc25834be6d2ad5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.2_1614028434529_0.3205186676161005", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "233d011885ddf670420910fd24cefa11999cb399", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.3.tgz", "fileCount": 7, "integrity": "sha512-VETbaz2UPSTW3WBc0IC6vjxvKuVAjgMKdInt4e3Y/0A99+u460iw/y7JdHOUPYnU90tAMCMJo0Mj8huwK/ETSg==", "signatures": [{"sig": "MEYCIQDI221aMb5bHQxa9/kNZINIz5CT7Q1N0skyJSUC5XN3OwIhAPenclgCnkcuc3fPZcSXTv8Y4J23QsHdcXnt2E5eoGKb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNOWmCRA9TVsSAnZWagAASfoP/imfOxhTEYDGRbK+wPqS\nT/d7T1YJRcUaPv0aFnEOTv75meLeZluLitfSoDrDWeiR1twTFZ0+ECOH1YLz\nmmvzwM14wSgjmNzqkmAE35PwCRsTdY/JoLPPXdM2DPfTHEUMfM/OTyY28k6w\nwRJ0R9rp67BiwNzNzmVMX/+kTqAJbkz8ftj4quSsQBLh9TCCjAahq0/mXtnn\nP6EI5flTzh7Ejc/He//HkaM+ILv0THCuiMD5fltrEf6PhtISWXAbYfINaiPy\nJ2B6PjzYkqpRpINefPWPhk+RF/xDg0DcVb13whkD+IEK3TWeT/+gBDugD3Zp\nfZWyC9uSJtKK2SfOMtX9P2uad5qG73eychCGEVtpQVdsmtPzBq3WE5UI4ZiT\nCuOPMlPKzO6fzW9ktCDFQcDAkYL2lXAAJSJPyFxK2ekw85kTutZrwqygROIh\nf991hU7D9j9rRHBPdzkqwMTb0unEMy0+VUWTsvw4ioD4Mvvk9Ijj+qWFT/8z\nUafLX50mh6gc8rCSbN7gwhHEe0aNQoyvww9/qFoHLHq2holHpzdSqiXxqT9e\nNPz7nByoOxDdJVeu60OakD17Nj5ToA2i88dqgQYLHWzTVdq//edoiqxm9eIR\nOMENPwIswc1uLKp4uE5wg+YlwPir5PQoaw0HpnCx2KVchFewpYR+bpZmtyNe\nY9pV\r\n=3A3h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "187944845010adf2dc1a673af48d0d9e6d06d6b0", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.3_1614079397747_0.5579610274511291", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "4ae3252b3c1f369caea30df5e483af86bcff777b", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.4.tgz", "fileCount": 7, "integrity": "sha512-Iju2mcwieZLjCl5s0jNrdjf0Z1HDidT8zDuVAh9jT5lN+p6K3KgwY5vTv1FWNklCSLXS+UH+C4tPBlEKbAfB5g==", "signatures": [{"sig": "MEQCIAf8wPX5I2hPMF9TH3C8AE3ZHo/KIcX2q2Pm2TKgZaEQAiAahgbwM3oTQ3T7bnlEwOoq1cgaUqlTqDyD6pvSao4LTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNOpMCRA9TVsSAnZWagAABKIQAJG+uWkZwYSeS+kUery0\nooWcQ/X9DcMYDCQm/EHbkYPnWCMfCFgSoiBhCCAw4bFjtffztK3uSCVOcrYX\nNH+KZu1955mXz3erXdIwsxlVTlmGmfMjeGG8XgA+/s5BXTE08+qjJm0PTR6D\nhko6Vy0rw7sfD0QeclOyweWgYQVHbLuTSiwWesXggeu3Cl6WYCOV5pRJQXK5\npiI3fvV++37CN5fy9vA6lzrQ1etKHDwRoEGFdXrclRe/RFfQlz9RozIOoRaS\nGy4rUTAtt3X3Pppi/lONRcrkq9wrcvj1lf+DXDVPdBt67ZmPS8fT3SBp0hix\n5Pz2ql2/JfMvD/qBXyvnJphWIcJf1gsveoX2H+Tyq095UaG8xqkTBBcN3Aaj\ntxJ87aUctPiTsoG33lDtJ/vyZnmTvkydT5M3QqE4G7UASaD4F/3JNS4G+5C7\nslamRicBIwF4JsDSUsyBVwy4Uc5bUX6puDWyXtshri4c/VNBKQBeBtuagK2N\nAFvYl6yC+vwVjJJkEF0WNS6Nco6zL34j/CXW/fSg9vkhvChScWxTQol/6Kn8\nwVAlBNB9yzeNoWyppvtGk6S0GIitMtWiuza3go/Eu8Q7e5DzRM05D8sRR+0r\n9IsG4XPkyFOvNYE6Dl0QQDmTDG3jIYa/hF8k4mrvf1Ci0HMFnPrpMDtjhuej\np9AH\r\n=7c6H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8a59cefc151aa2104f4e4f921ec9c743a9d4eb72", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.11.0", "@babel/helper-define-polyfill-provider": "^0.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.4_1614080587734_0.9986145618504543", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "8fc4779965311393594a1b9ad3adefab3860c8fe", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.5.tgz", "fileCount": 7, "integrity": "sha512-5IzdFIjYWqlOFVr/hMYUpc+5fbfuvJTAISwIY58jhH++ZtawtNlcJnxAixlk8ahVwHCz1ipW/kpXYliEBp66wg==", "signatures": [{"sig": "MEUCIQCUG7b6OQW9ulij1ao3wRszTbKDFP32dCG+QOO7K5mgYAIgdGhQs5bUBSjQQZuDN4xSdNZej1E/zsGl5hUUiEqOFqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNQFPCRA9TVsSAnZWagAAD+wP+wVQ1jbA80GFU+KigwNm\nufOdWJuvRuH6GD9lQwOiPYT4A32Pcnn3Y7xrN6fRuUU4PIRe/CpBV7Z2zM1h\nf3vyPQou1HJGZHSb4HHKTuilln38JymGk8B5qabWYvvJDchnIyzhymk+JprB\nSDkUfwTOE7kmdV6PItj/dfWD4Q1V/Xrl2VphAQIwo1vJlwku22YySBa0ojkQ\nQZzc/6c0JeHcGmSLg4+jDX0eN0x17NGjUbw8iVaujH8u2VTKBynNU6bwcWND\nbk0QbzQ/HQvCFif5ZkazLAx/XNxTRKelULQkMLTKFk6mdWOTE8pAguOIlwWu\nAUFNBugYwKUTRN6MdOQ+0mF4zK0AUWk36QNjM+Y4+Uu1w8imyGdmUTmz37qp\njdRQHd9w5QkXuK+I6YIikIF5E9xBTcdCR6BIOTorjREY3geewhVzeHp+PN1f\nKseNvYJU/lGNT3DUofAIoReCjOTo8opujTEJoALZLqAv88XNVwGTyI3jfevt\n74/PM6Q1kefrw1S2hAfOPAjcu5221l+*****************************\nG5A2ohtarmX0aiwJq3vLmNZy+3fx/iNTM6/Pv5zwpEp5cTz+4F5a3HSc9AtL\nM5wzdqyyMiga5EBv6xUFFewTObLb6f82AIV3WGApxN5l3XiRpfKci5QO3tBR\n4ChP\r\n=F/Kt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "55f582c8ebc4b8a181a51fecaa92c89158cc7ac9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.0", "@babel/helper-define-polyfill-provider": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.5_1614086479430_0.6219931640948841", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "947a1227efa1a14ce09ac5fafc66ce8e039071e2", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.6.tgz", "fileCount": 7, "integrity": "sha512-1PfghLDuzX5lFY6XXO0hrfxwYf0LD9YajMWeQBGNaPNLQ35paV7YB4hlFW+HfwFS5kcp4rtPI/237xLfQ1ah8A==", "signatures": [{"sig": "MEYCIQD8VrjRw+WCNgNfzY9EfQHvui6fa0AdXTBY6Hz/6027bgIhAKRQHJKKwyjpHnsFqAYEv7PvgLABCjuN4l/v2BshVv/2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNpzlCRA9TVsSAnZWagAAkmwP/iOwgMepfjYugIq4o/Yx\nDMsV82fAjMsaed33NM565wBp0DAllncTZ4gYmkIg4a988B9Ebr99VnDEvHvQ\nLz2eCeHTafpGJhVSMvaacpMPvo0iYZqohatc/w/oLnsprfyReipK0kmJQdm7\nMe2lmSNOb93wCrpeoUlhnQ4KTTUhnL8IUe5kldJDXkRr/w0fdfpXpQ6MLeFN\nFcIpyUU9pslyA/vLVXtcrUVOR4BhiqO/CY89Cerzs0p9wiNk1LryQEQIt8WG\nbsfa0eEneZ+5xIctC2iHzHiS1IkyohzXdEcP7XLLgW1bxugFcHvocSuUtAw9\nzI5DU+son3EddxoUHgMG/jCm7UfgDoOy/EFzJedbq5mQIoCC1JImVipTLdA9\n8wMbSODxO3ZmkMK9Yi0U8Enm7ihY7R+CSUgq6Z2soXCCFoV9n1QMyR3Pwt2i\nbjxxEjED7ij6I3Y1ZbdAlVm3h82ThXSKuy9PDupmafS5/qs1Dl+m9e7OpAL2\nadsQDv++P73cJrMaZ/llNjclJA2Ixkc80IEmRQwBDBvuUGDgCmEtyiN+IH2/\ndmXTr1e4YVF7T0F6+i6AZIQWve7d3NhpGyFchPuBI7jeIIrmboJWy8ZU5x3N\ngv4yADy8clXxt3Pi2Ctu/knHUgNPkLqpNt/qTxj4WXaD8lmdLtDrgRaxWGhh\n6Y31\r\n=39ez\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "476f2b453e689512c722a5971812ea0558e56564", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v10.24.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "10.24.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.0", "@babel/helper-define-polyfill-provider": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.6_1614191845437_0.5987112904881384", "host": "s3://npm-registry-packages"}}, "0.1.8": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.8", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "54ef37b1c4b2311e515029e8f1f07bbd4d7a5321", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.8.tgz", "fileCount": 7, "integrity": "sha512-kB5/xNR9GYDuRmVlL9EGfdKBSUVI/9xAU7PCahA/1hbC2Jbmks9dlBBYjHF9IHMNY2jV/G2lIG7z0tJIW27Rog==", "signatures": [{"sig": "MEUCIQDtOOQfh76i5mcxvTOiv0FH7fPbYK2+h7+NQGIZ+9GYxwIgCsISEil/y6kL4MyNYfw+iQwRo/lrhx7wjIugTwv012E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODAoCRA9TVsSAnZWagAApeMQAIs/b/+8sj0Knq4zAsAQ\n2Apbv9pRVXWd6mvk43IUVfXdMk5l+DsKyagEY8FhPyfXN/+5UkmD9NBmKsU5\nqJKySDSrpwbgeD0Hrb9aG0Gzsx9Eh5hSG6t9GAOHuaF2NeVHI7UTlnOY0xPF\nk8aXw+FIfpQjjgDXRiyuhJTAWyTp3bZQBl1pCYFDXlYdKt4DeU8zGLzVLWVA\nISuRwGgol1AIMe3wFkmjchX+T3outroy3BXTqPaDj2Mjei4S6+BmJL+j3bM7\nr94w4K0HGRXUUD8cK3EIA0PLZkd06KMXHJDItxscfZfN+LHFfeVl4dhQMLyR\naNJW5qFYwdfZ+beqh18zpLRoeOiEkDe5QLCqrrnUyo413/0UW+U+qaumb1p8\nXimD8pKbDarSLtPTldOomYm3mma79hQja0mkuqRygkH4duwsU40UGWEjq0fI\nR3LkAA+pT/aIQXpU6DDrMIY4a7L3u0ed+Ddt8tqapxkGSWMP8rA6ZX7++Ujv\n0b217di4WezO6Abml+f8sI6/2kVm+jHVxUXYAzxYmBwzpb6aVXlnIx0mlA2x\nPITPUZ+vqHRWXZkVCKOF4UPOZRr1vKkf5QrxtRkAf5u0QpHn8s/UlcTo+E12\nZ1dfadBu9AKKUnYF7Bj/ODOQ3QZH9WEsSsUv0nPTS20oonGUghPRlRL7SmSe\n25ih\r\n=d+7K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6e6c7d2925b0f512397518d9f5ec35c925ea9960", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.0", "@babel/helper-define-polyfill-provider": "^0.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.8_1614295079945_0.5864514575399975", "host": "s3://npm-registry-packages"}}, "0.1.9": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.9", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.9", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "eb1634b8b17b71567f955787bd05b05f5de856b7", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.9.tgz", "fileCount": 7, "integrity": "sha512-+QMb65TxS4ue/mSJmd0M3BofCNcbsq49Pxij3X2mFjVhpwm81gYpmaHEc+GJgRzFrLaWjFbTC+kvVrmeRjwsCA==", "signatures": [{"sig": "MEUCIQDdyNKRrMtvM7lnWm+kt1cuOEL+G7Qi4vbZ0mWm2d8C5AIgHCWN23MkKE/R+JNPrK22P3qAGRvTct3VNpuj9s63q1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPfmbCRA9TVsSAnZWagAAFpEP/2NmBEsBu8py4/RwoetN\n2JsmCsUpRqQ9yZFflfgcSBHxURdn58vtNgEEBMQDEs4OrgZlP9CQF+v9Fm4G\nflfLGtpUM7K84I7DguFnsYVaB5uocDqy5UqRldgH0UZaMzzbQbngzopNe8AA\nox9xAxVqLfvdrc1/Q5s1IpK+azltWQNOyoyigZxMwZOlukFO0U6SgXSIi22W\nQIfit3zXGy6XSN+F/YwMax/AbABh+YclkuCXfTMu1ShmCmqqKogtz6MuHj/+\nS90i7UY6gYThdmt9AEYZS7lOuHXY/Kw1y5Hju6n0iVFFfIu9GEqvWaDOBTuK\nTM/qDjhSYENTQTAkNggDlUwLkkUbscWAMwGWJX1FGwOWd7Hmwk357fBM95T1\n4BJ1UkUC7fvWabJ72ZbktG46Z2bEYDqrWUMm51wSKtISkgedB+gepb/npuZ/\nDlhnJPDKCyKqn9mOTm0HhmNMA6pNzLV6hqtG5+n0zdGQo8br4VrbGTDEoMUK\n3EcAeGWSLA4K6ul2iA91LpJihLf8aDakt2wrsEiUCzw+Dbts2DayED0ziFs/\n44ZbeagspuIrEDyj0UyZtbmj0ogDEKGeSpGRM3ZP83n91PY54VwmEhhMmnM7\n5rUizibs6okTlzfWXs+CFsO8lnabmS6hbVTMXzxvFMk1ZS+J0+EfYkn5S67D\nq4Ak\r\n=qzT6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cca90e89debf689aff23e982f8a30bc3b512c573", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.10.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.10.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.0", "@babel/helper-define-polyfill-provider": "^0.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.9_1614674330898_0.6971810330644306", "host": "s3://npm-registry-packages"}}, "0.1.10": {"name": "babel-plugin-polyfill-corejs2", "version": "0.1.10", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.1.10", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a2c5c245f56c0cac3dbddbf0726a46b24f0f81d1", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.1.10.tgz", "fileCount": 7, "integrity": "sha512-DO95wD4g0A8KRaHKi0D51NdGXzvpqVLnLu5BTvDlpqUEpTmeEtypgC1xqesORaWmiUOQI14UHKlzNd9iZ2G3ZA==", "signatures": [{"sig": "MEYCIQDS7q4gUyO9/ecUzXWsQDmTIMTSBR6txmFouv4JAcUkpAIhAPP6XgoU7p+HnLvPhtH0eEoj1wtn5VWz52RgOH5soypM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21631, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPhTdCRA9TVsSAnZWagAAHE4QAI3k2J7ckghAXJ+LlQJ7\nUpEQRjGQBzSHecf9Iv8rvjvBS4JRMJeHe8YkeL6KfD14lhx4WN0rl7CYzdKj\ncEXb15I/gDC+G4JZ0HenLsCbxBH4WYnJMNYGny2Q2aEdKRzQxeXyu3bO9mG7\nScLSIRgoUWifARJDDmF7Htfph9PHDD4cZPpXpg+GkkQswxqPxUlfnH8MExyH\nwX+g1QG2LJFt6vYORZC/miwSfNEIJNBk3rkK0gwZ905O4cJc5Y47Dbo9ren1\nqZg+YpEEc85u3+FVLjw2iJSjkP+w+X7dpViA2vJKgCX6+mBabFhmMAHkTP4t\nLBysoZCdMsuCnu3BLddXfNJ1OeTeKM9xzw2KNLnCeJj1pPDB8u1fe98jVk/H\nXL7qxWGp+3In7JodPlpbWJtPSFdY2qSjjIzwObSlFCopMmlMvSMgNX7X77WU\nt1uJNy9+yd40TsW3t5Drc3V/mgAuRQ6ALWeBD7T8ZN3KOoIoX27ZMylSXMU1\nlcQ3gcpY9gFkhVkG6Q0eFet5lHNTBcBVlWaFWSDfH1iBRjnYZcRSXs4U5yzs\nf5ZcBh2i8YWtG6ChASxXhU69i+kYx/2jlzzH5GWwkYNTb1BnSj61BEEtvfhM\ncNpz2TwEbV11hP74GQR188ZjvR2c8d/Q4K5/L56qiu5hdSLk6ndsdThvWfoM\nKJ3x\r\n=7Pc3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7ec5d8eb769d309c78d27a336c9764abc8ac4db7", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v15.10.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "15.10.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.0", "@babel/helper-define-polyfill-provider": "^0.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.1.10_1614681309063_0.8672815740354818", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "babel-plugin-polyfill-corejs2", "version": "0.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.2.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "686775bf9a5aa757e10520903675e3889caeedc4", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.0.tgz", "fileCount": 9, "integrity": "sha512-9bNwiR0dS881c5SHnzCmmGlMkJLl0OUZvxrxHo9w/iNoRuqaPjqlvBf4HrovXtQs/au5yKkpcdgfT1cC5PAZwg==", "signatures": [{"sig": "MEYCIQCtDDNKHdI40MqteSOU886o2hmHr+3Fa2RqId/dqmEkBwIhAJXs9AO/fUdGHeQSid4l2iPTRAZZSj37j1c1EyRmWzdV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZJraCRA9TVsSAnZWagAA23QP/0AlKn672sQN+fjvYXGs\nZjFYO5GXH6oKbZv9xH8N2tyotXWLfdKWu+fYaAu3AZbscFUDhZ5UfWL8S2rH\n0SAzYXN5IN5Mll9r1fR0BneWL0rlR4H0Ro1ZOhq8uytHgd9w4gvGAC7UCM0z\n5gpI1eiv6iP+gRDC4he++qHi4AFPoaHRfBLQwSCmnia4EFrO35mOWbWEtZtN\nYo925RcGy9KyG3jyZgR10Azr9rh0hYhGWtpdurX7jF/RuIhGd3JCUYkd5sxP\n/5PgP9nbQMxE1Csok0bg9f3dHZO6+HB9J+JtH3vbfsBGAZ4Lf6UawHgYEyrr\nFv2+2Qexb36Na/njHDrADKV1DF0dKnVKinQG9T5iS38+wpP3oEj1HU1fdhmm\nAV+s5RD+TZkAo/TS3lsEHP973y4NHKTUjEGX6IPfO1K0UyLD1mbH6NKs8R7G\n5VpWrH6f2IDo2R7+NGo/rlD2MNjzPWrArWwfVijPpYLho5xcwChvhcQl/w1t\npckhGXdjz+9WXfZfsqjksWvgnRYLqcyTu9RjxGBlFc9/sFhv4NOaTQMwCE4f\n2SKM26xp9S4AW9iEJpZt6aEYExtG4/3tesq8IZ5jhOrCWgRHyF2ef5c9sow+\nFtuITWCYTWiZg9QawZBrAvAAdEEU+Myc9d1pEEWwCBEoVZ3v7vPMHTnQAI4T\nuKA0\r\n=NglF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "841e713e0002eb8aa167553fc43840e526fa2d8c", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.2.0_1617205978206_0.8705771760266625", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "babel-plugin-polyfill-corejs2", "version": "0.2.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.2.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ae2cf6d6f1aa7c0edcf04a25180e8856a6d1184f", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.1.tgz", "fileCount": 9, "integrity": "sha512-hXGSPbr6IbjeMyGew+3uGIAkRjBFSOJ9FLDZNOfHuyJZCcoia4nd/72J0bSgvfytcVfUcP/dxEVcUhVJuQRtSw==", "signatures": [{"sig": "MEYCIQCcevi4SrJaFMLFPs0quEdHiU0wfqfeTwVemMVn8x9CWwIhAIkoa7qiIlXrA2dsiPu4iEIU4GexK07FwQJgok7s7AJQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqDI9CRA9TVsSAnZWagAAmasP/jLFR3wEbyzIu5f8ZyLQ\nwhM2Nof6KaX3164Fx6pRd36c/g8toyq8y5w1jxkcs3p5s8IWyYRyn0onW6gy\nG2f0z44+jhp5bRrEWNWhblPuESHJsQp5Uoc5+LCgdSrx+mtII4lgYnVVd87t\n6O5nRJOCdMLQfXcseRA7xiseVUMw9fdoCqHiRrdHGM7+6ARtk861WfcVm6TJ\njk6KsGjA8oRop1zsAujxNfmkZKb8624w/clstaNinynp+57ynuQqRbdksnYa\nEYvQ3xEcHpJeFxMnl/AEIPpxL9o59ekYgdeaN/TpEyaO7kt3oLwuplCjUj/g\n+kmR5+mcThIRaWPXnR3voGf9rVhoPB/ahuE6VitaHHSTpn0lCYLCql6go5Ry\nid5C0cGEMkNUz5BfMQhVBIC39ay1R3H4DqPSTCjLlx9waVIdczPDBXR6uj1o\ns1mAxogXrMqs/1nT4Q+VL/slanX6lBWQHUcHyVNBHoZOwIpA7BBwsshk40BO\nLk2xHUfPAdogErMkRDKao78Zp27y2Rao/F2OgFakzZz6SIXu2b1pbJqk/5xm\nnPTXrfAI50zYpXyx22xcOEUhNx9e1IiBMSNg9bqOYeRpJbd678/Q7R/Ryvs5\nO1/TUOEtnIGfeNyUsWSy3n7nTHF67KjVuLdosO93tKjjWFXlUO9ZZLzITi8K\nt9+X\r\n=axKJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "a82aa2032d84ece15914846fde5f1544701b0a04", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v16.1.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.2.1_1621635644609_0.3080074640025159", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "babel-plugin-polyfill-corejs2", "version": "0.2.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.2.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e9124785e6fd94f94b618a7954e5693053bf5327", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.2.tgz", "fileCount": 9, "integrity": "sha512-kISrENsJ0z5dNPq5eRvcctITNHYXWOA4DUZRFYCz3jYCcvTb/A546LIddmoGNMVYg2U38OyFeNosQwI9ENTqIQ==", "signatures": [{"sig": "MEYCIQCxdrpTlgVawEN7fLDbQtJw6jNBDFnSuCBOosO/pW+kPAIhAIXfxYp7JXGnAyHtpaL2T7ixdCWNpvzon4VkkqWsX3nh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrh6XCRA9TVsSAnZWagAAfIwP+wdkrca5y9BOsQUxMdjS\nz2lpOeu5xrNr5f68CDMlaPjBOY9ZsYBi+QwsZDChJ85I+pzpD5i7gspWeeF5\nX5BH0/o+igsP4zgreNNTEwUsmqL5KDalL+50Ff9EoSdx7sfvxRP83l68TYoZ\nb4olnc6X56VFmfm7lbDyFsWQirVua87IPU3NDpBuo+ApDk56CQmF0eMv1PkX\nfVoA53ITkXY0xfnU5xWXFY3XQ1mI7tIVrJngsMvTeaHlR9zGE1xd0YgcIk7J\nJ8+yjbcL9XfoErxBsCpr8sBP9GHSwjBcu9dydyh9gk/BLQlr56Wh+dH7xKaM\nRYgoy5Ja9Lx2PXD1gCVAxqKz/IDopay9/7akwXgQ8ZUQnBVh1rwyjmHhRdbb\nAqdKJncWKqwRxYUgTsaJGfTqNC6UX6g6TbxnroaSkwwvGdt2h2/g6oHIrUOf\nwYrD4u6SxCk5DlYAZ3zErTwm0fpdkhKnOjVsB58ADuD2PfQfOuwtn7yw5xWK\nRjiEicaDvlwyC9b42jwu8CKgFCBKrLCyptGRI7EQfjGXqSE7RatumsTu7N4j\nVVfsaTLM/Bxf39y3SZpWxXPvvgzZ89fIHjBOaFvTYcok9ZKOsn0l/20aIvNK\n4h2QxyaJa6DmxT6egTjGh00YZdGL5LIxYo+pupuAcXRbJdEYP0DGh9solSpI\nVH7Z\r\n=q8EU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1db1e16a7e6855094c52a6cf9b98410e3f0e80de", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v16.2.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.2.2_1622023831532_0.49631798726366005", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "babel-plugin-polyfill-corejs2", "version": "0.2.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.2.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "6ed8e30981b062f8fe6aca8873a37ebcc8cc1c0f", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.3.tgz", "fileCount": 9, "integrity": "sha512-NDZ0auNRzmAfE1oDDPW2JhzIMXUk+FFe2ICejmt5T4ocKgiQx3e0VCRx9NCAidcMtL2RUZaWtXnmjTCkx0tcbA==", "signatures": [{"sig": "MEUCICNDMy/OgE4NZJBWX8a7KRzmOZL8E6emLY8K0MVc1AtkAiEA/E2MFuLJWlThIX9+Q9fOa8CWsNvVCXfUkRqOgYR4V/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81207}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "de48880e867791eaa94c00ce7132dc259f3c777b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.2.3_1635546017691_0.8715354750996149", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "babel-plugin-polyfill-corejs2", "version": "0.3.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.3.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "407082d0d355ba565af24126fb6cb8e9115251fd", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.0.tgz", "fileCount": 9, "integrity": "sha512-wMDoBJ6uG4u4PNFh72Ty6t3EgfA91puCuAwKIazbQlci+ENb/UU9A3xG5lutjUIiXCIn1CY5L15r9LimiJyrSA==", "signatures": [{"sig": "MEQCIF3neSZYp2CVxi46on+jIeqTGMDdNQsau9CjIHaUWxB1AiBRNAS9KZHPBSKUClQxwQPHxZ71gemnctLqU0lKBt7t+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2kQ/CRA9TVsSAnZWagAAvfIP/3243TFOsnexaKQvlqDv\nZ5cZZT0DE7Yq6OyKTCJPdCjrdpg0DavdE5q/nDuztov7LEKZllz7GQ8qOX0y\nPEdN5TP+DBq1DKmG81ItJAYLoIQ6PYCRzO5NrpRqK3AymQA/1l8TfaqYX74P\nCKgWcLrgSEUEfyP2eSvMpJJIjF1QFh4O02reDBKZdW18F3FkZy5mwCL20STu\nUhbJtRtfa/EvUZUuT6sFDKXmyqckm7cwcmjexx6qUlDVH0IoERAjfORd9Nof\niaQBW9rv0jzl139IWI9NJTgcAtYojJbEpq5oVxkDt3p3DJq9OuuHoPTJk9FH\nEb0EYXH65ooGs3FteeWyJcwTSfg0doTpetEO8N4B2BOO9Z5qNnlALPwlbWkb\n3B0ya5g2C5/2Wprba05C+Nwdrrpnx1N+HgkFshxhdtVN8hvvjJszqq4LYRwv\nUFbDKtk5x7Qal9gdevA1ppoO2I8hEm7OQzb7Jhg07r4Z6ob+dErq7zQATx1O\n6dwOhhQG+3NPo87cx6dWLNtDqiBJJtOd6N/vmv16UBc0IguP1e1DH3+BW6i4\nGsUBYhSx+0KfQ/y0QuskVfSngUocNIIhhn/DLWXnIBRhXO9IU/Gb+z8rE8vq\nzpnrW7qNcvzSGhQqxf5jAWTRy7zHqo5kdc1dUdY40WY/eYOKOTU1BrOFquWO\nHtD/\r\n=Nplb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "b2688bbffcae0f0c6ef5cd6ddf1abacc574060a1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.3.0_1636799700935_0.04260756468264559", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "babel-plugin-polyfill-corejs2", "version": "0.3.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.3.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "440f1b70ccfaabc6b676d196239b138f8a2cfba5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.1.tgz", "fileCount": 9, "integrity": "sha512-v7/T6EQcNfVLfcN2X8Lulb7DjprieyLWJK/zOWH5DUYcAgex9sP3h25Q+DLsX9TloXe3y1O8l2q2Jv9q8UVB9w==", "signatures": [{"sig": "MEYCIQCeKLHvSCdcCzkQj6jhvWCVgCWffP3CPFf5d1U/bTKEngIhAO2nktLl1XWPJbz4oOjTP2WYlBvzBBVuk2vlMeTZDd7v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4uKOCRA9TVsSAnZWagAANKcP/3L+lH/einWX/VychfIV\no25nCEXiAKzGga6CCRBMhmWBFtTIV1qeu/kB5Yq+IPi6+aZIil1XS9wK1UT6\nsEP9H7KnjwX1hJ+v/x9B4QwnyXelABiY6b7heQ2DGYOW3BSRDwoD1MwuXyqH\nz28dCjNWF7+vdsbyycD8XsN7blpJ0FzEc5HaGGJ1clvSMyjWyRFJW6tatLA3\n6oD9VDA4WJZxdQ8/tMUPb01fVacKpXsoiosWLMMpNnvKlN/P2Q/A2QBUZ5Ay\n9Z4JfU5vwMvq3lQ0MD3w9LUIGFWW8Pp8gxshPc9U3dhnytcGe8hlb39buWpH\nTsDOiaxCdoCjoWhoN85vg+839+3RpKh3nIGjcCLTt2vBltSj79bDmryq52AP\nFe3vEkyRHTSEMY/MAKyTAYVkM+KxQ8DHyXBO6thNicDJPGdQBmk1gDIhvct8\nn3VYUf/5b7EoVfZL2DPYzt7qUcEuH6pF/4TlEUOQyikqIroR5/fjs3B6sRnp\nFbEAz70XWuixhq0huQgUjBT3yaBzex776QVEngwyTfuVrODnd+l/Rsx1P9MC\nBBx3kZvUfhOPBExAJVIoqufqBoeiJJECuxW72aQpnzZKAc+bSL8DKMFE/QgX\nHRdm1AtEXjWCdDhTm289Gq+QjR4I4D8wqCI/EbYNpfCk7H/k1k44tgz2xfrG\n2oLG\r\n=x9YR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "f5eb1c6d74fa29afdb0a3a533dfc5742557197bc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v16.13.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.3.1_1642259086295_0.24935607486890365", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "babel-plugin-polyfill-corejs2", "version": "0.3.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.3.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e4c31d4c89b56f3cf85b92558954c66b54bd972d", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.2.tgz", "fileCount": 9, "integrity": "sha512-LPnodUl3lS0/4wN3Rb+m+UK8s7lj2jcLRrjho4gLw+OJs+I4bvGXshINesY5xx/apM+biTnQ9reDI8yj+0M5+Q==", "signatures": [{"sig": "MEUCIFPgSwGq8e3s5ulEyFhCyAj8Lkbb5dTrdWKEafxfOKgGAiEAtUr8M4xENOkxDwWMhXf26aLzEsLY3u8XSBQr3nCI/0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3O05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyvA/8DcLE6Nqa7pBYFHhAiGdS0Efb/GNIcsAYGi9pLondNMIYwNwr\r\ntgpq8ex8cI+ShNZDejIXkkth+1VZkpYSs0E0nQgGMqv3B0hHoLq2eKwjzFDn\r\nVbkh+FiLA5iQbiridldn8PI9ogQV8WEBOijBn0a/XihwBkp1/eBTMWxWek//\r\nCzDYeGn4td2xUbs+2J//hO8wZxUaklYrc7bGjyicofFTePwfuP0JZAkxtvXc\r\nQosMBCCK3z4g5FiEV0JPZW/vdardBVEA4Wh9sO6Nkn0st70V/s9ZKaFldQwH\r\nSenJwGoCg8YsTcDySyehUgAbXpQn6h32Xkpn1A1+wolgNFFi3EF4TtgtK8gW\r\nqtDW97l44nWgKJTZc30bkBfSPERW55CavJNB0aZtrqc0IXqyfD7eIsD4v/aU\r\nWltIXdqmtMYd1UgylyiShJkgEiXfXsBnmoXdyZaQcqbR67/J9KEjJhQGEm75\r\nund9QmZkzPOXTdLzwUMCM3IKb57imQ6XSpxV5IfU2T55NvwXxzyI4EnxCraI\r\nOLdBdLhYInBUOMpAogO8i4jUU5kb/gjyfdfpKY7GyS0tPwKaZtoDWiQzKjye\r\nX+740zaltGZ1d6/4nKtHC8PThRlEEJEBSiGuOPm55wUJOI5RdGhhzQinX6Ay\r\nDqwVlNLZQlCueYhR6YhvS6wKWC5ndrw8xLQ=\r\n=ucQq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "62b9025beeec450a1ff5d61fadcf63963aec5015", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v14.20.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.3.2_1658645817123_0.04990109816759469", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "babel-plugin-polyfill-corejs2", "version": "0.3.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.3.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "5d1bd3836d0a19e1b84bbf2d9640ccb6f951c122", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz", "fileCount": 9, "integrity": "sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==", "signatures": [{"sig": "MEUCIHtfh0tExC0XAPEbBEZzfiy+VfKnVsZbqc/KFqKRew80AiEAgbsf1npWsiDWDmdnKrOhOskc9rgzXoWQ8Tk1u7B8bxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH+hjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ1Q//YjI4y68TnfhPN6ALDT4sO4X+CgEEvDkbR8OYAGDxZkOkB1ra\r\ncccmQCWMrwWFIOQra3bwBXFiqfMKfSQdenzWAW8Fcas75UvMbhgxkUNdlt4X\r\nPpGhNwLmv3E9bDXIDfJQoWVxexs49pinI8umONuQcxfluIV0QPIWN0OIpMvV\r\nHgqjXQFWLNirkpt5z7AlM8LA16DeewNRJD9oxZvS3nK/nzst8OuArlrRdtDp\r\neSB852uZbF5H+YoIkPYFuCruScgHfwJBlQdpj5uOr86tKnxkBN2an9LWZRm3\r\n5ruAakv9ENUy9YZCxrgOvCDS7Zi7MUPHAywcBQcpCAygGhVHfRgIm8WCjPW5\r\nJahwiA4dJROHh9PasKMELrovDpCOdJTAxiz7TRpBHqfIKnpQMmwJDRQLkNMM\r\nUtutppmcjzbrBdOGCMtC6Tgk+M2tgD7yh+C1OlMXefDKo2x9wpEAQv5xMyPQ\r\nK1BGVEUSWPol8RX7aPHTzOrE4QEVAVTnauGKpLcHv1szHPxvYMaejpu2yY5B\r\n6v16rgW70xrrPsfA1QGpUd6Q+bKwfHJeYkCGamCY+ASdx5bRym1WPL5CN6kz\r\nIORncKjSCptjcuN2gfsq9x6aTKuv9OFI/lTmuiX81AJs9DlUkTGgRUpGBi+E\r\nre5e8kwzJBjsIA0d58OJ46YB+aXOW/FTr34=\r\n=GLAk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "42b9477c199c0c5420b45cfa8c9bb892d94a64af", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v18.7.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.3.3_1663035491150_0.47455164048023035", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "bb159b71fc036479b83e5a906109ea42200a296a", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.0.tgz", "fileCount": 9, "integrity": "sha512-IS9NDITf8nYVoFih/wDs3jqRvi9MC+FZZt0nflml/KYE3oYqmjWM0T2x0BQnweAXDB2b3xTIpPKdOjs5EleJ9g==", "signatures": [{"sig": "MEUCIQCTnuQ4dhSxct7qh19Ls5vxR4XUM7OmMsezCeFRZSJiUAIgBEhIGBw84uDcDDSHSlno1PJCKpn1crWZHHfsgXvu8xA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80658}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "391a1f4049fe1d6943ca8e91cf7e2e23f3f1ef73", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.0_1683713941860_0.12762355119051638", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "65daabf234f7e3e2368a5d566b32a6c4d1714edc", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.1.tgz", "fileCount": 9, "integrity": "sha512-7gKaNF3qkEt1w2p3Q2D2f+UofAKtjWT5B82VXKdDlPqJI8eLT8phBKHq6uLEOCAEtnAWW3lomu3+X6VJrFMoSg==", "signatures": [{"sig": "MEUCIG7oNW80aeZE6HrXmn6l2d2P0hKy0dGQ0gemuYd1W4u9AiEApHObcjZi8GEfwji6GhTjUItvXdp5Ogukb+YxUuxTH00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80733}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "4b3c67a6a946df756123952a0033dc4426696131", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.1_1683716132716_0.8702977293062164", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "147295bbd078f731617be5947bc8536cf8b339d0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.2.tgz", "fileCount": 9, "integrity": "sha512-hnfNY+kHQc4227pQJ9gzziiF9iDOzccPKlisfH1+B9EZrC36V+JcwSApxZsj6tD2jbBthKO3l4XpQeCVa2pRbQ==", "signatures": [{"sig": "MEUCIDueV8T7QxbRP5XvCU/IG5wUN/qtcQVRxh+hLmmWnxJaAiEA1Y5AnJ1RbBtEN4graWFc6K979EUREjXihlUvtlV12AQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80753}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "6b66be1d9fae2af0dfd207adc64ed99f8e1a5924", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.2_1685121175138_0.5695446340812036", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "75044d90ba5043a5fb559ac98496f62f3eb668fd", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.3.tgz", "fileCount": 9, "integrity": "sha512-bM3gHc337Dta490gg+/AseNB9L4YLHxq1nGKZZSHbhXv4aTYU2MD2cjza1Ru4S6975YLTaL1K8uJf6ukJhhmtw==", "signatures": [{"sig": "MEYCIQDWur/dfoZMmUKSx+pUx3xPtqu8z9if2efUwbtzkQRh1gIhAKcne/vzkPdGdJaMCKQRJxyZ0QmCFGF3YITOqEGaCa0W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81440}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "476a0d277ae9bdf705fe2b2cce645125b3c774dc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"semver": "^6.1.1", "@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.3_1685124424612_0.35237515791510554", "host": "s3://npm-registry-packages"}}, "0.4.4": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "9f9a0e1cd9d645cc246a5e094db5c3aa913ccd2b", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.4.tgz", "fileCount": 9, "integrity": "sha512-9WeK9snM1BfxB38goUEv2FLnA6ja07UMfazFHzCXUb3NyDZAwfXvQiURQ6guTTMeHcOsdknULm1PDhs4uWtKyA==", "signatures": [{"sig": "MEUCIFH8V/ELrz9sbKydGHW4Y1V/9tXLMLYKIB9nKKe4sQ3qAiEAmkfBWh0+4wpFKBfGiH9mAg5He30AHsqhMYUG89GhiSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81494}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "74956db5d547985ac8e60bf1af56f4c61af12e4e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.3.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"@babel/compat-data": "^7.22.6", "@nicolo-ribaudo/semver-v6": "^6.3.3", "@babel/helper-define-polyfill-provider": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.4_1688546289133_0.430162087279504", "host": "s3://npm-registry-packages"}}, "0.4.5": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "8097b4cb4af5b64a1d11332b6fb72ef5e64a054c", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.5.tgz", "fileCount": 9, "integrity": "sha512-19hwUH5FKl49JEsvyTcoHakh6BE0wgXLLptIyKZ3PijHc/Ci521wygORCUCCred+E/twuqRyAkE02BAWPmsHOg==", "signatures": [{"sig": "MEYCIQDkG5yrrzwS9BL8XFEDii7qnqMBnlEGkSv34yWbgpzw2AIhAMIWhrxvqX/PiXp8/I3ZVdGB/ebqh1HcGziK+82jC3Ax", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81270}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "0e8cfb85899c8fb01728199d81fd37108e1668ab", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.4.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.5_1689930194338_0.6256001343174125", "host": "s3://npm-registry-packages"}}, "0.4.6": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "b2df0251d8e99f229a8e60fc4efa9a68b41c8313", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.6.tgz", "fileCount": 9, "integrity": "sha512-jhHiWVZIlnPbEUKSSNb9YoWcQGdlTLq7z1GHL4AjFxaoOUMuuEVJ+Y4pAaQUGOGk93YsVCKPbqbfw3m0SM6H8Q==", "signatures": [{"sig": "MEUCIQD37h+GzVcX0UU+oU2H2HKh/JIGWmvLZLlKOWYDpjrNygIgdr9r5h1q9DMOipxz+3/I9eYSQnm9U5CBqegzo1LWJHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81270}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66a6819f44a57152798cb3b0a9272c65752bae86", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.6_1697007741731_0.12576141234639082", "host": "s3://npm-registry-packages"}}, "0.4.7": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.7", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "679d1b94bf3360f7682e11f2cb2708828a24fe8c", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.7.tgz", "fileCount": 9, "integrity": "sha512-LidDk/tEGDfuHW2DWh/Hgo4rmnw3cduK6ZkOI1NPFceSK3n/yAGeOsNT7FLnSGHkXj3RHGSEVkN3FsCTY6w2CQ==", "signatures": [{"sig": "MEUCIFveiNY953PiqG6aYivLQrdcU4JIq3XvNde5jXj+dsknAiEAon2xQJg7QR5spPrjDVWyYkR7vgZVQdUqFtxyWdy+XCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81270}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "984c56c59568283889c3f0f89e58d370e4fd10f8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.7_1702304408129_0.737074765582814", "host": "s3://npm-registry-packages"}}, "0.4.8": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.8", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "dbcc3c8ca758a290d47c3c6a490d59429b0d2269", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.8.tgz", "fileCount": 9, "integrity": "sha512-OtIuQfafSzpo/LhnJaykc0R/MMnuLSSVjVYy9mHArIZ9qTCSZ6TpWCuEKZYVoN//t8HqBNScHrOtCrIK5IaGLg==", "signatures": [{"sig": "MEQCIBWMFcgNJjc/hhrNpf+IoA07DD+hMN+Qh2GaE5z5D0wSAiA5G8hUpDq3ptrj9Ki4LGCqYFQz4cPwVoDFD4Xuy9iTbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81270}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "9738ea2a12643376a52c9be30c20ac19426a88cb", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.8_1705571831690_0.14599471893533034", "host": "s3://npm-registry-packages"}}, "0.4.9": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.9", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.9", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "15a285f681e1c5495093d85f1cf72bd1cbed41ce", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.9.tgz", "fileCount": 9, "integrity": "sha512-B<PERSON><PERSON>WIaO3MewbXWdJdIGDWZurv5OGJlFNo7oy20DpB3kWDVJLcY2NRypRsRUbRe5KMqSNLuOGnWTFQQtY5MAsRw==", "signatures": [{"sig": "MEYCIQCk0KLy6NQFJUydqJoMOiGslo0nN8HgyXXUP782mWJoMwIhANhCct8jUs5xwM/l7Yo88nSyb6epdwPl86ipQj6gBqQm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81458}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "58703f07c9cff9f27d145215265042094739a175", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.9_1709919060892_0.4668780318258303", "host": "s3://npm-registry-packages"}}, "0.4.10": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.10", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.10", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "276f41710b03a64f6467433cab72cbc2653c38b1", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.10.tgz", "fileCount": 9, "integrity": "sha512-rpIuu//y5OX6jVU+a5BCn1R5RSZYWAl2Nar76iwaOdycqb6JPxediskWFMMl7stfwNJR4b7eiQvh5fB5TEQJTQ==", "signatures": [{"sig": "MEYCIQC0hRqT/oNe9KtJi6nzZYwRQFuXUz6RDyQApp6uJ0OVfwIhAL/ogM/v5PqZaWJl9ZOMl36P+wfGBrFGJqXtnQGrsVX/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81459}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1ce88db2507db2ef3d2ed2a2f920a3cf0b9364b5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.10_1710259199557_0.9347365351669672", "host": "s3://npm-registry-packages"}}, "0.4.11": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.11", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.11", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "30320dfe3ffe1a336c15afdcdafd6fd615b25e33", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.11.tgz", "fileCount": 9, "integrity": "sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q==", "signatures": [{"sig": "MEUCIQD0fr984NBgs4DjlrELaP2HClRtHrWGHXzyTXCGvEbY6QIgaLOpx/JZ2qz3bibYARK7mM/tm5IZyot2hm2+O3+gXgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81459}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "2da4da8e1a3d87640c88a3cd7e650cbb1c049a33", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v21.7.1+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.11_1713809615800_0.8007398799727299", "host": "s3://npm-registry-packages"}}, "0.4.12": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.12", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.12", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ca55bbec8ab0edeeef3d7b8ffd75322e210879a9", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.12.tgz", "fileCount": 9, "integrity": "sha512-CPWT6BwvhrTO2d8QVorhTCQw9Y43zOu7G9HigcfxvepOU6b8o3tcWad6oVgZIsZCTt42FFv97aA7ZJsbM4+8og==", "signatures": [{"sig": "MEUCIQCZAf9BfDQokO5xueNsFZDLdLX5PJdzDqKhHeB8zqkJ5wIgC8ipCX2rpNEs7Ktmlz5jIc9gbeLlL0fpAD/7DTilrAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81459}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66340fb145086a826c496f008f67488367846c09", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v22.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.12_1731351013805_0.6319985941493269", "host": "s3://npm-registry-packages"}}, "0.4.13": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-corejs2@0.4.13", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "7d445f0e0607ebc8fb6b01d7e8fb02069b91dd8b", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz", "fileCount": 9, "integrity": "sha512-3sX/eOms8kd3q2KZ6DAhKPc0dgm525Gqq5NtWKZ7QYYZEv57OQ54KtblzJzH1lQF/eQxO8KjWGIK9IPUJNus5g==", "signatures": [{"sig": "MEUCIDIqfrorK/WaBm2rAJ54o3zS2THCKpy0mWeGyicAVNofAiEAuJYc33jyeAQE6k7HimwM1seYLHHtN4aC7j+UG/Rb0jU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 81459}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "_npmVersion": "lerna/3.22.0/node@v23.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "directories": {}, "_nodeVersion": "23.6.1", "dependencies": {"semver": "^6.3.1", "@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.13_1742390649428_0.6424358641043673", "host": "s3://npm-registry-packages-npm-production"}}, "0.4.14": {"name": "babel-plugin-polyfill-corejs2", "version": "0.4.14", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "homepage": "https://github.com/babel/babel-polyfills#readme", "_id": "babel-plugin-polyfill-corejs2@0.4.14", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/3.22.1/node@v24.3.0+x64 (linux)", "dist": {"integrity": "sha512-Co2Y9wX854ts6U8gAAPXfn0GmAyctHuK8n0Yhfjd6t30g7yvKjspvvOo9yG+z52PZRgFErt7Ka2pYnXCjLKEpg==", "shasum": "8101b82b769c568835611542488d463395c2ef8f", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.14.tgz", "fileCount": 9, "unpackedSize": 82653, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDLc6PDRPGaCJa7oyrInc70VQPe19/j9aOHEgCqdv3n8AiEAnZYaRjkoLCRNkLAM3jH31cbhJNZiAZzwJ5oaL2UxSPs="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/babel-plugin-polyfill-corejs2_0.4.14_1751042269603_0.4226829072579612"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-04-08T14:27:15.557Z", "modified": "2025-06-27T16:37:49.972Z", "0.0.0": "2020-04-08T14:27:15.717Z", "0.0.1": "2020-05-26T20:42:25.206Z", "0.0.2": "2020-05-26T21:00:50.645Z", "0.0.3": "2020-07-10T21:36:08.230Z", "0.0.4": "2020-08-31T22:59:43.610Z", "0.0.5": "2020-10-03T10:54:35.088Z", "0.0.6": "2020-11-09T14:37:29.507Z", "0.0.7": "2020-12-22T00:53:51.587Z", "0.0.8": "2020-12-22T01:02:01.344Z", "0.0.9": "2021-01-07T15:06:07.167Z", "0.1.0": "2021-01-09T14:09:29.997Z", "0.1.1": "2021-02-22T00:57:13.550Z", "0.1.2": "2021-02-22T21:13:54.685Z", "0.1.3": "2021-02-23T11:23:17.902Z", "0.1.4": "2021-02-23T11:43:08.015Z", "0.1.5": "2021-02-23T13:21:19.566Z", "0.1.6": "2021-02-24T18:37:25.612Z", "0.1.8": "2021-02-25T23:18:00.076Z", "0.1.9": "2021-03-02T08:38:51.131Z", "0.1.10": "2021-03-02T10:35:09.173Z", "0.2.0": "2021-03-31T15:52:58.378Z", "0.2.1": "2021-05-21T22:20:44.810Z", "0.2.2": "2021-05-26T10:10:31.649Z", "0.2.3": "2021-10-29T22:20:17.931Z", "0.3.0": "2021-11-13T10:35:01.097Z", "0.3.1": "2022-01-15T15:04:46.441Z", "0.3.2": "2022-07-24T06:56:57.257Z", "0.3.3": "2022-09-13T02:18:11.353Z", "0.4.0": "2023-05-10T10:19:02.054Z", "0.4.1": "2023-05-10T10:55:32.976Z", "0.4.2": "2023-05-26T17:12:55.320Z", "0.4.3": "2023-05-26T18:07:04.779Z", "0.4.4": "2023-07-05T08:38:09.290Z", "0.4.5": "2023-07-21T09:03:14.523Z", "0.4.6": "2023-10-11T07:02:21.935Z", "0.4.7": "2023-12-11T14:20:08.345Z", "0.4.8": "2024-01-18T09:57:11.828Z", "0.4.9": "2024-03-08T17:31:01.095Z", "0.4.10": "2024-03-12T15:59:59.775Z", "0.4.11": "2024-04-22T18:13:35.942Z", "0.4.12": "2024-11-11T18:50:13.979Z", "0.4.13": "2025-03-19T13:24:09.635Z", "0.4.14": "2025-06-27T16:37:49.804Z"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "license": "MIT", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "description": "A Babel plugin to inject imports to core-js@2 polyfills", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "readme": "# babel-plugin-polyfill-corejs2\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev babel-plugin-polyfill-corejs2\n```\n\nor using yarn:\n\n```sh\nyarn add babel-plugin-polyfill-corejs2 --dev\n```\n\n## Usage\n\nAdd this plugin to your Babel configuration:\n\n```json\n{\n  \"plugins\": [[\"polyfill-corejs2\", { \"method\": \"usage-global\" }]]\n}\n```\n\nThis package supports the `usage-pure`, `usage-global`, and `entry-global` methods.\nWhen `entry-global` is used, it replaces imports to `core-js`.\n", "readmeFilename": "README.md"}