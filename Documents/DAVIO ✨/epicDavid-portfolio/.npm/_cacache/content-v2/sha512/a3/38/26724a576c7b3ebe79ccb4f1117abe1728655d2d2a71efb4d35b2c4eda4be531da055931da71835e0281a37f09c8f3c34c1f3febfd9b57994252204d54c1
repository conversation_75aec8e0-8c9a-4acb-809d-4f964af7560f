{"_id": "@asamuzakjp/css-color", "_rev": "52-a590c245e5163cde7a70f5586a238c39", "name": "@asamuzakjp/css-color", "dist-tags": {"next": "2.8.3-b.2", "legacy": "2.8.3", "latest": "3.2.0"}, "versions": {"1.0.0": {"name": "@asamuzakjp/css-color", "version": "1.0.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.0.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "f8e0b297820a2d660037795e8107c362946530b5", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-lo7k8F33cu01rzneWv2Pj0cBw1L4B/VX+V9XLJscyIMjvVuSM7jBUsRDR+Z57/c1DMjj4QHm0IBN/PG2Au14aw==", "signatures": [{"sig": "MEUCICUf1KMGIfeAAI7snPpBkJgQ0jWhieHNu2KQ70hpVSgzAiEA3xwxeOHVdNFS5nob3RZCLZHb8CzFRykYPzUys3UWOro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257579}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "require": "./dist/cjs/index.js"}, "gitHead": "35b86bb32bdce4ee004eb78a92514795e2dafb74", "scripts": {"tsc": "npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run compat", "compat": "esbuild --format=cjs --platform=node --outdir=dist/cjs/ --minify --sourcemap src/**/*.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.0.0_1706274840094_0.645722571602811", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@asamuzakjp/css-color", "version": "1.0.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.0.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "61cfd9651604ae21f2a389c7a6166cc366fa5652", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.1.tgz", "fileCount": 16, "integrity": "sha512-HRE89jJMyiCdYbgOrozBz81oYVG8WSegy7jLrCAQ8JWQdH5lyz4Ax6t1Q7ja5hjdxcWSHs/HEYvzC1qwEfTmKw==", "signatures": [{"sig": "MEYCIQDnTzVYLBk7HmBTq/izF5ZwEVMvDq7hchvatk/EaTgTEwIhAJtJDjMY8plVaKazToyqsDUnoAMq94jB5dVvTPjUDFFc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259583}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "require": "./dist/cjs/index.js"}, "gitHead": "204528c09d66f58e08030bc345c854f8570dc65d", "scripts": {"tsc": "npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run compat", "compat": "esbuild --format=cjs --platform=node --outdir=dist/cjs/ --minify --sourcemap src/**/*.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.0.1_1706322435284_0.6040943965579251", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@asamuzakjp/css-color", "version": "1.0.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.0.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "ac792411f94e4006befd4068feabc1bae5f545cd", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.2.tgz", "fileCount": 18, "integrity": "sha512-dmyJMjEtN/cJU9QLjpFrtkJGucffWOCJD8tqolXJUfSKe+1jyjr23yJZFZjfvvKqIkFO29e7NX24dxkBU8BFxQ==", "signatures": [{"sig": "MEQCIBpftDPH937dQ/WeHOmWtZZpDCdGGrNNBhAYNm0BsDA9AiBR8bslRNTNUMi5HgFU4uINL1IJKFODjXw0Bq0HP2KT4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426503}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.js", "require": "./dist/cjs/index.js"}, "gitHead": "180f9b6b41042017562d0cb8ee5ebb4d053de2c1", "scripts": {"tsc": "npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outdir=dist/cjs/ --minify --sourcemap src/**/*.js", "bundle-browser": "esbuild --format=esm --platform=node --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.0.2_1706356678672_0.2882833113402199", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@asamuzakjp/css-color", "version": "1.0.3", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.0.3", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "4fe0fd729b9c1163456005c5230dfefb00936bd6", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.3.tgz", "fileCount": 18, "integrity": "sha512-0b5QM+7CT4nLTfVh+v5kflqhK0KLUwawJhymiAWsvS0P9t6qwqoRnwnmnqnPRRoh2rSgi+dGKwWjnCtABJNTnw==", "signatures": [{"sig": "MEUCIA9mYwP6eIkw7n64Dk5kSwA790e28BbHZL7aGEzcwBdYAiEA/PWbYQLnJgquO54d6JH6iY1xvL57EV8s1GbI7LrcdPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427100}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.js", "require": "./dist/cjs/index.js"}, "gitHead": "dee5526428a7e17554a91670fd02004f3bbc405a", "scripts": {"tsc": "npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outdir=dist/cjs/ --minify --sourcemap src/**/*.js", "bundle-browser": "esbuild --format=esm --platform=node --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.0.3_1706358826520_0.5188625247962582", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@asamuzakjp/css-color", "version": "1.0.4", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.0.4", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "a99fdfec05542dbdfe362414f3a166c9078464a6", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.4.tgz", "fileCount": 18, "integrity": "sha512-1F6N0WjGsL4ZmQ7PS+hVl2Bb6yaH3wvXpGLJHjaCoWqsowkI04e9I9KqU2Bbdv+O6RBYv8XrjkPObQixlQHveA==", "signatures": [{"sig": "MEQCICz/Us/AkwHYKx1/JsMxyr6l8E7OIKewZaJBt5LFiSR6AiBeWkCVc7mikTUsQSnQfwSTXI4ZDHbXeXCNdMFo5pCLvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428140}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.js", "require": "./dist/cjs/index.js"}, "gitHead": "061be89db2a5f79053c1407fee92279409b75283", "scripts": {"tsc": "npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outdir=dist/cjs/ --minify --sourcemap src/**/*.js", "bundle-browser": "esbuild --format=esm --platform=node --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.0.4_1706362051833_0.8186441816255927", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@asamuzakjp/css-color", "version": "1.1.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.1.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "2df51aa4d61076faba8b97e4f9506b11a5b9bfd4", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.1.0.tgz", "fileCount": 14, "integrity": "sha512-wWXyb6PPgTkfqfA+pRAr0fIKdUMwGxt0gPpnVuQ4ArcRcXWg3VQu+qHCFuZ4DaxNCu4pB0OYoj7FC3pxEtq55A==", "signatures": [{"sig": "MEYCIQDCfHMm7FAfqQeup6hxh9IrdpXu3aoWXH3GO6unW58zygIhAI3ketNBeHmutp/nuqVQ9g8skkHKB5Iy5iOZ1/4XEom1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423596}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "da2df933c4c537edc07c7f0031d61249c665fb3f", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.4.0", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.57.0", "esbuild": "^0.20.1", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.1.0_1708740729858_0.20652832514944586", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@asamuzakjp/css-color", "version": "1.1.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.1.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "0e8e4ffefaac6e7275aefebcbf18f62be0c9836d", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.1.1.tgz", "fileCount": 14, "integrity": "sha512-wfKjiBrlQC2bexTisWDyYrZToSGKV3AYZ44MTdYb2bJiQmt29VtW43MDSax8pAghfmDKuNg08tNXBGcy96b9jQ==", "signatures": [{"sig": "MEYCIQCVIfNI75u8y79VEJxPlBFaSaOYnQ5hKnxHIQdy6mYxrQIhAP8Cn/HV22T1NrvjeN/tQEhayHfAKH+xkzXMQkBbr+f6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 695665}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "809dba7bc6b7036850756a6e8512be3eefb56d03", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "overrides": {"glob": "^10.4.5"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"lru-cache": "^11.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.15.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.6.3", "neostandard": "^0.11.8", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.5.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.1.1_1731813009508_0.3469340615023675", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@asamuzakjp/css-color", "version": "1.1.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@1.1.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "da5b7ac6cc51b08e08f2259d7306802a3cd29d92", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.1.2.tgz", "fileCount": 18, "integrity": "sha512-Pj8hBJj1tKHxVs79CGzj8iMzLXswLXrp/X8Yk+uxIC3wzeNolRLHBUi4UV+bAFL3+Y4cleSgDaC/dwoeAvjiqw==", "signatures": [{"sig": "MEYCIQDdBqI18OVVj4a+k+ImqKs/ATx0l45Oo7tWs1gJrY6fngIhAOU18uFCtAdeEXRk9BNKyGGPVhCLUu1ZsxdTewN/yLt/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 720229}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "4aba7917ba1ddf974867d4be292a14a089c32de5", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "overrides": {"glob": "^10.4.5"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve, parse, convert CSS color.", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"lru-cache": "^11.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.15.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.6.3", "neostandard": "^0.11.8", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.5.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_1.1.2_1731848608502_0.9169002781976889", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@asamuzakjp/css-color", "version": "2.0.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.0.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "f7c85c3d0a32ed75d0f9008a196da96ebc5d3895", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.0.0.tgz", "fileCount": 18, "integrity": "sha512-y038hFi18iBVsHz1lZZTDhMyXIp6DXdZFhJwQvH5i+pKfEG138z0UDBi1pvz6qFWWMZMVhksQnfps5W70MTN+A==", "signatures": [{"sig": "MEYCIQCmj9fgQrHIVL7R6o902sXGXwztlr8eBxcVOwOX3GyHTwIhANRPPBhsEaTJJNgnrAzJxvYOHQ3NN7gdOx8bUK/Yg9Ki", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1171268}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "2bd01f59626912dda0dd4e74b65c32511664525c", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "overrides": {"glob": "^10.4.5"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.15.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.0.0_1732880368653_0.5287786407078625", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@asamuzakjp/css-color", "version": "2.0.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.0.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "034ca9dd6f3fbeb24fb162a04d1e8b51f4071a45", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.0.1.tgz", "fileCount": 17, "integrity": "sha512-q9rufhO6u+1Se6ZOqflgTnzk2b6qWDCCtxzSTZTyx4gYmx125Ek58IyGpw9of04wGqVELg/WAEp/kRx8KolNbA==", "signatures": [{"sig": "MEQCIFAGiVf+MkrohwwsaizdXLL1Msi73TeOtqhklg9EBKY4AiAzHYoJl6uH27h+Xr6qw8U6cDXwmxMHOv0bu4iDqO6Ypg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1169244}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "c0b70590c64a50ee7c149a9bfcb4a170c4f6a7e0", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "overrides": {"glob": "^10.4.5"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.0.1_1732931066228_0.9582168982834534", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "@asamuzakjp/css-color", "version": "2.2.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.2.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "8d7d095558927e27f346349a5fd15127474ee041", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.2.0.tgz", "fileCount": 17, "integrity": "sha512-3YFjdkmcADmBBz1ve1T++UgnbjJUFnssl6cRHaTY34oKjDbHy65T/kTHpO6oph2Z1wdZkeFt9PvQRjXccAufUQ==", "signatures": [{"sig": "MEQCIHJgZN5tZU7tEN5a77kZZOgqGeXP3cLVOv2hBPh/QaCgAiByZS3LPRZuQ61n7xkjoKR2RByGeFVXn1S7xazjodWnKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1253261}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "71d3a99042708f38fded9d747a7541db24859090", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.2.0_1733299781050_0.5601220755636662", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "@asamuzakjp/css-color", "version": "2.2.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.2.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "9cd258617279c91bd183f92a4e0bfbd3a5b6daab", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.2.1.tgz", "fileCount": 17, "integrity": "sha512-4/RoWIs1GTq3Bm7Xyh1cjRC1h+nyMqoB23Twr5vnbyNqWx5FTbBq7Pb70IovyL6vZbi8vlsoyx03bIBl0U61FA==", "signatures": [{"sig": "MEQCIGTPaY2EWWyM7/KNCFKD+QnHxT25nu4kbpytjBesm3NTAiAWq9EeYrpPBAof7si8Gx/zAnY19KYAjcLaQOlpmCr8AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1255435}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "5d5c1aa44d5c4bd68b12c8da5c0c2e09c2efcf0a", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.2.1_1733301415979_0.6508955368479994", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "@asamuzakjp/css-color", "version": "2.3.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.3.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "c746b8b9c97642caa70cffa0d521a4b03f42bcd9", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.3.0.tgz", "fileCount": 21, "integrity": "sha512-vDI1tl26mhkepjjDW6th+vM8p6kEqUsdQy+GwYkAWIS7Uo85nV9Zf8FZLb4RGzWjKeys3KOHEyUwRd20TxdXBw==", "signatures": [{"sig": "MEQCIGt+wWBVJbMjcUXlV4K9rtCCdWlWlqW9Y4NJxSiJhAT7AiBNc7q1l2vWdXEnAL4j4Wh28CwACYXsg4VUB04WXtQh9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1325841}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "f4cb357a9572a536dc1a166da965157434667973", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.3.0_1733549817384_0.263433266227739", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "@asamuzakjp/css-color", "version": "2.4.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.4.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "ca5d4646b5409dc01af2034fc830477e053b0b65", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.4.0.tgz", "fileCount": 23, "integrity": "sha512-7UjwVHh1yFoJS8phGqWJH/spSaPrP6hO1WTnwlEM33gISpFruOKiaD4TI4411HsQAkkRA8neHZaqj+aHcTlaUQ==", "signatures": [{"sig": "MEUCIQDxS9lLGPHlOGVeDYh9McpZdwHtVwqRHeGFd2GylQgg9gIgMRDCRwt/XaLQv6yooiBieV5Vy5b9SVDt6ubMKLhr5qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1310382}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "5c4c97e42a6d11d22024dd141a671ff1520797b1", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.4.0_1734062149546_0.47255257371680837", "host": "s3://npm-registry-packages-npm-production"}}, "2.5.0": {"name": "@asamuzakjp/css-color", "version": "2.5.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.5.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "11bec53b218ed231f042eda5fc82a788d373f99b", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.5.0.tgz", "fileCount": 23, "integrity": "sha512-LuUSDuz8MPTAPOb9Zvd6b/D2nUyG7NZaI8OGZhu45xgQTqLQOC29HLfadPMrs5C7JY784WRtLMvLmQhTpIY0Pw==", "signatures": [{"sig": "MEYCIQDHrkkV6fK5qCrIlN/6AoZpfoptkCDL8icghOeaWWyhqQIhAIMTIiORpUc/FYEzDFeDGSf8Ov+ff2VkhwXvkJnPqi9n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1311190}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "939bbf9e7675127391b88b5d372e9f1f1031de00", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.5.0_1734136425560_0.3159857088639706", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.0": {"name": "@asamuzakjp/css-color", "version": "2.6.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "d34c0e449de976ba1c403b258c413a6b660b864e", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.0.tgz", "fileCount": 25, "integrity": "sha512-/S8Z9048cqsR8xpzLnky48Xw0HTPus+TeO8GS8eru3RUAT0YwxCmLuKxU+6iUJCFthiNshvHRTn2cY4nCfoauQ==", "signatures": [{"sig": "MEYCIQDCOCAHgAgWDX0QMvsi0QTAt52BHB2jftDih+eMjKl6VQIhALIsDVWUgRwv0UxAC8W1YzezYxi4dgJ/nT1qaU1RTV1L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338050}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "a7896a26ed7411f7455faca04704b97f63c2e14f", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.0_1734358417167_0.8768707078942677", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.1": {"name": "@asamuzakjp/css-color", "version": "2.6.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "1a36dd3edc0980b24b1723b7f53700884f677d37", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.1.tgz", "fileCount": 25, "integrity": "sha512-QRBdygAtbi9H2SUb8p5xU+TjHfBIidoSUkmRhbMG8UoTGucdXblcP9T4JnzKKvv+FaiQ86SynsECTQ8SDYP5HQ==", "signatures": [{"sig": "MEQCIGLVLj3Dzy0uoI9ezR3fOyrqZXWydYYPjSJiKpYKwjeuAiAU29JAc/fBrIRG/11jhowJEQQH1MIhyivp89Ub9FCb+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338235}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "f10705a47a34f852b0f7324efb25b4f4a6ad2b04", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.1_1734361350790_0.7661249492691449", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.2": {"name": "@asamuzakjp/css-color", "version": "2.6.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "55f6a7101657e66047bfe244bd942aa5a75c0c44", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.2.tgz", "fileCount": 25, "integrity": "sha512-KDnElUF8LWAFGLqaJBYbgOXS0RIhOHHFLWD2DV3jW5EPrCrOD8Hfco+qn8McBFnfSss33WUarKW9PTyqeXDy3Q==", "signatures": [{"sig": "MEQCID4kjZgVr1N/a4pGImD0QriK9Ee4hjcw6lcTzxbjMiLuAiA5p64pnTbwa4njrTpaVm1hqgn6Gy0ZvahsoPImHqi2YQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1344743}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "c4332404e19065ff49b654a046b432da64975186", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.2_1734403472113_0.6232929540641479", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.3": {"name": "@asamuzakjp/css-color", "version": "2.6.3", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.3", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "68d12a64f9fb356460a17b7f7ef55591d49160f4", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.3.tgz", "fileCount": 25, "integrity": "sha512-zrMP1u8F3U92LSsuys9jV2WwhidRVfXX5Jyqf533h9Cw9y/VuKToUMjapv3ZKC25p0KkXx75wMGKD7UIkl8MCg==", "signatures": [{"sig": "MEYCIQCYQNscP1DKrm3RhPtVxqXa0bymukYAC86C6ANdF4RGIwIhAOfILE2oz5ZGxEB6EHf7Gvm0mDDjXEd6zq72OELfvHi8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1345324}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "303f42a469a2dcdf34ab6e7d070813d7973644b8", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.3_1734434102370_0.44424491949948686", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.4": {"name": "@asamuzakjp/css-color", "version": "2.6.4", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.4", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "47126da705a9702c95173abce8bbf1bc004ae7ea", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.4.tgz", "fileCount": 25, "integrity": "sha512-4qX/Y3Hr+Z3B93fY91GMCL4Dm+nvl1a/7iq0JG5SfmblbqW/l8esXlxKTHsOeXIiROIY9uuu83I3eXpTyIMMtw==", "signatures": [{"sig": "MEYCIQC2sPgh2x4ewGEWnj5B25G7vK78DWMJZsPJC2XyNRx/GAIhAL3PWc4d59I91jOUWI1EOlDCgxCaEEq0t++B9cNE68o6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1350017}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "2d1a66f8bc9cbbce41340f4b2088a1608eca6763", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.4_1734701797956_0.9965031710722687", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.5": {"name": "@asamuzakjp/css-color", "version": "2.6.5", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.5", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "9420b664e6555b7a966a2a6b3ba80fcf4a8145ac", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.5.tgz", "fileCount": 25, "integrity": "sha512-MIF+O+I0TadFOcvMTxAKvxz5s/2AeqX0Zk546llW1Sc62rH7zBTF5i21r3sJYhoFEZvHb76tNQfnWqesn29h8w==", "signatures": [{"sig": "MEUCIAJzY+1639md54KEAtBJHpbzSRxc1SZBHzQ+kHeZw9swAiEA2KkO3BMRcHXZZpnBXRTffijqCPqpEx4CAPzzBdzphOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1350244}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "6625d3e41bb1225ed1061d969b1b46de1f29c34f", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.5_1734704264254_0.2872601022142969", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.6": {"name": "@asamuzakjp/css-color", "version": "2.6.6", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.6", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "8df73ac98812d710197aa8cc6f14ec28c5ad9b96", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.6.tgz", "fileCount": 25, "integrity": "sha512-6kThlAl+6+YXKwArRfkyptBK6JbpBqGfTYlpMOINha4OIU/gIem0GPQ1a77OusA3N/1lWvVgL21GRGlvz7UbUg==", "signatures": [{"sig": "MEQCIF7/iEv5wsyWtkFxCFO62qw+2q0hfgOBpGCZvYrKqMiXAiADrYxVaCDznZQyzvHnFV39ohXUcSNmHlBh3XFsN8bTWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1347776}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "31321349ece396b7f372594197d014a4dc75cb54", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.6_1734733275742_0.8647146695288195", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.7": {"name": "@asamuzakjp/css-color", "version": "2.6.7", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.6.7", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "35cb3c9c13bf1a189faaaf0cbfd9ba0d38ca08e3", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.7.tgz", "fileCount": 25, "integrity": "sha512-d95sStIjZzGoAYPHHT7A6oyU8Tr+BMYOebog/U9kVkRonH2AUA0QmDOqEVQvpNbLuj0YqesS08RRW+bhrR1/jQ==", "signatures": [{"sig": "MEQCIA795943YEIvg2qqZm6oJVLNgHZUoqJqEqa06lKSTk9oAiBihk7DH9JFXpLJDl2BVx+5NOfDyIUKdMeOMqYsQaLJqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1345233}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "9304f66dc3d691d3eb57baff5b77a33fda95b867", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.6.7_1734796160155_0.7387814384787859", "host": "s3://npm-registry-packages-npm-production"}}, "2.7.0": {"name": "@asamuzakjp/css-color", "version": "2.7.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.7.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "4a831859876778b7d4c731a18bea6d3481f31d88", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.7.0.tgz", "fileCount": 25, "integrity": "sha512-Q8vPhtSooMPXAMif1gSk2TorlnlT+1hkgryGayGNKdxqdptj0o21W9q9JYKCJMMKa5hVfOgsru6zI7fhXzv/vQ==", "signatures": [{"sig": "MEUCIBD+IBLBGZ9wwzUrZpSxb2H4kkAoGjtGXw0llZ+jSOfhAiEApbbJJGP1umDi50NxeTArIZ/6hcLDO6sczJbNAbopru8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1353352}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "17642508362962c136dc3d9e411d8c48952733b1", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.7.0_1735259158015_0.12203327687856236", "host": "s3://npm-registry-packages-npm-production"}}, "2.7.1": {"name": "@asamuzakjp/css-color", "version": "2.7.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.7.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "ee1a577530c377cc9fe565cbfb44d200f8b8a5c2", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.7.1.tgz", "fileCount": 25, "integrity": "sha512-FrLFymoHF4ygxluMLQFH6TNaOFvVaMj28//YmfQBrDieEc4gvcCF6mJyI4Bdw7ggKWbD3o/vlo4aCI8I5NEtgQ==", "signatures": [{"sig": "MEYCIQDOiKs63CEGgt48wy83tsMfbj8NoyhYjAvzH9ffN4IkxAIhAM7RA5WmaS9XBtMC7SflfrhPi+IRb0EKdSEaTr+lhP77", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1352875}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "d8607c5a737f9539e676f51d168991787e324c7d", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.7.1_1735346531386_0.8699185686691513", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.1": {"name": "@asamuzakjp/css-color", "version": "2.8.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.8.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "2f72de2efaf7fa6545b0c37c9394b4cf705a7a2a", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.1.tgz", "fileCount": 27, "integrity": "sha512-dnEjj0gnL1+rquxI2MIS661jWefxu5XF2Hya7dHvl2jK+MMg2zie4kNxHCmTTyi63tuAiKY4hCXa2rT2/rG/2A==", "signatures": [{"sig": "MEUCIQCypCiQkmGvp0MhSb5SMMwkT2xY4HaivAYoa9TIRcG68QIgF6URH39stvkQLBn7+8092kjQftzSHTiYUekK45ovqtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1797897}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "e8e094dad48b0d0f84de6764c50ca49c1fabe9dc", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.8.1_1736067443399_0.2682837604234294", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.2": {"name": "@asamuzakjp/css-color", "version": "2.8.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.8.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "817e84b0cc9f426379f4b549836f32b670c43649", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.2.tgz", "fileCount": 27, "integrity": "sha512-RtWv9jFN2/bLExuZgFFZ0I3pWWeezAHGgrmjqGGWclATl1aDe3yhCUaI0Ilkp6OCk9zX7+FjvDasEX8Q9Rxc5w==", "signatures": [{"sig": "MEYCIQDJBZTAVq7pUKrhRIy2CL6FYxJQZSDkUBpJLS0vmEQF9gIhAJkuahfP34ztR9074RfppzWwJFI+Y3jfyqQFG3eQQqUf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1882366}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "browser": "./dist/esm/css-color.min.js", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "621624e0a072ff2ee132c8c81ea6067325747b1f", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.8.2_1736340888520_0.4313996222261727", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.3-b.1": {"name": "@asamuzakjp/css-color", "version": "2.8.3-b.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.8.3-b.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "529a9361048537159a126d85be9a57a0a2bf255c", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.3-b.1.tgz", "fileCount": 25, "integrity": "sha512-H2D+f+wwFx+4KpPCwKYPye3OWGCV6Pc0dzDkPvbq3l54IK9z5HFCkqBVeCO0GtaWaSMxnic6HCWHQYP2HdnrZg==", "signatures": [{"sig": "MEUCIQDvWgEi3P112iLdeamJecE1SPzWyWk4hsc/OBnsHaLEeQIgcaPz+po/jOIRhzbUBl2shU9t+PF3WakHSMG2w1TiQJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1044371}, "type": "module", "types": "types/index.d.ts", "exports": {"import": "./src/index.js", "default": "./dist/cjs/css-color.min.cjs", "require": "./dist/cjs/css-color.min.cjs"}, "gitHead": "b74fec53d896d77aecd5a1c7f60f957671663513", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "esbuild --format=cjs --platform=node --outfile=dist/cjs/css-color.min.cjs --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.8.3-b.1_1736507673561_0.3780786228944837", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.3-b.2": {"name": "@asamuzakjp/css-color", "version": "2.8.3-b.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.8.3-b.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "e98f6d8577f0d6c5c3b6d9afca1a012ba7db3683", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.3-b.2.tgz", "fileCount": 28, "integrity": "sha512-caREANPZtL+FpyN5cJMonkm4jJ0EMarV9PdGG8HkkqmPJIbnXZXn1h78WdorZKTgOt379aGkt1CRMd2YNV9ycg==", "signatures": [{"sig": "MEQCID8o8HR22QuHEo78tLZupk/pOseZ+o3D5d3/V85zPxiyAiB75j0SSHdY6eWuL3LXfHsKWXym824E7aTWu1ykM785nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1375967}, "type": "module", "types": "types/index.d.ts", "exports": {"import": {"types": "./types/index.d.ts", "default": "./src/index.js"}, "default": {"types": "./dist/cjs/css-color.d.cts", "default": "./dist/cjs/css-color.cjs"}, "require": {"types": "./dist/cjs/css-color.d.cts", "default": "./dist/cjs/css-color.cjs"}}, "gitHead": "b2745176bc659e975f685cc86179e9f5b528cafb", "scripts": {"tsc": "node -e \"fs.rmSync('types',{recursive:true,force:true})\" && npx tsc", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle", "bundle": "npm-run-all -s bundle-*", "bundle-cjs": "tsup --entry.css-color src/index.js --format=cjs --platform=node --outDir=dist/cjs/ --minify --sourcemap --dts", "bundle-browser": "esbuild --format=esm --platform=browser --outfile=dist/esm/css-color.min.js --bundle --minify --sourcemap src/index.js"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^10.1.3", "tsup": "^8.3.5", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.8.3-b.2_1736658503729_0.2670909306755618", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@asamuzakjp/css-color", "version": "3.0.0", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.0", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "9db2a28c819b9903bbda017915b3081f76fa1df5", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.0.tgz", "fileCount": 74, "integrity": "sha512-APrXjl8zFbxMw8N8sUciUcK24sMTu1FJhBMM418sXbuUQ4dzVWHBAU7JpX5u9venGSpoaPA39jEB7zOskTsXtg==", "signatures": [{"sig": "MEUCIQDpt5sJs7c81AkVT3zougxXfCicmOTgl4N2CPySo6v3WwIgIltFPsu5y911xejU87Gb+bu5lXiiwDyLE5O7D/VacKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1403394}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "25a4dc50a7888d8bff2c6d57f64d677549b5e9be", "scripts": {"test": "pnpm run  \"/^test:.*/\" && pnpm run build && pnpm run publint", "build": "pnpm run build:rest && pnpm run build:min", "clean": "rimraf ./dist && rimraf ./coverage", "publint": "publint --strict", "prettier": "prettier --ignore-unknown .", "test:lib": "vitest", "build:min": "vite build -c vite.browser.config.ts", "test:knip": "knip", "build:rest": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./tests", "prettier:write": "pnpm run prettier --write"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.42.0", "vite": "^6.0.7", "eslint": "^9.18.0", "rimraf": "^6.0.1", "vitest": "^2.1.8", "esbuild": "^0.24.2", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.15.1", "@vitest/coverage-istanbul": "^2.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.0_1736867648169_0.9773921679345425", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.3": {"name": "@asamuzakjp/css-color", "version": "2.8.3", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@2.8.3", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "665f0f5e8edb95d8f543847529e30fe5cc437ef7", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.3.tgz", "fileCount": 74, "integrity": "sha512-GIc76d9UI1hCvOATjZPyHFmE5qhRccp3/zGfMPapK3jBi+yocEzp6BBB0UnfRYP9NP4FANqUZYb0hnfs3TM3hw==", "signatures": [{"sig": "MEUCIQCYH6PeqLrQUcldhBWzBdLYrLavVTaDECpMNwGMO5b8QwIgAmPrAcmJLOkuOwkT7bIV7LowMZ49vjiZ7ynSmU9qXAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1403394}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "25a4dc50a7888d8bff2c6d57f64d677549b5e9be", "scripts": {"test": "pnpm run  \"/^test:.*/\" && pnpm run build && pnpm run publint", "build": "pnpm run build:rest && pnpm run build:min", "clean": "rimraf ./dist && rimraf ./coverage", "publint": "publint --strict", "prettier": "prettier --ignore-unknown .", "test:lib": "vitest", "build:min": "vite build -c vite.browser.config.ts", "test:knip": "knip", "build:rest": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./tests", "prettier:write": "pnpm run prettier --write"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "readmeFilename": "README.md", "devDependencies": {"knip": "^5.42.0", "vite": "^6.0.7", "eslint": "^9.18.0", "rimraf": "^6.0.1", "vitest": "^2.1.8", "esbuild": "^0.24.2", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.15.1", "@vitest/coverage-istanbul": "^2.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_2.8.3_1736944211181_0.4274757619227001", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@asamuzakjp/css-color", "version": "3.0.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "7a2616c08be9658963d85d3e00f2b951bd871f61", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.1.tgz", "fileCount": 78, "integrity": "sha512-qaU3hPi2DOcuPq8U1jRJ8f8LLIj14U1JR34V32qODhg72CekXHjdAJV+CGQMd+Br5OusfyYaxGLbUbIWDWoT7w==", "signatures": [{"sig": "MEUCIEUlsCsMeKyHAMSE/dJYspT2LYm/sVwJiWsO4sDxp4Z4AiEArL/XABWwQ2fjV+IDeZ67dPiKE48aCma8O5vz/5IK8RE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2000368}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "3d512b552f623e027cbf73b547bbd975e8c38a6d", "scripts": {"test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\" && pnpm run build && pnpm run publint", "build": "pnpm run build:prod && pnpm run build:browser", "clean": "rimraf ./dist && rimraf ./coverage", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:knip": "knip", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.1_1737769354872_0.630116717417653", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@asamuzakjp/css-color", "version": "3.0.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "96a2c03467342fefe78654439b2151db64615ccb", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.2.tgz", "fileCount": 78, "integrity": "sha512-a8puD5V+O2iGfm/Y9Ow+6kGLfdGB5Y32Mg7LRLdhLTk7fhTdBlRJzN4kSMZtytjywVgZxH9IInJjQ0aVT7L3xg==", "signatures": [{"sig": "MEUCIAHHRLv6hA1mcikqYrPxGvxGSUHwukeo1yOo4tO+GlpaAiEA2g7L0p02uD2SLur2zVgaGH08fvBRMvN19jdd9UKTRfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2000375}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "8a969744a3eee54166516000fc05f25c95c48e42", "scripts": {"test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\" && pnpm run build && pnpm run publint", "build": "pnpm run build:prod && pnpm run build:browser", "clean": "rimraf ./dist && rimraf ./coverage", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:knip": "knip", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.2_1737779329105_0.09079185296407077", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@asamuzakjp/css-color", "version": "3.0.4", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.4", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "0ac250957aa7a1c13651d735f34bfacc8c6b032e", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.4.tgz", "fileCount": 78, "integrity": "sha512-JvJJWj+qfjCf4zoYLoNANTm7ZnYlUS9pltsS377qjW3o0ry/FrbgOvSW+VGNfyOvD95uldikfsi3oem2vc6tsQ==", "signatures": [{"sig": "MEUCIEFZ8rWv5cVjq3F23dT684Hmbl7Cf2YLWQrlntjUPON0AiEAupXV6pSwvlIJ2SOmej0ShUbmCtDyXEMsp/+3rjwAYY0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2001698}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "1f546f9913dcd21df5ff01ffcfbbcfafd664b923", "scripts": {"test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\" && pnpm run build && pnpm run publint", "build": "pnpm run build:prod && pnpm run build:browser", "clean": "rimraf ./dist && rimraf ./coverage", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:knip": "knip", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.4_1737783473627_0.21244522434635393", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@asamuzakjp/css-color", "version": "3.0.5", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.5", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "ad21e767f20d799547238275ce30ed4e47c18716", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.5.tgz", "fileCount": 85, "integrity": "sha512-rN/3RCmcRYYHWW+hM0ZQDFSTLyn4F3kbrh+jqfeMZl540fO7jbjHv0GFv53atYOY6yjpOIcMkAzuuyntfuTH9A==", "signatures": [{"sig": "MEUCIQDN0ffMr9YvFNJ5I4xYvINRwA/aiAFKo2KaECAjHwmvKQIgSfIw2NMJ50iou0BjLirv2Dkds/UBWaS+SIKadnT9Zvg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2042522}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "7f447b6ebb66889fc23a4062b946c630fcacba99", "scripts": {"test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\" && pnpm run build && pnpm run publint", "build": "pnpm run build:prod && pnpm run build:browser", "clean": "rimraf ./dist && rimraf ./coverage", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:knip": "knip", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.5_1737863531278_0.35108664990161875", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@asamuzakjp/css-color", "version": "3.0.6", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.6", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "182d9fa00160f79b23e4907e9907bb7305ecefe8", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.6.tgz", "fileCount": 85, "integrity": "sha512-AP2gkyJFU5qhmBgRZCoKfl8jqQLawJTGC6GTvgy5fn7LMIxDUKo9pg6x9+8pmv+jmXo+v70vQSq3FI3jfUP7Kg==", "signatures": [{"sig": "MEYCIQCr+0T7HxffqK0DZdPYO85r3hWJVDW4UpBQbbhq846/OAIhAPdQn7CEhwDTxpve493uyGYmtMa2kg77jpx16Nf3w1aI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2041710}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "ca9ac15ad0f1b0396d17ce6b5c0fbee848d32b0f", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.6", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.6_1738362743370_0.7464494531419394", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@asamuzakjp/css-color", "version": "3.0.7", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.7", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "c1bbee8030ba8cabab0a82484e86e537072776d8", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.7.tgz", "fileCount": 85, "integrity": "sha512-hsROoNn4Y6DU9b/Og9bTCu7nWDjHNTHGqjKZdS/lZZ0JM9NVrKyMjp5LqMHL5dUOvwF1EbVmuG6q2FIWBysfEA==", "signatures": [{"sig": "MEUCIQC92ILttAfjausvxvfjvxZQccNEKo28jmMc36+Mu8FNHQIgRdBaiqYM60ebWMHLUX6ynsEfFOFx9ccgNeRJfEPqryQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2045587}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "e2335d9c7aa8d213a64342686bb3c915d532771f", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.6", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.7_1738472863809_0.9446910709805958", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@asamuzakjp/css-color", "version": "3.0.8", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.8", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "bec188c3b258483a8957df889f7179d736ec1599", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.8.tgz", "fileCount": 92, "integrity": "sha512-cVcivPZ0aKWTE5PuaY+/x7yPOJlA5nlhz44VQKlWMnKyLe7qCpuol5ilYvAQSw/KxmDq12rZ9kPihHdkrpvldQ==", "signatures": [{"sig": "MEQCIBvNGSkJhM8w+PSBDz7/GBcGPE2dNOv5oFsa+qbBCf99AiAtaKrqxw2FuvJsTKe4TrU0DsDf7Ck+jgdzlc+FSwsceA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2128338}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "9a80ad9909ecfdeae6dadc566c631ae7836d2801", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.3", "devDependencies": {"knip": "^5.43.6", "vite": "^6.1.0", "eslint": "^9.20.0", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.14.0", "publint": "^0.3.4", "prettier": "^3.5.0", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.8_1739255639819_0.06502328646479194", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@asamuzakjp/css-color", "version": "3.0.9", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.9", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "420ac9d7b7bd43008e9b36c03525642a071685f7", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.9.tgz", "fileCount": 92, "integrity": "sha512-u/vJXVMQajT/+MBmokhdSAwI2Loa1bdKp/bTCXziGD+oE1m9aRnltvieAvpYR7WHiLs4IIWikcq2l3k6C6GdYw==", "signatures": [{"sig": "MEUCIEGopDTw6GP8A41G8pn2b027JmCyEvC5EqWwEx2s1qpUAiEA7YFDrI06uDsomvzlAIvyOf4T1pCyXUJmsiatMOg79Y8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2136164}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "2d36d296384768feae298912f23310d7dc100808", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.4.0", "devDependencies": {"knip": "^5.44.1", "vite": "^6.1.0", "eslint": "^9.20.1", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.15.0", "publint": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.9_1739535446022_0.6342659382392914", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.10": {"name": "@asamuzakjp/css-color", "version": "3.0.10", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.10", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "170aee94d4557842bc4804d1125f18ff5c43be53", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.10.tgz", "fileCount": 92, "integrity": "sha512-LpMJfPAVp6eCMabw4UxojJ7dnjcZR2RVFBM7YWywk3KAeSF0IZ7FXKOFcmurWgpRCLz5PnE8SwedagzLR5BCPQ==", "signatures": [{"sig": "MEQCIGuz95dN/KOEh/oPggy9PTEIGduIY9e/k/hXaEecMeJZAiB1XdnGW4HewZZ/IqneAJ4IDHT3vyy4C0/5eEhFNTvKFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2136477}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "fe098fabbf152c7f8378847c9ff1b42bc4cd4d43", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.4.0", "devDependencies": {"knip": "^5.44.1", "vite": "^6.1.0", "eslint": "^9.20.1", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.15.0", "publint": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.10_1739580489046_0.31627708644621166", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.12": {"name": "@asamuzakjp/css-color", "version": "3.0.12", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.12", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "d15dc8f4ad6663ea211f9361280f5413201efbf5", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.12.tgz", "fileCount": 92, "integrity": "sha512-pjiqCzOm3L2YbDGrCwZm3fiank8DFsu0Qidi70sx6XqAob/UvE14bd80BDsy3z9/e+BupDzu052K0Qb6/LcYKQ==", "signatures": [{"sig": "MEQCIENQee0qRaJ09EyeAI/iwzTwkOsPTSOFc/NLx0suLaQuAiA4wEt3bMFA5kzc3j0A3gA5foFDNcPsRTceHDADduESIg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2146473}, "main": "dist/cjs/index.cjs", "pnpm": {"overrides": {"esbuild": "^0.25.0", "vite-plugin-dts": "4.0.3"}}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "df1d70922e085a04b534cc0bc2019ac33703d8f0", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.4.0", "devDependencies": {"knip": "^5.44.1", "vite": "^6.1.0", "eslint": "^9.20.1", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.15.0", "publint": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.2", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.12_1739862282623_0.6416990405014806", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.13": {"name": "@asamuzakjp/css-color", "version": "3.0.13", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.13", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "aab016a736a370ca8fe66076f6b01dab3da602fb", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.13.tgz", "fileCount": 58, "integrity": "sha512-ENP7EA1x67tMLaGu15rLZ52kKDvjT92OQyFXvStiCOZSOn8N7x4I38CJs4+vGQ7hjqdxhMVBZiQLQIktTAVgGQ==", "signatures": [{"sig": "MEUCIQCaGdRkebhHm0ayY+sLCA5XziZh8cie12GdJjtDj1Zd3QIgNkW+uaVrEqDfW9Kh6WCDdFzujS9G/uo+3FyFshF5L54=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2072221}, "main": "dist/cjs/index.cjs", "pnpm": {"overrides": {"esbuild": "^0.25.0"}}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "a9093bd31abeb91d3bcb77900a2f0dc4fa3fa733", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.4.0", "devDependencies": {"knip": "^5.44.5", "tsup": "^8.3.6", "vite": "^6.1.1", "eslint": "^9.21.0", "rimraf": "^6.0.1", "vitest": "^3.0.6", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.6", "prettier": "^3.5.2", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.13_1740356224781_0.7886321130729779", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.14": {"name": "@asamuzakjp/css-color", "version": "3.0.14", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.14", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "9a1bd47ffe9184307e9fa826e05e6540a5a2921c", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.14.tgz", "fileCount": 58, "integrity": "sha512-5oV4rn3v1VfdRB07eo1I6TIFxTPUNxoqXjNEaHmksTX+fqnoyEQve/XttPlUd9c/6Zxz4bi4tr33e7xqsVQUXg==", "signatures": [{"sig": "MEYCIQD/E6ja03okRMQrXkqfiKGIIOyZIMgqxMr21QeqLZsbOAIhANNhCnrkVlJqGpM1rclPJvNtT+dxOtjVEdy/YzOCc0JJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2072152}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "235056cc8ca32266454e1378a1eaefa139c4bd18", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.4.0", "devDependencies": {"knip": "^5.45.0", "tsup": "^8.4.0", "vite": "^6.2.0", "eslint": "^9.21.0", "rimraf": "^6.0.1", "vitest": "^3.0.7", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.6", "prettier": "^3.5.2", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.14_1740523910522_0.37669469956774093", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.15": {"name": "@asamuzakjp/css-color", "version": "3.0.15", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.0.15", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "9e39fe39a73e511b6010968b184ad96b9e40c5e9", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.15.tgz", "fileCount": 58, "integrity": "sha512-6Rvemf1V1EZbeu7z4zgjdWl2oOWyX2r2njwUBAtlp09t1uwIwAfjc0Fsr18Ij7WbM09GEers/ZYqGNDUAV+udw==", "signatures": [{"sig": "MEUCIAuv3F73CuLsNIR5uMJhVGUzDMy1lV1Iv+AC5rZVlx/vAiEAzzumnKQPI3RYyHC3qpOUKPzbvQF1dxA70ygygIpLf30=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2079971}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "251998ee5293dbd11da5136dec3d2a3ca24da245", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.5.2", "devDependencies": {"knip": "^5.45.0", "tsup": "^8.4.0", "vite": "^6.2.0", "eslint": "^9.21.0", "rimraf": "^6.0.1", "vitest": "^3.0.7", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.7", "prettier": "^3.5.2", "typescript": "^5.8.2", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.0.15_1740955246796_0.23469407915612406", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@asamuzakjp/css-color", "version": "3.1.1", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.1", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "41a612834dafd9353b89855b37baa8a03fb67bf2", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.1.tgz", "fileCount": 58, "integrity": "sha512-hpRD68SV2OMcZCsrbdkccTw5FXjNDLo5OuqSHyHZfwweGsDWZwDJ2+gONyNAbazZclobMirACLw0lk8WVxIqxA==", "signatures": [{"sig": "MEUCIQDyF8KR13iH9eRYeyrnhf6vPcLL/zBVGKgAh7q9D3BTJAIgZG/yuh3/C0EXctQ8WnBDp/S9wlS9GoTYhX1tOx+xKZc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2081685}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "d3e1b30503938225f1d95e6c515a0e9265b88704", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.6.1", "devDependencies": {"knip": "^5.45.0", "tsup": "^8.4.0", "vite": "^6.2.1", "eslint": "^9.22.0", "rimraf": "^6.0.1", "vitest": "^3.0.8", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.8", "prettier": "^3.5.3", "typescript": "^5.8.2", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.1.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.1_1741411005310_0.9482312913071331", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@asamuzakjp/css-color", "version": "3.1.2", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.2", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "4efb1abb3bfbb5982df66bd6e71fea21e3a29fbe", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.2.tgz", "fileCount": 58, "integrity": "sha512-nwgc7jPn3LpZ4JWsoHtuwBsad1qSSLDDX634DdG0PBJofIuIEtSWk4KkRmuXyu178tjuHAbwiMNNzwqIyLYxZw==", "signatures": [{"sig": "MEUCIQDNy2VyDD02SW91wj/StEWFN5wwOQhwmEGGF5nUwuiH6wIgRN5hFi+eAeTY4fajSm1OtzktZvriNtosIG8RjlmlooQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2083522}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "2381dbf57b74f958d976e07507e059f8cbfa80c0", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.6.3", "devDependencies": {"knip": "^5.50.3", "tsup": "^8.4.0", "vite": "^6.2.6", "eslint": "^9.24.0", "rimraf": "^6.0.1", "vitest": "^3.1.1", "esbuild": "^0.25.2", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.2_1744686257627_0.8977415082832978", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@asamuzakjp/css-color", "version": "3.1.3", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.3", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "e408e8756a0dc561ccf20f52931034ea509ee760", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.3.tgz", "fileCount": 58, "integrity": "sha512-u25AyjuNrRFGb1O7KmWEu0ExN6iJMlUmDSlOPW/11JF8khOrIGG6oCoYpC+4mZlthNVhFUahk68lNrNI91f6Yg==", "signatures": [{"sig": "MEYCIQCjEeBdc7MBnFyHQMCdCBanCIrWBB4BM2S6R+ax3volEQIhAIos0zKrIPsvOjRv7v86Wbj8I7ztbI1YHbWZ+tSsCXeg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2088274}, "main": "dist/cjs/index.cjs", "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "fe3f1eb60b2e8fe9bc25d97ef87acc286bcea081", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.6.3", "devDependencies": {"knip": "^5.50.5", "tsup": "^8.4.0", "vite": "^6.3.2", "eslint": "^9.25.0", "rimraf": "^6.0.1", "vitest": "^3.1.1", "esbuild": "^0.25.2", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.3_1745153636937_0.13901159792417883", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@asamuzakjp/css-color", "version": "3.1.4", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.4", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "99f4de297fc8c9707a124a43c5630a0fbd9489dc", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.4.tgz", "fileCount": 58, "integrity": "sha512-SeuBV4rnjpFNjI8HSgKUwteuFdkHwkboq31HWzznuqgySQir+jSTczoWVVL4jvOjKjuH80fMDG0Fvg1Sb+OJsA==", "signatures": [{"sig": "MEUCIQCwsOm+RfhEB0fHwXJTqEiLK5V/vhhTk0jsI8x63zm7pAIgbhwTkqTyD2cc89Z+1ip6L64qnxV7jBbf+Qt3Qcm9Gwg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2088594}, "main": "dist/cjs/index.cjs", "pnpm": {"onlyBuiltDependencies": ["unrs-resolver"]}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "0ab9cf9e2516ae9c107ca1b3c40a06c2b0b14f59", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.9.0", "devDependencies": {"knip": "^5.50.5", "tsup": "^8.4.0", "vite": "^6.3.2", "eslint": "^9.25.1", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.2", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.4_1745273752374_0.189884250013167", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.5": {"name": "@asamuzakjp/css-color", "version": "3.1.5", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.5", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "b6bc36ad3a10289219102028f10e6d173165350a", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.5.tgz", "fileCount": 58, "integrity": "sha512-w7AmVyTTiU41fNLsFDf+gA2Dwtbx2EJtn2pbJNAGSRAg50loXy1uLXA3hEpD8+eydcomTurw09tq5/AyceCaGg==", "signatures": [{"sig": "MEUCIDidXS6X9CPiuic8oNwe80E9Kf7RxdGTviXcCtAQeL2NAiEArPhK+XkgxLxVJmim9FuRsegDHhD+zFTCqjCOeqd5ecY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2090933}, "main": "dist/cjs/index.cjs", "pnpm": {"onlyBuiltDependencies": ["esbuild", "unrs-resolver"]}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "cbd6ceb3bcb2d436a74de7316afb39e8f680d259", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.9.0", "devDependencies": {"knip": "^5.50.5", "tsup": "^8.4.0", "vite": "^6.3.3", "eslint": "^9.25.1", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.3", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.5_1745706839826_0.7732438160600001", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.6": {"name": "@asamuzakjp/css-color", "version": "3.1.6", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.6", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "1c214c5a919d6f420625eede7ec0adb60117284f", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.6.tgz", "fileCount": 58, "integrity": "sha512-zPxs+RSMav//EcKKnM3bSPUbOASVY8eYHL/QVYnapSylYsiMXYackRRSpopzRqkzfzReZBxl1YAw5ohebGLxHA==", "signatures": [{"sig": "MEUCIQCvjkZZC/qTFeq+k2jl2igFNGoKGT3iovDQrnL0kF84wgIgd4x5Yk0vIG7sFP4xHAjsqCHpqhj0uz0x1svNFZ5H+E4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2091839}, "main": "dist/cjs/index.cjs", "pnpm": {"onlyBuiltDependencies": ["esbuild", "unrs-resolver"]}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "b1b23b1e97ebeb8a2e168b3ebbfda8b67e6ef4d2", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.10.0", "devDependencies": {"knip": "^5.52.0", "tsup": "^8.4.0", "vite": "^6.3.4", "eslint": "^9.26.0", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.3", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.6_1746255616477_0.6271451393739951", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.7": {"name": "@asamuzakjp/css-color", "version": "3.1.7", "author": {"name": "asamuzaK"}, "license": "MIT", "_id": "@asamuzakjp/css-color@3.1.7", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "dist": {"shasum": "01fb8475bc8dc999ddc4b270ab2e31f82780d17f", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.7.tgz", "fileCount": 58, "integrity": "sha512-Ok5fYhtwdyJQmU1PpEv6Si7Y+A4cYb8yNM9oiIJC9TzXPMuN9fvdonKJqcnz9TbFqV6bQ8z0giRq0iaOpGZV2g==", "signatures": [{"sig": "MEUCIEi0HEUk8ovHfkDsRo3Vi++0cejAhrNF20V1lga5DAz6AiEAkFYvP6UtlvU5tlpmfxw0Vw5ftcKRl7/LY1O+hYHM8+8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2093573}, "main": "dist/cjs/index.cjs", "pnpm": {"onlyBuiltDependencies": ["esbuild", "unrs-resolver"]}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "gitHead": "803c345cf6988f431a8cba845bf3384bc1bc6f97", "scripts": {"knip": "knip", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "clean": "rimraf ./coverage ./dist", "publint": "publint --strict", "prettier": "prettier . --ignore-unknown --write", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "test:unit": "vitest", "build:prod": "vite build", "test:types": "tsc", "test:eslint": "eslint ./src ./test --fix", "build:browser": "vite build -c ./vite.browser.config.ts"}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/asamuzaK/cssColor.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "CSS color - Resolve and convert CSS colors.", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "_hasShrinkwrap": false, "packageManager": "pnpm@10.10.0", "devDependencies": {"knip": "^5.52.0", "tsup": "^8.4.0", "vite": "^6.3.4", "eslint": "^9.26.0", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.3", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-color_3.1.7_1746260029732_0.8557644686837595", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@asamuzakjp/css-color", "description": "CSS color - Resolve and convert CSS colors.", "author": {"name": "asamuzaK"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/asamuzaK/cssColor.git"}, "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "main": "dist/cjs/index.cjs", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}, "devDependencies": {"@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.4", "esbuild": "^0.25.4", "eslint": "^9.27.0", "eslint-plugin-regexp": "^2.7.0", "globals": "^16.1.0", "knip": "^5.56.0", "neostandard": "^0.12.1", "prettier": "^3.5.3", "publint": "^0.3.12", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.1.4"}, "packageManager": "pnpm@10.11.0", "pnpm": {"onlyBuiltDependencies": ["esbuild", "unrs-resolver"]}, "scripts": {"build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "build:browser": "vite build -c ./vite.browser.config.ts", "build:prod": "vite build", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "clean": "rimraf ./coverage ./dist", "knip": "knip", "prettier": "prettier . --ignore-unknown --write", "publint": "publint --strict", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "test:eslint": "eslint ./src ./test --fix", "test:types": "tsc", "test:unit": "vitest"}, "version": "3.2.0", "_id": "@asamuzakjp/css-color@3.2.0", "gitHead": "9e713cbe49d8cb510d68f21f9a22f694710ff57e", "_nodeVersion": "22.15.1", "_npmVersion": "11.4.0", "dist": {"integrity": "sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw==", "shasum": "cc42f5b85c593f79f1fa4f25d2b9b321e61d1794", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.2.0.tgz", "fileCount": 58, "unpackedSize": 2097817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCyxisP1OifoyRTASkl0AYLRkHFMbtQEWEEiDpJO4Y8sgIgY42gn85Zr9hrKyEM1aufq/SfGG5JQhS+Z7qiMG5vYnc="}]}, "_npmUser": {"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/css-color_3.2.0_1747719238737_0.6482801024399458"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-01-26T13:13:59.931Z", "modified": "2025-05-20T05:33:59.129Z", "1.0.0": "2024-01-26T13:14:00.281Z", "1.0.1": "2024-01-27T02:27:15.550Z", "1.0.2": "2024-01-27T11:57:58.904Z", "1.0.3": "2024-01-27T12:33:46.702Z", "1.0.4": "2024-01-27T13:27:32.036Z", "1.1.0": "2024-02-24T02:12:10.054Z", "1.1.1": "2024-11-17T03:10:09.728Z", "1.1.2": "2024-11-17T13:03:28.773Z", "2.0.0": "2024-11-29T11:39:28.870Z", "2.0.1": "2024-11-30T01:44:26.520Z", "2.2.0": "2024-12-04T08:09:41.336Z", "2.2.1": "2024-12-04T08:36:56.215Z", "2.3.0": "2024-12-07T05:36:57.627Z", "2.4.0": "2024-12-13T03:55:49.783Z", "2.5.0": "2024-12-14T00:33:45.800Z", "2.6.0": "2024-12-16T14:13:37.392Z", "2.6.1": "2024-12-16T15:02:31.037Z", "2.6.2": "2024-12-17T02:44:32.283Z", "2.6.3": "2024-12-17T11:15:02.613Z", "2.6.4": "2024-12-20T13:36:38.228Z", "2.6.5": "2024-12-20T14:17:44.476Z", "2.6.6": "2024-12-20T22:21:15.969Z", "2.6.7": "2024-12-21T15:49:20.414Z", "2.7.0": "2024-12-27T00:25:58.267Z", "2.7.1": "2024-12-28T00:42:11.608Z", "2.8.1": "2025-01-05T08:57:23.596Z", "2.8.2": "2025-01-08T12:54:48.764Z", "2.8.3-b.1": "2025-01-10T11:14:33.794Z", "2.8.3-b.2": "2025-01-12T05:08:23.932Z", "3.0.0": "2025-01-14T15:14:08.393Z", "2.8.3": "2025-01-15T12:30:11.471Z", "3.0.1": "2025-01-25T01:42:35.068Z", "3.0.2": "2025-01-25T04:28:49.366Z", "3.0.4": "2025-01-25T05:37:53.904Z", "3.0.5": "2025-01-26T03:52:11.488Z", "3.0.6": "2025-01-31T22:32:23.692Z", "3.0.7": "2025-02-02T05:07:44.013Z", "3.0.8": "2025-02-11T06:33:59.999Z", "3.0.9": "2025-02-14T12:17:26.279Z", "3.0.10": "2025-02-15T00:48:09.323Z", "3.0.12": "2025-02-18T07:04:42.858Z", "3.0.13": "2025-02-24T00:17:05.097Z", "3.0.14": "2025-02-25T22:51:50.735Z", "3.0.15": "2025-03-02T22:40:47.018Z", "3.1.1": "2025-03-08T05:16:45.528Z", "3.1.2": "2025-04-15T03:04:17.813Z", "3.1.3": "2025-04-20T12:53:57.156Z", "3.1.4": "2025-04-21T22:15:52.615Z", "3.1.5": "2025-04-26T22:34:00.080Z", "3.1.6": "2025-05-03T07:00:16.745Z", "3.1.7": "2025-05-03T08:13:49.969Z", "3.2.0": "2025-05-20T05:33:58.956Z"}, "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "author": {"name": "asamuzaK"}, "license": "MIT", "homepage": "https://github.com/asamuzaK/cssColor#readme", "repository": {"type": "git", "url": "git+https://github.com/asamuzaK/cssColor.git"}, "description": "CSS color - Resolve and convert CSS colors.", "maintainers": [{"name": "<PERSON><PERSON>uzak<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# CSS color\n\n[![build](https://github.com/asamuzaK/cssColor/actions/workflows/node.js.yml/badge.svg)](https://github.com/asamuzaK/cssColor/actions/workflows/node.js.yml)\n[![CodeQL](https://github.com/asamuzaK/cssColor/actions/workflows/github-code-scanning/codeql/badge.svg)](https://github.com/asamuzaK/cssColor/actions/workflows/github-code-scanning/codeql)\n[![npm (scoped)](https://img.shields.io/npm/v/@asamuzakjp/css-color)](https://www.npmjs.com/package/@asamuzakjp/css-color)\n\nResolve and convert CSS colors.\n\n## Install\n\n```console\nnpm i @asamuzakjp/css-color\n```\n\n## Usage\n\n```javascript\nimport { convert, resolve, utils } from '@asamuzakjp/css-color';\n\nconst resolvedValue = resolve(\n  'color-mix(in oklab, lch(67.5345 42.5 258.2), color(srgb 0 0.5 0))'\n);\n// 'oklab(0.620754 -0.0931934 -0.00374881)'\n\nconst convertedValue = covert.colorToHex('lab(46.2775% -47.5621 48.5837)');\n// '#008000'\n\nconst result = utils.isColor('green');\n// true\n```\n\n<!-- Generated by documentation.js. Update this documentation by updating the source code. -->\n\n### resolve(color, opt)\n\nresolves CSS color\n\n#### Parameters\n\n- `color` **[string][133]** color value\n  - system colors are not supported\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.currentColor` **[string][133]?**\n    - color to use for `currentcolor` keyword\n    - if omitted, it will be treated as a missing color,\n      i.e. `rgb(none none none / none)`\n  - `opt.customProperty` **[object][135]?**\n    - custom properties\n    - pair of `--` prefixed property name as a key and it's value,\n      e.g.\n      ```javascript\n      const opt = {\n        customProperty: {\n          '--some-color': '#008000',\n          '--some-length': '16px'\n        }\n      };\n      ```\n    - and/or `callback` function to get the value of the custom property,\n      e.g.\n      ```javascript\n      const node = document.getElementById('foo');\n      const opt = {\n        customProperty: {\n          callback: node.style.getPropertyValue\n        }\n      };\n      ```\n  - `opt.dimension` **[object][135]?**\n    - dimension, e.g. for converting relative length to pixels\n    - pair of unit as a key and number in pixels as it's value,\n      e.g. suppose `1em === 12px`, `1rem === 16px` and `100vw === 1024px`, then\n      ```javascript\n      const opt = {\n        dimension: {\n          em: 12,\n          rem: 16,\n          vw: 10.24\n        }\n      };\n      ```\n    - and/or `callback` function to get the value as a number in pixels,\n      e.g.\n      ```javascript\n      const opt = {\n        dimension: {\n          callback: unit => {\n            switch (unit) {\n              case 'em':\n                return 12;\n              case 'rem':\n                return 16;\n              case 'vw':\n                return 10.24;\n              default:\n                return;\n            }\n          }\n        }\n      };\n      ```\n  - `opt.format` **[string][133]?**\n    - output format, one of below\n      - `computedValue` (default), [computed value][139] of the color\n      - `specifiedValue`, [specified value][140] of the color\n      - `hex`, hex color notation, i.e. `#rrggbb`\n      - `hexAlpha`, hex color notation with alpha channel, i.e. `#rrggbbaa`\n\nReturns **[string][133]?** one of `rgba?()`, `#rrggbb(aa)?`, `color-name`, `color(color-space r g b / alpha)`, `color(color-space x y z / alpha)`, `(ok)?lab(l a b / alpha)`, `(ok)?lch(l c h / alpha)`, `'(empty-string)'`, `null`\n\n- in `computedValue`, values are numbers, however `rgb()` values are integers\n- in `specifiedValue`, returns `empty string` for unknown and/or invalid color\n- in `hex`, returns `null` for `transparent`, and also returns `null` if any of `r`, `g`, `b`, `alpha` is not a number\n- in `hexAlpha`, returns `#00000000` for `transparent`, however returns `null` if any of `r`, `g`, `b`, `alpha` is not a number\n\n### convert\n\nContains various color conversion functions.\n\n### convert.numberToHex(value)\n\nconvert number to hex string\n\n#### Parameters\n\n- `value` **[number][134]** color value\n\nReturns **[string][133]** hex string: 00..ff\n\n### convert.colorToHex(value, opt)\n\nconvert color to hex\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.alpha` **[boolean][136]?** return in #rrggbbaa notation\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[string][133]** #rrggbb(aa)?\n\n### convert.colorToHsl(value, opt)\n\nconvert color to hsl\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[h, s, l, alpha]\n\n### convert.colorToHwb(value, opt)\n\nconvert color to hwb\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[h, w, b, alpha]\n\n### convert.colorToLab(value, opt)\n\nconvert color to lab\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[l, a, b, alpha]\n\n### convert.colorToLch(value, opt)\n\nconvert color to lch\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[l, c, h, alpha]\n\n### convert.colorToOklab(value, opt)\n\nconvert color to oklab\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[l, a, b, alpha]\n\n### convert.colorToOklch(value, opt)\n\nconvert color to oklch\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[l, c, h, alpha]\n\n### convert.colorToRgb(value, opt)\n\nconvert color to rgb\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[r, g, b, alpha]\n\n### convert.colorToXyz(value, opt)\n\nconvert color to xyz\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n  - `opt.d50` **[boolean][136]?** xyz in d50 white point\n\nReturns **[Array][137]<[number][134]>** \\[x, y, z, alpha]\n\n### convert.colorToXyzD50(value, opt)\n\nconvert color to xyz-d50\n\n#### Parameters\n\n- `value` **[string][133]** color value\n- `opt` **[object][135]?** options (optional, default `{}`)\n  - `opt.customProperty` **[object][135]?**\n    - custom properties, see `resolve()` function above\n  - `opt.dimension` **[object][135]?**\n    - dimension, see `resolve()` function above\n\nReturns **[Array][137]<[number][134]>** \\[x, y, z, alpha]\n\n### utils\n\nContains utility functions.\n\n### utils.isColor(color)\n\nis valid color type\n\n#### Parameters\n\n- `color` **[string][133]** color value\n  - system colors are not supported\n\nReturns **[boolean][136]**\n\n## Acknowledgments\n\nThe following resources have been of great help in the development of the CSS color.\n\n- [csstools/postcss-plugins](https://github.com/csstools/postcss-plugins)\n- [lru-cache](https://github.com/isaacs/node-lru-cache)\n\n---\n\nCopyright (c) 2024 [asamuzaK (Kazz)](https://github.com/asamuzaK/)\n\n[133]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String\n[134]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number\n[135]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object\n[136]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean\n[137]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array\n[138]: https://w3c.github.io/csswg-drafts/css-color-4/#color-conversion-code\n[139]: https://developer.mozilla.org/en-US/docs/Web/CSS/computed_value\n[140]: https://developer.mozilla.org/en-US/docs/Web/CSS/specified_value\n[141]: https://www.npmjs.com/package/@csstools/css-calc\n", "readmeFilename": "README.md"}