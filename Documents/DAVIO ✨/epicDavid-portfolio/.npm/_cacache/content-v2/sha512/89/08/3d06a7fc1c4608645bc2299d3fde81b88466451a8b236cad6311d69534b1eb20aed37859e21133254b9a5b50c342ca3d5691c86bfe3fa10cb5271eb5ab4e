{"_id": "@babel/plugin-transform-typeof-symbol", "_rev": "120-250d0e4dd15bb1244106fa77b92f281d", "name": "@babel/plugin-transform-typeof-symbol", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "af411381a4da838cb0a4cc535b8fdcc9559e57fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.4.tgz", "integrity": "sha512-8kv+O+Lq04nIpwaOiTTxI3kYn8BLu9S2UecYe5lV27DQN8BMmaoEJHsy22EdVrBtJ5mQeKY9NVymnHRYV6pK/Q==", "signatures": [{"sig": "MEQCIGGXDSYpq/XlsAoi/HBtfoAW+A6mV9HVcvseQpFpEIu0AiAXHaMKZVCY/9n9sA1oFw4/7WVtNcSeWkHiaRZ8mZxaDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.4.tgz_1509388491473_0.3141512500587851", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fa59470380738f14b23e92cd4712ba7684b05f22", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.5.tgz", "integrity": "sha512-VRgF/L+K4zdNxr2xWyZIeYa5eIyD3EMdmV9cf/4ZVj579Zf6RuVE9SWRXeGXkss+5725xzAM/ftgd44XV+nTpA==", "signatures": [{"sig": "MEUCICZsGmpH6WPTIGLjc9aiWJkP+jUnueS1E1Oc1qvmYjzQAiEA8KjmiMMmZN6msQcpQ4i/qu68vy2Vj/xE7AtHcTYyFRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.5.tgz_1509396991089_0.8863304688129574", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "faad6b603dbdad43c7874ca61dffe4df8793139a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.31.tgz", "integrity": "sha512-/0PHYrswVljPv2KYJkfsZMmgzZ9Kgxx6Q3PsBvflcUCzFNRH7IQLUcYPGxug+BFDFtIKpBkNwSbwbf1t5GeS7w==", "signatures": [{"sig": "MEYCIQDDQs5lahEW32mXA9GwCBFAz90m0e5eeGICzj8oKZzboQIhAPTUHF7cacy+VvfJn933C0hr+ezQDqVb21cqDGlybaEW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.31.tgz_1509739412427_0.809553315397352", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f819960bcd339fa557ae16c28561852d7a6d1ce6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.32.tgz", "integrity": "sha512-AILpoMhhFg8el2YZSUIw3sZiy+4wvzDOUBPvbvDlJEjUk2wyZpfsZDv5FzY2LiWeRQxg1Ce06slPzaEMIOxEyw==", "signatures": [{"sig": "MEYCIQDazVY4fZnPtZ7xl8V8yl3D/UHjycIEcyCX9pTcx4HoXgIhAKS1GxcB94TMjPKwz0JlOZiVWv8UPX7NQUQ5R0nhaJMk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.32.tgz_1510493603424_0.9401675281114876", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0bad44faf30e512b0be8e8a148b43851a8becc90", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.33.tgz", "integrity": "sha512-Esg6NvV7I1BlIg5x831tLrdVmNoE9zGl2nyGwbkjrRTFyT3p4tbIB6/owXtqWAmJ8ShQAJaCIsepByLmS9sq1g==", "signatures": [{"sig": "MEQCIFdYgjkcrohK/foJexcAsVLCpfDSWU3hySZPln8F52ytAiB/9Vg68/aYcn7GjYZsTLim+kcUqqGxrVIMoPqGKe30hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.33.tgz_1512138507000_0.38911378011107445", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "960c5c822a8e0ac5bad6ff81ec092def1bf93d7b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.34.tgz", "integrity": "sha512-zK2W/I6/eafbN7eWtrn7mWUpRdZMbV2GrOfS9yzbMifs9QvLK3jSmXXDSYu1dUPaG2sufxyp73mzvvr3owzpAg==", "signatures": [{"sig": "MEUCICTvMKtIhjM4GIIotEMu/naUPJRqCPY9SpbOjOI+ZEXOAiEAoPfjjTx7a57N5zq9a4HFd6jtHTFLfpOAwE1Iunp4jP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.34.tgz_1512225568499_0.5362619359511882", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ab8a83cf44fa63556471622ae886a14187e118c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.35.tgz", "integrity": "sha512-NzzikFdDh278dKxlwEG51YTtZj/siAebcbLILL00dM/McDrLETnz7WftfDFpVDvbFem9dLf0y59cq9/br6dp+w==", "signatures": [{"sig": "MEUCIQCefgISf6OcEqm1BYbjhGOK/MSdusPya60lvcM8Hol3dwIgB08HRk0iaUCEoJrwOUkjW5Kna4pcuJS0acIfKTzWMVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.35.tgz_1513288073907_0.28600400872528553", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "26c46433f33c9a20c5b8ca7d4394bce38972e5ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.36.tgz", "integrity": "sha512-1w/EdTveqnYe37O8hCvizL/KmZRHSG59Hh00mOnLc7/Ue5wnV1TLAA1rlNp+4d+D9OJsjf1Qvi7pTYMfLqHADQ==", "signatures": [{"sig": "MEUCIQCEWJzhbj42DUhxDb0OXumEaoCeJYeLElzQ7F1pB2CkUAIgRD688TW144BFke3rzdf7AnJWs/nP+v33x60ZzFxnM0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.36.tgz_1514228686550_0.9648714258801192", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "50706473552b094f00506fe86f86c7bd2db86bc2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.37.tgz", "integrity": "sha512-7cu6YN3nwa5CMMjVydH5GSDIUIy/FBK13oIXLhXDAjoRAjVAROPwi+7BgKWMiJw1JKcq1QY8v+JPZUk/lYA4Yw==", "signatures": [{"sig": "MEYCIQCDr5Lok+KO+/5rVSQEwC6dkaBszvXvTJebxwbP39EftQIhAIvLVh0LewoDa/TLiDuUHd70esc0CTzo8pJ6HbRHIcpY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.37.tgz_1515427356167_0.4095850025769323", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f390a9c6c79c36cf7bf2c9f818ffd52c05a92a59", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.38.tgz", "integrity": "sha512-vVULLUBkiLj4Y/p/37jxnutyOW0Ho0k6W2Own2wRe/kcWQoHxTDwwDWU2tGGBed1u1jbbYw5+B26F9IQ63tkvg==", "signatures": [{"sig": "MEUCIQC3Ygzr6kdexsxfmGzfGnq+JXkR00EL8yTyTW0yBmW6hQIgavSI3NJ77s/SWGYQ3YNPyS62hEZ9NfiKy4WsKjUNmDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.5.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.38.tgz_1516206722419_0.8220577037427574", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ac2134df64e014e864f7678e46770ecd13f604e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.39.tgz", "integrity": "sha512-u/kiS8cS8rAhUD8AvZvZ5lGe+4CuDlkY3L3l2soteijVMHKGvRGbGoVf9aqYSQyqhXskNZupgt3yks3QZTFw9w==", "signatures": [{"sig": "MEQCIChsPPwWu0+tTZJuCF6oRMX0TwFiX28Qcf5mxJaBDrtqAiBJhPzt8pmu4XcFLCxJfQRe6F463nP9pYBY/sznthDUPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol-7.0.0-beta.39.tgz_1517344058156_0.22929644328542054", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "67f0b8a5dd298b0aa5b347c3b6738c9c7baf1bcf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-y+mXC0tIlTZj04ZD9326grEIvFjI/IeLSIVVKMIf8nSodLDCgipuM6zXhxqXVvjcTrvvUKuxPrvPeSuht0eeMg==", "signatures": [{"sig": "MEYCIQCqkHlIXquKGcLY8jlxHMhuXDr60j0NHi/Sws04fjFhBAIhAPg6/U5CtHCNLanpC0GPBRvggPflJeMD9DmEBCcVmeXK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3317}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.40_1518453704183_0.423606201186969", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "954ed7a16170d17c1928ab9cd52667011d69ab12", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-tBBd/wEo4kUaj02vFIsUtq9Oioh3z4rVB3DExWyWy8Ku+TYLysihDP21hyABeyPlq5d5/VQnoV8IClL6xoZcHQ==", "signatures": [{"sig": "MEUCIQDivN3npFlAp8hNaGsIkpdM7bI5fxMRx80SOKicbmfzmwIgARRKhHinMJIc6Xs0Z4KwSemsKV2nkY4QDM36BjBfo/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3552}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.41_1521044779477_0.5858070778293483", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7d93fcd194db78b839488cddddefbaa46032e327", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-cuDOzCv7kpMqaLypTEN90ErVmKUV3vjTuO8qzCGlmJMZwCcteTpxCiwISJLnAxcGtj++fMVY0vntbcj4nXf9XQ==", "signatures": [{"sig": "MEYCIQDZWS0QrB1hB1x2C4Pl2amQPTrxrIVkCxCTJ2TX5A6R4AIhALcEQBva93/ryqQPjoK/k703wtCsktulQsCtjZky5Caw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3558}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.42_1521147051742_0.7365829194121514", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b53433041acab36bb12cb09d9bb849998c84b379", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-nHNnVhwb5hZrHhifcu8BewLja+auPbaKlEUdJ+nL+WtM9Ypo6CXwaZf11G3Cjtr0DPtOCjwx40f0bzjs9J02Hg==", "signatures": [{"sig": "MEYCIQC/R1logCTmpW0Vv0jkxXe93nz7GIOgZ5SVykyu7tZ1PQIhAOMwuEO4+xfSlhQX4GpmZ4rX2Go4ANNQ2Dd8xccwGTx7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3746}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.43_1522687711079_0.09970118222807489", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ba0ded29aea2a51700e0730a054faa64a22ff38a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-ObXHYNMRrvRcKANNVbR50ZrtX5i7qiJYH2GiD4snt6pOEooWJQmbHALFyTS/+I4eCVhNe6CQ6cMqXDhMl0RYOw==", "signatures": [{"sig": "MEQCIHfD720bJd06/QGfW+J7N/aBmfAKgcYQoZyS7u6cnrx4AiAnsXhDqwul1XMBSjhDI45VoF6DrCvaaMcZ35qCjrmgnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3820}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.44_1522707613115_0.8873111948287884", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b846314cc58d0501c5c86c701e58ba4cb81c0331", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-a42uuEGopxW9M4wsADnD6vpGgsoYMKpvKKP7LRTTFUuuCn7T3xw6uKbLDFYOEiYlcmPgmsQYeUESE98m0GIfWg==", "signatures": [{"sig": "MEYCIQCb1/ZxWfWWl9hTl1egEUAzr5h9+QrsFtTbtmd8vJWmawIhAMxkz3VJwawsgjdJJVyjOs87OUXSWFB+PuDmt/SMiwk2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T14CRA9TVsSAnZWagAA27oP/igoUgMaVOSFchAb58a9\nV7ZVuKIrvy+NYQuTwL07f8z3KVEaqctKj7R9TrObQX6MeKl/luhKSIHnW9Lf\nPMzM+c/kteLCal2f9hxCy47WWPh6fdZ1IPP79uaAVCTIdVjMynzCQFufFzNG\nTljTSVfeQLSs7ugEA9UCqW9M3lUIDP5Ay1rwCq8FhrUokXyVRN4A5nnyd5pw\nJF/vnPQbICPdhW/6d+e9NXOob/oroLsRcyiSCUd8bC1p+k/0fpCobtUgnla4\nI8xjgObNXGrBGtFnELZZeLq8+MMiKQVQhvgbm9K8Nxadexd5U3KgezlSxcK+\nKuggE/FmRmX4zWTZFc06L2FZ4iW++LZLcw73qFQZ1aeaN/Rm066f6uEzI6kB\nzQCYzw7cVdhQzkT2CB/j+iV4FJuH7RjUmWl73ISi37bOiwf5WeEm51VWS2AR\nS4U4tvCp89t0N4Di8pXi/0ewztbD5U8OMYOblccTCsjxnMLt76h5kFBjW3Kc\nJPl6FQj9GHAZleZk2rgo2QZygqmYvwrqYaJd734L1ADMVowTYMJ1vCHShXL/\nE0KPeTpgUmAz2WvviVcYT428/U4SgkrSBrq4bxyvk7syJxxdQ7bJq+ht/hU/\n1fytNmFrNlSnbvR8UfiPExxi0CpOaTtYJTmJci9oH2W20DrMWzf8TbF/uud5\ntg9M\r\n=kFBy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.45_1524448630941_0.9519799982390853", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "643529184cbb07199237c94537c89ea9a721fa0a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-l+CfLR2c5KzZ/uZlkflNe7nK2hiEahc/FeiUCTd5Krj4aCKDdLLSdb4Lowf5hdZbqIPQc9TdL9SZt80YhUlmFw==", "signatures": [{"sig": "MEUCIBvwErb+W9t8fDl3n/cDQBR37wZn8S1+PeIQgDxD0GwxAiEAkWEVXoSC3FhwnO1BuVE0Z9QDTES7c2guAwfm257YJLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGmCRA9TVsSAnZWagAAJ0IQAJLjgwQeRB2SC3Uzdxpv\nDqX1st3YVdtJTB3sfSRH+p+yPVI+ZLNwDIM9mUkLuruIVoPdVb3PWcAHPrGN\nMfd640EQuRaY3paWuv1DyL3uJkHWTvKO5WjNbP0hsb68nn76Kjpj7KZXDBGd\n+KnTtflL8+8Eyo2ir4isPPB5jWANQQPV2J426oI13Bky6Mhy5IjkeeqBoVvw\njgMAT2azgXnOo2rPOz/ejbUCKwJIQMN4b/Q1kvhWZn/cAD83PkJD/Zfi1ha/\nz5xG/zE0/m29I8Teqv3v3CDIb6kPLUtVkSuV5LM0oo8aLDX7xYOxH5FX2qAF\nuK0oR4ctpmt9Ogmb9WhuRLaBS2fkHa+2p+o5eFXczvZuLcfMljcZeb/EAXWF\nScjcURNgR/+SdMQqWs13NeSbA+8eYiA8TAydesl8D8Tivk8wi6ylwStu5laU\nKAIZ+dGgApbDU7MHWVA29ZanFs9Y4YqLY9vPoW/2WM2Lr4YCa7pjhI56UmQN\n6i4LDRD/a8JE4HSLOycjizInB4PQxNnn9M/wyHQ3enMW0EeB/DXGU7J7vUof\n/nQlD0rUDab1/MuInqa348kV86er9kOOJM3vhGzMLvI4nXd8OXeQglegdD4X\n2azvVSd0sAum+yYmeXET3IdKZCqdlOaIs0hOcySM1dAQ6gWP+lJLBDZFE69q\nAJeF\r\n=psFR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.46_1524457894145_0.5479055882746771", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "03c612ec09213eb386a81d5fa67c234ee4b2034c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-PhPy5NUY5MT++fEr7/sxN1/ERSM2siHSbGgdkbqgDlyvf1NlU1HeqyfomHJEFE1Y4PX0hj+XmtjSAali/6XqYA==", "signatures": [{"sig": "MEYCIQDbqg8qWSfqF1BqDOrXCS0qFl4tIfBu6jJzdiqKN+ZqYwIhAMOBwD5DRYUbEw48N7pHnIs934E2nMgiGoi5FOP8u5eE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iU0CRA9TVsSAnZWagAAx4gQAJd4eUVf3rusjGXtXCoj\nWuRmGCQ3Xty7qimTXXAg2NRCPwK9P3UkCMOCDQ0HhfnTBlpoq2wq6yEy7DEW\nAvb6TbnsfU5mcybNE/tl5i0MzGL362hhEbT0ZGwRjbyPLreu5cV4AhMmUR76\nAVvZ45fbHSHJt7Rlf8kMOEjtO3kQEW+oa9PQVhVczl38XX/bMJgBsdq3aI6t\nrgWDXR01HkUBzh3mSxVKrIXvnJaLz30ISD9YZHOgU12PNc6WO9w1cXHEYe6v\nhu2kt4L9AW6TSR+Q2CpLBR1biTGVQqueT9uTJRIsFfpqbKMhUrm8k78Opdqd\ndIqpH3BPyP0st3nHNmjQl8v0YQCD+Y9nknuTLtFU/9Mq+gi6kpUHkJLOJiEz\n+H4UxDtS4J1bvBGJIuReYxJyr62HdkKRj0pvjKh92uYutQR2rwCDYdVlXAFe\nf5ikOIsWV/gImfBJgZhebDw08fxqAYxw4K9FfD82Fc5KpGngmuPPhH5GMYyd\nBFxOTIqV3yWSB8awnwZDQz5bqHvcqDanvCfF8/VbFqe0mNhkWN3+l8PMcgEV\nb8AnpkJjcb1+crIIdmMtqScrMhiU4zoNPxXhW5L22pr7eZk/xvgncj1puDUe\nwfuMk7b2UFpc2RK943zmEQIDPc3VcCyhxEtb7HyZ0Wtyk3bK+SKDTExkXPDU\nY9md\r\n=IFei\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.47_1526342964439_0.9685763495736206", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8bbc1274f937adbe6d12285dc354df29e483ba71", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-oQZYsVherJCr6GGjHWd6hl0CLQB+H9C8QilqiTO98Uw5ng2TRw+UQi1JrjB2fZrGy819Uf11yu/K9OQ2CeF9nA==", "signatures": [{"sig": "MEUCIQDwiU1ldgv9BE8l6N1oAXtpo2EK2HwllBCF6LHYcNdcYAIgCDO+9dMFyjs9faGQ+FG+RqPeazqwfABANEmpqnmyFSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxEZCRA9TVsSAnZWagAAnKUP/iXUVyjgL5bvA6DqNJsd\nN56NS8hZO/fEE/waeU36yr4AMe+2gKkmku8fkNTb7S9sx1eF1DrxJVRpjM8R\n+vPQF4xMD/xVmzO3ZumT8aYr/bs3VHwfjhTXUYnsNvIZvb/x63wJtb/VlwL+\nbz2Ouuv9hr5CrSqSc7m4OziW/9F5AQd8SYcdHiEDJ2ePM+9583JE04+LCaBT\nGqkY5x5ePbZooUjh7Sz6L1IupuEFb8WqejkFA5GTBiqmua5QmSLnL4hBWKn0\nXCf+b7N5A6y2cwfdYGq9zHNn1B+Z4miLM5wjlEZpjl8bkqGe45HwMjWbxcwE\nHJgU9JwfVHJU3Zg3aMQsTuHkJYC6In9q4Bf05JkcnGw8PAnZX+6eucX/B/Q9\ngCpEec6f3WoCxuv/nivX1W52WtAvk5jsUV136kxk1F+719NVv/4hjPTZ81oH\nauI0CVx/xUD88U/hqgZKNUpKSmA4rLHXhSYGO1ZfOTfg+W0MvN+nGeroV0LG\nyf/avBC3JCmYeNbfjD3EHDcypvI/pAujUh5UcSMw2vRLwKeErnDoteetl4Bm\npuzGoMsrUSadzJOP8zUmcx+cFpxZGch25EvZTxfAdmwGTTSY6q2zTMIXxqdW\nSAQf04nnkTA6FSidSa/T0JTucqCSYXDmTb8IHja1JutxwJEm2f6N3XzWTdEU\ng20W\r\n=8wfy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "5.6.0", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.48_1527189784411_0.498533025549617", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "365141ba355bf739eefd6c2bb9df1c3b7146e450", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-0g0eScXFmgTjnUEQO5VYq4fXCfaFWqag33wi0DI0HlaXEWw6He3bnyMiH0yWCDG30SzpnpcvR6x05d0C8EmsTg==", "signatures": [{"sig": "MEYCIQD2+YTUXirMIHM5k0B0rpbGzBSDV6FkLmQNKh66e3gt7gIhAPy6qK35d2OnImKrV6/mN2ez5667bVq4gv2/n01SlvJ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOcCRA9TVsSAnZWagAA7mgP+wUQfBXe7LSvQvXfvxkQ\nWDJLT7LwJJJmA6wZ1s1l8bIdwqqkF7RM6iWp4DMNo6EVuIeAtHsWmEBfnQ53\nc2pdG49C353H+BGNZdb2cER3dMTBYgn9D2LV9fxafXGaUrK33jz9F8SolZGj\nmYPIX4HzXzPw1bPxblYA45TEpDUzAA0l8hcBFhPk695AChShvWsqONjj0Qjk\ns+x3xAjisY+jDyKcTwr2qq7pIvJ3EPd/agKpIhmNBRkQCOCPcbrWF8pvpO/L\nUujQU3MPz1Ii+TIjVWLbA9gIoCRV7/lgNFmhdBNQ6sFoxuEEoVYSmNUYGnCJ\n1+56LUSjVe68WjO2/6OsFsv2/9jqvMkhQitdSyTl3F2zxiUDTRyzRsF5gdDh\n4RF0cqLGAPJJ9SjCnv7+56cxWQhq6xD8LBy85Ts/pWpcEEXWAFUSCyoojU0r\nnCtT/zSwXltqZe8zUHx4YErdLO2EihZ98K8uvoJJdwJTdrP32s/s2KcEqtwp\nWQzLHfcBzIhKosGrMlBIvwuxcH49jbtioSeOqPX2fLixgDpMQKs+QW8nUXbE\n9dH7yZkRqKZUqxjxPknIzj7GXFeJBTlYQwA7XmhqpUu+7vZuLhJuoyN1wDRU\nG/PTW3uW8mKiL9hxhgsLpavvM0my/BuEFPjjRMnCtPJgljdeipYMEPfNMEJk\nr5jW\r\n=KhfV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "365141ba355bf739eefd6c2bb9df1c3b7146e450", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "3.10.10", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.49_1527264156016_0.7132631693199651", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d49ef1c70f05c6908b2c5b4b738f5671c596d582", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-ouLgy0Bm9P+mYxrus9dartJdIzxLmH4elDcSVkw/GbjDk8eLXQ+MDRIVAh8SrTAact7UEkiTZEZnlkaiRSyBiQ==", "signatures": [{"sig": "MEUCIQDCCGvgVCFeXd1MRQKUBb7Yym7mzapE0Qdw8BasLYSspAIgCDnpoPsSuFoz02itEY9KraJd3UYEHY1TckOUpbADsbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3285}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.50_1528832844570_0.4203528878473155", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4950c0c8e3c9e1e141e45cebab5e6148263204c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-TiJ5Lyuy1ZQ8tX/b1LM2qZg55O2WQn4uBsiLrMwx6TnJUrsXA/nYsxdOm6AXhdOrmmUz9alwBbikEroQY+arkA==", "signatures": [{"sig": "MEUCIBNl1R5jFzc9NkQhacEWZRMpvWbFtQ3eXPK/UuhTSXIlAiEAkFD5b8FqZej2UAPAwoYR5UpvL0F3wB81e9Jm90+Gavo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3299}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.51_1528838398843_0.3706658491530923", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "77070d409f8e199c38911e2b5835db761b9a56d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-xXcxdzSI9J25ZQxgDjs/NcOR2sED3+jwNK9jqfYFrt4b+xngEh3de7JphioWgX1FD3dm3IB3OtnSgyKwj+paWQ==", "signatures": [{"sig": "MEUCIE1j2AH/M9YY+VheBJHLokS+PA3jap3en629SbIGiKldAiEA4ywcvaffCogsKhGsROu5OTNgmZoeOPtAdLMqW420/GI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3298}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.52_1530838769719_0.35689522509394234", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "65aae871a9aa40f611483665731209aebd5c2a2b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-xm9X4m0x+HrZ9r8GqNFjnFlip0nh7zUjGzGFOFD1l07gepng3tG1HYrO+LJ9WwOqnE1pqc8d0cCz7nvxlEqHLQ==", "signatures": [{"sig": "MEQCIHpwc9FzKaymB3RrbilLmwZ9n7tdwRU8gUDzGgTjXZMQAiAsQLX+bpDeGb6uNkTxmiAJtcwEB8tQ3fQv3s1geB8jbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3298}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.53_1531316419741_0.8338374239564816", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6d068686239c9ebaf534d1c0d8032953f7b521bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-nFA9UEuKIhvDwRt87pCNK/vQCtbAdnx9ueuRUy6IZisj87f+8qM6ib/QjGBk/SwGgC8DWRcMKT+xqGqr1+y+UQ==", "signatures": [{"sig": "MEYCIQCVNHV+cjwZRj3v7B1P3Lmbq8/UeI5jbsYRpPdCDMCkRwIhAPePu5SL87xR7vl9bhQx6eWcPMkJgbZMVK6SOSqgTBIX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3298}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.54_1531764010838_0.48221556089864515", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "62326918560b765bbe9f362ad3a4ce3bc71477bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-CiNUj6kDXoXo1l+V/EO7fIB+4eSXgY3NLLy+7FUY3ibleQrthHczbLTp2ohKZwoS3r7o8n04xeHwICNTv4gDoQ==", "signatures": [{"sig": "MEUCIQDBm3ueESdabRBOtQ61/mNYOqcwox26SAYFCcWLSvqPUgIgPAThMcVDLDB2lhnaFJ9ZdnT6vGY4lWe7tz8bLbNLB+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3298}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.55_1532815645257_0.7741145634458177", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "76160d2bfee43b7d4ea07cf3c51dd7fa6578f8ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-BnKyH+AUvnVHyl+1Kt40jfr6+0/DUED9huSdirp1DrKwmjlcu4FliGDNcSSKBfawV4covKT7MBjWpE6s+XNk1g==", "signatures": [{"sig": "MEYCIQCCrTON/Dyo0oNlWeuoPsykhvGTcKO79HPaIczoTgk3IgIhAMT+jak4XCHNdk98QqVzuzRj8pVkQIxZivwo4IQbKxZb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPwJCRA9TVsSAnZWagAA9IIQAJUSvSlEZeGJzjkqwRSJ\nfnWSBhILt1cGfYxtBLEkQSIQEEcHLxdzga4EsXQJka5ittPASmCV1ilYI7IG\nVcId63w+AvDojr37doe1udQjTApY69XIzBNpRFw/pqgy6+Eq9DtZ9KMsxhys\nWhehp+bgnD5AnEEQW+/KV8NcWHeP/upiPnZvP4wQ5lc/pD9yF5EYtpjrfisa\nJ/s+CtCSbHpUDYwOcWZQwpd4fL9JBG/20xmfYXzh10+Usnf8AXpHFZe4XVGk\ns7LH9Rhr6Qg1RFk3WGX9o/tIb9ffZ7oMK07zFwhcPQDKJ7PxR0wv7d3+Jvzh\nndKPU83WdYoXIGgeViZEfJiHowSZcR4ZoFBEdMoyBCMrLxds3zHZkyZnKhdr\nGaf38xMHCXSQn4e66xTsveqBJsxqBNqVnxsj8ozOuRCypHAJQDybv8SgWCDg\nEdjaMWUXqDhXzPquGkIamq00JsbxE7Y3b0yevg2DXz7adbahMtt7H0Qyfs2j\nPqUxXMpnmaOmfDxQLt6ADT+VZX4ESeRAf3G180u44WNRbkhOiuR4XjWb0P9Q\nQOQUsbukEheJV1KWgm0XEay1k/W0g3QO6n69KOXKq/aFDXb2oPbTJip4HHB1\nkAvfq64nu7+n4ODap4eMC0wBEQfmK48vthBEvoCAYqlOB73/8ZZ/YTd5KC/D\noA3q\r\n=Uzmi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-beta.56_1533344777099_0.4015616787247207", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6c92d5266513c91623a9af6358188ca20ec82afe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-DoSEuLQNbutHYODTecmqBr9gDspCrPgOz8ZUReJqmO92ajTC7tEZHNa5LefghGNEAbIn1Ez9MpTtN2fuUVh9+A==", "signatures": [{"sig": "MEYCIQCQQoO0cGi1kqVBAKpGygaFWRNct91nG8ieOkrApbRFFwIhANzR0NCZKFtnJC63WEwI89zjXKuhDfJpyPdW2ZmBQGRc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSxCRA9TVsSAnZWagAAnXMP/0EzLqrwwj4Hhl6bJGZl\navsTZPNIpEfZC/pRiba5+z4pIxhXNBPW6S0EwtlEGhCvjmkKhxbsXtLBDCMW\nrL+WiTxXBkJYX/EViC+9bNt+wfWdqaV6Tg2Q17ZGF3Z/FiVytYTEFNTR/MuW\nQB+i/AvDuT04Aa9L6U2xEf/K8TpbmTgd3Q+qnEJuoRNWzXFYUoPQgnXyIwq8\nhHpU3dT99//waKIkuikL1yqN0xi8Pl9btMrevKQkZDS54zTEdObW5FY3GrUj\n9QqrV9grUOgKZ5K7WvyScW/gion88IsRj9xI6nqAIuw0061FJbg9iEr7+W/E\nKCBGpSlaPvHXai0Ne1SVNxbt/BYEfG3sktoE8MdLpPu+HmRdZGmJYFT627IR\nRFb8EKkH5RgBWoQtm410SmZlcZAn2aDZGxF0tBS8WbPpJ9zHnpfOY/H2CEs2\nEmftGMXue+A6IOc/veZWGqOG1O08mUU0NwtxyYqMNdKHltnCDt1lxLGqSnrs\nTDS5Tl8t3JmyTzvlnJcHULeN/LInYF65gdNQSZA+XPMCb/zWaomYr/UaYN1Z\nm/DUDLsqxaydXTf6Sx31Zv9MDaLQbm57V9k4y1LGCk8nrpr1liN2v45AmVt7\n2kpBCvoeuNR3YGIi2v9t0vj+POQ8ErL2Z+rTlBgW0DyUgSE207wV4gko/7dq\n831C\r\n=oqQ0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-rc.0_1533830320622_0.24987025642440308", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "51c628dfcd2a5b6c1792b90e4f2f24b7eb993389", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-wUKNscuv3WOOFy3tGOBeayeOLyZjixjOSvb0QNXrCDRuENhfPaFQjZt/T0UDAZN0mXvAQ7Ksx2pOtXBsyIBxUA==", "signatures": [{"sig": "MEYCIQDZMvpJb+XKSldA58gbRCyDqMz/hPdAoy79o4ZR5xOsvgIhAOLP7jKsLO+9gWyoVAx24K3rLGHhvPGLGGXyGGEYMC1m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ81CRA9TVsSAnZWagAAurkP+wVfTlcH7NOkARaEwKD2\nlKICewh1nuZhP0CDY7WxTdZ6p2GEUF/hEph4YwejmbzUPYFrXeYGnfOCs2gA\nWNg1ZJKs+QCR7mefy2rP3SrGBq6uy9mRJaIT1G8qyJLtFoZfM5+YS5unEHar\n8rnDN9SRgR72PPvSQKxKXWRBnEm+MD3uYNkI2bNKf3PYKqSb6Ug+000tF4K3\ny8e0JeO5ucUAdLUpInjBHJ5MwXNvhWPVryyffI4yw+TLwInRTlhojgAnBVt9\nEntwy1r99kRqtlXRwAXt95h6P8AK31KCXdtyWMKTeK1mUlAIskUlVFJyoCg4\ndzYENGGROkoFs2PaU14Dwok+Jwyoe5Mw9tu1Q59LkjYZRWk1yYhCYMWlv/QC\n9tjSGQ+FmDf8nl3Dq6b9j6wi7kvaKData3PB4wtw+XLhkgMjkFU3M9xTyPYJ\nME1G/71WkQNCEgAMR0zrnDPvXciHbmWHbE47iEM76bhR/xWULqlYB7ACpC5w\nfb3/thdh3ljr+9GwnByYjY384vuAv0nAvogaV7MpylB4Rel3T60bj5LcqEbq\n6pIVqts+fYzLdBL6oyyBQiCzA1y/PUXCGYvhj5+L/X26ZFMuksLAjE5gbCOY\nriOPqNe9qS4cmXcfXFJ8zPeuIDkL82MhRogYNOlT07U2SAtkSEo66AHU3k+9\nXjM2\r\n=ShkF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-rc.1_1533845300667_0.20029414900516884", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3c9a0721f54ad8bbc8f469b6720304d843fd1ebe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-nL5PV6/VjuBreNsaJm00RzdIuC8ITK1O++WFwlZLOen5CPNSFmpOqP84J088hGk3tU9Iw5x4ETEzYHrf/5/72g==", "signatures": [{"sig": "MEUCIBi9TkWMKpbpiq0bG6Wem9Oj9i/phoVswRVRqpzSqipTAiEA54c26++jOXluWCGFaKeWmLMsopb012btHeal9wZicSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbmCRA9TVsSAnZWagAAXxEP/jiJnaVLWv48uKAeEr3d\nx85AEbhf1fV4gRCcKiIWCZBWTI5rqx8vjfPVOBXX9YMcCFEWBh4VgKfxnu6T\nz3Gz/6HXnt8vh1xHV9SId4n9ssJfeVb6WGbdlR8qdrVOhX4yj+lJOjRNg/oo\nECReKNd2tbXe3X4MiAdn1W0cdUQrX3ohswsag4PPD0qavVkTd7zsLfvloS1T\nhb/MRMcd7njd8H1XLcCuHibSjloAT0jHiZZ+0yVfM9RvaqGlm5jCU7Smui+8\nIeWJkCQffiNhB5/KjiAq2tkH0smit6acZLSauwiIZXCAJts2FSQEpQC6QvZ8\nsMBoscV9yxLE7iEBvUJxZEe8o/Nnx6fFu2iKUcaCP1QbeI0LTB5xBSvghtJI\n/gN++mznMkPHadkIcl99VjIioWDK/9V3fS4WieCxibS4/HarPbAHxqUWxq+0\nmtAFOiqfGz1YB8iEQfVpzGaWvrxwElioVm2AGTXKkyS65gBWvYBVjaWGz32o\nMzEUY908o7LIv2EXp2Kq4D1b/G8dPw9GONVrBaWLBKNMhq7UWES/EZ0dksRw\nEygY45j8qLqD7Thrao2PvKtYLcZ2KU+tKV4BRVjekHUBFnqvQ+40HGNh84FQ\nGZbrjj1jQbfyQrlRgzX8h982rZmkdRPMnvxZ+X63DUKl2P6v7VxJjVpR48jw\n1nSA\r\n=vOiz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-rc.2_1534879462120_0.38695429483740784", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6ce43fa10a4a2651c35bd4913cefd238b3610e39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-OiGvK0lui291koy77hIOF1/OAh5pHUxRRo+IxV6ARd/446JrTm1b2TL1Wb56mJG3kSsjLBncuLaZ32JfuPIqBw==", "signatures": [{"sig": "MEYCIQDjXphQmmWmYPpYFvGguR10ClyHPEzOUvyiH7RxhfrlLwIhAIdb5QKIbVAIlBR27XUe7bHtHQLGml/PXwYwWUXWnVsJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmVCRA9TVsSAnZWagAAhTUP/AiJFEUeOj2F2eu/VvKU\nkPvXRYaUSV9lip9MS//7qc0Dk39IrGdpvxIgdvsDef3A0skacb1lMjs+nyl6\ndn11pWO4wIEKcNcFOftiyHOfuJ/6cdClwcSR99D2rIWvcUtSk4N7QM51ZEa5\nf78s8vFd/+hltx8Huo+3UdpALheZ6frNTte4o0sGfiBmtNH+LlcrGgfVFtKK\nALSPXpDMiAX2ri13qHforDymlPNStUiGndq+2V6XyMnfakiEADi+NhfxrA6d\n2Qxx0TKqlD64LUtVRVccLZNF3De4x4A42/o6k6IR35t+DG6Oc5rx8zo0jdRn\nW1enjNOazKaGvUdJuf/JtvRmHkBKMuruOHIuM3EBb3ngNYrQRGjuzKeEsYMl\nTqEctiXsv7Oa9/G0OFx8YkuzpdZ6cZW1g1ydjYSmu0EOvkFP3HSJcro6pSPp\novTz+dCM6b1xJwPpJwRM1CfyLBnj1JmUzSN935ktrru7ZtStUJQBsvZ7DBNC\ntFWIR0Hz7jzYHCDs1WF48OFFytBYf+foGof0d8iOnrGo2O9XBI3aoZ0dKYMt\nA1jw0XEEQY08uTpFoaGBYpOv9p9/aZDYsNPQ8EeFy2vkR8U+FVbxosi3nzzO\ncjbGGLpLfLws2neKdSoUeguib9V/ohjpS+sKC3bDnFwWODumJsDYP1HZf4/X\naP79\r\n=uRWf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-rc.3_1535134100438_0.724931456432029", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d0489fe67cfebe39076fa1bd4dc768a287757079", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-N5cSfQGBobyUtxyTTtaONT7yHgXeygzy5Ni9/ZzIAovSG/hf10mIgQpAhUk7nT4XThIY9qT4ALAGDfgak0x+6w==", "signatures": [{"sig": "MEUCID8j/mjcl0E45oTZBQy52e+ujcM0k9EuJUQ7hB3sfc6mAiEAkFgohIRFC3L0j8mcaakFgYEwdTaZiYjuHQequ1nmsaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCp0CRA9TVsSAnZWagAA2qIP/3A3Hf6sj817ZRlthWNz\n3VUGtTlZw/x4SxeBsdGDwVx9Mgtkol2xicw4ZJnhf1LZ5+QUkp6+L9Oj4jto\nrDYZ1LqjZhZ7lpWPkW6xThAXMyICnEHgfHKx0dWI2bKQqyrOYTfmo8pSa3j2\nKF6YcVhmxuN6aRfI7h/VfxxQLVoGRdSrIIi7sFKKNt/zE7xapr52eKgoRCQS\nRFBLPUCdNJJBrheT54MJ3+zYZHbAthMxl+3uH70OE5aUpF6jchZzYTwoS1t1\n/J4E+TYz5pLuEdvgYhPCe4jmvhnYkVc1hPt2Mto5yNb8UxVMj2raQ71j53/5\nr8x5L4EbS0yocYMk28FbQouPkB6IfBUuJQHnojmWWjxguhMLddXU6XXt4Js1\nvZhJxc2ikK6X1G95rtdUvsI5f8SKaXwn8uX03Vf270EpRpP1XmjbV5Mv8nHY\nUC4xdr58aTnISxiF7keCcOLdchN3458ucosQ+Beqk3qcndfNIYAmODSJOBoO\neV94G6HIVpBnXNnzcPx87Uh6VBuoBlHRw3EFTAmW4qEfgGuwat2Lb0YrmHTS\nbEbaCrM9weN7p2x14ZynYsSNIJT0muBM0HM3LV8Y2PECIOBctytjDwRlV57F\nj2/0aV4oQVrhfNIWlvsihJow8WFWA+K06xudEwCSA+QLPnun1vXgbIR27H2h\nHXiv\r\n=p0AR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0-rc.4_1535388275605_0.3327958682439187", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4dcf1e52e943e5267b7313bff347fdbe0f81cec9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-1r1X5DO78WnaAIvs5uC48t41LLckxsYklJrZjNKcevyz83sF2l4RHbw29qrCPr/6ksFsdfRpT/ZgxNWHXRnffg==", "signatures": [{"sig": "MEUCIHhQGHjYAoQCLMEuNTdiQkLdMfgwKsUF1eyMWzmv34PRAiEA1xxOLF+7g0tX+qsfVxXQ6G5dnd7lnbMHVKqxREDu1h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCECRA9TVsSAnZWagAANJgP/A/wUz805WQUD1R9Mm7B\nHhNAE0fO95MrWRpEfE7n5DvOwOTordoI3wCO6J9ksqZpAbIK/P4RGRS+K5sL\nGpcCPouXXMU2sND4DZedvZZVF/t1oFRWoQI+FlTyg8hoO4gRGMHOWdaAE1WY\nnsLeC8kgwHdYeuvsryeZUCkT90FyCO+InCqspDHq7U7xQcb6GRQWArBPFypu\n/IQUXBMo35p9qg+7gFRvQS1Ih6w1Owy92UKSoPLrOQtzhH//dG8lTgZ7adOW\n+BFLieP3XYy6EIYyF2xuRZ6YTDGaPrdYSuivaK5RvFGaztRll9Fd8uq6XoNj\n8SUCT9OEy0TmmQDdqoXlJasBeiqHwi8rMHkJ6C6ApB+EjxFbdoORcWZjsJPu\nd+8GlFkjQSLlLvZWnJbsLlIS7X4DtJy5dWxTXLbM3zNz7ZvFjiv1HXAkmpqE\nAhQZcPkgjgsjwZMy4guA8DCHwcGhSg6akUkQzWAaArPDZh29rXUqAEB/iKqT\neNh/44rXaBJPf84Mjtc94PGPq8asKrZghZk6YQxS7b7+bB46IByY4gVtL8qp\nuouUAQbvLa098Oi3pLnlwePG1IWdsP0DA65Q6m8aGaciqnGnKexmoDBOWIcI\nskM1+S1CZNQEA+WoFBXgdmPlV11Ev5k2eKAxyuNgbl+zoXGY8tH64A7hgIeW\nS6JU\r\n=WzCP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.0.0_1535406211564_0.5618030172841244", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "117d2bcec2fbf64b4b59d1f9819894682d29f2b2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-2LNhETWYxiYysBtrBTqL8+La0jIoQQnIScUJc74OYvUGRmkskNY4EzLCnjHBzdmb38wqtTaixpo1NctEcvMDZw==", "signatures": [{"sig": "MEUCIAMs2pKl2gTD8Tl3Y1kskXR2Yw3+A89F+2eLkobVyGyVAiEA9KRvVz039R3L7RYP5ykk2Ex6d1g0Huy+aupm/motWcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2kCRA9TVsSAnZWagAAAxcQAJ7PwPL4JdaURRFTmlDW\ndGpZu+UOkXigOaI/6v9tJZ9VMcwBwJ0u592woXXbbK5HRaEvPfdlRIZo0zAA\nxROrM7bWCjNxCmOX+TlDrDX+swl+fP2F9nWEow553JxgIiB8EiiVyFMMIG+d\nX5zUbVIr+m6smjMlaWjOoi/L9QkJBzYvsJGr4s3wmBuief/2NOUFPypLsqy2\nuGyyqzxtDSK2lqltjEk3las64/4RXuZDyRpz2Q3dSoUTMV95KKCHY0nWrTDU\nyfTdBm1Rwy7v3NH8/V/Bd7lHbOgnFnMy2Nmve94SyFyEI2+c2h/YHqJbHPXE\nHS71K2E/BS6BCuUTkIky7Lq4Qwlmxp5CacIY7bKZYMm8ebqX1m13BCgZ9QHH\nM/uxlL9SNQrm+s/0iGmvOL4pk56wry/ZFYOVZzQuwMJH8tv4H2TLr66baoQa\n/D7Ku2pK4tXVN1lpL/hnE0JZBc9u8XNGLxBOobpaQROu7252wLAKFurm5nnK\nVyqVp+t2Fo9Wcsr1XnaOKbYqy3gY/IE5r5b/9fUs5e9fB9Xsiq7P+oakEIOU\nrKnrxtgIGEy0bTmKikZsl7FmzXZsOTpsUf9c1pQzlVwQSKSaPvD1Sil6iLEg\npB+lhCx9FevDLvvd2Hlp0ELMgPasINbnGLYSz0yOvtS9xcl483fhd6JINeCI\nNs3R\r\n=Ba5l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.2.0_1543863716281_0.5146572585088292", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3174626214f2d6de322882e498a38e8371b2140e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-KQPUQ/7mqe2m0B8VecdyaW5XcQYaePyl9R7IsKd+irzj6jvbhoGnRE+M0aNkyAzI07VfUQ9266L5xMARitV3wg==", "signatures": [{"sig": "MEYCIQC3wCtP6qHqVnt0UxzjK6HXz48v7VOktIY7kGiagQdpsQIhAOlyx6rksVop34vJ4nwBu2oA/4Uth+l35kR66PU2WVWy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HAECRA9TVsSAnZWagAAwNwP/AofqGcEPP1xDcBULlZ5\npzzjPMsmBRFylNv/xg4mKenRoNZReTgmnvW6Mdt7MDv8mW02dFLWNYNVjE8O\ncRuqVecKtnCbqL/8tP+jHq+bUZ9UAg5WUNIplx4LaXeUQWPtOFn+2AznC+aZ\ndvtM92adGulLiY3LV8rLG24eZb7SNGci2vfzaCSAhtafYJUl2+ryziyWghmt\n22pT9RVf+QjXpRMutqtxPEugj1r6pGwgqS9/DuuUiTvvs8LLubKGVT4plR5T\nql9rkbBTfTSaeKElpfVWygabOVB3hWqBsh4YG2dn3Dd4/NvhDK3cGFb7XyNS\nHcYHJBmIeGDuD97/I0LfTLcbD5043vdxdWEjW92MYr/RAkthp/7KGwegL18G\nn3mepdYvoocjL3KbzuzmLxHypopuRzPdMu67AMaaZg1zK3pu9T6HHpx8A5nO\nYNEfh/tRkZu0HMkn4ctyvkx/BM4j/Jh9+KdI9iUwbd1STN92b2ke2mMTmmMR\nlSSir6gNwQbIBWZ1yDkWCuSvvW9m6Il1s0PFm/XiDAJHsaFyr+1zSbvtLUVU\nQNvCDrpy1J95ztsk+zf1joTBpnqopMq6PZqb04LC47LJ3rdybJPgleT6guGo\nmH1yuhlmI/NP4ny62ahlRUrkfCQW+9UFbotEf8utgMz5KvD3eNyCewy8pnws\nCPOj\r\n=XVzh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.7.4_1574465539734_0.38232504063963546", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a8d0dd317349d3dcbb9d659808099c94486554a5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-rEUBEFzsA9mCS2r7EtXFlM/6GqtzgLdC4WVYM9fIgJX+HcSJ8oMmj8LinfKhbo0ipRauvUM2teE2iNDNqDwO1g==", "signatures": [{"sig": "MEQCIH+MJXOOX6EKKOO8r3D+hqOgn+kmMqVIzKSUMXkAzmyfAiBjsMpPFXq4u6TF/RwmZctSfkVwuN+8tSZm9nLj2SgcKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVrCRA9TVsSAnZWagAAobYP/jh0hq9oAR2gyPfTMGbN\nprHm/wKXtfzjqf2Y1z+5agW3LbJWoMJM4fDjZZ2Uj2Hy9S+5JCQlx2O3e+wR\nbOUGPCkxZAy8eJOuMrzJrQaNIBMdWFL4H0PKl4lghHtlqDsjWigythqbG4Ty\nDjiFc1Y2Ro7Y+sTNpb93nK8E20lmXcWcaYs5y12pKT/k7WjnKlkCm6714OmT\n+nPOGKblRPnIvJKPvcbweMyyxaNwzEiG9WqA54ywJ6b/WkI7FE4CnNLo0IvX\n38PyUXi041xabbYQWHR92Ddy96+YG08umxcvDcIidBPvbaVLLD+tMtcuJ5Fc\nbz89R8PW0iR32ykX/rwAgrnJiRvPzce/vT63k6/yfO4BbbuUREndbLIYNIhw\nZ2GLCbv8eBRW52OZGZ0o+ZHRjULRu1gPIua/REvnyaJoScbcF/OrKW943E8G\nc/N6f2jAx5MiRWRvGVPduw1bAua3G4G14bQbsRDp/3TDl449ZvlSab5qnAHo\nEPSU1cJ203CEhxstCRNaJWLyFJUvv3rFR0VmVFC9RwxMHzIAeCP9CjiE1h4A\n0cqlPMj6JZHqWazNy7jLr5QPiUI/8RyVpX9p27/T1g0HeDhEKI+4v4vlwdWb\nXYPvk8plwx3zybPQKBk0w+MVEeI0GPOrZ1iG9fZLzYDdhC1taGY7HII6RwNZ\nl1wz\r\n=XncQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.8.0_1578788202649_0.23772338542217564", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5cffb216fb25c8c64ba6bf5f76ce49d3ab079f4d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-3TrkKd4LPqm4jHs6nPtSDI/SV9Cm5PRJkHLUgTcqRQQTMAZ44ZaAdDZJtvWFSaRcvT0a1rTmJ5ZA5tDKjleF3g==", "signatures": [{"sig": "MEQCIE+Qf+YRHCbVdFpSQuiPBdQDsNyy86rNEJwfku4WKJkOAiAelyLU7FWz9vaD/P39qIXgw+PWWY45CNikHeW6FSYA/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQYCRA9TVsSAnZWagAA05gQAJ9Usplkl5GZrwcBKWAV\ngnflU1sxbkLUZF9lH2DnmCkohB38+vyfcFLTbs8e3QaiRWsolVpneQYTnfpw\niVsnoQ9WdYwBt6U22JAYlT15/kkR35OvIsM2PU1JRRBOO5eLBTNgsfuzgjsB\nVoXYt2r9uehOTUYI/8sZ/S65FrXNkz6l0GlB2+l0KlDWybzEDB7MqQeJkTuV\nJmDhkagJoB174lgeqR0oMPzTtJ9sS4dtoWAVqtT2NB4IpyRm8UWwOlHKku6g\nWQpCV6MYWXOdB9axhiV74uUGM/iSdZd1+HkwYI3pib+riays2cE/xY307d8f\n1Hvyn3EVROlVm5YDTdD+V48nXuLz5Uh8C6smWh31adMxRGg13AIu6X7hEDfj\nWZV3M1FwL46zq+JI3h29htVG1lbYEyexUwLfVWhvMQ1A1aKX2scw9167dkxK\nOwu6KamMXL4fzXk0ZZwyZl0pLnuiMW9pJOQT3uAgolXmXDAEj+jC7xnYJOaq\n4jcWUO8AVC5YO4DuW0pHrQhUUGOX3NSy7T5dunnkuP4kpToSYnmG+xIVtsn7\n+jzLj4f540+u9TuvM3qqdWKF48EBkwSWgk0gstsKcdJxvb5mbR0fkk7auXY7\nU/a/G+wWyioc1YWDx/QvbH3cxkf2B9FqywwxwwE1tosq9jykIAgtLzi5rDEH\nwxec\r\n=uA5T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.8.3_1578951703863_0.6579276029253576", "host": "s3://npm-registry-packages"}}, "7.8.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.8.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.8.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ede4062315ce0aaf8a657a920858f1a2f35fc412", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.8.4.tgz", "fileCount": 4, "integrity": "sha512-2QKyfjGdvuNfHsb7qnBBlKclbD4CfshH2KvDabiijLMGXPHJXGxtDzwIF7bQP+T0ysw8fYTtxPafgfs/c1Lrqg==", "signatures": [{"sig": "MEYCIQCx0cuAtNKm9iq6JeiIWyQWT5OuKxGXr/TLd8np/qdbOwIhANA55GB858ycgq5zkcFvrS4GxUI5ffYLD/dWSlmE14u6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs3zCRA9TVsSAnZWagAAfh0QAJomIJHn0FVfsdbcIAZ/\nojiDFgv9yQrltVSgxxG4RA099YXWFuklryikNSEDADnfeeucj8yTuMlDZcYe\n3JWkFyDjtP7D3nCPUpCVa+NKGVtBkO21su7+Zi0HXhveQK2KAsF+bif+UKF/\nBRF712zgdRG2WKAKaung80wEA7zR2YAdy2OGVP3HL7RIN5n8stlDrNv5zNQc\nAAeaQDh6wMBVEpogusnQJR0jftYweiPXjXLI1fv1dL3VpvzYSrpClIStDsXE\nB9u1adAQUTWKV7BuaGXXg6rPu22dN8+Z26DMqaI7J4lrAQJJ2rWRSxTkFd/B\n1muaLjFP8uvpwDoz0C7m24AU+SQ8iiG+ib8RY7ytB87TYLpcxpTdF/9UQSMs\nbF6aYE3qcysdOmxM2Jx2dPx0S1z7LV9oPdPnaEK22otThbsnXSuRO4ZgM/9I\nCB9fopW98JlrJ8j5SeNwLdB68O6mNHJPbNBBCxjO5kppl9LtFKTmwrQXLqKU\nt/bydryj4lB127VQUt963kTPIDjw7zR/Ldnu0eoVyWwKAKgLAEE1Y/IHZSQ8\nlyL/DVL9T07KUWd6BhumN4wXwUuacEV0VOWPkRHsVHSescNRhgHpf1JlLr8N\nlRbAEnvQkZAel7nkpG0Dotl8YSt7o8MQ0xt+JGF2LqVSXkQ7nhgXCGIUwd4q\nlycF\r\n=YI25\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-typeof-symbol", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"resolve": "^1.15.0", "@babel/core": "^7.8.4", "@babel/runtime": "^7.8.4", "@babel/runtime-corejs2": "^7.8.4", "@babel/runtime-corejs3": "^7.8.4", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.8.4_1580387827219_0.5571340470054063", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "60c0239b69965d166b80a84de7315c1bc7e0bb0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-qX8KZcmbvA23zDi+lk9s6hC1FM7jgLHYIjuLgULgc8QtYnmB3tAVIYkNoKRQ75qWBeyzcoMoK8ZQmogGtC/w0g==", "signatures": [{"sig": "MEUCIFanNyIMtX1GV/pY/E18J6nMw45lMKmbP7+fYqvy0n9qAiEAxIRuaKq9p8C8eKS7Bk1gATxrI4BqI7EdlO0HiVzrK24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSiCRA9TVsSAnZWagAA18IP/2M9MJQSd/tma5sNngyv\nb3HsL8sllB9DqETfdVYsB6tarzOVzrQM54NMoIUPMPBc7P3A4QxO6kFTO5+M\nXH1sHzkCBNGRLgFqjLGnU8/MnEUrVn4FiYXZsRR72JY2PYei+0xrHct/3ZhF\nx/Iv1UfmSBga/d6CYB9ko8o37slBll8RVTLXtZsiHylvFzUpeRYowLQt6eO/\nxOzRwL0SkvCPoXOxcZw92+9krn+/5gJ/9nFuZhz/osWuxVUYdW4+x7mB93AU\nrQOqJ5kptZGjX+mp2MP6HOcd7BVmlpl4/VzXL4jxM/WV8j2I5NAeVaTSiig/\nX94hq5c4kJrrAQ92bbbcRMhB5aBCsXiKyYopG10B8Fav9lZMthGWP/dGkW+f\nFN84/NISJoIKRPGbT2t06xkGaCe75i+DUCU02KnD86rzZOWdRrTgDajPvBSS\nyebyDVhyTbuSeuovW1zIdppUUeIYkU1LYesOT0ffPy7+Y4F/iZtVA2mpAgNL\nHHC80CSiV58RG+WpVFbo0LS2mNFrTYCVaTt33uRM8VXncViSM+tv/cLYt1PL\n8NUCCP3u+7q7fu6VtT9LC3k228TsVtg/HN7I8g9YiDj0CBBiO8u3K9hwrFrA\nkWiGx5lRhacto2LFPLRIj2pQ5DKC3x/uErtQ28GfP0eYGHSRYRStz7m+ZL3I\nexXK\r\n=q5/y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"resolve": "^1.15.0", "@babel/core": "^7.10.1", "@babel/runtime": "^7.10.1", "@babel/runtime-corejs2": "^7.10.1", "@babel/runtime-corejs3": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.10.1_1590617250463_0.6144155182702025", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9509f1a7eec31c4edbffe137c16cc33ff0bc5bfc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-QqNgYwuuW0y0H+kUE/GWSR45t/ccRhe14Fs/4ZRouNNQsyd4o3PG4OtHiIrepbM2WKUBDAXKCAK/Lk4VhzTaGA==", "signatures": [{"sig": "MEUCIQC3fzIUscyVrJbKxu1Hf8G0wFcRy2MVVK63satbZ/wUyAIgZAFhsKNmKcc5zyIxgiJcyZDgrCj0ukF0yF/036H/6KA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zouCRA9TVsSAnZWagAA258P/ivMfaj9+z76yVFN7SVQ\nQwASzZH4L82YPEWE60A0RbqfHRevDCdFFQB/J3oLKiL7CGGKzH/FTjTBSkPX\nxeH/jfKGdC3bqTUItGab56Otbc0SrsHeHtljWnpkfYOxhgenTBZVLSYEhTWH\nPlaUWL7F1hfAFQqDLsGwZO5t70NIZc7A22OqmISYcm3S5sHu7SPwRzPQqymY\nux72Kl0pjYPcxAxQTIxsk7KkHjpqaokkJ46+Tlz7AkL2Rs992cLq9ZGqonUj\n28kjH38GKe1gsqZ6OhPTdGGUAVYup9itUTHQD2BAqytnHYJDAwSDNeXCQPvB\nEUFTptmyvNXQOQvXiV8PtyFYHWRB8PrYY+gTadrQkQO5aeAuyyLO9VOtHDUh\nGbOLqSeZ4cWJeG01qV/uIwIKf3hHStq9zQqcqmwAOQ8pjhk4IUwQAz6d2PGJ\nnvE9+oHAmysfPe7BS8lPE6dBeGJ4u/zM1yWtCZJnNZb6LHGCc68tdUPHEyNs\nwkEJVVur04fSyZ6fFLfiBTGRUiXROrYphSimESGT6EwIgVm5tTXrsYW2DiKH\nHAF2RIED+Ms911HplugzuAR7tvmwN0tstULYY79wisnlZu89ehY56yth5E7b\nXcMSOsfA9PfBbWbi4bapM3kdJc7rFywS9O1TPxkzn9sI5Gl7f9m6CknmUlX9\n0YHP\r\n=815G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"resolve": "^1.15.0", "@babel/core": "^7.10.4", "@babel/runtime": "^7.10.4", "@babel/runtime-corejs2": "^7.10.4", "@babel/runtime-corejs3": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.10.4_1593522734032_0.4661851160439039", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "9ca6be343d42512fbc2e68236a82ae64bc7af78a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-EPGgpGy+O5Kg5pJFNDKuxt9RdmTgj5sgrus2XVeMp/ZIbOESadgILUbm50SNpghOh3/6yrbsH+NB5+WJTmsA7Q==", "signatures": [{"sig": "MEUCIQCTcNIcYddlttFB1IaV+6nL+L9CShSHXyzHD/FGHdTdfgIgImKzkD0gi4WilA4tqzfkjfhhdnj/SlblKmHct53dmTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/iCRA9TVsSAnZWagAAdSgP/1BPeGsDV6oWYWCJh942\ncK2u7XLNj0ZX9HAeLu40nO0bqAnjjUbY0VG3kqFT6cTAbC4M9jde0lGmpPi7\n0Ytr9/r31NVA5Z68lPOP9cGrcMU0q1KDV+6OxB8LFckNRdKKlZpg1ApQy+7A\nIBj3A98hRStlGG3Ub8rTmJ/pFE/TNR01C2O4L1BDN8t43/7s4zehDaLNya44\nZVfyYi2wW7wpEBiqQ/k2U5RaA/vjIAzYzY3CEdltJFvmNg9+maLNSpYzbb+k\nErHhlJUC1+bUC6okKYzbXX6XLtxScCnqeFOwB1P7XoZhQD4PEzPy4BWPnVF3\nHzemWb2Vt8A5ppDbz5s1ZmRYxDi3bA6gC3dBwkJFdBwfKyqjASzyO0IRAFTE\n9uiXIYVEV7v9nqcW4iTTKY5aPalm5RT9vaVFfjaMcSWpAfqgLFPKLyKZvlp0\n/EXOMSyGOyfkudC+IcvAiEzcGxHr3BvvR/ewzVpIIZS8G8v+nT63yVqR3pv3\nYdZJ3Ndq6DLGCmjf1ZyCMQnGeah1IN7HlOrGQXHnq+yG0xGMZnlHD65Omfx2\niO/wPGy/rWDsRJNF20xfztb/5uvFAwt7NvYWkXkwpPbC+Oxg967kbh4cl1DU\nLjRzNHJ17kVhT/TUimptc+QY3II9+Ow85K71YxqxNl3jcWvhAXvB46tuuSs3\nVd3Z\r\n=H6sy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"resolve": "^1.15.0", "@babel/core": "^7.12.1", "@babel/runtime": "^7.12.1", "@babel/runtime-corejs2": "^7.12.1", "@babel/runtime-corejs3": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.12.1_1602801633579_0.27543299708177926", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.12.10", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "de01c4c8f96580bd00f183072b0d0ecdcf0dec4b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.12.10.tgz", "fileCount": 4, "integrity": "sha512-JQ6H8<PERSON><PERSON><PERSON>//ijxspCjc21YPd3VLVoYtAwv3zQmqAt8YGYUtdo5usNhdl4b9/Vir2kPFZl6n1h0PfUz4hJhaA==", "signatures": [{"sig": "MEYCIQDNnsr4IlPUE2I57nY+2fR8AH9uxHm7SUtinEI7NZbKHgIhAPZ8kLDgp2Op7MP5woTOi7GMVnCKvKLvAa/i83/rHqYz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQcCRA9TVsSAnZWagAApK8QAJoiMFYB3ltd8a2VE1eE\nUBlVbi7DfihOhB89K0K1torbS7U9ZNGKavpbBnt59ZJJviQW9X99RG3PIU8C\nCA9TtdKnHL8tv7YLFf7R0+xrT1eEtAS+ZOWWZ0GrLmTdicimyaMa6WAzbDAN\n4f9bZTEbH+K+M9xKIohIl6mKDL+0ni4Lil6XaaclZaZFLB+idSMsYIx4TLkP\nCHRErngWPXUIZCGNMh+J73vS8lso4jAoXyXEZVyhwXEN06OCZ2h+eu0vZyGk\nIwbSXAeFfgq/w/Fhgp1pQGQztGAc+Khe6+ISX8KXhkg8jFvrjR092PtVhpz3\nPWgWZVLJOMZSFWlBTjTikUrbiOMZluG90hSq1zireQcj4TfLjlPQHYE5Sbl7\nmycj9YH6cfaXfplmoNzHMBT3t/HYhb9RqKq/wdGpJu96hLP5C4KAlMfOQ1zh\ncZW4D0w0waTZmmV0JimLOXp8n18ALrkPpEvasQs7NY6Z5n0/zXlcfF0751b5\nJVMuj7Poo66iO6EIzCB7o3TinWGtpVKCPEW6M7tGZCSBGfIEKiYM1h5ElHlm\nh86H00SuIxxWewiZgnkExjwjx3SKdlYY7Av0ydquZ3Fmdk5ed1Ma886OE/MC\norQDFNrKiYevoBlFIZTm8aw5JYC9rwouVg8fIBublPv4Qh5O4TAlbLymJcOD\n8cnl\r\n=G1mN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/runtime": "7.12.5", "@babel/runtime-corejs2": "7.12.5", "@babel/runtime-corejs3": "7.12.5", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.12.10_1607554075794_0.3369904615979764", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "785dd67a1f2ea579d9c2be722de8c84cb85f5a7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-eKv/LmUJpMnu4npgfvs3LiHhJua5fo/CysENxa45YCQXZwKnGCQKAg87bvoqSW1fFT+HA32l03Qxsm8ouTY3ZQ==", "signatures": [{"sig": "MEYCIQCwg3IOrMldA8b8VWYHhJO2srwSVq0TzdetnurVLizfbwIhAKHlJ4PP1uwzxQNWHbgkZBtKrSbubTZTJq/CaKI07tsx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgxCRA9TVsSAnZWagAAonoP/Azu+hrjTlm0+DUcikFG\n1ybn4fmZKRaqeIN8e3d7qaM4s9gUlARhpOuYwIE0AUsaMbbseuZFlXVLd0XF\n9c+xBqoZqesua0ZCuOcWiokSNpFHoF5rT9fsHuXhAb4tk1UI807NCUymX/Bh\nKh5vvTKqZTdwG2FPM0jZw+j8WOUosLGoi41NTN/SJ8TBDAV6bdJQkUyTvgfN\nzgPh0KEn630l646lCyyESczBb8AAPvLfNdY2MGwDD+ndJhmumYvak/Ch5qg6\nuo3xGQEbbdOOQn627qQgLlXx1WwL+vCGKmu/W3+CtZxDY7y+NhGUX6w4Z7Uo\nWAN/N3mVBRcfnmegviYtlCtLRNZ5q6v3H/CrHMl4IsvgOWbg8SgN40HVNyD/\nnhx4WMkvzA8RxTWeToMcg0NmIRJNQt/iPQZENbnD+xWx8yLt+KCE4rsS9rbX\nQHV/QudL4uy2PplSorQ86wuHuM03TnLwc9k/khRafLbmUQ2I/tmmsRR5XG37\nevG3qEy+H45pQXW5jM/CbcSdLa6PnU+DgtLtQt7ApChd6RPCfEBo1rPQ8Hd1\nUhEdJLYeoYlvm3Hfcfkb3AXr7jyd4LhDrasXDPNhtLt1L13LPDSET+xCFpFy\nxMdid6LA+br7IO9Vt3xZxbeVBWPkzxCoQBAIjztOmoCKDdewRHmbeutZQq+G\n5Sqh\r\n=572s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/runtime": "7.12.13", "@babel/runtime-corejs2": "7.12.13", "@babel/runtime-corejs3": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.12.13_1612314673383_0.8892953657597265", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "39af2739e989a2bd291bf6b53f16981423d457d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-lXzLD30ffCWseTbMQzrvDWqljvZlHkXU+CnseMhkMNqU1sASnCsz3tSzAaH3vCUXb9PHeUb90ZT1BdFTm1xxJw==", "signatures": [{"sig": "MEYCIQDUXlyFm4f2WKlBwiYOHWrTJbWo9gZj2zHdSLKE/YsUNwIhALVAyyuQFLDJKbMPEzXykelzz6Us37cai0EiylIb3YmF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrRCRA9TVsSAnZWagAAH6gQAJ0kGFdZIMMCeMraCAPj\nSbdQOWhAuQdCOdDQj8azMdg1moeSnCP/8thRUlOGVyID0/Dej74E208O1s7g\nWm7qBoJ/hWlzgphFnIZXsOVWZxa4osyp+7B0k6pNl4WhdJxmZQ8tYDK2cyqS\nBcuENeiP7EnwRSNovPFrg1QMqq5ski3fzZ2FbSqNaRt9HIIwrukuFrL09El7\nU1IP+nSRl0v09bYnRUFEVBrmkm6eHOaLs7mtgnL9NhzvcWq7nnXNx/PczIk0\nsXqo4ntgNW4KVJb0FSDbzvvPAtpz7pVBfaFEK2/KudyJRMbwNfLXEPBgAB1D\nXV2ZaivZDq5uORmyppc6IkHOnL1M4VFhg33K+t8TmrqUUbR+farG7u6FjQ1a\n8RFfWErfjJzs6/V9ejXQml/GeYVQdG0F1dRVy5CvyRMzf4eep8YHIRUufWPY\nJrgQC/Qt8a9Q0N9FP25eDZxBH1OIoJnDdKtCdL2IV4xFuEfBcsRaJjvN4X45\nPcY6LfkLt45mau5KePPQrRgxe4zbcB49miPi499nKmeV71YaWW6ljyfg3f3s\nXiu319A5/+rVIPt79aEX0ynem9hA5LNEbcNxYiv3sZhV+zEVwlq7NUYewpKq\nepdx72utS1aCR3p3wFKrCxxrWvMTnD/M0rjD7/gAFlBI3kLSXIB+5uBGMaMD\nhOV+\r\n=oo5j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/runtime": "7.14.5", "@babel/runtime-corejs2": "7.14.5", "@babel/runtime-corejs3": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.14.5_1623280336962_0.6580400650876048", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "8b19a244c6f8c9d668dca6a6f754ad6ead1128f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-++V2L8Bdf4vcaHi2raILnptTBjGEFxn5315YU+e8+EqXIucA+q349qWngCLpUYqqv233suJ6NOienIVUpS9cqg==", "signatures": [{"sig": "MEQCIF1U1SMuMOAJfJOfTYCUwoTSVFowkWNT2o1j/k2/T3spAiBgV9YBNF15zmYw2DEgLPGu/MnrVH9dRjQGr3Y5XzUGAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4889}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/runtime": "^7.16.0", "@babel/runtime-corejs2": "^7.16.0", "@babel/runtime-corejs3": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.16.0_1635551255430_0.15021974388065207", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "a1d1bf2c71573fe30965d0e4cd6a3291202e20ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-ldxCkW180qbrvyCVDzAUZqB0TAeF8W/vGJoRcaf75awm6By+PxfJKvuqVAnq8N9wz5Xa6mSpM19OfVKKVmGHSQ==", "signatures": [{"sig": "MEQCIFNjbWxKuSJL2NEaCAoKlXzrWNzCaVxYhbRe5FNfjG1WAiAcCy23WRYZl3TVbU0XvPLGk83cX5EN7fV4tjXk79G7lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kSCRA9TVsSAnZWagAAcekP/0USdsNAipV0oN9J82D0\nU63YDIuznMZYd71hhgcdLXMxSUDpKYSgNsx/sXFm5ZYvqMQw57JNS3No+evI\nSGYQWkWugpL4xIXS86oZl7YzJEDBvLz00HH4WnZZXdTG67j/6nbGmUiRxVpa\nA9riGy5xg984G0ycmgkfs8Ziavp6BDRkXJWha2KKJgjsAFQr3GMTHdqKeP6d\nY2pstesjYom0FTmzRlLVV0NdsXBJwm6r+IS82MOISEMEuAvAU0KOzfKyoO0i\ne6qkHE9pT1J81cnBzqq30kVXC3IpwN0bDgGR6hJ4SclOnkjb/30y4BEev60A\ns8jr2/ZCj6dmFepAZSxCzOw9MiOguoF+eIPJtGMzvU2Xq/hfSsvUMbXE9LNo\nIAbT+JVy3TFtM1GDwxS3iM6vH6I/5kOI9CuXO1ofVjOmuSCexMR9f9M4a+QN\nSG/JldC1QYgBtrS2e7DzrlhuhGMIbGCcaKAyFqXoSonOC2ftjCqpZhNcT02D\nLq1Fo+urTnYZUUh79YNWSnouNc3lZQNcyBkYVy67E0TsDKtqQoS/pvf7hv6q\nL4qwm1Wzwz+7H8ySeCup1vZZ6NAdwC4JYQW0Sixi+Jm4Hy/WWy0Gvk+l2jMK\nak6Ijrpo6Klv2DzhkwXy1AtBD9wA9ZvIaooqlO4dL3FNtqETP87ia7Yo1iBi\nHiZT\r\n=CzNK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/runtime": "^7.16.5", "@babel/runtime-corejs2": "^7.16.5", "@babel/runtime-corejs3": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.16.5_1639434513996_0.6485515405499243", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "9cdbe622582c21368bd482b660ba87d5545d4f7e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-p2rOixCKRJzpg9JB4gjnG4gjWkWa89ZoYUnl9snJ1cWIcTH/hvxZqfO+WjG6T8DRBpctEol5jw1O5rA8gkCokQ==", "signatures": [{"sig": "MEQCIC34OG9YkSCF5Xop5ht9/ndurAdgGWYLC06Xgj+EooEjAiAfYR78wR2STTGXdKgxzKtFxAynka5hGCFIa6Fj9TiITQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk00CRA9TVsSAnZWagAAAfQP/jNq4lF1YFgqhyCWeWTd\nAjTxtlWm5ITKu4aodrNZF958Eoyy9nYEbPHRpFI6Bs0bImyXLXrNo3pREI0P\nDy8k0NwUGIMjd0PHgQanZTApBv/1ztj8VfRdnYODZp6U2ggfAlFBTvc3gu0P\nVomVwbTdDXluUYpPYYvUW+uBKphwIqauZSasCxh3qTFvrjFDTa9EDmS+9UG0\ne1krO3NXKYADNi+EePgcCkKQxKQgx3PUnKC/9ma7Q5OJwvgwYa6w2mCyqr2d\nPsSGnRt1grHmCVSKd8ebl7TJ5PHgfwMDp++hkyJ9xKN4Swc7KyUWf7hIqPJx\nKJu7Sb96EDhJ46EEFryFOh2C7wEHfA6ByxOEp8Zn7SuFr3HBvcKgXNW+qByL\nXY0ecm7bILhzR/gWQY2FfhL93Jj39LiWyBc6MER9qeF9c+JC6PdyfbnnpCyo\nruSOINyaweoQXbW3oVfBbStpiORIeHUHeSJFJgzD6/jp2BlGhXVDaLKaXRGF\nLlqcCxkJEh7Y8zOMg95HY5+4kl1lyKw5CsagH3ikipzsqKi6gaovaTPETFzu\nmxW6d1ndIE2XX70J+iwO3oAEFQJ7arwDvIvjEowpHwQzO/MI6L4q9Ilm46u+\n+S9tR03pTu+8wHE9gWbqomcev2LC0xA8umJGEsJs18wxnH44gKcjXnEppM70\ntYFJ\r\n=XPxZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/runtime": "^7.16.7", "@babel/runtime-corejs2": "^7.16.7", "@babel/runtime-corejs3": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.16.7_1640910132827_0.6563264613148589", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "0f12f57ac35e98b35b4ed34829948d42bd0e6889", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-Q8y+Jp7ZdtSPXCThB6zjQ74N3lj0f6TDh1Hnf5B+sYlzQ8i5Pjp8gW0My79iekSpT4WnI06blqP6DT0OmaXXmw==", "signatures": [{"sig": "MEUCIQCUE3n/3Ms4Vgz9PNIY0KnpZMlgaR6pw2suXtA0TmVShAIgc+ZSE9Ci525LJvp6HhqDo+QRJncaip6Kv5O4aRLwhuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFnxAAkU0cuZdSIBrwfIzhnYDLO7OyDMnxVA2dltv2OZwHnvv2jULf\r\ny7wwLCUeqqBWwBJ4t1T8AN+ciYPBfSSj1aG6xUFiQsx3tEmnHh3eDy1X0Cpl\r\nbcr95fMdaykNU2+EvIevVnpyQkiUamuSLCIdj8sI4kTGYKHRZTGWEJtcbokP\r\nSxW2MRYDvCj7+eeGabCZZLBCTNDUhwgHUSsUbXWQDjM837BAOaEHIGOWV1rL\r\nTecvIirzvpvliasLWEBF2Xe6+9rrvthQg2D5y3tyBwJi9jFVupT7u75Crn56\r\nuEGT9JufIsBe3DoH8ZsCCIVHol4vq3xW/quA5PoQBd1aMugbzJkZEJCtyP95\r\njw5nQwAPW7Xt+pDwgTK2obDOY5fHfkJCqgikJ3HGVuP/rhBKODnIns6sqpc1\r\nxMcgBdYp93ElqW3OE2SioZs/JBaoH4Kw6rayVfkdTgTt5dB3pe0nqyRV9a6R\r\n3sv2suZfIIdbJaBuo7Fvj7rGgcsPw1J2DUp9SjtGRVyr69GrkkInJonjwnzD\r\n88qnF0PRvc2dKi22wFBBaBNPwxt6cybp8wWQ+hJMGlutvgxgv61weRrgpzOj\r\nDpKcch5vewsOZLETNxWgo/QJr5wvR1Gtb2JLnYQxgulqU2MyenY+3g4A7n7r\r\n5jHXuobH6DEnKASgiXitqmCTHtiU1jGnL3s=\r\n=dtwb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/runtime": "^7.17.9", "@babel/runtime-corejs2": "^7.17.11", "@babel/runtime-corejs3": "^7.17.9", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.17.12_1652729588404_0.21204419480017211", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "486bb39d5a18047358e0d04dc0d2f322f0b92e92", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-7m71iS/QhsPk85xSjFPovHPcH3H9qeyzsujhTc+vcdnsXavoWYJ74zx0lP5RhpC5+iDnVLO+PPMHzC11qels1g==", "signatures": [{"sig": "MEUCIBHAUt8pBN4hG5IOURdHs6sdwELQZU/Dx+iUDqlcGVPbAiEA602dvFOwGcQ3YG8GS1PDm6CbWnosle6QhUYxaZPbBPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugntACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0eg/8DFAl82J0ZgmvBs2J0Bz5Tq6Vh5A90qHJfcTKisHohvBqXNgv\r\nL7A4nEi2BIdgg6C1JnPUBtFHbLmNknTWIvZLePIv+NpdP4nkZEAGq92KWe2Q\r\nh8JNLBXwEEGenFSyiJZ5/CuXgxPqrbOQwPdAzK2SN/qiroo3ZNUhJU3OrAsn\r\nzjoag73bsFHSS3/U/tqzjKeJcydvkqzvdvlzsGnY96h36TdbyRmc9la7q+1B\r\nCACjj10z96mG+XY7BTYdB0TKkuQmUNU3CI7mySbrsKpSLfWMnseQq7TwxG/k\r\nfqbTOVkIK+1lEDyQwUTmDEhqI0zbuzuMqsqxKWPyYVIz0HzmfwOxTSZA9XEh\r\ndh7AO42b0F0faC3bjGEm5JbBuvQSeUHz7oNuaCX6nngDDRHa5FT4XIFZ6rEx\r\nmOHdV2hJdNgOdlSyPqTNiUqqLkDza4uc3RtI2EVCjsGsj3ooc6xV1FbHG8Uu\r\nd2d0Lv89MtwddBg59R/SQzJc+FWG2ket8RWQstlGo5DKTXarLWTy9Tx9/Y69\r\nVVLrjTe5qlXdhqjfdavMi2fSypnLHeAUR756YQoNavBYc+eucKdSQE/krTCP\r\nDslJPW4D41k4zWTFEdz8mFxihDFIRRzn5nnrV91pXRBxlG3mz3uV2RmZnuyM\r\nfBZEL6rd4/wVBPrtxzlGMkKasmWV1PycdlI=\r\n=3YIz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/runtime": "^7.18.6", "@babel/runtime-corejs2": "^7.18.6", "@babel/runtime-corejs3": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.18.6_1656359405415_0.05711525436807996", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "c8cea68263e45addcd6afc9091429f80925762c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==", "signatures": [{"sig": "MEUCIAMXNMtf+ws8VZIDuN9XijaH0y3FXdjPrpguieLkvrfyAiEAs7gCGK5eTzRFhnYlWjFOIty8N6buOrgTH6aSTA5Xs1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr25RAAiXE2tVuhNXZrI1O/fXGleCQxVdAbCifYk1BakRdG6NMTCGci\r\ng4YcVXK3B9ymMs4TzX6euTk0nx63BA/C5GRdUT6ERm1ml5c0x/H+GKhrulae\r\ndkLSBPFFz2fthxgOqsiNoQGmLSXFVKQXJcO1r3/dhQ+D4WXFRbC79ETZeEJi\r\nkm7CFivjG6uej9v5j2QkzGPsq4R2XWvgXupm8Famjr3lHg5mEgkVjCtBGEHN\r\ni0nUGg3tYrvl8vdwT862kdrJZnC/8Z+1RFRI/pq/7mEVqvJtYq3IohVmk9WZ\r\nBfd5rmia47ZjxgpQR9KnVlVbkb3STk2ijlTLCJa4jEh/DU2tqVFzRdz1ER9Y\r\n7GExvglClpEREP0oKxf4mqCDcl1fJMFvQG6xitvGkmqaxu9qGfVNEJy8VF3F\r\nxasQhRQmQF8lj8nu0lb0eWjzsrBUggxnR68Tj/BvhgvZysEVdcqlCs85hY3p\r\nEkL9B7PBt6W9kHU7FN/fV8JgVLsQqyd2W3n8+0FEfxoW6Z6A/h0Sxg4vz6yI\r\nkytytiV9Kd8z1OtO5DRnqsNAyrmdQtgLSps97EcD9S2/2oMNeEL+22WkC7Ek\r\nM7vfLMUWjcfYf9bakP73LmC9utlYi6vsXbZ/lsMI1N8Jspb8jaRdTk5FlUbz\r\nPOejnKugAOdaA2qAJ5jPyaoD/uZxFzzbpdw=\r\n=tiP7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/runtime": "^7.18.9", "@babel/runtime-corejs2": "^7.18.9", "@babel/runtime-corejs3": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.18.9_1658135853822_0.22619949742490553", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "af104001639fd7ac39aeec6537941915e412c282", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-0jubXhrd9AjrhdRC1gx5/EZMSdzcKS/wLop2uLXc1gYaDrYoTXawd5YdEugG+uwyFN1gzVHMg9XHoxe6Oyy+1Q==", "signatures": [{"sig": "MEQCIFxrF1rhNp0dQT3SHyFcrVMFHwvUTUsOq7YM9xVH4GqNAiAHDx2t22g/v7PhtQyn4Wqhm0aYgO5nxLgFyam+UWdU0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp87hAAlARZ6lUS1LVeVz2El55BmXKmsEnWXJ0K+qkLPKBfm8CfXuLl\r\nOMhKZjtFUzxPTGEkwPYEeUJRMNK9BnAgIC9bC1aRPXk3Wx/PFZWjENiYAP53\r\nTCCIZJ+5Qo7hHKlU5rAmGzjIRtNcmxxC4JEeqOalGhMNhBKM2dAi2Cb66McD\r\nm4dqI2oESiH0Zn0Z+0CV7Khu1PBM4ShE6dgRZSUTX+OENMSmFpJ8R4vSfZwj\r\n28soewzRkFSKE5S/I+S4E/JK3/vaSQ07x2ZcRcQ3r9B2sNBBQSeG+j3ZvD4c\r\n8rszuAP/NCprSF4e6MTsIvXiFSgH39PXldsGeUBXtPv1HYWi5ZUh/wMa2v8v\r\n2EPNxWFalGjtm5/rYEr0MB8hsAxus6cirqMgvJ7DccHpQ+UX1nRRXfoNYtkN\r\nKpSiA5deSFtzW4CfoIhXdrin9cHXwcqbQtpgd6EF6ivl9qEr37EHZ5pXWsFj\r\ncBdOfAx4TslmNrKry1/j5ajWF4ul9yjwpUuEiM6XO0zfp5pYDp9rikjFccDh\r\nhDfSLufZp6twGoPXAT4DH0XetHMZIvm4GO1E7bsOOQrA8iWDUhhkaRTRQD0S\r\n4UwNCzLuuv98vqg1f8k/F+SUvo0dUZUFnpR2Z87GNA9jOehcIgnrK7mD+pby\r\nTdFEwRUZoBe5Ww6z9C7ShFSpJs+A/eUV3IY=\r\n=v21i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/runtime": "^7.21.4-esm", "@babel/runtime-corejs2": "^7.21.4-esm", "@babel/runtime-corejs3": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.21.4-esm_1680617371001_0.7227640311955579", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "c50b982553f23e3a1663d02d24e708027f57c37b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-dsljD3CbChECntrahj/yrfU4eoTQEsHH3YyFCn/4WJhVYTT7Ipe1GdYkEqDLnahyTsJFgVXfruvxsNaAVw97/A==", "signatures": [{"sig": "MEYCIQDecOnMP+P4nuC349zddpfYoCTcmTxdbjQchs5QJX1daQIhAPzmtYMDT57g5XXINqdep4HBri6V5JEQwmvJSigR4U9X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCGA//f17gD59bM3UOi4EGmjh7bv9x7ri7k7+GdTnpDxjqOxjqJ6Y4\r\ntbSCCLCt5SdLE6Hok+8oLl7Wn+Yxrq3LMegbh9MD3alTRIXnWVF0G9zLPykQ\r\nC6OC4D7xG1BdDGCN8QrW7sj6E2i09n0rAvM73tTDY6u4kkx8884Dy/076pxJ\r\n166eVjvrAqhh2pM4qxezyRiGP19Dg74gyZAhf9Ezq3qLrkoXc9VKWFFpNqnF\r\nu35djMymXE5oloZgvAo2JOM+oc/hDbdpNq4Z91IDXCZNy5bCnQ7QJKx22lHR\r\nAmIp7i53E+0XsHAURurNofsX5KSjV/0gH22Gqz9lUTitk2Yl0HPego1Gxjvt\r\nQt/zXeBByGWvJ50brUpclkkmE8IlppDmuc6QzhONKLdF0vrQWAlmb+8sfx/n\r\nOCf2uigliJ6iLJkbyPCk/TK0Rpy6XthT7p83bsil0NiTQsJrZlxDsvOLgJZY\r\nxOpjAXHqCBAbZPfb3Tmdo+v/fATCPlaNDVk7W0tQCe7aEDVAgiuzsKUcz9zE\r\n31LizBCvnNgNCh5r57rxlRe0jA6/+vr4GrMKeDAPMmxdAqRCpw/eSrkkIrlL\r\n/bUFhj7nvJX50HN1bafxG6d9aEsU/v9SF/1lSLPIheETHcPyGfgjB9v/ie23\r\ntUAdEtVYu/5VHcrPiBOnwKyD9Ote01MYCDg=\r\n=a/UY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/runtime": "^7.21.4-esm.1", "@babel/runtime-corejs2": "^7.21.4-esm.1", "@babel/runtime-corejs3": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.21.4-esm.1_1680618084016_0.4249161062504254", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "2977627f97c8cd79fabc474ae608ef6197bfcef9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-223vQxLKZwnev9+ECovspoiZF+/WQkBOV+OPbnyc8f0/BtczWix40wWiyU14BaFLcnDgBocLyBjl9azuqhsYfg==", "signatures": [{"sig": "MEUCIFL4afgp8q6/8BTwx6zqLZzUgIHlOUYakY06ZpsLgU09AiEA5MrNPJsv+B+lpdDXERoyY2rw6OmjIMEOHNlDYazcJUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDadACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpT5Q/7BVKcx9GlsIfv20hB5QlSrxsQrGhb/3FYnUVjgm4jvVT3RHJu\r\n9hBpySXPT7o8mH/9ZteHrF/amw00KV9sCuEs7V2y6CXgZJE1mHEeubl0e9eM\r\n2vDzt7Q0wC0BFNeqIPPi4bnFjKRmWonYQD4XsDyOBxhejNH3gvO8MVummAKc\r\ngOnIUZJkk53Sw5jC+/N/FnUiEcOxc5Qcqihr0A+RwnT9syUET7NE1tiQ3Kuk\r\ntWMgKVDHEEuvrRFttoz7QF+ye+FXMwmt19hsdsddMMUZjy2f3Wk/tMYNoQQI\r\n3M6LmfSQtp+2MKnszPNLc7OThe8ysQFiiq5Ka4dXhwLd+4Lkx7uqIIoZWK/x\r\naV0/B9/JpAWsWvZMSoSaqaL6yePzwMGW19Er3Xll+8Eq/prNKRHi1glkVqCU\r\nkNwQg+udYm/Mq9xBT6scsgoeZth79O9axhcb0DcaR8iyX6tedlkigEzvR6N9\r\nnkJCbMAGeWO1rk8Jokn4FJUJ3h7c6/Q7y7mEQzq29aYfWqE0x558CsKl3Lo8\r\njZW7/gwqxabJuciEt1IXQ9LfIMXyMcOdcacx2Ss6aqcm6st6k2h+/iS3XfV9\r\n31cXtuQJ7HK4Oq/F1tp/LO6HHAr+525zEpBIAp+40V82HDnTAm1uzgaxtJK4\r\ngVv6kroQZ923/NAhg0BoK2I2vb+X9hkURDI=\r\n=Cssq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/runtime": "7.21.4-esm.2", "@babel/runtime-corejs2": "7.21.4-esm.2", "@babel/runtime-corejs3": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.21.4-esm.2_1680619165241_0.40892035438074115", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "0c849571d9a8fc1cd6fb6dd77a56027007b2b171", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-qPHct6n61haoYbp78dWZgmxCcj8t/w/NJJmPghqOZdl+uapw/EtkVxlEkowNNuYKQybVRYPZiC02eKTjWZ2Rcg==", "signatures": [{"sig": "MEUCIGl2oWEU6dBtFc39g0Gl4R4wkUCiQV1OupzKh6fA1sDSAiEAvxCeNCnRpICfqRlGopbPRMR0PMjCQsa84mJs5Am1hvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm4Q//S+QFyqcyACPXC1CVe5G0CI99YYU+LDqX2zPHcgO64dw0kSJj\r\nbI1rHU8bPytFWqXboLcEtm6DFDH6FXSXcK9d+bRLLUBc7WVaf/Af+w7OpSeh\r\nRmKI1VXWc5AEc0tlUPGJ3pyNgiRLqJRpPMaZurXNRIJ5oY0aRvcpWm7YBfXA\r\nbuaxqfP/cUTAqjt3XoU0JehuLFxp1YJd7+n+UsGxXT8EOI/X9E1MMefaFvsV\r\noFeWuI8ez9CdMoqSU8fqM8YMNBncTDh2+IPGqbJdGM1UIYZB1wd3PNO50H0z\r\nmTttmELTkAZ8cudUSwyctPcarRZ4KMrw+a8kuKZ9YJQ8dxCL0zgAfHgpT9Od\r\nhHe4dK18QqzdrWNXVehUrQo0zCjhqbF3pePmvYpUTP2wfv0WrnLcPmCG1XYc\r\n0kBDymqJu+APeDgpVxdWCtyqFoCLzajS1Nxh5XL1nt9qWeslMIZAOs0CRNPa\r\nwf/6Y8jUrFoHVGW/TH4fxwe5WOr1/WvxUgEmlHAO/1qLBch0lgEaUHu+xOF9\r\nMMt2FfsMQcUP6ZlABK8/7q2oRLKqparjQEDI/FDeSiWmdvGd4LfOCSzLUfYY\r\nXlFcw3xLA6wksbC6FsvZZx4p/nrZ4UdCk1I/ookG0xc2UdWMBkWY1fOkrJM5\r\n+72S7RRTjyX699xmLXyM25a0mPTHcnZe9B8=\r\n=fgQ9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/runtime": "7.21.4-esm.3", "@babel/runtime-corejs2": "7.21.4-esm.3", "@babel/runtime-corejs3": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.21.4-esm.3_1680620173203_0.09076040068958569", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "7e775b6fd32a17eb7d4e151d97250a75846446fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-CZn9pMw66mnrAWawPGvdSLromyRrN7z6SgxxhKZ9JUroRHZG1YRY1NfBONLlRCWeWe43BtOYjQbaUoh3U+geBA==", "signatures": [{"sig": "MEUCIQC4NKW4EqHxXSXVF0++u7mytrk7haOday+TmdO1p6OE3wIgG8yq24FNPfKzFQp8xCYWxJZyhBuFpAiHagwOg0ZfqYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkyRAAmlt1b6VrGN6lBbaGUkCYwnHRaNFkxpJs1TLN6plLfaZ6gROt\r\nfO+CNHrDgC4yZu002M+w82nHYw12thhBEaYKoLabGSfQp4qSuqp/LDVaY0Fk\r\nrybieZJAyyfvMKw4pZ07CTgice7UQD/RO8C8fhyN0WTeQQxcIZ0/iiFenEHI\r\nDiIHoVYJuMPWp4Z+THtVcIFPECmfjGdcs31lUeyfi96B7Q4V8hxVSsGw14dJ\r\nGbKcj0JBVxOSkjEymrZUhH7yagQkmFGLGkuLgBMSaTDkwn0auQ4vRA9NFyPI\r\nLTCu2CVoluIPm2f8HzBeQt5BLdVNM3wX5KgBr6AoA0Z4UtSwF31vPYZsXbNK\r\ngglm74Ao+YrY0hqndusAcNOwAsbv+tcWiUiFvuvp+1nfMoGUwfvHq79ysThI\r\naNfYgrF+ku/aW31XwvemHEvOu5bzYMUs69vsdVMeZUPnLqxbiuloccr5ewXP\r\nB8/TO4Bx9KhJlhMwWHq3rdjDaIQ7o5iXxO2qS9vhwU6/eLBLSGcE0eMZiyqI\r\nD4Dxnkm/Vx9qSztl8Ke1fiGbpCHWfZ7Vz+BnSIaHpvOihXdtSvaAt4UJdi4E\r\nrcyBUAdgC7yPdgPIr9/WBIw3n1R9P+XwShXI2HMOF0Frpb1DDPCoLybZSz4y\r\n9dvOLjs8b7KTr+YJUoWZmXWkpWY8Pgl0XBU=\r\n=6vXq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/runtime": "7.21.4-esm.4", "@babel/runtime-corejs2": "7.21.4-esm.4", "@babel/runtime-corejs3": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.21.4-esm.4_1680621205654_0.6876970638100839", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "5e2ba478da4b603af8673ff7c54f75a97b716b34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-bYkI5lMzL4kPii4HHEEChkD0rkc+nvnlR6+o/qdqR6zrm0Sv/nodmyLhlq2DO0YKLUNd2VePmPRjJXSBh9OIdA==", "signatures": [{"sig": "MEYCIQDUd5fCLAYHWiuuIMw3M2k9gZqzB4pBq5mRoXOJXNn/mQIhAK21UJa2HixYmPZJ6nyxyrvQi0FMryfQdzoApfA0kCwX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10521}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/runtime": "^7.22.5", "@babel/runtime-corejs2": "^7.22.5", "@babel/runtime-corejs3": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.22.5_1686248483091_0.767395363921348", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "9db6acd589fe109fa9e8e5ae2d1d2590720c1ce7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-brlV7Z/bzoBO3LvzrAc7RgjKHBokf7JQD6bwjC01saqG1wMDomU69MIqGiK+sxP/JGXrztleyvw/IrSFDZsWEw==", "signatures": [{"sig": "MEUCIQChEwPzPMGvLuodpbJQ3bqRAOMmVFH0TydfGGSSs1x9jgIgb+42z6zdEs6iDsybnM8kLoF7SLsB89ppo8ZHmMr/adg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10452}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/runtime": "^8.0.0-alpha.0", "@babel/runtime-corejs2": "^8.0.0-alpha.0", "@babel/runtime-corejs3": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.0_1689861599595_0.13393580239331704", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "145368bd6de0fafbf5511006bc6c93f228ccb2cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-YkQcFS5t/xvaCNV+5hdfQ+4i5E23/pVXyWlFLpeRQcjxlH7N4kiS9AaJLBDl6pkju3HpYTAGlcpJB+u2lBVtOg==", "signatures": [{"sig": "MEUCIAWTrMiHoRMwJdQfwqE0nBcA0JCYeo12LK3rJ7n9uS4UAiEAj+1lJd59+zTRSf7tSH6mhJxs1UWHnWjDD3+uxpAagaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10452}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/runtime": "^8.0.0-alpha.1", "@babel/runtime-corejs2": "^8.0.0-alpha.1", "@babel/runtime-corejs3": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.1_1690221123691_0.275226982837234", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "67ebc20f43d5b0fdb8ff959bef2dc6b73cbc6823", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-mx7whGS7EIpT0STXTw3OPvTE11viD7YtqWHwIyrFklcL4VZZU5mvfgROc9z/adE3494Vs2HuE46Ct44s8pYJWg==", "signatures": [{"sig": "MEUCIDECD4BkoZD9jIwWmPlN9K63obmXznjwhksudxNLQN8HAiEAlmRzRq3tHXZIWKYqBQmZ5p+uPYokvGsqoT1eGGFds6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10452}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/runtime": "^8.0.0-alpha.2", "@babel/runtime-corejs2": "^8.0.0-alpha.2", "@babel/runtime-corejs3": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.2_1691594098837_0.5857524934531351", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "23419321df653097c572eefdf82fbe59f10c51f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-MDaoWQiZEi3j77yQCfQuXInEwhOLl6Ctd9+pW2j4auKxHJ4ciNR/8kDzbcUbIxlSM8xnynsluVMu1OuUit51XA==", "signatures": [{"sig": "MEUCIQDBolA57a7LRzbcyQAdqeTiwX63xGosMdIijX0J7LuxKgIgP1O8QJWwN4adZLBZ7WRPOMS2AFVT8RpMK3x8MFAfMk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10452}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/runtime": "^8.0.0-alpha.3", "@babel/runtime-corejs2": "^8.0.0-alpha.3", "@babel/runtime-corejs3": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.3_1695740222212_0.20046179027377597", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "e8ecefe0a6c5daa18dd0078d029025ea3f5419ee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-JsJlOYMC11Zj+Vg1Ii4zR951I0Hq9L8/KmDRYdl1wW5ebkdsD3QvjUKs19IIVKWxoK5pOUxvbU/OGxY1KmI3OA==", "signatures": [{"sig": "MEYCIQCJAc64qLHzTgpb+JJqgy56TlI3PtcYNhJzo1gz+hfyWgIhAK7/c+rtaJs+tCqMT3rUrVCCr+ZTTecTdTWTM616QWsM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10452}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/runtime": "^8.0.0-alpha.4", "@babel/runtime-corejs2": "^8.0.0-alpha.4", "@babel/runtime-corejs3": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.4_1697076381916_0.8566820173812393", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "9dfab97acc87495c0c449014eb9c547d8966bca4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-4t15ViVnaFdrPC74be1gXBSMzXk3B4Us9lP7uLRQHTFpV5Dvt33pn+2MyyNxmN3VTTm3oTrZVMUmuw3oBnQ2oQ==", "signatures": [{"sig": "MEUCIAVkl9yWOGJGwgy7auGFt35IlijyuGPXQQtcHLawPKnLAiEAixplQGLENmzUprLY7P6W7vdKry1djqF5qrhmkWQ/sbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10600}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/runtime": "^7.23.2", "@babel/runtime-corejs2": "^7.23.2", "@babel/runtime-corejs3": "^7.23.2", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.23.3_1699513442543_0.8403204895350653", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "e0dd5389fe6c4aba9c58788ef6732986965add9b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-5fCSQqijJO9fyqiXVqmTxdMJnlCUXRf0o60ijYY7XkskfCCYYABhbEigvS7ZSedRHaGUtsJyo0nL2AqYefgsew==", "signatures": [{"sig": "MEYCIQDNPatZRYebDmc/G1CDognCgqN7IQM/quUdyBMPkFQI5gIhAI2mVxfkX+OENBtX7uGYqa7ONdDDq1UOYoXXAtX5yHHx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10565}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/runtime": "^8.0.0-alpha.5", "@babel/runtime-corejs2": "^8.0.0-alpha.5", "@babel/runtime-corejs3": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.5_1702307939467_0.38947129546278636", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "4caa4f2d6430d9e9835c704c3f58dc2ec5aa77c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-9l2CJaWDTxKh0HEdO4gw7xBhuiF2dMC6mlohU+BBurhx249N3mE6lW04weKRAgt/8PYgqUaZykkczzI0xHXphw==", "signatures": [{"sig": "MEYCIQCSNxLolkNiPMywaTEflNGVokZdKbjohM6RQ6lRUQhccgIhAMH3XZ2a0O1lwrDvrJBr873i2nV6ZEqO4MM3YZEeM+a5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10565}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/runtime": "^8.0.0-alpha.6", "@babel/runtime-corejs2": "^8.0.0-alpha.6", "@babel/runtime-corejs3": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.6_1706285649788_0.28496431965878566", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "4eea4425cb3e6b37c425eac5fe4c62e9bf1c452d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-9YH6jvoU4ZVYAk6DYSW9CpYdD6IGIqnZga+zE1Tz2pDqGA15QEZhAOeNyiYplHpQy8n+5Cd/EnRFY4JivJs+xA==", "signatures": [{"sig": "MEYCIQDKoPxuIw0B98QykLD9QbM2tD3fJcmrR+G/+nV8chWxwQIhANCWLJEgU0K/f8WjRv0n+3po4kp9TJax+FATrQnZKtOi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10565}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/runtime": "^8.0.0-alpha.7", "@babel/runtime-corejs2": "^8.0.0-alpha.7", "@babel/runtime-corejs3": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.7_1709129099534_0.21055279141202998", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "6831f78647080dec044f7e9f68003d99424f94c7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-CBfU4l/A+KruSUoW+vTQthwcAdwuqbpRNB8HQKlZABwHRhsdHZ9fezp4Sn18PeAlYxTNiLMlx4xUBV3AWfg1BA==", "signatures": [{"sig": "MEUCIQDywMzlaZocOyRT0W7cceisEAJegI099GFAHX+UvorCQwIgf2HrlmrF9Y7DK3CD+BpA/X8vN6786hRdAPSJIxTEnsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10531}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/runtime": "^7.24.1", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.24.1_1710841746843_0.9079580039240276", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "3cf759a484a6c5203f3477f9092156a4a165449f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-E1X1NUTXQjlRDfgxn5mVF5RXhWcMaQgmloBXTur4/Uve+fRe0Wx4BjAZaKB42nLJP51ns80s2ylZIpeOcDttiw==", "signatures": [{"sig": "MEUCIQCesxaspr9kU7kxZhPBtQtFy3tAhtEEmxkjiN1KLCLp/gIgIyFUz9SBy0JY3d2UbAXvXh0Dy99cg71LzXXOhm9uMFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10472}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/runtime": "^8.0.0-alpha.8", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.8_1712236793800_0.46549447844155734", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "703cace5ef74155fb5eecab63cbfc39bdd25fe12", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-UTGnhYVZtTAjdwOTzT+sCyXmTn8AhaxOS/MjG9REclZ6ULHWF9KoCZur0HSGU7hk8PdBFKKbYe6+gqdXWz84Jg==", "signatures": [{"sig": "MEUCIQD56+OVp7fPTeSI0lffc43XAG1LWZoauBLzdxUJPztDRAIgYynXFsb6pcRghhEgZq+HLcK86i62SkAqRkYnQDiYf8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76196}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/runtime": "^7.24.5", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.24.5_1714415660109_0.6099344330619909", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "3d02da23ebcc8f1982ddcd1f2581cf3ee4e58762", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-IshCXQ+G9JIFJI7bUpxTE/oA2lgVLAIK8q1KdJNoPXOpvRaNjMySGuvLfBw/Xi2/1lLo953uE8hyYSDW3TSYig==", "signatures": [{"sig": "MEQCIB2AaFxswa3UCuK9zpb7yfaXS1HvaF7c5knRgc86Tu0WAiBnBSFYnCdjX/b3UG/hrwCM1QMVG4Qtcb76TLSa7XFJgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76365}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/runtime": "^7.24.6", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.24.6_1716553476102_0.14954715320831502", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "2b106b15e94c49d2cafa3f81598a274b37055d97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-76uS2eSKjSeFps5XMLbrTzYLiDzA5n6svsbgdLiTx7rF5jVeSDtUlKxAFOQ8q0GpqiHFVC6kTHMvWjEJQQPnVQ==", "signatures": [{"sig": "MEQCIGvZlvz3Rar44cCerHGwEuNiGNEUZtBHdKgK6uCZ2D2lAiAmKrTPOPQ61vRWlFS1lPbb1cXtSiilrWByNPLjfaaxtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76616}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/runtime": "^8.0.0-alpha.9", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.9_1717423461628_0.0428730952178733", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "5b360ff3b9eb1a712cff59465210e3be99017d20", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-ihvLfY/u05nvmAz7PsMmFxhhFjUENtaYnXqlZUWkMb7nUxuyXihoqHNotiMT9xLnpL/d69SsBOKYmgxEQClSsw==", "signatures": [{"sig": "MEUCIHUFuSMTPnSeKDPj6LmsotblM8SMhIPaJfTKgAVDXCEQAiEAnZ1hnR/QjzNpBXQhBDwkXavqCOOuS4Wd3b+IAMCSxCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76625}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/runtime": "^8.0.0-alpha.10", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.10_1717500011922_0.2376585056287399", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "f074be466580d47d6e6b27473a840c9f9ca08fb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-VtR8hDy7YLB7+Pet9IarXjg/zgCMSF+1mNS/EQEiEaUPoFXCVsHG64SIxcaaI2zJgRiv+YmgaQESUfWAdbjzgg==", "signatures": [{"sig": "MEYCIQCawmehRTD7INPIMExxaK9qWE1qbp7M7aGze5VAzy4b/AIhAM1+cVbpscGeQZeu7qnSe2fmBenOl4jvpBVPFGwEVSyA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76361}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/runtime": "^7.24.7", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.24.7_1717593325779_0.8024224660346415", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "b325dbd58073e607abb589137981b440505f2228", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-15LXPdCFHqiJe24HR/zgAu4GabTnfWbYV/6ocqUmApAl8MFNiBrUKJmzcM5VlL6RsXYG7PIHUnSY+QMZkz1ZbQ==", "signatures": [{"sig": "MEYCIQCXha/CLbnSNovvae4OjtGnNkDMh3hiqWoW0bQBHvPVigIhAOqi3zdgXkNjYkmJuFF1XMzwO2SLR9hRlHQafAwlHPtg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76514}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/runtime": "^8.0.0-alpha.11", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.11_1717751736390_0.22446517869113358", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.24.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "383dab37fb073f5bfe6e60c654caac309f92ba1c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.8.tgz", "fileCount": 7, "integrity": "sha512-adNTUpDCVnmAE58VEqKlAA6ZBlNkMnWD0ZcW76lyNFN3MJniyGFZfNwERVk8Ap56MCnXztmDr19T4mPTztcuaw==", "signatures": [{"sig": "MEYCIQDFKesDH+2bZIL5S7YSlZjjBlXGoMQGsyWduuRINGd7RwIhAKL0YgbuyFetW+lfvYHWAGXqqwp6DeFBHW/3ICnp/UY3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72713}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.8", "@babel/runtime": "^7.24.8", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.24.8_1720709689929_0.8790720916849195", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "0eaf7a1170a94f6132553266f57ff1061686ad9b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-JsGC4eq730+N99/UemIdtYsxy0r5qaq2AdOwd5JhUcfyMpL79V4YjzO2WcB9liawX6MNAv1SlMoMb04N5ePwnA==", "signatures": [{"sig": "MEUCIQDm9YuxjN3efyeHr0IVaCUrT0lljL9bVk/XrY8rnn1lXwIgCtOZNIKeywJl01JnJMaNA0pmqzptRkW79pGY/EEtpCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73298}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/runtime": "^8.0.0-alpha.12", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.12_1722015213431_0.2595214482546975", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "debb1287182efd20488f126be343328c679b66eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-OmWmQtTHnO8RSUbL0NTdtpbZHeNTnm68Gj5pA4Y2blFNh+V4iZR68V1qL9cI37J21ZN7AaCnkfdHtLExQPf2uA==", "signatures": [{"sig": "MEUCIQDGvNOuIZ2wXhzIUlP8s185xtlrohf/7DvHHM0G9lTTjgIgWxY537OePa6ScHfZXoWIJg7Lfkr6PvW8SxL04eMZAaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80882}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/runtime": "^7.25.7", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.25.7_1727882094446_0.9122955113665892", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "224ba48a92869ddbf81f9b4a5f1204bbf5a2bc4b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-v61XqUMiueJROUv66BVIOi0Fv/CUuZuZMl5NkRoCVxLAnMexZ0A3kMe7vvZ0nulxMuMp0Mk6S5hNh48yki08ZA==", "signatures": [{"sig": "MEQCIASDsE6mqZMydf/48zx0ZTAtULHBGvJpV0yRqdoWEqc6AiANgsxGZ1tcw9sUzF/QIiMGavcnA3BQUyBm12RTbdx3lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10425}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/runtime": "^7.25.9", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.25.9_1729610473285_0.08045288734939371", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "34adb0c14ce0d23d5b66aa266838efb4d1584756", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-lfVgTOERiyTdTitAhQAkEEXmz7dvVHPns+aGXcmODumbiWsezcQuPDUn0MHYtgZPZBJyamzZ/dVjkBJLBkdmNw==", "signatures": [{"sig": "MEUCIQCzeR5ud9CgiLQidKhHg0pLN9SSnDRYkIL/ovWRvarGVQIgQnN9NLY5mV4veOowvrBGQ0BeydQdRVfSjSwbCBd6x6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/runtime": "^8.0.0-alpha.13", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.13_1729864455623_0.4415404113608967", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "a75ebca21f30950dfae0ddcdaa56cf6cc71bea77", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-DaWnlwmDHlzyuLQszJrwpiOq/i3SYVskYPURkkMvbMJkQLk05W9bvUWvQ5f0tp0L9cEveB5tYpKtYRrLtG8PyA==", "signatures": [{"sig": "MEMCH2ZPm0DlGfPSaf3a7+Rw1D0yM09r7cQgc6PIK7Hsub0CIArN8/j9Ah239w7lUhe99hc96yHhRAqChyzqrvIvikYi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/runtime": "^8.0.0-alpha.14", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.14_1733504047528_0.2549794875968818", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "fa6f6b29ca6d1f2fa297da6bf2524e27e857d697", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-KEsBpeiGRSoFjs9fQu4CbyYI/PWCaFYmbBoVYWsKt9oQ8dypbAF/Wojmc0+i7da7rBHHOxK6Nta1vSz324297A==", "signatures": [{"sig": "MEUCID0TpC3T9svHLOx+TFSqJ3ouFKoDsL0re/DpL2j5Kv09AiEA3mIOdzqjmLoom6cYlDB2Bd6kaw/re4M0+lwbVr0+kQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/runtime": "^8.0.0-alpha.15", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.15_1736529872499_0.13655308141037192", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.7": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.26.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.26.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "d0e33acd9223744c1e857dbd6fa17bd0a3786937", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.26.7.tgz", "fileCount": 5, "integrity": "sha512-jfoTXXZTgGg36BmhqT3cAYK5qkmqvJpvNrPhaK/52Vgjhw4Rq29s9UqpWWV0D6yuRmgiFH/BUVlkl96zJWqnaw==", "signatures": [{"sig": "MEUCICLUYKyWFxW9aBGZCKCYogvk5ccjsloZisI3r7ZzLbJ6AiEAu8nEuwX6TymLe9lI7h4jUTaxW1wTDLUZPJdtZdCNIXQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10434}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.7", "@babel/runtime": "^7.26.7", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.26.7", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.26.7_1737731093317_0.12075833287658155", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "f3ca7c657294fbd87dc96f8e5224c5dc26e0e66c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-iQGBFUNNmcrGGMuiYoBpCzLZOjfJcMyiIIowrCNIa5ZmtyUPVplsdUWD9d+QtGaywmiqUEhS+QpofBSzqCCEcA==", "signatures": [{"sig": "MEUCIQDbDYtSXf4/5VzdHyTlv/WvQV7k4TFmp7baRF2jnSp2cAIgGkzQfAjIkeeW/MEDdXscw7FBRog8VrlHr7w7NK5NlJU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10719}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/runtime": "^8.0.0-alpha.16", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.16_1739534349030_0.9614150391940512", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "01b3ec8ee5d646bca88349bc6fa76901923495ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-1Tymgb7bh/jr4h2yQYv53bUNQzSPSscb+EIYsWLfDi703jgHfhBc38SqH3dSYlnmsG2ITWTwOgQTiPjeJb2svw==", "signatures": [{"sig": "MEUCIQDybSEAJR8sz4/PXfrk4HNu585BkmMWOTWWkIYTY3UHUwIgCnvb0N82CA4MaIKyDeqY9WwB28M7ZFSWJbzLQS6MZnY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10719}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/runtime": "^8.0.0-alpha.17", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-alpha.17_1741717503002_0.956123419766173", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.27.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "044a0890f3ca694207c7826d0c7a65e5ac008aae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.0.tgz", "fileCount": 5, "integrity": "sha512-+LLkxA9rKJpNoGsbLnAgOCdESl73vwYn+V6b+5wHbrE7OGKVDPHIQvbFSzqE6rwqaCw2RE+zdJrlLkcf8YOA0w==", "signatures": [{"sig": "MEQCIAvMDOPb1Z++3YvLH0fWnfu7rEaKx6Raelki+yhTaCDvAiAIFQruKMfatEBMyFqXMC7JnHiX+U5B8PmXHDBywTJrHA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10520}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.10", "@babel/runtime": "^7.27.0", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.27.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.27.0_1742838101824_0.12914982490928684", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "70e966bb492e03509cf37eafa6dcc3051f844369", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-RiSILC+nRJM7FY5srIyc4/fGIwUhyDuuBSdWn4y6yT6gm652DpCHZjIipgn6B7MQ1ITOUnAKWixEUjQRIBIcLw==", "signatures": [{"sig": "MEUCIQDXdWpYY+y031kF1WDMXYz5oBiJ0oPRhcFi+YFVHE2A9gIgawiErvvcw4FOg0lIjrpEF8TdxUa2mTb3LwTxA1r0+jg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10519}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/runtime": "^7.27.1", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_7.27.1_1746025738651_0.3471474897672857", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "dist": {"shasum": "dcc4cd6a5005b6d2b1bd35abc26dda52762591ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-WyJ4Kg/1yJhTtoKeFN6H+hchgIjzOGK3UTUePaZsyzWW3flBEbmejJZlI8rb8LxpcGWesU2ayHPNkgy6saZskg==", "signatures": [{"sig": "MEUCIQDSo2M+viMmw7PM1q3afYyJ0+Gz7hpqANsZUdCL+aMrCQIgIpiEILgcUEdfD/9SyzKpQOoPJxghIZXWkPdOHsz1yiM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10071}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/runtime": "^8.0.0-beta.0", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-beta.0_1748620270442_0.29397336249783956", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-typeof-symbol", "version": "8.0.0-beta.1", "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/runtime": "^8.0.0-beta.1", "@babel/runtime-corejs2": "^7.24.0", "@babel/runtime-corejs3": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-typeof-symbol@8.0.0-beta.1", "dist": {"shasum": "be91eaee2447cf2435ceeebb867f1a0cc0483c19", "integrity": "sha512-MhZ1t/L3csV9PA2f3w0TpQaB5g8218RQlXlsAcdM/NJoAZ6SlW7skAo47VsktThS5Azt/MABwwwhn43WaO8Thw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 10071, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIE486vv0z3J6uyPVHWe1u0Y7/DhJ5CwuaeIt4+nv8HA3AiB1/31Ki4hB0Dkui0IHSltHYyUxWZPHsmhRY6JeV23Alw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-typeof-symbol_8.0.0-beta.1_1751447061787_0.47400970248183705"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:51.548Z", "modified": "2025-07-02T09:04:22.242Z", "7.0.0-beta.4": "2017-10-30T18:34:51.548Z", "7.0.0-beta.5": "2017-10-30T20:56:31.161Z", "7.0.0-beta.31": "2017-11-03T20:03:32.503Z", "7.0.0-beta.32": "2017-11-12T13:33:24.386Z", "7.0.0-beta.33": "2017-12-01T14:28:27.906Z", "7.0.0-beta.34": "2017-12-02T14:39:29.309Z", "7.0.0-beta.35": "2017-12-14T21:47:53.984Z", "7.0.0-beta.36": "2017-12-25T19:04:47.407Z", "7.0.0-beta.37": "2018-01-08T16:02:36.225Z", "7.0.0-beta.38": "2018-01-17T16:32:02.705Z", "7.0.0-beta.39": "2018-01-30T20:27:38.291Z", "7.0.0-beta.40": "2018-02-12T16:41:44.228Z", "7.0.0-beta.41": "2018-03-14T16:26:19.520Z", "7.0.0-beta.42": "2018-03-15T20:50:51.858Z", "7.0.0-beta.43": "2018-04-02T16:48:31.141Z", "7.0.0-beta.44": "2018-04-02T22:20:13.275Z", "7.0.0-beta.45": "2018-04-23T01:57:11.038Z", "7.0.0-beta.46": "2018-04-23T04:31:34.245Z", "7.0.0-beta.47": "2018-05-15T00:09:24.548Z", "7.0.0-beta.48": "2018-05-24T19:23:04.650Z", "7.0.0-beta.49": "2018-05-25T16:02:36.206Z", "7.0.0-beta.50": "2018-06-12T19:47:24.818Z", "7.0.0-beta.51": "2018-06-12T21:19:58.946Z", "7.0.0-beta.52": "2018-07-06T00:59:29.809Z", "7.0.0-beta.53": "2018-07-11T13:40:20.032Z", "7.0.0-beta.54": "2018-07-16T18:00:10.894Z", "7.0.0-beta.55": "2018-07-28T22:07:25.719Z", "7.0.0-beta.56": "2018-08-04T01:06:17.227Z", "7.0.0-rc.0": "2018-08-09T15:58:40.685Z", "7.0.0-rc.1": "2018-08-09T20:08:20.726Z", "7.0.0-rc.2": "2018-08-21T19:24:22.206Z", "7.0.0-rc.3": "2018-08-24T18:08:20.500Z", "7.0.0-rc.4": "2018-08-27T16:44:35.706Z", "7.0.0": "2018-08-27T21:43:31.668Z", "7.2.0": "2018-12-03T19:01:56.429Z", "7.7.4": "2019-11-22T23:32:19.822Z", "7.8.0": "2020-01-12T00:16:42.785Z", "7.8.3": "2020-01-13T21:41:43.967Z", "7.8.4": "2020-01-30T12:37:07.358Z", "7.10.1": "2020-05-27T22:07:30.569Z", "7.10.4": "2020-06-30T13:12:14.226Z", "7.12.1": "2020-10-15T22:40:33.705Z", "7.12.10": "2020-12-09T22:47:55.931Z", "7.12.13": "2021-02-03T01:11:13.493Z", "7.14.5": "2021-06-09T23:12:17.074Z", "7.16.0": "2021-10-29T23:47:35.569Z", "7.16.5": "2021-12-13T22:28:34.101Z", "7.16.7": "2021-12-31T00:22:12.972Z", "7.17.12": "2022-05-16T19:33:08.561Z", "7.18.6": "2022-06-27T19:50:05.557Z", "7.18.9": "2022-07-18T09:17:33.965Z", "7.21.4-esm": "2023-04-04T14:09:31.113Z", "7.21.4-esm.1": "2023-04-04T14:21:24.138Z", "7.21.4-esm.2": "2023-04-04T14:39:25.368Z", "7.21.4-esm.3": "2023-04-04T14:56:13.389Z", "7.21.4-esm.4": "2023-04-04T15:13:25.809Z", "7.22.5": "2023-06-08T18:21:23.258Z", "8.0.0-alpha.0": "2023-07-20T13:59:59.769Z", "8.0.0-alpha.1": "2023-07-24T17:52:03.835Z", "8.0.0-alpha.2": "2023-08-09T15:14:59.093Z", "8.0.0-alpha.3": "2023-09-26T14:57:02.387Z", "8.0.0-alpha.4": "2023-10-12T02:06:22.104Z", "7.23.3": "2023-11-09T07:04:02.732Z", "8.0.0-alpha.5": "2023-12-11T15:18:59.626Z", "8.0.0-alpha.6": "2024-01-26T16:14:09.964Z", "8.0.0-alpha.7": "2024-02-28T14:04:59.714Z", "7.24.1": "2024-03-19T09:49:06.994Z", "8.0.0-alpha.8": "2024-04-04T13:19:53.986Z", "7.24.5": "2024-04-29T18:34:20.309Z", "7.24.6": "2024-05-24T12:24:36.267Z", "8.0.0-alpha.9": "2024-06-03T14:04:21.822Z", "8.0.0-alpha.10": "2024-06-04T11:20:12.085Z", "7.24.7": "2024-06-05T13:15:25.923Z", "8.0.0-alpha.11": "2024-06-07T09:15:36.575Z", "7.24.8": "2024-07-11T14:54:50.137Z", "8.0.0-alpha.12": "2024-07-26T17:33:33.658Z", "7.25.7": "2024-10-02T15:14:54.692Z", "7.25.9": "2024-10-22T15:21:13.448Z", "8.0.0-alpha.13": "2024-10-25T13:54:15.855Z", "8.0.0-alpha.14": "2024-12-06T16:54:07.722Z", "8.0.0-alpha.15": "2025-01-10T17:24:32.717Z", "7.26.7": "2025-01-24T15:04:53.463Z", "8.0.0-alpha.16": "2025-02-14T11:59:09.227Z", "8.0.0-alpha.17": "2025-03-11T18:25:03.198Z", "7.27.0": "2025-03-24T17:41:41.999Z", "7.27.1": "2025-04-30T15:08:58.820Z", "8.0.0-beta.0": "2025-05-30T15:51:10.596Z", "8.0.0-beta.1": "2025-07-02T09:04:21.965Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}