{"_id": "@babel/helper-create-class-features-plugin", "_rev": "130-9ad44a492e4de815f6e14e8dde9c0be2", "name": "@babel/helper-create-class-features-plugin", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.2.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.2.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.2.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "8d52930558897042444e670f26ae6581ec9a4a4a", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.2.0.tgz", "fileCount": 10, "integrity": "sha512-INigeagreiKKZ4Ouo8Wun2y+XT4KxAdNztesU3C/AynDsdtIu00qjgw6QXSM3hVzQ7FvAL4sFmoEpFPuXkeHiw==", "signatures": [{"sig": "MEYCIQCncF+UyfblAxYi2L0epyL2QeebR82NuNjuSrllzajmFQIhAIIglugE9uSE5R8Hm+RkOuNWqIBWPz8LuzwH9vdcYLix", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1VCRA9TVsSAnZWagAAQjQP/0KnKV8I+kBPXS/5CdMA\nGomzlW6mCyB0iOBTdMT4cpVoueA0oIkxteW2ZA/L2BLz5rFU6L/VaYmZ37No\n0k+HH0LigfZGGO1/T37OIvPl7369pvoU+gE8RxbSHhV/zsGygmEjQkaSsdIu\ni+AlUKd9IZi6zoOL/v3mjvdtzkkfDTgLpWwcR3+T8jS4GlQLm2dbd8W2ok/N\nCU3yI6h7+eKb2PPk+EPDADCUasVyBukt3BPppOub3HOjCkbXbI5rFvwDngW8\nkBQaZSjtuRc0KzSPhIEEQKgRZS/gYB7S5/t80PEj6JCpSMNXTDfxpUBiEeJx\nn11p4tx2CHjlNb4urdkVKUIklaYg1fDhVXP1yUo4veVWY8a1bY7Ix+4BFrN9\nWNfectARX8cEQmupyAEjxDSis2LzK8b4j8W+26U+OVS+JRLp+RwxLlnlyllp\nk8/QWn57rw6fdzJ1VOI3CfihY3gYBgVKVB5L+yo7vJe2dEPBm58WsC8ppje2\nZBeFwQuHHbWwdmlM2xrDJlk8yKYVLr80w1bWZ+sDRnGfDF8NhKeH2SnZ9B+7\nlc5sS5gX6m7L90Y7NCfBFTKCyDdHmcPWJY3WLNEdtJf/JMyYIHYs5aXZ0WQE\nArWAentMMh3b7+Dc+35MgSOnmt5QcWV3xZ7H9j9pphnHUxhLNHtWohOCm7QL\nsbrh\r\n=rIEz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-class-features", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.2.0_1543863637333_0.9666524546148407", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.2.1", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.2.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f6e8027291669ef64433220dc8327531233f1161", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.2.1.tgz", "fileCount": 10, "integrity": "sha512-EsEP7XLFmcJHjcuFYBxYD1FkP0irC8C9fsrt2tX/jrAi/eTnFI6DOPgVFb+WREeg1GboF+Ib+nCHbGBodyAXSg==", "signatures": [{"sig": "MEUCIQCcQHsOhmQfUKpleHQ9ibJ//z4LhmE219y4DDtqH/y08AIgEd9KEUvjAiSIgYwxDb9dweX3Vj2j2hiDCHSzIVF5858=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBpuNCRA9TVsSAnZWagAAMSsP/3YVE1E1hZkXr2oaQB8C\nllcajLjjl8dmCxnvtDdrtrU2PXGcnXMEpPlDwoF2pmLHhADqMD7F7+qsdCg6\nrDnSkRuiemhkU7vcA7yu6I0a3tIMcEE5t7EGPMOA8hwiiauNFDZAho9DbkA3\nmjf2TsLdklhAJaIjwSI0N+7VmTabAP8UdWvtdhPTCW5RGlk+EOpZzCkC1pIG\n6f0vW9XF38Ai/VrOX3oCLnLhccRTqNoAUcJaoMsatyITZTcd58dAippPEQTT\nAuWGB+ny7r0OgJNCuYJV75B3proBl9l1txlUJ6FnXSp4fJ2XbRdUp0ZBrQsx\nNz9RNizXX0CPMu+t9ax2Y9r1lLc+4rEPbZvPW1oSvmWqNGBy1TvUtzRoUHXy\nWASb9gO55/t1erogaUJeZF6lI18DvAfPU6/vFSJMOSbtjqkJ/rHpY0n2dQtI\nqOUyXEeUBAR67tmVXCsRUlZ/DFjEa+f+Mbv2lxf/oUGuRAIbDmZ6QcCEcO0Y\n3UbuAMs0JjEI3Bh60TIBz/O0LFuSgIzjc+/3xw2kLX4jZXBMwUb2/fEZ5qe6\nv0qxl9nc1JWXvmMzanMq017FNhC5idJS9gBWutljRuVd97ivuYx1cjbH6PhS\nTIi5x1M+Vq7BPQHkARDABSmN/8abg71edCwSwG6GiYhr3Pd1/eieheZ8JTOp\nZrwD\r\n=FJE8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-class-features", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.2.1_1543936908400_0.6639463831267518", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/helper-create-class-features-plugin", "version": "7.2.2", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aac79552e41c94716a804d371e943f20171df0c5", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.2.2.tgz", "fileCount": 10, "integrity": "sha512-Q4qZE5wS3NWpOS6UV9yhIS/NmSyf2keF0E0IwDvx8WxTheVopVIY6BSQ/0vz72OTTruz0cOA2yIUh6Kdg3qprA==", "signatures": [{"sig": "MEUCIDE2KirgnWsGhCSzAblWzIuDwdFAcHOGgcFia5EXQ1HVAiEA+4MWbfs05ojHSuGAhzmG5gCHxk9YnTDBQHaGzS+HdIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHcCRA9TVsSAnZWagAA/ggP/RmKl8AJnyCafPGgHg6X\nSbjagWY/W6205ZZGiW2KjIemFfn6kvzvA3aAxy6RQOJt0JQZ61WmLnEWHilW\no8n9kh4Q92bzCwPlj9JujEpBDM/VM7rMWx/TRL9vbntT2j8XrGhylKE8sCOZ\nzba8WjgorOL4724jpB498cVEkfGEQ1uk/B+te0EGRhk02IpqSOgDnT8yF2JH\num89pOE+P6VKWtJ/JLY0vyAVbYYNd0MGEthtmKcGLBtBnZbzhcn7/1/A4L68\nNKSpK18+WR/2gKdGAYxwffUG9K5tykviWdGzyY79ZiedQp0mB1Ll/vzhx/xU\nghgg9H+SNM1mni+SxcXSKyOkT5eYl+7S/dZWyNt0QV4lXRZNANdCLyjjrh5x\nP4VchLxCCKudw7w+s751SHxGKzRSty+B6Pj9VpWJzaT91tYnSc4tnMr9V/tn\nNvyIY0HidP8HKtwfPoAGSOTCONMLszy5mRwEHk8jeB7XnjwWETSqBUwesI3v\naQInIoM0uVkCN8BvQM8XlrUwN7EdN/pM+eStpwJoodNRYYLQvcu6Frl+1EWX\n0PlA6nKXAB7WUj6e2yt6RFdkqWSOznZZacn9C7SjpYXSlp7ozXPhGyCUwXGy\nN4thyJ1zwuwjDnNhCBJX0qMGOCGaCl5MxZ7SmDx7gBxrRN4pijLCF3V2dnXI\nAfuI\r\n=P1u4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.2.2_1544868315645_0.8300365384325792", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "@babel/helper-create-class-features-plugin", "version": "7.2.3", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.2.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f6e719abb90cb7f4a69591e35fd5eb89047c4a7c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.2.3.tgz", "fileCount": 8, "integrity": "sha512-xO/3Gn+2C7/eOUeb0VRnSP1+yvWHNxlpAot1eMhtoKDCN7POsyQP5excuT5UsV5daHxMWBeIIOeI5cmB8vMRgQ==", "signatures": [{"sig": "MEUCIQDbttZPJ5ecCEH3Ehmptk3TqzUHyvgH/19x5r1/4no+ngIgDF22v95DcK8+AFbJJfyRXPos6SNS0VW+SWWxAyIdTWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3lTCRA9TVsSAnZWagAAnFQP/jm+PUpa4tR7Ti8NNaq+\ndxyi09XdLeoacAss5vfDqCaSeGzPDq/mc+DRiwZMepAoWwG9VCLsKZqbZ+9m\nnlyVf08qbW/Fph6sD34mG5bRZTHjYPbuI5/uTwWa1ThpHNYQurSUYm9rIvsN\nEwqAQk/IjGr6kUU1lcxu9obGdou7h8vv1+jYYwpkeY1D2dmYTJoVHZoDHc2S\nTlN3FjNSeheixrj9REQ8zgOtDhzXwwJ6TLexpXviKdFGai2vFZ4gs2HK2H5J\nQeTJBXXa18rg9oKZbzvK7JRWHtiJ2nKo60o+DVsQbv+bqDkciMAbPrL8T7z3\nUBc5KOunoHrx5beWvMJaATOeZsFVpg2KqrPC43iSHZ9rPR82izZruod9hYIS\n4xgtYu0h3GZECgZ4FGvugrzW1sihlEifoV+Nx4ZBbv2a6MIdj3BaZAsAYwdp\nU/LHMeWoYymCpyzHbogY1b+bE5yNP9wJNn8wx/e/qJv5c9Iuu5wvIFhyjRBz\nwAyP/VXVVntTcJj9c5/jJHaJQgujL2+xC9xGfC4F/DPvAsQAk+jCwV3BVmLq\nreM6Nz6tVVgqMvEZ0D1WEjiHXabG+WrLd29bPwZ7Lt3QANnKAlQwnC5WJ8s+\nl5Axf3WZpz0CA3XlQDtOwYQ4oY+6b22Nx9sQHyDkAuDG+01lXIgexz0fIkL1\nQqur\r\n=2Xrt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d35f2ad92b322c3bb9c6792e2683807afae0b105", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.2.3", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.2.3_1545304402957_0.45138816491420175", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.3.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.3.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "2b01a81b3adc2b1287f9ee193688ef8dc71e718f", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.3.0.tgz", "fileCount": 8, "integrity": "sha512-DUsQNS2CGLZZ7I3W3fvh0YpPDd6BuWJlDl+qmZZpABZHza2ErE3LxtEzLJFHFC1ZwtlAXvHhbFYbtM5o5B0WBw==", "signatures": [{"sig": "MEYCIQD62u2cjCRsUBfP980+7PilxBPUyxQCTydKd5BO6OoyCAIhALgQzzuW0z+V9ZbjlcqFXnjatjBQqol2LELKi8MsgWer", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjn3CRA9TVsSAnZWagAARVMP/2A9St6jTansQf4n+dlF\n1hFuIbGGJe3NtPQP6SJ82y8gDMbCGzlhQ1CginuEUiF7SNnyDMbI6Z5/AlGX\nSt5vQpdxyUts37KvD9kGah5ja+9G4pKclRfJ2hhvceN7mMSzPxERitmN+KJR\nKAATOqoJVWif5t+y7XQ4gGRYagId8pdrxKcyPIW7pV1fXAMf7esAb0sVltmL\nhbQ3xDC7z1ULeWhFHcbKLubmShCRbodmdLsxIrYmQ+jz3AdsVhP+IbMnXu1b\nqa08vGJVzC3H8bwZ6L6ElbphbYtJ7B73WJznVtlxOOUhlOBlgE8GBSJyY8h7\nsOM8GB+lvhRTGKlpgRcfpXPTgtO+3sEsQD70a1UcAxAukYVhDTXK7TojZ4uL\nK4oZsySiham8mVd7gR3hTMSHlnHSext0lFBq4isT3hBw1gqrNXpOPOvZb8t/\nNnP6E98lb+2AsELkQ7GTJPNz81oJDsJVvhfYSDz8GpVKDI73AXaqH9JB/yeg\n5ITeBdPj/0TfyDT7sLX9IdLtixfTIzK24QiYMNCfS10gNsWr8nb04RuyqFfP\n45hZ412erfzO6VEVHfBAlV0epl/zDb+eRjOgo2uYXdk37LgaHSXARdrjCbWi\nAEHjsiopHyl001VQEamXYDnpAhaneLm7WtNkWIRxhthfP5ioyNHnkGZ9hcAD\n8fcF\r\n=k+J8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f6ee26c3da4c903818fd61fd9e5e8e1970185f77", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.2.3", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.3.0_1548106230915_0.5107073718439354", "host": "s3://npm-registry-packages"}}, "7.3.2": {"name": "@babel/helper-create-class-features-plugin", "version": "7.3.2", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.3.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ba1685603eb1c9f2f51c9106d5180135c163fe73", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.3.2.tgz", "fileCount": 8, "integrity": "sha512-tdW8+V8ceh2US4GsYdNVNoohq5uVwOf9k6krjwW4E1lINcHgttnWcNqgdoessn12dAy8QkbezlbQh2nXISNY+A==", "signatures": [{"sig": "MEUCIDtsxcbRmyglI7bOZP2SCMcQ1m0Qi8AaYH8y/dgCVzNbAiEA6Vgiq+RGGw4GkxlraQFLsny9DlllL46rzuxWnSyaVCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWLtCCRA9TVsSAnZWagAAkq0QAI77RiI42VoktkbezmW0\nc5Dmyqd3iaJxKaDn+G6NYF3Wok4KJqlNSN0PValG5Mw+jwlTmSDxinE8kBt6\nbjWMDNNhDuJZG2Jtf2L9feclgwDIzl+LZIbZLsAzAcOBGJrLkOZko3dtNwTa\n1oFg1HIT6UUUE5Y2/xM18PN2LeYWbBfDHaGncK3NaCnVfkc7CNqSGN+Kzphj\nRpjhTUZ2r5jIL+YAXkRkP3Mf20/943wEqqNuoakHwzxfA0lRsmmXtlNBvbqb\nW/xgFaZCxQmkwRqOeGlSYKsrCADrypRbT7fwJgEzqMwgb/bw/UvlewLZZh64\n28By1kp4mPJQ/SopOryrSgweT2gjeT97EXfaRNp/MdizgY/9xhWoII9myxpS\nuS95pJQBq1f2ubE+U+81rC4Vh0DN7a47OIgSmQEycCmPTskHrrBQXduzNl2N\nlAqAg8Z6qrkrATbUd6popvKkVE0qbah54ohJoWib13zoFiu/9IofchKIPqta\nZLbKw/cFrT53fhEqTs172QBN4Lav+ofXNKk8yeku5dC/d4YIqUWUq7K87X8l\nbh56slrI2eKMnKBQPmvFBGPnpgRvzXW8GPVf8M4oQsddjdEViFxumNWyKetG\niV4r3skmHJt1kO1NgnCbpJUG2aYcz0sTRHDtZ1w6D+1tpjOldyFJyR8F6NVS\nI2Di\r\n=HJr3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d896ce2b53f64742feeea27dd33ee45934cd041a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.2.3", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.3.2_1549318978353_0.17493788993920845", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.3.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "092711a7a3ad8ea34de3e541644c2ce6af1f6f0c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.3.4.tgz", "fileCount": 8, "integrity": "sha512-uFpzw6L2omjibjxa8VGZsJUPL5wJH0zzGKpoz0ccBkzIa6C8kWNUbiBmQ0rgOKWlHJ6qzmfa6lTiGchiV8SC+g==", "signatures": [{"sig": "MEUCIQDqx/R8zZoJgMdHOqywVw4Rp9ZOzMqPymTLgyBldbxHzQIgbKDy2DHxAj5OGgLlYSwP2FEy5CvtUxaNgQZLsbxdCCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDWNCRA9TVsSAnZWagAAx9YP/itZ5Pqc7FCRjC3hYj6s\nFLtQbE//pxxkFHniUyt8vUqrJ150CypdQVCJ6zIj3bquDWxHajJi5nfiN5uC\nX+lpHEQlqczM9ZxMRE43UGi2zimQQBIffxERYkuDi5leiu5exJoId6ePTsor\nHeOBIxdO5RoOFb0aNzZ3OKFIYrUMcFLMfoK6dQb4Qnfz26H2ztobTdoC2Px/\nWqX/HzRi6X3PD7LTDB3KYgEaA0aVRemWVjI1pAMhvOSFo5c/0iwt/h7Bn1V6\nlabzxXE+H7Wqsqqx6enEgy4FvsJaJOQhAnUyNnuKnebKUcRlWmvpxEZ+/m0b\njE+otaXdAlVm5jxoUqWdtoeXLAjfxQDAT+4RzWuOJV1rzItBN8qD+FVls8BV\nj4WeQDYWxv0QT5f8SEIUj2GqFvry7cDZA8cY6DlZvvpbomZnNTn+DTujf+51\nDE/g0L+8HF/Kx/rk08V7P9tXNKnLMigZEUbatkA1BTqUALhHf10D46XJXgsv\nQrEsGkq5pxngU3/HkjQWPyfdqq42Qs8ZygLjMu/4A2DLyiMFoQremDwN9YFY\nC6flaM7xHgguyAibicKpn2hR2Cqn6XU+fe0BV2lngj2tg9FkUqCLGHDqREEV\nZpXEwf2khLGH4rOTgTKLJnhCTJgy3tnJfyI4i1VGaMFLxOhY+pG5bE0Ocp6D\n88yP\r\n=Km8N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.3.4", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.3.4_1551119756869_0.9474764698776765", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.4.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "30fd090e059d021995c1762a5b76798fa0b51d82", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.4.0.tgz", "fileCount": 8, "integrity": "sha512-2K8NohdOT7P6Vyp23QH4w2IleP8yG3UJsbRKwA4YP6H8fErcLkFuuEEqbF2/BYBKSNci/FWJiqm6R3VhM/QHgw==", "signatures": [{"sig": "MEUCIQDBRiW6ZrD3DJcRf5+/n7dkgwkBeCPytjlhWV+c9BaCRgIgdJAqYsnHgY9Q2K2wf1+vxsXy84OfUI9IhTRVUlVCU5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTjCRA9TVsSAnZWagAA3cQQAJaE3hZTAORKELiG6J5d\nOe0fEXZ8aH0HoeFNwWYr0qw3jS33WLYzcY2JRWruWLxvWr+exrDFVj0tfU1Z\nIZJEIltaqCpVB4OIYPA6VTfWSAX/cuyEn5oYN92aATNKeRH97Q2NhYG49O0x\nskimadZK4XNfGO1wz9/2BHJpXR1tlnLyU0djHGCDEqCziCXDsoS3aRz4vok5\n/kaxmxl09PPfL6GTcwA/bl6MaUL4VAQxqXK+4iGb1MQqP0P/xWbV89PWzft3\nzynSt7fqwgxLpLt37bIKuTv6RPAnyQn486VE6MRQPNoYfHJiURdp/hedeLaF\naV9PF4Yh7sWi+a4Yfn26aNK05sinS6D9+dEru0/KLFNXR2snZySzu2gX+Akm\nY2FG3Qhm0lXn1KoyboeKHsP4dmwx5gx2YWWPOqiI7+YagDsNkiOEgTLm4zX/\nw7ipj17v7ozWyyiPlZQtosgoi4xkZhS6tEGyacdDGAgXZrlhbU7Q65c5v1c9\nrWLFFEGZe7kg7fzquP8Rtr+dKvE9O4nM6iUfzjG4RJBF/tN+nKUYzzB+z7J7\nQObAW/RHtWHCctYHrDFkBS0eTdwkCtg+ySHfqqZxJEv/hy9He3KN/Egv9kPF\n+bHX+eLhkG85yCW1UX9GB4AqWWBumkLNgo87y5J+4otM+rKKAGwJ+RqVfXor\nnvmS\r\n=wX7X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.4.0_1553028322779_0.165706835018689", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/helper-create-class-features-plugin", "version": "7.4.3", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5bbd279c6c3ac6a60266b89bbfe7f8021080a1ef", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.4.3.tgz", "fileCount": 8, "integrity": "sha512-UMl3TSpX11PuODYdWGrUeW6zFkdYhDn7wRLrOuNVM6f9L+S9CzmDXYyrp3MTHcwWjnzur1f/Op8A7iYZWya2Yg==", "signatures": [{"sig": "MEYCIQDszjym+wZ30HDpvwdGBjliaPI+03te7mKcfQ/+W5Xm1gIhAPhw5yoc4b0rXY77FQMDhjqxNXPTe3hjHNbo5Fx0KQjO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco743CRA9TVsSAnZWagAApiMP/RwQawVxbDf+FMuCfbU4\nEl4BC1wSfyLx63hfkDzRTVsxF0Hf2FS9hW7pQO8zww5HkW31WhTcrJWHUxpr\nNQOXKch6JUTp9EXFaUo0qEs0kiVDdAxRiTSYGDJY6ls4Tvzrc+d+7CaS53AL\n/1mm5nxB+X7zyYlbExGS38nmwa0aM5JiRmWRpCsoiKKl8cwXpK0zgepNvZCy\nLqmvqQUhECXy/fGr5HaBhlEZ6olvd/gx/GHwt45kyiWqn6OFbFXF7kUvKC70\n46szXDjVuGAjAugkhErXN6wmX+E438hQD9HzfJpEOy7273ae+zj5eyy+u7zG\n8+lS6PR96tKgRgM6gztpjAwpT39cS1e0yGDpFgcbWTUW3TuDAYyKE5scoA1y\n1AgmltO68pfA2IRYQU2CL8s9Da9wkBvDjnDBskrQPdpwX/onZQbOBoWAl8v3\nUzfYN95zGk3dtDrGSVJltJt5iq+VNR5AozslwfqwqsH7B/L5PcIQQxh+xq3H\nYq88lYF3xUfojwy7eyvOZ79tInZDQfY3JQXRjstN6rnX/DbSRbyvaWkYbM6Q\nOv9zHXPhWxdLRFwaJzVmJbKzEwZH4nEkuPVutcpPb7q1dEMPQgTHKQuUFa1+\nfaf+3hIikMTL2wC9pUVHskg96gKsRgMpY771KPsMfzaBXuok+0oMcpas60oz\nSrjF\r\n=FfiH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.0", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.4.3_1554234933960_0.6474646147164078", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.4.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "fc3d690af6554cc9efc607364a82d48f58736dba", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.4.4.tgz", "fileCount": 8, "integrity": "sha512-UbBHIa2qeAGgyiNR9RszVF7bUHEdgS4JAUNT8SiqrAN6YJVxlOxeLr5pBzb5kan302dejJ9nla4RyKcR1XT6XA==", "signatures": [{"sig": "MEQCIDXoR1nMDPRWjz03FC8iZwc53OX1cKWsxEtPBOaVpSLyAiBUZlflt74+swYLVn4UmOooQD1ecJa16yk6FzV2Flzi5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3J6CRA9TVsSAnZWagAAP2YP/i0a6tVn5KZ9o7R8PDrd\n172I6MUaBQSR0LGhBu6JQdgB3OcrPKrTTeRw4Z1IcKuGzcmThnPNsqLw9QaN\n3RsOJo0j8AAsXOP7NtS2laEppma4uXohqgGv5y0qMkWt/00gXTi/VA+jGNYc\n6GWKG6/Y6FO/iFeX7g+1kQDx9j5DJikTtDMckYfuLUjeV3n7pTBtrgffGjrD\n2hOxKvBlRNAivQxdoKWj3Q7e22OCOeKamyXq3ycdLeHmy90bbHTyAmgqZhN5\nnVGPVC/FtIibYGdOA/a6oenLAmxVWWB0AfYYhST7O+r2/hWzbaB6r/canHzH\nzEMIeCfgZlfoOBQgOiIxhGVZlqbHV1cTrzbqbeki7r49RMv0o5QR7uxG7Y1Q\nq9v4Qq33/jDVQ49jkMZQA74N98blZsQazu1j5vHVoCrmEBZL5sM52UKFK+QM\npjEoZNNuQOpxQYt3H0wq25IUxCTyvRNcW1xr49J/esaVL8lUXwzqBjXL54/w\nMV/jyd16YlrdzNobX54LHjZudejaYCwAS5PqqW19swzkM9ztRt2A4LwTzxdf\nTUvufpwqNo0dDxMiz9m4/Q/USc5nIG4s4OB7jS90StK4u78zdDl6W9tgpUqN\nBuK57BhAFBEfH4qd3eSqRJFlxBTGTTvRRhxRtQLcm/Rug02MqNYk4kacZHJB\n8eR2\r\n=fJ0F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.4", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.4.4_1556312697261_0.42407171906429575", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.5.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "02edb97f512d44ba23b3227f1bf2ed43454edac5", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.5.0.tgz", "fileCount": 8, "integrity": "sha512-EAoMc3hE5vE5LNhMqDOwB1usHvmRjCDAnH8CD4PVkX9/Yr3W/tcz8xE8QvdZxfsFBDICwZnF2UTHIqslRpvxmA==", "signatures": [{"sig": "MEUCIQCKagUjy1KfPv0QwY9T+gvev7eAyfC0sHANalzQBXgGugIgCacHXnkPCHW9C8bSE/vgmcm+tVIfPANR5/JzKIg0R1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffQCRA9TVsSAnZWagAAxrwP/REPGI0r8z/WWYZI0COQ\nfeNU7KrI8kvlRL2ZIxMRvCJX9KLCs9QB+QB1/Q47zckViyPy+yYb8gTT0yoa\niNfDPzCDbTmvDhxmiTAwcVUJKLdjhlZ7gSrvr2N17HGB6cN2Nblnta4M2jhT\nJm9scdtSuocTCux4MeZ7wLsN5QIH9xABP86/X4VGB8aIdwM7B7S01e+8/xhr\nQkD6JdmBOAlQYu8Ghn26muj9nwk5PVQYBRqSyKt/Hjyh+UlLGAiQ1ILAbz0l\nxVNXFSQwf15zcTiJmStU3w2nM4eXEyeckWIJHrrKqrlvdO860FCVgZhABzUg\nyneTP1HrLIvIZXHu5qvXwPo5KMQWku/lhbIYpQOaa9pQjn0mW4jDXYo/jA4C\nBuxYR23e+Sr8OQIva4ok4bW6Qwm+OG/QhHAQX2eMj5tbNjG1hhMQ8B1aeIwR\necTVNT1guirp4mNuKmZDK97Ru14QnPoXsflzU6OTVxBfN0R6X9P95mcBMlA6\nFVBz0zhh/E9xyyA1Rie9WpmBwEqvsh56qt2SbO3BQaYafdifN+409UWkyZsH\nx31zvMGVSVsxWubyj02I7FZR9gat4QQTgcknvunq0qX1nr/K9LGl6l6msbOJ\noYaNNjwh9WPIdN5jVXrbcA2sOQoUkQhJWxv6tE5fzXjorkJU3WHnOQlj/mwt\nxPD7\r\n=VfDM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.4.4", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/helper-member-expression-to-functions": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.5.0_1562245071507_0.8365006316675061", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.5.5", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "401f302c8ddbc0edd36f7c6b2887d8fa1122e5a4", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.5.5.tgz", "fileCount": 8, "integrity": "sha512-ZsxkyYiRA7Bg+ZTRpPvB6AbOFKTFFK4LrvTet8lInm0V468MWCaSYJE+I7v2z2r8KNLtYiV+K5kTCnR7dvyZjg==", "signatures": [{"sig": "MEYCIQDTOyFrKn8ofg0J/2CPCh13ARX44AR5NoKOfxmdGNw7ZAIhANWdx8F8I90oExvVCoYGA0agoKE7BkUhHafcCtjBmHNn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FxCRA9TVsSAnZWagAAWlcQAI612gE+OynrW5OrDnf7\nNcnAcSPNA7IOG7qLzAjJ4JugYh8ehqXY3yEduRezVtAJYcAvJVL4Zs+6mLAO\nI4yoqRZ8RMkQ0vMol6xog2mq5xLKHZ1ufH21JlcC63gicNk6R3AWwBH89yet\nHYf6EbsEa4DE2fUrHtXyKxwNi9hcK2dA96+nRE8lks9Js+HNu+t0KLc7Saje\nuToHMpnaWmnc+2paH8+YLLikgjexQvC2IG2MsDX9cuTKeKGmNHUPIfUivdB2\nOItNH644Y5i8oWl+7HqBeOddfLyeFEahu97iUuYY+ZXsf7oTP0B71893u1PS\nk5rrkq5MMgRUVi+NWagmlvwgXc3HYACcd8zx2x2FfiHT3hEqPQgJv0peUQiz\nomYBo97gYbi1SGoMoiQ4R+8k5zUPi49B/o5Kd4JS7hFMSDztXxlFtyC71ZOi\n6fFcfuc5nqc29/tjX1zQI9/lTYxsKn4jcbcIczPtdIcM1OnGff09MLEI5eF7\nnszVo70qKuCkWQyES45QkTeGXCOa1us/f1mNOOK6Qas4T9yX9JLA6RYJEKT5\n2bYWDKqWiO0v1O04nBhh/slO3nOO8bEKKAmTsAW8l0+15q6OVEd3CKcgRiRK\nb067hLzc+Ub6jMeG9lnssWcf6v97udnCttjHy1+5EAIfsjo7Jm65/6/Flv/T\nA0iY\r\n=YUYs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.5.5", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/helper-member-expression-to-functions": "^7.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.5.5_1563398512971_0.7419540128404563", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.6.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "769711acca889be371e9bc2eb68641d55218021f", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.6.0.tgz", "fileCount": 8, "integrity": "sha512-O1QWBko4fzGju6VoVvrZg0RROCVifcLxiApnGP3OWfWzvxRZFCoBD81K5ur5e3bVY2Vf/5rIJm8cqPKn8HUJng==", "signatures": [{"sig": "MEUCIQC/Trw7LXemZawFxFvUJ2UyK5dh9dQQBKBtGJHy5r20bAIgFxKG8KrQ0VwPQ0UtIpc4YOXx67CdqNYtftEmegzArt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcphxCRA9TVsSAnZWagAARcUQAIuhE6ctzc+GVfBBg2m5\n5aIhm3EhjbWn7oZtM75UpyRI0lsDw+xJO/iparpAALnoiHpzq99SYrVKuTWj\nXhUocm6084VTHLiZv+bXu5kZWJ29Ce4IbAApDqS/S+jCsIokMO+acWznEOcF\nterGAS1j9e3L5UV2StG2bK9rv52kYrjW052HWCtGuUXXG01T8Am6I0JM9TDP\n4wK7TPgEBRSIJ5KTMX0Z1BS5ljV5V/lrMk9uxz5688YoQtiOeaa3fGJdnk3p\ntKH7xlL8Bqlme2YJ2iZNMPcuNnWsCI8Y+THIj/Wg+gLwXIRC7MKm5mmfCIjq\no2Ibwd2an7NxqJUoOr+F+H6N3HA25TFPBbAloEe8wY4htxMDqLi78AU1qDRr\nAGBzK+09cTgh7UmnvZb36g3hNpAFIftr5j3Jq8O0/E9yDgPXtS9TQZRN80q7\nbRilvgwxJF1w3cuCxSGL4rKgBwyFPCXwv4OmdZdzRWdUz9L3VKTM11tRMUAn\nE5SUbyPg3y4xw62DeJzPLPcudGfVNT0nRWlqdY6YlKQ7gMKu7hLe9g1b6uQx\nO+dxeGXDCtpMNj2AeEVVXlYD280TWLcdoY4NJTMZms9jcSWmApyVU8iqyB/y\nSLB2Awkpiu77M1YHNSuNEug8qzIt/pUKoQhZcDD0ZgXcS3VJSE4bUBXy9CNw\ndGRg\r\n=HNp7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-replace-supers": "^7.5.5", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/helper-member-expression-to-functions": "^7.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.6.0_1567791216830_0.7551554303145502", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.7.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "bcdc223abbfdd386f94196ae2544987f8df775e8", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.7.0.tgz", "fileCount": 9, "integrity": "sha512-MZiB5qvTWoyiFOgootmRSDV1udjIqJW/8lmxgzKq6oDqxdmHUjeP2ZUOmgHdYjmUVNABqRrHjYAYRvj8Eox/UA==", "signatures": [{"sig": "MEQCIGGRVcYyBGWX3juI7c33oBx45rDJp+QrQpn9qemFMVBaAiAVy/FoUfj40qV0vooVhTCOydGZjgSgBLKFpAGbpZ2D2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTLCRA9TVsSAnZWagAAytsP+wbnldSbTt74Z3k/GBTL\n3UoIY1k//45wha7ZqTciekU1/ol5+2vzzb3eoAE9DlyrXa9y3Fog95kM1IME\nugN7IdgXkg3BSeYvYTNF/1pyXX038DJO7Idol2cAiCp4UsIlorABG6Qft61o\nq3NBiwiRYPBbj+XsKkM8o2Ix4Vmc8GfqrDnBL4gesQhmqLsZAtUIlbJeTm+u\n+QU7Xu0lEhBJ3grsMlyMY6zYXp/m5yjUeD8+plKEjXRJM4kTELeUp2kwtrVl\n8Ox9N1nu7S2tvbos7AOy7XyaDJwlMeugPgakjlzKlF7UgEBu2UNfLxpelsep\n0BLLC6Hf6zljFW2KvxlnGTc899MCZYj4isAVQncqkW2LJ4dcq5obJ3VLp4T7\nrwqCyccX8oWGMM7Tkffw1pcAVdBZEgcey2oCJo+OgmWKn68M5MtQqKpQKnKO\n4MOtvipSPubfLgPz4ecxSDi7n49P0iF/V/erWsDnJkgZX4Apz7opK9TwXLwL\nXDdLrJJbbjMF1iXGQQ6weTzBOfvtaSmsPsNmNKM2HmKumPojCONYOtssptls\n5apY0pUY8apP73Ud8uEdbb8VuFlPXDGmiHhqtKmtbwDMlL3QP9NtBbBz10vy\nXKlPYgGsBeZLz2EWB/EUToW/USdF/lmXFl1j9U2cUDoOVedzL1LMWzdz/Cis\nMTwo\r\n=6efU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.7.0", "@babel/helper-replace-supers": "^7.7.0", "@babel/helper-optimise-call-expression": "^7.7.0", "@babel/helper-split-export-declaration": "^7.7.0", "@babel/helper-member-expression-to-functions": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.7.0_1572951242632_0.6109750072939153", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.7.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "fce60939fd50618610942320a8d951b3b639da2d", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.7.4.tgz", "fileCount": 9, "integrity": "sha512-l+OnKACG4uiDHQ/aJT8dwpR+LhCJALxL0mJ6nzjB25e5IPwqV1VOsY7ah6UB1DG+VOXAIMtuC54rFJGiHkxjgA==", "signatures": [{"sig": "MEUCIQDYcB5344yvm8MjqNBJCp4v1cUIoIRH+PTcEAmG2w5HVgIgYR6iGkinsI1tLyC+sQ/qO8GaZkwPexQfTxq27qTNsK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBgCRA9TVsSAnZWagAAr3gQAKNvg03Un64oRdXltYrt\nv+ZsnlJ9TUR4xj3JJlIzzWaTqFKyWDgVSVX5bPqUiCAZtGvXHLBHW2Ewg+S1\n+Wu3VN8g5KKFSNEEaFuQgeUmxHcItghfMqTlWInf2mUSps2D6lXxTqwzlBO+\n69sDN+j4aVbQ3OVSJtQWNkqJIXB0tC8/2DVRvdWosuph87rY8Pe3zLd2lSBo\nn0neymKw/mZkQRV43x5xD53Qn11GttGFTwhLebr312eLC3XeebP63FElMcwr\nC72R1fUH5XKE0hJTxg+eg2/NETmY5NdEg/RWch+weoeoH0mMDQ5wtBcHjD/9\nxkGPieku/JHgIg4BWB/8hCWXTupBj3utUluvYQTRN5+A7SrgS/us4iL4BczG\nOBV3pm8I5Hz+g+HbpfzYQcjdXMIQ3eGj6975DZGx4tBFOuCsM/OzKe+jaKUa\npq526pi7csUwea/ZJ0sXqd3dhUGcgnXo5i3WNrtqts7zuPpZbndQPvU1WM8I\ns1pOOHyEoxGSZwp0Ytx79lsUvUxYRNC7vy1v2xfS8xWC2efqaaDtHknHDhuT\nQgZnwhoDsBlczuB75+eJnlo6EpGtbShw4OkN6GXrUDHVLKvlJ7AeiAKFI9xA\n50hlZw5+IN3b2EFmNaFgdWqL3gTh8fOpxgWXA2rByomC+Vv/uaMBsuXqKQb8\nv+QF\r\n=3ZgF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.7.4", "@babel/helper-replace-supers": "^7.7.4", "@babel/helper-optimise-call-expression": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4", "@babel/helper-member-expression-to-functions": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.7.4_1574465632556_0.0007368346118852287", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.8.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b3ddf557ed4656e0d296c3b0f3fcd381ea8de72c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.8.0.tgz", "fileCount": 9, "integrity": "sha512-ctCvqYBTlwEl2uF4hCxE0cd/sSw71Zfag0jKa39y4HDLh0BQ4PVBX1384Ye8GqrEZ69xgLp9fwPbv3GgIDDF2Q==", "signatures": [{"sig": "MEUCIQDFCKOWwTctFLAzYjQP+2CkCkrVM7IwIEDQ47Vsv9p6hAIgf0Y4OKl4lf5lhIQjeXmOLAK7/4p3PBkoBOxBdMIyIeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWdCRA9TVsSAnZWagAArMEP/RTnfltUe4SjzC5j4KJz\ns+5qvZ7PhhdC6bWkN/gRdBOAFfz+Y+zI0//OvGJhYgYc5+vztsaLKLZ8cSi0\ni1raJvTctaduVat+2dK1asaaw2Ejp+z1RdoM94gxJ4N7MR9Q4pzh+/i/HYM2\nPcQfMWw6FyPDlHIzqVn49myFteBInyuWLSRFnn/8hAvme+zT2Um1MBCtrh8B\nuDGBNwmmMXroKkXhli3Qek+8VzfmFi2IoB3tTAOrml4neXA9tcsq50b5dbjb\nQBs18GcJvoyq4IjpTlD5rZNd+vWHroXV28u6W9y+nwohhFAWZlHTW2g1uEFb\nQVYuMoJkw4YAkRztmJj2bw/vVQKVgQB5T4MvMs8uPGeNO58Hk7bWuUUf40fj\nVZOcovtF3JVv5jcHJY1HL//I+NTRvl6ofnAYLHmNoHZMgJAiJ98nKW14hofd\n0WSp51G+5GKc63S8Gf+4dcRxS57U0JgCYORnZQfBihw4579ZYSNEYVIxrizt\nEfkgIg0LPxy5aNTl5ZkM/yg+csCVh5ywZEDBZWYCx3NJyvoMqIzIF6Tu5MVp\nF8sd7o3mwte8yESD8NICLLcjwIs11kJBlrlwpvvnXReI1lc7MWq0vWoCmVmC\nXrQ2UpIqjS41jjzVuvIJHLIPdIzSJ6DM/0ABO1gY2HtNko2PcDcQpZ1wUrKj\nP4iv\r\n=vVAT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-function-name": "^7.8.0", "@babel/helper-replace-supers": "^7.8.0", "@babel/helper-optimise-call-expression": "^7.8.0", "@babel/helper-split-export-declaration": "^7.8.0", "@babel/helper-member-expression-to-functions": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.8.0_1578788252881_0.6328816963179369", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-create-class-features-plugin", "version": "7.8.3", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5b94be88c255f140fd2c10dd151e7f98f4bff397", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.8.3.tgz", "fileCount": 9, "integrity": "sha512-qmp4pD7zeTxsv0JNecSBsEmG1ei2MqwJq4YQcK3ZWm/0t07QstWfvuV/vm3Qt5xNMFETn2SZqpMx2MQzbtq+KA==", "signatures": [{"sig": "MEUCIQCgUUhDePyPoUKPqU8dqxy0ZhPUkTJipyt6/MBmoDcDRgIgXkNuyvIqabM+oVbJCeJPrPoXTk14LXwUCZSdSMOCkQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORGCRA9TVsSAnZWagAA9gYP/jq7++VTY4vUwWn77tyo\npr4DQTGBmgqs5XgGUxSF+tKXkVG8zwLbc241p6ggxcjRX87a/l6nc6vyOvdh\n+RP4fkRCZ6kUDa6n8/tLMtVjbc5tyanNinvd0XHbVycA7RFdyY89K9OIiPSh\nExjaIOUELfZJhH/XC2oqer4+KTQ1T4fDawRvcQUcsMlKZsr+rLp22h5IeSZ6\nrhAkBsuH4OzVn0rCmYS4Nfd407GvcD8v8YB56woPITjKEW3KN3pwdWRwq2cT\nZ4Ya7s8Ez4lF4aBB9r6O1t4WbFJjxOF/2SkouxoD8DaPMuDRAd+7Ei0ES5Yo\nW2R3l+/NCEQv9NC/BuTYdR18vuKreVK9n92h5rTrBnvW2P3Q8/WkvnkhsgzD\n8bvADj1YlnwJvrG9azMig80UQCY15/RVwb2j7biMEgnlpvdV8lFJdyoQnCTW\nWRvpt0UTwRjpCYi3Pk7IlfvYvT8nWFAnofrW2Z6ZXOvNNtGQZ95n+ThMinKi\nEKe2cDqjrZFoPg2yQrmqg+tBEytTVOBZrZX6AMTUPpYduha0a+/SrwNhd32l\nECvlHZarJMy8j2mRTmoc/Qbl2CMWHpiShyupFA6OpeUDSul5v/YTgDzhKDNR\nDng2k3EFe8dVNYBXdBn6aH+7B2MR/1XWbQsznJjHsNtK8PO7EZvSGREn3Kic\n5VKF\r\n=sC4F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-replace-supers": "^7.8.3", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.8.3_1578951750178_0.6888995393489936", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.8.6", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "243a5b46e2f8f0f674dc1387631eb6b28b851de0", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.8.6.tgz", "fileCount": 9, "integrity": "sha512-klTBDdsr+VFFqaDHm5rR69OpEQtO2Qv8ECxHS1mNhJJvaHArR6a1xTf5K/eZW7eZpJbhCx3NW1Yt/sKsLXLblg==", "signatures": [{"sig": "MEUCIE4v08EL1v2pQVXNpdnqZLqR7Jv/Bs4TR492pe1x5BRnAiEAgfYtbk/yLQg8tiMj4ToIozhVq8VyLxvIRPh/ZxupsG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RaCRA9TVsSAnZWagAAVW0QAJKpKuxkjKAVYqTDJwjc\nvd5SqnsRSCQqwA5PA+ZSKTZ/sAuAxbnx+8dtJVpnzioAyKbfKnZAeOltfA/j\noqMEi5dVc2KTqIMxYMDK5QEDYIsrzw3dl9yvOss+LcZJhWHsXL+VePTqwwH6\nkNxadQR8kCS2CntPmUvKgUa/UKwgs8M3R/EjtsKgkqiu8fXEROp37Qz8Sqfx\nJkbBg8hCN8w7wxd30KY+O+tP9BWl3jy+WYbE+MNw9jMzePwE7FOI5hFe4aWm\n+i+a8ECMOyErP5Etju+kA6vzyynbzdF+m8zR2eVdgdkBWy0GsOaRXAoxBZ19\n3/HwzRpiIwAmZehcQgDGsyqbrBEekNcydrq75/migVfMOvXvPCco1YsuSk96\nLi5N1+iHH6L/NIoqWRETEAI6c1M1/MdKL1C+n11j5clKEF1OsLDg4dvccwOO\ndI5xp6GXzUIdI8f9BV66gD8e0DfrDxMBOb+VxLNcuJn3QGzlZ6zauoJiESZY\n8QLrrkb0z/WqdXdxniH7ii3dZ4dh2w4ivAx2uKsWTARx3bFV4TRcG9bdcZqU\n6n8rw8GrYyqaqCjhG2LkDcMp0KbZEwlQVdBG1hO1SmwyGWGQfL7Z0lIQSlHg\nIgTGtnoEbSLL89mt7BZ4SssdRSigaBUnyhBkjWsdmvBYy08xkobHO4pa3cyx\nCyqO\r\n=LB5p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.8.6_1582806105784_0.8369661159663635", "host": "s3://npm-registry-packages"}}, "7.9.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.9.5", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.9.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "79753d44017806b481017f24b02fd4113c7106ea", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.9.5.tgz", "fileCount": 9, "integrity": "sha512-IipaxGaQmW4TfWoXdqjY0TzoXQ1HRS0kPpEgvjosb3u7Uedcq297xFqDQiCcQtRRwzIMif+N1MLVI8C5a4/PAA==", "signatures": [{"sig": "MEUCIQC2OdfhLu4Wr+hwDr/dc8Z3k3T3JKwyA5gdyHdBi4JR+gIgDfzLLSXuITG5eKqTnTss2hOgAYu2D3mfg89dMDbQhkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOuCRA9TVsSAnZWagAAJdkP/iwzScD1Ied260+bal+1\nfwTQ7AZ6YnDhdE27bmQgmTNZFNmSPOdpJURCTRTouGKaNzTOFsLarvfUVmnr\nN18vDFX4f/9hzkoA0csKzffslegAmwXTj+mZykFqqaFIq4xQh766uwYNSpgZ\nhS8jjbWe5ny7mazebQKaW5rqzMDHgOH2ndItrYxPprgot+Ex+RU6LzLV8BBd\nIbId0ANWEl3JOTiCip23u8F+v+Q+/fk+LYMqA1nC7rWixD1ufNjbyrwRZ9oA\n419eOSnWE/oZHCBma8LHiYoZ5ewxjqXwJRQ2wiRkpsySd+wUc1B8Vz+wYU9s\n+Kaow5TYnzWrPEgRi7rv5FRUxfCuqinyKKD2PczlbSwn3pGzC4W+rt5rNGTN\nsnZrCpvftaR807UsuUYC0dDA3qQi97TPdJRhhFBB3koibVc40roojaYWxhQM\nII4oKKoOcq96NdYLd+nDiz65D7WM8Cg5zoDVJyu2UNALCliRhcfPo5znyKiK\nJPJj/atJkfX5WXgSgoyDDOzynY2kKs8xVotQVohh31aFhQtS94C5opCz7Lb+\naDuzw0HDFrCSqQk42RwFqQLAokys8Fvmjs4tf1m3w/reAmeH5pquuLUCGH8s\n03oLsLN1IOtMpd7gozPs0GL7tQDl9ixF7NEve1Q5kZIm+SfrpyB3I+kv5b+3\nTGb3\r\n=U8kq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v10.19.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.9.5_1586287534245_0.9068945922776601", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.9.6", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "965c8b0a9f051801fd9d3b372ca0ccf200a90897", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.9.6.tgz", "fileCount": 9, "integrity": "sha512-6N9IeuyHvMBRyjNYOMJHrhwtu4WJMrYf8hVbEHD3pbbbmNOk1kmXSQs7bA4dYDUaIx4ZEzdnvo6NwC3WHd/Qow==", "signatures": [{"sig": "MEUCIE0V0hi9QPgqGFYXekUgdIDntuB6PHGEz9+zjZAM+eGAAiEAtfIan6frMY4iSw2SZAuiT5y+socC9bM9hiWAwCALsT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmjCRA9TVsSAnZWagAAljkQAIqgTZbYyePBkpSTFFW+\nFdtmx17P5djCbf2YwoqCIedjnltwaa39+DZYJ3SqNy1HyxTyUs1Jkw3qZlqa\n3DJ6jdLANIJCcvpj8bJBTPZSf9++m1NcV+/pyUcd2KrRL6YC6C8/FlehEyLp\n9RyohckZOAXGzaL/v13ZhHO/TF2XB7Tci1nb3L5l7ii7LJTfbweP0lTQQdEj\nV36BuRI0IHxNgaYXRMpdwFxDBSb3j24pGdtFP1ETuaQjvJIz7b2wkASySH9r\nHmN3o+qwCwHcG+WjWp2gHOHUOIlp1swr2YdmL+xKaZt4XOdFLwJj8RHWuYBa\n6nJpGY2exORgYPoTLJSxbo8nsBwlT1fy1hNV0Dd4lIi+UUXEwL/gwmzguNi0\nUcV/F6aw2F3sVeWtkx8U8lGqg8b0RLvnEIZW5l8oXwsXkT2/RpvPfxhpbvJE\n1gow3NsQJQqr8JZ5Lu3FjAQSp3DEKbcLdzHixGevvDHw2TyTC7YVa8uZFRdY\n5MLN/LpXMiwwp/5AA//JHJ3C/r+Wr5oM8Cphcgw4YG1FS+7XqOnSBZSfIWQd\n2JHs99ephf6Y9UO+ZY0GNGTRO91X/Hib95JD2ACsWe05kNoMwlcxJ6RSkaof\nj5iUVwzupz/cCxK/eZpeqZDZBjINBm+4+Q2qoCedt4C2foHUoFmjHWk5daPA\n9oNs\r\n=IWDG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-replace-supers": "^7.9.6", "@babel/helper-optimise-call-expression": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.9.6_1588185507101_0.9765279768095179", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.10.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3a2b7b86f6365ea4ac3837a49ec5791e65217944", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.0.tgz", "fileCount": 9, "integrity": "sha512-n4tPJaI0iuLRayriXTQ8brP3fMA/fNmxpxswfNuhe4qXQbcCWzeAqm6SeR/KExIOcdCvOh/KkPQVgBsjcb0oqA==", "signatures": [{"sig": "MEUCIQDCSY6bSaWsZHUfl4Lrs01N90higi2JK4dm2PsFEELVaAIgAXqf9kc3byi5UgPVCqJ2naZ3x0+HrCszhddPsk4A+Ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2VCRA9TVsSAnZWagAAO9oP/1Tag1Fuy3vFaWy7B62R\nxQD6VS61veqkLUBcSw5XJIMGxHLF16+bW4G4/X5gbqlDNTG3mwvdBwVlM8VY\nwQiAAjFGAerzq5qVK2RR06GUYyNf0yCE/OSMTrXEYVyoEU6LMh3PI5U7HnCJ\nGNItzfL2k/Bv/inCRt03g/8a7iC5WkcGKewO4wRR7sVFlW0LbicTczpPYfab\n+MutMQOit1vxOdwVdBUnXZZEeeptNdCxJvirMvqPVXUhh61t0Lg5/LdXH8he\nDlf5lkxuuh12yyk8DhziQY+KMI51sQNKyNQMTQWvwfAB9iPyQQlQZebOhJsU\nuoGgewJfc14/jXzTuYbKupQCbk4ePsMPZ9SUdaACCWqY7I8iwSe36j2QnygV\nUfacKxk/UT3w3VHPmlxE0P43Z4tj/VJqNzwSl86wpNo5e/GFB4o3eWeAMVni\nU88/ulSf6p1/IOm/ylxPw4eOjwauysy6N5jB72/2d2H4AIberxvLSL9mj60U\nHwQ8AjEnS3oCrjq5yr3AA5ydOpeQEzc3WbW0OlRW5+pjwGMNjalwunjlSu3S\nR60i5DrxguW0Na1UqrUWQgmaW4d0AUDpL4fVIByDkgnBYPYQ6lSfcMKqokKV\nX+rrMeXRIQej2pscGkigMmts7HS657/mnC8ZBCBWNjK6fISvX4Zmiod2gSuE\nsQDr\r\n=lTVJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-create-class-features-plugin", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.9.5", "@babel/helper-replace-supers": "^7.10.0", "@babel/helper-optimise-call-expression": "^7.10.0", "@babel/helper-split-export-declaration": "^7.8.3", "@babel/helper-member-expression-to-functions": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.10.0_1590529428480_0.009025949501543895", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.10.1", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6d8a45aafe492378d0e6fc0b33e5dea132eae21c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.1.tgz", "fileCount": 9, "integrity": "sha512-bwhdehBJZt84HuPUcP1HaTLuc/EywVS8rc3FgsEPDcivg+DCW+SHuLHVkYOmcBA1ZfI+Z/oZjQc/+bPmIO7uAA==", "signatures": [{"sig": "MEUCIEuc1HAtqd4EceZJ91MhqTFmak0dmtFiFjj76i2xC2IyAiEAzLCPuzRXIrcT8XmAJHYHjq1Jvtl0a96p3DMDufUutJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTbCRA9TVsSAnZWagAAy+QP+gIrjR3LcfyL4OnA0mTg\ntHRRc/5KNV+tA1xCBvlLS/tVTcLW6C/1MXCPP22CZUUZj+ijdfWIiD3pvtsS\npndbjzBRAQ4HxcMwykJtd9nySxs5uiMT8I+sx8KC6ItwETBHJMb8xZUnMhwS\nJnhRp20SuX5c8SJ4wP8q8VeQRKDaWkuboxXSfsshrLlEX8vU6yYryobqTImc\nVyv5tdUgol6A/ux1Dj6tLZbBui/HiNIys9RInQTYwRAwATSp1uqTJnXcqfqV\neDOrKMOtFryvD23OrrsqGLMvCMxyyGEnB9Mcwqq6gQWFf8ubHV+ZEsDKMXPK\nojLI5Jd8V1SKeBM/ICYa8EY1bPpXvx6Iv5rA98uBj/zlgVykmI55cv3GLHNe\nTgv9Gc2KDGr+TLY9Nk3qT+PZKvr+Hss+H1UzQkDRn5SOTtTGR4Azp2x1t1HF\noM599sHvyKZlsyXd+4Hr7hsrov5+St+suDbVUC7vYX0Ns4mNfWQMmp+/g3nH\nVwOZ2zneulNLFzgz2WPasjYBAWejQtzQB/fxXWrKlKNIpr54JnF80hUexu7x\n1tlPk7H1FOcwcCQwwm1Ph8Q9ar+eqiTZV8lxPDF8kO9I8mMrH9/FTDc2qZm1\nYHCr0+TBXO2vWX+mI2cx6kcw+wp0jhVvHfXN3Axj+ajGU00jwUkk3xb/f6xh\nB9Qy\r\n=VvwJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-function-name": "^7.10.1", "@babel/helper-replace-supers": "^7.10.1", "@babel/helper-optimise-call-expression": "^7.10.1", "@babel/helper-split-export-declaration": "^7.10.1", "@babel/helper-member-expression-to-functions": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.10.1_1590617306757_0.3188483126569943", "host": "s3://npm-registry-packages"}}, "7.10.2": {"name": "@babel/helper-create-class-features-plugin", "version": "7.10.2", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.10.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "7474295770f217dbcf288bf7572eb213db46ee67", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.2.tgz", "fileCount": 9, "integrity": "sha512-5C/QhkGFh1vqcziq1vAL6SI9ymzUp8BCYjFpvYVhWP4DlATIb3u5q3iUd35mvlyGs8fO7hckkW7i0tmH+5+bvQ==", "signatures": [{"sig": "MEQCIDVzlR6+xNha67amQK1steYHmEeC1o55RzdEVtP+W+XcAiAqsqDldMqEy93jXJr+CDtEqb2iYwny/0QIZuwjEjmp5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0rMXCRA9TVsSAnZWagAA0vwP/0NU8jBUfc9mikHHQAAP\nkr12pAV43IDZLVbmNN2Jo0dWKJ+L57796b/DjbUCl2PzyPtdcWlA7qpR4vnz\nDLjwz5Zjyb5OxnMX1Rtxdj3Ur7aysCSAjPtOcVePii3h5g7bmZfBW32WZZFX\nKM1+DaJxmn67/gQ1xijAUJ/C/wyvXOreZJIHcesaJ3oibMZDaI2q/flSc0Cd\nG5ByF1eYp3mdojCzsGdgBFy9izJvMQ2Hi0WAu4EVccvAzTNCSTj77UirdKla\nRO+OHQhrqnmTKDb7wj6cc3FABhuDdwMaICCh1Cj89/1D009GYjspkS6xz7kq\nr/Gm6UgmdHhkZ+spx3XyzouR799ISOq1rBbZFihjp2ne1L07icgX/LG7rnO5\nq9IvJxMtC6CZS3rHNn/yiCR0j7mGwQf9pPa7nk0ph2KHSD+B2mv0YsL+SMK5\ndNGw+YeqljacTlzesgI2wtSmeP2kHDC1pSwpJt+X998gdbbjzQxf2baIJIKV\n8CNG85ahYiqyW1UBsXPF6TYgtQpZJnu436i8vl0dZw9HF+DeITCT6B1N6gnL\nl4GpVjgOsKyHxZTdUeahmaQvDmMCwKb8xeqMIkcTPXjNLrmD4qZWuSFB2aF3\niv7xDtFzJ4aPljnMa2EfHQoTrw052bd3T2jVpcIzgJPLEAGwrp4q0aEW6t4Y\n0xQQ\r\n=WG5m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b0350e5b1e86bd2d53b4a25705e39eb380ec65a2", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v12.17.0+x64 (linux)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "12.17.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-function-name": "^7.10.1", "@babel/helper-replace-supers": "^7.10.1", "@babel/helper-optimise-call-expression": "^7.10.1", "@babel/helper-split-export-declaration": "^7.10.1", "@babel/helper-member-expression-to-functions": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.2", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.10.2_1590866710512_0.9334004144711505", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/helper-create-class-features-plugin", "version": "7.10.3", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2783daa6866822e3d5ed119163b50f0fc3ae4b35", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.3.tgz", "fileCount": 9, "integrity": "sha512-iRT9VwqtdFmv7UheJWthGc/h2s7MqoweBF9RUj77NFZsg9VfISvBTum3k6coAhJ8RWv2tj3yUjA03HxPd0vfpQ==", "signatures": [{"sig": "MEUCIBM4hZz1yXtMIh9UuSL1fyswVaxjsz+haKxo3UBge3VPAiEAz+DoBOZ8u7mxk9ubHWb8DNcvD7npfzL3sxgu6TJNc5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYSCRA9TVsSAnZWagAAUHsP/34P6XaDyEKrsTV9t+K6\nUDZJHyS7+T03GH2BrTCcpFEnL7sGHGpRIQxEcSi5H8mFi4v0eerhUg95MHh2\nIzdTQZhUH51/X2C48bHuxyiMW9LYCQKCCnYiDtP5Tk7YxAMq54SPdpcJNXUR\njRG/SU2bDAlauaw0J7ajzRTKBQMTInFESUS+2bDxLw0RZDEfgYbOUWBeO1aK\n/8e+cADTHeULp6eaHmznDCGVMR88jJAKGIkMntUBRzeT3Xz9NhCfBB2ignSM\ndob3k8LrA/hrj2hBqKudj+1tDScTRdqug5AtOjjh4Fa5yFeYXtRL2I24Nfwr\n5SvfRvcLG/c0dLnkGO20lgiYWKJ2Al33ElxsN1er39T+7DN+pv4gNb+vybLH\nIUmwTN/McwfofzAzBvJgTlpqfL7CYSiClgViB3VXui48tPL1e0bnKQdZg/Kh\nzQY0p8vgFBoU63LtLluNsJBK6zsDuzA8DdR8b37zAqEiOS8ymUCRv/0OYZnr\nmjZwuMYcCK1ZeViVsgh7wzj8o+XJRQeLpTzKbwhJy9NEHqVpcCIzzBB5zU/a\n82XyIInT5eXsPDabE2wBQJqA1i0mC9QK3TBclcLM1sLNU/z6MNcwtupJqGQh\nQ/XUQk1C/CVlstk8g1YbA0EquY4bBkhXv/HVRrv8ycooA/li3DJ+tJsR2OlP\nFeS0\r\n=gtqq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.3", "@babel/helper-function-name": "^7.10.3", "@babel/helper-replace-supers": "^7.10.1", "@babel/helper-optimise-call-expression": "^7.10.3", "@babel/helper-split-export-declaration": "^7.10.1", "@babel/helper-member-expression-to-functions": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.10.3_1592600081987_0.37306701243850915", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.10.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2d4015d0136bd314103a70d84a7183e4b344a355", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.4.tgz", "fileCount": 9, "integrity": "sha512-9raUiOsXPxzzLjCXeosApJItoMnX3uyT4QdM2UldffuGApNrF8e938MwNpDCK9CPoyxrEoCgT+hObJc3mZa6lQ==", "signatures": [{"sig": "MEUCIC5x7DwkvaIJFuVRSmC8/khjuSi4+kr+wKwFVWvVP0rvAiEA80dCEeVcjpJfX2nLA8DzhLnwYWLAjPAiJzptOOpMjDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpzCRA9TVsSAnZWagAA37MP/A0ZiD1cazAb67j20tlZ\nD3ReGcVqmLsMRG4gf34mTH6EBOeVdJc4et2EA8uzVJeYky7avmGYBE6zDHSw\nsakQ4j3/Rn895p4wtf/1MSiuBNwSAGhfXKwwONeC6yNjLd2F7KPxy0rYCI1k\nvFbqkH46mSVyAzUbMJR5GDboKATUrt5gIrw6e1dv1rXKaNa2OGvrlVTGC+gZ\nD1iVaCtgHKTKFrFCB3zZa7LaxcrxRqS4aLc/SKPWc5PMxPsGthfnseYuyGGd\n7z3SpfcsFNz7Rp91aBHcJbeoQolBR17I22IussVPfU2zjgpbZPZltPDxIdR+\n8GQc54MnWzms/zbB7h0WkQhyShbzZgAaUJGg88+2CEB+lGtraB5CblJ5a94u\nhakOLzK5v+/vSnyGzyB5cHYop1qBxhZN2UwxrQyj8lHUqD9NEe+tVHQ2vGos\n5SjsDVWdy1TWcCloqNkJmEqko9Vg479Y06DAPY2DoLufiKQQ1MnuuFfZYNbp\nz2RAY0L52dSBQCD+1QALjYzDtbz8l9xs7q/Jfhw6ZDHYcsZF4IAe3wjbbC02\nQZ6wZCU9OfVpvOzXlFtRYiTcaE1OqHN48rbYWpoLm0ALLQP3WQTb6+ayXDf4\n0BHkt8DMKZ1/4B6CkWt04a6gjy3EImNjkWOTcJRIqBcZZdEdfPHwK+u9RC6O\nQghl\r\n=qkmU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.10.4_1593522803184_0.5893483372007604", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.10.5", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9f61446ba80e8240b0a5c85c6fdac8459d6f259d", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.5.tgz", "fileCount": 9, "integrity": "sha512-0nkdeijB7VlZoLT3r/mY3bUkw3T8WG/hNw+FATs/6+pG2039IJWjTYL0VTISqsNHMUTEnwbVnc89WIJX9Qed0A==", "signatures": [{"sig": "MEQCIB42itPQpqYhxazpAROpYNtvWQPqQSVug/GXDK4hcBwEAiAxeg7dylqOAu5lKbzZL2CW1wyfX4si1hrNAJkLQTLk5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbpCRA9TVsSAnZWagAAXAUQAJdhjyzPFyH/+XaBC7vr\n5+K/sSET8An/WjBX2SEoA76hgerSbJG25rLryKTAECvbJvFrSv8tXGjYME0v\nsGvW+0oYSCW87tUxZrv/rzhThE2gvbgl2I0NVPY7iif3+DQiWGDkbe8Uugqt\npU1yl9+if6MPf7wEPzxVWM23Wcm5QGKZuJi0iciaqw7+bUdhGo+OEChjABPx\n9SYAp/l/ahnYqNXH3sJFDg46dBz6S3bBlSTF2rtV6Ry3jptqzrcY6T4boIfC\ny0n5DG8G59I7pHKBOjhXarIYg1817z1NIoQL9YvIG3uM6RbetyVTo2bsLzkR\n9ImVPirQLBUGn4aDjOoqNpjuIKJEabY7tZYU/eUZvO1InTc4vzyZcQ/HzZig\nkJyAPHEhJKbfXmw6lrNXA00lMtIEdADP3i7G3DT5zEtHRPOslHYgpBRGT0RC\nFoMlfjAJRA/0haLTNU86Xg9yChogqME8/YEkAuvmZI/Fkx7kQ2PFj1/9yeE6\neRnYBqe7F2nv/MlYnapfNz/QqdSDSW0CeHu4ayM07R7YRuFzNj/fmoBWKqkh\nLE53vMbuy71KiPwhkdju3PVXv7B6A32z2NuQ+SPASTeG6GH8eU2c7cj1Oeh+\neZOtPFkS3FswKItjycIv4DFycLcjH4GhATFfUoUhIr1JXxAQMCNZ0Bq+pg2k\n3jGy\r\n=pDZM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.10.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.10.5_1594750696659_0.9682687981793692", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.12.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "f3f2fc77bacc89e59ce6764daeabc1fb23e79a05", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.12.0.tgz", "fileCount": 9, "integrity": "sha512-9tD1r9RK928vxvxcoNK8/7uwT7Q2DJZP1dnJmyMAJPwOF0yr8PPwqdpyw33lUpCfrJ765bOs5XNa4KSfUDWFSw==", "signatures": [{"sig": "MEUCIHhxgl/ePq2HYvcozcqIq8k3DCB8tDV42zGPmyluw2T5AiEAwLukwriYUHGprmYUSLRl5JRBgdOpDNNgANr2cuqsGiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mQCRA9TVsSAnZWagAA4vcP/1rzWAjjOasQBC6drApb\n8OEZ6V2xXpCRnXJb0wK/lK8WIvqJqcLLorNgEM6n84KVAy/XQwP4hCLxnqQb\nWkTb5Wv5mgeLkxHArMrijFxOk2trMmVruY9ghXbqc049SVtmrRtRagCorl4j\nd1Sa+/jxxqnf234htGKnhys/L0ME+E6BepgEriScIIObyA++huTMR2CrlB/A\nYTDw9iU5Yl/k0e9Ipw0/FHIoEdtaX/6GQ/2N/GyJy7/OIlHTHFwQtu6eePGJ\nYYNl4DDyOIjpDaDdY9s6qaWsXTUnIN3VAnmUAMF7djaujtrDu+Kxn09H5oUS\nx1tDSEX1Uu5UVdPNeM5FW+OORSNBFiQlHgG0l5TFCcs0wrrqaT+usYnhWKKg\nkDi6Je5CFom1IrZdJJdK37bc36qJzLlzFtSc71ajXi7yMG645T52bePUswz0\n/16iSoSa6NOqhRz3ikhFcjITEisUJKFGAtzxD2Bn+QymkglaAxkYcmulfFRY\nz38ONpUFuIgwfhmInOPYK2YKsWsb0W0ryL5fYhaWB/f/u78iaPV9dpzPKorn\nCIQL1iWHMeYM66wJh5c+OGqt3vT58I5Q4he9bc746Blrg3vqAo6TPrZT2kpq\nigEctJO+OUhAhf6PIhZE6Vvj62n/Byt4LroLFREkb9hpqU7/1ZF+5TYMzpST\n/kI7\r\n=9ivN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-replace-supers": "^7.12.0", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.12.0_1602705808149_0.2580748346625701", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.12.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "3c45998f431edd4a9214c5f1d3ad1448a6137f6e", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.12.1.tgz", "fileCount": 9, "integrity": "sha512-hkL++rWeta/OVOBTRJc9a5Azh5mt5WgZUGAKMD8JM141YsE08K//bp1unBBieO6rUKkIPyUE0USQ30jAy3Sk1w==", "signatures": [{"sig": "MEYCIQCq82cgk5BhdRI8o1VafZ7IKVv5Vg7KZblrWtXinIIeHwIhAOINjzNCL71CmHCNgm+6OHkUuTA8RmqJ/o++lc7p0pIo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAzCRA9TVsSAnZWagAAojAP/iXs+nyqdFCZZmX4bsIO\n871SUOQmcanca5pUzPIWgVhqcTXJZvkBCkqBh7oi5/XO6ymFJnFVPgJDWyyx\n/3mcBbuiOFdfnOdTyewlOWJ0ezrHMS/HtjTvQcnKnzDKr3uI7AgqZHlNM9CD\nLPKY8OfT4yPp6aGVX0MU9m8aAyIxjszkimPqQqpW45AXlyWTCg3PT7yVJM1k\neyvEaq/QY+ll8fEz2uyZO+dKP1qSRIYhBSS8b/qIYsAkDU+7C1lDj7aMJjt/\nN+UHVitTlkvmFJGsHaTXRsp9gbZxjj7ZS3eL7H2efsb1ya+ihjmFwnCobKVs\nYto7zeXccPHBQ7Bwik6o471dFE+bY2NIIlTwtkfD8uKmmOOU9KdenqxgoYSi\nZ3aPAS7xf5if4cTDA53aaE+mx0yTcLM6PWQJxc8F8lidQF5ASP3zkzDqrfe9\nooW0fWTGqGP/ZARPCUG2Snq4LbCDUjOQEF1QEMpucS/a/F+u4nVpHVkxp8aa\nCg8WHMgX7HIlsnUBsXkyBjkkBmS39k3tWlxi0MsnAoOrOADxyGTvLJuGcmZL\nU8kfoBp1Z7aoDRzQSd6JboSwJ6oIc2sscQm1P7to3bTEOFs2vYrQRI1GYWSH\n/8CPK5pOwHZA69EqazkFM13BQ+3RoRuv03ZrJYRDpt+oKxi/hWLcH7UoW7K7\nbz/+\r\n=rhPT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.10.4", "@babel/helper-replace-supers": "^7.12.1", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.12.1_1602801714664_0.5961821911242904", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-create-class-features-plugin", "version": "7.12.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0f1707c2eec1a4604f2a22a6fb209854ef2a399a", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.12.13.tgz", "fileCount": 9, "integrity": "sha512-Vs/e9wv7rakKYeywsmEBSRC9KtmE7Px+YBlESekLeJOF0zbGUicGfXSNi3o+tfXSNS48U/7K9mIOOCR79Cl3+Q==", "signatures": [{"sig": "MEQCIEwjInHzESg5aeu6vBWiPsSqK9jVYMsF1UA2p2ezewqLAiB9berbV/CKpZobbwfZ4VWbHOA+vreanonIfvWGukEsUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfheCRA9TVsSAnZWagAAJlwP/3jQYFCgXkWCrbDFz3dM\n5fPFrBwZvaoF8b1f41TaNCHGm6hLX4QIS/rcbaE//Gjf2v5L2Hgv8pTJ7klw\nZS/FUY2q4X5cwcpvycqOf+lVqvUmSf0HlYWz4Pcr8TzM2QTQF7Bu4t3LvP5P\nx1AYWH4d2+0GXXymObORlmwB210WON4HYgLJHsOuMPoZj5VfaMllExfCSsRw\n5nRBvW3I+AE9QOtepjcbLTaWaPWASu65sF06j12XkcrRodDkn9AGd9bJv2br\nQ5HUw69ssc8RSeiq7mf+EG8qMbow2yGNOK+E0MNRlrboqfe2pcwL83+pz8Q1\ni3wcFipwTQAHQ5AntamkBf6Gx3U7clpjmX7lRIGNvOZk1CcMOGr+dXX8Q5Oj\nDBDsSzYHNXTZoO1dK3mJn0MAqzcEsiYvgLoSxsAgOvb4+oMSrrw1tZDHDXbJ\nXo4KP9UYepcn/wKjygXOa/iNGwElEGYAz0RQ3kz512RRNIru35a3N39QkhV6\nrFBbsPmHgiVEfEEpIxBqq2vxI6pt5xXpwGrmMxXTVaMsUMW8TLPZ+ttl7w0u\nWkrJZRNr385zW3POrPMd1vJvv7WvSREm1XdbEzLjqbQb5VAe1XC11A2fHHbY\nW7bA7Tv6QJI12GtPc2HZeJ9h8z5Iog+u5FQm/gHMy0OrjQu+UnCwH19tQUoL\nkk7e\r\n=oEvL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.12.13_1612314717589_0.6073806864973825", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/helper-create-class-features-plugin", "version": "7.12.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "955d5099fd093e5afb05542190f8022105082c61", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.12.16.tgz", "fileCount": 8, "integrity": "sha512-KbSEj8l9zYkMVHpQqM3wJNxS1d9h3U9vm/uE5tpjMbaj3lTp+0noe3KPsV5dSD9jxKnf9jO9Ip9FX5PKNZCKow==", "signatures": [{"sig": "MEUCIQCYKs+aGK0cxPRJmJ4PznQ4f+PpZGt7uvBbHW0TU8xESQIgR8XAZhvuBLSqnVBB+7DlVFKQ5zjMMn2x/T2t0U62Xgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPtCRA9TVsSAnZWagAARuAQAI7fqykNk9v0E1HsqIVB\nuBmqgjExyHhDzPRyPwnJmdHFci+ugQzGcslu0KG3Y3O7DTrJ2s90OqzELAHN\nP1VKqgdUkxx8oNqjUOGu61af6LbF5OTJXxIrn+s49VHuN/b95r26JcItu4Y7\nN+vBk1G/6RA/GSlT8DaxQPJVPs6/JMigaq/pcBdPvoS3ukUWQ7jRgQ0qw6rs\nCIRdBeW+QZjtPFwcQdSnaLPMyC28obN18S9czID+Q2iathOkLIfP2ODzpSZF\ndmJSCz97k4HYzowLX0xKNEpOPAnrAs5owKwGNYD3ve2NEzGm21FTeMgKYjnX\nPB9+lvu/wR9eB7Lb4jpgGpw4ODfy3N3DFSFowQHUFKZKt8GSQ9clz0G34WL2\noaDLjcE2/J7R6dcCKOvbbLbTl2bjjCs5BGQt2Q4vAHj8prCA9aubms/AIFQX\nNUfDoEn/kTgkSSmjMTLOfILdxoboKHlirxHDskGPG0N+4ONSaqIrHcxvUbqd\nAh0QvtDgwMoeS4Hy37fbBTnFu3wM19d2rBqENTQFYebRJWcU3+xz9am8apag\npLSvrhTVq34r7HKKEkT3ATQ3w2p5IGrGAw/lr5WEwKnGiYDyFS+Z7kaW/gxh\nm8fc3bwrHAkXjvziA2tMNCLZoBvcPbiywM8MBMvxo/3/CxkbFIPVlxduVI5n\nfxdm\r\n=NPO/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.12.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.12.16_1613083629167_0.668036654872685", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/helper-create-class-features-plugin", "version": "7.12.17", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "704b69c8a78d03fb1c5fcc2e7b593f8a65628944", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.12.17.tgz", "fileCount": 9, "integrity": "sha512-I/nurmTxIxHV0M+rIpfQBF1oN342+yvl2kwZUrQuOClMamHF1w5tknfZubgNOLRoA73SzBFAdFcpb4M9HwOeWQ==", "signatures": [{"sig": "MEQCIBTkn7bwAb/Ry/cv3p2hw17vChtp9w3TUspKzP/SrtZ/AiAnAtfemN1g4PAxGjJ+c7QudNYZfLjMmnlNEKEqikz2bA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoQJCRA9TVsSAnZWagAAOEcP/1w/SFpnaod+1bBomrr0\nSIbRLJTQiGuejETQAn15jtFx/vjOSZ6OrICsG/atoE9l4CK+b8uEFBQVqwlY\nsbZN2G155PNCCldX+5OdkYHhz6WK1C7o03Ct/fvDNJ7iK84lG2AnqYQQ3MCl\nDl0Al70mXmIeOVVs6IUW9IK6OfHh/62t9Lq5lqVmZGx7NRbwWPn8sPZRUeEK\nIj5FQGLGiDK4MXvSgt2muZtK6wBWpr1WHihYBUlc8MdeY3Hdp9tM1bsPKHF8\nHHN/1cilRe8Up7ikTdJbQA3EukDrvDkQDJKUa8XPejF5s+KOv4XkfGL1z0LE\nZYPTommQ+3yVPJo6D/2fUraob06UPxXJOqL6Ku+hRMQfZe4vE+4mOCc2b790\nnr40/N7woB1B+AOfxNU3veCA+PvygJfQIX5r5gAIYTUpeqsd+4XyPFJAZ01s\njp89/XudwYmflftM3jrnlxh/J7S7h8h0N3Yxb0XFj/WaoH5+lQfw/32UD3y7\nRBx8ukuev4ekVgbhinVRVqZxPzKzmVFxuvIeStU86cuUN7h8AaGKgF294YQQ\nVH40uMadX+XTVy1T7EVgOcZurIfhL+/C9ev2gNmLq1B/pdVgwVzgQyLMYIC3\nGCmtPy5EUIjD4e/Z5ie+acEISQnSmjWTEv+3wIp2XuDOSbd6h2CnbdtoiPG1\nf8lD\r\n=U7Lo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.12.17_1613661193390_0.26643743068520886", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.13.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "28d04ad9cfbd1ed1d8b988c9ea7b945263365846", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.13.0.tgz", "fileCount": 9, "integrity": "sha512-twwzhthM4/+6o9766AW2ZBHpIHPSGrPGk1+WfHiu13u/lBnggXGNYCpeAyVfNwGDKfkhEDp+WOD/xafoJ2iLjA==", "signatures": [{"sig": "MEUCIEzqGZERN7cSgFc5pV4Vwkoa3yJNlMdtqubXi/bfVQqfAiEA+gQVK6rppP5JCEynQBerkdZfjDSSlRGq/D4iKi73olo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUrCRA9TVsSAnZWagAAQCoP/0GOD1MX3i6fjS090nbq\n26sh12p6zlHdYLRUkuVLPrRXoUuroM2jlGWH1c5rqsrVzuP8ivifw0YBE7vr\nVcOGslDPCDlvg7kq5jx511YBBqDkEqmgMtfh0NHkzagFsip0jMTqCqe8NqQG\nuTGZThzadl52tSdVvHndr74TdY6r5zDdqCNl8slqrJ0fTwVOuIjoifMQe14m\npkuc2jycT7CsdNGROGRQ3Ab4sYgNMsNP2udQMjS5ZdJfuwIG/Z88gBAyqr1L\nyzKyauiUU3IR0DxOFK6/usR8+I9/InEoV6PgDMElXa830OtnQkNO/dDnT+Nn\nGi7vhPiyh4yUbE3qiXm/exJBqyZeYKoi9H6GX6S7vTqdT4WGSC2zeCZ/toFy\nsoa6W1X03EVQeKprNcGxRbpf9cmIvQqE471qxwVHjXt3+xmjqRQH9MD3z2dV\ndBxP4lvYKJaHRH0pSQj6TZtZ3cp+Jdk5Am4GAqvv+dmpygUzrWfV0HdNZbBz\ngjACwgtVb4KNYW5HHIagQxJy5I9U8kaHTwDFP3lbG1QvMzkAy+V2yObDsfjo\ngZojwwnxky0ObJpEDPj5ZAMb2Da/QZHGA6/cJZN/SPA8QCVvIq5rTW9D4OeU\n/dhy/t8N1LKFZ7hCIERLwHc9vYdU+fJ+xihPLHjBq8NFXVLeEcLpCkSIXawT\n0X/c\r\n=O3Ov\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.0", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.13.0_1614034218559_0.6870249078516808", "host": "s3://npm-registry-packages"}}, "7.13.8": {"name": "@babel/helper-create-class-features-plugin", "version": "7.13.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.13.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0367bd0a7505156ce018ca464f7ac91ba58c1a04", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.13.8.tgz", "fileCount": 9, "integrity": "sha512-qioaRrKHQbn4hkRKDHbnuQ6kAxmmOF+kzKGnIfxPK4j2rckSJCpKzr/SSTlohSCiE3uAQpNDJ9FIh4baeE8W+w==", "signatures": [{"sig": "MEUCIQDJUXIADZGzXPXyaO5UarHOXSnDBiX9oW63BmWvCIcxjAIgT4G2pLfBaEA2KjNlxFNf39TKqIX/5yZYJ1ACC0f/hNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOYaFCRA9TVsSAnZWagAAdy0P/jm6tUnd+k5x8cq68FCV\nAm7ShhwvR0iI/E6YRlXLDRGmMH6RjZ9Xy/KC8WUEEFEmqYKtu3lq07Gfizt4\n/8Ui5cK4MHb/h3VExZMolziv7lZGzYSJp8wst+X34oq/rR0lqWEPpePNkolH\n7xMj4/GwGqCYYb5gLhmvb+cFKe3muBylXyPxE0wdTzw4FY24ckKd0V/Br1md\n/pc7OiiRf+KPq2RX/9NBAbekbVkRp1T8inAfysjLkyIQFp9997a/TYxyLRD5\njS8rCYHTu3nNuwOy6VuvslQd5pY+TteIiQUcYw6uk3uk2MvuAJo65QDwD90l\nUJSlsqn6UhWiPC15MkTrCiZqJnSM6FPfnpfxu6dqG3Hku6RuMnS1DmAzlBGj\nrGHiuZe5vSBpwuTYcIpiNu+nWeqfYAPz0oNEldJs2hs4j0Mgji0GlaTWJHO1\nSM3QCu47vLxXKjzrRitN5nsnaLszjntgEgZ5dmEgh7x6Sz7tTcrlmQbf3ZJC\nYQkXTAsrmhBS2VC38F0pqqZgmjDtpHSOr3c1Eu1h+2p9P6NK9TRRyYe5c94y\nev2vagx1muLq6+qWueSbdDsuIn5PujYvSEN6g3CEbEapf/YiGtv9v1Ni3Ik9\nF7BUp7xVm80goZFLvEtYd833+6VQpyHbM2ZYAydbLER8oYN3MP4xsmIYXSg2\nQIYl\r\n=dQqc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.0", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.8", "@babel/preset-env": "7.13.8", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.13.8_1614382725467_0.4614211729294535", "host": "s3://npm-registry-packages"}}, "7.13.10": {"name": "@babel/helper-create-class-features-plugin", "version": "7.13.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.13.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "073b2bbb925a097643c6fc5770e5f13394e887c9", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.13.10.tgz", "fileCount": 9, "integrity": "sha512-YV7r2YxdTUaw84EwNkyrRke/TJHR/UXGiyvACRqvdVJ2/syV2rQuJNnaRLSuYiop8cMRXOgseTGoJCWX0q2fFg==", "signatures": [{"sig": "MEUCIQCbUEjlmyuh9t/gei5CxVND73yhsmr3OrqbVrhqhYxPWwIgGZowLN04L/FuxLzztG6aAGfSdAzjT0uPX9JL53aTDxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRqbYCRA9TVsSAnZWagAAFjUP/jjQHt4A/9vLJnAVjTp5\n9YaCyuV86m3xCJL8p6erXxuGP7Tgha2BrtFcXEcVT1u/RarNiPvI/Q7gvYAl\n4bRzVc7pAg7Rmxp4j2o6MmOAbrBLKwO8pASF7HCWSpJPPpCt8FAc1lYLZCId\nKMbLH/HL6ZrotxQbuIQY63DrnPJcEGD4FadNKREnqKqIZcdHOai2pmNJj7g+\n/5GopS7gt8ANBPaHUhUl0InNpS52CJSueH3NvTYbnPK2om0ZNRqSu9iaXLdO\nc4K0eNYleqxDrf7zJ5JJ8Z/5R7nQRIVPERL2hmbYm83xdnoBQt1a8pvCHwfU\n2HFtK8AzRlR73N6l1ubTDh+NZY7AUrwGL5nk+DlSyehN4ewYAXg2XTIu14XK\n/+58a12a8Ui+ITb9iek75pb+f5bh//e2hkkq5P+jAFmGjzGLgan8KAP8E5Go\ngV+DsKQRi/Udc0aOrB3jFImkRL5d5MGhDHIO9D9GBJRjg0SRFipzBRMy/EAD\nwNLxyz5YO/+nmX6AsQ7CEicQCDg55uc+dhDFPxvo1qsVGCrX8CChIuB8kpya\njbzuUCyx1P13RThdx7YRqeqrLeqFidABE78PwwISm5tck8Zdd1llCQMlXqkn\n7hVFgpHC8iW8fFkTpkpq+cbEHoNTJFNZ4WQMRiVYiDNdNuMzX5cbLqP87n5m\nryaf\r\n=COOv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.0", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.10", "@babel/preset-env": "7.13.10", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.13.10_1615242967785_0.4941593440279728", "host": "s3://npm-registry-packages"}}, "7.13.11": {"name": "@babel/helper-create-class-features-plugin", "version": "7.13.11", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.13.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "30d30a005bca2c953f5653fc25091a492177f4f6", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.13.11.tgz", "fileCount": 9, "integrity": "sha512-ays0I7XYq9xbjCSvT+EvysLgfc3tOkwCULHjrnscGT3A9qD4sk3wXnJ3of0MAWsWGjdinFvajHU2smYuqXKMrw==", "signatures": [{"sig": "MEYCIQCy/7nfYPPSABEbyi0XIZPOYQzSm7GFpFaCO3ppBqi1NQIhAKH66xso8IxqWNNYmaM7FWT7htSI8GVDlyJrJJCRcQeR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTyx6CRA9TVsSAnZWagAAiSMQAJxN1zXupzYir0deWdcQ\nDNgsdhTI13cSODpVFMt1xNmJuYCEgH6hujjiICgqZ6/xcj8Y3N+Ahq5HvSg/\n4L1Nwjb+xT/K9C/f2wYmrOvFAMdfLf5T/3y4xOc6/i4G+vMWt6Y2W8a+wFgm\nIVXCEizYtBHK4hXzO2xR4uQPkwEyinHyo6zvtTlAgCw4UGXMzu6Qy0uWkdvI\nDdTQS+diftZEborwD2jPpzIh4+7AQqrpwV1vAHoM+bUVkRSkWphmK8LXLKJh\nodepnc12Nv0wfhaqx2xt2RqEE+XIw8EsnnBt7x028SpqyUY2yX5x6mQcmK+N\nK4HwRGIHoAFl5zunpeDZ+efVCoAOfJxT3X864QGTyOO0NL6y7boKoZHCpw3G\nA61hY25K+Iuj6+qD+ltW90UCwUvpwiSPZxxQnb3vOTnNAQHTKYKVX5CLOLlf\n7XajQWRCz4IbG2VAzNvqLOuwz6OuDtue8ilwHzGKojF9QpXamuXFJpjSqtk2\nqgxx+u5A4Pe2zDmvwq2gwmz0tbJsz7Rhd+fz2PC36m2C0NNlHgJFqWbwRwv+\n+FhDg/fJM3eFYmr2Ja29Kr0Ofw9loVg/FlZeUUN9vulon6IkoyS33FtAhZCW\nn6VFnzW5tVXgccJKH3XcKaZu4ocLdQ1bgbzsVpLVQLHzrmjrFlne1sz2ouas\nxt97\r\n=FL+k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.0", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.10", "@babel/preset-env": "7.13.10", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.13.11_1615801465901_0.8823966996040349", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "38367d3dab125b12f94273de418f4df23a11a15e", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.0.tgz", "fileCount": 9, "integrity": "sha512-6pXDPguA5zC40Y8oI5mqr+jEUpjMJonKvknvA+vD8CYDz5uuXEwWBK8sRAsE/t3gfb1k15AQb9RhwpscC4nUJQ==", "signatures": [{"sig": "MEUCIQDgrdW+1K6Rr18fFBHxnpyW/Mv9ZtVn9MlqPW58JdWPugIgX4jhjEJ6EXd6apSOpMkSE4jvvDC2tCZIwuhBBj5XmyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKRCRA9TVsSAnZWagAAnEkP/337HFrl8oFG9tfXjpJ6\no95PPaKhXsOrhvQUdxf4JW5o1Ll2rZtFhGEtgJE0VFNC6+EUa3pHZktnirob\n325eMSOACiErmfp+PzQz6Z+UGtdbJqPu2BNggQy8B9oUzXFLdYC3UeKVDKE5\numPdnnRix/+5mQbV+rCGY4yAXfmMYV7xXthZxqEAO7ZXYVONri5ZKBor3kbQ\nPl66J748IVh22L7lF9rh+StV8XuSoPoo+W4Y5CJTN2JVG9ZEq8wu9yMPUY8w\nhGcrHmTe5Ej+03demsauZ08oLqEB6vgv3lWexwrcLsDcCEy5aMzVg4//heg4\nNlDdXbJ/hTbOo2k+ydhH/ldN6A2ZDnxkxj+IXNMqw5eckvDKMAZ6FMzAZZsg\nHT8hTpAOW5ObE8qwdFiQXWRtSXCMu66nka9XL8xFVv4aNMZptTzksZIjNttH\naQpLq+Yj0uyF8M0gXNLqQnYWZUpPELoPugj0Y84IsoBxjVPfrg/ll7Nj7Lw/\n25bEuVptWgSOLTyXxVSBoMU6K1XVe/ASAF14mXtZxptkQEsd379t6eqvH0pI\n2zym1zL90E7k1sBv3VkZLuMEUlfp4luVMTZB/9pNSbGXOHBRVKylcQCmWf/z\n2XcxYaXEKjYFCRqaHTlBRSu1GYf3IAXZpOjiWo2rnnv4X5yWqEdEPix2YKWE\n2tyh\r\n=Egyc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.0", "@babel/preset-env": "7.14.0", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.0_1619726993160_0.4923459182183425", "host": "s3://npm-registry-packages"}}, "7.14.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1fe11b376f3c41650ad9fedc665b0068722ea76c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.1.tgz", "fileCount": 9, "integrity": "sha512-r8rsUahG4ywm0QpGcCrLaUSOuNAISR3IZCg4Fx05Ozq31aCUrQsTLH6KPxy0N5ULoQ4Sn9qjNdGNtbPWAC6hYg==", "signatures": [{"sig": "MEYCIQDuC2GBouFPssDKOsqadp+9emXPm/+57vIgXqzOUmG23QIhANFMNAFpA5zhv1DjpKndWOjgYPgL/GPVblc6KdknHCSR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkKmvCRA9TVsSAnZWagAAJDoP/16ytcyUJYJIJkHmjEzi\naRv/k9+FcuZoEMz5Wano2q7Jp72bp3FvXY2d7ZlWKhhPLHYb4tVlWbwWglFP\ni5cntnPFjZQj2IrRfsY114UpGbUy/CoMc/N68TxUC0RN6LtiKA23CM5hsWaL\naDTrpgJ40pTvk3AvKpyOoWp31WXQ6KhnBkj4Fsv1B03jOv90YKXBmemt3bMm\nenvmCzGjMM6Az6DKSRwJRs27X9d5pvQoEXvpI7Um+Fq5gIz2NktREETIvulB\nQdOZwdbPGT/B2ARP1mgPVUxEOAODqsr5qnYPjHbxXWboMVbsbeX+6EzxbZFT\neEUo8r9t6WCdqNCKpY5kPC9+gc7ohHbwdgIoTmK6ln+jK/Zg5zX6G4kVnjjp\naGN3InEo+cBwvrkHZmUuP1grWAKq8tquGD8nr/yWB1xpC2NvmDgrJcchcZCX\nSsSrp2iPcV5icNEVC4oCxTnx9quFqSIThwBkTH5KpiU79Iz24gmcWfCm74fy\njwa7ieegk7Vsf86wFNevonH0yZxh5H6dSU6sSehUhGsPMZrHobhXHh7wYi7j\ngqBg73TthN8YzqwJ2NUid3ZsUgP3ESBQOvX2+uUoF7qCkwF0SMIqLh57Nq5k\nuO8xCK/bRhoSf0EPZ9c0B1bvgafVNDNSV1cJsFF9wLnsSbw3Gg0ySwFExcMy\nrYCO\r\n=3UFW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.12.13", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.0", "@babel/preset-env": "7.14.1", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.1_1620093359318_0.02927043354382186", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4e455b0329af29c2d3ad254b5dd5aed34595385d", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.2.tgz", "fileCount": 9, "integrity": "sha512-6YctwVsmlkchxfGUogvVrrhzyD3grFJyluj5JgDlQrwfMLJSt5tdAzFZfPf4H2Xoi5YLcQ6BxfJlaOBHuctyIw==", "signatures": [{"sig": "MEUCIQDEiE03PTFE30+1nEhvd21p/hq015f7gltNZrpHMRmDpAIgH9cNM/OH4k+TuGvjiKbtv3R5SRpq+Jgxbuk6PqujKio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47681, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvQCRA9TVsSAnZWagAARb8QAIT2FQ/sk7dsywUzY+go\nK9leD0uiJodzz3o7s4+YxLm9ygHyeQDhvZ6Hp/aTxEHOcYYGwJw75XVahpC+\n2Hj46v3VeLB32zgiXSiT2W6gP4xYUDqs9N//Z5DdlOhDhg60A+GDN+JxcX+v\ncJh/gBGi1rLHz4jvXDmPV49efxNzJljv9aUm/ViAbshjfBZJvgmel3g7/HG9\nOsvveZnCKYjwt/Jn4i3NM8UbMd8rb4bYGYCajkCy9ZoeK2hVAX90CJRYr2fj\nFBmL9IoJHD8w62Op81xoa0MxhV91CtJCC1sk7enDyHj2M7CV2vjZw4BrkQgM\npsokqgE+EPiH+tM2t7yqEF5RTOQzKUjSRYyVwrG/4hL0l6cpML17EbjhCaSz\nwOowrhdOIrLnHa9nCJ5dv4cs2g9VVcKzZmW+SZa/pu75QtOIvo2vSYbEPgWZ\n+/iy6IN7lcjrPtpxKQf7USco3B2I+wy0zoiafhjMav+ZV0aDqun7Nf2dwoin\nIRxFD0OkvE/ITwc2ey5LIXZVm9xE/sSKEYwwRhMo3dfwDRsNU2jbU34uKpJ+\nFVGeJpsI/Vbe7Rf5GGuNONddzE5eYFCyBtavIpiJOy2pW8O2Rep6RbHMMpTh\nkiDSWn5zLp5IH/kE40UPXaCMViXON6AKcPwl8X6nvovdpGzYp57JT5nKEajs\n5Y+c\r\n=B9lE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.2", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.2", "@babel/preset-env": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.2_1620839376392_0.3677937229125807", "host": "s3://npm-registry-packages"}}, "7.14.3": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "832111bcf4f57ca57a4c5b1a000fc125abc6554a", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.3.tgz", "fileCount": 9, "integrity": "sha512-BnEfi5+6J2Lte9LeiL6TxLWdIlEv9Woacc1qXzXBgbikcOzMRM2Oya5XGg/f/ngotv1ej2A/b+3iJH8wbS1+lQ==", "signatures": [{"sig": "MEYCIQDSIaD4pj2NoKeDoEvAmm3IBSp1hT2/gY/WPCAzg0Ja2wIhAPy6dYyiea6tBl3spgT2uS2K2Q75d76QOsObHrEVgAk2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotWxCRA9TVsSAnZWagAAotEP/ikPDFxTPlCOiFz4JC/S\nq6ivVBgZzmALxdsvoSIB5Q341WYWsq7HzQw+FW2IOaSGCKnlf+b7p5DLXUeJ\nx/hQiW/zk2kv9qIe+j4Hpe5ywGx7ymDbk0FlKUDBWLMwEokEf0wluKF21Q12\n+NciDVWxPamZ5WJyQLVqCNV8wQIHQ+PyVBPI4NzvZf0SRD+uKw3zwJWjHIDX\njCNZ5iAWxCK1c0guEYFWxKWx2np/0oT2TL3kVsCI/AlgHGAPrAatF01FBwlI\nR59dVulffLHKWnvPLBuUVJRwOZiP9cqKsS01TeWOTHT7kqVIKylDYgYp65Y5\n3chftvLHqfT6FGIPGUYCe91tITWYt00Q4G7YYNIvhC2mHTLniD3hAb3d/I0d\n6K6xvWKvowjLc+34VN+0gZTEVNxzQ9c0PRYkgmeDAJoXi0jyOF4Bw+zUqIle\nYFjYvZ0RAFPXtEJOCCzDU+OysBPVvCTwgc6zre9+Gh5PVj5Xl4FEhUQiikQI\nh9uyCBJk8V+0hwO3Qr5wZcnbu8ictty4ffyv+WETIW1C9WNmOGc+V6IC3kJk\nrE6+JPUCM6OqgjCJuiw0ZieLzD2+uIh1niAwH2ymF7CIkEo7Cwhv8fPeBkUD\nOGgf8hO9t55iqs1V8JUfRfW1CcDqvfwBPSdQCSZbGBYUREq0dVneAaLIB1B+\n3H++\r\n=qYoQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.2", "@babel/helper-replace-supers": "^7.14.3", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/preset-env": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.3_1621284273237_0.2982538959167045", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "abf888d836a441abee783c75229279748705dc42", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.4.tgz", "fileCount": 9, "integrity": "sha512-idr3pthFlDCpV+p/rMgGLGYIVtazeatrSOQk8YzO2pAepIjQhCN3myeihVg58ax2bbbGK9PUE1reFi7axOYIOw==", "signatures": [{"sig": "MEQCIFbKLIVg/QIzFeBGycUwJGn1BxLqrzkRL3Q7QYS+gjnaAiBezvRYk7ZuykQK9hVR0me7hNSe9uRDczvQj8K4VdOTHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGWCRA9TVsSAnZWagAAx0IP/AqXmpNKSrbZwRkFajqw\nx2iRqvTJy+OyX9Ys7Zy4a/S1O2tEtbV2Ts0+pDpJ1H0ATR+ypUSgojh61d90\ncgQN1Me7e5l9i0pDr2IcPX5NVrdEGFqcw3e4FD5GfDEcfEKHAp7VYKCPPrnK\nS5FWLqyh76qpNFGdcb6d0oTAc3unp+Iz2fFhIO2dE0zo6qZ61TKJ7J/a18dg\nZnd5uRv1VLeb0+IgxntEVNj6gHmfYXm6+B3mk8Vgl/OzOPLqvbJUGTywSBa0\noSR/E8RQOISuUaD8yiT9H0FI4A3U0W25EddaMZ9BNfey4638c6keDmLPBMcA\nEFWIHGS+2VqUQd7ailrIQD1wpVE70Odr36TWVTvrH1q9SgZEsA5DdiS+eM8l\nf7fFxLeZ2GIKI61cIuO4UfZl1cNIcs+HVxRvKo7G89OfXeUxykl5w/jxBai6\nnZJisaA7OeMT01vi2HPkyrqVcGXXg9QL6DFGMRwnylYHEf3lGU6E0fIUVD+O\ni/bh3tyqCHGEixYrt8qqawFKXgZB5kr2yWR4aQ07M2iyjsfnDGfPrykvPzCG\ntDZPpIRbn0TfE2/tG3VUnpBToZU8Dw6jMWfg8VraVRTZxKbkOuCKJBrOPZYt\nVsP2xrw9tUXWXUx2XY6uM7VrOtfPn15mXrcHA/EJXKNSkaKz1UwgwBXCUlri\nH/2G\r\n=qa2/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.2", "@babel/helper-replace-supers": "^7.14.4", "@babel/helper-annotate-as-pure": "^7.12.13", "@babel/helper-optimise-call-expression": "^7.12.13", "@babel/helper-split-export-declaration": "^7.12.13", "@babel/helper-member-expression-to-functions": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/preset-env": "7.14.4", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.4_1622221206152_0.9597738128858833", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8842ec495516dd1ed8f6c572be92ba78b1e9beef", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.5.tgz", "fileCount": 9, "integrity": "sha512-Uq9z2e7ZtcnDMirRqAGLRaLwJn+Lrh388v5ETrR3pALJnElVh2zqQmdbz4W2RUJYohAPh2mtyPUgyMHMzXMncQ==", "signatures": [{"sig": "MEUCIQC+YYrdRSZX5hW7uzf5eI/5Hyl4BPLFXWv7VuQtTBc7QwIgBo7Tyw/yTXL2LIvqjRQCuvjg8pW6B3QbMgKgDxCXmjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsFCRA9TVsSAnZWagAAt3QP/3/xpTa6AlX6HQZtaUbb\nBu6NuBpksxSldwGl/cxnDdB6JnZQ9K/MrALoyVarz+CS9UqB3So2n5oykWB6\nJCT59G1w+ioK1BLpkC2ybfoa7pohBCqjgFtr6IkY1PYd0ceJO0BY3slWymlU\n1INjBn1YZ5QglBtBUeQGush5QVjocOUk0n4rl6u54yKKH9JO5mV6UEGjLXz9\nG8FWFsZ8fFV8E71MKAmgtEjmc5nhEQXDM7DeTn2skaX2C4FPH7oRktJediPG\n3FsdIAnSQPHVRqWx84Drbji0Bicyx+Qcooj/XzAQqvFvZd/dGnVute3jai3y\nwtOoxziCP9q5M1h6Dmyolpe2sBINh/fu+sFORBV33X27S4ldLRRnDU9Szdg5\nNwKAYz1vFD/EBFd69ZIs7Alea113e6dq4lhDMN4HZcFC9Yu/LLmL8FCzlRjv\nX2GAFVJDyD2pFbnJyrKTDEWwTCA+mcYOWf4skeKoL4F5SSb6IUjAgWtvXe72\nldT8SNZPJOW1Yhl//eu2mlJQ0lnoTqZ/68RHrdFnVkwvRwGxbaI/VwEP3raN\nnHSCOSxHpV/5GSvqOfJ+v/6Dz0RmT8qDXXzIULk7vOwU2xESl1lvM71IUMgx\nHg+4yf5VhgkBGnh5XunhsUuIeGnZMtLmSUiJ00bDlTN2mHct3nYhPAhe/CQ3\nE9jK\r\n=xF12\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/preset-env": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.5_1623280389796_0.9951092207251355", "host": "s3://npm-registry-packages"}}, "7.14.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f114469b6c06f8b5c59c6c4e74621f5085362542", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.6.tgz", "fileCount": 9, "integrity": "sha512-Z6gsfGofTxH/+LQXqYEK45kxmcensbzmk/oi8DmaQytlQCgqNZt9XQF8iqlI/SeXWVjaMNxvYvzaYw+kh42mDg==", "signatures": [{"sig": "MEUCIQC/MMk+dAhVTWDxbV4rpT0olm+p64mHo1jAHKtJyxlnfQIgEYRanqU9xO73pOIy02aJ32dpdx2XXenqwJJ4LwKcUDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx9CqCRA9TVsSAnZWagAAqwgP+wddd8kIELjUGa9fEhtF\n2yx/GPJTbHqimIWlGQTJoGznksE01Gp/snVDxmeHCte8Wejh8OBYillq8zDg\nj9UmFwcLm20xNu2Gcv7xdYmdRFRNfYF/LjPu4cS9pTZY9EBgZP/jhMzKbUot\n1DsJHZ3da1rSwI9MdKKrpLlirL8VdUjXi02tPe4hrsV8BOS6pL0nak4nLfxj\nxMBacrvA4NevWNtV2sIuGM0t8Hu5XNcwy9+4MutvKsCsgkhr9QoyL5aGlT5+\nPSF06KMowX/o5MD+TWZp4XdLM3zkLqiMUBopH5ZquSX4hpOc6cLCYu/fTBKT\n5WJ4gbWwhr7s90PB0DMA5nAGPdtCEjISs0yPVgVngQLvH8Xpmyc+cihiTFdz\n9Ntf/v14K+AF4MNMeXMIYqxc8HHYuWygdWNDCpuJcF9C8cwD1r/7reaJHU06\n0f1b0IJ2ObpXggWJyTV1deGP0jmLZ6SWjXEgHO9tdGMBPTYJUTqz71eqEK0j\nPThbp7ZPBhcuJbO5uzT91gjSpRruQBWV/KnBXLyKwrMV1eMEh4r44uNXKv1u\nwYnXE+DDxUxRgEEySLo38ctAa0lKOhCxfn9Y9jD6K2C4yOg0qBKeriC6jYOM\n6bzbMA/ciDpjSwa0t/kOJDL7E+VZ9o1xYyQ09HhyJuYGNVa2WKuAxFIs2Wb9\nnHcW\r\n=mYTM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.6", "@babel/preset-env": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.6_1623707817929_0.8083893250227028", "host": "s3://npm-registry-packages"}}, "7.14.8": {"name": "@babel/helper-create-class-features-plugin", "version": "7.14.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.14.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a6f8c3de208b1e5629424a9a63567f56501955fc", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.8.tgz", "fileCount": 9, "integrity": "sha512-bpYvH8zJBWzeqi1o+co8qOrw+EXzQ/0c74gVmY205AWXy9nifHrOg77y+1zwxX5lXE7Icq4sPlSQ4O2kWBrteQ==", "signatures": [{"sig": "MEQCIAOk6VxiImWAxNbT6zkPjIl6uoejtWNWgF18lBf9ghulAiBS/RyIAYvxsRZJ+t4Qk509Kn6QL2sIF6qxu336DssIUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w+6CRA9TVsSAnZWagAAozAP/jh5cuofMg470sp3nMJl\nVEGL9d2V87TwiVD1Uzm7WKmduwHQ655G70lRbILk5746KCmZuje0WyBjfSSD\nqtzkVLSiVRpaYfrRTKEfAMbAMBBTqK7w5Q1HkzFVS//evkz/Ub/NXxHjK1wE\nZaUss3+rABHTu0+uibZGyiLxI29QO51Zy6O/kVdfkiVP4HwPhJKDaOIanAIw\nNhE4yiDXSk2LcH36pUdtoooRYvaoOMrsq74GrVWX11TytwaV1SE1NBI+d9eT\nl6PdOxJkepzMNRP6GzNILwfIOZ+8mEnlnYP0+6I6ObI6PLng8cfSiGrQ7VCF\nviAF1+FjaS9btX20zNaI/wjpqcCoUQyypOPgyN5aZ+Twl/1xTuAHpWPI/IQn\nnVfp2aoJdFxqHrk9+oy6A3Bn1IEqcPX+ob3L2qZqaxLOZP7OOKG9XxaFJSrl\nTAZHDZvNSqPDdKPOkDqTSc2w1l3mjNBDodRatm8Q0ZDoVQet9Xe+LZ/crZPo\n96XHe4ibkHs1uhx8goULFu2ZlqYFBS+Q1BJVaJ5tsiAu8x7fBRScVHUrhixr\noo5e4rQ9Lay7hLd3FYTPVq3+cI/IGbkkNKkpfPboKwHoU4DR6XiaCYI3kQnk\nufbMmNYrHkUrhb3r58kh0QmeaA8UD3rAgiyja7Pzo3BTX/N1Ucm/EBX/F9YE\nQOjw\r\n=3tRb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.14.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.8", "@babel/preset-env": "7.14.8", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.14.8_1626804154833_0.1359779160108685", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.15.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c9a137a4d137b2d0e2c649acf536d7ba1a76c0f7", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.0.tgz", "fileCount": 9, "integrity": "sha512-MdmDXgvTIi4heDVX/e9EFfeGpugqm9fobBVg/iioE8kueXrOHdRDe36FAY7SnE9xXLVeYCoJR/gdrBEIHRC83Q==", "signatures": [{"sig": "MEYCIQDHUrykf5Xi2RlSilnBk9payQlFbhvpdZbd3aNj/ixtKAIhAMIHNE9BKD7ftRgeqQ/3bGcPStZu50V2uquy4ucSsgpr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLoCRA9TVsSAnZWagAAG5gP/3UWd7I7Cxmjv9rtQHBK\nOMhxf89OMwbyhK14f7Cy3MjtatsFajRvdajOn3oP3lGTbZHWgtFOg2lNA2Ft\nFxs065TTg+UXqgg5ZY6z6ZA1Usot3XqB/4ke3CTtqSmsx1tdbXxbuJCZ+nyz\nY9cGeITSM+hZAn9zHinI6twnd9E0u4NhdK3jEufbK7aNO6NhX2j7/5wSXd0I\nIb5+sVvRL7x9Hwk/jwFeC/NQD9QBNaOAEON3mZTcq+pfILMFt/jPXbx4M9EZ\negRwG0uulDc0AsdAIzaqUxbSzbraHDtvp8gepFGihC9TzUVUGjl27VV48RGJ\nGLSaq7xARs9+bXhGnhSq2g9Jejc7xoF9tX6XqUSWpbXX1zgAkni2CKpsGPcm\n5bR+9bAGFUV7WuUy3JJIJXNjw10ZLgFhZLLnvQyRqc6ouPs0+p6478+36QNl\nfuzWVw/gsoXJjU7JbkmAJicnuFQ/V3XyTcTSu0Ux8eJ056/lVoUEDablKhLA\nX+QEE4t2Mlz3kFxdO9eCFZepISl8HIoVOvhEbI2NJq5bCROrGLTN1+s1gxke\nG1ISHRSNDPAAXRsKrZqLDjcmjTuvDePNap1EgERlpumXOEu8nPxq6TCN7wDy\nT3fCVm3PK7+GcjmYjHnlyhiA3FJ3o9Wgyz4fpK3eBjaNfIHa3S7YXfF6n4Cd\nGsN8\r\n=O+jg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.14.5", "@babel/helper-replace-supers": "^7.15.0", "@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.0", "@babel/preset-env": "7.15.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.15.0_1628111592328_0.6177881053923426", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.15.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7f977c17bd12a5fba363cb19bea090394bf37d2e", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.4.tgz", "fileCount": 9, "integrity": "sha512-7ZmzFi+DwJx6A7mHRwbuucEYpyBwmh2Ca0RvI6z2+WLZYCqV0JOaLb+u0zbtmDicebgKBZgqbYfLaKNqSgv5Pw==", "signatures": [{"sig": "MEQCID4ujWqb9Vks3pMd/d5C61+p4T3drrcX/vlMXG7RJkMOAiAmvFyjaMpKBR8OkTaRtdV4908/3XWrG/5PCBa/Kf+lkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSoCRA9TVsSAnZWagAAWGIP+wUPh5LeUjuXwCugKc8D\nv0MKdH/JkDuIfC5n3Ks4ZxcF8QfFd17YzAWrzDIwGIk+QNEBfU6K7u1bloDg\nv/piq7cATdtFZyuirM238GUawd3hOTq2wE122FoO8QwfwifIkU+CrevIYOv0\nWRM5XhM6MalS9Tp0UMLoiKSNxvcejDe6BIEpEncjvNh6cazVurEyJDeEsrpQ\nywc1MhpbjqAWp0STNY6Zu3cxVVUeLeFuz8ssn8Hg6vIFWOU53XIh+uC7PI5U\nYvvGbv8yWcVASB4rdhfUWdjwEr2IQrlWziN7jh9rrG3zXUPrknFXXWUCDmoE\nB8qJXNmN0CyxZDNcuHT1n98GG77dB9c1bF0CzRHG2hqdDZfgi02XSbJ2B+Zm\n0Ltb2uj2TYt6r3Png0nUBTDMfealtbNPEEiQhANgJdnsb244EzQkaslg0xWr\nOagEhf6F/vRE51baKvagUPf6Qf6YOGY1ZWUImFRGn044r2X2cn/aooA9SMlW\ne3AUB7aAzSqZZrTPGb0KdIVWQX2zzRMGwcPyZZkTzUZBlQYyYFlVP0MpRTnN\nhFJl5PcfCoYrXVXNre/UONNiQOVBswWd465G7aeJ8ieOVvyH746MRpAoG8Bo\nWpooaDJbbLiryb2+ovh1RO42KXyRK6eKntcLZTmJU5Bgs77H0XRngBFT/Q2L\nu+tK\r\n=/K7d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/helper-member-expression-to-functions": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/preset-env": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.15.4_1630618791851_0.26415239515386824", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.16.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "090d4d166b342a03a9fec37ef4fd5aeb9c7c6a4b", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.16.0.tgz", "fileCount": 9, "integrity": "sha512-XLwWvqEaq19zFlF5PTgOod4bUA+XbkR4WLQBct1bkzmxJGB0ZEJaoKF4c8cgH9oBtCDuYJ8BP5NB9uFiEgO5QA==", "signatures": [{"sig": "MEUCIG2i8Q47h/hqwJc4CKND/tLL/7hpq0QCwop2tSzAYzyPAiEA/J6Md6DCHpsmhr9WMMqEDZmp7ntllmRfTmOULSztj7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51954}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.0", "@babel/helper-replace-supers": "^7.16.0", "@babel/helper-annotate-as-pure": "^7.16.0", "@babel/helper-optimise-call-expression": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0", "@babel/helper-member-expression-to-functions": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.16.0_1635551275350_0.9774220404851084", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.16.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5d1bcd096792c1ebec6249eebc6358eec55d0cad", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.16.5.tgz", "fileCount": 9, "integrity": "sha512-NEohnYA7mkB8L5JhU7BLwcBdU3j83IziR9aseMueWGeAjblbul3zzb8UvJ3a1zuBiqCMObzCJHFqKIQE6hTVmg==", "signatures": [{"sig": "MEYCIQCSbtPA4xpfuHn5lUgAJM0Mq3BwsXIZ/XAgnBINn1hzeAIhAINvEBAPID2cnEovfo9ea+ddknDXnhPO97wuxCPMxXbT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kiCRA9TVsSAnZWagAA34EP/1HirDX6DDtBaRnPCJsQ\nbOkbUonUCAt6FbROdy5axhCglsPkwORhjCjnSFBRFmpX3cKggT4XOWpFwo+b\nU+6zOB0U5HJKSHAa53Mq8R216MixuTFfSYy39IPrePknM5S7zjqjsFPtU6CM\niOc9iqMfaPrWnc+TLeypYHFkW/NWG2w7p6vXjg47PNAzpiYGbP3b1t/ZOIej\nnCni7QOTjm9YNYZceqBKVMpA417+nrpY3QxQ4X5lGms8PMVyidZVavWV0PtK\nIfCNRbbZTa5kck/8rDRVoiOZcoBpCKVzqTqFVnpKjQHChxnjW/70aBNdJbqh\nYsJDJ9Gi1+F/9lVAGVBYAOPuhUR0wZQNLoMVaViYJrwDC8l1a2rzNdOWLDmp\nRYjo/x0x0C+sKHGe3yYa71l56v0zbjoPnuvXzqZL5eyWiMiJJYIu0CYt4lGa\nV3WGCbApdF4n6XrVEmNuOHGanVpr/2PXTasby2CjVS0iuT1ZDusIwzcsnes2\n95Ojijukpt2ftjvNATaTWuZ/FrnIdxRJj437VUHiaHgN+s3w4LavlDk4t7Hk\nY3yRk+xIY2wmzHfV3CrwLqORPt94uciLkAfDdlumsGp7G/MhU1zcB+f7oW/y\n/YJpVVaFvoGioF3s0hLBe4OZN1YZgl3UD2wnv2DDPjiq+tFAq7qRqXQagcVC\n2ANj\r\n=fUXs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.0", "@babel/helper-replace-supers": "^7.16.5", "@babel/helper-annotate-as-pure": "^7.16.0", "@babel/helper-environment-visitor": "^7.16.5", "@babel/helper-optimise-call-expression": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0", "@babel/helper-member-expression-to-functions": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.16.5_1639434529790_0.8599460142133624", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-create-class-features-plugin", "version": "7.16.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9c5b34b53a01f2097daf10678d65135c1b9f84ba", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.16.7.tgz", "fileCount": 9, "integrity": "sha512-kIFozAvVfK05DM4EVQYKK+zteWvY85BFdGBRQBytRyY3y+6PX0DkDOn/CZ3lEuczCfrCxEzwt0YtP/87YPTWSw==", "signatures": [{"sig": "MEQCIBSV6xjjdp+woF59KaXQV52mljLFzcc4yzhcAVMCmXSPAiADyvezdEALWfw8fHwTMLKJqtkPrwRCqYkrfJj91C1dNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1mCRA9TVsSAnZWagAAuO0P/3/yRMvI5z4qCQ0dNxWD\nuJo3/iL5D4KzZJDlqvqHoFbQt6aaiqdexyKmQJIspk0ZJFypI92CRW6vsILF\nZVeJdTvcjtbZuZ8AgQZ3DfJ0qPnzC7BkKlbEl8XUEZ2I1FCKPbMOreVFJClr\nWluIzKKxoNGlIgDO6GLi7ljdY+8wwOOQpCiiFXX7GzbATWYsIDjOQi6fx4rM\nZwVCvzjiyTh1RweV8K2fq9rCtWvOh6l61NI7857aUUdgO9jwUdVstI8cSHrJ\nDVanXQ437IZrXXWeq9UYUPRv4amAciwTgYkteZ7vBQEtM8EauWF7vnzU5HcQ\n2euQ6fJCVUYHJddWlN54CFKxVgHq6/c1TO+bQU+LcfDbtHqLmMH/ezqPqxMU\nh1oCuZx+vesb9lDzj+zqdRpxun5VwEkXCV9ghSmUvyH2/IhVSExuE3WdfKgy\nPil2e4BWZt8ROSxYuXzN9D8awPPHpOgDvWMonUIJx4dizhbVPC3VuCLnxfHN\nY/hUG3L9TTXwULZSBxC6rrK51vhI2Mpo6FTWOpa2rt26M+Emt3leOF63Vv6i\net6J75pYU+DeT+Nl7c/rD17qCcigoJADp2t8P2ZhuFs3v46g6KnHlphKwNUX\nqmrsL3uqWSbQol61TGlPDfOqHOoKDq5jY7TcrHPboLzD3z4XZwnVnYHH+FBq\nmp2y\r\n=9dZ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/preset-env": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.16.7_1640910182126_0.29459355759767925", "host": "s3://npm-registry-packages"}}, "7.16.10": {"name": "@babel/helper-create-class-features-plugin", "version": "7.16.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.16.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8a6959b9cc818a88815ba3c5474619e9c0f2c21c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.16.10.tgz", "fileCount": 9, "integrity": "sha512-wDeej0pu3WN/ffTxMNCPW5UCiOav8IcLRxSIyp/9+IF2xJUM9h/OYjg0IJLHaL6F8oU8kqMz9nc1vryXhMsgXg==", "signatures": [{"sig": "MEYCIQDCdUsV2WlGP3E0vUxYJV92E33fKiU95pPOQEnpd9ZrjQIhAPb/PcehGiujQ2/dwau6fi1cF6Mb2mlR3HL5OKZOCzje", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6FrECRA9TVsSAnZWagAAgM0QAIfO4QgZEYKwcUwhEYKS\nboj3P0zKepWBb0DNNBYQHuXOXI3F+CZ0CLF9pituaSo6dWhWsvfmidNYFFOF\njFK14t3pjaI6LKExi3KJmJU2DMesOIafiargWfNoPnMIRwQYMSWfwkiHMau1\nhkBAoMvH5aMPcYQe5/Pw697sEPOIS8VGSoISdSOwXuhIlL4AplXhAmdkw8yS\nZlV4Vtsj4/JQJP0KHOtUp0e5SFBw3iK3+h9L4IT38Psll3uXvCVm0nMvILBe\n6LTr8C038ycGlJPMKxeSHjYBHsV1cz2IrP2gFJ1oEidtYFAbCkM4wc/YlGyk\nVIkGN7JsqTLjBa1qWh2wdQZYsoLNNR06S8E4STgkNML0EroXDT45phOWsjnq\nEEM3mrnUZDyvuf2mbGGXbjA9OYDG9I5KvRbvvTtMkb7CjVHaEBV4eLsii2MN\nx7JEaA9GiV4kS4jDB83o6lcetxHPIPCzdfFMZr+WyMKGozT1KS5yLDtO8gZ3\nrdtCRHVyNC6PPkYOy3tJ7D0x6dqYSgrNZlBy9x8063Nh32z39C0pm9wC/S9G\nYPTMdLajOQ7NpADTOBFiDPmugfInvyPQN+uJ8lIFFsy6+1OH9dF7heB/N4mt\n8TZjN3nm0sMC/T0/IkZaqtNvF/HDvyWbW98Fcyp0raE1zAHrABrw/4sMlnbE\nniZV\r\n=QCiO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.10", "@babel/preset-env": "^7.16.10", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.16.10_1642617540476_0.4521485905955791", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.17.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.17.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3ba0eae83313d4077319d11a768c46adad026433", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.0.tgz", "fileCount": 9, "integrity": "sha512-S3+IHG72pJFb0RmJgeXg/TjVKt641ZsLla028haXJjdqCf9eccE5r1JsdO//L7nzTDzXjtC+hwV/lrkEb2+t0Q==", "signatures": [{"sig": "MEUCIBJxvZtD9ExftC93538ab7D3GM+j1h0554F6JTaw59VoAiEA50SQ9Eb+aMBzieAgToXSlr8QJoIUQIumSHPpGw2M+oQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4ICRA9TVsSAnZWagAA/WEP/RVe/lAeLKHyfMPHrE0p\nTdBeFn/i68th865HWWE2PoVWbSA0kpSyXXEtZGz1kcmZVoHWEdegG0W0xMGi\nhSoa0NMihgedn+Evnguz1YW90zO7MiK3NPP9lHasN5eWNKIlhLqwW93swA8q\nd6teyiTlYMdRCmILr+/IfIyQ77VeNdk+0JnJKjejy6hk63+la9ipDUPwJw0g\n7ypujmylaGRimD0P0Av+vmZXK/ehovKf6tZdPK8g1EruDEic0LnRhx8Pgjn7\nJ0bHAjvuioYBIpmcRI7ZvGu0xEQfgPWMlYrZnw25wbpzBjoVlNm+TV7C49tK\n7NUJNPl2IwNflKwl1OcIQVb40n99p0egNr1ObDrzw4jtzIGniMt1Spmu+yJM\nG9numapSSWvsqxK15yWCeWFcjXQRReix6vk21LKpWafY2PWBifbxSfVvFsqG\nKnJ0Rles41X4dHnX1Wrg2EPE5kO8LG4Zucvofqs7ZrgqQ3dx84MabYiNlXlb\nriXVuFrh5aKP3V/xyDbAp4QhCH3NcN2vWbdFiE66Af/QHs7T3z1holZvlp7U\nOOJnXECkpTC1KEl+ejm90XbrWeh27I8aTkY0QBeUwCQRKM/ony0VNzLTpitK\n5IETkQ4SNbFkduSpv/+XpF+OIxlmdgJLFXjcVUeeRChDF9zJGTPia8aWIIKE\nsZqw\r\n=6PP3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.17.0_1643843080333_0.8364528032733707", "host": "s3://npm-registry-packages"}}, "7.17.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.17.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.17.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9699f14a88833a7e055ce57dcd3ffdcd25186b21", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.1.tgz", "fileCount": 9, "integrity": "sha512-JBdSr/LtyYIno/pNnJ75lBcqc3Z1XXujzPanHqjvvrhOA+DTceTFuJi8XjmWTZh4r3fsdfqaCMN0iZemdkxZHQ==", "signatures": [{"sig": "MEUCIQDMn6flTP87VhBPb/G4c8WvD4LcKOuWH7Ar5dFeZLWKMQIgeXcHbiuSvopTll8RmZxncUeOT4uE8gZu/fbOnr7Asf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/BLkCRA9TVsSAnZWagAAuOAQAIyfFBNRa+lZPUHJozdw\njFIkEndTaXssGw1zTjwlk2lupUqqvqHQALBIMLK5pBqq3XS5Rp+a6deEaiOc\nrnB2lQ0lIl0/u6dyu6LlvtKbCVFOErPoW1OwTulfJy2G/fEMWTto4R+EkJjH\nQsWgOFRHPoYkG27gZIgiolZBPKIyOUUpNkxiABfpbxm+XzoNKeTVoWkAw+6/\nw6jpoRJYS2zvwGm9/BVOcDhNFQXz7XfHpcNSRb3UXDpExXg7G4LR0O73+IuF\ncayyK8xG9VlB0JztKBQOQOszhAYbMEnoxOKSvpwwd10uQrkcjsIAiZ1PDZ5w\ngFjMQjb80XPHHp2iySBcKvML31OFZZC4DyKiR8piK4nkSYX58A48aRAb9SqS\nQTGVOFIEN7bt57RvJS9YCcyX80Yh/DR7fvdXK6qIiAzqHfhcG3bU14zxxUNf\ndSmMK3LJ/ySgX9nfOz3q4+dQr9hd2/qn+bfvwR+AtiGv3Uc2mXFafn4XowZr\nkzo1qU1MKsSgpwreZFroUFdxef5MDRjfIATli92/KXmwYbW2SHLAVZ1kAQXK\nC3K7D61dsZh6Dri9O+mYSHCQcwpbFEz1+wMIOg/qKM8CXgR7rTa1YiZO9949\nvOD+NRVyBZnQUWdWPLfVUhpbenXVYFNRhAudf+kCDn/pCsbUnD8soPxhhOfO\nhGon\r\n=qqW0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.17.1_1643909860616_0.19628477135279154", "host": "s3://npm-registry-packages"}}, "7.17.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.17.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.17.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3778c1ed09a7f3e65e6d6e0f6fbfcc53809d92c9", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.6.tgz", "fileCount": 9, "integrity": "sha512-SogLLSxXm2OkBbSsHZMM4tUi8fUzjs63AT/d0YQIzr6GSd8Hxsbk2KYDX0k0DweAzGMj/YWeiCsorIdtdcW8Eg==", "signatures": [{"sig": "MEUCIQD9UbViU72hZV9XaZbqtDbxacr0+c/O7vwEmH7UtlRTRQIgW8y90KNP7ILtwZdFY/oSpsNCRHBs8VS8/dQUo9bVgXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFBkPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKrRAAiXl3WBV/iyWWuZXHAW98TuoDqx2ZPZckhwd1veUXNHd8Meij\r\n4kgwDIOgc0yq0IweaVpz8rsIxD4HLAhJiMVbBe0z6pqbzwfUeBNlJnjZQ1R9\r\ntdtx8YgXqXrEraq8Oa8A+SGFtkztENmSQybL5JpMo5Yb50d8oEtFmoHj0ULE\r\nYWjdXg7qJuiEsS29RkQy7tHKC2r+jnKpPvuy9BFAQbA7jFBysNQgbJl8ViPr\r\n4HjSEB+dAsicamLdF6YSxskEzue6tGZdecJbl/lF+p5nbvzBEAOJw1Stpt5a\r\nMnDtSGHJuPawA16PfImzjMEthbU0vb+5pLTfo7JYhezq6sBsDso4nXt8tEWj\r\nEz2E2yfnjjmWIq9wZmKXG86mu5LUODaNm6sGPPESqxGtIe+5StMyvdNm2H6O\r\n/D+xx9gRtuGg4PsNJBCRvhO5nbnzFd/nqiqmH8y5G0drGpg/EkPVu/yBL3C8\r\n7+m+Njmq0jeSBPqugh8w6n2G4Dhmn/Atq0XVhEtqpNBcNUByHkFmsz8EallR\r\n8PCCEKYQki5FuEL5mvCTDrNA4jHHfnZDLhi7slFu5FaKWw2LTaKMjXfyGEpR\r\nfhE3Z667Tj1GcQojwLB8GBFbF3s/lKRJcnliiY2xG4jxb/JSc7fub9qXKbYt\r\njnyUEdJRC4YGB+UGV+5FU9YtCtChrSugB48=\r\n=E4Ig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.17.6_1645484302852_0.8949408724533057", "host": "s3://npm-registry-packages"}}, "7.17.9": {"name": "@babel/helper-create-class-features-plugin", "version": "7.17.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.17.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "71835d7fb9f38bd9f1378e40a4c0902fdc2ea49d", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.9.tgz", "fileCount": 9, "integrity": "sha512-kUjip3gruz6AJKOq5i3nC6CoCEEF/oHH3cp6tOZhB+IyyyPyW0g1Gfsxn3mkk6S08pIA2y8GQh609v9G/5sHVQ==", "signatures": [{"sig": "MEUCICfzriG8YkZ2u/TFaFqSKrHAvClu0QHkc/flMZU3JZIXAiEAp6XX/Az0yI5rEd2pD5jpvHwHqLt6tpmuXwdTk0YLwqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTbftACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGlw//VvMNz4Hw6ekdYz6A/St7FZl5ds4+pEKC7w3oRZ/VCSiSrlh/\r\nQV/8+ZcvMbW3ukRrMkHa+02MF4EPZWxE7L2mggT/ScEvKLNtAGrMLgTEechs\r\nC0my3y5xeGWPMk3qsfER23DqwhHqbruaxQKMgazmDD38hrpnFWxOuhXxtTUE\r\nmC/T9I+Da48ZVuhcugNIHXoDxcLH76qifbtZGqlh9AhwFeUL4DjAAR+wSrCU\r\nQn2rm1jtkW4N+H4A9EjZBhpnxHp87Po9+oBx+NKDYCX41NgpcsNA1cqomFKB\r\ntlyoLVx5FSQ3j35umAAS87Puv+ATYLFq/FFa7P0U8UpA2lcvZqzqbStRT8Ze\r\n28GN6BPvmJ4uG1WGw7oyp3nYzsjiLrsPoj6R9pO4ux2vu2ulbcHhovirMZ5q\r\njPJl+SCqPdIreJNXxo3z2zOQ02MAZcDrY5+oZFZL/MdPVTTgRmsc7/p8KC+j\r\nmIUjrQJtU7SK1x899ivS5/kl47Z0ohRb7gq0t4a5AkjbF1t7Ua5HcZRqmHlX\r\nPs6MVjEEuaWP26L8AIXqa/0n5I0WR64EOmVA4RdIhvUTAniem9afaWrQT/2O\r\nwVx4NUZhucKl6KB0UZa2zsZNMax7uqsFe0+yGpZg//OAXAvqDErOxJW7g/gZ\r\nRakpcq2My2SwImZvJ6Q+I7v4k1VaU84trgs=\r\n=VIwL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.17.9", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.17.9_1649260524829_0.2788371250078576", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/helper-create-class-features-plugin", "version": "7.17.12", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d4f8393fc4838cbff6b7c199af5229aee16d07cf", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.12.tgz", "fileCount": 9, "integrity": "sha512-sZoOeUTkFJMyhqCei2+Z+wtH/BehW8NVKQt7IRUQlRiOARuXymJYfN/FCcI8CvVbR0XVyDM6eLFOlR7YtiXnew==", "signatures": [{"sig": "MEUCIQC4DcNgM5j9gLnej8ZZQdrz9ka5WI2fXj4PFxIZT3b8iwIgYd1Nnaa8je7D195BDNoduj5xUG0iFxfFBeE245E6Bcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqa8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqkA//V10cFWvnzupa1g0ZkX2XUB0YGFwq5xM4mQhHrqGnkEVKcMfK\r\nblj/BnkuVIWxS9jMHM9iP9BMoUxohGihR84nRAjVnRY1pWN8IRI4qsFVlAO0\r\nbSU/D35nuMgAzyQx1pvRBNeEitawVLHQrNRjEkiQJKrm+49YIpIPl9rSLbPE\r\nCPAWteG11K7S8xkyhzDY9lGYIpVIo6REXO+9tHMsfDncLx5X41OThjlAXNnn\r\nxv6zbVCavoJLAr7BruAcTXi1cBODw6ru0n1mZOyzNPg29kQFN9+fTsQKmL5T\r\n4FKFvdSmflop0N5GlZBRP721BKp3hBWr2vCQd2Y8F/A3oPs5pg/4IRm41VjY\r\nnWgtdCDmlQmDWT68uVHWjyaEAAvEJY5MPfpIfk6NjHxw5I8AEQPg61dwznKW\r\nTx44GfC51J1jfse4jvxR006Bxm4dVVdHW/7gbquyvFARXspKj2FuYlLKQ7SJ\r\n9OJ2iYQPDw+X+mwszeGCob6Gi0A2ddmoMJ/OAetpbAeEmJBqFKaRSXO77F/t\r\nt+4Yak3IvnR4EQB5eXPO3At1QXblGpSCTFhUbsLko/kGxAemtpVXSN68mcxX\r\ngfK28BlbIaQpM/wbzqSL0rnnwnRQjz6gknHGT3YGhc83L14WLYfyiCPFHaFA\r\nWMHyvyQiWi9oRwyia+ILz3gzBlc1x1UsAJY=\r\n=OviH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.17.9", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/preset-env": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.17.12_1652729532637_0.0397777775607957", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.18.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fac430912606331cb075ea8d82f9a4c145a4da19", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.18.0.tgz", "fileCount": 9, "integrity": "sha512-Kh8zTGR9de3J63e5nS0rQUdRs/kbtwoeQQ0sriS0lItjC96u8XXZN6lKpuyWd2coKSU13py/y+LTmThLuVX0Pg==", "signatures": [{"sig": "MEUCIQCA54O4dorGdpSasmEekFY/ohN3K9sUyNTB6PmGIB9TEwIgBqGt5DsWXqhsxw04clIdlqj12hSTSo7pT87piS8FLO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIzQ/7BBu+/EbUQGJcokFwQpjHgQ6wpO7Ra7K8nOYc4ITBr4YpEajS\r\nCc9thC3l0TScmNsbjJqb0iFYMPlWHsgpI4bl9oo3vSYdt3WmywYPeVOGT6Cq\r\nv3IZGeGQHfdO1yz4Yxv+9uPr5OBjwZGuZX5G2PSIq898ttQj6uch71W0Ba8+\r\n+gGz3cRUDtufOWZi21nEvWN/H1hfLgCX6U8DkZhh4S9TniruBSN3WPYe4nmS\r\njZWcOYsxIV+s5Iwqk2VhMpUj2sa5ZdHMFGSynXQqLsQeWCOrp24QppfILN9w\r\nymtBy/6V/w4fc6+LHnk/QSE7o9SGaEu030iEOxQBVEzOcYKmdMEU4PCCTKLj\r\nd0feCuJvxms0pz3SfYnoc+Rb3RGChIw2OsZD4E7kDUSDbeXoYU0FiihiCa+h\r\nteKhoQ7XDcNU1A4z+LH3OIiVQt4yXLws3a1zFUzxfnq6J+6R6MaC8gg4Zfuf\r\nAbThpHqJxYBwlDCmPmMYIxhfnnmGnYbGM7JqI1R58x2O6Dg0BM+sDp4ZuBFc\r\nvs4q/ZSHXrBoowdqq8rylavi1F7ipxD/ACGCvZJwEMVUiohN4eYZsKNOcU66\r\n1O5ygYnf52zUVbCd+/X1d/8prkliUmWK5sQZHmC3DX8iqTk+U1OtBnf18q6k\r\n4dusLBca/hOmGBZSgliotLBITaCVp/B+3Qc=\r\n=NPZl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.17.9", "@babel/helper-replace-supers": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-optimise-call-expression": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7", "@babel/helper-member-expression-to-functions": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/preset-env": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.18.0_1652984191928_0.24893743284028602", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.18.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6f15f8459f3b523b39e00a99982e2c040871ed72", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.18.6.tgz", "fileCount": 9, "integrity": "sha512-YfDzdnoxHGV8CzqHGyCbFvXg5QESPFkXlHtvdCkesLjjVMT2Adxe4FGUR5ChIb3DxSaXO12iIOCWoXdsUVwnqw==", "signatures": [{"sig": "MEUCIC45+A3fgpQSHiHNI4PK+sL25QMdkumfyy42omUlS7yaAiEAo7+hCcr/lNQO+QXL+BiVf1Fm6EyDgQbm0pQQMr5Cjhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7nw//ZSmMEyFml4f8TnfD5IrRl+kugvaF3qv4rACEKIqrQbHyel8B\r\nxoTa1h8qYXffaYoK5dAGFBIWRx9j9JgdIwdF33jRephKyHeqwPAZOhO70z39\r\nBZewNr4t+DjBWpDwgdgkw1O/paoK1f9vwXqLe6Fhe6E7rJSFe1PoLV5iLHUF\r\n9N4Vmd6DgATZS9lTd26kE22HTbByTQ1Y2vj7ek9BumELsNw/0tQyBl/8+WM/\r\nCrhR1ImpMv1YE5OYFW/aSAnL0dukDkfi2c9KjsIaAY0BHPq6HU1Iyr0fvDO6\r\nrynnj9S2ZDyoIbEP6rglYnPSFdFuGNpdkZ2UblzknUQcDxSgzB9oyL26GVT1\r\n800L70soGfGr0eXBwVVx39OOGAWfveg7+kyoEY6zC646XuLgwVFQfm26r5El\r\nbFtSMc6Oalzntf6JESJ9VDW8bAybMzlzCI8ZFBMrIvypZ0YJVDd7yCqcuPSa\r\n/5QvFiJJ4sb1qLB/sPuOBz3WlCpepZuSbTm6JivpMNpi06hZ+YORYi68h02D\r\nLvI/bdNHlnD1e5q8dLpicCIstvVV5E0CkowoskEJg31nu4kjl71ytndOBqlJ\r\nLXRnOSu+F68u5fN2Fcg/hc5lfL2EO8TlrRYIijOKiRFUcr+/So1Hyn7O7Ls0\r\nOvE+2xxgMCg4ouOVYXDRzXGv62MOOqEeeu0=\r\n=uS/B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/preset-env": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.18.6_1656359437593_0.6365039134432096", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-create-class-features-plugin", "version": "7.18.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d802ee16a64a9e824fcbf0a2ffc92f19d58550ce", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.18.9.tgz", "fileCount": 9, "integrity": "sha512-WvypNAYaVh23QcjpMR24CwZY2Nz6hqdOcFdPbNpV56hL5H6KiFheO7Xm1aPdlLQ7d5emYZX7VZwPp9x3z+2opw==", "signatures": [{"sig": "MEYCIQDER/6WXboE0v6J1AqHnMMivvMFHRSgWxhrOErCOruo5wIhAKyKKOW+hI5kULW7rkNx2ShplfxoBEk7QH+mJ9LG49UB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT+A//cMkLLvNncBVY2i/RwiIDjKt599paWBQqfV+D3Ki381XJnfFr\r\ng1fo4fM13tZ0Vlbvq9ms0Gv+zX3LU6me+5y7aOOtJ0sfD1rjU0NFamv+ZG/o\r\nW6ZzUt7MN7TjnN4+hNM0QE9xXTKCqfKgKQ5vPuUCNxpBbBXJ76AaiEINKDwf\r\njqLiORbGtDaDo5zpqersx3PzLL7P86T5XRA/xtgT6a1Y9ZPA4xCUAk/MZCtq\r\nB0F8UGUhSH9Pp5HGQuQCHRfaz1Zf0w29tYc/dcWT1DZhZVjjcYqx8O7vocVs\r\nLQkMvRn3h4YXf/8aruGFT2b2D/S1dKo3vvctFiqX1x1gFk6yWcpQGMKxv5Cf\r\n2Pzo4cTONoOTdmS6vRKhBp1QRHK97XI6W9V4LfuCCXcObnPYGi25q+yOFR4/\r\nNjsgsPOvQv92eaqEkPJrkgH63AxKdpW1AKP9oKzC/E58qu2UMGZDG8j8Jrip\r\nBXZt/nW652gGgu065LRCw7qJpR6aE9eXP59uIdN8qyAYWZ31fEUIrQyruC/H\r\nprhweqREP0xbgizr9SLf4Q6DagxjpX/CoKJTIMq7FknA1az6O/C8DIEF1vB7\r\nStug7TnpDv4HdR89RcOCIjHy2G4xssAl7zBGm1cCaCzyWogEP0RGXCrmBTHJ\r\nQoFUfoEfB1ABS4TiYSve/SNQ2oIqKV1K/g4=\r\n=bSIS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.18.9", "@babel/helper-replace-supers": "^7.18.9", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/preset-env": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.18.9_1658135861926_0.8228273826814805", "host": "s3://npm-registry-packages"}}, "7.18.13": {"name": "@babel/helper-create-class-features-plugin", "version": "7.18.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.18.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "63e771187bd06d234f95fdf8bd5f8b6429de6298", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.18.13.tgz", "fileCount": 9, "integrity": "sha512-hDvXp+QYxSRL+23mpAlSGxHMDyIGChm0/AwTfTAAK5Ufe40nCsyNdaYCGuK91phn/fVu9kqayImRDkvNAgdrsA==", "signatures": [{"sig": "MEUCIDZXpp7l4LaMNQgN8a5ijjcit4zIlrBHiFzndxQqwTz3AiEAqoNaBqqrAuGB0ep8qn8abGQTZQYTpLSnO0GbBjCbD7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA6k2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrX9g//URq5VuMvecjt/yRjTWgWcXwNnumEyXbdUJNhuXcVYgHZ5Ua1\r\nuBoZam+Wwh1LlEIgjiBGd80WE7UDnZ8+VfN8Gt9xyCwOdjvX1NPvFEN8CmNK\r\neKrGKp9wZl8spKNGF9Qix0gGNKXNciUdA6nNYKCg61Dnz34zzUmEFyrboZF+\r\nA+a4GoG5WPr3nZWmnfHsEtemlZ22imiAzvIZCpm+yy+C4G7EhRoGX+DI1zu8\r\nxMVkrit7m8SHxf6w8SJkPO79FApqnskUGK/Ha9PnMhXVwnOQLIAwPVyqyFd0\r\nF1zGrdwdapF0bD54zE4aPK6SF7GKReJ8kDH/bmYdBi0OS3jHTiWwDMpOvm3d\r\niFlXZ43ry4rk4wqt3PhPpgp6Jd3A5rc5WC04YG4zdZ+b+cVneF2sgEMjXlLH\r\nNEWYwBbCW2p5JoBsXnDhXPCcOcbTUWwGjWimWt/cqRVkPvrsv5pOn9336Xzb\r\nEWmBkRoqNn8kD1L/Fm0DceyQR5QNxNaaHJJFzsxYX9Flco8MnxGADcVr1Ift\r\nFgVpmlZrDXmnG0AmOBzuPDLuTthIK+bkZvwj98/rFwI1exeXXwpavarbxP50\r\nBETgc+VnpPbE7i1pIRdVtXTiB/tkwXpSTHZifssLMJcey8CksUvie/B0ial1\r\nfQ9z2agxuTYrr11PnMVx//NdxreAU1cI9yg=\r\n=1/tI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.18.9", "@babel/helper-replace-supers": "^7.18.9", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.13", "@babel/preset-env": "^7.18.10", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.18.13_1661184310600_0.8834485707644559", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.19.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bfd6904620df4e46470bae4850d66be1054c404b", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.19.0.tgz", "fileCount": 15, "integrity": "sha512-NRz8DwF4jT3UfrmUoZjd0Uph9HQnP30t7Ash+weACcyNkiYTywpIjDBgReJMKgr+n86sn2nPVVmJ28Dm053Kqw==", "signatures": [{"sig": "MEUCIGUCQM5CUrDVyow7hI0JWhjhv1JZjGUioScf5PNzbitJAiEAsbvfxqSh87z/8tI/94KeoYZSIupXmja4BQ6bYKM6GEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwvA/9H+KzRqT3hVApqxugqm4IRxHFg656KivQJJrzarVEjVfCr9VU\r\nS6Of0Y+jOX8wyAvWegXrIWFRfu92ZbwJv1TnAT7RYTn6ZpN/ZImby7Swzq1Z\r\nSIRK8RukgKeNG7W9S569Va/UqPzXNfvz4HyrZPY5ccrVSk60tbAJrf+TeEDq\r\nh7EMfN3Q1c7CQ2/b4ieageeiYTRkl4zx49evoMXOEEXc6Zhf+gyozWaf6nLf\r\n44gPJ0yltaeFhY1O8qd/9uJBvXL02Dqj+JywozcIGjOcu2W4m7VMOwY8FyIm\r\nwsMby9SjcstndeABgMHx2XECKppb/XWtiSr6xrZRa0TprhVYmbEoKX1WXE0e\r\njbz7wwBCbyj8I49t6YKepy6yrfLWu+A7uuADz6PdLTNnaFJwCulnyrlbQ3vd\r\n3P9AXhjL4VGl3tIWDwKA51cZp4us3fuWMG3kJewqhfz3n4yt28PeDFft4pIp\r\n4Xe7K4MY5s6n6OuprOCMl3DYnNk9sYfFa9dFCOQCfbM3s8MatMzbfRbLee2w\r\n/2XZvZGKzuJ5EaIxAYukw+G8oYKBPFUfRl43sM7mX36qMoJZHDBZGK1P3bG8\r\nMEmQLatHwmyUlo8F0L4fcomrdrHiPYBoUbneFLsawtc+FXGyI6JLvmUD7sPq\r\nd5GjgUqJxFSzORMQz6xqcZzhSXqnJZZ7TtQ=\r\n=mFOw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.18.9", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/preset-env": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.19.0_1662404539939_0.32066069371441386", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/helper-create-class-features-plugin", "version": "7.20.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3c08a5b5417c7f07b5cf3dfb6dc79cbec682e8c2", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.2.tgz", "fileCount": 15, "integrity": "sha512-k22GoYRAHPYr9I+Gvy2ZQlAe5mGy8BqWst2wRt8cwIufWTxrsVshhIBvYNqC80N0GSFWTsqRVexOtfzlgOEDvA==", "signatures": [{"sig": "MEQCIB/N4vikKSgvCyI6GTn28VzOABnPDDlkU94yCuvtxWNGAiBSvLafw8yPH+H/Q/79uKQ+InLbQh/GHvgWwadA+lzf8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgBA//dpfNIaVRpKOfwpHY7mBg16wmvnd3OK2WrLCV5sIEDtyFmqJa\r\nilc8GdbGem7YUj0ZnTRl+AbIe3RwpCz2fZ5+3I8Jl+Ykd2QynWbgd1uDucjf\r\nOGgAn7YeMFiemCR6WnFBl5i1hSuUMH0HvYNIDsI16UDb8YBIeOOG6QYSPdbn\r\nnUdYBDA+RHHhHWq7sUi1G05k1RozmNMbUq9pToyjXpaTvokxjAllEIscqqim\r\naGgZdZua5Sbed4sFosr1hF1CEoq9p0mQbmKJufE10GWiEsgjFmLi/5q5UJu4\r\nUQW9SKW2vEGQIwxk7LaG1hpD8jrKPtrPuvqmF3ywOwDc2i8/vn053lBEkDjt\r\nWMjcyUffxmBsDyRfnu7+Kxm39A6qlRYDvppLRtriZslCFAm2dOQ9agw9MJvs\r\nDv87bhePaaibMmHgDhH45Uy+6pcHkeFe/hI2fXprBkyG6g0xKyMqI2gXaw/h\r\nIM+AOWxsy/nSPIBHTrR+RqPKOFA6ah3xhSCWfDjky+F0kfmBt20qnU5/Xbbf\r\nC4Y/TvT59z5ncl44PwI8FKlI4PFhlcLJR7NViHzSz4q0OqqmZXCAlQWmrNV9\r\nXltXQaGfQ7fx93yJ5IPTQITriay9+bHfwrgw4269claUGtMF4Sd3D58AKdAC\r\n8gA624BVM50VllXyKuFZIRPNr4YYRZgyGzU=\r\n=K/BT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.2", "@babel/preset-env": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.20.2_1667587862617_0.7642147850675811", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.20.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "327154eedfb12e977baa4ecc72e5806720a85a06", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.5.tgz", "fileCount": 15, "integrity": "sha512-3RCdA/EmEaikrhayahwToF0fpweU/8o2p8vhc1c/1kftHOdTKuC65kik/TLc+qfbS8JKw4qqJbne4ovICDhmww==", "signatures": [{"sig": "MEYCIQCK9nu4QgLasZCCDgHGpHfSOXXHvPJngT3LERBPTmbdLAIhAONfCCV3DA/Gr14tWC/11e/HhjvWWrPCqeNgqpfLb6Cj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Pw/6AmgMoTGvay+MJRqL+WFuG5rdZaX+Kdy2JMrHP5pH7A+Y2Suq\r\n6ybduxPGVtEY5mSp7PZ8t0ETANHRSDjLL/ya8cu/LqBM6v570vummoL51kB3\r\nzXOe3iXlpPXBRQzgIdfLERwwwvnTQ4XSzjZEgg0p23EGqOZq0FLAX3celJI1\r\nAbr4pDSy6+tlklRIBHx5cK/89nYfOuqsC9gm2VGqRJVSRlAWOtYl218yypUB\r\ngjCotl+2Euo36ZxzBESQ7HmNBtMx4OXu9jJJskayhVLF+bINJnPeqwytmAvG\r\nz9vub8p6WejvowXsjeQ67Z0YNxTYDIi4TVk0ssbhzRYKPbC9wehqXzTEN8SV\r\nRe02QiUr+OvgybNI9bGhBqKTorgnbyJuKdk1biPuKsnZ46QSDRmV7tzh/C+1\r\nu6v0kgcCvSmw2NYdFzO/jU0d3WnQcjS1f3Bv+g706KfTbTLhnsXNnjxEAgMd\r\nfzGNt2CfCyRdEKJvUDB4RLxP7XRCUAKkIWsK/a9Vws6pD8NBW4rOtMxHZEpr\r\njAOMCULFUKT1WhaAsMGM5LIAoWzxsTNlPsw6YnUqKpnTW+7sA0e4F31Y5jid\r\n/S1IXK0Gd/YTsS4Aoq8wPR6MjSS7bJ35mesHNVtt15b4D85wTbfB1FnUx93w\r\n8j2+W3Te2OTnJ6OAw+OSQUF/S7/eaeIf6w4=\r\n=1BQz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.19.1", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.20.5_1669630362340_0.47744000962169575", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/helper-create-class-features-plugin", "version": "7.20.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d0e1f8d7e4ed5dac0389364d9c0c191d948ade6f", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.7.tgz", "fileCount": 15, "integrity": "sha512-LtoWbDXOaidEf50hmdDqn9g8VEzsorMexoWMQdQODbvmqYmaF23pBP5VNPAGIFHsFQCIeKokDiz3CH5Y2jlY6w==", "signatures": [{"sig": "MEYCIQCI+epCKUgD4q0cVFRgOpv7/WtVucjeUoCAoBuqyuhn2wIhAK9EIlmsB0u4wogQfC+hiHNHDTS8GPrx21awUYiHSDZj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrL8RAAm0nlGrYgPTdoIuA+bi3CkEACjfpuXaxG6/k5g0mZRqm8MVuH\r\nw6F0in2E2WdEgO38fZenGGF3Jzgm1OV0lCHyNZyOUSy1jym33EmVeRCHuAb9\r\nCKpgn9RuJyMNHR0hU9vbWdviAxs5jxALzqQnicYsJ2nCjQYxr2bhuBA6FA+0\r\nKvzUBzvuLmeVna91RP14SqJBQH5qtGjzXqeMQZLuN9kAkHmLOdR3gOmMt2ED\r\nfhnCXFUIBDgZeqllhQ0Z30+p+D0dFesYDf3x+dMtSj4vmpInMN7GNHeevD/V\r\nfBBxRsS5tBDNE9knTNtvcIbNhMLSN979lNQjMo+Gxn7yiWReNhU7/1j5cg+I\r\na9E3996ocFgtM69RK0k0+AIYUK9c+ppu14pcfKIECm7LlT2Q5re9bnLG3p/o\r\nYycBoKVOhdofwqzli8ea1kyQIHbgeos5uYv61qBpf3RYgIBP0UpXt12vwS/9\r\nK+u7iBT4I+zcW92Xf607OK7yz4+n6FHLQeOnmVpEWTtoj9sIRlQjaeaCPlK4\r\nJPkGFmcnx5idj3R4exjJ1u0lvuHqt4Zf8bx/04B9CO7D9s0mC0NuO8ck9u09\r\nWPnzT70MI6uy8z+f6rYPR9BvhZDBkOfslMagDPZuXwJuNp2hKrJybeKtP3xk\r\ntDCRemkah71QRGd2nJGZaP8B48W0IH5R09o=\r\n=geM0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.20.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/preset-env": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.20.7_1671702337313_0.37929362851593207", "host": "s3://npm-registry-packages"}}, "7.20.12": {"name": "@babel/helper-create-class-features-plugin", "version": "7.20.12", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.20.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4349b928e79be05ed2d1643b20b99bb87c503819", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.12.tgz", "fileCount": 15, "integrity": "sha512-9OunRkbT0JQcednL0UFvbfXpAsUXiGjUk0a7sN8fUXX7Mue79cUSMjHGDRRi/Vz9vYlpIhLV5fMD5dKoMhhsNQ==", "signatures": [{"sig": "MEUCIQDgRKvtVIkbfV4g3qMH2WX++FsfubvPEEKaeCG/d60lXgIgZ67dR/pwYHVbcq7l82r40qwVhB7ZCnyIIAZtrm+ceig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtaMIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLyA//Uz3mpevHFMoRo/kmOUWqiOd6eDLL2DfiKR8lc5vVBKtFVctO\r\naAXnbSMKMWFok4n0qOu+lxr/bWh43ZyIBp9RzH9qnUpWbRIvN5K6xiPZKI45\r\n9dMvS+3s4gWfG50r6R/ezad2kFqWd1DQU5EjLfg2unNLrII+EUMLPQQN5XE8\r\nRhB1DWK34yQOT8RPVZwAJ8xFWpl3NIoiEU2/uiWQDE/r8By7YSSLxH6cKfJd\r\nyJhDOFZLt6qg0rrWSZvrQEINWXCP2nCIi+cpWyB0yObnoy9AoDT/jFNFnjIP\r\nI+w8sLuU7hGh7p5nFcWn+UMmWC4VFmDsO8K45lhOtObla2p7h6XlGIQfah/U\r\n04sWT82B5u1aMx2PgQyvBG98iTwMVHwt6ZXtaAiqPoFENA9C9EuwpM5AIdrj\r\nWiS5DQc2s9BVPumT39wvzYZWyvcHvD1C8Sc98bhRyv/rqD/VLCtbaG8AtdGI\r\nLfA2vW9O41LujkKu1CSLGJrv2aYeH1Q6kuNN7wrIPZ4k2hg2FYoSYp8TRfOh\r\nJNij307MJ9HZ73bKJZUXMgGgTMgyhclogvJigN7mEqKlq2KMdUhxCpsxEEnq\r\nv2rjD4ogGPjT9aXDEv3KjiYYhGc7u98OZGN0BMfpviG9e0yi7DtrqStILl6j\r\n2eiCvyLgAgBj8w3K74GqGHeeCCFpCaLtD4s=\r\n=VvsT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.20.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.20.12_1672848136340_0.6787392593617081", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "64f49ecb0020532f19b1d014b03bccaa1ab85fb9", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.0.tgz", "fileCount": 15, "integrity": "sha512-Q8wNiMIdwsv5la5SPxNYzzkPnjgC0Sy0i7jLkVOCdllu/xcVNkr3TeZzbHBJrj+XXRqzX5uCyCoV9eu6xUG7KQ==", "signatures": [{"sig": "MEQCIEzx9vEKF/W8pxr9tLGKisFoXtEFbN00acDz48naldnGAiA55APm3PSdkEQk6kZSbOayqFNXbqe8IDPiKdUL/p75zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXaQ//TRMc8QbDwYJP0gT0es2ogiX/9y9ZSHPVWUN/L/I/Uboc7ByS\r\nMTAR7aYDyNgs7xE/JW2spHo5VsbDqVKjWMhEtpPngKZulCnVUgA2M0MZHPzU\r\n6+VXd5tsteEvUgKWYcCMMSjofddFjQ1KALCacI30SpjNjefYcyuq6+TjCncd\r\ns8Dkvxl7E9CZr1oRhpn+A5mgruRiA6MC9MdxHJPSuLR3xBhpgbBo+2EQuZCo\r\n53150+XwwZpv<PERSON><PERSON>+/QGeNagE8ZxkG9IXXf+/rtVtKPqKWcaKiM4dwVpD2Cz\r\n/zGg+bIuI7PymaYQmuUz+ugXO7Nz8Nva+4cGBH3wYQaBdsVFGQ/5DEBZ3yXL\r\nZsGzso7zJIkj32Lnaxv8VDSaMSSPHE4Yqpt+F9gpln6OOKnJqCL5Cq6wlFbs\r\nPHhjgDTs0YV5U4r8Gevc5EEJd6HW8Q9+dC1dder/7Fk2ZXq9iQhRquP4Jpoq\r\nHVG97HdazSQ7B/Gou0WQ0rCEfyTSvNwrdETpFvXS/qaiHNgKQ6bxPZM2aCzJ\r\nPHvS+PgigUMF+HSsKkmavjg8WRaJF62a3RTSmkR/9HioFzP2ZfDeLjBIK4o4\r\nHaFXcQOnMqxSiYqzEd6OZZEMG6kzH6rhNLnegYxB73QHg1NYJjo9enYIg2wJ\r\n35d6UPfn7ygDUKTlR9bwEM/UT7PHQKwaA88=\r\n=GRGK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.21.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.0_1676907073910_0.5405691151756469", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3a017163dc3c2ba7deb9a7950849a9586ea24c18", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4.tgz", "fileCount": 15, "integrity": "sha512-46QrX2CQlaFRF4TkwfTt6nJD7IHq8539cCL7SDpqWSDeJKY1xylKKY5F/33mJhLZ3mFvKv2gGrVS6NkyF6qs+Q==", "signatures": [{"sig": "MEQCIBJU/b3SBut7LP4FZwapcWz+xXZQThYVqhwMvFrxxZBYAiATnzQ6lNnQM7dCmmsKNJKI0EY2ENSB1l8zn8MjCZuvwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqF+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsvQ/+K5RoSXwSaFZ3H23BIum8JLqg4sno+WOQKg9xizEMDCYsJ7nQ\r\nmBqLrzpNul76pDYA2OFQU7xyQezbQW5pD+FdZssSdXJCvUYmMyc2HcT0lUF4\r\nhv8F2qSSgZRq+picc06E6aiNJd6ItMcNErLMie7kKl1oL1KrnxafZHBjTJK/\r\nuScWrVYqVU8Isgbfy576gr5FpSXyCjNtfHE/rRu5WPIuOHdjGA0n8msNy4D2\r\nvWf/7wRWZZ24+Kss9JbrGos5CiLXYkdGQdpxJ4hDqG21c55BCTKx8FA1PQJt\r\nS0PUYEMdPqvY5O5x7Y9KGRg6XWoj4naqwWx5pxcwCd/7KtNbdFShfu2fvmd0\r\no+LYxXmjDE2z5BovZXi5SBitmR7UjIoqvAc+rpRQi+a06jrUoBdt6llchD8Y\r\nWjtNcwLZnGAN8T1iAPBjQfVVNFtqCZmIyFqtFEg/2zm4E/GzgX0t4D10gp9Z\r\n4/2qB1L+6232A23k8xTS4viLRgdPwX7iJkV6U3XQvAiP8WmE/fCgw3QkNCJ2\r\n7KSQNgd6nAJ2JBprzBl4cBe9YMVel3eb02ADc4OqYjEmXOZe+S+2pHrbCr5+\r\n0nm/Q8F+mzKsfFjLo47GUp+THL/W5zzmq/SZWGwFwxbM8uvW4jjCb1H/O1P1\r\nYnepQBeflf6493j/5LavKxMEM5fGNmXCfyU=\r\n=jDSj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.21.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.4_1680253310780_0.6029259982747841", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.4-esm", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "92fdfe05f679f4dcd7bf2ee34147380a4a9282fb", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4-esm.tgz", "fileCount": 16, "integrity": "sha512-sX1hScoOncv+tk2B+3O8dqFWuqgTQ565Xza+J3hlCvxTLolrmYiCueLnElWhgxbm2wK/bHrQD7h93dNHEHWbBg==", "signatures": [{"sig": "MEQCIDRe2xvWe9z9IzyWQNDaTJAUhHsiI1qLmI/K2IcrNX7vAiAXUu6GGkIDQW/Ks95fN40U8b5bVfRrvdqt6X3mJBFWtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkrQ/9FlKC2bbC7lBNaZGwXkEGXuX8hk4DsFzr4W/hCmaGxYlaFC9i\r\n6nvSiqG9BR9b/4rX7grh/PHvPVqoKVvTs0CNFV2WgqiNdGwD6X+55CUBR3Ms\r\nMwAHHIAdlgZrYv9z7DlDRzSMlJjey+FhJxjdZWmA2nyWbDPLtqdvY+gIXQFZ\r\nSaHQ9+jBkCqLjMv50RpFA+N2GMTX4AMZWoXixlvFM1+QjmJ+7r81veOKVhaR\r\npH/yBokRtdiXltUSPtCgx/PzebxRX65F1im5MsE5QRGFTqmUAyoArjPfeVpl\r\n1O5Z6JJFhh9xg/i8i78nEjbyWRKiAk7HdS3q/4oa+u7adjFKW/Vz4FJOjFpT\r\nMXo8q2GrKIoJU1tsTeQT8wiWPkDXzHrQe2xCt1+k2smZr+ueEVgZcvvI+a2p\r\nToQCktSCoVst5xHl+BgSzqBo25a3214E95bBI5Gyj4k4qwzsNhYLJJZP3ZXt\r\nmukIYWup9z1tsBbvCLn0S2jAtJRehbyBxurRPOdOZjrdJi3TMhU/YYbDRSjB\r\nFICfDX4qZXyp4JvB4cX9AxK0lLnLzg9g+03jzdkBEHOA4d6eR1YGfHNzJBaE\r\n8S8h0OxT1ZCt8HUOIkJdjNRZTDH7mho8CjAzq7Lt8a92FedB8ZMUf4hQpI0O\r\nDxSmfKiERDRiZ1bdGDnhIMammbCjRcqhwck=\r\n=NrqX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.21.4-esm", "@babel/helper-replace-supers": "^7.21.4-esm", "@babel/helper-annotate-as-pure": "^7.21.4-esm", "@babel/helper-environment-visitor": "^7.21.4-esm", "@babel/helper-optimise-call-expression": "^7.21.4-esm", "@babel/helper-split-export-declaration": "^7.21.4-esm", "@babel/helper-member-expression-to-functions": "^7.21.4-esm", "@babel/helper-skip-transparent-expression-wrappers": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/preset-env": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.4-esm_1680617395838_0.8331468779323437", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.4-esm.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "308c22a6b243e80cda82669a029b6341367e1988", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4-esm.1.tgz", "fileCount": 16, "integrity": "sha512-fPGeTl0eJw1FInfVO0agvfVOSWDjnpZwCC9+RRQGtPuGiQfnoJ3v1XbZOv2wCwSQxJMxedfPLjkdt9krGk+JGw==", "signatures": [{"sig": "MEQCIA03Yki/OF6sS3ei/0jUVL++ez947SXDqNd4bLAi9zNlAiBQXnUZFUB0t6fRQX4r+AIK6XjFSwvnVtOAtMyfz7tm2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183999, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdlA/9GaHY6bDsWTutvdfeobVDPdKell5zQR+x/zbe+94oiNin6Ovg\r\nGCA5rbvxc2AHmT581dfvT6USmIMz8cdiKUctZbaSBXqpoG+zdSxeZcSWc4Ms\r\niQb7Bj6lFnJO7eWijCIIN+ajn6OtrbLj9m0F3o9dE2MikppjqEwyb4Sro/4z\r\n0JUAylj4b4U7dRCv3YeJlsax+70nAuMCQKSi8iiA0ATy3ZizXZAqnOQzTZgl\r\n0Eegco9dan+WLCvaDS9+k/LObKmJ+eONLWJdFu4PdJp6zNPjl1rWyutFAisv\r\n8FA9nP1hCXhk6yNZwW3hCsxuz9IPR7XZZAgp6EaPH79nDBRONHC790Hh8w8g\r\ne5DxDbibivjKzszx54WBJZP9YXaW2Zu17HSb5IpzNd4UPkUJJC2xbQK5jR2P\r\n9mTPF81plLhl+i+tUmDiw8UJf1KjeMx7L6YMSNeYRIKgYuWTLpHqcuPHFLjM\r\nVBz3cbIb8X/VtvdYrmgJ8tu2qC+hSqz8jdhgVvPuvq+96vUapQbKUfbWJwkW\r\nd6lLlOYLAyJO8zA+PSdw0BMPa39xCFoxbyEyxb7eySWgKv3If4CUeYPjGlfd\r\nD5YDJOrkjTOnDEvZHAsZFAiA7tzCurE78612BRTrN8GzK61MxqL9VtGjhXoH\r\n5W1+zgEYfOoJ0gss36f6SK7ySCJE4LkXlMU=\r\n=XOkF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "^7.21.4-esm.1", "@babel/helper-replace-supers": "^7.21.4-esm.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1", "@babel/helper-environment-visitor": "^7.21.4-esm.1", "@babel/helper-optimise-call-expression": "^7.21.4-esm.1", "@babel/helper-split-export-declaration": "^7.21.4-esm.1", "@babel/helper-member-expression-to-functions": "^7.21.4-esm.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/preset-env": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.4-esm.1_1680618113606_0.9638158386502498", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.4-esm.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "57886a40a239a3018ac9d00c25fcae33c51e3ea2", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4-esm.2.tgz", "fileCount": 15, "integrity": "sha512-rNMos7uINcTTYn4EMdys5t/f+RqrmKsIUc5BFsaDrAg0a21wbUTcSS0edlqtcW46FarbhFV32LUTWxCHg9kPDQ==", "signatures": [{"sig": "MEUCIQDyX4LHOIN6az4jyP05kkB2Y24DY7ejqInY1Bxjwsm4QgIgOb+/9NJwnQLVttnMuuIY+mjzgOg5Q2j87EJZiXCbCaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr51Q//Y2HYcuqzTKVFjK1aCeJ9slE21AiJNEEbD8BHlhauaNVV96Yv\r\n0akK3wNxpLs8Fhrl9y7GnolTyZykeA819UJwSNkloVf2uY7xep5ZwhCL0OWR\r\nJAOhVrvYpaXbTt+qxcHPC2sD7vvplv7Tt02sofqao896SlntwWlhimduM+17\r\nfmJM9hIUe5EBwihXQDLBI48a0IUqGqXL73MpqRoygzlLd9U3ax5cLK44IKn6\r\noOVnLJPUDUs+RayQ2j7iTjBy6kRo3yYE7pgHmSCpWX02PI17LAZZH+pTuPFG\r\n2rSC2R35ya/Lc3ALjIX8Y3gXIlx0yEMjoHcNP+eGmaiApqeqkEzOMGyPWVqn\r\nmw77MwmGBxH6BjIRu+78pRZ3ab5h9cx5iykxRNKAN2PmOTXV30pJg+ufQOLt\r\n94OC6oFCt1jTwZOYEGmPIU0qXEpv4Rem8D6UyTUVta1rX8uJq1LhWHS3X8kc\r\nFWiOz7AnbOLWhaq+mYlX/+lrCaI3dY0Urx/NOugMzseGNDeJfps3NNJh05I4\r\n040d7q+h5RHXat8ocARSIeIco8VwzqFsxt4sTB38PYeDsnybgm0IvyNUcXXD\r\nZTfW5c2OfArp0Q10ipNqc5InFYAGKGji/Ccvebz7c6V4h09bo23fjw2k2aVI\r\nRFmnkjq3GPp5b52AyrSVh1HObdezGTx57LQ=\r\n=CYwe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "7.21.4-esm.2", "@babel/helper-replace-supers": "7.21.4-esm.2", "@babel/helper-annotate-as-pure": "7.21.4-esm.2", "@babel/helper-environment-visitor": "7.21.4-esm.2", "@babel/helper-optimise-call-expression": "7.21.4-esm.2", "@babel/helper-split-export-declaration": "7.21.4-esm.2", "@babel/helper-member-expression-to-functions": "7.21.4-esm.2", "@babel/helper-skip-transparent-expression-wrappers": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/preset-env": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.4-esm.2_1680619197810_0.9207844247313643", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.4-esm.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7022fbae4a4493771e7d7c537f1db6efd1876993", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4-esm.3.tgz", "fileCount": 15, "integrity": "sha512-UT7CM7H7FShCbd0vK44Mbwiri23tn0Jw+Yf0QqDz5r2S1HdCdslKbepvIGxH0eKdN7Ph2ImvVzwrA93AdvvoGg==", "signatures": [{"sig": "MEYCIQCWm2WmPQfXUqgGLN44a3/eF1vbjnNdHzZxVC36upDYyAIhAL12wbL8lWh3ht9A+OQOwLOjAhZw03t+vhgn1jWox2o5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmXw/8DI9/4NGaYuVMHSMt1XFWqZWtsKWm7ZiEIbCJJEvW8dTcNbLU\r\n2Apju4IXEgOkDPzobd0KDxfX2lrIYsahjG/ByFJ8EVCAIWpk+54f8RxG0IJV\r\nmjFo8dOl8tmgt9x2SHjA12lthaW/bf5J7Dv5yWaXSO76QsxyFijW9FYQBcre\r\n8K3Oo+kdX9OhSTibs0otR0jd3VgM3naU2BNVocZlFV3xBx9++6H3+/cpX0Ng\r\nzvAA34A7NuKnOeNuhgyFSxV2VgDjY6+d0aCDYkQTl1AJ7KYJmOO/p9+HQ3kB\r\nCF8nlBX65l4njBbiBIA2XnhNAKBFR2G43EGuxsrxNzVdViNDv8l07bhfNKw/\r\nvBLyvnaKJ0+WvDnJFyx4IuKJyl1GqeR0s0yijaHYsEQ4t19O7oINgTjK/lJu\r\nlUJFwrirAqxDHVm0PfZA6zIEgSi9Tg26O+N4W0nOlYcMsfm//Wnb8sFQG/vA\r\nRS5Txtf7xwqFawcAgx3pI1H1hbApgTZVea7u8DcSsK3Ziqf4Gfu7fr6bMAme\r\nmUd+1DcO4SHxvWV9mWh5HqG1gR6EOUV6IM1Klpa8pWVP/tCO37faGOV//L4F\r\nxDQl6WR7nc1v3lnnixbDthhhc3+voHx0SLsAVdaFh3DkHbT4ZsAhCREYCVGL\r\nhMfJDw0qjppo4uRIUt8jhMVm58L7L+obHJI=\r\n=1LsS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "7.21.4-esm.3", "@babel/helper-replace-supers": "7.21.4-esm.3", "@babel/helper-annotate-as-pure": "7.21.4-esm.3", "@babel/helper-environment-visitor": "7.21.4-esm.3", "@babel/helper-optimise-call-expression": "7.21.4-esm.3", "@babel/helper-split-export-declaration": "7.21.4-esm.3", "@babel/helper-member-expression-to-functions": "7.21.4-esm.3", "@babel/helper-skip-transparent-expression-wrappers": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/preset-env": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.4-esm.3_1680620200528_0.14233425172725722", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.4-esm.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3b89eaf2e997ae7acfc17e6b7788ed37606df139", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4-esm.4.tgz", "fileCount": 16, "integrity": "sha512-4Gy/WPrFSJDaJGW4aVxqobzI40DgiBCHpBsXsRN/1SYvBK1q+Wwn0/fyJjf3Y3i//wOZEvcFsHycgexUPcEX8w==", "signatures": [{"sig": "MEYCIQDk1BRHsPbESn2DO/9GoYhdR5YEM6d2Qpl82ykLayYWuQIhAMv9PJoN64nXFlWHIBpDcDj/rAosrTuatRzXF37TxamP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMgRAAly3XWG65jD1YRjab4EvN3p356xAJfEQFYfXNvtzfY2q3Dr0Q\r\ngnLivdcRuLsn75kSlEVayr0rXAQEORQf1PZ3erQX7v8TX+LoSayteGf+OXfg\r\neyL7SFNBxf+FQ8J9crsN8ZPF4FfuLgVozB+9dHpYa0U9EoXguFAqxECpf5TF\r\nF14Rya4FLo/GEDHS8bja1iRPRTzK4A+rQ7d/zNfVR+3EJGR9lqBAityxXlCH\r\n1FBocmZEpEYkYjqNAwc5XpsyqQ3P8S6C+92muTePeJWYmystxOhAaem+tFUt\r\nt4bOngdMLv5AFlgJYVlLj+//tIuHM5cJ9CGaO4GQ1GTuNa7rwAm1v9zl3pva\r\njTHj/v6sjNB3qST49029IYYodbXgQxoejFfwE9w+1dP/o6TZvd3w6AaxZOP6\r\n1HXH4WzUNSopUu0iWubYKZ83hVuSp9ra59a6Xa/1SxgsOhceb6X2GYefB+cz\r\ndeqNqMa5LaGZwfUDFSvP88aKxWmFXw/9z4NvzNbUNSFuVM4XbT4gPFw2NFAC\r\nGrkRl02u6B1G7vdsEwp5rsCZlAQ9td+VfVI1fu8CYrHiCeg7oxBV1wsbERH0\r\nXLfrjnB3GOINHUB8NSYRsLnX/iM8q9EJL+PMDmyqFEcKDw98i0sO/XxxKjak\r\n3PWY8qRCxTau0T3bHMnMWcCGzHzHGrHv7Eo=\r\n=XiVD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@babel/helper-function-name": "7.21.4-esm.4", "@babel/helper-replace-supers": "7.21.4-esm.4", "@babel/helper-annotate-as-pure": "7.21.4-esm.4", "@babel/helper-environment-visitor": "7.21.4-esm.4", "@babel/helper-optimise-call-expression": "7.21.4-esm.4", "@babel/helper-split-export-declaration": "7.21.4-esm.4", "@babel/helper-member-expression-to-functions": "7.21.4-esm.4", "@babel/helper-skip-transparent-expression-wrappers": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/preset-env": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.4-esm.4_1680621228870_0.9582284343365362", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "09a259305467d2020bd2492119ee1c1bc55029e9", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.5.tgz", "fileCount": 15, "integrity": "sha512-yNSEck9SuDvPTEUYm4BSXl6ZVC7yO5ZLEMAhG3v3zi7RDxyL/nQDemWWZmw4L0stPWwhpnznRRyJHPRcbXR2jw==", "signatures": [{"sig": "MEUCIQCNZEmrEjOjaXp061vwwD7c2W9OMNAi0uPknk27KqxTdAIgHBgzXxF0qOvrWKInxTHg2715JC8e1SXGIXlg3S9N+u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocZg//XaBQ2QlJ/+VfzZe/bNlKvLayDB0r3JMWLVi9cN4c4Tl2Qw/W\r\nzmia3Pbu3VoJspPSVk+/wXwIfi3nHXq70yF2MwLDAqcHJsxWDcMDbSCtDcof\r\nxnL/JgRUk/xJQoPtkM43L4AWfVNwlKeuL2lR+AvwAakhQNsGSo0NqxMI17rR\r\n3jGSrBUdTUI0KlUJ5Lywr7Wy9OyF6kl/Crhjh9R3Hp1zpTolnYULWnca9SXe\r\ngMFFfoERRvf177BtSPS9oue1MLtcP75s85OvdRAh8xuGe0SitYkCdwebgK8r\r\nnqdrKN1czPj93q6Z0O7X99ePVGW7lhgoYucYJODirc0Zzhq7Kpw+STZFC7Lb\r\nNjScGdNPtKoZWkGlpxI1ymV5LVYiHV+ATthtJVeIe/ZtNcl/fNHIXrM7H7rk\r\nyTPPOs8XbPnyMgvLdEwQZAGeJ9wTcdiqAn+ke2Gdc2v6AayHID8hlrm/RBYC\r\nkphgYUNfDY9AqLyPeMwNPs0+W8JNT3f03i6G7KEmBwIhFYSZ6NMe7YFwZGXs\r\nOd4YUJny2R1Rr0mFj/SJ+NpTIXWSQZ6zwkkZozMhfYS9rvASqXaHCwpMzExy\r\nWmkdMWbSs8+LioBrh/7jSP/kjeNdZbiM6Vx4Iwh66jO8GAPoqA6bmBpygzY7\r\ntzmxqkzuQjFKRB9PY1oXha6weSmsAEhkzWw=\r\n=R09G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.0", "@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.21.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/preset-env": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.5_1682711429842_0.17763622773737087", "host": "s3://npm-registry-packages"}}, "7.21.8": {"name": "@babel/helper-create-class-features-plugin", "version": "7.21.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.21.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "205b26330258625ef8869672ebca1e0dee5a0f02", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.8.tgz", "fileCount": 15, "integrity": "sha512-+THiN8MqiH2AczyuZrnrKL6cAxFRRQDKW9h1YkBvbgKmAm6mwiacig1qT73DHIWMGo40GRnsEfN3LA+E6NtmSw==", "signatures": [{"sig": "MEUCICLUdShqc/0bS3LM79eK8BLcA0AlpidpVzEJE8vZirXOAiEA6CgjOD9RATK7RilyZkD1jDcvuqd0Py3aKkmGTxq/4JM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188950}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.0", "@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.21.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.21.8_1683040509497_0.9461512971354193", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0d1eaeb45801696842391c5226bcd648e0293716", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.0.tgz", "fileCount": 16, "integrity": "sha512-7ayl01xtLFm/6n41nMIc0wIglPrAab56sxcsz+V6zA+q0aWcc7ycimmdRrSUSq55uQYHEssVA51/d63P4dMkGg==", "signatures": [{"sig": "MEUCIQDZBOBhXjbEbL/S9de9aeP8OGwMmjXZP0G0TANRLXLbvgIgc/tAsSAAhsbhHIKMtzvHSb7AYqMGPdqUJyGUcMIdVho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189002}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.0", "@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.22.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.0_1685108744996_0.4369730310070188", "host": "s3://npm-registry-packages"}}, "7.22.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ae3de70586cc757082ae3eba57240d42f468c41b", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.1.tgz", "fileCount": 15, "integrity": "sha512-SowrZ9BWzYFgzUMwUmowbPSGu6CXL5MSuuCkG3bejahSpSymioPmuLdhPxNOc9MjuNGjy7M/HaXvJ8G82Lywlw==", "signatures": [{"sig": "MEUCIQCK1rX4OH1qZPfo3hch70y+eP3BF3zUnW9Grot1OibgiQIgcjGUN04N0Y3XlU528jjfi9HH9WRodZrecTchHLsBO74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189286}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.0", "@babel/helper-function-name": "^7.21.0", "@babel/helper-replace-supers": "^7.22.1", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.22.1", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-member-expression-to-functions": "^7.22.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/preset-env": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.1_1685118896739_0.09867386248034316", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2192a1970ece4685fbff85b48da2c32fcb130b7c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.5.tgz", "fileCount": 15, "integrity": "sha512-xkb58MyOYIslxu3gKmVXmjTtUPvBU4odYzbiIQbWwLKIHCsx6UGZGX6F1IznMFVnDdirseUZopzN+ZRt8Xb33Q==", "signatures": [{"sig": "MEUCIHtSJ3rs8P2RRdkeKM79igWWrygxfXSdQZVbn8q0HIGJAiEAxmNbhlVSPOitbC/bGFAoFCLwJfO6FXoMv4siZXOSaI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189286}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.0", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.5_1686248504018_0.5200007855574711", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "58564873c889a6fea05a538e23f9f6d201f10950", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.6.tgz", "fileCount": 15, "integrity": "sha512-iwdzgtSiBxF6ni6mzVnZCF3xt5qE6cEA0J7nFt8QOAWZ0zjCFceEgpn3vtb2V7WFR6QzP2jmIFOHMTRo7eNJjQ==", "signatures": [{"sig": "MEUCIQCkeKDTDD5Kk66ZdDcjf6M9WooQn2xZcPyU0HWDOtEx+gIgfEl463Dt9RJIrQS8xuaT7XN6+dkcFdD48zPcnONZYbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189354}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"@nicolo-ribaudo/semver-v6": "^6.3.3", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/preset-env": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.6_1688456938773_0.7554797430985136", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c36ea240bb3348f942f08b0fbe28d6d979fab236", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.9.tgz", "fileCount": 15, "integrity": "sha512-Pwyi89uO4YrGKxL/eNJ8lfEH55DnRloGPOseaA8NFNL6jAUnn+KccaISiFazCj5IolPPDjGSdzQzXVzODVRqUQ==", "signatures": [{"sig": "MEUCIH4RqTbhdjqTm/TbmbKj/W4/Wc50DPndQt8Fi8rodO6aAiEAwhPH4ttseeQC1Xx5iCujpuQhTP/Wi12ZL51AS+ZVd04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189196}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.9_1689180827549_0.20989106547931158", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b6bb7bb2bc4c471698e3a63b43fcfa9cf50484f4", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.0.tgz", "fileCount": 15, "integrity": "sha512-7LmjDbtbn2BFn/o+mhGTZyRUyCV3GsSceVvO2iTIGmwqdt5ECsYzvBynUJrdi2qmP7b2D2hwRTl+mYpnXMhMkw==", "signatures": [{"sig": "MEYCIQDBDohpaCdMtR48zXUQsOgE5rdMLp2rYJNeMfVwuGy/FQIhAI6ZtWmW5+LSkP9GlZCi2BynrtNAJwjA8nus3r9Y8dQM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330477}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.0", "@babel/helper-replace-supers": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0", "@babel/helper-environment-visitor": "^8.0.0-alpha.0", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.0", "@babel/helper-split-export-declaration": "^8.0.0-alpha.0", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/preset-env": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.0_1689861626568_0.25936313511299147", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "20e0c14fb8039ee75107f1a2ad19404fdfa97a21", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.1.tgz", "fileCount": 15, "integrity": "sha512-TtP58yQ9u0YOViotOvCw9MGC7UjSZAbJaLbMAKt37V57GYTs+db93b4nJWatuZBTafspopU/SlAWgDEHf7E2bg==", "signatures": [{"sig": "MEUCIQD5yiRtvpXoPV/QlxQkkkJ3rLgI5NI1Pp+qzKL3HeZywQIgbWYxIJgDAJ2hGGYfHklOLs80I9dVIPnT/DvN9FEISbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330477}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.1", "@babel/helper-replace-supers": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1", "@babel/helper-environment-visitor": "^8.0.0-alpha.1", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.1", "@babel/helper-split-export-declaration": "^8.0.0-alpha.1", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/preset-env": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.1_1690221181039_0.7495511961095216", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "dd2612d59eac45588021ac3d6fa976d08f4e95a3", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.10.tgz", "fileCount": 15, "integrity": "sha512-5IBb77txKYQPpOEdUdIhBx8VrZyDCQ+H82H0+5dX1TmuscP5vJKEE3cKurjtIw/vFwzbVH48VweE78kVDBrqjA==", "signatures": [{"sig": "MEYCIQCIJ/2c+itp2POTzf2paNsiZhfHS7P0eeIaVikjv0nu5QIhAPm1KoTN88HE8jhVKGx2PdJE/6acOw+Cfmtwdq2k+wzu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192510}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.10_1691429110076_0.3338593177809921", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "54fb9a752b07f6e4c1a94ab150c7788b6ebefdf7", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.2.tgz", "fileCount": 15, "integrity": "sha512-yQNlsu4oz9elco713jnQDtdR2Mdbn1hyzP6v4r0EReVIlj09fQebm2SHu8c1ulfbFQiI0nPn/55K2L1czEhu+g==", "signatures": [{"sig": "MEYCIQCkMLZ9w1PtKTpU2hMG+gmGiJRlj6qF5yAxVBYjZ/k8yQIhAIAqJOMc3II54qgOkIYaiuevPZsDaLhsCpaVdLZlF9mJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330419}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.2", "@babel/helper-replace-supers": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2", "@babel/helper-environment-visitor": "^8.0.0-alpha.2", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.2", "@babel/helper-split-export-declaration": "^8.0.0-alpha.2", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.2", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/preset-env": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.2_1691594121690_0.4415680515408238", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.11", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4078686740459eeb4af3494a273ac09148dfb213", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.11.tgz", "fileCount": 15, "integrity": "sha512-y1grdYL4WzmUDBRGK0pDbIoFd7UZKoDurDzWEoNMYoj1EL+foGRQNyPWDcC+YyegN5y1DUsFFmzjGijB3nSVAQ==", "signatures": [{"sig": "MEUCIQCDpVsmQOYvmEUFL+2o9XyNKBYntEnO3tZUmGed+eSMOAIgJ9u/I9SzTngpksfbeaGQ6A/zX+A4MjCYOjn8G3QMPOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192452}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/preset-env": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.11_1692882516480_0.01633264351642727", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-create-class-features-plugin", "version": "7.22.15", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "97a61b385e57fe458496fad19f8e63b63c867de4", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.15.tgz", "fileCount": 15, "integrity": "sha512-jKkwA59IXcvSaiK2UN45kKwSC9o+KuoXsBDvHvU/7BecYIp8GQ2UwrVvFgJASUT+hBnwJx6MhvMCuMzwZZ7jlg==", "signatures": [{"sig": "MEUCIBwRWY+TGC/oAOT+h2BwyTPTSutVgMz/3TvAIYi4JZHCAiEA/REZNpYM/ROxJuzd2EJWru1FnXrEqIq77peJq+fd/94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192512}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.22.15", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/preset-env": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.22.15_1693830319887_0.5869902203441311", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bd10360ad6dc5defb810cda8d8a9da754298e507", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-isyLbojrWdHQH52oiihwWeopboy3OiHT2FwDdfNByIFRW4pDpN7C43/NdR2uQQ8RclHx8h2Ktfu5k5cn6CcKPA==", "signatures": [{"sig": "MEYCIQDrda+YYw2uHE3Zb2yE3zntN/URJcEvw+SiHexBiYVWbwIhAP/9+2VY3+mapHH5uT0NB4JK8lXCNL5uZ36FlJagClTp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176575}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.3", "@babel/helper-replace-supers": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3", "@babel/helper-environment-visitor": "^8.0.0-alpha.3", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.3", "@babel/helper-split-export-declaration": "^8.0.0-alpha.3", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.3", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/preset-env": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.3_1695740255361_0.5847433294467359", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f4e6a40b9e9c60fb453ab29406a23e6dee593cb2", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-qN2t0POACJp3p+WUHJFQJpOueFUsF5MBF6i4WXkMtep6NJFTc3YMtNZBJ85uLNFeIYizD6qiWUu5Q4bz3hnZeA==", "signatures": [{"sig": "MEYCIQC5/cyPmmApZNL0rtBjRnSJcArmABTcbGc4qCCVUmAtzwIhALqd4adhT//uboag0rCQ38dKZrM3aM+KxQwQxNMwRpOw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176575}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.4", "@babel/helper-replace-supers": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4", "@babel/helper-environment-visitor": "^8.0.0-alpha.4", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.4", "@babel/helper-split-export-declaration": "^8.0.0-alpha.4", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.4", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/preset-env": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.4_1697076408281_0.698685493851444", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.23.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2a8792357008ae9ce8c0f2b78b9f646ac96b314b", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.23.5.tgz", "fileCount": 15, "integrity": "sha512-QELlRWxSpgdwdJzSJn4WAhKC+hvw/AtHbbrIoncKHkhKKR/luAlKkgBDcri1EzWAo8f8VvYVryEHN4tax/V67A==", "signatures": [{"sig": "MEQCIAwn1C/H6repP23iXpaKeK8AyHe3RIISzyy5xxs/BNvgAiBuN9dY2pFCH+dvyUfewi85T61D9rnpGJ0ol5TH9q35Mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194768}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.23.5_1701253537691_0.9576158548263272", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.23.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b04d915ce92ce363666f816a884cdcfc9be04953", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.23.6.tgz", "fileCount": 15, "integrity": "sha512-cBXU1vZni/CpGF29iTu4YRbOZt3Wat6zCoMDxRF1MayiEc4URxOj31tT65HUM0CRpMowA3HCJaAOVOUnMf96cw==", "signatures": [{"sig": "MEQCIBj+7YI9Ri7e496h7FXE3c5U06aZ6kfsO+xlVFeCgkGiAiB48W7SFGHrZ7tkMa/Lznp3ZfFwJ1vBkEkAfSP/bKOfTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197493}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.23.6_1702300196619_0.24218267282591754", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "79f0d632e3756ec27396a4bab640a3c4cdc6ecf2", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-sBC7+yGMogmkVrW2VxqUcgJsfP6bQlEPH6evqYswEbLd8CPbYEGq56DV2nu7hJtc1sOXpW5hOOr2LHn35e8tcg==", "signatures": [{"sig": "MEQCIE4I9IHl1qj5XKwGxhZm7kyJLe25qaZiwBt8uOgGOW7vAiBv3yrLpcWbBGvdxnroMHKkZa6wsqAAjW93XoMyFrtd7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178041}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.5", "@babel/helper-replace-supers": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5", "@babel/helper-environment-visitor": "^8.0.0-alpha.5", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.5", "@babel/helper-split-export-declaration": "^8.0.0-alpha.5", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.5", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/preset-env": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.5_1702307981638_0.7567330568509998", "host": "s3://npm-registry-packages"}}, "7.23.7": {"name": "@babel/helper-create-class-features-plugin", "version": "7.23.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.23.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b2e6826e0e20d337143655198b79d58fdc9bd43d", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.23.7.tgz", "fileCount": 17, "integrity": "sha512-xCoqR/8+BoNnXOY7RVSgv6X+o7pmT5q1d+gGcRlXYkI+9B31glE4jeejhKVpA04O1AtzOt7OSQ6VYKP5FcRl9g==", "signatures": [{"sig": "MEUCIQD/5HTzyg7IhUVIjtcnKiJHIpuBtsCw2zY69zZQTYdZyQIgXb+PR8L2E9t/V/cLK93E7l3BnY7GLDnXU2Jis4hvRTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325297}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.7", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.23.7", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.23.7_1703880088168_0.8428383519708493", "host": "s3://npm-registry-packages"}}, "7.23.9": {"name": "@babel/helper-create-class-features-plugin", "version": "7.23.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.23.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fddfdf51fca28f23d16b9e3935a4732690acfad6", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.23.9.tgz", "fileCount": 17, "integrity": "sha512-B2L9neXTIyPQoXDm+NtovPvG6VOLWnaXu3BIeVDWwdKFgG30oNa6CqVGiJPDWQwIAK49t9gnQI9c6K6RzabiKw==", "signatures": [{"sig": "MEUCIBlB5vYkJVjMncw8a02PrUKqV8gNn5Z74j2INNrKFaQaAiEA2xIqVHgJ1mTvnLnhNLIRbpGjpk95pR4lTR85zdO3fuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 328981}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.9", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.23.9", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.23.9_1706201866636_0.2890576944241259", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5a2fc5351317d4f3b8bdd09fb9724539e14c3df5", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-b74cp6H57e3aqM4Xyf6VIjy+FuIZrTNbif8y9QeC6JybHPvzg5ju3vQ0GFp5K4J2hSvjJwp39kqxz9IrHyz7hA==", "signatures": [{"sig": "MEUCIHdItJg/dh9gL2t/tHjoPQfy03ew2XkJS5L/l0nAETksAiEAqQ95PhFvecEnSwAKaCfZYAEEFrnYq8oBcpT33UYo+RU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 315668}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.6", "@babel/helper-replace-supers": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6", "@babel/helper-environment-visitor": "^8.0.0-alpha.6", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.6", "@babel/helper-split-export-declaration": "^8.0.0-alpha.6", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.6", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.6", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.6_1706285680563_0.7033604505465114", "host": "s3://npm-registry-packages"}}, "7.23.10": {"name": "@babel/helper-create-class-features-plugin", "version": "7.23.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.23.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "25d55fafbaea31fd0e723820bb6cc3df72edf7ea", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.23.10.tgz", "fileCount": 17, "integrity": "sha512-2XpP2XhkXzgxecPNEEK8Vz8Asj9aRxt08oKOqtiZoqV2UGZ5T+EkyP9sXQ9nwMxBIG34a7jmasVqoMop7VdPUw==", "signatures": [{"sig": "MEUCIQC9rJeKcEdMz25XAZoxV8xZcW/p76LXBARje8mOj/QcegIgChZ/qPLXhNbqYTrd1xYVo4BROWNQ7KgmeO5oPeqOBr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336969}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.23.9", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.23.9", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.23.10_1706701330587_0.47734735476658696", "host": "s3://npm-registry-packages"}}, "7.24.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fc7554141bdbfa2d17f7b4b80153b9b090e5d158", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.0.tgz", "fileCount": 17, "integrity": "sha512-QAH+vfvts51BCsNZ2PhY6HAggnlS6omLLFTsIpeqZk/MmJ6cW7tgz5yRv0fMJThcr6FmbMrENh1RgrWPTYA76g==", "signatures": [{"sig": "MEQCIBmGWJ3PsHPIS4yteoQaz9D6KeLdI2BC/A9nMF5twb/uAiAFiwnKczDvejtTOmAJuSc79TeIyrXPzJ7FKqlP+DVI2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 390703}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.0", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.0_1709120852085_0.9102170667222274", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b1171af1b14ea695cf4818f12cf464e3886a9c5d", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-BN0nrlVSNPPDFePNNdufiDy3ARqfDfSdytKtqZYNQU7NEreZZe4zS30zDdd49UvE47HgCsrTLeci0FXnO2wtCA==", "signatures": [{"sig": "MEUCIQDf9JnVwWLc+u9s6nfsOGpHMiPINmNkW+43Bm5aB+yY5gIgNEOJeb+gz8zgyEIUUG2f0F9nAPLxKRt21P7sVo8WfE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356201}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.7", "@babel/helper-replace-supers": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7", "@babel/helper-environment-visitor": "^8.0.0-alpha.7", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.7", "@babel/helper-split-export-declaration": "^8.0.0-alpha.7", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.7", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.7", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.7_1709129142570_0.3326633690287297", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "db58bf57137b623b916e24874ab7188d93d7f68f", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.1.tgz", "fileCount": 17, "integrity": "sha512-1yJa9dX9g//V6fDebXoEfEsxkZHk3Hcbm+zLhyu6qVgYFLvmTALTeV+jNU9e5RnYtioBrGEOdoI2joMSNQ/+aA==", "signatures": [{"sig": "MEUCIE8q7+r9nAUKX4cPnNYucnO9FSfrUyTzptVFdKz9+u49AiEAquCylkP1EyvbfVmk1NPZkb4AB5ZugcKp0jyqPqWmiS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430041}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.24.1", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.1", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.1_1710841757658_0.7255226054843107", "host": "s3://npm-registry-packages"}}, "7.24.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c806f73788a6800a5cfbbc04d2df7ee4d927cce3", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.4.tgz", "fileCount": 17, "integrity": "sha512-lG75yeuUSVu0pIcbhiYMXBXANHrpUPaOfu7ryAzskCgKUHuAxRQI5ssrtmF0X9UXldPlvT0XM/A4F44OXRt6iQ==", "signatures": [{"sig": "MEUCIQCN1hZnCpwGVscWTHuKuIMPDdXy87DjKdUanILKJs6kagIgTCfZSpU6URrSmacm00F0H05cwd4Y9Cwqs35pKMcBj+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 434334}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.24.1", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.4", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.4", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.4_1712163224995_0.17228527258459736", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a9f6d09a63b66ba0e53b0e82b94b6b093ab57c4c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-86MgJqf/ZmgIqd2n2zFKAHQKg1wmcIjbuA5zG8N+ClAJFeiAsUoV/6IO/mw1iB2hLhUst/YT9Xo/5Nyof771tA==", "signatures": [{"sig": "MEYCIQCuCCWWuZl9gG8zLPJCTeG9XAifIT3TEGsj/m19CJpcjQIhAOspsYUKEQotcpx+ic9dUVhqHNPJa36wgBzJQOuEIwTA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400278}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.8", "@babel/helper-replace-supers": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.8", "@babel/helper-split-export-declaration": "^8.0.0-alpha.8", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.8", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.8", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.8_1712236818471_0.8818093631532296", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7d19da92c7e0cd8d11c09af2ce1b8e7512a6e723", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.5.tgz", "fileCount": 19, "integrity": "sha512-uRc4Cv8UQWnE4NXlYTIIdM7wfFkOqlFztcC/gVXDKohKoVB3OyonfelUBaJzSwpBntZ2KYGF/9S7asCHsXwW6g==", "signatures": [{"sig": "MEUCIQDJAx8w1XqAa3O+aoXhUrtwwt9WaBqcRmKulUsz9NTGwgIgWHxVKXmQd54kaM9FZpU5+0sstzDJteArZbSHVhilpx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 502371}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.23.0", "@babel/helper-replace-supers": "^7.24.1", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-split-export-declaration": "^7.24.5", "@babel/helper-member-expression-to-functions": "^7.24.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.5", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.5_1714415665347_0.996474234399191", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c50b86fa1c4ca9b7a890dc21884f097b6c4b5286", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.6.tgz", "fileCount": 19, "integrity": "sha512-djsosdPJVZE6Vsw3kk7IPRWethP94WHGOhQTc67SNXE0ZzMhHgALw8iGmYS0TD1bbMM0VDROy43od7/hN6WYcA==", "signatures": [{"sig": "MEUCIQDTscSqulLG2Ixo1+aMmrOV3XyWkJw9usSM5zcn9q3pzwIgQ1wp+wAQ7KnYrsFK1K1X/dTTbe2H9OxZOkKKzZhN0go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 507100}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.24.6", "@babel/helper-replace-supers": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6", "@babel/helper-optimise-call-expression": "^7.24.6", "@babel/helper-split-export-declaration": "^7.24.6", "@babel/helper-member-expression-to-functions": "^7.24.6", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.6", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.6_1716553509264_0.7592962545530744", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a4e4346bc6bbb45288dbb8663a497f29d5ab1c1e", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-hS+aieoClMmIefwXhvwJ7JhI/hZdVIYDEB/QGgt9+TC1ZyjCFGoMxYLGidy8PrU8MqMYWrwBTTbOJplGR7vtoQ==", "signatures": [{"sig": "MEQCIC19CevS2XhCl8akRH3682pbS5WhFulZex3rMpmwCio4AiBET6uhvbMqnegq1RMIXKQx8xjjDlVCjTF7h0OdUeJF9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 474622}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.9", "@babel/helper-replace-supers": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.9", "@babel/helper-split-export-declaration": "^8.0.0-alpha.9", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.9", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.9", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.9_1717423538798_0.04655567251228976", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b920b71d2d732bb6cc2a04bca019cbd124377a52", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-RILDBSGyIFQr0My6cHFcrOyDsc6A0X8JO00ZRax13qeK6DqYRpKOJ3yRVOBGZIqqaDTrAr8Y70GFhusuIC42Ag==", "signatures": [{"sig": "MEYCIQCiScx1RbK1hAg3Nbljct+ywV758ybi+oVvam1nGhvS5AIhAKEbZayNp8FriyTCqYvAd553yP4Wv+mWHZKS4kh0xWaA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 474640}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.10", "@babel/helper-replace-supers": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.10", "@babel/helper-split-export-declaration": "^8.0.0-alpha.10", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.10", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.10", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.10_1717500039408_0.7749731169259575", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2eaed36b3a1c11c53bdf80d53838b293c52f5b3b", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.7.tgz", "fileCount": 19, "integrity": "sha512-kTkaDl7c9vO80zeX1rJxnuRpEsD5tA81yh11X1gQo+PhSti3JS+7qeZo9U4RHobKRiFPKaGK3svUAeb8D0Q7eg==", "signatures": [{"sig": "MEQCIC2M6SqqOjexNRVQ5Ip64RbfCeiXTRsDuFK3BEm0aYYHAiA4RCAvlsjvTRfxC8DNOBUYoVurTORctD8VykAwaRN67g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 507143}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.24.7", "@babel/helper-replace-supers": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.7", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.7_1717593353224_0.29591163296851675", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.11", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ffaa752131e43f9f474a58cb6bc6f3e18abedb81", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-TD4uAHJxnAQ31DZ8rpd6Us2egyXl3AuN+DBV3Cht0NC3tsf0kkF5wcSquHwiohNhqcYRUWnxMvqBmrn+YfTJFg==", "signatures": [{"sig": "MEYCIQDrkGIhldHkbWyWzYezQRo7xoJr98+YASElBp5JC/txkgIhAO1P5UjjvOww8BTaVH7QwdO8c192cGvz5mx/7igmnVa/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 474529}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/helper-function-name": "^8.0.0-alpha.11", "@babel/helper-replace-supers": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.11", "@babel/helper-split-export-declaration": "^8.0.0-alpha.11", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.11", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.11", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.11_1717751763832_0.17485158862660177", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/helper-create-class-features-plugin", "version": "7.24.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "47f546408d13c200c0867f9d935184eaa0851b09", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.8.tgz", "fileCount": 19, "integrity": "sha512-4f6Oqnmyp2PP3olgUMmOwC3akxSm5aBYraQ6YDdKy7NcAMkDECHWG0DEnV6M2UAkERgIBhYt8S27rURPg7SxWA==", "signatures": [{"sig": "MEUCIEMO39rILEC8Pv3Fi7C26nOco+swzrxxqy9oCP+vgfZ/AiEAlH4GED6+LjcuCkTKQK/4jNjisBHJRzhE0sRm6k0yp/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 503517}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/helper-function-name": "^7.24.7", "@babel/helper-replace-supers": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.8", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.8", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.24.8_1720709697315_0.9998725239311597", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.25.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a109bf9c3d58dfed83aaf42e85633c89f43a6253", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.0.tgz", "fileCount": 19, "integrity": "sha512-GYM6BxeQsETc9mnct+nIIpf63SAyzvyYN7UB/IlTyd+MBg06afFGp0mIeUqGyWgS2mxad6vqbMrHVlaL3m70sQ==", "signatures": [{"sig": "MEUCIQDdtWQr/Isle1TQCk5lbx2sUtmtJMkkpv2TIJiR5Le1agIgD+BtMZiG+HJKHW5/TAzbO1TtQM8PcSWtWvC9CxqbXK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 505751}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.25.0", "@babel/helper-replace-supers": "^7.25.0", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.8", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.24.9", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.25.0", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.25.0_1722013172200_0.2116009022915888", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.12", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "644253833c22754a50b567fb6533e0bd3832c5ea", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-F4L0m+xssrndfgPmUYrYTg2L5hPtDH9omYTpZj/QKJHYRxuRXwgP4o+OQx9xofmaZucMWNHs+ibEzrt9/jMt8A==", "signatures": [{"sig": "MEYCIQCSg6RSvhui5u+E/JVK/RLbX16TI7RsATKbv1daSiIWpAIhAO12mfiVb8saAX/px+35Q6hSaCbqAMRT5nBLmf4yClea", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470465}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-replace-supers": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.12", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.12", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.12", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.12_1722015238133_0.9315003790085574", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/helper-create-class-features-plugin", "version": "7.25.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "57eaf1af38be4224a9d9dd01ddde05b741f50e14", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.4.tgz", "fileCount": 19, "integrity": "sha512-ro/bFs3/84MDgDmMwbcHgDa8/E6J3QKNTk4xJJnVeFtGE+tL0K26E3pNxhYz2b67fJpt7Aphw5XcploKXuCvCQ==", "signatures": [{"sig": "MEQCIFSR1olcxTXJgOnoeC3zqWms0Z/KtWMddD+Ms7uxmsoJAiBOch2ZyLngoasN4NVFeefyhbdQ86CchPGrO5m9w3xCpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 505636}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.25.4", "@babel/helper-replace-supers": "^7.25.0", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.8", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.25.2", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.25.4", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.25.4_1724319275607_0.9196942316958814", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-create-class-features-plugin", "version": "7.25.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5d65074c76cae75607421c00d6bd517fe1892d6b", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.7.tgz", "fileCount": 19, "integrity": "sha512-bD4WQhbkx80mAyj/WCm4ZHcF4rDxkoLFO6ph8/5/mQ3z4vAzltQXAmbc7GvVJx5H+lk5Mi5EmbTeox5nMGCsbw==", "signatures": [{"sig": "MEUCIFA/KSiAE1JivmWJYTmykR3PJGMSRkBa5OVvqI2nc2FGAiEAxW6iR2yh2GmeBsXftH80kt1wAsYVi7GBZAqgH7CCpfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 513575}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.25.7", "@babel/helper-replace-supers": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7", "@babel/helper-optimise-call-expression": "^7.25.7", "@babel/helper-member-expression-to-functions": "^7.25.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.25.7", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.25.7_1727882127439_0.2507452206912131", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-create-class-features-plugin", "version": "7.25.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7644147706bb90ff613297d49ed5266bde729f83", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.9.tgz", "fileCount": 17, "integrity": "sha512-UTZQMvt0d/rSz6KI+qdu7GQze5TIajwTS++GUozlw8VBJDEOAqSXwm1WvmYEZwqdqSGQshRocPDqrt4HBZB3fQ==", "signatures": [{"sig": "MEUCIGgXw5H0WspJHYR/iBu+ZBhWxfDC+OF6Jqdk/eIqyGe/AiEA+2vF73bTQi5I2VsBclnSWth4MiSIZib8mP8lKZ93/Ho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440822}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.25.9", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.25.9_1729610503611_0.1111386289614229", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "561d14d6cc29c32b4f626d05dad838c0776f5540", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-E0gZWZsfkBWyaBYrmF9bpYu9TF6bYbnocbSfdZSp6/S7bK9aTRmPNxFs+1p2+fpxlg4slZwQNMxjv/UY8QUc1w==", "signatures": [{"sig": "MEYCIQDronLBqLF1jKwXe9CxGGYL7btYvnntEOFFabwFnna3HgIhAIcfgCY9k+4CVmnsojPh/5Q/EKJsGeOL48+phDYW45gi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405663}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-replace-supers": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.13", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.13", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.13", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.13_1729864483854_0.6085164187262633", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.14", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7836369e4355bb5568f8bb41d72d9828c4b45039", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-p9X+EKqPgzEfaALGMj6PdYs9SksQo/xhfZroztfbMPOq1ZM1rcu/2GCVB78sovyig6V4J4x6VelncjNCv63D+A==", "signatures": [{"sig": "MEUCIQCIPT+cd/FSkLY98ISFMktzIQa69Bl1iUeGHP4Hmk0dOgIgXjxBCl7f5FVB7Ow5bPUpzKmn+uWpZWzXQYj3RJffjMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405663}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-replace-supers": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.14", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.14", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.14", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.14_1733504072562_0.5741733828586062", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.15", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ace1daed3e1376bde8e7d84ba658383b164c403a", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-EyKZQ68WkwHteYE/6ZjsSwSWu4we1W2wbk79xZa+dWX3CpQSygWGKP+tcnDjRd2qNcfcBYnTBZ9XHJNtmxf8gA==", "signatures": [{"sig": "MEQCIBm4/SYSaCZAOvOXg/mbEu5g2hPvEc00IG/ADnzSMMUjAiBXf/vkNdfNSAGod4lxjHMEmX8zI9oflIBmf64RLFpDhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405663}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-replace-supers": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.15", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.15", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.15", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.15_1736529901549_0.38880751563833127", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.9": {"name": "@babel/helper-create-class-features-plugin", "version": "7.26.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.26.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d6f83e3039547fbb39967e78043cd3c8b7820c71", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.26.9.tgz", "fileCount": 17, "integrity": "sha512-ubbUqCofvxPRurw5L8WTsCLSkQiVpov4Qx0WMA+jUN+nXBK8ADPlJO1grkFw5CWKC5+sZSOfuGMdX1aI1iT9Sg==", "signatures": [{"sig": "MEUCIQCejppPa3Xv09ZCLEjLYrHIgVruiAmL5iIK3sJVO3NXdgIgO6Ymg0apNnwiiNC4ajegyPW3ku4qhdZ/BMb1jWKo4pU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 441049}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.26.9", "@babel/helper-replace-supers": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.9", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.26.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.26.9_1739533691598_0.37387843394205533", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "146a0eb166d4c05c458704c2c437b5f019b7f63c", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-tSuHHzvz/LNegO/pZZD7iaaR0UnvTrSIzn0tEpdP/+6oiaMtDphxwcJNYZSahBdQKRIk2917s3Nx1HU6XdjYvA==", "signatures": [{"sig": "MEUCIQD6d+F9SNAkgqLDr0xvIF3ydMC6j9xkXZKlAf74wf5RHQIgalcKt9TjQGTk9ohIVCJn9P5gWVuDenpsNrpRYBLnqLY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 399164}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-replace-supers": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.16", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.16", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.16", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.16_1739534377037_0.14213590272005194", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-alpha.17", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a85ca66b3968eb8b4953a4c75d272b7d9d93f6e3", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-PIyFCWL+81mwHjzzvyiu/Zhw9/RkGKdgyZ6Rs8j03jWpcY62QTvVZ2P51cCA7NWK12K0MIW1FPZmBDkKbgmI/g==", "signatures": [{"sig": "MEUCIE3eSm7AFPoEypl3LeYk4m7i3MBYxhjBS1C3jllh4u97AiEAt71DPssZ9qKxTlA6dXe+N635Tb1fn1yjFSB2ISp4gRQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 399164}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-replace-supers": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17", "@babel/helper-optimise-call-expression": "^8.0.0-alpha.17", "@babel/helper-member-expression-to-functions": "^8.0.0-alpha.17", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.17", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-alpha.17_1741717530416_0.886892521869648", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/helper-create-class-features-plugin", "version": "7.27.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "518fad6a307c6a96f44af14912b2c20abe9bfc30", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz", "fileCount": 17, "integrity": "sha512-vSGCvMecvFCd/BdpGlhpXYNhhC4ccxyvQWpbGL4CWbvfEoLFWUZuSuf7s9Aw70flgQF+6vptvgK2IfOnKlRmBg==", "signatures": [{"sig": "MEQCIGiik+2eq8psuUamIUey7Fjzs6j6uQte84Wlf8TCJDNCAiBvG3wzmMp+G+9Na/aKY7K1J0msOZMZUwedi+4Awnz8dQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 441091}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.27.0", "@babel/helper-replace-supers": "^7.26.5", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.10", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.26.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.27.0_1742838113326_0.8392687286475138", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-create-class-features-plugin", "version": "7.27.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5bee4262a6ea5ddc852d0806199eb17ca3de9281", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "fileCount": 17, "integrity": "sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==", "signatures": [{"sig": "MEUCIDApkXJpP5ZBO1wjxU9pIYqqaC7uTNF1VJrh29iJqTMOAiEAuV9EG2KXEZmjyFqdsykuEWCgeecxFJ/FG12Lr5Z+D5E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 441090}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^6.3.1", "@babel/traverse": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.27.1", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_7.27.1_1746025765461_0.9008487910021303", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-beta.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-class-features-plugin@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "818f578db3c03819e8290f705bab06b6b931d034", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-8Nw0mk4kZfjsvv5yYWxv8gwNGjdWrXsg91S7BtnxTnBbH3TZf6t1HJEyTa/LNzQTMdZzHSmBVb51IjxztLAKVw==", "signatures": [{"sig": "MEUCIDGNgohR7ngkly+cbRLOydW/rItH0E9/LnxzDPAZmvxGAiEAx6xj6brGNAe3rvhSMrxBP8zIKGpEv3KvE/vGYXI4Va4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 399883}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "directories": {}, "dependencies": {"semver": "^7.3.4", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-replace-supers": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0", "@babel/helper-optimise-call-expression": "^8.0.0-beta.0", "@babel/helper-member-expression-to-functions": "^8.0.0-beta.0", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-beta.0", "@types/charcodes": "^0.2.0", "@babel/preset-env": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-class-features-plugin_8.0.0-beta.0_1748620303133_0.02146549261435049", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-create-class-features-plugin", "version": "8.0.0-beta.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Compile class public and private fields, private methods and decorators to ES6", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-class-features-plugin"}, "main": "./lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-member-expression-to-functions": "^8.0.0-beta.1", "@babel/helper-optimise-call-expression": "^8.0.0-beta.1", "@babel/helper-replace-supers": "^8.0.0-beta.1", "@babel/helper-skip-transparent-expression-wrappers": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "semver": "^7.3.4"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/preset-env": "^8.0.0-beta.1", "@types/charcodes": "^0.2.0", "charcodes": "^0.2.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-create-class-features-plugin@8.0.0-beta.1", "dist": {"shasum": "eef3fd48691330d6a96ef699337de801766fe485", "integrity": "sha512-gmCnpm4UOIM2d3sKx3C09g258dw9mXw8n/Xu2f1FBKQxjSlpL6BTvX7IopzgcROOaJuOwmYaahx9UIGzoP665A==", "tarball": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 399883, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDvdL6bkmBSjgBUxu9pR2NZ5iK+5Cq1zkjmS5kUhS0aCwIgVmsV9XPsJOJWgSANKnzhtmlCx2QAMT2aUz3Hss1Qhk8="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-create-class-features-plugin_8.0.0-beta.1_1751447085699_0.49146540003394223"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-12-03T19:00:37.096Z", "modified": "2025-07-02T09:04:46.253Z", "7.2.0": "2018-12-03T19:00:37.415Z", "7.2.1": "2018-12-04T15:21:48.550Z", "7.2.2": "2018-12-15T10:05:15.761Z", "7.2.3": "2018-12-20T11:13:23.103Z", "7.3.0": "2019-01-21T21:30:31.046Z", "7.3.2": "2019-02-04T22:22:58.516Z", "7.3.4": "2019-02-25T18:35:57.017Z", "7.4.0": "2019-03-19T20:45:22.987Z", "7.4.3": "2019-04-02T19:55:34.126Z", "7.4.4": "2019-04-26T21:04:57.413Z", "7.5.0": "2019-07-04T12:57:51.613Z", "7.5.5": "2019-07-17T21:21:53.132Z", "7.6.0": "2019-09-06T17:33:36.965Z", "7.7.0": "2019-11-05T10:54:02.756Z", "7.7.4": "2019-11-22T23:33:52.639Z", "7.8.0": "2020-01-12T00:17:33.061Z", "7.8.3": "2020-01-13T21:42:30.358Z", "7.8.6": "2020-02-27T12:21:45.894Z", "7.9.5": "2020-04-07T19:25:34.415Z", "7.9.6": "2020-04-29T18:38:27.236Z", "7.10.0": "2020-05-26T21:43:48.609Z", "7.10.1": "2020-05-27T22:08:26.848Z", "7.10.2": "2020-05-30T19:25:10.628Z", "7.10.3": "2020-06-19T20:54:42.127Z", "7.10.4": "2020-06-30T13:13:23.342Z", "7.10.5": "2020-07-14T18:18:16.905Z", "7.12.0": "2020-10-14T20:03:28.320Z", "7.12.1": "2020-10-15T22:41:54.777Z", "7.12.13": "2021-02-03T01:11:57.763Z", "7.12.16": "2021-02-11T22:47:09.294Z", "7.12.17": "2021-02-18T15:13:13.565Z", "7.13.0": "2021-02-22T22:50:18.883Z", "7.13.8": "2021-02-26T23:38:45.590Z", "7.13.10": "2021-03-08T22:36:07.937Z", "7.13.11": "2021-03-15T09:44:26.071Z", "7.14.0": "2021-04-29T20:09:53.300Z", "7.14.1": "2021-05-04T01:55:59.479Z", "7.14.2": "2021-05-12T17:09:36.565Z", "7.14.3": "2021-05-17T20:44:33.409Z", "7.14.4": "2021-05-28T17:00:06.423Z", "7.14.5": "2021-06-09T23:13:09.963Z", "7.14.6": "2021-06-14T21:56:58.065Z", "7.14.8": "2021-07-20T18:02:34.940Z", "7.15.0": "2021-08-04T21:13:12.538Z", "7.15.4": "2021-09-02T21:39:52.096Z", "7.16.0": "2021-10-29T23:47:55.512Z", "7.16.5": "2021-12-13T22:28:50.006Z", "7.16.7": "2021-12-31T00:23:02.304Z", "7.16.10": "2022-01-19T18:39:00.673Z", "7.17.0": "2022-02-02T23:04:40.527Z", "7.17.1": "2022-02-03T17:37:40.769Z", "7.17.6": "2022-02-21T22:58:23.025Z", "7.17.9": "2022-04-06T15:55:24.992Z", "7.17.12": "2022-05-16T19:32:12.837Z", "7.18.0": "2022-05-19T18:16:32.109Z", "7.18.6": "2022-06-27T19:50:37.810Z", "7.18.9": "2022-07-18T09:17:42.163Z", "7.18.13": "2022-08-22T16:05:10.752Z", "7.19.0": "2022-09-05T19:02:20.117Z", "7.20.2": "2022-11-04T18:51:02.812Z", "7.20.5": "2022-11-28T10:12:42.504Z", "7.20.7": "2022-12-22T09:45:37.481Z", "7.20.12": "2023-01-04T16:02:16.465Z", "7.21.0": "2023-02-20T15:31:14.019Z", "7.21.4": "2023-03-31T09:01:50.934Z", "7.21.4-esm": "2023-04-04T14:09:56.097Z", "7.21.4-esm.1": "2023-04-04T14:21:53.797Z", "7.21.4-esm.2": "2023-04-04T14:39:58.001Z", "7.21.4-esm.3": "2023-04-04T14:56:40.657Z", "7.21.4-esm.4": "2023-04-04T15:13:49.082Z", "7.21.5": "2023-04-28T19:50:30.046Z", "7.21.8": "2023-05-02T15:15:09.687Z", "7.22.0": "2023-05-26T13:45:45.180Z", "7.22.1": "2023-05-26T16:34:57.007Z", "7.22.5": "2023-06-08T18:21:44.366Z", "7.22.6": "2023-07-04T07:48:58.940Z", "7.22.9": "2023-07-12T16:53:47.755Z", "8.0.0-alpha.0": "2023-07-20T14:00:26.711Z", "8.0.0-alpha.1": "2023-07-24T17:53:01.173Z", "7.22.10": "2023-08-07T17:25:10.259Z", "8.0.0-alpha.2": "2023-08-09T15:15:21.931Z", "7.22.11": "2023-08-24T13:08:36.791Z", "7.22.15": "2023-09-04T12:25:20.158Z", "8.0.0-alpha.3": "2023-09-26T14:57:35.539Z", "8.0.0-alpha.4": "2023-10-12T02:06:48.516Z", "7.23.5": "2023-11-29T10:25:37.845Z", "7.23.6": "2023-12-11T13:09:56.879Z", "8.0.0-alpha.5": "2023-12-11T15:19:41.890Z", "7.23.7": "2023-12-29T20:01:28.354Z", "7.23.9": "2024-01-25T16:57:46.866Z", "8.0.0-alpha.6": "2024-01-26T16:14:40.823Z", "7.23.10": "2024-01-31T11:42:10.763Z", "7.24.0": "2024-02-28T11:47:32.262Z", "8.0.0-alpha.7": "2024-02-28T14:05:42.734Z", "7.24.1": "2024-03-19T09:49:17.879Z", "7.24.4": "2024-04-03T16:53:45.147Z", "8.0.0-alpha.8": "2024-04-04T13:20:18.702Z", "7.24.5": "2024-04-29T18:34:25.535Z", "7.24.6": "2024-05-24T12:25:09.415Z", "8.0.0-alpha.9": "2024-06-03T14:05:38.934Z", "8.0.0-alpha.10": "2024-06-04T11:20:39.569Z", "7.24.7": "2024-06-05T13:15:53.371Z", "8.0.0-alpha.11": "2024-06-07T09:16:04.011Z", "7.24.8": "2024-07-11T14:54:57.511Z", "7.25.0": "2024-07-26T16:59:32.385Z", "8.0.0-alpha.12": "2024-07-26T17:33:58.365Z", "7.25.4": "2024-08-22T09:34:35.743Z", "7.25.7": "2024-10-02T15:15:27.617Z", "7.25.9": "2024-10-22T15:21:43.858Z", "8.0.0-alpha.13": "2024-10-25T13:54:44.046Z", "8.0.0-alpha.14": "2024-12-06T16:54:32.738Z", "8.0.0-alpha.15": "2025-01-10T17:25:01.766Z", "7.26.9": "2025-02-14T11:48:11.793Z", "8.0.0-alpha.16": "2025-02-14T11:59:37.225Z", "8.0.0-alpha.17": "2025-03-11T18:25:30.604Z", "7.27.0": "2025-03-24T17:41:53.487Z", "7.27.1": "2025-04-30T15:09:25.646Z", "8.0.0-beta.0": "2025-05-30T15:51:43.324Z", "8.0.0-beta.1": "2025-07-02T09:04:46.014Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel", "babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-class-features-plugin"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}