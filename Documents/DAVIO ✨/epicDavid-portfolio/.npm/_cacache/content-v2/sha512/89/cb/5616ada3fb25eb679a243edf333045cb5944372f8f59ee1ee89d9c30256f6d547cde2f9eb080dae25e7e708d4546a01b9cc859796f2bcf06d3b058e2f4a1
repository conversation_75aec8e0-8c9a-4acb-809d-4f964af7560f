{"_id": "merge2", "_rev": "48-3e55baaaed4e69f8c0dae7bcbfd7fcb1", "name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "dist-tags": {"latest": "1.4.1"}, "versions": {"0.2.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.2.0", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "dependencies": {"through2": "^1.1.1"}, "devDependencies": {"gulp": ">=3.8.8", "gulp-jshint": ">=1.8.5", "gulp-sequence": ">=0.1.0"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "d27c1db2fb13d142cedd19a5fdc23f8f47f2242b", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.2.0", "_shasum": "c09144eb0a51b9b825fda28892a5cd99d413898d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "c09144eb0a51b9b825fda28892a5cd99d413898d", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.2.0.tgz", "integrity": "sha512-O7nzT2/cxUtB4Ut+NSfyj/0/3g+6VVIMzqkoCfGenV6XnWvImXr1wBHDYAouk6ekq9x9Q6rYXRNjkAitu0S4dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG1+cpPwCPIFRPyxkcsQX9ssjaE7pjZGNQGCP6yL6eOOAiBLLWE9awSEEdhXeT1ULmUGVNnqeAyKb2yh03bvyeMz4A=="}]}, "directories": {}}, "0.2.1": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.2.1", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "dependencies": {"through2": "^1.1.1"}, "devDependencies": {"gulp": ">=3.8.8", "gulp-jshint": ">=1.8.5", "gulp-sequence": ">=0.1.0"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "d8e027bde8eda12a9260e60972f4ead3e0821fb2", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.2.1", "_shasum": "33613ad7ab6897b42d70e29c1b20c1da5551ed5d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "33613ad7ab6897b42d70e29c1b20c1da5551ed5d", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.2.1.tgz", "integrity": "sha512-1LUPCysCJLRu9N5zIfYCHBWZMngCsEkiARs1Ft0ERyh+e0da2nbVXQbbd2PfyLulBdKhO4kgaW3WNOiAqWL8DQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRYQuvAzZM3hE49eJAFHsn9vvDCDBXylu9SQF5QFrFsgIgZ6RWnLMPjxM1y5mmgJhNt+R+DAOUGYoW80q/TEZSpNo="}]}, "directories": {}}, "0.3.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.0", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "dependencies": {"through2": "^1.1.1"}, "devDependencies": {"gulp": "^3.8.8", "gulp-jshint": "^1.8.4", "gulp-mocha": "^1.0.0", "gulp-sequence": "^0.1.0", "mocha": "^1.21.0", "should": "^4.0.4", "thunks": "^1.3.0"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "3fb4d81ba15219b17e821593cb82280b26598714", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.0", "_shasum": "3056110d3889cc20343a5ff3b70a0abe41bdd5eb", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "3056110d3889cc20343a5ff3b70a0abe41bdd5eb", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.0.tgz", "integrity": "sha512-yjwnrKsPhes2/mHKIT5jh3iMMFqVyq25YxiPuhmguKkOsv9nK6n6igX48thDy3XGqBCNKSjAyzNV8MJj/KSJgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVj5MIEcD4bpXCQrJIZqof6e8ycCG9m2d3TNPNFtCsaAIhAOTfWysBTnFbjUrzPL4yZGjlsNTVQhbHBAQnU0hyFJUD"}]}, "directories": {}}, "0.3.1": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.1", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "dependencies": {"through2": "^1.1.1"}, "devDependencies": {"gulp": ">=3.8.8", "gulp-jshint": ">=1.8.4", "gulp-mocha": ">=1.0.0", "gulp-sequence": ">=0.3.0", "mocha": ">=1.21.0", "should": ">=4.0.4", "thunks": ">=2.1.0"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "e35a5011758207bdfd2f018b0d32c679b894fdb6", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.1", "_shasum": "b961e8be10c69569abbac673cb76f7fe75d359f4", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "b961e8be10c69569abbac673cb76f7fe75d359f4", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.1.tgz", "integrity": "sha512-80aIoN3PmO4CCy+YG/Vy9cH8ppf7P5gUvEQHeI+uJj33MTyHFnMRjng+zKJcWooCOptNobEBcyRftMxbYxyWgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFjEtTbeBq1jOLhK6P4DhUFQxYlEjmHec9kY5+iN3gGPAiEAzdyGsoNFr9parwovA4U1TbpvkxdzsahAzSYg5Zu5F9E="}]}, "directories": {}}, "0.3.2": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.2", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10.36"}, "dependencies": {}, "devDependencies": {"gulp": "^3.8.11", "gulp-jshint": "^1.9.4", "gulp-mocha": "^2.0.0", "gulp-sequence": "^0.3.2", "mocha": "^2.2.1", "should": "^5.2.0", "through2": "^1.1.1", "thunks": "^2.7.2"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "3ddb80db5034b598ac07d215e5f6d34cf102aa64", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.2", "_shasum": "2066dcc5f9092744790b05546a97e976487154cd", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "2066dcc5f9092744790b05546a97e976487154cd", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.2.tgz", "integrity": "sha512-PMpqBbrx3lY2GjW0NAnfeVcoNHeSXrhNt3Nd95fBT8W0YXkJw23ulaiUGGPCTM5J5H0aMRF6ER/+g4JgGo9B/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJrLlB2uP/gLyHeWivUBtJafR4ZdJbn/0darV2kwzvNgIgWwmKQnwdFONmta/6RVnGnzKRijn1ADhe9UEAZuB87KQ="}]}, "directories": {}}, "0.3.3": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.3", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10.36"}, "dependencies": {}, "devDependencies": {"gulp": "^3.8.11", "gulp-jshint": "^1.10.0", "gulp-mocha": "^2.0.1", "gulp-sequence": "^0.3.2", "mocha": "^2.2.4", "should": "^5.2.0", "through2": "^1.1.1", "thunks": "^2.7.3"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "c440a8a553092e11d97eea0c2fc73f91eca85cab", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.3", "_shasum": "da860f87f11fe3da7da280f8660218194d298aaa", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "1.6.3", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "da860f87f11fe3da7da280f8660218194d298aaa", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.3.tgz", "integrity": "sha512-KKjPFL6XdLdTr7h+U5yzO8pEN7Sqi9RjguESmPSaX36Yjm3VTcb333I29whDiDUpNsNQeYjK5QQH3+yM2HP6pg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4HngT+petrRezAEVaCDJbh7VLpiMaIuJhKzC01kKUeAIhALiyYWNZqkV4VA7TLeBW5rsxZ58Q5QUMWuRV0myIT8O8"}]}, "directories": {}}, "0.3.4": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.4", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10.36"}, "dependencies": {}, "devDependencies": {"gulp": "^3.8.11", "gulp-jshint": "^1.10.0", "gulp-mocha": "^2.0.1", "gulp-sequence": "^0.3.2", "mocha": "^2.2.4", "should": "^5.2.0", "through2": "^1.1.1", "thunks": "^2.7.3"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "06bf7b6d03560c8466e5546481750b55a5a230d4", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.4", "_shasum": "e01f7d7a196b73576a6567d1986f097858eb9d81", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "1.6.3", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "e01f7d7a196b73576a6567d1986f097858eb9d81", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.4.tgz", "integrity": "sha512-Nplc2foc4wbJ49r04GewOmxdfVGfxE4XP1DsiKW5BSMqv1foyowy/htKFZDr8T4lsSDaZdpFLIJ8ij9Re45nXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIENs4UgCOSaVO3nHVjiyrknx6FDC5La9f355Zw3k/7HyAiAkxIfu3iwzQIJIFOLke52eGOgk6/6vyMTaeCnPtF4YRQ=="}]}, "directories": {}}, "0.3.5": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.5", "main": "index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10.36"}, "dependencies": {}, "devDependencies": {"gulp": "^3.8.11", "gulp-jshint": "^1.10.0", "gulp-mocha": "^2.0.1", "gulp-sequence": "^0.3.2", "mocha": "^2.2.4", "should": "^5.2.0", "through2": "^1.1.1", "thunks": "^2.7.3"}, "scripts": {"test": "gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "9c211f30d1244000a869bf15ed3d02874370f3ab", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.5", "_shasum": "c61871c22f8503d971d0fa90bac8c5211de29fef", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "c61871c22f8503d971d0fa90bac8c5211de29fef", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.5.tgz", "integrity": "sha512-dWSQRHoTkdO4sPo74c2RuwwngytmSS0cf7vHyKQ3zSaiFunQzkraCtJoXEt48Anz5qPpI9AI3PYBYX9ISXxjNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHLQ3ILfNAZpTmnOrFLyL+Wdeh7sf4DcQKoBQMX4l00PAiBKxNNHa0ddchvIfN6Rw98E9AADcpR/85BM6ztidcwGsA=="}]}, "directories": {}}, "0.3.6": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.6", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10.36"}, "dependencies": {}, "devDependencies": {"gulp": "^3.9.0", "gulp-mocha": "^2.1.2", "gulp-sequence": "^0.3.2", "mocha": "^2.2.5", "should": "^7.0.1", "standard": "^4.4.0", "through2": "^2.0.0", "thunks": "^3.4.2"}, "scripts": {"test": "standard && gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "48bd5d94158cba71bc6152b635e6e848dc90bcbc", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.6", "_shasum": "2b1fbf5ae6573962b77d68ea689859b529a6c224", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.1", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "2b1fbf5ae6573962b77d68ea689859b529a6c224", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.6.tgz", "integrity": "sha512-ggrct2Rsu7dxehtpZxkz7X5xVlQ1mRzSSVnUDZ1XAzdfqIqp2365Aix5d19z9m/hxxsgpjhYn6HGE9ymJmVlSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhjA0wQZThoy96Pr6+F5ph6oeBid5iwt9EbJk0IfflLAiEAx4Bn5lu7UZKgKC5PElIXGg2CP32pu5mjFLyIRxoS+Fw="}]}, "directories": {}}, "1.0.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.0.0", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10"}, "dependencies": {}, "devDependencies": {"mocha": "^2.3.4", "should": "^8.1.1", "standard": "^5.4.1", "through2": "^2.0.0", "thunks": "^4.1.2"}, "scripts": {"test": "standard && mocha"}, "files": ["README.md", "index.js"], "gitHead": "06d126ba0b2e6e746b3201ef17eaf7cb1a6988fa", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.0.0", "_shasum": "6a50d1226dc0c0148af8c88b2dcc568f902ef2c7", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "6a50d1226dc0c0148af8c88b2dcc568f902ef2c7", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.0.0.tgz", "integrity": "sha512-tOuEAF+oGO/mf4V6A0kUgwEeIfc/T0B+m4NClCsMmgHXV6b8V/MHovhGiXT3gSoWYal1eNHlSUv6Qat6g1CFig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGOpXvPLDSDM3UOexgcFYhi/vX3RuafX7Kh5TgC840VCAiBQnt7/s5/Ap9bob+hOiXJz+gWYDjlqs2VDZI2tgahs/A=="}]}, "directories": {}}, "0.3.7": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "0.3.7", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10.36"}, "dependencies": {}, "devDependencies": {"gulp": "^3.9.0", "gulp-mocha": "^2.1.2", "gulp-sequence": "^0.3.2", "mocha": "^2.2.5", "should": "^7.0.1", "standard": "^4.4.0", "through2": "^2.0.0", "thunks": "^3.4.2"}, "scripts": {"test": "standard && gulp test"}, "ignore": ["**/.*", "node_modules", "test", "gulpfile.js"], "gitHead": "64dfe1e359bf50a7b616dc26ffd542cd2cec0a98", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@0.3.7", "_shasum": "18e00a00671090de1165025fc86bd3e77c207f63", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "18e00a00671090de1165025fc86bd3e77c207f63", "tarball": "https://registry.npmjs.org/merge2/-/merge2-0.3.7.tgz", "integrity": "sha512-o7A99C1dVkO992STg/HEtt+goC2XFq0AgAc1K3CKf8vaVphdJPLNRZyji0P7oNw+jRtd10JsZwFxHy7UJ76X+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/r4ENaeZK6gZcda9sFP+uZfkUTWDkCydZGhlD9AFVagIgbVOaQCEhD5efdCvK5mVb0xe4P3vBuR9UZSFp+SwUM0s="}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/merge2-0.3.7.tgz_1454509742869_0.03190552443265915"}, "directories": {}}, "1.0.1": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.0.1", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10"}, "dependencies": {}, "devDependencies": {"mocha": "^2.3.4", "should": "^8.1.1", "standard": "^5.4.1", "through2": "^2.0.0", "thunks": "^4.1.2"}, "scripts": {"test": "standard && mocha"}, "files": ["README.md", "index.js"], "gitHead": "c7df41b9e28f7b827cc4861395c632d899b9b532", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.0.1", "_shasum": "d4eeeee1ac230a4abc2b2c089136fd6b45cf9af3", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "d4eeeee1ac230a4abc2b2c089136fd6b45cf9af3", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.0.1.tgz", "integrity": "sha512-eh5mc+L21WJ6GbzuSHIWSTa7oGRZ6XV9NrZn3E5q2P+XSi9zzqymhE8cpxpMfiFIgD1vtYgxTFj3N0rRh6DB7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFhJEb5xTqRIItWokf32gDOuG6LIPNLy38h8PEHD18NQIhAPsHmXeYCcBFNSp4l9jW9ZZXpLU6CGeQ4vzLO6IaVyX1"}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/merge2-1.0.1.tgz_1454509924931_0.6569136942271143"}, "directories": {}}, "1.0.2": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.0.2", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10"}, "dependencies": {}, "devDependencies": {"standard": "^6.0.8", "through2": "^2.0.1", "thunks": "^4.1.5", "tman": "^0.5.2"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js"], "gitHead": "b30bc519369ab7cc35b4ffe22bd31ef678171073", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.0.2", "_shasum": "dd394ea14e0b202c265b567970ba8136e1003bdb", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "dd394ea14e0b202c265b567970ba8136e1003bdb", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.0.2.tgz", "integrity": "sha512-M/uy8mqLgqT0HkgY8F1m5GonjvhRtpaMgonsKnEzDnzACJx/E/eLRKbil9rZ3TufhnqWWO5Z8at/bYLqS6A2uw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKVX8+YhTGRKqtIOk9AaLa/AWtauIU67tlJ33CFTBoBwIhAK3BQk3H5c2brHmlBhtDQo0/+xm8y1a7gCJUn/DmePj3"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/merge2-1.0.2.tgz_1459768514614_0.16177033097483218"}, "directories": {}}, "1.0.3": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.0.3", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">=0.10"}, "dependencies": {}, "devDependencies": {"standard": "^8.6.0", "through2": "^2.0.3", "thunks": "^4.7.5", "tman": "^1.6.4"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js"], "gitHead": "ca1c7a7e3b25f6e1778776249bee29c289813859", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.0.3", "_shasum": "fa44f8b2262615ab72f0808a401d478a70e394db", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "fa44f8b2262615ab72f0808a401d478a70e394db", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.0.3.tgz", "integrity": "sha512-KgI4P7MSM31MNBftGJ07WBsLYLx7z9mQsL6+bcHk80AdmUA3cPzX69MK6dSgEgSF9TXLOl040pgo0XP/VTMENA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTxpmI4q1lJydBkszpzGQ76eE/Za5N9MffR58K0zUp1gIgEGwh470FUMcn5g+DEtpQgE3JZxD9r/3x+wRNhh/cIws="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/merge2-1.0.3.tgz_1483154707958_0.8970819583628327"}, "directories": {}}, "1.1.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.1.0", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 4.5.0"}, "dependencies": {}, "devDependencies": {"standard": "^10.0.2", "through2": "^2.0.3", "thunks": "^4.8.0", "tman": "^1.6.9"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js"], "gitHead": "9d0b0a4032500cb5ea638a82dfcdb5b9311efbac", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.1.0", "_shasum": "99fb32b35e9fad840146004e13a56b7549a524db", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.0", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "99fb32b35e9fad840146004e13a56b7549a524db", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.1.0.tgz", "integrity": "sha512-oVV8fMViwrQmbeKHBWQQaOBs59LxbGf57N+DtvCtjSlGo+OJEz1QisKYARmU8qxEhTi27HsVwDFqHReyWeuo3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE8Jko6uxqxixX88inWZsrWkqsHGZ8ArR94qb/D4Ywb3AiByuaEFSW5xaYcdrrAiBbChEnaKTHzPinL1ciqnN9dcFg=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2-1.1.0.tgz_1499003858617_0.15787300490774214"}, "directories": {}}, "1.2.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.2.0", "main": "./index", "jsnext:main": "index.mjs", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 4.5.0"}, "dependencies": {}, "devDependencies": {"@std/esm": "^0.8.3", "standard": "^10.0.3", "through2": "^2.0.3", "thunks": "^4.8.1", "tman": "^1.7.2"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js", "index.mjs"], "gitHead": "66b3dba1a584e5dace819cc9f188789e68fa0f77", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.2.0", "_shasum": "0f882151d988b1f3d0758945404fa73ee5923d3f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"shasum": "0f882151d988b1f3d0758945404fa73ee5923d3f", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.2.0.tgz", "integrity": "sha512-Q/lzJJa+lJ1G292m4E/cinz0iz9WOTXccr2c8/9gLv1ZQINM5s3waUK56jpPfWfdtfnai0Q5NdssmapN3tWy5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDQMntCejt4D1WmK2D7c6ygpoWBiCv1cMo1JCX9W8m3eAiEA+87Fipd55TLyydyRKfNGgP1ZPR66RxeHW3mZ2aDFHzE="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2-1.2.0.tgz_1504969399918_0.8848722805269063"}, "directories": {}}, "1.2.1": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.2.1", "main": "./index", "jsnext:main": "index.mjs", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 4.5.0"}, "dependencies": {}, "devDependencies": {"@std/esm": "^0.19.1", "standard": "^10.0.3", "through2": "^2.0.3", "thunks": "^4.9.0", "tman": "^1.7.4", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js", "index.mjs"], "gitHead": "e13bd5cc3db17460a011e372321f94d11acb6130", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.2.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-wUqcG5pxrAcaFI1lkqkMnk3Q7nUxV/NWfpAFSeWUwG9TRODnBDCUHa75mi3o3vLWQ5N4CQERWCauSlP0I3ZqUg==", "shasum": "271d2516ff52d4af7f7b710b8bf3e16e183fef66", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGguhdKSEbMgE8+ltVAG4aR68jWHjihN7ouU9njgGbH5AiAg87RIq3YhOHGrDKvoN7SS2TfJ4SjC0d/xMTSMIb4nsw=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2-1.2.1.tgz_1515514622244_0.8095978898927569"}, "directories": {}}, "1.2.2": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.2.2", "main": "./index", "jsnext:main": "index.mjs", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 4.5.0"}, "dependencies": {}, "devDependencies": {"@std/esm": "^0.26.0", "standard": "^11.0.1", "through2": "^2.0.3", "thunks": "^4.9.2", "tman": "^1.7.4", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js", "index.mjs"], "gitHead": "bfc1dba71bf4c9eb6d7e85868425f6cc07baa300", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.2.2", "_npmVersion": "6.0.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-bgM8twH86rWni21thii6WCMQMRMmwqqdW3sGWi9IipnVAszdLXRjwDwAnyrVXo6DuP3AjRMMttZKUB48QWIFGg==", "shasum": "03212e3da8d86c4d8523cebd6318193414f94e34", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.2.2.tgz", "fileCount": 5, "unpackedSize": 11160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7Gg8CRA9TVsSAnZWagAAs0wP/3QKKsiCxhuQmWtdQwha\ncb/1d43HQR2+KrEUnM/XUm7Fq7NdJ4fz615pEMdAv21sZlUYeV4sVrw8BB/R\nwtOnjc5+YAKBciQtO9q8V08BxJ/5wtsHtrb+mvz5VBhVDpDB9dCOcilrKbvk\nF8W1aNu6kuq63Ep8SJQUtY/JFnZQFZJBVVnp2arAfAMC39kS/pYLpHOVrd8x\niNzDIi0caN3KbUDC5ROwibAQH4+QpsA5TI0Fs8516osroPkBQ4ko6WO4cjyN\nXgbZBte7PAXeqn79YF1FqKs6k9H9/VrxZIH0iNKLzf+5GVYwTu9lmvblJ370\nZ+ikvHIKYBUCHVuxwGmR4RTVvzWsw+auPp6UFIMASIR0LPXRHIBPo30pX4k2\nNyGvlouZfQ5cttPkzFWjnvs8yDYe+umgKCHmRFl4sm4M0IgCA9QteK285sk7\nxxUMPO9WdjVTjNMejpSvEARyHEUKcppWNYFGPCCL3sXUo2rjPmLRvgHOjGWk\nyv3+PUlCsTZVycrEHaE/CeWBSesXbUnEmsjX8tOM9oDarSO3MxIL2br93d8e\nmHDkwxI6KEUihT75mgWWigpYyatlORvMaPo7SvnkdkWNUaP+/o1+gEceoGNk\n6iEOsExfB71uJW6xD+WDsQRokWUrQu8VZlt7ZJP6wKY/VioNYbur3WLFVSPF\npdxc\r\n=ry6W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDn/K2CcPMnn8cpPgFgIMnihWiXUgQ0D2NDfRT4U1K2bAiEAp4u9488VVSAxAKJwaWMUQk85kflI3nQvrUOT9lnzCCc="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2_1.2.2_1525442618781_0.07448611204789701"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.2.3", "main": "./index", "jsnext:main": "index.mjs", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 4.5.0"}, "dependencies": {}, "devDependencies": {"@std/esm": "^0.26.0", "standard": "^12.0.1", "through2": "^2.0.3", "thunks": "^4.9.2", "tman": "^1.8.1", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "gitHead": "d2f21832a16c8eb24c853b3e7cb133040688c898", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.2.3", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-gdUU1Fwj5ep4kplwcmftruWofEFt6lfpkkr3h860CXbAB9c3hGb55EOL2ali0Td5oebvW0E1+3Sr+Ur7XfKpRA==", "shasum": "7ee99dbd69bb6481689253f018488a1b902b0ed5", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.2.3.tgz", "fileCount": 5, "unpackedSize": 11179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwfQKCRA9TVsSAnZWagAAPZYP/RoffmCFre0R/7XzcXje\nwcrz6Q7QWQeC6Xmjmf83tOJ+fP5M9v7WJYcWCO/oEIacqq0d5eLLw2GUhwYG\njwhcALvz90fft9+nVIiLlA0B4/AD8VRZP2wext53XiWEzniDpWPHOI2XI6Lq\n4SgNwXzzhV8GFao4ps/u2tjeXcghuVjFcxFEeu8vS52Xsq7ZbnGw5UjQiYaB\nQtt1mBneBnQ9NKsT7xszgeSlznW4X91gJ4N/djIkfBwh89wSf4cQWI8/VtyQ\nES3gP1jRDe54fM10A3WLLsSgTAMVILZ/pB+PUUSfZxB1bEdhqvUd51/HfCS2\nv5AWQfMvYTef53pT9PWwTN+wCHmje0XM+fdXdSretfbVeiizI2ziuF6t1ApZ\nuA12ljQx8XrzIWLEENhnhgw5eS2UdWMkYARjzGgjIaATIIMm9ZSsxsK2kH6K\nbmEDZ6t/3HMQSBW9JtAhJZ9KZOUJlSnHS2UV1fBoPh7MGBmZq9qyOJ69+bwn\n2WAcV2MI6k1GwhDIaojnou4wOuQ3CGh5CD1D+EHEDzcTQCZUEOKsDsgXIchB\nJPv0M06ailmlK6pNsMXkD1f9XC09OZJCEf49S/jMgOl5gf9XH1C64dv5nYtd\nLFxZ0YFdqB9FpV44cFoX0mkNYDe41LAP9swzSCu4LySvi+w9eEE+FW1jgPXn\nrFV6\r\n=exgQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDUyHlbCL82Q6wZYEPGQKxRZ/d7hRJtqJd3F1PhcQbqAiEAih237DwdseN5Ep4UIQ07YQEBt/qWKmZuhhH7L/sve+4="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2_1.2.3_1539437577227_0.12785779573811085"}, "_hasShrinkwrap": false}, "1.2.4": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.2.4", "main": "./index", "jsnext:main": "index.mjs", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 4.5.0"}, "dependencies": {}, "devDependencies": {"@std/esm": "^0.26.0", "standard": "^13.1.0", "through2": "^3.0.1", "thunks": "^4.9.4", "tman": "^1.9.0", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "gitHead": "2bd8b4645fe38a7df0df645976e148ab6182a1b3", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.2.4", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-FYE8xI+6pjFOhokZu0We3S5NKCirLbCzSh2Usf3qEyr4X8U+0jNg9P8RZ4qz+V2UoECLVwSyzU3LxXBaLGtD3A==", "shasum": "c9269589e6885a60cf80605d9522d4b67ca646e3", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.2.4.tgz", "fileCount": 5, "unpackedSize": 11179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQW04CRA9TVsSAnZWagAAJ8kP/j91mM8A7+rA/uAd/0O4\nT0iRAEhMeo/ecob6Fdf2eaJQ2twYQpqGh+IxtWrowc6pjXg6FmjyS9VNY0Oq\nKvXQnBy9xsUhDrFJshFAAlQZ+q5SZ0rU3HR7OtKorTSkLRibtbZqw8YsAXkw\nNkGmtCmSo3Z+7whJ/JQ8HEE5hMMNunV+5IF7nWsBal0NQEUMAcMO902PGzNF\nH2N2fbGiauIwpTWrzwWldYihSC5DSb5tHyAd9gKCSEJ6g0Gjajb5fdItd9H0\niWFQzS+bk19zuoD5WOHwXdn4knavE6FATL5MoHk5Z3linL6pMIdRxZfaLFen\n4E90mDKe/jalsmhPD+hATKHb4MvDjfN/YBECr5aJraS7FmScYO+61Pvh2ES5\n0oEkw4Q0h86r222vvsPRK9iXTSf2owHK6e5YzPFgzEtz3ZFfQWv0UquVYns7\n19FYOz6XnqUS06Gxf5L/izCP5oJrS+xVWaNecTEpm7aAFjees0yZrxuuKyaN\nPmOxpdPV5YScP5gUEuZrvaf0NaburOp0iVL9p+vkVYTXafBanshxGhCNW9cs\n+79yP33cV9+fctswT8QUNtwwsqDAFQzx/huXcJkZURv8WTQUw7UJkg0pkh7Q\n9QPXtvwv00wcSSDcwrn6CrBg3w0Vpo2KNy7ucGEDvD8X4yeofR9oPT5uiEny\nhAbM\r\n=kLp+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiJ7Isq5aZJqcLUd421Wiuxib6OQIu7YJGt3p/B8HElAIhANlJuViDVcWfamGFq4HmIKM6OxNRQpwfzfAl4ChuepTm"}]}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2_1.2.4_1564568887881_0.9098272893817139"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.3.0", "main": "./index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 6"}, "dependencies": {}, "devDependencies": {"standard": "^14.2.0", "through2": "^3.0.1", "thunks": "^4.9.5", "tman": "^1.9.0", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "gitHead": "32e910f695e549b4a12e396dbe97d6ef45103219", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.3.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-2j4DAdlBOkiSZIsaXk4mTE3sRS02yBHAtfy127xRV3bQUFqXkjHCHLW6Scv7DwNRbIWNHH8zpnz9zMaKXIdvYw==", "shasum": "5b366ee83b2f1582c48f87e47cf1a9352103ca81", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.3.0.tgz", "fileCount": 4, "unpackedSize": 8368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJde5rqCRA9TVsSAnZWagAAlssQAIKl6vM27pNUNoGk+kjY\nT5hMofOjhMxgmwjW7wNsVVEnP16uzAfsnOVCxxwXitW1lanyO/RwSGe3xL3R\nM6b1XS9zu+rpa5/TpC28kB+ND6Bwn7zhwM3Y9OXMrpHsf/AtLuB+7Wmq5SgO\n5MawGkOUTRWXfDtRGijWMaCztajiVhiq05Rrg1kWvCOkAnwoARGaHMYb+YCl\nGqnXq7F4y31YjpB0vPqL/d8IbPQZ+39QPDZwgROPOhYUmIhsDmuTB97cVmXR\n0V5DkKJWPBZZCCiyVPz2ThMHj2yvLUwvarI8p4g+OSk1WsqM9XpgfptFfpSm\nRtWentPd9aLpya4C1j9Zt7DclXt9Ft/df8GLFDe11MD1Frbn+NWju2OWt/dN\nh3gSFvawbYoykufwnMRgi4pIr9NLwiUvYUWqROVPV/0ICzKhtZSBk3d9W6nc\nC9EbH2XRvihCnjBdh/GoqWzkDvak7yWOC4WZ2lzMklEj1fezDdtU4eMFaprM\ndp38MnH98h/iVX4anLgUTJ5yFaw8e9gaq5+qeXLVPibP/wAIZZFq/E8e8n+Q\n4y9lw4byBhvXGuCgX7rKVYP1vdRE4BxL4pU877oCX1CS/MTVTGFhICG4X9bz\nUzMopzE+6W9JeLlphNolKvXGEhSu0dEJYT7QzqC+hAD6/hY6Mnkeg0zQcWp6\nCjBI\r\n=RH9u\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDha//NZzVg9msvM4X5KJ7R7PRgwwomrIY8bhho/NaZlQIhAOGZH/wKnUWMv5wlviBMYIJ8/f39iBLJIY6PYPhuNyUc"}]}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2_1.3.0_1568381674105_0.9732640019445622"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.4.0", "main": "./index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 8"}, "dependencies": {}, "devDependencies": {"standard": "^14.3.4", "through2": "^3.0.1", "thunks": "^4.9.6", "tman": "^1.10.0", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "gitHead": "5dc3ea60a18feed7fd26f29ec2f06ddb89bf88cd", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.4.0", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-jxzaaEHzHbWqSj2fn02NhgvJSYT6RQYNGBDh4xL8ynXzzEhrtolRQ5AKCNXX99mX/JbwD9uI6gK5Y1dzZcUUtQ==", "shasum": "bdd9881be97140bb706cd4ff0fa4dc95855dbd60", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.4.0.tgz", "fileCount": 4, "unpackedSize": 8629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1z1yCRA9TVsSAnZWagAAZEUQAIQovhF/TqOmh3NfsFV7\nwHeKi1MafotdKNaJzjEzRhexGpzsMeBL4WoHxEiYpKdQ4QoiSh/P4gklh+pO\nxtm74KsXJ0oupmodQtj/m/685zXumod4t8rtnNJXNCqjflljcPTrOLdcfe1A\nfASe1RbeS+iRlJnoMnG7f8PzmAu9LWQbmZvKoC+GlIJOZYOt5/pD8HmMkFY0\nvvG3Fdx4pmOD8hELN4I9jyhJiRzsjf1lM2ipnurOgcT5B9UJcADivcwxsXod\n+8xr5uXNGLBjr2arZT3mIEKC27b9Np2ySNPbFnmcD5Bz/gSyUErwSQ67chnK\nOyl0oNcufmB93mqYhDLGvK/cUSCFKs5LzIZJlzHPal/62g2XEQqnDbibNc2r\nFYewUIziJZj4omkBAq9T38ns0KXs1idEva1YyAQ9nPJU+KbsoHSHuuvQzbPb\nnh9ufmMIOxMwk1CcZk/K7ZJU4BoBx0rHi65iJPf+sRfxNWD3tVnqVTFdvSh+\n35j/kC6m0Z8NfCrheqftDCa8p6/OSXqw0Ri3fOqqMXBFrIcv76gP+VAQ5+67\noW5alzE8T0Lb1mC5vdE09EY65zULZF1FUT+5Lodg4zJp5gr52MDGvy+wWwyP\nEX0FY8MWGuZDq/Xm0tLlEUdd8/mr1EQxAdUueV6uW0HZv2nHIz2nU6W1qe+K\nLoUW\r\n=tfH6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGavH+yWPt0cPhzGjfq45Ud1w/xz9VvFZDVaO3e2CzZBAiEA4YKJCypJuvsOfYeHZjnmeklVcX7rn5olnE7/CJv3DXA="}]}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2_1.4.0_1591164273436_0.3962905345888792"}, "_hasShrinkwrap": false}, "1.4.1": {"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.4.1", "main": "./index.js", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 8"}, "dependencies": {}, "devDependencies": {"standard": "^14.3.4", "through2": "^3.0.1", "thunks": "^4.9.6", "tman": "^1.10.0", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "gitHead": "a7300d42983d668f61d751ad2bfc42c880a29a36", "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "_id": "merge2@1.4.1", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "shasum": "4368892f885e907455a6fd7dc55c0c9d404990ae", "tarball": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "fileCount": 4, "unpackedSize": 8897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe11OeCRA9TVsSAnZWagAAtkYP/0QycgAilblsVUgP0X5M\nwSsiRY+ypBmatVTMXB9R+eZYFeAOG9kiq+D5VeBYxLDGw+Gchzn83b4LPq3f\n5196XgknRgO+1OHET61D5HW84MIkmIy0u59jnRdsrVbuC0nwlEZhvn5xxSdI\nln+SKQcc+w2y4ebT1KtIY2HzOkeKfUVJv2DjCJwbDz71MtqY5UFh2UhgOhgr\ns3wNnIwIg2MuysXkVIP+TNyDzgoaaHFek/VA+dU8bBMbCNIZQaKXrfIjbc0X\n5TynOOk4zWLTQxyemO0qv6GXu5KXK83QG3D+BRN1D5mAQNOn0f4zYqyynEuW\nmXHxKltwKMGVIzutCKgbEKd80Rdgu/8SGibjXX8A8q8/R1wn+ihJYmjKhqDI\n+GbgyQYMzzMkt8bExDftKdZudDRmDWo8rYz33UaPg7tpHjoFgplWWjglSGVX\ntJeEFf9tg9UH+U2x9iSiYkJEdaG5m2EI0uamzUIpXf+lHI9Enbpgn8HYVxUo\n2vjpd+R+sFvu2HOO3brz1+G7MXDnHxucoEWF9MPgsziNkICwWAtqxAUIcTEd\n0y4QcRuIpoPDmfk1Efs6h4Y8CkFId6RVGLDeN+7wTX0V532naRgMgqo8gAo5\n2Z5mCejZlfOzH9zJ9EqamKOT3A+17QyPjzMz6b2Gd9zqY316GCb3/AfibVCv\nR17I\r\n=hZu1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJXQHvo3KiOCNB4nFEac294COcLkgBbPuyOCI2Xnrg5AIhAI3SqKXEynHwHV7NU5juLJ8yp+2/N1eRrCRUSM/tOACM"}]}, "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "_npmUser": {"name": "zensh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge2_1.4.1_1591169950348_0.45149369595461764"}, "_hasShrinkwrap": false}}, "readme": "# merge2\n\nMerge multiple streams into one stream in sequence or parallel.\n\n[![NPM version][npm-image]][npm-url]\n[![Build Status][travis-image]][travis-url]\n[![Downloads][downloads-image]][downloads-url]\n\n## Install\n\nInstall with [npm](https://npmjs.org/package/merge2)\n\n```sh\nnpm install merge2\n```\n\n## Usage\n\n```js\nconst gulp = require('gulp')\nconst merge2 = require('merge2')\nconst concat = require('gulp-concat')\nconst minifyHtml = require('gulp-minify-html')\nconst ngtemplate = require('gulp-ngtemplate')\n\ngulp.task('app-js', function () {\n  return merge2(\n      gulp.src('static/src/tpl/*.html')\n        .pipe(minifyHtml({empty: true}))\n        .pipe(ngtemplate({\n          module: 'genTemplates',\n          standalone: true\n        })\n      ), gulp.src([\n        'static/src/js/app.js',\n        'static/src/js/locale_zh-cn.js',\n        'static/src/js/router.js',\n        'static/src/js/tools.js',\n        'static/src/js/services.js',\n        'static/src/js/filters.js',\n        'static/src/js/directives.js',\n        'static/src/js/controllers.js'\n      ])\n    )\n    .pipe(concat('app.js'))\n    .pipe(gulp.dest('static/dist/js/'))\n})\n```\n\n```js\nconst stream = merge2([stream1, stream2], stream3, {end: false})\n//...\nstream.add(stream4, stream5)\n//..\nstream.end()\n```\n\n```js\n// equal to merge2([stream1, stream2], stream3)\nconst stream = merge2()\nstream.add([stream1, stream2])\nstream.add(stream3)\n```\n\n```js\n// merge order:\n//   1. merge `stream1`;\n//   2. merge `stream2` and `stream3` in parallel after `stream1` merged;\n//   3. merge 'stream4' after `stream2` and `stream3` merged;\nconst stream = merge2(stream1, [stream2, stream3], stream4)\n\n// merge order:\n//   1. merge `stream5` and `stream6` in parallel after `stream4` merged;\n//   2. merge 'stream7' after `stream5` and `stream6` merged;\nstream.add([stream5, stream6], stream7)\n```\n\n```js\n// nest merge\n// equal to merge2(stream1, stream2, stream6, stream3, [stream4, stream5]);\nconst streamA = merge2(stream1, stream2)\nconst streamB = merge2(stream3, [stream4, stream5])\nconst stream = merge2(streamA, streamB)\nstreamA.add(stream6)\n```\n\n## API\n\n```js\nconst merge2 = require('merge2')\n```\n\n### merge2()\n\n### merge2(options)\n\n### merge2(stream1, stream2, ..., streamN)\n\n### merge2(stream1, stream2, ..., streamN, options)\n\n### merge2(stream1, [stream2, stream3, ...], streamN, options)\n\nreturn a duplex stream (mergedStream). streams in array will be merged in parallel.\n\n### mergedStream.add(stream)\n\n### mergedStream.add(stream1, [stream2, stream3, ...], ...)\n\nreturn the mergedStream.\n\n### mergedStream.on('queueDrain', function() {})\n\nIt will emit 'queueDrain' when all streams merged. If you set `end === false` in options, this event give you a notice that should add more streams to merge or end the mergedStream.\n\n#### stream\n\n*option*\nType: `Readable` or `Duplex` or `Transform` stream.\n\n#### options\n\n*option*\nType: `Object`.\n\n* **end** - `Boolean` - if `end === false` then mergedStream will not be auto ended, you should end by yourself. **Default:** `undefined`\n\n* **pipeError** - `Boolean` - if `pipeError === true` then mergedStream will emit `error` event from source streams. **Default:** `undefined`\n\n* **objectMode** - `Boolean` . **Default:** `true`\n\n`objectMode` and other options(`highWaterMark`, `defaultEncoding` ...) is same as Node.js `Stream`.\n\n## License\n\nMIT © [Teambition](https://www.teambition.com)\n\n[npm-url]: https://npmjs.org/package/merge2\n[npm-image]: http://img.shields.io/npm/v/merge2.svg\n\n[travis-url]: https://travis-ci.org/teambition/merge2\n[travis-image]: http://img.shields.io/travis/teambition/merge2.svg\n\n[downloads-url]: https://npmjs.org/package/merge2\n[downloads-image]: http://img.shields.io/npm/dm/merge2.svg?style=flat-square\n", "maintainers": [{"name": "zensh", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T17:53:53.486Z", "created": "2014-10-19T07:52:56.896Z", "0.2.0": "2014-10-19T07:52:56.896Z", "0.2.1": "2014-10-19T09:05:50.651Z", "0.3.0": "2014-10-19T12:08:01.032Z", "0.3.1": "2014-11-22T15:30:48.571Z", "0.3.2": "2015-03-28T12:01:27.531Z", "0.3.3": "2015-04-10T13:03:11.768Z", "0.3.4": "2015-04-12T14:17:24.813Z", "0.3.5": "2015-04-21T07:10:35.955Z", "0.3.6": "2015-06-28T03:04:27.799Z", "1.0.0": "2016-01-19T13:42:52.218Z", "0.3.7": "2016-02-03T14:29:05.103Z", "1.0.1": "2016-02-03T14:32:05.826Z", "1.0.2": "2016-04-04T11:15:15.145Z", "1.0.3": "2016-12-31T03:25:08.193Z", "1.1.0": "2017-07-02T13:57:38.729Z", "1.2.0": "2017-09-09T15:03:19.987Z", "1.2.1": "2018-01-09T16:17:02.333Z", "1.2.2": "2018-05-04T14:03:38.912Z", "1.2.3": "2018-10-13T13:32:57.347Z", "1.2.4": "2019-07-31T10:28:08.170Z", "1.3.0": "2019-09-13T13:34:34.277Z", "1.4.0": "2020-06-03T06:04:33.623Z", "1.4.1": "2020-06-03T07:39:10.563Z"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "readmeFilename": "README.md", "users": {"285858315": true, "mahnunchik": true, "mohankethees": true, "hsrob": true, "temoto-kun": true, "fadihania": true, "panos277": true, "enil": true, "qodesmith": true, "larrychen": true}, "license": "MIT"}