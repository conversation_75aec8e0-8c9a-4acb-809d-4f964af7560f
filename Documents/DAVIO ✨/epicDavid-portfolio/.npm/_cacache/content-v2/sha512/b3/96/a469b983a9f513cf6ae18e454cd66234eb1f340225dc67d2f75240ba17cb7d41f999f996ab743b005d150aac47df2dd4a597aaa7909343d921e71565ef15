{"_id": "pure-rand", "_rev": "49-22448dfecf70c086daaf7986740fe19c", "name": "pure-rand", "dist-tags": {"next": "3.0.0-alpha.*******", "experimental": "5.0.0-experimental.*******", "latest": "7.0.1"}, "versions": {"0.0.0": {"name": "pure-rand", "version": "0.0.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@0.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "ed84e6eefe76554478e6190829e58070082f86e4", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-0.0.0.tgz", "fileCount": 26, "integrity": "sha512-tjDf6DI2aCoUNYxYJPq/ysS+YMyLzA7hjqxIAsdNAxJG3eRZxBhchYTHD/+597kzNO9Gs6IbT4+QgQz6yku1MA==", "signatures": [{"sig": "MEQCIDCoKLiFlwJ7rRXZEtytdYSXnz7lqtFU5ZZg7eXHxTKKAiBRVNbsT4xj7BsjdI+SNdVVas/bG2ko99Zdemh6QPfjAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29800}, "main": "lib/pure-rand.js", "types": "lib/pure-rand.d.ts", "gitHead": "6cde14b3bbdae880be01f75954fe904a8bbd2726", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "5.3.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.4.1", "chai": "^4.1.2", "mocha": "^5.0.1", "ts-node": "^5.0.0", "coveralls": "^3.0.0", "fast-check": "0.0.13", "typescript": "^2.7.2", "@types/chai": "^4.1.2", "@types/node": "^9.4.6", "@types/mocha": "^2.2.48", "source-map-support": "^0.5.3", "@types/power-assert": "^1.4.29", "codeclimate-test-reporter": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_0.0.0_1519938837964_0.9686890497954077", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "pure-rand", "version": "1.0.1", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.0.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "0d52d4b8b863d8ee240ebd22d1adf82c0fcd8ee8", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.0.1.tgz", "fileCount": 14, "integrity": "sha512-CE2BhkLPqsA1UldfC8z/h5kKSmeNIkWAgHFSJcsrWZr1kkWUAx6YIxrBEK2Orgxpp6W8eAi7ykvbxwfoKV4NXg==", "signatures": [{"sig": "MEQCIHeFoG38AbzQjQ7h95cSbxtjpZpJsnzAimr4ATjHL/HrAiBFJS3k2DzW6y3HZB93tvqYLqAKecStJDc7p+F6iGafCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16686}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "_shasum": "0d52d4b8b863d8ee240ebd22d1adf82c0fcd8ee8", "gitHead": "a3b2f3c7adb41a42262f03f8b94ef61017ee4f2c", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "webbuild": "browserify lib/pure-rand.js --s prand -o lib/bundle.js", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "3.10.10", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "6.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.4.1", "chai": "^4.1.2", "mocha": "^5.0.1", "ts-node": "^5.0.0", "coveralls": "^3.0.0", "browserify": "^16.1.1", "fast-check": "0.0.13", "typescript": "^2.7.2", "@types/chai": "^4.1.2", "@types/node": "^9.4.6", "@types/mocha": "^2.2.48", "source-map-support": "^0.5.3", "@types/power-assert": "^1.4.29", "codeclimate-test-reporter": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.0.1_1520363355443_0.19987063618494716", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "pure-rand", "version": "1.0.2", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.0.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "8e0302a6f4a397d3bc096cef0af4286e7bf5b358", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.0.2.tgz", "fileCount": 22, "integrity": "sha512-wROtWPz9Kzy6XN7aLJ5laTjaR2dG1XeNu/gXTH+xEIKqVlZcCabTodlSeHkq/EkChyMuJE3VB9JFSodo47Pe/w==", "signatures": [{"sig": "MEQCICQzGJ9I6DKLRjAOWhECtLKqlr1FEohRPLEn5GtYVBs7AiB1YN2tfSYBTby17ZDvfCqHHlqCDh9yjyymto/lEDAqXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28326}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "_shasum": "8e0302a6f4a397d3bc096cef0af4286e7bf5b358", "gitHead": "fde84d9070787a67f7ef25b75f8dd66682186af1", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "webbuild": "browserify lib/pure-rand.js --s prand -o lib/bundle.js", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "4.2.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.4.1", "chai": "^4.1.2", "mocha": "^5.0.1", "ts-node": "^5.0.0", "coveralls": "^3.0.0", "browserify": "^16.1.1", "fast-check": "0.0.13", "typescript": "^2.7.2", "@types/chai": "^4.1.2", "@types/node": "^9.4.6", "@types/mocha": "^2.2.48", "source-map-support": "^0.5.3", "@types/power-assert": "^1.4.29", "codeclimate-test-reporter": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.0.2_1520364885886_0.011078198680264606", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "pure-rand", "version": "1.1.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.1.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "56fc7597fa22aa0752e609bfd166edef94bb4999", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.1.0.tgz", "fileCount": 24, "integrity": "sha512-NZcrbFPSnxqLAzb6Ay7SrbvvUfA+hn6vGpgi5e4Av+Ag6Dz2JZVRFD0zfaIYGJB1he+H+gW92BFhdlEdZ4g52g==", "signatures": [{"sig": "MEYCIQD45oxLJnlmuuq/vlU9FH/e9wEkyf59mmNz2wVY9ulywgIhAODpmAUIrFTyns5iktx9YF/lR1h3ybV870pGMlTI4kxu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAy/oCRA9TVsSAnZWagAAXRoP/1+PfV2/6CijBCW9dTl7\niaMPLMck+veqp3zt2SJJmnHed6xOVVSrDGCYJ7zM3fLR+ZC23rfPgQsZfq+T\nai1ujL06Wkx/f/4JxCuoix+TvPrTFlYGbmLEm4/6679rpJsP96a7UQINGAMr\nhe2/bYT7O1ogJb2bBjsBVcV3hVf3xTSm3bauSw0GNjXKQUwBRm6+u9WLTS2H\n7+nXPCCJljsP+jDPB7Hf9TfZSdUo6YR84aH3d7z7sTgEu3Dl0KMyqXMOwK0m\nFH+EpCScZEvv4gIZi0DZndTztHuIr9DFklbPpAedL7ECtclHrcqg+9MpTpw3\nKA78Xz5CUUb5zZlcZjHlnYP8he7j1iGpM2YDlUmGN3qXyAmB+7RgSpzLXPyc\nt67LNei1oo73QuDuxauDYOkQaHZnPIBfAk4rNcFWFQGYAbRyJoTDw/YgLqGm\nW8VjT2PykizZbaWWBlCUse+F10GdLfKTOVZCSAZEt/y+RHWRhXr7THRsbigx\nQrNE3dGjFuciVzByKyuQCz7G+1b2/uPP3U72VfHwGR4PfiYXzhyTvf80ueeY\nRe8zeQIgy57tockExBXmCprHdVIhLnF/wdXVOPbN8v1jk8I4bGlZWAHsAj17\nludkLGb9YknJGDGhAskKWAJDy5xdVSLvQ17ReONryEHf+mCMx3RkMBqRu3W5\nWxuc\r\n=UtUq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "_shasum": "56fc7597fa22aa0752e609bfd166edef94bb4999", "gitHead": "e00da8a12d5af1b3efa248f2f7965ebc9cc8c9fc", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "perfs": "ts-node perfs/main", "coverage": "nyc report --reporter=text-lcov | coveralls", "webbuild": "browserify lib/pure-rand.js --s prand -o lib/bundle.js", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "3.10.10", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "6.14.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.2.0", "ts-node": "^6.0.3", "coveralls": "^3.0.0", "browserify": "^16.2.2", "fast-check": "^1.1.0", "typescript": "^2.8.3", "@types/node": "^10.1.2", "@types/mocha": "^5.2.0", "source-map-support": "^0.5.6", "codeclimate-test-reporter": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.1.0_1526935526677_0.49963317410468", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "pure-rand", "version": "1.1.1", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.1.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "10e9d55b8c6a181408b5384fbfa10e96a3930b92", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.1.1.tgz", "fileCount": 24, "integrity": "sha512-e0U8pEtr1+cbS9ogngur4K+1Hy3Y2Cmi7IkCwbuTz+Xx3gaeabunprXQm6gYcO4lpX90uW74A0DL4NNWwF7fTQ==", "signatures": [{"sig": "MEQCIE5KkNd2TpvZAjj2i+1Ycqbbnl5NqUmLq+LvMRjTICPqAiA2MzrK8OLtcMU1oC7SGiPADYEszXqsQMPVGJLo58Brsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDulMCRA9TVsSAnZWagAANcQP/R61rhHsVU5LrJLOpNAM\nY7UIHRc4t5qFRmujDodMnN+Bfr2+vlpwTxFgfkoZZMhQN9qI/7xDrmBF6dxd\nPatH9nJYvlLDuZVTzvMZ1QfAC4rrFnllPRqyScg0U8ZsBQJc+0K6XIsTOd9Z\nHsjNLoH7Pn2b6EhGqzgb7J8KiiT834NFL2rXfn9QHDnoXnWSD3wVvDq7wn63\nbIezSPU3eKiiQOlvdIZ5XHr/rP5zKg4QNVyqE2hc1ybI25XORpsGGe8fFvBX\n8LgEdZdU+unWS1XrY9cSONRhWdFDgv5ihs/0SHELCLyhfmylZ8bcDuAcy5Yv\nv9uyYMj886dKCzbCWRs0AyGiSAy1jxzazfuRMC4K86eh2/mq4LMud17ESUC/\nkCzkbewV84dDOusfHAXtgf97g7t2ivPNj5AiAXcpt59b2v83pK+VEMUv89rt\n6sLpm2k95zeTci9N7SzT1d/g+lS8NU78gXlNnGSWeY/ab5NQNrolR3mQDgIy\nB8CizjgY5jOSz9cjk4+g3wGMle0m4mfX1QyqBIr8mfLKwqnDKUHrrRwTBAnB\n4lIS7rwP4Msrc7baFkegAiXVVhDpUXTwGWQYqMhJlHkVdV1IIuPWOwnrGoOG\n5ZsyciCZkR9lrKSlsE5nrC3NwlFec5ExyIqqVa2H9EGC96eZyaMUss4/IxcY\npHTQ\r\n=lzWK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "_shasum": "10e9d55b8c6a181408b5384fbfa10e96a3930b92", "gitHead": "1aa9796f0dca92247151533c57b8a268ac28e1da", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "perfs": "ts-node perfs/main", "coverage": "nyc report --reporter=text-lcov | coveralls", "webbuild": "browserify lib/pure-rand.js --s prand -o lib/bundle.js", "codeclimate": "nyc report --reporter=text-lcov | codeclimate-test-reporter"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "4.2.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.2.0", "ts-node": "^6.0.3", "coveralls": "^3.0.0", "browserify": "^16.2.2", "fast-check": "^1.1.0", "typescript": "^2.8.3", "@types/node": "^10.1.2", "@types/mocha": "^5.2.0", "source-map-support": "^0.5.6", "codeclimate-test-reporter": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.1.1_1527703882093_0.5056576811871254", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "pure-rand", "version": "1.2.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.2.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "d13e51b4b83131b735545f2a08cba33caef72dc4", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.2.0.tgz", "fileCount": 31, "integrity": "sha512-/5RcSvT5hUbzsSno9C4uFnyVPujqpmFojsGk3qWZK7LemRYP2Fx3XmAdaCVvQjcjAl7loofNwDbvvtC3gUDUIg==", "signatures": [{"sig": "MEQCIDl3ts8Nx7tJVxofbeA6VCXxobgg8+pv6iftknyuf0tZAiBnZhu0LNMxVLAzR0Ms+weaAPXF4mQzZtwFvPH4rRh/9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdhSZCRA9TVsSAnZWagAAOoEP/j8tHG3bvowIdGJSapW7\nMXuuQXwbF31eFtd/pEvPMe1AINlqNKYFpwLb5RSx4seqvGRkS3/T6OpmvnKM\nTmapI0hqCe+nxvrpXx4mITrdjv5aaY+Nkw25jhBKcYnNLV9HJWEtDJZ4SuJN\neXUW3uxVkiUZ9RyPjCavGmyooHABCT/P8/J7i7RJmBxtVXrHskSal+emHhMg\nTQ6QSNuWLBe89S85k2eJhZapMNu97d//0q7bb5pHVycXY8onzt1I6GTZ+zyZ\nEHlbP5UF3uBKoo+YPSGaLFvnWqORwVyyzwZk/lrNYRfNqtsx1ozLpoeLhFBv\n2jYJ+ZFQb9tUrmjn5uXzj2qk/sS+Xvk2dM6jgSvTqG2knz+xShyn01RCYPjv\nF5ExIJk+2b4Po2d4b7aXxpuilclPz7wm9BvJUF9jwKh9+eeOP0535dlU22zA\n36ynJHI6iZKxG7NchN8ciumo1t/wWSsHGWCkeyWU4PojECV8F+/iTP3UlT6+\nVgEH6UlL7jJ9yg17J1xHleeGK5Zr3z0vavboztMuC0p687RS+R09wOCKcfnd\n7LByM7fvLN7T2F8hE6Q687fFBgCc5smRiDNVCrcV0RCCPMT2zSCT4KH7YnxY\nqi9TAcvvEyxOGVlnmE5rvK8w7NnRpwu5lxoe4hkP++Es0DohlmVkfcxO59oA\nfVxQ\r\n=tm6B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "_shasum": "d13e51b4b83131b735545f2a08cba33caef72dc4", "gitHead": "9a44366fcfb5a6f6d32f2d98bfd77bc9af3e59e1", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "perfs": "ts-node perfs/main", "coverage": "nyc report --reporter=text-lcov | coveralls", "webbuild": "browserify lib/pure-rand.js --s prand -o lib/bundle.js", "build:esm": "tsc --module es2015 --outDir lib/esm --declaration false"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "4.2.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "coveralls": "^3.0.2", "browserify": "^16.2.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.2.0_1534465176943_0.3006516248568707", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "pure-rand", "version": "1.3.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.3.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "79e1487e8f489493846bc1953c70e938a66645c7", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.3.0.tgz", "fileCount": 36, "integrity": "sha512-HdDkJHGeXk+tC5R/PCP/+9wzW4f4hMPlONGs10ft+1qtDJlvpqL4ibQi97feDFqe49DjLMmyFGMXebPiFNdB3Q==", "signatures": [{"sig": "MEQCIFXzW5Qjxe5Q3Kjj/ShtspWpgQm+XPzz6IQ6yzaJhSv8AiBD7jgpxj2uVLGY3+8BhpyVY3MBLjuYggDlo/Yky9SkVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsAyrCRA9TVsSAnZWagAAaKUP/1LVt3RP1DyynT+kx+Jx\nJqNTMHGAGf1bMjedVwtGr9bU1m8kLbTt4RgyHklhMpsd89LbfqQj3qPz/l/H\neLTu2TomQQFnFuJy8rNMl55Rj2ZmvVWCTF/DogonjuvDAX05/Nh9jBUWFfQK\nVcksLgFtiVxJ//ZMGx0YfBXmO3sCYatFbZJuZ/117T3+Oj1dYiMYCEPJvWpj\nJLRM8bvD0QRjddeN7bRoGz5dL2Q/jVMVMzcXLBSdkLJSzpfRZqdlPuuxlRtr\n/CKNsrD3A/McWPpwMZjj7rmlcAtv+OBEygRZxpIMOMgVvGYXncRX5sfmfZMo\na4AYS1yslvkdXInrOTHoWFDtTHxx+RUagIP/7a/o1c/E/TwrmZ3lFOh34m0U\nmPNzTWivkCddQHx04BZmjc1fu2VeqFf5pX1esNDzbCBId42FSbTO8c2pG5zU\nr25OYDU8k0Kh5sjHPCudGpRFePHwf+7dkSPJ1BBVY9aaTL+5CJ6oex+qym1I\nFTbpzW0yLZC5ISBimxVgYn2DrGW0rsaEuLbKEd6kaa6PIeXHPGsgcr0A70oj\npo12L4xEoGj8pzs7g5eAM4srnG2MExqpU5+cCVWfDYRAyFDY837yusAwSjk8\nEb6NSuGEjw1fPbR7d5VQ1ly6yS2PVDUXNgVwj192dr7i7eCIx1wHyWhNRSLH\n0pu0\r\n=JjF6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "_shasum": "79e1487e8f489493846bc1953c70e938a66645c7", "gitHead": "c2cd2c29deb3b9eeb6b8d43fbc328440a9cb8266", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "perfs": "ts-node perfs/main", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "3.10.10", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "coveralls": "^3.0.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.3.0_1538264234982_0.3272898051684592", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "pure-rand", "version": "1.3.1", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.3.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "b325f07630ba51c08dc41b6136bd7b24ead8e5b2", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.3.1.tgz", "fileCount": 36, "integrity": "sha512-xpwaEN0V16m7LlfKbAu7VpO5hkvG73KXfjTPgC4NAU4FN1bLh7l1Xa3M8g1HJeS9aQ2IM5uoDcq1dli4U1uYaA==", "signatures": [{"sig": "MEUCIQCO0ZxGLxzLXPolR3EG/ooxkDMj3I+W1OKW2x7gmL9kVAIgPpnecjP0xoFcesEnKyzPbA5HipNPUhwPkOZlSfohzaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsBFfCRA9TVsSAnZWagAAePUP/1mhr8wRpPks+ugr6G/U\nhaDll3pDTS5nQ1036T0kLFU4m5wLAZ3MiWQHBasY7rhJRUkpogclvDu6WSw0\ntBri4YC0gaerW8qqq59sMmQz+AyaYdH3Vq6nE3r/Se6PiBnQDAeHH7V0G/Cb\nPyUBjMx97awXGkP21J2fsimpdrzQ5/MlNRmdG+zaCMm1DBSyEVR5q6FKfE9j\nV/sYi86Tu8xbifDRiZB9iMAwaTKhxkruU0+YWjzJUuxxC+v4vp9/WsFnbKhW\nJyvgYjs4+B30DbflslS2HSMaDZUZre3gm5agWdLF0w6LeSM8okYC4LPVFUA+\nsI1v/oohYNqCSx8tFeJvJShg10GDrJHr6jqu95Givyl2h1b85ZDqdH4Vh7NI\nnW6gUc/sMV8hRb8JzbMZUTaOirvWbds8Zny1F68ld+7CJzDGMi5KqLiJJLGq\nukNO1kS+ajP8MomhFk9Cm2yZWi1C2gX/wtGzWM6yR/1g5xqrDtR9hPnoNcWO\nRJbJN+srClDEN4VSmX2Tspdq5EruoS0ZFxl/HKm1cacC62KzD26UANuS4W7J\nWsrdC+i7S3Umj4/1prGQvAQXqnd8SQIYs1EYllWzOeBQCMaSp23Lr0qGtleY\nsIB4tfEMM6beqUxGbPnBeWDESQQ+lBnYNyAMFrhZ9oyrcrwIdrjFCX8K9vMF\naWP2\r\n=haJ9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "_shasum": "b325f07630ba51c08dc41b6136bd7b24ead8e5b2", "gitHead": "1ab12854f882f081700362fcad08378fb7ad3475", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "perfs": "ts-node perfs/main", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "3.10.10", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "coveralls": "^3.0.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.3.1_1538265438440_0.563805048168079", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "pure-rand", "version": "1.3.2", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.3.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "3f93241e98f15fea828067e5254caee543ee0466", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.3.2.tgz", "fileCount": 36, "integrity": "sha512-srynRmJiE331hhB7LTlMweSlspYB6UScrYjXnvqg++8QJlUw3kBVp+cxS9Z4oz/dtS3fs56XaQvtrKEdGNFLRQ==", "signatures": [{"sig": "MEUCIHYyOavTWtxTieNf3Rb2I/W5gzxHRx94be4paOWo/SCOAiEAs6guz5143ATIDHEY6CVirCd7sZr2e+ia6l6+jE1Xksw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsK7LCRA9TVsSAnZWagAAmy8P/24B7wRdxuxIDrk2sdEw\nkIHAMqRqju6BTB/jrAGmqPPoRIaPD81rDQudyFRB96GIVaA1AG379ZZ7uKRi\n0x/U/fQjoxkBzUbvyxnEhbXRRbRWBXeX+l4aZxAmsFvTLAhh913hDl1OUsyz\n4C0QByhkexqvVJ7IVkmzbO1Od0jMxQRlTJ0B2iYZIUaCQF021iZob98/2Sn8\n2jXzJEE3m5VZzi+VSfDuIQRRSJYL3CNQbqoguP37whkwcAJdxs1xiCg1XsBZ\nnYOrdDtxq2tBFfKILAf+oCbSVbChHiGGOvRG0TPizec5dMc46CfzPdqGMKKH\n2qpvFb2Pucn7IaCX6V78CWSqvwRlzZ0dsBAqNsZTYJbJAhgaXtv7AGUkU6U7\nAToWam1Tvhov+QF/8u/rNu9WpVQYqyHUVrPTqAr3j84K2N04nrgoH3RbWVpr\ncurr3dcBkpj6EI/2wXY3NqvlLdP9W33/oakAfXUXDAIgQvsCMNNmMofMapF2\naNmr7sCOVC35Q+6xEjwJCl3ipipVVKN0tPt8viLo6QTqtyyYdlfIOjIKDcnp\nDrq1FsSRLxf2ILhTch4DphN+S7huGkVMOa0XJUSypjEZBMzRKESMak32mP+I\nEmAr9TZvwhwTSLH/qsSRVEn4jKaOtGyoll0o2H9f2aItYGKzYspWfyoM+O/6\nvX0E\r\n=qUfK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "_from": ".", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "_shasum": "3f93241e98f15fea828067e5254caee543ee0466", "gitHead": "300f68743385fdfc4956890d9ea2b0022d1c9d2c", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "perfs": "ts-node perfs/main", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "3.10.10", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "coveralls": "^3.0.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.3.2_1538305738938_0.5331672030549917", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "pure-rand", "version": "1.4.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.4.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "fe334f00bb92f25ba46a80f71b955215457bddda", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.4.0.tgz", "fileCount": 38, "integrity": "sha512-CklGkKJHZX6AZ9a1xehaCB8d3YipDwewjIUvwtIDhd2BRAzpIhau44A3XMKKlkHsS6MFmh/FyNa79XDZMOwFJQ==", "signatures": [{"sig": "MEQCIHaycDkH1Iks5l/q7qwHJubfFDqzkl0f9u8GqMg95GFWAiAvB/KqplZZ+wF2bSPAOxzD2Vx9IrihBHbOHbbjf8Fb3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbt/EPCRA9TVsSAnZWagAAqi0P/RWZnSN0Cyd5v//HDlDY\nIn73xnSDzfG27NLi962CmQWcv0RbF5bHGAvuEaIk0cvcu6gxwNg7lbpPuDtY\n2tvuR1hsfmG89Xhzd8GdX/TYlOdEYN1Y62tFNO7R20/dJ99S9KUvUtc+XwKF\nwfaEwLx7JzXe141ExR0Ow/IJaUh6tVuRE0ucNpBFlDK/IMMkhT2Lxl37iIlj\n6dRWS77K1p6qJD7h1BCBJmCCzoBVOi708nWyn+zWUBgjhdH1PkDVr0MqPzrE\nUgxIdqLbfioFe8+995z9z7E82nxrpDIwu86kTG8ISCvB+armVDE3/hIBDCAZ\noPPd58B5kwjISlLih6mtwBOhUONvMlI+N/+XAgxccGQKVlGyOcVlEn8TscKl\nwsfoFko5pru+SMsZjgl7aVKcoAU46pN065Ope82aujI3+EoX62jgOEAEt4d+\n/ev92LoorqMGtgMoemTcdncvk/QATPL6EalijUJi5P8rdkMnyZWfCZjqIcO1\nT4CHwC9ZzhbP2E8nKlNFQl6j4fiKKcLxCHvo+tLJ7OE9ejCjIPE4t7gUSh01\nq+CICYjw7uoHxwXvzsLp74zcTASqBeQ/F+4ooGhIJmGGJ9n4Rr32xneHPrTz\n9Eo9AFsyxqckzTUe31gUIhknv6SIMHNdgOj8QQxfrp+sNCntmuMkho0+70xO\n2Kml\r\n=kTOb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "76c7acf6b5fe3d86eff89cc35396b1ccaba14a33", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.4.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.4.0_1538781454430_0.7428734028628616", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "pure-rand", "version": "1.4.1", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.4.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "0bf262dd37c0692fb15f0fb50347707d0a4b7d28", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.4.1.tgz", "fileCount": 38, "integrity": "sha512-H7DMJVPinAejngfLeBc1LRZFhUDipOeQk61EuKN3TwW3pt2C7tdyP9o3Ez6bwn6OvZUvqlDmpg9TrW+IcAGfAQ==", "signatures": [{"sig": "MEUCIQDK4ZwEaNKkECZp7Nt2jGodLZzQrF1Tz+oI+PHr+mh+/gIgSTuPSr7TVc0/qbJFx+NCa8ERCsUQ1/+EXaboQ79GGJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbuIwtCRA9TVsSAnZWagAAwqAP/Ah04FmuRuW27qEFRTKy\nhOLxjDmmXQ3Ksg4NlT+aKcDToqwKMvw2qUrMpCV0Mg+HgIeaG3G0I1UmHV/I\nVbXKyQbQy4lSOeRZ/nuCRD8amPdu7gub7rkT0aE0SF6buAhaGB9GrfNWCOS2\nUkQtdSnbOCAblxmk7JT9pva2Yv2O0R+H/l2Xs06DardttPh66UcMjQvqnuR9\nukpm/eIWK1WGlVb7lgPE8nk17se0+CIGSm7Eko+qegieiY5G6dkPm9e5Y+Zo\nF6WV65xL70YOmVzD7TZ2g+Fc7MnTTK/4oFcabW5Lupu6YARoXdWVuh7sAjOt\nshkoEDsF77vI2101KcFxx0gPqU8FccSmM+TrYOcSnyiUULZD2MYuJcu5AEeI\nIQ4spGYEmyAJpOzUFjeM0brqaicooL9R5OS6HR9MD/NlMcyE4hPhYzpKYaTN\nfKdYdzbYSIEMdG+Nh2/GowH95xNTSgmhvHZmJtvqsF+XDDxUpq2LkJ2X0INO\n2vUaUMkdqFlRDf5LU7JZrP4RruKH1nFpGvBg1jkkKrKHpzqaYw30EACNIPf4\niQJMcoKSnrAc0dwq3sqdlF3BXXZw+rk7GMq2RKsK1vp+HGoDnCXL9hFCFW7g\nrZdnMrYT9qEWcXSFno14aRho1y10pfl4sbWiy4YElgrIyMcY4VB3L5g5UpvJ\nZlRa\r\n=tARX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "ca0dc43fb774aebf7e57805d8f8a32793cb5e1d2", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.4.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.4.1_1538821165066_0.6186332379575188", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "pure-rand", "version": "1.4.2", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.4.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "bb5b3b47a81e3cf089336dcb97c5b8124c9355e4", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.4.2.tgz", "fileCount": 38, "integrity": "sha512-5WrOH3ZPZgwW5CRyeNxmZ8BcQnL6s0YWGOZL6SROLfhIw9Uc1SseEyeNw9q5tc3Y5E783yzvNlsE9KJY8IuxcA==", "signatures": [{"sig": "MEQCIDQFcVtHD1GvTJNHCIvliGzybF/0W+Jx/429b+6/OLHUAiAMptJjCiJGOtvo8JsTsuR3WzV1QGvIZqVVTzmYU/7V1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbunnaCRA9TVsSAnZWagAA2gkQAKKRp+LZQ3NPkpMRhgdA\nSdvVBORR/GI5eRT+0sWLSN5AxveYeF3MpFpjo2aHKAH09PU3Gth8N88AqSX3\nc3VsXnY29zDGi52TSHRJZDROhiUAR5erYBksMW5xSA1VrBYcW7Lr2CDMMNdU\nhnCXOXjroBzk0ZODYCP/b9QEhZFUPtdNbOR2L4p/dnfKYwoxEkvWUsvik1bZ\n1LALiwc4Jkw5yU0fYKsQ7wqW7BOr5TydXcqLgR4c9fkkb6+5nQ8oHGSBy8hQ\nYq3HBw3ZR6F8ptCyY/pVDbiYBKBko4p9DsUajJqJSa/z7B8t1fZgcSTYZH7t\nQP1kyzi1chindmVTvvbrA26XXNmW8iNvA5cRyVOHOYHauuksFVR/PK7E12kk\nVnrWBDtAGIdqq0jEiONiV4z2g9vtW9MVm7smdJ3lSoK9a8bbaWCBoL4jYxP1\n1FInIffr4n5Z2b1dc6qbE5oeRr7FGbqLHpGEzxs2Vqw3KJ5uxW1VPffnxW1v\nUO76UZcZVELb3CVjfQMuc6dlkG3GNu86D2mScR5/I7VU3+LYuCfPjgnDPC3Q\nDCoxJeEbdD0yynFOGz0TqrQvVNBUItFVnjMjVRAVqEgkC7/e6lkg334bvaSV\nL9kq3FI+jbEfbKNkw+2gYR80aGx2+74uEkTl9HS8pKsf3hPwJzJDIFzus77P\nNduS\r\n=Cqt2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "5f36e2081080b3b0a2ad12cd0154e0e5ee519357", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.4.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^12.0.2", "mocha": "^5.2.0", "ts-node": "^7.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.4.0", "typescript": "^3.0.1", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.4.2_1538947545492_0.9932322025998985", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "pure-rand", "version": "1.5.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.5.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "4deaa72034e67fbe4e7e53ebeceb161030999857", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.5.0.tgz", "fileCount": 44, "integrity": "sha512-acykcCwcBmt5l4w/lPxPUxMeYLb6qwsve3v9A44SNckwOyuXxZgTfP5qAsH/CyZSGkHdUnOQGmckOExOldivOw==", "signatures": [{"sig": "MEYCIQDj6UR9w92rNM399PvOxZCyq4t4MeWGxbZrzBTI9F5PLwIhAMcVBKDHGQImqla/fcUiCIDL5ZNx5wz/kebqyNBqlRTW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwR7UCRA9TVsSAnZWagAA9b8P/A50UqBdWuNTn/iG/xPP\nNKArvWhH1aa6THF5QfYObw25/BRUuAOmEAHd4Hy/aePvDTTmgwkq2+J65S/3\nrvMse/1XtK8gLMikHgOlf5NZC5G9GIL77YUJ4N9l8tPqCQjnY4BPfE9iVnlL\nJ0e5vvGjme5S2Jlo1hHQoPcjI2aTLmCAM9BIMt2ku+HUFdeQoHb/wAgX135G\nUkqwuAQXL7hnseC/KZc7tJ2toiBs7agag1rsy/lrD0kExDrjWG1IhUu7OjQi\npW9NjnVD4mFUzlIuNz5D9StXCqfb9TuOqXRT6Pk7u0t0sckHVDTpnCzXiLmt\nz8I08RyHm+hP/QRsHPYTgi+VLkgmvoTr0OOgzYwTKBA+gZI7QQX1WHOiiePV\nANTCSWTw8JtqCtQ4jFdJD8RAziDh9nZI2ooW5pxn0SZarP5NM+nbXSq5ylY+\nbRtbjYgiFZgt+9Fw01Rb4ny5mpi0FA9cYcS3NHqwR3BU3nzKVR5oh3IMihOl\nr3CbXqpPRt+ET0yRvctpDXOWhSJooUI7ZLQP0UIhSxjobwKRcJ9Bo+uxhbL8\nZEac8/0ZoxNVwX5ZoxviXE6j8vteQrqduZIBPzijlgu87sNRj1ii+XkvbdcX\ngqpIk6o46UX+Es3rHMx8MTaj1GTytsqZIjMtDD/i0cvyOAbkWKIJiTnsSFC3\nAPUE\r\n=KXgF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "6a67315545c5581c5faeab6b2dab4c12d699d586", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\""}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.4.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^5.2.0", "ts-node": "^7.0.1", "prettier": "1.14.3", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.6.2", "typescript": "^3.1.3", "@types/node": "^10.11.7", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.9"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.5.0_1539382995081_0.4482252111082867", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "pure-rand", "version": "1.6.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.6.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "7bf6e90d68d796bfa93731c2efdce33c54602d67", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.6.0.tgz", "fileCount": 48, "integrity": "sha512-L6ER1SlIhjuuoWf8N5J3ipzkFeBVduRgeOk87XuPdCp0xSPm4WYz9CteM7fCFaiPYVmVMkoI/nt6dRkVZ9SDNA==", "signatures": [{"sig": "MEUCIHT1FgDYLswjCy5vUrZxe379SZ/LJiqFIGDhGSYBqqSWAiEAj6WBjFZaKcP5B5o314fFUS6+uRB8/XUxEEt1xAYozpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFbj+CRA9TVsSAnZWagAAEUAP/jFykG3zFw0lHsPRoXDZ\n5DNif3FyQ+kwLoOkzOwaqXXhpRmoKDwppeKg1fqY8WobHoBvBaHP5/Xh7Xtu\nlH379bywsEHyawrDkkmjSv/5B49O2P7UTOUyZv4GCbuaY5EY+6zlCPjoBx4m\nPfpzNKzydbhSO7SbyO63KXFxNCmKkRpRwVmQLZj4tlGPWfYViFvruhgPC7t3\nfefbS/GjU4l4+iSKD4/DDKzdRM/94NqnPsvUkjw/mT/r0AffXfbNckblm7xr\nd0k1goVXgrZzx91ogDIIzoPfN0waSwn+TL6KOzpt/FgPkDXX0LG3QtGS94Bx\nsvVOWFaT7TuhO94GK1MKNXRUp3+ARFMuSQj8ClmzIyz9PyeRQEw/AZYzLYV0\nTVnottLu1cK4P4renrYH7NI6PwtZVqUfY7wHVvqpzDIVrgfOA5Tt7NAno+QI\nKqW8QP0HCQsasbcPOIgqhUUE1UmpXWQov8kHXkd2aNoJBf5/rGpMROmEsnXh\nXJ2iica+PLoRjilYCLIAH3bnyMSIrkplVJ2Sv8hOJD+h4v4cGLBowKAvOgQ+\nJaEvmIPZAP/dqX9fxH6201ODl7UwEgQW8o0iUI/vCbM/swQ5NuaA4eDbD1kC\nDoUQdulFcd7EqepWx1fwGsOt2VEbhUooUUZn/cdw7Tjd+Y2YOl+84/tAGJPT\na7Yi\r\n=2ZPL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "73be107fd5e225b2d21502d4fb72ae3b50cb6b9e", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\""}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.4.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "mocha": "^5.2.0", "ts-node": "^7.0.1", "prettier": "1.14.3", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.8.2", "typescript": "^3.2.2", "@types/node": "^10.12.15", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.9"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.6.0_1544927485418_0.07017972180730347", "host": "s3://npm-registry-packages"}}, "1.6.2": {"name": "pure-rand", "version": "1.6.2", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.6.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "90b3ae78efe36f7e6e27bfffedf934f77382e6e6", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.6.2.tgz", "fileCount": 49, "integrity": "sha512-HNwHOH63m7kCxe0kWEe5jSLwJiL2N83RUUN8POniFuZS+OsbFcMWlvXgxIU2nwKy2zYG2bQan40WBNK4biYPRg==", "signatures": [{"sig": "MEYCIQCuSCQcdFyYBqQa2XY4N40ljEEWNFQUji2ZqILi9pqFGgIhAOHNy9EVVocvbQFpdMpB5ULd7d0Z+6jTFxkiDtn1F+1f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLnFgCRA9TVsSAnZWagAA1cMQAIWzW+++lgJYK+vrV7nj\ndNIA+p0UZV5bpTie9JBD6AI4oCujaMkrvEa1kbFELlSXgYQeHWgJQeoXwMRY\nNfxsmL5y/U2m5lcmCvVRr5EeqNxVzJi0p8gKTCKg8sA1FfINg1GjYXD5ghx/\n9kjJLSIpmauSm81RXnxDy6mLYLGoefTwq7EZVOQOAdai1se5bmh72IJ2ZbnT\n85ZDtjpupwITeP0kqyWT58AzJpA5PcpDBaplIlUO34rnaX6thHUvvZKEDla6\ndGvSm5Ej6x69ot3x4xKeaFZr9VMMDO8ZspRebvyVfONHsucZjOOZU+LVoFMW\n97fkYvy8CE00E1J0rsylmLXaG54QKVTVnugp9rNC5/c6prMldKM+TgWzjGS/\n/vSNJkiVIhVGCAynD+fUMpd+Cj60sowjEpbLeeqMt7iY7fLjM7jzdO5odGIz\no5fNHJMCLLmxux/c3SJ/ybA9JBPg6S8BqS99ezhg11cXGCJbF5SIWkfGBWBL\nemKj4vd9re7rkw2G9zPtXcsnKLGJl1EYWNHJ6NrE8bvug4QvXgdVvp+8ltf1\nEBEKY8HTG/ATy3qFiQvxnbWqziyL2xHx+rN4Qgf814NyHXT6dXfz3KRqJmgS\n/4kEyKOKXTRadiRXZ6jCcjypsMlSp4zTtmVMtWoKgTSddqUpdciXdZNaXjg8\nuJXm\r\n=fr+N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "dff7fefa980c6380b288defeba0a9a636ad9e60a", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc && tsc -p ./tsconfig.declaration.json && node ./buildTypes.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\""}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.4.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {}, "typesVersions": {">=3.2": {"*": ["lib/ts3.2/pure-rand.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "glob": "^7.1.3", "mocha": "^5.2.0", "ts-node": "^7.0.1", "prettier": "1.14.3", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.8.2", "typescript": "^3.2.2", "@types/node": "^10.12.15", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.9"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.6.2_1546547551401_0.04357945312234279", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "pure-rand", "version": "1.7.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@1.7.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "8b119d05f5f83c409efbede897f1386c4df32313", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-1.7.0.tgz", "fileCount": 49, "integrity": "sha512-Mpghwu8LvQUpr8NRFmGTgX8mFi8WHqor9oE0cCj2Sk5XjA10lBx880qUXtXQPYzrUL8NVQ87nqZvV7LI6gVdOw==", "signatures": [{"sig": "MEUCIDyfBJbDJn6AE2sTFcvN7t4CGrILFC8k0JKfU8ko5s9BAiEA2+N0ZzEKxAhlY9pu/hsA6QcQbfVYLSoI1E2OuKyvE84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrhx7CRA9TVsSAnZWagAA2lwP/ipZrXNA4Uw0z+izZXkt\n2cjlCp8sss8+fJkPQxsvALrFf0n9XLhg6UBepIv/F6fFTKt6rr2zR5eR+AoK\nIiq/ANXcgSOE5RzveVFbc4UbvBvkxRQFqfVcwwf8zyDzxE8MmOBCCioYOTnl\nNm2/94IbMLufU0/GZG5BQHdwFplMBhkFmzXp3n6Bp+UHY5VWjhWoVgafu0a6\n6VRKkKdaSheRcbs7TDDhx0Au8Ooikr8sjA0UWJmaX3S3Dbx63redtTBBAM7i\nMs2L9Mz8ZSRIQEuBcKRk8EdWaRt/hFy+Rch1P6opfn22g+1Tb+2zkGtQlXWP\nAuxLjbvXWgPzSdl+cgM9Oxb2kzFdz8Y4arkccfQgZ4ADvctU5V88lSRY72Ez\nYhC+DrP48ITiXTUv28ycSAytoqlc8kgs/SNwr1N9JwN/jw9jSHUT8qCrJqs/\njuGbdYeonvJidJWdGkydgvKzlBbXhD+1h8ZWkF7QS8m2KD6a/xLG4S9m13kQ\nZsaY/u1kWhF07xhik3vmFd2pJc6ytxpRJoYsj5KHYRpaTy5PmAgTM/1ReEgN\nYWRW2Nc7/eKmu+yb/KeNTidjImdZpSSAaEwNvczqAIPeCp68IO76N2EULab1\nHcL4cx6AKMt/R2G0jJ2+ui7xYoLyXPEzSb0/mO1C37QFpYojMoYMVMR5xn0U\nkHvU\r\n=B6cZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "9568618439327808f1df5367acb874374fe46c1f", "scripts": {"test": "nyc mocha \"test/**/*.spec.ts\"", "build": "tsc && tsc -p ./tsconfig.declaration.json && node ./buildTypes.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node", "auto:bench": "git stash && npm run build:bench:old && git stash pop && npm run build:bench:new && node perf/benchmark.js", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.9.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "10.16.3", "dependencies": {}, "typesVersions": {">=3.2": {"*": ["lib/ts3.2/pure-rand.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "glob": "^7.1.3", "mocha": "^5.2.0", "ts-node": "^8.0.2", "prettier": "1.16.4", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "fast-check": "^1.9.2", "typescript": "^3.2.2", "@types/node": "^10.12.15", "@types/mocha": "^5.2.5", "source-map-support": "^0.5.9"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_1.7.0_1571691642687_0.3418616069698568", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "pure-rand", "version": "2.0.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@2.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "3324633545207907fe964c2f0ebf05d8e9a7f129", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-2.0.0.tgz", "fileCount": 55, "integrity": "sha512-mk98aayyd00xbfHgE3uEmAUGzz3jCdm8Mkf5DUXUhc7egmOaGG2D7qhVlynGenNe9VaNJZvzO9hkc8myuTkDgw==", "signatures": [{"sig": "MEQCIFSuZJSKe9XkCJkV9aXChL61E9SrpDgYcM5+rT0iCyEmAiARs/7ZVvUaNuzTvFbRd4nwXqrhBgfXQnrJHp3GTu/SVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKhoNCRA9TVsSAnZWagAA8wcQAJyBVC3NJzVxv9tpchLm\n1yyWrPT93U69611CPsSOp2l0AYq/LEuaxdp3BeABDALIvt3ckAcGqAnxGQaj\n1djh3zoQtGbxF6m+3hm7FHRx4r/CXbyflTPYCz/zG2Gq4ibDVvoBkRcADJAc\nAv+I+xHp0Q9UizhO2SsdgbC4kX4VDEWngaHM+q89w4h7u7g7tZmxOglfGS/Y\nX4BECbDhAPsvFqJt40cN4t7KlISAHs6gkoFvNeb+6VE8NKbgPEnY1YXNct8D\nOBc9kc1+TQSQ/YPgri5L7NhvBY7jdpcxVWjq0Gb73KyvK1AomlcEc4um1+Ir\nm+vbpDqeDW5GvQQecLcXwmbfSx0KNWEBQcy7tAxUM91g8ZNO4cr8n+N5YLRW\nklKkfFDWUyeMEvhs8RA/lci6Z/ozSfGSmfAFezX7PT1SeaVLAmimc/58ckD8\noB4aiDwB2KQnZ7HrTEESbn1wWGPBSBYIbPZN7BOKSClYcbT6dswlZVkkXmU1\n3g1xyD6+4KEXF5ny6YGl7LrLgto4QsXB1fLV//S1A3mMjI5ZfC/wo4XfzMq+\nXuj+Dv3KbuMeg0MjoFSuIvuxtv10k3d07HCIngxo9DOBFiy+f+KVqJNwsle2\neoxsxkLV2SWl7r8+EipUpTTIUoClfP3DobFl792GJI84+Smr6+Yt7yU5AV7A\njZZz\r\n=irCV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "gitHead": "68eda900316c01ecec84f030eaea83047baecd88", "scripts": {"test": "jest --config jest.config.js --coverage", "build": "tsc && tsc -p ./tsconfig.declaration.json && node ./buildTypes.js", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node", "auto:bench": "git stash && npm run build:bench:old && git stash pop && npm run build:bench:new && node perf/benchmark.js", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.13.4", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "12.14.1", "dependencies": {}, "typesVersions": {">=3.2": {"*": ["lib/ts3.2/pure-rand.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^24.9.0", "ts-jest": "^24.3.0", "ts-node": "^8.6.2", "prettier": "1.16.4", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "fast-check": "^1.21.0", "typescript": "^3.7.5", "@types/jest": "^24.9.0", "@types/node": "^13.1.8", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_2.0.0_1579817485175_0.36211969461133453", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha.*******": {"name": "pure-rand", "version": "3.0.0-alpha.*******", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@3.0.0-alpha.*******", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "1fa9a3f4e3d8e00645036bf9154965461fc2e039", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-3.0.0-alpha.*******.tgz", "fileCount": 48, "integrity": "sha512-itTDyUn20gprHALxeFFZBfFoLKdDGCZvQsWClgbm1igShB7/a4QctZL0MNVsbuMxbD/2BelFFQQH5qu9Rl6cTQ==", "signatures": [{"sig": "MEUCIQDQcmxieEAJIBq8yH6uv470QLgL7BZ/ZbM5WOGTmFuwIAIgBf3U/oYLoXW7wXylsdfGinMf/p/MYLXdosBnGofiUqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiQKKCRA9TVsSAnZWagAACe8QAKGA9zLDV1W5MgDP8Bx+\nuzeHvbqiYYicIyjy3eoqYvCBOrLr7kvcCG9twBCfQ8c4Djt5U+gz8U93rx0U\ngoQ9ek0UuLQ/JNvL7TnLNtpkq/JRj5cR3Rv4F833M0y2DdyYJSdBUh2dszRa\n0SNXSz9KAH3hzeFrQGxEVnCojMpP4p26rEao/aby+hETNtrthV9zPZODio7s\nyEKlcbaVl5nCwNiqpUqqQLA1U+SycGucGRJOj4ALLqOoyVHcfuHtn26aYA8o\nzAQFGShaXTErVBeY25ezJ86OZoAto4X5CkHIA+LFbcfJvGHmOSHqb5sZwCmc\n97hMko+/P7ntg8MwBn5/84WQ7lO01qBszWAN2N5CL7KxCzquvR36S81GtpM0\nkSzkHgYf+ZyjjCQR/e1kAQNFU3HbBXsFeZqaYApXsp+wliKEIY/tTavOjFJj\n1uN8UP4RKt8GSggmfUulX/dBbCFbt8zCD7EVJASsbQN71w4HBr2cZdjKs1RF\ngyzTT2a4RheQtEjROmeMFHlMAXvU7SNGCJH9J8UzajMKEy07w2VFdvRfnFWv\nFcuwBE3VDDPHlPzB4yFcY8+vW1Y/hkVsta9T4TIg/rzGz6F3lB4uJnt5OPzs\nEnTtjtfrTb2r8DBmZIKWZRj2ium6eytR/x13ckywxuFSCdXJlyvG5d7yRIw3\ndY9Q\r\n=T8JS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "b170a5a5599704ed151d804c7c09093026dc0b89", "scripts": {"test": "jest --config jest.config.cjs --coverage", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && npm run build:bench:old && git stash pop && npm run build:bench:new && node perf/benchmark.js", "build:prod": "npm run build && npm run build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.13.4", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "jest": "^25.2.7", "ts-jest": "^25.3.1", "prettier": "1.16.4", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "fast-check": "^1.21.0", "typescript": "^3.7.5", "@types/jest": "^24.9.0", "@types/node": "^13.1.8", "replace-in-file": "^5.0.2", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_3.0.0-alpha.*******_1586037386258_0.6568037584654571", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha.*******": {"name": "pure-rand", "version": "3.0.0-alpha.*******", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@3.0.0-alpha.*******", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "841a5cd45b2177aa6ae712e59724a29f1ccfadf1", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-3.0.0-alpha.*******.tgz", "fileCount": 48, "integrity": "sha512-314keepXe2ZUJaKE7M58RmK2+U42liNGEJJK/NkyKS8O3ZjGA8TdzuVh4CTaI+4y+u9weQ5oVAaB7SQz394Zrw==", "signatures": [{"sig": "MEUCICicNvDTMXvejh/EiMsNzD7D0SJySgsBoBMM7AZTxtxDAiEA9TsHwesVAeHQu8C5lryyR6pxxXUVJ1JVeMMzVzJ5kVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiQsUCRA9TVsSAnZWagAAETYP/2b08x4BPIO+R7nL31kn\nDmc33msLvzCAGV+it6UAHo6n9miFhUbw1P2X6AGJwMTnLjtJfUOZWMFOlQ7X\nOhVNg+KLzB6yBn/oNgBqjx1iWzzyv0aZ+eLAvMWsxwXibAJhe7J97BCEY1j6\np2b0a0OgX8H9hZ5Esal1KePBLUrFdfqdrAHlrv0sbFKGbMGESx+AVXd+rDLn\nGRT4erRZSFjvKsqizSCUPSUmXiPxB5WMtRu12T4A8EfM1+Y2uFyYCnxoQcBJ\n/2cBvK2EKvjob0YVg6kESD9jFMxAhBvYw8Nu3mkEg3/uS5kDQ2Db1231uIoT\nK9NSrkombROUTaNrzabHpvys2fGUhkdedVDlLIP1UpZRa3631zNIl+b3PNmx\n6lVXL7oHxKhBewLjdCCIoE33huYtpEOoGzDxPK75BUGVkmf16HidKRXws3HX\np2jH76WBBu7RKcVao32VumEKZY46v941J+PGAPlQrpLwMBiommA0ulVPlWaX\nJmuBN7t/XbHDrk+I1rD+ArVUnBOZcgrsBUI78NYJtc7sv553GAiwagxBmidP\nH9Q43GwDwKbBZX3i85uiUnkSp1ODfYm/6UYhk9VZsGAxZ+kwkPcR9R5h3po3\nqX1OEmt4w9FKmz7MrelkvLpQz4lqazL97C4SzhKaiQq90vbi3EXDxvNKHfKO\na4xT\r\n=9/Iy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "browser": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "e85f57820a403c3a75274bca8084a0271bae400b", "scripts": {"test": "jest --config jest.config.cjs --coverage", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && npm run build:bench:old && git stash pop && npm run build:bench:new && node perf/benchmark.js", "build:prod": "npm run build && npm run build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.13.4", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "jest": "^25.2.7", "ts-jest": "^25.3.1", "prettier": "1.16.4", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "fast-check": "^1.21.0", "typescript": "^3.7.5", "@types/jest": "^24.9.0", "@types/node": "^13.1.8", "replace-in-file": "^5.0.2", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_3.0.0-alpha.*******_1586039571432_0.7967382430381855", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha.*******": {"name": "pure-rand", "version": "3.0.0-alpha.*******", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@3.0.0-alpha.*******", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "4e701ff3d43384aa7a57cf2d3f580e44f6cd65aa", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-3.0.0-alpha.*******.tgz", "fileCount": 48, "integrity": "sha512-QyV9El0uy2aqqVsEmPoNSIsvl1jbytTRtLR5JPH7h+mKJ9GMu7kHPJJRykVD/+L9uDPIlcHJxDuDozsJqJs7lg==", "signatures": [{"sig": "MEUCIQDu69fVvXHIyOU+GLsVoprWwoO6rH2MQ8mSVG1+dPZRUgIgCvae0LbMXYJlbERDVvlmTM0IhNUx7W3b5S9HloUPbGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeia65CRA9TVsSAnZWagAAbAQQAIutCRF04BiuAFdeztWi\ngS1PKPnxWdM4zCqk2hGy6/iEkcI/mBQBNUSDi5DwN+P3dMVLD4qFnrc/LFfn\noqivnw2+KP7A1GeO+Na+WJs9qQOC2u8zcn0lnPmJdDInfNjkVUJOuI5ocziN\npmWJYeO8AuOeWBt+/Ej0xfQnlxAnPCcp4+ZGQRPn3naH5yZ7Sf1EUkyqDQOO\nGOvdEEPmguGhBstnM38/bL3G/iehenyw5fDXVXq7A84DvsgVygC9LleDSw+e\nALzsHIaTSlnOhfIGe3QZpjErKjXTDm9CbdDlEHAAj7VSRP7G0YcWBl3PqxpH\n/glSGtEFe48tHTW/97NCK6CDNJfxmCYMyH4PoP4GsuDbpLfxITfU3VZSFkoG\nTM1O6FmCxml5lSuXo/b7o2J7A7pH5u5biAMHA3cXxQODeXabOO0r3ku8YKOY\nBww8mQZAI8VvAwI3WoUjSpzGN3otAh0/b9KkOwZeNLo/ccCRdLSYIllehAOp\nrxL13qeG2u/v8sggOPYkhB6VIJfvIu+793lz9TeeNWnSLVGGRzz7ahELMpAm\nu/c1TgxRRqNdO/GrhWV11gWB5Uq0K+f28XXDwQxxW62AzoNWUEZ8cSXyPj0j\nYIy/juKy7oqQiancKEtvLIFf9wwhg9CAegyjPZY20qiXYNQnnJejZryz9qyK\nxSly\r\n=YFRW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "d6bb7c841052798a89b0b86e1172280928d0972a", "scripts": {"test": "jest --config jest.config.cjs --coverage", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && npm run build:bench:old && git stash pop && npm run build:bench:new && node perf/benchmark.js", "build:prod": "npm run build && npm run build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.13.4", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "jest": "^25.2.7", "ts-jest": "^25.3.1", "prettier": "1.16.4", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "fast-check": "^1.21.0", "typescript": "^3.7.5", "@types/jest": "^24.9.0", "@types/node": "^13.1.8", "replace-in-file": "^5.0.2", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_3.0.0-alpha.*******_1586081465047_0.730356720142292", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "pure-rand", "version": "3.0.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@3.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "9dd90685e0c6ff98871f656a5e37fe90d2bfe0e4", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-3.0.0.tgz", "fileCount": 49, "integrity": "sha512-7/U3rk8elhZPagxdheW1UHEhRr0IF8wCs3qyYVVswSxLoRrrrCMyaTKtAYIrCfuTeUoX/O3rN1piY/pX8JEcEA==", "signatures": [{"sig": "MEUCIQCB45n+Di2TGg5/0cHQnoBYkPZPQl4NTlVqpcZkM24tEgIgW3EGF9vYH/uWlyyyQufnFisEVbE/MLBIg6AlHHPAMjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCBEeCRA9TVsSAnZWagAAHwoP/3FNIDLNkjaaA9OZTfvs\nHqyEH2N1/ZlHVeByuy8M/KAXoeNyWPJqHytrJxz1XUrMVKs4y316v5O5Il51\nLFf/Mq7bxFAtXszB2M1MWdqa4ync+tjvhOA5p4ijbWhSWX9s/lzj1O3NyHr6\nH56dezJY9chSomH+G5xCkm5chrEcmTlesPaAW07tW0Nph17jOD56D/yM/iJM\nbE74NAS5lHSAkFcxqnor4lPV+1NKVreetRcOXcXQu2MtKhxU4xWkTh+T5P1y\nIJ+idcAOujM0FqLxw6hoEiQiz1yayHY9anaxgEFBDXm3IIezEFF0OfM5ReDk\nBK4RCc89SdRt+R5vHpLWMDlHV6H8sOlERM1ajABuBGtDnjxyb1SKbmX5H/qC\n53XOGgcPK+KjKctkKVLBpGEAMVSMRTNBUiNQd3Yz5d0By4v/EQQFoix1h5HQ\nXB1jVLVM7EHdgMiQ3oPcKuMpQiuT0sSspHiu+JM0rNLZTyveQVesE3nFOHdU\nk9MQM8C8PmPDOkfnD0jon2j9/EzuCHxUZewH+q5a9vik3gNR4uIkSNrS2I6r\nDvnsico39XfAgRF0Klw7KUBo50iO9MXTMFKyw5xhpUUAjzrQneSqhsTgbQvp\nJy2+K6wdIAXGUADVomh51xNW+/agxxd8fE5L6MBO9QXRZP/irp4NzhEewI1b\nSkto\r\n=Pg09\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "6e86aa2029ba41ffeedc147ed076e7a346f3428a", "scripts": {"test": "jest --config jest.config.js --coverage", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && node perf/benchmark.js", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.5", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^24.9.0", "ts-jest": "^24.3.0", "ts-node": "^8.6.2", "prettier": "2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "fast-check": "^1.21.0", "typescript": "^3.7.5", "@types/jest": "^24.9.0", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_3.0.0_1594364189614_0.7817227269395572", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "pure-rand", "version": "3.1.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@3.1.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "646b812635cbac86105c46b0b03aa5dac1c759b3", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-3.1.0.tgz", "fileCount": 48, "integrity": "sha512-xkCSMNjEnLG/A8iTH9M5ayXN4SCWRP+ih3rxi09Q7Fu0b9jAP6V9H59pOtcB37IsVt3eHxf1FMy9n7YrqdDdSA==", "signatures": [{"sig": "MEQCIBH+oPN6CMpzBkSp2v0Q+IwzBz0F3EykSiYhCAvrRXFSAiB3ARe+DDmLtyhm6UcdzOAYgCKtLsYEoWTrLCELS4vFVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMaWFCRA9TVsSAnZWagAAX1sP/jubbai0iblrW2U4yYjD\n0HTuVBrYULsOpZYu4xc4tW5L57z0Ocxai0y45voVZeoYiD8+l2JH66+EUpZf\nCI/hGKr11KMqmkwldSmO0g5Ad7KbKgBq62vhS/Mwkq3ZqS1bU6nrcdMHWiGV\nbqCffx4uElsZmEUwqCK/pf0wz4KMJM1w5n+vfaWsq/xyV49Zsh2twQbzcEhy\nYkIF9Y+xV7P3MkGSLcuPrbo649BOHD/38H7DhaIgzUvrEZHBxfg5Zbt8Gx1u\nqtowU24nbCR9UB0W4DVHY+QSyORBTgrcmPQSnrdSVYcezqk3trqTjQSIPWkC\nzYy1WS8urha8o3aikD6ln7T7sB3hwas3qZglLJ9swVPsVrJ1AKURZkWRxAaF\nH8NYVfajz8XTmIGlb9AIRerRdZ/DKIPaO9s1HSZczxhQDgsyiOxw/PinaEvo\nbQESKkQoiYoZuKSgI0imFKKI6IjDiohCW2OVDqPvYr1XKOimtU1nueq31L6F\n/jFundPkRp53mvYRKQsSFSoNMG4JqaRGWcDVdS0WFgFg51YwKJr8TvqFc/BI\nkfUlJiTA172ULupLvMuSNeuAJXvDqvIrDZxzHwyMnlQ/bNDA0KWCWtVqe6LZ\nGiYM/BFkrRtogK2Afy82XGY8jCUfhJ12OjbRbKdMkJ8KjXysXJRwVEnn0Dmi\nUeLv\r\n=jTC1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "062041a39eff50db57ad3dd3a3742a4f11e53cb9", "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.7", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "ts-jest": "^26.1.2", "ts-node": "^8.6.2", "prettier": "2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "fast-check": "^2.0.0", "typescript": "^3.7.5", "@types/jest": "^26.0.4", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_3.1.0_1597089156522_0.261985304144819", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "pure-rand", "version": "4.0.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@4.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "df8f44bc1b82c4f3d0e245e8f7ced6f09c1e9dc4", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-4.0.0.tgz", "fileCount": 57, "integrity": "sha512-5+HGyGi+6VygEcP1O4jMj0c5HyFgsP9lEy2uA4c+KBq84y21hpmv85wAzPZ/H+q1TUbP3mIMZhqFg08/HAOUqw==", "signatures": [{"sig": "MEYCIQDr9SXgomhg21CQM+5hX+XBP0Zpp2OQ8rrDIMresswpWgIhAO9nUycIExSZ3OuwvuM+1z9mrn+/f8RUciK2jgR8u4/S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpGj+CRA9TVsSAnZWagAAuSEP/2loVoiIeCBizh7a0osv\nb0r/GKy56N/auPBAQGhsSWwH2sBwfKkLgx55zTBa8yRvqZKGET3q61vKy++R\nbk7y3gn3B21vRMmLLoCwJKFr0mVRwhKJaiZV/NUd8RRDL6gj10STSHmtHeTh\nfWPlbfUo1tGEPN9cS5VkvkkByUR1BRrw7Uxx5T+n7hPHiTJ7BMMpBuxbilNH\nftuKaN9LGsrgYBl4EcdjD0z3SFVWvxLxq4k4sc+9QpikR4yUanumiUOuxdtW\nRwM8G2QMgCM0kmmPUsCsYgWyJOGOZPBpMoQhqqpKU9ZQPgOuhtaXS+AT83V9\n726DmXq9SeoAa1xEtTZNKc01wgfNQCcCuypFzf5dE2fPUYmyMCacncEXtC9Y\nQ11qaZXPK+w2q8SqaBo1IqjhQZM5kmqvzgk0sETAAaq8VzPp7K0ZJsv//gR+\n84Qk6yzRAk8vuk8ENCST8qMU/kZV7h6X9B73DC/weDKcq9GpcNCzu1Ta/tJw\nxCelwRIzCSfczLhN0vx6T7PZLrK2TBOZQQX7lCIgxCYuRmso5JHLFc+GQgoy\nTB2AErG2GxFAy+1EjBDP5wi8C9ZbsQP/FNWWBzaCCUFVVH/JvTfK9mkf47JU\n6xji40Z1AVW8mKRvaIAFeujSGx552jAwBpso5/e6FFAXZCK2EMUo+p17bsH1\npUC7\r\n=X90o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "277d25ab00fc6fee8e0a7bdf3d737686ecb1d043", "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.8", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^16.0.3", "ts-jest": "^26.1.2", "ts-node": "^9.0.0", "prettier": "2.1.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "microtime": "^3.0.0", "fast-check": "^2.0.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_4.0.0_1604610301880_0.05536484250232365", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "pure-rand", "version": "4.1.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@4.1.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "b5c688e62cfbbce271580f61d84d0a0ad97c9884", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-4.1.0.tgz", "fileCount": 60, "integrity": "sha512-dN9fofEFAsg7J5aaQh8vqZhPM6DCNxQLp6w4BcAspQTsUXVVCsIVSnaO9hHT4XBxhMbwb20e7gL7VCknWnPOdg==", "signatures": [{"sig": "MEQCIGfsmblTg2uFCKlM0+ZbCVoOF9GWHO0vAd5WMxtKslUCAiBox+********************************//ZSuipw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuq8cCRA9TVsSAnZWagAA4wMP/iaUrFcbToKvOCm1Df3W\ntM9DG0CGdt9BpqtZ+noScGiEZp3ANyRQaw7Hm2JTd57clfVqWx3z0cpMBoCL\nHbIhavazrfDa+qlKn2g03eVPPyof5k8H7d8kB6BBM0YAgtBOSbO8RgBb5VJm\nOoWKjF/CRN6IVZ+rlf1eaMmhWLHtcMSGicDaKFPqX5p98f5LSBbcpX5w/AJL\nxXNvAjlQH7D15xfFF97ySkKBXasULN/ey4Wd0VnitsMNOw6PdLO+rBdUR2O1\n0+M4H8b1xAp0VT6VCV+JQZTSkr8o3r0KtRkg79/FLCFwiRAbcuLtmRRt9JMO\nkQNhF5d3Kz0WElhXwjfRA3RPi8w39+IhMfKGFlJpFInsaaYBykAjdXKJd7R2\ngi9SbqO/8gnLuD97Smzpv/kdgSOSYIodERJ4rZHvJuylzzlvUvd8NjBAM9YX\nzPMRwjP8P+594+Ix/AjDfU1L0VCbsmq0FeCCDs4KSkCILEfhSREfsJ/hZjyq\n+6ihbqxFhxq1sUUeGOlM/HPIMETftvqmUdYr0lrsdSDkdjYsdEnUGY7YdeT6\nBON/tuOBSMsqU4BRgh2HiQjQrXMfz9JhLisb40I5v9Dmw8Ru2wwlae7pafiI\nGGT55hdTM+ad5n+lW8Lv+JOA1hOPODk/MRW9SUNe/EPwxuxv0+uO3JyrK9AH\neGpA\r\n=g0GN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "8db62366f7f940877b393e062b97a5aa176b10b8", "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.8", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^16.0.3", "ts-jest": "^26.1.2", "ts-node": "^9.0.0", "prettier": "2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_4.1.0_1606070044191_0.22977607507776043", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "pure-rand", "version": "4.1.1", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@4.1.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "9fca2d4af5c4e870bac337ed860977426ed17bf6", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-4.1.1.tgz", "fileCount": 60, "integrity": "sha512-cZw4AL/KI6aDTdqHEbJPe2ZoHM3kSdpJRLJetv8c3tfq9o+PvQDXrHNEpB0AWukAGFx4fmeOerAGwkA4rtUgdA==", "signatures": [{"sig": "MEYCIQDMpf1qQ1ormSvf7F+GnF2HG61RkyxF6IVxjmPHGd4ifQIhAMXE4047h1cAYawgzX7BtYlIpptYckXTAvc9GDneAWqq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw9m2CRA9TVsSAnZWagAAc10P/1DHrKsX/QRIdXMq8TSX\nc2Z0cmuiZDTc4ssHjGSdtLrONxJuES7djDc9tZFP+hEVNeyCCaN3ijsQe2wr\nyK7afEKtHzFZbteTsF+WA4+SQGecYBWJpV2DVWUCami6swY3VOM5xgECDpzv\nHy9tpcrjsdya5g8ZlGwwsZvfs0vmvGL5cKVwPneYMuu/3NGMyY/a8XrGL44e\nebGuavKnnjrTlQEy/kGOMe9DO20h02xzWWcM8oNAgai9FfWkEYfqJBkjboyH\n46FB1MBGfGhxk9kVz9+woBjEO0nmWYUdxK6ZZzTRqd/1aia3VIbuhheAlPnb\n0l0wgGXFAKUpZJaFBVHblRPZPnAdlwBQUUUBaomrteUmvFdoSL1RtlAcSnPj\nRh/gWC/FRN1kyYfCKYMQYY1QEXSPj+6huvNFKgWlgojyzSDTFsnEWA3J6KMu\nFJNvSPBgbcnCFKRhyS7G+cxniLDB90xFC7QMpXfz3PhZTnAsq5t63Y59NvzR\nkbJrzRuHblHd3mqEmm1D9IviQZRM5rrw300u8gB1bWcCtHEYXJRt2TDbhngD\nqXCiPqOBK3Uzwkcw6gCxmheVo3VsJ0HSJHC5ouvYSkbveDfMvUVm+kDYwT+5\n79ZkEuv4o+aln423Ynje0EoyB/TPFASO3gA0rBGt5TgYY6Lli0NXQgCBsvd4\nkE/4\r\n=EuS6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "gitHead": "e008d06171f5a182f791dff211b8e2efc70ba14a", "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "coverage": "cat ./coverage/lcov.info | coveralls", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.8", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^16.0.3", "ts-jest": "^26.1.2", "ts-node": "^9.0.0", "prettier": "2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_4.1.1_1606670773437_0.2238491790616266", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "pure-rand", "version": "4.1.2", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@4.1.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "cbad2a3e3ea6df0a8d80d8ba204779b5679a5205", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-4.1.2.tgz", "fileCount": 59, "integrity": "sha512-uLzZpQWfroIqyFWmX/pl0OL2JHJdoU3dbh0dvZ25fChHFJJi56J5oQZhW6QgbT2Llwh1upki84LnTwlZvsungA==", "signatures": [{"sig": "MEUCIAgKTDrK0QMjKRZGFbaZ7+vepltpY0OTCKi6KPvBJw6RAiEA98v5QVmpc6vmkh44C7fLS3WVdLfIMetnVa3isY4r+D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDxbcCRA9TVsSAnZWagAAD34P/1zEnHRR4wBba47S8vui\nsuJM/YjtCVlZe65m93nGUCordnqAxwGty+SvvavVPwaXyTqNpt7mUZfd8tJ4\nimxqL0+e1yEzzQw14vu2QXZDIj86XvZgZl/OdcuUJnQ315uwawgVMH+cmzi2\nwuyA4qdHaQK5KT/cyDKvp8Ye+EbmJTt08MLYKSmF8Qp8+AJYbLuAGb4WKBCo\nOUaKayesNwWpIBtiIrNFVS8sLLmUHTKiHHhmWU1T7z/Op+ofS1LrCGLCQh8s\nY6gnNGgNKLnhdUjl/GIgAGMuasdND0GU9xg/fn6h7082wYFiB2s2zzq5ck+Z\nI0SwSphToHvHnn1NZNvynIoKHWOFBVTAgpUYA+I65nnVUlMoojHs7aHfYA2t\n1XsBx9nOXGaTSiXy8vC/AD0TIE1bG+83jX/9qUBPFtdfKOeDNo7Cm8nGPvPz\nOzChsA3j3wESFiVBxGGuQKQ/4wWLklebI/uG17YkPiNlT5wVMOAi4dgEJ2Wf\nnGZPub0KqaLQn/LElI7Lm9pqbsvdGFATrxL9+oKOw1zSjmdmKZz9cjAI+4pK\nVlLSG951Hj8RBaQjhyMZkr+MQM6cXZ5AwKN3D+snCy0W9FVji/e+NBIS0CQr\nFsJIgFxVzx+T8j9KilzQUHzxeTpBw7Kw36LoYbdKoAt5V3ENw/Pm2BtGmmrr\n7j+n\r\n=Vjz9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.8", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^16.0.3", "ts-jest": "^26.1.2", "ts-node": "^9.0.0", "prettier": "2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_4.1.2_1611601627899_0.7045475632964793", "host": "s3://npm-registry-packages"}}, "5.0.0-experimental.*******": {"name": "pure-rand", "version": "5.0.0-experimental.*******", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@5.0.0-experimental.*******", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "a060c4740b613e80970c6349641190ae97a80098", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.0-experimental.*******.tgz", "fileCount": 59, "integrity": "sha512-gV0Y/srNkWf2weERWEpmfl6cxbqzrzXmBhks52NdX5EtTv/9Uk/wGo6F79lm0LfAQ3HRbiznaGfiV0gPkJCcFQ==", "signatures": [{"sig": "MEQCIC4ILgOiJzzR3KL5707j2+ARFrq0bjC2K1Soe/f+I/WvAiBfNU1WIEfnS+bbHYXejwOE8OJlHYObeXGr5rh82D4qIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguS/fCRA9TVsSAnZWagAAcMcP/jwp0ez/mVvgRazmn1I3\nydbFSdCE82uNcT5fn917YTGwXz3IB/F+fLIQLlG6tkPjLcSrMeJWb/KtkwhO\nLJkZ+1I/IZ4aucJaJkVKG+jX6L9g+R52zz/G5kK1nKRpzvx35MT2toEyPH1b\nxvFzsWyj8IftqZY+EROCCX69alpWTJjdK0/aWEyf15Vn4/qNiGyluLLNMK7O\n1q2daU/4wK6fs5O3YOMYHKMXZ2H5IZzJhqHBxwN/1z7sDXFCLptbiPvu14mO\n3YqDNSNG4uf1NuQrY9xIolIdjBi23ah7pT0366CGozIYN8kvigSnBDa2tBbZ\nbwUL6QHEsOmZG9j5nt03CvD/bM/rz+Z0xF/k1ZigGl2rMYGhksgsFlNRfAX8\nGIIAUw5lfeT3nRIEiCgJSwUVRQFdtFfJGA9qWesXTHzEteKAsoRjj8GlZQi7\n8UmLPrMwJxe3fkbdhN6EPHEdw/Anb9bNBLULsLkOQViDhr9M+nx35X5spSnQ\nyCg1O3HMz/GdnzSxazGNkOVfiPyraM5ZNWPLouN6qYzz7hfyyep20+8JFR2I\nFxifNRSWZ54HCyn/LsX0LHGiih97GLUQzDH2nvshkDb8IFXj+kJY0RNSU2sS\nkfWFcWSRpgCcuVzNenrVQusBNZe20PubeX6X3EeFQLABZ5NfdqWEj+g5Ua8D\n5WCi\r\n=9Hn0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:fix": "prettier --write \"**/*.{js,ts}\"", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "6.14.13", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^16.0.3", "ts-jest": "^26.1.2", "ts-node": "^9.0.0", "prettier": "2.2.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^14.0.13", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.0-experimental.*******_1622749151511_0.609438616657964", "host": "s3://npm-registry-packages"}}, "5.0.0-experimental.*******": {"name": "pure-rand", "version": "5.0.0-experimental.*******", "keywords": ["pure random", "random number generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "pure-rand@5.0.0-experimental.*******", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "cd51452e5123dd7123242b3054ba589e710d0ac7", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.0-experimental.*******.tgz", "fileCount": 59, "integrity": "sha512-AaPBXK6rNAAFz9xg/2b3XlU97BS8T2yzH853rtGEZIkyBwlrgXwwzNJuRVNZI4/GhodQ7XeJzpaSVWUzWcuBrg==", "signatures": [{"sig": "MEUCIAiXA46QbPnQq6nd5jZPNGrduyCTRtTjf06rDmdf0FC/AiEAk3AaHoJL+xIY3+Gffv6hDtTjhkvP1yqTyAibY4uSglk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgu8NFCRA9TVsSAnZWagAAC3cP/jD14hOPlE+YJwlNifYW\nu6WwxtwYdxfq9jNs7mRBkFNeFK5I1YVhqms6vbZLhGp4anPdwSuz2saPOsb8\nAErI22hYZdGWD3fPobdEUkImEPPquQRVYjgJRSmfsa3pcZSdQp+P+VTEirSJ\nBePjYwScKibGY+dsRzQ2YpSMZuwFvBFICRh2C2/uZSB+aiZp7nq43xbLbQol\ngMG8ll11Mnp83AdwM3ltewidSfZTCSWHM8Wya0kBc3X6p3PTiHKwAvnhkEic\nKo5ynsaVB5tV3lhoFg1tmcFfe6RYX9MfCnaZW1C8fyXnA7eJEMFu9VqH8vV/\n900gN1/lxbmW9kQMLmRYk1qsACSf7xLZGdU4h3IQIv3tKoUawlnTRM1fHkqd\nLuvz39LKgYIi8ULblgN3tLJweIhExrZukchYhr2UePcY/Ibq0njp9b0zyu7Z\nwhWkMCp8koVBXzG59Ltf9cSQIw0Wvscfw1MNf6e6hFs8Jadj3sCV9zHokKm3\nQJEVhdomNtv8p/lDY/twVi441BZK+0xwwzK8Jcm9DOqq1xP69grZGUEYzrVq\nBzA/SJf9qJqkHAlZN6XhkhtyjiWu9EpmVqIs54m3D8uGexmhaF/hDDQowOsR\nT5pMYAroVmueDw89PnxnR4jugoSZxbzypseoxbR85tt6Pb1H53lh76AQ1Li9\nOSyZ\r\n=Fs82\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-AaPBXK6rNAAFz9xg/2b3XlU97BS8T2yzH853rtGEZIkyBwlrgXwwzNJuRVNZI4/GhodQ7XeJzpaSVWUzWcuBrg==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "7.15.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.3.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^15.0.0", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.0-experimental.*******_1622917957506_0.33513088244580724", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "pure-rand", "version": "4.2.0", "keywords": ["pure random", "random number generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "pure-rand@4.2.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "ea08faf7accdad7bbcb68295641881b2ea5eb74c", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-4.2.0.tgz", "fileCount": 59, "integrity": "sha512-d2IwZpSFSLGWmjThAdMECofl07uOMqmuIHqH8mRY7h7Hl2jY5ZJZ9fx9dEBIKkKyk4jmEvS0vnpdFUtzPi3Ctg==", "signatures": [{"sig": "MEYCIQCh1W7O+z6NTiN6/JcJBO30C3G4zOE9XWxrzQab974RagIhAPz/ZxiEYSNnGqEFP1OV9txlO/C4R0XoA8+XQDG+w4aV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgu8cYCRA9TVsSAnZWagAAPUkP+wWqkSoyATeLxI1XBNbd\nzzoz4xtabR9hjRdv1Zm9U8ONFAB29TiBi4l9MsiOGBH/HCchmCI/jfGvg6cd\n/cOp0ryKsGJl2JNTavWAqofDE8Gc0fRUiAyuED0TJqJtDv/+D3DfVi6/I+D3\nAITYcnM8K/tynS8dk6NJb6gBhgLxgaB7i5tgjSmMSBmS8y/r3DQ2buWUrewO\nnCTMCEEf8Bonf5Mo8kAPGWSJdz5ei3pqFBC732Bp5Npft0Unp+PfcROPBOxy\nUBrxczDT65hJ84rl9dvToEo3YV+PqGlhlsiip63LIwPHngi5Yff/14ZbVmu2\nCQZfmolh1Fobz+Ea+x+VmOibGqnCUsKVRe/nYFhWNpKACdKpl3EvAIhSDe75\nGqG0A9EAvgVlEYYAV0jYSQ1thornaCpabXhdEVu54RczTSUogTeYwHxZw8Di\n7XhKiT+YAy4G2rpd2+2hfa6ivMlBDcvI+DRY8LVh2tccecqd3vbc0QMKYh69\n4G0hcHq3hz5pyLF6AUp/PHFdjgaGbFW5PmO2z5tgH9+ngvUzD3lXjRKvXM5s\neIUDiVStK/iDv09vuG494HtkGbTNIGnejOVeIZL0jC5ieTZ03kBmWrl2GShP\nLRQ1AjScSizXuaxe0bhf3ORkBmK5hYGe43P1386sg25R27yugwZsv0kobTNh\nQCT5\r\n=60Oe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-d2IwZpSFSLGWmjThAdMECofl07uOMqmuIHqH8mRY7h7Hl2jY5ZJZ9fx9dEBIKkKyk4jmEvS0vnpdFUtzPi3Ctg==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "7.15.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.3.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^15.0.0", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_4.2.0_1622918936027_0.15930757831078535", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "pure-rand", "version": "4.2.1", "keywords": ["pure random", "random number generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "pure-rand@4.2.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "14c08ab1cebce0eb95b987638039742a1006a695", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-4.2.1.tgz", "fileCount": 59, "integrity": "sha512-ESI2eqHP9JlrnTb7H7fgczRUWB6VxMMJ2m9870WCIBhYkBzSGd6gml6WhQVXHK+ZM8k70TqsyI28ixaLPaNz5g==", "signatures": [{"sig": "MEYCIQDp7Pld73eljNVTMvNKCLzIwTAdN77wpeAfsJhqS02H0gIhAL79EF/9yy777YQf4j8FQVDi3F0EWN520RRmhhHpKPl7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvl1bCRA9TVsSAnZWagAALxAP/iuOV8AUAgeg9fIsJ1we\nPFoqjdwAPfoIB5J1m6nX4f8gh/TjVJh6e/2EmIiQoFgz4GGMKMjO2ChbsPva\nVz/QIqg1veWsxN3SvEuxUHHLj+GdfKgZ/4ezFpk4ctNfvScWZPHK8++DQtb8\n3ekrkKOJ204JXfGi+r0CeqbBBEglUZB8b2+IGoXmJaXD00k1iyNONXiPEZS0\nEgfyEK1VCD7bmU5N6wAlhRyah8+eb2FBClXGsA4d9OBDWD1z46pCUM1DBOlI\nZwJ0i4GIiyFiJ2YT528E5ih/3QzVIF+rGXhDZCA6es9n3x6WH5/tCaFgpKEV\nvKEs8tnLxzP+ewa7zJcv6wkOx0e+yXao4Dzylif0qA+kT7wBfX8WqVBfDy2L\nZM9jZWpO0TvoEk4qYBAEH3HufrVmRMqlh0yfOhLDvOf5YAWFS6jhN12/txIt\nxFzLP/62pz2eriy7EP9Nc2I0YxtkkDtjIbC8hE5qkAYgR6YYJM2zLJ6dz7ku\nK3tTHt3tKSuhh/jFyq2RqQhexlOrJwh5nOfwasDcsnHtfiK0GxrSw0ueXLu+\nG8rwON8sGE3tVCY6z0HpHTs4qZ4ZMJWVXTsHdETzV+2wJATIzlweq1Cl0Zvt\noZFtxRjCabG6OSJFoSGgR/YH4QBFokeg0Z2WLxvJl/SVsRstaokpGXcZEVIl\nhIVl\r\n=/biz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-ESI2eqHP9JlrnTb7H7fgczRUWB6VxMMJ2m9870WCIBhYkBzSGd6gml6WhQVXHK+ZM8k70TqsyI28ixaLPaNz5g==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "7.15.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.3.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^15.0.0", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_4.2.1_1623088474867_0.7862031219926657", "host": "s3://npm-registry-packages"}}, "5.0.0-experimental.*******": {"name": "pure-rand", "version": "5.0.0-experimental.*******", "keywords": ["pure random", "random number generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "pure-rand@5.0.0-experimental.*******", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "e34655172144eb7960a2add72358ce06fe937741", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.0-experimental.*******.tgz", "fileCount": 68, "integrity": "sha512-Z8zQ0DK6ncZQTG0KzdwauIxmsmA0U7D80+kuEOcUdmCvUQorTdlrLpRWyRXv105EFRVJM0i7+iBI4E8bSfWE+w==", "signatures": [{"sig": "MEUCIBI08uZ6nQSCOSnKz/S0J0M6jRQ8r0Q66P0iUFzr2bOhAiEAtseBvgGUrHQoRqYNQKG4iSzykSCcfydbgDhqxMchFI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvqOYCRA9TVsSAnZWagAA0UsP/A8bh8W1U5yab6HyUp95\nYNP8Uq2sTzSIVqTFsxnCFM5llosiA0nieaoUIPxMajovy36pt7RxJ2AAedtQ\n26PjgmybtxE41djgHyg9ELeAFkWbvHozFQUnzQb7eG+950QOalY3O/Rwc0yz\nwOnPoxJJo4DZNSUWvzvmU7zTJ7L54SISEZ5V5PgIh7V9kL7HasygY/GFxLeR\nqvIPG8KryS6ktiMSW2MXMLsg61tAG9Q3yWU1NWVioUY/73y+5UFvMp3lrg6h\np3nq9aRMjRa+lmGyL+F+RZbkTNC1kVvu59Eeu5HO6HgxtCE0T3wA78a5/7dU\nrJjL025zSGMKv5ZLxWwtXfUirtecDax+aO7xW5tKwm32y+krVy9HFMA8zrjE\ntQqDYf8kIA7mk8ftPlu+6Rqhh7vfoQrR36xNPj8nMYBYMj8hSgi6u+YQ+T5D\nvg4dXV5JOIHaM45H1z/ZwRYmJlZZ957sDQOnybqOxKxBFceJwbM9ObaIzAQk\nf8snqp+3pR9W7VzieRQOVSaEL+lVYpDovPXJWlhUrkWlOLjOakK07PVRVPvi\nH/2oU1cndhaHkgAZ4CMYWBZTkCprpd1iZV06hVAEaN4t8j9AZkZG3LxVrA9N\nblHUTsROqA1UzQYAN2xI8d89Cr6+AcaYhmjW2F+qGyMp8ngqsq9HQrrFTVn2\nrhHo\r\n=08JR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-Z8zQ0DK6ncZQTG0KzdwauIxmsmA0U7D80+kuEOcUdmCvUQorTdlrLpRWyRXv105EFRVJM0i7+iBI4E8bSfWE+w==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "7.15.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.3.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^15.0.0", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.0-experimental.*******_1623106456338_0.6884641045383344", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "pure-rand", "version": "5.0.0", "keywords": ["pure random", "random number generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "pure-rand@5.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "87f5bdabeadbd8904e316913a5c0b8caac517b37", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.0.tgz", "fileCount": 68, "integrity": "sha512-lD2/y78q+7HqBx2SaT6OT4UcwtvXNRfEpzYEzl0EQ+9gZq2Qi3fa0HDnYPeqQwhlHJFBUhT7AO3mLU3+8bynHA==", "signatures": [{"sig": "MEYCIQCaT+UM3F0Jw8orKekikehUOB4ZJVu3f5rJ+pejyMUTFgIhAN5Dz3MRivcgcxlYGJCTG7c3sT6fL+GGJxQQXaNcj1K2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgww3XCRA9TVsSAnZWagAA2jQP/jTQuhaWu6O9To46Cgo/\nBy0L4it65HiH4/jsKxpoT6WU2B3OzhbOWSbCmswoYmUGz+BE6lhSk8rjmXMp\ntwQm2RryaJAXqOoq6wUCFN8Y+4ofufjoURmQ3II5Vhy4pK5qaVSATSOE9Uyp\nDmyeuL95bMBSZvhouZ6G+IkZEFyD1wuGutb7OhxGkYTaR1dePLlXd+SiwgBh\nbPiQvXcOQc2Xh4AireXnm+v8Gkrs5cvZoIoJ51toOdTLkA17wkEBgIytfKUj\n4GN3aJok4Ev9nGLP9BTb+PYkDhiLi+os98DXeibjCD/W/yFvcTp8Z4ydA9KY\nzoIqC6D4HxxXCPJjIZzTFsjQu8yenc5xhLYZP5rC3roROqjSushVcCgXT3Bq\nvyEcWMRXtMdG9qCXWsqw8uXdoLnp+i/9odDg3yE2Y9+7oM5P7gdFObt1G7IA\nd1XfVP4SbhPVHWfe3bo5FVX8OdOf95/W+n3M0AOTNrYoRIsa2zciQn0ZcY+e\nGtZ2oEByM3FzhCus0lGZn9ZSIJTDyIFu0Maw6ZfOEWz2Zsnh9Yl53mlvMW6/\npDIRKJ4Q0ft10fPv2sxcf3lNYDSJ6G1Gir/hIAQMY5W4wC+wu74L574cvOV/\nb8T9XzeVVsocEEn8B4EyRtTjCV63loOP6H8/m/FFHkD+hJMC4M1CPqgCR5wf\nsy9l\r\n=Y0rv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-lD2/y78q+7HqBx2SaT6OT4UcwtvXNRfEpzYEzl0EQ+9gZq2Qi3fa0HDnYPeqQwhlHJFBUhT7AO3mLU3+8bynHA==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "7.15.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^4.1.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.3.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^26.0.4", "@types/node": "^15.0.0", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.0_1623395799269_0.8514909181127064", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "pure-rand", "version": "5.0.1", "keywords": ["pure random", "random number generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "pure-rand@5.0.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "97a287b4b4960b2a3448c0932bf28f2405cac51d", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.1.tgz", "fileCount": 68, "integrity": "sha512-ksWccjmXOHU2gJBnH0cK1lSYdvSZ0zLoCMSz/nTGh6hDvCSgcRxDyIcOBD6KNxFz3xhMPm/T267Tbe2JRymKEQ==", "signatures": [{"sig": "MEUCIQCs5IhVA0b5TxOF1ngzEaeO4E2OeFKWZxkvsEYtGfSZvwIgCYH+ZNp1Q4gk+y0CpjNa3G1WI24A6PFEG3iOUHPfsG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiM4iEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJug/6A70PKDHqa1uNN/2fNxjsy3KohHaizdZnvGzQhRdZeAfl0JKz\r\nsoTOw7DrAWhMhi98Yg6Ga6yH4SD0q/ZI98j1sH3TLldDwUXu6X20kcMAjhnq\r\nasz/ZPaR18pqPOEXxc2e8mnOsMubpKplMyGsz15DWtB+h+5zMR3fCsw7qMTc\r\nVEhW9YU6U07D5fxU6QlKSUohDIA3n4GQJ2phdi1ceTG00TeukL8M9g9x66EP\r\n76ReKrUeFduvR8KljeQ3QvXK/PdiIme8TP/H9VI7h6D5u66W9HbY5AMtbc86\r\nbwf9vgh9UvTDt+V+KDOd08L+cH7iAPwE8f/hj8GJvcsWdxU5CUQYMw5GDwLn\r\n0jWv/FLRKPscKK1HDs4cUEAIiCx5I4iTP/t3ECMDG7WC0JaC8/LgdQtlk9KX\r\neaNTz8OEZyvqnJub6k1l4NHhIJXeTGTLEQzAQa6ruc9u2r1yAlzXyPtxKpwO\r\n/gDZyJSRA8vJ47mCqeSDpmPVUS8HV23deECp29r6uCbPCzTgwlgxyCe3X8+L\r\n4cx3Ngm4I4TCAyVgHuXE2JL5HiDGIhSANJJNPZMJMmmi2yvbjLlflG1nKEjO\r\nQocLef+zB3vnLy7b+WK4WEC7nsmNgFrPpZ3hyZhBlW4yZt2T3cocjYn35WQ0\r\n1FC+4aHlrYqJRmjiijCtaLQ9s3p191si5Ak=\r\n=y37f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-ksWccjmXOHU2gJBnH0cK1lSYdvSZ0zLoCMSz/nTGh6hDvCSgcRxDyIcOBD6KNxFz3xhMPm/T267Tbe2JRymKEQ==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.3.1", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "jest": "^26.1.0", "chalk": "^5.0.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.5.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^2.7.0", "typescript": "^4.0.2", "@types/jest": "^27.0.1", "@types/node": "^17.0.8", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.1_1647544452311_0.07474502334389554", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "pure-rand", "version": "5.0.2", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@5.0.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "da8363dad00c7f07bc8c4fea14a5d04710430dc4", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.2.tgz", "fileCount": 68, "integrity": "sha512-XE61hHPXHSgtdW2alp31apFNCSSzwJatdFyK3JF2hFvjGEHfuf2I3aBUEfISh5F0cncnDAZWX1x+SXlqK/cL7g==", "signatures": [{"sig": "MEYCIQClhWWXfvhROAC4SQPHB0CRvSF0h+dNXcRSeWiQHSPXIgIhAO1DbR8JHTJXElFO8zcSApv4rlMsa9VTaOVOkvHsOlYT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGmPiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqRw//XTNcPxBFsNsPqbM+Lw/41R0klXcb99FY3wHZhs2/8VPa/aGy\r\nUn2XvRW69GjWsCnLvUyh68TrcMPSO9V7UrMkm3z1vKQcbwiW24jpsOIoSvqa\r\nxldyogybAEN6SrOtzQBrABr7YL4IP/eLYtj3tWWHNNnAaVUsltzkV8OYbJ3S\r\nEkZv5iDxLTRj7sOz+4KjkZoZJr0MtnlVr8QLrnYQaYVdo30lEjZ/6WRMTedU\r\nkbDmtsTy5Q/+rzfHi7RmC37hUjqKx0BErbqqwfZkgOvp99lbWejsus546eT8\r\nQjstGdOAe6GV4utmKHKx7c79ybuwUcx4hpi/HkSSL8DD6rlyGYKD9GplWw1G\r\nee/nYOMdfJ6JjUOJbPdWzuzu05MzXZyWgtxjA6FFxGoY5WZLXKML+W/1N6o/\r\nCyoE4h4xdk9Dege3cxuFFHGT3HzvS1eUH0vsgeDzzye0dGVJmdl1HydAbUQz\r\nGaiRCG7E5FwWDvG3W6yn70rboeQ91TY6zOlf13C5qkKtTIXQzlBZSnR/FGRy\r\nRkZN7Lg299K8gd+MtnGvLOSVkOy01966/miZfTreInUGVTO2PO0KIsT2gHMB\r\nUEa86FJznRcEGR4bo7a9YKwFWxqY6eqX6pUanK//gSSwd7kHqsgLVFntyazK\r\nTYkVrBDePTMmwa6f1LS1Ptqag7WgiqRJEWg=\r\n=WJO8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-XE61hHPXHSgtdW2alp31apFNCSSzwJatdFyK3JF2hFvjGEHfuf2I3aBUEfISh5F0cncnDAZWX1x+SXlqK/cL7g==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.15.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^8.0.3", "jest": "^26.1.0", "chalk": "^5.0.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.7.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^3.1.1", "typescript": "^4.0.2", "@types/jest": "^27.0.1", "@types/node": "^18.6.3", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.2_1662673889799_0.3249201817590104", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "pure-rand", "version": "5.0.3", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@5.0.3", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "a2f15dfbc3be8433d1d8ed67ee411aa83fb90406", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.3.tgz", "fileCount": 68, "integrity": "sha512-9N8x1h8dptBQpHyC7aZMS+iNOAm97WMGY0AFrguU1cpfW3I5jINkWe5BIY5md0ofy+1TCIELsVcm/GJXZSaPbw==", "signatures": [{"sig": "MEUCIQCnrvsGwfpT+deFJt2dHZHjDB7Sa5u0XxF+mkQT1e7RggIgJTgIfcez5sSl+/ZPJMmjhn/JRvnuPQXFBWX2qtJpYrM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGmZtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoycg/9Goi9F8jfar8SPtR6667tcgq9tFgUtKQu8WtRrLl5PpIeJQ6u\r\nTkpo+GAY8+cLq90niKN4b4w9itJXPsimVixYwLlby6BoWPcErfhZ5noWf5Le\r\nxvLKKvB94rS92/haztRue0SLF8zQf6SBwRhYYtI9k93rY7X2I042yjTfrSw9\r\ngEZohBkmjC3ebSOx/m7KBatGlWHy3b1aTA89r7MOPaQMStczlC2hncYXGOWB\r\nfsBs/c2RQ0Kn/gipvavBzpGWM/8TFe+BuuNhUBkW5yx85jshTF/BcefBMRzx\r\nWTq4tX7+neFJ7N2o8HaP61DxFZep8xeQHupGNIqu0F1QvV4sMS7xiv6zF8mE\r\n6Oy4RzK5UzOfn1Pk0ucd3igBqnDu4k+2tLxvEPGpVTFEbkvEcATzMKry/xnE\r\n+VmgwAWwKOVftIO0tW73AeofVx9cWmXBJA8Vw/AtFZfhmOrhPUsy00H/6MoQ\r\nlFyzKc+VpXFI0vxMwSrVX5b/1RwjAhtAOT4IK75LtNmY7rAN3pm7harhPFiH\r\nwWCXy685+EXXwmHRg0zbEIlypucnxdvfIc9VkGw4aHQigjeKN3PRMX7KFd7+\r\nqzrAIgA3BA4SRumvezeYEEopp/cFANpYOc3SkXP5t5YcgnOb13vIhkirVBmm\r\nD4BdKSsDLuwzdE2ktC6Zg1h/UZj0Fy6JUsg=\r\n=MJyo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:pure-rand.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": {"url": "https://opencollective.com/fast-check", "type": "opencollective"}, "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "auto:bench": "git stash && yarn build:bench:old && git stash pop && yarn build:bench:new && yarn bench", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --target es6 --outDir lib-new/", "build:bench:old": "tsc --target es6"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/pure-rand.tgz", "_integrity": "sha512-9N8x1h8dptBQpHyC7aZMS+iNOAm97WMGY0AFrguU1cpfW3I5jINkWe5BIY5md0ofy+1TCIELsVcm/GJXZSaPbw==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.15.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^8.0.3", "jest": "^26.1.0", "chalk": "^5.0.0", "yargs": "^17.0.1", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.7.1", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "microtime": "^3.0.0", "fast-check": "^3.1.1", "typescript": "^4.0.2", "@types/jest": "^27.0.1", "@types/node": "^18.6.3", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16", "console-table-printer": "2.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.3_1662674541363_0.5805164736874977", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "pure-rand", "version": "5.0.4", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@5.0.4", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "d35261a33db14e4362253e1fefe3ff926a09adc0", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.4.tgz", "fileCount": 55, "integrity": "sha512-By0DXnZoPMng4+f3BvzNPbKcz+zccJjGeyNaqcCH4c7McIo1USVZ59Ed7/cwVFmdhsLkrgHBHqIIJ8zStWnQCQ==", "signatures": [{"sig": "MEUCIDa5mGbKvPv7TqFBcUNqL0jEbMYP69qRxeE9y9EJ05vxAiEAoIjdxcHi6ChTdh1qoptzMravmU79wyr8C5KVZabg4eU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfSnAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6RA//dOp+RLfy+ejJF8fRmSGQ1ypHcfn04xOnwtuln8JfoNJ3LKcR\r\nm5/45rVzr6NqWi6snAu3M2oOpmNzaCW7QE3jeAfUs8jtlnCq0gKG4t7lP7IF\r\ngdwo+yKi+Pwyy81bSW2mt3wOz+oIkvDLZxl8BW9Fgv8CPpmxuG5ZLdTseT5v\r\nmmEHJ3I2rZF8pFVawZahaiVMJbpdjIbNA8dbLLJ9nLZYUZn5ya+nsttmiIBp\r\nNBTIuCwQgbscz3NjSK98VWtq/nLep9OEwy+2Ah6qH62eX2aBodml3IB80EtD\r\n7y0oBafb8Ha9uG+1WByhEJ1v3s4Y2yYTWo+K8hSizQO0UyMFLl/GbB7verla\r\nQJ1prN6j48VLlndbicGsNahixXqHrtpFKQsRJfwgv+rxy2EThQOjIknYDY9M\r\nM4c4FYf1cUTb0BbxPc0YlOi7DjttV1JHJ8XWQd+bRHoVqQNqwa534uVS0JRB\r\nXQld1pkKKECiGBNQDCntNSN1GAAHwGSkSjnToWySxwc0x1a5XBjvBkkzMHGG\r\nwAYdDRZ2c3g0ktba7CFTf4tROQ1r20smFebS4wbBtNn+7jgym5944kAuDa1i\r\nlPbarZWgAI/3AegP5ndwYnQyow+ZjmXp/zGVlSlYlHhhlTuCWZXQ0nDsL2wO\r\nvcvYnP0mL7JTQlPAjqxOFWfRPX2tzrFtOXQ=\r\n=ppA4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": [{"url": "https://opencollective.com/fast-check", "type": "opencollective"}, {"url": "https://github.com/sponsors/dubzzz", "type": "individual"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-By0DXnZoPMng4+f3BvzNPbKcz+zccJjGeyNaqcCH4c7McIo1USVZ59Ed7/cwVFmdhsLkrgHBHqIIJ8zStWnQCQ==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.19.2", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "packageManager": "yarn@3.3.0", "devDependencies": {"jest": "^26.1.0", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.7.1", "cross-env": "^7.0.3", "tinybench": "^2.3.1", "fast-check": "^3.1.1", "typescript": "^4.0.2", "@types/jest": "^27.0.1", "@types/node": "^18.6.3", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.4_1669147071971_0.2311388836045749", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "pure-rand", "version": "5.0.5", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@5.0.5", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "bda2a7f6a1fc0f284d78d78ca5902f26f2ad35cf", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-5.0.5.tgz", "fileCount": 55, "integrity": "sha512-BwQpbqxSCBJVpamI6ydzcKqyFmnd5msMWUGvzXLm1aXvusbbgkbOto/EUPM00hjveJEaJtdbhUjKSzWRhQVkaw==", "signatures": [{"sig": "MEUCIQCGoh5jyo/iR/2Vvxj2jTf57InfVrnqqn0aBiGXlGITHwIgMhOw6jIxcZGLpEDcI/a59InKcheToyMDOHCoexYyWVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfSzjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhQg//Y5fyiWpsrHY9GhUeDV59HprXC/zzG4vJJAAQ0TVz/5WPOEZ8\r\n4745DdgoU+1LHxQuWnV/67fGVt8ru+my6kg1Q3gLA03vV7cxunVQpjRIH6NV\r\nR/U5VtmqvdVBIMGNJYyOgFG+itGAaPRXm5kPdgwcLlXRQeCkmYu0HG5qYQjg\r\nSLxJOaXpnfRjwoXFzS+eSqI1uj5vubQPAOTB+iG9YhYQx7RqcBPN9AyENkl/\r\nGpLP+iDiw9PCXVzHGxseQZWdGSV0B2FONDWG4yNFNEhOTx9E/ZiyR6JuXkid\r\nGT90UgIz3nmiOZRQelezYtCs9Pfes/+wygsZjgPkvdI43rwge4lSxjemd6+t\r\nyV2JR0ADsH8cEzJjoNx5DgHNqfTB/++467M4/5OBeXSBcGJQupU2cVerTnPu\r\nUGbY7Aa5Vaw2Jwq12Usx6dQM3mx3gXtuBK9qEWvAr+TjrWDOWRFvJiHqxjFu\r\n/QzVn1275DYq7TOra1vRAHp8VVS45T2ZSzPxqmxwuUC7e7ofLbIuRVJBdNwn\r\nQ7C8RZyDV6F1sc0xcpMoJXgrtXGq0Llrn7ZpdzrkIVVlXAI69bv6r/SDO1sE\r\nutJ7ae/UenA8sILf7RhNQTHBdVoBq09CPc6//jzOfQFhpe4mD8y8lzR66xXZ\r\nBoD2QfeyTgphXfkJ8JnnTc4T3+x++DfpxNo=\r\n=XrpM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write \"**/*.{js,ts}\"", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different \"**/*.{js,ts}\"", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-BwQpbqxSCBJVpamI6ydzcKqyFmnd5msMWUGvzXLm1aXvusbbgkbOto/EUPM00hjveJEaJtdbhUjKSzWRhQVkaw==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.19.2", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "packageManager": "yarn@3.3.0", "devDependencies": {"jest": "^26.1.0", "ts-jest": "^26.1.2", "ts-node": "^10.0.0", "prettier": "2.7.1", "cross-env": "^7.0.3", "tinybench": "^2.3.1", "fast-check": "^3.1.1", "typescript": "^4.0.2", "@types/jest": "^27.0.1", "@types/node": "^18.6.3", "replace-in-file": "^6.1.0", "source-map-support": "^0.5.16"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_5.0.5_1669147874903_0.27613352689090354", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "pure-rand", "version": "6.0.0", "keywords": ["pure random", "random number generator"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@6.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "701996ceefa253507923a0e864c17ab421c04a7c", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.0.0.tgz", "fileCount": 55, "integrity": "sha512-rLSBxJjP+4DQOgcJAx6RZHT2he2pkhQdSnofG5VWyVl6GRq/K02ISOuOLcsMOrtKDIJb8JN2zm3FFzWNbezdPw==", "signatures": [{"sig": "MEUCIQDFo6feqNEi7R37LjQQzOAOoNCMnZA5Nz0ou3jRlCM3TwIgQOayws/p8Lqj1uuSFc5gKfMEa8RI1dGdaAG+jUhfWrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwIX5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTrA//W0R3yD93vTL10ms9LXnQAkv+xc6BM85qGGJGcbcN7rGnyKA/\r\nUspDGfU8aofxeHfDZo9cHo42fsTF0+VRU2Aj2Ozg55JIBqK6TpW9+ahgn/Xs\r\nvSVeQauLUBYih+FGrgx8IVqoSiI3YF88UgkVC+Kr7qBVJkcXxcH6B+2B7SH3\r\nPGd2a7idIRu6DwWyZ/kQb3Nss9tKBGr8G2/cO5ZbdCHiMswLH8FjROdsWN8j\r\n4ujAIsJNlV18Qo3ZdyEGf9rhefbHRMgIpppFOoe1aFYDNGrFXNmwBb7mDTbW\r\nS8Cv7IJByNLBHCv4goph+FStMS1hB4UeXzizJxdrfoFLpQc0QcM2no1eTm8F\r\nyBoTUx3jPbNqGXl3ksyscIPdypxWn4SYmLNyNiiLArUSEXkpKXELxrflf70W\r\n9P+R5q4zCA/iDqcHC5AWE3j5DvUI6hWYCiCMPd0Qkakx3cA0lekpX3yz9FCw\r\nsHJ8ZGF11u9qvqrUT646pKjt1NE2uNgPxI5aVnozp8Nfa0WR7t4b3ya1DRVP\r\nZduwaekQpfca14xmytK/fjUbSnepaAyDACZrGQ/YjVPhikYHKxNM8zRUzLq1\r\nZiTq1V5taSunu4iy5ijn4MiXy73vMjRrIbGpIYATwr+KMLHEYcQGfgGkWvsg\r\nEbTi3oON50wSAhmf52IRv/AgnpTWufoI8jk=\r\n=orV0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-rLSBxJjP+4DQOgcJAx6RZHT2he2pkhQdSnofG5VWyVl6GRq/K02ISOuOLcsMOrtKDIJb8JN2zm3FFzWNbezdPw==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.19.3", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "yarn@3.3.1", "devDependencies": {"jest": "^26.6.3", "ts-jest": "^26.5.6", "ts-node": "^10.9.1", "prettier": "2.8.2", "cross-env": "^7.0.3", "tinybench": "^2.3.1", "fast-check": "^3.5.0", "typescript": "^4.9.4", "@types/jest": "^27.5.2", "@types/node": "^18.11.18", "replace-in-file": "^6.3.5", "source-map-support": "^0.5.21"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_6.0.0_1673561592939_0.10564394151881884", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "pure-rand", "version": "6.0.1", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@6.0.1", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "31207dddd15d43f299fdcdb2f572df65030c19af", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.0.1.tgz", "fileCount": 56, "integrity": "sha512-t+x1zEHDjBwkDGY5v5ApnZ/utcd4XYDiJsaQQoptTXgUXX95sDg1elCdJghzicm7n2mbCBJ3uYWr6M22SO19rg==", "signatures": [{"sig": "MEQCIEM0Cz/UO3MjJmcyOOpFd42wKZX+lVIp4gYNPxw79uvdAiA0ymRQ8m+NbPYJfqN/gJoYatAwXxRK0ZfgE4pJ20SXZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDj4uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDTg/+OH6MLqYhXUvCTc5ihLMPuHg8nRqQVd9ULZVQEz/EyqhPTcNB\r\n3GaQ16WuZa6nflU7Viuul9HyQPIExdgPF2Zo+i5YHAx/keK425Du6oKzG6PU\r\nF1IsIlrTx82ldoAGjd0dJ5OLHV05uVZdt0rBJFynNW0DV92ZtVkjmp7o4KId\r\nge8NfktJ3q22I1hVZ6vXcPABkHUkNuR4ObWl7Sk+jFoXPwRuTo9YEZNutdze\r\nDg/QYrW7TooULP//c3t/tD7+NZ6EvOJCS2FxlM6g3YKApS9qXCvfTbzjIJoK\r\nNHLQpwKvj+NlqHqfnufbZ5q7QuACrNTzhU61Lwii0a03cQ6mhIfXZTfkJ01x\r\nnF/up3168oU7vgygXpl32mBeqL949yef5sHy/Azl4LeWTxFMpEvqpni0ocy5\r\nzzZ1UNtZ74/gAJDJEGg0VVuuH3vIP9A3kPgKQiFohrq4iI31Y0QN8JF/NNgn\r\nekmBjc5Ryd+rRCwx53TUKcGLu7NH63h2KUcTTMId3hldV45+m7ZXM8UgI5sM\r\nzRQsZWTRMMNYYO5v3pEh0xqgewL5ABqcGPtdevOFzLDnkwWn/qDkjgIAH2Le\r\nUIeWyRsIu3XKbZ1ZrEJYOV7VzH30S8XYQFjlHnXVqnZq/uTinf+UIQP0R1P+\r\n4MkGT/hKcidWpE+RsbWCBKqT0VLiI/MUdZ8=\r\n=cljC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-t+x1zEHDjBwkDGY5v5ApnZ/utcd4XYDiJsaQQoptTXgUXX95sDg1elCdJghzicm7n2mbCBJ3uYWr6M22SO19rg==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "8.19.3", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "16.19.1", "_hasShrinkwrap": false, "packageManager": "yarn@3.4.1", "devDependencies": {"jest": "^29.5.0", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "prettier": "2.8.4", "cross-env": "^7.0.3", "tinybench": "^2.4.0", "fast-check": "^3.7.0", "typescript": "^4.9.5", "@types/jest": "^29.4.0", "@types/node": "^18.15.0", "replace-in-file": "^6.3.5", "source-map-support": "^0.5.21"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_6.0.1_1678655022723_0.5406368739616207", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "pure-rand", "version": "6.0.2", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@6.0.2", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "a9c2ddcae9b68d736a8163036f088a2781c8b306", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.0.2.tgz", "fileCount": 56, "integrity": "sha512-6Yg0ekpKICSjPswYOuC5sku/TSWaRYlA0qsXqJgM/d/4pLPHPuTxK7Nbf7jFKzAeedUhR8C7K9Uv63FBsSo8xQ==", "signatures": [{"sig": "MEUCICfTnDD/a/pzKT+3kedsUFRVMXVq6voOtzm8b2xJkfCvAiEAyktJj4VtggG7Jnzke2e+fxLAEHrBIjeruasMvSaPWDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/pure-rand@6.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 70721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR3x5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkPQ/+LY8r9/EhdA6gaBz9+/TGivIQa34B98ZEVhDzfQvkWVS58bK/\r\nXlK7Z+8YxgWZZ2oG1rnnY2IWlKEErJVYWqXHRZPX+RPtYO7nhW9eILvq0YUY\r\n9fwYqtCukVu0Xk2tgb1boi3y+Raw0aT4eZv1+r+C2w7uiae93jsZnneE4rEX\r\nHIwf1NuXMYvBbf3ze2KaAUJUT6y7cKhGulcPy93RShqMRlTd9pIDapGd4JlR\r\n8XXcuq2RPFpIh3dmSINujHtgi0pgPNHdq/aHd8/XlNUUeotNVzvh7odlTMIj\r\naCXeypVa6Ime2oZh/ljICBkeMPC/EvoDWZPASfh24C5yPyAgSw+PevqdRwRr\r\nfCsZrI8tksLJD1lfkTTLuwqvBnPm2vX99tksnVAQ3rtoksVhrGQ+7gL2zQKb\r\n3pOS8AyNAI4pGt5VNAZ5tfZUajBKApegddDXPVQfyJDWDSFkP3xX1Bo8T+g8\r\nQXqz+DFxrmsXq00a/08KJIF6KrwzOdxZJ6X+QaTUhY35p+lF1nQKR8A9nC1N\r\n6o7HLvc7Mk9TtlkN8IuSPH5RtcCPtKnniLmK7ZvZXOkrl1WujtKUsnGkYqC5\r\nvN2iLfLO7YDLCAaKSKczEHEs3c3UuXlW49DiNdhUnUVv4RwNZj3a7QiScBLh\r\nTnifW86bfq4jr3awWwC93zGYoB5/wnqvZks=\r\n=vs9w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"types": "./lib/types/pure-rand.d.ts", "import": "./lib/esm/pure-rand.js", "default": "./lib/esm/pure-rand.js", "require": "./lib/pure-rand.js"}}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-6Yg0ekpKICSjPswYOuC5sku/TSWaRYlA0qsXqJgM/d/4pLPHPuTxK7Nbf7jFKzAeedUhR8C7K9Uv63FBsSo8xQ==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "9.6.5", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "yarn@3.5.0", "devDependencies": {"jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "prettier": "2.8.8", "cross-env": "^7.0.3", "tinybench": "^2.4.0", "fast-check": "^3.8.1", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^18.16.0", "replace-in-file": "^6.3.5", "source-map-support": "^0.5.21"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_6.0.2_1682406521214_0.17925433292128767", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "pure-rand", "version": "6.0.3", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@6.0.3", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "3c9e6b53c09e52ac3cedffc85ab7c1c7094b38cb", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.0.3.tgz", "fileCount": 73, "integrity": "sha512-KddyFewCsO0j3+np81IQ+SweXLDnDQTs5s67BOnrYmYe/yNmUhttQyGsYzy8yUnoljGAQ9sl38YB4vH8ur7Y+w==", "signatures": [{"sig": "MEUCIDzn3F1mjMkCLppkxL5hHLR25vH15lpK+gt2mXoZQB+6AiEA42nRptgD6fxNr5E3QOYCPCXpuYkygYiRTQkNIxH02/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/pure-rand@6.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76568}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": {"types": "./lib/esm/types/pure-rand.d.ts", "default": "./lib/esm/pure-rand.js"}, "require": {"types": "./lib/types/pure-rand.d.ts", "default": "./lib/pure-rand.js"}}, "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && tsc -p ./tsconfig.declaration.json --outDir lib/esm/types && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-KddyFewCsO0j3+np81IQ+SweXLDnDQTs5s67BOnrYmYe/yNmUhttQyGsYzy8yUnoljGAQ9sl38YB4vH8ur7Y+w==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "10.0.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "packageManager": "yarn@3.6.3", "devDependencies": {"jest": "^29.6.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "prettier": "2.8.8", "cross-env": "^7.0.3", "tinybench": "^2.5.0", "fast-check": "^3.12.1", "typescript": "^5.2.2", "@types/jest": "^29.5.4", "@types/node": "^18.17.12", "replace-in-file": "^7.0.1", "source-map-support": "^0.5.21"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_6.0.3_1693858864898_0.28338469367479036", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "pure-rand", "version": "6.0.4", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@6.0.4", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "50b737f6a925468679bff00ad20eade53f37d5c7", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.0.4.tgz", "fileCount": 73, "integrity": "sha512-LA0Y9kxMYv47GIPJy6MI84fqTd2HmYZI83W/kM/SkKfDlajnZYfmXFTxkbY+xSBPkLJxltMa9hIkmdc29eguMA==", "signatures": [{"sig": "MEYCIQDKKqM9LYtH/aAIVg9YUQmkY/0u6yai4qbkmn5tQxNXwAIhAObbzusWORZg9tABxTh6V0t9S7ZrNMhMjjvUEm8B1Aiv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/pure-rand@6.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76896}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": {"types": "./lib/esm/types/pure-rand.d.ts", "default": "./lib/esm/pure-rand.js"}, "require": {"types": "./lib/types/pure-rand.d.ts", "default": "./lib/pure-rand.js"}}, "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && tsc -p ./tsconfig.declaration.json --outDir lib/esm/types && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-LA0Y9kxMYv47GIPJy6MI84fqTd2HmYZI83W/kM/SkKfDlajnZYfmXFTxkbY+xSBPkLJxltMa9hIkmdc29eguMA==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "10.1.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "packageManager": "yarn@3.6.3", "devDependencies": {"jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "prettier": "2.8.8", "cross-env": "^7.0.3", "tinybench": "^2.5.1", "fast-check": "^3.13.0", "typescript": "^5.2.2", "@types/jest": "^29.5.5", "@types/node": "^18.17.17", "replace-in-file": "^7.0.1", "source-map-support": "^0.5.21"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_6.0.4_1695834461940_0.38258491570484443", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "pure-rand", "version": "6.1.0", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@6.1.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "d173cf23258231976ccbdb05247c9787957604f2", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz", "fileCount": 73, "integrity": "sha512-b<PERSON><PERSON>awvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==", "signatures": [{"sig": "MEUCIQCmSE/f67vERzU/3ZpN5Fn0a+89r7vDibV4z8JreusgWQIgKBqcFKK3fNEwun9rA0XKspwwGJP2P3Y+CUSLb4orb0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/pure-rand@6.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84036}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": {"types": "./lib/esm/types/pure-rand.d.ts", "default": "./lib/esm/pure-rand.js"}, "require": {"types": "./lib/types/pure-rand.d.ts", "default": "./lib/pure-rand.js"}}, "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && tsc -p ./tsconfig.declaration.json --outDir lib/esm/types && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-b<PERSON><PERSON>awvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "10.5.0", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "packageManager": "yarn@4.1.1", "devDependencies": {"jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "prettier": "3.2.5", "cross-env": "^7.0.3", "tinybench": "^2.6.0", "fast-check": "^3.16.0", "typescript": "^5.4.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "replace-in-file": "^7.1.0", "source-map-support": "^0.5.21"}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_6.1.0_1710970189006_0.4839162102152734", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "pure-rand", "version": "7.0.0", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pure-rand@7.0.0", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/dubzzz/pure-rand#readme", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "dist": {"shasum": "41010af0a81cef6aa813aea403f622c4ecdf584b", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-7.0.0.tgz", "fileCount": 93, "integrity": "sha512-P+yQSnu0T0M3g9Wlwq9ZBA3iOTQ/i4H/RnpSc9VvM1nCnQTs1lQpK/kAU5kHJoWn5Gv7310+mV63hwwWy+UUqg==", "signatures": [{"sig": "MEYCIQDtsRkTlWrvau5Ixd7BoRekcsvQ2ByM26+vkm3hCj4kFwIhAMcenFom6wwPttJ+z0N2l2OZECoieUA1ZCynWnWJ/Ucb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/pure-rand@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85036}, "main": "lib/pure-rand.js", "type": "commonjs", "_from": "file:package.tgz", "types": "lib/types/pure-rand.d.ts", "module": "lib/esm/pure-rand.js", "exports": {".": {"import": {"types": "./lib/esm/types/pure-rand.d.ts", "default": "./lib/esm/pure-rand.js"}, "require": {"types": "./lib/types/pure-rand.d.ts", "default": "./lib/pure-rand.js"}}, "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/dubzzz", "type": "individual"}, {"url": "https://opencollective.com/fast-check", "type": "opencollective"}], "scripts": {"test": "jest --config jest.config.js --coverage", "bench": "node perf/benchmark.cjs", "build": "tsc && tsc -p ./tsconfig.declaration.json", "format": "prettier --write .", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && tsc -p ./tsconfig.declaration.json --outDir lib/esm/types && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.cjs", "format:check": "prettier --list-different .", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "build:bench:new": "tsc --outDir lib-test/", "build:bench:old": "tsc --outDir lib-reference/"}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "./types/*": {"import": {"types": "./lib/esm/types/types/*.d.ts", "default": "./lib/esm/types/*.js"}, "require": {"types": "./lib/types/types/*.d.ts", "default": "./lib/types/*.js"}}, "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_integrity": "sha512-P+yQSnu0T0M3g9Wlwq9ZBA3iOTQ/i4H/RnpSc9VvM1nCnQTs1lQpK/kAU5kHJoWn5Gv7310+mV63hwwWy+UUqg==", "repository": {"url": "git+https://github.com/dubzzz/pure-rand.git", "type": "git"}, "_npmVersion": "10.9.2", "description": " Pure random number generator written in TypeScript", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.6", "./generator/*": {"import": {"types": "./lib/esm/types/generator/*.d.ts", "default": "./lib/esm/generator/*.js"}, "require": {"types": "./lib/types/generator/*.d.ts", "default": "./lib/generator/*.js"}}, "_hasShrinkwrap": false, "packageManager": "yarn@4.5.3", "devDependencies": {"jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "prettier": "3.4.1", "cross-env": "^7.0.3", "tinybench": "^2.9.0", "fast-check": "^3.23.1", "typescript": "^5.5.3", "@types/jest": "^29.5.14", "@types/node": "^22.10.1", "replace-in-file": "^7.2.0", "source-map-support": "^0.5.21"}, "./distribution/*": {"import": {"types": "./lib/esm/types/distribution/*.d.ts", "default": "./lib/esm/distribution/*.js"}, "require": {"types": "./lib/types/distribution/*.d.ts", "default": "./lib/distribution/*.js"}}, "_npmOperationalInternal": {"tmp": "tmp/pure-rand_7.0.0_1738883877576_0.5841412840399591", "host": "s3://npm-registry-packages-npm-production"}}, "7.0.1": {"name": "pure-rand", "version": "7.0.1", "description": " Pure random number generator written in TypeScript", "type": "commonjs", "main": "lib/pure-rand.js", "exports": {"./package.json": "./package.json", "./distribution/*": {"require": {"types": "./lib/types/distribution/*.d.ts", "default": "./lib/distribution/*.js"}, "import": {"types": "./lib/esm/types/distribution/*.d.ts", "default": "./lib/esm/distribution/*.js"}}, "./generator/*": {"require": {"types": "./lib/types/generator/*.d.ts", "default": "./lib/generator/*.js"}, "import": {"types": "./lib/esm/types/generator/*.d.ts", "default": "./lib/esm/generator/*.js"}}, "./types/*": {"require": {"types": "./lib/types/types/*.d.ts", "default": "./lib/types/*.js"}, "import": {"types": "./lib/esm/types/types/*.d.ts", "default": "./lib/esm/types/*.js"}}, ".": {"require": {"types": "./lib/types/pure-rand.d.ts", "default": "./lib/pure-rand.js"}, "import": {"types": "./lib/esm/types/pure-rand.d.ts", "default": "./lib/esm/pure-rand.js"}}}, "module": "lib/esm/pure-rand.js", "types": "lib/types/pure-rand.d.ts", "sideEffects": false, "packageManager": "yarn@4.6.0", "scripts": {"format:check": "prettier --list-different .", "format": "prettier --write .", "build": "tsc && tsc -p ./tsconfig.declaration.json", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && tsc -p ./tsconfig.declaration.json --outDir lib/esm/types && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.mjs", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "test": "jest --config jest.config.js --coverage", "build:bench:old": "tsc --outDir lib-reference/", "build:bench:new": "tsc --outDir lib-test/", "bench": "node perf/benchmark.cjs"}, "repository": {"type": "git", "url": "git+https://github.com/dubzzz/pure-rand.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "homepage": "https://github.com/dubzzz/pure-rand#readme", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.13.1", "cross-env": "^7.0.3", "fast-check": "^3.23.2", "jest": "^29.7.0", "prettier": "3.4.2", "replace-in-file": "^8.3.0", "source-map-support": "^0.5.21", "tinybench": "^3.1.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}], "_id": "pure-rand@7.0.1", "_integrity": "sha512-oTUZM/NAZS8p7ANR3SHh30kXB+zK2r2BPcEn/awJIbOvq82WoMN4p62AWWp3Hhw50G0xMsw1mhIBLqHw64EcNQ==", "_resolved": "/home/<USER>/work/pure-rand/pure-rand/package.tgz", "_from": "file:package.tgz", "_nodeVersion": "18.20.6", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-oTUZM/NAZS8p7ANR3SHh30kXB+zK2r2BPcEn/awJIbOvq82WoMN4p62AWWp3Hhw50G0xMsw1mhIBLqHw64EcNQ==", "shasum": "6f53a5a9e3e4a47445822af96821ca509ed37566", "tarball": "https://registry.npmjs.org/pure-rand/-/pure-rand-7.0.1.tgz", "fileCount": 93, "unpackedSize": 85234, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/pure-rand@7.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF5YsIuSQvN9VyoHGhFiVTuK4x9f28yKCr9hSrTMdodxAiEAvUVSKkjFwMUGUU86nLVZrJaaR5eZWmjhAIXIkZgApJA="}]}, "_npmUser": {"name": "ndu<PERSON>n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/pure-rand_7.0.1_1738890622241_0.8118779360355459"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-01T21:13:57.964Z", "modified": "2025-02-07T01:10:22.949Z", "0.0.0": "2018-03-01T21:13:58.048Z", "1.0.1": "2018-03-06T19:09:15.490Z", "1.0.2": "2018-03-06T19:34:45.932Z", "1.1.0": "2018-05-21T20:45:26.760Z", "1.1.1": "2018-05-30T18:11:22.603Z", "1.2.0": "2018-08-17T00:19:37.102Z", "1.3.0": "2018-09-29T23:37:15.209Z", "1.3.1": "2018-09-29T23:57:18.562Z", "1.3.2": "2018-09-30T11:08:59.060Z", "1.4.0": "2018-10-05T23:17:34.598Z", "1.4.1": "2018-10-06T10:19:25.259Z", "1.4.2": "2018-10-07T21:25:45.631Z", "1.5.0": "2018-10-12T22:23:15.547Z", "1.6.0": "2018-12-16T02:31:25.552Z", "1.6.2": "2019-01-03T20:32:31.527Z", "1.7.0": "2019-10-21T21:00:42.776Z", "2.0.0": "2020-01-23T22:11:25.347Z", "3.0.0-alpha.*******": "2020-04-04T21:56:26.370Z", "3.0.0-alpha.*******": "2020-04-04T22:32:51.569Z", "3.0.0-alpha.*******": "2020-04-05T10:11:05.249Z", "3.0.0": "2020-07-10T06:56:29.778Z", "3.1.0": "2020-08-10T19:52:36.646Z", "4.0.0": "2020-11-05T21:05:02.159Z", "4.1.0": "2020-11-22T18:34:04.324Z", "4.1.1": "2020-11-29T17:26:13.606Z", "4.1.2": "2021-01-25T19:07:08.030Z", "5.0.0-experimental.*******": "2021-06-03T19:39:11.633Z", "5.0.0-experimental.*******": "2021-06-05T18:32:37.642Z", "4.2.0": "2021-06-05T18:48:56.216Z", "4.2.1": "2021-06-07T17:54:35.043Z", "5.0.0-experimental.*******": "2021-06-07T22:54:16.502Z", "5.0.0": "2021-06-11T07:16:39.373Z", "5.0.1": "2022-03-17T19:14:12.491Z", "5.0.2": "2022-09-08T21:51:30.017Z", "5.0.3": "2022-09-08T22:02:21.567Z", "5.0.4": "2022-11-22T19:57:52.131Z", "5.0.5": "2022-11-22T20:11:15.149Z", "6.0.0": "2023-01-12T22:13:13.142Z", "6.0.1": "2023-03-12T21:03:42.902Z", "6.0.2": "2023-04-25T07:08:41.392Z", "6.0.3": "2023-09-04T20:21:05.060Z", "6.0.4": "2023-09-27T17:07:42.224Z", "6.1.0": "2024-03-20T21:29:49.243Z", "7.0.0": "2025-02-06T23:17:57.798Z", "7.0.1": "2025-02-07T01:10:22.493Z"}, "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/dubzzz/pure-rand#readme", "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "repository": {"type": "git", "url": "git+https://github.com/dubzzz/pure-rand.git"}, "description": " Pure random number generator written in TypeScript", "maintainers": [{"name": "ndu<PERSON>n", "email": "<EMAIL>"}], "readme": "<h1>\n  <img src=\"https://raw.githubusercontent.com/dubzzz/pure-rand/main/assets/logo.svg\" alt=\"pure-rand logo\" />\n</h1>\n\nFast Pseudorandom number generators (aka PRNG) with purity in mind!\n\n[![Build Status](https://github.com/dubzzz/pure-rand/workflows/Build%20Status/badge.svg?branch=main)](https://github.com/dubzzz/pure-rand/actions)\n[![NPM Version](https://badge.fury.io/js/pure-rand.svg)](https://badge.fury.io/js/pure-rand)\n[![Monthly Downloads](https://img.shields.io/npm/dm/pure-rand)](https://www.npmjs.com/package/pure-rand)\n\n[![Codecov](https://codecov.io/gh/dubzzz/pure-rand/branch/main/graph/badge.svg)](https://codecov.io/gh/dubzzz/pure-rand)\n[![Package Quality](https://packagequality.com/shield/pure-rand.svg)](https://packagequality.com/#?package=pure-rand)\n[![Snyk Package Quality](https://snyk.io/advisor/npm-package/pure-rand/badge.svg)](https://snyk.io/advisor/npm-package/pure-rand)\n[![Tested with fast-check](https://img.shields.io/badge/tested%20with-fast%E2%80%91check%20%F0%9F%90%92-%23282ea9?style=flat&logoSize=auto&labelColor=%231b1b1d)](https://fast-check.dev/)\n\n[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/dubzzz/pure-rand/labels/good%20first%20issue)\n[![License](https://img.shields.io/npm/l/pure-rand.svg)](https://github.com/dubzzz/pure-rand/blob/main/LICENSE)\n[![Twitter](https://img.shields.io/twitter/url/https/github.com/dubzzz/pure-rand.svg?style=social)](https://twitter.com/intent/tweet?text=Check%20out%20pure-rand%20by%20%40ndubien%20https%3A%2F%2Fgithub.com%2Fdubzzz%2Fpure-rand%20%F0%9F%91%8D)\n\n## Getting started\n\n**Install it in node via:**\n\n`npm install pure-rand` or `yarn add pure-rand`\n\n**Use it in browser by doing:**\n\n`import * as prand from 'https://unpkg.com/pure-rand/lib/esm/pure-rand.js';`\n\n## Usage\n\n**Simple usage**\n\n```javascript\nimport prand from 'pure-rand';\n\nconst seed = 42;\nconst rng = prand.xoroshiro128plus(seed);\nconst firstDiceValue = prand.unsafeUniformIntDistribution(1, 6, rng); // value in {1..6}, here: 2\nconst secondDiceValue = prand.unsafeUniformIntDistribution(1, 6, rng); // value in {1..6}, here: 4\nconst thirdDiceValue = prand.unsafeUniformIntDistribution(1, 6, rng); // value in {1..6}, here: 6\n```\n\n**Pure usage**\n\nPure means that the instance `rng` will never be altered in-place. It can be called again and again and it will always return the same value. But it will also return the next `rng`. Here is an example showing how the code above can be translated into its pure version:\n\n```javascript\nimport prand from 'pure-rand';\n\nconst seed = 42;\nconst rng1 = prand.xoroshiro128plus(seed);\nconst [firstDiceValue, rng2] = prand.uniformIntDistribution(1, 6, rng1); // value in {1..6}, here: 2\nconst [secondDiceValue, rng3] = prand.uniformIntDistribution(1, 6, rng2); // value in {1..6}, here: 4\nconst [thirdDiceValue, rng4] = prand.uniformIntDistribution(1, 6, rng3); // value in {1..6}, here: 6\n\n// You can call: prand.uniformIntDistribution(1, 6, rng1);\n// over and over it will always give you back the same value along with a new rng (always producing the same values too).\n```\n\n**Independent simulations**\n\nIn order to produce independent simulations it can be tempting to instanciate several PRNG based on totally different seeds. While it would produce distinct set of values, the best way to ensure fully unrelated sequences is rather to use jumps. Jump just consists into moving far away from the current position in the generator (eg.: jumping in Xoroshiro 128+ will move you 2<sup>64</sup> generations away from the current one on a generator having a sequence of 2<sup>128</sup> elements).\n\n```javascript\nimport prand from 'pure-rand';\n\nconst seed = 42;\nconst rngSimulation1 = prand.xoroshiro128plus(seed);\nconst rngSimulation2 = rngSimulation1.jump(); // not in-place, creates a new instance\nconst rngSimulation3 = rngSimulation2.jump(); // not in-place, creates a new instance\n\nconst diceSim1Value = prand.unsafeUniformIntDistribution(1, 6, rngSimulation1); // value in {1..6}, here: 2\nconst diceSim2Value = prand.unsafeUniformIntDistribution(1, 6, rngSimulation2); // value in {1..6}, here: 5\nconst diceSim3Value = prand.unsafeUniformIntDistribution(1, 6, rngSimulation3); // value in {1..6}, here: 6\n```\n\n**Non-uniform usage**\n\nWhile not recommended as non-uniform distribution implies that one or several values from the range will be more likely than others, it might be tempting for people wanting to maximize the throughput.\n\n```javascript\nimport prand from 'pure-rand';\n\nconst seed = 42;\nconst rng = prand.xoroshiro128plus(seed);\nconst rand = (min, max) => {\n  const out = (rng.unsafeNext() >>> 0) / 0x100000000;\n  return min + Math.floor(out * (max - min + 1));\n};\nconst firstDiceValue = rand(1, 6); // value in {1..6}, here: 6\n```\n\n**Select your seed**\n\nWhile not perfect, here is a rather simple way to generate a seed for your PNRG.\n\n```javascript\nconst seed = Date.now() ^ (Math.random() * 0x100000000);\n```\n\n## Documentation\n\n### Pseudorandom number generators\n\nIn computer science most random number generators<sup>(1)</sup> are [pseudorandom number generators](https://en.wikipedia.org/wiki/Pseudorandom_number_generator) (abbreviated: PRNG). In other words, they are fully deterministic and given the original seed one can rebuild the whole sequence.\n\nEach PRNG algorithm has to deal with tradeoffs in terms of randomness quality, speed, length of the sequence<sup>(2)</sup>... In other words, it's important to compare relative speed of libraries with that in mind. Indeed, a Mersenne Twister PRNG will not have the same strenghts and weaknesses as a Xoroshiro PRNG, so depending on what you need exactly you might prefer one PRNG over another even if it will be slower.\n\n4 PRNGs come with pure-rand:\n\n- `congruential32`: Linear Congruential generator — \\[[more](https://en.wikipedia.org/wiki/Linear_congruential_generator)\\]\n- `mersenne`: Mersenne Twister generator — \\[[more](https://en.wikipedia.org/wiki/Mersenne_Twister)\\]\n- `xorshift128plus`: Xorshift 128+ generator — \\[[more](https://en.wikipedia.org/wiki/Xorshift)\\]\n- `xoroshiro128plus`: Xoroshiro 128+ generator — \\[[more](https://en.wikipedia.org/wiki/Xorshift)\\]\n\nOur recommendation is `xoroshiro128plus`. But if you want to use another one, you can replace it by any other PRNG provided by pure-rand in the examples above.\n\n### Distributions\n\nOnce you are able to generate random values, next step is to scale them into the range you want. Indeed, you probably don't want a floating point value between 0 (included) and 1 (excluded) but rather an integer value between 1 and 6 if you emulate a dice or any other range based on your needs.\n\nAt this point, simple way would be to do `min + floor(random() * (max - min + 1))` but actually it will not generate the values with equal probabilities even if you use the best PRNG in the world to back `random()`. In order to have equal probabilities you need to rely on uniform distributions<sup>(3)</sup> which comes built-in in some PNRG libraries.\n\npure-rand provides 3 built-in functions for uniform distributions of values:\n\n- `uniformIntDistribution(min, max, rng)`\n- `uniformBigIntDistribution(min, max, rng)` - with `min` and `max` being `bigint`\n- `uniformArrayIntDistribution(min, max, rng)` - with `min` and `max` being instances of `ArrayInt = {sign, data}` ie. sign either 1 or -1 and data an array of numbers between 0 (included) and 0xffffffff (included)\n\nAnd their unsafe equivalents to change the PRNG in-place.\n\n### Extra helpers\n\nSome helpers are also provided in order to ease the use of `RandomGenerator` instances:\n\n- `prand.generateN(rng: RandomGenerator, num: number): [number[], RandomGenerator]`: generates `num` random values using `rng` and return the next `RandomGenerator`\n- `prand.skipN(rng: RandomGenerator, num: number): RandomGenerator`: skips `num` random values and return the next `RandomGenerator`\n\n## Comparison\n\n### Summary\n\nThe chart has been split into three sections:\n\n- section 1: native `Math.random()`\n- section 2: without uniform distribution of values\n- section 3: with uniform distribution of values (not supported by all libraries)\n\n<img src=\"https://raw.githubusercontent.com/dubzzz/pure-rand/main/perf/comparison.svg\" alt=\"Comparison against other libraries\" />\n\n### Process\n\nIn order to compare the performance of the libraries, we aked them to shuffle an array containing 1,000,000 items (see [code](https://github.com/dubzzz/pure-rand/blob/556ec331c68091c5d56e9da1266112e8ea222b2e/perf/compare.cjs)).\n\nWe then split the measurements into two sections:\n\n- one for non-uniform distributions — _known to be slower as it implies re-asking for other values to the PRNG until the produced value fall into the acceptable range of values_\n- one for uniform distributions\n\nThe recommended setup for pure-rand is to rely on our Xoroshiro128+. It provides a long enough sequence of random values, has built-in support for jump, is really efficient while providing a very good quality of randomness.\n\n### Performance\n\n**Non-Uniform**\n\n| Library                  | Algorithm         | Mean time (ms) | Compared to pure-rand |\n| ------------------------ | ----------------- | -------------- | --------------------- |\n| native \\(node 16.19.1\\)  | Xorshift128+      | 33.3           | 1.4x slower           |\n| **pure-rand _@6.0.0_**   | **Xoroshiro128+** | **24.5**       | **reference**         |\n| pure-rand _@6.0.0_       | Xorshift128+      | 25.0           | similar               |\n| pure-rand _@6.0.0_       | Mersenne Twister  | 30.8           | 1.3x slower           |\n| pure-rand _@6.0.0_       | Congruential‍     | 22.6           | 1.1x faster           |\n| seedrandom _@3.0.5_      | Alea              | 28.1           | 1.1x slower           |\n| seedrandom _@3.0.5_      | Xorshift128       | 28.8           | 1.2x slower           |\n| seedrandom _@3.0.5_      | Tyche-i           | 28.6           | 1.2x slower           |\n| seedrandom _@3.0.5_      | Xorwow            | 32.0           | 1.3x slower           |\n| seedrandom _@3.0.5_      | Xor4096           | 32.2           | 1.3x slower           |\n| seedrandom _@3.0.5_      | Xorshift7         | 33.5           | 1.4x slower           |\n| @faker-js/faker _@7.6.0_ | Mersenne Twister  | 109.1          | 4.5x slower           |\n| chance _@1.1.10_         | Mersenne Twister  | 142.9          | 5.8x slower           |\n\n**Uniform**\n\n| Library                | Algorithm         | Mean time (ms) | Compared to pure-rand |\n| ---------------------- | ----------------- | -------------- | --------------------- |\n| **pure-rand _@6.0.0_** | **Xoroshiro128+** | **53.5**       | **reference**         |\n| pure-rand _@6.0.0_     | Xorshift128+      | 52.2           | similar               |\n| pure-rand _@6.0.0_     | Mersenne Twister  | 61.6           | 1.2x slower           |\n| pure-rand _@6.0.0_     | Congruential‍     | 57.6           | 1.1x slower           |\n| random-js @2.1.0       | Mersenne Twister  | 119.6          | 2.2x slower           |\n\n> System details:\n>\n> - OS: Linux 5.15 Ubuntu 22.04.2 LTS 22.04.2 LTS (Jammy Jellyfish)\n> - CPU: (2) x64 Intel(R) Xeon(R) Platinum 8272CL CPU @ 2.60GHz\n> - Memory: 5.88 GB / 6.78 GB\n> - Container: Yes\n> - Node: 16.19.1 - /opt/hostedtoolcache/node/16.19.1/x64/bin/node\n>\n> _Executed on default runners provided by GitHub Actions_\n\n---\n\n(1) — Not all as there are also [hardware-based random number generator](https://en.wikipedia.org/wiki/Hardware_random_number_generator).\n\n(2) — How long it takes to reapeat itself?\n\n(3) — While most users don't really think of it, uniform distribution is key! Without it entries might be biased towards some values and make some others less probable. The naive `rand() % numValues` is a good example of biased version as if `rand()` is uniform in `0, 1, 2` and `numValues` is `2`, the probabilities are: `P(0) = 67%`, `P(1) = 33%` causing `1` to be less probable than `0`\n\n## Advanced patterns\n\n### Generate 32-bit floating point numbers\n\nThe following snippet is responsible for generating 32-bit floating point numbers that spread uniformly between 0 (included) and 1 (excluded).\n\n```js\nimport prand from 'pure-rand';\n\nfunction generateFloat32(rng) {\n  const g1 = prand.unsafeUniformIntDistribution(0, (1 << 24) - 1, rng);\n  const value = g1 / (1 << 24);\n  return value;\n}\n\nconst seed = 42;\nconst rng = prand.xoroshiro128plus(seed);\n\nconst float32Bits1 = generateFloat32(rng);\nconst float32Bits2 = generateFloat32(rng);\n```\n\n### Generate 64-bit floating point numbers\n\nThe following snippet is responsible for generating 64-bit floating point numbers that spread uniformly between 0 (included) and 1 (excluded).\n\n```js\nimport prand from 'pure-rand';\n\nfunction generateFloat64(rng) {\n  const g1 = prand.unsafeUniformIntDistribution(0, (1 << 26) - 1, rng);\n  const g2 = prand.unsafeUniformIntDistribution(0, (1 << 27) - 1, rng);\n  const value = (g1 * Math.pow(2, 27) + g2) * Math.pow(2, -53);\n  return value;\n}\n\nconst seed = 42;\nconst rng = prand.xoroshiro128plus(seed);\n\nconst float64Bits1 = generateFloat64(rng);\nconst float64Bits2 = generateFloat64(rng);\n```\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}