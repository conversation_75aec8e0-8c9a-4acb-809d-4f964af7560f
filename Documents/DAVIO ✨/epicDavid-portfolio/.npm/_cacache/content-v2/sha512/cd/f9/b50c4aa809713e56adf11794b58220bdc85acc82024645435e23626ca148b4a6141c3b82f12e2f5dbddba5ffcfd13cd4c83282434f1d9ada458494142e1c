{"_id": "@esbuild/linux-mips64el", "_rev": "95-85f6dddee521663fbd123638de82e252", "name": "@esbuild/linux-mips64el", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/linux-mips64el", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "026c0d0c2f58924019785944e3d8aa2fba39f106", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-RBgufiEfqAl8dnYgqpblPrH14NUCFsCW1/zEtbR3hZx671CNNQ+MPIBjh/vf2qnzXoMRtCmuS25yAWGXXr1o+w==", "signatures": [{"sig": "MEQCIGIMOdRlCUA3PhAUak8Assz8XtYWT0xK89tHneFXGfARAiBJ/OfUYL3Nsg18SkLm5eGNm753JAh5RqMqDP172q5xCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoNfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqS1Q//ZTKOccoof8MYL3qxpEhHhSb0Kn3TJz9TwwQZ1BI8rd9X4+SV\r\nnGw5R6TiG2FNNrpcPCaVb8MwsxqunXBy8Iq7CIYkzeUea2s6C0j/6K9auAUw\r\n23ZfWOVpWBUpeFquI/fvYvV8wVzp+2JBWjDT0aiY29z31vcNk9+lg5LpXhpt\r\neJzJQOYuP04K0MKq91mj69PEbV6XXgKRmpT0SyYriJfbhFxoe181yC1FB76b\r\n91ixCU3cNYo+wQBmQQX+1nvuqtiLzyts5Oo6hiCGmjzgjrwqEkEnMNih3N33\r\nof7M0zZBhVZPvmuolpaCinapei4mAwbXw0bSQVaSluOOLH4tGf9AWs4WQBXU\r\nEwFgb2MymLio1Fd/d+II1eotXOnVNA1bDcLQ/kOgR0nB0IP2ik8ZJRJ/GLb5\r\n5eRlnnBp3fTHqFHMqOL1hZxXQmRirl2Y6jPrth9vJorBc7jDuSfYsl6XoaSB\r\ndmpmKWAnXiC1twJ6JzVmObEnKB+UCp3zoo1zVp+12VulBzKqIo8PXRn2SiRB\r\nifkNBHddOuq+HCDgc3VpxMbcu5KtGnK8hxr1u573y2v70wkOqZS3aiGkFirr\r\n0SalWxFaHftO47WlEb6vMObvbLBSvw/wLy17PXKWMa+QztajJXCAZ0MeZfL6\r\n50nIQbeK/C2bRq5hIA+08umRvqGhMmnaFDM=\r\n=Ta91\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.15.18_1670284127290_0.21732486118219185", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/linux-mips64el", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "1bfcfd12dfcd34618826ce9dcd3bee5b07716d02", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-LjVf76f20s8Sgoepa2JgnwAvEVsDal30ayxuzmNhLqJT5qXaYGgpw+rgAKPbg1e7QSNaPqutE2xqbB9nwytZXQ==", "signatures": [{"sig": "MEYCIQDMClE2zL29CM+2UQTWaCo9D7xaZ6XOpmljTmgTg9ZVkgIhALYe56qbKhwgKtfu6dHahb4Vd/rA+XrR1caWUq5Usvkl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoc5g/+OrBsOAp1qxpCEu0+Wu1oaoop6WYLJrvawphHhLxIQGzghiPl\r\nJSX8LwV2vO8zLkzcFLQTaFeGuhiqgK6p3ACkFH2HaIWaOy/d2/nmwoBRphyL\r\nko1uzSRfKQL61WZzAHZ7Qr6NHnNiMzrjfLlm1KsPbiVS0ns+mQbNPotekcsE\r\nEQannGCw+9WCPvpLF60I2cjc7+eLp9r55zSbI3OFhorciqKbArEqLa6ZxJky\r\nJrY36F/cK69QyBSXSUgae6C5VKhy9ckSdKNt49hW3KWzVMvfgf0SuryhN/V1\r\n47CNqzkxAiu3HyS/6ou3oOxbtG28COSU+/Hn1WePiLL4QSK1xV1OdSsIcxsV\r\nfe95GS/5E+SwRs+mNqgKziliYM0bGe5tU0AXA91Sfc1KKe0q+IVGxZ0lcOGx\r\nRo6vWizR3RGgmKx7pOGgn5Y23x4fer8eoymvy0j0e1OKooe1kUxEP/8+Q3ew\r\nEwJUOIDSSOJaaIwksDZuFmiFISj6pNxZx4P6T0er3n+eoBxoQB6lpOWp60Ba\r\nYq68F0Laoc72GonfuPhHG+OcPIcftPEu0pfH+gauHabbFq5Ko3nea4kuN72j\r\nWv2AtEecsP6rHA5iKw7UJIy8Um/4A2wQJWzp7S5uuFGHBfqBRWeFnR6sG/1C\r\n5J2XT9JxVsBsGYtqluxwM+9l1viCAOrnYCY=\r\n=r1F3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.0_1670385329825_0.41016367318382985", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/linux-mips64el", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "9b5ecec04f308d7e9951cd11bad607ced1b25ab5", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-TH6aEzbImbo1iUrdhtRdhgynuuiODx+Ju2DaIq+eUIOLj6Hg47NlcM5hQ3bHVKxflPiGIrGi1DTacrEoQOiOTg==", "signatures": [{"sig": "MEYCIQDD+GktXl1kXe3FfN/4zKkHVnMpkfl6WG5Y0e6vz+JLYgIhAIp42wFqbUtC6gMpzCkW5CGa4GE2WUgqj/15U1DDn660", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBs5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9vBAAoICcPbI1og4Zs0cWBJ+69NPBdP58wIv4wBJdXuhmPv6wtur4\r\n8wI/icLlpNEo8G4j11h054pXq48ETQa+1wNXO1ocvjVYTjM0enuGvFcLeoj7\r\npXIZsF6gRpB+Df6p3Q+MaPydjtgKBlgeO/9fmxl/UDFHHv/i9ZlpmAj3sfPV\r\nhcWnUeQzgolekjMmcaYiA0/GC5HjEt61bbNEDpeKCq2dX5QTNDXmtna58QSU\r\nBcWYqRJRwW66s7zxV8iej2g72NLzlFjEN5RZ24sKa8jOtWAuWDEzdcb0Asyd\r\nPuYAKcfoXUiZ2sIFVIFt8umupP9Uvi/SlIc/hF9YJpNqSSLRkA57e3biFhuU\r\n0nSYNmcJ8AshiXwyHXZTIFPytCNgk1HMkln3eduyzueA6xzK2jHQJaeaqZwQ\r\n1Xo1trgdfWmou5upu2n1poD3nBhQrnA2noMonW/VKd5khu6BUwBD+e2YjHBy\r\nMS1hpcIRrA1Ng+/UZ3918+LzoosoH2ItCwsMV7s/zXgjFU81qcse9+pon/Dt\r\nWZBpUB/U7dnLFImyHTd6hpzIZHls8fLoW2mqaUAgiRxhiN+QUK/j3a56gUCB\r\nVrzKNKnkjShxfqsLW4low0Rb9mZFrYwytmEXq/AVuuu++K8edcCpnbrARRBn\r\njEsxK4R/p67pnLJ2E+XtKE7SQCNjCRjUaa0=\r\n=+uMl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.1_1670388537579_0.5792502629696679", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/linux-mips64el", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "5c76d385d68189336b78dfc933a77d13465eee5c", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-YwMpV41qIKRHASV4MaaA/PKk9CoZ4QyVyPXhUtLTO9kPWtWECRI4MTBrGIb9kGUpL6I+jiT4fAZn8YpWSGBkQg==", "signatures": [{"sig": "MEYCIQCHDBvlvpXhwGPuJLS8Nh5bhP/Jvu2+n9ho2GSAjw4HKgIhAIkhSkl2DeOVUvQAfjk0B7MmMWoXN8pE9jMT0Sk3dCkV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYuGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH2hAAo82F1eWhHNyNDQexIQfqToWEOlyDD0I48+tV5ORqm4EPuJCG\r\nhzjrciA9TLsbArTRf+eapLgFUOs6HBwk1o35njps0SD5ynPAKJs28goK08vE\r\nhohAkdoMqXEeuFT7TRoyfPSMfzBOCPd/6bKGaA7hSWJYNjryjG+IUUKp0eCz\r\nebaDgl8r7zFFbPb4IdOlJuYoMlczOYM1lxPBVeiPC2+lNjMn/HV9BOltwZ5r\r\nBNbErHeD7PUhO4Vowa8cusUV4dkL29Z+yaxUDTyYYsCrLPCNy2l9VvhHeqUd\r\n8rssggMKBM2cSCm5a7ts8KIJodc5lFDwJQywIq4vp67HclHItCPv9P3ckMho\r\na7D37Cio9V3tT5mwIEkp9bWdXBmXOcYEc4IvAanr5xAdZ9MopUN5NK1tSiBe\r\nDy5CaeJs4nCOnSu9osVCz48qLobW9Q04EXmBLR3YWmLRDDrla0MstI/O2qc9\r\nQXgoTMz3CiW/fj0FPpK98NPWVUB5Ss5MsFLrIZLSN+WofgEuJzojDWMzhQAd\r\nqJD2ujtZwsNpHkn0gzHPq7T96/k5HghoWmRgaueJdKZJXjq2r3chOH21ffYb\r\ntulaEBTa09eB1FOgnSs3omrmrNQYKLh4H+8KydaCvL70T/MWMErXH6mGpDmy\r\nRQPaD/Uw0MJyWFS2jeHfgWNHdTuhDgq63gc=\r\n=0Nzx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.2_1670482822491_0.012626628966390108", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/linux-mips64el", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "5dd5e118071c3912df69beedbfd11fb117f0fe5e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-znFRzICT/V8VZQMt6rjb21MtAVJv/3dmKRMlohlShrbVXdBuOdDrGb+C2cZGQAR8RFyRe7HS6klmHq103WpmVw==", "signatures": [{"sig": "MEQCIArt7MLb0N12YGs2vRi0/7wLEzudC8mTwIGEcjEmWSGVAiBGXxgVtY2Fdc+5ioYoIla8hagLD7lcLoeBLd1JiLHdDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeKg//W7mRfILe1ib8TambJEXWmwn88Qg5NjmwQ5hm1YSk+FBt37R8\r\nLnvTCFQ/8QbrPp4VxOxNXqQYKVA0fR1InQeHY4Dq2m5xG/IdGPOrdjLzaA4/\r\nu/Ge2v1H7nd8MfnQ4PGixexvtk4dzuuIsC50VIQyOtJHo37r4STKYG2NziQ8\r\nvwXNRYsg8feoUXoutu8+lHwIhSqDBaHyz5YQiKHH2KeZZfu4KG40LxSbR7Vd\r\njxb6+RzqWida85iqM6O4hwtUu9cpyp18MD7Yj9KgXuYoPOs5GCL9SqAAKtGT\r\nA+1QM8IxtGJUL0OeMdP+wLMoKr3j6u2MoxhK/bCy+sfGycup/RZlEfVFrDA2\r\nQozwekU0QfuGTEt6cd46M/80WadRBqcXBtjLUQpK7CSDMsuI+7A/XiHakfw4\r\nXjRouRUpT7ebb3HRLI/OR2l8mlix+FaDBlG+n4tjsHK7UJypNqU3sIkHn2vL\r\nqAzELMq/SsmeMGnoLVux8uSBJpYAV9fN8olUgZsEyGBMBSpChX7wnUThGvf5\r\nfOY8khIWOWquB9NnOy8Ab8Yp5qYa7Q5Q4VT8RlSnlWamwwF4y2Pxzgkxua3F\r\nvDpuCoEfrGyyogs/L+auR38ElciqSAxDWD7yLsnJcdYSxHtdwu9IaTN7kn8K\r\noj5FZWscxRNxANuO9hFjrYmqaR+Sld6FE2w=\r\n=yEoU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.3_1670530418205_0.6565249304688134", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/linux-mips64el", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "9ab77e31cf3be1e35572afff94b51df8149d15bd", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-sD9EEUoGtVhFjjsauWjflZklTNr57KdQ6xfloO4yH1u7vNQlOfAlhEzbyBKfgbJlW7rwXYBdl5/NcZ+Mg2XhQA==", "signatures": [{"sig": "MEQCIDAlTZu1XiRv2ZOhlFlwy3TwYtKWQZhecP62o41h/QKeAiBi50IcEPgnH5kI4HvO4r1rXGntnsu0byr3wNJSq/MVIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAInACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpehhAAn82aWK6YT8tityse3gopLTL4SOq3De52MCPvT/GYctlB5jqL\r\ng7rlqwBNkSv5MNXSELU5q8SsiNpOc+aNJZ3dd/VMFqGHhwt8eaF+G4PLgoZR\r\ndF4l9/TnfQ9IAvJHrBzJhjJzC1aCFot30kkP+rQ0nbT27eflAKHv5jgFWUxI\r\ni2FFS2IURd86uwyg63dU3JrnkownDw8P3wD1pkQLxa3wWOolVKbg3zMK68hE\r\nV06BOtHdoL1mzcpjd0INg+4PJ43WeFrNrXrMqEC2gJSHF3QNYprE4FG+6X5r\r\nT7Nq1sO/4aWBxz5CxFH8lrdd+RIu3CZLED3hQqjv1JVmwNeKGJQMh8GE/Zmi\r\ndda212IRY6oIqy2eifCByQAkVJlkGGON90DFAQunHitcQSpE0HndH+S6jYQs\r\n9lgEhnRQQDxuqmrZVAZKyWs9n82UxjhwWe3FRXca444SuJ9iOwG0XzZw8g6N\r\nIUDvH2W5Z1RaAIzMNr7cl9rmQ0aOlIoqtHLGYga44QqbZLbkYs8LIoZHaNGR\r\nfjCVjxgBdO5T3tCABWOZ+jf0PHy8nRfsIHKGh+FO6jkNN0bjdwkub4KEavRg\r\nUvL8A0e2G6R2h6mgB7wyfiv/KoLJDBsnGBH+v7P/DTtRbUNScmnYdVBMAQ3a\r\noTTk/LV2a0rDsskAokhKPlxv0Tx+ETInit0=\r\n=Zx6L\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.4_1670644262748_0.5079386332776128", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/linux-mips64el", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "ef9ee33e33c13771055f3726a3350cb0a4e1e550", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-0WMhOlwfeeAp6KMx3E6LZKDN6INk4Me8dwIw1XMSFvmE6r31vRnwXkrQlAk5FI44KZ/rIi+yynRZqEd7UJAV2g==", "signatures": [{"sig": "MEUCIQDK+6OzffBf7Ey8/Fafo4SislYYlXa5Hhf9SUfSDB3FOgIgZ7MT/f9NuLh93BOzAL7dfLwrfhFhnCHgr2NlhNwk1xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLrZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprhQ/5AQoxxGO3+1v97nNmGJrGV5UTD4wTjj+7sUFhXRV3ioqRI8Ss\r\nWgM9vxpELBP6gvonGSCSRGN0kL1ZPcmM78fwlXk2w+i1MNQ8cj7kFGOnP4+I\r\no3Akl16ImlAdeFOMuSrsw14A8MrIXPq6pfHwVQ/eM+qBfVii5VrUdzLEreWW\r\nOxdmxKTmarc4emRjMlNqwL0YBCtaVEgQJ9f/urxnuUlVJ6wlgas21RI+G6Zq\r\nEcDWrz4rucooe/2D6boCrMXIdgKOGGnzduCwKhpY5Xb1KjY0yJfq+mEGXObD\r\nAS16JAf9AiR3LUEJm6G1VjtBz+E8vcwHr0ZqigyN+r6YTUp5NsBeE9PRJOuy\r\n85wgz3M3ygtd3JSL6zYODNXNAa31gM5b1piOKGO/+ej1ds0nTXwSb5hXHra7\r\n7y/v1cZVg9o72HFc8lA93dTFJb8AIIbfahJwk8DPlSdP2jGbu32Ppp6RQa6o\r\npNGJRrsMfa0GpTDdsdW9wjC1MtPoXFmAxadnkr8H2+qKh8YwpaxsTeTmeoAP\r\nUAx14Gh/aSCSiEse8BX5TX3+uTHe1f5Oj+AAEJAqBh2Y8nn3JBhRdV0tv1gp\r\nXuRztyQr5hSaWClh3bbD0x89Ik+i4Rn5HfHE1PIDG4ZL3VvqsWMbd8NKTIo7\r\nFvn4F7eUPnWdsYVLfEgE1fVFHZXznFQQxoA=\r\n=Jv5q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.5_1670953688787_0.2746737521327054", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/linux-mips64el", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "cfcb7bcae8fbc91d07c93f3c2e26779ddaa7c738", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-/eR74aTs0dWrg/Y9m0H2iE6rIigkwxsaJlzlSoz6N5JspyARRXutAITveg1wGek4W5LkistZBjEeeyCnC3FT9Q==", "signatures": [{"sig": "MEQCIFtOUwWnyCPvXhbtwGGj4WH91tyTVZrz3OAM63w5fIsRAiAmJVU4xS/pP5rDl9I6Jl9JpQPn9SBn3UNybjllkE1swA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMKhAAk21Gwk5MdGdG1rICkJMOyS6Q2c8VYOCtc0kr+WXgp2eem8Wc\r\n22yM95bZoAVhUbsFl9BUygLt72YGTFlEC4Q2+FQEy45ao/XWUikxip59e9gC\r\nWCWa9zoEhHPxIUPICdUxDoUfrXl3ESOown9NnjJ1Ge53mF3CQ0zFcgYKbXDt\r\nYWspA5P93PpbI+K2PF1k+oXVBQPbHhS6ja73Az7DkLTc1EwINMmlxsB7GVYl\r\nIiYbrpTghjn+f/uaQhZoe/qFoanMEVbGw3T73rlvZ1gZa7AtMN5aeqwLrFsx\r\nTvkFmxQ4c5KNo/AW1EYzW0bbzwpTpND2p2WxTMHC7x+KpakwZKpB5IX+whOY\r\nQULF3hYzv/KQWSzJuyTiZzkpwA9OViFrYpguGKydfVHXmxiMAXUnIfmN8eTx\r\nyo+yJMd8/VSeFDd8OGuebLkd1HfC1/5MMLBe4S5teLog6Xa20CUTYfjDM5/Z\r\ncy92N6AYX8vbVQLzuIsfTSzK5xWNhRIrF/5fptGqU/8oqoC8UzyPLqEWOoB5\r\neaphvmwNoeIW4NDuqBrDNnthm6D4tya/JAlaIMjFKBGw84DOYTyMOuKFx+Sp\r\n8Mxa7HykElczZh0fQ8xtuV9+ofowYf3X0VhcMS2rydJw8nYhruZ6M+yxAWBF\r\nMT7wppgrwPD81kCDku7WNGcygnHIUeaEtQ4=\r\n=vzdA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.6_1670995424101_0.16160573765668285", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/linux-mips64el", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "7c1c8f3de254b4e975ac2580bba187b87b959256", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-hxzlXtWF6yWfkE/SMTscNiVqLOAn7fOuIF3q/kiZaXxftz1DhZW/HpnTmTTWrzrS7zJWQxHHT4QSxyAj33COmA==", "signatures": [{"sig": "MEUCIQCWqTfylygmXDO+6qVUVg1IqryboXEeQfHC8O4HsGZTmAIgB9jQ+Lb0XjJMsH4EirxcsSbE34rPuSuzCKiLLLIPPrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJ8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotMg/9F+OZTgnpftEwKdhvA6k3mc89cGPXG0ADCPOPOGMExJTAhbdy\r\nBOcoMB26+ihHwGeQhYEbW7pm9ZJIle8ROMDpRCFx5/Y8+kF9gyp5ZAM0ahde\r\n3uBL96jlNXs3vQgGzCcONoS4vaH2aeJLY1OCl0CUFiYZfNUbuwAkwWvJq9PT\r\nHP9ez5v1FUGyKNpgX2OtxGfp6prYcZhfQHsE48e2s/7I3eGB/79J56FNIu0m\r\nN0+1/pqHVP7nxaQ8Dbm7zc30dn7QOGgkdoh3P+Y++r3RSgFqWLzkOifEpFKy\r\nv+7pmtUzkdiJysy2CSU0Lw5jaM1cg1MAONkS+s0PYSZbgYpeZ/I4kM2Y2gIl\r\nI3aSLiw3c1+fMMEQ7BQThM+kY7QWQz6p2+/IkWqSX4K8gTsyGnbjO+yanAgb\r\nNL8esePE+iukFfG0XoO2A8tcu5ahfu27SKRiiPzQCsphIwRZi1qiypkLAHQA\r\nPuCjg1CZd60Z/70S9jkHzi3sUsjcGB++1h4apyb4Crdp8jila8ZT11/phWxD\r\nUSiSOLWGVm77NjSSZXd3DxR6TAzooVId4Fcj7f3ThLD8d2+ZBLtZMnAC/NCS\r\nqv+01aq1RBp0wp9j6GqISb4BFnTG1ihiL8eH/3wKiMnNFoiG/oxXrkEstsqe\r\ndXv0LGs8ToVE8Zu7rveZF+eaGwh80M2WOOw=\r\n=G8it\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.7_1671058044210_0.25155997059027846", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/linux-mips64el", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "6cf093dd9706e8f078868ffb91b602c1dc7f135e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-RDSnljcka9UkVxcLtWv2lG5zcqkZUxIPY47ZSKytv4aoo8b05dH1gnKVWrxBZ+owp3dX48s2lXm6zp3hZHl8qw==", "signatures": [{"sig": "MEYCIQC/jyr/ZacLPDjJoSHtPrG5xDZsVyNmB5Ki+7dOqtL0AAIhAIaBCz3MX1D79i/I0kVBaOLcpH3gB8VKLB8nAFqqqhBe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQGqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrsfw/+PHBnJy5FwLkWYvWlx+Hl5xJrqsM7fOTWpGoJIZvsMl8IPrdq\r\nJ2zyatJ1uXczUvZl/JUn4E2i88TIBDW8lJTm5H8agEc0xOcBRK9R4qARVkv+\r\n/Otsn0mQBspWEgNHw/Lv+hOhV0n4HWF2j+aFTjOEJIb1TT5GcGirBzyCMj+z\r\nE5XjJsTR1TaaFOO4RvK49oamydWvVX5W7irUrygDHtx3XDSE9264n7A/Yt6Z\r\nolsK5SpbCwG3cmjkrlwXVY7qms/0+6+bR86dXZw6ZcEbTxI3rn/SrvY1qxfF\r\nJYhIAATlod8bOUd81QkIFJ4OTdjvKROVg63SxO7BQnAweeplnWPq+AsXsnyz\r\nqbDNGZtT4azutUWjdYTSHhXBioLrqbYgVLjp3ECJtv5E6Yy4R5+64NdLLA1i\r\neGhJzMAL68w0Po798b9VJiTDctZENn5FrbkDpkdOCq1xbkpRdISOfl0UsCj0\r\nnrntbMYcSqEldXvg1EJlyARJjO2ggwu/bYGlNOteW37wTvwNFG9DgRLhtOmz\r\noAJToq9uX4avauzJuIVFp/1FlSPLQYKeaaZvmObiv0PBQkMZsql7LGYvo2Kj\r\nachNWiPioV4j1Xr5REzRN9K1Lq/l9AO7TYIXHb0cq7/eHnniDv1uaZyRh/8W\r\ngA8SWfiY4HThnWuURftv9NwhX16r6Infc00=\r\n=bglY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.8_1671233962592_0.6807329366039567", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/linux-mips64el", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "492605f13f19dc06c350d94e4048c21478b9dec4", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-ZqyMDLt02c5smoS3enlF54ndK5zK4IpClLTxF0hHfzHJlfm4y8IAkIF8LUW0W7zxcKy7oAwI7BRDqeVvC120SA==", "signatures": [{"sig": "MEQCIBJjeeL8wAjjqVLRjExy4PjkvZgy/27Lgu9pEvDJO1ltAiB2ONZexoTBaDiUs58styk26nypC70fk1ATPgWoYwYEPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpe7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+vRAAkv+egppUa/bXr/8aOtVXZuFMSpS4lxDBT8tPgeNLY0cMZNki\r\ns8DNgM22leUy+GBPKyuL/mC5KfG4RkcNYE+swAjdluBMZduc16PNbUx2GlFE\r\nz++aUu4AN57iePMnmsmVTgsyhAkh1Pyjzca5ZqthBBo7uGALznEI3SIPfcjw\r\nW5qDcifZwhHryvPXsBA5V4hfa+/5j2WuinnHXu0zN3cxJZh7ttNU1iYGk/+o\r\n/EHSBYt9UjNfszujx4e8fKZFIURxKhq+x2pBZXaruEKjDHtXLW2euL21rp3x\r\nJ6/xQ1B7/k0hrwWcFUALq+j1R8X7vYYwd3F45kiZdr7rMK/UH02QRZj3KrUM\r\ns4rZ3/APox+VlXPRYVF5vlYe/Yry+8oUWMBRjXOqEyi1W1cyWoop3YqzQW1R\r\nPJ40XW8b9l6AgTDfKun/LmkOfSlJIFJ3iTy/Uz8GJniVsKIeOvILR7ETWrvw\r\nP6ngIcC14k77koFFFTBSTV4vXKpnsIschWZC0NKYCIkR8qk2NbkX8d7MvvpX\r\n+pKnZPP4+l1rWu05bN40SxJxdo66ZGunsfgaw2T2pS40fikcVfSi9jP4kmal\r\nquPzFo5DdvPVMh3nyvQlcEZkCNEI/th2eFzRfBJeF6Uo1A0PsPP3zXX+GkOW\r\nXRROakP4omVmWD3fyNQM07xX5ZeLkf8gtHk=\r\n=8QDR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.9_1671337915237_0.36737523478349265", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/linux-mips64el", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "574725ad2ea81b7783b7ba7d1ab3475f8fdd8d32", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-FN8mZOH7531iPHM0kaFhAOqqNHoAb6r/YHW2ZIxNi0a85UBi2DO4Vuyn7t1p4UN8a4LoAnLOT1PqNgHkgBJgbA==", "signatures": [{"sig": "MEYCIQD3DaFaLdV3ekd3xo/9rPEFjC/vK+Vm/C3wfHq6BHUa+gIhAPKn4s6O9mkN5ZGQbkp7/Y/32WXNfrCx61gfcWSvBsJe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPNMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpfaw//e/LczHlByZNnhVD89cGU86J+ODXTh+ELt64Wu7ClVB+yjl/5\r\nzhEY2ZpRmWkrZMOBp5bmY3/zX1/9SR/oZEfLGLM/+p53acPMYTgV2zQYWMX/\r\np0SpRm+IPounN7cj7PAsQX+YDA77SysF/hxM6PlJrNihJMjuQnv7LmWhR/ds\r\nSsEpuAoa+C2qUErnmcTs5xd49faGcak1CNE1Y0WD53mIammE/0cTkN0gR+lN\r\n+CeZ6idVJh6S4DRVYYwFRpIC/ry40XdOBM1vAqiONp7/KTPA+U3Rkodg2D5S\r\n81rJr0d1lW2dJ4HYK0IGnXWJs2ZNRMBd8MZL/D1DdSOSPEqJWf7mqFpQZZvg\r\ngfNfghI+65LQpGYUXfq0NgewplHl978ylxWf/CYo9397jUYWV30RwrvqL5Ua\r\nqSwzSACY/NG69ga3wsGACKp+FKD/GKtkhF+eSVEpQZO6gH3VI6nnYnKKnp16\r\n91dQnZ+CxUKsrfW/Pz0oIy+iTtFz3MrXbR2n4QGsQsOfTPilviqPXOXaCj6S\r\nzUbTWe/sRwuruAuDVkOEx4Q6Sn1id8ZGttwDzJLF90UBXO7btzUt8T+YNNUB\r\nH2rXY4sbwBzb7Jj+jY0FZl68r3g6dJxqNFTt6ZiQ4ZM9NyEXewDjblBDDnrh\r\ntGUcb32umD7RmC9JYlPBEyFzczHokeP/8XM=\r\n=ZH1Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.10_1671492428351_0.7344973925479892", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/linux-mips64el", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "0935896b7b44992e5d539cc2eff8939c12e554bd", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-IyotdnRg0J8F9FKttYe3cy/M9ZJ5W/Gm6whH08sbXMxRVKs/TyyoqFIA8oT1MzR+si6GLlRpcF7JbUnOXssjPA==", "signatures": [{"sig": "MEUCIHszGRq4wOOntJGPWA+D/GrJyzbqH8uG1XHHWf59iJFkAiEAvcbc7wSaWuvvevqzrk2Zw+8llG0azixaXJ+3+Yza3t0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY+RAAkUBDi0N8i5TVWNI4sxYBH6qfCZgW/alvYCmzdS4tuvRvwg/+\r\nPZwMFTiPZKauH/e69W3+vvXrP3DnvAGcdMEQNAc+SfkwaYgjla/zYdmIa2pl\r\nNkJQ7/kMYneCKoYJQ2VGndf3V3B7NkHmD5LEPs2vJoiLM/hpo0Dng8gtOwX5\r\nec9SGSQhf2baKNm01JUMGo2uY0Wm4bHEFBTdmaeTpa2S+am3/bCI+FcN8Fa+\r\nrJM1IcWvr3k9JBHQvDxupdi81deayExgmpWZBFxdnDQplsYLvDMYdA7Gqj6N\r\nX5RWkn7HpsS5S9mcRirdx8uqYykf+k7hYG8E/9Aw/XcPqKj14V6WtXKtJGdE\r\nnK/ePsVnbrqfO5Z+/2qo+I6tu5Gs0D5WlcaHbN8e8cCJ1dGa8RvlBeK3N8Fy\r\nHm4AVOVe+ompyuIDj4w6qB/DEPoncc3KS9Ngr6eGNk2e8bcVkhW0fw9HK+LA\r\njF6QVsGYOji2Hu8KdO0XmfzF9Yd6E8bxlTKpeLAj/GSS7e2kQTEyPjjxmS3C\r\nLOun6xqlUFZTbMqLpNBQFa3yw7Z9CRqJ0mvngsJPFkI6MpiXJF+J7YcxeQAv\r\nw6kiIlfmWNsHN4DQIHYCSXKvcGhTuOn/312mVtPWoUtx/uvgz9kKWpJwFFnN\r\n9yBAW2xLdF//CAGRsY+x5lzC88DcCvC1WvI=\r\n=4Kff\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.11_1672105173133_0.6953303734701075", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/linux-mips64el", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "9616c378ca76f12d06ffaf242da68a58be966a18", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-zI1cNgHa3Gol+vPYjIYHzKhU6qMyOQrvZ82REr5Fv7rlh5PG6SkkuCoH7IryPqR+BK2c/7oISGsvPJPGnO2bHQ==", "signatures": [{"sig": "MEYCIQDF3Xxw1pmwEcmU7d/BefcXwFh+30T/suk2CM5b7eWltgIhAIXv0NWI3Ravm3xmwAFN7q35JhO0O88ZyIgYxO+m98ji", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6U0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqidQ//cUPuPAqTxYLUClmjN4khqlpGEQ/P1iH6LcLP5sQ9qnvzAC33\r\naqp2X1bjchJnYCrz6C6REiy8jo5ubDpAqiv9wW8tUdU+VNudb4pEoNaieotK\r\n2BNrcMgD9uyn3z+TtbcwmNYjcndSvKn7yukYhVl+MDLX7eDroUQC/O8KhgeP\r\nbSH+AJFObIRT6TlTfb5sN0QiaK39pJAbPWrziUGa91dL08nXMSH9c77zKj2N\r\neWr8LBBgaW9rGQBi3VFfY4qzum8hJ5vF48S0kUM2OgcPJU2GN6L4gpqRogy9\r\nzunBBS0gW2hNDe5vAJ5t80qk36qozuTjxAATpsnmMrONpd2nKh2XgbbcNV0h\r\n+fpUwd19FObzfEZcg3tQNR7FCZhUoh8Sxiz2u2DodBzDEmvWMXHpmqq2H2fK\r\nagbw62kLflaH4bRMmM54ruITbhE85LGjgthJrGefbHDF1LTms985WhVoSyO5\r\nA0AlW0HutnHe07V2XFnDXwAala3bWDcJG01itnFj52AnuMPvG5Z/BaZbyf+5\r\nisEQREfNAOjXl4fF2GweJByhUklICjwlg7IxjG+E36fLpqH4rrId+chw0vq0\r\nQBGvAPCZgKFcpO6K/53HUZR3Mo6KwVf6cmKGDizvZpIdtivqpT5q5Fp5rlyY\r\nmpf5DRZNyDukwePEOsOgeJi+Xq3a9gZ/C+w=\r\n=TgQS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.12_1672193332039_0.2440081490790278", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/linux-mips64el", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "30d8571b71e0b8bf25fc5ef11422221ed23cdacc", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-qhhdWph9FLwD9rVVC/nUf7k2U4NZIA6/mGx0B7+O6PFV0GjmPA2E3zDQ4NUjq9P26E0DeAZy9akH9dYcUBRU7A==", "signatures": [{"sig": "MEYCIQDnbpUZ2Rmf2gsaUFTY8z5hhhv+cLlpOzTRiaRNq3HXdgIhAJ+mXZm8rGtrN7MYoeCKqkFfD8HZxzRlI++a+zor+JvM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqcg//R4eeEWI2Pb6PqhhFIZMKQ03H7vA2GpAA1ViIq8xiPTxZ+SOZ\r\nZXJy8k83fKi+2n24QeSckT/cI2RI4/zNcbqpzC8qPfDBmkEGWmjPF3XSBcyG\r\nM6uT0fp33kVk7y93UNm5lDK4YxchVOfqKvfVBFD+6PtJw6Y0mbngTCixd3mm\r\n1b6aW94gzFV3sBQW1/i9U4Bb9D81yZ7HvU+0243jsrVISb6Q7K5Wlc1mpKSC\r\n0co/H/NHTDzTCLGrC7R0qCQv6hvPB0FOGZOrTcPgVgYrBj9UsIoxv+E5vook\r\n6+BdHvu/ti+FH+DerR0UHhKXF3g3q2NN+pMWJQj+AtQfqyugEMnvOPu57yrW\r\noYdLGA4fcoRU8ki2hcLbow8EUnATNwRpXC542OBSbL4lcQjwrTxR0ZrA2QAb\r\nRxx271XL+HgE1oI3kZZlHQKFyWMdv4mooYxogUd65ElwOEcbtXQYvnm+40gj\r\njG+Q7i5APhKMBasfiVGg/Ef6f34XotssA8d1EUBWaeBNJLHn65u27EpBC+tU\r\n/Wk5W+we3FfSJ7zxmKqELHO7lU+8jXOXsqRIog1TJqp2DeLwiUb+j5URAeSs\r\nQeouwfTHrtrV76QeQ0NrrXn+eSFBj3acQkdXHAT7rCbd1SCOeINinZwuk+sI\r\n5Wioa/DCbp+n3FDtWmeilOHhkp6wrGkxY5g=\r\n=cFne\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.13_1672700270784_0.0018627458709348232", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/linux-mips64el", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "8af86bdc6ee937c8a2803b3c197b28824f48df8e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-kJ2iEnikUOdC1SiTGbH0fJUgpZwa0ITDTvj9EHf9lm3I0hZ4Yugsb3M6XSl696jVxrEocLe519/8CbSpQWFSrg==", "signatures": [{"sig": "MEQCIFStQxtpp5naKt8YQ19LyNJ8jhtGBPrUv5gN2h2d+y4vAiADTfMlEVIZxANEkXEcPhCscPX+IGtTyFDlLHNlLydojg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqByQ//bn8ePXhyApTvS91ospEYW+88TPAZFHKao8igv7xLiYRkJyUO\r\n3QQr0jvMFMIXlw8UT9TcFns3YOzL93yXXhWz1ow6MYpF8QtYvq5qR8SiWBgP\r\nzgSjcWv+sV7kB1ootN2lxMsu8GGRiYmYKnMkD2SnyLO6i87/A2IpwLIeHTVv\r\nVzP0b/aet7YPgO3KfkFxtSHSGMC8ujcQq1PzyJwt8BSCHjynW51CHDEtAgt8\r\nsNFneRjlYLGij8lQG+fLCcVMsKkX2Z7HMAkbunOWHYdUez5S/vAKD9juxL5n\r\nikJpJu3gjeDXhQLpPLJ6a0EKe3cd6mk1OzYyHumYfCXunZFeegXtXbYzbXpz\r\n1Ron9N2LAYOko7Fb7Aj+ULqeJ5QklQiowaP1HbgCFWMVGR0xLylH2+VZ1HE6\r\nsAnU6TDVYusbhT1UXvUzASFaSmO3OcJIKwYDI7ziR3lhRHBrJIozq/nwD6xQ\r\nuMb2ET5Dc7JZiiD3HUr3vXatHxB7iMQRJYjL44bNqfNpYBXVUce9N/r6EhZj\r\nVIjdSlETJkCdylotGxJW4TyZkrWjJK7L/oGQ7OM5mHIVfneUqWmMDOtnJGM5\r\n6WEKrK97Rny1L965ix7wYQNnt7T68URSgWYepWQZ7FNR1AS0roPK2hOBA9VV\r\nZeM6CX499fnmCkemLKBYcaCzwbvugS1zL9k=\r\n=afhL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.14_1672863218228_0.6849873400703377", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/linux-mips64el", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "41e325ccd6432f952c674d763d9e5acc25a00267", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-3SEA4L82OnoSATW+Ve8rPgLaKjC8WMt8fnx7De9kvi/NcVbkj8W+J7qnu/tK2P9pUPQP7Au/0sjPEqZtFeyKQQ==", "signatures": [{"sig": "MEUCIGdgqXS5C6lFlKpfe0WioruVcFZq4RQO1Apm1buQKRwaAiEAgXKtcfP38T9p0zaS1iLnHtE2idy3XzgtoDxGmwOQdk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPLbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnIw//UzTJ8t6vVtMb4XT3xaggsbSKadGKRvXZdIj+kViXDO5upG+P\r\neHlgI115n1iDCTqP7F/f6DlcqsSAdx9hGLAqnrkKJvXz8V6bPZYatKE11nam\r\nuGNIszXzIEx5IAXffMlwCPlQWQUbNp9J3o6r+3JobW1oIuRrxOubCztcKMc4\r\nd7o59MJKbekuU79sGtoWGmmHYiRE04cDqfWdSs6tNt09hR/rKwyTofeghBfX\r\nuKgq+fI4R8dsAnQHLzYupQNgJNpTfLk9DpPZjUavuY5Lt1cH/PGWEn+Pq578\r\nAZQrofbj+kXCIIq1V2LlHFSu0caksYNCo1COK7kOA1TyaEWXltIcwfFIXqJ5\r\nb7WNyyDdhq8gnXMCh6zNJ6cSXf/VKsqSE6zKGkoWh4CqYmwGjTv3pHk3YFBj\r\nne2yXbcLFMD1yFWeifqO3yNccHNf8w13ovWLyNclKdewoPkkkaJJA6BUHTWz\r\nK2HjNt/bOFYMgp33PVTzWjHsTjPFNpk+a5B2ACGTIEYGzJSP59uZRVBg4OMp\r\n/VIL5dGIW+s7baBKLH7ln7XJ2Zx+DKte0hG4h0mXcKEt+usCXTY91jZoYd5/\r\nan2tggBOQd7Xl2/zv9KOoSHFf3bDzKiWiEM6LFzTySKo5iE9vGu7ZYMg08j9\r\nYo2/dNED9oatp85SUTZ2NFeXlH9DYwVf574=\r\n=SARU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.15_1673065179265_0.8738351989790727", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/linux-mips64el", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e85b7e3c25000be2ae373e5208e55e282a9763e0", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-CO3YmO7jYMlGqGoeFeKzdwx/bx8Vtq/SZaMAi+ZLDUnDUdfC7GmGwXzIwDJ70Sg+P9pAemjJyJ1icKJ9R3q/Fg==", "signatures": [{"sig": "MEQCIEFGSOLgMa9gDIKz4rP+00s9A5yLpaThjbVbl/VCgh2iAiB1x454VcxvHBzNYx3qbsra1FTpnayy9ebrwsMYsfT3Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9437702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0dEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+sg//U5Yxu3zIjcRqb2Yg4BjSZ4O04CQnCgu/IHkxeNw8JVeYW7so\r\nbrMy5vcs3vjex13DQWphjnWqXvx7Wz/aC0XA6haA9J6iUSqhyKTiDUOgE3lv\r\nrI8x09xURysW32GjwM1BvVyQE02tFNVMZHgbtWp1Z2FUC3t2RXLYJzki/UTA\r\nY78c12vWEjmcE5nXYwgTZsf3qjz6w8nvJkAPQ9Q1FJ/SjE8+3pedEQMYeBL8\r\n/n/SNki8w0QN//xm197eERZFpj0dPO8en9LAPFijdtEzK7z62rTIcU8grAW4\r\nHIz0ATVbRGY2MBDZ90JLwPdxIUFpiu2ax6fvilIwyXdKCMs+kFQVJZtxcieJ\r\nQPCID1tdu+85MQXvmCRQyc5Dpar0zx489/lHQP5dL7KvrK2t2aRefsJb5NuZ\r\nRTX2ODCQataGYWgigjd099UcJuA4ozQ6RG+S4gYZF5yCVa2ZcRdygV9UYwYw\r\nbLcOf0ZU6TemnrHswb4m8QmZuW6T7drXuTh6Fkc734a1GCZ29wZH+/Fiyny0\r\nD4ZBYYKKXv14Q1eExj9BiChDQp6SFW1G0J4UO4EjUvFDuxIyziDSGeqAINT7\r\n/eBLZHMY7vc01I5y3gl8qtFJddH6N4tcya/kMoW53tHGqO86Hj9YnIhsUf97\r\nK91SFcsiMtRuRTL/N5V9GWoYrUg2tPRGfFQ=\r\n=SGtA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.16_1673217859761_0.9802422092971923", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/linux-mips64el", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4e5967a665c38360b0a8205594377d4dcf9c3726", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-ezbDkp2nDl0PfIUn0CsQ30kxfcLTlcx4Foz2kYv8qdC6ia2oX5Q3E/8m6lq84Dj/6b0FrkgD582fJMIfHhJfSw==", "signatures": [{"sig": "MEUCIAYkXRfihU7yjDALlD8LjZ5IhdbguP5apafAYUZ6zsj3AiEAl4aEPtyc/3YZon6ZQTJptY3ZGkFZlpBwsXIitWQjLoc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzEBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzrxAAgRFPaEKrGJoBNNI3jfapdgR0sv/cIA4vyg5Frq6UWksefpXI\r\nZXYOMzKDYnNqkc0iJHFhfqE1K32S886W+NTdO+yksINW89TS7qXleyrUEqPu\r\nl4d4l1wv9+717i3m4qu/h86uU34in21eu8I/FCnGlaxR4pCKBN2b8UTemO6N\r\nnO9vmGn3vWdtCYO89Yo+alKi0aO17zsAEP4pzvERmLVr1PAAaVa4kqiE51LE\r\nx0OATYt2bPpOBbbkYM7GeJpdwoVIzm+94XMT7Xe/ztuaH5ZMEB4QvN9HNWXB\r\n+HYyghWsFgL3GefxwnQI82oP8Ox77PXK3EfR3QETMlazbo4ZQZm6bCr61yxG\r\nNH0xUQPcTnqr4CFAhd4A3WzqOmV4Dq+ZkIgoGXfsrC8rXqrcNmP5Ex1Hxg03\r\nbAOurQocIxpgD+zhi9JDhhBv4MYtcbIYUKQT7jIwjNl2vS2cvTlVhRUWWbnJ\r\neVxswp+sqKeyaAINu/LW2qeRXDwEdRPASPacSL9HIa4TqQUfBcA/4KGEw1A5\r\niAo3+hiWQbYiuihJyHi5lh1yjAZKOFuA2vRhFl+lEwWCZkZkszSHW7eP0rOy\r\n2F2aYM9nQ1PmLO+nY162WzLnlAn2+NPTLgKF6GlDBrALStws/IpOwixzYfzl\r\nBZP3VYoSqZCt7/yqAEiTkAqSFsFQ1YcBugw=\r\n=rDDj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.16.17_1673474305458_0.737153172461436", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/linux-mips64el", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "94b50097a3421ff538eb6a41cd4fb5db4c4993ff", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-LWQJgGpxrjh2x08UYf6G5R+Km7zhkpCvKXtFQ6SX0fimDvy1C8kslgFHGxLS0wjGV8C4BNnENW/HNy57+RB7iA==", "signatures": [{"sig": "MEUCIQDZKsPOwiJ0LOMWntzcdgWsnZCqz9jnsMASsmafUMjPmAIgXhut5qgg2zA2MPvTx4Ly7TEmwfro9aTETC+zH0Tc+cQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjGEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmre1xAAo41J4hgZNdgbMcOPPzPXQ04nyPo43O6/Rc/i6pUIZ7/CEnfk\r\nF29QCLm5E//zCpHP07vMpfT0a7VnuFqGTW8Jpxg0x0PcqNKmwAlZxjA3yE8F\r\nIPGfn1WWywbtXV2tb5rm/jmm6waAebZdd0643NcjYjJIkBBxSDqtTfG7plF5\r\nDoXn5wpVrSVaAVyziQxBOghAARN0244KNIekX++OMiHjP4dSlBCR47wMmbzQ\r\nf80p6UM7gtLF9pKvRLdgNs2v/wB4ZPU0AjBxpJe3rXgHNyrtts+rtjjkGilS\r\nyFLrDe4aHsDpFGAeADMRavzfOInNXVcikbHfnVyv4SqnhvPcikczVjTfo/d7\r\neUGi1gwE/Pd/XJHcx/87AERSC5XdXJcZUZ3kGtbDvsxCNUP2n6JZsl2Jqj/R\r\nvCXJvhR88E3I5pkVvlq09qqpeSFR3xnuhYyqS7VNEOqDvyutrDclOhrA7aD6\r\nvd8wM0oChQ6ivGqZpzLMq6S97oNtH6JgRgytj+vqSritxpZ9hFmxCtDOyJTP\r\nI/GNXz4tMszE/cNPI6NBuNOQy4rHioxlg2Ty6mH4x0O9b+jVlRqTxMLqXRET\r\nMV2U2pi7LfbdEUASwKub80ioB1V0ncPAwm9O83O5ENXNqMPRJb8jABXmIqZ3\r\nQw5tqQhnR+Ag3aDEtP5pB8EZVZgPABCGBrE=\r\n=G8S/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.0_1673671044583_0.13405956116377116", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/linux-mips64el", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "920d6f203da3b89bf868f7b3f6eb66374c24f92e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-FjUTo3aDHy0g+4aKjuQl576wSOzcfKrYYyhQKRTmeEZIaYKHu6/iLQkSqB1EBbfKcn6gHqO3Fb9PoBF9cWC5Gg==", "signatures": [{"sig": "MEUCIQDhobi0uRquF/jSRjjWGscS3hsKf5DiV4pvBaXSl1cBYgIgMrLCvNEk4LT8M/eFANKpzKPa4UeQw3EZW2P2xsIsz64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZIbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCfg/+KCPAdHFuNsg0rGK6xzT1SayO7FDMiDo1KNHfGQzW9jo7UpBz\r\ng6yOmFwI6Rjos6k9C6T3oypYcLiHkQhNuxxZym7nx9pTzhuPpMjzQJ65tGJ3\r\nP0QRN+bSQh5rFY3f/h/y0EYi4uogTAhsn/EZ+FVuts/7MeVUSm30SsM71gt6\r\nqxjK8BZQHs8DYea2ivrKtS/dxPUWYoPQ5FDkIhkCLPbVnXvuBkpxLhy25VdH\r\nIMdFI+AjEwHf/ZQmDWRKqa4xE51dQNjfYbjnWMfh7v853kmw8dMns1ZZFzM4\r\n3c72VTqS66kqk42LqvCzrSWxZymM9jPG0iQns/YNUT1m/aQ4yGG6WEfj7bm9\r\nxqUDFN9yswBhUTZTBUCDiAY+4J0StdSNTKuIDSzDCIw7UqIeiCBbHwP1qsoD\r\nEA7AmdqLUXbEOEax9JKNO7crHNC8aVthvRZ/dS5VQgltpuLTJbEXNj9bF+ie\r\ni+qX+Nba8pw3QZ7d95WeTppAD1iAGjRH4OJzHEmHqL71t+ZVgTukMR4LNWXt\r\nbq72ImKjbwCMP1jx8a57UV9quN3DSzkTYF72uB/y53JX5FoF+gdZZDc8roR9\r\nZSyO8jZId+ex0ogrRzRqznlVJoJSmE43u37QCP8CzdFLFczraKkUXrCBeI10\r\nRIIgeLLEKUfKHWAM3P6ZD4b11b33hm/JeIE=\r\n=XxnP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.1_1673892378794_0.8703083572523147", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/linux-mips64el", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "98f0e25b86153d725d4379bc267a2cd4c9bcdd24", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-guYcNHjMRO1BMxWAeb8LDfgQaU8oeUO65xtlclwBD+hX3163KBifEHyao1hK96J10BP9n0UmZug6GhtGZaNm2Q==", "signatures": [{"sig": "MEYCIQCt9iOMpnH1cTbLWgFly83qa1AJL+iD0AgrNr2ZMiVp2QIhAN8VOo6t1alEwrywqeRFVq/ad+OGRsdRPYWRX3q784u3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkLPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwZQ/+Jk6NPIlzEAYV6TM5T+tqBgiyyDazSbpLHjnbtsllI6oYKzqF\r\nzHkA2g1sQX3NrseqANh5+pS9PCYp0PPbuX2qloJ8v29wDymWhhk9hNhOr7Nl\r\n0j76eyAuVZ5UvfacIQJ52sJkpbudznaZ91oqBsT1GyjcBm3nGdXSYWJWSnbw\r\nF52yUkKQz17JAaDwJx721I19Dqa2CKny/UC/zNInJCEZBDuifAP607+20AV0\r\nnS+TNBHSglNHa3D7r5G0/JXYQLgZ2JRXGFnCIs6NrnL9VW+zQeQ/ACRiMFAN\r\nRLA6zKqeEC/kPlnKNPSATy7V6YqcIn+oO7WkT9tYENHZ04T8cKHliZr49OT3\r\n8f7ZRMwsu4qRpFX6KJp84mS7QRQjI9QVDVGyS7rYO+DytkglKUjCBeDzkRDJ\r\nkalynODTkdB8qgUEZOePrI/M+30cgHyp+8C9VfA2UF6eq1AudhaMtxyA9dAk\r\neqBckfHFnag1+mJos0I1ieTR9ITZCU4dKEq3rrG/bt3CNKUao9vPie1xIeaL\r\natOasI9b5e5q07uheN8UbZAOoWhTETUA+FGM+0T5zj4VY8JLPaNEYAtaQrFF\r\nxcQxg+GPCXFaVS/2XkqM4FSjIy48AT6o/Np3heOD+s8pl4aseaGqnMTnAJfm\r\nPyYudE1RE1wu1n5jMzXMcgOSfjqsin/qUJk=\r\n=74GD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.2_1673937615511_0.6555170640567534", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/linux-mips64el", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "fa38833cfc8bfaadaa12b243257fe6d19d0f6f79", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-DcnUpXnVCJvmv0TzuLwKBC2nsQHle8EIiAJiJ+PipEVC16wHXaPEKP0EqN8WnBe0TPvMITOUlP2aiL5YMld+CQ==", "signatures": [{"sig": "MEUCIQCzRiQs67tdygwGTa6WEk2Y5DDtKq+TKENqVZhRMl4d+QIgehUSsom84ZDkYZKhPm5lyjqDZlNlhRzZNR/n6LBYSxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEU1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlMw/7BSaLQ0CH2KyHzVDRjLlk9jpF8JVKAhFWGykZTQQaPyTn/7hG\r\nNOqOdiA1B3Gv7s7paYn7fuRYRGZ/o8SO67uEaojZWU+TciEmGzJHjVYZPnV1\r\nYv3Mibjxph/Mj2haatNTc3ftxz5wfqnAvxaYrBQUs6gugfl+hR1MKuoQcNGl\r\npjOmbqJPT5ZvfnB6ShFPCUqUTyEU6cAx23zc9FjOJTZbwyAHBI4I7cHPt4TF\r\nxnlHIgPsckiSeIzVm7noEGRVmBiEvot71X5dJkOtSn9g0coFOBpaP2fdR5MI\r\nD7QOH8CbA+RCwBNXSynPwBcC/9mp8ejdohTL/sBePmA/XCrR6lFfo9Q8N3OH\r\nQv8ucOZ7aCGq6USWRYJeEYsV3mtZiH6mUVWD/wtFtd0OF3jKoZ8Nxp7kJpl9\r\n5rn907abSSyrz+l5Iyc/vlEZVB8hcCYclLWver9MnNq/spsd9buc0Np/9SAo\r\nYelygePmANokukYyp3ylTnAOpwgn6PD+7yqV6MNktEcEFg5HQS9+NvZYwUjh\r\nLzOENTWyIFWutLatlo9ybSuyMXeCgeApQKhouEsA0lNT1nZpe68vQ6cH3egh\r\np7CzW2gz1BisitJRdm1PL5PLv9Nx74TbN5NWlKT9w7l8e7GtQj227z81J/eD\r\nBvcxabsUk2G/vG2NIy3wQkqb1X+aD+rLLhs=\r\n=Wky6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.3_1674069301447_0.9805309463707106", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/linux-mips64el", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "3dbd897bd8f047fef35e69bd253b8f07ca7fe483", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-d/nMCKKh/SVDbqR9ju+b78vOr0tNXtfBjcp5vfHONCCOAL9ad8gN9dC/u+UnH939pz7wO+0u/x9y1MaZcb/lKA==", "signatures": [{"sig": "MEUCIQDpoiPMy+Bm6AepNVgZUbBj3Ok3XNQDNycCWOA6b208MgIgVlaDJyL3mq2aAPwxi9frjP3nv5ETCB3q+yiaKhivxDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4Yw//bb+uv0k530y+3SnDK54kx+Bn+I6HmDOxHmxJ8iEjjY2VrN3H\r\nHXK748RvMno9ef2JV08PIqMnPBHKwDuLB69pMvevznPmY11hrEE6oi1hj0ly\r\npXDMviF0HOWvSSNmzhemQsWYZcgPV76ELtnrlQi0zmwdv2ThE0A4wdj4hZpE\r\n6nboXYfiXFkrUi+FOuCs0NXcFIj/I3d337LrWctgV4lnN9DPD37K+DCObaId\r\nWitE90JPhWuwCl3MyZNNKbnNGDlR5pDt/FGMkMuzMo8Aeh1FcseleZNCYVex\r\nZtKslPC4iw9F1smaAKkP0z7Yn6F+Vw1J53oUX2GGbSsk8jIIE8vVQDQy9vrJ\r\nMHmnvw0GGi2mIE10E31jOT3nvgqsIG+FpbsnRqLx28UqH9XPgfyp3l630wZj\r\neJluwNZyToRO+x/Nc1uw6ScltGc7+ddYYHpjvh5xUPyrh1lpFR11cy2acTJJ\r\nSroCcdQCPuNRy8dGHCl2kC24sfXwBunu/1P1h9QyUx4vaGgFtEtfAom/8wCz\r\nknZXlCCslDw+5X+LOaH0yT/gX5Ia2Km8SGoYgu9SLOeAuBA+Pm5sXxap+jw6\r\n/KJO56scCawOJUr6AeLWO9NUgCLEdFPd8LkMcFydCpNOwnX3OQWGlxFgyQon\r\njK4ikrZCISOC56P5syoP/s2MmVXnf8ljHOI=\r\n=065i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.4_1674368040964_0.029890700917523993", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/linux-mips64el", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "fc98be741e8080ecd13b404d5fca5302d3835bf4", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-Hy5Z0YVWyYHdtQ5mfmfp8LdhVwGbwVuq8mHzLqrG16BaMgEmit2xKO+iDakHs+OetEx0EN/2mUzDdfdktI+Nmg==", "signatures": [{"sig": "MEUCICW1JdoS6eP20XOXAE14x2/8VegmdAqyHew6TPWysYAFAiEAvjpHWID2QGoB2tTdhCVQ1UD///ccsoc56ZmKGGvQjyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9765381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/37ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmopsw//c50OXXcFsf0/XTwiHejpuOkqOWREJ7aHwwZ3yIl8rQLLe9hk\r\nMOWDscAzcRULSU+A6LgfpteI7QODNbfxGtjV2cLyMfu/NERuPMg4Q+igptCN\r\n3uiBXLyN0T251gaP1cq3qN031ZxC2Egaf+MgQYKA7LjNiDMOjE9tIMPN/hVY\r\nL/p/LGN33ikbfkd4LRcov7hUCptj4YTLIor7gK433kSggZpJIR5EJD3xZ+yP\r\njbY1yHI6+I/RgbvmspSM8kqgC8XUvFxcGpywMYmELwDhkYdw0n7zQgLrp3in\r\nVUMOc8W6hUMsZgstm75YizDmO8eLIcmYr0Mqhcwmq8csriF3hJlipSCcpdR3\r\n8xHBt813bc4OlZXoLM/IYdwPbAlMZ0p6L+Bog5wL9upl5iLcZGeH7XnVhbpB\r\n6TT6RZnfIxv6SvRY19D7jwkX7RqrUC7X3TO9Sub2Y6JvlfBtJmE7rEbxy7Ad\r\nP0P3DVNZRJCPMoZ4Jm5NHMXxmuvpS/6nsaTgW0qD82wdd3mxnmORIubJlkb0\r\ncO7RKW/1WiTc86wEVfvFcnc/c7dRY9Vhxu2nIgtFt2iTSWU+yD7YURWb4+6J\r\n63Bor+mLthkUd5kEts8Po4aDLouE5IlDJmjo6ypgSRPLd/F+AZ7IJiLTSrrb\r\nlswaohRVEvUSq6BMkYuTi2k3XDhEKpo+nxM=\r\n=X9q3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.5_1674837499275_0.8462200956059218", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/linux-mips64el", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "15fbbe04648d944ec660ee5797febdf09a9bd6af", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-09AXKB1HDOzXD+j3FdXCiL/MWmZP0Ex9eR8DLMBVcHorrWJxWmY8Nms2Nm41iRM64WVx7bA/JVHMv081iP2kUA==", "signatures": [{"sig": "MEQCIFGSEv/S6c6GTW5km/qI666IDO9FwAOk6K/oK6ApBCNeAiBFW149Lx7EJVDJsg9mPthfwZftsIsRcWzmOJ3xYgnkXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5vA/5ADnIgSHnBL/1nw83RWq4Fwv/WPPdthSzHe4hWzq4VCKIOd74\r\ngccC2yo+NaeVG+t38XNRaTrO7/eoyuaDAx4I/F7/kKHOEOey6VOn3A4frr7P\r\nrk3p0V7ZH6Z62lgcu5ITrx2htVPhcHHVKUWNo047rzd1wgvqdoj2Bep2qfnS\r\nxAKNh8z+C3Rh2AbFRzKuJV3b/1yLDZga5k3Y3rAexeIJJFyVhDHJtsQ4FU2/\r\nLHPKYd0BsM7SxUt1AJFAZKc1uBwIRHYrcKzEMYUAy/2Ia3TBTy5kpws9puvv\r\nFeijIdUEF9t/PnzVU83E5qXykz0uvPeuLkEUAuCnXO9AZCOlldBc2HKn0vOU\r\nkze09an8QKUqNGFPc6V691FTXTsCrBDkWJL7cK/+NTOgRx1MX3aHEOx/KIYy\r\nHi84hcQaiPXLCZFZXb8WhQJxZRMrkTYRD3WBBP6c6NYuZ2bi5ZXFYD4rfvRN\r\n/KkKHkYxDO+wCKm0k2hJNXRDw8hBgbdsgiItQktnfvTrSYzGUYQXRiFPov3G\r\n6AySRl3h9YEk0kI5eL4icLAr9dobdIgb/C7Py3zAXH1fG4VZtfdi8DtNV4Tl\r\n10I9DlWN7Cl3x3pe60kzfMC6ur5B1Acw23yVHFPEG/qhsc/4VvU5JvE7f6gy\r\niL+OE1ydzjccCutmx6wNGr9x71dvaH6LrIY=\r\n=Yeez\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.6_1675702871762_0.6239857652733074", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/linux-mips64el", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "ae751365cdf967dfa89dd59cdb0dcc8723a66f9a", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-MDLGrVbTGYtmldlbcxfeDPdhxttUmWoX3ovk9u6jc8iM+ueBAFlaXKuUMCoyP/zfOJb+KElB61eSdBPSvNcCEg==", "signatures": [{"sig": "MEUCICRHtSvD3hSr4Bi7RKkuBgEBNDcV/KUon+6Y3aePhUN3AiEA/KRIcxsRYoHrYxqU8KMZ5U7veZEFspN6h9ZK5qmR6j0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XNAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMrA/9FFI60yShdO60KBEiaQQVRxhQzZlxfxg2eoio0R64/Q9VesQ+\r\ni0NtZ0jmvjoaf5mDCHue4Vz8irYg3qW3OHMeZnhs9VCBfEf8g7MTyON5Tc6I\r\nJK53pYgLaiQete4AoJV58+mPY0pmowl5+5PlQaUwH2PGYWLHU7/Wj52HPGwJ\r\n+RlK7xZptoPhfDTD5MY8ZK3CIsZ6c1UdI7LHi6WmfPz729ERL1nlh9fvhjyO\r\nQchG6t118yqkzc8VQgHhPgM2+PfoSMkg72W8ha3DCjk+Ya7+7nbsSx6I9/Pw\r\nlSi4kuUFLNTV4qwcmxIcr+E8BtgO1uxsrayty9fU+9nxn/UPTGnXW2/+errO\r\nz5q1Ru89rUbTDJ/deqxzGLbaFynUhrPYDRe/TTuJH7VX1qWhFyNG7aHTofJx\r\nAo/pVN61acihoAI05Mfk4G4M33H7/HrylQMiLmfZSE7CPt+O+raT4KCIdoWP\r\nTRBtQ8l5SBrakkBJXiKCEHeGGsAKHXTWlcRj/820qr9GXdikapGKiFZ3kT2i\r\nJx3esXENcaFx42Y7kwQmAYeFv9jKkCumZIi5pCRgaGoAsRed6M7lHrvlbf9x\r\niTywtBWUjdpurt/AFoFpORHk8eaC9bt0ysWDR3LQFZpEqk4HuKyK46b3ABjy\r\nD/7gkx4SEFod1E3K0elkdEAXjG087kTcrBU=\r\n=I0w8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.7_1675981631828_0.71678678851812", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/linux-mips64el", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "f2b0d36e63fb26bc3f95b203b6a80638292101ca", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-CCb67RKahNobjm/eeEqeD/oJfJlrWyw29fgiyB6vcgyq97YAf3gCOuP6qMShYSPXgnlZe/i4a8WFHBw6N8bYAA==", "signatures": [{"sig": "MEQCICh6vwsedycEIZ/st7yglQ2M+k0DfD26SuTfcKDjUe21AiAj4NZY5G+suexRH5233C0uKh5wJbsDtvRtfRYuAQmcQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dpYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiRg/9GlfRg0lO2k9nskXHLBV/+o9WWvgkQpSWtYMvRiaSYA/y8BFC\r\nirFYh8cmY1h3V6unue0nyoWfXI5aVjXMjGxckLkOvtimT+oCVsnZcAjkYqR2\r\nfSukDrAyWd1tGH26EjB+9Sl8lrCmQ7asfencnBSxHOWaePTMjaOn07e6PFvs\r\nFwKXVcyQo/TkwjkV2GoCsCsflUtgM7wHHkatxGHEOFAc1qwyp7iCtoS4asVx\r\nkdlmExPBK2SsyPfT+UDwdpnLHOUmIutGo+fQc9250xhq6UxUbhPbR/Jnlz1a\r\nLbOGgmnArIj/DxRGzFURRC93yYgS/xYSUxnQAUidRLcqYoH9I+HFPDyE2Z1Y\r\ngUrUuw9HHSpBXEXlTgZ1StiNXzeILNeWrTuPg+kHLnYAhsl2UubJkfn+Uwnt\r\nYCQ/wEAkV8Enb8cvhNVSIkKtmjzd/2w+I5jdueP8JLUJatJhNBi5pdVs4w2A\r\nmYJepdS+lCtOQA6KgxqoDGOyVI6HJIYiq9LUeCiQqecjtQ7dqKieKKauFKyF\r\n50a8NsuO0pi2gXRinKddOp+COga/CLpW4bdFdsl86Y6dXNBuen2HdVXynLYN\r\nWDGKhb7xysiPEs21njh2+c5DJg/+5B4z0Eqvx0U/XZzJjx2KBWx3SGhINOR+\r\nBtgGCUN0eJfnu3lVLM1vzryvbqsF5a7/+wo=\r\n=hJQQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.8_1676270167766_0.6092210106254401", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/linux-mips64el", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e12f789e606b6bd239c69a56c50869b1c73bc7cb", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-9O0HhtxRzx9OOqavv7kIONncJXxhzrbDFmOD+cJ/3UUsy8dn52J6X2xCeUOxbmEOXYP2K+uha7b1AXG/URhF5Q==", "signatures": [{"sig": "MEUCICrIhWIj5Ma89eU66rxB270XnUlBbHXtaXm80SFJwBhJAiEA5ZqL7/cgCGalKBCWbt89N1+RZkRn5ffJbE2opJjVj/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mBLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7EQ//Q9vKFHfBRQB4sFwKKOw6GRWQ7Qx4HZgkjzEbyQjlmEHgbGax\r\nhzbo7z3wNpjSd/A1KWajuR2hqQtfuDijGy6U2HfZaqlPUJ7YhfAqCSeeOVwX\r\n7Mj9CdS5DMUSKDNpMVXzzNvyxRzZVi4H474sjAIsEGvvay4iPTSPqdWRRejX\r\nRYExLNpYf7uTXyZ8tx/AkZJz/+HZcOl72AK/zPwYLttioxIy90j5EWcNhjvw\r\nPL2Boe7QmeFzOs6D5Psb+v1cXHQ4zZ03G/pr/ZyJprL2gVwci4OgAXwW1EXM\r\n0HPW/3VfJcAVFOMgooZgYY38w6cGEw950YEcrwuV0FijCOtqRdrHZTqe23Cp\r\nMSp8fDfSRU7Hi2KVxRjC2NMYsR3bmrGJyKZvcZ0GdmxAYjr8z0J1IewysTQC\r\n0DuzLla9fhPxdF9sdIFDq2q8g4meKobzGBcjeQD6yl3DuZo07AiM4a3B8BwU\r\nXY8N83+CNag7yNVsCUqe6Zr4Pjn5Llbd/riZFqXhMx/P+jkJD+KsVukwyu3o\r\n4+ccMNrVuKrwOgcixBHliTjZfmDegnoyiF4/1JL+ikkEORpK9Y0BDt/Q/brG\r\nNnJAA6fhprtOCNdOCQdKkGgwO9ewz1o5O2JId/rqFt3fktYBAiWXMYhXXRJ0\r\ncvsKJ6icgVNwGTAXpeVi+MV/vRqzTOgot48=\r\n=ltO7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.9_1676828746866_0.09457387355958558", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/linux-mips64el", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "14f7d50c40fe7f7ee545a9bd07c6f6e4cba5570e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-XilKPgM2u1zR1YuvCsFQWl9Fc35BqSqktooumOY2zj7CSn5czJn279j9TE1JEqSqz88izJo7yE4x3LSf7oxHzg==", "signatures": [{"sig": "MEQCIGNhZf8sS1DluJXmI3Xm2xkKtasRpX0pcdx+GYjjWKPJAiBbAhznIFcUVq8bkRmnGJKhGUNyjDYRIe5cd4qTEKHOHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87QKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzjQ//ZiU2bD+ZEr/04Vfzie2TlXsD5mcBMFVonM7nc8TKnEHX4d4R\r\nO9s65N17G8Eq88sU2CZ4ifb+UOIEqUvQ/As9w8TsGcG/5xJCb28yjbEBjaJ1\r\n3SsnUWVN3YN7CTF9DLJPll86Rjc51L04OYNJzEB1xLp/17n3Qsq53JLc7ofN\r\n8gdM8QFKBQnyAnT1gxNhaUrqosP9UUsrxb7fw25PLTO7UfkfHHCxSMjbCQfn\r\n7Ie0oDtS99+JH74qIgv3jXxalJdqRedZBUm0RQOVMoGfCNsLbLhXctT24HY/\r\n0wwaPDrNPQhT236y8TPlrAEqtwPzaf/KaP6OnQZqJzV0yRaLnQ9sI29KlXCH\r\n+SnGFllyl/jS2UA38Dab78qcSRmpGWblvZL5Xt70JTB2BPyWcA3DqNzVn4pW\r\na/+//mmGO+UB6/5W1Oe0/mLNGyFbVlfNqo9UVeuxmZUyWn56VbkJNaZt6BfB\r\n0Y3JKkov2UXr+t/pxoeI/wz6CogXHVbQv4BSPYGidAZ5mA7oEvygQ32OrM8r\r\nxc/xbMik3KLUcc+qwW6j+hJElKE0lHsDZBEd9K1+DG/KhJGyuYbv1jMl28L0\r\nKUUFuJptcDtEGzwvNVRIKtfGC9AP2xl4i9fACbkfm2Ju0upZ9I+NLUtoraXl\r\n21/4DOHxAYkmadOeuwQkrl508T5cTfc9voA=\r\n=lWyQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.10_1676915722241_0.2114148994346141", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/linux-mips64el", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "0e18ca039dc7e4645efd8edc1b10952933eb6b1b", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-cGeGNdQxqY8qJwlYH1BP6rjIIiEcrM05H7k3tR7WxOLmD1ZxRMd6/QIOWMb8mD2s2YJFNRuNQ+wjMhgEL2oCEw==", "signatures": [{"sig": "MEYCIQCOmJ4nurcqb4NbwZEpx2BwH65b3R9gKDysMDBfCVI7GwIhAKtr5UXpMmUwxV0sZTL2HzMUAuuk4PeuZbyBCrxRX5qp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9830918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrczA/+P8qUo1qGbgOx/gKQriSP2qvo2p3w/L/2f4p3T7YI1sAYNiCu\r\nRPXCd6SRCrTPtggrIUOu9dQG63VsxbOn+1IlmPIsPBgkTY20vV8J/R4JvkmO\r\n3sVsQxqVmHhSmXOFyY3dj3u3jmtXZOyAQcIFD5X04M3Nd1W8ioi9xo9xqVjt\r\n6kyXJT2oAePf9blrC+uUmOI/pqG+SIkm8BkKB3weQlNecbJpsUyFkYcGWzdP\r\niLkL9wpYpYnXn0I8rXrJv67KdfSlqbP9Bn+lAgeVkCFPtLnRsNsKbEUqaJ+w\r\n6qE9D/bmMmHiHFjIWgdvtx/lYLR26/eEVRnYijA/cAA3YlucgD+luzCyPOZZ\r\nxksQaQLqr7bviZ8zFmmXfUyximAEXSNX+7uE4VyA9rtDjOArVW9UYEqRzIyo\r\nXo+xZJv/N2Dp/l8WEqmgSP0tbM3J7yAXD2ahFoakmM2/itmFHRJQ+FspkLz4\r\nDy1Mz+kSQnnUuuk30pF1fdxXZvL+/hjQlHxvAU/E18uAb0+DtScBMlH4faKp\r\nJ1nqedQHa9UPE9Z8/yrsQkK4SYS5bOwtdfDwaJLN4F2sfyZhox0dZk126nIR\r\nreMc/iYSifHoLLwVHrofpuS5Aj6H8e8qARwsLQsufM/e2KM7RNz4oyo582ww\r\nVQQg3/uxi/3oiC6dVjoe07wN5X6qEuf6Kss=\r\n=2PVz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.11_1677883248750_0.3809014569556157", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/linux-mips64el", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "6ef170b974ddf5e6acdfa5b05f22b6e9dfd2b003", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-o8CIhfBwKcxmEENOH9RwmUejs5jFiNoDw7YgS0EJTF6kgPgcqLFjgoc5kDey5cMHRVCIWc6kK2ShUePOcc7RbA==", "signatures": [{"sig": "MEUCIQCrMEkZx2qQW26saEp1Q7IYWV6752QeSTzL3P6LqE1QqQIgSB4pxMj5HI02fdkRwpXDcq6ty8tAKP7WgGP6vDSM128=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAYDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowGBAAnUYzm+Cn7GtDDBZdfaNfcoI3o4lHTrEfdRWZ/z9dVzr7MB4M\r\ncog2Q0AH047mkiDWYUM2f8tl6O3v05mNleKZs2rsUskBvDEp//Z02FZTbzqq\r\nf5/qKc3BncrWP3a4opBMddy61QeZTvlFnUrkoE2jAzus8/O4g4mR0rBz4VcR\r\nDMeiei2xvEXK1ghslI1MlgoIcpTpCemRHEJTIhwRcFTWghz7rtJ6qoQqAWh1\r\nTSHSPzi223Q5oAOG7GrjNJNkGp8Jy/V9wEH3StmRkhetnau8/JbAJBPOR1ew\r\n5cn84xrtyjVf29C/CSXiCGIPazh+BUxztPDFUxu7J5A1Ce1AzwBs1uhXXJeF\r\nP/kT8fWDS1b8iV4Uvlft81XzJ7HG8mwBVokjD4Qo+rHG61CLMRuCWHAk3ub3\r\nAgYQVVjs1XdR+bLBMLboOakb7GBJYuq1TrAwih6k6sC3G4HUBWRq8LikGMCW\r\nt+VW7B728JnhByTP/aiPZzlr6IH8BMri8n9q/A3vC/FfW1GOEY44MYV7blyr\r\n/BIOLSt/TjkyPbJo1ceAOyylx7dCSOLqnaUkx2KlbG4GFD/cuTl9nHhiFWKR\r\nM7hwX4FnddIbdpzsSXJLiada8MMdIUDNLmzrdoiAZQIxpFucth01BrP89xs0\r\nkPtjt0HjlGilOSuWIrP2IF4DhAEN92MuDIg=\r\n=2J3M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.12_1679033858883_0.18403834830040156", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/linux-mips64el", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4d80d34c1ab79c57f5df819e45e555a695c840ab", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-d0pnD/j5KKQ43xtSIvOD+wNIy6D/Vh9GbXVRa3u4zCyiJMYWjxkPkbBzlEgNjdDmUM+5gBFen9k7B8Xscy+Myg==", "signatures": [{"sig": "MEYCIQClhXXqWu3mgYrZWjWd6Aepk6S1SAzwJEaQOzB6/6TPeQIhAIf9wfrFf0FVrc6mv8b5V628Zo7WqTyUxwH88CE+atSd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUeQ//WQg+dZZdih6olOB/baI0lNzjhfmhq2lmUBpO6HjJOZCXqUV0\r\n4chkHJRWNimd4bLLOJaqoGaLAzPzr/tnQb4wsVdVT6HsMmjj3a5Quo4CV/ki\r\nQw663x+WxX4BjaKa6gO+sJLAUj1N7AvVVYtYGvzUR3JjY/+cDGvTp9GzgFXb\r\nrhOnGDrMfpE98vMx3iqYhbCAhJT8ZS25i0eRFeRzXHpukVSzVilLQHc8hE3V\r\nW7xwurgiuE/cI5wsi8KWCTzae0xx/Xa4Ut9mgxjycxrY5a9nlvv6LkXr5cun\r\nf9+oU/azPRdscH4qU8HvspdXmHQd8N5pCqRa7/Nz4LpXJP6JoF0t3PmKPJ03\r\nxdY8jse+ZvzFaLAJ4tFgCyBMLIWpUz57ojLA4/hOxD/acZbNGpy6ZrRgKXqr\r\nRE6RGrPe6p8ITipbtsdTfRSA5ZAD5We22zrxld93nTTmC9FhxM6dufClUCaD\r\nO8kD0+VAQ8XbYWXbLgHm64ZBuZeQxf1VL7fkNJgyN0Gvrdj8plyyvdCmU8Dy\r\nLDQMSm1EQpB3FiS24g9owPro7EC3H8XspPdbvuko9DEtM1sZMN4ISrH3GlQT\r\nVDKz2ZdD9UtCrKrHaVol7otIW89f/fuYhnnDvNQWqskPQQufKHCO6JpgphYl\r\nLSTVLsrjcR1xMsatL3G1/6Y32znmdgsJ+5U=\r\n=YRu6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.13_1679684254388_0.9452019785536818", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/linux-mips64el", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "a602e85c51b2f71d2aedfe7f4143b2f92f97f3f5", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-90TOdFV7N+fgi6c2+GO9ochEkmm9kBAKnuD5e08GQMgMINOdOFHuYLPQ91RYVrnWwQ5683sJKuLi9l4SsbJ7Hg==", "signatures": [{"sig": "MEQCIAszpxCwyN5Q6mSEx5kWiEWHOsZj2iX3WaHqZdnDLCZTAiATQ6a8lpObXgohm4Af1YN4xbRD7CLMXp8CTla0kNJddg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtoQ/+IOQbVYVBErQHaebJYr27r518tjoNpMTux2J2YTeF9T5BiDCb\r\n6/cdnB339odMYrPfVUO4md5QMUTk0YrMWwBlh0qFS0glHSgxn4tu2B/btXyU\r\n8cPgiie/qD9DXhzmWizTOq6rq6Rnk4bsmZluhgimOUSvgDRm5sL6Bd9WiZSx\r\n1mkxDAVaLAXjTOoxu/iV0DsJ0DRxWmQCTv9sgwBASYBbbZFB/EXVgQZd0F2o\r\n9v32InaLaTLQ7rxdaYzuXk71PtNwVodq5+g3jZx3GK/5k+wxUYT4yWaOggj7\r\nBS6HnQ+IkvlzPepj7i2KlBRX4lqey9zDbkRdXqd7cRnatgcnQA92JgtAsoGa\r\nFQqlzfnOnEFh2T3mk72LJzB0ljda/9KTMbK++xiPKLUSg+PeqqsovFdpS3sC\r\nHDVHBmXTwNxYJxmeEm0aFal28JyeExCWXJ6PP0Z6XpohVQhx3Eh7+hhF475l\r\nYYoNgB4d+mbOZITu5EeXgPpfp6MYZWGOQJqEGHO2pRdrPyj3bMMJ1Arzw2au\r\nzQlnRlcYxP6xYh+DNas9uB6yAAd7xEAIlDbuDkvgrlRlSuXqLfE3PTuhzbMw\r\nL38JPLY07YNLNmsAZKOLrpPvK8G5KOfLrNC2e99icJVgENLe6e8CkYeVfSZH\r\nhCl0de0ZI3TTPw/EvTS4wBe/aFU8eV5TEnE=\r\n=z9c5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.14_1679798891651_0.24690689028129897", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/linux-mips64el", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "af12bde0d775a318fad90eb13a0455229a63987c", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-ZLWk6czDdog+Q9kE/Jfbilu24vEe/iW/Sj2d8EVsmiixQ1rM2RKH2n36qfxK4e8tVcaXkvuV3mU5zTZviE+NVQ==", "signatures": [{"sig": "MEQCIDF2WPHXRpjy3T23dQL4uzp7FubmyCchBS7hsnY7GBVPAiAlMmEPQksBUBM6bggIT7b2LMA90ADHcU4JIDhdzwdfLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK/NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtUQ//YRjHs3z1z1lWQAFtYujHGVkJ9QCVaz+3Eibd5zsg/wDrjTFf\r\n7AwwRagT8mTAlZ8cAIXJzUh3YeubgPyYxeOi7ZmLGr/aJRZSHJYutcwuW8cC\r\n9xac6ZsJ1N5CW4o4P734W244z5BrwugEwNclojTUZiLdTSK2/zTVoqz45Lba\r\nK8vxRIdOU08TY8jNsxr+5hLZWsdUfDCDJ/wQNexvvBDaS1Rf5AWLgzNLYl3b\r\nBPr/YbKH6NhLSVlPi6EHQhKObeGpFiEAcCV05r12Yw1mB8tjaPKknFvMFzwY\r\nIfvG0ziqyVvIOPT7/n1AXUnIhnsddhGHTUj9EWhfHLJ+D/oy/Sbno7clz3pT\r\nvVGVzHyscjtuXz8XPXYcfm3/O2c2sOtCHGBSRT6wHMOyYB82QeKGKuYQg06M\r\nkRg4Pve5OVPiU1fVY+LaRqn3T9nG9HgnONfY2x7SCUmZEKoe0Gc+93QBMQNt\r\nrWUazPC1OwHZtzXOaiPyJUp472PVwauVc8xJ+BjiszHIuXSS2ijotW/3Dr1B\r\nVfB2ufU8fCGU3f17Zi8PBD+e2L8EyzRT4kKkQRxAso/uox/o1/JyrDcQru5t\r\ncFngRzsDgYD+N160ydNmHn0WdeQY2piqgfx2qxqcGdQ1QEeWvppiu/SNzTL9\r\nCAdaiBUa8/PNILz3uxKiHBY4OgGGaDhvYUI=\r\n=NWvw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.15_1680388045078_0.9702820085542068", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/linux-mips64el", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e1e9687bbdaa831d7c34edc9278200982c1a4bf4", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-UPeRuFKCCJYpBbIdczKyHLAIU31GEm0dZl1eMrdYeXDH+SJZh/i+2cAmD3A1Wip9pIc5Sc6Kc5cFUrPXtR0XHA==", "signatures": [{"sig": "MEUCIQDal1VxJKmBsmvtlHUKJdG/KR3wg9/xeaDzQ6PgWJN0YwIgIgtWogZcAkmX4hqP/sE51hTgsVSr48ajnb0xjWg2M50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5IUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9HQ//S4UyfSrVGGzPAoK0vW/IWXCi1jBzLO469rEtAyLCVZ0D94Qk\r\nqp1tUMiwXub/OEO22wJWCHpwaCBxLFayfHlFh2K4kUXGAk8BKRPK1sQm/5Dq\r\nV0hIfpgMrKkh2vc9k3qCxxwbPJ4yiiZo4RxnBC3oLqt4veTMcBekNlQUb+Jv\r\n3U8D0HHuoYYmrLESDLUDnFmLHtluoyemCwVQir75vsGyb+4Joz2SOznZOdqF\r\nNo1ccivp6puLyRS18XQzlSW3nOhNvlpE/KbYaoJQZHgLdeUJaX/fhpfi3Oir\r\nf25arTylrXrB8FpYAivD8HCHIoHnTyUCGd1T1sYpgjXQu8XOkL0VxSeKIcWL\r\noH1FmPfSYle0zWo0avfg0N5vxjCbrXJwuqe9W2/EHJq+97yn6afioElPc7Lk\r\nn+st2tVV31bLuFci1ye+J+ZPEaNNBXkPA+SHMNjqdvn5f6HvavLAnoZnh+VT\r\n11oj1JyZjv00jeYg4GOh3COXx4Zwe/89xqqBvKD+2QfNWeMOitBK4pVbCXhO\r\n0ZMs7kJX9YkprPni3kaVY/xUHGZQbiTdNSsXyOLRmp7syloCrtZ6vvnbZp+r\r\nQgpnOagheF/kq3NRF11V5DMVkAx40lcBhr0g/5KG52LFoszWadUsTcssFZ5E\r\nYAuVmmDCdnHSiBsJlpoMWBb41mRlxMb8Kps=\r\n=ZhXM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.16_1681101331932_0.38087841258129007", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/linux-mips64el", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4784efb1c3f0eac8133695fa89253d558149ee1b", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-2kYCGh8589ZYnY031FgMLy0kmE4VoGdvfJkxLdxP4HJvWNXpyLhjOvxVsYjYZ6awqY4bgLR9tpdYyStgZZhi2A==", "signatures": [{"sig": "MEUCIEkoCId5PCy1S4CFAHcWfpetbESSiM3PyHMkD05mFyjkAiEA+/YXOlOCuxNlk6lYghcK5Nwy5HIIGpXI0wtTOs30g+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi2A//VBXj+YpQMpo/BS7ZcapQ2XokrGScSqCLynsncg4yP0xteEia\r\nhCoCNMjv0NBfQXQy8R6soRdUYHykEZUOENlCs2KXEcViV69l/uaJ+2xB6Exg\r\ncjNjil7WZCfk1YW1UsKSRJGtYhZ4V9xpQq6YNe2KpZD9MRaWT7OzCDtfWzkN\r\nZs3OlKqRNYVq4mGkSLkTEYEKXjvHgaFNZg8GlHr6Szky9DOgjv5nMuuxGddL\r\nSUHEJO3LzRNydID3J+G04kJ8bMXpaW9UOtQASfNJGeoGaYvL3+ikbxecVKzu\r\n0NYg9XTDtBKPwa5pNeGjIeClZaluPjRwgkmnoOwV2hAFplkX3fP2fnALe7X+\r\nxZZNIWeFkg++ONnBNAr86LZLQyNwVrAouRkuWVaaJ0bSEmouARjgBrryX6et\r\nLMpO4+29NGObaxRbN/yNZ4PxJMpkhmfkFPe/3Djym9P6s/B3BmBvkjNsliu1\r\nm2zJ7sFx4ZB1TH6aPCRf/pyfYT/EXEiZ5EwwuwVc/w8/j73FxLJBw3QJCu1a\r\n3s5FV/V3aSRQt4fKeNcE/37SbFXTiTRHukIlHvq8bJiMddJ3H5BalX51O/vx\r\n98if79F4pGVZ+MajxHI6/GuSf6akoINRPUombC5lDHjUBGK164EiZxlWfIeU\r\ndcU/IaA5pfyXX7+ZS+xEFi6FC9JeNHVeKbI=\r\n=nyFq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.17_1681680242760_0.36756123871443314", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/linux-mips64el", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "9d896d8f3c75f6c226cbeb840127462e37738226", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-oVqckATOAGuiUOa6wr8TXaVPSa+6IwVJrGidmNZS1cZVx0HqkTMkqFGD2HIx9H1RvOwFeWYdaYbdY6B89KUMxA==", "signatures": [{"sig": "MEYCIQCAstRzLF7y1Jp+hyyTfxfiOkEzYSFJa6ERt87g96P/VAIhAPGjrxacBDH5iRvdsZX0bv6MA4Hgvjv53WlVa6ge491d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREaSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC2g//Rk+mYkvJ4fO8zAi7s9XkGSpRcZQs0qS8tPf2uA9kZtSIWWly\r\nhtTHI+2ex0o61qK1Bgbo6Jn3ThWp1y/B4ILU14eo2+4lUT+UGOt8CP3tDT8c\r\n3YxEb3Re7i0CfXSZDAM+4L4mexdHKXiLWzJYjBAMuJqzBK3Oewhc5ruQMm5w\r\noi88s3iM5b6fxNWKIUQEWNK4d9PGd4AwueN569TDQKlIwSoYnT8VEDpPOHqh\r\nbDlUTNwXAE/99DrHTYnC4VpecV1C4QTSVDd4R/WNW2ban3epytDCFCIZd0d/\r\nthAdr1tFvKzfpIcRKOcV1I+SPjfMlQYP2GaE+UWx8ngDTQxm3K9qRnbclt3U\r\n8R4U7kmyouSJS8y4N6VkfU16aOgJRN+DNeCH6YhoFpkwAaeG04MNJLFIdlaj\r\nrz2aZvPPbB0NsFwOxVurORifIpODniolGBkvkBxGeUdNR5wLPiPS2Wf/gak6\r\npIy0H87NpKytF84kALy493VMMWbwAxOPe8IWbQ5uEkbXLTSc15iJ3N4fZOC+\r\nYOQb6wHxPO1JlWQiMnZf+oQhElHGMxiC1Ro3P/pIs96YXETz/i40soQZFbEf\r\nulQAwb8Gzlk6t6rzpx5HgubZOWJwdbN6JcCWqedPqoDX2++dAeP8S0XvkOfD\r\n2Kcg90yDYzulA2owqBBt0eHtz4r17ZNUqv8=\r\n=pFNI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.18_1682196114394_0.5024816200548485", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/linux-mips64el", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "f5d2a0b8047ea9a5d9f592a178ea054053a70289", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==", "signatures": [{"sig": "MEUCIQDeXJTOaHmqTS4llgI5KMh1p3DFaQ1o/OIIt99SEMnmlgIgbJ05FMz96QYHz+g25qf8NEHE66zkO9rjAYzWt6Vq3go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896454}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.17.19_1683936418023_0.2500425217789539", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/linux-mips64el", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "a1e040a758d80dce29368702e6d431a64f9e9ba0", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-Lg4ygah5bwfDDCOMFsBJjSVbD1UzNwWt4f7DhpaSIFOrJqoECX1VTByKw3iSDAVRlwl1cljlfy7wlysrRZcdiQ==", "signatures": [{"sig": "MEUCIDMjX3X0mOJeELC8TcTBf6nKXA5kP2GEcm8FB+c+ze3xAiEAwDaG+EbrHk0FH8+o+6KfGTMhqWe6k8MwPrw3LTAHAUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9896453}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.0_1686345885959_0.8502489771133264", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/linux-mips64el", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "5e01859f27d455582e094bff666836881e4348bd", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-/G1fzmaR5u2S9wgQhiQEhWRct0+GMpuNjhll59uv5Tjojlma9MUPinVnvpw9Re+Idb6gxe6kmzUxFP2YkC/svg==", "signatures": [{"sig": "MEQCIF5BYjRvgRfKI+SNbVDBbF3wRYkP4GzVIUqsdXMX/+fQAiA0mTyCRd+abr8YpHW2EPFOVR4Z9NZM5wA8vxscheBMHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961989}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.1_1686545530515_0.8693069736522903", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/linux-mips64el", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "09034b507c0625d5dfc4026183ac8d3b3d62c0ff", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-WcTbt61+9dREuOFKXac4Qg+3OuRhLxPL9lmkI2P7fGuq/fWS2qq+AvGGVLMyk+OtXGDjyQolcEDeYlRoOmjRYQ==", "signatures": [{"sig": "MEUCIQD9IvS/aDNMoBnrzGdQa3R/FNKGc1MPFk5nUsZOH0ZA0QIgHx5XXfeXeTdMN2ufM7RBqWC5OHLB8XjZC3BQjXCxJOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961989}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.2_1686624072572_0.8750790206568448", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/linux-mips64el", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "088aa0f909509adb5ea086255f7f654ce8d88074", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-CRVkkSXf5GQcq7Am2a2tdIn85oqi/bkjuPvhNqcdeTgI0xgNbqLnEPRy2AEGkRuaJWB5uCX1IC4sqnY8ET14Yg==", "signatures": [{"sig": "MEYCIQCeurpgy0WUUDinZQv7KOpIVU3TSsNzupKGbh5RxOtWOgIhAPukRvak0vYd40ySwLTumkkMXjn7q2BUxQeMTtRj3Ddm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961989}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.3_1686831707611_0.4947895512303624", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/linux-mips64el", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "f57cef70de4147df9efb761ff0056509d8d1ba87", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-LRD9Fu8wJQgIOOV1o3nRyzrheFYjxA0C1IVWZ93eNRRWBKgarYFejd5WBtrp43cE4y4D4t3qWWyklm73Mrsd/g==", "signatures": [{"sig": "MEQCIEnnGPJo6I99oiJYFnfWs39fCUWj+cIggXMSl10Oi70oAiBpr2PXMdQaAxv75I6wic4+SXlRkIVk/RNGFrZ/Mk8H6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961989}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.4_1686929942772_0.6114983524951287", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/linux-mips64el", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "b172cc90b54047f078289d06d539feab9e175dc0", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-DxW4nNDIGbivZxnJD01C5PlwKPpin8YgSwWtToCy4w4lNigT7Iaf5A+wcPT2laibdgbcgPKpPOXUg6RFGTt8xA==", "signatures": [{"sig": "MEUCIQDqrDZGrirqMd92HBNGPkDhMNtsjh6Cb0j7BnDk0qGw1wIgKou690Z/6hiHomuVZO5g1+0lOohpWpcyc88Nx4xZk88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961989}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.5_1687222389151_0.5606201147597827", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/linux-mips64el", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "db30368c6f827f0eb9600a165df4c93d6d8ccee6", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-kATNsslryVxcH1sO3KP2nnyUWtZZVkgyhAUnyTVVa0OQQ9pmDRjTpHaE+2EQHoCM5wt/uav2edrAUqbwn3tkKQ==", "signatures": [{"sig": "MEUCIDf22LV0XgWbYjkZsKdL+vKhg0XZohT4DGk/9kJvkaH3AiEAiNG79iBRa9RNqLP/+IEZKI4iAFzFlVz2wYtgs0vfp34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9961989}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.6_1687303522168_0.6692759161748258", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/linux-mips64el", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "2f27021be14d7dd2a6286996c4337c705a8c2c5f", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-/dKQ3OLr2Tmj0kuf4ZJioD+qnADUEJSBaiuDbk8v5602HnNNBSGHPrEB6S8PSb8y8jWsX9MMnxqk5KpLTf86OQ==", "signatures": [{"sig": "MEYCIQDvoDNY3tjYyIAYrG6PuBPHmYzGA927o14VlKkRkZvcAwIhAOgG/A94buBDUqBNK/DJ8OzL5bbKaBIEr+xeXL3tExVt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027525}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.7_1687574812483_0.5171346534465977", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/linux-mips64el", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "f644e8bfbffc75aaeaf8efa9e9d67dbf22a3605c", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-9vIq+bR1PYqTV0Ea38a9h/r2xAecC56eQemOBXyzM3jQ9Bo8f3Q9On7mpiDrXich1eQuo9nna0ZBcaIJxaO58g==", "signatures": [{"sig": "MEUCIQCBtm2Py4hjZvTcd6rfcIRoR36dwIMulhu01Zz5faqV4QIgZP8wHuQ2rXrkoFv9It/JZmpEMkqikKtFoJtq58LmLr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10027525}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.8_1687663180442_0.9445812293969462", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/linux-mips64el", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "cc734a77fa17118060dc45e9c5500626b5ba72e7", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-Ymv4j25ie7mVEVlcThnOlRVvqDSsj22MJBH31QGMsyA0dUwReqCg9yNqRM2Dh8QHDRO2UrMhGmiL6BaTdBWlQw==", "signatures": [{"sig": "MEYCIQCNrCPhJuNFzp76Jp1IYUv7xMBLXCnyuxrhcM22i8dTpwIhAOSpbiWvq2xgx4u8JcRtBKchnGKYoGe1kMIQz+oOCc2T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093061}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.9_1687757329656_0.4954325028361515", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/linux-mips64el", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "99801b17fe2bffcce5cf9375731adf3025e7aee9", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-+dUkcVzcfEJHz3HEnVpIJu8z8Wdn2n/nWMWdl6FVPFGJAVySO4g3+XPzNKFytVFwf8hPVDwYXzVcu8GMFqsqZw==", "signatures": [{"sig": "MEYCIQCClrbkTJuCe4sCcP3vcD7xF5yJ133oDJZ5GBEqaKg9JgIhALJywgl8GYhClWugm8dxsMOPC0wPYVLp75OvTScw9e/C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.10_1687814452702_0.2545357022381647", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/linux-mips64el", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "a3125eb48538ac4932a9d05089b157f94e443165", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-qVyPIZrXNMOLYegtD1u8EBccCrBVshxMrn5MkuFc3mEVsw7CCQHaqZ4jm9hbn4gWY95XFnb7i4SsT3eflxZsUg==", "signatures": [{"sig": "MEYCIQDNYtR0eFHTzob5OU8saDsCSMAPGVJbnp/srGydMV9hmAIhANPtWTJSSCE5dDgsPqsyLOKXwTDQxKMxbV5GB3xpyI99", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.11_1688191463988_0.6072649912046657", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/linux-mips64el", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "2313a1b0528ebe24d1c5eab010e0e2427b54ca24", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-wHphlMLK4ufNOONqukELfVIbnGQJrHJ/mxZMMrP2jYrPgCRZhOtf0kC4yAXBwnfmULimV1qt5UJJOw4Kh13Yfg==", "signatures": [{"sig": "MEQCIA5cT73d2E47dEDnAFEo1Ip1WDHxZcQZzRd7WG289G+mAiBkihwGxQjVDXRDukH6lfuyXdQK60y40srsTeRp3IxQhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.12_1689212078662_0.9087206560404231", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/linux-mips64el", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "2123a54b49ddc1a1dff057bba8a9a5e9f26e5009", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-6GU+J1PLiVqWx8yoCK4Z0GnfKyCGIH5L2KQipxOtbNPBs+qNDcMJr9euxnyJ6FkRPyMwaSkjejzPSISD9hb+gg==", "signatures": [{"sig": "MEYCIQCgxIacP/wiOOwTYEnjFpdNzj5qQLyO//c/vPcvGq/VvQIhAOWU/26a8QCJIx4QpgtWai8C4qKHqby1gi7AcTLNf4kG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.13_1689388663322_0.15597323079030678", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/linux-mips64el", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "c635b6c0b8b4f9b4bff3aaafad59fa8cc07b354a", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-qn2+nc+ZCrJmiicoAnJXJJkZWt8Nwswgu1crY7N+PBR8ChBHh89XRxj38UU6Dkthl2yCVO9jWuafZ24muzDC/A==", "signatures": [{"sig": "MEUCIQDsyMZKmZYcFikbHYST4tq95eKqHjr9Ueum42HP2IkvlAIgTKV0Tp3XBld848RZbRhXCxesUwM1f4LlGojinXaegyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.14_1689656448548_0.6046704966682412", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/linux-mips64el", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "b63cfe356c33807c4d8ee5a75452922e98502073", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-b/tmngUfO02E00c1XnNTw/0DmloKjb6XQeqxaYuzGwHe0fHVgx5/D6CWi+XH1DvkszjBUkK9BX7n1ARTOst59w==", "signatures": [{"sig": "MEQCIC8hodWsJAG5fKNq4fQ7fqiVZ2/mCIVNxmlhdREJoIdgAiBm6qwj1b9qjnZTPMF9z8Nhtg8vL3kR9bpmR5OS43n4Vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.15_1689857638453_0.2265844447233658", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/linux-mips64el", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "2d47ace539257896865d243641bd6716684a1e82", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-YMF7hih1HVR/hQVa/ot4UVffc5ZlrzEb3k2ip0nZr1w6fnYypll9td2qcoMLvd3o8j3y6EbJM3MyIcXIVzXvQQ==", "signatures": [{"sig": "MEYCIQCmxEmm58Fc4HSvZXgfvtOfb0R0B9gJuFaQeb5OhVUoUwIhAK3N9IvIwcGb3lHK2jmXJPWOqq4cnpAZlAP5OZucczQ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.16_1690087713829_0.9128691428177766", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/linux-mips64el", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "c367b2855bb0902f5576291a2049812af2088086", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-e0bIdHA5p6l+lwqTE36NAW5hHtw2tNRmHlGBygZC14QObsA3bD4C6sXLJjvnDIjSKhW1/0S3eDy+QmX/uZWEYQ==", "signatures": [{"sig": "MEQCIDnyhj1TmI0dWCfdMKZmbEeQQ3roE5Ot6P5IjVwBUTSJAiAERAaWYE5tP42Y/leuYmAd9X3w81uN3eZlAUt+DcQeKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.17_1690335683103_0.3536373150588856", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/linux-mips64el", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e6525b60ae9d8c3bdc652a773e6ebf66caa3fdd3", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-dLvRB87pIBIRnEIC32LIcgwK1JzlIuADIRjLKdUIpxauKwMuS/xMpN+cFl+0nN4RHNYOZ57DmXFFmQAcdlFOmw==", "signatures": [{"sig": "MEQCICLgsuvW6cmCATx/f46qYMQQFLUwARiiDVTYcrIq3DHRAiBnH7Vff92kaTe6Yjiw9PS8O0T6gj4hNLys3hIKhNeRrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10093062}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.18_1691255213726_0.5002103818144337", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/linux-mips64el", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "b25b352e7645885fa1d01182116c506a78fe4733", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-vSYFtlYds/oTI8aflEP65xo3MXChMwBOG1eWPGGKs/ev9zkTeXVvciU+nifq8J1JYMz+eQ4J9JDN0O2RKF8+1Q==", "signatures": [{"sig": "MEYCIQCAPgS8Ul6QX1Rg4CcwTSbEmqvzsEVX5HZusFHjozyNcgIhAPgF3kJagtNYOqGmxtFmU/kcgJ7Lsnh1/85cYdhdBquN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224134}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.19_1691376707490_0.7786440100971428", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/linux-mips64el", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "eeff3a937de9c2310de30622a957ad1bd9183231", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==", "signatures": [{"sig": "MEQCID/beT4wG9EUoFItW2qcyVEMQHmmufdHph/3FVsT4B6FAiBMEhSmuI4CMWJUb5qApNkF0FgC0EIlIh6ueJV0J99aAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224134}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.18.20_1691468138502_0.07065505528481864", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/linux-mips64el", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "a918b310f9bf31fced3853ca52fee6e7acc09824", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-bZGRAGySMquWsKw0gIdsClwfvgbsSq/7oq5KVu1H1r9Il+WzOcfkV1hguntIuBjRVL8agI95i4AukjdAV2YpUw==", "signatures": [{"sig": "MEUCIQC2/gsLPA80943+tD1BfPYzCeLitnFh3yC3QRMfM7RhpAIgLcFqtKKcWxFQ14hBrg7Xgthu/NFlIFR8TUCEb1J0JkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224133}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.0_1691509978807_0.13495820040375062", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/linux-mips64el", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "92b242fde2d7d9875d3aa249d57f3508f9058934", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-Bzmv6rRMzR4ErG2k/jwfj5jKNzVMVEI1tThuirFdAoE+duUv+jlDnlwxsN3s1eqMzADTOV2sSIcUUOfgv++Dgg==", "signatures": [{"sig": "MEUCIQD/a1YgyW8OJ3T+1Tw9AIKRZ4j3FPhq0UhX2nH6H+FzgQIgBnKrvoaio77VltCkcp/E8L5DaBbrOsdjCDZcKYcwE84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224133}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.1_1691769480192_0.46398715305302085", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/linux-mips64el", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e0bff2898c46f52be7d4dbbcca8b887890805823", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-KbXaC0Sejt7vD2fEgPoIKb6nxkfYW9OmFUK9XQE4//PvGIxNIfPk1NmlHmMg6f25x57rpmEFrn1OotASYIAaTg==", "signatures": [{"sig": "MEUCIG1Tj0WXYpBHhAn3KWZ3c3G1mUQFgzotsLELVz7wNgXQAiEAgGGlvx1DL3574DJ2fG3rWBVOY7uJCWozMWzx+rrICgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224133}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.2_1691978328695_0.7597975076633559", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/linux-mips64el", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4530fc416651eadeb1acc27003c00eac769eb8fd", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-gy1bFskwEyxVMFRNYSvBauDIWNggD6pyxUksc0MV9UOBD138dKTzr8XnM2R4mBsHwVzeuIH8X5JhmNs2Pzrx+A==", "signatures": [{"sig": "MEUCIQDa6V/LeH+wImjQNRxpCH4WmPmLR6wb2sj4BVTYwPCnxQIgNFCseSpsXP6TAWS/5V1nYcStK0AXmOGCP0yUQY5iCxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224133}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.3_1694653969506_0.4711491238783627", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/linux-mips64el", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "76eae4e88d2ce9f4f1b457e93892e802851b6807", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-keYY+Hlj5w86hNp5JJPuZNbvW4jql7c1eXdBUHIJGTeN/+0QFutU3GrS+c27L+NTmzi73yhtojHk+lr2+502Mw==", "signatures": [{"sig": "MEQCIG7CuT7azeb2FMozOVS7SXh1LEzReL4+OlgVi194rgV4AiAkhmYAZula0xEwZWfsxim9hurBMGAGlDGaJXp/7MXFiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224133}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.4_1695865706085_0.3343881594497824", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/linux-mips64el", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "ae36fb86c7d5f641f3a0c8472e83dcb6ea36a408", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-kcjndCSMitUuPJobWCnwQ9lLjiLZUR3QLQmlgaBfMX23UEa7ZOrtufnRds+6WZtIS9HdTXqND4yH8NLoVVIkcg==", "signatures": [{"sig": "MEUCIC0H32oYrg6IrJYqJ0vFhbrAsOrf1lz8sqhgvVsoP7gKAiEAgV7CnFw2vBbYBy2UA7OYj4yzSWPMh7750+lLfe2JaGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224133}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.5_1697519465890_0.0782724711249454", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/linux-mips64el", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "5a922dad90fc8a83fd0631c136b46128153ffb6f", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-8tvnwyYJpR618vboIv2l8tK2SuK/RqUIGMfMENkeDGo3hsEIrpGldMGYFcWxWeEILe5Fi72zoXLmhZ7PR23oQA==", "signatures": [{"sig": "MEUCIEpbXTgo+OQUhTLLchDdycggn/uN7f2CxfvUCUdYYtRHAiEAoC69zlxXfbsaggGDGTaAp8r5ofkKfMmA3n0hUeAzk0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10289669}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.6_1700377922486_0.21558074126556126", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/linux-mips64el", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "f63c022a71a3d70c482d1943a27cb8997021e230", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-DzAYckIaK+pS31Q/rGpvUKu7M+5/t+jI+cdleDgUwbU7KdG2eC3SUbZHlo6Q4P1CfVKZ1lUERRFP8+q0ob9i2w==", "signatures": [{"sig": "MEQCIE2nnW1U0kLxtA4gBF/MQfO3df3TMhlBzx0NkGgVB1nJAiAQOz8b6/ncrksdEmDZIM8h1o7qHl67EXA8P+ap38QzKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10355205}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.7_1700528510475_0.12589946005506336", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/linux-mips64el", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "1359331e6f6214f26f4b08db9b9df661c57cfa24", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-Wy/z0EL5qZYLX66dVnEg9riiwls5IYnziwuju2oUiuxVc+/edvqXa04qNtbrs0Ukatg5HEzqT94Zs7J207dN5Q==", "signatures": [{"sig": "MEYCIQCYFTLE0hge7P+7eAZ2jBM69+gd0NYwDwUeREzsQO6icwIhAKSnhGlbRHwJ6Cd97IOcvUyzH/7Tg6ItHqRiSTINqabT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10355205}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.8_1701040114506_0.378234131872607", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/linux-mips64el", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4c2f7c5d901015e3faf1563c4a89a50776cb07fd", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-jg9fujJTNTQBuDXdmAg1eeJUL4Jds7BklOTkkH80ZgQIoCTdQrDaHYgbFZyeTq8zbY+axgptncko3v9p5hLZtw==", "signatures": [{"sig": "MEYCIQDP/kiP3bZsAqjdW4qvO8+CBEU9/wAKgVbuzZFF9AtixAIhAIj+nILl+MpBMX4y7xDVomlGNt8iOMZwLeCfoFncbnYC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420741}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.9_1702184992508_0.8960000753743833", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/linux-mips64el", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "b011a96924773d60ebab396fbd7a08de66668179", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-J4gH3zhHNbdZN0Bcr1QUGVNkHTdpijgx5VMxeetSk6ntdt+vR1DqGmHxQYHRmNb77tP6GVvD+K0NyO4xjd7y4A==", "signatures": [{"sig": "MEQCIGGWy8AQHP1UHzUVvthw+zucMedoZ972uY76GdDLlLquAiAmqKBetbuVWXLmMzdKV24RWTEwnWSi/9IVO++jIC+Ugw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420742}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.10_1702945328885_0.2794207256396415", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/linux-mips64el", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "38eecf1cbb8c36a616261de858b3c10d03419af9", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-B5x9j0OgjG+v1dF2DkH34lr+7Gmv0kzX6/V0afF41FkPMMqaQ77pH7CrhWeR22aEeHKaeZVtZ6yFwlxOKPVFyg==", "signatures": [{"sig": "MEUCIQDUhD2I9eVHRx41vKnuWQ+BYkSD2atptkS+i2SoKybbYgIgPjV5gXqIyLIeqCPABz4Tde8EOlC1S61usqs7R/AnSxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420742}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.11_1703881957804_0.5894200504820573", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/linux-mips64el", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4ddebd4e6eeba20b509d8e74c8e30d8ace0b89ec", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-fEnAuj5VGTanfJ07ff0gOA6IPsvrVHLVb6Lyd1g2/ed67oU1eFzL0r9WL7ZzscD+/N6i3dWumGE1Un4f7Amf+w==", "signatures": [{"sig": "MEUCID52MuEDd+sv5E2ZpgHfXfgu+E4kEi3dUfF0e5N5z4gIAiEAxP11tqCk/oEGV+PgB/sUG1Fh25FkSrKIP89W5AGvsSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420742}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.19.12_1706031662307_0.3148366469510877", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/linux-mips64el", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "7ca1bd9df3f874d18dbf46af009aebdb881188fe", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-9Sycc+1uUsDnJCelDf6ZNqgZQoK1mJvFtqf2MUz4ujTxGhvCWw+4chYfDLPepMEvVL9PDwn6HrXad5yOrNzIsQ==", "signatures": [{"sig": "MEUCIFSWx9WnLqgV+zbVvL9y+NGL+VvrdOIaIn9G2fzgXF7eAiEArR1+ZAS1lG2B6L8+b3fR5uroBCF4UtcfvjCLeelHvls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420785}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.20.0_1706374202133_0.4502385557758781", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/linux-mips64el", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "c44f6f0d7d017c41ad3bb15bfdb69b690656b5ea", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-TgFyCfIxSujyuqdZKDZ3yTwWiGv+KnlOeXXitCQ+trDODJ+ZtGOzLkSWngynP0HZnTsDyBbPy7GWVXWaEl6lhA==", "signatures": [{"sig": "MEUCICMn9ES+DOXgbErFBiIoF5cCkJmsuj8fzkS3p2Hcek+DAiEApep6Oc1G4TWdoFJvDpmaQ9Y1haNR90pDniSWnQEpZCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420785}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.20.1_1708324754212_0.6002569179712993", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/linux-mips64el", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "d08e39ce86f45ef8fc88549d29c62b8acf5649aa", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-4BlTqeutE/KnOiTG5Y6Sb/Hw6hsBOZapOVF6njAESHInhlQAghVVZL1ZpIctBOoTFbQyGW+LsVYZ8lSSB3wkjA==", "signatures": [{"sig": "MEUCIQDjPvKVW0+AVPw/xCMYimQi1GlA6g2R+Mny+kbYs+GajAIgCoJBjfikR2rcs4KjJCv49UMYkW+BLa+e+10/pBHLuYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420785}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.20.2_1710445808841_0.6725050842521323", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/linux-mips64el", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "38ee99147956b924345c79b294d31a9cb13f9b21", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-lUvMkXlUMrx5vnspMWohma6vuWh+Z/mPV6DdbXW07fNgF2Tlg6SLSqqzDXv5XYV4og5awNFYcPXpgqOVsqdx7Q==", "signatures": [{"sig": "MEUCIAwdkXf97c5M6MXhTWX3HRFekStsOLn0hWY1lhWB+wLCAiEAwAXU8hqIAavJF6X4lEJwR6GLyrw+EqEjzURHBJj822c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10551857}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.21.0_1715050371166_0.8510390925017624", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/linux-mips64el", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "754d533a4fef4b0790d82bfe1e82d6876f18370e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-YCKVY7Zen5rwZV+nZczOhFmHaeIxR4Zn3jcmNH53LbgF6IKRwmrMywqDrg4SiSNApEefkAbPSIzN39FC8VsxPg==", "signatures": [{"sig": "MEUCIH0Fz6rawe+ajNfwqAz9TSlJySM/9eGpCFjgF8raS/FsAiEAlFWImCIrKYSVbpt3/YEsDbI11wVoqxaLXWnydR2Kanc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10551857}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.21.1_1715100959020_0.8053919473130169", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/linux-mips64el", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "0adac2cc3451c25817b0c93bf160cd19008ed03a", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-9siSZngT0/ZKG+AH+/agwKF29LdCxw4ODi/PiE0F52B2rtLozlDP92umf8G2GPoVV611LN4pZ+nSTckebOscUA==", "signatures": [{"sig": "MEYCIQCTGcseU7JQEt8hSpNekgd+mnmsP7VZ8ctJu4FjPvR4AgIhAIbzS24qRqU965OPhSlgLEh6Y0nI+t4huGK2TF7Zd1K4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10551857}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.21.2_1715545996862_0.8183283325181565", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/linux-mips64el", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "b97543f3d8655365729f3702ed07f6e41be5e48e", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-5rMOWkp7FQGtAH3QJddP4w3s47iT20hwftqdm7b+loe95o8JU8ro3qZbhgMRy0VuFU0DizymF1pBKkn3YHWtsw==", "signatures": [{"sig": "MEYCIQCuEqIAJ3VkuAmCgsENElD7sPbh7pkY146TU5cDr7BK3wIhAI57MhGx51aXi8YwcCRE0BZt4x5wbdNc78sDyxGSYsdG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10551857}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.21.3_1715806376067_0.2603946041485188", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/linux-mips64el", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "fec73cd39490a0c45d052bef03e011a0ad366c06", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-qtzAd3BJh7UdbiXCrg6npWLYU0YpufsV9XlufKhMhYMJGJCdfX/G6+PNd0+v877X1JG5VmjBLUiFB0o8EUSicA==", "signatures": [{"sig": "MEUCIEe78e8Zy6QWKp1H2EBGu/2Qnx+djzkPnb+h0S6V69JYAiEA9jAi8Q+ktAWtypIB7eLkCd6OVYtE7rt1zpuQIF94ZFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10551857}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.21.4_1716603072164_0.9493535193966074", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/linux-mips64el", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==", "signatures": [{"sig": "MEYCIQCZONYrNRXrx3SsX8GHcRNawX6l3T/f5FsDPO0gF4XFZQIhAO/ZS2guNZxNnXBXRXxmTJ3Bydbt5ct/5QR1j0l3EvMp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10551857}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.21.5_1717967843932_0.29646386569534", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/linux-mips64el", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "3a2877a78f6719e5eed4cfdded5121c5ab9305a4", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-hgrezzjQTRxjkQ5k08J6rtZN5PNnkWx/Rz6Kmj9gnsdCAX1I4Dn4ZPqvFRkXo55Q3pnVQJBwbdtrTO7tMGtyVA==", "signatures": [{"sig": "MEUCIFWnuHJrim/r1j4WZsOQOQTXpMGcG1WOfFN2oTCCrS2LAiEA3JM8O9Gkwom73U/ffHWAqo6CcE/HSVi9EJo3XRYrF7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10814153}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.22.0_1719779894914_0.3976880878393161", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/linux-mips64el", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "9a57386c926262ae9861c929a6023ed9d43f73e5", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-J9rflLtqdYrxHv2FqXE2i1ELgNjT+JFURt/uDMoPQLcjWQA5wDKgQA4t/dTqGa88ZVECKaD0TctwsUfHbVoi4w==", "signatures": [{"sig": "MEYCIQDy9i8EpJx/jIT1KVQxEMKwF97QonDM8FkEr3ZzRBslxAIhAMLtb3fLS+Bcq/2BZwYU8HcJqdctgH06dOSHBmLc8zw4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10814153}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.23.0_1719891249876_0.8034291736449752", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/linux-mips64el", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e11a2806346db8375b18f5e104c5a9d4e81807f6", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-nrFzzMQ7W4WRLNUOU5dlWAqa6yVeI0P78WKGUo7lg2HShq/yx+UYkeNSE0SSfSure0SqgnsxPvmAUu/vu0E+3Q==", "signatures": [{"sig": "MEUCIQDURb13M1wFXhL9iXQKJhLL+nlJOKaExVyX1Aoo3ZETGwIgTGDaC1Xov1Yerj/Pu7bbxfGs84kCyGUVGVWzl8You0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10814153}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.23.1_1723846423936_0.9259430237300357", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/linux-mips64el", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "3f720ccd4d59bfeb4c2ce276a46b77ad380fa1f3", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-hIKvXm0/3w/5+RDtCJeXqMZGkI2s4oMUGj3/jM0QzhgIASWrGO5/RlzAzm5nNh/awHE0A19h/CvHQe6FaBNrRA==", "signatures": [{"sig": "MEUCIQCBzZSgRnYxN73UYppr+AVsV0d81bg8oK+N7lF9MQcoGgIgKjHHFctSRG3OtXqH4fPcm+3IriPhhSVo5B3etECDqm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10945225}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.24.0_1726970810850_0.02294302588147845", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/linux-mips64el", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "e875a73cb836e82e167346545a3a62fd031c222b", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-X35vI7EufAX17Nqo6XoD89/HSlPJUB5zJ1UKeTiGOLXpOaz7zo+t1trSQOoq2Gr8usOX++S77VUw6L2wTMc2cA==", "signatures": [{"sig": "MEUCIQDnmRC60jr02IzeJd4l/Xmy+AFP8sECEycBnfJ6Swr8TQIgW4xNF0T1I0zBy5CIeEphK/NaCIO+7uoMpxRXh0HTFmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11010761}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.24.1_1734673315723_0.5525950018384447", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/linux-mips64el", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "0846edeefbc3d8d50645c51869cc64401d9239cb", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-iMkk7qr/wl3exJATwkISxI7kTcmHKE+BlymIAbHO8xanq/TjHaaVThFF6ipWzPHryoFsesNQJPE/3wFJw4+huw==", "signatures": [{"sig": "MEUCIFqqw2yQdU8YoQQN6OpbD0vKIjR9tw0RiQk1EoBQ8WBkAiEA/9FyhthnnGteDh3HxKe6jHe7fivA6Nj8uejxW8dTvjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11010761}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.24.2_1734717435809_0.44230976956384027", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/linux-mips64el", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "4cababb14eede09248980a2d2d8b966464294ff1", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ==", "signatures": [{"sig": "MEQCIGNSBtU2ZVPRzhOyFft5d43SzKibf4rDuateb5i+Xn9MAiB0whI+PX2tdzPfp/c3Jls/CVmTS7arjW9XciYoz9/0Hg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11076297}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.25.0_1738983769980_0.5024262794286167", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/linux-mips64el", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "530574b9e1bc5d20f7a4f44c5f045e26f3783d57", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-1osBbPEFYwIE5IVB/0g2X6i1qInZa1aIoj1TdL4AaAb55xIIgbg8Doq6a5BzYWgr+tEcDzYH67XVnTmUzL+nXg==", "signatures": [{"sig": "MEYCIQDVNt47H9j2Z7O4iOPeliMTXqD3/5lzydcGgg0BzP1/ZAIhAMIH6iJAaz1dalIDAUit+RS2kSAl+a6dan9v8XdLW30R", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11076297}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.25.1_1741578362998_0.6574006986815921", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/linux-mips64el", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "afbe380b6992e7459bf7c2c3b9556633b2e47f30", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-hDDRlzE6rPeoj+5fsADqdUZl1OzqDYow4TB4Y/3PlKBD0ph1e6uPHzIQcv2Z65u2K0kpeByIyAjCmjn1hJgG0Q==", "signatures": [{"sig": "MEUCIBJDsMZ61H8AzFn8PetbsqmehHyYDMq5G08G48Z9VrpiAiEAwlMjUySyajBYf3I2wuzRkpMC5gF+denb1QRMOg5cJvA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11076297}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.25.2_1743356005928_0.5480771659627255", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/linux-mips64el", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "1dfe2a5d63702db9034cc6b10b3087cc0424ec26", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-eRAOV2ODpu6P5divMEMa26RRqb2yUoYsuQQOuFUexUoQndm4MdpXXDBbUoKIc0iPa4aCO7gIhtnYomkn2x+bag==", "signatures": [{"sig": "MEYCIQDA6vxvlinRAaG3VxB7eY2Uly0wO1lhvn+NqIOEXJ8t5QIhAN9sUu3K1PdXhqOI5IgBNEJsohbZjpsnUXnfimNJN7eC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11076297}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.25.3_1745380606058_0.9732459975118783", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/linux-mips64el", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/linux-mips64el@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["mips64el"], "dist": {"shasum": "2a198e5a458c9f0e75881a4e63d26ba0cf9df39f", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-IcvTlF9dtLrfL/M8WgNI/qJYBENP3ekgsHbYUIzEzq5XJzzVEV/fXY9WFPfEEXmu3ck2qJP8LG/p3Q8f7Zc2Xg==", "signatures": [{"sig": "MEUCIQCTyvStlgkAi2IrkvBPpoYWDN3VDaKdqUB1v8+rsby32AIgQfWSK1IcUZ5ZTjEKz7t5JaYUdoWyHNrX1xp+jhfZPdQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11076297}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-mips64el_0.25.4_1746491478694_0.35500361520397083", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/linux-mips64el", "version": "0.25.5", "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["mips64el"], "_id": "@esbuild/linux-mips64el@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==", "shasum": "5cbcc7fd841b4cd53358afd33527cd394e325d96", "tarball": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "fileCount": 3, "unpackedSize": 11076297, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCnGD289WvLM4jZLcj4iaEqH82FYkK0Hoy56ycsTPKu+AIhANcTNwQoAYDsg4pnELOyB1yOYXVA7xzqGsi8egSDC7Kr"}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-mips64el_0.25.5_1748315603487_0.4955395185835516"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:48:47.238Z", "modified": "2025-05-27T03:13:23.923Z", "0.15.18": "2022-12-05T23:48:47.605Z", "0.16.0": "2022-12-07T03:55:30.076Z", "0.16.1": "2022-12-07T04:48:57.843Z", "0.16.2": "2022-12-08T07:00:22.759Z", "0.16.3": "2022-12-08T20:13:38.469Z", "0.16.4": "2022-12-10T03:51:03.034Z", "0.16.5": "2022-12-13T17:48:09.066Z", "0.16.6": "2022-12-14T05:23:44.415Z", "0.16.7": "2022-12-14T22:47:24.456Z", "0.16.8": "2022-12-16T23:39:22.880Z", "0.16.9": "2022-12-18T04:31:55.527Z", "0.16.10": "2022-12-19T23:27:08.663Z", "0.16.11": "2022-12-27T01:39:33.352Z", "0.16.12": "2022-12-28T02:08:52.254Z", "0.16.13": "2023-01-02T22:57:51.004Z", "0.16.14": "2023-01-04T20:13:38.522Z", "0.16.15": "2023-01-07T04:19:39.566Z", "0.16.16": "2023-01-08T22:44:20.039Z", "0.16.17": "2023-01-11T21:58:25.774Z", "0.17.0": "2023-01-14T04:37:24.880Z", "0.17.1": "2023-01-16T18:06:19.023Z", "0.17.2": "2023-01-17T06:40:15.825Z", "0.17.3": "2023-01-18T19:15:01.749Z", "0.17.4": "2023-01-22T06:14:01.245Z", "0.17.5": "2023-01-27T16:38:19.517Z", "0.17.6": "2023-02-06T17:01:12.071Z", "0.17.7": "2023-02-09T22:27:12.133Z", "0.17.8": "2023-02-13T06:36:08.080Z", "0.17.9": "2023-02-19T17:45:47.136Z", "0.17.10": "2023-02-20T17:55:22.504Z", "0.17.11": "2023-03-03T22:40:48.992Z", "0.17.12": "2023-03-17T06:17:39.157Z", "0.17.13": "2023-03-24T18:57:34.632Z", "0.17.14": "2023-03-26T02:48:11.892Z", "0.17.15": "2023-04-01T22:27:25.378Z", "0.17.16": "2023-04-10T04:35:32.246Z", "0.17.17": "2023-04-16T21:24:03.018Z", "0.17.18": "2023-04-22T20:41:54.675Z", "0.17.19": "2023-05-13T00:06:58.346Z", "0.18.0": "2023-06-09T21:24:46.260Z", "0.18.1": "2023-06-12T04:52:10.752Z", "0.18.2": "2023-06-13T02:41:12.797Z", "0.18.3": "2023-06-15T12:21:47.852Z", "0.18.4": "2023-06-16T15:39:03.087Z", "0.18.5": "2023-06-20T00:53:09.371Z", "0.18.6": "2023-06-20T23:25:22.425Z", "0.18.7": "2023-06-24T02:46:52.778Z", "0.18.8": "2023-06-25T03:19:40.677Z", "0.18.9": "2023-06-26T05:28:49.954Z", "0.18.10": "2023-06-26T21:20:53.046Z", "0.18.11": "2023-07-01T06:04:24.319Z", "0.18.12": "2023-07-13T01:34:39.045Z", "0.18.13": "2023-07-15T02:37:43.598Z", "0.18.14": "2023-07-18T05:00:48.828Z", "0.18.15": "2023-07-20T12:53:58.642Z", "0.18.16": "2023-07-23T04:48:34.067Z", "0.18.17": "2023-07-26T01:41:23.413Z", "0.18.18": "2023-08-05T17:06:54.035Z", "0.18.19": "2023-08-07T02:51:47.763Z", "0.18.20": "2023-08-08T04:15:38.765Z", "0.19.0": "2023-08-08T15:52:59.054Z", "0.19.1": "2023-08-11T15:58:00.464Z", "0.19.2": "2023-08-14T01:58:48.959Z", "0.19.3": "2023-09-14T01:12:49.782Z", "0.19.4": "2023-09-28T01:48:26.361Z", "0.19.5": "2023-10-17T05:11:06.282Z", "0.19.6": "2023-11-19T07:12:02.824Z", "0.19.7": "2023-11-21T01:01:50.832Z", "0.19.8": "2023-11-26T23:08:34.773Z", "0.19.9": "2023-12-10T05:09:52.776Z", "0.19.10": "2023-12-19T00:22:09.164Z", "0.19.11": "2023-12-29T20:32:38.066Z", "0.19.12": "2024-01-23T17:41:02.587Z", "0.20.0": "2024-01-27T16:50:02.365Z", "0.20.1": "2024-02-19T06:39:14.450Z", "0.20.2": "2024-03-14T19:50:09.079Z", "0.21.0": "2024-05-07T02:52:51.416Z", "0.21.1": "2024-05-07T16:55:59.260Z", "0.21.2": "2024-05-12T20:33:17.110Z", "0.21.3": "2024-05-15T20:52:56.313Z", "0.21.4": "2024-05-25T02:11:12.444Z", "0.21.5": "2024-06-09T21:17:24.155Z", "0.22.0": "2024-06-30T20:38:15.179Z", "0.23.0": "2024-07-02T03:34:10.108Z", "0.23.1": "2024-08-16T22:13:44.215Z", "0.24.0": "2024-09-22T02:06:51.216Z", "0.24.1": "2024-12-20T05:41:55.976Z", "0.24.2": "2024-12-20T17:57:16.312Z", "0.25.0": "2025-02-08T03:02:50.253Z", "0.25.1": "2025-03-10T03:46:03.242Z", "0.25.2": "2025-03-30T17:33:26.176Z", "0.25.3": "2025-04-23T03:56:46.282Z", "0.25.4": "2025-05-06T00:31:18.973Z", "0.25.5": "2025-05-27T03:13:23.737Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the Linux MIPS 64-bit Little Endian binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}