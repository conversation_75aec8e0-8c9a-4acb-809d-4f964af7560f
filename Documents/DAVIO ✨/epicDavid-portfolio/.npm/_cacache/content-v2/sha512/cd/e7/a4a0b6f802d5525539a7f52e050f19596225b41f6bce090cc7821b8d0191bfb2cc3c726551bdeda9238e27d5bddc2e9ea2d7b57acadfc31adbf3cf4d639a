{"_id": "dequal", "_rev": "7-d1bf1f7c9cefce63437e1dbc2b2e6d41", "name": "dequal", "dist-tags": {"latest": "2.0.3"}, "versions": {"0.0.0": {"name": "dequal", "version": "0.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "WIP", "unpkg": "dist/dequal.min.js", "module": "dist/dequal.mjs", "main": "dist/dequal.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": [], "devDependencies": {"bundt": "^0.3.0", "tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "cefe731a4caae001cc4748d8eb6b7ab5e6a88b56", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@0.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6pLZb9YC+O8c0MEzwcjYR6C2elsM3XTrtsQz0ZvnQ9ZMhZyBXl9bU6sBvpCOJiXeX9IsTixcluAfxvFfstlIXg==", "shasum": "f93cc16c3d8baed18b3d1239f0bff38ec0189b13", "tarball": "https://registry.npmjs.org/dequal/-/dequal-0.0.0.tgz", "fileCount": 6, "unpackedSize": 2340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgLM/CRA9TVsSAnZWagAAKk8QAI8DeggzUr/8kjl5h6yf\nKl2d7cOMjl25l+xcHxpBUsMLO0Nc/6/2QT3vqrrJS/c/qm+ZSyWHVlD8V80V\nxzqk4Px4HT0oRTB7EXuYwZD3R/wJl5C26dfXrDzL0to5kQcydNW4QcI2GOX7\nL8UA9dU6Sq7y8wmUqIJi7FodrdnqCF9fV3uFuxjlpgXguK7cxZ8mPriCoFvx\nmBgmhCZUVjtggs9aG+N9aYFMpK+sbSpXbPHx4VXGcRoEgdi89H6wpsNQAdJN\ngG+qS4/ZR+qTjZ+/Z+fDPwcg/qXS1MHzsEoKEEY0riAO9M5U7T3o7FRZ21h/\nvrisRgf0tbdF2mpMlFnRnhF36B7pBwyLdF+mJrCr/ahaeocKUAdfsG8sQ0Lw\n+xLfyho3amrMjz4hmglAWTEh858IM74wRCcl8CZatSW68hF4psn1Kg2pkqWL\nOWQmiuT30zv274xDJrckyk/3rgaCShdCimBGTmIoFNgxm+EbQ3GvmVEa0n81\nCMWbfFkS6k4rg9HYSVnH0WfJUEuAjsbXkkDZZJOIRdCphO7EZMHSbZeGJWDO\nRAHkQi3GL9Xn6d5OB8kbJzM4jZLc18i1te5i4AmTQ1k8R9G2RqH9mIv5yTTm\nkD4L7FwR8UxZrcUXOpgWnHru+NOubE7dpaPZR0lqEaPmKhKgVVzj4J8iAL9Z\nsAs/\r\n=CGAZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9afn9OMYupLk+QV2GsDX0e4b523r7Tyz5MvEONhy04gIhAKuU+lILuVtxkjBEghbf31ta96M1x4u34zTxNufYwhPi"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_0.0.0_1551938366991_0.24426179303095696"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "dequal", "version": "1.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "A tiny (247B) utility for check for deep equality", "unpkg": "dist/dequal.min.js", "module": "dist/dequal.mjs", "main": "dist/dequal.js", "types": "dequal.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["deep", "deep-equal", "equality"], "devDependencies": {"bundt": "^0.3.0", "tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "546da76f3d11bb3925fb4939126c5b18c574c5ae", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@1.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/Nd1EQbQbI9UbSHrMiKZjFLrXSnU328iQdZKPQf78XQI6C+gutkFUeoHpG5J08Ioa6HeRbRNFpSIclh1xyG0mw==", "shasum": "41c6065e70de738541c82cdbedea5292277a017e", "tarball": "https://registry.npmjs.org/dequal/-/dequal-1.0.0.tgz", "fileCount": 7, "unpackedSize": 5583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgYt5CRA9TVsSAnZWagAAwvoP/0AfuQlPu+qR3ce2/+jf\nODGdR4jUxnKQOgK3RmvrMqOpOs56P65I88/sEsJxyxvn64zKkdOTH5xGjafd\ne2BFQ2OmUTF0IkqRQWnOMcqc/Lfef91Atk8HHfhnp8h0WePIr0Hu9lnKXGDl\nkLBodYQkzmR73T45asO2ZF0lNU1L1hzHf/AVayqaakGsfGS6u8gJK9yCaR+z\n9vNSSIweqOauHMR/HNfJFIuivXX0B65l4IFJi3CxwQAkNAG71LVwNVAWcA+0\nbI5CThZjN+apwYG2rO1mCWqjZ+QBSFu8nnZd1xO39fESBzIF4HfndF12h4Gv\n7lTviAhtUsBOEInJ2czLfRVS+m9gaowfJUNHTsZo3eEx8geQ6aN2i2vUDhXb\niBrkP8MJ2BJNR+eFAsYVEJmrlKI0/HHWStA64gPQ5dwwNPz8Het6esSkea30\n05wPj6WRgi1EVcvzaw3F+OKyHCGA6WEOetsPUEayJLYSgfl273JgBe46Xuvl\njrlLuVFpYa2PDVeq/kexFOKgJDpWqU/FVKTcHscqqyXZ0rNysROo5nnjf7UL\n/sAP9DOVMXA4HUTQfrUW0WJBRf/GxEW10C/jykwfbNkegsWeCySCKqXo6ZkU\nuK1kF95WDzBz3r+oeFds5bKesW31xafpGC2m+Dd5DFX+1MpdozKFLaFsF0N4\nNjFO\r\n=Ln+W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmCw2/wDekG9FZ02XEPkXL4LJv211Gi1NZl5+LxfWcCwIgelezAh+qi3Xl2QNLIA9p9pQIuTPC+6Fbf1tpCJc+Tk4="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_1.0.0_1551993720721_0.41432664629644655"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "dequal", "version": "1.0.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "A tiny (305B) utility for check for deep equality", "unpkg": "dist/dequal.min.js", "module": "dist/dequal.mjs", "main": "dist/dequal.js", "types": "dequal.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "uvu -r esm test"}, "keywords": ["deep", "deep-equal", "equality"], "devDependencies": {"bundt": "1.0.2", "esm": "3.2.25", "uvu": "0.2.2"}, "gitHead": "1e97166cfc11bc285b798615a148eaa7810878d4", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@1.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Fx8jxibzkJX2aJgyfSdLhr9tlRoTnHKrRJuu2XHlAgKioN2j19/Bcbe0d4mFXYZ3+wpE2KVobUVTfDutcD17xQ==", "shasum": "dbbf9795ec626e9da8bd68782f4add1d23700d8b", "tarball": "https://registry.npmjs.org/dequal/-/dequal-1.0.1.tgz", "fileCount": 7, "unpackedSize": 6420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfD0uvCRA9TVsSAnZWagAAaxsP/2NgRIz9rtcndzuLtekO\nmC6vnPtCfgEtlrVHL/zHNnWj/gEl4xdvEzD5t6uvRMQ1ZMRygOZ+gz87ryLt\nmYYuZ7Tc8ncCUfNEJ1YsbtFRJxBq0Jq6q18At5Zrm8F1o/S2ZPWqIRCwhjB1\n986y/VjqcDvDhKjg40k3+J+zBbh0q2BF5ljOWlhfqIAZwgJy4qLzM8cJROeV\nH2+NiP7zIbOLDp/femj8axGbMaIxDdXZRdF4TLyZ6u8ytHKXKB0BSbRW0r9z\nxy0/OcqlGrFxyCRU76JoPvJAXgQebmKqMv6henILnOXEcFjjhY0MIxrG74PL\n8mvu/SNAcVZ8rIsuVn6lsufr3to+AOcpvKCSc5w1SVlqVZZYKj8chea/1xOw\nlXAFbKGEwFrjSmsLFzVp9muIehqJ6b91Kkv46m0L1yFsE3J+w+jE2gC5AXzs\n0FGV7Y/4oneMABD0qQanaRlEMZm+NzLX4W73QcCKkOVTTn2dkPTR1jS6NEws\nV99giwLfJFeYKUrHuNdbPLICKHrzB+SHecb1AszSBi/Ii/zShMJaUov6JS0A\njsGOtDMJkhzIM9g2OUA8kdvsAiK+EwpRTkx7UYucgyrZDjRDnGa/iJQ4i2Fm\ndDsj3QvXVSHvLS+IwCiLH+0JqqvYwPoaVaCRRxp7UkrANgG4IXD3LTdwCCdr\nvWTx\r\n=062p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYdvMtsv/LcymwRPCeLkOM/OnGnHB2ljOFBes7CzLG9QIhAJ3KKxWEF+8LJKF8jzRQpYlZMLdJEOZlKX2tqFSXZg7h"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_1.0.1_1594837934591_0.10306879951704673"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "dequal", "version": "2.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "A tiny (304B to 489B) utility for check for deep equality", "unpkg": "dist/index.min.js", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "uvu -r esm test"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./lite": {"import": "./lite/index.mjs", "require": "./lite/index.js"}, "./package.json": "./package.json"}, "modes": {"lite": "src/lite.js", "default": "src/index.js"}, "keywords": ["deep", "deep-equal", "equality"], "devDependencies": {"bundt": "1.0.2", "esm": "3.2.25", "uvu": "0.3.2"}, "gitHead": "c4381cc7ddce15140d1c7565eba8bda3de700daf", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wPh2J8aSspO/XY8sn31LIBq8CGvG+Gg1zvcBwMzGnnms3XhOxeM7IiV6E/tbW0QHWoFwVktAnZjXv+Qqbc0UTg==", "shasum": "703be76f3f9d6fe447add5a1ba535f249aedbaa5", "tarball": "https://registry.npmjs.org/dequal/-/dequal-2.0.0.tgz", "fileCount": 10, "unpackedSize": 14049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNHpnCRA9TVsSAnZWagAAm/oQAIkdhA1wL1m3dYKddiag\ncofO4ikqVrDs6PpoAaveZDzLGO6vNRXjB9CzVOa1DBdkIcFUXUHzeW62eyev\n0aVlAuVUm/oGOISzymOjYit7RHRtTBKL8N1GsNgxGY0ynJCpRADyP+QznLEA\n43gBaGaMvXroIMQc7049xm1vr6ZsAfdcCazjlf+LJ9G9GszF2h1lj77JzhJj\nkCjblhXEIjt5TORbzK5+3uTCcmbyiY0c8bAgpupUcwpp0T8lg7n0l3unLHVT\n7IVmK0C3atb0dEj99A84h+Z7zAiUlA1rO2EgonLDLqamYSm5A2/lr8C+86dt\nQMUn8tCcBxFPTGRlN/EZKBidQJNg8X5AQGXqPS2rD+hWU7xfLa7fH+F3cWnp\nykW3x+dNiFhp2UMi9CIQx4P3NvjGaQh6CkVOCmwzOqPwjDUeH4cnsFU8zY2k\nSoUhU89eremuof6EFBHId6M7sw/lR8Bc6BV0goZB0MUB229I3KV6elM+h/1I\nBm0e5XRiOPPGbE/QlxXx3q0P7dqVOJM4AhHsuxhTppirK2DZql61LFyS5VES\n+Q43Wh89uIIzKhRToMyPjYMaitUvunMoSgOPFvyRqf475kMrKOn5jk5BXCBD\nfel2SpOMXqTHUtMe1M/lXJhIScPzN3Q51XGZpJhQVtCYqeZ/80+pywmQa1Ns\nkc/9\r\n=0dLH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID8tFHMAGbJELaRcD68tmNaIGOx/8urpYlqRxqPgi/WuAiBKRtk8L2djHvpl/o4R8FDOchfdG7rpapOAyYn/bxKEmw=="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_2.0.0_1597274727399_0.31965522025474535"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "dequal", "version": "2.0.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "A tiny (304B to 489B) utility for check for deep equality", "unpkg": "dist/index.min.js", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "uvu -r esm test"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./lite": {"import": "./lite/index.mjs", "require": "./lite/index.js"}, "./package.json": "./package.json"}, "modes": {"lite": "src/lite.js", "default": "src/index.js"}, "keywords": ["deep", "deep-equal", "equality"], "devDependencies": {"bundt": "1.0.2", "esm": "3.2.25", "uvu": "0.3.2"}, "gitHead": "cc4e212d06f6b7e299408adbed83c4c45ab2b8b4", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@2.0.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-yFMvmBBExR78UdgG0xuHeR105wvqHcvfOFJWuKpveyjw0TipbrxzTiM4RRw6Hb57XA6fv2UgbdCRqLYu+rdDEQ==", "shasum": "ede18a4165063112f1a204c7e117cc6d7a66ca4e", "tarball": "https://registry.npmjs.org/dequal/-/dequal-2.0.1.tgz", "fileCount": 10, "unpackedSize": 14170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOtKWCRA9TVsSAnZWagAAYswP/jQZXH6GC080hDYbuf0p\nPUPPf+957iOHjrpC5vycfvgPfdsCtVNb2NQ1bOf1bJn4GtdHDgjZ9xJny04f\nYh31u7mP74klqhMP0gBDgIrknLNXeNNIQVFrgtm+o4p50WruLbFuhB0o1gKt\nna9tcoIVcolJJWGjIduufEo+qXpXTrqi9+qOa/PacgwB2NZBLDnTyn5IBRTp\nqMRzrBBw1DTRV5x9PMjbVWTopQGWdi26q0Txzkbs8tF8XqV0h79hRUe0737q\nIL5DwOIDGVQZ4gv7Wu+lruyHg744pYVctSXrs8LfJdqJTaaa+BsDn4b0cTw+\nidFjaDrRT6uvRMOteem4OulBhp2EOx15JPuBPrzSYCf6rGuJJe0rov0u+2k+\nAcBFrUFOy+4oUlpNzWOBj7u0IhVH+rmjspsUMco2nDaPq048U7kvNt4Ait6Z\nni/d4/mHCCMVoxxJ6CIfVeWPV5rRFHfcmmoFm1WPG/VEvx1pQs1GOFPoqJM4\nbJ4YrbdTPqDte3NsdJwgMW6Qe5iDwsEAeWgU9mXqNxflP06QP+U0KgsbmzPV\ndv5qtbg7r2QdnF3Uos0/4HBI0DTIpE6wWMXFj8AnsAb+PaVByKKC7tGMwxb5\njKXRqyN9hslDKoVFLOV5azGcWSTL2Xk/fSRmR/YTfBHZcksXSfvv0A1hbxqS\njNGS\r\n=FKN/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgbCNWXSSpln2v6qjALEM5wMsDWoM89u+T8XpromWzCQIhAJjKesoSnz359q0pp/uHuMZZQ7z5eW6jMFbH2ozYQy7t"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_2.0.1_1597690518142_0.17796559741071727"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "dequal", "version": "2.0.2", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "A tiny (304B to 489B) utility for check for deep equality", "unpkg": "dist/index.min.js", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "postbuild": "echo \"lite\" | xargs -n1 cp -v index.d.ts", "test": "uvu -r esm test"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./lite": {"import": "./lite/index.mjs", "require": "./lite/index.js"}, "./package.json": "./package.json"}, "modes": {"lite": "src/lite.js", "default": "src/index.js"}, "keywords": ["deep", "deep-equal", "equality"], "devDependencies": {"bundt": "1.0.2", "esm": "3.2.25", "uvu": "0.3.2"}, "gitHead": "9aa73181ac7e081cd330cac67d313632ac04bb02", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@2.0.2", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-q9K8BlJVxK7hQYqa6XISGmBZbtQQWVXSrRrWreHC94rMt1QL/Impruc+7p2CYSYuVIUr+YCt6hjrs1kkdJRTug==", "shasum": "85ca22025e3a87e65ef75a7a437b35284a7e319d", "tarball": "https://registry.npmjs.org/dequal/-/dequal-2.0.2.tgz", "fileCount": 11, "unpackedSize": 14167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPKmwCRA9TVsSAnZWagAAR4YQAImSgNMqgRgRL4In8Nrl\n9RMZLGtlGnFQ8S/XQnzRLU/TmE063L8wrJxYjlVQeGdArW9XhxwCdL2+CEsN\nnUj5zL0/g9zqIxDEeBmAQa26PwLdQwzxr6lmcv3gcCxDgUkBEAr/buVQslPW\nNIxbZ39lu477sTYnTAvnXyXxfPxGyLKC9AtSDY0nQdDn9YHzreafQBDA7WRp\nvpG4k3g1js7l5LQsj58b0uSaXCBz69/1wLl7cj/FHBm2j7r+Jwmk2V5coaFq\natLxomh3+sQl0IFh18grvRc5wmfzeg+INHtvoSVHyO9Aw3/RW93mi4DmxQhO\n1w0eYPK/SmB305uf/G4ZOA6NH9IIDdP6cix07C5KdQoXiChX0zQY1Wd6XdPD\nOFAb1L02ie7L2ndb4R2HtErsfXkMBeQWFnx069UYf+f1EFbcf7PXsWkjo4bg\nQErxKurg6QVN2fsqWfZbioUYjPrhcJEAQUlp06GFSvzkR3e1eH2T4osyiIM8\nGl1AkCyEHFZYy0maCz0QVaFYYV4+8ThNHPXLqBWF8BR3R9jFGrKLzLAt+zKc\nDFxSG+nY2UKDweu4KtkzJOfoSOs397T6vF7vqoIJHC9Mi9PpFttH/A8Xa2RF\nQuZpMjjmQQxT0647fAB9Xz1cZxsy285EHjLKJUNddsNmr5bXr6q1S7vqu7Ot\nriDb\r\n=YXIl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAPU6gNrpSUP7v/YuDUQJ5/hKytKC7sKqYbla/vHhxHTAiBa7JDOJU+2WeY8JaeSzm7UCg3WDC7DAIir5MGWp9ReCg=="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_2.0.2_1597811120028_0.4877269224774894"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "dequal", "version": "2.0.3", "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "description": "A tiny (304B to 489B) utility for check for deep equality", "unpkg": "dist/index.min.js", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "postbuild": "echo \"lite\" | xargs -n1 cp -v index.d.ts", "test": "uvu -r esm test"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./lite": {"types": "./index.d.ts", "import": "./lite/index.mjs", "require": "./lite/index.js"}, "./package.json": "./package.json"}, "modes": {"lite": "src/lite.js", "default": "src/index.js"}, "keywords": ["deep", "deep-equal", "equality"], "devDependencies": {"bundt": "1.0.2", "esm": "3.2.25", "uvu": "0.3.2"}, "gitHead": "37d4f27de03fa6264e406cd03288cec8255fca4c", "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "homepage": "https://github.com/lukeed/dequal#readme", "_id": "dequal@2.0.3", "_nodeVersion": "18.3.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==", "shasum": "2644214f1997d39ed0ee0ece72335490a7ac67be", "tarball": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "fileCount": 11, "unpackedSize": 14229, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgWgKlZIYPELAoZgvd75QHuBEqzptycatd8f4ZuS/PQAIhAPhLUsj3mbddEtxP/pDnv5pL7jzd13Qyot9JoTMiSlHT"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizLJAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpopRAAmHhHzk2y0ADyGW5AylMlcuu05lzJjyA1wKZcA+l4QQabeSUx\r\nPb+xnee4+iSWaIL0LVNqOrOtOE75C1f+h4v9s4013utIraLKHffPpRz8R7B0\r\nt9zBFRx8v35U89EM932/gHn0zSXJMiNozfboYQEfJBoUZF2mdMsB6i/F7FtW\r\n7GTJ3ChjZBS6ujPFYHNx4FJN1tAP9sjyj2a5zydRyTZZyyS1RbmHwjNw0Fct\r\nGXZ9fwBBagzcHlTohCDy2jIQjj/dwy9NuwTEG9Dzc6b0MiQSp3mZjlmQv4R7\r\nhRF90vmoo3Q20eBBKbPFZxkFJIJRqVVOKjEW7lw/kCA6+xNtSGHG05Q/0Mau\r\nsFEkvELdBOwNbouG3jMWMKNljDv9Z3P12+qQOnf+gQFKQqE/myBzZb7SL3VW\r\ngeobdyO+si7fEbK8kgrGSxNOId4vzZauR7dSF7nzRqDEjNzVkNgxIUmJszEZ\r\nOHPk1pm/5oTSrcwAYh4zo37BqpzQqmqv6fvC1TsEEeJ5IAaGGUojRYdHtaAv\r\n9SOHMuWB/iWoSO+LwSREio+XN4pkJFi5ThGq+Z97KFQTE+TAu2DtjPX/8/w2\r\nQZb8WkBo6pCol2YfB0sYQ/YbX+yMmuLVy1qlYzl2cGA8zWsSF8H3EdChXObD\r\nbrrIpo6Uf04oLrymAbAh9QXRbvKwEyzyVWM=\r\n=SMb/\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dequal_2.0.3_1657582144817_0.4755944407894437"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-07T05:59:26.990Z", "0.0.0": "2019-03-07T05:59:27.146Z", "modified": "2022-07-11T23:29:05.053Z", "1.0.0": "2019-03-07T21:22:00.861Z", "1.0.1": "2020-07-15T18:32:14.741Z", "2.0.0": "2020-08-12T23:25:27.516Z", "2.0.1": "2020-08-17T18:55:18.257Z", "2.0.2": "2020-08-19T04:25:20.180Z", "2.0.3": "2022-07-11T23:29:04.965Z"}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "description": "A tiny (304B to 489B) utility for check for deep equality", "homepage": "https://github.com/lukeed/dequal#readme", "keywords": ["deep", "deep-equal", "equality"], "repository": {"type": "git", "url": "git+https://github.com/lukeed/dequal.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "bugs": {"url": "https://github.com/lukeed/dequal/issues"}, "license": "MIT", "readme": "# dequal [![CI](https://github.com/lukeed/dequal/workflows/CI/badge.svg)](https://github.com/lukeed/dequal/actions)\n\n> A tiny (304B to 489B) utility to check for deep equality\n\nThis module supports comparison of all types, including `Function`, `RegExp`, `Date`, `Set`, `Map`, `TypedArray`s, `DataView`, `null`, `undefined`, and `NaN` values. Complex values (eg, Objects, Arrays, Sets, Maps, etc) are traversed recursively.\n\n> **Important:**\n> * key order **within Objects** does not matter\n> * value order **within Arrays** _does_ matter\n> * values **within Sets and Maps** use value equality\n> * keys **within Maps** use value equality\n\n\n## Install\n\n```\n$ npm install --save dequal\n```\n\n## Modes\n\nThere are two \"versions\" of `dequal` available:\n\n#### `dequal`\n> **Size (gzip):** 489 bytes<br>\n> **Availability:** [CommonJS](https://unpkg.com/dequal/dist/index.js), [ES Module](https://unpkg.com/dequal/dist/index.mjs), [UMD](https://unpkg.com/dequal/dist/index.min.js)\n\n#### `dequal/lite`\n> **Size (gzip):** 304 bytes<br>\n> **Availability:** [CommonJS](https://unpkg.com/dequal/lite/index.js), [ES Module](https://unpkg.com/dequal/lite/index.mjs)\n\n|  | IE9+ | Number | String | Date | RegExp | Object | Array | Class | Set | Map | ArrayBuffer | [TypedArray](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray#TypedArray_objects) | [DataView](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView) |\n|-|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|\n| `dequal` | :x: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: |\n| `dequal/lite` | :+1: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :white_check_mark: | :x: | :x: | :x: | :x: | :x: |\n\n> <sup>**Note:** Table scrolls horizontally!</sup>\n\n## Usage\n\n```js\nimport { dequal } from 'dequal';\n\ndequal(1, 1); //=> true\ndequal({}, {}); //=> true\ndequal('foo', 'foo'); //=> true\ndequal([1, 2, 3], [1, 2, 3]); //=> true\ndequal(dequal, dequal); //=> true\ndequal(/foo/, /foo/); //=> true\ndequal(null, null); //=> true\ndequal(NaN, NaN); //=> true\ndequal([], []); //=> true\ndequal(\n  [{ a:1 }, [{ b:{ c:[1] } }]],\n  [{ a:1 }, [{ b:{ c:[1] } }]]\n); //=> true\n\ndequal(1, '1'); //=> false\ndequal(null, undefined); //=> false\ndequal({ a:1, b:[2,3] }, { a:1, b:[2,5] }); //=> false\ndequal(/foo/i, /bar/g); //=> false\n```\n\n## API\n\n### dequal(foo, bar)\nReturns: `Boolean`\n\nBoth `foo` and `bar` can be of any type.<br>\nA `Boolean` is returned indicating if the two were deeply equal.\n\n\n## Benchmarks\n\n> Running Node v10.13.0\n\nThe benchmarks can be found in the [`/bench`](/bench) directory. They are separated into two categories:\n\n* `basic` – compares an object comprised of `String`, `Number`, `Date`, `Array`, and `Object` values.\n* `complex` – like `basic`, but adds `RegExp`, `Map`, `Set`, and `Uint8Array` values.\n\n> **Note:** Only candidates that pass validation step(s) are listed. <br>For example, `fast-deep-equal/es6` handles `Set` and `Map` values, but uses _referential equality_ while those listed use _value equality_.\n\n```\nLoad times:\n  assert             0.109ms\n  util               0.006ms\n  fast-deep-equal    0.479ms\n  lodash/isequal    22.826ms\n  nano-equal         0.417ms\n  dequal             0.396ms\n  dequal/lite        0.264ms\n\nBenchmark :: basic\n  assert.deepStrictEqual  x    325,262 ops/sec ±0.57% (94 runs sampled)\n  util.isDeepStrictEqual  x    318,812 ops/sec ±0.87% (94 runs sampled)\n  fast-deep-equal         x  1,332,393 ops/sec ±0.36% (93 runs sampled)\n  lodash.isEqual          x    269,129 ops/sec ±0.59% (95 runs sampled)\n  nano-equal              x  1,122,053 ops/sec ±0.36% (96 runs sampled)\n  dequal/lite             x  1,700,972 ops/sec ±0.31% (94 runs sampled)\n  dequal                  x  1,698,972 ops/sec ±0.63% (97 runs sampled)\n\nBenchmark :: complex\n  assert.deepStrictEqual  x    124,518 ops/sec ±0.64% (96 runs sampled)\n  util.isDeepStrictEqual  x    125,113 ops/sec ±0.24% (96 runs sampled)\n  lodash.isEqual          x     58,677 ops/sec ±0.49% (96 runs sampled)\n  dequal                  x    345,386 ops/sec ±0.27% (96 runs sampled)\n```\n\n## License\n\nMIT © [Luke Edwards](https://lukeed.com)\n", "readmeFilename": "readme.md"}