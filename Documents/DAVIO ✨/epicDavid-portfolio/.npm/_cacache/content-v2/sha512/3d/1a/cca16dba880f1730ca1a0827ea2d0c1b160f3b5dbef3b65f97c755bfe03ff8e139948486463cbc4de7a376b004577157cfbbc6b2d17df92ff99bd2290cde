{"_id": "xmlchars", "_rev": "11-2da42b8fe5e0331cac8e7c0694b55c33", "name": "xmlchars", "dist-tags": {"latest": "2.2.0"}, "versions": {"1.0.0": {"name": "xmlchars", "version": "1.0.0", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "lib/xmlchars.js", "types": "lib/xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "@types/chai": "^4.1.4", "@types/mocha": "^5.2.4", "chai": "^4.1.2", "husky": "^0.14.3", "mocha": "^5.2.0", "ts-node": "^7.0.0", "tslint": "^5.10.0", "tslint-config-lddubeau": "^2.0.3", "typescript": "^2.9.2"}, "files": ["lib/**/*", "LICENSE", "README.md"], "scripts": {"preversion": "npm run build && npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "build": "tsc", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "commitmsg": "commitlint -E GIT_PARAMS"}, "dependencies": {}, "gitHead": "ed1f4d9dd02eed035b2cd55b2a7a0844845e0569", "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@1.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-zNReY0hmka9/q+OiTW/3ScE0fa46LRt3wH2yk1NnenSH0GC9+s/C6k6hARRQOlJiBbSDkz9p7feyteXoGZcQvQ==", "shasum": "bc1d86ff7f8377e406befcf9cd0384aedb070c7b", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-1.0.0.tgz", "fileCount": 6, "unpackedSize": 19372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPlvhCRA9TVsSAnZWagAAXGQP/Rv3OU8lazVTTvVY8pJt\n9KwXzqyBnSZSmadQgGx+sO0qBYEKPYJ+5Y6ZRZMJAe0gZ4T17sltA+9AALqI\nnlBKDbseNbRU+Q54K3iFaPOJFzjkZzZRKXOlbJJU18opQGno7EOQyhXLfG9t\n7SRWnz1e46nLshsOjTjmeuOY3sS7Tgfs3JMoSPQlBgLkTgjEakMctlBdObjw\nlN285bIoHdj2zcWFqbwHd2dlyA9ynvD5l7UTAlLupuRAzAJsangN+BAv9tE1\nWehm46D687Ki0Jwo33BcT3ftIvvIT1zo1LE3mS+M/cOpjc9tJ4VA8zwPugte\ngt+fH1cl8qOjUK3WUb5HuAODaMJ+/7Dv94tM3YHitDn8mpLwXZw0yDv+neD9\nrIy7FM1IrsmY9nXyCcfNAB/2yrgBbt05NyxyKWgN3lHwQ5XJ5tqUI1YK2nXS\np9pL1lhAG48KsgeDOGDDmyBER9CQsKFXI3+q0rtjWsQWoHxxbrf3y+eX85n7\nJLwVBWivs01Bl6cGZhoDTJAex+O8YVIkYtUCOC0PGfzoo08ylkFDgMia5K1v\njFt9QNow0ghPfn1FUOTghpP/FHH1sBLwr+Fb744yoTu2a2yAPPg9GlJiELEn\n7KeIXVPP8VQFHh6NwjXQFzwvOk7dREljTrLxneHrFJhVDtAYn3Ps3yvtfb5U\nmGWv\r\n=R68L\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWRMY6wx2GiToIPP2D+ncX1gULhVQnsax+8ZA0uNtcnAIgYIprHUbaJSj0n14srqoY9JdmLuowWh+6Wl3v5M3Evt4="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_1.0.0_1530813409271_0.29344691554904"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "xmlchars", "version": "1.1.0", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "lib/xmlchars.js", "types": "lib/xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "@types/chai": "^4.1.4", "@types/mocha": "^5.2.4", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "husky": "^0.14.3", "mocha": "^5.2.0", "ts-node": "^7.0.0", "tslint": "^5.10.0", "tslint-config-lddubeau": "^2.0.3", "typescript": "^2.9.2"}, "files": ["lib/**/*", "LICENSE", "README.md"], "scripts": {"preversion": "npm run build && npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "build": "tsc", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "commitmsg": "commitlint -E GIT_PARAMS"}, "dependencies": {}, "gitHead": "cb215cb44b6c47fbdbca76b349d95e6ee54af950", "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@1.1.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-cgi/R5rYK7oIQrsBK/I6at51ZAPMx+PGCbZyHkrJFh0OxyS0vlHPdOEt6hg3XebKflF9D0uAGZVBPAzlcfHIPQ==", "shasum": "efe153f1505def1eb9e1887365670262ed4f296d", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-1.1.0.tgz", "fileCount": 7, "unpackedSize": 25698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVebVCRA9TVsSAnZWagAAnCsP/2AtXkxKthvJkn4HVST7\ngfD5yWPWhZ2DR+KQewIvgscjk6HhE2l9qsIVLwsNQuqCdSQWFU1Uhkmx66fR\nK3SZZNAaTxF3+VPuKNdsGW3mwugtKmSL98YiRJWe+Q7SXAWaxN40uqB+ecTs\n0ZSfNoDK/qr3ilso5pgfBJ/ZCFoVKLEDwP9KQGgTCloMaR4p3itOMemqLa8B\n6qpySDx2ZIIbU6NMe6UMLDlVZnIwcFSjWvigKmeMe9YXgzrwOzSvUGy6D8fv\n1UdRaCD25x0QMVpC63AjtqUmBD3+MoNMO+3g5SxIqDihiuPC5rb8d9nqEhuQ\nc98LuTQfse+XAdP3kTyZADBWmj2VtKlSaWb8KlPMDZ9QWljqZZNWdJUOCQuZ\n+r4NFd7xKrfiYcCnV+p84txDqPLpTLwVc6ua+cj8hVly/UNbMeDUwvVDJjqR\nVSAL8Ln00YSvpXnA8X+jjFFpENqWKMwtQIfQoTKky3lhCmVG7DX8hQ4xkmer\nwkoYMtn2Ebt0W6a2g/kXoQXVvi7InQER4Z8mAWxUI7sFSRipw2TVB2z5rEzA\niVce0CcerO5hKCiGr7+0ERDx58HArCJMWUbbaN6zGQlJJunb5lStMWCT/jCZ\nM9ClbhO8juF8lNjKZUxOrqyNSWhaYMkSDA2XLf8Vv7/8b8qOaYi4JtUlZq0+\nnkhI\r\n=s7Gj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhYpD5tSB8OP15c3AYisPRXOKIkRZ97Ge4SFcFw8nPPAiEA31GQ3NyO5wAi57FaKUq0tvaj3gM7eLU3K4ZtXCwKIf4="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_1.1.0_1532356309021_0.7037621772339091"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "xmlchars", "version": "1.2.0", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "lib/xmlchars.js", "types": "lib/xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "@types/chai": "^4.1.4", "@types/mocha": "^5.2.4", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "husky": "^0.14.3", "mocha": "^5.2.0", "ts-node": "^7.0.0", "tslint": "^5.10.0", "tslint-config-lddubeau": "^2.0.3", "typescript": "^2.9.2"}, "scripts": {"preversion": "npm run build && npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "build": "tsc", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "commitmsg": "commitlint -E GIT_PARAMS"}, "dependencies": {}, "gitHead": "5da68a0b6ea1f74f9a27683e6a2091993a4521e4", "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@1.2.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-J9gyyXkeuZGZVqfM3sEZQGIvUP2CoCQCa02aPy+EEQScK6wcoFBj6E1pw/AVi+ZVztIKgMr/DoV9RWhiDsNA+w==", "shasum": "eee758b485f576c7ae942b486926bff533dba681", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-1.2.0.tgz", "fileCount": 7, "unpackedSize": 33608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkQGoCRA9TVsSAnZWagAA2AgQAKAyIneGasHm1v8wi75b\nTgW8IgKH+kOD9j1hIcg3g294sR/tBSaQIisu7CVLjXiMENumJdCwdq7Rqi56\n3Q4+CKBgFhFD2qxj6gXa9g/d4IPLh9mcTB82VlJ/Zz3mje1tNUPzwnLDnkIf\nDtPW7pNQJPiKSt8Pvo+k+i9sx81HPRP9fB+tLsdiQVj103gUtUqc6Zptk3I9\nyMEJwUYngBe5Lgw3upifzsPDmClbBqa546WXVi/1JSCUfZ86a5JOXqKHJ/NE\nn2M35pcw0XDhDeWjfO8aYT8RnKz9jH6OlB8uVlkO1yY/qWc/r1r8QD2MjZco\nsfSdbyyKO1h2+ES4wZimPZslh3eQCsc2GgASqp31z2wFxVfSoDHztI6Ehce2\nC7qVvvgqvd3OZ7Hhd7aGweofjnRQPKZ5V4lIW+Qy0awLSQbRGm2i5OC7LCGn\nfgRIrXq1/D5bjgPjJHnhQCngAHU5OprUBpxFG+5CcXiUMMfH+ylooHlu71Tm\n7IGHIT43AsgQArinTPAI/1sMTjKaNSmHPBNuhrXFnkojBMq+VhgjRIwR3sAG\nsLSrniACvkczM2AAfX3oF9B0asrYKyGAZ4xghhnUrECHahdidsfUfrs4yzC3\nOERJnRP/wM45Ti8/62JPkvtLeVF2DWlEisGUc1YHN/Wa/fkUvEMSCvBGAVfF\nk0Jt\r\n=tOkf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChttuuxoRLYuYlHr80EoRm9jBqemVc8DFewOtQk5fEZAIgYkExjWRl1cmbQJMwGqAPsoZTK/VKuFpv2iMYWWD8C8c="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_1.2.0_1536229799770_0.7042027254659966"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "xmlchars", "version": "1.3.0", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "xmlchars.js", "types": "xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "@types/chai": "^4.1.4", "@types/mocha": "^5.2.4", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "husky": "^0.14.3", "mocha": "^5.2.0", "ts-node": "^7.0.0", "tslint": "^5.10.0", "tslint-config-lddubeau": "^2.0.3", "typescript": "^2.9.2"}, "scripts": {"copy": "cp README.md LICENSE build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "tsc && npm run copy", "pretest": "npm run build", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "prepack": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "test-install": "npm run test && (test_dir=build/install_dir; rm -rf $test_dir; mkdir -p $test_dir/node_modules; packname=`npm run xmlchars:pack --silent`; (cd $test_dir; npm install ../$packname); rm -rf $test_dir)", "xmlchars:pack": "cd build/dist/ && (packname=`npm pack --silent`; mv $packname ..; echo $packname)", "prepublishOnly": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "xmlchars:publish": "npm run test-install && (cd build/dist && npm publish)", "preversion": "npm run test-install", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run xmlchars:publish", "postpublish": "git push origin --follow-tags", "clean": "rm -rf build", "commitmsg": "commitlint -E GIT_PARAMS"}, "dependencies": {}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@1.3.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-ItNDLDOaIeTPL6HSYjSqZEY2nZBdmbOA/3xiGTd/y0HGILzvQ3FifNnlQGWvKaMfGZVQWHogtH6GjsNP3cg4nA==", "shasum": "a3e2ed9dc13c6e57e404d2641eee62f0d1b49f92", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-1.3.0.tgz", "fileCount": 15, "unpackedSize": 48286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkYoYCRA9TVsSAnZWagAAHJIP/3EJPoWMLFpz/S5MbfZS\ngw6zS6UDxRLxewxk4eNIUv7HDlgkGtsQAtQCZIjRP+yvHOgNPGnKJ6uc5IVD\nkG3DOmSwDfHNif2LcH9ZGPAfJ3YwGCwDu2/FMaFCt79ftzJiqkpmvFQZoIJm\nZ5BUxgkkN5bmiUfSrbJR8JDxtxuGLHL/HCNab8c/g+TWcixTup/nzzJJBDtq\nUat2x9owmG7nIrOWmuMaDNBycvyGreJ4/xWg2wJxBIWaMeoVd3dZqVo/oF7c\nbM+xcDbcI7wz+ZA4yvpKiKvCYS2oPaMfhUS3EMrnboQVns8ye3uR7FyRsQ5N\n49M8hhxwSbSZvkGdIlZKrBz4yM1rmhDwkdFI8Y3JRi7nBjw4ANwNQbya3tSw\nW/k9vyP1W3IBDQVhtl/f8QW6XwzFl55vOkdmAAnfP+2QNix0nXX27wnvIf/Y\nAAgpIg4KEQSneOvUqLIA5I2bq74JkXPthHAd5jYTWIxykqFLc7bVFLOe768n\nVka1ASChKrO+gsC96IBVVB+TrlMe73oR2StQOKgwCdVmC1he6TbbGEQhfQQd\n7HCVyDQA5WaSTNTe1Wca9duyH/fQ072G2kwcEWVtCub+lu33a/nva+17mp5K\nZSgkflt+j6/Ebn18pbyF6sjVbx0SJXP/irbI9uh6BDZTfSnuAl6PVdLBw/Zn\nHC6l\r\n=zNB6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgkLUINY1bMKChmqx9e3kp2T9g+9sHhOE7o2VZ8x0oGwIgB01N8tsKAXC1GcYaL7qS8InAc8bU5zHokxkYFwDz1DU="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_1.3.0_1536264728145_0.08352783774316253"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "xmlchars", "version": "1.3.1", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "xmlchars.js", "types": "xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "@types/chai": "^4.1.4", "@types/mocha": "^5.2.4", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "husky": "^0.14.3", "mocha": "^5.2.0", "ts-node": "^7.0.0", "tslint": "^5.10.0", "tslint-config-lddubeau": "^2.0.3", "typescript": "^2.9.2"}, "scripts": {"copy": "cp README.md LICENSE build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "tsc && npm run copy", "pretest": "npm run build", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "prepack": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "test-install": "npm run test && (test_dir=build/install_dir; rm -rf $test_dir; mkdir -p $test_dir/node_modules; packname=`npm run xmlchars:pack --silent`; (cd $test_dir; npm install ../$packname); rm -rf $test_dir)", "xmlchars:pack": "cd build/dist/ && (packname=`npm pack --silent`; mv $packname ..; echo $packname)", "prepublishOnly": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "xmlchars:publish": "npm run test-install && (cd build/dist && npm publish)", "preversion": "npm run test-install", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run xmlchars:publish", "postpublish": "git push origin --follow-tags", "clean": "rm -rf build", "commitmsg": "commitlint -E GIT_PARAMS"}, "dependencies": {}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@1.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-tGkGJkN8XqCod7OT+EvGYK5Z4SfDQGD30zAa58OcnAa0RRWgzUEK72tkXhsX1FZd+rgnhRxFtmO+ihkp8LHSkw==", "shasum": "1dda035f833dbb4f86a0c28eaa6ca769214793cf", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-1.3.1.tgz", "fileCount": 15, "unpackedSize": 48285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsgLOCRA9TVsSAnZWagAAsMYP/iAKS1R3GY7sGsQGqP1g\nPg5k5EK9e9ax8uIIsXxv7+ckGiKCJigO8D31B9FFQyUtjVrZxeMGChVptg/t\nHF6JEVc2lGPeDpmEMtP1Uf57Z+KQBkLtwXamSJIrUAfWpSTaVN+SJ35WvoaC\nGwRpIMRqUW3z+QwDBA5VQTADdpKcH78aTNmmXt2f//tYVVVQpa4Up8c+2xT9\nKLvu2ESrRmRGVTRgejdy+9mqMewrdl1WWpq6gGkDFx/M+2iQwgU1USEGcxFQ\nh9Ac/eIc1aAgPsCGdWpFFguuiz4zrCAVcDOjmnb2OfZfwxbf39yqGs5Bz5J8\nNvJso+v9RYW6Pqj344l5r3R/lbBc+geNjRqqgkr6qmRPQzu52BLlktLZrncO\nFEyKgVF9wc8VDqZGYaSOowC2oRffLoiVlnbEPuWcxAD1ZkAGLBOUOYXROctL\nG7JS8V9lw+THsn9ywKfw+HePzuQ3Z6svWUgKrIipsrWWNbCbY71L1Ojp8YJo\nodowlovIMC1vejA23hKT5eULlasmWSckWpxVjVOVadIcHdABTZ3OyKgrikW0\nT5te9F93V1aIgW3AiLOnT4FzABZV+CJ3yZby6kGfj9im8vXByH5vBFfQ1TtG\ni4xbBEA/RxkkU0P9bk1GUQRsTAn4oKtNEy6fk3lnO1EwRpIldjFuvWuerr8Q\nH4S2\r\n=Fmu8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDle8z5rhL9J2/aYoruTT2Wp85vkTiL4s+s3lKdplKRcgIgQ9jSj+sKK9OzRasKhR+D2ngoC44J/I0riLAkHt/ZjEc="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_1.3.1_1538392781305_0.15015225283092426"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "xmlchars", "version": "2.0.0", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "xmlchars.js", "types": "xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.0.0", "@commitlint/config-angular": "^8.0.0", "@types/chai": "^4.1.7", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.22", "husky": "^2.4.1", "mocha": "^6.1.4", "ts-node": "^8.3.0", "tslint": "^5.18.0", "tslint-config-lddubeau": "^4.1.0", "typescript": "^3.5.2"}, "scripts": {"copy": "cp README.md LICENSE build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "tsc && npm run copy", "pretest": "npm run build", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "prepack": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "test-install": "npm run test && (test_dir=build/install_dir; rm -rf $test_dir; mkdir -p $test_dir/node_modules; packname=`npm run xmlchars:pack --silent`; (cd $test_dir; npm install ../$packname); rm -rf $test_dir)", "xmlchars:pack": "cd build/dist/ && (packname=`npm pack --silent`; mv $packname ..; echo $packname)", "prepublishOnly": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "xmlchars:publish": "npm run test-install && (cd build/dist && npm publish)", "preversion": "npm run test-install", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run xmlchars:publish", "postpublish": "git push origin --follow-tags", "clean": "rm -rf build"}, "dependencies": {}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@2.0.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MnRUkN6PXcnmOPak13yGZVyqTBijbdaHEqkKBdNsd/zEAgVpcS1+eCgI//hDkpnlziM6ppStdzc7XWCLLCdOsg==", "shasum": "ef7ead34650048ff9c6b23677dadbc9ae124eb95", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.0.0.tgz", "fileCount": 18, "unpackedSize": 55871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdD+TjCRA9TVsSAnZWagAApAoP/Rlt7ToCqmbxtRhBObXp\nefhoLed7rKjSJAEB7bLnHuyiJAQ4EM25onKoCymk/z8jSKvnXQp5ZVZm2+bI\nBqPKxaMe9KKvxRB5nX0sJL5XjfMxIQyVaFLJ3BBu9ISDpDIwvu715zQIu7vb\n2g1Ta0y3zu94OUBMG/QMRk3rH6PQKdG1qFAF9KDbhJP5sHsN3GNIhm8XzA3U\nAbyrEZHEqhHFMHPW50+S2fUOPhA+Qb4UQcxrI7G9KxA4fwA1l49LHwpJ0nzY\nIWddxN8tM9eCT7HuL+39EfdkLTRVgQbX8dIc1oGLGaGsqazjqO47md9SZCIq\n13Hh4Xy7K8ZIcGIJqd+iQtKlSXJjt7IOpEkX+2y8nvzvAFlNH8vMD6UArb4S\nfwl8QD4C7NqKEy5IE/0kafhsD+IC6i/0xHQStgZPIQyacz62/xM/rm9+Pu92\n2I4Uu7klGWZEaP7Il0KzudPSEI6SLlEcsOSyOrWVOCWa95/ZWqROrniaAXMD\nF2VVHL9G63CQqnaeMpCpAG6+PBGxzEoWkkq774aJpdNVnweGr7zOJQsnlIrn\nhpGwED9ynAt5rfJUOTmYnMP1Z2j4/vO/pFFc+tVU2LCU/oAFWQ6RnAy2t9NX\nu2EzyBBILa6uEFuiU7+2UwAh32aVqIJKkdzvJwUdpmuA8kJp3nlngPlGyBjx\n1B7i\r\n=SJFD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICMv5jRzWS3KdZfjl0Kf5mzZjsUasIkOOE2tSJYb/jcuAiBqJX0eANhhs+64bF3SYRy9SGLtpEcKy1S6hNTpvK6nFA=="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_2.0.0_1561322723173_0.5033152100751921"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "xmlchars", "version": "2.0.1", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "xmlchars.js", "types": "xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.0.0", "@commitlint/config-angular": "^8.0.0", "@types/chai": "^4.1.7", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.22", "husky": "^2.4.1", "mocha": "^6.1.4", "ts-node": "^8.3.0", "tslint": "^5.18.0", "tslint-config-lddubeau": "^4.1.0", "typescript": "^3.5.2"}, "scripts": {"copy": "cp README.md LICENSE build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "tsc && npm run copy", "pretest": "npm run build", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "prepack": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "test-install": "npm run test && (test_dir=build/install_dir; rm -rf $test_dir; mkdir -p $test_dir/node_modules; packname=`npm run xmlchars:pack --silent`; (cd $test_dir; npm install ../$packname); rm -rf $test_dir)", "xmlchars:pack": "cd build/dist/ && (packname=`npm pack --silent`; mv $packname ..; echo $packname)", "prepublishOnly": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "xmlchars:publish": "npm run test-install && (cd build/dist && npm publish)", "preversion": "npm run test-install", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run xmlchars:publish", "postpublish": "git push origin --follow-tags", "clean": "rm -rf build"}, "dependencies": {}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@2.0.1", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UEceZ32GxZ5dAwMqMm6h2eRHCp+RUut/BKBPjXFcmPR01m775PGPI9DCxQIgzdnYxdYilRet5fNk5MmAeRszoA==", "shasum": "7abf27745c630dd35548b6d003c951ab256a210d", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.0.1.tgz", "fileCount": 18, "unpackedSize": 55862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdD/sgCRA9TVsSAnZWagAAjDoP/jqm4qrZjZn4AG/V6DZi\n1GoQlUnkOKwN4LL0mPwOoQbK+LPn9LDX7nwsc9GFidOim/FH6Vj+ZWWG+cas\nJt5+Yp4wIEAIUHbudlRMqVfwv4e5NASPO8ey5GscbEmbTIYbrXm9zLSKf0am\nJ+CdOk8QG/2UBnaqn7CVjIRIeBzcdp3aMpUpw8b7QwPyxxsridOCRd6lw/K3\nda5GpiyaiRP6FMYu1FbjWqnzP/SsVYxaXwTavEL6yjU32VeBDnKF7ECGoseo\n7eZUjIc7U4kxRsz1lbMrtdFANr/W2v0jOPBpvh+mMgXb94PH/oc5KBXl5Wvk\neVwnQ6JDiXha7Y3XMmDQU/jzJQbB+HxjmPq+2Mn6tYcsv30PbL2sWWM/xkcA\n7iO0VJ8KWayyWv5Wk9HnPRezaQTSHZveNSHyvcKfZTh/Pbct/etn+klP744y\nZbqAb1K5OyQmMPia+Yv/pRWnUf9fAHL0Ly5RWiA+h6ZGps35dTaYTuFLOiMH\nFQ4PTddJbJWbCpUSrvt/OLJ9ljmrRe+Vtw2O5KH0eGzeFZHgHUaSShb9VGRB\nR10UtKKpKexys0a3zcY62Ltt3+WX2tCq2IwcP2juFTYL0FpqbArXImlcLXID\nGkv1icaADWj20X5Afmle+dRY/soCHmk5SlxkfrHERrtz9TWTGaDB7PdFYuh/\nIJrB\r\n=UFyT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKJD40fPf4AEEn2q+n1SsHjrAaeVu/I3fbBQ+1DNsYaAIgH8EjRood6+3n4eHIjKIn3sXyL34olSNv3E+fXD3Gp9c="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_2.0.1_1561328415695_0.7851875469817549"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "xmlchars", "version": "2.1.1", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "xmlchars.js", "types": "xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.0.0", "@commitlint/config-angular": "^8.0.0", "@types/chai": "^4.1.7", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.22", "husky": "^2.5.0", "mocha": "^6.1.4", "ts-node": "^8.3.0", "tslint": "^5.18.0", "tslint-config-lddubeau": "^4.1.0", "typescript": "^3.5.2"}, "scripts": {"copy": "cp README.md LICENSE build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "tsc && npm run copy", "pretest": "npm run build", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "prepack": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "test-install": "npm run test && (test_dir=build/install_dir; rm -rf $test_dir; mkdir -p $test_dir/node_modules; packname=`npm run xmlchars:pack --silent`; (cd $test_dir; npm install ../$packname); rm -rf $test_dir)", "xmlchars:pack": "cd build/dist/ && (packname=`npm pack --silent`; mv $packname ..; echo $packname)", "prepublishOnly": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "xmlchars:publish": "npm run test-install && (cd build/dist && npm publish)", "preversion": "npm run test-install", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run xmlchars:publish", "postpublish": "git push origin --follow-tags", "clean": "rm -rf build"}, "dependencies": {}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@2.1.1", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7hew1RPJ1iIuje/Y01bGD/mXokXxegAgVS+e+E0wSi2ILHQkYAH1+JXARwTjZSM4Z4Z+c73aKspEcqj+zPPL/w==", "shasum": "ef1a81c05bff629c2280007f12daca21bd6f6c93", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.1.1.tgz", "fileCount": 18, "unpackedSize": 57305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEVqlCRA9TVsSAnZWagAA6x8P/RVTBr1P398CnCJvlkMH\nlxD51AWESbzdjQdBNks+epkqdveqwPM1W90CG5FjcoBk8fV0+/x+uNV39Ncl\nBUo4zuT5D5MFKrEvZfbBFE7OWwtXIsLCWCjnNtxlXWdk8QAUnW4/DctXUiOv\negjw6JRjoJI+/6ScPwvLqJABALI1LTeA+k15G+fa5tD7d32UWpUaJQmZtCxf\nTCEAenepu3nT0bDhEXpkslBva44T9c9dhi00i822QJYQJL0aPYeWSbSOgCbI\n0z1c2NCVAEc+qx4SFlD2WayH4SyU6Q2RNooc7HECcpAwXgLjzZCMeEAzhVkL\n3ybiUf/KTPBzvJKrU6GS+fYhMC94w2yEFQbm/E45GYkrRfnQrBf5Tn88XePV\nwS23CizvJhfJGGCejiA/npmSKEpkVkIDOwc556GV1P/x7rTj7mydyRYGw5UZ\nphhEheVF5rxsdYQiixgaLPWTR0Tf6uasTduU/nxlXxvb8Sd7bOKPniz7u0x7\nX2eGxhbI7vLCNGGDe0Emtfw+opX921mST4oAUFb2NEYFaMUcvu0sRrgHPmN9\nsq+6G4lS8MYeNk6Se9CkkuLOqkSB7d2kADs5AqKqMffhoGIQazEga12R6Hzr\nHQTd4AG24OgBggiVkTi7BjnOKEDPxrYINSFjiAD7gY1goD+06W3WTPFjsYZy\ncyRW\r\n=jcrA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBR87D12kKarrk2gTIeH5iT34H2HEyB3MNsyE03vGBSiAiEAh6WgSiQjejQ7lQ03hGIXIT9Lo55oHwvgCg+SIDgPmoQ="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_2.1.1_1561418404206_0.30157004395919773"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "xmlchars", "version": "2.2.0", "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "keywords": ["XML", "validation"], "main": "xmlchars.js", "types": "xmlchars.d.ts", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.1.0", "@commitlint/config-angular": "^8.1.0", "@types/chai": "^4.2.1", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.23", "husky": "^3.0.5", "mocha": "^6.2.0", "ts-node": "^8.3.0", "tslint": "^5.19.0", "tslint-config-lddubeau": "^4.1.0", "typescript": "^3.6.2"}, "scripts": {"copy": "cp README.md LICENSE build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "tsc && npm run copy", "pretest": "npm run build", "test": "mocha", "posttest": "tslint -p tsconfig.json && tslint -p test/tsconfig.json", "prepack": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "test-install": "npm run test && (test_dir=build/install_dir; rm -rf $test_dir; mkdir -p $test_dir/node_modules; packname=`npm run xmlchars:pack --silent`; (cd $test_dir; npm install ../$packname); rm -rf $test_dir)", "xmlchars:pack": "cd build/dist/ && (packname=`npm pack --silent`; mv $packname ..; echo $packname)", "prepublishOnly": "node -e 'require(\"assert\")(!require(\"./package.json\").private)'", "xmlchars:publish": "npm run test-install && (cd build/dist && npm publish)", "preversion": "npm run test-install", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run xmlchars:publish", "postpublish": "git push origin --follow-tags", "clean": "rm -rf build"}, "dependencies": {}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "homepage": "https://github.com/lddubeau/xmlchars#readme", "_id": "xmlchars@2.2.0", "_nodeVersion": "12.9.1", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==", "shasum": "060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb", "tarball": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz", "fileCount": 18, "unpackedSize": 58957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcoucCRA9TVsSAnZWagAAiHUP/3TGNViRDuMi9TowSTnE\nSg74kVTWRTgCaANLqM/Ng4OazPTrWKc6NwsbF+/CNTlB47LAoSiVRQkTuKSI\ng2v2tkcCGPaTb9XJ4o6HKjOCDqsBK92IpWquFKB5i3ctQT+z2X6KKjPsBBWQ\noCrb9pvN0KgMhN7OEnXytxb7Up/IcmoiKC4nDQoG3zMGU0Sjz9nOYpwnAnXx\n4w7v+NLusys+t4F7z7V8K74ut2YGF8GeHsWAk0wXAwV0SNrHEmXbbkoCNQKz\nuhpMn4iE3k/7YCVgFx7Irx128Dt6wHafoEiFFXj9MkvSQm3M/kCNWPUPqT0y\nxqKXKHJxKcg02U3gkM/OqbsNLWMjGxpjL0ImNAcQUR0j5zFdPtEUChz8nw+j\n4C0ao8/K0b3s2LTS6qKjd8fCcGyrB6UjPQzWNGgZ5RFk+8xKGPkxAFVwcuHm\nMmTL/O+MIcynJCsZfqOzOJrRfQh+m2LppruqRe48M/EIawX17TpN14O9I9to\n5OiquU1qhVZumL9GmBju7DC5iDS2Q0WypUNd8kVVQ+1u7g+s+QYJeFpdPHaK\n2zUMPgJbXeeVZ8auIKLDqtqX4aOPtZGamNflzzOAO+U/1/M1tmCWLGb0j1uE\nzqyc2lHbsQ9JnQV7r8hGim+Om0WIy1vZ2pmSbKg+Y0VswK8KYKhcCYYb3dEY\n6746\r\n=+Skd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCI5wV+vpqPkhdGrrZUHvNMbELOY7jb8JzvlE2jp5ebRgIhAMOID9EcC6gHgOmSZmRFfPlbBv1UbQx+toX48fdgRlKn"}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xmlchars_2.2.0_1567787931812_0.6302971406766107"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-07-05T17:56:49.271Z", "1.0.0": "2018-07-05T17:56:49.353Z", "modified": "2022-05-25T00:25:56.843Z", "1.1.0": "2018-07-23T14:31:49.114Z", "1.2.0": "2018-09-06T10:29:59.983Z", "1.3.0": "2018-09-06T20:12:08.266Z", "1.3.1": "2018-10-01T11:19:41.520Z", "2.0.0": "2019-06-23T20:45:23.303Z", "2.0.1": "2019-06-23T22:20:15.856Z", "2.1.0": "2019-06-24T23:17:35.372Z", "2.1.1": "2019-06-24T23:20:04.352Z", "2.2.0": "2019-09-06T16:38:51.976Z"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "description": "Utilities for determining if characters belong to character classes defined by the XML specs.", "homepage": "https://github.com/lddubeau/xmlchars#readme", "keywords": ["XML", "validation"], "repository": {"type": "git", "url": "git+https://github.com/lddubeau/xmlchars.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lddubeau/xmlchars/issues"}, "license": "MIT", "readme": "Utilities for determining whether characters belong to character classes defined\nby the XML specs.\n\n## Organization\n\nIt used to be that the library was contained in a single file and you could just\nimport/require/what-have-you the `xmlchars` module. However, that setup did not\nwork well for people who cared about code optimization. Importing `xmlchars`\nmeant importing *all* of the library and because of the way the code was\ngenerated there was no way to shake the resulting code tree.\n\nDifferent modules cover different standards. At the time this documentation was\nlast updated, we had:\n\n* `xmlchars/xml/1.0/ed5` which covers XML 1.0 edition 5.\n* `xmlchars/xml/1.0/ed4` which covers XML 1.0 edition 4.\n* `xmlchars/xml/1.1/ed2` which covers XML 1.0 edition 2.\n* `xmlchars/xmlns/1.0/ed3` which covers XML Namespaces 1.0 edition 3.\n\n## Features\n\nThe \"things\" each module contains can be categorized as follows:\n\n1. \"Fragments\": these are parts and pieces of regular expressions that\ncorrespond to the productions defined in the standard that the module\ncovers. You'd use these to *build regular expressions*.\n\n2. Regular expressions that correspond to the productions defined in the\nstandard that the module covers.\n\n3. Lists: these are arrays of characters that correspond to the productions.\n\n4. Functions that test code points to verify whether they fit a production.\n", "readmeFilename": "README.md"}