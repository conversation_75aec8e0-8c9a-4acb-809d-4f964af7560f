{"_id": "@babel/plugin-transform-class-static-block", "_rev": "35-06ca834aa13286fb53392ac1d4f838ae", "name": "@babel/plugin-transform-class-static-block", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-class-static-block", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "c3cde152c0460523b076866f4055740756093517", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-b6N2cduLeAmnZMHlLj0XB8108D4EHLtpv1fl7PudLjHf+yxFxnKvhuTn5vuQg61qzS+wxp5DBOcNo1W/GEsFWg==", "signatures": [{"sig": "MEQCIEOosCSyUFRYn1hQbd7YE0jPsNlH5zw1iYW6PM7u9CwyAiAYPdwgItoBd4cZycyutd4ImovZIRdASExeeppzoEoSMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10130}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/types": "^7.22.0", "@babel/traverse": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.22.0_1685108750574_0.1510915766195955", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-class-static-block", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "e352cf33567385c731a8f21192efeba760358773", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-5BirgNWNOx7cwbTJCOmKFJ1pZjwk5MUfMIwiBBvsirCJMZeQgs5pk6i1OlkVg+1Vef5LfBahFOrdCnAWvkVKMw==", "signatures": [{"sig": "MEQCIH3k/DZLWiT5Hz5uKaJd5bLek1OX9AsfOmbhZ8rEe0gaAiA+qYY20H2bPttARMW1RHf59DF28s1hX4jq4Xcv6l3avg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10124}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/types": "^7.22.3", "@babel/traverse": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.22.3_1685182255417_0.9346324080404942", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-class-static-block", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "3e40c46f048403472d6f4183116d5e46b1bff5ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-SPToJ5eYZLxlnp1UzdARpOGeC2GbHvr9d/UV0EukuVx8atktg194oe+C5BqQ8jRTkgLRVOPYeXRSBg1IlMoVRA==", "signatures": [{"sig": "MEQCIDGbavE4fyl7rNTxCnQG45cRuFTTZwzyKyuwv/M2NUPWAiBtpL2sZVMypXbLJkLeU+sHJ9waoCt5pi0kHmSxdXafcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10117}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/types": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.22.5_1686248509212_0.878807735038708", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "7f3251ec4dc9e713a8f976576feda8e7cff46df5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-SXeFY37Gd/vzkpZmrtMzIQZH9PZBlE3LFEJdMTYJ3I1TW4sH8KL0eYGW4lViy2Fonf2g+nsPAwBezv+X/tUVrA==", "signatures": [{"sig": "MEUCIQDg7B6KFnQrKvqTgHDSSYYK9h86L7gJ/Q0Jr6u02r4mqAIgf5oAD4XJjE9AfVNI4xckG7BgV69rEiGnXqqzCs1d6eQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9999}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/types": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.0_1689861631829_0.27530263122629406", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "1e6e09b4db46f0de5d1af2822c1ecdf8b37c7029", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-p0IARIdBJTGuLjxvW+SWdHYiKl1hU/AkFlVut1Y49FmiYHFA23y6iW8Dj3c/StK08HzE2hxv4HfcDW3Q8ickSA==", "signatures": [{"sig": "MEQCICBdnsRJhEBBgB+wwCvDyjA5RljxTM35hMNZ7GD2lo4HAiBjSrk5Mv9nFEVB8pyrGhSk3F0/19wkB2VA+Ex/Gj/Btw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9999}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/types": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.1_1690221185054_0.5733752641846046", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "1080e2f13c484e7c9e51b9344c9ad1e3cbf7e884", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-0lm/EIkRjJVfTgtAo7tTdo6HHDMXbE2/UP7hlt/M6Ta85vteuyslB98lK5J5oD2ZKOK30VkUu81zkMbOa9TS1Q==", "signatures": [{"sig": "MEUCIB4htT98c6scN3Y0YMfxDzxL+Jkpqif2LRbsHfWAT7b2AiEAiA0K6q/lPK1URL9EoZ3LPDzuV9syfC9lZCIEaApun6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9855}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/types": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.2_1691594127412_0.8818060217097907", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-class-static-block", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "dc8cc6e498f55692ac6b4b89e56d87cec766c974", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-GMM8gGmqI7guS/llMFk1bJDkKfn3v3C4KHK9Yg1ey5qcHcOlKb0QvcMrgzvxo+T03/4szNh5lghY+fEC98Kq9g==", "signatures": [{"sig": "MEYCIQD5R0XG7Pbhz0NsT4zUwwrAYjiI0w+qUjnPg87HKS8DPAIhALh+RfzAH7dUnQmPQwlZAwensh5dyGGjvZdS/Ifnv9c2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10070}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.22.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/types": "^7.22.11", "@babel/traverse": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.22.11_1692882527320_0.08113844065851228", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "ed251b7f099fab8e8aaf2680b57220a8050ebfac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-y9BC7RqmWWRaAkOl55c8jeDbTBD8DXWbWKWS00gcAJ9DBTc5NpxTnFDHHiMfkQTaXkDZIcBWj9WedgTfVyTerg==", "signatures": [{"sig": "MEUCIQCAwXijVZXERRD+tITVIcBvUH3W9pPjWCO8AZfgMAJvWQIgdJZa6uNhYKzlJ7EF+LsBlT1Puu1KrMZyMifzA4cP2pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9897}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/types": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.3_1695740259574_0.726448899221674", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "d04e4391d5d99061d204042f181cbbcd1ac1c6bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-feRAXapHknG1tUaPnPYT4DXPt85tDoWnKuPM1GGlTetD7DcjrRA/pSHs6pCEQHoi2jF4gpfZACRvrXy2af36gw==", "signatures": [{"sig": "MEQCIB7GaYxG8PcSAfF/wDVpk1ENxV4qe8EBGx/2mvQX71IsAiBuhnbNubQ9c8MxwTWEl2fb6xvHynk6zP74FWnpfzKA7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9897}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/types": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.4_1697076413164_0.7253551437618824", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-class-static-block", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "56f2371c7e5bf6ff964d84c5dc4d4db5536b5159", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-PENDVxdr7ZxKPyi5Ffc0LjXdnJyrJxyqF5T5YjlVg4a0VFfQHW0r8iAtRiDXkfHlu1wwcvdtnndGYIeJLSuRMQ==", "signatures": [{"sig": "MEUCIQC68bVZPqaXaQE14ekzN08mQLZtVprKdAMEoLit8OaXDwIgaPCNtgnVmtpFbI4nOBSUlXo06Q2lGMM8U+Fg0SVe/4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10147}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.23.3_1699513426669_0.20206947225598948", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-class-static-block", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "2a202c8787a8964dd11dfcedf994d36bfc844ab5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-nsWu/1M+ggti1SOALj3hfx5FXzAY06fwPJsUZD4/A5e1bWi46VUIWtD+kOX6/IdhXGsXBWllLFDSnqSCdUNydQ==", "signatures": [{"sig": "MEUCIAbHtRnfsdj40tDvy3gBE8foG0XDlkNaMOXaD4s7E5d+AiEAn2qLpUIWAfaJETNn1QMiTNrLeVNFb15Y7F8RJF1M9HM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10153}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/types": "^7.23.4", "@babel/traverse": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.23.4_1700490125496_0.046936592444274616", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "e562217fb8611e1196b5d5ff0a9f63fc30760dc5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-pPUefSwzvo9lOb4lSQX5oxPUEKabg4eakHusMY1o5dqnKm2QcGiETF9co621JMhGzSK25veW6+b3NVf038uVNg==", "signatures": [{"sig": "MEQCIGg7ab1gxxEvBRu5HGfNPbepLGugofxs1uturRw5+mjEAiBwcIcRHDsWCy8USu/BPeQOgRjg6BmvWbWYSW0jduDuKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10015}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/types": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.5_1702307987406_0.9449170017830744", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "40526e943c7c747927de38fc1201cc3c25e35f73", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-KEMXG3unrHznOJkJsJ6geQRYKZtCt3PihDCY/DZ23INhYn99NTl4g9f2jb6/5JrHzNJIIM2YkdEOyTq1/VJJiQ==", "signatures": [{"sig": "MEYCIQD1yoATSe05lGifyBzsC7DPTthKx+eOrai6tsJYyQt77AIhANHdwTprqDCuhS2KbNhptghtfrVDze5SwREN1QVEO70d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10015}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/types": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.6_1706285692321_0.609375499765775", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "e978661e88b7a020753a61ef9d47dba752ca6153", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-eQqrYGFe5DuCQAsVZbQ0qjXXEdZY9vFPVy2a6BD3mfm1rxaCBrJbs42UHDSIjPNOeq6CevUY+HV9931Ks/ZPOg==", "signatures": [{"sig": "MEUCICtNqUGzTkVspketcjDRFOnaYngpgfnHD3jl0yKiU+FDAiEAvd5FChrZ3VSDNSIkVNSw2FCmmzT5JwKDTaKMSDX8krA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10015}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/types": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.7_1709129149929_0.7415031670582253", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-class-static-block", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "4e37efcca1d9f2fcb908d1bae8b56b4b6e9e1cb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-FUHlKCn6J3ERiu8Dv+4eoz7w8+kFLSyeVG4vDAikwADGjUCoHw/JHokyGtr8OR4UjpwPVivyF+h8Q5iv/JmrtA==", "signatures": [{"sig": "MEYCIQDQGf8ay0gTSO3vJTAFEPt8QUzDVzJHVGNMSaqTpoit4gIhAIc3EaOTCX+6qiS/tl3x2ct8o51KaXmtMcAns3EI57l9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10224}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/types": "^7.24.0", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.24.1_1710841775191_0.1825636244948594", "host": "s3://npm-registry-packages"}}, "7.24.4": {"name": "@babel/plugin-transform-class-static-block", "version": "7.24.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "1a4653c0cf8ac46441ec406dece6e9bc590356a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.4.tgz", "fileCount": 5, "integrity": "sha512-B8q7Pz870Hz/q9UgP8InNpY01CSLDSCyqX7zcRuv3FcPl87A2G17lASroHWaCtbdIcbYzOZ7kWmXFKbijMSmFg==", "signatures": [{"sig": "MEUCIQCh2SpGulI3fQa3fxmrtlgZXN3pUdVdQh2aICnSnfziwwIgG94EasXtDG4l05uiFmeGVN0QZW8FTEAWz+Swr9AXhFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10297}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.24.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.4", "@babel/types": "^7.24.0", "@babel/traverse": "^7.24.1", "@babel/plugin-external-helpers": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.24.4_1712163232734_0.7210473465597877", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "e3e8846be474551e057e02b3de4ae75e14f37bf7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-nNAq/C8jtQF9MJaToSjKX7R8k6xVubOjhr0mINvgDtAZHyU9VOb3NFQny7OgofVz0/0etHysax8RgOb613isFw==", "signatures": [{"sig": "MEQCIGzFsDY27CqhCbO6t4yRbnF8SU4sSyRzNDjrbyK3tla7AiBJSEk9WnWrS+3fYp1H0vLg98e/4QYrgg0PmYbvN9C5SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10033}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/types": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/plugin-external-helpers": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.8_1712236822902_0.48845559466807575", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-class-static-block", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "f43f29286f6f0dca33d18fd5033b817d6c3fa816", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-1QSRfoPI9RoLRa8Mnakc6v3e0gJxiZQTYrMfLn+mD0sz5+ndSzwymp2hDcYJTyT0MOn0yuWzj8phlIvO72gTHA==", "signatures": [{"sig": "MEYCIQDRh6Vh1AGRHHl5sm5RBeYWZtde/oyIdSz7Ar34tbHzQwIhAIt1Rs5zWQFTs8/3CcK3K5EPqKhGZh67ffusJdINOgPY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77247}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/types": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/plugin-external-helpers": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.24.6_1716553513274_0.00341345354692435", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "ea27fd59087c968045f8f0d6d5b4a966dc2a2ec1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-A8O72YYNvRY06ztcMWVO9OqtQFGL9nAfQI2wZmP/9Vt+SnpEh//k9l+7Y6orQBxwt283NU7OhwHkRmCVQvNOTQ==", "signatures": [{"sig": "MEQCIDP4JuNb6gsy0pIbTnwteffalwdbKKuX2vsZjEAgg8eAAiB7OUB8AjlT+RjPyIc00km1xbB5vflnwVJ+7Ot+cBBVng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77229}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/types": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/plugin-external-helpers": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.9_1717423552165_0.9491392697978316", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "3fc5d47dcc9444e9de10748c00a5e1d421cd7523", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-8q0JRRaBzNxRu7tRdm/TYxgeqWuWKd/E7pNgQMcwj6gC4/YNe9VhXPnAqz94yoHZKCEypXtG8BVdY5Hv+cIRnw==", "signatures": [{"sig": "MEQCIHB0V4SAa0ZxID5us8jax3UVBelsJUGM+e13TZQhAucvAiBq9SPuc5P5VvVufDGqlIfDs4NpVKG7xNsUQ4bs+eApSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77239}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/types": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/plugin-external-helpers": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.10_1717500047409_0.29754847405601437", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-class-static-block", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "c82027ebb7010bc33c116d4b5044fbbf8c05484d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-HMXK3WbBPpZQufbMG4B46A90PkuuhN9vBCb5T8+VAHqvAqvcLi+2cKoukcpmUYkszLhScU3l1iudhrks3DggRQ==", "signatures": [{"sig": "MEQCIC0KXB1l/jFa2oSQJM3mQbXJE1oAzGPfYgVH9PryFmoMAiBCJ/vQRSlVmiawLY0+VKpjxHRxNKiTNCzY0QJuU9Ijag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77239}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/types": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/plugin-external-helpers": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.24.7_1717593359804_0.03199050020344085", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "33dfb3366182e0c63bf51ba83b64de4693990644", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-hqQflI2VoMlSd7mTRQXEW+IKB3PhThAfzjmOJfENXLVVQfk4C0h12twe14hdmJeUcK9epQh4hXN24/BiCi5Kqg==", "signatures": [{"sig": "MEQCIBGsO9fItnyTc1ursaN/aikEs+YbFadSR+ZD2drMgWGqAiAcqAKTBPe4bzOgsZs094fTw1FvD5ktwR6GTD91HtL/FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77128}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/types": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/plugin-external-helpers": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.11_1717751770665_0.7334997971313544", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "ca6e09f079c27f6db694a44f5d4aa9e345418aeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-XcFr0bM4AOPs9XHWhwW3rV5f0B/77VbgUudJr5hVgN2E9QgIMKKVd8clpM5czT0JHz6hmadfWN20QPYruPT17A==", "signatures": [{"sig": "MEUCIQC5XHlBx2bWfSzG4JVn7CL8MVugdD5kTR4KNXX9Y8k1MwIgPXLfKtPE5o6tiBkF61NuuHyK82QLgylZGmude9k95bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73859}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/types": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/plugin-external-helpers": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.12_1722015245850_0.8422097740868868", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-class-static-block", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "d2cf3c812e3b3162d56aadf4566f45c30538cb2c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-rvUUtoVlkDWtDWxGAiiQj0aNktTPn3eFynBcMC2IhsXweehwgdI9ODe+XjWw515kEmv22sSOTp/rxIRuTiB7zg==", "signatures": [{"sig": "MEUCIQDEVA1eypS/OXD5jBHhAJqTX9CZvj/l2o+5AgpwT4c5GwIgDgkJFhfXW3NoOQYNL8a1CuP83n3gwvRvzC4j0/BRJ4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81735}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/types": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/plugin-external-helpers": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.25.7_1727882136485_0.8175431034731229", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-class-static-block", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "a8af22028920fe404668031eceb4c3aadccb5262", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-e82gl3TCorath6YLf9xUwFehVvjvfqFhdOo4+0iVIVju+6XOi5XHkqB3P2AXnSwoeTX0HBoXq5gJFtvotJzFnQ==", "signatures": [{"sig": "MEYCIQDX54ss+RpDNedlT8tPlaHK024KxykKtiMw2M2kpSzHCQIhAO/ChP9sPCyX2GH6FXS17r6ZTq9et2Nc9KTQjO+t99Pe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82038}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/types": "^7.25.8", "@babel/traverse": "^7.25.7", "@babel/plugin-external-helpers": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.25.8_1728566708298_0.23269426289539674", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-class-static-block", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "1cab37c4278a563409d74c1e4f08fb77de5d7a5c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-UIf+72C7YJ+PJ685/PpATbCz00XqiFEzHX5iysRwfvNT0Ko+FaXSvRgLytFSp8xUItrG9pFM/KoBBZDrY/cYyg==", "signatures": [{"sig": "MEUCIDOsly0kLPxBFOKA9vupF4IxmQPYIhQQ0RLQPtRa9EuDAiEAnPgzXQjVFzlnWYoARcgOltHVMMqexBt7JBtF1Vpg0BM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10180}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/types": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/plugin-external-helpers": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.25.9_1729610511745_0.6753848607135848", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/plugin-transform-class-static-block", "version": "7.26.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "6c8da219f4eb15cae9834ec4348ff8e9e09664a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-6J2APTs7BDDm+UMqP1useWqhcRAXo0WIoVj26N7kPFB6S73Lgvyka4KTZYIxtgYXiN5HTyRObA72N2iu628iTQ==", "signatures": [{"sig": "MEQCIHvxbgYc6PysbKBaA/VQmU9Udy3IoMZkVxmhuHbMZfYTAiAFEMI8A0R1vFyBp55LeKAxJxbIBlH+vUQKvhLjgGZ4lA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10239}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/types": "^7.26.0", "@babel/traverse": "^7.25.9", "@babel/plugin-external-helpers": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.26.0_1729863009725_0.30393508959780835", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "f766f9527301ebacef4cc8154a1043b1cbbca69d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-dImEzrOZBJMc15YZ3T0E+QDKOvCuTVnsfFcO0Sq3LL2llKjBAF1xwGHWP6lS1MsgqzZsfl9Yl034ipBn7E+l5g==", "signatures": [{"sig": "MEYCIQCRK7ZFc4TVwFNl15pRgOXTVPwCa9mFOX6l0NYxUEXtnAIhAMzqSzrVNSAAUl73iV5kJKc3eoYuzdS2fge+3yrf/IZI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/types": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/plugin-external-helpers": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.13_1729864492122_0.8183733795242314", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "f7ae8f321cd771ee5c4a04ca8a71ea074e9cbf6c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-YtqhRt6/rB6AainvxIZVVr7Hsm5R9vkgW+LMk8igBbsq86IVEHzPb6oBPvnYLjaQdl4eq5vQA1OSViTWD8A0jQ==", "signatures": [{"sig": "MEQCIFjxMLjGqQFb+5pDiv+kRDpo5Gaw7smP1RTgBN65+9ShAiBdHN4c6x1iYpfiNLSyIlzgNXy0T4guvrwvYhABPJXZXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/types": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/plugin-external-helpers": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.14_1733504081107_0.21099821782740724", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "ed45f6519a93cb5b19442959234dd3dcec2db305", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-Xkgrn609KJKFUL8BYBS79+PAhB0ZNGXD4qoZ3YOZCDPX5p9eZS2/eyN7JOlmiwyclKDSVaHfp7pMEYCBCiD4QA==", "signatures": [{"sig": "MEUCIQD6Kj+CkryPALJ0d2/dP+83DEPkrLV/4rHAGpfPuMkRnQIgZEUYr14V/6vWbI8IgW74LGYTguVQGRBxLq0BuqPn4mo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/types": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/plugin-external-helpers": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.15_1736529911530_0.4093776270986391", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "177cdf8fde4303700f1f3010f82edb9e59af0bda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-QTIpjv+Ik/nd3HEaipamnn5bxkzFffMxNNXma1kpEtsTrbaSV+gP5d0ANp2va3Ay7oV3YkyJ4yjMVXu+auUttQ==", "signatures": [{"sig": "MEQCIEjOM6greiz2UCToCFv61uQF5US8v1XRp+XNgXI6xi1EAiBt67FhHWHbCtuQcUkBVbtLknrYGjn50PsHJ9c1W4Jgdg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/types": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/plugin-external-helpers": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.16_1739534384905_0.3980304597750375", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "48bbe77696a85d781c3dd652a43e4de05c26c82c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-1olwKrgaPbB5+boascBBZUQy31OKrj6oZK2EhRENjD3/8FX0W/R9214yKbi2cYud/EwtnujFXMnXfhISNL4OvQ==", "signatures": [{"sig": "MEYCIQCQWyngT2NwIufqFfOleEDwGFPGYHgtuveYKOgmFH6IYwIhAI1uOWwtWW00rf8qQ3tvLB8rY5KqakUkE+enqK71sLyV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10379}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/types": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/plugin-external-helpers": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "@babel/plugin-transform-class-properties": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-alpha.17_1741717539503_0.5726550934430068", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-class-static-block", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "7e920d5625b25bbccd3061aefbcc05805ed56ce4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-s734HmYU78MVzZ++joYM+NkJusItbdRcbm+AGRgJCt3iA+yux0QpD9cBVdz3tKyrjVYWRl7j0mHSmv4lhV0aoA==", "signatures": [{"sig": "MEUCIBqegLZB96/fHhDBCx8pKpPjMBfPrEqMyTcOs2j5oyIUAiEAvJRrVlBmnfx8uU8kQ7nmyVhsrsKUNhPLWtB0giBCVL4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10239}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/types": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/plugin-external-helpers": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_7.27.1_1746025773600_0.39098073555140966", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-static-block@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "dist": {"shasum": "078de26fe5bbe0988345af66e26b7ea6db9101d3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-O7g6j8hVc9QusZP7iHD9aDGoU5vgRhJ8/0MFdHO7WLfq+3slRyVc80mstwgGJ/1b8mnC1rJUducDNd1lk7jBJw==", "signatures": [{"sig": "MEUCIBYEsp8YNL6Fa9q0caPPhwPDX2gWCmHOSD1lTMhQ3Sg5AiEAwHqe8CdLebB5i9tHRVZTjOMqVzkyGx0hjgjdVVcinbY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10345}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/types": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/plugin-external-helpers": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "@babel/plugin-transform-class-properties": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-static-block_8.0.0-beta.0_1748620310705_0.4676890241376357", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-class-static-block", "version": "8.0.0-beta.1", "description": "Transform class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-class-static-block"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-external-helpers": "^8.0.0-beta.1", "@babel/plugin-transform-class-properties": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-transform-class-static-block@8.0.0-beta.1", "dist": {"shasum": "fe5af48ed80f1e1734206d2e94025302cdd27027", "integrity": "sha512-tBHi+fYS7qnGZ8bQxqHBYGS17CNwdp88MgvzvHoq01hRYxKKxuWxZ8y9UzWGfSjgf1wI8w18dTiB+qGCZZLs8Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 10345, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCro9xnxywbphYyfwYssiJsj0ojaIJXYiEO88owzAo8TQIgKsOWTsPungNv6sNXmMwT1RhyR0jqrievKx2CnoafI+E="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-class-static-block_8.0.0-beta.1_1751447092574_0.9195726176478254"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:50.498Z", "modified": "2025-07-02T09:04:52.957Z", "7.22.0": "2023-05-26T13:45:50.905Z", "7.22.3": "2023-05-27T10:10:55.572Z", "7.22.5": "2023-06-08T18:21:49.396Z", "8.0.0-alpha.0": "2023-07-20T14:00:32.055Z", "8.0.0-alpha.1": "2023-07-24T17:53:05.209Z", "8.0.0-alpha.2": "2023-08-09T15:15:27.586Z", "7.22.11": "2023-08-24T13:08:47.488Z", "8.0.0-alpha.3": "2023-09-26T14:57:39.788Z", "8.0.0-alpha.4": "2023-10-12T02:06:53.327Z", "7.23.3": "2023-11-09T07:03:46.836Z", "7.23.4": "2023-11-20T14:22:05.685Z", "8.0.0-alpha.5": "2023-12-11T15:19:47.664Z", "8.0.0-alpha.6": "2024-01-26T16:14:52.466Z", "8.0.0-alpha.7": "2024-02-28T14:05:50.071Z", "7.24.1": "2024-03-19T09:49:35.327Z", "7.24.4": "2024-04-03T16:53:52.922Z", "8.0.0-alpha.8": "2024-04-04T13:20:23.053Z", "7.24.6": "2024-05-24T12:25:13.447Z", "8.0.0-alpha.9": "2024-06-03T14:05:52.311Z", "8.0.0-alpha.10": "2024-06-04T11:20:47.688Z", "7.24.7": "2024-06-05T13:15:59.982Z", "8.0.0-alpha.11": "2024-06-07T09:16:10.808Z", "8.0.0-alpha.12": "2024-07-26T17:34:05.996Z", "7.25.7": "2024-10-02T15:15:36.764Z", "7.25.8": "2024-10-10T13:25:08.567Z", "7.25.9": "2024-10-22T15:21:51.955Z", "7.26.0": "2024-10-25T13:30:09.891Z", "8.0.0-alpha.13": "2024-10-25T13:54:52.325Z", "8.0.0-alpha.14": "2024-12-06T16:54:41.310Z", "8.0.0-alpha.15": "2025-01-10T17:25:11.685Z", "8.0.0-alpha.16": "2025-02-14T11:59:45.060Z", "8.0.0-alpha.17": "2025-03-11T18:25:39.677Z", "7.27.1": "2025-04-30T15:09:33.779Z", "8.0.0-beta.0": "2025-05-30T15:51:50.891Z", "8.0.0-beta.1": "2025-07-02T09:04:52.731Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-static-block"}, "description": "Transform class static blocks", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}