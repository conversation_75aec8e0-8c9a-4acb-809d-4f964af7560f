{"_id": "commander", "_rev": "1434-dc926d57738735b6f7a56a62ade2c399", "name": "commander", "dist-tags": {"2_x": "2.20.3", "next": "13.0.0-0", "latest": "14.0.0"}, "versions": {"0.0.1": {"name": "commander", "version": "0.0.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4d4128672182d377fa53618d31282a985eeb0298", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.1.tgz", "integrity": "sha512-tCOJMg2UJ7N/ai7PbIb09Ae5gwURekJ0MPmgu0u8n1Ur1I9dX8ids/QWNw3B281xTGKuSi8zINkxEDp7TjM7xg==", "signatures": [{"sig": "MEUCIGzAgD29K4V+7Zbk/4+4T+MYkiC1f2xhdJ4H1tNVjhmpAiEAuMdFu1Dhd9x0EygujXNQtxGMu3XzirZWwZ7DbPIcs+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "0.4.x"}, "scripts": {}, "_npmVersion": "1.0.14", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.0.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.0.3": {"name": "commander", "version": "0.0.3", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9feeaa41be6cd27a5682218cb986773e25b49525", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.3.tgz", "integrity": "sha512-AG38Y14Bs0PgWBao5p/7c9fM0k5lG6uH00KBiX7zqaSiyR74wyh8vSkR6BqryPrLZ14FBvlHCcQmjYWqN+05qw==", "signatures": [{"sig": "MEQCIE7lQBger02i/TP2JJv3H29uEIzhB8cMW+HNDgxdiJcYAiBa3zN5Ypv6WHH0sd9lN7vI4sWCSH9d+GN4OJmz0hfGlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "0.4.x"}, "scripts": {}, "_npmVersion": "1.0.24", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.0.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.0.4": {"name": "commander", "version": "0.0.4", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "72206c96453f4475c0a6e0f041707b217bef8331", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.4.tgz", "integrity": "sha512-oEmRSRK81PzVDy8BNNXfxv/QzOf+fGKeJ9Jq7Lzxljsx4QH7wEQQA92ZOe2C8GcLNRW8c3wVRcVJDRVRFpQMVA==", "signatures": [{"sig": "MEQCIGl1Mf3LQS5FrJNlVVFYJX8gCJ054/3FlZGt0KWHrkcqAiBPLvLSHZFmZdPavSLMfW3YzkeOLK6Q+H9M5JTRyY6T+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "0.4.x"}, "scripts": {}, "_npmVersion": "1.0.24", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.0.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.0.5": {"name": "commander", "version": "0.0.5", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.0.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7824fe04d5357f6dba0045fba86fffcfc843ebfd", "tarball": "https://registry.npmjs.org/commander/-/commander-0.0.5.tgz", "integrity": "sha512-Uet84SNbwy+qsSU+hDhExwhWoAn65y9W1+w0n9aOTPuMJcVwcH8RpGU7Pa9uiZqK2d/Kf50SW3drsMNtNWnLqA==", "signatures": [{"sig": "MEQCIAHfxT5VyvMjNU24ijiY/18Ip70CYCyWJyqg3amZ+HVLAiBowFwal56IUprnfiuSyQzHrW7gt2X6/VkS6W1jKoYJaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "0.4.x"}, "scripts": {}, "_npmVersion": "1.0.24", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.0.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.1.0": {"name": "commander", "version": "0.1.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4f1b767116853b659106f9cf5897c8bac2c189b2", "tarball": "https://registry.npmjs.org/commander/-/commander-0.1.0.tgz", "integrity": "sha512-Co5D0DJGIK+I2RHwGIWOli42dNU3QakxwZAROH1AN0lD32HguSbgrlrfQFxe5fSM6RK3rBQdgsEWMwQIDIjPpA==", "signatures": [{"sig": "MEYCIQCltoDPf4N7EDWDqy8UBP9h9eWmfsAlyV2gTOiGJo7kWgIhALV7iRcn5Lt9T0vrw3hxtvZzFN2tT7DlQdWeWRFgGCVw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "0.4.x"}, "scripts": {}, "_npmVersion": "1.0.24", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.1.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.2.0": {"name": "commander", "version": "0.2.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "61d495ef9c5d9d4ab0a9d168674822ae07e961cc", "tarball": "https://registry.npmjs.org/commander/-/commander-0.2.0.tgz", "integrity": "sha512-tXycDJ1VgN+9A0DVf3iQvbnZzEf+BCY+ls/nlXQmdEmvnkppjB2pCu+iX1SiCPpRdKXKdrq4kbgnPGMqRJSnQw==", "signatures": [{"sig": "MEUCIF+wOdvB5Rllv4qsudzalN18VDCq9CBqoXvi+PUR6EkGAiEA0AfIUmfSVEnk+pIaWzuUFkoFNKvbRLHiZPWOhBzBl/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "0.4.x"}, "scripts": {}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.2.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.2.1": {"name": "commander", "version": "0.2.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.2.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "32ca3c217ac340082bd70e1326b5bbd41fbc6cd1", "tarball": "https://registry.npmjs.org/commander/-/commander-0.2.1.tgz", "integrity": "sha512-X0rKovMH5Z1nJ1tHvoC5/IR9LOa02F3wLp/VSCqE3JOoruENwoz+gh1hAmieiAdlaEooi//eRCQnOKI1JGva9Q==", "signatures": [{"sig": "MEUCIE+SBRkwrXk1gx5vXewAtZ1sqUsR0gfbBSlZ01Pxd7RXAiEAt4/e2wjR7JxuzOkgA01almlHRIhC8jZTi4fGwqLxEII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "scripts": {}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.5.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/commander/0.2.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.3.0": {"name": "commander", "version": "0.3.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "02cafd95f625df941eb0697b6bb540127c4778a7", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.0.tgz", "integrity": "sha512-PI7k+/YsVGSyqHefMZFaV/YktcBPuEtk9sJhGpd4usOn7Nm3rItEWx7fYns9Adu2JCtkFQk8VBpEzV0fPK7asQ==", "signatures": [{"sig": "MEUCIQCOBX1pxYtKEEiv2pvdXciHphEur6oH+mDvqXzGgOoH1QIgS6gYJyuIQES4UChC65siWXRWyLc2I+wJg1moZUIwdg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.3.1": {"name": "commander", "version": "0.3.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.3.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a70cc95038d614937abf9349b0b94f5491bcd8eb", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.1.tgz", "integrity": "sha512-WH6vNSOAx1eq6wPKK2gxnf3bZ/A89cinni53VfAhhby7EntNP3eyAStVwzF0/jVEqO7/TZ0+ORZyspLZHPzalA==", "signatures": [{"sig": "MEYCIQDl3ej6HqA7Ecf9bU6h4gsYxmADOVpAJrM3lNsh71f+XwIhAI2cgsw3AR4g3uV4tgkhVP179WORTKGB7rUpvnZbSTCz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.3.2": {"name": "commander", "version": "0.3.2", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.3.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8a98a6b590d2abab04892739da8f8577da964961", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.2.tgz", "integrity": "sha512-wCUu0gLg2JakdzAOPenfK1TIOot7sMjfnCe8A6St7F0RTaMytPrP1ZDn6dfVu0xZIsshGwNZj8RJlsRvMPMAmQ==", "signatures": [{"sig": "MEQCIBlt1i92agTzdv6pqmIsqW2zYMWICVm1pAlxK5e4FbfTAiBpd1TA/NwzV/7XPiBQ5BNUsNDDRy0JuENllH+nnfrjWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.102", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.3.3": {"name": "commander", "version": "0.3.3", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.3.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "388a4097f857e9299c26415352b54d0706b06a2c", "tarball": "https://registry.npmjs.org/commander/-/commander-0.3.3.tgz", "integrity": "sha512-xDvGtjXnFJnbD74oS0G4bb/Skbuf1SVt33qKXhxXX0eubIsbk/iNzF484OyAIXM74lg1VcnE8eqcHu6mo3MIJQ==", "signatures": [{"sig": "MEYCIQCa2YH0kWKnwuDMc1iDmVKefmNUU0aV5r8xsHQOEVbtKAIhANzT1asskhJ3Gl5kG4xO0UV2wYj8ZB2pzJlC2gkG54Hq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.4.0": {"name": "commander", "version": "0.4.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.4.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "85f193a56264f4959401bdbbce0bb09e5a8764cf", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.0.tgz", "integrity": "sha512-M3OgolicFDe3+ttllzBqekTBHNJeuljdx4aH5AczHCZYFaCA/zyO4TyHYC/9yjuBNM67MFtrEGKl1oyJPLrvrg==", "signatures": [{"sig": "MEYCIQDw4QT48TQtQd+qmVtgqbLKuQgUvuXGFDXGARBQ42vEPwIhAIgJtnry1zo9JU3ZJAYhgbW129UD8sWapvA9Yig8gaFD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.4.1": {"name": "commander", "version": "0.4.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.4.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "85c30d8e80fb57de9a95ae9bd5084021abc1dfdf", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.1.tgz", "integrity": "sha512-aNQ4eVztznBD1VizefwNwII4wCft/LbvLSXeAi8uvz12lHDA7J2ZCytxPmmE0hgqXxuv/SbdDWO9SrrKuu23Jw==", "signatures": [{"sig": "MEUCIAi3yV/utZHTNWn5WIVsd6wdCm2BcP41dB/0JeTWblP5AiEAt9yxniB+Bdkn+SNspQ/Q+ndWhYvPYuMurUbb8qd6trk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.6.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.4.2": {"name": "commander", "version": "0.4.2", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.4.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f1872070e42d271a2a1c419981628628716ce01c", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.2.tgz", "integrity": "sha512-tJhzOn6s7lwgMtAYWifuRPwSsINVvtm+4dN4xEMbHpIeTXjeL4pKefUGpYXfHx1Z5jq3HS2wbnJpttmkwVAW2A==", "signatures": [{"sig": "MEUCIHy+YPwOCPWT/VzDHmTs+TjAITYuko25Rnmf9SCYuGooAiEA1Ak3udBVwb0lrEgBq51hbMWUXd/3yOF/i91pbw514Yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.104", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.6.2", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.4.3": {"name": "commander", "version": "0.4.3", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.4.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1f9c45f5c2d314c4bc9f9a3dd5b883261fbac8fc", "tarball": "https://registry.npmjs.org/commander/-/commander-0.4.3.tgz", "integrity": "sha512-qakepITbqSBLqFR7G8cTiN3c+cUuZCtuGYiPddr+K1SZh4y2i+UII8uRaX39WjuRUAP5sP8oO4RokJ1WHjfrGw==", "signatures": [{"sig": "MEUCIQCEYE7UFWVjX0KYigcMmpKYGtFT+9F4pst+u8LER3xp1AIgHZCsL7vThs6HGPuaqOgM9kI9pK2zIoD/a+dibUjNBCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.105", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.5.0": {"name": "commander", "version": "0.5.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.5.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8fe03c71e444891dbda97c7dfbb108a33a05eaf3", "tarball": "https://registry.npmjs.org/commander/-/commander-0.5.0.tgz", "integrity": "sha512-+3agkFmJqFgJWwcPJtADylbCFa8x8RkJ5OQly1Fg6bTZoLQcpL9GcLUhJ77pvqENav5C04b5eQ+kMHBv//y9Vw==", "signatures": [{"sig": "MEUCIQCx46w6+2p36L7EmkE0K3JPz43SFqetUgnk3o7WhPsu7QIgdCbqlvSLa/6/wrHku37hAlhDi5OwA0GJn08+sj51Zy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.105", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.5.1": {"name": "commander", "version": "0.5.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.5.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "08477afb326d1adf9d4ee73af7170c70caa14f95", "tarball": "https://registry.npmjs.org/commander/-/commander-0.5.1.tgz", "integrity": "sha512-oRF4uekGyV5otUrGRrvUUTNSZQ7R8own3s8pwAMHt6au/DF6o7CDVDoI7jl+tPzHdJS2tiAbLKqZwOj5YA5+wg==", "signatures": [{"sig": "MEYCIQCBPztQg4bgtBT7RWSphuJNgHsZ1MuBqBk5M/5fkHm9kAIhAOwzTDwoQBvCq/N2Q3rLVn8vkriPKzwTVrrYQ0qh5PvI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true}, "0.6.0": {"name": "commander", "version": "0.6.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.6.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "778f617d8a485268b0e06c02576d5a349aa25a9d", "tarball": "https://registry.npmjs.org/commander/-/commander-0.6.0.tgz", "integrity": "sha512-Dk8oKrZqmQsosZ0OmnO3cOpHY4t22WfNnUA+wbLhkk+TQtbgbLx4D0ImkhwVXZikGJsmOzb5ZymtszUMU1+hNg==", "signatures": [{"sig": "MEUCIQC15uOW+Z//ySvEc9lTyUpjjqwk4EKH4UFd+PXwzWdpfgIgAaC1DK8wlDX0rUHrMiWlH0MZGiuA4nhaCK2xyLIOdYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x < 0.7.0"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true, "optionalDependencies": {}}, "0.6.1": {"name": "commander", "version": "0.6.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.6.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fa68a14f6a945d54dbbe50d8cdb3320e9e3b1a06", "tarball": "https://registry.npmjs.org/commander/-/commander-0.6.1.tgz", "integrity": "sha512-0fLycpl1UMTGX257hRsu/arL/cUbcvQM4zMKwvLvzXtfdezIV4yotPS2dYtknF+NmEfWSoCEF6+hj9XLm/6hEw==", "signatures": [{"sig": "MEYCIQDMySj/ZB9OsSCc1J+g8qMePktWD6Xt1SvvgXNGCZ2Y3wIhANHeUxwjwc0r6hUK0s/TSla+He+z+Y2vjSulFOQ1XIa2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"should": ">= 0.0.1"}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.2": {"name": "commander", "version": "0.5.2", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@0.5.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f270326709a115a126cfed5623852439b8e4a3b5", "tarball": "https://registry.npmjs.org/commander/-/commander-0.5.2.tgz", "integrity": "sha512-/IKo89++b1UhClEhWvKk00gKgw6iwvwD8TOPTqqN9AyvjgPCnf9OrjnDNY3dPDOj+K+OhN9SRjYQH0AfX0bROw==", "signatures": [{"sig": "MEUCIQC/SXfP61fjPzpKIMOQVDj2X1lolGDBYG+P4To4eeAkRwIgR5wgONaSyb71s085st8/qCadUvthi9dmgPCmhqyAGTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {}, "devDependencies": {"should": ">= 0.0.1"}}, "1.0.0": {"name": "commander", "version": "1.0.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5e6a88e7070ff5908836ead19169548c30f90bcd", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.0.tgz", "integrity": "sha512-ypAKENwAvjA+utibuxSPeduXV/tIX73+9IyWMkFNnbxiJTeY2xdcM8C2KZo3KEGlDnO5tSm2BVZ65QfuRcR8DQ==", "signatures": [{"sig": "MEUCIQCHaF49gjzKU1z+PJFCNqRUnzfMFVFu0/Rr3/wqz+ZEQAIgWopGzE4jaxZ0HxlV25Nh4Woe2emd2APMQUz18aN6OI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {}, "devDependencies": {"should": ">= 0.0.1"}}, "1.0.1": {"name": "commander", "version": "1.0.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e2c18dc9b8f7ce51185b248271890b1af62cceaf", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.1.tgz", "integrity": "sha512-3ObOU/JSXO2XoVNLoBkeh0mMB9UHFCnZLfR5by6rrPbY5mewLT2+QCZG7ZMJyXgBmhuvevgIzu4j6CbSe0F02A==", "signatures": [{"sig": "MEUCIHMlqR1xbFaKa4fJiPMXP9mHD0SIsEHoRcRxbz+8eq9gAiEAjJ32wIgJwbkOK3QCrR9jy9PhhNrVAxe721JDDZRpkm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.4.x"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {}, "devDependencies": {"should": ">= 0.0.1"}}, "1.0.2": {"name": "commander", "version": "1.0.2", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b9443ef3a966fb3a77d62f2d92dc5a06f1516116", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.2.tgz", "integrity": "sha512-QWuZAG0YliuK5KKerIxHFeMd0Oc5uxQ5fOME1QPkBbZgm+io5uKe0WvP31xAWQm+Fse9Jpo6PzpTvAv7fGBinA==", "signatures": [{"sig": "MEYCIQD4qCyHi6Lya8kU20d2PLfEMMusY0zB6ANcHbgoLQaLIAIhAJi6ohcfdDX+xCutA/Tio9wbveP18CbXuLrQ5F4APUqh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.0.3": {"name": "commander", "version": "1.0.3", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "037451a770f85c2fbb760e2911757fd79a366e2a", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.3.tgz", "integrity": "sha512-nRE7Lp7Y7rBdpvV/XsrltPpKUqjgLh+2ZYpUkzh0kkZm1UZnBlNC5gAZMEY87PMeJ/aLaAnEzYTT/YF8XHaaGQ==", "signatures": [{"sig": "MEUCIH+PVNyPsS+EwIYEVLzO3E+f9hEO4Ipkqyp7pRmjZC1ZAiEAiXaZvEi4bCvOMhv401mOmBP7CGZWeJGrts1esItZyBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.0.4": {"name": "commander", "version": "1.0.4", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5edeb1aee23c4fb541a6b70d692abef19669a2d3", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.4.tgz", "integrity": "sha512-Xz0JOF7NqSubDnWmw7qvX1FuIpCsV62ci/gkpa2NFlm+roeMniBtbxK8QePjs762ZGsuhKaGgcb83eaBiSJ16A==", "signatures": [{"sig": "MEYCIQD6FouWG4kemFayjI+TOgAat3QpbrUywN2UANvbB3au7gIhANbtGBWlam3E2+PIorkMJwCsZCuh+xvTIkjrCBN6rZa8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.0.5": {"name": "commander", "version": "1.0.5", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.0.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "457295bb976e388e9dd0db52de4333e249f3d88c", "tarball": "https://registry.npmjs.org/commander/-/commander-1.0.5.tgz", "integrity": "sha512-Iil6cZ1vitahfQSTrGO3L4v3dtvnfyGpKkXN+aJV9uR24JYxhM9bUfBLat65nU7cIXzOcnkjGtfdCuqaO1caIQ==", "signatures": [{"sig": "MEUCIBdSiTvLb6sQpvUSek12wLgAtkHlrGe8+LfqBN1M+L9bAiEAr4stJ51UykZlnvpgsldu2sDeRiVVn9ynnxm280OUIG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.1.0": {"name": "commander", "version": "1.1.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "88ab74780346d69a112d2efd30f2f4132624af67", "tarball": "https://registry.npmjs.org/commander/-/commander-1.1.0.tgz", "integrity": "sha512-R2HfyKOSPaYYmVcSSALM306VgeflxlmunyCibHIEPif4WGVA74+GRLygh7fadAPyGqR1aNz0ECDJthxu6TFTeg==", "signatures": [{"sig": "MEYCIQDGGyOhTseyIY5U2EiUMIMOM82WLHIvmCVktRhxU7YeugIhAMS4gk9wdAKchZShCQ/9rpIndI93jJeqCC1aFPLA7Exb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.1.1": {"name": "commander", "version": "1.1.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "50d1651868ae60eccff0a2d9f34595376bc6b041", "tarball": "https://registry.npmjs.org/commander/-/commander-1.1.1.tgz", "integrity": "sha512-71Rod2AhcH3JhkBikVpNd0pA+fWsmAaVoti6OR38T76chA7vE3pSerS0Jor4wDw+tOueD2zLVvFOw5H0Rcj7rA==", "signatures": [{"sig": "MEQCICVLzbdrkLYBmbOvLighz535ECbp65ULO9cDSYj5LE1yAiBfINdCoSNSzcHH9yTzhpPCkWLVDzSaiVcel4QWpe2QkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.2.0": {"name": "commander", "version": "1.2.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd5713bfa153c7d6cc599378a5ab4c45c535029e", "tarball": "https://registry.npmjs.org/commander/-/commander-1.2.0.tgz", "integrity": "sha512-4AzfHvT/zLkvp+LVOHxZ02sHTuNrtoTnu8qEoJbpcL3nTnFhoNmAml1UV+96k9y5Tgz7jIjsom546WIi2iif0g==", "signatures": [{"sig": "MEUCIFE2Z8Ht81n0/PAd02vfF/f9wNLd4aZvAdkyJDPUm361AiEAiP02We2PpXuoqJDxneLWn8gbteiXPDsvrEE7AmM+/oc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.3.0": {"name": "commander", "version": "1.3.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "01e9f594426491a8baa85ebece3366685e0a031e", "tarball": "https://registry.npmjs.org/commander/-/commander-1.3.0.tgz", "integrity": "sha512-ndfuM4otja+nsHzAv8uZ7a/qD5tdV7prBCc0M8ipK7fYNO7GOOd3IkBHTHJWOP61aQ/79PCqfgm9wagqI4RBFA==", "signatures": [{"sig": "MEUCIDnrlL/vFCR/qS/acvjBSwYcdpULM8vKvJ1OfzfK2f75AiEA2IBccO5vBNd0SZBI5ITD5U+qOiOfasyiul/KM8XN9DU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.3.1": {"name": "commander", "version": "1.3.1", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.3.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "02443e02db96f4b32b674225451abb6e9510000e", "tarball": "https://registry.npmjs.org/commander/-/commander-1.3.1.tgz", "integrity": "sha512-tpQAw1M5L/v+kutCmx0ceB6WOLeJR+KZyj+g1tLvJUsA5ilaMqqM+90oIndVIGQnpB/IwcMmc1ov22KL08Cqhw==", "signatures": [{"sig": "MEUCICjR8zWasLb68sbJbJAkw0F5QkStBXFMrpjl1yZYTLVtAiEAsUNjRmC+ya3pB9+oZrAYBL9/weqzSAXHefTxu1aAV4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "1.3.2": {"name": "commander", "version": "1.3.2", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@1.3.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "8a8f30ec670a6fdd64af52f1914b907d79ead5b5", "tarball": "https://registry.npmjs.org/commander/-/commander-1.3.2.tgz", "integrity": "sha512-uoVVA5dchmxZeTMv2Qsd0vhn/RebJYsWo4all1qtrUL3BBhQFn4AQDF4PL+ZvOeK7gczXKEZaSCyMDMwFBlpBg==", "signatures": [{"sig": "MEYCIQD7dBM2NL5oir2YzuXlqAa6L7Wvs/rUVraJIxI7BPuAnAIhAIq+WRJSEx/rxzLyti/xYyQk1HOKIRKJ6jR/FbYTc4tY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "the complete solution for node.js command-line programs", "directories": {}, "dependencies": {"keypress": "0.1.x"}, "devDependencies": {"should": ">= 0.0.1"}}, "2.0.0": {"name": "commander", "version": "2.0.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@2.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "d1b86f901f8b64bd941bdeadaf924530393be928", "tarball": "https://registry.npmjs.org/commander/-/commander-2.0.0.tgz", "integrity": "sha512-qebjpyeaA/nJ4w3EO2cV2++/zEkccPnjWogzA2rff+Lk8ILI75vULeTmyd4wPxWdKwtP3J+G39IXVZadh0UHyw==", "signatures": [{"sig": "MEUCIGaGEY7sLxxXtBt2YKSZ8HUrM54h2Btkv9rKOd2SByWwAiEAsSzmCZAcLvCPFUhiZf4ag0RdspfUlrWJ+VOL+wI8dio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.2.25", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.1.0": {"name": "commander", "version": "2.1.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@2.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/commander.js", "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "d121bbae860d9992a3d517ba96f56588e47c6781", "tarball": "https://registry.npmjs.org/commander/-/commander-2.1.0.tgz", "integrity": "sha512-J2wnb6TKniXNOtoHS8TSrG9IOQluPrsmyAJ8oCUJOBmv+uLBCyPYAZkD2jFvw2DCzIXNnISIM01NIvr35TkBMQ==", "signatures": [{"sig": "MEUCIAGXiOta6KKW+i+JT1vilv6xTV6tjb9iPLglZ32dn+EtAiEAxmk2Vj5BHO2SaCzABW2MTzTxl0/DNllWyyyWboW2x28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.2.0": {"name": "commander", "version": "2.2.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@2.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/commander.js", "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "175ad4b9317f3ff615f201c1e57224f55a3e91df", "tarball": "https://registry.npmjs.org/commander/-/commander-2.2.0.tgz", "integrity": "sha512-U6hBkeIsoeE81B+yas9uVF4YYVcVoBCwb1e314VPyvVQubFwvnTAuc1oUQ6VuMPYUS4Rf1gzr0wTVLvs4sb5Pw==", "signatures": [{"sig": "MEQCIEbJjC8rZ6UE/xmPmg83eLVpHg10sZO2XGwgSqt8dp/NAiAs6UAfpT88b+bG9dh2sga6P984/jvahjSuZy6FjamLKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.3.0": {"name": "commander", "version": "2.3.0", "keywords": ["command", "option", "parser", "prompt", "stdin"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "commander@2.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/commander.js", "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "fd430e889832ec353b9acd1de217c11cb3eef873", "tarball": "https://registry.npmjs.org/commander/-/commander-2.3.0.tgz", "integrity": "sha512-CD452fnk0jQyk3NfnK+KkR/hUPoHt5pVaKHogtyyv3N0U4QfAal9W0/rXLOg/vVZgQKa7jdtXypKs1YAip11uQ==", "signatures": [{"sig": "MEUCIGIltvNKhTdXE5ZSPP5Ey1lYq+M66cIIuqBxvbEIfD7+AiEAl7pVdIOdU5Qa6AR0gY5YtTt2ZekaXTfpSF43kCWlT2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "fd430e889832ec353b9acd1de217c11cb3eef873", "engines": {"node": ">= 0.6.x"}, "gitHead": "7e9f407ec03d4371a478c2fe417db4998ecb6169", "scripts": {"test": "make test"}, "_npmUser": {"name": "somekittens", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.4.0": {"name": "commander", "version": "2.4.0", "keywords": ["command", "option", "parser", "prompt"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.4.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/commander.js", "bugs": {"url": "https://github.com/visionmedia/commander.js/issues"}, "dist": {"shasum": "fad884ce8f09509b10a5ec931332cb97786e2fd6", "tarball": "https://registry.npmjs.org/commander/-/commander-2.4.0.tgz", "integrity": "sha512-TK/kiMz3F635LzTAaPIKzoBmnRiKX5/0zlzc4DytFvItRvXXcjCxfw1QXa9u4S+eeb+jKCaGlKSFcV+j3cRA7g==", "signatures": [{"sig": "MEYCIQC6lq8m91qW5vF2nH7m9SlE17WX5DPKWVhAE131IihX2gIhAOTqQgadZwVXN8GypaDXSpqoZD/tefCbd8E8R+gE7D0+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "fad884ce8f09509b10a5ec931332cb97786e2fd6", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/visionmedia/commander.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.5.0": {"name": "commander", "version": "2.5.0", "keywords": ["command", "option", "parser", "prompt"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.5.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "d777b6a4d847d423e5d475da864294ac1ff5aa9d", "tarball": "https://registry.npmjs.org/commander/-/commander-2.5.0.tgz", "integrity": "sha512-OWTkyOHhPiThmSv9jA4mIx9Bf27sG8MZb/c7FR5nuOEuQzCIzIaUAIopbDODfg+CBem64FtiYj8t+G+3NGuQZw==", "signatures": [{"sig": "MEYCIQCJhdDL6/IbbjGrk9Y/sfkViipZQYE8iM1R4GWbQXFaxQIhALoUobJHOr0W2+LR0bUJjuVonaShCULOR0wPMuI9UZSL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "d777b6a4d847d423e5d475da864294ac1ff5aa9d", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.5.1": {"name": "commander", "version": "2.5.1", "keywords": ["command", "option", "parser", "prompt"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.5.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "23c61f6e47be143cc02e7ad4bb1c47f5cd5a2883", "tarball": "https://registry.npmjs.org/commander/-/commander-2.5.1.tgz", "integrity": "sha512-1eyhrbxuz9laj/ZA8OlTxfmBy7Xy99Fw1aubQpL2swXikVEZIaJzjwDmbGKoVOdsU2pMg9sNmoEa93mhKsdlbw==", "signatures": [{"sig": "MEYCIQDyEukhFzEYgazRPd1QmRmnCoC33o0NCVIP+2wPIncjpQIhAIC8jLFMm99/G1KnJkRlMpvtmx2xn9DFrJpvWWifdW5l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "23c61f6e47be143cc02e7ad4bb1c47f5cd5a2883", "engines": {"node": ">= 0.6.x"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "the complete solution for node.js command-line programs", "directories": {}, "devDependencies": {"should": ">= 0.0.1"}}, "2.6.0": {"name": "commander", "version": "2.6.0", "keywords": ["command", "option", "parser", "prompt"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.6.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "9df7e52fb2a0cb0fb89058ee80c3104225f37e1d", "tarball": "https://registry.npmjs.org/commander/-/commander-2.6.0.tgz", "integrity": "sha512-PhbTMT+ilDXZKqH8xbvuUY2ZEQNef0Q7DKxgoEKb4ccytsdvVVJmYqR0sGbi96nxU6oGrwEIQnclpK2NBZuQlg==", "signatures": [{"sig": "MEQCIDCGoA2tUfZIea/I/MZBlWbPyCTiyK6c5wuZkoaMTMvJAiA+QPn2Lmb6+ReKe9Oeb5Xuej4pnXPE8dGSDDUqRZepkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "9df7e52fb2a0cb0fb89058ee80c3104225f37e1d", "engines": {"node": ">= 0.6.x"}, "gitHead": "c6807fd154dd3b7ce8756f141f8d3acfcc74be60", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "2.1.12", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "0.11.14", "devDependencies": {"should": ">= 0.0.1"}}, "2.7.0": {"name": "commander", "version": "2.7.0", "keywords": ["command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.7.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f3d8e36f6fcb32e663cabb70689a59ea847433b1", "tarball": "https://registry.npmjs.org/commander/-/commander-2.7.0.tgz", "integrity": "sha512-oiMhn3QQ6ySav1b5daL0mPYfFhqlGrlrwC9nPzwSWBiRtu4O2Ucx390mFe1H4hCxccwV7RgrYkk2pGIaXRttAA==", "signatures": [{"sig": "MEUCIQCbatr+I0zc0Dn+0NPTQSdP2njO2UhAKnnN/6bpMm54vwIgGqgjw0XRP8FqSIU/IkBPbvyvmPIAyDF+SZZuYWirgFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "f3d8e36f6fcb32e663cabb70689a59ea847433b1", "engines": {"node": ">= 0.6.x"}, "gitHead": "481e94381a0997260b867c066f1a8ac02b45e290", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "2.1.17", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"should": ">= 0.0.1"}}, "2.7.1": {"name": "commander", "version": "2.7.1", "keywords": ["command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.7.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "5d419a2bbed2c32ee3e4dca9bb45ab83ecc3065a", "tarball": "https://registry.npmjs.org/commander/-/commander-2.7.1.tgz", "integrity": "sha512-5qK/Wsc2fnRCiizV1JlHavWrSGAXQI7AusK423F8zJLwIGq8lmtO5GmO8PVMrtDUJMwTXOFBzSN6OCRD8CEMWw==", "signatures": [{"sig": "MEUCIGNVl4rt/BzpIHmUeYp6lNaV9OLCQs9eckBEjXPr+qhcAiEA4MEUWNGb9mPG75Q2XYius5H+Fuw/c0D0SLgpEYBKhuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "5d419a2bbed2c32ee3e4dca9bb45ab83ecc3065a", "engines": {"node": ">= 0.6.x"}, "gitHead": "103654f8f32c010ad1e62cefc9ab92d7c8d18c8e", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "2.1.17", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"should": ">= 0.0.1"}}, "2.8.0": {"name": "commander", "version": "2.8.0", "keywords": ["command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.8.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "117c42659a72338e3364877df20852344095dc11", "tarball": "https://registry.npmjs.org/commander/-/commander-2.8.0.tgz", "integrity": "sha512-cwCeOWJKgop/zBQ+L0iRuerHLaGtToAJ7F19k9E5d75tUwANc26J/yIwmzSftGo1oMRbRuK6h7h1SLTSU+wJ5w==", "signatures": [{"sig": "MEQCIB9p2VPzTWoSTBpCecNoLpvC8fYjOfKRq7kdKBSiSK/PAiAnDfReQEB9pxRAqQJQnV5q4rmN6ipaQ+tgo6THWjQWWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "117c42659a72338e3364877df20852344095dc11", "engines": {"node": ">= 0.6.x"}, "gitHead": "4dae21d9336eb225ef20deadbd12347d278390d8", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">= 1.13.0", "should": ">= 0.0.1"}}, "2.8.1": {"name": "commander", "version": "2.8.1", "keywords": ["command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.8.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "thethomaseffect", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "06be367febfda0c330aa1e2a072d3dc9762425d4", "tarball": "https://registry.npmjs.org/commander/-/commander-2.8.1.tgz", "integrity": "sha512-+pJLBFVk+9ZZdlAOB5WuIElVPPth47hILFkmGym57aq8kwxsowvByvB0DHs1vQAhyMZzdcpTtF0VDKGkSDR4ZQ==", "signatures": [{"sig": "MEYCIQCJXrJSl227y8cgT2Ozzq4PTCu08kuQPvJzV9KnBsJ39AIhALr2Ubz1gXUz5dI+Ab4jdSx2S20G8X6FsJo2kn8S8UeO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "06be367febfda0c330aa1e2a072d3dc9762425d4", "engines": {"node": ">= 0.6.x"}, "gitHead": "c6c84726050b19c0373de27cd359f3baddff579f", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">= 1.14.1", "should": ">= 0.0.1"}}, "2.9.0": {"name": "commander", "version": "2.9.0", "keywords": ["command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.9.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "9c99094176e12240cb22d6c5146098400fe0f7d4", "tarball": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz", "integrity": "sha512-bmkUukX8wAOjHdN26xj5c4ctEV22TQ7dQYhSmuckKhToXrkUn0iIaolHdIxYYqD55nhpSPA9zPQ1yP57GdXP2A==", "signatures": [{"sig": "MEYCIQDYupOTtVbmZEwIy3Wac7LgNqK5nnVlLyw6Ks/QxHK4KwIhAMcbIu0L9YM+ipy0oufE50poN4Or11qW+bWHNgLRQKKC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "files": ["index.js"], "_shasum": "9c99094176e12240cb22d6c5146098400fe0f7d4", "engines": {"node": ">= 0.6.x"}, "gitHead": "b2aad7a8471d434593a85306aa73777a526e9f75", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">=1.17.1", "should": ">= 0.0.1"}}, "2.10.0": {"name": "commander", "version": "2.10.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "e1f5d3245de246d1a5ca04702fa1ad1bd7e405fe", "tarball": "https://registry.npmjs.org/commander/-/commander-2.10.0.tgz", "integrity": "sha512-q/r9trjmuikWDRJNTBHAVnWhuU6w+z80KgBq7j9YDclik5E7X4xi0KnlZBNFA1zOQ+SH/vHMWd2mC9QTOz7GpA==", "signatures": [{"sig": "MEQCIFSW3bgO12KwSYywh+z2c4J8hbaroheeI3GuO7PduGmKAiB7MX7ovG4D5pDXxAezlJL2fdIj6DASzNalwLGbyq84ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js"], "engines": {"node": ">= 0.6.x"}, "gitHead": "8870675aa189d84014779b53760544a0e614cb40", "scripts": {"test": "make test"}, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "devDependencies": {"sinon": ">=1.17.1", "should": ">= 0.0.1 <9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.10.0.tgz_1498210375405_0.9538901860360056", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "commander", "version": "2.11.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "157152fd1e7a6c8d98a5b715cf376df928004563", "tarball": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "integrity": "sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ==", "signatures": [{"sig": "MEYCIQCxqdSlCyLiYmSJxsu6oGK3QLXfRzOvJK12IA2mpc+UQQIhAO+JHieh4ntdnqCq+pTSJ7kdCSg2OtZ1VZIBYtC3i5CI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js"], "gitHead": "30535a67a7d1f3809231603bc4dc0ba873ae85ef", "scripts": {"test": "make test"}, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {}, "devDependencies": {"sinon": "^2.3.5", "should": "^11.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.11.0.tgz_1499076445115_0.39661598461680114", "host": "s3://npm-registry-packages"}}, "2.12.0": {"name": "commander", "version": "2.12.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "2f13615c39c687a77926aa68ef25c099db1e72fb", "tarball": "https://registry.npmjs.org/commander/-/commander-2.12.0.tgz", "integrity": "sha512-0FAmW4svUhnHJzjJHrg0vHi8+3Wp5mqvZTOui03Tc0515CToaw1BD7WC8ROcY08UnTJJOr4essVYvXBSPYeV2w==", "signatures": [{"sig": "MEUCIAGaJCZfc87GzQwB7O3BzfN2yBCS6KFll4stUTUPG2hNAiEA1HzSbjGkcXO6moVpwBrmW6ofNfg+6jb+Kew8vNi+nQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js"], "gitHead": "7e22f38a09a0975ec2da2e0eda13cb7fdac370f0", "scripts": {"test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"@types/node": "^7.0.48"}, "devDependencies": {"sinon": "^2.3.5", "should": "^11.2.1", "typescript": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.12.0.tgz_1511391893498_0.24965589377097785", "host": "s3://npm-registry-packages"}}, "2.12.1": {"name": "commander", "version": "2.12.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "468635c4168d06145b9323356d1da84d14ac4a7a", "tarball": "https://registry.npmjs.org/commander/-/commander-2.12.1.tgz", "integrity": "sha512-PCNLExLlI5HiPdaJs4pMXwOTHkSCpNQ1QJH9ykZLKtKEyKu3p9HgmH5l97vM8c0IUz6d54l+xEu2GG9yuYrFzA==", "signatures": [{"sig": "MEQCIBk9cjIYR7V3KUzEOq8UYbYiFhLwd39Nh/v4oRnbWYiEAiBr9UgcL82AZeG7VczEbtcDIOhGFor6c1fKOeUHK8rXsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js"], "gitHead": "91c2514f849722eac57c38c2c63f0c0dac9b59c8", "scripts": {"test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {}, "devDependencies": {"sinon": "^2.4.1", "should": "^11.2.1", "typescript": "^2.6.1", "@types/node": "^7.0.48"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.12.1.tgz_1511432330735_0.8460609647445381", "host": "s3://npm-registry-packages"}}, "2.12.2": {"name": "commander", "version": "2.12.2", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.12.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "0f5946c427ed9ec0d91a46bb9def53e54650e555", "tarball": "https://registry.npmjs.org/commander/-/commander-2.12.2.tgz", "integrity": "sha512-BFnaq5ZOGcDN7FlrtBT4xxkgIToalIIxwjxLWVJ8bGTpe1LroqMiqQXdA7ygc7CRvaYS+9zfPGFnJqFSayx+AA==", "signatures": [{"sig": "MEYCIQCZIhfd7Xjw4kYrFRAvABDydO6oARwHdrntQItUvH4tiQIhAKTX4s0WbTP8pXdwKFk62oq/Y36o7QCH3Ops8iMQX2aV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "6864c953d781d4f665afecbdace84d9d80e45060", "scripts": {"test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {}, "devDependencies": {"sinon": "^2.4.1", "should": "^11.2.1", "typescript": "^2.6.2", "@types/node": "^7.0.48"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.12.2.tgz_1511852934826_0.43355596787296236", "host": "s3://npm-registry-packages"}}, "2.13.0": {"name": "commander", "version": "2.13.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "6964bca67685df7c1f1430c584f07d7597885b9c", "tarball": "https://registry.npmjs.org/commander/-/commander-2.13.0.tgz", "integrity": "sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA==", "signatures": [{"sig": "MEUCICJ11+5N4BEtFBphGC8BftaF2IEYHLz0D8FkTO2Xm/GOAiEA1RR/WIpdcq4FeorFTuby36+DsOGsjXUsGnWGdfFAz+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "291fbaa61e3704ec30617812ab6646c8443a03f0", "scripts": {"test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {}, "devDependencies": {"sinon": "^2.4.1", "should": "^11.2.1", "typescript": "^2.6.2", "@types/node": "^7.0.48"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.13.0.tgz_1515654595288_0.4785951259545982", "host": "s3://npm-registry-packages"}}, "2.14.0": {"name": "commander", "version": "2.14.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "7b25325963e6aace20d3a9285b09379b0c2208b5", "tarball": "https://registry.npmjs.org/commander/-/commander-2.14.0.tgz", "integrity": "sha512-okPpdvdJr6mUGi2XzupC+irQxzwGLVaBzacFC14hjLv8NColXEsxsU+QaeuSSXpQUak5g2K0vQ7WjA1e8svczg==", "signatures": [{"sig": "MEYCIQCNOKtsUMm+0OC26EpAOVQJV7lw+A0Mfjx+MQWIS55/OAIhALz2zGKRMCbrCeumzTAkR31J4S7jhi7IH6S0NwvlkZbq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "de4af3abbe9baa6dd60dce614e2c94b615c603a5", "scripts": {"lint": "eslint index.js", "test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {}, "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.1", "@types/node": "^7.0.52"}, "_npmOperationalInternal": {"tmp": "tmp/commander-2.14.0.tgz_1517880226071_0.43818329833447933", "host": "s3://npm-registry-packages"}}, "2.14.1": {"name": "commander", "version": "2.14.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.14.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "2235123e37af8ca3c65df45b026dbd357b01b9aa", "tarball": "https://registry.npmjs.org/commander/-/commander-2.14.1.tgz", "fileCount": 6, "integrity": "sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw==", "signatures": [{"sig": "MEUCIGv0LqMLRutJqlMQSqy2Z8tCnLh3JWR+k0FYqXt2b/RWAiEA7OLkq2XI8Qit/97G/Bhu5EtXfMJmNU+WTwm4AwKwzm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58015}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "6b026a5c88a2c7f67db70831c015e9d11c7babca", "scripts": {"lint": "eslint index.js", "test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.1", "@types/node": "^7.0.52"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.14.1_1517989378540_0.7122613806538618", "host": "s3://npm-registry-packages"}}, "2.15.0": {"name": "commander", "version": "2.15.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.15.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "ad2a23a1c3b036e392469b8012cec6b33b4c1322", "tarball": "https://registry.npmjs.org/commander/-/commander-2.15.0.tgz", "fileCount": 6, "integrity": "sha512-7B1ilBwtYSbetCgTY1NJFg+gVpestg0fdA1MhC1Vs4ssyfSXnCAjFr+QcQM9/RedXC0EaUx1sG8Smgw2VfgKEg==", "signatures": [{"sig": "MEUCIQDTg/dq1YxxVio2+IsYyUqnmnryX5fN9RQWP+Ca9DsduwIgBVWjgpuwZQGjzEhCFpQc1rAUjCxWpq/ItEqSEgiQuBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59781}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "d8404f8de45c9ba780606878f4d35d0a45743d32", "scripts": {"lint": "eslint index.js", "test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.2", "@types/node": "^7.0.55"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.15.0_1520471512609_0.20298682127168233", "host": "s3://npm-registry-packages"}}, "2.15.1": {"name": "commander", "version": "2.15.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.15.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "df46e867d0fc2aec66a34662b406a9ccafff5b0f", "tarball": "https://registry.npmjs.org/commander/-/commander-2.15.1.tgz", "fileCount": 6, "integrity": "sha512-VlfT9F3V0v+jr4yxPc5gg9s62/fIVWsd2Bk2iD435um1NlGMYdVCq+MjcXnhYq2icNOizHr1kK+5TI6H0Hy0ag==", "signatures": [{"sig": "MEYCIQCVzQTQOJSWLqfWMwy/+3ADNmkVcpY30cBG0lxHeX+cDwIhAMwurGXChhtOtwsmb5mA7mQABW/x+daGiFwiUPRBKisA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59781}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "649eaef336ddc7224eb5c73e4a958685e24de25e", "scripts": {"lint": "eslint index.js", "test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^2.4.1", "eslint": "^3.19.0", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.7.2", "@types/node": "^7.0.55"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.15.1_1521510442790_0.20361588610282877", "host": "s3://npm-registry-packages"}}, "2.16.0": {"name": "commander", "version": "2.16.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.16.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f16390593996ceb4f3eeb020b31d78528f7f8a50", "tarball": "https://registry.npmjs.org/commander/-/commander-2.16.0.tgz", "fileCount": 6, "integrity": "sha512-sVXqklSaotK9at437sFlFpyOcJonxe0yST/AG9DkQKUdIE6IqGIMv4SfAQSKaJbSdVEJYItASCrBiVQHq1HQew==", "signatures": [{"sig": "MEUCIQDq2mixK5WslstzP3Nb+QjIbd5DURJV8Msvwd3jVUQ60QIgGWJkqHrw7Ez/6S32VXJP96FN9YV6LbfN76MAQ+Zczfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNf6TCRA9TVsSAnZWagAAeOcP/1GvW++kyIoNTLX/kcWn\n12krx6uUADcgK+gqzipj8GNP2xWVfXO95CiKB99d9bufa/okMMuDVYnUMCjN\ne0Z1ytDg6ZEAG2aQd5eTxUMLGBAkvQXTq3iRmuFwS4syW1RpFir2wXJST+dp\nMukBMYzaMM9Jl+DcLZ2TJqgkmI0nqW1Da/ItMB4wVmSJ21FShHQuCQg2VW1h\nR7d1dDXCUt5uwkmIPxqE7v/o4iGWNlLludzbdqKZs/uddTITq983ASwhxGXG\nFjvAPR5jDwOpalIGgy28blTyV+mHM4XK1QekMxW1eAJuUkD6YSXAIets/v3t\nWlY4Lj3Jq3Q9jWp08iwLiSJjv7MtfMlMZH84zJrqTo5XxVvbl2z0u2DM+Wq+\n93mkWoVVLJpC5m5n7GoCG2rTFrxak7hkOcqA4yAB8IEPbDqRmncYrWwrgTcm\ndWwVUlXe0Ij/ZYOxjZETZls2Ed78MNh0rLgZGo4m87z7wGV2lmDDfdsvwReQ\nw8MR4b0jglgfPQ/bFgo0qXCWOLrfCu4ATPS8DWF7ioAus8H5gNhyrji4ALyJ\ndePUf/IJ7hHj6B9AKQ86RIM8pU4ma2fsSG8EkHZ/7GZtcdivNqAsXY44JPX4\np5Ux6AaC88RqeCcIG+aK7LGR0F1E4phVcyKDhtYXryW3oz+XE+D7lT6GNrw3\nxApX\r\n=gqzQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "4cc348bd9808f799ca0600a39136b1fa25820f3b", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^2.4.1", "eslint": "^4.19.1", "should": "^11.2.1", "standard": "^10.0.3", "typescript": "^2.9.2", "@types/node": "^7.0.66"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.16.0_1530265235564_0.5330625131044013", "host": "s3://npm-registry-packages"}}, "2.17.0": {"name": "commander", "version": "2.17.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.17.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "9d07b25e2a6f198b76d8b756a0e8a9604a6a1a60", "tarball": "https://registry.npmjs.org/commander/-/commander-2.17.0.tgz", "fileCount": 6, "integrity": "sha512-477o1hdVORiFlZxw8wgsXYCef3lh0zl/OV0FTftqiDxJSWw6dPQ2ipS4k20J2qBcsmsmLKSyr2iFrf9e3JGi4w==", "signatures": [{"sig": "MEQCIDec3zI0RitVz7HQJjfAWIeUzxMq8A2Kgq+nGLoieNjFAiAjvzm7YyPJWmlq/r72VzIAcpFJ0kHertC4LecY8vr6WQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZO69CRA9TVsSAnZWagAAPs4QAIHuGLlmIFZIvEdLxDd3\nh349JSxqLuDUfbGRvUD/av80KRl0woSw9gP8MypoS6MNjSrBF1de36uoa+/4\nEvmt71OEWnx5TGUPAwFv3/RxL/rQehFq9v86WpRY+PcRuOue7qLI7v2azRMa\nbJ1zc3KLaJzUnO3B/n28RQww2EK0XH2iDI1XQKM3zwEgfOGJL4pHTWP2af9Y\nHHLX1+n8xBN5/HazRAZabAxMCVxnYf8Yre6PHVuq3kWFV0nvUQS7Sfj8tnvr\nAGGFvto5M062n+Ze0a5gpw43VZ3D0GznidlehiYWUoleLe6d/0vcvotMs93U\nkRtLloc6wMtQiwuvHUuS4JaExSB1rSQRXw/GISrIlODDwbNwHgcfBRYdpfY4\naopWzIz8CWVgQBMJ83EDMHyCEZYQpIEpXgmOD/K1etvfjLWeBoLmV7UFRjZY\nQ5Js8u73VGh15Yvz3QBMLIKQE0xh+HeQzNjCR0WDocGhHp4jWjomSHac/J3B\naMCqqKkxT/qHMuZ8z8cWBZZ/bhjbrDIb1jBUDL5eenCG/fWrd7CJNeJmKnO/\nifLs8GdtAn+U34otn5548cw6w98M3Wi7t4f/lcP2tAgwoNBnKlkLp7nT/Mri\ndbta24L4uNUfVI4gQgWS0GzSi70Br/LP7U8LVIOiGFKEOP/tzNeVxlGv7iUq\nx50C\r\n=bZ8y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "25b06ee12b6f14afa0b4b7b6f028a66a7c4faf5e", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^6.1.4", "eslint": "^5.2.0", "should": "^13.2.3", "standard": "^11.0.1", "typescript": "^2.9.2", "@types/node": "^10.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.17.0_1533341373266_0.9287988805640361", "host": "s3://npm-registry-packages"}}, "2.17.1": {"name": "commander", "version": "2.17.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.17.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "bd77ab7de6de94205ceacc72f1716d29f20a77bf", "tarball": "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz", "fileCount": 6, "integrity": "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==", "signatures": [{"sig": "MEQCIBJfoMf91v3Lk2MPJhn90TS2mBBALaNroH2FX8A/p4fiAiBkFALr9q441VMD1e7FPeqwHx/trotcDrz0+M32cSZ1KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaYBDCRA9TVsSAnZWagAAl2QP/327vj1GeSPmzfzN1vJW\nS6v21X14g4oQ+5+r+EcZ94Q5fgcloOJcR05Ad6yD50uzlHvGjux9dqe4prBg\nkp3jIt58NHz1c6oXmZ5SuVILrIOv3J2xrxoH9OC2ZBwBSejp1iZsEGLOByhR\ns2sV/zjgfrD9FCHT0ZMyRixkp9wFBw5ncfzMnFvu6L952t41e1dgnoJutXQT\ny3F4/f2H9nUSvw5llrkVyhZ2iZZBuZxZzNEgBKxpOwdhHQsnuWpTQ5PUssAL\nnRLc2Kk6Txi5bdvEYziH1yzHq42EPYW9LHWpGiR2SXl+Elbdkcc2sFHU8qd+\nITA2k6GEMo0Evpv9Wt3CACs0OoyShnuNz8J03WYh9Pda8dvZQ2h/NHNzYaGB\nUwMmwiXUL2b8iY490eZQCn4vYvKnGBkiTHXeGVay+BwqGjpbE9NdhSou0OLJ\nJs/wPPOXDJLNELLxs+R4jlk2tn9flYsC1bvz3y9tuQE7hQtYLM5+KXKUucE7\n5u/wbfSHICgLiAbU1ko0E6EnkGxGojeleM4/EYyW8c1sO5Owx4qQRuBEUOUN\n/FREwjY/8/MLI5F5bYhoRhhRQmdgUjRVYlQolj0DG4d0QZBBobsaGcCnw9gz\nroYMYAW1WtEcYhmE807vv2n1nEiUVjePqt5Aunpuf4/heqzRC7jQHtsEDWOX\nzE5d\r\n=rq5H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "gitHead": "e5b27cc553c0c55eb2f8890dc83034d3a3eee531", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^6.1.4", "eslint": "^5.3.0", "should": "^13.2.3", "standard": "^11.0.1", "typescript": "^2.9.2", "@types/node": "^10.5.7"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.17.1_1533640770608_0.9257530317415459", "host": "s3://npm-registry-packages"}}, "2.18.0": {"name": "commander", "version": "2.18.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.18.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "2bf063ddee7c7891176981a2cc798e5754bc6970", "tarball": "https://registry.npmjs.org/commander/-/commander-2.18.0.tgz", "fileCount": 6, "integrity": "sha512-6CYPa+JP2ftfRU2qkDK+UTVeQYosOg/2GbcjIcKPHfinyOLPVGXu/ovN86RP49Re5ndJK1N0kuiidFFuepc4ZQ==", "signatures": [{"sig": "MEUCIQDGoYSS6pmqtUrRd8HaBJxXV5YW0m0skn9Pbaid+CT9KwIgHHu30AUcan7UlWNA1KR/10QGVa4dpzcm+xYQ4SkvK7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbklKICRA9TVsSAnZWagAA3UsP/2wOSXhOIU8UZC7MzbKq\nO1eUAOk5lidElUWiWBAnMv53DD98JGThTERjePa5L9bndPOKeJ/bI3mW4inB\n3Do+gpeXgEP2bTgVUPgZy2kJQhF4Is67p6D9cg/C5zAocyhKq0+m93Q7PKbb\nfpK69lD7T9q88h5+wvwjUCyJbF+tsdDJkgVI0jQEyimXqtPbM1Ici8jrAl/h\nkZakZ/IhwsbAaksX0nKG2Da27KWzAF7VrqkNyHSmhgBHGDG2De3mfOstHo0C\nJYx/WTAlZvPLECzMpF+Q6+tDLAj+XyoXuwYMGSHMjbxg4IF1h1qZMBfxx1yM\nOdpc1aSGb0+NAWqm1ksX7tviAk8laR1VRE7y7EciUN7FTZUYTgNZDQ/r6Qut\nLDuiUEP6NYIRmBJqxH4jbrU8zsaBJw9tutPTGkNWp1LajOq79BR2OGUGgHlI\n4WYRZMbiBmntPxJ5hjKsaz1CY8s3Cxk7y9oDAYGWH57+ddBAxKRY726yG6an\n7i5NbfBqoFe1HNUQQ8qpTLp1Hf/Plt02dIMevscye0oaofH9UBTDd38AmKc3\npYLB1zgF2SQmdpYS2DZkadUSmHtQ2Wwd5uRSBlSVkKklFAZLQVbfsKpLK23c\n6XnXrgplszdTA/fIqxsZR59n9SRaasITF+QTxBNpeg5B9HS6z6nWPIC4ravz\nRXw6\r\n=q4UK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "54b7f142b1fa960442c6c8982da16f7a5204c0a2", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^6.2.0", "eslint": "^5.5.0", "should": "^13.2.3", "ts-node": "^7.0.1", "standard": "^12.0.1", "typescript": "^2.9.2", "@types/node": "^10.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.18.0_1536316039810_0.8329669530575801", "host": "s3://npm-registry-packages"}}, "2.19.0": {"name": "commander", "version": "2.19.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.19.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f6198aa84e5b83c46054b94ddedbfed5ee9ff12a", "tarball": "https://registry.npmjs.org/commander/-/commander-2.19.0.tgz", "fileCount": 6, "integrity": "sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==", "signatures": [{"sig": "MEYCIQDUq51T5Rmhp0/+4T9ufdoOu6hB4Ltc7II8jECcKCIqJgIhAPMVwdgLm91eAn7L1omeB21/AesSBN14hXFZ1DDLfD+N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbu9FSCRA9TVsSAnZWagAAc5YQAIADsKVX+3i2QDdUwZzo\nMltfEFOMP1vIiiD0jgsOe6RWO0AwEIBds7dFc4bV4oRoOraZhE5gMAPGd1Y4\n7jekExbT/VYWZYJg+Pg2PppiD5W2PZRS+0LfGiujcyRlpFIsKKc7dUOEQ4Ty\n+htXGTetO5qeYH10dso+9Rel8dsIAKBMQycKS5cuEk4SQC7VjwalM2KJUeC/\noHRzLUtfw64zGGFoshAOWCdsuuMpKvjOiO70mZbmNF0h4Cqd2gZlzdA/XiFi\nM4//gqKcl2hAa6S2Y6cd6vb4P1azv5S2Go2tARL1OBhuI6ooofhAfmfClDl0\n7lfkGwtKkc/c5RgQLAyaBiT73WsAy7bSFW9HPDZNJvO7y4JdI4ZczSi5JeeQ\nt2/Zw5pnOWrIzygMAvhiXC2c8BC8BQ+8CuxuvCkPzonr4F56fC54JR0ANHWa\nRkwvQVWI3C5T1YX21ZzgsvaXaY460sXEksT89stxLfzN0FfFxHnwjRn3crX8\n6RltbIevb2gT8QyGL1WmwMdp94IkBpw+7LfLrEl5o95v8IAJr6k822A/bFiw\nOYAGTJ9OgtEuJd7N8a1kCEkF7u54Yd9YO9vfPkO6SnEMiENW96wuzFBpv3K/\n/M0+4X8VDPXjEsS8wfRhUYYawhhDcE7/I9nnbngbA66V5Ci3d27ASkx0i6XX\nZ798\r\n=7lGM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "78b7dbd18aabc23ccc9d151db411913237a3c483", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^6.3.4", "eslint": "^5.6.1", "should": "^13.2.3", "ts-node": "^7.0.1", "standard": "^12.0.1", "typescript": "^2.9.2", "@types/node": "^10.11.3"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.19.0_1539035473746_0.9826574892249367", "host": "s3://npm-registry-packages"}}, "2.20.0": {"name": "commander", "version": "2.20.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.20.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "d58bb2b5c1ee8f87b0d340027e9e94e222c5a422", "tarball": "https://registry.npmjs.org/commander/-/commander-2.20.0.tgz", "fileCount": 6, "integrity": "sha512-7j2y+40w61zy6YC2iRNpUe/NwhNyoXrYpHMrSunaMG64nRnaf96zO/KMQR4OyN/UnE5KLyEBnKHd4aG3rskjpQ==", "signatures": [{"sig": "MEUCIQCc/z5c/aj49ttTzeEpU/X07nS7ynhIStYA9HSa3a9CuQIgMiS5h6QVmXwMXk1dEL6nkq0EfA0Op41lGxCMhcEQrRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpAE6CRA9TVsSAnZWagAARXcP/1Dlmv/NEiARtvdTN0Fo\nZoBKmjwWrgyT5oLE1efAg47RdO1zDQLWD2y5pUfW3RgdEPek6cmPTwjnijbj\nF3fuYCChl9ipkbkQMPhfN7eIv13pz8muVXvo9Rt1HF7rQ9jm25+lorzaOHL4\nn/ew0TNvmjzvxEi3cm9hFTYA2QMvGo6GnFh/7Tp1BZcVM7xC6eJRB3sD89vo\npv3CZ0R/JhZkLiKJ1eljTnE7Ch1YL739tUZDFGmkwtko0WPiiKmKuLseTokb\nKlvc6q6Qmy1tAzf8v0gCET/KSUzRXByd1XXaONQX7WNQRBjF4uit1GGxaMVB\ngTNR6lcYPqoLQ+9td4xnxYLKYN3xIKRvzex59PKS79j6D8PW5SwfHJG6mIDm\n3HWkZRHT8MRQcF792ZcwvaeOe69W2fty+7tu4FQxmSQ6ajgSem/mymkePOre\nkBIUx6U3pRq+qo8SuC0xHJaHehUaErhz76VYuJOut9DVRul9QNlD9c/e6Bls\nQ3zgSlrrzGoHddAB9DG+vQaDmvF7twV+oWYfzDxNwxNlOATjR0cXod3230EW\nEzs7vDpx9UrJBhqKAOxga/oYWzqx2GvC5vuXZcnMnNQ+3xejO08gBPWzNEEc\nMIGsO+cU9Kyd+9UJWQRBE7kH/6j5Bh54qyxNskfc+hpJ3UyBgKUJBsUWm5um\nj7Uc\r\n=zhg8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "3e8bf54b9b2fb3960fc2320a4174aa79efca90fa", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "11.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^6.3.4", "eslint": "^5.6.1", "should": "^13.2.3", "ts-node": "^7.0.1", "standard": "^12.0.1", "typescript": "^2.9.2", "@types/node": "^10.11.3"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.20.0_1554252089341_0.9811870313123083", "host": "s3://npm-registry-packages"}}, "3.0.0-0": {"name": "commander", "version": "3.0.0-0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@3.0.0-0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "c7555e26ab809878560e8a3533220a4d37996b27", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-b/YlgJi4Nn53KWvxsOU9Mz1Lr+ZK4tMUyDOk2IvB+Hki5eefYZusNOvLls91HeI2oWLyxJ3KMEU9QeEaRtTPFQ==", "signatures": [{"sig": "MEUCIQCEqHSHIg6yulNLsPOU4ZJfhqwikZheVXkZsMKwAytdIQIgbtr8tGlDXHm5OtMAsoa6+vqry7N1ZIznGo+C0ldGUF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdO7kUCRA9TVsSAnZWagAArrwP/j8rZpV/bcExP07qqGe+\nX9ZFwooqq8/9XLIAumCnaMO8vAbqJBk7aphBLUkXorhS4Sqh3Rhc1wdErgeP\n05yN2jd/GOzzPAQqqSOzIsP4jFTFyqb2wk3LJJLtf1VtDGHPh89mon4A2mP/\n4CjqMaSOfIzZEeUCksToOpk+pxlmodALSt+NBMh76KiDKWNpqS1slnZ9VZ5V\njIV58Z2AVBdE7PmaJLo7tL/WMG8rWWXnSLyNU06GGJICVC7PTr2Kdex+MYRB\n+px8ehvq6tqbSW2AdVhyk+QpKTRdbTLDxeukGPm5m2fO84jGe/Y1twCpIrdM\n3cNx8Smnqy825feY4RmMTCulcULK9WKPuBp9PBANtYPQqnoRD9hQq3b7lN6r\nz/ZxkuzBY8prgwppniGguZ3zCrA2lNzL3pHWUXhZ9JNvijTwiR9OPev4F2NE\nTty5YHv/mFAQ+kAOIOtGSHj4lOYLwtR/PJaSiq1uCk2np1KJSREFCyTCxFgV\nQPtPFp7XR7V94Uc7qrm3I+0n579FecbMLWQfrXNSZfFT0juEZyp7aSXJQ+QA\nGGZVeP8QBUZRyhZkxFvI50jXqq3nI+tNXReP24TG++RbcY0L3SltORDC1bFP\nKy9FweZbcpAogQRAzBDsdWZ6oTJYjbDL0VtPCoIG2MhUXQJvjWNnxy6f//di\nm3bZ\r\n=9Csb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "f743bf456bbda392dbc4a106fc196bc9ef5c8b76", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"sinon": "^7.3.2", "eslint": "^6.0.1", "should": "^13.2.3", "ts-node": "^8.3.0", "standard": "^13.0.1", "typescript": "^3.5.3", "@types/node": "^12.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/commander_3.0.0-0_1564195092083_0.9400991591327539", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "commander", "version": "3.0.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@3.0.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "0641ea00838c7a964627f04cddc336a2deddd60a", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-pl3QrGOBa9RZaslQiqnnKX2J068wcQw7j9AIaBQ9/JEp5RY6je4jKTImg0Bd+rpoONSe7GUFSgkxLeo17m3Pow==", "signatures": [{"sig": "MEUCIDvLESlJv46u5FjCuNhRFJz25idUicXNldtXUzIn99sCAiEAwPYy9DrfW1iMLNSHT20A0wZpHMqPRGasIAzVVOY45ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTMJ8CRA9TVsSAnZWagAAC+UP/RjuPatKnwcfPeI7gZQd\nVDRsyzHJGmSlxcpsejCctPu/AuRGZ6kxzcKcSNE4m+l+1GyRkT45DJsf0tL6\nWWB3muf7lHWWiprf6bMaC39BBvr5LDXbHfHFk+MSc7CVhFK9Ekl1KSD6MxPs\nanKzcnrchestrWTdzZGV/Sw54gtcGXoOmHMrxQJqaw+kPB9Vt+55q3+SCVdL\nUTIoIVFdKLzavEBdeRzPGV2j48zFU3dmOaEVcHXZgGFZweAKUMVqCGDYpfBe\nU0cIAbQNCRbLvE4g91cccqplSq+6OZRVcMjY7xxDjEs/gXIAEdR8x7xu6QpI\ne5v3JM+hm3n+hzV0Gm5803zCOXnopu9f++pHXXWFJ59Ysu3Emjca3eBDEcDX\nT+FFLcAQV5/PEE/3pbW3UGoHCwUKlaABYORSzD/fzjutFpehR6JGVzkFhGCt\nVkoaOQy2YBP5AltfHMF+nl+tOc8HL10XPJicll7/MDfX33Jsq/R9Nc1qBSeb\n+5YdQ3Zb56s1mHWPcbUKfksUoCuK3xCPQTlcXOgKrQ9Hd/4TnULbiYnSz8rY\nJVh3KxKk0L3nQWOv1JhgtltuRF634bqHQpEBTgIvXyJxf+0Jt3kDWZQcoq/Q\n4FfhbgCks442wkYf13rZzkCKcCELYUTayeLdV8GXSlXkykbKCm3stPzZPwGF\nb1lJ\r\n=YAJ3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "3b0127b1906ff268d42640b59f106d283c810710", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^7.3.2", "eslint": "^6.0.1", "should": "^13.2.3", "ts-node": "^8.3.0", "standard": "^13.0.1", "typescript": "^3.5.3", "@types/node": "^12.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/commander_3.0.0_1565311611336_0.30076989536927967", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "commander", "version": "3.0.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@3.0.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "4595aec3530525e671fb6f85fb173df8ff8bf57a", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-UNgvDd+csKdc9GD4zjtkHKQbT8Aspt2jCBqNSPp53vAS0L1tS9sXB2TCEOPHJ7kt9bN/niWkYj8T3RQSoMXdSQ==", "signatures": [{"sig": "MEQCIA/CaiG6uvCXdd6D6qnU2WTlhCGiZNnAiKfS9ca5+B+JAiBAvKiGyKNSH2Rqh9ZlHJ3Z880gKc/2k5wpHTf//P5t6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaOSLCRA9TVsSAnZWagAAqh0P/291MbV2QUq2ft6zXOFI\nNZZ32+k1GVp7z4U7ho4LqeVlfb+Pl/VKCkuxt6Y7wZrDJIIAdfxtOn1BIgz0\nrN//DDXGBepnDpbTwtSGpoBktHKV35vSAtRQde2yqmSdGEevRCyJLcwFgPUo\nNObsXgzzFa6Q5mByP+e5qoxYcMP6aieVw9PZJO7/L/bp3CrA7I0bAQfoUAAh\naHlFFbFiMbfu7+sS5ldoINa02pzV7wqPd7Pcnhk1FAbC+kl4wYUHzNix3mUC\nJ0g0mJuUXb0tvTOWd5I1wQ1S4IzQpcYo0Bcn58Hq3sApeIeiBrA4VA7ECAxG\nqu9uY/aJv0pfJToBYR35ZbqX52P++QHwqjq/mLGBzKxLpzPEBCdgUD8/Dxjf\n7FwXlF0qr+7PCv1j4rbOi1lwvHpCJvugeLzrRq9erQSLIL2O1zzJ1Bhjbp8S\nOY14zCLarxLOj8tND2qahJZoiJtBMFIrr9ZFiGbTJ+ybPGQm4JfK5afXTTNx\nGD7Jnmyu6EXrk+pmp+iJLuWgdgs7YHYbEDjkwr9LOSk32hZrKsbvgPCdOZQd\nKFx9BHcrjv+BvRyxolY0CkRi8j3qPZ/8ImNm7Ukus/6z4gYUw4lmdGB+CZVj\nubzAplB3/1old2GBA/eZTDIYRYDcB8su/cFoaCFcvHLRIyIWBJZXFgUMFFRJ\ngrYU\r\n=O9oM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "43123022cae2f01cc4cffe3eb44b5275e857a6d2", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^7.4.1", "eslint": "^6.1.0", "should": "^13.2.3", "ts-node": "^8.3.0", "standard": "^13.1.0", "typescript": "^3.5.3", "@types/node": "^12.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/commander_3.0.1_1567155339276_0.5367414136318001", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "commander", "version": "3.0.2", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@3.0.2", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "6837c3fb677ad9933d1cfba42dd14d5117d6b39e", "tarball": "https://registry.npmjs.org/commander/-/commander-3.0.2.tgz", "fileCount": 6, "integrity": "sha512-Gar0ASD4BDyKC4hl4DwHqDrmvjoxWKZigVnAbn5H1owvm4CxCPdb0HQDehwNYMJpla5+M2tPmPARzhtYuwpHow==", "signatures": [{"sig": "MEUCIQDjOxrLLfp++1TsWeeYqkyROURlbV/16ebDhb/xUDFAzQIgeG1nVSy95uOk+38jJIbsaM4zukFoobyL1dLp/aboTv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjIB/CRA9TVsSAnZWagAA44IP/iYCjdRvcOZL6HI3Nm0i\nh0w6TzknRyKPDSySwajq4umoQERahfi4xVCj481SAoDoNqvtoDw0lIPBZWfM\ngrF50DW8ykRbtyl1LRQZQJiuHu0B+3FNnjh8xOQ+E9LXrQmj2h0OU+G9N8EM\nqO5zmmsZU+RfQGzZc3j6TIRxLnW+xQNUPuL6MZRp5Gap0bDnVp/sJn0XQcju\n/FTVfjLvzUTC4SMhRX7lExfPXy7ylqNwOFSBtM8mIIlwd2N2Yxq9dQcFQJFC\nlV0Rx4ZdsR1icWBXsY7iWX3whUsx79QBNKMr1RgNL2GZ5yFd8sg7bN5hcx9O\nDeo9KYJElgQ9lKeNYBnMIwQQxTDER3VjHlJ4VL1bJ1bj5ISnrA1EG0twLnpm\nb5E+HbImrHFgK8dVZFnz8QiAGyi/EE0kLEj2hb1EWaeuUle0CrSOcU9lJIIE\nX387cGPrQJT1Qo4k4nQ3W5EFgb0NEWs5OctqkFYE6uta86Q08yiTB1PRGYeV\nIpq79QOmUZO8UoNFn+C03HRW9fKfnsBfh18YitucN8w0OC8ELTmj1YAJ7ykq\ndeoEsXCYuu6ahIXnK4EveJShiyFMxNfAKgWJJw+jEtkRRxQBUb7Qp4YVpYxf\n05/4ub0MTdtAy1fd1AyN6DKM3MLIG8q5nmcKUe+LRumJ7PW5uPX+KFZzLtdX\njMqr\r\n=ORqI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "2544df81b478a4afe15560f27b3575aa3a1581c4", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"sinon": "^7.5.0", "eslint": "^6.4.0", "should": "^13.2.3", "ts-node": "^8.4.1", "standard": "^13.1.0", "typescript": "^3.6.3", "@types/node": "^12.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/commander_3.0.2_1569489022766_0.9087751523372256", "host": "s3://npm-registry-packages"}}, "2.20.1": {"name": "commander", "version": "2.20.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.20.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "3863ce3ca92d0831dcf2a102f5fb4b5926afd0f9", "tarball": "https://registry.npmjs.org/commander/-/commander-2.20.1.tgz", "fileCount": 6, "integrity": "sha512-cCuLsMhJeWQ/ZpsFTbE765kvVfoeSddc4nU3up4fV+fDBcfUXnbITJ+JzhkdjzOqhURjZgujxaioam4RM9yGUg==", "signatures": [{"sig": "MEUCIB0Lt5PS+XkAGcnBcMmbZq3FLFZB+pMkPZGW+QEGM+E9AiEAi7OuLEzTCA73eNSaaJNo66DSsohuzsCwjnlNL1qPEv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdj9hICRA9TVsSAnZWagAA158P/itguvXbWFYz25Aj9NL7\nwE3zXiaP2L6Va4JsYkm7ZlCHJ1DmrboYBvXkYFsQI29bOtIjFN5lAjgcm7b1\nh52EygFKxuzTxHbyArV59d/o9POYgosINU7/QC9Qd3Vtzi1jwAKxVoT032Uz\nELrY80U4YTJwy+rx7xJPSy1p+rCeCHpF74NdsYBNvQtrSUa3FUmq2lOLC5WV\nUV2XsOSwCG1j2GKnNnAnBy8AwRP+OWQ5SUekSAK3KXiyFylsH5hJWKbPWPQJ\ny4MAYE3wYwvx2rJJ+MUqGT/DGU6exvXrrvWIzT2QicERaFo4FFgdpp0eIapi\n6Bk3wOt/lL+v4KUNOyIdv6aixLKgO1vjxAWuj/whPEO4bIpTjn4gcPsFqDac\n8nIW6cMtKtHy6YtyjjtOWjyGkdH62fKd4nCs6uagz+Qq1TjyMfhDIKmJBL0Y\ngS7WHMsk95emKJPpXxj9H37KRcOBJEcHODlBwL8sutND5bem+CWMTDBRXm4A\nmnYS8FmMKO7Wc40I1FFPzLT7noRbN5yQvdJ6W1Fh1lqb7Al4UY8rc9S04Mgn\n+khJKLmrr9YqEYfziXWZ/KoEW86M7K11spTUdLoSPTxpubpjXz8S5fZNi22u\nURzwkK4FA0V17Sy+op1+uwnpuOlR9MNE/lzp8wEl+PHD+bLkx0hm7H+F0iA0\nK9wA\r\n=VsbK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "48b02f06da3b96b200a973aaa1f7e40287882e8a", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"sinon": "^7.5.0", "eslint": "^6.4.0", "should": "^13.2.3", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/node": "^12.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.20.1_1569708104186_0.2554397084003732", "host": "s3://npm-registry-packages"}}, "4.0.0-0": {"name": "commander", "version": "4.0.0-0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@4.0.0-0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f6bae309054b2e6c870189dcc2f51555c1a4c377", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-jARQuTKQXYqAbHK/WJqYHbsnHzKZg0TQVw/31gg7gr4JWFJAoB5KFJS13FpF9ewiB7LhWA+0eT0ejt1IgvCWlw==", "signatures": [{"sig": "MEYCIQDg5OXJ1vM7cEYk2Icv5j158Ps8ObSz6BpCM+zz9zP+6QIhAL37vPWFMBpI9MCXug7K3X90zQ/YF4rRAevCsiIdjKxm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkxDRCRA9TVsSAnZWagAAXZwP/A4AfrK1oLTL2QuN86bG\ntlHd8VrVqq0KwPCtuGIB3RBMWcKyXe0FbUToIPNeBqH7r93tYP4k4Fjk2oe0\nx1MY2rbpS7TvcMN12aCqQC3/wXZHybD2eiLSHqiUKpqyTDO0iZOKqCyxACx8\nW4gbaCo+4JAfEB/mNwKikKpVTL0rtFgsKo9wMXvLintndk6Rlbol+0QeAICu\nAKUtimtZxi1AeXs2rXOiNEAGsTk2FQgn4wI+payX233v4BIsSNE/7Tab1jlS\niH/ItqVKwaHExTegoxXseOk8TFJ1fW9uxdxa8R+hgPO5ujg7RCWpumkRVVre\nM2S7dP7Xd3sjSoY27Zb0oNPE+6JVbEfwzHacSEc7OMQKB3g4nraQWE5i8VAz\nISiVpakYLiUKISg4lpDWN8YYyAQKZu3lvFW8O25Clji8QevNSm1nqheDj0FQ\njQnMGf6Dk5G38zRNeuS6qYjFCnINK8IxATDi2WfhWMRDkUcDdDNF4aaKc25f\nWLnoL6mV5Zd+l8eEEPR6ZIRWzEsN9VBaW9MI/zFTzHmQ99w6lhEPogh/aguh\nuHMqP5hBiEUI8St6MYEWI67qaimgYrxunXvVPthBUt2ffNlUtzpCVoUA3bQz\n70mIMIrnQSB+Cj1WaHL3PiH95LdgIpL+r3xwSMWzibqiI5oyrzg97ajrAtwF\nV3PU\r\n=YPfl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "400e6afd493c8ebf59cf54174bb80b123aa27138", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_4.0.0-0_1569919184426_0.4993958331925179", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "commander", "version": "4.0.0-1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@4.0.0-1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "cc00f6d884cc492aac3fe3b32ff36e5f46cd67d4", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-6UCnFDyJnZfk4ZugvEhl8hYtlViGJNVki5J+3x/zNd8nLDpBcDHZShvBYWKIIktsiBUuRDttP/qG1yF+bhhVnQ==", "signatures": [{"sig": "MEUCIAIri2bgPp82yFKu1clSMz4STv/V/aUNLI911UuyMxIdAiEAqBQUfUyHuEtWdBAsgANiot60P2wSjEjQxDiBqBl9ezw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm+YYCRA9TVsSAnZWagAAR9oP/AscfhK6wrWOAQirqHnq\nsgF0JCgHlpEPW9+tmvu7vL4kxni7K1wlCbur8y+dvyiBYnnTsWQZKDLrm/F4\nnA8TEK2HprGJ4mBsUHihfy+BbdT+OLxoez8IQCOC92ghk/yx3Xoxhqg+ik4I\neXV+S70BFBc/EnBN+yJDwn3gvQ6YGpeGPKR4+dv5vY8cx1zG4RR5CPx/pPOl\nOu86quAB90L0tVu+KXqTEGfx8rbyVKtrThhzgHUVOtAiZP3Wvpwv8Zct194O\neaDdQKfLOhSd2we2XLCCD/C8mTxi7byXb4XbW8MV2+pPrelUIkLhapssXA2n\nk388Qr3xud/2v0V7FPiTGd45Pr1iuo+a0cMvmFbWsn1buFVuDyhQLo3duz72\nyf+WIYmAxgIb+ucQ/d420boD3h/zTxMZm7V2378mOFWj0HXFRORSzHgO2RPD\nlaahbwzvfjfXdljJKZljjkAppQpdCWsUkyj1a+NJ8EbgkV2jx3qZAAnTuar9\nmckqw0KH6dmdTbIizNQsc2I3fL/KJpE42VKPOmN5i8Cd1XaMozJnHjSbFdGw\nUuu/+jCl6ADxSeGeIyPQNvD6jqHehtL5n0881e9xFv/lBAwyDFkwVkbz9XE2\nbiLFqQI8CUn5UDRncvpO9V2Du5tQKiodXpS5OIMHCxyn7vOGsueyy0iUTSYI\nmRGp\r\n=H2sA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "e9929fb8c83ab8cd654167f02d027c041f295d69", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_4.0.0-1_1570498071630_0.1486698540389535", "host": "s3://npm-registry-packages"}}, "2.20.3": {"name": "commander", "version": "2.20.3", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@2.20.3", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "fd485e84c03eb4881c20722ba48035e8531aeb33", "tarball": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "fileCount": 6, "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "signatures": [{"sig": "MEYCIQDi8P97IdM/HCU/5iN1cUMTYac7x2dNO9uJn3grTlBU5wIhAJTnisAKEKNxXfo45VISgVzZa+3JpFUl+TaprmdT3PYp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoBXICRA9TVsSAnZWagAAxZoQAJuUZ2avRahPhSmn15Vt\nRrnyt/EzaZaR9jGpwPpdSVKEyEypyKO21zsrFKg+3TjJGLQVb6gd6eXvY3Rs\nTG1JeNQKYyqcrvH+q94ZIDTMT4tzUv50MVBuSdVCAQB2liHKzcay2aMbd0bW\nFbb56rVEfixabLoYJGhpsHAamzAD51CpxwjpeKuVIYe2LDTxcDOAsMvrjxwK\nB1vsuQe4EqcuMWE0a7jtGJTHqxxqQxcvZLloxgO+kr6TGMZQ1oMtwhRUayNO\nPbN9R97nhOoV9J9AZu28O98yJXiz6p7TQ11JeN/mWF8F4LT1N5Nv227OHP/a\nVh6EtM6YPMLQU6lQTWUVa9BF6mqCMREBY9QRmh8wfRhrrCfD9oOez1qmGsx6\nnz6zqiqabuy8KlULmhyXA2ixHcKgEimlQOVDGJLy4EJIv84o+GCdVJNiP9qd\nrB7qTVbiNm9TiY22OehPADtx9QhcavZuju6xftjXllvjKN3lJFx+jbyenfR5\nBonTnPuqx4+dmUyFnmfHSM7pHO7NtzpLuEx7N/SGDtUfI6KPRBSEt7DpoaFu\nF7lABbtehgMpcR/gCOMez7BXcNkigDWHQeRqaJ+iedXrd3YLvJMQoaFgUmDU\nMyS3zLW3srq8EsazcsdlmQpVb8lRJjKnki6Bj93v0caKaT+4aAjpMtn2xyCK\nPqC9\r\n=91C8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "6b8499b24f4f6498ad630c50c8a00c9579a8536b", "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"sinon": "^7.5.0", "eslint": "^6.4.0", "should": "^13.2.3", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/node": "^12.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/commander_2.20.3_1570772424007_0.3931389310307576", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "commander", "version": "4.0.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@4.0.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "e782b6afe6a0f1b1408be59429919e1305160e3f", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-SEa2abMBTZuEjLVYpNrAFoRgxPwG4rXP3+SGY6CM/HZGeDzIA7Pzp+7H3AHDukKEpyy2SoSGGPShKqqfH9T9AQ==", "signatures": [{"sig": "MEYCIQCKtB/nu1sVQLzj7lLMXgRk+Rn8EB+kKPJg9hveuqPlhAIhAMI6OKMu+BDEGCQfB5WUsyldjlewGe7ihxwUAl6nma+m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvARDCRA9TVsSAnZWagAAStIP/1aYZBwrAfiGXxnQxy5l\n4GrF1OC2pML0nVRqyMDfnd94hn+1q5lyDoLuoEr7dr3HwacDy4p1hKDQTdSw\niRCqc3Zp8ktKF1y3ZpUFFnMWvq+kaPv5U1TSVwtlFKt/ywuThpjh28g7MRVR\n4aNONkGhlPyyMfKd2N9AunC3sx7Ykz6mQlLvyGlryz4m8VcQU3rsBmcsIdzu\nPHUUCHQUeqmz9z1czJir/4NT+IUnmbGMfyw+auHrMzQmoI5tKMJ4TJfGWAxY\nsKeF614GAcIWi4CuZDtj48KVSIrsrLoGLNGuzBS52+w/Z0RGXYapYiSKWf1Z\nrP5MTl4SxaeiUlB2gzTsV0Y3iNyMnn01hLWJMCOACzGR9e4llCO2CMgxYE8O\n9fK/qeN+NW/HFnJ9pR8ReiOoCUVO29BYroh2/yzabBEIBD5Ie0VRNoDNiFKy\nFEGeOI4MzzutFTzlZ+mvS9S/sKqAaaFEo0IR5OuDR/1Kb+1DZwsiFFI70JwW\nqRMqbV+ucar2BcL4T1Nf+PTj0F/M4jVpqq4mekv/wuzZWk3h8oi6lSlInDXv\nFS4jABQXWLlPFLd7jynloPmXj8BU7H/oZEWMJX9PO+hbJ2Cx2ReGnsrxhVUo\np+gnPb6eqo7bZG8DRf2U3X/cm+C97rqWWG4fsyrCo80WgySKV38XPIT1qQAe\nRx3P\r\n=wiE+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "f0315b892d0425dcb8589d19a8ef61c2f965071e", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_4.0.0_1572602947069_0.7900930790476839", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "commander", "version": "4.0.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@4.0.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "b67622721785993182e807f4883633e6401ba53c", "tarball": "https://registry.npmjs.org/commander/-/commander-4.0.1.tgz", "fileCount": 6, "integrity": "sha512-IPF4ouhCP+qdlcmCedhxX4xiGBPyigb8v5NeUp+0LyhwLgxMqyp3S0vl7TAPfS/hiP7FC3caI/PB9lTmP8r1NA==", "signatures": [{"sig": "MEYCIQCVLRb/IYRLG5vlyKfCEWwlLK7hYEvQOPCWX3VqvwyIggIhALcBY5KKXmuiyVhAxTB4I7lbw59XeJ8BAeIdYCYhbyJs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdySdqCRA9TVsSAnZWagAApccP/R6EvOeW1V7wNBqAEmU2\nIm0OW6aOuLM/b0qRTzQPuVNeIsu4l3Y/usfqZl5Hk9RRew3xAfHEJDT8sj4L\nJdRIUP5+MEe531awH5WQpLspY8VBHYsNuW13DqZyc3e/axLOlq6vC+yWVO7i\nlUmowznWvG2/X8YGJYI45lihfaavbJ5v7H8Z+mXTgBXnXQzjqH40T+ck/6L2\ntrjrfUFtDyTIMDtLDu17dTEF3vklL7TMf7w+BhajjLrCW+Kq72PSOCQPx8CP\nRpoSY5Qo8/fKzhUkpgQ0qh8M+x8NOvPdkxfzw1hV4v3mvMK/kEBKXkaBdw7y\nsRDJnU60LlPR8eGpYXMdMEzWFicOE+IK3ySxYEAWVdJCDoV/tB7dTlVXOgIn\nVKLYL/Z7adnv27GeFOO6aNhA/xyflsjstS5so7M5Ay6eyJ8R1/cUDfxjf7vz\nrJP4sPixDN+lGdVb7EGAWaMlbTEOdI7cmGE8Vf2HPgGLoREYBhkDztkFex9q\n7TngRMaUo6umxTX66LDj3NieDos3LksfOLlu+PmZU8UlHM5dMyV4PEzxj7Uw\nTlaq9kiX6Ebtn3ey7J9i/YpiIWoCeN5CTUpjiBClFKgXkgsWLBasfMWXOrdD\n0zpawMfj7hBtn5n7srEb162UD5dI0OhmkW9fsvik8S0gG5fpttVr8rQuYGzV\nJCcl\r\n=Jqbu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "5083e1463ef4a7d91e42d430965246a7a80439a0", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^6.4.0", "ts-node": "^8.4.1", "standard": "^14.3.1", "typescript": "^3.6.3", "@types/jest": "^24.0.18", "@types/node": "^12.7.5", "eslint-plugin-jest": "^22.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_4.0.1_1573463913792_0.883424062108114", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "commander", "version": "4.1.0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@4.1.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "545983a0603fe425bc672d66c9e3c89c42121a83", "tarball": "https://registry.npmjs.org/commander/-/commander-4.1.0.tgz", "fileCount": 6, "integrity": "sha512-NIQrwvv9V39FHgGFm36+U9SMQzbiHvU79k+iADraJTpmrFFfx7Ds0IvDoAdZsDrknlkRk14OYoWXb57uTh7/sw==", "signatures": [{"sig": "MEQCIHQoGWeZlFh6isUDEas5C8c+Sos07LYvYjLt4cOOlP13AiAK6pFWn6Je0DwTmMJVksEL7/IMT5KUPy5nCFNZ+3PDBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEvsECRA9TVsSAnZWagAAVaAP/jvX6NMVBFylf2XG5Yfe\nPeMK9l/TIcVNvrEgzXh2blZPtAuYHHdKIepwZPf7oVTshy0bSvoMPw/oSv6z\npinA2XDgYey322hLsBkrmv9ln/rwOmHi38Mo+WBU2Br167s27//rqoNdV4Pv\nfcc1/9CIMNZ7C78hT/Qxbk6r8bs3HSGvQe/vxmB1pm9kdivdy2PNBd/C1rXZ\nvdiGHWpuvO/C/p0DJjzu+nbJLfOM2UJ/cK1tMK1cXJbI6xEsuDW8izFOHFCr\n2ywH2KUVDnJK7dv52mHv8mKTvErUBfeSVUg4632LVR4BTbf6N3tYZxix+Udx\nuUSOMuYxEqaFYdmuT1BUO38mvmB2iW3vFcZTEcdkLXuJ0uPaVy3GmOtJaodu\nAE92QxAnyj30kd7Bp9T3LB8GDJaD0gJWx0x71WqpDqQ6yCsHi7W6hm0zzXc4\nTPTXCUKA1/QC7gErv9ZXjIoZ8IiogX/JhNoLR0rPhNR70x0brCG3xOrhac8m\nLmZq5z9Q2zTuxw4r4OhAvXmrPtBKoiZTVl/Nwep9WypUVkZdK1+FqkzNVpeF\nfSkAeGSVEH4EY1q8k4Z7kiL/9qvi/jcJZewFIiCnJXnQ6luupCnPDxEbxYmg\nA6aRl1acSacEhLfEZITjfvGot5iRaMLHssIFgQHACvWcmnMSW9xSFtEThFfU\nQelL\r\n=IVoz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "8e1cdf5405b1e67359ca7613ff3c6ed81714b181", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^6.7.0", "standard": "^14.3.1", "typescript": "^3.7.2", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint-plugin-jest": "^22.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_4.1.0_1578302212096_0.5951093748080849", "host": "s3://npm-registry-packages"}}, "5.0.0-0": {"name": "commander", "version": "5.0.0-0", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.0.0-0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "22c4588cb45740cbaa27bf8163f2327a2c126a75", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-LLP9hLsnl19X3DTYAZ72b5ZFxsaDNw31FZPxaJHefhat7BSjA4O7L3xFy8AuOBTt6AfOZlNYsEgzYJBE/KRV2A==", "signatures": [{"sig": "MEUCIEIXO3SwYIkkskwa95HkcH1LdMG0cNN2wUzHFKNpv6ETAiEA4Vc727ASRHZbbm8T2v5jAF42MaQG0h8aoGjYgU+V2fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNPQoCRA9TVsSAnZWagAAzBwP+gLLq8hnVoqCroK9VK/2\nygLOkf3phbegwQbjQf6As/yoIplt+6kWuFwpZRj61bKiUvd7sH8qcSepv5Hd\nGSINQtbP9hF6FMaOqW5YXzMGPIe1qz8nmC5pIM2ep33Rae/Q2b/1lRJKt0ML\nNM7be/jeOKbZeAQhO4DcbzFHuV+5GtDOjEoJBRLSswOBTUN0pOmUzgZOEUJJ\nVa3ghhtQCelE5ykGB4VtF6TKlJbcX8vE/YWNSLz7ALZt91Qi5cxn/fw9WACF\n08uS+BULnhBUvhaopyfx+3V2B69KQ40nhZ7DK7cBIGFoFMnMnoj44xCnuCx4\n0mZyqaEHVnDGZ0e0MYRfmlutv77wRIfcbRY4PCxjPvRyLknHJ/xIkD2nZx+E\nVaS8ww+Zvbscdo1/xjydktibpO3QtWfPWKqgzlnyvCjEWOC2Ixxof+mTJR2F\n5pnDA4dZlV/ML0HSqFhuHPsmcD+uyYQI6enXsI1/quKDtWIVjH0qUcRp2JFA\nrORoZeXtrMVZU4/zy2KYm/dLrzDCh5cvTrXAFgPM0d8O5TR2iBZ5wAfOEmjC\nbWry0p8o4IW6L9ee3q7/VdYRiqO1LWr8z2iC4MFUmla7QLOz9Q7DRW2Ga5NK\nU0SiXUTpyju/MlgoLZ+oj0npEC88wKYP2lZct7Mf4p2xJ6doZ6AWwPpcYWmn\n3L/5\r\n=4M9R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "6857562e2952561b226d09b2683c859dcca800ae", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^24.8.0", "eslint": "^6.7.0", "standard": "^14.3.1", "typescript": "^3.7.2", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint-plugin-jest": "^22.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.0.0-0_1580528680443_0.6839616301312152", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "commander", "version": "4.1.1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@4.1.1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "9fd602bd936294e9e9ef46a3f4d6964044b18068", "tarball": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "fileCount": 6, "integrity": "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==", "signatures": [{"sig": "MEYCIQCLxbbWdsLFDvxCF0Ne11CQnzsD97XGapRT63Z+PAkC8QIhAKzQqDOO3VYVVKFzP/ClCL1lwC/PZAntTzJjZJUKvqSS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeN7qdCRA9TVsSAnZWagAA1YUP/Au6/s5G11d8OQETCs5N\nqQwXL1HldSNz8VBxRfCCqXF34p4Tlg8MSUxuf265a3tHAQM+cIt/9IFXPt7T\n2JFgKkenOGltg9VD9QX/uerDQBpzWpGxIFPhhw5CjZyJljIzoN/Dcffyrena\nu5O7qTVZyqo3HAqRDBeso+r0bfg2i7st7I0gA7+Dfgxw6DNyZd30qFVZ+9i2\nnNRAsh/cseVTN85OBkb3aLUL5q1fVVR9MF/A4AJrC8zAVDyS43L+tNorfuF2\n8jkInsLhjRH3Tqy9pTAjRVZnuTUapuAN1I9M+Q4BPPWpvcs3gouxG8WmED90\nmsCdrZzzFlqL9IdsY56PoPpQ2RQba9UtovOs5u5NCWNPRR91nMD5j/dN2J8K\nONVQtqXBriGE8T7d8OBozmv6vARWbQjX7FRVhI0+NruCmapRYJqJWUgyuYCF\nBvrg64U0aJLasO0/Uca1wbxDSRn7wvzbfX/A3tuO/URuvLYMdlwAUCvNmZzN\nUJI8EOkJDJBBMFQAa+awrKa1E3/xG1MjY89b5aKZb1G1/+QXfYwVqxJ7ovU/\nC8uiD0diRHx6778UafnFBoIZtWVpYPJvO0VWQfWjXPNvYnu/a3KLmn9F2iuk\nEAUhTqqJpwFen/pB1avLZ0lYWZ/PRew7HicENZBoJm2PW2lNyEBlEHHMsr6K\nSh1o\r\n=zQzX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "d5186ba4b9b64a72cd685fccbb9ec5d0ec0c430d", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^6.7.0", "standard": "^14.3.1", "typescript": "^3.7.2", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint-plugin-jest": "^22.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_4.1.1_1580710556739_0.7541163312659649", "host": "s3://npm-registry-packages"}}, "5.0.0-1": {"name": "commander", "version": "5.0.0-1", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.0.0-1", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "7af5ad7324cfdbd6ac23b71d54b96336646a9d25", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-cV808agy7Q4lIV8YV7zAmMa929TuQVbEIEtZ3P73T7WyWlbS6QqABihySoS4wKoFv08psv+DhagmVn3YZQREAg==", "signatures": [{"sig": "MEUCIQCXLj4CtK24AX0+X8RD68kjpZki/VHF5JK67rg6QWLILQIgFfnZ0pSLOc6GRafsN49pVpUOqh/J9EPWE43y5EFExj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePx7bCRA9TVsSAnZWagAApL0P/12hiNxfOsVOZSyyUHrJ\niKDmvX+nGVCY5vBtCGeP0ZMnlRZ2dfGXIzmsV1dQ3BatSwho0Q4H+rh0RfM6\nMteIkcf0ufCYpltAKj9dvxbDgktaUR0yZ0R/AnF99NzTl9UyyQjmsjEj8kBn\n6oYtunBTAu6tC54kxjUHBG7XwxQ9RdbIWNUtncmTqbHNDhTIQrnDWYwSEjd3\nEhxzGzNSq5MYeX8+7/cRCQo4ZIEN/hD86P8NjJaY/5lHSKlfkZaJDtvHGrE2\nc4gaWqAYHujripcW68zCZCaQi9JjZ+QfN9kxjAsN22raD1K64GvRHsiwZvvP\nlrDgkFKuYa20GFiexfDi2rx3xaxmBmPqRDahB8vqYTdr2Of8YyQhs1FmT9e2\ne7l0sZpG3GM5q9+SNPYjNf8NGfqPTKUPfgBQeG7K53uLHKrDKJpeLZS/5uMU\nw/UbEHw9tCvCdtEOCpT0ok8+imNwnDxaM6XaGP0RTS6nQcEETrll/CMtfDh1\nc+iP1ndKH6D2CKr7pQWkZYYxVWMN5WtI+AQBy33JUIE/+VCQMszlbccJ/Muq\n6FZ0Ur0Xutav6XE37ltToX379Sb9gPfBO1EbCL6j5keK33aO0RIHc5oTo4jh\nIuuEjzD9XFFVTp6ikMd96TRJuXrWpT0yX0l+VM9xBq5m0RtnwLxv29TVZqfI\nzl32\r\n=clQG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "a8a14c91b3a65ad09d163894adab2d1c93316150", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.0.0-1_1581194971136_0.6626231082994336", "host": "s3://npm-registry-packages"}}, "5.0.0-2": {"name": "commander", "version": "5.0.0-2", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.0.0-2", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "4550bdd78a7616b7ed0c3e2a0f4ac83a2c641333", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-2.tgz", "fileCount": 6, "integrity": "sha512-9oKoqJisncoX2ppeXigKyRiFtLZreopWKdOCP9GeGESMAHETXgy3k7zJPuh+R+nm4LpDeqlPAz9kAUaRnLSlvQ==", "signatures": [{"sig": "MEUCIQCtWP67IOz+6fiGTxmo2ToZ2vo4xxp6em3uBAUmRSCslwIgYq22TQ1cy0+RKI9et4Od3Q1PAIeUO8E75hnPKLo42tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQnm1CRA9TVsSAnZWagAAt2cP/3c4tuHFMMxp18wLUGp1\nYGG8m2f7CKGdUL2UvnyIgZR64nmYckoNE/nH4lljfnoN+rWtubKQ/hWGExv3\nRbshBa/9RbLetVQmqxjMHbkfncRKqJZpjQLXnlWM9Ux43O9AT4EtPoaq96QI\nzHd7VnsCDCKvZacRcCBZFP3lNzH2iUFKDPuWAVzDzsglVb3EflKX2zUFz6NV\nKctCP8DU5iCzH/q9P0jqdj/aYW3byQItY640D6kBVvB6jceY8NeColU/MPvK\nFzQ3XKG0A1qtenW4jOaaEfG89Kod5ubZnk6uqz6HCUEMPo4X0Gvog4Hvn4b/\nFMS6DOxrAXLt4oIBHBNNytqV/JzJu0m6Qrq8Qfz/lrDgb9602eya3zBV7ihT\nDiRmF99T7NDPhY07SW9RxrvB7E0+ar9oxGA6jgDk2p7Kclqoj8LPskf/po3v\nkAuMWnq+GlIQk6H96w2ndAzH38GTuTrKdcyJF3jtROrtZcAIwfQPeYBo0UFJ\nzDJjpTBWioxvM0u4dRJq2gAYxlNOU2cwkCidvr7LPD6/ZhRtIDLcfHd61fq8\naliVmRG/suVoUYymTlCUZfMdQ+Xoe3T07LFZT8j0qEpjSIYPEpm4zj7IjsBO\n+D3vN9/nmi+0FCSJw6kmWoOvckcB+Ei7/kJMF64z/njZ3MWj8cBY4k9Zuycz\nJknR\r\n=ARMB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "1a84b806f3f83055fb8f864d0c5d849319ce522a", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.0.0-2_1581414836630_0.4903290135236571", "host": "s3://npm-registry-packages"}}, "5.0.0-3": {"name": "commander", "version": "5.0.0-3", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.0.0-3", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "4bd12705959837cfaeb679799580e81c16593423", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-3.tgz", "fileCount": 6, "integrity": "sha512-8lfJT6SUoBSqxsXxICEeSB6+/YeVUDshV4jfrDneRcrC3H/bZ9YossoD5KCtNJlmKFUsWFZJg1b9MmEvLD3BCA==", "signatures": [{"sig": "MEUCIQCYIEj7LTGFqBGpHxIHym8rdviSGxJS0FO0eI0eENvj8QIgQaAFW1kkD04sKtdeVueopcuXObbKXC5turgD686YeGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTjcHCRA9TVsSAnZWagAA7p0P/iy4uQGHCrxy9Y7dvdFY\nRdbfGBWc9SKXQLxod66Y8RUqtt7ae1vtYuGcz2Wfht98FxCyq0JYtnXk350P\nkL9ztWITaKVS8lGu01lioybn2wTHzSJRCKfV0Ujc58DIQzAUXn1C/lBUDcUM\nKFPFZ44p/9cQhQz+ZsoUJQ6MeblRHiIp+pOixfAintyj+bM3uIPTGJtCzhby\nVJrLmF2+T1l2tg47OWxzPyXyWJl9pQV/f+m/6ZqEM1uTToeIyVbRqjO1M8/p\nK9xdnrEY7riP6KwAUGxQ3WLT+TMIl6VBEkYzai2Ynk/TaLC4jARFfsHkvypE\nrWVujnmegVBZrxjvFoyh3k4jirbNnI1zZ/sfty+cmbM3kLdZdu84I9T30REB\ncHMiHrN9q9yYkvu5H8Zq7eW/JdSZMvGSFhopQzYgrOlrY1DdfYgMgFVhKqCZ\n9I06UK9etk1K94FfSPZTOmkLEMTpHNGs2JCecV8d91+JASpIDlwiNUcHEo0U\ncOHo/NKQPFwDhSs7sxuVU/5pGgiwfYLgwKPnmwC0PcqKskUXoB/kRNsZIM0m\nzCbiDOzqA723Ky7Jabgs3zt/bhh6O8GR8dyezCp3OWknGDZaFhy6aGQbdSXC\nKxL0n4m5C/krMreJIF8amdNJjT3ybORsJKDf3Ua9c8Z3WvZBiMKBuNBSK1ZT\ntLIq\r\n=fSGd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "a3f453f11cf3ad69d2b15ce82efdfd3af53efe22", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.0.0-3_1582184199234_0.7276851509041251", "host": "s3://npm-registry-packages"}}, "5.0.0-4": {"name": "commander", "version": "5.0.0-4", "keywords": ["commander", "command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.0.0-4", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "3960a6d58cfe1a67537dbcf993f63f7f883daf32", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0-4.tgz", "fileCount": 6, "integrity": "sha512-5ouZPC4TTJaxyjVoLbFP9M08CFmiMewAl/Vg044S5hv/oVtP3sUDPOl73XY75l2k8njoplxogL34oaaFayuS+A==", "signatures": [{"sig": "MEUCIQCpXf/tBdd8qh4klu5Vb8Oxjt5NzFiZ8cye88dFWZsciAIgJTOFLu5V993kbuUCLopE9mky1AyDZLM6vHKs8RYyOFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXeyoCRA9TVsSAnZWagAA2ekP+wWZzsz3MfTPGoXM7g0h\n5E3ES6Vnbr8UH5DPbpiBXvdzIdWhDClKZzzfBTPgtcZcYO5qW1fVn09lNE/S\n8lJwxU5k4ijhpzPE/lJ1rEfi19QTJi10c0k65j1K/GSzPdvF+TvuaJlHCXkQ\n1s3uXRuGCJVsMgvkFjknjhtZk4EWf0uqxWghVCu8t5xpEVec3rNO3Vk0EA0J\ndyEw7oF7quKdt31f7P5LJqAUR91+Z3eWuLti1dSkZByf8PMx2h1NA76rQHMJ\n5AdRGRuqvXJyePAES3MCrmsBmiZrKG4LUpC7A9+YX9ArMB9VBGV0Urmu/XqL\nrRQmCiyaMpd61OLOQARwisQI6xvYXWnz1i1XsEYWgSiv1cU/I1PLiFgLg6g5\nHv5lRenitGMQaHlheErPTYHtGprzp3luGjfm3inLQjk9Cuf8tUbI1XSJvl4R\nA/0sbKaowDcMn9ihKHwmwleEm9Rb0eX9fYQSitZsCMHnswwiPrGvAJwXcVp7\niAEMbV+O53cz/jtdBNn22hUlGpLMPtwlvgz8XsR/TJZS3H3xbCPhUJ8uvZv6\n6d0IU2Uj2wjS8urSw2XAAlVilo/Yh7WVG68o98uZpqAPXn/cEfbD+kKDvYMp\nzTNYReG1zy4z1mbipZ0rGo0Cx/iBhpDIKNONmvoaCmEfppuU8OzKCU8ir9Er\nyZDH\r\n=nXND\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "95e0d193ec02859f1bdcb08785e21f86942fc5e5", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.1", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.0.0-4_1583213736290_0.593589778400347", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "commander", "version": "5.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.0.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "dbf1909b49e5044f8fdaf0adc809f0c0722bdfd0", "tarball": "https://registry.npmjs.org/commander/-/commander-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-JrDGPAKjMGSP1G0DUoaceEJ3DZgAfr/q6X7FVk4+U5KxUSKviYGM2k6zWkfyyBHy5rAtzgYJFa1ro2O9PtoxwQ==", "signatures": [{"sig": "MEYCIQDxmxPpv3vFDxpNEVv2z62gMzWfbd7qGdgH/twpS6IArAIhAM3YN20gjeU8uoX5pq82Zthx/+DNoMMy7OavKSq5vFSh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebC8OCRA9TVsSAnZWagAA2tIP+wWxCWJU32qgy6hq7y3D\nhPASN47V1YIbKcxEHrMrBwf4pgbI/gwdw9YcnTQ/mn+7nd2+ULyGYf3vP58i\nBhTtJF6BwOPj1tpyroQOICHOMm7VEzhsWOgtAqP6ItaSTIwvrqCDmPEY2iNP\nFwfKFkO2IPD6V+2yEYuWljY/RrlgWrqfeswu1rZxXAg98rwGR9AV8V9fq4rX\nGCcBBlMcZ9tOwGcZFE0VGq+lTHBrBUDICTnv77CzyABkfEbJgyTryRWw7wPb\nTW5JuHlob2NIAsUS+TFK17ixDZeU8opItU+WLm5/31OBWgMoSlYnLsDTO0Sa\nAemAazg5ZAjYDYTXHbxJdGfLOZzQvLKOR1WNLZVNThcdJ0MuHDKOFniCA0Pj\nBIop1IjRiMTOeA0K48SVm03gLpq2dz8AVLf+ikxOoP1jYDya5vezABKDU7qt\nih6d0vb7pdbAsGh6/5mY+dWnXGlp+shTVbjaMBEhiXh5GCL/qfAQQv73gASm\npPAL7pYEg1s/Moo8422Xqx3wSFjj4oXffy+u3/Gy6g6Z4rNfdO2zK7M4daCi\n9k25IQ7ynZaq+SsZcC31Vc/UVbvYaK2FLjH4fofKIgqb4hw1RlEVu+njrLiB\nP0y6xSswgzy1VRiC4Tyn7OhEedC+iKBcSnDr/Zbo3EAnm0EEe7f3GDg6Xd+D\nPZQB\r\n=x8qh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "2aad525640d5885d9f51ae8dfe07a01c280cf4e3", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "standard": "^14.3.1", "typescript": "^3.7.5", "@types/jest": "^25.1.4", "@types/node": "^12.12.26", "eslint-plugin-jest": "^23.8.2", "@typescript-eslint/eslint-plugin": "^2.23.0", "eslint-config-standard-with-typescript": "^14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.0.0_1584148237584_0.9651249025797677", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "commander", "version": "5.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@5.1.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "46abbd1652f8e059bddaef99bbdcb2ad9cf179ae", "tarball": "https://registry.npmjs.org/commander/-/commander-5.1.0.tgz", "fileCount": 6, "integrity": "sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==", "signatures": [{"sig": "MEUCIB1FQcMp2VtJHCVeVAZ73481wG33i9MfSftnJTeg3qJPAiEAsGouRfQtcUo7IFKlmqEB7esXCuIBWwCNDWPtJ4EBumU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeo6CCCRA9TVsSAnZWagAAEOkP/1vbabWlRUu3mPVGI6zS\nL58zA0x6phEnUHkY5aTS3+0STAzOhT6bDB7sptNxT2WmTkyoP/U20czuOzda\n5eCeQH2FDhvj9vYPnmE6mU/DejL5JyBQie8UsJS6k3yzmrVNcxcAWu1xNkLu\nvXtfC7gp16TpUBLagTGROhJ/+TvtLTgz4/4p2oB3aI4pr26MXd2UvUfOTnwn\nB0rzbnfkCIovf3kmSiS29R7mWvo8WgHHQ9NKkasNJ0iDYLFaiqNueNeL4bBP\nB/3rS5m45p7JpAYq+uPAYdd/Jsl5EWsF72v/gVXioodDS0uUA4INx05DzWFT\nSyO/teSqVIpHH8/OBWb8eF5qSN29uVOrH86PBgKgTWkIQ1MfpEQfRZWI4Rk0\ngQlLSX8HuLmPCz8gT4R5lI6ccO/L4izsArnPsM5y/WlpAlzYhw2E1QBq+Nrc\nu00tc5EkgBL7L6cN0ceW2yYL+z3zITXcZC4MbLwdpumh7wBn0mTDI6eDzAdm\nNNljdDi4Z0btHT/z2X3stkLbC57ppY5RRzThloLMLLv9pEgznoJ1sxhxX8cd\nzpMpkuM+JPPyBgTuNe/X5AYfi8PN7T/WgrAnUahP5I0+iXhwx0j3PDDOcwty\nJM9j93JS8qsQ5iY6I3o4wSoDXyCzeDjryMUv3Q2jeRmSSCoFpKc9hwo5otD1\nAcU1\r\n=6wNq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "64053252cbd5b5434afb7ec3f12c46ad2a352d8a", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.4.0", "eslint": "^6.8.0", "standard": "^14.3.3", "typescript": "^3.7.5", "@types/jest": "^25.2.1", "@types/node": "^12.12.36", "eslint-plugin-jest": "^23.8.2", "@typescript-eslint/eslint-plugin": "^2.29.0", "eslint-config-standard-with-typescript": "^15.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_5.1.0_1587781761810_0.924048830244679", "host": "s3://npm-registry-packages"}}, "6.0.0-0": {"name": "commander", "version": "6.0.0-0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@6.0.0-0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "9916b52863f367a878732bf88c06782ec656bed6", "tarball": "https://registry.npmjs.org/commander/-/commander-6.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-KKrRiIPOMyRoCI7QrFwZIsRjfJzRqBswzAYWxKo0dXCmktq0Qz04gSHTx/c6oRLhC/BTYZFeWv6rx2jJl9wr8Q==", "signatures": [{"sig": "MEUCIE/5X+oCEhqIJ9XzaGFKGdtk3CMpej1R5VLBnEKVS0wzAiEArXzSGXIj8TC+L1Ri1pZ9wV43WDySkSCqGgb3mIUMLSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7abACRA9TVsSAnZWagAAVnUQAIyaPrFTsn3I85j3GU0t\nEaS+OIIDj+1RqIGOpqgJVhhhOjjiotBR95amnm4rrfgbRoXF6/1eF3995ft1\nQfkdyJJQBphN/RqQ5QdKGIX0NUljK0Gl/TDgxrQT3esL0UocmMzCPPOqKwoy\nKwbU6krqVlIFxj9OsA6GVISBUZ23r5xSMNvGJSWptfji5WgoNpD7drKB1Efb\nJRGGkQ59Ifajen3TtAlfp6x3PvTie8ajPZ8JgA+baaA0hWP0eeORTG7UhkC4\n7D7cUhMyInkv3/ndMin5BJHBS+6wpyAxI1EkNHoJx/6Qicbon76nFfy/2UMo\njUXmIcQ3fUfoqzip82/NNrQiAHggQp0yCbpaIr6yRU4ZQUtFg/cSZPIor2w4\nGnxiFehjzqGMI7HeSxqjcWWOMHe0I55X5N8YOguvjV4TiN8yYbOeN6fIB65D\nwNJG1q9Z+YrTc6BHL/PuaQPsOFkQwjoQbnleKEk98vCLduGocDxm/JOFI/OI\nRsp9mmrJixAFd+9O+n62KWPKWHnVoXGAVqPavsGedDvlYAeJfrFOKutv20D2\ni8pM+/CfyjD9D0A4dpbyLJWZphaeAQWtNRZ3YU9nSpAkGLJqx3AAcbtSv9e9\nMyujbN+nnqWcwwpXUTjmg+t1/HH1F3T9r3FGer7RdY4Q7olAqhPP+AUZVtQ8\nlBVz\r\n=pBhB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "fc36472de1e2b0eccf205ba1854ac1acee1be76f", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^26.0.1", "eslint": "^6.8.0", "standard": "^14.3.3", "typescript": "^3.7.5", "@types/jest": "^25.2.1", "@types/node": "^12.12.38", "eslint-plugin-jest": "^23.10.0", "@typescript-eslint/eslint-plugin": "^2.31.0", "eslint-config-standard-with-typescript": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_6.0.0-0_1592633024189_0.9170190131393023", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "commander", "version": "6.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@6.0.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "2b270da94f8fb9014455312f829a1129dbf8887e", "tarball": "https://registry.npmjs.org/commander/-/commander-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-s7EA+hDtTYNhuXkTlhqew4txMZVdszBmKWSPEMxGr8ru8JXR7bLUFIAtPhcSuFdJQ0ILMxnJi8GkQL0yvDy/YA==", "signatures": [{"sig": "MEYCIQCVJ7BYua1ulrJbWcCspNQJw1m0YmEOrTLdZbSgqRJjOgIhALLRyb4Rdd21cJyaMoGnYRdxAvS1DtRKIN6JqxD5BHna", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFFI8CRA9TVsSAnZWagAAKqIQAKRqf1kM12djMkODES39\n6nX6MN2G3L4FxOy4F6a8sgjisU3y7fyOrGb8PkbEaBw70YLwmA61sK7F8OWS\nm52LfVjrKRO/XksTmK0xnzYDyy8My14Lr7S9rnlswQ2lcjNqoglCnpOwiGZ8\n2UNB+cp7+da23elmx8+ldTYrqyec2c0VmL27YurQavKmvruJ98AZgZkn0BvK\ns/SH0sQRHLrzF0Ye/5oh+H8AdLqKVWe6tjCzcSpPRRKjk/upBovNqO2l10tJ\nefBpMe3z//f6X8hBLhCuR+xktOUBIeHMYd3ND9m8qAqlzrKXlU5mrvKD7pSw\nHVv7sm8SXdpkxxJJjVAJRABid6Bcorge8+rKVZBhZBsColSzsSsftcAvqc3Y\nUaRDb7jgYlKLFYEgqpIN/rY7RqZxpLKaFTJp1N/jYiznXZAGpt8FsfHUSyrM\nciNjrC4UICbzD6MwaKj/ZX7Ft9/qA3f7aQhSU1N/zYeEuTmymNLNESFIJbdT\nnbHN5ykgZoLeyMv2OxPdTfjzQ9x2cg97vm/+u7Oqvx6EQMq4VSErKWmamNKh\nOcQFBHvovabHXAcDJjxcBnQTY4w2wzrwn3UKzO6YMbqxJX+P1+jHapxWKCoD\npgI/KPoUOrdmusPsDVx3uKODpj7EyyvRyy75BWDybIoq/o82c042EmPNm4GP\nLw1J\r\n=P2lW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "c5a5e7b70d425d6f739bd84cc622c1d8775743f1", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.1.0", "eslint": "^6.8.0", "standard": "^14.3.4", "typescript": "^3.9.7", "@types/jest": "^26.0.5", "@types/node": "^14.0.23", "eslint-plugin-jest": "^23.18.0", "@typescript-eslint/eslint-plugin": "^2.34.0", "eslint-config-standard-with-typescript": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_6.0.0_1595167291790_0.6825361726430099", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "commander", "version": "6.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@6.1.0", "maintainers": [{"name": "abetomo", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f8d722b78103141006b66f4c7ba1e97315ba75bc", "tarball": "https://registry.npmjs.org/commander/-/commander-6.1.0.tgz", "fileCount": 6, "integrity": "sha512-wl7PNrYWd2y5mp1OK/LhTlv8Ff4kQJQRXXAvF+uU/TPNiVJUxZLRYGj/B0y/lPGAVcSbJqH2Za/cvHmrPMC8mA==", "signatures": [{"sig": "MEQCIGypzeQz/k7rIICljickaOCyDIiitBb4HsDVCz0pdWV1AiARUjUiBgKDh2Wgsg4Yq4D0MANnALWfBzLoPxW/YnyJgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSFxuCRA9TVsSAnZWagAAkgsP/R80bYbMXPlaEGRMFel4\ndxus1GcGr2hM78XU4uA1bXP7miANDwiMJVEWU9e3IGqlaP4olKE1zXL00JRM\n3XU9lMgE6RaEiM5cCuQGwZ9nqidDZgT26yHqz16mIKTJqVGFOkx7PKC2RA+o\n5hzgpqi7AHjzgz+T2b5U/1isILdFGCllG9oRML3DxQ3MuRzh2ZQUJ7bjwKtr\n/hV06Ka+KXzkNs0yafe2pdnXoZzKQdoIIdyxEnXIroAUcbPRjxLi/igfePgn\niU1rM1lKwr89jglROOmawam+a6SqB6649S+zj0PkTzf+/n+2u4mh5ze25q8b\nbm1AoQhgBF64uFP2DoXt2I032yBvMbC55VfurgkJiECW61MB6w03Yhox9cU9\nSn9i2h+Z8YNSmuJa+lnlXKKafF9gzFx8p8F+5hYYkETzuqqiwqODLJ7my7i+\nNhCtLL1ktnm4xn39Rx7VrqS4i/6m6+8VfPKzinQMXeHXpTrankNKP+Im+74X\ncqgrB4buNQxzrI30kbj4Q4Ixn2n4LPytTeZ0pqNECdpN5a/JIri9mJFzoOee\nZu7wT7zSZxvGLdXJAkzcdAwmQb7Ym1b2mOeAKbVHwhxN/z0YkkLx+ehMkjDX\nizihTyANkKw218VqYap7TzUQ7b/5g9+/snlF5d6EvLuGUaa72QE4xpZDgmJt\nyV84\r\n=FpLQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "9c7cfc07d523faa9577249e5d18759186408115d", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.1.0", "eslint": "^6.8.0", "standard": "^14.3.4", "typescript": "^3.9.7", "@types/jest": "^26.0.5", "@types/node": "^14.0.23", "eslint-plugin-jest": "^23.18.0", "@typescript-eslint/eslint-plugin": "^2.34.0", "eslint-config-standard-with-typescript": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_6.1.0_1598577774109_0.7753989550960525", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "commander", "version": "6.2.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@6.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "b990bfb8ac030aedc6d11bc04d1488ffef56db75", "tarball": "https://registry.npmjs.org/commander/-/commander-6.2.0.tgz", "fileCount": 6, "integrity": "sha512-zP4jEKbe8SHzKJYQmq8Y9gYjtO/POJLgIdKgV7B9qNmABVFVc+ctqSX6iXh4mCpJfRBOabiZ2YKPg8ciDw6C+Q==", "signatures": [{"sig": "MEUCIQCfyUbONjxqsb0vWknV1phLk8bmh6C1xxcqdt8+zle4kwIgKwKDBLBHJugTzayUPz//D44KVcM9XP66Uq9X3wEmhLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflOUFCRA9TVsSAnZWagAATIEQAIXPfPigdRxIl67hNDaP\noegoXv5FxcWLBf4Rh+h2Xr+3nJQAbPZ7aafvr4d4lljwaEc1+6S1ybb46mH5\nYYwKpE9hwuomHLOxhp1FMgt871kJlLJ13MbJMeLYLNQK9jsJZaEqbzu7L6X1\nOFCbLX2rSmVKTCLMPOT0jAqytyr5LoG4T8DA6Xm6l55XIhwXjCpGu4MVNUJn\nshzBeTcteNdAhXcb+JBNj95ih9TtBC3sznawtYE1aWKV+OiBIG6jaMKtNYar\nNkdJpefEl8DKbeQoHVE/ImxDzTPgBoocEqnp3bSTZ+CCjJ4gqk32Bp7e7Tx8\n0jicPAgs1d3OoPz21FxwRexlFvZzBTrY3BpNp5CBzG19T+8GR5zb05GYs4tB\nqFMd6TTyNta/SNFZnZ8ZeW3M7a4JpkEWCzdhf3HvUZ0qIqyLw46PTPeXg/2H\ns/o895CUOEnj2kT3+xiW9TKZPFHnE+8MC5Nnt11P9IF5Ael58feh4cxSZHhM\nwrrFjqURhP1e0FdajOe/4ynOR3uT8elT3906vs5v74f+wGEI9Sc8HM1+znNb\nYvKY5kEPBGeciBQYWPHxEI409Ynmvdx4T0lEmXE9jlX0y+kFRsiu/0k91p1k\nmSQ7H6kaeHETNjIMiILRRGUbsAOQLlOmKi/32vi8Vz/RmIo6xI/bSD8CeNDX\nupNZ\r\n=01r0\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "0192d6664a0d52af54ba73e83b8527faa38508e8", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_6.2.0_1603593476723_0.15500338892678145", "host": "s3://npm-registry-packages"}}, "7.0.0-0": {"name": "commander", "version": "7.0.0-0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@7.0.0-0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "b4f780553cbd4b0f3dfe49c7a1d13b088353d474", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-PJ04PNZn5mXaCHQZGW8WqBvkIgMKwtGPdmL3X3EpJuKZF3hggmrwff9rCUCEn2tewnrbs3jvcJW/F11MJ8I6QA==", "signatures": [{"sig": "MEUCIQDIHnL7UiLynfNMLNorl4a9429nv/lpmci3rH9odlNIdwIgKLpAgBprhzHyf2383v0CsGfqr6wNm3LP2lKNSrGD8G0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflQPKCRA9TVsSAnZWagAANaAP/2RB3Iuez4YlPglFVNJ2\n1LVDIHo67A8S+MbNHEKsYu9v9ybHoLYMndwpLdiGF03L7xFYaFleoBFJEThN\nFz1l9cM+nOw0BYclJnQqwbnes2Vol+0FVWx1gEKrw4SnYosfadFPqEC4aqFI\nM2r6BRuRi/D9Ypf3JYQlPrStHjzsrhIoqRP5FhDJAUR22ouHU+NMJV52tQYr\nHbjI4LR1AhUwTfy1fkiMF1yXIxUOiPpZJTc17rf0x7iGRvcd2dno7U4YB5C/\nLMxr+6pcPp3l0RWUQbvYjZKlkTJa/C2dKly2cr3XZHCJrQ064rcMLI0mg3aY\nRkd36+aMuy9KBe9keAUf/Y7ZW5w8vhyYZbbs002z/YGw8riNuD4lHpeTAcAP\nUWGJ/mM4fB8E7udaclU33FTfuzKmGWLNeGyjv323IB2hiSb01ONeMn78IQTK\nX5glMXxq7Y9y3PKGy4tJ85kj1sKH6w3gMkpxW2Q3D8gri220mNmDPSGWukNa\n3Q3Vk0IAu4WAsZRkXQSlSMdP3kU0pBFFb4wVdy/pfaGrCTzEVVtkoswxzrIz\n85PhDuALPQh5vr5806k8C//iEH177AlrT5PkOm8YiCLpyorykCuB7Pg9FKDJ\nhDVnR3VKO6J8Y9k76ImuzSwP+DjypA9XcC1J8jKlWaYyx99lBzSKtaD7n+Fm\naRU3\r\n=h+dH\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "0703a4d94794bec546537e5782ac212ce10fd870", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_7.0.0-0_1603601354427_0.016890314319127908", "host": "s3://npm-registry-packages"}}, "7.0.0-1": {"name": "commander", "version": "7.0.0-1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@7.0.0-1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "c55188136d80d1a0df19ae21c3317fa0bc21c6a0", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-5yh6ihhjDbdt8nchmjJINF5ttoczMFPEdSn9VBK3v+Yg+SsozGTdonkLf6GFOzKu8o2PwVjYorARioDLgbd3oA==", "signatures": [{"sig": "MEYCIQC3bRvuoluiLyGrMeqlj9gX4A7utjnR/6ShYalAaZL5QgIhAN9pMXuiLfEB31XPIAg2edZ2akQC56xNkNGOw9zS+ehq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuYb0CRA9TVsSAnZWagAAvlAP/3o0pXvUtjuc/njElgbT\nLQAbnE0Y8HKHg/kZXKjfvyY3xoBR4XT6THRR9G/ERXmEZkPASewilXa9PbMS\nw5InwSp4KqvUBzTWLXzRp9xjHWf5qJqMF6/2RaiyP0CmlYdaRGg44xsN3Q7r\n47w23Gwj3KkZRF95FDVK1q9vIt/SmLXetZkysdqeulPDNVJwSGXunxjuTgJl\nBVaEb2XR9hPvA6xyg4Jf7EPpUahwpN9UeCdnsPS5dKNu454eAgw0XWvgX2e+\nuZ8DJ6TVuENj2+P35d4fVOS/jw5BReWCg4xOQQqGtgZ8tleXjXp8TYE2ZMSx\nVxcxemz9t3BJzlTbLTECXs5NSJ+BVayROVNy/oZzsk4bg4slBLw3stn1VoYP\nSXC+gVybZcsD1k09qyEFokdJQqy3iX0T0YLWndaoaLlNSKwW6NerzfB+ZUN7\n9oqhhNFLEpu3FS/ezeCzjo5NLpbDsBrsOyrz8xl6kWzO24P5TYm/mzoftwER\nEMbQ2yk+iLU0VbM8+eY8ZZkB9096+eQZBuBFDWcM6u3DP79+bvQ8/WGOE5sk\nPnDxTzVZWogPWh/EfnkvoU4/+N5xF3KpU8g+HzKd65ZJzH9C56s3afklBvVi\nqDN+IzeJoGBq/DYLjHOrOYoMU4sGjlBDGNRjEzlM5ar6UD7Kkb0GdxEBb33j\nLciU\r\n=AYUy\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "e1a6cf43b29ccd7f8976fb432f57236b9af25c63", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.0.8", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "15.2.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_7.0.0-1_1605994228284_0.8020541804908767", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "commander", "version": "6.2.1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@6.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "0792eb682dfbc325999bb2b84fddddba110ac73c", "tarball": "https://registry.npmjs.org/commander/-/commander-6.2.1.tgz", "fileCount": 6, "integrity": "sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==", "signatures": [{"sig": "MEQCIFGpc0sdG2iBTOgN4bknTSpG5p9ZUtAKMn0XgEd8Mbs+AiBEpET+wpBLUiOIKLMaXwf92zXcCi8zpiVMFb+zkYG1MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113457, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1rYWCRA9TVsSAnZWagAAvYUP/ig9f6oDfuxRTtZ8wOOe\niV22RcHDYTrmrrlB1HVlPUhlcr096FPIYuMpQnQFB3d42tjVSCTdOCTnQAi4\nnjedQscYVg+FiA43AgMRvSktmnPEtQfGBLQB7fQLEhMicHbd0+pJwn5dQkah\nqui3Uyk3m5JxPR8garPFHF2Zv95m7pS26zGPKWASnTGJ5qsQtRAZ/77vZBa2\n51aOhmt4976UMBjDhD1Bfpz+8a3/i75ktP9Vnyqtsw9lE8N6t7meaICBu1nD\nPEli9AHxDHYY4eLnzqMA5I2wXA/QT8tyhh2+khYhnbVTMzj9FNsCroRwwfpV\n/9FHxW0ilnS6J51SPVp7uV9Idzo+EXkf3a45TpMNsJ7o3BrZVqEDGEe94o5E\nCRYjt+ykyJpv5Yh4KfV0lrNBr2BLl6ZvSITsCbxWthjJNNCs1eyoMXpEXb2/\nqKUFvVCdbfMO3OW+AT6ThDsed+hv50YMBeG05KrLpUjdwFq9n2Wwy2Qdu7h2\nk919SyMpuG/GMNjuUnyf2G0O2eYl3Iw5+sgR5gDJIv16jDn/u6kP7EmNl7iA\n6JCjLN1xm2MgPi0/SeJJRfvT74y2qpIOZ7hyf9TeFgZbgeqhyIVRsq4X6vcq\nIH3ppyZQgvHcgt5naNEIYvqUWzpdFfeKB3a0e4V2MHc5Z9YjkAA+f6BbBBMv\n6hYv\r\n=QUiO\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "index", "engines": {"node": ">= 6"}, "gitHead": "e0e723810357e915210af38ccf5098ffe1fb8e65", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.0", "eslint": "^7.11.0", "standard": "^15.0.0", "typescript": "^4.0.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.2", "eslint-plugin-jest": "^24.1.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_6.2.1_1607906837996_0.7689641689438", "host": "s3://npm-registry-packages"}}, "7.0.0-2": {"name": "commander", "version": "7.0.0-2", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@7.0.0-2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "5df69e0962ae9780eb5692f55b2eb650de8f6b02", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0-2.tgz", "fileCount": 6, "integrity": "sha512-ezxiUopEH3fSd/sI0UTlhwR8EXjrgMOCphZGTMqhrjJvQNsrbU2E5KOxiUTPaZDxhYDFZnBe/GDSpcI326MTAw==", "signatures": [{"sig": "MEUCIQC0Q1lj6Kan1D5J/rdlJ+bFoc64HbYyXZ5EKvwcqfCAGAIgbu8ozkdgYJbVD9NwbHgjCl3iVPi2vYodeeG56ynWHew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1xjeCRA9TVsSAnZWagAA4asP/iClE0JY9NnFa6aSO7Al\nxjvpaGghePxjOB2vRP+1gCQzcinf2SXeLbSxGzaKpSxHHuyFMCZBjhqvz+YE\nQEDJrP7r9stYIfigiLHpeZGnH03OU1zD3opTNumjEC3himziTjdBSaS+SvrV\nP9x68e4u9E6fj9A4cJChh+exbX8nxhciNlteDY9LDRHcVfXECkW24+g60O8i\nAd3sLMGE9BLSPTxENcJFllZWtvLoR8Jo/xrFWi9QbpZ3kMf8YITdZ25K+7qJ\nu3vdiLOICP/WatQ3pkAQKeuwt/jOLm1XJMkfJV+GnfHPATbqP/c529RLrJee\n11188PB1nZjyNlz6lKh10WLfpe5cqdDp+fylIrxJ5zW635xEG0gVpRYYJXNB\nNNTRMtdb9561UQDWQh7tqWjo8RZQcWHUyqlRJu4mprcURVSPSCCR2ZsPENyM\nT8Dll8V5Ve8ctEaVzWeqeft8f62VlvaeCG9N1xPtmsRFP84FpGPIqvq7Q/7F\nwA7hhl/sHrjzTJyUoBnNUxFlreMEWIsrA6428VOFF/ibadM+nrwIYoMK/HKK\nyLWwKNZAX5hmPVBu69YiJVhqT5E9EjcJdJ2aheyUqnSy0bDpEHEJM+dWXqBO\nFhBEMGrfIN1ov8mSx976OxmTgV8onX2KbNtPDqdKKO2eNMbAH30PBGT9rAeK\n1+pN\r\n=5qeH\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "index", "engines": {"node": ">= 10"}, "gitHead": "4b43f66f868c3810467cae811fe9292382dc7e22", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.15.0", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.16", "@types/node": "^14.14.10", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-standard": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.9.0", "eslint-config-standard-with-typescript": "^19.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_7.0.0-2_1607932126335_0.020094620686481157", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "commander", "version": "7.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "3e2bbfd8bb6724760980988fb5b22b7ee6b71ab2", "tarball": "https://registry.npmjs.org/commander/-/commander-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-ovx/7NkTrnPuIV8sqk/GjUIIM1+iUQeqA3ye2VNpq9sVoiZsooObWlQy+OPWGI17GDaEoybuAGJm6U8yC077BA==", "signatures": [{"sig": "MEYCIQDZzPLrCSqglA2MMIXAJgTBpuzEAd27SS5X3U7i4nhWtwIhAMPaKjMoORc2kREPyV8ccq6iRKUIovJunwWDgjVRsIQ3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAWSlCRA9TVsSAnZWagAA6bIP/jKECd4+HOOBFE8gL3q7\neTibJKKsTe9MT5h5DXjNaayZm19M7Ak4kieI3/zMtsOfbGLK00KxbIyuhQUJ\nMJ46e9xQ2IuylI8xen4WVu+QObQO+C1377pfEPBD16dhnN8onb5qBlicOpVu\n8SNgEJETQ7uICP0p+Sajvn4F5dIQZujKT30xU6xVhJF90Zh3Nh0GeNjp5CBn\n8Nrrr3BOy8/stzPKopY9sL5BekkbUyjRXWLJEe/eN3i35XRnrH8Kb0SZXHz3\nMDP2dkKzOsNZrZXL0jha9xyxtaS5KyBfwMK9uVkp56PACY+YNWhhxw6u3JYp\noyKHBrpvhNGHeZ6+3MT5Vm1Ypeju/BtW6pHK5v+wdNgukcH2M3iY9bw+UGhO\nJ5/2ONrpin+iHZHX8dCwErH0utgO4QYX+o8iFnoQw0P32u9vnkz7uhROR0NF\nUuP0xYU9+W36xVPRsB8uKXDfqT7kniBdTziXhPTuF8VyaRXDiViVfTDUiczh\nPnBqaRGTGVUqVs9Dmkk3FEsDXf1nHiWcgKDwiTR7MdwcELPIEb1UFhgtJNQP\nAGktBMiajSbJ2rqx4w641L7rnWLqsFoN6h7jj7yrC6NrzzxzcRWlbgbvO8Ei\nr2PsgrVkje3B9ujaenaI1mkaK6bsS0Byj47xWXriXd/ewSHkL3NElVuwdiEq\nvqd2\r\n=8edf\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "index", "engines": {"node": ">= 10"}, "gitHead": "034ad9f326ee2b26001b864adda5fea465ee148b", "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS", "test-typings": "tsc -p tsconfig.json", "typescript-lint": "eslint typings/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "eslint-plugin-jest": "^24.1.3", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.12.0", "@typescript-eslint/eslint-plugin": "^4.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_7.0.0_1610704037116_0.538056848331937", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "commander", "version": "7.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@7.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f2eaecf131f10e36e07d894698226e36ae0eb5ff", "tarball": "https://registry.npmjs.org/commander/-/commander-7.1.0.tgz", "fileCount": 7, "integrity": "sha512-pRxBna3MJe6HKnBGsDyMv8ETbptw3axEdYHoqNh7gu5oDcew8fs0xnivZGm06Ogk8zGAJ9VX+OPEr2GXEQK4dg==", "signatures": [{"sig": "MEUCIQDedEMG8CddTIoczuxvjC6DZr+JY1xo2tVWms1LPptRIQIgTP+LfNHQR9ZcQ91LDhGJxnuOM4anJ8EI5Cdwh9V6zGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKiG7CRA9TVsSAnZWagAAF2MP/3lJPrZx51HV9ixvKakr\nMbu88wHvSmbvpQT+C6rULEm7cqeNd2Z5zif2++TckXvVvbsmOwD5Vjg5Miw4\nLsR1JnufHrgNDSSL3dXiksYjFHTijgitDGIXD3CcAKOSBLoSbOua4nkGj70u\nG0InzexV3QqpXNgguHRfTzuWAKw21ZMfMxK1Xy3/WuoNTha2agTKjury7L/z\n6QYR7CH1NOW1tlWM0M6EUxVH29IicgN2lPCPZ07B5w8A4sWleV18UtncsSkn\nC6+3jwz/SnrJXbipl0ngOUsNgbhNEEa4nYen/NraTjP6D595wry/3hCMTCgX\nCyZTWvb3HYuFO2uJyuTqCRI5X9kEafkaiUAzNR0n5+mATGy8YTTR6NPJTb5t\noAJcaIdIhTKksTmkczpgcpQxdXWkrZohS+e+BZsNIzrIFlkZ+R/99qcp+4zg\nVg9kKYknxzJzUK/lTmf3F79RPa/nNCcpdP+S1mlqlAowz/8mxnFMwaJVaPLd\nChQghCu6bUtVpSJ3lY+skH8Nrt2PJ7NoZT/Kg3VdLLMctsF7RFoheRZ1t4hz\najZwlCtqt7A1E/gMdhBBZ9buiY/kAB5lWbE7aXAX5bQsEPVeH5QRV11KImRK\nxohv4ykTRXXrVuOoTpT4PdwJwdL7/r0fXqymuA80izOkTJx8eCc0obZWnCYQ\nliPe\r\n=E36w\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 10"}, "gitHead": "4aaaa9de3fd09401afe3894483193ba0e2e512d7", "scripts": {"lint": "eslint index.js esm.mjs \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.5.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.14.0", "jest": "^26.6.3", "eslint": "^7.17.0", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "eslint-plugin-jest": "^24.1.3", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.12.0", "@typescript-eslint/eslint-plugin": "^4.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_7.1.0_1613373882385_0.7205821327578177", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "commander", "version": "7.2.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@7.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "a36cb57d0b501ce108e4d20559a150a391d97ab7", "tarball": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "fileCount": 8, "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "signatures": [{"sig": "MEQCIBflDnZJYcPmn5yXDFWwb4LBZUuBOgEJi9vXWkkVGnvDAiA+SOYBJIcvrnLcyhTsa0RQlj+iRp3vQBT+kTFM4ZDDig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV8EACRA9TVsSAnZWagAA8+wP/07OyprjrjtHW7TDf7ek\naIJdj4jqxdT3v9io7Xv1gp4c5GodhB4ACE4GI8XcF0T0ARZIeFTFnUlYBhz4\nfkTf4YOn5gbSKG7nhpDKKm3LEHpOSstnjsX43DCR/eZfQMzRxsmYpj+jL5Rp\nrS7ATBCiG+H9ULsL79Yb5EiB6txsUX7vkmBgjsfZK8h0/qtBU9yWIgtjokas\nrWsUD1PnMHUYzUoCsJzrE2Lh9tVud7Jq1ujTeu9roiwEmvf9Vu6yHv81b3KY\nwFmt7hkb7oa2AvhWIwZxsEF08AmaBmsbO6Y3GK1FojAiKWZW3f8ixnws92fA\nlyoVA1QP6921UZ8zUOODFYpjztKwlBrx0t/6AtUGAkQDsI/v71p6aeulWhYZ\nldz0O3wduc0LYgjn0/M2VKP5bKLJdK9/MdAK0YHLFZfJ9hirVNK9JHO/8UhB\nwrWLLMmPVFSa6M55ibVdU+W2TDvWE40gt3VGEC39Blu5VKCpK8K/v30iv8sm\nAtDl9bFogJvcjryYwSdHN+LaxMee5MnUEvE8xRyNJaHFoKMeVZ2b4LTY8Ges\n/4pXzZ17u1lNnwkLSKs1pLajGynZbi8Dcp1v+y7AgfqShecaLWvdlwO+o35L\nf9eZcxl6QWf0VWFuvVPGP/xOHt/Pz5qd66IoegXCpOsPgtnmAAikf5KHcp9l\nYIid\r\n=Lt+W\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 10"}, "gitHead": "327a3ddd552f90d1dbd37f55fa7a88b9553468ae", "scripts": {"lint": "eslint index.js esm.mjs \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.6.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.14.0", "jest": "^26.6.3", "eslint": "^7.17.0", "ts-jest": "^26.5.1", "standard": "^16.0.3", "typescript": "^4.1.2", "@types/jest": "^26.0.20", "@types/node": "^14.14.20", "eslint-plugin-jest": "^24.1.3", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.12.0", "@typescript-eslint/eslint-plugin": "^4.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_7.2.0_1616363775881_0.05048079648507575", "host": "s3://npm-registry-packages"}}, "8.0.0-0": {"name": "commander", "version": "8.0.0-0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.0.0-0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "581657335fd53b1bbc3978341f66d7dea8cec239", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0-0.tgz", "fileCount": 8, "integrity": "sha512-RChUqiJ5hzjkLJ6V5Gcx349TFXiB4FLNgZKRHEhKAe37gMsgByfsUi16b7IuYFYm4gN7aFibfhuIwyKd30v0Jw==", "signatures": [{"sig": "MEUCIQDRSEa/SXDctqdW6heLxwGEWIVeVX4AVxtUkIJ+0hIe/AIgClDagsfT2RY+C4hyIEcefLyCkPPyjrryQ/jPCtsvniE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqZQ4CRA9TVsSAnZWagAAjLcP/jF18uFOobuHGv5McMy/\nK0qcwWW7fDOJi2yrkWI3VFder5wYyMhHS+t0CUIuK/MLus41cJe0OFAL+3qz\nLDIY3H2QLcPjvnrzY3eDxxNY6baW3QvQpfsQrCRo9iVmF3zimJnHQpTFzpZe\nVfhJvcomTgtxGxlnDtd0XLyvD4VMe2QLgYK0JwnMmZQFRZbk7Tn/nGNGGYNb\nm5DTN2vXKTnvziT95l3CFI4rUlK0R4L4ExI/fWmo528ryKR4sVCYw+9Do5A7\nR748SCdQcBpoYn0VQLYFEbHCL+ocLF3vCuAssUmT/di5mEbHsMu0kTznMg4j\ncidZiODhG/OY6yoqrHko5PF0Uigv4UJHRbjG9kQM7Wd5PL5mHDD1qGJ5bVmd\nUFd5oOoM9o1OfbFgo5+DUP28Wd33IXAo3X5cpPtAheY1nyfxoECkg8TlHsmx\nOyAU0xFTIhxVewo+WTKXnqAf5M8+/z3emYyjsGtjP9IYgsyO2QC38C1iBd3j\nRajySx6ryMlIVNsuZPb8wXRvUl5ZMqhfO1/egI0fEnSYJ5rdNH57awu+9EZH\nsoHiLHcIEh5PP/TWHn5puVqZ5BAjphi4SAW4tgjSBYIztccFk+JVLt0TT3or\nu4uS7A3bD3KrFP1MA6nxkDpNCExfF4/bqHciwMqpzQz6drI4qM+sIkBQOcCH\nOozP\r\n=gU0F\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "ff7658cb0ddcf20cc13f7b792634867771397eac", "scripts": {"lint": "eslint index.js esm.mjs \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js --noEmit"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.15.1", "jest": "^26.6.3", "eslint": "^7.26.0", "ts-jest": "^26.5.6", "standard": "^16.0.3", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.0", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.2", "@typescript-eslint/parser": "^4.24.0", "@typescript-eslint/eslint-plugin": "^4.24.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.0.0-0_1621726264093_0.7665670974009267", "host": "s3://npm-registry-packages"}}, "8.0.0-1": {"name": "commander", "version": "8.0.0-1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.0.0-1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "beb815df52cc9d0e1afcfff93b0f94b79a62a8ba", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0-1.tgz", "fileCount": 13, "integrity": "sha512-tSrRWF7x0QOO7bjPJIWxOmFtrqcUGWkyp4zANM8ZholgD2gtw9zRNtM5RtbQyOQAu/qp01tnvyFmP8KrlXokHQ==", "signatures": [{"sig": "MEUCIQCmJvqZDOONkS/vyQPIltQYo7XoLOk0W6XieDSLda2qiAIgF+C3kG+d+c7pQiDVDH0JYXAwCIh1M2PlaHHn1bVINMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtJCTCRA9TVsSAnZWagAAdu8P/0NtcPdmNQr8w53gSQbd\nonUl25G8i89kRNIqJCIF50SKl1Y6rSYwve5yvoBcjgV4SbO3TJPBiVA+0mur\nrOCK7vsckxsamxlAV2rFQA/6+0rwxn2fmrVkSfGqEdjAsvzUW7pBCnnCnPkB\n01xQBdYu8fddsGUZmzscnwUSwXQSk8eBNFdIiBU1gKko8F+rHxdiTALw5DUO\nIAJP7z/hTJtZ3bfYrAil6p2E24v/KnALQznQiVNpf9U85N4rRxN4RPcFSIOA\n+1ce6tmu4Mo4tq/KMLGPBXztPMsC1MU/zpJYqRQz4I0S9kxiO4e2R8IfhtAd\nCkWqCI5+LBJSHtK5gP4+1nd6DlznipyOjlCMOa1AvOQddRaGx4dFKXTMQRXJ\nGunSutktE5bxxT/OI6hTbKHPjjtgF635ecM/1PQ1E8R0I35xUdDfx5mZMWGs\nk0cLEig/rDYnghpufnx4MfcZUeE3EFnk/OXo3UvH2QMakGl0lZW0cCQpE8ws\n8qBrkqcStjKepqjeBj8LigbpgN/1oZDsCDQQB8X54xhTqseTk2fZjT8jfDZN\nucC8UrEqB/Gd7kgSafPU14bOkcnZQgbaWKXzT2vKAynd1BeRwtIXL+F0hj7+\nHCZdBdybZtIczAumUTNg2uRLnZJk42hhnQ6queaYjnAfa7/cIdl32OfJ0ALM\nxbUz\r\n=6HJ7\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "5ddc41b5503114b50cb6ac6e91baa8fa25a834a5", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.16.0", "jest": "^27.0.1", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "standard": "^16.0.3", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "@types/node": "^14.17.1", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.25.0", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.0.0-1_1622446226702_0.225051785341156", "host": "s3://npm-registry-packages"}}, "8.0.0-2": {"name": "commander", "version": "8.0.0-2", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.0.0-2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "b661866c4fe85f254f94e360eecf0b631792fba7", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0-2.tgz", "fileCount": 12, "integrity": "sha512-Eo8<PERSON><PERSON>chZJS3CORoBTljR385shpHd00uYXBQPRCbtEy4P2f4VpwvV5+AKYAhc7QLxVj3I/cR6nHq0MaMfnPuVbw==", "signatures": [{"sig": "MEQCIE9jTfM3Q5xSMucFitHv+36JTJYnz6gLnVRDuMK3ZDtkAiA/WDiMXZST1L9HCH0OqEN1A/WTgmeFRL5n47LJi9AlyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvFDKCRA9TVsSAnZWagAAZ5oQAJNNDQO+Dr5AgWuixCrI\nRu/FimD6JbTW/r6Ay+D8cDiHW0ihCfaxKEaqSBlxQyIRV5Q/2PfY82UstWMp\nPrKVQ2CSzjTq1SjkVNBPOXrnuOQcaA9M+zL0AgqZXfrorALkuewEc5n0pzBZ\nBaET6CUGvTrhI2JtIxGLGluCM36pbVWUDfIhDkWjdciCHY9m96JwrDnQv3lP\nY4pATulifZf9JAapfI8ssy1mcllq9ZpCwFcbUHXvMPPox7/VRjZdkyky5l41\n4nC6em7yAGVybU92K8Pl4T5caklfUPlpy6hetev025JqBo5Q7aVLcujDtkrA\nvsIFM89I/0l3d+kEBjj896n+ZbSUOjArvVrGRYCpKfuDwmMLZm3TbCIrBsRq\ngPfntAlbKj6BX6ig2BG8Z1TIHdqaDWKPjs5iXHNNV7wc33NShy8e3+z63nmB\nCZLVtPw3EPB2gshHcMgyAiyWXfKqIsmIwT2z8P96qiqokN9Y0Lp0oREk+aTg\nISbzqmak7JORpmjTjZnZ7vQvK2jujV6IPAv2TpVxOWTY8HQJZi7lZOyrDkbl\nJS4UViOfEJb/kCOL8qMi/p44fr+I475pILTPWrwTQ+oJ5Fj6rCJCH7mk7pte\nddrJmpuJOk6sm5rhCaUJrSoZr0w/quyJJFPIvtWE2M2z4/bhOVFvn3KSHHJE\nd5Q+\r\n=GEhw\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "0e204e8babbe5290c8e3670fc8d9b8f6da6b8c29", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.16.0", "jest": "^27.0.1", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "standard": "^16.0.3", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "@types/node": "^14.17.1", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.25.0", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.0.0-2_1622954186266_0.5783033530962387", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "commander", "version": "8.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "1da2139548caef59bd23e66d18908dfb54b02258", "tarball": "https://registry.npmjs.org/commander/-/commander-8.0.0.tgz", "fileCount": 12, "integrity": "sha512-Xvf85aAtu6v22+E5hfVoLHqyul/jyxh91zvqk/ioJTQuJR7Z78n7H558vMPKanPSRgIEeZemT92I2g9Y8LPbSQ==", "signatures": [{"sig": "MEQCIHcKA69zrYUV4bnOXkidiiE5MigBOHwIb1AOzIXjrT4aAiAunx3RJuJw80pxhM+6A19A76tq+CC1do0m65TtOtKIhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1ZKwCRA9TVsSAnZWagAAt+MP/j0fXBaAOV40TEeZwDP/\nO3adO1jhSYPTP5pySjHfo5plUw5Ynvd4MzWXwvl3by9z1oqNB2MeODH7S28c\nBXqy1xI0K5iI90jSu8/ObQS47UGpTIOmgZn1643h1C+yI0Ha279nuloj8BkT\nDOCXNXIOYq2yxy62SoziXiNpjLcjtouv7X85cqF7K2gOSBbHXQV6f9+6L5JN\nZxgP1+UOtwpT0i6BL1ux5fHhnaOm71Wwo8O98mlWfqZi+JOEpSiVPMYXN782\ni2DcVAEEMCv+HCFrhbAA/bGGAqv4bnxqsQ/V1P9NCA50ZjIS5xzidCA5rvdQ\nokdsxK7+yF4DSJUHM92mRdFl34RNQ+8Sug/Vg+/ssGXo3CJaC2eK6+cFBI6w\nd66Fpuqs0E6nuNu9vWEpOhZ70EOoD2LW5f8ED3fsiLe8P7+R3Ks2AfzdkHQi\nlrKHKHNcEk1ff+E3yB3R0Ont6DD3Jhe7v8euYMc5JLWRd/dbd/ZUuQIEtTVO\ndsqwMyQKTg+JPhZV4x8lTuwX0ay01kKlK0Ut/2mXwWRu5xeXIVI7hQibA/jP\nrAe3p2wtN/RIK7F8cPeE6oXXKOunYgN65zkTWZGyYxjyGvhTHnGd+E/0oNiz\nWk95Li4UsUSOhHmqoqJ/v22AnZtZQMPTa3o2qzZI5pnNvYGqU710r0LdUXoM\npOPS\r\n=jWYQ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "80054ba3756853c1acf80e168ee7d44b63ad826b", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.17.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.0.0_1624609455896_0.4408500319550237", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "commander", "version": "8.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "db36e3e66edf24ff591d639862c6ab2c52664362", "tarball": "https://registry.npmjs.org/commander/-/commander-8.1.0.tgz", "fileCount": 12, "integrity": "sha512-mf45ldcuHSYShkplHHGKWb4TrmwQadxOn7v4WuhDJy0ZVoY5JFajaRDKD0PNe5qXzBX0rhovjTnP6Kz9LETcuA==", "signatures": [{"sig": "MEUCIEl7B2A5bY36X258Csd0Wun2OqtCJoIv117te9edJCcsAiEAqSvBdDRQWMTjcb9iWpb3PyW8WaSulod/y+iIpcizHFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/4PmCRA9TVsSAnZWagAA4zYQAJRrf/GUVlaSo/tFUZ1i\nXp5dIJKCj6xE2hzFvi7MEzpzbX9Yx7uAiN4TQ5Xx/MvyXayvo2bo9pTcUrIs\n9CAnqGmQh1CAHJnSxt+bzZAQ/1c+MG02TFrM3W4zMbgcjyEz24sJhh8nhlxI\n0AmhQqtNqVOwgvDdisv66uDD/e0Shc+x4iroouFQJv7+tZGBV/JU0HsZvpgJ\nVreTI+8KldNmbwEe7VGhgc9Y5Gl8DEzPVTisi1B0hJdueQJFunO6mInzBLZI\n6eA8yIQ6krSogha1hCHifcHU8qkZ6BBQUqLKG1O+lVIMhichumwhRKofBU7k\nWKK/fSLhTbfSZusAksAXoyWyz+2dgh4wFRMcNiADYCBymxaq2a75GcH1pdIQ\ndw30Sqb2Ho5SJAvX5cHpLIEjH7kC2QoLdtovgvxLNg9jKtFSloYZy6uZl9Sd\ny2P7Q2OZndZpq/ZwijLA2sXgOB/EoRPPPihDPOVT+K1OIwMor+fdRzJz7ZFj\ndDieqWCYHt3Xjj4bRRLX7l0sDiS5V5/xxl2YWr5ocI7JGf92jGnRGAxdoRxh\n4r/t57LuJ2Sp6GV3F+QC1Ek2OfIVfz/jL+ZModdh0oW2XvfXh4n61iwZa83H\n92R4q9HaBSBhLRQl0cz9lf9hmrs3QmARmAqRrVPlNfCegIgTXKoGY3jAFTAn\nYS5V\r\n=JRn7\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "a9c9f17c7eff96b8da8c2b9d01751d41f1eb0ae3", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.19.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.4.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.1.0_1627358182680_0.31172909264133697", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "commander", "version": "8.2.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "37fe2bde301d87d47a53adeff8b5915db1381ca8", "tarball": "https://registry.npmjs.org/commander/-/commander-8.2.0.tgz", "fileCount": 13, "integrity": "sha512-LLKxDvHeL91/8MIyTAD5BFMNtoIwztGPMiM/7Bl8rIPmHCZXRxmSWr91h57dpOpnQ6jIUqEWdXE/uBYMfiVZDA==", "signatures": [{"sig": "MEQCICbMQKxMlq0M38M7+5p1kMbmsua7qRznYK2gcIQRopWeAiAUru7//z5x13mAwnO8Aj5dmQgiGtJXBYhN0ltBi1VlXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOwKQCRA9TVsSAnZWagAArDsP/0SuFT++uruPwyIyLFCL\nEOKcvGK7Pat8Ze4oAPZO+MfIdjOI6kEDkootxu2IphUxt4ZA3Jfs8E/1ATQk\neL44YdewOj5Atqke3yIjcAkobWdDYdNxYMVh6PuT2QvpYHuSqNBQ8p0c+qh9\nUpLIDv2ZOshX2kaZTiCGVxp8TrilxJLSwFclpbF9lyWekJVhR9ly+ViDMj1K\nFlgG2uC31A51AjGTcKujLEXN2mzyIqDxngYxfB8Xw1nM0zl41cSEjK/Vmsd2\nN7B1UDApsb7UfCwPp9PGuhMZoJwOWqOHT7qjnkHIk/n5QovBu57hP+JeeKMG\nUtLdDTPmmifErOYxyD2WUkcbDUH7/Ai7Ofa2E5Ubi3Hww3gIw7iFZ+1ZxMT4\nQcIFg9LH6mv7iuAEJcvvBJoAZPtdwMubEWYUEDolbQA3YDDNloLRVTf4nQO6\n5CPsbTshU641VQNTtqhCOZP8LYHwiW9c/GuHsXiVEv4sGB9oY5lLPfIxUNLS\nwM/OWn5VDJD4LJHzB+yVG3VNBnB9HzmWe7V+tojaknmBu6NsASmlJBaa7IxX\nm3v0PAmfwaZFYv7vfbJfSotchOY45xQ+FasjPONG/gyBJNrPifHTZDzwgrXB\n2l6nWGfjCZAnxo3a3MCdLeKygD3EhEB/nFq1/4wNz1JNBdnUMwaRibXuteTb\n6P5q\r\n=ig11\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "950f742e60ae6b30a3d4e945db6d4322919628fd", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.2.0_1631257232689_0.25638681421232046", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "commander", "version": "8.3.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@8.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "4837ea1b2da67b9c616a67afbb0fafee567bca66", "tarball": "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz", "fileCount": 13, "integrity": "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==", "signatures": [{"sig": "MEYCIQCTvkYjMZlMkau8hM4o9CT1rJmed2t13Q6eXSEo+sGsoQIhAPj0DACacRAViqyCS0davf0Biah/5ZIUZ7l40DkPhQl0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151267}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">= 12"}, "gitHead": "43f4743864e2f670db5eebcf88c92aa4612c54f1", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "17.0.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^26.0.23", "@types/node": "^14.17.3", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_8.3.0_1634886126524_0.6328909123201889", "host": "s3://npm-registry-packages"}}, "9.0.0-0": {"name": "commander", "version": "9.0.0-0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.0.0-0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "4709bd066c9b2477ded951f68bc381a9e2b1819f", "tarball": "https://registry.npmjs.org/commander/-/commander-9.0.0-0.tgz", "fileCount": 13, "integrity": "sha512-t74Nk7fzdtI4N2lxWUKAUdSmCZlHINFYKgARvo+gXHjHY0MUyTElpiqcJKimiSvZg2cMzjDdTwwr8Qhja1AcEA==", "signatures": [{"sig": "MEUCICxDuN3SpVGm4Q8TMl7FJjy+Wte/FGSy3y7mCFQxwQSNAiEA3waYO8mAYBmmoPT9zPjv1iw2dr68bPPvrVt5k+O+Zb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwruSCRA9TVsSAnZWagAAF04P/j302qtQ+HhQKc38KT4a\ndVn7w6wPQRC7apmpMucVVL/CyEhW9f8XthvP+i55yDnhXdMXNPtl3UcBTc/7\nSW4QwJbkIL7dlyvEYX0mf92JtEJcdtXJlaZEI+vjHssUrHOfyXX0OZ/rNZBi\n+cw/hKfvZfjBU3NhXuHmpgkEgllYbe4B4MEQy8+r+4NR/K7MlFk41bclOt9b\nth1h81viMumcQrTaO2M8Uvnk4s2NRi/wahCn5wsnYdGwgP0hmaWMEEKxXgUr\ndYc8hJ71SJ1NyXxD/k75UNrHW/kUugb2IpRYFY9SQiAlJ7DyKWO+wWQr4hs5\nO2/Lpzt8YgjrA0Wyo8wPHxm1k9DbiDk8i8MEw3DBVl87Y72Rx66cUesQcsHq\nwfTStK7qYbR6OHmpWHZpSdig2FveMuRXz3SKAH6UNvPOwB8XZJDtbeHCk7jR\nYSnln3Sx5+bKi08qM8e0bnHY3aePG0IwTDDxtsor3JWXVHUCHaiDGrozq/ox\npS19vu8+vTcMkj89KWw++5kVVNEulQkvuIenwyzJ9AOA8Z1fpoVOMvppyn4h\ndVOpFMrPdadCTtmQIEJQ6NEqWgWwPeA/E4T6vF2xCLam2w/zOpdGN59vSqpH\nFFpU84m1LobbP2e/oNk+rld1LMLGm4dL8wjXwq97KBAZ81YteCIke3BwOhdn\nyTgT\r\n=SSF5\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "253f4ff7486e4e1ae09ba35d16964f45791c6a7a", "scripts": {"lint": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-lint": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "standard": "^16.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.0.0-0_1640151954683_0.6313204506781085", "host": "s3://npm-registry-packages"}}, "9.0.0-1": {"name": "commander", "version": "9.0.0-1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.0.0-1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "4e462b5c7d1ec4df59acbf12439ffed46a294d77", "tarball": "https://registry.npmjs.org/commander/-/commander-9.0.0-1.tgz", "fileCount": 13, "integrity": "sha512-arL8xbMrDr6BfDbgS3RHICRBc7hKrLuMZVWyWswvpVYd8jLa4cv3AnEb/DnK8NenvTEJF5NgPa8/K9oiY94ezw==", "signatures": [{"sig": "MEQCIBGWcoycjWPRiPOpsRzSB/IWkvEClTWaJlMsNNFvBNPPAiAKXPhznjNuCu661vW2WfvzXzuc3MLWJ6J0joapLqiPFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4d4OCRA9TVsSAnZWagAApP8P/0MS7jQg5JOwabHTMyzb\njBTpLLnIsCSF8iqsPTLOF18Fj+SrCWnhZej6TsZpQprHZc/3+jqhfC3X+LWN\nUU5WVRFbFW658gkt2SYgfRZQmaBxHKGp8Ns8amqs682b99oqshIm0q3EZLcH\n73EviGJ7acnrIRuFf6FDXy7B11ZtPwDEiWiCKVR5ZJhhnF6+elLisWWh2MFH\n9odViCp51iecvq1VZEout4Avsyhl1FadQMu/sS3vGTYwrApZLDS+WIblRtlC\nuWCT31lvn6x5/cenb94rqWCIL19/KZgQ6ydxQ2qHlYrWxbfTgyn29leoPN/2\n43G1KPoMsi35Sm1iQF4XG5SPTVXZrQkhAHsTJeHIv9B3LYgl/j0kQvd9MtA7\ntW69LNGapOzk5CEqxSo3ZfkVOcFO9vtupG8dKY2QNOcRtgWmGbIiUwbNQ72w\nZR5qUUYAZMMJGigVrViV6JG8VfBhyRkLMnJ1UWyT18+O33m3ph57TVJbDh5/\nsZe/K2BJkaG+jT9v3sQpQpwvRMU+NzT9tWCMRybY9FMZLZ2nADnud3Q4MSxS\nosTqX1lUzguxVoBRXZsLDmoM58CrzAatjGhQlP2cLysNtm1g5nS/QWL4wTAt\nDQWb3YLtL8lEw05J/qi5Ox0/nkctXQ86VNWEYraKSlshvY/i5byYJjNKdV+2\nl0WW\r\n=zj+U\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "c58fe4d32f72b9fd6a6bfb64b499f6d3dfd06bb2", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.0.0-1_1642192398569_0.16921628473830674", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "commander", "version": "9.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "86d58f24ee98126568936bd1d3574e0308a99a40", "tarball": "https://registry.npmjs.org/commander/-/commander-9.0.0.tgz", "fileCount": 13, "integrity": "sha512-JJfP2saEKbQqvW+FI93OYUB4ByV5cizMpFMiiJI8xDbBvQvSkIk0VvQdn1CZ8mqAO8Loq2h0gYTYtDFUZUeERw==", "signatures": [{"sig": "MEUCIFMXh1L+6s1reN9h9hP+WQ5oZmSa/dPk1Cq3gx3DS8AzAiEAiDdbbzeELRAaIhWdUWKBgtEduAbakvNyKvX5y/hz9Ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159103, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9IqwCRA9TVsSAnZWagAAKfEP/1+5z7NTXJfEaIXNH3dY\nesLyZFR8SP+Tmm9w4W8howA3lQSJJRGU6T5LywUI5x4yc6HLornpk5nFJnIN\n9B1PTjBlgRl003psudg7EuCZMSbJWL211pwxKqa0kmj5l87S34PRe6wGGaEQ\nBiDTxgt0IUu+a36yA/L06VLwpZeO2kio8z6mPZCEcQwfDCKjO3Y35jr78N3S\nliO/pE4t76pDRqIma8nrrY9nx0mYzeI2GeX+XoqNYLAX4NbBRzsc3YKF5V8Q\nMAvlzDSqTmTwNs8a3rrvigZoTrXqLHkcwlnZz3tweF/xy+WD8wC7KhxjRvve\nbImAVhN8VcVHsL7J2Q6CrBv/mG8yAcmAyTo81cGei/GZJsIQMm4XZ7DFVheY\n57l7Kw0J8xFQPH9MVGW3Mot5a89Fc2/3IqndoIqbo9DwWWdvzLBxzdSA2MBa\nPThFf+5VcrXe71VQCRBWfcvOJ0mUYnwuUKZgPfmMNrR+SgqjaxSKO3tvNodK\nmkj5q8Dq6u2W2O+mGOMlfyytASH6/0LCFA2ZxEu5lBJnBbhb6066qlPpjT+W\nw5or2WTJNSd54WJocIesBZs5ZRXsZWG/eTeDHHQ6MvmzauR5+iYXf9QRYPJt\n7M5TyL3lmX2wGDiovIXZfAlzFRDxq6k/7UxtNdp1UXEgIDxqR46uHGC26nJG\nwj41\r\n=hbI2\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "02a124c7d58dbae2ef11f9284b2c68ad94f6dc8b", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.8.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.0.0_1643416240849_0.9195341872767004", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "commander", "version": "9.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.1.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "a6b263b2327f2e188c6402c42623327909f2dbec", "tarball": "https://registry.npmjs.org/commander/-/commander-9.1.0.tgz", "fileCount": 13, "integrity": "sha512-i0/MaqBtdbnJ4XQs4Pmyb+oFQl+q0lsAmokVUH92SlSw4fkeAcG3bVon+Qt7hmtF+u3Het6o4VgrcY3qAoEB6w==", "signatures": [{"sig": "MEYCIQDE9y4pIADOIH0JgMymKd82AcrHxQh09VZqqcfonZQaagIhAO2eyxHuUIMPvNekK2O+jG/YvoflHWT2WMcrQOnX7W7y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNB8FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyJA/9FLJGLVapbl69UKld2WW74UouICXSC0/3C/99vr8MxMG7qASr\r\n1ELuhEfpZC2cgcgfdFedGUNIRqlK7ldE6lScmDtx7n06HtNJof/wFh6J2mGq\r\nhLJDPOms0v+jin3F6vOfEwdM4+CpxiQcm8yVw0bSCV+kFXYZkAmBgihFyhjC\r\n/eLMsI5OcGLjNdh+pPQIgv0BG9snZXTZxh9v0nt5LRNQUaCe41uUEYxru65k\r\nrPG+1/0Sg/4HtP8+5IVySxv4be6hQOhOJnusu8IdlSbMmqUUghHNe/pnaWcb\r\nXX5I9tKv1SbAFEsj+lMWZhkl+GU2pJgFYyxHPsRW1p216sqoE5mmNMvymNA8\r\nmkBlgsh5LdFq0yQPQYf1NuAbsLO2jXePzkhJW16cfA49X37C87YLnhhMfBFa\r\npADsvuAWKSynoxVDTwSXtdnmqJ3zpeneQX4JRDNqxYP7skSaowgAkTLFEXcV\r\nBPyg4PRd3p6MBNk94LHF+8L8f/3vmAPvbvClrqI/TIgjGhBrgWoEphQG5AwD\r\nm3moX/gdxQv17p3DMkKWEr7k1S34vcFc/69YfNlXRyWsBGwD4d3hmE+7eAn2\r\n842Id1dV36c/uuYx8i03EPUz3l8TXHTzYlZkXZZxA2wEnJkugmKlgip+WjmI\r\noHotsPgtFijECaAJnwGzzpnCoYSGRXyc6kU=\r\n=HqSt\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "5bddedf2496e31c0a5d3eae7b8419b09291cf2fc", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.8.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.1.0_1647582981419_0.9145293492260986", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "commander", "version": "9.2.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.2.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "6e21014b2ed90d8b7c9647230d8b7a94a4a419a9", "tarball": "https://registry.npmjs.org/commander/-/commander-9.2.0.tgz", "fileCount": 13, "integrity": "sha512-e2i4wANQiSXgnrBlIatyHtP1odfUp0BbV5Y5nEGbxtIrStkEOAAzCUirvLBNXHLr7kwLvJl6V+4V3XV9x7Wd9w==", "signatures": [{"sig": "MEUCIQCIBIQ/rLBbQNE86QY3zNbwqpqy3QYTlM8ck9xYLxv0mwIgX/aJgq/15aQGXrGeioFgCERDQphjPnPt21DQadBZkCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWSo0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZrw/+OV277VrhZFxWfjn5vyTuOD8427DBYfvux9Y6n9AUDH8kCTyc\r\n20TvYkuhU1Tk8JoFgP8IiRrhvKc/a2du3BVlgyshdqL8aNCBhGhGpj/DK8H9\r\nZn6jqAuOWR1ARKBS3iTJNLTG4nY5mdqDR0YVCCRGmuFTiiVjq6zA8ue3C8W7\r\ntLZmjnOKSSTfQUdGfiwGRnUdRAFYH3IRX1DI+4FWDynZAe+TDujTByW+ZL/7\r\nZmvOai8gu3DgFQWigQn75bu+MD8qWZrnc5s+oSIN31KFlPdsDpKqnPh8MP0/\r\n3DDq4rcy1Pf6PXifrXTc2HhEFxxs8ZIvOqxmbfG9VQxGfmkW+whe6p8fn3Eh\r\nc+dHMhSqicBP3V3O15sZJLNSsPDevahcJPUunnB09Q4W6C8DpFVvaTUNIrWa\r\nQFIal/dEB5BppGmjs6w9rx/lfrV94U5qXfUbicyHingryWuwXS3A+smZgtXS\r\nccZRxb34T/Omr+WHo288BOhu2V8Nzwhy1xfo7wY7F8Qc+hsGfyi83W+Wq/YQ\r\n1Hsdk22jC+Z4x/INwAFg3SLCAm4Vh2ntzchkClzvZ9/KPOp1qzYixzUbxW/G\r\nECNm94AFORwaOIAvshk56klWPR0RX7odePpPcZJ83vVc2M1e1NYL7pZ++3+f\r\n8XnNmAP9JXG4UvUiuTOe+wKMaFxtBOmykGs=\r\n=cxAB\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "6c26370fcba737d9786a34d0e8693f689a067569", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.8.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.2.0_1650010675917_0.000026128876978726012", "host": "s3://npm-registry-packages"}}, "9.3.0": {"name": "commander", "version": "9.3.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.3.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f619114a5a2d2054e0d9ff1b31d5ccf89255e26b", "tarball": "https://registry.npmjs.org/commander/-/commander-9.3.0.tgz", "fileCount": 13, "integrity": "sha512-hv95iU5uXPbK83mjrJKuZyFM/LBAoCV/XhVGkS5Je6tl7sxr6A0ITMw5WoRV46/UaJ46Nllm3Xt7IaJhXTIkzw==", "signatures": [{"sig": "MEYCIQCqt1oj43rokg2mwu0zJGmult9cP2uHSoNKq0GhMtABvQIhAINfWX2eomVqVK3E44sKduBoE0Cv1TG75z6L6nZOSfQ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikY0qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoafxAAmu5ns5xh1iR4rCjN7Y67Ih2lNFdiYc9a8X2HMShNPb6gFM/F\r\np0RasV9IdPLOnHupbgfCUJLnTxPNwtfT5nOSt1iv7c7cpi8tlnIlEXbBe7h8\r\ntz795H5UPlwq7GvORSnX+K85uFlA1KY+nogsceSAkfrjc1UUjTPSHz+1os2W\r\nMUid8aknf7dQkWxf2R5PErDtTzMAfrqrrdbfcLRLratknK0h9AmFYNlaPlzL\r\nENn3SHi8G/ta915Xzq0Io7eNmnq0gr5mpOOKt6H9c7lautVGQB20jyDeDHgx\r\nIRRHFa2gUqR2hgctkKZsU4XXhGcTakNvunPP6D1S5jQ8cev14VEhkuxV+dsF\r\n6XtSkslxNrEJz/bT5M8b0zb6iMfgJZwVIz2ydDClR1cLoMo3y1JNYXCGf5A1\r\nRVGzcQ8Y60aC1U/uNlgNaurBbXDDztpluQtQb6hpxF+nUfVRgWJf5UanjsT3\r\nYrqvoQOAT6sPUOcMoFwyyOa8G2B9Rc66lb4wKSiO7Ioa5lZ2B83vE0qqPsfp\r\nsgYmVo8JQfsRmj94IHGVhAAhHXtSR+xmhuCAnK17W480/hjgOSjs1IIdH1Tq\r\nck/N9IZNjbKc4pvRA2QAzeoovp6ghlQuwNBbcm996OE5H0z+QwbFtWAECeIj\r\ngLA4hzvU4jqVKd+CQfvR+y1XnawHJ1GMlIY=\r\n=K7oX\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "f2aec2648d68b01d8947298b27093df3fdb551a7", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.10.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "18.1.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.17.0", "jest": "^27.0.4", "eslint": "^7.29.0", "ts-jest": "^27.0.3", "typescript": "^4.3.4", "@types/jest": "^27.0.3", "@types/node": "^16.11.15", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^5.2.0", "eslint-config-standard": "^16.0.3", "@typescript-eslint/parser": "^4.27.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-standard-with-typescript": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.3.0_1653706026504_0.58426621720664", "host": "s3://npm-registry-packages"}}, "9.4.0": {"name": "commander", "version": "9.4.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.4.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "bc4a40918fefe52e22450c111ecd6b7acce6f11c", "tarball": "https://registry.npmjs.org/commander/-/commander-9.4.0.tgz", "fileCount": 13, "integrity": "sha512-sRPT+umqkz90UA8M1yqYfnHlZA7fF6nSphDtxeywPZ49ysjxDQybzk13CL+mXekDRG92skbcqCLVovuCusNmFw==", "signatures": [{"sig": "MEUCIEf6QmdeSaLf3+an3+gq8ir/VGHLuSeUIV8B9B28gmOmAiEA94Oc5SeRiK8vE5Pqx8oLeVR/gdz4zDiAU8vExhMIk5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0QcJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxMw/+JuPMPhOqYOKFnGQQzEVJuQd49jAyjkSiuCk6aKmzXqWsJQfT\r\noxoSoroqsA/JcAAlohpL2kfgFbz+1p6e71R7YbgicU7aQFDExsm017MYGqtv\r\nnd10qdbwmSGIsbtY1I3h+ydnnxmMY5pu4tznTF1PBJa6WtU9/Z0uzNPIX7a2\r\njDuMA5mpufKU89qBF8a3g9h1xUpXJ1ye4AbkkOwuYNQazSGGkjqt+7pRTcex\r\nPH6Df+i3CvUx3X/lGvhwe3NjLDHfVzyaePxYz8ill23tcFGDbo/PNpRNAmon\r\nv3m7O4QGviD/Tth37FSm/MBBoiKZzXco+JPkFcRQ2w673ydJdpZe4a/KGRKg\r\n75AqTN7+Jpx6GMiTpk2PziWYZEYE2bGwXKPxSKA00fXu0VzjTpVYBy7wN9CK\r\n9OjrWG+btaqRwWerf3nqFD8MG3MwVVLt0qugms6AhwndDapR9Ml4D3pydo+G\r\n/JNmUwwgyWMTSgYWpnXZCjP1ryMqZvzo0YRRsrobrLJRz+fkqv9ktzaa0J+l\r\n/ARQjc9GAcRAacISWWbxlADlyTHCohe6yRYSN62rYhjgAWbXTqlB+HlOu1cx\r\nVTVMPEabquUsqSepvBqTnibgtBdSpZBS1nIAGarJfLvzt2/JKf9qNDAlVoHh\r\nmSZvBfheG7qjKf1X8p5lNrfEK10Q4h30tzY=\r\n=EsY2\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "82fcb98cc27164a98e0c5f2c6f54621b5bbceef9", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.22.0", "jest": "^28.1.2", "eslint": "^8.19.0", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^16.11.15", "eslint-plugin-n": "^15.2.4", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6", "eslint-config-standard-with-typescript": "^22.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.4.0_1657865993077_0.6314160407672071", "host": "s3://npm-registry-packages"}}, "9.4.1": {"name": "commander", "version": "9.4.1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.4.1", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "d1dd8f2ce6faf93147295c0df13c7c21141cfbdd", "tarball": "https://registry.npmjs.org/commander/-/commander-9.4.1.tgz", "fileCount": 13, "integrity": "sha512-5EEkTNyHNGFPD2H+c/dXXfQZYa/scCKasxWcXJaWnNJ99pnQN9Vnmqow+p+PlFPE63Q6mThaZws1T+HxfpgtPw==", "signatures": [{"sig": "MEUCIFRS4i402M/vySu0tExO8LjawFKsijSAt1xdtUvEgITWAiEA9RwfQsP2b0dcfSH87hjQvcujZy5U6eSZ/PkDYbY8Klg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNpqEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDdQ/+NIlmWtECOBd8wkHULQPIGG+b9CXZENS0Yk0wNlrbIiB5BP2q\r\nLJlQwb2fDVhMvlmwigF+x2vvgyaubKkKohuc2W/CT7YldynFHQWXLHpZR3QD\r\n60uzVnFKzY5ra8uyWzMgyay0B3gYkhOT0qjVjpSvwUmgBp3XfH3LWezAs6u+\r\nQEH/EACjebIktNQNvqcY2KWvhAiLLWhynRJcN6ONMTlqnT4kTeUj7dIm8bJZ\r\nF3jipDSFr7Y50l9tNQJyBR7Ca9WNx/LINq5VfevjMqP00HMRhKcFMCsQdxBA\r\noE3HPSjZ3Mgf/XALE6rZFlFGmCRdw4Qtu1dSUOd9dB3TsX93bSppiVKWY2ax\r\nAX+8PHC5uvxAAg3rAuzwbe0sCk+LspCh3zbWpAArbQ6h1+uJceEzg+s0mruz\r\nH+YL2SOM0Kzkg1+l4sxvP9TpgLXHxyOBh23u5I1e7/V3TK9Vrxr0THAkFRIX\r\nbUN6w8waatr6vPrxjva09lkV2tHSW9vptCbQ+dHVbCY/KgSGry4OYJxVF3gU\r\nfD8+vTdfU9RIndqTbdLlhXQUJAWGPYKrd5segA/2k8WqKf8Uuh8AFUZTrNVj\r\n4ezYbXQBsIWTrVp720ssxEbF0OSuD/wjSZInPTevsB92Yk3znnw8Sx0JaKZL\r\nLEzRtc+Vz3XF9Cicp0qo1YEDb369BDumWmg=\r\n=mrwF\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "0b4198dde02cc8fc571c1265afa5bde447f6fca8", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.16.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.22.0", "jest": "^28.1.2", "eslint": "^8.19.0", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^16.11.15", "eslint-plugin-n": "^15.2.4", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6", "eslint-config-standard-with-typescript": "^22.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.4.1_1664522884008_0.3388958427835751", "host": "s3://npm-registry-packages"}}, "9.5.0": {"name": "commander", "version": "9.5.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@9.5.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "bc08d1eb5cedf7ccb797a96199d41c7bc3e60d30", "tarball": "https://registry.npmjs.org/commander/-/commander-9.5.0.tgz", "fileCount": 13, "integrity": "sha512-KRs7WVDKg86PWiuAqhDrAQnTXZKraVcCc6vFdL14qrZ/DcWwuRo7VoiYXalXO7S5GKpqYiVEwCbgFDfxNHKJBQ==", "signatures": [{"sig": "MEQCIBbtDgW9BNWrOvjCqr6RFDuuBT/c9591XUP9+8IbN/viAiBR4pOg7rHwAQo5RR0HtkCtZh6SbG/jrC8kXFIrXxcgzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuRmNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWAg//S9Z4RBLYQxfpiLDjtG3eewGccT3kkpJ9bivcWhV+uH9SAwhN\r\ns6iMRF83LUuMfqu+2aC3QOGrXynxnBscTn1wId61AkCiOAYvsZsr8mTH3PC3\r\neMtGZt4eQFnI8Vb+4zyEWp9gPok2MmdlZh5AKe7rYOmYJolG6g84gDwcbUBJ\r\ny3hFKq3P2UMMCw7ims2QAo1a5AK+8w1pzHjMdfb6TSq+dBkqMQnTkKFrUE3u\r\nRQWjbiG2J5KuIzJ0ZrhBCPKgH9oRngBAHvL3miuJa+kUOEpOGzNWySqe28ps\r\nGvKQkpwpnzipnl570vgdgJVBbJqVgj+V1CQK0mA1FUNEor/n2CtTi45l4Aew\r\nesipEpBRb1hVI6leJMrLAb6+pQ6S3mEL4wnzyroIxm2/O2nt6O3NJPbQe+DN\r\n8zZdnncFOrh1GeIUnIhAK4dOr1cMX120J7XBysWN99G+qLK2NVmdqeUsg+nJ\r\ni5hSwGx4tH3OXxR1UY9oYBj2mwlNNmeyuLBNnxnH0NZZAP4jEUmni4ajTLyD\r\n+MK49K2vzZS04HLdQBzCTMIPNxX7dyIML2PSL++or82q4hogF6SRaHUfPtDA\r\n/Sh5MMLnWJQTCcYdZ5ZOHCTF6+tKbu9hTFbTPKpmLou88F4+rAGvVoTNIeke\r\n6bSZwmVDpqEu/PDaHOXID9PAKg3X3VYG+Lw=\r\n=SBts\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": "^12.20.0 || >=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "7a5bd0626a5a2e6353052de0a6985480e51bbb9b", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node --experimental-modules ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "19.3.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.22.0", "jest": "^28.1.2", "eslint": "^8.19.0", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "@types/jest": "^28.1.4", "@types/node": "^16.11.15", "eslint-plugin-n": "^15.2.4", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6", "eslint-config-standard-with-typescript": "^22.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_9.5.0_1673075085625_0.37355873869183664", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "commander", "version": "10.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@10.0.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "71797971162cd3cf65f0b9d24eb28f8d303acdf1", "tarball": "https://registry.npmjs.org/commander/-/commander-10.0.0.tgz", "fileCount": 13, "integrity": "sha512-zS5PnTI22FIRM6ylNW8G4Ap0IEOyk62fhLSD0+uHRT9McRCLGpkVNvao4bjimpK/GShynyQkFFxHhwMcETmduA==", "signatures": [{"sig": "MEYCIQC20DufiOmKMzBPAy9TozrTyfB6OkqptSLk5/46uiMDjQIhAL1lPCbcKxFKOB7z4WlN6BMY9f4o8ZOH4aW1oRGBIbEh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwgslACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Ew/+InYpoqkMJez7x/YUHx3xaoS203GkJL3of04oArkjd3fW1Y2U\r\nILrIAbX6qmHH4dq7OpFaSSZHPYwQkk3AtELMwpZ/0Q0zO3NNmB2QKwAoP3QF\r\no3CXxvjU3u4vIlSEBXiiO2zMuD2bJ5CsP7c7syIsUrv/q47yUDb/GWk0k0sP\r\nTzXEN8SjfTX0blkQ2d4GmDMICyA4lj/Td6VMIgPoBujdYb+TBKo1DfwZBdaz\r\nWq2Tb7YrnAALHR/dAluQT8k8kRsale30ob9YHT/EOII+eJU5VA62MwNlBp2R\r\n7UlUC4ryMAte/E676zo6Ua0d0SxTwJTrE/CB1ms1605xxg1+fVvXBgedAF2Y\r\nUyeRAR3ZFnNqXE1X+KH6P76f2FWlYS1/J94ARPeT6cpT+z4rRXvv+Yg4c1HD\r\nKMdw4GfXHFg1syIOiEC2bbd3ge5OCa3EbpEJOzEqhzFR1AkTaXS9IjmfPe8t\r\nJPsRZLkcXCfhEbhaNKhItTR7UUhvoVl9aTiKyByYFLMulAEo1dxOY4pl0B28\r\nP6J2vqCyMwb/kKmLNtPsF9+n6/ArNIbNcBZgiDBTH0oukL5Zxxq9Q2iTIvaF\r\nsU/xSjP5CW+v3WoqIFwzVSa83uWV66eqPHpGOXxiyPcXRctTTmPQwP8v9J9A\r\n6M//sPEf3L8coY88JTAOcRSMd125BrmMGtY=\r\n=dMOf\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "8b03ab75b5431fd2d58a24b842ba088d621f12dc", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.25.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^4.9.4", "@types/jest": "^29.2.4", "@types/node": "^18.11.18", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^24.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_10.0.0_1673661221233_0.03324843974562097", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "commander", "version": "10.0.1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@10.0.1", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "881ee46b4f77d1c1dccc5823433aa39b022cbe06", "tarball": "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz", "fileCount": 13, "integrity": "sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==", "signatures": [{"sig": "MEUCIAdfY3b4EC42gKHGu+VwkrhYwtZc39I3Gew/XLCd9cmmAiEAgVdaQ7bsLSjZ6XPBVuJves9Tou2c5mm4lYu6l5bLI7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOi/EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqghRAAjGC4IvMW2ZN86qlSobS47HdFySSatonSysNxhXIzyqqIAg5r\r\npO3ALz2CGUI8fpUTUzpZUfa2fuW7oDxbJAAilqLtAeBKfPEq9LsNOE98UBJz\r\nPh9v9S7AAC96m90KOz30EIoKQVhgVLJ+AYSe2fBzikgjacmVVOJ9vN4luAVr\r\nxL5svvG6+85Nmr55aY9wV9HVnXejZTdLZ2Z5iXo0CXKvhERr6PCHF9QEZ8Hp\r\n/OPQzItFekxQBA4cM8T8dNiTqAz2iQCd1m04zg9WvHwcFRblEFkXTj2Dgz/Q\r\n78WHapsCcdL1qkYNB6nowTkHNmAZjTFXv5ve0G7lU/h/PhGaOwg477DosaJg\r\n882yfhO1u9uEyCx4yi8jjl1bZt01ZgRaLkOFp874CaTWer15h0nVKngvNksk\r\nMnyUb0brlNuPlMs6qyNI0UpGFWi/IXeE79agfGvh2W697LXb2cNJmAPyGszE\r\nv3aax5xCG/uvtnkOZUgPk8m1mikXpaSYEN1zucVgbE0SNkvRdVnOKQOa4Ldi\r\nGgD+9znAHrxBbBcwO/XadZkNt4pigFQ7WFJSFkUsGglcATt2zr2snpQ2EXHx\r\nIKUs8uNfxwkdMBzSHiBNbsD6VVpynv6OxNe08t/2grNwsoGrlwpq1vAxaxDa\r\nFDXw/9ABkJ7L0fq6PibWQxIdqActL4UuylA=\r\n=y0So\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"types": "./typings/index.d.ts", "import": "./esm.mjs", "require": "./index.js"}, "./esm.mjs": "./esm.mjs"}, "gitHead": "33195f189b1ffa568c232503fb6ac0cf5548eb74", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "9.0.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.25.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^4.9.4", "@types/jest": "^29.2.4", "@types/node": "^18.11.18", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^24.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_10.0.1_1681534915947_0.9583715547325435", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "commander", "version": "11.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@11.0.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "43e19c25dbedc8256203538e8d7e9346877a6f67", "tarball": "https://registry.npmjs.org/commander/-/commander-11.0.0.tgz", "fileCount": 14, "integrity": "sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==", "signatures": [{"sig": "MEUCIQCnL/Y6uyE9c40ZCWmhPrH1cK74I1hKigKXY8Dx7SgMXgIgc1aQbAyRDJ48vZXIOjfoF66Awtb9r0pX40JNLbGShlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175557}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/"]}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "4ef19faac1564743d8c7e3ce89ef8d190e1551b4", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run test-typings", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "test-typings": "tsd", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.0.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.28.1", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^33.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_11.0.0_1686876680336_0.6453178874329064", "host": "s3://npm-registry-packages"}}, "11.1.0": {"name": "commander", "version": "11.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@11.1.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "62fdce76006a68e5c1ab3314dc92e800eb83d906", "tarball": "https://registry.npmjs.org/commander/-/commander-11.1.0.tgz", "fileCount": 14, "integrity": "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==", "signatures": [{"sig": "MEQCIG4OXx0HEJTNxkBjrp8POwBJUFrpuZiWKJAOYJujZTTKAiAuz9dTVRR7iBlcXkZRVNYjCzOHIXwsSjYaSqg6j2KbAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176667}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=16"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "f1ae2db8e2da01d6efcbfd59cbf82202f864b0c1", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run typecheck-ts", "test-all": "npm run test && npm run lint && npm run typecheck-js && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "typecheck-js": "tsc -p tsconfig.js.json", "typecheck-ts": "tsd && tsc -p tsconfig.ts.json", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.4.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.28.1", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^15.6.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^5.47.1", "@typescript-eslint/eslint-plugin": "^5.47.1", "eslint-config-standard-with-typescript": "^33.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_11.1.0_1697156974546_0.4304863535469152", "host": "s3://npm-registry-packages"}}, "12.0.0-0": {"name": "commander", "version": "12.0.0-0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@12.0.0-0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "5b82ae6a6d2433b4035bfc71157f76569840aca7", "tarball": "https://registry.npmjs.org/commander/-/commander-12.0.0-0.tgz", "fileCount": 14, "integrity": "sha512-UsCJj6ASq58nS+xEF4P/eCKYaBC938xOTQMJnTEdUOn0EZ63zHjr4ewwbf++T/w7kdpY57dW2Ff4Jsk2xL+aUg==", "signatures": [{"sig": "MEYCIQDlZ8WXjC+8tqyrZG8O/tB9MfklxGn4GPPq8EBsk2RvQAIhAJ9dQdqxHosxZ3UyAsW6EpS7scDZHzLoC/NmnLj1I2AZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178431}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "18f921d9778ebd1578ad98aba9b873f51786fbe4", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run typecheck-ts", "test-all": "npm run test && npm run lint && npm run typecheck-js && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "typecheck-js": "tsc -p tsconfig.js.json", "typecheck-ts": "tsd && tsc -p tsconfig.ts.json", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^16.2.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^6.7.5", "@typescript-eslint/eslint-plugin": "^6.7.5", "eslint-config-standard-with-typescript": "^39.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_12.0.0-0_1699681366534_0.7397661235491186", "host": "s3://npm-registry-packages"}}, "12.0.0-1": {"name": "commander", "version": "12.0.0-1", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@12.0.0-1", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "8f23171fad23c9238e8e3ec014eb589efdf172c1", "tarball": "https://registry.npmjs.org/commander/-/commander-12.0.0-1.tgz", "fileCount": 14, "integrity": "sha512-43nKW58vMv4OVD6Xm+KIlF9lx9hpbwaA0A3bSz6kpsMzkxBOhkPv/wsrU/ZC4Is5xaKqDKF5CBRcM/h4xkz47Q==", "signatures": [{"sig": "MEUCIQChzU9cD1CyuG74UEtTDRfORwFaNWBvjc1ViVQ8Cf7TmAIgOtM+6h1i1L2MUm5Y9rAk7KdZQwhfGvgZCDL6ZuAAY2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181044}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "90f3c8a4c99bd03e641ab23210fce8a3c989df8a", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run typecheck-ts", "test-all": "npm run test && npm run lint && npm run typecheck-js && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "typecheck-js": "tsc -p tsconfig.js.json", "typecheck-ts": "tsd && tsc -p tsconfig.ts.json", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.29.0", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^16.2.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^6.7.5", "@typescript-eslint/eslint-plugin": "^6.7.5", "eslint-config-standard-with-typescript": "^40.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_12.0.0-1_1705695132845_0.046763212014158784", "host": "s3://npm-registry-packages"}}, "12.0.0": {"name": "commander", "version": "12.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@12.0.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "b929db6df8546080adfd004ab215ed48cf6f2592", "tarball": "https://registry.npmjs.org/commander/-/commander-12.0.0.tgz", "fileCount": 14, "integrity": "sha512-MwVNWlYjDTtOjX5PiD7o5pK0UrFU/OYgcJfjjK4RaHZETNtjJqrZa9Y9ds88+A+f+d5lv+561eZ+yCKoS3gbAA==", "signatures": [{"sig": "MEYCIQCqMcOJ/77v7Uz/pTyod3HlhUtEaNzX7jUj1/9ZJWNKJwIhALSSkM5pUUoYdfAocjnVTK5Sw5PqEZOyUTJ57l1k/c1Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181031}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "83c3f4e391754d2f80b179acc4bccc2d4d0c863d", "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "test": "jest && npm run typecheck-ts", "test-all": "npm run test && npm run lint && npm run typecheck-js && npm run test-esm", "test-esm": "node ./tests/esm-imports-test.mjs", "typecheck-js": "tsc -p tsconfig.js.json", "typecheck-ts": "tsd && tsc -p tsconfig.ts.json", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.8.1", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.30.4", "jest": "^29.3.1", "eslint": "^8.30.0", "ts-jest": "^29.0.3", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint-plugin-n": "^16.2.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-config-standard": "^17.0.0", "@typescript-eslint/parser": "^6.7.5", "@typescript-eslint/eslint-plugin": "^6.7.5", "eslint-config-standard-with-typescript": "^40.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_12.0.0_1706953473772_0.24319181371018384", "host": "s3://npm-registry-packages"}}, "12.1.0": {"name": "commander", "version": "12.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@12.1.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "01423b36f501259fdaac4d0e4d60c96c991585d3", "tarball": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "fileCount": 14, "integrity": "sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==", "signatures": [{"sig": "MEYCIQDGfZX/89afRyWbnvCN6Ti/61zQGiZDewN03eez9Ni/fgIhAMk8A/9F2Tbtcna/SjHl/aJn7Uc68PQxZMzK36OzHsQx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186398}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "970ecae402b253de691e6a9066fea22f38fe7431", "scripts": {"fix": "npm run fix:lint && npm run fix:format", "test": "jest && npm run check:type:ts", "check": "npm run check:type && npm run check:lint && npm run check:format", "fix:lint": "eslint --fix .", "test-all": "jest && npm run test-esm && npm run check", "test-esm": "node ./tests/esm-imports-test.mjs", "check:lint": "eslint .", "check:type": "npm run check:type:js && npm run check:type:ts", "fix:format": "prettier --write .", "check:format": "prettier --check .", "check:type:js": "tsc -p tsconfig.js.json", "check:type:ts": "tsd && tsc -p tsconfig.ts.json"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.8.1", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^8.30.0", "globals": "^13.24.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^8.56.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "typescript-eslint": "^7.0.1", "eslint-plugin-jest": "^28.3.0", "eslint-plugin-jsdoc": "^48.1.0", "prettier-plugin-jsdoc": "^1.3.0", "eslint-config-prettier": "^9.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_12.1.0_1716031013847_0.9774705545309066", "host": "s3://npm-registry-packages"}}, "13.0.0-0": {"name": "commander", "version": "13.0.0-0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@13.0.0-0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "1ec4503b69359ec74ea31d9eff53491f07d9dce6", "tarball": "https://registry.npmjs.org/commander/-/commander-13.0.0-0.tgz", "fileCount": 14, "integrity": "sha512-3CshrHCF8M4mJhtBruqZvYzMoRplFO1NmsIzF6uaIxZpzvW+ZI6xU/fx5LmprLNYB5sH5F2CnjTkXflGfkYgkQ==", "signatures": [{"sig": "MEYCIQCcbokr333aCVfqogUYtd+ft2aycM3Y2/UHH6OFjrDEPwIhAKQ/J0o+wx+WRW/zbjnZ+uJNbXx9NxAD2odxDzn5JBGT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196088}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "37e86af4c14b22db04adfce4b05597567f207167", "scripts": {"fix": "npm run fix:lint && npm run fix:format", "test": "jest && npm run check:type:ts", "check": "npm run check:type && npm run check:lint && npm run check:format", "fix:lint": "eslint --fix .", "test-all": "jest && npm run test-esm && npm run check", "test-esm": "node ./tests/esm-imports-test.mjs", "check:lint": "eslint .", "check:type": "npm run check:type:js && npm run check:type:ts", "fix:format": "prettier --write .", "check:format": "prettier --check .", "check:type:js": "tsc -p tsconfig.js.json", "check:type:ts": "tsd && tsc -p tsconfig.ts.json"}, "support": true, "_npmUser": {"name": "shadowspawn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^8.57.1", "globals": "^15.7.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^9.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_13.0.0-0_1733526752117_0.8032912131001044", "host": "s3://npm-registry-packages"}}, "13.0.0": {"name": "commander", "version": "13.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@13.0.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "1b161f60ee3ceb8074583a0f95359a4f8701845c", "tarball": "https://registry.npmjs.org/commander/-/commander-13.0.0.tgz", "fileCount": 14, "integrity": "sha512-oPYleIY8wmTVzkvQq10AEok6YcTC4sRUBl8F9gVuwchGVUCTbl/vhLTaQqutuuySYOsu8YTgV+OxKc/8Yvx+mQ==", "signatures": [{"sig": "MEQCIAfq9DCLBTnsNln8wX2GrEEQgV2iyqyj5bffmTcjNmYoAiAGuHeloTv2kgjXmKaG7h7qnJGSVg3IDLdhbCdtQ7fowg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199411}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "d6bcb0b6e144d8854360a5c3c2759fadf0e4edb3", "scripts": {"fix": "npm run fix:lint && npm run fix:format", "test": "jest && npm run check:type:ts", "check": "npm run check:type && npm run check:lint && npm run check:format", "fix:lint": "eslint --fix .", "test-all": "jest && npm run test-esm && npm run check", "test-esm": "node ./tests/esm-imports-test.mjs", "check:lint": "eslint .", "check:type": "npm run check:type:js && npm run check:type:ts", "fix:format": "prettier --write .", "check:format": "prettier --check .", "check:type:js": "tsc -p tsconfig.js.json", "check:type:ts": "tsd && tsc -p tsconfig.ts.json"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "20.8.1", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^9.17.0", "globals": "^15.7.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^9.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_13.0.0_1735536399933_0.030418366285915432", "host": "s3://npm-registry-packages-npm-production"}}, "13.1.0": {"name": "commander", "version": "13.1.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@13.1.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "776167db68c78f38dcce1f9b8d7b8b9a488abf46", "tarball": "https://registry.npmjs.org/commander/-/commander-13.1.0.tgz", "fileCount": 14, "integrity": "sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==", "signatures": [{"sig": "MEYCIQDmyGTBaLFQN4/SExNA2Zz8YhU4S4D3LUPYXaJePOktPQIhALa2IBUy9wXlnRmenlSlaWTxtSvKa1gmUTzhiPl6T6F7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200514}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "e6f56c888c96d1339c2b974fee7e6ba4f2e3d218", "scripts": {"fix": "npm run fix:lint && npm run fix:format", "test": "jest && npm run check:type:ts", "check": "npm run check:type && npm run check:lint && npm run check:format", "fix:lint": "eslint --fix .", "test-all": "jest && npm run test-esm && npm run check", "test-esm": "node ./tests/esm-imports-test.mjs", "check:lint": "eslint .", "check:type": "npm run check:type:js && npm run check:type:ts", "fix:format": "prettier --write .", "check:format": "prettier --check .", "check:type:js": "tsc -p tsconfig.js.json", "check:type:ts": "tsd && tsc -p tsconfig.ts.json"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "22.13.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^9.17.0", "globals": "^15.7.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^9.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/commander_13.1.0_1737415749979_0.9261796025805804", "host": "s3://npm-registry-packages-npm-production"}}, "14.0.0": {"name": "commander", "version": "14.0.0", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "commander@14.0.0", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/commander.js#readme", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dist": {"shasum": "f244fc74a92343514e56229f16ef5c5e22ced5e9", "tarball": "https://registry.npmjs.org/commander/-/commander-14.0.0.tgz", "fileCount": 14, "integrity": "sha512-2uM9rYjPvyq39NwLRqaiLtWHyDC1FvryJDa2ATTVims5YAS4PupsEQsDvP14FqhFr0P49CYDugi59xaxJlTXRA==", "signatures": [{"sig": "MEYCIQCOh07iotg66yGE6l7Hlxgj0e/8T7YE7NRh+Ci3rLppowIhAJVbXCIGb8ayS3pHbEmamarp0JGNw15bbJd6kjmoUXvR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 208171}, "main": "./index.js", "type": "commonjs", "types": "typings/index.d.ts", "engines": {"node": ">=20"}, "exports": {".": {"import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js", "require": {"types": "./typings/index.d.ts", "default": "./index.js"}}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "gitHead": "395cf7145fe28122f5a69026b310e02df114f907", "scripts": {"fix": "npm run fix:lint && npm run fix:format", "test": "jest && npm run check:type:ts", "check": "npm run check:type && npm run check:lint && npm run check:format", "fix:lint": "eslint --fix .", "test-all": "jest && npm run test-esm && npm run check", "test-esm": "node ./tests/esm-imports-test.mjs", "check:lint": "eslint .", "check:type": "npm run check:type:js && npm run check:type:ts", "fix:format": "prettier --write .", "check:format": "prettier --check .", "check:type:js": "tsc -p tsconfig.js.json", "check:type:ts": "tsd && tsc -p tsconfig.ts.json"}, "support": true, "_npmUser": {"name": "abetomo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "the complete solution for node.js command-line programs", "directories": {}, "_nodeVersion": "22.13.0", "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.31.0", "jest": "^29.3.1", "eslint": "^9.17.0", "globals": "^16.0.0", "ts-jest": "^29.0.3", "prettier": "^3.2.5", "@eslint/js": "^9.4.0", "typescript": "^5.0.4", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "typescript-eslint": "^8.12.2", "eslint-plugin-jest": "^28.3.0", "eslint-config-prettier": "^10.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/commander_14.0.0_1747548628975_0.4415597476612214", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2011-08-14T22:17:51.639Z", "modified": "2025-05-18T23:00:59.689Z", "0.0.1": "2011-08-14T22:17:52.365Z", "0.0.3": "2011-08-15T15:30:08.289Z", "0.0.4": "2011-08-15T15:45:00.595Z", "0.0.5": "2011-08-19T01:18:17.154Z", "0.1.0": "2011-08-24T11:45:37.962Z", "0.2.0": "2011-09-26T15:44:03.241Z", "0.2.1": "2011-10-24T21:03:27.736Z", "0.3.0": "2011-10-31T15:36:53.375Z", "0.3.1": "2011-10-31T22:07:16.209Z", "0.3.2": "2011-11-01T21:27:35.103Z", "0.3.3": "2011-11-14T19:02:49.976Z", "0.4.0": "2011-11-15T16:05:27.872Z", "0.4.1": "2011-11-18T16:48:21.692Z", "0.4.2": "2011-11-24T15:22:25.202Z", "0.4.3": "2011-12-04T17:54:46.487Z", "0.5.0": "2011-12-04T19:44:41.915Z", "0.5.1": "2011-12-20T16:26:29.300Z", "0.6.0": "2012-04-11T00:52:24.970Z", "0.6.1": "2012-06-01T19:23:51.854Z", "0.5.2": "2012-06-25T21:09:05.996Z", "1.0.0": "2012-07-05T15:39:47.150Z", "1.0.1": "2012-08-03T20:48:33.507Z", "1.0.2": "2012-08-24T20:37:14.775Z", "1.0.3": "2012-08-30T15:07:57.325Z", "1.0.4": "2012-09-03T15:41:28.290Z", "1.0.5": "2012-10-10T15:56:29.273Z", "1.1.0": "2012-11-17T04:59:23.345Z", "1.1.1": "2012-11-20T23:09:50.552Z", "1.2.0": "2013-06-13T13:52:46.805Z", "1.3.0": "2013-07-09T15:15:27.256Z", "1.3.1": "2013-07-18T16:17:47.951Z", "1.3.2": "2013-07-18T16:26:50.160Z", "2.0.0": "2013-07-19T00:55:45.073Z", "2.1.0": "2013-11-21T17:06:23.757Z", "2.2.0": "2014-03-29T12:36:09.643Z", "2.3.0": "2014-07-16T02:14:43.461Z", "2.4.0": "2014-10-17T14:16:05.654Z", "2.5.0": "2014-10-24T07:41:38.958Z", "2.5.1": "2014-12-15T12:57:52.861Z", "2.6.0": "2014-12-29T16:16:14.148Z", "2.7.0": "2015-03-09T15:28:12.220Z", "2.7.1": "2015-03-11T00:46:03.033Z", "2.8.0": "2015-04-14T19:38:49.246Z", "2.8.1": "2015-04-24T20:27:36.920Z", "2.9.0": "2015-10-13T15:26:25.933Z", "2.10.0": "2017-06-23T09:32:55.567Z", "2.11.0": "2017-07-03T10:07:25.387Z", "2.12.0": "2017-11-22T23:04:54.032Z", "2.12.1": "2017-11-23T10:18:50.901Z", "2.12.2": "2017-11-28T07:08:54.938Z", "2.13.0": "2018-01-11T07:09:55.387Z", "2.14.0": "2018-02-06T01:23:46.219Z", "2.14.1": "2018-02-07T07:42:58.790Z", "2.15.0": "2018-03-08T01:11:52.912Z", "2.15.1": "2018-03-20T01:47:22.840Z", "2.16.0": "2018-06-29T09:40:35.648Z", "2.17.0": "2018-08-04T00:09:33.375Z", "2.17.1": "2018-08-07T11:19:30.675Z", "2.18.0": "2018-09-07T10:27:19.913Z", "2.19.0": "2018-10-08T21:51:13.862Z", "2.20.0": "2019-04-03T00:41:29.557Z", "3.0.0-0": "2019-07-27T02:38:12.348Z", "3.0.0": "2019-08-09T00:46:51.482Z", "3.0.1": "2019-08-30T08:55:39.389Z", "3.0.2": "2019-09-26T09:10:22.874Z", "2.20.1": "2019-09-28T22:01:44.284Z", "4.0.0-0": "2019-10-01T08:39:44.602Z", "4.0.0-1": "2019-10-08T01:27:51.795Z", "2.20.2": "2019-10-11T04:01:45.299Z", "2.20.3": "2019-10-11T05:40:24.166Z", "4.0.0": "2019-11-01T10:09:07.258Z", "4.0.1": "2019-11-11T09:18:33.992Z", "4.1.0": "2020-01-06T09:16:52.298Z", "5.0.0-0": "2020-02-01T03:44:40.532Z", "4.1.1": "2020-02-03T06:15:56.861Z", "5.0.0-1": "2020-02-08T20:49:31.242Z", "5.0.0-2": "2020-02-11T09:53:56.733Z", "5.0.0-3": "2020-02-20T07:36:39.505Z", "5.0.0-4": "2020-03-03T05:35:36.394Z", "5.0.0": "2020-03-14T01:10:37.734Z", "5.1.0": "2020-04-25T02:29:21.964Z", "6.0.0-0": "2020-06-20T06:03:44.450Z", "6.0.0": "2020-07-19T14:01:31.919Z", "6.1.0": "2020-08-28T01:22:54.292Z", "6.2.0": "2020-10-25T02:37:56.917Z", "7.0.0-0": "2020-10-25T04:49:14.594Z", "7.0.0-1": "2020-11-21T21:30:28.454Z", "6.2.1": "2020-12-14T00:47:18.186Z", "7.0.0-2": "2020-12-14T07:48:46.624Z", "7.0.0": "2021-01-15T09:47:17.281Z", "7.1.0": "2021-02-15T07:24:42.526Z", "7.2.0": "2021-03-21T21:56:16.053Z", "8.0.0-0": "2021-05-22T23:31:04.447Z", "8.0.0-1": "2021-05-31T07:30:26.816Z", "8.0.0-2": "2021-06-06T04:36:26.462Z", "8.0.0": "2021-06-25T08:24:16.053Z", "8.1.0": "2021-07-27T03:56:22.861Z", "8.2.0": "2021-09-10T07:00:32.866Z", "8.3.0": "2021-10-22T07:02:06.771Z", "9.0.0-0": "2021-12-22T05:45:54.861Z", "9.0.0-1": "2022-01-14T20:33:18.784Z", "9.0.0": "2022-01-29T00:30:40.995Z", "9.1.0": "2022-03-18T05:56:21.581Z", "9.2.0": "2022-04-15T08:17:56.139Z", "9.3.0": "2022-05-28T02:47:06.659Z", "9.4.0": "2022-07-15T06:19:53.286Z", "9.4.1": "2022-09-30T07:28:04.231Z", "9.5.0": "2023-01-07T07:04:45.771Z", "10.0.0": "2023-01-14T01:53:41.421Z", "10.0.1": "2023-04-15T05:01:56.178Z", "11.0.0": "2023-06-16T00:51:20.545Z", "11.1.0": "2023-10-13T00:29:34.905Z", "12.0.0-0": "2023-11-11T05:42:46.741Z", "12.0.0-1": "2024-01-19T20:12:13.073Z", "12.0.0": "2024-02-03T09:44:33.948Z", "12.1.0": "2024-05-18T11:16:54.043Z", "13.0.0-0": "2024-12-06T23:12:32.307Z", "13.0.0": "2024-12-30T05:26:40.123Z", "13.1.0": "2025-01-20T23:29:10.201Z", "14.0.0": "2025-05-18T06:10:29.194Z"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "repository": {"url": "git+https://github.com/tj/commander.js.git", "type": "git"}, "description": "the complete solution for node.js command-line programs", "maintainers": [{"name": "somekittens", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "abetomo", "email": "<EMAIL>"}], "readme": "# Commander.js\n\n[![Build Status](https://github.com/tj/commander.js/workflows/build/badge.svg)](https://github.com/tj/commander.js/actions?query=workflow%3A%22build%22)\n[![NPM Version](http://img.shields.io/npm/v/commander.svg?style=flat)](https://www.npmjs.org/package/commander)\n[![NPM Downloads](https://img.shields.io/npm/dm/commander.svg?style=flat)](https://npmcharts.com/compare/commander?minimal=true)\n[![Install Size](https://packagephobia.now.sh/badge?p=commander)](https://packagephobia.now.sh/result?p=commander)\n\nThe complete solution for [node.js](http://nodejs.org) command-line interfaces.\n\nRead this in other languages: English | [简体中文](./Readme_zh-CN.md)\n\n- [Commander.js](#commanderjs)\n  - [Installation](#installation)\n  - [Quick Start](#quick-start)\n  - [Declaring _program_ variable](#declaring-program-variable)\n  - [Options](#options)\n    - [Common option types, boolean and value](#common-option-types-boolean-and-value)\n    - [Default option value](#default-option-value)\n    - [Other option types, negatable boolean and boolean|value](#other-option-types-negatable-boolean-and-booleanvalue)\n    - [Required option](#required-option)\n    - [Variadic option](#variadic-option)\n    - [Version option](#version-option)\n    - [More configuration](#more-configuration)\n    - [Custom option processing](#custom-option-processing)\n  - [Commands](#commands)\n    - [Command-arguments](#command-arguments)\n      - [More configuration](#more-configuration-1)\n      - [Custom argument processing](#custom-argument-processing)\n    - [Action handler](#action-handler)\n    - [Stand-alone executable (sub)commands](#stand-alone-executable-subcommands)\n    - [Life cycle hooks](#life-cycle-hooks)\n  - [Automated help](#automated-help)\n    - [Custom help](#custom-help)\n    - [Display help after errors](#display-help-after-errors)\n    - [Display help from code](#display-help-from-code)\n    - [.name](#name)\n    - [.usage](#usage)\n    - [.description and .summary](#description-and-summary)\n    - [.helpOption(flags, description)](#helpoptionflags-description)\n    - [.helpCommand()](#helpcommand)\n    - [Help Groups](#help-groups)\n    - [More configuration](#more-configuration-2)\n  - [Custom event listeners](#custom-event-listeners)\n  - [Bits and pieces](#bits-and-pieces)\n    - [.parse() and .parseAsync()](#parse-and-parseasync)\n    - [Parsing Configuration](#parsing-configuration)\n    - [Legacy options as properties](#legacy-options-as-properties)\n    - [TypeScript](#typescript)\n    - [createCommand()](#createcommand)\n    - [Node options such as `--harmony`](#node-options-such-as---harmony)\n    - [Debugging stand-alone executable subcommands](#debugging-stand-alone-executable-subcommands)\n    - [npm run-script](#npm-run-script)\n    - [Display error](#display-error)\n    - [Override exit and output handling](#override-exit-and-output-handling)\n    - [Additional documentation](#additional-documentation)\n  - [Support](#support)\n    - [Commander for enterprise](#commander-for-enterprise)\n\nFor information about terms used in this document see: [terminology](./docs/terminology.md)\n\n## Installation\n\n```sh\nnpm install commander\n```\n\n## Quick Start\n\nYou write code to describe your command line interface.\nCommander looks after parsing the arguments into options and command-arguments,\ndisplays usage errors for problems, and implements a help system.\n\nCommander is strict and displays an error for unrecognised options.\nThe two most used option types are a boolean option, and an option which takes its value from the following argument.\n\nExample file: [split.js](./examples/split.js)\n\n```js\nconst { program } = require('commander');\n\nprogram\n  .option('--first')\n  .option('-s, --separator <char>')\n  .argument('<string>');\n\nprogram.parse();\n\nconst options = program.opts();\nconst limit = options.first ? 1 : undefined;\nconsole.log(program.args[0].split(options.separator, limit));\n```\n\n```console\n$ node split.js -s / --fits a/b/c\nerror: unknown option '--fits'\n(Did you mean --first?)\n$ node split.js -s / --first a/b/c\n[ 'a' ]\n```\n\nHere is a more complete program using a subcommand and with descriptions for the help. In a multi-command program, you have an action handler for each command (or stand-alone executables for the commands).\n\nExample file: [string-util.js](./examples/string-util.js)\n\n```js\nconst { Command } = require('commander');\nconst program = new Command();\n\nprogram\n  .name('string-util')\n  .description('CLI to some JavaScript string utilities')\n  .version('0.8.0');\n\nprogram.command('split')\n  .description('Split a string into substrings and display as an array')\n  .argument('<string>', 'string to split')\n  .option('--first', 'display just the first substring')\n  .option('-s, --separator <char>', 'separator character', ',')\n  .action((str, options) => {\n    const limit = options.first ? 1 : undefined;\n    console.log(str.split(options.separator, limit));\n  });\n\nprogram.parse();\n```\n\n```console\n$ node string-util.js help split\nUsage: string-util split [options] <string>\n\nSplit a string into substrings and display as an array.\n\nArguments:\n  string                  string to split\n\nOptions:\n  --first                 display just the first substring\n  -s, --separator <char>  separator character (default: \",\")\n  -h, --help              display help for command\n\n$ node string-util.js split --separator=/ a/b/c\n[ 'a', 'b', 'c' ]\n```\n\nMore samples can be found in the [examples](https://github.com/tj/commander.js/tree/master/examples) directory.\n\n## Declaring _program_ variable\n\nCommander exports a global object which is convenient for quick programs.\nThis is used in the examples in this README for brevity.\n\n```js\n// CommonJS (.cjs)\nconst { program } = require('commander');\n```\n\nFor larger programs which may use commander in multiple ways, including unit testing, it is better to create a local Command object to use.\n\n```js\n// CommonJS (.cjs)\nconst { Command } = require('commander');\nconst program = new Command();\n```\n\n```js\n// ECMAScript (.mjs)\nimport { Command } from 'commander';\nconst program = new Command();\n```\n\n```ts\n// TypeScript (.ts)\nimport { Command } from 'commander';\nconst program = new Command();\n```\n\n## Options\n\nOptions are defined with the `.option()` method, also serving as documentation for the options. Each option can have a short flag (single character) and a long name, separated by a comma or space or vertical bar ('|'). To allow a wider range of short-ish flags than just\nsingle characters, you may also have two long options. Examples:\n\n```js\nprogram\n  .option('-p, --port <number>', 'server port number')\n  .option('--trace', 'add extra debugging output')\n  .option('--ws, --workspace <name>', 'use a custom workspace')\n```\n\nThe parsed options can be accessed by calling `.opts()` on a `Command` object, and are passed to the action handler.\n\nMulti-word options such as \"--template-engine\" are camel-cased, becoming `program.opts().templateEngine` etc.\n\nAn option and its option-argument can be separated by a space, or combined into the same argument. The option-argument can follow the short option directly or follow an `=` for a long option.\n\n```sh\nserve -p 80\nserve -p80\nserve --port 80\nserve --port=80\n```\n\nYou can use `--` to indicate the end of the options, and any remaining arguments will be used without being interpreted.\n\nBy default, options on the command line are not positional, and can be specified before or after other arguments.\n\nThere are additional related routines for when `.opts()` is not enough:\n\n- `.optsWithGlobals()` returns merged local and global option values\n- `.getOptionValue()` and `.setOptionValue()` work with a single option value\n- `.getOptionValueSource()` and `.setOptionValueWithSource()` include where the option value came from\n\n### Common option types, boolean and value\n\nThe two most used option types are a boolean option, and an option which takes its value\nfrom the following argument (declared with angle brackets like `--expect <value>`). Both are `undefined` unless specified on command line.\n\nExample file: [options-common.js](./examples/options-common.js)\n\n```js\nprogram\n  .option('-d, --debug', 'output extra debugging')\n  .option('-s, --small', 'small pizza size')\n  .option('-p, --pizza-type <type>', 'flavour of pizza');\n\nprogram.parse(process.argv);\n\nconst options = program.opts();\nif (options.debug) console.log(options);\nconsole.log('pizza details:');\nif (options.small) console.log('- small pizza size');\nif (options.pizzaType) console.log(`- ${options.pizzaType}`);\n```\n\n```console\n$ pizza-options -p\nerror: option '-p, --pizza-type <type>' argument missing\n$ pizza-options -d -s -p vegetarian\n{ debug: true, small: true, pizzaType: 'vegetarian' }\npizza details:\n- small pizza size\n- vegetarian\n$ pizza-options --pizza-type=cheese\npizza details:\n- cheese\n```\n\nMultiple boolean short options may be combined following the dash, and may be followed by a single short option taking a value.\nFor example `-d -s -p cheese` may be written as `-ds -p cheese` or even `-dsp cheese`.\n\nOptions with an expected option-argument are greedy and will consume the following argument whatever the value.\nSo `--id -xyz` reads `-xyz` as the option-argument.\n\n`program.parse(arguments)` processes the arguments, leaving any args not consumed by the program options in the `program.args` array. The parameter is optional and defaults to `process.argv`.\n\n### Default option value\n\nYou can specify a default value for an option.\n\nExample file: [options-defaults.js](./examples/options-defaults.js)\n\n```js\nprogram\n  .option('-c, --cheese <type>', 'add the specified type of cheese', 'blue');\n\nprogram.parse();\n\nconsole.log(`cheese: ${program.opts().cheese}`);\n```\n\n```console\n$ pizza-options\ncheese: blue\n$ pizza-options --cheese stilton\ncheese: stilton\n```\n\n### Other option types, negatable boolean and boolean|value\n\nYou can define a boolean option long name with a leading `no-` to set the option value to false when used.\nDefined alone this also makes the option true by default.\n\nIf you define `--foo` first, adding `--no-foo` does not change the default value from what it would\notherwise be.\n\nExample file: [options-negatable.js](./examples/options-negatable.js)\n\n```js\nprogram\n  .option('--no-sauce', 'Remove sauce')\n  .option('--cheese <flavour>', 'cheese flavour', 'mozzarella')\n  .option('--no-cheese', 'plain with no cheese')\n  .parse();\n\nconst options = program.opts();\nconst sauceStr = options.sauce ? 'sauce' : 'no sauce';\nconst cheeseStr = (options.cheese === false) ? 'no cheese' : `${options.cheese} cheese`;\nconsole.log(`You ordered a pizza with ${sauceStr} and ${cheeseStr}`);\n```\n\n```console\n$ pizza-options\nYou ordered a pizza with sauce and mozzarella cheese\n$ pizza-options --sauce\nerror: unknown option '--sauce'\n$ pizza-options --cheese=blue\nYou ordered a pizza with sauce and blue cheese\n$ pizza-options --no-sauce --no-cheese\nYou ordered a pizza with no sauce and no cheese\n```\n\nYou can specify an option which may be used as a boolean option but may optionally take an option-argument\n(declared with square brackets like `--optional [value]`).\n\nExample file: [options-boolean-or-value.js](./examples/options-boolean-or-value.js)\n\n```js\nprogram\n  .option('-c, --cheese [type]', 'Add cheese with optional type');\n\nprogram.parse(process.argv);\n\nconst options = program.opts();\nif (options.cheese === undefined) console.log('no cheese');\nelse if (options.cheese === true) console.log('add cheese');\nelse console.log(`add cheese type ${options.cheese}`);\n```\n\n```console\n$ pizza-options\nno cheese\n$ pizza-options --cheese\nadd cheese\n$ pizza-options --cheese mozzarella\nadd cheese type mozzarella\n```\n\nOptions with an optional option-argument are not greedy and will ignore arguments starting with a dash.\nSo `id` behaves as a boolean option for `--id -ABCD`, but you can use a combined form if needed like `--id=-ABCD`.\nNegative numbers are special and are accepted as an option-argument.\n\nFor information about possible ambiguous cases, see [options taking varying arguments](./docs/options-in-depth.md).\n\n### Required option\n\nYou may specify a required (mandatory) option using `.requiredOption()`. The option must have a value after parsing, usually specified on the command line, or perhaps from a default value (say from environment). The method is otherwise the same as `.option()` in format, taking flags and description, and optional default value or custom processing.\n\nExample file: [options-required.js](./examples/options-required.js)\n\n```js\nprogram\n  .requiredOption('-c, --cheese <type>', 'pizza must have cheese');\n\nprogram.parse();\n```\n\n```console\n$ pizza\nerror: required option '-c, --cheese <type>' not specified\n```\n\n### Variadic option\n\nYou may make an option variadic by appending `...` to the value placeholder when declaring the option. On the command line you\ncan then specify multiple option-arguments, and the parsed option value will be an array. The extra arguments\nare read until the first argument starting with a dash. The special argument `--` stops option processing entirely. If a value\nis specified in the same argument as the option then no further values are read.\n\nExample file: [options-variadic.js](./examples/options-variadic.js)\n\n```js\nprogram\n  .option('-n, --number <numbers...>', 'specify numbers')\n  .option('-l, --letter [letters...]', 'specify letters');\n\nprogram.parse();\n\nconsole.log('Options: ', program.opts());\nconsole.log('Remaining arguments: ', program.args);\n```\n\n```console\n$ collect -n 1 2 3 --letter a b c\nOptions:  { number: [ '1', '2', '3' ], letter: [ 'a', 'b', 'c' ] }\nRemaining arguments:  []\n$ collect --letter=A -n80 operand\nOptions:  { number: [ '80' ], letter: [ 'A' ] }\nRemaining arguments:  [ 'operand' ]\n$ collect --letter -n 1 -n 2 3 -- operand\nOptions:  { number: [ '1', '2', '3' ], letter: true }\nRemaining arguments:  [ 'operand' ]\n```\n\nFor information about possible ambiguous cases, see [options taking varying arguments](./docs/options-in-depth.md).\n\n### Version option\n\nThe optional `version` method adds handling for displaying the command version. The default option flags are `-V` and `--version`, and when present the command prints the version number and exits.\n\n```js\nprogram.version('0.0.1');\n```\n\n```console\n$ ./examples/pizza -V\n0.0.1\n```\n\nYou may change the flags and description by passing additional parameters to the `version` method, using\nthe same syntax for flags as the `option` method.\n\n```js\nprogram.version('0.0.1', '-v, --vers', 'output the current version');\n```\n\n### More configuration\n\nYou can add most options using the `.option()` method, but there are some additional features available\nby constructing an `Option` explicitly for less common cases.\n\nExample files: [options-extra.js](./examples/options-extra.js), [options-env.js](./examples/options-env.js), [options-conflicts.js](./examples/options-conflicts.js), [options-implies.js](./examples/options-implies.js)\n\n```js\nprogram\n  .addOption(new Option('-s, --secret').hideHelp())\n  .addOption(new Option('-t, --timeout <delay>', 'timeout in seconds').default(60, 'one minute'))\n  .addOption(new Option('-d, --drink <size>', 'drink size').choices(['small', 'medium', 'large']))\n  .addOption(new Option('-p, --port <number>', 'port number').env('PORT'))\n  .addOption(new Option('--donate [amount]', 'optional donation in dollars').preset('20').argParser(parseFloat))\n  .addOption(new Option('--disable-server', 'disables the server').conflicts('port'))\n  .addOption(new Option('--free-drink', 'small drink included free ').implies({ drink: 'small' }));\n```\n\n```console\n$ extra --help\nUsage: help [options]\n\nOptions:\n  -t, --timeout <delay>  timeout in seconds (default: one minute)\n  -d, --drink <size>     drink cup size (choices: \"small\", \"medium\", \"large\")\n  -p, --port <number>    port number (env: PORT)\n  --donate [amount]      optional donation in dollars (preset: \"20\")\n  --disable-server       disables the server\n  --free-drink           small drink included free\n  -h, --help             display help for command\n\n$ extra --drink huge\nerror: option '-d, --drink <size>' argument 'huge' is invalid. Allowed choices are small, medium, large.\n\n$ PORT=80 extra --donate --free-drink\nOptions:  { timeout: 60, donate: 20, port: '80', freeDrink: true, drink: 'small' }\n\n$ extra --disable-server --port 8000\nerror: option '--disable-server' cannot be used with option '-p, --port <number>'\n```\n\nSpecify a required (mandatory) option using the `Option` method `.makeOptionMandatory()`. This matches the `Command` method [.requiredOption()](#required-option).\n\n### Custom option processing\n\nYou may specify a function to do custom processing of option-arguments. The callback function receives two parameters,\nthe user specified option-argument and the previous value for the option. It returns the new value for the option.\n\nThis allows you to coerce the option-argument to the desired type, or accumulate values, or do entirely custom processing.\n\nYou can optionally specify the default/starting value for the option after the function parameter.\n\nExample file: [options-custom-processing.js](./examples/options-custom-processing.js)\n\n```js\nfunction myParseInt(value, dummyPrevious) {\n  // parseInt takes a string and a radix\n  const parsedValue = parseInt(value, 10);\n  if (isNaN(parsedValue)) {\n    throw new commander.InvalidArgumentError('Not a number.');\n  }\n  return parsedValue;\n}\n\nfunction increaseVerbosity(dummyValue, previous) {\n  return previous + 1;\n}\n\nfunction collect(value, previous) {\n  return previous.concat([value]);\n}\n\nfunction commaSeparatedList(value, dummyPrevious) {\n  return value.split(',');\n}\n\nprogram\n  .option('-f, --float <number>', 'float argument', parseFloat)\n  .option('-i, --integer <number>', 'integer argument', myParseInt)\n  .option('-v, --verbose', 'verbosity that can be increased', increaseVerbosity, 0)\n  .option('-c, --collect <value>', 'repeatable value', collect, [])\n  .option('-l, --list <items>', 'comma separated list', commaSeparatedList)\n;\n\nprogram.parse();\n\nconst options = program.opts();\nif (options.float !== undefined) console.log(`float: ${options.float}`);\nif (options.integer !== undefined) console.log(`integer: ${options.integer}`);\nif (options.verbose > 0) console.log(`verbosity: ${options.verbose}`);\nif (options.collect.length > 0) console.log(options.collect);\nif (options.list !== undefined) console.log(options.list);\n```\n\n```console\n$ custom -f 1e2\nfloat: 100\n$ custom --integer 2\ninteger: 2\n$ custom -v -v -v\nverbose: 3\n$ custom -c a -c b -c c\n[ 'a', 'b', 'c' ]\n$ custom --list x,y,z\n[ 'x', 'y', 'z' ]\n```\n\n## Commands\n\nYou can specify (sub)commands using `.command()` or `.addCommand()`. There are two ways these can be implemented: using an action handler attached to the command, or as a stand-alone executable file (described in more detail later). The subcommands may be nested ([example](./examples/nestedCommands.js)).\n\nIn the first parameter to `.command()` you specify the command name. You may append the command-arguments after the command name, or specify them separately using `.argument()`. The arguments may be `<required>` or `[optional]`, and the last argument may also be `variadic...`.\n\nYou can use `.addCommand()` to add an already configured subcommand to the program.\n\nFor example:\n\n```js\n// Command implemented using action handler (description is supplied separately to `.command`)\n// Returns new command for configuring.\nprogram\n  .command('clone <source> [destination]')\n  .description('clone a repository into a newly created directory')\n  .action((source, destination) => {\n    console.log('clone command called');\n  });\n\n// Command implemented using stand-alone executable file, indicated by adding description as second parameter to `.command`.\n// Returns `this` for adding more commands.\nprogram\n  .command('start <service>', 'start named service')\n  .command('stop [service]', 'stop named service, or all if no name supplied');\n\n// Command prepared separately.\n// Returns `this` for adding more commands.\nprogram\n  .addCommand(build.makeBuildCommand());\n```\n\nConfiguration options can be passed with the call to `.command()` and `.addCommand()`. Specifying `hidden: true` will\nremove the command from the generated help output. Specifying `isDefault: true` will run the subcommand if no other\nsubcommand is specified ([example](./examples/defaultCommand.js)).\n\nYou can add alternative names for a command with `.alias()`. ([example](./examples/alias.js))\n\n`.command()` automatically copies the inherited settings from the parent command to the newly created subcommand. This is only done during creation, any later setting changes to the parent are not inherited.\n\nFor safety, `.addCommand()` does not automatically copy the inherited settings from the parent command. There is a helper routine `.copyInheritedSettings()` for copying the settings when they are wanted.\n\n### Command-arguments\n\nFor subcommands, you can specify the argument syntax in the call to `.command()` (as shown above). This\nis the only method usable for subcommands implemented using a stand-alone executable, but for other subcommands\nyou can instead use the following method.\n\nTo configure a command, you can use `.argument()` to specify each expected command-argument.\nYou supply the argument name and an optional description. The argument may be `<required>` or `[optional]`.\nYou can specify a default value for an optional command-argument.\n\nExample file: [argument.js](./examples/argument.js)\n\n```js\nprogram\n  .version('0.1.0')\n  .argument('<username>', 'user to login')\n  .argument('[password]', 'password for user, if required', 'no password given')\n  .action((username, password) => {\n    console.log('username:', username);\n    console.log('password:', password);\n  });\n```\n\n The last argument of a command can be variadic, and only the last argument.  To make an argument variadic you\n append `...` to the argument name. A variadic argument is passed to the action handler as an array. For example:\n\n```js\nprogram\n  .version('0.1.0')\n  .command('rmdir')\n  .argument('<dirs...>')\n  .action(function (dirs) {\n    dirs.forEach((dir) => {\n      console.log('rmdir %s', dir);\n    });\n  });\n```\n\nThere is a convenience method to add multiple arguments at once, but without descriptions:\n\n```js\nprogram\n  .arguments('<username> <password>');\n```\n\n#### More configuration\n\nThere are some additional features available by constructing an `Argument` explicitly for less common cases.\n\nExample file: [arguments-extra.js](./examples/arguments-extra.js)\n\n```js\nprogram\n  .addArgument(new commander.Argument('<drink-size>', 'drink cup size').choices(['small', 'medium', 'large']))\n  .addArgument(new commander.Argument('[timeout]', 'timeout in seconds').default(60, 'one minute'))\n```\n\n#### Custom argument processing\n\nYou may specify a function to do custom processing of command-arguments (like for option-arguments).\nThe callback function receives two parameters, the user specified command-argument and the previous value for the argument.\nIt returns the new value for the argument.\n\nThe processed argument values are passed to the action handler, and saved as `.processedArgs`.\n\nYou can optionally specify the default/starting value for the argument after the function parameter.\n\nExample file: [arguments-custom-processing.js](./examples/arguments-custom-processing.js)\n\n```js\nprogram\n  .command('add')\n  .argument('<first>', 'integer argument', myParseInt)\n  .argument('[second]', 'integer argument', myParseInt, 1000)\n  .action((first, second) => {\n    console.log(`${first} + ${second} = ${first + second}`);\n  })\n;\n```\n\n### Action handler\n\nThe action handler gets passed a parameter for each command-argument you declared, and two additional parameters\nwhich are the parsed options and the command object itself.\n\nExample file: [thank.js](./examples/thank.js)\n\n```js\nprogram\n  .argument('<name>')\n  .option('-t, --title <honorific>', 'title to use before name')\n  .option('-d, --debug', 'display some debugging')\n  .action((name, options, command) => {\n    if (options.debug) {\n      console.error('Called %s with options %o', command.name(), options);\n    }\n    const title = options.title ? `${options.title} ` : '';\n    console.log(`Thank-you ${title}${name}`);\n  });\n```\n\nIf you prefer, you can work with the command directly and skip declaring the parameters for the action handler. The `this` keyword is set to the running command and can be used from a function expression (but not from an arrow function).\n\nExample file: [action-this.js](./examples/action-this.js)\n\n```js\nprogram\n  .command('serve')\n  .argument('<script>')\n  .option('-p, --port <number>', 'port number', 80)\n  .action(function() {\n    console.error('Run script %s on port %s', this.args[0], this.opts().port);\n  });\n```\n\nYou may supply an `async` action handler, in which case you call `.parseAsync` rather than `.parse`.\n\n```js\nasync function run() { /* code goes here */ }\n\nasync function main() {\n  program\n    .command('run')\n    .action(run);\n  await program.parseAsync(process.argv);\n}\n```\n\nA command's options and arguments on the command line are validated when the command is used. Any unknown options or missing arguments or excess arguments will be reported as an error. You can suppress the unknown option check with `.allowUnknownOption()`. You can suppress the excess arguments check with `.allowExcessArguments()`.\n\n### Stand-alone executable (sub)commands\n\nWhen `.command()` is invoked with a description argument, this tells Commander that you're going to use stand-alone executables for subcommands.\nCommander will search the files in the directory of the entry script for a file with the name combination `command-subcommand`, like `pm-install` or `pm-search` in the example below. The search includes trying common file extensions, like `.js`.\nYou may specify a custom name (and path) with the `executableFile` configuration option.\nYou may specify a custom search directory for subcommands with `.executableDir()`.\n\nYou handle the options for an executable (sub)command in the executable, and don't declare them at the top-level.\n\nExample file: [pm](./examples/pm)\n\n```js\nprogram\n  .name('pm')\n  .version('0.1.0')\n  .command('install [package-names...]', 'install one or more packages')\n  .command('search [query]', 'search with optional query')\n  .command('update', 'update installed packages', { executableFile: 'myUpdateSubCommand' })\n  .command('list', 'list packages installed', { isDefault: true });\n\nprogram.parse(process.argv);\n```\n\nIf the program is designed to be installed globally, make sure the executables have proper modes, like `755`.\n\n### Life cycle hooks\n\nYou can add callback hooks to a command for life cycle events.\n\nExample file: [hook.js](./examples/hook.js)\n\n```js\nprogram\n  .option('-t, --trace', 'display trace statements for commands')\n  .hook('preAction', (thisCommand, actionCommand) => {\n    if (thisCommand.opts().trace) {\n      console.log(`About to call action handler for subcommand: ${actionCommand.name()}`);\n      console.log('arguments: %O', actionCommand.args);\n      console.log('options: %o', actionCommand.opts());\n    }\n  });\n```\n\nThe callback hook can be `async`, in which case you call `.parseAsync` rather than `.parse`. You can add multiple hooks per event.\n\nThe supported events are:\n\n| event name | when hook called | callback parameters |\n| :-- | :-- | :-- |\n| `preAction`, `postAction` |  before/after action handler for this command and its nested subcommands |   `(thisCommand, actionCommand)` |\n| `preSubcommand` | before parsing direct subcommand  | `(thisCommand, subcommand)` |\n\nFor an overview of the life cycle events see [parsing life cycle and hooks](./docs/parsing-and-hooks.md).\n\n## Automated help\n\nThe help information is auto-generated based on the information commander already knows about your program. The default\nhelp option is `-h,--help`.\n\nExample file: [pizza](./examples/pizza)\n\n```console\n$ node ./examples/pizza --help\nUsage: pizza [options]\n\nAn application for pizza ordering\n\nOptions:\n  -p, --peppers        Add peppers\n  -c, --cheese <type>  Add the specified type of cheese (default: \"marble\")\n  -C, --no-cheese      You do not want any cheese\n  -h, --help           display help for command\n```\n\nA `help` command is added by default if your command has subcommands. It can be used alone, or with a subcommand name to show\nfurther help for the subcommand. These are effectively the same if the `shell` program has implicit help:\n\n```sh\nshell help\nshell --help\n\nshell help spawn\nshell spawn --help\n```\n\nLong descriptions are wrapped to fit the available width. (However, a description that includes a line-break followed by whitespace is assumed to be pre-formatted and not wrapped.)\n\n### Custom help\n\nYou can add extra text to be displayed along with the built-in help.\n\nExample file: [custom-help](./examples/custom-help)\n\n```js\nprogram\n  .option('-f, --foo', 'enable some foo');\n\nprogram.addHelpText('after', `\n\nExample call:\n  $ custom-help --help`);\n```\n\nYields the following help output:\n\n```Text\nUsage: custom-help [options]\n\nOptions:\n  -f, --foo   enable some foo\n  -h, --help  display help for command\n\nExample call:\n  $ custom-help --help\n```\n\nThe positions in order displayed are:\n\n- `beforeAll`: add to the program for a global banner or header\n- `before`: display extra information before built-in help\n- `after`: display extra information after built-in help\n- `afterAll`: add to the program for a global footer (epilog)\n\nThe positions \"beforeAll\" and \"afterAll\" apply to the command and all its subcommands.\n\nThe second parameter can be a string, or a function returning a string. The function is passed a context object for your convenience. The properties are:\n\n- error: a boolean for whether the help is being displayed due to a usage error\n- command: the Command which is displaying the help\n\n### Display help after errors\n\nThe default behaviour for usage errors is to just display a short error message.\nYou can change the behaviour to show the full help or a custom help message after an error.\n\n```js\nprogram.showHelpAfterError();\n// or\nprogram.showHelpAfterError('(add --help for additional information)');\n```\n\n```console\n$ pizza --unknown\nerror: unknown option '--unknown'\n(add --help for additional information)\n```\n\nThe default behaviour is to suggest correct spelling after an error for an unknown command or option. You\ncan disable this.\n\n```js\nprogram.showSuggestionAfterError(false);\n```\n\n```console\n$ pizza --hepl\nerror: unknown option '--hepl'\n(Did you mean --help?)\n```\n\n### Display help from code\n\n`.help()`: display help information and exit immediately. You can optionally pass `{ error: true }` to display on stderr and exit with an error status.\n\n`.outputHelp()`: output help information without exiting. You can optionally pass `{ error: true }` to display on stderr.\n\n`.helpInformation()`: get the built-in command help information as a string for processing or displaying yourself.\n\n### .name\n\nThe command name appears in the help, and is also used for locating stand-alone executable subcommands.\n\nYou may specify the program name using `.name()` or in the Command constructor. For the program, Commander will\nfall back to using the script name from the full arguments passed into `.parse()`. However, the script name varies\ndepending on how your program is launched, so you may wish to specify it explicitly.\n\n```js\nprogram.name('pizza');\nconst pm = new Command('pm');\n```\n\nSubcommands get a name when specified using `.command()`. If you create the subcommand yourself to use with `.addCommand()`,\nthen set the name using `.name()` or in the Command constructor.\n\n### .usage\n\nThis allows you to customise the usage description in the first line of the help. Given:\n\n```js\nprogram\n  .name(\"my-command\")\n  .usage(\"[global options] command\")\n```\n\nThe help will start with:\n\n```Text\nUsage: my-command [global options] command\n```\n\n### .description and .summary\n\nThe description appears in the help for the command. You can optionally supply a shorter\nsummary to use when listed as a subcommand of the program.\n\n```js\nprogram\n  .command(\"duplicate\")\n  .summary(\"make a copy\")\n  .description(`Make a copy of the current project.\nThis may require additional disk space.\n  `);\n```\n\n### .helpOption(flags, description)\n\nBy default, every command has a help option. You may change the default help flags and description. Pass false to disable the built-in help option.\n\n```js\nprogram\n  .helpOption('-e, --HELP', 'read more information');\n```\n\n(Or use `.addHelpOption()` to add an option you construct yourself.)\n\n### .helpCommand()\n\nA help command is added by default if your command has subcommands. You can explicitly turn on or off the implicit help command with `.helpCommand(true)` and `.helpCommand(false)`.\n\nYou can both turn on and customise the help command by supplying the name and description:\n\n```js\nprogram.helpCommand('assist [command]', 'show assistance');\n```\n\n(Or use `.addHelpCommand()` to add a command you construct yourself.)\n\n### Help Groups\n\nThe help by default lists options under the the heading `Options:` and commands under `Commands:`. You can create your own groups\nwith different headings. The high-level way is to set the desired group heading while adding the options and commands,\nusing `.optionsGroup()` and `.commandsGroup()`. The low-level way is using `.helpGroup()` on an individual `Option` or `Command`\n\nExample file: [help-groups.js](./examples/help-groups.js)\n\n### More configuration\n\nThe built-in help is formatted using the Help class.\nYou can configure the help by modifying data properties and methods using `.configureHelp()`, or by subclassing Help using `.createHelp()` .\n\nSimple properties include `sortSubcommands`, `sortOptions`, and `showGlobalOptions`. You can add color using the style methods like `styleTitle()`.\n\nFor more detail and examples of changing the displayed text, color, and layout see (./docs/help-in-depth.md).\n\n## Custom event listeners\n\nYou can execute custom actions by listening to command and option events.\n\n```js\nprogram.on('option:verbose', function () {\n  process.env.VERBOSE = this.opts().verbose;\n});\n```\n\n## Bits and pieces\n\n### .parse() and .parseAsync()\n\nCall with no parameters to parse `process.argv`. Detects Electron and special node options like `node --eval`. Easy mode!\n\nOr call with an array of strings to parse, and optionally where the user arguments start by specifying where the arguments are `from`:\n\n- `'node'`: default, `argv[0]` is the application and `argv[1]` is the script being run, with user arguments after that\n- `'electron'`: `argv[0]` is the application and `argv[1]` varies depending on whether the electron application is packaged\n- `'user'`: just user arguments\n\nFor example:\n\n```js\nprogram.parse(); // parse process.argv and auto-detect electron and special node flags\nprogram.parse(process.argv); // assume argv[0] is app and argv[1] is script\nprogram.parse(['--port', '80'], { from: 'user' }); // just user supplied arguments, nothing special about argv[0]\n```\n\nUse parseAsync instead of parse if any of your action handlers are async.\n\n### Parsing Configuration\n\nIf the default parsing does not suit your needs, there are some behaviours to support other usage patterns.\n\nBy default, program options are recognised before and after subcommands. To only look for program options before subcommands, use `.enablePositionalOptions()`. This lets you use\nan option for a different purpose in subcommands.\n\nExample file: [positional-options.js](./examples/positional-options.js)\n\nWith positional options, the `-b` is a program option in the first line and a subcommand option in the second line:\n\n```sh\nprogram -b subcommand\nprogram subcommand -b\n```\n\nBy default, options are recognised before and after command-arguments. To only process options that come\nbefore the command-arguments, use `.passThroughOptions()`. This lets you pass the arguments and following options through to another program\nwithout needing to use `--` to end the option processing.\nTo use pass through options in a subcommand, the program needs to enable positional options.\n\nExample file: [pass-through-options.js](./examples/pass-through-options.js)\n\nWith pass through options, the `--port=80` is a program option in the first line and passed through as a command-argument in the second line:\n\n```sh\nprogram --port=80 arg\nprogram arg --port=80\n```\n\nBy default, the option processing shows an error for an unknown option. To have an unknown option treated as an ordinary command-argument and continue looking for options, use `.allowUnknownOption()`. This lets you mix known and unknown options.\n\nBy default, the argument processing displays an error for more command-arguments than expected.\nTo suppress the error for excess arguments, use`.allowExcessArguments()`.\n\n### Legacy options as properties\n\nBefore Commander 7, the option values were stored as properties on the command.\nThis was convenient to code, but the downside was possible clashes with\nexisting properties of `Command`. You can revert to the old behaviour to run unmodified legacy code by using `.storeOptionsAsProperties()`.\n\n```js\nprogram\n  .storeOptionsAsProperties()\n  .option('-d, --debug')\n  .action((commandAndOptions) => {\n    if (commandAndOptions.debug) {\n      console.error(`Called ${commandAndOptions.name()}`);\n    }\n  });\n```\n\n### TypeScript\n\nextra-typings: There is an optional project to infer extra type information from the option and argument definitions.\nThis adds strong typing to the options returned by `.opts()` and the parameters to `.action()`.\nSee [commander-js/extra-typings](https://github.com/commander-js/extra-typings) for more.\n\n```\nimport { Command } from '@commander-js/extra-typings';\n```\n\nts-node: If you use `ts-node` and stand-alone executable subcommands written as `.ts` files, you need to call your program through node to get the subcommands called correctly. e.g.\n\n```sh\nnode -r ts-node/register pm.ts\n```\n\n### createCommand()\n\nThis factory function creates a new command. It is exported and may be used instead of using `new`, like:\n\n```js\nconst { createCommand } = require('commander');\nconst program = createCommand();\n```\n\n`createCommand` is also a method of the Command object, and creates a new command rather than a subcommand. This gets used internally\nwhen creating subcommands using `.command()`, and you may override it to\ncustomise the new subcommand (example file [custom-command-class.js](./examples/custom-command-class.js)).\n\n### Node options such as `--harmony`\n\nYou can enable `--harmony` option in two ways:\n\n- Use `#! /usr/bin/env node --harmony` in the subcommands scripts. (Note Windows does not support this pattern.)\n- Use the `--harmony` option when call the command, like `node --harmony examples/pm publish`. The `--harmony` option will be preserved when spawning subcommand process.\n\n### Debugging stand-alone executable subcommands\n\nAn executable subcommand is launched as a separate child process.\n\nIf you are using the node inspector for [debugging](https://nodejs.org/en/docs/guides/debugging-getting-started/) executable subcommands using `node --inspect` et al.,\nthe inspector port is incremented by 1 for the spawned subcommand.\n\nIf you are using VSCode to debug executable subcommands you need to set the `\"autoAttachChildProcesses\": true` flag in your launch.json configuration.\n\n### npm run-script\n\nBy default, when you call your program using run-script, `npm` will parse any options on the command-line and they will not reach your program. Use\n `--` to stop the npm option parsing and pass through all the arguments.\n\n The synopsis for [npm run-script](https://docs.npmjs.com/cli/v9/commands/npm-run-script) explicitly shows the `--` for this reason:\n\n```console\nnpm run-script <command> [-- <args>]\n```\n\n### Display error\n\nThis routine is available to invoke the Commander error handling for your own error conditions. (See also the next section about exit handling.)\n\nAs well as the error message, you can optionally specify the `exitCode` (used with `process.exit`)\nand `code` (used with `CommanderError`).\n\n```js\nprogram.error('Password must be longer than four characters');\nprogram.error('Custom processing has failed', { exitCode: 2, code: 'my.custom.error' });\n```\n\n### Override exit and output handling\n\nBy default, Commander calls `process.exit` when it detects errors, or after displaying the help or version. You can override\nthis behaviour and optionally supply a callback. The default override throws a `CommanderError`.\n\nThe override callback is passed a `CommanderError` with properties `exitCode` number, `code` string, and `message`.\nCommander expects the callback to terminate the normal program flow, and will call `process.exit` if the callback returns.\nThe normal display of error messages or version or help is not affected by the override which is called after the display.\n\n```js\nprogram.exitOverride();\n\ntry {\n  program.parse(process.argv);\n} catch (err) {\n  // custom processing...\n}\n```\n\nBy default, Commander is configured for a command-line application and writes to stdout and stderr.\nYou can modify this behaviour for custom applications. In addition, you can modify the display of error messages.\n\nExample file: [configure-output.js](./examples/configure-output.js)\n\n```js\nfunction errorColor(str) {\n  // Add ANSI escape codes to display text in red.\n  return `\\x1b[31m${str}\\x1b[0m`;\n}\n\nprogram\n  .configureOutput({\n    // Visibly override write routines as example!\n    writeOut: (str) => process.stdout.write(`[OUT] ${str}`),\n    writeErr: (str) => process.stdout.write(`[ERR] ${str}`),\n    // Highlight errors in color.\n    outputError: (str, write) => write(errorColor(str))\n  });\n```\n\n### Additional documentation\n\nThere is more information available about:\n\n- [deprecated](./docs/deprecated.md) features still supported for backwards compatibility\n- [options taking varying arguments](./docs/options-in-depth.md)\n- [parsing life cycle and hooks](./docs/parsing-and-hooks.md)\n\n## Support\n\nThe current version of Commander is fully supported on Long Term Support versions of Node.js, and requires at least v20.\n(For older versions of Node.js, use an older version of Commander.)\n\nThe main forum for free and community support is the project [Issues](https://github.com/tj/commander.js/issues) on GitHub.\n\n### Commander for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of Commander and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-commander?utm_source=npm-commander&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "readmeFilename": "Readme.md", "users": {"326060588": true, "abg": true, "apk": true, "dm7": true, "dny": true, "dvl": true, "gvn": true, "jwv": true, "nex": true, "nyx": true, "pid": true, "rsp": true, "sdt": true, "ubi": true, "vbv": true, "vio": true, "viz": true, "wdk": true, "xch": true, "ymk": true, "aguz": true, "andr": true, "bcoe": true, "binq": true, "ca0v": true, "cedx": true, "cyij": true, "d3ck": true, "dyaa": true, "eijs": true, "fill": true, "foto": true, "gamr": true, "golf": true, "hain": true, "heck": true, "hema": true, "huyz": true, "j3kz": true, "jiku": true, "jits": true, "kari": true, "kobs": true, "kran": true, "mrra": true, "mrxf": true, "n1kk": true, "nilz": true, "nyzm": true, "oldj": true, "owaz": true, "pana": true, "pseu": true, "r4ph": true, "shan": true, "siyb": true, "tztz": true, "usex": true, "vasc": true, "wzbg": true, "xufz": true, "z164": true, "zapo": true, "zhen": true, "zmus": true, "abdul": true, "aiboy": true, "aim97": true, "alien": true, "altus": true, "arefm": true, "azder": true, "bacra": true, "bhill": true, "brend": true, "brpaz": true, "conzi": true, "csk83": true, "cygik": true, "cyyyu": true, "ddffx": true, "dec_f": true, "demod": true, "ealen": true, "eyson": true, "fm-96": true, "gconz": true, "havvy": true, "icris": true, "iisii": true, "ineva": true, "iseif": true, "jacks": true, "jalik": true, "jeach": true, "jimco": true, "jmm23": true, "jream": true, "junos": true, "kremr": true, "laomu": true, "lcdss": true, "light": true, "luiko": true, "m42am": true, "masao": true, "ndxbn": true, "noita": true, "okdyp": true, "panlw": true, "pftom": true, "qrawl": true, "raulb": true, "rioli": true, "rshaw": true, "rurri": true, "sbskl": true, "subso": true, "suddi": true, "tanel": true, "tchey": true, "temsa": true, "thotk": true, "tht13": true, "tobho": true, "travm": true, "xerik": true, "xkema": true, "xrush": true, "yrocq": true, "yuxin": true, "zoxon": true, "0x4c3p": true, "456wyc": true, "ackhub": true, "adammc": true, "adonai": true, "amovah": true, "aolu11": true, "bkarak": true, "buzuli": true, "bvaccc": true, "c_ogoo": true, "cdrnch": true, "chaowi": true, "chrisx": true, "cr8tiv": true, "crwnvr": true, "curcuz": true, "d-band": true, "dankle": true, "dejanr": true, "devgar": true, "dopert": true, "dr2009": true, "drudge": true, "edjroz": true, "egantz": true, "egorgl": true, "emyann": true, "evan2x": true, "fulvaz": true, "gabeio": true, "gindis": true, "gkodes": true, "glebec": true, "godber": true, "gpalli": true, "h0ward": true, "huarse": true, "husayt": true, "itesic": true, "itsakt": true, "iuykza": true, "jimnox": true, "jmwlsn": true, "joanmi": true, "joe223": true, "joliva": true, "jorycn": true, "jsolis": true, "juandc": true, "judlup": true, "karate": true, "karudo": true, "kaycee": true, "kazem1": true, "leesei": true, "legiao": true, "leshik": true, "liu946": true, "m0dred": true, "maxart": true, "micash": true, "mitica": true, "mmatto": true, "monjer": true, "mr1024": true, "mrzmmr": true, "mzheng": true, "nanook": true, "neckro": true, "nescio": true, "nhz.io": true, "nmrony": true, "nusmql": true, "nuwaio": true, "nyakto": true, "oheard": true, "olonam": true, "omegga": true, "orkisz": true, "pandao": true, "penglu": true, "pierre": true, "plitat": true, "potnox": true, "pstoev": true, "qlqllu": true, "rgudey": true, "seanjh": true, "sgiant": true, "shaner": true, "shriek": true, "simnon": true, "simoyw": true, "slints": true, "slypix": true, "someok": true, "suissa": true, "taddei": true, "tedyhy": true, "thewei": true, "tianyk": true, "tomi77": true, "tuscen": true, "ulongx": true, "vmleon": true, "vutran": true, "webpro": true, "wickie": true, "willyb": true, "windyh": true, "wisetc": true, "womjoy": true, "xlaoyu": true, "xsilen": true, "xu_q90": true, "yinfxs": true, "yoking": true, "youmoo": true, "yuch4n": true, "yursha": true, "zdying": true, "ziflex": true, "zwwggg": true, "aalpern": true, "abetomo": true, "abkolan": true, "alectic": true, "alexu84": true, "alnafie": true, "amaynut": true, "anoubis": true, "antanst": true, "anticom": true, "asaupup": true, "askaton": true, "awynter": true, "balwant": true, "barenko": true, "brenolf": true, "bryanfo": true, "cambera": true, "clarsen": true, "clechay": true, "cocopas": true, "decoded": true, "deubaka": true, "dskecse": true, "duwonyi": true, "dyakovk": true, "epdplus": true, "ezodude": true, "fatelei": true, "fdaciuk": true, "flyslow": true, "ftornik": true, "funroll": true, "geekish": true, "geekwen": true, "goodwid": true, "gordonz": true, "gregl83": true, "gztomas": true, "hagb4rd": true, "hartzis": true, "hasnaer": true, "hexcola": true, "hingsir": true, "hitalos": true, "itonyyo": true, "izumisy": true, "janez89": true, "jhbruhn": true, "jnoodle": true, "jnovack": true, "jrvldam": true, "jybleau": true, "kahboom": true, "kakaman": true, "kjprice": true, "klipsil": true, "kontrax": true, "kreding": true, "kxbrand": true, "lacodda": true, "langjun": true, "lemming": true, "level9i": true, "licj316": true, "lightky": true, "liunian": true, "lochlan": true, "mamalat": true, "maxwang": true, "mikepol": true, "moonavw": true, "moonpyk": true, "nadimix": true, "nfrigus": true, "niinzir": true, "nilz3ro": true, "norfish": true, "nwinant": true, "omarjmh": true, "passcod": true, "perrywu": true, "pinkkis": true, "rager_m": true, "rcastro": true, "rkmedia": true, "rogerta": true, "royling": true, "rpgreen": true, "russomi": true, "sahlzen": true, "sam2304": true, "samuelg": true, "saoskia": true, "sdolard": true, "sevcsik": true, "sfgarza": true, "shenbin": true, "sinahwz": true, "sjonnet": true, "skarlso": true, "snghrym": true, "stalker": true, "stephnr": true, "stursby": true, "subchen": true, "timwzou": true, "tophsic": true, "trackds": true, "ungurys": true, "varedis": true, "vrfrnco": true, "vtsvang": true, "walchko": true, "warmhug": true, "y-a-v-a": true, "yanghcc": true, "zonetti": true, "abnerlin": true, "adwayish": true, "ahvonenj": true, "aidenzou": true, "alexchao": true, "alioygur": true, "amazonov": true, "amit7000": true, "anhulife": true, "artjacob": true, "aztlan2k": true, "bagocius": true, "bapinney": true, "bcowgi11": true, "behumble": true, "bigwinds": true, "bouchezb": true, "brugnara": true, "bspellio": true, "buzz-dee": true, "carlos8f": true, "cbarrick": true, "cdokolas": true, "colageno": true, "congcong": true, "crafterm": true, "crissdev": true, "crufener": true, "danday74": true, "daskepon": true, "ddaversa": true, "deerflow": true, "demian85": true, "dexteryy": true, "dgautsch": true, "dolymood": true, "drkibitz": true, "drmercer": true, "dtiziani": true, "dzhou777": true, "edalorzo": true, "edinella": true, "erikvold": true, "esundahl": true, "fabioper": true, "fanjieqi": true, "faraoman": true, "fearcode": true, "gchudnov": true, "gourdboy": true, "grreenzz": true, "gurunate": true, "haraldur": true, "hari1295": true, "hecto932": true, "hellstad": true, "hugovila": true, "hurerera": true, "hyanghai": true, "iamninad": true, "ifeature": true, "j3manoto": true, "jfmercer": true, "jimkropa": true, "jktravis": true, "jmsherry": true, "joeyblue": true, "johniexu": true, "jon_shen": true, "jprempeh": true, "kenlimmj": true, "kevbaker": true, "klombomb": true, "koalaylj": true, "krabello": true, "kruemelo": true, "kwpeters": true, "l0n9h02n": true, "lai32290": true, "leejefon": true, "leodutra": true, "leonzhao": true, "losymear": true, "lousando": true, "makediff": true, "mapharia": true, "marvelsq": true, "mattdimu": true, "maxblock": true, "mhfrantz": true, "mikebake": true, "minazuki": true, "moimikey": true, "mothepro": true, "nagorkin": true, "nalindak": true, "neofreko": true, "nicocube": true, "nketchum": true, "nraibaud": true, "oboochin": true, "oknoorap": true, "paazmaya": true, "paflopes": true, "pddivine": true, "pdilyard": true, "petecemi": true, "plukevdh": true, "pnolasco": true, "powmedia": true, "putaoshu": true, "qddegtya": true, "qingying": true, "rbartoli": true, "rmarques": true, "robermac": true, "rochejul": true, "rokeyzki": true, "saidgeek": true, "santihbc": true, "sanusart": true, "semencov": true, "sgodoshi": true, "shiva127": true, "slessans": true, "slowmove": true, "snaidero": true, "solzimer": true, "stephn_r": true, "stoneren": true, "sumit270": true, "surfacew": true, "tamer1an": true, "tcauduro": true, "tfentonz": true, "thechori": true, "thing772": true, "thomasfr": true, "thorsson": true, "tjorange": true, "tmurngon": true, "tracker1": true, "trolster": true, "vamakoda": true, "vchouhan": true, "wangfeia": true, "watsoncj": true, "wkaifang": true, "xiaobing": true, "xiaochao": true, "xueboren": true, "yashprit": true, "yazgazan": true, "yitzchak": true, "yoku2010": true, "yutwatan": true, "yxz_blue": true, "zalithka": true, "zeusdeux": true, "zhbyak47": true, "zhiyelee": true, "zhouanbo": true, "zuojiang": true, "1two3code": true, "abalandin": true, "abuelwafa": true, "adamkdean": true, "adamrenny": true, "alexc1212": true, "alexjsdev": true, "alimaster": true, "ambdxtrch": true, "ambroseus": true, "aquafadas": true, "aronblake": true, "azaritech": true, "becarchal": true, "behrangsa": true, "benpptung": true, "blunt1337": true, "bobxuyang": true, "bredikhin": true, "bryan.ygf": true, "ccastelli": true, "cedrickim": true, "chriscalo": true, "cilindrox": true, "cmdaniels": true, "cmilhench": true, "codematix": true, "danielxie": true, "davequick": true, "davidrlee": true, "drdanryan": true, "edosrecki": true, "elviopita": true, "ephigenia": true, "evanlucas": true, "fanyegong": true, "fcmxtzsca": true, "fgribreau": true, "firefoxnx": true, "fleischer": true, "flftfqwxf": true, "guumaster": true, "guzgarcia": true, "hehaiyang": true, "hemstreet": true, "hermanj13": true, "hongz1125": true, "huxiaolei": true, "i-erokhin": true, "iceriver2": true, "icirellik": true, "inderdeep": true, "jaredvogt": true, "jesusgoku": true, "jetbug123": true, "jjj201200": true, "joemdavis": true, "johndietz": true, "joshuarli": true, "jtianling": true, "junyuecao": true, "kaizendad": true, "kulakowka": true, "landy2014": true, "largepuma": true, "lazycoder": true, "ldq-first": true, "lichenhao": true, "liujiajia": true, "luckyulin": true, "macmladen": true, "mastayoda": true, "mattfwood": true, "max_devjs": true, "megadrive": true, "mistkafka": true, "mjbeswick": true, "mjurincic": true, "mr-smiley": true, "myjustify": true, "nice_body": true, "ninozhang": true, "octalmage": true, "papasavva": true, "pawelotto": true, "peterteng": true, "phosphoer": true, "phpjsnerd": true, "piixiiees": true, "poliveira": true, "powerplex": true, "prasanthv": true, "qingleili": true, "qqcome110": true, "ragex1337": true, "ramzesucr": true, "rbecheras": true, "ronniesan": true, "rosterloh": true, "rubiadias": true, "rylan_yan": true, "sasquatch": true, "schiznitz": true, "sean9999a": true, "sergiodxa": true, "sfabrizio": true, "shakakira": true, "shikloshi": true, "shushanfx": true, "sierisimo": true, "sjonnet19": true, "spekkionu": true, "spielberg": true, "sqrtthree": true, "steel1990": true, "sternelee": true, "stone-jin": true, "subfuzion": true, "sunkeyhub": true, "sunkeysun": true, "superobin": true, "sushiifox": true, "swift2728": true, "tampham47": true, "thepanuto": true, "thomas-so": true, "thomask33": true, "tstringer": true, "visual.io": true, "wolfram77": true, "xtonousou": true, "xunnamius": true, "yang.shao": true, "yanrivera": true, "yousoff92": true, "yvanscher": true, "yyscamper": true, "abdihaikal": true, "adamdscott": true, "adamhalasz": true, "ahmetertem": true, "aitorllj93": true, "akinakalin": true, "alexdreptu": true, "alexmercer": true, "angrykoala": true, "apache2046": true, "aquiandres": true, "avivharuzi": true, "axelrindle": true, "bdudelsack": true, "bengarrett": true, "berkmann18": true, "blacharnia": true, "blakedietz": true, "blind__man": true, "borga.hugo": true, "bradcozine": true, "brandons42": true, "broadway69": true, "byossarian": true, "cestrensem": true, "chinaqstar": true, "clarenceho": true, "codelegant": true, "colleowino": true, "dannydulai": true, "darikspark": true, "davidchase": true, "dozierjack": true, "drhoffmann": true, "dwayneford": true, "echaouchna": true, "eruditecat": true, "f124275809": true, "felixpitau": true, "flovilmart": true, "fmoliveira": true, "franksansc": true, "giussa_dan": true, "goodseller": true, "hoibatpham": true, "iainhallam": true, "iancampelo": true, "igneus.lau": true, "ilyinilyas": true, "incendiary": true, "indigo0086": true, "instazapas": true, "ivanempire": true, "jackishere": true, "jakedetels": true, "jiang-xuan": true, "jkrusinski": true, "jmoser-amr": true, "joelwallis": true, "johnnyeven": true, "jokesterfr": true, "jpfilevich": true, "jrockowitz": true, "junipertcy": true, "junjiansyu": true, "kennethjor": true, "kevin-wynn": true, "leizongmin": true, "leonardorb": true, "lewisbrown": true, "lightning7": true, "lijinghust": true, "lius971125": true, "liushoukai": true, "liuweifeng": true, "luffy84217": true, "manikantag": true, "maniktyagi": true, "marco.jahn": true, "maxmaximov": true, "mjaczynski": true, "mjwilliams": true, "mutantspew": true, "myorkgitis": true, "myterminal": true, "nerdybeast": true, "nickleefly": true, "ocd_lionel": true, "piecioshka": true, "pillar0514": true, "pmbenjamin": true, "pragmadash": true, "princetoad": true, "qqqppp9998": true, "quocnguyen": true, "ricardweii": true, "rocket0191": true, "ron.eisele": true, "samhou1988": true, "schroeterm": true, "sean-oneal": true, "shipengyan": true, "shuoshubao": true, "stormcrows": true, "ta2edchimp": true, "tangweikun": true, "techmexdev": true, "temoto-kun": true, "thisjustin": true, "tunderdomb": true, "vapeadores": true, "wearevilla": true, "wuyixiang9": true, "xieranmaya": true, "zackharley": true, "zousandian": true, "acollins-ts": true, "aereobarato": true, "ahmed-dinar": true, "ahsanshafiq": true, "anlijudavid": true, "arnoldstoba": true, "blackoperat": true, "canercandan": true, "carloselias": true, "cedrickchee": true, "chrisakakay": true, "codeprowong": true, "coolhanddev": true, "cranndarach": true, "csoellinger": true, "darryl.west": true, "drew.brokke": true, "easimonenko": true, "enricostara": true, "eserozvataf": true, "evert-arias": true, "fahadjadoon": true, "fengmiaosen": true, "flumpus-dev": true, "frankwinter": true, "galenandrew": true, "guyfedwards": true, "hal9zillion": true, "heyimeugene": true, "iandstanley": true, "jakeklassen": true, "jamesbedont": true, "jasonlotito": true, "jeffreylowy": true, "jonatasnona": true, "joshuaestes": true, "kevinbird61": true, "kieranpotts": true, "kobleistvan": true, "kodekracker": true, "liqiang0335": true, "marcfiedler": true, "marlongrape": true, "mateuszkocz": true, "maxkoryukov": true, "memoramirez": true, "micaelsouza": true, "morishitter": true, "mrmartineau": true, "mseminatore": true, "neaker15668": true, "polarpython": true, "ragingsmurf": true, "raisiqueira": true, "riaanpelser": true, "russleyshaw": true, "saintedlama": true, "sessionbean": true, "soenkekluth": true, "speedazerty": true, "stanleyhlng": true, "stonecypher": true, "strathausen": true, "techmatt101": true, "thangakumar": true, "themadjoker": true, "tonerbarato": true, "tylerdurham": true, "wangnan0610": true, "xinwangwang": true, "yujiikebata": true, "adriantanasa": true, "alex-the-dev": true, "alfonsovinti": true, "alireza29675": true, "andrewtimney": true, "austinkeeley": true, "battlemidget": true, "blakeredwolf": true, "brentlintner": true, "brentonhouse": true, "creativeadea": true, "danielknaust": true, "darrentorpey": true, "davidbwaters": true, "demiurgosoft": true, "dineshbogolu": true, "dmitry-zaets": true, "doctoruseful": true, "dpjayasekara": true, "dryliketoast": true, "duartemendes": true, "fanchangyong": true, "felixfischer": true, "francisbrito": true, "geoffdavis92": true, "ghostcode521": true, "goatandsheep": true, "goblindegook": true, "hugojosefson": true, "ifahrentholz": true, "imlinhanchao": true, "infernocloud": true, "ivangaravito": true, "jabedhasan21": true, "jakedemonaco": true, "joris.gillis": true, "josiasmontag": true, "justintormey": true, "kevin-foster": true, "khaihoangdev": true, "kiandrajayne": true, "kostya.fokin": true, "letecitanjir": true, "malcolmdiggs": true, "natterstefan": true, "nickeltobias": true, "nickytonline": true, "npm-packages": true, "oussoulessou": true, "pandachaotic": true, "piotrmroczek": true, "rafegoldberg": true, "rethinkflash": true, "sfpharmaplus": true, "spacegeek224": true, "taylorpzreal": true, "thegreatrazz": true, "thomasghenry": true, "tobiasnickel": true, "wallenberg12": true, "wangrongding": true, "williamsando": true, "yourhoneysky": true, "zhangjiayang": true, "adammessinger": true, "antonio-gomez": true, "chinawolf_wyp": true, "chrisdevwords": true, "codyschindler": true, "curioussavage": true, "dallinrparker": true, "edouard-lopez": true, "edwin_estrada": true, "emilien.jegou": true, "gabrielfalcao": true, "gamersdelight": true, "geekforbrains": true, "gillchristian": true, "goodacre.liam": true, "hibrahimsafak": true, "ibrahimtaguri": true, "jian263994241": true, "lucaskatayama": true, "manojkhannakm": true, "markthethomas": true, "mdedirudianto": true, "mikewarcholik": true, "nonemoticoner": true, "pablo.tavarez": true, "patrickkraaij": true, "pauljmartinez": true, "phil.a.taylor": true, "pixelpicosean": true, "ral.amgstromg": true, "ricardoserrao": true, "ripples.alive": true, "sakthiifnotec": true, "spanishtights": true, "stone_breaker": true, "thomasmeadows": true, "tienthanh8490": true, "vergissberlin": true, "arnold-almeida": true, "blackflagcross": true, "chrishonniball": true, "eliasargandara": true, "fabienfoerster": true, "fabiomartins87": true, "greenbud-seeds": true, "imaginegenesis": true, "jakub.knejzlik": true, "john-goldsmith": true, "jonniespratley": true, "lefthandhacker": true, "leviwheatcroft": true, "maycon_ribeiro": true, "patrickpietens": true, "popeindustries": true, "shanewholloway": true, "spencermathews": true, "suryasaripalli": true, "tomas-sereikis": true, "usingthesystem": true, "xiaoqiang.yang": true, "arcticicestudio": true, "eugene.tsukanov": true, "federico-garcia": true, "gestoria-madrid": true, "hairandbeardguy": true, "icodeforcookies": true, "joaquin.briceno": true, "lbrentcarpenter": true, "pensierinmusica": true, "phila_dangerous": true, "sametsisartenep": true, "yellowblackbird": true, "bursalia-gestion": true, "codekraft-studio": true, "gresite_piscinas": true, "horrorandtropics": true, "michaeljwilliams": true, "netoperatorwibby": true, "theoryofnekomata": true, "alquilerargentina": true, "nguyenmanhdat2903": true, "ognjen.jevremovic": true, "scott.m.sarsfield": true, "vision_tecnologica": true, "azulejosmetrosubway": true, "fabian.moron.zirfas": true, "granhermandadblanca": true, "recursion_excursion": true, "daniel-lewis-bsc-hons": true}}