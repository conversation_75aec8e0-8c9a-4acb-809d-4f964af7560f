{"_id": "regjsgen", "_rev": "24-201278a5f844593f9247af80f5ed33e6", "name": "regjsgen", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "dist-tags": {"latest": "0.8.0"}, "versions": {"0.1.0": {"name": "regjsgen", "description": "Generating JavaScript RegExps in JavaScript", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, "license": "MIT", "main": "./regjsgen", "files": ["LICENSE.txt", "regjsgen.js", "README.md"], "homepage": "https://github.com/d10/regjsgen", "repository": {"type": "git", "url": "**************:d10/regjsgen.git"}, "dependencies": {"jsesc": "~0.4.3"}, "scripts": {"test": "node test/index.js"}, "devDependencies": {"got": "~0.3.0"}, "bugs": {"url": "https://github.com/d10/regjsgen/issues"}, "_id": "regjsgen@0.1.0", "dist": {"shasum": "66188738ee46af809877f9a58f7f35bf575e3c7f", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.1.0.tgz", "integrity": "sha512-fcibUgT5scLs0WCrWYKI1Ja5zPfRlj5u98S9nOksVrJ9mgBYV3tuJS3dYFGxjUijmNw84Mink1ppq6wwOOh5ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7kdXf9rSeCmZnSy6PWy2t81l9qNGWDReVeG5aY4AjHAIhAIECkDrD3gmgCNlH+ZwJhASpdy/7BxN0VsWu+idBcztU"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "regjsgen", "description": "Generating JavaScript RegExps in JavaScript", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, "license": "MIT", "main": "./regjsgen", "files": ["LICENSE.txt", "regjsgen.js", "README.md"], "homepage": "https://github.com/d10/regjsgen", "repository": {"type": "git", "url": "**************:d10/regjsgen.git"}, "dependencies": {"jsesc": "~0.4.3"}, "scripts": {"test": "node test/index.js"}, "devDependencies": {"got": "~0.3.0"}, "bugs": {"url": "https://github.com/d10/regjsgen/issues"}, "_id": "regjsgen@0.1.1", "dist": {"shasum": "f44c338e7fe848487d484a3671b3162630902053", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.1.1.tgz", "integrity": "sha512-wNtfFeNQ2Pj13y450RDnmQPNTAT3Ru/Mf8jEEWDZUP6AI/MsyfrvncrT33UQbeS0IanIhWBeRFHjXZvYR9EwIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2TvHNQ96O1TKQaCeFgYiuXgSe/MN+HGJHjKNGD7TWSAIhANKQ9KRG4HVOrDqBkVvdciNPBNEy8i1HLV4gRd5tOzJq"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "regjsgen", "description": "Generating JavaScript RegExps in JavaScript", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, "license": "MIT", "main": "./regjsgen", "files": ["LICENSE.txt", "regjsgen.js", "README.md"], "homepage": "https://github.com/d10/regjsgen", "repository": {"type": "git", "url": "**************:d10/regjsgen.git"}, "scripts": {"test": "node test/index.js"}, "devDependencies": {"got": "~1.2.0", "jsesc": "~0.5.0"}, "bugs": {"url": "https://github.com/d10/regjsgen/issues"}, "_id": "regjsgen@0.1.2", "dist": {"shasum": "781a415384aaf584a863f93df4912394f6387d35", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.1.2.tgz", "integrity": "sha512-l+hxMy3UD7UU2mZKLqcJq3PcVU8j+MCtpDioRO3zmo+Oi37ldaaTkS/ncsAaRK8XpA3yRNUe0xHrRrlgiSQ5Yw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIBCup1ziyG3y4PCVeU/+Amu/08MtxTIcbFise4seIF3yAh9ZTfLsoxR62gaklGLT7P8PBtuER+q8erzaOzzTt2En"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "regjsgen", "version": "0.2.0", "description": "Generate `RegExp`s from RegJSParser’s AST", "homepage": "https://github.com/d10/regjsgen", "license": "MIT", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git://github.com/d10/regjsgen"}, "scripts": {"test": "node test/test.js"}, "files": ["LICENSE.txt", "regjsgen.js", "README.md"], "devDependencies": {"got": "~1.2.0", "jsesc": "~0.5.0"}, "bugs": {"url": "https://github.com/d10/regjsgen/issues"}, "_id": "regjsgen@0.2.0", "dist": {"shasum": "6c016adeac554f75823fe37ac05b92d5a4edb1f7", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz", "integrity": "sha512-x+Y3yA24uF68m5GA+tBjbGYo64xXVJpbToBaWCoSNSc1hdk6dfctaRWrNFTVJZIIhL5GxW8zwjoixbnifnK59g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGv4is8zqJwW0bXENRZyRXQRG+/4tkWlLNRiyuAUy7uJAiEAtBRuA/3YqrWb7X47dPWKbO58AF9sfXqnYB+m+kcpV2Y="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "regjsgen", "version": "0.3.0", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/demoneaux/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://demoneaux.github.io/"}, "repository": {"type": "git", "url": "git+https://github.com/demoneaux/regjsgen.git"}, "bugs": {"url": "https://github.com/demoneaux/regjsgen/issues"}, "files": ["LICENSE", "regjsgen.js"], "scripts": {"test": "node tests/tests.js", "coverage": "istanbul cover --report html tests/tests.js", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"coveralls": "^2.11.8", "istanbul": "~0.4.2", "regjsparser": "~0.2.0", "request": "^2.69.0"}, "gitHead": "3ca9009ef07ca8cf5a4f104be87b68a4a31d872b", "_id": "regjsgen@0.3.0", "_shasum": "0ee4a3e9276430cda25f1e789ea6c15b87b0cb43", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "dist": {"shasum": "0ee4a3e9276430cda25f1e789ea6c15b87b0cb43", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.3.0.tgz", "integrity": "sha512-HVnDMGSR2bwG7BQmTeyiarhsyGyfjO394lZU0T3j7nNEyqCW4SbeyW59ekcUghfruMa3vSOhFcBJ2V2OTRMotQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC2ECoPlXuABHwkGNB/pDJGPG30aNR2x2XwlWCXfzDr3AiEAqF6EbNAKTWC+1twu3f5uUZ/vb0EQYjNRxyoAdHC+qR0="}]}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regjsgen-0.3.0.tgz_1464773921251_0.36796207097359"}, "directories": {}}, "0.4.0": {"name": "regjsgen", "version": "0.4.0", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://bnjmnt4n.now.sh/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "files": ["LICENSE", "regjsgen.js"], "scripts": {"test": "node tests/tests.js", "coverage": "istanbul cover --report html tests/tests.js", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.0.0", "istanbul": "~0.4.5", "regjsparser": "~0.3.0", "request": "^2.83.0"}, "gitHead": "42bc3cd545a2fa25cf226324773f232325da824b", "_id": "regjsgen@0.4.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-X51Lte1gCYUdlwhF28+2YMO0U6WeN0GLpgpA7LK7mbdDnkQYiwvEpmpe0F/cv5L14EbxgrdayAG3JETBv0dbXA==", "shasum": "c1eb4c89a209263f8717c782591523913ede2561", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.4.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPGoxVe8KPHBnZNpVzt368XXd4ym1p+CjlZ6M8BdRtOAiBfzzQQCJXNFxf9kgtyLRQcWwPLyywfzRFBYOqfW1I+YA=="}]}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen-0.4.0.tgz_1516192464288_0.0024640210904181004"}, "directories": {}}, "0.5.0": {"name": "regjsgen", "version": "0.5.0", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://bnjmnt4n.now.sh/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "istanbul cover --report html tests/tests.js", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.1.0", "istanbul": "~0.4.5", "regjsparser": "~0.4.0", "request": "^2.88.0"}, "gitHead": "3e898bdd4577e5f6fc9ffc7c867bbe8661b28344", "_id": "regjsgen@0.5.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RnIrLhrXCX5ow/E5/Mh2O4e/oa1/jW0eaBKTSy3LaCj+M3Bqvm97GWDp2yUtzIs4LEn65zR2yiYGFqb2ApnzDA==", "shasum": "a7634dc08f89209c2049adda3525711fb97265dd", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.0.tgz", "fileCount": 4, "unpackedSize": 13630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb09BzCRA9TVsSAnZWagAAHL8P/2+qPfMU6ChUdXtesJcS\nb4J4OtV52K6iYdJky/TSI2udBPTtzyJ3JXIr5Q2RnBj3wIkDuhFGesCoZ7Ap\noUEm8jYnQXTV1PBt3Q1nTeUww0Wcj7/0T6PHVA1IuKHxt56yP5/h+C9eTbK+\nFabsdn86iXKZnUNggtiVAjE0JarqVfKZC9rpvrTdTFn9hx+rr02RPtXHHb1J\nHipRJsJ6zsLRyZ1ysIB3VZYrIya2SH1FjiddvVbLbB7QDnnlYKuzC7n4p4uB\nVcTWQMIBg3fkqjYIDvx765FABBqxaMxYdjiUsQK/mLz+uilKukbHWhKuGsAb\n/F47dJAUKxs6yXreiQsLNNhAUcWQbE7cDmNKfTI9sMSjtctogTqYWgJb0PVi\na0CbWNclNF7mFwxTsanjCFypZZSMgpTsxuJDf4UrjZvfsBYfqtH7HhpIVsZq\nBFBev4QpQkaK8SOm1R8QCFy1fiMqlEREU7goErBO6kQvQC9DiEq7h9pRwVnV\nhe2pr1OyMcsSkADI4EjvwrUgoirbZzpZ/5ir2LUyQ+QodIgPc6DhDEbldlI1\nmr0lFWHkcmFbo4TPcz/sDUS4SSVvrxiQ2J/XuH9s61YJltnwWhFFauRUdI22\nalBB6AE1zPw2kP580z7JLsEWyMPYELCKmcpXSRdq58NCMZXvrzbqjN/+6dBS\n1XD3\r\n=h56Q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID3aUd8KnfVBpipc4W9TsDlyM4sVR7yAE4WOWNnpnJKWAiAZvEN/f1h04NfUMj/zxfatEuZdFc9Mjr9A9EL/4rZv+A=="}]}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.5.0_1540608114803_0.0388782175432143"}, "_hasShrinkwrap": false}, "0.5.1": {"name": "regjsgen", "version": "0.5.1", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://bnjmnt4n.now.sh/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "nyc --reporter=html npm test", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.6.1", "nyc": "^14.1.1", "regjsparser": "~0.6.0", "request": "^2.88.0"}, "gitHead": "32eb43d493f81420feb1a7c47c1198d337b6ffdb", "_id": "regjsgen@0.5.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5qxzGZjDs9w4tzT3TPhCJqWdCc3RLYwy9J2NB0nm5Lz+S273lvWcpjaTGHsT1dc6Hhfq41uSEOw8wBmxrKOuyg==", "shasum": "48f0bf1a5ea205196929c0d9798b42d1ed98443c", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.1.tgz", "fileCount": 4, "unpackedSize": 13772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrBt0CRA9TVsSAnZWagAAIo4QAIAo3M/x97PN4Fq264bl\nzhpJo84sdtmMt6xe/7EQgD2Bgq+rSeDpylC2jX6OEbBbtZ54PTqo75N6Z4FO\n/3oxAt5UUjcvNpGGAqeIdTEfBINKjlYc/llHsyLcENnPBTPLyZHDPMBI2fNU\nFrprw4ABg532oJU28oMSBX/qnzY12ErcP2IhCQksTzpXA03kYBDmgZvfEgCf\nciTrTm3OWb+lxyrdgyrNrzV76tGaOse893Gg0CskcHW0lIuh7+CKozSQpsd8\nZN9fe5XqoQcoawlX69dcDZn19VnaJtbJEprmGvti7HATa3uhe36+FmMyLERG\nFBL17lAaFFtqhsfp2wJUlz42fKi0uHTzzOI4IBRvZ2lKwGHfF8oduxHI0Jhq\nZiQqKcl6cMRgBPydkgFauidwBR0qTgjBwFu6YmtSYzzDjhfS8j8ljEGgClEr\nevTVlVAjJTjYot01LMOJ+CwyscgIGoDkTFTJiqr0Lu8WR/U80yn3r5uiOBl4\nkoKWtI8jsJH2uDO1vhZnQaOxkhZHBbauyW+lQcEJW4Q/C2dV2VxdEIV6Yo1j\numO5by9Ot6c26LvadJWL+7DrzTgqUoliMS36c2bVzHFCrtgDvR0JtQI8Jawi\nwPFg6K1z5MhJcthFPo9OXBaRUFawPE7qWAWtiNS1Sc+EslALOgzM+fMxEjOC\nQu6t\r\n=/o5O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE7vtP0dVTBKsI2lkmSzQdmBGKio78z31FdBIbmg3oNBAiEA3aO0O+WxI02/eBKImG7cv1Syk1pJszbEHK8jbFaKn/Y="}]}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.5.1_1571560308140_0.011090244139019267"}, "_hasShrinkwrap": false}, "0.5.2": {"name": "regjsgen", "version": "0.5.2", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "nyc --reporter=html npm test", "report-coverage": "nyc --reporter=lcov npm test && codecov", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.7.0", "nyc": "^15.0.1", "regjsparser": "~0.6.4", "request": "^2.88.2"}, "gitHead": "813c5d1f28c1fa4be3abcafaaf3240d28b8145f6", "_id": "regjsgen@0.5.2", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "_npmUser": {"name": "d10", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OFFT3MfrH90xIW8OOSyUrk6QHD5E9JOTeGodiJeBS3J6IwlgzJMNE/1bZklWz5oTg+9dCMyEetclvCVXOPoN3A==", "shasum": "92ff295fb1deecbf6ecdab2543d207e91aa33733", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.2.tgz", "fileCount": 4, "unpackedSize": 14331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeypJ2CRA9TVsSAnZWagAApiEP/0WXrHtSreMvZ+fnIcqz\nnvvBpQEr9Bxi5RTCDkO7Fj8e/EeY3uQ1+CXCV9iZTr4kyGAG2FY3M8O++fhE\n9F48hPN7zUsjFwcJ1tca6Dyqw+EP5jIZMwUXfXx4RhKhti64h9v30aRyJ0bf\nbZBQnomlODTtpjJujfngmo+ONp/zyQ6zePkcgEZZ3e6zMB/ufGuRV7sNRTwy\nDFjFJlr4WfoAKCZSTMGtZ219iKOkJtuHpaym8qQyD+xbhtbuOK5QPWp0FucW\nIWTCgxo3WEN6eQdWx5J7cb0MvZEjn4tdtGSLNcmDnnukZSYuyIp98n0R9b6X\nyihGhdLr8MtfC5q0+u/GG7/jtMw1I6dpAIg1U/bMapeZpBPWE67/NfocTIk/\nRvZ6vheZga7oC5WWqRnlDlCDt6s/NqXf07y7H3gLOFEQ+KkaC2Qp5ZkZljgZ\nJFXTJ7qENv/FfwmA3fdQNcl1IIDxmQdTWKjlgTriqi8YbF0Q3o8nnGCwGkSO\ngKdEBcnTMT4IOhBSyVUQpc2AgX5T3baKvEFGEiE1MIzvqijuWDOyf6rCZhbi\n7zAXWHsw/stFPKVRIpm6ILUp2Raw+qGqUrcZMFtzvvQ0ZstQgBsoZ+NaBK2I\nAtvWu6+X04imrU4rOvYjB2yJQxjTurPnjqwqDxZ2k1Pkih/RiN1dhd3gTuFv\nidcL\r\n=XH+a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHOk27j36UbSUuucBoxSGVYRnxFwzYIHL+5brSXEne77AiEA0e1NKbQ6P/a64yXzbhPsF1HztNur5BuMu974WWA+Vn8="}]}, "maintainers": [{"name": "d10", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.5.2_1590334070278_0.09375785063276543"}, "_hasShrinkwrap": false}, "0.6.0": {"name": "regjsgen", "version": "0.6.0", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "nyc --reporter=html npm test", "report-coverage": "nyc --reporter=lcov npm test && codecov", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "regjsparser": "^0.7.0", "request": "^2.88.2"}, "gitHead": "f671ce7461dde54583965041c6571ecd57ed614e", "_id": "regjsgen@0.6.0", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-ozE883Uigtqj3bx7OhL1KNbCzGyW2NQZPl6Hs09WTvCuZD5sTI4JY58bkbQWa/Y9hxIsvJ3M8Nbf7j54IqeZbA==", "shasum": "83414c5354afd7d6627b16af5f10f41c4e71808d", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.6.0.tgz", "fileCount": 4, "unpackedSize": 14738, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAPnMzxQOZvF/BnuVWYT94ca5y2VqdqvVdHfBSkOvR6wAiEA290/Z9TV3m6tPYKtXRGDW4aO+IfvX9u/GhUwYuiIOQE="}]}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bnjmnt4n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.6.0_1633097413945_0.6082941976483329"}, "_hasShrinkwrap": false}, "0.7.0": {"name": "regjsgen", "version": "0.7.0", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "nyc --reporter=html npm test", "report-coverage": "nyc --reporter=lcov npm test && codecov", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "regjsparser": "^0.8.2", "request": "^2.88.2"}, "gitHead": "561c458c803447f696395867ce09c72737c0cde0", "_id": "regjsgen@0.7.0", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-r0PK6tCRfnlily5ykKG81XTikeuob0nREE8Ds1YJrwiTDXlBbVIcXQzL23X0Qh0Glhoqq/W6P7JBWkwGyOSrxw==", "shasum": "184ac358f604cd6337c57cf51e5a8cfb0720cc5c", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.7.0.tgz", "fileCount": 4, "unpackedSize": 14741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxXhKCRA9TVsSAnZWagAAY7UP/3mQxmtTovKJOH7tYFSq\nxo2zLA2vwpNhJi9awyqVqwLB+XlupIfJklD7j+MfuNCEKIvlYI9YaZfYU0F3\n6zHcRqbeixlI4PE4SSP+CK096TVl/LyQBUtDsn+7i9Q5VHN6tMiePdTtmaE3\nTsWT/zw7nkhDXFNwZ32qoxq8hC3uLpglkw2k3e1RgTzZ0sjjrNODDAWyfzPk\nzPcm3ryYS5i83NRc42iCN1wVvgZZ8lRnROykZxosnM8eivdA2xrlrjVGy5N7\njg6nD7kH6EVwa04D2unQYHzHhA/IM4zzQaZxl6CLeIS5rHyx33/jSv4vHfWs\nSxqOi0ghdYIh6hDfb0Afw1DQVL4zlVALmg5PebzVeukdmcUURWXKjVzUgzHR\nptD6+nLkfhF/7oZoQWlg189EXQOTR+fT9meQRNAWAYQpXIaIUtpSecOF6Qxk\nO74reJkLCb32N9gpBBlfmKSubLQ4WRb1tr8Db2rkR0yIS6HGuXFQVvtfKI2U\nB1aI29e0XQHTj4I+mLOi88rvhCt0p8GAcSRfDDRthsw/1aawdLS1eq/dLSjh\nDvYTYvsRbbgWmLyHq25Oi3rqwgtIJszIeG8aKHPYXpfAOUQ3QUPFXosOuyva\n4YxmNRWd29cdRkYeRrWlYfdgr2qt4Tnj4lkIVPrz0R/sRUst6GxbBLtr1MNm\nhQkV\r\n=6Qhr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFrwDDVYIYStlx4qiOruX4CS3UqGdWC9bIHJeogvBK9fAiEA83w+1vrOFWHjDUP7xjMwO4uB5E2f4s0U+u8WgcxyD2E="}]}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bnjmnt4n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.7.0_1640331338606_0.01259576861012257"}, "_hasShrinkwrap": false}, "0.7.1": {"name": "regjsgen", "version": "0.7.1", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "nyc --reporter=html npm test", "report-coverage": "nyc --reporter=lcov npm test && codecov", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "regjsparser": "^0.8.2", "request": "^2.88.2"}, "gitHead": "a8075ee0471dc96dff313e1e37c1922409a80978", "_id": "regjsgen@0.7.1", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-RAt+8H2ZEzHeYWxZ3H2z6tF18zyyOnlcdaafLrm21Bguj7uZy6ULibiAFdXEtKQY4Sy7wDTwDiOazasMLc4KPA==", "shasum": "ee5ef30e18d3f09b7c369b76e7c2373ed25546f6", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.7.1.tgz", "fileCount": 4, "unpackedSize": 14712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5PN4CRA9TVsSAnZWagAA/HgP/1sqwKnp8q47Gf93aXH9\nWmM+YVuTrya9pSPaQFnaGsPq0lK6P5LMtZ6FPHR4DMU+7qWDtUjZ5Tum2TgB\njVgqWmFKArcZoe8ymQftGyk91E1MDsmurFQrDt8ahmn7FCjt8fSmapDqURhk\nde+tUSXVBl9n/BCYE9O4V0gaqK+m7DKyLVWgpGKOEIzhAaKKI9VJ1AaO3HfV\nXVacI6r17HLImVbgbcZ1erdeKHQiFr0+Cjvh762Qd9hu1X7F2ZMKxqgf/wSe\n8YpOI0yOnzPemm7WaJu9oEgRY9D0bUqtd2XlkPTzkX59B/lpd4A2xanBVDOq\nql//ByyOC0qqeEvX0X7ki8Qs2wuk89moKRx+Eb+l6PXhyLnU0F1S46MaoBks\ngqsFlpQKipuQlHZ+dYPp5qnNtrJMbKuRMJJ5/J5z/wgxw3Pvu64FGmrhFL4G\n5QcglBMlDfaejMN7dLAXTXEfnHxHi8cbKuEP9NYTxb6/m/yJlXcV2YbMzL/W\nl7yId/dPF3Wl1kpDMZTDP/e0LcQwUtiLbBY/qhBcWkMu3UcjFG9iDLQ4UdVN\nU8yTLxRpC63WX13is16aanicDq1T7hJcyiBsXglLmBD7Fkp2FfkV+EyRfjNv\nb3s6PxWDvM1q0umxGxWEfpu5oKwbPnPMQ8hfNzEIDDyP8k0d0+LJt8IsZmgG\nUt8F\r\n=3uBn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC578cUKLa+ZhzrheVOP7xjGR3qfUPKeXA7k8RE7YNKFwIgfBusZYX4Ha6pi3+eruOdyloaf3abdNQ65Wsx8VG36Zg="}]}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bnjmnt4n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.7.1_1642394488744_0.5785602177231037"}, "_hasShrinkwrap": false}, "0.8.0": {"name": "regjsgen", "version": "0.8.0", "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "homepage": "https://github.com/bnjmnt4n/regjsgen", "main": "regjsgen.js", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "scripts": {"test": "node tests/tests.js", "coverage": "nyc --reporter=html npm test", "report-coverage": "nyc --reporter=lcov npm test && codecov", "update-fixtures": "node tests/update-fixtures.js"}, "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "regjsparser": "^0.10.0", "request": "^2.88.2"}, "gitHead": "fbb7ff03be5b72898c282a2376f78c95d07ec739", "_id": "regjsgen@0.8.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==", "shasum": "df23ff26e0c5b300a6470cad160a9d090c3a37ab", "tarball": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz", "fileCount": 4, "unpackedSize": 14986, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDP3PoR8bN5v6aqsi4eqVt0zVv6qYPEWEPBfI/GEpBEMwIgOPxca1GfE5HQV2CCkg4yoSK5S4t0/DrBjZe0+Ibpjq0="}]}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bnjmnt4n", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsgen_0.8.0_1686069840922_0.5380264095904803"}, "_hasShrinkwrap": false}}, "readme": "# regjsgen [![Build status][ci-img]][ci] [![Code coverage status][codecov-img]][codecov]\n\nGenerate regular expressions from [regj<PERSON>ars<PERSON>][regjsparser]’s AST.\n\n## Installation\n\n```sh\nnpm i regjsgen\n```\n\n## API\n\n### `regjsgen.generate(ast)`\n\nThis function accepts an abstract syntax tree representing a regular expression (see [regjsparser][regjsparser]), and returns the generated regular expression string.\n\n```js\nconst regjsparser = require('regjsparser');\nconst regjsgen = require('regjsgen');\n\n// Generate an AST with `regjsparser`.\nlet ast = regjsparser.parse(regex);\n\n// Modify AST\n// …\n\n// Generate `RegExp` string with `regjsgen`.\nlet regex = regjsgen.generate(ast);\n```\n\n## Support\n\nTested on Node.js 16 and 18.<br>\nCompatible with regjsparser v0.10.0’s AST.\n\n\n[ci]: https://github.com/bnjmnt4n/regjsgen/actions\n[ci-img]: https://github.com/bnjmnt4n/regjsgen/workflows/Node.js%20CI/badge.svg\n[codecov]: https://codecov.io/gh/bnjmnt4n/regjsgen\n[codecov-img]: https://codecov.io/gh/bnjmnt4n/regjsgen/branch/main/graph/badge.svg\n[regjsparser]: https://github.com/jviereck/regjsparser\n", "maintainers": [{"name": "bnjmnt4n", "email": "<EMAIL>"}], "time": {"modified": "2023-06-06T16:44:01.243Z", "created": "2014-07-01T07:27:00.362Z", "0.1.0": "2014-07-01T07:27:00.362Z", "0.1.1": "2014-07-09T09:15:23.663Z", "0.1.2": "2014-08-24T04:00:41.014Z", "0.2.0": "2014-09-02T04:32:12.545Z", "0.3.0": "2016-06-01T09:38:45.706Z", "0.4.0": "2018-01-17T12:34:24.541Z", "0.5.0": "2018-10-27T02:41:55.238Z", "0.5.1": "2019-10-20T08:31:48.247Z", "0.5.2": "2020-05-24T15:27:50.436Z", "0.6.0": "2021-10-01T14:10:14.157Z", "0.7.0": "2021-12-24T07:35:38.937Z", "0.7.1": "2022-01-17T04:41:28.905Z", "0.8.0": "2023-06-06T16:44:01.106Z"}, "homepage": "https://github.com/bnjmnt4n/regjsgen", "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "license": "MIT", "readmeFilename": "README.md", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "users": {"amobiz": true}}