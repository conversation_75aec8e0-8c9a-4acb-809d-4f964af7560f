{"_id": "acorn", "_rev": "222-1a86b7e2b6f0344595db0bffcd041a32", "name": "acorn", "dist-tags": {"latest": "8.15.0"}, "versions": {"0.0.1": {"name": "acorn", "version": "0.0.1", "_id": "acorn@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "dist": {"shasum": "326bc57985a3433daa37308845359e684fe49f17", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.0.1.tgz", "integrity": "sha512-TCwwEqBKstmjaOt7dtKMci1gwxRRgm5pE3bSauNLZ03EYGWIWLeem/x7FvMgkX/pV8KUADs+aKvFevzZU1Z8aw==", "signatures": [{"sig": "MEYCIQDyrq5I0Blg82AofTst90y0hLQjSOx+0Kdd/+RsJFGKPgIhAPogZW0oaymCxFQA8SpzfnMWFzSoUkKI+0NHwQ8xS1rs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}, "repositories": [{"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, {"url": "https://github.com/marijnh/acorn.git", "type": "git"}]}, "0.1.0": {"name": "acorn", "version": "0.1.0", "_id": "acorn@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "dist": {"shasum": "d278d28e293186aa1c7984704eaa0b3b88989b9c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.1.0.tgz", "integrity": "sha512-+CoUKvPJxQAQtOIMB9toxYZm5lvavNJHqvSK06JNTkR5giFcm9zyehia5spkQpXGQR1zLlArnmHcolLFy5FkOQ==", "signatures": [{"sig": "MEQCICWRl4T2IFHWLSC7SjefmA1uoUmHqVWRdLR6G3yqRYjPAiAHyfvo5Q0f4f83TAEVCD1k+0igc5fqYt816yXzv/Rmfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}, "repositories": [{"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, {"url": "https://github.com/marijnh/acorn.git", "type": "git"}]}, "0.2.0": {"name": "acorn", "version": "0.2.0", "_id": "acorn@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "94a50d2c55c3741df5bdbc6e0fa5663a880889bb", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.2.0.tgz", "integrity": "sha512-j/X8z36tZ5iji34USpSGqf1gYg5nr4JZl/ai10cNcU70DMvce+vShvFFtgaToj+XfZFqu7iliJEipJ0uBNUumg==", "signatures": [{"sig": "MEUCIGphgz0itROgRnaoBIBquBLAjajugPt0sBM3wAkhwlD/AiEA+o+0flZSH0YpIOfSQguX1uZILiHPPgFmvP4V4gcAoOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}, "repositories": [{"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, {"url": "https://github.com/marijnh/acorn.git", "type": "git"}]}, "0.3.0": {"name": "acorn", "version": "0.3.0", "_id": "acorn@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "35b326d4f11393e4368a7ebd135987593cc1884c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.3.0.tgz", "integrity": "sha512-Xrv1/EBEjhCe531AyGQ3Teb5T9qbJS1Eg7WbWN6IdTKlbzmJ3t34cIyLuMhNLIV5qRAyo/QkzJ61COM2gxMs7Q==", "signatures": [{"sig": "MEYCIQCPU5l+JehhFGoV2KB8GnqfF9PL1XISSuXFr8AtaRIkAAIhAI0PPf+0EIM2U+P5tF2WuAGQuoWFGRDitL8CjFCw5T43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}}, "0.3.1": {"name": "acorn", "version": "0.3.1", "_id": "acorn@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "9f0949f8decd3e544b5c8197e7843ee58d8cc9ee", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.3.1.tgz", "integrity": "sha512-/5LW069ZTqANo5V20ibkCWPWNEYew7YnTGdhn8jd62LHqrVc9kKRoyhHU0VFequmM1pz1AH7KZWaVp6qQNgk0Q==", "signatures": [{"sig": "MEQCIF8kJGR9uqiCWjuvEyFemvEEarQI7abjjjPBdh6BKt8JAiA0kyWkZpQYC3wsaqsl+c72PG+DKF5rEoqTasFhYBRHog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}}, "0.4.0": {"name": "acorn", "version": "0.4.0", "_id": "acorn@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "2e1e880975278cc2538afb6a051e86e3c0befb99", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.4.0.tgz", "integrity": "sha512-1P<PERSON>z3vYOKxJM8tz6PIVLiaLCW8PKYR37r9HO3v40zrkAlv/Fn4oceOF9OzrWZB2Bb8flkSMYGtyFlCZHc1LHQ==", "signatures": [{"sig": "MEUCIDTpyCNCAc/yb2Xm8FcCXjwMqaYM/eDXEK9KsbGfKf/VAiEA9pP5QDM17T62n1GtGO0tMOzE/z/CIEMWEN5BTqovWIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}}, "0.4.2": {"name": "acorn", "version": "0.4.2", "_id": "acorn@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "c79a8c12e98fe6c5a600f28070232d4ede308b0e", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.4.2.tgz", "integrity": "sha512-kXl9FfA6UNrNOjwALowIkwPVabyjc3RjQkFG5BU8azNv86L8dabW77k2ge4Z0k6Sp5VSuyYV9dUhSjcqQmJZWA==", "signatures": [{"sig": "MEYCIQCBKkPkYeUHO6SQC9HkwTB4SrOFtd8HirGGqsVTo4pnGQIhAJmCVobao+DkQCyLp0Z087PYt51wR9Zi7jV96BFRpvrf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.1.59", "description": "ECMAScript parser", "directories": {}}, "0.5.0": {"name": "acorn", "version": "0.5.0", "_id": "acorn@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "f342114b20e314c5fe7f72e8b7b37239604ea40c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.5.0.tgz", "integrity": "sha512-C+F0F79vOrEG1cNTBTKDJma7LSiWl6AkWJd1lqt6W784YLQSD+bFBLAbbLOXDHSr7+vSZvy5cX3CTMsCgocfxQ==", "signatures": [{"sig": "MEUCICF1qnI5iOgaXkNXqfeVHKgATZFKB01JVoBCEc6+irUQAiEA7LfC1+gigRxsjz/IHAAGPMxubBr7ZgRij+r9U86xHw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.3.15", "description": "ECMAScript parser", "directories": {}}, "0.6.0": {"name": "acorn", "version": "0.6.0", "_id": "acorn@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "6e3d2156facfb363368bdabc735ca15e4e7513b3", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.6.0.tgz", "integrity": "sha512-f9eoayjYMj1UhTvuK5f4y+kECzbljyKJZkpRf2NY3f/9z2q8zaJM5pJI+mf5UHsRgXneL8cF3ZrPfDeX+NQ5Fw==", "signatures": [{"sig": "MEYCIQDO3rzuwKV42lLBWUyC/R6dQaA4yO73sFFl3l0VwPsD2gIhAImj5fid+4dO9s+vvH52MUkD+9h8dAGwhRSFA4LdIDLA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.3.15", "description": "ECMAScript parser", "directories": {}}, "0.7.0": {"name": "acorn", "version": "0.7.0", "_id": "acorn@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "zsjforcn"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "cb184aee3b2a13e0eef0c43bdfee14a5816469c0", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.7.0.tgz", "integrity": "sha512-AH7lJzxP4Jx4Gd+6or9Lhub43QxbljrA8R+436tJDmscVmbQPEY0nHsLfKg2klHbz65jKdOAGvfDG7c6pfFBXw==", "signatures": [{"sig": "MEUCIQCWk7NYrFmy/2xuU1N1Up/VoqEU2z3CrZYnIX+LOroQyAIgDCnzrHYKN6Ffvn/EVNUryTIzgYFVPZvOEvECEI4CyHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.3.15", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}}, "0.8.0": {"name": "acorn", "version": "0.8.0", "_id": "acorn@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "zsjforcn"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "e07ae6721110b22878242950ae2e96658b0aac7e", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.8.0.tgz", "integrity": "sha512-xNrVGYbU3j3DliCJbmiA6wGn2KaoGm5VNE5O/gTr00jpWBm/x4/x1bzl1ur/kt/9TdMrhOLQD26qBncKi5lcCg==", "signatures": [{"sig": "MEYCIQCVtQPHaJUrMecQ+6FwrMSMdzGceXuhruQYaG5q4g0dAwIhALvNw6UgeLbwVIbDO9eqVsT1+BPr2MhuMKFJQM73Q6ka", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node test/run.js", "prepublish": "bin/without_eval > acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.3.15", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}}, "0.9.0": {"name": "acorn", "version": "0.9.0", "_id": "acorn@0.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "zsjforcn"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "67728e0acad6cc61dfb901c121837694db5b926b", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.9.0.tgz", "integrity": "sha512-EUkRU2MEioMEzdlGMNloq79cYzTk6g/5vg2Skap1bCn68225yeQIb2FRShYeJnfd1/YL2AuLvyJWwbX6kbIHuQ==", "signatures": [{"sig": "MEUCIQDwUduiLzXhRZHXN4MOT+XLY0wp6sJzZkNZbVis08MZmAIgLQKi29uAS/pXJT2O6fP3sLbXnTs/MrbckPQMAXS5ODo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "_shasum": "67728e0acad6cc61dfb901c121837694db5b926b", "engines": {"node": ">=0.4.0"}, "gitHead": "01a89cfec14fa4875b98c3e829e5a76a2a1208f7", "scripts": {"test": "node test/run.js", "prepublish": "bin/without_eval > acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.4.23", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}}, "0.10.0": {"name": "acorn", "version": "0.10.0", "_id": "acorn@0.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "c8a20812d631dfd60f85596472d34f686e39fef8", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.10.0.tgz", "integrity": "sha512-KaSxQHOYwQy+Wt/LDEIY//VUJgXgwKrs34D9P4l068CwoRNYe89MhIgpKaUWCQiQA+yZDs40Hp5ljk9Iu1vM3Q==", "signatures": [{"sig": "MEUCIQCCOkPLAjzbSg5BaO7zfyeoVbNaHV3Y/BzDPZgou28AXwIgcmLXSEQXwbTkS9OCWZlEjDpHQqTTWO6X8BByaFzpfjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "_shasum": "c8a20812d631dfd60f85596472d34f686e39fef8", "engines": {"node": ">=0.4.0"}, "gitHead": "0a812b6020e89bc375dea94f144f68851dc8813f", "scripts": {"test": "node test/run.js", "prepublish": "bin/without_eval > acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.4.28", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}}, "0.11.0": {"name": "acorn", "version": "0.11.0", "_id": "acorn@0.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "6e95f0253ad161ff0127db32983e5e2e5352d59a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.11.0.tgz", "integrity": "sha512-fvlA412hssF7l+22DIRlhXUKIuUz9Z9IrCMn/gPyWx90R75X/xB4SsqU8Y1vhiq9xd72P6duzQn5kKojqFN6cw==", "signatures": [{"sig": "MEYCIQCnbTMjQu3kUMSyRDzVr/kUe5Aa7tDz1d3RcImHby2VwAIhAJlshYt/R55Tb6AXl10ChGKhBdehQnZWXKTnFC/C7Vuw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "_shasum": "6e95f0253ad161ff0127db32983e5e2e5352d59a", "engines": {"node": ">=0.4.0"}, "gitHead": "78e1d7ada684868d92d09555408ae2df1812b5ae", "scripts": {"test": "node test/run.js", "prepublish": "bin/without_eval > acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.4.28", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}}, "0.12.0": {"name": "acorn", "version": "0.12.0", "_id": "acorn@0.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "http://marijnhaverbeke.nl/acorn/", "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "337b0bb293dfd6239d7e66c92c727c56a08d403e", "tarball": "https://registry.npmjs.org/acorn/-/acorn-0.12.0.tgz", "integrity": "sha512-itM1P1dPZymI8PCDR/eU0Q1KyIoPv1o7XqsjiEDHuQMZ0X6gPC79OpJScw4a8ZgzDQ8dvnRi99kBEPVcvJX99A==", "signatures": [{"sig": "MEUCIEdgdD5aX/hvFg8vwple0V+76vcVwVhz2eoBLg9RtorbAiEA6YukySLClq93E5U/aNry5yc31n7BeOFobRcyIwdfvxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "acorn.js", "_from": ".", "_shasum": "337b0bb293dfd6239d7e66c92c727c56a08d403e", "engines": {"node": ">=0.4.0"}, "gitHead": "99f1989f72c340f517ac5a99f9293484ba1fddc5", "scripts": {"test": "node test/run.js", "prepublish": "node bin/without_eval > acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://marijnhaverbeke.nl/acorn/LICENSE", "type": "MIT"}], "repository": {"url": "http://marijnhaverbeke.nl/git/acorn", "type": "git"}, "_npmVersion": "1.4.28", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}}, "1.0.0": {"name": "acorn", "version": "1.0.0", "_id": "acorn@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "c5b9714a1c254b5109a047baeee1a84c22840b07", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.0.0.tgz", "integrity": "sha512-bs2BHqYRDTX0yei8horrZ8XG8w9457qBYkgNAK49/XO/FpuWKkcNyi2D9C2plKqM5YJS7AeU5GDR2+gqDa3bkw==", "signatures": [{"sig": "MEUCIQDIwWJA/kceCpOYnr9Za6ZMivx+kHot6uJFaVFfWvuNTwIgUsCopG6NjXb8pigEKikI8H+T5nf2QN60RXsQWeBr2J0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "c5b9714a1c254b5109a047baeee1a84c22840b07", "engines": {"node": ">=0.4.0"}, "gitHead": "baa833d6359ace7a98eb0830062b85f759457689", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/marijnh/acorn/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5"}}, "1.0.1": {"name": "acorn", "version": "1.0.1", "_id": "acorn@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "ad62a7902c1e0ea3c5f081190c6b02e33aa7030a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.0.1.tgz", "integrity": "sha512-goyeqJaYIEon7Xw+ltfheM2MSoZ1lDffIYDBMP9UyUDYhsBiw1dAV07wlUGwRYntyvlxGpk/Avk58Ff9clZuUw==", "signatures": [{"sig": "MEYCIQDUvqmofOI8PIDxi6jbSO1S6aDMnQ+dkkQYZoqwnQ50OwIhAICGqzchZcdGf5gXxhqLFiZZBL9Z0V3zM5euaE2gKU/V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "ad62a7902c1e0ea3c5f081190c6b02e33aa7030a", "engines": {"node": ">=0.4.0"}, "gitHead": "cc1806a265551e0667986d203706675f69881010", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/marijnh/acorn/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5"}}, "1.0.3": {"name": "acorn", "version": "1.0.3", "_id": "acorn@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "6f4cd7c4de8f43a92708ecd8180fe4aca36baff0", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.0.3.tgz", "integrity": "sha512-oXxZRX6IXHww6eNfWA0qN/NNnJHocjqWmV/Lzcya1Wq/fFgUVkhZAUZKvGefgdXlldhwSPw/CrRt9MDqi8NXkg==", "signatures": [{"sig": "MEUCIQCe5dIEOQAdX8FSO7ilz2QKzLsEEefecsGXPtCNJYSgrQIgb5pquazZE/swd8pZFE9k74fLrCfgzvBgZcYrCJmajC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "6f4cd7c4de8f43a92708ecd8180fe4aca36baff0", "engines": {"node": ">=0.4.0"}, "gitHead": "948edcc51cc305027adcfc6eba2de977c5349b62", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/marijnh/acorn/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "1.1.0": {"name": "acorn", "version": "1.1.0", "_id": "acorn@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "cc407869432902483e2993eceb89e58a2a31eaf9", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.1.0.tgz", "integrity": "sha512-Ii2MtgNkMtaHNZGiaZltuPPsCWblEUwVIrT6gS7ijpu1j6qS+xQd5z3TZ602NextGuDkv3rLCIcgYEH7DabRnA==", "signatures": [{"sig": "MEUCIQD+flAzoeYjWaXhIuiSnIkBXJWwFAoBuyRb7AKvXxMkHwIgLPpCJfx/tiFXgxEnrpMOQXAGPljU3nLpY8593S6fIfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "cc407869432902483e2993eceb89e58a2a31eaf9", "engines": {"node": ">=0.4.0"}, "gitHead": "8d4a4ff1c9ae7a6cdf97967b952b2cfd7ab99db7", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/marijnh/acorn/master/LICENSE", "type": "MIT"}], "deprecated": "Accidental backwards-incompatibility in the AST walker", "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "1.2.0": {"name": "acorn", "version": "1.2.0", "license": "MIT", "_id": "acorn@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "d0e511e060bb4b2294f75cb97fb2af01046ffa56", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.2.0.tgz", "integrity": "sha512-HZJ9YMDgsh7NG12tg3zIiHHATWKWrgwOPAk13clDBzyhHUNIAgxK2qcfnPgoRO01iKTy+GRE3KFbeFOm4VoUdw==", "signatures": [{"sig": "MEUCIActgwr8lrt+OhHs33kLkN/a9ILuwRlb0OnPaCmgPGSrAiEAxNS5g/hfR5IbTYKPdWNaYvWdn120jK3ljFHMJa+HbaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "d0e511e060bb4b2294f75cb97fb2af01046ffa56", "engines": {"node": ">=0.4.0"}, "gitHead": "9189e775e9cfd8d130ee8e92c580158647e0490a", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.0.0": {"name": "acorn", "version": "2.0.0", "license": "MIT", "_id": "acorn@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "9dd84a765d7279672f0728c8034a9cebd2c5e578", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.0.0.tgz", "integrity": "sha512-OxzL6SaSy5HVIkLgVZ2nVVpts3WwQXCOgK7cRlHp8B5daYVDeELOQKiQuPoBmbS6Chh5mdBIn+cfWid6/tf+lw==", "signatures": [{"sig": "MEUCIH4qQvwNsO5+DcL8Oq8pfAF7dqLHxI5zVVTUu39oNHCyAiEAoFp3gapF7P1NnogdIB7Ugt92g6sMnvg+/ucfvBnducE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "9dd84a765d7279672f0728c8034a9cebd2c5e578", "engines": {"node": ">=0.4.0"}, "gitHead": "584807eb60936fbede95388adc1436dc44d8bf48", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "1.2.1": {"name": "acorn", "version": "1.2.1", "license": "MIT", "_id": "acorn@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "ae46ed295eea4e175a376634a8825eb6710f8058", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.2.1.tgz", "integrity": "sha512-1rC++NY+iZovcq4C1O5Ze2DuWxuG/RxWxs04WZrWBjv4gW56Wg50lj76iD7DtDkfw0dBBS1jPkemIRCklr+GTA==", "signatures": [{"sig": "MEQCIEtN0aDiXXKCZNH1GcEdbug+9x2t7aXCkGA0ImAcs6F5AiBOsdNEUbGTnDlCMkVCZLeHhWLae3aiTYtCYZpxYqnr4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "ae46ed295eea4e175a376634a8825eb6710f8058", "engines": {"node": ">=0.4.0"}, "gitHead": "d3d29372096ed29f5abdd754347fc8b2efb56651", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.0.1": {"name": "acorn", "version": "2.0.1", "license": "MIT", "_id": "acorn@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "e577f8af33249fc0bf7b273512254268e5e2e533", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.0.1.tgz", "integrity": "sha512-AgnkHzwC/crxdh+MMcaK6pjsDtQeuQhxF+6G3DqT/+4NekIG6gI/nM+QT2Fh4HRk+VCtbHnJosp2vD+sTTnYzw==", "signatures": [{"sig": "MEUCIG7TA2jpL9M6ZJJa+LZt9SAeB/Y0kWsAYJm5PN1WfTW1AiEAyRsAnu9p/dPTGYAu3HdsNmDIEwtCSwk4f5jANiJdFzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "e577f8af33249fc0bf7b273512254268e5e2e533", "engines": {"node": ">=0.4.0"}, "gitHead": "4518005f9d3ba4fe82dcc3cf6ca783f9ee706877", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "1.2.2": {"name": "acorn", "version": "1.2.2", "license": "MIT", "_id": "acorn@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "c8ce27de0acc76d896d2b1fad3df588d9e82f014", "tarball": "https://registry.npmjs.org/acorn/-/acorn-1.2.2.tgz", "integrity": "sha512-FsqWmApWGMGLKKNpHt12PMc5AK7BaZee0WRh04fCysmTzHe+rrKOa2MKjORhnzfpe4r0JnfdqHn02iDA9Dqj2A==", "signatures": [{"sig": "MEUCIFJT5gWfTcqHfeUzAseJBJ6DvHZzaFDDD6X/9i3hymEpAiEAtYaUpV31927AA8JUi3T0jGVPuNZca9h5dUQu2QfrjOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "c8ce27de0acc76d896d2b1fad3df588d9e82f014", "engines": {"node": ">=0.4.0"}, "gitHead": "0857d8bb9c3c05e6c8fac9e83fddaefc4f43816f", "scripts": {"test": "node test/run.js", "prepublish": "bin/prepublish.sh"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.0.4": {"name": "acorn", "version": "2.0.4", "license": "MIT", "_id": "acorn@2.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "d6cb7a65fdfc1a16586132baa40528dafa28a16a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.0.4.tgz", "integrity": "sha512-G4SBux5vm8S2qgTOqAPwZTPdY/RiztHIy4oJ7/m+9FnGtd+vhq3jD4PHfIG8DBuiN8xfhWMwtS8NmlJAZQae+w==", "signatures": [{"sig": "MEUCIQCgLSYvOoPrZwJdUWuiKCmG0DUHSteg/izRaBTC3dybpgIgMZK7QSlymmqWxWc6jMPMA0LVR3ndW53KGI/jq989Sog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "d6cb7a65fdfc1a16586132baa40528dafa28a16a", "engines": {"node": ">=0.4.0"}, "gitHead": "6760df2c53527d7b02b260b24556c50d7c6271ba", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js && node bin/without_eval > dist/acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.1.0": {"name": "acorn", "version": "2.1.0", "license": "MIT", "_id": "acorn@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "f7f066239714dcdabf85705050d2b5aa6e3d0b55", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.1.0.tgz", "integrity": "sha512-TVh5slteJJfn+ZyW+jnLMPFkgb/7sVcV+1vYfpQNja0DF+MCH5dwKGoK/diEVRBWyNOzNjtQTufw4vmDjI2SRg==", "signatures": [{"sig": "MEQCIBNFsgRhp/X+I1D6HapWOUMC9zmlmnFMin6MPxu23yGzAiB4CJiwa5ItRdVjSxvhq0VfcZEg07V2UarETlhmG+NXOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "f7f066239714dcdabf85705050d2b5aa6e3d0b55", "engines": {"node": ">=0.4.0"}, "gitHead": "0c114fb5a1bfa8f9b216a36e2aef7a13c1d4fd88", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js && node bin/without_eval > dist/acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.2.0": {"name": "acorn", "version": "2.2.0", "license": "MIT", "_id": "acorn@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "8ce2308bc87cfdd7f234e2613ebdb0970fe518d8", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.2.0.tgz", "integrity": "sha512-mhuc55Y543ihydsFztOfCKQmbSrlcsrBFI2admLiyTYyvdR3jomUSlC7GJ5ZfoqmEH6uyPZrrWayV2ELk2InuA==", "signatures": [{"sig": "MEUCIQDjjc2NIs6auvYx1RLLN8LeK6taBP3pfaKWi6nugUyIiwIgNICeWWzE7W74dS6IcEnOsRl9v02ZMmOg8xpXG+hm2y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "8ce2308bc87cfdd7f234e2613ebdb0970fe518d8", "engines": {"node": ">=0.4.0"}, "gitHead": "58b659be49c34305cc1eeb87adf8384e0c79bd5a", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js && node bin/without_eval > dist/acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.3.0": {"name": "acorn", "version": "2.3.0", "license": "MIT", "_id": "acorn@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "1334584c8a3014ee9bd02fa09251cc97e43e388b", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.3.0.tgz", "integrity": "sha512-JT5g6OP4ReWDjKcuPBpZKbYwhaQMYBYEAWBvwyxlbDDroLxJwitRP8jUen1M2ihpvBQi7/kJgnULjvN7xVfywQ==", "signatures": [{"sig": "MEQCICnu4JlyUAI+2IIQKgmJaaFgxT0CZNu/O7ywAJA7EolaAiB5FKfT7im1WrZBuS+b6QshLPlkNobI04eYkRxOVfDoxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "1334584c8a3014ee9bd02fa09251cc97e43e388b", "engines": {"node": ">=0.4.0"}, "gitHead": "45d9a316b26098ba5f819d8a48c54a350ceae44b", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js && node bin/without_eval > dist/acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.4.0": {"name": "acorn", "version": "2.4.0", "license": "MIT", "_id": "acorn@2.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "ef0e3ac322ca516a1846452acd11a5560360aefe", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.4.0.tgz", "integrity": "sha512-f4sdQqiyUe+KISP1LklxUxDVjtknVjMCpUjxtKe7TTkUzBXUU1hmltNaMRqB+K34bvgzFQDJirUdbv53T4NZVQ==", "signatures": [{"sig": "MEYCIQCm9dQ88szhKQ6vMYoaL5+VbStZ8KxceDUUlAyJ2yghFwIhAIu9kxuKqwqdDtvhy4x833wjsm6veU3SDQYgLMKw6FX6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "ef0e3ac322ca516a1846452acd11a5560360aefe", "engines": {"node": ">=0.4.0"}, "gitHead": "b012acf2077198ac17786115daa446af323f17f3", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js && node bin/without_eval > dist/acorn_csp.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.5.0": {"name": "acorn", "version": "2.5.0", "license": "MIT", "_id": "acorn@2.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "6d796fc34f6cafb84df7d37a28cfd0335fdc3da6", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.5.0.tgz", "integrity": "sha512-ovM4RsG+50Mb95As8nl8MzSBxhYgIT1Fj2lnnD4HuOVGVwOZNZoAR3VjyjjKDxVsiJlu1LiqoHy/Gms3o/jcsw==", "signatures": [{"sig": "MEUCIQChSTFCS+Ts3O3GQQl3nW15V0sc8yT6+PcraedCfOfoRgIgMZehAjnuLEz3RhD463rmqTrVP6eQN4rfJRGeZkSubCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "6d796fc34f6cafb84df7d37a28cfd0335fdc3da6", "engines": {"node": ">=0.4.0"}, "gitHead": "6a706be56cd7943648085e7795ae88fde546c1f4", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.5.2": {"name": "acorn", "version": "2.5.2", "license": "MIT", "_id": "acorn@2.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/marijnh/acorn", "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "a68b5c814de12a8888ab4373542e5e5cbfe312a9", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.5.2.tgz", "integrity": "sha512-eHE8FQCQRwEe4NYR/nnu6R3P07lJJIN3/7rAltJf+/6rOOTFnooZQqO6VVZk7kZvHtX4audmtHFkN/GB4aQcDg==", "signatures": [{"sig": "MEUCIH5qbFxe6O/Y6149yK6wnWcsgJk+zQ7kpfNni5V0EiWkAiEAldlyDjYhgu0u/9SpC3r91JiLoepfi2JnKvZkbA7VAWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "a68b5c814de12a8888ab4373542e5e5cbfe312a9", "engines": {"node": ">=0.4.0"}, "gitHead": "298e6e72815dcf1e194e8e17533537e17a32f9bf", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/marijnh/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.6.0": {"name": "acorn", "version": "2.6.0", "license": "MIT", "_id": "acorn@2.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "ef6c0770a0bb1b2e3cd3ee13c8781f806978d0be", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.6.0.tgz", "integrity": "sha512-VDK8KMeUJw5XD9tu7i1X4upxE32QGsgF95/o/8P7ISyaWgQVASdgiN4YSjxFLfZAqgpiU4bopt9HWpClJEYEVg==", "signatures": [{"sig": "MEQCICgdxiinIp6YITZkeRls/4sAIZmyaC4hau9CRdv505PNAiA3mr/BZwx0+A5BGUqbApkd7Z/yApET6PJ64MbQaFjdxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "ef6c0770a0bb1b2e3cd3ee13c8781f806978d0be", "engines": {"node": ">=0.4.0"}, "gitHead": "e45e55b221226f6b14b00c310555f63a62690035", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.6.2": {"name": "acorn", "version": "2.6.2", "license": "MIT", "_id": "acorn@2.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "18235b42ac934ca30f89d27472b653985dd03f76", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.6.2.tgz", "integrity": "sha512-ezH/NoT8ywZcBmatNtD/dSmjx25miuuklfJqzw3OqkkyiLHgxQYjnZ7fi3ds4I6urEW6F8pLfTPgsRntUmEOdA==", "signatures": [{"sig": "MEUCIB+Gk1fLyXN3pQ909FwRt4203P+Gs2PhEzjnJD9+caC/AiEA2grLgFi1Q5g5JiVl0VZWK8E0ztgmIFsDHNHoXhKn0ZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "18235b42ac934ca30f89d27472b653985dd03f76", "engines": {"node": ">=0.4.0"}, "gitHead": "aa7d7ba03e1256339f0f44830586794552ec131d", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.6.4": {"name": "acorn", "version": "2.6.4", "license": "MIT", "_id": "acorn@2.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "eb1f45b4a43fa31d03701a5ec46f3b52673e90ee", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.6.4.tgz", "integrity": "sha512-aINieSoQYX0C9uQqJGeC8mnO1T6onBTmtCdxHel6ZP/nBu4mpC03EoDtQUzAAAlUXluWjIvVV9vCuMhmOdRDXQ==", "signatures": [{"sig": "MEYCIQD4ENp3wP+29bsZ7Ehi0TdPuIJsHfhhhgtHaAqv/tZO2gIhAJIr5WtphO8bu+f1n4V2PEW6D3zSWyfw6s8kGV0xeU/B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "eb1f45b4a43fa31d03701a5ec46f3b52673e90ee", "engines": {"node": ">=0.4.0"}, "gitHead": "2c0e4ab7872135959dee8d331c7089353365d379", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ECMAScript parser", "directories": {}, "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "2.7.0": {"name": "acorn", "version": "2.7.0", "license": "MIT", "_id": "acorn@2.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "ab6e7d9d886aaca8b085bc3312b79a198433f0e7", "tarball": "https://registry.npmjs.org/acorn/-/acorn-2.7.0.tgz", "integrity": "sha512-pXK8ez/pVjqFdAgBkF1YPVRacuLQ9EXBKaKWaeh58WNfMkCmZhOZzu+NtKSPD5PHmCCHheQ5cD29qM1K4QTxIg==", "signatures": [{"sig": "MEYCIQCma3hUDNR5cGnQqknTwX84iUayrtFaf7sK/BIBNZ3u2wIhAIr4Wp9vuxJuxgf2o+3VUHbNn6wvhiYkmmq1iTYGF64O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "ab6e7d9d886aaca8b085bc3312b79a198433f0e7", "engines": {"node": ">=0.4.0"}, "gitHead": "1405436064bff087f14af55a763396aa5c0ca148", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-7.0.0": "~0.1.5", "browserify-derequire": "^0.9.4"}}, "3.0.0": {"name": "acorn", "version": "3.0.0", "license": "MIT", "_id": "acorn@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "bf4bd949fa3f3db67720a3d398a5b2903aa9c740", "tarball": "https://registry.npmjs.org/acorn/-/acorn-3.0.0.tgz", "integrity": "sha512-Dvef5m7jndSJIOXwUeGtazQ7iD3sM8+o8cTBy00vfjQMjpPPNOlcNsKqOxIWVhwHZWd7XGdDF8XVbyhOW97a+A==", "signatures": [{"sig": "MEUCIDPqDVRy0GyZBNW8pqIhX5vEWrFGyANBIfxySn8W/eRkAiEAo/TNhLDQRA39+GbJowAdMd/w002BnXuPPZR9SChmpyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "bf4bd949fa3f3db67720a3d398a5b2903aa9c740", "engines": {"node": ">=0.4.0"}, "gitHead": "a31a904d53606e05e094fa92de65c27fd47f7fe7", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "4.2.4", "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-8.0.0": "^0.1.5", "browserify-derequire": "^0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-3.0.0.tgz_1455096550752_0.13618880859576166", "host": "packages-5-east.internal.npmjs.com"}}, "3.0.2": {"name": "acorn", "version": "3.0.2", "license": "MIT", "_id": "acorn@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "fbeab84d332bae665b640e6b721eb9d6b06e2ee2", "tarball": "https://registry.npmjs.org/acorn/-/acorn-3.0.2.tgz", "integrity": "sha512-NKTM+prZG1nZBcSYPKM6K558Ph/ZrSvhRR3xn6Js88QjtgSbZ+HB6dnfmygNzoe/PiMl0eyf8eIqgEAhnPqhnQ==", "signatures": [{"sig": "MEYCIQDAGkxUQ9z9VrJfkIsUEZv2A8CtWY6j2IyxZLui7R3tKAIhAKqX7z4V1OL4lgS/gIG2848O79VKaqDl2op4p7vMtVk1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "fbeab84d332bae665b640e6b721eb9d6b06e2ee2", "engines": {"node": ">=0.4.0"}, "gitHead": "31a9d19da386156b208b3b9131ecf7ef90b6b8ce", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "4.2.4", "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-8.0.0": "^0.1.5", "browserify-derequire": "^0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-3.0.2.tgz_1455101363464_0.9033806468360126", "host": "packages-6-west.internal.npmjs.com"}}, "3.0.4": {"name": "acorn", "version": "3.0.4", "license": "MIT", "_id": "acorn@3.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "04f244950fdb8faf85507ad481c2edee7aecdeec", "tarball": "https://registry.npmjs.org/acorn/-/acorn-3.0.4.tgz", "integrity": "sha512-WqBIfa8wHnbz2LjeLHov4pjJ6iin9FPLpOt4+bdMkkHPo2Rf6RGfqL1LJhbLv5nkk1U0nB4sPyKVjmIau6TMSA==", "signatures": [{"sig": "MEUCIQCK5dMTBtM5coKSaCrUsNS5UzbJ9rNG71koF9NRz0EX4wIgIQknONAxcogqqxCWHhNT7yMkpGIp8m1uC9cTg6kVQq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "04f244950fdb8faf85507ad481c2edee7aecdeec", "engines": {"node": ">=0.4.0"}, "gitHead": "327cef57d0907500ec1ab5508c6eccd727a79cf9", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "4.3.1", "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-8.0.0": "^0.1.5", "browserify-derequire": "^0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-3.0.4.tgz_1456390282440_0.9833372407592833", "host": "packages-6-west.internal.npmjs.com"}}, "3.1.0": {"name": "acorn", "version": "3.1.0", "license": "MIT", "_id": "acorn@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "e79a281c23983ccc079471a849866067e7f0c693", "tarball": "https://registry.npmjs.org/acorn/-/acorn-3.1.0.tgz", "integrity": "sha512-pRfZnVEk5vX2+Z9N/ko1Sl4zK3Q+zDDDD09swT8wjq4K0+ZOJ12lav3y53gYfoOH1pB8qMEsggch1I/Qk7a2tQ==", "signatures": [{"sig": "MEYCIQDV7XRpoUMT9mdn2nrO9AD1FA7wrCxo8EKnsc89swtpuQIhAOoSXXJ9k2hLXMgbr20g26kzJuXBtBBig8cMX/jqsaqO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "e79a281c23983ccc079471a849866067e7f0c693", "engines": {"node": ">=0.4.0"}, "gitHead": "3c5ef61cc55dc887cbb807fa8d6917d6af85af51", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-8.0.0": "^0.1.5", "browserify-derequire": "^0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-3.1.0.tgz_1460967388002_0.5425194606650621", "host": "packages-16-east.internal.npmjs.com"}}, "3.2.0": {"name": "acorn", "version": "3.2.0", "license": "MIT", "_id": "acorn@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "-e List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "7a82989ef6f063a237ababaf8df20d2965184b9f", "tarball": "https://registry.npmjs.org/acorn/-/acorn-3.2.0.tgz", "integrity": "sha512-C0vKPaCHdLMHhKsq2Rx0IHGgQ29pNR57L4nThmubQP/HC/LuWsngUHKTG4jT7rhTG0MJpIg348uv9A96zIZslw==", "signatures": [{"sig": "MEYCIQCGb3CdChoCWy7GU9FEDohJLtsYMCzxYzDoZhvXzEGybAIhAKmK/jq437fd2iyBmJJB+u6NoRPik0saTJEiuq31s7Om", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "7a82989ef6f063a237ababaf8df20d2965184b9f", "engines": {"node": ">=0.4.0"}, "gitHead": "c7d72884836f92742cb6fcdb01aa42de0c2dd8fc", "scripts": {"test": "node test/run.js", "prepublish": "node bin/build-acorn.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "6.2.1", "devDependencies": {"babelify": "^6.1.2", "babel-core": "^5.6.15", "browserify": "^10.2.4", "unicode-8.0.0": "^0.1.5", "browserify-derequire": "^0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-3.2.0.tgz_1465299452629_0.4749092774000019", "host": "packages-16-east.internal.npmjs.com"}}, "3.3.0": {"name": "acorn", "version": "3.3.0", "license": "MIT", "_id": "acorn@3.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "45e37fb39e8da3f25baee3ff5369e2bb5f22017a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz", "integrity": "sha512-OLUyIIZ7mF5oaAUT1w0TFqQS81q3saT46x8t7ukpPjMNk+nbs4ZHhs7ToV8EWnLYLepjETXd4XaCE4uxkMeqUw==", "signatures": [{"sig": "MEYCIQCPKe0fGfXUhyvyo1wSJbvbVwCL0W46/cHw2h00dzT5PAIhAKnOBtE+TddTlprSTdEHiclmpmyxquEX0PSykf6G7EJJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "45e37fb39e8da3f25baee3ff5369e2bb5f22017a", "engines": {"node": ">=0.4.0"}, "gitHead": "693c5fe9257c3e114a7097dc9196d6e484e52809", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.3.0", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-3.3.0.tgz_1469481913382_0.3856039580423385", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.0": {"name": "acorn", "version": "4.0.0", "license": "MIT", "_id": "acorn@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "817e5f85894a83edf2361a2bd21870fa1fcafc34", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.0.tgz", "integrity": "sha512-V5F1dQzR87DYP/ivmnum+t5DQtXQ782JrQ2dVA4DFC9mQuUzmixIR1YcxhA++CBBfQGAGG4FeK6Ee+rTPwexQA==", "signatures": [{"sig": "MEUCIAxYR7iupquA+tO1pWxbog1XXadwnqnnP/RdrZXxQus2AiEAhJoEUiGcUbyyJkTzIWFwL/9pYD8BxR+1W0isVVxDdqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "817e5f85894a83edf2361a2bd21870fa1fcafc34", "engines": {"node": ">=0.4.0"}, "gitHead": "44f09254214f9fa9820af95817d38739a6c51cd0", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.3.0", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.0.tgz_1473236325531_0.22021302999928594", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.1": {"name": "acorn", "version": "4.0.1", "license": "MIT", "_id": "acorn@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "ecfb67e46b97771d5cacaf92ecde853d7b4b0459", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.1.tgz", "integrity": "sha512-0b<PERSON><PERSON><PERSON>UqcZT6DdSEgXLQf/PRjvOy4TiMREf/d/krZmNUtrywmvGv5pps88liMAxyNw8BiC1/qk7SlLTWesXUaw==", "signatures": [{"sig": "MEUCIF7BGUu/pMXu8VU/BQ7wqRwwX4VOyuv60GbFTaGWrArZAiEA9vQkt0qdyGdexmieV+JETGgYaLhbpDzLPJCrbsLMfQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "ecfb67e46b97771d5cacaf92ecde853d7b4b0459", "engines": {"node": ">=0.4.0"}, "gitHead": "cb95800519f8cd264d46840a84451296ac5e53b6", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.3.0", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.1.tgz_1473322282744_0.3456953226123005", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "acorn", "version": "4.0.2", "license": "MIT", "_id": "acorn@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "b55f2d582e38233ddec31de8ee2bdfd5b5e92824", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.2.tgz", "integrity": "sha512-8LK7ztPKlr9lYHoGX5LW72i5apTfll50SbY8HlgqIGWPThZ6C2AT/5DkmyztR/S9wjKOOM969Ql+u9hKmJYfLA==", "signatures": [{"sig": "MEYCIQChu7mBliHaTW2ov2gQDO6NGIK+VJfFFUWtOyyxim694QIhAJRZerAKzRGmluF2wTY13pNGWoGN0xDhh7K+xMcpVASb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "b55f2d582e38233ddec31de8ee2bdfd5b5e92824", "engines": {"node": ">=0.4.0"}, "gitHead": "9e804620b73746ab4b8f182f3c674df24c4ec4da", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.3.0", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.2.tgz_1473622441369_0.776710340520367", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.3": {"name": "acorn", "version": "4.0.3", "license": "MIT", "_id": "acorn@4.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "1a3e850b428e73ba6b09d1cc527f5aaad4d03ef1", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.3.tgz", "integrity": "sha512-ezz9ine4mmscgFcLME0GLpGi7j9SGgiqGYjaHbG7aymdPH3Zdn5Dkgvv39ErCk+XMfUW/+Pptt00Xv9vjhntjQ==", "signatures": [{"sig": "MEYCIQD+PPBKitjeovdqfqqD7uGTwCowyKFumMHnw5QQ/OktZAIhAPQhRfMWnn1n2zARAyVRxWiN4lGkamLw+QUrABt3z1rf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "1a3e850b428e73ba6b09d1cc527f5aaad4d03ef1", "engines": {"node": ">=0.4.0"}, "gitHead": "bb54adcdbceef01997a9549732139ca8f53a4e28", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.3.0", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.3.tgz_1474012883070_0.7297828732989728", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.4": {"name": "acorn", "version": "4.0.4", "license": "MIT", "_id": "acorn@4.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "17a8d6a7a6c4ef538b814ec9abac2779293bf30a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.4.tgz", "integrity": "sha512-q2rPPDFLTHr/KffXaU4UGvi4/a7LWaYGVJFqvjIIRyzqaUiH66bdLEs1UeSxrexjAWLH6gNb3HfFaPRvY8HFSw==", "signatures": [{"sig": "MEQCIG4RBhb5z3KMWMt+Kr8fGBVY/DrL6pFCDqB6m7kBssSNAiBMZbf5Kfs5nDQdrJUbrUEybcy4N1wAI0PwuPJzIcGaew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "17a8d6a7a6c4ef538b814ec9abac2779293bf30a", "engines": {"node": ">=0.4.0"}, "gitHead": "e7001cad79b4b0d7c4a6cf569ea33bfc808183cd", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.4.tgz_1482157987722_0.9571468001231551", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.5": {"name": "acorn", "version": "4.0.5", "license": "MIT", "_id": "acorn@4.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "d5c76ef581c59748047067e2fc96302b1333c132", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.5.tgz", "integrity": "sha512-BSlE7J7TiltPhZorZIpWGLKjsAZkyorWpUIY/kCRkpOEB1gcsuEvyGoeeLxBjq9yVTiH78z6oC+giGNcF6Foow==", "signatures": [{"sig": "MEUCIQC8S6R9kpRWd+bXF7V9VSi5Yyw/AlRzIruu0IAVNpiMTwIgIeblMKsFtBGmi86ZF41NJ3wpB3+2VsWwfaMGrcIqQPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "d5c76ef581c59748047067e2fc96302b1333c132", "engines": {"node": ">=0.4.0"}, "gitHead": "03a0941815187a68b06cc7549076bcec40df3c7c", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.5.tgz_1486071746879_0.021485510980710387", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.6": {"name": "acorn", "version": "4.0.6", "license": "MIT", "_id": "acorn@4.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "b76d0a1b8adf6ef809de1e3005b2c1b94e027103", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.6.tgz", "integrity": "sha512-RlpcN2UI38SA9qBNde/wgPLGzenm/FGfky2C6loL6DE+MF4CFM7hbyQX1dYe7Bc+D/9iMyejMT+5gqQA8ENoig==", "signatures": [{"sig": "MEUCIFq3OXPeS/tCSh4iJaPzRGlbJ26gS1JLuh+3lwVZY+/DAiEAiH/Sc7zxHQPEjF3B1/6h4mBzFeH2kWsqsH8JF3G7Dv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "b76d0a1b8adf6ef809de1e3005b2c1b94e027103", "engines": {"node": ">=0.4.0"}, "gitHead": "c5433e30f4c24adb8a83ccc584e5760516665b72", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.6.tgz_1486078596606_0.879464068915695", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.7": {"name": "acorn", "version": "4.0.7", "license": "MIT", "_id": "acorn@4.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "084596cd4636f796d2273990c699e13fe4092ac8", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.7.tgz", "integrity": "sha512-3u6WRTY9Z+BrkLGFabKqnDLE5RE7zjTcWkqODubPUx6ruJMQLSlHF4qHWr0Sm/dk22utRbSbTTheW7o/p3IUMw==", "signatures": [{"sig": "MEUCIHlEvQwxhE7iW151DH3MaFYvYKD4kjfXLqN4qwQjd5qCAiEAsYPQr3BizjfxJPEjWJl9p8WKvrjAQD/VKJXX9j+t/jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "084596cd4636f796d2273990c699e13fe4092ac8", "engines": {"node": ">=0.4.0"}, "gitHead": "0b71d219a58ca09e5f4b05daaaa2542186926dc3", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.7.tgz_1486079609766_0.2424702092539519", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.8": {"name": "acorn", "version": "4.0.8", "license": "MIT", "_id": "acorn@4.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "f41e52020ce78118a3c68ed0e9215eb8fc68b5b1", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.8.tgz", "integrity": "sha512-TbWXvyj22upq/mXNPEZfLkku5SN8pyokdV31UmEO+7IleRqojOoC4zKYEnMu+TtSau+uToOgSp0YY26HY32Y+Q==", "signatures": [{"sig": "MEQCIFoHvgvNmiB8zWY9V55Y1xXbDT9t5jY+4Ge0LHYVZVVdAiBwdYFD+vgDMrJM16mkE0B2qwGkh+EQStCRjiUAWe5D4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "f41e52020ce78118a3c68ed0e9215eb8fc68b5b1", "engines": {"node": ">=0.4.0"}, "gitHead": "482cf6ffa1c7f13d9cd9ccc48aaf8be5c1b2e95c", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.8.tgz_1486111362650_0.2338384878821671", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.9": {"name": "acorn", "version": "4.0.9", "license": "MIT", "_id": "acorn@4.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "2d2eb458fe3f0e31062d56cf0b1839c5dc7bd288", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.9.tgz", "integrity": "sha512-ery9woHRTFOIdUTy+pgA3O/aU46HDNwhECX9oaDVwhGaSQ2/BZv2o4yqdbmNyMFUAjVk+Eh+pta3s2t89RCTAg==", "signatures": [{"sig": "MEUCIBOqA9Tzdioh+Uw1b0nFOilhaLdRpyK4AVBUIqXjyHi7AiEA/cETHIbCS+y+Vs9DDLE/fpkWBIJ0T9x3mipFJO5CGmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "2d2eb458fe3f0e31062d56cf0b1839c5dc7bd288", "engines": {"node": ">=0.4.0"}, "gitHead": "c3dbe6d1a99a7eebc624a0458ebc3ad9947e0dd3", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.9.tgz_1486377164964_0.9220353260170668", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.10": {"name": "acorn", "version": "4.0.10", "license": "MIT", "_id": "acorn@4.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "598ed8bdd4de8b5a7a7fa2f6d2188ebbf9b1f12c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.10.tgz", "integrity": "sha512-T72JP5v02JBUKDe8RNII29TZo5bVVTeJuaFYbmR2He5sNAQx4WUEf9hx0J7eDlgQusxmAXEPbq8wVzoykWKZ+w==", "signatures": [{"sig": "MEQCIAN9Tl3couBZ0UAQvWedXppgSbPjhSRGF41ux3LRFm6tAiBYd7wL43NuxKd2KogiL0UalVniyAXNeW4a9MBe1EKV7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "598ed8bdd4de8b5a7a7fa2f6d2188ebbf9b1f12c", "engines": {"node": ">=0.4.0"}, "gitHead": "e975ebdd4898362e4b4242a6503e2582acd32f95", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.10.tgz_1486457985455_0.2294631632976234", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.11": {"name": "acorn", "version": "4.0.11", "license": "MIT", "_id": "acorn@4.0.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "edcda3bd937e7556410d42ed5860f67399c794c0", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.11.tgz", "integrity": "sha512-dneKcCksU4vVWWS6zPPrLy6LIdpJO4mX2D+nqEK+jUAfP9yOmdoYPLakSNbbLRkHbuFJFiTZvLrgLH0MXpwiYw==", "signatures": [{"sig": "MEQCIBPDHEaYJuuesLu9daQr3gILPxNyJXJo6+lZvbi4vuW6AiA2/YwxuS6lxXSH2SZS6A8M+tnb2AoKwQ7z7CPkItvPTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "edcda3bd937e7556410d42ed5860f67399c794c0", "engines": {"node": ">=0.4.0"}, "gitHead": "a7359dbaed1f2a5ba204e82522ad5c6a3d1fe367", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.11.tgz_1486476084634_0.26453727134503424", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.0": {"name": "acorn", "version": "5.0.0", "license": "MIT", "_id": "acorn@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "fdeb148500dcb749853db6ce9e7b2fcdb4574331", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.0.0.tgz", "integrity": "sha512-whFJvJ4lrP1OCoF6XE/ugwNGI+uhvU8bawo2nAv2SxT3MQq7GMTJtpuX+YOf1bmzep1xMkBfamEjvzfnDsGTVA==", "signatures": [{"sig": "MEQCIFjtWg9e7NM951ByQaHopBxD832dyJG7UgKeDU6tCeCWAiAvIaWbN58FyT9aEd+cOLyERcbYPdyrRk7QUIRu+LDaqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "fdeb148500dcb749853db6ce9e7b2fcdb4574331", "engines": {"node": ">=0.4.0"}, "gitHead": "a3980aee90705b0cbc7a3e4f3130e41552f4b590", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.0.0.tgz_1490702298230_0.6628579343669116", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.1": {"name": "acorn", "version": "5.0.1", "license": "MIT", "_id": "acorn@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "7c1e71a0afb99ed144fd44eb57dcef246c923c01", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.0.1.tgz", "integrity": "sha512-eXPuMVY5drYSa9T9VS0yz9Q3xzilWjI0x8cXeYUGBSax0QLWuHjVfDj8PcFy59lVWQEDEImHhqT0PJwTk16aBA==", "signatures": [{"sig": "MEUCIBwn4HF7ReMVy/yqEg0hP9LGmnM5kBGIqAyK/+7JVfELAiEAwh5Ly3Ox07F1CE4KB/4aEC1mwcfZZgLm+v7UcnyD6u4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "7c1e71a0afb99ed144fd44eb57dcef246c923c01", "engines": {"node": ">=0.4.0"}, "gitHead": "a29e1583e5487c2b34a54a292c751e1e6901a390", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.0.1.tgz_1490854433847_0.28430336667224765", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.2": {"name": "acorn", "version": "5.0.2", "license": "MIT", "_id": "acorn@5.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "dc1c8fb907f64db2ab573de2326b73527c24de36", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.0.2.tgz", "integrity": "sha512-NsjKZ2HY6vGmF7twLQGJKvuyFZSOMVSE6JY0/pIFYw6juWwQVWGkufnBnU4qNIBCLWazDsIUwii/Uy3mFV+EEw==", "signatures": [{"sig": "MEYCIQD0uxnxjaogiA/zQtr9Uj/yjtA/riEXvJ0vz3ITPJ189wIhAP8sqXBFDuiv3FXFZTKCVVPz7D1Yxxs/dZjkTJlsXtyT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "dc1c8fb907f64db2ab573de2326b73527c24de36", "engines": {"node": ">=0.4.0"}, "gitHead": "aee6008ed9efc62fe106213b7a408e00ffc846ef", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.0.2.tgz_1490859537894_0.29313385277055204", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.3": {"name": "acorn", "version": "5.0.3", "license": "MIT", "_id": "acorn@5.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "c460df08491463f028ccb82eab3730bf01087b3d", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.0.3.tgz", "integrity": "sha512-Bg8ZrM3YfY12mPZkONS5uKZsTj9ctIduab+rkfIibEdWeVaZt37HeqsXPf+7ekOECE7NxOOa4VxuZKSkTGo8Tw==", "signatures": [{"sig": "MEUCIQCa44dJxwVcjDxj0RvYm7FCr4DwgTxF+LzDek0cKwKyBQIgZk4zPMDu4Y4IOUeaLO66oB8ZGfoMbI7OkoLYaUWqDJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "c460df08491463f028ccb82eab3730bf01087b3d", "engines": {"node": ">=0.4.0"}, "gitHead": "dc2a033831e0813496bdb558c70b9fdf4720f048", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.0.3.tgz_1491067098969_0.3370379332918674", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.13": {"name": "acorn", "version": "4.0.13", "license": "MIT", "_id": "acorn@4.0.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "105495ae5361d697bd195c825192e1ad7f253787", "tarball": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha512-fu2ygVGuMmlzG8ZeRJ0bvR41nsAkxxhbyk8bZ1SS521Z7vmgJFTQQlfz/Mp/nJexGBz+v8sC9bM6+lNgskt4Ug==", "signatures": [{"sig": "MEYCIQDX7nHCC2c6jT6mCwcoFI1FTr0xZMZAnMPf3mdD6m50FAIhAN7h/K9Vp+c2ftf+tQkWHTrnm3KFDQtOxl6jIERqJK7m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "_from": ".", "_shasum": "105495ae5361d697bd195c825192e1ad7f253787", "engines": {"node": ">=0.4.0"}, "gitHead": "a7359dbaed1f2a5ba204e82522ad5c6a3d1fe367", "scripts": {"test": "node test/run.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "pretest": "npm run build", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "prepublish": "npm test", "build:loose": "rollup -c rollup/config.loose.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "ECMAScript parser", "directories": {}, "jsnext:main": "dist/acorn.es.js", "_nodeVersion": "6.9.1", "devDependencies": {"rollup": "^0.34.1", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-4.0.13.tgz_1495621718785_0.37868548510596156", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "acorn", "version": "5.1.0", "license": "MIT", "_id": "acorn@5.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "e468bf609b0672700e02f878ae2f1b360fe24b4f", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.1.0.tgz", "integrity": "sha512-WXZ0VTJT8EE25BmZjc+wr0qIwG7QaEna9csPKHS6WQp8gDo4V376wUWi222LXRiuAF6CAS4Ejv736DdRwuPK9g==", "signatures": [{"sig": "MEYCIQCcAtDqxT7KleI/wyUKqqBNLEhI5eUmHZCxdxkv9Esu+AIhANY4u2vgpadgXUrUTRlg7CpN3LERTmkxM2YiwkMSmMko", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "17bb365a8cae1e002c03671eb3726ea3506e5c23", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.43.0", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.15.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.1.0.tgz_1499267621149_0.5898419332224876", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "acorn", "version": "5.1.1", "license": "MIT", "_id": "acorn@5.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "53fe161111f912ab999ee887a90a0bc52822fd75", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.1.1.tgz", "integrity": "sha512-vOk6uEMctu0vQrvuSqFdJyqj1Q0S5VTDL79qtjo+DhRr+1mmaD+tluFSCZqhvi/JUhXSzoZN2BhtstaPEeE8cw==", "signatures": [{"sig": "MEYCIQD9ES0Q+q3DbfOqsmhXOJ6BSJ1xd7XiNMvT5atKv+XD/QIhAKZttIcKpLn2J/JevAGCl459IAw+jsTCzQwpsX8HNsww", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "8d80b2dd9166495dab85f5b7029d899dce252c82", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.43.0", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.15.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.1.1.tgz_1499327622251_0.7539563917089254", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "acorn", "version": "5.1.2", "license": "MIT", "_id": "acorn@5.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "911cb53e036807cf0fa778dc5d370fbd864246d7", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.1.2.tgz", "integrity": "sha512-o96FZLJBPY1lvTuJylGA9Bk3t/GKPPJG8H0ydQQl01crzwJgspa4AEIq/pVTXigmK0PHVQhiAtn8WMBLL9D2WA==", "signatures": [{"sig": "MEYCIQDbxFE0fsFqLXqd3DAzLSr5RZNJcr6gcS4BUERT5UIa3wIhAPcrzGpURnurVnMxJG7mEzk/5OP9Q4IrfiNDdD09OqkQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "9ae70b727426c2f9e9b5aec6cdaaed3657b5ea3a", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "5.4.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.43.0", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.15.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.1.2.tgz_1504512485232_0.36428429861553013", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "acorn", "version": "5.2.0", "license": "MIT", "_id": "acorn@5.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "39fd0cf9d2dd4c82068602a404019d8ed5167b1c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.2.0.tgz", "integrity": "sha512-TJT3XkJ12+1vDj24PN5KO5rbUr34UzETv0ZJ4jLBy1IUhQFBb89xpGq9CgovdJfogOuwpFteHyl0jib4ElODzA==", "signatures": [{"sig": "MEYCIQDBaL8GCeGtdsZ9DPpoH3dz9zEiDrFs/zRfWFRSvPGa+AIhALRzLH8XYXFOFCrPum3plr2FKjmd8SM9K8NP8pyuG0Tg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "d54b3a5926631f159078ef7c47a1a3072827964a", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.43.0", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.15.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.2.0.tgz_1509270107870_0.8590125523041934", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "acorn", "version": "5.2.1", "license": "MIT", "_id": "acorn@5.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/ternjs/acorn", "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "317ac7821826c22c702d66189ab8359675f135d7", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.2.1.tgz", "integrity": "sha512-jG0u7c4Ly+3QkkW18V+NRDN+4bWHdln30NL1ZL2AvFZZmQe/BfopYCtghCKKVBUSetZ4QKcyA0pY6/4Gw8Pv8w==", "signatures": [{"sig": "MEUCIQCdeJYbDVVjdhha8nWr/4CO9OqYy9Ul+RJHyDHa15FBnQIgXaneEmkWFNRv9OV85jESBdXKSslzZhyC/mV//PoNakM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "9924efe268e899665daeeaded9bf452ff1cb510b", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ternjs/acorn.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"eslint": "^3.18.0", "rollup": "^0.43.0", "unicode-9.0.0": "^0.7.0", "rollup-plugin-buble": "^0.15.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^7.1.0", "eslint-plugin-standard": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.2.1.tgz_1509295079758_0.15344050084240735", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "acorn", "version": "5.3.0", "license": "MIT", "_id": "acorn@5.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "7446d39459c54fb49a80e6ee6478149b940ec822", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.3.0.tgz", "integrity": "sha512-Yej+zOJ1Dm/IMZzzj78OntP/r3zHEaKcyNoU2lAaxPtrseM6rF0xwqoz5Q5ysAiED9hTjI2hgtvLXitlCN1/Ug==", "signatures": [{"sig": "MEUCIQDW1/89BenJdno9zEZm/9KEq12us5R4V/rm8rRE3rts4QIgTnUzpwb879iDvEk+m3AAGdTO8c2VGEOkbcVO84jhm84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "621f8691a840a4677e3153a3c06f0b7ecf9b8d46", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#51553973738063f457e248f7f1e643c561c8a64c", "unicode-9.0.0": "^0.7.0", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.2.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.3.0.tgz_1514469548212_0.19219086412340403", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "acorn", "version": "5.4.0", "license": "MIT", "_id": "acorn@5.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "51c581c9d4b1943dda59a3e73bcf02661ac727ea", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.4.0.tgz", "integrity": "sha512-bkLTrtPfRASTxDXFaih7SbeYSsQ8MjrqCQKMrgZ4Hc7kYI//WVU6rDTAIqVrAudjgMFQEGthYfodtaw8dTRJrg==", "signatures": [{"sig": "MEYCIQCXhXjtvgdTAx9JMEqZC6gpxd0hPWPIzgncTBFZ3//PNAIhAIKJxaCTE/dWe30f4nivBHMLIyPBdzJg7oG/m9Y/1mDl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "9a3bb727e60587e759797548167328f429a56e33", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#51553973738063f457e248f7f1e643c561c8a64c", "unicode-9.0.0": "^0.7.0", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.2.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.4.0.tgz_1517520658261_0.2607122315093875", "host": "s3://npm-registry-packages"}}, "5.4.1": {"name": "acorn", "version": "5.4.1", "license": "MIT", "_id": "acorn@5.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "fdc58d9d17f4a4e98d102ded826a9b9759125102", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.4.1.tgz", "integrity": "sha512-XLmq3H/BVvW6/GbxKryGxWORz1ebilSsUDlyC27bXhWGWAZWkGwS6FLHjOlwFXNFoWFQEO/Df4u0YYd0K3BQgQ==", "signatures": [{"sig": "MEUCICMjLk4j02FpwZq5itUEcT17YIspC8TWpW2GNVgxmkLuAiEAwwzvoxu324SDRAM2huDDVxNyQjieIRu7ywXKtoG1S3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "662efffd69ab380ed0a692cc390cbc2987d41e14", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#51553973738063f457e248f7f1e643c561c8a64c", "unicode-9.0.0": "^0.7.0", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.2.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn-5.4.1.tgz_1517563159856_0.6037265080958605", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "acorn", "version": "5.5.0", "license": "MIT", "_id": "acorn@5.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "1abb587fbf051f94e3de20e6b26ef910b1828298", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.5.0.tgz", "fileCount": 16, "integrity": "sha512-arn53F07VXmls4o4pUhSzBa4fvaagPRe7AVZ8l7NHxFWUie2DsuFSBMMNAkgzRlOhEhzAnxeKyaWVzOH4xqp/g==", "signatures": [{"sig": "MEYCIQDIXRMId2nUdohX7/n7N8LLIYDdYIc/sYwvPyAnTx+PJwIhANv1h+vBmUOBEnGSxDyBYJs1xqdhzInB2lxBI+Trohaf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 558636}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "5364b99d85cb7cdead682ee940c51671a1e91026", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "9.5.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#18c1e799a01cc976695983b61e225ce7959bdd91", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.3.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.5.0_1519717586435_0.830956183693476", "host": "s3://npm-registry-packages"}}, "5.5.1": {"name": "acorn", "version": "5.5.1", "license": "MIT", "_id": "acorn@5.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "84e05a9ea0acbe131227da50301e62464dc9c1d8", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.5.1.tgz", "fileCount": 16, "integrity": "sha512-D/KGiCpM/VOtTMDS+wfjywEth926WUrArrzYov4N4SI7t+3y8747dPpCmmAvrm/Z3ygqMHnyPxvYYO0yTdn/nQ==", "signatures": [{"sig": "MEQCIEQHA6FUeAot8CFl4sG1GSrz3bo0PkChTKsKI/bEwf3FAiAQbjhkmg1wpDfNW8/ag4WpXKwjc+9OOPUf6Nwxa61jag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 559169}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "28a0cf42bf57f6d949bce2f238921b5dab3ad3a3", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "9.5.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#18c1e799a01cc976695983b61e225ce7959bdd91", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.3.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.5.1_1520335707982_0.4903561404595085", "host": "s3://npm-registry-packages"}}, "5.5.2": {"name": "acorn", "version": "5.5.2", "license": "MIT", "_id": "acorn@5.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "51e0c3d18558c2bd1bd14e74e9150646a8eb3826", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.5.2.tgz", "fileCount": 16, "integrity": "sha512-L0NyFma7Pr4Cy16aAVF2pEQsgwLdRQNk6HuMRHUFy0fPZWc4w20Xqwq7d1JkKg84EHOmLGkkbjGK+1eVwSOWdA==", "signatures": [{"sig": "MEYCIQCzbZaN7si21GVQ4McIA6ijAWsFbRR12UpSNoGag6ublgIhAKrqgxodj5DdOaRpCxYr7rjFEonCp3U6vzvUZAditCvS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 559326}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "701f36a598ce30dae104cd517f99340c3dceda89", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "9.5.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#18c1e799a01cc976695983b61e225ce7959bdd91", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.3.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.5.2_1520500920091_0.6954846108716117", "host": "s3://npm-registry-packages"}}, "5.5.3": {"name": "acorn", "version": "5.5.3", "license": "MIT", "_id": "acorn@5.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "f473dd47e0277a08e28e9bec5aeeb04751f0b8c9", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.5.3.tgz", "fileCount": 17, "integrity": "sha512-jd5MkIUlbbmb07nXH0DT3y7rDVtkzDi4XZOUVWAer8ajmF/DTSSbl5oNFyDOl/OXA33Bl79+ypHhl2pN20VeOQ==", "signatures": [{"sig": "MEUCIANEFdOZzJtGyzXOJcdjqHxqPVGMfnCS9L0TnVOFXkAvAiEAumS/Hi7s92ITIPmFSsmf5W+S7VNrYHg4dc/fc16vh8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562561}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "5da6f356e93e30892c0fbe4d0c024897d9071e89", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "9.5.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#18c1e799a01cc976695983b61e225ce7959bdd91", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.3.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.5.3_1520501718112_0.4612627870062018", "host": "s3://npm-registry-packages"}}, "5.6.0": {"name": "acorn", "version": "5.6.0", "license": "MIT", "_id": "acorn@5.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "572bedb377a1c61b7a289e72b8c5cfeb7baaf0bf", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.6.0.tgz", "fileCount": 16, "integrity": "sha512-QatFQ4C0n+PLqemyC6zXEv04tSqRR0hRqe+uGKPEVgKe2G8kl8wJvHzRYWwz6vqqEqt6idPVMFojZ4P1zlyAzQ==", "signatures": [{"sig": "MEYCIQDmX8i1uFoEWXc/75De2siYfZDaVaEf2XgRovM++dCGXgIhAKIYLTeNqBHnur+Tt5zj5LiX1F5YTSWSuevM0r5OWzSe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD7Q6CRA9TVsSAnZWagAAhs8P/1vz+ZzUSxnFk25mtF6u\n25JOrBEPj6+5RJfZL8OvjmeijLoffktiCUV/7126J2veYwQgvg972GMaWFdT\n2lqJo4XNEoqMAMRT9/Qa2isdbxaPHvtyC0+B2knFeLfBxh4qDa/6qncvsNFv\n+MKDanHal6Tr3l1PmprF6rBzTTKqXqqc0EDQt9XfgEUE3a5NVMX1jsyN0Qjr\nKkLccgSH54+L0L8v1XGYVRjB2HEc4PfJQqhNWQgEeVrPgrI+85rcfuWEAhwm\n7hRuVgs4LUSxq7giPj8VaIHqXf1zUAyAHgHmHRAmCT5Leao3uJAMqkbnbVMh\nU/lEMGMpaYuM+Stqe937DzQ8TILZoq2yFlKMlax7f2GwqXqv8A5dWTF/Ei0F\nYsQ9HaqihT09uyJSCSn7n+WPmrmL7JFKPJir62Kdeu6amPW2gOTrmrMQ52B1\nEfx40KTTCRMwU4SYXFoVKjgp4taOhaZTMQX3aW8T1Hu3pr510hSYgS29s/FO\n8bzNfFy3nbfdlXvfrXId3U+OQTH457lf3Tn5gg10jRBGjOt2BYVFjnER9+qL\n9/PdqodpkRh+zv07AlX+AuZs8aFu9EKYTL4agII4ohClRo1u9UEOLVDB8YTQ\nJ7O8DbyPXtdMKXkDxU/8cUuS5tvCMU7Az+qWIZBHOfOg6nZfMazhtP0ZKVbC\ndEtI\r\n=OlJ5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "6e4192ffeb554ee21b938de5ab445d18d320b54a", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.0.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.6.0_1527755832769_0.7983023136465626", "host": "s3://npm-registry-packages"}}, "5.6.1": {"name": "acorn", "version": "5.6.1", "license": "MIT", "_id": "acorn@5.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "c9e50c3e3717cf897f1b071ceadbb543bbc0a8d4", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.6.1.tgz", "fileCount": 16, "integrity": "sha512-XH4o5BK5jmw9PzSGK7mNf+/xV+mPxQxGZoeC36OVsJZYV77JAG9NnI7T90hoUpI/C1TOfXWTvugRdZ9ZR3iE2Q==", "signatures": [{"sig": "MEYCIQD+S6ADeF4xvosdAJKWf+UmyAjII5FO+NIxAXN7QZvb9gIhAJ3DN8CczRN101NrAdvXLucLy073Z42o/lYwCeusGZmX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEPp/CRA9TVsSAnZWagAAjM4P/0dyHAOtDDMQ8ww07KM2\nDTAOKuDqVVZhgQFHyenCkyqTzJGqeI7YPtMShqCfPHRfYae5JSw/CdTsZ133\nw61i9ZCUsCc/9dDlQ34ziDFTy/c8revVDfYSD69y+KilN4GqGrnUlnlFt1c6\n8Zzi3x6RplopgDppPeXUfI3WErXhMAa8M+RKKNbEie7JFDevc+KcrDkLa/cM\nJN9EmNKLaOJd8r1Ela6CvlYgocv6YS7o9Y3thrUGFaXbDUFkz6kzfbQAP0/X\nHjGGPRN+hzcfTtq25RCpV7840hRqVl/RMzIv+f4+D1f7CR471K/714WtnJKf\ntVFNnr18a/NtRaCGbcBym7iV+C9XyfrcE8k3+kSOd7BT1IUU6YcH84H83TNE\n9tfbkANGJTbxHZcbtx91LmuM+t+MM/EycQiLpTF5ouecsafq0G50ZwYgwO6T\nCSHFB6AZUJD6ECCkK9pBE8MFSpCtEwKe9NMdlyUm6jLNDp3Flqbn+oh4InzG\neYjuRBh+cj039MgoIpg1RndKPBEuXdk13Scooyitwx6Kz8pG/6SnGAq1ahsP\n4xsgc3DWK1OQ1gcKe7efuTiww/2mHzt5MISYhNApKMX/ygwryEhVHte3Rku0\nCiX+/pq+srIK0XtrTsm4ET9Tizvx+EnEXgzsvjeGmw9x3YyKpE0P3d/phep8\nbdVO\r\n=XLM/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "690cfc78b38ff7f4914dccf3f50b0b95a2bcb550", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.0.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.6.1_1527839357747_0.6800429104111891", "host": "s3://npm-registry-packages"}}, "5.6.2": {"name": "acorn", "version": "5.6.2", "license": "MIT", "_id": "acorn@5.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "b1da1d7be2ac1b4a327fb9eab851702c5045b4e7", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.6.2.tgz", "fileCount": 16, "integrity": "sha512-zUzo1E5dI2Ey8+82egfnttyMlMZ2y0D8xOCO3PNPPlYXpl8NZvF6Qk9L9BEtJs+43FqEmfBViDqc5d1ckRDguw==", "signatures": [{"sig": "MEUCICr4wh4y23MlzJOfgXzfpo/FY8e/ka20ItdLhdfipb7NAiEA15tc3oAHrBklk5cJWNkkdyl2ToBIxTJrHxQadYVJMeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFjBfCRA9TVsSAnZWagAAbgoQAKCykpMEv0BX3cbx7+gS\nm5RsIJappiLrYxJ5mZr3RIiaVIy0kt+ALwPccUauIdfVf/vjojCPeW5EbxMp\nICMGNxoPTs2nfRwwts2KFZK7GD3Nn/X43/uhNHItzJYb3x6hH/s+mRTIm0YV\nHE4DjsM/CI0omPeCvpcfeewbIFOPAsP4FRpn23Zb4Q/rPyyBDzqa4j75kbgv\nO+PrFj3k6tQhGhoOtYOk19PqXeP8P0dV0NEqR3Ao14JCX5VkCVvCNr+SjOI0\n0MKJ1F0VHyqfdKNVhkHTvou8CuacoKAZ0WqZJmHfqhIVox3xnbbw3MVZPHFy\nQ9cVYHeBmSnZFKmmmyGfT0FzCm2alNq4f+baumFfMjgxB//A1t1wMaTzddH7\n0VfbV6MZZLuxd6kJFbEsaeosKRMqe+tAQjRdorA5GUcZFYnU6n5NXtvr2AtX\n6k1OF3sD9gaa0QsLi1vkL2NvXwObWYavS+Ic8IuvlOZvSbXpvf76ggBAB2go\n+P9KPzF+SDkXjV3wy3Uz9MJmiFLBjYX6qOH2TwHA3M2p4b1O38kS8f6+lhHH\nlVn9vKFDO507ASAZ8SB4sy1D/nvTTCWQk1e1K8qRwwI0jkHid51ai4nlfrum\nAVgjgBQ1uBSrrqGp+caKSkstTrCskUyxrP/XRINM19SLiXhAz52Bbhl8hVtu\nDy55\r\n=5o0q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "3f97744c7f8dafe43e4d9a548f058906bd4f3783", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-10.0.0": "^0.7.5", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.6.2_1528180830348_0.4228347625078508", "host": "s3://npm-registry-packages"}}, "5.7.0": {"name": "acorn", "version": "5.7.0", "license": "MIT", "_id": "acorn@5.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "03d135486e648e870799b4d26c82b21f2ad21531", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.7.0.tgz", "fileCount": 16, "integrity": "sha512-a+5wMaSIZcV5BD26vrSXQntElNyh+VXfLmwMn7TKtvwu6pvg/84j58Irbwr9VFvCoVWxJMquw1ZApmf4pHRxUw==", "signatures": [{"sig": "MEQCIG9WDiicVDkdpwSpybtLcwOXgKpszF4VMqV/0/HL7zpmAiAO7AexDVD3R++XRffY5U1VzgBBuS4FxeKkDuuGgDD3SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 563399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbI5XhCRA9TVsSAnZWagAAR2UP/2iewftiCZC4y8do5J4R\ngAXPHivqwAuK4rxLQHjm+ljNZPf85AveYIGUJXBGBu1qN1jf3vDqEe5q4vxw\n5zWyIjf9BDh3gBjIVZ7fi+68MNpN6PLoWEAlGUmhRJlYNPF01M+pI+JsJfaf\nST302DzWcc+3pGUgfEem+SKdsF9FWBtAmUaqmdDTyIzGzFD/Q+AoETE7oqvS\n9rdC4k8/2/V9xzgn/m/H0jISBQ7kl5yEhRwVGqBk+jWQX2W4T5by8NbN1gmV\n29FdvyV0umpm8qkZjkHDsvbpnJGyiScM5BPYgOdtDDm+PjimdDgI0eiq3afE\nG1ePpqnzoE6J3RU7rTqwIGEe9Axw62WGVbQrhTwA9Pi/i9wFXaMl0Vg5XDFu\nWvsEh8KFPQKu1c2MWqKp/rm79Ham6NsTmwFDtrbjPIdtMCGK4D/i+54sTk7L\nzqREbXK3tv+CzYl7/v+TeX3y1ibMF1NDT4rLaSo+GbDcDvmSUGeMX5dj+g6P\nod2elgRjruD8Y+tb8QW5cFjz42nzbu8r+GTueSEI12C9FHswkC69DilP8WQb\nKO5PMSH7cVlYh4t9qQPECY33T7aaz/v46kPeOQl9qwerrbHKzu6L7Zpvqud0\nj3qaNyi1lfsNzrXvHY3RYHv2dwv3FW8puQbk16IiRHX8sSOHlS1YuLKklA5j\nVL+o\r\n=8RfY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "bc1b6dd93e8dde6311de5714029faef78220a83e", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm test", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-11.0.0": "^0.7.7", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.7.0_1529058784136_0.5850277886026853", "host": "s3://npm-registry-packages"}}, "5.7.1": {"name": "acorn", "version": "5.7.1", "license": "MIT", "_id": "acorn@5.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "f095829297706a7c9776958c0afc8930a9b9d9d8", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.7.1.tgz", "fileCount": 16, "integrity": "sha512-d+nbxBUGKg7Arpsvbnlq61mc12ek3EY8EQldM3GPAhWJ1UVxC6TDGbIvUMNU6obBX3i1+ptCIzV4vq0gFPEGVQ==", "signatures": [{"sig": "MEQCIFHzrFV1t8NKR65zEkJSoc03HE7u1WPOX/YOAjrfOQ+jAiBN/T+EMHfVOVgOYCYVUQcdu+1IikQ6SD4+DBBSPE83nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 563675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbI6RSCRA9TVsSAnZWagAAv7oP/31l3wZmIR2V8stbtp7N\nOJR5q1llx81Bxz+wA/G96LXyI1vZyJEubStOrHz+gYctUQmdUfHFgANgdkU1\nWclUfLOLWe9/cN0to6kaP1/9T6NjgDVuYgsQlFxjFsphV8Q0NjfHcjUwqVi+\njCUw4F5JCrL/EFT9/lpsJquZog3B2igLEmC7VOjiW8vivlSZVvcjF2KZM27N\nvOhEpGYB5aKoyWR/3uqHfm31NxLuFBFGfuXPAtehIKQfrqvR9iK40DzJpCT5\nwJa9JbD2Y4FyTKWyxyGHwLsD9/ZJA8Q/hdjNrOGU6SsVjKY0feotdqxtbZm6\nm6SkG2uZjchryVv/iDfYj8SujofBs4LTqQtbb8LaUbPYIjv/JA+xbXGB+gE5\nwhnYW64shJZhCEplR6hxSxy7sgiin3gWt1tD+7xGU7ScheUKHQGXCPB5jwWM\n9XvfwUA0rh7AHuoNqbKuG8xfscSeczil76dmG4y0Yx6X5zRQATTIOZy2boav\ncdPJ9QVYPszg0Subc8Db3bFfmFFbSMdCa6zHy7pSzaSw7r6UY20086YWNleU\nUUClJBM8+mxQohuJ0Ena95QdYrqN0FhxgKQCrrtxVxZxeGbSirK/5wP1Py27\nyVnRe1+vvQhTLS9KYHvczFZnWIfVj3RfV7jHjzFJ4ARomLLv8P0k0LTTBlDf\nubB1\r\n=OoOd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "18c5af71d82ad36c051aaf77e9e5b9742a0e9610", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm run build && node test/run.js && node test/lint.js", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-11.0.0": "^0.7.7", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.7.1_1529062481132_0.854620714012416", "host": "s3://npm-registry-packages"}}, "5.7.2": {"name": "acorn", "version": "5.7.2", "license": "MIT", "_id": "acorn@5.7.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "91fa871883485d06708800318404e72bfb26dcc5", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.7.2.tgz", "fileCount": 16, "integrity": "sha512-cJrKCNcr2kv8dlDnbw+JPUGjHZzo4myaxOLmpOX8a+rgX94YeTcTMv/LFJUSByRpc+i4GgVnnhLxvMu/2Y+rqw==", "signatures": [{"sig": "MEQCIBd8TqSSBsipa5yxH0NYa0Sp5QS3bTPXVVaxF2ke/OOFAiBLVuxnkM3tUMdnFfsU5aqTBpRTVyQXQ3PDRmj78gyg4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 563970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbf6bKCRA9TVsSAnZWagAAIU4QAJv3Tc3X4bcHLXiNPKMJ\nfRyO+GK1BIcgIXHjRcIJMRxnl3ODtePwzRNysdGvXTnW8KaGab+HKoSTislJ\nRWGQheBn+kC1eLtl+SHz04xMZJpjKt2VbqlaZQTGyjv/rSWXeS5Qto628aqK\nLKjikz2dtjukN58e9YCl9Xi7OMblb9Oh1wo/hpNj8WmXd9XtOoT75AUwQ6K1\ntDcZBnnqSXI11u157TwFtAJdjKS+mSjuo9B+DvqOpQzO5tEPthDZ3/VdOeOK\n0tzwZ0BoY/u8ShGd7E0qpEcVmR7mh/gh1JFTmot093WCt5+zN0K3SYzSC5Yo\nJgqOfIsFug7czrlisxUu3hxMpxtCnanpUyk3n9KdHm3pqC9AUJRWLzg/x92o\nrWPQXB+NHlGrLx4Zj8y3Ya7jFdVL72iXCfexmSLp0O3wtAAf7t4HWUYDo1LH\nR9a4fo8EQWBMULdiDgoDJSCossy2UmpdW8ZAJORIcxlHV0Rk4YcrbTQ+Ixrj\nq3m/mXwIbDlRfmmmLLjUcwxeD2Es2P/JsAW3Hma6Z5EeLEftyZDFBoVnScnU\nNaqtTe0Y8Yp6ZMyTxmqfRCCp8NwaMAROGRQoo8AU4RmabH41OROsZhzTGUIb\nTOY77kkN/rx3YF97fSTiO6Nl/vhtqWnhTCaWzWwXHSG0bbedSeEtbrOFFL6a\nhe/B\r\n=tUZw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "95ca55c7863fafd8bf6d446a0098325388ff9f1c", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm run build && node test/run.js && node test/lint.js", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-11.0.0": "^0.7.7", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.7.2_1535092426214_0.20528101974459734", "host": "s3://npm-registry-packages"}}, "5.7.3": {"name": "acorn", "version": "5.7.3", "license": "MIT", "_id": "acorn@5.7.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "67aa231bf8812974b85235a96771eb6bd07ea279", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.7.3.tgz", "fileCount": 16, "integrity": "sha512-T/zvzYRfbVojPWahDsE5evJdHb3oJoQfFbsrKM7w5Zcs++Tr257tia3BmMP8XYVjp1S9RZXQMh7gao96BlqZOw==", "signatures": [{"sig": "MEYCIQCfvb5uhmnRcsR+zxuiD8z+bvtfpyqSoTIhVxkcihGREwIhAPPx4G36DYGxt9AszNb7VeJQRfTgs5VuvmioBewzCfP4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbljrlCRA9TVsSAnZWagAAK1MP/1SHm0CFiM1p/mMYmAtt\noxnzXtM2/9CBG6lAop3o1aHRf6tlNOabKbpAwPxOX0MexURC/GDpPL3bPl2a\njMBfBnn3ZOTyTxoY6gZXKoJlaxldzOMc9gs9a38qWv8fjx1bGu5XSBbPyI5I\nJZ7nnnAgUxiO6GsqzGVKyOzff3ZUBNUoZ5nXe5fGZ2/DkhLYPjfVoyPYIsEr\nXx+LabIRDtEqbBKji5jMdwurBvO+j1PyXbrepI8mTTRqmWtWRo2+nuGVQzwN\n9xAqMKtM/D1eaL6XsD9XL6czDhgi/qMRWIy2cnwVa9hAS+XqsS5OMmSpmb/P\nVLMjK/DbJJ5GD0SjLBYqoNQSLYTm3XKnEPFDd3LPFHKvqJh92dpMuAGUi3SO\nDsE72lnqg7MFYaG8FKzOH/hnBLWzCXGkoZWy3DKj+hUpPvVC4FNIV8xPm/xU\neEbuHf5YZ6wwHbn2U+leB8dNBhMtsvPlA0+zSDWKsDSc5uuHKlqDBgJvoyXb\nzyZv0Vd4GaXqgOVUqY1gS0vBWjPI9+fdMI/5KCuORvwwPVuyJT3i/XHjpjfR\nl3vktm/Vwe29hcB4Mu585gzYOEruPfW65Nb16g0psS5ZDN1ceQkYDXkB1EOZ\nBdZMyIwwQznXpf/NUVrieHpFvZSCSeMAAs2sH0Kl8F5b4nIviKAebZvDsLQM\nGvZD\r\n=5BQG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "910e62bbda199ce7acc5de10d374afa0f6fcf7d6", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm run build && node test/run.js && node test/lint.js", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-11.0.0": "^0.7.7", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.7.3_1536572132101_0.3886095569427317", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "acorn", "version": "6.0.0", "license": "MIT", "_id": "acorn@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "913d61a3a93dd1638ebe3033395083192ee0ae73", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.0.tgz", "fileCount": 11, "integrity": "sha512-vvZ8PwswGTM15ZXyk3I+SvpTm8UbF8iRnGiU/f9TPU6By7K1XTvfvusFtoQt0WYycudFSYW2lrJDivhBlGovvQ==", "signatures": [{"sig": "MEUCIQDYUSXf3uLyar6GIVCAiQv0y12tEJNh+J88YOjf27PB2wIgX6OA3zMqTuPbze+oJnkx5Q1NvtNfclCV+9Yl7CPaVGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1075313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm1+LCRA9TVsSAnZWagAAeewP/RfNLxvkfOt0iJaydxiv\nldlhpMY/wDwsEtoqJICcYenIyoAEUvWQ4wgPOo55xFpRzp5W1TLPNRe6X8iR\nQGKwaq9g1wShByqDAEwHuXkzKq0kqTzRPzfqc4+26eGOVzQ73Jwz1o2RWXJj\ncLiig4KK5+K6LgFQA8Y8I/ONMUazb2VUuWL8vO37Wi8qvBrCKMGAlAjod75c\ncqVt8nGbw3af6CYBZqrYSE3Du1GBQoh+NZvfbdmJ7de14DE5ZSknzl3Jp5/t\nl3v2jEbHr93S80QNi1IKQcQJ2P+TTbp+cuVExHZ6BskRSHMOeseVxhnmppPK\n/eKrdsBRwxPB9O7nLA6C74t33rBY7KwUuMR669+1Es9vsbiogsHdqEkKYoPi\nLJ+sGttWYejMszP97QCNWsf98erCkNxtz3JByJSVkhWlU1X8tnqMlJQLaC/r\nvy8dragw2nuX7ecQlR2miS9IvvG6DZ5GpIrNqpfTY9n6O0n61UvPj6r46rss\nA6HkwHhaIpf2v6oP/vRxubrAGZO30toE6FFhy78vc0fXRacfMKd5zfAQmp69\nKsfOd9RyAjQfdFST1FqU3Gnr3ll0Y5UMTM2X/E1wIyVTleacWPFNWHADFfIl\nk/NZ1R2GSbwfXoiL14f1a/ztm/viznSlWH9YMElrKIJyuvOC7vkJJeIHaA63\nZXno\r\n=JELf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.0_1536909194829_0.6153469080698375", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "acorn", "version": "6.0.1", "license": "MIT", "_id": "acorn@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "66e6147e1027704479dc6d9b20d884c572db3cc1", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.1.tgz", "fileCount": 11, "integrity": "sha512-SiwgrRuRD2D1R6qjwwoopKcCTkmmIWjy1M15Wv+Nk/7VUsBad4P8GOPft2t6coDZG0TuR5dq9o1v0g8wo7F6+A==", "signatures": [{"sig": "MEUCIQDAfC2R1AVqLFkhe/rUPdKzUXqfZk0/lPrJ7lDm1UKuJwIgL1TEsdnIC+ZJ2ZEbsoERsDTw5vnqMHt6+BMqbMlJLzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1075389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm2CDCRA9TVsSAnZWagAATzoP/1ZABbbg03iArTyh8LM9\nrvtdqsl2dkQteYFSvis9ISzGxlhUmCdfk/UL9kfncJ13gTQiQ+5XHX3Qhwc7\nsqpaJPkeXIoofwqUjAXtp1ONZ84Aw1qMvhS0WsfliL3/V7aTtYtDMJanbDCv\nVMp5gVA1uQ/yDzCIuBpuUgZ27iK++H13OJyC6YqmZJLNFvwYsPudlQreFZbX\nTQKAmKDvlR5mgWAanbPQKBQj1ZxTOqoCdOo0+ejmU7bofsn86pzrjmR+t1/K\nkIqN3ZtD/ZYmTMdj8ZMnnGcf5J8b9KtCGJwxJFqSkKRf+Z2/W8MaWaBIS0wO\n03WG/z7UZ4xZf6Ynljs9wJ8cTCUYhFKFVQ+yESQW7gKp5hNOjr3y1jdTTH+a\ndvdEWMDLmKyZAikxkIPPbpIZpJm7VoRlzvpZhQuV3ZkKoqTGyJVIJp50QWRJ\nnwBCzAkHIUj4/cnCn74txvWN4GV0O959J4f3YRh6LqGFV97OvNONDwBeiJiy\nkMQ+qof/WHZlQ1uP0BKcLeEl/5nVVLCRR0JumCyukL43rFqR5DO2ps/wLLZA\nuaeE2rs8aa9K4fDPOLUTvdssGmCPoaalSHXq7d5Dbmp9pS/zxe/UU+A36WpE\nXcnbEKFDEmHcK+rNDI2Ro/FFyYI6qM1a3ZlUy8Xf44pJ93J/PFG+bSEsMJr0\nEte/\r\n=628w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.1_1536909442434_0.7351835551387282", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "acorn", "version": "6.0.2", "license": "MIT", "_id": "acorn@6.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "6a459041c320ab17592c6317abbfdf4bbaa98ca4", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.2.tgz", "fileCount": 11, "integrity": "sha512-GXmKIvbrN3TV7aVqAzVFaMW8F8wzVX7voEBRO3bDA64+EX37YSayggRJP5Xig6HYHBkWKpFg9W5gg6orklubhg==", "signatures": [{"sig": "MEQCIFZ6lHTw5LVnUaVTi+cSVfWwPM2IXrkfDbTOV4WBkVzMAiALf8e+wfzlSCeeTsmph1MDMyKx93zryOBHjO6s5TpJSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1076793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbqyoZCRA9TVsSAnZWagAA62AP/AxY4tKJKBI860Dxmpao\nNEJqkrp8AzURP/ZO3JicxKCVUALSUPap0tt6IdihxINr48KVFTyA4qWMxitX\nz5FLC/mY+EuOmatrz7UdgBGT15mDazWH6FpYTCuslzIN9zjKaJC1Ml3VDeFc\nBMLztTfPk0KtE4Y2GICIQDOUOo9c8iwqTSTHgekN/t/LiD6POQLD2Wwv42BM\npWAevfpVlpau4ljmh50saP7y2ZGq8itA4o2lXPppv3Ivm5TQup+Ks/nDg5pJ\ntN8mxla9WJWYTO3wFqWOO19t6Y7vHfEdeuCNGBAuKHQkT8ppJPGfwGuIw/5z\neoTXdAKmgxICpJT2W1M1XJUrygah+WDaHdaCEZVECYbAuVnUmlh/HeXkVrUF\njw4ozUQzRC9baceXgk2qlrGI3slxMPhZ27IMYDqYcWixTZ1gMM2Vj+EOALw6\nYfvQ9oDEFBenOV1ne7r3FzDYzXytDURraEWspTWMq3rVq0FpFjAWVIO7ihn/\nzUZEjp5y+9rhbZCP2N4jcEXEvdjgv5QBzqX3p6b7l1VvPUg7kqJsVRDmZuN8\nPEofHwUete78q1Rxx47IK03swjTQgiyD4FaEnLncX/Wi3/i5Rdlft57Ug1UL\ndxbOD5XDZ4VhAZdGF0YiYptVkRcF2p3bW/rpnby0m5HXfbbEOyI+FKMlbolK\n+Ffp\r\n=1Ttj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.2_1537944088949_0.2802094177136971", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "acorn", "version": "6.0.3", "license": "MIT", "_id": "acorn@6.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "52adffeb02757f06420085a0ed0f7c0baf55fdf4", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.3.tgz", "fileCount": 11, "integrity": "sha512-xEnlTS2J0PKuub0pd2Y4W58iEo1sfRZ3h23E8AKmlnV8Nc6E/syRdVeo0DMuLSrgRJZHnFeDou2llXfB+wb1/A==", "signatures": [{"sig": "MEUCIQDZmKO4MXCZzfN/aWdUKATXPD1uzwO6P4/ccff97QoZjQIgB1iLZYS4svWJNjuNx8kOzi3607f/2ahR8vq7E8+IS84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1082795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3vmJCRA9TVsSAnZWagAAdloP/1C4uZ2JiCt9HXQhRM97\nakBfuTdDfOz8J2Bx0XTsPtp1P3mxVwD6CmHeXs6dgYtqflxfF8OBPIwWeWEo\nhnxmTqayY2hKmGd5Ag12ijhAyAo+KpmSfrXXlhnfPtVK3WKCGNFLBKpxZ3If\nhBqls0UP5ve3SILgIY85LyvfDP0+Fai/8omSCA7Bo6BCDi724NSajV+6MuNr\nyNB4uoe6Sut0W+wzxCu6++15AIqQE7FcR9IDqvBeVxRaTv8q3Oi6Lk4UtcYE\nb6CDDAO5oJhTH+B69H3UFgJTf0HljMvg5Bl85TrxnOaCk2EE8082W33KAeBo\nzKOypMyqhKvfQqNUfPJpvn1VPmVIUeRR9cvNt1iWn4Q0blpXDiI3HI8uvfnC\nhAbFb/ZfA83m+ubzg0cViVdAwPKGnQ5n8X6OKFY+5QG3pgZuF2iHrVgUypg/\nErpgh6bt9ND8g7YftK1eOLzzl9MT98L8Xuzvj5hzoUaoqOQSxke7SQ3Ovh+y\n4aNbQmNwN3Ayrq8eLGbOOEPA3y09n5W1Ew3QaMI8hv9s8pMf16Fc5SdlUDqg\nY+B6sGpkTCbgQpRzgZ0QycIKq4eiUznItCAVHqjgoduRl8NhxAlrYBNpwOJz\nE5culqFUNGcaB/mNcEC/W0TQtPp9bB0wv5AbIKLwmKtjcYc8slvHIJp8Fgfi\nttae\r\n=jEtU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.3_1541339528857_0.5343282313726032", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "acorn", "version": "6.0.4", "license": "MIT", "_id": "acorn@6.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "77377e7353b72ec5104550aa2d2097a2fd40b754", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.4.tgz", "fileCount": 11, "integrity": "sha512-VY4i5EKSKkofY2I+6QLTbTTN/UvEQPCo6eiwzzSaSWfpaDhOmStMCMod6wmuPciNq+XS0faCglFu2lHZpdHUtg==", "signatures": [{"sig": "MEUCIEYPMw89136GusF3+8SRdmeQqCB11fqwdfZTVsZ105NDAiEAku3djpmplQmBj5l9GVViKpSm1EY3rMqjFBLjnvcGpdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1084020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4Ab2CRA9TVsSAnZWagAAkaMP/1bVwPdjTNng8q57cDnt\nOT1e8bAfnX3wBgVcZjdlEe2oGejZ0inMyplloLpH9NoP6Cbp4zZ8rURNtXpu\nvN/B5Fid0bQY0384sUYtNpuTLTAqCYv+lbmOuhLEByR1tEYywcK7dIn2VF4R\nr185gj1UaDATL7221OwiOc/VyYFM2c6VrsHUoiFOBytAJgYGuVIIibcvT3X0\nVZYURcXz1cLmvACwhml3qGEfhxyovLNWCPLwxCxtdCfZXEs9HDJAiA9kT8rs\ngOEycxSff3C5E+ilNzqsxjslRusCXpJ8E1prqndkvPdQcsu0C/eHwXOxZldu\nXj79uaIeeNHDqeWJbrHezD0tcqKppPCXtbbcjcqL0YrJwXgueZexGvuhWXu8\nyRB/YHKciyufG8QHMBh5PxgEj9yaU/KzatXObLvb+mJw4Bd8Ndw0rrUE4APt\nEnJIqeTZBDXtLKf3ILFEH+2f2HmXmIDHpIV+n5xs8qo1ekp0biuHz7eDL2Mq\nzWgqByoxhrbvQrfAcN5i9iKUwl7fbLb26ChQmCuNt/FpyLurZSI3JsQt+kCM\nnGT4JNT8UqgnL++Lm/StrqSWS9sJG7Bbg3Nvg2qQeFHLrF2WrKicZ0A0V4QF\nsFk07YPVSiFcsZ78ffhioxiRc8eYpz/pzcEcKnuRzXmAgWS3kXxUlegnMq4C\nIuCb\r\n=7Qce\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.4_1541408501772_0.10250883394023069", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "acorn", "version": "6.0.5", "license": "MIT", "_id": "acorn@6.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "81730c0815f3f3b34d8efa95cb7430965f4d887a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.5.tgz", "fileCount": 11, "integrity": "sha512-i33Zgp3XWtmZBMNvCr4azvOFeWVw1Rk6p3hfi3LUDvIFraOMywb1kAtrbi+med14m4Xfpqm3zRZMT+c0FNE7kg==", "signatures": [{"sig": "MEUCIQCUHyQoVf31qcDk8MGiELvii+QaCdu/Yx5zpWSswW6aRgIgPErInHpKBG2vXTxyOaaIhbv9vTL8zWW5UxBmlgWBgjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1084980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLKThCRA9TVsSAnZWagAAPdMQAJRvN+u8N+Brr9vEHhxL\nhhCFbe+WC8MA0OzCC7eYj9H8cP+HbmL9gXXteyl/OvKT8TL19JiHgrE2wREV\nte054iG3K7xmciPuJw9KYHTrxcQMYRBJdbFgRspikJqfcZTXI0bXSh7K7d6S\nYG4rQEt38J2Gxsz53acstm8/6P/qU2clUODmsJdUMmVJwW4UdZ6yDVOmrxfV\ncs8rQTUkamCsBaNcDfQNqnqoc9aigXuSCFNR6650XOAZMnYvEhX+vOgNBD49\ny1WNHmswvETnei2+aWENY<PERSON>jVBmKLSdKiCpmgB5S5VLhcHzB2/3dAxP+5mCA\n+fYp3PGdnilWKB/TYsgwe+odI8k2P5UTXqCVeQsHApS1Gz8xmRv7f2olh24D\n14YBwwZpZLOoC8DDyelcK1uJ9eHpv7wRD2961GSVN9Zm64tYXWm24472PFBk\nBFRwNgTG3qrKsEB4WbfY63wkHkomJtQby7EVMtRJaDVU561pYmKOXFhuNeXO\nvbKrfsxM1cOcdWpZDf4+Tl20ud6D+/YmxnwVBwsqVb+qHRZC52Sfse4McBUg\nkKS48IquB/U95vXqYhKnmB+PmV2ONR7X4z8h8GhQXQwnWvm2NMH2OIe+VN55\ne4j9F0sYYQ3zMhCbiz1BXT+2U0aAqCPbDXfn7xnEWHiLhXRVkQ4I89PisOQM\n72uj\r\n=PbvZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.5_1546429664768_0.2904693881325544", "host": "s3://npm-registry-packages"}}, "6.0.6": {"name": "acorn", "version": "6.0.6", "license": "MIT", "_id": "acorn@6.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "cd75181670d5b99bdb1b1c993941d3a239ab1f56", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.6.tgz", "fileCount": 11, "integrity": "sha512-5M3G/A4uBSMIlfJ+h9W125vJvPFH/zirISsW5qfxF5YzEvXJCtolLoQvM5yZft0DvMcUrPGKPOlgEu55I6iUtA==", "signatures": [{"sig": "MEQCIHa5qGdXBfbu6Ybrx6L1wFs74kSK92vKsZnKpdaZ/OgyAiBaTHieLPg53MtoxbCXPx0uxC7G8fBk1M5MgqU8OQeyvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1097912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcUWPHCRA9TVsSAnZWagAAZh8P/AlT2QBpie5u7BucmoWC\nbHR6/m98zIDWaAyZWPfzCdJxEFf5nskpvlbs4LYPs/XlCAtRuWtIeNv5Ek/Q\nuaQK9rAAiva5Gp4i2XktcAVnMNO+IEdSS8BuxX9yMtTT6UmcjrHfDEXlEhD8\nbiScOZw1b9Bx2tcdqZc9PsntdtiG3Ojig39TDUdvyjJ+9PK8axABbi9Kg+Y/\np4uNPXKXfJmKP2+CCtZ69Ryz7FKBn0j2LBQhyIdZFT3GDZ1BIydp+AI7C6hE\na0daOPxZOkHYjv8g14fq03qRTGamYBqPiminBouoAvjL88mPb4zGFElt6n8G\nt/OmSMFKOqCZPaSqFh2w5hEpU21UOcQ3BmydB6ATl59VaX6x0rHkTmo9QjuY\nxA7N4RdDgTMFrkMVeLr9j+9dxdRxTpkw0rvq3IceBtuYIqY27GvaZVZbgKJb\nhRaDaASiGYQeCnUEw3UppLEFfUss3Zgw6nYuqc1CladXy3kDMC59c2ChgQKC\np9V3SmAwIVrv2sBq+kjObetzFGTgzJ7PSdFBc9Jm1ExNQqT8y2iwdT5u/U1G\nCWqczKhO4Jz4d00H6BvlENAyDbAaDkje6hhsIyj1leRV82zoJFFznqwB5SOo\nltLLXC4t6wnsRdxud7CjfR0nO3i9jLCr7nx8zOniNnR3CDzc5wkQEwDVeoO5\nx/3K\r\n=S1bp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.6_1548837831054_0.6359716466383694", "host": "s3://npm-registry-packages"}}, "6.0.7": {"name": "acorn", "version": "6.0.7", "license": "MIT", "_id": "acorn@6.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "490180ce18337270232d9488a44be83d9afb7fd3", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.0.7.tgz", "fileCount": 11, "integrity": "sha512-HNJNgE60C9eOTgn974Tlp3dpLZdUr+SoxxDwPaY9J/kDNOLQTkaDgwBUXAF4SSsrAwD9RpdxuHK/EbuF+W9Ahw==", "signatures": [{"sig": "MEUCIQDosEGaJNjK3KRY/kwgvLfEscHhKW9OWsTXQVnaguXviwIgHVALBdNLUJRdoKR6UxMaaeuIHYi4ojj32X/elLVAuLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1092078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWAiXCRA9TVsSAnZWagAAthEQAKHeK6KdgswXRWLLgQPD\nLuMksuUYAtrh886df1lf4+egGi09icv7vxG43BRyrdG0XbYDuerr35hl6lQ3\n0UnPdNfphsonVm76iBNs5yQIMSfABopIPuS9T5cHbbxVsKVKK5VyqZHqO53N\nEfXt5ssFxtFYjmiIzW/4o5+HnWlUNipAVEBeSvnslu2zrdLwU16/hh6Tuaah\nvIbSq7i4pwtV5RMUl0+YKy0i4bQP/w/ZDmbvvh2hIkGJ3Up+SSPV782YXDzR\nSwnGynIin67wohjt/iIpn1HbXnm6r82UjV2OaulfI6RZycXSDL/GTlfBNBVU\nGMJQoCzs6mq11M6tw6JuPFJ42bDXNtMqxlpr1rtYcUh87Gv6JmQFKuRqV3/W\nP/voUGhb0YuB4H5PaJQxt/fT5hAEdnI3SXNfrqHpes4cWMf99Fdbi9X61AOV\nXDtezdem7LqKa+ydpBSzNmb5s3WVEtdbBBJJm2oJrY8SDrPZdEppP8VqT73J\nEVhZBD93tIm55yu9k806cXGjiyo+V+IkS614dfEH6l+g/+YUnFkkjVN93dYo\n5o0vzKdKroEpHWFAMHrpbtL5qbcvYmEadgA6bdyX2Xi1OsQiOQ00C6Va0oka\nYGdd5TxgYjatHSFne9nLQJD5sJ3R04A/aqSoJRR7tIavoLg6SquM1s3uz0RX\nQPyF\r\n=Z3SQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.0.7_1549273238643_0.21581088786153213", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "acorn", "version": "6.1.0", "license": "MIT", "_id": "acorn@6.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "b0a3be31752c97a0f7013c5f4903b71a05db6818", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.1.0.tgz", "fileCount": 11, "integrity": "sha512-MW/FjM+IvU9CgBzjO3UIPCE2pyEwUsoFl+VGdczOPEdxfGFjuKny/gN54mOuX7Qxmb9Rg9MCn2oKiSUeW+pjrw==", "signatures": [{"sig": "MEUCIHWm7xHHAojHLT/SmsfsEmXhLrdBavnM6v7QrIBtsFBuAiEAvMxGplEhVKtVpAYnoAOxkwLT4HbHnu0odBDLUwCbLGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXUbOCRA9TVsSAnZWagAA3WcP/28NW3794F8Q4qPULeV6\nRWBCM0bd04gahgU0OG11r0MApaYydCQgLompKcPhJUDmORATrnraSB1GMr99\nyyeiEcFyk+p+MMeWNwkS8wZ3ODzkzdG3PrCQEdLDqMGVse+PNXlry+LRSbQV\nUhh4uauIlAPxypV9XNkAYk3BgI7QIOQ+BKCkZ2Vn9cKEeAxmIth2hKR0FTb5\nv4jxuHRLikm6kPwdgnvJrosXPxxJ6s/Lmx8xf1lo/wK+xRcGr8T4G7LSsjOc\n7WYqIRJEDbYuAA9wucyLvzpo0xueotYzDLcz+ffoFB/KGPzyrqUj5oO9Iw3O\ng3jwTDPLSPF2ajATR8Jv+qvXi9n3Wi5F8+mAFDFundzugCZoBGvuTnxixUNa\nc+VEUpR3532ayDiy3MKlirVcrD6L7ANC6vOys4ezpc0xqOoig/Tni9HHK3Rm\nUopIj4doW5R406AVyMQL3SElil4T6sZv9Rs+mrmaRA3rkJkBYhqtbqvg4njA\nXsXcbK1X9QHAekC0oVXklLW/9+sKPWoihBucJe/LFEwWyQxxdvzwMFUIPbuH\nG3c4H5UhNt3A+owiN4A+zwpmeIWLUJ5AzEVlKf3j30R7RwinPALeGxjl9ybo\nXHmkuUVB87XMUTTd4E9qnQHUFcmGdSlR4L58kuP3U4mE64cwAghVS3e5g7/J\nP7tj\r\n=4ExC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.1.0_1549616845845_0.9141495228970564", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "acorn", "version": "6.1.1", "license": "MIT", "_id": "acorn@6.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "7d25ae05bb8ad1f9b699108e1094ecd7884adc1f", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.1.1.tgz", "fileCount": 11, "integrity": "sha512-jPTiwtOxaHNaAPg/dmrJ/beuzLRnXtB0kQPQ8JpotKJgTB6rX6c8mlf315941pyjBSaPg8NHXS9fhP4u17DpGA==", "signatures": [{"sig": "MEYCIQDmvJcoxhRKYjHN9leaYHZVXMaU+nGf0YixTsEJPtQx6QIhAP151GmCbO62tul75xdf8A2s6qGpJ+7zbrfdp1JEMthW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdjg+CRA9TVsSAnZWagAAC+MP/2SQsjq8ZT2iE9YbQk1a\n8deLcHgSIHNtxMe0mEguVCQDYpxSHlO8WWrFRSKc/Dkt15+48+QxLX7okDeF\nFEwsw5Ig3uRKtybi59nQGevinPptiXQKGkNqXcxM+kl+XHu7GOdLj/678ipO\n7GmkZP0Icn5YYo/ylm9Cw2kBMjyFaaLC+x2ZRT/J8IyqSZBLIdtysBwyU3Sb\no7DQxc2mUXACTRb2ojE9/hXyKAA63S093bSowzdFafsnoBK/w7HURpKaRSlW\nAHSAkj8yhrEpIW6QGzVAdOFeHwhrLrfPredZ8imDJ660FCvUrsxLhVMWVOYc\nRKkBB4luy0O8/zRjiG6PFqP5VqrRro8YBrhYXgJH850flHgabgLCxl+AoTjA\n8I5/yiA80BMApYJaDn/mgjj+TM0cMmLeSLFCL5lNolvYpDa7kD3RYIqP/DyB\nVyz0aO6PVeE/Z3CNurA6UjmggIApEzzYd0re+3qb4qBRnhHiklSNjfHL1wHc\nZLi2fki9cvLIwc3krpgFbid+x/QXlQVOgL62O4XAfks0V/XuyHQNoPsURtBA\nBwh3PYuxG2e/Gzcb4FH0QqH8focaJlwNozH7bzP/NcpvErKFsHV6l3omeZ+n\nyP8Yufnks565tYwjOl+eCQ2AYtr+IupOJqvv346/EpJ/kRQwlGDwJhTbUVtS\nX2oa\r\n=6nWg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.1.1_1551251518361_0.6006501128068276", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "acorn", "version": "6.2.0", "license": "MIT", "_id": "acorn@6.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "67f0da2fc339d6cfb5d6fb244fd449f33cd8bbe3", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.2.0.tgz", "fileCount": 11, "integrity": "sha512-8oe72N3WPMjA+2zVG71Ia0nXZ8DpQH+QyyHO+p06jT8eg8FGG3FbcUIi8KziHlAfheJQZeoqbvq1mQSQHXKYLw==", "signatures": [{"sig": "MEYCIQCyLwShFE9zmFNri/tbtJLfisZ9mWDLiinunT5/uThYDAIhAOD/YQcVNA/eMTOkE+ylPb4VijmehX8mUxMQeSrZF1L3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1101068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHZoeCRA9TVsSAnZWagAACg0QAIkzx1iLle/hLCQMoVCk\nyIoaXVKSP4F94Gc/RDeWDZG6KLDYpVOTmGqGSDCp+GEKrq0jn2sF7JEmCP9W\nplHLEH2IhRdLSyDOVcsbPFP5PYa1A4jZ2E9/BLoKADgf2ObL8wRJ67AUpoE2\nkQ74IdSGoa1PzIC+94D1v8ur476taU7xd0lv26mDzW75hme+4wl9AR3OlDqW\nfSeHQG37BImyJviHkV3XM1ZOgHmfoR/64JozViiaizjtGddsF+g43tewbcU1\nR007FOxJYCNLm5P8Css7/0X+IJlqmFfmbzkwFmbfgLtFazhsaA73nffoNpyk\nnX7iwQtePyxzRlVQPd+EtXzYLEHJ9Pw7coB1ySfxOpx+V03LpTFtt3jGMnPR\nYQr5a96Hw3gwHSFtaVzilPiyDJmYCKeo8qVjyV3RqxnAF7562WH1Lze6cDUd\nkTzPgRBgplmGUx+DYFyZsbqzoDfur200fRzO5izDwN5/Ul1E1rfTQaKRViFQ\nK9lQyHpw9tL0yDOrEM4H0XBEWaAMc+9yBtSh1/rERfIkt1MnzREatcmUpZCZ\nlcqiSJvACpY7JT6GJdpaHpl0X75hUE2jMUGWWUe5OPZU5Ud+QevupcHvBjlA\n6VZiXNlwjcMgisCflrAwPmupEpiLJ1L+8fJKog/IicWd9SKwDQe6uZAPcEYg\ncoJQ\r\n=VIQ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.2.0_1562221085482_0.4827654225641429", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "acorn", "version": "6.2.1", "license": "MIT", "_id": "acorn@6.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "3ed8422d6dec09e6121cc7a843ca86a330a86b51", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.2.1.tgz", "fileCount": 11, "integrity": "sha512-JD0xT5FCRDNyjDda3Lrg/IxFscp9q4tiYtxE1/nOzlKCk7hIRuYjhq1kCNkbPjMRMZuFq20HNQn1I9k8Oj0E+Q==", "signatures": [{"sig": "MEUCIEp8JkL7KZYzU9bt0MEDx/ze/kfRm5cwGL77fcwfUiXeAiEAxJJ8qm4ozfrI0uNb9Kuwa7qhtV3qTdR5RN5w8r5qwkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1101957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMyWGCRA9TVsSAnZWagAAft8P/iSF8Za/8StuIYngjdY+\nOL4Uu1fLVU0WIsb2FrVyOp196XxlXM1iI4zZHqzyB/cd1ItZgI9/RJaOKcEZ\nhbwNOq/XnzgSbmOxQ8GKidL0FNY4To0OVg7YKnBIKKSuPn0Q53qXUioW/WXp\n7xVl3TGJ7OAUMnIdSwBmcZ0euDhIM54HpoCRFNdJySttejryQ3kn/BVCQ0V6\nO2AWiaqTAeYfxeW9lh9L3hn8fPVxI3dBaiUTCbda9SOc3Irvw0l4E1+eCyQc\nie/J+By47SPnYB4A27DcwJsNuZRq2Rfps1bVBAChTCKbGcWLvF2ZUWJdzGtK\nS6YuHmDTKTc6YRxfeXMZTjgqPrVL+r1bi6kcDWC6cN3aijMOTZ9d50sH0Id7\nZc1H48uuL4FMNRtgFKIWfHmNywkaf7DO62jBm4Yh4TQNk/95CoNiKapFdVrE\n/zrGgabAYl8nBfPMJ/mSxIJCAbJHRbWrI7gEkMFGcClLpZu3tjU//IA0z6fd\nZurYnuCJv4ZKmsd20BGIJ29OhOpHwKEFBqEYZDfRUpBcZE2JSDtWNMq3eQPD\nBSpeH+2xbgNEi1IxgC2YbaRkKNG54VJT/QTD9X/cmxga60aIagXpSfJo8CF4\n4xZvnK4+42tOhjCL9v03OEvY7FthywV2DdZyXCOWzZY/3fSisAkSYMiMIWPr\nEaoZ\r\n=vhGl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.2.1_1563633029905_0.4120628992889064", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "acorn", "version": "6.3.0", "license": "MIT", "_id": "acorn@6.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "0087509119ffa4fc0a0041d1e93a417e68cb856e", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.3.0.tgz", "fileCount": 11, "integrity": "sha512-/czfa8BwS88b9gWQVhc8eknunSA2DoJpJyTQkhheIf5E48u1N0R4q/YxxsAeqRrmK9TQ/uYfgLDfZo91UlANIA==", "signatures": [{"sig": "MEUCIEqWF1HZUqmvvWKOMXklD1aeqtqAqmXkny2NvjRFB1TYAiEA3tnnde9bIAi9cWhcio7rP0wqtkLZlmd95Pha+DyJuXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1102462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUTRoCRA9TVsSAnZWagAA1CYP/1ZRKpEj2Gb6r3zpLPZM\nIIgMjHUvXSazqjV38VryqUcBLLsyAXSNua6J+272toCl07OqxVxJsvcnULOG\nXwMg8NdEidSe/Y9DCwGFH/Q8Kjr5zmHW+ORWyvRAa1XoxvJQm6X9SY52xQPR\ncDe1KIh6MfuO7NHdFeZ5nZ9MH3GVs3+RPXKrY8yT7NRfFgdePZJ8gxpa0ums\nPH/fpZ8hyKTtgoUoHbChd4snaeA5cdQyvFQ4h5PLwTDjwo7ePh/M0RqIKTCP\nchkPitwjz4HVygSZNS5zIgaw1x/Rg2JG1831AFDhiukYs3z3bfGVYL33s3/X\n0dX16Z0rrs6QTekr209LK9wiXTYbEStCbKeP9X3UEE9mDgcaLjpQFNQu2EsO\n0CxqrhebagELnz/Kspc5Fiw3JP6qJJhj/PFk7ubb02KsQTFDUIADbSdhclrJ\nLVuQjrXMnSA2Q75V4xN4iYB+L6bpulrc0RN5jVkpP8CNBFUASiJJxs5zPy36\nsfXzslCx2eV6D4/Q7k6p7z8yUHR/Yu0psyn9F7b0llJ79YOa9bm6DkngEZTI\nv3CjQlk3XmztwSlXlglzvN8z4OuZ7xHRc896vW0SE/ydtCr5OJtneIXgTpoz\nNtulhmux5bpDsnoYfnSJuDSVyZikHyBzZdfHfDzdvGx9KtQihh2EJgRk1Vxn\nK5G/\r\n=pgSb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.3.0_1565602919824_0.8427641499026255", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "acorn", "version": "7.0.0", "license": "MIT", "_id": "acorn@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "26b8d1cd9a9b700350b71c0905546f64d1284e7a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-PaF/MduxijYYt7unVGRuds1vBC9bFxbNf+VWqhOClfdgy7RlVkQqt610ig1/yxTgsDIfW1cWDel5EBbOy3jdtQ==", "signatures": [{"sig": "MEQCIA/i+D+TGBGZ6QgFI3M19YI9nwAMaTl4mhni7UWWDqoMAiBvSXfVXCFa1Z2umM+8YS6u7fnnH8NbeGKHj84g7beB3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1102988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUm3WCRA9TVsSAnZWagAAmQYQAIqp0CAQMovN9MUSsmyq\n1rUnOKN67FpxwcHUZ/pk3IjXAekBuZylgy6EPKuLrE+G9h6CFDHJj25S+sOw\nnmJ9ZYoCy0Ublur2bARq+NLQ/KXQw79F7Ugy4YAFElvhpiyZBEVeMjmCrtHj\nNwFgi/mDwlocdoOX5gfU6TIT1fVb9CWYntlJx9uF5JfhvqzCx4slosdbI0zV\nkK8wTFkffOyQEUvMYV3w+X2BTJ4C0v24gmAiMKs15Wf1VQtPmYq5ALTPzqom\nQJFBZdEBOQfeNdbX5ZRFuH4WazkI/mGNpOvkwK6v7ro4EXaWuUMOPbqESnkb\n2bavnJEUr0zIOg7bgY04N5/EFXBwA+gdRuRtBNfB2rzdxX+RIemYUgWCw9L6\n8SQ04iONxi/MqhONw5IynlnrpfPKKVTJ0jn6j067ki8aIqqLC7a+Nfr71DXp\namHMSS8qpRVy2KjCzamIgTvFhEVKxYSRnbuFkGgNtrYlkh9MLjyHjy9hswfw\n0iMo4SL9qJGTX7l56KJ9PkmxoKNmX0NQfcNNxSNitywpR99jHAWr8RaAHZTF\nzWrB5D0Wjeoq5jXXU9xsEvnNFeRf9dlfNqRh+TbYJUpwmtJcnHe6cxZrcVON\n7eiBFCtIWwyoe/m0dUWPS/8enMOGjygd0tVgVziZDWc0RqOyxtnf1l//yd/1\nRsw/\r\n=trSF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.0.0_1565683158111_0.9265227127346505", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "acorn", "version": "7.1.0", "license": "MIT", "_id": "acorn@7.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "949d36f2c292535da602283586c2477c57eb2d6c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.1.0.tgz", "fileCount": 11, "integrity": "sha512-kL5CuoXA/dgxlBbVrflsflzQ3PAas7RYZB52NOm/6839iVYJgKMJ3cQJD+t2i5+qFa8h3MDpEOJiS64E8JLnSQ==", "signatures": [{"sig": "MEUCIQCVxrj8Et5khch+p0vJTBEBaS9gn/JA54vY7F/iQgzSZwIgc83L1RT4PZOs3RYPHIjMQeR8hj4s+Od1zR730VuN/50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1104477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdibjuCRA9TVsSAnZWagAAmQgQAIkJPo9i+wdBLLZY8yjT\nHI82RsWU5OvIQxhE6H9qLiEfF57ZSn3LfKVugsEocc84loCiJkkd7vsGsidJ\nRMbkEIK2ENjH1VcUHdHq8PnA/HgnMgHNq6nGg+h6aXLsN/H5SFdyvWedoLXw\n5bwhLbfURV/00bx2himrt/RBNL8SxfowdpQ/ps5+mAWUzLTf7D2+PchYktve\nRJ1lSwpshrgu4AxUdbRmqDEH7+Wm5SutB/EMkQPxQV/sJviFzB8qqFlQopJS\n83RuvIhQb7ZNUQeh5A1+pQocFU+yd227m9tAr69UeES8vinI+0FYVh5GNh0y\nk5MoiQiG3LROtO1RNea0uYGv8W7cU9CK/6MnHbDMUgr+fpEeU/VGItZfOLhB\nTg10PpYCKtQCsF+Z36YLTXgxeWEoRvvKWmJET/MDQzBM/pRzMRpPHiWn6E9T\nl9wt1WLix2qM1jymwaq+0H6hcCtu0ioY7qn4GGeCAfVlBxWYcydNR11Rb++n\nO3oXEqhc8oxKy+hIDHIMDDz/YfCrJ9q7ZzMO9Ie5dzOqeLxty4fbuDeLcD9U\nkbuA+I8dfEFmO2UtRwyx3k9yjX2Tsf2oAC8VOTINN8TXiauyTjWyU//lSKDX\nmUHBDXXZhKWgygi3TMNW7gvmzYuzmDcbkienQeoADvONpCuJNmraVaC33n1h\ncQmj\r\n=blZm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.1.0_1569306862281_0.5203038504592481", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "acorn", "version": "6.4.0", "license": "MIT", "_id": "acorn@6.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "./bin/acorn"}, "dist": {"shasum": "b659d2ffbafa24baf5db1cdbb2c94a983ecd2784", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.4.0.tgz", "fileCount": 11, "integrity": "sha512-gac8OEcQ2Li1dxIEWGZzsp2BitJxwkwcOm0zHAJLcPJaVvm58FRnk6RkuLRpU1EujipU2ZFODv2P9DLMfnV8mw==", "signatures": [{"sig": "MEUCIHJZV3dTriS1KbQMPwMxkfnPrGxl8/G2Vn9DDQFQuGy0AiEA5ts84bccX+3XrV+v4/pDZweyPaO5xgb5XkJ4IcDPvpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1103763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3aSWCRA9TVsSAnZWagAAXfsP/3Wh3NpO/DIa04+7p3ue\nXiwERNN/yVQ/7hEtlytDTq6DpS0/B4HwqsmKpst6kz62R9ErhWXrREbG+2Nk\nleOHJuIQGoUJh9V2ZocfSeEojhnAn/k5sqhZWHnF88bEm8ObYuv7uj2R3u7K\nO2v3QBVEMeK4twXwXSj7HCqG3mn/5OBTwSVA5554IOUneOlmN3x3CtG71kuC\nBRFJuSz4MOGAdjvMt9PjqUfPDSfUFIN4rZT29MPtH18Dwq2NLv3i1AC749fs\n0jySRco0FcNjuTJYOgmcqeJv+Pu2onTZ2EEwN7v0bzS58stE5oCJTg4tz7nC\nP9xe+R3PmhSOzWJlZI5+h0CQ4Vuhfwn3+KENEVPxgELKN1XtXF4jLHzftIGs\nfW0y49jYLWXiOFBwjowPaf34tO1yx9NBJLu92YrbdqaHqBHUnBRW2KaaJzX7\nqEAHbzz3tNJ2b397hqJgVNrozVubipkD7U/EsR3m932kjGhcEt5yIb7Qb5Pd\nmVuEPallLL0bgDUnwtCEEDFkX+tMF476Do+9DtptUv1S3tWZlxIH+R5kzSPy\nPj/kYYA29a0CSlgtwEPc3uCMbmEkg5F5SQl7OdaUtm3viKPv4ewbTpjzqt2O\n5btpm+TPFRgrnZoTWLizJC+vyWzBYzh8pV4mmGHi1tVnX++tl+8FjCymsrtc\nM8Yj\r\n=kQlW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.13.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/acorn_6.4.0_1574806677282_0.13764065759587019", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "acorn", "version": "7.1.1", "license": "MIT", "_id": "acorn@7.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "e35668de0b402f359de515c5482a1ab9f89a69bf", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.1.1.tgz", "fileCount": 11, "integrity": "sha512-add7dgA5ppRPxCFJoAGfMDi7PIBXq1RtGo7BhbLaxwrXPOmw8gq48Y9ozT01hUKy9byMjlR20EJhu5zlkErEkg==", "signatures": [{"sig": "MEUCIQCNKgl/6qv1HG3Y4L4N5aBociTqqfX2rLki7NmMlzoneAIgXmZrm+/dZOJtTINsNIfskqAWg9KoxzQs0n+diHlY9fY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1105670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeW60ACRA9TVsSAnZWagAA7TIQAJzQwC85+1t0xpSxegw+\nQ626tRBT6i5rb1cyQMfwYESoN0H2rd4ZZxabz/DaEDA36goGFCmfB9U3S0OS\ns5XIafLnAhgT6L+KY23FYBoEZFM5B4VQVT+zl0xBZwbBOW+Ed4gGgMWxY5jO\nLHu0wuMrnlEFXcaY3MGHo7/sioQI8n4Xxm1IWOl0ZIFreusjgKrXa/Wr0efY\n5SDeJdKGMX7dsW1a9SjLFPT4mFegB5rqaXIfvOhR4aCVyzxOzMmWYPmXkkNS\n+dGeoOIlmRk9nSzWuYr4IWwllqINoyAbPJFCr5ZtLBWcmXDgClVSVUDtdpMp\nrBPtgmZm6C4fW7owFP5dQ1fRpzytTMPb+qewdHAuSl9QPmg+OOHxcLdCWz+3\nuTi6xUwiNnGsPCfdpofVcGHir1/ZyMTpHygHZ1ssBVeLqkehSg4xqgVcfNos\n/QPp1W5Y5BMk8IQzBnRAh4N6+Gjw4YHXTeArsFzScksq3fpfSIqVUANMM49R\nTtwfTN5yxdDK5PdsSeTVfEMst108BE2aIMwn/iQPdo71mYwZpFsae+t4ZxVu\nvPOIqtO4vItduD7jtx4BDjP8SY6x6e3+y819wzLAIjqMdE7KJXIVdcohoumP\nvYICyAp79WqOdvBtWHcaFtAwCLK4+Sl4c/VAVHvf0jJAAF9IoinC5q7Ku6A1\njNOT\r\n=vqYP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.1.1_1583066367655_0.08734337855607599", "host": "s3://npm-registry-packages"}}, "6.4.1": {"name": "acorn", "version": "6.4.1", "license": "MIT", "_id": "acorn@6.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "531e58ba3f51b9dacb9a6646ca4debf5b14ca474", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.4.1.tgz", "fileCount": 11, "integrity": "sha512-ZVA9k326Nwrj3Cj9jlh3wGFutC2ZornPNARZwsNYqQYgN0EsV2d53w5RN/co65Ohn4sUAUtb1rSUAOD6XN9idA==", "signatures": [{"sig": "MEUCIAZ8Y58eP56DIOV8cirw+zYaXS6vnyMgWKZW1uJY2vStAiEAiC0H3isjUSe7z4PxckXgSzHyjaHiviCDWsTd7BiGP/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1104023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZhzLCRA9TVsSAnZWagAAF1kP/2i/8XA/1VgN23QMZt8E\nXFBEISdzGpeGDroNIoQwPPOdbCDCpeGeqCZiEciguHCG3sPgBQZiOeLyw7/T\n613RWrECUoW0ioFmYUypDdaIF5eTfk0lUGD6XlCYdSrZ29nA5jDIBpiv7DU4\n4p+bWx5Iq66rokCGmsWQr//1AoqkdNogl6Y4OVjoocTPjmzWEx1PRC5HtaWW\nFS+H8r3EYZLxOJByWTxt53SpI8UjvxmaXsFD/YUjvjzQVVQmW/lHfBCPorKO\ngK5XDunVhDg4KBwbkMlxthTHhjLAYzrwSnIrjLxtMVA+KabHBKhY3GeOhQE1\nksLKmbK9eiBRAHiKCylZdWGR3+TlLEzhT0umPve8BucL0ZRUEf2nEux+diaH\nqh8SbbvPcgprTESVWqaoV0+4ZK2laysvuU02agwPAX0SX7POtzYNbh8uzh6n\ndXgjsuffF76H2id2t6QqhEOy2lgmPxA1FRbFOrAi/UMq2cgSuiaDUuXK9jLn\nUdfbrjf2bHNtjSPnAwDFBXNGs1+jzU3V9QiWeQCtVzuIlnSfHr05kZJH+TmW\nMrKucR3Gw1dM2SOHEkrdIiyNcvePJ4+E57kd6aVjx+/PNIvBILzc3uPJwG4J\nXFW9tk4NIySLkL60euqr77J15SS8GDn++Dzkrk20OrTpxvIQguGM9HYX0bj6\ne36L\r\n=r2a9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.4.1_1583750346701_0.007352038532391303", "host": "s3://npm-registry-packages"}}, "5.7.4": {"name": "acorn", "version": "5.7.4", "license": "MIT", "_id": "acorn@5.7.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "3e8d8a9947d0599a1796d10225d7432f4a4acf5e", "tarball": "https://registry.npmjs.org/acorn/-/acorn-5.7.4.tgz", "fileCount": 29, "integrity": "sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==", "signatures": [{"sig": "MEUCIQDT26LFBYaJGfNMbnP7HnlM0HySFrQnivHfnoqUiqqhbwIgDUatTtVPjvByZicCYnO2GOafXzGWBUx+J6sB+6xx6A0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2022352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZtCECRA9TVsSAnZWagAAaBgQAIAIcEZZUDphnt6spUk5\nlRlsQr6ynCQaCsoet1ZBnfOv20J9EcoSb26dN1GRM54fnoOLdUFOPQZtSK1l\nVN6v+YEti7uqM3xmZdJDJeQGgMBSt+tWvxmuqBoplifB8cdahsDa8BRqQJcp\nnjUGP44vqv5CpxPgAhQYaY2J2aqPPlRoyQQlc04/F3O35Dgitb3UjvmO+2Gt\nZe92SOWh8bw1XWK5m2DP39vEwfSDAoC4FnJRavLFpVAAQJ3t/7fdaysFvCkw\ncqGBgRM9ltFIGh9zDOWNpIzA8YqvgOAOUUSq7BoOct88RSnKIvrE41NdvKH0\n1LCknp9pXWm7iJ2fGzx1SahdT+Dw7z0iL9vEgA+wgEiCjcH/T31tMGFwSdWl\nk5n9xFpRqBcNP1u4zIzdmsi/sbq1QWwt+vjh5Vnao5wsc+fc/52xqCYRRwrI\nmNKRZbKx0PgrU8piPylZrQzxhQLeYZ5UAzYvkXzLOTgTTbXvaJDEQo7qCrvp\n7l9BMTvQjBLzd9G9vfxfZhGeNDnSPMNs19FV8QcXvZecKNqePjb091ukjaJ5\nkL+xNX6alq5N4KBqY0mzcLPLv95tk6dCWnLeWYlZ3ha2Pk9EFyGYqN7cIJZH\nz5y+JZqJLWGv98Aep6iwc0zUNl3Ls7olpNg/4RY8PJe1KOEIax5JvLdYsz49\nnZ5S\r\n=uMur\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.es.js", "engines": {"node": ">=0.4.0"}, "gitHead": "6370e90067552022710190319cbbbd8c43001957", "scripts": {"lint": "eslint src/", "test": "node test/run.js && node test/lint.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "prepare": "npm run build && node test/run.js && node test/lint.js", "pretest": "npm run build:main && npm run build:loose", "build:bin": "rollup -c rollup/config.bin.js", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "test:test262": "node bin/run_test262.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"eslint": "^4.10.0", "rollup": "^0.45.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "unicode-11.0.0": "^0.7.7", "eslint-plugin-node": "^5.2.1", "rollup-plugin-buble": "^0.16.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-promise": "^3.5.0", "test262-parser-runner": "^0.4.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/acorn_5.7.4_1583796355918_0.7955430376538595", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "acorn", "version": "7.2.0", "license": "MIT", "_id": "acorn@7.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "17ea7e40d7c8640ff54a694c889c26f31704effe", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.2.0.tgz", "fileCount": 11, "integrity": "sha512-apwXVmYVpQ34m/i71vrApRrRKCWQnZZF1+npOD0WV5xZFfwWOmKGQ2RWlfdy9vWITsenisM8M0Qeq8agcFHNiQ==", "signatures": [{"sig": "MEUCIQDPH1RyvQLCeo9ObXF8LdetCILoVgYWQ0gKOBHRzCVI0QIgItQQG8k7AUk4xcwVI0dubXHgNGaRpn0SWtuA+l9GcEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1113050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetlYJCRA9TVsSAnZWagAAnoAQAI6TSWRS7mmo+BYC1N7L\nMc6FHnpvJiCRA95NSBgCuxGYAsfnoDxuvdJHd8xG/JTiCPMvCGDIH16H2Xzh\nfOKRg4w7YEi1j+Nx2hnHg0L6wdIGMUAnKfylX6EObN+pco+QRoAXepWQJCH+\ntwhbax2ZkHIdiLDlx0RQZxruVoQdMcLXbPgW3Frv5Qcfnj+jdW7Y7fx9sqqA\nqXS2Jvk98dnHrtrSjaMkXFpVyC5EQ+4X8jQaiZ3S1ETUiLOIx1cGrZJkDfxN\nIeTERiEkHg1hgPCn1l9Zhz59lPPzMlKDWWkbepnRgrbR1I8Q+W/QvyfFFg3t\nLTgFAkDAIW1eQEcqdUtObp0yyMV3p4jD4M5N44hyJ8VfHbLiTPJ/IzsveTuh\ntNKCFHyb9R8VLZk+jx97/FuIOLjVtroCCQc3/jH0j2f8BD/fG54o6I+jFz3n\ndnUCl1IiabcGS5lImTxLQ3fUvY6HUpRfXOW1dtBmSgF0xM1QhpC88ClDEFY5\nWnnobIkw2rma9IxPhXE6GTqgRWO+ShJT/PWd8USpyEcnFAYQaZra0W1DJKJj\nSkYc8zjVITfc9GvGp3jCvk+quYelbTH+d5lWsXHEFr7NGbxMN2WoaooCIBiI\nytKFbQOZITjOiotdqaGNmvAaS5z7KfI1FN4eHj5fi4OuO5kF1iGy0Chs/yxa\nuEk7\r\n=5Wnc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.2.0_1589007881211_0.8636980803176082", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "acorn", "version": "7.3.0", "license": "MIT", "_id": "acorn@7.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "34025cfa3ca25b3240d80700dc3bb695f8d86614", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.3.0.tgz", "fileCount": 12, "integrity": "sha512-Q/reetejf1Yf8vY7wyZI8DOsrMr9r7RfnDYBVjIdE61Rk8atUkbV1Kyi/diJzgAWiDiHEPWpNoqpPb+2CUbudQ==", "signatures": [{"sig": "MEUCIQCEqnE35gowBAwC9hl5fRSWbShGk0WqI+fmAykRMjCMfQIgQCzSA2jdgMIuPHZMo3henzJLpcJNXQdVBOBKBmhryg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1116300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4eMwCRA9TVsSAnZWagAA1C0P/ApfOhkdmQz4JH3foJU/\nilhVDvJVMv4s9Jx8DauPIJJPPc/UsQvvVofS4qyAeGC4sVvYiULdjb3dpsPa\nOduSLkcgq3TWIQ/ZKeEJdzK9ml9NvZCDbf+AhuqXYaWgaD/STDqB07SGJ+eF\nmN4vYd44BME0/N1L4IwxfsoveUHAEzgMcIpMqFu2Fbp7UfCJ4NyFCeGetAvr\n1u8Mtw930irNpf2O4MU/ZPK4Ce26rWea9Y/9vPZPX491DEJ4BYKu4/wlHw3e\nxrJsHhLyq4K0gBeUgGMv4pzNZY0etbOFZsAT/kEUMHOpOVLVY6vFLiX0bVr6\nsQZhwoXEOV1GeRMLZzix6tPmmJRYRsJsexyhAOtVwqJMFDndc4ESmhfey5ff\nC6erxWjSzbhOjVIDXP8ouBP/XPQ1seG37pTL6slkleAitoghzodUto89CoFv\nezzsfJBZjer0XYOmSjkCZbYI2f8OjPy7eBiABq1x+oSUaJTFIbd4IQOK/jtB\n1wgzQfoqWPYkzvuNPckyW24G1J5zkU2F3hgAMqwMiJQdIZeb3rmcqPObwCQo\ni3i5myoO35dGUAK6Fc5r2KBxegZxI+1oARR24MXewryNRDGs5aHlD5BWvGpZ\nazAMZ8t1IcnH8JqucvzGtir5LC8E/q/lQH/FPmLh6XPUAX4qDDiDs6tzV6wI\nry8a\r\n=00qs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.3.0_1591862057637_0.33164038411403807", "host": "s3://npm-registry-packages"}}, "7.3.1": {"name": "acorn", "version": "7.3.1", "license": "MIT", "_id": "acorn@7.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "85010754db53c3fbaf3b9ea3e083aa5c5d147ffd", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.3.1.tgz", "fileCount": 12, "integrity": "sha512-tLc0wSnatxAQHVHUapaHdz72pi9KUyHjq5KyHjGg9Y8Ifdc79pTh2XvI6I1/chZbnM7QtNKzh66ooDogPZSleA==", "signatures": [{"sig": "MEYCIQDedW75BGfhPJpE+9WRxfKinivFxt5pqeceY7x4qjqXZQIhAMaC8PswZ/GBUT2Vu/wfEEiFNGvhW7oKAd2ungjRc8SK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1116413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4gZBCRA9TVsSAnZWagAAGS8P/1zaXZ4Pyx99YKl5I/gh\nWiA3GO6VoOse4xdH2QSyiJZQ5JrCJJcCGNZ0tN+0LWR/+dtnyh66p42gyG5e\nyvka3Puoo4I7wMgQXY9B1OrGR1ZBHuuQLNeB1SyD4MX7lqrfcfokc05y767T\nUtpMrBOjdlvOxpe78dMf2LDRQUM1rRrucz97zOe4okKuRi4WYTjdO2UWkOkT\njbT98Hhf9U8eUL1gwxUrefG1fXQjznEkHQMogQhyDtcQqYOL3pxC44wCuy+T\nMv+IkGW2Y51Yf7x+UrAk7tSw4R5OYeHuSY4kdrCncbu8JvXp/z09JeQz2Q6F\nGPxK5TpNsCTvtbReaTRRzibtBgXDqKc5AjvKk9NcqFq3Qg11bqRnLxp0C74/\n7ggFHGMYvpZ98+/zlJCTg1oXXha1HJD1u1YPGAiLHD6Lvxzdw6Yz3eKk2RIc\n0vZtWkK8u4JrOVLD6ipGnENVOuGYkLYcCgximIwPKY1nLdBnT8wQJvS+kI+a\nr2tXcY6321bRC08wkSwZ3C9mEAF0WUYwDxEGrNmnZD+JHQXh0+r54p3+x7L7\nK7HUpTacjW6DqxTPqu2+I2Jh9DYGeXAln6s8Zfk3Sw1scWPVAwC+nBBvwRLY\nz58HNw/3R7XXg6bbqiZXODGjagB6pbml8rg3TrCMlyoi3XPDgWjRUIKxUJ8N\nQBWs\r\n=eet8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.3.1_1591871041183_0.8624241900319172", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "acorn", "version": "7.4.0", "license": "MIT", "_id": "acorn@7.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "e1ad486e6c54501634c6c397c5c121daa383607c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.4.0.tgz", "fileCount": 12, "integrity": "sha512-+G7P8jJmCHr+S+cLfQxygbWhXy+8YTVGzAkpEbcLo2mLoL7tij/VG41QSHACSf5QgYRhMZYHuNc6drJaO0Da+w==", "signatures": [{"sig": "MEQCIGuLU1S1Oz6sPEbwt4ZMeZQKjKKUc3lJ5q5ocicVGTEKAiBsYuWpjjRsUQGNdpQxxYxnsxlQGyTTpjMsFI41d8h4Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1120336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJ+B0CRA9TVsSAnZWagAAv4AP/2ISV5bDniltv6UktSvp\n6Nd8JkYvzhv6n0h4F5xU4F/iyCL6eSYLcENC2IkxjRJCZmvZnQF6uyEn81cN\nJflwP7DMEGpbgFo93V2Om4JgAX6uh1BIErXrlBg9FLyJ6GtDqgOFJp/JCQwf\ndmDt+ze7nzXKE3P1WQgY4BgsFANGqioHwKdaY59Y/Ai/+BJn68Cbx3Sa5ha8\na+8XGrNoAJrNuo0sUI78uPA2dosdPeUlej5SMQaeAQvDuiagLRXMvlR4J7HT\nWdBDuHHz65/sVMhKKyp3idQFkinsIfQprT78legPi6uUdt9bojMBGuCyAnmG\nsL3nBT8YDqsErX4mz+/iemykHCgSad3J5YiA3VetZIJwf0TN5HTN/joRT59f\nxEr6EzMhXkpG8TMAAc+bqrwgStNWO2W9IGwgvzWytSeAuK/wiHBHjijqXFK1\nKfrW/e9hXDE69hduupnDF80DGCTW4BNpHWoaKnulkDgvvVTddKJOOwBUZDy/\nGjtrdEHi3nrLvM+thW5ScuIAbwN/geKX0g8Y0xbIHVvqt1DEJ2nv7lmqK6Ro\nXnZ6CIgedmS3/ThatjpYOd/XAomlhuS2H4X52anOpfuXO4FyQIXQTk8ibIgG\npxajL/BGMDWk5YHoR1YYXkKTQOc5vvJrJMwl+bN6BJaw/gON/Jp4yYOs7K71\naluu\r\n=4zTh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.4.0_1596448883800_0.41102687383605563", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "acorn", "version": "8.0.0", "license": "MIT", "_id": "acorn@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "489ce76a1b3bc2406077ed8084f94b23f59c2ff2", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.0.0.tgz", "fileCount": 12, "integrity": "sha512-wuJmBvKP9pkjPd2m+6skNcgK6S43df5cRbUTWQlns8rukcu5bcSvx9eZjvNXTtFe4+oCslAvIhNCvYBe/69xMw==", "signatures": [{"sig": "MEUCIHTtRKTxyBMdtYFrl/Wo7KxKKS+ocmQYSOCzzt4R7NYyAiEAyZSVz42c/up/5dwEdnJdHq60GJry4/gX4ePmQirK7uY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1129952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM+d/CRA9TVsSAnZWagAA0FwP+gL1D6a9wsOTdQYE7zDc\n96bGrJ+YyjzhKb2/VfmZbc9n2YodI92M28jcsN4eksiAe8mOjtNG7WxQ32sV\nZhC6Ilg5ITtTdLX4mpAM/Tk/bWz0WOgWyAF5RYKcoB+bPX8689Q7r2Oz2ug0\nZ5JT7tE2fuvAU+eJvutRaiYrozXprbViLhDgcq+U04JP9BruDKeq8oMy6l3W\nZfcNiDi2R+SyFQB53pszfuwYGbplwC0FNV8Q6JsGUv4DaByCJCsyAXybIqxa\nJOmulgxjkEGzyMAlYViWQu7m5HzXmsrLBclVNwPKVqGKJA+5NReqmHFfv9Qy\nDdYdIrpqBrZi047HL3fdS/Fdjg3kF1NpAQwOQPmBoIUZLSxuYVehPgX62xFC\n0fnQe0lYgqsi95hFz0N7PtxZzeuBPNqiG95NcmFzOb1EExhm5v63zPL3LRWq\nvj+hv3tljV4LR+hlP3kIWpHug7M28mf4CGjYmi2kfZrR3q1X/JGD6fw/5IXV\nEBnFeEG5GBWy4sfTlD0rjxyuWELMpFBaBBAtDbrJ9UtHCVdrqoVjOMPXyq0c\njG39mh2WxK9m5ujxqp1sjUPKiZWMzzdnyjYshH919IymPZ7E+xs+QTsrBm+2\nyoGGgVJHkgubF+59OLflKaYhNFkNJKxS05rpEcUweec8SxjqwuNUjAxfKbh4\nbl5L\r\n=X2+b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/acorn.mjs", "require": "./dist/acorn.js"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.0.0_1597237119022_0.5846856429733727", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "acorn", "version": "8.0.1", "license": "MIT", "_id": "acorn@8.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "d7e8eca9b71d5840db0e7e415b3b2b20e250f938", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.0.1.tgz", "fileCount": 12, "integrity": "sha512-dmKn4pqZ29iQl2Pvze1zTrps2luvls2PBY//neO2WJ0s10B3AxJXshN+Ph7B4GrhfGhHXrl4dnUwyNNXQcnWGQ==", "signatures": [{"sig": "MEYCIQCHVggZsTEeQ+FYLxzyuNx3i5jt5MpHhzaVIgqOs+Sa/gIhAI+01DRhdb8DPZuMfZ//n7YvcKCWy7h+a3N+LOwvtaVU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1130042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM+i9CRA9TVsSAnZWagAA+0sQAJHmQkCWv3E303i2IWqI\npVEb+yaGkO25/i0OvMROKdx3p7JrlX2kvlHZvQLAqjGXTFICCXATGfiK6SA8\nx5h9di7kNgkCXdZdusafaVaq9ysT5R2XL/mx1MGuPbM/mJ3b5pnYyYDGRSpI\nDFGsvWp+wnHOGsfrIXtNGCrgRJPKEODEwr9GB6vJGQRq6f3zT5xiUeB+ybJK\n/zsnc8oL/6LLMvB/qQZFnzv+IZjU8L5hCH2Ml8FECo5ygg95wn17kFM6lAmI\nXmTgliRwfVmQplEenuRKS86gf2usZqfYclQOF4xU7GmfElhxc20CjnDb2++k\nO6w+H7Y+Gko5y2sMI2Wm2jeP1KM/dCW8pbZBmklRfWT/JKBr5DzMdxURvI+s\nRmJ5BXAuGw1A0UvbbMVNnRGaPTEUxco2QsWOepq8I4L/OSBmnbrwE3BpsZT0\npe2dxX1rBYvSfOtWOXkIbEdMGLZJGsSrkVb/Wkg2NlvjjD0cafjmjAaSprUV\nMHWPmyd2vcO5UtOh8Rqm1jLxbqSrvA2LOZ04iDKPLI8/90kKkgpDvyVQ8vlI\n484K8UgwTQuVJ0kgxuGGDz/K+lE3sQeEjyVKR4cig+5wucTK6gD31jZq2218\nWkRXmyoKEMgKEucNsbTuYy1gb5beXg21eLhtpQbO4srpR6xVznxBjQ3pMk7a\nN+iI\r\n=8+ek\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/acorn.mjs", "require": "./dist/acorn.js"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.0.1_1597237437354_0.032129772090597086", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "acorn", "version": "8.0.2", "license": "MIT", "_id": "acorn@8.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "dnalbor<PERSON>yk"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "Fabien LOISON"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "kyranet"}, {"name": "laosb"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "peakchen90"}, {"name": "<PERSON>"}, {"name": "piotr"}, {"name": "PlNG"}, {"name": "Praveen N"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "su<PERSON><PERSON>"}, {"name": "susiwen8"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "tuesmiddt"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}, {"name": "龙腾道"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "f7503a253311d4af42332bc188d5713edb2e030a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.0.2.tgz", "fileCount": 13, "integrity": "sha512-t0Dw7AOyeKs4nez4dhzkBDHB28ICo1pxk3UFsLfsCHOkLW+CwbAZJPMa0vBbq0Mqsslhb7n/7H4qB5txaVQ4ew==", "signatures": [{"sig": "MEUCIQChx+YmmFDZKt969OA2dvIWqdsGIsKwQoFRGcs/t6h2aAIgaLIiRQcyHjnNsEBHfOpSxBXmQAOvl3Ix9eU0R6uXaOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1221102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdBwoCRA9TVsSAnZWagAAaBkP/jeN4/SWdikgvEW3E7KG\nloUql4Wo61wopAUzVEFgJUerUyQ8oYDxMg/2p3KLfcApdlpWeoMcGIEgOQqL\nVU4Ma2qGQi1SN4rl0fpLL1HWLBGY0OK5Z7Eggzf/+d25mO/3R9QB6JpD1r6t\nUnnL1/jaKtWyOp9tbkx1BQy4QJtmDtQPYiFJ0IYj2sV9J/qoePw2qh2UXdjL\nRzDFR5VURcxZxqmk34okVaM1ErnHoIRIXWO61Of7dbesq4tbtqzvs8owzjmA\nPuIpfhnB7hqJU2w9JHRg93u/RlmBzKh5mi5uXKn7e7pm0c+dxCsN8FMathPm\nV6N06FaDPBkbdZ/idogN3i9JtgwC4T7JDC9gIa6Lk1BJ3c8mzYCddLmclTCz\n1KJxGaVb3aPsrHPFrIfqNBsOplvomuKu2ukUoyoIOMdT2P8T2Bbae4gOatN6\nXCw4RGaEt6hwnoc/AdRrjB9nb0paf5CCsTggfSPXupfRcriy4j+EfKBkXS2s\n+usYuPvFi81gI/D6F8PBtKvW707XNbjd1t9upcLZ8B+yV1mpUoyNX5lwTLSY\nkG/ly/a/rsd/Z0o33trUzHLp8MQSKZVwanQzB+LTK8N1sMfiLEkLspiPHE2I\n0YrWBsK2cH3Hm6Q4DMGkQldf2MdxJIARaWR0orqHx+k0mVQNFKnhTlTAhEEQ\n7S/B\r\n=BDIi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/acorn.mjs", "require": "./dist/acorn.js"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.0.2_1601444903892_0.08995704828812667", "host": "s3://npm-registry-packages"}}, "8.0.3": {"name": "acorn", "version": "8.0.3", "license": "MIT", "_id": "acorn@8.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "68e78e53da020414420282428f1a4afd2075c2dc", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.0.3.tgz", "fileCount": 12, "integrity": "sha512-uRMbCU3bM1L697KISxvkd9TA2zASoGFDODzFFdQR4qNpPffj6ZMdp5J4ffXUXR9G0PVPsN0enEkCrvssCkoPsw==", "signatures": [{"sig": "MEUCIE86zLT0Jv+0bhRWhMrMnesbXmLDsH7UFs6enhMwd6KnAiEArvzgaCdcfLTvt32HMQZKyb8nhQWslKlqyrwrfU3uAsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1219915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdtUaCRA9TVsSAnZWagAAnXcP/A+nyWzs6lsCSxn8DiVO\nvZmVFDcntnm87+syNSExqIZOWvCbb4PEmugNjJrKmaKeznn/CzaeVHMYSVyi\nh0cf2nv9Y1aP3Y7IkHbiNjK+KYL1ZJTVUJf+zMZd54/6/DgJZblKMBFGXzgM\n1V7eFGNWfNtGE626134epYS/nDdbEOD6lMsucxf9AaJiPSPELgBim4jDwROc\nia0pCDX89q6I6gS1CSCrC++YAIoLdM7pSchdUuDDfnfKFoMTbc/zlQkrT/2e\nKDss4N1BktiEErwGFm2Xqg6QcR150jSVnamPsbazV8Bka9Lw7ohlSlpMe8Ak\nOLLnUppS+HUD8WkAPj1yIUkOA1MIyi3CkZ2ODKUrgx6Oy0swGnmxNONZo6Xq\nAtZEDt5m2rCdWqmNB2XCKsOoyvZgFFyZsw1YyA4LFgBPDUL0owRZCnRa9nk4\nIrZpwl3Y5LRjtAd25Fc19CtEj9/8dwadEH6EzAE4czT75yTwtsvEo0gajLqw\neVrRdtuf+nSjwcOMyzETiIidVRalGEvPtwKjpu/rspmqFhq0UG3m+hiuOP42\nFjyHZtk16vmoagJAMox0E6M+ebrd35hbeQFGsOrWFtAxt44F8o/YZP/MGEal\n118wDArPeI6DMkx3La9FkQBjmS5q4damCiTrno1G1toFAhcBYPfR5w1Mf6Lq\nZDCV\r\n=ZB/e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/acorn.mjs", "require": "./dist/acorn.js"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.0.3_1601623322070_0.1545837103373191", "host": "s3://npm-registry-packages"}}, "8.0.4": {"name": "acorn", "version": "8.0.4", "license": "MIT", "_id": "acorn@8.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "7a3ae4191466a6984eee0fe3407a4f3aa9db8354", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.0.4.tgz", "fileCount": 12, "integrity": "sha512-XNP0PqF1XD19ZlLKvB7cMmnZswW4C/03pRHgirB30uSJTaS3A3V1/P4sS3HPvFmjoriPCJQs+JDSbm4bL1TxGQ==", "signatures": [{"sig": "MEQCIGv8uU1KdaNQLYYl9SoAW9/TWCovJ90aOOe21ybip7u1AiAIPBfZFqED8wOlVhyaYIA5ZWc/cuxErYNbJRs8L0cSqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1220073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJferq4CRA9TVsSAnZWagAAtlYP/12AfDBYvb0lEXB/y/wu\nIEMFE4tmFdskTYV4qmZhmetR9strQaQ8fjuMWdmqW9WFbAsNTE9ESze1uXGk\nl+KRQN9KorTfv5b0rsf0ZfPf7GePCaThWfNZdmUid8NZDaJX70AlYkDQhFLb\ng0PgXGstPAgwNKO3wteWlhIInT/omW6aCm+nsIrSsIN8P8OcPZUgNmaDhX5y\nyFfSS5YvAQrAg+XCs2mEj9Vj8YA+8suxHEJKVmEpfltH8NE9UXNG/esThvsI\nTrHo3PjtHKoN2psho0USnnkLQfwxwHmMeXeNQ1r+ZGu/wSgRXguOUHmBcrl9\nE/mTZCUBfXIKyTt2O+W9Bk9K27jXF2EYJO8BJ7clx8b4/T/lLqtwgCovULAV\n2CnNfkezF1LhVzcaoMUxVqAMsOAJ/VlIvkOxkirml9W6eIX/DglXfvNEGvt3\nAK9Mt+u5EWImIlHzHMJSQ7Wi5ER/OIb3k2n8sWJcntv9hxypMyvGrkii46oU\nnKuh+GlpQIvKBLAQ/qrWfKMlORMFTHJJlsg0/e/hyP+oSG4TxED9QECBBhwH\n9M1yd6XtRTmTHOAQfadbUZT9s+3Y3HRTafhP2x7KDbO1HzFswAiMAzuibYeI\nLiV4/izCvUSErP8Jar7Q1DT12INs0vax2hDUW9OsIcO+VzcUnw/wIB15NI+f\nTpAa\r\n=STxO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {"import": "./dist/acorn.mjs", "require": "./dist/acorn.js"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.0.4_1601878711859_0.3785650952812938", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "acorn", "version": "7.4.1", "license": "MIT", "_id": "acorn@7.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "feaed255973d2e77555b83dbc08851a6c63520fa", "tarball": "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz", "fileCount": 12, "integrity": "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==", "signatures": [{"sig": "MEUCIQDw727crFrr3olU5/ed9GXt6jWwkP92qHzeJi42fzoyDgIgB/Z8jC2aW/x+dTM8aeUTcE/A47gIs9TCtLz1XtM976Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1209760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfetD3CRA9TVsSAnZWagAAXJcQAJl496cHXW9dEZo2uJeI\nOess2fsBmeuWzFOwGW33mCDMGBk5RVV56YscF0RoKVR8jEp+aem6IY+Rqs1a\nThIJdlym74AMsA045lPhzztgJKnZi8u41RzDkQH3U5AMePFngpGB0t+ATvx1\n0llLdcaJujlvMQlvbln2hiOm72IdEsqPU9Bv9rhpJF3y/zW3Fi2W9jxw0+ls\neBql9dkC4u9yRboY0CYNAyKn8QRyVxqh3SaECOamyeDIoZFRUfCoNWs2uaUO\nQNn81uN6BLI+9qbK5+EFoWGmEtMsF55+7DFVPk3aoGtwt9SoIQV3LTlqEEDy\newrw4FM00tOgHVW2DmVs8iO3iTlgtm9mJh6DNDXBtLlZzzmSZnP5oGbEa9Tm\nLqHng7fyKzPn0svWQ34WQGlxLxwySp3NK+HbDiWootDupTjm6SxxBkwKeteh\nbW+nH0cHN8DGB7pfUvPtEbtH4OrFYNrp4Nr95jLh5Oo8NLr4RvZ1FwsFvNF5\nGUm+wP539xXJOAbayl2Zii3e0iROFN+54B7LPVPUpPnNUsG92g+ExpcAWy4S\ngO0Zr/592Maa8mjfEnxFe9lo8JFFakoqkue4UcWA+Q0+kFzNm1BwBen5YyOP\n9DGVD1leG9y5UiDqjTX1lYsxmOTRHR5quG8EqTujbuLplKGNKual7ujT3FGG\njrvO\r\n=Un8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_7.4.1_1601884406666_0.3507732536109269", "host": "s3://npm-registry-packages"}}, "6.4.2": {"name": "acorn", "version": "6.4.2", "license": "MIT", "_id": "acorn@6.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "35866fd710528e92de10cf06016498e47e39e1e6", "tarball": "https://registry.npmjs.org/acorn/-/acorn-6.4.2.tgz", "fileCount": 11, "integrity": "sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==", "signatures": [{"sig": "MEUCIQD5iCEyMp2bKWSI8ZjDyWPB90s+E9ciSkXpvBcCIR/VaAIgChbd/udN+kxoOcMG3XDh2oM/tIRb8yALnInk/kLPr2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1193449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfetYtCRA9TVsSAnZWagAAaYAP/0z1XVKjjHmktwxanOc7\nrtrV0WCKBMQKGOwppaC8mMrKrtSDkUdV1/IO5jD0AS+37YgF8JYdjTVnv1Nv\nXY7gFnpcoviTc46852B8eSrfwWmRQi/S4IoS4yum9vPJBM17FhSE1XIlBVlI\nHEDyip5gaecI9RkAjHCTuvfoIgxeCffFqnCCrKeyJbPkYXQUuZKJPY9EwLQP\n5VzskXGyl95Sn4FdnJ/AMUN1b35XD0Fvu+sC/LPaQ1wEW5usOwIHR6Sd2Sy/\n1r/gY2xzGUFSDoGA1YmIP0IcvoORyVwpXomCfEQlMnr7obnAXDRLyvsxnDtO\n9hQir8FBJ90pn++xxHTmrZO53XUNSKrAhIw6JcZC4bw8CCZbPaH9pLIwX7nv\ncN3R1z3a4LMPEaJfWmKR3ZAMmOabmH6bX1fySmOC7IPSqMj1NdmJ3RPQKWsQ\n2a+wrKGHLXxXzMTeT89AeG7hdIV1vOqoalIaYEv/fRHBYxTQYsih4HGge+Mf\nmJcgKoL9TJF5E5m3+p0m9p+OcWmfouVp0QyQu3tHWIKJDbVeoquEc21OeRcp\neJX0R8j4e/KbeZrMkcnIMciIGnYQmmp7vBy1PT+3aOGnqaBjyiEQhNxU6uV4\nvia2t2/GG+SDaR8/SVL2y70NkmZuzAEFMck2wwTDGYxCSvvd6XmZxRua65wV\nBkck\r\n=OHo2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_6.4.2_1601885740904_0.5951674345387055", "host": "s3://npm-registry-packages"}}, "8.0.5": {"name": "acorn", "version": "8.0.5", "license": "MIT", "_id": "acorn@8.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "a3bfb872a74a6a7f661bc81b9849d9cac12601b7", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.0.5.tgz", "fileCount": 12, "integrity": "sha512-v+DieK/HJkJOpFBETDJioequtc3PfxsWMaxIdIwujtF7FEV/MAyDQLlm6/zPvr7Mix07mLh6ccVwIsloceodlg==", "signatures": [{"sig": "MEUCIDJ2MZ5YPWUHK75XeIEJT/9IjAtS34HktHj0i5eUW8LbAiEAveDicrH1/ulyxmpm1MwJr3mYhIr9clL9S267JxRFvlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1220671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDnXhCRA9TVsSAnZWagAAz3QP/isJyfCwC0pdsioHqU9/\naMHglwlyjmLXqXHTAPJUXKUWrlkXjDPfxkLCiO1KAcbKUDwm0Ma7jmPQlptw\nY+ri5Sv5vdH5Gd1xtM3NBCe2RTVr98bIHus0HFUH41mA7axPkJPbGDeN+MIW\n6BUvXxdWPrej7CfklS1JJ+DNyKGFzRAyIKQT5StjGNxSn3iMc0XcQOQ7Go1L\nh2tf4rtJIxIFb9pqbx4qCsj0GhJQcTieWFFwrCnaXa81CUshGApafChnYh6w\nCCf15VfLwXFlspYR/Y15co6Y5e1k8PfXwagiS866XVL/79VTlW8XTDF1QRh5\npZEDifS7b7cWSjOV5AIw80/EbKse5ADUe/H4v3D0oNbV95w+aWeLNmp9q/fX\nHYZ+bCSSqXj/U5AW3T729HTsBU3UVCPV/ore5G5CHGzo2yhovhUk7CDCkYg+\nXO0/QgsvkEMseLxF9cAJ8IZrH2K6ngR534a56qYmiv7iz8kCOWjZ8kPsfqhp\nIEQJBfoK9Dow+oG4DUwvPYV8rYgaXeIXFcSm/jKpZjse3389vh8WRvC7rbng\n2XGMu8nQ+T0iXZodhXawE8Dyk6aRuP3nzHeyPEuq6GEZbUU10dUV4KPGT6No\nkuwc/UWS0inDWUsZ0KNgVOiufT+XVhykRkzolkwF+GJXdR4AsZ9YXrGg6h5L\n7+BJ\r\n=Y5xv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.0.5_1611560416496_0.1974239153916273", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "acorn", "version": "8.1.0", "license": "MIT", "_id": "acorn@8.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "52311fd7037ae119cbb134309e901aa46295b3fe", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.1.0.tgz", "fileCount": 12, "integrity": "sha512-LWCF/Wn0nfHOmJ9rzQApGnxnvgfROzGilS8936rqN/lfcYkY9MYZzdMqN+2NJ4SlTc+m5HiSa+kNfDtI64dwUA==", "signatures": [{"sig": "MEYCIQCG5zy5kXP39aicyBHhBy55kmIsCdwAakb/ed5up3nblwIhANAIUQoC7Vx9d+sxJLEC3zbhh9IYdxOKoLAGbYklyzEN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1222450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgR0aGCRA9TVsSAnZWagAAUgwQAIpmo1DKOfOHZNZh8Zbm\neAV4tsdPWoJ0yxiNZfi1O10JgWVugj8D1P+x5EdXiZmrRfIntPaGyul45C9P\nRZM0inbnKeiQEUr/IrSEz+7ssFxltKNMCA68Y5+Zodet8y6aqWJSib0q74Qu\n2yBE/E5fsc6ZAeOpnG4Ccw7n0u/Cv2ry7wraRr5XpcnaxkgpOji/j3+lKetn\nZetKELZYy1Wx9xkTHJqmfbj7s3+6jDJUjvcazOeBUHkq3iSIhszMbNsxIHUd\nKne1qsTs5+j1QO1eiHRiWGafdaNx6p07zR3n1IuS2Ga8O7UXnVN74Wu12f53\n+3n6Ss94v4q1toViq8IeHJ3ZDx4TGtr/zyYYnddaqQEXmvvts6VZUA5QvYjL\n+YkW382M2lMReM9yXbm7JJljZ4LIwT5Ot/aMXVgWmdRlyLw9PdKqjJRkWowV\nO5mmO0WOmxNQNUdEX3TV0iRk7vJJivjzEq2c9ygKZpu12FxBJPBFkO2kFouo\ngMbKMBW9zz8ZZ5QpfYJMxFnned4xhsz6s0i0aWsJmKbYrmgP92FgGFFezQGn\nI64bzBYSzw/1oRa/W6PXfnrOtS/YVfZG2LIcew0GOjruxVM8GRU1/JBsS2tx\nVRMkMOcKphaEv4CzKiovmJXAx3FEFWa0KbLTTvGBJKU8GsbWUTYevTg6iop7\nrsNy\r\n=iDkQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.5.3", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.1.0_1615283846295_0.1868617324610995", "host": "s3://npm-registry-packages"}}, "8.1.1": {"name": "acorn", "version": "8.1.1", "license": "MIT", "_id": "acorn@8.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "fb0026885b9ac9f48bac1e185e4af472971149ff", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.1.1.tgz", "fileCount": 10, "integrity": "sha512-xYiIVjNuqtKXMxlRMDc6mZUhXehod4a3gbZ1qRlM7icK4EbxUFNLhWoPblCvFtB2Y9CIqHP3CF/rdxLItaQv8g==", "signatures": [{"sig": "MEUCIQCkTX9pgVFhEiVmm0FQGEFOUX944ly79AGLU6rzLvKWEwIgYGnV5ySL+PNdJBsc+pQ5yWgBkz+eC4gUUxlaLom88f0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc/bWCRA9TVsSAnZWagAA7m0P/3/koc8ZN8n3PvEP8T0r\nlcGXFodFpG9ksrUGtHlWJT1CIGAVMAtY0Bu743A/2RxVZh2t6XlM67+RsuvQ\nlJmXUXBSG9PmFIJ/qAzyXJpt2NAD/yP2NsBKdqP/3IzmNIc0LfenNcg3morE\nuOY98v3LSnBXB0jCPAsh5TlCdFgfv05CQ4UbEh8iI/BA9B9WbrEhN8MhWRrf\nuuI0tiMCuUpIU3Bt54mgve3sKzimiYIBvUOVKNuUj/5f1YSwuofIfitruLPu\nYyxEzjwSjKTIoQWXjfT1mA85049gAcDG4VvUXYf5Z8tPVwMRZp3dsL6rBGUX\nxaQtyDmUGkciXWIYkkRgJkerlFOOXqRyXGk89VvG82gYTCYGWaDy+vzr/eap\n+wWilQXORxX5KKMcoTF39l7SnVsZOyE6dyNHO7fq/fNhe/uMVRz9eGsHXzk5\n+ZAil5AiRlZtZ2lxfs1MhPyzsUEYjLzVPrsJu4PmZXVFA6cB6hIa/5iSLaC1\nzujwyiX7z9CJa/oZZ8k1iFYW6+XSm1t+0IP87lbsHd81XI1VpzEo/lJbfrVT\noHYXSa3bhGIZXe8f98rg3hrBRokM12Z1WRsDpI32NWeo3h6vIghBnZu3z5UV\nt889VWnPF2OGqbCo5RbW1YV2wBtnGGaLJ37x/EC5QKl1lv9R12zOPMBd+HIu\npLsW\r\n=KbV6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.5.3", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.1.1_1618212566143_0.2020606572016581", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "acorn", "version": "8.2.0", "license": "MIT", "_id": "acorn@8.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "8acbbbef258a55e376d3c6ff66edbccbd2c915a4", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.2.0.tgz", "fileCount": 10, "integrity": "sha512-mKX0DCmQfrI8bczZxj9U3LEdfzYO3HsITd2w6HFGjYAy6mDkhwXQwhtuVyLNdfTztXo6tOgrD4TVpk27kiCStw==", "signatures": [{"sig": "MEQCIGr97hzmmp40P+7bDfsF3ffYTLc0eS2xVL7whWNnU+/1AiAq18QTDuaox9NAuMaxrHr92WDfjjvgpsU7BNyaW020QA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 449704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgg+7RCRA9TVsSAnZWagAAsZ8QAJQAXa/o9PgZMzjG+VMy\nt7CkNk/FCrN7n1mGY1hHY6cJfOtiHRj6k42Prph+BWY/4hGNH96QuoNvfEqy\ngB95Z9cbovusEjnBzvIAGuD/G3VZauxUgyWHO/8olHFY46haLGvtxwLwaqlX\n+wRWoZLGdW7IITcwGCeImde8WCRJL66x4KruQTzDfY+vND3j1PSPzVpeXwtp\nWdQ1HFwKMYGq1kD0CyqCYYwMDkhJ2/iSmV8UADuV44P08mwQ8pUBoQ6PVHSp\nr9zg6q68wl8e5452XYiTm0GmR07MAts06Y0H8TSxRFqsOerOm/LWAaDN927S\nEgp3vnsIBWZfYDzm/FsRNlqsE8M0gA/xER042Bb1Q2sJvHPfsPhwOAz3NN9r\nCLAWj9f1a8SwgcTsav0SYblCph1ZKNb/RlDZQhjX4FtichSEIBE9LJkuHRWw\niAI1MZrxM9UI4REXG5HaPcU0FnohCalUE6pew0U9MpV8yiKttR9DF3Y0aryX\nQM1sRsMaTSAmAdYcpA2bxZrNQaj9Bo8MxZvdlIJXFFRLmW15BIWT7ZxTSqdc\n0qQoUNQg3v3j2UkEXA2kgZRGQOrbsYvuehiU4+2oh+Z7kh98CZg5KRNWRqK0\nuWFct8S5cy0QLRNy+zmYh8nE8QxcrbbBNcJZE9YQIpvW22giGYel0XX5Y2Wk\nZQKg\r\n=1DgF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.2.0_1619259089178_0.6740597317459962", "host": "s3://npm-registry-packages"}}, "8.2.1": {"name": "acorn", "version": "8.2.1", "license": "MIT", "_id": "acorn@8.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "0d36af126fb6755095879c1dc6fd7edf7d60a5fb", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.2.1.tgz", "fileCount": 10, "integrity": "sha512-z716cpm5TX4uzOzILx8PavOE6C6DKshHDw1aQN52M/yNSqE9s5O8SMfyhCCfCJ3HmTL0NkVOi+8a/55T7YB3bg==", "signatures": [{"sig": "MEYCIQDSKRIFjuztIbDLlaFcsOgq7NtDSQW75rUmXsxnIzvEqgIhAKOn+XDZT3HKxpiEqOPEmfmbAZ1zFMxlX/PTh1cFjlY9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 449902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghFVXCRA9TVsSAnZWagAAOUAQAKEUIHLwP/5l0uLe70aq\n1Zt0/o9Gjo1mGLWFmSUXIWHCYjSA0alivnXAZgBSHiLIMmvGToTEugN7hkIM\n1NFuLv4wAKIzkHoL6bF4LwSmJeGlqJD6LBnbhcWD9fuHEtODe0avTkCENwUH\nxJkLC7kwPOAFuynKKB3LEr+9001SOP6FHCm5V9/spNvwfU2zrXaetreoHJYy\nX7Ko5JeJ1LS3D4KoxkVbtS/8P/2Brpl+uYSrwOsevPk890qA8rlyyJQj/5QP\nAuXjMYyx/+jsOoVP38kpq1ejTl6/nAG/k4UdbNVWA38rl1OCSERKX908jL9x\n3CJy1jfbXm8GGe4QvoxJALZ0V1W76Z90/041eCpVUXHxJzgmbssTCCpkastG\nbRRPZTzbww4UhPWkwAM2w5iHQKp6ar8nc5CF1UKSAvPyJ5sJ38iGkLC44Jhj\nfCSpza+Akw1ZkcTZdLwSwNOV3PpDU9UzsTKc2fJZINyKChs7/IlPF6/AMxwu\nK8paWhkfwmyU4gKfmu8KCofC7LcXplnM/nRnwQXdlJZzir0c5cizdwMjeBHG\n0SrXcLpC9ONhWDv7qvmosQlW9lh1tQcjyZ3i2ReNTH4XcOp933bofzXKzdCX\nckkfSQc7rnqaoWDjSNCIxUEXlagOC+2BPOAB8R+f/eMz1mZUPCJ4yKWTyxGP\nOJd3\r\n=6LsN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.2.1_1619285335078_0.9442590214983313", "host": "s3://npm-registry-packages"}}, "8.2.2": {"name": "acorn", "version": "8.2.2", "license": "MIT", "_id": "acorn@8.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "c4574e4fea298d6e6ed4b85ab844b06dd59f26d6", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.2.2.tgz", "fileCount": 10, "integrity": "sha512-VrMS8kxT0e7J1EX0p6rI/E0FbfOVcvBpbIqHThFv+f8YrZIlMfVotYcXKVPmTvPW8sW5miJzfUFrrvthUZg8VQ==", "signatures": [{"sig": "MEYCIQC6DdaHylauQDa0vLREd80mC9pHPdCPKZQc/kLYkX4lxgIhAJgtqh6XlJ195ZjXxIlDi3A3pX9jxDtmaEFH4Te2cjoF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 450297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgioLjCRA9TVsSAnZWagAABvMP/1zhWjw170TC2IeKq6tE\n15LRVA9bV9cun0TUfI+kWnlTRk/x67BXHqOD76OhalYBjfgcoJf7wmL7WsDQ\nYPLytiamF3yxkvpjrzcP10t/r2uXZgA1PR34JybAcjTLyNVQl7V+pdwE5Wr+\nIw/0xnr1N7/ISS2LJkGux84hqLv+K4IUzBG6VRzkmv2Y1q2GJ4ltstE8xJl0\nU01TuYk1hPY8n9C5tzWsfSsB2qvJ0bd61ZOkVGDBFwh1AwpdDjnVWXitCVy/\nHBHUA8XQxl1K++LXmJA79tpuQwOGFlIfreNdUTHqcNoGwQChdqvAQ91/5mYb\ndtvobwlxeCCsjirYVId8+IaHk/oyyMX8DI1Vusiwob/mFqSktzKjNj3DRy1U\nC89hYLAuPG9qyVjmGvOzxsqzDZwPW2uUPKAvHZK9CJwv9OBRhJfbt/2GgwX+\n2rC6CGi2srX0k+0nQq4WTN5UMifaz3/pls1jy5cMk5VNs8oGIumfxKLqcmrx\nNE4fxelrrdpCr1EEh6tchWAOZpBVwfhqTHRqoSb4w83j9KZclZeKXd3XHa4l\n8KkwCtnMq1G3IBxavVr7aX7FvIDbhAlLFgNVHZ6kom0wgl0nGPmjtq1ubnM/\nB/Wm87K9DQk4AXIOz4eoHOdVH74iSXBiT5YGreqo4bIxrW96wwbLmXjcrjLV\n5IkN\r\n=E5Vr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.2.2_1619690211063_0.5474201210801013", "host": "s3://npm-registry-packages"}}, "8.2.3": {"name": "acorn", "version": "8.2.3", "license": "MIT", "_id": "acorn@8.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "37ba3771cfb1ee31a05950eef495259da28658c6", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.2.3.tgz", "fileCount": 10, "integrity": "sha512-IJ3kohgrCGAVZrTAc2ufb2Hk2IAdkZTrMHo9DYDC5hX41HrcavlIL0nx31NQLdSkgAk8yE7oFSwPVYX17HWNHw==", "signatures": [{"sig": "MEQCIChtbifTBSgZDmK7vzOr/eOfxjD1b6NzBbKSWJbft0e8AiBChUY14ZRpnVYS8KW3oMtOPP+qv8tOVh2xPIpbV6ryCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 450904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkR/HCRA9TVsSAnZWagAAEzcP/0DUdV7nGgsyhzBM5Kll\nlsr2kCHjZK7KVx51J+Nbaa4XRgmVlTDb+066G9y+A0LXur22HR3m2VOrcFJ6\n9vyT4ChbEw2LFaPyLVRviCPhSojk5aWPPOjZx8/l9b9PfeQFyxak3tnEAl1l\nacaz5YP+I2koCBt+Y1tZ/4MI4GvtqtwMcOBquoeUilRORPbukRq7NcgWwaMB\nV0XYxaskkrpzS3YT9qzy8F3SEkcbHmjK5OCl1N0JsPtAcV14aXUSEGTYxK0U\n1/avV92J5GxTZKvk2F5dBxLFgWy4sp1M7APzW/ZTcZ0afLlqhxTdwKisjwpw\nVWAx9qV3/npIehZvh9eAPPNM/Dw6sc/xvQQR7g/I6PH5O5VoOvfMEL0CRq++\nl6jT21evUxPpST1z8BmLe8IzOQls3m62MuCEPEPmaKBfVgR+yHN/+GhosXJ7\niR7Blg/Mw4Os39VvcTBqvGLBjHwqn/Regce3CctgzReNGXUFmQIUe4qNB9hL\nfG0r0ZqbPOonOeoSyEBT8ApR5hhdC9RByG6kEBPJTRzAiCp5aVug55FOOwxe\nRGmV/CrkQcf1sqmSEKP10ZNCo8L3OJedLwqX2oCxKm/p9TPCBPRuZ/ZVsj9a\nze1C/g08jwSKzuSJwwCeBJZblv/19m/dQgibULuHixD0yVrmylglP7V8REl9\nHPMD\r\n=AY/N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.2.3_1620123590547_0.8309502139573897", "host": "s3://npm-registry-packages"}}, "8.2.4": {"name": "acorn", "version": "8.2.4", "license": "MIT", "_id": "acorn@8.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "caba24b08185c3b56e3168e97d15ed17f4d31fd0", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.2.4.tgz", "fileCount": 10, "integrity": "sha512-Ibt84YwBDDA890eDiDCEqcbwvHlBvzzDkU2cGBBDDI1QWT12jTiXIOn2CIw5KK4i6N5Z2HUxwYjzriDyqaqqZg==", "signatures": [{"sig": "MEUCIF0CfwwaXmKyvT38D+0g3XwKhG3YszruIUbfpC0vXsmBAiEA+3pF5hnSdcKWbCcZ2s0SCXpbFz2QxrCyMHOPJ+0Cl08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 451226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkUjYCRA9TVsSAnZWagAAA6kP/2DYZ7bLZAfKP6jo7h/S\nkx9OG2TdlXhYqCHWd+XAwwMrZL6wMXPbNvj0eRoobwbBWP+zqnI8EzC0GKQ+\n3jcpEgdzyM6e94ECZ+dDaIDFfRezO2CUoQuWGFAVoAfpe6EEaHrOLJZo/VLh\nWR/bk/5/im0R9+Idl1pdv8KrsWJqJCwZnQMGm2fGR2Gp/wluZuaJz5UAefwP\nkTbHueyQcyVaaRvPYrUzUElWlOW+OvJWDfT+518zbly4Zkzm7gagDQdfBTtz\nXOJ6DK+X6h1k9PkSKhSIEHjIo6zLuqNCK72OOlLlewFDxjOFcrlruXbge+l+\nHMT3gWFUb/oHm6cs1dCTJPfBVwe2vOleHSMBMdWZve1soWmuY2QVs0z4wV8v\nflaef2laDJXoHzn7ZOu48yoAhYwuuG9Edzq2VTPpeQL/VhP314tpITWs/Fpw\nccqJS/JOfTqnj2Iph/u1IPQVC6oTDcue6+Wbf0QY3Hb3KJfEg7BLsuGiwjju\nR6CakkUOqVVwZ16Z7GXsoBq3xXVKF5ScJpa3710Wtm8Fl/hz7+398OPIUdQA\njn/YiZM8qZDeYpc5RskGR1C4nrpruHzamW8QEUO0suf2UiclJDCYx8r8Xxls\nFJJLTAr/M8wp1CJkkAcTm25Gf4VAyU45nk5FdNl8ZRyX7CqSH5/8HQ7DiGhc\nEvmW\r\n=q8My\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.2.4_1620134104316_0.6809546743337827", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "acorn", "version": "8.3.0", "license": "MIT", "_id": "acorn@8.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "1193f9b96c4e8232f00b11a9edff81b2c8b98b88", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.3.0.tgz", "fileCount": 10, "integrity": "sha512-tqPKHZ5CaBJw0Xmy0ZZvLs1qTV+BNFSyvn77ASXkpBNfIRk8ev26fKrD9iLGwGA9zedPao52GSHzq8lyZG0NUw==", "signatures": [{"sig": "MEQCIE/0ku4/fMY9m7eT0xeddJOv4A+W5cf8ubXTEe4CoSc8AiB+7iP3uOCLTF3q2WbnqyWiSwuRLGXa+368+ygJAfZajA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtHfgCRA9TVsSAnZWagAAMDIQAIevs7aVBFIhwAgP2Cc+\n+zJTWpK93MiIWpNsTJwm6KTazwC5Hju+Qqn7tkaA72HVNMN9pSF8kTYaTywo\n3MVsy2iPMObqSv/efebcbAQsJQY3BbnwoGfKMZQYwDXeGuxx6949pixxtBze\nf8XsfKq+8Bx236GI+IVlH8UKVS7q5ucIoZHnHL4/DJfV+UpE1JxEvVr3R+na\na47TvdNoIVSeATfdPgx24lJKfXQPbeBMCuz07pYe7FHRXHn3I72lgx/fzo/C\nq+9ELMK6gK1UJhkikJX71MlnesIrvuY9qytLGzHDnTbzqkZQ0wj1WRdjj2T1\nC6HWGw69eWSez8us/ibtTlCpkgJa78BVM7ITvs3AZyNiq+hagr/wcF6lqimN\nvU/zf3i6K2KV5RpTL6FPNTBmV96H0RvgdUcwywamSi7f+FGnLbJdD4NoylUh\nMXuQm7y6UgHXAJHLzjRCHuLbTHarR88eFc2AQlQP8/v2BbttbxkYlm2tmCBt\n0FpmswXWQ5GvogHTXrc/Rd967unmomVB/JrjErx+25IEM7xKLg6wfZ02vJkX\n73B+twKRVam2/TpSsfr+4OehWgyi3uM2Fu0eFYkm2ZeKMvCM4U8s1wLNidJr\nR2JhOX9/u1HjClRgsRcz709VAim8+u0xi6N+pPmN9gMG0Q5Z3GGU+Tir3Ixc\nBkJI\r\n=Cyvo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.3.0_1622439903621_0.5763506668358733", "host": "s3://npm-registry-packages"}}, "8.4.0": {"name": "acorn", "version": "8.4.0", "license": "MIT", "_id": "acorn@8.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "af53266e698d7cffa416714b503066a82221be60", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.4.0.tgz", "fileCount": 10, "integrity": "sha512-ULr0LDaEqQrMFGyQ3bhJkLsbtrQ8QibAseGZeaSUiT/6zb9IvIkomWHJIvgvwad+hinRAgsI51JcWk2yvwyL+w==", "signatures": [{"sig": "MEUCIQCHMYUjaDnCXv+3hH650z/kMsRatP8t+spwYFUTmTGTbAIgZ7bzEwPDDSoIy0Yw7qQlhQgrMNrtorEnOKX0iHIdnmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgw2s4CRA9TVsSAnZWagAAbMcP/A1N3vDCt36JTHwBbwxW\nf1gwuhSBMdxKfGH4Vc20FYGss0LkppvbDwh7u0LlhEol3/cyLWMUsEwzgpPq\nHry7pNBcoS58qEjwaTNdR3NFKKs8AMpDlV2jnQhgx3jujEFX6ABC9ULZGQTY\nBiOdMvMJaJg+853rTotVmteENfz05Drf3N3wbvq16RVFuOStTENlHbi+7fw6\n8cHdvhkq9586N88MFydQwprqdCizPR4eRMxBC8AzJdW4eSFI858KSorxsL9c\n85tIeANHLEWVI4dP5CYCAwDQR3LeY5SLjkres75qSRNmKUoTrnDDsWZotdwv\nz6W43WnoB3M8Vb4mU4G21/EXO9xkddtFnTTOG2NSQOlcJiah+XlyYCSwav7y\n7koIlcuOuYmzSdvscC7227dEd3PjeFMliKEM9Ho2jnLL+RD08gDX5REpzpcp\nzBzsSlDGBG7dcAUCQILLdIz5++QmsPm/dMERZFWIzXlqwlPQz5OBzq7eDsTA\n5kGjlaD9KDbk/UW6R4zkK5DoAwjzVpSrUb990TLnEMRyS4gHjB6+D/dumIwX\np6cXmZuYamHCCFIlRyihhcW1SDnMY9PN564mw43b5ffE47190c1bnZfsqK4J\nQRIJlxEn9+sfW4k/p3ycIGgJX3eRQKsMeE3crDnzA07UYN2NVLTmb7YYGWmb\ntLht\r\n=E3GC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.4.0_1623419704547_0.9739465978286523", "host": "s3://npm-registry-packages"}}, "8.4.1": {"name": "acorn", "version": "8.4.1", "license": "MIT", "_id": "acorn@8.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "56c36251fc7cabc7096adc18f05afe814321a28c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.4.1.tgz", "fileCount": 10, "integrity": "sha512-asabaBSkEKosYKMITunzX177CXxQ4Q8BSSzMTKD+FefUhipQC70gfW5SiUDhYQ3vk8G+81HqQk7Fv9OXwwn9KA==", "signatures": [{"sig": "MEUCIQC4cocpvVgkM93X3AhdmDx+LZgOi8hhzR18BU4rDWx3UQIgWZ+m5Rai8DGQ5rc6fDufgqTfEy733+Pz9CLazohd3Ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1E+eCRA9TVsSAnZWagAAAHsP/AsN5Vb43HcbMdK+mxt3\nkz9q7Ed8mp2AEVRmCSw+FYPCIvSDlactTqs/JWEsmmfr2ODQGOyMQKw+nM5T\nTDH/mphl0qxltxgHa7jFpRGdSxhRx1XLfKfi1W0MsvV9Zw/D56oOJE0XrC9x\nuOY3Oq6Gi30WKDfLaS/1A1DG3pqKDRNsEykyq3uNMjolUVnO1VyB5m+FSL/U\nXj2JyNtmiDuGF793tMmp+vle/miseukT94pSDWAp7GA8Bh7LH3pIfYVl+umz\n6qsiak+2zga0VcOb26g2qQrg1ftKTFLmwTFqzFZWIYoPz37PjSFDHZlSEvnv\noE/ctpBw9Jo4LyWuHs5+e5VMfp8BoiCwB7lY4zOPDKjQux6I8i/gSUdfb5T5\n4Z/XMQkJyJdqMSoShmqfPRT+VvvAQRAjQEnalvnbhZTf/KWDGKf+O2quYTYU\niTUg72kr7jtc2ALcajWortHhndK31Ztd4dFSrI6cNg9ov/IZubXkhvTIhpy5\npPalSVABGS6UsyjHMDaeQ28dnm7+JfMDMhAtJrgUnYAl1VdkYshWKDvfyjMC\niZmjf14vs/8HRnHf2A0HFBkhMqdC3KGhZ4GTKaNRNqJYzShyWhf3ZnzvNSiY\ncbdGtszEq5K1/2mXVUZ3vJV+v3h/6fo9akl+K1Ne/8dVoFYK2/5FXy3iHpxw\nlpZT\r\n=XJaz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.4.1_1624526750061_0.4957824746322008", "host": "s3://npm-registry-packages"}}, "8.5.0": {"name": "acorn", "version": "8.5.0", "license": "MIT", "_id": "acorn@8.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "4512ccb99b3698c752591e9bb4472e38ad43cee2", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.5.0.tgz", "fileCount": 10, "integrity": "sha512-yXbYeFy+jUuYd3/CDcg2NkIYE991XYX/bje7LmjJigUciaeO1JR4XxXgCIV1/Zc/dRuFEyw1L0pbA+qynJkW5Q==", "signatures": [{"sig": "MEYCIQDqndcQFjEfNtDNUtHPvTj98SlY+Aqw5xvWpvJYvtVN4gIhAOYqs/1EKYheYDTHSSdSWKCKU2gypccDaTQ4tn/ZOGeS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNc7gCRA9TVsSAnZWagAAA4MP/2CQOebTXBltd/DKW7Md\nJ3dwWRevez70lEDSH1WD37Qh67ekoA/pGiLrEWbS4hkeYdvx5uxUsGgnPZNg\nLN6mdUXTEDN+AJZalr4lxZsp0BiXjv1cRVB+Cu/ZohHjGEjvDW3KLUhga8sT\nYcqUogirtKWk80A2Co+SQCvi4wfhHhvzgTfM8uN1dOsbfGGKde19UL0a1hyH\nv7sa69x8pBYlTd44mVxNCaCqWIG0KQhhqVPddY8Vdyq4a9GZpN5jtNi1Wv6R\n6pB9487S4MvjIPa4ScQBmAVRfUfsf6eBejFiEP+hD4sMoeSdLkURZSJ6e8+j\n7U05TkDQfkHhle9VcGD5urchJJGecpNI3OHuiAdfyRVP154kuB/F9YoWxX/E\ny9ZF5Zrsytt3FOJW08BySCbrzZTcFwKwEuqm/UkxQwOAG/d5KiF7wAcNLN4X\nChc80c1AaXhfobGvstq+sh1TxelQPegdHH8H4PEvolTfkLGElK6JKiv9HxJB\nR7UE0jaEpqYj0Ra4NZKgECSOseLwFcvdNt8AzivIiEoael5VF6W3x28yD4SE\n22ouDBl6R2+icOSe6ouRJeKR0xCzRz/1EdI5KcEOdbVaA/K3lLikPjQRBUUk\niKZv/XpDCWjz8DjLU42j8nhcQpYWCjktOI4v7cNRWti7uosR5cTxOLxIA3rU\nP9pg\r\n=MfHk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.5.0_1630916320170_0.8303152334786656", "host": "s3://npm-registry-packages"}}, "8.6.0": {"name": "acorn", "version": "8.6.0", "license": "MIT", "_id": "acorn@8.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "e3692ba0eb1a0c83eaa4f37f5fa7368dd7142895", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.6.0.tgz", "fileCount": 10, "integrity": "sha512-U1riIR+lBSNi3IbxtaHOIKdH8sLFv3NYfNv8sg7ZsNhcfl4HF2++BfqqrNAxoCLQW1iiylOj76ecnaUxz+z9yw==", "signatures": [{"sig": "MEQCICui0yQQGnmfgf8JDFwtWzMo6kSJkrB0lQ/3Y221C3r+AiAgiclp3Q42LzAUqqZ2OFq7+KXdClRJWN3doQcbaCJH1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 462402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlhNACRA9TVsSAnZWagAAab0QAIxvxS4kBKt7ogZzC7cI\nM0MxXsrje2TIzTTLG7QPGskuOnzDCy7v0evscktNKgrnsyxZD+Q03gODvJHF\n6YiwEA4I/3qMMDRlLTNoblIIiKexoC+aXXB3tWup4aCXrSG2J0poUD1oVfFB\nAsW8ApmCjXEMLD71XuIV6EBoH29gNBErX2AWn3Y4v7em1I4ICZTOa82UIPNl\nNFNWNgiiPlHX4Lm+xtK73SIJuOJ3NCEPKcdu10i8z069kNouD0tjoOunuqNt\nPYcZd85PamfR+ZzVpGjk7FfemDeIZSh1SqVJjk7TtdrPVP3RKzwACma2xoZo\nE8DfuYsPQR3SWSMwPwJQevmuoMNSSTa1d4GfsHODvy7rXnOq5BEeEDE9ZKWm\nhj2DG6HBicYY4svEJo1Y/Rzb7BwrvqjzAUoYjsp36n8q+3tc4wUTwSxsdzca\nO4a+DGZc6L7NzXTR1T/2xG0hRP7x7wr8QzmpQZYcrHsasPDIWjx/nSRJkUub\nwZTyIgrdOfuHGFKrgIwJwMNSal3T10Zr7M9JaZAHhOQqRCRjf9arguYFl1sf\ndX73+z79CiZkIshv9c+LDorBnXgPzUQlYisw4lkVzbopT6HVdC1bL89skfOt\nzgDi7hY7ThjK3atoGdFzn1/xs+/OqqiWgK1buDkQjDY8FZRgZSNXKm5XQd4Z\nGPpt\r\n=2Qpy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "scripts": {"prepare": "cd ..; npm run build:main && npm run build:bin"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.6.0_1637225280403_0.8341380101232216", "host": "s3://npm-registry-packages"}}, "8.7.0": {"name": "acorn", "version": "8.7.0", "license": "MIT", "_id": "acorn@8.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "90951fde0f8f09df93549481e5fc141445b791cf", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.7.0.tgz", "fileCount": 10, "integrity": "sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==", "signatures": [{"sig": "MEUCIH/m0BjtButGupBF1vQEAPg84Tz04eqsIeYqoGEo7iIfAiEAjKPNJtKDrdKs6RdQCVy6I0hmn3emTjIPx2MOYuU3tSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 465489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhyZZ4CRA9TVsSAnZWagAAx/4P/jq7O0tFn6l5qnHMmIYI\nlO2eyjjUPOS79TZFsnRl3AHj8Gmj8nYXEMSiKI4i7SW8Eo6RDRU9YKjYEHbN\nkVN4cvMtMW5eawDMoMd/ItYGf2usbX7SQrRw0TF6g52vGC21zMK9/1AS4jBk\nec4ltZTTKQKwOm3KXJZ4EFNDCpBU7k3v3Zc9ansehE20dW7tIreHg1HEheNC\nOxD49oftrgogBpJt5IUem6l56qIR0I7CL+Sstu7cWYWIkW5ypdenmu7AZo4b\n6Fl5muUjPTY0A3yBW7izJSW1CL44Kp7JybQi4tS66iC9RPBmKRWxy3VlD2lp\nn5EWOZCzxrdBesrACwgzuwFTm/i3MJiZiy0NiB6r761RibP3WUjG41KlSKca\nXXVZPwsPd/tTdVljALgnbnKi+ZiGMV3k3v9oChUo62j59QeSOJj1I+Oh172v\nGnmnx3sGN0CGo398kOyF5ZHB4EPHghAPnGYdh7BPLSOR2iPjw6UNid6vZ3cd\nZVZXJXMRkYNfH78bMStS53uGgp9UWT8qLOsJKylBhhfznI1ZQ/I9dkUNwmOA\n9rjDyOq2K1UG8xN6YbSRYMeHj8z3hpKv/7wfC8T/ikYhrvyz8/C24+aOi96t\n4hT0M6tCo0JiC5s3/xgoPbmQck8nLGXE9DuS56w/Mn26/nQ0FBobrLm2BqkU\nR71D\r\n=Mb0j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "2c400025597c7a50e08cad6bcbb41b86fa9f4f21", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.7.0_1640601207977_0.04313651412494157", "host": "s3://npm-registry-packages"}}, "8.7.1": {"name": "acorn", "version": "8.7.1", "license": "MIT", "_id": "acorn@8.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "0197122c843d1bf6d0a5e83220a788f278f63c30", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.7.1.tgz", "fileCount": 10, "integrity": "sha512-Xx54uLJQZ19lKygFXOWsscKUbsBZW0CPykPhVQdhIeIwrbPmJzqeASDInc8nKBnp/JT6igTs82qPXz069H8I/A==", "signatures": [{"sig": "MEUCIQCq/HpmWB3weyMW4xuLUrKuiWoqpDbwZwP33+1WvxI6YwIgWbhjCnSg79MVIEZXApRWmqUa2lTMJERdrerSM6rPkh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ4nwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5FBAAgE/W4Qb19nRPcI4H8ehwDg6YOK4Qu72g92AUwR47tK3A1A9Z\r\nQhdz3AoQywBJewMkckziGEvKHH5gAjas5XLnyudtDFMdGDsqUTPy90QtAqUb\r\nQJjcYvuzR589fczl80J8++XWmkvIRw9pOUCkSeGnzrsXb4z/Cp9HHUGY2xRI\r\nAZmXtrzhijy8t1gFvMejh4WVb9wps/f8r8ZxXlVWvcUWJ8QR6CqEkZivxXaX\r\n7s4eQcdHVpuutz5dLHJLKpOtKCxPpIe3WCwpYl0yWRncnaVFV1sR7gf8QGJW\r\niQD/+SJ5GOe91HW/fSztxgik51y5khPZaNUOr2791vgPsfjOoNR7QsZrarNM\r\nCrsOGQBg3HTAg/s8gFu87Ivqe9CnDHRbpWyOwENchXKoF8yX3JDxmPHWV4y4\r\n+i2JGgR0kj6CotGo5zL6lOJxQJXTQIsO+gzEJlsYxxZHCOucTw9Y+Lwgx9Ha\r\nX+ClOaWRDOF9rtC9kKdZU90DZH7rniZfiOvaBXzp74PXzSnsh3fV9/U3XAW1\r\nirDfsXZWvLwhUyWywiyaJ2tBdM+3WHS76aqVPM5/OgTIavuan38r9Ua0TlDu\r\nvk4xhtajcSbhvTbu/dEdJ1mdYUBJH4CuIssnJLR/RZaQxzVWuCZYtL203SGA\r\nIaqmkvefNqVpnS1SKREVL006k4VAyX7KKvw=\r\n=kna6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "04bdf9a67ebc38e8de717db5311a5401c8f1b6c5", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.7.1_1650952688127_0.05549127261300035", "host": "s3://npm-registry-packages"}}, "8.8.0": {"name": "acorn", "version": "8.8.0", "license": "MIT", "_id": "acorn@8.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "88c0187620435c7f6015803f5539dae05a9dbea8", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.8.0.tgz", "fileCount": 10, "integrity": "sha512-QOxyigPVrpZ2GXT+PFyZTl6TtOFc5egxHIP9IlQ+RbupQuX4RkT/Bee4/kQuC02Xkzg84JcT7oLYtDIQxp+v7w==", "signatures": [{"sig": "MEUCIQD0g8oX1NfsnBEYPL091dApgCqeT1WG02VoN++MawO//gIgFD81nX2XVkq+2pLJcc1OJfPa/5Mq8pQzz32ifmdw4+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2R5kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodBg//aCq1SRPU4iggcmGPwM+MdDbPgn9AfsfVR/2KWnuXj4KIyVSf\r\nktpghhwdDh6A8MORxj1gPaGm7Fim6KYMrIuTq9JBqhtiX4MZ+sb9q/aitVNN\r\nT1qUIYIYayD8955wIaRhrUgkVK17WORgqA5+5DvYDMbyPD96B/zQ2CooZ3I8\r\nc+/QgG40wsrIgDENJZ6/qYzBAzf/0w4ybyyHWk10vr5KGN3G6OWUf8bu3y9L\r\nQHSG3OW2r13mregCMHW+uW1MxfyxEoOlOsEXSQcq8OaMKARrwCGk/InqFecr\r\n/yB76QJ2t3S5xuoAd2V/Sb3LPMny7w1ALH1NijD5bh5/vR8ognKblAMhfjlD\r\n3T5qFcH8HBWbr5WjulLoOGB17OcuaSU2FDUwRVQV+nzaoybXM7CY7NPYdjuD\r\nE6WIMkcIuChvAAg31Kk7/Cr9aL5Mz1ambtVBwsn83gLtXf0Cl5Mbb+1WVb8g\r\nIS4sy7TVEIqMTiDkuF8yku+H3+UQ8W2hMS5oYkJiuspU71GQcLn8iw8KC1+w\r\nfrD6y1FZVSIe8UlIcfok/e3uGdsQPO3C8ynh8aAaKhEeaXRjqHsAGxI6v269\r\nz6nsSnGsKkqA19R/EI/GYJTp1TPsgHaf4Nv65ii/L/EdfiQIRL0D4aftv0MN\r\nNF78ulTZ/utPlNRTISMIdoMXuvlgjaEZcwI=\r\n=QgH7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "18ae3fcd33e7218450686a2a1bb78e39bc3d2025", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "8.10.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.8.0_1658396260601_0.022497570743768902", "host": "s3://npm-registry-packages"}}, "8.8.1": {"name": "acorn", "version": "8.8.1", "license": "MIT", "_id": "acorn@8.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "0a3f9cbecc4ec3bea6f0a80b66ae8dd2da250b73", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.8.1.tgz", "fileCount": 10, "integrity": "sha512-7zFpHzhnqYKrkYdUjF1HI1bzd0VygEGX8lFk4k5zVMqHEoES+P+7TKI+EvLO9WVMJ8eekdO0aDEK044xTXwPPA==", "signatures": [{"sig": "MEQCIH4L+29eCZy101Z1yGnQ4VMx5aPg8nbr/1FdI8kRWqHmAiB+Msnj5hrMvXVAUMJwugJgwKU71nqLOa+F6H0nTsdsbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVpq+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4ZA/+KlFflifHseJSNTJYfDAn5g6zddVO/OZzOfox0CUPx5kwfdq6\r\nVwtO+uN7gI8YiIlcCJWzho4ihroZKdSpkZDLE/kQkS3CWYbYnYmrWknN1+/Y\r\n/L7tLkov+OnRt+55L/B4cDoJN5T/G0IPIU6tKSQQIfbF1fwHKr5WXkVo0sXX\r\nXEhgZFB3/p9HYgg+scDDr+Lg2XB9cbCzNJa43NFYGgeKHKEjZgWafQDgDgxM\r\nyaZJCMruthpXIMnY1+fqj5OjyF1197mRXAnDRAnNAlsGUmK5xVDeOzlO/qQb\r\nfLU2y132323vzk0fKpkoFnGhL/Ysl2Pt4XLI+RppTXYqiiaD53IJkD9AkLMW\r\nGDvPmk0oMkh4usI69YlHcVdDLol43Q67H3MInNO1NVfCTXaaswOkEjJXRfiS\r\nZ1TOWiPwIZIb4ZBDhWpIY16z7yQQgSwQ6cFreynXhJ6qMKEGagpskWrspVFf\r\nUeyJwG1rM4HwPx7pLcpA6pGICPsRe1EmRFEduhUPzYQw5A2c84skQm+4k5SE\r\nnS77Thsq1exC2q3h2jSLxp119CTozJP2JckqIGv8UwRR2buMuhhMT2TijQWD\r\noM2g+rjgLVNxHhs/Ul2Qad0PMYSA+4PuGSFFZOmP5MlGIF7dS/oDhlaSb3Ht\r\ndmvKrIcK4GQbsmRgOubr5K1hVese8K36A1k=\r\n=hzfr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "ceb7bfd9ed3b377976a2aaee95b0bb7bf360b636", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "8.10.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.8.1_1666620094015_0.843853901964233", "host": "s3://npm-registry-packages"}}, "8.8.2": {"name": "acorn", "version": "8.8.2", "license": "MIT", "_id": "acorn@8.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "1b2f25db02af965399b9776b0c2c391276d37c4a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.8.2.tgz", "fileCount": 10, "integrity": "sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==", "signatures": [{"sig": "MEUCIQDIpF6mwJK+DdegdpneBamSRQUlfEhm4WOlNWBFzeKckwIgGupkCeTgX9Qd54hXDCq/qLy4E77JVbAlaJSwMlhyqjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzrTlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm6hAAnP8CHGGIxmKeU/ob9ZO88R9HYTIVlk/IWnAL9HWZ0J7pegUw\r\niBSIMLPhKUt9TDwGNdayOO2xXL8qBHVTrLx6iMrbpxFfk0dZglnENBEdpEME\r\nV8WFrqpZ7k7RXUP55JJ5Yd4tnL6rhO8HhJfbb+D55sXTdHm3T0lJiuXkNpYa\r\njgG6+l3vumLWmvM2yjyTIDG+3kBH1KZ4fQLP8gb1cZOHTZZFAuo2HLtoh8bU\r\nqmmw3BtQNdA4wsr8YpHijH1EK+fnqp3C/vhu/98IlAjqJKD8IZz9Z52Ap4ok\r\nezT/MAxHMmCzD3uDcWEb7TULFdlv791Ow7wO+ON9e7yd5bGKUrQSiQkj9kzE\r\n/f01zjhayEx7m6colgsodZDMVHtLzTbWac3NfiU5hsLJfJi7sUXcLjTqLIrZ\r\nXYnwLunn/YJMEZ4rLgzI/lnu9wMFvMkXCZE9cIRIsHl949tpGKnE0FMf/Qgj\r\nYz3KiNz8ku2cc+bT/75/ErLylJQCTJOr5i/Hz8Ogn9Q+LE1bXYmmTxbXagOE\r\ndYFrQJCOobeh1kdsKdanE+DZcuTgTmMeZOBDHKKIF6Ra7rm1hRmMDKcx8f6D\r\nC5Z1QlzGl22OaKCvVQAD6I0cYlYl5ti4yePm432MIkgLtu6NKGgE3xd+vvB4\r\npaRPLDD0OK4Cc7p3BVrDisJHhpXacrIz9wM=\r\n=trg1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "2282d4be7c8cae733c613a7e1c0478ef103159d9", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.8.2_1674491108998_0.1634104731821986", "host": "s3://npm-registry-packages"}}, "8.9.0": {"name": "acorn", "version": "8.9.0", "license": "MIT", "_id": "acorn@8.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "78a16e3b2bcc198c10822786fa6679e245db5b59", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.9.0.tgz", "fileCount": 10, "integrity": "sha512-jaVNAFBHNLXspO543WnNNPZFRtavh3skAkITqD0/2aeMkKZTN+254PyhwxFYrk3vQ1xfY+2wbesJMs/JC8/PwQ==", "signatures": [{"sig": "MEQCIEA547iy093a3yWQUiec9Lk/qgb1xXdn0M/33bpAd2NyAiB3VF6x3Ao4URwTWOY9tukoc3CFCRuS1aVOcxKznwnoAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492715}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "a354538af8390538f6de8f7905b0158bd2e582ac", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.9.0_1686900434667_0.6341609312756873", "host": "s3://npm-registry-packages"}}, "8.10.0": {"name": "acorn", "version": "8.10.0", "license": "MIT", "_id": "acorn@8.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "8be5b3907a67221a81ab23c7889c4c5526b62ec5", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.10.0.tgz", "fileCount": 10, "integrity": "sha512-F0SAmZ8iUtS//m8DmCTA0jlh6TDKkHQyK6xc6V4KDTyZKA9dnvX9/3sRTVQrWm79glUAZbnmmNcdYwUIHWVybw==", "signatures": [{"sig": "MEUCIQCiLXh5+R3Te7PFTgSYeEy/i/NEEDNEQ73d4Juz9Y4yFQIgGOhTYawAakpOWiAqZXQjd1ePRNTyPUxUTwsKbDBcBDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 493792}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "8da1fdd1918c9a9a5748501017262ce18bb2f2cc", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.10.0_1688545465565_0.6852981326335126", "host": "s3://npm-registry-packages"}}, "8.11.0": {"name": "acorn", "version": "8.11.0", "license": "MIT", "_id": "acorn@8.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "04306e13732231c995ac4363f331ee09db278d82", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.11.0.tgz", "fileCount": 10, "integrity": "sha512-hNiSyky+cuYVALBrsjB7f9gMN9P4u09JyAiMNMLaVfsmkDJuH84M1T/0pfDX/OJfGWcobd2A7ecXYzygn8wibA==", "signatures": [{"sig": "MEQCIE1w83uC6ocBMT96e48VbhEfzbQA1v8YwSeJXmI/JzUfAiBrWW2dIBcJu1Szx0hRvYo+vqPSYzIkSrbnCrYriUKaiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530360}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "e0dd391ca1f674eeb4b27aeef1f728e24eccce53", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.11.0_1698343557548_0.2837045966436993", "host": "s3://npm-registry-packages"}}, "8.11.1": {"name": "acorn", "version": "8.11.1", "license": "MIT", "_id": "acorn@8.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "29c6f12c3002d884b6f8baa37089e1917425cd3d", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.11.1.tgz", "fileCount": 10, "integrity": "sha512-IJTNCJMRHfRfb8un89z1QtS0x890C2QUrUxFMK8zy+RizcId6mfnqOf68Bu9YkDgpLYuvCm6aYbwDatXVZPjMQ==", "signatures": [{"sig": "MEQCIFKN2lnB0k2GHGzEJee/W0LcJ1NNhhemvPkQJjKvO5oAAiBRojg28R1te2Izx+2vb45+5rmIFHAFKwwbewV2+jETUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530721}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "b65dd429f4f9473410c3976bbbdb9b5779c28804", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.11.1_1698352175140_0.24689054991227133", "host": "s3://npm-registry-packages"}}, "8.11.2": {"name": "acorn", "version": "8.11.2", "license": "MIT", "_id": "acorn@8.11.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "ca0d78b51895be5390a5903c5b3bdcdaf78ae40b", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.11.2.tgz", "fileCount": 10, "integrity": "sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==", "signatures": [{"sig": "MEQCIFO9csXkmPJMsO0J3I2t+1i2XHWRFLZPM+Sz3eqsdbj9AiAnzpncTLdtowrQBlay4/Mz1G9LjsITaKVNKpXLRH62kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 530854}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "31967af4ecfd4d77ad1a4e09884513fd234cd39f", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.11.2_1698393930337_0.25308427405680645", "host": "s3://npm-registry-packages"}}, "8.11.3": {"name": "acorn", "version": "8.11.3", "license": "MIT", "_id": "acorn@8.11.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "71e0b14e13a4ec160724b38fb7b0f233b1b81d7a", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.11.3.tgz", "fileCount": 10, "integrity": "sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==", "signatures": [{"sig": "MEYCIQCVkU009j+WyLotaPqTM11qL9bttMB2OmP/HObSCNbONwIhAML/u+IU/tFt9yWOy36AzPD4+Vaydj6ErDwneWsWGVo/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 531357}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "fcb15ff8715481fe02a456685f7b92eba74a4d85", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.2.5", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "21.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.11.3_1703840659797_0.45082892782096584", "host": "s3://npm-registry-packages"}}, "8.12.0": {"name": "acorn", "version": "8.12.0", "license": "MIT", "_id": "acorn@8.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "1627bfa2e058148036133b8d9b51a700663c294c", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.12.0.tgz", "fileCount": 10, "integrity": "sha512-RTvkC4w+KNXrM39/lWCUaG0IbRkWdCv7W/IOW9oU6SawyxulvkQy5HQPVTKxEjczcUvapcrw3cFx/60VN/NRNw==", "signatures": [{"sig": "MEUCIQCTJtVEckhRVMHr0cKIaJxhIHAPmUbb23Fx3/omTsphpQIgTPUsJagR8/0w6oWZBEE/qFvmoI26Bg7AAsfh6GhX1c4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 537484}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "5445810f6dac81f0a5f8a1b36a2f5c312173c629", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.12.0_1718348916038_0.6159571320932338", "host": "s3://npm-registry-packages"}}, "8.12.1": {"name": "acorn", "version": "8.12.1", "license": "MIT", "_id": "acorn@8.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "71616bdccbe25e27a54439e0046e89ca76df2248", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.12.1.tgz", "fileCount": 10, "integrity": "sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==", "signatures": [{"sig": "MEUCIFNAgwAf76UGRoci1ml7ouMTiTX+7IEguK4WCCdidvttAiEAr/MN8uTUljtMi0KUlusJzgYqahCTe2MGnnWPs2DiYkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 537602}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "12ad1164a70dbe804f1ece822f34d100545f1afa", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.12.1_1719987192027_0.8825365736099033", "host": "s3://npm-registry-packages"}}, "8.13.0": {"name": "acorn", "version": "8.13.0", "license": "MIT", "_id": "acorn@8.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "2a30d670818ad16ddd6a35d3842dacec9e5d7ca3", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.13.0.tgz", "fileCount": 10, "integrity": "sha512-8zSiw54Oxrdym50NlZ9sUusyO1Z1ZchgRLWRaK6c86XJFClyCgFKetdowBg5bKxyp/u+CDBJG4Mpp0m3HLZl9w==", "signatures": [{"sig": "MEQCIGI7qmcIqru2RbV3yj5FKuI4D+8JFFpfzs/yZxUGaLs6AiBNqBglJKHlX3MFLS4Oc8CkwBOz26I8AgC/iM4f254qYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538136}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "cc5ec013e7dae1b2ad60cbbb87c1b9e1fe252df9", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.13.0_1729059057308_0.6006114417366586", "host": "s3://npm-registry-packages"}}, "8.14.0": {"name": "acorn", "version": "8.14.0", "license": "MIT", "_id": "acorn@8.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "063e2c70cac5fb4f6467f0b11152e04c682795b0", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.14.0.tgz", "fileCount": 10, "integrity": "sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==", "signatures": [{"sig": "MEUCIC3l9a35FCnhsEvB6lmhV3X89R6p4yuyl/OlbcbqsVBNAiEAoFtAAcViAxs3xO+IBWidxlaIR9m2sCm1pimKFAKk4n8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546788}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "3c6a5a98e8c6dcefef391e6114cc7e64657979d3", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.14.0_1730028229729_0.7267547946361241", "host": "s3://npm-registry-packages"}}, "8.14.1": {"name": "acorn", "version": "8.14.1", "license": "MIT", "_id": "acorn@8.14.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "721d5dc10f7d5b5609a891773d47731796935dfb", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "fileCount": 10, "integrity": "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==", "signatures": [{"sig": "MEUCIAED/xm8km0o8ythCQxU0WEn7mYLV+UdnLing9pLgBTUAiEAidbnWEs4LEjk1f6Zo4lkCiu9T1AacOiFwzC5YR2vEko=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 547473}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "4d4ed27f90168e110b5e505c842e7f5efb774c93", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.14.1_1741179202709_0.949955834182113", "host": "s3://npm-registry-packages-npm-production"}}, "8.15.0": {"name": "acorn", "version": "8.15.0", "license": "MIT", "_id": "acorn@8.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "homepage": "https://github.com/acornjs/acorn", "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bin": {"acorn": "bin/acorn"}, "dist": {"shasum": "a360898bc415edaac46c8241f6383975b930b816", "tarball": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "fileCount": 10, "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "signatures": [{"sig": "MEYCIQDqyUQGpXF/gWY0+fshMUJqabnk3i+MOqZN0g4pzDce9gIhAODnQ9rmuf+bR4rKnuQr3OZSXs4ZCOIvHdHd8lza+a9p", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 554942}, "main": "dist/acorn.js", "types": "dist/acorn.d.ts", "module": "dist/acorn.mjs", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "default": "./dist/acorn.js", "require": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "gitHead": "6dc537416ad628b3959b3ff963fbdcfdb380e0a3", "scripts": {"prepare": "cd ..; npm run build:main"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "ECMAScript parser", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/acorn_8.15.0_1749399784679_0.11531521696336511", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2012-09-24T10:10:49.310Z", "modified": "2025-06-09T04:08:15.567Z", "0.0.1": "2012-09-24T10:10:52.687Z", "0.1.0": "2013-02-14T08:59:47.842Z", "0.2.0": "2013-05-03T09:53:34.809Z", "0.3.0": "2013-06-11T10:40:15.246Z", "0.3.1": "2013-06-11T10:44:07.733Z", "0.4.0": "2013-10-10T08:40:52.887Z", "0.4.2": "2013-10-15T11:12:20.345Z", "0.5.0": "2014-03-12T16:31:54.007Z", "0.6.0": "2014-06-06T20:42:57.280Z", "0.7.0": "2014-08-14T11:23:26.347Z", "0.8.0": "2014-09-12T14:54:30.443Z", "0.9.0": "2014-10-01T21:21:54.797Z", "0.10.0": "2014-12-11T13:53:11.163Z", "0.11.0": "2014-12-17T10:55:27.504Z", "0.12.0": "2015-03-02T10:30:24.506Z", "1.0.0": "2015-03-25T11:20:44.173Z", "1.0.1": "2015-03-25T11:48:05.034Z", "1.0.3": "2015-04-07T20:35:36.681Z", "1.1.0": "2015-05-12T09:43:51.704Z", "1.2.0": "2015-05-22T14:49:28.549Z", "2.0.0": "2015-05-22T14:50:51.063Z", "1.2.1": "2015-05-22T14:53:43.772Z", "2.0.1": "2015-05-22T14:56:03.182Z", "1.2.2": "2015-05-28T07:46:58.475Z", "2.0.4": "2015-06-19T18:45:11.138Z", "2.1.0": "2015-07-17T08:37:48.387Z", "2.2.0": "2015-08-06T11:33:14.478Z", "2.3.0": "2015-08-17T10:41:18.197Z", "2.4.0": "2015-09-01T15:02:56.888Z", "2.5.0": "2015-10-27T21:13:58.540Z", "2.5.2": "2015-10-27T21:25:49.810Z", "2.6.0": "2015-11-09T10:04:44.181Z", "2.6.2": "2015-11-10T08:27:45.783Z", "2.6.4": "2015-11-12T21:51:07.998Z", "2.7.0": "2016-01-04T10:31:25.525Z", "3.0.0": "2016-02-10T09:29:12.558Z", "3.0.2": "2016-02-10T10:49:26.599Z", "3.0.4": "2016-02-25T08:51:25.012Z", "3.1.0": "2016-04-18T08:16:29.088Z", "3.2.0": "2016-06-07T11:37:33.448Z", "3.3.0": "2016-07-25T21:25:15.599Z", "4.0.0": "2016-09-07T08:18:46.709Z", "4.0.1": "2016-09-08T08:11:24.729Z", "4.0.2": "2016-09-11T19:34:03.057Z", "4.0.3": "2016-09-16T08:01:24.639Z", "4.0.4": "2016-12-19T14:33:10.138Z", "4.0.5": "2017-02-02T21:42:27.589Z", "4.0.6": "2017-02-02T23:36:37.320Z", "4.0.7": "2017-02-02T23:53:32.145Z", "4.0.8": "2017-02-03T08:42:44.892Z", "4.0.9": "2017-02-06T10:32:46.847Z", "4.0.10": "2017-02-07T08:59:47.662Z", "4.0.11": "2017-02-07T14:01:26.828Z", "4.0.12": "2017-03-28T10:13:42.527Z", "5.0.0": "2017-03-28T11:58:20.349Z", "5.0.1": "2017-03-30T06:13:56.198Z", "5.0.2": "2017-03-30T07:39:00.116Z", "5.0.3": "2017-04-01T17:18:19.691Z", "4.0.13": "2017-05-24T10:28:40.151Z", "5.1.0": "2017-07-05T15:13:42.478Z", "5.1.1": "2017-07-06T07:53:44.288Z", "5.1.2": "2017-09-04T08:08:06.466Z", "5.2.0": "2017-10-29T09:41:49.224Z", "5.2.1": "2017-10-29T16:38:01.323Z", "5.3.0": "2017-12-28T13:59:08.571Z", "5.4.0": "2018-02-01T21:30:59.558Z", "5.4.1": "2018-02-02T09:19:21.474Z", "5.5.0": "2018-02-27T07:46:26.607Z", "5.5.1": "2018-03-06T11:28:28.046Z", "5.5.2": "2018-03-08T09:22:00.241Z", "5.5.3": "2018-03-08T09:35:18.171Z", "5.6.0": "2018-05-31T08:37:12.896Z", "5.6.1": "2018-06-01T07:49:17.861Z", "5.6.2": "2018-06-05T06:40:30.435Z", "5.7.0": "2018-06-15T10:33:04.212Z", "5.7.1": "2018-06-15T11:34:41.215Z", "5.7.2": "2018-08-24T06:33:46.294Z", "5.7.3": "2018-09-10T09:35:32.194Z", "6.0.0": "2018-09-14T07:13:15.044Z", "6.0.1": "2018-09-14T07:17:22.634Z", "6.0.2": "2018-09-26T06:41:29.222Z", "6.0.3": "2018-11-04T13:52:09.111Z", "6.0.4": "2018-11-05T09:01:41.912Z", "6.0.5": "2019-01-02T11:47:44.916Z", "6.0.6": "2019-01-30T08:43:51.204Z", "6.0.7": "2019-02-04T09:40:38.790Z", "6.1.0": "2019-02-08T09:07:26.083Z", "6.1.1": "2019-02-27T07:11:58.531Z", "6.2.0": "2019-07-04T06:18:05.694Z", "6.2.1": "2019-07-20T14:30:30.076Z", "6.3.0": "2019-08-12T09:41:59.978Z", "7.0.0": "2019-08-13T07:59:18.286Z", "7.1.0": "2019-09-24T06:34:22.464Z", "6.4.0": "2019-11-26T22:17:57.665Z", "7.1.1": "2020-03-01T12:39:27.836Z", "6.4.1": "2020-03-09T10:39:06.965Z", "5.7.4": "2020-03-09T23:25:56.153Z", "7.2.0": "2020-05-09T07:04:41.348Z", "7.3.0": "2020-06-11T07:54:17.900Z", "7.3.1": "2020-06-11T10:24:01.388Z", "7.4.0": "2020-08-03T10:01:23.980Z", "8.0.0": "2020-08-12T12:58:39.202Z", "8.0.1": "2020-08-12T13:03:57.589Z", "8.0.2": "2020-09-30T05:48:24.102Z", "8.0.3": "2020-10-02T07:22:02.326Z", "8.0.4": "2020-10-05T06:18:32.011Z", "7.4.1": "2020-10-05T07:53:26.858Z", "6.4.2": "2020-10-05T08:15:41.072Z", "8.0.5": "2021-01-25T07:40:16.696Z", "8.1.0": "2021-03-09T09:57:26.446Z", "8.1.1": "2021-04-12T07:29:26.309Z", "8.2.0": "2021-04-24T10:11:29.310Z", "8.2.1": "2021-04-24T17:28:55.321Z", "8.2.2": "2021-04-29T09:56:51.208Z", "8.2.3": "2021-05-04T10:19:50.689Z", "8.2.4": "2021-05-04T13:15:04.578Z", "8.3.0": "2021-05-31T05:45:03.838Z", "8.4.0": "2021-06-11T13:55:04.657Z", "8.4.1": "2021-06-24T09:25:50.298Z", "8.5.0": "2021-09-06T08:18:40.297Z", "8.6.0": "2021-11-18T08:48:00.583Z", "8.7.0": "2021-12-27T10:33:28.121Z", "8.7.1": "2022-04-26T05:58:08.266Z", "8.8.0": "2022-07-21T09:37:40.888Z", "8.8.1": "2022-10-24T14:01:34.172Z", "8.8.2": "2023-01-23T16:25:09.166Z", "8.9.0": "2023-06-16T07:27:14.863Z", "8.10.0": "2023-07-05T08:24:25.747Z", "8.11.0": "2023-10-26T18:05:57.890Z", "8.11.1": "2023-10-26T20:29:35.529Z", "8.11.2": "2023-10-27T08:05:30.548Z", "8.11.3": "2023-12-29T09:04:19.934Z", "8.12.0": "2024-06-14T07:08:36.203Z", "8.12.1": "2024-07-03T06:13:12.194Z", "8.13.0": "2024-10-16T06:10:57.587Z", "8.14.0": "2024-10-27T11:23:49.958Z", "8.14.1": "2025-03-05T12:53:22.957Z", "8.15.0": "2025-06-08T16:23:04.888Z"}, "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "license": "MIT", "homepage": "https://github.com/acornjs/acorn", "repository": {"url": "git+https://github.com/acornjs/acorn.git", "type": "git"}, "description": "ECMAScript parser", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}], "readme": "# Acorn\n\nA tiny, fast JavaScript parser written in JavaScript.\n\n## Community\n\nAcorn is open source software released under an\n[MIT license](https://github.com/acornjs/acorn/blob/master/acorn/LICENSE).\n\nYou are welcome to\n[report bugs](https://github.com/acornjs/acorn/issues) or create pull\nrequests on [github](https://github.com/acornjs/acorn).\n\n## Installation\n\nThe easiest way to install acorn is from [`npm`](https://www.npmjs.com/):\n\n```sh\nnpm install acorn\n```\n\nAlternately, you can download the source and build acorn yourself:\n\n```sh\ngit clone https://github.com/acornjs/acorn.git\ncd acorn\nnpm install\n```\n\n## Interface\n\n**parse**`(input, options)` is the main interface to the library. The\n`input` parameter is a string, `options` must be an object setting\nsome of the options listed below. The return value will be an abstract\nsyntax tree object as specified by the [ESTree\nspec](https://github.com/estree/estree).\n\n```javascript\nlet acorn = require(\"acorn\");\nconsole.log(acorn.parse(\"1 + 1\", {ecmaVersion: 2020}));\n```\n\nWhen encountering a syntax error, the parser will raise a\n`SyntaxError` object with a meaningful message. The error object will\nhave a `pos` property that indicates the string offset at which the\nerror occurred, and a `loc` object that contains a `{line, column}`\nobject referring to that same position.\n\nOptions are provided by in a second argument, which should be an\nobject containing any of these fields (only `ecmaVersion` is\nrequired):\n\n- **ecmaVersion**: Indicates the ECMAScript version to parse. Can be a\n  number, either in year (`2022`) or plain version number (`6`) form,\n  or `\"latest\"` (the latest the library supports). This influences\n  support for strict mode, the set of reserved words, and support for\n  new syntax features.\n\n  **NOTE**: Only 'stage 4' (finalized) ECMAScript features are being\n  implemented by Acorn. Other proposed new features must be\n  implemented through plugins.\n\n- **sourceType**: Indicate the mode the code should be parsed in. Can be\n  either `\"script\"` or `\"module\"`. This influences global strict mode\n  and parsing of `import` and `export` declarations.\n\n  **NOTE**: If set to `\"module\"`, then static `import` / `export` syntax\n  will be valid, even if `ecmaVersion` is less than 6.\n\n- **onInsertedSemicolon**: If given a callback, that callback will be\n  called whenever a missing semicolon is inserted by the parser. The\n  callback will be given the character offset of the point where the\n  semicolon is inserted as argument, and if `locations` is on, also a\n  `{line, column}` object representing this position.\n\n- **onTrailingComma**: Like `onInsertedSemicolon`, but for trailing\n  commas.\n\n- **allowReserved**: If `false`, using a reserved word will generate\n  an error. Defaults to `true` for `ecmaVersion` 3, `false` for higher\n  versions. When given the value `\"never\"`, reserved words and\n  keywords can also not be used as property names (as in Internet\n  Explorer's old parser).\n\n- **allowReturnOutsideFunction**: By default, a return statement at\n  the top level raises an error. Set this to `true` to accept such\n  code.\n\n- **allowImportExportEverywhere**: By default, `import` and `export`\n  declarations can only appear at a program's top level. Setting this\n  option to `true` allows them anywhere where a statement is allowed,\n  and also allows `import.meta` expressions to appear in scripts\n  (when `sourceType` is not `\"module\"`).\n\n- **allowAwaitOutsideFunction**: If `false`, `await` expressions can\n  only appear inside `async` functions. Defaults to `true` in modules\n  for `ecmaVersion` 2022 and later, `false` for lower versions.\n  Setting this option to `true` allows to have top-level `await`\n  expressions. They are still not allowed in non-`async` functions,\n  though.\n\n- **allowSuperOutsideMethod**: By default, `super` outside a method\n  raises an error. Set this to `true` to accept such code.\n\n- **allowHashBang**: When this is enabled, if the code starts with the\n  characters `#!` (as in a shellscript), the first line will be\n  treated as a comment. Defaults to true when `ecmaVersion` >= 2023.\n\n- **checkPrivateFields**: By default, the parser will verify that\n  private properties are only used in places where they are valid and\n  have been declared. Set this to false to turn such checks off.\n\n- **locations**: When `true`, each node has a `loc` object attached\n  with `start` and `end` subobjects, each of which contains the\n  one-based line and zero-based column numbers in `{line, column}`\n  form. Default is `false`.\n\n- **onToken**: If a function is passed for this option, each found\n  token will be passed in same format as tokens returned from\n  `tokenizer().getToken()`.\n\n  If array is passed, each found token is pushed to it.\n\n  Note that you are not allowed to call the parser from the\n  callback—that will corrupt its internal state.\n\n- **onComment**: If a function is passed for this option, whenever a\n  comment is encountered the function will be called with the\n  following parameters:\n\n  - `block`: `true` if the comment is a block comment, false if it\n    is a line comment.\n  - `text`: The content of the comment.\n  - `start`: Character offset of the start of the comment.\n  - `end`: Character offset of the end of the comment.\n\n  When the `locations` options is on, the `{line, column}` locations\n  of the comment’s start and end are passed as two additional\n  parameters.\n\n  If array is passed for this option, each found comment is pushed\n  to it as object in Esprima format:\n\n  ```javascript\n  {\n    \"type\": \"Line\" | \"Block\",\n    \"value\": \"comment text\",\n    \"start\": Number,\n    \"end\": Number,\n    // If `locations` option is on:\n    \"loc\": {\n      \"start\": {line: Number, column: Number}\n      \"end\": {line: Number, column: Number}\n    },\n    // If `ranges` option is on:\n    \"range\": [Number, Number]\n  }\n  ```\n\n  Note that you are not allowed to call the parser from the\n  callback—that will corrupt its internal state.\n\n- **ranges**: Nodes have their start and end characters offsets\n  recorded in `start` and `end` properties (directly on the node,\n  rather than the `loc` object, which holds line/column data. To also\n  add a\n  [semi-standardized](https://bugzilla.mozilla.org/show_bug.cgi?id=745678)\n  `range` property holding a `[start, end]` array with the same\n  numbers, set the `ranges` option to `true`.\n\n- **program**: It is possible to parse multiple files into a single\n  AST by passing the tree produced by parsing the first file as the\n  `program` option in subsequent parses. This will add the toplevel\n  forms of the parsed file to the \"Program\" (top) node of an existing\n  parse tree.\n\n- **sourceFile**: When the `locations` option is `true`, you can pass\n  this option to add a `source` attribute in every node’s `loc`\n  object. Note that the contents of this option are not examined or\n  processed in any way; you are free to use whatever format you\n  choose.\n\n- **directSourceFile**: Like `sourceFile`, but a `sourceFile` property\n  will be added (regardless of the `location` option) directly to the\n  nodes, rather than the `loc` object.\n\n- **preserveParens**: If this option is `true`, parenthesized expressions\n  are represented by (non-standard) `ParenthesizedExpression` nodes\n  that have a single `expression` property containing the expression\n  inside parentheses.\n\n**parseExpressionAt**`(input, offset, options)` will parse a single\nexpression in a string, and return its AST. It will not complain if\nthere is more of the string left after the expression.\n\n**tokenizer**`(input, options)` returns an object with a `getToken`\nmethod that can be called repeatedly to get the next token, a `{start,\nend, type, value}` object (with added `loc` property when the\n`locations` option is enabled and `range` property when the `ranges`\noption is enabled). When the token's type is `tokTypes.eof`, you\nshould stop calling the method, since it will keep returning that same\ntoken forever.\n\nNote that tokenizing JavaScript without parsing it is, in modern\nversions of the language, not really possible due to the way syntax is\noverloaded in ways that can only be disambiguated by the parse\ncontext. This package applies a bunch of heuristics to try and do a\nreasonable job, but you are advised to use `parse` with the `onToken`\noption instead of this.\n\nIn ES6 environment, returned result can be used as any other\nprotocol-compliant iterable:\n\n```javascript\nfor (let token of acorn.tokenizer(str)) {\n  // iterate over the tokens\n}\n\n// transform code to array of tokens:\nvar tokens = [...acorn.tokenizer(str)];\n```\n\n**tokTypes** holds an object mapping names to the token type objects\nthat end up in the `type` properties of tokens.\n\n**getLineInfo**`(input, offset)` can be used to get a `{line,\ncolumn}` object for a given program string and offset.\n\n### The `Parser` class\n\nInstances of the **`Parser`** class contain all the state and logic\nthat drives a parse. It has static methods `parse`,\n`parseExpressionAt`, and `tokenizer` that match the top-level\nfunctions by the same name.\n\nWhen extending the parser with plugins, you need to call these methods\non the extended version of the class. To extend a parser with plugins,\nyou can use its static `extend` method.\n\n```javascript\nvar acorn = require(\"acorn\");\nvar jsx = require(\"acorn-jsx\");\nvar JSXParser = acorn.Parser.extend(jsx());\nJSXParser.parse(\"foo(<bar/>)\", {ecmaVersion: 2020});\n```\n\nThe `extend` method takes any number of plugin values, and returns a\nnew `Parser` class that includes the extra parser logic provided by\nthe plugins.\n\n## Command line interface\n\nThe `bin/acorn` utility can be used to parse a file from the command\nline. It accepts as arguments its input file and the following\noptions:\n\n- `--ecma3|--ecma5|--ecma6|--ecma7|--ecma8|--ecma9|--ecma10`: Sets the ECMAScript version\n  to parse. Default is version 9.\n\n- `--module`: Sets the parsing mode to `\"module\"`. Is set to `\"script\"` otherwise.\n\n- `--locations`: Attaches a \"loc\" object to each node with \"start\" and\n  \"end\" subobjects, each of which contains the one-based line and\n  zero-based column numbers in `{line, column}` form.\n\n- `--allow-hash-bang`: If the code starts with the characters #! (as\n  in a shellscript), the first line will be treated as a comment.\n\n- `--allow-await-outside-function`: Allows top-level `await` expressions.\n  See the `allowAwaitOutsideFunction` option for more information.\n\n- `--compact`: No whitespace is used in the AST output.\n\n- `--silent`: Do not output the AST, just return the exit status.\n\n- `--help`: Print the usage information and quit.\n\nThe utility spits out the syntax tree as JSON data.\n\n## Existing plugins\n\n - [`acorn-jsx`](https://github.com/RReverser/acorn-jsx): Parse [Facebook JSX syntax extensions](https://github.com/facebook/jsx)\n", "readmeFilename": "README.md", "users": {"bret": true, "dwqs": true, "iisii": true, "panlw": true, "edin-m": true, "emarcs": true, "hyzual": true, "kkuehl": true, "hugozap": true, "nacholg": true, "tsxuehu": true, "wenbing": true, "baishuiz": true, "fattenap": true, "newmedia": true, "nraibaud": true, "xueboren": true, "matthiasg": true, "timothygu": true, "genediazjr": true, "leizongmin": true, "shuoshubao": true, "battlesnake": true, "crazycatmax": true, "fengmiaosen": true, "flumpus-dev": true, "michalskuza": true, "soenkekluth": true, "xinwangwang": true, "zhaojunbest": true, "brentlintner": true, "floriannagel": true, "liujingbreak": true, "raysix900202": true, "yowainwright": true}}