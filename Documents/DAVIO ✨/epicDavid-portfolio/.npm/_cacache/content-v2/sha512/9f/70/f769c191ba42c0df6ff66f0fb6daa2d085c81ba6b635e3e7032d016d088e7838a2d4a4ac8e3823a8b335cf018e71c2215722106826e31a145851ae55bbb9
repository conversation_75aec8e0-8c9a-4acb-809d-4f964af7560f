{"_id": "@rollup/rollup-darwin-x64", "_rev": "154-9e84bb357d71e41efe8db6bedf90534f", "name": "@rollup/rollup-darwin-x64", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d6df1697f7de6c6a59ea376ab03038eb12d3d397", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-VHgM7ZIOdt1X4yvD6mzYFUWbJC7ao1V7btJ+DcWKvKO8OfxbKtmaR6H+bLHWgr/QXe7okOlKlbAtbW2kKFNOlg==", "signatures": [{"sig": "MEUCIQDc9kTJmL4zFvnCRhfB++L3wuMiNXwwZWKGNggC0w5k9gIgbAjLNitnHtaCVIOmMO4s5kx1YKANKuMDZP58BqMJwPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 706}, "main": "native/rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-0_1690831066781_0.03976652162856542", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d9ef0b20bf9d6190baca9ebe8dcc58a5f1b75166", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-YrNwj+NpU1+0ApbqGGGs0rN/FhMTU2oQOdaFNE/gge7FtwW5fqWrxZSY6In0uASSUqReiGDddBnzFVUl748XbQ==", "signatures": [{"sig": "MEUCIQCnAldFDzKPf5SKiDbry2FwzidKFsfWaabES3pewCmpBQIgQrpve2saukjb5bQ0qaE7Tr0POprMb8PeQsMdSnWg+iQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2163515}, "main": "rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-1_1690865348613_0.20185057181791755", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "518e2155b5eb9f570d0955446baaf72c9b1cffc7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-9XEVjli8yrWtGQXTasS0jMS1GIsD4r5h4/dQyJHXwou/W9F4Q/Pm8WrnR9R9K+PBEtjw+nlZkZlS+LYfpZrABQ==", "signatures": [{"sig": "MEYCIQCF8hxn6bL9g2mFoRhP8uUC4JR7WdmYOkv5GovxeAxm3AIhAIDRjRrLDAwuXmD93v5YOMkX9AunYYG20rEDazmT/GlU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2163515}, "main": "rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-2_1690888603151_0.3772328697407721", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "9b68999578f9277239ed7f1d4c888a65a5bb60dc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-JuUDmFnL9WW7Lpci8URxWjTiNTHaLZIrLvqomsdXRuxL+bmWh0fWgPtVU0f+6dZpvy707pbKdKxQHFv6C8W+Tw==", "signatures": [{"sig": "MEQCIHAV+nOdA9Q3DQhjV35spXJUXCRS1qtZZDsbNQAnH4FgAiAiwCffRxbp740RE3Bc1hCd3RsgBcfmYb2A3XjvxMTJ5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2130707}, "main": "rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-3_1691137028676_0.3307676359302849", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e69788ac9af562d8d26c03b36f7216ab29aad4cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-WojdoAhJXBqm6CvpflvZ1V3hDePmVC23IcpLoPdBDUUQOqcYQcnzhZD2l0IwOSPCjDgdXeJH7zZwvv4qxeW3pA==", "signatures": [{"sig": "MEUCIQCnDBkyE36gLyY7Mo62jt2YeeeMkm01nRbqn+/fRwzuBgIgd5FLuXsMf2cJ9wKULJnu/Aas1B/wxAzRJSiYCQ30PhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2147091}, "main": "rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-4_1691149013963_0.5374485847182247", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "16c8c49276d64a9793f88e1e06c06434f630c103", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-PAHqK7PEDgzRzetdQKvSI2rmLFSM0Nsmifoou2HlYnJx9iRaK5irJKEcVpTUNu0y8KxmWc+dZOszZpaBTJKsyA==", "signatures": [{"sig": "MEYCIQDi9yuwDICm8Z14pJoix/GvmfbkCfJQADWJHi6/01ZEygIhANbazLxAfEYCi7GSO1fBIqxYbsRI9sS1Lrddm/jS2VuI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2608990}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-x64.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-5_1692514620897_0.2724732707363138", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "10db15929262d3a413745d52d5d2710220c16e38", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-2kAbeAKK2MTotgIKDuotfZsJt+LyBtXjyq265FK8yTBr9CUSj10Vu80Dv+yfECyHhfmucgqrawvWUn6wO1Ee6g==", "signatures": [{"sig": "MEQCIFN6h/hrRXzFDRs3iVg2WFw5haU4J5/pzpafVE4bLvUiAiB33v9TErQpsVnAkB/Yvms6a6od2tBktr/bFL4KMyEINg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2608966}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-x64.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-6_1692517909191_0.6282577864845522", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2308e8212fe52e050592319ff6df3c13a3d5730f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-McNXEGQ5TuueVVK7Z+ItQFoiNQtHGoj2tdCWyomLWLm93xkJGzDgvxws67zu9Fn3PTbOroQ/C9EzPoFUv0Ck4w==", "signatures": [{"sig": "MEQCIATkuiPjKGJ+l2Hdi3iyviPzsmskKCrMTYGPZBFYu7xGAiApVq6aold+fmVpnP7LCL7PULkrsykyAMMO+2W7SEpXug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8063888}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-x64.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-7_1692527628525_0.44271122839412747", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "4273c9a16105fa44c70c5d845db84aecf3dd51e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-8PhR8Z8jI1+eLwuFvd8B2CCuSs/Lgqktts4LWGAlM+6RlbcbifY8eZnwKx0iW8Qd9rCHp9XMzhz60b4eLwO3HA==", "signatures": [{"sig": "MEYCIQC0XWXmDwg1MCBb+J5UGPAA+9TqpI1nEsW0bG75qc+9swIhAIRXEyq9qng2tZdQuHxfrIGmaR0PmM58NDbvi1JF+T2X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8063888}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-x64.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-8_1692530560412_0.2492772565846726", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "71403a9a63c6764507b237d7607c62ed741e051f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-uluJ9x8lnALouvIdg0mFcYan938UxwHYDCnTyS9wyouxn2c0KqtJZ1ablyNU1b9X8/B3IXtWfyIvzP24/NLyLA==", "signatures": [{"sig": "MEUCIQDpBWhIKZbtMoism+TEFbdj2Foitz2vphwqn+4qafy/4QIgKDTQoi7QkgxszO7zvbRoIldbrViBRbcb4RHf/fBT9yM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8063888}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-x64.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-9_1692541766124_0.7753474834779821", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d511d4ce7b4adb133417bb87671dab5f14aa4e45", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-SMiF5DJH9SJIU4WG9IaUUoWZE0+Usmz7QUxXuFoWgA/YJEU0Fm3Thsm2pTEco+UF5qlHfUQ4S5d1WV9kMKf+yQ==", "signatures": [{"sig": "MEYCIQC6zFgg5iDWyRtnFc91XK3Jlq0Y+eMnIPuIv5WPSt2rRAIhAJY22CWN3MBUMlQ8XQ9usxutGcSm7ES3gtjYJhPbCIZz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8052276}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.darwin-x64.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-10_1692631824683_0.9618503975469068", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "fd850fcbcfa0c1e93f4f75c971a564ecc62ace53", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-zAEoggNYkiSV5dmzSfZpHi6+Bjr3RvIgzjOjKTh/bU/UPVCnihydQkeAJaM875K3NHmaAwKY1yV7KXe9tFPs8A==", "signatures": [{"sig": "MEQCIGgcfFaURV+HgSoKeTptpEDn/XeoRAht9FjWuaG1B6XkAiAmDdEWNpPSu0vv2XdY0483gwNNI+QG88HGop44Hc+3Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592463}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-11_1692785771119_0.6354217544314016", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d9ecacd72be1e8e7613666244049598618d3bd93", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-STnc1kMQDcd/i1aD28kn/Of8aY3LIFNJyjcBe43wrfQ4Dooqy4XtD+ng1DPYdHFXxzXUPT+i+9+Ct+IuTSEZRg==", "signatures": [{"sig": "MEUCIC1Zz/HtmTLhn2A0quCoLGuE8lzjxV/7WC6goDoz44RCAiEA5+o2bOS4Jf2bdDrhvH0oKGqDSle/8ndHERJ98coLLQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592463}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-12_1692801657067_0.7801884610518082", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3bbcb8a4c0564eee094f08517d422d954edd4dad", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-2pdtaiuoKTWqZxl7oO028pBT5fZpVJyMNnCVxfgyEe+0jHUXOTCPwIwvtA0HZS/kGAtBCoPW69nvW2H2AgDMVg==", "signatures": [{"sig": "MEUCICjZeWY5UNnKFKA+zPjZuwQfFljs/5GB2pESiloUFnZsAiEAiSgYAmngATMQDTBbHVr3ssHTnv0+1j/MOIZy4r5NKWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2608847}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-13_1692892127489_0.8135682522088725", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8c496d5d74d7c66418d72f121bcfb14930b29c1c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-DAHS0xPqxhw21WYPdtJgOdpBmZ7UrPlFy1EAj4dfxHUzQjBhOtmq6y8JIDGpMnuQoVOZG3kiD2G65PPFdqIriw==", "signatures": [{"sig": "MEYCIQCF2uD/p9WWbMcyx0gxGJNcacR6Xo5tGqDsYfVtElDJkQIhANK6ogembOeusxxrTSmNuEycFY4rfDf4iBvlY7QwEYbg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576111}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-14_1694781277735_0.17578749186002862", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "07a8539a4f63c0468b948ed45fd32cefaf379d13", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-4VQJAqFJryL5ynePqxpJy7Ms5c3TDIzlH22prClMX81K5pAjMj9Jcwtum1AmhQw4e6xMU+ApQodzlDNNFghTNg==", "signatures": [{"sig": "MEQCIHMR6Tkc1coI85cP8kFb0jVzmGZCGbz0reG/j6ZngpSjAiBqMevxgo9eoyV2osoD7NMgnlutXgUXiI5mwSPSA4Jlyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576111}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-15_1694783224548_0.19374994449320293", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "ade2504dc6a1d441f6f822864c3caf2fc37ba39a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-JcVYzmp/VGpbpYN6m0l3cTJzefYeOLvJJk0avf+6R7VNOHv4xfFjC1v9RMqABH6dSwoS1Dyq2dS3w0sXdYqOkg==", "signatures": [{"sig": "MEUCIFI1DTGkZscoIIp6IGOPeS8AqYnzSsFDKN8xZCf+dqLGAiEAoGtFHJ+eQmc3n0+Jgun7cABghBEVhv80rOoBeHQ6B6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576111}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-16_1694787449487_0.7363808646798056", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "ef10406605a7bbfed7645d2d2774da6bf14dd85f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-wYwCf8aoBRc0kdgQ3QErzliYLgU6hXxJglerVEfPxodfePakLNtjFYHd3QuRlF/Ogg3LnJT/Ot+M2GQH83bIuw==", "signatures": [{"sig": "MEUCIQCcL8jHnNRYCevV+0nyPzwomr680suXNhg8NmYmg1AaugIgYCLF8HBKKvRsCNgrD4L3KBxFTVo/hsEeQRd0sEA6PSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576111}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-17_1694789959749_0.7731195542474558", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7e0dd93e138cf46ff4c7e76b2b7cb81b84916984", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-Bdz/4tfXc++Tl7ah93J3aw6gkyvvgfdz0mPwzDeYuY8bXHJHh6NIDz/1GvaF6yCTwqNqeHEyyK7iC2k6vo54tg==", "signatures": [{"sig": "MEUCIC4KOXHEYL6HgB75+LwGQVPc4KAZGnVDj9hdy9JWT0P3AiEAiHBAcb4MP385Z8chCMZET/7iYrpXGH1taHI5ExmVLnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576111}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-18_1694794257807_0.6319925817244572", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d07320f01dcd2fa7d066d1a95c8bc94250b55819", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-ZSmUEerczogEsGBRcdoTGREbWJ3yaXDbFqaRUNNnAO8lZjbngy9GIaa/R3VGWRmjd1Qkqs7LnpBaF8SiEmmk9w==", "signatures": [{"sig": "MEUCIBYKEnY/lZmskSY2Z0BqFWbmRyS2vUd2xIUCVWbX21NNAiEAqgJ7D6X14Vl8AlJcqOQ6ENSFGu0sv+ptew1ITlL3rwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576111}, "main": "./rollup.darwin-x64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-19_1694803870133_0.3539821696129042", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "f9c633fb50de2adc2a4387563056cc01a6e98aea", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-cd6u5U5aM+3vpL/1Gm4CclYgFUEy20YUvmgIRgrJMd4rp7z/FZgXr7Kuo6ggJQYsvJokHvL+YsXYdorDlcYRnw==", "signatures": [{"sig": "MEYCIQCe9XCBeMrp28qgzFMK66MhDEfWqoL8UVRWOi0vDBY7rQIhAL1HIQXUsN8bb6xpNH/Gp12KybpI8zFIyLAp0bmGWqxA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592413}, "main": "./rollup.darwin-x64.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-20_1695535849873_0.4081271376164062", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "ae015a887a9232829191ba8f0fe71fa530083509", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-7VpvGPy6IkjamB33dA8W9YcCnSfWNlBLNthWKvw4Rz+QuwzO3wE7FsP0JJXIi4xljnRRTgiEiQBh/DRTp2FyZQ==", "signatures": [{"sig": "MEQCIDOfYM92QupTuRsHMeHh8rVPiKJJeKAeVuD0IAu7r5UGAiANmtT2QP/Y63h1e+y7XgBrd782R9NP5TGg6UHTffQeSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592413}, "main": "./rollup.darwin-x64.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-21_1695576156614_0.6436363958425921", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "ddba6b52f74f00a35fb45ea41d39ab0285446a06", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-9FmwQJIOeyiSc1nY1aYAexBheLnex6/0ZczAj1dbpe9heDMffiKVf0/DgfMnz3eNCWqwR8VJ5vXNRsOD2jVU9Q==", "signatures": [{"sig": "MEUCIErzb8lvKEuR5XGRUgt8IMiE3Euw6P8F7+Q+5qXm+T/dAiEAnkNpa5cL7HAnWxSfp0qJpthQMiedM3zUBITjpSOTR+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592381}, "main": "./rollup.darwin-x64.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-22_1695745067315_0.37209258540639345", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "624c9a7290197b1e67ce075b5958786686b16d32", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-xyZ1WHFgp3xttutYFnk5AMqEGK42uSW6elI11tGi6x3OcQG8WkE6Vq2GQnFNoA8dv0QlZZjldFMBYEQ/ksvBPw==", "signatures": [{"sig": "MEUCIFiLnWDbBLWwYxhd59v8dIIvi0wiqn+8KViSxFLqLciCAiEA2+83CIfeSMl2eUjeFife/V3u+hlX9i2xVyyHDMzoORM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592381}, "main": "./rollup.darwin-x64.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-23_1695759279216_0.6933744552961714", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5e6117ca71cb0aa67e1734c52a3d186f33c15bda", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-8tIz6Uga/5XdeRkid7kfNtxrvru7o4lDBxAPooZezKXbyB2ap2yKAKCqTFEXyTuPhl2yxLMa5zqZ91FBEnSbPg==", "signatures": [{"sig": "MEUCIQC6u9Uc5ZmmaLMfzOZMebSyYdq/jK7/R+c7Xfsf6NixzQIgCzzrWL8TTl2a/QZeoDFMBNLYncxEi1Uzs787qWzbolM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592277}, "main": "./rollup.darwin-x64.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-24_1696309979933_0.18093844789757885", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "151735b8bf51efcd8b164bcd8124031ececf2937", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-rFGYe+A9fNOj2kmbToK0CAaD+pAAbG7TxRJIf5NY6XcwZLLp8jfWxtqZhoFuwgK4OTIZznSqvRbcZs5wAUotkw==", "signatures": [{"sig": "MEYCIQDlQcpHeF8A6wzndna3KzSSi6BXrn/Hw3rJMcjLdv1M8AIhAOBD3pX03QXNNbSfL6H6YiiYwYpyT6sJOKeClrh+uo9/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2575957}, "main": "./rollup.darwin-x64.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0-25_1696515209706_0.7359699200497622", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "262abe210e6c23238a8a7bd70745ff2281eef694", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-rEBuHQ2ejl9gb0//19F88gR7Z9HY2kcCX8jT5LhCHqGqAvlloETXO1FD7DKEdqGz98UtJy6pVAxxeVBN4tlWag==", "signatures": [{"sig": "MEUCIQDb6/YMZK7Pp9vvTpjIcI5NgNj+7TyMS9XHuQi2GHq+3gIgHZlTXFHlz+nxJo0HsR3+VZbdoK/zOmkGZS/zfLNQdr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2575954}, "main": "./rollup.darwin-x64.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.0_1696518906146_0.7062162645408157", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "974d6cd7cf253d3ee42f9f92c922950ddd298108", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-qe4+FV6OhtOrn3gOCfzXN3BpxwJsrQg8SgnbdrtAFQyG9jZm1kTYREc1TvKSpFLGZyC0dtBS21JTS4e1+RXiCg==", "signatures": [{"sig": "MEUCIQCZkSx48UBsFREs5mhffNR7H2WGxokWBu6c4PORLt3DwgIgHgXHW/94ogPH0KPtfgPxCDFQ/J2qvZ2j596v8KE/Kpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559466}, "main": "./rollup.darwin-x64.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.1_1696595817983_0.878154704442001", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3c6988acf7a95b909801e92d905dfbc7f7e73542", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-vX2C8xvWPIbpEgQht95+dY6BReKAvtDgPDGi0XN0kWJKkm4WdNmq5dnwscv/zxvi+n6jUTBhs6GtpkkWT4q8Gg==", "signatures": [{"sig": "MEUCIHSb2mJGma/qUXEDW9LPPIUpEfajlv/QePqqUx7E+2wiAiEA7mI3ZCgpLAKkDqOUKcFQlUR4AeehoPqMiK4PaSa02Y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559466}, "main": "./rollup.darwin-x64.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.0.2_1696601937643_0.3615546403328296", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "fe3e5899c5ae5f2ef58ed585c77ae6c7ec00510d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-bq3nagc+N+IquV8p49eDZE3jFaXt0Fjr7nxBeMkg2lKuzEjUfwN3iTRQIBC+jQT27qa5SegHaawfOf0pfyUWCQ==", "signatures": [{"sig": "MEUCIB5GVAYE8t2seTfU11aojbF+VQcXEueBQ8YowK6gfylGAiEA+wmUidZlrqE5sS9NI+bfHbknKo0pwm18AQn9A6ZfbbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2625130}, "main": "./rollup.darwin-x64.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.1.0_1697262748593_0.7565564818156136", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8b4a3e747890e49eafc1abc8f43d6f303dc36d99", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-YBPQ9qjqkPQPr3oBtU0nRGQGZFTkDY9VnuSjNTlQ1zvVS1X+5zPsLEP+EDZBYyN1IkF3HCFshXJaREwh2VsmLQ==", "signatures": [{"sig": "MEUCIDHFDXRaUf78rwCZRVhTLMl2ewTVmKLraa5SozVa/Y+XAiEAsst4QyQZaYLhrw4bR6wjpvZ8OdUN+lVEFVkmAC0QwS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2740962}, "main": "./rollup.darwin-x64.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.1.1_1697351522631_0.7280979618820878", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "98666b18ddd12f99ee84d72c9e47b4072399c8ed", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-wgWtqQZjtOVuWnDr3BoNqO9mRpgb/XoqSyzqHxA4xSUgj+3cksJzTe7XCTzBoRc2IPB0mWr9/W+QDUu7OWleEQ==", "signatures": [{"sig": "MEYCIQDguy6ehX9zZ2oAeUI3cjI7EP8PWQi39NVkJKO/pgCZWAIhAL4UCM52WfyWIn7ACTraPyvI87UuRTbu96tAHUC78V+C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2740962}, "main": "./rollup.darwin-x64.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.1.3_1697392121307_0.9997236496698385", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5f0f6bd8f0a29e4b2b32ab831e953a6ca2d8f45b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-eGJVZScKSLZkYjhTAESCtbyTBq9SXeW9+TX36ki5gVhDqJtnQ5k0f9F44jNK5RhAMgIj0Ht9+n6HAgH0gUUyWQ==", "signatures": [{"sig": "MEQCIFNmB6i6n8zbmbI2HEG0QOYrXXX+8efEX3y/9zU+LdIZAiBHk+wAHlC6TrtayNypDprRNFG+3PPDprUMZuY1uWdLSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2724538}, "main": "./rollup.darwin-x64.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.1.4_1697430864444_0.5827149416488357", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-darwin-x64", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "76d4b023964bfbf9dcfb9d968e3707841935f198", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-v6qEHZyjWnIgcc4oiy8AIeFsUJAx+Kg0sLj+RE7ICwv3u7YC/+bSClxAiBASRjMzqsq0Z+I/pfxj+OD8mjBYxg==", "signatures": [{"sig": "MEQCIG13xA+dVHcVlNAcW+cAwfFAHetw2LcADk/NgBQo33xoAiAK68MxYgFD/lW4UXtj5bC0ZVprBWKb2TCdbt2np2FZQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2740898}, "main": "./rollup.darwin-x64.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.1.5_1698485027332_0.7037891070307567", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-darwin-x64", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7b837418196b6de0719db056fa6921a28af6546f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-mmeVLqAx5uIvgMvK5IuVC5OEaYslCQyvQ6uep8X+TIyqegJVL2zqxR06Nfr6IT2jxK/cq1M+OH/FqauHec32UQ==", "signatures": [{"sig": "MEQCIB/YZZD7a9lrqjelmFqjyYvFzdG/4NmAJfjMXJ+ZdTIQAiA/0jby0HDYLdlQD3PG76F69q6xxyHtjvGbzO/1nJCtFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2740898}, "main": "./rollup.darwin-x64.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.1.6_1698731130542_0.5730416030781791", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3e8dee93e01f60f380a8d3828acf6eb07c356b1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-BXcXvnLaea1Xz900omrGJhxHFJfH9jZ0CpJuVsbjjhpniJ6qiLXz3xA8Lekaa4MuhFcJd4f0r+Ky1G4VFbYhWw==", "signatures": [{"sig": "MEYCIQCiU60d0bfSiCLIii+aSs7V9oS2WBYesjFaf1ukctU2bwIhAMd0ERbfqsikk5QEkktMqDuqahLSeO+5gLd1HIWz6H1B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2758026}, "main": "./rollup.darwin-x64.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.2.0_1698739856573_0.36386059906726165", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "52ad0db40d9b5ae047dfc08e54e4b3f42feaef82", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-h8wRfHeLEbU3NzaP1Oku7BYXCJQiTRr+8U0lklyOQXxXiEpHLL8tk1hFl+tezoRKLcPJD7joKaK74ASsqt3Ekg==", "signatures": [{"sig": "MEYCIQC2ZPz1Z+7q0bhn1lO/Xaf4EcQ7H+2WrwOh6iX1ca6TsgIhAIzo34q0m9RjJbpsRfjPtfnT4c7Ds5vxqowt7i6g2aJX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2758034}, "main": "./rollup.darwin-x64.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.3.0_1699042399729_0.16802337450977878", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "471c5874f85c732abcca73026a79f1d5a182a8bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-+63fn9QVEHsDz+ZafHN1R7tAjqfVG4LaFEPeHVcM0YWSNc6vq7UOdi7IUTdQ++RZHev5rYm8GTGwJccULX1XnQ==", "signatures": [{"sig": "MEQCIA26i7XP4rYpXZWabGhA35RIclwo0EtfmbuP6Zs2QGORAiApuNbhUQZF5zMpjmDmZPBSNSs1eVoGnkoWdAVKpjKT6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2839914}, "main": "./rollup.darwin-x64.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.3.1_1699689487954_0.19246012413926672", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "0135c8d3c04ae8b19e1310097ac631b5d99bb495", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-7GXsMiX/giTDBMs/gL3rePLBRC6gV7DT7JQ0lNqoNDe5hm+Gm4NEWky9fwEmer64fIUbOsTiLUsyQ5fDXUbXPA==", "signatures": [{"sig": "MEUCIHcWwyA3LibCnQKVvvLWHhUP0fYmmpjBHLPXQ5bT9DG1AiEAgckVu4Ar5QivlcPlLaCYWdqbUA+TzVFmaK71azbQ//8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492874}, "main": "./rollup.darwin-x64.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.4.0_1699775405999_0.4567742299771842", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2868f37a9f9c2c22c091b6209f6ce7454437edf9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-Ogqvf4/Ve/faMaiPRvzsJEqajbqs00LO+8vtrPBVvLgdw4wBg6ZDXdkDAZO+4MLnrc8mhGV6VJAzYScZdPLtJg==", "signatures": [{"sig": "MEYCIQCbcgWwDq1dif2pQSSAmLChPb2i+qXE+T4d1kFziWX9fgIhAOqvnerGhQ5+jaHgTDC8K69qodcOrRgvZNllP2VlMEVK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492874}, "main": "./rollup.darwin-x64.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.4.1_1699939553806_0.001787296426555729", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "66bd162a3fea48cb1cef50cedccfbeee5685b444", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-QZCbVqU26mNlLn8zi/XDDquNmvcr4ON5FYAHQQsyhrHx8q+sQi/6xduoznYXwk/KmKIXG5dLfR0CvY+NAWpFYQ==", "signatures": [{"sig": "MEUCIAj2gGQfk04BGGWqKQaxViKT7dR65lwEMGDN5Qn2fGI4AiEA85bS4w9Gg1pDMmDPzkiqNqCyvCz9eqKsFAzzkZHxkVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509298}, "main": "./rollup.darwin-x64.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.5.0_1700286744661_0.9915968850760803", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e5140b0aaab0ea1424a4c8a1e76442105866290c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-RrkDNkR/P5AEQSPkxQPmd2ri8WTjSl0RYmuFOiEABkEY/FSg0a4riihWQGKDJ4LnV9gigWZlTMx2DtFGzUrYQw==", "signatures": [{"sig": "MEUCIQDCpK3LA8J5cQjgijB3MMNi1D02aMDpEmiNAY5q+lJc8AIgcK9QZC/WTB8cVAesVlvtREh0pB++TV0u64RCB1kWVdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2509298}, "main": "./rollup.darwin-x64.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.5.1_1700597602412_0.5350310367414715", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "ab3a46c846bed784e8f52f253c59dcd70175ef24", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-hlKqj7bpPvU15sZo4za14u185lpMzdwWLMc9raMqPK4wywt0wR23y1CaVQ4oAFXat3b5/gmRntyfpwWTKl+vvA==", "signatures": [{"sig": "MEUCIQC/MSaC3BH3SaQB0GP8swUQX7AUkVRUlje3txLVNe9xWgIgfp9quzVkjTdJfjuq6IJv1UVpz9Jk8Fkh1rthWkq1tXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492890}, "main": "./rollup.darwin-x64.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.5.2_1700807402616_0.43369258009710854", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "b7d0a4bbe6fc493efa269a60a66dc070ac10e2bd", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-+ANnmjkcOBaV25n0+M0Bere3roeVAnwlKW65qagtuAfIxXF9YxUneRyAn/RDcIdRa7QrjRNJL3jR7T43ObGe8Q==", "signatures": [{"sig": "MEUCIQDkObmvJyTv5xPT/ADDV+cQYaHRVhl+3dPhtw/teLk8RwIgPsciZX++BCJEyPPJH3Imn7Lvfu3BMe01Bsgdnh4y7Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492890}, "main": "./rollup.darwin-x64.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.6.0_1701005966451_0.8425616344472739", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "af844bd54abb73ca3c9cf89a31eec17861d1375d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-LoSU9Xu56isrkV2jLldcKspJ7sSXmZWkAxg7sW/RfF7GS4F5/v4EiqKSMCFbZtDu2Nc1gxxFdQdKwkKS4rwxNg==", "signatures": [{"sig": "MEYCIQDjUQI+5d4lB1MFfM3z1GWGS5ppfkMXGqfKbFc6M9S/gQIhAL+8TntsdHxdmlibLBkNMxmpI5hGnbsbbr+8cucsJV0q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492890}, "main": "./rollup.darwin-x64.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.6.1_1701321803395_0.33805587507346146", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "484937c6b987bebaeccdae774977ad4bf7bcd940", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-/ImhO+T/RWJ96hUbxiCn2yWI0/MeQZV/aeukQQfhxiSXuZJfyqtdHPUPrc84jxCfXTxbJLmg4q+GBETeb61aNw==", "signatures": [{"sig": "MEYCIQC4X/u3jURqvTG+hOuycbd+40mLXrH1qFJvxBxyfOH+KQIhAJb/JoXF9QeiSlA93i81+LAdGgnWpYDztMkdsaZcTQTg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492914}, "main": "./rollup.darwin-x64.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.7.0_1702022297291_0.5779110396489782", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "91d7d31d22607c4fcccce9126457d6785c57f7c7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-A/FAHFRNQYrELrb/JHncRWzTTXB2ticiRFztP4ggIUAfa9Up1qfW8aG2w/mN9jNiZ+HB0t0u0jpJgFXG6BfRTA==", "signatures": [{"sig": "MEYCIQC53897ouhd/4J4gnEcOzbRwFdU5dyj0uUAnpISkLLsmwIhAJo/DUJAfFHbEj1YvxwIwOCVvoHYi+qkGyJUVnbLYgO5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492914}, "main": "./rollup.darwin-x64.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.8.0_1702275911942_0.29351201369620883", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "626d7786fe7c10b2e8533ad981b4a791fd72b9d0", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-8FvEl3w2ExmpcOmX5RJD0yqXcVSOqAJJUJ29Lca29Ik+3zPS1yFimr2fr5JSZ4Z5gt8/d7WqycpgkX9nocijSw==", "signatures": [{"sig": "MEYCIQCoC6DuDTSbf9K/YY3PkvOAIvBxuQiiq1JyTDaDhByB/QIhAMi6d/WnWwuVmv6BJCwujG5dgfwLuRyxjqeHGPNdKere", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492946}, "main": "./rollup.darwin-x64.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.0_1702459472481_0.5789869428733809", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "707dcaadcdc6bd3fd6c69f55d9456cd4446306a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-KyP/byeXu9V+etKO6Lw3E4tW4QdcnzDG/ake031mg42lob5tN+5qfr+lkcT/SGZaH2PdW4Z1NX9GHEkZ8xV7og==", "signatures": [{"sig": "MEUCIAe4R72L0F3nLTkn1noUV6Iiz+1f0Q4bHE6g4QDYorhSAiEAw/cJlTDdzHTK9nwzQtREncUqgJw2C5CrKYGL1iz6l0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492906}, "main": "./rollup.darwin-x64.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.1_1702794385205_0.16049460300807428", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1b06291ff1c41af94d2786cd167188c5bf7caec9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-otPHsN5LlvedOprd3SdfrRNhOahhVBwJpepVKUN58L0RnC29vOAej1vMEaVU6DadnpjivVsNTM5eNt0CcwTahw==", "signatures": [{"sig": "MEYCIQD1sj0T1dRXN6rz4Y8zO6rimf52XMTqfkxwQtM5CJPFKQIhAJokDRKbXf/De+8qvnFX2vnZ9x1mRJENztu/VWv/Pb2G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492930}, "main": "./rollup.darwin-x64.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.2_1703917420851_0.7276691473657857", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "6357e22952abc679ea651c8c5745ff7371bfec37", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-5HcxDF9fqHucIlTiw/gmMb3Qv23L8bLCg904I74Q2lpl4j/20z9ogaD3tWkeguRuz+/17cuS321PT3PAuyjQdg==", "signatures": [{"sig": "MEYCIQDODDkKdCWZXesFE82VIBQYBjtfkX394Fn1uSjix1Vb5wIhAJrs4ceXw8wfMHKjgzzlSSC0mtEnxB2/juo91/I071pg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492906}, "main": "./rollup.darwin-x64.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.3_1704435660152_0.15809550720787513", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "97742214fc7dfd47a0f74efba6f5ae264e29c70c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-G<PERSON><PERSON>cukkF38RcYQ6uPdiXi70JB0f29CwcQ7+r4QpfNpQFVHXRd0DfWFidoGxjSx1DwOETM97JPz1RXL5ISSB0pA==", "signatures": [{"sig": "MEUCIQC15WNm8Epw/NJQXfTVDw4erah3rdvns+ER3ZSBq+mTQQIgaJyN++qy/N3HVlPoLapcr+bcwIngO9H3tA8z+0fJjmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2492906}, "main": "./rollup.darwin-x64.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.4_1704523154354_0.9478701178447793", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "981256c054d3247b83313724938d606798a919d1", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-UmElV1OY2m/1KEEqTlIjieKfVwRg0Zwg4PLgNf0s3glAHXBN99KLpw5A5lrSYCa1Kp63czTpVll2MAqbZYIHoA==", "signatures": [{"sig": "MEUCIGN1cbJunZ0qctN4LMQREU0PAkU9/bPJrxTk7kiK8lCqAiEAxwPke0xgc8zCaadyY7UAuHDK79ziJKxByS+SMla5SgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476578}, "main": "./rollup.darwin-x64.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.5_1705040187188_0.6471806241281326", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-darwin-x64", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "a2e6e096f74ccea6e2f174454c26aef6bcdd1274", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-zRDtdJuRvA1dc9Mp6BWYqAsU5oeLixdfUvkTHuiYOHwqYuQ4YgSmi6+/lPvSsqc/I0Omw3DdICx4Tfacdzmhog==", "signatures": [{"sig": "MEUCIQCypgtgVqeDntq7GRcS/Z9BBLB/X2SY1g3yAOzdpKGhUwIgeDiAIdSrgLJewRR/QEJ33Hdo4Dmm+RqzZ61VlJkDMZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476546}, "main": "./rollup.darwin-x64.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.9.6_1705816352638_0.39654301154746086", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1d08cb4521a058d7736ab1c7fe988daf034a2598", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-nIdCX03qFKoR/MwQegQBK+qZoSpO3LESurVAC6s6jazLA1Mpmgzo3Nj3H1vydXp/JM29bkCiuF7tDuToj4+U9Q==", "signatures": [{"sig": "MEQCIGM53UWlNwIdrFaA7ZtYKKQsgAS947rnLR0HKTzXKd4eAiBVscjXc44wxi/mzSq10vj+NQfDGV/9BWaHU1HoX6Nz9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542363}, "main": "./rollup.darwin-x64.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.10.0_1707544736460_0.03430300029390532", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e36d66c6b2d4d716c94926a375d6865b0c0c9c02", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-OvqIgwaGAwnASzXaZEeoJY3RltOFg+WUbdkdfoluh2iqatd090UeOG3A/h0wNZmE93dDew9tAtXgm3/+U/B6bw==", "signatures": [{"sig": "MEYCIQC873KUmwaF3Sji/OCSk2BvfQU37JPbL9aQsziLiRozowIhAOrpezALjNQlXRp8lTWWzOgoL+oXumTeJaQurFWw2HMa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542363}, "main": "./rollup.darwin-x64.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.11.0_1707977399967_0.801293825031578", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c34ca0d31f3c46a22c9afa0e944403eea0edcfd8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-cc71KUZoVbUJmGP2cOuiZ9HSOP14AzBAThn3OU+9LcA1+IUqswJyR1cAJj3Mg55HbjZP6OLAIscbQsQLrpgTOg==", "signatures": [{"sig": "MEUCIQCM0OrBCGyh/Ku/2oeJcTZ4R5JZ+A9M8OA4nhAXwCTF6QIgYKnfJZA9rhYVoBIYwoEG7db4lScjJt/NhOYZUVetFCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2575171}, "main": "./rollup.darwin-x64.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.12.0_1708090362742_0.04374365163858562", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "397dcc4427d774f29b9954676893574ac563bf0b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-opjWJ4MevxeA8FhlngQWPBOvVWYNPFkq6/25rGgG+KOy0r8clYwL1CFd+PGwRqqMFVQ4/Qd3sQu5t7ucP7C/Uw==", "signatures": [{"sig": "MEYCIQDHrw/hTSHN38qkJ8W9hluQt7IU4/T47XkaJbgJJnl38AIhAJuto+3Mas1tA5WeO9TCugr16/1JWP58bUoQOeUGUun2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2575155}, "main": "./rollup.darwin-x64.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.12.1_1709705035208_0.5694761489325291", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3ce5b9bcf92b3341a5c1c58a3e6bcce0ea9e7455", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-U+Jcxm89UTK592vZ2J9st9ajRv/hrwHdnvyuJpa5A2ngGSVHypigidkQJP+YiGL6JODiUeMzkqQzbCG3At81Gg==", "signatures": [{"sig": "MEQCICYu+1fWuOZ+lzcuP8qiILSN4cjfRBFHzVQbBzc5IsDSAiAYPMOPMWirhRTX5Vf56floWTt93RTTSQ7nlXaWPhchxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558795}, "main": "./rollup.darwin-x64.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.13.0_1710221339892_0.5546778412311806", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-darwin-x64", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "97f8da698fe9bb8bb8a32a485c901f8e1f5d622b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-V4Z59kM62DTs84Cs2gpvSdJm4hPE5aExecYG65yedHP+2O5iRiIY60zngtZGWxhnkcWI1ETHOiMwhRIAUMSuGA==", "signatures": [{"sig": "MEUCIEiAtuS7YAdrWTeI4I/1EOn4Hzu3HI35SyULEVwBe9I7AiEAwZGYdCqTmWJdyIOee5A8YMRvUS0BnDDPbDFEcNVjpCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558749}, "main": "./rollup.darwin-x64.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.13.1-1_1711265980737_0.4889099443625402", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7678922773a8b53d8b4b3c3cc3e77b65fc71b489", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-WTvdz7SLMlJpektdrnWRUN9C0N2qNHwNbWpNo0a3Tod3gb9leX+yrYdCeB7VV36OtoyiPAivl7/xZ3G1z5h20g==", "signatures": [{"sig": "MEUCIEdUT2bwP7z4bzAx+l6q1XpvILHWb7Yuer6gZv+kpoy2AiEAwWjq7zqrJpePUmF9qI6VGb7gJR2tfidcdRAxPXULBHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558747}, "main": "./rollup.darwin-x64.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.13.1_1711535283872_0.020201518225703596", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "fcc05af54379f8ee5c7e954987d4514c6fd0fb42", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-yUoEvnH0FBef/NbB1u6d3HNGyruAKnN74LrPAfDQL3O32e3k3OSfLrPgSJmgb3PJrBZWfPyt6m4ZhAFa2nZp2A==", "signatures": [{"sig": "MEYCIQDY7RJxigQVVmPdzmqjoHZWAUhxsueJT7H6h0MnAFCX0QIhAMopItvm78nds7tSOZIYHuudVS3kCc6P2i+FApP6ocR7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558747}, "main": "./rollup.darwin-x64.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.13.2_1711635245671_0.006980567482800959", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8baf2fda277c9729125017c65651296282412886", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-LDyFB9GRolGN7XI6955aFeI3wCdCUszFWumWU0deHA8VpR3nWRrjG6GtGjBrQxQKFevnUTHKCfPR4IvrW3kCgQ==", "signatures": [{"sig": "MEUCIFIzCVJT7QL7NoAETboZ9gL8iC8ubdO8ippbCVr4O/20AiEA3D6eezRm5lL30TvEtjLYD5KhnmiP7U/VZSnCWGSvjN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2575123}, "main": "./rollup.darwin-x64.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.14.0_1712121804405_0.8200930190620486", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "217737f9f73de729fdfd7d529afebb6c8283f554", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-2pYRzEjVqq2TB/UNv47BV/8vQiXkFGVmPFwJb+1E0IFFZbIX8/jo1olxqqMbo6xCXf8kabANhp5bzCij2tFLUA==", "signatures": [{"sig": "MEUCIQDpIGg5clc1dRVpet0KF60oMUVL4JT7IrtjU4gQheaqVwIgMWN6Ijo95T9XKL4Ie9lShM9YU3YHGdGhL9nLM1Zuch4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2575139}, "main": "./rollup.darwin-x64.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.14.1_1712475359116_0.5948753011099306", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "59ebe3b858a44680d5f87546ea2df1c7e3135f6a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-o/HAIrQq0jIxJAhgtIvV5FWviYK4WB0WwV91SLUnsliw1lSAoLsmgEEgRWzDguAFeUEUUoIWXiJrPqU7vGiVkA==", "signatures": [{"sig": "MEYCIQCFoA/mQocNCHBWa1M0YN2fg0kLjWeL08fjTT0H80OBcAIhANOWpnNVEMcVCwK+/wV/nBm9kJOApgSbBR02duw2YaxW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558739}, "main": "./rollup.darwin-x64.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.14.2_1712903039770_0.19504180560663076", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8e4673734d7dc9d68f6d48e81246055cda0e840f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-0IMAO21axJeNIrvS9lSe/PGthc8ZUS+zC53O0VhF5gMxfmcKAP4ESkKOCwEi6u2asUrt4mQv2rjY8QseIEb1aw==", "signatures": [{"sig": "MEUCIQCTKNsrtvpaXAZeU4Iv4GCdGi5nGqsOoCYVHWORxehS9QIgbGbn8aVRLUBxTSbV1j6mhzeoktDeXsgw4WvF9rYE7zU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558763}, "main": "./rollup.darwin-x64.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.14.3_1713165532931_0.9478255893297098", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1035bfbf53e6acf16771191f41c3d3aff089e8f1", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-HnC5bTP7qdfO9nUw/mBhNcjOEZfbS8NwV+nFegiMhYOn1ATAGZF4kfAxR9BuZevBrebWCxMmxm8NCU1CUoz+wQ==", "signatures": [{"sig": "MEYCIQDDfNij0zJxbSfdXptDUWB3cgf43EF5W/batfWTMVFFDAIhAK4tAGIgtJpOME7zyIVkac6Lxyxk4ntU44BUA87SZBPy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624483}, "main": "./rollup.darwin-x64.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.15.0_1713591458409_0.1990272588621349", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7f9bb7ca0f792bcf35d1bf10f0f4a848f9a2d527", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-RIY42wn6+Yb0qD29T7Dvm9/AhxrkGDf7X5dgI6rUFXR19+vCLh3u45yLcKOayu2ZQEba9rf/+BX3EggVwckiIw==", "signatures": [{"sig": "MEQCIDEKI6JWlI1LfbENLpZ38AQmkH6NqexPjoUt97vezMlXAiBvI2l2lZhsAZ/BK8qZYRLb3SmINXDW8CLql11r7wvWTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624483}, "main": "./rollup.darwin-x64.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.16.0_1713674570330_0.34930694591908495", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5ab829322926fefce42db3529649a1098b420fe3", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-TAUK/D8khRrRIa1KwRzo8JNKk3tcqaeXWdtsiLgA8zmACWwlWLjPCJ4DULGHQrMkeBjp1Cd3Yuwx04lZgFx5Vg==", "signatures": [{"sig": "MEQCIG8Ulngj8eupwuEL83ljjS6hTB68veYD6va+vUPMzgyCAiAwpiVJ1hJWSu6dMXQ/bkLyeoqdGKK311ttR8sX4KGIkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624483}, "main": "./rollup.darwin-x64.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.16.1_1713724227077_0.3093721968456251", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "fe45a772526b2c03d545e20f97a1e5cd60a46e52", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-aIJVRUS3Dnj6MqocBMrcXlatKm64O3ITeQAdAxVSE9swyhNyV1dwnRgw7IGKIkDQofatd8UqMSyUxuFEa42EcA==", "signatures": [{"sig": "MEYCIQD+sSWkM3PUZmDusvZI00L/C2RgJLvBf71U9Grq5nhHygIhALPu+gC1BT1yXfyMoYUiVqab94uaITK3lMn/Y4cMONhm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624483}, "main": "./rollup.darwin-x64.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.16.2_1713799185207_0.682368408227157", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "dfba1eeab53dd5665891420d66e7137acb91d9e9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-NPPOXMTIWJk50lgZmRReEYJFvLG5rgMDzaVauWNB2MgFQYm9HuNXQdVVg3iEZ3A5StIzxhMlPjVyS5fsv4PJmg==", "signatures": [{"sig": "MEUCIBhs2FBBEhVWDJIDllDI2X6V4yQNiiogvYrOQ6wjc+dWAiEAiWVLZtPk/oCPM9VunZZ8+kgp+koBHSV+0X6+xw9AS14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624483}, "main": "./rollup.darwin-x64.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.16.3_1713849175443_0.5862939594255165", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "fcb25ccbaa3dd33a6490e9d1c64bab2e0e16b932", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-WZupV1+CdUYehaZqjaFTClJI72fjJEgTXdf4NbW69I9XyvdmztUExBtcI2yIIU6hJtYvtwS6pkTkHJz+k08mAQ==", "signatures": [{"sig": "MEQCIHouYA0Cbffz5GcS4S6EiZv4jmQj+MF1A6Bg28IGVMGUAiAoNbyAgHOkx0VhhK/c0+dYe9duQLQGJeeCbwwrseE6kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624483}, "main": "./rollup.darwin-x64.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.16.4_1713878128255_0.704892725009832", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "4e35f5f2c28ae3e160e1259d473e51b9defa69db", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-3nJx0T+yptxMd+v93rBRxSPTAVCv8szu/fGZDJiKX7kvRe9sENj2ggXjCH/KK1xZEmJOhaNo0c9sGMgGdfkvEw==", "signatures": [{"sig": "MEUCIB0v7Dg+hSUsTw1bGL7B53CXrFQIV1bJvWq6X1EdJPKWAiEAxk3H+rCpLLoMSq8vyhIhi4MptcYyaRtlJMTcjFZ3BdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2638059}, "main": "./rollup.darwin-x64.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.17.0_1714217413606_0.42767839034964616", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2291592328f6a2fb5dba3f1f41a05a078325a674", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-S7TYNQpWXB9APkxu/SLmYHezWwCoZRA9QLgrDeml+SR2A1LLPD2DBUdUlvmCF7FUpRMKvbeeWky+iizQj65Etw==", "signatures": [{"sig": "MEUCIBgQowETWlUCeub+oUP2cneKhbM0mxh1RTIksBXyJ0ulAiEA47uConAtDH2xXLJxyYjZGnMmbQ9uJusNqhRNszdkwro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2638059}, "main": "./rollup.darwin-x64.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.17.1_1714366695144_0.4655893760938141", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "f64fc51ed12b19f883131ccbcea59fc68cbd6c0b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-AtKwD0VEx0zWkL0ZjixEkp5tbNLzX+FCqGG1SvOu993HnSz4qDI6S4kGzubrEJAljpVkhRSlg5bzpV//E6ysTQ==", "signatures": [{"sig": "MEMCIEWvBp6msSiBcHGanIhPnJ4y+wHHGnhJEpob/jIAFDj2Ah96B/YZUkzOdfRymtNbbcI3KWGueD5/ctlLPdpSdQC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2638059}, "main": "./rollup.darwin-x64.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.17.2_1714453272935_0.12044757448308041", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e07d76de1cec987673e7f3d48ccb8e106d42c05c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-n2LMsUz7Ynu7DoQrSQkBf8iNrjOGyPLrdSg802vk6XT3FtsgX6JbE8IHRvposskFm9SNxzkLYGSq9QdpLYpRNA==", "signatures": [{"sig": "MEYCIQD18BeggczH3Haifw0axJiaUdZsH9MPz+CxsSpmscPZVwIhANw8g65NKTHPWc1j5Ys2jCoHA4EKfCMQaGrFinlP7W6p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2638195}, "main": "./rollup.darwin-x64.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.18.0_1716354248330_0.0368823818150803", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "47727479f5ca292cf434d7e75af2725b724ecbc7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-IgpzXKauRe1Tafcej9STjSSuG0Ghu/xGYH+qG6JwsAUxXrnkvNHcq/NL6nz1+jzvWAnQkuAJ4uIwGB48K9OCGA==", "signatures": [{"sig": "MEUCIQD4XTCVC8ncKwO642C0aFA3Z5T7+kXKSXn+ogFX744wbAIgaqXjwk+OndhMEmdIMmkzYsej5fqqybCNALCz/InUixs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2555899}, "main": "./rollup.darwin-x64.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.18.1_1720452336119_0.7059364781106094", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "13fbdb15f58f090871b0ffff047ece06ad6ad74c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-fO28cWA1dC57qCd+D0rfLC4VPbh6EOJXrreBmFLWPGI9dpMlER2YwSPZzSGfq11XgcEpPukPTfEVFtw2q2nYJg==", "signatures": [{"sig": "MEQCIGlnZSe54A4lw/CEow1mNoHI08I413b/Q6Zdglt2TJ00AiAt3nlONq/kJSPKygfue5UHGjAq1f2fBgyT8kdvX7B1vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2490267}, "main": "./rollup.darwin-x64.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.19.0_1721454397943_0.3280429862815175", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2b0a0aef6e8c5317d494cfc9076d7a16b099bdcb", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-4T42heKsnbjkn7ovYiAdDVRRWZLU9Kmhdt6HafZxFcUdpjlBlxj4wDrt1yFWLk7G4+E+8p2C9tcmSu0KA6auGA==", "signatures": [{"sig": "MEUCICIeRof5hGTquMdItGHDwqJYCBR1tEB9whiZjd+8qwUbAiEAv5i9iB/pMUFA1kMMayDEkKXD/b8QgMpOIKjHVmOve3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2431891}, "main": "./rollup.darwin-x64.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.19.1_1722056062994_0.34826984056252974", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "70a9953fc624bd7f645901f4250f6b5807ac7e92", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-52udDMFDv54BTAdnw+KXNF45QCvcJOcYGl3vQkp4vARyrcdI/cXH8VXTEv/8QWfd6Fru8QQuw1b2uNersXOL0g==", "signatures": [{"sig": "MEUCIBrYjMJso1RcjhRBMK40bEzuWKUTO1BT3hlmuu6sUNH3AiEApOp+bNKs8zBWpAOslHKXNWQgwI9eMZZdKPvpXgIcwEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2431891}, "main": "./rollup.darwin-x64.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.19.2_1722501196775_0.8302213042603617", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d8ddaffb636cc2f59222c50316e27771e48966df", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-xbrMDdlev53vNXexEa6l0LffojxhqDTBeL+VUxuuIXys4x6xyvbKq5XqTXBCEUA8ty8iEJblHvFaWRJTk/icAQ==", "signatures": [{"sig": "MEUCIELVFLvpgZKm3gBC9ueX2kW8/FolZrUrUcog+DxzXYQgAiEA0QZXprcyYm20+zSx3EfPItBoZb7BJfI2CYnGFyVCsKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2431891}, "main": "./rollup.darwin-x64.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.20.0_1722660554371_0.6272950496484231", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "34b7867613e5cc42d2b85ddc0424228cc33b43f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-7doS8br0xAkg48SKE2QNtMSFPFUlRdw9+votl27MvT46vo44ATBmdZdGysOevNELmZlfd+NEa0UYOA8f01WSrg==", "signatures": [{"sig": "MEQCIGycaOqUyOPQDmFE1U5WBzZU9fbtvPW8K3xT03J4LbcOAiBeUTb6GLMb5us2sHbt6SO8oNo2RtvzmQXPjcGq5LZXnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2357971}, "main": "./rollup.darwin-x64.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.21.0_1723960555295_0.0531333450238729", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "897f8d47b115ea84692a29cf2366899499d4d915", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-dO0BIz/+5ZdkLZrVgQrDdW7m2RkrLwYTh2YMFG9IpBtlC1x1NPNSXkfczhZieOlOLEqgXOFH3wYHB7PmBtf+Bg==", "signatures": [{"sig": "MEQCIFBwzRlygAb1MoyoDJba3FMg923dE+R6EBY0lFuuvmDgAiBKmn0SHnVJf0S0RrWbs4rbkPU1ltEnSmLbx/OU4/cyig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2357987}, "main": "./rollup.darwin-x64.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.21.1_1724687677397_0.7280884846854148", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "a972db75890dfab8df0da228c28993220a468c42", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-ZbRaUvw2iN/y37x6dY50D8m2BnDbBjlnMPotDi/qITMJ4sIxNY33HArjikDyakhSv0+ybdUxhWxE6kTI4oX26w==", "signatures": [{"sig": "MEYCIQCLvB5xzY9TNMmZyd4QFzh9gCKrk8oc1UBYpxSgJ5Ja8wIhAM6GwfydwihPykIbjKZxYFHHAOmiJ5qPiqC4yq+6i1qO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2357987}, "main": "./rollup.darwin-x64.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.21.2_1725001488643_0.3155761676057802", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "0ce8e1e0f349778938c7c90e4bdc730640e0a13e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-L1M0vKGO5ASKntqtsFEjTq/fD91vAqnzeaF6sfNAy55aD+Hi2pBI5DKwCO+UNDQHWsDViJLqshxOahXyLSh3EA==", "signatures": [{"sig": "MEUCIQC8AbNmHlC2U5GN4lLrnk4OWSfDkTOBFjzBavM5RJYq0AIgLsEiQyUP58S009bSBCqGqbhTLVPFEEQL0CXJUW2Q/GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2325235}, "main": "./rollup.darwin-x64.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.21.3_1726124774021_0.15584709062964586", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "453f345899cbf544aa0d6f5808d24d2e42f605b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-h0ZAtOfHyio8Az6cwIGS+nHUfRMWBDO5jXB8PQCARVF6Na/G6XS2SFxDl8Oem+S5ZsHQgtsI7RT4JQnI1qrlaw==", "signatures": [{"sig": "MEUCIQC6AsJDOz07slatdOFtpELKefKNLG86cV+kv/UAq6M4/gIgF6pCWXHsIUNimWB7BfFP2YzIYfIhSwa3J2fUFG/fqq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317043}, "main": "./rollup.darwin-x64.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.0_1726721754405_0.06281333943414347", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1fb4690fd64460c5687b291011bf2d8871e69e5a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-LSbJhEOTz557VBcJOWspdGyiFbMTNgLxbWnup7bDj1elpNTK04E3M1qLlvGzPKPmk+uG6XlbT8xAUSKkyn0g8w==", "signatures": [{"sig": "MEUCIC8PRbxNr3siRs9sZiwyEFY7R5WmW5GQCik7345sM0SOAiEAw236ULJaM7fOrX5MB43wfm/APzyfOOx3nWcWHVyZpZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317059}, "main": "./rollup.darwin-x64.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.1_1726820535626_0.7166354889422917", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "22ee27a0ccfdc045c2a37f6980351329516ce119", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-1esGwDNFe2lov4I6GsEeYaAMHwkqk0IbuGH7gXGdBmd/EP9QddJJvTtTF/jv+7R8ZTYPqwcdLpMTxK8ytP6k6Q==", "signatures": [{"sig": "MEUCIQChr4OceHc0R40Vnc7E3GBG08dOvjMqAAhznf7P0oZzCgIgQniDQo5sIRjQmPA3sgSxf/ak0WxQgDcihbnXxRB9A4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317059}, "main": "./rollup.darwin-x64.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.2_1726824848444_0.05464032768157279", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "03d6370b458cfa5adc074defc26187d76947b2b4", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-50T9syQGspy6pcJpnaso3iYxLjsnFb4rdOkSFQzxTIxr2cJO8AqTboO7XBJ1fu0yzC7zOH80iPq4VJy/6sRYbA==", "signatures": [{"sig": "MEYCIQCqmV7nAQuX/FGPYSkEaXUK0K65yS17EH8Asq5IyjquDgIhANJDxHBsVOM32hJdM41ywAkMvAH8wPz5uYRhmqs63sWc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317061}, "main": "./rollup.darwin-x64.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.3-0_1726843702489_0.5793938095905125", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "647704fd7ebedc8a57d1c9f458da1c4ba1e04844", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-6MvaCC++JZcKWZ/BdgugJSnt03DMFwoKhAb4QADDkV+iQR/utqPrEgisAdleTb21MrgBi5XcgMPiUtrhNlNSTw==", "signatures": [{"sig": "MEUCIQCUKkp1uDxqg7H8TqarMRMo6vb6CK8L1kGM5elgcqtj2wIgQW6ZiWqvc9/6AkRxaty/YSBU86UERDpAhZv6gglsbYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317059}, "main": "./rollup.darwin-x64.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.3_1726895013793_0.2079268802427987", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "42bd19d292a57ee11734c980c4650de26b457791", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-aJJyYKQwbHuhTUrjWjxEvGnNNBCnmpHDvrb8JFDbeSH3m2XdHcxDd3jthAzvmoI8w/kSjd2y0udT+4okADsZIw==", "signatures": [{"sig": "MEYCIQCLd0yf44trI8Z/snykLm8sbUzRaXv687sXyp+QMh+XSQIhAOddUe8Bpb6RW8uAi4eThQcEvjXUYFUv3M5a31ngI2fe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317059}, "main": "./rollup.darwin-x64.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.4_1726899103694_0.9291159843035903", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-darwin-x64", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "b2ec52a1615f24b1cd40bc8906ae31af81e8a342", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-D8brJEFg5D+QxFcW6jYANu+Rr9SlKtTenmsX5hOSzNYVrK5oLAEMTUgKWYJP+wdKyCdeSwnapLsn+OVRFycuQg==", "signatures": [{"sig": "MEUCIQDvwI9TbmZPcAQ/KopOvVCDnrkExNU0XCXIOFXpjerVIAIgVXSB/R0Mbo3CPj0dEd1p8ahOwdfxVi49vPT0u9s4sTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2325291}, "main": "./rollup.darwin-x64.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.22.5_1727437721765_0.5252932604373195", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "272b6787d8a356ac8460738c03b0281af75ed73e", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-PfmgQp78xx5rBCgn2oYPQ1rQTtOaQCna0kRaBlc5w7RlA3TDGGo7m3XaptgitUZ54US9915i7KeVPHoy3/W8tA==", "signatures": [{"sig": "MEYCIQD9H1jJ8coFo1m2TVOSswq61COuxyj47n228U/rlbEtfAIhALcvajy1QZSQOZqJdBHEi4l+Mf5eZNiC3Cby3p/nXxUE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2325291}, "main": "./rollup.darwin-x64.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.23.0_1727766641283_0.5055508335461272", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "0605506142b9e796c370d59c5984ae95b9758724", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-X6/nOwoFN7RT2svEQWUsW/5C/fYMBe4fnLK9DQk4SX4mgVBiTA9h64kjUYPvGQ0F/9xwJ5U5UfTbl6BEjaQdBQ==", "signatures": [{"sig": "MEQCIGa9iQzJnGDIgtClzb7MPIBiJ0PyNfm2WPcx68h2nR4pAiAMb/HCp5zSJJefKJciB+qWah8i5/1Ng3Wug1aH9zIBaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2333491}, "main": "./rollup.darwin-x64.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.24.0_1727861865416_0.6492843974770801", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "75e0c626b9c610e457e9d12fb8aae068a81ff348", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-Ufz0fX79W9937euBI4qEdh2xLb0Lzo4GiZ7xxDpueEZxWdPbow6gnTRokSzSgtqRFs1vFgcgm7Ci/KnOo15MIg==", "signatures": [{"sig": "MEQCIEKEWRLZr5ARhaI57z4dc3T8MQnCMisGHE7dNyh3tEGKAiB7mIzZygLbLu15+Hu61C3QOZcN9TFkw8IzaNsJrHFAhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378515}, "main": "./rollup.darwin-x64.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.24.1_1730011409812_0.8760895638620503", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3f4987eff6195532037c50b8db92736e326b5bb2", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-1F/jrfhxJtWILusgx63WeTvGTwE4vmsT9+e/z7cZLKU8sBMddwqw3UV5ERfOV+H1FuRK3YREZ46J4Gy0aP3qDA==", "signatures": [{"sig": "MEQCIG5/RJvilVtSqdeu8PbGeoCCqaguzAcHw4z0rGjRkCIFAiBKUtXu4hKZPzGRSStV6PnhNSFTroYAE+73vwnhVQwxJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378515}, "main": "./rollup.darwin-x64.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.24.2_1730043637181_0.5825623382297962", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "29c4220fdcfce68b079165df09e0773e6bca2c83", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-+7DGRM5GzMMBj8UJbV4xYgX8YrsaTROEcAdwjSU+gUM3CSzj0T4rQJrW0i0UkkIYf1L0zy/oTFUHG6HF+Oz4Vw==", "signatures": [{"sig": "MEQCIErG3uSoeCXUg1fsLbyrYOZULNHh3gF/1KFzgehIReomAiBoL2CPa4hVjTnUq0lkO+XQ70+j17mF3lI4zu8vj43RIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378517}, "main": "./rollup.darwin-x64.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.25.0-0_1730182539610_0.7114380851063853", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "4e98120a1c4cda7d4043ccce72347cee53784140", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-KO0pN5x3+uZm1ZXeIfDqwcvnQ9UEGN8JX5ufhmgH5Lz4ujjZMAnxQygZAVGemFWn+ZZC0FQopruV4lqmGMshow==", "signatures": [{"sig": "MEQCIApQCkab0bsX08+oOmiz8m3MO6NaOLHjl7zmnjNYp3EbAiApZngBFh2puMuXlihhUPaAteat4IYwRXlO0MHjB0k35w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378515}, "main": "./rollup.darwin-x64.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.24.3_1730211277911_0.7284392053659232", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "f30e4ee6929e048190cf10e0daa8e8ae035b6e46", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-N6oDBiZCBKlwYcsEPXGDE4g9RoxZLK6vT98M8111cW7VsVJFpNEqvJeIPfsCzbf0XEakPslh72X0gnlMi4Ddgg==", "signatures": [{"sig": "MEUCIChq6uTPAPkJc1OjfLPNvGoBXzfISZCqGmjScsZWumtdAiEA+CZX7HCNREwVVcY9/NdKSY3xI+dgC8Ea+w4H46V3Ec8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378523}, "main": "./rollup.darwin-x64.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.24.4_1730710056706_0.9867411763692144", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "28e6e0687092f31e20982fc104779d48c643fc21", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-ZRL+gexs3+ZmmWmGKEU43Bdn67kWnMeWXLFhcVv5Un8FQcx38yulHBA7XR2+KQdYIOtD0yZDWBCudmfj6lQJoA==", "signatures": [{"sig": "MEUCIDZ6SzRKPR1VnZhEnIEn/MCuCxbwq3c71yItpCVBybYeAiEArI9oca7UmIj9STbzCi7Ji22LCDXVjFJ1CTi2bzxK2e4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2362107}, "main": "./rollup.darwin-x64.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.25.0_1731141471013_0.24062281835964017", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8131b174ca8cec04e2041e42eb8382afe31095c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-wbgkYDHcdWW+NqP2mnf2NOuEbOLzDblalrOWcPyY6+BRbVhliavon15UploG7PpBRQ2bZJnbmh8o3yLoBvDIHA==", "signatures": [{"sig": "MEUCIQC77Z0LYeEEqHg7C63IhlIoJc1wswaU73YymEARdGLxpAIgXL2oWh9hwnoWh38KPrjIuW2ktkVXXYffB2ajADB2Qv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2362107}, "main": "./rollup.darwin-x64.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.26.0_1731480326951_0.10326232492399168", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d0580a0841d70068100bd7ee459fc950df3d74a7", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-pi9sJMCHvCrbKRicn/46gDkf0mMib1l8C1gVN2tqFM+5WgYQlubD/bl25zsEeBZT9wmQRCaZGrFMPAA+2Z/MIA==", "signatures": [{"sig": "MEQCIAv93kVk/4zt0071lHEqMonij+DVIadqzfv8Xvju+1YEAiBRYlxU660W/CGOpTiGUzGFY4kOLab0b2kWnp3fv1s3WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2362109}, "main": "./rollup.darwin-x64.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.0-0_1731481421443_0.0223263804978735", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "4a476773d6ecce2ea6ac4241018d0d11845fc829", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-tadhTCNzDTtIQ5HjJOYcmugqhx7+3tKA4ulVnaXrJd6wAAMQL5/s8eAN2IyQU9zLUiA1vG99MoVOGuop+LoFxA==", "signatures": [{"sig": "MEUCIGWYF5e1yWdJgPUFjk0jgEomYzOn9+aD3NHzkZozfiRqAiEAsYjolYIQCfwoUTNOWoyPTs02nK9h4oZX5221Lt8FSwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2362109}, "main": "./rollup.darwin-x64.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.0-1_1731566018975_0.5015018936072042", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3fb5368ecba86b2f085f6b9a235f70a8bf2c08e5", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-VAjOnHUwpvxf3XT33sMpsLGKq24Rz1sTQhLuUicYrV9pxB4TNi0w11qAGPOyR+dQu/iZf88DmEmG0+2Gaqa1gg==", "signatures": [{"sig": "MEUCIGaegYGQURHgJVBh+tp7xsHTvLiC/IDfqM26lDzS3tMYAiEAvTeaDV6ioAMhPqKJR9XN83d2iK62+NNdPobItV9aIaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378531}, "main": "./rollup.darwin-x64.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.0_1731667263153_0.1962592218685717", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "9918406db3aafa9c948d352ce926f6c9e19c0492", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-IbvEl6Eu9S6aOXP2+HQShXexiF8cexsBfp5Ha75eittGY2vSDZxJhIfaUTOE/Tgf3N3etyvD5BQIEtg0j2QP+g==", "signatures": [{"sig": "MEUCIAc/kMe3BUtPALFlY+91pM5O6w+MWzjhfxCkDzQ49GF/AiEA6QfqDMfgQt9m1jXy6DnQ7/3yJTjA5RRiNfJWuOmTPIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378533}, "main": "./rollup.darwin-x64.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.1-0_1731677317852_0.2167453619342108", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8af65f4f21ad6b2ee087073810e44e291cd3ebab", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-tOVrvYgUNfRP1NOb5UmuTXj2SM7FYn20wDd3prSHkswz6BoQblGNlgL57ewjzedYmeZbG5y+Uu00sP4ppD+ctQ==", "signatures": [{"sig": "MEUCIASOBQOxW4ZvvWyIdsKuFCR3NBgyDK+WOcRFQeRnmYVRAiEA9CJ0OIVDSjqnWkJSzD6+0Y8IBSCbBZwzGVtSTufe6mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378533}, "main": "./rollup.darwin-x64.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.1-1_1731685112027_0.7002882895199463", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8304d27881a5c31f228e041b0483c5d6d369acd9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-0KA3hHrcqdnAfyRb0bl6ifXTNoiktWR6mYKWxiCJZmUMrmR90M2Y11w5lDcjatmflo98iI0id0TztTuHcZKqRg==", "signatures": [{"sig": "MEUCIQDxma6ZCl3P/HCY5u6SF8n82g+ZVAHBl1hpKNkJnuilBgIgfkYFKn7vjXQO/boFvbyIrpZG8W/LLQXHv5xm5u/h8dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378531}, "main": "./rollup.darwin-x64.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.1_1731686890525_0.941570998995032", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "268f23bc2beb3be98135ab499e4e6cdaf7c8993f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-Hj77A3yTvUeCIx/Vi+4d4IbYhyTwtHj07lVzUgpUq9YpJSEiGJj4vXMKwzJ3w5zp5v3PFvpJNgc/J31smZey6g==", "signatures": [{"sig": "MEQCID2PJZZO/OAYPtLBaMSI2Z+Ndg/Y2iC9BKFOExkAvw/OAiA96dSu+vAy1+wbJdHiOvTcr2EvqyaMbrx+L8kP4heb9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378531}, "main": "./rollup.darwin-x64.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.2_1731691233682_0.35989048317487904", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "56dab9e4cac0ad97741740ea1ac7b6a576e20e59", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-xW//zjJMlJs2sOrCmXdB4d0uiilZsOdlGQIC/jjmMWT47lkLLoB1nsNhPUcnoqyi5YR6I4h+FjBpILxbEy8JRg==", "signatures": [{"sig": "MEYCIQD8705wE2nH1wy/J6FX2XG+D8f/2f/SFVu5LHbWPShicQIhAIEB1bTE7t7+N+truoztbJcy8tCLhL9wbW3PWoAFsI1d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378531}, "main": "./rollup.darwin-x64.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.3_1731948005153_0.32567202100781323", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7d87711f641a458868758cbf110fb32eabd6a25a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-o9bH2dbdgBDJaXWJCDTNDYa171ACUdzpxSZt+u/AAeQ20Nk5x+IhA+zsGmrQtpkLiumRJEYef68gcpn2ooXhSQ==", "signatures": [{"sig": "MEYCIQDi5XztkLHDxecdBHbxTrMiiRNoJKM8lxf9oRxpq1XfHwIhAKQbf9bj6kRhWtkeqqpEumWlluor/NG3pZxGYg+PMxDd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370307}, "main": "./rollup.darwin-x64.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.27.4_1732345247305_0.15278161557552128", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "f72484e842521a5261978034e18e20f778a2850d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-8hxgfReVs7k9Js1uAIhS6zq3I+wKQETInnWQtgzt8JfGx51R1N6DRVy3F4o0lQwumbErRz52YqwjfvuwRxGv1w==", "signatures": [{"sig": "MEQCIGPZinnzP9AvkvSEUhWpogmi7UuV+0riqgkDEX7og6SFAiAMXjDbXVjtbcUlI7gPBBGCXkb2CcncugYBpD6YYDowVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378547}, "main": "./rollup.darwin-x64.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.28.0_1732972575423_0.4184703403873482", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8f63baa1d31784904a380d2e293fa1ddf53dd4a2", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-WsvbOunsUk0wccO/TV4o7IKgloJ942hVFK1CLatwv6TJspcCZb9umQkPdvB7FihmdxgaKR5JyxDjWpCOp4uZlQ==", "signatures": [{"sig": "MEUCIQDnMTgyVIgDdI1obS7rMumR1oNs9oKqRLPL+9SkSDoH6wIgW3aUK3EPuo1VbZVRsrP1nhGiEeE9vCML+JpZb7kwm5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2366227}, "main": "./rollup.darwin-x64.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.28.1_1733485527081_0.3750471054860933", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "f263dfb65f0dd5df9e03680d2fc4523f7a556d56", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-Mstrak7He7GNR5K5dfO2Mi32RIeb7WfXxjddR3a9hGuxYfT1PoIuy+0o+dNrQQGyq9jtfYieYk+uAO8gLzKDkQ==", "signatures": [{"sig": "MEQCIHCjM2VX/F7BhgYGPs7RoE8uJ02Sv74l7WxSJmsih+iAAiBwA31jXxpsBUIZp/w8EHBMuNsOXo37bC5+r5Y5rz0f5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2366221}, "main": "./rollup.darwin-x64.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.29.0-0_1734331224977_0.4085822673204733", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-darwin-x64", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "22bfeabb9e79af41a764c469795db4072d4ef649", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-vKuDiKhmxv+S6rlMZEoIDSI9OIkHTzSuKrrCGKAu/5IXfWZPpIAFaHkUpgj192zT0n7b9zHnYmo0IPHJaGQBOA==", "signatures": [{"sig": "MEYCIQCxNkIJjGfK+ynwPGHujO/PJv2mlg+LU7i9SJ0edO4dmwIhAJVYXwLD1XGX2DAVChrk7BHFhoO9D3BaMJkySpKs7taq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2366221}, "main": "./rollup.darwin-x64.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.29.0-1_1734590279757_0.1934312851156581", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-darwin-x64", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "66e22947a0ead573a9bcec87aeffb5a4711c948a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-4fLcyg67jM1JLy+nSMHLRHDQtfY0ZBOBnqru8sXK7sBspO8+keGkLQQKOC7QRJ/67oNSaV8MFR3/FUWihI03NQ==", "signatures": [{"sig": "MEUCIG1HWt3oF+TXjYEs7Ldrh1RN62KXZFMhIu/k0uRcLcGkAiEA5duorIS0MHwA/VVFXvmwn1afnSYTWafdl/dTsmo5hWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2366221}, "main": "./rollup.darwin-x64.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.29.0-2_1734677794438_0.3644128111807443", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "6c79524ca9f0ad4ffccd364d652139dc159901cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-CTZ+lHMsTbH1q/XLKzmnJWxl2r/1xdv7cnjwbi5v+95nVA1syikxWLvqur4nDoGDHjC8oNMBGurnQptpuFJHXA==", "signatures": [{"sig": "MEYCIQDbJQ/3MmKlnoFHJkWGWtoCGAU2H1iJ/cDgbUMnRE+NEwIhAI2M9BV9rVj0Z4t+dHH1aTFIF60lXMaFTnTbezB7DQ3b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2374427}, "main": "./rollup.darwin-x64.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.29.0_1734719875686_0.13806650683467403", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "76059c91f06b17406347b127df10f065283b2e61", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-j/Ej1oanzPjmN0tirRd5K2/nncAhS9W6ICzgxV+9Y5ZsP0hiGhHJXZ2JQ53iSSjj8m6cRY6oB1GMzNn2EUt6Ng==", "signatures": [{"sig": "MEYCIQCztf0xlKsuVqaS8oBtynwBUFAreR2rlckXjp6x3U5E/wIhAJ2Pj9YIXV8Q3/yugYeunQKt0hdPdiTuj2HjVx2dU9Lm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2374427}, "main": "./rollup.darwin-x64.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.29.1_1734765394243_0.26229238180187875", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8ee753d0f3d19f31cbaebb730f52c574b5ecd573", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-b2hqONa0/1Tq76o41XIBicNQ+RtuJhP78SRl+tfLrg4t9WtRnZfaNjlCEz7VgekrMfjXRmGeCGHLVKbIW34RvA==", "signatures": [{"sig": "MEUCIHnBEZ/xYA6kLzPLyijcW8uJQcpyxRcKHes++nVSdjqgAiEA5+6Qb4sbbx/4BVuule7opDaeimU28+/SCXf/l6SrqlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2374429}, "main": "./rollup.darwin-x64.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.30.0-0_1734765465375_0.8618698689810509", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-darwin-x64", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "55a6a6db4dfafb50047f5ac19129a40934657507", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-7NSGuh9nLnqadBcKfoF7AUMmatyFcbsKKDYZlJrdFOKV/CacKQIi1BO9sRbyoJBCMvXDBkKqgWttZfpzDs6xTA==", "signatures": [{"sig": "MEUCIQDBl3qxH43mcLj7whzhVOusCw/uQyqJFXClVg897j9s0gIgFh5+nEwHCblW+3RxdfgB8g3nJln7lGLfXls0mRp6usU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370325}, "main": "./rollup.darwin-x64.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.30.0-1_1735541569101_0.41191191975089914", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1393f12d5722cc39b8c014aedd4b4da8043929a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-e2rW9ng5O6+Mt3ht8fH0ljfjgSCC6ffmOipiLUgAnlK86CHIaiCdHCzHzmTkMj6vEkqAiRJ7ss6Ibn56B+RE5w==", "signatures": [{"sig": "MEUCIQDrjOYJcy5vJhpxz7St2jzN+e4U4v0BdAKd3PH2Vri1qQIgCbOwHyO84po29y41iNKlhtqZjlkO3BoWPTq+CCMPNMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370323}, "main": "./rollup.darwin-x64.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.29.2_1736078895228_0.9467206936793424", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e492705339542f8b54fa66f630c9d820bc708693", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-Y3b4oDoaEhCypg8ajPqigKDcpi5ZZovemQl9Edpem0uNv6UUjXv7iySBpGIUTSs2ovWOzYpfw9EbFJXF/fJHWw==", "signatures": [{"sig": "MEUCIQCHW6vf6yGxB1vVrsUljIM7RfESqeAv+dKxPQNzIy3PagIgJQkEdA/5fuy4CfMS0jX0EB8OcWi5h/C5HiSKbEbPY18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370323}, "main": "./rollup.darwin-x64.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.30.0_1736145428859_0.31340603786459975", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "0ca99741c3ed096700557a43bb03359450c7857d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-x78BavIwSH6sqfP2xeI1hd1GpHL8J4W2BXcVM/5KYKoAD3nNsfitQhvWSw+TFtQTLZ9OmlF+FEInEHyubut2OA==", "signatures": [{"sig": "MEQCIFXJ3ab3xjk/M1GaSwD7YuPSflUiv7nWxg7cI6Qxhc3pAiBG1CpSkrhuDyYKuUPFWpByBTFTmFBdYeFmmxKTFUpWdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2370323}, "main": "./rollup.darwin-x64.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.30.1_1736246183037_0.5696707505318048", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7ebd127635c42e692781784cc61771d12bb9b55a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-bE7qZR7k+bigEUu5iNb7UkUMMIsjoMmp7fJHjN6eihZArcOG4IW0eZZU9vFY7uXSs49e0CyzdXvI31pCyU0guA==", "signatures": [{"sig": "MEUCICZjz6xUCvVezfd807ofN2I+4XqfsLA4CAbLc9+cWxtsAiEAtcUS6eeqNOL6zyM9jDj2w5lG2dHLWTP4paZaYJwvyMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2374517}, "main": "./rollup.darwin-x64.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.31.0-0_1736834292853_0.206179425528485", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "58ff20b5dacb797d3adca19f02a21c532f9d55bf", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-hrWL7uQacTEF8gdrQAqcDy9xllQ0w0zuL1wk1HV8wKGSGbKPVjVUv/DEwT2+Asabf8Dh/As+IvfdU+H8hhzrQQ==", "signatures": [{"sig": "MEUCIQC58w1azFwnI4ODPx4H0x/Zd5lkmZpxVHTzx0PtxzfaRAIgTLwSLuxR6PUvU8TwwElReKcvgAUKbhi43TEkjUBQX7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2399219}, "main": "./rollup.darwin-x64.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.31.0_1737291438704_0.4076991541134918", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "dde6ed3e56d0b34477fa56c4a199abe5d4b9846b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-F9ct0+ZX5Np6+ZDztxiGCIvlCaW87HBdHcozUfsHnj1WCUTBUubAoanhHUfnUHZABlElyRikI0mgcw/qdEm2VQ==", "signatures": [{"sig": "MEUCIQCtIhNNKB51Mz0C5b045fyPxBpVOmVfJhipQoOipXSV9wIgEPaCXWv7kObgzC1j2kLrsM7fCSMorEKeJ4t1qwEjSOk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2386891}, "main": "./rollup.darwin-x64.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.32.0_1737707285503_0.5690967132312856", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-darwin-x64", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5a37fbdf5aa155986f1e62e7b1abdeeb085ff25b", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-7Moi7u90Ox7iBpgACXmXTttBgBpgB1uL6DoxlxH2dwWp9LvSiLQR69o9jQdocLHSlN07/xuoRcz7H8kL+VWQNA==", "signatures": [{"sig": "MEUCIQChIq4R5+t9xSJo6jlVUrHnVO0G8Elss53SNqo1CcXj0AIgDp4XZp5p3s0i33NeHNSP/XIMjyTq2j5nhcOzqPMM0SU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2386893}, "main": "./rollup.darwin-x64.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.33.0-0_1738053039191_0.6831654246479879", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c04c9e173244d44de50278f3f893fb68d987fcc6", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-sFvF+t2+TyUo/ZQqUcifrJIgznx58oFZbdHS9TvHq3xhPVL9nOp+yZ6LKrO9GWTP+6DbFtoyLDbjTpR62Mbr3Q==", "signatures": [{"sig": "MEUCIQDj6/4W6oC/4wK0ZkrjnmfVhFazbvzcqMJIWlSvQxaGCAIgZrmrIXnbf/epK3uEt4kz+Tg47H7jmU5h+WMMKUrq57o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2386891}, "main": "./rollup.darwin-x64.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.32.1_1738053226260_0.4195927245399478", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "780053a37ffd5cde687fe532377518f79e5eac56", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-4rOvNnIRt2Kc4a6G6OXlmA8H88PUcrSn9Ac3L6rksCNK83rvHm1xTQz/XAcoy3EWuaKqkRUch0HC5DjF1rNRKA==", "signatures": [{"sig": "MEQCIC0Yl1cNQ01VIka5m9xbtHs7CsBnPExLuSG1budpzdZlAiBQ9GdXOFiTLHmsQ1Wm/Ku/E0ImR1OgPV7Vyu7V1Gb/FA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382739}, "main": "./rollup.darwin-x64.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.33.0_1738393951734_0.022733672946929184", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d4462dc79ed6a0f278488b9f2ed9abb401966270", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-aIB5Anc8hngk15t3GUkiO4pv42ykXHfmpXGS+CzM9CTyiWyT8HIS5ygRAy7KcFb/wiw4Br+vh1byqcHRTfq2tQ==", "signatures": [{"sig": "MEQCIGImAKpsSigU/AJkKoNBS7vDdZlHltNRENf71lHdWAnYAiB2CTZWg90wtcPbK2TX3MR0WIeWfnWmX+rta58YaYxjIw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382739}, "main": "./rollup.darwin-x64.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.0_1738399252661_0.3212509821607208", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "bbf8aa9fe655d75b5fb858b5bdbb1c790c36a3cd", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-+/2JBrRfISCsWE4aEFXxd+7k9nWGXA8+wh7ZUHn/u8UDXOU9LN+QYKKhd57sIn6WRcorOnlqPMYFIwie/OHXWw==", "signatures": [{"sig": "MEUCIApWPWm/zbBbjPmgCy09YOwwchcn3RjRJiCOriBRIgTqAiEA7pJ/adIS5B5ceDxtsqDlahy8YzlhlW1jXS8r496fVw0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382739}, "main": "./rollup.darwin-x64.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.1_1738565923235_0.018030484224879517", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "3e75b09993a3584c613d7c100cfa62992d8907ab", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-gQhK788rQJm9pzmXyfBB84VHViDERhAhzGafw+E5mUpnGKuxZGkMVDa3wgDFKT6ukLC5V7QTifzsUKdNVxp5qQ==", "signatures": [{"sig": "MEYCIQDphP8c4g4cX3foVhw5QXA04J03v9TRi0QtwUB9eHALggIhAPF9FUump6+NJ8HLhTcY2T3fdZiE8OfkvCxD/QOBPeSU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382739}, "main": "./rollup.darwin-x64.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.2_1738656634548_0.430290991952647", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "27501960a733043c2b0634c884d20cd456d1cdef", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-8Wxrx/KRvMsTyLTbdrMXcVKfpW51cCNW8x7iQD72xSEbjvhCY3b+w83Bea3nQfysTMR7K28esc+ZFITThXm+1w==", "signatures": [{"sig": "MEYCIQC2K21NxDThDynhWiCuKlsKXAHA95tpyk/41HImRg0fmAIhAMcP6Xi8Dp6gTxUiOhdMLWYVxSpltiQM8jy4+D3XZsTN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382739}, "main": "./rollup.darwin-x64.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.3_1738747356319_0.3973449402417568", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "116e2186564f5c26db819cea20248e310761d7ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-hQqq/8QALU6t1+fbNmm6dwYsa0PDD4L5r3TpHx9dNl+aSEMnIksHZkSO3AVH+hBMvZhpumIGrTFj8XCOGuIXjw==", "signatures": [{"sig": "MEQCIGI6abU6vNY4WcQ1G3YGf4ri6bDbOwxUjjFH1HuEvm3TAiBdtun1OgtJgZ0LhdiGoXNYFmOeey4WIVZpfu7DVXMDUw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382739}, "main": "./rollup.darwin-x64.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.4_1738791102366_0.5109635961804322", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "cdf9913cca30ff5730005cb35d8dda315f1944bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-Gz42gKBQPoFdMYdsVqkcpttYOO/0aP7f+1CgMaeZEz0gss7dop1TsO3hT77Iroz/TV7PdPUG/RYlj9EA39L4dw==", "signatures": [{"sig": "MEUCIG7cgdYie9jEv33Ht8KdbvRaoyL1CPltl3X1P1O2hlvcAiEAoZaTT2IFLlYGFakN/fxnCK4relnqzLbRi+EzBRnEeog=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2382715}, "main": "./rollup.darwin-x64.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.5_1738918415070_0.4837414093781789", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1aa2bcad84c0fb5902e945d88822e17a4f661d51", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-PShKVY4u0FDAR7jskyFIYVyHEPCPnIQY8s5OcXkdU8mz3Y7eXDJPdyM/ZWjkYdR2m0izD9HHWA8sGcXn+Qrsyg==", "signatures": [{"sig": "MEUCIDKFJpC1Re29i5gas8QBy1QrYBTOO9PrI9ozBM1j6V5cAiEAw4BlQK3+2XPg9KBIHrf5OI1Gi7VCe3R0DfaizlZdQYQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2370363}, "main": "./rollup.darwin-x64.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.6_1738945958979_0.6073956845646131", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "781a94a537c57bdf0a500e47a25ab5985e5e8dff", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-rSI/m8OxBjsdnMMg0WEetu/w+LhLAcCDEiL66lmMX4R3oaml3eXz3Dxfvrxs1FbzPbJMaItQiksyMfv1hoIxnA==", "signatures": [{"sig": "MEYCIQD4hj/xWhDoiyXd6fZaLF6btcHFgAchtoB3pxKX86xgcAIhANoxoCpKKXdV13vU8zDludsIa1DT6HortiEYBBcmEbze", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2362131}, "main": "./rollup.darwin-x64.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.7_1739526869130_0.5991430364569512", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c572c024b57ee8ddd1b0851703ace9eb6cc0dd82", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-qIP/elwR/tq/dYRx3lgwK31jkZvMiD6qUtOycLhTzCvrjbZ3LjQnEM9rNhSGpbLXVJYQ3rq39A6Re0h9tU2ynw==", "signatures": [{"sig": "MEYCIQCZvbAhj0w5pH86AXgq6k+FPETrAffaXiMahRYY7WIl0wIhAJ6/wM3KfEpcfhI5hlCkAo51RU+AKKnEIM8w1J78jMSO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2362131}, "main": "./rollup.darwin-x64.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.8_1739773615072_0.856471687540511", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-darwin-x64", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c2fe3d85fffe47f0ed0f076b3563ada22c8af19c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-eOojSEAi/acnsJVYRxnMkPFqcxSMFfrw7r2iD9Q32SGkb/Q9FpUY1UlAu1DH9T7j++gZ0lHjnm4OyH2vCI7l7Q==", "signatures": [{"sig": "MEYCIQCXExgLWTRGcFeG+hv0pbLDGZ6/FJGwSG+6lsdvniJnQQIhAND1v2ynm2Qg6T4ldZGqw8/B76v5c7Prj7B28TN82IPi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2517931}, "main": "./rollup.darwin-x64.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.34.9_1740814388607_0.8409659323365257", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "25b74ce2d8d3f9ea8e119b01384d44a1c0a0d3ae", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-3IrHjfAS6Vkp+5bISNQnPogRAW5GAV1n+bNCrDwXmfMHbPl5EhTmWtfmwlJxFRUCBZ+tZ/OxDyU08aF6NI/N5Q==", "signatures": [{"sig": "MEQCICux2b1xSwYOW/ekpWHs1Kt+wrN0y6IFbkszxlRZYRPPAiAWqNzJVw9iW+oPC9v68FaJ9+tD+PmH9kVT0foB8O+zYQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2538483}, "main": "./rollup.darwin-x64.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.35.0_1741415120577_0.5933962992296842", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "0e961354fb2bf26d691810ca61dc861d9a1e94b2", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-6c6wMZa1lrtiRsbDziCmjE53YbTkxMYhhnWnSW8R/yqsM7a6mSJ3uAVT0t8Y/DGt7gxUWYuFM4bwWk9XCJrFKA==", "signatures": [{"sig": "MEUCIFVFEqt13PV7px1V6elwfow1EOVUBsvYWu5hhJGwzXRKAiEA8KpIiYGUxqYdE7J0+E4bX3q8Gj+2JmfNdERQxTBfjWk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2538475}, "main": "./rollup.darwin-x64.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.36.0_1742200576967_0.7138654421703197", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "a6a697bb685ca9462a7caeea5f22f6a686acff1f", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-m8W2UbxLDcmRKVjgl5J/k4B8d7qX2EcJve3Sut7YGrQoPtCIQGPH5AMzuFvYRWZi0FVS0zEY4c8uttPfX6bwYQ==", "signatures": [{"sig": "MEYCIQDXz46QmPcvvhw4mY1QuYqgKGoyWzPYIPdLg3T/NieInAIhAJVrtzZMuEbTL9l6ZvRTkxmeZD8arLrg0K9vk1c/9IXq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2534339}, "main": "./rollup.darwin-x64.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.37.0_1742741857535_0.12711334001490915", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "77ee357aeeefe3fe8bee33df18c240e391450476", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-Mgcmc78AjunP1SKXl624vVBOF2bzwNWFPMP4fpOu05vS0amnLcX8gHIge7q/lDAHy3T2HeR0TqrriZDQS2Woeg==", "signatures": [{"sig": "MEUCIFk5NCSZofHe3kWNHIZyYrmt8Y3fYFC3VsvB96fha2duAiEA/J8LMN/AyDwjWWbnLaVecTnZmgcPS1e+I6DiLNImjy4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2538475}, "main": "./rollup.darwin-x64.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.38.0_1743229783056_0.2501296865081917", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "b26f0f47005c1fa5419a880f323ed509dc8d885c", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-mKXpNZLvtEbgu6WCkNij7CGycdw9cJi2k9v0noMb++Vab12GZjFgUXD69ilAbBh034Zwn95c2PNSz9xM7KYEAQ==", "signatures": [{"sig": "MEUCIQCvVNtEsFJxDHTmeA6XSaB3kfJESmO0o4iJr4gy86YUEQIgPyEiHdUC2mMqXWjkV/BhKxBgTcH2vy5rFW2IVQ61lxU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2538475}, "main": "./rollup.darwin-x64.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.39.0_1743569408017_0.08198823129651545", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d7380c1531ab0420ca3be16f17018ef72dd3d504", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-CoLEGJ+2eheqD9KBSxmma6ld01czS52Iw0e2qMZNpPDlf7Z9mj8xmMemxEucinev4LgHalDPczMyxzbq+Q+EtA==", "signatures": [{"sig": "MEQCID04xPO1Tihd02UD8aV5qyDusAaCFS0NyHj+nByzZl6QAiBC6ZfR5I2iBdJTiOEEtCmW0gBFB7uowAdAgIdMxkt8kQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2567275}, "main": "./rollup.darwin-x64.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.40.0_1744447209805_0.6829230501328085", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5e22dab3232b1e575d930ce891abb18fe19c58c9", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-nIwkXafAI1/QCS7pxSpv/ZtFW6TXcNUEHAIA9EIyw5OzxJZQ1YDrX+CL6JAIQgZ33CInl1R6mHet9Y/UZTg2Bw==", "signatures": [{"sig": "MEYCIQDaRn0B+aLk5+vOMSF82bkJVBW9h68dHgHIyaQ2V3ZBMAIhAI7Bqf4PXxrII6ThQMmjqZkbl2Qtk7T9kAb/w343a6xg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2583723}, "main": "./rollup.darwin-x64.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.40.1_1745814958317_0.5429534325883991", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "818298d11c8109e1112590165142f14be24b396d", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-47N4hxa01a4x6XnJoskMKTS8XZ0CZMd8YTbINbi+w03A2w4j1RTlnGHOz/P0+Bg1LaVL6ufZyNprSg+fW5nYQQ==", "signatures": [{"sig": "MEYCIQD8Rc/QB+8Fw4dAvdP52/Ing3YuJTAjlA+lkzwGMQOZawIhANls59PrbWsLaaJ1Hy84SxZwHE/rEeMGdFAyLIaOB2pR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2579587}, "main": "./rollup.darwin-x64.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.40.2_1746516448891_0.9434796817779452", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8d72fb5f81714cb43e90f263fb1674520cce3f2a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-gE5ACNSxHcEZyP2BA9TuTakfZvULEW4YAOtxl/A/YDbIir/wPKukde0BNPlnBiP88ecaN4BJI2TtAd+HKuZPQQ==", "signatures": [{"sig": "MEYCIQDYUES/xlNrSOh60yz5Prnre5uUli0cQxwC+8mTcbYx+QIhAMwJlcqc2dQb+5U990owlCdt9K7wzlH25wd+5jhqtDRF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2682131}, "main": "./rollup.darwin-x64.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.41.0_1747546442753_0.05348924044500136", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "aa66d2ba1a25e609500e13bef06dc0e71cc0c0d4", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-egpJACny8QOdHNNMZKf8xY0Is6gIMz+tuqXlusxquWu3F833DcMwmGM7WlvCO9sB3OsPjdC4U0wHw5FabzCGZg==", "signatures": [{"sig": "MEYCIQC0DTL/mxyA9xrOLYrS+Xsdgwa60vfhH5ZDRPApcDybYQIhAJt6u6PYEWhmQ/bJ8p16d5N372WJlQbnmAU1CCjCIjmF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2719523}, "main": "./rollup.darwin-x64.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.41.1_1748067313103_0.6875470947193487", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e597e41e5f92826f7080da25aba22cdfd923c807", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-oNUD4Kywoild1lsuUu1jydg3+IdQbyg4JNZb1+3Voh/revkYxMqMl4a/sZw5z+BSgFbXK6rR0RGutnHO8QS53g==", "signatures": [{"sig": "MEQCIDvU81iydFM6CpR5WPiCm6gAMq6WCn5RYgi9EEmC2hNDAiBXm6yDxm5u7oae7WQ2SrtN2DxXYFbr0KtvPpI0p1XiOQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2719667}, "main": "./rollup.darwin-x64.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.41.2_1749210068423_0.4807590580604275", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e15568b2fea4fdc526e86424150df9ec511fbaaf", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-rv5UZaWVIJTDMyQ3dCEK+m0SAn6G7H3PRc2AZmExvbDvtaDc+qXkei0knQWcI3+c9tEs7iL/4I4pTQoPbNL2SA==", "signatures": [{"sig": "MEYCIQCaT+ktbUskpqWXyOLM7ZTg8SCptl+HX2EEIamlttA+8QIhAO2LofJeWEfJnAEkuh/1Aj7M3uNaLTwQ50QRY3QpZ5o5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2719667}, "main": "./rollup.darwin-x64.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.42.0_1749221327979_0.6808759874947241", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c92aebd02725ae1b88bdce40f08f7823e8055c78", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-SYwXJgaBYW33Wi/q4ubN+ldWC4DzQY62S4Ll2dgfr/dbPoF50dlQwEaEHSKrQdSjC6oIe1WgzosoaNoHCdNuMg==", "signatures": [{"sig": "MEQCIEVey6SwWYNETFsuBes7UGag1XyDy8VilZ7jMG0hTQiJAiBjBHGlUOvb6mP8HIGrtGJrMz6UQu5Y40x8xquOpa94jA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2719667}, "main": "./rollup.darwin-x64.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.43.0_1749619397535_0.9030688037367196", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-darwin-x64", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "bfe3059440f7032de11e749ece868cd7f232e609", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-fBkyrDhwquRvrTxSGH/qqt3/T0w5Rg0L7ZIDypvBPc1/gzjJle6acCpZ36blwuwcKD/u6oCE/sRWlUAcxLWQbQ==", "signatures": [{"sig": "MEUCIEFERPUOg9m1uyq6uHjF7WsZK/JXuB8hhDXibCkAbUdxAiEAr8MqkaNwM00Cp9S88QOjjUNC+9HNWTu+jm9lcCLN+r0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2707339}, "main": "./rollup.darwin-x64.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.44.0_1750314214805_0.13239999743832453", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-darwin-x64", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-darwin-x64@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "96c919dcb87a5aa7dec5f7f77d90de881e578fdd", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-gDnWk57urJrkrHQ2WVx9TSVTH7lSlU7E3AFqiko+bgjlh78aJ88/3nycMax52VIVjIm3ObXnDL2H00e/xzoipw==", "signatures": [{"sig": "MEUCIDkFRlkqDYr98LVlCMeedF+J0jyhAipjJKHdR6mYCk2qAiEA+IO+AvDis7GbGZU63u1FG6lRfruLw0PHFEOe6MPVf+4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2707339}, "main": "./rollup.darwin-x64.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-darwin-x64_4.44.1_1750912492677_0.8231382840060473", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-darwin-x64", "version": "4.44.2", "os": ["darwin"], "cpu": ["x64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.darwin-x64.node", "_id": "@rollup/rollup-darwin-x64@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dv/t1t1RkCvJdWWxQ2lWOO+b7cMsVw5YFaS04oHpZRWehI1h0fV1gF4wgGCTyQHHjJDfbNpwOi6PXEafRBBezw==", "shasum": "202f80eea3acfe3f67496fedffa006a5f1ce7f5a", "tarball": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2699131, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCFqhXPNiGChxDQe5YhK3o8puZaCfe0O7w+dJpisSn1DwIhAOQ+lxdqzf9xi931XIiunKet33oqjsFhLjWqyLRnOSYZ"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-darwin-x64_4.44.2_1751633804304_0.10516109323101963"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:46.716Z", "modified": "2025-07-04T12:56:44.763Z", "4.0.0-0": "2023-07-31T19:17:46.930Z", "4.0.0-1": "2023-08-01T04:49:08.891Z", "4.0.0-2": "2023-08-01T11:16:43.302Z", "4.0.0-3": "2023-08-04T08:17:08.911Z", "4.0.0-4": "2023-08-04T11:36:54.168Z", "4.0.0-5": "2023-08-20T06:57:01.121Z", "4.0.0-6": "2023-08-20T07:51:49.458Z", "4.0.0-7": "2023-08-20T10:33:48.858Z", "4.0.0-8": "2023-08-20T11:22:40.728Z", "4.0.0-9": "2023-08-20T14:29:26.496Z", "4.0.0-10": "2023-08-21T15:30:24.994Z", "4.0.0-11": "2023-08-23T10:16:11.394Z", "4.0.0-12": "2023-08-23T14:40:57.247Z", "4.0.0-13": "2023-08-24T15:48:47.897Z", "4.0.0-14": "2023-09-15T12:34:38.016Z", "4.0.0-15": "2023-09-15T13:07:04.808Z", "4.0.0-16": "2023-09-15T14:17:29.672Z", "4.0.0-17": "2023-09-15T14:59:20.065Z", "4.0.0-18": "2023-09-15T16:10:58.111Z", "4.0.0-19": "2023-09-15T18:51:10.423Z", "4.0.0-20": "2023-09-24T06:10:50.186Z", "4.0.0-21": "2023-09-24T17:22:36.852Z", "4.0.0-22": "2023-09-26T16:17:47.578Z", "4.0.0-23": "2023-09-26T20:14:39.543Z", "4.0.0-24": "2023-10-03T05:13:00.334Z", "4.0.0-25": "2023-10-05T14:13:29.932Z", "4.0.0": "2023-10-05T15:15:06.362Z", "4.0.1": "2023-10-06T12:36:58.530Z", "4.0.2": "2023-10-06T14:18:57.892Z", "4.1.0": "2023-10-14T05:52:28.853Z", "4.1.1": "2023-10-15T06:32:02.878Z", "4.1.3": "2023-10-15T17:48:41.560Z", "4.1.4": "2023-10-16T04:34:24.689Z", "4.1.5": "2023-10-28T09:23:47.579Z", "4.1.6": "2023-10-31T05:45:30.855Z", "4.2.0": "2023-10-31T08:10:56.791Z", "4.3.0": "2023-11-03T20:13:19.982Z", "4.3.1": "2023-11-11T07:58:08.189Z", "4.4.0": "2023-11-12T07:50:06.222Z", "4.4.1": "2023-11-14T05:25:54.030Z", "4.5.0": "2023-11-18T05:52:24.912Z", "4.5.1": "2023-11-21T20:13:22.751Z", "4.5.2": "2023-11-24T06:30:02.887Z", "4.6.0": "2023-11-26T13:39:26.726Z", "4.6.1": "2023-11-30T05:23:23.661Z", "4.7.0": "2023-12-08T07:58:17.565Z", "4.8.0": "2023-12-11T06:25:12.191Z", "4.9.0": "2023-12-13T09:24:32.744Z", "4.9.1": "2023-12-17T06:26:25.449Z", "4.9.2": "2023-12-30T06:23:41.053Z", "4.9.3": "2024-01-05T06:21:00.398Z", "4.9.4": "2024-01-06T06:39:14.600Z", "4.9.5": "2024-01-12T06:16:27.418Z", "4.9.6": "2024-01-21T05:52:32.824Z", "4.10.0": "2024-02-10T05:58:56.725Z", "4.11.0": "2024-02-15T06:10:00.193Z", "4.12.0": "2024-02-16T13:32:42.972Z", "4.12.1": "2024-03-06T06:03:55.430Z", "4.13.0": "2024-03-12T05:29:00.176Z", "4.13.1-1": "2024-03-24T07:39:40.930Z", "4.13.1": "2024-03-27T10:28:04.079Z", "4.13.2": "2024-03-28T14:14:05.820Z", "4.14.0": "2024-04-03T05:23:24.794Z", "4.14.1": "2024-04-07T07:35:59.338Z", "4.14.2": "2024-04-12T06:23:59.959Z", "4.14.3": "2024-04-15T07:18:53.129Z", "4.15.0": "2024-04-20T05:37:38.600Z", "4.16.0": "2024-04-21T04:42:50.537Z", "4.16.1": "2024-04-21T18:30:27.273Z", "4.16.2": "2024-04-22T15:19:45.433Z", "4.16.3": "2024-04-23T05:12:55.856Z", "4.16.4": "2024-04-23T13:15:28.504Z", "4.17.0": "2024-04-27T11:30:13.805Z", "4.17.1": "2024-04-29T04:58:15.365Z", "4.17.2": "2024-04-30T05:01:13.144Z", "4.18.0": "2024-05-22T05:04:08.618Z", "4.18.1": "2024-07-08T15:25:36.375Z", "4.19.0": "2024-07-20T05:46:38.160Z", "4.19.1": "2024-07-27T04:54:23.254Z", "4.19.2": "2024-08-01T08:33:16.995Z", "4.20.0": "2024-08-03T04:49:14.708Z", "4.21.0": "2024-08-18T05:55:55.513Z", "4.21.1": "2024-08-26T15:54:37.613Z", "4.21.2": "2024-08-30T07:04:48.893Z", "4.21.3": "2024-09-12T07:06:14.168Z", "4.22.0": "2024-09-19T04:55:54.640Z", "4.22.1": "2024-09-20T08:22:15.814Z", "4.22.2": "2024-09-20T09:34:08.689Z", "4.22.3-0": "2024-09-20T14:48:22.777Z", "4.22.3": "2024-09-21T05:03:34.039Z", "4.22.4": "2024-09-21T06:11:43.880Z", "4.22.5": "2024-09-27T11:48:42.022Z", "4.23.0": "2024-10-01T07:10:41.545Z", "4.24.0": "2024-10-02T09:37:45.657Z", "4.24.1": "2024-10-27T06:43:30.077Z", "4.24.2": "2024-10-27T15:40:37.474Z", "4.25.0-0": "2024-10-29T06:15:39.779Z", "4.24.3": "2024-10-29T14:14:38.248Z", "4.24.4": "2024-11-04T08:47:36.904Z", "4.25.0": "2024-11-09T08:37:51.250Z", "4.26.0": "2024-11-13T06:45:27.195Z", "4.27.0-0": "2024-11-13T07:03:41.764Z", "4.27.0-1": "2024-11-14T06:33:39.220Z", "4.27.0": "2024-11-15T10:41:03.358Z", "4.27.1-0": "2024-11-15T13:28:38.175Z", "4.27.1-1": "2024-11-15T15:38:32.364Z", "4.27.1": "2024-11-15T16:08:10.795Z", "4.27.2": "2024-11-15T17:20:34.025Z", "4.27.3": "2024-11-18T16:40:05.490Z", "4.27.4": "2024-11-23T07:00:47.519Z", "4.28.0": "2024-11-30T13:16:15.679Z", "4.28.1": "2024-12-06T11:45:27.273Z", "4.29.0-0": "2024-12-16T06:40:25.217Z", "4.29.0-1": "2024-12-19T06:37:59.968Z", "4.29.0-2": "2024-12-20T06:56:34.771Z", "4.29.0": "2024-12-20T18:37:55.943Z", "4.29.1": "2024-12-21T07:16:34.484Z", "4.30.0-0": "2024-12-21T07:17:45.633Z", "4.30.0-1": "2024-12-30T06:52:49.300Z", "4.29.2": "2025-01-05T12:08:15.536Z", "4.30.0": "2025-01-06T06:37:09.054Z", "4.30.1": "2025-01-07T10:36:23.210Z", "4.31.0-0": "2025-01-14T05:58:13.097Z", "4.31.0": "2025-01-19T12:57:18.941Z", "4.32.0": "2025-01-24T08:28:05.707Z", "4.33.0-0": "2025-01-28T08:30:39.431Z", "4.32.1": "2025-01-28T08:33:46.491Z", "4.33.0": "2025-02-01T07:12:32.036Z", "4.34.0": "2025-02-01T08:40:52.924Z", "4.34.1": "2025-02-03T06:58:43.453Z", "4.34.2": "2025-02-04T08:10:34.835Z", "4.34.3": "2025-02-05T09:22:36.618Z", "4.34.4": "2025-02-05T21:31:42.537Z", "4.34.5": "2025-02-07T08:53:35.319Z", "4.34.6": "2025-02-07T16:32:39.231Z", "4.34.7": "2025-02-14T09:54:29.397Z", "4.34.8": "2025-02-17T06:26:55.339Z", "4.34.9": "2025-03-01T07:33:08.886Z", "4.35.0": "2025-03-08T06:25:20.827Z", "4.36.0": "2025-03-17T08:36:17.177Z", "4.37.0": "2025-03-23T14:57:37.767Z", "4.38.0": "2025-03-29T06:29:43.363Z", "4.39.0": "2025-04-02T04:50:08.245Z", "4.40.0": "2025-04-12T08:40:10.040Z", "4.40.1": "2025-04-28T04:35:58.540Z", "4.40.2": "2025-05-06T07:27:29.148Z", "4.41.0": "2025-05-18T05:34:03.018Z", "4.41.1": "2025-05-24T06:15:13.310Z", "4.41.2": "2025-06-06T11:41:08.656Z", "4.42.0": "2025-06-06T14:48:48.203Z", "4.43.0": "2025-06-11T05:23:17.742Z", "4.44.0": "2025-06-19T06:23:35.045Z", "4.44.1": "2025-06-26T04:34:52.926Z", "4.44.2": "2025-07-04T12:56:44.562Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-darwin-x64`\n\nThis is the **x86_64-apple-darwin** binary for `rollup`\n", "readmeFilename": "README.md"}