{"_id": "fraction.js", "_rev": "80-9c1c4743df35d4d41813d11a136e18f5", "name": "fraction.js", "dist-tags": {"latest": "5.2.2"}, "versions": {"1.1.0": {"name": "fraction.js", "version": "1.1.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.1.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "9c5e9b198d85733a88680c992f60f25f83ac7320", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.1.0.tgz", "integrity": "sha512-3Gq1sJjG3zJ4735lVjzEv4AaQ1g7jGoy4QFVzvYdmjsrFjrdN7sGdt7u4VpkUy06LssqWvzeXW4QOdVbis1s1A==", "signatures": [{"sig": "MEQCIExKWseefDisEiyq+YMfMGN5hT4WWM3k+TSis6PHDegRAiB42VpXML0XUI8shJSeCgFPRe169vw5VaPPioVdLL6cMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "engines": {"node": "*"}, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "A rational number library", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.2.0": {"name": "fraction.js", "version": "1.2.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "a0060008d092e5f8028ca76d72cdeedc7751dea8", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.2.0.tgz", "integrity": "sha512-F6hJVpNe/U8jaFIVnQ7eKKbbO0rkUqZ8c6JDA5YApsJoG2lPK0ygO3zh6CcGSTyAcHfpbB1y/FQ1KNSQjSVMcQ==", "signatures": [{"sig": "MEUCIQC+zBflabvHOAPhqdC8WFr3+DDebsp2z9KqYoQnYgDRQgIgVZ1eKs9k57VyJBLiZQkesecHTeAAtxbL1n1m7PeoWl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "engines": {"node": "*"}, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "A rational number library", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.2.1": {"name": "fraction.js", "version": "1.2.1", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.2.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "5699742415ccced9650c928d29c1fefba946092d", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.2.1.tgz", "integrity": "sha512-RmJFvN/HzOWz4n+pzrFEv18rpGaqwY/n5ilf9e+but9TqJpxynFRj8r3oZS4syByeEf4+DxoPLv9jGW3ArWXcA==", "signatures": [{"sig": "MEUCIHdpJzA+fNtl+SHtWSt6mCeSDFWpuwmQ5PM1Y9cMowvwAiEAsP4z/lQ9NEhMGLiYIMDQaOz5ec0z0Fn7yxpBN187DWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "5699742415ccced9650c928d29c1fefba946092d", "engines": {"node": "*"}, "gitHead": "c01664b5a30f25d9e7f601a1eabc888cbb74d770", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "1.4.16", "description": "A rational number library", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.3.0": {"name": "fraction.js", "version": "1.3.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.3.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "957f268fd3e61d6140826e122b985edc574b8aeb", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.3.0.tgz", "integrity": "sha512-oZyjm4BXYGepplCOl4ZK/41e8sdN4K0QWll3x1XHrpUp/O8uLOnyojRKjIG2Ar6xHea5tExM3Ks0J74B9H1oLg==", "signatures": [{"sig": "MEUCIBwNdtM7SSVPR0sGwRoE8TqX7qZ4CPcjjvinaZqeOzNKAiEAwJjrGsEn+Z7SFq4uYAdwQ6a19TeCDNlYc19/klK8siU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "957f268fd3e61d6140826e122b985edc574b8aeb", "engines": {"node": "*"}, "gitHead": "d3befba1f82aabdfd109abe2b53cecb081c1fbec", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "1.4.17", "description": "A rational number library", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.5.0": {"name": "fraction.js", "version": "1.5.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.5.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "7de884a8161e3e9e0d3b0ec5e3b78a82d4b42f92", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.5.0.tgz", "integrity": "sha512-Oq9ojQEmriTXCk0xp5RGTMjErfZxU5VN4cF4s8dRDdavA/6+gV1PSCk0VlV7wuqCDjmAPlhRLRN9fA3fewjPcA==", "signatures": [{"sig": "MEUCICAIDfzUjZa4eE7ffIYGBGYeh2eDh5NJU/FaKXq0YzR+AiEAqIfNhTkGE09rFZ9Sm9SqJqRKfFcraMkSUSi+NA6sF0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "7de884a8161e3e9e0d3b0ec5e3b78a82d4b42f92", "engines": {"node": "*"}, "gitHead": "71476469840d4c22dfc3d6d0f6dac6c3c3e807de", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.4.1", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.10.36", "devDependencies": {"mocha": "*"}}, "1.6.0": {"name": "fraction.js", "version": "1.6.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.6.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "b9e56dfd9562dfcbbe05a862fb9bd1400d9c5488", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.6.0.tgz", "integrity": "sha512-4kZCc7HzWJPRnJqfzyUgGtme8F+B4EYiPccOkA54xIprG+1ItqWbKYWcudDHR5G9JtA8JboRcbsKp0xODARv5Q==", "signatures": [{"sig": "MEUCIQCLD7+VwG9/JYRId3GqbOqIQPdJoE/bSQ7Ie3Up+qUeoQIgJgWHn7iDvhIv/kGxT7LRiOfKXUSakxIqP/TvhRimumg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "b9e56dfd9562dfcbbe05a862fb9bd1400d9c5488", "engines": {"node": "*"}, "gitHead": "dea70736e8149d8c85a40cb227116732fa30b420", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.5.0", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"mocha": "*"}}, "1.7.0": {"name": "fraction.js", "version": "1.7.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.7.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "8e95345c3a64e1d8cb960c7fb738c7449ea7b4d6", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.7.0.tgz", "integrity": "sha512-UaLTK9rFsYEOhbgIrYvUNedO7R9GPHE1aBphpVF8WYpcvRA7ZcqKd/Iqf/JRY3CtOVyfj5YhDjfiQqDwS3IjXQ==", "signatures": [{"sig": "MEQCIFqRxxI3HHGVqdI7hFf+GMLrMjBx/DwvBtnTGk2otCUqAiBZrOtbGM9AlPaNMV1qCtLyJnTRdW6PEbEbtsCgZmxoyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "8e95345c3a64e1d8cb960c7fb738c7449ea7b4d6", "engines": {"node": "*"}, "gitHead": "b904864daf9ecad4c17f2a56cff663553bc4258b", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"mocha": "*"}}, "1.9.0": {"name": "fraction.js", "version": "1.9.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@1.9.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "34463f6f7bc1ddcec3e05e2d4977d24612932170", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-1.9.0.tgz", "integrity": "sha512-faMq7cd0HxcgjzVn70zO5IiAngfPEIajIDHPQ8TO8KJYQJ/GiSvvIatDc7AOr4bhGMv1JXBTI3f7nb5YO+mmqg==", "signatures": [{"sig": "MEYCIQDHr4r9jtMLzpUlLSp+n+CzY5ucZDFoWPWwlpeUFr5TwwIhAOaAXAZxnkbfruF+y154CQKCldIg3OhzqkjcPhKH/Dx/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "34463f6f7bc1ddcec3e05e2d4977d24612932170", "engines": {"node": "*"}, "gitHead": "ab709646762b81a6bc6660ad1f894cfa2c201ae4", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"mocha": "*"}}, "2.0.0": {"name": "fraction.js", "version": "2.0.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.0.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "c7508c03c5f4f03971c94d7f57626b07d503d1c8", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.0.0.tgz", "integrity": "sha512-0ZNAiEcZKHQmlsksnQ6aMn7vEdApRuXFFiyEk/CgqzrZr5mzHxOJpvC0/FUBwQKYqFBMvoQ3bISBmKctFipD9Q==", "signatures": [{"sig": "MEYCIQDbiuLLCY7zHivtcQFKzP4BjBuwrOLKiT09WJhUcHhdYwIhAMHyp4kcdmt2E7hFO3hoi2IxLbcpN9mfWkP5FBCfrj7M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "c7508c03c5f4f03971c94d7f57626b07d503d1c8", "engines": {"node": "*"}, "gitHead": "fd457a8ce0a243e5178fc07d7c8dc058580088ec", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"mocha": "*"}}, "2.2.0": {"name": "fraction.js", "version": "2.2.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "a91933797ac159353bf27145c37d57f3c44f3d97", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.2.0.tgz", "integrity": "sha512-P0nQNaVSVtUAdU3KYpdxQfiExxUYmACgNKtHkl4XhEuTrg9ILuJupxo5Cc+3Nauep43RuIhEP9ztpjvvQ5tLkg==", "signatures": [{"sig": "MEYCIQDTNhCVcDxC4Db/faliHLddY8016WYrSx0sjru/H9IgEwIhAMCyMY0bSv3GFZ9fZQCgSPW/9oeJXjrjPx0xEWM9HlrN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "a91933797ac159353bf27145c37d57f3c44f3d97", "engines": {"node": "*"}, "gitHead": "722cd19c14c0dc57bdce424d3950f369d421cf6a", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "*"}}, "2.3.0": {"name": "fraction.js", "version": "2.3.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.3.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "937b0a962da28da3bb0e95ea8776e6c56b702165", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.3.0.tgz", "integrity": "sha512-aIqHNoICWSrqVh2HJzRRrfuVc3F3sucRzotGodt2iYjnvyYGAG+XJ7QuyAyXsLANyI8LlPIoCC3mVprYVqRXrg==", "signatures": [{"sig": "MEYCIQDP/8lAqv9Com1wat/KusRR/G+gw3hpJdk6XO9dIwS3qwIhAIX9NznvRZLTZO0pgd+ZP0uaxYP9QDVACfQF64RnxCi4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "937b0a962da28da3bb0e95ea8776e6c56b702165", "engines": {"node": "*"}, "gitHead": "fa2bf9f3dcc3b1491f368383b0e0673ec773c0b3", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "*"}}, "2.4.0": {"name": "fraction.js", "version": "2.4.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.4.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "39d2c2c9b91d0ae2888e7d81e5d1607d561707f2", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.4.0.tgz", "integrity": "sha512-36IatoLLshc4vTe8uk5706ALFMiZhaTifPcKG0Afdzzm1GD8/53hdKxI/shZ7uyfQCrHFndWTppAOtrGFeb3Gg==", "signatures": [{"sig": "MEQCIAPW20INzFIlwWSRXKrA/KUFTaPaq7aw/B1V0wa23BnDAiAE3+sYnZDBUn1qI+WnrRT5Bo+CNT1EX/mB3qn63oH/qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "39d2c2c9b91d0ae2888e7d81e5d1607d561707f2", "engines": {"node": "*"}, "gitHead": "df1b3df133055d62d2de474779db3b8e797838d0", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "2.4.1": {"name": "fraction.js", "version": "2.4.1", "keywords": ["math", "fraction", "rational", "number"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.4.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "277e70045754a5e87557cc49e9c9d370f01e8a2f", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.4.1.tgz", "integrity": "sha512-Pm3MEWVSMqnK9+Xgb8HRahQEHNwYTXe6uBWqPpEGLsjZWyyug6Lh4I1mbGL/G0gu91BCIQeTId0A0cYQVMXmyA==", "signatures": [{"sig": "MEUCIBy0NFPPhKjNMKwzhaIWMYXOHDSNBbitqu2mj6YaMmcSAiEAiYMZqa4KaERQ+x3/eAzY2JXqlC/UYZNw6f9/7pBcfWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "277e70045754a5e87557cc49e9c9d370f01e8a2f", "engines": {"node": "*"}, "gitHead": "f0640781b4bf68b28505516d3bb562fead77b66b", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.6", "devDependencies": {"mocha": "*"}}, "2.5.0": {"name": "fraction.js", "version": "2.5.0", "keywords": ["math", "fraction", "rational", "number"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.5.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "abca045c6ede85edf20a47ba3a5bfa525d18513d", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.5.0.tgz", "integrity": "sha512-eZ/iG7JVRGytfYA7crdAIGQo8BPtx8yX3G+ls0Y6+Y1ex508unZeFiFMeGbezax/BhvOVmlnrhKPO+0itmw5Ow==", "signatures": [{"sig": "MEYCIQDo3xpNseEHe514SLIsaZo11WIR4dNjlqXYnAmASOz9IwIhAKoWGlcE8VTqN7kmzRnD2zrZp/yb1nJaLssksO7Xslva", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "abca045c6ede85edf20a47ba3a5bfa525d18513d", "engines": {"node": "*"}, "gitHead": "e38a320aa2f3a6390ecc93c9b9478c68fd73f9e4", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "2.6.0": {"name": "fraction.js", "version": "2.6.0", "keywords": ["math", "fraction", "rational", "number", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.6.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "bdfa365253d90413df629b853c423324a8195d52", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.6.0.tgz", "integrity": "sha512-xWYZuRssiYjiIJ0SSAYqmvTmFQhInrQyq/M8wNVNeR9jUrawuwpp6S5/mTgLSHQM0ECQb9DboJtRoKJppjXUKg==", "signatures": [{"sig": "MEUCIQC7deXcKv5bAR3jkMW1X+jSdmMY350lrxfhxyH/SHChqgIgTD3jzECcoP8iqQTyaNsGp38AA3OhmLb2ecGhFHfoIoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "bdfa365253d90413df629b853c423324a8195d52", "engines": {"node": "*"}, "gitHead": "0971ea2c3f2f3d71b604d615d1ec5a1e3cd03e20", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.13.5", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "2.7.0": {"name": "fraction.js", "version": "2.7.0", "keywords": ["math", "fraction", "rational", "number", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.7.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "22e38b7a9408a6a19c0604f1da507a72cb3fd093", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.7.0.tgz", "integrity": "sha512-ZJaJDBBwDGE/0j+P4t8QU1onEExewJaBP2ALy6VUiSzEy/2vzclFe532nB6swBjvh7JNpoubodsjUjwsnBXNzQ==", "signatures": [{"sig": "MEYCIQC2HhF4ZaS42HlS4jqEZX9KxNIlr8CSS98upL6nYvh8mwIhANXnwyHv4cTUIv/68Fdw+Gu50h+yhF8VmcWphOdF2BUc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "22e38b7a9408a6a19c0604f1da507a72cb3fd093", "engines": {"node": "*"}, "gitHead": "58f2241a306e546979dbd52d3dcbba4a8e6f9b12", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.13.5", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "2.8.0": {"name": "fraction.js", "version": "2.8.0", "keywords": ["math", "fraction", "rational", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.8.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "7644fb904b0217747aed0cc103ef1cbee564ece4", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.8.0.tgz", "integrity": "sha512-mGQxncTd6IYL3dCDMGIPYMevttFIytkUTKYmNs0vitR74q3hLDc6wszuGaaVOJxHrDU8J72JddrAaMiZSs7o3w==", "signatures": [{"sig": "MEUCIQDMI7qkMQALFcehubaI7vD1eh6xwRFjy2iL/vFCb8sVUAIgZtN0zDCrJykG3jEdMBJ+A4aUMcqckWSMW3SYQ+zmkz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "7644fb904b0217747aed0cc103ef1cbee564ece4", "engines": {"node": "*"}, "gitHead": "2e6adc95ca73e29d04b778ceb858c9a923a67b9f", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "2.9.0": {"name": "fraction.js", "version": "2.9.0", "keywords": ["math", "fraction", "rational", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@2.9.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "dc0b487cb5f5ef89abe8ec874107f2898fbf9b2a", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-2.9.0.tgz", "integrity": "sha512-NXkSZpj8L0gr92PREEH6tx9BjL2PofwdAF395sZvqcOUX/uQL2eWWbjVERT+AB6mHA2JB0o54OxDoXdTbmeJ1w==", "signatures": [{"sig": "MEUCIDFDBtP+f3Sb+0ptdrrhDnXDPkWkNxoYzyrBVgtw+VYVAiEAgNCw3KFNSTsKcbWuMwYD070aAs0ht8yNapmQp3/eJIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "dc0b487cb5f5ef89abe8ec874107f2898fbf9b2a", "engines": {"node": "*"}, "gitHead": "722b2764d06e0af5d5d357d045e2a1a5e6f95355", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "3.0.0": {"name": "fraction.js", "version": "3.0.0", "keywords": ["math", "fraction", "rational", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@3.0.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "4e8c1c5167c607c623e759cc23a60abf48941080", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-3.0.0.tgz", "integrity": "sha512-8dQGFyJJm1FqIIlSA6OWCQ4KPQOpn6H4E4SYEAHai+qieXoyGEFFdC66GHVeQ7+IQ2/56lQ0B1O97QLvPx1zDQ==", "signatures": [{"sig": "MEUCIEhnDuAtd4zVFw+i9BMRu2Ap9hrftP3ErrpWwBaQ1C2pAiEAzs+VhC7M++ukBTtVIv4iC8NnTyYs5PqmMRA6jSk6YMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "4e8c1c5167c607c623e759cc23a60abf48941080", "engines": {"node": "*"}, "gitHead": "5b595ea57e1d0de0d45872db965089d249e045c4", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "A rational number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "3.1.0": {"name": "fraction.js", "version": "3.1.0", "keywords": ["math", "fraction", "rational", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@3.1.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "34bc3e6dfda754405d468e48b1f70de761b01989", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-3.1.0.tgz", "integrity": "sha512-NQc/iEwboIOcg2ju1dwtYOL620XJNUh3TBWtQZPUDc1bhM0o1BVr6DMzecojY5VZO4G5V10ugypUWthQ2oMjEg==", "signatures": [{"sig": "MEYCIQDyj1WGU++DfBHajUZ4pBsq1TLUrOW+DNOYH2LaCAfSqAIhAOTTyCizdi38YxH5pFW6X+r2xAMHH7O9Cr7Da8JvZo55", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "34bc3e6dfda754405d468e48b1f70de761b01989", "engines": {"node": "*"}, "gitHead": "f0c236b7e4fd0bbc33078a0b7ea01396480fd20f", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "5.4.1", "devDependencies": {"mocha": "*"}}, "3.2.0": {"name": "fraction.js", "version": "3.2.0", "keywords": ["math", "fraction", "rational", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "fraction.js@3.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "597b76b9dd835291ed0170c86c4763fd683439c2", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-3.2.0.tgz", "integrity": "sha512-PmoO+vmb5qNj830ghZmz9p+IpOP1AynSRrEsqmDQvBt2UsCbUGn3mYt803F7lskD0r7G4JgZH3oJwfqQvv3tdw==", "signatures": [{"sig": "MEUCIQCbtsbo82N4pepVuhIuhSs9zfnrQAaqNntyYE39Aq+OiAIgUQ9bVSvzgXa7YQkGf69tMcB1w2841Z3x7TUwJPwFKFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "597b76b9dd835291ed0170c86c4763fd683439c2", "engines": {"node": "*"}, "gitHead": "5829a13e9bd27f84837583578af4ea5374177eb9", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "5.4.1", "devDependencies": {"mocha": "*"}}, "3.2.5": {"name": "fraction.js", "version": "3.2.5", "keywords": ["math", "fraction", "rational", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@3.2.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "502efada3f737824f16ad1c1a41f23ee51fd781e", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-3.2.5.tgz", "integrity": "sha512-1nfE6w6ZUik6w9hEAtqOxsbuvIYj/+sUd8SSMKWZebrGYY4hc9vECqPHrDfdD0XhZqTTlr9KEmS3KgPfyEspYg==", "signatures": [{"sig": "MEUCIQC0+rjnqkO9c1mM8dy6cxKGTjffIwPDceglt0KUf5yz7wIgeQqIr00ZMUZLM/gwJSii02RFixJRgO+hFyyPOwAMTBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "502efada3f737824f16ad1c1a41f23ee51fd781e", "engines": {"node": "*"}, "gitHead": "654e6e72d2522dd420b28fe51b28c06dcd540975", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-3.2.5.tgz_1454761750288_0.37279163231141865", "host": "packages-9-west.internal.npmjs.com"}}, "3.3.0": {"name": "fraction.js", "version": "3.3.0", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@3.3.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "700a6a0ca73ac4ccca1f88e21eb374a275167e3d", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-3.3.0.tgz", "integrity": "sha512-kstdwZCe1Gc/RDRHdWAgqH8L0CVgeg36bJP0qRu0mUSxYqKP0cpPJz4YhEtZNGLF3Bv1MZfWiHCrdBGXJTv7zQ==", "signatures": [{"sig": "MEYCIQDLG3VJLHU8/JqsEpU/Uhyl8VVW35VXQKF1ECEiRkkHzwIhAL1XY11wGHFIZFHleSzv2vAmzQzZArV1a5KsmLW0DokM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "700a6a0ca73ac4ccca1f88e21eb374a275167e3d", "engines": {"node": "*"}, "gitHead": "918c340fc7aceeee81328fa01810c590ab063fd1", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "3.8.2", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "5.9.1", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-3.3.0.tgz_1458860009202_0.12263908958993852", "host": "packages-12-west.internal.npmjs.com"}}, "3.3.1": {"name": "fraction.js", "version": "3.3.1", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@3.3.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/precise-calculations-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "5d6a31ff07707294f204ccbb6b53791607771083", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-3.3.1.tgz", "integrity": "sha512-37tFTeCLiWfS31fBebu1JPOdnt0CBZuZLoWXzakL8+60z+gDD8DuYuOAeM8NY97F0oafvf9OCcvkPf7+wzZMOw==", "signatures": [{"sig": "MEUCICBEqJ1MJ4auRvGRs7VLD6FXVEenw0pUo8brFKKUNNCuAiEAnkHRaKsQiUMnmFkg1/ddF7Z+oMgFbl0YO7HJVWdm1Jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "5d6a31ff07707294f204ccbb6b53791607771083", "engines": {"node": "*"}, "gitHead": "bd7ae34bca5f0dae488253ff9416452c765e3c3d", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "5.10.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-3.3.1.tgz_1460043236976_0.2814995003864169", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "fraction.js", "version": "4.0.0", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "73974e2f8b51ef709536d624cc90782e2bb61274", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.0.tgz", "integrity": "sha512-VFj4e6g5arfi1iH4YOHao6Aq8ZB9e+R4mW8dUZA0qeLqVQWKMc4+4N/U1eTWPuddMsXd0MjwGRFRzBW2G7msBA==", "signatures": [{"sig": "MEUCIQDxbc+5BtnCdgNI0Vf/kAazJSFtsC+jNDnehm4K3XOwUQIgWzgri5Y/L2awUUTWj7UrO049ODzNGcRQA/Z6aES9Epk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "_from": ".", "title": "fraction.js", "_shasum": "73974e2f8b51ef709536d624cc90782e2bb61274", "engines": {"node": "*"}, "gitHead": "4a12e0540610ab67a8e09fbd2327152f61d91f38", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "7.4.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-4.0.0.tgz_1486595444310_0.6041910354979336", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "fraction.js", "version": "4.0.1", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "c6b5218b230b082188e6c53e76b0965c052da24c", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.1.tgz", "integrity": "sha512-NQYzZw8MUsxSZFQo6E8tKOlmSd/BlDTNOR4puXFSHSwFwNaIlmbortQy5PDN/KnVQ4xWG2NtN0J0hjPw7eE06A==", "signatures": [{"sig": "MEYCIQCFqkyeCE5AsYF87PmGJcEYjEEcVoujJJJgutHZ7mn1wgIhAIMuAWN9MIL7ZUxmkx1cVxSK49EKFh1b5f0Q/KtFXMyX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "fcf900715ee43b26da04dbf47fa4516ecd9e8f5a", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "8.1.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-4.0.1.tgz_1497378374878_0.49628469650633633", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "fraction.js", "version": "4.0.2", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "0eae896626f334b1bde763371347a83b5575d7f0", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.2.tgz", "integrity": "sha512-OswcigOSil3vYXgrPSx4NCaSyPikXqVNYN/4CyhS0ucVOJ4GVYr6KQQLLcAudvS/4bBOzxqJ3XIsFaaMjl98ZQ==", "signatures": [{"sig": "MEUCIEu790Kqx1fDY9teKaYqHSkU6kZdDZz4isavEYRjTMzIAiEAw2v8JWeyXlBW9BBpMxt5l350JJ+FC7fE/pX6xyFLW7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "c7416d31670ad1bb53f833b6afa5d0ead2e86232", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "8.1.3", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-4.0.2.tgz_1498831992426_0.5949151231907308", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "fraction.js", "version": "4.0.3", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "5020a51c31fd021f2a8e2fe774373cc860e42c71", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.3.tgz", "integrity": "sha512-KLVdvN87ayhal5rSNehgSAr3ZFS9NnVuQoeYhQ6ZTsFhpVkssvyIuMN6a9QqgeTijk2hyrxmgeODTGAMcyMvCQ==", "signatures": [{"sig": "MEUCIQCDLMDQFqRsQifXwo2GTYGSmv7y91978P2UxJ/MIqGfhwIgMUuEFpuWJu1VndiismbMQgsCApImopnhePEXqTcakr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "5fff1578005970055ecef1123fb35345eaf84561", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "8.1.4", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-4.0.3.tgz_1501082005015_0.9780106407124549", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "fraction.js", "version": "4.0.4", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.4", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "04e567110718adf7b52974a10434ab4c67a5183e", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.4.tgz", "integrity": "sha512-aK/oGatyYLTtXRHjfEsytX5fieeR5H4s8sLorzcT12taFS+dbMZejnvm9gRa8mZAPwci24ucjq9epDyaq5u8Iw==", "signatures": [{"sig": "MEUCIGLY40G6g6eZYw10u87ubZ6DcsbR/EbLK+LS4GNcJFfBAiEAzRxpK5PagoKF/CHzzfVsY7pUo2gKDC1YpYekLjlvAuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "13a1034f801686c63b61d639b8e252a53589d02f", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "8.9.1", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js-4.0.4.tgz_1512778620302_0.3451210130006075", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "fraction.js", "version": "4.0.5", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "9809774a151f08845d7ad5d1c184372292af0380", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.5.tgz", "fileCount": 12, "integrity": "sha512-lWTmXrFIDlYrkbtjmbf5L8L0u0Z9kdCqISYRNhccKa3smkSn9GFmta1K+kXoomq1fnFzS70bKOqOOhzWLa33xg==", "signatures": [{"sig": "MEUCIGMSVzbeb4REuAHnVJOzCefrZoGzTpvxK5vQFbweo1veAiEAwRqhJChscIeQY6q/LIXWRYHtAyU8DIKXPzjHfdVaR2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77912}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "c08285fabf78eadcaebde96fcc54be5bc63127d2", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "9.9.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.5_1522353375691_0.2528580207420219", "host": "s3://npm-registry-packages"}}, "4.0.6": {"name": "fraction.js", "version": "4.0.6", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.6", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "cc3e6502dfb386cdd4c0d0d429a2f4d4e520ffc2", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.6.tgz", "fileCount": 12, "integrity": "sha512-q+Qoi1D6VKptxgQsu4IKTzkkYjwZEa8rM0x9quksWnyTY+NEcb9ZvohZLWVLXBBRrNHQZpdmUL+NndU5o7XnEA==", "signatures": [{"sig": "MEYCIQCFD11ZYXdhkKcR9bpAV+OJvzBmAZHcnl+NCFYHP0uuzAIhANqp9juy1KH3Do7UOznwl1EiV2E2TPVJLJYCEOC9sT/8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77900}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "45646ed65202eb82ddad8f7c89f781d46c74a9cb", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "9.9.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.6_1522357577823_0.9698391647987594", "host": "s3://npm-registry-packages"}}, "4.0.7": {"name": "fraction.js", "version": "4.0.7", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.7", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "0f1d134ac7da0c2a584777f4ce6628def318840b", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.7.tgz", "fileCount": 12, "integrity": "sha512-aFA5K0UVz6sngdUvMjr5IDEYMGwbevD+ougBPkwC0BVaPTplepUwMXfmMaueqTpFVeHO+bIKP3tORni7auknoA==", "signatures": [{"sig": "MEYCIQCZINmFcPYZiX8RtmSxY2Xk5lzBjzmA3fSuNBIAuwT3vgIhAMhnQnEDBJYYjx9yJXt0hczNc/eeHMwLuzWM/64SLtsB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77902}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "4bf262d49e91d6155d53e90ab595c5b10853be2c", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "9.9.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.7_1522358593475_0.864276882535864", "host": "s3://npm-registry-packages"}}, "4.0.8": {"name": "fraction.js", "version": "4.0.8", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.8", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "e53618112e3b36b348c61a81323173bceb01e418", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.8.tgz", "fileCount": 12, "integrity": "sha512-8Jx2AkFIFQtFaF8wP7yUIW+lnCgzPbxsholryMZ+oPK6kKjY/nUrvMKtq1+A8aSAeFau7+G/zfO8aGk2Aw1wCA==", "signatures": [{"sig": "MEQCIGu5PVcfLAWEq2r8Eca4zeW68RXyqGrXb56gnxl1CdwvAiBMj2Jq3KecpUbDhVo80k8WAvhJuVmlQLab54k4R6fvkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa0TUdCRA9TVsSAnZWagAAG7YP/Axr3KPszxJjHWhI3kuP\nP2hzzoy11g/MrAM8xRp5KSlwqNxWyjb7JCMqkMfR0zzqf6maBsIuLF7uqAeQ\noX+chVofMt1JFKIvx2Z1LW3r/3oDamqxULu3FXjmagQGjR02hM5s41V+Qmc1\n1LH/2qNsyx97M3d2vAcgaCCWlLUgcorOSW5tzkzrPxM7L+h2O7nRcVkW9Mko\n3yX3NCbTe6BejRJ7JRiInblAfb2DNLC1kRNjHBWnsQfZ7RSYgTI4E3PnrBkk\nbbJm6yILkN3Td6ZWPcTDC+aGKC0WMiKISVRzsinHzXO3cmDYIqqs7kKp4Yxk\n3cLQR+rk1eu4+oS6YZHebaLaKSN7sVWY27IUuen4JiyocD3IXedX0R8w/PG6\nm4JFXxCvQl26vpDvYvWboe/14MPv3YqtWv8uHFQlRCpWnxhTHhZF53cd88/2\noSKK4dBCag4IkiaVR+9ERMRZB4YMmGbDeeWPyLzU9JAafnB78XgnOVZ6Qa13\nE9sFJHeIk+BC+Ml+VHsdwhxEh5VwbcXebBluRzY3vbk0UpXG5nqH9t10RYsn\nLFRKBaEehOGGnzIsux/d9CBySaU6s0/wErQagnqTiHxJAtneIBb39mtIGBDR\n3/1qQ0gF1szq/dg54xWx57Ue5VIMpamPEawrdY0VovokhKba4k4pE04App/5\n4XgK\r\n=cAjf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "28ea1dd7a2798b5fd08fe1edf426061bd988b096", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.8_1523660059807_0.506093810242999", "host": "s3://npm-registry-packages"}}, "4.0.9": {"name": "fraction.js", "version": "4.0.9", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.9", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "ef7aa1ad47ef600021ad7fa7991dfd56c3b5f7c8", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.9.tgz", "fileCount": 12, "integrity": "sha512-qP1sNwdrcA+Vs5TTvGETuaaUmz4Tm48V6Jc+8Oh/gqvkb1d42s99w5kvSrZkIATp/mz3rV4CTef6xINkCofu+A==", "signatures": [{"sig": "MEUCIG4BaXCovfHOke6JZ0q0UddoCF1jDeZN8GzfmPyezKpbAiEArGzsZp89/Is7WpnhDP4VepsMwJKG5a4NcZy44OXaqhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT8ZkCRA9TVsSAnZWagAAln0P+wcX7p0sYB+XJdLuR9oX\nF0ASOAXL4bub7cPWl3QA/0MbUGs5+L1xv1uEZqRY+5g24OYpu4d/OZjV6wOH\nKZJcsYBCq/93BmXk21rbV04wAU2WnfwQiuzKey6xXUjRybFM2Mgir7R+1HcI\nXnleZb5+RaPQu0trlE9w05VeJhSZiwoYRZq+AuyGVf5SWQMYY2lYSfrp/V4h\nYUDAe1oyHIn+b8MxCeP3/rqdP04uJudgACpaO1uqjQr/oghFNINxZr8uVwkC\nDku8ZS4vit+hXfe7S1XjQhniRSkrkA/IdEVGw4lK1LEFSg6Bmjnay91m3IIK\nre+BgvVAVk+YzAP0MY9w1/vNqnhHnXAblQiA8X39Pi+RMUYd1LTNjAt/Wobf\npB5sIaEEoHjfh7fVhfx/nBeOu6LJwGPyrj+CzK+Ru/DAuUOb5t8JzbWgIG+C\noTAzmDxztGGITiCLIhbM9p9wz/pqRzOGXqGP3EIq2i8iL/IqjuBeqo9kMGo/\naIMfre9mJ+/lPdYq9wHfsItHK4H1D8hj3ONkCFFXmLqtB3q/MTW5BuVhvIA8\nSC+/K4o3Tkac1LnkBTL8OGoKOpX0uZrE6JJyQCZj2DMI5F0t4MGHcVCqb9Wu\nF4VFJK4Xhk1fgqiSSi59dS6ovYMlXpHNNPUAkrvNl9WF0MSntj7LrTsR/tBj\nrXad\r\n=6y68\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "eb72834d548e76bc0f255a7eb69454e9c64c1f1e", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.9_1531954788665_0.17684447104493173", "host": "s3://npm-registry-packages"}}, "4.0.10": {"name": "fraction.js", "version": "4.0.10", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.10", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "08a2776cd6697902ae589715a485f99bcc4251ba", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.10.tgz", "fileCount": 13, "integrity": "sha512-MHkhk6ggCtwVhKR4pbF+aWrV+8cIVxyWhucESF/1NEcYDgRm4oze/2M09yGTGUQ3WQyZSdcoswJSThc2VGb+KQ==", "signatures": [{"sig": "MEUCIEcc7xWvsOTQtinqusCtsYisUIU9CxEPIea7BJRPckksAiEA5IWbWs9kPswO5ywlIunx5GU76Sau/2hs0HXnvb1+ZYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxLJxCRA9TVsSAnZWagAAtRYP/jRMepANGpkYoun7ubJf\nOcHWFBIlGGWVkBglz97EdIzrLUWrs3wtdIX0P9yWBhRx1GWMnnBeWrmkkUjp\nLrY+oN6z1ORRoRxPgMRfWrQ/IzkpDQhr1XBouPuZH5JqCaLTh45jbRuU49rr\nULgBpJpB5oBdUMSqKY422uOrzCPCUc+E5/SkgG4Y0WAVY39+BXQXunwrMzXh\nQTJWGry0XW51dfXF8xqgKCGN89Z2cvTgPsYAt+JDqX7GE9kPkfpMD1VIcq9b\nhz4eE8KokbbwoE7ZMx2lftXn6kegbo3MMynIFKjMAPW3NHvUjs/F/LGHqkCJ\nE3CC8wejxAcIoLe5uMFGtGV8UdCqtNo3n+XJibwt88kONahNNmCVsafVvZLR\nFwEFfZxr8I568h+0vGB4JXdeXjUHBrx3X5ewhCzlMScYUWeUHihqqYjL4E9H\nIq5GYzkCjZlSZxMG9i+OG9v1LQw1IUcI06ntVA70S3GuubM9U9kyZIRSo+4C\nnuomZjJuVqxXiO7b4BuDZ/dhcvaROQyLManhaB+ONzqiBPlie3nxNbRAMAi2\n1OD+vJ3IBruIL8SizlGbIDxKRNiBg0is3TZ/2ZHVLnnWn+Rr1MppTayqZpUI\niONQbB1UltWYqWcyb0l7O0cFz0RxAulKHgOeoA9O4XRh0v9CFeYpKJhBUqvq\nvRQm\r\n=jaEg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "5eaf574499ef835764f789dd0a259e760fbe7c18", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.10_1539617392519_0.389040418169317", "host": "s3://npm-registry-packages"}}, "4.0.11": {"name": "fraction.js", "version": "4.0.11", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.11", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "f82474349f7a198b6ad8d713d65621302a0e6f58", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.11.tgz", "fileCount": 13, "integrity": "sha512-wyQd1A06dTom7vTkf8OsnqXNi5XjleeaDFrvQX3SDIyMsVN1eb8KSGH3fsckF5FMlSWBrMfDCcJWVf99rXbf/g==", "signatures": [{"sig": "MEUCIQDA4hZTgFN8qwHu2IsowpskTx6zSkdFLoSbWWAHFi4qxQIgD8DlizzbG7ZNMGsnrEw+n4GauHhvrEgqsTyjECLgtQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5Yu8CRA9TVsSAnZWagAAQOEP/irblJYTv2IXjE7LEh2b\n4MSZYOQ/fEuo2yyFwB3qOCclfo5ic+plz2M+0hwIJQZjUSH1h0w+EkIRWU2W\nPlC0u7K27NFr9xpqiNbp8jLc5NW2que/HKBzrzmljM74j5Wld+sXdxtnOvZs\nvr4V15aSz1LM8w9mydhUhRj0WDqGrcamOX/cLr0ppqFjpDSeC89PRrakBWrs\nxbabRhfDWLFK/XECYzzOC07bXMszRdNu/OXnnbzQrQ05g1tM5aDOjRwsuj8e\nn2WUbCvuz1mbDExSZc9ekjbO2FUJvHwI4jOLMvBH4ACHXqMyXfqHSKm+E6/p\n7PKbixVAwVXADHwoc1Ut7GNLVJ3F6R3SSFIKIuW1LNuoCUPVbgPz84w6xALb\nNjQH1haadVosCLMgj8bWWKFsSAzGj4I0V/JQp30EhES/bU79b6CYQ1Am34I4\nMvPY22d5iSxCcN9eU8XxtziOMh+ETd+HiuaJwjHBaVF4fVQQyqJOqXxjMw07\nBx0NC7J0HlswiK4wxjWaYNH1efmJKAjyBzx97s2IuV9t8g6r7MnhfCBQ1FFM\nidC4XyregVfir/5mpB+HuzidRxbfUGrazYIKWjNuAO98cS3P8kPUbFdQ3Qrv\nNWdFtGB5WsdU+vFiJNGVxiSgQmYABNbgxKFtdUQRLw1/ErTvrK9hPwW8LLDr\nrpt2\r\n=9vaj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "69becb99363fcc108bd0b807e8443c114bfe4718", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "11.1.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.11_1541770171374_0.6311271916460928", "host": "s3://npm-registry-packages"}}, "4.0.12": {"name": "fraction.js", "version": "4.0.12", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "fraction.js@4.0.12", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "0526d47c65a5fb4854df78bc77f7bec708d7b8c3", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.12.tgz", "fileCount": 7, "integrity": "sha512-8Z1K0VTG4hzYY7kA/1sj4/r1/RWLBD3xwReT/RCrUCbzPszjNQCCsy3ktkU/eaEqX3MYa4pY37a52eiBlPMlhA==", "signatures": [{"sig": "MEUCIQDixqNPyZzK0TOZ6rhKJyZwlgze3KmaR8CxTDSWOS8dZAIgT4ooe57cBXulez4zhh7kwKtM334QLOC4/pC64COThOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLdllCRA9TVsSAnZWagAAfKgP/0AVDT4Kr4l64zrgMIyx\nd2bixEuav0Lbk6qUC5Jhp0ZXF6CLkAuVziT/yY2X9bXASqMO9qMvZnsq/C7h\nkZsFs4BfxtjdNXUlONoHMf8J0dXOOK4n3AAkGUcCpuWqlUfBFZlA48iMLi0P\nodNgtj2ssmhT32GL9D+PV1gbx59IQUDkyf4nvYOu//HSqHtK4+u+JCvXGSBx\nwZTddB8WOaOOSI/VLBWiJhUxH3FUgEtDa6i/SvyxmjhNpQqacb4vANzVvEVa\nW0DZaCNK4pVHkNOvXRmGmTYY3cJHVmtF50+lhsQdbnvDB6w29NFT4heVm4fJ\nS1fw7D52hAr+dzVu/awT+3skAT4AEWeUT5F4FPaKxV6OTfkmJAdYNs92LfMN\nqHbzJxOXoPuvz9MLRob77x+6gPC5hhbmbheZXJ10iQLFP2EIppKHPKajcBFF\nU9a8A21xgBNjLDcyUU0MA1THSKJmEDVsTVmY48X7oFgFSt69Rq9gFS+3xbZO\nlDZbt2aJ+XTnteUF1GhFmK1jf+F8gJDuKKh8TF+dX2KqEY+zSaOww2pQiy/f\nTzjGJmHyOcfT9zwhlOO+eN90yaMnVkkLAcWNufj+1IJ7R7rCR/8RarNlvkKQ\ntpPcErXFfd7tEmDfp+RzU7j/EBIDJiuSivm3J91XsFuwAXfOV1eCTXhf9rBM\nhQ2m\r\n=bULN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "engines": {"node": "*"}, "gitHead": "3ae4f0a42eb279d47cf6aa2c6f6dc90d1513a143", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "11.5.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.12_1546508645126_0.858811413715997", "host": "s3://npm-registry-packages"}}, "4.0.13": {"name": "fraction.js", "version": "4.0.13", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0-or-later", "_id": "fraction.js@4.0.13", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "3c1c315fa16b35c85fffa95725a36fa729c69dfe", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.13.tgz", "fileCount": 7, "integrity": "sha512-E1fz2Xs9ltlUp+qbiyx9wmt2n9dRzPsS11Jtdb8D2o+cC7wr9xkkKsVKJuBX0ST+LVS+LhLO+SbLJNtfWcJvXA==", "signatures": [{"sig": "MEQCIEjoqrT3QhZR/1RFzvtkjFeRypox9bFKQ2q5eDnSfbFSAiAOJxxajmkhH4SbD6YwPBv/vEJVA7ySPZnAbYoVdbbfKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4iCeCRA9TVsSAnZWagAAiY0P/0pJGjcbeUihOHnG/UU+\noj7uxYXaPuXlVfxNgMquQNAQBn6gvSKHhv7b1oocDk4S4v59u9uhDZTuquGE\npt9GfmtUdeH8CYYGCTDKH/TnVqwgUEAb/Po76dKvsWY0HTrxXR5WoGWPdfK2\nRbgI+Zf5rFK4uMKSPrdjWqDrmwEpk1BB769/OJeoKGdtik/+e6/sw8er/YXl\nlaOflZ/jUJpWKJ1p7sbQjYWuKccCmci+6Y3Su6oNilewljLUH78aC1t0QOPM\nh8EzsIJO6nYHmHPvzPdNv1XbODyfGVvTTMPvHIP28CWc4ZUhC6JFpazcj4Su\nXWfP4kHHrRcG50FVSeO/jhGDywIfiN1CiTBMJsh4QJ/3wWbVDo3ZqBCfDqNt\nas2eFyvGNGcFgktjJd2AJlBYsP43u2gcfoBs/F4qoZxjD/m8CL9N0cLt2biw\n/S6EZ0v8SqLEnjB5UyGX3pDxFV4Y6Zytx6WtbCCcwf9xhGtG+KweoqnAUGzt\nYObauHGLIQRNcGn3uA06JH6E9fowEB+3INEPIcPhm1LHKZgd76DGbgzr/48u\np0ULoXFZpuO4/1vmUEvpg2gq278rM0dxnIESLfu//RLzPzBXfdUvcAtxWJB+\n/xIPveiYGCj/51apE1TREEKetiErUVY4v2JzNMEzHa9oZHHrALhmua2CfIVN\nRRKc\r\n=c5tv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "gitHead": "0e2e8cbc94e084aab21b470c04ad3bb669922425", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "7.0.15", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "15.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.13_1608655005548_0.15056217199944633", "host": "s3://npm-registry-packages"}}, "4.0.14": {"name": "fraction.js", "version": "4.0.14", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0-or-later", "_id": "fraction.js@4.0.14", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "ddcd55d0d5ae2bf554a493ffcc60d3e6e709fbae", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.14.tgz", "fileCount": 7, "integrity": "sha512-x77+/j5Xd+mFLPzXT1UivKBzr8RIbmYhBOCT9gBFYdTkJMd56lv0ppY3v7F+kvHoQ5VALp109J367NiKpu7lWQ==", "signatures": [{"sig": "MEYCIQD8j/tKmnfNyszlbxZNer8Wfimw8+W9tsRBvoAgfYmIFgIhAIZ3jj4Rz7man83qru+XxwhIV4B631kmAsU8aNKwPJsm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnSUVCRA9TVsSAnZWagAAZlMP/1P4KNNzjszOWY94NkGq\n8B99zm8e30Jsalm7xfez3j6qQT86RQP+awhdPSYuGhrVhHoSw5g0Laqg0KvC\nEELsMRLXK1J5lJr0WaSFMxYwuytK+2liqNfgw2cU12YDC3Oe6T8E8vk4KJUT\nszdfqEYDzpdgGozYMarEV6eU+rUQsPGWqWFU6ProFnszWve7bE/BsYwkiNq7\ny8wIufLRTOAegK317Wvlfs9gPIcyn8BTo9+JtxMul/5baEXBJYts8elSnwbH\noPTH3foN4cBmmsaSgxF5eUuizeAdA4lth1BviZCZBVhQvxZ0I5Z3qfRLK2+0\nShgCYttRV9FsgpZxKaGX08HP128MtccS7lTxCArlUaY0DNCeCv/489dzUj84\nK9uQs0XqIYmSnwybiOByqj5rDpR0ArdN1Csymaz9pB0Nzkt1PFZiw/o0+LtY\nKyKP52XEvF4H2SZ28Rp+BYYjbYeMiby8UTUcuRq3e9dAlRDKeJNfUd3xFxhV\nRUmdMscTwVcQTYXdtYZ9WMgfRuQr0MhLxbb/BcFhC/2/Fmw5nK0ktVkWxE5r\nuK+p/Dz+9mKIu3bSPlNFgLHD9a4fz5h2AX4uy6rZp5hypqO6VITITZbdIbMW\nL+cDX4TDt6BJCersADnGvTGE0s6sANaqX6RvVKvgfD/u+lwyCz08lcPd9xyk\nPPKZ\r\n=jrzr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "gitHead": "20073f6baaae4bc87afc47b7d022c1fb82419d29", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "15.8.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.0.14_1620911380696_0.44718519648761124", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "fraction.js", "version": "4.1.0", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0-or-later", "_id": "fraction.js@4.1.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "229ec1cedc8c3c7e5d2d20688ba64f0a43af5830", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-o9lSKpK0TDqDwTL24Hxqi6I99s942l6TYkfl6WvGWgLOIFz/YonSGKfiSeMadoiNvTfqnfOa9mjb5SGVbBK9/w==", "signatures": [{"sig": "MEUCIQCCiW4ktWi3RAgIED1yQvqxOLeCuVZXHJbTOLcc6HyGbgIgaC/nAosSPCO9UxEfOQ3URYzvZorVqRajzcNrU8x9hmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnSq8CRA9TVsSAnZWagAAiLsP/3ZQ7lYciwTHh8Cj/klt\nYJv6I9laGBJfRhu3yp+NrPkZGyIul3LNocjU5FglfXUKc6XIq6iUeE7VWb1i\noEVFKF1jdPC7afEapJFC5iUABqD31rbq6bkf9FT49xKGLmocHWo1K77XUzF4\ne4bGnwKV22zh2tCdz4pzASLbnJabc77HthKX95maS5X1N6GImqw5sHw7AZJj\nZd1b+xO0QIgN8jxB0DydQEP1M0/71CX5nWHOEumUJ9li03TTxd+WsWFm31Ch\nGOSw7XWsRJBY4yyJ3uxHYxnosy/855pBuHZaa4BK2kZu7mZuOrmOxsu1/JQC\ng0Q59GNzf8qtI+JGw6M+LSOBRf8SKCBHVDW/TOmaXTwVSUkYkiAGfTkaArrK\nVjiX7Z1rJNIwvjyRiC8zI5rFQiqiMk6zz/M6W72w9TdAVBq2VJQ5iJue2cRl\nA8hq4p+g4DeZVnGiClmkmcbYw/L/sx2nqaMrnmRKjQUdigU8h4nBtUh9B8Rc\nOOqPgiH0vqZtnywTTj2+kup95yX4JMm6fod/PvnhU8nWT50PMdguIKYHUHfW\nubMHGj4LjahTtVJy8idlbUv+LGKuDnIQzd7l67YvZx/lJc8lprkm9lEt+nFp\nKeNSO3rrYREsaqiunfwZFJtjiJAFKh2n39lFIzDL3gtROqeAE0t/Af6HrwEQ\nC6mX\r\n=9SHJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "gitHead": "1e931d0db059859d74f8d7ebeec92d8ee0c99f21", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "15.8.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.1.0_1620912827738_0.758324545664669", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "fraction.js", "version": "4.1.1", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.1.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "http://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "ac4e520473dae67012d618aab91eda09bcb400ff", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.1.1.tgz", "fileCount": 7, "integrity": "sha512-MHOhvvxHTfRFpF1geTK9czMIZ6xclsEor2wkIGYYq+PxcQqT7vStJqjhe6S1TenZrMZzo+wlqOufBDVepUEgPg==", "signatures": [{"sig": "MEUCIANE6GFFPOoiTIPZ+lYme9Dk2pZVGud46nf4KxwHGsgZAiEA+k42lirQaNCU0WZB23BCCxmUbVWCAjdSkI0Lnsi373E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqkGyCRA9TVsSAnZWagAAYiUP/0CsJanZuIl5QtlP+fKu\nYbzCRRwEMD3kzT5tu9BQm8qdRKM5InQOtju4GSBFNO12rH0wGQ0Hqnzhgmr0\n3tav9aLa3juFNQbJJbRm2yDSIbY1f2iRzneGlxRf1xRTuuKeeBRmUiy1JTWA\ne383Z++TKaQsOHDdeeu6sYVAxk0UB27imu/iUVRtbvwRjopmo9HuWf3wRoyQ\n2nBPo7E9zm9oiCYanLZ3aE0yi3S5L2g5mb/wU5zdYaxxTMKSrArWuNtb45hh\n0o9ayOcKwyynie7Nc8SuhQ45ojmiLJDMlmBTwFgLTtBg9p+BTA4/3G+5uig5\nV4aXtWiG9amQH5qGK/PjJHIYNFLwL8sIN1PQjY7UX3tl2tsKhpmxpIVJL7c8\nhCzpqmCOBV10FY9ptv3525r+kGN9SZCZCXQ1pAlEtINn3PjoyG8R9O5YvlM0\nRVMooxg8lgD+KXunkcP06siQXQGRlE+Xck3bchQ7c1Ql1ylgWgaPsr2ZZqQY\nRZ0mUEnceUNBT3Hv5AuWIOPb684tfQmVDYvx2La0iu01qtohecFgdDQoIPFF\n1th3IEsA7nZ3RWelk4w6Yyfr6ko+Qbt+Wxkui+EhaMJpYoPcpYOz45ml4nHC\nXdopvg7X/34xmjcf+OY8sSXneOV/QqNVOQgI9KB0+JNfokVgTH6HZRcb/L5G\nHdAa\r\n=V95i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "c42e0bef621df2a55b368cd8a4948dd9639c8d5c", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "15.8.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.1.1_1621770673433_0.2382329941408352", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "fraction.js", "version": "4.1.2", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.1.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "13e420a92422b6cf244dff8690ed89401029fbe8", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.1.2.tgz", "fileCount": 7, "integrity": "sha512-o2RiJQ6DZaR/5+Si0qJUIy637QMRudSi9kU/FFzx9EZazrIdnBgpU+3sEWCxAVhH2RtxW2Oz+T4p2o8uOPVcgA==", "signatures": [{"sig": "MEYCIQDNEZBhLGdUjfBmYu3MgI5f/RKCpKCiI3LH94pinqFP/AIhALg3uTXkUiecmPgABHeRn2Y7fquBJz2Ma277exqp45UZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2rd2CRA9TVsSAnZWagAApSYQAJ30QJZUx32AIZFaTW0K\nsamwHs9YSy950vuIcGSaM81zmXCO93TD/xlOLUwIZ8+U9CDNDLQ8Y6rx5ocK\ne552yhrdvWLa06uyaXo0Qi7GgoZktRJ88jxffjZcRAVTTg/76jraXSWM55LJ\nVxt14XAvvwOjlA64I/UtIxjwFonQrdX6tiJgI2Jn3exmGvezXtKjqguGfnMg\n0qbDIPMOrWZXnsk7PBtRCWmJee3gZY/sGih68oxZYNPyiYVjHfn8WwnwADG6\n7vkqJOXKa+RY/pwINSf5W8POpkUuyXvXvFeMl3aDy7r9vo/lQg4VAhRwHCMs\nvW1oMXH8pDRmk5nZqBHwmD7cR1xZi5m9g10phStnxGPrLHit9K+uobP+8cY1\nwArZP2IDALDZstI8xZMwOmxWl+ISd2SFPnzdPR4Rqt3K7Q3qH01SDminDSeW\nkqa9yel8E1JXzhPgtOocxWQRUnBhH5VkUTnN6XWuEBI1GdB8ttjqZkIN2FJ7\nk4AjsS6kVYGzviv0gJo9dGg9SpxJrZNuTYEipV+SvIX0K/bOtsKNE6o2kM1L\nxGTg/PVW86WP7asvXLHhdteBQSPK5C8/wRq+m1Tj5SMxWOODKMFkoXZF8GQ5\nrvBFlpxwKh7L3eg4lJNI1OlkShif/CMElkVUwqm4tbf5VRUFSRjVdvlnBl6Y\nZhlK\r\n=K9S0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "212783179460137cfed10f00e87e242763868bf2", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "7.19.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.1.2_1636701794950_0.31110967818372925", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "fraction.js", "version": "4.1.3", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.1.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "be65b0f20762ef27e1e793860bc2dfb716e99e65", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.1.3.tgz", "fileCount": 7, "integrity": "sha512-pUHWWt6vHzZZiQJcM6S/0PXfS+g6FM4BF5rj9wZyreivhQPdsh5PpE25VtSNxq80wHS5RfY51Ii+8Z0Zl/pmzg==", "signatures": [{"sig": "MEYCIQDv6Z6HynWK3y3h4XUSDN9x7wZ24gukeP6Hz+lWMoVf3QIhAOGLRVomDy0O6FFaGC8NaoHwcIjaU8dKEduje0l1q/Ow", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiARlpCRA9TVsSAnZWagAAGdMP/1JA3AppyscwIYmJUUDZ\neYgnEmLI2qZH5mku6Vd1tcw6nUtJpHklBD/6kCKdFFg+B+kfRrpIuB/RoE5n\nWDf5FBfKmYtyW9EdFIaye7XzUhSmaUB0quMMTR7pusvVK9UW5NRutdcb9DkW\no1h0Zh4teJ85cLRMyaxYI0UMEnbVpL9y0FyJotRzNptHcKebNTYdXv854Axo\n5eymECypFIVr5A7WbVQd7Dvt2XdGJ0xSciK73ZOKIWOqijP3Ur7Weq7wnMtB\nZeuhl87VTCEbPHRZzSEPbUnrzDbjLDvIrquzmDaF2qJ+XANISSbbsoXqhJ6W\niorYNiuqfjqQxGcAvnqN6mCKYCa/2iPy06M6dBKQwmcDXPGcV4BvZRIfMuD/\n19Nw5hNnhwgphzdJMT/etQjmynP+QPdYbrLwsiqC+ErNgWIcPZ3Jx8qYVtL6\ntJ/rKwhclYYIYYMPZkMo2O80EK0PPda7fWRfnjcliRETx9CutKyAlYCsalUV\ndcdiW8EzQF3FpGVBJ26HODLhDH6Eldz4jVUE6ooi/xjSwOii0iQnHdNWV5jf\nhXRUnMKDXHL9WbM9NkGdVhR29u+4zjUIfebSsvA2JRrtS4puNvWsY+53WUb2\nB2yniF1hJ9zn2c382vIEMnbWbj3W4of7Cusn0upTc26cLc+oylafVk2fiozj\nzvGX\r\n=MqM2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "cd7d62ba2f6ecc9eeb6d23b6cc08e169ca4877ac", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.1.3_1644239209080_0.30378484148303375", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "fraction.js", "version": "4.2.0", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "448e5109a313a3527f5a3ab2119ec4cf0e0e2950", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.2.0.tgz", "fileCount": 7, "integrity": "sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==", "signatures": [{"sig": "MEYCIQDXoZEcz1Sd45YDbgGpXTvSRRZaQKI6bCZgbUBbsafdVgIhAIEaYpbVDppED48LWFEqLLz/yb+5uxhO2c4FnCxNw6cu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiI9ecACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/lBAAjC2oc8YCaLNzAdSTT98eVqKdnbSgx1dUPqi3VegopgfFE5Mk\r\nMxkCPTYR9XO0kC/kyoRbl8om9kCu8VjjrywVQSCcr+j4DF1B38Rkgqj+cWum\r\nm0Y+maMgjVfcpzwJhePQCeF/2WKVWu/FSLXQJpWs4fXtcVGvfbUj33xSwVA/\r\nrMrr/8OjPEEnK+GsyzsS+XZdDOHz0FyYku4zsuLgsvXWhCF4NRTTey2RxiHM\r\n+5QkQ3JyqU/nuMvqI077mlsXkfOtkSJhlB/Mib65BH9JGVWu+LriSP4jMH34\r\nT7/DHhgYeEzgIDT6jdHT1FjqxUNQIspBwkIRuq5KtAvT+EkYsneeBlDI6cx1\r\n/4DDKGRjhkWlM4ejEvwtRPeAo3GhuwMFJ/Ewza9ePycVtY37/CCq7aYCzr9b\r\nID5ebur19E9stwIfgK+jns7DZTcOiNqsWbqkTKDDGb+FiXIV75ApzxXEE1az\r\n0+3n84ZmdPW2RVlRHS1YwMH0ubTim6iPOlL9xQ1LFR+P5pbEqAcqwWA9+CvQ\r\n+xuBKSXm0zv45pOGUGvkMtEvHMlNqGii/Uzj6bAj/D8QlUomGg8Viy56/jjp\r\nlEHDmrwpDVShKbb/FmOsTMH/Vp4GXs/QcbvgMNkc41kBsQ6pakDTckH8Ap0p\r\n/456rQi3dV+zyejXidHP9/NqH8EcsQKacjI=\r\n=xBZv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "9d2f3d4abe58918cf19c82167784d36204834a1e", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.2.0_1646516124720_0.2552800070784398", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "fraction.js", "version": "4.2.1", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.2.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/infusion/Fraction.js/issues"}, "dist": {"shasum": "14b4cc886575a5684f8d5fd5759c5db376bb7bb8", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.2.1.tgz", "fileCount": 7, "integrity": "sha512-/KxoyCnPM0GwYI4NN0Iag38Tqt+od3/mLuguepLgCAKPn0ZhC544nssAW0tG2/00zXEYl9W+7hwAIpLHo6Oc7Q==", "signatures": [{"sig": "MEQCIHv8WVwvPe2iWuKgsxYJSeU7ImtQBl8lVfRwczOUNYJxAiBZ4VkPKw0M7ad8gp6u3RsFVxGZW5PDXWt+ffqWBjThYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67232}, "main": "fraction", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "eb0e1094434ecf4397685a2be287ddd5c15f073d", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.2.1_1692517275244_0.9047436435908094", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "fraction.js", "version": "4.3.0", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "ba77df59c194928c218764accbdc688a09cf961a", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.0.tgz", "fileCount": 7, "integrity": "sha512-btalnXjFelOv2cy86KzHWhUuMb622/AD8ce/MCH9C36xe7QRXjJZA+19fP+G5LT0fdRcbOHErMI3SPM11ZaVDg==", "signatures": [{"sig": "MEQCIG00zcoI7QIdPU6VHAzr/+Y0x9+t9l7GwvU0q2HK1Lt3AiBOiqc0KiawfYaV+7ZT0vrDemp7jrJ3ErxzpLRB0BK+/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67032}, "main": "fraction", "type": "module", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "exports": {"import": "./fraction.cjs", "require": "./fraction.cjs"}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "47102bdde4d8bf8f6d0d24b8aef3b50b41ed9949", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.0_1693327099623_0.09435997335166735", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "fraction.js", "version": "4.3.1", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "828c46dce1c8ad270e1c4170b3d06a2761e997d3", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.1.tgz", "fileCount": 7, "integrity": "sha512-nx0cki48JBA6ThPeUpeKCNpdhEl/9bRS+dAEYnRUod+Z1jhFfC3K/mBLorZZntqHM+GTH3/dkkpfoT3QITYe7g==", "signatures": [{"sig": "MEYCIQCdzol1CbFJzbv5hgO8YSKw9QIZCy6ZW4RvD7qSUjvi6AIhAI27SugcLg5PfU8vDTKst7Kz2y2RHIo291BjDYQ1ylVJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66669}, "main": "fraction.cjs", "type": "module", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "exports": {"import": "./fraction.cjs", "require": "./fraction.cjs"}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "fdd86bce3ae64a1b1f9f86d46a1a14fe6e446648", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.1_1693346965776_0.4050891038761675", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "fraction.js", "version": "4.3.2", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "ea144a08ed23a284bf9067153075f212771a92d9", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.2.tgz", "fileCount": 7, "integrity": "sha512-9VLF466MqX1OUP7/d9r7/Vsvu6Hpp+taXBLmiR5x6mEYfT0BDkGVBt5TyA1aDu1WzIE1sF8F66evOFaz7iAEGQ==", "signatures": [{"sig": "MEYCIQC7Yclq4fRVkwD09pdoOMFe7T/XQjNwsUdiZF2kSH0xjQIhAJpZv3lUsPksCLm0L8xMf/yfR0IIHDVfiRyJ7o5/GTZr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66699}, "main": "fraction.cjs", "type": "module", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "exports": {"import": "./fraction.cjs", "require": "./fraction.cjs"}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "1f9f8b223cd9c355314af743a1f72f840161d1c2", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.2_1693425309068_0.8629063447466829", "host": "s3://npm-registry-packages"}}, "4.3.3": {"name": "fraction.js", "version": "4.3.3", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "97b3d3eb436064ce0984a85c3d8efe00b1669c65", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.3.tgz", "fileCount": 7, "integrity": "sha512-tkkgxPcxJO0waFMe9i3zIES/tbhgOxXT/20ESNQkUvWASktF8Mxnt0vt147ZA+MNGme+w3mmPD0m61ySNmpTHw==", "signatures": [{"sig": "MEUCIARDCmPd0TOBV005QfACDQVyBRzkH25L+DtDmnA1AsZhAiEAwugdKSDUqS8GEGrcm6U1CDcH95cqvTEpRIQLr45RhLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66595}, "main": "fraction.cjs", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "b8f1f38098ec62743f9349039c01ced3914209a5", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.3_1693466762610_0.7406690912755471", "host": "s3://npm-registry-packages"}}, "4.3.4": {"name": "fraction.js", "version": "4.3.4", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.4", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "b2bac8249a610c3396106da97c5a71da75b94b1c", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.4.tgz", "fileCount": 7, "integrity": "sha512-pwiTgt0Q7t+GHZA4yaLjObx4vXmmdcS0iSJ19o8d/goUGgItX9UZWKWNnLHehxviD8wU2IWRsnR8cD5+yOJP2Q==", "signatures": [{"sig": "MEUCIQDcq8lbGY9OiBwbjqwrhXf9iivIX/OzFA9SOUoulxDp2QIgSjq+Yf2n+mKHdPCO3YCUf4dDV41MRXxZkKwmTaKFZd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66595}, "main": "fraction.js", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "f9a8e46121a5fcdc3bdd0ef59980a439fc6c4fef", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.4_1693469147832_0.886221217860893", "host": "s3://npm-registry-packages"}}, "4.3.5": {"name": "fraction.js", "version": "4.3.5", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "4e43c82c446e5dd64fd06aeb0cdab52f915fd110", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.5.tgz", "fileCount": 8, "integrity": "sha512-58DncB2bO/8ZvTHapG7U2KEbeFFyUbbrFFkHakecpdUSqJrQnEuBeTUPEggIVkx5cnugZJ4IVzk2Nbb32MOxBg==", "signatures": [{"sig": "MEUCICMxk1YyZ9DOsdP2pmAqhN641sA5Fe8dwd4SrT4dGwM2AiEAl7IQrhlDKW0K+KNgQt41/4OZXvdbkMoH+4B7Cl/KgS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85609}, "main": "fraction.js", "type": "module", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./fraction.js", "require": "./fraction.cjs"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "e7a04d2c1d2ee39f8903de56a041a7a7b5f48be1", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.5_1693554619937_0.7298252761023813", "host": "s3://npm-registry-packages"}}, "4.3.6": {"name": "fraction.js", "version": "4.3.6", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.6", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "e9e3acec6c9a28cf7bc36cbe35eea4ceb2c5c92d", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.6.tgz", "fileCount": 8, "integrity": "sha512-n2aZ9tNfYDwaHhvFTkhFErqOMIb8uyzSQ+vGJBjZyanAKZVbGUQ1sngfk9FdkBw7G26O7AgNjLcecLffD1c7eg==", "signatures": [{"sig": "MEUCIQCBda+r9qZFJ6YHw2YG4vX+NX/5kK112KdjLJcSqjHv6QIgDnA+SQSro5TD4W8rB8ZQx5TAhgxWndDjkFOMkvtabak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85611}, "main": "fraction.cjs", "type": "module", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./fraction.js", "require": "./fraction.cjs"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "e721cb793ff9625ebf8e5e964dc5d226a91b3396", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.6_1693579244073_0.9974444305172252", "host": "s3://npm-registry-packages"}}, "4.3.7": {"name": "fraction.js", "version": "4.3.7", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@4.3.7", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "06ca0085157e42fda7f9e726e79fefc4068840f7", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "fileCount": 8, "integrity": "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==", "signatures": [{"sig": "MEUCIQDVyIo/8IYfDTNA1Qafso6FkULpeFWsPRFCHf1Dp8iGXgIgE4oFP1xDQNcMWGuXO4z8ubm9TAH0uzP/6sLgfOiXHVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86195}, "main": "fraction.cjs", "type": "module", "title": "fraction.js", "types": "./fraction.d.ts", "engines": {"node": "*"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./fraction.js", "require": "./fraction.cjs"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "patreon"}, "gitHead": "8c41c1da7739c4cace893d95fe78a9d35b3877c9", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational number library", "directories": {"example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_4.3.7_1697128642500_0.367451537824659", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "fraction.js", "version": "5.0.0", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "84b2b2dfb693b8504aa3b0ca1fdb128ec0d026b0", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-de5iarCUTo4jNtBUmcSEmU1wvMtDNULOiG1D2FQRe9Wxz/RGr7k7oniUUnUOcjJ/W52zvN8OXDIVodQX+uV/6Q==", "signatures": [{"sig": "MEYCIQDmpCbTofuBvZpieebvDeO/+R60Bl+jkFvTySqAUdxR2gIhAMZEc4B0mKi9m0504jplG3KPcz2mbcItfb5vk50LOf51", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89492}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "33cc9e5325556c5f24716539ccb6d5b95590d883", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.0_1728060110809_0.025826230621441626", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "fraction.js", "version": "5.0.1", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "43dfdea79d8d984af315daa4a5e9ca7aa92e5bae", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-+o3zjQp1VbtRkdQHuA5e+1tN0GD6bBPT7QVI/JCGE3wd3dlvvmOaxDpfWiBGR42bPJieQiDIIFoaIHGeW0uWJg==", "signatures": [{"sig": "MEUCIQDYBZHOP4SLacbnjHy7R3QX6Z3+6jIfPfb5HaiwPnbVyQIgFvpYZrWa+owwu7ROrn/q7nHbepHW/LO2W9DmMHUceVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89551}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "e440f9cd01dfe30f97fca3b179ca08d3de4f8d99", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.1_1728135772960_0.9691758939410549", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "fraction.js", "version": "5.0.2", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "cbf7f0c12073c9fd9ec99e26c447c8fc42d76f7e", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.2.tgz", "fileCount": 8, "integrity": "sha512-yhtgw33pInOk1BaFks14x08ioJ+99VNUMcCEyeatyO31EypbTlitv7/IptkH/rvbSzzxmosHHjaV8rptt2kJCg==", "signatures": [{"sig": "MEUCICqrG9HxTSDlr4wRLRa/hdjPanWheFDpHm0W5KP7Ib6mAiEA9XmE9dFWWxiMZWzsw415qs0XR7vUs1SpuPDae7LrwAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89561}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "c64b1d6d738641dee01dfca6b66cf37de9989aca", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.2_1728238514422_0.0788122378460836", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "fraction.js", "version": "5.0.3", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "c3a83f69166eda52547b63bbb71472d9549237ba", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.3.tgz", "fileCount": 8, "integrity": "sha512-KcwJhP1eVCT0iuWo6r1XJ6sXMNLvGw8Et1TqPLKi/PzJx+xO7FC6fUyiE7HZZgL1rvYHpNdqAyKOdgocN1voeQ==", "signatures": [{"sig": "MEUCIBZUAXX/CbxXEB0PsbV3gX+mxGIN3UAv+yztvGrnafoDAiEAmQNX9KXmYtOorluN/jO51JXFkFI4d4O3b1TYZ9qam4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89777}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "7d9a3ec86bc2127ef2c51716b77ea4f7fac1f5d9", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.3_1728455911945_0.7760132381982223", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "fraction.js", "version": "5.0.4", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.4", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "b37464fe97c8462c5d98b35d3704ee4e252642b3", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.4.tgz", "fileCount": 8, "integrity": "sha512-5b1NochKz6Hi46IXReWs8L+RteW/OQ6OMoLGLm/Zy7ZsPwH+to52ZrjcdRFAOTdlO/Uifyjn8HgiNnliz9KDeg==", "signatures": [{"sig": "MEUCIEerHl3ilW16DlD/QOCcLZEOKlFinyUcH3RTkAJKlPAVAiEAmJMwlLo/EOAnn8xwGk/gTCS2utIRAzcK/KTo0FXngz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90278}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "39e61e7fadda7f46c5094e8644876cf80d20b28f", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.4_1728559524665_0.6308106164638574", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "fraction.js", "version": "5.0.5", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "950ed67d98b64c1d764a6ccca79d507f6398d99e", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.5.tgz", "fileCount": 8, "integrity": "sha512-/zf4ljUk4e1qPyUcxxxFOP4FqyMQKlOtEPjekZoO70TxBEnTpfM4eknyGTZGUQCGBn+hnxD9l2+eDPMQf0mHNw==", "signatures": [{"sig": "MEUCIHlodD0oFYgYBA5j/KxcDo9/u5LnhneoZrUHny/PWuhkAiEA5i9+gze8KOrY/jZNITK1qMoJ4ivqjZfD5scd1vAQS+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90547}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "2c9d4c2d837a9685b8158e81a3408645ff615e99", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.5_1728580401957_0.14121053544315432", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "fraction.js", "version": "5.0.6", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "bigint", "arbitrary precision", "rational numbers"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.0.6", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "1cd227309a181a722720a5e65cdfd4f0265a1cc7", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.0.6.tgz", "fileCount": 8, "integrity": "sha512-sQtpxkR+oTPCZ1RefIKCeytvMt7dkfdzvsQhCVe1kcZ6eOdB+d1QwxNfJLE8yf/+AlyH6coy7w6Hs3zKu3aSsw==", "signatures": [{"sig": "MEYCIQCHduYAJ3uh63u8p2hCMMoH164v7JaQ1Ut4XNtVuc0QagIhAJthy663/iNB0Rhvk6jkk5Lr1jluK57AhjpzHpk/3mnj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90533}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "acabc396917cb4752b7df6dbe272b3fc149e35a9", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.0.6_1728580568425_0.5266140510077524", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "fraction.js", "version": "5.1.0", "keywords": ["math", "numbers", "parser", "ratio", "fraction", "fractions", "rational", "rationals", "rational numbers", "bigint", "arbitrary precision", "mixed numbers", "decimal", "numerator", "denominator", "simplification"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.1.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "f79179e18a176cb56f1b38fa488d2d8a27b7513c", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.1.0.tgz", "fileCount": 8, "integrity": "sha512-7cfE2+OuFWt/w+Hm8FZHDIINNx7gFR5VyxFxAW4nbeFwwKdET3xgd9cC3RvErVuL2K8FcoB2AThPNgCVq6aZIw==", "signatures": [{"sig": "MEUCIEQg/c/VyFTNvD8sByW/Mzw7aidtoMi8OYJAiLnNQaLqAiEA+NijpKjOPEeuXd/R10KXaiUhLkB8OBPLpgTYeD8z+io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95156}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "70304f9ff6555bbbc4c0f3efae51e84531532603", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.1.0_1730309180439_0.7175748489998264", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "fraction.js", "version": "5.1.1", "keywords": ["math", "numbers", "parser", "ratio", "fraction", "fractions", "rational", "rationals", "rational numbers", "bigint", "arbitrary precision", "mixed numbers", "decimal", "numerator", "denominator", "simplification"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.1.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "5d190636d0af2bb681805bace128822bebf3d3f1", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.1.1.tgz", "fileCount": 8, "integrity": "sha512-1ZU8WTKq/emjynSmw+in31WLl9ll1c6kV1quuuQDXse1V+Eke5Fxy9VMLFR+reGOQGaW2Od87NJalvtJiTCDUA==", "signatures": [{"sig": "MEQCIDF45lZCbpAA3BaBL9Xag3PyNr85D7Q1ii5YNlPd+y+SAiAkOWCO7lEffjiZ17n/HvL4l00JXQ4gFIjzQpl4/WE7Ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95172}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "b773e7ad0562ff131fddbb9bbc1b5a8de393d086", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.1.1_1730481698060_0.25239498196263965", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "fraction.js", "version": "5.2.0", "keywords": ["math", "numbers", "parser", "ratio", "fraction", "fractions", "rational", "rationals", "rational numbers", "bigint", "arbitrary precision", "mixed numbers", "decimal", "numerator", "denominator", "simplification"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "beaec5444a9a008b0a946209eb046c0000e36a52", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.2.0.tgz", "fileCount": 8, "integrity": "sha512-KaFAAu+6f5BfkafXuWG9eNUF56o4wZnH0a5EfEQaROVRCxPAS7fYxBaNQEOphDptUP+sZluS26zG5uOaB9yYPw==", "signatures": [{"sig": "MEQCIC9HapyvqWrlQXXHAOeWYTsWFX1DwgRHcNZxCbwC64D3AiBJLQlOn/WI5Qt7IVsUDl5L4ItX5IAjMakToQUPElQknw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102057}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "6f9d1240a7935bb03b5459c8af6eb84ad0601e0d", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.2.0_1731855982328_0.6907883715744261", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "fraction.js", "version": "5.2.1", "keywords": ["math", "numbers", "parser", "ratio", "fraction", "fractions", "rational", "rationals", "rational numbers", "bigint", "arbitrary precision", "mixed numbers", "decimal", "numerator", "denominator", "simplification"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fraction.js@5.2.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "dist": {"shasum": "93ffc9507b1a68a1271883aa28e98f58a1c0c6b3", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.2.1.tgz", "fileCount": 8, "integrity": "sha512-Ah6t/7YCYjrPUFUFsOsRLMXAdnYM+aQwmojD2Ayb/Ezr82SwES0vuyQ8qZ3QO8n9j7W14VJuVZZet8U3bhSdQQ==", "signatures": [{"sig": "MEQCICP7N+aXqf5zFddUmL7gFEnyNG01oMxj3XYjGq0Xtf0+AiAdF6KxhbuQnVdZbb/IwGs819Qpl09oNP+JmIIzaaKLAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102279}, "main": "./dist/fraction.js", "title": "Fraction.js", "types": "./fraction.d.ts", "unpkg": "./dist/fraction.min.js", "module": "./dist/fraction.mjs", "browser": "./dist/fraction.min.js", "engines": {"node": ">= 12"}, "exports": {".": {"types": "./fraction.d.ts", "import": "./dist/fraction.mjs", "require": "./dist/fraction.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "2bb7b050a8916b649ba08edfe23babdc0846ec3f", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Fraction"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Fraction.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A rational numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/fraction.js_5.2.1_1731856902328_0.8971725221069871", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "fraction.js", "title": "Fraction.js", "version": "5.2.2", "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "description": "A rational numbers library", "keywords": ["math", "numbers", "parser", "ratio", "fraction", "fractions", "rational", "rationals", "rational numbers", "bigint", "arbitrary precision", "mixed numbers", "decimal", "numerator", "denominator", "simplification"], "private": false, "main": "./dist/fraction.js", "module": "./dist/fraction.mjs", "types": "./fraction.d.ts", "browser": "./dist/fraction.min.js", "unpkg": "./dist/fraction.min.js", "exports": {".": {"types": "./fraction.d.ts", "require": "./dist/fraction.js", "import": "./dist/fraction.mjs"}}, "repository": {"type": "git", "url": "git+ssh://**************/rawify/Fraction.js.git"}, "funding": {"type": "github", "url": "https://github.com/sponsors/rawify"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://raw.org/"}, "license": "MIT", "engines": {"node": ">= 12"}, "directories": {"example": "examples", "test": "tests"}, "scripts": {"build": "crude-build Fraction", "test": "mocha tests/*.js"}, "devDependencies": {"crude-build": "^0.1.2", "mocha": "*"}, "gitHead": "859b36ee3de85172343b1cee23f3fb5d018ed091", "_id": "fraction.js@5.2.2", "_nodeVersion": "16.20.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-uXBDv5knpYmv/2gLzWQ5mBHGBRk9wcKTeWu6GLTUEQfjCxO09uM/mHDrojlL+Q1mVGIIFo149Gba7od1XPgSzQ==", "shasum": "c1295bc47307c5f93f75790859645fd74edaa195", "tarball": "https://registry.npmjs.org/fraction.js/-/fraction.js-5.2.2.tgz", "fileCount": 20, "unpackedSize": 145827, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDEj4A+XMxheAUK0JJXVq2T7cwx3xTvgSw5vDeNYet21AiAdufOUXN8VtlnQTJN0A7AZVL1QkIPSNcdRR/C9kTxnrA=="}]}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fraction.js_5.2.2_1743363520027_0.904749352464834"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-03-12T01:46:09.440Z", "modified": "2025-03-30T19:38:40.447Z", "1.0.0": "2014-03-12T01:46:09.440Z", "1.1.0": "2014-03-21T19:04:46.856Z", "1.2.0": "2014-04-17T01:27:50.505Z", "1.2.1": "2014-06-20T09:39:39.099Z", "1.3.0": "2014-06-29T13:27:22.836Z", "1.5.0": "2015-02-08T18:40:22.146Z", "1.6.0": "2015-02-14T12:29:02.844Z", "1.7.0": "2015-03-25T19:19:20.808Z", "1.9.0": "2015-05-15T08:32:41.163Z", "2.0.0": "2015-05-30T23:53:50.352Z", "2.2.0": "2015-06-04T23:30:52.206Z", "2.3.0": "2015-06-23T11:16:00.181Z", "2.4.0": "2015-07-03T14:52:32.554Z", "2.4.1": "2015-07-07T12:56:03.869Z", "2.5.0": "2015-07-29T17:06:31.269Z", "2.6.0": "2015-08-23T01:26:16.843Z", "2.7.0": "2015-08-23T20:20:46.533Z", "2.8.0": "2015-09-04T16:48:58.496Z", "2.9.0": "2015-09-06T11:01:45.144Z", "3.0.0": "2015-09-09T11:09:48.253Z", "3.1.0": "2016-01-18T02:09:43.567Z", "3.2.0": "2016-01-18T05:20:54.892Z", "3.2.5": "2016-02-06T12:29:12.803Z", "3.3.0": "2016-03-24T22:53:31.838Z", "3.3.1": "2016-04-07T15:33:59.598Z", "4.0.0": "2017-02-08T23:10:46.386Z", "4.0.1": "2017-06-13T18:26:16.076Z", "4.0.2": "2017-06-30T14:13:13.628Z", "4.0.3": "2017-07-26T15:13:26.259Z", "4.0.4": "2017-12-09T00:17:01.366Z", "4.0.5": "2018-03-29T19:56:15.986Z", "4.0.6": "2018-03-29T21:06:17.877Z", "4.0.7": "2018-03-29T21:23:13.612Z", "4.0.8": "2018-04-13T22:54:19.936Z", "4.0.9": "2018-07-18T22:59:48.780Z", "4.0.10": "2018-10-15T15:29:52.668Z", "4.0.11": "2018-11-09T13:29:31.527Z", "4.0.12": "2019-01-03T09:44:05.252Z", "4.0.13": "2020-12-22T16:36:45.688Z", "4.0.14": "2021-05-13T13:09:40.879Z", "4.1.0": "2021-05-13T13:33:47.918Z", "4.1.1": "2021-05-23T11:51:13.632Z", "4.1.2": "2021-11-12T07:23:15.209Z", "4.1.3": "2022-02-07T13:06:49.250Z", "4.2.0": "2022-03-05T21:35:24.883Z", "4.2.1": "2023-08-20T07:41:15.483Z", "4.3.0": "2023-08-29T16:38:19.827Z", "4.3.1": "2023-08-29T22:09:25.989Z", "4.3.2": "2023-08-30T19:55:09.245Z", "4.3.3": "2023-08-31T07:26:02.868Z", "4.3.4": "2023-08-31T08:05:48.153Z", "4.3.5": "2023-09-01T07:50:20.115Z", "4.3.6": "2023-09-01T14:40:44.267Z", "4.3.7": "2023-10-12T16:37:22.757Z", "5.0.0": "2024-10-04T16:41:51.085Z", "5.0.1": "2024-10-05T13:42:53.325Z", "5.0.2": "2024-10-06T18:15:14.602Z", "5.0.3": "2024-10-09T06:38:32.149Z", "5.0.4": "2024-10-10T11:25:24.907Z", "5.0.5": "2024-10-10T17:13:22.200Z", "5.0.6": "2024-10-10T17:16:08.661Z", "5.1.0": "2024-10-30T17:26:20.622Z", "5.1.1": "2024-11-01T17:21:38.309Z", "5.2.0": "2024-11-17T15:06:22.611Z", "5.2.1": "2024-11-17T15:21:42.505Z", "5.2.2": "2025-03-30T19:38:40.264Z"}, "bugs": {"url": "https://github.com/rawify/Fraction.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://raw.org/"}, "license": "MIT", "homepage": "https://raw.org/article/rational-numbers-in-javascript/", "keywords": ["math", "numbers", "parser", "ratio", "fraction", "fractions", "rational", "rationals", "rational numbers", "bigint", "arbitrary precision", "mixed numbers", "decimal", "numerator", "denominator", "simplification"], "repository": {"type": "git", "url": "git+ssh://**************/rawify/Fraction.js.git"}, "description": "A rational numbers library", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "readme": "# Fraction.js - ℚ in JavaScript\n\n[![NPM Package](https://img.shields.io/npm/v/fraction.js.svg?style=flat)](https://npmjs.org/package/fraction.js \"View this project on npm\")\n[![MIT license](http://img.shields.io/badge/license-MIT-brightgreen.svg)](http://opensource.org/licenses/MIT)\n\nDo you find the limitations of floating-point arithmetic frustrating, especially when rational and irrational numbers like π or √2 are stored within the same finite precision? This can lead to avoidable inaccuracies such as:\n\n```javascript\n1 / 98 * 98 // Results in 0.9999999999999999\n```\n\nFor applications requiring higher precision or where working with fractions is preferable, consider incorporating *Fraction.js* into your project. Integration is straightforward:\n\n```javascript\nimport Fraction from 'fraction.js';\n// Alternatively\nvar Fraction = require('fraction.js');\n```\n\nThe library effectively addresses precision issues, as demonstrated below:\n\n```javascript\nFraction(1).div(98).mul(98) // Returns 1\n```\n\n*Fraction.js* uses a `BigInt` representation for both the numerator and denominator, ensuring minimal performance overhead while maximizing accuracy. Its design is optimized for precision, making it an ideal choice as a foundational library for other math tools, such as [Polynomial.js](https://github.com/infusion/Polynomial.js) and [Math.js](https://github.com/josdejong/mathjs).\n\n## Convert Decimal to Fraction\n\nOne of the core features of *Fraction.js* is its ability to seamlessly convert decimal numbers into fractions.\n\n```javascript\nlet x = new Fraction(1.88);\nlet res = x.toFraction(true); // Returns \"1 22/25\" as a string\n```\n\nThis is particularly useful when you need precise fraction representations instead of dealing with the limitations of floating-point arithmetic. What if you allow some error tolerance?\n\n```javascript\nlet x = new Fraction(0.33333);\nlet res = x.simplify(0.001) // Error < 0.001\n       .toFraction(); // Returns \"1/3\" as a string\n```\n\n## Precision\n\nAs native `BigInt` support in JavaScript becomes more common, libraries like *Fraction.js* use it to handle calculations with higher precision. This improves the speed and accuracy of math operations with large numbers, providing a better solution for tasks that need more precision than floating-point numbers can offer.\n\n## Examples / Motivation\n\nA simple example of using *Fraction.js* might look like this:\n\n```javascript\nvar f = new Fraction(\"9.4'31'\"); // 9.4313131313131...\nf.mul([-4, 3]).mod(\"4.'8'\"); // 4.88888888888888...\n```\n\nThe result can then be displayed as:\n\n```javascript\nconsole.log(f.toFraction()); // -4154 / 1485\n```\n\nAdditionally, you can access the internal attributes of the fraction, such as the sign (s), numerator (n), and denominator (d). Keep in mind that these values are stored as `BigInt`:\n\n```javascript\nNumber(f.s) * Number(f.n) / Number(f.d) = -1 * 4154 / 1485 = -2.797306...\n```\n\nIf you attempted to calculate this manually using floating-point arithmetic, you'd get something like:\n\n```javascript\n(9.4313131 * (-4 / 3)) % 4.888888 = -2.797308133...\n```\n\nWhile the result is reasonably close, it’s not as accurate as the fraction-based approach that *Fraction.js* provides, especially when dealing with repeating decimals or complex operations. This highlights the value of precision that the library brings.\n\n### Laplace Probability\n\nHere's a straightforward example of using *Fraction.js* to calculate probabilities. Let's determine the probability of rolling a specific outcome on a fair die:\n\n- **P({3})**: The probability of rolling a 3.\n- **P({1, 4})**: The probability of rolling either 1 or 4.\n- **P({2, 4, 6})**: The probability of rolling 2, 4, or 6.\n\n#### P({3}):\n\n```javascript\nvar p = new Fraction([3].length, 6).toString(); // \"0.1(6)\"\n```\n\n#### P({1, 4}):\n\n```javascript\nvar p = new Fraction([1, 4].length, 6).toString(); // \"0.(3)\"\n```\n\n#### P({2, 4, 6}):\n\n```javascript\nvar p = new Fraction([2, 4, 6].length, 6).toString(); // \"0.5\"\n```\n\n### Convert degrees/minutes/seconds to precise rational representation:\n\n57+45/60+17/3600\n\n```javascript\nvar deg = 57; // 57°\nvar min = 45; // 45 Minutes\nvar sec = 17; // 17 Seconds\n\nnew Fraction(deg).add(min, 60).add(sec, 3600).toString() // -> 57.7547(2)\n```\n\n\n### Rational approximation of irrational numbers\n\nTo approximate a number like *sqrt(5) - 2* with a numerator and denominator, you can reformat the equation as follows: *pow(n / d + 2, 2) = 5*.\n\nThen the following algorithm will generate the rational number besides the binary representation.\n\n```javascript\nvar x = \"/\", s = \"\";\n\nvar a = new Fraction(0),\n    b = new Fraction(1);\nfor (var n = 0; n <= 10; n++) {\n\n  var c = a.add(b).div(2);\n\n  console.log(n + \"\\t\" + a + \"\\t\" + b + \"\\t\" + c + \"\\t\" + x);\n\n  if (c.add(2).pow(2).valueOf() < 5) {\n    a = c;\n    x = \"1\";\n  } else {\n    b = c;\n    x = \"0\";\n  }\n  s+= x;\n}\nconsole.log(s)\n```\n\nThe result is\n\n```\nn   a[n]        b[n]        c[n]            x[n]\n0   0/1         1/1         1/2             /\n1   0/1         1/2         1/4             0\n2   0/1         1/4         1/8             0\n3   1/8         1/4         3/16            1\n4   3/16        1/4         7/32            1\n5   7/32        1/4         15/64           1\n6   15/64       1/4         31/128          1\n7   15/64       31/128      61/256          0\n8   15/64       61/256      121/512         0\n9   15/64       121/512     241/1024        0\n10  241/1024    121/512     483/2048        1\n```\n\nThus the approximation after 11 iterations of the bisection method is *483 / 2048* and the binary representation is 0.00111100011 (see [WolframAlpha](http://www.wolframalpha.com/input/?i=sqrt%285%29-2+binary))\n\nI published another example on how to approximate PI with fraction.js on my [blog](https://raw.org/article/rational-numbers-in-javascript/) (Still not the best idea to approximate irrational numbers, but it illustrates the capabilities of Fraction.js perfectly).\n\n\n### Get the exact fractional part of a number\n\n```javascript\nvar f = new Fraction(\"-6.(3416)\");\nconsole.log(f.mod(1).abs().toFraction()); // = 3416/9999\n```\n\n### Mathematical correct modulo\n\nThe behaviour on negative congruences is different to most modulo implementations in computer science. Even the *mod()* function of Fraction.js behaves in the typical way. To solve the problem of having the mathematical correct modulo with Fraction.js you could come up with this:\n\n```javascript\nvar a = -1;\nvar b = 10.99;\n\nconsole.log(new Fraction(a)\n  .mod(b)); // Not correct, usual Modulo\n\nconsole.log(new Fraction(a)\n  .mod(b).add(b).mod(b)); // Correct! Mathematical Modulo\n```\n\nfmod() imprecision circumvented\n---\nIt turns out that Fraction.js outperforms almost any fmod() implementation, including JavaScript itself, [php.js](http://phpjs.org/functions/fmod/), C++, Python, Java and even Wolframalpha due to the fact that numbers like 0.05, 0.1, ... are infinite decimal in base 2.\n\nThe equation *fmod(4.55, 0.05)* gives *0.04999999999999957*, wolframalpha says *1/20*. The correct answer should be **zero**, as 0.05 divides 4.55 without any remainder.\n\n\n## Parser\n\nAny function (see below) as well as the constructor of the *Fraction* class parses its input and reduce it to the smallest term.\n\nYou can pass either Arrays, Objects, Integers, Doubles or Strings.\n\n### Arrays / Objects\n\n```javascript\nnew Fraction(numerator, denominator);\nnew Fraction([numerator, denominator]);\nnew Fraction({n: numerator, d: denominator});\n```\n\n### Integers\n\n```javascript\nnew Fraction(123);\n```\n\n### Doubles\n\n```javascript\nnew Fraction(55.4);\n```\n\n**Note:** If you pass a double as it is, Fraction.js will perform a number analysis based on Farey Sequences. If you concern performance, cache Fraction.js objects and pass arrays/objects.\n\nThe method is really precise, but too large exact numbers, like 1234567.9991829 will result in a wrong approximation. If you want to keep the number as it is, convert it to a string, as the string parser will not perform any further observations. If you have problems with the approximation, in the file `examples/approx.js` is a different approximation algorithm, which might work better in some more specific use-cases.\n\n\n### Strings\n\n```javascript\nnew Fraction(\"123.45\");\nnew Fraction(\"123/45\"); // A rational number represented as two decimals, separated by a slash\nnew Fraction(\"123:45\"); // A rational number represented as two decimals, separated by a colon\nnew Fraction(\"4 123/45\"); // A rational number represented as a whole number and a fraction\nnew Fraction(\"123.'456'\"); // Note the quotes, see below!\nnew Fraction(\"123.(456)\"); // Note the brackets, see below!\nnew Fraction(\"123.45'6'\"); // Note the quotes, see below!\nnew Fraction(\"123.45(6)\"); // Note the brackets, see below!\n```\n\n### Two arguments\n\n```javascript\nnew Fraction(3, 2); // 3/2 = 1.5\n```\n\n### Repeating decimal places\n\n*Fraction.js* can easily handle repeating decimal places. For example *1/3* is *0.3333...*. There is only one repeating digit. As you can see in the examples above, you can pass a number like *1/3* as \"0.'3'\" or \"0.(3)\", which are synonym. There are no tests to parse something like 0.166666666 to 1/6! If you really want to handle this number, wrap around brackets on your own with the function below for example: 0.1(66666666)\n\nAssume you want to divide 123.32 / 33.6(567). [WolframAlpha](http://www.wolframalpha.com/input/?i=123.32+%2F+%2812453%2F370%29) states that you'll get a period of 1776 digits. *Fraction.js* comes to the same result. Give it a try:\n\n```javascript\nvar f = new Fraction(\"123.32\");\nconsole.log(\"Bam: \" + f.div(\"33.6(567)\"));\n```\n\nTo automatically make a number like \"0.123123123\" to something more Fraction.js friendly like \"0.(123)\", I hacked this little brute force algorithm in a 10 minutes. Improvements are welcome...\n\n```javascript\nfunction formatDecimal(str) {\n\n  var comma, pre, offset, pad, times, repeat;\n\n  if (-1 === (comma = str.indexOf(\".\")))\n    return str;\n\n  pre = str.substr(0, comma + 1);\n  str = str.substr(comma + 1);\n\n  for (var i = 0; i < str.length; i++) {\n\n    offset = str.substr(0, i);\n\n    for (var j = 0; j < 5; j++) {\n\n      pad = str.substr(i, j + 1);\n\n      times = Math.ceil((str.length - offset.length) / pad.length);\n\n      repeat = new Array(times + 1).join(pad); // Silly String.repeat hack\n\n      if (0 === (offset + repeat).indexOf(str)) {\n        return pre + offset + \"(\" + pad + \")\";\n      }\n    }\n  }\n  return null;\n}\n\nvar f, x = formatDecimal(\"13.0123123123\"); // = 13.0(123)\nif (x !== null) {\n  f = new Fraction(x);\n}\n```\n\n## Attributes\n\n\nThe Fraction object allows direct access to the numerator, denominator and sign attributes. It is ensured that only the sign-attribute holds sign information so that a sign comparison is only necessary against this attribute.\n\n```javascript\nvar f = new Fraction('-1/2');\nconsole.log(f.n); // Numerator: 1\nconsole.log(f.d); // Denominator: 2\nconsole.log(f.s); // Sign: -1\n```\n\n\n## Functions\n\n### Fraction abs()\n\nReturns the actual number without any sign information\n\n### Fraction neg()\n\nReturns the actual number with flipped sign in order to get the additive inverse\n\n### Fraction add(n)\n\nReturns the sum of the actual number and the parameter n\n\n### Fraction sub(n)\n\nReturns the difference of the actual number and the parameter n\n\n### Fraction mul(n)\n\nReturns the product of the actual number and the parameter n\n\n### Fraction div(n)\n\nReturns the quotient of the actual number and the parameter n\n\n### Fraction pow(exp)\n\nReturns the power of the actual number, raised to an possible rational exponent. If the result becomes non-rational the function returns `null`.\n\n### Fraction log(base)\n\nReturns the logarithm of the actual number to a given rational base. If the result becomes non-rational the function returns `null`.\n\n### Fraction mod(n)\n\nReturns the modulus (rest of the division) of the actual object and n (this % n). It's a much more precise [fmod()](#fmod-impreciseness-circumvented) if you like. Please note that *mod()* is just like the modulo operator of most programming languages. If you want a mathematical correct modulo, see [here](#mathematical-correct-modulo).\n\n### Fraction mod()\n\nReturns the modulus (rest of the division) of the actual object (numerator mod denominator)\n\n### Fraction gcd(n)\n\nReturns the fractional greatest common divisor\n\n### Fraction lcm(n)\n\nReturns the fractional least common multiple\n\n### Fraction ceil([places=0-16])\n\nReturns the ceiling of a rational number with Math.ceil\n\n### Fraction floor([places=0-16])\n\nReturns the floor of a rational number with Math.floor\n\n### Fraction round([places=0-16])\n\nReturns the rational number rounded with Math.round\n\n### Fraction roundTo(multiple)\n\nRounds a fraction to the closest multiple of another fraction. \n\n### Fraction inverse()\n\nReturns the multiplicative inverse of the actual number (n / d becomes d / n) in order to get the reciprocal\n\n### Fraction simplify([eps=0.001])\n\nSimplifies the rational number under a certain error threshold. Ex. `0.333` will be `1/3` with `eps=0.001`\n\n### boolean equals(n)\n\nCheck if two rational numbers are equal\n\n### boolean lt(n)\n\nCheck if this rational number is less than another\n\n### boolean lte(n)\n\nCheck if this rational number is less than or equal another\n\n### boolean gt(n)\n\nCheck if this rational number is greater than another\n\n### boolean gte(n)\n\nCheck if this rational number is greater than or equal another\n\n### int compare(n)\n\nCompare two numbers.\n```\nresult < 0: n is greater than actual number\nresult > 0: n is smaller than actual number\nresult = 0: n is equal to the actual number\n```\n\n### boolean divisible(n)\n\nCheck if two numbers are divisible (n divides this)\n\n### double valueOf()\n\nReturns a decimal representation of the fraction\n\n### String toString([decimalPlaces=15])\n\nGenerates an exact string representation of the given object. For repeating decimal places, digits within repeating cycles are enclosed in parentheses, e.g., `1/3 = \"0.(3)\"`. For other numbers, the string will include up to the specified `decimalPlaces` significant digits, including any trailing zeros if truncation occurs. For example, `1/2` will be represented as `\"0.5\"`, without additional trailing zeros.\n\n**Note:** Since both `valueOf()` and `toString()` are provided, `toString()` will only be invoked implicitly when the object is used in a string context. For instance, when using the plus operator like `\"123\" + new Fraction`, `valueOf()` will be called first, as JavaScript attempts to combine primitives before concatenating them, with the string type taking precedence. However, `alert(new Fraction)` or `String(new Fraction)` will behave as expected. To ensure specific behavior, explicitly call either `toString()` or `valueOf()`.\n\n### String toLatex(showMixed=false)\n\nGenerates an exact LaTeX representation of the actual object. You can see a [live demo](https://raw.org/article/rational-numbers-in-javascript/) on my blog.\n\nThe optional boolean parameter indicates if you want to show the a mixed fraction. \"1 1/3\" instead of \"4/3\"\n\n### String toFraction(showMixed=false)\n\nGets a string representation of the fraction\n\nThe optional boolean parameter indicates if you want to showa mixed fraction. \"1 1/3\" instead of \"4/3\"\n\n### Array toContinued()\n\nGets an array of the fraction represented as a continued fraction. The first element always contains the whole part.\n\n```javascript\nvar f = new Fraction('88/33');\nvar c = f.toContinued(); // [2, 1, 2]\n```\n\n### Fraction clone()\n\nCreates a copy of the actual Fraction object\n\n\n## Exceptions\n\nIf a really hard error occurs (parsing error, division by zero), *Fraction.js* throws exceptions! Please make sure you handle them correctly.\n\n\n## Installation\n\nInstalling fraction.js is as easy as cloning this repo or use the following command:\n\n```bash\nnpm install fraction.js\n```\n\n## Using Fraction.js with the browser\n\n```html\n<script src=\"fraction.min.js\"></script>\n<script>\n    console.log(Fraction(\"123/456\"));\n</script>\n```\n\n## Using Fraction.js with TypeScript\n\n```js\nimport Fraction from \"fraction.js\";\nconsole.log(Fraction(\"123/456\"));\n```\n\n\n## Coding Style\n\nAs every library I publish, Fraction.js is also built to be as small as possible after compressing it with Google Closure Compiler in advanced mode. Thus the coding style orientates a little on maxing-out the compression rate. Please make sure you keep this style if you plan to extend the library.\n\n## Building the library\n\nAfter cloning the Git repository run:\n\n```bash\nnpm install\nnpm run build\n```\n\n## Run a test\n\nTesting the source against the shipped test suite is as easy as\n\n```bash\nnpm run test\n```\n\n## Copyright and Licensing\n\nCopyright (c) 2025, [Robert Eisele](https://raw.org/)\nLicensed under the MIT license.\n", "readmeFilename": "README.md", "users": {"snowdream": true}}