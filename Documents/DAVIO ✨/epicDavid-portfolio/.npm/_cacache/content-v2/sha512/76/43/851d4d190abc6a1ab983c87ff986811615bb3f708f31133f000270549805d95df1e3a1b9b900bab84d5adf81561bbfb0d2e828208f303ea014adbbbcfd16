{"_id": "camelcase", "_rev": "70-b16902dd65095751575310d61a309f6e", "name": "camelcase", "time": {"modified": "2023-08-09T16:37:55.753Z", "created": "2013-10-30T20:39:45.449Z", "0.0.0": "2013-10-30T20:39:50.719Z", "1.0.0": "2014-10-12T11:06:54.223Z", "1.0.1": "2014-10-12T12:07:56.522Z", "1.0.2": "2014-11-25T07:46:37.286Z", "1.1.0": "2015-05-15T23:13:02.556Z", "1.2.0": "2015-07-30T15:11:19.077Z", "1.2.1": "2015-08-01T10:38:13.833Z", "2.0.0": "2015-11-15T12:49:16.601Z", "2.0.1": "2015-11-17T14:12:18.285Z", "2.1.0": "2016-01-24T18:40:18.240Z", "2.1.1": "2016-03-12T17:30:36.527Z", "3.0.0": "2016-05-04T17:33:27.903Z", "4.0.0": "2016-11-08T15:54:49.686Z", "4.1.0": "2017-03-30T09:16:02.724Z", "5.0.0": "2018-03-28T11:08:34.664Z", "5.1.0": "2019-03-04T05:21:30.689Z", "5.2.0": "2019-03-05T05:33:09.693Z", "5.3.0": "2019-04-01T08:20:50.223Z", "5.3.1": "2019-04-03T13:34:32.701Z", "6.0.0": "2020-04-07T03:23:00.391Z", "6.1.0": "2020-10-10T17:04:08.402Z", "6.2.0": "2020-10-28T22:19:50.567Z", "6.2.1": "2021-11-15T02:48:10.463Z", "6.3.0": "2022-01-01T20:29:34.388Z", "7.0.0": "2022-06-06T05:08:17.147Z", "7.0.1": "2022-12-13T12:46:35.180Z", "8.0.0": "2023-08-09T16:37:55.548Z"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist-tags": {"latest": "8.0.0"}, "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "readme": "# camelcase\n\n> Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`\n\nCorrectly handles Unicode strings.\n\nIf you use this on untrusted user input, don't forget to limit the length to something reasonable.\n\n## Install\n\n```sh\nnpm install camelcase\n```\n\n## Usage\n\n```js\nimport camelCase from 'camelcase';\n\ncamelCase('foo-bar');\n//=> 'fooBar'\n\ncamelCase('foo_bar');\n//=> 'fooBar'\n\ncamelCase('Foo-Bar');\n//=> 'fooBar'\n\ncamelCase('розовый_пушистый_единорог');\n//=> 'розовыйПушистыйЕдинорог'\n\ncamelCase('Foo-Bar', {pascalCase: true});\n//=> 'FooBar'\n\ncamelCase('--foo.bar', {pascalCase: false});\n//=> 'fooBar'\n\ncamelCase('Foo-BAR', {preserveConsecutiveUppercase: true});\n//=> 'fooBAR'\n\ncamelCase('fooBAR', {pascalCase: true, preserveConsecutiveUppercase: true});\n//=> 'FooBAR'\n\ncamelCase('foo bar');\n//=> 'fooBar'\n\nconsole.log(process.argv[3]);\n//=> '--foo-bar'\ncamelCase(process.argv[3]);\n//=> 'fooBar'\n\ncamelCase(['foo', 'bar']);\n//=> 'fooBar'\n\ncamelCase(['__foo__', '--bar'], {pascalCase: true});\n//=> 'FooBar'\n\ncamelCase(['foo', 'BAR'], {pascalCase: true, preserveConsecutiveUppercase: true})\n//=> 'FooBAR'\n\ncamelCase('lorem-ipsum', {locale: 'en-US'});\n//=> 'loremIpsum'\n```\n\n## API\n\n### camelCase(input, options?)\n\n#### input\n\nType: `string | string[]`\n\nThe string to convert to camel case.\n\n#### options\n\nType: `object`\n\n##### pascalCase\n\nType: `boolean`\\\nDefault: `false`\n\nUppercase the first character: `foo-bar` → `FooBar`\n\n##### preserveConsecutiveUppercase\n\nType: `boolean`\\\nDefault: `false`\n\nPreserve consecutive uppercase characters: `foo-BAR` → `FooBAR`.\n\n##### locale\n\nType: `false | string | string[]`\\\nDefault: The host environment’s current locale.\n\nThe locale parameter indicates the locale to be used to convert to upper/lower case according to any locale-specific case mappings. If multiple locales are given in an array, the best available locale is used.\n\n```js\nimport camelCase from 'camelcase';\n\ncamelCase('lorem-ipsum', {locale: 'en-US'});\n//=> 'loremIpsum'\n\ncamelCase('lorem-ipsum', {locale: 'tr-TR'});\n//=> 'loremİpsum'\n\ncamelCase('lorem-ipsum', {locale: ['en-US', 'en-GB']});\n//=> 'loremIpsum'\n\ncamelCase('lorem-ipsum', {locale: ['tr', 'TR', 'tr-TR']});\n//=> 'loremİpsum'\n```\n\nSetting `locale: false` ignores the platform locale and uses the [Unicode Default Case Conversion](https://unicode-org.github.io/icu/userguide/transforms/casemappings.html#simple-single-character-case-mapping) algorithm:\n\n```js\nimport camelCase from 'camelcase';\n\n// On a platform with 'tr-TR'\n\ncamelCase('lorem-ipsum');\n//=> 'loremİpsum'\n\ncamelCase('lorem-ipsum', {locale: false});\n//=> 'loremIpsum'\n```\n\n## Related\n\n- [decamelize](https://github.com/sindresorhus/decamelize) - The inverse of this module\n- [titleize](https://github.com/sindresorhus/titleize) - Capitalize every word in string\n- [humanize-string](https://github.com/sindresorhus/humanize-string) - Convert a camelized/dasherized/underscored string into a humanized one\n- [camelcase-keys](https://github.com/sindresorhus/camelcase-keys) - Convert object keys to camel case\n", "versions": {"1.0.0": {"name": "camelcase", "version": "1.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "f04fb9e9f67a015b74d3686a8f9fd39f98a0a501", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@1.0.0", "_shasum": "922e65c1ca86276972a94de002b4e06e215324a0", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "922e65c1ca86276972a94de002b4e06e215324a0", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.0.0.tgz", "integrity": "sha512-fpWAbaR/oqERzTdXNqmh12wTeBxl3vFe8caG+6QbpWwLzo6t7f4UHi2/qoKrROi/GDzc2bumYgMbZKbeYAYSiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB2UAFC/MMcv7rK37qXHraqGZDPHTMEkyYq26o0wFRi5AiEAjNdax3NPYlmFRi4xZQmTGd7jL21EmfDy+5FI/mFRsOY="}]}, "directories": {}}, "1.0.1": {"name": "camelcase", "version": "1.0.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "4cdcb6a37bc99bffbc5645f8b1be88f9f512f486", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@1.0.1", "_shasum": "f4f09e56e00a7749a4579f7741a61a2180797220", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f4f09e56e00a7749a4579f7741a61a2180797220", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.0.1.tgz", "integrity": "sha512-dBi8+nyjQjeS2iC2u0xOrUUv0riwpNpCnjySvvbFjpoxi8JdAxWSlF+8nmreIdfVfhMlrPNQvRMPoatZZWKVmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMwwPRbyR3yAIMuODbS2sNRJAN0mR9PD+lEVaBJhdeiAIhAKHKm4XRrSt3zHHCMITS0fz6pXWkuW5LzYm0VzUWeqt0"}]}, "directories": {}}, "1.0.2": {"name": "camelcase", "version": "1.0.2", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "9d492d45989e5c47a1b3a314e2c132837d3ef295", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@1.0.2", "_shasum": "7912eac1d496836782c976c2d73e874dc54f2eaf", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7912eac1d496836782c976c2d73e874dc54f2eaf", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.0.2.tgz", "integrity": "sha512-5jmcHpJIKH03K+TT918DJAfRWMclqFUCP+H8MGyRzVKu0S3qoiD9o1wOjat1ac5TdZQVBeXvGNxIcxZH86KvZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1tvv9kUzNrTbk9SepuBf+vRoeiba1vM8nWJlHs+w9vgIgTn+QzDrVqtpE2H9PcFH6LlJvy+sAs5s3MdfQF6yUAtM="}]}, "directories": {}}, "1.1.0": {"name": "camelcase", "version": "1.1.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "552dafacf307f3dd97586b0f96b1d608a2d48b7d", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@1.1.0", "_shasum": "953b25c3bc98671ee59a44cb9d542672da7331b9", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "953b25c3bc98671ee59a44cb9d542672da7331b9", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.1.0.tgz", "integrity": "sha512-6Pk70dNCOqZqdKzleSY290parUC4Fn8kuO/GEaAZnk45X693jwW5gONOPULFqkvTL6wux4afSP1aeALN2m9iBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHU/XomroxaaYxO0LifBfTiTDmvUoYK2L22sSBPET9KXAiACpQdzNBNeijiXX3jaPpdfNBIe07VgAu4ECooNcqZAmg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "camelcase", "version": "1.2.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "ca0d7611a290b9f4bfe9720fe36fa1700da13541", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@1.2.0", "_shasum": "44c851ab95bb936513844be4abd4529337838530", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "44c851ab95bb936513844be4abd4529337838530", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.0.tgz", "integrity": "sha512-JUtTqMqthGwzQEGOIfkJ3S06CpC62AbGii/ZM/Y6RczZ9c+3tKbn/R2NlXEwWU3bEHTXDmTMlRp3o8F60E2GCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC31f46MTZ8f1BDn1lyx3aNfodZYEVkujtMWWLyGHIsTwIhAPeQSHZ/5i9+2WtjFj+HtkE5iyWBM/69O4MWFCvjtdgU"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "camelcase", "version": "1.2.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "185ba12da723be9c1ee986cc2956bdc4c517a141", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@1.2.1", "_shasum": "9bb5304d2e0b56698b2c758b08a3eaa9daa58a39", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9bb5304d2e0b56698b2c758b08a3eaa9daa58a39", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICA/82NB8hUaHBXNl3dMGkyo17jorcMvL1o6nknBql4LAiEApOIEU8GFN8tQPQVKvMI32Xt47dhFqNjM8ivyse3fZCI="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "camelcase", "version": "2.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "2911efeb93b84c10406391c1536edfe73c658588", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@2.0.0", "_shasum": "ce69bde576ae24cb4bec697eec93c81809ecbefa", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ce69bde576ae24cb4bec697eec93c81809ecbefa", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-2.0.0.tgz", "integrity": "sha512-OQAg19G31J5TwLhsEfcWhJCUTAU/U3Kyn79Kb63edi92Ew7d2CDCInqQvBk8B2URwjLHfx3q50TK8QIHj1SYHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCY/sq0sn41vyB+GP0ERaBQcl2ntOPTOiueD5ottA5PMAIhANw9e+xVzSRitAiT2O+UA78Z+ftFS8f07vsS72FVtz29"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "camelcase", "version": "2.0.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "fb178b39412e3b63ef86bf6933089282d74d85c4", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@2.0.1", "_shasum": "57568d687b8da56c4c1d17b4c74a3cee26d73aeb", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "57568d687b8da56c4c1d17b4c74a3cee26d73aeb", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-2.0.1.tgz", "integrity": "sha512-ko1zZK+aZg7Orkv4yPBpFX80JTT8E4DR2XP4CXgk3m+Uy2KLy/vrMzQUtSY2DCXr3j+zhDEOZbyf/FQnjyHTcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZux7/AUY6Plm9xmzvV1MsJWIh7yPU3CYX+SO2vZ0HrwIhALmGIBBlPUx24dM9uPVLdLsKjtSqtiNKcqZFb+F3GmOc"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "camelcase", "version": "2.1.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "9b73ccb3f48ab86eccb136c155f0eb2e67f40dc3", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@2.1.0", "_shasum": "533ad4cd7f8a1080ded31aba6c79b4bf437ff30c", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "4.2.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "533ad4cd7f8a1080ded31aba6c79b4bf437ff30c", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.0.tgz", "integrity": "sha512-B9NrxP4E6fldGR2F81O4XctfEZOOZNXL5dXmXiOQiWUkmBM1nHCem7BWQtetqEynu2137PlKjvEmjv46wJx6Ug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmLeRWIFTMOUGWLfEZwwUXYRIUsqYtwLotEXpQC6QgRAIhAPmkpC6Wrfch+Ai6jVvpm6U9gm8TS1otXaY5QOYXa5gP"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.1.1": {"name": "camelcase", "version": "2.1.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/camelcase"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "35c9c8abce5b9cc9defe534ab25823dc6383180f", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase", "_id": "camelcase@2.1.1", "_shasum": "7c1d16d679a1bbe59ca02cacecfb011e201f5a1f", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7c1d16d679a1bbe59ca02cacecfb011e201f5a1f", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz", "integrity": "sha512-DLIsRzJVBQu72meAKPkWQOLcujdXT32hwdfnkI1frSiSRMK1MofjKHf+MEx0SB6fjEFXL8fBDv1dKymBlOp4Qw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIChVUk69qXQ110/KimTVtm9jPr35LkIK3YGu3gZIaO9CAiEA26djF5phdIOb+6X1GnSRQvnYUaLoWqKl1yf27WRw0oo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/camelcase-2.1.1.tgz_1457803836074_0.4515206723008305"}, "directories": {}}, "3.0.0": {"name": "camelcase", "version": "3.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "d4de0e37b625e38a880efc6517194917a5beda01", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@3.0.0", "_shasum": "32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz", "integrity": "sha512-4nhGqUkc4BqbBBB4Q6zLuD7lzzrHYrjKGeYaEji/3tFR5VdJu9v+LilhGIVe8wxEJPPOeWo7eg8dwY13TZ1BNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUECihlQbKhHsgSDfuJ6Ve9P4ArY82BEd6ZyVJHEZ53AIgQB9k9ieqIPxkFkz6s8oZTUAza2bNzZ1WM/7gjxr5ECA="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/camelcase-3.0.0.tgz_1462383205197_0.03801905922591686"}, "directories": {}}, "4.0.0": {"name": "camelcase", "version": "4.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "b1f77b8185bc99f32ba6fcf7a9ffd87a802f1152", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@4.0.0", "_shasum": "8b0f90d44be5e281b903b9887349b92595ef07f2", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8b0f90d44be5e281b903b9887349b92595ef07f2", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-4.0.0.tgz", "integrity": "sha512-xi6I2qnvM3JgpP3rfhe0htvuqk2EG8coOfadGaNBiTVemKQCphPKNEvuq3TlMBmIIoSdQw/VZdeisnVaqaPB0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyPr4FFxQi3a/ckk40Ne7IKIuJKj3en0hxvzbyJS1H0wIhAP7l5Si42uxBNUMIwoFPE4kQ9Ytj1hdd1+PjvoqgWv3S"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/camelcase-4.0.0.tgz_1478620489451_0.9916922175325453"}, "directories": {}}, "4.1.0": {"name": "camelcase", "version": "4.1.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "0e6e4a2752aa013b8e9477145c7b8132c95a82ef", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@4.1.0", "_shasum": "d545635be1e33c542649c69173e5de6acfae34dd", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d545635be1e33c542649c69173e5de6acfae34dd", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-4.1.0.tgz", "integrity": "sha512-FxAv7HpHrXbh3aPo4o2qxHay2lkLY3x5Mw3KeE4KQE8ysVfziWeRZDwcjauvwBSGEC/nXUPzZy8zeh4HokqOnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETEa+wptnVyAB8yMPWHboi6ZLAkmqWMS26aRaf7gQe5AiBApVswv1QsT+NEpIYUMV9u8EOBZQGdZ8VYMzLdQE1kwg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/camelcase-4.1.0.tgz_1490865362489_0.433825216954574"}, "directories": {}}, "5.0.0": {"name": "camelcase", "version": "5.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "a526ef0399f9a1310eaacafa0ae4a69da4a2f1ad", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@5.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-faqwZqnWxbxn+F1d399ygeamQNy3lPp/H9H6rNrqYh4FSVCtcY+3cub1MxA8o9mDd55mM8Aghuu/kuyYA6VTsA==", "shasum": "03295527d58bd3cd4aa75363f35b2e8d97be2f42", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-5.0.0.tgz", "fileCount": 4, "unpackedSize": 5089, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA10dWQJ/Lelzqu9pRRsP6ehAI1G9DUOn3KVEpMgJYunAiBZNhT8Es8eMCmj3wJqthjdzWcO5+ExFx/FGBsYH/2/5A=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_5.0.0_1522235314616_0.509666479685914"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "camelcase", "version": "5.1.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.2.1", "xo": "^0.24.0"}, "gitHead": "134172c3ecd5a7d098cb905fcc503ad82614c5ae", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@5.1.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WP9f9OBL/TAbwOFBJL79FoS9UKUmnp82RWnhlwTgrAJeMq7lytHhe0Jzc6/P7Zq0+2oviXJuPlvkZalWUug9gg==", "shasum": "29e83b9cfaf7ad478f401a187ae089cf83c257ea", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-5.1.0.tgz", "fileCount": 4, "unpackedSize": 5948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfLXbCRA9TVsSAnZWagAAkQAP/irBPeRWGxnADjwJL0zL\n+AAANPU4El4ND0mjeZqy0T/2hdfAGxUU1H1sLTou5GWo0ybf977gIoQaNelh\n3NgxSQY1JD0rI3RxMShfF/gfOccwhbdAuGe0kjl/m+TeOz18j4Z0ZYd6Rmqa\nNRz2Lxj+Oc0Prt9LFHN9SiPlj6w6tcquh2E1Dyxzn83KrCWHX5lsZaru3vly\nNfnPVI5GheT9ACTqTbOjj4WUvEGGb66uMIYYq2YqFEbcq1A9/82WUf4T6xYD\nySOPn+PDf88BzUmG6pdKRWqiMZcsmNI+oZvoemAtpIoa0qNmcFcTJWyrVY+b\nr3xAX33kQsKatkN3cR66eJdOlu0alHYYN4hiQOtdPhpDnXMyFq/I6fJ1DQ02\nuLl7EGYOMTNjOWcqb87Xqo1hlaoJH9q9Mk5HAOxsXGyW29Ew7WBE2I8lUEV3\n4Wo7Ktrz/l1qmG4juQ3CTdDjI/QykM1eau1JCBW857LXAL1pXHiU4fYZ8lG8\nxtQRkpX1dhcVc7c7uZkaFeKr+NkLPQ8+uLsTu8XvUKafcc0glPU7QqyNfIN/\nyBkGqQ4J9Tl6rCXefSijGsFdaOBgMhI7EQQWDM7ANc00Arbm9qjMjlKjfK6P\nIKGBVV+wcxbONy/ciUFMOIDbI7gUa6UM6F78YLYjtCJIffCCnTYbVH1rmszJ\n0gYy\r\n=ErOn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwSe5nYYD8DkZLiNcodEy0KKIZvy4KpTY562RLfqcyWQIhAIYQ/B28YYWmbR9hPBkyjR/aNUqlTKkS10uEyN9w8qmi"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_5.1.0_1551676890480_0.9018404055431712"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "camelcase", "version": "5.2.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.2.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "3eae33df5e286fcd550c52da971c675b73294cb3", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@5.2.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IXFsBS2pC+X0j0N/GE7Dm7j3bsEBp+oTpb7F50dwEVX7rf3IgwO9XatnegTsDtniKCUtEJH4fSU6Asw7uoVLfQ==", "shasum": "e7522abda5ed94cc0489e1b8466610e88404cf45", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-5.2.0.tgz", "fileCount": 5, "unpackedSize": 6497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfgoXCRA9TVsSAnZWagAAg+4P/0jZzN99uGyHnMq9wryI\nLWEdeWcsoTnFxTBUQ7ibRwpYzRxJC7rkaIlL8Y3omJ+0uLs4XkF7nxHbFiEs\nJOzLLDfenlVIdd6g7meck9Zmj37Z/RacwlElrSq37VB4TP8a0AUzB8aGsABV\n74BxjTOmq1RfoiPf1pW11ZikgOftc2IfT7I6Q2f+I5Qd07Y/uy6DqcbH+1Dy\nRlIiJZQjr+4iwDcD3hxqVPAi6Ie4Jq3u1MxwLT5fc3RJ7ONckJi41kEM0fel\n6Ev2oMq3QyLBRR/7EnDdytXN5cZhlT7X5vIzjpI0ceI8MsNNAMtkPNm5E+eW\nEkrLhn7cfJmi2voLgixVLNiFM7DJwKIhlZiFDt79sOPbYPd5/7Uukc6yYc9X\nkKNMF8qa0/AerZe1H9LajJsE19VFzG9cp17e/wyn1+tKap+KROIUfIZhUVlH\nVqpatcvEjnSiNqK+/KISJ//Hw12aFZ7yQCGB9srHfzm/Xgg1p1uXawWjHW//\nj76q+0o/08AMnKxS7uoBLq/xcDq2rFWMNF0fnl47aKoA55QjD4IAW9HvMY48\nlQ0uwlHw65UElMzOM/ZXu3LZDENA2nntWXvX7SOl5DCMtzoj737EmQ82Xey+\nVsCxh++n4Mb8TV3k3sVDbCtQdW3OucQx4bWn0vN8cg04MS1pb5CIAhl+kT45\nRHlk\r\n=eypF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvtolOQWzIpxbewxoTcUbzxgfltp2rxUV1vFX2/5GKMAIgQR3HosHPeKtcdA8xS0IO0hzv5NdDgL4T74hiMnfVIlo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_5.2.0_1551763989562_0.7397484159919618"}, "_hasShrinkwrap": false}, "5.3.0": {"name": "camelcase", "version": "5.3.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "75014061a127d6e2c35503059605c4f655fe66db", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@5.3.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Y05ICatFYPAfykDIB7VdwSJ0LUl1yq/BwO2OpyGGLjiRe1fgzTwVypPiWnzkGFOVFHXrCXUNBl86bpjBhZWSJg==", "shasum": "0a110882cbeba41f72f99fcf918f4a0a92a13ebf", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.0.tgz", "fileCount": 5, "unpackedSize": 7466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcocniCRA9TVsSAnZWagAAxccP/jOyplmYsEKSaSjqTYGh\n7ct5y8VLjlhXhWkO/b24qsIA5d3cbTkL3kwBZ3ZnG03WUq3atayhj86FtJ/N\nsQTJd2C2KwA0OdiWZLK5GuZYuWMcVWoEq3Rbl2I1usurlc7oUbDXN3gA1PvJ\nOliOcYqQrgUFGtOjS1vATqM2G0mXiDjDtnDqr/jNOzW/xbOULVBUMLYWRk6X\n3xd1knSqRT5xI0FQoi6aUNF6EU8W8V7AlAGks7S54PBAmzTsTnbd6O3oEsif\nc5KWyLtpu96Ocb4WzNDCNVADTnhtQ1EXquloj9Q3/tNdTmiGYUOCsiPWJh1Q\n9chFROUIBePUJDXBTnO7Vydw7J9VQc8BBgDQ7wJJ8DrEG0ppUUl0EALK4tRv\nGTsW9+80/mNgJjtIMwbq7utDzJbLJjtXP3A2Pvy0wBZK3hqWD7GZTbp1GVOV\n8MnLz84vRpFiSf9Er+jR6I9x54wzTMeIlLgzrnrqupmeFYYoa+Wyrt83ctIc\nm3dWqFwRXUHbFtToDD/nh202oiS1IxQ8O+dX2okWDYGzsokUHBp/oUQTy9LK\n665Hd4r1jCd91qAPyF9nbSLEE61wR44ZBTWoPiXFPck8iyu7U3T6i8SCXcDy\ngOgBMMkl98K4hlePGMckHKh9f5qoRztOSmBK8ZmTVxogdaxQTc9DRUqjM6Zl\niJ0m\r\n=P1Re\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDuhv3xahPE7JApoRKkhJSJju4V5OpFgxEa9C7WkV155AiEA5iWto5dO8nU1BppyzDz6ZooCxdz1KMzacIcw/FbhMT0="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_5.3.0_1554106850085_0.4588034976342792"}, "_hasShrinkwrap": false}, "5.3.1": {"name": "camelcase", "version": "5.3.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "cbe5a519ec6745adbb5283d5ee8c5c9889050d74", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@5.3.1", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "shasum": "e3c9b31569e106811df242f715725a1f4c494320", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "fileCount": 5, "unpackedSize": 7447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpLZpCRA9TVsSAnZWagAALTcP/1baON6MQMxgPLfVLfwR\n8QRj0qzMyFh7kG92DKB1wI7/9aMYWFPLaYb11734w1mL2omq9SqlTP6BKXOE\nZG4Yl1SQtAA3OUzJbFMi3t9m+/G2yBHHDAFIMQy8l0yXFfxbUmRlQ9Ix5AbR\ni/yU+r9PaJz0qrtRb4dtbTavI/Aj3bBA35lF1PQr/SPFsAGJEMBg7wDL2TDk\nyRQur7ssZjrfr313E1v9hbeWkUrp+i1fiZd9dpSic1TsRutbrCjZLxtWsHMc\nfAQPKqrx94gPgn1qqyYDxtCBdEoKIAhHN9hvPTGJ8r0ueR07DyyaVvaxlTfV\nfAjVDOH6cS5D9y1121j3++MCv7DL3I4XgfYtGkVZj5a0//UJX0aj4GmxFBU8\n7YppCnQiAl9r8Vhz8fHNPlRnx52X/dWxZHD8bDRXsYg1OxiwZecCJJtayB42\nYzX8QB9Wq/u2/EojyyW+4w4CrcVC8wgOMlQP7gaueNHLV/FefEPP+CTshyk/\nwxFS6vkd/tARb5gdQLKxxkvpcvJVyDl7PDXfsYO86rUA2JNf9wCIeAKmC0nX\ngGcEn8Ew7oe5rM6UaTZGBiOJER9Kcf5xZ24/01J82vGWnuqX8tC/C/iWrBKe\nfyXXqmZ38xENnHfb/W2HqTxCuEzg28Imms55Rmi14FmRr8s7ekPdo0PuSwPW\nGg2u\r\n=OQtl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDuztwb7jkQu1SGrBiLhWlKTz4LQmOPO1cvIa0jaYKlHAiEA/HsA5Up0bHLv4nO7dBYAx04p4PXcfzIvW7jFvYNmDzs="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_5.3.1_1554298472491_0.4347901486248773"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "camelcase", "version": "6.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "gitHead": "5a0d0919eb2228578e1670f42675d1acb5e5c317", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@6.0.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8KMDF1Vz2gzOq54ONPJS65IvTUaB1cHJ2DMM7MbPmLZljDH1qpzzLsWdiN9pHh6qvkRVDTi/07+eNGch/oLU4w==", "shasum": "5259f7c30e35e278f1bdc2a4d91230b37cad981e", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-6.0.0.tgz", "fileCount": 5, "unpackedSize": 7415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei/IUCRA9TVsSAnZWagAASEMQAKA4Tx3t3ZP1i8OfRYn3\nfzHKf7hELGzAHO9DjHzVSq6FVk4fq2NRF8GKXR9DFbAdaE4wfjfgZ4y/cnQz\nCGbMIT7TaplnZ2kuARxLnyH0/7Y5pmBzKrYsJ2yg+RphWHUj/zDuskKbvnm0\nUSVHCvXkViSAqeui1V/v1MgFkrseQoWAqzj5I8kDpgr9Rx3hgCIpGjUdj1OB\n9HkvNax7VVmjGl8ufSGe+cBiHnaGtfDHYh+cnkLMhgoFlY59zvu33y9wQTHO\nqNb6HN7tGwxhEot/2zT450deXsNTpt8eG3kg38fqgM4m5U3ExS9V+JKRr/5y\nRF+UT7JI4hNdoGk6XI7cZW9u1AnipN0VJPrcD0+HdHrqhWJwXfVym51I0DiL\njcR3q5GilfgvKVAC+YmCtTTGc8VbCI/1IOmmgqwfmmSfc8fn85JJMRY0fMMk\n8qyDJRZLPfruC+Et7VGGf0NMvQMeQdp9w+YACdGh+YEvhTNvvypW+9qAC3Ze\nk2Yujb36WypXq/HiXeOCvIQ6ZqtXD6qzu29Cv5uCe2xk/Q9rOONTy/ZW8qTW\nkkm+Ai90TqbWBq33fsR2qLS4DivAfK+8a+o0yRI26P7bF6T0UvW7y8rlReNI\noLh1rxV610f+PVSXG17pkPXWo6eEX1DuU1fo8Z70l0VNRoS6xbPC/drqRgbz\n+kOJ\r\n=RKdy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC33kpEuqhQFzesBNKu9mG7oAGxPpQWPB5kgy0S5U3meAIgBByxe0bWWZN3cwzdhfa1/d1zjAYecmzGcNXzp9t3qYc="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_6.0.0_1586229780284_0.34572182948635266"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "camelcase", "version": "6.1.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "gitHead": "a077c7bcf5a37630f3733fd7dcd377991377ac32", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@6.1.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WCMml9ivU60+8rEJgELlFp1gxFcEGxwYleE3bziHEDeqsqAWGHdimB7beBFGjLzVNgPGyDsfgXLQEYMpmIFnVQ==", "shasum": "27dc176173725fb0adf8a48b647f4d7871944d78", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-6.1.0.tgz", "fileCount": 5, "unpackedSize": 9234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgemICRA9TVsSAnZWagAAsXAP/ifYJquEDBu39tTtVUO3\nvRqaxrDY0NGAqP8TZGPoGJD30dDenPh93MzPonvziOweD6rrQd/iIKSLCnLh\nLgpEtiLIn07Vn/17Rs3bVDQ0nQILusNNk/2GoFy3TfGB+cSsZqaiVKPsG5X7\nnBxU4pftZIhGHE9uuEjm9zVgFUJ3K5VWIAiNeANZWxFwLZ4WyqsJvFw56Koy\nbw3JR1Tc8jO0qRLwyH4UD9em9u1bYXc3p3+6Ift/Shk00KQ8yatSufXCvYrq\nI5+sBLEQOMquLYdOTpO1UO6hcV8HpOk1vD1tjr+16VN/TCrHAAxWuD4dkQ3Z\nkIN5lyJj2eAY9gwqhxwDygdevYIPQaDJf6xIr71jpx5ylJz4ijD1ec2lOA5H\njFCsNanofD7JZj4FVZt8IE0Sx6VGW2J9bFEpDqSxsr4jsyhqF9j0ExnJ7m9U\nCBm/gQSjPglm57j9t65oI4CP5rd6BcaR7CH5zl9E5KG+YXE8WRJ5sMsf3Zek\nLorSWcYDqjBynCcwAnYqJsnqNolkH9nAWcf5VQTLZfCqFnGYT9KY343BWkGc\nRiO8zPJRW/2p4ljJ4cPHVq0H5pJnD+b0CJC3hfOoQlVtUHR1TzY1+Di5uvz8\nmjVcIl0JK176h3Ct/6i3iCxsWSIzwCK0UBwUOBECSfNiBhOfWvQy/kHsaBGI\njD+K\r\n=qVFq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBMFXhWKRCcS5kaK9qpUwZR+owh6XobERjhu0VeHLTWqAiEA+nQkKg5sb131JQ649VMhXKNqnAZ/6VhCI3PG+u/NmkU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_6.1.0_1602349448254_0.9683442945546847"}, "_hasShrinkwrap": false}, "6.2.0": {"name": "camelcase", "version": "6.2.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "gitHead": "1321775c740c20bd93d0bdbd844703464e0e6de4", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@6.2.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c7wVvbw3f37nuobQNtgsgG9POC9qMbNuMQmTCqZv23b6MIz0fcYpBiOlv9gEN/hdLdnZTDQhg6e9Dq5M1vKvfg==", "shasum": "924af881c9d525ac9d87f40d964e5cea982a1809", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-6.2.0.tgz", "fileCount": 5, "unpackedSize": 10385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfme6GCRA9TVsSAnZWagAAG38P/1ERwSZBcvSye2Pg+iwq\nK/cPpP3CgGwupCLBFX5ATzJJm0ai4gI2T127MZK8rVPSw266HKZleXKU+SNQ\nDE5cVkwtSGfQXqXAPitbEbRdquSSShQijGcTAShXQ67LNiOU4n6yKWsHpXCU\nECHgbOHIa6WrGhw8GuRECIaARPY/sEVmsk6B7LJo5SExwfViAJD0wk0uf3l9\nwY/IVPSBDMHZASpwRf9y+C2Y4w+NX72gOAua7wpKcXrI8hfsHS5vDTzV+Xb2\nwlODU/RoFqlNwoVgHcNN09LUV3fZg7dJCb0B7lzcQ03EZyaDl0E6/wLDc/q9\natrqcwaN78aOkivcbAKme9xGE6d+XVdE1f5sklOzo4LRr16Nsd3xsOXNRB6h\nPhYkN6oFt87UFIk5eFeJWsEaCx+9iE1r0zI9F1J26JneZ/amT8vOERLisI7M\nDD+KVEx/neqpmQYvaBpRe7BbrCEsaU1yCMET2iDEX1h2vkAn0LmYLO7g42SA\nhFedWysqH5fHWPadQUorCm0iOkhdtVMSHX7QAFi8Hlna1zVSse7aEn+TCWRM\nZc6H31yJfD4rFTZ3BWNsjTFiCo1KOv3TwalSFyJwdUeWKVBRrmMCVVKf4+zp\nZEnGUKPgdCaFI/Z0ck1/QOJB2YUPAYHAZttzdSlnG1IWMEy5R8014hCr0mnx\nNfet\r\n=36QW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFf25wRdhBTACDJHMlBUM+MPRRsj6OkQviuSP6Pb20PvAiEAi2eAvYS4UUfK6izsaNs1X8+mS2OMuu0UQznFLXFkjhc="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_6.2.0_1603923590373_0.6280326369817386"}, "_hasShrinkwrap": false}, "6.2.1": {"name": "camelcase", "version": "6.2.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "types": "./index.d.ts", "gitHead": "f28c4631fe879bd430e21a3c1b380e158c53184a", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@6.2.1", "_nodeVersion": "12.22.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-tVI4q5jjFV5CavAU8DXfza/TJcZutVKo/5Foskmsqcm0MsL91moHvwiGNnqaa2o6PF/7yT5ikDRcVcl8Rj6LCA==", "shasum": "250fd350cfd555d0d2160b1d51510eaf8326e86e", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-6.2.1.tgz", "fileCount": 5, "unpackedSize": 10892, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDKBbpz1A/dKnTeZrGWYYwXVugvVfEEUS4WYD8i1UE6uAiEAnECDhCzj/NNrTDl2UPk7rh16COKj/nvmOhCOQs7JidU="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_6.2.1_1636944490324_0.49364627338998623"}, "_hasShrinkwrap": false}, "6.3.0": {"name": "camelcase", "version": "6.3.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "types": "./index.d.ts", "gitHead": "497d7fc3ae98b2232af1e56aa24f82878d7f53f0", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@6.3.0", "_nodeVersion": "12.22.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==", "shasum": "5685b95eb209ac9c0c177467778c9c84df58ba9a", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "fileCount": 5, "unpackedSize": 11697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0LmuCRA9TVsSAnZWagAAP4AP/1gUxyZEJLRQeUFqXoAl\nEWAsu3QGU6KkzCmfcbAtMhJe4YTcofQE6ORuZURsEFLvT+gfY7yemgBNMfMU\ntjoVWvz3Q0eM5qoquB3WLD6WncKPCDQJSZybKcxFH7KgO8fbL/aWnVdpX2jI\npuD0SpXryCZFQo9KHPydhEgPqW28WsDxK40qxfDcYee7ibCOUTkYybk1xg3F\nsl1G10dp6knF/PBejgWDp3nh8jNED4aRI3EBdGEltoLKVG7XHUL978tRhW/4\n2zPExJ2BCST0E+57Ez7Rmas9pFazZXIZM0lCOkTYoFEVoGB5lx/a4J6YbrNp\njg07zcSTz2W3AHCy5/E1EzYRsPsaeDEg/rrOnP36xPnIlDh8Vq6Y+eqBPG+U\naW8E531bKCJrDL4tPIuGh6zx21lK5MS8WCWOL80Frvb64pu00AM3pE88rVSS\nWzY9SExOm85EEctMptR1+HkjF1fECx7jxcGKRBHJ9DF3I5nrhmxYYmRb4rym\nkIkaV8ukB/E8imf90FzID7GdxC7349JOway4SQ1yj2RVZROYskkbGsLKSCB6\n384B/sjGkA+17U/MSNl8AJBTOPaKdfbe69q3+8vsXoYu6DjYXzyk5OyVmgwq\ncQ/iMmCersknIFYhcPUm/IHN16pwFz5hybJg28aJLMJeei8CMldgPM4GiTsM\nIo/4\r\n=dAx5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6upqnQaGJh3JKO9Mvp1LiigRYrUZK4ad1Es2msau47AiAlnoIeAVTN9EOPEH2GHN4epICDI9fLib2cQwOHGeXpkg=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_6.3.0_1641068974256_0.6782005025667823"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "camelcase", "version": "7.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^4.3.0", "tsd": "^0.20.0", "xo": "^0.49.0"}, "gitHead": "6f5439a78b523b157b55546b26bdc5e14cd2b923", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@7.0.0", "_nodeVersion": "14.19.2", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-JToIvOmz6nhGsUhAYScbo2d6Py5wojjNfoxoc2mEVLUdJ70gJK2gnd+ABY1Tc3sVMyK7QDPtN0T/XdlCQWITyQ==", "shasum": "fd112621b212126741f998d614cbc2a8623fd174", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-7.0.0.tgz", "fileCount": 5, "unpackedSize": 11478, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHT7aJC5XqgkmJjw8JRZ2iq6L9htWM/oG0KqNdqMXHVsAiEAp/sK++7BIiiiQOVvfXZw0rRUQVrixXGZu7JnND6+EBA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinYvBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfSg/8DakHOicB1Vdv1Wn2ELIqH9ES2mB0EVvuQv5N6/15KbTHpFLe\r\n7kv/pp3VQlkv9N6fCQqFh7qo+UHuKXcZiWzsJvNsad9gY2Vwvd+qxh/4hI9t\r\n5UUop3uLGZyzQcFT65CRXwNWpj+ZYPnpdMyEvERVlxQlngx2XYTmQyr/sX5S\r\nXQNNVs07/wcYmTHRkgR7myT748cqVrmNd4LKYD/x2HDAZ55hTGcKW2V1MBdX\r\nNXShpEiVi27n6MwcZSt1bE1ahhSXLibu6MOlK6PXL8i00zAt59d5ICgtK40p\r\ne0rpTLAgg9tw9EZP2TIxNARnBFZSLgc/XsH/pGJQ2DQ3S2tCb01UxgcWQSIT\r\nmhd3IW5FTd3JDmoso2P/VgFoWwgZgNufe0+fELm/MsaFfXu8gAJYwJF8kRH0\r\nqQdK5LC9BsleCIINMBLdr5Okonbupf3X4rIbuOi6HkwMm06u4vRwRGpiuVv/\r\nt9kzh0B/lSUBUMsEnRGCyoLTZ+KvRbhfH7YyfpLKTZ6YBOv1UZzUv6gkXRC2\r\nPvtiu94HXlKgz3XHO2g0MxL8rvbADDvwQqU/iC0N28NB7zu6ybyHniDxOesw\r\nEgiVOaGSHo3DxzH3hFP6gSh1I5CvVbIThPev3ter18syBXFbExGyGQX/OIJ3\r\nQwcnzmvm/ehvKaz8LpMDimrujSqd+ECcZdc=\r\n=Kkhg\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_7.0.0_1654492096987_0.5837998318613613"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "camelcase", "version": "7.0.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^4.3.0", "tsd": "^0.20.0", "xo": "^0.49.0"}, "gitHead": "ec442933b287719947aec1dfc04dca2ccec446e6", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@7.0.1", "_nodeVersion": "14.21.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-xlx1yCK2Oc1APsPXDL2LdlNP6+uu8OCDdhOBSVT279M/S+y75O30C2VuD8T2ogdePBBl7PfPF4504tnLgX3zfw==", "shasum": "f02e50af9fd7782bc8b88a3558c32fd3a388f048", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-7.0.1.tgz", "fileCount": 5, "unpackedSize": 11720, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9D/EJwzMz8bIJiW+TDwnHGBQTrZ4+yoqHD3r00QmHzQIhAKpEvLrajCzGRrXt8qkNc/rNHs8oi8mWdMRxXwk1xUWQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmHQrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmZA/9FJ/tqvWkR5UeEWwVNAbbRK+rjGwBff2u5eu+83DKAiifPLBm\r\nWlxX7NTYfCA9ZUSb1RABujI8aS6TC6yF0PsiQmUNphHfArayhktItPRMRokI\r\nHz6xZHyz1uVX0MEuODabw0mXoUb6zOCsRNEyzZwNZCMJF1DSqT4csMSxmRzu\r\neOs5eVONxuK56Xliq72r1ct7xJIAQIV3irJA7/YI95MYGfxXSC18XqAaKCrD\r\ngWLsFYsBsKv1qlhlqfZtUHmdi/Ln9bSvSujvTv9RfdDKPKn0lCi0Q4/DQAfT\r\nrbDwZeBwwcv1sTIfo+zhqHzVJ4H6nbzrl/HUt2mEWTtuvG3lIgf0J+Y89AHI\r\nhKL6YP/2PxCtpdHaTiUoC7ByusWCa7YOGcrjMRsLEAIye2Zp0M9OfVTcPcBm\r\n1UdLK1HtD5wodY75jNPuxXHr05bcvUMga0HcMEfSHeqJ98hGjG3toelo0eB7\r\nY2qn0q1dt2JoO7Bmdrdv6n5c+kj6qE0pwb7wbLJopm7GwiRCOq2qXMbKLK9/\r\nwySi9coyV27mC+69jiz2mLZSNqChq6Vw/u1YDt6RR1+jJHNzKlUtCOuROx0N\r\nHGdRp6Nb8g8mGMlgLZDrr+NUOeqU7fahQ7g665Mxdcwj8VXhNzqEccoZiGXM\r\ncIz9UbdpNUPl60pO/AysvzPf03eBKHJXmEI=\r\n=QEFR\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_7.0.1_1670935594985_0.03181013154573176"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "camelcase", "version": "8.0.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^5.3.1", "tsd": "^0.28.1", "xo": "^0.55.1"}, "gitHead": "01954f09378d5d97722138a00f3ab9a20f46c8bf", "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "_id": "camelcase@8.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-8WB3Jcas3swSvjIeA2yvCJ+Miyz5l1ZmB6HFb9R1317dt9LCQoswg/BGrmAmkWVEszSrrg4RwmO46qIm2OEnSA==", "shasum": "c0d36d418753fb6ad9c5e0437579745c1c14a534", "tarball": "https://registry.npmjs.org/camelcase/-/camelcase-8.0.0.tgz", "fileCount": 5, "unpackedSize": 11163, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDWnkCrPNX1P8Xy0Ka8atS6hcYxypatwuQ48v6FaUqIZAiEA0MkbTvD5zTQse/F+f9ZOjylbXBGnuJLQxwlKMnvznlg="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/camelcase_8.0.0_1691599075398_0.026191746185258324"}, "_hasShrinkwrap": false}}, "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"andrebassi": true, "akiva": true, "gochomugo": true, "bret": true, "galenandrew": true, "xiechao06": true, "vwal": true, "docksteaderluke": true, "vzg03566": true, "ridermansb": true, "monolithed": true, "joaquin.briceno": true, "esenor": true, "jetthiago": true, "alanerzhao": true, "mehmetkose": true, "vonmauser": true, "quafoo": true, "domjtalbot": true, "ansonhorse": true, "terrychan": true, "xhou": true, "zhenguo.zhao": true, "vunb": true, "rocket0191": true, "justjavac": true, "shuoshubao": true, "dwqs": true, "flumpus-dev": true}}