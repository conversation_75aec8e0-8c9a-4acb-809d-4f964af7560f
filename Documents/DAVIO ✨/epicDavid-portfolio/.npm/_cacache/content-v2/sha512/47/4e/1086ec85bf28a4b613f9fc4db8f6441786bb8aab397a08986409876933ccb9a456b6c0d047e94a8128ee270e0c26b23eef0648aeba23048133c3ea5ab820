{"_id": "@types/redux-mock-store", "_rev": "556-31018132b74cec3cd7c5baa099a97a13", "name": "@types/redux-mock-store", "dist-tags": {"ts2.2": "0.0.12", "ts2.0": "0.0.12", "ts2.1": "0.0.12", "ts2.5": "1.0.1", "ts2.4": "1.0.1", "ts2.6": "1.0.1", "ts2.7": "1.0.1", "ts2.3": "1.0.1", "ts2.9": "1.0.2", "ts3.2": "1.0.2", "ts3.3": "1.0.2", "ts3.4": "1.0.2", "ts3.5": "1.0.2", "ts3.1": "1.0.2", "ts3.0": "1.0.2", "ts2.8": "1.0.2", "ts4.4": "1.0.3", "ts3.7": "1.0.3", "ts3.8": "1.0.3", "ts3.9": "1.0.3", "ts3.6": "1.0.3", "ts4.0": "1.0.3", "ts4.1": "1.0.3", "ts4.2": "1.0.3", "ts4.3": "1.0.3", "ts4.7": "1.0.6", "ts4.5": "1.0.6", "ts4.6": "1.0.6", "ts4.8": "1.0.6", "ts4.9": "1.5.0", "latest": "1.5.0", "ts5.5": "1.5.0", "ts5.8": "1.5.0", "ts5.7": "1.5.0", "ts5.6": "1.5.0", "ts5.1": "1.5.0", "ts5.2": "1.5.0", "ts5.3": "1.5.0", "ts5.4": "1.5.0", "ts5.0": "1.5.0", "ts5.9": "1.5.0"}, "versions": {"0.0.1": {"name": "@types/redux-mock-store", "version": "0.0.1", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "50c0275b10e970e81893ef6136cfc6795cc0fcfa", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.1.tgz", "integrity": "sha512-G7QGo4PH2Y3MkbVJpRcLZ4z6G69MgIEUVzBeH9WDExlJWl7RqHsgnJyUG/kZdNOys/18JpnM5oUTf1UJqTJTLQ==", "signatures": [{"sig": "MEUCIDroPyjdBS5gQXsAQfrTB5/ZDrIGKtpjL7KMO7JFjD9kAiEAk2saw3HUPfA5lC2Xu6Ee6DPWOh6Xkyx6DbZ7JvoCiAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "redux-mock-store.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store v0.0.6", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.1.tgz_1470154101087_0.8435650027822703", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.2": {"name": "@types/redux-mock-store", "version": "0.0.2", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8c71bae1c33245696f4f6290cd7962245af580e6", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.2.tgz", "integrity": "sha512-MkERn50TWW8saVPrNyWcsHJNfrUJV1zVnQAtiARWYsU7uLFOVZYW7y7SmkMhvZd42MxrT4OdlnTUGsGqf1isfg==", "signatures": [{"sig": "MEQCIDlkMj8AfUof9ev62C7lVWGbPevA9BqObsYsjJU15RpJAiAfDwl6JRtYPI9NveLpqhGIGSWtX08vbXHoeKIA3WXAPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "redux-mock-store.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store v0.0.6", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.2.tgz_1471621212234_0.4249798965174705", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.3": {"name": "@types/redux-mock-store", "version": "0.0.3", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "19b27da41ba93d18c1deabb588aa5da85f9d959a", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.3.tgz", "integrity": "sha512-IXRQn2yCwViwId+HLPkRG0XuBFpxPz40KQtP7DfcSSMl1fzuA5gvb26v3a4G+YSUmIpWDq2QBJ1FBnTBIuF5Sw==", "signatures": [{"sig": "MEYCIQCvpmA3QhifMNsZ89/mBPUMnPkszr91ZCkxez+lEjVkkQIhANWelJ2F72iZwxzVACMD5HUXUYySruLUD4m0tn3CEhU+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "redux-mock-store.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store v0.0.6", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.3.tgz_1472151506785_0.20055304956622422", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.4": {"name": "@types/redux-mock-store", "version": "0.0.4", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8240a3feec8389ca6f59098da4e52bf539a2dac5", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.4.tgz", "integrity": "sha512-R/eGO9P425gW19jTICV8nDbKjP/dz1jOqt5t/h5plYLoPDoxRSYY9u5uuwo2mFE15Feiy5FYylIXtThu6Dehpw==", "signatures": [{"sig": "MEUCIQCzSt+LxQxv2+KWgeVo1CrOhN9nrI3jS5NZkdg3HeXZnAIgL0OZGQ/J96/kmWosr+4GSKBGCbigAj3RcSqaPhet3fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "redux-mock-store.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store v0.0.6", "directories": {}, "dependencies": {"@types/redux": "*"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.4.tgz_1474308488071_0.8000704073347151", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "555a29363b59050024f468055383956d24dd227bbf48c8e681143a5004ec632d"}, "0.0.5": {"name": "@types/redux-mock-store", "version": "0.0.5", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b2180fe7f30b74a43469d862e8becfe68e0433df", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.5.tgz", "integrity": "sha512-2ftLi0hGGkSMWbE4Q53g/wob8iEk0qQjdX9Cn5QQyKpCajk/uzujf35033gf1Ph9NyRgGDHir9RN+p959duOxg==", "signatures": [{"sig": "MEUCIQDJJSStnLPyZjeufhe6uhjn/zPiZBVjO2EsU3wVWO1OswIgNPsgbrFa/lpmFd77TDsRFg5KvOtubZqWFSVzxW621Rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "redux-mock-store.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store v0.0.6", "directories": {}, "dependencies": {"@types/redux": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.5.tgz_1475701321891_0.34992785612121224", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "2823128b7ecdfc316d350b64cc3bcc12f4cbf7837679a01c272d815e214dd41f"}, "0.0.6": {"name": "@types/redux-mock-store", "version": "0.0.6", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ef345b9aa805650c20bfe4245d6fdf2051817d17", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.6.tgz", "integrity": "sha512-aPI3I/8G5+uSwkJ9BCmSjyvLhOaRciRcEv01qiaZVqnSH7I+9lN6b/X40LfGWJz1YtlGkMNJSyGS1+mWptzTGw==", "signatures": [{"sig": "MEUCIQDA1m9exS2j/6YjrhF2+4t7IlEBH/awLpBs2SoR+pFbnQIgbsnVNyrS3ocOcg4fGKH49QD4cweI1aHbvBjcTJLza5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store v0.0.6", "directories": {}, "dependencies": {"@types/redux": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.6.tgz_1479762339529_0.5407068526837975", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "a7e0a1bc98e214db3b409892305c20abc970da2628f4e10877d66de8b547dbd8"}, "0.0.7": {"name": "@types/redux-mock-store", "version": "0.0.7", "author": "<PERSON> <https://github.com/MarianPalkus>, Cap3 <http://www.cap3.de>", "license": "MIT", "_id": "@types/redux-mock-store@0.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f7b46af371da4b6ef491e57e107d326f0a832201", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.7.tgz", "integrity": "sha512-58cHsCLdfw8PXfjysm3zIDcv6+eq5av23+z+w1AxCV4F32wPxY+hDSY2p9O2BycmG6H/A0QEZCGNWSjDNwC5gg==", "signatures": [{"sig": "MEYCIQD1eU8aOhICpFYGb/N/+F8g8deVjUDS/9rH4VuDOYbeQgIhAJf5IQcOTt0QCw9odv82YnFpTJHuT/YKQGbtvc+eFLVV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.7.tgz_1480442659470_0.7454635023605078", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "922c95f38dd7a3729f798a01d909221e4694b40314d514e3ef014a5cbf3f3381"}, "0.0.8": {"name": "@types/redux-mock-store", "version": "0.0.8", "license": "MIT", "_id": "@types/redux-mock-store@0.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "b3978b068e316a2640e20f0d01a8a2c80ecda1cf", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.8.tgz", "integrity": "sha512-kzXdp2xKcepb86pWN4Zx8WYCsp7kfoLEJjq8WptekeIvhyoe2Na/e7P1gkKt39MOHUgPxmL2y35/W2FIau7CYA==", "signatures": [{"sig": "MEQCIBxg04qvamH/F4UqEUqL7ZCo0doGoE8uAvRdD5TQEEUHAiB34G05bEyvcefVlLObPgWGArUPxC84K1psUfU3QtkatA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0", "redux-mock-store": "^1.2.3"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.8.tgz_1497305826786_0.16650499659590423", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8666182aec030d17f27981a7bdcc512f55dcca100486d5988cf6b62b8a855116"}, "0.0.9": {"name": "@types/redux-mock-store", "version": "0.0.9", "license": "MIT", "_id": "@types/redux-mock-store@0.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "0081c362a249776e7016c79c8949923c08c5d14d", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.9.tgz", "integrity": "sha512-Azxv7Yx8qN8K/YkYjB9OUJZXZ3vkoSEk7VqvgKl9h2FuGwzyakGdRUQxZfOdTbC1cUB9vJRWZcFQrhR3kIEITA==", "signatures": [{"sig": "MEUCIQCTsCK+lcLh3s+JyUuesFP9Nk2X20vu4HVemQqRscvQLAIgVbsyCRF5+eg6hg250O1QyA6tjCWBsR83+idgLQXDrNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0", "redux-mock-store": "^1.2.3"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.9.tgz_1497991716401_0.7140708488877863", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "219d46a8e97635092e6e74ce7384335f1600cddbe57ad61cede8de3d5505bde3"}, "0.0.10": {"name": "@types/redux-mock-store", "version": "0.0.10", "license": "MIT", "_id": "@types/redux-mock-store@0.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "53b89a6d83c9970e492f14e1aeffdedf13633bd8", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.10.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>cLaarMgazFOXsw6PQEPcBFYnbpqjI2TZBVhb7iJQxNZk95+TvefG2Dv8dASqkzidGeWsYX/2iWLEacxmmw==", "signatures": [{"sig": "MEYCIQCwyLmOgDMp+Yb2ZsZSrlvn+UwsVirnVpoM15F+puEx3QIhAPGtuwDrXkEoC4iwsAC19JAIfXo9pQs8gksbJgJXNPum", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.10.tgz_1502458951300_0.6438727730419487", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0f72dc389c68d7969af47800be2ed9ca45de7db99bfbeb7b12b2f29f85d8c7ff"}, "0.0.11": {"name": "@types/redux-mock-store", "version": "0.0.11", "license": "MIT", "_id": "@types/redux-mock-store@0.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "0febdaa60de13248fde6313729537e1d5157cdd8", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.11.tgz", "integrity": "sha512-iwKJtfn1bKr6PEpeGCwjdOw8w2nF8XniZG+pX2zdsOb6MtnPvrlI7xsFZ4oZrIx+BZ/xwkSq7CTDoY04gd5zaw==", "signatures": [{"sig": "MEUCIFTKOjycDOhR7zBuLdB96FBvX8lglE6QhlWVjSsSJdXZAiEAw7uRYv0cttPsvFKq/uZlk7IAXFl8feKAFu1F7fR9vts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.11.tgz_1504302726583_0.09955762093886733", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4804fc2ef16a2052d0801ba28aee61bae033f5373bfa657bb8fa0ce06a6cf525"}, "0.0.12": {"name": "@types/redux-mock-store", "version": "0.0.12", "license": "MIT", "_id": "@types/redux-mock-store@0.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "0c1b298e62aa5da273bcd347c73ebe629e9a1523", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.12.tgz", "integrity": "sha512-DEIyiTllcmGfP3VjBnL+8sx3Tv63MITodkhL/vNig5fKtCI/vWkWurLKgrs/hUnj6wiUUPdUlALuIv8LUZN1Xg==", "signatures": [{"sig": "MEYCIQDdIBqHv492aslPJWUZVVlIqR6zkU0IrZP8OL+Kbj/HvgIhALbT9XrwzcjI/AHjyUk5yUs6E0cePYFO+iSIrYQ01vEm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.12.tgz_1508956947400_0.04565574135631323", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "19d361482c44df2d7702fed68025300400023641ce077ce308aabe87ebecd6ac"}, "0.0.13": {"name": "@types/redux-mock-store", "version": "0.0.13", "license": "MIT", "_id": "@types/redux-mock-store@0.0.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "f7ec160214b854f2d976ca12525997a21512b2ea", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.13.tgz", "integrity": "sha512-OMU6xD2byzaURUx6rnkWusEhGfTYvNkwoNHcJkwfwVn1WWUtRSB+RReGhHLeXchg/8DcyLPikD7OP1vygMz1FA==", "signatures": [{"sig": "MEQCIEc7dSnyOF5GNRCS3TfrxLSPQFlyGSk0coZV+iKixI4BAiBU3bD5m2eKQ0SYLy1xGI5fjSLKoWkIWeJXqCnMOWV04A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-0.0.13.tgz_1514505542387_0.5061091862153262", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "960957b7638a4759cd69cb186af194f7c558d49d69c9677b1f13904420620170"}, "0.0.14": {"name": "@types/redux-mock-store", "version": "0.0.14", "license": "MIT", "_id": "@types/redux-mock-store@0.0.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "c46f2bdf51c87db972424d350ec72ab333ab13b8", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.14.tgz", "fileCount": 4, "integrity": "sha512-q3rLLXRVoJvQZR+85bQWKmDrQc8t/pfoE3eKSV3yTPqY++ye4EWB8mloN/86qc2iwspwm3gQ4vg5Bli/2/R+uA==", "signatures": [{"sig": "MEUCIQDSgJQxTZc74cOwOLeNXI1JJ3q+b81R/MYB1YBghcTDrgIgJ3YCj3HvNkNngVguohXbZWlE7wi43ROnuXJsODb/qpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLD7YCRA9TVsSAnZWagAAGkwP/1veiLtyM/IdnmK0m5ua\nc/906vW9M5n43ULEo2aa8OkBgMOaPZ1ER+vTgdykMGwKBvDXBaIwlNRsYFAI\nQwjFjfMEwj5Wzy8Wa4F0hUHLOdPBgjO4Sc5XUNcvehwQ8LMnnuMV2+xcC1oj\nEHdZeXZD65h9ZFoPh4Yc9BD3Kzc4IXVh8USt5KEYbAPs2hGw27xuqpC2G8Pu\nIECo9k3VbmVP1XiFb7izrmy9UxgVTH8vhBq6Ykbin3pBR0mNKnI+hpCeaY3Z\n0QeCn7SiQwJPQSjVuq2p+VMGtUeD46nJr633XKcGSKGfABcp2O2N3p1ZmkhG\n/3ZaG5TgjJGZZdKGqwD6p3/+71LV+Kz7sOcbzA35qr07lmT83Yk3gHD+We3r\nRFVuBfaEkjMYIOybZbJ+yv2YQhAsJ8ix/3SNE04zAC3e6rGl/xWKjdUe6QvT\n91k5eP77+AiJ4VyDxPvUPe5DBIWtpl/pRtuTFWfEMSBZc72sQruQuSCA3dKg\nmSGIsLSrY/7mS84dmSPfLj6P/DL9Ipz1EIRXHI054YSivgl3dqw7GNBOMfwo\nEf0SCaRMo6DrcuZhWf8BYcSScRaiJSbQeRCcRl8kA8hB2oZsLN4cMEKbTpBv\n4nb+qV1qMCuq0I/i9/cktdAiJJ+saJDMzBFFxBBZGncIsAtPJNR4HOPeL7AW\nA+0S\r\n=DSTq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_0.0.14_1529626328892_0.3601949826532189", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c8d0b90c12e1ca00449c1d014bd1063322c6c5ea0968bf5d0f4040a0734d98ec"}, "1.0.0": {"name": "@types/redux-mock-store", "version": "1.0.0", "license": "MIT", "_id": "@types/redux-mock-store@1.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "e06bad2b4ca004bdd371f432c3e48a92c1857ed9", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-7+H3+O8VX4Mx2HNdDLP1MSNoWp+FXfq3HDGc08kY5vxyuml7OAudO4CAQFsKsDvbU5spApJMZ6buEi/c3hKjtQ==", "signatures": [{"sig": "MEUCID5qKMOg1vfRS3jMtybAK31Lfj99AvRtF5SSVSMHuPqxAiEAuJ72wNTFgaGwB1zYDEsmCx7nKmh8xSlePh2wcjV+lOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLD8NCRA9TVsSAnZWagAAhG8P/jeXedtYcAlJ5vmrgteb\nh1wLOt1iP9ugv7ia9xrd7TvcDXxgfdEyFsQkwvpuVl0BAjEoSIcu9ILBQdh8\n4C2WgAUiCIiiqhq0Yl5LbYgKxfcIjYTCV7/z4gqsWMhFOq6RYeqC88QK+ZLD\nFQD7sG/Fv1XPWflBEaMa1rAFhpKi5YsBD77fEWHyCeDJKiYCEp/QONzELPa5\nvB8jl0JB+31fuep4utLItLB7xOA7cZ1mJYkFf/V+c/vQq8+S02gAn3Xk3Fl0\nq8nMnen4ApyZD/f8D44LzXQYnnpalYpvXSo78WwWOTa8H2GO18ZbNgwcm8sk\n7t2CZnZPy8KpQoTzdO3KZGSvb57JQju1lM81xZaU5An3KhQOTNJawE0GuZBf\nePXeRr0GdHmSCbYPBRw1gNRqL77QSp91Mtnory5cgvNqLcmSsDX15qDVeZpH\nCoaDUTK+E2rpiYH1nXt+VdXDeUQmejeZEcJArOWDrwQLya2Bl9ZLUIvlhI9K\n/eQQiwbVX5GCldTpzgsJ1IaiN4wvVcwj2n6dCM4dR1IEKwIcQ51CT5/SOOTb\nETqHRuGpGnNNM+TAaCCazc+nEIP9eJpUyNclyIy02zKuBhlNWcVRvgFWRQj9\nS3EdpY4kH5wDw3Iwyp2G7dC5ICx3AS/w3c7GTXSd8K1aZ8giHiPdDxZON7fg\nqHjN\r\n=hlAi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^4.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.0_1529626381646_0.7199253012434181", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e9928d08b972ae4bf1dc0ed550fc55660c3a769d8faf373985ea9cb276c0f947"}, "1.0.1": {"name": "@types/redux-mock-store", "version": "1.0.1", "license": "MIT", "_id": "@types/redux-mock-store@1.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "90ca701d640aef7c007f564a9a4f8dc03180b0f7", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.1.tgz", "fileCount": 4, "integrity": "sha512-1egEnh2/+sRRKImnCo5EMVm0Uxu4fBHeLHk/inhSp/VpE93It8lk3gYeNfehUgXd6OzqP5LLA9kzO9x7o3WfwA==", "signatures": [{"sig": "MEUCIQCqt+mep1xkyyuWNk3BD8Y3yUJj/2pv2EiqHwYsLdo0FQIgbavPT/fCFRm6M+Ip2XtEOiAgDCNTrjX6OXcvTrk69L8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyIr4CRA9TVsSAnZWagAAQwEP/RCZkpQR5ManlGhy/On8\n0bXdBv6LyMqYKeT/yoOlEnxiEIU19BQ2ehKWqDjQAS+9BbiK8GVz/nV6oG/A\ncpGn/aMWXNxslYR5sL/l553fjsE2kGrx4MYEa7TNYKKZQqH2nfgUqlaCHL2h\n6cFIwRI4+msWaH4tGb1WV/PIEF1bqfDsptGkb+G4Mz74a6lsTFGIpEVTQGLF\nQX7kGZNGL3vwTx+w2Zolxv3WoUevyHcP4J6vqtVOKDUN+NRf+JFJeXzH42GI\n07XTUYqYqp4/5VXb3AmJHKDS7vmKcXRwJzLGLKXPq2canco8y7otZdposdOD\nbpEXDNmC1Cl4ARAACAU0rHtDq56sS/i65WIJziHWZsChGyN7vQ86Nov83fG+\nUq96xxKMS1XQMspCa//2CBjDhMTOoxXzzhA97GKbZG2DcmbCHH0Xt/6nb6WL\nCAJZxQeTQXttxEkViRvPpH/ERcBGqoRhwlfG6omozkAk77FFQXja+Xj+tV+4\nKUq5f97+9i1BzhW26VeKqj5uvhW4MUO+gl9HE1l0MPRBbyYesgrAPj0xu9Qw\naeYkN7M+UKtfw568PHrPNj5FP7wjXmuZ6iZYutvAEH3NzzGvf95NHlWaEyMU\n4eEpumkuj+9IRLRWlDO5v6YNNRd4WVOLcuMpqwdDWM3gXVm5Ft4SWWrCwFS+\nsNZF\r\n=A97g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^4.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.1_1556646647458_0.6480583555338646", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "55b42286a1a9170ae471ef0e558c1cfbdf72d93fc6ca7251a1842d25937bb7ab"}, "1.0.2": {"name": "@types/redux-mock-store", "version": "1.0.2", "license": "MIT", "_id": "@types/redux-mock-store@1.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "dist": {"shasum": "c27d5deadfb29d8514bdb0fc2cadae6feea1922d", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.2.tgz", "fileCount": 4, "integrity": "sha512-6LBtAQBN34i7SI5X+Qs4zpTEZO1tTDZ6sZ9fzFjYwTl3nLQXaBtwYdoV44CzNnyKu438xJ1lSIYyw0YMvunESw==", "signatures": [{"sig": "MEUCIQCEi/FKT9GIg7lKlbOxEi4rJ1oT5dfny1AzxDefz82pEgIgP4B6qvr77aivOzRnUpnoDMTEtzsX4CR1P+prt49lwz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL2VwCRA9TVsSAnZWagAAT8oP/iCwN8lA1/zFP09pIlld\nbIsX8qclhOnfi7EFvf9eTyIsDITKrpCG7iXW4xIjSSerzaD5QICI3tc5DT70\nfPHVt5ACW/swryIVRQpEapBy2YdfY+EGThX5qm3R5vpKeZDJCaCML9JhnxZM\narL77KoOsuCxvGndJpiZwtWWvjVqZId9zuTAoUqlUc8l77TsHPSyAzAwKhKy\n/5WqJpyfTgjwU9t9ZLPS3yXmxbYklNFtXGl4q0RbDrURMEh7FxFD9QKl6hWZ\n0Xwp59ALGqsAukLuQufxBHzvClfrp+g7eFOs7HZy35tPmpTxCmm+F626Zf92\n5dDvALQttUb27xuo5stIOVFMpgpGCQUDOkirHCcrBmKImfWF5RTUHr7YfFwr\nLA7huwMJYBz9D30JwKxpPKhOfopbwdBSdT6nS3NMnhP8Ovw+n8e6cKFCv6jO\neK8wNaOcpTiIOrspOD+iRt6C/rwrATEQ5zDzpCrJKxBhx8DgW9SUDs1W6nqx\nbrbJJvIG1z/SU88muf8oGQ3dhM9hByf4PD2iOVc7X7GjFb2Hld/mn/SUBFcK\n9GovynggaA1WM5syG3dGRUWQtVkpuX6Oum9S2PrH6dEldATShOp1/tDY2dsS\n60deWkWa7sq3lSmVJGxyMIwKRWTDF8DLTNVbcbiwqdXTA0SssGvI+dk+VUWu\nit3C\r\n=qsSR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^4.0.5"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.2_1580164463702_0.3397111405051201", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f9cc50c4801679792d4896f05fa6ce7d18a73a1972f2869f954c1351e5e89d70"}, "1.0.3": {"name": "@types/redux-mock-store", "version": "1.0.3", "license": "MIT", "_id": "@types/redux-mock-store@1.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "895de4a364bc4836661570aec82f2eef5989d1fb", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.3.tgz", "fileCount": 4, "integrity": "sha512-Wqe3tJa6x9MxMN4DJnMfZoBRBRak1XTPklqj4qkVm5VBpZnC8PSADf4kLuFQ9NAdHaowfWoEeUMz7NWc2GMtnA==", "signatures": [{"sig": "MEUCICZmtPC96HgfCrM+61u+39r60Je94xK//nXbBd79/SSgAiEAlQWljFSI4aD4ckshIxZe5rCtZYMV8BpDZKc2zbgQaiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg57nfCRA9TVsSAnZWagAAIewP/jtTUPRxiw3UsfWIvDIf\nqWy6cMiucP3b1h4KpPHgYadcm5yKex9wxczXGcnO0kM3jlL+LIviud5fLgmg\n639YfpmIHXBFaPgwOrlyu575SsQvbrW0vqellFK6isWg0YQwubDR0LvN+A4m\nu3+812H9Kb8LZSoh7C8hxsDGkAPTXTTzGa+pb+WWE5SMD48+ijmWufITmCI0\nM8oQyVInWOSGvtEPcDSaYmpxaKz5qB23AlnNaFh5iY7C6PrQt7WOD8jTXn1w\naAiVmbAC7x41lHKLPvREAROMfQGoi+dXHSddakIRYP4TSBLGwNAFZhMZviXW\nfthUXoebE1yXmLmuBsYeRBowPzVOqU7vq+arP9c542eOHwU3rP7rhnq1PA3D\n8UYekSGFCJ9ziPX6tLnnkvSVTMvDv5s+GtKMeLbNVTC2bk04BtcMMO3OPMWp\n7YG3Alwc94VJ77asOTBmUYwypF6KVUoMyp2ktJyA3oX9//5xd/NcBJfi0uuY\nOLovo0cXScF3UC96M7v6PTu9XpksG9hO1M+eBVEGpY6/Myl0RK8ScC4PcCpM\nsdZd4ZOdZUPvCLstvHwPvKrOLlZCf4pJANhJ/a77v9ZIFZ4mc1ZpxnTIsRlT\nLvsL0OPZZ6z8CPuRDFrKQMABXlLWEPEcG6yHF/KYR/vLfaeEPJCfgPdXKEI7\n4yrJ\r\n=fnYo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^4.0.5"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.3_1625799134917_0.7226992054952874", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "07e4809731e419d1fd1fbad3c55efde09ab30735e7810c0003d285368832d4f4"}, "0.0.15": {"name": "@types/redux-mock-store", "version": "0.0.15", "license": "MIT", "_id": "@types/redux-mock-store@0.0.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "94298dfd8395648e8c1f18705be2a5d4967ff3df", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.15.tgz", "fileCount": 4, "integrity": "sha512-w7bLLE8Luu/MX7BNaoWODeI3YRIjYTr9J3cnzmiNfiwjfay2PWoGoBpUifYA8Y5CXdoacKAx8WyRtw5Xaiku1g==", "signatures": [{"sig": "MEYCIQDbol0L+WPyphDjQBB/aiRCvC5SECQa3xWeCPw/m6CndwIhANzggxY5OcDRaiy3Pyr0SCaIxONILrpOeeof1QWjOB7t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg57nsCRA9TVsSAnZWagAAEcUP/RYejoOg3gRj2NU9KPlj\ntuW82h2Q/RH11KdOBvo6zhXTnJMYZCOaROP5gpJzNhRr+BIDujaL2HHLKrEr\nXeLgje9ShyiwbyZM2CFdORK/Rxjs9R3T5GNvPX7TpuKw4FoWStmoTqHngTS1\nB7wV9bUlGRivFGzyN2e+XjpCxOFVvWi64Zx66ADOOBSG+nOIL6stX77GUn2y\nm4inaciKF30wwQAbBRJbaacD1oRZaTOjTbUVi9CAY7cXl/pyFVqLPnTivlne\nCa9ttZxpDUDcj5xd7FB6eBF2hPk4qO23BQSh1fe9RRCy/IjQB9nmq2E6Gt7b\nLrnGYnLenLJJVhkJ2cfIXjiwwUx6m8Y01Q28q+tKFxzq55wl7KPdA2u6YgUp\nerFqpj++lGpE2p/6zHPu5oOzulCx5ZM4n4h5SPVF8Xpr67ACeNzk0QQejiK+\nxCh895A7hy/tn3OY/ocmHOmtEPlg1XnwYsTzQanBATzy1vXbimdGmb8atxCe\nF64GMePyw8fGVvZbF33FYFCvseZe6XCrZvKKAILnT7VKgYkYsBiAjLFN7r4m\nSOQgwQmqHRwBLDAkfbx6joQ/CDMvOYLqKMmX/1OohbjyP6iQ19JTFAUOaIdl\njqE8Iba0jlSr5AHdlZhxYD+ALgeN/5UGaA14TcrukzeUTaf+iYFZFgJqsDgH\nzEac\r\n=+Shn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_0.0.15_1625799148035_0.27303050897762393", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e7cde50a82e960a9a6b7b98e25a62aa9fb932cea6fb93128ebd1cab42669684f"}, "1.0.4": {"name": "@types/redux-mock-store", "version": "1.0.4", "license": "MIT", "_id": "@types/redux-mock-store@1.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "d82d97d61314f02a3849a18edabd1a169581e632", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.4.tgz", "fileCount": 5, "integrity": "sha512-53nDnXba4M7aOJsRod8HKENDC9M2ccm19yZcXImoP15oDLuBru+Q+WKWOCQwKYOC1S/6AJx58mFp8kd4s8q1rQ==", "signatures": [{"sig": "MEYCIQCFG8F94Q1GM1VcfTkgkWs5rg7PG5GlEEgdHGcy1nuNjgIhALhKz3GDzEXa/DhnArMpJJo8FhTWOls0weO4ZaQvG6mh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5474}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^4.0.5"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.4_1695805757886_0.7621746798109037", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9e2ef6e3b1ed24c045b87894affdd053f53fc9466153f0a01a936d8290334413"}, "0.0.16": {"name": "@types/redux-mock-store", "version": "0.0.16", "license": "MIT", "_id": "@types/redux-mock-store@0.0.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "0903a2b7fad94cd0ebdee4d4078c3a5587913f31", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.16.tgz", "fileCount": 5, "integrity": "sha512-3N9Sq1F0im+3esW7+R4HFMgG5eMIMme+R6BkzwhqwMv0niPtdVlTm9tnkrPRMtmwmO3qK8YlgzGKS44tLVz4Xw==", "signatures": [{"sig": "MEYCIQCVop+x9eYLf6Z/DXo0c4ZdyY6KEK3+w9OLlGJX48OzFQIhALupcmbHVP1Ijz4onnFqn9UeVqYFqlKD8lwe8USGR9KQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4113}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for Redux Mock Store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_0.0.16_1695805764089_0.9136731977934471", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "80b03743c08373ed59642e6784415e3fc22f6b3f679e949fabc677bb5324df1a"}, "1.0.5": {"name": "@types/redux-mock-store", "version": "1.0.5", "license": "MIT", "_id": "@types/redux-mock-store@1.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "1579f50af25c4820ad4ca49883e1ed80aad18501", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.5.tgz", "fileCount": 5, "integrity": "sha512-RU3IuL6ZVx/Ove0qxPz0Lcaiog2YIVIO2iyEg6xN0EqDGYn60cVqVKjo8mvoIsgjH1SepJoYpDZ2fGzFoY4YlQ==", "signatures": [{"sig": "MEYCIQDBJyoamPlGPUKR46TQZgSFqS73tAhJXvESB4J7uL+LwgIhAPSnzUeCUEJFiPTEAdu3RDMieRBK1D7RReFzXQQuCQvh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "directories": {}, "dependencies": {"redux": "^4.0.5"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.5_1697636216873_0.816734554215049", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ae321b03b18267b47f8c2053c544c31b9e3ab8c087d69b75cd1da835d8d7679b"}, "0.0.17": {"name": "@types/redux-mock-store", "version": "0.0.17", "license": "MIT", "_id": "@types/redux-mock-store@0.0.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "81f026b215543aa9d04fbab788e5a787ac168d5e", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.17.tgz", "fileCount": 5, "integrity": "sha512-AoFDwgIDzjSRCpDyC5zq5OiEE4dpY0VWtb7qPpl+vo/zsm7p31w0GfSJ3KE5hMUTlKojh8BQIBaClwnX/o+5mA==", "signatures": [{"sig": "MEQCIHdFCu/2jemdALaCjrq7FvXLt+hdKAJYcPrhIIwmHDAkAiA8wbE5FeRWhwsCDlNo2Ouza08Eu4decGQKskMpmgwVpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3481}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_0.0.17_1697636225332_0.5255926970382112", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0d381d938cae02e375243929a0e96806c2f1086b715a053674f03a9b7d9b1012"}, "1.0.6": {"name": "@types/redux-mock-store", "version": "1.0.6", "license": "MIT", "_id": "@types/redux-mock-store@1.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "0a03b2655028b7cf62670d41ac1de5ca1b1f5958", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.0.6.tgz", "fileCount": 5, "integrity": "sha512-eg5RDfhJTXuoJjOMyXiJbaDb1B8tfTaJixscmu+jOusj6adGC0Krntz09Tf4gJgXeCqCrM5bBMd+B7ez0izcAQ==", "signatures": [{"sig": "MEQCIBaMDBBOaA5zIq0uuyuYgBWMeJjPeT/j6m1sBuuLSx7tAiBpCy+a9bw//IOK4qrjvNgOb/sVh1WPP/Rs00cR5rDLtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "directories": {}, "dependencies": {"redux": "^4.0.5"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.0.6_1699389653959_0.467667199362779", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c3748f3cf1e666bcc324ba6e2983f8da51488c04dfbe76a4d82ef7c2c86f19f0"}, "0.0.18": {"name": "@types/redux-mock-store", "version": "0.0.18", "license": "MIT", "_id": "@types/redux-mock-store@0.0.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "510c4b74ca18f92f5298e91dea159cb20adba009", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.18.tgz", "fileCount": 5, "integrity": "sha512-retu0Xe/4hTr/tOVCFsogU8XgUMDlZb9M9OtcpukyRtMaH3HNPpG6jm+P9LhoDn33TSZJiMfVUuip9L7hTQDaQ==", "signatures": [{"sig": "MEQCIHk+cbZj1/0WSadEalDsjzkRUxsk/jGHfK7I11s2Y+WRAiAv09uVLcRikPVBT1KWFEfLQYRbPlGcgKmvzXckwPcl3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3481}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_0.0.18_1699389661022_0.48917350198220944", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "83d5c1a23f6cc996ebfc963eccaccd756efdde828cde8407a66abedb3f965332"}, "0.0.19": {"name": "@types/redux-mock-store", "version": "0.0.19", "license": "MIT", "_id": "@types/redux-mock-store@0.0.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "7392c598a4735d858c81d6f9f59a5047433c36ea", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-0.0.19.tgz", "fileCount": 5, "integrity": "sha512-iMJoJDJ2xhDTSAr5YXm+qI7q9DBL5cQ6+sm2v7OqP0tbvDzvSIEh3NDjGyRA6pz1PIcpIKu3uZbPqdtusLOFJg==", "signatures": [{"sig": "MEYCIQDsWoRMFV5SZkVpdjFI585dh4QSylV1l9Acw2BZqUsylAIhAM/ouTLIWULOaqZGASHbm8NtxNzlpuVyxFpKHXeE2+Ix", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3575}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "directories": {}, "dependencies": {"redux": "^3.6.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_0.0.19_1730606615587_0.18253089105315468", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "79e405475764634129a346d2f70a70a88f9e42ae8179efb01f5230637e403e5b"}, "1.5.0": {"name": "@types/redux-mock-store", "version": "1.5.0", "license": "MIT", "_id": "@types/redux-mock-store@1.5.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "dist": {"shasum": "53e9caa9f94c6839e5f34b1c9bcea09b911b67e0", "tarball": "https://registry.npmjs.org/@types/redux-mock-store/-/redux-mock-store-1.5.0.tgz", "fileCount": 5, "integrity": "sha512-jcscBazm6j05Hs6xYCca6psTUBbFT2wqMxT7wZEHAYFxHB/I8jYk7d5msrHUlDiSL02HdTqTmkK2oIV8i3C8DA==", "signatures": [{"sig": "MEYCIQCImbxIASrLvWm7dhUhOJNkJrZU0lc2wnVBqMxcH8VJnAIhAMIzsmLPbq0Vyc1ptyQ/l9SX9mYD+fDPOxku0NiZVRRZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7432}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "directories": {}, "dependencies": {"redux": "^4.0.5"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "4.9", "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.5.0_1731931388668_0.04900597348212776", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7a025bf15251fd963352b662b110eebf67e2a92ce85f00135ad146b1fae18337"}}, "time": {"created": "2016-08-02T16:08:22.067Z", "modified": "2025-02-23T08:09:16.109Z", "0.0.1": "2016-08-02T16:08:22.067Z", "0.0.2": "2016-08-19T15:40:13.413Z", "0.0.3": "2016-08-25T18:58:28.446Z", "0.0.4": "2016-09-19T18:08:08.322Z", "0.0.5": "2016-10-05T21:02:05.120Z", "0.0.6": "2016-11-21T21:05:39.749Z", "0.0.7": "2016-11-29T18:04:19.690Z", "0.0.8": "2017-06-12T22:17:06.860Z", "0.0.9": "2017-06-20T20:48:36.471Z", "0.0.10": "2017-08-11T13:42:32.058Z", "0.0.11": "2017-09-01T21:52:06.665Z", "0.0.12": "2017-10-25T18:42:27.457Z", "0.0.13": "2017-12-28T23:59:02.450Z", "0.0.14": "2018-06-22T00:12:08.968Z", "1.0.0": "2018-06-22T00:13:01.765Z", "1.0.1": "2019-04-30T17:50:47.571Z", "1.0.2": "2020-01-27T22:34:23.864Z", "1.0.3": "2021-07-09T02:52:15.072Z", "0.0.15": "2021-07-09T02:52:28.203Z", "1.0.4": "2023-09-27T09:09:18.101Z", "0.0.16": "2023-09-27T09:09:24.284Z", "1.0.5": "2023-10-18T13:36:57.081Z", "0.0.17": "2023-10-18T13:37:05.530Z", "1.0.6": "2023-11-07T20:40:54.176Z", "0.0.18": "2023-11-07T20:41:01.377Z", "0.0.19": "2024-11-03T04:03:35.795Z", "1.5.0": "2024-11-18T12:03:08.846Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-mock-store", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/redux-mock-store"}, "description": "TypeScript definitions for redux-mock-store", "contributors": [{"url": "https://github.com/MarianPalkus", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://www.cap3.de", "name": "Cap3"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}