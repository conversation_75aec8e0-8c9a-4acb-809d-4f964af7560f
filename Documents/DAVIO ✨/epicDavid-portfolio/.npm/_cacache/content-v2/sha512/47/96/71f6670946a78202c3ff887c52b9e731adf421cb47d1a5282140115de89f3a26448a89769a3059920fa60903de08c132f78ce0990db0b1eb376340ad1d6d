{"_id": "minimatch", "_rev": "287-79c51e30e0d534225180a17c817f92c2", "name": "minimatch", "dist-tags": {"v3-legacy": "3.1.2", "v3.0-legacy": "3.0.8", "legacy-v5": "5.1.6", "legacy-v4": "4.2.3", "legacy-v7": "7.4.6", "latest": "10.0.3"}, "versions": {"0.0.1": {"name": "minimatch", "version": "0.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.0.1", "dist": {"shasum": "33b549784ce98eceb7a86329c11a1cd02cd00ce9", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.0.1.tgz", "integrity": "sha512-O58AgKiuTUy9T9n8hHfzyd3G5edzELFW4FshSlo+CdhW8h32M99YSntYrSahkmGi2iRijW3fpjXQNZH5xI++1Q==", "signatures": [{"sig": "MEQCIB2FfKAEfxLj4yUj6YXfvAV3eviEWFY6FFaPqqdCSqBmAiBZ5tSdnDDeuG7nB+UmG3PR0+FZ+5VAUjIXCU28VflewQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.0.15", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.5.2-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/minimatch/0.0.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"lru-cache": "~1.0.2"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.5"}, "_engineSupported": true}, "0.0.2": {"name": "minimatch", "version": "0.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.0.2", "dist": {"shasum": "582b28fed87d3bbe9f9afc8c9490f4eb3b08ba91", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.0.2.tgz", "integrity": "sha512-4JQR+18teufKm0qPXZLcppB1aHHYZfMroj42h3dvPVrEH2v/GVYTSgFdbSHyX9jcSk7g9R4zRRkRFTzxxYLHqQ==", "signatures": [{"sig": "MEYCIQC/Zz08HG5MV1s+RJNvFMqS/HiqMj66dygMFBZa0aNNhQIhANwMPqiKTPzYN/oxsj6cji2Fa1TFd3tyQIuBh00ZPyLs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.0.16", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.5.2-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/minimatch/0.0.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"lru-cache": "~1.0.2"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.5"}, "_engineSupported": true, "bundleDependencies": ["lru-cache"]}, "0.0.4": {"name": "minimatch", "version": "0.0.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "791b9e5e6572b789cfda6f60e095614cbb7504b6", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.0.4.tgz", "integrity": "sha512-vkUtv3vFj5nLzMHi2kksBn9f8Glfpl/kCe6DNmK5PuLGKOgeGyRL39HSQmYxBu2HOp6luKAPDVHVZf6Ey8fSSQ==", "signatures": [{"sig": "MEYCIQDqFwvn/fN2h9A+In45gIdbSrILj4c9mOqwYHSkk7U2awIhALHOUf2j8XiMRb7DGJH3xQnMWJK/W1HL6SUkv9jSmEtM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/minimatch/0.0.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"lru-cache": "~1.0.2"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.5"}, "_engineSupported": true, "bundleDependencies": ["lru-cache"]}, "0.0.5": {"name": "minimatch", "version": "0.0.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "96bb490bbd3ba6836bbfac111adf75301b1584de", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.0.5.tgz", "integrity": "sha512-+uV1GoFd1Qme/Evj0R3kXX2sZvLFPPKv3FPBE+Q33Xx+ME1G4i3V1x9q68j6nHfZWsl74fdCfX4SIxjbuKtKXA==", "signatures": [{"sig": "MEUCID6VcGPzlCjk1mWPnyH/wYpvRWIO4OWUXbCUJZuG1NLWAiEArhqlScMY5SvEf5zqeRVWAnAHxEQ6LK18DesHk2ZtQ7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.0-beta-0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.6.6-pre", "dependencies": {"lru-cache": "~1.0.2"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.5"}, "_engineSupported": true}, "0.1.1": {"name": "minimatch", "version": "0.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "2bbeb75b5819a6a112ef5cf444efa32e006bb20d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.1.1.tgz", "integrity": "sha512-3P1PPnTQol15+tR2bCl4nLaB0RmTLI0+EoC3poub868Yf0JaMStkdm+Jsq2hPzga78XeqEHyC4VmpfDiXFOLDw==", "signatures": [{"sig": "MEYCIQCLSrJ26E7FKLKFPVxFPhsIZBZiu/3asp+VYd7TjHoNagIhAI/sPIXzDTiCAmrdk3T2dobUun4YWSLweAg1UeoGFiXs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.0-beta-7", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.1.3"}, "_engineSupported": true}, "0.1.2": {"name": "minimatch", "version": "0.1.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "81ba8cfe095f0acd7d1f8afa93819099ef2177e9", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.1.2.tgz", "integrity": "sha512-iHrfWyTxNiTYxOyxO5V//w7V1m2xfsLQehQje7R3hCf2VKIGIak8WC+hwe9ib/qnWY+3HMktO7pWjBQ/sSYq8A==", "signatures": [{"sig": "MEYCIQD9Eq7oVejaSfoNayffxkiLpaat/squtJztnNUcfNt2cgIhALK4V8o4qc2z6X10UOAJ80JsjgpZdErNE+X3U4hm/V69", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.0-beta-7", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.1.3"}, "_engineSupported": true}, "0.1.3": {"name": "minimatch", "version": "0.1.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "3811e5808181ee2d923614faf14e0b24543daf06", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.1.3.tgz", "integrity": "sha512-4OSyt6WcgHyEKlqfdqrgYhTpDt1cDL1b/BPKeSPhinAW4zT+Ganc+5wwnDIpcL3LnkXLd3ScDGcSgza8+oVstQ==", "signatures": [{"sig": "MEQCIFsiTy+TrPzEfhnPBdPV0NASx6zMnUWn4LYPpFcpfiXxAiBLltTgmUPGUbEiDG2Zzj2IxaZ/rbQ9t32Hzlq2B7ouEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.0-beta-7", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.1.3"}, "_engineSupported": true}, "0.1.4": {"name": "minimatch", "version": "0.1.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "5c5b370eebd3f729adc1f7740515ac3c70769ae4", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.1.4.tgz", "integrity": "sha512-X76ZdMWYNglH32KwUuoCtcL3+ne9M+FVjuGI6RMwGf1l5wFF13KGb+cdWyp/WzcHnOT4dFzyQ8VBzX5zqvnAww==", "signatures": [{"sig": "MEYCIQC1H47SSlgoItEnSWA0C/8l1+HHvylAbZrJM6/tp7F0GwIhAKO3Ox1YMVR6AA5mcb9A2rMWk7frPp0bKnBIJUSysHKs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.1.3"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.5": {"name": "minimatch", "version": "0.1.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "b762f312066cbbfe50462a68360bfc9ca0ccb1b9", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.1.5.tgz", "integrity": "sha512-KFPxSmc7KgXLngf9Y6mZmg+uCySJ/rqeLkHbZK+ygcPtzQvN9XGBbgFMPO8pfFYEKFgOUAE0RmXFIQtoRc7VHg==", "signatures": [{"sig": "MEYCIQDyxK2OIQkFiINz2HGbghaCRujYdZZz6BBadDG9OE/CRwIhAMEU8wH41fJAoTzKqL17RqSQ7gRCorg8KQNOjOYZQS1d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.6.9-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.1.3"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.0": {"name": "minimatch", "version": "0.2.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "7fb18a99421493c520b508f00699cc8f5db86e2a", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.0.tgz", "integrity": "sha512-b2FfTK0XrP05b+maK+WDBoZrkFPssZFghCUT4wWL3K1mR//P8ShntiMrQz/MPaNZQDZxeK4yq0AaySGugLLPyw==", "signatures": [{"sig": "MEQCIH+/+agcgWowvxzzpAv4SNTFM+lKYMdA7rJhkvxEclhKAiA2WqQZniylb0dZXclj9qQ16gl+ggEVaNpv25/u8SAyLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.7.5-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.1.3"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.2": {"name": "minimatch", "version": "0.2.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "209c214e81dd8d831122c56793c93d90d8e44b7d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.2.tgz", "integrity": "sha512-7MU7MvYmitfyHefoIpDPLpnHqt2mZDXm8AQY7Nfu61PI2j2nN7Gzh2WUGKdXJCg6U20QPyrN2Pt2mLdjlOduPQ==", "signatures": [{"sig": "MEUCID1PRROBwsaG6IubemhteD64uoY1WkRpU5g9S9s3lM2hAiEAy6+k+5gc0NH+d4U2XrWVcbmcLo8p8mz/OrQVljbqNLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.10", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.7.7-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.3": {"name": "minimatch", "version": "0.2.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.2.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "625540b1be01b3e7fb5a04a01c847ae54e8f3a9f", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.3.tgz", "integrity": "sha512-Wq0dfflKxiKUiEUhpAnmcrOajyQtOemEGuYsD8reIaeYxLGOd7TWAWFjN5//WP5PwJc0YfbLJFBrjMf03AzcHg==", "signatures": [{"sig": "MEYCIQCntasu8Eq6B8SLA5W57iSNWGrCCiGVmXTRn/EUjIMKEwIhAL+YIOTmFH39QSrbcCaCniGfqNhBIP0GT58Dqcc7sJ1I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.13", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.7.7-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.4": {"name": "minimatch", "version": "0.2.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.2.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "093b5cd06c40d460d37c50a8ce3fa1c0ef636baf", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.4.tgz", "integrity": "sha512-Y0qPir8GQ447NQ//SF5omZFjFQlv5Zy9C1hOLTLeQCxX3PZ8LQCo/wkTIDEXmvFafcPrOu3dH3eu+W/EmGb98Q==", "signatures": [{"sig": "MEQCIAtyUyZM5JZeqcDKGrKB4NYEWHvwh05lW9YVckxRxSrpAiBnCHGuXtJ3HOK8vZTLNFVhn0ogruvAb9OwiYCBMfnMzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.13", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.7.7-pre", "dependencies": {"lru-cache": "~1.0.5"}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.5": {"name": "minimatch", "version": "0.2.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "minimatch@0.2.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "a85048c04cc707538cdcb6fb798c421c3cbc7026", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.5.tgz", "integrity": "sha512-YBgP/Fxaod9F9gtGsTEMUhlpWpI0oGmr+ZfPNtXKXHZSqAacQjfix3Jx9MokCngc3xG+7IySJJpIPiBFJs2ePg==", "signatures": [{"sig": "MEYCIQCn8VSi0K97GkVu3o4WppB2MHtiDu9u9VtU/9kdA276RgIhANmhqZF2B8axfEfMdQ2klNHN3YpOOwI37lWR8jHxU94G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}], "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.23", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "v0.7.10-pre", "dependencies": {"lru-cache": "~1"}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.6": {"name": "minimatch", "version": "0.2.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "afc731fd9874c4c47496bc27938e5014134eaa57", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.6.tgz", "integrity": "sha512-EvglFf563kuv5NZyrafElzdXmDS8aa0Ec+UWy6pFwDfjaqF2hMqEwVOH3ZoW/r/2yiK/1jslEQSn5liSNpr/zw==", "signatures": [{"sig": "MEQCICCF02s+mVZYpyNuL5jUyyckAzq2EThmbXNelALXJtxyAiA7KBEKGH0PF5MenOrml20V1iSif70pHlExOWI5NMl+/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.48", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"lru-cache": "~2.0.0"}, "devDependencies": {"tap": ""}}, "0.2.7": {"name": "minimatch", "version": "0.2.7", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "850e2708068bfb12b586c34491f2007cc0b52f67", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.7.tgz", "integrity": "sha512-7bUwdPMeF9f10cs8IQviwU19NvvmnhgR+hD+raS6J1eA+e5gxE9alUMP2Rvhk3HzNRMALpR5UFYEA6iH46b1ww==", "signatures": [{"sig": "MEQCIF11HYRUfSZqDnQ0lSsTXdqWrekVth3xvduOSKJUdOrlAiBeWLaoSTfFMvJyeiJW06ZUEGBk/rly6FXH/RDmCIjJ1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"lru-cache": "~2.0.0"}, "devDependencies": {"tap": ""}}, "0.2.8": {"name": "minimatch", "version": "0.2.8", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "1a983623de40ccda200c37192a28a58a640501c6", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.8.tgz", "integrity": "sha512-P69iDaX+rSff7gi3eR0W5SgvDFLYULFEkHVrdu2mYFuILoOFlk9i/4Fj7jjyX5PzXMN5J8v8CqCfwm4c+3nvUQ==", "signatures": [{"sig": "MEYCIQCkFpGPDxYc4+mju6nUN7i/hFcDJrrUCAeI0FkOnWbHIAIhAL8I9W6xOmOkyDQ/WCrsBnA0mGe+Z7MA4UzDrAyuN5/v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"lru-cache": "~2.0.0"}, "devDependencies": {"tap": ""}}, "0.2.9": {"name": "minimatch", "version": "0.2.9", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "b80af947e6a83a8c68f26e54c0d6125bac9f887f", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.9.tgz", "integrity": "sha512-rw/QYK0eczkp1dihfhMobD4kIObmAK/l6ET9ji8nFlB2MnmAAnqONDMZyALBz6sMdoWTHU+iqNx5GkT3Q30bcg==", "signatures": [{"sig": "MEUCIBAbPv3bG63kFvMIC38ZMWXr/dStyVeznvNyQaWlDfbyAiEAiDGlnTNTav3OWA4q6h9ys0CXVba35oNERdQjboKv13w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "~2.0.0"}, "devDependencies": {"tap": ""}}, "0.2.10": {"name": "minimatch", "version": "0.2.10", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "4b1d35d316d09c78e31284d6acf6245b5ec8c455", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.10.tgz", "integrity": "sha512-/tzON/XsyFGQt/ashgu1tyE79oPG1fUEUIOklXrRkiBUiVKCpQOkL1lQkdashrrsrdv096xisFHforsf41Qdng==", "signatures": [{"sig": "MEUCIBrxrZiNz45VayITP5zYpdejI9p4z9lZtwyQRK+NHFCZAiEA18Ua9Aa9o4uW00rn/CN0ZQvFzuxUle/ktP30MdTTT40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.2.12", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "~2.0.0"}, "devDependencies": {"tap": ""}}, "0.2.11": {"name": "minimatch", "version": "0.2.11", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "a0ef5fa776aa6fbd3ce1ebb74efb8a48c6abf4db", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.11.tgz", "integrity": "sha512-4W9kRInQoxhLpsYkjM65o8vWk6Lq6ZBQRWXGppeRWWxPSyUKxfDtlIVHlJnIbkEiBcp9bFzusqr0OKdV2l/Hvg==", "signatures": [{"sig": "MEQCIGsPGxG1Ky09Xkm25JpuIYUgD3gguIPo6ilhlf6soO1EAiBcwFE7rkfnW2TI/uXKZk6yAd++0R+i78+SaWnwNQy8mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.2.12", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "0.2.12": {"name": "minimatch", "version": "0.2.12", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.12", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "ea82a012ac662c7ddfaa144f1c147e6946f5dafb", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.12.tgz", "integrity": "sha512-jeVdfKmlomLerf8ecetSr6gLS0OXnLRluhnv9Rf2yj70NsD8uVGqrpwTqJGKpIF8VTRR9fQAl62CZ1eNIEMk3A==", "signatures": [{"sig": "MEUCIEvWL7v2tyA5rpnALFyM7/aZlR2RuOD84q9UALJlBDMTAiEAqJyNwcsBg8J+RW+zKRtn5nNRsSpqDJ+FyVDukdvh9RM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "0.2.13": {"name": "minimatch", "version": "0.2.13", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.13", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "dc58caf1eba5681e403163af3ed477bf69c8df69", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.13.tgz", "integrity": "sha512-S7ivEqu7jMn+hfWyiknt5V581pAa6/ZEUVPLAXmaGQJxoYShcGNQeovOA7hqHDU01uAcyrZ1cQOUc1+I3UgN7A==", "signatures": [{"sig": "MEUCIQCtHn+NfB2tNjXvZt9U5eA+VjcPpJwLeBMGqCS/n8Vj4wIgWQoFMTXNOxBQtw2jYgQZTUu3pLe9v1Ek7CoZ50uGvdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "0.2.14": {"name": "minimatch", "version": "0.2.14", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.2.14", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "c74e780574f63c6f9a090e90efbe6ef53a6a756a", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz", "integrity": "sha512-zZ+Jy8lVWlvqqeM8iZB7w7KmQkoJn8djM585z88rywrEbzoqawVa9FR5p2hwD+y74nfuKOjmNvi9gtWJNLqHvA==", "signatures": [{"sig": "MEUCIQDPftZ76eImQmDvh4OdfDpB5+8szm7Kyaw9SN6zx0igCAIgSwMQgovrkC5p5eR0w/njzevajwb5/z5rbZTjaUBuJaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "0.3.0": {"name": "minimatch", "version": "0.3.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "275d8edaac4f1bb3326472089e7949c8394699dd", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha512-WFX1jI1AaxNTZVOHLBVazwTWKaQjoykSzCBNXB72vDTCzopQGtyP91tKdFK5cv1+qMwPyiTu1HqUriqplI8pcA==", "signatures": [{"sig": "MEQCIBnX4QzBRtFDjuk0kdzN/EuaHvrhSQaVtXsfEGk4PZCJAiBq6f4lhA9m1UfHBNbkXL0PAuOxU8hDg/NbdCYIlBqwzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "275d8edaac4f1bb3326472089e7949c8394699dd", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.4.10", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "0.4.0": {"name": "minimatch", "version": "0.4.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@0.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "bd2c7d060d2c8c8fd7cde7f1f2ed2d5b270fdb1b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-0.4.0.tgz", "integrity": "sha512-yJKJL1g3to7f4C/9LzHXTzNh550xKGefiCls9RS+DDdsDpKpndY49UDZW5sj/3yeac3Hl2Px3w5bT8bM/dMrWQ==", "signatures": [{"sig": "MEYCIQC7MwfIMrN4v6S4YZ+Hw1kES2TDByMKlbWGLKtqOjjIQAIhALXu291op4MHzD7w2nCRaeBCK1S1RSQPIOMlZJPAHdcl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "bd2c7d060d2c8c8fd7cde7f1f2ed2d5b270fdb1b", "engines": {"node": "*"}, "gitHead": "56dc703f56c3678a3fad47ae67c92050d1689656", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-1", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "1.0.0": {"name": "minimatch", "version": "1.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "e0dd2120b49e1b724ce8d714c520822a9438576d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-1.0.0.tgz", "integrity": "sha512-Ejh5Odk/uFXAj5nf/NSXk0UamqcGAfOdHI7nY0zvCHyn4f3nKLFoUTp+lYxDxSih/40uW8lpwDplOWHdWkQXWA==", "signatures": [{"sig": "MEQCIGJqRub3TcTRXzbR471Yfk77/PPiCidXtH5Q3Z5L3kzzAiByxUDos3UBKaxZK7jttZnJBeSP0biKlzdE2JbDdiHayQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "e0dd2120b49e1b724ce8d714c520822a9438576d", "engines": {"node": "*"}, "gitHead": "b374a643976eb55cdc19c60b6dd51ebe9bcc607a", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "a glob matcher in javascript", "directories": {}, "dependencies": {"sigmund": "~1.0.0", "lru-cache": "2"}, "devDependencies": {"tap": ""}}, "2.0.0": {"name": "minimatch", "version": "2.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "c0625745200ebcf77451423f3d649821f8f0b6e1", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.0.tgz", "integrity": "sha512-9d7FVak20oGJ9AnjCtB4Q6Jp6O4tUlIziqTjCwOb0zMDDNNb+QqDoUxCZ8ngUjO64ArIgYgDCR5IUoANeK2GKg==", "signatures": [{"sig": "MEYCIQDazU7dErYfGSwurlFahzEYfcweoH9noVrh9/BQ6ol1JAIhAK6ZzBZpzHz89qQxrh/yMo4r1sWCSJEe0+rCoO2TmcWq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "c0625745200ebcf77451423f3d649821f8f0b6e1", "engines": {"node": "*"}, "gitHead": "105482161fc08437a84d4b51a69a5e3be6dd23bd", "scripts": {"test": "tap test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "0.10.16", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "browserify": "^6.3.3"}}, "2.0.1": {"name": "minimatch", "version": "2.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "6c3760b45f66ed1cd5803143ee8d372488f02c37", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.1.tgz", "integrity": "sha512-5<PERSON>bQbWRyGKQ91/OwLqpOZTyl00WtHP8E+cRqSsa7IHRuxKr5o8bx7XUIA8GybJiL5iMbaup1YT1A3U1/OqTyw==", "signatures": [{"sig": "MEYCIQCJacyM0I//T3VvhijEevYvrEt8gNScCSNbcCCnc/K38gIhAOtPo3vjUGZ+nFn58ElchkUaSCKmprTHOXowazPmOt0c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "6c3760b45f66ed1cd5803143ee8d372488f02c37", "engines": {"node": "*"}, "gitHead": "eac219d8f665c8043fda9a1cd34eab9b006fae01", "scripts": {"test": "tap test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "0.10.16", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "browserify": "^6.3.3"}}, "2.0.2": {"name": "minimatch", "version": "2.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "9e0d08d40a713a8e1644bec3d88d1c11ee4167f8", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.2.tgz", "integrity": "sha512-nUuOvve2VVdJ5CewD8EcEXrkDlj7Wcak1jAWl22KTz9qyxN1wXLVJ/VS0FFTCCIN49yIncmpHxN1ABHMrIyFtA==", "signatures": [{"sig": "MEQCIA4TcZ1MuoBKzwojU/NZVBl5qNFVqu13scHaJ2It2Vc0AiB/a/AFKRLLKhm4Ks93ZwwQpd/LI8Qgl/p3OdAGTwJdlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "9e0d08d40a713a8e1644bec3d88d1c11ee4167f8", "engines": {"node": "*"}, "gitHead": "df6467ae94d679dbf6cf1d4e888588c2b55f1981", "scripts": {"test": "tap test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.7.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "browserify": "^6.3.3"}}, "2.0.3": {"name": "minimatch", "version": "2.0.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "a265d8cd62b109ce85be49dd36932b8017f7df18", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.3.tgz", "integrity": "sha512-9MfwEqccfN6JF8kjrzN7qHfMgc88TYoYrqAY5Sz2e2TzSNW30wrxTmyp7i1c23GsDjTcNRdCLu6o5+1HQ7JRKw==", "signatures": [{"sig": "MEQCICyMCdcjMnibrnzHPqx3FdoVF8wttT52h2IdsAx4owlYAiBHQQMdVI3uD0C847SvC+z9vZv0oaUeAgu9+LrdIzTUCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "_shasum": "a265d8cd62b109ce85be49dd36932b8017f7df18", "engines": {"node": "*"}, "gitHead": "85c028654ca35b0a5ae3bc83b830785d58c3710c", "scripts": {"test": "tap test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.7.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "browserify": "^6.3.3"}}, "2.0.4": {"name": "minimatch", "version": "2.0.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "83bea115803e7a097a78022427287edb762fafed", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.4.tgz", "integrity": "sha512-S5wkq7sXohYqV86rbXQQZ8jay9Lnw1zMWlurkMsHOfX44ziIBxXUxf4mjMiqIaU/JkG3eu/W+uA4BTwQNQGN4g==", "signatures": [{"sig": "MEUCIQCuUZXY+D7fk+FWkn3hH+EoNhp87ANm8qJyh6hPsBaLrgIgKuNI1h/LiJaRQb7edMuL5xTCWdeXA9Vojg7IVHJk7L0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "83bea115803e7a097a78022427287edb762fafed", "engines": {"node": "*"}, "gitHead": "c75d17c23df3b6050338ee654a58490255b36ebc", "scripts": {"test": "tap test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "browserify": "^9.0.3"}}, "2.0.5": {"name": "minimatch", "version": "2.0.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "a5c79dfcbb3ad0f84a27132d28f3fcb16ffeef73", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.5.tgz", "integrity": "sha512-ypM9yhrVq5GlAE5GXQFn8fpbkW6BCqKIst51+xpAhIuMbgBy+o/j6gtamnruXkcdPLWGSi0bpt+sj42QBso9kw==", "signatures": [{"sig": "MEUCIQDhoTSUaXUI5TnOO4FYZIduxkSinaaJcxjcq0jbsunM8gIgQxudYjlqqKOq139edT1xpFeesJsF0oVTvT3bKlNkP/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "a5c79dfcbb3ad0f84a27132d28f3fcb16ffeef73", "engines": {"node": "*"}, "gitHead": "11ffd7674dc8a76eb6ddceda6e1bf8863d2be63d", "scripts": {"test": "tap test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "1.7.1", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "browserify": "^9.0.3"}}, "2.0.6": {"name": "minimatch", "version": "2.0.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "21df8ea63e67b5848d09d67e57432a5dcb8cecf3", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.6.tgz", "integrity": "sha512-F8O6X6fCXCGysUsI43Fv4FZ7ZSl21L+is3NCdej5XW1G/9ydCVKIbJCOMKW5f/RDSBH7dX3GRqiS0W3njNtm0g==", "signatures": [{"sig": "MEQCIGdW8MCaqKqOB4Wvjqi4d/sqCwW7DyGRa9LVk2/VKtFyAiBVEblTwnXk3g+jOVX5IDCyCT8E/ZeLckGf7+6jNKcIWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "21df8ea63e67b5848d09d67e57432a5dcb8cecf3", "engines": {"node": "*"}, "gitHead": "24d9260047c46e95418407b4a4b0078e11f658fc", "scripts": {"test": "tap test/*.js", "pretest": "standard minimatch.js test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "1.7.1", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "standard": "^3.7.2", "browserify": "^9.0.3"}}, "2.0.7": {"name": "minimatch", "version": "2.0.7", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/minimatch/raw/master/LICENSE", "type": "MIT"}, "_id": "minimatch@2.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "d23652ab10e663e7d914602e920e21f9f66492be", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.7.tgz", "integrity": "sha512-ISURyo2Kd+8HslnBTx41UcZAhT66AQgn9Xm0HbJQHHjw0FL1+t5h7/SlIOsiFQ23NFUjulJ35vPi81jZnCnL+A==", "signatures": [{"sig": "MEYCIQCLOVMvBeum5Vpd2emnSHRNRwmZj5ZRsKssxHFycImb3QIhAKocJvrLKelHsYUKRrfSzDeU2INz+0G6Y4VOhjerdga/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "d23652ab10e663e7d914602e920e21f9f66492be", "engines": {"node": "*"}, "gitHead": "4bd6dc22c248c7ea07cc49d63181fe6f6aafae9c", "scripts": {"test": "tap test/*.js", "pretest": "standard minimatch.js test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "1.7.1", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "standard": "^3.7.2", "browserify": "^9.0.3"}}, "2.0.8": {"name": "minimatch", "version": "2.0.8", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@2.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "0bc20f6bf3570a698ef0ddff902063c6cabda6bf", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.8.tgz", "integrity": "sha512-Y6T1De6r48DnKfyUmQ6RcB88IulNk2rfqEkSyqj+8HSoJ+qV6wsV5xmXsqMIaSMDhDs0EDLETlVWOsJs4P/rWQ==", "signatures": [{"sig": "MEYCIQD8YnMVuoK/2tWF4HZC3nLg34a4dkMeEEao4McEaDlkBAIhAOE6P25u7JbnvH1GLihzyCSPfHLx1Jb38X6Hfx0rQK/w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "0bc20f6bf3570a698ef0ddff902063c6cabda6bf", "engines": {"node": "*"}, "gitHead": "0bc7d9c4b2bc816502184862b45bd090de3406a3", "scripts": {"test": "tap test/*.js", "pretest": "standard minimatch.js test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "", "standard": "^3.7.2", "browserify": "^9.0.3"}}, "2.0.9": {"name": "minimatch", "version": "2.0.9", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@2.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "4dbebef26f62a35976db0737ea3389641baf9b46", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.9.tgz", "integrity": "sha512-mSfGkf61166JDC9TjQ3XUgAS+/szGgTUkCEba9DIMnvgpWsLt4/OPG5MAmV3U0C/bIVZHgyBkDlK6Hbxr/Bjsg==", "signatures": [{"sig": "MEQCIDdN9ggmh6MdKz/qpy6lQpW4cYrmgTunvLALcvFOsYyhAiByTfPdxFxd8R/4/2JKndFq2N3LAE6R4jgr4yk5ShNfgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "4dbebef26f62a35976db0737ea3389641baf9b46", "engines": {"node": "*"}, "gitHead": "5adde897f3865210dfa659beceff8617ee828197", "scripts": {"test": "tap test/*.js", "posttest": "standard minimatch.js test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js -s minimatch --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "^1.2.0", "standard": "^3.7.2", "browserify": "^9.0.3"}}, "2.0.10": {"name": "minimatch", "version": "2.0.10", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@2.0.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "8d087c39c6b38c001b97fca7ce6d0e1e80afbac7", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "integrity": "sha512-jQo6o1qSVLEWaw3l+bwYA2X0uLuK2KjNh2wjgO7Q/9UJnXr1Q3yQKR8BI0/Bt/rPg75e6SMW4hW/6cBHVTZUjA==", "signatures": [{"sig": "MEUCIQCsexMy5sD8Si9k/MRZEAFsWFz796LnRJfSRigactTHMwIgHzBR3+DIS8ork+XH7ytkl4WRTCSHMKsfvLjb34vYVZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js", "browser.js"], "_shasum": "8d087c39c6b38c001b97fca7ce6d0e1e80afbac7", "engines": {"node": "*"}, "gitHead": "6afb85f0c324b321f76a38df81891e562693e257", "scripts": {"test": "tap test/*.js", "posttest": "standard minimatch.js test/*.js", "prepublish": "browserify -o browser.js -e minimatch.js -s minimatch --bare"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "^1.2.0", "standard": "^3.7.2", "browserify": "^9.0.3"}}, "3.0.0": {"name": "minimatch", "version": "3.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "5236157a51e4f004c177fb3c527ff7dd78f0ef83", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.0.tgz", "integrity": "sha512-ekKdP/98gMbw+JdQaHZlS5/irFw63ktA3FXHaal7TXkvdaUJ9M6BewwNyEujYzRsTirZGmEVDho+Gh8bfcpVxw==", "signatures": [{"sig": "MEUCIAW2o7uG32p42nbfkoAHcvfMdoanQiRmJ8yDDODn26czAiEAx2Nr6XBfbh1GL2xkBh/wpKGnHcnDXhsYUstsVq0ULCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js"], "_shasum": "5236157a51e4f004c177fb3c527ff7dd78f0ef83", "engines": {"node": "*"}, "gitHead": "270dbea567f0af6918cb18103e98c612aa717a20", "scripts": {"test": "tap test/*.js", "posttest": "standard minimatch.js test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "^1.2.0", "standard": "^3.7.2"}}, "3.0.2": {"name": "minimatch", "version": "3.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "0f398a7300ea441e9c348c83d98ab8c9dbf9c40a", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.2.tgz", "integrity": "sha512-itcYJNfVYt/6nrpMDiFA6FY9msZ9G7jEfB896PrgYCakHrW0mOPmzBVvfI2b9yoy6kUKNde1Rvw4ah0f1E25tA==", "signatures": [{"sig": "MEUCIH5X2lyGbETJYcKsyHB4ZtvC6kY2LwJjoZJ+RBktPpVLAiEAgd64s6LF89VjmvK2xvVXhOZ1yled2KdDjxyCmcHfIoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js"], "_shasum": "0f398a7300ea441e9c348c83d98ab8c9dbf9c40a", "engines": {"node": "*"}, "gitHead": "81edb7c763abd31ba981c87ec5e835f178786be0", "scripts": {"test": "tap test/*.js", "posttest": "standard minimatch.js test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "^5.6.0", "standard": "^3.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch-3.0.2.tgz_1466194379770_0.11417287751100957", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.3": {"name": "minimatch", "version": "3.0.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "2a4e4090b96b2db06a9d7df01055a62a77c9b774", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.3.tgz", "integrity": "sha512-NyXjqu1IwcqH6nv5vmMtaG3iw7kdV3g6MwlUBZkc3Vn5b5AMIWYKfptvzipoyFfhlfOgBQ9zoTxQMravF1QTnw==", "signatures": [{"sig": "MEYCIQDVnGy7m1NDW9pZ1TN5IebjzwyNAMeIlAw0MV1QJ0AEUgIhAIxuC0mxKvBPKaJxJgVpZZ0qn66Z+8YaVp7/+Mv2zfIv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "_from": ".", "files": ["minimatch.js"], "_shasum": "2a4e4090b96b2db06a9d7df01055a62a77c9b774", "engines": {"node": "*"}, "gitHead": "eed89491bd4a4e6bc463aac0dfb5c29ef0d1dc13", "scripts": {"test": "tap test/*.js", "posttest": "standard minimatch.js test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"brace-expansion": "^1.0.0"}, "devDependencies": {"tap": "^5.6.0", "standard": "^3.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch-3.0.3.tgz_1470678322731_0.1892083385027945", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.4": {"name": "minimatch", "version": "3.0.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "5166e286457f03306064be5497e8dbb0c3d32083", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "signatures": [{"sig": "MEQCIAQgj/vGRw4GsiGymCgvYIs89TYe/cCTDwvKfvFqzMpvAiAx74WMvfHgunlixpGmmaWBrGVcUp23wUCuSTGh29lWvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "minimatch.js", "files": ["minimatch.js"], "engines": {"node": "*"}, "gitHead": "e46989a323d5f0aa4781eff5e2e6e7aafa223321", "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "5.0.0-beta.43", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"brace-expansion": "^1.1.7"}, "devDependencies": {"tap": "^10.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch-3.0.4.tgz_1494180669024_0.22628829116001725", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.5": {"name": "minimatch", "version": "3.0.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "4da8f1290ee0f0f8e83d60ca69f8f134068604a3", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.5.tgz", "fileCount": 4, "integrity": "sha512-tUpxzX0VAzJHjLu0xUfFv1gwVp9ba3IOuRAVH2EGuRW8a5emA2FlACLqiT/lDVtS1W+TGNwqz3sWaNyLgDJWuw==", "signatures": [{"sig": "MEUCIFmtbk7KzRs+Pds5cLUrMTOO3Oua1zioZzquHkkGhg0qAiEAl5NiTtpRVzxDa78DmM1EcNK/GrmoVXoHZxmmbit+g2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAC9UCRA9TVsSAnZWagAAYcMP/jf6QiKU3WOvktJPGGKz\neX03vLSIUkMMgxksl/2rwq1msHw6m+SLNvu5NetVgngXK5bwOVam4ZGJ0Z96\nmPdeuVzouEDg8Hzlz+kvdK32k8DXcfxfuP/gjuafUiaU+7l1CJ/zTjsuHU23\nUXuxgXjRS85Qv3aNygMXwM2/RSEbUsuEJFDcjHIarxlYeDHF93Xr4aevmJxA\n/BVtrT3JtZ5lu1x/jzZhAIKWhZwvuqoC7IYhogSs/4yE3jnxMi3rKYGiS2wA\nOZVZSj2EepM95yOB2mZtCChp7dvdboKCxvptnchamu0MP/7G6j30G7TjsutC\nimhtyTi95CXPwqzqBBhsZu8uo+fiOLw+zA/znBL/2hRU+/6xkz05mzsUUyYE\ntaFR8ivlyWneNg2RGX2d+6Ec7gXkY7w9jhBPbGi+JpiCfpoTpchXc5ZWimZh\nLeymiguK0bddXREaVPL2cye/5bwvrC32oBy9XJ5C43kMe+Awiq6DQajPMCCS\nTkW1glOQZ2qNvOhvP9WYktWnh+PKTtriejaVsE00nYWr2uAHjNhk76WaB1Uu\nyhCXcmqDrrRa5FyUmqkuc97CXx925T6phqzrZuR7DhoBgF02g+gGe9cn5Jyc\nodiLzYt2Lsxq+o3cIxaiV5ybvaA4QLuoJ6N1LKVIXj0GIdN7BHiwGrR0SHkb\n0ItT\r\n=gKVD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "707e1b231d5ddf5b00040bd04968a1a092992d1a", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.0.5_1644179284517_0.44135801625913396", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "minimatch", "version": "3.0.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "4a2cab247c5f4cc03b85813ebbde7ae23a6406a2", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.6.tgz", "fileCount": 4, "integrity": "sha512-dpxq1Q5/wIfWZjlpjEZMpHWLDIEGKAzfxdWG/vDJs6sX2mtpm0vuysXd9bD/XnMrGc/O14D116bYXsSDBJK2lQ==", "signatures": [{"sig": "MEUCIQC01/8AbsaQSS08Yw0pjwKNDZ5ekgrQ5Kshn/Q6CludNAIgSiKQwzDsy0Kr12+yQxXl12AEnkKgSBYPHyOo9ahSlN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCEmZCRA9TVsSAnZWagAApU4QAJ8Et5xhO/5xOaxqD3OU\npZpSaqGKTrhOwPH2KKBEm/ecZSkT7HtaGNStNKFJSxw5QWZ5jUYHjiD3GyaB\nfYZJN4/IGXM4iFzmr0t5oJ7E/1a7Mg5HsaDPd53QQMU1pGZFui+XbG1afdGF\nyD1j8X7UOUxkk1c8IinuUxFOgLR3Re6p6X3Z+xPEsl3Q54yNkaI9fuPtKsaT\nuZHTW5mW7ha7bCER7T+AKpGn8fEfLU1eqGFZr83+zoMe116MHbH1qqRkIDmY\n0JfLR4txqX7uPgmHv8Jfp8/LAzzQrwdJQAFeGLJpVOTZhHRJb2TbMkAvqgDI\ngL2WhXh3eO8dsIRZ/G6uKO0zNsMAqqpr84jMxW2cOzSQeDX6f1pwmPL4b5Bv\nYtigt4aEZIKoXNFnrc/r07uezwbcS0KmfYysQYw8BPme18MA72sbCnwXmZkO\nwYJLDS3ceNQuqQkubXdvhLIHOLmZeso+d1gh8WYwTYNUIVx16PZmfMMFBRUO\nugrMhNk+sKxNWuUXM1Hhcaxdc9dgmOXMYtsZVsQlIiWujU30WqDv7s9qnXdM\n6pUfDwW/F99MoqV8/G6K9GNxcdtJ4bxbwR+jvngGFoprU/gJowKwWHFIs9KS\n/mNVJPXqcPHLwwlyVESCwOwTXCdXQzK+SacvLRFavEaXf97SSfC3TXpqwQY8\nD8F+\r\n=7VkV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "5b7cd3372be253759fb4d865eb3f38f189a5fcdf", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.0.6_1644710297540_0.9150656470974228", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "minimatch", "version": "4.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "befe49964c07e98a5eb7c5191be997b2aaffacc8", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-nUXi6viDR8DREKfJTnzZLh1amprrt6Hepx/WUngirNdiNEDmfkW2DLfiTeXWcMkMY0bRkuQ8eyCSbG3W4D0Qcg==", "signatures": [{"sig": "MEUCIQDd71LmyBvRR0Lqo4F3o10PgK7Jgk7EulaQzsh8aUFE4QIgcr9pn7Tpe3gNKK/lto3Jr+J/ofVmWPPrzHoB1EBoEoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCFLNCRA9TVsSAnZWagAALRcP/1HqDx9jpAnruCjxh80O\nkeOPftFHEuSKKzhTd90Y4ZNwzKfb0xjl0QXZ3iGN9kx4GHoHn7D564l+z3Y+\ncKoxy9kGeX9y/tw7nKtosyraVKTrC3gbKTtA3FH2nPRm9KvABJ8OT776YJT9\nu2aSfk6mdF6hqqrSTubYnfQBNdcaO61kt+AJJUmWfUM5Iz6BAP2ljznViMwY\nzZF2/uQkh6AMtKeqR3q/4sEEPSxShI1qgNaDXuB1TWH6Lr9coa6OxJ72+K0s\nL3mrVxFbt5sGA0zi4G2sXc192HxJgX7oN6mjevOZex2z11x/6e7IichFw96b\n8PAyxQxDvLGpgt1efUu13g9/1N2xQQGZ2Ta+T9bbGOaqKFoXIJ6xW96icmW3\n0iWIQM17z509rKOcRpXB7bPpXMbU19QhU6EBVJNUdgqGozJuY4llpLg7g5bq\nIGcuYH2YZW0IRTWG9qgqO/BLuWZeWslr6gTlZUSTmeW/hIXCLZRj4v6IaZVu\n8ZuIGmyFDy7imL+ZeHf4iVhyLWcasJXXcW1g4lmasv04xKgz+MdHQs8isqXk\n6spOIYgxHcPZ1d26RwK3F/w97WcvsMvdesKTx52BN9yUqKLBknvJ0dBo/pGW\nQSqooaxUXBnaTh6p4H8C2tLumOtNUYxcBKBbKRzn9Gxl2NobDOPlPJd/x7Tr\nYU9/\r\n=052M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "26d281dc585af91df47cb93844e227e0ee90b7ce", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.0.0_1644712653720_0.38662374444664915", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "minimatch", "version": "4.1.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "6cf9879294e33a259536492d905e31be8420800b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-CRDd39hmjy31EqKRNlvKSIblWVXjoB9v1+qwqsxVDlnXXEHrC/63ilDd/iZFnmDLUT/FZxHc4+4t9JbG1ZoeYg==", "signatures": [{"sig": "MEYCIQDI+Dt6aoY6d7ckeIEl5se7FXagkVrNJbxuFguOA9I/mwIhAMMZVS3kSgQc7yqIVST298wqJPq2uH1Pj0L+6MWra9lD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCFfICRA9TVsSAnZWagAAV34P/2ETQvagtBgwR4vHSNM3\nWFlEHKLPRWJx0cywMYYG7tXfmMzLYsufG8r3f8HVCEoi0WdCXTK/cooIGdn7\nPElLUudr4sBcLBSnDwxEW4T+T2USavd/VeIvcw/mJjXngKn5/jKzyqM1fvqw\n8ogCnyQqX7dV2hCrxqnmLrkKeClV4ZAwrvvWlAYc8v77KUv+iQwwPqtimCWt\nY6H8wfQaRUYK99Eu67Ya/sVt0g81d/bGfs8SD/UkN8gPNglgzGx1/3+F4F+p\nX5/rHkGFNSv0yuO8E7QSA0ldy4es4E9uYR6Zw8WSO8qcapScN9Xj6byhZtnM\nJ9tQjAd633qEYoHI/2j0qobZNUtuneTbKwui1RwfQEORKdDTBXUxOibcEWac\nhs9sOPjfRzGcPPibGft78DZP00Xk8jWALaXzZx5+6jvfrf2FZVUeMd5bsarH\n+fUXXs1PrW/8j4xeT6bB1EkJ3MZ9yZIk/47g7bx5vf7IiFt9tPDdr1MbqT3A\nMQBe5gNa7VdOlBZuHnt2ydBcFTtbjmf82JHSG8hj95SfMoXuhNfAz1WE06DG\nMKr2lS9MJBZNAdVerHzUrKB8Klpu/I7VyXECe5cP7GXMA57+LwclDVH46ful\n3pj0V7JD4511uyZkD1yq99U6LcbLy/ggt3FhTI92VmfJ2N2ICun8PbGDRGvJ\ntv3d\r\n=kLsy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "5d15c146e9af3cbded8e62b7b5bdf76a7189c58a", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.1.0_1644713927866_0.037644074617840806", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "minimatch", "version": "3.1.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "f59168d5cba6b6fde15f6a88f557fe7eaf31d8ea", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.0.tgz", "fileCount": 4, "integrity": "sha512-vgmUyWyt2qidcJXdF6e+kf4DgJha4vzf3ctWJ0uGjfvqEgoX2V4GXQt0eZwM2FJWKANfS8VmzpvPKbWYibkHZA==", "signatures": [{"sig": "MEUCIQC5kVDVKTBEBi9F6R9/4/cxVqtfBgNL+AdNfDgyfat2KQIgOhMT7XtbAyCNjmt8O+Qesdrs+3juuPyaIxsS/jb2wto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCFjaCRA9TVsSAnZWagAAF8IP/2+P7gq6aNTC1ubmkTcB\njZlPYLsPh78eRpCUjR0x8SGyH14/0wogEGPYq48r5J80qEnUUEOSGLq93es/\nWoJoixNjgk1PdOM9qZSXcxzek2VoCszRrRMntYJ4b8r2iMX7phBnyzt0IF4Q\nP1LkjF+5sspxfSo5jBTGbnItZ0Y3NIEXf1ZnjH18fDwm8rl6j7xXKQxLtK0y\n1/dIg5cHaXsWV253tboTDBy2LirDCggjmY38kMbwUqGmvEMg0eiJjXPNp8Nj\nAP7ffljigF5DQFaPA0fY3prxfa50sdl+ofVS5XbFd6J0CLHeVzdvIWuBgr3o\nZQ7eD/ENON21+Kw3AEcWqZHEFYuwFNHZe8znQNXuhXiPcnyCYYxIuTVa4R8M\n8e6New9MaP4JlVMibOxUdZw4nCRHHEZqXtlIN3QsT/jhY9UJatXwtYPVOhHx\nQ19cfJuhnFlrKj6BVfFdXlLAS0waBzWhjL3EcOdyTzDu/lK7e6zFiFbeeHFD\naz2V/+ubml/QwIU/RewwjF9Gj8vSSl1ZLobVTMvbQCf2Et9TLoFkt5KNUQSx\nbsuRoHnP54eyFtwY4YcOdqMmw1PBT4bwc/te04CUFFDbVHm36ufHEa3XMu/b\nELjJyc7oQaZNaB0f1g50ECuaclGD7FSA0Ld0xyt5X12412d7eg3Qwg1i0GYI\neYFM\r\n=99c7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "5e1fb8dd2bb78c0ae22101b9229fac4c76ef039e", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "v3-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.1.0_1644714202770_0.869405998492546", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "minimatch", "version": "3.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "879ad447200773912898b46cd516a7abbb5e50b0", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.1.tgz", "fileCount": 4, "integrity": "sha512-reLxBcKUPNBnc/sVtAbxgRVFSegoGeLaSjmphNhcwcolhYLRgtJscn5mRl6YRZNQv40Y7P6JM2YhSIsbL9OB5A==", "signatures": [{"sig": "MEQCIDRHuxeUYfTOfuSKaniiGo7d6PLEPoxHSr7sQAMZzATvAiA+D2sFhBAk3Zhs8gjxf6VORhQuAwuLvcJjXBEsFzBddQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCIKoCRA9TVsSAnZWagAARqkP/jBV1JMkqDfVVhDH140k\nvRFNcYgYRhvSHX1R1fp+I+iVPF7WqlL9i+C87L7f37+OGC3z9gR3lQKBWbfo\n0tOGuFXJDM7oyKZz6KgjmDCHc/ABF6vISGg/MrDWb1TpG+0Qvcc8WjE92014\nETCfjjLqC3jqmyfKUkPnMFwcHePs4DI3whlQ7+aNqhAgUzbiBGCkSfgy7JFx\nRK+y+jdKSLJI1rAF9nV54FsRObFJeFh8/AccpRYL4KZWQORSjjGG+bGK6p5V\nD7Zxj6iVg3mH7Q/SHoPx/mVmL9wfrR4VzdqiASd5IzDd+y+UoZrkSAKDwYgV\nwB6Q+L5MIWn2GRLbkQzf5DSb1n0gH1pfwwYncNVjKCCWvYsgStpdZF/ll3w+\nGd1rD1tCizBpnhM1My9Za5w95dDgaZq7N4kOJwGvWRygwBORS8K3DROE7arE\nsRM93t4zwQWjCXWjsnAb9+ZvKBTTjS2a0OTm+waZ6wKy7olROD779oDGz/Ft\nePjGWRDL9VdtWkR4Tk37rScf3cjfR0NzHjLpxRcttojLX7L/g6Yn/Tqh6hok\nymNrKvJyF49L/Tjzt+v3dmzF1F6Wd8J8w8q5y67gFKbPqpmBcBbjB2k7xeQk\n6duKpPR0uEaINjTdfz3j7UdfPh6C9i99Lb+z8YIT68UE/IT17wDxuOWh5ieA\n3sdy\r\n=IqsG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "25d7c0d09c47063c9b0d2ace17ef8e951d90eccc", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "v3-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.1.1_1644724904212_0.2022292949011142", "host": "s3://npm-registry-packages"}}, "3.0.7": {"name": "minimatch", "version": "3.0.7", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "e78aeb8dceccb0d12b57a75872da43bc68e7d7ca", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.7.tgz", "fileCount": 4, "integrity": "sha512-pYjbG0o9W2Wb3KVBuV6s7R/bzS/iS3HPiHcFcDee5GGiN1M5MErXqgS4jGn8pwVwTZAoy7B8bYb/+AqQU0NhZA==", "signatures": [{"sig": "MEQCIGl6hMac7Um2//TeRhJiO2strVBXSo/nBaz90WzMI9yeAiBlgoXIiLBPQZOc8qXd0hfu93M/sFfl0poFjfdcMZFH4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCIMVCRA9TVsSAnZWagAAva4P/i0HkM5RG+FjQxZkMtH9\nSamKiqKt0g4O7tbn5oi22cqihGjbYL75AYC6TCBQ6BRAwtOjmmU5yQrF03sz\nORNdI4wNz3/+ZW5qyZCR/fQZzsfgu5hProcdMra4H8resSwgIPxuPCfdbK4d\n4o1rsi+vl2nhZEaazNH9jTaVHqHsInyXJUJURskh0QWFLAd7zEohb2OM+0av\n0VWcAwWCDXlPV6/ZCFb2+Kh4ZcI2/3Jx4jiKZ0fvrFjZjB8FMHRk5t92gRA8\nHD6VwGP9O5ZzXGvxUqliBNl+fIy7x+aSX4QSrdSJl2INILErTrzqBPLygFHI\nIAPwUEfQisaSDMXHeFMk2DW9mnknL7r3VKnuFV4UfDy0fscj/pWyak6rETbH\n9CXagq6kPELsBrkvZYHZ0492neAcg+Xhca1woRZ4GX8NzDAO0/BUaoKcAvX/\nCYiUxUABil/xMOtZ8F4PdTIrEMgWZoRD/gaUStY7ubA+34UkSXD5u9tjmc7l\n0ex7OTcEAbreQSHri3lG9LcpmIj1ASOaOicMzPIQtsBbx9VVLbMztnIjHYDP\n5cOKahVjoqdUpGxbHqeb+hzzGIn1MbZyqIqbx8yt/fCmNi6SXQREQd5TXQtf\n+saogj1GdD9ux5IwspEwdCP2QWTLGdxUyCkHnVbSV4Lmxu8gmmRhbeSZyzEW\nctv5\r\n=yweF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "a6f52b0f9692e918e59bae84dabee02db97f96c8", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "v3.0-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.0.7_1644725012996_0.08373802686227072", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "minimatch", "version": "4.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "88d8172f2e1babcc3e249538b1a46970dfea1300", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.1.1.tgz", "fileCount": 4, "integrity": "sha512-9ObkVPP8aM2KWHw1RMAaOoEzHjcqzE1dmEQHAOq9ySRhvVMru1VKqniUs/g6Us4KSwXKk0+uLko6caynDcWEWQ==", "signatures": [{"sig": "MEUCIFEr2UJz37yniW1AhGJ2LAlTNfefyTW+rwyzkDDgN7OAAiEA8wleh5ZLOyR+xSaC72yCBP1svcw7DTgf+nmgnlNEM2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCId1CRA9TVsSAnZWagAAoaMP/3D/9KQl03yq6n24Lnf3\ny4OqbqM9YIkBHbQCXeI6X3sr05q8DCw1OK72C6pAkgr1VPC0U8lWY4fRMDrh\neobrJ/Vi3X1gjoMRE5d6v3kHAx89Msn3I32zf3YfU13Sr5SIoR9TdSLX+izk\n6ZZbYSY4byxHAOSsxC3rklIYLu90N9521tq6ZHm/5xfcmZkGcPqzN94ebr+f\n+ly5xAWc08fkEAghnYoBoplTYtrUl+VIXdib3JCw91dhEY5tb5w8VASZDV0o\nXIbnUwUZcITTphmjCxEab1ki1IsqDzMD8xnkJqIgCy1o1QAlqHBNxTcI9mkS\nfetsJUs1rDo6wwsp43jlhZH4JxPhMeTeErATWEWS+17kwkLUxR0MfzSyQVUW\ngJR74RtLWYuIYiA0rzyJoqz//KtGSDAJha/XTFeBHBtWLSO/zeUnjyz9+shc\nId3wYiGZ7c2shnb7iurFTygctjqA34gfN/1yRHgNLjRusK9W2MdU8ISOJWEH\nrYNGRiwylJj6P1KD+LrP5rSGPRxixeSorq7CPfo0JNjiDhCqBTQG+YkBPCBo\nC2McFwoJRUyFq89PBgSu+VPP6TEOulXoKRMpurESJ6HeSzQx1wE2QDTPpDZb\nhFOAHK7MorzbomXZ6UgtTfS1YAZFZtR6NcWeotZ7V3XWsMNds3g8O6aTqlzG\nFBhj\r\n=mce5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "9c7c4bcd9abe9d2de9139383d6f69fd50a0cd990", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.1.1_1644726133727_0.39065732218879523", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "minimatch", "version": "4.2.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "3575202103e58aa0065edbe85cbe5354c77135b6", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.2.0.tgz", "fileCount": 4, "integrity": "sha512-ZeCGOh91BdmL6q8aHpnLax/3UXJA+g41DbtDXt7MQ+rNUV7mlpdjxZJGtl5JQ4EYXl1ajRt+rd3p6r234BhZ0w==", "signatures": [{"sig": "MEYCIQC5WfiXFxwuf8OAuUmISZnDHzTY4yyEkBta7UjixVA3tQIhANB0UzVyTEqAo0tAA9yvXD3FkBSWATmUHahxDZvTPxPN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC87QCRA9TVsSAnZWagAAzbkP/24NCnbmfkh8lbfEAYFe\np8ejYtf0Mo3HPexrbTEHLs2EJw1vFEXu+LXabUfIvIQiyKS4k7PnDxgv7GLx\ngbNAAn+8PdB8jOfBMZoI/r7iP9Xx0gUuIlrH5yvg0YjIOl4oJDSh1555TkVj\nKvyKhdbdp+JKZ4PIit5uCnFrSeJ0TlNr2O9v4IdiXb2AUUQ9Lg7DIdlNiSBQ\nBryoDgs/OGPsiDYRtAH0I6grsw//LlkotaKSyFxchuZI6rYozMmUD7912//V\nklNtIOgcNT24Ydq2bkE140NNuReu3BGJsXj0dYqv35Z6vBtvz6kfWeHLUDff\nu+w+ZK0Nh1aR1Z2xaUGFRmrOy0o1xLtQw6d6o1dL5Yfe+AaMj5ZfyFpCbTgq\nLlu6bGoCPCBjJetrl2vdeDvvTjG1g8qjI3T/NZhXBWYZST8oD5wiDqtIF4xo\nZDfixAhPjVwp+a8/KjQ5YfKOJAj4RsQHf7I1ogJldB4XrHlFyE3yn0FLDzHG\n5nv32MD3VJCA0DdopazAT8VJMPvnHlwVLC6P/cvrvB4mixfwQ2oqeHUgpzXj\nP5MpJNy2uWcAbFsbrAB27nIAGkdD6JuADNSXwuR6raLux+BDjlyWf17DKhDw\nGMd1M1O8fI25m1kZ6uqPsAIaZPoSDGR/fDOlhn2iIDWXmH5ywv4J1vVwXfH0\nXthG\r\n=4ZLI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "dd7aa958cf4cbf9e0609739f46b78777afeeeb49", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.2.0_1644941008285_0.12448061888028517", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "minimatch", "version": "4.2.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "40d9d511a46bdc4e563c22c3080cde9c0d8299b4", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.2.1.tgz", "fileCount": 4, "integrity": "sha512-9Uq1ChtSZO+Mxa/CL1eGizn2vRn3MlLgzhT0Iz8zaY8NdvxvB0d5QdPFmCKf7JKA9Lerx5vRrnwO03jsSfGG9g==", "signatures": [{"sig": "MEQCIBcXgrJSwR08WmGmb/kVg1JILZGlZW2UQe97P1XKgZTrAiA4qXi0RZir+hOB/oKYW01sOyyEr9b+Br1NicZ219nuJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC9ZQCRA9TVsSAnZWagAA+kMQAIzfV71ZAcbVTxbirxVb\n9GDNdvISRrXgstza8d/wj6ZdGm08UbR3TLxE3L2ZCFiwnLacx+CaM7zbb+NX\nSKs8gtR3VgJEDuD20l7ktXSf1GDsrjacOdrlHrDga0AEiiVU8IPXIwOK90s6\nIQOU6iIxIRGRXO9mKr6cuE0kFOGCisZSkDc7cc+fZ5aTb9vLYtz+EnhVlBsP\njbAS2KS9ugBmRdqxVnV7gEH6koXq83M0NEkM5m5oXjgl/RXc5iNE622qbjux\nDsI3IrNdrflUCEweCZ7qBxOyPjQDb/SZYDXEzoKDMETA+c+/LclmEFFTp/4B\nlu7wUaOIAOlUzh7XSAIpi2qmDAemPJZMoqfXwwlhTrx2ZiBzKxbM5JmroXvg\ne0c0B2bbbK5Pn0AAHLNZNdSSV9V/qApJrjpCTwe1BsS3bswqg5PibnCqYNZk\nucCBQZr7eMvhugSr5ikEnRA1p2fgp6jkQgTETZFIi6WrAGOZnP7kFsz6eaIy\n1HNm7rE/2bH0vv1p2+avZKM+ZjhUCII3c+yW/WibiQYUB+pcMlqWoTruyYLc\nGhrANPIt3GNeQ74EeN9QWIgoJGjXfBDG02kY2VA4sdpEmrO5rZNZ5WHKS/dr\nDIEvx5PrnLE4HE9bS+OHrtMB4bL/Fx5FzzERwRWBDLCCC7t0MryBZRpx0muN\nHjKT\r\n=ciRQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "048ada0f1f2c84718477050a25f5fb457e7fc75e", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.2.1_1644942927948_0.9465140767715339", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "minimatch", "version": "5.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "281d8402aaaeed18a9e8406ad99c46a19206c6ef", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-EU+GCVjXD00yOUf1TwAHVP7v3fBD3A8RkkPYsWWKGWesxM/572sL53wJQnHxquHlRhYUV36wHkqrN8cdikKc2g==", "signatures": [{"sig": "MEYCIQDkrrgNBShM0Xmqk86Zrr2CGq/7XjoE9IGikS4/OXCuFAIhAMStBAdzov0lbKSDWvf7+JJ/uHaRrD+ENhNue8Zltzs2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC9m7CRA9TVsSAnZWagAAyyoP/RAf4SslmulJ4xXo1e/T\nYJT0+LL6OAoBrm7haqnW7NjTV0StqC+A5NIohzvSdRe46/i88YtFzgNppD3h\nOa8boghiBN3GdZ3i4wbwR6I/8l1cZe9KT5yuqZkAALbwEUOO3UPMxqdFaEfA\nSVMhugIMdxcBdR4nHpyDuqTdWgRFjanaMUluaBlq3VbxtT1iTodQHQmHNDML\ng7hW1tQ+G5tzb9cbP639fkZvIkBH4I/iVJqswbFNXfPUi+I/aXN9MShPi07j\nW9couHlVEhZJFYarl7cV1OW5u6P5QttB+c7WsL5/e7FJr8riQgoctIyDVRA5\nfYWtsWfC3WF8BQX3VNH4YK3/L8+m2+hfESRQKSgsAw5vRwZhqSIuvBIvpbRY\nyXC47ld+wS/CderXz3yUJGF8hxfWX9Nz/lgpHfgHQ/d0v1J1cVPzy+s0cZAf\n/C+a2oh9AXWIBKpt8ilPhngIkr17fX7bYS5L4KosFAJ8gua/+UswX1h4kUR4\nWgujKRdXHQmvsNyqLU8JYNlptMNODj92SXvRuoiVt7geQrO0j+AWSN931ikq\nx5VQt1LOQRAKLT6nmsSYo39CR8IqaFivi8nkbASjjxKC+yjzCBfcjZqgt/lm\npBk9kh1Wx8+dqkWmFXHxeKNmPCo7fAH838O5cJGif2x0Ai3/+wsxdVA7TTq8\nFlIv\r\n=2J8l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "fc44f5f9123f534ecbf76af65558eb87d90f1028", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.0.0_1644943802981_0.33945245682881464", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "minimatch", "version": "3.1.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "19cd194bfd3e428f049a70817c038d89ab4be35b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "fileCount": 4, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "signatures": [{"sig": "MEUCIQCjRUIUS0JXXl/vaCoh294tvrp6lut12ZaWVv4bh7mmWAIgDLzDZgvoXeXzGPeAuSItCyQASG/Nnk+X7NCGfbFFR9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDA3rCRA9TVsSAnZWagAAiV8P/iCEUNUKlBcB1VfJNYRv\noTlTsav1Jmjk2+W5Asvl+fMwzWDOf4irHs1SS8vArxogUEvM6UBeCZedmtcE\n8y/XYYf1f4rMIODe906e30Xv4bcfDqQOMhKrxQSI7d7LqUS2EHnk9tYL2ROL\nxiqzxL0a2Ujpc34YD9hqV9WyhHJaq5IprVqi01Vg0NggZvVYj59BsvadIGDE\nb5HYaQVPpmhLy1ykt7dAXUmRFWco1uCfwmMhyX8204ZjJS7keRwkuhHqAVp/\nSfWyXaQzKoEQNWVuWf7wp+u8DWctHAAza69bdzIMDZoa0wOukve5eScDw2ud\nXvs/3kHPy5Li862zjU/kQdZW3WmN3AM0vId8pvWEzt6uaMxYOB+ce+ZR/2c6\n0pWfwwxg65qn6FYUx7mU3aHjharqHNUoPuPUJCAdmjsSRwNUl9hvFhbWr1W9\nCXQBpo+dZd8Iw8nVLmFMVmAJrDOb/1aQcEHmiriYIcSY7wLY+XZuxtZwxnvm\nuYDEFCuSmASf4M7f+RY/okzxwsGk7gD5PL5bKb+kANYctCrke6wDLzDNCxJJ\nnQR37/YjSJUxExmJETSuAkRR0brKi9qeKMMX6tw+0ZkdixRElncpETz3MkrL\ng3F78+eu8oZcSIr4s9CwE6wlXnMl3zQgyYaVyeXnZ9OC4aQ+vWU05n8mQgH7\n6u+o\r\n=0kEM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "699c459443a6bd98f5b28197978f76e7f71467ac", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "v3-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.1.2_1644957163371_0.7957923209168807", "host": "s3://npm-registry-packages"}}, "3.0.8": {"name": "minimatch", "version": "3.0.8", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@3.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "5e6a59bd11e2ab0de1cfb843eb2d82e546c321c1", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.8.tgz", "fileCount": 4, "integrity": "sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==", "signatures": [{"sig": "MEYCIQD9j916UDkpsBvOBuzz39QTDMU4OHnlfybNOqUJydinsAIhALl8i6QHw6K97fGn2+cHLbLOmssW4OQu7pSGA5UBbWNP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDA4dCRA9TVsSAnZWagAAeioP/0HV719lTdfog4/rZpwJ\n5Pmi0W8eU2JRS+fvakOJs6rmqc/nalWIK4MXDrwTZici8lSdxm+N43fBP6Lw\nF9wTN9EQCoOwSzEPqRjjq/xlwCSIQazGeay3b/WmvS6mZ6xRLfcyZkv0kv5b\n/xz/6qIuPFIiZExdLGnWZv20a6i9rOxuA3x4Yc1OPZPo1vdc0X9syNEdRZOQ\nvKEh943p9N2Py+3kUUCuBvsuK3deGyScYaM7sT2/nUgQXt9wKpBTiop+xm4d\nR1ND8SZUVE18QTlvUbkIZ73ZtySLd1WI6tGBgk2PJf7U0iu0l+xga6ON4bEt\n4y3VYLddmPk0HHCBQslnEULb02JMv9O4hZ+2IJJbCIcxy2jxtqHbuE+cL5vz\nqbY6W+NuOJJ+kxhCv44p3+CCNK+Mx2ZZNAIBk0y4gMJVUDHnjLR0YkD2at/m\nm5NaLYo+WCtlxuHgrHtKT7tP/g2bO/09JjK6vpl7/Tf9zGc8iVQEQf9NrEWU\n84kQresJlMG5DSX8A3QFIQceifsL32XxrJm/vq1n5hEO91h5YmkDAY1aN+JC\nTuknw7LPEFf6dyx/5Ou3Jn6WQYgFAIdpJz5dmEWvEcQHfY2fYwnwhjmhAqJY\nlCrvCJa/7WcZ5EfRMwQYYCLh5G1Ec4tBzN8roA3z6m0nb/uS99fsEP7LdJIf\nc2mB\r\n=1I1l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": "*"}, "gitHead": "782c264e9ff4b02b41923e827726e03c1bcaec28", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "v3.0-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_3.0.8_1644957213544_0.7912394685327393", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "minimatch", "version": "5.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "fb9022f7528125187c92bd9e9b6366be1cf3415b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-nLDxIFRyhDblz3qMuq+SoRZED4+miJ/G+tdDrjkkkRnjAsBexeGpgjLEQ0blJy7rHhR2b93rhQY4SvyWu9v03g==", "signatures": [{"sig": "MEUCIE78pBtDY1bp9T5HHzwqUppgLnMNx6dairfX/YajzeuIAiEAkMOeOdbQGcAU3c3zKplJrAD63SISphtLaN0uV9Pefpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8c8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqRw/9G4cX4DFDDrPuwP+A6vTrjRIlur6L2EzI6odrdM1DFAwVXk8R\r\n3617gRLK923cZdU7oKKlPBepkb3TBj7OPp1NKspBQJnNHfiREkGy8EOCzQRa\r\nJzpynnTrYRhemYav3lDutWCnQqHVjB59CDljmSN2nUgr8i/DI6xeYAipFEwH\r\nvYYU8swj+Xmqkir8kMQAF7q+AxZ6JVOa1O3ydZSlnsdbqw0XiQhGVnY+sWOm\r\nNdSb6pOkqHgmoMOHaVb61cpvU8C7+AmSThGpjuRg4QbOtcdVBiAO8bw8mCva\r\nXV+a3tggCCT6ImtJWgR2dEHZL6Ld9LsGKrQQ4nuN/z1b0Ti0jj6z9kS2jycT\r\noiFi7QzFX1fbPlZp3aPjiI+LSMNGrww5/C1Hbgg3Jd3VjelEf6v+pSjf1ski\r\nLlVJDn/22x6TmBlxTDezk2sXmkD2Kcn0CqZUzDyMXiXumrj7CeN8nNzepIxZ\r\nNikX9qIQlAtKoEFT62ZaY32usvvcb637oNxp8ucXi/lwWDCCOe8Tq1l8np6V\r\nj/CA+mN/EopGXvwebSn2eQHEUZHsCn3W9BKlR5YPRwaDedeZ9UR6tuP/Ag3B\r\ngNVJj/akzxAhgLRLnXvO+gT5gfmcUb2FBsJ+mjk31dpAetWF0U7q2ZkOYapc\r\npP7JaRxCS3GSOktJkKLTe02cJQo2c7g9CKk=\r\n=izgi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "9f49616b782481f3bc352ca76db958b813f14fe8", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "17.5.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.0.1_1645725500668_0.7098368038728715", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "minimatch", "version": "5.1.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "1717b464f4971b144f6aabe8f2d0b8e4511e09c7", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg==", "signatures": [{"sig": "MEYCIQCtEjMkg2cM5mBWKX+XLT8M+6vOTh2EvuCdLm3jykxhTAIhAKsTKSqNIOYklL56ap6FMJg3zm4/9gCHcmEOtzuB2AIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigngYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp+w//f9ljiJNtvG+7F9qrWhwsYVagbeVJv2yZMn+/vAUN7S/f4k2e\r\nbPhboYu8F5PZxVC0cfLUihwEWMXNdWJBTNnBf76Qiu0Ody6d8PRm7HvdufI6\r\ndK7UbIm+fG+ksNYgTzzqwdD0oE6WqH1Cblv4t48MalNbwTKBLWtG/ZXsJEko\r\niffSzk1/QSbcfq/gt/uWS3iSKbxvWy5xQZLxAEfXWbLZKxlwIWtfoQ3Cu9Z4\r\nQoeq4zxHWSSMiAzD/0y8g57+5wOjC/FZkimdZqVJdMvcj1vXEAEHL8aTdhVp\r\naP2RZ9GssHIsJ8KcLMGP/LGa3sMLfAfzPvXMnSKkgE8d9qIzB3BtpoG9jaKU\r\nkkdhBkhH+5/d3LAZ2ymlUr5GqEq6XXhMEDP/6GgRWIo2BCEiOlpipLyKfOCW\r\nvA4YihgqBlO10xPARVY8ho7aW+IyOxGY3i9423cdXJIQdBViHxgmt0yUP5gx\r\nnObtuzsdm5G/qU2T2A4FGihFYm1WqSKZw/dEFGn/LkVS/WmOsAtjDuSbKEZ1\r\nKXZwEURd+AC9bY5CMKFQDuGMcAKdAaZ6T525uO0hD3feTVOVSkn11wHALA6j\r\n7exPJsd7pzTXt9EOlGB72kkgI7yXKwadV4DiFXvR4MBomU7ULVAy7ThH69fG\r\nM9Ywv58oz12RSA5BMLXQWeKOVl3Fb4dtV/E=\r\n=fBM9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "6410ef32f59e4842121ca13eefacdf0b3da8533c", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.1.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.0_1652717591716_0.7625770559447562", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "minimatch", "version": "5.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "6c9dffcf9927ff2a31e74b5af11adf8b9604b022", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.1.tgz", "fileCount": 5, "integrity": "sha512-362NP+zlprccbEt/SkxKfRMHnNY85V74mVnpUpNyr3F35covl09Kec7/sEFLt3RA4oXmewtoaanoIf67SE5Y5g==", "signatures": [{"sig": "MEUCIQCiQSitNr/7uI1kFENWZX0vcKe9UDUBoC+DVUbD2yu/yQIgV/AO6JaHWOn0hQn0Lk/Gxa4gy7PHShmOA4oeIuLoBFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhmywACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPtQ/+LM+Kr1W84r9tv5SH0DziKuKHUYBoeJVs2f6qTuDoXcX8udLu\r\nP+DudiKurMPX8lvz008MEE+LzXlMWn+BjLVyiJU1LY9bJxiQGZfro2NDwIpD\r\nJ8facuYIn+I2/Gdf3y/AsT7hQOqE4Hing3uJ/PtSxuhI2zVsEGMV+AIYBXGi\r\nR3ygNF+Vo1kI8ZJVobReECn69zK4+RymFV4bKz0vv0hLvtsFPBx1JnniD2v3\r\npL3WsAdSyZSb3+frr+rSpWBshRX0Xh2PdH8Mt2RpF6cbA7S7bRqZaiz/LOab\r\nNuIWdGgPKqZG9a23s4U+5QNDCtvM1rgxyxa0hvEZbwl2piV9QL1QpGyIVH7K\r\nYizwIMtwFvH8nW3hhTD/75qMJhHrv4crm/769QGqtM9n07qLqOuhetF/NL9b\r\nC15zCMpjju4vXyFwOsx9bBpKQ3zuJ7lWWGF66wD3XWAm0SqkJGN7DB6bgqGr\r\nTqsCUdXDAHjdT2hG7XtYhQEI9Vw/if3HKkjJKkaRmjDVMiFLOnnThXFsrb4K\r\nG8uTUggazR8Nwjik/Gom5jq3Cd6iCEbFkXS2d9rK/xPhB0O82xrXHyojEnAx\r\nlWsTRbZ2BEfbguZ6LKgFwX+tMWWwG+KA6SwKStRysiZZrBDRVTWmg7XajKzs\r\nB5bg1ZkzlxuM2XPyurpxespGH0RN7CpANws=\r\n=C0Ma\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "493a42f9412a7d46c0c977c51f8e202caf417269", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.1.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.1_1669754032689_0.6542352854745834", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "minimatch", "version": "5.1.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "0939d7d6f0898acbd1508abe534d1929368a8fff", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.2.tgz", "fileCount": 5, "integrity": "sha512-bNH9mmM9qsJ2X4r2Nat1B//1dJVcn3+iBLa3IgqJ7EbGaDNepL9QSHOxN4ng33s52VMMhhIfgCYDk3C4ZmlDAg==", "signatures": [{"sig": "MEUCIQD3Ec9ljPRKPyC3kiypGbZBKui8o9w3B5Xct1ois/RzfgIgLILSPW00ASWsayyp/dhi8hZ3chuSZFtbrfo4FSRz/0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjodDNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnWRAAlD7Vpt007BzI12H1M0EdWh+AvfIRI47HcFuniBcoFOvc2BMr\r\necqJTYbGGOH6Qtt7s+fQ3j3njIWdl84OWqJjaYb3eyTuWdPv7u4sDkqgzoH8\r\n0fdWW7MsTf6Kk8c77DjaPMWJnsGIWBHd6KrNp9utVtm0Aa+uYUAEEhVWcEE0\r\nMkTSmfWxEdEyf1QpwRKVvfV/qQJ6MGs45iivtVydVDH4cgoCuHPnjEoLgcYK\r\nszAW3hOG543FnWjzbfn0uiTA9P10Y6f8R1klQChO7MtQ9pXy/vfE2d/izXvH\r\naeZo1svYWAxlCmG/ObYpMib8v68hm2XqETRPDtJqoXYtc21wA9YHUn9nm345\r\nR/r4LvHgECAH/hejIEwmqITlVnJDpVxlfbejyiwsKXoHqCOy1c0OwpWOQHUG\r\nWWL+aRd9DSmUymICjlQhaWqUKc24GSO1V198MNJgnKbfGKedIo80bs7vXuMQ\r\nGynWc9bbSKLJdjGwzoTOgH4KtvL2puh461OsqzEhIV0agrpMI2HfDrwoaoIA\r\n4js5+k/u9q8t/HJitgWZLwQVqXInI8NFQc6znutfjob20oiPO6xt63ekUDhI\r\nS0xRChCSwoLf/nLRjYkyK+AXuKZch7IaCmdBDQy+PZdSO4g1F2iOreW18pzI\r\nHTPwoRttMK1W33b65bY/DDtferpEtTrew78=\r\n=GQJt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "baa35781c7b4a121543996eb9e89dba4d9e9953b", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.1.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.2_1671549133721_0.9672506961819634", "host": "s3://npm-registry-packages"}}, "5.1.3": {"name": "minimatch", "version": "5.1.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "10ae385b8cb023b634156a145fb365331b9aee5d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.3.tgz", "fileCount": 5, "integrity": "sha512-yR0LpRkZBFmItN6as1GnWLpkKQNb8bbOWqb1ndXXnCtk97W/DM+GWc25WmTmOaP6/Mk4L4pI082ukXkYwOrmUA==", "signatures": [{"sig": "MEQCIHgT5p1IrGDLkUH4BPinPmiQPMGWNZLww4FNKv8OOEHvAiAxyHgikMe0gltNmuQhmHk+XtX/rOPh0MLz1MIWXMlYJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwvpfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDkg//RR66RECu+rUasyKZv7pN4Q/Y5MLcjZOHH7/BcS/CxCX0+Tuv\r\nvgK/z8MwfvyY+WKMcw00tx8cpOg14u3E/JaytAmrHY2l7XvtEWz62c4FdzMr\r\nCyUZL8LvB5bR09Xd/ZcQw3P42qBbJ/yejHDNzImtkddcXbDdVyRvznKojTqh\r\nmlXl8SPoQ12r6QDaRzXbICko7XnOq4XR8t+cgL0tTaaS2/6iqqmKDddc8c8x\r\nOFVBSYmLxF/9mGuKOpbrkfvZ6XBRCnMTRkbW25edqYaYcB/TRS+Lii+Krrdw\r\nyUNLGmX3e685fI30PFeJ4BTeK5Qd7zGRkhh7yCJHVBmwBoizfUavT7xhesWV\r\nj6hN0SDnftTKjci5klcABA3djhAol2Msx99W0HQX8xaJarXC2G/ekQt5VM9x\r\nUFJYRZBqA287x+HYuIQLLvpRKgCw4m6OEaanoxTpIpFeBovHeU0CmSWBsBDA\r\np1oHIuYdkPwqxeIf+EkAQQhqRIaEiUTMcX4T43dYZQhKUN4bjCKzYAKduuTY\r\nt4jwrl9xe19hV6XVWrf4cuqOrpcM1JkSOGNiZc69I9mec4S1ALzVzcah4uTH\r\no7zG6Gvrc9Xr4mNizSsFn+qEw++LM6MVzFD6+gjAtU4DTCm48kRri4DRc1/D\r\nRgvKU/OI+AXtqLu48r5agWJI0DRKzfhpqUs=\r\n=kJw5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "9b42a9c252502016f7a318a28e3bcf9f325893f6", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.3_1673722463227_0.0543677901522126", "host": "s3://npm-registry-packages"}}, "5.1.4": {"name": "minimatch", "version": "5.1.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "4e2d39d872684e97b309a9104251c3f1aa4e9d1c", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.4.tgz", "fileCount": 5, "integrity": "sha512-U0iNYXt9wALljzfnGkhFSy5sAC6/SCR3JrHrlsdJz4kF8MvhTRQNiC59iUi1iqsitV7abrNAJWElVL9pdnoUgw==", "signatures": [{"sig": "MEYCIQCwZf4A13X7thWAHPVz25pR3cf5wjShqg867g4qvNM3SQIhAPEPDwv5bWn86QENNjx3Lr65NRAX0woIq0ux2vXE/cfP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwv3zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqebRAAhEzcJKI4XZgJZ8hdLjc2lxGfI2Cp2reEgrWRHtjATJz6tBe/\r\nk32t1pAdBxI2vqbgYb3ob9Z8bIJpkUJOjh1ekSpjrQiRwdb0Uq4r14VOj+M3\r\nA+nLsNsOFpTz9q+SEoVW5DAoufiNr5FatshK6NVNekzYWRqxbavNenwxsVsA\r\ndkgaFSO9q26klW5GftJ2zcqfaXY7vS/riWKiNrmknRtKU1CXupTmkz+ia9NZ\r\n5rYv7HYjL5g0HUgK7zcBznsWPflIWB0v6o1j2krBRtZwF1oXMXVou+TLL+7t\r\n0zYTIzGvZQ/4IriFEgG3PF5QnLN0LK3v+FGqRVai8hDtOO1QAxvr/XqccfLL\r\nRzOiqL7hOPCYL3dpuIvfzumAAZt81NqUSlaRwE8i2mnkNBk2oEaBqBbHL+/h\r\n0SEoWXz0KOnq+qeDa6zYzzjkLnrXNpXiCLiQEclHYNFoNhJrwecZBEHDNBQc\r\nyeCmbeYeENi6MHNdTP7Z5enA1SX7TJjCwzbYw/XudZ2EJDgGYWZnuMcytwZ2\r\n9x0d9TVt2sih37/dWDUAtqQtMgql6MRTppwvPgo4j0kge6Tg+V8nzMfSoKPL\r\n0bW3KwDb0ooCW2ciP+fvlGYyjIwJKkANJqkxGXozlkBBMpup+eUp8B+0l3lY\r\nVrCjPweX+iI3EP1oIgLqF0NR3tlKbVNw09k=\r\n=YU4r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "857a631ae19537b01d440aa2b4f77a6f07ab3eed", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.4_1673723379203_0.10104771982469174", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "minimatch", "version": "6.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "e00d2875b7ae572f9e699e674fdfdab587aa1fdd", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.0.0.tgz", "fileCount": 11, "integrity": "sha512-6c/YhQ/ZnMCtAGMJTuPQlk1Q6ig41qn1laC1H/AU6dIIgurKxxfh0OEALh9V4+vPcy2VC670D46TOGc8dirt6w==", "signatures": [{"sig": "MEUCICizfJvXoYBDSMcGaGVUS2CefsaWcjwin+MGPZ8p0KpDAiEA2NMxIgaaR+pFi39mGy08ERwtXGPkTJeVYPlpjU0AHGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwxl2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9Bw/7BYke0jpMhqRwEgRerjU7xGDvDTG1s/29AIImToQBhX20uP0o\r\nVERg2A953FA+Fi/SG76ngG2fAEL7f3n1SU27M3mG6ofv0pBwPz+AksFBEFCx\r\nejZI7/ozekxuyq4CcLSkLLVJ0izRWxsDoM3FXo9sbZVo/euEmx+CFa77HdtT\r\nQuMxekC3avkWE6f8DWsUdO7ffQrtfir4rbbQjB62hXju+FoqMksSJUpGjD6a\r\ntDrLkQB/KyVBPhEwFMFcyyLPTO/xFfXEZhIXbaCiKe76y5RR8Rz+bCP6LIJR\r\nosZFQDuXHaRu2BsB7J3k23OcRmElbnEsNG0eztuHg53yrF1TXFxxRCCv3LJv\r\n4EB2CtZYPU0XD4/kwmerBd1H0r83VTdonz4wTvOjxIopxv9/jIP9IhtqOxIa\r\nJrNJMwWEEAR3FmzgDL52X0bMMZ8Yz7FParj9oIw6lMzzHJ4cVKXyEE982lLn\r\n91W+MfMNg4RL1N1gUMpYvXZgMItra2+jOp1miD6D4EU19tiurypKE7sDjG5E\r\n20h6pMu7pbueUH3c+muOeFY28k1Um+brvuVvj0mt/MczrQt2O+vFM8spaHdd\r\np/FBQP9lUUb0fPK3YPhW6upDpDtbOx+tHe8BGM62PcH64AM22lO+r/HGnk5m\r\nxYs/RyYPVtDf/uWpE/6K3v2255dpvGs1uXc=\r\n=dONy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/mjs/index.js", "require": "./dist/cjs/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "31f61eeaa6c4493f81bf5eb0a146f23e538fdfd7", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.0.0_1673730422676_0.9559884162297128", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "minimatch", "version": "6.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "3b098e1b2e152b325d00be05080a3a4a31b2a53b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.0.1.tgz", "fileCount": 11, "integrity": "sha512-xmOf/NJae19NcLRIC3LQYGtEuHhbmLFTL/B6fmpD3+mkB3fmAt4NmiqBMVzq6ffycJqazfitJ5bQan3yzUDLCw==", "signatures": [{"sig": "MEUCIQDu3pAG4MKa9OOKo488YJv9qNK63MroUqK0OpfpexcjdQIgM0yu2doQ5jZcskB8Ns8qFgZYTYVI9APF8QE/46p3yxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxDnlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXyRAAiaggNUc375auQkBNsBdh/pN6HXU3g+9Menfw4fNsMchJeIjm\r\n6lw0VcLxEs14PCwG5BFlvu0L7y1d5iPRrbbgM3uUhd3qOImGAGc07HVazBov\r\nR2kJfV44z1adxIJW2xeCu9nNmKGh63ZqrqHAvd7FgNiRs7Cq4n7SupIsOvgv\r\nEYpibICS5Uv8vJPn2k7UUnXLZc6UkMjt6uxHZAaRgrYcIzBXcsi2I2Sd6zsG\r\n4mwFb6b4WLAiWyYYaK+8Uts0UE0r89I0JJXQ21V1Mq+nRSYcc1ZxH+eQRqXP\r\nbQz3OVhnOqw0+3N8jmARpJhWXV9wR2511pDc1FzrywunGyZerrR4531BllRf\r\n3UG38laaGwqBwkXBim0jAR7nXNZMe3qnLlfrC+ogphCJBK3k5eqRo3PpR6cz\r\n8M0jAZsxtVSJhthxWzEnspP2tu9O7E5h6hzgAlEGwDWDM6lwK4WQEU8nVL3v\r\neODcfU3cacV6+NMJPdqFy1/SAaomtJgXdpVoadjA8Qrnxlxxuc43+hF9dclo\r\nS4n+kcQN+etrT4xFWXygd3+w2BkSsRk0nDGpKRr9YolI/QkEspBmaVTI4jSw\r\ndZfEdCGz2DC8eUxSROhczyFCQJ29G/mEuXdHeU8tcWpCf6WVQt0DxWJGKUxz\r\nFW42HlOI8qdUYnwfERVs7AlsWRkoZgIzPzU=\r\n=1qEI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/mjs/index.js", "require": "./dist/cjs/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "8910265b5a1c368d519f63d5264cc743db95628c", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.0.1_1673804261217_0.8802099473212495", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "minimatch", "version": "6.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "769d72a5988f266b201676d8c47eb65b76b92ca1", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.0.2.tgz", "fileCount": 11, "integrity": "sha512-grITTAm/pe4x0n/9/KKLhIWuMb+zqdK7QkFWa4/E2Xp98yyCOmYqm+CP/3dnd5v9Km1s2OYeSvsjSUPN2/oS7g==", "signatures": [{"sig": "MEQCIHIk+HE35dRvOkpb+bWBouilXuou4Y1uZ26c95toOiIvAiBIz5irQpA6at/Boh5NKB0kDrEUVrKkEHtpXb9djZu1rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxG9zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9zRAAlZrFUWowg+PCpQIh7ROh8dHcK9f/WjubPghn817F4NAMCzud\r\noH6VRFOCs5Svtfi/zLuoAVHqJYYinM92usetG5uLF1luAp39zQiO5p5q+q0G\r\n7bk1WyIK06H8Wlrrkw768Zk6KD8pWMlj6ElJdSlwjyZ0kzCFqQkvRWhz53y3\r\nxwdGKDsJaNdM4ba4251oDLdYYaj5CqKsbaJ2+BiVGpUb5aGgkI506+Met+YS\r\n6veGvVE5e+TKbnVItyOX01NNRD8TJRnezHELMc/rHglL+98HPg7Lccb0fzDV\r\nexFapQ2UOatuqT8NixTPca0PoWxN5KvqyuFTS7n3mZ6sOQREJJ4+2gYoXSqL\r\nh3JWCbusVXggtjeIcjrSW7NMCHVOIzE8ol7l+xtWKzAGUYuuBOSk4TFPj+/7\r\nAFCVxPldgxU/jCrNLl0Z3KHV5HS2ljQgaEYOlGtzranr7G+0z8VCdXHSzgpk\r\nHr32tH76IY/np5a7ocoEaOivKfz8hKZAqeVjl+SwmKEOhIVWw+eatCuxof/m\r\n6pl/qcRoyBZCpEaWFp5g+JPOuEIqwRUEFpRWnFSTi2RRzzaS/ldLXkirIwAi\r\nbFpXS7kAq6+6sIg70Lmge1SEn5mx6qgGs9gucFnO3eGyr5INw/jSPyZT32uX\r\nQ+rFfQV7wgb4P02DjYZfNBfWrcxOhK+8FhY=\r\n=QBTe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "de4052acda8476cc96f552ae3569e3639ef3ff27", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.0.2_1673817971486_0.6356428605867932", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "minimatch", "version": "6.0.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "9859768163dde0e998635f3a89068e96144a51bd", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.0.3.tgz", "fileCount": 11, "integrity": "sha512-MEMLwGVOlRN2AT3u2YEuC101XLzw8kxrYe3sfGLeSoNe0rK3gQAchxsMxlVqFhmkIX/lwFiQSnANfydPOWCR5g==", "signatures": [{"sig": "MEQCIHHHlLXvNWoiEJS9z4LFmxnbNjlzTkSoGXJ89Ot1W9j0AiATKjQKSSnun6zi/nvXr+iJ6jUvn1Uj7WGnucbwGBzxUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxIdSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgUQ/+MvcPBbBp2D8aRoMNV1KF7zwUZWu79HV4Q0PrHHIQMJIkLc8n\r\nYshNms/3jPTg9dm+uX2VGlm4x6CrzQz0u9qPkdG1hKaTWxbhtGv2DkNVS2nM\r\ntcUq7UsSE3SOLWpBBnVs7dF0eMs4dBJyjhaXx6AEsnAbXgjD6JLbMUdrATau\r\nr+GGsCvAdKDdJlB+VhgIp1eq9Zwc/6r1QwDRXagRU88QuLLu3DZXHOo6clrS\r\nGBu2rSRES7htPsaDt8paeGNy/nXcB+l6srvfMjqqCRZtr7gSzoG5bP2Aw8XN\r\nlbynWg+J3TlNyHgMmlDNLHCaaUOE2xHtHpxPq0BSUaaffPK9kjxxQsQvLS9K\r\nLew1A4uCDVhLLIIGEV01DKQIp+n9h/F5XIO7BcNP8mqfT+C0nbLFgUZ0WEQ3\r\n2sekZQi0OttVnP8Q263EmfGQh6rfkCOIP+NpjLIpsGbGmwmVFrgxchjpBPDG\r\nVb51Z1TFzA3vF+WhyzdxKaLT9dh56cUWAagmQUgLg6vVyUIwabYMknspLe6H\r\nOHt6s30ru6tfYDa3ksNj8U6mfNdWKadHzE4eVrndCfK1YoO3nMN0NZu83GkI\r\nNMRYIiujN6/2ADOv7PFKoCD2U1yhYQyKHoJ2ZPJYx7n4wu3czaJs0vIO7Q0n\r\npMzRai/3QU09GpDgfubbMDtS90a08bryvQ0=\r\n=zJ+0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "6c0c46421bd0175b546cbfb819f4ce72bebdfbc1", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.0.3_1673824082277_0.210773342275409", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "minimatch", "version": "6.0.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "cbada37326e86dc19434874a04e29df0ba64cb17", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.0.4.tgz", "fileCount": 11, "integrity": "sha512-9SQupyyavjdAc1VFjJS/5kdtFtlLAhKSWt7HocG0h/npy626jYrGegSslcM7Xxet5z0U9GOx9YbcpyIjBzn7tA==", "signatures": [{"sig": "MEUCIQDKzu5Lm1nkp2AJgpi3eTJcxXeSu2QDM7vdW6GFjMsCmAIgTtz+CVC1a+821YJqPppqHXN/EYUjMNfjzEDJtITAWQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxK51ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpychAAn5DaALi0JryCkSUNfF+aqMJvd7C5UYh4pMNth3iWISpkAaoA\r\nCwpI4ubdUYtEFaBguy6ytnOPNJ4A6RQBBQ+oekARawraeMg9FcdGVe1ZCaBi\r\nLyfM9KWBf6MlyzYfpN87hNqiI86wndImf0l6SJWnNKgr6nrfy3dDzCWvk32T\r\n8hCUBOn0MifiSMrAtZfQ4lqpkaDY9MEXUvl5nXNBW+QFhUjV6QijVmSEnuny\r\ngZLqiD0wfnggtKX95beKidu7LBWS6FJNSwmRvTPF+SK9egK3Vj4ok4vcS5/5\r\n7U560+GJYhZ9zINMKllOGSPpI5R66W3Yf4moCeBtYnboN2rUA4+Swvr+4cmZ\r\nju/QSY7+VJLpq1ypE1mChfmR43KsTSAGjEvHGg1uP1Eqfah5wIHqwXrb4xLk\r\nXsaM2SgeLZ7kSPPVJqxDuezPn40gFSPhGezeZpGWn4QOXd6TOavcPoy/3/s1\r\nLPXuRHIAqdYITXkF+YoK0jGkSkp8Sv0FZnyVFNjvZxYUOfMkb38RUAdjK+mT\r\nvbz1uNSjMscsOZbhD1I1sfEEeAGLqXQBb/ppu6oS2nQQMxaehO43H0g/B7wx\r\nWryduV/+AJEahYelOLiJdz7OD4ICadz7X9ZkrttrLZSgtxSCW9fAOHEp48DV\r\nR3TbgTx1jzWL/b5zkgR6ScBORHGRSW8tJTY=\r\n=LHCi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "0f4727498887820a72bade310a2472b9e0f01e0f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.0.4_1673834101595_0.2678741342289297", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "minimatch", "version": "6.1.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "e0eafd0a19977f5accebd4b3909a34f6611d4288", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.0.tgz", "fileCount": 11, "integrity": "sha512-eqe4xaKs1/JmNylXNFY2f41n3jNZAZTZlmOitWd71YazZlvvXMtzL+gK67jRKhrTQmHfrCbErYWV8z9Nz4aNuQ==", "signatures": [{"sig": "MEQCIAtc0m94ajC5hFAjl1GOKxfsj38ljN/0RLF5btlDV+bgAiAgW2vnJzI/KbEcWfcY+lGC3VQiexMa4DJEZePFXutPZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkoKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwRxAAm1qIzgH86Wy7CK5uHrQVO7qdrvIJjykGCpUfTWA3SHiE/+Ud\r\nHD8vBe9DVYHmecE+RkYYqZ5/ms7n7yKLIbf2QemchIvUoepBWeFiJIaPpN/O\r\n6x/PVsDOMReu7y0CgrByqLyl2Ua7BuPnFkd3o+W4YfBD/9lZVeGv9mb9HWl6\r\nkYownTP9vT9LDBoetmyfMXvGR1Y7Wh68Eb18L2qzxQRTp12Lo66ustnZ6h3+\r\nMJ5vH8QE9w8nw47q5ibP16YwifLM+TjYnhT7NbTZSNjS6ginK1VcpocJgNNu\r\nN+VSX37xMV+6ZC/kiWQk3j+BmEr/DCo1vMNCdxjoAKuOqElfqUjg/YB6fLBL\r\nSLCxrT7PPfEHJHKoW9fDEH8m15X2QwLFpQXVa8wtx2Dpyjc2/QqwYarXToew\r\n12maQN17I4sSzG5/QbIqKcQSkCJzjBKfsJKiXniQ3Msi99xxhkUz57xZaIGs\r\nD2n0WVhJwrewQiS9jVHvw+gRfozW5q5qcJrH2WILE/WVDCQ6QNdw3x3UMNyG\r\ncFpUIp4NNYL1zRlWxNVDeUvHQKAHDDteVphgkZvzFZCsMNXboLt3oXwuRc97\r\nx5/5MNGwFQLkNhuiJhxTuxFC8pbT3kCM3zaAjnAY+FgsrsETYgEnBU+3BMAN\r\nzsh+O2k3s9qgziA2qpn2TZPRgRAqcrCSm0g=\r\n=ouF5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "0e7b19e667c0aac25271e3a55992fc3f8692d1cc", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.0_1673939466104_0.014389871239345986", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "minimatch", "version": "6.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "79b77e6296ea6fefa1c83d86c7e667d2d4bb3cb2", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.1.tgz", "fileCount": 11, "integrity": "sha512-cNBGP54dXiAnbZWHrEh9nSHXNX5vvX+E3N9sF6F8vLYujV8TiCjDv0qSStGUlZEJEyaclfVh0qxok5PQFx+kTA==", "signatures": [{"sig": "MEYCIQCe/wY30MutOwgoEMhwz4HsQiAXAOr8BkEiLkBuwLWMMQIhANfZpzeQjisHvDzS+hKZqQluSqdjE+5+V0Om0uMfks26", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxrdFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovEA//b1nPvbSCrfGn5TJj+mHGQmxN54/75HJddGvNVgrLKSkg/ym8\r\nk/yb2uKyc4tQF31fHt2elDvoPnwfZ+p/zSB3moI+8ID/qlHszOct2fhCJb7L\r\nVN3byvM1Ia2PN41AtVf/ftJdbP0H0CfxuIS06iB5oMMUalJEyUjDRejCJGBb\r\ncs61ZBlrWttblKydXwV8s4MWc69oHk8b0dBtA/JlXg7nhGB6iPc9LtLnB5uC\r\nV6B92rdWTv3MmQZvdsR1TNSD/fAIMGQXagszhru6MTWClffkOpynGB1aCPqz\r\nBOePSeIggmjZpLzm413+qdXopiq8CVKp8iw/b2YrrPL0CPIAGhoKmc79Zhyj\r\nOueiekMS5VR1atScRATJd11QMwqU1nnwto3BC2vMBqy+OOK73jiLV4CRCPzi\r\ndpkcHIbdgxbntyf0lBR2KqL+h150qn5sak4QvRIa5OZbTqT5vuEa56x6/c91\r\nhEucnJOGC8iXtmy8ULkHMbTooayOX06dyNGjudCe+dAPtPPWWymmKriPBiwV\r\nhGT3gLIH3Je8ygaCR+FbkTt3arspVX50Y6QjsVE/yOVH2CmgCSORfgMxPIrY\r\nAbi0fMJl0udo7WpUdQulIdXSJnBJ9HX3HddN38cjOn+O2atH7xhJ9xjZtYol\r\nEkTWipJ3h09468D8Kp1VISH1vbV09cWOhqY=\r\n=gHfG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "c80b476c37644e5420e81a01433c00ae0d9ca4ee", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.1_1673967429051_0.5208000526416932", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "minimatch", "version": "6.1.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "f09f2db04a72f7a57a3bc42a7d1a22c8c89270da", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.2.tgz", "fileCount": 14, "integrity": "sha512-5sS5vZsmC5o2bfPV1n2mpmYuOvrfVVY45fi0z+rfKPwx2aXrZF1XeCbSbWWENAi90YDK7bM3TRo74BB9h2zBgQ==", "signatures": [{"sig": "MEUCICz/4BNxYohwVLqwd0w2DvpUcMEFkdqftoK5iTGLNhKaAiEA1QymwUWEznkJZSE9AH4FLhyymoH/cMBJY7xrUdw1N4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxriNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEzhAAmk1pKI2NmBz0Doon2ARMxh35IYXxcuOb67ZY1f+ED70v6OBv\r\nMEd7iMzmtzNsmTHSe7STnTymMwfKHFKRBRfjvpM63ddKL01TNqzDohk9jF+D\r\n2hakXTV0qQzFAE9cpORGwRNKahGxlgm6En86dqcWS6wV6aGkK+99kyVLABjW\r\n+qRL9j9UOi/1m5kdL0Bc5xene4iD8lRZX8/U3nzsiqMepwKzO2H1b7vsRJG8\r\nZEQlPE3C6rrsDNQHOlHH2jcEvcjq5Hfpfze/uHaJeW993X5l/DUjOELUODcO\r\nZ4CIW6ctRmUcSOjlkq+FkQescyyuTvRWV/6cPgkVrjcGqggGkoF7I8znJyPP\r\nVSmj2qQDSFvRlPzPEufIufCpZ6Kig1MedhLyHEE4rSKeU0CED2VfxDzTwiAn\r\nwvLDR1qViMB5rFFF2dGjDkq9zNFPqM2MeHaxP0s/GvGScHTBeXjbyqlqjCES\r\nNPPc1wmZgTOfZlK2J5rUhfx0Wl6PC9d1TLg4x7X8lUcnejlUMeC4Lv9dyHem\r\now89UbfWVyZfm0PuNirDDZ3LmD729B0DvR6me1HKCvyOr5LwIRGQnsDa1oLF\r\nKX+7rA631gCDFV+b9hOf7o92uiX5Rr6UT7ZMLUc3ZZi4n4wlaOSVGnrAWozP\r\ntKFl6rWNNE0KLXoe4I3rKx95AK5GenQitcI=\r\n=u4eC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "58c673594a360ff686732f032aa1211a4c09962a", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.2_1673967756909_0.3468366850312836", "host": "s3://npm-registry-packages"}}, "5.1.5": {"name": "minimatch", "version": "5.1.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "cabd894be2c2091e01fd09211326dfd996ee022d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.5.tgz", "fileCount": 5, "integrity": "sha512-CI8wwdrll4ehjPAqs8TL8lBPyNnpZlQI02Wn8C1weNz/QbUbjh3OMxgMKSnvqfKFdLlks3EzHB9tO0BqGc3phQ==", "signatures": [{"sig": "MEUCIQCHJSQL0WRrIdK5sNSq44O+RZFHnK4Rf8QSNlnK0ZTUDQIgZCbuh3utfERZ0JGU1hLTzs2D+5tTlK7XU25UZZCz/Ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxrjrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZvxAAnWbr1r/+nLZEAoJpnyEWSVwqhxlORxKU7dLDqu+K91VqgGpx\r\nJUPgn0m0ko6yWGdbr9vYF8vHmB2ICl+Fa7yqFJzzoT2i5vYAFDCw48/BMm6G\r\n3NsWxMSlwr1KhhkyMvee/ERNmlOcZ8hV15UIuHGtEiA9JzfwHO38Gi2/fhXJ\r\nnbjlY9u5d6AOXvbWLfnXjmvlB7tg3jVuGfpup150pmMGVN2qXyFiyC5H/FzW\r\nKbLSGas2qvwoMQ7oQN9dwSsNoWZmryXd+LugCtkaj5uCVIZk3c2HxkNQkhLh\r\noSFnYheIOtjDuc+SE8sJHUrqghc6BF6M5EJG5tHAwUwZFxZV37/xnwHlhTru\r\nept2uDjmRo6Vec+UB/eNkiuWKJy3p12ZjNZwbHN5oFA6TCST0ktrWmJHXdGk\r\n1aq/77K2yHpKX0ei2dQgads0UP/Kzi67swDF2q8hJyHMBJ13uzHh9rp0L6AL\r\nxZCtemHAX4CLpMe7XY+OFLeXKTTQ4iDFhicm9+Tg5X63PY3++K9ZmlfbKHjm\r\n9GgQ98vAoYLwWwvnTOQCcQUJltpHXkGTKfWAe+EyOABjPzp4xoWg1UQAj5wc\r\nwAd1CO+eh9L9sYeTPUBLVOTCNei2lpuKOej0cDrKV5WFn8lqGxk9iteGMHB7\r\nSnAftyn8PkuYL3nVA3WrE0l5ZShsp7id67o=\r\n=Ddjw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "9556826e34e25112efab082ccf7db62de9343f5f", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "publishConfig": {"tag": "legacy-v5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.5_1673967850924_0.4526126725098829", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "minimatch", "version": "4.2.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "2d2c05fd94ee370ed49275dcbb9a3d050c342795", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.2.2.tgz", "fileCount": 4, "integrity": "sha512-irqDyJjDrUjbx9QWe5giqW3tltdpUk263Qiw4BVMLv+uzQR0/udYN5h08kI14npUPRXIpbSWXFC9p2Z7K9ZlXw==", "signatures": [{"sig": "MEUCIQCZwm/SZ8C2ZxyIhupuBSh6nyEJF8UFSErBysix7METXwIgedaWR7We3yhpe0wA56mz5Dw7Eg5ywhdI4C1vZg2u0NU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxroaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8Hw//Xr2EAx8sfk2Av9cn0daiNWIHwn4buFmXjAx2eFTkmaUtaPjf\r\nkpCKUc8qnu+sOfwkMzuX/EhfYSuYSeUAok40z6QksJa7ASPj+e4o8H95IR0o\r\nj6vMFILqmJYchPdmXkxSJhfqqyyHTgokp0ZNyDIsC8YHyKvkVUVP4pDT6FgA\r\n9fO53FW+3NXPeQHhBYSXsIXp/ZSFkuFYoCcH9SMFfzRM3lw/e/4a0X77iu8R\r\n6InrQTgH8rmXQ5xHwDZiFyrk7yhaRCCcvJYJDAeJx3QCZR5t/KPxATGJV7Kw\r\neGKllhIHAuJaYgceKvKqmhhI1ZdIvGrIaU2QMzh3/5C0sQS81dzvpIwMRWE1\r\n0nF5Iwe8dukX5EbwNBKtdLKV04MNy/z0g/UgiZKZ97Tz1T9gQZ7LND5DqMry\r\nDnMOcHYx3QVhCumXtEsFPLRdO7W3jWCPxCzY9shpQhA1QPwajOB415N60mEs\r\nVsX9Qqn44YRJCFiu2ktGnpK64/sWodWNAnRv6ldW8ACjh+JeUydPM6SaI7BT\r\nl0SOEL2AELg4QF1VPez3Z/B6McN7e9DplTkGI6FLDYOveQvroQo3QfebEhkm\r\nATxOcRcK0kmu5F9H6Xqqh8bq7gKDmkb6t/cqd3f2R2bzIjz7EdF6cNwQod7o\r\nadS+pI5EEtV5O464W+a1YD/9ETW0J1677Yo=\r\n=dr+J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "465c25e349a3b85bf376e6241bf478a7742ca486", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "legacy-v4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.2.2_1673968154122_0.06961205614674726", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "minimatch", "version": "6.1.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "edfdcf136a4a495a31cf427e5344505b3640a5d7", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.3.tgz", "fileCount": 14, "integrity": "sha512-hTKbHAihZdzpD4K9mhG0dqv+j+D7kAHlPfEb7BQA712xP9afhS3x5pyoYrGqZYJd5PpawOp5V20rkFki5jDZKQ==", "signatures": [{"sig": "MEUCIQDp3EbVAWBab1gESRE7RPYCWmhqfHI8T3KdcGo9nC3GLAIgEgQBsSOzex9I76UNXdQG/iNv3McnbQomWDRVqwDM+M8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxtm7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwURAAnhoGOalHjR6LhwnfsY3Hh5Ov+y724l5aUxfIfpHKQA4oi5hQ\r\nTX5MxgAcl5s4mg5Kr/cdUee4j21Auq8ltYGBBiCXHvrHwTAdh+Uws7wewKb2\r\nof4jrl9652qY19jH+BSbsWIVYh8tBiilR88dDdJwGwsn8onQ3hwK6sQGfIdT\r\n/CYODedLiD+Nws7hrzljAMQGZ6wjWmX/IPbkuUx6Fib03tsql19nzoinq8FL\r\n0ZJrSZLyUTzXvBuGREjIc6UPVXRqggks+QvvrQctXyc9qR+jIrZvmfQqp4nG\r\nYrqrJ7OW9fE/xf/F8WM1XDoVx8C4MlN2pLTXnSlA5cC5pD+POiKfx4AQj0y6\r\nd2WvHO36WzJhV1jtxDFnnOcQGnUuCOJOEL0yuFaSQRMdf3xgL0gHEsY3YY0I\r\nArIdfj1aQ6Jrz9xMA0dKU0TLA3r5xZTRRXEJPdDrFO9w6xWFPSlNaRMAHvIc\r\nZgE4CLqInU9BYuwCIFyU+ztbs7R2tc0jufnTm7+dTCfdd+0pWJ0irPqAhKvw\r\n8dzroIpNap36pcAK7eDbNYZC/uDugfxm1vx8U/PKtUwcufA4ms1Y5lv17Utx\r\noWn0GGyz0ddOzoX0gEBQEi73TXARXXHPo3HBPHbnwX1xmgSb7GBDuHxE0wcM\r\n+HhpbNpW0ZfAY9+eJ4VXh20FJ/R7+JC1Y6I=\r\n=V7fJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "9ddf1df5d987988378a9e557a26f96011d673b8a", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.3_1673976251473_0.37049608623819186", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "minimatch", "version": "6.1.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "f34f10bd5f67d7b1aa0032931f2dac64adb51c81", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.4.tgz", "fileCount": 14, "integrity": "sha512-NsDTCCf9qKxjUUxSfhnF2AbYR9xSpWvJx/HL/4E+KbqhKqdHMXjn81So56Hga/lpyhXL98aLPZLdPmUUEyIrWA==", "signatures": [{"sig": "MEYCIQCoXOQhXdg22yl6MUy1SDfAIZ4++tWZrMPvVXi1LTzDzgIhANF1OWvdLO9oZWtUKipqg+5APRpxZ/7WOlMdwXUoApjw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxt73ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqY2Q//ROArySs+MYltfaP6gImJz8eiXkJC+tqvaajL+V+1AoCrpikD\r\nh2Ms09mkista7cNZNrwAP8vBTOzwNJTMEzUanjnXB0MHilkNFtY0lP9GH7GQ\r\nA0DJlPSexcR7jyfCfjOX4oO7uzVN7e6X9AW9o2/52o/kK5ngKOh4z8RFKiOQ\r\nJMStP3O3boh7CLsZ1DisbvgIiaBGp03OKwum1bkUMKKqjRoCUMhWyo+4oQzD\r\nOJ/BXv7k1vACWQgJP2KyTUf64ggzAxqbLB8aZZiI3XxplnnvB4CaeLtkhOhd\r\nzskE3jP07cKz11R3Yv5lIWNx5e26X9FmiJUYlVASEKGcAGJXwJQfQzftEmjz\r\nBgR9iTHVlUl+xnTuAC7xodeC3fkEKb9pqVCT9jl248lUUEuIdXoQ4fgpp0zX\r\nbXOjXBg9qj6Nf+LDQ0vBYZDqw3H1yGYfqvS25STQXKhJUB2skR1zpd1rV68w\r\njIWWacKuDBVk4z6/dRsZR3VEH5vom4TIhxLzvBZXrZaWzMT0KD3MOg//twjj\r\nY0F2rFAvsx2Oo/S413UZ+1SpxL6qUyOxsmhOwlx8QmDjUeRDCvyulWEp4US9\r\n8/ws3l2OK52jgF4wr/Gcab93YU99YR3vpgEQFA7Hpv2jn3vBAvOjWzPdANhu\r\nWCOlVNI5nCESczX7v56dgV6Xqs8YF2BOyJc=\r\n=oFmP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "040cd419177aac1c50b962e828cd967bad8a178f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.4_1673977591461_0.14965598486836384", "host": "s3://npm-registry-packages"}}, "5.1.6": {"name": "minimatch", "version": "5.1.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@5.1.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "1cfcb8cf5522ea69952cd2af95ae09477f122a96", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "fileCount": 5, "integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "signatures": [{"sig": "MEYCIQDiMjtuM8wCBI1A20eMmJjguXdJs9emsSfkyNrP9HfX9gIhAJiQHdN0U5+b5hYdykGXicc/CoiJu7mYZLpfjuHt3xel", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxvsdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlgQ/9ELWjZ+57ildeZ1IQyYJ/WYaaGCjqth+pGej5n40f/2GrBNH5\r\nEl3IbiTHuihdfb2NsxzZySieGNor7vFSL4sZEah6h3atWMvbkKYdYTEhpopi\r\nREkRpqOMcbLfrhYOwplT51iNaOutqLm7enUF53OrAf6/VgiQE6dBChJc3KDn\r\nXDtsdQiKUY9IpEpZR47VB8HSupYrH2e4p+fQKbwF2k9xuFGmWdf16V6sGFNh\r\nBqNTSYVzjSoQWF+k+9P3qlJEuAlmj3OmC/lsBrvpvmwMn6bH3dCTltt4REwo\r\nqlROVwV3gYXzPv/8FOOJK8jEWG49SxgGvgzNxu+WxGT7fJL3YyzYIQZpNzzR\r\nwFwO/zRCW4fLK52RrJ8fNelXavCcssrB7TmoqT2e0Yk3BIGkUNdSBEnFQItJ\r\nBBtA+T06Rp2u9XSUj6IObvU9tAz4s2qSSmXNv7//otXn2Cg1NESAz7lIxBA4\r\nm175qE3D1hLqobf65uXAngwVuleD896/m7ncclPQf6HRbWf9k7fA5WgRQIMT\r\nRZsJpTrxqHadZuvFItWe9hscXDuwbXZVahV3wwTgUmfXFS4hh2Eq/UK/0NTY\r\npHvQcJi024PAq+LLgI89J9k7i+TIT8YI1Vk33+hUODBAmXUSiVSSyzRyzX1h\r\n+7SmY0CxASDdlYg4YmNaInkbqQ4M40MkBG8=\r\n=Z9Zl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "3e216b9cf09528b8cbb90c5ecc01d054326d8f85", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "publishConfig": {"tag": "legacy-v5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_5.1.6_1673984797276_0.0929935685939185", "host": "s3://npm-registry-packages"}}, "4.2.3": {"name": "minimatch", "version": "4.2.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@4.2.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "b4dcece1d674dee104bb0fb833ebb85a78cbbca6", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-4.2.3.tgz", "fileCount": 4, "integrity": "sha512-lIUdtK5hdofgCTu3aT0sOaHsYR37viUuIc0rwnnDXImbwFRcumyLMeZaM0t0I/fgxS6s6JMfu0rLD1Wz9pv1ng==", "signatures": [{"sig": "MEUCIEOmujqloN0Tft0Nw57LOOuQOr0AhWETXoIiT0vqh5Z0AiEAippOmoNyvYkpXoKFwE+/q9psUsBXAmb2jm4LrBS5Z4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxvs4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqphhAAkWCMSsfK0hfQHjBLB+yU3pNpSdeVuhtNTQLm7boyAv+x47GS\r\n65ybRsVb7n6yuua0tRIU6p2xueVBQOFkTpykLk+qzP2wNfW4GhdzjlILWnlx\r\nvJUWpHGbTlAluyFnh2flhlLgwSLPZsCADoNvc4VWdbN1SsGYzrpt3wYGsc8h\r\nBuhSMjuNX5SENUYvUNlCwUPrd0AHynC0IU5ijlAgMeChuxBSk2sNqanjO/uu\r\nT1R07zkR+kUWtlOgwSlqEk6+YIwvuYh4S4j1DRX5AioOVyyCjPsR1Z8kXOQr\r\n8Z4jKeDFs5tZfITveurXqejM0+VcoP5OzW8w2NBi62cnDBQMvz/JKdXGh7a8\r\n52gciU3T+IvZSPmMJFAgJEhq1m3GMFIHw4iDr5zb11HgyHBAQTLDb+u4hfIv\r\nUdzcUYkytjPe1dQCy6jlktD+epKJSPT5XDfcLVNcInLjflTKvhXMw61f5p5E\r\n5thc7zwhmPS5c7fwNawGmYJ93dku/yIAqyKzjhxck8+qIWXprrJssXbxz/AK\r\nzvURUtKuhlyJcnSd2B2y+zVC/yZDY7AcK129n4wpqVeCGoHrxn/G0HObRGGw\r\nriM7Xv1D+OZVynHOlxgx3qe7ngea5TAZIFaNiv3KhmRMGDv9Jlb4AG/+P9Tl\r\ngMcaS9KIVw+LUHsgxzWRhRJW9ai9Zyv7sfw=\r\n=6XsP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "minimatch.js", "engines": {"node": ">=10"}, "gitHead": "b1f802285460e88460bf8e71c0707298a56c48ac", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^1.1.7"}, "publishConfig": {"tag": "legacy-v4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_4.2.3_1673984824631_0.433523385660598", "host": "s3://npm-registry-packages"}}, "6.1.5": {"name": "minimatch", "version": "6.1.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "61adaa90e59b29022fd8e326364f0336e4f9282d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.5.tgz", "fileCount": 14, "integrity": "sha512-2/WxnHMkH7qFS+pG8ibLN5GZdx5Y0aLlgFSghaKRUpkeEmC85wZRb/xDvj9jv601KdNOS2G/nNqj2h6k42yxBQ==", "signatures": [{"sig": "MEUCIQCZIJGjUut0GzgsdWp6O8N1K9w4KaaYba6YdpedpqCtWgIgbIbV9RpKppcVttyECnQGACedMQh5b7gUqjse1gxphtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxx59ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3yA//Q+auWKpDwXCEZDvAQ20Q+cMjrtbL1HMfPdXVS+CuudgNTLBl\r\nGGJsVYN+PhftSQ6Q/Iy6rMWQzIstAMHmprAaAODCaeLUfSgJMlO1gg8ek3dA\r\nT1YcN+9rfRTtkO4redTGLek+5Do134JvsKb4OLKUIM3+nNFXhvtAcy/b8XuM\r\nhgYxoILPbd4NXDOOryLpcY892A0btha1lT66uJz+CPEJss7b8m8dtywxasR8\r\nq2oEJ0aP9HwgC8neiX6dJ69/vfkrVuZOdauWXTgy8dVrXRN9HkizY3mqqR4d\r\ng+avk29soJ+TWxRJrgBNvFkVcJC4YUiu380ZQq/v8p/KjAEvLx+AX1SUYP99\r\nl2/B+V4LbCJnUq8Bg24X6LbzhAkyxI61HHkGopR+ENw7zhhcaudM6OB6qpra\r\nNLggZbEM8S/aQpY/kcIIHEp4TZOFUw7lPNdq79TO5/AQ9GxTABy9JyXgAy5F\r\nQd6FZdm2uQ+8r9aMBT6ga5depkCscQov+yM9No2swC0pN/c09csgvS3B4LwG\r\n9pIc22U43zte/snIbxs2YlqaT7ZdANkDD+W2XXU0zvVbWiplS7p4ACsz3ydr\r\n47YSJiteuQRKUSUu5tzBaZqonvVGOrwytj2e4W6SSYp4Kkd39MmGHE7MpIeI\r\n6VeoCdKmIEs3A2jdbyyo/cx3ssUieMYrLak=\r\n=SY7B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "04ab899ab7c13051c2a86ff15ce6543c5e723290", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.5_1673993852906_0.7226141523595293", "host": "s3://npm-registry-packages"}}, "6.1.6": {"name": "minimatch", "version": "6.1.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "5384bb324be5b5dae12a567c03d22908febd0ddd", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.6.tgz", "fileCount": 20, "integrity": "sha512-6bR3<PERSON>eh/DF8+p6A9Spyuy67ShOq42rOkHWi7eUe3Ua99Zo5lZfGC6lJJWkeoK4k9jQFT3Pl7czhTXimG2XheA==", "signatures": [{"sig": "MEQCIAU7+bQt5y/4dzl5JX8eUidYEAP2TbmIJLLV80NTqDPPAiAn/4whptPpB/YTtjDkdWVdfrtu+UoY6uuel36sTzo6tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzXfGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrX1A/+I7lBYgXrP6tMY9848ZT/Osbq6gMy2fsbPaFqehBQQEHfQ6O0\r\nQcMBVVkja1KhslvOXdR/V2j1FysDsvM6QwlJ1aNiVgG052wQQVJM8dxU3bZS\r\n0tm0+t16xjgx6dPp111dy2FdtQs4glX3ymd9qydRnmmg+K2HL7j36GjQAGTP\r\n4Yxauc7yl5U2gJzzIvK1Gspg3xT/CcGHMvQb17I36SHCpgxv9FKDXj5ecEJD\r\n8ihdrrX9BE0rbZsxRt1sKv1aNcJiTgcQujcDzkRwd71i4X7g77GuV7G0iseh\r\n8INxu4EMD02KIvAEQ1xAYQFFIoUWvQm+UkUyh3SsHhVeAUcp45nXBjS1283I\r\ndVycGYTm46ZKhSEC6kJABqQmH5PwLi7JOPE0Yteqfh4qAOqZj3xk6ZQ/7cGk\r\n11mjm5JAKpJnvRc7veNhv7jRos16XZPu8QrLXtMT9nLTCY4pbBrpyG+2GauY\r\npLcluMCqwWcJ+mQdgPteDn+WI59CExJlcajHLZrhQ8qK14R9yl8VFz6SLPXh\r\npZT6lsCmfM/J0GjzQBEPTU1o50mxATyH/nHu7pJse/HVNvAcYfDBVSlTleZO\r\nn3+YQWHqeJNC96Xed6O7qQDX7SoZ3fDGEpUOHmwyNQsOl3FEVBQqOw81TAVi\r\nSIudRtmVl66KU+B1i32iF7QutFaiC3xBXks=\r\n=yuY6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "aec47a5a869e3eafe55826ea0322ff5fd5338f99", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.6_1674409926159_0.6215933488106167", "host": "s3://npm-registry-packages"}}, "6.1.7": {"name": "minimatch", "version": "6.1.7", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "42f838160e09c0d72d385d8ae36b4419a77f4284", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.7.tgz", "fileCount": 20, "integrity": "sha512-n2nxgpaqchIzQ5VdaL58aAwncIaSJBB8Bn8rbfnuCGm6qy20iZUiN6uqS07/e0SFexS6mDHvz1kewu86OdciYQ==", "signatures": [{"sig": "MEUCIQDuBNqwhqjOvI5id6lOn3H1rBO316REPCtTqHAARqbpzwIgbOjaEXLFTGGbtI2534LSHnkWLgUzOuMTAmyCmqeCZK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5/ubACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBsw//Uo6sZTOTgJ+TXGlQ3gYszVTZc0cOT8QIbarIgBilZxgdWJoH\r\nvbN9BzJqnrbKKXJ7GW+mawernPR1WAsqcHCgyk1b075BY+TxWXLZR9sUW+0y\r\nr1YiCUxwuya5Q+Yn9SADLunldgsmrw27QWFBAZ8nQ6UbDR0xjGR4OJI1UBvG\r\nl5WV+7nl5GWK1E4W2Up4GNWvUvgZCfzaLqA5eAaQLYeky3TJmRcR+nVus4DE\r\nyobiLicOAIxH2LtAYab8uH9sqIf03OCKUvA3l6lWcz56cIgdFGAZeCAm985o\r\nOiueYoxvEhu+Io9iFhj9GdL7SQMQtmze04T/1wuw6xlEEgBZLLUxwp9hfO1S\r\nzX/ssM5h7ChqHQqMCuwQxqAgIzb5eUu35v19bnfJoXqTcELrp3V85V+SXuL2\r\nmilw4WvpwaWbLJ7bWLEW8O43IFzHkcxq3xyu4WJGtjfFC9smLGgcLmOzy/T2\r\nQSimBSr7rodOHbe6J1MJfAG0kKMa9fPAnRcbalBEWSYnCpC35K80uV+FQHqY\r\nQmUz5ivIB+wOmR3dNDvn1c38hjsHLHXonFVr4RRXVeVWh8mpLHgKUE1RHHsq\r\n1uDdTAnS9wu0YuC2WRCyYT+T1R7HrosZx8y48NAYp0J9tjbqfe4f8wUQD0Z3\r\njz6DgGuQRjG4NntXEXVZc1jwPPsAdQ07ntg=\r\n=Ftl5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "61df471534e225f70ac14781190c9ce3eb9114b1", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.7_1676147611212_0.02873018235140057", "host": "s3://npm-registry-packages"}}, "6.1.8": {"name": "minimatch", "version": "6.1.8", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "5a0ea694ca41342e14561bbec62802b59509f7e4", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.8.tgz", "fileCount": 14, "integrity": "sha512-sTvS8Q4mIZiSnMo9192lZMxfeaGCslH5CC7B62hd9DlbifUVrc02ABTeRJINPosxKnvZlrmAaNSo8f4PZqDDdw==", "signatures": [{"sig": "MEYCIQCGwVhcwBJFuIcqBySxI3Njwh4tM5vJqw+REnf7Co8SkAIhAKzkvk/ewdiaQEH/rqKW+AmakFJ90TLb40m46AJEQXhm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6AQrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFsw/9EXr2leur2zJt/OP2aO8knrsOscJc2d6nRLfr7tdIuYsE39aA\r\nMP2Gp5AXpvcpfM+MeIJcSPQbR1y38Q5CtSY82oysxP9nfx0daFx4ULh2GrDu\r\nQEoh4KHzzA0n3VxDkUGYhZShHSgbuHjhiDITPAlOfhC5bvxSA96qHhtmQqvI\r\nyJ8HZYrPCynhjV7CRa87W0dCyqy1nitk9T6plllHw9xDTZoznUUDmEN4yegA\r\nCuZIas2+sSXKhtiGqx8qSJmcU438Z8zcvnjV+kZnGrxL63kV8ZD+1d+lqpoS\r\n2Ad816tGkzfc/UOHd0HU93wlAhIu+0r7aw52v+pFSAcx1um8F9ixsCgfSZ2z\r\ngCbivoxGZpy7MdlMqQ4pq6DXlKtgwaOnaOMm5faVzFe7vQS+nOhFQFkSkF7l\r\nKDQ2cWHw9Xofxmus1Wvga9kfyEMZ9Nke4QixrwPqPiNiU1uXnHMmQsGSJl84\r\n5fAGerOpMuiIDdo3Nx+Bn5GkLkr/sMBhEgIkeNZyNnUg6Y3eEZxgDyCch4mE\r\nAyCwHE7VD764rlchJYo7ap1m6KnWMOOm3+ZsFHQMafcEn7y6b4zhdXmBMEb0\r\nJklpMBSTBB0Gi/R9+fGRHNYyGkizFtEsH4tLwAQ7xnU1W1BWBOtEWbvk+c+t\r\n7zGucf8gqzOiU+rkCBRFV2i1VVceF9fivt0=\r\n=U3C6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "ee4861b752dc5f7f4a7e5c21b90c3f66354838c6", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.8_1676149803315_0.4070319014417969", "host": "s3://npm-registry-packages"}}, "6.1.9": {"name": "minimatch", "version": "6.1.9", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "b4035a25d7175e3801e1d75e1f3f4d65c9e537ec", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.9.tgz", "fileCount": 14, "integrity": "sha512-6Vjiyqqav1xqJqQzMFOS/tHZi3vn0bGsKykLvK36uUj9FtZdq6XpIdHXBjoln/q+i3mTTGFeBGzhZ44wLtFQyw==", "signatures": [{"sig": "MEUCIDHKqBnwKEgKP6azkUKCaS+h51Vih3slzE2WlovKOdJfAiEA1DY6OkrU4OrLmMzpNXB/WxAjiUtXWzGuNccnDP95dV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6d6MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/nQ//cHxnY6kbSauxTC3HzX9SQbeD4PUDgSn7mEECi12kzxgIJOQd\r\n12pj7BALE4yt4ap4wAdXJ7iiVhjSbi/o1I7/j3JGoxg5JXimIqlohYvp1Di4\r\nf9Zql0wTz8bD9RAocPqJiLRqy8ky4dgyCUrqx4newI3cdyb6SugqXvhvKa4l\r\n85S9KMIkuog859jpcW0b6EKyxYobUmQK92JDc5XQEHR/vMC97DeU12GEtIby\r\nRFHfv36arO+Q/Kwn2mqxd1jMq38TNwThu01GQhS+34GULnOYZUbiYcsEDrr8\r\nBQel0QRoegye1SI3LLZsTAa/pTDg5K+yXmWjWduDm6I7XFXuHbgwopaIJ5QX\r\nMgWzx3tmJ1KgmQ2iFCi08FeLsDFH0Ax0lgxA4Gqr6k745JQq8zok+Qkh4DNr\r\nuwDUpiD6Anb91zunVMnL7aIYileLhnPBJF+bgj8B0HZgDWxlstL+P0VKO/jy\r\nIoUhEbeRoao0rSvZjTFlZVyaZcEpcanblQ3E9DbgKpIUafBvMri48dMazjTO\r\n0NvquwQsfy7cqk8n5lIVYiSqHLZwuw/078CNYEGJ9xcZm3mUwt/309ogT0en\r\nZpIOoHOzPAsr0ANpjFhi+Lvfmh3eT9xUNR0+2raa+as6f6ved/GT/8NmHX5L\r\nULnL1E07FkjNmHSk7Q7dygHpY6nStEnZPFE=\r\n=h38E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "a26fe302d59afdc0af340617383352f780b8fa33", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.9_1676271244101_0.701385115615359", "host": "s3://npm-registry-packages"}}, "6.1.10": {"name": "minimatch", "version": "6.1.10", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.1.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "36762bb7c69cd5830b432627c9a4f7cb91ce4e80", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.1.10.tgz", "fileCount": 14, "integrity": "sha512-+zx+2Cp+C4Ar8yNvGtxbLr7AjblzdF16P8CeyZEEGVAIhTLHM51wchc6+JKQkxdznmQtHn/dWQgzt5SiU3+leg==", "signatures": [{"sig": "MEUCIGhx86MK/L1dFB9L2TT2Q6PrzxHaQSev0f0STVmpyKrmAiEApRMSbbbjZGE1BKRGRJaeTV33d9QM/k85POpzozjsnF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6fLWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogIw/+PvbaCQYr3juLder+JCMv1Nn+5mpvVzqQIhJxCBLwb5NvOdbE\r\nu09VqqXs/YcOl3Sn3IuOS+knrf8zJW6guTlALsUJJkEEw+0IVXwEB42/6Va8\r\ny9RnUBkXpjwRPejtTWXgtJawyr9cyYxA5y0SQCenZGpt3XcqcAziugOXqnHd\r\n+H3VkncH4rwX+awqV2K5wCDe4qSuO1tHYXL5QLLb5vZ0i6kgaSK/s75myEBz\r\neoXsUeRBIdaioTVbYBNz8GLzGIwBxBPEm7Rn1UTSvIP7BbVIcXEhHH1j7yoy\r\npFlMmUIOJqcwGWLYMqB0JS8QK67G06RYPSDDfhkq3+q8Qqofoue7i/jHX1PD\r\ns3UEtE/kD//9mnERMgvdIlEUJMA1Ikz38E72r+EX/FfwZjF6Ux4mzpSPvhpI\r\nT+ImoEnCX1w+wFHOMoy8+YAXp9eAJogi2hI5Hcr1rAn7N80k2RDhhKk8IM1C\r\n1iWHY/1tBGlaJDnZkkn0eiPCNysbP8P1dwc3eCpj1ED0ofv9dleRvS5RWfOk\r\nFA8cliJYcoluQQOinNlVGEqGY3IR66fbEF8W791cN7ZWXHT5+yfBCPSPsFnq\r\nKxYa5WO29LfTut1d0lI9/1ZLbRidUVndZ0LFyP7IfL8/lN0BBcdnLBq04bvH\r\n9JM4AMPZP1ZNayCE4/lVRCr+QivMYua6QbM=\r\n=qXHh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "6b9f9b0d4db3e214835279deeb355a989887b440", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.1.10_1676276438746_0.8149328117530705", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "minimatch", "version": "6.2.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@6.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "2b70fd13294178c69c04dfc05aebdb97a4e79e42", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-6.2.0.tgz", "fileCount": 14, "integrity": "sha512-sauLxniAmvnhhRjFwPNnJKaPFYyddAgbYdeUpHULtCT/GhzdCx/MDNy+Y40lBxTQUrMzDE8e0S43Z5uqfO0REg==", "signatures": [{"sig": "MEUCIQCj7ndcLmGKNsQra+zikyJrfz5NMVIZiuCivhTT+X7cFwIgBUl7dw/fiW/dOJXPQf7xauVd68RKd9lFKF+g3f6YqRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6fuuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdBRAAi972eq79CjYf6G2cj2AkphjrdUXFiHUK+/QODOv2sPbfswWs\r\nh2iAFkmbyGwflSdOWQ3TQ/RP1RO1Op2FeVU4uQkkiELuJrc6XQFnnwLmq2/o\r\nD6JZ3gEGCrAVPu7TJCW6iG2WN8uxt+TgcghoSBxy8T4jZ8j2F+Kw6H784U11\r\nJNR/BefC6eRVRXfi0qHB5eMq3/TfrJIzxUxa6JjfpFUGH0JnjhcZVU54g5i9\r\nGW5sW8SmDJosoV28FDDzmoZUHrA5abL1Ix3O3sdyPGwgAhXpU+2bic9PDPtK\r\nZZXULG+SaknZbS7KOHR3VGD9676HdN9PpP1nryyP8FYln2JmB3S/zvde0qjV\r\ni9a5IDUBTadRU0YfsGzpbDTBZf+ZABL3TU3eDzFIHgTw0eyrFJGEEeoOvuSh\r\nEQ8h/HAeuY4hMxSLFsQuU1+l+f07rZi6BLurIo+Xm6rsTHnZG1s2BrDtObnE\r\nFXCbkr8Brub0wLjh1yLpYZaoR7Z00P9EkNy4FeGEDgCVvJNXn1nUKbz5Y2pl\r\ngR7PiPl08xpX2FHBydGcRMV5xb9DhMphKG2eGzu3CyqyRaalYz5BXzSX5sOk\r\nmGEoWcma3jNmrm0nUbeV9LBXEaqkc99/bFLFkRPk3JmxdeNZLiWAt3qHU88n\r\nk7chQT4hISltYduNR/YQAljTjkwUTqjE2wg=\r\n=EtI9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "01ec3c0a48e25263a759f870fdb825c97445bbfb", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_6.2.0_1676278702175_0.4844215714962743", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "minimatch", "version": "7.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "20a8df098715e228c918b2bd81f34d9543767a75", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.0.0.tgz", "fileCount": 14, "integrity": "sha512-Wog4y1P2q/0sF+0vw+6dWgqVmo/XPJg+2OtVmR6IVvNGDhcfAPjFacjZCUlGCoU/tbzH6EOeSt2P3llRAqRNiA==", "signatures": [{"sig": "MEUCIQCJ9HBS8Cvyw+Wik3oxdbVVrVp56Ye3s+5jMtA9kqpHUgIgamWqJZ8hzkqOOl7TvsNaxHGB0lTORUCaVQUXx+nLHys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8sLBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5xQ/+NJUMhfq1aIIwpRq6279jC0Q5ofcpXL7qMTQ4MOozqS5jqB4C\r\ncWwr/vXHPlgoMgTJaQ3TI2e38AKvVOsqTZoUJrSdM9mkMh2RAA0e2b+TcJYS\r\nedi7ylnS4iI+oa0gxu27BrCQ/WsY5pGWWzoxMmvgpLbenUsdjwfvHnrOmqDM\r\nC0VBpGjx9ovpmKT1MdZR+vAkOdiY5h1CosHyfq4vbI0dnnToIQK7xJw9BX0/\r\nguXcfh/ouIFu2wKReNFsa3rCQFL1KhSMRg7swFrLgmH9W3gtqt6LxbXcVxaf\r\n67erd1ht2O09UPZPQBy4/p2WB6SoG9cMRqktewdyNVPnkGB9alusi5c2NsOP\r\n7sT7SL+dq5caZZcIxmrlFImS0MfFl5UCWTBpXLuGMJ+kn1WrJUI8fZ4+Ro7R\r\nrXfJhWb731+csv8RZZ1UC0CMJBifpTCcpp44hfYWbmSfQnoP2Wg44tvElXxB\r\n1RpeTfybMTX8NMrWSuwK7Adtv8lhSROFWzgtaylgutNZR0HLOsiaLAtECzoT\r\n9CDx50wbchXYxque4TTtkVIoh1bbqqJKDR0AH6eJQUu1FIC60cncGS13c5jw\r\nJPhZjzioByA3/mFxPkRY5JPMBGHnQq+9T25uI2tXlgQu7zvgqCIRNl7OM84H\r\n3HHKExJEyGXzFXeTkDv9KU8i76OVDmD/D70=\r\n=zrtZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "ce9e6a4b42eb8a4c39eff1258affa9d37c66101b", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.0.0_1676853953384_0.5407872951362818", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "minimatch", "version": "7.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "346583590577d192cab9b88514f1832b9509d737", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.0.1.tgz", "fileCount": 20, "integrity": "sha512-C4CrOG1kAnaIxQPTAoiAmZCR2up1yjDdseGpr8UCUw5UqBUao5E1q2bOv0cAX0+y8MUxcyrvkTsoj5DvGRnvdQ==", "signatures": [{"sig": "MEQCIA9KvkotgHfdmcJ4FLvOlyk2dTR1QKpMny16f8fBlShxAiAm3gx12+iw/BDEYMNGkH/XmH6uXW+lUMQjdUvtBiagtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 309370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9XeTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvGg//ZkHirZWiBU927LrRDUmSafeRP+f/oNUjQUUU9rQYuf2ktm8L\r\n/V9EMB9oMSIHTj8O9lPSUciQOel4iF0WaiugzcGsNwn8D9TfI7hai1v9JiyU\r\nuAqRwkKIaL8aHBi63YG4P93JnMFaPiSTDk2hcTgBawx5GUgWEuSyKQtGu/Sv\r\nAWdcB6ASfYL83WP6RbW6UfYJJLM/RKFqTJ0XCbASPEIaQG8H11FZ42ODRcEc\r\nSltg2geIsW8e+j9DTIYLg2rix0ENPAlMdWkrtcUaE55i7MFpFXGKcCI38QDU\r\nmkdmi1lewCOqqgsptji23pKkNRPnw1SA7j9zu9GOP9wZ+WrPrq6TYkUarCN/\r\nt4M/dmucE1fr7L/bXxyrz9mYC4Jsxh3nZGmlFQvBtn4RaZngvdg64Lvic41d\r\nH8DEBtwVvArKyOuBizT1lIVWZ7P2xnNUyq/agN+v5NXGaBLEVhWI+Ul76QtE\r\nfMF7hb/TsiWybozQA/rv8cq0U6nm5BAzHSNhPEzQR94RYHE8TGRs/+wTluBG\r\n1Hv3/7+QmHJV2L4oIxFwiIlFYHrYNRCE2S4UvlXBqiu0/M/C7L/stJaohDSL\r\nDX/+kLvGCgMyZFo09AwmHq3iACAsIicuFr0B6fwpZe1OgPd+3+N2NVwR8oqO\r\n4pBk8syCvuCQbP4SRfnJ+Edsu2HfMj3adqc=\r\n=9qOT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "9130c1880391b9416630b050f31f4c0d114e228a", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.0.1_1677031314799_0.19671083509081821", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "minimatch", "version": "7.1.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "8d3b0a361b02b4420c89fb2b8295621429c340ea", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.1.0.tgz", "fileCount": 14, "integrity": "sha512-ZRvZsrVXiuB/QDlJx7WPymFyOHQUntQOEH3vFIwCzs/fDnH/siHZQAmI6Zamx1J9u9S66ucgKXU0CnqHfi8Z4g==", "signatures": [{"sig": "MEQCIGfgH0GcF8fNnjxBtF+y83QIq1beuGXvmDIO1f37QpFiAiA7TqTayBOXDjhXA3aaD6advgmCI9z3O1ZwmpzixZjpxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9qkaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFOg/+NBU49pzRSFr6Wqd/wGE5Qfz5+iO6EFJNK2DLfkFcApY4veXX\r\nOKvd/rgWfyJ87ykZfq7crKUcqNooAfd8QXHKC4Dn8vypBR3PDJSwilQZiRmX\r\nvp8Z6glwOyxUNubZajX+SdLPjiwFkSC3yyLyvN8W9AUyMF+I1XlRjQtFt9x5\r\nn7mgbJaRG/A7Twq0uidfUMlWtoCKZ8qQIe/s8x6jRlvZRTM8FMbLqVUHtvG0\r\nQD4vtVdKOL1v2Gi24LbS8cUyAqx1FNKTcrad7qa6PccmlPJxEFnkJ/hJHeba\r\nqsydGoqjvWA1yZaI5b4YRegcA0mhIxC7yGdaYzkUEUVVNZubhgNYV4qHTE78\r\nOqaxnTpLd4aIaip7A6LRhNFg1bsYtVddtGpMlJH0QySAL4jxDf/h2IpMgOR1\r\nnTMiBeEMuuYiQykKD04Rpii760suGjAX4ghfXD+EZJKX2E6eHR+7pyRHAJMj\r\nEY9s/xu1KhSiZmVYvO9bNnuCB3c8SVl4aZ8Dy5SJH3ZC5fJR0lG2SvRvVsNw\r\nSb0mf7amXyLPpYznhgor04b0uyYiSX8g23M7yJeZ2IfsM0JRVj3O6ziQKnPl\r\ndhel/HNLFdFS+Wq76T/XvjfMh7/i4CqfIfV2Wy7L8Fbh5kypRnaK3cpcRzbx\r\nSzXGVXHndaF8jNgvm24mKelKz9calwYalzA=\r\n=PNgG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "40ddf3bc75487c4622ec6b42d15b6d38a9ea8613", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.1.0_1677109529777_0.09648821795068452", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "minimatch", "version": "7.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "aeb8f7b0fef16531de96e56944b744860976d34a", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.1.1.tgz", "fileCount": 14, "integrity": "sha512-jjK46CRPxSRHTwHYtg+7LJ4pmfg01JuCjYr24+PUi1zHtZ8rOABPA0cMHKBF4QNeKn1xYy4hiBOm57p56ClCjw==", "signatures": [{"sig": "MEQCIDs/TlPbKrqrS2iNcbsc4SldPwwXBy+0nkzcrrYlLyECAiAPrnGzw0gKOE9uAtR/oOEmHw943YdTh39rNJmnZO/EXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+AaIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5nQ/+Jl299beqbIgBQgWTMRg4ePdGZe9yYWmWz65JnDrKC9y2KBi7\r\n6RkfnqAEVn+4eWh4IwggJ+mki3GfuVdVqoyU1aBgOAY6A3QOxT2Op/PIHDgY\r\n/OA0gWLwoN94t+EVqwMdIUhnIdhSj4r/ybF3FaQBhPsH2RmjbpYJ3Vzg+3/7\r\nEszbQ6+QoxQiy6Fys5tw4HDn3EaZUqTS8Ze1RcvMQeKa2CTvPIkdRe0bzFH0\r\nZEhDVd6nzxSJ/KGVeEyFCl6b7sIYyO4RfDOLGJnee7rco1+HlBO6sQTIN9bt\r\nxNzOb2L0XkA8/DAlYrBihftsfQ8KL7difel3jfKqph8pHhrxIwNEw0HHjorA\r\n8Dp1Tnji1ZI2WTzR+MzpTFwHr2qFP5tDkZk/UqJyQ8+MB8/m9c66SmV2/kHX\r\n486EDUvNCHpr+OWcwhlxUf1G9onqpOM997cdf9AztbCxmWMTWmOXq4y1arKD\r\n9iKRQpvnErM2VK0Yq01rJ2EElWPR0OfYYiC22zC5CLmxMXKz0PPwUbSFDdRB\r\nQq5YOt4yYoV8Dk5+uV+syNrjp1xojnBdlOP8H3EXSZaVK7bJqF7h/cUaL7K5\r\nLIuiIqKLrq7Bx3RgyFuQiUqaXzlmcYpzyBx/mukKMW10qrLj92rqqEfz9FnA\r\nDHj4wpBULJgfzz6KXSoQaOVsUVsarVbYlZM=\r\n=BxD9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "5f3438d346f0c4506a5be77d561626a754ce2ef9", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.1.1_1677198984595_0.5882418832347809", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "minimatch", "version": "7.1.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "806a43e4366dbfebdcd63a9c59ce9987343a86f8", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.1.2.tgz", "fileCount": 14, "integrity": "sha512-pPjQK2t2CsPTwXy+uNJxAajN2qMK1T0np9aHx2d5DTzpW/75J2fpHDm1p16OQDhu4AI8NfCH1FdtgyZGWMxy3g==", "signatures": [{"sig": "MEUCIQCWB/oweztjhW23wwrtbows6+q3N8G6hpEoEIcmgA5rmQIgauWzsVQ5qqjpFgzPHClmLCgZsdEbVDqAJ9baCEYUSKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+UC6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnEw/7BvMhs8PBmsLTPfUDSOxhAbmrCyC1pzJ4WwEI7SaDpwzVCDXE\r\nsFIAAAfhDHG1i+atManziOcAYdQs9brJciesJONpfm4pBmFTEzywthrZpnub\r\nVLUf5vkZ2cgrhNSuK9rduebtwKuMkewDOvS4evKXUJ+hfdTizzG+V9f4SZvs\r\nxLwknlmwDggrghetWlfNyMr2mRQGs4wjDyaynSNPq6pCAntUXCOz1uBZ5jVI\r\nm74sBXLLsaDv6Xf+qh1ZazRG4CDZ/gu6FY41rW6SkUKY4/+Z0T3Si1t1agz5\r\neMQIhybpTYjPNwk88poCNDiVe7PvoRTWKMK7rBEC0EpD/Mmcp3K+w3HSWm9R\r\n05DUVvDgXFpfYpY+0C4db0v2wck+TU4rpUI4Hs8zK6MpxqrmRfPf2kXHESph\r\nKEzcyJq31coIamufiaJCuUWEzEpJGPW2qkuPy+zkL5x6XxG6w8APCq8PJxXh\r\nAChZB8cWl/aTg/Z3Nnzf36W2Y3nKuDaGtgzzLIKF5eLrhoVroyLhkdb0UN3r\r\nPOkqtAv9WfloGYNeHTojRuxD5S6zcHZVMytIUZy+cnal0Lk6YHgqFiSFSz/i\r\nTsd0DibLhEL6142Q2d8+nkzgBOIL9smC432XN4Zb3M7RsVgNs6HTn5PGD3mQ\r\n2P3vIdedN3U/P9cNcMrymaN9ju1nMwrCaOc=\r\n=e8HQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index-cjs.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "36c50b76a16f79b6862ecaf2e73b0eaa0ed30701", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.1.2_1677279418017_0.7591099986781089", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "minimatch", "version": "7.1.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "1e1f1af99d47b48b7133a641b0748ba039719d5e", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.1.3.tgz", "fileCount": 14, "integrity": "sha512-kpcwpcyeYtgSzpOvUf+9RiaPgrqtR2NwuqejBV2VkWxR+KC8jMWTb76zSlVJXy6ypbY39u66Un4gTk0ryiXm2g==", "signatures": [{"sig": "MEYCIQDLazKC6r+HWPMmG/fdT4V8QIUWBTEiDRQYyNF1W+5cFwIhAMudHByKav6+G3o1hsFf957ybdVIBkSA+Tf3oP48/PP9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+W1yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquAA//dn/cBh8whXoFVdjvSLccIFudh6V5lhNWz+JXKoz4X7p7hu/q\r\nczIxBE4edtqTU9Q6MfqG/MoQ7I+FzFlBXjpWDju97Pc/tYmPo3T75BFUe24h\r\n7VXGb+vxXk3ePGqusABJ2UX7jYvEC/AIDt0fgLdYmTaGeiD4knuHOs7bTkIo\r\nlSHJNLJNVc6LHtiI8mYFEE41oJy+mQZpWrcFBohUoN38SaUhO0NmlnK7CMFy\r\nBsHP5cFODUpFdFffVXtr4VaGBCl4ubuczvsz3Mbpo3qiNCbior5eE/i39k0w\r\nNzH1JSprkMiICbChXNH6dXFL190e3bN7tVK+IjnsoZalwjbfxAMts+SOZf6N\r\ncrldp0pt+QA1fhwKQtSVLa4nHZnJLiKsxe1qDbJqABJzdSF9T8YHjKUCwh1l\r\nhLTfe63bSPIcLPIG1TEBX+E/mhYJ1sjaFzLXgNFXCejnPnzLhFOvctn2nHze\r\nl1QQvU+p5Mme+4KsbyhY72G9VJR26viHYfADeKu9aqBngV6kqqXUl77fucIC\r\ntdqCaomN4lrFIREGtHKOfGvHNKLpKwg5a7cyy6cgfyiYidBHL6R23G/Y+k7c\r\nf35pPSk7y75wbatgHDIywkbZH9uxzaDjWxxbm83On/CgRlcuQZhMIcdd/Ucl\r\nKM+MSAlkI20lm0OWtnHEpcHZNEn7ATsPFhA=\r\n=iHdS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "77e1438de2a585ffd84389c268d79cf8876c1c37", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.1.3_1677290865878_0.3198414786779613", "host": "s3://npm-registry-packages"}}, "7.1.4": {"name": "minimatch", "version": "7.1.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "5b252ebef9931e082549370815afdec3caed0319", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.1.4.tgz", "fileCount": 14, "integrity": "sha512-dZdn8jDUB4Y3eu7hABT6IgLTMQ9cVf+vhhXjLAkuN40wRkweVxEpvnGYLYZUhNB0P+BbTOZDzo+1rCitOQWc3g==", "signatures": [{"sig": "MEYCIQC+UhSTZFAATPep8XMTDGxZTdwjuRKbxEOggdq24T8cQgIhAMUvMna9JU7lL8LWE+d4C7v1YN+7/qVA+uZgDmLGGFBj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 216408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+q/LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMSg//d26hIJsUgJoM5xDhin3y70TkN0CUno/EVbG4Kr19+Lo5M+s5\r\n44QBAgrF/K+eTpOMYc6bo7FD2/I3eEiHsN5vVxSXkQnTPR8vnqSjFQ05Ed00\r\nUcjw4G8XWuodm4b009qdCN6CnwZXSYH8kZjJdEAs3wLkqEsS81rgykUkNcTm\r\nCqaI5ORebliUQk67CyK9P1L4TEijbiotr3cBJRqsEN6Cyxa4OSrS+qHg6wtO\r\nGPLOissOskXi850oDL1dCFGLWR1viNQNw0pI2j/u/7ztaoCRHhIdbsHEISuK\r\nA0IkWKtC7IbxUSEkkEypjM1mS63goe6YpHvJ8PYsxmKZ6rN38NJ/4yoiWUL/\r\n1LlF7h2LcXpxFn4yOplKaAnd6T4ECtTEj2ISP+FbkgYSD9p3CYhNcWnNdtoF\r\nHPdt0eG6hOoyGpnMfE51LPQJAd905o4HJh4GD6GzPPJT/myS2w9HybjIL1lj\r\npGd+eoDq7MFRe8hDFgRMXUyIIadauy2XKREqO2w2EAqMsDAYqvFZFRhE0nPE\r\nxr6fqtV8KeAW1G5evwXJnR5wz2fyOfGxKvGRgS2sG8IpqWQAa30nr7i5hBVF\r\nFI77JOklPAXIliufNPTPLpPI9zSiuPQSrClxne8sWoesmdw9vpAtCdauWmI4\r\ngp4nVFjJ/eGY4iBQwQkYmJmctOsJVOLlJXc=\r\n=czf+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "f050a221047e2c73f18dacc3c5069a1966ed436f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.1.4_1677373387282_0.835285345087917", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "minimatch", "version": "7.2.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "741ff59370007ebb8faff0a9b20cdb44357803e4", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.2.0.tgz", "fileCount": 14, "integrity": "sha512-rMRHmwySzopAQjmWW6TkAKCEDKNaY/HuV/c2YkWWuWnfkTwApt0V4hnYzzPnZ/5Gcd2+8MPncSyuOGPl3xPvcg==", "signatures": [{"sig": "MEUCIFoPGBOrdeXfsKjNV24kzx8NFr8/1WRBKP8w9YLrZdFuAiEAvjLfHuaTInor4acFMjvfs+pxVDSR4V3HznmJu2ji+/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+yLIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLZg//avYLkGKsdvTVvLKL/tiO7AYxZuTusPdbqqeouko8M1O85LV7\r\n7KfqBex+mCowO5bVjkeWzVc/BKFnJyFYCAlVOXuXl6MnqZFVO67+F3hloXzu\r\nEhzNllFH4/HdNPcoL6sytNwp0eLujXqZePXqbmLrvSLjmEBsHYQK+qdIZGWw\r\nEdoaM2d3LMrsJvZEKI9jhb7N3C8OtNckVB01qpGTyumoIdb0HpPDFCzKOeDw\r\nxiZckYiv70lItqoNGfQg3GFXBraOLMqm9lAhWOyw6k84et9z2LextcFsh7m+\r\nqM81EX+3XtAB38Dv0b6Cci4uLnZ3iWvFbo+iZwBDmMO1hNdVNL5PDsaWpzLY\r\nS7Mrnk/Dw1cY9UyUkB9Co8LkPDlq4fy6XYlZLQ6SQj99cgDNQYw3nSzONSmZ\r\njuAB94pIWJ7IoCLbYj+j4kVWPLtblcCMTpUzqEQnkDOCrfqc8RZZx+1q2+ky\r\nsXpKfW4G/JAAk+VFSCoat8vahQteKQ0bFlVGvfBJdGU2ONoB0YOTLuMQTT/1\r\n75Qa55kP3rElr4XQR8Vjw1mzy9AOl/NfOUbk9CA8rFElyG11epLw2AYZVpyl\r\nEVXQ3fKOhOqTAd8HWit9xMwIYC8ze72HJha/u/Ek3F/sef6UEdImX+GohAF8\r\nqH3Ooyu287os99xxa4Szgo911wXTfvxu9PI=\r\n=7DD5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "be2394fd50c4c44bb82b2123bf362c2787ed7cf2", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.2.0_1677402824667_0.5725682723980614", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "minimatch", "version": "7.3.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "cfb7337e7460308e7147c58250fa0dee3da7929c", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.3.0.tgz", "fileCount": 20, "integrity": "sha512-WaMDuhKa7a6zKiwplR1AOz+zGvJba24k5VU1Cy6NhEguavT2YRlHxuINUgTas4wiS6fwBpYq4TcA1XIECSntyw==", "signatures": [{"sig": "MEUCIQDCrWtLvwwv4e7HKOXnmYnIJ9sT9QfVjGEe64pBv0rLAwIgclMnu8M+u17QKKPinUwEn1MKIo+dI7ZwJfz4EWdbauk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230092, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/QtUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxJBAAicPSOEOMrwLpI16vSOLm2/pwa9AsDbSlzoiZQapEz0kSLFOB\r\nxaWkEH+A/eA5aNtya2mEqX4EqmW2OIHaWQWrlij7Vrjskm3A8aEIplFl2VC8\r\nX9UXjdx4H803IIWaXoGJQsmvANOgrVsLvpwDt8DGsDxTkNsdgXA0n1zDgmxo\r\niV7KjGrNj9oHIw2b1PKpFXa3/8wV/5mSys5cab9BPzMQEsEG9qg8jnt8qAuG\r\ndBqcAtPZDjCo3295KuadlqucV9QVRTXWzurS3CKMe0DY3QGrvPat8yUjKr5M\r\nuGfiT/vxj4pQEhFlkddHrxG6mj1T3Fc7oOgG4M8jGoG4TB34h7/CaV+Bqz0K\r\nZ8PXnruUZkYUyZXLB4srG9fFtA4ZA2Aa++jJ+OzwWj+jxJq3k0NqKurz59yR\r\niYIyQITWiAOILszvHwhhRRBwsBvRYpgdJgCu4CmqD8SYv76j4qpwL1ri4gF2\r\napLYMb6/jj17gN30EGhggH6kYSKs6FTsrpC9DeseqQTt3F+SiWDKTUX7Z1wa\r\n7NrQ2IUR0TL8SZxky4nTEDPKIWNa1MVJGpo3crSAhxBIBz0N/57dV3PkbX1a\r\nAKsbCoX3L3s1TwVI35u6JEGiuiLaSQMzLzqWjhitgS1Yn/leaEA8MbBlhO4t\r\nM//UVBrunRXvOMRWUoewKxIB8hQ8aDLu2Ak=\r\n=2Ozi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "75a8b846391512fcfae9822da12054e1bc2496f4", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.3.0_1677527892438_0.09427200950168779", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "minimatch", "version": "7.4.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "d43ecc64c434fc9622d3fe447db71c7caed8e1a5", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.0.tgz", "fileCount": 32, "integrity": "sha512-Co6cQqQPyou/311vxtcfit/RKf+MbRN6qa5K3Zfh2Fo9+Pvfg8C1LeaAXcoRfwhxfZIe8sBc/kLXdkR88xJGBg==", "signatures": [{"sig": "MEUCIQCRH5WkhNVuUGjXPL/e/On2Vu9wnCr5FqBt0W7qvoiYwQIgWbnMiow2+BeZBjLgBqX1IFr7sS5Nstne/IekGBc8T/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/vfJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2vhAAhDz+wG+TZ4NHKmHtPvYaB0QuCDhUmbgIVItoLev+VcAHAihM\r\n8bQIPRJ1fDgbntJ7FOphF86vuDhiZM2w3cF+EU8c6wWGQDniGkhiN4DyzJ8M\r\nKhZ1qcsYY/+DpNsdzKwKEwVyVS7ERkmChSbcaorREQ7+tG7wetwAQ7Ba0KlJ\r\nQzuYqtvV7cjLOh0tW9iul8+mpNCpGe2hVS+hLT/El4H4yDMOcoKA9WJXUhJw\r\n0n/hXNLUcEcNiIpb3pSCTK0Fjiv0REIpFnYQjbkmdipaG5qkcMJNj6HSrMES\r\ne7Y7dknjS2VCRG/yZ6cafQs/h5NEFhdPey3PLy4qJSiAG6QWkcuZ8P3koOnN\r\n03nxQE1YN3k3/AUYukieBdrTdBZ6KnCebxYjM0y8j+aiYUSD+FDIyWJR5n9v\r\neSpPhmun6msMd/aYRiOuz/3WEPodDLuLzrTdn/EL+zSsPYPnfbp1oBXl7Sw8\r\n4jL9ln4imGak1uRV1gmccO3l9BGOZWdZOPXZFMxdLrRDN1l1dVV3WrgYisoM\r\ntwn1fJweSJgACAZUfGHZchtqougmwHXev2yXQz37l5wXPdsDMU1lQP/QBXg8\r\ng/DrEMmZxNu6TEx2SY+pC4hncVNi1BtmCQkkKde4LD+zt7Iq2l/bYQ1WQqFz\r\nHLTRD5Tz7TXH2BsPpreiqc0GTELCy8wDpf4=\r\n=ZK9H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "f19f6ab371448d0e819129d40b66745a7b6ba7c8", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.0_1677653961727_0.2915757555774323", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "minimatch", "version": "7.4.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "166705f9417985ba5149f2dbf2fa50c29832913b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.1.tgz", "fileCount": 32, "integrity": "sha512-Oz1iPEP+MGl7KS3SciLsLLcuZ7VsBfb7Qrz/jYt/s/sYAv272P26HSLz2f77Y6hzTKXiBi6g765fqpEDNc5fJw==", "signatures": [{"sig": "MEQCIEEsPRkEsrNLWyCYXaNaCKct+EgfY9FnIYFs8dnoJnl+AiAd8MkdQ86w4UfXPjrBUDEh4MjUWSr2KAAhq5XbvoBqig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/wX7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotAxAAhPjx1UClIVM5w0p8QlWhM0O4NPsPpY8118k2cA5SJSo4BxXD\r\nQGPqTAL7X9LKx4sH+Xqu8NGmpej0HNhdOBATUNjcnadC1pvGYqs/vR//BaPf\r\ndhypM1X+PG9FK/YkAGJfKTcucEn4zcXgvOKeHEnvj8KZOOCRzhIEJo0Dog20\r\nUqfmQbo7xiXUhVvZGeulTEvPDoVfuQVv0SOZIdgul6MfiZLjD+FP/TiBYIUj\r\nZK4PtIM6mrjrg7hdgQaa+YiGVA5A2IfIw2brF7rNThAMUtFgk7sYzDRXTjYk\r\n5nRGe2u7LclBsYnohnN8Z5o73YEOna9C8AmX51hG4v3K0Rtz9LKCm5pHrQnQ\r\nrWZXp8phL7Ogz1spfrsqN4FHv3xUve0VB7O7kKmtM+qr1iZvNi9nmZjWsf4R\r\nkJ8lxjDXCJK6BqOhsjHcrplgubHzjRir6gM7ibPnJkh3gbwc4lpQ5Qi6U5Vh\r\nrGST/LfMoR2Cc3YcYKVCP7oyTgu6dI2CCUCqufbRPqPWruYknQ77SH1hHMlY\r\nV1CHI/ZvgdsFEfDUAocTEt0/83G8XU1eldu/SdafA4eeWwZOHs8kZ7XHQcY+\r\n6bWKWN2vXXtl/Xszh/6D0+3yJ8pJwNJqna6mCzN77UZZiP9SuXpcpWq5ogG1\r\npX55a7ujlrYSnU0YYmaLsA96xemdw/xvVf0=\r\n=6+fU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "7a45545fffd5955b972aee97b9c33c2cfdf18889", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.1_1677657594911_0.31622000129147465", "host": "s3://npm-registry-packages"}}, "7.4.2": {"name": "minimatch", "version": "7.4.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "157e847d79ca671054253b840656720cb733f10f", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.2.tgz", "fileCount": 41, "integrity": "sha512-xy4q7wou3vUoC9k1xGTXc+awNdGaGVHtFUaey8tiX4H1QRc04DZ/rmDFwNm2EBsuYEhAZ6SgMmYf3InGY6OauA==", "signatures": [{"sig": "MEQCICFkuIchZYoUyaGcY/6BnNTkIffS/nFdzY8fPMJcvCCQAiAJNY8m3vhfG1Robh/iBE7njO1L8wHlbhMY1otFlVTchw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 256291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/7H+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpowA/+NYwxaE+ssVwpQNRas0/CLcAwLNV4b4Wr8+ZM0Xy0hx+LOMhB\r\nVR5OFHR5wrBK+axuJNOeHqlfriR0Cr9sA10JZqyNbqyC9FT+VbUAW/v8qp2F\r\nrwgWlbr9SV1+HI3jh4J0MVoLoN5+0xAUgEekAjqnk5K+up9v5YUvUQwh8l+2\r\nvSo2V4JlVDVXD1TtNZI9y8QQG7bxiflGUgphmP+/srf8Mqt05xvv3j/A7kaN\r\nz238N9SKL3vtpPPZyoMyKnfFwz7JAhD2ZWGf4VeOByo7xtPLMUgt8/wW3770\r\n6SJRsvQyk70JZBQbx/hpB3qchVPb2JUzYfEW80GhQTA2qNJerpxjF3kQADIp\r\niqe6J4SREZj9tWEfrofinOxVdttfODaglYOjoGbjkDOdTJVtvk9tFCEVfeEu\r\nVusF/Rf9XqVaBSX7yq1IVR+kA6QbVfMwosGiREr+xyrIFl+tlLpcp+zsc9Ab\r\neyoPCpQWjALbbCmQnkjdlTaBAcy84/h1dudXxcN8TQScs1fE/g1uI2xtNvrN\r\nn3ksaKWVhHGrfcSQsssF1iAMktCMQWz1r5vr3Td2bqUyuocvRHC46UqJLMnw\r\noXiY4DiNRU290s85nekgl5Nw8k4KLLtRCgtiwUNxAeb+Jjtkd8qVXWJkT/lK\r\n9sZQFqYVnObZ039JylHlf3MZfnmJtkuBeaM=\r\n=v7UZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "d545ad6dd83f9001f5c52756dd43708cfd2837a4", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.2_1677701630364_0.45483190781715677", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "minimatch", "version": "7.4.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "012cbf110a65134bb354ae9773b55256cdb045a2", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.3.tgz", "fileCount": 73, "integrity": "sha512-5UB4yYusDtkRPbRiy1cqZ1IpGNcJCGlEMG17RKzPddpyiPKoCdwohbED8g4QXT0ewCt8LTkQXuljsUfQ3FKM4A==", "signatures": [{"sig": "MEUCIERWfNmuYFfkHSFD5K99YTkw0XMrKBVS7YI40GpulNThAiEAxStQcZuErjQMmEowOm2RbmGUhXw8RbUaw+VR38cT6JE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 553776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG05bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcZQ/8CCAYc7CP3ECn96RGzOn3REXugCDXeb481N/65tYGyo3UsvoX\r\nuwze3VjybBdwmwSYwqJ78YxNFpXdUi1WW/R9aPU48ZffK45midCJ1Kziyc3w\r\n+3UaK1sLGjBYoK558MrLdYJHUZlSd3K0wPAFGNGnnMrGaJZCEmrJwkEQ2y6e\r\n4bc9raVd5/keNir4YqVIHwEULC1TkEuMsDh4YBC5+cp+1frAIuUbw5BL7Fwf\r\nKQh4n6kAbXE7cNGRrOk2aI5o2mbC1aM9O48WpeGLPgAHL7Zzfd9OIt2i7OVL\r\n3sNCj5VanZuDVR/eduEeKLqjmm0M1YYxkiA47eR9FbF8hEWEtQ0+u/+Lw0zk\r\nJxQgeIyumBhNPc1YgzDk0w40Q0t6wDiNbqCZ2mipKz6QASWbhsdpSeO2CaO+\r\n5iCtI4MAxaPbBHLZ15pdP80Cmpknlnkvs2WAis7naPECJPrt6n6/Q8V4fX9+\r\nfWd+knw66ypJA/UQHyZmUfGL2sOOtMVvYPrekvAkZiMF4V3XfyNuzRts4nGa\r\nu7arPh/svxHRw2i68cNzIrULODfhTmGPWkKUKOfb8xU0LF8SNXslWPdLwYjx\r\nORbM4nXl2kGHTV39lE+qzBc5i9c126dldVMkOC/27EHHDBeGvWWRxGVK0Ki2\r\nSiw7SuOFaezaUWJTzpRI02/BJ7IujV8yBVQ=\r\n=VIlD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "5139e19188b9fea0cbd9064a17b40551d6ddc23d", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.3_1679511131037_0.05843171699737226", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "minimatch", "version": "7.4.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "e15a8ab56cc5469eca75a26a1319e5c00900824a", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.4.tgz", "fileCount": 65, "integrity": "sha512-T+8B3kNrLP7jDb5eaC4rUIp6DKoeTSb6f9SwF2phcY2gxJUA0GEf1i29/FHxBMEfx0ppWlr434/D0P+6jb8bOQ==", "signatures": [{"sig": "MEQCIEbwQOz316VNjXNCeC+4egFi4c9H9cqbTjIz4+CBfN6YAiB62Pl5eCYXM0vreuIasHI81qiCgBs/oofXC7qGFnef5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKMHIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNaBAAgbKaYm9aJa871c2rsLMNHUDR190v1eZk/tgQGrrBmkKN/CQB\r\nH2+FvffwhVUFlAvEcvd3lTaK3Z7UPkXms5z1a26E6fvQBDEH4k+8EerdXHmH\r\nZikeTnJQHgsfhT89tuwUmzwN+xmCLekj6TMqmOX0PrbG8QX7tG0ivsrZDC+1\r\nQWTm5CDvQrDgimbG3YoCdZ3Lcw6vAdPRKM6gqjomHaRko2ZV/DiPDUs/iGut\r\nFAn8WcHv9EbwybHACGWld6ZfmcTQY24k3O3lDgcxKaLwj56K0+YWYUGLATd3\r\nyGpr8Cc7BwNYvaoRw/4F/Hfusmodhe0anJjVnoGJxWMaqgb5SjW42tgsrtEO\r\nnwhJW9FL3y4xh4NHLt42NcBXKNisto00txvILo/keK3x/7n989zAHm1lMam3\r\ns4oR32BVVpxnBawQeTU4w0X1osn+6PxTzdJLMoYhHkv81Jt9czmdeVNWSojm\r\nFygxJCXTlRcVU1suZvTXO/fgaB3UsuDQfwosilHqp+DEWDR1i6dptfJDO1Pp\r\n/1LyA+p/WNX1b29ycHpHM0sowxEC/xzrF+FpVdTwJXZdE2LzZWHc8D7cce+G\r\nMlWv342BqDBVhocoy7h3SWTGA0WqJwm1VfvPEyctViHGQ23vFonnKBCeO5vO\r\nnYeOpBBUVRzeDdTRSx16S0vg5p3e+EMcIlM=\r\n=rqMp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "32bad341e9a3d876fc103a680e5f30cbe80318f4", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.4_1680392648739_0.5017720729272948", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "minimatch", "version": "8.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@8.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "807427d41e90f1e071aa06083e13edbd95479f73", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.0.tgz", "fileCount": 73, "integrity": "sha512-Nlub10O3zlSIkHHVVmhEvpXoBQV+rD1gTSax2w2bklGU5y0zg1MD/biD/elp2+Mw+8/6F8MzEU0WYwmStMDZ6A==", "signatures": [{"sig": "MEYCIQCUumX0+S1QZa0r3Bno/+B1UcopXOFMBGf6aZq2ZHaK1AIhAJRBptw2FTPQICG1jAFitDagP9Qa3PHiSImeDungnQkq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 497750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKPegACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM+w/7BLNsY7YSCVQLtY2sPjSudXbwVAHirUOKxP/IogoGfR8ha1md\r\n2t8i37+Y2kOBjC15pVbQfkPhygSOuLUe4up+h5RKRT08hJ1py+jnoK0zg6KE\r\nxfkJlhkT8tA4AqH5fPneD5xjTtsTICdm3k6RJoDOak9io9Iu6jaCW4sTWpUl\r\nzL6YU4+NvEd7nSsVBsdHldYP59g8Bn8X9pS5hml+LxGy5srv9StW1J/ZuQOv\r\ndQGALJCrnQvKmEzThO5gHRV8Z/+EThEMQr59px4OmzPx5J8PxdK7aiuRet2a\r\nMBvUjrorAz02yIjMVsLNB2xznFwfngN9r87BgOALeQP9k57mvhTD8BhGMIoY\r\nKGlXwnzXazM08s7uLq82UtM4hgU5XJ+Iiw96rpO/KwFVH703UixISqEvcHQp\r\nGZBB++3FUdifOD66YQOBw/jCfmYxi+BX3sdgrTIeg1Y9CRtOMxPYaEMMR9Y+\r\nq23E21Qx3Bnvj5yJbYBF/rAAdFUqChdizC+e0eoeby4VDqJY8tJp8jb+EZGQ\r\n961RNxhJBiMdT2UROZirGvDVvN+Btdf5YzjoLiHyWY3ILwxOtL3KAdTd7Sie\r\nHRXKQapPnDQ2/s8EEtps4k0ETWZ7B/wdKJaFazsR+arSPDJ3bTNAc7X6XqET\r\nUvBSPrkCFEPBq8GWAPlutqYtihAImNHzqJc=\r\n=AUEg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "5d20578d5749f53f3413b7ca413e5658d6ab0d05", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_8.0.0_1680406431981_0.8508828183835893", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "minimatch", "version": "8.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@8.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "1ed443e0df99ef3692e8f2c8fab8ec9f25433a6d", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.1.tgz", "fileCount": 73, "integrity": "sha512-Azl0R5ZMbhrL031Jfu/xeeS4tTMnjXHiyVIbzutzq6g0b0iRWmC9recCfbo98+uReJYoNLqSDiaMhu3Lf0YWzQ==", "signatures": [{"sig": "MEUCIQDM1GEfeZt3TadUCoIhmEc6bwku+w9b+/aHEhHL+68vEwIgMRPl7+glOxOkEHWCGJYALztu/ailrsW/4Z5zPl9sbyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 497768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKPjcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHZw/7B10WfQVhx384jPvwU0UBfCwMnMdsJ3IHqP3gMyWlmZqilKlg\r\nCbT9woEooSpKWxmh+DaCZvu8Dd8CsXA7yLbC+sy7NUsdFoxemIfQGvvVrDqG\r\niuo3MY/AqDuvr4CXrxK46Y+w9yvEfh5Vgdbymf9SC+RCrfGl2IV0IwsVkRQR\r\nrMIwIbcUBhSUxJ4HwPWZDcub+RU1fhTxWzSaf/luRpop57KIF0cWcOd4q9In\r\nsT+D+vlk0SoWmoSPBFUalgUqhUofM2e8ay/kLs8xHh49ikDVgOdKDSYWdDOB\r\n3Jod60JYYDGExMqv7I4iAq7q8XEBl6Q76/uTBAJ0NJn+3HiQmZ/jEUkRC2l1\r\nZmt+O2nKlNJ2wNtCAo3bQvdvVxx/gCefVloPObhDVmoj31dUHBrvgV6Wh27t\r\nvRLvhNZTtzC1HKjCY6Cn9SU/d1UIcdxpl/Jr52WE7SFX1lQu95HAsQd1dhAR\r\nE5qSC0q8sp6cjZ45lq+7qTQ4DFY+IBTzElWSMoZIAwJCEZgpgs8JxeS/z0MZ\r\nqr2fTgHDCklExujPeEBQVC2e6poV2pvpgnhcIYpz+OxGUdzaUt8/a6X88EwE\r\nPtMK+RSxcmJjmNMqFBKxIQPeGi9DcmOMvSRGged/Ov9IihjnBSNYIaYjH19n\r\n3ILhrcN687J126rKZHCUATY5KifWS8FZ01g=\r\n=IMYP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "bdddb1d5ae8401c46d1ed8310dc42e8891f1d3d6", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_8.0.1_1680406748552_0.023994818503613402", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "minimatch", "version": "8.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@8.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "ba35f8afeb255a4cbad4b6677b46132f3278c469", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.2.tgz", "fileCount": 73, "integrity": "sha512-ikHGF67ODxj7vS5NKU2wvTsFLbExee+KXVCnBWh8Cg2hVJfBMQIrlo50qru/09E0EifjnU8dZhJ/iHhyXJM6Mw==", "signatures": [{"sig": "MEUCIBx4LM+4f2Yx3WhCzE8v4+3kreQrNfO16OUvEsc0a9lMAiEAjKwYP6CxQ4oSXJgYFfNKrgnJ3roH6wRtl32l2ZG59uY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 497831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKPlXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1Fw//TT6QWYrgD7Q16p5xvhekDi94ezjzEiZJaSXXaIrqaLIoH+NF\r\nxCecvBeWGhGVmG5wOmZXAdsrVS2oBWBFhHoCbeiZw5k8IE4YbyktatdAQ42U\r\nG/5POc6cO5cevvEWltmyJ7R1+j7MA/nt4P/drJQJzgBhClSnnyD3DbXQLSkR\r\nMgI2rTbK11Wy1QSoy9RmqHgKk+UalsexqawcoS+KdkPLKAAx+98Hchc2izxC\r\nSnfEUVPgjbPlZANdLeUy22VujerdyEKaU2HThsu4CamruEfUdYIpkIYo8FKS\r\naIiRbE/ZnCGfd/5bnVe9JEhWW5uulsnR7fw+ZW/fQ51ismo2odT5pMUV0BfK\r\ntmo29/o4lgRJ9frqScrNEivCqSpl3INpquMw31h8aDobp6gthRLu3j7YAFQ9\r\npWWzP+z+i5bGsmvcB3HSYX0Cp/hVvL+HaMU1PTEGzNFq70LKqV1jbYYvidPa\r\nnEtCjMi+9JHvVp0vekZaPAC6tU4Ult1k5UjkFNmSoxLkjFukfpTnxtU59DcD\r\nCzjXKyi9HjffTfUJjI0CdIMNPgfgWIPSq4r3ze6M9jdh/hCUdH1j+772MrNj\r\nJWRXHZCwSePdI+mNuuShrehCBNLYREjRdb65CHy+KH/SIp0v/sYVq3SpEH3t\r\nQlzMz7lODB0FQhm+kyFxTn3X3UlbvGZUPGk=\r\n=1l5i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "cb1b69080ca4f972ce913e2a7ea6720a14b96e39", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_8.0.2_1680406871445_0.8086971078380669", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "minimatch", "version": "7.4.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "e721f2a6faba6846f3b891ccff9966dcf728813e", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.5.tgz", "fileCount": 41, "integrity": "sha512-OzOamaOmNBJZUv2qqY1OSWa+++4YPpOkLgkc0w30Oov5ufKlWWXnFUl0l4dgmSv5Shq/zRVkEOXAe2NaqO4l5Q==", "signatures": [{"sig": "MEYCIQC7JyHfA9Msy43OOtpIAAATiwFSXadEKzmdlty5pMZDIQIhAOtmPe3iArGO0WklyJdxL9iNe47jTs2kLuN9Y+x1+Zwh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 368159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKwO2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj8Q//d9I0fvoMnpNYy9vV/KQoRRk1jT6x1DnqWdNZVU4S0+P2z/Vo\r\nopszFZUfPzw/tYJJT2CeK39eiArkEXk2MVaNvgvELJZAF2XmwHqNcCDVvEFl\r\nHk9KwCyhokWSNudHMXnS51jE53uIjwvLk1aPjlc5HZl+zf1YF8oY5v57qm0w\r\n2SFTCNCCFIjyrpgdz7kOsBt3eJu+B3+O1TT2f8D1UGiBjf1F06BmCuh+BfrT\r\nJrae2RoxM6Nsg23FpaxCYzKf0cJ3s8B1I5hzJa4lYDOiHdzIBkPonqye00uY\r\nEUOsnfwhCYASwqccMu/dzuWzRXpnuu8tez8D0Ycx7o5a/JCnhUiSd6EuZJcG\r\nEOSEiiXjOk1pmF+U8owrzNOS8XiV8A7IFLGI1+uXR/9f8kyApqtwIOfej9N6\r\n941D5uXvk3qR/ONKdtNqsQYOAlj/dxab08OC7MsMlBcJi7WHFrPdIucBHbhD\r\n1i2l/Nyv72T+u8LAc2jzsY8uuvSZMn2ARFK61UpYAyAZkZUhb57FrEwUdQ9t\r\nLDFSRYLTpiqadrXOz+KzGaHE4pzFyjWhJmLTEpV2S68kwkjKK+xDe71SAkfr\r\nFHdUlvAWP1bLw5eimNwwMOa+ClGh4MYUmBKLeOJ50w8xomsEXJErr0/JD9s3\r\nt46UEEzztT0YaS3OPTAU0WIZZon1bQDAGds=\r\n=30U/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "d6c09b749eaba19bca643522d95fc316b3aa65c2", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "publishConfig": {"tag": "legacy-v7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.5_1680540598253_0.29439372305106004", "host": "s3://npm-registry-packages"}}, "8.0.3": {"name": "minimatch", "version": "8.0.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@8.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "0415cb9bb0c1d8ac758c8a673eb1d288e13f5e75", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.3.tgz", "fileCount": 57, "integrity": "sha512-tEEvU9TkZgnFDCtpnrEYnPsjT7iUx42aXfs4bzmQ5sMA09/6hZY0jeZcGkXyDagiBOvkUjNo8Viom+Me6+2x7g==", "signatures": [{"sig": "MEUCIAwzomo7Rei+sG4Yc/wBVQAjCCUNeVaZgfhaI113igptAiEAiCGgoOcyy9hqeDDyM08BjfA0PTvJSouqZjez06Vxkyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKwQOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKuBAAolCz/guOu2/a54E9T/7t1cQH53R/YUdne+MoOhHihimOvcBs\r\nE3Vj7BoGxlL85qiZRp43ySwRl0RytRPq6ya03DFpeSiCbTuBFlWJjcNQPzuM\r\nKyiqkY6evfR0GLt9I08N/M+olcYOr6yCEAl51AskNDxgzKy0EsK91WHPLPRe\r\ni082Mn+40/ZqA1oq96IWI3xFI7PTjBB6/dGqTbSaO4WX3cOwyEnpL7uS0Bh/\r\nH7BsNW6EJJ9cVWsLBFEI/UtXG1QmJ0eVBSGMQtCJ6bPdMekpNMJYlU+kQEfx\r\n+aLdpos57QFmVoKvlwRplwaFIwqVEMIFA/e2xpcOE4lDE/1iwm7ndkyK/jXk\r\n4PDdIh82i1nh+YIAGcC68dZVhlK0wzY0Tq/QSjSfbLeqnldAdKNj2Qoq98eK\r\n12WLxhlXs7Ar9ARECfB6wEWkNdgyHLIUF1NaojWFSTSx2FvLyj2mHKlUmhY2\r\nk6J0KcWIKJp0F0oVLcGBCHOpdBqgpsGM82RZPSdUNRhxf41FsgBcqeyazFSj\r\n+vwX7JmWQefam230oi8TcyQ585BI+GzMQ9tFESNYBXZwS4ypqLhuQvo8NcxF\r\ns3qey8SPklB2QDNrnaeMwlq4COecboxugUH+wRfKiaVm/Tasr27fswq8a6iR\r\n6KtIdjxL6KCvoNu9MYhbw21oZI8sj4VoYnA=\r\n=fRv6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "1c0a3cd1e64504e39e3e3ed67f162b4d8a5b3555", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_8.0.3_1680540686491_0.22076753848167918", "host": "s3://npm-registry-packages"}}, "8.0.4": {"name": "minimatch", "version": "8.0.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@8.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "847c1b25c014d4e9a7f68aaf63dedd668a626229", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.4.tgz", "fileCount": 57, "integrity": "sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==", "signatures": [{"sig": "MEUCID+azTFTdI5oP/8gpzzMAVPJF+AyKUR2wNjrqypoqHb9AiEAwGfxwFWZ5bSILuAj/uFBOQ5eFjdw2dE4HoMFGDU7fw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMx1pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4nw//YywOsipnggjuKIcF2oJkzBzq+dak/JXOOlnCJ4FWgbvIVzQC\r\n5Q3qC840LhjcGEbBOJ1IHYNo6PQdQZh782CLD/H7wfwc9YAQkqSj9skPkaz4\r\n00mVWt7Ac7Vuwo7YVh4f76lNwkJHIbPMc4/fZUj8fKqasuF/wPI5iAhottt9\r\nryor6vzxmTGzRrvYLXUt80wHnV0SsKkgFdGAP8od0U/1jryyjtusvmDrSeVv\r\nTCy6uJAcuqIOWo8LBV4zWyXvALOQGWqCTT2Sy5uDPYP5K3oyTUdVpArfAUrf\r\nzNCW+Zkn3T6LzPtmf7uNP3RF1xKUOKu5tni8gJdpDHUz0VZSkvlNghqc8cxn\r\no1XyaCIsSuUq2Cw2N/mbXLDi0SNh3kCIVb5A6SE4w6id8MdtNX/qDUSzwNeM\r\nxSCCHVAJ8nfu1EBW3DhaAv2cggn7aDSVZat1QzNRhFb/ARyMPzVv04C5klIS\r\nslfh4K42WzMGWhEn+AOfRCymO5naRW3x4OdYMWSTKYa7qdIhV/3yzyyvNmUj\r\nzGDQT3TSIG+vR275QTQOD59OKQWK4llske+d6yrHk160MduYkQpv5daXyhqu\r\nDWS1PydYw4luHLkiV6O4vZyxKuh5eVVmc5oXYHJztHvzGQX+9fmoelEq9yJh\r\nlXQp1Zgkf13+Bk/rXEfHzyWj+SE46tq5VqQ=\r\n=MX/2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "3093d33a615e2fcf7900fa1ca6916d5151a96072", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_8.0.4_1681071465130_0.1362650847977107", "host": "s3://npm-registry-packages"}}, "7.4.6": {"name": "minimatch", "version": "7.4.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@7.4.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "845d6f254d8f4a5e4fd6baf44d5f10c8448365fb", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-7.4.6.tgz", "fileCount": 41, "integrity": "sha512-sBz8G/YjVniEz6lKPNpKxXwazJe4c19fEfV2GDMX6AjFz+MX9uDWIZW8XreVhkFW3fkIdTv/gxWr/Kks5FFAVw==", "signatures": [{"sig": "MEYCIQDv9VHpC6irXMiLiB8/yzdE0BA7nFF2tBh7WXs2+fXJ6gIhAJNEMYwzBam/YGM7hPbX9lhJ6S7XgAAXM6KbyCX3hqQ3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 368067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMx2kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDaw/+KXTGkugSppF/Omhp5sYRSH9GqPAKxQCxA2Y21h4EfnC2HTLo\r\nVjStZWPZwC76RTg/6gFmbKGlN+7+fYvV/f4SRNcjBqw2BTNUzIMHk7M9x5to\r\n4j5li0BhEVNCsZNbSjvMVU2712e0FAwbYihUlWpGXpGpMXC7h1Viz8nIlSIb\r\nsnOo3V48BKjvIppmVANzI2pRPAgf+Tr/u4XRDSXkl4416u5YYCUGsEidBBkC\r\ntwE87+82qwgLZ56uRJGL+fUSggpgyl6B/lyWZwlWR8q+EXsUBQZg/U/GMBg4\r\nyzKih04lFqcD1xWC8JWTIlPXRc17jHFxSH/JaCcGqikFL+OdXqdGgptF1jg+\r\nQQGHQp9KQEQGFgkbTeXPfJKNKUAUICLZIrpJHE/J2IicGsuwKV7daTjlurK2\r\nRF+M2GVlYEnpch2nxwez/4Whx+QaRlKgqI6lvPszWxLxcUg8fz3KvJ+LLyFn\r\nKSvgbsWAINgd3ET+Z5QBIebkIzg10XWcaOWxn/tERLuRsH1ZjHJN9JNglw8A\r\nQFR+9fGoK6gYA/K8Cqq+JQSTnRG/C2GfrphXe8WiDA3MmhY00RiL0ORMbK11\r\nAD9/i8Bb1Q1IV6FmtKwz9AoCfaIrL4obniD5ewSk1kkObjQw1KsfUGLFSH7/\r\nHb+kOq3GMxhYe/rLD0GPcE8QNOI56xhp2ww=\r\n=o3Tu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=10"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "7fb6a36a2d33ffa8266148cc5b5b4ac9a62ee7cd", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "publishConfig": {"tag": "legacy-v7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_7.4.6_1681071524029_0.12020245940117014", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "minimatch", "version": "9.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@9.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "bfc8e88a1c40ffd40c172ddac3decb8451503b56", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.0.tgz", "fileCount": 53, "integrity": "sha512-0jJj8AvgKqWN05mrwuqi8QYKx1WmYSUoKSxu5Qhs9prezTz10sxAHGNZe9J9cqIJzta8DWsleh2KaVaLl6Ru2w==", "signatures": [{"sig": "MEQCIElefaysQGa8Nr3MLv58NzXmSjfdYR3wWizfsMCgX24aAiBSZrwhZZh7ZcwK6uHf5X2o8zKsD61fihIeNZWeL7f3yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMzjrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTJw//dW+PAibru2az/UByduc47XvZ19uVQGTntL0J7xL1w53O26MV\r\nFNXOdOvAgU25/0WADEAzpn+VwzqeZnoblnD5CpF38N0IhGICT8LuZZmVZZ9e\r\nSVdqv6kqbq2sWrAQOvci2eQs3P+WfakKhhKBpE7QT9J0q+BqR2NWiwUzut9r\r\ndFTjucQI3YRfTciHCchCNowfbW/YK9HIhCConuIc5MF5OF5/eP7HCAPN929h\r\nYrNmXouFoJP2odTyirmdhCuDfjNLzS+L+/+xC/0/kjr7GRsujJ2zIjeuCf3u\r\nyH8Cvztb+FkGqh/qcf+QWPNjUbg9RUzuhAG5VLcD7C0sLY3VuWMWzvoa5bgy\r\nSd4RXUJRMkv2lqAm5yoeAEnb6hKTgHQTGQu3kY5QLj0V76yGyipuRn/5RYGK\r\noDXYfCISM5fWI3lTI/rIg1ANy8SQertDtP+U8yHcYx69K67iF6KBKSCbnMeW\r\nC4sOlA0Ia0Ijl3IiilKEMjBtEU1p7AqV+4EtqA8u88G8jaX/mcXKMfh8lhut\r\nlJPL41ecQO1qJlxxwjzH85tdQyEOU5pFtr2+/5O1RITxMeUgvJvhYKhzs7Bq\r\nmPU67HwXuRiQ5ra7XRUYemreEHbc6OwHGkT96hGP2H0SnrHwKfvR1lIpeb6V\r\ndoNQ+xclWtKpmB0vikRw+V4oonu6xwt/+Yk=\r\n=KymT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "b95cb1e4404ce374e447b3b1bfde837e74f139bd", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_9.0.0_1681078507424_0.33681436676552456", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "minimatch", "version": "9.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@9.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "8a555f541cf976c622daf078bb28f29fb927c253", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.1.tgz", "fileCount": 53, "integrity": "sha512-0jWhJpD/MdhPXwPuiRkCbfYfSKp2qnn2eOc279qI7f+osl/l+prKSrvhg157zSYvx/1nmgn2NqdT6k2Z7zSH9w==", "signatures": [{"sig": "MEQCIALzqgmZjKE3691Y/WQKusEAyMjnFykDfyyIrlrNGyRGAiAY91n9vj7MzXlwH/sFYqtD9OmOZ7ZE2OGGOXGOIZAKhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428478}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "f1b11e7906818f0ae455a82fe7c1bfcf655c786d", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_9.0.1_1684616039844_0.6499629025752791", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "minimatch", "version": "9.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@9.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "397e387fff22f6795844d00badc903a3d5de7057", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.2.tgz", "fileCount": 53, "integrity": "sha512-PZOT9g5v2ojiTL7r1xF6plNHLtOeTpSlDI007As2NlA2aYBMfVom17yqa6QzhmDP8QOhn7LjHTg7DFCVSSa6yg==", "signatures": [{"sig": "MEUCIQCClDIYBp4y7cbhQbhk3rlDIYUJswjBJRndD+upNcEhLQIgdjZX6Z9JQDRbzPabt1WqfbB4/YVRvoFKp7X4GhrGmjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433704}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "b7bd6d6db0b2521c12f654895971cf81790a5257", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_9.0.2_1687554743457_0.763619162681844", "host": "s3://npm-registry-packages"}}, "9.0.3": {"name": "minimatch", "version": "9.0.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@9.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "a6e00c3de44c3a542bfaae70abfc22420a6da825", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz", "fileCount": 53, "integrity": "sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==", "signatures": [{"sig": "MEYCIQCamyK0PER6ghY490MnmvqR9yobciGJrpbT+cf0/tstiwIhANKmkVC3BveaqUJrdGHzs/OW3ygmshF5Tod3RkppHuDA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433705}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "f8b46a317a7695c342402cde52c8b0f7a47add17", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.7", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.8", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_9.0.3_1688663147247_0.36720562387508116", "host": "s3://npm-registry-packages"}}, "9.0.4": {"name": "minimatch", "version": "9.0.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@9.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "8e49c731d1749cbec05050ee5145147b32496a51", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.4.tgz", "fileCount": 53, "integrity": "sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==", "signatures": [{"sig": "MEQCIEHx6wDga4HI7ZIwQJ5815qkSydqrccn45jiUWd+XGywAiAlO83AcJHK9yJ7o2ApIF8drPKD2Ln00MXM9eb/4zSfEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 434900}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "cb4be48a55d64b3a40a745d4a8eb4d1b06507277", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "tshy": "^1.12.0", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.8", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_9.0.4_1711654976118_0.11283678243962436", "host": "s3://npm-registry-packages"}}, "9.0.5": {"name": "minimatch", "version": "9.0.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@9.0.5", "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "d74f9dd6b57d83d8e98cfb82133b03978bc929e5", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "fileCount": 53, "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "signatures": [{"sig": "MEYCIQC8i1XVlxHUOKd0etL7moPA7FuIE5d+E6J4fd1YQj0btgIhAMtyRwTteIb7e0oR/SIFP0LK/JFECg7Aj3KbraAX9pih", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435003}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "0de7f45232cad5e3e49e4eb7cd9b6e124ed04b84", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "tshy": "^1.12.0", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.8", "typescript": "^4.9.3", "@types/node": "^18.15.11", "@types/brace-expansion": "^1.1.0", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_9.0.5_1719355262600_0.4346187038816942", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "minimatch", "version": "10.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@10.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "bf7b5028e151f3a8db2970a1d36523b6f610868b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.0.tgz", "fileCount": 53, "integrity": "sha512-S4phymWe5NHWbTV8sAlyNQfkmdhvaoHX43x4yLtJBjw2zJtEuzkihDjV5uKq+D/EoMkjbG6msw3ubbSd1pGkyg==", "signatures": [{"sig": "MEQCIAw8dNW+YvM6CrTFZ/M8aoSL/a1fuF6jycL3Bsnm8sgOAiAxp0R2h4KMBh198Ou752Sl5aHI5GIALZE/XTvv3NavJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438775}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "346685ced5203464bb10fd3d4dfa6964f6102ede", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"brace-expansion": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.1", "mkdirp": "^3.0.1", "typedoc": "^0.26.3", "prettier": "^3.3.2", "typescript": "^5.5.3", "@types/node": "^20.14.10", "@types/brace-expansion": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_10.0.0_1720475591563_0.1362400401155739", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "minimatch", "version": "10.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@10.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "ce0521856b453c86e25f2c4c0d03e6ff7ddc440b", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.1.tgz", "fileCount": 53, "integrity": "sha512-ethXTt3SGGR+95gudmqJ1eNhRO7eGEGIgYA9vnPatK4/etz2MEVDno5GMCibdMTuBMyElzIlgxMna3K94XDIDQ==", "signatures": [{"sig": "MEUCIQCAYhDBiow3JZoo/Wjad4/ocXc9Ijsec0bAReaf4pYqYQIgMDAifdJ7f0bom8/4uSrttUwr4NUIdWu9FMYhcy9wvZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438775}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "0569cd3373408f9d701d3aab187b3f43a24a0db7", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"brace-expansion": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.1", "mkdirp": "^3.0.1", "typedoc": "^0.26.3", "prettier": "^3.3.2", "typescript": "^5.5.3", "@types/node": "^20.14.10", "@types/brace-expansion": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_10.0.1_1720479745386_0.5053831268283802", "host": "s3://npm-registry-packages"}}, "10.0.2": {"name": "minimatch", "version": "10.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "minimatch@10.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minimatch#readme", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dist": {"shasum": "99c15b4e4f7e71b354e086e20b7f6c4d65dd15f3", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.2.tgz", "fileCount": 53, "integrity": "sha512-+9TJCIYXgZ2Dm5LxVCFsa8jOm+evMwXHFI0JM1XROmkfkpz8/iLLDh+TwSmyIBrs6C6Xu9294/fq8cBA+P6AqA==", "signatures": [{"sig": "MEUCIQCDnk8ahrTDmM+aNXfKqQhMkePOXoywx+OwGwcrtdpDNwIgIZ+MYYTXnJntIMPcWXTGV2CeeoSNQLQRvoaTYtKXlUs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 438745}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "ad70a8575e6107bb94e8eef760d54bdaa52445a8", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/minimatch.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "a glob matcher in javascript", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"brace-expansion": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^21.1.0", "tshy": "^3.0.2", "mkdirp": "^3.0.1", "typedoc": "^0.28.5", "prettier": "^3.3.2", "@types/node": "^24.0.0", "@types/brace-expansion": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/minimatch_10.0.2_1749665367994_0.30020191400875884", "host": "s3://npm-registry-packages-npm-production"}}, "10.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "minimatch", "description": "a glob matcher in javascript", "version": "10.0.3", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "engines": {"node": "20 || >=22"}, "devDependencies": {"@types/brace-expansion": "^1.1.2", "@types/node": "^24.0.0", "mkdirp": "^3.0.1", "prettier": "^3.3.2", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.5"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "license": "ISC", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "module": "./dist/esm/index.js", "dependencies": {"@isaacs/brace-expansion": "^5.0.0"}, "_id": "minimatch@10.0.3", "gitHead": "2c65ee2c57964cae0ad55a612e2c76dfff7cb16a", "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "homepage": "https://github.com/isaacs/minimatch#readme", "_nodeVersion": "22.14.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw==", "shasum": "cf7a0314a16c4d9ab73a7730a0e8e3c3502d47aa", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.3.tgz", "fileCount": 53, "unpackedSize": 438647, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDG/hNo3JS+apEeN+YR9mnu8n7h1CKe3xAS9xCYfnkL3AIgOAPEl7soTOCXtsUxkvxM6tSar6wu+Cd1H3LW5VPLbZ8="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/minimatch_10.0.3_1749759567659_0.3204073784177832"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-07-16T08:52:46.242Z", "modified": "2025-06-12T20:19:28.018Z", "0.0.1": "2011-07-16T08:52:46.751Z", "0.0.2": "2011-07-16T17:57:12.490Z", "0.0.4": "2011-07-29T19:13:01.148Z", "0.0.5": "2011-12-14T02:32:05.891Z", "0.1.1": "2011-12-31T02:22:30.836Z", "0.1.2": "2012-01-04T02:28:33.356Z", "0.1.3": "2012-01-04T09:14:19.756Z", "0.1.4": "2012-01-19T00:14:01.941Z", "0.1.5": "2012-02-04T19:32:24.992Z", "0.2.0": "2012-02-22T11:04:45.694Z", "0.2.2": "2012-03-22T05:22:24.613Z", "0.2.3": "2012-03-29T01:37:56.651Z", "0.2.4": "2012-03-29T01:48:13.065Z", "0.2.5": "2012-06-04T20:57:13.340Z", "0.2.6": "2012-08-13T16:42:08.866Z", "0.2.7": "2012-10-04T03:49:50.719Z", "0.2.8": "2012-10-25T15:24:02.744Z", "0.2.9": "2012-10-25T15:34:02.432Z", "0.2.10": "2013-02-25T16:21:43.150Z", "0.2.11": "2013-02-25T16:23:45.015Z", "0.2.12": "2013-04-12T19:28:43.983Z", "0.2.13": "2013-12-16T06:02:07.117Z", "0.2.14": "2013-12-16T22:01:16.541Z", "0.3.0": "2014-05-13T00:47:58.044Z", "0.4.0": "2014-07-11T23:25:00.225Z", "1.0.0": "2014-07-28T21:29:33.094Z", "2.0.0": "2014-12-01T02:12:02.529Z", "2.0.1": "2014-12-01T16:30:35.174Z", "2.0.2": "2015-03-10T00:34:30.286Z", "2.0.3": "2015-03-10T02:03:12.334Z", "2.0.4": "2015-03-12T17:34:43.679Z", "2.0.5": "2015-04-29T14:37:27.431Z", "2.0.6": "2015-04-29T15:25:34.731Z", "2.0.7": "2015-04-29T15:43:59.337Z", "2.0.8": "2015-05-19T01:38:00.517Z", "2.0.9": "2015-07-18T23:03:23.099Z", "2.0.10": "2015-07-23T01:51:41.664Z", "3.0.0": "2015-09-27T18:18:59.997Z", "3.0.2": "2016-06-17T20:13:02.079Z", "3.0.3": "2016-08-08T17:45:22.959Z", "3.0.4": "2017-05-07T18:11:10.900Z", "3.0.5": "2022-02-06T20:28:04.652Z", "3.0.6": "2022-02-12T23:58:17.727Z", "4.0.0": "2022-02-13T00:37:33.892Z", "4.1.0": "2022-02-13T00:58:48.018Z", "3.1.0": "2022-02-13T01:03:22.958Z", "3.1.1": "2022-02-13T04:01:44.444Z", "3.0.7": "2022-02-13T04:03:33.143Z", "4.1.1": "2022-02-13T04:22:13.854Z", "4.2.0": "2022-02-15T16:03:28.444Z", "4.2.1": "2022-02-15T16:35:28.146Z", "5.0.0": "2022-02-15T16:50:03.151Z", "3.1.2": "2022-02-15T20:32:43.510Z", "3.0.8": "2022-02-15T20:33:33.732Z", "5.0.1": "2022-02-24T17:58:20.816Z", "5.1.0": "2022-05-16T16:13:11.979Z", "5.1.1": "2022-11-29T20:33:52.832Z", "5.1.2": "2022-12-20T15:12:13.918Z", "5.1.3": "2023-01-14T18:54:23.367Z", "5.1.4": "2023-01-14T19:09:39.409Z", "6.0.0": "2023-01-14T21:07:02.827Z", "6.0.1": "2023-01-15T17:37:41.372Z", "6.0.2": "2023-01-15T21:26:11.666Z", "6.0.3": "2023-01-15T23:08:02.448Z", "6.0.4": "2023-01-16T01:55:01.750Z", "6.1.0": "2023-01-17T07:11:06.269Z", "6.1.1": "2023-01-17T14:57:09.235Z", "6.1.2": "2023-01-17T15:02:37.126Z", "5.1.5": "2023-01-17T15:04:11.073Z", "4.2.2": "2023-01-17T15:09:14.295Z", "6.1.3": "2023-01-17T17:24:11.735Z", "6.1.4": "2023-01-17T17:46:31.707Z", "5.1.6": "2023-01-17T19:46:37.483Z", "4.2.3": "2023-01-17T19:47:04.835Z", "6.1.5": "2023-01-17T22:17:33.109Z", "6.1.6": "2023-01-22T17:52:06.355Z", "6.1.7": "2023-02-11T20:33:31.384Z", "6.1.8": "2023-02-11T21:10:03.494Z", "6.1.9": "2023-02-13T06:54:04.311Z", "6.1.10": "2023-02-13T08:20:38.903Z", "6.2.0": "2023-02-13T08:58:22.378Z", "7.0.0": "2023-02-20T00:45:53.556Z", "7.0.1": "2023-02-22T02:01:55.015Z", "7.1.0": "2023-02-22T23:45:29.974Z", "7.1.1": "2023-02-24T00:36:24.751Z", "7.1.2": "2023-02-24T22:56:58.174Z", "7.1.3": "2023-02-25T02:07:45.997Z", "7.1.4": "2023-02-26T01:03:07.458Z", "7.2.0": "2023-02-26T09:13:44.871Z", "7.3.0": "2023-02-27T19:58:12.599Z", "7.4.0": "2023-03-01T06:59:21.929Z", "7.4.1": "2023-03-01T07:59:55.185Z", "7.4.2": "2023-03-01T20:13:50.508Z", "7.4.3": "2023-03-22T18:52:11.270Z", "7.4.4": "2023-04-01T23:44:08.895Z", "8.0.0": "2023-04-02T03:33:52.141Z", "8.0.1": "2023-04-02T03:39:08.726Z", "8.0.2": "2023-04-02T03:41:11.591Z", "7.4.5": "2023-04-03T16:49:58.486Z", "8.0.3": "2023-04-03T16:51:26.689Z", "8.0.4": "2023-04-09T20:17:45.328Z", "7.4.6": "2023-04-09T20:18:44.178Z", "9.0.0": "2023-04-09T22:15:07.639Z", "9.0.1": "2023-05-20T20:53:59.996Z", "9.0.2": "2023-06-23T21:12:23.706Z", "9.0.3": "2023-07-06T17:05:47.404Z", "9.0.4": "2024-03-28T19:42:56.374Z", "9.0.5": "2024-06-25T22:41:02.824Z", "10.0.0": "2024-07-08T21:53:11.825Z", "10.0.1": "2024-07-08T23:02:25.545Z", "10.0.2": "2025-06-11T18:09:28.209Z", "10.0.3": "2025-06-12T20:19:27.846Z"}, "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "ISC", "homepage": "https://github.com/isaacs/minimatch#readme", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "description": "a glob matcher in javascript", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# minimatch\n\nA minimal matching utility.\n\nThis is the matching library used internally by npm.\n\nIt works by converting glob expressions into JavaScript `RegExp`\nobjects.\n\n## Usage\n\n```js\n// hybrid module, load with require() or import\nimport { minimatch } from 'minimatch'\n// or:\nconst { minimatch } = require('minimatch')\n\nminimatch('bar.foo', '*.foo') // true!\nminimatch('bar.foo', '*.bar') // false!\nminimatch('bar.foo', '*.+(bar|foo)', { debug: true }) // true, and noisy!\n```\n\n## Features\n\nSupports these glob features:\n\n- Brace Expansion\n- Extended glob matching\n- \"Globstar\" `**` matching\n- [Posix character\n  classes](https://www.gnu.org/software/bash/manual/html_node/Pattern-Matching.html),\n  like `[[:alpha:]]`, supporting the full range of Unicode\n  characters. For example, `[[:alpha:]]` will match against\n  `'é'`, though `[a-zA-Z]` will not. Collating symbol and set\n  matching is not supported, so `[[=e=]]` will _not_ match `'é'`\n  and `[[.ch.]]` will not match `'ch'` in locales where `ch` is\n  considered a single character.\n\nSee:\n\n- `man sh`\n- `man bash` [Pattern\n  Matching](https://www.gnu.org/software/bash/manual/html_node/Pattern-Matching.html)\n- `man 3 fnmatch`\n- `man 5 gitignore`\n\n## Windows\n\n**Please only use forward-slashes in glob expressions.**\n\nThough windows uses either `/` or `\\` as its path separator, only `/`\ncharacters are used by this glob implementation. You must use\nforward-slashes **only** in glob expressions. Back-slashes in patterns\nwill always be interpreted as escape characters, not path separators.\n\nNote that `\\` or `/` _will_ be interpreted as path separators in paths on\nWindows, and will match against `/` in glob expressions.\n\nSo just always use `/` in patterns.\n\n### UNC Paths\n\nOn Windows, UNC paths like `//?/c:/...` or\n`//ComputerName/Share/...` are handled specially.\n\n- Patterns starting with a double-slash followed by some\n  non-slash characters will preserve their double-slash. As a\n  result, a pattern like `//*` will match `//x`, but not `/x`.\n- Patterns staring with `//?/<drive letter>:` will _not_ treat\n  the `?` as a wildcard character. Instead, it will be treated\n  as a normal string.\n- Patterns starting with `//?/<drive letter>:/...` will match\n  file paths starting with `<drive letter>:/...`, and vice versa,\n  as if the `//?/` was not present. This behavior only is\n  present when the drive letters are a case-insensitive match to\n  one another. The remaining portions of the path/pattern are\n  compared case sensitively, unless `nocase:true` is set.\n\nNote that specifying a UNC path using `\\` characters as path\nseparators is always allowed in the file path argument, but only\nallowed in the pattern argument when `windowsPathsNoEscape: true`\nis set in the options.\n\n## Minimatch Class\n\nCreate a minimatch object by instantiating the `minimatch.Minimatch` class.\n\n```javascript\nvar Minimatch = require('minimatch').Minimatch\nvar mm = new Minimatch(pattern, options)\n```\n\n### Properties\n\n- `pattern` The original pattern the minimatch object represents.\n- `options` The options supplied to the constructor.\n- `set` A 2-dimensional array of regexp or string expressions.\n  Each row in the\n  array corresponds to a brace-expanded pattern. Each item in the row\n  corresponds to a single path-part. For example, the pattern\n  `{a,b/c}/d` would expand to a set of patterns like:\n\n        [ [ a, d ]\n        , [ b, c, d ] ]\n\n  If a portion of the pattern doesn't have any \"magic\" in it\n  (that is, it's something like `\"foo\"` rather than `fo*o?`), then it\n  will be left as a string rather than converted to a regular\n  expression.\n\n- `regexp` Created by the `makeRe` method. A single regular expression\n  expressing the entire pattern. This is useful in cases where you wish\n  to use the pattern somewhat like `fnmatch(3)` with `FNM_PATH` enabled.\n- `negate` True if the pattern is negated.\n- `comment` True if the pattern is a comment.\n- `empty` True if the pattern is `\"\"`.\n\n### Methods\n\n- `makeRe()` Generate the `regexp` member if necessary, and return it.\n  Will return `false` if the pattern is invalid.\n- `match(fname)` Return true if the filename matches the pattern, or\n  false otherwise.\n- `matchOne(fileArray, patternArray, partial)` Take a `/`-split\n  filename, and match it against a single row in the `regExpSet`. This\n  method is mainly for internal use, but is exposed so that it can be\n  used by a glob-walker that needs to avoid excessive filesystem calls.\n- `hasMagic()` Returns true if the parsed pattern contains any\n  magic characters. Returns false if all comparator parts are\n  string literals. If the `magicalBraces` option is set on the\n  constructor, then it will consider brace expansions which are\n  not otherwise magical to be magic. If not set, then a pattern\n  like `a{b,c}d` will return `false`, because neither `abd` nor\n  `acd` contain any special glob characters.\n\n  This does **not** mean that the pattern string can be used as a\n  literal filename, as it may contain magic glob characters that\n  are escaped. For example, the pattern `\\\\*` or `[*]` would not\n  be considered to have magic, as the matching portion parses to\n  the literal string `'*'` and would match a path named `'*'`,\n  not `'\\\\*'` or `'[*]'`. The `minimatch.unescape()` method may\n  be used to remove escape characters.\n\nAll other methods are internal, and will be called as necessary.\n\n### minimatch(path, pattern, options)\n\nMain export. Tests a path against the pattern using the options.\n\n```javascript\nvar isJS = minimatch(file, '*.js', { matchBase: true })\n```\n\n### minimatch.filter(pattern, options)\n\nReturns a function that tests its\nsupplied argument, suitable for use with `Array.filter`. Example:\n\n```javascript\nvar javascripts = fileList.filter(minimatch.filter('*.js', { matchBase: true }))\n```\n\n### minimatch.escape(pattern, options = {})\n\nEscape all magic characters in a glob pattern, so that it will\nonly ever match literal strings\n\nIf the `windowsPathsNoEscape` option is used, then characters are\nescaped by wrapping in `[]`, because a magic character wrapped in\na character class can only be satisfied by that exact character.\n\nSlashes (and backslashes in `windowsPathsNoEscape` mode) cannot\nbe escaped or unescaped.\n\n### minimatch.unescape(pattern, options = {})\n\nUn-escape a glob string that may contain some escaped characters.\n\nIf the `windowsPathsNoEscape` option is used, then square-brace\nescapes are removed, but not backslash escapes. For example, it\nwill turn the string `'[*]'` into `*`, but it will not turn\n`'\\\\*'` into `'*'`, because `\\` is a path separator in\n`windowsPathsNoEscape` mode.\n\nWhen `windowsPathsNoEscape` is not set, then both brace escapes\nand backslash escapes are removed.\n\nSlashes (and backslashes in `windowsPathsNoEscape` mode) cannot\nbe escaped or unescaped.\n\n### minimatch.match(list, pattern, options)\n\nMatch against the list of\nfiles, in the style of fnmatch or glob. If nothing is matched, and\noptions.nonull is set, then return a list containing the pattern itself.\n\n```javascript\nvar javascripts = minimatch.match(fileList, '*.js', { matchBase: true })\n```\n\n### minimatch.makeRe(pattern, options)\n\nMake a regular expression object from the pattern.\n\n## Options\n\nAll options are `false` by default.\n\n### debug\n\nDump a ton of stuff to stderr.\n\n### nobrace\n\nDo not expand `{a,b}` and `{1..3}` brace sets.\n\n### noglobstar\n\nDisable `**` matching against multiple folder names.\n\n### dot\n\nAllow patterns to match filenames starting with a period, even if\nthe pattern does not explicitly have a period in that spot.\n\nNote that by default, `a/**/b` will **not** match `a/.d/b`, unless `dot`\nis set.\n\n### noext\n\nDisable \"extglob\" style patterns like `+(a|b)`.\n\n### nocase\n\nPerform a case-insensitive match.\n\n### nocaseMagicOnly\n\nWhen used with `{nocase: true}`, create regular expressions that\nare case-insensitive, but leave string match portions untouched.\nHas no effect when used without `{nocase: true}`\n\nUseful when some other form of case-insensitive matching is used,\nor if the original string representation is useful in some other\nway.\n\n### nonull\n\nWhen a match is not found by `minimatch.match`, return a list containing\nthe pattern itself if this option is set. When not set, an empty list\nis returned if there are no matches.\n\n### magicalBraces\n\nThis only affects the results of the `Minimatch.hasMagic` method.\n\nIf the pattern contains brace expansions, such as `a{b,c}d`, but\nno other magic characters, then the `Minimatch.hasMagic()` method\nwill return `false` by default. When this option set, it will\nreturn `true` for brace expansion as well as other magic glob\ncharacters.\n\n### matchBase\n\nIf set, then patterns without slashes will be matched\nagainst the basename of the path if it contains slashes. For example,\n`a?b` would match the path `/xyz/123/acb`, but not `/xyz/acb/123`.\n\n### nocomment\n\nSuppress the behavior of treating `#` at the start of a pattern as a\ncomment.\n\n### nonegate\n\nSuppress the behavior of treating a leading `!` character as negation.\n\n### flipNegate\n\nReturns from negate expressions the same as if they were not negated.\n(Ie, true on a hit, false on a miss.)\n\n### partial\n\nCompare a partial path to a pattern. As long as the parts of the path that\nare present are not contradicted by the pattern, it will be treated as a\nmatch. This is useful in applications where you're walking through a\nfolder structure, and don't yet have the full path, but want to ensure that\nyou do not walk down paths that can never be a match.\n\nFor example,\n\n```js\nminimatch('/a/b', '/a/*/c/d', { partial: true }) // true, might be /a/b/c/d\nminimatch('/a/b', '/**/d', { partial: true }) // true, might be /a/b/.../d\nminimatch('/x/y/z', '/a/**/z', { partial: true }) // false, because x !== a\n```\n\n### windowsPathsNoEscape\n\nUse `\\\\` as a path separator _only_, and _never_ as an escape\ncharacter. If set, all `\\\\` characters are replaced with `/` in\nthe pattern. Note that this makes it **impossible** to match\nagainst paths containing literal glob pattern characters, but\nallows matching with patterns constructed using `path.join()` and\n`path.resolve()` on Windows platforms, mimicking the (buggy!)\nbehavior of earlier versions on Windows. Please use with\ncaution, and be mindful of [the caveat about Windows\npaths](#windows).\n\nFor legacy reasons, this is also set if\n`options.allowWindowsEscape` is set to the exact value `false`.\n\n### windowsNoMagicRoot\n\nWhen a pattern starts with a UNC path or drive letter, and in\n`nocase:true` mode, do not convert the root portions of the\npattern into a case-insensitive regular expression, and instead\nleave them as strings.\n\nThis is the default when the platform is `win32` and\n`nocase:true` is set.\n\n### preserveMultipleSlashes\n\nBy default, multiple `/` characters (other than the leading `//`\nin a UNC path, see \"UNC Paths\" above) are treated as a single\n`/`.\n\nThat is, a pattern like `a///b` will match the file path `a/b`.\n\nSet `preserveMultipleSlashes: true` to suppress this behavior.\n\n### optimizationLevel\n\nA number indicating the level of optimization that should be done\nto the pattern prior to parsing and using it for matches.\n\nGlobstar parts `**` are always converted to `*` when `noglobstar`\nis set, and multiple adjacent `**` parts are converted into a\nsingle `**` (ie, `a/**/**/b` will be treated as `a/**/b`, as this\nis equivalent in all cases).\n\n- `0` - Make no further changes. In this mode, `.` and `..` are\n  maintained in the pattern, meaning that they must also appear\n  in the same position in the test path string. Eg, a pattern\n  like `a/*/../c` will match the string `a/b/../c` but not the\n  string `a/c`.\n- `1` - (default) Remove cases where a double-dot `..` follows a\n  pattern portion that is not `**`, `.`, `..`, or empty `''`. For\n  example, the pattern `./a/b/../*` is converted to `./a/*`, and\n  so it will match the path string `./a/c`, but not the path\n  string `./a/b/../c`. Dots and empty path portions in the\n  pattern are preserved.\n- `2` (or higher) - Much more aggressive optimizations, suitable\n  for use with file-walking cases:\n\n  - Remove cases where a double-dot `..` follows a pattern\n    portion that is not `**`, `.`, or empty `''`. Remove empty\n    and `.` portions of the pattern, where safe to do so (ie,\n    anywhere other than the last position, the first position, or\n    the second position in a pattern starting with `/`, as this\n    may indicate a UNC path on Windows).\n  - Convert patterns containing `<pre>/**/../<p>/<rest>` into the\n    equivalent `<pre>/{..,**}/<p>/<rest>`, where `<p>` is a\n    a pattern portion other than `.`, `..`, `**`, or empty\n    `''`.\n  - Dedupe patterns where a `**` portion is present in one and\n    omitted in another, and it is not the final path portion, and\n    they are otherwise equivalent. So `{a/**/b,a/b}` becomes\n    `a/**/b`, because `**` matches against an empty path portion.\n  - Dedupe patterns where a `*` portion is present in one, and a\n    non-dot pattern other than `**`, `.`, `..`, or `''` is in the\n    same position in the other. So `a/{*,x}/b` becomes `a/*/b`,\n    because `*` can match against `x`.\n\n  While these optimizations improve the performance of\n  file-walking use cases such as [glob](http://npm.im/glob) (ie,\n  the reason this module exists), there are cases where it will\n  fail to match a literal string that would have been matched in\n  optimization level 1 or 0.\n\n  Specifically, while the `Minimatch.match()` method will\n  optimize the file path string in the same ways, resulting in\n  the same matches, it will fail when tested with the regular\n  expression provided by `Minimatch.makeRe()`, unless the path\n  string is first processed with\n  `minimatch.levelTwoFileOptimize()` or similar.\n\n### platform\n\nWhen set to `win32`, this will trigger all windows-specific\nbehaviors (special handling for UNC paths, and treating `\\` as\nseparators in file paths for comparison.)\n\nDefaults to the value of `process.platform`.\n\n## Comparisons to other fnmatch/glob implementations\n\nWhile strict compliance with the existing standards is a\nworthwhile goal, some discrepancies exist between minimatch and\nother implementations. Some are intentional, and some are\nunavoidable.\n\nIf the pattern starts with a `!` character, then it is negated. Set the\n`nonegate` flag to suppress this behavior, and treat leading `!`\ncharacters normally. This is perhaps relevant if you wish to start the\npattern with a negative extglob pattern like `!(a|B)`. Multiple `!`\ncharacters at the start of a pattern will negate the pattern multiple\ntimes.\n\nIf a pattern starts with `#`, then it is treated as a comment, and\nwill not match anything. Use `\\#` to match a literal `#` at the\nstart of a line, or set the `nocomment` flag to suppress this behavior.\n\nThe double-star character `**` is supported by default, unless the\n`noglobstar` flag is set. This is supported in the manner of bsdglob\nand bash 4.1, where `**` only has special significance if it is the only\nthing in a path part. That is, `a/**/b` will match `a/x/y/b`, but\n`a/**b` will not.\n\nIf an escaped pattern has no matches, and the `nonull` flag is set,\nthen minimatch.match returns the pattern as-provided, rather than\ninterpreting the character escapes. For example,\n`minimatch.match([], \"\\\\*a\\\\?\")` will return `\"\\\\*a\\\\?\"` rather than\n`\"*a?\"`. This is akin to setting the `nullglob` option in bash, except\nthat it does not resolve escaped pattern characters.\n\nIf brace expansion is not disabled, then it is performed before any\nother interpretation of the glob pattern. Thus, a pattern like\n`+(a|{b),c)}`, which would not be valid in bash or zsh, is expanded\n**first** into the set of `+(a|b)` and `+(a|c)`, and those patterns are\nchecked for validity. Since those two are valid, matching proceeds.\n\nNegated extglob patterns are handled as closely as possible to\nBash semantics, but there are some cases with negative extglobs\nwhich are exceedingly difficult to express in a JavaScript\nregular expression. In particular the negated pattern\n`<start>!(<pattern>*|)*` will in bash match anything that does\nnot start with `<start><pattern>`. However,\n`<start>!(<pattern>*)*` _will_ match paths starting with\n`<start><pattern>`, because the empty string can match against\nthe negated portion. In this library, `<start>!(<pattern>*|)*`\nwill _not_ match any pattern starting with `<start>`, due to a\ndifference in precisely which patterns are considered \"greedy\" in\nRegular Expressions vs bash path expansion. This may be fixable,\nbut not without incurring some complexity and performance costs,\nand the trade-off seems to not be worth pursuing.\n\nNote that `fnmatch(3)` in libc is an extremely naive string comparison\nmatcher, which does not do anything special for slashes. This library is\ndesigned to be used in glob searching and file walkers, and so it does do\nspecial things with `/`. Thus, `foo*` will not match `foo/bar` in this\nlibrary, even though it would in `fnmatch(3)`.\n", "readmeFilename": "README.md", "users": {"326060588": true, "pid": true, "pwn": true, "n370": true, "tpkn": true, "vwal": true, "bengi": true, "shama": true, "slang": true, "akarem": true, "d-band": true, "darosh": true, "h0ward": true, "jifeng": true, "jimnox": true, "kagawa": true, "knoja4": true, "monjer": true, "mrzmmr": true, "n1ru4l": true, "nuwaio": true, "overra": true, "pandao": true, "shaner": true, "wickie": true, "yoking": true, "ziflex": true, "abetomo": true, "asaupup": true, "jpoehls": true, "kahboom": true, "lixulun": true, "shavyg2": true, "skenqbx": true, "subchen": true, "takonyc": true, "timwzou": true, "bagpommy": true, "bapinney": true, "draganhr": true, "esundahl": true, "kruemelo": true, "loridale": true, "losymear": true, "mulchkin": true, "pddivine": true, "schacker": true, "slang800": true, "supersha": true, "thlorenz": true, "xgheaven": true, "yashprit": true, "zuojiang": true, "boom11235": true, "chrisyipw": true, "fgribreau": true, "i-erokhin": true, "larrychen": true, "mjurincic": true, "mojaray2k": true, "steel1990": true, "sternelee": true, "webnicola": true, "xiechao06": true, "ajohnstone": true, "charmander": true, "coderaiser": true, "javascript": true, "jswartwood": true, "kappuccino": true, "langri-sha": true, "mehmetkose": true, "morogasper": true, "mysticatea": true, "nicholaslp": true, "nickleefly": true, "shuoshubao": true, "xieranmaya": true, "fengmiaosen": true, "flumpus-dev": true, "monsterkodi": true, "phoenix-xsy": true, "shangsinian": true, "tunnckocore": true, "dpjayasekara": true, "ghostcode521": true, "shaomingquan": true, "zhangyaochun": true, "jian263994241": true, "montyanderson": true, "scottfreecode": true, "tjholowaychuk": true, "yinyongcom666": true, "danielbankhead": true, "brianloveswords": true, "deepakvishwakarma": true, "davidjsalazarmoreno": true, "klap-webdevelopment": true}}