{"_id": "json-buffer", "_rev": "37-735a8df6fc52e612495d8761f612aca9", "name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "dist-tags": {"latest": "3.0.1"}, "versions": {"1.0.0": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "1.0.0", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "_id": "json-buffer@1.0.0", "dist": {"shasum": "b1f06e1c51213e380c8d0f8c453ca38f249c2123", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-1.0.0.tgz", "integrity": "sha512-8NDvDijhhkhC44e19vTwvQY/dm/deHksOqCdWBosbIwemJqtOruXjvnsiibpfdo85jb5QWo/hUx9ekL/NtOLFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBXytnTOjM+a/D5iOP5v0590KSKTOfBB4tBk4fk42W+1AiEA4+MiJZRdh7/do85cgpoC9AyZVlkrTakjWk6ETiVCIF8="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "2.0.0", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "_id": "json-buffer@2.0.0", "dist": {"shasum": "cd42c9061a729042a8f02bceb549c5e9a80417ab", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.0.tgz", "integrity": "sha512-F/EqqcCc8MNPjPEzjeBI7KnkDbodTvSMWMbK53UHh0WjlDfWl19bZ7EWozKtz1hqK2Q79xBzr73bfCjH0G9WaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBEPj53qqTMQ10xaqdDeY8IslZgw0Kvmuy4bdXkPpOV1AiB4pSzcGkZppfSltpNdIEsGO0FpS/tioDfVUysOuyn6hg=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "2.0.1", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "_id": "json-buffer@2.0.1", "dist": {"shasum": "f11217324542ed4b38025b54f9343d9714f684a2", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.1.tgz", "integrity": "sha512-zH5wkFUtl66l8BO1xjfH0kQgpkdKAxebpvHrdqCeGKwabZJlKdyU9UDKXhsrI0KQ2XnZKWGO/YzO9AVuKwpg2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFz9p9mgKq3XEZoFAM+vzjPBOjM7k1oNuLPKut3JI4TTAiBubTy9j3Hz3V4xs3boJ2C7A4xqU4o4dgoGiRl6M3MdrQ=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.2": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "2.0.2", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "_id": "json-buffer@2.0.2", "dist": {"shasum": "c809846fd58e37002e61ed5ea7a1c2722504971b", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.2.tgz", "integrity": "sha512-FAIwCoKdh1c1CCRnUaV+huaiQDou4lauIOQ7jHagzbLR0NWyw7j8vFjKkTlUz1PcxxsHdDKuajr2mc0xzMJDNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgXObadSfofjPotv7owi1ITPp4jLyW6uowL8w5gS/wcAiEA+w1siyHiopT1O+YV027zihv6f/jN7MmYamvae8Qiq60="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.3": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "2.0.3", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.3", "dist": {"shasum": "98dacd3fa5c6307c8c15db36cabb4d6d5cf46740", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.3.tgz", "integrity": "sha512-PgemF6c1vzRgDUdy4vtvphuEnOHoZ2nAFaRfTQqPtHIxUa7DYKyHIx1qH5VHUjzKxZrhHsgWzwSEP7U061RILw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEA6ss+oUTEg1gnXTpXWjvgUltG3hKAGoFzGUldjA9U0AiBEVoz+fNdVHDvnpGJ3OYtuurj+XEq6c590Y+DPdHMYeg=="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.4": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "2.0.4", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.4", "dist": {"shasum": "e93fc91b2e08e624cd6ba82140d2119e34187fbd", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.4.tgz", "integrity": "sha512-3YXSSK7HmPKJLo6b8U/sicH9Xs/p3MSBlRtQJ9jE+2WMNJAAPkAO4i05QDFNG9jBFRoTozwvWEhxe8o2RxUipA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmgLLL4TvNeyt2x6bGtRlZSu66jufNh1x7F5W9IF8K3AiEAyq0JcwiIFl4rmR800l7LbWyegd/yhsKqhpmDETmHalo="}]}, "_from": ".", "_npmVersion": "1.3.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.5": {"name": "json-buffer", "description": "JSON functions that can convert buffers!", "version": "2.0.5", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.5", "dist": {"shasum": "e4125af859efd780a215e369ef20f9c323d7f10c", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.5.tgz", "integrity": "sha512-iNCRwzm+gY5Mc3utHXgKSg8TzbofB1yA5pD6lMsETWVy3Q+ok85ZJkg/opG9FlqwT6co8Vi2txU03ZVdGsmcSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCx2kc1GU2xZUiDdahZxpLaph5yMBQDhsKeN71EuGNiDwIgdBstiNmlkvSFTe5Bo129EiNBLXcHk3z8l5Da9U8LA0M="}]}, "_from": ".", "_npmVersion": "1.3.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.6": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "2.0.6", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.6", "dist": {"shasum": "99b94c9b45c9842ec1dcb1e39bd00d8d6861890e", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.6.tgz", "integrity": "sha512-dwqODxdJ6DmpRf2YelPzaIV5VO+6ulCppF8eQ22ADogrhdc2vlNEzXXPlNkm4csgMifOWQN3/1CfZVuZj6mD1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqjKPhneuNnlW7YWQXVwzJZwOwCq5/kgp8oWDEf0OaYwIgDuQqC77TG37VA/kKBEAwG85cPUW8TNzMgt7RDMafOOY="}]}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.7": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "2.0.7", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.7", "dist": {"shasum": "9166af7ed214f63c3b243b760735cfc4013b9885", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.7.tgz", "integrity": "sha512-JBPWfv4aGJFBRWQSdljZx3DKoDV+zAgAPpg/tPYm8a5YtwwlhSGZ+Sw0p0iFQWl4XzTCRUjH09qSYTifJUNbmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCuKgy6DA+drHg2Se7z5Aze6czq9mGGKdj5btS6fSU7gIgD22ii8xaOfKFi0sGZovA9OuhbRFRRbTYXNVR1vmMH4w="}]}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.8": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "2.0.8", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.8", "dist": {"shasum": "df5c769f67f38f0541f16b25bb7b1e0ad1ad0dcf", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.8.tgz", "integrity": "sha512-iavPUpQXHmUc8u21CXUOfcIXmqMdJLr2zTLHg5pXHS2OIQn6NWoVw2npp7+NW/IaHeIWkR2SOzIufzrR+lc48Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4psQJn6+OVXFKfhWhkmxBFEXKjo9vSRaA6VAxiluPUQIhAJxsQ7QosooGwW0qKV8+C2wVCXDzi+bxC3b10h3ahH0U"}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.9": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "2.0.9", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "dependencies": {"bops": "0.0.6"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.9", "dist": {"shasum": "d80d6a69af024c18726f125650be35d44fdeee6d", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.9.tgz", "integrity": "sha512-c+a2XE1sf+UNPINp4xKOWuTuREsRktP3Lvx60lMn9EvQL1qhKz8CWKUUF5JA1wUs4TR7ztLStsYvGf9tfjKh0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFymd6eVtqjuJurCG749R9TNH3gYR9feEix9ifGgFUDSAiBfHmOvIrGzPvB4UJEjC7JGgjCEl2/Uiv04IooGZPNK7w=="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "2.0.10": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "2.0.10", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "9bd28b43cbf78433cee8b8bafef4c3cc18583b24", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.10", "_shasum": "0cf594832e582cfdab24c5b2c448fe2b23f77775", "_from": ".", "_npmVersion": "1.4.26", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "0cf594832e582cfdab24c5b2c448fe2b23f77775", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.10.tgz", "integrity": "sha512-RT6p8CGD+mA1xejIKsdQ4sqQtunXcSZ/JEKMarFttWGrnCEMbrSj/Mx4p6diLW76fp8RFjNH2baOragMAyuObQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmALandxqi46tAQXgkb8RobsbBX4rjcFSqqGnw4Yx8oAiEAlkhgNX8vLjsSvyJN47FsZoo1tZj1AlsBCgjdsGSbImk="}]}, "directories": {}}, "2.0.11": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "2.0.11", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "devDependencies": {"tape": "~1.0.2"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "30d41f371889e61f6ded06f3cd18aec0920fa7f7", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@2.0.11", "_shasum": "3e441fda3098be8d1e3171ad591bc62a33e2d55f", "_from": ".", "_npmVersion": "1.4.26", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "3e441fda3098be8d1e3171ad591bc62a33e2d55f", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-2.0.11.tgz", "integrity": "sha512-Wu4/hxSZX7Krzjor+sZHWaRau6Be4WQHlrkl3v8cmxRBBewF2TotlgHUedKQJyFiUyFxnK/ZlRYnR9UNVZ7pkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJcb9RRPisRK3ySsBOG1JS8BkJIxsEfiEX8ytTE7nTwAIgHimVSp+olOchjnnqovcXh+LTkg4rcsKaBTwCt6kglOk="}]}, "directories": {}}, "3.0.0": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "3.0.0", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "devDependencies": {"tape": "^4.6.3"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "da6fe4c61fd9a5e7b450aecb079219794733b245", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@3.0.0", "_shasum": "5b1f397afc75d677bde8bcfc0e47e1f9a3d9a898", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "5b1f397afc75d677bde8bcfc0e47e1f9a3d9a898", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.0.tgz", "integrity": "sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEnZWVTbllBmut4suZN/5CaNQNEJguWN0PnOGZX8xPEdAiEAr4NS1sduIHUi1d3L7mgjXNE8WuaMeeGwIWj/35H4yDI="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-buffer-3.0.0.tgz_1500312568079_0.3351021413691342"}, "directories": {}}, "3.0.1": {"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "3.0.1", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "devDependencies": {"tape": "^4.6.3"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "39146e89172469d5700f06e6b569574827d77fed", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "_id": "json-buffer@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==", "shasum": "9338802a30d3b6605fbe0613e094008ca8c05a13", "tarball": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "fileCount": 6, "unpackedSize": 5404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblr+4CRA9TVsSAnZWagAAILgP/23U6G1NHKcMYs8MI4Vr\nPBSqFuKZvPa8P22K0ke09ylj4Fnkq5nxqgSSogynB3TWm11aH7CJxT60Nsns\nlNMyKuxF/XtfzcyjeicFLqYcXf5boKEiLChCX1llSRB2OnXy0zX2b42RmJ+W\nP1U9VXRvnzHyOBsYQrlD5741dRu8/4KlrWv1m1biVF/q3429NWD5wdtgvlCG\nN56ulFbSJ8Edd73b22Fk9ggNJaABjZXTITqE21f3b3BjmqSWkBvkWIVlMuDP\ngWJdMSC1q308XiaIv4lCW/4mNc2PuBErRD0k52xI3h5QSFEapQ4e9MAKIZDB\npNnhebD8xAgNh/ucCGEn0LgSQW8EmfemrnXM8YLQw8PWAE+ZjZLYmgfEaIK3\nyWWQ2XJlI5CkUf5GQfLxVrcDaHO9TS22Nt8Bnd4eA6swEWrOgakk4H806lJd\n4zuv30TsriZPBPyGQ8CvSSauFpNcSPnVlPzWTsaBVuVF6dWNnlby1dYKilOw\nOtDPWjfI900qvqJxQ8/hro+I/mWht7BzmN8Xf0zGQoC4QbAJU84AhbMeajbS\nGN5YLX5z0oKriDY7syF/f+bgnZvxvp+id00a60z23wu3yo1LQFPjYlyC9uj0\nal0IW2jDhxnzrvzQE6njk5Ln0HOP6HM2mAaS5GRxosP837u8erMovsvBnJQc\nKUb8\r\n=Nhkm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/DCJxWI6Lnq0fSxaLoeLhqJiZt5Yms61i0vRv94oO1gIhAKX9BCTUFVnaA4LmUucPCs5b7qVBUdGrc0ja94pUksKb"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-buffer_3.0.1_1536606136167_0.5055444051404696"}, "_hasShrinkwrap": false}}, "readme": "# json-buffer\n\nJSON functions that can convert buffers!\n\n[![build status](https://secure.travis-ci.org/dominictarr/json-buffer.png)](http://travis-ci.org/dominictarr/json-buffer)\n\n[![testling badge](https://ci.testling.com/dominictarr/json-buffer.png)](https://ci.testling.com/dominictarr/json-buffer)\n\nJSON mangles buffers by converting to an array...\nwhich isn't helpful. json-buffers converts to base64 instead,\nand deconverts base64 to a buffer.\n\n``` js\nvar JSONB = require('json-buffer')\nvar Buffer = require('buffer').Buffer\n\nvar str = JSONB.stringify(Buffer.from('hello there!'))\n\nconsole.log(JSONB.parse(str)) //GET a BUFFER back\n```\n\n## License\n\nMIT\n", "maintainers": [{"email": "<EMAIL>", "name": "noperson<PERSON><PERSON><PERSON>"}], "time": {"modified": "2022-06-19T06:12:08.317Z", "created": "2013-05-20T16:08:19.097Z", "1.0.0": "2013-05-20T16:08:22.609Z", "2.0.0": "2013-05-21T09:02:42.663Z", "2.0.1": "2013-05-21T10:01:55.473Z", "2.0.2": "2013-05-21T10:05:46.547Z", "2.0.3": "2013-06-19T10:02:54.841Z", "2.0.4": "2013-07-17T22:24:02.612Z", "2.0.5": "2013-07-22T09:06:49.563Z", "2.0.6": "2013-08-13T11:35:15.300Z", "2.0.7": "2013-08-14T13:15:13.339Z", "2.0.8": "2014-04-14T11:09:12.595Z", "2.0.9": "2014-04-23T05:52:21.217Z", "2.0.10": "2014-10-19T17:21:08.532Z", "2.0.11": "2014-10-19T17:43:38.846Z", "3.0.0": "2017-07-17T17:29:28.192Z", "3.0.1": "2018-09-10T19:02:16.381Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "homepage": "https://github.com/dominictarr/json-buffer", "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"rugare": true, "dpjayasekara": true, "vivek.vikhere": true}}