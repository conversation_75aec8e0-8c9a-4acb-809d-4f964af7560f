{"_id": "@babel/plugin-transform-member-expression-literals", "_rev": "112-634895f30d1ca44616e7fd803213b303", "name": "@babel/plugin-transform-member-expression-literals", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "56387fb95dd4655eac25d93b8f5fdb7da65eaa4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.4.tgz", "integrity": "sha512-TpefR3dLxHvrSAtzkgPfRzrO4mpXTqsu6D95N/+KskgKU1udnFPYXdfV8hu3UNtw1WcdrTHyjcoVDENpSHDPPg==", "signatures": [{"sig": "MEYCIQD2a2TZ2nLyE6FweVWKvv65CRXuZkRzPJpBEFAe0gNPLQIhAIKvnzpH597RgFYM+Jfaf5+4aHR403ExD1cB3+vETWkd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.4.tgz_1509388480476_0.3124116265680641", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "922bb371aa55ead089042a494f59f7cb9436445f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.5.tgz", "integrity": "sha512-aOEq8Zq/05JhwtjoDBiQpK4nOyWZvd0Vyv276N8k//my4iCDNRg5y9Q4AEfqaxdDdFXGMgiQv5uTBCia9Jke6g==", "signatures": [{"sig": "MEYCIQCIBBXgh9t1eBg6bYpsu5AKkbxXGdUqULNZDHTpxxsjzAIhANPqNI/kgwo1UayWbqDtZQ0gyedqJ7Q37nPFl0ad5YGA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.5.tgz_1509396979396_0.7542533436790109", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9e903a73df46b9d85f3b46777cdc65cd76577fbe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.31.tgz", "integrity": "sha512-28UZNza78sGye6RcfWN8yVZX3ppNdJcdeF8O0xeBBZaNMQYxEBF2CFUODawJ9AogBNgygVUyMo8txTVu3FjwYw==", "signatures": [{"sig": "MEUCID4vD7binmDl5dXLEYE9aPpog5ZYlNBBE08ZkTn6+4SHAiEA/CwKgr5xBNh0PeFuf1VBXwtmdP4LA9Xe35w4Yi/OHrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.31.tgz_1509739405565_0.2950202359352261", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "52b90e3bfc1bff59ce7b0eabb1e48e4fa758f954", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.32.tgz", "integrity": "sha512-VVEptoIJ7xqC2VoOSKU4LJjQ3lmIRuqXOJG1rE4AIk1qIDGiSLB4H4/rDBmk+nEu4nP3JfpCBCwPEvRXIzJRvg==", "signatures": [{"sig": "MEUCIQCc7NKF6SwnrvuTH2hLuMTR5/uUZHdEyoXi4ziVSY4BjwIgLks0sRjDkAoOviXnhIv3FJr0l5FoBOqzJSeV9hL91a0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.32.tgz_1510493596047_0.5640160092152655", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "687090bafee7364d811329252221ce4f95b2dcd0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.33.tgz", "integrity": "sha512-pyPOh9Y6Ku/AGWlk3zIxiV1p/FCjKG6LmAsPmAvv05thTfFQ5Ru/urqrlEMOQSrwMjQY+2raJ8rCBuwufsqpSw==", "signatures": [{"sig": "MEUCIQCUt1YV+w2ohI61siCMCvB3EBPgb1twWkcHyq/W7hwtPAIga/o6Q0kTI5PspNpd2DkLqDoaigKkCdGQLDnjM2B0QnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.33.tgz_1512138498303_0.9134722822345793", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "26a174897c81027fb56bb8593b2976e95483f25a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.34.tgz", "integrity": "sha512-5v0fDnmf5EXmJdPKDNj+MTL+fN2fwX5SLo1qbzfcfnuXx880EKURG0305/mhlhPHf/4cX+IV95dglkTANS9xoA==", "signatures": [{"sig": "MEQCIBGzBFedCWeEP3N04DwDoHTXtFFPrEZ4j65ESWtS4qTCAiAZkiqpTwGvUNkPqGWAvU5Px7O2zSoVO+AG6DNkVpaFeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.34.tgz_1512225559441_0.48863352299667895", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "749d5e28871bb6cb83da48c43645d38a71036f1e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.35.tgz", "integrity": "sha512-9QXY2g1KAKBDWfjZwVuvpjJBrvDXwWYB9T93NkEVX3jl0LbFF2NC7ddRzh8zbMKlov/PnFKAysaskgXbhUBNMg==", "signatures": [{"sig": "MEYCIQDtcP8TQYp9/1k+TgcM2y2UYvajKxUmwRLxfT6cYg8JTwIhALjytOSlUPFmTHsaM2oQtlYHL3ufJWG42AHZ+T0Vh/QC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.35.tgz_1513288066207_0.8246156570967287", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a8ada8860ea30eda99199519161b295dc2e2b663", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.36.tgz", "integrity": "sha512-/7ok4XxZqToglWc7M79Sfyxq8cOdAm+X1gCjiL7NI++I7fRwq9FLd1TVoCLk4RAdNYHX6VEQnVcPMyobz0UsJg==", "signatures": [{"sig": "MEUCIQCCafsc324rCPAUvFo/k0JXiJpjFS/xSE8GZ5YDcXuiywIgckHlgzGqbNkcD+xDyf7XEbE3ZJeYF6qpco6OaxtOx28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.36.tgz_1514228677197_0.04500483605079353", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e4fa68d95e7d6fffe2f5e836bde810b720c03fc4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.37.tgz", "integrity": "sha512-pnjQ28rLhD9BSJyyx+iF8lO9KH+vOeHXAzYQfKfgown2zdn3hiJfjAYTj+owaODILEbmDoS5S0Y9B3TbxNTu2A==", "signatures": [{"sig": "MEUCIQCAp/w3xlghXrf0K2TOGaivnediwa5C39LqaOuAqKc0NwIgCt7hakwRli+6xy6ytnPaoUiG64RQI4EUl19foW6or5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.37.tgz_1515427350902_0.03947269986383617", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1182db07bb8820cdf2ddd39237cd22cef8300709", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.38.tgz", "integrity": "sha512-kbw7FJHAVRKBMbOb1eJ6ww6pfBvnSZcvMD8X12zmvXj0QCRMJ6QmyC5KV31HCyWEmnTFAvZNaJx7cwQNii7rxQ==", "signatures": [{"sig": "MEQCIB2UghNef5aT1y5JGKHrZG0SJ/E19L2CnbowYnYt6XGUAiALuwFUFjCgQg5wlFqhcuQ1LiXjeDasnKLPyR1YJTalpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.38.tgz_1516206716490_0.7740832783747464", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "35426e350eec575ce8f67bedc5a5e53a373344f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.39.tgz", "integrity": "sha512-0kalfMKKbbyZvFR49ROvrScNer8BS/kv+r9JayyneoxtunW286pUILQp22OZ+L4IFGm0ZMZwssuHo6IoZOJ+7w==", "signatures": [{"sig": "MEQCIEhWqJ8yHgwnV1yDJGEhKhUEXos89Cmxn+wCv2+fFAVBAiBmddDkmHQtWol42WhwcETq6FSFQqjLYDagnSLvMzVXFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals-7.0.0-beta.39.tgz_1517344054141_0.7048018472269177", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "42a3e25bdec2ac9410322b730003503f068dbf3d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-h0dU1jX3ho1X4E3VepYTsjD+EhuRNQWOEj7yBvr2zPEI/Fi6VIgDcs+m+pxRTky3iosiLNpJU+f31SlaHPkNEA==", "signatures": [{"sig": "MEUCIDYSXqy4Sh5YpcUwCFG4f6qFXI/kmH/JmKmXbP68nJv7AiEA7HWzKskz4wv0VoLB2Fa5qlDxP6PZpf+849JcwtqK9Fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1813}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.40_1518453696409_0.8913718230349317", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f1ba93bc7470b786062f40fd8b415a48b5bfe887", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-e6gXdjL6JuxNcs0YXgWL3m4+X5AB2UYA+PcBjcNrvasPCP3djQM6+5qmhcBQXtCPhhuC/r81Wxv/vTx2nl2C/g==", "signatures": [{"sig": "MEUCIQD97BMO2MjGDBq2hId3zzhQneyL5WVDWjydrI/PkG8TEwIgay1iTcG2ft9kEOpci20LVL/nKYtP76mNqsWtRBXYovw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2048}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.41_1521044769314_0.23440241728586741", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1191c8df0057e1052e6a2a4239f94b0194609d2f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-6B/Fu0vq2qYetpJahYVz7mWLOClH6rO8nUfC93VV2Re82RUmT3c9rq61ZXnKD34xRmFXX7XtlaF/A7Qe52ycVA==", "signatures": [{"sig": "MEQCIB/36A9wDZ+1cBwv7vz8YAL/bhFicSBzOgy7dv9jUTwcAiBy+lYT9dI4JF8UUdnYAeZaeMqLRgrTnFwAz9fHX6xuYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2048}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.42_1521147044483_0.3903506957097955", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "30c26ce6f09ab8d068f6790a6fc421b581233621", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-taZGojJrCtpUOw+ai0UNlti6J/XNHePL0G2v6qfO2YN/cF5TifWYDKqc1cVQqxwAIG80IAhjWxmF/FBM+UzzIw==", "signatures": [{"sig": "MEUCIQD7Z4Hw0dgxEdIdnIpMQ4oHGuVH9Ppp+7UFCT6Bwj8gLQIgBgwSSL0DR/9i1hrrhzZCtxVTpwdodgpDCSr7ebN33sI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2251}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.43_1522687706000_0.8827159520191139", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1d25789cefbd69dfe859927a53cf7c6846f44ccc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-UI9j3yBUzC0kQ6+YSVex5Ejm2rT1whp3LvSU0bd+JGZ6akGwmKNHxISpisPgcovMT1MZ2NJ9U23t+kb52TEoiQ==", "signatures": [{"sig": "MEYCIQDgq/57nk+cXBGSE1CM9W8kftL2CiXpYgK2taqjP3blRgIhAKcYnGpPLXBn3QVdRGQIK0xdRej55cVdPtkBh9nN+LVq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2300}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.44_1522707607585_0.891878558313357", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "98b5b89936481314896cdaf484edaf0124607153", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-iwrayEO0eHkAoesMWyJmYEqOKf+VUGi4Is88MYbltvW+eMxHtKqBkFyxMLHqEGstdRU0ZTnkag5EdVDQ4b+BhQ==", "signatures": [{"sig": "MEQCIDW6AW1coEmsOCbhV8DyGuv5VAHhSjCHUa5IUXJoZLGwAiBlhzl7C5eW8ept7WizocymmtEPNHb+sUDy+dXa/DFYEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1kCRA9TVsSAnZWagAAOYgP/2caytGXKUJrfEuOD7fk\nmslwPeF5R7BtZvvE47rCBnQ5o8N58XBwQWbP1nTm2sjSHJngHTLQARr4vW8H\nyjFbl8S/+mYg4ZehSYWEM9jAdaCQG0KS3A6ByTGoS2QUBgboSlPypoJqZ/OP\nwhwabCXGJE/n7yqnNUytJiKyHVDGndlRzPuoqylMcz+iNXAl5kSjnRZHNFRr\nPRmoAprXI2Vicca+LNSJzEr4abM4xJdsCgxNElo+QippUjr8udr7YJ+c4mVY\nIYCdvcdZVowY0iDlp9h22HNwDrJlOmIngiz03oAjmzSHzm3kc+0zAUMuDETA\nccK4e/W65o8k5t4oL/oskcGTcH49NaHxhzRe3WmWTSnDppidbEbrOZZ4jtr1\njB8W8l4p+F1spkiF3ONJQR7Zi4CbhEpPgBM4sljAGKATNIiW12CO/eCtxiTG\nyEvWvpSKGGHsPFPRcJ8OZ+AIUMHZlzDQ3rxknR4tNng69sGWnNyq7+AYzkU/\ngSkSQ/+spxVzzBQNSiG9qtK2zm3ekvOPAAKy5ns88yIxBBuUM+hFEuSHg4GR\nrVj+MMmmOhZoo7U+G/kSREiG1KwSAwivAPBVw0AKj2XpfMzscLGb9xwjfkpI\nnWUzsOHhWxz4WvOqb1FWFWPauXiSJKp+7CAgczyrBennfWur5NifRLP+cNgF\n6E3H\r\n=hU75\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.45_1524448611499_0.8610708305360126", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ead0146e286e7d77cb5bbe9e637c34fdb7501847", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-obRn7uwkePxFOrNGAFm1i4zpTpvI6fSl89mFRCWMHvGuzYt+qtpK8n/IhKpHco4HxT19h/CjX8Cj/2i+97jLiw==", "signatures": [{"sig": "MEYCIQCw7ESSp3bG67QN0AGizUtYNMp56DzPfdizhvZ05iPBsgIhAOJM7U49S/9eJzOICuqXlVHipb4WlCXcvo5U63fCR/cC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGUCRA9TVsSAnZWagAAFtAP/1FtH2CtwsgpXy9rQaCV\ntWW/YS7huVHXiGFNxOwUTFBz1QqtTBey6BZNgkxz7LGve0cq/JjZ9oOVxG0/\nk8bd1HRk06utWQcTzo9nnnKGINpaLLi5i38AIHfhC1Pu4NOpe5DMqxvugcST\nvr3YzgIjd0kTK6V3N2ZseJz8a0EEkF8DDl0ONUcET9gvlsbN4Rixf97cMtyl\nEq6Lc0NsOi+0jRp/LicHma7VzN/Yn5Vnlr8g2dQP+UO9RRzBqY+JJ0v6JQB9\n4gGgzFF58c5WdqOcyTXUoE+X+QElIb0aBzB+MW4aQoKarrnhmCNblECSIctB\n49zXYlNMsN99VKraRN0Xi6eXiHazrTwec4PyD/fE2THRjXSNe7B4MG5sgEgF\nomG2dGKldkbeACWuDyuRhCNRrWEizhrQKyIj94h6SyAPn3NCxjSANA5sPivv\nS4sKj04hHeb46TEO5hPDCS7SaTog32NTLBQBpd/VZTV/goFDtZT73nuw2kL1\n5w2KVFn0Vlz4NTKxzODJow0JzNo/2jG34PbWzN2HuM2F7R7hog65Y4mWc+M1\nn8+8oF+4GZlLHomW5ia8I57QPoBe7FYbXCYiEO87bHiWDk19UHQm4W1vISl9\n0JyIStKxPMBD7qEEByM1QHASNw4YRQJfsxQ5j4kQufNvu2HoUb4XwLx4ssus\nycwC\r\n=yCdL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.46_1524457875655_0.8222438818728843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bce29d856e951f8e83ba5c685e55b07d254a9172", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-7kQHOgSRfXR07N38uX+JZX9Tk10UGbIsVEbkNmq5WMSXb2G3ri6Tyuh2ohlHzBQkW131hT22AAg/weVhETgVog==", "signatures": [{"sig": "MEUCIQCSGDIUI2okku9i1f/DPfonr4Gchof8mrMCyM2PMMRi+QIgafq2uzLQgIjc0WyzpdHzwz0I+8n4pUYX/D/TddZywzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUcCRA9TVsSAnZWagAAghIP/jY7rpayFwyUL2NOeA+Y\ndIW8CoB7/EfuT14UuTvmUqglXsIb+h4a1A+G6oq8ZfLUPLPtk5N8LI8Si8e0\nlDs3Q1RFbnpSmc3p5BN8sIAjgGyuIHc1sZQg3QIlVk196nQEvV2+8g71FvPW\nTbBqOre5jOdjrtPqsUT6IgZTSq/3ooFcWqTCn+WlYDJMnoM/6lKrrjO+2p9Q\nKHolCpQm+xjpfHfkRADb3ABAnHTHEUJlb1ThtgSh4/WNbQTeKRIw1s62dxnb\ne9Lq0GuG2qlWbKZT+ewVtORHFvUf+Qqt+h+HOeGSHLUjXhoibyTUyeJuv1iV\ntNDf/LI54xEhh1nQWylRM+5i80fLkQAejkpJicJVJ1eRiHq+t1MtZq9C/4yJ\nQqEC5HLkwt2+Ic9KjDHHUKd/JMW5RMPnFzrfcRuqFd80se95Cb/fxQs/dRmC\nHWo+kKGp5GBsTr28LrChkL+BBG+SM14uZe/115zdjzoKn+g/TqGC8Kl3LHid\nvjbzG7Hsvfjl9LlQ99MOt5jQIz5ze18jN7CYgoajM2d46cTvydyAmgFHUMns\n3K9hhDBlZePzKjiLAwXdK6EfT7kWjsEA4/r9YMrahrtFaYouO7Of4tJQcy8F\neZSFH1P8A+GQ5jl/adBSEM76l57J6n/W97dF8p3Tg7T0Ii4ezOcQQvHRZeVE\nfEA8\r\n=0Zbd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.47_1526342940090_0.8241549471409979", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "959c936a33c1073be398846c27f2914e2324ddf7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-Y/RsyDmAP3d5f+xiDZ9bb0ErTypuG2B4cEdLZlh3PKFRl2rCmyAgp7dgXusmkqWjhAFJRrPObtBcHN1HeiPCGg==", "signatures": [{"sig": "MEQCIA28h4f+ocnYCYC76CVlwY8gUUM9sngerFkHB/6tXYINAiALQEsPJTVoQoquJanqbmHYP5nEVQTLXNI6XgfuwWaZzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxD9CRA9TVsSAnZWagAAGNcP/1DmLcVDikaPOhFjGeyP\np3U754UqDbTqo50kvt1KA+J+FJNfMTvpHTg3a3QZQYdE8yycekWBNr8YVwql\nbY7RuID91FvNg0KzyU61uoTqlxbZBdl7yT06cd7FHiVjpeMApGwz8tbUnBig\nDyG2cwuV8ogUsPhdiC+aT6hfon0QR4pMk0sM1EzkrIH1SztCtx9lQYUsjrPt\nX7etskCUHTSlv1j5O0fLAJKptp8MVP7lv6jZgevkeyZBqqgD2ga7+UfT5k0c\nLvM/TXkhHwQJcLoTB9V+w7cjPB/LE4FgErhU//5NK0iV+5a7kZh7GG6Bv1tu\ni7ZDRbFaM+8b7u49EaHXHQYcQzONYm7fXteFyPTivlEH6BXt7s2u5x7nqDec\nJgIJS5MrsDxYhmyZmOlPgv2Nk3SnY3vt39KQIAYSi2NHNxOxsjnbNZ/wONf/\niHdqirul2mA/dkfwHTmMAPnF59Two/qjfdUPMNLs2utjNnYZNVMvGraIdHO8\nqV5/mn+UR8cFHkB34JOENhvURcOhUYzJ6WDZ33AhdN+VhJYvooed/RCdXeOx\n3czk9DmbzM2bhhNP4eGMO+ZBz5Avpv6WbwkRd9S8Dv0tuLE4XDngF2XYz3Lh\nlkIRj3Cb0lISdAv00woziPaSpOceLpu+fYbVUAtorB6xHqStRtLt3S4v6MTT\nQsxp\r\n=BRe1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.48_1527189757359_0.5427060504277141", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7e92c5fa2550651f380e04950a815e5f5579c06f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-7UM57Wa/O0EmjzRIKB5Dico/Q8d4f5cNrTk8jqbTQxVgL30fjYLkPMHrne+3fUGEQwG7fQflIDkkIhldwSad2Q==", "signatures": [{"sig": "MEUCIQCox+zRf+M0YqAibXExbzenAYOpIp3kklAhMWq/TkKRAQIgVXD0I238IudbP7dGsEbwCx6VVjMb4EAFjz6MXuJWKD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOECRA9TVsSAnZWagAAW4gP/jyjGyxyF/cPo7+RFu5c\nuoG/p9ChXm17joWajiCpN7YD2RyhfphmhhtNqP+BxBGK8Bu3I8qN9B10V7pM\nL+sK4KZLI+T/766rMT+VimBA5Z3vl5GcOTGsD5KzDD8wdxF6T8DpuvlcLY2z\n34xtVCvCkNIPGYLkYngAfMSjcIJetxoR1pXLC84fcesp+fiNmkWuLiUjsilc\n6Td0b1l1TXYaE55SQdb8paUEnaA0YaIIJ/SRVsrDJnBJTYNABUUVi3MItnn/\nj2kl8fIAACT+xoqBT6NjMrhnF/7Jydl9Wybc/+mcpMwz+P3aHRKSwC9bbMWC\nMRm8lslLQfMk11PQKfm17yyRTr3ID/tIcWOjiH5RbXNPwccz1HZNAom+SLbP\nEvRcCWv3vUXlE8FrNmGcnz4bnJTuTZejtedn2hcse6yjNFfk5FZqQB8Cq7Cg\nkgA12GmFFSzijZTrShjNnIgDgjt2ZQpEQlORQex6pE5y38jht/3vPMSD5hXy\nB0nOzJIlqnr1Rlb8PoiaRNuW/kMqb5owEZujPPvc7DwZ5/fAYNOwdW1ithGl\n9ZbyIZ0eon5iEQLYSOFbmlSxcCdCD/rFQM2vzdeOPDH0AkgPAEoy9Fe/15dl\nSKgQFhfGEK0TaSNdmHcxnyD81qfx0i3MsABsYdDVf9y8cgnL3oc3ukkhPAex\nP5hr\r\n=NAnn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "7e92c5fa2550651f380e04950a815e5f5579c06f", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "3.10.10", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.49_1527264131742_0.5330300750646211", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "63091a3e843bd95f526ef0ca17dd2f010cbd76fb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-8/54FLnf6ASF65ts3+J6qJ47dXTICYwASX3oKJ5Bp+AwEmPgRIspuQH4Nw2Ygz9SpcAzOaI+mgFfGPLSK7X6IA==", "signatures": [{"sig": "MEQCIHTBPyFTK/+aN6YkiDKnt9HsxbhhD7trwLKfUM1IJYxrAiAd/dRdJ0x6czL+9zFie9eSB9k1H4XrLKIQb002PdBxaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2046}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.50_1528832837734_0.2962324186404859", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "675341402b8b8be6a5302b79bb4e58cffaf45f0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-Jt0eb0ccoFij10bKQtTc5nhyKZRUwYbEyRWrvncXGjB5fE1jJYMUBV0AfK4nryfqT87lxtUBkI32OHYfFyq0ww==", "signatures": [{"sig": "MEUCICA6ndA+mD02u+wOEvWmH+w5EXa8CdwuR3XPXys+8EsXAiEAqjai+6SQuZbSYpMs7IushLR2NvFvC4+Rni6XzCjzLPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2060}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.51_1528838390060_0.5763496940856283", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "76d3a775bf5c233f4bd924eb3cf5fb91fe937be4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-hpt7U4DuGLmuybFOOuGbDzrUAzjy3/Hm3qSWttLdSKQ0/DITHRvvEjwn8DaNoE692AvnHoSLzc7v65lDeLbO8g==", "signatures": [{"sig": "MEYCIQDnkCp0Om9BhiiPV+9xs3mj0ttgKFm5D18Pk0NepNYXZgIhAMmJoflS2HUae7ZxdZ3CV9Oma7ocz8PS52/l1iksFLE8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2059}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.52_1530838765860_0.9707602172588787", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b121bacfb65ab5bdc0e60f8883cd76f122571cb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-ULgYJN0AEwvV6RmGYaEk6F+CJKM2UFMfiCPWkPhJkdTUFRgkdjMRKugyDgJrPR+ezFlf0pC7jwDFtLZ3ynsxpg==", "signatures": [{"sig": "MEQCIFjEhiI5RoXhU3X5tmkWLYg2TtXsFVx23kLkkVYoUmZ3AiB7w09b0/WtxMFGXDiFpSvv7EEFSzcup3l5WqnaRjelBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2059}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.53_1531316416263_0.4376859147417762", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c6d3fb675824a888475c06c6d84e1b5b46bbc298", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-cQH/6DL+6Y4j/RywDVGWo4dYxzZXSKnAiElm2CbcyLUR1/p4+V7V13fdNL2AXDHgZM5euaePKnVpHZz3Iv5rLg==", "signatures": [{"sig": "MEQCIAtlHCGybO41GrhXUzBRcd7tGSplT0+Ua7dr9foUwMaJAiAyNmRLUAeNt9zFhMIunMUJSNrgdy+jtPDd6kY9u3cEEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2059}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.54_1531764006851_0.7581598341507005", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4116b3969948cbb61d0076412b51016deee6bfe3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-uHQHtJ3J13vOKIBvB3n9u7Q4ab1UVXcF0C6VJQDIDmhTKxGXodztdgjgD4s3JrXQZw7/sdjEI/6+dcmSJAQVfA==", "signatures": [{"sig": "MEUCIHeageLfceOhN1xTpO2wuKGjR3WiukzW1EWbqcNPdlJYAiEA7cOxewYLvadSp2dI8NX11PcZ/rswlJ/JLGqhxq9Ak20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2059}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.55_1532815637795_0.32566494356114273", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9c5324a7c3b75ce9362db418acc4bde1adeb92d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-ePcjZLtqQHWgBHsVUcoqluwBjQuovraNUG/ZrlDYNV5wCpnr4Snu7UW5uFobF/rD3O7O0pbx3s+wJAWucslsDA==", "signatures": [{"sig": "MEUCIQDS40wIi7CsZcLvSB8jHFWaFzlLO4OjmssOa/p9O+4YXAIgcdHXv9TzCyG50w6cOOdNsWrEP+1L1fCx6upE0C1+t9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvrCRA9TVsSAnZWagAAFhMP/34+iNPG39t0i3JyPiKw\n019tPxxdtxJqrxCM+Weg6wGEVCSVOYUp1xCB68twJjUmEHebIASVS/SNBh65\n53xdN+de85b2GL2apTpV7VfB8rO57fBJchtsY3kZ4Lkmcq+lZhtd+lEbyrhQ\n6oxqfw6LPpo+zWmnT37shovxSRaNOUuNEz1t3VX5oIU/3IZdDMAZqcgQ6ET6\n3Wgcg+kN5Xw2N8BE3k6lQoEBMrl78aREd7Zo9uPxkosssI0FMYZ6BFPwInQX\n6THxT1/kWRWmyvK/Iz2QOKAxTlt6TX/8pz8E1KOPkt8ghSNo9M7l6zR1CePu\njWexlTsadwhRnaXZZjSxU1e10Pw+XnXS0k6ARmCWuFdvPh3yO5QJysNxHAsj\nMksmV6mL+DXMf0m95r4Mp+aQDq24XBZmwTKwbEFYJWMUPsFOKfei5Yzi26h7\n1xlz70npbVh+SgFBtKpBS5TjWfcOqRsVNdALRpkO8DlSZGt1hUHuanOvtOSy\nveHJqBVoth/H46xiPlRx4GpG2F8qHR5LqNcy0M6QGwHcfpv1TiOhAs8TrT1X\nex6EFiaoGGD0/2nJ17Uf7h3cyyqNJRy84xRWhHEep5EOGQaH9naBaqY+4LPB\nc2nX9BFJZKwLOD2bXaMXGVb9oxm0SJ6O/XXF/kXUBnrplRoGn7g0PsTowiG/\nfdAV\r\n=72wm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-beta.56_1533344743432_0.6193211854269336", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "09ca22b9fe9b665dd88ef0099cd70e2638d6b05d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-ETBbD9+aLSIdAF0o1PA4QlHL83NW23eLGF9biVV82bWkN5dOU9MKGctHSV+002vZXRd7/HzN3TiIF3gS+IZD6g==", "signatures": [{"sig": "MEQCIHg1GIPVDJyWN12lC36VZU/iahIFioeBNQL3D9dRTI6yAiBXHaYldYdguoLlkyV61vNTdidD0Z41YI1lDZVBKa00qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGScCRA9TVsSAnZWagAAK08P/1ypk1rRsSH0JFjj0ikl\n7lZnJpEFsF9MFFBGNP/RyH9+Owd3VNw/xYTDKPc0I3NP/ZDzSXST4lWEOHQp\nUM8CJQqQYAb/dbHljsqBAS2hD9z67hxicO35USs8k7V2q9ZbgIuImT83rDeh\n3c/DDK9ITIElDbjWo0nBnpbV1immrvPPdsXoeTXNHRwyb5APDSxz9y3A3WTr\n2BB015pUQTn90jvrmP9GB7Em4IiNzhMpjLjhavUEmM8Zl3Strnsu17ubf5XG\nWrt4/f1yzw4Kd3zO4zrISs3yxh0gdW7zfPQpI7OYBsQ766b2FAyKQj3DonSb\nHbZea/BbD+/x6mQAsK2eD3O+e7Y1yETM4qIgbuHrze4EZPjyOD7NRUm1CIUX\ndYxrPRBLKDwAzNdmklot+OAvDlZJ3c/5z1wB0bYingzFcDnCKUVXSYwjv6PV\n/O1Ohc2v3rIBsvzLwpjbvni1vYkExg+X8byKCx/mDwfU2uQRW33uvYbqMsxs\nmv8FWP1k01EUKyhImpiL73xE/v+AS6zzkY8PibUgHCJ5p7hG6XsWGDf2JwFd\nZEf4aIYmwDVTZHg8INLS+Pxuwf1LjbGocMju0c7PVP5tTw01IGWrOLciuWho\nOZEZ8vlN2vz62/okTIM/fJyykyjj5t2kAqD7ww2TddYfHMTkVb4MnOFtlPL6\nI1yr\r\n=UbnB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-rc.0_1533830299868_0.10768201683123846", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a4b60c55072caec0e4eff8bc2038e464e5fb995c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-WghdORLpYOmv9GVFeZCVvXsNthUDHSp7d6qhM4j7lrVOcCb5QWiSgXHTQW9qGErRs3AM7KJnDEy25bxE9UlZrg==", "signatures": [{"sig": "MEUCIQDC1pRkBQMgwce9XD65OXRdj4OQFhj9XrdKtVRwihCSpwIgBik5Bn3q0dcT75EMlOJaOVTZ1Sw9YImuWIzsB2rG5mw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8gCRA9TVsSAnZWagAAoLcP/R8FZhdUsKYmbf/9CRT0\nuRxjS0fFXAVJDytDGEpAkfonr8LfVMUY6utwFIuSI4l/Xd2ZUxWr7lLNGFhE\nrVXQv2yTbidUkcxT5aBLA3Si18Y5GcoTYDfoxDChvcEENhR3Fz+X3DzaVeFX\nSTppMMrnVXZM67+JPi358BcoeGRYoI9+XD9nRg+CwIszzwXmA+CdRQM09nYz\niitt7mbjFLiPDchuMqWZunnws5k8ldfEVmagxHqIyLbGUu/Qg4kNZF48Kf1/\n8pNRdhBufxqjSE6eKrNRnxA6lIDTkqNXLlabubpArjmv1VSQUULJdDHwfHsC\nwv6LM6ngODAyT2/MQTN0XqxrKVpWT6NIJn91zFvu4humKVhysgLyVScaljH9\nBErGBMJr71dHOBnGeujQ5xa758kYyXXAeKa825pZjC/Jky1lRRaWR70ivtQ8\nBU9bFuePM1Wlap6MKzkjA0xplJw8xyNxdxyNFsTe7NIuOS4pWvIUd60n6q2b\nNCQ78iFUOpVCYrdPmqqQyhGGdKT1vZ5WnjDyn2RQWJmp7u/FJzkPsJ2/q+Df\nczBJr4aun7zPuskNFOMMVKTrmQsm63RjdkZ2gjYAfSZa3PapBevyEFJWi8Hp\nFrvzK8UiN9QyBT1U35JDsicaQNS/s7J4Z+87ydRhIF+dyFQC/A3FYJkr0lsl\n2tEq\r\n=0Vkt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-rc.1_1533845280102_0.020670468200482794", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d896b3e6106a011a50f7ac4457bdd2d849b7ebaf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-6j63in3ksnqbe44PzqXFYjpZJ44xPHIp0AU4kd/mmDK8ZW5oDRfUY8rr+qbK+iZpdjHP+NhYY3OjyVMNm9impA==", "signatures": [{"sig": "MEUCICb1O7nh4RBFhwz8h6a9g18c2FvUjeUJq0/sLfJMfpoGAiEAscvpGoSpoSI5u8gR2+qhUkPrDaIZemuhJD/x3Lm0ZBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbPCRA9TVsSAnZWagAAE4IP+QBAnrYVZQwnFgAH/Pfs\nx/a6Pl0NtPjBA3A4kM4FCD41L15opTUX+imBcx8pXZHmJVL7vAFSX+6ia3NV\nfzwhx6cfZRlTJE2U/Or9l4DdBPrX9Oxc1YXVBnJZ/oCcLfCej/et9//ws55q\nlLhi8xSJ82nm8LQhn4fVrkTTV/MVxK4jatCYbYSfJ2tsm09nT7AMWagbBEm8\nJjdLfO1Ce8Kg7n9hMjxhXHi8sgLE8XqHRJMkaFTY/mgz//tgJ80zTxeKm2wJ\nd7Cw7Vp6taO5FX0hQgpOGK27fsjjX0q3pw0rJMppw6WWrX3VM7WOK8+3KS0E\n5oFC4h8hizgO9qSOYX3tFjfLAmK6QpDLxQqSfJkF6dTMKWn1EcRswWbS/qDW\nsltyjAF0PjM9hNupSsGOZMZos6S7jey5XwYN/5zczOOYaDe5jpLGyFJEX72j\nFnwO0K0zcB1u4s3VzyhPu+QBkq3XUbRnI53jbAhtJkshTpAaCUWlaBzMKaNe\nBUWy0Ci6FISQllT25MBPYqaalaQVXvNjxW73OWeFlrBTkg+fWT+N1h7+uGNX\nE68QvY2szJAUqCX1q8GceAWLM6B7dw7i7BT5/gMMS6aI46/70f24sQA2w6m1\nwNRydV6m49YjbBo/N/Y6gstlF+vKEjcn0gUoHDw2Q+r857COxv7AjTpkTua/\ncken\r\n=pWYG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-rc.2_1534879438429_0.6379583892098777", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2e39f8d9b846deadc3cbbd6bba75e6b80c07a90f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-L034ZmbH1XWxD7M2Yar8DQCiRSbJ0IUGXJ7V5kLXE0fp67oRBEGo/MI76kWolEG1+TRud977tXvVFtg55QNUkQ==", "signatures": [{"sig": "MEYCIQDxLIMOTneFwQirqO4kI/s13M5CHeKen5+Q8uKwRva5PAIhAK3/rxQ1tDD50chsQJmx+rm045Tt4ivOaG7ClMbF4hCl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmBCRA9TVsSAnZWagAAtCMP/A7jMD0xh48uXesbb3uV\nsR4Qz9nWqoIcEvF5ZBHhla6vncInHIlnhcNvW5YPMT0RbtxhZSTtUa3K8ZG0\nF6DEu1GFIW+jJRPkOA8sW+foUmoGk+Q4KsIW0MxTA57NHpqVs1UFZqR4Bswl\nG4vNH1EocGsVNCWF7UasftILthWWmXTBmEzBna6ETuUW+fpFbQzsXGtRN2Cx\ntT1Az+28ReAYEmsKnZPDlfVP/IhRVNfAFK5r0x3QCaStOXia2lH3pfV6BAxy\nVE/cBCk9uBjqlPnRtHWgMMQwfwRfZg+iB0Yg5CesTuqIig8bYE4vL6ZmyEMQ\nWtICUjk3AyioxTAAzdQ90d1UguXqRY3JAhv92azXMLpi6KGeKlz4AtKaBgli\n+dxpbCpz7YdYex0cZg++2LZz/xZrT4aypIxxTHGtmvcuXv97atMDDV3JkcGg\nNMEoxXk8WOgpKB60ljU2mDwiCseAGhEfIJXGtvBD2aLZQgM462wtemXPi51O\nFIZqcHYhLd/0afefbRMQZlWvDvkZ2ll1dFNbF3MarUwYF9Qw6j19w9k94Gfl\nbP6he1tDHlgsmsdJgQv7+vqvdB0w4SlT7cmTXZMzPReUQyxapk+lYMBIp7jo\nKRbrdwOMmBK5dxMIvcmSuWW2S95RcyBEGOtlWb5I7s+UreHMpCYuTQLxdOer\nLjln\r\n=EA/Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-rc.3_1535134080894_0.602885144754262", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "93195ca138e1d33f1421dcc81762390f7ea3edbf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-+FtEeshiigGshLn9XnrIS645ABr9Yp+03gQWHuCYnf1MLOtvfFmaWwnSjkuIl/q0aC++DSz6xwOKLAs10kd3Hg==", "signatures": [{"sig": "MEUCIQCyRAvlUDEDttd7yMq7T3KUKBqyalgHkYaUpcwxU13ExwIgLV1+kV3gLY25K18nXv+hWXkTMiFyqOVlJc1QD+THsNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpfCRA9TVsSAnZWagAAhAkQAJmNaGArhtBcuFTUdYMB\n5gJNoJNIppYaZSwJLFybzNH3yr1xP/lub9ri8csEVyfysb3shkMt6toBjVFo\n5m6uUdnTchDWd/ureLoiLf2MMWxVDSGpikCqeuRXlsZ5B4Qd5svl//0rmBcQ\nd6fF1axjO+ePXwan6a3pC/Jeq+eYjgaapXjYX/iCIeMYJBBbDZBrOaAGbOrk\n2w7zkch9aloyMRPpwLNjreu0Gm31ddUE/eS2wNInsc+fVbbgg6+7k7CbWsjs\ntnnbzG/iN/6Mda0dEZlbu9sdKg1vn8gMisCSKlHyV4mCqX0ViJj16ugp9zf1\nS5FFS9/diQu+c4RsmTXvsdK7RNBU6WI/3TQuRspx1flCAj1DUKkbs35NMv3v\nh6iOBoFFDgnCJ744PUETNKi6txXsSpGZ1O10LOG7iKIB940FRzERJSfUBQFR\nAdQ5tS0sABmJmdmiVPduCcuMLgP9eBMwA+0Q2N4r/reXZZjcW2q/EpphsET3\nDpA/iMzBgW07CzUWojoacUMfQMIhthkPs/HZIQLqkyPPtp1Np/j/C4KqwnOh\n+y8OxdV6rHUr3RoqfvhVfhPpWFqHYnok9jZ7t3lFlB4yJg+xZ5VFyBbkFIUQ\nSygD5KEY4Wih+uNkxKg/TDF5uiLLaQYZcRuKhhOchCP8xvZJMbX1t2nNPSKm\nLqY8\r\n=oEc9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0-rc.4_1535388255041_0.6852226614213706", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "96a265bf61a9ed6f75c39db0c30d41ef7aabf072", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-kgAGWcjVdflNPSaRb9rDPdGJ9/gF80VPmxx80gdKz6NSofHvxA2LofECQ+7GrDVzzH8zBJzTn1xlV4xnmWj/nw==", "signatures": [{"sig": "MEQCICdbn9EXHwoxZFEZUjWzjj0tXl08gDA/zYXE9CdgLnh+AiBErWs6hNoGjbVcXaRfaEwJezEWv5FqwnR8TmwqzYcvlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBzCRA9TVsSAnZWagAAc/gQAIFm9NoWYHex4OYjVAuD\nwM/27ShzmT0wN2qz/a744upfA57sqDFkVKJC0JuA1aeknrDq59GxO8Ohs+lE\nQual4UPVdbrL841A9aOO4qFxQqXwS8EopQRoSOGrZoueM/jbPEnTRHXIWJJG\nUS+zcZHD486XNLd0iCRP0F/5QvvtNyoZ5Xx7L9qqvXmmtDrd4Pg+jU7AtGtP\nCJqG1w44SB+83SpB1xKgHtKDlhNAygaWjQathTQe3rx7aBd1mo0uNO7EpgSQ\nrv6yOA0Gs4b+3KLYgH96ZWSsHdar02a/AgI6VnSU3OxKqXNTFtedOqneviQ3\nVNgtr7Vack1CdOwngbeR7E4yWEeurjZGXeRvAVHHAWAmtA/8mA76gYkI4j5T\nK27QOYioHCKzIok43e3+B3GMvhsGzD/KYKPpm/5wuxPdJ2N04n5gcs0ogPQm\nomCNJKilpGvFZjWbBH7ar9Dq9wJ3wCLkNOMC8Ljg//W4U5tMOMQtdtHhyyTs\nXQNALA3LknMC8OHTc03eD21hPFTylSPlS0bQQNBYo9+ouai1jrhdj2/iFhj+\njtFLHPDfbrXZ95hR5Sn4l+6/mTQ5sOsCX83Y8hBPvUsZtcH+DGoiQwS1A0V4\ne8PUna243AR3iRxgB8wzWmPOcUZRB1xr9V9f+b6hN4bOZKR8j/CuzDisYObE\nLlRV\r\n=9ohf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.0.0_1535406194685_0.8739390831405363", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "fa10aa5c58a2cb6afcf2c9ffa8cb4d8b3d489a2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-HiU3zKkSU6scTidmnFJ0bMX8hz5ixC93b4MHMiYebmk2lUVNGOboPsqQvx5LzooihijUoLR/v7Nc1rbBtnc7FA==", "signatures": [{"sig": "MEQCIBtvbzpA9P2OZEBkaDdjGll5gZnrhGUYRGZKVKo7uirJAiBwQAUO+KH4jDBoKE5Bga+FxTMcNzLjbCTWOIxElfq1uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2HCRA9TVsSAnZWagAA2toP/i1YutQgeQ4BKPIdIyVK\nXccWbmCiH0SVAqUNEBw+ziBCu4+gSaIv0+HcCnXNuiDk1anm98vi92NuIcfC\nyib/+/a5cLOKEEre5bQF0FvbkhJFkZl9/poFKSs6JrS92G2o/0XL2JzOHyck\nspw5vA1wJk+z4pmQL3XivMg1bR4WVj3vjOYe9qyW6c+RSW7KvbtBw5adS6Mx\njGjpwqpCirLp0Z4oSv0cJ1nMEtGD/zbFtPjRMjt6mc5XEpYSDvR+0CHjZdNq\nE5UVikJOyewyqfY27OpwI/kBN+1YmyckBGYsK+/1/bblIvyBOq9baimzX4QI\nJSfccl51OUI5BIiKOncsUoia26zxZZCAiFfzIUnbQpjgs5AC5qs36AEEay8a\nJ0AdPPCVDF2PQXnJL9rHz/c7o8bpcHDjUH4FOgvAn4hPcf0tzsNNzNSqHhVU\nJgIXv1hLISNC2lm+tFcPDEBkFNBRFr2uveZPN9FzxGYaW78BZEoMALN5xz3l\nB5g/55z1V5DLJ5PBfmFEY5kP9EDhQHhM/Tac/jnAg+mwcPCYO5JhUTyg531I\nyPQeCLDLsoMfnPXnB4sX19ZJqk1mbQ1PcsypXJZdg9sUWubngP6Uxt/RwpBh\nCu204W182Vb685bONFxvOI1BDuy4gxrMFvZYWegIphdG0xsr+IpOcYEb58dM\ngvIw\r\n=rE0X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.2.0_1543863687143_0.7120413591988446", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aee127f2f3339fc34ce5e3055d7ffbf7aa26f19a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-9VMwMO7i69LHTesL0RdGy93JU6a+qOPuvB4F4d0kR0zyVjJRVJRaoaGjhtki6SzQUu8yen/vxPKN6CWnCUw6bA==", "signatures": [{"sig": "MEUCICin0Te1tGzN/RBDpxVkv+4HS8dmz/S38ze9LOZ5dlvaAiEAopkKCm4x3eGs8iln6gpz/yDBpTgxtRT6JFS1J8mrfJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/9CRA9TVsSAnZWagAAu/wP+wcu1Fe8mHcRqEieTbLn\nwkiXBKw6I/3ZW5ZZPDxUMz9CErhamM1BW1IjlVmolp+5EfEjuAebCMkK1kRg\nnQqJLckGGIU0hhtzXwAj7PbHpquNlBEwuoeHlSE3wnmISs6Nv6Rp02lKWq2d\ndzEbkG44IsHgZw/JOp/Nqz5PeGZuCBTP2iVMUONNy6Cnzsd+OEENJ65mm3wr\n9oAkT37tSyUeMDQQSbG9of9WU4f87n1rLz/sW6Fp8w6aRkzIoRKMgNRJEEKG\nIualNub/CaqQemUv2k5CsvZIzNS6u+pHcJ7GfynMaILKn/+Ha+qu5+Vl8nNq\n2bJWTOksAu8ghOYkxK9j4kw52deh1ZkdeLLlhNKCIjFQeYKPXyobHo/NEDvR\nENzLfpRvJ+Z7t/9QrbiMNNbvOuRQ6N8asAWb3oSBDtIKVm6/3Fd5nb4XJsj0\nA7SOgspHrIhNIVlJuWeLuEpV0ZYmwYisHsZgCOMdN0jF2m2tvrJpnvLQ0zu+\ng9qA4M3lMmSOrK0G3uywrZGI4c+vXl8/VplQX2rdb7sHGiNoj/ImNGpuuErL\nfS+iO0fZ0odEN/CbgJcAONIiK+I06QTXo/8racoUxa6JwnP5o9VALUd6HOIc\nx7Y08u4j73qhfISnb66TTV9Fe3KjvPsa+labLLxzoang8T9kyHy5vzk0JOjL\n7jwZ\r\n=rMMd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.7.4_1574465532634_0.5601620678131702", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "da3e170e99c2fd1110785cc6146aa2f45429f664", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-lJSdaWR56wmrosCiyqKFRVnLrFYoVAk2mtZAyegt7akeJky/gguv0Rukx9GV3XwHGuM1ZPE06cZMjNlcLp8LrQ==", "signatures": [{"sig": "MEUCIHTwGRaP9EnFnDtEIrolva6CNaIIF0V3jpZ2c0uDzWbHAiEA7uqLM19Q0T42tb/0BaNQ1bQ21DVttQkYAlNbyUAv7VA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVhCRA9TVsSAnZWagAAlYoP/R00dGNSWTLj9B7Lm0QK\nD3QFAy51Uo8Sjw4kC2MsLdcQcmzBUzLblqvFrOCfw9stB53Kahi/EAnQUh7v\nPUsmUonW5Pn/lgt7nhXONEYnFVrxZUapNQtKqqVAooUITu09158iYFxfN++t\nS+tKRPA6G2ALBtsLeo+M+Nae0nTVs4Q0Ck8O7EMqiIWAMPZ2E/cW0905b70X\nkwn8jxwOpCzlzmPu6T/NxQnabs3IigoNHyPGbch2jyKc6R8LoMcS6S9DCTNz\n95wdWSTEltYTdXdgIyVes0h+ACdOkq6n9ZjLRdLFgdIfqMC/j/p77+aw16HP\nxoxE/tt+1Q5+KGjQ/AmA0fe/ogyh4A2YyF92HN0CQAqPkDHNC5wDac7Iffpb\nRYRTmDqOg09Cz1GqZF3kz+re+luMZ/2VZ++byKCf7rcFu+i8sGPCdhx3h+K4\nUxrFUSe0YVCpfAK23PdeA3mlwAiRhEkXFUcIWrW8AVlsVd2MK9HKfQz7g35a\nQqcrM+NDeMe4d4oU5APe9PhBQDRxI7uJNNX07gjFKwUhv5JKhmjqC2zxD7WP\ndCdSQaguhdYl6oXalvXD+yuCt+dPXrbtscySnp2/1g4GGQpZrzjv/9et1h1H\nUb2evfSyMsjTum8FexkKSxnq9Jz6j2Ap7v79wsFGhkjuS42QdsuGsJ1eq4J1\njT8D\r\n=ilNq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.8.0_1578788193487_0.3564734814117543", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "963fed4b620ac7cbf6029c755424029fa3a40410", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-3Wk2EXhnw+rP+IDkK6BdtPKsUE5IeZ6QOGrPYvw52NwBStw9V1ZVzxgK6fSKSxqUvH9eQPR3tm3cOq79HlsKYA==", "signatures": [{"sig": "MEYCIQDAUMQneOSfe9YyK8l1gHCFD9QunaVMfjzY+2JYtlL8XgIhAOyaTCPdqJyTKN/SP39vh6YgR16CnsdHXj7kNhhgaEBu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQOCRA9TVsSAnZWagAAiEgP/juSgBpvJPkaZCA4wsB/\n7BJ0UG4B3uMrK/JXnh5+l8Fl+2AJsimLxeD6FzKDfnAuOrhbuX0GrU8QKP8C\nUEfW2pEJR43brgorfRZFaRI2097ZIn7FVtjGHgluwMN5ma2M4Nj1SHpqIKIW\nO08d2OOWub9cQsMAIASE/dCPQ5XVLvOBCTCUrmNfGZdVYDGSEiZobdRVj7tJ\nEnR1k38xp//gdhwaJNfcYMCgcbg6YrykOAl4c9TdH0WNE2Y2+FR6Cn5qfi2a\nNjdyBt1sTmZOBpLxFE6EP4NYTS3iAhGWOEzVjqTmwqPOuaxPObjp9gwrGSsY\nh2srLRSDgQUqPFDbniSaa6N9sGQ6jmbS0Jg3o5CumtcGp6u/fGysJQLoovZ2\n0jMKze39A4dhZn46/fszWlygi9Cujlgbnumw15IF51nBmvpIsnJ5b++p3nFa\nEAsq/uu5+BOtEd5IUWNwKi4qI0lkTBSXnHe3POqjsXZxb09hNscbYn9Wti2l\ntA8JhP9KTJhMaPA7e67pxOYxkQk8lvbpEVjmHzDNAcRPt4hMrM75gqFfgDPi\nm8F/M+EzY5wOBWTIA2cRGMoZGlxT+EyzOW9OF2atvN5Xx99HQgx3Mvuv/KFm\n+IdSTwhF26a1UvWznALBCqHnHmLObT56kiCtsVQVRSmHnyq3z02H0t3fBD+1\nWQRl\r\n=clrA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-member-expression-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.8.3_1578951693472_0.9810698051986866", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "90347cba31bca6f394b3f7bd95d2bbfd9fce2f39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-UmaWhDokOFT2GcgU6MkHC11i0NQcL63iqeufXWfRy6pUOGYeCGEKhvfFO6Vz70UfYJYHwveg62GS83Rvpxn+NA==", "signatures": [{"sig": "MEQCICIPcPhgyXjxKb2ApI7Bv3IX09TzjQ9WbSzzepFSgEZoAiAxjqGPPKR/aWSoR15Jo+zxCELKcfBgt19SsRr/4nUe2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSZCRA9TVsSAnZWagAAkmUQAIJe4Tkwzkfxfr1kM+Bp\naUNZmDeNlXUL5EnwHXCUR/+vkcyi1rsP1cfGAZXnqCc05hg3lG7UldVoeIyw\nu8X4N7J2YTQhB9f59hqgfa8P5Q+65PNwxU9+f+lILz4qQ5uoWQIEF+9H/ag3\nH1Dyujkbb1Lsjr5WkxwkWjdDdJyC8ka+J3Qp3xCOe7eQoBU9hg+6qkf2rm1c\nIfZXyx3GflmGR7jxNNS2ypr5G7lXlOmO2UiPppTn+lWJDlhyGMnr8Ll1Isg6\nKpEp6lI2jVQGE/GDsNfNYxqx2p6RPa4kr3t7ylOxoaiQ5KKMTSgEHjslfx3J\nfMJhEGiBKF8vjMTfmcihCGZZGVN5dpig7oHBxNa3hb7nE3gU0pEj8f+6mlhQ\nkwO9CTd9jAQ+U6i2huEEPEDvG48dogS2r9THf05PrG4gjAHyxnw/j1mATV4W\nQWkZ5+aA5819LMR2rFko7SQDUpPn71WYKAvbEwd734uF4qCfGA0Q2/h4ehMw\nMjZoS26gjb6UG84eL5bS4OyqBZ26POX/CoZJCKLUiegpGEhIg2z7y2PdgfYM\nsZgBUPCs8lI3FaDxH62GLaq2IewWztzUluBntKqn1XhjGvUixxYEeHrr8l21\nJbQ3luj7woJ9Sm7WK6dred8+Dnpbb5v9bLpc/q/zzP0fKpIxEKwQy1P7gbxt\nrgqB\r\n=tg0l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.10.1_1590617240874_0.2144558065170976", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b1ec44fcf195afcb8db2c62cd8e551c881baf8b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-0bFOvPyAoTBhtcJLr9VcwZqKmSjFml1iVxvPL0ReomGU53CX53HsM4h2SzckNdkQcHox1bpAqzxBI1Y09LlBSw==", "signatures": [{"sig": "MEUCIQD3QPHaA710XI/+tuPRT/aLS1z+2v/TNMoMbz9SOLNlegIgfUoFAkMUUsUu7Z8lYGYIdSel5H01bCb8Gb8eItcrPOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zorCRA9TVsSAnZWagAAIqUP/jPWlk9e171Z8HXIrY2e\nW9BPux38e48MDT8ZOt9iGuhGDFiFYY4WSrpCR8JU5Vv6jmoCK0SSNdnHPGxT\nRiAia1QrwmUD1ODe3ZS9eaPd+vQmTvW4hYqQqcUUzzf6IGfSxo/cmukalRI0\n0/T7H8cqJwbDbGMdwLK/m/DfRM7QEA4x8yIv1qDw/4bpTIw7vJi2fq2BWR98\nFUjX4yoQA+D3WVV39tXiMFiZXRhAV+zoGRqMzczx3b0QKOQwnofErhi6swmF\nKm9vjjV+BnZ3yQ2wncbOyBk0L5sw8yvzvrScsHN+whCzDeH0qfVxgQL4eDyB\nnbB+vWhn3+XfccAGrdDq1iqvLRDvU3K6iBQTX/GbIrJB7MJrVW5x4jsjVSsS\nBx84Sc34L5paAqb9793t0Y2zpzEzPapVFGcO71lUJS//VMEMEJHQmpNcVlEN\n9SqHAYcuLPALABf6K+R1zmke26Tu5I1fGxU1lJM6s4OyTA/vdJQpOV5VNM//\njR+TOGT86dSavAjpl/OGzFrzyx54FZU+ialypYuU+ACknpGBoao4jHkdH2Ip\ndNokiKyAgGukHOo29StJcpFHpmJQKXXXKA8lELPnWjEu7mEj4O9HN2koE/xH\n5aZ6EEUzT33PA1lGMudrLf6J0NQytEppw5lwreLQHjE9KAGkJ+UbZYOII+pQ\n4LZO\r\n=debW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.10.4_1593522727553_0.8807129298868743", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "496038602daf1514a64d43d8e17cbb2755e0c3ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-1sxePl6z9ad0gFMB9KqmYofk34flq62aqMt9NqliS/7hPEpURUCMbyHXrMPlo282iY7nAvUB1aQd5mg79UD9Jg==", "signatures": [{"sig": "MEYCIQCpsOu0DuPDdb6KfbYbCY+HdxfSLoaUsrEGZ+wCSbcV3wIhAPidqzktF8Iw44WzQKJcc9mFvVlICzncwqX/HwrxgNir", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/MCRA9TVsSAnZWagAAUA0P/0zbI/4gU5Y14++G2rkb\n7vJtbc69sLg6gA4xrpd7h+tP+DjMLEr146sMLgdbpYFfxN75fpuXX0YaZ2Kc\nTpqHNbJ5Zw8Gks8kIJ0LMk5xcnlajQpQ491w1XEPsPBgq2FIkpSR3R0TfgPg\nxXRqFpYBMxjEXOa8+PdlsdRdqY7NtkI/bX+fyHua6Fyhx9Fq3lz3r2cA4ETi\n2lHMigYtuNzw9LG6WcpJa4dJ1vhCMWcI9o7pJ7Afbv/wwYRz14sImkh0TvGp\nDKz5rM7W62pp8ET5HMQZVw76Mx9GFJ6ssznBtmwViJkU2kayrZrRXADrDUC9\nATmfFpp7LW62n0/I2K1nKchquVnQZGr0NgCgtsTjFzq27VL2FVDSSkTxlgsb\n4zzg6doqXl6URrKAC1zYEM1RROc8ipWhZ/hpwkg/0CUaI2AajfU4aauPQaq8\nJgyscu1mLqltOPZXi1JgFWnG95ta/wMXLSwm3zjq5SLS6Hr2x/r+mXiHFgwh\n9LLn9er+PBkQqvqiImOh7bmNq6CMF2VpeLXBQwLjRT4NVj9q3j2xuF5CsFGT\nVIPgQJ3sQTaxZ8QdrRYj7uRTDu5pL6WdunCb8ReZ3HDToHdh7+abQ9R847P3\nSx6BuuUx8o68FRsoukjyGpMPKjFOr58cxhMSEMS0VTSo4dzN4bz80xN6rSWY\nRnW8\r\n=PAp8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.12.1_1602801611927_0.7347161900961898", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "5ffa66cd59b9e191314c9f1f803b938e8c081e40", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-kxLkOsg8yir4YeEPHLuO2tXP9R/gTjpuTOjshqSpELUN3ZAg2jfDnKUvzzJxObun38sw3wm4Uu69sX/zA7iRvg==", "signatures": [{"sig": "MEQCIE2Elfcv/0KXutFfWLyII3CbBoUWyrM8j9Grbvl/qJbyAiBUQpfuNTe+899LMj4i5ghrnB4dBNNPXPH0KHQWHZZU4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgfCRA9TVsSAnZWagAAergP/0eNUyyPmK2X0TV6G2aA\nDw2c8S+Di13+NljD++I1ZEVsthgnVxZjhMjDZTLPmVPHcH/625U/3r9gjgfJ\nANaBA2ECwjsyCBrYuXAnB2eTHA70Q6XD5ucaQ3kD0wsCIEMmMM5US8PGPBxa\nUCiehDowH+tIWlNkSoqbqSmBgkZRtiYNnqRmerJQct7A6jIRITflZY0gVzez\nrWBTMo8bY3P2j/MVo9fUIjZVtWmXWZd/qp3lwhm4retJFXmp004D6EM4SMb4\nWhqreHlh9P9NQAjlkYQZpSgg4Rl/+CRlbZIy/aTDH12am3FH/6qQlWl033pv\naoYQk8dVZm3bmr84PwgvJ4n8yZk/3JgtYYvkmzlb2lRoSH0VIVmVJlwZhcN8\nG4u7M1wfxyZPgrpYtXHeLigJi9WaS7lDrl6IT1Be5WbnADoyz50b5D8VpIqd\nZSQFFGvjgReNm+srdz+PEAu2a0sXjogXaiDNWtFfkh2zZW7VDNJgc4UeOL+z\nJmZaxmFnUo4qamJ8Lw3/TSMEcjyhiD7wVZrr6VaSCycAsQVFc64DrbzgWAU+\nxTkXdpSFv0gGyj+YNwza9bk4SPBehkS5LSL9hAJikOS5obixbPA5hZVqpyzh\n8G1CE4fi5nKpviTBbdK+KFo9KQ2Z02a6hbpTxQ584gERhY9TWb36ymzG3780\nky6I\r\n=u7O5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.12.13_1612314655210_0.4310360622994378", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "b39cd5212a2bf235a617d320ec2b48bcc091b8a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-WkNXxH1VXVTKarWFqmso83xl+2V3Eo28YY5utIkbsmXoItO8Q3aZxN4BTS2k0hz9dGUloHK26mJMyQEYfkn/+Q==", "signatures": [{"sig": "MEYCIQCKxHmXJ/o5ZnzG2GR0dzpjcqCzy26zmNc09upuGbB3sgIhAPEiErTNJsKhsQzsVxvWDn7M8QepP+kdQ1+fKXBsuBD/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrECRA9TVsSAnZWagAAodwQAIt5coFyve9//gw7KlT5\n8Zuk/k1k/2BLlWxg9/cPQEdaJZekUpj9moshD1MtwkeafC027ki1PZnWw3E/\ngHJkYlQCADMHhyabCnMVo4iv8ceAMC++Y8R/ia0M0gmSMhrWZa3iZmO+pMBR\nB+ZWBcy9THLTRzi8WIS4bDDuAGB8uL3cSXY4RkRghce3XP2WRActG2fae8sH\nWyya/hc6dGb3d2uwrcTxr7BW75xKjXKSC8ggVNIP9psFBloC/KSigqbj3Qre\nym541ZZLD1Oed8IVTO1oYDKeIA9F2AvjjaVJuHJbGd5VjskYvE12xB586Eqw\nAvh3YbwR4cOUIANkIK4IIGvctGqKxnDpzpn5GcdUfgAIpixN8fFyrPdDecxR\n4YeaXVByhnB0/hUzG5yN0fD83LR8bQwMPwnZKjdUE/H8XuHoK3b6269B/vwj\n27jNDWV6yquQWCMHUJOj5/0O0R83ULttJepJm3NY4xntGalDjseDKJgEhZ+Q\n9LEZtNPvSXzkOSUm19aNGZtPWUUScpIsele0Lxa0Nfkm3LT6mK3kYEyMjTwn\nno6XnFoKpTKjf9Pqx4fYwsMmiGxbzQHPq+COS1ZxdEpZg508ibDyV/8GBD0T\nK2v7MvZdPiddy5LPH4S9vR9VIgHb8M+VtIS21mWjDReH/cY2/4Xw0ZM0s39U\n99PI\r\n=+Fsu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.14.5_1623280324238_0.8527116561835826", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "5251b4cce01eaf8314403d21aedb269d79f5e64b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-WRpw5HL4Jhnxw8QARzRvwojp9MIE7Tdk3ez6vRyUk1MwgjJN0aNpRoXainLR5SgxmoXx/vsXGZ6OthP6t/RbUg==", "signatures": [{"sig": "MEUCIQDwQSxkjxRSZNCdZe9BWrron5OJdZufIU5VTCQ5/1+3OAIgKhp/ZEKb2wl38H1JzASUoRi6V33wIEYpU8DndTm29wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3259}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.16.0_1635551250432_0.307667797831769", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "4bd6ecdc11932361631097b779ca5c7570146dd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-d57i3vPHWgIde/9Y8W/xSFUndhvhZN5Wu2TjRrN1MVz5KzdUihKnfDVlfP1U7mS5DNj/WHHhaE4/tTi4hIyHwQ==", "signatures": [{"sig": "MEUCIQC02Q4bfRYNHbnKcKsHfHQps+pKRYNngzHD5BEiKlSHTQIgLg6pqyAtJWWLUUtu7B9f8dVJLkwbbVL/H+66WKTkVRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kACRA9TVsSAnZWagAAEfYP/ikNp6q75XTVNXaKkBIx\n0JU1bJOYpwczcpXrCwGSukgysXVRHwSfF6HJERbGiEf/Z9uo8KDZYmD3Pz45\nMemJA5WDgidwb+vdJdFSsjR7OOEC1V1as6hGfiI40g66vn837g0z5CGBnF/j\nKoejLkChK8VgJTY9+sDa/DB6kO0QyPSB5t1koxm3lk1liHHmoSblg4r0c/Fv\nfaqcN1RAhrV980quli0ywkniPP0AbsPziSUGYW1zugK6e+s/l6NVqtOSMTAr\nP5XYpXSaFpxY7shcnZMrOAWwbrzul70AUFS0c/+r0OpsnChxFAPEHuSa53ED\ne2D0SgCmK0PyFySmq4nm/cc6MXZbRNzRHieFjf/0eSrIFiXUMVemMnJg3Pbf\nRPicpffSiUn6IWDqV+zY+KZZ5phjY6C+SMnCwIZPq6n2vcCFdpMclsFI+CdH\nCdJhZ0U2Cg5ohgKVswk6Y5L05VsPP9rGNifCXxyyG85+3ulINDh0OEJagUjD\nfIk770gKbzB6rxLuQM049BiIzcqbG1zKQN007wGBFZSbgHATszyWaCEXDMhW\nX+2PS0+Pl1F4EJMVlLxupvs+/JzTbm5+SkFQcDbNGFnIKaF1auwCWj40tZdZ\nO7stXAMMn8I5UDI9Ov1zYVhxD9d0Ohskqg80rm5N7fnvAsqr8QWDgIOsn7nR\noA2c\r\n=K1HU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.16.5_1639434495933_0.9999609643030127", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "6e5dcf906ef8a098e630149d14c867dd28f92384", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-mBruRMbktKQwbxaJof32LT9KLy2f3gH+27a5XSuXo6h7R3vqltl0PgZ80C8ZMKw98Bf8bqt6BEVi3svOh2PzMw==", "signatures": [{"sig": "MEUCIQDMERhQadIzQMY50yDGGaSjG1/YfDLwqkPhMNA0EIB34gIgdNZsjbaOQzjYqC7DFsEfsLqYRDPL/58YOIl1nw6F83s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0nCRA9TVsSAnZWagAAszMP+gNkUstb+DVgj4+V9Kfs\nM3VZHGU3SA/gs5/6aCxp3dTPupJilUkct/CXgcnn4BkTk1+MEB3l/KkPhzQ1\nqqBfBxRlWWaKM6ssFaDCXa9RC/xRlJD21FvGWTIizMlFX7rTZwztSmdoO1Vp\nn3nXoay9ZAQrrrWufdFZUcV8zbIppXO+byieCgSKxZlmfnKa4HKGOFM7Eefq\nqaCghr/pozi8JoZWNKywBQz5ad8F7T44RN6B4jHc1VhDRSZNTiWipSWnIfnQ\nJg89wCzdzY245wyD3cFV3Mt8/3CyhbHFEivaezJlHaWdfKazVNFbHvWHVLAf\n0qUToQ82DaQI/uxJasUHRT85KyW2Lyjc4ynubk2t2aDPL8aDAdSlExDZRYy/\nNs4sid+W6pvlgnqt9XOfYRdqqU65gjV1yv7joFqa+FPf+TvG+uRyoB2CXxvK\n3nAj74wEfhvvyg6U8/cjZ+BUqSfnNi6xckcgqICzdfR9DfvCEigVoDrhYkAE\n6L2YukituV/aKfxfQ5qkGhtI40qATLLF5tw+Go/fybFxPbq2HJGopJqIwCqi\n6GWlWcNgXFLq6ri+E9q/zlIftopCfQlMZOuvVK3stsGScRTWsvbYlrtuuemm\nLHx/VBC4mBJhfszFuqY+R7f2ukxBhpu0hAuMZTe87kFDwObomY7K+GpTAzkB\n7Elz\r\n=tTsB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.16.7_1640910119429_0.3915468248465257", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "ac9fdc1a118620ac49b7e7a5d2dc177a1bfee88e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==", "signatures": [{"sig": "MEYCIQC20Cyf+atNa0tvklTVeqzIAPHK30Kf1jtmWJ2KcxDdrgIhALg8mmyxm8niLwrQtzuwCZpuDQBklRkjD/bkV5pazXsC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAoBAAiI59KWnRq14n+rpJfnWKkbb6YRH6BKBMtj+56Q7vrTSIR+94\r\nOS4BcB4+TRju/TWSi/UWF6cJi6ECTHj6c8dmW7ij560nsFaawXJFUTb4tuFx\r\nYJM+ZBWbyHvg258J+HdFQ24KElx8vOGt65BzwjpiWczyWN6mgjF9Msutc7BG\r\n6eXC7fcf9oDtpDImh+pjDCp0LZ2RR2Ik7+BuuAXVuToEHvhN+xsWtUNjIUCt\r\nKF6UQW6F2+7zdGq53HBkYHCHgd387bPmQ7S7wrrbbB8Z6QY/p8hALlVCgLsr\r\n7inoZCzgSjIYIIRwkd4vy7tlQFWxMyV4rSY89WFFN8VddFdHc2EcLWuOo8ld\r\nPdX0W6yhaRILj8FXKQDKRe2z3PihKC0WrwfUDGIVlH9NNZ5Z1aeTYNknmxLB\r\nI/luH8o/s7uegLdwgvL9aWSkHcrjZrfvcNVJPl6T+xtIzdKENd9sKqBE7g68\r\nOOVgMJwisWUhjiaLSAFwr45O6arhx4OV59e5poG6zUhpzqfKkxUt5RhjJUK4\r\nPyPtXrr6RgpQmkYlU/wtT/CdvZCjF+jtOH8o+TLnFhbhbAWoDKsGcTygzVkM\r\nUXKr6GKN6chDBUhrSuLLPcaQy8BmKxbi61CMEokcI5TXtPktkuxEkfUKdG2B\r\n3WAoMuhaAh21Ijzxa3Vi68v9jBty9POTH2A=\r\n=zIhG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.18.6_1656359400475_0.38069889304814764", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "ea75917f3e0ca7249ed58089402bd19cea2341ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-wFCzXBachCV2qLvuqHtOmBNXpgO0z/3foHUCq8Y68Eh08zwvf34cMadIyvNFae68J3byQANKnnMxG3AX1DLYmQ==", "signatures": [{"sig": "MEQCIFjn5fl2mpo5l+FLQThFsDL8T9mPNBPp8Qu0ol55dSz8AiBd6NvcTSx5jpWWgqalnbIJBqPtJwQ889HFl3Qodr8WdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooEQ//ZYt+DbONdsJlV6PWi2wHmIuFih2JhMUo4tenQwJxxS959ugY\r\nGjzbYk1HbRkG6L0oHYaIJrJhT+i3fqfZBI5X6FZqLnJRu2iiKGRscdhs7txx\r\nNEOHpbl3YJNksvGlGA++zznzOmbczbGWmKCqXd+gDTLGcmSPsgWvMNTu/6pj\r\n9ahWyYnFmoj/9YO5hpn9xkSKIQo8iAV+B6P08+mia9dfmyq0ldKyltf1JV9T\r\nvcnyiiPXVMoLQ/Do7D3TqD5SLo1AyrcXwWstdEVh1pe0por5lMhvWqei1v//\r\nEftTzl05zBNoh2yn92zA5yDrvsszeNsuVRqQFL3CzKjlOzDui1ckaGVoUgCM\r\nfcthSOUvkGhLxJVZ1R4WGj+k0AwOkNrZ7Pyz/1y32Swh/SSS5mjDKnhJ88qh\r\naAua3SaGhUst4MVa7zXXcTD/Mt9vCgja2UTglPa4Dyqfsz7mRrbWthSi4+CO\r\nhD4r1td2NCdA2TJS1NX47/LM0nWsPHdm6PScmCPqodKxvCnfj9xjzLzuePj9\r\nHmRkqOxFlCX/Kw922ntoQSs3FlNPGbB5MzXL0dfMnSzWcqa6xi885pNFdTUS\r\nw2IKBQA3n6fZmfHXgcQ1boDurnfeZh0pJT0gOZzAMYsa7ivpAcfALJ73AmkC\r\nlrXnD+FpLI/ZhXj6VI8QP4nfRqMmQP/m2Wg=\r\n=6fWp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.21.4-esm_1680617364353_0.9878180281183193", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "3e78032d035612bc96f91e8471cb9597fec7a534", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-ookV3Qujjy0K0q6aJu/b4BhnGHLkhPlhBjUatAJ5MFPBirfoxzfDCKVYB73zoYruP6HWiR9aWJtUysAhkhjtVg==", "signatures": [{"sig": "MEQCIAJPMCWz73bDU/nKnZYc1RGipDpzb7N0e8zyV5lR26y/AiBGDpx13cUWL7vy1LbVnQdWNJlsI9YAC6g6buyk4/eaKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSdBAAjO6pBUGYt3Y9QuU9C1joYstaV1eKS2QFFI407/xCpgAeUn4P\r\n7WDjS4duisEaiHddQLx/FOhZIRFZAL4hvKLza+3yaay10vVCtsfsOqQpe/oo\r\nOUgZAFEB+iYr4W5zlScBJMjBnXBK0+XBOkVju2BXdHSiV0ie7b8oaZ27W7w7\r\n7518Ig5uHCI33ZA6sWpwrpyyB/O8Zkh0D0R2XgKTgpNIXJcyIXlVHg2h3k+E\r\n+jgkQZGguX3Yw4qJAmVDtX2wQuakb5yzIzARW3dKFyxY3VQURAqzydrjtbAA\r\nf2KJomKUxNubZWMgAwOxG5lfHcoSrv50MA4u1r38guUZ5E+PXCm5m62z0cUI\r\nt/YMXt8/h+5kikp5dLQ98Hvokr/l/gvRkqiULJ2p6hU/XbVxfduEwZ6BP1bI\r\nxb2omL/DpiUmZlVbr05LZyYKeU/KBpsMSuYqliMftFfj+Qa5y+Gk0GTUhS+m\r\njmR+zgeNsimYW/9U/sUJvo1VGOovgNqvlodv4LHGw2az4XW1QhmfXY5HVOcu\r\nxLtI5/520IfYlX+PnX3fKQ+qVraqZ0uoHrIwQ7JB8PmCIkCp6A5ON2UQ1gxv\r\nWCMWmCvSAS3DTGh3GOK5GDcehaUt1bYeRJgre/ffxUzW3zbjJIBt+ixy2C/H\r\n0kStOkrotTQZHVwSrCCX5bGgTwMiJ7moUfg=\r\n=atG9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.21.4-esm.1_1680618075028_0.32360386881272896", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "771ec3e006506c5025ced62605acba7b22437b92", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-JOx/xcY96LcQcJ6B1js6ZhYTzbUnieWXe636T6Ktw8n5MEmU+dwQDtd7axf2FHKW+syuN6Iy+AOy2/DTjq8eYw==", "signatures": [{"sig": "MEUCIEhJuLT/mdWXY9aEAoyzjQfhZ6xPjGmNj6wS9tfDg2+vAiEA8IK1SvMNXc0BHlm1CkI4oXMsE0rBslVne52ToEWvQRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqppQ/+KDS3iocDuCSLSIWDMQr+JA9n5y5V5y10owW7qtV5WMb1XpUi\r\nD4ZNT1/kR8PlE4asSgdV5bbppPKUyLTUe705h1H/H7Bg8edV3h1dMWOfTIum\r\naPC1VggeIWjQ95YamK9FM7VyLQ5NlaqUL5F0ab3Lw97nvDdcpnEPnWPVVe13\r\nSYl1MjLDl+LreAdQOng4Sy0elXk8UdasZJdSkFcmPihRu3R7Xdju06eP96or\r\nVz3hs43pWEInvTEQmNUiqlDDxhStHjtF2/kFVy5RkCUBKbjzTlSiMsOzLwhX\r\nJsysQASLAtL97ijyStpxBAA+BqleSy7GUXYlwMw/5B8jqL41YPGA62ksiHD3\r\ndoiOpVFGTyJBBXhmE7WbN9SMfYRE/jOLxt4HrqiDPkXWnwFZ2/arsIL/jM2i\r\nKi7sGzPctfLiIsqhkuwzirQRUNTnRBRCmVneDXG/1sirFoRpUoTQSDmBX+x6\r\nQdj9WZpp+JcXO2aQqe4HlO3AduGRwKTO8McBmZGSZH5XXkWP2A0r7HGxcXy7\r\n6kzY6LtXYRk9Jiyff4r9aajsvbXh9veCt/b6brL0p0Aec+SghcsKF4z0cr+D\r\nmQMkn3u0t1mmkIJWOMNZoP3iRAtFN0lFyWEzi9nb3n6cEv4O+WichNs5zERc\r\ns7u3Mdzq61ActU4BUwNX5+N9CFnlUGiyb+M=\r\n=3b2N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.21.4-esm.2_1680619156121_0.9281718835706934", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "3b212082b4b54b767b78c44b5f5a07866d4e3b37", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-TB7c0F8vst0C5+305S4N4rbG2z+8UI3iKLUjrqALWAfZrs01esLXroBsb/AjWQGRZBjlLyNfdAQd8SQY8TEVGw==", "signatures": [{"sig": "MEQCIQCyJazUwioFKSSuFDe01p+QxnOKeAzJgQozPmOkHO1kCAIfY4Lc1Y/YafIvT67iaKAbWpLWx9Pqe0mwRCHtA5fTNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjGBAAlF/kEGCFgP91ZDqDnBH8AL9Zyabj7KH2Le745zsrkN/5clyh\r\nYYMaiLQsV8nFLIaSt2DPGDRTMlhheJg5RXrwSVgjVxNG2nI6XzwTZ4kzR7A1\r\n1vHl1BqyWnNkMBMqiGPyY9nRrj3gCEE16XMcZwlpvd5P4pAxzFtz6xa3DWIM\r\nwJRtrKBDYamcd8YtDEFW8iqt6sjlGf/F3Atr8PHBEhBeuHz5gDI+IheOwi9V\r\n+rMcmg0fkzT2j5mvLm9dm7Mz2zuY+qOVs+XZbFciCOE4ZLnUPqTxJ57dQ9ia\r\nTtCOqKQEo5k2lSoghxVQHNKq4TfgivSxCzmo+RGLL+9bky9yXAgxmXeoOmS2\r\nFk7KxikxhRQWBsyaDX1mFRj309h9E/PUV+a9fkjfLjT1ti80i1kD3IMjEWUs\r\n7ZXh7AlfUuswSZseDed2BDeSMnEES0CMnVhEuyYiwPbhzHExropVWLFkZs1W\r\nEc4JORFoNIFYTTdDtYIR7zOJDMwoD2Bw1gvJu0gn44onRv+6txeS0+y9Q02j\r\nTjOFok98Vu0swnKfu7l/V+w1bVBKwU+6BFsokH8MRCjOSpuVfFAPGwkP+kRl\r\nQAsYmvhWFHCe9OW+yqywPD05H4ulZV9Jn0AyVpC0zLyezPUOGr872CFLviMV\r\nfqgWryvAKZt6s6FcmbtmldLWdPSHNMDPkPg=\r\n=rWPU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.21.4-esm.3_1680620167247_0.29988243009215365", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "4b27582bffc8eb944e673cd4fcac92d58bf97694", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-MMdAqFRj8NKlKrncsQtCWgXgrQTZE2iX3JwkqhXAmxzLKsl6mn05pRPQFEvmdi/k9WwzFLZGCyZWoeYiKhaHAw==", "signatures": [{"sig": "MEYCIQC3JUTm0CIW10tpTvhUTzTe/Pd4inA1JqVrufs4VUaaQgIhAPbLgqb/tDox1TCflGrxWsfWGxXYeBre2kBCOvgS5uVr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0BQ/+NjGmoHz+lG1AWEA0VA9QkP5X/GxWNd+673CWs6xTKKoYjN+r\r\nvEU7lBxSGtfLVzPCOz7iMDnuiepnGSyX6uUO+NAesAlj89RO+QBsXEzOlDJd\r\n9nFkXMFKblTUdKDYFc8CwoM2+Eo5sPFUsEmW1H7+s+9K7vSlYf6/QNaX7uhY\r\nhgPM9a9PN5Z711IAzetV3BIvR+u/YPZbDoBO5SzJyVOtd+D7sdXRr453nL1Q\r\n8MaNsFlfRrOneyqQ84wmnjgxnMWlqLbI4qwb9LpcTqRLGNfsKWbZCTCQV1d8\r\n+GOhI5OfTefv5Qgcx00fkyqDaaHmMspYsDeATm3x4oIvhMWDhKS3mFsaw8GX\r\n3fTe17wNuet9gP9w4NaIcJp3ccneJCuEk7SP01ODqai6qVA1BpLJC3GOjVBf\r\nSIonqvTXJAUVypi5fc67kKWMCojZvTLNWZVdU9EUssJL7FormFCsYbT5S2h3\r\nHTxPgkuWS6gRA24rsqC77dlQWC8e0/4zTqgGfXsnRlc2oOlIsfsTkunl4G0X\r\ny8sFHLfiHHh2nDxFzYZrPxWRcI8iuRH9ZqxpaTCNx4ug4AtCau5InUTGtliI\r\ncHqiRa3AINYy2KMSPYwa3yh36AUjqYPYGr9Rm+w5esR9h4XfFc2F4oxaStrx\r\n4jqF7FszhRLJ7AwkiBg62OCHvZ7pNs/m4y8=\r\n=G37d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.21.4-esm.4_1680621199888_0.6828777731260711", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "4fcc9050eded981a468347dd374539ed3e058def", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-RZ<PERSON>dkNtzzYCFl9SE9ATaUMTj2hqMb4StarOJLrZRbqqU4HSBE7UlBw9WBWQiDzrJZJdUWiMTVDI6Gv/8DPvfew==", "signatures": [{"sig": "MEQCIC36HcFxf5tgNIi6uyLFZu1IR/nxC9q1m2nyLLhrPa4TAiAAiwwwmkTPgekWYAoChWOhXi2J1VliwiEtObV9jrobMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4932}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.22.5_1686248477094_0.1732737973355003", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "9d13e4f21e20892e77b7b14798f4aea314f977d0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-dd7UwfCxnuXbCXq9NRPCyoSQpEs7Hg3PjlZoKH07+kbFwy2i7x7+RBBQWw3cwmckcMY+TVq0g3EOtSG9B0CX4w==", "signatures": [{"sig": "MEUCICJqpHQS+yu4Wb+Pf2ibqDPaR/muOgVeU4Xbw14Gg+G3AiEA9NT6rz2iOGY2jNnUZp6neHtE8YN6UpDa3Ap1/czYX9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.0_1689861591961_0.8825090789324612", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "74543dbaee121a5b823c4773c28f804d03ce393c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-okOnWBkuVXTeKjo+RW/5OYVzplUewaiRoSTGe9E5bw87fp4FnfNbGOKi1lgBDTDfGjXQEY0jdT4ajiwp4PxeOg==", "signatures": [{"sig": "MEUCICNJWD7JGRYYsGYbLjadUVAn2h+QFCYxL4GzL/yryb8dAiEA94iRB0v4eHZai7V+M5OPNi4XAL1j33/cHBvChrfChB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.1_1690221106959_0.1193906294295719", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "2471751a8f07fcf3fd11ec1261572a44c42793e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-9PAARC09VAqnswc08zbFyRpaD7GSIIvbGMbcG9Vsltx31D0CbEy0Ya+VH76hM6l1yIex58NHME8ODuRmLF19Hw==", "signatures": [{"sig": "MEUCIBXgz6cNzQhlIMpDi/cSwyec/Mxb3kwHIh+aAskOmAuVAiEAj10IUlhH0eTGl0GQ+td/3BDmd/ggZBmmCmxZHtuh6jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.2_1691594092245_0.775529907840834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "9705991f6ac16b8d59a3d2c0e5ce57b0e2fe071a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-Ra7S54/K5SWNmQCNwp9s9CJEYfgEmKuVT3aVPp62Ina+LTcTr8GbsNTThttrjqoWBJbtwGeb464/4eG1gR0daw==", "signatures": [{"sig": "MEUCIQDeQTzlWmBVgHe0/plxnTjgkENLJPatlYSnNujScaQe7wIgddMQCKAFWiZ7Ry9rENuK4DftHZGb3CPBmu2LEU3Pqnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.3_1695740208805_0.5244154459119497", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "1d8fe2b597e3a4f3c5101f461fc064f8cb8811f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-N1GTDi+9RZd6cmBd3M2/IA3hOAKHUgl99cdxQzI/BJ+UwGGsOrI9+qkUcLmVDiaEGSzAW/OYgBImshfs/wfLxA==", "signatures": [{"sig": "MEUCIQDRbp1IDt659E5VbCNvfYi8yVNRybvnG/reXQ2usBZ+NAIgBpJWYg0nbtCd3x/e+3kRA0utQk0vE++j1da7KTpaSjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.4_1697076374125_0.6850656341151711", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "e37b3f0502289f477ac0e776b05a833d853cabcc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-sC3LdDBDi5x96LA+Ytekz2ZPk8i/Ck+DEuDbRAll5rknJ5XRTSaPKEYwomLcs1AA8wg9b3KjIQRsnApj+q51Ag==", "signatures": [{"sig": "MEQCIHhDZcPPy1Nji9yNBND6Hj7CKC0TMfAoPM1/fKhdV5JmAiA0eZd/P5UNNIV3uLVvElHkRubLkmlSk0mNy8dkXY4CTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5011}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.23.3_1699513432072_0.5117688034786179", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "78795508ce0637f1106b8fd4ba2f38a084339cb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-f3jmSwDifGbcMbgiH5qEyARqZCZ0f+YMEkto+qBotua0Fu5zHDNZoP43cp3Yh8OpnsG1sgNDmzUSJ88Pv+76kQ==", "signatures": [{"sig": "MEUCIQC0qayQASF3uSZ6oT9rM8sGijQnGARVy4MPSirEtdnhiwIgWWw4TLvqnobpOleyxpbjaVXTAjq8PFKHwIn0VV07E14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4955}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.5_1702307918488_0.09445319092769311", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "a367f8126d5938d063819a3c33c77a39ae5b20db", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-m+3RrjZstGQr9cyt+puOK8XGjARSGNUy5Iv9u7bYxg6l/fZhb2hABCpb5hkyWeJiSpy8ZOhJSLU+jj1546iF1A==", "signatures": [{"sig": "MEUCIE2ARgY/xvfFkr1WkJbgKibXyM5MBdrf72lCEPcLxa33AiEA15ZAEaza/Z0iANO+ZL+V29CX/mYsHHeTrZVptbUmGbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4955}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.6_1706285637715_0.9011700752376444", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "345a828118937d4cac4ea8b1bbd0ce54f01216ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-PkAv9cK3UdKBKVYCTuELrQOW9+YeVJOYh8yRm0XNePBcF0Mn1vguMqSJXKGxaLPO8UhbfM69KhXcX+Gu7eVzLQ==", "signatures": [{"sig": "MEUCIHbUkA+ZBmUC5/nArQkhhHfSVKH483a54Usz8xFrjjO9AiEAnuQC4iMQOoPeN4Tbe6fkNbAPgpxJG+R2P/TlPy6z/QU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4955}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.7_1709129088486_0.26612791185086415", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "896d23601c92f437af8b01371ad34beb75df4489", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-4ojai0KysTWXzHseJKa1XPNXKRbuUrhkOPY4rEGeR+7ChlJVKxFa3H3Bz+7tWaGKgJAXUWKOGmltN+u9B3+CVg==", "signatures": [{"sig": "MEYCIQDqJd9yVpWRb0pGhpjP9NEypQigBhlrL7fO9lamxP2gMwIhAKhHpnLlUoopjE8OcMlqRnOMwQ7gATtoJsXsusrlVkJo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4942}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.24.1_1710841720742_0.5727053355867167", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "ae85dfb355dd1798b2b822b6ddf8353696ab3bd2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-ZGx7kPJ+0PJ0CB0oGni+xb/hKcmnktaAlQ5B949zT84g1jFbuURBVvyGwrLFVgfz5lgshEyhaVucMVPWBQXaYg==", "signatures": [{"sig": "MEYCIQCh7VqY/R/dU+3pWge3QPTiNNEAczA8vEdE8FhQw9MemwIhAMRWi0EM60sN1cWr6omIeoXpYbU2EndNCvu+N4vJmg8r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4869}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.8_1712236787282_0.005144717783165831", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "5d3681ca201ac6909419cc51ac082a6ba4c5c756", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-9g8iV146szUo5GWgXpRbq/GALTnY+WnNuRTuRHWWFfWGbP9ukRL0aO/jpu9dmOPikclkxnNsjY8/gsWl6bmZJQ==", "signatures": [{"sig": "MEYCIQCMD6CoyUAEnxZAe0yTjxHdMGfNrkT+yJ1M06ywqF7h/gIhAP7TvFyEgPQhWokQ/on1sZdE5GWRGAbwsuwu5YEMNEO5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70891}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.24.6_1716553468986_0.25266431234784914", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "895f3cd07afd8af863ba2fe90ee124e477273769", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-C4RiVrGxIb32++hRl9HIvIGI0FKcdgSlEK9+824OQrIrra2537a6g8Ho/zj2fVCHBEgbkpZtdLkEalauKGEXhQ==", "signatures": [{"sig": "MEYCIQCMNoR6e+SWhnhttzMs9wqjIu9glc+CzMW3lA5xFl/JwAIhAO5C4QuFd5fMtd4ymh89r2YRPTsgDzdC593K8gvemvA8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71128}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.9_1717423453694_0.5710548001455218", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "5f428810d1ebe6a41b529e03a21507835ccadcae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-jljMFIqmwPgKziB4P5eGRVrOwgftVtmc5tbo1RZY08vBk/q5FYZJ2CrQqFCbKNT7U/SFtk6++8UYDvMLA0pbuw==", "signatures": [{"sig": "MEYCIQC6HiM6HLj9wvzAzNynIHff/3BGVsB8LjtiMcEcDWuaiQIhAJicyYQ+DrMmbSEk5WT6kdkQjJAo/e7kDIzhw+4MdM5u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71135}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.10_1717500002562_0.26243703985635425", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "3b4454fb0e302e18ba4945ba3246acb1248315df", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-T/hRC1uqrzXMKLQ6UCwMT85S3EvqaBXDGf0FaMf4446Qx9vKwlghvee0+uuZcDUCZU5RuNi4781UQ7R308zzBw==", "signatures": [{"sig": "MEUCIHZ2lS3RgfKF+xYAl6BWr1UagabwyJvc600DrxUVsfvgAiEArMARnfICR2tB8gc18mpHbw+0oPFnm/4oAID1xX6BYyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70887}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.24.7_1717593319312_0.6041760659579554", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "192dd7c3088f52a9ed20b9d97f35902ebf5386e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-OXy2u48W3nblz+lNLF3kKz/gNFY0oPtKCh6U7ADriOjOR8jCWcC6xJllxiXwksAIyQGuSMqNdIWRr1lKukNJKQ==", "signatures": [{"sig": "MEYCIQDwIYKcp3Mf7A3bISk5rBbaIJ3McUnoanA2OzKefbGDBgIhAJzz2M6n59aWYtFEjyBqpEreeJwnf6Xwj8rO/0Dig/Xw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71024}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.11_1717751729274_0.8753023176652437", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "b527b7f65d651d5d934bcc93809dfeada2618852", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-2fFcUMdtIMYiS6ycRfTCASvFSDoJOObJ9bWFj7hLsELVR4JeFQ7biPMVjK2XlQsDI/49KnizVxo3D9hfWitVog==", "signatures": [{"sig": "MEYCIQCZiFyXKwUq5e0+DlQa8+27uR6E38QzA9v0odPlfmGV/QIhAKsdVlm967W1iiJgw9t9FMa+DvcY35LLLIdu7VcAEiWB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67820}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.12_1722015205343_0.5909755561860317", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "0a36c3fbd450cc9e6485c507f005fa3d1bc8fca5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-Std3kXwpXfRV0QtQy5JJcRpkqP8/wG4XL7hSKZmGlxPlDqmpXtEPRmhF7ztnlTCtUN3eXRUJp+sBEZjaIBVYaw==", "signatures": [{"sig": "MEYCIQC58FVJLLHCTuSaKYsRF6V1ab4ytGN1kJQ0rysEC+lmAwIhAIVesRrYCapZC3Csj1QUiDnoTPnfTog5N5XRaLORZyAu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75425}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.25.7_1727882085194_0.6452220969857909", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "63dff19763ea64a31f5e6c20957e6a25e41ed5de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-PYazBVfofCQkkMzh2P6IdIUaCEWni3iYEerAsRWuVd8+jlM1S9S9cz1dF9hIzyoZ8IA3+OwVYIp9v9e+GbgZhA==", "signatures": [{"sig": "MEUCIQDjaMmH1A48+qWg57gY4N/6f+rSsltn79IBNSsXkizGqgIgXPaWgfoCyZ9kBDSxcfZyJ+ejyMuGA3xdiQS32VLSCoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4942}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.25.9_1729610463785_0.17820547963500233", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "de3f1f87e6152a69177450ca054347a6b047b081", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-hMv4Pu6Udh1iDQtxeHh7YHlPnRirPYq75eR7/4g1WYVnqaV6D6YHaNjzPC9XK6LEJ9FCEGxPnnozgdcn1tPRDg==", "signatures": [{"sig": "MEYCIQCohue7LFqpS3p+C80SrYQsbBInB8L6Ph1lQga7e8kkUQIhAKDRyO/JPOrZdQOaGRIC/rFwVSRcswqkVkaIYiZcU/TN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.13_1729864447078_0.7666844161464923", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "c7cab523b97b77dd7ffb5a5bb98df2e6c62dba85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-U/7RqLevH8aMM8Vn+L0UEsymMZCDemSlH3m7xvxeiURj1IOIz7JhnXV1Cw3lZzH9xVzmEvEO8GJd6NcbU0tz1Q==", "signatures": [{"sig": "MEUCIA1Aa4od73KOoW54GDMCHEiByNTNYeNA2piGdIhfOnshAiEA3BqCV9TbzKdPjpOtMZ6szaJC7+ryDfsZpdYvF32UELo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.14_1733504038358_0.426196201794387", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "2af96c73e6efc7aa895e699ecff93d8b7909ddb1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-8cAiIhLIHG7t18mx2xMkU4zn6xyPuS/v56/T1+0nyeM5d6VakEOsIVevEaedAYsl6W4ebg8MmHiu4SFGkCV19w==", "signatures": [{"sig": "MEYCIQDf7eURUuC/tAO3bpsfGq+/cvuUCXRD3Z34SmmxYaHbdAIhAOij6i3GLecDroHk92e89AWhJoyl+BmVfaDQgc0jKzt4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.15_1736529863493_0.3943436978047776", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "336c2f5e93c507526817e4ef55eb0ad83c90dd2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-wu64uITGH+OMIiI1m6opFt+ypW6fkt/JuICpw5TW0IZfySAO9SKFsdl1NVlb4M4fl/x9XhPdpStM3w6m8QfDkg==", "signatures": [{"sig": "MEYCIQCFPA8YldOWgjxS9oA5tRU2h39EcQ9g0xwDZKLTKp15xAIhAK+fV54A+eQb0XDt52h0H76j337oE+vZ5BV0HX3NCF+S", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.16_1739534340082_0.4983118062731231", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "64fa444035c8213489bf0d1ea339a9749b0562d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-gFRSOht/InxnzoswxxEQOP295Qnwn2jZugzTAGYpgFFLwF23kX37Ih0IiVmKTh4FeW2lm7St53QdVUah3HXcaA==", "signatures": [{"sig": "MEUCIQDJ8Q2m2PDQeHs5QlBcVMYKZtwoH6eXqzGnaq4l69T3wAIgT6modyw4Ss2laRJfQt8HGlSqJjgVua3Bmd0jJRJ2gGA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-alpha.17_1741717492081_0.8500177089018055", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "37b88ba594d852418e99536f5612f795f23aeaf9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==", "signatures": [{"sig": "MEUCIEU89/HF/XOxXHHOd0j8b45SEenVlt1tdGXIC+KqdpslAiEA1XlLFJjgHvVNrtA8z+6tJzKZV9hSwFuhPAdzNMgB82w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4942}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_7.27.1_1746025729262_0.6141281696134677", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "dist": {"shasum": "ce67ae3b25dec13b44c501b7b38683846c7669f6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-3K1yKGePepUjyNvTlPw5hDiEDctvutrabYOiYzPgWV/QNkx1mzGkge1ViL3JAhK62WAXH+uoFWS4jGiWGb8oFw==", "signatures": [{"sig": "MEUCIGhJPeOw2I3ghy2TCtGdSFvZJfF1hacWkCJaAqDeo0fMAiEAxrzBXMob/nALaQvq2L+7kYp9h8u/s4pNk4q68IhLoB4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5183}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-beta.0_1748620261790_0.4786530296536191", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-member-expression-literals", "version": "8.0.0-beta.1", "description": "Ensure that reserved words are quoted in property accesses", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-member-expression-literals@8.0.0-beta.1", "dist": {"shasum": "75f1d99e5fbd63171a96993fd6212a68df35c9bd", "integrity": "sha512-WNa8u7S0a7co/orGyrZPy+mb1x1iQ9q/qCltMmWrwUMrd9WopfpA7fUc81f7q/Mg+lxX9gSaet9TaBZCeja4Bg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5183, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD9xKpXm+ROxylKcWmbd+9mDMjjyci2goxfAfOXmw6GEQIgObMXewyAmrex92qsDztijZvEiSZG3P7ZWkNc5nHS34Y="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-member-expression-literals_8.0.0-beta.1_1751447054745_0.7209135309059265"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:40.578Z", "modified": "2025-07-02T09:04:15.147Z", "7.0.0-beta.4": "2017-10-30T18:34:40.578Z", "7.0.0-beta.5": "2017-10-30T20:56:19.457Z", "7.0.0-beta.31": "2017-11-03T20:03:25.615Z", "7.0.0-beta.32": "2017-11-12T13:33:16.903Z", "7.0.0-beta.33": "2017-12-01T14:28:19.207Z", "7.0.0-beta.34": "2017-12-02T14:39:20.363Z", "7.0.0-beta.35": "2017-12-14T21:47:46.261Z", "7.0.0-beta.36": "2017-12-25T19:04:38.083Z", "7.0.0-beta.37": "2018-01-08T16:02:30.990Z", "7.0.0-beta.38": "2018-01-17T16:31:56.575Z", "7.0.0-beta.39": "2018-01-30T20:27:34.220Z", "7.0.0-beta.40": "2018-02-12T16:41:36.466Z", "7.0.0-beta.41": "2018-03-14T16:26:09.359Z", "7.0.0-beta.42": "2018-03-15T20:50:44.563Z", "7.0.0-beta.43": "2018-04-02T16:48:26.055Z", "7.0.0-beta.44": "2018-04-02T22:20:07.674Z", "7.0.0-beta.45": "2018-04-23T01:56:51.560Z", "7.0.0-beta.46": "2018-04-23T04:31:15.707Z", "7.0.0-beta.47": "2018-05-15T00:09:00.185Z", "7.0.0-beta.48": "2018-05-24T19:22:37.415Z", "7.0.0-beta.49": "2018-05-25T16:02:11.808Z", "7.0.0-beta.50": "2018-06-12T19:47:17.781Z", "7.0.0-beta.51": "2018-06-12T21:19:50.126Z", "7.0.0-beta.52": "2018-07-06T00:59:25.929Z", "7.0.0-beta.53": "2018-07-11T13:40:16.306Z", "7.0.0-beta.54": "2018-07-16T18:00:06.970Z", "7.0.0-beta.55": "2018-07-28T22:07:17.849Z", "7.0.0-beta.56": "2018-08-04T01:05:43.517Z", "7.0.0-rc.0": "2018-08-09T15:58:19.946Z", "7.0.0-rc.1": "2018-08-09T20:08:00.173Z", "7.0.0-rc.2": "2018-08-21T19:23:58.520Z", "7.0.0-rc.3": "2018-08-24T18:08:00.946Z", "7.0.0-rc.4": "2018-08-27T16:44:15.212Z", "7.0.0": "2018-08-27T21:43:14.763Z", "7.2.0": "2018-12-03T19:01:27.290Z", "7.7.4": "2019-11-22T23:32:12.768Z", "7.8.0": "2020-01-12T00:16:33.665Z", "7.8.3": "2020-01-13T21:41:33.593Z", "7.10.1": "2020-05-27T22:07:20.992Z", "7.10.4": "2020-06-30T13:12:07.800Z", "7.12.1": "2020-10-15T22:40:12.062Z", "7.12.13": "2021-02-03T01:10:55.361Z", "7.14.5": "2021-06-09T23:12:04.368Z", "7.16.0": "2021-10-29T23:47:30.564Z", "7.16.5": "2021-12-13T22:28:16.058Z", "7.16.7": "2021-12-31T00:21:59.582Z", "7.18.6": "2022-06-27T19:50:00.647Z", "7.21.4-esm": "2023-04-04T14:09:24.542Z", "7.21.4-esm.1": "2023-04-04T14:21:15.175Z", "7.21.4-esm.2": "2023-04-04T14:39:16.383Z", "7.21.4-esm.3": "2023-04-04T14:56:07.427Z", "7.21.4-esm.4": "2023-04-04T15:13:20.020Z", "7.22.5": "2023-06-08T18:21:17.253Z", "8.0.0-alpha.0": "2023-07-20T13:59:52.143Z", "8.0.0-alpha.1": "2023-07-24T17:51:47.166Z", "8.0.0-alpha.2": "2023-08-09T15:14:52.443Z", "8.0.0-alpha.3": "2023-09-26T14:56:49.142Z", "8.0.0-alpha.4": "2023-10-12T02:06:14.373Z", "7.23.3": "2023-11-09T07:03:52.261Z", "8.0.0-alpha.5": "2023-12-11T15:18:38.701Z", "8.0.0-alpha.6": "2024-01-26T16:13:57.915Z", "8.0.0-alpha.7": "2024-02-28T14:04:48.654Z", "7.24.1": "2024-03-19T09:48:40.898Z", "8.0.0-alpha.8": "2024-04-04T13:19:47.425Z", "7.24.6": "2024-05-24T12:24:29.160Z", "8.0.0-alpha.9": "2024-06-03T14:04:13.848Z", "8.0.0-alpha.10": "2024-06-04T11:20:02.820Z", "7.24.7": "2024-06-05T13:15:19.488Z", "8.0.0-alpha.11": "2024-06-07T09:15:29.438Z", "8.0.0-alpha.12": "2024-07-26T17:33:25.520Z", "7.25.7": "2024-10-02T15:14:45.385Z", "7.25.9": "2024-10-22T15:21:04.016Z", "8.0.0-alpha.13": "2024-10-25T13:54:07.281Z", "8.0.0-alpha.14": "2024-12-06T16:53:58.534Z", "8.0.0-alpha.15": "2025-01-10T17:24:23.662Z", "8.0.0-alpha.16": "2025-02-14T11:59:00.248Z", "8.0.0-alpha.17": "2025-03-11T18:24:52.272Z", "7.27.1": "2025-04-30T15:08:49.503Z", "8.0.0-beta.0": "2025-05-30T15:51:01.951Z", "8.0.0-beta.1": "2025-07-02T09:04:14.913Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-member-expression-literals", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-member-expression-literals"}, "description": "Ensure that reserved words are quoted in property accesses", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}