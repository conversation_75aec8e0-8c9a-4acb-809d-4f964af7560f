{"_id": "@eslint/core", "_rev": "17-f0e4e7feb22b958094b11b7f99a2821d", "name": "@eslint/core", "dist-tags": {"latest": "0.15.1"}, "versions": {"0.1.0": {"name": "@eslint/core", "version": "0.1.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.1.0", "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "cf6bf14ce7112cb0af306ba1cea3bc216fa77341", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-TsGFdKFxmTWubuKObLhJJCnn/4jUecSWPnAqkOUoeeBtWv4QSFSkLo7w3GW3xQM52LyoKKHblKoN9+noM0NmTg==", "signatures": [{"sig": "MEUCIGCnHceD0oXRLRvHSoz8IocVbJNB+r19ryAJZvrtnTy9AiEAwB8hFSnXha1V3LYfvN13zESghXqA7rxrJ5cifvMq6Co=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24313}, "type": "module", "types": "./src/types.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "gitHead": "fac14b8cad5a8119dee73d2c5eec8e5f01af2a43", "scripts": {"build": "tsc", "test:jsr": "npx jsr@latest publish --dry-run"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime-agnostic core of ESLint", "directories": {"test": "tests"}, "_nodeVersion": "20.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "eslint": "^9.0.0", "typescript": "^5.4.5", "@types/eslint": "^8.56.10"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.1.0_1719418410849_0.005592107480270725", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@eslint/core", "version": "0.2.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.2.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "be35e5b5e26dcae33b9bab38e7ef49b313b516f0", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.2.0.tgz", "fileCount": 4, "integrity": "sha512-b<PERSON>kucH6uxPOaUeUoGnwFvEz48ooF5QcuDKSN142iYWCocLLJ4k1kC74ftzmFNC+gRpZOpRoPsk34PAarGuYFA==", "signatures": [{"sig": "MEUCIBlb+orcLQQjr0vsFsbmzcYhgya4S7uZplvvhLLLZYngAiEAg1Zz3zzaBf2hQhvUCleyN0+yB/416Uzhs9QDgSiFhkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 25188}, "type": "module", "types": "./src/types.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "gitHead": "20ba992f0eac1af2d6e29b30a5b6612d4f552210", "scripts": {"build": "tsc", "test:jsr": "npx jsr@latest publish --dry-run"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime-agnostic core of ESLint", "directories": {"test": "tests"}, "_nodeVersion": "20.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "eslint": "^9.0.0", "typescript": "^5.4.5", "@types/eslint": "^8.56.10"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.2.0_1720729621488_0.9050538461196806", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@eslint/core", "version": "0.3.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.3.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "198782123d0132b420a1a52edfb0ab1a39760762", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.3.0.tgz", "fileCount": 4, "integrity": "sha512-GTNr6el9ReTPD2PL3bce6niMijnldAC/v+B0QcnFRuu3Ij3LuUp7nWg2R7kADi+PGDj6QL20FsfbkF94ePV8yA==", "signatures": [{"sig": "MEYCIQDoofpH32Q2zTq2SSs+s/L6LEmsDSp/+0KfLgtnGIJIBgIhALrjpMJ1B/7lcV1bkF/IoDPy7gIJDCU2WwX3Hx3oI18o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 25979}, "type": "module", "types": "./src/types.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "gitHead": "7342e1ae8720e58d61f0aebdfb9f1407d9177d2d", "scripts": {"build": "tsc", "test:jsr": "npx jsr@latest publish --dry-run"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime-agnostic core of ESLint", "directories": {"test": "tests"}, "_nodeVersion": "20.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "eslint": "^9.0.0", "typescript": "^5.4.5", "@types/eslint": "^8.56.10"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.3.0_1721662067160_0.7463899113922114", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@eslint/core", "version": "0.4.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.4.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "a90c2e339018621ff8b191770d74af0a49b27743", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.4.0.tgz", "fileCount": 5, "integrity": "sha512-dJzhhN5oSbpaoFWmfA1iEBTINf7gYyt3ZNjqDm0xw1UMz6QlA1YjcclWhPhGQSR1XbwAIon00y259kaBmjFgNg==", "signatures": [{"sig": "MEYCIQDfli9MkxuO868k7yKdfzmZfUwBz0UpgkAwvjHBXcOwhgIhAK85+eRt+JeYklTmW+YSXsBdgy2g9IHx4cyOWaFM8UrF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35564}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "e2a7ec809db20e638abbad250d105ddbde88a8d5", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "20.16.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.4.0_1723476048121_0.5257498534627234", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@eslint/core", "version": "0.5.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.5.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "590b2d16013a785504924e618d76e5671d6d5b0f", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.5.0.tgz", "fileCount": 5, "integrity": "sha512-d7dQItxTFa0mgeYhU+E1sN28mYYX4Z5FRw0DRszhD8OQr92Tbd00dtSVU/HWj9+Q0iKPQUMtPcExqI/QvkHUow==", "signatures": [{"sig": "MEUCIG+NOg1/az2cjlG1FJIAh1qpTemaJq4jCgr/KmXi6G6bAiEA0wF56AGs5SMZ0MJ2Agy9z6tMSQfczrzLC0fmIUYc4SY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35700}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "3c54b2b01476ff533811d7745e5a81b4aa72326e", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "20.16.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.5.0_1724767118725_0.2866167218812783", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "@eslint/core", "version": "0.6.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.6.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "9930b5ba24c406d67a1760e94cdbac616a6eb674", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.6.0.tgz", "fileCount": 5, "integrity": "sha512-8I2Q8ykA4J0x0o7cg67FPVnehcqWTBehu/lmY+bolPFHGjh49YzGBMXTvpqVgEbBdvNCSxj6iFgiIyHzf03lzg==", "signatures": [{"sig": "MEQCIEZtvnbYZbX2Gf9wjj4B0TOS35luCM26AgSHLo9qZ2uwAiBuudcjkxC01P3WVRUE7S+0F7lsUgO25t20K2zgna5LDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35970}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "8743a5b0f176f548d6f5abd9fdaee9269a06df8b", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "20.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.6.0_1725029152790_0.39927001461109835", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "@eslint/core", "version": "0.7.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.7.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "a1bb4b6a4e742a5ff1894b7ee76fbf884ec72bd3", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-xp5Jirz5DyPYlPiKat8jaq0EmYvDXKKpzTbxXMpT9eqlRJkRKIz9AGMdlvYjih+im+QlhWrpvVjl8IPC/lHlUw==", "signatures": [{"sig": "MEUCIQD+dP6iMgUyup99KA3y2WknUG8cQpF/B5WSK2TW5LyY8AIgJE6Eom19T4cBNioh39MsERmLx5vgeKa+KP/33r1YcEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36264}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "80eb5455aa5e021fc8c514a11d67c59c583ae2a1", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.7.0_1729282293636_0.10955624569859679", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "@eslint/core", "version": "0.8.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.8.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "54b6591e4799b2c0e363da5b607e3686a6d8d893", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.8.0.tgz", "fileCount": 5, "integrity": "sha512-ncQZoR8YJtXIrBuJo1vDlIIR8+uoyYj2tRXE/RbZ3KHWYXNLcPeOgNKRBzXSZ/yQbVObVS8JGbhzvpifU+eQqw==", "signatures": [{"sig": "MEYCIQDlC59zIaKYt1Kiy2tJ7Xql418T1QKkVSisyFhQ1WutNgIhAJNCjksQDXfjU85EGnvxAU7/sgXKVzoEPod9mfeMU2vs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.8.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59372}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "0dc78d335a98ef680b579851026438473147750e", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.8.0_1730136228084_0.3528833445594328", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "@eslint/core", "version": "0.9.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.9.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "168ee076f94b152c01ca416c3e5cf82290ab4fcd", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.9.0.tgz", "fileCount": 5, "integrity": "sha512-7ATR9F0e4W85D/0w7cU0SNj7qkAexMG+bAHEZOjo9akvGuhHE2m7umzWzfnpa0XAg5Kxc1BWmtPMV67jJ+9VUg==", "signatures": [{"sig": "MEYCIQD0NsHXt5Eo6gvb9x3g8J0RYqvvlDSLWWeAAR2jWz67fQIhAIKXJo92Jtf+jZSHeblI3NthIuo54Ib+6nvhygbKVYp+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.9.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60049}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "a957ee351c27ac1bf22966768cf8aac8c12ce0d2", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\""}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.10.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.9.0_1731602788076_0.7381323205432995", "host": "s3://npm-registry-packages"}}, "0.9.1": {"name": "@eslint/core", "version": "0.9.1", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.9.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "31763847308ef6b7084a4505573ac9402c51f9d1", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.9.1.tgz", "fileCount": 5, "integrity": "sha512-GuUdqkyyzQI5RMIWkHhvTWLCyLo1jNK3vzkSyaExH5kHPDHcuL2VOpHjmMY+y3+NC69qAKToBqldTBgYeLSr9Q==", "signatures": [{"sig": "MEYCIQCoCkTAI2oVvEHGdxcJjaDeQX85TAJlkmNxGPsT1qRhRgIhAK+jM67yk3I1E0RIPOJvpAU4Jp/DLk8pl9YRsiB+V8CL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.9.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60030}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "dd8d161c635450f3e37109f833737bf69f54db55", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.9.1_1733347387719_0.22048904246572487", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "@eslint/core", "version": "0.10.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.10.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "23727063c21b335f752dbb3a16450f6f9cbc9091", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.10.0.tgz", "fileCount": 5, "integrity": "sha512-gFHJ+xBOo4G3WRlR1e/3G8A6/KZAH6zcE/hkLRCZTi/B9avAG365QhFA8uOGzTMqgTghpn7/fSnscW++dpMSAw==", "signatures": [{"sig": "MEYCIQDPc0kxuRcyrGGVOlZA2l9SUuen2otCw1ZSg7WA2kLI2QIhALbvfqK1rXmTTw6twbqslQ+ySYNxgu4ATpP5FPmRH07q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59880}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "7f291c70eafb050420d86092f7cd03f9c6a96207", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.10.0_1736533642626_0.11580830723717628", "host": "s3://npm-registry-packages-npm-production"}}, "0.11.0": {"name": "@eslint/core", "version": "0.11.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.11.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "7a9226e850922e42cbd2ba71361eacbe74352a12", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.11.0.tgz", "fileCount": 5, "integrity": "sha512-DWUB2pksgNEb6Bz2fggIy1wh6fGgZP4Xyy/Mt0QZPiloKKXerbqq9D3SBQTlCRYOrcRPu4vuz+CGjwdfqxnoWA==", "signatures": [{"sig": "MEUCIDAdNJKG2kBAu9M8ZAlyxLE4dk9/ikrhaK96Z7Mb4gwsAiEAms9wP1JSAjoldUh/ecnYnlvyvwsr5i9O4OC6fNDgGkU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.11.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64553}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "e1cb6037bc237313dbf3f6a7b9f5cd3c3105b668", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.11.0_1738344147827_0.5342692799245619", "host": "s3://npm-registry-packages-npm-production"}}, "0.12.0": {"name": "@eslint/core", "version": "0.12.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.12.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "5f960c3d57728be9f6c65bd84aa6aa613078798e", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.12.0.tgz", "fileCount": 5, "integrity": "sha512-cmrR6pytBuSMTaBweKoGMwu3EiHiEC+DoyupPmlZ0HxBJBtIxwe+j/E4XPIKNx+Q74c8lXKPwYawBf5glsTkHg==", "signatures": [{"sig": "MEUCIQDyzmdp0k9wAf9AatQBAzq2fRCXYuMVcmfdy6733LVesgIgAbSxpH/2gI5WnkpSmKoaMCCaqXnAPIBf2OA3RePvnYk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.12.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65114}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "3e9b0eb2b87b46842f157421001cc58ba007be56", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.12.0_1740140395313_0.03820680254617903", "host": "s3://npm-registry-packages-npm-production"}}, "0.13.0": {"name": "@eslint/core", "version": "0.13.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.13.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "bf02f209846d3bf996f9e8009db62df2739b458c", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.13.0.tgz", "fileCount": 5, "integrity": "sha512-yfkgDw1KR66rkT5A8ci4irzDysN7FRpq3ttJolR88OqQikAWqwA8j5VZyas+vjyBNFIJ7MfybJ9plMILI2UrCw==", "signatures": [{"sig": "MEYCIQD+qZ7TjOGqfPamQ0ILOF+q7g5aibdFuUFBSU0c3iw3EQIhAKtdkscJnQu1/YM3+rU+GesSQVJ3b31PGvWcaQiMpGYM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.13.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64791}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "1615a01d9e5c637dfb4d19bb53968185462fadb3", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.13.0_1743532231592_0.846297969649791", "host": "s3://npm-registry-packages-npm-production"}}, "0.14.0": {"name": "@eslint/core", "version": "0.14.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.14.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "326289380968eaf7e96f364e1e4cf8f3adf2d003", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.14.0.tgz", "fileCount": 5, "integrity": "sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==", "signatures": [{"sig": "MEYCIQDlumktcqFGXzDgMpBRkXP0Dhc800rTxFg/V7td5Rhe1gIhAL/6GwvtJy5HRxcUu0p74RnTrDNAADvKvf2ahZp6ml26", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.14.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67087}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "0fa78612cb4d26e2de812e776ba749b882a64548", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.14.0_1746127255429_0.3613343844590302", "host": "s3://npm-registry-packages-npm-production"}}, "0.15.0": {"name": "@eslint/core", "version": "0.15.0", "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/core@0.15.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite/tree/main/packages/core#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "8fc04709a7b9a179d9f7d93068fc000cb8c5603d", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.15.0.tgz", "fileCount": 5, "integrity": "sha512-b7ePw78tEWWkpgZCDYkbqDOP8dmM6qe+AOC6iuJqlq1R/0ahMAeH3qynpnqKFGkMltrp44ohV4ubGyvLX28tzw==", "signatures": [{"sig": "MEQCIBwJsmxyNxbXiejN9rs1O/hiJsZKOlajwzWPcLm9MhPkAiBEvTqLKdgIBKki6iQuR9ea8UZu80cqwHr/z5kYdWKX1g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.15.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67902}, "type": "module", "types": "./dist/esm/types.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "gitHead": "48b1f849476582257e1b6a110c4af55adbbec2e8", "scripts": {"build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "test:types": "tsc -p tests/types/tsconfig.json"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git", "directory": "packages/core"}, "_npmVersion": "10.9.2", "description": "Runtime-agnostic core of ESLint", "directories": {}, "_nodeVersion": "22.16.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.8.3", "json-schema": "^0.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/core_0.15.0_1749674175235_0.41163838148205056", "host": "s3://npm-registry-packages-npm-production"}}, "0.15.1": {"name": "@eslint/core", "version": "0.15.1", "description": "Runtime-agnostic core of ESLint", "type": "module", "types": "./dist/esm/types.d.ts", "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "publishConfig": {"access": "public"}, "scripts": {"build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "test:types": "tsc -p tests/types/tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/core"}, "keywords": ["eslint", "core"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite/tree/main/packages/core#readme", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"json-schema": "^0.4.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "_id": "@eslint/core@0.15.1", "gitHead": "0496201974aad87fdcf3aa2a63ec74e91b54825e", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA==", "shasum": "d530d44209cbfe2f82ef86d6ba08760196dd3b60", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.15.1.tgz", "fileCount": 5, "unpackedSize": 67892, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.15.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC9dKVjB0UMEnEYXW6KjjQVCxMDUm+bXElAPTo3rQ2WRgIgNjo0jL+GphdKaHdPSz1F4Rh/3D5Xd34fBmYXWiDx9gY="}]}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/core_0.15.1_1750860260171_0.9826067115568171"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-06-26T16:13:30.730Z", "modified": "2025-06-25T14:04:20.779Z", "0.1.0": "2024-06-26T16:13:31.031Z", "0.2.0": "2024-07-11T20:27:01.688Z", "0.3.0": "2024-07-22T15:27:47.309Z", "0.4.0": "2024-08-12T15:20:48.399Z", "0.5.0": "2024-08-27T13:58:38.940Z", "0.6.0": "2024-08-30T14:45:53.003Z", "0.7.0": "2024-10-18T20:11:33.804Z", "0.8.0": "2024-10-28T17:23:48.313Z", "0.9.0": "2024-11-14T16:46:28.379Z", "0.9.1": "2024-12-04T21:23:07.893Z", "0.10.0": "2025-01-10T18:27:22.857Z", "0.11.0": "2025-01-31T17:22:28.062Z", "0.12.0": "2025-02-21T12:19:55.537Z", "0.13.0": "2025-04-01T18:30:31.774Z", "0.14.0": "2025-05-01T19:20:55.603Z", "0.15.0": "2025-06-11T20:36:15.471Z", "0.15.1": "2025-06-25T14:04:20.335Z"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/eslint/rewrite/tree/main/packages/core#readme", "keywords": ["eslint", "core"], "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/core"}, "description": "Runtime-agnostic core of ESLint", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# ESLint Core\n\n## Overview\n\nThis package is the future home of the rewritten, runtime-agnostic ESLint core.\n\nRight now, it exports the core types necessary to implement language plugins.\n\n## License\n\nApache 2.0\n\n<!-- NOTE: This section is autogenerated. Do not manually edit.-->\n<!--sponsorsstart-->\n\n## Sponsors\n\nThe following companies, organizations, and individuals support ESLint's ongoing maintenance and development. [Become a Sponsor](https://eslint.org/donate)\nto get your logo on our READMEs and [website](https://eslint.org/sponsors).\n\n<h3>Diamond Sponsors</h3>\n<p><a href=\"https://www.ag-grid.com/\"><img src=\"https://images.opencollective.com/ag-grid/bec0580/logo.png\" alt=\"AG Grid\" height=\"128\"></a></p><h3>Platinum Sponsors</h3>\n<p><a href=\"https://automattic.com\"><img src=\"https://images.opencollective.com/automattic/d0ef3e1/logo.png\" alt=\"Automattic\" height=\"128\"></a> <a href=\"https://www.airbnb.com/\"><img src=\"https://images.opencollective.com/airbnb/d327d66/logo.png\" alt=\"Airbnb\" height=\"128\"></a></p><h3>Gold Sponsors</h3>\n<p><a href=\"https://qlty.sh/\"><img src=\"https://images.opencollective.com/qltysh/33d157d/logo.png\" alt=\"Qlty Software\" height=\"96\"></a> <a href=\"https://trunk.io/\"><img src=\"https://images.opencollective.com/trunkio/fb92d60/avatar.png\" alt=\"trunk.io\" height=\"96\"></a> <a href=\"https://shopify.engineering/\"><img src=\"https://avatars.githubusercontent.com/u/8085\" alt=\"Shopify\" height=\"96\"></a></p><h3>Silver Sponsors</h3>\n<p><a href=\"https://vite.dev/\"><img src=\"https://images.opencollective.com/vite/e6d15e1/logo.png\" alt=\"Vite\" height=\"64\"></a> <a href=\"https://liftoff.io/\"><img src=\"https://images.opencollective.com/liftoff/5c4fa84/logo.png\" alt=\"Liftoff\" height=\"64\"></a> <a href=\"https://americanexpress.io\"><img src=\"https://avatars.githubusercontent.com/u/3853301\" alt=\"American Express\" height=\"64\"></a> <a href=\"https://stackblitz.com\"><img src=\"https://avatars.githubusercontent.com/u/28635252\" alt=\"StackBlitz\" height=\"64\"></a></p><h3>Bronze Sponsors</h3>\n<p><a href=\"https://sentry.io\"><img src=\"https://github.com/getsentry.png\" alt=\"Sentry\" height=\"32\"></a> <a href=\"https://syntax.fm\"><img src=\"https://github.com/syntaxfm.png\" alt=\"Syntax\" height=\"32\"></a> <a href=\"https://cybozu.co.jp/\"><img src=\"https://images.opencollective.com/cybozu/933e46d/logo.png\" alt=\"Cybozu\" height=\"32\"></a> <a href=\"https://www.crosswordsolver.org/anagram-solver/\"><img src=\"https://images.opencollective.com/anagram-solver/2666271/logo.png\" alt=\"Anagram Solver\" height=\"32\"></a> <a href=\"https://icons8.com/\"><img src=\"https://images.opencollective.com/icons8/7fa1641/logo.png\" alt=\"Icons8\" height=\"32\"></a> <a href=\"https://discord.com\"><img src=\"https://images.opencollective.com/discordapp/f9645d9/logo.png\" alt=\"Discord\" height=\"32\"></a> <a href=\"https://www.gitbook.com\"><img src=\"https://avatars.githubusercontent.com/u/7111340\" alt=\"GitBook\" height=\"32\"></a> <a href=\"https://nolebase.ayaka.io\"><img src=\"https://avatars.githubusercontent.com/u/11081491\" alt=\"Neko\" height=\"32\"></a> <a href=\"https://nx.dev\"><img src=\"https://avatars.githubusercontent.com/u/23692104\" alt=\"Nx\" height=\"32\"></a> <a href=\"https://opensource.mercedes-benz.com/\"><img src=\"https://avatars.githubusercontent.com/u/34240465\" alt=\"Mercedes-Benz Group\" height=\"32\"></a> <a href=\"https://herocoders.com\"><img src=\"https://avatars.githubusercontent.com/u/37549774\" alt=\"HeroCoders\" height=\"32\"></a> <a href=\"https://www.lambdatest.com\"><img src=\"https://avatars.githubusercontent.com/u/171592363\" alt=\"LambdaTest\" height=\"32\"></a></p>\n<h3>Technology Sponsors</h3>\nTechnology sponsors allow us to use their products and services for free as part of a contribution to the open source ecosystem and our work.\n<p><a href=\"https://netlify.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/netlify-icon.svg\" alt=\"Netlify\" height=\"32\"></a> <a href=\"https://algolia.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/algolia-icon.svg\" alt=\"Algolia\" height=\"32\"></a> <a href=\"https://1password.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/1password-icon.svg\" alt=\"1Password\" height=\"32\"></a></p>\n<!--sponsorsend-->\n", "readmeFilename": "README.md"}