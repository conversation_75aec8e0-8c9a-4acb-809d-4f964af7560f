{"_id": "mini-svg-data-uri", "_rev": "11-d3b7d50890a6da2167b7421d63aa2fe0", "name": "mini-svg-data-uri", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "dist-tags": {"latest": "1.4.4"}, "versions": {"1.0.0": {"name": "mini-svg-data-uri", "version": "1.0.0", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "441efa63cfba5a6220684ae3ffccef434b7d4443", "_id": "mini-svg-data-uri@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HOLwKSwgAXyjFD9ki+d9/HPgvXmZ3boaGuhMxWqVEvyi1RnV2qM3z4QDjKQuU/rhZZVHuBMobu/Y48G3ND7J4Q==", "shasum": "a9c09bbc7e1908b89534cf1d918c4c0766855769", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMBy0zldQwrGXrYruwwjZ12AX5j0das2KkAYBMKLzX/AiA9jiqHFtHT2w7hNL+Hj+zj1cu33PXt0I/yvFjIU+kLgA=="}]}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri-1.0.0.tgz_1514324006249_0.36819159775041044"}, "directories": {}}, "1.0.1": {"name": "mini-svg-data-uri", "version": "1.0.1", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "2304df6b1fbc7fb7caaea5828c2cad3291f1cf9e", "_id": "mini-svg-data-uri@1.0.1", "_npmVersion": "6.0.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KJ3cjR4kJIP4RroDIXqVTOX0hDYaFMmeHPXqwakVuJmak31uB4+DEqK2L7cedtYHUOdQgh23YsXnAIOHLvjM0g==", "shasum": "d81ffc14b85558860581cc58d9790daaecbe91bf", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.0.1.tgz", "fileCount": 5, "unpackedSize": 8287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDIw8CRA9TVsSAnZWagAA+RUP/1FROqxnmqt3/9LwmXhn\ny5C8AOlriFpUN7LDUWh4xxJNn2HcQvZOabW3PFhE6x1qtBnqP+pF+5TvOEBq\nMQx2K/fcT2c+KX+9p8iITeAT7HSfDMotMwaUd4SXNqVsgHeVrkXCibXpz+uZ\ngfusrRLN2sBOyuAgwmDoGJ/YZWHZngGaJ5cd+U7nOhb0U0Ifr6X7iAsaSCQO\n9t0qtGLkQMSV36NixlY70gY6L52UbS36cuedcY5+sT4bC/P2Fr6xRcaTUVoo\nilrwyhhBA1Ovmt/ite5B2NpeTUVKckuFaos66hMePjjhDZ/XLmCb2hee9wyA\nwceic4odK6IV0YSHgtLnj15XdIpBiZiO/yBaGOVewfUNDW8mX/agKukR425t\nuAcvABT+9HwIR7ZeCQxeZylhxQS6MysVmdUHjqUUFbdf6XL/IcDB+YJ7N2bq\nSAw4jhCdWPW4wOaxw+BqPuNeEd1Qa8A3NtcivhBf+VJ+Uvepti6152gZMKET\nlRrSJexdXn2pTXb0G0AD0ImS2iOoo9bnRT725KMdnOo2JDRVaX46CUNua6+P\ncfwMN6WtcLaMwlemmC1BSCnRjcAOp6B6SC6hbt1GStdE4HArmKbrVHi1Cqhj\nmfgkcza/Ag9hbY/d4WEwBWy1MMeZp/cSJwyruaEgrp6LmQYm07LKXBJTC29k\ngiLQ\r\n=oVZu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVwvvDoMQgg9+OKAGA9TkTHtUo54NzoHnbQUYBQQlTdAiBND1fIgECsz82SHqzc9vhJg/KdMMUomi4zLw9Fo0CFPw=="}]}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.0.1_1527548986556_0.01896767318890613"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "mini-svg-data-uri", "version": "1.0.2", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "19f716b29b38c3711dab543cf646d77d731865cd", "_id": "mini-svg-data-uri@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.9.0", "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3bDQR0/DIws7pkqi/dhtmv5BGgTT2HPRzq9fos3Jz4Xc9bVnn5eC6jBb4mK25Jdt8UclKeRhateLLTz9J2Wwug==", "shasum": "c4efdb71499249e02160a53db99d2eacf4b5a5c7", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.0.2.tgz", "fileCount": 5, "unpackedSize": 8287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/D7jCRA9TVsSAnZWagAA9oMP/jaS99j0XT6s6PBlFMxh\ndnF8RvZFAarKtkfI0Wyq7rkPOBSu26bIKnb5PetGXz79NUR0eKpbMV/msbCk\nLECUREf3QJcd7BOhRDWSibK2t+EhXQdtU5ziUSaFrznYIRG2YgUulH50qMnm\nB2QzaQfOs6KG6eFvSUH6wkJIg6T9WhfdP+p0+klyKPCkmpf0xhJboWwMaVFy\n7UnK5sj4sw+R9SV3VyVRMn2kU22WrPpz1WcTRQ5DxeOnIBcx8venm1aSYqmi\nRrojfvaar6cZ6RSk6cco6+LFGVexrv+4s28JAitIBpaXavpzrp4LZqh5dfK+\nmZFhw2o9rlflW7/vhRDlot+G640uJlUolG7jxh3sNgjFTeM1At7gJGlgStNP\nECru2/3kJOGrIGG1khDru6vrZAwwmmU8B9PdEcxnYNhggu9RLqQK6x3ST6Hr\nZwIZ94uZVg1fLFkB49x52ZaZvQohfKjqJY2krOylSvQqWmyFp7WPC9A+nAh6\nMZiAR4OmqOWhVI6/etRLJVHoxY2XGNLC7JHWYxexcyNE5j8machi1fLtEa9p\nGKFjoWya7OJOa1NlsuA2/wnvNl7bxaNsfmO+vYwTVXm1G6uW5OO0j1ZhiDuN\nm9ZbtUXySGGalfCphUgE/MCiQtQsfAvRghhyydVJWItRDWEwAMpyTHKfyPS1\n1/4g\r\n=wjHB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWvRnQD0PPC9KsLUAxz6piWu/k4EGnEWsj5UmKhH4GkwIhANf8H07a1uCcavZUbY2a4fUeQVt1NdTVk5Qjo9QEYPjm"}]}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.0.2_1543257826691_0.10790881460614532"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "mini-svg-data-uri", "version": "1.0.3", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "5cc4ca36bf77c36be1702fd3d6bcbc512c44c877", "_id": "mini-svg-data-uri@1.0.3", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YBaqsh6GE+4jQhLNkFYEagH2o4bVTlGGpEvkuwtwc+1NBGXqpcVCnsUGkGp75ovPXxtF2GsDYzUwyhfC0hntiA==", "shasum": "f8bb0d7afbf3de29152aadd0f47701197ceab4a3", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.0.3.tgz", "fileCount": 5, "unpackedSize": 8636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcex7PCRA9TVsSAnZWagAAPYgP/j4YPQ98VtjzB6W5NDbN\nGbdZ9b7ZPnRa/rECo9pfhyh3MEeDZYqAGurzzQiXSWMSojMGPuZpMeSadBgI\nV9OaX3u2sdV02simg3fZntEKDYGIlel59CEAW9UqUlbZXadbG1cuqL9uQs1m\nhY1x5SNeA4l5UL92tbdb8UEhifTRcuO1Q6YnWu3bhvQ5WFwI+IuL2jT6ZaM0\nyYxd6zNjccIc4OIB87CqzO+Ip2pMYaIf6iECtZLj5K3zviDXNyp06T+yrtrq\nxwh6fVq7dtSLeVdoBslL+k8yazY29pGGMPy/KesImuLtCWDyEynDCFyT3yRO\nf+DnOwRROyEbMu2Ftx/GsDuKv+jFGQtIJGVmWiRI7WBX+1BeX5KifLcYqQAu\nL+dQ8QIxcSjjWpmi1mxseR84IY0O8R8N/agXLVXlPfoCatpQejiyFvUR8fz4\nYsms40anfW6zEvcwpYKxso41LYJw5W2H1/i/DsEFJ1qgnkgkn+kqMdeT9c5k\necjSx4iLlgl+XxQiG+sLeB9fz+nuNnM2tLZvn71eMohkvy7vh4QwavYNWRaS\nlWBiJ7zAGXxl/7qfh6xcPoGVOW82eV1zYoN3vfosArVJ4bof5uooJ6mfflI8\nnb1mBbVN3FyvYyg4r9R9h521eBf2wPCAOTKuZNRBBZZRekBU7ND1hWdrodi0\nqLOo\r\n=tQih\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDClWtw2IDKAO2iBTjbbRXCc1T5+igdBf9Xdi24rEaLoQIhAMOf14ZxsVlDRuCTe7M2rpgR2aTSohcxJYbZPMMwj4ym"}]}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.0.3_1551572686693_0.5797070716767267"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "mini-svg-data-uri", "version": "1.1.3", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "5e043dfc74cc7b93e4a805bae74350b5b2a61623", "_id": "mini-svg-data-uri@1.1.3", "_nodeVersion": "11.9.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-EeKOmdzekjdPe53/GdxmUpNgDQFkNeSte6XkJmOBt4BfWL6FQ9G9RtLNh+JMjFS3LhdpSICMIkZdznjiecASHQ==", "shasum": "9759ee5f4d89a4b724d089ce52eab4b623bfbc88", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.1.3.tgz", "fileCount": 5, "unpackedSize": 9022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdG+fqCRA9TVsSAnZWagAAoKoP/3Pb01E3/5VbDIaBr78d\n8DE8RJUBIOMTbzLd8T282WKi00H+OM1IWbFOLIIVwQBDaOJ8YWB+oAOb+jGq\nb2G+JiGYEVdQEc9osSI87R44R1w4y6+dHDANu6VO2afXO1zpQEyribwJ89tA\ni+TUU0clHDi8wd8G7k2OMqzWneb2z5UuzvTxsuk+b2Cf/Cyn7B7alS9ZUlHJ\nAx6Txl9a2TfOPH5ukeznN2gPBWuQamnkr3iKZEIVVcudOzhMprhbbIrODAX5\nKDVz3LR2GheJd/XEHdOrG0T203pEahUSpAl8v2DH0/oFR8blKQB1/pbInBw8\nKxZOx/+oI3hAQVeDqBQwwHoTA+ManHlh7doattTTImDlVT++itn2Jnkm4BEF\nYJYPwpfaJWbzMTBsrupAO9bkNmyA9ZoaFmLctaCfPnQgrdwx9BeDNqrfmE8Q\nOukCJS33PYvXedDDaJtledFQJ3k/Kz9mwGXVbdRBol3EiuhBgZ4KbPCRly7/\ng1g4M7BF9/LbJTkX9n/fGK/bEodrj7/+Kdwrp0N0LWWhG8akAeeipmpHBjRM\ngxUhfDUNkiTKpzAs3PJWvw2KNRRm1Ci082aMjP4TjKHyo0cxchmaEWgl9V7q\nXJjWz5JZvUy3Mba0SmtiufS1W2X1K4boA9es795bPVpliifKhwiwVu8hB4Ar\n8B+y\r\n=ZFSn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFTn1E6/gJAlLIOM9aejwRs4ZnOea9ykMT3tEeanOENqAiB+LNnnBNC1yP/PkfM0/n9DnG/CtBPoaHejqe3h/EACNQ=="}]}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.1.3_1562109929948_0.19904511672420733"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "mini-svg-data-uri", "version": "1.2.3", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "e3f87d2a79f55ad1b1d0509a8bce0c416da33be3", "_id": "mini-svg-data-uri@1.2.3", "_nodeVersion": "14.0.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-zd6KCAyXgmq6FV1mR10oKXYtvmA9vRoB6xPSTUJTbFApCtkefDnYueVR1gkof3KcdLZo1Y8mjF2DFmQMIxsHNQ==", "shasum": "e16baa92ad55ddaa1c2c135759129f41910bc39f", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.2.3.tgz", "fileCount": 7, "unpackedSize": 9317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeshxJCRA9TVsSAnZWagAAGicP+gMRBogQDsR5NOq72YM9\n9rsGSFcTgdQfunB7OGMVX3Ju6M2g1MUyPBfgKjxWJihXv+NMZYwey202VQYf\nLU4Zh6eIU5/Af2HnhEsDmTYk/y68yQUOhY2hQqWM+pF+OWl2u+zdeDyWBZEg\nsTNTreaNN+UlxZEnWr7TyUD2Wa876EhZBFP4dg1oS4/yNanL17ITx0X0MLAD\nf5XYKSRJNyqoi0awG3vinzIj5PZYwxFewpCYz/iqmu+W6nZOK1dEMb9Jnrw3\nUl3nASKHGU9qGsgd+6XhfPptApPxIeFuaGlzNZltrP7OmShJcyNP11KlhO4U\nkcS7GJCYq/2hfanN5TF3ipI0r82cX3XgSA8nOFwllN1RZQwwDX/7CEBk9Cpf\nspGDF8GwuB+4z3ouWQYgVjka/AHOXqRAQJMyBNrljYLNiVwvsh0oODyssTy8\nWvOEthIaQKfcTFzMTC2MqLajQplIZV8llTIphReWayEhJ1GUhELQ64JSqOcx\nBSnoH6sGUdlpODjyKf4aDcXXmk2qIoyC3q+Ze5Z5qS6lrX/tZuC5sya/OpA4\n0zVmgGCQx6DYNULhEm1n+Qe7o8CeWqu8WPYCTwAG6tfAijIFZBtqZ9f4Dvth\nDNY7D4BlSxSszJE34hi2RK5v/oZb9sBzAsjf6zd2dn+Dp/ZHmHx6+A0lb3cZ\nlBap\r\n=dnqZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNqeghi9QOctUnHrsoGvGqs8S4Js8s0XH1XWR1ZkF5lQIhAN6cDOTEv/vPs2nBrZ3QjU1VH5Dk7QKPU9sOLRK0CkFd"}]}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.2.3_1588730953109_0.3494308602366212"}, "_hasShrinkwrap": false}, "1.3.3": {"name": "mini-svg-data-uri", "version": "1.3.3", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "types": "index.d.ts", "bin": {"mini-svg-data-uri": "cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "e7f8cbc14aa70454caa965b3135d223b28eed600", "_id": "mini-svg-data-uri@1.3.3", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-+fA2oRcR1dJI/7ITmeQJDrYWks0wodlOz0pAEhKYJ2IVc1z0AnwJUsKY2fzFmPAM3Jo9J0rBx8JAA9QQSJ5PuA==", "shasum": "91d2c09f45e056e5e1043340b8b37ba7b50f4fac", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.3.3.tgz", "fileCount": 8, "unpackedSize": 10460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmZw4CRA9TVsSAnZWagAA50UQAJYP2zGPM3YUdbzk8Sau\nmu4vqdlAqsBPh1F0sw0bOwB2VtDTTNZzObh5Ajmby4294t8/aRuoNgLko6Bi\n5OuuY9Yd93M5zDm/aBUCm3A1p6sQJ1+ThO3APh3PMTEvylnr3pWAGDKH2Tvf\nNGhJvcvm2aZ7qSnmdK7c44n9hEKCCVvm0daL2GG4u22c7eiHPDga02lPpblX\nLSqK+MCR0LBYQunr4a6EEmn6V3p5ApCfHV8JVKbyXy8UoEPAxnn9teNj5SIa\npL8uG5LssG5AMQ2xvgZBd+0ICCeK4QpSdZ+dFmghz3+PaUa5ZDWCfPlJshxd\nmKwSFTppeiY/uOGK3Nrn6gV+4jH5cf8eAyvJpYr05+tMAEWRZYaaMWmGxQkq\n9MLjpDJ4f8nrrMCfk6U2YpCouNqb0lulW8ka6yn6eWYnc65Bd4lsnVHYNlsi\nY4tlb31GQ+06OnXTldAJfB5G3RBFaecVCpdSQoEHdFRV5RvzVdtNKFuWGZyy\nNh3XNklaZgrmpzawcAVlucemOPAvKk/VkVJmcYRSLckRUV4hh8dHNgM/dQB5\nw83os9rgr8HeNbB7oSXdDEgGdiB/rZ/Gh/njhCid/Vj6pTBlmdmJcr2DDKWV\nkK8s7RsnqvCtX7wXD016vpK7j0jS/SGP4vXL0ZIgpOm+aW5+4fw/f2E51Hrq\nUWtW\r\n=UF4l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNT8KJgy1T4UuoqKgzyrLNBeiTCP1Tfv2/9Jq17oZN9AiEArxf37YwTOlKbvV6fJFbt2Q4bLKgdwfbGdJ6ab70/CFw="}]}, "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.3.3_1620679735895_0.8989435233015821"}, "_hasShrinkwrap": false}, "1.4.3": {"name": "mini-svg-data-uri", "version": "1.4.3", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "types": "index.d.ts", "bin": {"mini-svg-data-uri": "cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "41b5362ae481444ddf3d352a7f78c5600ec0201a", "_id": "mini-svg-data-uri@1.4.3", "_nodeVersion": "14.8.0", "_npmVersion": "7.12.0", "dist": {"integrity": "sha512-gSfqpMRC8IxghvMcxzzmMnWpXAChSA+vy4cia33RgerMS8Fex95akUyQZPbxJJmeBGiGmK7n/1OpUX8ksRjIdA==", "shasum": "43177b2e93766ba338931a3e2a84a3dfd3a222b8", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.4.3.tgz", "fileCount": 8, "unpackedSize": 10466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2zSiCRA9TVsSAnZWagAAkL8P/1pewx6tlbJmp2mCYbtt\nvNqFzi9REAzhlwlnPX1NorOVevcTazAbkWI7cn19nIn5WRb7s6lu89JVK6oy\n<PERSON>viIsWtMW/qV+hlwq4M8o9QOXkAOACX9OjqQ3yRCSqGiBLoroDAV\nEcGB3kNpauvpNkT4+OIecOqyhzGNb31za4SPqVXobW+2gPMWo73W4p/erjy1\nrB47igqfjn3CpuS+t78t0ahUwPs8GWPNftCiXyh7Vdz+ZgW16cnWpiBS8eJA\ntdOnP3uvXosH0SqVLw+Y/5+t6gMgrY5A7sU1dat1a+9akSadtzgi5wgyTFje\nkw2POSVYk+mN4diweQcNel6xRvLNTPBOfJVw7q101tfCd1SJgGKxNLXCKTlh\nW+NLepZzEELKpIloSLXFDlPBqrlEynPNVYW1epT0U3gLbddDoMF8WLMMivre\nExodBfysfT3UA4eweCC5kdXu13QqsfyG3hLflUOHPGO5h6QyxjSASie2XgkM\n+AehjFvR1OdP5pClGVBM9anB7dwZ3dPewmTd0oQXHSqygVUoVO1SRU8qcwmx\ndeFxVEu6J9gQWRUlaBmjJNkw5M0Df7X/86vWl/67GNY28h2J579Ve0wkxudE\n+yASetG7z4ZQUvY75F6sFKP68qR08bGzVmHw084TmqMwAJYycOYdNNrtc/wW\nV+pM\r\n=5eS/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDE9scun6W531DdNKBfkVrttbhYbSltMuwioqsGyAt9jAiEA8LmywSxyG/UbmLXeKtHPvFZiO8sQHUTR7nwBAjitdwU="}]}, "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.4.3_1634252374984_0.8539236614850096"}, "_hasShrinkwrap": false}, "1.4.4": {"name": "mini-svg-data-uri", "version": "1.4.4", "description": "Small, efficient encoding of SVG data URIs for CSS, HTML, etc.", "main": "index.js", "types": "index.d.ts", "bin": {"mini-svg-data-uri": "cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "license": "MIT", "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "gitHead": "00dc78c8f77eb7b47299a9e3d564749105810c9c", "_id": "mini-svg-data-uri@1.4.4", "_nodeVersion": "14.8.0", "_npmVersion": "7.12.0", "dist": {"integrity": "sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg==", "shasum": "8ab0aabcdf8c29ad5693ca595af19dd2ead09939", "tarball": "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.4.4.tgz", "fileCount": 8, "unpackedSize": 10460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKSR3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooSA/9GGkzX6n+W4tHZfWD41k4vBJtYyQ+2QrPa3Ao5zJIwjUBrHK6\r\njxFyeP5QZGZunLlALxy0O5xftTGTY5I5EPiGD/GPTHv+yjkZYLdx1cDshGSc\r\nyhPFhDFF6tcC9PjPkMA5cUCNU+hacKckgOJHgz4CpNGH4MOXFfg2DsPoNvWb\r\nC523oK3bkL/WEMQAmUoUMIDBubkoCKxjXfpShCrx/J05sEN4oiS1k64WNH85\r\n2Ng1f6kwZmASyNO6hbBr6CtnUmerxGZGRelGoH3cfWmTK5SUEzsSvJqRe0Sl\r\nH/1e3ADaSoWcjvHFScCnoITWZN4apOoZvRdxhWjAHKD67UNz7NdCLyLS/Gws\r\niEYoWkEETwncvJY8bVjSjkbBDhGF4FAEWlxllk1+8xAi5iinMB4fKZ2TcPdV\r\nqMmZVuwoAUawzm/aESEmXp58DOaICvdPErR9+EBKFruEEql+QmFqGNT5b5Zh\r\nivPsWnt22Bd50k/M3zkv2C5y21yhuSlP2O603pvTTOylyaXvk/uYJw4O3qvm\r\nTCH82mrf3FkfoxbhVsI7hkqJDaM8NauQFX5YcuvC+n8NWFiQXg3nFnl5ZDrd\r\n9Wgd26Hj684d0scBqkzlaAMFHUCy7srSf/ZAIzXU4xJ27ZV6NEMKfrggZHj/\r\n2Z9tMguUpRkVg9Dh0t1gFE9ZnVR7m1Nob4Q=\r\n=FrEn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkSsIlm51eU+LiWKcKGbg+xdym5lzoXnbNIskGbIWoVwIhALU8dP5ZFPw2Dvuy39kP2RX3N9aXnmeyyjKr4u5jSTlA"}]}, "_npmUser": {"name": "tigt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mini-svg-data-uri_1.4.4_1646863479090_0.5585998240841532"}, "_hasShrinkwrap": false}}, "readme": "Mini SVG `data:` URI\n====================\n\nThis tool converts SVGs into the most compact, compressible `data:` URI that SVG-supporting browsers tolerate. The results look like this (169 bytes):\n\n```url\ndata:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50'\n%3e%3cpath d='M22 38V51L32 32l19-19v12C44 26 43 10 38 0 52 15 49 39 22 38z'/%3e\n%3c/svg%3e\n```\n\nCompare to the Base64 version (210 bytes):\n\n```url\ndata:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIH\nZpZXdCb3g9IjAgMCA1MCA1MCI+PHBhdGggZD0iTTIyIDM4VjUxTDMyIDMybDE5LTE5djEyQzQ0IDI2ID\nQzIDEwIDM4IDAgNTIgMTUgNDkgMzkgMjIgMzh6Ii8+PC9zdmc+\n```\n\nOr the URL-encoded version other tools produce (256 bytes):\n\n```url\ndata:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%\n2F2000%2Fsvg%22%20viewBox%3D%220%200%2050%2050%22%3E%3Cpath%20d%3D%22M22%2038V51\nL32%2032l19-19v12C44%2026%2043%2010%2038%200%2052%2015%2049%2039%2022%2038z%22%2\nF%3E%3C%2Fsvg%3E\n```\n\nFor a more realistic example, I inlined the icons from the [Open Iconic](https://useiconic.com/open) project into CSS files with the 3 above methods:\n\n| Compression | Base64    | Basic %-encoding | `mini-svg-data-uri` |\n|-------------|----------:|-----------------:|--------------------:|\n| None        | 96.459 kB | 103.268 kB       | 76.583 kB           |\n| `gzip -9`   | 17.902 kB | 13.780 kB        | 12.974 kB           |\n| `brotli -Z` | 15.797 kB | 11.693 kB        | 10.976 kB           |\n\nRoughly 6% smaller compressed, but don't write off the ≈20% uncompressed savings either. [Some browser caches decompress before store](https://blogs.msdn.microsoft.com/ieinternals/2014/10/21/compressing-the-web/), and parsing time/memory usage scale linearly with uncompressed filesize.\n\n\nUsage\n-----\n\n```js\nvar svgToMiniDataURI = require('mini-svg-data-uri');\n\nvar svg = '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 50 50\"><path d=\"M22 38V51L32 32l19-19v12C44 26 43 10 38 0 52 15 49 39 22 38z\"/></svg>';\n\nvar optimizedSVGDataURI = svgToMiniDataURI(svg);\n// \"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50'%3e%3cpath d='M22 38V51L32 32l19-19v12C44 26 43 10 38 0 52 15 49 39 22 38z'/%3e%3c/svg%3e\"\n```\n\nYou can also [try it in your browser at RunKit](https://npm.runkit.com/mini-svg-data-uri).\n\n### CLI\n\nIf you have it installed globally, or as some kind of dependency inside your project’s directory:\n\n```sh\nmini-svg-data-uri file.svg # writes to stdout\nmini-svg-data-uri file.svg file.svg.uri # writes to the given output filename\n```\n\nUse `--help` for more info.\n\n### Warning\n\n* This **does not optimize the SVG source file**. You’ll want [svgo](https://github.com/svg/svgo) or its brother [SVGOMG](https://jakearchibald.github.io/svgomg/) for that.\n\n* The default output **does not work inside `srcset` attributes**. Use the `.toSrcset` method for that:\n\n  ```js\n  var srcsetExample = html`\n  <picture>\n    <source srcset=\"${svgToMiniDataURI.toSrcset(svg)}\">\n    <img src=\"${svgToMiniDataURI(svg)}\">\n  </picture>`;\n  ```\n\n* The resulting Data URI should be wrapped with double quotes: `url(\"…\")`, `<img src=\"…\">`, etc.\n\n* This might change or break SVGs that use `\"` in character data, like inside `<text>` or `aria-label` or something. Try curly quotes (`“”`)  or `&quot;` instead.\n\n\nFAQ\n---\n\n### Don’t you need a `charset` in the MIME Type?\n\n`charset` does nothing for Data URIs. The URI can only be the encoding of its parent file — it’s included in it!\n\n### Why lowercase the URL-encoded hex pairs?\n\nIt compresses slightly better. No, really. Using the same files from earlier:\n\n| Compression | Uppercase (`%AF`) | Lowercase (`%af`) |\n|-------------|------------------:|------------------:|\n| `gzip -9`   | 12.978 kB         | 12.974 kB         |\n| `brotli -Z` | 10.988 kB         | 10.976 kB         |\n\nI did say *slightly*.\n\n\nBrowser support\n---------------\n\n* Internet Explorer 9 and up, including Edge\n* Firefox, Safari, Chrome, whatever else uses their engines\n* Android WebKit 3+\n* Opera Mini’s server-side Presto\n", "maintainers": [{"name": "tigt", "email": "<EMAIL>"}], "time": {"modified": "2022-05-09T09:51:38.116Z", "created": "2017-12-26T21:33:27.560Z", "1.0.0": "2017-12-26T21:33:27.560Z", "1.0.1": "2018-05-28T23:09:46.654Z", "1.0.2": "2018-11-26T18:43:46.773Z", "1.0.3": "2019-03-03T00:24:46.841Z", "1.1.3": "2019-07-02T23:25:30.055Z", "1.2.3": "2020-05-06T02:09:13.216Z", "1.3.3": "2021-05-10T20:48:56.041Z", "1.4.3": "2021-10-14T22:59:35.113Z", "1.4.4": "2022-03-09T22:04:39.258Z"}, "homepage": "https://github.com/tigt/mini-svg-data-uri#readme", "keywords": ["svg", "url", "data", "uri", "minification", "url encoding"], "repository": {"type": "git", "url": "git+https://github.com/tigt/mini-svg-data-uri.git"}, "author": {"name": "<PERSON> “Tigt” <PERSON>", "email": "<EMAIL>", "url": "https://ti.gt/"}, "bugs": {"url": "https://github.com/tigt/mini-svg-data-uri/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"zachleat": true}}