{"_id": "@eslint-community/regexpp", "_rev": "26-1d625105b2839b6e43e84af85a4e85e3", "name": "@eslint-community/regexpp", "dist-tags": {"latest": "4.12.1"}, "versions": {"3.2.0": {"name": "@eslint-community/regexpp", "version": "3.2.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@3.2.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "336b58d54a31a9a0bd1ae7bcb6be9ccce750ae6c", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-3.2.0.tgz", "fileCount": 3, "integrity": "sha512-4OClshm5ndSVZ6aLgTcqf48+vk/hZ332H2+ujSKyNVwl1tgUilIoRJZ2y43x8ioePbk+MYlNfFdmlZQc3xzZXA==", "signatures": [{"sig": "MEUCIQCwRUxj+BhOPSJpsPZePHhUKiv9GnyCV39bWmZNik7WrgIgJq4kQJv8keWVYfpe6FarssXqIl8n3VAg5tqhiEZy9p0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhBSrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnVBAAmtrVrMktlDzw9KeS1memP7nhNntotWsEyjYTZ4QrWHC9pFV/\r\nnPtpDi5s/2p8D/U8YXo1RmbIL+fY6R1+DSFiGXwKGuqklGiSAL8pIE5Aj1oq\r\naZH23rwdk+BfxLgp7sGsFrLEIjHoeDEfotLxVSGP7GuuybQzxitU5oWHJXoC\r\nV0vNfMdDHx4pfv3a9thGlYaaDiupQqgNjmv0HjmjV6kTzjMf/khSPOZnqkD9\r\nRaIjg9k/iQKds6EJhcNmEWModldz/GkiVXNoGXBl2iIlfI/GFLYK0ZYeKiYN\r\nSWqki2CtDTUqm16i9yQM4IUDCESWLd+oBDvwdqXIvP8Akzh9FvVc1PmgeHXl\r\naP2tGYgzveseQMUb1JlP4EGeU/K0DI8sfbsKZXTXU+hWy5KymhVCCdqpLnhl\r\nVXJ7WZRcat/LmKqe5U6qjCe6AXYdn/2gCKFU496DhcWWJm2XO/bo1U5Ay+Ac\r\nBYF/ikp1nZM5bn9ZtEo0o1PBVxDlw85mm8YrCeMGXoGhDpx9vI74TLKIo7Od\r\nZEpExi3/BjS70AyAAujayvka1SXOGbSrt94hHou9bly3NbYdw7MckiDa1S5I\r\n5Tbx1rgL8UZq+7fnXLCnOBvzs6krqYsY3LB4Yea2D3Qvhoil0+3QbDzcEYL9\r\nMsVZfzhVwrVEiaDDAR3etm5okZmrIukyaD8=\r\n=vV8l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=8"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "58cd5f959fecf58f0a1f6ec1c32d7c9435b08e8c", "scripts": {"lint": "eslint scripts src test --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "codecov": "nyc report -r lcovonly && codecov -t ${CODECOV_TOKEN} --disable=gcov", "pretest": "run-s build lint", "version": "npm run -s build", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^15.1.1", "mocha": "^6.2.0", "eslint": "^6.1.0", "rimraf": "^2.6.2", "rollup": "^1.17.0", "codecov": "^3.5.0", "ts-node": "^8.3.0", "dts-bundle": "^0.7.3", "typescript": "^3.5.3", "@types/node": "^12.6.8", "npm-run-all": "^4.1.5", "@types/jsdom": "^12.2.4", "@types/mocha": "^5.2.2", "@types/eslint": "^4.16.2", "rollup-plugin-sourcemaps": "^0.4.2", "@mysticatea/eslint-plugin": "^11.0.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_3.2.0_1669600427616_0.6419851410159789", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "@eslint-community/regexpp", "version": "3.2.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@3.2.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "9e3f8b59e21edbc312672733d5f80a0d5f66914d", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-3.2.1.tgz", "fileCount": 8, "integrity": "sha512-UcQeFeVMZ0rtWyYqurEbZ+rL/hBuhvAajwnSZu25buOKco85nDPh+y3SSg4NFA41j7CcxVZ3DsU7c71W7M+Wag==", "signatures": [{"sig": "MEQCIAM+X/K4EIxI9ZbOxAt8qdiBombN12oHbKx+OHMLgY5dAiAcE3Tayo66stJBSf8nI/cixQWlznUIpQx/YUalGBFXPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhBaBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol4g//cbOdHYloPD3yYD3FK72stEE3YmLjn8WAIldy1nLUeoaVxCr5\r\nVmLzWDFUwErYNWxlNG5IiQ9RyITtHJGknXhgeOrTmkznwNQczRPUaJ9H+gEi\r\norsQKBtxt8EuvvUcU/LprL67FblkA2CWUzx/mZkqSxOMW/STCsV4CvLZVyL8\r\n0q1ASbYQdHFWGR+WAU9WoUu0lkTsfE/KsVClsiBOVdKGC0tyTcT53/nqxwyL\r\nHpqQMluunhJsq/GoaeMvRoLfaQc3WCM+M3mc5shnE6T+1vlMbgtPzkfoMT88\r\n3EFjnzQ8lz8DjBcLlWPhU6dOi5GKXTVKeDzAhcdphOUEhLjp4E5T1pY+0XQr\r\nViWJUOMctJcMurlJdv7fA9yYqiwO995TUh0nu9ovK1tmn8zEEMwFOSreXuCj\r\nUaxX9pKoJUyYv/f1lB3kF9qrx1yWeUGoGNvg0ZtuC6Pqi3B7C+HSLoUYxC1o\r\n6wTwL+Zk0NmSxlqDEtkB7Kset7aBLnKbD5OU/X/y9KQdb3wPFndfWmnPgiXD\r\nmBZj7xKsu877BQr+Db7Te8KXEcZn3vLS1hFYzgYrKA8KM/YOk3ZxQIk7U4wB\r\nqJhXNmqNwIFACgMS12Wx96qoWr5NHoPAfMpVBegPev5Bt+ZGJGbLljPEbo4q\r\nPF11fb2E9j4hwnNrxBOMa+YpnIjEomVZ1dc=\r\n=pZx8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": ">=8"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "da06daa13284d3f0b07224845b1834a15a0db2d8", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "pretest": "npm run build build", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^15.1.1", "mocha": "^6.2.0", "eslint": "^6.1.0", "rimraf": "^2.6.2", "rollup": "^1.17.0", "ts-node": "^8.3.0", "dts-bundle": "^0.7.3", "typescript": "^3.5.3", "@types/node": "^12.6.8", "npm-run-all": "^4.1.5", "@types/jsdom": "^12.2.4", "@types/mocha": "^5.2.2", "@types/eslint": "^4.16.2", "rollup-plugin-sourcemaps": "^0.4.2", "@mysticatea/eslint-plugin": "^11.0.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_3.2.1_1669600897184_0.5996525590844781", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@eslint-community/regexpp", "version": "4.0.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.0.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "7f18388e3c461b5f40cd095b510de202b6261465", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-tGYfDqYd8xqgQziZ8zL2BIIPCUh88bteX7v92UW9o8oZQu3f8w+JRfMmOARzypyQ7c/zlqOFf5l9SCLOj8qWhg==", "signatures": [{"sig": "MEUCIGMv6FBhX+mxC7X9QOmC2zhO9e13neGuPyAujQkVy069AiEA7NTx5OjCdZn0BVS19J2qW2i8ZXrDsovSNMiuJCeN9Lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 300217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrzRVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAKg//Rg5+ROH4nHtsgRtl+HP8/5+/lWUZSpYi7IrOMVLpEyH1fPZW\r\nVTrQlbdclfEGWeHWLmIapBKKkY7ddE3kG7JeBhhz17B3OB1QhYaFjJqwsPGr\r\nAda522dV+tzFsnaW7r1BYoNnM/nN5ArHj5klmg2zRUVvfjUmM8g3ONkKT+3J\r\nyPhh0ZoV9spLcGN7eA9rkIFDJzQH2Iy2kobudTJNmQ5q5K8rj/AANcExGz6m\r\nkltPqWRZVe2220IHBtkU6wpyFGgFwGLoKf3VZQaxYxGt6N+MgK82abXpRlYT\r\nov60JAa9Xw+I5eH6psjNOannjPSpljr35S+p7gXrmD9WHYZY7/0zGJtJSXUC\r\nQ6I0D30k5hgJKchMOAr1k96WGcmpyRoD9QD8/1eTaLC6RStqT7b/UBv/CRX7\r\nkj6EkPFc5KB8NKn3HFX5piNKJfbkBifmG6eBwX3pEGHDNtD/+H6ny+/Ssne5\r\npVX9N+EOwiS3cj0GWpOEt2ruACakp2WPqzNOaQxWPqoNr+OFEzxHf4uJ7lkg\r\nlSfX67OWqI0Y1foyQB9ddwZUHomQSs7UwNjYyTaXtoomQyJkEIJCW2Ps9w2p\r\nfC9S7XdH+hexFPGG7cazo9sM3WvxKwr00sSv31SseaHyB/w601AX629cD0p6\r\ngMh3B6MMH7IDaufhJNZIe2W0Fc7UrpuIw1E=\r\n=JmTr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || ^16.0.0 || >=18.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "c0a67023b6f549fa776d581622c469d458cf7abf", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^15.2.1", "mocha": "^6.2.3", "eslint": "^6.8.0", "rimraf": "^3.0.2", "rollup": "^1.32.1", "ts-node": "^8.10.2", "dts-bundle": "^0.7.3", "typescript": "^3.9.10", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^12.2.4", "@types/mocha": "^5.2.7", "@types/eslint": "^6.8.1", "rollup-plugin-sourcemaps": "^0.6.3", "@mysticatea/eslint-plugin": "^13.0.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.0.0_1672426581414_0.4914325799466961", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@eslint-community/regexpp", "version": "4.1.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.1.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "b458cead28781c01c016284ba4423a04e9caa9b4", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.1.0.tgz", "fileCount": 8, "integrity": "sha512-F6OlXrdWuGtqYmO9hbDnbXZ5Z3b6gZ8+Eq28HioDMMYSe+dIIx3JveZlBgK4yC+O+lDADhAI8GLoCBTfkkXqFQ==", "signatures": [{"sig": "MEYCIQCToM4qb9d9/BfYfe1o4SDuK1by1xqS7Y5BRv4GFpEf2AIhAOj8FiDSSLzxtkkrpEH4V3c8sGkJvhxnDmrGZL/Mz5WR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwfILACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLOQ//X+4x9pXw7WOrMXSNauGQBwVT6HVnEDfsF5m2bl9xGKckzK8U\r\nKBHV2sfppNLXZFwSu6t8BlDMJbvsWXiqhh5N555atCiFWplTgnmeGcN2jZxW\r\nrPfwjb70hoZq/FCeFOM9Yto8R5fvk0c2nQg0lC62GwZ8wccQ0/v2RZvJoQ/x\r\nA4yuowooAE/gOi3pSIei7koEtbGnikgA9L4sZ0rWj8nqF7lg5YT44CvY9Jq7\r\nHH5YGUnABLvTYN/GJM1LoP0OG9csDh/rC6+PhC9XXDhfjO0AdXSwUE8MFv2a\r\n0sTnWiRiOKSiP4rRCMB40RSh2lnW9kFyrFwcFRXo8mVIGWLIaNGwoZNCJLCi\r\nxLDKbdwRRTLqTqcO46QHHNTPHXPNs79iFOTGw3/HIY/YukI3Bqc+fj2ReB2z\r\nWKJ+3UbIz2XUeVJ2acg0n81WaRsUyy9RkqrQ67DzcTKrRBzOa0AgqPo/xpE0\r\nHJozIfDgT6V3isAL8sOp3ZihOxqY7hhxuEI3NLW+IwCCbzU1MplIjiAmXsnF\r\nrwLy1h5AJmIp9IbxgeBvG7IcV9krhzc9Nw/voOqpj9qQY8VmvxUvq4syWYtw\r\nrAZ7361x8Hob86/3LvhTuV2nMt9T+qP3KnftX9FXV0ZmKr4m63k524qWrKDy\r\ne86E/6FcSDuECPM1Rc50JQcDBXQgH+V4j3Q=\r\n=xyxY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || ^16.0.0 || >=18.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "f373f34cc35aa68ec0c35d7d5c1f72c5ca9f7a86", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~4.9.4", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.1.0_1673654795657_0.48627951255061475", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@eslint-community/regexpp", "version": "4.2.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.2.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "55fcbea4a78d7b1d773d28b387118e16b1b3a239", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.2.0.tgz", "fileCount": 8, "integrity": "sha512-i+g5PjtJzZ+pXkf3fxGP2fNCpAOP/5LEBAGKegA17OdKQKJxQ09QlRel68t7o7BHgz7mVaDlp8VAnO8FcIhkSQ==", "signatures": [{"sig": "MEQCIEqMF5I9EmVsq+iCqoDdcXdJTEtlVKZwL+GsatzQLPASAiAoUMDCxUSIAnGSSXsfbJLeF0cGcmuWYM2XZKawUKLVVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwfMVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsiA/+LIRwtnf3jaj5scsMqzKe8hWD85y4rlpuz4rhEzsnZhIWphmY\r\nmvw5pmx/STejgvAnLLwaX/whbs6IMzCC+hmLb43zzeixV8LsibTW1+9VHbR3\r\nEkjq0A+Q7e80D3AeVAANOGNgFCBOnk+PH9pa3isQy7MLF7dsZSClFxycdR7x\r\n+hKp3M5r+y4P7qXJP0BVrxCBrh+FQmQbMzV2HN1qO6E6MY7C1yroBK9CBM5k\r\nrcIu806Uw/A6tjXD50VcN/l++UP1yt4wrPvBShgtZmlbkdTG8yh64zpbdy24\r\n9ewfnM2RsflaBjYqFpq14oxpsyMj8a40XiJ9kL9bs+Ch5uJZsnpAnr/hqRNF\r\nqb4svkK41eZTFwdQgF4DNLJQI/yjXUuJf6nAT6M6FBAgFth0hUbSCI7TOMJl\r\nOsQNowlMLCxVGC2jbnGoFIOa/9vWQNPQQYihSwd+9EQ2GP8dPud+jq/xjOP7\r\nVNzVIY73GhVLfj0lKhNIiH0MsKXx+OwoM8BW/RA2pWWWF3CVbSLQT3uhSbP2\r\nd4YBNw9Roqgi5QuHZgRxxrjRHjiFWtvnVZ8fAZaWiKfjBeJM/EmIq71aRdlr\r\nKCrifB/68yPY35QZLGDpvmASwN7n6S8Owh0Y1JJC5DiH2oTo2ywx+p/JP935\r\nS+Cvw/fVCD1SQe98jYZcxtyD4R7wWVRJYXU=\r\n=PmKJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || ^16.0.0 || >=18.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "c5696d654e04ab6bfb07a438c9a78b67b24c011b", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~4.9.4", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.2.0_1673655060780_0.03186268979893048", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@eslint-community/regexpp", "version": "4.3.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.3.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "a8fd720244a34bd9d2428bc28eea32ecdc74d950", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.3.0.tgz", "fileCount": 8, "integrity": "sha512-LPxsQrv/LmLq1jKj5m2PeiEgRy+3/WPx2x+hqke5iMnhr/Qt6GeP6TsVOcH8p42ApAnaJPbacRqMDEEIMtdehg==", "signatures": [{"sig": "MEUCIQDDcymONO3rQSyPBF6opIG6GrtH7UtK2PasevJZpxWXkwIgJ3vB2vu0lN/WSINUpaNng8UDwuzFjABQrmxFJcbHJnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxeuVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2ZBAAoqvsKYa2Tx9lVfMlXPqX5EzIqO52n0sOVIU7Oh7MW+xADBYJ\r\nGkgjmAlkOZFcmtMH+WdhOcclVHvHiBUM+sJF2QzqqbjSwj8AiY7KxGBzGXEj\r\nB1A2dWxTGI7Kbrpv51VxFJsMobMH9J51jxh4xJBNOVh/Ba9UF1Q48SUdLh5O\r\nN5P22zYF4YCKilvmHQ9YMO5E4DTxaDCYNlyIHjwyEFABscjCXTB9ZsSpdihP\r\nVx6HZbL9xpK7HOIS8b19u4i7qA9uWTLSAw26brUvukVu9n50eb1n5PcIw331\r\nW1wBSbzqNr+5Cfgl74/S1tWfq/GmrStKjWXX34s+KvpAHD6ZTqSJhF3OJJAF\r\n2MsgpjTjb8MxFhVqA4IrFImMXzsRsqXG+qIWnHRm72I5Ah54ZsH3rYl7c6qD\r\nB6kOGj1VHZwGGYzPdAUMAYSesJkd1MMf08nof0JId722E7LcrikCYu09yvBU\r\nmij8P8bkY+rGtZg6a/iLUeyPnqPx4Dw8U/Uehgj5eIZmKS8X9IdQkLeDBVvK\r\nFRoIvZ1zbwCx9MGFQa0B+2c5TUfehf/ITMoBbZo/ebHWyLPfa42mRUsBRW7t\r\n6ra9TvhbpgHkybIgFMvDnbtDIpFMgKB/KJ12av7HZCsyNl1Ds1FjXgmjI1qj\r\n8pdDHLxUiev2EqnxonE7uSQm4ZGDh34Q18M=\r\n=E44J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || ^16.0.0 || >=18.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "999b69a4439a279c135edd7968cc36e38f83b789", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~4.9.4", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.3.0_1673915285000_0.27900376544298067", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@eslint-community/regexpp", "version": "4.4.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.4.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "3e61c564fcd6b921cb789838631c5ee44df09403", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.4.0.tgz", "fileCount": 8, "integrity": "sha512-A9983Q0LnDGdLPjxyXQ00sbV+K+O+ko2Dr+CZigbHWtX9pNfxlaBkMR8X1CztI73zuEyEBXTVjx7CE+/VSwDiQ==", "signatures": [{"sig": "MEUCIQCeuk8hpuak8tnjizCGuRDL1qanjVbottZTHZvC/nAvhgIgb9D0a47uDNz8CNo7WrPHJ08goM2ofJDUMAk8HIf3IcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjx9btACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLNhAAkXEHg0jLQLrcW2RoEJkK9BRUcqneV0PRoiNBKnU5SUoA4WF0\r\nXZ8azruO814MdycbRrKYpmpbC9ED06rus8+V58121zq3UDhxle6NLGIBUNNd\r\nLeCCP4+FTR0KsDFRamO61tItfVxrq0DF8MjlcPl2osQjiPgUyk0Awmvs9OGF\r\nFNs+iKDIN2fzCIJmUJKhxwPfmQ2yBs8vSSf1F7dINqNqmhsTf4nWzH52u5GF\r\nzSY8HzOwBQ8mG0/h2PXJeEn3mAyEhROHUodT1OfswWWkYgaaqxJj55o1DMOl\r\nNTOJLv+gj7RlNNrqCEqaEv1rgqjenMF7P1AELpk455xMiOP1zA8cMKmYgMm3\r\n2QCPDBObXUbHpsFuhbakewjZF1iEP2j2UO9PXTvyAU1H0L9t1Q7Mg73pcYqq\r\nMdH3OET8LsacWFBZ23sh1fSJgQeFNTUo20aTuvjNLneogBEve4g47OOrKylC\r\nOwhAdtj2NizbFdx6fmFsobxn2hDCPBpUBkSvbZFVgQ+X/hV/MogcW8elx1/j\r\ntmCm4cE7OrVafEqFW3h88i1cS5PPuMsz1uJK/xzSSM9WZDf8KgmFkYcFB3Tz\r\n1y5jnhx8gWDIo5M78IKpOyjM8CJNpU3KeB02qERz1h9QOHS7OkzzGwiYoz8D\r\nFS9nreGUCqDgbhRtX9MTKABYhq3b3Td26/A=\r\n=JVJr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "ea8dee96311360bce1d8d76c136e25cdd595c6ec", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~4.9.4", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.4.0_1674041069553_0.803412381672937", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@eslint-community/regexpp", "version": "4.4.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.4.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "087cb8d9d757bb22e9c9946c9c0c2bf8806830f1", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.4.1.tgz", "fileCount": 8, "integrity": "sha512-BISJ6ZE4xQsuL/FmsyRaiffpq977bMlsKfGHTQrOGFErfByxIe6iZTxPf/00Zon9b9a7iUykfQwejN3s2ZW/Bw==", "signatures": [{"sig": "MEUCIBu5bck/vB70w/HUGeva+70zsYjc1JfthhEZjI0E1ok9AiEA+vXnhv1LkThX41gTpmH6UcVd/khJngbW4spysQWZ9Bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG/R8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFpw//bEpa5h9C4mIZU61xu2GJRmjz0KGXdPi8+ViMWSYU/gsDcn5O\r\nkEJqCHEWDW/5Fg6az10gOqcVY7Dt1cRoLpLbaYXQWbzyn3gn2YGnGb/If22Q\r\nR9xwD++6p+A7MojxjwV/zmmzS10N7y0WAxzJZ/hLslQu4cSrTisqozPUaDKx\r\n60O+dtt5G7Bh6MqzXzhbCiPbas0zvLiZVIR0cIv8CC1oIT93FeaULESnDyNh\r\ntMOQL+fQO7+w9tMObtgzsJ//G8VWZQUXGpvxn66GyXVzWnexYpsn8KO1/9Cb\r\nRv6jHhLMLlGW6W38BOSNcjsPbvhgoto7Ha30ZrXfTvca9ZX2urHsHZcVfNVN\r\nLQsn0siotTxcRuEUMLhgQZ5SPeeV18DmwSGEh+sL6a+2U27LTxUwE7Y50rUf\r\nHXLkTRudO7tJSlmyMFqvTLPf2AaFU4YCqmAf+SwwrqjB+8yCLTpwr+qsaPkS\r\nDK1KT8Q9dwnnJkUlJd8KTz0ww1uxa1q9Z4RgZVCrxdWnN4JY6uCssAsuj/eX\r\n/1E7qcudEfo0aub+mlJRa3UeGAFEPM+VQ5LFQ45vOHpach/8RbDPJpZw61uM\r\nc7OpcqeB7j3i4cLjW8tzxrMm9ORLmkCKf/uttQwWguzgO33uEcPSQtFsJAX/\r\nGBgMnG9qkGuBDHrShB6y1Bj4g5UOtSuXR+E=\r\n=5uC3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "5712c5fbc18dceadfa153a93f5bdcc54e4152147", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~4.9.4", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.4.1_1679553660005_0.14492672740378443", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@eslint-community/regexpp", "version": "4.5.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.5.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "f6f729b02feee2c749f57e334b7a1b5f40a81724", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.5.0.tgz", "fileCount": 8, "integrity": "sha512-vITaYzIcNmjn5tF5uxcZ/ft7/RXGrMUIS9HalWckEOF6ESiwXKoMzAQf2UW0aVd6rnOeExTJVd5hmWXucBKGXQ==", "signatures": [{"sig": "MEUCIQD1eq+YGkE0wP8owKivzwxdNGOECZdCzAjdkumre50qfwIgAl5TdokoteqqDn6wTiA6iQihbFgABt8iL1Gzq0uHkrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkI4zhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBvg/+LQMJQAT0RhbH11lLlM/EHn1Eyl71ZnnBBYxt+qV13n6tgdp3\r\n3lRrgSPDGirzGui9PViMv6oYzaHhzaeC0LrYS2TrDEOOMIPsalLQbxcyiczT\r\nGe8QDpQYzV5Tr/Cgc756eZOZYyXKeQWiSIkBf2UvcbDwkOlwErZQbB1onEhy\r\nPMRjw1HpesVie8vGzzCPIPBjSRuSHuBWiQZ5GOTADAHwLorj4ZZyzU4POOwe\r\nAtrg4dJeFURDAll68s6cM8jFvWnn10cXN46KLTQPgmy/7VWUe7QjZCT2dzwv\r\niKH9PtXqGnJl+g9NqXFzLicmuPoPZs1b0TOfrGf0VDPirk5ES/k+0Yv20LpU\r\nuqu0kFgxscWpiOJzLqjAU67EKeTgPI8nQpxADAbzYGm88S+TQxKiXaNw60+j\r\nx5JdDGjW5e0ntpI1UXvgAybRmFsyuu5nGt4D/unjaM7JYsGfg1iA+4MpvHd5\r\nk+vXNyhXJiELwOwbFTNfax9kTx0jvnmCJRa9JRzB4JB4zuXVRD4OtDjWb9h6\r\nVj8a2TSxDtKWuIQ9qgG5a/PhhN87srcd7b4riYXLNHWE4SMx47bJ6jKW8fSa\r\nyTMGuHM7NmPc0CthxXStRHavi+ChEKsSXuaxpuGJccJL3jL5OFxN9xLsmbJq\r\nQajsZQYSWzWjBLVvXZV/4sZNN9aT3RO/qHI=\r\n=+4ex\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "f66121da1189bcba0699092d81d554a1cd4ace49", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.5.0_1680051424978_0.5064109394750735", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@eslint-community/regexpp", "version": "4.5.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.5.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "cdd35dce4fa1a89a4fd42b1599eb35b3af408884", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.5.1.tgz", "fileCount": 8, "integrity": "sha512-Z5ba73P98O1KUYCCJTUeVpja9RcGoMdncZ6T49FCUl2lN38JtCJ+3WgIDBv0AuY4WChU5PmtJmOCTlN6FZTFKQ==", "signatures": [{"sig": "MEUCIQCsemeqnFRZSXz+9puEDQ9yxh9D85E9bF2XWX4ipVfu/AIgbhyIKdf5qARKv2DVgBv7HtywnenfZU69dyYMNrD+KSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTcXqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojOg//Xm1NhfkGVoDGbt5EhcuN35oWbJTBvl/nvZx9sjrpTtbJiGlr\r\nN/SuInexAHRcjbY/DJtpwdEEXaREjhLMf/zOl62nwgyT3fiPSsePsnjAmlli\r\n5lSrBBUIrfekm2IJgy0py+39n9/hZVlTWSZ6nRKOY7jyq4oaIQW0iapobedt\r\nlPCXHJCnnbWYD1aOLvQesLVj04YVigwx1i4hA/Z9VQaBGff7WZQnNhY14Rq1\r\nuD0TzHcTd+/wwd6f81KJsb3qld9uI8YracFFF1FinRvgVbLMf+BX/2sZ5lYU\r\nlB1hanqD0pB46Hp2f8cNTHqzvJbrqdu1Ofjw5kHNyoMWqxqW0dzdz8f9a/OO\r\nxsDdd9oJp+mYNJXw+i+3CINdHuaKj3GZbN/uPjqaeYdnkLNZapH3zvaVZvq6\r\nnd2b2iJ1Ys+mSXqI0SjrbCS2+PY0QR5q9VHkKN7mq8cnPdExicGI0Vd+Q0VH\r\nESdwpHH6qcMfXkzzBP1yDg83dSs1vt+9ApnLaLvgA9NNei2IXmCkdxEKO4Xl\r\nGmXIhdRtGb1RQloCw7+G5/l5OVC7S6pTrX929S6nC0ZCHCYSm6BFqHTDIVdr\r\nRdz9Tj/uLt1lUAsu2cQrtAD4jJF138DMGud0oi56Ixvgrf2Qqnkx9vdgOJPR\r\n4AKGydSwbPuk41PfPu6+VcNv7hF+QdN912c=\r\n=xPFA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "811bcaa9d54c797b0072f8fde987f48f8b69f6f3", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.5.1_1682818538487_0.19835097505155153", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@eslint-community/regexpp", "version": "4.6.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.6.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "5b63f0df5528a44e28aa8578d393de908cc3d4d0", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.6.0.tgz", "fileCount": 8, "integrity": "sha512-uiPeRISaglZnaZk8vwrjQZ1CxogZeY/4IYft6gBOTqu1WhVXWmCmZMWxUv2Q/pxSvPdp1JPaO62kLOcOkMqWrw==", "signatures": [{"sig": "MEQCICIYw+1R9n1cgGcRWi8f3M8Yy/cnw1UtziHv1j0VZCSRAiBPkxCSPbI01NpDpr7OacYsn1P/stEjhzgNLrk18ruaYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423015}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "31dac9d65af5b625197721e7646cb768289a838a", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.6.0_1689999475814_0.16004620891495325", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@eslint-community/regexpp", "version": "4.6.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.6.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "0b371c118b8e4ebf9dbddb56120ab4befd791211", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.6.1.tgz", "fileCount": 8, "integrity": "sha512-O7x6dMstWLn2ktjcoiNLDkAGG2EjveHL+Vvc+n0fXumkJYAcSqcVYKtwDU+hDZ0uDUsnUagSYaZrOLAYE8un1A==", "signatures": [{"sig": "MEQCIGL7ebAlOI3OhdHF3pO8r2BCKoDbSOb4TMCgdgiObwWiAiA75ADqbdzGo/gYxgcjIoe3dJCmu75eLlTWNmeSHPEvlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424104}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "96989dfad73492399d15e6432389cbc194320ced", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.6.1_1690207886330_0.5747641543171897", "host": "s3://npm-registry-packages"}}, "4.6.2": {"name": "@eslint-community/regexpp", "version": "4.6.2", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.6.2", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "1816b5f6948029c5eaacb0703b850ee0cb37d8f8", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.6.2.tgz", "fileCount": 8, "integrity": "sha512-pPTNuaAG3QMH+buKyBIGJs3g/S5y0caxw0ygM3YyE6yJFySwiGGSzA+mM3KJ8QQvzeLh3blwgSonkFjgQdxzMw==", "signatures": [{"sig": "MEUCIH33r4p6Ei1QIxYQcnBJoUKsOKgHmR2R2n5P/raWRWksAiEA3dUSCfOHcHBqgXshZccn3yYejNbRomBhqIuWOPQ0rWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 429151}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "72e9b5ac42fcdfed011026fb50e2e1fb3a1a4eff", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.6.2_1690328316031_0.5101489671838535", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@eslint-community/regexpp", "version": "4.7.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.7.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "96e7c05e738327602ae5942437f9c6b177ec279a", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.7.0.tgz", "fileCount": 8, "integrity": "sha512-+HencqxU7CFJnQb7IKtuNBqS6Yx3Tz4kOL8BJXo+JyeiBm5MEX6pO8onXDkjrkCRlfYXS1Axro15ZjVFe9YgsA==", "signatures": [{"sig": "MEUCIBXudd/H/YIE8ytvNcnfaPK5TBdPmAEJSLIpMAEozOqwAiEAtNTLFIEJFHP3C3f9L1Zdf5dcCd5XRF+cmx9vDWeApGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430415}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "6a8c5381e3f401d57607cd452e569704984a2207", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.20.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.7.0_1692662586283_0.9211867599050958", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@eslint-community/regexpp", "version": "4.8.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.8.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "11195513186f68d42fbf449f9a7136b2c0c92005", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.8.0.tgz", "fileCount": 8, "integrity": "sha512-JylOEEzDiOryeUnFbQz+oViCXS0KsvR1mvHkoMiu5+UiBvy+RYX7tzlIIIEstF/gVa2tj9AQXk3dgnxv6KxhFg==", "signatures": [{"sig": "MEQCIBEQD4AauhxhMEZJgw4tUrTc4W2oGwB/daZM/krsxSJ7AiA+UHxfAKEvEkIRyD24CGjbEJ1KEjWNTyERjQtyY1XXnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 429946}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "bc63c4c5e4f7b1b5f7f5e62868aa732ae33c2f70", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "16.20.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.8.0_1692947621788_0.15044182843429876", "host": "s3://npm-registry-packages"}}, "4.8.1": {"name": "@eslint-community/regexpp", "version": "4.8.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.8.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "8c4bb756cc2aa7eaf13cfa5e69c83afb3260c20c", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.8.1.tgz", "fileCount": 8, "integrity": "sha512-PWiOzLIUAjN/w5K17PoF4n6sKBw0gqLHPhywmYHP4t1VFQQVYeb1yWsJwnMVEMl3tUHME7X/SJPZLmtG7XBDxQ==", "signatures": [{"sig": "MEYCIQC6MBhvQ/dVQCkmv9GxVvnJGsXOk4OXdLq5U1Ewl7cR2wIhAMa0K9qegxzbx5ONs38Uw5BY4mg6llECVxDGWSXEUsNn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430388}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "97c3ce62367250ff8ae01e5322247e957aa247d9", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.8.1_1694476144437_0.23298992947044317", "host": "s3://npm-registry-packages"}}, "4.8.2": {"name": "@eslint-community/regexpp", "version": "4.8.2", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.8.2", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "26585b7c0ba36362893d3a3c206ee0c57c389616", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.8.2.tgz", "fileCount": 8, "integrity": "sha512-0MGxAVt1m/ZK+LTJp/j0qF7Hz97D9O/FH9Ms3ltnyIdDD57cbb1ACIQTkbHvNXtWDv5TPq7w5Kq56+cNukbo7g==", "signatures": [{"sig": "MEQCIETM6fi8AYcI2IScss8wIA6nxhZfsa+YYO9WkmyIa/7yAiB7In0NHv40K9ViHpWeYhiwIBqKY2DhcNPPGmtHt6iRiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430358}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "33e82031cfb6b329c2cb9431b0594b2329bb7b21", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.8.2_1695674585224_0.20398155789100714", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@eslint-community/regexpp", "version": "4.9.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.9.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "7ccb5f58703fa61ffdcbf39e2c604a109e781162", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.9.0.tgz", "fileCount": 8, "integrity": "sha512-zJmuCWj2VLBt4c25CfBIbMZLGLyhkvs7LznyVX5HfpzeocThgIj5XQK4L+g3U36mMcx8bPMhGyPpwCATamC4jQ==", "signatures": [{"sig": "MEYCIQC3MN52BwX7i4YkIMjxvMW8AgRTwA/eaZhLm4Uwx381sAIhAKjYb4Xve7VxdqA46Ys9Da18xe+iK2nXYfeYXVMTasHr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430398}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "8c2965ed934958374e50e9795b59e333c31c2dae", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.31.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.1", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.4.10", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.9.0_1695773623521_0.9498571246483387", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@eslint-community/regexpp", "version": "4.9.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.9.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "449dfa81a57a1d755b09aa58d826c1262e4283b4", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.9.1.tgz", "fileCount": 8, "integrity": "sha512-Y27x+MBLjXa+0JWDhykM3+JE+il3kHKAEqabfEWq3SDhZjLYb6/BHL/JKFnH3fe207JaXkyDo685Oc2Glt6ifA==", "signatures": [{"sig": "MEUCIQDqA35AMCF9GzQu6LtJFK/l+RQynO8axcxQG1Z27/w9OwIgJJO3txUf61ZUYHsToxVIR9C2oaSWAFBJpB7AgeLdbvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430288}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "af5c7f76fa0d5ab527f874f47813253a9e551c82", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.44.3", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.9.1_1696241932907_0.10051382960343846", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@eslint-community/regexpp", "version": "4.10.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.10.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "548f6de556857c8bb73bbee70c35dc82a2e74d63", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.10.0.tgz", "fileCount": 8, "integrity": "sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==", "signatures": [{"sig": "MEQCIHq7UKCGijNUp8deTo5hCRnk9hATGDGdpoV+3QH8t43PAiBs5/S7sL0CLzr3UPayUEISPyt4ubXNOrpuMbyE9il2GA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431246}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "2e8f1af992fb12eae46a446253e8fa3f6cede92a", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.8.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "test262": "git+https://github.com/tc39/test262.git", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.44.3", "test262-stream": "^1.4.0", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.10.0_1698250506280_0.8788374270523049", "host": "s3://npm-registry-packages"}}, "4.10.1": {"name": "@eslint-community/regexpp", "version": "4.10.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.10.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "361461e5cb3845d874e61731c11cfedd664d83a0", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.10.1.tgz", "fileCount": 8, "integrity": "sha512-Zm2NGpWELsQAD1xsJzGQpYfvICSsFkEpU0jxBjfdC6uNEWXcHnfs9hScFWtXVDVl+rBQJGrl4g1vcKIejpH9dA==", "signatures": [{"sig": "MEUCIQDVtrz0WRLW/0KWzUjRJJYzV4SaA2HqdIvHkLAdjlMDuAIgU2dI8DpO1eGVBasxXD9YFkJ9L7aMCh9Uh7qdgWvApTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432326}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "f38e97ac664fef01cb458af302a338f941134c47", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.8.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.44.3", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.10.1_1717426777089_0.08255437285404899", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@eslint-community/regexpp", "version": "4.11.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.11.0", "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "b0ffd0312b4a3fd2d6f77237e7248a5ad3a680ae", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.11.0.tgz", "fileCount": 8, "integrity": "sha512-G/M/tIiMrTAxEWRfLfQJMmGNX28IxBg4PBz8XqQhqUHLFI6TL2htpIB1iQCj144V5ee/JaKyT9/WZ0MGZWfA7A==", "signatures": [{"sig": "MEUCIHgdWkWCxy54TRkrdj5/xvdf6Bw8RiiACFOTEfIrjB7JAiEA/AEO/2cpTN6WWqEvDu/4fgV6JIKi9zLEcaWz0yHhNBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 445916}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "fb20f6888002ebdb92c99a6fede66b0720fe4dc0", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "npm-run-all": "^4.1.5", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/eslint": "^8.44.3", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.11.0_1719561764971_0.8705124513161069", "host": "s3://npm-registry-packages"}}, "4.11.1": {"name": "@eslint-community/regexpp", "version": "4.11.1", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.11.1", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "a547badfc719eb3e5f4b556325e542fbe9d7a18f", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.11.1.tgz", "fileCount": 8, "integrity": "sha512-m4DVN9ZqskZoLU5GlWZadwDnYo3vAEydiUayB9widCl9ffWx2IvPnp6n3on5rJmziJSw9Bv+Z3ChDVdMwXCY8Q==", "signatures": [{"sig": "MEUCIG3KnABqHC6DDe+RbZQ4i6UqzxEP7tPOctI6MEUXh0FsAiEA53ys+ozuvVlEaAUMXmyzR8dnr5l1CR3Kz2LhTkawMCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 446411}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "4d4787a2479b5c7f4984227c40123749898fb8a2", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "npm-run-all2": "^6.2.2", "@types/eslint": "^8.44.3", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.11.1_1726354939446_0.9756099096324142", "host": "s3://npm-registry-packages"}}, "4.11.2": {"name": "@eslint-community/regexpp", "version": "4.11.2", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.11.2", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "dc6925ab4ea52d3b9d6da204a3f0dd9c3852e3ad", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.11.2.tgz", "fileCount": 8, "integrity": "sha512-2WwyTYNVaMNUWPZTOJdkax9iqTdirrApgTbk+Qoq5EPX6myqZvG8QGFRgdKmkjKVG6/G/a565vpPauHk0+hpBA==", "signatures": [{"sig": "MEQCIB6o1Oub+XZ/3YvX2obrZj1AHF9HFuafyiHkF0ZwVT3vAiBXyj8A780oc8MyMB8Ohw3ihjyLz5/+tnV5qWxHxPzQ0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2fregexpp@4.11.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 446411}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "25af7a79f1bae5782c33f32e0ba2dac54ed4b756", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "npm-run-all2": "^6.2.2", "@types/eslint": "^8.44.3", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.11.2_1729899879913_0.4133897171806675", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@eslint-community/regexpp", "version": "4.12.0", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@eslint-community/regexpp@4.12.0", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "dist": {"shasum": "1b8e62d1244557927b9a7fc7a96e5bbd62e1870e", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.0.tgz", "fileCount": 8, "integrity": "sha512-gh7PdNombP8ftL8TinYC8Xd7WEypB8EKV4PI2h0eMzndKjPCXuo2zUiZtD2Hu+MSPt02Ty2MdS788ADl9ai1rA==", "signatures": [{"sig": "MEYCIQD0mgtE/c99xfPjP8GbgHQUB6caqv4jVLPCkE/yY1e29QIhAO+uq38EEW96bpkaDN1z6zYf9reLAAMp4TDW9h3I7aPT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2fregexpp@4.12.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 472794}, "main": "index", "types": "./index.d.ts", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "347b151931ec79757bd62dff0fc88e62b20f1cbd", "scripts": {"lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "build": "run-s build:*", "clean": "rimraf .temp index.*", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl", "prebuild": "npm run -s clean", "prewatch": "npm run -s clean", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "build:tsc": "tsc --module es2015", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "update:test": "ts-node scripts/update-fixtures.ts", "build:rollup": "rollup -c", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts"}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint-community/regexpp.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Regular expression parser for ECMAScript.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "jsdom": "^19.0.0", "mocha": "^9.2.2", "eslint": "^8.50.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "ts-node": "^10.9.1", "js-tokens": "^8.0.2", "dts-bundle": "^0.7.3", "typescript": "~5.0.2", "@types/node": "^12.20.55", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "npm-run-all2": "^6.2.2", "@types/eslint": "^8.44.3", "rollup-plugin-sourcemaps": "^0.6.3", "@rollup/plugin-node-resolve": "^14.1.0", "@eslint-community/eslint-plugin-mysticatea": "^15.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexpp_4.12.0_1730029248507_0.18687818020567226", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@eslint-community/regexpp", "version": "4.12.1", "description": "Regular expression parser for ECMAScript.", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "homepage": "https://github.com/eslint-community/regexpp#readme", "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "repository": {"type": "git", "url": "git+https://github.com/eslint-community/regexpp.git"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.js"}, "./package.json": "./package.json"}, "main": "index", "scripts": {"prebuild": "npm run -s clean", "build": "run-s build:*", "build:tsc": "tsc --module es2015", "build:rollup": "rollup -c", "build:dts": "npm run -s build:tsc -- --removeComments false && dts-bundle --name @eslint-community/regexpp --main .temp/index.d.ts --out ../index.d.ts && prettier --write index.d.ts", "clean": "rimraf .temp index.*", "lint": "eslint . --ext .ts", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "debug": "mocha --require ts-node/register/transpile-only \"test/*.ts\" --reporter dot --timeout 10000", "update:test": "ts-node scripts/update-fixtures.ts", "update:unicode": "run-s update:unicode:*", "update:unicode:ids": "ts-node scripts/update-unicode-ids.ts", "update:unicode:props": "ts-node scripts/update-unicode-properties.ts", "update:test262:extract": "ts-node -T scripts/extract-test262.ts", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions ts --watch --growl"}, "dependencies": {}, "devDependencies": {"@eslint-community/eslint-plugin-mysticatea": "^15.5.1", "@rollup/plugin-node-resolve": "^14.1.0", "@types/eslint": "^8.44.3", "@types/jsdom": "^16.2.15", "@types/mocha": "^9.1.1", "@types/node": "^12.20.55", "dts-bundle": "^0.7.3", "eslint": "^8.50.0", "js-tokens": "^8.0.2", "jsdom": "^19.0.0", "mocha": "^9.2.2", "npm-run-all2": "^6.2.2", "nyc": "^14.1.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.1", "typescript": "~5.0.2"}, "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}, "_id": "@eslint-community/regexpp@4.12.1", "gitHead": "c4852c9efb299a8579651e3ac3c7d9a71b89f8c6", "types": "./index.d.ts", "_nodeVersion": "20.13.1", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==", "shasum": "cfc6cffe39df390a3841cde2abccf92eaa7ae0e0", "tarball": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "fileCount": 8, "unpackedSize": 472794, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint-community%2fregexpp@4.12.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1zUfFzEhmWmb/4YBjB2Rvc0LJwJzsVG7PCR/Q6qj79wIhAKRZJzYUrMYXb8/WYZ3o62+YAEhpbAOn5oB3HZ8XC1O2"}]}, "_npmUser": {"name": "eslint-community-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regexpp_4.12.1_1730075914041_0.3150895126156603"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-11-28T01:53:47.526Z", "modified": "2024-10-28T00:38:34.716Z", "3.2.0": "2022-11-28T01:53:47.788Z", "3.2.1": "2022-11-28T02:01:37.355Z", "4.0.0": "2022-12-30T18:56:21.648Z", "4.1.0": "2023-01-14T00:06:35.825Z", "4.2.0": "2023-01-14T00:11:00.985Z", "4.3.0": "2023-01-17T00:28:05.145Z", "4.4.0": "2023-01-18T11:24:29.688Z", "4.4.1": "2023-03-23T06:41:00.199Z", "4.5.0": "2023-03-29T00:57:05.163Z", "4.5.1": "2023-04-30T01:35:38.692Z", "4.6.0": "2023-07-22T04:17:56.036Z", "4.6.1": "2023-07-24T14:11:26.522Z", "4.6.2": "2023-07-25T23:38:36.278Z", "4.7.0": "2023-08-22T00:03:06.470Z", "4.8.0": "2023-08-25T07:13:42.025Z", "4.8.1": "2023-09-11T23:49:04.722Z", "4.8.2": "2023-09-25T20:43:05.397Z", "4.9.0": "2023-09-27T00:13:43.756Z", "4.9.1": "2023-10-02T10:18:53.151Z", "4.10.0": "2023-10-25T16:15:06.791Z", "4.10.1": "2024-06-03T14:59:37.304Z", "4.11.0": "2024-06-28T08:02:45.181Z", "4.11.1": "2024-09-14T23:02:19.610Z", "4.11.2": "2024-10-25T23:44:40.215Z", "4.12.0": "2024-10-27T11:40:48.697Z", "4.12.1": "2024-10-28T00:38:34.193Z"}, "bugs": {"url": "https://github.com/eslint-community/regexpp/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/eslint-community/regexpp#readme", "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "annexB"], "repository": {"type": "git", "url": "git+https://github.com/eslint-community/regexpp.git"}, "description": "Regular expression parser for ECMAScript.", "maintainers": [{"name": "eslint-community-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# @eslint-community/regexpp\n\n[![npm version](https://img.shields.io/npm/v/@eslint-community/regexpp.svg)](https://www.npmjs.com/package/@eslint-community/regexpp)\n[![Downloads/month](https://img.shields.io/npm/dm/@eslint-community/regexpp.svg)](http://www.npmtrends.com/@eslint-community/regexpp)\n[![Build Status](https://github.com/eslint-community/regexpp/workflows/CI/badge.svg)](https://github.com/eslint-community/regexpp/actions)\n[![codecov](https://codecov.io/gh/eslint-community/regexpp/branch/main/graph/badge.svg)](https://codecov.io/gh/eslint-community/regexpp)\n\nA regular expression parser for ECMAScript.\n\n## 💿 Installation\n\n```bash\n$ npm install @eslint-community/regexpp\n```\n\n- require Node@^12.0.0 || ^14.0.0 || >=16.0.0.\n\n## 📖 Usage\n\n```ts\nimport {\n    AST,\n    RegExpParser,\n    RegExpValidator,\n    RegExpVisitor,\n    parseRegExpLiteral,\n    validateRegExpLiteral,\n    visitRegExpAST\n} from \"@eslint-community/regexpp\"\n```\n\n### parseRegExpLiteral(source, options?)\n\nParse a given regular expression literal then make AST object.\n\nThis is equivalent to `new RegExpParser(options).parseLiteral(source)`.\n\n- **Parameters:**\n    - `source` (`string | RegExp`) The source code to parse.\n    - `options?` ([`RegExpParser.Options`]) The options to parse.\n- **Return:**\n    - The AST of the regular expression.\n\n### validateRegExpLiteral(source, options?)\n\nValidate a given regular expression literal.\n\nThis is equivalent to `new RegExpValidator(options).validateLiteral(source)`.\n\n- **Parameters:**\n    - `source` (`string`) The source code to validate.\n    - `options?` ([`RegExpValidator.Options`]) The options to validate.\n\n### visitRegExpAST(ast, handlers)\n\nVisit each node of a given AST.\n\nThis is equivalent to `new RegExpVisitor(handlers).visit(ast)`.\n\n- **Parameters:**\n    - `ast` ([`AST.Node`]) The AST to visit.\n    - `handlers` ([`RegExpVisitor.Handlers`]) The callbacks.\n\n### RegExpParser\n\n#### new RegExpParser(options?)\n\n- **Parameters:**\n    - `options?` ([`RegExpParser.Options`]) The options to parse.\n\n#### parser.parseLiteral(source, start?, end?)\n\nParse a regular expression literal.\n\n- **Parameters:**\n    - `source` (`string`) The source code to parse. E.g. `\"/abc/g\"`.\n    - `start?` (`number`) The start index in the source code. Default is `0`.\n    - `end?` (`number`) The end index in the source code. Default is `source.length`.\n- **Return:**\n    - The AST of the regular expression.\n\n#### parser.parsePattern(source, start?, end?, flags?)\n\nParse a regular expression pattern.\n\n- **Parameters:**\n    - `source` (`string`) The source code to parse. E.g. `\"abc\"`.\n    - `start?` (`number`) The start index in the source code. Default is `0`.\n    - `end?` (`number`) The end index in the source code. Default is `source.length`.\n    - `flags?` (`{ unicode?: boolean, unicodeSets?: boolean }`) The flags to enable Unicode mode, and Unicode Set mode.\n- **Return:**\n    - The AST of the regular expression pattern.\n\n#### parser.parseFlags(source, start?, end?)\n\nParse a regular expression flags.\n\n- **Parameters:**\n    - `source` (`string`) The source code to parse. E.g. `\"gim\"`.\n    - `start?` (`number`) The start index in the source code. Default is `0`.\n    - `end?` (`number`) The end index in the source code. Default is `source.length`.\n- **Return:**\n    - The AST of the regular expression flags.\n\n### RegExpValidator\n\n#### new RegExpValidator(options)\n\n- **Parameters:**\n    - `options` ([`RegExpValidator.Options`]) The options to validate.\n\n#### validator.validateLiteral(source, start, end)\n\nValidate a regular expression literal.\n\n- **Parameters:**\n    - `source` (`string`) The source code to validate.\n    - `start?` (`number`) The start index in the source code. Default is `0`.\n    - `end?` (`number`) The end index in the source code. Default is `source.length`.\n\n#### validator.validatePattern(source, start, end, flags)\n\nValidate a regular expression pattern.\n\n- **Parameters:**\n    - `source` (`string`) The source code to validate.\n    - `start?` (`number`) The start index in the source code. Default is `0`.\n    - `end?` (`number`) The end index in the source code. Default is `source.length`.\n    - `flags?` (`{ unicode?: boolean, unicodeSets?: boolean }`) The flags to enable Unicode mode, and Unicode Set mode.\n\n#### validator.validateFlags(source, start, end)\n\nValidate a regular expression flags.\n\n- **Parameters:**\n    - `source` (`string`) The source code to validate.\n    - `start?` (`number`) The start index in the source code. Default is `0`.\n    - `end?` (`number`) The end index in the source code. Default is `source.length`.\n\n### RegExpVisitor\n\n#### new RegExpVisitor(handlers)\n\n- **Parameters:**\n    - `handlers` ([`RegExpVisitor.Handlers`]) The callbacks.\n\n#### visitor.visit(ast)\n\nValidate a regular expression literal.\n\n- **Parameters:**\n    - `ast` ([`AST.Node`]) The AST to visit.\n\n## 📰 Changelog\n\n- [GitHub Releases](https://github.com/eslint-community/regexpp/releases)\n\n## 🍻 Contributing\n\nWelcome contributing!\n\nPlease use GitHub's Issues/PRs.\n\n### Development Tools\n\n- `npm test` runs tests and measures coverage.\n- `npm run build` compiles TypeScript source code to `index.js`, `index.js.map`, and `index.d.ts`.\n- `npm run clean` removes the temporary files which are created by `npm test` and `npm run build`.\n- `npm run lint` runs ESLint.\n- `npm run update:test` updates test fixtures.\n- `npm run update:ids` updates `src/unicode/ids.ts`.\n- `npm run watch` runs tests with `--watch` option.\n\n[`AST.Node`]: src/ast.ts#L4\n[`RegExpParser.Options`]: src/parser.ts#L743\n[`RegExpValidator.Options`]: src/validator.ts#L220\n[`RegExpVisitor.Handlers`]: src/visitor.ts#L291\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}