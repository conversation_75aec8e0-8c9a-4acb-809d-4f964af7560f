{"_id": "buffer-from", "_rev": "10-3b2356a528dd0653c34fea2ebc334a8b", "name": "buffer-from", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.1.0": {"name": "buffer-from", "version": "0.1.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/linusu/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "gitHead": "25cc13c38ea511a655495f990c4846a419f219c3", "description": "A ponyfill for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/linusu/buffer-from/issues"}, "homepage": "https://github.com/linusu/buffer-from#readme", "_id": "buffer-from@0.1.0", "_shasum": "a49dce54d048e955595d22e983ba5fe8aed5ffdd", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"shasum": "a49dce54d048e955595d22e983ba5fe8aed5ffdd", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.0.tgz", "integrity": "sha512-VB11PczNxawoNdSfx9Wtp3e7u32ZvxtoTmBy3FBSF1AknhVjJIn6imh5IHn0/3jHGcjRxFDk9gkiemOtxfvKiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHOTbZuHjJgi4b4Y0v7QTkDqdZRskrDXsVUjdV/wZ1oaAiAUhEup+ldUmX/NZ+OGfRLPiW2NuqmlsVpl/9S0Q5Qm2g=="}]}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/buffer-from-0.1.0.tgz_1471550043197_0.43763306783512235"}, "directories": {}}, "0.1.1": {"name": "buffer-from", "version": "0.1.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/linusu/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "dependencies": {"is-array-buffer-x": "^1.0.13"}, "gitHead": "07ff5440f58e6b279dac5e9007eed4f51cf84e92", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/linusu/buffer-from/issues"}, "homepage": "https://github.com/linusu/buffer-from#readme", "_id": "buffer-from@0.1.1", "_shasum": "57b18b1da0a19ec06f33837a5275a242351bd75e", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "0.10.45", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"shasum": "57b18b1da0a19ec06f33837a5275a242351bd75e", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.1.tgz", "integrity": "sha512-ojL8pkJEJceHwDvyOXDlgJWLm2GruKWrykCPPfh1UBccsKsCd3QxUD9DinrU8DJsaSd/cug76qKYbiYcBFUNww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH91BBaXLWErR9u+lrQRYcQ5MYbELr/AuQHsYN5+RWqwIhANR5pzgHc5juVEqeMYnkNpPN+YmgC5VN2UILu71yXc1Y"}]}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-from-0.1.1.tgz_1476621469818_0.06613527098670602"}, "directories": {}}, "0.1.2": {"name": "buffer-from", "version": "0.1.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "gitHead": "4d65c51194eb8786218096d33a4613a091dbde5c", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "homepage": "https://github.com/LinusU/buffer-from#readme", "_id": "buffer-from@0.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RiWIenusJsmI2KcvqQABB83tLxCByE3upSP8QU3rJDMVFGPWLvPQJt/O1Su9moRWeH7d+Q2HYb68f6+v+tw2vg==", "shasum": "15f4b9bcef012044df31142c14333caf6e0260d0", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.2.tgz", "fileCount": 4, "unpackedSize": 4250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC36wb1f4oIP0Fw03snth62xyHhNp4QhZWl+7lwtLXi1AiEArX4FrsGq89Wydp7vHqmow5mypBugX+JY7vswkX2sv6U="}]}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-from_0.1.2_1520881959527_0.8321384344651832"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "buffer-from", "version": "1.0.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "gitHead": "645a803486f7aa17affe1b783f84f496789e28f9", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "homepage": "https://github.com/LinusU/buffer-from#readme", "_id": "buffer-from@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-83apNb8KK0Se60UE1+4Ukbe3HbfELJ6UlI4ldtOGs7So4KD26orJM8hIY9lxdzP+UpItH1Yh/Y8GUvNFWFFRxA==", "shasum": "4cb8832d23612589b0406e9e2956c17f06fdf531", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.0.0.tgz", "fileCount": 4, "unpackedSize": 4250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/nmfoa7Q2oZmWH0dJsYeOWQn/y0vwN09yWftZ8kIeLAIgV9LEvesCXOPIAXgvVdtGJzOs9CH/30yxdQ0LXB+TC3Q="}]}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-from_1.0.0_1520882003570_0.3930838505714873"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "buffer-from", "version": "1.1.0", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "files": ["index.js"], "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "gitHead": "2bea5de4fdca7b13b5a9edafdcb1b74b9ffa6f98", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "homepage": "https://github.com/LinusU/buffer-from#readme", "_id": "buffer-from@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.2.1", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c5mRlguI/Pe2dSZmpER62rSCu0ryKmWddzRYsuXc50U2/g8jMOulc31VZMa4mYx31U5xsmSOpDCgH88Vl9cDGQ==", "shasum": "87fcaa3a298358e0ade6e442cfce840740d1ad04", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.0.tgz", "fileCount": 3, "unpackedSize": 3888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDeRgCRA9TVsSAnZWagAAd9EP/2tgjGpg0D2foCpZEQKf\nCT1dXMK17+ogbS9ra2xGjuPclU3fkXMihwUINK7/xMoxxFo8fGOwMFPblOHM\nBfO8mEyUyWD8X2SNGwIC3thzobtiQFSiQIzGWag5G6TZxKcnhWXWDcRnGBeu\nfYF5N9K6TDItxL920LlzqJ3Y7glr4Wv7SDJyrzVBfEIGmfgM8aVad5MZNeEK\nv+Nzt/kd2jt5PvUoOoY826q03Vg14awD8vx9tJc6AoaFCOnOBnD/1uAqeg4l\nW404tmnqtfCBdSZj+/iv0xdjJSZqqInsiXK01+HNxorY7mSTc0Bdh0b8tnkA\njONz5g0k5a4S8Y1LYVfNg7MvmryxQFtdBCJj15eYVU5pYkR8AohCOkNF6vCH\nWPjS3FOfY3y6/ffbY32YhLnDAhIMk3leI9UIc2KvbQ0/bJgXHYRhrdU2Iumx\nmrx43cXbwTqLjngV23UISic6/5jJxyUbUYmjTL72j71bU61sZ/AWn8MwAXEY\nN56xPgL8MztdIcwHV+QqjekkP8YLiLutqAui42EtIxd8vZamypd2lOKiKEh6\nyJ641PVOvCKiIEq4F+9jqy0HtNnUIwZ5wOR+CMHwkaM4BkszVbviUA/9B0+K\n8dF13Js/z5YenP+8Atp4CUyAebmCrmJ5prFvFD+blLVoQSMjqz8KvHvHbNrh\nJ6rs\r\n=2hhN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTe6zGexIXMuWGkMiZbPTA0mzpKkDEsGrBDdX0XhWTnAiAno9YgbbuoBbvIAUztJ9agx+rUsrGgxC9T7seApUP1FA=="}]}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-from_1.1.0_1527637088129_0.03838545968741203"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "buffer-from", "version": "1.1.1", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "files": ["index.js"], "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "gitHead": "53f464194ae30e806f6faef635202b4dfd404ed5", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "homepage": "https://github.com/LinusU/buffer-from#readme", "_id": "buffer-from@1.1.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "shasum": "32713bc028f75c02fdb710d7c7bcec1f2c6070ef", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "fileCount": 4, "unpackedSize": 4966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYHUTCRA9TVsSAnZWagAAgoQP/1LXApc57d1uAc/0mnLq\nCI87dq7EtKS4lG7EjFY3yzxIZxlq1++vH0/sWe0V8je9sHhrm/AkSxlcplEV\nWELPjRwIY4h3LP6U5ppG7XAVGmbipkIAGhiRaJtqGfI/TgPRiB3QcrqanlRG\nBf4oDubBJ2vK8dh5Gd0AhAkkTOsIcf4b5uC64A5GUtMoPOrko+zyRYM8IrRo\n7kDspGfsk3YYxyB/L0APD4AIC8vYz2Gin2ca+zJBZH6cvfThhFWbSnCFfVuW\nxXcheune4boTojusOn5dm4LxzFyMgbeibDZTaq+Yod3RuuUVaSCXWNITcT6g\nIlP7wdbBFGx7Ik5hKEtAGtsCdWshzg9YxHR7g7CPe+0xIeb0zNeKBlxFF2jT\niPeiI+JGlMZuH2Qu+284T9WWom/j0NcbZuWmrV5E1GUimVJqtgqD3S3ivc4y\n6fS6OTIemRUmWwmS7farPxzTs34QkUp0vH/dhuvNeZKockTjLvlveHlOtC4m\nhGp5UdiwrsV7Xqqy/RHlACxPjexkDfhwi0FSeY799VkzbgrPm7Npwn8HwMDI\nv6qpaxPbruRJx65zp1J4DqWlhYENreSFOaXGY/t34GIQ1yNHXGg4Z2ltshPi\nb3lJuLEolQYK5GAcryVnAGusAisYQDfygT1BPxYsVT2ZWYmicpHs7UD+70ya\nfbJa\r\n=YgJu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGOpkDuA6FlIl3GH4TNUaaSfuNzhyYhU8BlqyHab5Ns2AiB5Y0974Ito8JACG79Z3KLYKOMEeY70O9aPG9PmzEOOmA=="}]}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-from_1.1.1_1533048083841_0.2458068233892754"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "buffer-from", "version": "1.1.2", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^12.0.1"}, "keywords": ["buffer", "buffer from"], "gitHead": "fa0c9264a1b7eddf8c1aa1bbd78ce0ac256c485f", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "homepage": "https://github.com/LinusU/buffer-from#readme", "_id": "buffer-from@1.1.2", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "shasum": "2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5", "tarball": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "fileCount": 4, "unpackedSize": 5047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAt/dCRA9TVsSAnZWagAAK3gP/3U4DZr6cDPvIUqEzn6Z\npqnUZD8mwnMtuFYU2LFuB1/biFBrLv0Mph2aXBh8k2nCjKoMuqy7AKGVkv7q\n1ieA/JAHWdEhDuo82I+Hi0qqzw9jpwp7l5mLFTdFX90HtA8BOvyWGDkdg3pL\nbFVP1dxBHu8LI3zrWnd2ruAYOoylSXNysJuXwaSqV6OToz/8ZRYrbyzZC/4m\noqxS6WdflQcs7jSLJkBZSHTV6rmCoDg3FbB5zLiJOUfj4oUM/JnlkN6cR54/\nhXVJo0s0+Rqb8KL7+tR3pj+sjJQqtzvPi21sMBbkg/btayJs9DUJSEp6h0/x\nfSgTBGVaPT5jbAM45e2KmkAI78eLlpwNUZzM34FFuYj8Zl1Fjnckdj7Fgadn\nJLpv283KG4oYY5yF16inPIaAh7hf74eNVx+KBjTs4jOt89CDFEmViANgV5+e\nLhS21CdMMkPIuA4xnB+Ya5jeC19Z03gKSN+t5vA2nFK2l0K7/JUMzF4jICUz\nr2q5kt3RiN2/EH3I4wYUnE6cuOKWmxF4IpB9rNU16ARV5Bxq4zp42JoDgGie\nbpaVM8YWvkMAj0eBXJ+pW5JSDdQnwpuiaPx9ry3ZfpYv9QkCWjr0qibSsXu+\n10CK9AbxTy+QRPNYkTn/te7WH2bc/V3l80ypNEnXJJICKWgOzgH4W4kG1fjH\nHMP0\r\n=2uut\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICwILuKQPZZQ6XxAN2DiGeVk/fpq0Ls5VaL1Sxgl4JGcAiAubelJ6nbraK71jpE/QRm7uePA3TtO4Yo/sXvSly8W7Q=="}]}, "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-from_1.1.2_1627578332878_0.7445773613261815"}, "_hasShrinkwrap": false}}, "readme": "# Buffer From\n\nA [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.\n\n## Installation\n\n```sh\nnpm install --save buffer-from\n```\n\n## Usage\n\n```js\nconst bufferFrom = require('buffer-from')\n\nconsole.log(bufferFrom([1, 2, 3, 4]))\n//=> <Buffer 01 02 03 04>\n\nconst arr = new Uint8Array([1, 2, 3, 4])\nconsole.log(bufferFrom(arr.buffer, 1, 2))\n//=> <Buffer 02 03>\n\nconsole.log(bufferFrom('test', 'utf8'))\n//=> <Buffer 74 65 73 74>\n\nconst buf = bufferFrom('test')\nconsole.log(bufferFrom(buf))\n//=> <Buffer 74 65 73 74>\n```\n\n## API\n\n### bufferFrom(array)\n\n- `array` &lt;Array&gt;\n\nAllocates a new `Buffer` using an `array` of octets.\n\n### bufferFrom(arrayBuffer[, byteOffset[, length]])\n\n- `arrayBuffer` &lt;ArrayBuffer&gt; The `.buffer` property of a TypedArray or ArrayBuffer\n- `byteOffset` &lt;Integer&gt; Where to start copying from `arrayBuffer`. **Default:** `0`\n- `length` &lt;Integer&gt; How many bytes to copy from `arrayBuffer`. **Default:** `arrayBuffer.length - byteOffset`\n\nWhen passed a reference to the `.buffer` property of a TypedArray instance, the\nnewly created `Buffer` will share the same allocated memory as the TypedArray.\n\nThe optional `byteOffset` and `length` arguments specify a memory range within\nthe `arrayBuffer` that will be shared by the `Buffer`.\n\n### bufferFrom(buffer)\n\n- `buffer` &lt;Buffer&gt; An existing `Buffer` to copy data from\n\nCopies the passed `buffer` data onto a new `Buffer` instance.\n\n### bufferFrom(string[, encoding])\n\n- `string` &lt;String&gt; A string to encode.\n- `encoding` &lt;String&gt; The encoding of `string`. **Default:** `'utf8'`\n\nCreates a new `Buffer` containing the given JavaScript string `string`. If\nprovided, the `encoding` parameter identifies the character encoding of\n`string`.\n\n## See also\n\n- [buffer-alloc](https://github.com/LinusU/buffer-alloc) A ponyfill for `Buffer.alloc`\n- [buffer-alloc-unsafe](https://github.com/LinusU/buffer-alloc-unsafe) A ponyfill for `Buffer.allocUnsafe`\n", "maintainers": [{"name": "linusu", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T05:15:19.171Z", "created": "2016-08-18T19:54:04.205Z", "0.1.0": "2016-08-18T19:54:04.205Z", "0.1.1": "2016-10-16T12:37:51.558Z", "0.1.2": "2018-03-12T19:12:39.586Z", "1.0.0": "2018-03-12T19:13:23.644Z", "1.1.0": "2018-05-29T23:38:08.325Z", "1.1.1": "2018-07-31T14:41:23.906Z", "1.1.2": "2021-07-29T17:05:33.074Z"}, "homepage": "https://github.com/LinusU/buffer-from#readme", "keywords": ["buffer", "buffer from"], "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "license": "MIT", "readmeFilename": "readme.md"}