{"_id": "lodash.merge", "_rev": "73-61fd751c90daf5ef95ee8622b36d673a", "name": "lodash.merge", "description": "The Lodash method `_.merge` exported as a module.", "dist-tags": {"latest": "4.6.2"}, "versions": {"2.0.0": {"name": "lodash.merge", "version": "2.0.0", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.0.0", "lodash._basemerge": "~2.0.0", "lodash._getarray": "~2.0.0", "lodash.isobject": "~2.0.0", "lodash._releasearray": "~2.0.0"}, "_id": "lodash.merge@2.0.0", "dist": {"shasum": "0fc4963e1947d2d892db0da045d8401e819ee838", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.0.0.tgz", "integrity": "sha512-vD5vGnRW2OiP7tae/l2E2iNwy2iZNN0ghKX/Hx15eryxYYuFijLa6GCHWXJ8G7CfVV1Z/SJaoS7Q3S2mQYwuXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFdggW7O/0ixA7GT8Zama6dhnr7lHJC+dWFhijBlrTjHAiEApOHT/aQZC/9NHoWvpQMOSz6Hs4b/VHPId45jIBk6+mM="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "lodash.merge", "version": "2.1.0", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.1.0", "lodash._basemerge": "~2.1.0", "lodash._getarray": "~2.1.0", "lodash.isobject": "~2.1.0", "lodash._releasearray": "~2.1.0"}, "_id": "lodash.merge@2.1.0", "dist": {"shasum": "ac631630df121f6874da08979c88d2cf6f92392e", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.1.0.tgz", "integrity": "sha512-OdCp1EzpzUbdDGDUUm0f+74qHBk3HpCfbwd1SEzE2h68qIFJavobnF6MfypJfk7SIUA+nXVhS0ZQ/FMphAmBLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBxLsA0I05RIyQFb7eoJEy0Z5v4HhlA11Gq+3JgtpAS9AiEA8KyKLNuTunOD8j57BnciQ1ormtvyklzAYBWY+ChdD2c="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "lodash.merge", "version": "2.2.0", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.2.0", "lodash._basemerge": "~2.2.0", "lodash._getarray": "~2.2.0", "lodash.isobject": "~2.2.0", "lodash._releasearray": "~2.2.0"}, "_id": "lodash.merge@2.2.0", "dist": {"shasum": "8f08a16917bd6a5897d8350de9aaf0e5196ba907", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.2.0.tgz", "integrity": "sha512-Cy7lU94sZmSmSR9JGOBNVa1q9dR4ePBgNzkbQHEbWLBvmxlUanMRVRQsEcEVN8mpOok2ahKqVdwI5dMBqDJ7XQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtn5K366QPlrKzrrobQiIZ1decGV9KF9S+nQJDTPVB2wIhAIGydRg0I4DhPuoqJ0pMYcKJ2LSNr6urIdmlya7wnCtC"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.2.1": {"name": "lodash.merge", "version": "2.2.1", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.2.1", "lodash._basemerge": "~2.2.1", "lodash._getarray": "~2.2.1", "lodash.isobject": "~2.2.1", "lodash._releasearray": "~2.2.1"}, "_id": "lodash.merge@2.2.1", "dist": {"shasum": "572920c79bf97b12cf4773332ca2f4aa6e9b0306", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.2.1.tgz", "integrity": "sha512-GUiBmzCqX6JycZ/M0eypZp6jZoGQ1d5TKshDnvnA10yMiP31s8p5jQpkzfTqr89TesfYj+Mr0KeilhoKRZ0Q+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5wn1SMA4T2X9M9re25a9hBA2hKY27yOghBYYcEbeuSAiEAhciCpQ0PPGkGyKRXrBgrpckrS4oI0BDCFJ8xMYauaks="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "lodash.merge", "version": "2.3.0", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.3.0", "lodash._basemerge": "~2.3.0", "lodash._getarray": "~2.3.0", "lodash.isobject": "~2.3.0", "lodash._releasearray": "~2.3.0", "lodash._slice": "~2.3.0"}, "_id": "lodash.merge@2.3.0", "dist": {"shasum": "1c88b2977e0fdcebe518b7099bb2c25028d81b31", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.3.0.tgz", "integrity": "sha512-+1C35fKpAdz6wRT2dIZ+STK0N4LHjM8JxDrxrhOoq1e3rPWcJNd249tefy9cvt/ywmqFnXfKyTr8DpDELlLzEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKWTLeJwSOVpO+NGs1tAkyB3X50zraOExmFdHhsbg7lgIhAM1POe0HcZdFFadiCXNrKZnnGj12vcPTzb/xxUKuPsV+"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.4.0": {"name": "lodash.merge", "version": "2.4.0", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.4.0", "lodash._basemerge": "~2.4.0", "lodash._getarray": "~2.4.0", "lodash.isobject": "~2.4.0", "lodash._releasearray": "~2.4.0", "lodash._slice": "~2.4.0"}, "_id": "lodash.merge@2.4.0", "dist": {"shasum": "984e6592f9f7ca9460eb5c3f7abfee36bfa493c8", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.4.0.tgz", "integrity": "sha512-zCyzx8C5mjVqcZzi8Erj/d1R0v+1W85AlB8fvBLIGbjGMYKa/rFO321UKShhO2V+upqcpXNpBpTUXxEOXRV49w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDG8HbF79DY0p2Yd4vssIaQZvvNmzvV1A2ikcnNRH7z7QIhANhTeJGpKMIG+3hwwPyb1F1ITjysFDW5RyeAmx+17n45"}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.4.1": {"name": "lodash.merge", "version": "2.4.1", "description": "The Lo-Dash function `_.merge` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._basecreatecallback": "~2.4.1", "lodash._basemerge": "~2.4.1", "lodash._getarray": "~2.4.1", "lodash.isobject": "~2.4.1", "lodash._releasearray": "~2.4.1", "lodash._slice": "~2.4.1"}, "_id": "lodash.merge@2.4.1", "dist": {"shasum": "4c9fe843f6d79e1b0522181bcdea04308867d21e", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.4.1.tgz", "integrity": "sha512-xalyWXgl3fJISOlUizOxFpJwG+XUj3dGbCgJ9VKuf66R37v2h8PRwSAzpUGhoHwLhInGGy86GM4ZX3Gl6ohYNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiCEdLdPuzUiCFOcBnHFeUv/+Idg8kR9YRsoIXz4WLWgIgHCdpFcTphV357wr2TWGT8GHC/C0g/SI7id9VAvY6dFQ="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "lodash.merge", "version": "3.0.0", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.0.0", "_shasum": "1a4426b89710b5df4be542c056231ee35c1a2a9f", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "1a4426b89710b5df4be542c056231ee35c1a2a9f", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.0.0.tgz", "integrity": "sha512-i4yPyje3nLV/1INrzXHzi+/XcIKGOI0ZVM3c8qeNtDCJHAqJAgeVu+Szg38XxIfbw4BaPq9nJeO26B1IxAJO5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmIjxpOtQJxX1JGz4q5uA69XWn5yXKxtll+Z3UJ8GL0wIgeHjwNMeIHPiN5pxGP+sgTOty/XWS0bzAdnL9lUNcBQ8="}]}, "directories": {}}, "3.0.1": {"name": "lodash.merge", "version": "3.0.1", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.0.1", "_shasum": "47fab13cce4fcc3c02dd6d661ae31dde042412b1", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "47fab13cce4fcc3c02dd6d661ae31dde042412b1", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.0.1.tgz", "integrity": "sha512-CosPZXadbZDUf88XMeGiZ82XbIWlYphoaLxdKPB0i5isTIVufE6gQSTeC5VXJ2a3RxA1G2Ykd50D6LfZMWhIkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFKTC4ZGo9ScBMx1m6UG5f5y2cEwrFhFUhEPiPyumA8KAiBQ79/drjCGwwBC56IpoTAYdcFDIXh9VkdKogIl1wlumA=="}]}, "directories": {}}, "3.0.2": {"name": "lodash.merge", "version": "3.0.2", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.0.2", "_shasum": "b13174fa192b57f76257f71ffba629126fd8038e", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "b13174fa192b57f76257f71ffba629126fd8038e", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.0.2.tgz", "integrity": "sha512-YX7mGJl+Ke1HbYjTABK55EVE2Wj3PSLpoqRALJa1AUkbb/oWFfW91jV3QXerwBk1+sJ/6akiAki3qw8X9Ys3IA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRpw3DG8qFRAfBGCpiXGesgSCDu7cJoDCTPMvbCsUd3AiEA/m5c3udFjD4Uppfej40D8ypIWpKN1G2J99r8eGVSBK0="}]}, "directories": {}}, "3.0.3": {"name": "lodash.merge", "version": "3.0.3", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.0.3", "_shasum": "2e4b468a07bd926bf9c2279263a3761227846463", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "2e4b468a07bd926bf9c2279263a3761227846463", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.0.3.tgz", "integrity": "sha512-8uDHb2kvPKC7NBv57hnpzQ90MVxaNOD1iPNSv9lwJUHjLjaUrGOnHlaxv9iIqae8JNG5jrZQGmVzjk3uYXYEhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDeQ93PKQSyBQeFCZoirTKnsCeMABcHFHchACCUBeNiqgIhAN6IFOlQz+EdEsyzB014a6pgdfTXOGO1/NBuLD/dl+g0"}]}, "directories": {}}, "3.1.0": {"name": "lodash.merge", "version": "3.1.0", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.keysin": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.1.0", "_shasum": "45110f78ba6baa860d367d746c656ee6d2d3703d", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "45110f78ba6baa860d367d746c656ee6d2d3703d", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.1.0.tgz", "integrity": "sha512-o9jrNAal0porBIjMfT+ePZu/TfNe6W+dPtEA4jo0QmC2oiTUZnRxBp7MF0OShyvhsPFt6cAwGR4MJscksIN2/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCufhm6XpGhYRXARAUbuE7Eo6SsNHITUX/ZYD+ugkp9rQIhAN8LDSYvjAaNqB858ljlU9dnsEqTomWa/p5jhYwjCQcv"}]}, "directories": {}}, "3.2.0": {"name": "lodash.merge", "version": "3.2.0", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.keysin": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.2.0", "_shasum": "52449ddf917be95b61f1f0a82e1f41a26efd5c22", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "52449ddf917be95b61f1f0a82e1f41a26efd5c22", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.2.0.tgz", "integrity": "sha512-mWRVj1hWtAblXVQ38WPIGe+DFMI/PuL5NKS7gZpRINhTd+RMod7kCn19rwNvTUcKyxB2cUM/czrP9Nw5YkLMZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG/Wxej19f2PQs86GPRhQfqgrmHIOS8TNH7t4Z5U+v+DAiEAp0PP35f8ah7hOm9ftB1+k/N0iRqPaVG8neyqbos1qxw="}]}, "directories": {}}, "3.2.1": {"name": "lodash.merge", "version": "3.2.1", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.keysin": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.2.1", "_shasum": "e5b1ee0cd232cfb03e98df74db139e6493c6ca3b", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "e5b1ee0cd232cfb03e98df74db139e6493c6ca3b", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.2.1.tgz", "integrity": "sha512-<PERSON><PERSON>+QR6RBQ55rIM8DcgCfTJPmnzum+vR3GuAkMi6kBSj2U1GzXG6k29O/GgqykQ6jz45n6k8iRUttNOg/O5w9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbbX+E9KRs9VnLEDnbJzzT1wDe6oWn3ca/OTLMV5OMSAIhALdH/7H7TBWl/4KyxxJxw8N7RBKUG3gxKMLsHQDg5KPQ"}]}, "directories": {}}, "3.3.0": {"name": "lodash.merge", "version": "3.3.0", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.keysin": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.3.0", "_shasum": "c0aaea517f71622abed9ffa0ca2a78548fa5d26f", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "c0aaea517f71622abed9ffa0ca2a78548fa5d26f", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.3.0.tgz", "integrity": "sha512-s2sYJkRxsj2LZv9hLerg8XNEIgsGW6uiyWzab9bVLocPl8xYRb3p5+rP9Q7yW+TA2xAJAt4dJw/ypdEsAB8huA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDP5/8+zGeeIWaGRChDgmtuZWytCzWH+f9JtdLXLBcJ5gIhAL/rVODl9lXkMl27KGChVEFdTOVnbRwio7PkK19q+WIT"}]}, "directories": {}}, "3.3.1": {"name": "lodash.merge", "version": "3.3.1", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.keysin": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.3.1", "_shasum": "5fb150502e491062edbe56d877aa062ecd1893e7", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "5fb150502e491062edbe56d877aa062ecd1893e7", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.3.1.tgz", "integrity": "sha512-uub/2lQl1fn5optYwQ0CAyhU5xJWvxf6b9FZaaDlrRWikR6dRtpziGF4o5S66AvXsOs0nMAejjPDGjZ7MOPSXQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoMamu0BepCsjH6Cs0yiTBjTNV8lVC8RpF3VdUEsEuSgIhAJHJoUq1eHx8GIE3fUWdpM6Fkteeqh4iptOMHkyej3aZ"}]}, "directories": {}}, "3.3.2": {"name": "lodash.merge", "version": "3.3.2", "description": "The modern build of lodash’s `_.merge` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraycopy": "^3.0.0", "lodash._arrayeach": "^3.0.0", "lodash._createassigner": "^3.0.0", "lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isplainobject": "^3.0.0", "lodash.istypedarray": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.keysin": "^3.0.0", "lodash.toplainobject": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@3.3.2", "_shasum": "0d90d93ed637b1878437bb3e21601260d7afe994", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "0d90d93ed637b1878437bb3e21601260d7afe994", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.3.2.tgz", "integrity": "sha512-ZgGZpRhWLjivGUbjtApZR4HyLv/UAyoYqESVYkK4aLBJVHRrbFpG+GNnE9JPijliME4LkKM0SFI/WyOiBiv1+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCB62DzKmqpPYb47JWjiyjTU5AYTvTOkSVN9jPiypb7RwIhAP9YewhrMMPOesZuAU5hZZpKFOd9mu7RYNUtWkv3WPG9"}]}, "directories": {}}, "4.0.0": {"name": "lodash.merge", "version": "4.0.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._stack": "^3.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.0.0", "_shasum": "d32b2a5e2563368d308e9692ea95cad43157a596", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d32b2a5e2563368d308e9692ea95cad43157a596", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.0.0.tgz", "integrity": "sha512-EFuV4EDyQIgAXsdNwrm65zTzzC1KjPYRXbmurVlOWJ1T7yak66ahF8qe/tCjDBVJRLgzmRARDGl5/fK3SmoOZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8tyZM+9j1akEeyF5QlrgDvOzBPmzc3lZlpIR3tub9VAIhAMONBVN/vbHbQ/mcsewXGUMG4oSEh5sqvy79lBSeU5yp"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "4.0.1": {"name": "lodash.merge", "version": "4.0.1", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.0.1", "_shasum": "d6a78df1facf39e8c1443e746084221919e3a644", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d6a78df1facf39e8c1443e746084221919e3a644", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.0.1.tgz", "integrity": "sha512-5BU/qlh/KjVqHhppbnYqmNeuflN0f37hzhvwj3a5KHEhgIjY8iJhtFSjoCd6adJvU6AyDmTspKtOnV7XCoIUkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGhTeUuKRz4zW0xjTReGJRUY8B/QC9kr8zkO4p3zgnCtAiEA5x+dThcnRqin9Oa3EUwsi92CKxrwKoXvOqBPKUHlOHY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "4.0.2": {"name": "lodash.merge", "version": "4.0.2", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.0.2", "_shasum": "e0559b7eb980147aa310d0df554e7f2543c8af96", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e0559b7eb980147aa310d0df554e7f2543c8af96", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.0.2.tgz", "integrity": "sha512-RmYJo7BxfpMkMb4y37GBkLkfkT117t8SVolAp2Hgr3B1JME91cwMsuAbU3YWLyORf+LMtmyCw+nerDpfsCCDgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2k8jSGd7mEBT8TfI66ncepXIhCYPKssvOlTqJrPSUQgIgY3DdxpCT02VUz/9SEQChGXblbGCLqVvadhFqthHDX5o="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "4.0.3": {"name": "lodash.merge", "version": "4.0.3", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.0.3", "_shasum": "1e8f4740112bbbaeabd75c93fb735693b6ad9ba6", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1e8f4740112bbbaeabd75c93fb735693b6ad9ba6", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.0.3.tgz", "integrity": "sha512-R/RE7rT62xmHVg3+nkmc0Hg9VDxaC8kJDEFDGVvjLY1kpbOMTcPdc285DegQEF4EatM8/a7U74JW6xNKdA8AjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCty5VBlSrcDHrNhDpCRKq/hktF/KigGDyCmLKm5MSZfAIhAPuojnrbHesEyRVZDqu0Wdso6wmPPgJSioEsqfdhXNnI"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "4.0.4": {"name": "lodash.merge", "version": "4.0.4", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.0.4", "_shasum": "5c83c4a7a9985ea87d8866a186c0c06c9af8dc6b", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5c83c4a7a9985ea87d8866a186c0c06c9af8dc6b", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.0.4.tgz", "integrity": "sha512-jp3g7qjnevT8F1qnrqTGSTTBjTzEFWF4+XzmYB/avHJTgdvGLsvSLtrxTV3mQ6Ujx/lSnIlH+PEhJN6jfabzhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICugKCsrWfUnVGKoyzsm8cGPtGe+BN0Km+qVR1ilzdTJAiEAsFfPNhn2WmAgay063NgpPulLlQRvIG+4EHFGvEKaMP0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.0.4.tgz_1454484587146_0.05031142942607403"}, "directories": {}}, "4.1.0": {"name": "lodash.merge", "version": "4.1.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._root": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.1.0", "_shasum": "9f9bb187309798e891efd5e6ff582ba5b434e515", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9f9bb187309798e891efd5e6ff582ba5b434e515", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.1.0.tgz", "integrity": "sha512-jSOKZXsbxwz3uqu1Q7OFcebFUbrmPms7jGaosHokRrb8XPTSnqJpwr54y7i2HjGJHrPsUg4eUxlitOsJz5l6AQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSIjY9vs80DNS9TPn1ct8EA/ESa64HKErOBBgGKX+AvgIgJ36XsXb1FpVq25ctviSf3gJi8EmTS1l2/GBt8rlM5FM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.1.0.tgz_1454898497330_0.7228132234886289"}, "directories": {}}, "4.1.1": {"name": "lodash.merge", "version": "4.1.1", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arrayeach": "^3.0.0", "lodash._basefor": "^3.0.0", "lodash._root": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isbuffer": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.1.1", "_shasum": "c00fce63799a5fb06cb67ff32b2f9c251176f27f", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c00fce63799a5fb06cb67ff32b2f9c251176f27f", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.1.1.tgz", "integrity": "sha512-qksQyqq7VmsaOdEcWdl7V7/aFo9H6it8WCoUVByjBgmc1j6P8j53UgZ41ZxLYLmoRuHA8qTCt1w2gw1lVYIaLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2tcixfs8F3Ip9ObLjp2EsK6eq9P2wggV3NN9fjmHtnwIgFGW3aCqaqid7sfw8CkfDMgPKqhzTtHpJW8SQ6Pz6rgU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.1.1.tgz_1455602473363_0.525275215273723"}, "directories": {}}, "4.2.0": {"name": "lodash.merge", "version": "4.2.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash._root": "^3.0.0", "lodash._stack": "^4.0.0", "lodash.isbuffer": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keys": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.2.0", "_shasum": "82a9d73ff6b321688f71f6ef57fa01f06c3777a5", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "82a9d73ff6b321688f71f6ef57fa01f06c3777a5", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.2.0.tgz", "integrity": "sha512-OyIfpfVlNASRChEAbQUhzyU0wFz5zpjunxaBX29lypbAdXhL4+pttGuDQKIRqhsjnAHP5U1B82gKUFP2hUY/3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4JGveerf+PviWrA/lE9rQtQs7iSvxMra/wbf0F4n97AIgAPFA9kmy8bCHb/YsdQsNYdGMmjTk1D/N9nkKAOThuVU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.2.0.tgz_1455615465871_0.32425284828059375"}, "directories": {}}, "4.3.0": {"name": "lodash.merge", "version": "4.3.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "^4.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.3.0", "_shasum": "26141565636e51ccc106ac3313dfe81f85655454", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "26141565636e51ccc106ac3313dfe81f85655454", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.3.0.tgz", "integrity": "sha512-VMlIjmD15aF2aIcPrO9oAah4JBSq/r7kq8+T/G8uTo4remO2BOChwgiZ8BpIv+cHc5l7Fw2tlpNVDkuyleuANg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6pB3ihtQQF/njpYzyYHnaPngVfez7XocAtU3eU0wnJgIgLbVygFx/+SsDNC8JJcXsG8U24HdFcKzTOYz9AZZJw60="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.3.0.tgz_1455700124464_0.9115130328573287"}, "directories": {}}, "4.3.1": {"name": "lodash.merge", "version": "4.3.1", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "^4.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.3.1", "_shasum": "79c64312050e2f72a2af0e133bdfcd9a5f610a28", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "79c64312050e2f72a2af0e133bdfcd9a5f610a28", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.3.1.tgz", "integrity": "sha512-0SLUO7q8N9aFd3zA7Yb1Ufm1KJ/wHQarYK9rYiluQ4izZa5zwTZV3CujFAkDjRnZMsuTK9VnRFPZIFrliWR92Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDKsTEupJ23Ynw/O2jBtu/uV/i5XGlLU4mYxa57FMMpZAiEAlIwRkiyP/3CFrROJssC24mEBemqi2MQJLxMwgxWVYwo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.3.1.tgz_1456330966152_0.15155523107387125"}, "directories": {}}, "4.3.2": {"name": "lodash.merge", "version": "4.3.2", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "^4.0.0", "lodash._stack": "^4.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.3.2", "_shasum": "de9ed940113ed1126e018658b1082b7f28e50729", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "de9ed940113ed1126e018658b1082b7f28e50729", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.3.2.tgz", "integrity": "sha512-zMD51XWC4+lSlmfGyqwBOkxd+/wWw7y44K39Jhob/+2uSU42e5kHLQ7K7SKsr8+4RBCLq7BLeTw3nyqqTW2AuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKskp7WpxXCTwACjo6kxkA1CdUxJCD0zD9vKKBGd5ACgIhAKhZz0PBbpiSnKQLV347L0YqYaSM8cncSLmy1takcMN4"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.3.2.tgz_1456896685796_0.5599874537438154"}, "directories": {}}, "4.3.3": {"name": "lodash.merge", "version": "4.3.3", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "~4.5.0", "lodash._stack": "~4.1.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.3.3", "_shasum": "0494a224256c6b8f8ffa3d1976ab3131ee1239dc", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0494a224256c6b8f8ffa3d1976ab3131ee1239dc", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.3.3.tgz", "integrity": "sha512-ySv2aZKQz4f3QwADf33zWAdEdmrB4xjC0+YvSPIXhxfTd6JB0U+hcMTF9pB8RkkeROeXYJexqEFxfR6PeHsR0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTKlJ6BABG48rG0/BlE0fCVojbQxyfxEESuBD+3v4TNAiAMJ7kUJ14ewl55MIICBGlclDA/Uhb7uMVV+pf61p+VhA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.3.3.tgz_1459310843899_0.5038228584453464"}, "directories": {}}, "4.3.4": {"name": "lodash.merge", "version": "4.3.4", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "~4.5.0", "lodash._stack": "~4.1.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.3.4", "_shasum": "f2cdb185b52f4694ea31ac992597b21a89a0f86c", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f2cdb185b52f4694ea31ac992597b21a89a0f86c", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.3.4.tgz", "integrity": "sha512-DgvJgmaAZMijHbdlyEtOWAZN5TSIU4ywVIPr4q8pMfcNTWCjaoPw8nyNI7CMWfUe8qtLL5yl/GDXK34kAaVhbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAH6zRISq5VnxeVt8/Z2edsasjb22SEem+hf6WytpLv9AiBjJOsn5l4n3EDHUCqb6/s/rR9zKUW/tRDNRjbqjAn6bg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.3.4.tgz_1459655461398_0.1855169297195971"}, "directories": {}}, "4.3.5": {"name": "lodash.merge", "version": "4.3.5", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "~4.5.0", "lodash._stack": "~4.1.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.3.5", "_shasum": "54e58c4f2083d9feccb1579a60f74b093d72ad17", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "54e58c4f2083d9feccb1579a60f74b093d72ad17", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.3.5.tgz", "integrity": "sha512-VOaeJaCs3/jZ7Sy3CIrOsy8yej1vUmFo7Xgb21rzp/HJNhghsnlIc+DcfekWJIwsfdkBdJp2CplncAQd54N37Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAv8tEEU6no9NOF+5WoQ4MbW99L0c+ufP+4ikL2Nw/GAIgWPWiDdIXFxdIZIpUGbn/asjr79lsbzeR4IE/UjRrxLc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.3.5.tgz_1460562005943_0.8410912856925279"}, "directories": {}}, "4.4.0": {"name": "lodash.merge", "version": "4.4.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseclone": "~4.5.0", "lodash._root": "~3.0.0", "lodash.isplainobject": "^4.0.0", "lodash.keysin": "^4.0.0", "lodash.rest": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.4.0", "_shasum": "2ec8922c6b09d076ef2abaebfe554616e798124a", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2ec8922c6b09d076ef2abaebfe554616e798124a", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.4.0.tgz", "integrity": "sha512-yjndNnTYo3g55ImfrT4Te3nTJMjn75FgWMy1d6MqBJoMBLFQjW4KYGW1zlcr0D2CXS/clfvxpGyxvB/K+/cy6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZuC/gGb7eSVbH9Pi/q8LUdJ6qhOyvRsiafIT2zl/k/AIgel7IO/SKYdxljyBrr8dwiKAsGam5fOMQJE3I0AQ4ejk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.4.0.tgz_1463062398643_0.8277419332880527"}, "directories": {}}, "4.5.0": {"name": "lodash.merge", "version": "4.5.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.5.0", "_shasum": "2e9e016e1a038e89a60415bd9a8458e5f7bf3ab4", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2e9e016e1a038e89a60415bd9a8458e5f7bf3ab4", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.5.0.tgz", "integrity": "sha512-L2Adys+2eAsfC/1JvywSu36BbMWyLZYKe4MuYqmLPvSKzVEsIAZYV6tpCTbwDvgpQlELAnOKQM2CUziex6oMrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAxyvKUT5JTVo+AhhHuf0Ij1kGlTn4fx88cNsxXyrxXZAiEAvS3h+Q9BGw5f5HHmHBOyAAFSkwu0Mtn94BQyth9WiA4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.5.0.tgz_1469458066897_0.10687315324321389"}, "directories": {}}, "4.5.1": {"name": "lodash.merge", "version": "4.5.1", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.5.1", "_shasum": "15b8d95a1692568323b7a4e3d60d50a04f51f4eb", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "6.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "15b8d95a1692568323b7a4e3d60d50a04f51f4eb", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.5.1.tgz", "integrity": "sha512-iN3AfqLUjJkGzpB2Gm9FlmcW9+ideWLK3dKdbIe8HPt1SNOpYKdhw5vBqbKEzpINJeg3vvfGnoJ672PUym+b/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfC1Lt2yTLgVing4FotcWYehdBYnVo1nK03R5YKh+kHQIhAOgpHKzOT+diCJYwEg3JaskgiG9Up/tpUgP5jHC9Zi0G"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.5.1.tgz_1469924718548_0.18094517500139773"}, "directories": {}}, "4.6.0": {"name": "lodash.merge", "version": "4.6.0", "description": "The lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.6.0", "_shasum": "69884ba144ac33fe699737a6086deffadd0f89c5", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "69884ba144ac33fe699737a6086deffadd0f89c5", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.0.tgz", "integrity": "sha512-yP6Me/9MwNfgOdUmMspV4xyjBVktgzlKDYLC9tSmldZGD7stwi6D+bbKihbMDLvFWnP9hr44lidKv5ETe82DKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDN6IKk/w3bQQrhLjyY4vcT80c001yDRLlMgZahxUlw7AIhAMX0Woc2QveSS2uChhzH3jXnzyLlk92bDYX57L+MH9uq"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.merge-4.6.0.tgz_1471110130016_0.44202496600337327"}, "directories": {}}, "4.6.1": {"name": "lodash.merge", "version": "4.6.1", "description": "The Lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.6.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-AOYza4+Hf5z1/0Hztxpm2/xiPZgi/cjMqdnKTUWTBSKchJlxXXuUSxCCl8rJlf4g6yww/j6mA8nC8Hw/EZWxKQ==", "shasum": "adc25d9cb99b9391c59624f379fbba60d7111d54", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrD7n8c9IicmCGX462JVjcXESMzat7IyPQwOpjsVRLCgIgCfAVrtHLUlujuP7leDV3cVUM4yXz+md4ite4bBN2xe8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash.merge-4.6.1.tgz_1517717256850_0.6092512272298336"}, "directories": {}}, "4.6.2": {"name": "lodash.merge", "version": "4.6.2", "description": "The Lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "merge"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.merge@4.6.2", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.2", "dist": {"integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "shasum": "558aa53b43b661e1925a0afdfa36a9a1085fe57a", "tarball": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "fileCount": 4, "unpackedSize": 54132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJS8eCRA9TVsSAnZWagAAToAP/1sdrluqqXFCoGvWetbt\nvJvVhMOM4OCio4jmPG1jZSVAjlvTw82cL2uNNRix0hEkEuRxE3wyzMNOuJl4\nlvHLuQojsM2kTa5hXqfmVZUmrnrKUi7xU6cnLY1Bb1neYUFhcQMO94JJk9JW\n6/+QmEPgpVdAYJZqDLsUwfolN4N0Q7O7k2ZwLK4n1JetIvdqyfxXYlYJmaFy\nFDp4C+VAwPGZKnk6e4NJ+ksagPqHbYewiZbgzKkCjo2bkgWqQQZBk9cnXVJR\nrNKpKC6Kpq8/vptA/bw+tnnxEmCjZFvuS+BBco/1OU8P5vKfzJazEUhh9BkZ\nza46qd93+hdm8DCDzwV9yEzJyzdXjqTDxsuCt+0cjTF8EviItE+d5phOWnrq\nhXDAv2rzwqh7XI9Njczx/DGvk9kM3pC9Lh/927veSUgYxYZFCi325CgG+kiI\n4B83Cv5yzUf+2b/ThOEQMyfNCU/kiS0RBt10jF1bYDliqzgmQn9qSixwbgSF\nitzY28CuBBFCNK7uocQcRKj0Y3pqtOLSwX0VokQuxoPcd5KjpXMzBM57L7M/\nOOhfRJ+bTgXs72pxnyIPvNG5wpe3JoBIxxHDhw9hvBc6EhM9b/BSXjP5fGWJ\nBI4e40U0QKTzYvaOZ2hRFaEmDVIHW4vzBDHJpHLvCzztQG4xVbPz64aR+9rG\nI7CT\r\n=fGFA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDccFp8fSRUTQ2hLOnCnpAXJa7RYFhOFAwQU/8+hzzb/wIgJirPWtB7PwAWMKKjROOhrcXRszm96KAwA7Dj059OSXU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lodash.merge_4.6.2_1562717981586_0.2700993888833618"}, "_hasShrinkwrap": false}}, "readme": "# lodash.merge v4.6.2\n\nThe [Lodash](https://lodash.com/) method `_.merge` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.merge\n```\n\nIn Node.js:\n```js\nvar merge = require('lodash.merge');\n```\n\nSee the [documentation](https://lodash.com/docs#merge) or [package source](https://github.com/lodash/lodash/blob/4.6.2-npm-packages/lodash.merge) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:32:54.853Z", "created": "2013-09-23T06:35:41.711Z", "2.0.0": "2013-09-23T07:39:18.264Z", "2.1.0": "2013-09-23T07:57:35.692Z", "2.2.0": "2013-09-29T22:10:18.384Z", "2.2.1": "2013-10-03T18:50:56.672Z", "2.3.0": "2013-11-11T16:48:16.104Z", "2.4.0": "2013-11-26T19:56:14.791Z", "2.4.1": "2013-12-03T17:15:32.369Z", "3.0.0": "2015-01-26T15:29:37.518Z", "3.0.1": "2015-01-30T10:05:36.966Z", "3.0.2": "2015-02-20T17:12:12.582Z", "3.0.3": "2015-03-09T05:08:39.991Z", "3.1.0": "2015-03-25T23:36:52.049Z", "3.2.0": "2015-04-16T16:32:42.583Z", "3.2.1": "2015-05-01T15:57:18.265Z", "3.3.0": "2015-05-19T19:53:41.377Z", "3.3.1": "2015-05-24T21:09:39.452Z", "3.3.2": "2015-06-30T15:22:30.756Z", "4.0.0": "2016-01-13T11:08:29.414Z", "4.0.1": "2016-01-13T22:20:34.220Z", "4.0.2": "2016-01-26T08:53:12.684Z", "4.0.3": "2016-01-30T08:18:53.328Z", "4.0.4": "2016-02-03T07:29:49.420Z", "4.1.0": "2016-02-08T02:28:18.296Z", "4.1.1": "2016-02-16T06:01:16.730Z", "4.2.0": "2016-02-16T09:37:48.140Z", "4.3.0": "2016-02-17T09:08:46.521Z", "4.3.1": "2016-02-24T16:22:46.806Z", "4.3.2": "2016-03-02T05:31:26.297Z", "4.3.3": "2016-03-30T04:07:24.407Z", "4.3.4": "2016-04-03T03:51:01.889Z", "4.3.5": "2016-04-13T15:40:08.865Z", "4.4.0": "2016-05-12T14:13:19.105Z", "4.5.0": "2016-07-25T14:47:50.393Z", "4.5.1": "2016-07-31T00:25:22.776Z", "4.6.0": "2016-08-13T17:42:12.975Z", "4.6.1": "2018-02-04T04:07:36.999Z", "4.6.2": "2019-07-10T00:19:41.667Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "merge"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"emiljohansson": true, "conantonakos": true, "alopezsanchez": true, "carlosvillademor": true, "jeremy_yang": true, "flumpus-dev": true}}