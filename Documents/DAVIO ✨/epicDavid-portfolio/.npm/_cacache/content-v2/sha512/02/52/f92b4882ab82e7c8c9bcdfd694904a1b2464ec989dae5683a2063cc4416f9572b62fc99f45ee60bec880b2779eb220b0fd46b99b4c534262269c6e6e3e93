{"_id": "@babel/plugin-proposal-class-properties", "_rev": "101-54ba1caf5a5d79c0050204e418c84037", "name": "@babel/plugin-proposal-class-properties", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "dist-tags": {"latest": "7.18.6", "esm": "7.21.4-esm.4"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.4", "@babel/plugin-syntax-class-properties": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LbNp3cg6QCRWUfjZheILHQ45h6nmna4kz+G/+z8Fdvp4Gyzsq+yqqkB7bFN3ah53g6Zb2ObPXO0gZ9jh+ljUNg==", "shasum": "5734abe7a5e4cd30238a51c3bdb5e48bb3266de0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDewgec1mnwFFzo/WZhjeuq0Mcxt/bQXWS/TMVbczBEjAIgdmN1b9XPTKRwiYeECLiitNN3ryYaZk/hRdW2DpTFMVc="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.4.tgz_1509388579752_0.08519175252877176"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.5": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.5", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.5", "@babel/plugin-syntax-class-properties": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L9o9NMNZUcuZpgr7Bh1koJjtXSUPKfpn9mldi3j6hbiwSDqZYmhsKHQ5mScfNfOMB61LK8omcaT3KB8ulFOrSA==", "shasum": "ec1ecbca7a5e958fbd16ed42c7d5636b6c1fb3c7", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDys8dHyXb/lpZMtgDMV8+Xs6zKTvib3ssZp2X9GDNxjwIgaeOOOOQxXP+BdwvrK2TMBhkevgCEaHxE2OzkpO/DaPM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.5.tgz_1509397076999_0.9730519903823733"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.31": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.31", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.31", "@babel/plugin-syntax-class-properties": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wLCvhSpXz6gIqjnshJ6aG4JrujJ//P6gOohD59YTfnQQiNjJnV8ZgSEZbIytf9RBkLs+A5au+dG/eBYmskv9hQ==", "shasum": "974291897593db94f46215efb61660973febc032", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6jKU+buRTgjAacj9aGEJPyhkeGuamFI5v5jpb7W0qMQIgZCorDxV2F3MJk8M0u9LBtRTQ56jKKlNcpWtSAAo/ekI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.31.tgz_1509739471252_0.5047349084634334"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.32": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.32", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.32", "@babel/plugin-syntax-class-properties": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-waZLn49DpreTFG1aeZhZQZlZjwltuk5cnZvz1Z2VjHEEQhzznRQqeOEwJLot9HjxvFnS7eQRCzu3QAzQv8FMRQ==", "shasum": "b4b51ef44f3ab7abeea474d664b826631d95bb3f", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFo3KXFojCmZpALelQq8CYmbHH7QzPRrSH7f1hozTJAFAiAxYfvCaEfgpEkLneyuVZBLoj3W4p0timXkFt64A+uQig=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.32.tgz_1510493633835_0.9666072262916714"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.33": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.33", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.33", "@babel/plugin-syntax-class-properties": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KXdYfp4xhscuaaaCFQM/AkVWm4c9MeVkHSUQ+TnrN9QtTFqPggwdXJyQt7wc6CwzQjYKQa2jj97aArpQbcFJQA==", "shasum": "0766f84f43217849ca7c1213e8a3beab279eea56", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuKfwdTKRIU42GK5D7qSz+5HuykeglRQY//m6SFeXr8QIgXwBneT+vtWZrzTOnUScFJ/xzJ1JBoEs0Ovw+tUj7Vng="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.33.tgz_1512138550674_0.803670403547585"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.34": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.34", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.34", "@babel/plugin-syntax-class-properties": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KL<PERSON>qA5aHR1Uz6phe2Mare8l5Zfogz6H/m+ukCEuAVpcCHR/2LZSqZCM24qYC2dnAwxvMRAl/QyUe8MclHhU3Dg==", "shasum": "3008cbe8d48f36a0610eebf78ef64dc3029a7f87", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfxM+Q5xVn+TCcN7n+1ytKFt8lMeyjsISUe/GOWkHsYgIgD6aCl/8080uVqMo6vg2ScLmSUnPHAnD5KLEYW+Pp9fU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.34.tgz_1512225607784_0.4549842136912048"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.35": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.35", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.35", "@babel/plugin-syntax-class-properties": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZKGT/xy3oTsvhpsgy2xw1PDofY5I7ZSJnJwDa+PQYyTalVKE09VJ9EMw5SAkKHT8Y+JqQRIpI1WkCyQFed3yJg==", "shasum": "f1c468110959115c7bfd0ba065148048542d06ce", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKTij9Bql6ojQGkjsxs8yI9dt9IYHTK9Fb9PFqwcUmDAiAob7QviQLBisP0SkJXwe9OLXteGe64pwNtz4Gv9/5eyQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.35.tgz_1513288101455_0.4471761966124177"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.36": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.36", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.36", "@babel/plugin-syntax-class-properties": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HqnbDiYiACRv91BxFe5pmCSK7+2mmYtLoOHRsCiezB5LL/A9OEnZx5YAxZZQq9+rPWplIxRx/xlP0SomG3djfw==", "shasum": "be236de98ff7ccdf650b7282e8e1f5bda808017d", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGHKaEUKRSSW1I1Vc+/Eunz9+cSeBzVsbSDB6HqVIBTbAiBOmDN3Hh8wE5yiqPzIzUcBuppkYVfGgxC1lD6sQGWQsw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.36.tgz_1514228730293_0.5881085412111133"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.37": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.37", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.37", "@babel/plugin-syntax-class-properties": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-t4uZY/k1W8B95muDtbA8NcSTkGNkix9zlXIeVfrqDqTCiYE+yBEYD1gtsX2QW3VRbEjUURjAXZuo091P8XdHCQ==", "shasum": "0991e720ed24fa452b02e86183aca8e890a8d2cf", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/arKifWLuSt7xcoU7oeyFCt+Vu1mj9V/Gt2HekzO9RAiEA01v7w7xAnbFcbXHa6N59C5nMh5pYGiv7lWSxvjG9byc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.37.tgz_1515427416163_0.2477794464211911"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.38": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.38", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.38", "@babel/plugin-syntax-class-properties": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-h286XHfkTja//3PDc8YR8ODRXP31JkEhm+j3AKgRUwKtZ1pBs7mXMdgOyX5SSEywDv7nr9IvYbapYiU3vOZv6A==", "shasum": "3cfa2fdedf7017ee14472d6859c3fb98654c0f22", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvP7lvzS6VOpIFoJI/ShCAtzQhVjVHO5+lovVsuwdpowIhAL58FH4dTNaNEoqkzeiIxbpC/JnSTqcg09oTb0OCrXwV"}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.38.tgz_1516206754411_0.32670489815063775"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.39": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.39", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.39", "@babel/plugin-syntax-class-properties": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NThvvq2agi3RH2MG9AyVjoKdWv8A9fO+4p5l7WGoLpgoSG/cGgjNLFmJiuYekp7Bh8PyKgZNwnqos5I3N5ni+Q==", "shasum": "251bf1cdbaa2d533897b57d26ac39cf52e25c734", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE1kZnTrOQ6ZH6AzcdKZqfJymyZLmYzwmMfMKraqrhtEAiAkpM+nLzrJyBriHpPdPt+1774cgMFMdNcv4jVzjLNiIw=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties-7.0.0-beta.39.tgz_1517344120546_0.34126326092518866"}, "directories": {}, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.40": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.40", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.40", "@babel/plugin-syntax-class-properties": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Sg9dQw/4TpYKdTO5wx+AxxICXEraBbdhDUFl9tJfJYgz4grJm+UR2xMXbBO6fGRyBlFLMqQQQcY7Olk5iN6qNA==", "shasum": "ee0549729e9f44603efa17523b459ea3021458dc", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 11898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDFsUC4qZauf09y/MCRsWcEAxzVQHlBaILfvz6ySRLS7AiBTheXgQgTIjsdm3SOu5NQ2lws31tg2UmhxWbkP3V0P6A=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.40_1518453752799_0.2203479791555183"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.41": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.41", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-syntax-class-properties": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-paqkztgeIHcz4QxyPJTiRHRImAa2OpGqK2dVisalcVYjQXqSqrhQQIkm7YvM8UoYskxNDqNsxNylfTjm5GAPHQ==", "shasum": "b02d89d773239abe043e3c9424155635f854ca18", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 12106, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOaPULTvm0k7aQIIRGByqaAKbA9/yt5nRScUlkc5kENAIhANR3WJirEawX8rTLc+/rwmJIiVCGA5xkieKwpmTZvLq5"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.41_1521044804283_0.18125286754248737"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.42": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.42", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-syntax-class-properties": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VXRmo/t2nAVciXxEgPTAfcUBXj0UXNPIvX2aj3lzHL51N+uh+rtgsIF0nuZwGE4u89WvBDH66yjAu60Ra674kw==", "shasum": "2cd29050ab997567071b65896f92afc08a620748", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 11957, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDY4+b5gbHHTxJiZ2SRhp/xx2eMXYZ7gwwrVrgA0CoPAiBO075pTX4K0/z/0ftZqqUQ6KnBeVCrsT0tivbWjrh0tg=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.42_1521147121417_0.762505592376687"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.43": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.43", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-syntax-class-properties": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-N/YeJM642QfiEaKZob/CEyWTQq+qMFzA5VFES1obmRPZF2/pTFxAkY6FqhDhbzkzhrRKBCYGBbwoEf88umH6Mg==", "shasum": "b6fe58584e95ac94adc41c80c431dc44e6e93962", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 11144, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOeo+xpk/l9k/fr/jKJVuLVA899lnmRDNUIPfdRl8HkAIgUsWZYm3fIwQTEjBOKP/1iNOmSLLP3BMT0x5AhO6R4nc="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.43_1522687731852_0.03745083740458499"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.44": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.44", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-syntax-class-properties": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IQ9kUIPg4iKDPkVKHvN8EAiRa5qH6+fUqzWPMtsDzmXD2Rpdj0FtR/I0rJkqcutYxneNwfDJFsgTvmFixClSww==", "shasum": "aff9192a883b41fdf1c73026b9105c92e931c55e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 12526, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH73sRXv/3nlqB6HLwC376D60Srx7j67FBmxJkYXjxItAiEAj+fvprwbJ7OnytE/WQ4bBiCdND6+YrRTqwKJutgetgc="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.44_1522707631540_0.9050120752766495"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.45": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.45", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-replace-supers": "7.0.0-beta.45", "@babel/plugin-syntax-class-properties": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Wwe6g+8k7rakRfvKv9mnCV7B4LRtHooFM+x12lie0lg3ygPnhfXRg0Oom4Y2L5fu+ahYQns4um145PbLVJdvJw==", "shasum": "27924fce506ec86a2298d6d8e360a8f0ccb756af", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 12266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3eCRA9TVsSAnZWagAABAoP/RxMjlqo9FxN+kH06p5H\nWO/xaEh5YlZ9Mth0aRrCx8FOJeUUiU5N9wdEebzshN8WpZ495FrG3rlA3+vu\nxokHhAC1atn64541/AldxE0h4OZnSAd4Iq1BDLhP4qEwncUso65uMsNE+pag\nWcwEjDOr3x6mFvOPFRAsOv3oQuiK58HnjmjysXflHp89zRmxCgkG751zlLUP\nZDElBEp1Y4kZ8+G5sHC7w/erooj+5vhz3FbN5b1uJN85iaFMnnbkNW7qOes/\nS4YD+WyjhYi9BL0ibuwcwiED+x970RKV/xdHH87U3F6SsYiuJnmcIulo7wrJ\nCvB5mgCQGiyIiu4UfKoEbcQ8fkfwvV3w+qIx47PNIrvRiUpO5K2FrEIONvhE\nDTqcGDriWuymWKr9xcoCG6DvHuZO+xjBDxbcHpsiS0JgTcr6OOxkIDIc4fbO\nByChVj91yD6k4NmRqPUPGfBxYryje2NuBtpS3IjtdxwRFcTR2WBfGfWLAV3S\nNNJPH20+jUgQJRX9cNLlPwjLK7LG0j11e7P58VGbC5/vNOjd6dqvAR3qFURP\nzKjgSUvRievoKsXKEdOPd2mRLYF14Ntf97yiBzS4FW8V82CjCIXBX8UIVAIJ\nV88qOB32DZ0eEW6Pn1PegMElVnq3zzco9Gbx0VORcB/Lb93EmvsIVZpn52/d\nlpEw\r\n=+RZ2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICeAdAFRt+rlysGvoEHyeZtOk00pYI8CAF87wZbuqrLTAiAFJzp9K9FZzSxtfSQyy9h9QHO0LPTYAPknOBAKqaEQeA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.45_1524448733705_0.709579142429017"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.46": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.46", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-replace-supers": "7.0.0-beta.46", "@babel/plugin-syntax-class-properties": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kWp3<PERSON><PERSON>ibdSeSEvEQWcEcs345KPQYT39uM2edFS78NH3Gu6O9mBcnXh5E2BJ1sbE+jJ6jYPOZz4BK/LR7BiF0jA==", "shasum": "1c505f8df3a312beb41c88d74209d5b6d537fa3d", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 12266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WICCRA9TVsSAnZWagAAnMYP/23MZm6gHMyXh6nnU4NW\n964HbeCaEBlEoiiog2KQLJycr66Vcnfy8YKDLtz6xZ6IZxFGfKgZvYA7kCPT\n0I94jsptJjhcJnnJPUlALsWUsDcilnsTLBxtr7o+a5Wgp344YzI5Tpg/A2ZV\nCMM1xoaBZIEiVFxEL/syAJpv9J/6wVTzWpLZMIpKmAHbYe72wgUb9r356V/y\n0/Bxc0CF65v1frEHbgSd2nD08VwDFdmIdcRXFJO6ypyNRy5Dl/4JjytP283X\nZsIwLQ5v+ZtCkZCVZrEvFNHHkQBv/gEOZuuDLDv86u0JOCFFzVNVvAI0Cnet\nw7NsbQxoar/uc1s+BNFzUnxhKnFHaV0qeP3T1P/YpqI/5QtghzbPC4DZwhgt\nsDMbcDRb5lYny2REgFoQPfSFUjZa/IyhG0cb4cbh1GShW82n4YYoC13qof2K\ngRh5S0tozJx7Sigsed4zUCD5E4zrZcRbksqcb3tIps/6P5+5WdCJeg/ag2Lb\nIvhHYQbGrj7ZrgsiXqsMoapCRoaSPx0QKpHHp8oAW82/QI1gEZepQhVwPbx2\n9KzPbRF1qPuMrknLYxbhGKhlX5J21+C9tV4nwwmEfIV7iwlHPXfQSv4aeltD\n1L2nyTAuC2HbdUcJ/Bq7m2UnCUIwotBLHGKAB4xGkppPovwdJLfVD5tzGfb2\nkIIM\r\n=NXOy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYy31S/d/0fl1vMTR3Z7RWvStYnxzs4Eqvropw4xghZAIgRF2GAEUI/5P2RuCBrFWmIr1hc0+4GxN6Xk8pMrLl8kM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.46_1524457986172_0.9564932547651384"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.47": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.47", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-replace-supers": "7.0.0-beta.47", "@babel/plugin-syntax-class-properties": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6yuCiF+ZZHPLgAa+0a6/teNeAMsWqY6AVtZA4NhCWnwP4OH0JrRaY7rwvFCJSqNGurf8rF65W9IucM/l0+HOCg==", "shasum": "08c1a1dfc92d0f5c37b39096c6fb883e1ca4b0f5", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 11470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+ic/CRA9TVsSAnZWagAA87kP/iqp2gWZTXtsooIX4ZxU\nb/g+eA8//twMKhM4gOYp6kTaWqiL/L7FxVOdgVlF4rVNntsCdsOxSaEB1b2r\n+sXXAfzVz6aDBfCGouKdvjyF5tgswd5Z+/5McwUTnlff+es29TZNyKfqKnxM\nVF62xRNrlf2q8wTr0H0FVBwSvpZp6WwLcZ2UTKyAKOdRgPgQu6mW5+EJZgxm\noODVbPSh9tvsw4opLmHqRaDGvNniLe+XdmY1PbGMgMjualFpOjgs20U12XZx\nT2sQXZ1ROkB+xDkNgSs6y8vv8qFdrYxlPYMpK49eVoPeDwK7YBFZ64hqcLg9\nqw+hLMOLQ6f4TB2YUSXLEjIPhSheP9GAznTn5e/5uAtBsWtq3l76OwX8Z+ch\nJump8UBMRYmErvNQrHom0KQMDOHX8XYSbG1Ub2RmS6RHapopHOKzqHuuhTUT\nqE5RYOmfD4fNkVUABvJslKpjyOGQ71VdFZoRbKg9alap9OQln+O4shQsEkdP\nOU721pjF7i9WKv8tC5S1sN0CCjfitdjPKLdgYlCSUHevcT+631lFliwOJLQQ\n4AlPFbUN4SpA4WL5GITuJjmIviTnqJFpsN/FzvSo+QSdNVikNSu7LHBH2pYv\nWPpyT0ZQ6eB4CO95BvxAMCChNexWeTLfi7FA0jbpolQLXS53yv7crPrqLGXh\ncZyG\r\n=6Lv+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDSXLWJrWOwVqoRIInbJxIT3V6vG/a1Q45NyhNEW1jsFAiAcrN+o0xDOwbE13yoTUZWVTZQ4mSb2dTulleDO4Q9RIg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.47_1526343486603_0.05867927682302776"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.48": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.48", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.48", "@babel/helper-member-expression-to-functions": "7.0.0-beta.48", "@babel/helper-optimise-call-expression": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-replace-supers": "7.0.0-beta.48", "@babel/plugin-syntax-class-properties": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vtZ2QeYKDW8VvX42aPw+sP9AyoZRk2V2DFOdbucxjuy6Gi9o2q66ioeOnIdrUnVxmHes4IFIuGqDS+XUT7h2Mw==", "shasum": "ca2d5b2e53d1563140f96c725dfdd78f7e12ab6a", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 16718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGPCRA9TVsSAnZWagAAATQP/jrA0BMus9TS6koLjm8n\nB6rMSbSri57dF//dWS98KXHh4LZJr3ReHzhejqBZJIxMl04WzRAT2teJ6Ct8\nZW25gS6tDAr+Oz6Y2YzrC70IMjmhZqznuIjdvS+L6jdtMm/sZydbnAH6U3bf\nIPUIVE2rQeYlOpg/v0zHv5UD/K1U9ceWxknsPgzp+4u/OdEVBJwnb085OQOv\nqhR9eVLDeWa/+qk2EOMXk0b5Prlpli07m+0JFht9Z1Bx+Ci60fvZOYvniy13\nVINlu/Vsg5fDAHvF2mSKHUkjki2QQ25kQWaVgMVBNBPJY70sKxyYqfj4fC8A\nv73XzKDrxFboYP/4HQ6lqz45C+f+8NS2LV3+McXgAloBrMuHoUEbUcr3b7r2\nORZ6xYV+MQlkZsUYFMJAuk5ZiwBWXJ5Hnhkxf3QJqkx3Ttx4DqWhzVkWFlPX\nDy9kuozHY6DtZ34+CXsWqq1hhKKRbJieIeLELua9IQmjcONlgWcogzoToBXx\nZpaP6rBIkzYP7YFwq7/EppZ4opq3wf/rotuG1JLDypTTfKP0KeLtJGKVOqPh\nPBiUgHtzYHh44gzADdrBaMXFyIdtWUUv01UtX9+Ji0JDY2ZVX7SBpup5DVdT\ngx8ZTb+XDnLCaWaOY+yHBqMAf/CNRuBmSAjkdOJUtbuJFnFORfYNqmtajOf7\n7Ug9\r\n=/yED\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAaQZUM3BS4VrRa+6efJDNbU/QlZmaoMXabJLLFTo8CgAiBxHXrVjdhi6SMLdTBJW5LyPU5BUKcYFeB6yMJSKZOGHg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.48_1527189902877_0.8799670298570375"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.49": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.49", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.49", "@babel/helper-member-expression-to-functions": "7.0.0-beta.49", "@babel/helper-optimise-call-expression": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-replace-supers": "7.0.0-beta.49", "@babel/plugin-syntax-class-properties": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.49", "scripts": {}, "_shasum": "527e90af75d23fd5e3bae1a218dc0a6d9236b5f1", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "527e90af75d23fd5e3bae1a218dc0a6d9236b5f1", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 16733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQnCRA9TVsSAnZWagAA2WgP/0Y0T6n66eGvCLNuF/RJ\nBPiXyX3z4iSzZTHn3/BjCRM01Nma6oRKEq+tpDh/yhEXN3J9A/rwPuIpgDv6\nS8v45wlJS2RHQANECJC0mpufbapu8GBi/P8rL4ZFw9g303zg5UD5aNcyHb3X\n0z+ZBon5o764UYOHg8+Ly+XgYMiC6nUhCAR8w+QLcVQ/jpYZ5f8Ha9znJZFP\nNcq/EzGTgdA+vRMfjEpuB3ocAUYNt+1U1LSxL2cKFnMjwzilign2L31urW0j\ncBRqQM2+L8MCdgaqJWRbLfWuXEWtPZMzWdaxgYoDqzGA3T+fP2sqglCVYuCw\nD9t+k6py6s9bVM/4FfX7zmqRIEP0XJSZ8EWy7jiubTBxkj3o/FZGQpX8Up+l\nK4ld2ODRGz8XcFDY4cwgCAHXsgvjZl35Nb6J614UbGNMmTNqOdTkrShyTr6O\niV7cuVWV26t6sHl6084oQuDTv9TU8kF7f5ihLhyLxK002wN2x4HTjRTOrBaR\nnnGm+FC4nlmIEtTEWuclVVVz4GJ+gvP4u3faB0V7iNxm8P4L3bRo61iN648A\nKBZ0lGFo4aM18aSihofxPFJlLl5fhvTXZMpCewIfv1SCP4YiRHcmP0cFTMU7\ne7hDRKaC14z8VTK70EkuskDLRGhbAYAIqc2e3SQkxlNv1NguBQ/TBWHwbyS1\nx+AU\r\n=m1GG\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-g/biyrrzWRB7yD/9oc4/dPm6gjpnUY+yxibS/qmisrM3IE4wWWqIzOrSo/DHnKYXsBaI4iOeKyT+UZzRmkVKbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdxPThznHaiIdBeSmMBphEE49uAHpaSxFMAkk7rzK3kAIhAPHhHv2mfPl/W7hlVeegfkor1A5reGHyLmSXojBkZZ7l"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.49_1527264294582_0.21726878880835798"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.50": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.50", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.50", "@babel/helper-member-expression-to-functions": "7.0.0-beta.50", "@babel/helper-optimise-call-expression": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-replace-supers": "7.0.0-beta.50", "@babel/plugin-syntax-class-properties": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.50", "dist": {"shasum": "4ce94a85903ad2a0aca6377a147167a2f1666538", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 14455, "integrity": "sha512-wJxQ3gUs17sF56W2EzfrWAWblOIG8qD7MEVyiZxPr8mvxWaY9DQce6iRUStlFyrQ9tR1kO+oi4MAcZlw9eTf3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNAQIdfZMQLM6sPIFEK24g6m+rASr9jeuzKSBnnriQ+gIgLMUFBLSbPNRFW6neCIyMypmV8VzlPkftk6CmvvTVmys="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.50_1528832898376_0.6158824833877181"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.51": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.51", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.51", "@babel/helper-member-expression-to-functions": "7.0.0-beta.51", "@babel/helper-optimise-call-expression": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-replace-supers": "7.0.0-beta.51", "@babel/plugin-syntax-class-properties": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.51", "dist": {"shasum": "b5c662f862a30ace94fc48477837b1d255fa38df", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 14469, "integrity": "sha512-xZORRLBxOhVVtcRtTC5RD32PzwJnN46LCUsHqSAvJ4bKzjxG00kygOjC0ORQAgvSOMaHXJz2eddoz5fqVibkjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBlZmxqKtFm+bxhU2/deSYDAO1vEITYOR24NDkEKiwp5AiBsoj8ZK1U+SyCbdTSnIpYmzMat+M/Y7GzYzwksFQv0wg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.51_1528838459728_0.6614984021995183"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.52": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.52", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.52", "@babel/helper-member-expression-to-functions": "7.0.0-beta.52", "@babel/helper-optimise-call-expression": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-replace-supers": "7.0.0-beta.52", "@babel/plugin-syntax-class-properties": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.52", "dist": {"shasum": "8cfca275fb4b6a462db9202970458cb3874fca7b", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 14468, "integrity": "sha512-1bdOph9BsQTMZPzFKJEPiHMLQ4M4APDbsAacwZP5EYkN6vVuhr5u1MIwOc65akAdrKhwFPPo+zMdE0kSjz9dsQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBX/n3wxMCyth0gGTIVCvqHmOD2PcS/rR6M2H9tVW7Y3AiEA4/g+rMn++NAIZ3mFAQKD4QDhqXaVnbP7p2fyJEL2qJU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.52_1530838791152_0.4733109197486065"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.53": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.53", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.53", "@babel/helper-member-expression-to-functions": "7.0.0-beta.53", "@babel/helper-optimise-call-expression": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-replace-supers": "7.0.0-beta.53", "@babel/plugin-syntax-class-properties": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.53", "dist": {"shasum": "268bb8c0d50a8ea91e5c81e03ad0cd3cc9cdc957", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 14468, "integrity": "sha512-/UBD+fQR/E5W57hakcHfdsH9+R4a9LzKfvFYlloIPje1WmD0XGII7AMO3lga8DfgduWEuDETkRUXk6HUJqdjSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCY8+U6sc+xYUtIH3gEnyW2lUGgJkBJZrfE+xTyhmOHvAIgENgxOMxPdGqUzoyeiaAn1G/k3BbOyBupbxjv9wqSBtk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.53_1531316454293_0.3145946086894533"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.54": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.54", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.54", "@babel/helper-member-expression-to-functions": "7.0.0-beta.54", "@babel/helper-optimise-call-expression": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-replace-supers": "7.0.0-beta.54", "@babel/plugin-syntax-class-properties": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.54", "dist": {"shasum": "5953f0499c1e69e732d33a550bce8799aa6b76f3", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 14468, "integrity": "sha512-FhEl5p2E43ybqlxaN/Pf3Vs2nvZhz7vWIbGMgxl03aChEoUTVCElmsDURXgTb+ps6iJe9a2G/9LpW8kr+HWg/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6bynPn21t9FMW0kbD17Bynbq/qDp2PYNEKjIa+d7ePQIhALVvrNovEFCoK07rU4nfor6fO7dPqcVJ+yQpM4yr6vmv"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.54_1531764031505_0.20282300213339788"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.55": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.55", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.55", "@babel/helper-member-expression-to-functions": "7.0.0-beta.55", "@babel/helper-optimise-call-expression": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-replace-supers": "7.0.0-beta.55", "@babel/plugin-syntax-class-properties": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.55", "dist": {"shasum": "d90792b5b4279e708f7c2a30bdad489dc398c57f", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 14663, "integrity": "sha512-fTY7QRCZHuYvJ+REbgTgmoBEXaIl4B6mlBUQE+O6lkopVeTe6fGHkQUDsP4zxiUzfQUh46R7Bx0dLHzRC8mm/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEUT1vaq1FcabTg1FjKJfRZlwbFb9JI5HFhcDV3o3YkmAiA7bHC6gWJ9KrQa+WOAjLKyowLqh6RYq23vsrKAFSQgtA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.55_1532815715757_0.7042447865584862"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-beta.56": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-beta.56", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-beta.56", "@babel/helper-member-expression-to-functions": "7.0.0-beta.56", "@babel/helper-optimise-call-expression": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-replace-supers": "7.0.0-beta.56", "@babel/plugin-syntax-class-properties": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-beta.56", "dist": {"shasum": "cfa5adf4aef4480ef3de3e8896f1524804cdf3d0", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-beta.56.tgz", "integrity": "sha512-OMfd8vJp0lO8cc4RtdtkTJ7nXP0lZlSYLwuX8HPYwd1BIZ00e92z4xzcVGZZCF9BW+Rl9flM9OYwjmlZkcvxyA==", "fileCount": 5, "unpackedSize": 14663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPy2CRA9TVsSAnZWagAAHIMP/3G4x8sApD/tEYVIrYjg\nLIqiEWfo5oEozKiemHoZdfj85Q2pMF3RcH8o/1BgK4ipG7FwsMPjAcK8XVWe\nVgxK0rGVNF4CY82Jqozp3i5+eZYbcnldo66wEaMNLh5rxGAiimpKCkdf/j2Y\nxhP0vnhqAvF8RVWxKr4NFh8GPbgEs3UeXDEpU9S7qh1EaKCK3apCz8tR30sD\nXNKVLhllnjwwfddiY9zHrqU6muak7ZzDsRajdNv4oemCNEKIfrQDKmetn4Ab\nASlLz1z5eFM1Xyw49aA88GyNP2KcxhqTb+AFolBaRm7X47bPwcSTqLKoT7KG\ngSoqQPfmfJMsFeCoyF6PrJgiuNXMJLCaf7HxxWlrcRfOCc3KAwvNV54vVliu\nfqzqCrGm29kvTlc9h48PgLxqzJWBVeUmmLPSDuHpn6Wd/yXNJ32z9OQiteXI\nPL2Mjv7sss8VJMJoju/fwPniW77yqRjN49rNO4QaPxVrbm+dIMf0cz+0LL2g\n+lFYYCjTpmHLAgeqUrtgjb3VwNaL8FcPSummVlFV9y4+q6fur3cMXZ2sf/s3\nioQ85ccOMMjSdTcuZV7hsovtzcKDdqwiZ+Pnu4eiMDeV6oUVtPHGxaChbumY\nul24g/RirRKyIDIwQuoHlmb63nfd3Y9Ph1Q1v5FXZ/dRqzK9VlKcBen8wDqI\nGog9\r\n=tvKd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBIbFIAYFU9yzxQQbfTCKOXobv9ZzP4IEPwYaW091FQgIhALHNyl/e/gzDu8u0ZcxZWkFIPgzljuk8OzHY2wPS7LWn"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-beta.56_1533344950059_0.44826906713997183"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-rc.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-rc.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-rc.0", "@babel/helper-member-expression-to-functions": "7.0.0-rc.0", "@babel/helper-optimise-call-expression": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-replace-supers": "7.0.0-rc.0", "@babel/plugin-syntax-class-properties": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-rc.0", "dist": {"shasum": "b73cac89b9a029365f857c88a1ec814816e56d83", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-rc.0.tgz", "integrity": "sha512-Gmu6hfcfl+oO2Ksr+NTSmcrk/yDkZ9hr6EJU/vWn3pGF8DwxIIE/wHt3AQ4Jt/xl+zAEgD5lmfCxnWnmcG9fkQ==", "fileCount": 5, "unpackedSize": 14636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUcCRA9TVsSAnZWagAAVEQP/jiOv+5arYeMel0W6atx\nXaP9p/OiL8tcwxSDcmV9v9wW6qYM08pts8VSurMsatAFdTsHE1Mtu3xziGDN\nh/1tMZHl2pS+WHNNvj4feNxONSHKtK2D3/oLzcvUy06+x07EKAlCq/OJ+/i1\n0PgK4/SVdKv6gr5kld8wugjjXgeS0nMNKW8kvUyXAlvuJE5af1M8kLnzcm2V\nKP4L3a0jk/CO8Pgszzchb4YeCYrbrzPi7FoYts0WnrW+3DyUKhMFRorRo8gA\ndW4EnDq66ak+BJqE/pZT4POqyJfjxRoPAX7TN1JdNed7l7jNG1SuuLzgskqR\nP7ZFmEtYohHw951vuh8FrYBjtGQE8ZJIvntdEgVMsss03Son8t7ZuYW+dPlf\nCyUXsGzlQdELm1bfPMatvd6+t9ASWvUi/SdIi4ljmJH0ZAKjakvZt0WuwdW2\nfSwRJSro7JAdaPofplv+jQ65cGSDubzjtvvUFYo5af6kP99hBFuhK2KD0Ibz\nggo55Z4zSakAztqOy7+ujoU/mThLvG3YArML88RTbqQGxvQ2pMf5c59Dejhp\nUbeuQctCDhRL8MzM5do9ZqQVQ1/I+d+6sei91nZOVcIEi6mH4s03PAS7rmto\n4iGz5wOauHjbNo180V6ox1QJUqgllXImALtZvRBztFBaXTFSUi7MG+JR/w4R\nnl8w\r\n=fFOx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1bnKCehEr6GfTmdIZ02IBQgZJXJoN9nEYpkpEBogClgIhAKsDs+ktx0su382xnaRXjQGetBnviGpPWZCiXWaXYgjE"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-rc.0_1533830427795_0.01716487088105545"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-rc.1": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-rc.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-rc.1", "@babel/helper-member-expression-to-functions": "7.0.0-rc.1", "@babel/helper-optimise-call-expression": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-replace-supers": "7.0.0-rc.1", "@babel/plugin-syntax-class-properties": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-rc.1", "dist": {"shasum": "88b3d3b257b9ed53fae50b13103e4c3c725e704e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-rc.1.tgz", "integrity": "sha512-Y3yrKbLk8xSz8QHTDbrixJBVgiysAukRml/HqrXLsULMkHth3I3K/QBXEVxiJyawBUaDZvENQJ+H8CTv8y0aSw==", "fileCount": 5, "unpackedSize": 14617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+gCRA9TVsSAnZWagAAN4kQAKGZa0o4F3lm/FZ41KX1\nO01IV93LVhrSv+bl2FoqjfXAwr3o8E3r3Ld53rtA77MlCbl6jSJhM3Pm7KBR\n4gsQyb02moOzKDP+cSreT4kqykiQhsR6oHH6H7LTx2LqjjLmZ6/ZbV7AHdQi\noKh+6cvywOBtMsOJt+8fLdU9sysojLWVdzHi0J+a4/T7Jbi2c+P8gnW2QWOd\ne1KUhgTItJaknjc9vvcdVMCEU3KNaH9OVYXSlv0h4pswm/Agp30TDS8PCzZ2\nneHYpxS/JBivW24jisPhGIYBElAsvLPfjr1CQVmWqqva42ghccdHyYA3G0DI\nU/CQ2sNZ/t34sGImw0XlbBeWm/7GtcnQpz2AaF743eQXa7+pkcdlrP/ive+B\nE/K71Qet9V8J+5iIyiGTvbTCaRF6mjYJIjCMOF6rcsTOs7YogNHS1iBU7YW7\nceXpX+q33Hz1faBdvJTS4zPxQSY4EMzLqzsb32IoA8AAINfJtCWyPICDyIDZ\nJa2g4dE9vTgolx8tC7e0usfDQ7AudOuaaDTc9Lz0bzHJIYXE5xCY/RJQH7Nm\ncHZ+O59T6eXj//xQBcROSS0IVtY3GmlG9yH1ExuRNnkR8gQ2N46tSjeN0pqv\nOYFvueUlUwy0h/SaAdjAv6t2wZmDCH0Wj6H8F4DUGoiZpHRLznTkgjV/Hvfd\nxTcW\r\n=j+PM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBQcOCcin4R59tvz7pEzTDpwGVNsts6BbT7x92IcDaEPAiBBHH7rJpO07P1nVZ+n6EdHBYY5BWEhcln0wqauZtVf/w=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-rc.1_1533845408283_0.5098399665570621"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-rc.2": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-rc.2", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-rc.2", "@babel/helper-member-expression-to-functions": "7.0.0-rc.2", "@babel/helper-optimise-call-expression": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-replace-supers": "7.0.0-rc.2", "@babel/plugin-syntax-class-properties": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "_id": "@babel/plugin-proposal-class-properties@7.0.0-rc.2", "dist": {"shasum": "71f4f2297ec9c0848b57c231ef913bc83d49d85a", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-rc.2.tgz", "integrity": "sha512-6hRGHV+UEOHB2FDNbHAXUgTKEMC41Vt8EuG6HIOXLP/shylKryb7a8OPg+EedlYtRJyELD6RlAbl561w1ax3EQ==", "fileCount": 5, "unpackedSize": 14671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdXCRA9TVsSAnZWagAAmHgP/RMYgGNOlNZLd7IwaAs+\nBXOgmvbOloGrA/YRHr/6BiM2YR1ODHvuLbWr0jxKJEsSLlaKu6dV00Guc10O\nxckIXPkOuJqWqEu+LIHy3MqHLM+1/tCvrK6GtQM6/gbqfbcJfnPeqxe4Ip32\nnDFDQoe/F2NU0eQb7YDhDmFIt/UnK0af4nRiS6WFbHm2VGh9s0pKSXIbNT/l\nPZPlRGAP0u+BoH06g2OssAw0cQ2+SKcCPaxx9KNfTH2A7Id+Opo3jTwIGgJN\nCijL1uWKu2ynvgN0HNGf4m5vL7WjA1bJIpIHEn6oqvxwUW4miAiTRkULhKk3\nUcCfn+kCkpqlutLjqSJM6ghsUineagbLSruSGP1BsYNfcAe9f4Y4hmJZ8bkc\n4w9xZlBaeRTGI3QdrLBu7FdUh/IKct2vfBY4ve1V+xQVrjLclPxUh5PPUD7v\ns71xvGppb8p9EjqQUusq7C1MB2ijO6KGoHLp12UsbJY//9/ZU1pK/0NZavkW\n+Y9LqIAQt+VyQWdEy0rhpKd2uypeY62CmmuVoiGvyUpd5tC4UNVKiziEXkOW\ngvQZovV1FMuVhveYlFrnXe2USn3VKEYURqHVPDw0Ji6Qts/w1C7Q3QEJ9VYV\nzimQJxUcUFvPWkT1L6aD9q3PGHroZ+SolM4TiKe9mtrKePbn3V2ik/gEKdUy\nUQen\r\n=eKyU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChspgv/dyadNW2icjkUoOzPuZ9ovgVitSyLQugpffuPgIhAPUXCYAMJbmkxYsThtQGdVdz3o+eX52F/pkqXEtOTlD5"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-rc.2_1534879574821_0.7146740703276486"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-rc.3": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-rc.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "7.0.0-rc.3", "@babel/helper-member-expression-to-functions": "7.0.0-rc.3", "@babel/helper-optimise-call-expression": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-replace-supers": "7.0.0-rc.3", "@babel/plugin-syntax-class-properties": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.0.0-rc.3", "dist": {"shasum": "4ccf74b66780a87a73160d17e799e3ada1b30d29", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-rc.3.tgz", "integrity": "sha512-CYhKT6hz5y+XhZDTfQTK9gHZcuprvwDzpgBtkUOGr9JGOgqW05UCSsLn8a7amA+YXJAuSBeLM2Q5EVKoOq1M4w==", "fileCount": 6, "unpackedSize": 15770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEn3CRA9TVsSAnZWagAAT4gQAIwzUkpj2bx6iiCL/ZHG\nV9y3RR+H2v3CwUjyxAtiFu8X2HKcl2lDkgmswK5DuGaS46GRKc+cx/fG6ioL\nwr2vB3YZN1yslnUXqHJvGWAZZSmm7P72To78b3n32hpmdR3hA4uMvP9QVj86\nC6C9ijqxIv+aAYKfmF1aqJwH1mY6RhDAsEUntQ3fZwtfKnP4tslLeNobeFJn\nRYjcRv0dxPhAtTz9vYS1OCz4cwkrp1hvMUB5WU83rIcnqHpW0l+vADR1KF3t\n7Kc2c8zQw875WbXhTGPxLxYRLYas6i5EHG/kJxVSwv480Oz9FwAUMXvjNsEf\nr9PVN7GRNwhprAQAX3BHD/zJfX4XFN2jc+viZ/nqycdpp39Ha8LTWm5nSi4V\n2bt7sfaZSL8BB/Ns3vIRBaT07GcUbPYjunSqTycreNIrYGoXqLh4+NDvjL4J\n03UDQ6pzzO8EXHQ7nb3kgqjmrbrasK52OakegCf5v3bNinBYCdXvvkFVovm6\nfVDoJ6ycjuvimqMqNu1FdBxcwX1BJ7/FCxDG0t8sGmWD7/HDqHnckiRLNOFq\nlNhRl1opQS4fJ8mMOpvEB8kbHNy3h69ZmTuLO76toB/RizdlSwrvTZC1KwD9\nFWuxwg5VdEwcrSTYcThm9CWIKZVyixgqMd1dn19kgZ1QXLpnDc0E0/9X1OU0\nJ8ef\r\n=JJ05\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkw6IUMCOFWtaKuIL5lfVBlkUtjZt9jSsqVwMnx68E6gIhAJOKJ7gjmmjxArTPOJ4WDfiNrtYc0GIAaRp0gxA1pUv3"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-rc.3_1535134198835_0.6330607482347455"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0-rc.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0-rc.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "^7.0.0-rc.4", "@babel/helper-member-expression-to-functions": "^7.0.0-rc.4", "@babel/helper-optimise-call-expression": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-replace-supers": "^7.0.0-rc.4", "@babel/plugin-syntax-class-properties": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.0.0-rc.4", "dist": {"shasum": "b1db16552d111edbf6f6098cf1f70246bc3ecd35", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0-rc.4.tgz", "integrity": "sha512-87pcPfvm5rWH1M5WwEiK4Zmk/yjMSRrWjtfMsXeLQ8ceGJH8R11OLSLkoAg44AwZlwHCQRMRwqb3uc26FX+hPw==", "fileCount": 6, "unpackedSize": 15778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCr5CRA9TVsSAnZWagAAcH8P/0ZIXT8wytMP7F+7ERSX\nA6tGyd6heW8hg3ZdJADeZxdjgR5pj2AfUcBdzPCLg0rMml2JJHwviVURfCzs\nxQypiNh5aIgZKNbKnPP6DYvjYoJWwkQjVqJ3oKz1VwTDhO2ec+MV+FbAa+Zg\nCKdCYwfSsS9KMdQwlhdcdqsXQHcy52MKnqAMtpvSSf+If5kPzltEsPWbKjoJ\nJBE01VsSXGiAQ43Gt1av1liBioHFQ7b5baZi6Bxbni92VAes829QpRofUjTe\nvlTwVB3iu01yMbXEyPhy5oDXHIX34nj/3TMweCdb9U8hdqe4pB8GwuOHI4n4\n1c0HFUMPiCAPn1KHqjWD2o+6qadx47SCDW/oV6Irq5kdnVRtxeqHF1L/y+qX\nmfcJLgLvILiAJ4qZo6Bq+ECouBEXWmOaIiEK6MRCxgke7WhWaPJOtGQib0/y\nx10CjylTuAneMGYvJ4rhSe2bE4ovRYZyjLqdqQWx0MwS2lMnh/TKihk6wt7z\nG6gvslp0ujc6gC9RHVHKJEqR5smvW2Yblol3/XaORjTiKTorkRo5+Nz4kWRW\n3PNQVYEARwIey3JmiD2mJ01XSJvyvJ12DUujgAJS+FX3s7h4myrjcccOG7aF\n47Fl3pWridX//gWIl236YuwDlyM9sRU8QvRaJVvWlSMy3XvbMdwCr1LQYRpV\nljYg\r\n=bYQQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFtlpid/Gpe+1y35JksygFjc8+8ly<PERSON><PERSON><PERSON><PERSON>bNPNDOZAiAFVLDMJuHiWLMFVNSKXGdb9A+vFcDlwxq5/iIRV4PBmQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0-rc.4_1535388408988_0.39547431527306864"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.0.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.0.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "^7.0.0", "@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.0.0", "@babel/plugin-syntax-class-properties": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-proposal-class-properties\n\n> This plugin transforms static class properties as well as properties declared with the property initializer syntax\n\nSee our website [@babel/plugin-proposal-class-properties](https://babeljs.io/docs/en/next/babel-plugin-proposal-class-properties.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-proposal-class-properties\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-proposal-class-properties --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.0.0", "dist": {"shasum": "a16b5c076ba6c3d87df64d2480a380e979543731", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.0.0.tgz", "integrity": "sha512-mVgsbdySh6kuzv4omXvw0Kuh+********************************+bbJmB5fE5nfhf/Et2pQyrRy4j0Pg==", "fileCount": 6, "unpackedSize": 15733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDmCRA9TVsSAnZWagAA2pQP/06tj+37AxJ6R9WULzo4\nsKtJUGX0Knr0BCaYSsSWt7ZCNSYXtmxTAIUd4KReq3Mgwl5rZzqRpYClcoXZ\nWBvj/KnX3J/Q+KCNqqfW+3hTXnmBgQGjNu+JNhJV7+IioxCilglPEFYZyQC3\ny+OSZ2pretrk/TTSBrBqr4Gdk/XUSR1xcWf/AFPO0MP0+q8jL0/T+UySXWSy\nKSgoA8LCbEZ1HatoxGulmAuylJ6C3SikA/29d++KIaI3r9Z55kmlZCdv/c1+\nUYbA/3hq6/OPWnh2r1FqrHLvxaECweCj2Q+WTqeX2G+iPW6ICvArebhF+/gf\n04UgS9U85VIC8GCu2wXF0nT+D1K6JZSaZAJEubQ2YijXdgqJGF70cfQVTtrw\nNPEf5jZleMbzKN1CH7hSu4RpTB8CsWAXdRsvSu7OmdcJMR/AbgnFzHlcTv5F\nZNmTynY1ylaZV5a0Zb3RiUie1Vv1I1DvutUBb/HEYju69uaqcOiTYQjzfnNm\n5aO9Iu1RWrCVLPb2n9ns94oNtd+M8umIRMavtHHG1h06kHF7Qb0cB5uAtlGy\nM9wiCNyQ/xHfIc4WKDmi6fsUZZyy0vAjOHDPtjzXb40RKnGw/DtiDb3NPw5D\n53OeDw+1nngIwOzJOSU86xSh5dYWvqlJRRAm/xsLd9o7q0q6b55nMEKG4zY5\nbcR+\r\n=WSXU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGaJofaJSlntRgxEZ2Hc2VGN6umAAfoLrSaWE1qNbutAAiBhZ/Cdl6fjPlaIgCWFdKIP3n9y6j94HQZfZtteqjGQbQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.0.0_1535406310002_0.6679522275060594"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.1.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.1.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-function-name": "^7.1.0", "@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0", "@babel/plugin-syntax-class-properties": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.1.0", "dist": {"shasum": "9af01856b1241db60ec8838d84691aa0bd1e8df4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.1.0.tgz", "integrity": "sha512-/PCJWN+CKt5v1xcGn4vnuu13QDoV+P7NcICP44BoonAJoPSGwVkgrXihFIQGiEjjPlUDBIw1cM7wYFLARS2/hw==", "fileCount": 6, "unpackedSize": 17873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAELCRA9TVsSAnZWagAA22gP/AhwRM0zO/x2Jtsw5Se+\nAhq6dJ1dWg/qw7MK9tAhpv4FWMG3BSrz2TnYCnyWYSFspf2BioeDeMdVOcLl\nGL/fwKoYa3+sOby45kSBryBf07RKv/TysjwOv0MlP5KXJpP7IcXnLTYBd/ye\nAIwzhxAf+W6FZC6seLt6pWBUWBaFTfSHWs2yEAJBmH+IbgMHHe0BtLqYx7Lb\nvw9g1ktVCkf2cmLCwe4udAA2CRuI+pGB2pJESEL1FzFchFJCHwKsWfW1H5hh\nl6rpCIlFdKgNo+4VLZ40wHyCgg4D6g6EAvegT8fLu5CS67756kiNCXhzRheS\nYnIZNf9DlsNZuy4mArruXglkLbluR+1q2uc2L/OEb/J76eVlVdnloPXDR+02\nr5lbtAFsbPYtZEZdaey8RrCcQEm8zqvRStToAV9AAYrkEsOrg8p7STw5z5Iw\neRq7x1xGX0flHdO+2vXuovY1aR/3M2LSLYBsRzd0deLDYEGQhSnHYYuZefI4\nMam7CFXLP+YhmOyYSa8xtBUYUUFMdmiSldbuoQrPRzMsprdupL+9li4OjAXu\nqY7C/eyqMrAg5KcQljYiQfF/hcrOC+HJhFnFUdxaxuz9LrMTJnifd30Ci2oe\nkoZT+zE7VlwBKtyEHwTA3RXmw/A69W2j+0N/YL2gPDATyySNNIFZu6A8uEnB\nidLR\r\n=Hpo+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO+g6EZzAfOzLRU7MWalNtXskZaKWxsOdOlWn+BupeQQIhAIAZqWP+byT8fzngdgS6C/PHsjieb/KgWS2Q+tpuuZKj"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.1.0_1537212681873_0.7539118816863608"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.2.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.2.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.2.0", "dist": {"shasum": "12ade6dc7b92ea4fa4c84b4d7b87e9d0f446b4f8", "integrity": "sha512-pdBj4Hvyt/L1LA0Vvm/Tkp9gNDGLIkSbX8IrYfWcoA5xUZIg8qjSOTChUSaCjYdvoMcB9y2bXVGNVZ5DH62CZg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.2.0.tgz", "fileCount": 6, "unpackedSize": 3296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX29CRA9TVsSAnZWagAAo34P/RWCVhr1tezPb1yp49Zn\nf5eI40VTA3ED7pGhcED+QKLPfglSvk4axLno0jiRyugf8r607Cw7b1pWJRVL\nN6GvukOcoF0qSdEELTyjdl9wwkwVSXo9nepYNaJvZ1o3gZAbO7ylUiJVcxr2\n68TjOjJLH4Put3xlacdaNuegqt6r1uBPWQ2yOmR3SocVnvOPj/1lY43/DQ5f\nb1ce40tmHHlUzYe0/cegk0amhmjTHiIUMsXDIP/pmvBXEXdLFESTlO7O6+hu\nc3Dns6WsNNHUbVPqR+Gf4RwkXvyqEuPpuqi+CgH+cYoJBKx41HbWzBJ+Gd9x\nRbEEC6ME9jPwiPaCLxm86PHU8fyxQjjdfq2vQU9HUKI2gb5muF1MuwSYuvaB\nL/XjNqAPsK/UTcyIyaH1EXSiEXPUpvnWxjgU2I7rJ/7ePVhvAYvmieg8n946\nudzcTFEnE1iJFQBml13VjXpC8nN/crDTK8YjkM6RzOvBDG96kzZSxjFiY9Wd\ns/kUN2+3/O6Z/L96bDroUVsPkS+gxLgUysjKeOW8WvIoyyWu7iJsOyIybbV8\ng+jK6hV9h09nGVj3hJlBkV4bx37J9LZA2IPssiaCS5CtCKPXONwYDf8uNdND\nnMGZ8q7F8GbEgK79fWVuTCtaKbh0zyXTWQJQKwbRMzX/xfrXxoHSSNA8Yj58\nT4do\r\n=AdKY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA3XuglJVOIJsUWOI+A2PaAL2QrwxANhXt9CIOx6JIr5AiEA6IlVqGHDfvrxlOWDt/Aulvn5gVOz1Uj9Dslh7s5AajI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.2.0_1543863741015_0.13279410348566367"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.2.1": {"name": "@babel/plugin-proposal-class-properties", "version": "7.2.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.2.1", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.2.1", "dist": {"shasum": "c734a53e0a1ec40fe5c22ee5069d26da3b187d05", "integrity": "sha512-/4FKFChkQ2Jgb8lBDsvFX496YTi7UWTetVgS8oJUpX1e/DlaoeEK57At27ug8Hu2zI2g8bzkJ+8k9qrHZRPGPA==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.2.1.tgz", "fileCount": 6, "unpackedSize": 3296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBpuRCRA9TVsSAnZWagAAYS0P/2iEbyR1XQHq3/rCoHiZ\nASF5QHoR1jAY3TwPA/qqxEsQHJpyBDJjqM35YSM4gRmINzFXlZbH52WYWGOv\nvn7cG3LirxdNTMlxorvppWJCZrQbJndFcfpys5XZ4XjheJqmE3AkBmjeGsWG\nqQWG6fQFwQDyvl92jmB05/HlU2VOipd9fH50xQ/oP0SlebaCkfw6K5EhEIHE\nw8KG1yu9r81jWL8bcVt8WgeyNguQskWu6vRwPxhfTfGvYkXQdJQRo5YqcnPV\nUFdxIEr/q4ac6oArCQk4MF2jYIec/RAtBuYlHqjF+dcTh6RylBuqAQWAhQbh\nKBJmh93pgO0qEWtyzyMkacNaYT7em4tdtwgW9A298GDaWZTLWiJBeA3mGB0e\nDAxThGIRGMC4Bz3PYgJrcIBG0nhAjk6lL9RUWv6Etp0tg+mf+f0yNbE26UP5\n0OoatIZ6jPsQj67lPoBMifHHiQb9m3vUdFl8fKTHXzeWko8zCbDAuB11jtUV\nTbVd2FAMp1n/z0mcfjngRXn33ja6lbAjR2TPAvheBpZDcSFM+CEdWmcen78l\ntTbjDyQ9Jo9/2R5PGTXK0SggjBRd2K6A8jRwzUkzglAVT1Gq5+Pa4vID4BZ7\nLmbeXD5Eud+tjT0E8E3OJBdg/nDT3pLhO+dEbU6jLs+75yzN1CU2mJEFlYgt\nzY15\r\n=xtaY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhe3aqe4gbCw5yvkF3GUBIRx3Zr/twTH6Adt5tI9VjgQIhALYvom9HEbXqvrgsBxKpBd80Kf7Ha1Uw91Ysp6rvPqyK"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.2.1_1543936912840_0.5155609800316667"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.2.3": {"name": "@babel/plugin-proposal-class-properties", "version": "7.2.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.2.3", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "d35f2ad92b322c3bb9c6792e2683807afae0b105", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.2.3", "dist": {"shasum": "c9e1294363b346cff333007a92080f3203698461", "integrity": "sha512-FVuQngLoN2iDrpW7LmhPZ2sO4DJxf35FOcwidwB9Ru9tMvI5URthnkVHuG14IStV+TzkMTyLMoOUlSTtrdVwqw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.2.3.tgz", "fileCount": 4, "unpackedSize": 3353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3lXCRA9TVsSAnZWagAArM0P+gJjrQbGo80P3DFYxPqa\nhgTsk5/3yM8ZLPAqu82ANyTzR0RsUYW/9LhFi1F18uVmuiDx8e4UN9epHV3r\nEL/Kyoug87vuPD5eeEO6Ub/Dh2mu1zepmicjZI4Ojr2hAAiPTvQ0LtVpOnoa\nFEXiSgC/8vT0/owwoJmbw2MBEV1LS9V35PZKbtTAmN5pKch/rh4HK8Tck+ZZ\nReYXzItOVcY0q4x73mZxm0W6+ymfrhIFzD7g9aHbGLXbbmwZG7PB25v7yacN\nnQeRM03OH/+1306yGEWZtHjGDnxc8whwcUC4ojwEj4sNfSRJHWxzWX3mM3lp\niY8zD4F79X1Gf1ENUsNR8Eo9Ml62OqUkU/+NZBQqDd4Aliwk0lz1QboE/wxq\n3EMl2qPv7duxZfhfLslOx2VrWd/iHlmYASbxyaswF6pzgzPXMErvSLENHGOQ\ndi8mJztc7eeTsPHu/Mir8EKWrYDpQn/p5IQ41wn0GPIfBro/91P68TVBuwM+\nwKPJ5glGQggtwLcgLvelyV0FZWmPL7cHZtjKndHsJrgGw5poqQWKFU7zIpIt\nbf4aaYcGUa+6ZZwG8iL7gdMSj7zAkKluTYzOmesD/EjIbjAm75rQQAghEFtW\nWNCKAcQC3jolfXFUnszPu5KEip58neLFL4UV64czp2wx+DDsC+QBQfUyTlyF\nVUEH\r\n=mPD8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvjmhx0aUVmtzUbDh9xv7yEMox5JRbccpZxt2lE2BRfgIhAJof4JLyyULAIbY9HAfXQORxtOTobq7rOzbyxCQQZ9QU"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.2.3_1545304406597_0.05647424693939018"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.3.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.3.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.3.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "_id": "@babel/plugin-proposal-class-properties@7.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.2", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wNHxLkEKTQ2ay0tnsam2z7fGZUi+05ziDJflEt3AZTP3oXLKHJp9HqhfroB/vdMvt3sda9fAbq7FsG8QPDrZBg==", "shasum": "272636bc0fa19a0bc46e601ec78136a173ea36cd", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.3.0.tgz", "fileCount": 4, "unpackedSize": 3299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjvWCRA9TVsSAnZWagAAx5oP/17x4wl9ePek7YG7Q1TE\nbGuhQvWfkHongGbUgsepAQTEzgbUBfdhswnYn7/KJEzsIqDYOvODmu6lRQ4Z\nqneBrFr0tZ63cNKQE3pOWlrjQQLWAeP40eyuIaEL6wDVIxElC3PgIUMSbK9E\nVRxBNEToCvg3pFMhRILkgm+bMKLEBjXH8LwVKPlrdfVz2prloVnsPY6JxUwK\noeJy4F6HkVePHajfyS7PLB1h5R6zWEpNZLd8GN4V7zrym3/pkv15OPSZN5Fm\nWlg/Cy21cUn9NavMu7VzSo6QRtbHpRc32UGyzAMpdzZoAB73x8NmABEx2Tbb\na95GVO4PCzqNKf/JFZOynS1bQz0Fz/sSx/KmKwyIUYzpqe1/tYEQNN40FJjI\nJpGD42zt01TOsDJ2+3KI9TBLl4DtGwmmKWW7hzOL+BL+k88DiuXm275IhA7X\nIN5rbu1siZh8bnsLFRjvhZ3nSg/AzTOMLgKHy4lS+0DMwUHawdEpJ6uQoNWX\ngcL4E+Mjmo/FWdv76wFzghPISleKfK91LDPQ9ZlTxgGdknqKiii+Qgu/W4xj\nxhHFSO2/ZOLFvatArsM13Ldkky0pabdPwWruvexR67gyDhIkGtM7YKru4JVG\nEsoVQrSJasM/BcT5KXbxUsaIUoFFgSlpmY6mMuZLJOorPJFR1efMuZ7b9eFd\nA5Pr\r\n=s/if\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5sL3XKyKrN/GBh7IR01xXDogmZY9SXC1PTxgXyoI1bAiEAnFIC8cdcgu9NVUJcB0LEt/Vs+dX1+v/nBIle/FtkzZA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.3.0_1548106710387_0.1327446435928341"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.3.3": {"name": "@babel/plugin-proposal-class-properties", "version": "7.3.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.3.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.3.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.3.3", "dist": {"shasum": "e69ee114a834a671293ace001708cc1682ed63f9", "integrity": "sha512-XO9eeU1/UwGPM8L+TjnQCykuVcXqaO5J1bkRPIygqZ/A2L1xVMJ9aZXrY31c0U4H2/LHKL4lbFQLsxktSrc/Ng==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.3.3.tgz", "fileCount": 4, "unpackedSize": 3356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyuzCRA9TVsSAnZWagAAW/AP/14opU2e4elWx0yLWsGN\n68Mat1kDtPYlad7QhnWwDO+yvWJsR9hUoircsPXCcAyYqC/18qkJopdgz5XQ\nvQQA1EV1gKBrkPFmlKHzrjmT32sgpG0ypeJPkxtjxLRtRFIcCew44eUFhrHJ\noYUiHOYGpUTwE2udyKwXFChKOeWw0ep7Ybd8CicRgmiT4w1RPGdKAI9M5n2z\nEOC9aU/s5k+rNL4sWRFh5yqjP7EahuA93RjOvWUkIr2AilB4b8jJXhUjoWr3\nxIVd8joJ5DBreKTrsb/Hso4g4x8RzfTx/DGmdqfb0bDRXCrjg6WXAesZZz0z\nfsoi+af1T2pkmgthW3pXcsKoZyiI4IC+SzKRhn2sQmHPnJ/sPUQ1LuYsSrdF\n09YAvLNAL7LDYt7w4GwOX/9M3yjt4r46/gj+GhSLsCtzyprw6Id5yGrJduvb\ndKgVOlOzJlXdxTS2+ujiFv3fScMJBxlO3xPhKzMcVbZ0icU086inuNUe1aDw\nCc1KkgYPcqqpCUembqMGn50j3/CGE1E1pP8hX+2rAJrh1rUa1N0apJKCbCMM\n/34M0Y16hmQCOwAjvalVntIHtE9ZkBmn09/6RxMR3pKTdI8egZWORklaG5qd\n0b7XUc47b3U/5WZZOGDZ9QcyC6QQe+wcR9Bfz93Q6JSlCQMmv7I1lIoAJTtH\nCgBe\r\n=7+7K\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDmeQG7CW2gXmbQuqGU8BHctlB1pbVulPXi+hgsxSFsAAiEA6GqK+SwAB6QOZ9PCmFCTsizNeoNzdJJDmo0LTp+t1pk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.3.3_1550265267130_0.06046682462592945"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.3.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.3.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.3.4", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.3.4", "dist": {"shasum": "410f5173b3dc45939f9ab30ca26684d72901405e", "integrity": "sha512-lUf8D3HLs4yYlAo8zjuneLvfxN7qfKv1Yzbj5vjqaqMJxgJA3Ipwp4VUJ+OrOdz53Wbww6ahwB8UhB2HQyLotA==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.3.4.tgz", "fileCount": 4, "unpackedSize": 3356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDWTCRA9TVsSAnZWagAAxPAP/2jsh9GsnqCbjPDFAect\nR6l8Gygxkyx6uq/9Sk4bfmtkhHrvQM2mEmLAyICAqY9ddG8oD+Gop+DcLcSK\nmyDfubRHrXZaL/JIGBHaF9Ky+w1uIRpjz0GHmZOrzsU1gMnZlaQfFulRyYak\n/uHW/Oy210/mboDEV7Lb8DTjfl2qJk6+d26s6EaDQZhr9cc3tprb0Ux9oVUs\nezA2dDWXPO9Qq/pAm+FMknzZKOHsTuHuw4ZRm+uOFir5WhE0XGUrQ6MwGmll\n1vshDUyfZ77ste4LHCYVasThpH3NhSsRuGjgj3Gj+U+r7SMSMZiH5jLlX4/h\nzrz6FQcwwyYDIeOQaG4Z4K203xDgwiKyx0v4PupMB/9tUK9CL6inoygb8VU3\njFekyHogqD56v8wUX6vJOfDY9wWZUeEi3N4zFz2NDlBX2UElmRFYIUIPHF+3\nGr1BaXXam7JXzJbcXY1hatUEQGT7UdlA9OOqzYSIxS49UxgO6/6RLmnHZ/6o\nFek+nvrfXSC/cpvnhr+SoEKZipdCotUXurHlmG56F6DS/YPA8na3YXSn33ha\ngAGLDZpitqu/GhGsH9FHwVp8I2JqyVrWQOnyJJv0DfsBN4wkXXQtwFheSW85\neMq/0IAYGIkfUg5Y9PwJD2yEiFCWdGqBNhpOmanUW+r4KfHd/bGSNodyzODV\nXFjB\r\n=E0fF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICCxdHyc1d0CzTDgtdptpUWc4UpseR1aVK9SETfTuOnXAiAWHXOehFuYFI3/4jDIOIFZQ7nUFIWqckpKjAUm23kJ/A=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.3.4_1551119762399_0.9184850158987277"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.4.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.4.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.4.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.4.0", "dist": {"shasum": "d70db61a2f1fd79de927eea91f6411c964e084b8", "integrity": "sha512-t2ECPNOXsIeK1JxJNKmgbzQtoG27KIlVE61vTqX0DKR9E9sZlVVxWUtEW9D5FlZ8b8j7SBNCHY47GgPKCKlpPg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.4.0.tgz", "fileCount": 4, "unpackedSize": 3356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTqCRA9TVsSAnZWagAA1LIP/1afih0akEzTW9PsaY1G\n8EJ8xGVbnrg2HEEs5Ib0UAFoKzlIE3tD5yZFehkAy9PkR5CtgAf5yBqrGqm+\n3iKySNglXhDkfMcbAhuTht6xzK2IhAZxnD8LplOFkDOO/jI7n5CTQYD1oPQK\nispmEHrmQKKalojDkJbFkWc7oDxMf//jj/crO9sRF4s0uznwpp9m9Ll5AiNu\n2osHN9VsLPs1Ge7kb+Nk1MZ4V5C+CQ9R50scI/JOKNsEct3Bn4cmWuztDJxI\nJKcHxVrdlhmaEJGFUhukW4PUOru3KTxfH9lb9JVWtUMi5OWuJmxESepqudZZ\nUzW4+GQrwzNXp2V1LThPL27qbLTMvFxdP9GXnYzln+Lhu595p+KOX2zQUs5J\nw6daSQQiw2Wa/p79aOEWiNYDTU73o50fC2hL5uDnt/HkK+T6HLRn5FM3dBgi\nkswlHkQac2EQsfTDZk2l6lO1Ttd8gGnicdIbGxulP3+VzcJzmzvEAEbLfxZD\nYkwoulTE8r51gRvXmDhjdHPGW8++b7bajI7wwrcInUcEr7umGVqZA4z6l6I2\n3L2r9Urp5nSisxFlO8/JqMFx/l6tbpbQthiFy2dFi/wNOLY+QkS8YvZFKbz7\nhH9S2zaym3f3ObIbGZ8og2Dl145yyfB1K30u33K/PXNKHkPPlncLtnTSQD4G\ns/lR\r\n=m9DI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEKw841eeUpNTxdiya5Dz6OpLFNsSOmeVvQhRWs1YwFyAiAw0xuccx531nkVchEe5eLtbfchnSDjFxjSmWGRCzqE6w=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.4.0_1553028329928_0.9333541168266872"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.4.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.4.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-proposal-class-properties@7.4.4", "dist": {"shasum": "93a6486eed86d53452ab9bab35e368e9461198ce", "integrity": "sha512-WjKTI8g8d5w1Bc9zgwSz2nfrsNQsXcCf9J9cdCvrJV6RF56yztwm4TmJC0MgJ9tvwO9gUA/mcYe89bLdGfiXFg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.4.4.tgz", "fileCount": 4, "unpackedSize": 3356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3KBCRA9TVsSAnZWagAAtmMP/21c2afqhus5r0vdbrFs\nJddD/qmLspbUeJ28kzi7bcryUQWPmggl2man+sUjEZLp2y0o0ECdaT1i5xyW\nyS8+EGy92tjnCiprlm/NhKwfoh59cE7zjqwoCIX8svpQceWSM079U1n19od1\nob8HpY/SLijyfgPFRlJOipGxqfacvsoY0GDek1DGhiiTYBZZxppj0NUt5b3e\nkC8IldRHHPiU85gZyZDKJg7hQDUN6ltyyP3sjhwZo9DeyjGpz66UTlLFBt+3\nLFRxahrX7/FVaWoOEyZY34rn9vz+lUPQsH4mvyFQvllmua3VCAVtGf8BIoTp\nxdx/5geepfx2qgkrAJno5Uebfbq96tpvKbziXHq0cNuwAQImPGKgUkPt/61z\nyvjBN+TG9yLJEFp5zlk3hsW8M4QETMF/Je/+JOVhThwijjpwyAAX4VSx6GVA\nS0qUudo1LPXyFEt9eBydgzLljHS7zMq5GNeSJ5fom7VGSKjvEgbbH2n/cvnG\n+xm9yPtzYRRJvpQeZ06B1kv7ixWG8n8d5RxRKMQBiGiQNy8mCgK8aLdNyxzI\nG9bTSveksGDedk83dbFmvQ5AEJUTK8ZyA9ET9N+PzbZ7IAHsz/CwUTRoMB4m\n8vO0CciZrHi6dzCOcE8g28qZWA4fk3M6f3jzn2YBI77QGuq8ECGqbCEy+BU/\nYong\r\n=kLNY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTt4m4P2o3VuKrJHQm5zBDKpK4j70OcIRHhEdY0dx+jwIgRWb2TxPVbeYQlIeBKcXBo86UW/kyLv/aKJfzwqlN8F4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.4.4_1556312704537_0.026520770948270433"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.5.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.5.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.5.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_id": "@babel/plugin-proposal-class-properties@7.5.0", "_nodeVersion": "12.6.0", "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "dist": {"integrity": "sha512-9L/JfPCT+kShiiTTzcnBJ8cOwdKVmlC1RcCf9F0F9tERVrM4iWtWnXtjWCRqNm2la2BxO1MPArWNsU9zsSJWSQ==", "shasum": "5bc6a0537d286fcb4fd4e89975adbca334987007", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.5.0.tgz", "fileCount": 4, "unpackedSize": 3356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHfflCRA9TVsSAnZWagAACgUP/imZzTeMtkMKeQ79Evwo\nI9F30f0fOVL5e/T8HP1GUpk5s0WvnexeqY46+GN38fI3Ni1zxPKcN2MVxhP9\nUaLM2Po13DmeQVSpeY1IEipjXnHg8ZRkWPfxxDyFgs3SLCGn5IUm5o6F/zk7\noloXb9kt+yPvJKEm4vhjhJNQMGZDY1AaejU+una0mbRwTlmaJJSzOELtWmOV\nvTc0/FrXrtJqN/uNSSNWaNaoWQCwAIph/k+F5R0FrEJTlDTuOht54QctjrKX\nurfFuP2slxo83kJgQQZK8o3YFBHy6EbjCisx+jEokdn2m1Etx5GzWQP6GDww\ntmh7gbDFhofpuc9uM78l1/pWazSZTRow25dbICvIzLaWJGNqdD2Q3ONRhblJ\n2DuuCByUcQtlWfuWWtg/lQYkr15/v4q6687T8hvdDOvqC99erIxmGnho4Hig\nb5N14wB4xiCHq/jW5vfnjM6WZy2pCEkvRS/sBBCzLnh5a0k0PKkOOHGCV+Il\nH5VYlnxuW7SWTCdUEbcjsslTf8MQw1qfWOgv0z3WytqZYOcGMEvcXp7e3ilu\nvEP05C0BzkeXok5R01AMyZmvCSh8iZrFMeoKIcdSOw3s3NCbihwwWYuHSkiV\n+Hf8atKupwzfuVuqQF12HWP9Hc+w4l1GEa6LSLRCP50PLgWczZ8BA/05NA6K\nVE8h\r\n=hds8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDs8Q1ri5rHu5Bsr9XWO+nnjqBXqkDh5lB2wFSuQdHPyAiBOJdiavdJ6qORNftnrH9VTepeAZCgaLKBuO1EDXxhtlw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.5.0_1562245092876_0.31058988005622257"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.5.5": {"name": "@babel/plugin-proposal-class-properties", "version": "7.5.5", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.5.5", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_id": "@babel/plugin-proposal-class-properties@7.5.5", "_nodeVersion": "11.14.0", "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "dist": {"integrity": "sha512-AF79FsnWFxjlaosgdi421vmYG6/jg79bVD0dpD44QdgobzHKuLZ6S3vl8la9qIeSwGi8i1fS0O1mfuDAAdo1/A==", "shasum": "a974cfae1e37c3110e71f3c6a2e48b8e71958cd4", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.5.5.tgz", "fileCount": 4, "unpackedSize": 3356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5F3CRA9TVsSAnZWagAAsE4P/1MNn8hCBogp67J/k0Gy\n9MqsYv5VqXJIuXcZxNYar5VGc5p+VH+5m7hxZ+MMHv7+vNy0OiyuWuyoKfru\nTqR318GKvsKXF0P9k3FZ/ThpMpnpnliGS5lX4be8szYbGK6Ul8mJDxVNsxZq\nTwwetZkrPnwUKjW/kF0LOsA6IWGr6rgvfPSBeTGcVPM3anebI8HN3a1e9sUd\nfzdRFY+qILpm77zXbv6NWU8L/svSrYC/UeUUChenbKUrYHqmlNgLNMSzi13h\nPStAfE24ov9wDaJc0lK7FoE0n537YevNk7EyCP+49ueH0EASm1aRvDUvwYi8\nZwUxJCvfiixa7tyve/++qDDUCNAynQmstBJGp2Nff/GWLANRSo8+1B8/Gnl0\nbQuVejUhQvnAFTWd9I2WrKAMUnZoSDuxKZBfNDGST25glNmjzk5kRwoWNk7i\nBnkv7Ce4aTdMxfAEgGVdiPkh1d//Lh2uY6W1fheXhe7bW3y+2DRIFf3NsFkZ\nXVYCMwd/QoDKUt1tZk+28mTt0wk6a/Udv/xXUvqPtwwW9pwIylHkh1ewiA9v\nlQXoS0RVjznP09G42tG5/IYdIoo3/K/zS1GJSso9r7ov0YUOQyHZMFBVaOEP\nWFrt3mbgjxl76Gt22kRU/LimVC7Guk+YXHMVPPHpU1wDY+cs8hlX6aOIKw9B\n+AU+\r\n=uacc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHWDrQDYMNmQ3/XhmLkk1QeevKkIc2l7/ktko6343uONAiArE8lBi4WFI3JCLDI79xy+Kh9v8oVtfmF/jZ2Y8Kr0iA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.5.5_1563398517943_0.4223535183268121"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.7.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.7.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.7.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_id": "@babel/plugin-proposal-class-properties@7.7.0", "_nodeVersion": "13.0.1", "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "dist": {"integrity": "sha512-tufDcFA1Vj+eWvwHN+jvMN6QsV5o+vUlytNKrbMiCeDL0F2j92RURzUsUMWE5EJkLyWxjdUslCsMQa9FWth16A==", "shasum": "ac54e728ecf81d90e8f4d2a9c05a890457107917", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.7.0.tgz", "fileCount": 4, "unpackedSize": 3136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTQCRA9TVsSAnZWagAAYeUQAINt/tclv2Vg1EVqWtcO\nF0O/pclEgfB9jORIVcc2fiKoWRxTZyODeRqgeWeLOjkmKIh+fDDpIB2pZu5b\nnjYW4xLLJ3xIHtMKtcpD45NHx0gJ4I/ZXLBJuBNlaIiLTx2Yk1OG1U3KcDip\n37jUZAVsbmz0bY51zZwMP4OGqyjyUwhIUIHOLxq/VltMqeE+fiQIAYU3A5KR\n0ZXBakvSZvBXESUeNslP6/er3lnXVSeVUoFMWJJloesFHVDveS73pqtxMULJ\nhq2XgYASIpYQfUfjby0UqxfLAjbldQ2OLyyOt/sYuXQ36ooA8hSz8lHHMIUj\noKnvF97Fdbzk7UBiFRVv1R2dNlOx5wxcKsGadIT+GElD3CyXNVMgKJaf+ooQ\nWhS2bZOIfHWrMNJYvyethM/hnkIuxEhYnooD3eO3BNl9IW0DPYsZ9BkJ529Y\nifWfN9jOBGAZT7LV3UXx0GAhh768TWEkfde1sS5+SQ9HL4DYLOQ/OQCn38Ni\nQRwtdNRuC25AX5SNiC89hJ0BOm9kbRxAn1koSFDtgBKKj5Y5tIFmqPJCT7nc\n81GWwR+snAEuNt9RfhnCf2T5sl1CPhruipUpEFvwlUUmdzRu5f919olJLovQ\nTSuXj/kLjofeDuuY7MnnxNn9HFpAMQdV16W/E5u5XOBmU9kgKPHBGifq1cpD\neRds\r\n=CHzF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbiV+gcc7zt1e2MeA+4l9IXtpmpBdQK9bVVC8jAjHBRAiB1tNIr0/uNgWyaN+Nna+nGZZlJGRxQXd0VeY/Kvcbrow=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.7.0_1572951248509_0.7634075299642127"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.7.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.7.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-proposal-class-properties@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-EcuXeV4Hv1X3+Q1TsuOmyyxeTRiSqurGJ26+I/FW1WbymmRRapVORm6x1Zl3iDIHyRxEs+VXWp6qnlcfcJSbbw==", "shasum": "2f964f0cb18b948450362742e33e15211e77c2ba", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.7.4.tgz", "fileCount": 4, "unpackedSize": 3136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBkCRA9TVsSAnZWagAAC+QQAIoGOjOpVloTSVzm28YZ\nay3aCcMzUrN6iQgEIvS8WUG/MT+11kgxFCzVjPb6F6aGmRsUDtxjzUx8c/yU\n2mzhqXzM+lKiVoyHUSo4i32PssjPjkiQDRQFGjw8iYAwL28blTzCMz+v2aCA\nChv8WfjLkU5hqENd8UEgLSGaHigBMl5DpMB9BTpOPRbHFuhITrQlYdv87RaW\n1huRCLtAWsZL0YN3nBngIw8XBiqHC5u8x1373dPlWr8l25oUaE/cmMq5Qfwr\nEFqVzGubIUwfDSjJkI5x0zHz+fKMPV3T94hrlsEfS33wOdzJMHsop2otr/bp\n9yUXlW44UVmgC7nTh04teMdTqKLjZxZNXmhaBUjd74bdFYUbIvF/fxLqIT0u\njS3SzpN3Ihul9iKL6bml4EiPC4ZMUX78TcIw0qhyUS4uq2wNc0+aY2XmG2n4\nNYbAQqFYIFCBJeMOMr0p5mSS/DuQj0qOpclKiNDOrlC0T6fT26pMlokvzA6M\nGaoxwSBGblSLzXvH/q45Iaks3GywW8aSSkxU3Hjmocczns47y6Y5IgBoyL5X\nl1VdsUzI54VBHlBnLQjAMbqs2w7HCD7B6Iw1rTVL9VhDYGyWfFrz292f1isO\n49LL/I1gPX9TW4vHgRt4yhLlHR9UKZV++Ruv2sllZeR6pgScydz8u42SyQwT\nMY5s\r\n=3JTd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDbBalW4Y7gtgnV6QxPITMi2BgNQSfIB546RfrqsyepgIhAL+3VgKPWjqOBSSslnXSFkBzGVjDQZ2LQq88nYpusi7O"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.7.4_1574465636491_0.056866426737267606"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.8.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.8.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-proposal-class-properties@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-eVGj5NauhKCwABQjKIYncMQh9HtFsBrIcdsxImbTdUIaGnjymsVsBGmDQaDuPL/WCjYn6vPL4d+yvI6zy+VkrQ==", "shasum": "bb3325d9166c80db8f2e15fc0bb6d61d7300e373", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.8.0.tgz", "fileCount": 4, "unpackedSize": 3158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWhCRA9TVsSAnZWagAAoAYQAKBycD1qOTepcy+nAGMS\n478HdFZsNJiFmnSZpxtUnuL7m8LVAQcXotKQ3M5AvsKhgKCTK+8HxUCHfjRj\nZht1LaoAQU7IBqUaLHalNIv+BVvyNnz2rvnrdlibiUdJue7lGiQ5RbQlK/Wr\n3IMb6wtw5ORG/7QamZmtmMGpNLk2OsZvqPFfN2F47YVn4o7oo/+luftpE100\nuuPpGkES6ki5xKVvPEuZYbnqEyucuKURcKnS0z4r0KTIzFCPXs50EfOFDMOJ\npSZc8GbogJYrXwrOycIu3tPofzsZZH0lSXN1Psoxjk4sucEJsF0Mhl9zrwVO\nPwWhD3vfGO30xDQMbqHgvbalNuRyGKrflsvpv6LkGYXsUdmUTpVlvZ970ppT\nqoyfM4BKnMTOZetlxe1E7m+1XR/6B9Wp17TycSrMzta21PB3mzMyGby2uRqZ\nfI7OwBufF4Gu9ow3OpUheDpOBm6EOhhckZY0Ojs2YZLSP4zr63ZaqzmfuH6N\njzU1nQN8okEeA8p9ahAlTj5pjDM2vImt2A+6euuBxN0YfV4W2iMbngbf1LiW\nneHxUjwYgb/On2HW6yqJCutylwG7Is7iJMBI90Qi5wd2VG22xswoFkeB6Scu\nxBSoVV8GDXx6+f8X+YyCW3wJiOOorafRJjZnZ3ExZBoorWEpOLaHjyHCf49w\n3Bs/\r\n=zt8b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBuw09DT8nBGxSbv0Ilmwg0exVYZs5dIYjvD1rm002CbAiEA3co37LA7AG/rNYBytENZ1p/iXt3FdBsug58j1e3Vin0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.8.0_1578788256912_0.3498853719236723"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.8.3": {"name": "@babel/plugin-proposal-class-properties", "version": "7.8.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-proposal-class-properties@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-EqFhbo7IosdgPgZggHaNObkmO1kNUe3slaKu54d5OWvy+p9QIKOzK1GAEpAIsZtWVtPXUHSMcT4smvDrCfY4AA==", "shasum": "5e06654af5cd04b608915aada9b2a6788004464e", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.8.3.tgz", "fileCount": 4, "unpackedSize": 3136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORPCRA9TVsSAnZWagAANVoP/RruRRqAQOx15unQiX9y\nkAG7rP23oJg1fYaXLPc86Hy2l/U5IVDesPO+2rDF2Lu0iiHBW5TXaND7ZAiN\nVMXfoBvqKbUWnvlugkX+7FhZNSSsXFj2p0W92DaMOorLn4qsX66/FRWZ67FL\nm042WHQ+M+VKaTBEIFSCIDdSoJ5pyNwV9Z/U915hytegC65XzDuwBFPE+lw7\nazTZoD7kzXZ1UtY1FuCCkv7sX85eDcbUGwJ9g820GYmej8zfUFcGvdmKRRFV\n5rk9W0I/AVXGR54kbIIg7Cz9sMoE+vTaUmCmqnAbEiTxeNZjcUSAgoR63KhQ\nrdcOp8QlK24qCb7iAZqqVJcRN1emSH7EbdmJmA0SbaUdHH/Lv/DvvUvwfHhk\n0icJs/h8UuSXdxCMX3Oq81CiZFcLoALXrt0DgF+Ft2Qr9Gy2bdPXAIyPZTc/\nmuL294vTKct4s47kZq7KiUeyzO14dEq2CR6PNw8jIUhK0cDC1BTalZZwShOw\n+5AS0gnZJhsxJeXgbnBwi+jt5Zr8qk7NdLkCcZRI0DrlllQBkxLvw6ASNag7\nHFH2UQA8AX/2DdAMj00jQMshvOaQ7taAYC775byG9W2s8ok58YAHesbNz3g4\nsadL3Gl2b0SPFRx0OSgzJINu85Kk6e7/7rs5lV7STtft5iB1yOeguE6iPYtz\naq4f\r\n=eguB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDtCccI9DGU9/oqyfHwBK56oBvT3wJnqXrmTdzSmpgAhAiEAi7toVjDsMAzMvp0HNLsEqayiLxumFMta7MHcfckuj2o="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.8.3_1578951758669_0.03097097914899183"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.10.1": {"name": "@babel/plugin-proposal-class-properties", "version": "7.10.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-proposal-class-properties@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-sqdGWgoXlnOdgMXU+9MbhzwFRgxVLeiGBqTrnuS7LC2IBU31wSsESbTUreT2O418obpfPdGUR2GbEufZF1bpqw==", "shasum": "046bc7f6550bb08d9bd1d4f060f5f5a4f1087e01", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.10.1.tgz", "fileCount": 4, "unpackedSize": 3189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTlCRA9TVsSAnZWagAAujAP/A5D0LI0FjDoTP17fYZj\nimPk071AS3jN0yObmY3AUCkmGwVI9gJIaq7omxq5/3DIJSjEePcXA5ivwfsJ\n/cR+APfwTACEFcX39rMNLHlA5TcYBSCEqkqVj4hBXMecul7gjP56KnzMQ5oy\n6nKQu2T+D7vqroWm6cmG0ge68zEiOMa9hdprycPmPhF8fn+0hCQTlmTW/FGm\nT3hGc1czVNvs7PnmSo++zjSYVSeORdEJQ0DZh3Srsq/oZse6pW4rZCUlt7HN\nxjvN3+n/zNz7PzAsm/E2yQRHYIx3EWQgzMiLn6AT29FZMoURcLXVrWFPaimy\nQV6TD67oO5g61ycF4XJAivVukrGUgXOcyplAxPlNVk2imWW92PomJisGkNKP\n1u8+mWoI+zdL3/yz1hXz011+cu6Y1pNJf9qvp9kB55ZGyVW6PyE+8ULBWZHp\n8ttKGdmledv0c7saaHAwpXPABmxYV3zeOtkxJZSrH+GccEyW9OmZbMxQXRC5\njJNkdefxH9wsN3pC8jC/IY8PBC3EikIugQl4tgqcmaQYZNFJ0XjEpCq0XGpR\nQc5jNOfUSWcXdi/BGH5DGT82+3OpmGx16HTh205nOQID+NQFqXDgoXyPUsX3\nIBCnTzK7gLRJWhe/eMMfVEXAsZHx40YHWJ2APM0czN5zczmIJb2sCzB6qkA6\nqt34\r\n=BgGD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7ltXfa25nK/jys+bAkVzoHv+Fapt1QQlJdhrT6WU3hAiEA2bYWnGjNqwfW5FSRFdC3dpAMYNHf5hZVqVp8GC2aKCY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.10.1_1590617316615_0.5886686321385077"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.10.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.10.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-proposal-class-properties@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-vhwkEROxzcHGNu2mzUC0OFFNXdZ4M23ib8aRRcJSsW8BZK9pQMD7QB7csl97NBbgGZO7ZyHUyKDnxzOaP4IrCg==", "shasum": "a33bf632da390a59c7a8c570045d1115cd778807", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.10.4.tgz", "fileCount": 4, "unpackedSize": 3189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp8CRA9TVsSAnZWagAAN/UP/2OLp3nEUBkK4rMHYg8w\nqW3IFpNvmUARkzX0w8d6Q612zuIXtau02Yb2PycAEDRbmMw2NZXXLLTGjVNw\nk9N3uQcNVqOKu0k+sGhS6nKnPrf079Z4d2IwKOVNDYeqfPpEsv0QAcLUxS3F\nOOhRrmroJRxF+KwgXcACAY4NmMbzxytVq7NxvIziB68vEnTTTVT/t8UaD6bg\nVX5N4UKw5CJhSB/nrvDE6hw6XSCGxzp34tHGBJFJS/Vtw3I/1iana8oqdj8w\nEHKlZ4OkKo5YqN0v0e9eBkgyQny7c7YdkIdd2zfpco7UxHW6JSWz8ELwRZqd\nq0SYAY4iZu1qrBWNlY4B1gy6mAk+Wkz1Mutu/kPvdavdSnzpmYJa74mk4mWS\nQ+sM+D8AqGEHbv+21/LzXTxDsNWSUlJrBGnHrqMcIIoL/LfCKXJ1CIU2KURP\nkCCPqRMCwC3Nd/YwTnsJO6QSB9wn/pk00tzNVyIJwq2i3KkOXCS9xrw/n3cI\n9aemuT0xHrZ481AmAzb8N/lvz7/xQ2Yx5mxFTopgb+3QBGuDQvda6t8L7Dlr\nMjk220dqDM2sSQW6LhoC5BpNEqRlGFHypxtT8ZQiiR2qr7FRVHdPv5IGekXS\nPTtK4oIjD1Mp87qGMC1K+bm5uMoSRbRVi1D2zdSnqAezkuKg0MExQnBqy3Nn\n1JSi\r\n=3xp5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCV+jQvwzVcTQrFv8tWD6GTOITxS4j3dEHlYS4RVROHugIgH9R4DKx0XmSdsLNo8ssDXuNpNz7ut2Y9NcHLkbvvPmo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.10.4_1593522812006_0.9202835778817156"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.12.1": {"name": "@babel/plugin-proposal-class-properties", "version": "7.12.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "_id": "@babel/plugin-proposal-class-properties@7.12.1", "dist": {"shasum": "a082ff541f2a29a4821065b8add9346c0c16e5de", "integrity": "sha512-cKp3dlQsFsEs5CWKnN7BnSHOd0EOW8EKpEjkoz1pO2E5KzIDNV9Ros1b0CnmbVgAGXJubOYVBOGCT1OmJwOI7w==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.12.1.tgz", "fileCount": 4, "unpackedSize": 3130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNA7CRA9TVsSAnZWagAA0FAP/1X25xAYqcKP8hGJ02D1\noKk54KdGWGqmUpnKbOSUZlVIrugBG9kN2iwUs34nYisiT1UOJ6toIWH4NRBo\npBZ9tSP+5p2nt9rXdYWEpzzbdVE/OSZdAHGvYvIA+lecyj6j3ZZPSrWjA9I1\nzJeEXd9xH0RMa0j+S4AD3HVZwDz+bKekqFOcPmJWVzAvJXa5hXsZjFIdPwnQ\nqx5g/4xXJqOIVCRxjsNtkpvaxR7VMaIhjYzMLad3UblsREw3IU+jQYVlW1l9\n3hOaYNXehNcNbUCAmQpVgzzaPajRAxKhh7f4Y5r5MGbBDgiReEG8mXJUUYMg\n5l6k+1nqIlkWPOdq1SmgFgp+vN4B1hKwoCpGwTpmi8ClteHxWYsTN0k/PYz0\nG4KqBU2FVNH+OsKDNhCR/4LzRnm6Ov0f2YiFGF6IP4ugxQqMHz4HZiGJS7uv\nbY4gO9/Bj52yvuKnZs3SSK5SV2cbFVhvLYIu9nsCLG/OBYPqBTT3ZyfRx/Mv\niNhoJ+UYCZfQ+bFluZMGEjXkVcaQVupZB5BbflfVpaKBFnP6KfRCmp1qz22O\nNccS3SzTmKBBLXUlEhfjtYzHwxaDZnlsmlt9n7esfH/ujyovFBu/7RZqrol4\n29g56BtNyFUw2MIk1aesoVajXEcyy0rSpeC9lXy74bvOacuq+Fh35bJgmMRm\nE7Gh\r\n=vLXC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoosMzmt2L+0RkkbZ2qxyYhAtiNdBImVqeiKItzCFvjQIhAL9yHIcK81YYgpGA28oARanT3/aaP3f2lYmxuRjXRqbH"}]}, "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.12.1_1602801722618_0.8425492717388428"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.12.13": {"name": "@babel/plugin-proposal-class-properties", "version": "7.12.13", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.12.13", "@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "_id": "@babel/plugin-proposal-class-properties@7.12.13", "dist": {"shasum": "3d2ce350367058033c93c098e348161d6dc0d8c8", "integrity": "sha512-8SCJ0Ddrpwv4T7Gwb33EmW1V9PY5lggTO+A8WjyIwxrSHDUyBw4MtF96ifn1n8H806YlxbVCoKXbbmzD6RD+cA==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.12.13.tgz", "fileCount": 4, "unpackedSize": 3211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhoCRA9TVsSAnZWagAAixoP/1L3TisP2MvnarYfExrz\nde7JoId5HsjbBSVFphtD4Jp7avHuAer/2BGRQBwvYUIVUdoW2oKNcksueHEd\nVAJXIs4QLDEt5ZePo5IHBZAlaUcsPnb8SNOHiygv3zAuoaqU4NZyODkMUzqm\nev7vJ9pPJi8homF4K1wAvhVhxW/4FSJ+FQpMK4cmHqRjIIZU4vDco3/QHwcv\nVRkhMlegaSZ1UbBFkfnJBIzHmBaJCv2y7FmHAkdSZEiNZDzM20FUvlp1Tkhm\ni8dl85BzQvmEnG+Z71GqS0cOtA39CkxRd2X7rbDgovDm/W7NwMMNZoBKqKHL\nC+7ZDg9gwXJr1JAQ/wXHJbEUsD5lFEd1Npvlb5vFs+tTl+JbQvOetBedAQD+\n865Ua8kTdsSVNN2i78v+rp85hVf24b7ifomQeGdnhdmKwmUtePMyNsA05RRO\nH9DOyyimewememKK0HKbiyLOrLIS83oH5LxaFHFwVKV9n4rrjSAaQguzju3/\nOtEgXVxktZoNrNBogOFO/HGRQibw+THO8UqwM7/3HzOfV/me8Hl5DiKhrtD/\nDxoPWNArJTI44EHIiEnEqz7bi8KSTuPb3+CK1U4sZB5yxGW8qyvlHnDzffSE\n00l5+JJ38EIP6k5CRUiDr5IsFlqPkyLGdGsKfaV1Q31QtidvdnSZx1C0yapQ\noZQr\r\n=Ea1U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEMzQ4rHCbLyXUsLxelL/spLhjBe13sbT23OXW9d5+V3AiBbFAq7eEUgcQAb1dJASmcOoVPuluVrQR28Z4Y6ZtcPQA=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.12.13_1612314728017_0.6125926146135852"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.13.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.13.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.13.0", "@babel/helper-plugin-utils": "^7.13.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "_id": "@babel/plugin-proposal-class-properties@7.13.0", "dist": {"shasum": "146376000b94efd001e57a40a88a525afaab9f37", "integrity": "sha512-KnTDjFNC1g+45ka0myZNvSBFLhNCLN+GeGYLDEA8Oq7MZ6yMgfLoIRh86GRT0FjtJhZw8JyUskP9uvj5pHM9Zg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.13.0.tgz", "fileCount": 4, "unpackedSize": 3216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUvCRA9TVsSAnZWagAA0AUP/1OrNxnM2UHq49h+/4rh\na8jS92uV+iZ5qi6n4RnL6DCM3K9px8Ri/v/B0k1MkaxZYyXjCe0r5TV8GxJt\npmN0ebc+FIVUIQ1gkOPfl8FxC4u1cDMP6U/MOR61eTf7JKAp1SqmlYoDei1a\nsG3KI/ISFOnMxFSJawBDnv8+wfPkriwN5Lkzl23LSJMrN23+vPX1kjdIxNHO\nImb1Nl+A+8lwluegWELF1MkYYJdvyh5AzBvAjQ9SbyM3WWRBzNsDkYnSglkW\nsX4rc7Opdj/ousojbD04Zhotn+YXDj5HTiYxyGe7myrE2bZzA6tBuFbkkO0m\nTv38IAE0iZgxs5Yn3QzQ21VciNQgIc98EIQZdsN74UYqVlZ/yQzmaTOQFO+V\nkUSNXTfCZvD7R91MkdqTs1Pl1g4MA9r+VFDJjjDYPBq4yx6hRFGSygwd0WBV\ng1Y+TZioGekD+V4SHtUZXebLNX8w7300LRlOjqHpvkXVnSLmoLYf81qiqIW6\naOl8GBYVGc5uhY6Qhr9Ti77Aq/6uVLZA0rLMo7Pa0Hzzoq6w+nEzZuYaVqn9\nHg5rtG10u0dKZtoEMhDLXVRae619XJQHeiLK1H5M4scfH5Bqp7sYXXe4wwRN\nH/nFjlr31xxqUtiG65pnPZslx0t7xxIphmqBxtGQgqqlLmFGz8BYJgVE6jpW\nBPdm\r\n=UQGG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7kN/dqK58glZ51lYD3kRgrmn9acMsWnIszmIKcn5OcAiA2hioAngl43s7t0RHRYJ4s2rqsCybnSOaCRbA+hMekVw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.13.0_1614034223392_0.11830292888077931"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.14.5": {"name": "@babel/plugin-proposal-class-properties", "version": "7.14.5", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-class-properties@7.14.5", "dist": {"shasum": "40d1ee140c5b1e31a350f4f5eed945096559b42e", "integrity": "sha512-q/PLpv5Ko4dVc1LYMpCY7RVAAO4uk55qPwrIuJ5QJ8c6cVuAmhu7I/49JOppXL6gXf7ZHzpRVEUZdYoPLM04Gg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.14.5.tgz", "fileCount": 4, "unpackedSize": 3314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsLCRA9TVsSAnZWagAA9WwP/i+ZFo8wBTMks7/9+Guo\nwO8irqyPgwG2v4kl7KXCu5XDQg5yaykzIC8GWi0XTOMwzVelNdvRmK20tsTq\nC8+HVVEL5bRRpXdnIGC8WwuYbNkfsYNI3624446T4mM0Tma7vH1l4DvSHqBk\nBCnfNkQRmmPXB2Y1iP/inWbebS8YY7XUbSfDWQzlyJRTS+kBrwBXaS2aQrqr\nLKCiPskEvbOGNx+lMp67W3H+BEIDAsgHkKgv67KoiMilf4u+0QhizmW7/DWM\nYe8xIVUi7n6K5MiM2aPUzjEzeN4RI67bRVQis4eUHljbvKY8rBPcELPWhS3L\nHqdLPPq6/6/SOdS7Mfo+4I0MMRVS3chm/cWIjdt2yoG6gFCMIFHEjjKWwOgQ\nagb9JmVlkRVRDDlyInL96+CJXbXY6OAVNKbv28CDm65RPne3lcBqDUI1BWMe\nPBWqSUaxhs6hTpJvY/onVOzwi6nED9IzcrUWHZ3XzgMM701+nHNxiac3GiLd\njfTHNCxzwjykk44DCqUo+EdLbawNKDsHjyRFD0x7/N2evOZmDln8SxyMCfo6\nVPrXaoafK6Z5BfyieUDOSBWDyYYkeXJLdZZjAUXhVyezA+ijUw9+lsdTZbbQ\nsuBFOipAQiq6UyYEfaIahDPYf+zyGAa5/u49/LkGvLgfX8gGWkl7m1BmO361\nP7aC\r\n=+5Ss\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHoNRSdE1OWwjgxd58/Azq5gRlT0Nrbc1KEUWPr6ivsDAiB3xFEAo7PzzcrEkMHkbuYtzB/KNjnyZF2DZvrQmt/RaQ=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.14.5_1623280395243_0.35630526873433577"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.16.0": {"name": "@babel/plugin-proposal-class-properties", "version": "7.16.0", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.16.0", "@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-class-properties@7.16.0", "dist": {"shasum": "c029618267ddebc7280fa286e0f8ca2a278a2d1a", "integrity": "sha512-mCF3HcuZSY9Fcx56Lbn+CGdT44ioBMMvjNVldpKtj8tpniETdLjnxdHI1+sDWXIM1nNt+EanJOZ3IG9lzVjs7A==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.0.tgz", "fileCount": 4, "unpackedSize": 3316, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFL4pQXh7ROEebbwViC0ch1fGPJu73bfFPH2flr7v5IAiBAWBpmvfajS7ztIQC5AEWPUY2klO+Nxrw/AN8xceu8Jg=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.16.0_1635551278043_0.9136708862639897"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.16.5": {"name": "@babel/plugin-proposal-class-properties", "version": "7.16.5", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.16.5", "@babel/helper-plugin-utils": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-class-properties@7.16.5", "dist": {"shasum": "3269f44b89122110f6339806e05d43d84106468a", "integrity": "sha512-pJD3HjgRv83s5dv1sTnDbZOaTjghKEz8KUn1Kbh2eAIRhGuyQ1XSeI4xVXU3UlIEVA3DAyIdxqT1eRn7Wcn55A==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.5.tgz", "fileCount": 4, "unpackedSize": 3316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8krCRA9TVsSAnZWagAAVqEP/22ZKC29FqFYQ9BPq3Wo\ntPRj60Q6M0rKTlAKH7khCYKPZHA7JHfLuxh5AkRl4JoMcDwqGaO31n6VWm5F\nRZc5I75sq5FAwWCdqbDMpeAMO2tfy0748ppJ5qMsGbTohe5sgIFEglm+DZjm\ntdO9IQLoou80IdMZU5DKWaj6r79U+SfKFOaKuuMZWWZ8lgqxFBybVXwXoB+F\nqymEmO0G1dVQ3HrClDdBY1WS2/aewWs9r/rDfRTtTRqFwBlrfJ9FWTNLq9Wt\n7hWmHpWh3TyYQ2rzHSclh+HdhcUymSH9NItQCmPwxtK/sbZdZtkwL84hfi6a\nlwQMfnPVRrghTtsRhIM7ZA1+33yuOR6DR3aWpA1yTNYhzI8jJJS36QayXvDv\noeiiJecKrOxMN1EGiWCSRIIsu4l0K/pgZcChKyj8AH56K9YfubvSSiK2hVAK\nJfk9oEuRcYDgFPfPZVOF4gWeUGhT2YzeHWI3IpF3M8P0u0y++2xRHcdWvYKD\nwge7zfa+xR5FoI2kSh03iL5LCMT0lsEuMIAtW4MHH5sC/+H5Ok4Ph9Fl7rEr\nvLCGOdOXqZjVa2sMm/5l7HSbyCoPOi680we7Y2lgPtwVmD/fmbiYvT8aDsmh\n8XqDMXnylqmnktoV8Tc3qtIBfoJ3TIaOp++dj79rJK/PFojjWKyx7Cm+lYYh\nrsOR\r\n=/xeL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxVdailtmnOflbEKqDJiSiJW3gxfmVLXN6ShyhDiVM8AIhAOhNkUZrbW35QcHoOz+jvGT7O4sTELZM3LdmVdw4a2aM"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.16.5_1639434539259_0.43241405780187003"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.16.7": {"name": "@babel/plugin-proposal-class-properties", "version": "7.16.7", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-class-properties@7.16.7", "dist": {"shasum": "925cad7b3b1a2fcea7e59ecc8eb5954f961f91b0", "integrity": "sha512-IobU0Xme31ewjYOShSIqd/ZGM/r/cuOz2z0MDbNrhF5FW+ZVgi0f2lyeoj9KFPDOAqsYxmLWZte1WOwlvY9aww==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.7.tgz", "fileCount": 4, "unpackedSize": 3316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1vCRA9TVsSAnZWagAARJgP/092ZDCPLGySnGpB51Ul\npiQ3xKLDt8Bnc7V7PcKFnmZHqH992cuYQT20wPzfcCbCMj5jNkGmXyv4rfku\nryLn/+D26qAQNjxM4D900LFo3pXHA0Rtv8LqG1338tG3SFh++Z7RxfFO4chI\n74CMoc+llxKMsiKU9ZEwJ17lVupDprAzSql/mbOAp+P8SjDUe9meUkQIwWg9\nS9O+DJXkL3rg/aHdF59mIMPe0mVOrMVWGWQQtbZIv8ZO9UFXsQCPz8VQsIWG\nfguOSpfDAOzms3flBht+3wBiw8f+T/791KXgUF3dDoFftQBzVvR7D7CTbFF6\ncQHDzx2+uFQPPy6+z555yb9xABvwrpeuviaLVepJJNXtZRwDqnusYdq3AuwP\nan70i3CtP3M28un7gHQtHEgPznqbRSC4TPKkOw1hK4HA1rQHqXV1M+M9YNLE\nq+JGSIH4Rye9yjRmCkMtR4lMk4TJM/PZ5rSFBuptlwk8l4dv4vOKDARp0ZDE\ncDJrmTxsWI3CxP3X3jJevlAKfFDb8tL+sft0Go28xW/yxfSBrOXwfk4ntkoQ\nE+quaDSYlhp3fchIAYhIHE0GEwy9sPq9PahS5+zBmpur58YZnZsIllfPiCls\nhSyg+qbdsGPzioDoyvjNNsWrFUd/FHW7ls9GR0YJp8ubOQhWgFyU6Bae2LLt\n8Xgd\r\n=mCP7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGeeyGzM7fliOm8IX/5w62s6aqwPVrvVVFx6qv1aqFP6AiEA2CErt5fjkbiyZE2TSXuwRF4oabCRKiWMuMQn7yGAxHI="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.16.7_1640910191386_0.5709954425624564"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.17.12": {"name": "@babel/plugin-proposal-class-properties", "version": "7.17.12", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.17.12", "@babel/helper-plugin-utils": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-proposal-class-properties@7.17.12", "dist": {"shasum": "84f65c0cc247d46f40a6da99aadd6438315d80a4", "integrity": "sha512-U0mI9q8pW5Q9EaTHFPwSVusPMV/DV9Mm8p7csqROFLtIE9rBF5piLqyrBGigftALrBcsBGu4m38JneAe7ZDLXw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.17.12.tgz", "fileCount": 4, "unpackedSize": 3320, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnkOfzk4YiiP3XCyg2UBys5/EDbHDplgx5Du8h+tgZ4QIhAMmVIp5c9/vaPOpz7XvqXqWdP0NC2zQR3+wrjoWIKeW2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqcA//etFKi6oJMkuwrD4NzeN+g3KY8FTLgugHjCIUQWS2uCw6TvA+\r\nhE+0nzFhpCZX6wsjhVUm05jgT3j3cwS0XFkq6dZM+HqhVtiPTvCwK0BwKEBE\r\n0EXpYg2frgMCFRtu/uyKju/p2UQtMj8QsHJmeW8uiHkPgxO0H4qFIl8Mjzji\r\nr8hSeptjjo/1+J1HCqbmQyRWldqaKsidlcTBA/lKUKqSefLnMsodU/++LbBg\r\nslT3gVxkEFR4zoIikzbYKHvfcUCxTo9Y5JHSr12gDcGqJAVBfnpR5eQ+ks+J\r\n0D//tshCtVsODSYKL8B0ZB8DXw4wi6vBRHKAU6gTn5FUiXA3cujodU+dj/d7\r\nPZi8q1fZBHiNX0pTCCo7clTDF8Cb6j/8My5CPbOtio2Htir+0BjTDj/RTFh/\r\nkB4qu8qUCvIV7nIEjLg8Xm/fllCKpTIP6i88KFfQR/c+1aQD5apCqMHbOvfF\r\nj3xMOODXfcIaPEx3xfyVRFgVR1SLwB/4sW4ktJL2CSvwkjcqCrKHAfh1I7Kq\r\nqXVKXl/oTm54j3+Ch110GTqP5E/3QLfQdFblpY5ZwvBGJBffv9iSOXHks4/a\r\nPJES23GGvxHNC07kY2063WvcBIGDpju3MstI8kxvyipN2GCAucTE0sYmBHjD\r\n0YD/q4bklvCzeYtnzcuzEdViVarW/oGFAtA=\r\n=Hinv\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.17.12_1652729542807_0.854734151430594"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.18.6": {"name": "@babel/plugin-proposal-class-properties", "version": "7.18.6", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-proposal-class-properties@7.18.6", "dist": {"shasum": "b110f59741895f7ec21a6fff696ec46265c446a3", "integrity": "sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "fileCount": 4, "unpackedSize": 3338, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDacnqHi1q/eU46XQw7pdB81A9NnCu5zP58//8KvkbBDAIhAPzvKvLGFoClcE5o4LTd4kHIBNA4tfQHe7JHolJhXz3+"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbcA/+MYMRc7fedOaME3dYfI+kRisiOhwRWSEkImITCqE51bp2bwrz\r\njLt/2B6q1vVHDAfgzyD7RRV0SU2dur6g1BtVi7hC3SSBE23go0WjxgRnpDuo\r\nn0sVobRBozd5YW8L7WTRm+GJTMuQ2xAATg/rxVAnvQ6xwPYHLy3QwecRs0Ym\r\nL/vyM/7N52RLyDAAmunK0xeEyZwgA0BPqgSRgQR7nTM4s6UWnG4WkyCe8SN2\r\nrUNZaqTTo/9bB8mQnQBLKMxj5rRqxyx2qBttT7afD88r/k0yek/l645tj3R2\r\n5sveSWUUkFs62tYk31ngb2g0tVFzrfY1z18nRMmMbcmGZwVDRXfWvZUsz4zJ\r\nquAxOgr+ZFcQo9DxF+glw7bRSW7d5OQh8ptWZ8r2CKOWphMDxPWgw2/Vijvf\r\n2FWHdmH3p2nuNVHOfYNHzEd6o20yvm7Uh/UGm4kyenWRstDy22v1wMQ0varO\r\ntro45Fd+JKEV/PRzuuZbbV2TlwXK1dK24v5U5bSlgkWVI1j42BcjyVO9BAnD\r\nmMtUylDKLcN8xsz3CoV+0+hjt/2VdYKooguVrdsPDi1UDW9W4V/CISYGXbh4\r\n2hUuMEkPdOEM/ow5SjumXKoc6w3gne9scgSJ5OQvuTjv1Vxj/BW6k1bpIfhr\r\nDF7Jwg0LOxMYYpq1STDzRttXdb6J+xcutjg=\r\n=NhKM\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.18.6_1656359444760_0.8218701134966078"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.21.4-esm": {"name": "@babel/plugin-proposal-class-properties", "version": "7.21.4-esm", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.21.4-esm", "@babel/helper-plugin-utils": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs", "_id": "@babel/plugin-proposal-class-properties@7.21.4-esm", "dist": {"shasum": "57e276c620fdffd1e6a750be85465b8f3d5314fe", "integrity": "sha512-Is/msVrKvxBC4SxJLSHuZVck9Xey/49Tq/Np8OFH8e0yr1aJ7Trut/Tju8+SZfypOSBKfYQMcxEfIQ+0HFvTPg==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.21.4-esm.tgz", "fileCount": 6, "unpackedSize": 4904, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAp85lwPpA2AhWpFUzFmnhcKgsiXzV/H+/9yMs6D+xryAiEAjPIblhuHXkBJbp91OXN9gx/3HcP7u7mkxK72EFhuvQA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1cQ//UwGOkcA1qPT7TcKFZIw8X7BOidm0YZ0T4BYBXJSLB1s5kBGI\r\nPl2/L+c4CaSbzqvvVx7PcV7xXMKqjgUOahQIxG4XtH0OkjOZpfPDcBmfQCWb\r\n/KsQXSy9FBhJ5H5zL9O6/PrJhds5QntX56GWrTWFBOoxZf0iyq/F3A7Z7sZv\r\nGgS+P+yOfkMzhtg5pmyc17ATttDWuDEWHUvf8LifP/Nn87BjODuCyRZlNphL\r\no7Grl7SIGX6cDD0IeLHsHkLrnZClP6G5zL0+C4tuxOw6E9IxJQCL0x4EOboK\r\njoup4GxWh4pclViDvEESFB2WNMFjz9llXWvW1nVCRPNDSYmCiNK4tWwO3SQl\r\nWbAzwjZCMdtWxPurRdi6Hd2ywjQjaWKd0k1BbAUvkuRXY9BfAh7vV9/8GbiX\r\nc11OoD++eWDaudL7licIQMhCGgl5cc+ysgJkRjIQxip5aF7/Z96kP7Fc3HAN\r\nA542V3gcRT5F98SMV2qgohy6h7IeFMCypkotPA2+gPXVmqmL/6KmiXT2hefu\r\nWtOOl87dQFsU+ciPKkkUYNx+6nZ+/k9C8Wlhjm5tazSUGZtaMVfWWEZB/K1I\r\nfqsdwi0udP2k1733lLKAa5dTyg96FlDkr3kAYrCeXYnLUZySu7/Yks/Bb9BQ\r\nUgxzANUg8S4HT+kFga8UtKLwSz8p43LOa6U=\r\n=hHQD\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.21.4-esm_1680617400482_0.8262432901796182"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.21.4-esm.1": {"name": "@babel/plugin-proposal-class-properties", "version": "7.21.4-esm.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.21.4-esm.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-class-properties@7.21.4-esm.1", "dist": {"shasum": "369330e7496515089e9a854a4a369447c7d5aea8", "integrity": "sha512-up1SRu2SBN3MPhT//6TkW84iaKY7FGitGiEu2xU9/ciYbrtumnHZRXA0C0lyIrNMBU4N2cEv0Wo7lMrVto4ing==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.21.4-esm.1.tgz", "fileCount": 6, "unpackedSize": 4532, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICUY/lxvzuqCvBhE1tH457SUPOi6d/uSjAJ/L8BCtANVAiEAproTFwn7LnUyp+3QncmSpQk9YzqieuzAYBe8EF7wui4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUdQ/+NT7aezIrPizKkOSfWqWidWDDpioVtNPYBT3V4YmF85xjduZO\r\nmPomofXGASq5yBEE7ayN0G81kj0xDtYAZ6LcmX/MBGruK/tcH69MBv5bY+aQ\r\nNyb0XqHV2o8KVT6Yh3Dgz14wTfBspALFxz+WZith+2rAVazX+cpdbx7XoZ7B\r\n+gWLL8TgExXVffqPGFio0OCKEhc8EGfI1jPKg40r10j8vQcf8q2oJbtFfrHD\r\nVSs/IcLLd34qg6a67RdVCJZ1Qzaky3OY3X/lWSpJBXly397HomXiWhFVcEm6\r\nd8c/pNFDiEwCoV+cYtsYYgB6LQOnYd1iw1P3mYKCyHOBBsplBAUqvz+ynTB8\r\n0HlgRDPRsrNqA722sTEZw/juX0nopBqEi2WsVFVY9R55npBy0vpewITBrv93\r\nZhO3o2enIe4CnaIsJqtoAbdS43gsPVdjB3fboLvEEzij3V+a8IphvWNf1hnF\r\n4gW5qKb21FvvpdbCuOyoAzCeUTh2MlLfLLnGu0RvQEF8N6IDeyZfgNmKgFmn\r\nGbuUBfh2kSpv5G7W+IX6LGt9x6+OtMEc3EKfF/dnVZizl7WkyERQUptIS2pX\r\nfm1xxs/hMaxzxkBW53AohaQ/dycV0zAr9MetDE/7cng3zklFiUCuVEfDiWdI\r\nszOPexYlAk7vHnvFXvXM6LrI/i5FrlPQ+k4=\r\n=CEs1\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.21.4-esm.1_1680618117636_0.43612575006029597"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.21.4-esm.2": {"name": "@babel/plugin-proposal-class-properties", "version": "7.21.4-esm.2", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "7.21.4-esm.2", "@babel/helper-plugin-utils": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-class-properties@7.21.4-esm.2", "dist": {"shasum": "b8531123da47f3f6f327110b6dbd28abfe1df8de", "integrity": "sha512-XNfH8LlqOJFWFUlPjUpHu2etnkoT5Nh+sIy+bq/3vndaJxSVDy6Swg2Qk8t9n3m/qW/ngZ8QU99jg6FoCgsuuw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.21.4-esm.2.tgz", "fileCount": 5, "unpackedSize": 4509, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDE3F7rc6nf9Na2xfejQggYVuxsKJgtJGGZALJHywmvZgIhAMJXmfXSF4x6Fycxlqi01ZV3jhadaAyfmjunKHQ/rvBA"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLGg/+JOr0moxIaGkCz8ywEiZc7gzxulH80U4Nm8a7hk/Y9jBoVVyV\r\nag6Kb38cniecEVav/fWzlMnSucjL7elEcMwR79nSEgVVYlv2r6OJJlIBd2Yw\r\n6RWqpajXbw8l3MwOjzJJkuW6/UgV28vbAnOEkTfB6VwkxDKTup+29bu3AygU\r\nX4TKrBn60rdxkPU51TcptDbNnnZVq6M3iv24pFFCRLtDod3HEUulfgUcz5D5\r\nFojBzBXt7uyGiHYpazIhEoxgM3J+lv7oRJRLtqHtUmE9Yo8K43qL9WEU3Ivs\r\nLt/WH+7mXxhSrMXKXSPyTwQ1pGog3wRcY7vMiQdyKigvYGl5IonqntDiB42x\r\nyS21fs0lN+HqO/RNs6pnxtTycNRJOs4OBF/HRLwIALYv4HDY2FNlFta+Oo9l\r\nNANwq9gJbyT47kpbNjuQJjFOympxt93j+VWrdmdUwZ16svkrmXb/zanPm75B\r\nNs1NnUVPL7wU23pH6Wpxk/QCnpEPuJL5L8bEbXiaPemM0DdoClCdJ9PWdXw/\r\nNHQ1oJ4hMwoOIa39IJfyibd1HW8O3e9RzC+/zxvu1/Xz74whHM4ASUABCFP2\r\nmDegfvKx1yh0F4YX5kbbFpRe2CLZ0+slzH/bNPE0dPEL6xwDMPO0utcRjDp7\r\ncUTyGEqfWXWDNERN+vcd5q9eGZCJgYqSKMs=\r\n=yJ9m\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.21.4-esm.2_1680619203394_0.9837114183867719"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.21.4-esm.3": {"name": "@babel/plugin-proposal-class-properties", "version": "7.21.4-esm.3", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "7.21.4-esm.3", "@babel/helper-plugin-utils": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-class-properties@7.21.4-esm.3", "dist": {"shasum": "bf94c0b59cf8726416382c09d4658f55db812cce", "integrity": "sha512-5haeFWW222y7jINMXWlfuWZv8cl/R1QFF4x73VBsw7CWQxHC+opSUMV+Bgub3HvRDG09ZyOvEuGdu/YS/2vrIw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.21.4-esm.3.tgz", "fileCount": 5, "unpackedSize": 4891, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYhLAldJA6C3UQg9i7ubtYtxaGuFzaDVGPrBaYw6lL2wIgVHbmMBdQLPkOLwyJov7ccjxn03pxGY1KZvwLBenz6Qc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/cQ//TZY1OBz4b2zJWz1chpiatFykJdbMRVUT93+89ccJCZUDzN/n\r\nagFA8CaO6GM/k77x8gnCYZYDNDkzld15pCSh29Qw48Tr6b+PWh4gExoMrXYq\r\nyc/sXlDY1k/7WMGr3UFInsh1iNNicc9a202xI7aj16TwzowD9hIT7t8Z3fSQ\r\nCJ4oYdeXo0SuVFYaCQ71v+VeSlT99htZ7b6w+6+u++Apw0HyP21+UBiCpR46\r\nQ/CiVBHS3hXX/ueFs496RR2ghwXSdOA2tJW0HQN1gGgWiBWZUfFZ8hpqap+f\r\nCtwvE1Xbj7q5L6dbrbaJnPM/I2W9myiR2IvC5k8S+qMMoHVSzEza/HS/pEtD\r\nEWczc6lN8K8xANU6AGSA1c6oLCYM+84eHKnPsCYlxuLgo+ZxcHwus0+qRe7p\r\nulBWzxginygZld7H4MCWLretE9iRFOs4x4HTtEs4eVKtuwQQZSBjd9J0qEbt\r\nauF/Qb9bsbFwVXWzk6zGBpJQtgpqrtUH1edDU8tsc2kVZdv08SK7jxtIx2Gs\r\nFqxQe6Q+ZgeYhr25k6oBYd1oXRUG5ffs7w5KeVShH6ZXjluKp1uhEMlVX0dv\r\nLNdGII0stESou3EFfK42tzywIk+Ny8PELaMgkHdYsmHa7hhACqioQzjWKUSA\r\nPNhhJOMVdWY9N/mFXOLhdLdtMBiF5VkBJ3E=\r\n=Yo/+\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.21.4-esm.3_1680620205485_0.5572885819589557"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}, "7.21.4-esm.4": {"name": "@babel/plugin-proposal-class-properties", "version": "7.21.4-esm.4", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "7.21.4-esm.4", "@babel/helper-plugin-utils": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-proposal-class-properties@7.21.4-esm.4", "dist": {"shasum": "9fdfe094ec550e14c784f06a1d85149d3f4d8c25", "integrity": "sha512-FGAxsi0vlhYx7P62/ttZ9Mu8DobyvzbU/Ey8nhybTNKEjGp8rlxX5nVAh6BNxNtc+9SRC7dHE8Ed6LUc3X2vWw==", "tarball": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.21.4-esm.4.tgz", "fileCount": 6, "unpackedSize": 4529, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/bxhbAc35TejX0TXOdP3f0PngrYHHyBDJK/9xVYyl5AIgFwtcec77Ee3NfggFcznravgTkHensPR5bKv7nw3INvo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptwxAAmKlzcjqYE4/GuiiaMO/PD1TAWu9qD4BCmk9YQcFlAQjjHP4j\r\nh4aFVcp6DwLkwnKNCge4VFLFiNaUHDFecaeB85i85M2GVRx6opH8zKmzemWs\r\n+eC52QDnAUIE6jnY8G0wEO0Fb254fMzkGik5ntvKyarHDAS2+QQmjB5AQkWB\r\nEo9P29qpunYUleoE3yeRBeUkuR82AeF0kmetXiqNHB0D1kVXnJkho3ZcFMSg\r\naM6xyZW4OxVW7WA7UDeVnF4c7lVUmCTh5FL4EOoeVCZ6q6J4x3pdA6HsxaL+\r\nYOQ62S2AJTU5KcP3hEdI4s7pVzOihkSZBGCrowNMLCQFikPiSsCU6JQNf9qM\r\nCafDJ6K3YXW3D3tltiX76RDJc/6daQtczVnBwl0Sn8fQO8ci32Oqe2735kzH\r\njC2+lDBjB2sakhOJugv8dP6EeAn2WOrBqZJWW89a1pFG7agnqTrMjHyGCTT0\r\n/Dn1bb0oW8xIReypn1SilESnJSf23+xZEOo4l5WpgvfDzIBkZjNAP1X+tymr\r\nvkGrnwzspR0vSKtuyzhU9PE9lEHwwDaUxyonMhUVTAREj6SqyHE+7/TkBxsc\r\n9BqV61tK+HEUD8OovpBhsnTbGqCe7/ExYNmhtdhc7hC87E5u2bYpOxua0aEw\r\nudI4hWfMkWXMwD5gAAa3cyr6nzHIeOo0v8w=\r\n=hM++\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-proposal-class-properties_7.21.4-esm.4_1680621233227_0.11834423343121858"}, "_hasShrinkwrap": false, "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead."}}, "readme": "", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "time": {"modified": "2023-09-01T18:53:36.827Z", "created": "2017-10-30T18:36:19.808Z", "7.0.0-beta.4": "2017-10-30T18:36:19.808Z", "7.0.0-beta.5": "2017-10-30T20:57:57.125Z", "7.0.0-beta.31": "2017-11-03T20:04:31.889Z", "7.0.0-beta.32": "2017-11-12T13:33:54.738Z", "7.0.0-beta.33": "2017-12-01T14:29:11.576Z", "7.0.0-beta.34": "2017-12-02T14:40:08.649Z", "7.0.0-beta.35": "2017-12-14T21:48:23.189Z", "7.0.0-beta.36": "2017-12-25T19:05:31.138Z", "7.0.0-beta.37": "2018-01-08T16:03:36.754Z", "7.0.0-beta.38": "2018-01-17T16:32:34.636Z", "7.0.0-beta.39": "2018-01-30T20:28:40.795Z", "7.0.0-beta.40": "2018-02-12T16:42:33.041Z", "7.0.0-beta.41": "2018-03-14T16:26:44.329Z", "7.0.0-beta.42": "2018-03-15T20:52:01.461Z", "7.0.0-beta.43": "2018-04-02T16:48:51.944Z", "7.0.0-beta.44": "2018-04-02T22:20:31.604Z", "7.0.0-beta.45": "2018-04-23T01:58:53.797Z", "7.0.0-beta.46": "2018-04-23T04:33:06.220Z", "7.0.0-beta.47": "2018-05-15T00:18:06.931Z", "7.0.0-beta.48": "2018-05-24T19:25:03.070Z", "7.0.0-beta.49": "2018-05-25T16:04:55.362Z", "7.0.0-beta.50": "2018-06-12T19:48:18.503Z", "7.0.0-beta.51": "2018-06-12T21:20:59.861Z", "7.0.0-beta.52": "2018-07-06T00:59:51.231Z", "7.0.0-beta.53": "2018-07-11T13:40:54.362Z", "7.0.0-beta.54": "2018-07-16T18:00:31.573Z", "7.0.0-beta.55": "2018-07-28T22:08:35.816Z", "7.0.0-beta.56": "2018-08-04T01:09:10.167Z", "7.0.0-rc.0": "2018-08-09T16:00:27.883Z", "7.0.0-rc.1": "2018-08-09T20:10:08.372Z", "7.0.0-rc.2": "2018-08-21T19:26:14.883Z", "7.0.0-rc.3": "2018-08-24T18:09:58.985Z", "7.0.0-rc.4": "2018-08-27T16:46:49.073Z", "7.0.0": "2018-08-27T21:45:10.125Z", "7.1.0": "2018-09-17T19:31:21.992Z", "7.2.0": "2018-12-03T19:02:21.094Z", "7.2.1": "2018-12-04T15:21:53.006Z", "7.2.3": "2018-12-20T11:13:26.751Z", "7.3.0": "2019-01-21T21:38:30.515Z", "7.3.3": "2019-02-15T21:14:27.239Z", "7.3.4": "2019-02-25T18:36:02.601Z", "7.4.0": "2019-03-19T20:45:30.040Z", "7.4.4": "2019-04-26T21:05:04.645Z", "7.5.0": "2019-07-04T12:58:13.054Z", "7.5.5": "2019-07-17T21:21:58.405Z", "7.7.0": "2019-11-05T10:54:08.732Z", "7.7.4": "2019-11-22T23:33:56.611Z", "7.8.0": "2020-01-12T00:17:37.089Z", "7.8.3": "2020-01-13T21:42:38.824Z", "7.10.1": "2020-05-27T22:08:36.753Z", "7.10.4": "2020-06-30T13:13:32.148Z", "7.12.1": "2020-10-15T22:42:02.751Z", "7.12.13": "2021-02-03T01:12:08.158Z", "7.13.0": "2021-02-22T22:50:23.521Z", "7.14.5": "2021-06-09T23:13:15.418Z", "7.16.0": "2021-10-29T23:47:58.168Z", "7.16.5": "2021-12-13T22:28:59.426Z", "7.16.7": "2021-12-31T00:23:11.542Z", "7.17.12": "2022-05-16T19:32:22.989Z", "7.18.6": "2022-06-27T19:50:44.929Z", "7.21.4-esm": "2023-04-04T14:10:00.631Z", "7.21.4-esm.1": "2023-04-04T14:21:57.812Z", "7.21.4-esm.2": "2023-04-04T14:40:03.611Z", "7.21.4-esm.3": "2023-04-04T14:56:45.638Z", "7.21.4-esm.4": "2023-04-04T15:13:53.478Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-class-properties"}, "license": "MIT", "readmeFilename": "", "users": {"dbuggerx": true, "flumpus-dev": true}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-class-properties", "author": "The Babel Team (https://babel.dev/team)"}