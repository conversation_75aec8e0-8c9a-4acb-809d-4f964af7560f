{"_id": "rrweb-cssom", "_rev": "3-358679e8fb20705973056fa5744a901f", "name": "rrweb-cssom", "dist-tags": {"latest": "0.8.0"}, "versions": {"0.6.0": {"name": "rrweb-cssom", "version": "0.6.0", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "rrweb-cssom@0.6.0", "maintainers": [{"name": "fitz5264", "email": "<EMAIL>"}], "homepage": "https://github.com/rrweb-io/CSSOM#readme", "bugs": {"url": "https://github.com/rrweb-io/CSSOM/issues"}, "dist": {"shasum": "ed298055b97cbddcdeb278f904857629dec5e0e1", "tarball": "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.6.0.tgz", "fileCount": 26, "integrity": "sha512-APM0Gt1KoXBz0iIkkdB/kfvGOwC4UuJFeG/c+yV7wSc7q96cG/kJ0HiYCnzivD9SB53cLV1MlHFNfOuPaadYSw==", "signatures": [{"sig": "MEUCIQDJ0wfAn9tKkgka9C7KE6+D/eoCfui7b/aB+mlf+J/lLQIgDLDIe14dcHTb0Yzu8nt5at8DNxlamAPRY2ciWPPFlLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivacGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/6w/+MpWc5or9pVjC2+T49OPLeYdfS6Cji+n9p0cnQw/gpys7G9bF\r\nzoo73drIgCl7NPxES81e9JKHdT2jRX+rawH81qIBneYDzNd7NWr7xMAL7mBi\r\nPtXzzKk8V1dngL+x4vtB2P6tbeeW5iMjoOWnL66/ZooOPLDtYfzZVXb/3dtA\r\nLQA2lFg4QPYgzYYO0jH3YsrYgEHfdCL6wXjPBAQFL6VYpqU/RFgvKeLSHVGC\r\nudFLSR+ZbRV9CXsu+BZNwZIMNk01y7hwq3jPRZ27K0iCEyuBX0YPj8948LzD\r\n3ngdovXUQKOjejscxXg8F8nPRWzwRXUm8MLus00MMWJyvTTsbF54zeQQuyEu\r\nsl5mHayuteiIUT6rQ4wK3xz1Y06zN0B20Z+h0viAf5ypbofGnHpLp9HUwxdA\r\nkl7dKEifG3OsU8o3iWUjtAj91qFHe0RYkwMmwzNFi+dDmon9V13okiUii+UK\r\nWKIUoedtSbEFOn6tHOs8lIo3ZGujuQ4D2X7I5gR7bsSrC9X9A2EpYBNBL3PW\r\nNpXQ5wcLqr0GHOm1E511n/t4E75FcX/ctfltC6WvL3MVJtzjrj/aZGtqOEIh\r\nFYjrk1c8wyLhyQhUelLElafSiPi6KF1HAqoQmnWwfk8UGRbmZZuBsR8s7rU0\r\nxZ+Y3Wj6D++gZf3/qEw8vCyCzg5hmg9AUe8=\r\n=lYCP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "9fa4edc7f99a2ab6f2a5fa9447d2f2cefc5faa7e", "_npmUser": {"name": "fitz5264", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rrweb-io/CSSOM.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "CSS Object Model implementation and CSS parser", "directories": {}, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rrweb-cssom_0.6.0_1656596230449_0.5949170759980786", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "rrweb-cssom", "version": "0.7.0", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "rrweb-cssom@0.7.0", "maintainers": [{"name": "fitz5264", "email": "<EMAIL>"}], "homepage": "https://github.com/rrweb-io/CSSOM#readme", "bugs": {"url": "https://github.com/rrweb-io/CSSOM/issues"}, "dist": {"shasum": "51cc1e7f4c20dd81218545b5092939bc6fd81bcd", "tarball": "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.7.0.tgz", "fileCount": 29, "integrity": "sha512-KlSv0pm9kgQSRxXEMgtivPJ4h826YHsuob8pSHcfSZsSXGtvpEAie8S0AnXuObEJ7nhikOb4ahwxDm0H2yW17g==", "signatures": [{"sig": "MEUCIFd/cS3Lsfmsf6ySegT71JnbIQ2nAYRDnJKsQ5whWT0LAiEAwb9CGB+jSBzUsECjHHVwgkRrtzUbH6gPX9yKA1ou5p0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101435}, "main": "./lib/index.js", "gitHead": "07d1b24524e6f1b3351f1feb072ca25eb632a286", "scripts": {"build": "node build.js", "release": "npm run build && changeset publish"}, "_npmUser": {"name": "fitz5264", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rrweb-io/CSSOM.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "CSS Object Model implementation and CSS parser", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "devDependencies": {"@changesets/cli": "^2.27.1", "@changesets/changelog-github": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/rrweb-cssom_0.7.0_1714636316389_0.33132480069985837", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "rrweb-cssom", "version": "0.7.1", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "rrweb-cssom@0.7.1", "maintainers": [{"name": "fitz5264", "email": "<EMAIL>"}], "homepage": "https://github.com/rrweb-io/CSSOM#readme", "bugs": {"url": "https://github.com/rrweb-io/CSSOM/issues"}, "dist": {"shasum": "c73451a484b86dd7cfb1e0b2898df4b703183e4b", "tarball": "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.7.1.tgz", "fileCount": 29, "integrity": "sha512-TrEMa7JGdVm0UThDJSx7ddw5nVm3UJS9o9CCIZ72B1vSyEZoziDqBYP3XIoi/12lKrJR8rE3jeFHMok2F/Mnsg==", "signatures": [{"sig": "MEQCIFIl+rTROu1OtipqJwvCCyxTIpcTIoPXnws9gW3AgL0pAiBhVU2acC2oT9XA5tdhomw0s0QboEYZhj0fcTRFfD3GmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101567}, "main": "./lib/index.js", "gitHead": "6aed5310c4674b70095fd493528eeec73787909b", "scripts": {"build": "node build.js", "release": "npm run build && changeset publish"}, "_npmUser": {"name": "fitz5264", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rrweb-io/CSSOM.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "CSS Object Model implementation and CSS parser", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "devDependencies": {"@changesets/cli": "^2.27.1", "@changesets/changelog-github": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/rrweb-cssom_0.7.1_1718094023200_0.011409918651859696", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "rrweb-cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.8.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/rrweb-io/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "scripts": {"build": "node build.js", "release": "npm run build && changeset publish"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1"}, "_id": "rrweb-cssom@0.8.0", "gitHead": "c92a277c1e8f86a682b1b29a9554f8b564bc240e", "bugs": {"url": "https://github.com/rrweb-io/CSSOM/issues"}, "homepage": "https://github.com/rrweb-io/CSSOM#readme", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw==", "shasum": "3021d1b4352fbf3b614aaeed0bc0d5739abe0bc2", "tarball": "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.8.0.tgz", "fileCount": 30, "unpackedSize": 105478, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8mSx/O7SQiJNzqouF1LabKDDlZ7WpLjBbZF8aEltd5AIgc/XGNwDcoKEsgnHhQUXgXGnI1G6/TArmsZ7d7NEJ1AU="}]}, "_npmUser": {"name": "fitz5264", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fitz5264", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rrweb-cssom_0.8.0_1728920434207_0.13040370637599596"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-06-30T13:37:10.392Z", "modified": "2024-10-14T15:40:34.575Z", "0.6.0": "2022-06-30T13:37:10.586Z", "0.7.0": "2024-05-02T07:51:56.553Z", "0.7.1": "2024-06-11T08:20:23.417Z", "0.8.0": "2024-10-14T15:40:34.427Z"}, "bugs": {"url": "https://github.com/rrweb-io/CSSOM/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/rrweb-io/CSSOM#readme", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "repository": {"type": "git", "url": "git+https://github.com/rrweb-io/CSSOM.git"}, "description": "CSS Object Model implementation and CSS parser", "maintainers": [{"name": "fitz5264", "email": "<EMAIL>"}], "readme": "# CSSOM\n\nCSSOM.js is a CSS parser written in pure JavaScript. It is also a partial implementation of [CSS Object Model](http://dev.w3.org/csswg/cssom/). \n\n    CSSOM.parse(\"body {color: black}\")\n    -> {\n      cssRules: [\n        {\n          selectorText: \"body\",\n          style: {\n            0: \"color\",\n            color: \"black\",\n            length: 1\n          }\n        }\n      ]\n    }\n\n\n## [Parser demo](http://nv.github.io/CSSOM/docs/parse.html)\n\nWorks well in Google Chrome 6+, Safari 5+, Firefox 3.6+, Opera 10.63+.\nDoesn't work in IE < 9 because of unsupported getters/setters.\n\nTo use CSSOM.js in the browser you might want to build a one-file version that exposes a single `CSSOM` global variable:\n\n    ➤ git clone https://github.com/NV/CSSOM.git\n    ➤ cd CSSOM\n    ➤ node build.js\n    build/CSSOM.js is done\n\nTo use it with Node.js or any other CommonJS loader:\n\n    ➤ npm install cssom\n\n## Why is this not maintained?\n\n1. I no longer use it in my projects\n2. Even though cssom npm package has 26 million weekly downloads (as of April 17, 2023), I haven't made a dollar from my work.\n\nIf you want specific issues to be resolved, you can hire me for $100 per hour (which is 1/2 of my normal rate).\n\n## Don’t use it if...\n\nYou parse CSS to mungle, minify or reformat code like this:\n\n```css\ndiv {\n  background: gray;\n  background: linear-gradient(to bottom, white 0%, black 100%);\n}\n```\n\nThis pattern is often used to give browsers that don’t understand linear gradients a fallback solution (e.g. gray color in the example).\nIn CSSOM, `background: gray` [gets overwritten](http://nv.github.io/CSSOM/docs/parse.html#css=div%20%7B%0A%20%20%20%20%20%20background%3A%20gray%3B%0A%20%20%20%20background%3A%20linear-gradient(to%20bottom%2C%20white%200%25%2C%20black%20100%25)%3B%0A%7D).\nIt does **NOT** get preserved.\n\nIf you do CSS mungling, minification, or image inlining, considere using one of the following:\n\n  * [postcss](https://github.com/postcss/postcss)\n  * [reworkcss/css](https://github.com/reworkcss/css)\n  * [csso](https://github.com/css/csso)\n  * [mensch](https://github.com/brettstimmerman/mensch)\n\n\n## [Tests](http://nv.github.com/CSSOM/spec/)\n\nTo run tests locally:\n\n    ➤ git submodule init\n    ➤ git submodule update\n\n\n## [Who uses CSSOM.js](https://github.com/NV/CSSOM/wiki/Who-uses-CSSOM.js)\n", "readmeFilename": "README.mdown"}