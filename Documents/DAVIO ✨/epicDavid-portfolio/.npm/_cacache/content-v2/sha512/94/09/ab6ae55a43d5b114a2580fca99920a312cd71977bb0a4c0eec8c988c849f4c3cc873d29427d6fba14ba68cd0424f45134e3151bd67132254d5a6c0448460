{"_id": "@vitest/runner", "_rev": "138-72ca059545f92e6469919d10d667356c", "name": "@vitest/runner", "dist-tags": {"latest": "3.2.4", "beta": "4.0.0-beta.2"}, "versions": {"0.28.0": {"name": "@vitest/runner", "version": "0.28.0", "license": "MIT", "_id": "@vitest/runner@0.28.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a21237371e1a3b5dd6f5f3f48b919ff30e086aae", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.28.0.tgz", "fileCount": 14, "integrity": "sha512-SXQO9aubp7Hg4DV4D5DP70wJ/4o0krH1gAPrSt+rhEZQbQvMaBJAHWOxEibwzLkklgoHreaMEvETFILkGQWXww==", "signatures": [{"sig": "MEUCIQCrVEMXrDqMNFyUmWZzLn93eXVMn7k067Uc7KB7N6kWygIgFWRLsqkMTs3vxGFfX20449gdW6mPW+SkPU1mVgoyQO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzlMuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiRA/8DP3oLqEY81XxNAxWUwqZxennxYDdHb+mmbK5ikbpTfcGkVV+\r\n5fqVhSLc7+GB9KeHsrIOHqbzRQlkTH3gRmyzjyTpiflJLewoxvV7qB3twUQI\r\ndhwV7gaBBxCUGKWEW9ktr0sp9qxqIaFuwAWt9lq9Gnv3I4YKaO1F3t5CZVG2\r\nmPWhI0Rqp+1XLuqkf3ZDhWB1XEArplr4AZvhYkyDJvVuceJGEinEkLwOniHL\r\nQa53HbehHqi6eFkJUZEjOB5g24EmsED3f4wKe28delJvft0lePJIIMN2AcX/\r\nqqfIbFEGMU//K2CqyjO3YU9nseb/1XeJ1H0QVjiGRJSOnLCBW2h6Pu/FBsSf\r\nPqAh5bS63IY0skrePnxoepLweVIc1If3YMkH+UGVeUqOgHcyv7Tx9t93riP2\r\nrGpCP9RXV3ZI9niq4dR6kAS89M/Dpsi79wv3UjGhtvDRSJ8ZiZjkwynVfGJT\r\nVguJq+WZTC80y+LIrrGi2/nFSzddwZ+HKi3grXwIAkJCV+P011IIqkwg/bIt\r\nC5B1wWTF5bse4t2jHgKzl9fp+Lr3S/JrdRX7FwxFUVfU44I45c354RkGfkW0\r\n5YTqiSppry8iIY1/9TxNibZOpJCLVP/V0f9Vhi49DIGpl5x1wqI3+Y5NJ8f7\r\nDsnrKV/u3+MCfiEAWX8V5ihxjcC+tccH3P0=\r\n=1VPC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.28.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/70451203792ecb4e44bb5cf4938ac507/vitest-runner-0.28.0.tgz", "_integrity": "sha512-SXQO9aubp7Hg4DV4D5DP70wJ/4o0krH1gAPrSt+rhEZQbQvMaBJAHWOxEibwzLkklgoHreaMEvETFILkGQWXww==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.28.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.28.0_1674466094460_0.5622634708247838", "host": "s3://npm-registry-packages"}}, "0.28.1": {"name": "@vitest/runner", "version": "0.28.1", "license": "MIT", "_id": "@vitest/runner@0.28.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f3c72befec05ef9a3565de7de9974b19f2ff7275", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.28.1.tgz", "fileCount": 14, "integrity": "sha512-kOdmgiNe+mAxZhvj2eUTqKnjfvzzknmrcS+SZXV7j6VgJuWPFAMCv3TWOe03nF9dkqDfVLCDRw/hwFuCzmzlQg==", "signatures": [{"sig": "MEYCIQCqdLZQJGX7a9/Pgv9SsXwaMsiXzp1z9Tq8ODtG1gNe/wIhAMOPl6aQWhSNZ3wUN6ABf3MF1IEFpGiri6TZSDH/sClb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzliXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfoA//WW3h+/tZqh4R0nHPSw0GsdtTjAnpZOq4IyRifdGPM7+925vs\r\nXbi3wfAA9/qO6Jg44yXPtOO4KOf0hdy6Yam4ZNCY53ZopVc9NP174Uw2zlYH\r\nJVKUpoUKYHLxRrUU+NfAwH07nwkROai02qNiWT1WKJKLaPljv4lhcpfQ/ngI\r\n2PQVsnw/APTlADAiIY7M3jw0gsndj0OqApqbPGSORjL8q5YfitE/vpkHxOaf\r\nvzMtFMoo7OglhcPwCAGs88fmI4SxtekPkE68v3MpAMu0iNZv8VH6Atm5frH9\r\ntEqW/QQF5Ll8KdI2saVgWgIusUSyaLik5YbgeqVZvI7TPS7HbX/vThWZLOTi\r\niORVcBUItEKCIM0nh1WbWzC8t/0Gcr02T7OJltJwDb+mp7rWHcrMzHsRWIoK\r\nnQndDwrBezDZkpzbuOHh3IBucaO1J7w3ePDxUCZdI2CrfgjBIWHG9vmJib9+\r\n31vWsOLR7hbjb3DseX+qsqkT3udpfeEKS3SJfYBRldOvq4kat/OjpS2g7iiE\r\nszhThQhjKi3SNyK8AUeAqVbTXX653DT9CdRqr5fL28cn8scRwUP8wbo8CZrI\r\nGt6Dx8TsEr9FPuZJu4XcW35Ox+0LnmjLNLtwUDktQmEP0KpVcPy02aRC5kvw\r\nc2a/ZRQ2fA0+Fb8pKcC67f2ocgebqvcOTY4=\r\n=mKku\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.28.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/771dfb94eb7c2bd2229d36bdaf554d37/vitest-runner-0.28.1.tgz", "_integrity": "sha512-kOdmgiNe+mAxZhvj2eUTqKnjfvzzknmrcS+SZXV7j6VgJuWPFAMCv3TWOe03nF9dkqDfVLCDRw/hwFuCzmzlQg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.28.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.28.1_1674467478935_0.8968661231571049", "host": "s3://npm-registry-packages"}}, "0.28.2": {"name": "@vitest/runner", "version": "0.28.2", "license": "MIT", "_id": "@vitest/runner@0.28.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "671c8f489ceac2bcf1bc2d993f9920da10347d3f", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.28.2.tgz", "fileCount": 14, "integrity": "sha512-BJ9CtfPwWM8uc5p7Ty0OprwApyh8RIaSK7QeQPhwfDYA59AAE009OytqA3aX0yj1Qy5+k/mYFJS8RJZgsueSGA==", "signatures": [{"sig": "MEUCIHOUjXHsp+hWf1XmxgUxBJiIvTyUOsf6viAX6iSXoYscAiEA8iO6vT1PIjJVStMvWaD43LZzCEb/88FxioWiyJFYdbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0RCjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsCRAAoOs1KVx+wxXDN6Ny+lgnGVm9MG1MtzWyJOaz1FVOcTW+Powr\r\nocZ1nWSexYs4doUpZgnjcwgbiIe5MIdQMQISfQtgRGeXvjTsIOnLFKeO9MIs\r\nDopHUlRlzEHve/Xxi/tdyvKOB5i0NDZ6ZrNmdweYZzAaqpK+R9e60k81jxd3\r\nmtvpCIOf/oFOle/FFZlWxA9qaorqAhIOGPSB7sWvDPKxHtQPWtw28G46Ea7S\r\nL7D7FxawwXGdFfw6DPJf30ok3fDnStnCvy5VYzSGk5rqVwZZE8uFP/8Z5RAS\r\nhykWrdUP6qOxKTgp4U7GStzhfXF2b5dUr+tJcnnenAI/bpbzgyTRFHKRPW8F\r\nWF2x8k3Tii5PGVGhPtmavlylFNpHli161NMXumyHZP1RBd/CUKA08QgeGfRa\r\n+7UL14X49EbJMQuImiM6BGFdefE71/MiFHezLl1cm3VGss/mOqWEGn3fPpul\r\nnpeCEdn8mNsTp7/rpBCPjhmzhnK5vW+Xlt+wm+Ao8Ars8R/KeNdG6CHzSgcv\r\nHfA+ewHQIv7LYMsgfZjzIYeyFCCrm86yRYukzsWjx3mMoWT6GK+6GaE7V9Hg\r\n2sBMpHX4Q5uNzzL1c7yUoXwwjT8mNHjIYNTF9MmShhVJEO0fO8nOpI1LULDj\r\n9nkOARDygI9ruw7GaYIwm+j2D5DYdW1qAas=\r\n=gA8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.28.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/30/nymxcyb909ggq2j5lwn7b_600000gn/T/37c8a7d3850b5787c27424b28c97f2ae/vitest-runner-0.28.2.tgz", "_integrity": "sha512-BJ9CtfPwWM8uc5p7Ty0OprwApyh8RIaSK7QeQPhwfDYA59AAE009OytqA3aX0yj1Qy5+k/mYFJS8RJZgsueSGA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.3", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.13.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.28.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.28.2_1674645667632_0.3469412775451346", "host": "s3://npm-registry-packages"}}, "0.28.3": {"name": "@vitest/runner", "version": "0.28.3", "license": "MIT", "_id": "@vitest/runner@0.28.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a59bc7a1457957291b6bf1964831284768168314", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.28.3.tgz", "fileCount": 14, "integrity": "sha512-P0qYbATaemy1midOLkw7qf8jraJszCoEvjQOSlseiXZyEDaZTZ50J+lolz2hWiWv6RwDu1iNseL9XLsG0Jm2KQ==", "signatures": [{"sig": "MEYCIQDx7rEd8EFOEtNEw/PfzPcULR1xK7MBjVXhpzznZ/TJVgIhANhtXjdUTd02zCgdqooZNd23W39U5g+IC0tQSQheOXE8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj078bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotCg/9F4gh9vLO6hJhr05c1kK53uZRUAiHjYbROYECIUPM4STEMlCz\r\nknFN/GFP4Gf9P0QvMzpZg8klUuAtJiJYeZx1QBfW9VAtCnyRXm1aTjmKbZz3\r\n/JKxsIBome958ohLFZM1J2PBYvMEubvlZIh8j8Iv6yKActQ3oDtWogguqnrz\r\n8YpVRvUthu+OkAq+wdwp2fCu348iZDAV14BCcfnmh0SkPH5Px3DL2U7Nf8h5\r\nxk4odQTCZzD7LSioClotGjsyD1+35Ah5HvO+IlVn2xUM7Mc6c0slBt2FmPZN\r\nf1nbJWnBERge5NYjQPHQH7p51fGvVAhjiq4ozNHKNNTJ5KXFwEe67XbvB7Xs\r\ng0dCdVWtY7My8/9C0dub5srNDpLZJUywECZtB91xQ3tzh0Y63Hyj8K120Zx3\r\nPD2c/IU58690dsLKK0wkNN5oyXDFfiBEdD6+azp83+B97NXKa3c15AXOFjk0\r\nSp4mss7661WdnsWOaI4OTOGJgZgjjNaVQlH0oZKXGGZvg89FsgOokoGK7HSf\r\nykeLz98zue+SZJ0pu4Uajjw3mZGoBPD2GfnXKzjuBsKKU4d+sUYyx2SVN+Ym\r\nmmzlrA/K1HQAdbWwglf1rOq5cdmVmhG83N6eAz2yx7V/CF1vcQq9OYdhPgUk\r\nkG6bzVSsXCK/obeizRsBYr156SxntQ8aHto=\r\n=9v9L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.28.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1184301f3f1ff429c70f7e3c8e8471e3/vitest-runner-0.28.3.tgz", "_integrity": "sha512-P0qYbATaemy1midOLkw7qf8jraJszCoEvjQOSlseiXZyEDaZTZ50J+lolz2hWiWv6RwDu1iNseL9XLsG0Jm2KQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.28.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.28.3_1674821403541_0.7868473735444297", "host": "s3://npm-registry-packages"}}, "0.28.4": {"name": "@vitest/runner", "version": "0.28.4", "license": "MIT", "_id": "@vitest/runner@0.28.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4c4e5aed91d4b19a3071e601c75745d672868388", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.28.4.tgz", "fileCount": 14, "integrity": "sha512-Q8UV6GjDvBSTfUoq0QXVCNpNOUrWu4P2qvRq7ssJWzn0+S0ojbVOxEjMt+8a32X6SdkhF8ak+2nkppsqV0JyNQ==", "signatures": [{"sig": "MEYCIQDAHRxFy4JDBKXEzuQh9mgqS1OX6MXicNy1q7aGqWcbNQIhAJi19SWuC97jTqmclpXBroN2NkVktWJTrf4QJY+LnVnF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3NxQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrblA/7BbgjbcrRMIhOPnpNrltFy0lOR2WlanfeGmz21LDdHxmc2733\r\njwng9tEqOu/MWBroTZVJzSddteCbse0i6xbdfVUM3XKEkHl9J3W0jWPnRlt3\r\ngGRemuUTx769VF4Agy9xwWAS12wGxs5FM7QzLPWRm8GRisDrSjB5HDsrZnR8\r\nAXktHx3bkBfIm3WQkzlzvy68/papA7+KoFvsmynPPit3oJN1tQGfB6fztpoP\r\n/p/bkeUyHbs8aS4KscvF6WfbJrGsmK0dbHLHtxEeqf7pOO5AYcHyIfRqZK/9\r\n8sOO8/JjsLZyNDKmVWxvCj+rkwiE4FffEJxkEa08LM29O5I+eDyzG7/BHxuM\r\nVNUGBPPtQsAcBFcgQejaN8X0EE+vXcv1/0a/FizHwxS5880Sy1xsRPgl7AtN\r\nu3nQhVj8FuqnEbgaHGNgCP6xDh8K9/mk7XN9ZYMj48bpYxHKRqjban+iQuMh\r\n7eereDLJfphEV6HMJQXlxROPVvyN9PKStDhYSeWpu1+mf5JgQd9+wEEbccdo\r\nOviv01v5tY91usLGG3uj0hNWXtZ2PesU5WBUyElfb4fuf3FTnr138JL45kzt\r\nS4+899RE+eMsPSHlFmnsIdfU5gDMR32qt/jaeRHQtI3pwb83H1b0/R3Uhzea\r\n0NOrVaDSqaB7hGp2plkCkLDUaR3o7UnpiGI=\r\n=niF2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.28.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/9eb9901756658ea9ff88e15a954bf037/vitest-runner-0.28.4.tgz", "_integrity": "sha512-Q8UV6GjDvBSTfUoq0QXVCNpNOUrWu4P2qvRq7ssJWzn0+S0ojbVOxEjMt+8a32X6SdkhF8ak+2nkppsqV0JyNQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.28.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.28.4_1675418704567_0.43937737516970676", "host": "s3://npm-registry-packages"}}, "0.28.5": {"name": "@vitest/runner", "version": "0.28.5", "license": "MIT", "_id": "@vitest/runner@0.28.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4a18fe0e40b25569763f9f1f64b799d1629b3026", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.28.5.tgz", "fileCount": 14, "integrity": "sha512-NKkHtLB+FGjpp5KmneQjTcPLWPTDfB7ie+MmF1PnUBf/tGe2OjGxWyB62ySYZ25EYp9krR5Bw0YPLS/VWh1QiA==", "signatures": [{"sig": "MEUCIQCAOPqYL5nSMHhEXXZq2sG/2aYlVny+I24JKls3paPangIgDQU5CgNGYiIb3FxO13+LUahJ5eSY7xN8mFrn/xZ6oG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6ivZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpqmg/9HQ1UNnFsoe6A7+xGNLK4TG0ymzMOOjoGTGytTIgcExYpIqvo\r\nJFVsGD3pWgCE5q1zxLwv7ccgJ4aUe4Q+vkRF7YmDqhYL7aSNbHIMIQUIQo/5\r\nDb+ni5cYKj8KHn+VyyV5Wl9bAc7a7UClq0SrAKJMFmVTgIjX3ROPDnfWoPKY\r\nLgvec+Jubi6Tq4QkU9WQtMIpb6NQ+PxbL5DArK4trTghUF72A3NtUMzj11cD\r\nbgCTaUR1r5A8uH0Ee6cHhfAxiMrSzTCieYyVX1SDJ+gxr0PmsnvhGvYIqCKq\r\n5RkUiHj/4JRJELpCcHbk2dZ4Dtam7uUU+v6tViHUovRVf+1bmftFYDwOUuMD\r\noxzmoq4uSQ29fBUQ6IAUFihCN+jsbDugP92faxFmwsgX+nMZg+TyDHPIk+e5\r\nsd43Ci7aKZnTpnQB85xXc7vVZvTitdxb8XVFzdxTv6vyTVJdbUnxXGw/0oFK\r\nUh/OvgX+sQxncDZEKKA5SkMuYJVmih0aggBdi/jjNakcC2bFTzUIw1/8mWME\r\nmlI0Bvugpn8JSnh1o4AgfB2Nkeii0ygZZhTwA3usQuRwlr/8N2xr5uOvj60Y\r\nbVLRlnyV4K5any04/sVNFopM1HdWcWM1KytmMM/7smleAl85hkV0eK5JnABL\r\nL3R6xDaaQrSmwDgFE9gFE8gkC0eAbeG901s=\r\n=y4zs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.28.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a5f42478ddd7a7398896d758a7b0390e/vitest-runner-0.28.5.tgz", "_integrity": "sha512-NKkHtLB+FGjpp5KmneQjTcPLWPTDfB7ie+MmF1PnUBf/tGe2OjGxWyB62ySYZ25EYp9krR5Bw0YPLS/VWh1QiA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.28.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.28.5_1676291032869_0.7945268107139185", "host": "s3://npm-registry-packages"}}, "0.29.0": {"name": "@vitest/runner", "version": "0.29.0", "license": "MIT", "_id": "@vitest/runner@0.29.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f25d8b8b5dc5691a263452c4a1e9e814f1e76ec3", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.0.tgz", "fileCount": 14, "integrity": "sha512-YL5p8+ORYNK/5JWYKG//FGazEYDpvRK8RZRJgaAuNxI1iZx07jnkl89nAV58hTgSFcPymnNAv1bOHmMa++5bRg==", "signatures": [{"sig": "MEYCIQDp//P489Rifk6JujuHiWXUCmOvBfB6IBTnfbRcAusDAAIhAOxacBwjkXnUk/4LFI2EB3JZKRLidwhyufSSO+prm2gu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+cYXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCZg/8DvemGXewEFag0hiNGXw9WkqlbbKcv/Lb525Sd7JnKQQNdQsf\r\nHPVXgJo7csntx8lYXqJMvhrgGFcySJVi0sWtjHctPsxmU6R9RnNVppuTBORf\r\nkCX8oc3W5tyOeEsW5D82RIhMF1zRZpgPJZ5CdPX+eaO5Rx9x1s85rkzYLQYL\r\nx7hV1/EY2Fx/30HSPCmwh4zaR9KSYT/k35ge11ybzrRbaN+AU4Q+nrb94gwq\r\njR6AvJgQAh3FNMz6vIeuNykTAsWBHwYcxJBjGJNM9hAuj0ZO+c/kLWl8bz3w\r\nM6lvDnyjYpDaTch8Hzu1FxYbg5FR7QAwaVzgpvvGJ89TftlPzj4WDpkRB5W7\r\n5VeUmNOUaOHO5OqvYIinWD1qtS7midohBhrsDhAxuQsuQf4vo6ra8FTm/u4m\r\nEWz1Bu1rccCfOGzfuaBN3/EQsotV7/ZNUIafhhEqvpI3Bbmsea0X4xv1FnRX\r\nLYis27AUGAPGfELZOGuYA3mzQEGN8QZkFKfl0D4Ir06FDx7Shtohxj5Iags/\r\njS5nR+m6seMf2Nju8BkLhO+iokxjBDt+gRGm+8dENJlVadtaRall+25ry9Et\r\no4yR9OAypVJomUhBZvv+WKmlhse72sJZ1bHEPgAIppB6Qu+VQ8BZADvh0wJN\r\nfOLGNp6Muuh2qnNRRbb3JurcVpI6e0YatdY=\r\n=w7bw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c86ce7341ee826e3074b92b6e6bd111f/vitest-runner-0.29.0.tgz", "_integrity": "sha512-YL5p8+ORYNK/5JWYKG//FGazEYDpvRK8RZRJgaAuNxI1iZx07jnkl89nAV58hTgSFcPymnNAv1bOHmMa++5bRg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.0_1677313559163_0.5700103800739789", "host": "s3://npm-registry-packages"}}, "0.29.1": {"name": "@vitest/runner", "version": "0.29.1", "license": "MIT", "_id": "@vitest/runner@0.29.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "798184b710e6b4ebd81f7ae8efeab64ad1bc627a", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.1.tgz", "fileCount": 14, "integrity": "sha512-VZ6D+kWpd/LVJjvxkt79OA29FUpyrI5L/EEwoBxH5m9KmKgs1QWNgobo/CGQtIWdifLQLvZdzYEK7Qj96w/ixQ==", "signatures": [{"sig": "MEYCIQCPcqmdI0nvScOn74UgZIPhAmMbPPvEX2eUAH8KTCfLowIhAI83XLOrF3V692eGulzLGZFyBG4KY+pAuWSYfUtZ9/mV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+dP5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxkA/+LZK39dJUdAK584JMfa58l2KsbXwoUrj4BLa0T3S+1V/9KYhn\r\nIH/zCdFnhoQ0CF5DCptNqIslyr7E7QCEwyEO5dXpVLSp7txiN956C73NFFah\r\nVYBUr4FKeAhLPQ/h6sa/O+ZptVLATSsU46Qekrjbmkw6eCsJgWwxao8h5GC3\r\nn548GdNq91KvhDbG8FvT93wNmZN6euDmCFyxZ9xjJCSVbg3+C1f9czxSTSG6\r\n8Q4vlKD4SmOurLvFB9OkDnEAvpCpHHyQvw+NJCHErZ2E49bz4PyVG8pUQCzj\r\nxxpRE1mzYKrT0+SKDK/WXfzMffJZq0leYeQQ471cwtlo6nUsLzqhboqNhXAK\r\nbaSuJdQUGra8y5JANfIXoSDZ7BLoZy23l36XwBuPPq7YZVm6iwMtf04xpULB\r\nWo5zsCnUDi+J2sE3dz38JrKwEmb1eO1REN9IqLSzBzr0FbsLneMnvF96Dv1p\r\nFAT5q66bO+kA46nizf7+SKedXysVXQOTAvmnVvVsInjEj0EXoEE23upo++21\r\nkeRhAolf3xdTgkSNzaFCrbSzqLBNiCvCy8KfrOrE8+IRUKa8uybp5QiehoQB\r\no0Nu8Re6Zl/zUDTtDEzudvF/uiuvBJYi06Zf4fVhL6KpYg5A0/c7k3LhJtpw\r\nfiJBvZgA5O56nX0/PQq7utVSL2EENrcezH4=\r\n=968C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5d514b1f6a8c7f0ccdc7c010303a96b6/vitest-runner-0.29.1.tgz", "_integrity": "sha512-VZ6D+kWpd/LVJjvxkt79OA29FUpyrI5L/EEwoBxH5m9KmKgs1QWNgobo/CGQtIWdifLQLvZdzYEK7Qj96w/ixQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.1_1677317112890_0.8071985919906808", "host": "s3://npm-registry-packages"}}, "0.29.2": {"name": "@vitest/runner", "version": "0.29.2", "license": "MIT", "_id": "@vitest/runner@0.29.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bbc7b239758de4158392bb343e48ee5a4aa507e1", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.2.tgz", "fileCount": 14, "integrity": "sha512-A1P65f5+6ru36AyHWORhuQBJrOOcmDuhzl5RsaMNFe2jEkoj0faEszQS4CtPU/LxUYVIazlUtZTY0OEZmyZBnA==", "signatures": [{"sig": "MEQCIHGJPmAjf6B/aqsGImHXl93vzaoHrGrVlifhoVTrJ2f5AiBpuCyS3WGKmXMV8H1674T4gXs+Y5hfnNADN8Lh1KN3Sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/hphACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBMg//UGnnEYeuU2BGc7im43ZXBP5GTdVMsFpYhY7O4s0T+1Yj0Okd\r\n/jb810qU3sPPXtyoopGdOW22pLWvkinZRN+uE+B0O2tD6LkrtoaX/1k58r9d\r\noig1ZsYKQYiVB7a0dUVSx3gR0jlKY9OlxqCWYbSfuqg9MEslHhODspJWymoC\r\nEc4xc1KZbcXEeZqWzoYu5FCE4tIJlOas94bcoq1WvJUXTKxQUaUWJIQ91FLE\r\nqffFlGrVXXksU/LPIJt0xqOOK4WKGVyKudzBITSTW+KXrSdExyFz6usNC6f6\r\nqDrO3Mo896pxHIBU5gRhkM4aBF+lOoh0U5mNIm6TgRKYtb4qjpFeNRFZs0yf\r\nAZSOi2ZIT19oCpULeBrZSMManiZxQMfaNLCzr3yBqfk2fsiJpWNCdL87MrYs\r\nR4uiYzQxzDVpFD2k2Br3kEb1vwGAZJIZLKwg8eM2QzV8vL7M/ubKcNietQm5\r\nE03d3x9kaYLmEPK8VAu+R8Fo4TkY7MPPR80ess4szHHtYwZ0btPEhRThbQN/\r\n+Yde58Ut+bi1sr4/8u7R4+5+qrg2dE6Tmi4liT/+IylW/Lki4kmXe5IBGA1z\r\nven87IVf6edKyRkOFMFCdZ1lV03+oRnCCNHzBA8LhASPmRodZOpOzgpw+GEI\r\nFGt64GVPEHw4HeQZLC0Y5IXKYRKFdH7n64E=\r\n=pbH1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/eb9b76ed14a81d86bc8377c71b32e650/vitest-runner-0.29.2.tgz", "_integrity": "sha512-A1P65f5+6ru36AyHWORhuQBJrOOcmDuhzl5RsaMNFe2jEkoj0faEszQS4CtPU/LxUYVIazlUtZTY0OEZmyZBnA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.2_1677597281372_0.2562045265776365", "host": "s3://npm-registry-packages"}}, "0.29.3": {"name": "@vitest/runner", "version": "0.29.3", "license": "MIT", "_id": "@vitest/runner@0.29.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "dbc67d6642d7f5d67f6ee9f8d806070ed7244f39", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.3.tgz", "fileCount": 14, "integrity": "sha512-XLi8ctbvOWhUWmuvBUSIBf8POEDH4zCh6bOuVxm/KGfARpgmVF1ku+vVNvyq85va+7qXxtl+MFmzyXQ2xzhAvw==", "signatures": [{"sig": "MEUCIQCzkArjQUbSOZwUSu27b4bz3C7roJC2xFjkp1BHmem//gIgVjYvu7THNbcQQlJbBW//9Vyn1O0ISoQEE0VBmvxxaQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEiS6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+lRAAgwwhKtthjD7k23QZZiuzibwkQUpzMq+K4REBx+mGUbIdCRtk\r\nS48Q6Mn3+6P9nuNS2Ye61ixPGWzs91kkpxe0G+QlXbS4wz+a/w+nMa6uQR05\r\nA7naA5VbzoQBbX46ZKyrCrBgKU9PnwYGGTr6Loa50ca+6jYYbA86BLxMHrhy\r\nda1ojts7xV9VhZ5AFK4TKwf7z1MUSQfYMknFI0hL753EuEegh5ifmRri/yeP\r\nzt2eBnetb65N1miGieS6HO20jkMrHfyTqpLp/m4BOKt9Dnpv9yfltqxE+yxx\r\nMxrfMqjKb9/URNwz7pI9o1k87JLSnjYPk+ElLQakrT/zpGCP37Rx3veRv/FP\r\nMIQpd+C3BT6qIPpIeqLAWE9YcDeJyn3lWemfe2UvBQIdGVH0/qUDUl55q9NE\r\nYaKuWFjHnjO6HchOm4Yc4pQEpdVOgSlOjs5swm+NDXlHd0dzulbzCZoRTmjn\r\nNFV6vfUQEZm0bn5Mm1n3n5plbjqA8dixhOhbisN/0lAnOgMSRVSb6iO+xdpG\r\nhVhCLzhLI+L3X4uwi98u4CvgNFJpxK5CIuhE4k+fQtx80W1zTZtWucMy5uVw\r\nq3PE8yX3js4NpF69GdMKDxDwLvlZamFRswd+86hV+g+yWCGae1Nz7KlnMHVP\r\nT/dyJNgKOzKZuSYQmSb0jH77aQPzldDsq3w=\r\n=xnNV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/98c6dfe24d29894a1fae90837b10b1c1/vitest-runner-0.29.3.tgz", "_integrity": "sha512-XLi8ctbvOWhUWmuvBUSIBf8POEDH4zCh6bOuVxm/KGfARpgmVF1ku+vVNvyq85va+7qXxtl+MFmzyXQ2xzhAvw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.3_1678910649831_0.1307575003923731", "host": "s3://npm-registry-packages"}}, "0.29.4": {"name": "@vitest/runner", "version": "0.29.4", "license": "MIT", "_id": "@vitest/runner@0.29.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f9b45e6ea26eeaa879f5975493d16cdee4d787ab", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.4.tgz", "fileCount": 14, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>19WSB9TP5rQLCUg/poRPY3mTnWfy2jnvLvkiwdDB8yvXWeMErp/pOH3Mm/bC5KPIMzCFd1E8RWPtL1Z9g==", "signatures": [{"sig": "MEYCIQDf25cpJYrwgIsvtSaxsleGOLCRzi1w+7T4yqdM/XdClwIhAOBPCN1SBdYJddVbnxZ5X6Jmyk+4GssLWcGuXdeFWVUt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGJUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2VQ//U41F92aJukDYlmUDS0Vgci3K3KTFC0D0z4o59MypoiZVEhDc\r\n0pBzGrvJq9FESpIVsy3dTvJhVfGpU9mYLh3FhLgZ+5eXXSu9ApI+9Op1YlqP\r\nL0a4hkCZah4Up81r8GdSr/XOFh+j9QwJimoLH5BqKylYrPZ+nQ59peop+T0L\r\n9NpT1B1pCOpzYxrUh+RkS/MZ8/pEYs3WfWshA4asbhjf8YYErWrThTPrieo0\r\nYPg3apfTyBHwlpDXHAp4E3lZr6FKdzDtlJ4j9zLdFsmG/bW66AH7V1rjivoX\r\nWKG/+Wy2wOIN+9HR3tdoFXOhSkqam8st4GofTEx/oZbsMt9+R0QCwY9xfqyH\r\nQthrMvF4ttRj6/QoOzosmAMxwSvvgwvyAJK0ruRVfU/uaTGt1hn96h7qaSsr\r\nK7TG5mo9ziJ5zzVVSCi/irnnhfl0nSJGKPrhfXMEYZ58QyxMI5R/WZ4fo7px\r\nI5qUTXFCTwKCJDqLv/4O5Ep3VJ4I0QcQyA0d4zVxal+VKJRZrdRHOm6RPuQi\r\nLRRxqueKR0SWhmv1CnjNUY1C9/8x71+ahtXWYc1nM0G8gYkDyu6dtcjpj5+i\r\nOSFhNwdjlESb9AsO4LH9BorVPZUTPkI+L9sLvYqKkwoWh+fT1ZwrRB3fHVEO\r\nMo5Nh0uOSgOCzzjxUMZPnjzVBG/h4n3Wk8Y=\r\n=/t6W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/dc9d04a961919d046991640f8b89b5b4/vitest-runner-0.29.4.tgz", "_integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>19WSB9TP5rQLCUg/poRPY3mTnWfy2jnvLvkiwdDB8yvXWeMErp/pOH3Mm/bC5KPIMzCFd1E8RWPtL1Z9g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.4_1679319636528_0.3023464965482161", "host": "s3://npm-registry-packages"}}, "0.29.5": {"name": "@vitest/runner", "version": "0.29.5", "license": "MIT", "_id": "@vitest/runner@0.29.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "66ebf8e48576cca74eb6126bf1fb200fcb6957ba", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.5.tgz", "fileCount": 14, "integrity": "sha512-qYRf1KTI4Js1dg2vWr8bsOEngogoVL32DsN3vuH0aWIricBgGw4hzu6IyhtH9YPmsHGeAaf8YPipe8bURzRQ8Q==", "signatures": [{"sig": "MEUCIB1S0oWJ1afDPd9ZefTen1GRzxx4aG/UwKMdAO9htOWvAiEA543J9XAjR/eXdr5c+fGqweH4qndVw7cV2nJA+5UtJpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGnFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtsQ/9Fo9NBaVJFevEEdwseoY1aobA4sMlY0FY/QmpCpVu3eWGaXVx\r\nXS/IB3+Za5yDHSS7Xnwss9MQUYO4BL6C1WOu9QPmLbHciSMkeQiV1uQQmXNo\r\nTcODGPjqYtZmqUOaL9cWKSpjqHDv84F+qdMhwokw8FtoMAPSUUbzwoJxyCMC\r\nYzDVr/hnaCYLPLzp1rdYlIEhyKYFj4r14GKE7NLlBcipN5x467gWx+6y01XI\r\nYZcySHOKOW1u+ngWAu//Tn/ciYiBGSCjsbOLmAZgf1G5Wl3XHQdaYQFYV6RT\r\neyCL/BFYz6F5UrinYTa1TOEDomMhKvkGf/Ta9pzv6nGJEbQzqSUa+hoRjRxy\r\nmMRVFFRBvLndkw9UR2wqmjD5bWtpqp+3mCGMnN7g8odoNotTymc3y5G7uU44\r\nJWvl8h31qoAryPY48BASRIqZBTSc5HUrsZyTIM28W42XaPfSK9kCQYL+7Nvs\r\nmgC+tPWfn7oYAtMNMQPwh0ditoVhnIbZH80nqbaT09s88eeg31Bb8uHwE7j/\r\nTp7BstITy/rPtnze9Dp1i05XnQ+ckZg9Bto8/Y20kQwPec8fN1YGckI1BRQX\r\nQF2BkpdMIlUUENE2hK29P9bwoliM+c472e2M3r3voqlIG9auT4+jlxNxV6en\r\nbpUhsFJnfDYuCGfj4KxvvXPQHqaIYce52ew=\r\n=QsX/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f2220e3bf663f364fd2e7e9220da2cb8/vitest-runner-0.29.5.tgz", "_integrity": "sha512-qYRf1KTI4Js1dg2vWr8bsOEngogoVL32DsN3vuH0aWIricBgGw4hzu6IyhtH9YPmsHGeAaf8YPipe8bURzRQ8Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.5_1679321541005_0.9573537369783645", "host": "s3://npm-registry-packages"}}, "0.29.6": {"name": "@vitest/runner", "version": "0.29.6", "license": "MIT", "_id": "@vitest/runner@0.29.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6ad572d2bb016e776771c4e00a39affe376a8e62", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.6.tgz", "fileCount": 14, "integrity": "sha512-yYeCi2bjhkzdUneD3WgOGgOiMg6GyaAfeRVF0PE7ZMgT9r6L8ZssiBJsmhF+VKSbTVjbO2sJQXSPYWc3Jdy0iQ==", "signatures": [{"sig": "MEUCIQDxrWsMi8bBJ7DMY8p2NA47nJrhnch2xUf4lxpbAOSOywIgBFwNXD1fmDNr0u6yEejPLw267UVs4qRAp6jglHw2Uhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMDWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi5w//XIKN9gMjiAFTPg96XGqHgY6gHRNhd6sAuxci767VpPrhmONj\r\nSw3QSKqN1OCDz22ymYJfPsbalUmLKvuVacL3jumJvVHek/2WprqJI0Efo0uW\r\n/dv0DRMR/kjM4OR2oN+6g3+Dh9jAMJY2mAHMHI3GpMPFnD2gBBetctWU2oRF\r\nxse2JPa8PioeQT2jQEN+8KfI0cfrIHD7HJzODSc79WvgisAmk3S9SFo0XB3O\r\nOICEyrfMlyFu4jwq6saLlTRF8oCqoDRsGXn+feYhkYKaO7je7Wi/ETP62XR9\r\n0ZLkzBtPnVe9KqTEykkmClUQpOs+YwNd6VDtNSZfdtRKuT/9nwfz5vrHy53S\r\nOzQQSrAB21xvb0CMk43k2JtmtM3QZ+wmal8VoL1OYxgYCFUm32at3rYMrVDP\r\noyVEXTLt9fH8zCw/K4FDgnVei1laems9g49FmjHkABv6xeNfK1oyzSs1KaAN\r\nfK5NBVdkQbirTy6J8hBniyWXbzdnr1O/sM6RBjIQWxtNhX9GU8wu53X9aeCs\r\nQCYs2uaa808HZmxgcVr07xFojuBwNJcupQ8VRKA88b5kKWAgcKzIxUCb389/\r\nMURC5ji+DTb4KAUf4oEklzA1c+gJ8H/P0LDG7zdi16gW0s26IPHD3K34Jpk/\r\nr9GQSSlMR88k9Ho+4TYjUbU7EkwNVJgTGd4=\r\n=P72I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2154c33566b5ae4a3dc0cab511f1a6ff/vitest-runner-0.29.6.tgz", "_integrity": "sha512-yYeCi2bjhkzdUneD3WgOGgOiMg6GyaAfeRVF0PE7ZMgT9r6L8ZssiBJsmhF+VKSbTVjbO2sJQXSPYWc3Jdy0iQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.6_1679343830638_0.017287343579167613", "host": "s3://npm-registry-packages"}}, "0.29.7": {"name": "@vitest/runner", "version": "0.29.7", "license": "MIT", "_id": "@vitest/runner@0.29.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7628e461b70b6588436e3567beea8eadd5bea511", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.7.tgz", "fileCount": 14, "integrity": "sha512-Yt0+csM945+odOx4rjZSjibQfl2ymxqVsmYz6sO2fiO5RGPYDFCo60JF6tLL9pz4G/kjY4irUxadeB1XT+H1jg==", "signatures": [{"sig": "MEQCIGRLRzZV12engL0v7D3CwjtuF0tiCH0IA2IeFt7w7PkcAiBcba3azRbv5DzRzaR6EkKjvAD8HDQ44q5LE/ulzhj6uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMQoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhmhAAmbUql1b+Bw7CBfoX0xhSjVjBo+7Ez6RdWkBvzfelnBrSUe1k\r\nr+vudNz5E4Dc8wIn/SMskrPBvAf1LGRdVK5U7qdI9lDcKG6PeyBLr/u7zS2Y\r\nuw1qwGFH6/oOtUQ9hqOsaVSgJiPGcZkYHwFpRBZkS9rRB7rrx1/6CNE8p56O\r\nGjHgf6KEw8Nc/xeJf86ZwOn/fMdkwryMjwufQweVFXAqhQoOHUl+QFXVpUl8\r\n/FrU02rgBgle8xGU8fPMwhc4wZ1UYOn/XQjuih7yj8ox6oCm95gsQZTBgYsW\r\na89ChT/P07zaKIMhwK29TDuAcY9Xe/ee0NOV5ejvA8+B6nY7XJ3VoXRtDdAP\r\niFhHtEJ5hFpKQXGmLmKXqTWDOFsBGg/RgFYGxQjP0sQhE7DcEM5VZIpilodo\r\nZDga3Z16Cph06ye5ZT2QO3w/7GKV1Rmf+AU3obKLWlVPTL9D/0P5EzcIcz+5\r\n+g8ED8WIx8FdSRAcGuqpYpSwjtRazAVEPFSDWhB/DN9qGu4LMfzLeEO2AhE1\r\ntm5EWc1trDQDbU86YEHqF4rfVdgAxq9SSORRh8dlSBCD4IARNO/4yxcJdEET\r\n0ztc6SZ3c8QwaYgRlg/5W3c1iQh21fiaW00/Nhz6C19pLpOzoK2sVbIWFXFh\r\n6Lyni+R4DsqL3qNvra2RMQM+mCuUhKIAWn8=\r\n=mJfB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/380a69cade645b08e9b2188c510a8817/vitest-runner-0.29.7.tgz", "_integrity": "sha512-Yt0+csM945+odOx4rjZSjibQfl2ymxqVsmYz6sO2fiO5RGPYDFCo60JF6tLL9pz4G/kjY4irUxadeB1XT+H1jg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.7"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.7_1679344680477_0.9246166769841673", "host": "s3://npm-registry-packages"}}, "0.29.8": {"name": "@vitest/runner", "version": "0.29.8", "license": "MIT", "_id": "@vitest/runner@0.29.8", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ede8a7be8a074ea1180bc1d1595bd879ed15971c", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.29.8.tgz", "fileCount": 14, "integrity": "sha512-FzdhnRDwEr/A3Oo1jtIk/B952BBvP32n1ObMEb23oEJNO+qO5cBet6M2XWIDQmA7BDKGKvmhUf2naXyp/2JEwQ==", "signatures": [{"sig": "MEYCIQCiUEoi98i5Bi0YcQx8sgQ4Q/puChbGra9kg7mJPlFEUQIhANtOnKcVg9H+qm+i80TF5p+c4tvCtqBLSI+e2f1gMdaw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIuevACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9bw/+PPdZkm2EuiwgriTsGkepfTMUTK9QJ4FGJUw3UZW1rkciRmL5\r\nRCoHNa7G23K9eYeagr7Fqu1fQ6q2pV9xM0aohPqtADhb/6FDT+n41ZKi+cMb\r\nFnOpOgjfKYT7GUKhIYrccm1/D4b/Bz1gSXYxRbmuyI85DViMiiINCNA34Y08\r\ntpaJTfPoiQDWUWzudTvUHAiVDCy7C0APeFVeHisPgDbByWqDW8tq2fZ/ChaZ\r\nQ9W+J+14++mZDigFiokym/z9kMtWcjZG+YD4FvISGeqmXE8V59x3Onr8eGc4\r\nGf2ItflmAdC0pGd+IZS5eVFcxeMrfv1Vw2/tMNDzlndy8kkYelM6R+xhUI0g\r\nrMDbH5eck6qa7yLFUddncF1ocL9jBlLxKcsUuRFoggvu0W+dMDIs3HPyApci\r\ngnYs0gsO8NboxuCnx0aDnNcVmjUeTB9ktgWUzm45jf9BAIdcfQzp8yw4gaIp\r\nJXcOD9/B7CrFivw678408WHORSKkET0RkbMsqdphL9R8T9iAGkDHlLWJzm6n\r\nICRM1JnzhNqTlJ7TK3GlTeTB/EMGvJkK+TE2va6bkTFPb/NHn6bmTcAkvtI5\r\nX+Cj2bAziO9iCaW3xJc9s8UPFuLZL96KEDmWoc47hxUlsAVDXjkAxpNYO8VJ\r\nLDmxu9lrNFS5Mq1Ab93DcY9rZr9gF4OUmHs=\r\n=t58Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.29.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/41c9724e97c77a8e775d4d3778697ce0/vitest-runner-0.29.8.tgz", "_integrity": "sha512-FzdhnRDwEr/A3Oo1jtIk/B952BBvP32n1ObMEb23oEJNO+qO5cBet6M2XWIDQmA7BDKGKvmhUf2naXyp/2JEwQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "@vitest/utils": "0.29.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.29.8_1680009135747_0.7714193551003685", "host": "s3://npm-registry-packages"}}, "0.30.0": {"name": "@vitest/runner", "version": "0.30.0", "license": "MIT", "_id": "@vitest/runner@0.30.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d589ba81a136d563bf46e190e889583c6f041f5f", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.30.0.tgz", "fileCount": 15, "integrity": "sha512-Xh4xkdRcymdeRNrSwjhgarCTSgnQu2J59wsFI6i4UhKrL5whzo5+vWyq7iWK1ht3fppPeNAtvkbqUDf+OJSCbQ==", "signatures": [{"sig": "MEYCIQCG0fLN/Xg2G1IzgIOKlt2ZJQGzN9RhC6jXUWR1qFla8QIhAPBjW5puMe0QQvsP6d/TVw1UbA+EwT2o10A6gJlyjtgu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMsABACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyEQ/+L2Sggr2+ypvtD6Kphqr551RJF154c3mhilfiBBNZIn+ugccB\r\nZkAuwVUtNJdbENTp+kr6Cm30W0agJLhOuvbwRz9JA9ijjxBi6+HxqvDfyuo6\r\nvBE/6wWjUDjuqrAg0hJo0NzJ26H4vckTlILJIPjFM7dxS55iiOmJgqT7ZI/4\r\nUW3kvCQcKpyIyz4ToXAVucdIpQcdN8YfoEYRaKAGo9+AseeZIKOTXGfLSeM0\r\nUhrqL84iTf6ujaghWzolQO4QcfkG+la3Zd0IDeiSW92tZDn9Lu93Xabb+W9o\r\nigKMBS1lctw3kNULBVK5QcD6FEPFqzZtzumNBvGnl+JjQGVlnJci3hhmEkq5\r\nkKylTHer58w1Z0T3AAa1/K/rxWy8KobPLZxh3/ISenmdBwrhBl7HOO0Xy3SH\r\nOHFEWTFAq/U7jiNYeHaK8oklQd20uwpP68IYaFUkvuJhoHsLtVuAf/dUWceh\r\nVp/JO/2orLLo9t72m9SUkqo9CIe/Nzxw+CM1e6eBAfrRPZf/q6/p0jhUWges\r\nCv314k42DrrqoyH2X0ggviZauI7bE0RolJTdX06/QRofaPNf3gshySNVh1mF\r\na8tAsiXj5U/QnQaLIAOzdLTtg13my4RaQeIPujlB275xSZb5kT0jIqLIqmMH\r\nJ8TKdNBGx4fiQkzro4VedFPZmQGB5JuKWsw=\r\n=9CXl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.30.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f46b588e2a62630ea4e96b76610d6a74/vitest-runner-0.30.0.tgz", "_integrity": "sha512-Xh4xkdRcymdeRNrSwjhgarCTSgnQu2J59wsFI6i4UhKrL5whzo5+vWyq7iWK1ht3fppPeNAtvkbqUDf+OJSCbQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.30.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.30.0_1681047553749_0.012598369617797589", "host": "s3://npm-registry-packages"}}, "0.30.1": {"name": "@vitest/runner", "version": "0.30.1", "license": "MIT", "_id": "@vitest/runner@0.30.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "534db590091e5d40682f47b9478f64b776073c50", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.30.1.tgz", "fileCount": 15, "integrity": "sha512-W62kT/8i0TF1UBCNMRtRMOBWJKRnNyv9RrjIgdUryEe0wNpGZvvwPDLuzYdxvgSckzjp54DSpv1xUbv4BQ0qVA==", "signatures": [{"sig": "MEUCIQDeKRMYdyoAX1iW+OK22KwYetZ6LX6Xrx0iRh2xnIdUSgIgXfQI7PO7gzn6ZtimZCuL8vzochv9rWd211MDK9wckLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNUP9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8PA/+MSsQLJzhvh58eCo01/F6J00uY6AqSFgIV1H5kXazyS+maMGx\r\nlYbbgQeC3J5q+glet91+UBHvlLKP3in7K5D6ITHhrWS9PJNc4B/POC4gZheX\r\nTWc09EVhk2JXmMpruyXm7G26TkcCKmrMMYJACBe66ayaWhVS6L+aYsSfSRiu\r\nSbPNJBgmveq/lShaquw/weWgyiEkgR+1L/Uw2/xO8iOhz9QEGtCpyTrP0DKc\r\nyjNWCyu1Yao0kASlqUJbrAXI0h6wM5Fe5cK06KruvgQ2TYcQ9XT0KGTcwEb3\r\niYsUHANwGl5hzi6Pe6WRj+8wzBlDohJNp5pgNhBuSzvbkDinQDvCosMmaa5G\r\nc/hEnQR+01eJEI+w7KjOHzmMbyw1ceClV90vsXWx0P95wnOFVVFzl5zBBCqZ\r\n9D1IlryCfWmvg6TzY8KDKINaYu2ZfkbX8tanBz2/sFdXVgy/h08pA01VEevo\r\nDpq7JvcqzWYEMfakDsZm5NOy8upKhiWhWu4FmQb174TY7/eeUq4ZmW8AKTaA\r\npQBbkloZCp1eyfSm3D2/bjo3D0LUvV/8YJvP19yMYxWdrAjSB98/0KJeW0Mi\r\nglupPIqpuhNetVVPuzq+CdhX3oiW5QR8L2wOP20xsOPITg2bi2oSDPrDxowK\r\n5qOtbPVPcSAJg70MUwZdFvaB4iqBZ9IIVNM=\r\n=ZFdO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.30.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2f94ea6c7ac4d74b0cffdd3676101e6a/vitest-runner-0.30.1.tgz", "_integrity": "sha512-W62kT/8i0TF1UBCNMRtRMOBWJKRnNyv9RrjIgdUryEe0wNpGZvvwPDLuzYdxvgSckzjp54DSpv1xUbv4BQ0qVA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.30.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.30.1_1681212413110_0.795685275119971", "host": "s3://npm-registry-packages"}}, "0.31.0": {"name": "@vitest/runner", "version": "0.31.0", "license": "MIT", "_id": "@vitest/runner@0.31.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ca830405ae4c2744ae5fb7fbe85df81b56430ebc", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.31.0.tgz", "fileCount": 15, "integrity": "sha512-H1OE+Ly7JFeBwnpHTrKyCNm/oZgr+16N4qIlzzqSG/YRQDATBYmJb/KUn3GrZaiQQyL7GwpNHVZxSQd6juLCgw==", "signatures": [{"sig": "MEUCIQD6L8uqNJpZd/qd3Udy/c9hsmI2NU0SPVmKXF+2I02z3AIgP28COL/TFM7Osmc1eu0Xes4Qko2q9vlbI3+b6C3lK7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUqMnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVFRAAkWdTwkSnmagaobjmkUqKTZJemldjKLqTGbC0mjfcUbyNKFOS\r\nmo7wLg9LwWlNnHvwhrQrjqQkmHiiR3ckSj0BYUV3IjRSW+cd9P0Rs7sY8kkW\r\nJCDGUn6f5pQp43L+NxRwRAaipGtrdNgG4RqzWISu85dDAi8V14UnDhtLP28k\r\ndAalCJYZ8YTlp7sb7hCaRI2PDh5TvL5ZOLDl5O+nbDHJ8I9bA+wV/SCcOnO1\r\ne3y8oZB/pfukNNppbRPWXEdpxIrSilb2djCXDVyvTYPyj6iTRGttq7qRJr+H\r\nfqMyntBco8iPdRUaB1O8W2QFnZoxTtNARgqXjYF4m+EkReeaMDj9PKyKLqYM\r\nOJO5IkhQnNlW6pJVIGu4GNl87/xCeFYNJhA886uSMysZ+Qub/lXERmK0DmgL\r\n/UF76fDh0wett0kmlSL5HxmOxOV/6DFB02K6IEzgcHeYAo/8UaeqZj27GltH\r\n6FmJseEUn+khrwyTzw5P410//PrJLnmhc61kjB/hcmXGoYLUiidyQmFY0pR2\r\niH1GB4MDhuV4QXEZwOHFb5WSR0xxiFvA4Q3nmk1K7nRcTPVdpkOt3ju4d7kJ\r\nS1mEQRi0oZkBZ4M6ElrALMna9UJQLrzsVmulM6s+M9m5LettK5EJxLPEbvz3\r\nzBQsXPFWaX90UONOUEdFlzINTNFIa04USL4=\r\n=noyz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.31.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/96ca9ca2115dffd02a5f3e059ee56119/vitest-runner-0.31.0.tgz", "_integrity": "sha512-H1OE+Ly7JFeBwnpHTrKyCNm/oZgr+16N4qIlzzqSG/YRQDATBYmJb/KUn3GrZaiQQyL7GwpNHVZxSQd6juLCgw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.31.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.31.0_1683137319412_0.08236812757072842", "host": "s3://npm-registry-packages"}}, "0.31.1": {"name": "@vitest/runner", "version": "0.31.1", "license": "MIT", "_id": "@vitest/runner@0.31.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fc06260d4824dde624abaeea1825d6a75bad4583", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.31.1.tgz", "fileCount": 15, "integrity": "sha512-imWuc82ngOtxdCUpXwtEzZIuc1KMr+VlQ3Ondph45VhWoQWit5yvG/fFcldbnCi8DUuFi+NmNx5ehMUw/cGLUw==", "signatures": [{"sig": "MEUCIAj3vxAOcnk90cESgRbS9g71QroH1u8m+P0yR7AIhGv9AiEAmArm9vyS4DHJAw+mcezmTpfmp05X4rKsE/Fyc7Mz6u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73896}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.31.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/334d8c2287dfd50f3426d1ff2ad196b9/vitest-runner-0.31.1.tgz", "_integrity": "sha512-imWuc82ngOtxdCUpXwtEzZIuc1KMr+VlQ3Ondph45VhWoQWit5yvG/fFcldbnCi8DUuFi+NmNx5ehMUw/cGLUw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.15.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.31.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.31.1_1684333421912_0.08203868675064574", "host": "s3://npm-registry-packages"}}, "0.31.2": {"name": "@vitest/runner", "version": "0.31.2", "license": "MIT", "_id": "@vitest/runner@0.31.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9cfdd8b28dd15cbd35719b47816600ce32a64183", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.31.2.tgz", "fileCount": 15, "integrity": "sha512-k2mWrzZD1xsWfzwEXeVr2XF4v8ELpFOKLxRbcnzZclHelOLn27nXvnw1A4JwJtmca64C3/6lo4WHZDlq3TefLQ==", "signatures": [{"sig": "MEQCIDzwSj23mOXQycv+e59+MFhKsjwhpTTMmRgZdp2+VI3zAiBOG884bi3cFFJz/JTBdSQagsQ2che/FXR5nqQqjP2t8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75484}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.31.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/bb8f3738a4aea5f0d4c3d20c411b2bf2/vitest-runner-0.31.2.tgz", "_integrity": "sha512-k2mWrzZD1xsWfzwEXeVr2XF4v8ELpFOKLxRbcnzZclHelOLn27nXvnw1A4JwJtmca64C3/6lo4WHZDlq3TefLQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.31.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.31.2_1685451921441_0.9905756024390711", "host": "s3://npm-registry-packages"}}, "0.31.3": {"name": "@vitest/runner", "version": "0.31.3", "license": "MIT", "_id": "@vitest/runner@0.31.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0dfc4825ef9e958c332b773fbf8b4baa21c6ea07", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.31.3.tgz", "fileCount": 15, "integrity": "sha512-89Fic1IRu4yNqnks/KJEncqZsmQpxq9RSnMVwV0RZeI9RWv/FYh9Nuoz8ld73YKOh6NiF4f9EE3COfNwX8aRpw==", "signatures": [{"sig": "MEYCIQDSaWhCcwUxEMwxiFfayIrj7Ok1k+bhio47bSw2OSoLFQIhAMKcgnJbX2ttpnW3l7oJLw3hM1qaS0vNCJBPyOrr/W8P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75330}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.31.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4cab0a22d0d729e7d68d622200e63b30/vitest-runner-0.31.3.tgz", "_integrity": "sha512-89Fic1IRu4yNqnks/KJEncqZsmQpxq9RSnMVwV0RZeI9RWv/FYh9Nuoz8ld73YKOh6NiF4f9EE3COfNwX8aRpw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.31.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.31.3_1685544579522_0.464829308632831", "host": "s3://npm-registry-packages"}}, "0.31.4": {"name": "@vitest/runner", "version": "0.31.4", "license": "MIT", "_id": "@vitest/runner@0.31.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e99abee89132a500d9726a53b58dfc9160db1078", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.31.4.tgz", "fileCount": 15, "integrity": "sha512-Wgm6UER+gwq6zkyrm5/wbpXGF+g+UBB78asJlFkIOwyse0pz8lZoiC6SW5i4gPnls/zUcPLWS7Zog0LVepXnpg==", "signatures": [{"sig": "MEUCIQCsZqTk32J4NFvPFdnMO2bXiYxpoO3oyovEuDZU0n1cOQIgS8g1QgcZ5nTn8NTZj5HkfR0mWfpBk0j74y/yqokO74M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75330}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.31.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ca0ac7f2b36500c9b6876f8728beec57/vitest-runner-0.31.4.tgz", "_integrity": "sha512-Wgm6UER+gwq6zkyrm5/wbpXGF+g+UBB78asJlFkIOwyse0pz8lZoiC6SW5i4gPnls/zUcPLWS7Zog0LVepXnpg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.31.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.31.4_1685613324020_0.7781921661123219", "host": "s3://npm-registry-packages"}}, "0.32.0": {"name": "@vitest/runner", "version": "0.32.0", "license": "MIT", "_id": "@vitest/runner@0.32.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d2a391bf4bb373e324cf2f372d8bb913ab323f08", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.32.0.tgz", "fileCount": 15, "integrity": "sha512-QpCmRxftHkr72xt5A08xTEs9I4iWEXIOCHWhQQguWOKE4QH7DXSKZSOFibuwEIMAD7G0ERvtUyQn7iPWIqSwmw==", "signatures": [{"sig": "MEUCICnISQ9718aJDZ0Lg1rbCS16sP7+ON7c8IDT2sNgeuGBAiEApGXdSDAHKc3Kb6BuE03u3R1rPLEyihRyVswIb1VnSHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69163}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.32.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/61064b8ec9bfcef6d79cd34fd8ee68c6/vitest-runner-0.32.0.tgz", "_integrity": "sha512-QpCmRxftHkr72xt5A08xTEs9I4iWEXIOCHWhQQguWOKE4QH7DXSKZSOFibuwEIMAD7G0ERvtUyQn7iPWIqSwmw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.32.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.32.0_1686071065851_0.0029477060238616293", "host": "s3://npm-registry-packages"}}, "0.32.1": {"name": "@vitest/runner", "version": "0.32.1", "license": "MIT", "_id": "@vitest/runner@0.32.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "86bebe6627a24db3c8c6c307a63b707d288fe9f0", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.32.1.tgz", "fileCount": 15, "integrity": "sha512-<PERSON><PERSON>bkhyptHuzwJCuuCXWSU8E9UkQIMQUhDHFuufzmPKWiLFzIY3Z4MRsPu1qw1uSZX9ZUQ3PPIAyMO5e0Lq3A==", "signatures": [{"sig": "MEYCIQC7aK88Mi+2s0icsLsGISGw34gh9esdLLZuLQp5vib8PgIhAIMVJklojtWgZm0XJiPR6QOhmKpRpvjqv0n8qIcU/AKJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68979}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.32.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/56a3b496c08518beaace4195520119f2/vitest-runner-0.32.1.tgz", "_integrity": "sha512-<PERSON><PERSON>bkhyptHuzwJCuuCXWSU8E9UkQIMQUhDHFuufzmPKWiLFzIY3Z4MRsPu1qw1uSZX9ZUQ3PPIAyMO5e0Lq3A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.32.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.32.1_1686918183467_0.8264463559735851", "host": "s3://npm-registry-packages"}}, "0.32.2": {"name": "@vitest/runner", "version": "0.32.2", "license": "MIT", "_id": "@vitest/runner@0.32.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "18dd979ce4e8766bcc90948d11b4c8ae6ed90b89", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.32.2.tgz", "fileCount": 15, "integrity": "sha512-06vEL0C1pomOEktGoLjzZw+1Fb+7RBRhmw/06WkDrd1akkT9i12su0ku+R/0QM69dfkIL/rAIDTG+CSuQVDcKw==", "signatures": [{"sig": "MEQCIDYNM9qQwy1CSqEZyWZI5AH19WzVxb9R4qKlLq34nQTCAiAUlCFQolrPzDg3E7JZhFT6QlragO/nYO4ZSXW1TntWHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68979}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.32.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/10107dcb7bdfc611082a3fdfea2ca513/vitest-runner-0.32.2.tgz", "_integrity": "sha512-06vEL0C1pomOEktGoLjzZw+1Fb+7RBRhmw/06WkDrd1akkT9i12su0ku+R/0QM69dfkIL/rAIDTG+CSuQVDcKw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "p-limit": "^4.0.0", "concordance": "^5.0.4", "@vitest/utils": "0.32.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.32.2_1686931557456_0.6987739596713323", "host": "s3://npm-registry-packages"}}, "0.32.3": {"name": "@vitest/runner", "version": "0.32.3", "license": "MIT", "_id": "@vitest/runner@0.32.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9caad9dc353e63ed0e0496ad87ecc93069006423", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.32.3.tgz", "fileCount": 13, "integrity": "sha512-Y/+rX+d9kcie2qy5kMY1oqxgaH0DRLrWFJfxiSBH78kFb+jTkhJg2j30Z/XcEGivSIi4wDRtG4a1pVfD3kDXaQ==", "signatures": [{"sig": "MEUCIFqJuzn7/nCzPiRYVYXJI9k0AsLJsc39oIl8/8Lcq6G1AiEA82RpCE/DsMsdEtUbqCiPR061H4gXagCihn8WMMy0bnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76575}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.32.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6771709d9d8f75e2f51783b7b94545f6/vitest-runner-0.32.3.tgz", "_integrity": "sha512-Y/+rX+d9kcie2qy5kMY1oqxgaH0DRLrWFJfxiSBH78kFb+jTkhJg2j30Z/XcEGivSIi4wDRtG4a1pVfD3kDXaQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.32.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.32.3_1688373339228_0.696763505982817", "host": "s3://npm-registry-packages"}}, "0.32.4": {"name": "@vitest/runner", "version": "0.32.4", "license": "MIT", "_id": "@vitest/runner@0.32.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2872c697994745f1b70e2bd6568236ad2d9eade6", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.32.4.tgz", "fileCount": 13, "integrity": "sha512-cHOVCkiRazobgdKLnczmz2oaKK9GJOw6ZyRcaPdssO1ej+wzHVIkWiCiNacb3TTYPdzMddYkCgMjZ4r8C0JFCw==", "signatures": [{"sig": "MEYCIQCXGfcjCjbY876qqd9KHu7j2T41l8R1SWo6Je5yQdfvlQIhAIzi8VVt0m1rPEkmNIJulJNeCSDSzSKfBEtqZJ2c27qo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76575}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.32.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f57dcdff9e61acc7cec197f18acf566b/vitest-runner-0.32.4.tgz", "_integrity": "sha512-cHOVCkiRazobgdKLnczmz2oaKK9GJOw6ZyRcaPdssO1ej+wzHVIkWiCiNacb3TTYPdzMddYkCgMjZ4r8C0JFCw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.32.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.32.4_1688382351833_0.7638013526942886", "host": "s3://npm-registry-packages"}}, "0.33.0": {"name": "@vitest/runner", "version": "0.33.0", "license": "MIT", "_id": "@vitest/runner@0.33.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0b1a4d04ff8bc5cdad73920eac019d99550edf9d", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.33.0.tgz", "fileCount": 13, "integrity": "sha512-UPfACnmCB6HKRHTlcgCoBh6ppl6fDn+J/xR8dTufWiKt/74Y9bHci5CKB8tESSV82zKYtkBJo9whU3mNvfaisg==", "signatures": [{"sig": "MEUCIQDh58YuzfisFkqINumFs03IJZfbSnnBGik+ZYexo+A2mAIgLV8aGXazvxu/koJtV+z5v+xSqzTSMBb3HUrSHQ90JwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76575}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.33.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1e633dabd5264eb4c3402211bc2a099f/vitest-runner-0.33.0.tgz", "_integrity": "sha512-UPfACnmCB6HKRHTlcgCoBh6ppl6fDn+J/xR8dTufWiKt/74Y9bHci5CKB8tESSV82zKYtkBJo9whU3mNvfaisg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "8.19.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.33.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.33.0_1688652684148_0.018440412776251636", "host": "s3://npm-registry-packages"}}, "0.34.0": {"name": "@vitest/runner", "version": "0.34.0", "license": "MIT", "_id": "@vitest/runner@0.34.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ad1e44ce460cbd020794dd1bb5e0ef3d38c964c7", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.0.tgz", "fileCount": 13, "integrity": "sha512-xaqM+oArJothtYXzy/dwu/iHe93Khq5QkvnYbzTxiLA0enD2peft1cask3yE6cJpwMkr7C2D1uMJwnTt4mquDw==", "signatures": [{"sig": "MEUCIENJd+pOual1SqK2LmMCoxMGgF6ahnw9jMGpAx7t6MhBAiEA8t+0EF4Eho3iGK+vPHJYev8vmhfg42UpNZ0gREWrEYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76725}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/148af82c50f66be0b9f6d842bdd97341/vitest-runner-0.34.0.tgz", "_integrity": "sha512-xaqM+oArJothtYXzy/dwu/iHe93Khq5QkvnYbzTxiLA0enD2peft1cask3yE6cJpwMkr7C2D1uMJwnTt4mquDw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.0_1690904509523_0.10542814322007366", "host": "s3://npm-registry-packages"}}, "0.34.1": {"name": "@vitest/runner", "version": "0.34.1", "license": "MIT", "_id": "@vitest/runner@0.34.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "23c21ba1db8bff610988c72744db590d0fb6c4ba", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.1.tgz", "fileCount": 13, "integrity": "sha512-YfQMpYzDsYB7yqgmlxZ06NI4LurHWfrH7Wy3Pvf/z/vwUSgq1zLAb1lWcItCzQG+NVox+VvzlKQrYEXb47645g==", "signatures": [{"sig": "MEUCIGNbJkGzk9Cu75loRV+Mt8ux1X44seZOziI7ZsXrIi0zAiEA+BGVpo1RYXwa7K/kMxP/ShGlsgdzaM+2gjR26NVncJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76725}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/68ad87e4261056725254ce50a438368b/vitest-runner-0.34.1.tgz", "_integrity": "sha512-YfQMpYzDsYB7yqgmlxZ06NI4LurHWfrH7Wy3Pvf/z/vwUSgq1zLAb1lWcItCzQG+NVox+VvzlKQrYEXb47645g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.1_1690908827480_0.6196415515157052", "host": "s3://npm-registry-packages"}}, "0.34.2": {"name": "@vitest/runner", "version": "0.34.2", "license": "MIT", "_id": "@vitest/runner@0.34.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3408682cd68475e733a3f151d27792be75d2f07d", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.2.tgz", "fileCount": 13, "integrity": "sha512-8ydGPACVX5tK3Dl0SUwxfdg02h+togDNeQX3iXVFYgzF5odxvaou7HnquALFZkyVuYskoaHUOqOyOLpOEj5XTA==", "signatures": [{"sig": "MEQCIC4hlCY1mNS+wEm3Q1ckzQKBDz7haJqRkc/o0pZB5tJkAiBggNvcBNCEcjH1CPrW+GS+K5vjyJE1HSRrEyx9AjP1wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78290}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a211bbf199c38b006bb5e84208855b37/vitest-runner-0.34.2.tgz", "_integrity": "sha512-8ydGPACVX5tK3Dl0SUwxfdg02h+togDNeQX3iXVFYgzF5odxvaou7HnquALFZkyVuYskoaHUOqOyOLpOEj5XTA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.2_1692266994308_0.2813200712382913", "host": "s3://npm-registry-packages"}}, "0.34.3": {"name": "@vitest/runner", "version": "0.34.3", "license": "MIT", "_id": "@vitest/runner@0.34.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ce09b777d133bbcf843e1a67f4a743365764e097", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.3.tgz", "fileCount": 13, "integrity": "sha512-lYNq7N3vR57VMKMPLVvmJoiN4bqwzZ1euTW+XXYH5kzr3W/+xQG3b41xJn9ChJ3AhYOSoweu974S1V3qDcFESA==", "signatures": [{"sig": "MEQCIGzOYYi0JxGFlaBKbBF2j820HZ7coGkHOrKE8CjzO9CjAiA8YRutjt5emKU+rIqU/trN0hGChoJ8Q1ZaBK19CrngIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78290}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/425c3d706e08f1d71d4d71060cdea859/vitest-runner-0.34.3.tgz", "_integrity": "sha512-lYNq7N3vR57VMKMPLVvmJoiN4bqwzZ1euTW+XXYH5kzr3W/+xQG3b41xJn9ChJ3AhYOSoweu974S1V3qDcFESA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.3_1692948611765_0.4746740620480179", "host": "s3://npm-registry-packages"}}, "0.34.4": {"name": "@vitest/runner", "version": "0.34.4", "license": "MIT", "_id": "@vitest/runner@0.34.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "07543915ad22c53d99fb4bb758cb9bd5db3d7f80", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.4.tgz", "fileCount": 13, "integrity": "sha512-hwwdB1StERqUls8oV8YcpmTIpVeJMe4WgYuDongVzixl5hlYLT2G8afhcdADeDeqCaAmZcSgLTLtqkjPQF7x+w==", "signatures": [{"sig": "MEUCIGZTRh+oztDTWRHgyu5CE3OK8FyaUW2ketqIO0wOVs2tAiEA1p2EWcGfzP3ORmbMGe+HzSoMEsCGdO+YINDCWsCjUbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78339}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/34b64b75209e175af36b24835368e011/vitest-runner-0.34.4.tgz", "_integrity": "sha512-hwwdB1StERqUls8oV8YcpmTIpVeJMe4WgYuDongVzixl5hlYLT2G8afhcdADeDeqCaAmZcSgLTLtqkjPQF7x+w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.4_1694169260632_0.20611063128965745", "host": "s3://npm-registry-packages"}}, "0.34.5": {"name": "@vitest/runner", "version": "0.34.5", "license": "MIT", "_id": "@vitest/runner@0.34.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2bc69a21cd1a09c9403a2a9b0cbd7c42df79f1ae", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.5.tgz", "fileCount": 13, "integrity": "sha512-RDEE3ViVvl7jFSCbnBRyYuu23XxmvRTSZWW6W4M7eC5dOsK75d5LIf6uhE5Fqf809DQ1+9ICZZNxhIolWHU4og==", "signatures": [{"sig": "MEQCICK7C/c4m9asx7mbBR7PKmQVSCvvNh/yQAE6kxYJgVYsAiB6Dg2AMhmWNGKONi8Pl17trLE4rLpaBzlH1oJ2hXqmfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80556}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6791f59064e5cf336b2903861d96cb9c/vitest-runner-0.34.5.tgz", "_integrity": "sha512-RDEE3ViVvl7jFSCbnBRyYuu23XxmvRTSZWW6W4M7eC5dOsK75d5LIf6uhE5Fqf809DQ1+9ICZZNxhIolWHU4og==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.5_1695304235785_0.5773475518258409", "host": "s3://npm-registry-packages"}}, "0.34.6": {"name": "@vitest/runner", "version": "0.34.6", "license": "MIT", "_id": "@vitest/runner@0.34.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6f43ca241fc96b2edf230db58bcde5b974b8dcaf", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.6.tgz", "fileCount": 13, "integrity": "sha512-1CUQgtJSLF47NnhN+F9X2ycxUP0kLHQ/JWvNHbeBfwW8CzEGgeskzNnHDyv1ieKTltuR6sdIHV+nmR6kPxQqzQ==", "signatures": [{"sig": "MEUCIEjEqUcw5tR4Ygcz9gt9IRBGQQqayNmeD0xzPVZuhAQdAiEAn3eyWgns+f4UJ06A1y863PeLau7GZ/GNi0/DCsVyPxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83134}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d85a596d72886008c720101e8d99f87b/vitest-runner-0.34.6.tgz", "_integrity": "sha512-1CUQgtJSLF47NnhN+F9X2ycxUP0kLHQ/JWvNHbeBfwW8CzEGgeskzNnHDyv1ieKTltuR6sdIHV+nmR6kPxQqzQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.6_1695972824339_0.7496017079346116", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.0": {"name": "@vitest/runner", "version": "1.0.0-beta.0", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3357c365e17e951a6cd5463e94f6cb353e24ba17", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.0.tgz", "fileCount": 13, "integrity": "sha512-ZoXTqEWEAcxuNRfGzZGbEXg1nZbtKhRcftdnQ0cajNF2Nt7b5XKHUusCIMuejAePpTZihQNwIKyqe1hzwiju5Q==", "signatures": [{"sig": "MEUCIQCstxIZZP+Hm6AuEbVRJx7F8lsw7o0QtB7i6xoVReRQcQIgKq9D2vbCv5/N2sSRPbc94X9RUPMlDOd7nx/yMi6LB5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85762}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/798604e438455ff81fad9c5b75804d7b/vitest-runner-1.0.0-beta.0.tgz", "_integrity": "sha512-ZoXTqEWEAcxuNRfGzZGbEXg1nZbtKhRcftdnQ0cajNF2Nt7b5XKHUusCIMuejAePpTZihQNwIKyqe1hzwiju5Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "1.0.0-beta.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.0_1696264825828_0.8173790427795262", "host": "s3://npm-registry-packages"}}, "0.34.7": {"name": "@vitest/runner", "version": "0.34.7", "license": "MIT", "_id": "@vitest/runner@0.34.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d2b5572e85992bc501e7fe437568325794ecf562", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-0.34.7.tgz", "fileCount": 13, "integrity": "sha512-o/LQILlF3igWGkY+lXcjPC3c20+KNZdt4JM6Thdg42qFqNw+fCrh1NUH2yHb3FL7Y/4YYIy40Eblb+s/UHfm5w==", "signatures": [{"sig": "MEYCIQDd+0Uc2z0T/Gc69NPC2qW0S89PoPBENQiH2vzwbtgftAIhAPF83sXY6kmV3qGb0nkqsLC0oMyjpmeSdDg+o2xCwojv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83625}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-0.34.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/dee4bae394d7c77ed6264550019d20bc/vitest-runner-0.34.7.tgz", "_integrity": "sha512-o/LQILlF3igWGkY+lXcjPC3c20+KNZdt4JM6Thdg42qFqNw+fCrh1NUH2yHb3FL7Y/4YYIy40Eblb+s/UHfm5w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "0.34.7"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_0.34.7_1696266210057_0.33001587623753914", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "@vitest/runner", "version": "1.0.0-beta.1", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cb3135c795db9f4fb74c137b127aa143e2de469d", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-TbZs0A58pNwELRseqkr//OmNy+8G027Lq1emqibMZ+jmDFd5d0QAWafwbNBXswhiYYZQxnQlcJPndkVyAsqP4g==", "signatures": [{"sig": "MEUCIHSjXlflFJgYfjidzhZDIzp6a+3mEzrM8nxG+0dIHEhgAiEAs0XCOngwLrCkozeWlDVO/47YXLt1A8HddSJzCnxZmgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85762}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fe59e8514e1a3ff5fb87ff8e29bb247f/vitest-runner-1.0.0-beta.1.tgz", "_integrity": "sha512-TbZs0A58pNwELRseqkr//OmNy+8G027Lq1emqibMZ+jmDFd5d0QAWafwbNBXswhiYYZQxnQlcJPndkVyAsqP4g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "1.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.1_1696332692009_0.4202851565047496", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "@vitest/runner", "version": "1.0.0-beta.2", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "04fcaed202d618446c9320744fca180ea0bc5364", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-+W59xEwQg8re9kQOitAYgjjO6LBkPgyUIhCryEslfxwFYJZC3+4CPIJI/8Wac53cUR0NcXzraF8mplE5JYIptQ==", "signatures": [{"sig": "MEYCIQCtX7nO07obTEAaKCqSHTEJMtdnGv7uGU5y/08aCGdY+AIhAPg9RwkjMV070IoV/XKQc9CZX/Fgv51FgzFUEM7qaXMw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85454}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c612cd12b20d8b9efd4bc3b4294b03b8/vitest-runner-1.0.0-beta.2.tgz", "_integrity": "sha512-+W59xEwQg8re9kQOitAYgjjO6LBkPgyUIhCryEslfxwFYJZC3+4CPIJI/8Wac53cUR0NcXzraF8mplE5JYIptQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "1.0.0-beta.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.2_1697182477211_0.8236237622029059", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "@vitest/runner", "version": "1.0.0-beta.3", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "54767e52dee4676770f76472f04d26d0ee612cd6", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-9xakpVjEpuFiyoCt42d0Ff/PiPOlBLQeKjEZbnT3LURi1M26llNVZ2I9ogDQipH3EH9IK0/xDfYAxOjs2jYaDw==", "signatures": [{"sig": "MEYCIQDqoCmYHKUVo/8VKLbPjlaR5Ui/Fda/ALIdI9q0Ktxd9AIhAKgpcELOHjwmR+q7yt5daDeKD74zGtzvrDPzw8JnMA+4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85250}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c8f62a71416934905660bc9337e5a2d8/vitest-runner-1.0.0-beta.3.tgz", "_integrity": "sha512-9xakpVjEpuFiyoCt42d0Ff/PiPOlBLQeKjEZbnT3LURi1M26llNVZ2I9ogDQipH3EH9IK0/xDfYAxOjs2jYaDw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "1.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.3_1698410742014_0.22296545101896936", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "@vitest/runner", "version": "1.0.0-beta.4", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1003f422c12013ca4de1d35f090299da5e0bd355", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-rlXCMp5MxMVVVN5hdhzPL9NpIkfZC0EXwAtN5gwBbCBoVRv9dBQiZ5qTw+LaNmugPl8gm76U4e4/nMZS9s6wyw==", "signatures": [{"sig": "MEUCIHg6IxXmtHfYUx/iILJiOsS5qlWpTXxJeko2qNxS33WMAiEA4oZIZm3uGmY6TZJ9YRoxScJUnFhUfhDxFSids0fVtRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85643}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/9e6bfd546dccb5a8187a1b08f536fcff/vitest-runner-1.0.0-beta.4.tgz", "_integrity": "sha512-rlXCMp5MxMVVVN5hdhzPL9NpIkfZC0EXwAtN5gwBbCBoVRv9dBQiZ5qTw+LaNmugPl8gm76U4e4/nMZS9s6wyw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^4.0.0", "@vitest/utils": "1.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.4_1699524816933_0.46760613495949266", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "@vitest/runner", "version": "1.0.0-beta.5", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "651ce47e5444a085b1d379352d427e543215dbcc", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.5.tgz", "fileCount": 12, "integrity": "sha512-o/6ZqQoKCIdI4dmdc4Yb1u3n56dU69SABXyO5yhFZTDjEMJs1DdCQ68JK+UcrpJMQndr6q5lTFrfHEhj4XJy6w==", "signatures": [{"sig": "MEYCIQDjb5h7aQ2lFdwejJ2Fvj5LM+EnyxFPtwF4SfKxuEJ2uQIhAN9Zn7qXM18Pq5PDUCst5CJzxJ/bTJRQMUTZJTi1FOFL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56798}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/37cd5b8007f0115c5be9b073f57345a1/vitest-runner-1.0.0-beta.5.tgz", "_integrity": "sha512-o/6ZqQoKCIdI4dmdc4Yb1u3n56dU69SABXyO5yhFZTDjEMJs1DdCQ68JK+UcrpJMQndr6q5lTFrfHEhj4XJy6w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.5_1700300672101_0.6600177819765813", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.6": {"name": "@vitest/runner", "version": "1.0.0-beta.6", "license": "MIT", "_id": "@vitest/runner@1.0.0-beta.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "dede8217296375170d30f2512eb7601e6c1ba4fb", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0-beta.6.tgz", "fileCount": 12, "integrity": "sha512-pcFo7XRaALk44ck8LpcjtIsr2Ue4jFNfaq5MDlHupAekkJdDdFrAAfOmsE/LnIqeUsnd/6IaKvLX1oRnCkz6kg==", "signatures": [{"sig": "MEUCIQC+1rPZmSW78qUQLPyrgwcUK4mwb/miQlOJYBVsW/t/OAIgdTjFk0L6yuiM1sro6krhg7lKhIrJ8/6W7TcnuZVQVRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56878}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/74fa4792ea1d04545c5666f7ef6daada/vitest-runner-1.0.0-beta.6.tgz", "_integrity": "sha512-pcFo7XRaALk44ck8LpcjtIsr2Ue4jFNfaq5MDlHupAekkJdDdFrAAfOmsE/LnIqeUsnd/6IaKvLX1oRnCkz6kg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0-beta.6_1701192435943_0.09743327151138059", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@vitest/runner", "version": "1.0.0", "license": "MIT", "_id": "@vitest/runner@1.0.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ee48be0edd1b478a012038a369b545980dc381fc", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-1CaYs4knCexozpGxNiT89foiIxidOdU220QpU6CKMN0qU05e3K5XNH8f4pW9KyXH37o1Zin1cLHkoLr/k7NyrQ==", "signatures": [{"sig": "MEUCIQCLOVKeOw0nIvKuJDpeOpWd0c+iU/nzr42gBlW+9qU/wQIgZIwloKiBH4U2cisZO/M3UpmBQh4RWFuxuBWZqx7L4Fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56864}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6fa8bb11ddc39d48cda85f0d6d3129a5/vitest-runner-1.0.0.tgz", "_integrity": "sha512-1CaYs4knCexozpGxNiT89foiIxidOdU220QpU6CKMN0qU05e3K5XNH8f4pW9KyXH37o1Zin1cLHkoLr/k7NyrQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.0_1701704781901_0.5373555616192995", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@vitest/runner", "version": "1.0.1", "license": "MIT", "_id": "@vitest/runner@1.0.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d94cab9e3008dba52f89e811540184334766ab61", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-/+z0vhJ0MfRPT3AyTvAK6m57rzlew/ct8B2a4LMv7NhpPaiI2QLGyOBMB3lcioWdJHjRuLi9aYppfOv0B5aRQA==", "signatures": [{"sig": "MEQCIDa+GvVQj8yohGXRc7dT3eVgos72ycX6cuB5KqyFPCyjAiAomxZIrB02euZBfjpMQSgJ9IaiD04x2GWsFy4bkDE/fQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56864}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/040173f830ef6fb8c9b8b9687b787470/vitest-runner-1.0.1.tgz", "_integrity": "sha512-/+z0vhJ0MfRPT3AyTvAK6m57rzlew/ct8B2a4LMv7NhpPaiI2QLGyOBMB3lcioWdJHjRuLi9aYppfOv0B5aRQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.1_1701713090159_0.5735668960914175", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@vitest/runner", "version": "1.0.2", "license": "MIT", "_id": "@vitest/runner@1.0.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "aad21c03fdcd1f380564fad37be7d5a2feb2f733", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.2.tgz", "fileCount": 12, "integrity": "sha512-ZcHJXPT2kg/9Hc4fNkCbItlsgZSs3m4vQbxB8LCSdzpbG85bExCmSvu6K9lWpMNdoKfAr1Jn0BwS9SWUcGnbTQ==", "signatures": [{"sig": "MEQCIFGGBnMsHE3Klqig9xuHRbN2VLBwdF7pO7N+ALAamKFzAiB7yqu+3ovOweUAsq7FQ2cUlU+V3ek4Cnaz/+2gIs8XhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56834}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/70ecf9ff619338f0aa9af14d4d63dbf0/vitest-runner-1.0.2.tgz", "_integrity": "sha512-ZcHJXPT2kg/9Hc4fNkCbItlsgZSs3m4vQbxB8LCSdzpbG85bExCmSvu6K9lWpMNdoKfAr1Jn0BwS9SWUcGnbTQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.2_1701943981295_0.8672138410715151", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@vitest/runner", "version": "1.0.3", "license": "MIT", "_id": "@vitest/runner@1.0.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "859610f1937f47413dd7fea7bb984b1eeef398dd", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.3.tgz", "fileCount": 12, "integrity": "sha512-fCqShW4F8VJ78USVRoc5e1OD5jh1x1quZu4Mgp/lIhZS6PZPtI3wdCfRChWO9ZMJ2Ya7WI3sZTJZD69FR/AosA==", "signatures": [{"sig": "MEYCIQD5LDKbYLKegNnmhK5Eq4/oOJobT8+y46Z9Y6+/Ykh2PAIhAJArNJYud9A/hpB1YZqU6o22TtKQ7zwFZLeH/0u80OGA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56939}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/0688b064c4ee6927add4accc05fd82da/vitest-runner-1.0.3.tgz", "_integrity": "sha512-fCqShW4F8VJ78USVRoc5e1OD5jh1x1quZu4Mgp/lIhZS6PZPtI3wdCfRChWO9ZMJ2Ya7WI3sZTJZD69FR/AosA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.3_1702127148350_0.24035586737625114", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@vitest/runner", "version": "1.0.4", "license": "MIT", "_id": "@vitest/runner@1.0.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c4dcb88c07f40b91293ff1331747ee58fad6d5e4", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.0.4.tgz", "fileCount": 12, "integrity": "sha512-rhOQ9FZTEkV41JWXozFM8YgOqaG9zA7QXbhg5gy6mFOVqh4PcupirIJ+wN7QjeJt8S8nJRYuZH1OjJjsbxAXTQ==", "signatures": [{"sig": "MEUCIQC3NIUaLq67Jv671H41y7wXMY3AY/NyC7WQmXUxdI86jAIgcgTzuITi0u7zpUrivZmLWnGLJ8h7oH6gy6PXtad33+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56939}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fca9556ffba34d257e47e5b2db1bdee9/vitest-runner-1.0.4.tgz", "_integrity": "sha512-rhOQ9FZTEkV41JWXozFM8YgOqaG9zA7QXbhg5gy6mFOVqh4PcupirIJ+wN7QjeJt8S8nJRYuZH1OjJjsbxAXTQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.0.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.0.4_1702148719382_0.903221723602174", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@vitest/runner", "version": "1.1.0", "license": "MIT", "_id": "@vitest/runner@1.1.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b3bf60f4a78f4324ca09811dd0f87b721a96b534", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-zdNLJ00pm5z/uhbWF6aeIJCGMSyTyWImy3Fcp9piRGvueERFlQFbUwCpzVce79OLm2UHk9iwaMSOaU9jVHgNVw==", "signatures": [{"sig": "MEUCIDEsZKibDmhVWaSaYbezs9A5oS0ACSjNV8f8TXUcHnBfAiEA7JWredmwoUDMtnAULzhgG5e2d/64HkfS/D/rTktGdfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57155}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ad5cfe007203ec4a2bcd5215cf2b9baa/vitest-runner-1.1.0.tgz", "_integrity": "sha512-zdNLJ00pm5z/uhbWF6aeIJCGMSyTyWImy3Fcp9piRGvueERFlQFbUwCpzVce79OLm2UHk9iwaMSOaU9jVHgNVw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.1.0_1702994791540_0.8256825836156316", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@vitest/runner", "version": "1.1.1", "license": "MIT", "_id": "@vitest/runner@1.1.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c2c2a6baa25f3964c3434e94628b324bc0f19587", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.1.1.tgz", "fileCount": 12, "integrity": "sha512-8HokyJo1SnSi3uPFKfWm/Oq1qDwLC4QDcVsqpXIXwsRPAg3gIDh8EbZ1ri8cmQkBxdOu62aOF9B4xcqJhvt4xQ==", "signatures": [{"sig": "MEUCIQCz+jRPH8r/dlmk8lFkrMyFoHM2wrNHPX8GTX56Hmu5bQIgEXKIqTRIlJVEvZX+cxC9HWjCEBWmJU2MVWGZk246cjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57104}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a57040673100b840d2e9ae479048fd81/vitest-runner-1.1.1.tgz", "_integrity": "sha512-8HokyJo1SnSi3uPFKfWm/Oq1qDwLC4QDcVsqpXIXwsRPAg3gIDh8EbZ1ri8cmQkBxdOu62aOF9B4xcqJhvt4xQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.1.1_1704029879789_0.17388067062702306", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@vitest/runner", "version": "1.1.2", "license": "MIT", "_id": "@vitest/runner@1.1.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8bf8d35e97c27dc13cc2b2ef1ac049bc808967c1", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.1.2.tgz", "fileCount": 12, "integrity": "sha512-oTqXCGtZzu9EaXq9cO/QDGnC721iryuTPs5rLyVZUJsdm33IQeIOwTRIWUB7EYFwpJsI+qMiCiuGZS49+DP5hA==", "signatures": [{"sig": "MEUCIQCkevYhu1VfKcyDj/iwfMEr0dmFkR1yLCB73RLENcfF3gIgTVQ1S2urPSnpjUJlyKc7h+O3ZCZSF7yhQ9Q2qRhEFmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57477}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/aa4c86b96e4ae05cc1e5f5b8e5e04c69/vitest-runner-1.1.2.tgz", "_integrity": "sha512-oTqXCGtZzu9EaXq9cO/QDGnC721iryuTPs5rLyVZUJsdm33IQeIOwTRIWUB7EYFwpJsI+qMiCiuGZS49+DP5hA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.1.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.1.2_1704387523891_0.3826504968422615", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "@vitest/runner", "version": "1.1.3", "license": "MIT", "_id": "@vitest/runner@1.1.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c71e0ab6aad0a6a75c804e060c295852dc052beb", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.1.3.tgz", "fileCount": 12, "integrity": "sha512-Va2XbWMnhSdDEh/OFxyUltgQuuDRxnarK1hW5QNN4URpQrqq6jtt8cfww/pQQ4i0LjoYxh/3bYWvDFlR9tU73g==", "signatures": [{"sig": "MEQCIBDZPakVtUvW9mfnsutqzpGkYgruyK9mOPW0ych4CpiLAiAtGXtDnc06awkqjsKSQfnRkuYUlpWzzqoRYaD991mnPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57477}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fae16d3564782ce04ef7dfc4e4ec12c1/vitest-runner-1.1.3.tgz", "_integrity": "sha512-Va2XbWMnhSdDEh/OFxyUltgQuuDRxnarK1hW5QNN4URpQrqq6jtt8cfww/pQQ4i0LjoYxh/3bYWvDFlR9tU73g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "9.6.7", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.1.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.1.3_1704442864640_0.2789811741747017", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@vitest/runner", "version": "1.2.0", "license": "MIT", "_id": "@vitest/runner@1.2.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "84775f0f5c48620ff1943a45c19863355791c6d9", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.2.0.tgz", "fileCount": 12, "integrity": "sha512-vaJkDoQaNUTroT70OhM0NPznP7H3WyRwt4LvGwCVYs/llLaqhoSLnlIhUClZpbF5RgAee29KRcNz0FEhYcgxqA==", "signatures": [{"sig": "MEQCIDVc2jhb7jEFhyPINVP7/+Ke4IeeoqZcI5HWao2+9jKtAiA7k56DntEw04J1VjMboUFxJjkBKgDQTuOQcAAPj6cObw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57477}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/0f52ba4b689445d8e0ae651ed87efdc5/vitest-runner-1.2.0.tgz", "_integrity": "sha512-vaJkDoQaNUTroT70OhM0NPznP7H3WyRwt4LvGwCVYs/llLaqhoSLnlIhUClZpbF5RgAee29KRcNz0FEhYcgxqA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.2.3", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.2.0_1705075642058_0.05248079139213613", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@vitest/runner", "version": "1.2.1", "license": "MIT", "_id": "@vitest/runner@1.2.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "13e65b47eb04e572b99757e55f063f8f025822b2", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.2.1.tgz", "fileCount": 12, "integrity": "sha512-zc2dP5LQpzNzbpaBt7OeYAvmIsRS1KpZQw4G3WM/yqSV1cQKNKwLGmnm79GyZZjMhQGlRcSFMImLjZaUQvNVZQ==", "signatures": [{"sig": "MEUCIFL+08VjksG2Sfqum0uiT6pgcBZ7p0vzZBVPCfa42v6KAiEAr5C9Zjg2Nyo0v+tFAxVCZEfG16L6bcT9L502o85UAmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57477}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/46333f0e1d095a17946b684ef3dbafef/vitest-runner-1.2.1.tgz", "_integrity": "sha512-zc2dP5LQpzNzbpaBt7OeYAvmIsRS1KpZQw4G3WM/yqSV1cQKNKwLGmnm79GyZZjMhQGlRcSFMImLjZaUQvNVZQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.2.3", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.2.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.2.1_1705508642945_0.4302296837316586", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@vitest/runner", "version": "1.2.2", "license": "MIT", "_id": "@vitest/runner@1.2.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8b060a56ecf8b3d607b044d79f5f50d3cd9fee2f", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.2.2.tgz", "fileCount": 12, "integrity": "sha512-JctG7QZ4LSDXr5CsUweFgcpEvrcxOV1Gft7uHrvkQ+fsAVylmWQvnaAr/HDp3LAH1fztGMQZugIheTWjaGzYIg==", "signatures": [{"sig": "MEUCIQC/BFRt15DERGvZzY73HqnUgn6tZWQzJegNCwqdn3APVgIgUadKPyVAdOdS4JX8jgqQYwnn+dqOOCZWhhyCyij2vVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57639}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ae364645dc03ebbc4413f66cca155f75/vitest-runner-1.2.2.tgz", "_integrity": "sha512-JctG7QZ4LSDXr5CsUweFgcpEvrcxOV1Gft7uHrvkQ+fsAVylmWQvnaAr/HDp3LAH1fztGMQZugIheTWjaGzYIg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.2.3", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.2.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.2.2_1706286352702_0.09720552367471869", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@vitest/runner", "version": "1.3.0", "license": "MIT", "_id": "@vitest/runner@1.3.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1fabbe8d13642473e1acc3450b4df67cf40ae9d1", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.3.0.tgz", "fileCount": 13, "integrity": "sha512-1Jb15Vo/Oy7mwZ5bXi7zbgszsdIBNjc4IqP8Jpr/8RdBC4nF1CTzIAn2dxYvpF1nGSseeL39lfLQ2uvs5u1Y9A==", "signatures": [{"sig": "MEUCIQCFbWB1epj6nDhGAcr7Dkh7/1M1EDdrkBAx5W7OTXvYyAIgIbnKqhP94aFROYsgE7S57hcmFWA+rJdfAo/Ejzx6WoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61594}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.3.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c5b56f22c4ac018fc85d2e430645239e/vitest-runner-1.3.0.tgz", "_integrity": "sha512-1Jb15Vo/Oy7mwZ5bXi7zbgszsdIBNjc4IqP8Jpr/8RdBC4nF1CTzIAn2dxYvpF1nGSseeL39lfLQ2uvs5u1Y9A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.2.3", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.3.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.3.0_1708104548842_0.9324809669129344", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@vitest/runner", "version": "1.3.1", "license": "MIT", "_id": "@vitest/runner@1.3.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e7f96cdf74842934782bfd310eef4b8695bbfa30", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.3.1.tgz", "fileCount": 13, "integrity": "sha512-5FzF9c3jG/z5bgCnjr8j9LNq/9OxV2uEBAITOXfoe3rdZJTdO7jzThth7FXv/6b+kdY65tpRQB7WaKhNZwX+Kg==", "signatures": [{"sig": "MEUCIHc+deYEBmtcl1+J1AMarMdXxmpH6cLFii3EXlqY1VqbAiEAsxGsBb/nEW2KQopADMlXndGwockkAosheZBTqSNnDsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 61582}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.3.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8169847536931cb74ec3daf295d5f513/vitest-runner-1.3.1.tgz", "_integrity": "sha512-5FzF9c3jG/z5bgCnjr8j9LNq/9OxV2uEBAITOXfoe3rdZJTdO7jzThth7FXv/6b+kdY65tpRQB7WaKhNZwX+Kg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.2.4", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.11.0", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.3.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.3.1_1708436910243_0.023416689472835595", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@vitest/runner", "version": "1.4.0", "license": "MIT", "_id": "@vitest/runner@1.4.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "907c2d17ad5975b70882c25ab7a13b73e5a28da9", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.4.0.tgz", "fileCount": 13, "integrity": "sha512-<PERSON>D<PERSON>VSmesqlQ4RD2VvWo3hQgTJ7ZrFQ2VSJdfiJiArkCerDAGeyF1i6dHkmySqk573jLp6d/cfqCN+7wUB5tLgg==", "signatures": [{"sig": "MEYCIQCZH9+ejeqEZlQNDIGYx5/hAhVKJ7hAa0P2MQ2PL9AZPgIhALetUxmKQZKWqOFtxmUgx2VAlpC45cYq9eAj6A1w459j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63457}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.4.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/59d4cef510d04bbc412dce5b852fd859/vitest-runner-1.4.0.tgz", "_integrity": "sha512-<PERSON>D<PERSON>VSmesqlQ4RD2VvWo3hQgTJ7ZrFQ2VSJdfiJiArkCerDAGeyF1i6dHkmySqk573jLp6d/cfqCN+7wUB5tLgg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.2.4", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.11.1", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.4.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.4.0_1710498652398_0.1895140560575812", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@vitest/runner", "version": "1.5.0", "license": "MIT", "_id": "@vitest/runner@1.5.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1f7cb78ee4064e73e53d503a19c1b211c03dfe0c", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.5.0.tgz", "fileCount": 13, "integrity": "sha512-7HWwdxXP5yDoe7DTpbif9l6ZmDwCzcSIK38kTSIt6CFEpMjX4EpCgT6wUmS0xTXqMI6E/ONmfgRKmaujpabjZQ==", "signatures": [{"sig": "MEQCIHWRz0b0x6AjyjFBlLNhX+pqvUverUsDvl1xh8IX8gD4AiAzb2CotsU9AfinEla924VCuuF6mTZho4fN6pbgmioVjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63376}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.5.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bc89bd59331aea10992ce851b111a6f9/vitest-runner-1.5.0.tgz", "_integrity": "sha512-7HWwdxXP5yDoe7DTpbif9l6ZmDwCzcSIK38kTSIt6CFEpMjX4EpCgT6wUmS0xTXqMI6E/ONmfgRKmaujpabjZQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.1", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.5.0_1712857683491_0.15701231400576576", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@vitest/runner", "version": "1.5.1", "license": "MIT", "_id": "@vitest/runner@1.5.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "dbb26a7ce1a3b8c3d186b2c06dd556ed093ddaef", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.5.1.tgz", "fileCount": 13, "integrity": "sha512-mt372zsz0vFR7L1xF/ert4t+teD66oSuXoTyaZbl0eJgilvyzCKP1tJ21gVa8cDklkBOM3DLnkE1ljj/BskyEw==", "signatures": [{"sig": "MEUCIQDKyXVd45//EDMN6NPL85GQP83dW6FGBdIuNmOadBU3aQIgOUqphB0TSy/sSOQbOUEGjnpaKcE7J2tSj+nZ0H1gWLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63588}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.5.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c9d69e854689c4c2206dbe27e535fc85/vitest-runner-1.5.1.tgz", "_integrity": "sha512-mt372zsz0vFR7L1xF/ert4t+teD66oSuXoTyaZbl0eJgilvyzCKP1tJ21gVa8cDklkBOM3DLnkE1ljj/BskyEw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.5.1_1713957749324_0.8921386607346415", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@vitest/runner", "version": "1.5.2", "license": "MIT", "_id": "@vitest/runner@1.5.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "acc9677aaca5c548e3a2746d97eb443c687f0d6f", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.5.2.tgz", "fileCount": 13, "integrity": "sha512-7IJ7sJhMZrqx7HIEpv3WrMYcq8ZNz9L6alo81Y6f8hV5mIE6yVZsFoivLZmr0D777klm1ReqonE9LyChdcmw6g==", "signatures": [{"sig": "MEUCIByIavuxHTT2DYp9/tAaCdn0BB9VVzvG5SiSQoYz/0tcAiEAui5f21Ls86rSEd4YL4JzfodXMvVyMMYNuoKz2QQ00aY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63588}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.5.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e0186365ad8c622264921817867c35d9/vitest-runner-1.5.2.tgz", "_integrity": "sha512-7IJ7sJhMZrqx7HIEpv3WrMYcq8ZNz9L6alo81Y6f8hV5mIE6yVZsFoivLZmr0D777klm1ReqonE9LyChdcmw6g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.5.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.5.2_1714036326286_0.8728093440576694", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@vitest/runner", "version": "1.5.3", "license": "MIT", "_id": "@vitest/runner@1.5.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "226a726ca0bf11c1f287fa867547bdfca072b1e6", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.5.3.tgz", "fileCount": 13, "integrity": "sha512-7PlfuReN8692IKQIdCxwir1AOaP5THfNkp0Uc4BKr2na+9lALNit7ub9l3/R7MP8aV61+mHKRGiqEKRIwu6iiQ==", "signatures": [{"sig": "MEYCIQC+Pg8ZKX9tOsaCUHBTLJVg18aQxIMt12l+qjk5eUpclQIhAIxDPt+C5CnFNy7wY/951SoRyaOB1n4q0vlS3I8BfNZg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63588}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.5.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3471ac5373dda30c42af2aa7a8f3361d/vitest-runner-1.5.3.tgz", "_integrity": "sha512-7PlfuReN8692IKQIdCxwir1AOaP5THfNkp0Uc4BKr2na+9lALNit7ub9l3/R7MP8aV61+mHKRGiqEKRIwu6iiQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.5.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.5.3_1714466430556_0.16988871038964048", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@vitest/runner", "version": "1.6.0", "license": "MIT", "_id": "@vitest/runner@1.6.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a6de49a96cb33b0e3ba0d9064a3e8d6ce2f08825", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.6.0.tgz", "fileCount": 13, "integrity": "sha512-P4xgwPjwesuBiHisAVz/LSSZtDjOTPYZVmNAnpHHSR6ONrf8eCJOFRvUwdHn30F5M1fxhqtl7QZQUk2dprIXAg==", "signatures": [{"sig": "MEUCIGngfMjY9+JXHGZNzvMfncDfHhsrenSDX7W/+rNOVqfbAiEAkxyINWU59G2rMZluHTw+dfYytM2NtDXCecMj4OiMISg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.6.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/30f1bb7ea220de5089181633915d5cdd/vitest-runner-1.6.0.tgz", "_integrity": "sha512-P4xgwPjwesuBiHisAVz/LSSZtDjOTPYZVmNAnpHHSR6ONrf8eCJOFRvUwdHn30F5M1fxhqtl7QZQUk2dprIXAg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.6.0_1714749739515_0.5675015466799067", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "@vitest/runner", "version": "2.0.0-beta.1", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c665e5aa256f29418eb280592ce3a9d7d8d7179d", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-JGnencmnO1fn9I4wohfUSrayT7GU1EaVb84fiYpH0oyZkCV/dkv7IG+81pxFwLfCI3CE6C4wjT13qV/vFK8iFg==", "signatures": [{"sig": "MEUCIQDPh96AD+yXv8+Khi1e5qcaro8/h0qKiesvkA5Y169+4wIgGp/QLhxqaQQoHBCn86SjbhqpIBBufDZTmD8LWRv9lWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64838}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bf00bc5c15b58a72d16382ab657b8429/vitest-runner-2.0.0-beta.1.tgz", "_integrity": "sha512-JGnencmnO1fn9I4wohfUSrayT7GU1EaVb84fiYpH0oyZkCV/dkv7IG+81pxFwLfCI3CE6C4wjT13qV/vFK8iFg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.1_1715265151348_0.48687975542984296", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "@vitest/runner", "version": "2.0.0-beta.2", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8adbff2f3bee63d5f0e3c02447038678cd0aa27c", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-VyFAbSozEMTvz1LwVNCFIDZE5ILZgd2w09QkrvqtaDYw66fPp3tKkAj5fpZBf+FdgW0TAgRVoR6jhc9PKH0gOA==", "signatures": [{"sig": "MEYCIQDyQDT6CJfvfSCf78uVcv+pm93c8rXU55a6iKZrlpIyEQIhALbTWPdxIdkUx1wB/Zy75YlRhJmCC2z4BWw7svjOnyhT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64838}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d45473f4f286be28722cf52edac06ced/vitest-runner-2.0.0-beta.2.tgz", "_integrity": "sha512-VyFAbSozEMTvz1LwVNCFIDZE5ILZgd2w09QkrvqtaDYw66fPp3tKkAj5fpZBf+FdgW0TAgRVoR6jhc9PKH0gOA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.2_1715268689311_0.1109181485292694", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "@vitest/runner", "version": "2.0.0-beta.3", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "002fd773af6ca495be02980e57fff20f321e7434", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-kXv1nelRHt7uRAxX7GMfRFB336DmiQ+9n+voEpN5HmSxJw+SE6k8MbzTsULxBKLtjI2BdpwF48UQaKmXP9adDg==", "signatures": [{"sig": "MEYCIQDu+XA3gdYon9aEcxJvXXVpXl74qbs6pblJWAVRDRse5gIhAL2Zqml7R5lV4DaJUlKc1gbD5DWzJjQ21e85rl2qYPzQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65492}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ae16657e4ed7445edba5071d3a6b23f6/vitest-runner-2.0.0-beta.3.tgz", "_integrity": "sha512-kXv1nelRHt7uRAxX7GMfRFB336DmiQ+9n+voEpN5HmSxJw+SE6k8MbzTsULxBKLtjI2BdpwF48UQaKmXP9adDg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.3_1715712285776_0.2748072511335631", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "@vitest/runner", "version": "2.0.0-beta.4", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d57eb01a1edc0d7211590a35152c0b9bba3563cc", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-DTTWOAu/ISuoUzxXQrPSLFKCIqp7VjkwL7OC2sqIE78PYxbSJx5FCUxWoIeaeJPjfLYzBjPU2iDBKM6h2ZyYFg==", "signatures": [{"sig": "MEUCIQC5sHagSZy7eGU72LxBil+/IfZDS3wi70Se2fffgH8gBgIgOK/7dmDCaJ04Jg+YA7qkfYPzFnjMqqJPG9InW59LkEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65558}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/608a906ff1d15d91fbaf522d7b81c7b2/vitest-runner-2.0.0-beta.4.tgz", "_integrity": "sha512-DTTWOAu/ISuoUzxXQrPSLFKCIqp7VjkwL7OC2sqIE78PYxbSJx5FCUxWoIeaeJPjfLYzBjPU2iDBKM6h2ZyYFg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.4_1717330559555_0.706555345184618", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "@vitest/runner", "version": "2.0.0-beta.5", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1a8f06ed81f0b3f808fc63f9f7d9e00f047c26bd", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-1MEC6x7kK8PMaLjaspcBfaJftmIOdLlp+HrYc6KiW3EDM6TIvgLrqTcUFD0zj/Kq4AVNGgvJJN8vCDIV7nO0Gw==", "signatures": [{"sig": "MEUCIQCQGXnLvjU6iMVUAQktM930eT4HBftLeZ7L4CsT9NRUxAIgazauZP/FnTD9jVKzIdxpG2mqr/vTkPIv/J6nYVBhWzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65558}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bcc89aff0b3780f752bcf9d0c91e7d23/vitest-runner-2.0.0-beta.5.tgz", "_integrity": "sha512-1MEC6x7kK8PMaLjaspcBfaJftmIOdLlp+HrYc6KiW3EDM6TIvgLrqTcUFD0zj/Kq4AVNGgvJJN8vCDIV7nO0Gw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.5_1717331272782_0.7438272978069722", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "@vitest/runner", "version": "2.0.0-beta.6", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a8b261f0b001df78c1baa7f01efae2ea24f9eb49", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-6u/UJrOuEPDZkvuHC+hbQV0c8H3SUgiyYnBOu5KQivPjCLzw7svxIWg8xB+WIcl3TQuKcJRw7eaF6Xa98d8qnw==", "signatures": [{"sig": "MEYCIQDB+CrvZxsqtS3Kl6Kg/UcsBFpxUiAWEfsVEkxzqviO3AIhAOP3vj0lU9G72oEQvMZEU/2bHJiEc+U4BKg5LMsGji3i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65558}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/040adf0071f74e8b6b7a77971c39c79f/vitest-runner-2.0.0-beta.6.tgz", "_integrity": "sha512-6u/UJrOuEPDZkvuHC+hbQV0c8H3SUgiyYnBOu5KQivPjCLzw7svxIWg8xB+WIcl3TQuKcJRw7eaF6Xa98d8qnw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.6_1717355849949_0.48875939395462975", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "@vitest/runner", "version": "2.0.0-beta.7", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "56dacb462e52e99b286ebe0a281747d75cb008aa", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.7.tgz", "fileCount": 13, "integrity": "sha512-PE8Pyqac1od34C2lm6kvcUbP4W/zeTsGi9sFTfaPU28lkS/3gq+jP28ztyfYAOikcCVP35K3m7ZaP+3QSL7oOw==", "signatures": [{"sig": "MEQCIFvc2/fF/5HfAT2N2qiMfCTdPQZWXKwgodTGHM5BDxslAiBAp8NPQR7eXVtKqeJHJYuYra50RoQJBVAvLW8KGMGpfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65956}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f1b363262f32c857f9f4a6e53294586e/vitest-runner-2.0.0-beta.7.tgz", "_integrity": "sha512-PE8Pyqac1od34C2lm6kvcUbP4W/zeTsGi9sFTfaPU28lkS/3gq+jP28ztyfYAOikcCVP35K3m7ZaP+3QSL7oOw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.7_1717414551676_0.20226000918059395", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "@vitest/runner", "version": "2.0.0-beta.8", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.8", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b246c4d84e571ede3d375c00f18d319f8c93a0af", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.8.tgz", "fileCount": 13, "integrity": "sha512-L+U0vw0TyM7UA2EfxcR/3TTdlkqzDP5Uxj3SpsAsw9b9rFDtw5ZhrRnRRb4LZDildt3q/P/kjuJG6LSigVoITQ==", "signatures": [{"sig": "MEUCIQDZ7KyXfRDXk1wuMU/4k7Dy8uI/Zx8mRSTa5tqDavIAGwIgfZuBNYaoVh4o6JgPNabbsy1TXfXKZ36rCJOUIgZmUak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65956}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a683eafb461a98c77a8c0e63bd3e27bb/vitest-runner-2.0.0-beta.8.tgz", "_integrity": "sha512-L+U0vw0TyM7UA2EfxcR/3TTdlkqzDP5Uxj3SpsAsw9b9rFDtw5ZhrRnRRb4LZDildt3q/P/kjuJG6LSigVoITQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.8_1717504769865_0.6328930396100092", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "@vitest/runner", "version": "2.0.0-beta.9", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.9", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e20a9765005876bb589eaf4d1c3daad8aaab986b", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.9.tgz", "fileCount": 13, "integrity": "sha512-ZwmBOfg8Fl/dl66/ds7WnHqM5m+f6HacgPxbvHgZHRaE7F9YRwjfraJx3MJTrI02I+fuftKLNr/1obnthyePeQ==", "signatures": [{"sig": "MEUCIEmwwGkZY09+WW0DFsmVamC0hVBYoquTaVYi1dGEp2qEAiEA/nqQyiHxCQE7k9T4izn7Rm0LK5GwPd1/0C1LfNsdxvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65956}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/58f7dabf80c8457feddeefa34c89baca/vitest-runner-2.0.0-beta.9.tgz", "_integrity": "sha512-ZwmBOfg8Fl/dl66/ds7WnHqM5m+f6HacgPxbvHgZHRaE7F9YRwjfraJx3MJTrI02I+fuftKLNr/1obnthyePeQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.5.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.9"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.9_1717574466870_0.2917595165337692", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "@vitest/runner", "version": "2.0.0-beta.10", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.10", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c26da0ce5836d370b9608672258b858d546f8144", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.10.tgz", "fileCount": 13, "integrity": "sha512-OYqqCCOWoD0jJCJuAr5mNqlI98b1IUUw4o57JgoJ3sU1FRvgeb2fJX6n3/EFFGz9vkCBshiqkICZsEVy8AxUbQ==", "signatures": [{"sig": "MEQCIH87lxPmG21gsnio6dEE6ITtK6Rip56pdXGdWhkD6J1AAiBJB4WbzGFcRKKHiqB9q0E30IoPwYPqFdD37DACSeplRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67288}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2e0dcdc54794626f3ce0e9490c210d78/vitest-runner-2.0.0-beta.10.tgz", "_integrity": "sha512-OYqqCCOWoD0jJCJuAr5mNqlI98b1IUUw4o57JgoJ3sU1FRvgeb2fJX6n3/EFFGz9vkCBshiqkICZsEVy8AxUbQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.14.0", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.10_1718194301520_0.7438960589595944", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.11": {"name": "@vitest/runner", "version": "2.0.0-beta.11", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.11", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3cae45421b7161c58569e2bd22b8b710b33b0222", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.11.tgz", "fileCount": 13, "integrity": "sha512-g1Dp0PGJf3m6al5vQuOXtKpqqMJuS2KctgzkLJESAG3IOKC36FaJImqNjDclKPPSwrkUSjEyC5XLh7mFG1OqGA==", "signatures": [{"sig": "MEUCIQCzD9yHbQTx60HOjvEHctT7CwPgqGvoqrq5S5jDUusl8QIgZc9mQHU9acpXtKqGEGWblnQJO+nkuJs2HTXvot8yy94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 68898}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.11.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/940ae7dd89cb22681f90794d0a9ceec7/vitest-runner-2.0.0-beta.11.tgz", "_integrity": "sha512-g1Dp0PGJf3m6al5vQuOXtKpqqMJuS2KctgzkLJESAG3IOKC36FaJImqNjDclKPPSwrkUSjEyC5XLh7mFG1OqGA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.14.0", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.11"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.11_1718828044555_0.4939236708362418", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.12": {"name": "@vitest/runner", "version": "2.0.0-beta.12", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.12", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6523f6a6a76449b4db52c4489e899cb4f5f32e01", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.12.tgz", "fileCount": 13, "integrity": "sha512-nAerpQvAw1/6vO4vRjOy0A+7IwtktSME3thwUoqWZxMKBgmTzIO2/WevbtFsAwYPc3V8NEY/Erv4PjQt9JTlzQ==", "signatures": [{"sig": "MEYCIQDBqkcCTJn3quXXOmD7Vgal7+n91Svt/eZsXbBsE7TsIwIhAN0X8FkX9kE2HNUCCY9f6BzmsGukOOhu1Liy6uZWqihK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69231}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.12.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/01e848ae2a4f46258b1802e555ab294b/vitest-runner-2.0.0-beta.12.tgz", "_integrity": "sha512-nAerpQvAw1/6vO4vRjOy0A+7IwtktSME3thwUoqWZxMKBgmTzIO2/WevbtFsAwYPc3V8NEY/Erv4PjQt9JTlzQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.14.0", "dependencies": {"pathe": "^1.1.2", "p-limit": "^5.0.0", "@vitest/utils": "2.0.0-beta.12"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.12_1719346581099_0.7287393546589067", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.13": {"name": "@vitest/runner", "version": "2.0.0-beta.13", "license": "MIT", "_id": "@vitest/runner@2.0.0-beta.13", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b17faded89796a3837e1cc9318a95516f3bacb63", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0-beta.13.tgz", "fileCount": 13, "integrity": "sha512-50J8rYy3u3sjxNc+IfCu9xnCn8Z3vpmUeKO0d1zf0SNrvm+1QurFg0AxVbsaqc0jIdjR9bVZwwtrOF8rM2pqsg==", "signatures": [{"sig": "MEQCIEnLIc8VWhZHXLihOtvaB5vBMWZ18qciklsoEdEcZ7OyAiBxZb6iWd9QbJGdq5eSOG6fZ6blK4LLPbFfh4B942BEhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 70707}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0-beta.13.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e0d6ec529bde93be5c56eff142d0d6a9/vitest-runner-2.0.0-beta.13.tgz", "_integrity": "sha512-50J8rYy3u3sjxNc+IfCu9xnCn8Z3vpmUeKO0d1zf0SNrvm+1QurFg0AxVbsaqc0jIdjR9bVZwwtrOF8rM2pqsg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.0-beta.13"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0-beta.13_1720101827099_0.7688390762806196", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@vitest/runner", "version": "2.0.0", "license": "MIT", "_id": "@vitest/runner@2.0.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "11034c38febfe06d7e828fcd1327bde538ddbb7a", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.0.tgz", "fileCount": 13, "integrity": "sha512-OovFmlkfRmdhevbWImBUtn9IEM+CKac8O+m9p6W9jTATGVBnDJQ6/jb1gpHyWxsu0ALi5f+TLi+Uyst7AAimMw==", "signatures": [{"sig": "MEQCIGZQfFQelR8dryEKe9xj4QbR32kghvmOY2cEkq56TsNMAiBPH/xHPiqPv97Q1wWRxv0u1uxFPpUcBg7UXrWUVkZOFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 70691}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c1973f02033850f06a7e132943e4cc73/vitest-runner-2.0.0.tgz", "_integrity": "sha512-OovFmlkfRmdhevbWImBUtn9IEM+CKac8O+m9p6W9jTATGVBnDJQ6/jb1gpHyWxsu0ALi5f+TLi+Uyst7AAimMw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.0_1720438767519_0.13741892157884994", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@vitest/runner", "version": "2.0.1", "license": "MIT", "_id": "@vitest/runner@2.0.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "eb5c2fbb135cc7d73b4f289555fe7c08f1a6f54a", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.1.tgz", "fileCount": 13, "integrity": "sha512-XfcSXOGGxgR2dQ466ZYqf0ZtDLLDx9mZeQcKjQDLQ9y6Cmk2Wl7wxMuhiYK4Fo1VxCtLcFEGW2XpcfMuiD1Maw==", "signatures": [{"sig": "MEUCIQCxFKf1uzQzFZsuqsEnaUsJLRlYLPzjOWechfwxtlmEkgIgEMnftRR5E8OqJelDSbNCk6MbATzP36yB5eeV+HeFcZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 70792}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1092d0094413aad5b370bc2d70981ef2/vitest-runner-2.0.1.tgz", "_integrity": "sha512-XfcSXOGGxgR2dQ466ZYqf0ZtDLLDx9mZeQcKjQDLQ9y6Cmk2Wl7wxMuhiYK4Fo1VxCtLcFEGW2XpcfMuiD1Maw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.1_1720452784694_0.3522297349946135", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@vitest/runner", "version": "2.0.2", "license": "MIT", "_id": "@vitest/runner@2.0.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5716c25f762308e4c87485668e4654cd4b832a73", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.2.tgz", "fileCount": 13, "integrity": "sha512-OCh437Vi8Wdbif1e0OvQcbfM3sW4s2lpmOjAE7qfLrpzJX2M7J1IQlNvEcb/fu6kaIB9n9n35wS0G2Q3en5kHg==", "signatures": [{"sig": "MEYCIQDu9itln3oWm1N8pYdXl91p8X5HbX9jef8n+2cqpwPYagIhAMSSOwZKN8rcbGy1nzG8/5hGm4ttFltHtJoiY4oLsJgp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 70792}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c8b9c9976656169e285f1fa9af9f5699/vitest-runner-2.0.2.tgz", "_integrity": "sha512-OCh437Vi8Wdbif1e0OvQcbfM3sW4s2lpmOjAE7qfLrpzJX2M7J1IQlNvEcb/fu6kaIB9n9n35wS0G2Q3en5kHg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.2_1720626397385_0.04358044362990099", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@vitest/runner", "version": "2.0.3", "license": "MIT", "_id": "@vitest/runner@2.0.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4310ff4583d7874f57b5a8a194062bb85f07b0df", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.3.tgz", "fileCount": 13, "integrity": "sha512-EmSP4mcjYhAcuBWwqgpjR3FYVeiA4ROzRunqKltWjBfLNs1tnMLtF+qtgd5ClTwkDP6/DGlKJTNa6WxNK0bNYQ==", "signatures": [{"sig": "MEUCIEmCfi+7CmMc8xKcI8U2BWksKFnmhreMmp9ASstyCkKyAiEAgr91FaGTDlMaiUoGzap1WbWUV+5D9hqb4VEFaSxHJBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 70792}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1c5766168a8eb640fdba4bb8b6828307/vitest-runner-2.0.3.tgz", "_integrity": "sha512-EmSP4mcjYhAcuBWwqgpjR3FYVeiA4ROzRunqKltWjBfLNs1tnMLtF+qtgd5ClTwkDP6/DGlKJTNa6WxNK0bNYQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.3_1721037814904_0.33801778178297326", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@vitest/runner", "version": "2.0.4", "license": "MIT", "_id": "@vitest/runner@2.0.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0b1edb8ab5f81a1c7dfd50090e5e7e971a117891", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.4.tgz", "fileCount": 13, "integrity": "sha512-Gk+9Su/2H2zNfNdeJR124gZckd5st4YoSuhF1Rebi37qTXKnqYyFCd9KP4vl2cQHbtuVKjfEKrNJxHHCW8thbQ==", "signatures": [{"sig": "MEUCIBLWYV8grLW2J//xb/0E5wjBDQyuGdddcO+OU6jPHuwCAiEAuzdcKRVqXclaelwqtn4UWuOyB8VTNMQpEKRcQW3wFMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 82087}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/684e40f9c199fadfc5822241fffa0211/vitest-runner-2.0.4.tgz", "_integrity": "sha512-Gk+9Su/2H2zNfNdeJR124gZckd5st4YoSuhF1Rebi37qTXKnqYyFCd9KP4vl2cQHbtuVKjfEKrNJxHHCW8thbQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.4_1721639608762_0.16824825766639329", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@vitest/runner", "version": "2.0.5", "license": "MIT", "_id": "@vitest/runner@2.0.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "89197e712bb93513537d6876995a4843392b2a84", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.0.5.tgz", "fileCount": 13, "integrity": "sha512-TfRfZa6Bkk9ky4tW0z20WKXFEwwvWhRY+84CnSEtq4+3ZvDlJyY32oNTJtM7AW9ihW90tX/1Q78cb6FjoAs+ig==", "signatures": [{"sig": "MEUCIDH8k9AXtaXg6b9wcDQW0E7MilLNSIjJv29gbBPHcaclAiEAy0WyIjxPc9itTMTtWUt0jnqhbOfamaebuT73rKTz1a4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 82423}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ff9bd721c6ee5960bf5c0329a4a8bd9a/vitest-runner-2.0.5.tgz", "_integrity": "sha512-TfRfZa6Bkk9ky4tW0z20WKXFEwwvWhRY+84CnSEtq4+3ZvDlJyY32oNTJtM7AW9ihW90tX/1Q78cb6FjoAs+ig==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.7.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.15.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.0.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.0.5_1722422400963_0.42726167291696826", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.1": {"name": "@vitest/runner", "version": "2.1.0-beta.1", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6e47f4f7c4a8a653cc80b0cc91ceaf63abed2b67", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-KJ5drnZCvoWFFocbWAQQXfl1H9TXTjDLf/mRXn84FfQOKUyPoQOEXPSoeW/S50mthVoxR9wUbF/VgpDzWcGDtA==", "signatures": [{"sig": "MEUCIAkcnoSb9Z1+RvpLry9LdYldE1Hpef7q1Uol9ICpopxkAiEAzS2zL1nNfY8RGyLyj3yEY+QfyrVo8BwlfA1Luz+vcLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/05f72f1f26d1e83c6d1ae0b908891dee/vitest-runner-2.1.0-beta.1.tgz", "_integrity": "sha512-KJ5drnZCvoWFFocbWAQQXfl1H9TXTjDLf/mRXn84FfQOKUyPoQOEXPSoeW/S50mthVoxR9wUbF/VgpDzWcGDtA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.1", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.1_1723011688551_0.3667112353035724", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.2": {"name": "@vitest/runner", "version": "2.1.0-beta.2", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e7148c0918f72c2222633bdd0956f14c9dc7864c", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-dR3PF4ev+gvKCgRWtUU27gYc+Bel5dHUEP4od0Aqcve74Aj2P1q1vIHoXP50swg3rS9Fq2vnXODdKZ17ppM85g==", "signatures": [{"sig": "MEQCIFpVgwy0q+NiRBQPeOVz8gfbos0PbP5JpsqPjeKzLG5lAiB0u2ehdQWz2YHG97hZPrakiEtvVK1xl1+09qR1Dz9uIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e1ef3267f842e5854b9f3917a7db4965/vitest-runner-2.1.0-beta.2.tgz", "_integrity": "sha512-dR3PF4ev+gvKCgRWtUU27gYc+Bel5dHUEP4od0Aqcve74Aj2P1q1vIHoXP50swg3rS9Fq2vnXODdKZ17ppM85g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.1", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.2_1723017412715_0.8046309324394023", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.3": {"name": "@vitest/runner", "version": "2.1.0-beta.3", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2f7275f7a4f6e251179b8f0937d01d87b5d1a9b3", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-SIr8nCCaFLNqtpPKTc5TCxJNRjWIFRFzJCRrvYctkpG0TvNeSCgereXdBSOoB1H7rkRLThCuBxv9qPsiDD97wg==", "signatures": [{"sig": "MEUCIDuZ+dL43nmM2DtNbefj8dscCrWzm6SvNv15Eb3vcCsaAiEAphqoQDpYOFW9UxnSLdui3ieGSu+FLtxcy79iNeRmvyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1689ef6ee538e5b94ce845c6dcb62850/vitest-runner-2.1.0-beta.3.tgz", "_integrity": "sha512-SIr8nCCaFLNqtpPKTc5TCxJNRjWIFRFzJCRrvYctkpG0TvNeSCgereXdBSOoB1H7rkRLThCuBxv9qPsiDD97wg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.1", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.3_1723018625145_0.6343391429609797", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.4": {"name": "@vitest/runner", "version": "2.1.0-beta.4", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "23d9f7fcc949b83fe7f157d169329a24c02c1092", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-ghMoGHk9dYHstQWqGvvzIDxzcb9DIo4TXmoo7fyS/eqU3QBz+/eSkRhii4rr/HLLVGGdretP9JBeP8kk0zfgKA==", "signatures": [{"sig": "MEUCIANaThie4NOSGCAq6n3R73wjIUSw38NzpSl7ntJUGqfCAiEAnAXqPDyBaRf7rNOW8/t+ukZ60WSCfT+uSDYHcp02gA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/704e86b9cb48ae398ccf006d2a70eb6c/vitest-runner-2.1.0-beta.4.tgz", "_integrity": "sha512-ghMoGHk9dYHstQWqGvvzIDxzcb9DIo4TXmoo7fyS/eqU3QBz+/eSkRhii4rr/HLLVGGdretP9JBeP8kk0zfgKA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.1", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.4_1723030974077_0.892155338942392", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.5": {"name": "@vitest/runner", "version": "2.1.0-beta.5", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0b7796d35ec4d1c3f94a428c297c2282fbd42bc6", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-2kPvcAtZlb+l/bjEF4eDGTNqbausgbau8nlnZnx2iZUin5E6VxEgzMckvxZyPbt6QKQOvCPcsXRnoM0cckctVA==", "signatures": [{"sig": "MEUCIHcFPJhWNmLtOu/GFHe9JnX25tl+WTi/Cz0XagyXk2vPAiEA5FVJANl9mCDCKn/V/kYzicx+N4QYSq21KA/qJinRNTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/69163ff46745e0d04f5e20d50c7ad8dd/vitest-runner-2.1.0-beta.5.tgz", "_integrity": "sha512-2kPvcAtZlb+l/bjEF4eDGTNqbausgbau8nlnZnx2iZUin5E6VxEgzMckvxZyPbt6QKQOvCPcsXRnoM0cckctVA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.1", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.5_1723462521983_0.1969601780758936", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.6": {"name": "@vitest/runner", "version": "2.1.0-beta.6", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0d2106541024f27d813d9397408c2db44e77b109", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-0JcfITMlQDXlYeEgumZF60V6qB/4aBuYC4dXzT0EJTsgekp4l/qFCqX1dJp6a4kbq2rkpCIqBrrmzTaw8blDKw==", "signatures": [{"sig": "MEUCICmxCIJk6Is5b4vr3Tbzh1KotmgMk3ke/rWkMWkZlt4YAiEAu2gdbXg6hbm3/2S1ZxPx4RywHo1g3qUK/ZLQuQFjvdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9c86c3996e0125f9bbb35d00888793d9/vitest-runner-2.1.0-beta.6.tgz", "_integrity": "sha512-0JcfITMlQDXlYeEgumZF60V6qB/4aBuYC4dXzT0EJTsgekp4l/qFCqX1dJp6a4kbq2rkpCIqBrrmzTaw8blDKw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.1", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.6_1724159914525_0.7373421099351323", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.7": {"name": "@vitest/runner", "version": "2.1.0-beta.7", "license": "MIT", "_id": "@vitest/runner@2.1.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6ace68912234aa56eadb9e9643da892cb8325c99", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0-beta.7.tgz", "fileCount": 13, "integrity": "sha512-4HWnNpHw3irp1Tz7PU5GXPq2kmjfyJVrjnhTUsnEucKBhuVoyV2g578ILLKz7uI8uXGYo5jk3iMXwZ2y53tVQA==", "signatures": [{"sig": "MEQCIDoMChtSNUVErN2kGAvVQxNEyDvmcd1eGJeMHKMuy8bsAiB7fFD+y+DutEPaGmEXVCVho+SEQORiZDLoXmZ9sC03bA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87893}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d9072b76245ab8d1d1d5531f0fd33990/vitest-runner-2.1.0-beta.7.tgz", "_integrity": "sha512-4HWnNpHw3irp1Tz7PU5GXPq2kmjfyJVrjnhTUsnEucKBhuVoyV2g578ILLKz7uI8uXGYo5jk3iMXwZ2y53tVQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0-beta.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0-beta.7_1725894800870_0.07104750689938855", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vitest/runner", "version": "2.1.0", "license": "MIT", "_id": "@vitest/runner@2.1.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1e2d902c669cb3d3d5256b7f76a9449db2f33215", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.0.tgz", "fileCount": 13, "integrity": "sha512-D9+ZiB8MbMt7qWDRJc4CRNNUlne/8E1X7dcKhZVAbcOKG58MGGYVDqAq19xlhNfMFZsW0bpVKgztBwks38Ko0w==", "signatures": [{"sig": "MEYCIQDUZDJnGfOiDxTQgT/PfhvGPtNufaOiMRWmLXhMoo84IQIhAK+vHLpfV3od6Ka8zgseTSNWGA9sBe7BE06fxZTYFPtZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87879}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/336f8deebf6818b25159ebea5fea0648/vitest-runner-2.1.0.tgz", "_integrity": "sha512-D9+ZiB8MbMt7qWDRJc4CRNNUlne/8E1X7dcKhZVAbcOKG58MGGYVDqAq19xlhNfMFZsW0bpVKgztBwks38Ko0w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.0_1726149807495_0.6284772639129927", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vitest/runner", "version": "2.1.1", "license": "MIT", "_id": "@vitest/runner@2.1.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f3b1fbc3c109fc44e2cceecc881344453f275559", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.1.tgz", "fileCount": 13, "integrity": "sha512-uTPuY6PWOYitIkLPidaY5L3t0JJITdGTSwBtwMjKzo5O6RCOEncz9PUN+0pDidX8kTHYjO0EwUIvhlGpnGpxmA==", "signatures": [{"sig": "MEQCIHdFGLaPnTmatZ87OO6OP/vBrV5AA0VK/iA5gpFvh1C+AiBZ79TLu0v68FwynNrYhVf249jTwJuuz5E7cYzn8kGY3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87879}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a02a976f243c8eb3d9ad821619230035/vitest-runner-2.1.1.tgz", "_integrity": "sha512-uTPuY6PWOYitIkLPidaY5L3t0JJITdGTSwBtwMjKzo5O6RCOEncz9PUN+0pDidX8kTHYjO0EwUIvhlGpnGpxmA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.1_1726241561486_0.9698288191644002", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vitest/runner", "version": "2.1.2", "license": "MIT", "_id": "@vitest/runner@2.1.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "14da1f5eac43fbd9a37d7cd72de102e8f785d727", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.2.tgz", "fileCount": 13, "integrity": "sha512-UCsPtvluHO3u7jdoONGjOSil+uON5SSvU9buQh3lP7GgUXHp78guN1wRmZDX4wGK6J10f9NUtP6pO+SFquoMlw==", "signatures": [{"sig": "MEUCIArhYAOieoXEXdsd4OK5U+0p98ZDtZ5EmMrx87DCzoUFAiEAkTqLzbehng2GUJyFL6K8ikaJyNVxBeglB8CpYIsgDSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88581}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5e7a3d1d397cd5c785e7fd3b90224149/vitest-runner-2.1.2.tgz", "_integrity": "sha512-UCsPtvluHO3u7jdoONGjOSil+uON5SSvU9buQh3lP7GgUXHp78guN1wRmZDX4wGK6J10f9NUtP6pO+SFquoMlw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.2_1727886009011_0.39806458185624916", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vitest/runner", "version": "2.1.3", "license": "MIT", "_id": "@vitest/runner@2.1.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "20a6da112007dfd92969951df189c6da66c9dac4", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.3.tgz", "fileCount": 13, "integrity": "sha512-JGzpWqmFJ4fq5ZKHtVO3Xuy1iF2rHGV4d/pdzgkYHm1+gOzNZtqjvyiaDGJytRyMU54qkxpNzCx+PErzJ1/JqQ==", "signatures": [{"sig": "MEUCIQDYVmO2XhLmqcKS0Z7D2rvDB7+l0GzoJClGTZcaujGj2QIgXQJlDX8Cx774QZeoVEcKfndav2vqnQNmRTClGFQdpmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88919}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/225983114b7a96403154ac37963fc549/vitest-runner-2.1.3.tgz", "_integrity": "sha512-JGzpWqmFJ4fq5ZKHtVO3Xuy1iF2rHGV4d/pdzgkYHm1+gOzNZtqjvyiaDGJytRyMU54qkxpNzCx+PErzJ1/JqQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.3_1728903934106_0.9513331212738247", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vitest/runner", "version": "2.1.4", "license": "MIT", "_id": "@vitest/runner@2.1.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f9346500bdd0be1c926daaac5d683bae87ceda2c", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.4.tgz", "fileCount": 13, "integrity": "sha512-sKRautINI9XICAMl2bjxQM8VfCMTB0EbsBc/EDFA57V6UQevEKY/TOPOF5nzcvCALltiLfXWbq4MaAwWx/YxIA==", "signatures": [{"sig": "MEYCIQDeEbP2SR+5eZeLEzT6J8wefLmDHJ7EUtPBPoHc/GuhKQIhAO/Gt5tK2TlkwfInIe9nBdEnjP3jwRUkYWS50R3bGkjC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89254}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bfb226d0356d490f50f7e9f1b32faee5/vitest-runner-2.1.4.tgz", "_integrity": "sha512-sKRautINI9XICAMl2bjxQM8VfCMTB0EbsBc/EDFA57V6UQevEKY/TOPOF5nzcvCALltiLfXWbq4MaAwWx/YxIA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.4_1730118442443_0.1258538800592084", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@vitest/runner", "version": "2.1.5", "license": "MIT", "_id": "@vitest/runner@2.1.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4d5e2ba2dfc0af74e4b0f9f3f8be020559b26ea9", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.5.tgz", "fileCount": 13, "integrity": "sha512-pKHKy3uaUdh7X6p1pxOkgkVAFW7r2I818vHDthYLvUyjRfkKOU6P45PztOch4DZarWQne+VOaIMwA/erSSpB9g==", "signatures": [{"sig": "MEUCIF7VUPjsKj8wsiukUQsCoUd1vGGqv2C6UT4TY6m3fLCXAiEAtbo8K6a6bEOmRHDcjAAxvVyhsit0YKzKLIUujvk6zOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89264}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ccf9d76d57b823d9399437607089f571/vitest-runner-2.1.5.tgz", "_integrity": "sha512-pKHKy3uaUdh7X6p1pxOkgkVAFW7r2I818vHDthYLvUyjRfkKOU6P45PztOch4DZarWQne+VOaIMwA/erSSpB9g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.5_1731511450475_0.8795272693711977", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@vitest/runner", "version": "2.2.0-beta.1", "license": "MIT", "_id": "@vitest/runner@2.2.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1ae499f3d70e099932555bdd33953de2a0171ee8", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.2.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-7ficOLS16vTe3oRztp1ni1lrCQjRV+XJbu1KgNIPVcx8ZHrztL5TqRuHmxxBpdmcuoeQq1HpyEFepf8WZfa0Gg==", "signatures": [{"sig": "MEQCIB0Izm4VAK4roMVIzbyQEHvG3a4reT/aSnhANV03VJroAiA2tgOzpGDw62kmV2qIq09W667+nv1zT8rAhIgTeE+Raw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 90024}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2eaf951c0cfec1b7ac330c1e586c5b4d/vitest-runner-2.2.0-beta.1.tgz", "_integrity": "sha512-7ficOLS16vTe3oRztp1ni1lrCQjRV+XJbu1KgNIPVcx8ZHrztL5TqRuHmxxBpdmcuoeQq1HpyEFepf8WZfa0Gg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.2.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.2.0-beta.1_1731518242145_0.9938173964121448", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@vitest/runner", "version": "2.2.0-beta.2", "license": "MIT", "_id": "@vitest/runner@2.2.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "793a8d7947fd87907cfb83c56c80556ea9dabee7", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.2.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-XV7E7S7VwPfLkB+cvpM+ahwNEikSJSDT9SRKNxZMHd2lo7R2Wx4Lm5wyiU7HO60y0W3WtzAK5EDnlYe9OgIBeg==", "signatures": [{"sig": "MEQCIEaktyt5btaGAjzp6fYS8L+NZ7IaxWTZg5NcAg8L3lILAiBu3u+kcaOX2VUQTXhH+kF1hYDeRCprLi81Fei3Bem+qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 90292}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/878df3c4f1c430414ef8ce49233cab1b/vitest-runner-2.2.0-beta.2.tgz", "_integrity": "sha512-XV7E7S7VwPfLkB+cvpM+ahwNEikSJSDT9SRKNxZMHd2lo7R2Wx4Lm5wyiU7HO60y0W3WtzAK5EDnlYe9OgIBeg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.2.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_2.2.0-beta.2_1731939492410_0.12872617522170704", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@vitest/runner", "version": "2.1.6", "license": "MIT", "_id": "@vitest/runner@2.1.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "948cad2cccfe2e56be5b3f9979cf9a417ca59737", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.6.tgz", "fileCount": 13, "integrity": "sha512-SjkRGSFyrA82m5nz7To4CkRSEVWn/rwQISHoia/DB8c6IHIhaE/UNAo+7UfeaeJRE979XceGl00LNkIz09RFsA==", "signatures": [{"sig": "MEUCIBAuRqvIMuJeHfWiXnUi5TAQvkpr2s7/QAuspJ1q6IqmAiEA5MYV/eh0gIYJoU+kL9OyQqU9X1SsbYxmxnb/dAnqFMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89264}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7b37ae1f156504c0e7df58ad845e76ab/vitest-runner-2.1.6.tgz", "_integrity": "sha512-SjkRGSFyrA82m5nz7To4CkRSEVWn/rwQISHoia/DB8c6IHIhaE/UNAo+7UfeaeJRE979XceGl00LNkIz09RFsA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.6_1732623840985_0.571067152465059", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "@vitest/runner", "version": "2.1.7", "license": "MIT", "_id": "@vitest/runner@2.1.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "83c56271adccac6779aca46c5f685df4416f38ce", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.7.tgz", "fileCount": 13, "integrity": "sha512-MrDNpXUIXksR57qipYh068SOX4N1hVw6oVILlTlfeTyA1rp0asuljyp15IZwKqhjpWLObFj+tiNrOM4R8UnSqg==", "signatures": [{"sig": "MEUCIFX4zhC5hbh5ESGAXl0tJ2Ge3awhj1wkj7KTi2OFtOcvAiEArqJIP1/49owOKbLCTJsALtKbxcHlugFoV5/fmr3YWEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89264}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5205b72c53d1b5fa7634ac4ec6d96889/vitest-runner-2.1.7.tgz", "_integrity": "sha512-MrDNpXUIXksR57qipYh068SOX4N1hVw6oVILlTlfeTyA1rp0asuljyp15IZwKqhjpWLObFj+tiNrOM4R8UnSqg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.7"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.7_1733132948312_0.6223297920716464", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "@vitest/runner", "version": "2.1.8", "license": "MIT", "_id": "@vitest/runner@2.1.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b0e2dd29ca49c25e9323ea2a45a5125d8729759f", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.8.tgz", "fileCount": 13, "integrity": "sha512-17ub8vQstRnRlIU5k50bG+QOMLHRhYPAna5tw8tYbj+jzjcspnwnwtPtiOlkuKC4+ixDPTuLZiqiWWQ2PSXHVg==", "signatures": [{"sig": "MEUCICa6NY+usNihirZedqGpqSzdwWrDUop4CEzyeyqO7IBnAiEA5o4UWD3h80eShliiEj2qPLHo6A4K8SqXDvas+lvEuhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89264}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/21923737209a243323876884d46c1daa/vitest-runner-2.1.8.tgz", "_integrity": "sha512-17ub8vQstRnRlIU5k50bG+QOMLHRhYPAna5tw8tYbj+jzjcspnwnwtPtiOlkuKC4+ixDPTuLZiqiWWQ2PSXHVg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.8_1733150784363_0.948040286978465", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "@vitest/runner", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vitest/runner@3.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "086666a4aa7d5b22640fbb4ec78db740e474edb3", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-gvKd+ETpA5UfGiWCFXnz9NL0FCcESnm5ou2SKjG1530MXNtaqUXwoxokUpIsAFv0I+GfxzzqpEOZX7eRbtn0DQ==", "signatures": [{"sig": "MEUCIBTEyPD3hdRg4k9BjmPnVh70en3+bxLCfPAk7ufhQzNjAiEAs92ykfWfGiE/1PaXzd9vs8xkCQavozApcmpo/OxBBso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 93005}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/646b96387b0e2b00f88936a7a875f873/vitest-runner-3.0.0-beta.1.tgz", "_integrity": "sha512-gvKd+ETpA5UfGiWCFXnz9NL0FCcESnm5ou2SKjG1530MXNtaqUXwoxokUpIsAFv0I+GfxzzqpEOZX7eRbtn0DQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "3.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.0-beta.1_1733420024801_0.15520137071035744", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "@vitest/runner", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vitest/runner@3.0.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f7dea0ea30f3dc4f220d2703b24a8f6fbcdc887d", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-Ytyub2tBCGrROrGfVlB8SuWdQjFYzJTTR969CGJF/xkIgdkLE9SiQzBZy4td2VidypntLXAVHYjeGr75pvw93w==", "signatures": [{"sig": "MEQCIF2oxk02dacI7wmarltfLbgtEMuQkI/VuGsAZCPbm8nfAiBE5sOayV14Xgwt9I2ZXUp2VE08KzC0rrG2RWqBb5q9FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 93310}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3d975b507b6cb19d6e6ae45858b519aa/vitest-runner-3.0.0-beta.2.tgz", "_integrity": "sha512-Ytyub2tBCGrROrGfVlB8SuWdQjFYzJTTR969CGJF/xkIgdkLE9SiQzBZy4td2VidypntLXAVHYjeGr75pvw93w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "3.0.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.0-beta.2_1733826105858_0.5317091959865308", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.3": {"name": "@vitest/runner", "version": "3.0.0-beta.3", "license": "MIT", "_id": "@vitest/runner@3.0.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e0ad39e9cc5ab319508568f4ee34954beb5811b8", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-9ibO4DPJAyyxQfGDOCpMY36M4XFOX4G4VNmDr25dzrEmJ8sMt8/8E6kdb5lV/m0holHJFgvpGZr8zhXkWQ4+cg==", "signatures": [{"sig": "MEUCIQC+px1v7oh1qtsTQf0ZqzaUeeWWUj0ni/ts502y8rmz0AIgG97mD5HSw41Pjd8ipQcvXP14JAyvinsmypIqXk01YQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 93612}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dbeb1d2ae06f3ea3901a4b5df6a1382b/vitest-runner-3.0.0-beta.3.tgz", "_integrity": "sha512-9ibO4DPJAyyxQfGDOCpMY36M4XFOX4G4VNmDr25dzrEmJ8sMt8/8E6kdb5lV/m0holHJFgvpGZr8zhXkWQ4+cg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "3.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.0-beta.3_1734712371240_0.7822188419077207", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.4": {"name": "@vitest/runner", "version": "3.0.0-beta.4", "license": "MIT", "_id": "@vitest/runner@3.0.0-beta.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "288cdd9dffbf31fbd3767d65f0dbf184895f7ed5", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-+tTASu585TT23AeO+bOBSQ/2nin66nPqOznDCB1HQJ8+Mb3gtOw9faJwQEZ9uPRNhi/mLNau4k9Zig1p/AnntQ==", "signatures": [{"sig": "MEUCIQCAW5hrkc+uXfQ5mGZueoyaWrtwswsapL9KLfn3+reUKAIgcqVC9BOhzz18ly3YnnXAYZ/wvNgnabUESkTSu3Lk5pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 93896}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8740a6fde8f07f0b75727b1a62eec033/vitest-runner-3.0.0-beta.4.tgz", "_integrity": "sha512-+tTASu585TT23AeO+bOBSQ/2nin66nPqOznDCB1HQJ8+Mb3gtOw9faJwQEZ9uPRNhi/mLNau4k9Zig1p/AnntQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.0", "@vitest/utils": "3.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.0-beta.4_1736346247080_0.7058122661228134", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@vitest/runner", "version": "3.0.0", "license": "MIT", "_id": "@vitest/runner@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "383bd01ae79c19ed3054b78b723818d0ad0aecad", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.0.tgz", "fileCount": 13, "integrity": "sha512-6MCYobtatsgG3DlM+dk6njP+R+28iSUqWbJzXp/nuOy6SkAKzJ1wby3fDgimmy50TeK8g6y+E6rP12REyinYPw==", "signatures": [{"sig": "MEUCIE8o4uPSJdVDs0j5dZzaMuaF/f+MP3ia8IAZK1kX4GLOAiEApHYUa9ZbY5HWTbhYBDGRg7/G+lJfmtxptS9S8BwpNZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96081}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/05422f1821b2e55fde524eebc40dd94c/vitest-runner-3.0.0.tgz", "_integrity": "sha512-6MCYobtatsgG3DlM+dk6njP+R+28iSUqWbJzXp/nuOy6SkAKzJ1wby3fDgimmy50TeK8g6y+E6rP12REyinYPw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.0", "@vitest/utils": "3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.0_1737036464178_0.8234889508878369", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@vitest/runner", "version": "3.0.1", "license": "MIT", "_id": "@vitest/runner@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5e4119efdfec1aa0d8a501e069ecce9b4364f73d", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.1.tgz", "fileCount": 13, "integrity": "sha512-LfVbbYOduTVx8PnYFGH98jpgubHBefIppbPQJBSlgjnRRlaX/KR6J46htECUHpf+ElJZ4xxssAfEz/Cb2iIMYA==", "signatures": [{"sig": "MEQCIGWEBhClkCdEjGukPrmXYAdYesuJdBUHkn11aWco4B0YAiBgyrJsyS467NB+gbxJclDekpMBnuiEZ1tWv4ybijABNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96264}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/185532247f30ce9b1f2773d44797b6e8/vitest-runner-3.0.1.tgz", "_integrity": "sha512-LfVbbYOduTVx8PnYFGH98jpgubHBefIppbPQJBSlgjnRRlaX/KR6J46htECUHpf+ElJZ4xxssAfEz/Cb2iIMYA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.1", "@vitest/utils": "3.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.1_1737055972494_0.7757190009582327", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@vitest/runner", "version": "3.0.2", "license": "MIT", "_id": "@vitest/runner@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bea5a177e8ca278c9eaaa68a80dda038a33a0081", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.2.tgz", "fileCount": 13, "integrity": "sha512-GHEsWoncrGxWuW8s405fVoDfSLk6RF2LCXp6XhevbtDjdDme1WV/eNmUueDfpY1IX3MJaCRelVCEXsT9cArfEg==", "signatures": [{"sig": "MEYCIQCwd5El9XxfUhPcIhS3NE+L+OvZT7dHze1Wb0HPiOADowIhALc3BjHttcZ4knV6Qb4T4T2jICvPjfCzX4PuRE+5u+OW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96874}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/47d697efebbba1abfb5073253a64c092/vitest-runner-3.0.2.tgz", "_integrity": "sha512-GHEsWoncrGxWuW8s405fVoDfSLk6RF2LCXp6XhevbtDjdDme1WV/eNmUueDfpY1IX3MJaCRelVCEXsT9cArfEg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.1", "@vitest/utils": "3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.2_1737123982515_0.29258905332189133", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "@vitest/runner", "version": "3.0.3", "license": "MIT", "_id": "@vitest/runner@3.0.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c123e3225ccdd52c5a8e45edb59340ec8dcb6df2", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.3.tgz", "fileCount": 13, "integrity": "sha512-Rgi2kOAk5ZxWZlwPguRJFOBmWs6uvvyAAR9k3MvjRvYrG7xYvKChZcmnnpJCS98311CBDMqsW9MzzRFsj2gX3g==", "signatures": [{"sig": "MEUCIDSKPE7ncOd2074I4HM73GFiIhqbcbapp0BrZEz8KwirAiEA7c8IwN3HF9ekw5h1xMyTwbxHckai9JFZJ+dGu608yyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96874}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5b5a6d872a3ec37bc19ada227918c87f/vitest-runner-3.0.3.tgz", "_integrity": "sha512-Rgi2kOAk5ZxWZlwPguRJFOBmWs6uvvyAAR9k3MvjRvYrG7xYvKChZcmnnpJCS98311CBDMqsW9MzzRFsj2gX3g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.1", "@vitest/utils": "3.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.3_1737467933934_0.8332338784688069", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@vitest/runner", "version": "3.0.4", "license": "MIT", "_id": "@vitest/runner@3.0.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5bdc965c32721c7cf025481124f73589deea313a", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.4.tgz", "fileCount": 13, "integrity": "sha512-dKHzTQ7n9sExAcWH/0sh1elVgwc7OJ2lMOBrAm73J7AH6Pf9T12Zh3lNE1TETZaqrWFXtLlx3NVrLRb5hCK+iw==", "signatures": [{"sig": "MEQCIFCeZhW6UuytE2ESIGfmbXWMdEbkjHhB4DaNCqc4/a8RAiAYNMGdQAYiiikkDmAauuF4RPB1E5HYoD+CKVfgt41gTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96761}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9a4188a813937227b4bdca4587734595/vitest-runner-3.0.4.tgz", "_integrity": "sha512-dKHzTQ7n9sExAcWH/0sh1elVgwc7OJ2lMOBrAm73J7AH6Pf9T12Zh3lNE1TETZaqrWFXtLlx3NVrLRb5hCK+iw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.2", "@vitest/utils": "3.0.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.4_1737639710666_0.2466102739338789", "host": "s3://npm-registry-packages-npm-production"}}, "1.6.1": {"name": "@vitest/runner", "version": "1.6.1", "license": "MIT", "_id": "@vitest/runner@1.6.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "10f5857c3e376218d58c2bfacfea1161e27e117f", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-1.6.1.tgz", "fileCount": 13, "integrity": "sha512-3nSnYXkVkf3mXFfE7vVyPmi3Sazhb/2cfZGGs0JRzFsPFvAMBEcrweV1V1GsrstdXeKCTXlJbvnQwGWgEIHmOA==", "signatures": [{"sig": "MEUCICQPLj5ah5pCzr/6QPXKzJDYHSVPK4HhCY/Sk+ZrZo6wAiEAjRAkgrJLrOEGMDORl94YnXym+MRd/tli+U7/2Cvgg3I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-1.6.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c7bb9c162ed32a0a55745e1b061e287b/vitest-runner-1.6.1.tgz", "_integrity": "sha512-3nSnYXkVkf3mXFfE7vVyPmi3Sazhb/2cfZGGs0JRzFsPFvAMBEcrweV1V1GsrstdXeKCTXlJbvnQwGWgEIHmOA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^1.1.1", "p-limit": "^5.0.0", "@vitest/utils": "1.6.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_1.6.1_1738589776436_0.9142143871420234", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.9": {"name": "@vitest/runner", "version": "2.1.9", "license": "MIT", "_id": "@vitest/runner@2.1.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cc18148d2d797fd1fd5908d1f1851d01459be2f6", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-2.1.9.tgz", "fileCount": 13, "integrity": "sha512-ZXSSqTFIrzduD63btIfEyOmNcBmQvgOVsPNPe0jYtESiXkhd8u2erDLnMxmGrDCwHCCHE7hxwRDCT3pt0esT4g==", "signatures": [{"sig": "MEYCIQCxR8kfHUEfrSmBPXh71Kj7gyPoLynkwA46SbTZftMWigIhAIoUEdWKFZ2RBP5XPsT3O9iIa36sNZ8j3WOHt/btrFww", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89264}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-2.1.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ef2ac8aaedbf34a5b5c084b6fd22c0a2/vitest-runner-2.1.9.tgz", "_integrity": "sha512-ZXSSqTFIrzduD63btIfEyOmNcBmQvgOVsPNPe0jYtESiXkhd8u2erDLnMxmGrDCwHCCHE7hxwRDCT3pt0esT4g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^1.1.2", "@vitest/utils": "2.1.9"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_2.1.9_1738590261548_0.48108613546113155", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@vitest/runner", "version": "3.0.5", "license": "MIT", "_id": "@vitest/runner@3.0.5", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c5960a1169465a2b9ac21f1d24a4cf1fe67c7501", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.5.tgz", "fileCount": 13, "integrity": "sha512-BAiZFityFexZQi2yN4OX3OkJC6scwRo8EhRB0Z5HIGGgd2q+Nq29LgHU/+ovCtd0fOfXj5ZI6pwdlUmC5bpi8A==", "signatures": [{"sig": "MEYCIQDscdzEYIZN6caFOO+gBSK2Xo5Wqd/fwJkfGsZkbsRKxAIhAMEC6EcbhyFt8HqKKtrw/ycX7x+0P/XZ7JDGmJ2gvbQ+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96761}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/27f380b3f85f711a3e19ae4b26d7b1bc/vitest-runner-3.0.5.tgz", "_integrity": "sha512-BAiZFityFexZQi2yN4OX3OkJC6scwRo8EhRB0Z5HIGGgd2q+Nq29LgHU/+ovCtd0fOfXj5ZI6pwdlUmC5bpi8A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^2.0.2", "@vitest/utils": "3.0.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.5_1738591330153_0.24800118331760457", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@vitest/runner", "version": "3.0.6", "license": "MIT", "_id": "@vitest/runner@3.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a07b54674b1a495424f2ea959a28a6096c17c33b", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.6.tgz", "fileCount": 13, "integrity": "sha512-JopP4m/jGoaG1+CBqubV/5VMbi7L+NQCJTu1J1Pf6YaUbk7bZtaq5CX7p+8sY64Sjn1UQ1XJparHfcvTTdu9cA==", "signatures": [{"sig": "MEUCIQDqNPt3TSMZ9tGTXnpEaC3ifz7VkxNMcv97ZjD6NUxndQIgBRLJRqoRm24kknxIMFAwUhtJZ4C8HrHDUUjly4RkcIo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96471}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7396bcd8ce18829bf300e6af5c822c26/vitest-runner-3.0.6.tgz", "_integrity": "sha512-JopP4m/jGoaG1+CBqubV/5VMbi7L+NQCJTu1J1Pf6YaUbk7bZtaq5CX7p+8sY64Sjn1UQ1XJparHfcvTTdu9cA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.0.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.6_1739885930589_0.5443427117317912", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@vitest/runner", "version": "3.0.7", "license": "MIT", "_id": "@vitest/runner@3.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "65b64ba5f3291fdca4670bf9e50627200ea33b7b", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.7.tgz", "fileCount": 13, "integrity": "sha512-We<PERSON>l38Z0S2ZcuRTeyYqaZtm4e26tq6ZFqh5y8YD9YxfWuu0OFiGFUbnxNynwLjNRHPsXyee2M9tV7YxOTPZl2g==", "signatures": [{"sig": "MEYCIQCbqrn4yaZMDD7Ns4emSHA6bv7m/O7DWdhMMh1/HV9EWwIhAKXfwJoHPqaDAV7uIK7lNn6tkPKr9t+OShG3rzVIVRdI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96471}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c62e184f603c721dc83fd0e8b5d2adaa/vitest-runner-3.0.7.tgz", "_integrity": "sha512-We<PERSON>l38Z0S2ZcuRTeyYqaZtm4e26tq6ZFqh5y8YD9YxfWuu0OFiGFUbnxNynwLjNRHPsXyee2M9tV7YxOTPZl2g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.0.7"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.7_1740419457040_0.3278375473541928", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@vitest/runner", "version": "3.0.8", "license": "MIT", "_id": "@vitest/runner@3.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "dda7223c25a89a829a29c3f0c037a99e028a9c64", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.8.tgz", "fileCount": 13, "integrity": "sha512-c7UUw6gEcOzI8fih+uaAXS5DwjlBaCJUo7KJ4VvJcjL95+DSR1kova2hFuRt3w41KZEFcOEiq098KkyrjXeM5w==", "signatures": [{"sig": "MEQCIG3V7QT/dFVjP/B7+vMF1W++lbhOWhGJRX5p0DGTcueDAiAzXe913rXOKZotCLqxmE+V/lsTYQmBFJIeZzIcpXRVXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 97152}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fe01de252e71f1d9fd877c8b4b6da739/vitest-runner-3.0.8.tgz", "_integrity": "sha512-c7UUw6gEcOzI8fih+uaAXS5DwjlBaCJUo7KJ4VvJcjL95+DSR1kova2hFuRt3w41KZEFcOEiq098KkyrjXeM5w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.3", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.0.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.8_1741274184399_0.5569225549638439", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@vitest/runner", "version": "3.0.9", "license": "MIT", "_id": "@vitest/runner@3.0.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "92b7f37f65825105dbfdc07196b90dd8c20547d8", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.0.9.tgz", "fileCount": 13, "integrity": "sha512-NX9oUXgF9HPfJSwl8tUZCMP1oGx2+Sf+ru6d05QjzQz4OwWg0psEzwY6VexP2tTHWdOkhKHUIZH+fS6nA7jfOw==", "signatures": [{"sig": "MEUCID1iDUg0ILvS0NabTc4LxID3R4XF8ncdtELc6na98SgBAiEA96x5J/yGgYPK60WhjeROsUBFYZVlcX/eA/VenQ0hnEw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96344}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.0.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ecd410fb656b5b47ce09112fd57c523b/vitest-runner-3.0.9.tgz", "_integrity": "sha512-NX9oUXgF9HPfJSwl8tUZCMP1oGx2+Sf+ru6d05QjzQz4OwWg0psEzwY6VexP2tTHWdOkhKHUIZH+fS6nA7jfOw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.3", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.0.9"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.0.9_1742212765243_0.786974624955654", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.1": {"name": "@vitest/runner", "version": "3.1.0-beta.1", "license": "MIT", "_id": "@vitest/runner@3.1.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1fdfd3d56c624f8fbdfb9c9659394e1c91d662cf", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-y5tfw0N5JEiuLcZandKG4eWKZFuK84t27aGUM26b+4SsBrE9grcnje+ZD9VpNTiRS0DKhpH2179CSqzjdY2VVg==", "signatures": [{"sig": "MEUCIQDRDHl2Fvo84A+zf9k3DnGE5DOKDdO6hkmBx6sXhMSWMgIgV0c/z46txe+dlPL+DZ3DAxuTjW269B3hn3GZFhhPkAg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96808}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b9480a63a64840062ec9adbcd73f7fe2/vitest-runner-3.1.0-beta.1.tgz", "_integrity": "sha512-y5tfw0N5JEiuLcZandKG4eWKZFuK84t27aGUM26b+4SsBrE9grcnje+ZD9VpNTiRS0DKhpH2179CSqzjdY2VVg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.18.3", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.0-beta.1_1742213859236_0.7774114485583796", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.2": {"name": "@vitest/runner", "version": "3.1.0-beta.2", "license": "MIT", "_id": "@vitest/runner@3.1.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "490334dbc24b6e3893d27bb7195f5b0e395aa266", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-xw+ZG77eh88EcNl1T9hSvcpDc3pFp7dVC43VRRBVJg2ZsJ9oJHu6GC5a6XX9juAXKW40oonWggABlzkBZZqAvw==", "signatures": [{"sig": "MEUCIH31jXD71ZHVxpSG6grUhnTHY/p8ee8r4Mq9Br6Odh1wAiEAqwUbKSwRUM6Uype2PNyfNJNnK9IKDc21KVlRHNdZj6M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 107313}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b3924157443ce5e9ed0bd63a9257f318/vitest-runner-3.1.0-beta.2.tgz", "_integrity": "sha512-xw+ZG77eh88EcNl1T9hSvcpDc3pFp7dVC43VRRBVJg2ZsJ9oJHu6GC5a6XX9juAXKW40oonWggABlzkBZZqAvw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.0", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.0-beta.2_1742545704277_0.849372559175732", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "@vitest/runner", "version": "3.1.0", "license": "MIT", "_id": "@vitest/runner@3.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "144a9a5089fb0d917a96ef821fd8daf142eb4abd", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.0.tgz", "fileCount": 13, "integrity": "sha512-SA+55CDD+Yn3UtAL+T0Wf3CLGytju3aoSkeynGkCgWA4LamzWpT9c2nulxiFDzGopEO2nDekaMXPXYncvk2oxw==", "signatures": [{"sig": "MEUCIEaozLAK2d8B10KwmSeiWaeg5zr7oAt0htEBZvQJyr2kAiEAmuowNfWfvqGKBHjKzqCqEv8gNzSRgk+9PC+3n3Clrok=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 107820}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3n/td5k6fm13gd722lls3bsyvlm0000gn/T/53296fa089293bff9fc00278c822f3ba/vitest-runner-3.1.0.tgz", "_integrity": "sha512-SA+55CDD+Yn3UtAL+T0Wf3CLGytju3aoSkeynGkCgWA4LamzWpT9c2nulxiFDzGopEO2nDekaMXPXYncvk2oxw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.9.0", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "22.12.0", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.0_1743416066670_0.8263305015247024", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@vitest/runner", "version": "3.1.1", "license": "MIT", "_id": "@vitest/runner@3.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "76b598700737089d66c74272b2e1c94ca2891a49", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.1.tgz", "fileCount": 13, "integrity": "sha512-X/d46qzJuEDO8ueyjtKfxffiXraPRfmYasoC4i5+mlLEJ10UvPb0XH5M9C3gWuxd7BAQhpK42cJgJtq53YnWVA==", "signatures": [{"sig": "MEUCIAbSf3YRkqLRi+FEJXwRbe7x8gGylliENuuWVUUmgEunAiEApoGbZdWvjQVMMtGk6C1e9V/kkZgQyIASs8VOKZGx/bk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 107820}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c2a9c6dedf4e83bd7d9511d76b058330/vitest-runner-3.1.1.tgz", "_integrity": "sha512-X/d46qzJuEDO8ueyjtKfxffiXraPRfmYasoC4i5+mlLEJ10UvPb0XH5M9C3gWuxd7BAQhpK42cJgJtq53YnWVA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.0", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.1_1743416346970_0.2911951010327998", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@vitest/runner", "version": "3.1.2", "license": "MIT", "_id": "@vitest/runner@3.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ffeba74618046221e944e94f09b565af772170cf", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.2.tgz", "fileCount": 13, "integrity": "sha512-bhLib9l4xb4sUMPXnThbnhX2Yi8OutBMA8Yahxa7yavQsFDtwY/jrUZwpKp2XH9DhRFJIeytlyGpXCqZ65nR+g==", "signatures": [{"sig": "MEUCIHq/LD2BjPb4D2rKgjBLfZPiRFpdcaAMFGHnYcOvTfZjAiEA+ZLy6+MkNKFtCbh1rpaLKH3NRM6qFIItiZUYEXuL54c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 108398}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8d24177075119b855edb1eab5f5f9284/vitest-runner-3.1.2.tgz", "_integrity": "sha512-bhLib9l4xb4sUMPXnThbnhX2Yi8OutBMA8Yahxa7yavQsFDtwY/jrUZwpKp2XH9DhRFJIeytlyGpXCqZ65nR+g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.0", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.2_1745225921238_0.8113519766709005", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@vitest/runner", "version": "3.1.3", "license": "MIT", "_id": "@vitest/runner@3.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b268fa90fca38fab363f1107f057c0a2a141ee45", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.3.tgz", "fileCount": 13, "integrity": "sha512-<PERSON>e+ogtlNfFei5DggOsSUvkIaSuVywujMj6HzR97AHK6XK8i3BuVyIifWAm/sE3a15lF5RH9yQIrbXYuo0IFyA==", "signatures": [{"sig": "MEUCIQCqJdpxBVjYq7b13ueoN1Pa/iM5GR3y/GxUuGm/N/urcgIgJjdCIOo4bHEhAktOntDNyPi+XZ6eWuZIc2CsT7w4oB0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 108417}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4a3f6ceb6570b37f96edb9d1b05cb9d9/vitest-runner-3.1.3.tgz", "_integrity": "sha512-<PERSON>e+ogtlNfFei5DggOsSUvkIaSuVywujMj6HzR97AHK6XK8i3BuVyIifWAm/sE3a15lF5RH9yQIrbXYuo0IFyA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.3_1746452710873_0.9196314907386549", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.1": {"name": "@vitest/runner", "version": "3.2.0-beta.1", "license": "MIT", "_id": "@vitest/runner@3.2.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c5d51b7b794009ce4d9eae54959421fd076e3e84", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-sMwEa6SQc9DOGBbwkjUpcKOXEgeoxkuujPIh9sxqWBV0FeRey4kHxSkULmqzQR1/YCWcVEX+KCTISd5uYdrmcw==", "signatures": [{"sig": "MEYCIQDufyb7wLGgOVmYsMcYN1CLPbzvdkx1N1V9R8sErNqkwwIhAM5Wm0VdK8bdXSahFzwytIzd0Oz4WRdHZXN1VNKQfhkY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 110594}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ed7406fa7bb9895c06dd0d64754bc5cc/vitest-runner-3.2.0-beta.1.tgz", "_integrity": "sha512-sMwEa6SQc9DOGBbwkjUpcKOXEgeoxkuujPIh9sxqWBV0FeRey4kHxSkULmqzQR1/YCWcVEX+KCTISd5uYdrmcw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.0-beta.1_1746463884360_0.5885746557613987", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.2": {"name": "@vitest/runner", "version": "3.2.0-beta.2", "license": "MIT", "_id": "@vitest/runner@3.2.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0a2b28b44850bacb4797bfb955b84303bb3f4655", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-oAg359jc3N+ldM26SarVCLuxoR1Hafg+ZUkEE14Dz/w3YmROmIvpMvF8ZhQfEbAkshlT3onG8b80scjiXNPuiQ==", "signatures": [{"sig": "MEYCIQDtlaeZC47+KJdJouaTR6K7fR0/3nGCNo5Azum7VPbD8gIhAO1gg4eBhKzZ84kyOUZvUPHWU86kbQ1RitRsFKLQclpD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 110594}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c543c295704676c2717984ed94acbfdb/vitest-runner-3.2.0-beta.2.tgz", "_integrity": "sha512-oAg359jc3N+ldM26SarVCLuxoR1Hafg+ZUkEE14Dz/w3YmROmIvpMvF8ZhQfEbAkshlT3onG8b80scjiXNPuiQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.0-beta.2_1747658331618_0.28688054587825107", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@vitest/runner", "version": "3.1.4", "license": "MIT", "_id": "@vitest/runner@3.1.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "19fa16eb397f5325b99baca48c2bca6cadd098fa", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.4.tgz", "fileCount": 13, "integrity": "sha512-djTeF1/vt985I/wpKVFBMWUlk/I7mb5hmD5oP8K9ACRmVXgKTae3TUOtXAEBfslNKPzUQvnKhNd34nnRSYgLNQ==", "signatures": [{"sig": "MEUCIQDmWvp2f8Q0dB/O6i7P1hDiYMA6pqGndDrVCahs+RGnnAIgM9J+VNGGeEku6+tmpoW85Kivk9eHak0x3Zu2qDgXrao=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 108417}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/87adab098062caab5f62d4da04af8bf4/vitest-runner-3.1.4.tgz", "_integrity": "sha512-djTeF1/vt985I/wpKVFBMWUlk/I7mb5hmD5oP8K9ACRmVXgKTae3TUOtXAEBfslNKPzUQvnKhNd34nnRSYgLNQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.1.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.1.4_1747671842110_0.7616678232626595", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.3": {"name": "@vitest/runner", "version": "3.2.0-beta.3", "license": "MIT", "_id": "@vitest/runner@3.2.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "322a48478259a2ee5bc2443607d3007f2a44ae0b", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-tc9d35sKwWltMO7hsoLRTyzvbmZo5WKM+dxIriOqJiDulZOKsQA3DZjnFJITxWvqkoHc+q+Hc6x0a2HWn7JdrA==", "signatures": [{"sig": "MEYCIQCSRzk85vpw6ShGH5GCkeH2DMNNTceoekQkGceOHmQ+mAIhAJXZ9Uv8m16JnV5f1LjLWO4miJ/HXL43KGTBbFCfGxzO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 115798}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5d2a04253956b223fb12e25f3bcf6346/vitest-runner-3.2.0-beta.3.tgz", "_integrity": "sha512-tc9d35sKwWltMO7hsoLRTyzvbmZo5WKM+dxIriOqJiDulZOKsQA3DZjnFJITxWvqkoHc+q+Hc6x0a2HWn7JdrA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.0-beta.3_1748442523152_0.7197230521952576", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@vitest/runner", "version": "3.2.0", "license": "MIT", "_id": "@vitest/runner@3.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3dc645bc40ffb9f586267776337e3247402e4831", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.0.tgz", "fileCount": 13, "integrity": "sha512-bXdmnHxuB7fXJdh+8vvnlwi/m1zvu+I06i1dICVcDQFhyV4iKw2RExC/acavtDn93m/dRuawUObKsrNE1gJacA==", "signatures": [{"sig": "MEQCIEZaAXQxKTPpK2l944wznYN3V8Kt7musBW/IufU+cPffAiA6fZ0k2N4rhu7wq6jMatwewy9OA3QZfXtLBrHR9y0EVg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 126183}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f4d7e021c97d987b23c0a1eeaf7c90a8/vitest-runner-3.2.0.tgz", "_integrity": "sha512-bXdmnHxuB7fXJdh+8vvnlwi/m1zvu+I06i1dICVcDQFhyV4iKw2RExC/acavtDn93m/dRuawUObKsrNE1gJacA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.0_1748862653055_0.9959855382335148", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.1": {"name": "@vitest/runner", "version": "3.2.1", "license": "MIT", "_id": "@vitest/runner@3.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8bc3a16a26b6f21610e33d8ac509683c336253b2", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.1.tgz", "fileCount": 13, "integrity": "sha512-kygXhNTu/wkMYbwYpS3z/9tBe0O8qpdBuC3dD/AW9sWa0LE/DAZEjnHtWA9sIad7lpD4nFW1yQ+zN7mEKNH3yA==", "signatures": [{"sig": "MEUCIQDBud0yt0GQYNULfFST/Nym37FM8eRVKNhkDo+11gaAbQIgKxkUu/6G03uZGbAVKpW99nT+KGhpnZfUQbaIik1zNsY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 126183}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/53af58c33fb241ab374993d04144c4b9/vitest-runner-3.2.1.tgz", "_integrity": "sha512-kygXhNTu/wkMYbwYpS3z/9tBe0O8qpdBuC3dD/AW9sWa0LE/DAZEjnHtWA9sIad7lpD4nFW1yQ+zN7mEKNH3yA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.1_1748970449391_0.9970395487100381", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.2": {"name": "@vitest/runner", "version": "3.2.2", "license": "MIT", "_id": "@vitest/runner@3.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4428d59ebeeca5dd1e68743d9430f4b28b89c29b", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.2.tgz", "fileCount": 13, "integrity": "sha512-GYcHcaS3ejGRZYed2GAkvsjBeXIEerDKdX3orQrBJqLRiea4NSS9qvn9Nxmuy1IwIB+EjFOaxXnX79l8HFaBwg==", "signatures": [{"sig": "MEQCIETOfSOM+Mw97EebhLhy5fdkTr2uGQjhiTyltieSgtcpAiB4XQwlCbnLytrBKtKO6EQtOOJqaAhiRiIhgS06OfOUmg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 126146}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d3ae0ce57853893aeceaa6c2cfad6431/vitest-runner-3.2.2.tgz", "_integrity": "sha512-GYcHcaS3ejGRZYed2GAkvsjBeXIEerDKdX3orQrBJqLRiea4NSS9qvn9Nxmuy1IwIB+EjFOaxXnX79l8HFaBwg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.2_1749130942539_0.12230285318549239", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.3": {"name": "@vitest/runner", "version": "3.2.3", "license": "MIT", "_id": "@vitest/runner@3.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e45318d833c8bf8b9f292a700fc06a011f70d542", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.3.tgz", "fileCount": 13, "integrity": "sha512-83HWYisT3IpMaU9LN+VN+/nLHVBCSIUKJzGxC5RWUOsK1h3USg7ojL+UXQR3b4o4UBIWCYdD2fxuzM7PQQ1u8w==", "signatures": [{"sig": "MEUCIQCJWUDw4HBwxDfXU1xIjrOZTASfWFLcJF6OY+If1G0xlwIgCj/7qOA4MC5JUu8xqnQLq/7VXKWODjZjcPyCFxEvgPo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 126661}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4600049d6383e3539ae51a4b0e3774a8/vitest-runner-3.2.3.tgz", "_integrity": "sha512-83HWYisT3IpMaU9LN+VN+/nLHVBCSIUKJzGxC5RWUOsK1h3USg7ojL+UXQR3b4o4UBIWCYdD2fxuzM7PQQ1u8w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.3", "strip-literal": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.3_1749468755518_0.9286446192519546", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.4": {"name": "@vitest/runner", "version": "3.2.4", "license": "MIT", "_id": "@vitest/runner@3.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5ce0274f24a971f6500f6fc166d53d8382430766", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.4.tgz", "fileCount": 13, "integrity": "sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==", "signatures": [{"sig": "MEUCIQCnuBlbFsKV37vGMaI8ppMeHQ4RkS/Q4bqscsMEruhC1wIgSJHjFceTuT4gKdgab3zM8X074o6a1lzfvCj5d1vBanA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@3.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 126867}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-3.2.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/8f03a614c426dd7b1df969f407d504ae/vitest-runner-3.2.4.tgz", "_integrity": "sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.2", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "3.2.4", "strip-literal": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/runner_3.2.4_1750182848375_0.9778653347121489", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.1": {"name": "@vitest/runner", "version": "4.0.0-beta.1", "license": "MIT", "_id": "@vitest/runner@4.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "daf17eb24bbbe1f22d5e928485a117ce9bfe9797", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-4.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-zqu23Nok8yLEUdvbtwwD5pAleWOqGlcgy1G3c+r9IIxA1LLeegG8M9FVEQlk3heEl8TZWSq7N811vOHYbynvqg==", "signatures": [{"sig": "MEUCIQDmBQqOiNXctUEQWc+XyB8BY1MlUUTNCkQ8bTK0TOToBgIgfMtB5RdvbhIhh0MrpWROCQ33tcjsEdfzpj0+AAxZlqE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 126873}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-runner-4.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/7d9d72e7e9949f76740429bbb2f1846a/vitest-runner-4.0.0-beta.1.tgz", "_integrity": "sha512-zqu23Nok8yLEUdvbtwwD5pAleWOqGlcgy1G3c+r9IIxA1LLeegG8M9FVEQlk3heEl8TZWSq7N811vOHYbynvqg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "_npmVersion": "10.8.2", "description": "V<PERSON>t test runner", "directories": {}, "sideEffects": true, "_nodeVersion": "20.19.2", "dependencies": {"pathe": "^2.0.3", "@vitest/utils": "4.0.0-beta.1", "strip-literal": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/runner_4.0.0-beta.1_1750433323371_0.779274835818579", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.2": {"name": "@vitest/runner", "type": "module", "version": "4.0.0-beta.2", "description": "V<PERSON>t test runner", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/runner"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": true, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}, "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"pathe": "^2.0.3", "strip-literal": "^3.0.0", "@vitest/utils": "4.0.0-beta.2"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}, "_id": "@vitest/runner@4.0.0-beta.2", "readmeFilename": "README.md", "_integrity": "sha512-5+kSs/a+BFwP3zoY7S+4KPO3FwTzI1ZfWaCrESieb13p6+a+B4jEBkMx4yyOS6fPVQchq/YrRHtbPVVTO1lemg==", "_resolved": "/tmp/d4a00cd6269d5699b281d0a07a7372ab/vitest-runner-4.0.0-beta.2.tgz", "_from": "file:vitest-runner-4.0.0-beta.2.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-5+kSs/a+BFwP3zoY7S+4KPO3FwTzI1ZfWaCrESieb13p6+a+B4jEBkMx4yyOS6fPVQchq/YrRHtbPVVTO1lemg==", "shasum": "aae9bf7ded9fcd4de2e737ab710ef9563b1c4bd3", "tarball": "https://registry.npmjs.org/@vitest/runner/-/runner-4.0.0-beta.2.tgz", "fileCount": 13, "unpackedSize": 126108, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2frunner@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDYH5xsGwjPWHhGZsju6i2KVuf5snUVxQR1BPEI1aDYDQIgAsIyQXg3pf6EYp78VMbnn4pRNw2fW61O/Zsr8Zs8J5g="}]}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>", "actor": {"name": "vitestbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/runner_4.0.0-beta.2_1750775101925_0.24685801766672455"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-01-23T09:28:14.396Z", "modified": "2025-06-24T14:25:02.648Z", "0.28.0": "2023-01-23T09:28:14.633Z", "0.28.1": "2023-01-23T09:51:19.135Z", "0.28.2": "2023-01-25T11:21:07.794Z", "0.28.3": "2023-01-27T12:10:03.728Z", "0.28.4": "2023-02-03T10:05:04.762Z", "0.28.5": "2023-02-13T12:23:53.040Z", "0.29.0": "2023-02-25T08:25:59.385Z", "0.29.1": "2023-02-25T09:25:13.047Z", "0.29.2": "2023-02-28T15:14:41.498Z", "0.29.3": "2023-03-15T20:04:10.010Z", "0.29.4": "2023-03-20T13:40:36.652Z", "0.29.5": "2023-03-20T14:12:21.193Z", "0.29.6": "2023-03-20T20:23:50.856Z", "0.29.7": "2023-03-20T20:38:00.632Z", "0.29.8": "2023-03-28T13:12:15.903Z", "0.30.0": "2023-04-09T13:39:13.898Z", "0.30.1": "2023-04-11T11:26:53.317Z", "0.31.0": "2023-05-03T18:08:39.666Z", "0.31.1": "2023-05-17T14:23:42.110Z", "0.31.2": "2023-05-30T13:05:21.664Z", "0.31.3": "2023-05-31T14:49:39.706Z", "0.31.4": "2023-06-01T09:55:24.221Z", "0.32.0": "2023-06-06T17:04:26.198Z", "0.32.1": "2023-06-16T12:23:03.697Z", "0.32.2": "2023-06-16T16:05:57.649Z", "0.32.3": "2023-07-03T08:35:39.449Z", "0.32.4": "2023-07-03T11:05:52.055Z", "0.33.0": "2023-07-06T14:11:24.322Z", "0.34.0": "2023-08-01T15:41:49.659Z", "0.34.1": "2023-08-01T16:53:47.694Z", "0.34.2": "2023-08-17T10:09:54.541Z", "0.34.3": "2023-08-25T07:30:11.994Z", "0.34.4": "2023-09-08T10:34:20.901Z", "0.34.5": "2023-09-21T13:50:36.030Z", "0.34.6": "2023-09-29T07:33:44.496Z", "1.0.0-beta.0": "2023-10-02T16:40:26.024Z", "0.34.7": "2023-10-02T17:03:30.302Z", "1.0.0-beta.1": "2023-10-03T11:31:32.235Z", "1.0.0-beta.2": "2023-10-13T07:34:37.515Z", "1.0.0-beta.3": "2023-10-27T12:45:42.235Z", "1.0.0-beta.4": "2023-11-09T10:13:37.174Z", "1.0.0-beta.5": "2023-11-18T09:44:32.283Z", "1.0.0-beta.6": "2023-11-28T17:27:16.153Z", "1.0.0": "2023-12-04T15:46:22.169Z", "1.0.1": "2023-12-04T18:04:50.380Z", "1.0.2": "2023-12-07T10:13:01.491Z", "1.0.3": "2023-12-09T13:05:48.532Z", "1.0.4": "2023-12-09T19:05:19.584Z", "1.1.0": "2023-12-19T14:06:31.797Z", "1.1.1": "2023-12-31T13:37:59.976Z", "1.1.2": "2024-01-04T16:58:44.049Z", "1.1.3": "2024-01-05T08:21:04.780Z", "1.2.0": "2024-01-12T16:07:22.267Z", "1.2.1": "2024-01-17T16:24:03.096Z", "1.2.2": "2024-01-26T16:25:52.888Z", "1.3.0": "2024-02-16T17:29:09.015Z", "1.3.1": "2024-02-20T13:48:30.419Z", "1.4.0": "2024-03-15T10:30:52.589Z", "1.5.0": "2024-04-11T17:48:03.709Z", "1.5.1": "2024-04-24T11:22:29.491Z", "1.5.2": "2024-04-25T09:12:06.527Z", "1.5.3": "2024-04-30T08:40:30.709Z", "1.6.0": "2024-05-03T15:22:19.686Z", "2.0.0-beta.1": "2024-05-09T14:32:31.489Z", "2.0.0-beta.2": "2024-05-09T15:31:29.480Z", "2.0.0-beta.3": "2024-05-14T18:44:46.071Z", "2.0.0-beta.4": "2024-06-02T12:15:59.740Z", "2.0.0-beta.5": "2024-06-02T12:27:52.947Z", "2.0.0-beta.6": "2024-06-02T19:17:30.154Z", "2.0.0-beta.7": "2024-06-03T11:35:51.851Z", "2.0.0-beta.8": "2024-06-04T12:39:30.010Z", "2.0.0-beta.9": "2024-06-05T08:01:07.028Z", "2.0.0-beta.10": "2024-06-12T12:11:41.673Z", "2.0.0-beta.11": "2024-06-19T20:14:04.778Z", "2.0.0-beta.12": "2024-06-25T20:16:21.621Z", "2.0.0-beta.13": "2024-07-04T14:03:47.250Z", "2.0.0": "2024-07-08T11:39:27.693Z", "2.0.1": "2024-07-08T15:33:04.848Z", "2.0.2": "2024-07-10T15:46:37.559Z", "2.0.3": "2024-07-15T10:03:35.046Z", "2.0.4": "2024-07-22T09:13:28.937Z", "2.0.5": "2024-07-31T10:40:01.127Z", "2.1.0-beta.1": "2024-08-07T06:21:28.789Z", "2.1.0-beta.2": "2024-08-07T07:56:52.915Z", "2.1.0-beta.3": "2024-08-07T08:17:05.312Z", "2.1.0-beta.4": "2024-08-07T11:42:54.294Z", "2.1.0-beta.5": "2024-08-12T11:35:22.149Z", "2.1.0-beta.6": "2024-08-20T13:18:34.689Z", "2.1.0-beta.7": "2024-09-09T15:13:21.051Z", "2.1.0": "2024-09-12T14:03:27.873Z", "2.1.1": "2024-09-13T15:32:41.691Z", "2.1.2": "2024-10-02T16:20:09.186Z", "2.1.3": "2024-10-14T11:05:34.277Z", "2.1.4": "2024-10-28T12:27:22.662Z", "2.1.5": "2024-11-13T15:24:10.685Z", "2.2.0-beta.1": "2024-11-13T17:17:22.354Z", "2.2.0-beta.2": "2024-11-18T14:18:12.606Z", "2.1.6": "2024-11-26T12:24:01.177Z", "2.1.7": "2024-12-02T09:49:08.482Z", "2.1.8": "2024-12-02T14:46:24.521Z", "3.0.0-beta.1": "2024-12-05T17:33:44.961Z", "3.0.0-beta.2": "2024-12-10T10:21:46.063Z", "3.0.0-beta.3": "2024-12-20T16:32:51.414Z", "3.0.0-beta.4": "2025-01-08T14:24:07.349Z", "3.0.0": "2025-01-16T14:07:44.371Z", "3.0.1": "2025-01-16T19:32:52.787Z", "3.0.2": "2025-01-17T14:26:22.749Z", "3.0.3": "2025-01-21T13:58:54.134Z", "3.0.4": "2025-01-23T13:41:50.873Z", "1.6.1": "2025-02-03T13:36:16.630Z", "2.1.9": "2025-02-03T13:44:21.767Z", "3.0.5": "2025-02-03T14:02:10.348Z", "3.0.6": "2025-02-18T13:38:50.778Z", "3.0.7": "2025-02-24T17:50:57.216Z", "3.0.8": "2025-03-06T15:16:24.609Z", "3.0.9": "2025-03-17T11:59:25.415Z", "3.1.0-beta.1": "2025-03-17T12:17:39.384Z", "3.1.0-beta.2": "2025-03-21T08:28:24.456Z", "3.1.0": "2025-03-31T10:14:26.845Z", "3.1.1": "2025-03-31T10:19:07.180Z", "3.1.2": "2025-04-21T08:58:41.405Z", "3.1.3": "2025-05-05T13:45:11.063Z", "3.2.0-beta.1": "2025-05-05T16:51:24.512Z", "3.2.0-beta.2": "2025-05-19T12:38:51.784Z", "3.1.4": "2025-05-19T16:24:02.325Z", "3.2.0-beta.3": "2025-05-28T14:28:43.350Z", "3.2.0": "2025-06-02T11:10:53.237Z", "3.2.1": "2025-06-03T17:07:29.651Z", "3.2.2": "2025-06-05T13:42:22.707Z", "3.2.3": "2025-06-09T11:32:35.708Z", "3.2.4": "2025-06-17T17:54:08.563Z", "4.0.0-beta.1": "2025-06-20T15:28:43.550Z", "4.0.0-beta.2": "2025-06-24T14:25:02.160Z"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "license": "MIT", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/runner"}, "description": "V<PERSON>t test runner", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}