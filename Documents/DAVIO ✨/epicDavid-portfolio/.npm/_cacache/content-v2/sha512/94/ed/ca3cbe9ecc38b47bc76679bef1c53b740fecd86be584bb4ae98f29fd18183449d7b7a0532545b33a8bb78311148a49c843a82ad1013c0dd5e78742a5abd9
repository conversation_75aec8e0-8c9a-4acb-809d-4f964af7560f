{"_id": "make-dir", "_rev": "26-809ebe576362596a9585605c31fd8fa7", "name": "make-dir", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "make-dir", "version": "1.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^2.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^2.13.0", "graceful-fs": "^4.1.11", "nyc": "^10.2.0", "path-type": "^2.0.0", "tempy": "^0.1.0", "xo": "*"}, "gitHead": "5fea1cde511edf07f0ac8101b376269de3f4c98a", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.0.0", "_shasum": "97a011751e91dd87cfadef58832ebb04936de978", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "97a011751e91dd87cfadef58832ebb04936de978", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-1.0.0.tgz", "integrity": "sha512-d4rXDWLFad/RwLfoORm8T/Vtm8A9h7S8tobJ4WRo9DEJg5KjWNVFbVpzLgpX9OfzD8JiXIa7fq/p9z6HT1J9uA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTVQr/K3SffItkLX/7EhurQHCFdUWQL5kscf2sjFYuvQIgc2f735np57QStS6CMGmM0s0nCZF/+vC+PI3hnmCXsB4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/make-dir-1.0.0.tgz_1494354871618_0.5163482388015836"}, "directories": {}}, "1.1.0": {"name": "make-dir", "version": "1.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^2.1.0", "graceful-fs": "^4.1.11", "nyc": "^10.2.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "*"}, "gitHead": "de4a32fd806f4da0b880a8f2b3962d0ac34d7244", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.1.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0Pkui4wLJ7rxvmfUvs87skoEaxmu0hCUApF8nonzpl7q//FWp9zu8W61Scz4sd/kUiqDxvUhtoam2efDyiBzcA==", "shasum": "19b4369fe48c116f53c2af95ad102c0e39e85d51", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-1.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGjGvWdz+K5N0ad1l40kosqUGOMHqDAnhHnRCnABuyeDAiEA7gbriXOvPZEtUoj5tvHQXlO/PpEbuIYhPLKnVj/tNEU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir-1.1.0.tgz_1508653268298_0.4698842391371727"}, "directories": {}}, "1.2.0": {"name": "make-dir", "version": "1.2.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^11.3.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "*"}, "gitHead": "7f854b4728c2ca45fdc015cc7099dae23bcfe738", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aNUAa4UMg/UougV25bbrU4ZaaKNjJ/3/xnvg/twpmKROPdKZPZ9wGgI0opdZzO8q/zUFawoUuixuOv33eZ61Iw==", "shasum": "6d6a49eead4aae296c53bbf3a1a008bd6c89469b", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-1.2.0.tgz", "fileCount": 4, "unpackedSize": 6484, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkKGikctOp9zV85rd4CyhjdiwULavLHWMsRppzqmiwJwIgNUX/ZXQqq7CLM89JLBzocul8Vqd7JMroXkOzdHLrYLg="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_1.2.0_1519025204776_0.3757507708266983"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "make-dir", "version": "1.3.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^11.3.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "^0.20.0"}, "gitHead": "e962394d01198a961d42272f1e849ab601aaef1e", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==", "shasum": "79c1033b80515bd6d24ec9933e860ca75ee27f0c", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz", "fileCount": 4, "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9E64CRA9TVsSAnZWagAAtRIP/2xNUbh+k8l3ZZTPipTQ\ny55O3BfyWR9scXFf1Mnw2RbGmDYiahyV/q0L3xcoEEeZZdyBD5DUTM2HjTwy\nLsFIP7/d4Z5XkYBDpFG2Anfy8tc/flW1+WVW+bo0IrdVWHCAc1SE01XWwcEn\nmmCBI8vwFSwC8VxhPhSTjZIfKtwK7P+muo2Kn/ovPOH1fF6iptClFIi/Danp\nKw5/sdxbKL2fXvDY9H9/I1gQ54wUpfA/f6mgZhdW+e6z6epf4nznRT/6ATSD\n45ycc4OXBJ4SHXO1BWUcX73a9mXE0sDtDhX8Jsd1StEUY+km31W4RQBdwMKi\nJBnrvW/0Rb39LvzJAhAc7s5KHWhBVUTlmO9S+4cxFVAT7f/nGkHtzlYx11cZ\nk1tlIZgblHa5P33AzBZeW+s+E3VIL+6K/7H7PPipyPX94iwUKUskZpnBiE3j\nufZAB5g7GKDoSYHte0MJF0NMVVLPcnMBBEQULGGxGWRRXorMHVQZuAPl2vsK\nZ/4wzI6uzkA+gRbawKHf8H0F6nb4LTMmVdkNdlMUOBDSZk5bVDKPYfKGEnAg\nXp8c504hr48qkRMhlcon7Zc4Wqco+9ThCj2J64qTgY1jgKiBztjZ2uquUflN\nt8jjzpnzKJzbvp5Y93fKGePBvVO6oEmFTT5Yr08fWl3zYPE+vsqZwP7Y5Gdk\n8NRz\r\n=i0G+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKe2Bd2hSogZLqYVSdCH2mLWjgVMk13HJreBfYhHrUvAiEA5K1dIp20UEyIzSVEXdV3U/pjj3wsG3Hl/3MLwqscIqA="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_1.3.0_1525960375355_0.21501361126034468"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "make-dir", "version": "2.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "devDependencies": {"ava": "^1.2.0", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^13.1.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "^0.24.0"}, "gitHead": "c1294d718f3d82fd238e91ccbda08c9b6ad474e0", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@2.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DCZvJtCxpfY3a0Onp57Jm0PY9ggZENfVtBMsPdXFZDrMSHU5kYCMJkJesLr0/UrFdJKuDUYoGxCpc93n4F3Z8g==", "shasum": "648a648c64fe460621461997f7ae2b40d1f65c7e", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-2.0.0.tgz", "fileCount": 4, "unpackedSize": 7923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcV8+ZCRA9TVsSAnZWagAAHvIP/2tq5HDHtCtJJUaDoOAS\n4q3e8QPhH68116ssGCzGbM7HSxMrFpN5alQ2eluqDIZxhc+dr52SQXEloIJp\nHZG8zRlgsiBXOH/P+8CGGuABDNfYFOGyofX2VB5jjos1xsR5/UxIBiBZvCwR\n6RYUO1nDmLdusLphiWdbWS51fRQf4I8VPflkjp4fkWGzZ3Wk/4T+roxcZXcM\nyrgxLu54edFAwnre2Pyy0GbXXX3z9Uj2s7fjofUJU9LdEkS7/CD7KHCBOTRq\nDy9ouxXemgrL4ZI6+D/f+efNg4X39NoddK+pgvD2Smg5wSGM0SKtm4/5SaR9\nZvpW8nrt3Xod5F92FC2ixvrcgkizlDOGAIVhv9CLbmKCn2wF9AOLON3LACSx\n72ZWlepO0Xd1kISC0VEjxCuzbdbmfeiNMDr+X8qdni+IMP8GpStPUemVmbRv\ngSB3s3szW5o2iEwpiTqLoquGPYwdS6vVa6qFkIZ0GSEzu0t8icUoPSSLohfW\nSHIO7hBdBUrM/bhGoKS5FYMiZl24oLa8IX3nb+CLylV6InQNm3vIISFaIgSX\nDjED+fZs8d67fpUOF8MQjpe9Y3xLatIBYgcGZr3sW5wnNsitxAexAli21s9e\nelc8KOtLt1Xzvh0TNqbFzeY5DTGytQIXfwTlN5SowZRAr6SBEEiNG+sB9IxF\ne2b2\r\n=RkXt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSz0ScQkng8PYHKpl9QLfS/a3TXZKhmNxfSdsWGxHP3QIhAP1142MuXe14KhsL240JPe6GFCoeGAWr0/fd22+vHvGZ"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_2.0.0_1549258649050_0.2079142967111085"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "make-dir", "version": "2.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava && tsd-check"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^11.10.4", "ava": "^1.2.0", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^13.1.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "1c36213da8b2e13306ab4704ea861742bbbe7a40", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@2.1.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==", "shasum": "5f0310e18b8be898cc07009295a30ae41e91e6f5", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz", "fileCount": 5, "unpackedSize": 9198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfC2lCRA9TVsSAnZWagAAwpEP/2KxMsEidauM1H7Irlli\nCnz82p0XueCmHs1eAXgwPhG20Kf6LzmAtKQtDNZdrfcjPcWhL+4NYmPGlzeE\nPibVyLlR0zjqVfJcT/mEJhJyxiQ9DK+3cdH/qgkBjXxR6Hk5d0Q2EgzUOIIg\nLogLCVbd06bMJYljhW5P3dIZxDHhNVnHud5retu5lkB6QwgQnB/GGsAKFqRO\nhH/haetn9YOQYz8NFXA8CbI+LrWcLWMmYnrjSC1QJE8DuxR8QoFBbpur7MSh\nyEFOqY6QNlyDV+vpLQG9QOn8kvE07ZCOCljDPS2yE2/TKUhbz/GHy3dzOt08\n6hdKbhRhvP0DuKaJX3a9xhHGyPq5/Gqrt+fpJKJiPwjuBOizcN8EF+PksJdD\nqWUnW9CxI0s2RDWDSE/+cPfkYGRponT41/5piLLldFIjfXsT2JGKNNWfRKOC\niKCEKVWLB4odXZpby1oVcXBOrLOoHkQAWlDf1058nPHkYG35bwzcTeI0Pgsz\nkKQGK1SP0DIyFemGpgb9iP9AyTmo4qs2DOE3K7Oh4p+EeydZUM4i0B63B3fp\nhpM87irlqpHC+EcYW6ry94MkFVRVunXLJIRW85qphMc7bI9Q/FQnchnsqa3W\nju6omAFllTZX3M4hV72wczEt3DzZmd255TMYcEOzTjVnTEePwcbXng6bHNa1\nBexC\r\n=CTEd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHOcIkZytoSr3cE7ObXA+zzVziLB+ba70AN9O/cW7OONAiAe062q1kxAVrm1B398N3V+mGyVouHyJ4eKZTLU3tv4Cg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_2.1.0_1551642020600_0.43649614195121145"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "make-dir", "version": "3.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^11.12.2", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^13.3.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "b13a97d67d92b0948803bdbb9b6bfb84c21079d9", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-grNJDhb8b1Jm1qeqW5R/O63wUo4UXo2v2HMic6YT9i/HBlF93S8jkMgH7yugvY9ABDShH4VZMn8I+U8+fCNegw==", "shasum": "1b5f39f6b9270ed33f9f054c5c0f84304989f801", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-3.0.0.tgz", "fileCount": 5, "unpackedSize": 9481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcodmCCRA9TVsSAnZWagAAKQAP/10hUugymOap/gUgZWJy\n6U4M07o7Vdm8gw2BZ6ulftR9p7r9DuV7ohDJUYHhQCc8F0lMWzqq8uFVsotK\njuzk1G1v6qJPwqEpinjolVtLgNyXLHKoSUKYsIIgeqt5nfCndG7uNbwlAVd9\nSxh5dBK1cycKEcHKNGqKqTRc8qfsGN1V3LxOBdZ6++dgktWMkvczaT/7fGw2\nZRNoIBt0cnGG6ws2s7Je9FEYjt3ubcQ5KmEwmKOtzv7opF0yvz6ZCStVHgVm\n0QuvgF0zp6wJo9Nk76jz/HzjLgcl8IBtBRcrvXr5xjWoCQmX4hRBemYtTIMQ\n0ABtuwcvJvrOCTeJH7UoRB3gu7niNX//wA4xMFbMTZbVmBHq3BodsghF1HVC\neHZNI9K9SSmwWtxr/yFwx98UuS2QX5NlQ3iFaouugIqT/1zWEURO5VfAH81W\nGcQLyxZJJ2K8hQAk3ipa4Tvp7iRdPlCuV85bW9xMK7u8HkpOk/fmYOC3232u\nc44iH1e9R7wKTprzQ+aWcNUfwnnfb/nw1fA89KaL+YQg8qXInNiyzVF7C9Ik\nWeoHGPrQ5jebnZkgTJjubABHqohcezPVb19P365y/EIYULbVCyvy9i/gO3um\nClIEFGlm1P2Vj+68kYp2K255i85w8gjYhFvdGyILRDkZhrZDDGf3UAxDqmjL\nOEuR\r\n=Cuni\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8xLKsGbTRyPcfLLAow+G1gbyrGQxTTT7733iZz3QezgIgaJIqDzQ1p/uHZ+BrIXI0gCla5YWfQz06BOuJQ+qCb2Y="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.0.0_1554110849895_0.2988523279475934"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "make-dir", "version": "3.0.1", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}, "gitHead": "cde3270aa23b6bf094feeb90a34779e1da4880db", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.0.1", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NFCymdWS4z/T+3AQ6tjsknt/+4hikbOe6zdq9ZBoPFfoX1S3fthRUbQWzZhI4WZBOM0kQ0GkhdCdfdan3HK+wg==", "shasum": "584a4ee258a41b8bdd56665e5acd888f5168afed", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-3.0.1.tgz", "fileCount": 5, "unpackedSize": 10018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRDhlCRA9TVsSAnZWagAAnjMQAIJ6auQl1NL6iGdb05zr\nEQoY7a/4Ngvcg0Z20YFnCXfeU2nSdigwRysFmoyXyrsEz3arDg5KLbp04qV4\nhP+ZFQUqxwPqSfbf7/R1UChJfhG3P+SPOHyjDYaRER7q+/szEq2TFup6Os3V\ncAVluVE1YXo+JuQt38255cWxMK/JOMuD9KpWpTUah3288s8ng6ZWiopUJ/B8\np5I5/VhLh6IgDnzQnpsWFBcRPKT653f0uTbNAYTG+kNtCtdtHtvcb6iujqNF\nwqPbJ8y/q48nvIBllQ/dBOL2jeYgMitHmX8CixmSOFY8Rgzew2DEmtKsnV04\nfMM+GoTBoIEo+nmzM82bOBlJIlFJnEIMK3ukhyzFkx30u27eL5E0dw0RnG9l\n9hWMyQBHkaJz08PSlMud1/micPoxofV6BCwt2gUC+hekgOUuIn3SapTdDLUL\nrlJGs66qhB3Gwe1XLID3hGzvqRADnyOmAfGyZiImK4VC7aNw8FZLa6i1IPkj\n2aJOeIVAJO2maVnsOGC0ugK1QB23m/qEXflqtXoW2+0ymwdzvmb6ol9tOxIZ\nQi8W/IEyj8FR2vywOxJRUn6Rf0xTfZEKp47DS4ZgXAldxekLvz1yuel7dORy\n1yYiNHU8UxbhvvLeIhwqXv0xquURPmYsV3Z0p5Gc8TuPEad0tkYAyNWSWZVm\nnoCP\r\n=ikSp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCffhRuI0qudWLh93w2ZmXgst06qUlOveHb8CB3GS5gjQIgGQhfmMPRCIYZslvb5V0fGV68ocHUM9cugOdoDMk5Fq4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.0.1_1581529189182_0.6326104602385865"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "make-dir", "version": "3.0.2", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}, "gitHead": "66ba0d9def1b32054bbb94eaeb27a345e35abdd7", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.0.2", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rYKABKutXa6vXTXhoV18cBE7PaewPXHe/Bdq4v+ZLMhxbWApkFFplT0LcbMW+6BbjnQXzZ/sAvSE/JdguApG5w==", "shasum": "04a1acbf22221e1d6ef43559f43e05a90dbb4392", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-3.0.2.tgz", "fileCount": 5, "unpackedSize": 10105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRFvWCRA9TVsSAnZWagAAPcIP/16hZ0cHmLUQi+dRkNgF\nvUkdyQphCE+fD7cO9mzQZvHu6ur52EPNeqM0eOJkMOs5RyflpTnHMoEkl0K3\ntHPRpUWfcWxhyx85l2Uip+EB++5ChnF7F8deDDFnQA/O8w11vTTbpTj3Hx5y\n/awO41KbnK8hy4/SbAWq+WlKK4PhusnkFK3UPJio7fd3jsUGoYCUjYA2ckrj\nvhZ/5ku6XRj1d00j/WA490YWy/bLzIKTyqXHaMt+PxEeOBvO36D7d0xi7Rpg\nA8xUzGSKRqR/z1wKnzdfqRoZ7D8rkLlYHJf9dlVncaI3fnL6bQLI4+HVGlri\nfrY0t2MZsCvVxRiVNfUghdEoTvu8hTk+QvSpzlpenIHbr46+mH2hTXTDvKX5\n4A2GQT8LMeby4R33m1YX54IieX7xikOcD+YfQT7z0qJ58HrcpLTkYKTmT5rx\nLB8TRE1bSNc50GBwGbehrhTEgEi/J5x/XcXC/RGWPhC5+amC9P+Wfspf3Lnl\nZIxSVKEhRrIu3kGt3/7rcIWkcw7Q90fnIp0+2CeWht8ocZF7CxNtmt/CMv6y\nt4/J6TtMnWyJcScOs1C89jWJVoPqJs7moaN/n+EKdNv7wr7KlBBXKfrsRarX\n1ZytIDZawuZQIr35rpFhB4tSCrc0gQh2yIXbDLHC+wex23FWW+mzoEmqPQK6\nzqIl\r\n=/qk9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMbdeOmBCzlNSaWt9xbtlMcfB9r5JJ5vimOhNUdPYtsAIgaC9gQwqvtbcwtvmtvnQrD8jAt0V9JmSvUmm69EsYQCI="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.0.2_1581538261609_0.9143599805378171"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "make-dir", "version": "3.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}, "gitHead": "6d029fe1f75f1a02fcdd7a67f34eadf0941a424f", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.1.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==", "shasum": "415e967046b3a7f1d185277d84aa58203726a13f", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "fileCount": 5, "unpackedSize": 10042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoFvCCRA9TVsSAnZWagAAVCYP/0j1ONZQFX627tOCtOD5\nsUTka6qnxnPel+6e7jvtfn7mjvJlZumRFKaxnxCzIjbPmPxpQhgosaqR1dVb\nqDBK4EcJ81WzhxNUC0sahu/HvmHuXdgmyzuHcwRbWJ14QJiPjml5pV1eBY8r\njIJ+fyCSUj5qtCpJu1snM1ZeOoUH2hEU1wMPAS+SsUmlMqdzVWH7am6UdNCT\nAuaLJPIPuzPBfvPPkf/8VyrLY4OFap1PMKYi9K+FYOqUxidLJX8s6bFskHBN\nydaF5LUjLgY4U5A6Eg57iQDhWKAyi/3hGRC3828QLFkk2vSXhX/3MpblRCUG\nwECzHwTcXKJEgMK3olFHvG7FldHxAlhlL6IvZFNUe7YADCnLYrALdJNK7sak\nt+13YXEQtSrKpp4ZRcfjHcUjhHZnNSmD7rH2oKMUgT+QLpCLn40iZWQBWWJQ\nYpnOKKncCqzFJC6CtoXgcS4yyt4075EoRuYC4dBg6q4tFiarpSEK6Pm75pkR\nZSoNYt2dNzeZ4TjfnRYgS3mOFNm4keK2BtkOaCREGaelzRSqYapUmykoA///\nJ1BVOKqtAgPV1W73s4DiBgV95bUujSQBdv3lvB4jev3fayiwWj1zvtN94yfF\nLHBun2y39cPLRfGDBql/jYUspLzorM03seQz6JklDI1+8oJoz52gwAMjKVV6\nJ28Q\r\n=CY0M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9/vG40IjzOf93+Wn2ALa6mJ/baU0qYJf7597t5kI5lAIhANjrW0j3zrsFaVAAoWGuurvPaTNgblCakP8yNjH0cZZI"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.1.0_1587567554383_0.7728968177603428"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "make-dir", "version": "4.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^7.5.3"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^14.14.6", "ava": "^2.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^1.0.0", "tsd": "^0.13.1", "xo": "^0.34.2"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "54612e856661a0c2c61b8414f066ca43e407fe77", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@4.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==", "shasum": "c3c2307a771277cd9638305f915c29ae741b614e", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz", "fileCount": 5, "unpackedSize": 9913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyavAwzgHVhOOZvRVjh/L5wLuYCAJ6jGlklwgtVTbMDAIgBn+Srk8Wt8KVb9Cttai1QNKT0+GD46iP7/odXoHJu/M="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_4.0.0_1687521248134_0.053928984077646236"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "make-dir", "version": "5.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "devDependencies": {"@types/graceful-fs": "^4.1.9", "@types/node": "^20.12.8", "ava": "^6.1.2", "graceful-fs": "^4.2.11", "path-type": "^5.0.0", "tempy": "^3.1.0", "tsd": "^0.31.0", "xo": "^0.58.0"}, "ava": {"workerThreads": false}, "_id": "make-dir@5.0.0", "gitHead": "9b6308ca97bcb73e1a0d24a9c6173f21832bcc69", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-G0yBotnlWVonPClw+tq+xi4K7DZC9n96HjGTBDdHkstAVsDkfZhi1sTvZypXLpyQTbISBkDtK0E5XlUqDsShQg==", "shasum": "39b41251426eb8cff94de63bbef62e66ccbb538f", "tarball": "https://registry.npmjs.org/make-dir/-/make-dir-5.0.0.tgz", "fileCount": 5, "unpackedSize": 9498, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbJP7p++j2JgGwk9MJ0FsdB35qc2G2A3p23nWeovUBLwIhAJPdYAQR7YEmQuUKd6iz2ybA6HmJhAvbBJljYQ0Wu5H9"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_5.0.0_1714666134176_0.9038903094648874"}, "_hasShrinkwrap": false}}, "readme": "# make-dir\n\n> Make a directory and its parents if needed - Think `mkdir -p`\n\n> [!TIP]\n> You probably want the built-in [`fsPromises.mkdir('…', {recursive: true})`](https://nodejs.org/api/fs.html#fspromisesmkdirpath-options) instead.\n\n### Advantages over `fsPromises.mkdir('…', {recursive: true})`\n\n- Supports a custom `fs` implementation.\n\n### Advantages over [`mkdirp`](https://github.com/substack/node-mkdirp)\n\n- Promise API *(Async/await ready!)*\n- Fixes many `mkdirp` issues: [#96](https://github.com/substack/node-mkdirp/pull/96) [#70](https://github.com/substack/node-mkdirp/issues/70) [#66](https://github.com/substack/node-mkdirp/issues/66)\n- CI-tested on macOS, Linux, and Windows\n- Actively maintained\n- Doesn't bundle a CLI\n- Uses the native `fs.mkdir/mkdirSync` [`recursive` option](https://nodejs.org/dist/latest/docs/api/fs.html#fs_fs_mkdir_path_options_callback) in Node.js unless [overridden](#fs)\n\n## Install\n\n```sh\nnpm install make-dir\n```\n\n## Usage\n\n```console\n$ pwd\n/Users/<USER>/fun\n$ tree\n.\n```\n\n```js\nimport {makeDirectory} from 'make-dir';\n\nconst path = await makeDirectory('unicorn/rainbow/cake');\n\nconsole.log(path);\n//=> '/Users/<USER>/fun/unicorn/rainbow/cake'\n```\n\n```console\n$ tree\n.\n└── unicorn\n    └── rainbow\n        └── cake\n```\n\nMultiple directories:\n\n```js\nimport {makeDirectory} from 'make-dir';\n\nconst paths = await Promise.all([\n\tmakeDirectory('unicorn/rainbow'),\n\tmakeDirectory('foo/bar')\n]);\n\nconsole.log(paths);\n/*\n[\n\t'/Users/<USER>/fun/unicorn/rainbow',\n\t'/Users/<USER>/fun/foo/bar'\n]\n*/\n```\n\n## API\n\n### makeDirectory(path, options?)\n\nReturns a `Promise` for the path to the created directory.\n\n### makeDirectorySync(path, options?)\n\nReturns the path to the created directory.\n\n#### path\n\nType: `string`\n\nThe directory to create.\n\n#### options\n\nType: `object`\n\n##### mode\n\nType: `integer`\\\nDefault: `0o777`\n\nThe directory [permissions](https://x-team.com/blog/file-system-permissions-umask-node-js/).\n\n##### fs\n\nType: `object`\\\nDefault: `import fs from 'node:fs'`\n\nUse a custom `fs` implementation. For example [`graceful-fs`](https://github.com/isaacs/node-graceful-fs).\n\nUsing a custom `fs` implementation will block the use of the native `recursive` option if `fs.mkdir` or `fs.mkdirSync` is not the native function.\n\n## Related\n\n- [make-dir-cli](https://github.com/sindresorhus/make-dir-cli) - CLI for this module\n- [del](https://github.com/sindresorhus/del) - Delete files and directories\n- [globby](https://github.com/sindresorhus/globby) - User-friendly glob matching\n- [cpy](https://github.com/sindresorhus/cpy) - Copy files\n- [cpy-cli](https://github.com/sindresorhus/cpy-cli) - Copy files on the command-line\n- [move-file](https://github.com/sindresorhus/move-file) - Move a file\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-05-02T16:08:54.534Z", "created": "2017-05-09T18:34:33.547Z", "1.0.0": "2017-05-09T18:34:33.547Z", "1.1.0": "2017-10-22T06:21:08.477Z", "1.2.0": "2018-02-19T07:26:44.932Z", "1.3.0": "2018-05-10T13:52:55.437Z", "2.0.0": "2019-02-04T05:37:29.131Z", "2.1.0": "2019-03-03T19:40:20.829Z", "3.0.0": "2019-04-01T09:27:30.052Z", "3.0.1": "2020-02-12T17:39:49.297Z", "3.0.2": "2020-02-12T20:11:01.785Z", "3.1.0": "2020-04-22T14:59:14.524Z", "4.0.0": "2023-06-23T11:54:08.292Z", "5.0.0": "2024-05-02T16:08:54.358Z"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"raycharles": true, "kiinlam": true, "agnibrata": true, "zuojiang": true, "cedx": true, "jhq": true, "restuta": true, "tztz": true, "flumpus-dev": true}}