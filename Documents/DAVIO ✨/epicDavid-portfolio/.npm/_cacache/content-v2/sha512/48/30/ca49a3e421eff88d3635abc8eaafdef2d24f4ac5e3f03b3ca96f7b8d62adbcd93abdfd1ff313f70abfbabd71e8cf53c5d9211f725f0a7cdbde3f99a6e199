{"_id": "tiny<PERSON>y", "_rev": "52-1faccfa709d19db0ee7a6cb70e13503f", "name": "tiny<PERSON>y", "dist-tags": {"latest": "4.0.3"}, "versions": {"0.0.0": {"name": "tiny<PERSON>y", "version": "0.0.0", "license": "MIT", "_id": "tinyspy@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "0d38c8a8c2e736c5307261d81ac63c612bade34a", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.0.tgz", "fileCount": 4, "integrity": "sha512-HRBzCS20RqVMKhYlpbssXKmmquNp2RxGNX7dajYRllP8jgaCgw9cI25D0+sIZyn82pOtaQzNcsJhMrHwuz9kRg==", "signatures": [{"sig": "MEYCIQD5fFOqaxcEUpGJzx+8MB7VU62F8H4B3BuqfUHESGhB7AIhAPTqSexz8XLSr5OjZa/ufIj+v4bTrllzswW+lpZ7I1l0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht17WCRA9TVsSAnZWagAAkncQAIE02AAPIdzLreIcMUV2\ngdwvwFj+p9UYZQfkq38rn9nS5AurHAP7gvFZdsPxhzd1bHwhw1oHT3pUdYVo\n4j/rSA9rz3W+syDgH8ZuO/7gQ1+0Gpk4TLuowsLiFuvWOxkLHeZXSPaiKHlv\nq/GvJyk0TLQo+6Aw4tKWtvNEqKEcuNB4N794DVaakYSAwWliPPkhp1IlGZH2\n9d6UPbhRfOLphqOIc+83e1it1+ESKJTDN5KbYPlwY/eaN5NjxsbS8AhrF5Mt\niDfGnF2vyEMjF86oBmsmrJD3RIfl4hn5Zq9o5WgS2LRjr43qeSor+zm/LeGO\n021lM0khTHTF2nJUrSrvODd3p8VL5HCeHdDclYrx1MzHburzJsSL/5T+FuA5\n9+96qEvcdS9e2PsLgxwJWFXSRR28Abo6pw1nc4PytTj6r7RlmbIDvpcmTThB\n0Ab4elxo/UPDqL+AJZyujjPox+Dtg4XFa+KaE9FqIXZhtVsuU8D+O+oi1QJB\nCJunJ60f6z7ook6dO6TOibqsK1yRsojYSImdASsqgizxHMeDHgSUqRkVIpwG\nQwpHS+uc+pCuHUyEQ3/31Ek3jrXyTbxpjUNXGkJIxw2FAKu8jsj2LfmVoUUw\n6+6O/rJcuKZwa7qHZW4uI02wZ44+OfX1knev9Rakt2yUXKXVZ9xwS42Of07M\nmLU/\r\n=P717\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.mjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "gitHead": "e78ff7d7c447dc9819bfed0e3e0617218335373e", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "4500 ms", "import": "{ spy, spyOn, restoreAll }"}, {"path": "dist/index.mjs", "limit": "4500 ms", "import": "{ spy, spyOn, restoreAll }"}], "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "lint-staged": {"*.{js,ts,tsx,md}": ["prettier --write"]}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.0_1639407318806_0.36376659363482067", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "tiny<PERSON>y", "version": "0.0.1", "license": "MIT", "_id": "tinyspy@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "8e3be443dec6d684f3174002cd8d341c01f9970a", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.1.tgz", "fileCount": 4, "integrity": "sha512-f5OCA8J2FgSTHmtnVK1fmCzLSGdYc/PqhFAwNL4aD3ZrXhTGRCQn3+tiqPnse9Qq6R3ZYH92oUE7P9DFy+R9AA==", "signatures": [{"sig": "MEUCIQDXiVyR7J5YCwEBtz0lgdESchIuJbOg+hmkQlxc8P6kbgIgFkfRAya5lZNEOa3lnOXPPDCb8cLt9fmFEiekHi71pHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht2NXCRA9TVsSAnZWagAAp9UP/ihS6tScBIvia0/Ozami\nKARrFy1OK2xpjsPhhQisQAqsWNSXB4CgkZskSc7TvGZg3/jz4tZQavggzA0i\nGpTCFy350OP7GsX4OvrgJkIBqSKbGOi1GeqDImb/ZG1k9APpkXKk+2r6GPFN\n7/351Kf8vxVXS8yoCwxqaMj1l0VwfL8uiIabIYNDPD0bApFlOS0yUvUEJPxs\nENBaAAV4Dog5IFVw/+QoX4QvOoyfZhaXym0IavyhakxcHoq6VnfhNmQBC1F5\nGQuYVV2cFwGwR7lvDie9vQN8vDK3ox9hbs7709MMZX6voX6+HM1GXAR0br1H\nSzVEInfLRw6jSrxZPNACGxX9hqdPZabN9ujFGkOlOFklpyRBc/ovykkzwgdM\nFNqE7Mk8yWGmWEDDz/jWJsAdq1BT1lX9VSvhNxiXfst1zqZTy/eeoFyaJj8p\nnmJnuBcABPoXw43WJIOeY3AlOjhIL+9U5QrgwkqyMFLw2HeQ0PltIgit9RO/\nEfgjDmXuXMIQoKGJVnO9OIiGz07juG4GmN0D8qyndB3jKPsYioRSEFngZ+lx\n6KzK4vn+Wq+vpBF88hoQaR8BwWpnQ4SvWLZpCQyJocnPfKs1G1WMofOTtSLg\nhqIZGRE2xOwbW4tscvye3lvFY/39PU0CcCayKNb2dIl9diD8vwybbA19VsMG\nyDuD\r\n=lVnr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.mjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "7210bd55ade94ec3266339ad471e275e0911a80a", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.1_1639408471305_0.7017485562509422", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "tiny<PERSON>y", "version": "0.0.2", "license": "MIT", "_id": "tinyspy@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "8dedeab83076c14e921202e8b50e4b05b9399fbb", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.2.tgz", "fileCount": 4, "integrity": "sha512-uKwum56kEir5oEVNT3ELZMDZesXzvRrsff4qDhZx0KxuG3wgRZy1R/4R1cHxjtTJ+QHSaOVgckyeIUvOC29h9Q==", "signatures": [{"sig": "MEUCIE5Efpn29o0LtNx6sCLaJn7Xy2mI6NCEMfuLGXTHINI8AiEAmEpEhP9lpP+R6P5wHV9P4Kzbf/x4cVY7uVZL909yTu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht2baCRA9TVsSAnZWagAAP/cP/Aw4uEukibiQeOy7LZVo\nXLzA0+rwpouNoyo9Hm1KPxsVoRGh4ovHN/tojE+FRCIuTUbpHwQFjqsqElDv\nDX/N5Hz9kXAyU399et4Bi9ustrtd0s0yYiwRbISpwv46/6UeledXYRa7DvbL\n9t3JzHKEr3zfW90T7x/y+h0ucNeo8G54DxmH5GUjtC8l1fyXFtNl5fwkPXdH\nCnMTwkVWYK85I1DW8mD2yfisN1XKEwalnwG6cHp/kmbFHUS5EEoi4V0mnwHd\n4ZOnwzB8qXDUP+GK/WbKc0dy1oftpM+iVzhZoborJj5XADUzJSJfa87Uui2m\nt5xfWrgIPOeIX7z+YtkhqentUDUBmIj7c+a+Pz3CzLRYCr/JtCCESGSZIaoB\nlafEhvJ7R4CydtbCW6m0oehsbC+4XSQGagIHt+TybwyxGQCvuedZhN7YuWs5\njOY9cMvcDab7QTTenoNMrvlphWsqpjxxJoHRrR8BWoAIK7J/GWMZ9jYLc2FQ\ncp8Ojb3/yCzhhvKd8VKU6cINOMc9ScZWv4yRHdkGnxM3dDUFCJfxqW1TRcuw\n7OZeSKcyLgnM56gjiGy0+Meoa8ugar5AmEBUcXH5S+pffiK7FSVrVmI8e8sH\ny4tTVPplfFsSMbg9rp17yj9CdLugKK4tvTdU93dVEtZOudJIqmZ3LjdH1jJh\nHSyz\r\n=sfN+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.mjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">=14.0.0"}, "gitHead": "73faa5e2d9ef0f0bd0b1f1a7411158261c0ffbb0", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.1", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.2_1639409370248_0.6590230299963387", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "tiny<PERSON>y", "version": "0.0.3", "license": "MIT", "_id": "tinyspy@0.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "2962417ca9ea34f37ac38635da8ccd00b954731e", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.3.tgz", "fileCount": 4, "integrity": "sha512-+qeSpuQGBRuCcMSagHNQCT7b3DBYbJtKat3PvNOqkVFx2y0YxDshXtJQ/lbKvQM5N8Ovo7iE6xQskwRmJzwB+Q==", "signatures": [{"sig": "MEUCIDsw4SGqYbinJyMRppR1F02UwzkcO/dEI4vDqon8lkGvAiEA0C7x4MO/uZtPzWF8gaxyN5vdcZmtZydK/zwwgz0wk04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht2eHCRA9TVsSAnZWagAA9MsP/iqZPDH3NwhPalXyMq41\nd2ekBGoSI+AkWWiEnCr3C+eWBLzb/FeM3uKXbrTHZk/drsr1dAbm1VcBB6ZS\nwTiWZz9r7JhVOka8TwMEavZh4UCh5rW7R8ob+RsuHqw3FfShQhKjpC1O16WT\naJEb5ecaE96gihmSH87kjkX6kfcpNXVEXeizDxLpc/qkanPiL5AbLXFqyl9t\nTCaz5V34zMmj54D3tBwOdP/udk6hGExXGPEGLmcos7orT3zvViMIgGWeCAlo\nUZ2YuXPNXjpX2rHpysc6FViuHz8HvuSKusV31FVlcepm7kqV/nQ1dsEW0ap3\nvaexenTnSDjRVt4UZ/pHV6VfBUEhgRs3n4UUawL5iY3jaOHSV5UTNig07p7d\nD8/0C5cGLnckz2X+/yARC6R529b6SR55Hrtk+ecFA7lxgL57OaIIf2MfzWIv\nLg5Ogf1I7sLG59GbR8rukS2MK3is19tHUo4WWctNIZx7QV207dtaKTziRBA/\n7w5A25BHwYpBIv7Zq8v7RLmLmzWFikxC1+ZbdxG6cCmetLA/CyjHtLHq1OWT\n800E2Q5cVyBLgsqznxk764zT5QNytE8qcRcRktkrc7GN83qEP2M/29zxh0G4\njde3vKFdZFDdcU67SupE3ePWnTZn69nLUcJfqqstAr4GVSlsaBzoiWtx6CF0\nnAIK\r\n=TO/g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.mjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "gitHead": "bea767790b5804beff49c3858302a49a9b9f8a30", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.2", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.3_1639409543614_0.8407414876644053", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "tiny<PERSON>y", "version": "0.0.5", "license": "MIT", "_id": "tinyspy@0.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "4e4f4ae01ef2282d651a0cebaec7a600c968a13c", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.5.tgz", "fileCount": 4, "integrity": "sha512-Vs/MgbWKVJmmVW54lvk/gcUHc6NAgofM3axIGIPCVESecMXvlFyMUR4xE2n0UR4/5itO7cyQlhmGnELnWaBLEA==", "signatures": [{"sig": "MEQCIHoiNYpnOvSKKXgc1hCFkrUT1SaXRWyS1fbyCM4pIyAsAiA703lstNJXJ2yIKT1yjVhYyU7cJX1vqIGWUjfXqDU1vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht2jcCRA9TVsSAnZWagAA+CIP/ismQOzJWcqz5pdc60Ni\nFuirwNNi4yrkAoqOqRquESfRDNOfRvC+1IiiKQuLmNWwSDrDE5154AWJdp8X\n6v0/TczSwC3CNg2wE3UfAjnnORhZ6xBcBAdn4oTYXU67FVbnjZ5B6iMx9lek\n//tY1FYPIj0jKk07lUBO5qOdSLn0R9FrTe95mYZ+EmGj0onkUE8BdQbHtAw/\nOh281ZGhuTaxdLaucJwPdSysrxPB8hkICBVGEavF8MYHS/UIkvYnSNBqkANr\nYjuG7PZUwXN/b9o6U5+t3j6sNoCaLeXd+CsF7AXv7qQWrlHX4qsliY2Q/fEE\ns5dpuy1r25oGTTORW6IHg4xuT+PHBrpNnBU0SFUwGzj4bH+9yyZ5WeZ12s2K\nhzJx5JWTVkw5YmVKSoJnbdq2lAiLDRTqzbJijHMfiPTGbrkrrypdYznUtl5Q\nlqGorTpMzn21kAkKj+KFYeD7cp9IiVKUHZQLzfkgiGtQ0mn/eLesVkK8cH9H\nFiWvNRBYmE8HUGT8KGamGtoD+mMwHyIZBzgmiUaqvghEFE7R+P4PjIU20sxW\nWiwrMAKpbbS5IhmLwax4PJFkLbD2TdCFxBllZIGm0vWew1rVAPKS0pWiCIPC\nGHjJQ7rRLNPBkEviSnDRY1d04D6Dvmi/vBgvpAKZfsXN5AoZ1qgNwfAX3Gu5\ntSWE\r\n=Ii7h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "gitHead": "f05c749d167aabf0ae260d8cfadcaed67910da59", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.5_1639409884322_0.775584057386361", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "tiny<PERSON>y", "version": "0.0.6", "license": "MIT", "_id": "tinyspy@0.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "802a67bd52eb7ea3586bbb093f0e59f88ce86aa4", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.6.tgz", "fileCount": 4, "integrity": "sha512-1yBxEQ/lmgu6NvtAYrTxlhm6MszdJNlVHFxEWBmHYMGAWhr+Lc8rrm2K2J58NbagMINkU/qJuOfBU92kdUidSQ==", "signatures": [{"sig": "MEUCIAoQSbUa7ZmE0VEk3j5KzlX4OG34riIMc/GGHtTUnKulAiEAtQLZaIMiqFSlHCsSLnz1WFN+mwFxPFY2BpYqqansUSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht2lCCRA9TVsSAnZWagAApVgP/0UECimBNhKoe7dQ65sm\nPj7v9TObam0EWVCSmIA1Qz6lO3GXRrU9ElbIsW/8HsPJ3fBYvxYYTVCUkA89\nVuMH/JOXb2h48HN/Ii++BJK+l3/otzCCij8FKZoP7lRvHjvmr//U5k25+mLG\nuYNLjR7or5c9Tijh7m7el1kOkPWoJGtAWXgTCqf2patYDEqZdNxrpURxpgrm\nEQZeK965OiTHpaTomF0laqpjaAoaghoOZwESMYwfw4Lr0AhNjOaPyoOexwea\nu09ZRJQ/UEkSKzrjJE6IehgNXnUrAqHmkhTqNFWjWppAA9pbiX7ja7F07q3B\niEMj5oUKc7tvfYCt2+Wm7iU08WUZlPQK9eHSDG6AqB0uIXk6RLAAn+jfceVE\nMWyUR5itXbZkKVNphOQ76EeLjMWoGar8ql4zlJb1R7+x30t03deiGlFTdKM9\nQSTyF30mbTOb9qCCP6Tz/cTUJS7jPRcHtGlR7pPJiFmiDrmrYJAa8HBMKDGV\n1CLAsSa/sxbOj0ReTeeC/ZZxN5EV02D9a5gzwUgr81PtscUC6LMM5L2t+JiB\nUvFsUhD3NqBgWkR8Z3K3+Ybj3prd6rSDR+dSHFdGted+s02Gjjxl1Efe5MxQ\n0K9wHyuo5FboxFI83jLmpl6MY5gGXnQYzg4gUQab/hXxPYwAE4f8VwY2H/aa\nQCea\r\n=uX0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "34e32d1a5051b54d5c930ca4c318db351a25a4d9", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.6_1639409986389_0.1786369944145061", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "tiny<PERSON>y", "version": "0.0.7", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "cb8e475f04828032ea62be49092970b2f24570a4", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.7.tgz", "fileCount": 4, "integrity": "sha512-4RootE3cPh+k+kkwbClvaVDlLrOwLKCOuX8xaT9mxAig3MbLpp+Gnz9xnMheFzx3jSD0O79OBrnaPPz3P9DLtg==", "signatures": [{"sig": "MEYCIQDHlK098B5p+VfL+vbHHjRk2gBaE+gAM5pDhOCYab8nZQIhAM0UNnggLFzViBKZ21MRU9N0tN/uB+F61QBKjEeC9jdd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCMACRA9TVsSAnZWagAAg4MP/R4xIaFtU9Nu5bgHamRP\nlzl7ajKkWYHWWLXGZqDbA8hUyqKaoH//BhYGuDCy+5Vd3Noh+z93zE0wVvrB\nLohjA1OptDZWwn3nDiOrFqBcmzZVAw8r7WTvAdL10DmtgeJaw/kj1CIV2tyv\n3V94taY8chr4hUWdnJU5DjNID0u3Zl/cUkgc58Ez+jXPPByNHMVziKPmoCUJ\nrLpXSgyxgw5y0mqfx3JWpbt+cTCWu9LnR//3XFVhD1rtUCHVNxH+HA3oXnM9\n5bsxC6i/uLckcblhFXjuaJG/FlLDDu1etVIY+1JvAfu4sfJUgJzEoj4donoR\neky6lFUejDGcek0+PX5FJ8OOFOWSyI4qUvxF6I+wEwo1j94liGa2b/12hWu1\nvzFhLFdRfJsmkMu1vLmCo09x1lzKkRuBURq49XG1laYbZYDlY2gUP0TTb0hv\na0e6xohf33aAYTCccEDlB50FAX3iwVFmGN+ZcxEuHMp9oQZEMjPNkp3E8D52\n87Ol3uWZM5GjJ+jml4JB7VRbYwWupicvXSpmAjk8WvyuuhHH5EmfpajadCx9\nTCmZM9Hq68wQZHUuA8pX0XT2rNPxNwFEjv2xIg84f3MXpAru+O0GDgywlvO6\nFib9uCU/7zWXCnM5v+lr68AOqFgIH+xbiHhtZmTk4EQj2PNDVld3+NHALc86\nxqAo\r\n=6kuu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "d261f61e7b42812fc5b53f79f42111f784af0329", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install", "publish": "clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "clean-publish": "^3.4.4", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.7_1639457536199_0.3569117125529784", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "tiny<PERSON>y", "version": "0.0.8", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "c17311cb81dfbaed3a3ce032cc23d2b6ffd83edc", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.8.tgz", "fileCount": 4, "integrity": "sha512-KbLMmHfRqDq4JST4p9IoiVkOOrTniZ0ALxG2EbWBvWlBXkkdL4pnBEZthdrpkynYtJuqjwQpgQ6GfMQCDSV2jw==", "signatures": [{"sig": "MEUCIFv/YVUMUdU2GXsdQQvbOI00K0z7aOFPC2+i1jJ4xvs0AiEAqOyilfP7smm4L9aBeDq8eSvUtlW6f9+kRa5ucD+YxxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCMwCRA9TVsSAnZWagAAUOMP/jiZrCLsIVFZ0HBa9RPY\ncqa1+JPzEuBE8V0Cvkf0vJ2VixrihqFeKbCEGv8Pl3oWN3tYYIZrAvw4uBJF\nNf82jMR0Qil/3dNDcWJgvjDUZUeWtM0oVF99BLS4RrGOkghkJ2I7e/DDaPDp\n9kW5/h1Nxr1cJI5eZJEEmAZwLnpmUb8VE5b1ys+l6SfqoWLVrHF0HUtXAiRw\ni/eh+9wOCUICDxc+x2S8I8zO3WQecy/uhETsPAOlxQTp1xST0ARm+8/LUpvQ\nTKk/HGOWo8A00t24ku14pI3jBMH+WhyMGL4+Q1y4unk1LBCcD/eu2lVYsjf9\nRfn+Yq3Yk/oBfKHVX3RKz+Tr/GZh60efNAMghxcUSZTid6b58n6T6kXbqVbN\nwIBZNFmoWgU1eZ58hmwX4tn67UbaSA17vkgjocGzLXoAanNIkjWuFpD3Ylwr\nkfkFbPsK5SV0KJrGgdgeH+utYhf059Ea+KuwzEpvyWlBF6fFHXmlJxyXbYGY\nEoRJ1syFvWkNPX1QG3hZR7wNlcsi9ULDjuOfx2W4V8GVpz1krL6DxCnzXyAL\n9w3fQwh9yLm6XjiwBDDVlVW5iofHe2yYTX7HDqS3GbvnsmG/I8DQrX4h/obb\ndpKG3jo2Dm6TX/5mmr4nOkC3wVoXR/IUPeuu+S86U92I3it2eY6fjfM0McHU\n1SnU\r\n=EuOC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "631f7538f959b954b69c121198ef8764ec7ecf95", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install", "publish": "clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "clean-publish": "^3.4.4", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.8_1639457584405_0.9561452498567347", "host": "s3://npm-registry-packages"}}, "0.0.9": {"name": "tiny<PERSON>y", "version": "0.0.9", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "fddb182768ec33d2f40ce37ced1239509d51cf8b", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.9.tgz", "fileCount": 6, "integrity": "sha512-PUk/yCGDk5d++Oi6gq0fOJdpB/0pWnfJI7hM0JQCg+Ipe3tc91/GMrpxYt2E5kXmnPiig8U7Fu1aqeQPyrtLAw==", "signatures": [{"sig": "MEQCIHDdvcUtXDMpXAVb1fcUzSPQHFaKmu9sUyvqcWrEzr1oAiAWPO5gXeU//ykYDF03lHfx4XTupHMzZClKTTXhXv/4/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCUuCRA9TVsSAnZWagAACPQP/jKzPt05lCTu19pwRspR\n6aJ00E9v70y8jX4ln7JLw9HSHitFHJFUBLBuxaUTpiiae9uvHS19FrEgqI6v\nI+0qgL+fc/pcku+jykqA/wf6Ho4HKqxhnfi+rIkQCfvGc11SjDCVu9tlWWh1\n8d0jqCHy/cxfLLnO5r/6gmMLeyzm6tVbrPx6UBCekhJarR7u0zjvGX4XWM9g\nFaomynw95Sxodgwb60nb1b+0wFf6HK/EeYtK4b0UHRyzYvJHgAjVvXMMZIkA\nAjP/WPHRGgjQ0Kh2/xkJcyR3o6kOEQuzb3zBOfZowycSCok6lQbYUiwa1nM/\nyxUc/JV+30fI53vyIgPfPsqxTgh4rMXoEMYgMX/opCpUjcyJNDXrC2fPifPX\n738FZxbH89/ZLb+ke/zjDw1+D+p2N/sWCB/THduoYHdpOXz2qfUcVP2rlfO1\nwzviO/GM111c1DATGMzX0uStOLD+TtoeNp1P10hb3qgHgshEllCiiVdxsPOS\nlQtP1Th+c6QVqfhZGGxuWd27ro+kEduG7NZD9VM3efg8w/IHy5lH4JKAFYUE\nQU2mwZh4abW/LDUPygzTd7MI11EBABLZxrRt5XxVyHGHtgrWjGh2Sw/d58n0\nxe2+JyOW4Hno+AKFBS9RdqVN4VdVvGiaQTmW/MJZXiJzMShV+1Nbw8XBxFIp\nIRHL\r\n=9baC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install", "publish": "rm -rf tmp* && clean-publish --without-publish && cd tmp* && npm publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "clean-publish": "^3.4.4", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.9_1639458094747_0.8645030186114373", "host": "s3://npm-registry-packages"}}, "0.0.10": {"name": "tiny<PERSON>y", "version": "0.0.10", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "975cd345ff295587eec8945b1ce21eb792631146", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.10.tgz", "fileCount": 4, "integrity": "sha512-Uc4I2IP02sR/M2QqPyieOUAV26yaxQ79qXR/SxwwQxXns7VWK/SzTflSeimMyVleQIiYNSR2Qnhwp07gRrmxag==", "signatures": [{"sig": "MEUCIClpu22m+3L35UmzTSt4PT3LcI5FZZGrRuPX4jUUQL+RAiEAuHlSX69tvv9tGDy6CLhREgjtJjFYlOcFCEjV7W8cFzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCWFCRA9TVsSAnZWagAAThAP/2cquFq5hzf5P5rW6DDO\n7i5M9FjHuVJ1vpauCDTV5brAJgCAy/AOoDtgyTMYwkyyACd79XNFKlE4ahK2\nEz/mxPYQaRW51dDfNm62NCFs4oJm5+uUn5ZAMixYGff8zA0INt+z7UDLyZ3t\nzW/J///3CAiVJZtO+n5lY2tVWH50KG7pHbv3uh/3BaxZsEtsO+tAeQbEZ7k2\nmaRXVMVObCnwdFIpkb+U2rIm2Rpq9JXiUk+6qhI2oViw9Xo7VkTV9g+sDZjL\nrR1ZBMjXyCGJhNk8RDZ+5h01gILtENdW4yHqNR/G39uGbuDE0L9M/kF792AY\n3T1suVasVo83IqCmuhfuOD3Ua4o2BHYgcxiBvJRmnsSCBma59VPtNH+r3V9o\nhmEGb8u7bhu5PFVjwzl8DswsSMU3Xn+JwY+WeF3bpo9du8kLf/kFH3UBYOcb\nV2T0kwETZMbu3uTUTqCCl5FB3jqCbtXUmwyGiBCajIJHM37hq0Y+W/T64k4C\nZbiFcxLoCVld/37e1t9Y/cqD790tYHmFqte/7yPGZXgCiXJtfUfzHTzcNcm+\nbBFSQojzMO5n2UvUi78vG7cU0VMUv3G15Z3jWNqhMhe3Z73eq2zm8FGu1FOY\no74Y8wg/0gLRUJenPNXmgAzZ1NJXY41QH4/T81bMaOUOxejyS5ZdklhIuk7X\ngzXA\r\n=jCGC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "6dfcd4b8cb859c5a7ab1b561ff7d8f53c374a731", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install", "publish": "rm -rf tmp* && clean-publish --without-publish && cd tmp*"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "clean-publish": "^3.4.4", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.10_1639458181108_0.8590584132150065", "host": "s3://npm-registry-packages"}}, "0.0.11": {"name": "tiny<PERSON>y", "version": "0.0.11", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "190feba784ea641132a7eb97e4456229fa8f399d", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.11.tgz", "fileCount": 4, "integrity": "sha512-P49i2XSRN59oN/5aLsU35sslodBykNNOz93lLrsQr4U8rMS/PzcQ+k1YRT+P76dC+sbSh8O97mPOeBzmPXeJRA==", "signatures": [{"sig": "MEUCIQDJptKv8QxHKsFA+Qsf63yJGQ+t44ZViYbJa8zvoDYZIAIgO9ix8xXlJvg85EnhYZ+zDWYBwsvH43XC7NeSH7PuT3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCW1CRA9TVsSAnZWagAA584QAJ445MAmp1cEZQ9at18D\n7AxIkYq0iFVGUOcgBvf9iXDchAXl+vwGPXkLy4Qcg/Ar6vsClGlsDtj7e5Ti\nq3bE7cc0SbmJSW99HwdKAysAeRBHO3DjDuwpuAhPb5nP7reJZhgBqJlmRVVt\nf72322K9/ej3pcN/4Ru4Ha8iUaXEo/pMqapJgvbmZC265az23uB7lD4Q40nm\nA1Z6BeaaptGHpO4kxmQTFa0wXyDDWuZCKdJbjq61oHrNKjQs+OaQrzOd+Rgl\n4RWxzemjOfb8xua3EQh2KX3O5O+qGcFaCE73SyhbWY3WJDuhYA+DWTSB3fu8\nNXXx2gKp0PMwE9feR47MxiCYtYbrZF6A2FaAq6i1Aj761P1p0JPtqHtHwa+n\n3zKqcEg7DCe79JhvcxItaF6cadUfPqqk5ghS/1HRE0rU5FA/tmxC+cjeeth6\nfIRDVYAhOjhNMkeRWwIz2csXTTlxB+7Dy6ZvUXhfzc0oiTnSa3mFLi1fqaWu\nYkBcppudhj4q9UbxcklNOq3bw7MdeYzWIqN1SndjEzhr9gNlMACqGWeJn578\n5UntYhJ9nacLlmvDq4p6bD7cTUnHFlSFOCXjEuYtzGt9KlbJClrhahPmfZkS\nap9Q2FD0FymV4r4O/l8aATWnyFLtFrPXlcrp5auKQ7BiD6GVTTyEf6CCrJR5\ns//q\r\n=SUOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "809db21607bb0350302d00dbd33ee8fce37239cf", "scripts": {"dev": "tsup --watch", "test": "vitest", "build": "tsup --minify", "prepare": "husky install", "prepublish": "rm -rf tmp* && clean-publish --without-publish && cd tmp*"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.1", "vite": "^2.7.1", "husky": "^7.0.4", "vitest": "^0.0.76", "tinyspy": "^0.0.3", "prettier": "^2.5.1", "size-limit": "^7.0.4", "typescript": "^4.5.3", "lint-staged": "^12.1.2", "clean-publish": "^3.4.4", "@size-limit/time": "^7.0.4", "@size-limit/preset-small-lib": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.11_1639458229257_0.8131950565930832", "host": "s3://npm-registry-packages"}}, "0.0.12": {"name": "tiny<PERSON>y", "version": "0.0.12", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "f791dac4ace35baf5b415cfc1c9421f93ed9320c", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.12.tgz", "fileCount": 4, "integrity": "sha512-nP7qB75ZU4xdUNgWg1xensI7z+GeDFw1zGfjVQviyU3lf8ZS9TPE7dCvad2VznBFylTHrdoScBWO5bI1ZsZu6Q==", "signatures": [{"sig": "MEYCIQDG4Fq5LQfsO8AefcQJ8YileeNvs3aX3fPIODCUtdRLvwIhANoVFXKomuQmpqNHwtLx0NgXUIesXet8R18YJA4DxLY2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCZHCRA9TVsSAnZWagAAuUwQAIr7dLEe4eP3BZp/OBKk\nK3G28orDUbIosny9lKVlV/S7NzquSu6SgFyehnmjFF/auyBuas9z88n805eJ\n2+jHW8G1QpOq36JMZaAom5o1iiaOE0yX3yckNe3gL53uo44/iHnKIATpjMrW\nDzIuqS//gInll/xim3ttygcOzKAw/0GF12tLpSdrrCymxmr2+RWXtsfdaBjB\njQIBNPvAtgqoSiUSWWI644VLLJ7VH8uP3CnF9Yd8q8wMphKhLvThbHIUXJ5L\nwHFnvZmGqpM5/+4f37Uzt2WcUEVST692A+oqpSmIYjG3HkZIb2HNiUYPmxpR\nG0VE4aLpR5pl+KG8kcbhGipZNji56HZV94OdN+ZPYT+QydbYj2gvyEbxWTUQ\ndKzhYkxLDeF1krPXFL3tsiKBo6bUxdYGq/Whx9QRoXV34cU8/qNWDco3+DBO\nCgTnM3gOb8RrGsVXNzL95z7qlP2faxcMpS1ggW6x6GXIm2TaY/K0lKhnJXiG\naMcHJ6/YnZfU1Thj2FoVSaOii5PWFl0D0M5G4viUMou+OGJQ3tfWNxkamMna\nsz9U9OvPgumvsDZ5qpDvlPhA+tGj7YAxkgQgmXtAuAvGiBKibGsg3H445UIP\nMihA+M7DnX7TEaDPPCOKLLyKrOT3ROfpMD9PF8hbANDNHiuN+gRS2rzlIyM3\n4B5Z\r\n=ZeLm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "scripts": {"prepublish": "rm -rf tmp* && clean-publish --without-publish && cd tmp*"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.12_1639458375150_0.11093786401406125", "host": "s3://npm-registry-packages"}}, "0.0.13": {"name": "tiny<PERSON>y", "version": "0.0.13", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "78a60106f7cf6c5a02898af461b63563b4451867", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.13.tgz", "fileCount": 4, "integrity": "sha512-31GGJc7+mqL8mIP8BDJYve4WvwhXK/jAmEKhqQAv/mN660TlFIY+3seBANGsAFP9YFon46XlP94A+N2mPM063A==", "signatures": [{"sig": "MEUCIQClhYV3TBfds+Vo5pojeKlx4/MM1vjA46vD/ivGUlFNgQIgW2fDImsqLVHeLqrtM4hKCIkiFegVMj7VK7Rz498G0HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuCZ8CRA9TVsSAnZWagAA59cP/0BmaIko8s08e3PSIcIf\nbUnKOxV1y/gKuLh/9MMb7rlZO9wC/hMfXJF0SckD1r19uhK0xT2WIaqSWdUD\nOY668QoxfAOaQpgmzFNpPTzZuyP8u+McyT0EB/R0M/+ekpi+IbU8baHQahTa\n15EJq4z0tQNWnhoHXW0XjR7SbWy2IscEPZxAqPCfK7vbzxPV7ii6KmHxrgNg\n+wsKHbHKfADC1Uz5lEP8uR2sc1nngXBHijSn+2aeuLv2Bk9/jfa4qBuylBc/\nbIN6ORJcV241mpLGkCZZl58xjm90t+WWibrVh4+8S1rhdwAASoltQs6QlBNc\nGQvAbASYJnUHLccct4GJr5h+gnxcYo9/LxDsKj5NO4I8TwGVnZpUoAqmKf0I\nH8s4Pq6QpyS3EmxjH+GWaZbs2COwqXYP+3hmRqrBeIh7MJ9eBhWF+dNPMKzv\nZkf0j1MwIORr1V3KR5xqKWNy7IxS4PH7sXs9NaA0doXktTvx3cRT3WTK/iUf\nBJQEV6JaSPVrSjQ3saouRnhax3qtR+22aGSBubUNdXBh+Yr01nKRmH/8o9Dx\nHbjwlt9uO1dEEe98BrU0g+NXXAUHic7Hbkck1lGN2RCpHi5+Nxb8EbxjwGPI\nm98EQL8l0HOO4DkBLypekpQonsjMf3MKJR66u2fzU4LB6lfP0YHUib3mQxlt\nSjbR\r\n=uQ8f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.13_1639458428650_0.47187029681450987", "host": "s3://npm-registry-packages"}}, "0.0.14": {"name": "tiny<PERSON>y", "version": "0.0.14", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "64ec73130313c8f313b0921b3d7ce72a4f82780e", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.14.tgz", "fileCount": 4, "integrity": "sha512-Z8TTDvdBr4N3N/nmW9+ymY7E1E7AuAZ/ChtmelltgFTwHyWAbhRuhfEmkubUwpZKo/7aP7wPlIFVDO2T1zoNhA==", "signatures": [{"sig": "MEQCIFiC7+IBPuxh+ROIB9PUNztpeQypPapx+gkkfLfykyUEAiAKA+T/ApldVTNgIlLqC73a2ZPijqAIYBV3uWSG0/jomA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuKe2CRA9TVsSAnZWagAAR2MP+QDjthkQ/VPIec7gwDtY\nsOaKw0c69CPVBgbxaPFirezrQyWB5dj26jx9Z7O+80VYcJtYhLNV4MDDWAVn\nT9/vNFOoJTI30LdOQZw9cMTUcFpQAGEoKRNkuTV3qLh5Od5YzrShMNMNPsNt\n56nHeiz4V3SZ4v8MUekeFVaWK2Nr0NzW6eY0iDC4zJabPeONxdI5yuHt+JtG\ngMGvVGxVFwL40i5UeydzyTNETGwHCNIhSOHuwoVI/0asj5XJwRapWP805Ra3\nCLzfzCETpqme0DzvdJhp0lbpnoxdvvrwLmh+A0iA/lNR4R7GV+QmJAkNghNw\n4DJlepwQPhacQcjh5OSggD9SnLUfr/loColpb40aP+SETPfxqh0P7vXE7+Mf\nbBzgg1ZKn5D3XLmloWW2aZw/BP68ILq+8qdiY6Kychj8nXjBR+zg3J8+VPTD\n/bSE/EDUO6gC866Bmhm4yXQqIog2qrb94BJ/S9LHdRGxEOdTMC17ckDp5EfQ\n4y0PazaGzIIyNSoLYzQZBQcqRRLgFvt9zuaViBuiayaFZ851nz/TfLsT4Bu0\niqPXbyCxAJIKImG8RYZOp45WirvFwOaKxVdeH4cPZqL8rO+JBvSR5L93h5Dh\n4k9bWIAd6FIbN2BJ0faja9v6hQZQQ5OM/bSefrLopILbGRTdk0o2WL3JL8Jh\n3Fmq\r\n=TuNm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.14_1639491510357_0.8691805382793121", "host": "s3://npm-registry-packages"}}, "0.0.15": {"name": "tiny<PERSON>y", "version": "0.0.15", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.0.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "b401b68df1b3b0e03a3a3c22d04a078a3099c6ba", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.0.15.tgz", "fileCount": 4, "integrity": "sha512-s2+x6FRUJqsMxUG0Jo3l0zqIvIubbpuHIY6n3TRwtxd6llk1ZAGIWzKn8MsRHJVFB1xzwp4gRHeVo+pvSvr3PQ==", "signatures": [{"sig": "MEYCIQD2Yx5VE4nH43yaE1CVRhWSMYaFknH1zMO8V5EjXomyzwIhAKoa4ZMtWdRdMod4nKXQDE0BJz6qOnfwHoSqWdg3FYm8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhug4FCRA9TVsSAnZWagAAwb0P/Rwk18NT/qU4hX3n1eDt\n4/UJ0C70pxLg2uw88hzYdr9cl5RovuOSptHStFRK49Q3KCtNn3AiKnB0H+t+\nlbMFZKWkKrHF7w0KA1jYn1GjO1KktPY+xSClGdg5uUgcPo1rXRdbupp2VMHr\nsjt27gkuwibEpbTpjqjuU6tEdfTZSMif5h6++syv+YNBE54P8Urt+fp8rX6s\nEw2a0E6pjvZmHcDcOOQfbhib+VY0qkz7AjqleUX3R02gpyEXBh41WVhFQICh\np84EUb+dlkH4o2i2wHwwZwBToPPdG2I2Xy3IjDT3Qa7RFQNvaFujpeU49Nss\n1V87+hcsiI6W+AjcH4nM2puKkGguQuYaKPD+j8rxF/MpAxAf0lsexb/3D3P5\nFNz+oVC8psiACxlzUgK9H5jGk6G7FJxIUHtnzDnjwArumfEvN0j+3FR13lxa\nqt6x881rg58WYA92+WWBFRx4+yGH/5Jw5EFd2k5X+lAK/d2rLW5DZFXL2QJN\nHxZJ5G8IqFmwKAdMvJJ57Ykw0Q0sqtyC0skKj0L4IaLclx+3dPmuS/xOLmH+\nTVYC1rF3bBTSsh1uf5HxC4nP4zJkB/ZavkRsWKEbIXXYWv5Uyz48TA9sWw8i\n+zi26wwP+GHdnJ6LWpSy0cjlAyLVuv1dbR38eFG+EIknmWKJamBeXVOHgR8T\nXslA\r\n=wYOK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.0.15_1639583237567_0.9019054936826454", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "tiny<PERSON>y", "version": "0.1.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "0466886b0fc3cad7b99b52ffcc95a6f9bb6ed737", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-hxHEtyz3+QJ3wg4ZmUlqhpNgaH1Nxe0E4g8ThJgJbgkB0GQgCSKc7AtDm94VdVbfepgflcXshq81OwW83j9bbw==", "signatures": [{"sig": "MEQCIBKm/OH2lo/Pr44Fg5ORugbvLGrSdW+qBknSlUw5omerAiAiWbT+LTbpAbjaVxVD64Mz3820pI0Pr411RKGZTtFTrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuheACRA9TVsSAnZWagAAOpQP/R6+XVzWwhQlrvk1Z7s1\nTe+zGHb+/VPIz2a5cDF7tq/TW3V5M1o9tlH2+pd0voBIoxNaG2Ivgm/QApyO\ndYoWg3X9hT+LXpe483W1Wf3DPZD2TFUMCdWf1dMgYrEgy8um68pbwdgG03Y+\nsIRrNJLbuv/BPOQ2v24l2jBDMe/JsPl6JKkrOkm3n29abgzfM56G3PYZK4+t\ni/fKP7yi6tdQdFKjLKrVRdOxoz3qVXGmBS1eQ9QXyO6JX2PwwSYD4bZOfDmX\nF9WT9P/EejDaX/BVT5XyOU4o039FdnIP6/PDZ5eZszFNyXZ9CYcdtWv7ZQeU\nOsLdK84l28ND7PO/l+/YtI0ZjT2X9P7GeijyIhpjy315ZJHiAkzoES98SXXS\nE8/nv5hAxsn/vQCELL27ybl+soSqHDya8H1vdIy4HKEG86XqqYcLqPBQqqUk\nwnHZP/Wntl80uafmmTdtNRUGoV9pMCsUJ3E88xB2IrapUToCY+7NMJKHOCdY\nVkuZ/E39zjNuiNpC7Z4LdG1Zgkfvm77cvrLxMWYCMVuxJWmTL+3CERvhI46/\nx9CTz8v4aDP2UcarNOGQQ4nPHnAXeBWqT1v4WSUX7sN8c83l6HU802h3jsid\nsGJ7yrLsQ4Kbq+8r1DYP0xvYWd0XsmuSBqj4bz598CQ+vWLBYPKgW5BTeKqX\nqqv+\r\n=vNs9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.1.0_1639585664400_0.6955374280964892", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "tiny<PERSON>y", "version": "0.1.2", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "449f480dc624fbaf441ab49632429fc2c6387f7e", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.1.2.tgz", "fileCount": 4, "integrity": "sha512-ReLcUvHQeyg6FCXWoaajS2FdikgKWSuwrbrKdq4lr0lBAHK57JqBPEF0RZVqxVC+ap5+ktQemTBJJc9XDO+fgg==", "signatures": [{"sig": "MEUCIQDafjhcoiL31acOA7eHrQbzvgAdan4quZo86YePvzakIAIgR9pfcUuhemprKKEh1yyDtc4HdXnAta84W7XfyL2PSzo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuyntCRA9TVsSAnZWagAATW0P/i9M6M8A1r4cAujyJChj\nJWSw7wsOksLy1rNJSQCnGEevZB2QqXcQrp07Lnz4Uf/VDW+occQqTeMddUDJ\nU/ctlj61j4r0UzMZrV8mF1UwoUP5ZpFqw0q5SZV9FYzQ0eXUmNC+cRuNvgxz\nxy9RXtaLYkYQM1L73VqE8aBLOTgtGlAuuH99+A6zXfFuwm3MeIeJlo0HlMQg\nCN4dJZcXIhSKfKZ+whCalkYSEOlyzmnEVg13s8Ui1L+3jTTC6UxMaahrjG/I\nDwuORsWLhrxcAC7/lpoB5qWzBq70JRizjTFV6mj7cUb2Dint6vchHlWzXn0t\nNHSllHKIUwN2MeGtbXuZSDbaCDuWssiZyCfsycLke2XRTiOaLJqfr3QtS8Fc\n01pql7FS5EWXmYcTLl9SIcyuEnvapRM4mzpy3RmNTJiPm3y/7TSOpJQpxMW1\nnvFdnWklUxlQ9y6LRxQ5aaNshXeZnaYj1nXPYJ8eHqV2+T2WvoMJvmGEmDOl\nHT1Lod9Gd3tU0uMLEPi0OnlUikjiMfDpgZvWxVUZfDSM4/B4gOWqNEXdJw2p\nIEzRvz6O/FgaqPaOJZMd4+FcY2B1gERcM58c4FPeF7nKaD47AbYYT7zWRlBJ\nXMzJmgMKa6TadmPBUCjM7X/Jfdz+U40p2SyB5L2unoXaH/YJCZK05g+qfruQ\n9qzE\r\n=dc9B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.1.2_1639655917171_0.08254420781136096", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "tiny<PERSON>y", "version": "0.2.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "38d95e3849d883e60dd940de1981fdfefd9911e1", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.0.tgz", "fileCount": 4, "integrity": "sha512-TWTMi43o7yISXJ3R6Hfb5hrU/YY0KXER9brvJUZOMXN5Kj4ZCrBSlP8resgBG3gdDYKp3wR6iOLGgssHy+wPlg==", "signatures": [{"sig": "MEUCIQDubmpPhu11AL+zHAIqDVwFNSEDbLzzT0CU1185vFVuwAIgN6+2OGeLSpo91bQqLI6DmgM05++Mic9CN1Um8mpBOZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwA6kCRA9TVsSAnZWagAAOi0QAJcRfTFNo89KahUO4RNg\n3u17JE6SWdVgzvDKSya7BuvC6RAJSWd66tV9l/VGkeNVQLpsjaYzIt7YhjYF\nSDtUqfw1Lv/ZFGOPALZtF7nL2nNkjQOVqHVeRTx4lv9VcEMnaWYxY848WVLM\n++oozReaPG4wHzzXgKzJKs1ooyeggjNnnpqnZoeJMe51LytGt90hmEniaIZh\nUdAIXf1uU+8/fqOBCEMK+5iRPdFTCd6Lr3hYsPwLOTZk6vqZVPQTJLfur169\nx3K16bOXvon15yj58+czEWa0V0vQoy4umyFa5regr1tUnMclKY4gWxb5y8l2\nZMLXpGqFltpiOux6okeewQgADaxJUWrGDnlx9yKd5J30MZoYEgnORHgBbeL0\nMTgQ1yIN5guP3nq6KMmQ72LEiK7CNiP0zxiBX0aWd3FHntwOxjuemkZZDt/Z\n/UkyvH2RIst4dmmcZ6y5WkCqSd2Kms83R1stPFe5oUMU5cQFjjGPoNcllIIb\nKLvYdErYGjDTCNrITAa9TVLO5c398y3eRbPNVnKyIUT7ceeizE89QbwFjnJn\n1yXKk48Id7Qba2Ko9cSfRCQz8aN7HmO0UN1LCJ1y//niueOknLCvBBk4zaa6\neqv788+vAZ3axkv5YmxrUrsujTnqxNWpJzgXX0XFtdfV1/IqrUuNO5kx+SPc\nMY4u\r\n=TqyW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.0_1639976611936_0.18440751229706054", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "tiny<PERSON>y", "version": "0.2.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "c2a3b051ec8a77d85394cb0832647f29b2849bb7", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.1.tgz", "fileCount": 4, "integrity": "sha512-5iI2IXXrXVm2vipLylJpQjBGUoA992ir3ZFnRb/pPYZX8AHXr4a3XevUO1+611Utiqr0/EYnxhGU3Fux5vwfJQ==", "signatures": [{"sig": "MEUCIB+zy9heEW7aMbGoCUAiUAL6le8AKUT6a5ZtEGejNqbRAiEAgNLngELLbrcLczXOeI31Xq6dgDQKONOG+DGZWYm11D4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwEdQCRA9TVsSAnZWagAABmkQAJh+E/fjhCvmvvYBCNBP\nxDVIWas91G4RxnnYNHKc5OHNpj7/0newpqkZ/KL+hxFVcRBHiELUNXGWBM15\nnaXjBIX2gxjnYxgE471/e1VEHKfoerUNSPCK1PnRr5Rut3qpdtfxlocZJ8oE\nTu7kz2B7Njc33j7nIfWAsg8i/fFjrviFrNeyfURqbq4J8bQRYx2w/jHucAet\nAbLCOD/G6xXmtRDiatiJW07WueNK9ACfzLjqnkmjJShzmo8AdvhnceQT31LS\nPH2p7YpxM4xEphN69wol+HX7eLcHgFQbw3sW0x1oorHMbJVuY4B5wGiGG/Xs\nVk9nf6DXApqcz1KUOCmljQnN3UpCAZPzot7Y7axGTQuQMuCwPcHt6/wkR2iR\nuFQ3o5IEpThH/XlH4QgrebqbNDrr7Hu0IYywPV4LGKncG6OvH5LvQJGR5yhv\n9Ljqpxb7GIrht7op0PfTz1DszQcIbbPRzfqyEfM0k7sSmWHJR1UVWC1WELx7\nGlWwzxNRuIwDdBl+rd4fS72xqIhGBE/KAi1+jp532NW+bGAVN83WyLZmelL7\nMtiI8IEqTvhw0X6ANtFkLFwrGfwZoRdGXGvqNBFoWgZG/0a7xPqyU6lJsv/X\nkjwb6ZCcXi18fNKi/9AlP8HohVuXCpubLmlDGz4OPoIPHzBmKk2SWbKtzvK/\nC0qs\r\n=ZKVL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.1_1639991120196_0.4852238064160197", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "tiny<PERSON>y", "version": "0.2.2", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "28a56a8419710523ef37de523e45757313ac75c4", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.2.tgz", "fileCount": 4, "integrity": "sha512-PrBzI65ANTl9dnf05u1nvFF8OypYXw4n6jQ2krrzLKyGsVD3TF2ybIGj6OgfMNFUBseN1wMSJoDqGKCpzDYtCw==", "signatures": [{"sig": "MEQCIHzSJpHTZsFJH/mYm8yHkDVU9BBZii7Lh82Y5qu5j+9hAiBqCGDnTJiSm0vzsXd0L0jJ4cKSZzWvlw4COrzPFDC0FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwH95CRA9TVsSAnZWagAA0IAP/1tnGbquKdAVqRv5H1Bt\n6wvfFo/Nn9yyKAJSIh9d4PIrpLw6jlPTAs7U5RorN+b/6vlJ7qlFoSRZzT8Z\naxZgM8M3Vunzqgek5SF3RHnZfWNtCdP0MFLju54VoqR/bjvNIzPl66gnBP89\nvqxzcoTVl59IclPlr2ORwFxoDQfmsfMGWfvScBzLqe0iWjaU8GBpUFveXCDt\nNgCjqSuikCVNz2GF3YPqh68i/rPgewZfuLvg6q+Yf4nbN3ZjD0H5YYfmWcfO\nN7oDEsS/wQZSNpcbSkYicbgmDS79u2jNEDTOCHvfTGyCHpaJHdG1+Pq+m6iE\njdXI0Q54AlPFC/w/yH+9htLoIj68ifLnOHJNhKnWkdNcDbvBfJm/5RlVb+LG\nQNx3AMbdzJtj3VZQwI5kEPXyFvpMXwKx3ThjLrdBQhpjdBxPjYYk5ruywpsV\nDsUHrvBTaaCPYVX050rORlhLNolSTrr3pz7oyzrIcDYGyYW4r1EQz6MI2vo2\nW7SggpRaH/dl8nna1Hw73Ol5+qyumM94O+KkfNINaHjnOpiz8OffI3fWKLNt\nfgIei0UYhMqsqbZJhed3asO+fcYVd4V4YdfMq30USWJSQYuhf5+byC1OFZe4\n6lC9OTvnxyHDQMApxF1YCfwgoMKotOISqgAU86G1jSywjZkvAdMlRNeXP5Ou\nbIMx\r\n=RLld\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.2_1640005497747_0.8404236855900953", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "tiny<PERSON>y", "version": "0.2.3", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "0dac66c73748d63fa082ca953a77d5076c2ae2a6", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.3.tgz", "fileCount": 4, "integrity": "sha512-dVGQMYQ4tgW0gizUrL0A6XZitcNH4G7MLuhu6xETre40YVNvzB7Tn72BmorQadrOdZvw4YHfEynTpd59ypg4Zw==", "signatures": [{"sig": "MEUCIAwhrbo95WULzbl/IwEGXY8w2riiSgFK0HN8Trb1CzhBAiEAhoDAHb4th1Oza7+wwAr94a250l+nJ77oYRDg8o++Uds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwIaaCRA9TVsSAnZWagAAzvcQAI4r7+5OqtIYE/4jymBV\n/B+pulaVTyM/Y7FAUmT69Q6ar3gO8IctY5Uja0WsvfFAySbYry8nI1YbN8ix\nLtESIAvUGTfiGPIPDKPOKSQoH93Nt10cSqGQRg4iyoYbIyUc1o7EW+uArMeE\nAQZks7m/FkRF5NZ/jJ+EEXW1LZOTt5uZbR691GN1xAcmIOatPJwhANeS5jbL\nCG4jOCpaOUg8P6qK4YIzPYyebX7kBaReOoi35onlALVr7FEXdHEVk+7ZWkEK\nln4mODmarwNNNf8ZfshLvimUBeG1K0cK37N32PMpu95OuWOoH1InVxYd2GGD\n2WL3kFAdlTeUtGkJSymjYNsm9sUIQdGTB5bKOOM3LVAZSI04diph2PSgvacX\nIhrCBkewFDHIb/rBwlKKol6j4q5v2AbTeC4iLrkVgnxWyYTykSQjOYDgalMT\nWNEQa1MX7zIXFNDv6vZ1AdgK3rpsBprmw6io30lrtwerP5iBApUEuD/CmQV0\njo/ybM1IX6FYjvk9cYf2X2f9H3dcRpV5ehP+aKm6vE4JWeKqtF7J9B/HK23e\ny6meo2RG7NaweD12VOEst4VHf8sWveiB7XpT3WkblEZZNt5JtUWAtwS1IFlk\npycxAWe9ctI2+m66NH4tIYoYxV1zli3FTFs+MgjIeY+BQ7gMrGymiL01xYkW\nixh4\r\n=Hv4D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.3_1640007322265_0.5234042475752747", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "tiny<PERSON>y", "version": "0.2.4", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "4011bc091021520a602a22832208b25858eee109", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.4.tgz", "fileCount": 4, "integrity": "sha512-A6C0zYMhY2cECXJwJfgesG1jbEjz/d9Enuzx5cC1qiSrGp6eZDMtO8Dxl0Nrn47wR9abEtGr2ckQi52OoJ7eiQ==", "signatures": [{"sig": "MEUCIAzFaX+bmw0l0IslJ15zd1RIpuPxD2dzsoa84o0U2zAaAiEAsN4McK3yTn5JbUklMDW8j6xL+FLjUMmVei9FvFAM/vA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwVujCRA9TVsSAnZWagAAM5kP/jKWgzcohkcjjFhQpi6n\nVgM5QnHf7mFcK4WU660my67uJAfcIwlfnm219iT4N5awzk1q/bI+2xhoaK3y\nlgYLsL1It0XwpteIAnRvoWR8vsaPIQZ9+7Xq7f1dYRsUseMVmLnd86lJZ79Z\nIq5353JmxK/YeFdQtm7QGdnd7vI6eEFg7WB3fVxJX6CF8fKL6S6zFPdBUUjj\ntWRM6Jb/brTiYI+vvqSYPFDLyvHU3QAJg1WZyy3rJxm0IKJ9MaepONjYdA0f\nxP9G1zJ01UFi50QgMRFfK8Dz/42TJ25b2vFFbko2QD02IED6qf6jt/gy7pLr\n5qcEt8BjIHTR49rSZqkl/S742I/m1pBOmjAYowRulP7pFOoDV6Fdi9u4nyjD\n7c3gLecbA/3uDR11IK1XBMtSjvxSDexOcZ14iUGnpYscZzn3a04sK5xmDW5h\ngPO6DfGNgVLDxLJo83cbcvY2DLxJ1bnG+LgFSubHRN/TAhmBbPaasGEa+mEd\nUErfz6OX0rd+Zkrtga3nN3vug2CmMsPHBi45Ae8qFTsb38yERm6ZE5hXQLXt\nkPE31QVa9OETRI3GnS6/bVAXpsJKRam598Y65yXvJG9k931lfTTnHcwMYqdQ\nWjgsBjkskNGpQcdouECDp5eH5scLpJEC4RLzZ1bfGwARornAAGiZabzNRZ/H\n7q1w\r\n=Bqaa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.4_1640061859170_0.9818552547064101", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "tiny<PERSON>y", "version": "0.2.5", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "044c8629779942c92c98aabb62c140acca23dbfc", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.5.tgz", "fileCount": 4, "integrity": "sha512-RGRejsG0PSjMQyDdOry88HgcAda/g3pnMTsNngwSdnDasGC87rj+vbvVp5FSdh9pQ034AmvluVD5ympOaFr/yA==", "signatures": [{"sig": "MEUCIQCGiCFD/7AdqMrWNOaoubdDy68ri4ZL+b/UahzOa+iMXgIgQzYOFiNNig8Ziu+XsDJutzyDqusoH0bmvDzb1uqaEHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxCvtCRA9TVsSAnZWagAAiXgP/iOxb58/Bsr2B1MuVD3l\nL5vJMlv2D38958jZRNO45GeOQ9QuDxnFeKHqfqtSlI8+eOU4e/ggOJyjadOB\nRdNRgvvJcskvMpo8M/sr71kgX+qSa6ogU0h/qf1MmvOtYJ9szN/YsZSKAre8\nGZHTqn2J7x0osxRTHdYdsGaM4Ntxs8yDKv05HW+5ZD+cXpE6mtkPk3C2IcGF\nozUPlOX+vxMO7EZd4ybL1dV3jz4WWPcQa/a3nFPXCJwedY8IMOFEIqyq2r5P\nN8vjgoxl7GT3h3KY7BIYqyrz/SbhdDi2hct2Dut4fq0wpo1Ppf6soKnwakfH\nfjy6qSLzTU9wsDQkILQMpGw2MAv7y6M3ulkovXXavTQd4beetTFtoZPOla78\nWBjiMyR0a5fQdEdQn7wSOOu2d/4Kr547mxUjxpHkkjauBJG7Dvv9dj06P0nq\nc4vfPYaD6exdeDoXEAzkFze0PE6PU9ZoIRorlu3pmjBbwOcwzdIYygRkNAnJ\nwicoKBXD4MAZknw6scpPeRbuQ1eAB/RxHweSi+PUNvzPciJiRcTIxFmEVatq\nxYjZ6zh+RdiRRUDVZJaqvwJK5CIJiyeylmzBynL33Aa3VNYE1fXmmbNyMEUE\nJdDbXU7tXljRdDlUPDfkcPCGLP55bh/OnvNH7qJBxn4LeKLtK77cuw00DNMr\nLpyo\r\n=B9Ag\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.5_1640246253156_0.3424947451223461", "host": "s3://npm-registry-packages"}}, "0.2.6": {"name": "tiny<PERSON>y", "version": "0.2.6", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "4c5430afa361d4a18431f85d9ccbefc21c607cb3", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.6.tgz", "fileCount": 4, "integrity": "sha512-HXNA1PZ9p95rWK7h3DvWaDK06XmsjN3ldZjacW/1F0viVfPanTGSQ6l2iQyjNElD0Nd5ogfq/hT0sx2Hdyz4wQ==", "signatures": [{"sig": "MEUCIH5AiPKD9hGIPXpmNacO5/oTywQH9DJsX4RkQhseXnIkAiEAsOU7eLlwcLargeGZRmV/mY4yJxkBSuZ+gp5FkYXOBiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxEEdCRA9TVsSAnZWagAAl4QP/RpUQ4LIKhq1UdZweBah\nI5i2P0nOOlzdJTdt6UWYzZDpcnDCAbdGO+IRgY9OTn8iyowla7kEoaGr+RHl\nedgeCAOoR4uOov2kFQDj/uTe3RGF1z6iZG8kILr8hjLGZELu1CD5AzdaFCL7\ndeVL7NkroTKYBgMJ/EqSE+yqaWezFcSEpaTIFX5Kyq/hruA/OPmsCxt3tUIF\nxJp4WkMz/Mgue/aiFoD0y94KqPjMj/vESEhaOgBh+RfVEuAaYH8wwb3KOvtj\nwg1oVKbKaPyhe5pbMt6spxYfsudSkVNNF5DWNsmIGQMTfRkWWl4coH+892HF\nVWBJBgP+jiekK4zNj5e1kUxQYL1fe5PUNeklk+xLPGPskKJavwMp+C+oUEPA\n/JgciAah/mIi2EIf+QCWzY0E3O6hvWlRjm470pIjhGVgl1giqRI01clQ4E1X\n+cKjiv9BTmLbXaaX4YBnGR/hV4FTDYQIWK3tN/gwti8ai0TeAo+18Z6XB2we\n6k1K1cbcIKEyQ6H61K3bYPpR6Ek6D7AltOMoXVZiqk+s2aw2Kwsf5Q79dM3d\nYDfW42UWhh8e9fDf8Zd4n7owslrFcEnjPbLXFxxloyNwIOvhjEr6Y/WN3dsv\nNmJHuBXJpaiqlOJ0JWbFwcdCqOlc0nBo1c3S6RgQ2W4eCqMd3MMBUtwpT6JE\nH7Vv\r\n=EIh+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.6_1640251677436_0.6694869257063947", "host": "s3://npm-registry-packages"}}, "0.2.7": {"name": "tiny<PERSON>y", "version": "0.2.7", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "265b5deab32c89b2fa1986939869ef74b160c0f5", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.7.tgz", "fileCount": 4, "integrity": "sha512-AaNITSED8/2j2/nSgPeQZW2nNhdTxNJ7XwcdBezI7MtPS2C+pJ1HjcHc/qfDhohUhQvODITdxzWH9u+Qs+7yFQ==", "signatures": [{"sig": "MEUCIEOwgVEDfgolX9E8G5idvv9Xk5F+9Ep4/iOAktZr8cZ4AiEA06t1fFMxpUXupMap6VDeOTwJPG6pvjbDW/WvS+8/IK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1CgZCRA9TVsSAnZWagAAHLoP/1aHqTC+eK1XL5Kl8NJ2\nmQT2lUA46V4Pocaa0PSsLS+bjsYn1i1zi/+DfYcnrq6/9DIhtm0JrFM4nolR\nj6Z6kHhp2uztm1W2hgwD6XO9R1Z0W22xT++FWG6EEQOv3LmAVqqUWmO6SQyX\nsu6gbshp3Kygg0n3SQNBAoo68Si2dUZs1DOy1fQ6WRX7JkIcRxxeD1VYNy+8\npgQcC0aI3Sve74wWtSaszLklAkp4dbxeGny6DK0LsEn8vjKpqvuuv3KptAU8\nsu6W4N9Irl8XjorWcDba+tBGSBGZrviEkxfMIIeTdkBb0qSftHxyTZ9bLYlq\nI7EgeRacxRwzJzUme0kMbY9Dy/qy1+JZABVCilNGWzS+eLAezPt+RO9eXh7f\nrvs9L7J90LlKKNWX+nXA8POaEsRw+fjjjzGMz7pqnpCsv0CkKOiK/y58tlOY\n2QpcNzztTELeRcp7L5Y4p8urafaZKTiprS6L0GjQimfqPMAYQwql2so12Lid\na55uXnCONPJlMqa6RPloUdb4pUBCFUtkq6DlRd9yVNd/Me6pLS6QEv4XGy0h\nvXUeuJuSluMsK6apFs6S4QSNwcVaq3Ccka4x7hIWDvShn4BuazcdSTbvU8KO\nS/eHSrSZFO5gC1fqdH8zYNQJORg9sQRq482GXIlA+pbpM/qXgGCLUDfcSbYQ\nk64r\r\n=vOgR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.7_1641293849688_0.2055779372182831", "host": "s3://npm-registry-packages"}}, "0.2.8": {"name": "tiny<PERSON>y", "version": "0.2.8", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "b821b3d43a7d5ae47bc575a5d8627e84fdf4e809", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.8.tgz", "fileCount": 4, "integrity": "sha512-4VXqQzzh9gC5uOLk77cLr9R3wqJq07xJlgM9IUdCNJCet139r+046ETKbU1x7mGs7B0k7eopyH5U6yflbBXNyA==", "signatures": [{"sig": "MEYCIQCeALAt7ismkY1dnhIGaIAOOcYGCKfOrMrBBNtGJPVeVQIhAIR86EK1fDv4JvQz5zD5Irvh3QiKEchUK98MtKEgbHum", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1+gVCRA9TVsSAnZWagAA/IgP/iePwW+jccS/c/rtA9QS\nyJiAZUgNILF8Bwc1ciPR2oUEPNknGSqUvozDmh8LKfUHBGxcgrmAmhakxDzP\n9ZSSvEK9u6Df0wTgO3pqBxV9JkJoGk1Qe/ovInCiMwcY0qmpKNt2Z+FBsQhG\nQTRbw1emoEg4f3CEsfhEPrJA0X8zUTtDozW/4MBVHympm/W3JPxL3+oG2dp2\nLkdpeteJEEhY76tKtfRRRyMiIOR1uPGN/t5vbn5zsN5Ro1udxH0ghDRDKtVH\nqNa/72EYUdPPuWVX4NSMBjWYlbKKNHV5w8M4seHDRx1/IdtyHvQ2r00hYoub\nez6X4Sj5wXWK0uhsH6HUQAzhw0lLwt+8LwZO/StSv1mvqodBh4SpB4r7jOvk\n8/fZTtaeodXiTRjpwyQa8LTZ9Ug7PKNh+24/NyT3IEBzi9n3tSBfMcuKig6j\nH91yUowaXa0wxAybPg+AYxF3C+Pn27UAspBp2OTcYpWswdy9mxHFDyCXP5Rj\nh5Gnb91fOwsufAzh3cO8bNfcZnUdG5Hc9NNFKl5KhmfoqGBxpQivXX6PdUBM\nVdfv1rvg64MXrFne3k26q2/PZukE8I6VSn6efwCBUjr+NEmx6uv8LbRUCNx4\nvnTZPUL3MSguCBFtvdiumFn2XK5zeHDXdw9/SAFUsn4DwmNldHBxQYGLIn+X\nIAec\r\n=s4p6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.8_1641539605701_0.08010677544361844", "host": "s3://npm-registry-packages"}}, "0.2.9": {"name": "tiny<PERSON>y", "version": "0.2.9", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "0927e4aeb13cca50f4b418946dda5460fae66bc6", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.9.tgz", "fileCount": 4, "integrity": "sha512-hAvGBACXrTSs2jgCO2Q1rYVlfx0qErhOQE11zRrYIyMJAf1Xej9ySc56uxKPL7linfGX1csfSR5Kev4QGRkdlg==", "signatures": [{"sig": "MEQCIHerR5fBCgIDJtbBKzjVv3BujpLXNmDuj5QVLq+5st9xAiBrbZjJPNzwfyQ7Bw9Qh9U56GbIUz0xIFSAxpBWxI111w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8tmjCRA9TVsSAnZWagAAzugP/AoBiO4nWKUvs3uH/AB6\nVIyl9T7KdIY6Nv+myD7pgDEq2ZKzkvJe0kwjbj0/3bPeA89QLfLOlTdttgSc\nrFmpCmaJT3eFxJ6MtgGRnBLSKX3a+01SZuEfAcYSoPfXU/ETCtm9SAo8Nln0\nr3/XxJ3NpIBMRdQ0kWxzHe9tHAUlzpuNfzvgedPqAlgg+ng0ag1169ButTKh\nIbxjEb8dbqq4Kn1gc1mSGQ/rb9hV8a1hQhZ0iM1sDhXkiRlT9WCe2w4uxil8\nHrMiPqNLR7XqdSNgGB5vqS5YDHtAB88THV2pM/OfVn51IMXyJ1WY1F0hBIiD\nnWaKgTKKGXKgHHpZ+TYJMRvrp6kwvVvLxcFICk1hAqAKM5vVR6KazXebnbvZ\nptoiyMje1VkLT+mfgz8r2GhJ3qe1I78a/V31ThEfNb5pUw6BJ5Tu0XXF5x9a\nOqq0ogtyR5JC6SRd8KMrLCDOjA2IoGIiVbeUwbnZKVWLscSMfGuWqTMDfnJL\nl+zaCdDP+MMp96vWamJHqkc0NxdAEe4GMs+2zfPNhfROHw0IWPvrzkB+6iVp\nMIzgtikdTMKbXgwbHermdr1lmf7CSoaaLUZQ3AaiCbfJirjDtZx1Gqy6bqG6\ntd2lVaBm2ILGG40/yXnWwxQjGUkN7nloU9E+4TOPRt3M/fyQVjWfIWBJkruk\n5dni\r\n=+Bfv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "cd377a2531510ff77f9f48bbb79a4e6e521a558a", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.9_1643305379210_0.7578762064653253", "host": "s3://npm-registry-packages"}}, "0.2.10": {"name": "tiny<PERSON>y", "version": "0.2.10", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.2.10", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "7f684504bda345620f7a6a8462c618ef3d055517", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.2.10.tgz", "fileCount": 4, "integrity": "sha512-<PERSON>j6rGWCDjWIejxCXXVi6bNgvrYBp3PbqC4cBP/0fD6WHDOHCw09Zd13CsxrDqSR5PFq01WeqDws8t5lz5sH0A==", "signatures": [{"sig": "MEQCIE3Zymdn8z7sFcv/OgLuLZ8TzbuXpNFF1yy+tD1KdI3eAiB4xaSPnH/JqQ7MMbdhuiaPpTJsVV08b8hi0RmsbTUc+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7440, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8tpGCRA9TVsSAnZWagAAuTsP/056CKktA88pSOV3Xcz7\n+OP9LugRCRivMNoLx6IL1vWH2OC66pnkgf0fVKLVxYKPVDmn2EoFW1l60hj/\nbN8MHg/55gA0y/+q6HCIxbxyVu+RXqmt8qSkx640zxh4G9goKT82RNV0LuKm\nm2u9p+1yLEkJ/ix9JJ4CUuQQQvMR8TUZF2zDWEhkcFIiXZUkdc3Wwr74ivi+\njMxrlL7SUXSR54xKJDy+w6tJeaRwBh6usi2RgoJWIINbAdF2b/Pmlez5YciB\nu+9yhyp9G0Mj56i3O67XtVmPH3yQ31y6yBguFDDUXW2rnPFHzRq+NBlWT1iV\nPt5aub3AJfDNnNtuEDfCkKkz9zhsIN+AXsC9EzY+Nh55vjQH9pDsPdKQNwDS\nxEFdrnXqSrEIpBN1qDlZqoz6XDr9/2Tn9av5NsuqOyMu25Ti3LU03FOX9ggl\nBcR3wPK5VltLzTSwcBJBMh1gok4N01vHor9PY1fSrvNYv39IBXoh9aPhCZFA\nyKbCylUDThO6dNRqAJMswk1we8IUYjOleEhOXK8L5yZOCMrsp6H/gTwyFoiW\n2bT4LoOPS0EywVhpL8Lp36vPF1BJRjahK7bwChCqWOSTV4We/mZNLNwjQ+AY\nzSVwToj5/ytXkF0W9WezypHMEBFm0zxddN6XihvLwVRaM6ewstz50sE53b6h\nlz/t\r\n=OcbD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "cd377a2531510ff77f9f48bbb79a4e6e521a558a", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.2.10_1643305542566_0.10160497947601987", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "tiny<PERSON>y", "version": "0.3.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.3.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "51bcc198173385c50416df791cd10c192078cb36", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.3.0.tgz", "fileCount": 4, "integrity": "sha512-c5uFHqtUp74R2DJE3/Efg0mH5xicmgziaQXMm/LvuuZn3RdpADH32aEGDRyCzObXT1DNfwDMqRQ/Drh1MlO12g==", "signatures": [{"sig": "MEQCIDxA0FikyGPO8NoqC2/EEZRf+2MPIJChauaSNrFlpnV4AiAfXv8H43ayukb3Fw95ULbC+mPliuEvGs6R01hW8kEf8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDoBtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFJg/9F4m3w10dO8icbUAPWobeGbmXGuuKqk7/tVK5GsO7h31v4get\r\nsN0x61Hyx/9EKmg5NFF9M6cGFWueQRqaQGlvB+y43OHwGIJH7FY7Ulw/KF0w\r\nqwNsgyOp5c5c/rti5zZyQXIJU0Ca2XzLGf+eLUoUExbCSnKP2lbCwqbXwLLC\r\nXNrggqeqtK1nCTNGqNKqUh+Di0bI3iTzO7vVs6d9xcuAIYcq62tHv3HlIqGE\r\nXzmanjrfHJDVYAcQiTDiZr+B90wkz699wQNkftGkDZ9XpTIhb24zZkrFSXWz\r\nQgzYBq5BoizrP+Om5x9lOtyCMJCnH7GUL3fef36e+EWJSxXg4is/PTUet3fd\r\nPdPSV1ImO3UdC6tEWtBI5mVaz/9jNS3Jt1e5ravk2/sxYZ3Mz0In7C6c3rQB\r\nw3KH4frJRaU4d8pfdFJ2e7mtjKixy/3pVewUQqPXQXXTuydcW6r0fzfwbvG9\r\nS20V1uvonSrte4pmSmkayQQvpdhPM1d6G2BzMx/lwMCcjG4rYKMlVQNWd9Kz\r\nVl97ABfXkYv7HVhfTywPw7khz0qvJ/duWfwDw0s0XYfudh8Z0QL9pcJUt3kw\r\nEQOYtAo5+lV7wf8NYAELgKOAwryH0/3jyNWnrpdY3jAgMzlF+/edaL+NMmIt\r\nrf4k5xbf2TNAlq4KNdScy9ZaN0w4FzW79OM=\r\n=7F/k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "d097117d51f1ca7df2cb851420f12d3f95f1150e", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.3.0_1645117549368_0.23062160066422988", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "tiny<PERSON>y", "version": "0.3.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.3.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "e4a9c428e30463b3d147cc0217806b4238d8bb00", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.3.1.tgz", "fileCount": 4, "integrity": "sha512-XXD1NYn5HciffxgxlHmOzq45tTNKRtPHss5go1ZAQmX9LLBIszXLG+NEpnD0shS2F2OMcQ9VSk7IswEeViCrbw==", "signatures": [{"sig": "MEYCIQCKFxJPT9L+wqExCkWTSeFzQQYU+rSMUwO2a2w+CRpuhAIhAMGNC86DPBwrHQcLZnWyvgjxH8KVhQvmFl5amrDpkFvT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSv5fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojDA//XCcgwuWtyH54CJ54or/xRaxQF2jGh5oHu8P1vMDDr+hz3Ya/\r\n7j8RbzX9aihz8CZ39BOw+9uP3/dERGcY5aJSiJ4ICAYorYLcpSxZWCFEwiea\r\nfYkshqi6e8Ut7Mvy27eoiQxrBFPshK97IwvZ3hSZTHUE/KNpZcsjdbGFdkOS\r\ns1hOh/fCLvJoaXEX8XpX6JozKy48ndfV2X81IZuC4ylfQo61QkZj0eOd6hPj\r\ndv6I3S9DJbZczS+Nj9RXCH9ouFSdLQaSKCenM32nfdP19fJw1iKMcZVMPBlj\r\nCNq+RphNXfXqR+EZaOB5VSBpYRpWcgX1ihw3TJgWQ0ryvdB1b94OVz8xJdku\r\nRdU0A6nOv0bXCzR1F7ISeL2Nt9s0hRQ2+A2HFLoWJwlMyOR4lVHoZ4ErqIwf\r\nL8GESgRggHDrxpXxrWMieJHJi+F7zY6StR7xFJ1HMxE3Dbfm7M5IbMLBZQKF\r\nk1hVKeeee2VM88SVPtffIqh3HGxZX1a/CosxL2A6f5qhjxpQ8frhnXbAXcDU\r\nWVo80X7ru6dcTV+QiC7WXPpaSw+DOpY24jKUbboD9olkk/HS4uJbQ2YF4x7J\r\nBeb7GZHrVpB7RIIMS3JslglyHeaTAiJwcEq3y7i6r7xDTHGQRe94SE6k1hXZ\r\nobEX1JUM1OwUqNdeoarQutsDuWGR0OeRbv8=\r\n=07Kw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "469e92a649c9247df640b29bcdc76f4d230ac316", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.3.1_1649081950929_0.8122037947128222", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "tiny<PERSON>y", "version": "0.3.2", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.3.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "2f95cb14c38089ca690385f339781cd35faae566", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.3.2.tgz", "fileCount": 4, "integrity": "sha512-2+40EP4D3sFYy42UkgkFFB+kiX2Tg3URG/lVvAZFfLxgGpnWl5qQJuBw1gaLttq8UOS+2p3C0WrhJnQigLTT2Q==", "signatures": [{"sig": "MEQCIEYIew9z7ELTDJWDLeDF8HmYjwnaaN8tOaMo6esKLjSVAiBTghApDBotXJ70LCL667ifbsMfUsNa/i8CUsM60MA8aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiToTDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrg8xAAjYefOG+EqMxmak2nqie4ZiIMcYcKz9kpxKGKpa6kuJnFTr3k\r\niVopOGzZoZ1ZBqs1UHaysk8N0WrFCFYR/YROhAoiMw2jl7TwB6ZnhKaUZRTP\r\ne6qECfN8oVUhELktDbogX/ckuYrkhyQxqVQ7xXsetJdy7QvYqhPitg8UN2de\r\n5NtdMHFMTf0Ga1EdOgEIpfl3JEKcU/1zfctmZ3+9Ui92eqUEyz2MYJFTBvdX\r\nh1+k6u12nwtO0yDbUNi7N9XZe0AQfNFp5nAViLtvQFRHdeSrxBTcWAhOw/uK\r\nUAze9fHG+REzXCZi1zf5mXA+Yi9p9LoJBLxh2FRHne7AbSyYt9d6cDN7DfJb\r\ncKbjr9IWLk93jhkpzP+CefV4wPJ33M1Xd1OozYD7IrxKWQhr0IqxX/TQm1hJ\r\nP62f/AMw/N0wJkG1HYmVpK1CgGYGHxHNbc6kti7qJetbRPbof48MY+VIv55l\r\n1lIkPvg70NxWIvBrgf3rsSfsDKlkWSOgOdDIMQ/Gso7eJianvroQ7pCzLxfJ\r\nXRcOpVO6qKiU1poi6UnXcR41PB3Y43Qhz5TtTTO9zMQLoLRVuyAXZ+nmNrMH\r\n96Klzjcjf8jai8v9JfWPz7rpAeyIsk3apliLvi67rD0Av6QMdnUTxnfgdH7z\r\n8tFzJYB0k3hirhaYlwqCxo+teT/INT1F/mk=\r\n=Oqlh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "e812c39e0ea9af7187fd7c4c47ef8c822b640fbe", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.3.2_1649312963644_0.13564169480957577", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "tiny<PERSON>y", "version": "0.3.3", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@0.3.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinyspy#readme", "bugs": {"url": "https://github.com/aslemammad/tinyspy/issues"}, "dist": {"shasum": "8b57f8aec7fe1bf583a3a49cb9ab30c742f69237", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-0.3.3.tgz", "fileCount": 4, "integrity": "sha512-gRiUR8fuhUf0W9lzojPf1N1euJYA30ISebSfgca8z76FOvXtVXqd5ojEIaKLWbDQhAaC3ibxZIjqbyi4ybjcTw==", "signatures": [{"sig": "MEQCIGg8sakt1i/HXMwOugvGRJ6MfFDa6255bWf7jqBHhVM3AiARXTBqNbualYs2TTdFywq5Onm+6Sv3DMT1MlTr07Xf7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipu2UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqdRAAkhOU/Jj+6fRsgnbLRLpdldRMHtthruZD03G71YswIZlvLCKK\r\nSIEsG+ko2uZsCTcMpNghdPPwhpdslWX9RZqDbeL+KVT0yfrfTaOEOUvCsCEp\r\nqKYAKyDcJIkJ95TPXQuaLvpOVvLQT53CevyQhRBdzcZjqmTBOlDGTlGNyGnW\r\niaG8b6OEuv9V9nRf2zQiJBrEtvPuAgroxfPriQR5Hkn+hHT5tPDQ/6rqTjJC\r\nNQgDUlIzEUlI348ee1+g0maZWFH4d/2AHxGgyNa6VeOeh3yZjKD5Y+tvnd0q\r\nOaYY14B55T0y9TkGcjxJwO3eMs+M7wpWRxgubU6F0bTiBgv2AuRiKRQyE3yN\r\nrW4fBeiZGbz+Z569vzCEApiYXpTXsUIuW4142PiTn1q7IZ6aKNLB+YhNde1K\r\nxQQUG2TMGrtnASIbBE5oYbP1dh6r7b6qM/WwfwQWbfxUdZ21WNGSE+fXpU6M\r\nDH8XAnL1yNTypv+f4VpzQRGgApZi+DT4QmhRSwZuK2TScA654qu4z12zdXeR\r\nYTm2LfJkhpRaVTDNhx8EPBjRIF8Q1iFZanCa/3ibAGkGQ/qmUiZ54aDyLDL0\r\nVHxJnw+/v+RzBVYr1Q7Y2LLxPEKQHktOHt+cgJSiuXYXpkTktwAVq9yys3Mn\r\nVlRtX4t2Gld3ktX/PblPz4wLPS6bPxl8BFM=\r\n=828Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "b4a2cd1954c9dc09cf26661d8f8cd03050c58c59", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinyspy.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_0.3.3_1655106964571_0.9574121921362919", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "tiny<PERSON>y", "version": "1.0.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@1.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "0cb34587287b0432b33fe36a9bd945fe22b1eb89", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-FI5B2QdODQYDRjfuLF+OrJ8bjWRMCXokQPcwKm0W3IzcbUmBNv536cQc7eXGoAuXphZwgx1DFbqImwzz08Fnhw==", "signatures": [{"sig": "MEYCIQC2KKyXg+ZYpO7MmxNY0zViQo7J5PKWRGesvDShyTazigIhAIzrymG/ugnjAvutPwNJtWkPHV8xxR/ZDW4A1973wYpM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirE8jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXDA//e+MNKhy2qz57kS98YYLWvYcfNeU1NOVlUe25OnYsUvl7saQG\r\nlXU/Cro1/H0qg+ttupobxpjYxYOiFm2WCjuO4ulmjLqQp8u2a9/A2CcuCU17\r\n7wHwiG6xFrDAaqzGVTHrOBkC+qN7sYtpzkljXMNWMTqlDtFiDLHwE1bBRk9T\r\nMCQ6CikQyqHKxcbc9orpNYftY3cazhVspnyY/mn77DPcmw1zdS++b17nY1e6\r\n3yZNQcXrZUmEaIENt21et70iIV3BNANLgLQK0unnuy6hUxYykfAXT4wbNMaW\r\naoXbV7eGthlKq020JTzfq/zzIKIhSKVmD+sIvHzLGwhIERLaa9KtoCt0EVlm\r\nSpZ2Rf7V6QSfUWqN6jMdQQTnsJgNYf/6lKqn0T0nEwV0j/j/HPWph65ZmbBI\r\nnB4ofKtsujHfQSTRRYOYnVr4N0rwdPm6WY2PSURDHnTzXFTJJ8eGZysjODN2\r\nh2zHwvNXM4FT4xeqxJw8cWC6BoBFCeoOWVrrlJDfv9vEPNJ+P2d0K2CNk11t\r\nj40CafmaceWi80DkTnhftplafhssMczwAkvCBFe3VvnO0Kte9uAE3vss9nO0\r\ncv9Dtza26otWrFavDwU/DTtoaDop1gyK838jS2IGbPQBHL7EyBpadN0ev3gD\r\n/EHBBZVEvqWG3a1C6wFDRS6QTCCUxs7+8Gs=\r\n=1Pm3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "458483d498b34f37031a027a263dbaad46bbddc6", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_1.0.0_1655459619371_0.1349226171288327", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "tiny<PERSON>y", "version": "1.0.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@1.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "8e7c2c79cd33c321fa9fa6e63e2ac4223f9ec8c7", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-xuhU//HrMEC6mcZPKjQpusK5ZHeh9b1JpeMAkFqR3kasUEk1dGdJXWqRYh2945qq0N7ftiv1QQQLh1RSE7lS1w==", "signatures": [{"sig": "MEUCIQD0Iwa5BGV5NsuDJJGOntppnFosRJtHjDWvOporZCD9fgIgduBRqBWygeECOjPapnIrx4nP3RRsj7x2kBcBTs5SQGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/OUvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOXA//cudVg5CvGWbiTDRPgaRZLmK3shed97Y8mVvS/v7fQvaQ9MJh\r\nIjYnjep2hz0dO8wbBkxNVvo1dLwu6LVBIIakiu2sSQ6iQb0dBykf1mqMBAzj\r\nURQIpGdL4x+K2u5Mem6GMqkB+vg5oSwVBCtA0s8PhOBSzctHCxccMkxpvCah\r\nRTDu7fHz8WHx4GikKe8P1k2SaTms3YAeXshxaDyipxhPffHzTAUDXRKyxt0R\r\nCQ5TXARBL3V7WUpcG+BWNLL0kinWef1v5oLoucCH91Ue0oGTccTYLBNvLtGa\r\nagp3970yyBbtsUFIBcWu0oHtnZnDOtR0UPkkc4Oe4AEdhWo3I76PxOxITvMm\r\nWtFiyVMCOP91t5xYitb3MeX+LdGr32wzvQs5G33ZRNY1zUIz6Aq8Jh3mS+cn\r\n74Ke9E/gzv65mcyNTAE4bFaUVTYwYxkJx3AVYVTe6DUSb4rJ6LQz/FcPQFSs\r\nkfzRXs3eBDvnJ7172jblhY5pBpNWnTE+hCHNyjbPcLxz3rUv2ZjR2CtsX8Bx\r\nMFOZSRSziIcgtLRdjACBAREmjjUXHOZyu7aqxzGaQ1bz16uQpxTsUEEHL7W4\r\nq129YkUmXTE7yDokMlSGtQJ9dbtRvsTwRsOaChv2Kj5YB4hI3mmv29r97zUn\r\noIxqwhA1kX9r5fwPPF5Mn4voQVbSaoJfCmg=\r\n=YMuK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "3ce4c2e06f82e3ab6dcecc82ec28f4301c14700b", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_1.0.1_1660740911663_0.20503589511721865", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "tiny<PERSON>y", "version": "1.0.2", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@1.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "6da0b3918bfd56170fb3cd3a2b5ef832ee1dff0d", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-bSGlgwLBYf7PnUsQ6WOc6SJ3pGOcd+d8AA6EUnLDDM0kWEstC1JIlSZA3UNliDXhd9ABoS7hiRBDCu+XP/sf1Q==", "signatures": [{"sig": "MEYCIQCwEP6rk3rd+IaGijIZYGMBX3lo1ANCjzegO77XYGjpfwIhAO5z9HUEZ6fMfEWufU+z3UTJzojb8gxtIm9r6uNf2oAV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/R2DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7ow//TPiJYJGOiDfHLPMT9o+iAswBkwdns+q7Jmv+nNQrSmPVvXLG\r\nHXmiBrV8BEoC3s5r9d1ZXQzxFsgc+XTHWbuG/4Y4cCQ1E4U/Aoxeo3ufh9oF\r\nOslNkZMHUYrzVc2E9Pkn9Bw+at2AQx/K5QP5EnlE5aFrbUXQp2RD/SqsFBaO\r\nUsjvNrXUFs4iEt4gGEBSsuOGdyjGOa8zQHZrJ8e3NUXms4KW63NQ7eEtKI4E\r\nY/guDIf15S0WB5+ALWE/rKrs1QSBNgdxP9Dab4LMO+tLjf5qO46tZdNLrF0+\r\nwbaInZuTobZJD6+Eol4k7pjunlXg6M5oiFb+fJyYK7haP7Lo53uiwL2fY/mz\r\ndfBKnLrZwPnBItYPVTGJLwzVJOxzX2275uNGjCgicJscywsT3M9EimRRfAHN\r\nad9btrYGCjJxK9t2DytLwJAehsw9RCJ/qGmsLDjqCY9v5Mlr3euL6c9HZ+ZA\r\nGWvpzPxiCelxpOKcMtxq+T1JxvhpzX06y1PEzOQlhN7iaHBImMyo3qE33NIQ\r\niCIEJKMO39UUXipFxNaMHUL2Ac6k87pb6OtPVi8zfHiRzPIc/ZwtT2REvL3W\r\nX8HwjRmSuC7AMhmzIQi4te9lnWTqHzKA2lBIgRRZkGRPeN317maSXTOnEeah\r\no74T/sGuUmGb2sBo9SoRVFtkGpUGfJUHFEY=\r\n=M+Qg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "46054593d0e256072d49ff30e129bb33e9025a2c", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_1.0.2_1660755330929_0.6163322562072084", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "tiny<PERSON>y", "version": "1.1.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@1.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "b1312c128f4cec385f311fb1c0341fab4066b691", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-xdHzOlnGb1hxNdgO/45hp9ngVKhyN9fD1LH/3059kmHyUT/qZ+ee2y8F/BuaIQpK2r6l/kLLXwezfMpedc5Ycw==", "signatures": [{"sig": "MEYCIQCqIClx1+Aw8ouP6RpagGDPoYsSieQlzTPI4pGAnhi1pgIhAIPvYrVk8iYOYRmeDf4ozcRWRnZGdHyzP6gGptdPaVkM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4d4RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/xQ//aGfcfvAWPd/qLMTkuQ5AtvrCNLq5w43hhZWC6DkVushZkh8/\r\nT9sFBJq81juUhSCz6gACx1jWFsus3kP5MbaBNWVq7v/CupWQ5c1yymtgzV//\r\nQ3hei6xymEzulin1TtXLiaWA/Lkys5k9eRhqAErLuWBokbL5PKVacM7PMHu7\r\nxlP2HZX4MYZTJ8qBEL00BS/xHj7rrpmrkZgXE17HvBwLYj6bBU/y9U4H13fC\r\n8AFBnZ5km0YHVVH/TIzwd2zpdISOZC6g7nol5ZNEuEvRtssgxy7+1mEpLtNx\r\nOZxaP6Y5DfB+Vh2nIHQC8TBdnz7IVct8InCpWPo37jeatl57TSPhnUFMBYIf\r\nNuQUUb5YsqO/lLZwsmoM/ZFxlbpuXzehYA+VlAySb5wgx9JqbK6iexGZRgrV\r\n0r8aUKRSQtkfYhk2dFkft1NLZdGBs9bZJA6dHeNZIwvi3GUJNTJPwWnf4V7w\r\nO60XsaBENafyA1JLpVhzTLYkWfwABtZqQgtAsYBOczcCAS7pFL7KklN7mtDb\r\ntfOwkf2nSuoOhxd0rf91pSazAB1XJUGHN6C7W2PWcO4Eja45Fy2epY0z7t4u\r\n+oGMo3Fg0YlkiB3lV5YjjXYAKYtlkp0AqWiUYEhRBGdNIWO+q4vRnzaX7d02\r\nxFBTd7nKTIbwcviioOfOK/acCzs7uuKYva0=\r\n=4z3B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "1c1553c0ac8f131089c82b8459100e3499c559d6", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_1.1.0_1675746833311_0.5764378759527491", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "tiny<PERSON>y", "version": "1.1.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@1.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "0cb91d5157892af38cb2d217f5c7e8507a5bf092", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-1.1.1.tgz", "fileCount": 6, "integrity": "sha512-UVq5AXt/gQlti7oxoIg5oi/9r0WpF7DGEVwXgqWSMmyN16+e3tl5lIvTaOpJ3TAtu5xFzWccFRM4R5NaWHF+4g==", "signatures": [{"sig": "MEQCID+6OP/ksdPf6C+WujR7Hqevi68ml8Qe7O+ev83ORqL3AiA61KgZrcZcp9FqWr0p4iYKF2yCL7WhLnIKJFlSd8KHGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4mfPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmTg/9E/Fssf5wS7qbXLtc2qLkcORV9Oj8M2LK5P1jHDT7snJVuat3\r\njvDoJT4NGDSTgSwICXzGaylSc5f+jbFQc4U5Y5zfVhnX5uhU1Cu7v/pHRCDA\r\nGFbVuMyTSeDfkNrSzKJK+bCoZj1OAALZjrs3t60w/s4yqSSe4kg0v0XVNLsM\r\nXHTSq6niKrmiup13/wmpkykVDXA/ISb1baP2l1FwsJ8jLmRJ6aMCAQdlxuBh\r\nOfRju4GWaWMeYHE/2iy5MCZiqkJr4wQ26V3Vey8NT2/PoU06ysya0vXbhYGL\r\nIEUJu70hcKJLCaIrSgP/fHp6UuTbe6NkaF4Ie0D+mIV80KSitHAEKvMk2t46\r\n3BjmTNlWXioYU9hyyoNtgap8R82wTQ6GkjbAQO60ZG8eIgMx42slQci++hea\r\nLGTzd10H4uDWzc/AWXNr5nsd36EXFPeH4AEtwpYX+z0e+iNWCtuUIQBfjVgv\r\nzRY1rIk1d/Mzt/HMmXI3xJi5xMFk+Ky6d+p7Fa/ryUFi/pruoJGiYSey/FBK\r\nmGIBh0mZ6ymIZNYuT3BKpsxU15EVYd8hbmqQeV4V89BGx3gFSJY+JjtvGqap\r\nlS2Lgo+cKwNUVhbibUvinM5QDSpIryrStWQFk5V1cG8xGvGZ4Tyul4W7i9aX\r\nITaBoCjB7LT8MGrioZmg2AtO/NJ9WLd6ElA=\r\n=EA1a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "7422fef8a74e2b517bee0bb2f011dd2f9faadd5a", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_1.1.1_1675782095531_0.25188085244868264", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "tiny<PERSON>y", "version": "2.0.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@2.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "96e3a81e344fd8619290c1e02806b49b4166d4b4", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-B9wP6IgqmgNTDffygA716cr+PrW51LZc22qFs7+Aur0t73gqf3vNwwlwdcnE1AcqusK6V4R4+5jQ69nIQDiJiw==", "signatures": [{"sig": "MEQCIGdyEJTcsf6dxlt4JRMxxW3eHddbQNfaWZcV0XVVDPX9AiA8nYlj+TIx2j1D4/xXp82rmykpcuEnhD5qCIIjHOrSEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHHgQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpfew//ZvwEXXJEsTkw9R7geYebC84sVSlCWmJfALtkHdHJgMWal7Za\r\nZOarkANzv3qiMS2MCmSLmpPVHrKNK8bKcJRCOsCyFur6SIakJdQurPsdn/2O\r\nPg1P0gnrDNfY2PvIOBdPkXtbSTcMPAs2zgptFXBQT13Bj5XQY/TMba1owvUf\r\nfNSzUqMPCOZ9clAMSo+IfaR4AVZeevsf2H5y9CrUHETXKzZk9kLq51WIgE/j\r\nUfulDcJ2EyOmfa42CjiKniJSShxVXNHqFUBLFE7kLdbllm8sG8c5FKjRDDJ8\r\nJkznxJkbay/E7NG+uxX+q1t5Sle94SZ+6GJoIOehMFrPGKmBf/i/vOPhYpcR\r\n9HI2zwK66/bd4OnbM1ZYfb/Bpu9N54tEoiJNjzZCT1FHh+gltwVD75fFQi0y\r\nTOR3ws7r07Vg7iV7Onllcj5TYrRTVQaLRO5wBxCrVjeh34M9bw8uCD/9sfda\r\nzxHqO6HP+hFWWQ26BoIknoMgwNS8As3BlwvVD/JKbeh+Jf2Eu1BfKOgqrne4\r\nPdJkh+8NeBFbWnMKrHMNFx6K9PPV1+CYn472n2gESeCFSoW6FFZyeY7fABHr\r\nfPMdalYMgtIxq/HESWaZTqW8bRwCGv0qNIeBUo+t2dhTKeXZEOP7i4+62PZD\r\n5HbTFYGaoNRJ8f74cnHu0NnHomfXuDq7POQ=\r\n=pL9/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "760a1eee6e8062023dc847c2cab83ca3dac58a59", "scripts": {"publish": "pnpm run build && clean-publish"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_2.0.0_1679587344621_0.025051097812978984", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tiny<PERSON>y", "version": "2.1.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@2.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "bd6875098f988728e6456cfd5ab8cc06498ecdeb", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-2.1.0.tgz", "fileCount": 6, "integrity": "sha512-7eORpyqImoOvkQJCSkL0d0mB4NHHIFAy4b1u8PHdDa7SjGS2njzl6/lyGoZLm+eyYEtlUmFGE0rFj66SWxZgQQ==", "signatures": [{"sig": "MEUCIQD6aSIge0cwxhvKQaBWLa7hEa091Xu+vUJyumkCYPa4SgIgMuBJpfRx9tj/2T0TdMvlu1QjYDhMLMQvyDBVBw4RS5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHHyZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/RhAAi1eZnv8wGamyOLjPBVTiXvNKfVP1hHCMp2kLN85l8ODypgUc\r\nX446+pHq/+iShoUUiaHm/i/pj1FEOjM+mQ0QocA3mm7a/alRGvarNeTqBvBq\r\nJ1FHwkLIzN+MI3VfjCY5qwMi1IsPUonHyRxvT1VR7FLRAP7PVxSan7E+4jXF\r\nulX9GYuJpPBwqXv6C0YFQmPlciHPNSecHFXubFAod29k1f3l3Li5hNQ4GEYw\r\ntreacmeUGGdv++Vg4TWiYGwWIObJmOEmwKBbI6RG2/5Vvx/Pm7siF9bgJeV0\r\nC0my3g//i4DZv99kEOQawNR/ckGZIrQ1bhTg0d/mT/cwmT9jcsm8ULGR7/3Q\r\n+OjEQfjjRV+BoP4v8HvkdaryHBLaA8LUB1/OFjQOR64lfpjr2+I0D1ryLmc9\r\nvtKjoTds6i5U8hdf7I8is05Tpd76Banve4xFWIrPIrOwaiksaV8VM4c0nSFD\r\nwewDV2tV5CxTPhi1xNKHzmPPoXBFFoHqN5Vl1oyCjZkkEhUMJTso/pD6cuYO\r\no1y+4Vg9663tL021lQ+oJdkMbw72AmtUwm2Pd/uBz0q5Sq41Wx2tu3XGRijP\r\nD3Z9xGvsM7ps/FTGixnEaV6uHasfvGYzEHRHpE5I5qtJlrnVeKsO6Sa5D+mb\r\n9qvBu234GzqmrgsyC5D7TpMJhTngRwlD0Ek=\r\n=XxLB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "ffa02b42152018fdc81a22f8f42eae6575cd187e", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_2.1.0_1679588505448_0.053211290150228185", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "tiny<PERSON>y", "version": "2.1.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@2.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "9e6371b00c259e5c5b301917ca18c01d40ae558c", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-2.1.1.tgz", "fileCount": 6, "integrity": "sha512-XPJL2uSzcOyBMky6OFrusqWlzfFrXtE0hPuMgW8A2HmaqrPo4ZQHRN/V0QXN3FSjKxpsbRrFc5LI7KOwBsT1/w==", "signatures": [{"sig": "MEUCIECwNzHuzyUCK2dbjXMA3O1Yrn5AdSZ19zYA4g9Z/bg0AiEAjo8X8LMjVgGcjnx6nV6O9OqHC1vOlYdJ+se/cXRvBIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13361}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "64a143f6ca5e4382a9c7fe5ef3d3cf5fcd401ec4", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinyspy_2.1.1_1685457412713_0.20652556520688603", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "tiny<PERSON>y", "version": "2.2.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@2.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "9dc04b072746520b432f77ea2c2d17933de5d6ce", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-2.2.0.tgz", "fileCount": 6, "integrity": "sha512-d2eda04AN/cPOR89F7Xv5bK/jrQEhmcLFe6HFldoeO9AJtps+fqEnh486vnT/8y4bw38pSyxDcTCAq+Ks2aJTg==", "signatures": [{"sig": "MEUCIH8qzrt1Ddje3giMJAnlgSeA95798pB31lIN10ZfPFPoAiEAi/zdJU86FAxLgraRpaYLqeL+bO5TBRaze/T5mnuLeBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13568}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "a3e959735afac620726160db4217cc0039c0d5b0", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_2.2.0_1696244799719_0.6853618731635791", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "tiny<PERSON>y", "version": "2.2.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@2.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "117b2342f1f38a0dbdcc73a50a454883adf861d1", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-2.2.1.tgz", "fileCount": 6, "integrity": "sha512-KYad6Vy5VDWV4GH3fjpseMQ/XU2BhIYP7Vzd0LG44qRWm/Yt2WCOTicFdvmgo6gWaqooMQCawTtILVQJupKu7A==", "signatures": [{"sig": "MEQCIGXFkaz5xcpif5IHIfVsxql36LAuJIt0na/xbkPF/6+WAiA10+qpXkTe7IN1HnhUekSyu8mfOh5O+7Vscvx0qfe8jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13913}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "cd4d86683a2a62f34d9df90308039d1faa88f9f7", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_2.2.1_1707401155220_0.5412962379557875", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "tiny<PERSON>y", "version": "3.0.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "cb61644f2713cd84dee184863f4642e06ddf0585", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-q5nmENpTHgiPVd1cJDDc9cVoYN5x4vCvwT3FMilvKPKneCBZAxn2YWQjDF0UMcE9k0Cay1gBiDfTMU0g+mPMQA==", "signatures": [{"sig": "MEQCICfp9kYzV9BhASaAmdPccsa9J69i0VvGFm/ND5+JmwSkAiA2t8mmAUCYu9XBvcJaMjjZ+lH9PZ0eX7JnjKNdBLd4Vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17389}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "a550239d5dc619509101257260005339ab2c7d0e", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "> minimal fork of nanospy, with more features 🕵🏻‍♂️", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.1", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_3.0.0_1717150287825_0.711154139970883", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "tiny<PERSON>y", "version": "3.0.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "1a7817235f490e045c153230e495c8ee6924ed61", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-Zl/ko7fioZvaflejqwgnWwnTjr3K562tmyNFZADyRWYLXxkr2g+cuZ+lP+s2GRUV0PVCqqoDu0Rmx4fzdJnrZw==", "signatures": [{"sig": "MEUCIHlMx8OL7OrAvtcXVr8FWBz/CFQdZLqsXSkR2pcEIMvsAiEAqPgbsXzYJoEl6MyPC8Tj9nrjAJRz3e1bn/Sc6vb006w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17703}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "04a5e767ec02eeacd1e40f1e9ac8e5f81234d0e2", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A minimal fork of nanospy, with more features", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.1", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_3.0.1_1725867230296_0.3283624269254517", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "tiny<PERSON>y", "version": "3.0.2", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "86dd3cf3d737b15adcf17d7887c84a75201df20a", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-n1cw8k1k0x4pgA2+9XrOkFydTerNcJ1zWCO5Nn9scWHTD+5tp8dghT2x1uduQePZTZgd3Tupf+x9BxJjeJi77Q==", "signatures": [{"sig": "MEQCIAUXvF93WNnWUj3InYJaf3ujVBsku+Sv5T4HWTmrtjnAAiBMLJIzANIPGRb11DT/0aZWlhwf7/JUWmiRMyQ0IUIzTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17595}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "2650a4a2cc68e68c245f82da1fcb6351f4f8fe6c", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A minimal fork of nanospy, with more features", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.1", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_3.0.2_1725880330103_0.48599605369488197", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "tiny<PERSON>y", "version": "4.0.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@4.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "4ac333ec05dfd81fc0dd37e62cf8062025b4e4fd", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-cfaGNABIQGjWsqbj1snffI3KPwAFQfyj7mLqKQlosPgGcWKW4mXDO3/yo5+JqiL9EQkvInSot4qxbvbK2uOtRw==", "signatures": [{"sig": "MEQCIFCIrSIvhgi/co+K7c2A6dxvHu0UfarUKW16wBA+g2UJAiAw0zfn2e4Rb4hsgw3zWgm200y8B4zv52THDyu8YNibKA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9493}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "gitHead": "a129541af40224fc1dc826ef4c3671c4ff392c05", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A minimal fork of nanospy, with more features", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.1", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_4.0.0_1742328181828_0.5695037487438135", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.1": {"name": "tiny<PERSON>y", "version": "4.0.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@4.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "b6235a2be3e6bbe03f55a2b1342fa1b7f9ec65f7", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-dI+PLAVsfw9s92CdS+YGhRCqjK0RWlW/Z0Etngirsly/7gMb297mk8niOwgwbPAaiiHTGPOBW1JzP8D23XI8Mw==", "signatures": [{"sig": "MEUCIEai6dfJYrTYZKVaxqtGe0vNjkhOeHujNYeDg5qQNO5YAiEAvnP129Ajh1DPtCN2HoBb0pgO0wslmS5CAgbxzJMSwec=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11620}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "gitHead": "40f116744c33033bb526b408fb63c918912ed322", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A minimal fork of nanospy, with more features", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.1", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_4.0.1_1743517441301_0.5936597054388297", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.2": {"name": "tiny<PERSON>y", "version": "4.0.2", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinyspy@4.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/sheremet-va", "name": "Vladimir", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "7944a54a64e9d0d8d5fe21479383675642b2c4f1", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-1gniIIoDOAivEwCUNJI1Vz0y47HfxLlVJLYAi3TtDvu0PIxeV6tICaJPiZFUxc/0RoohyECxSwLHvnBDBIhDdw==", "signatures": [{"sig": "MEQCIHwYbm8Sl5aI8i5pMkyK+kAWUlcoBjQCmCIftcNCb/BoAiASgniVGJ/BVOYAAZ93npC+R5s5cDRAUicW1hSKnENkJw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12739}, "main": "./dist/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "gitHead": "f42d54522dc94b2102558172ab7c0766f1d65110", "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A minimal fork of nanospy, with more features", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.1", "_npmOperationalInternal": {"tmp": "tmp/tinyspy_4.0.2_1743610752974_0.49342115232202755", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.3": {"name": "tiny<PERSON>y", "type": "module", "version": "4.0.3", "packageManager": "pnpm@9.1.1", "description": "A minimal fork of nanospy, with more features", "license": "MIT", "homepage": "https://github.com/tinylibs/tinyspy#readme", "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinyspy.git"}, "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "keywords": ["spy", "mock", "typescript", "method"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=14.0.0"}, "_id": "tinyspy@4.0.3", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/aslemammad"}, {"name": "Vladimir", "email": "<EMAIL>", "url": "https://github.com/sheremet-va"}], "gitHead": "1571fda871c8c644e5bce39fbeddff70a4adb109", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A==", "shasum": "d1d0f0602f4c15f1aae083a34d6d0df3363b1b52", "tarball": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.3.tgz", "fileCount": 5, "unpackedSize": 13109, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDsJiNlUL2v9ffjwrkUT7NE5mOx/hu4R9iVSNps2k1kyAiBfuvdLdjtlJWlC6d15Dk9Vi7glBMkDvddgfgJYF8sUKA=="}]}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tinyspy_4.0.3_1744885296240_0.182515070719246"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-12-13T14:55:18.806Z", "modified": "2025-04-17T10:21:36.574Z", "0.0.0": "2021-12-13T14:55:18.955Z", "0.0.1": "2021-12-13T15:14:31.706Z", "0.0.2": "2021-12-13T15:29:30.417Z", "0.0.3": "2021-12-13T15:32:23.760Z", "0.0.5": "2021-12-13T15:38:04.489Z", "0.0.6": "2021-12-13T15:39:46.754Z", "0.0.7": "2021-12-14T04:52:16.353Z", "0.0.8": "2021-12-14T04:53:04.544Z", "0.0.9": "2021-12-14T05:01:34.931Z", "0.0.10": "2021-12-14T05:03:01.254Z", "0.0.11": "2021-12-14T05:03:49.393Z", "0.0.12": "2021-12-14T05:06:15.339Z", "0.0.13": "2021-12-14T05:07:08.789Z", "0.0.14": "2021-12-14T14:18:30.506Z", "0.0.15": "2021-12-15T15:47:17.735Z", "0.1.0": "2021-12-15T16:27:44.550Z", "0.1.2": "2021-12-16T11:58:37.368Z", "0.2.0": "2021-12-20T05:03:32.067Z", "0.2.1": "2021-12-20T09:05:20.375Z", "0.2.2": "2021-12-20T13:04:57.914Z", "0.2.3": "2021-12-20T13:35:22.472Z", "0.2.4": "2021-12-21T04:44:19.319Z", "0.2.5": "2021-12-23T07:57:33.293Z", "0.2.6": "2021-12-23T09:27:57.604Z", "0.2.7": "2022-01-04T10:57:29.874Z", "0.2.8": "2022-01-07T07:13:25.953Z", "0.2.9": "2022-01-27T17:42:59.370Z", "0.2.10": "2022-01-27T17:45:42.754Z", "0.3.0": "2022-02-17T17:05:49.530Z", "0.3.1": "2022-04-04T14:19:11.117Z", "0.3.2": "2022-04-07T06:29:23.794Z", "0.3.3": "2022-06-13T07:56:04.722Z", "1.0.0": "2022-06-17T09:53:39.534Z", "1.0.1": "2022-08-17T12:55:11.900Z", "1.0.2": "2022-08-17T16:55:31.665Z", "1.1.0": "2023-02-07T05:13:53.474Z", "1.1.1": "2023-02-07T15:01:35.665Z", "2.0.0": "2023-03-23T16:02:24.786Z", "2.1.0": "2023-03-23T16:21:45.616Z", "2.1.1": "2023-05-30T14:36:53.024Z", "2.2.0": "2023-10-02T11:06:39.881Z", "2.2.1": "2024-02-08T14:05:55.502Z", "3.0.0": "2024-05-31T10:11:27.962Z", "3.0.1": "2024-09-09T07:33:50.428Z", "3.0.2": "2024-09-09T11:12:10.264Z", "4.0.0": "2025-03-18T20:03:02.031Z", "4.0.1": "2025-04-01T14:24:01.548Z", "4.0.2": "2025-04-02T16:19:13.142Z", "4.0.3": "2025-04-17T10:21:36.408Z"}, "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "license": "MIT", "homepage": "https://github.com/tinylibs/tinyspy#readme", "keywords": ["spy", "mock", "typescript", "method"], "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinyspy.git"}, "description": "A minimal fork of nanospy, with more features", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/aslemammad"}, {"name": "Vladimir", "email": "<EMAIL>", "url": "https://github.com/sheremet-va"}], "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}], "readme": "# tinyspy\n\n> minimal fork of nanospy, with more features 🕵🏻‍♂️\n\nA `10KB` package for minimal and easy testing with no dependencies.\nThis package was created for having a tiny spy library to use in `vitest`, but it can also be used in `jest` and other test environments.\n\n_In case you need more tiny libraries like tinypool or tinyspy, please consider submitting an [RFC](https://github.com/tinylibs/rfcs)_\n\n## Docs\nRead full docs **[here](https://github.com/tinylibs/tinyspy#readme)**.\n", "readmeFilename": "README.md"}