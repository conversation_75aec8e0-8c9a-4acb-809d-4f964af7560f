{"_id": "jest-regex-util", "_rev": "158-f069c2a4b4942e190079261cc8a45a5f", "name": "jest-regex-util", "dist-tags": {"next": "30.0.0-beta.6", "latest": "30.0.1"}, "versions": {"0.0.0": {"name": "jest-regex-util", "version": "0.0.0", "_id": "jest-regex-util@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "b2d0f0aba87503710701c6ee58cc521196fd5b21", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-0.0.0.tgz", "integrity": "sha512-BzWPDOzNxY4KQZejNaE8DsTLUg4Cr4b9vHbtYZ1ZmM67DepR57sDAqe1mfbZ51AnndqmI1dn1lPTY3sW5yadDA==", "signatures": [{"sig": "MEQCIHCnrBdJWBazeNZNAPbI84bNAwJHbfdMoo6xPbjxzDLpAiBG63T/I4dNmFDm6S6YV6TjoPsdZBpwe/PqQw6NSJ6AXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b2d0f0aba87503710701c6ee58cc521196fd5b21", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "4.0.5", "directories": {}, "_nodeVersion": "7.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-0.0.0.tgz_1486488260067_0.7402346867602319", "host": "packages-18-east.internal.npmjs.com"}}, "18.5.0-alpha.7da3df39": {"name": "jest-regex-util", "version": "18.5.0-alpha.7da3df39", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@18.5.0-alpha.7da3df39", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af94fd65b07d8564119552510dd70115360784ce", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-18.5.0-alpha.7da3df39.tgz", "integrity": "sha512-8//3OyXj9b7Vjbc0N8qQQVAz2bkRtnpVwwGO3BfT+qcpNeXl5Nt0Hf+Fk0PMx/u/X/+n97t/U3N/44Y507+x4Q==", "signatures": [{"sig": "MEQCICGhARcyqC0tgwdZw9LQYHjvKLuJgtSwtwuuhnAaOrPJAiA4O9Z6Zz26HNdFaZJsIf8aEEhFqb3ZfK0h7JUfGNV8ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "af94fd65b07d8564119552510dd70115360784ce", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-18.5.0-alpha.7da3df39.tgz_1487350666996_0.26044164621271193", "host": "packages-12-west.internal.npmjs.com"}}, "19.0.0": {"name": "jest-regex-util", "version": "19.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@19.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b7754587112aede1456510bb1f6afe74ef598691", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-19.0.0.tgz", "integrity": "sha512-88kDFudJMZFqnwniB5Z9ujW715SFHDQU1xOfRk5DI8daOLgHOdWYKPnW83OIqMG64YAN4LpDsLwFzhVLDnatNw==", "signatures": [{"sig": "MEYCIQCUfzfktz6hkdNdEoY5357f216lCPzldJ+ABZOFFhR0MQIhAObPTKibm6i9PmTrxjmM/UI5MTnKsmW+5M4z+cRDlctN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "b7754587112aede1456510bb1f6afe74ef598691", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-19.0.0.tgz_1487669422733_0.2394241455476731", "host": "packages-18-east.internal.npmjs.com"}}, "19.1.0-alpha.eed82034": {"name": "jest-regex-util", "version": "19.1.0-alpha.eed82034", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@19.1.0-alpha.eed82034", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cb4316945b581e6a0c8674d4a320494b7d128196", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-19.1.0-alpha.eed82034.tgz", "integrity": "sha512-lRaBo50K8r8d0cqQAs9fRpuzJX40aCyxgCOW0XiI71nkYkx1FxheEcEwSABtvv98xwBvlvHxnbtD02TYg0dRWA==", "signatures": [{"sig": "MEQCIEm7wWEoSHUy9T4QIgs+SmyN019nslpOg576yLfdzRSqAiBYrk7ZtgOzi5oP/Aa/eoCbAAWfslM7V+s/sxA1ELtjGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "cb4316945b581e6a0c8674d4a320494b7d128196", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "directories": {}, "_nodeVersion": "7.7.2", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-19.1.0-alpha.eed82034.tgz_1489711281016_0.22532766521908343", "host": "packages-18-east.internal.npmjs.com"}}, "19.2.0-alpha.993e64af": {"name": "jest-regex-util", "version": "19.2.0-alpha.993e64af", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@19.2.0-alpha.993e64af", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0944561f7219a3b8e5884e86e753f0dc7e7cdbcc", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-19.2.0-alpha.993e64af.tgz", "integrity": "sha512-hnYGpcgUrxav41mn5gNnKpLVnr6RRvTtIgFjBhVaBOD9Falt48ExrLzdpoP4VgMrvK77j34gOc+ov1L7mTPZ9A==", "signatures": [{"sig": "MEUCIDrk7cljYgki7m7MiNDcVIk4ngC/o7TA+21n/KjsnCrIAiEA2UI3El1eNbm20FgkPUBz26D9PROBUKRmCpXYQ/s+mws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "0944561f7219a3b8e5884e86e753f0dc7e7cdbcc", "browser": "build-es5/index.js", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-19.2.0-alpha.993e64af.tgz_1493912258087_0.7172435875982046", "host": "packages-18-east.internal.npmjs.com"}}, "19.3.0-alpha.85402254": {"name": "jest-regex-util", "version": "19.3.0-alpha.85402254", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@19.3.0-alpha.85402254", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d332bc880157d9d87d0155f62cbfabe095b4aa9c", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-19.3.0-alpha.85402254.tgz", "integrity": "sha512-SGoKMXjRSDxlCPNYaskTJOHUfttn9dGJdZk1Cjio3RnkmdfCdEyYbx+hJs7YCkELvNr+jGcavB6A4KzrIGGItw==", "signatures": [{"sig": "MEUCIQDZHEP4ALrdgJ0LuvmbPsn50ZJ69HGbpV3BuhGJFVly0gIgXJaDPInoqcQK4awHzY++khOBbKKnFFvo5+NO+TpUSvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "d332bc880157d9d87d0155f62cbfabe095b4aa9c", "browser": "build-es5/index.js", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-19.3.0-alpha.85402254.tgz_1493984899092_0.5938792044762522", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.0": {"name": "jest-regex-util", "version": "20.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7f6051e9d00fdcccca52bade50aabdd9919bcfd2", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.0.0.tgz", "integrity": "sha512-dnXgM/A1Qxi53SbHxNIjgATM/1/+TW2GINOMqpo6EGti8mUQk7PgDpJLfRk0q3oOiVRchUtPO1lzAePhXXtG2A==", "signatures": [{"sig": "MEUCIHCa0SVlEJIFDfv7HiIxSeSDNAtrNenbi6CIhftSZsdVAiEAizfvhirQ3/JdqP0FNL+A4musUBuI930b+g3ezjxbt2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "7f6051e9d00fdcccca52bade50aabdd9919bcfd2", "browser": "build-es5/index.js", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.0.0.tgz_1494073951297_0.8711710933130234", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.1": {"name": "jest-regex-util", "version": "20.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ecbcca8fbe4e217bca7f6f42a9b831d051224dc4", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.0.1.tgz", "integrity": "sha512-We70Kwa/xGTUU5eZI9tU2LQNnMWhS8ElM/8amdtS2R6Pk1K4ptWoBmj8tTg0ElHaC17hZyZTa+K1B09s4RMM/g==", "signatures": [{"sig": "MEYCIQDRzcaq9N0UPLcchO92rc6g0PfHaoMes/AYVrv2Gxi35wIhAJxfIa+No2OIHjVjS/Q7bfC0l95f1+QNAqUOlpxFONZU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "ecbcca8fbe4e217bca7f6f42a9b831d051224dc4", "browser": "build-es5/index.js", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.0.1.tgz_1494499804153_0.2087879532482475", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.2": {"name": "jest-regex-util", "version": "20.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "678362548e63b171f120cb6c9c4dc608ac8b8004", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.0.2.tgz", "integrity": "sha512-/ole30uzN7kJFuyAdfitu/MLlpjnUL/MjtRhdDF98m6tfizhcTJFa2KEMY37ykiP4mCPuxJ5apa6Cqw9a6qxrw==", "signatures": [{"sig": "MEYCIQC2tO4jdNqtPcWJKEnhyt9GNHw7XwHNzyUXFm4q66mPnAIhAJtqPHOSmM12W9SbiwzNgxyIBPGbvWLHoYSKnyHjo00v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "678362548e63b171f120cb6c9c4dc608ac8b8004", "browser": "build-es5/index.js", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.0.2.tgz_1495018217920_0.13842965569347143", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.3": {"name": "jest-regex-util", "version": "20.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "85bbab5d133e44625b19faf8c6aa5122d085d762", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.0.3.tgz", "integrity": "sha512-WVFSnROOYgYA+AyTytpZA93EEv16DfPkkR8V8okVQjirXLfRs9n451BPgiiUJSHIyJv+OQ4El0+q16hyY1dEdA==", "signatures": [{"sig": "MEUCIBrAALWxrRBzHkG/PMVplXcTplN7qjYbnlQMtYSzeWq0AiEA0r0riSp2pkhMDtElQ7q+Mh2viXWsXUVlYdwKTySvGoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "85bbab5d133e44625b19faf8c6aa5122d085d762", "browser": "build-es5/index.js", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.0.3.tgz_1495018628069_0.23686859128065407", "host": "packages-18-east.internal.npmjs.com"}}, "20.1.0-alpha.1": {"name": "jest-regex-util", "version": "20.1.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9b16caf8ee644b9f0d82a856108011a8e26bbe86", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-alpha.1.tgz", "integrity": "sha512-h03Q+N+KY+ZV8Pq38bNiKza3FbrRJgUMi63z7aKXXrTOG3gyf+uuyYGAQ+uIfifgFvwjAOWiXd7/1uHZf2WM7w==", "signatures": [{"sig": "MEUCIDvHAHC6aDHQe/PDViX/UiAe3rrzv5LJWihtZB8WlVutAiEAzC4owHB+9tj4mQBOPLU65bWf+WtaOOwY/PIfYeLpHXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-alpha.1.tgz_1498644979658_0.7882064499426633", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.2": {"name": "jest-regex-util", "version": "20.1.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "36e02193e4f1ae8acd58cafda0e8b5aa7f92716b", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-alpha.2.tgz", "integrity": "sha512-fgCkD8P/FbjU7inVB796pBXNJ9dsbDEtofeBOuXvKr87lDl2e9QM/z6neUdJ8nTcDaP8rKIpyCmnkKkJcGqifQ==", "signatures": [{"sig": "MEYCIQDWCBbfvbsycdX7p987oEIJD+L7BHofCsXC3+Wgqy93BwIhAI8VjIbgOkj3BP0kZTTkBgnruRCzD+p9InF2JSZ4qwA/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-alpha.2.tgz_1498754206383_0.7606661713216454", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.3": {"name": "jest-regex-util", "version": "20.1.0-alpha.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8147485a4a26b90d936152c92ac8b51da0cbf57d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-alpha.3.tgz", "integrity": "sha512-McJMmtvIrxG3ebal6/4uG7p0gJPW6YfTQhYefFDoSozleaVBRBoQHYco2fq0IYSLSgHWLp/EdXa2mWTxac0H7Q==", "signatures": [{"sig": "MEUCIHKEiBcY6BcNWaDlFtRMqbuAVJhVwcmMRvQp4Ykil3ioAiEA0MoQa6jr11L0VAbMGoaYw5ygW1xjMiANAC795kHKbRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-alpha.3.tgz_1498832452766_0.925757659599185", "host": "s3://npm-registry-packages"}}, "20.1.0-beta.1": {"name": "jest-regex-util", "version": "20.1.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0924d6cc1e75a78127ab3708533d6c8e1e3e672c", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-beta.1.tgz", "integrity": "sha512-kWzoQvink38eh5Lcn/Low6H/zB8J3GObi4MQ+SA2RF090aFUiKQbl+aob94VIHIRUvSed6/qx2Vt34iF8HFzbQ==", "signatures": [{"sig": "MEUCIBdg/GqthZ91BPEinYc9afhNwsa1q1tQFx1cTP3tmQFMAiEA2sg66e+dJoNIQI1NDCk6nfHPv/TgMS0QC5WTcUh+Mt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-beta.1.tgz_1499942021409_0.3729263723362237", "host": "s3://npm-registry-packages"}}, "20.1.0-chi.1": {"name": "jest-regex-util", "version": "20.1.0-chi.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-chi.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e28f24453b60f4e6846470ecffc6f7c2bc63e97e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-chi.1.tgz", "integrity": "sha512-96dsqPPP24XcWFyTTtu5JF28wta6vT/gKhkfGUP/43z9MJi/SM79tfhz8TVn2m4pdLI6nEkqEqjFgtxhdew7sQ==", "signatures": [{"sig": "MEYCIQCyEmteSHhuasJFYsXCIbVbrXBP0s6bvaMvXGb3GX6ApwIhAOzmOspPUlFNvRy6n5/OuKV463WoR6AzYkKJFMbgs+vM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-chi.1.tgz_1500027903378_0.872201653663069", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.1": {"name": "jest-regex-util", "version": "20.1.0-delta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-delta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0aaea967cdfc6cfe8fa828a11235679cf0eb2312", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-delta.1.tgz", "integrity": "sha512-XtU+TmmmEN9u8EsK2f4uTNfWuAZqKdDtmQEnkMzt2qjLGQBUQja16ZFwReyKxF9SrAZpB8CzLpKsDc9Zqi74jA==", "signatures": [{"sig": "MEUCIAOwIGzfWHwhQm19c3wiBtzfvizBEBaKzH4ySC1CAFCaAiEA8Jdu8mtO41TRxS0UtSCOh7v2fOQpmCoJjrZofnc+wOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-delta.1.tgz_1500367613183_0.7945801054593176", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.2": {"name": "jest-regex-util", "version": "20.1.0-delta.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-delta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7141620d4742ca8c90f567f90de75ff2ea35ad7e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-delta.2.tgz", "integrity": "sha512-aWAJ0WsqUgqvqODwQtxR7pQVRzpbb2b6FEKZwyysRwYZuRTy3Y0pKBoz8xap1rFePtthu7SFZSD85U6Aa0c0dA==", "signatures": [{"sig": "MEQCIGfWeSPTrDoQ5xC8SNpwMsgGptshQNPgdLNUFmcUXGjeAiAU8AAE7OgxFeQ5RvHbJ7gIesvcPwOyA0UNGZ9WQGLrdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-delta.2.tgz_1500469002643_0.9308556222822517", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.3": {"name": "jest-regex-util", "version": "20.1.0-delta.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-delta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5f0708ee9152050b8fe579c04c6b76f500149e47", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-delta.3.tgz", "integrity": "sha512-5PXlcwSq4oRTDmE3gxOlWBsjg4D0cIO2xuo0AdArIbrpZnXETn22F7tCxUqiEiV5/kAa88GiN6M06VpM2Yt0yQ==", "signatures": [{"sig": "MEQCIAL5gncnlq0meUIjCvWYAWAJLxrm5IAdc4c4pU5cHVN6AiBJk/7XZBaER3UyoGOWqq/rthcxaJGBzi1d7jbJaA78ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-delta.3.tgz_1501020744112_0.16160052991472185", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.4": {"name": "jest-regex-util", "version": "20.1.0-delta.4", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-delta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "198d64c257a32965c1819c04137dc1ffec93bcb3", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-delta.4.tgz", "integrity": "sha512-iDVGeiJgdU7pFAadqdiabx0YopSUbTkblNQlgUmO7kHGGA2+9p2JoLcZHgjrVkpPd2JTF5yDZF0+a2ilsDe6FA==", "signatures": [{"sig": "MEUCIHKZlFWp0+p8qEq9Mce6l6lILRtDOhslhQ4xheMG8VV4AiEAmGXzJWGaTyMoJd8uxdLg6SaDKmyOjv2YXYC+1UJjmQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-delta.4.tgz_1501175947770_0.7398725193925202", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.5": {"name": "jest-regex-util", "version": "20.1.0-delta.5", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-delta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad30d52b16e505c793a16ca73aa37e4852b24858", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-delta.5.tgz", "integrity": "sha512-HXTrkGI5VwjgLOg6F+5uD0tqlTTm1V5HUmOv3ngaHHOv13GRxIrHtCnM2nhGAVD4HmxmwlgnYlFuYYadlWV9+w==", "signatures": [{"sig": "MEQCIBf6fyZQRn7avreUoo9kVY/F4h7nav57Lnuba2uajPtUAiBYKtc4E2unxmKHbpnFsSNxPrxcr9JORVlmMwJoU29OoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-delta.5.tgz_1501605216406_0.5112762942444533", "host": "s3://npm-registry-packages"}}, "20.1.0-echo.1": {"name": "jest-regex-util", "version": "20.1.0-echo.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@20.1.0-echo.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a5cf0e74eb59578ae90a21a2db72870996bb7db5", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-20.1.0-echo.1.tgz", "integrity": "sha512-huY38hIjh+8X1hgmJ5n7nQu5gfn/AcPERzy+nFE/oL/o6Zq7kNeu4bij/Nv55CdhQPdBH2Mqo12KCD17rDwpXQ==", "signatures": [{"sig": "MEUCIQCu4ZwpDzOVPVSJuSaPKwsAqfJmXdIiYt1bQc3XBIRWIwIgQI3ecSDnnrKNywtq+EfBNHfSq3NRsJ6HX8YNAuz0gsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-20.1.0-echo.1.tgz_1502210993241_0.6444149648305029", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.1": {"name": "jest-regex-util", "version": "21.0.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@21.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "77297806005bbb140d1aa00d48780fd314acaf47", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.0.0-alpha.1.tgz", "integrity": "sha512-GPN1JMV437WDJzd5AeGQruF+u9znouJ4z7pKcCGtwhovfs5+JpaxO2kWFEmoaeuXUwLs6D+FW/s0nTxQu74ryQ==", "signatures": [{"sig": "MEUCIBzcYo4yemnueKaVmuIbw8gTt325mhwQVy3lb6jxVLmQAiEAjxAsQ28Jk+EfBWfVxi4RrrxJzBhT3v1MUn9GTdms2TA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.0.0-alpha.1.tgz_1502446444206_0.35226996429264545", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.2": {"name": "jest-regex-util", "version": "21.0.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@21.0.0-alpha.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a7c29037b557774d523a1749637337924a698b3a", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.0.0-alpha.2.tgz", "integrity": "sha512-JfPD5T0X4dVHhvO/54zambXgToOjitipNtsEpOZgXXXU51CogcjK+fD93hLBWJbIq/nQH9uEy3DUciNRxvRSDQ==", "signatures": [{"sig": "MEQCIFtZzK15oyxKYkIJywe4oiCYEbd1FXWywJENTB+w30BlAiAZcz3WEwm8G8s3LlSwy8X0LI7Whj+B6+fDlNPjwRMgXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.0.0-alpha.2.tgz_1503353208311_0.5086642398964614", "host": "s3://npm-registry-packages"}}, "21.0.0-beta.1": {"name": "jest-regex-util", "version": "21.0.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@21.0.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a6ab0293adb55fff23f59e801a94f0166dc94f9", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.0.0-beta.1.tgz", "integrity": "sha512-sHGIOqIaG0oljQV6fWFBNoWOacHFa+cbM9srC+pBFq9NU+dFMBc+C1SOqM9GTQDmmKp//Cdl9aVUVUlw2cfGnw==", "signatures": [{"sig": "MEUCIAvu6PCh93pq4kfeQofTUja1bm5RoL+Z/DoA0+Q7FbJCAiEAqi5QQGhWCzjHjp5kbewKeax3Vv2Cz6FXBQcX6F3Evfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.0.0-beta.1.tgz_1503610005912_0.4121958401519805", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "jest-regex-util", "version": "21.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@21.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f13c382a1c55515c20471390ab38e5d71cbd320e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.0.0.tgz", "integrity": "sha512-4xVhz33XZpeoNCplooEBc0PdOW7BCydT8wqvGlgxEzteUWXvctFdvMwzqdUv7ZjCN3QRMZ04SVruHlkMsAH6Eg==", "signatures": [{"sig": "MEUCIQDiOZWbpRZkY+wqNIXpGiT5PH0xV/EiXZdeYd7FyMMacQIga5F17g8uw/3o+LmojViFu47f26EYhMw3KFlAsfoA4+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.0.0.tgz_1504537310294_0.11215380416251719", "host": "s3://npm-registry-packages"}}, "21.0.2": {"name": "jest-regex-util", "version": "21.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@21.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06248c07b53ff444223ebe8e33a25bc051ac976f", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.0.2.tgz", "integrity": "sha512-KmUZvbvislZZH7FHAhJ9PFMVcjSoso2fb+ClmJWd9oxrXiNMYmNsrMNbqQ/vyIQfCObR3n+ZtZgECS/8QJnEMw==", "signatures": [{"sig": "MEQCIGcSPJ6RWseglS1yHTc6Q5h43t1jaJXfvvnZeYAYrMsAAiBlJ4IclazO2wl/YcpgWPAhWBBy341j1zyQi/1tTmexnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.0.2.tgz_1504880355162_0.11583812301978469", "host": "s3://npm-registry-packages"}}, "21.1.0": {"name": "jest-regex-util", "version": "21.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-regex-util@21.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "59e4bad74f5ffd62a3835225f9bc1ee3796b5adb", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.1.0.tgz", "integrity": "sha512-1QiJa6nvdzVgDhY1FTG3fl+2eYrCQGQoIeGaVPjt9+VmxsQ/BwZD6aglH0z6ZK/uEXZPAaj1XaemKM9tC6VVlQ==", "signatures": [{"sig": "MEUCIEE/6KZAaI9tX5MwUxgxWaNaRHpCqrx07pmtUMnfh9+2AiEAos9WyYgZAtRTcV4gnIrC6jmzx2vILQ0WF5jXE4vA+64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.1.0.tgz_1505353808620_0.8099632242228836", "host": "s3://npm-registry-packages"}}, "21.2.0": {"name": "jest-regex-util", "version": "21.2.0", "license": "MIT", "_id": "jest-regex-util@21.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b1e33e63143babc3e0f2e6c9b5ba1eb34b2d530", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.2.0.tgz", "integrity": "sha512-BKQ1F83EQy0d9Jen/mcVX7D+lUt2tthhK/2gDWRgLDJRNOdRgSp1iVqFxP8EN1ARuypvDflRfPzYT8fQnoBQFQ==", "signatures": [{"sig": "MEUCIGKVcq7Lpt6Ev2Hhz+ak5iRAdwWeCTb5n9GJJW0r9kPFAiEAnyHc3vw03DZ2dl4ojDrqhTQImmFYxT12+f1dwf82Woc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.2.0.tgz_1506457333696_0.769426078768447", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.1e3ee68e": {"name": "jest-regex-util", "version": "21.3.0-alpha.1e3ee68e", "license": "MIT", "_id": "jest-regex-util@21.3.0-alpha.1e3ee68e", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2fb33b6bbaa766beba2808e02ed35d6bb47bcd02", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.3.0-alpha.1e3ee68e.tgz", "integrity": "sha512-CzpuXjq5fEgwMwe3UaPk0UuMOhZxu/tHGiCTYUD78weRXAmz2/Sbs+6d1qfEoiCN7SPOtl55TnnZIKxnYL2k0w==", "signatures": [{"sig": "MEYCIQCCQ0YmSItLQo4QWkwPHec4YABK+0h1/pcxD/PueyDZjAIhAK38ibNLKQmznhOR1Z+C6V1C3Wzacb1Aa4CAbZwDSOCs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.3.0-alpha.1e3ee68e.tgz_1506608436530_0.0636887326836586", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.eff7a1cf": {"name": "jest-regex-util", "version": "21.3.0-alpha.eff7a1cf", "license": "MIT", "_id": "jest-regex-util@21.3.0-alpha.eff7a1cf", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "04cf4faf64738177e18cea2bdd86ef3058578441", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-21.3.0-alpha.eff7a1cf.tgz", "integrity": "sha512-Hd1S/BH4S9vFslX3avir8jPbB3A8ddlIHRPNMCUi0hOmHkcrxwXbhPSZUE4nuMKmLiI0e5NmsPiCcwe1JksI1Q==", "signatures": [{"sig": "MEUCIH904aISmqaGQDjwhqp/6YxGW4+LtxSHxUKkqXNRU0GIAiEAz+2C0J8TEEw7un2Z1L03XuS5Mq7xCWCsW77NLsUAwAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-21.3.0-alpha.eff7a1cf.tgz_1506876407707_0.16072881245054305", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "jest-regex-util", "version": "22.0.3", "license": "MIT", "_id": "jest-regex-util@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c5c10229de5ce2b27bf4347916d95b802ae9aa4d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-22.0.3.tgz", "integrity": "sha512-mplC9chiAotES3ClzNhy0SJcfHB2DivooKJZW+2hDdvP8LLB+OUI+D6bJd7sncbKUsyFcmblEvpm/zz/hef7HA==", "signatures": [{"sig": "MEUCIQDFVfi0PrYAaYYiGzoBuA/QWvhE+CCdcIFOOxKZ3cgEogIgXeWlPMNA4Up+jP9QAMgh0pewWBLa4K43qrLV3ovbn1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-22.0.3.tgz_1513695532596_0.6001056327950209", "host": "s3://npm-registry-packages"}}, "22.0.5": {"name": "jest-regex-util", "version": "22.0.5", "license": "MIT", "_id": "jest-regex-util@22.0.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e05eef614d7211d6320ac443f2996064890aa224", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-22.0.5.tgz", "integrity": "sha512-4Jo9jYhH1OyKm6cYUKdXGgsPnw7EtAhlmnbN0Ik6OoQkIDgkO+rizFNfXSayZQKq2w+QIPD0jroyE13aKd7KxA==", "signatures": [{"sig": "MEUCIEOfv+qhpCkbrhIoFnw5Oyq4cdPfYC9XOXgWeUhjsQAuAiEAjIo3NdcnV47e32iBQ65c1S5/ZUT+lpKsinB95iypOdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-22.0.5.tgz_1515510592195_0.30463219434022903", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "jest-regex-util", "version": "22.0.6", "license": "MIT", "_id": "jest-regex-util@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cd01d33c5993340f5d61be09b270773787a41d88", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-22.0.6.tgz", "integrity": "sha512-VfgVLmPv1Mmu87P53vZK0XdQlioVcw7MjAxiIHS9/QruNqbDH3fPfeqL3YkJI283CmkkxmQNZOCAYinlwJTJfA==", "signatures": [{"sig": "MEQCIBxvJnicw0ETwVgEGht6j5emV1Qfwye/YJd/RCS984g8AiBRs+ddrSDi+6XxW3GoCMXzgW6yLDAK/F20JKR4JcVZRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "9.3.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-22.0.6.tgz_1515664001555_0.9279658489394933", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "jest-regex-util", "version": "22.1.0", "license": "MIT", "_id": "jest-regex-util@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5daf2fe270074b6da63e5d85f1c9acc866768f53", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-22.1.0.tgz", "integrity": "sha512-on0LqVS6Xeh69sw3d1RukVnur+lVOl3zkmb0Q54FHj9wHoq6dbtWqb3TSlnVUyx36hqjJhjgs/QLqs07Bzu72Q==", "signatures": [{"sig": "MEUCIQCPj8QQkYCM9sjpBUtONX5uA72AYXGK3CBnzi3HU/VdZgIgKsfh3xaKoVtsS+6Wqr/O0E9jWsyMv0k/nbvvGmG/hAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "9.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util-22.1.0.tgz_1516017433191_0.15771168377250433", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "jest-regex-util", "version": "22.4.3", "license": "MIT", "_id": "jest-regex-util@22.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a826eb191cdf22502198c5401a1fc04de9cef5af", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-22.4.3.tgz", "fileCount": 2, "integrity": "sha512-LFg1gWr3QinIjb8j833bq7jtQopiwdAs67OGfkPrvy7uNUbVMfTXXcOKXJaeY5GgjobELkKvKENqq1xrUectWg==", "signatures": [{"sig": "MEUCIE5P1ePSkZAg9Xfqe/tZ/4MrUh9lAgR+GfY22Z6dEWwSAiEA2lxHGadxdyxJI6oAeSowQqBw+NVI6FWE+ufj89rAQKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1406}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_22.4.3_1521648486818_0.9047696286742883", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5": {"name": "jest-regex-util", "version": "23.0.0-alpha.5", "license": "MIT", "_id": "jest-regex-util@23.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6dbe0dcb23c9389a2f979c7e293ead3e91052e14", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-jgZjWopAm08962+xiA/+8S6Ra7PGxBrnf0omiGV7EuaPvAhy+03/YNc8aFO6bS+vx6fQD7ZwrJXJgAB9v+waLw==", "signatures": [{"sig": "MEYCIQCOB3V31WR+gGjXorjhfsWxuzqcIS5iM3tMAjVCpV5jVQIhAKOLby6tE/ZuEq4C99lntSjTyROzQV+RPQanqAzcoa60", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1415}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-alpha.5_1523387897070_0.731183492871494", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5r": {"name": "jest-regex-util", "version": "23.0.0-alpha.5r", "license": "MIT", "_id": "jest-regex-util@23.0.0-alpha.5r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5c40a81210d62501241b32d2d314024ce2a29d7d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-alpha.5r.tgz", "fileCount": 5, "integrity": "sha512-8FJGF2YnDFSNyzNJ6rDbLNuHgBDZXv5e42rXjYdTvLnWXIfU5E3rhN7QwPiit2Guv36kK/KjXSAvoa3NHxPD6Q==", "signatures": [{"sig": "MEQCIDB7ZAerIQQV7J+iqpXjBADn8XiUObn1+hSkzja0PG9LAiArCl/R2/JR6b635Gpp6qy0L6lk2KJCNQsKQDE6s3YTbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1416}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-alpha.5r_1523425967043_0.8284178290373221", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.6r": {"name": "jest-regex-util", "version": "23.0.0-alpha.6r", "license": "MIT", "_id": "jest-regex-util@23.0.0-alpha.6r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6f79ca52ebd072f4d62650989503108e7bb45844", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-alpha.6r.tgz", "fileCount": 5, "integrity": "sha512-YmJone4nVY7pTfkY/9OamGtLosCmPN8QsArEmezghW/4q1722WaixiDpHdOQIULEnCRJ56rlvh+w9hrzNszVlg==", "signatures": [{"sig": "MEYCIQCxme+kfgRDa+11yg6SmlUkULecMlYLyUOI7O9QIA3jdwIhAKVOapY9q8lVklq+9yBt8uNLoqXfHVekQAyVC4N2nYbT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1416}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-alpha.6r_1523516491922_0.8359571576987959", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.7": {"name": "jest-regex-util", "version": "23.0.0-alpha.7", "license": "MIT", "_id": "jest-regex-util@23.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9664936b5a331e5988aa3d3ef41bca047b3b8009", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-8qgFxSChe8lnpNUY4mDa/Sr2zvXiGgrvuwvUUkySTcLN8FWE7Ymc1VuzcHk182hA4dJk3QL/lbkiuOROnic49w==", "signatures": [{"sig": "MEQCIBH5HzimHXQ9lsleSbtjEGVpMaN/61zGVjmcds1hZ39xAiBaaCSW3FzE+Wvg7NeIQWLwcRKGOEzBNkMjkz9FRT4Iiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1kMFCRA9TVsSAnZWagAAwSwP/2rtT7b/0AJitEQ81egT\nBoDY2NERg3MwH3MhM91rUYe71GYSwBgxuvvzSoAbyA/gQtzg1Q72TRUksLo0\n284UXnnUIj6wSMlb4czUwtop+qTps/wBQhJjuXZGZ2z+LeKv2i8G+bTZHGRl\nkySUTONBVqR4ZiIgC+wHgb5Li5KsEeNryFLhT3o98QG/4vZE/f9lK6qXwM/p\nRLWimJPpJK579D4oBytep0SVQ9KZ9yrsMBIbzCtI1b9KbCxt2CK3CrnMyoCN\ncmJywX80S2y/QvL4Yx8T76kjBMZOiWV8+rjO8A5KAFzxk1dfotkC3lmTFwZF\npHE+WbzjTN+6dqde8VoCypRtDuRfWX5vORSBrfSgDV0ZekWa5kweHXJnISPe\nGJXRmfWn3GpFxPT+p9agvcHlI5G44bdTCs0M4FdJ4DYcvM92h9YSPP8qYLok\nOgiBv49VLxUjrkiBr2n/l6Jm9c/GI29Ihw1Fn7baqwdLSzo54hK2kzfv9Az5\nYizQOOpllpeNpy1ZcFxYIrgiZhaBgoBhwTD1MKis8Fz/v3Oycqrwlr2Vlrxk\ng2gAyZIZxs/J2DSE9gsfwPbQ5jdwyMLWxQcfXgoXj+jVQ6A1tylLYbL0+037\nnKoaL5S0gE8dmx/rimhXdzSA2nO1cR4WgjH4jFTRmS7avVaBiShDhPbw4W2c\n6ysx\r\n=bz74\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-alpha.7_1523991301168_0.20699948599442464", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.0": {"name": "jest-regex-util", "version": "23.0.0-beta.0", "license": "MIT", "_id": "jest-regex-util@23.0.0-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f30cf4c881ec189395743828d957f568ce86b22b", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-beta.0.tgz", "fileCount": 5, "integrity": "sha512-COqIKXTev2616jlEhbFQMhrdlCmLC8Fr/4h6AuB6JFTOrJsEpEd2rXoo/4UG7Vo4iyKnX8TB0Cjw0StHgqmQGA==", "signatures": [{"sig": "MEQCIC8Zcux/y0hxPp2QZ1W3LD4V41m1iDpaQK+L+qD7/zkzAiBBvwNNKEHYmtGZBC95uWeNN5T/0SgpFKlwCxsyiBnWYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2byRCRA9TVsSAnZWagAAXOgQAI5vM/qugEWC75ZPBFqP\nfhq/gZ3vgPw7FabgOZSCn3PiAS7BKd00gY2mH/RoMmufiL1AJU2AsfOVqVCM\nhQz8REVwmUFXeO4MeaIgdTN86uH+tBZiZN94Gg8cMRFh4Vdyz8vQws82i65P\nsgxv7gzyEf4ket8LmyJmTYJ9SS5Wkkgvh0nKD4sGR9Tlg18N9XklZOrzB+X0\nVYb2bnmt4qrtlc3Q2hkAifq4XEVytV+564ea6Br1a64NBhCjKFqxxqqkm9NP\n0aHT8XsuP9aDCIWBTYyJef+BmCLGOWS6AZlrpBhw8u6yqW3UtHhxcO90LDYC\n2JZ008ePA0ewMwXQ2OjBhUp8uWs4PUBFctKXc/QaZMOGtsB9WbzvUmyAycdv\nHABI/G3NSAXPtTQdBhSgU2/VPcstRevEDILUENS814h1wNlKdyRmdGoVAWRG\nef+WtZI4Lfk+4Y+sKUEJvmh/SAjEw9GnXiJCU8tjATWYvHfH9UlqySSgnRHJ\nwdSy55VsER3FWihmYuH/FulP0+K3qO64TAUKlYWCKZj1NqSqjAZwU9Yh+sEt\nIgec8cN4sIJRe4epkAG8U7Wr410Zc1RRVoltcOu8XuDTdh/jzQa9jmUhgqJS\n+E46EXlsfgK3sXsPZL53+pTss7nZ4S0Mr1A8bxuZJY3rHcPxJ8xgzjjUhhkw\nsCWU\r\n=sNi7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-beta.0_1524219025285_0.8801225856851005", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.1": {"name": "jest-regex-util", "version": "23.0.0-beta.1", "license": "MIT", "_id": "jest-regex-util@23.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "970fccbf94c0dae668090b5b5ad312ef4f7be2e2", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-a+C7aTecl0qT7R41wDfKpXTzJLGPTC74Av2smfibW836x/qJwq+emvgP1tUZwoNoXSW3vHVetE66sNrVWk8FJA==", "signatures": [{"sig": "MEUCIQCORLBxmrOIO95cSQ2kawIPqIGfmcq9w/uj9Kk3M7j7XgIgF6I4plp09h9sfd0nRL0e+FTi+6aym8HmBMpa6VhH1sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa21xRCRA9TVsSAnZWagAATpsP/0X92A26JnLrgyWpj/lK\nXqUZIsbZCNTdqd98Ee+d5ZUubdKJ6DfnR+05vk0NCkwLble/IZ+UC1vSRwsV\ntKz9RSQ4V3mPlEpwXa4jVjFSwwYaDkALOiUIYsd/BReOnOvVQ4VkTzoqPC6G\nHKfXDo7gElRu7jjB6Tdbxq9oONdWvQNz0H2TAwvi6mJTNfOxsVDEo3KpnZjt\n889kjSkbP513nYtEP28HucfJM9jGSYa+RBLm6PDx/5AW97tKPvyykulubkJk\nfsUVu2As2Wx/P7PlXXgkrjYQDHX7NJAIirFT3LbmYfnO7PMAVd/s68Er1y6H\n/lT9l+u3tedzWyXgTqGFMBU9+YF+ZSj8dY2MOrgI4D+9hpf661kxcruWD3oY\nIZJloQnDjGQuPGk6laaBcKB4XT3UO2y3TAP2QyqAxk7WDdaR7k7NIPINSN6x\nqR0pjM0txbenNvl6eaXbyxb6/CijpxhcXu1QPQYiUzQwZjvmkQCzw5gqrjFB\npFTxlKMAOKJKSPk9DRNKYrVxxIB2Fz4+Ed8RYl723JnecpNp58F9jhdF1W7S\njIcUU1yM3Tmf5mSN6c2QTWzwyLQSCy1uESmRW/qx0WubWtyHSseRqb/hLfgr\n29FwGYXuXdPu1uwZ0f8OIvculi2NlwKSQ3B4tSXVjCVIPligeVMxNhs1fvfi\niUs/\r\n=e4yP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-beta.1_1524325456710_0.768519128731231", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.2": {"name": "jest-regex-util", "version": "23.0.0-beta.2", "license": "MIT", "_id": "jest-regex-util@23.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e210117ec92abfac532f9ff120302d9665324f6e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-LXmLVASABJfZwZZ5xaLEODklXGLumPa51U7epN8e7zish2/1W+gO5nArLHZAukPQUXRCt/hyaCrN7d29DRTFTA==", "signatures": [{"sig": "MEQCIBAv1SMZggMglnxnf+0V4BPG/nzv3MArMP2rNr1IlTkVAiB/1oO+wE7seGHo/qN0H9pQnY4LRwUoyxy05OOiy3cHDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4kHqCRA9TVsSAnZWagAA9XIP+QH8B87vLm1u6y/eOj7s\nviRufh2Zr8jjwUlyu1asry1MONCWl6ePU3iJzonil0YR2ZiUt3TuHUHvnXKK\n4sSq7e7auVlJhVJHqhTll6u137Vo/oLdxYNW8NEX+U5vkAAivoPHoidSYUX1\nIxzmcDS8mlO+f8nHvRzPUdC7a8auJ4BnKbFsEdBS7yZj4b6MvtseE+5o2haQ\nCLOrxRnigfGRTjbFEVZhWRYk2eGOirLzUVN8BqOdlDHIeRuzHkas6h3yTkVQ\naVHdmh6S6laPyjWlkd69uvbe5gUYToRsR4/11ZLcvbZFYTlB93JybHJ3MRB6\n4UeVi33Ko9BreFGMCweUu6wgus2Wo9iRGTVHDB/63fIimnNObpkvGbGSUs68\nHfNvMxJ3G/dLgXtSDkL1Jq8bXCp822vY9Da4otbx4qEOndleQlIXVqt19D0j\nTbL/doJsLjK/LMeQRucx25nmNk1uzYv0K5FvoczSqG8rXovFKtqxUiY579ny\nEQvPyvcHZqWiA5KMu1JcTeySE46aAz/qhjoMwJAjYuRxje/hKQkBhQIhI6y6\n5+TqpE8vWjJtGdE1ikX5TpYhiL3xPkWfhYu3asv2Rc+ZHuNt74Z8qTwc9Qts\n6TRknTONmKn1CfHpw77COD+bq0ChLvJbR+ZV8R1zVE4sZEqIauz1RT4TKebr\n9eq/\r\n=EOu6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-beta.2_1524777449712_0.3342599214225517", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.3r": {"name": "jest-regex-util", "version": "23.0.0-alpha.3r", "license": "MIT", "_id": "jest-regex-util@23.0.0-alpha.3r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "125156ba4faf6a7789dcc01cb16f4f0fe65d3816", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-alpha.3r.tgz", "fileCount": 5, "integrity": "sha512-9HO0HvzfTluLjdat1bt5OP72OkehPz3dZ7P3HxcQtWHW37an+yRAS8NvJHNDcJ11amsVdH/I+PU4hV7CvZ9XhA==", "signatures": [{"sig": "MEQCIAJdUIeBFlCFtF2DLGt9iVsZS9LUdj0fOPPkRCwJrsRiAiBwEA2GNBXgNf0fQoDQGkR5SVyMI3m+CfHx/ZxGmOePcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xWrCRA9TVsSAnZWagAAHkwP/iNygXgwwY8MRLjv8blg\nz6XA+D1MeJW+opGnwGjsaiYmIuajdCPXA+ogp+VVo9iRxY9meQbQtYndJQb2\nDcDfLigM446Xb+YjTnFiRWAk6PeJ0rwCTWNATw8A4lFGUBTjZtH7pPXYIlRH\nvCx6YOEro93aqWVcsMFuG1ZGdeUyFluUMUJsgjxU6w+9eib47xT3JXLw5+G2\nJztAEyaBFh4FElpOX/wdUNLBlhOAdqEvzTIx4lCeGsAzrAKVY8UG173GYNpM\ntKrE+KbTAfwcWkdUO8NlUlhI25F884yfpDlnX3LMiZ53FwJu39QKfrFcGmnO\nC36MuIAJMMEH90npu9sMw+2+SzldCP78RHymxoVIOdLnxYaizSGw3oDq4+tE\n7irAye7Jz3RUZl6sToUO1q9Ory0wCalBsYoJAYzb4a9l6tKTi5tZtc+pboIj\n5aklankKG7fEdBbF/wsI5005UIdng0k1Dqv+DJi2Agj79wwjbNwUnJWznm/P\n38C2YFGZfn7yEbOYDw/OIvoDxQj2c7Zx8LNQIJyHZ25LZbWUSSklNL+GSgv9\n8JrZwhhmZ7C4kZuTfdsXnuPkfX8c+KVGcdqcwyCv7mZckLstHd5BEVyZad0x\nl2nymepc8BZvFDTzUxIAS736fWGsyw6QTX/sWJ/2AjyJwAIACTpVjZJcINRL\nSWwO\r\n=brI3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-alpha.3r_1525093802938_0.25673560522908745", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.3r": {"name": "jest-regex-util", "version": "23.0.0-beta.3r", "license": "MIT", "_id": "jest-regex-util@23.0.0-beta.3r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7831ac388d5a3cbacdd2daaa9fb0b46ba59e5dec", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-beta.3r.tgz", "fileCount": 5, "integrity": "sha512-ETY31ovqpG7Puj1YdrPFkZNdmPh8apUbAg14AQtwxMSVMZvBZFOlqIXeEGM2fHGq3kxMmIVQJPIgsxhy3fKYGw==", "signatures": [{"sig": "MEYCIQDYDu7ASbw1r5OSu6HbIC2pnfGEzMNJGALs8RZ5gCrbyAIhAN1fab0me45qcKDZAXvlITGayHnzhf40s9LSdFkM79/U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xbFCRA9TVsSAnZWagAArcsP/A9pOUNcgWknqsEUgmi5\nL0oPa6qqNTGZR1Kv0O8JwgEjIiVk94c5m4xj5rBQvyLVlSyn32a8TSv2zOmS\ndyMwsOpQYO9dcQcy2ERJyP3GfPZvBfzcqf9mros2FsAG35aNQNv06fbwTl4Q\nbM0Sy0jC+q51+W1BXY7sS3TAAC29aes17lSp3YtdAcm9HKqCofQSrGPykSZU\nycyXSHTzNItFF+mSUoGyXPT9SrPIG4tc1SgxI4bFYfZpRdu0QjsGjGhrITrd\nx7cjmcfcVMH/LeHrnStTerzqfY0kD64N1vdsAwUFFh+E1W/nbOHI3to6EvYT\nGFuzd9KNrN9PFrS4W6QBoJKUPTbr31ytNTMQaFuxmBEgtudBkKtqPLPmdbrb\nCMlCrL/Z7jDoDOCULBERGXxjfYSYETNxrmP0s/wM8n7LMrPFRgzNaRW1yuYQ\nIdVauaF6vEqptANYe2uA+XykRpqtrU9eLhr+gC9YrPWUPlU0esTc2wwgDGxU\nMsfEzrzT3WXHoNazc8UkFo+0oG8R/wQ0pxpaAzSVQJr4k26p+CVdGkKCUIAe\nVHsFf2r8w0DdkmDac9PqaZ8TnJ47Uenod/XTVXyZ98yokVku+QA9jkdCnaVY\n5jNKr3KSEZDrcg1KDpMUd6E86H3e57u4GlvFHNa7aTZthWyv1DKZXx7dk2cT\nZi0D\r\n=ceO9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-beta.3r_1525094084470_0.7637327617136365", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.0": {"name": "jest-regex-util", "version": "23.0.0-charlie.0", "license": "MIT", "_id": "jest-regex-util@23.0.0-charlie.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "34515ecb19d71d6b3ba9e86bdcba5d931450d5eb", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-charlie.0.tgz", "fileCount": 5, "integrity": "sha512-/BX3WOO6I4dbVyUEU4BCfPYJ5Etrce41LF6/fp0g4dFYAyGMtjwdLCCDGnD07/DT85muBafSm2rZYmmmJBx4TA==", "signatures": [{"sig": "MEUCIBEPwq7G6Y9fvL1el8ZnVqTHR0Mo+qp+tM5nNbBodEggAiEAqqpwTh/B101xxCi94U5Hy8fGZbQj5BLbo+ZZ2ZPB1BI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6ZlGCRA9TVsSAnZWagAAk3QP/RFIoRT5/tZDhjHCnBhz\ntAPy2cXXOIEAnzlBMBVAJJkv3KM9KnvjW+almpGTG4NYNpH2LJ1UdWFas4h6\nqi1DhqA5QI+OI4cKloFiEf2wnRxHWj4IOIhILdaQ0bJf/2WkuqABb1ut37QJ\nu7K4tFAHpBz4Ri23schqZ0If1NJgR9u2yaeUaLRAz3ZYVFLHceOIsgDTqiBA\nCL+olssuYy/wqr9m0rkA860Mn5o3qenv8K4Mory3WHJIJhyYa/Y4MUrYuyvZ\n1TtoUgQ79xBMuU5lgWQUABwToXAvQQSYGMK+uI3G96oQgW8o5TUO6TJ874ew\n6xrBSf9Y6+R9fHw4PsPdRiPNgXSN2dkzRYtfdK0m+zC6C4ttAzzwS/j0e+1w\nJVlAx+0Hh1/GAUDUO+9Gyo17z1/YtepVofpvvviaRpbuLQ7aXrG8V7AN3ihi\nnRa3yIj4lCQ31Y+LZnv/PT8kVA9p0jD5w6WldL5HRPRVHN7kVe9SSXQpngwa\nPXgiwdbufRk0velp4KYI2Qu0xwC6jKWgZJqOd7I/g/fu3oH4FdscCiQZ8IKY\nT1mfMEjuDioG65px7VVnfllPwglKf9aLMEhxV9iqwxWDYlQSCq0cb+zDCnA+\nhJjN7tgdRc4ILf/JlvEFKWbZFZ/gsanuqiN9GGj1i8XVw2pTv9PP0AJWNHgI\nk95y\r\n=2bt2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-charlie.0_1525258566317_0.2353502799723184", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.1": {"name": "jest-regex-util", "version": "23.0.0-charlie.1", "license": "MIT", "_id": "jest-regex-util@23.0.0-charlie.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b85c25e0e879e4c5c11bd66996a820a487b08b23", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-charlie.1.tgz", "fileCount": 5, "integrity": "sha512-MYMnmEC1P7n/MTdva3pvEk7LTj8AvT6eBeaxXkyVjiA0rK12GJxfOPFv4AVs5SgXWsGHFhycpzG+H8albgdi/Q==", "signatures": [{"sig": "MEQCIAtuAERL2dKW1QARbcQS3f1mcgUoXIXwdTkbTADUifzdAiAkSDmXMn2GJpbZH3OVsYBerBQqeRP55h0+3sIvfW8RLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6vweCRA9TVsSAnZWagAAbhgP/jy/0otRi9hb83/uzO7N\nnqgj3+MWy3Uix4OsYjg0yI4oke7VhDHUP4bxnpzE3isSitgKpSj8FzMlk+pa\nl0n32TS6qTDiVPxI+imEcGnUdY4hSsEvSr1jNHzGQO+M3JlD7Z7CBjXBiwP6\n7cfnjhhkeCND8pgS1SJNDeD0J0ALzrsDPnb0EiFJXoIZpGcfw8x9NqBM2zRd\nuDixR64dGPPcHl79UtRzZd2JYxpFqWcGVrtcOGIKvS2NRLPAHiSPlAlpHoTB\n3j16rIiHdUm02DbmB+/88sbRcMcCTwqV64SIw/PUZeZoNAtYm/AfAq7qVtBi\nE4nDJh4FkXINbQJhkK0yiWhIoKtMP25sBxK89nzs1gb+g2E9duM9UCT3VYnJ\nzptOVmw7FbW+mTDprzX+6E1/UJZAQWG6ezl8yROldpDPvx56owj3HLQw9nZv\nbLOJ+qbJZkO75zo4HNcO32SlhPW6Q8kcFVD7IugTGIcX1B3gfJbhU9XOBP/f\nSZNnriZN3slBrwjVVO6JF15A33yoRsfmnVghTPi+gV6ZKK6SmKa3X4jbmRtM\nVqvoD3oZz5wbEIuur2pz0yP+Aar1IvVsYgSIxXnkZ9MJYFZGFsuBeKVrJauL\nCXV0/Mlntd14cY8ABRItrJThvn1JRxH+srIqV7PlHh/6LSU0+uwxf8/PoHQB\nV6AP\r\n=yeIW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-charlie.1_1525349405443_0.7196528151561474", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.2": {"name": "jest-regex-util", "version": "23.0.0-charlie.2", "license": "MIT", "_id": "jest-regex-util@23.0.0-charlie.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3af5a9fb891487443fb391c19f91c0402b49abfe", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-charlie.2.tgz", "fileCount": 5, "integrity": "sha512-egYVtQ9WYB/2LKp1at46akHi45uMGQdI2lD+BpeWITDcuovVN4NGfZDAkaDju+xb8fkUWYLZEVw15LPBIoBYOQ==", "signatures": [{"sig": "MEUCIEueF2roEuGD+B1ubiATIVsuHQg00NwOA59KPpqMQLblAiEA9CPZRuBUQJesoAwZZIoOZADpaJZB0ITu9JsK446R2zQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+q2SCRA9TVsSAnZWagAAyc4P/iEZorCfdlNsJ5lSHP+a\n93UI06NLzMMJv/nhFZn1/pUfkkX0SoI79jfEc/hN1QE6oL6QvaJ8593UvasQ\ntGfrrv7x9/8fXfK799MPk5LqRqsEDj2aduvVi5U26D44GPQ89MO2FAdtXR8j\nWejtZukNuP9sn1nhLN+sBGaPD/JdSU1YPPMtgSq/ZHoiCJTwhDW7HNC/nqxN\nywmpZFtWrHf8e0+ih1so4Jo49hct3DqztfKD//l6K4LCvNlzQ0iXhJjwCaFo\no6dF1D8C4FULJOE9Fb6tHOO0NYAOZRhu1JOMGoKwFmrEvF1OTDxQ8lNJsrQ1\n2AlPHfmboRhnfj4ayif3dQO0TqSSlwijMyTs5wLGxdUB5nEuWAOvETGGALRw\nXFwkpEymI/PRO/Obz6li8/1Lv/CVapxzWjso0Ehh+24exYQsfy+7Ms6UiSk1\nWg4wA7RfZ7cg2KgULxv1hmInIV8C0d3xddmt2L48mUmTbN0LkGI/KwfXcjjC\noTHlnfsNnHWVwG/NH/aSMraibaGxX/EexnkaDk07HhnBMBw2/J4XDa+oq47i\nUZ8L9J64qw+W7qIkJaduzcXea/5GEFSoREZGHIadQykMCmp6sMVVLJaoJ1TK\nH7VQipphOT5dCn3+O0x9wCRJ9R1BTpE0+RfkQS6zqrLyvaPfezUTy2DLv3B5\n7Xie\r\n=SXMB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-charlie.2_1526377874176_0.9136901650342826", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.3": {"name": "jest-regex-util", "version": "23.0.0-charlie.3", "license": "MIT", "_id": "jest-regex-util@23.0.0-charlie.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f4cc6069940a2dbb1c5c4983cbb400932bef497c", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-charlie.3.tgz", "fileCount": 5, "integrity": "sha512-7uTy22xCRBbW+LUgQ4UvmSqPvOaP4QuYWRxlpB87mRDbWMm8g77/K0LOeVWM0xd7JotmuLcWYzrPqvpeCuIKoQ==", "signatures": [{"sig": "MEQCIHsw42r3o4T5WOBK+Hx2InfGusm662Th7pi/bqatSYGTAiBgvemW3RBGETLHXBC+d4mN+niFF/kTW1dOFgM42MojAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBDAqCRA9TVsSAnZWagAAsGEP/2lSpbcocHlrARZstAC/\nY0XZky0DSndjXdcKCYYvysrAlhCoAdnfGZ5LSPuWZYyBpDgEsslWxK7oe49o\nNxnqJPCExUIrNaIijpVqgd0vI92BfLnZyHz0MxdHQe2l9QZ0+clEPy3gbXUS\nwHsUCwGSBoEpJMfAbjodUAzV6UkLmMvMUPg++NRLJzoP73uGcoLjByd9GhOP\n9U5Z/4kddP+OQf3FtBMqFRKDNgaqN6iq3xfZoyiuz6wGZobn2yjIn1oFxNtf\neg7zbkI9DNgEq2gPRQ+Bfk4k05m8b+UTj6IbPo432J51sJ8JPFEkYmLtBMxC\n/TpRCktmE8/jxExKb+zKf/RTyOAmwvwesIA/2DwRiWAdxiJ+dI5K7qavBCjf\nYae3qC434pHD7oIH5WAqtuBuan4IVGzrJbZcVvdcECq79/j8uLXMZm5Vm53a\n6gb2RuN/5+BffS1b1dBwbGcJeOyGJ/4P1cWkgDjG7X6P/JAMcWixQ83Vcttd\n3dmg0qkqsZJZEO2OJpgM/XNirSBH0Ye+rzb7wZj/tKVWAAHQUwiqJ6LdxMFr\n1ntGjWgfmTeNCUew10IT1wIWUMJEn6kBPLai6gw8yF7VAY+ovV64v4XQ2DLB\n8aK9DG4UJR4Ckx+cUCfSEBekMIfinTmeg5cF+xbCFDJ/kL2uQcWNIA9M3yRR\neEck\r\n=TuUf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-charlie.3_1527001130495_0.4155031203891624", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.4": {"name": "jest-regex-util", "version": "23.0.0-charlie.4", "license": "MIT", "_id": "jest-regex-util@23.0.0-charlie.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4eb1fdf4545d5203346f22489aaf26891e1fbd5e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0-charlie.4.tgz", "fileCount": 5, "integrity": "sha512-lExF1lJnDDkzVDx6lRn/rvN/RUXx52X0b8tJsGhwVHWGT5vtZHvgj3ViguJC7QrcwMbov4tcCLdJaRmHuWCRww==", "signatures": [{"sig": "MEYCIQDy8tdRuY9ZmMh8G3xTmUVR13965XZLyHPTA8jTAPql/AIhANXsbp2iUYm3WlAEEZB8vXjU+E615KKG8QdQlCtuuL9H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBUV+CRA9TVsSAnZWagAA7dEQAJT2lsZ8153//ZF+mFpG\n8AwSpIpZ34LdPu0U26YARERnYc8IjuXC1NDXkRWI+EwSdHPsxGj1IAVcBhZi\nx3b9sxAyI9RAuu7zV4Qy8VSPPMYOw3ueNjyLNrKHorLCFcZNMUyfu7D8D3+j\nDxi/iF9OZ+YzF/XBQ4hqzmn9Hli17CdfQGbV8COzcqEp50gKjJO+M+wf8m5b\nXW9EaPya1/he3Emw5aKAD6+wegQGPtDzXNX7mJeUv0pwGu9SeIiOZqTE6qmG\nIpYUS5G4QNTCSjIuZxlM7s3QYLjUgfagI93hggzBT2RkKz0aSDiTopXHGurr\noZgo87+L+1qMQt019JWMtrUqOjdkQ7KlM1kaGFbrgsyqP94xPBgvHFe5s2Cq\ned2uqbZnbjAoYNCriXQuWuRur4ggAiGkLfcTaa6W6nqkGNQhfMCTHgDNapAR\n57Bgj37BLVlUovlvpaCSj3f2ajSxun/0LGf4pE1bbiA+8tU26GcP0xvNlnyq\npbsM1aXX4U5Lum7jXwas0S/HxI/yu/ZV1Ys2OwCJ1yd5b2RLJXY0KrFNK3DG\n9ZyRkqjayejVHDYbX4QC4pgEK7dZT3yxgE49TqtrGUA9oYY4gNzr9fFuf7Df\n+Aviw9cOIrHIC/dUIy3j1rC30Sp2+oBFth4iwkFE14RFWTIHlENk0axeA8Ad\njfr5\r\n=oOwA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0-charlie.4_1527072126245_0.17505163260650436", "host": "s3://npm-registry-packages"}}, "23.0.0": {"name": "jest-regex-util", "version": "23.0.0", "license": "MIT", "_id": "jest-regex-util@23.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dd5c1fde0c46f4371314cf10f7a751a23f4e8f76", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.0.0.tgz", "fileCount": 5, "integrity": "sha512-RV2eIkOoVa8G5zZsp4dIjO4jqfLJ/hD860jgjGsJeL+vmnXJN0QF/xg0sr9T7itlpcqyhSTyE+xLr+MNxIjacg==", "signatures": [{"sig": "MEYCIQDeC0yxY80Xcs9kr4vU1vqyvU7nVyJkt4tHvS8jveYQIAIhAP/ZYx0DsAWZErZ59N6i0LNy5lQYa/fc1QdgUpN8t3/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1407, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBvW1CRA9TVsSAnZWagAA5ZIP/1ARUS9gcXY05ECVr3nk\nF/5TGxUEsqwpPBwmQbvyrMpdkbKbDPsDHijX3K7SDAkP9WwBxTuGjKVPENwe\nwkM3qQEXm7MN3Dz7YCnUpTMmyyqKpEGjUvIrd8k3ceg4cGdL3JW09PHssTiz\nr6zivZMVDJeWUSgrG+DJZbA06odNKOo5eBoX6/bJSw4Dzyt7AHvzwO3MRL8s\nJ9iF8liC1qCPPds+4WslzrEF7PPngmJcOH35tesvvJzrxTbmHLL4OFkFuw31\nFmeGqe7TXGuf1YgcCV/Ww5RtzFAhCIhiA5wBWTERqr2YRgccFZBm2zhhDM5D\nnHifqCJ4rOfN4I7GH2Ty6WMMhVvyUemUbbozXm3UWUb0Zr3bqwpkzmRl9tjl\n3lYiMm+WVJRkLZiceYFY+9bZsmtmfEZia2C8l1YIvIEakHU6R7VPZFWG2YQM\nm7lpOwaShUuRGNOv0Rq7onW70ODp2War3tE+uJIvEmduRxjhY7st64A0Ldi5\nGVqpovB6gyVEFp8q0sWpRx8clycOxlJa/qC607XdSpTZWejHgsP1rdIvMfAu\nqxJl6gjxdXeB0BWK7UCYkU6bPJcrwkcnljX1iJNSuuB1Pq/U1xFNm32/xgQ+\nl/awg1kqFFVJMW+km5LuMMXAvfEe6lQWWQgjuhrrgroVb5BGYJ+52pu5VtLN\n0TjT\r\n=Ynhr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.0.0_1527182773486_0.791298410706007", "host": "s3://npm-registry-packages"}}, "23.3.0": {"name": "jest-regex-util", "version": "23.3.0", "license": "MIT", "_id": "jest-regex-util@23.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5f86729547c2785c4002ceaa8f849fe8ca471bc5", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.3.0.tgz", "fileCount": 5, "integrity": "sha512-pNilf1tXhv5z0qjJy2Hl6Ar6dsi+XX2zpCAuzxRs4qoputI0Bm9rU7pa2ErrFTfiHYe8VboTR7WATPZXqzpQ/g==", "signatures": [{"sig": "MEUCIQD2cHz6sO82rHCgYBa3kR9WNxvZuWjX1IP8vXRZabkXogIgYJF85oxu5CKM1vRTa2Sv9gofccJB66C2Pl4vlkStrIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1487}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_23.3.0_1530706969251_0.34154018688683774", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-regex-util", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "94eae33c6547204d7d6d6ab9b096c25fb03ab645", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.0.tgz", "fileCount": 3, "integrity": "sha512-Awh0bNT4ovfBtRpx4QaW8o24IaNoiYK3D9pJIZTkLwZW7iILonwRRlXtD+Nd0gIXZ6L2hYASTvOlkULZZNfrhg==", "signatures": [{"sig": "MEUCIQDF5nqizC1u6ZoFoHiUEr17IlqMyIa9REpFRTd9nLcbyAIgQO8CHpn1yhwW3gy+ejLNmcLXoUk0DW7k32E/t0cLo8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyco1CRA9TVsSAnZWagAAjL8P/0bzCvbAB8q3091kejFn\nzvrzAJ9Xv6726+LiqJno68DcsVAtFf65lFt03LNdujC7HDFoDfhVGN8PzQkI\n/7F39guKN4fkP44SfpwbxxkvIEhyX6TyVO1DUIdxT1xLUPdf+2ERx83Lp9uL\nyzbM+mr1EW7uy5OpayGMy0zOKKrXIo6GrCDQ4+0mS4wnNm6vfyghx8HkncvG\ng62Qb9qXoabLLuabsVzzzUNDXdReMP91wfLodH8Z6EhrEBIB39YZ/boojh2J\nwLJGx+vQRHerVmBA1rx1NBGXT1OUUuJsZOX6LyfROUuwBf9YAf2x1nOSOv2f\nwMYF65pr0IJ7HqWOdi7a1Mk7J+9TlPgoVuIDO/+GxL1jygJK+IY3MBpUzk0S\nVU40UqKREbRWnmVMAhFTz8b/x0Y9DOTNpVs2Li2x+noRHnMJ9vob6AK39AoQ\nJO/dX2rxSPuHfDzo3zHZ20xVTDvI/1AKTKYpfO7OgNWJ7YcrDqFBKIt4AR00\nTgMG6lNXWHtuHVWOO7vo2Tkxjb+Mxq0gpYgg2lRxEDr4K6EeFIV9lKI42oLL\nD52gecFB7FtGHWP9EMBNhhAUMQKn3DhmUFDkvErQudYmhSYyRwgagd5qmZFl\n9BlNhqePJu4HrKRAD1oaGqg4a/YeVonLLqS4I6LnJKi63PQkUrcl/I5O1W2l\n9C3u\r\n=9LDt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.0_1539951156639_0.5525202387111765", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-regex-util", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6e74e9898cef283c669005136c21024a0d19024e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.1.tgz", "fileCount": 3, "integrity": "sha512-qvCqgll7SSW1zMkbz9HzwsoUqtuXM5iTejPPCCcZXZ2DPpnTV5TqHNuCmi0swpSOCDC1I/bN55XsvjZNVB0zjg==", "signatures": [{"sig": "MEYCIQDDA07tCKjoSzhGSg/BC7m6Or8WGchSelKjXrl2gTsO4AIhAPkMzUIn/SnQUXvUutshDd84r2/KaVNkou3DCFxJ8G3w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5PCRA9TVsSAnZWagAANAQP/RbL8nmHBTMT5qgEanSi\nIUeHpvY+75iZOfRrK0cF70+teOOyQCeuh8tbL5Wk5pIYN5JweOcr/euL9LHe\n3hKrYEEAs1jnxAF8RPUmEVUIFZQ/Hc0c9EF5dI3b7uYQuiT/qyqxGFhkO83/\n/hl3vujGVGT6B/0KdqIXJq/BbvLJOUUYVj91jS1vwv9UwgtZ+qlGT6zz7Vyk\nTfwYLR1JQlKQonn/EeQdRwVy0fbCE/YWSue8vi0ompVlBVqdYqkSXGrOzgEk\nRTMK5M9CDL045JKcW7xuMRtt7qxTy28cJrRQH6K1wbvxDglRFUlmfnnti5RV\ndMicuxyMiRBG/TZizdL1KA1BG/uLfFnOP/rvEBwjgdv8NgEmSNhPYvNBoDoL\n5MaiIhSRwZ3SwX4hlAQ9fxqfZclateK5+tmwapjabi4SwWcqh8S7xdHYP9ng\nnf47HferWHKqd74x4AZDUJDCD1Lg4nVfXrFXH3YsdrHYfPtf0wM6ImECMn6y\ncQ6wfVqF4f8DYYP7o3jxv4v7O6Br7bqvttqoA0kLnDafy8cNBm6aacZ/7MCg\n91L66Hj0j+lS1+QBRaf/9TeTMoILFER535Whr3EHyH0RL9qpNhcsgS12GAI4\ny86uiQYtBt+1Jj+p9ZeTGn4viIMrtwvCPfpM4aGNbRlq28DCBxi/w/PrK7Bh\n32Ea\r\n=bgQ5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.1_1540222542171_0.45018294284660665", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-regex-util", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ade43ddb38650c3a209daa55b93da21b38c5afff", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-cbw6z9wNOjCXyRc8aql2ngOTZ9uEwfhZcaHamy49r7wwdL5+sDTUDezh2erVE/P9BC0kLHzcdmM2j/db/LmVwA==", "signatures": [{"sig": "MEUCICVBARpW8y+CEJUFgOk1IcgbqDiDrCscdSNWCm84zAhXAiEAmVeDRWHLUx4c5ausLpGbOKCsQpI4wKmHh+sS/OupzrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aAdCRA9TVsSAnZWagAABsAP/0q+EzrYlVCDK+MEDF9t\nsl/pZJbiFSvnuDwOGwYCijFamUYWscs8aL5Jd5hkYjcS2VLLmE+zoclCbuUm\nRrgUT3k67I3ZbwcE2na/KGcCsmtVKIAp31LdAuXtFjLzyK7Vo5heVMCi+sqP\n6/s1fgjYizpCzKKbndigJmTZaNkqY/04EZT1BG/jCICNqzoGdDcL4ErOF4NP\n2NIRGqpXFxQOQwkL26YC2dBDigZfGzY+Sdt6dIA8tidlLPt0QBCIxaIldGwg\n8zeveEHYIYYyRhOnlGr77Ko1Lv7BVHyJifpxQefINMIODj02vbTfj8u9Vy4o\n5DhxonL3GtPS5pIVB8HX6DA6SqZkaYd9znE4o+MLEad8Ky9hpnf18g7oT/W6\n7uFVpd2SIt+qZYBSrhsHi+/Baakx+t3Wmgf1FsFRX6OoeAtFdHxdVy+0xVw9\nc2kATvM4HcjCZ606pGCm+VjvdfYJF04vrgxubJKj2iAm6KNlUL/zriUGjSLP\nQjwTm03ZH4x1hQitFMWVgIAEl7gKXrkXvJcpb62f6M9fkrcT3F0PAIwOf3Mz\n3WRJOPxoU0quOc9TU31EJnKR9+Ve4ibccTSrDfftnj4aAJAjHCemiUHVjOiw\nGJH0/d+bbyPz3uGKW3xebn2vBa01n0Lgt/hNglIld+FFgj0T22mkKrFYHysd\n6GGC\r\n=2Nmj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.2_1540464661340_0.9536770770393164", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-regex-util", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "acbac6df4d895a9d3008595efbac5f759b9f69c3", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.4.tgz", "fileCount": 3, "integrity": "sha512-zjkN2+Ue4ryS9n7OCXshqtG/oa3f4xXqOeSyxoTmGl5vQ/zAurlHnKA/x54BZtgwFxWLumEuNeyDX85sakwItQ==", "signatures": [{"sig": "MEQCICXhmezNTjOosSaq9ubmaWyV/CNUYMB3ty3niBs43lASAiBHDOFxxQ0JCvcatVYXDFd+/zum/p09eJgJNRVjIPdFCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00HGCRA9TVsSAnZWagAAq9IP/3iiJy704bvQE94YswKs\n6j63yT5geyS3PPhaRSWwTsjEFULvjec1vITx0BR3JZ6eJmXKytJ3pEnTHvBy\nFY7WIxlDLN7mpp622pQcjreqHD92hHeVcZkUwsRLfoe4GvTgHzTdU/A64jNq\nrkXXgPfU/4V4X6RmZZ4ncsaeHS8S6IPSBJV2FkzQEt3QGiIR86v62MPq4akm\noq1LNWhGUxYxGwrVpZ9xkXYxi1/Untk+t53Ls0xnUK0nVpduk41x83F5Sc7n\nrNwLgE7qzTxm3k1OpCAci0o1jreO8cmVFsxygoBcF4D7Pn76miU/RyxN6AoB\nuSDZR7DI2x2oJWSPK7E+jcAs+UKvhjBDuuEeXIIeinlhJc5ZKFf/jaWNbwsm\ncyeWnl81wv5euxeuxXkQXpOnucTcPBAvDTBRP4NUCOiSWy2sPAUZLPyPFrbv\n1/L4Cz+LpIWvk8XCx5R3KaLFEzjtxzCotm18ELkMO/5M8abehVxlWSpr+oZC\nYlKGtikSbcqU1aAc6ajYYL6HJZ4sLnZgaG8dCrFIkyT0kN+fSLEbOUPeoqwT\ngNMEGD3yDUwOdrCO9TL//b0M3Wn6one7/9UGDRjzmmIf/o/xkQO/MFR5W9sz\nOJrZamphMnOgdJ/j41eU+Ag0N+LmcdAMAOi5DDvpGPVh9NDESBYT1TjXf7Bp\n127L\r\n=Cceq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.4_1540571588828_0.7778027887476591", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-regex-util", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d8c82e3973810f13b818a000f72f88f3cc180edd", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.5.tgz", "fileCount": 3, "integrity": "sha512-I91jQvETDtAKdBI3LzAluEPJYhqHHAY4hv+ohVo9lslrCUcpFSDXoF/SLrC78TjtrBsQ9FsX461GQz3mdoeySA==", "signatures": [{"sig": "MEUCIQCyyeiPgoq0EMCle2UL4AHEk5wRfkMh9OEASxNVMAafwwIgPOxbXEYLVHzcTYCayHh4knymbdTs7hiv8oFqAtoaSlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfJCRA9TVsSAnZWagAA+4AP/iJFtrrPtqP/lsPADuzW\n5pcrJW59F6OErVyJz9fi8wrNXMcz4Z9Vvqrmc1kgX5HTXw64A/kzTclEkkmw\n+gPr/VD0oise0GMEO5B9wdkeis2Ry+j+lActpV97s9dyhKj0Vp+E1gZ1deQ/\nuyBnAB+cIqNkavRRZvHXCL8QinWSlPfqkLTIZASLjJMGR1NSwd45/lHzFzS4\nO1+x5uAT5OvdOkFtm7DkGmOnMPp6NgpfVWUzv/PLgKZN2frZ1ot0TvdZFgyl\noBF7au/Sodg5WsGD4fMKFw7snRWJOfdbcBH/jo6sIqjvMkWeC4YlIiXhEQKe\nIui/+OgjQyFuwQ4coUMmr7mz1VPknnlr+vGItGwuH7CSxixzalKf2xOItIGT\nNBJ6In0T22t2Xyi1yqJnQMaq/4cy+jG5tPffDRhWOXJ+ot/vvNjRGLUySnAz\nf0lHXzQDX5W+TEOPUkywfcYj7hUPRAaOKhE4I8xKiC1z4vi6mIoSJSnp19GJ\nrWk1AJnsBmJXshpwd687VufCHLX+LTJfJa6NxgZK3phGqOdTluehWxoco01P\n6j9N+HEtRDBsBxWijuA5c/ofU5CjgqHcQQIl0DLCpGN0DLyTH41SMJJCburm\n8z4MNmHyuh8ROQZk6FwrU+FZ/DdK+QRb3/LszmnzGtPuxCo/XukyyOiy8sYy\nGM6T\r\n=I0r6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.5_1541769160433_0.6701644899064834", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-regex-util", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "290684073641e2be4c0a5ddaaf4450d7956f7b57", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.6.tgz", "fileCount": 3, "integrity": "sha512-kfV9Z/Sfj/PRcuJLrnk4IoOC0GDfzEn+GmPZ2PI8ql+xa3EYq/+bM75SnbvM6axLaaiLavxPZfoAY5UK07N1Wg==", "signatures": [{"sig": "MEUCIQCo2HiNUMfl5njr29YjheILF25g0mbjNHSloji8zO2DUAIgLsqaaWO1kSJQ9ho2P4dtDgPByFeRAEyOz/j0bqJtxGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5ciwCRA9TVsSAnZWagAAKEUP/2mFL5i/Iu1Kaqrn8/dK\nf1ffeJFJ/BdG0GJmbwNi0jbrm+jMT9VvjODamvcXn7TKrADZeP5iWn83VEQd\nonUBE8qrUUn023d9BA2fH/r/74KMD6SoziDXYnGIbrisJyLbqzjAXum84VSg\nAPJdjRUrxPICufHqu1HkqblLuWqyU7gPrN8rNVZ/79P11OMf0aNiaUMAar8u\nq2rbYNvHXD+beYhdXh4P1ykPK/PYtmfLEiMqPnbgQX1EtxlUJNicBKvthnxe\nby02YTli6/zQc6A1PxVudsqHYum9pIUWiYRGdDuxd//J531Md3vVWwHXenss\nNWUtB/AUL3Pysl1CdZWiKscGyYcPskzBqKdFSrBZar4vkDrxQmmFD79pAEXO\ntHcYs0VdOczJ+4IeIIMEKqtaAE5pXZiyk5CcJ3/ToWAkQtlABkKWXV7z6NCx\nYIvBb9k7rkJbjNzneISGB4P0CPc3R7wbej/w1qKEGhWrEzpzjIll+gqJyVKU\n3r2f0GVZWU0af1hY0e0eyXGhFrU/DnCY5byn78Oqe85iSuw209bGVpanjQmg\njPoieb6fwQImotTfgGwNMHmmNGIPKXbHRTHMohWL4B2L3gdJJ7RS4+CE4wZi\ncKIAdakhKrSkHUVXOwiHJLQGQGK0hv1oe4LngbtTqX8BiWnbncaN89yIzQNB\nrsa9\r\n=Em8v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.6_1541785776302_0.2770421933726006", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-regex-util", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2bacf96446e74a12228b657a5aa8a30818ead50d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.7.tgz", "fileCount": 2, "integrity": "sha512-Z<PERSON>+r9nqpsWC5mqk9o6QOInAKWDeqUoGVS36dKhQu9zzxPpyDy3OasQc1/8FurebR079cbf1y+Ur9xU/yrQEb6w==", "signatures": [{"sig": "MEUCIQCnaKZJHULFmeMnByPnsM9XrNT9elvdlwu/N/NhJo1s+wIgL3cm1onlF/Qo17/8NaHHr4dRM3lneumg6W8WncHDwr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DPCRA9TVsSAnZWagAA12IQAJEPWXgFubKyqbAUz6YG\nuH2YXkOVv/TCW6q84z9XmNDIPgmZKXctDJAPFnrXSzeynBKZKYBCaPSH2JzS\nHHD1T9OIvLI8UyCjImeHU/HO19BrWLbG0EqoDGozZuTCw1XsDmbAu0BOzXnE\na8cPNKTle+8/OPpiqmH/P4YM7zm9Mf0ce2TTddahbiEGgC19jFO7X0T+0nYU\n8L+bVgFT/BxSNsJSDoi+uuUJY5gDN/LcKuQ309EYE0ACOoL67kNdpfELQSbw\nN6yJrcaUNdP8ZqJ+jGNkyVmC4wPs4fEIEzpck0a7DUl8Lj9Mlvg+YL1RbAjz\n/08JSl9l0MRbII4TNXaetrq38T6pfinMh7qp88YGnaqnKoa4mgQyn0xoinBM\n+/6w34s1YJHCdckoIjvVTLPqZj9IHD8/rTha5RLUUlTtKhf8ysBNc/rsuX+G\nCek2NdQfO/JpKzTpj+VvPmhe5C1sqgtuNmm/slPf3rwvoq6GcHNolMqlVSs8\nR58Bbn7s5jVXwrsaGKauwDNrILYZr6kYWwBPxTfzX7MjSKB6t/V/WM/18n1B\njvjGS+OT1FpjbLHtnqjOJ3I+K6HdMM98dqdAVg2HVMivdOZBGonpcWzOnGq1\nuArvG/ICGRiyHULAUkz87VwBwFVyrAAYxMGibp05erp1OPOVJJlGLNspqghH\n2ysw\r\n=mEkY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.7_1544544462398_0.09512110285455", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-regex-util", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec4713342a9bbed2509df6b8d8aa24e759673d9d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.9.tgz", "fileCount": 3, "integrity": "sha512-wpahI7oMEBsOWUw/MgWrFHv6C0etsgiZgkt4GXFINfBKXrgchRJ8ObFtI9XmKsfad43fPH/Gji0ZKxHRId47bA==", "signatures": [{"sig": "MEUCIBFUpOynLoEZ4OmoW4MnCe3yupkFCyajB03+hmdEHaISAiEAqmmMDLcX5wt4cOWXEKNESCpro54IkhG9VWUoYI7Uf3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlS9CRA9TVsSAnZWagAAM6AP/j8k6CYirLJkGqUkloNw\nh2bk7qGUae+o7XF9rCxR7z42jMqGncTEaoslwjLgWoCHpl3llR85IFHQNHEU\npedVXZwUy5y4sPGm16w758kW83U7qv49oU56RrTaQPz9RYAB/uW1pl8bTeVe\n+1RDZwEPdEs2mBUPl+uFYAEh5boWUYx6f2JMT+Pt0B3PtkJpC0u7XRQg9xXY\n7XH7djz2/6Kv5FHUrb6Ws0vTXfYLMrvT5qBxiWIjf2OO2bLJ6lZUEZw7jE/h\nwuNxskwcNOCPDm1pCLFMgAOBdUajUmE4RiBZj49gRcHnho8v/h6vmK+rEuPx\nDGNVDpSTR4Vlq7Y9Kf14Bhrzs05vCklgMhTuPUJr5Fj5aCL2PrcsXbhYKx1+\nMQwJ0D3ta3v+YwsjLeXyqhuJ4WHWgTC4CjHjekv9rHGXv6G5CYhCqlN5Cnw3\nfuloYce8atcrZT7uksVwEtn48p3IAAwyqFcF+YIPaWff5sqR1hCLoAJ25ZkL\nPP462P9Mz5v43J6eNUe5FkUxxndmCIS6eAu/OXidhAnJsuljEDDFCSN2GORB\nxip1gjXk13BFhJhwwG+O2rM3ZG1YV7lVuUOMUKrFU+IBjUYYbXW33CHwnedg\nXSiZGBaupjJvPMQgBn2of9Rm9+bD+leemWzudESjLTsbN3lxQKxnvckjgfi2\nxN0K\r\n=vvyN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.9_1545229500839_0.7427827067031596", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-regex-util", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2aee2ca5157bd8ccdce759b4a2b8c89844f2e7f", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.10.tgz", "fileCount": 3, "integrity": "sha512-zuUJ6DdeREjly/hszjC0X7BgKAFj2+zmAf2pFlxqpbB4+j+3+Cqj3MBWP404Rcsdw+VJsOuFW0HaB5VuiL4NLQ==", "signatures": [{"sig": "MEQCIFzczWHFgVhm25wXvWMOEp/XbHVXw6dO/N6jO7+jgdIwAiBWBi5JTXE9vgtaX9eZyC+1Ione3mw8dkOLgXfzTekcFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNilJCRA9TVsSAnZWagAA5igP/0RZSZpPLC0slKHndf5R\n7FMu7npSzZOh+UN61iDu1E2kd0H+8l9rNxJj5I8ZtQ6ULb72SKFffMWwTvMO\n3SRkbpXvCSpyIVSGfJwrBOKYFDIDDSLrL8tc/Kw5T6O5H+MeYBirnmBMfePV\n/uzSoox/p94l6RLOWbP3JELXUYY7hvtoq3di91NJwl+cTMasiO0akWQ8Ntfr\nUP+VEDGfb6xYFmtXh9pQzgZ8sOSU0kgMvjwKSN6xmYDpgTXvE5pXzNHGRdWu\nLgpSyGyzkLoq1kmgZM3e78pw4+CySYbnTXM/UWUNB9dZL+ksXAS5QSoD+l6r\nYE2x7YFECqsgVT0/frO9+STLxs4t57huH+LG3mo+czziEJvshxPgX7H1HmRJ\nQy+AlLKDCi4QsyxmFvA0JtKgJwl67GMtcL3BDtgyhI6jHfz7q6pvCAq7k11L\nxZFOOxd/DWOdemptFlSMjWm2XYXvMSfBGTqwLe7pMrBPJSUeQw3DgiW603+Y\nAO96Fd7ZEwvuaPI0Q5loYzD4YEG5kGU+dGLLzBnCj9XZTasitv3lujbYloUZ\nK6RWQKaP7fKERWTqM5oee18vAKS5vCs9/5aB7nYt3KPyYI/GL5EKySlCB1Uq\nwp3otmz3xXPRt4fd1+r3HcC7fIRrbz9m1TWViWtWnsQjcjG7VZl65QwxJ2dX\nWs+H\r\n=gF49\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.10_1547053384450_0.6420540254990259", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-regex-util", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "379fb1a27e3c2aad55489dfb744e87cb663389d8", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.11.tgz", "fileCount": 3, "integrity": "sha512-fVQKhpb0WV4xMAMdaqcd/1gZXavWxEvfOMkrEmrQjIeFnuUwtlchC4UaZkaUiEDKr80Nsi1XpMsMDdjtPo9MoQ==", "signatures": [{"sig": "MEUCIHBnfqYMHMqxJQCxFNMAC3tqrBGZqtmQsmJWRPNLO39qAiEAgkrlxoZNoGodXTHeDN3cNdl0/WmxDLMhE3nvcJoSGNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN4/3CRA9TVsSAnZWagAAJoAP/3KAg9/uLdbvgLaf9H23\nLzw4K+p0BBvoHZVOjacX/YioagUzw5XvKoRFhKv+Ntqi+HkJwSJ9bghwMcuM\ntKZmddNr7I/Rb2Ex3Fvsxu5X0GX0uyKPCwOa9ERcLIg+uBgYKvvlWRYcQRVI\nqOmUS6oqx7X5fDm44ALxQJamaHQ2lYFtpT0ZnLanaEHT0HkU5WpWDpsTQoYi\nk7qPu7My4Bj1Phs7Xsz8X4noDvAAEkBq6Zme3sFuLMUOnyTPjIMKBn2UV5BI\n7bg1BtNn/MT1A2WxduZiWBkIYLdW106OkHOQIA5Dq/dTnSv5Qyz2oHroN2I5\nWvU2jhSurmR3Jeervzg4cA970jPy0Wg2Z12F/v8e1mvewBoaAaxNoU67y1Tj\nX7raZgQOL94by4oqATL+JTCw0BB0DGj/OOs1kbxFUKOoCRB0qnGIrk9UzJsv\nqmf7CZlKOht/qfNdmhys9S0+LxcHmHNyCMFg9vvQS3uyb4orFcr057galits\nNs65VJGapmME/xTT0OGQSQwKz7fwj5EcqwQ9cmiA9ebE6Ciwf4AsJiDThOgo\nKDnzKxSXIOpyAN8DY6Mt15Dg0tsiuG7TXioUozf87yPb3n8rpC1pkjzG+Z79\nhQo3skMMEaw4CSjRNU6vpzPfhFbcli4WP1vKnjA7I/bfdFQLEwtR/poCYvPm\nUVJK\r\n=ILgE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.11_1547145206638_0.21991191099200358", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-regex-util", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "952911ac275f8e61d6662b9d6fe43eb9dfb5f939", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.12.tgz", "fileCount": 3, "integrity": "sha512-7kH2nM8NCv9f56rs83VySxIEf2grzavaK0ngtiL7l1QviHyLee5J1FYLa1Y1D9Zz/EA798GiqGXrWD12aB3ScQ==", "signatures": [{"sig": "MEQCIBN59mDHtDP5vu6hDN4UQ35kWVBbxo23KfNGjSSfTHkHAiBOyFYEh05E4XKrgPDGet1xkF+IEw44iJl61WJdTnVTGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK9lCRA9TVsSAnZWagAAWOAP/37xc8BIJwbMRNXTg26W\nWWlHKBR0WbcDRIBPsGWa/tHFxDNdAjzFH+ocPdiF+gUPLtZEP3dc9JDKPpv+\nyAwYbEa8w1KcCGg83fFSvg4nrGaeDHZKpUs/lIaHKGF1b4EbWPh6cL37ANnd\nSEeEEkZfuEB3ajIPhv/sO/VXx8WeDVN4eI6ebm7JzkhHWKyzdlQRQblRT7Ks\nVIyWbZ5MZtLzlQgDpTAsJiNS/ERqXs26A/pappfHVWZbawrrzlHh97TOqiam\nw7oYXtlHtathATgL2gEKRQdjVgc9d0uM4M2sw+NiycVvkpJUjKu50jWJfMK7\nZ5uStwx25ZjpX5oX52bgxNGQNsImA+pqhfUSoEES8eU9SeKvQgPfw982oZBd\nwNdzq2jfaDJ80yYVbcVn2KS66dZ0B/l5KaBExQ/UHH/oZJU2if/MByDSBeb7\nU5Qjc9IHCmYyZqQ6ahRLb18Nvx9ZF2bWjx7Z3AOEWnzrMBP1xHj0zUpa0h3o\ncQToNUWmzcZ6pBBz1/B0KBgBoa0CmnbfYuAR6IvH6Y5dTxPNBEfoJ4DJoE+1\nUaLZ7JzY+XY86OG6IYRIL5Ub4JFDX04uNJHo9FzG9pd+LMilwppsLiQXzFm1\nYN6HQR60D2qQju/VPm4MfsEcN5Tv5dZlYdXO3vp+9HQ+TZFlr1n8l1hrQKTz\nH+XX\r\n=8x5Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.12_1547218788707_0.6026292355427418", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-regex-util", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "191a3168f45c5ede2fcff1340388c80b9ce56670", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.13.tgz", "fileCount": 3, "integrity": "sha512-fghA2SVADt3EHJunPRB4R7Q8RYsHhGZga3pqRUZFhpYyKllZoW5zVxoc88gI2yLbvjn0PNtpHFFfoHAK4VToVg==", "signatures": [{"sig": "MEYCIQCyERRMtVNkn9VEzC1RQVQ8qzQrwUFUKoUJZ645UK6CRgIhANWRWD4J4VJChCGZ77ZUM/i/uxPGuUqtfReQRpBO+M56", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUMCRA9TVsSAnZWagAANQYQAIc5GsgKBFlm0rthg0IZ\nu4kN7QT9ilzi53VnYvulvbs1X8qn1Fy5XTQGx8ew7t+a9aZugLjJGZUln2gL\n14dWTl4zopFHo9gI8oTved/a5TbSnYPxSNaq4F2hPHek+nwK6UTapZLI3QTK\n9Ll9Hoe5/a2Qz+kvZAmrD+9+FfNkpbD8jLVvj1c9AP7M3/BxROLvW3rObNho\nTyIvHmdSzvpwRLAqAVszzZ7Mei76aD+AH7WPpUEKmqmxgGs8V30ymYaDAZmA\nBlezKakxmzGyrryRqBZR9HeFPwRdC5zYCBzhYWlX72w2aiI2eXDa+embGRiE\n8oaYXljORtne7KSJI7ltIbONjoJ09kyQAnhCyPuNBdCmVvyVTI867k10mrvc\n0uQjbLZ0s4ooyequp8A4I4AtwlCHFOQcpEtBtZFvFRBE8gMZzIedmA3YIKBP\n5oNK82NgQbAbTbryjvxzCd3vPJrU2dr1wTbK47fvKHr9oPt5CfE4SJqsyds1\nVAYgPfNVyAON06aXIcLkPDaWVW6GipM/qA3hj3BVgmseSGZPmkXqH5bV2zKR\nM2J0ZRkXgeQ1cxYzBbGH22rW5HXFJN+/ZNM6gYKsn/Lv5x1WcoR9N4BGyPzi\nIvpGKixmkjUOwxyFcDVT0RCmIs5mNx/JkYZMzKDDZwjiCWPhC0mYwJwwdWv+\nsEbV\r\n=7Szd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.13_1548256524200_0.10844291460242395", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-regex-util", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "94421a2487fb17b33cf9228ee1d6e85d841ac704", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.15.tgz", "fileCount": 3, "integrity": "sha512-S5ffZZVBMJdSznkaFQKVYykyxUsNboNGalS0bLwCT0u+EF92ZaCQdVIB82gE5WJo8eaLAmBT+8/fv/aip+1S+g==", "signatures": [{"sig": "MEUCIBZFqMwgwT7LoErLQDBR6z/3CBrEo4uP/ByA5UPCNze1AiEA0XJYBxki3K+STy7peW2H7sfbid0t6LAiqvLPViyPpHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftbCRA9TVsSAnZWagAAwS0P/jlYR3dHoJ3p2ihYZW6y\nVidX05B08SSfMhCOOp72q+yNW26KGkYISlkNf4nLP4ciCRhvJQuaSg1tjBYf\nIJQsOCHbvWFPJX/uFnBBTuhMHits8ppz6OQ1lnfhvdvfJTWkKBMfeqexDbt4\na5cm1upvQScjEhgKjD9zcShxneFt2JBDdQ7ONAgizuwBjaZNRBdh6MjaRaK3\nI4yRlearHCaR+9MjUfEydWAg4KA7Zi9i48P2iCTGMTuh4D3frvxAPx1+jhwf\nTjPrAnG19kzNe+RMauKXnRMc5N9jKsdvouQ8hMCyYQPWZSOBQhskXJKHkUsT\nw7RwrvzLywrpam0oI/Dtrbf3N1xwiVmoQQI8qgpwCrhlg1z0UPQNh6jYY1nt\nhQNn28yLWDq+yiTQRKhgkeSUbw25shiyrmJn/cmuuuB5bbcllNvpzOXqR9Ix\nxoJOKXIOelMDlQIeCtb5t2c/+Bnems7AWAWQ7DGdwwjiMzG8sSm0Q25X02LF\nySw5hky8mTY2AOxn+bunlWqpNv028Waz9DrH87VUvWU6tXpLntKvV5Dn53io\nHdO0/mWqIQrt7b62ZeuP5tu0xWcDK6pN00wH/Br+tNLUSHYo9RaIA95BKzZl\nMeVRJZiCi6ccpAB8/szZf1FlyWh+a8PFolZVnBwYDX81OOcMQ6qzRdrPlisp\nrooF\r\n=fN05\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.15_1548352347340_0.26928795053237065", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-regex-util", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-regex-util@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a52f91800c3b0f96f38fda475b7ffc864ddedf32", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0-alpha.16.tgz", "fileCount": 3, "integrity": "sha512-t9r8jPAhFR5GN2vGlsoUNymVs9eATTpoZ5S5B1wLAebr7qZ6AIlrl2RV/HnF+rBzmb2zxU1jcDv5qfwCav+VGw==", "signatures": [{"sig": "MEYCIQC5HuU6t/DtPMFDuaZGsB0r2HZX6DkVO3BYO59y9SxSJwIhAIWBIO606YMcvwD5OKYMrxGHzK6wga/qvuIW9YhVj1T4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIlCRA9TVsSAnZWagAAROUP/A0JDvH8cU9MwUQ1xPxo\nzv6fdTEKcLf1VQGbTS4hsrYhqOWpsWdjotP2JP8dug/7KHusZCKmATCGxEkv\nyV16Zt7QfsYr2umkvWVp5LKDfnSK722Qf5Oxc/lQARLwpkzifAvfwcplzNt6\nv5wiGy28tqbLsffaeH0f7ybIpE09KPuIfXBOwGV6Eff5H7kGswpYrM/+LdDe\ntebrid2qHjCXCyUTC917jQ5hGELLwSSBSP0rBkko+EdGhTKpjrvWgfUI+Tjp\nuH10qdD3RQFoOo3TmgFrm99cYAteLTdBN3x4pE2b1ZGk/IQ0oS6rkvjRLAAA\nn899tvqCHmu0P3xL2D9jbBckEvxnzwUCYiMfQh8UjFQ0z+gHJMWbwrxmk7Iy\nhU6JUQWC6vG2X7oNvprXFo+IeUn7WwEj5LLNtuO0ioDwJbMH4ObFwbTBJMU4\nk/nwMJRgY7m2sTgp5HrpJQp40g3Ti87DxMvt+0NseN9l8qkyFBTifinDkPB+\nwcm7dF3ROhqpi/Bfx6iLE7uLvpkmevVD2+okKBiW07czHctmXMF5jFqILRar\nxYOiy4sgWoEWtbKmXripU6ymnKcvcz1vjvgl8k1860e4bkrLvQbVyTbmZH73\nZ4ITefj6Pw4ogNt8XrGSkT/moceLAbOamwym7bTVNUusGWX1y4AUvW3OIlo2\n94ed\r\n=fald\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0-alpha.16_1548423717163_0.408251326197985", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-regex-util", "version": "24.0.0", "license": "MIT", "_id": "jest-regex-util@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4feee8ec4a358f5bee0a654e94eb26163cb9089a", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.0.0.tgz", "fileCount": 3, "integrity": "sha512-Jv/uOTCuC+PY7WpJl2mpoI+WbY2ut73qwwO9ByJJNwOCwr1qWhEW2Lyi2S9ZewUdJqeVpEBisdEVZSI+Zxo58Q==", "signatures": [{"sig": "MEUCIQCnmVmomnQSLiuGGaCfhu+jdtv6GZOygrr/d+NIIxlEAgIgM9yHkduJzUDCBdcQZjQs8Rg/SqU85lNllOi+GDLvgoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWWCRA9TVsSAnZWagAA5H8P+gNdaGvI4+p989zHUvpU\nDELucBjSuREESYps5kH4Z90DAZ4tvnaxzOONBKAgazM3ZYcrg9mE7ysgDjDU\n4QDV3okVGDlpYOJQ76ReUs6u5Id1S5YJAOGXvLJuERS61TlBgY4hLfTvVByb\nMOWY5qpYgoQROHG3rDlUi6DGQB+T1L1d/WiqmSE/zxuj/i/7nN0EQpFauBkn\n2+aaJfhmyhk0+vp/qGPHX9G20aEaPgEiOnLVLFcfE5HVOCRnMQOQ5W88FWsA\n/tO81LpKfywG2OqfijUxID3604SpYeBDrhzBYgFvsHYhYHtE2OkOBofwFNO6\nP5GTjLPfsC6e+T/+nAAKj+tcxQyQoRdv+MP5mHN9PkqZ6eiX1jSpMrrEIrj9\nUHCHOQgEVlZMNIFGUo2BBR57alLtYDFEWM3r0xaf4Yyo18emcDvZQZAV45yO\nlaqVeWVPUalLZwqyiXrkum6nSM1tgTzXKMMQ18CfnmIOYsjDFQQgYZydEtmd\n92Ru8tmYqcNPuxqN/BFwr4GTXx49bxX00fDwXeih5aDZxallTaqy72hQQqC7\ng0ifGmJoy/0Rv4UXzNHpsfJ07eiGV6kpeu7NDbt8nxpivKWxvYCK7VrM6dMg\n0wZs9QJvyzpFXPf41pIuX2D5fbjEIn8Hqenx+c8jZYMDBTLwZcLb9AJDCih6\nR86W\r\n=v9Dz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.0.0_1548428693651_0.17930015629156948", "host": "s3://npm-registry-packages"}}, "24.2.0": {"name": "jest-regex-util", "version": "24.2.0", "license": "MIT", "_id": "jest-regex-util@24.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b910edbf0eb0d94cfc6c7603f496832c897de87", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.2.0.tgz", "fileCount": 6, "integrity": "sha512-6zXSsbUdAPIbtThixsYMN+YsDW9yJ+ZOg5DfkdyPrk/I7CVkaXwD0eouNRWA3vD1NZXlgTbpoLXMYYC0gr0GYw==", "signatures": [{"sig": "MEUCIQDx/aNUj0T8ECH3uQbCrneiE80Qk1GPd/GRol+2uMp11AIgA+ILCOrvVOJ3PjZM3GcQ7cdZHyyAckfBwVPnoKRsVRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcflwECRA9TVsSAnZWagAAdTAP/3Zf1RTyf8kYJqwyQIt0\nlFMj9YuBQbMYhxE447ovngTrmCytx8wOgKK4ruOSPdJ2ZU6Nw+V40cBmFvu5\nxodxvIsGmikMu6pvx0k0GcvqX6Ffk6CnKP58ojJ/uwRNtCygT4KgGk+2xVH+\n4iunfWxUBlydZjVSxcbR0cIRNGcGI2+5EvWbXZat3mw6uDx/yHkaflUO7ert\nfgST0HrTBlLpDoZNdBwl1GJj8jblk0Lmji+mbYTd9cwnPBNY7GtpQ1ugFxU2\nZImZAC/8zrkZ1HbtBMpFSaUjawfRFMoO3RpQ0kemGlvD3f1USjO+NvGvUhHh\nuCD4vtpJHP9m66OOKBiVdaSdMjwHfjvKHeRHvybc5eth6bDHD+W5//nZprfs\nFDCaz+3AoHZ8wQx8InkPslhrIuGCA4CVHgOxz62pAFq208aF7/J1+kN3J2et\n0ipHK8cS+VjbTVSQ25Z6ux1Ebd3vGlIJdk7509Z8dfujuJ7wMszqziFPyvrQ\nkGkc1En8bgffKRtTZR0zEcm6vn2ytYZgSYcEeiCls4+6J7aAukoXSgKqlq8V\nF+3YufFuail8ShISSy2rkzeHNguQPbbM33Taa/qrRnhIB5fmYau80MvErCqh\nUa2tbbweYYBvajHsPO6c/+1NXwnsQkgDw2okG/UIOvVVV5gascW3GLIHi+gS\n+Cxl\r\n=VSxU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "d23f1ef18567763ab0133372e376ae5026a23d4b", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "This version was accidentally published and might contain errors. Please use 24.0.0", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.13.1/node@v8.10.0+x64 (darwin)", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.2.0_1551784963747_0.7090499370413597", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-regex-util", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-regex-util@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "24b0e03711373c26819c5ea1b4c5a129d2e56008", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.2.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-VVVmkuso1A90SisLOWG3z+/l+/Afy53PqadMkmPl28KEYx4NUMzE+b3N52zj7s1GbtK2Y5SjRffWpG8LoZsyrw==", "signatures": [{"sig": "MEQCIH3YUU/h2y5T5qZMpG/1CwIxa5Zf2V7Tvb8BM/6E9lAWAiBQUNjhbpe7EJQE+ciJBqcxJTFd+9tAfjsqXzIjMFgocA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovPCRA9TVsSAnZWagAAGqsP/jcW6UDnmoma6awVZe8b\nFMUTppZJvtr6896BqJpxDp7cJOH03QbpbAs8QKzjIp1BoR6t9X9E3wDc7X2D\nMg1cXGYb3tdATOA60yfwgIaZndUxZ/iEau3ixYS7gph8MBmFAiiFBj798eAs\nOjWPIXdnusHgTPkBZof+dnj7p30pbAe4DigQlkmnuvvFLG4Kk2UY0pahgUPy\nDXILjmFkn7MowAQ7hDcOpfOYTMYewoWHD7dDUIxmB7QD4P5aLi5IKUsG6fJD\nalsF+wBX7CWRp1Lsu9z5yqHDa36E7tTfS2GCFskZMyAYQANOKSUISxFNUhKb\nJw+LjUT7osjKjGTLTLkKVn46GREPxp3iNOrXKpb4ChFHFbLh7rxB0IuNr3bR\nlLe8gZNEdtmtq76dKHFqCc6CtNU/9XGlS2v7QliNsVfUqOkHrR+4drGeoh8U\n9XtMapqiOAYQG8xhAEAUJnBlE4/WnWiyykNf9dWZMvOfp72Pvf5a5fcYQPHw\nH2eiQlhHbI4qy05Cm6IVdxQvI2xmWK42aVyWi60L0fGwnt5cEbE1K5SuGQFd\n+KseveRTC4tH/WzewUMEA3sy1ftIGJYbk9/RhK8tZVtEKIdAG2cpRGNbHBG3\nilejlwzJlz1FWQlct8nRQ4uXvEmfg02isPXnmK6p+f73lavqJ3c6j4w6tj8o\nbc9e\r\n=I80Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.2.0-alpha.0_1551797198714_0.8004109249176552", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-regex-util", "version": "24.3.0", "license": "MIT", "_id": "jest-regex-util@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d5a65f60be1ae3e310d5214a0307581995227b36", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.3.0.tgz", "fileCount": 6, "integrity": "sha512-tXQR1NEOyGlfylyEjg1ImtScwMq8Oh3iJbGTjN7p0J23EuVX1MA8rwU69K4sLbCmwzgCUbVkm0FkSF9TdzOhtg==", "signatures": [{"sig": "MEYCIQDwFEroFg2cd4WXqS8ZOx4PVVxbf88dnMmqfy6I3TZ9MQIhAJqkd93e9nxt5ti/4drkI8x6cjtynpR+kdU789BTL9ZH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWnCRA9TVsSAnZWagAAclUP/3LpHBqpv+YJsPYa1LW8\n8Bd4RrVaIduXLzeLyfm3q5j7zP/fqU6/SPifENwhPoOMo7hXHAFtlR8JxWq5\nwmEOVBxkVDRY6ML1K3X6/ePbkTgJGzVkgk4BvtuOsp6DCBhoXddIFjZ50+9l\nZXtoFIEfihb+7oVYqvjJlJiJu0uACs8B0t1EG+KU7uEy4ECXFmPs66mNh0da\nZ5jk+vtnNxgkq18J3lwrFi7UqdGt68E7PFNV7vBMAsuEZfsmgNds8/KsChCM\niUC7/A96jyqZ4HIeOuz8vw4wh7dgSikujzh+4wgeXv8y2jUvw1X4ItM7kHab\nbcdxROCJQK/swHGJi8TeTpbscjQKvCyCUiJYVHPtbs2tOPx84BW7QLNsQ6Hy\nWus/dxAQNLqqQ3SNm8XhFOjHIsPs/iDNb89r+PzM4kJSnMtSK2Mq2O6DrFSd\nSZJKFfM5ndvfglqzevGPPWCKanVicoWxamgYKYkVZZsreoEsyW7rDnrblEfv\ny9z0GSPnM1rVprO1PGVK51XMYukNCdjo9uanpeb5FlVomRyMg0a//Ss2aKZT\npKX81NxUWhJT7Y0vEZUs9nYqxQQczh5HD42eqw2qoCWgApcn6NM+yczh62vI\nKukZAxsZGCpTnUPV/Wr7P9XXvSy6CcL5LImXnsVLgCE6dhcbDx0YQozdl+1y\nCwNT\r\n=wODh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.3.0_1551963558697_0.4887254487238022", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-regex-util", "version": "24.9.0", "license": "MIT", "_id": "jest-regex-util@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c13fb3380bde22bf6575432c493ea8fe37965636", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-24.9.0.tgz", "fileCount": 5, "integrity": "sha512-05Cmb6CuxaA+Ys6fjr3PhvV3bGQmO+2p2La4hFbU+W5uOc479f7FdLXUWXw4pYMAhhSZIuKHwSXSu6CsSBAXQA==", "signatures": [{"sig": "MEUCIQDAi8mUUFiHKAMeNZ9/N2/GGNRR/qjVscITDTxJ6CmsHAIgPLVFNIgsDYIxHh1/XKf+qpgpxjpcgv6/+WLf+yDOw7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAAUjAP/2zoYxbRK3NMeH0A8hJ9\no0YaC5WN4oTd6LSnRSXKRzPtiZo3+X1C1E2GyNF2CWveg+Ogc7kszWTZI22B\naTwDKn9UW5L2j4uUbWj3WaeEptaU9ek/C24iGXIn0GToMSBJXGapI2+kcirg\nzE/UXcPrvCNaAttLL99aZuIbVAPeEKZBz3gK0oybYI5muRJGyzS6V277elve\ncazuNjwDeRfxLVlLH0gLXlYBzPHe44CwP+DWWAPUJP1FQGX07uOXv8OaVMB1\nJ6T9vqCdSHitsnOwAtn3QsyuhIJTqu7i+gYbucjP44ZFB5z56+GSvCn5EFyU\njpefl03CdMomqAeURXSQnTX8mw6le56jI8FotqoBPIIt07tZe2LQshGgeFrz\nX8kEVbMPPd5jkzVma3A5pCq1CF91Fe9vdoFcmX45E/r/5mhdioiUBy4YExGE\noISHC6Xs0xmywQprwMil86S60jTp4AqHqePCeLto4BH7bhEYvAYEXjhy+j18\n3Z+AyQVWkqTv2ngqeuwNfGjsFzKo7W2+FRQr+L32INztlKeLg+LXvSlLRZR/\n12pelPeHtxEsp+BaCE7OM65MOshmyXD25W/VlZGbJaK8PpBUW3yO6LeLOdy+\n12o5JIPrllBmYkvzMw7pkuUEV2b7SHILllAHLbxYenZfXt6bJM+ZHpqiJKvk\nsGnV\r\n=kT/j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_24.9.0_1565934946915_0.7628211481338474", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-regex-util", "version": "25.0.0", "license": "MIT", "_id": "jest-regex-util@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd4226e8f42f96d77fe6fd3b72694fb66e5be5b1", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.0.0.tgz", "fileCount": 5, "integrity": "sha512-WJ6YhqP6ypf4wIX6aReDP48+/WrhQnp3YujfFXScKgkYEi9jzTa+shQCET0p4KbRC51ML7dNVkXYo9kO3mUliQ==", "signatures": [{"sig": "MEQCIHtZPfR2e23mzM8LdB0aBW/lz4yGjUZEUh0iDZeB8ZZxAiAXUyZEDddq41A8Tyb8yjUdRqFspDLkcDXP5tjpR7xIkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrACRA9TVsSAnZWagAAkvwP/1ixX810C6xP7kTgelOl\nRh67zGga3btVrD3mt0o6VU2ZHTfNhU+/OpuFfgir5EBkSDH/w0Uo6DeYjmqq\nQIBjWVHmyS5NrHlUpwXcmDoa6WJHGmVKJ2seTKUxyn4z0AG1KYWs6gNJcBEL\nRfiMkw7eAg5X3u/OeyXAWmI+LmbyRJJ6VfQOH7iZx3Qy0wE8++WexSc/PznX\nlKINpj0uta6VCKyfA1j0TYxSDYj2/rlK+q5AXXDPoyYmS8PKd0PBwzpGk5V4\nfeL2tiK11De0NIRkJTTfDDXBA/v9FPsdvB4lNUc1Lx2Mbl26FTr/HcQxbVIO\nGICVhrRFjA/s39B5BbyF3WA32zUjJSkWUGkJrQqVGk2+I2Cfuc+1qV4lC8FZ\ncOt/3tPj9tl+S41PTzv1/nLXpYaLsc3x/LeJFYj8hktUp0zSh56ZaS/v8FeQ\nsVq3omzoMi2PqA2C+b+Y2GU4mo3JnaZf0NmSw1kwuEmeIPXsIvEilIJZ7hX7\noJX2SB9Fk5kCvR9SAuK5NYJuxg2oDliOHOAxCUPH2KGWEaZD8sjt627GgJqv\nU55iqgx+9v/4qiVdsk0H2KO3M5fOr/8El1CJCPFqaoEJyxG0aU4kK+OvWpni\ntmW8108SYnZHGBd//YGFq4rigz9pN8rhWPXPpUph7dYIi/O375y8d+9aeDoo\ni6PB\r\n=z1mO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.0.0_1566444223887_0.2583953696025305", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-regex-util", "version": "25.1.0", "license": "MIT", "_id": "jest-regex-util@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "efaf75914267741838e01de24da07b2192d16d87", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.1.0.tgz", "fileCount": 5, "integrity": "sha512-9lShaDmDpqwg+xAd73zHydKrBbbrIi08Kk9YryBEBybQFg/lBWR/2BDjjiSE7KIppM9C5+c03XiDaZ+m4Pgs1w==", "signatures": [{"sig": "MEUCIQCC96YemqcLKbnhhZmXq4Bm2wuCe0Mu4+FRQPZo66VF5QIgODhTNDoSNURfylwoop9BzgPcfOZ3f3MKi1mFTHmR9KU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56ACRA9TVsSAnZWagAA3IgP/0blUYAM9qG23E1SfpCn\n8chBV+vMdmdYa+we3AgG9sP7+iEr89GuWWSqbgZ0Yakw7KxwdjyC/GhLautd\nlIpN+fElO4VcXr0Eayu8Xz62qKKrmPh4Cbn/WrnF2RZ7dpODf0fb2AIYdPZZ\nNlMSvMZPl+BZpmpzGjvAaTl26+Vxm+fCcL37zd4v7HWQbahF9yPMcae6jSvJ\nwTAM77nwzzMvz4+sa7S3+jDj84LV3e5VN3ZvzkUJ7sC3n5q5N6javZMIGYiN\n0H7PMgREmGpR3EhX04Eyu3ii0Dg/NGoOib2JEdRTViPswvcu22TXPu3EVPLa\nS3HKorQHC/vP5gyDokErh8WbK/S5cV1Ue+660vsWjjW8DuhIA2F4Bv7w81xR\n38cwaQa/3AspSYKY2abVzLa41CVc4W8blFdIkAKTIJ2WAXmcf3EtYIVGQkKh\nXZvZDPa58ccNcU9CmpHXYXnwAhIdXdJVI2ycnqKtHJT9tCgCS8aW8vb5FicL\nj5CYpOxYepnz40KtrF7C+hXbRi75enxjRE/MNB41F+Luy3+jKiOTt+jHudD0\nzGxWGRUvBJ+dJ5ahOISqjVqIw7dVRp1F+Qq3yvXeJFF+euGWnnetC+enF9ZH\n6YGgyACxmL8HTQTtalpXfwZZapqBPUhk2IyUsVD09SA3p0pTrAxfwL1PcsJX\nEnXT\r\n=sfjk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.1.0_1579654784362_0.6627369272401598", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-regex-util", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-regex-util@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9aa0d373475438970fd82804c89c4144058d5b8c", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.2.0-alpha.86.tgz", "fileCount": 5, "integrity": "sha512-q7FUCZWaXtej4ZxtV7gVJjQhRe2URegjlAuroNoGL+ujQH/rcANHfDsnThojHOBEAEYvl0NqIOq7mp9PeHKRFQ==", "signatures": [{"sig": "MEYCIQCbXJ1JmGeDO0qo6YzM545v3DO9B6p2GtExmRpjmJn4hwIhAKzsDzzoV8EuQnqr21CqTNubTWd3ZyaMQMs+DFz+hwnn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HbCRA9TVsSAnZWagAADpIP+wTazadLts5LLDBZJFYD\nGUORE7ciX56ZnvZ1ebnZ4s1gQeMV8DbRulGSUIWhHNKctKQhoOKNky9xYaAV\nfqJXaTxcKf9Z9zVRsAqs92q6yGVLkKh5zYeDvowS9MsDZ0gMMT3+QBrD46CJ\nweut+Bng4SX2aMrcar/TlAB2YxSEP2LYctI0MlfRPAEXe0ShBY7Qo1bMtW7V\nM7TKxI6NKZPryqa3TJtXvR1pTcjO12wv0v/5XHfQw0f1mCoKgGJ0BjITd4Dd\nSsFBEMJA8J9ghp1IMoXySKOIeiCLoIRD8bD8TTWOy2dp+9lrL2Wp+A7xeOjo\nhm4pxZqZXXf3lKNbdbOBI7VwHiN8zTerHSNvZ9TFJAbAybmAuI1PbexMZmdm\nYoC+hXTCiYbod12c1RVnrEd3Rur18On3BveXGE9GYv3POqZ/86IHJZtjRhyr\nJuCakXo8Dft3o60gKKQGKmtF41gHP5Mz+0oDtIBV1fY3R4YXoCbY5RVFWY7V\nQyauOBmW5qtJiMCRGsBKpmqcBqgr+LgjzKTSB38mxcOIBTe7VMOatwiagbJ8\nZZKmgXMmdCS9Ef0LY1yh17VCzZvex8qSX5yNiVk5YRdwvY5tndU4mzdr1tHC\npzm7hU8UfCUt4jP1URRkQUePzuX4U9DiOhVPZEeMtoYus8ffxl9Wey1HJ3jZ\n66rR\r\n=IOaY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.2.0-alpha.86_1585156570869_0.11844666629546685", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "jest-regex-util", "version": "25.2.0", "license": "MIT", "_id": "jest-regex-util@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1386764a0f57b79a3d930b628ca83696c0ac142f", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.2.0.tgz", "fileCount": 5, "integrity": "sha512-D85pUKyzdi4zFAnub4EDp48eB08oua2aaN8wPrcaL98SnmJmJCSC+8iMZvonyy8qTtXgElK8JcsdPl4Y8+WhGg==", "signatures": [{"sig": "MEYCIQDn6lltDTI+yGR2QT5u0evYH4mWnDuwjmQQAf5KehqxQgIhAMOkfUc6/8r7dKflffIzSm9XIpckhlIHbxCZ3J1Ip8se", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uiCRA9TVsSAnZWagAABKcP/RQTJmcOaga19TNv+7h0\nh0BRmQN4enJB0d+d+rWfXmGKFfOipFN1+5Ec/sfN2Ps3RJrv6ZrVZgPuncnG\ngc3UKqao7YIhkw5nIsoaTyXzhc8cNS/Hj/yFrWcUhteQMyevGP8eJkf+Ttc4\nkVlPQohEt8WJaQy9pvqcaBaODJDvguwxtdXLhILu39tdH5BrJBUJ2tMOPN5M\nRmV63o5ukj3RIjiVfbjgeTqyAp4ISwRvCWWq2LNlCmmPeKdmnqIvKyawOXSz\nVwxLElEC5WWd6Ze4hnTaDg5a5tmmaGoZxbxVEp3bism3Oo9RO5UsOv7xLVu4\n9zD3H1Em4hbbNatrpaDiXBz7SYTAENQDnORyGTZbPXOBSeuwANQ0OEPInAiT\ncs8QNWYlMZQc+880X2KARBXBs71FjzQwMzojDe458tRJwt1c0VgKRJRxtS8m\nUutoeDvTOGV31L584xQXW2wX6R0VroLRBf9nZLEPET30EJm25JXGO/RZs0M2\nS27/7shDq1xpGbB1jy5aPW2d0oSSIwSl9Kq1K+NI6TmMR5aPahUwuxuqDSiA\nkL5HxWkXSyAenYwt3EHA7XsXunvCnYdhf781ZBwHKP8mfXH7qktCixMQYPlz\nimm+k7r4AV/U4Xq6lXTxCbhlmGgbc6hMtvnYlLN87KNc3Cg6bX2gyhf9gES8\nGKhr\r\n=mA6V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.2.0_1585159074160_0.3075106020642151", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-regex-util", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-regex-util@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bbf3b28dd3640278589e7bfc29e444a930652e0e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.2.1-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-YE7z6n0FNtVL5q+Y8yRrL0Dw2Hscs+tyFBVcTCPbOVa4l+E1ESpNmzYyaVTwCmEMD5rtxqEbRdEQMeFogunDoQ==", "signatures": [{"sig": "MEUCIEjqVXBm7otS/Ci/ui7irOjbvAuoe3AKqJqj+aNXiLwLAiEAk9knV9qqkB/9xydVyRvYydMm0oG3451Qmox8FwkXRhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+lCRA9TVsSAnZWagAAbEUQAJFI+pUeaiG5ztGSmEnk\n2Z7yDvd36ssOsLBbSH8Kf9Cvt0gqxr/X6Bo6RQxtazevE3vdVyyM8YShL9md\nWQp3Ms29Om8yPD8DYwcbxsNzu2aWMvIwfHg3/jQhkr4rjq6Dw3DtZ2OvvKoS\nNoeK45hvO82i9O82wVcf7lQZPQIHPTOrLNDJMIrjAxaMACIqR50SPXe0vMil\n6VRKPzW7Hf+snbqpK/XXQlNIhgxuyjXJhBlG4K6oIvKc5SJCV9UT8EWoou1w\nzLTZVisngx4Mu4BHICphEp1FOuEAX/PrBjEZZpUW5THVRjZjF3ClZvT64DmA\ncF3C902nhz+BWoQqWZXVsOOvaPQySGOzQXQpDcSR/QpsJpcm9tJzIhr075G0\nS+6e2JXU3VW8bXC9s5I4nGmvO5EGsu4FYegTKKTztcVpNA/U8nuqYLPSF9tv\nGNmYBBnACLE387Gg1lxYFPMaGTl7iuYZCqbTTcw4fNvIpdbI2h/4oc+x9l8U\nWTZyULY58Omsi0EP1j17+uWQWQjO3Vw3g117k518zd4VuBUsqS6A5kgCkH3D\nHzsgLb++pMcqpMHMVUU3mSFcirblmoOn0w2sFEjjFga5vGlxyS3bACjYJakf\n0xPiycmW1n+I448EG1/HpY95/iE4xicLW0eObtgeVgO+nqSMlkansiyYtGRr\nsaKL\r\n=YxF7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.2.1-alpha.1_1585209253348_0.47276501670676563", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-regex-util", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-regex-util@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec20bb62d7fc9f17d9063162fd200a883ab7255c", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.2.1-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-2NzXGanC4iYL/PRQ3zBmxvDBDXocKuug1DeG9pLPNMzGo5Djy38lRHK9WUKQeKPBa0rsEvIk978RMyOvRz0Rdg==", "signatures": [{"sig": "MEQCIQCL2ShnEGdey/zOo4s+AlyXrL3wD7dk0KG316waeHHZcAIfH9l5HRwc9YsnNQWg+TaVRi97A/DbTSoBB50YOTd+JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNtCRA9TVsSAnZWagAA/XEP/344w3jrb+pO9hMnKk4h\n3jBM4QQQuK/I8CNapQn33tYCvn95AGTDvaDrhrVBQ+9f/GUSjLdIVsMbxMqj\nrowSvBV+k6uiD0acj1JaRvonohomwN9bFEkKuxpoCDAoWVEqD5twv+b8L9UC\n7qJ83sK2GHXafhWv7+nlcx0a8s++JgGXf2wOZ7nS1fz29W3ZXS3zznYzFvyD\n3lIa3NwRJ8ITg04TjlZaoDx51L6Xt0N7EW7zc2pPOLwIKviZH2ArfIHfH5gB\nSUfLeoHxSyPrgfIwXxczuo1PnLETSkayinGmSF4nvv1nrFJr0lzNdEYJ+gS7\nz28SKgkpOJMYQ5lTYSgqddp3lSnM7+HBgLkDRGpVCFQpCBrvLFU+kaD9b3ek\n3aHb0S6ilAsbSg1dlTY1fyikvHGF7lFl8k4F5Ngb8M5cfWGpWhgSh01myiPn\nIfFPdLIRhymsxJr7z8mtaUzeu5PgfADiszBlN9E1kik+u+Yfsn/pXp1N5N3Z\n7Ejn+22iOpg1U5Dyrurzy8TBNddrzJpEs+bmrhf9Rk1KTJL7URaL3nPWYxt1\nVqTDSjhm1CDd4vv9plel7LcPWNXaWA8+d/ZTWND5MELcDleizDAt4XCXjx1O\ng4sPRYn6pTzedH2FJNTMSgnLWkVIBzMn3pHK0B54+lMicZlSGmMkvvwI/kDw\n1ti+\r\n=4HLA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.2.1-alpha.2_1585210221095_0.8353314018620457", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "jest-regex-util", "version": "25.2.1", "license": "MIT", "_id": "jest-regex-util@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "db64b0d15cd3642c93b7b9627801d7c518600584", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.2.1.tgz", "fileCount": 6, "integrity": "sha512-wroFVJw62LdqTdkL508ZLV82FrJJWVJMIuYG7q4Uunl1WAPTf4ftPKrqqfec4SvOIlvRZUdEX2TFpWR356YG/w==", "signatures": [{"sig": "MEYCIQDPEDgU3bAMtfU0x+De+xMATXOV2UINgW81U9vY7hBvCAIhAJ9BBT3WiG1ADgMUsT5TeToIGG//5nh0b2KlEoAfzOBy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9QCRA9TVsSAnZWagAAiNsQAJBoM+Wrm3u35E+UPg1N\n1DA6qx0MH1PYpVAI21Len2xt5NHReWMbmSL4bn1pRl2tzltJ81zTZusKYUH7\nbp+FaA3UrhEmWOBcVR+gxztJ8bynukndN6zYiiKaaMjCenR9sSqs7P1ABvvt\njElM4PY+rAaxj+Ll7lfY30OcSMZ+MrGf8SrIfwy8/8fv3okmmIiWGql4IIzf\ngQg1sQL53YfH1BJFQuptNxHMNjddZdZzYTeqkKJ5XK6+edgDtT4B1Ptb6U8H\nPZ1aXCRcVX5l/0m+tIgC/xEGg+tHspIChR096Dvp8h+gZFFnLAehzRYYd9Wn\nCFXH22EfB76F51aBsUhREv+y1a/9PjNHpdiRFML56zWbZ7cCyidluPBlTYPs\nVrgSwQfqUL92vsxOMmHqiF0fALwvfQdgNFvjLutoB+N5ESq+/ODV3M2Q5Qwv\nPTq0HO8qYWr6UqAMMmzIQniQ5D18Mw+X5RHUtFLSJMhw9smQvUmz9CZl1Jb4\nE7KLwIIp3O05wQgryCDYSpFpSe915oBY8PrS8qoQ2qbImzuOn+nRyd2LLnwE\n1f5FDY3csDFO6QE5M8gxTn2vNr2wd+5Fr2djFOx7UrZyd0rzDkSnrNpDR07Y\nPJc9mdJ1mqX8xbUt0XDeZ2czztFlMOWJz/pzdLQzkzARzQtRoDVof82FeWs5\nGLVw\r\n=w11K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.2.1_1585213263929_0.36905997388004064", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-regex-util", "version": "25.2.6", "license": "MIT", "_id": "jest-regex-util@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d847d38ba15d2118d3b06390056028d0f2fd3964", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-25.2.6.tgz", "fileCount": 6, "integrity": "sha512-KQqf7a0NrtCkYmZZzodPftn7fL1cq3GQAFVMn5Hg8uKx/fIenLEobNanUxb7abQ1sjADHBseG/2FGpsv/wr+Qw==", "signatures": [{"sig": "MEQCIERIaUlCOpSgm5xpY3+A133JQwqgPkpC7f3OhvLDWraqAiAD2wnozJ7haXbIvCyJSz58pLhfynyuA4m+KaZ/cYleaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb50CRA9TVsSAnZWagAAW/IP/AllLfTlWc0fhf4wdTUL\nQIIrSxoFJRoSGO1+b8zUGhmPHyD2igXwAwoHqoGDNfi70sWlQLqLKw8bB/gA\n17HaxKs1YKGzVYtdW8jI9pz1bLWCszyAHkDD6nzRrGleWP5iLQIklt1zRcJk\nV5fxzCE3dNPAJ9EZUfN9P2MWqaTSGnENASUEbL1mrBB3R2VK/5iy2eQpafzX\nJSICVcP7RWi+OGIWYyhMeumkDGwQ3xNeD43REYiqjpd7v4RD3rZ2eOUs+q/Y\n8xgafWp9uY0oF0IHo0sZdXs/WhKn8O5uZy9AmoTNePmuZWC0oonE8IdI1sbs\nR8IsLYrA8wtuquqii5AJ7aUqoejOSIt2si6GPC4jFi4l9Kgy9suytoXZ9NQy\n66Kd+0J4f3y870GFUI7p0ZaqaLO7uOHnP4ilnu9ng+RLOfd4YiQE6ehyHwCQ\niKjzWcl2gnmVTt9RYk9C9UEnNt1IHRHrfvuU0OsiNQH9mn7dm2kEEQ4ekntN\n+q75SEwQQ03Q0HXUGwpMtIGevNE4b69YankthPqjtNHZzHq9j7LdeaV3H+Hs\nO237iaAfNE2ZaAiZhQ7v1pPpSmW9Y73QfaPCol5y+zLVUi8y4IL5TZRTGuBj\nhCK6OqTXV0T89wtcmV4wPuslZiaDRFKmbIxaQynWAmYWk1VNj5U2rdmE3nhI\nD4M4\r\n=bvQM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_25.2.6_1585823348027_0.3813968283358129", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-regex-util", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-regex-util@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "85be2cfd5442f6e1b9cb2787816eb3cc87ac0ca6", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-26.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-p84mskmQL4p0D4IO7tpktQpQgDUzSpQ3dVAdXggPWYi1nq6nrdBNyI+4Irp4C8nZAJ/j+GMa4tiOBvy1T3I/hw==", "signatures": [{"sig": "MEUCIDA6OQPT96/BZZKDOio9K6j6xRZXFGX5v1XwmWDkELwhAiEArzdCk/rxNVvn9fJRnwJRkakGNN32nPAwYCbS7fTZXgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPFCRA9TVsSAnZWagAAmLMP+wc4nDWRyXzrNC/6Phy/\n9CXwSWGWbIT9QbsfaAh03aLpRGT45sUb6KWPjwfwbSnByt/cWlR6YzBJTV8o\nTaTRZBPhhSDNOLo6qNsCu4EpIRzdtSDf7EVz5bA+ty1jNeIRPc0Ue/V4JMPP\n0GYt63vFo78yFZgeZmqzCftsBCNu6Cgyyld2dXDCbPuoTMkCiwT45TdukAV9\n2bHR2U8C4Ec2mdNuBaQn+2w1/7X+OONMrjaWxQWCZP+DcKJSWciWnLvBybI4\nsPoAyNTyilc9udjoReRZI6O1mv+tR8B/dwiddHltdxwdgX/WleDSoIpG5GGc\naspu06szhBFyRknS/SMhJA5km1UbbD1HSery8NxSw0QWvQEWDNVm76Ixllek\nCnKwwtgdE4n/EB+tcD0DR8nl7OxeotdCcFV7ZuyfIOXZHlN1bZvMH+k15LEF\n5j30NO2+/IdcmDjUTFxk1qdn3VVILMribEEEMmvFlcG/SkXnEMRLr264RG8S\neCIEFWLSltm5uzPysXrEfj8Xb/Em1UqG0d4CJtroH5ATrdgfr2hHF82N1chb\noxM10wlhQW81NaX1Y2dv3b41PVzw4f3JUGEebQTkPZRfYPwAHN+CVfS2ZXQd\njqO/0v/8UaZNfWyScLRG/kEO2kWHe5x6cOqRfGZ/79/1TBBvPvg95hmeZVb1\nDhR2\r\n=wC/F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_26.0.0-alpha.0_1588421571385_0.25947509555618775", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "jest-regex-util", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "jest-regex-util@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8913b0affcdb480205f5b3751f27c896ec4fd09d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-26.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-V9CK9Gn44RV13TDt7NWN5iwn49sKWX4HvyvdgyYP0Fbp6uTm3SMelMwO3hLG+UjtByHW5ijp/BfZv/gxfJPr3w==", "signatures": [{"sig": "MEUCIQDkgRdKWyjxk3W6w1BoKE8pR18+Zxr7G+Mcm3385TYL9wIgNF974YRGujvERF1/Nk3vOwvN+BnMdZR8LN7ouhR37DM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHbCRA9TVsSAnZWagAAlzoP/3TUahpME2bW1jrN/PQn\nAAofvRYLUevqKSMPGqO0zwIlCmV06UumjKuBtsHbEZIASPc0xTCud5LZugSh\nbWtgEWWOVUQq+mLqWUOFj6tRjV/oUjGFLEvWKPA43GJQVHZ9Rs0xAIPWXqiU\nSAbK8aMOB4+t6NfAfez+xsXYpPwsYmV3tNPQpUEbHGI5wiX0U3ietCb0NOE0\nIdAJlqkFVr2/FuSlGNrPfaf408YOMXuE/9joEYd3v4NdmD48cc4jIp9Zd3mH\nMIE7r4r1f9e5L3JS1HCg13w433sfitQWAFHxKslt1SUX72ypMO1YkPZHU2cv\nap1fdqZv6y3gbO1g4MyF3OUtWixzoGXqw/LjoGL1L8D0YnyUrSc1H/1YGs9t\nNtancUQJpWYXAA/Oxt9hQDNqtgtYqCbHtcPdrKGHr6NP01soEhBFdalNnTCF\nh7tH2cWapGyLUHkYvoEryCnfJKE3vWAygcH3G4JV8KUmMsJIOwSNCcGCsHNq\nfa1lb44v6dFwnih+2hEGV1Z9vEJXjHdPBSiFyG+ahFs9pS/yGAjQFR/I1NwW\np6N3dOhVKUcMz/Y+8qgGpl2WLGXA9mibJ186HL/k6MeUkDUMoXXZzrt+1dM5\nYIPgx7Yi4K2knDwMLsZtO/rAaGt7KlPC2hKFWwAq035z8bC0J8TdHIeig60U\nFuex\r\n=9A/S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_26.0.0-alpha.1_1588531675377_0.28311156488822453", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-regex-util", "version": "26.0.0", "license": "MIT", "_id": "jest-regex-util@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d25e7184b36e39fd466c3bc41be0971e821fee28", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-26.0.0.tgz", "fileCount": 4, "integrity": "sha512-Gv3ZIs/nA48/Zvjrl34bf+oD76JHiGDUxNOVgUjh3j890sblXryjY4rss71fPtD/njchl6PSE2hIhvyWa1eT0A==", "signatures": [{"sig": "MEUCIQD30s+a69k1G/y+43saREy9Yal315k7x1ztiHh7au0sFgIgTPruXZ3a/At6EU61Ph+zFlTCAL/gx2Kp2BC5nsOA59w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ4CRA9TVsSAnZWagAAmocQAIlpM/pcLEjmzww8mNsI\nEaVx1g6pqezHNHjmO836nW9a6L9eyl3N6X7iISvyHeBl+g2BYL1DVYXtX7ND\njUuhgccxqS37ADzC5EAeRFRq8zkehzTv2VsZFaf0p4BMYWjJmY0ysoV9KWf3\n379ERwL3NViYoh5vG2FZP6iM1lDOFnlcVtBOYJaj7HJTXUkVCGRB7nZ2cjUm\nKzKSvGSm5J/MFL8uMHxnMW0TTjSHQOpOgNNAEORgYcp5E0etqcoAH2atSx/o\ng9gHmU8x2ck+F67MsbKfaffRYcqvkR0g8ZoKIpShxJzaVyyYWsBmY6m16v9b\nI64vlStQNe7vAUP4M2JCVVZP9pNK7YLrHMLEYbd0RotABIlWP4VClS/q4HkN\nR4tuUqr+6eeKSgIyDk7+vby6+hTEH5nsbrftRTsatOzXeUAaAstR5hilYjo4\n3AhoZhRiWXJNYjvF1a8xsDFgiucbJrJgDwe41KPqjijBW6byb4GAqX1PN1Ea\nOjI36LxKuXgQuA1vplGxEBWwchR5X1vwJs5EIaUltQyiR0iJb+tbrZGTSRKs\nbqCsYTp063ptASfeA2nijKhGN6W6tq42PCHEDl676whO0psNSVEg+KMyk62c\nejc4aUKsRL4wfL44EkkoudLcBNaA77CTonp2h1JvrG/BYUfHfa9ONf29Wzgg\nZfPy\r\n=ZsPX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_26.0.0_1588614776112_0.08791460759924341", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-regex-util", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-regex-util@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "559ff0c5388000d3b82dacc6adca682af9f9dabb", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-9f6JHFuKOGaTaBWAfneX6k/20o9t2O0JTwOCn/2qlBUZRbQt1sqd2U3qme7t85n8qZVdd/LRteUyZ6lnbWDNvg==", "signatures": [{"sig": "MEUCIFpVMRL+TM3mM43IsfoHaFa0IeklJNai2cp2ITxz9B3pAiEAkcLN/YKJ3j2TBzOgAx+AVXRZRGGo40380OVtoJHqhy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8JzCRA9TVsSAnZWagAAESMP/2OQUZD2XDNSD0WE6bUh\n0xqqb1pduBSXdDIYjDE/76/cSGaNLKnNxnsZZvtv3mVJg6YZ1MMdydt5xRQA\nUFSCOZd1+E2LWFYL0Efn+G6l4AV2bvsRzBRxr43Z6NHV0+8qIhsHJfJJG5E0\n6jokeiINM+i2CXCnHTs365YP2rWZ8Tpn7u0vF+MGzv5JpHa7gi+Lr20L65YZ\nVg/vs49OHseQ04xTvO6BWLztTaE2nWAPwSTjX9krLKI0QZCSpFahNs5Z+THS\naASZEnuSGAscVqI7E82ypwVLpiskU86Rt5JE+RMmN5u3O2+/3ahLVRaGqfQK\nOlP0jLrZ7fTe8O8oMkapsFgJdoiAccoY48O5qHRCrg95uGYSEkeLIdykmwRS\nzPVkC6rBscYcwFWFMzaVTvesPh14dsEJGUXoBj+U26YbS0PDjdKvfcJVyZIu\nBckIzVbdo2OqwVyA8U9JBRONGUwxqEHGipE8+AwgAKvmNuJv2Vl6B81hNZri\nZTqn60zn3F/cZo9VVVFdn6k+hmkhntAP0z45jYvxjEwB8i/d9WUzx4gLcucA\nu/wpczDZuYfFN8kFn2CLS5rwoxylymnEFqMQTrWjoQrJQhtpJbILmRzYiYPy\nz9t22g1iaHrlbxj/qNGmDsSbmb8jiMrJDKRufG50j8czMevl93J17KrC0Bar\n9vrg\r\n=KU7O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_27.0.0-next.0_1607189107472_0.6255394925653772", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-regex-util", "version": "27.0.1", "license": "MIT", "_id": "jest-regex-util@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "69d4b1bf5b690faa3490113c47486ed85dd45b68", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.0.1.tgz", "fileCount": 4, "integrity": "sha512-6nY6QVcpTgEKQy1L41P4pr3aOddneK17kn3HJw6SdwGiKfgCGTvH02hVXL0GU8GEKtPH83eD2DIDgxHXOxVohQ==", "signatures": [{"sig": "MEQCIAQwzbA8CPfLafNGs3HrQFeZ/BGm/GTQ8SlWy16S894GAiAOyQsCjONYdkkCcmYuo0a4LZO5SgRCyKnOum7D898qWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwgCRA9TVsSAnZWagAARFAP/R27PoPYyP6SBt3VTpBK\nHd5RZT03Nq2OymeTu8Kv1oQV9rWWwZkTRAEbN6Mkmx0b7jcF/eqLWeBWEQWt\ncx+4BF1ko52wONzb63srr2Rstm6bnI25vhlXTYE6D/sIgyi37T5m8rQV+Bxq\nR3+UcWXLli3Y5BszRFShVYl7HzymkZF/3knnVZpklNy0d0Iluy8A0ZMQsF5d\nBfkB1EmANMXkOnCiQFCtgukcK/gwaHXy7keEpD1EXDIux9JP8IthkCYrIsa1\nkp2nYvLPQ2nIRHMMPlxegooN5BfOJTl3lb6b5n/z3eW1ssvizT9wSsmzqHwn\nlJUAwM+5VZ4xqJlXXamTqtTbamb8fT/WDQMEJZddqiAIW5oParzEpBJM8vnU\nCALw9KdbKWUmGnq0BNfRfWVvK5tEwcu8GsWKRUFqwIYmzPiOq/UuoKqwpKWm\npR1qWF2uBkKOqFbsKv5hhmNQAMgrx3hEmdvRKRyli+BaSUgDiiICHLd9G3nH\nLVnJXaDtrbfyv9Ob5l4yQ26AY07tU23qZMJuMgQ5HfECqUaGVTwGxOmhxXu7\ndB4rUHXIGMvI+BANXwJuGNNDlPz6xZoCVMRVD6C4oF++r1YEZ8wAOU5iVD0q\nN73rU0iYsLq6RuyX2m9D4YTayQ0eU0PSdmIS3XwmIiYLqqCrljRp2C79mb29\nQd8F\r\n=BVJ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_27.0.1_1621937183769_0.041860345984139125", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-regex-util", "version": "27.0.6", "license": "MIT", "_id": "jest-regex-util@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "02e112082935ae949ce5d13b2675db3d8c87d9c5", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.0.6.tgz", "fileCount": 4, "integrity": "sha512-SUhPzBsGa1IKm8hx2F4NfTGGp+r7BXJ4CulsZ1k2kI+mGLG+lxGrs76veN2LF/aUdGosJBzKgXmNCw+BzFqBDQ==", "signatures": [{"sig": "MEQCIAfrAsCqm/OYT3VirlfmVTPA1B3vMX7Dx28WU7Z7TUz6AiAiVNkbFWeRsIi+1/3RG6a+yDx2iAkhSILqoGEvtqQpBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAAp8kP/3WOvskSVSPELPmul/1z\nsQ9mOR/7WBcyQmYgHpmK9wewYBYnH956bf9JURzVq8FX0BINO39DtzpX8s9M\ngR+fq90UgigbZbbVutXDW8PdqOQKhphe5JGOcKyA7JOJGkOT/F2tc76kWz2w\nf6rnZYq2UIYIdfAQMpFvBVPsCmukMIgWemVcIRItT14+pDuXuGR3zWChpvUX\nXhgr8TRS6h8dp8ak68UJUlhao+0TssMz548qNVIFiflODMUzNmUI2PGLAAIZ\nD87aZLxr991MG+81NBEYftKFjYoo2VZPL5OPedhUvkLViGCcC62uYqkmbVT+\nV0scUj+ZU2yyQU6EyAVyo3bFTcbDcnuNyd6B9eKQDekZQe0Z+qY3wgq8jENe\n0zwOG+/Bphout1lDmF9ABmSv0OQ38FbWPehsd834wvRGPo4qYBTG/7DDLpSG\nOB6q2TR2H6dXgn+muf/lhrs/7y7QYDVhUdrfCtDkJ1rcTSRI3nQAyvNhkmZG\nzbAooC/SajRZZMDRhtJll3oL9DirJOUdPmCHGNYB7F2ggFIOe/dzMtdwen7s\npSKR0o9oWMm/64S/y4zWvHSybFO0ANqQNjJy7Zbk9LWzY5U/W1rWUZ8Kehkm\nSSoUzxea3k6ka7DCqA/DP+vEcYR1DxS8iO2SKasjjjTnA6BtgXvw0FON97mo\ndLHu\r\n=XTYV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_27.0.6_1624899931253_0.08871432727669193", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-regex-util", "version": "27.4.0", "license": "MIT", "_id": "jest-regex-util@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e4c45b52653128843d07ad94aec34393ea14fbca", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.4.0.tgz", "fileCount": 4, "integrity": "sha512-WeCpMpNnqJYMQoOjm1nTtsgbR4XHAk1u00qDoNBQoykM280+/TmgA5Qh5giC1ecy6a5d4hbSsHzpBtu5yvlbEg==", "signatures": [{"sig": "MEUCIERQlHRzD6Nq7AoLi2kqECsBGRXqrEQDc9ilB70/CIF9AiEAlNKAcpwHCxnT642fh8XDZgJoQ85YJA1noCO9cYNDXlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAATY4P/3PLnbL4+QmtIH/7XwLT\nsgI4AtLN9pOTJ6ZRWA2Qe6kYHk3U9LcPfsJ7ryahB0391mGQntJwIbVoqQRm\ncGxZzFi4Ze/kpoye5ZLKD7jbKIq0N7HUXVR6d86qAMbJtvonD8ls2cfYIm3N\nVxxaY8B9Vk321vQUY/Wwrwq9Qcr9T2N0psRF9wlgFIvnHoWT/DXwRjp7P0Z3\nn8QhWqszTiO2YILI4/PadI3Bo3Q7QCilpYmDwHZUj5vXnY6YhFtGnvB/Xqxk\nPUl0gEaVYCXaki73on3MlSi23rYHGf+T9dZKudnIDR5Wl0uJYQ75eC/RnkCe\nXsuW1zVjauo9JjqBu4bIT4pgitkCu/i/klj1pScXWvN5Q3BN3WmZJ3OXsYzw\nFnRK8CVHntblo0vstOO9880ZpFzQs0UwckhM+JmdxRwzROA4jgJQTXETd2t/\n5oYvXq5fB5yp3aIItvYOsCNgsKW+RSIkXbQq/5q3RD8FLhdZ2zQvHQW1Eybn\nxglJg7QydWcpPvY3ls58wnBeoJzZoVSNRhjqhb0Xv6cmxTFYQlsUYWZl/1Od\nNbajnlsg2E4RBAz66pV1f8b5R1W1SWxzaPan+rN61Jr6JO/7mIcQYu9MgTyn\nYaQylYI+6EU3ig+ZtNNIk1uuN+DJ9k7tDAS8DzSfwIITKCCKKaHfh5EX3x1t\nc6oS\r\n=RIuy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_27.4.0_1638193014718_0.3775509871720699", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-regex-util", "version": "27.5.0", "license": "MIT", "_id": "jest-regex-util@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "26c26cf15a73edba13cb8930e261443d25ed8608", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.5.0.tgz", "fileCount": 4, "integrity": "sha512-e9LqSd6HsDsqd7KS3rNyYwmQAaG9jq4U3LbnwVxN/y3nNlDzm2OFs596uo9zrUY+AV1opXq6ome78tRDUCRWfA==", "signatures": [{"sig": "MEUCIHcCde3vdtOlvIypiCGSfgsRKKu2bQ2cUyURPzYRCac3AiEAi3NXGInkDb2mQu6mpp/fKVPAbGjB23El9gLFWcH1C4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp1CRA9TVsSAnZWagAAIQYP+wcC5/+ufRmUN/rzvYYB\nkuMFdwrPdoFhP4CP7MFkUC+aHclk+Ob3WpYp3V1fuafMLf1up/XyR7pLyKxd\n9TTOikdROq6ge1yGWRSI85YKjL6JZ75Mk1plIu9YsgJ7OXiu6/K6E0gDkAhN\n4zMjhm0Qd0HN9ObKUBUOsIpgFYSx2weqHK+15AwqXKXYAjyknDZ1v5tFuS86\nAf8ctRlfR2KOBkB5yXMxN7zuC9DQq8GzOjEQPCwxn5Ba3fF4C5np29PKHZOg\ndZnnsWyGU6EIYjzIhQtAjJgRptwClf82uDdpvK16OBTz43lOaym3hERfO26b\nwydlNnKahKcAx/qm06ltuYz1mXwy+b30arUISAWrMp63zY1rwCpwFDkGUmRf\nnD8Fv6rMtDy7WhUZThxYVuYxcYRNetX5Ln+ZqXn/4yK7vytzTC3RYqTOUR3D\nemo+y/Hv7x+Ol4Ys8LneQKkwuCEwdDp6CV/XR1knzYPR67quk6RA28UwkADI\nmDoBK8a993OrB6lmA5/ntVmANi1/VBWG1p6PzGkmQknAQFonhNZCguNEsfKg\ng7brvNbyAlWSWA5EFOP7Iu8dq2w6YkmwNLir0iaWDEJFk5E1UOuuk+SmvVvU\n4Uvc+tCzoKiRUyxldEKBGix/Z86RXNNyV6rFFY1YLemzeKF+wpr3etJIKGnQ\nYU9k\r\n=SDVz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_27.5.0_1644055157671_0.8538357109376962", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-regex-util", "version": "27.5.1", "license": "MIT", "_id": "jest-regex-util@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4da143f7e9fd1e542d4aa69617b38e4a78365b95", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.5.1.tgz", "fileCount": 4, "integrity": "sha512-4bfKq2zie+x16okqDXjXn9ql2B0dScQu+vcwe4TvFVhkVyuWLqpZrZtXxLLWoXYgn0E87I6r6GRYHF7wFZBUvg==", "signatures": [{"sig": "MEYCIQDTcwHYC2eCjntxu2xHXeyKq98f2d7f5dwMddG8yjeXmQIhAL1BbK5/L3EBdZV6pq0vDCO6IppcwNBg4If3yfXtRlsO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAAnvYP/3Oicm6CEgYfnQC/GUMg\nDB/QAyd7rl7sUteprWdwni79+RwaK1Nqk5pn1t4xYdcI4ATGNcDVrnm9ufLE\n2XHleu4Ock4q60RGynBcYIJ3MJFs1kkaBCVbFB+qAnlCRoaFoS5/KzW19WV4\nG50o0VH1AqSYQI+gj/x2VEtcRHp6F4uGLiZmZkid0EdmFvXNy7N1uaQt0tR2\n9bEBl7EZXBHR5un0i3FQzeJz/1ragEWz4Oyai+9qiTOjAbJc77fvzbSLQEI+\nzKW8BRBWkUVlsvE1soln6UhQKsZZhbdZBv6TeVNncP5asNgJ/vqCP9z7mTlM\nvip8qqc4yJw8/4tEVBSuf9bUX3vIfIA/XiAtDlZecb2Gelq7EDCX6e2y82bq\nBm41u6B0FaSjcMJdsbLS6Uup4KZIOVV7xDnEsgae65VjdUKZ23k1E85mpjs5\nOspl90Y8Z4f47tqqRzFFuB2pI218tRLuulJ+bZzpr+sCqIp7xHq8iyDd+7k2\nGb5ket4kKtnJDrzxSNpQwqG/XLdMa4c9b6cXM9MROavZGFHiDZRJehPAcYta\noEFOO2CgqTSSkGrZEfU2PkDqBhKdDj6l2SQoeah+HkKGk6CEM5uPNTZbHVBD\nnh7EIoNR+4dDs3jc33DUjWOpp+AMoObjKr+NcSAdOaVyIULiGhoWZ+U79OQm\n9LU3\r\n=lpY9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_27.5.1_1644317531894_0.6346372381640288", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-regex-util", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-regex-util@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8fa087ec8f6b9cbc7fe3985a273f6bad5a296b16", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-WlXVuHrfPXUVfyGLItXePXJIWXTAQfmH/oRAHoMacGwdNsW4R3MKoqWV5sqOtTzrAo7uZj6Wgo1R1WzTWvoypg==", "signatures": [{"sig": "MEYCIQDt48YXy29MxRHzvHtEua5opvXR2rnGjLmRBwQqc+8QQQIhAPlMsor7bGcXRXDX2/xhPoV9X2QH+OCQEDi0KNwtVHGc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAA8A8P/RrcDMUDPwLywCXHz9N6\nI5gNIrFya9hNWrNppx5GLT/fCoZFTnAPCT863HJb7AaDWoX1iXXjtlj+lnqY\nkLwQdq3v4DdtsEuzrSyzu1U0JhX0FHyHlrF7avAuVSTYlI+UbTsYNQ6unVTy\n1XCkFebpCngu1yKa/qu6cEX4WWEhdfkMg2HeHrVfuVwopnaeBiDKO1P6n+mt\niSwH21AVQPy4gRKJ5SxF/Yu/agN0KuDhldieyKe63geu3W9zi41+xb2pa+il\n8L6d0VGHVms3gd+4TxnDNmoHgfuxuhL6Bi92ZZPUnoLzmTbSw9xJMoAYY1vK\nTHZ+K8qHqeBh0ahW03RW7HAGRnvS5hzqdf5dARj0Sm5TguKDkEwAadY1Kjse\nngtnzNUg1K6+x2WTpNIU0ShwhyBGN9vna5gccB3QVfPITy8DaktPfZxPkt1i\nyPx+ejYLIqPZw+X4LNUHqqtgxTvvnpFuMcFy2xhgtsyedcUnL8VOkBQTXnVd\nuIYukL7cuMIt2YU+DkgfQVZtPiwwLRVL+6mXOLXlaYRuxku4AzGmtIL5+iOT\nS4uU+h0TN+onwY0cLTBah15lAr2JDSRLn9EVuaEcrb7ewKBzuOTkuc4ETl8e\nEmNLXvXVvYeIsOPuoH6lgFa/Fy6N9m1WQiAUbgyP2kDFupk0YLUqUqN/TMVs\nRuVF\r\n=/csl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_28.0.0-alpha.0_1644517046272_0.3948173911289734", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-regex-util", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-regex-util@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9173180f05499a6388ea0d16ebfa3ed4db55d34b", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-WIreShBIxnWmJLBIpqazYh2n1WDA3BtsZgaOEuPxZ8GnL6FEnhdblKOIExzfhOT04p2/PCKwxKRZ7hiSyyKKSQ==", "signatures": [{"sig": "MEQCIA6Kv+W+R96mvnRioztfW7lVGuxw4+Rci3JwaTZVE3klAiBusA/ZZkQ83Ur9tw+6mJw+zPJH67XqWzmgFF2yKM9IDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKBQ//UdsQwR3ra4mS1rIQal8FVyM4TWDmpH2MMq6YUdMVW+QbImoX\r\nolvjzJi6g712J6+hZj9n5xwZbs5lOelBASJhn/WIu7dnfCUjdJVHpBufUoWD\r\nnjTfqeZRa7bzmNk9ypsQloIBIAF0bHTrAeDzfBpu9RaM/DhW1SQpURkN9ldN\r\nlCcXy2g3gFD91+BYoANMLPeoWYb9/N7Cwu7nqpwMqdjWItR4LHrB+hDq0IiE\r\nOoBC8jf/Va+doQ8533gWJY8fsKrcYFdWXxT8o1I7LdFvwqZhULwR1K6KXEjg\r\nOils8NmanT31Rs2vRZsyJUuY768rJecas0CUk6hK/h9QKUfAdX9ulL13y4KU\r\noCuZzSuhTiWf1GFi5+QLQd7ZJM0cByKnMqY05CHVogz888zfq6Fda92j98A7\r\neH0br6WXzmFoL4LLk6c/XszxQmt33zuKfIJVbYhRKDtYLde7ylE3JdyxJbq+\r\n8NnDBcEP5Eqi++XLXgabtSu1KkjVAMUworFVV8Nlbt6ngLGwNweDpPh1R/5d\r\nUgfKcXPQdklNnVBEKfd1ugtF42Gl70WYvLS78hA5VMCEXjHJXQJhtCCfpEJo\r\n7Vj8Hstn2AUza5dkZEHAUnbMN4kpXGkl869FI12XEvoChS4zHd8IxSQEnLOe\r\nCvaw/Km98/JshV2RI12CXx+4+FIHZtE0D+c=\r\n=F4e8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_28.0.0-alpha.3_1645112539999_0.977387043606402", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "jest-regex-util", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "jest-regex-util@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4880ce2b7969e3e1553e70027b268f3c997675ad", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-EunuUlOE7vRjxjOvr/NYR4FCIShD6MVKa2oaJxGH0Xr5IaBKC9yNxyl+uBusp1+mdEU6qqBzSzdfwSliCaofrA==", "signatures": [{"sig": "MEQCIGV53pMkVa4uciWokB7eYpWKy30atdSAtLsXt1Ta/zkkAiAqqY9NklImN6K9cEkCQdPnLgu45tcEB+Pt50pF0xC19w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYhQ/+Pjb9k+1x4l6YwJZ/1ciQombjUMlMt6qresQs29EogyOM7MT9\r\nc+Gc8dlVUZw59Hkg5jgdccwTNKPO3YK9fTJcwlOIhHfNFvxOMbSE/CNeTmae\r\n04Zws1BhVwCdXEmFKw1f3SZqBacIHealZraHfQUU6o9iBqR7+X3+ajZ+DjwS\r\ne5u73QuOj4cQ7l41R5v14K4iTeDzlPdL6oWIUHiNZPouIpVgSdOeBGPTLNWo\r\nfeatOzw7H/vCEr/dcDisH/re7g97+VasZMr2kNZzaVcoCsnQiAkzXYLWIX4+\r\nk1DynWMsU5tPpL4q99GDEt35fgxF+Gzr4rJGJhm8op/t/SeYmzzlY5SwPlTO\r\noURzoWVxYZEQyiY0Zq0sEXXTxOxtqKLToxls6HUGWvD4qiMLEyIuxqNe+pCk\r\nqiiXQ7qRgFzhAPSFiVn/ntlHM89bG0URwar/m+uAAq98lFy2nabmAAWr+6Ar\r\niq6KWAaCmKmbIkGbJ2/tpXEoQkRUcYKUAXNhs7fFxKIOU2PPBIIZbIC+RUgA\r\nU0jUybGZeCxzcJ+UzWl6KD7uDW8VzB00XGvW1qS+Gtszo0cL8JDK+KH5qJ5m\r\nEYlL+qKbpqcPqNuupKpkh9ycew3kSNIql+bIyJE192X5VzrhgqfRP74PoAtJ\r\nc9cWio+IIVkft+z5tnd9l4FNwFP5btmnGF8=\r\n=kaDy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_28.0.0-alpha.6_1646123542360_0.8750995002816855", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-regex-util", "version": "28.0.0", "license": "MIT", "_id": "jest-regex-util@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b8d0bf9761328ae21900ab3f3b0ce8c4d392a73a", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.0.tgz", "fileCount": 4, "integrity": "sha512-VqrjkteNiucN3ctI/AtBzO7iitfk5YGArPwU2cJ3WyT5Z6kGFHw/HQp0fSTkOUHdwVdJkFzbI5nh0yC82f9Kfg==", "signatures": [{"sig": "MEQCIEEv++cD4zYhhz871O8Ree7+quWh1mAYwzQX0cBul2GRAiBCeRao0GYWeWES9Nc7scO7BQ1DMIe09j0J9CoVuX62IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqhqw/8DmQWckCIK6/TNusRakBu52h940ULf5aAoF6vlN6gdQ4QQI9p\r\nBQd3pQ/M+fSvI6jAGOkhXe2IDlZJsI2UmkSYx1SO/vbxvKzD8ExJ8b7kXxE3\r\nnjsdJ4E3Na6KTGyykWa2fzZz6E8YRKKqKVHFnC+xYtRS0LhsermIS+/kRSUZ\r\nsO4L+Vd5bBy9pU5z1h9V5WJgl+WTnZEwJvHJC5xmzgOj1Ftq68pEXBTzve2G\r\nNlLxk5H9zoYTwQlLW3mScDMLgR4AwH7pn/rRalGIYYTI0tS2fXwn1btgXT1O\r\nW8BcS2rd/Owp0MQbueT2/agTDlFwOHKEfFbNY/QBuR6UdMZ0N9wJVn2mPp94\r\n1ZJNxqOz6hlpUh46uD+WC3dJqcLU3Yb+5KAGrTf1ZvlTp2gM62OqeJdKunxz\r\nDtEshK2v/HraylkSgWmJAfuT/XveY9wyWT63FR15j+EqQX8LxR/0Ud8iuXGT\r\ncCiowM18Pgo9cKCGDLBDZYBHFglYcl46HgtbwJUhnBnyQQrtQoTtWv0HpeTq\r\nXr1qsb6feuFqjXaxhBUNdLmgHKPEVcBPTqMhc/Tddy5TqvSsNZ1gbKZa+sbl\r\nmnmb2pSEqhiCWuBQSjYEIPHIr4HIcJIeZEDcXV66nHLSAgQKbXNN7uCGNdwg\r\npYSTgROt4wxbS905NG8g0h8pwG0625GZuBw=\r\n=9JBI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_28.0.0_1650888481431_0.7636824555051887", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-regex-util", "version": "28.0.2", "license": "MIT", "_id": "jest-regex-util@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "afdc377a3b25fb6e80825adcf76c854e5bf47ead", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.2.tgz", "fileCount": 4, "integrity": "sha512-4s0IgyNIy0y9FK+cjoVYoxamT7Zeo7MhzqRGx7YDYmaQn1wucY9rotiGkBzzcMXTtjrCAP/f7f+E0F7+fxPNdw==", "signatures": [{"sig": "MEUCIQCjVOW+sMLvu5TflZqvMHGhPi2CIU4kM6fjDfldNsy5kgIgFEElwsISjxcDSnrRh5mzF7tT95yitsOtNT72ic1p6qo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreMQ/8DZLl9oQ/D178jPwFzpTc+HvcTVuPzz5KCkSOiiukFD01Q+9d\r\ntTA8HLQRX3+I4eWHqb4Db80j5fw9vSql6nwZxqaAe+1UF8ock/wLa5ixng3T\r\nrtORUKXADo70LSM0LHrlrOsI2rZhvGRF3B8DCjkk+cikWk+yzmmShXFlqANs\r\nb6WEJFFWUEL+Bhg31KaoKXzGf0lv+chLvOPjlo8tpVwnvfFsh0DvXJp8X3s4\r\nJgTwqQkR5rhTkKZ5sU0lqDSd2kz3qpaBKdAqt4BlkCL66FCR2XKZJUzzQC2M\r\nz3XhRfM7FSwT85EMuSVdaYJFwxkFlK1YmNq4DkP+RMZi6gVL3TAdvG45o8ha\r\nencBXQjpu2Jk9HznqRgvYf8JATg0fFAs5Zb3usij6u3m+ETvFQYGTrbcvRZi\r\nC4ZwYWudsQgBCWDKqGkJyx5OKC/6tJyMuOH/yt3eU8MSdZVahsoGkG6VI6MO\r\npwVr3TYoBjxn46w9nBc8JHA5wu5lxsqmN1oZuYS0uekoJQ0kxzesefl32aBI\r\nKMHugbbT91430rWMSuRkA/uTor1YgavWLhCFnkVcDUIPRyIJQRygwzCZkYJ0\r\njHJgS0d1LlecWS5blCOGnVc7IbJkpMj8QtF7vdukEYWhGVF/bSoN/1mNmAks\r\nrI0USKzekDXEJdVrDepoblkQnKNF7EDv4eE=\r\n=2NAm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_28.0.2_1651045440184_0.8667633302853051", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-regex-util", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-regex-util@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dbc8445b15ea793f07f72bb957374e8fa8c2d7aa", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-h+8JPgqMHd/C7Nua3WpsquvlqnCyiPr50vqU1478XOBFkCEeGWB9Oyfota4dlN3Ak3mAecdqjbjiIunwuHtTMw==", "signatures": [{"sig": "MEUCIQDp+MrXgaVIhKn7l+hAyjMKQVIrnG2ittEWHXPRneVWtAIgfiBjL9MV3f0XtgCyde0iiebT1HxsXC2p9FySJp8ol9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorcw//fNpsQEzblu7+lrwK176HaSolPYbAvoKQ99PmzFA6fxoZaRFO\r\nm6VlaIJs74hXIpe+S8JpuKPuCP0H/3IwXmV6MhxG+zufpngmB81r/ZYk1FLp\r\nAruBVoxIAZ+cbGyja3iQEOZ7ujELk/ltDQ3dgFo7oTnTOL5jyNRNcmbO1aj6\r\nJnQ6nED39uGGgQXDpyVusopXUoMYtC+6AY2gA790Gtc8afuIygWE/PzE3cvQ\r\n5XoGqESZoC5b7OlQqll4O1UwNT8Rii6NbiEP8tpGxlz9eOdCVDAiP4mzmwAl\r\nFljyeB9PM6BAmLVXNekQWH1jaql69Lte2wOFjrLfyBJ2DpUCGVMYYouz6VL6\r\nhm+Eaf5MnLtyWJeOAUAWkCITEREcNy8JmJELI5EaEgBauZiygUYsoLb9qPjO\r\n/pLw9PBDv7jk22ALkYOh1Pd+0PqN7UlpxO2DxT52SqBq60q5iUIfNBvLZv0i\r\n8zGn3BEWV8fAFkaH4J3aKUI26e1J0k/6Y2I+YD0nBRqeZxe1zKlq/1hNapLP\r\npDc+AB+uRENP7sjF4RhTqO/b/RzRXxXLSqNeSSC6GaYmfhCDB711iE80Y4xK\r\nre6+IMEAqBNrUY8xBblJzmL0goHwijc1QF9CkX5/zG3AkWXIF5T/kPj0n+8B\r\nUfvZHzcnJHQN9zLDSIi8VRf5ZHCXtVv8AJw=\r\n=rypr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.0.0-alpha.0_1658095625751_0.7817423502750389", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "jest-regex-util", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "jest-regex-util@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "40b181e143aa945c67a03a2f981ccfe58ff23d09", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-bNIHBmgNhvKfzEuqfUyShlO8eFkHD3+FlVEp+GJbo+HYHoLtv2LHPFnYRxTjETpgTIXM/6fPEOplYt0t68GSMg==", "signatures": [{"sig": "MEYCIQCusl6fH4fOr4qcUb68ySduNCvx0zLle2OPAMRL5sLjRQIhAMNdIRQZ5q9PA1pj6RC78UNgX3db7hWxdihfgwYI/Q7p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr11Q//Upqhzq1EsxVkD2/3RvKRa6SMmWRdBl7wF0T7PzSACRP0BXn5\r\nkzirylqpxHFCoJDNmTXl2fUHBMEiAilUWwun++k9IOGKRenixwOg4Xr6liDE\r\nunH1aSfwKH/WyKvsIeszmpw7IVkXfyynxPEMz33hPRbCzW97W5mnSZtbf7ss\r\ncvipuNvHFozyEplZJ66pxu0sFz8zkzhDoFFkMyr6Tyn1e2z5ejqXU+4E4q2o\r\nt+xDY7w4dVw6boXKlvZIGG1hvs0WNIwp9qAYMGcYCwiNBv7tsJFCOUcBLybz\r\nvffgGLR0K3yJ1oVNw/lKF8lUKDtSVbqbfowjapvplIGa3bavRa/t8VAX8oEQ\r\nY3hcs1WoE0U3dY42NBXEUSSZeOp6Zmo5AlWGm/qetCJt1M2oYSwLfHLGrnHY\r\nZAROAY7f77X/6/xoWONMMbsueykwrndZncuVOXdQsEmODRkPT7QEw3QWH5mU\r\nkN18TB6gG8kN+4G8OtJXpDv2RTY4IcwbESoj9rtXGowLyWgSR5AvqeUBlv+H\r\nBlN9nWhy+Qk+SJxCejkVimJH4sRdzJSWdPgwR72JIFPGtkcgCWJsLlQWbsqp\r\nVIl8fz3737/X+R85KteirzltNnhcTge/AFDVxUN5O6LBT8OTnbpMab2aR0Ey\r\nPGVOKsGnEbL4K4fAd0MRdK4AiC5EgE7F6UU=\r\n=DNce\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.0.0-alpha.2_1659742345201_0.8396690607068868", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-regex-util", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-regex-util@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "be56b94f0cc4fcd785f12dcdf4be178fb5b80fb9", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-lPeBxm14mDlHOHpq+63Ljr5WIQ4eJ4Gs7TAVa4mqE+kOlGIg50yrgURI/moPhkDU8P8s/4NAi0Z1ODQ6ha9qkA==", "signatures": [{"sig": "MEYCIQCHYg5yZErtBntnp+jEsWA463+tAAIN416nscehTKiksgIhALjFO91lQr0zDL52fNo1rQy/4CBan5x8wj1nByp5tUPb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNeA/8D4vDsbKxpsT7ckzYurPG0ooPX9GNkiQopC8SMpC2hDOr1mS5\r\n5mSYrschvpYq4Bi274a/PdYvgC/3e+gumr6EV/4fhN7T2pt5XFJoG6jiOVAC\r\nhDo8WyvoT7+Oi4jaGwbLN291+skRwnmOF5LDTEH9lqLlQHGaPfa15NjN4as+\r\ns36TQ/cm4LhJJo/oshPNfskU1d6GPhpQgsRgWp2KjzGK1Z56j6gHm1tJUy6S\r\nb0fHRzACJkOR5/kCzXYs5CG3RA43v6FN24K732L4//B8i1c3zzzuY2pp+P3z\r\nhwxq2YuqrgEi60gj62KGco/pMpqXZGLT28GZH/SVWBKCrTf65W2ghdiXlCTk\r\nidRqyQxyJJSDz8Y/5l4DuIvvEZ/x+vzP8VwvE5MxTUogCmP7pUPj0qk9owfW\r\nIvNag6NAHB7mgr1Gvo1LCacn26DU2b5dqEbTGchPRxUaavYHgaZ38UovN3xO\r\nBfBYGO7T+eu+mdfUHJyi4IdRMMoCbGTbZhNksa+7qf5QisI78FyftOudvb1B\r\n+tMAdJU/DVjvy/rtqjpInE1R3iNkJbIMxNSYgg6EzdLI2UrrEf3J4zdwvab5\r\nPqkqA0G7+4wUz6IOVmiGME5flyTxdAiMpxNPfhekcK+DEmexyaOePRPwywTj\r\nbt5uvYVUJ07t+WI9Xom7/OQz3DbkwVCsjMs=\r\n=ZvVW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.0.0-alpha.3_1659879691825_0.07702485386222957", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-regex-util", "version": "29.0.0", "license": "MIT", "_id": "jest-regex-util@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b442987f688289df8eb6c16fa8df488b4cd007de", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.0.0.tgz", "fileCount": 4, "integrity": "sha512-BV7VW7Sy0fInHWN93MMPtlClweYv2qrSCwfeFWmpribGZtQPWNvRSq9XOVgOEjU1iBGRKXUZil0o2AH7Iy9Lug==", "signatures": [{"sig": "MEQCIBDbj4fL5SJjsp/5bv4SYUp7m7uC/dZahv4vgSthX1GKAiAxLBhJpblIPNlHaWycez6siQglDzUgG1udU/1maSysLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz+w//QGTP9YDTwkIHuKbumc8baE50zebjeYz+mhPhPWDexHcCSG+d\r\nmbmXnY0paLXVpD7FrvwkQzJ4WIAsNC/oZZIXZDtVXBRCym6c/M39Kqqyu1YA\r\n2CYBS9FrJ/P+kkN1IODhnF7xft5YWmFaW58wdXW2jO8ZGXgqTS/sHRvzZ49i\r\nGSW6V/O84ZnJx0XgqMXuzGfXdixfIfQ2zbnc5EJdtQHdH2GTkpbCXMtjOm88\r\nc5L3d3q204YT9cVb8VRgzoPixnSq5bEuAR/dCRMI5hvw+75H22JHxmlmrYiK\r\n9sabqPXTEGYn4EP0KJrHaEbpXaDIBBiEAWGLgcCBoTGSKZME6trAVMdXia15\r\n65K3kZh+5wWKjKh8F/2kSKhUhfGjJ4m5AAc4NdNVUTfkzZB+Z/0itT1sSynV\r\n4aGxlnTTLEIsu65iM30Yls0DbBLpa7ah9OAmgJuotjdY1pKPEnCP7adiwn4K\r\nPWlqTAF6RHAPb6rqUcOLFLMAoh7LEFYhgz5ppujSU0duXtKt42PfMeZ3hUEn\r\n+xDloUIzO0JzuPWpiBbOGGXpuh6QSqGMgiYtxNq+MVFAsrl5NGb/jCB4zdmD\r\n00iwuvIVlYOTpnqHWs0GKTWkZTgtjLR0RRbck43S94nqtd7CskPDUdbmUNPv\r\nkCPtHphntqWVrobVEB5zFkzI7tO4DGTPwfQ=\r\n=Ivcs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.0.0_1661430804472_0.9486758976702196", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-regex-util", "version": "29.2.0", "license": "MIT", "_id": "jest-regex-util@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82ef3b587e8c303357728d0322d48bbfd2971f7b", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.2.0.tgz", "fileCount": 4, "integrity": "sha512-6yXn0kg2JXzH30cr2NlThF+70iuO/3irbaB4mh5WyqNIvLLP+B6sFdluO1/1RJmslyh/f9osnefECflHvTbwVA==", "signatures": [{"sig": "MEUCIH37Quqs7jG7bfcxc4CjcT2JYWgl0jyeVmTN8ADZVuLiAiEA+ymttOrTZnS/nOq9NjfHieEqHtzmPqAGXQraDhAkyjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvFQ/6A0CWlik2admM5/D7NkxnBLquJtMjwqVqcEuU4NSWdIFfiWXi\r\n5+THsO01djzC+d2TZEGzM5Fn5vx2n7PoHLfOdwXeXfPKbo/Ld1ed0GnzbY4G\r\nrgpJUR70KVLa/pV1A0SIOzqu/ICQs6JbpTNVFvhHb6mjaTUWoV74sUuSracY\r\n08V+BBySt3yu8rsKGIAkEap5hNGXK5LnSAeR2cyZBWiefMWtV6jaQZFcCy4u\r\nQzOv87PX+ElDVzLpTwI0iO67KYJDxI9GpThFMlyKWNSuvYRm62nPms2VU+VR\r\nN38Uo6xlphgv2RHMGodyjeWVJxYbv/T5E52vlhgCxHYCqsT0dyDbjklFSUj6\r\nYtB1Ctq9Nbc1fbb0ZchByUD75O0JaEF9t2Y+wmDyts6eSx4uBJqpm988DLI9\r\n/UOg8ej7GV+oaCxNLo/sp2D/BL3ItsbIaFpAJJ/V0GPJAsF7CCcdpKXfRnM5\r\nf7O/ZagzENl1mF7qnF/tZ5rv5a9hZkTNytsY2TFHyAlNp5U4TFPmJiRMpbeP\r\nH+TYzsQisjCgtrP3CDe7AOVjqbkSn5DKrYh07i3mPhtE9awXF2vaa+Vly66e\r\npWMzkNuLff6YV88gUa1CEPWkgZ/tMhWQiSaPLKvfA2I4FNkoZjZexbt5qBmo\r\n3uJCC6ADjl9qZdIDqobWsmuVzzQc4jDguHA=\r\n=pl46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.2.0_1665738821154_0.5998186672607291", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-regex-util", "version": "29.4.2", "license": "MIT", "_id": "jest-regex-util@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "19187cca35d301f8126cf7a021dd4dcb7b58a1ca", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.4.2.tgz", "fileCount": 4, "integrity": "sha512-XYZXOqUl1y31H6VLMrrUL1ZhXuiymLKPz0BO1kEeR5xER9Tv86RZrjTm74g5l9bPJQXA/hyLdaVPN/sdqfteig==", "signatures": [{"sig": "MEUCIQDNzF0m4amzD9ody5AUKr+LCOLZDW5IM46D6WIjyHwcJQIgKlWcTakUfNhg+dNYU+vydSXmmgC6jnw0+kzqVK1lYXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaQQ/+Nh+aVO8Ag5k1eI63eEBpgbDAXAybE2aI2qfAWZ91Lu9cV8/4\r\neWMBPQZTxzMvGUy6tm9p+K3G9NrwSOO3wAe3/NxlyF/sAAzn2a+m9+lTLrlP\r\nXM7Vn1qR6cUh7xHrlF+rS2yZ6F6l+7fGYRRaGTMa+hTzBER53YKolw6MLIQ5\r\nq7YzRvVRP9bfb6/TqCE9GbHo4IK5RegeucnWkpJmQbz1nz147gy6bthlUImH\r\nP1pnXLSwCAoaVpiEB+GCjPvEDS5QAc6kur4OUOruWX5IecQW+s5b9JVlgST/\r\nBrnxXB+mJ8FTYAr/uTaIyoPiQEy/6t8vYYpv4o6HD2dWayGZblqRmnRFOVPW\r\nf3pVWFMGpqhWKRSvMh3k4ZMuogTXvIcLyRodX6m09mI7AhVjs7NSnuuUIKmG\r\nOcr3JC2/KUjPMkDVvkVIwn1Oe4qItwvb0wx7JQsx2UDEsyw6SJDF/km8PFpb\r\n5k0vBMPMWPA7CJAT6NtcSsDnJO6i841aQb6p0WdaWdmuV+tubRd19vil3Uol\r\nor2DQk+z4DPMoHcv4q8YEypG6+zoc5nN4pq7AtOuGAVYadFsJMBbCxkevLFr\r\nV8cy63X2bqgQVYhxtdF/mA+l4K1SD4mtDNBLyvZA7u3b2HMRZ/eNYVhVav4Q\r\nWiZUMomSNHs5N2lE+EeEvjPORUtbBx6LKx8=\r\n=0tx6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.4.2_1675777522118_0.5093692332334514", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-regex-util", "version": "29.4.3", "license": "MIT", "_id": "jest-regex-util@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a42616141e0cae052cfa32c169945d00c0aa0bb8", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.4.3.tgz", "fileCount": 4, "integrity": "sha512-O4FglZaMmWXbGHSQInfXewIsd1LMn9p3ZXB/6r4FOkyhX2/iP/soMG98jGvk/A3HAN78+5VWcBGO0BJAPRh4kg==", "signatures": [{"sig": "MEUCIG5oM0XGXVR7FA6467W53RDCRIkixtuNIgYdXO/dF95HAiEA2wQMcSvajqQqqmNrxiEQf1pmO6SZyXPR6RsyhoB7oQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4xA/9FzxxNI/VkH0vzkVMn07Bz3CgXMdlwkBI5blDujo0Cz3tgTRI\r\nCsRQgVyPc1TXJEXAR++0Zmm96/3kgZdljLMGe8/WPH4Zx5N+2gZy20q76yYR\r\nTco6ko+OtYte5Szcd/hvLqkGOWBTIrQCZuXmRSaq93XJ/NGrcqTY6zG1tf1p\r\n6/bwuM5t1KC/2kdx5gTA87uKUhUnyrBl7PGC+Q5D8KJ6dMN86OZeMYARM6i3\r\n2Xy8TVd283D8OsqqvXau8W1qUoQ5TG5dDO0LPi1l7cX4bQVB76r699cAtMVO\r\nHzYf5QGbfLgDcrcUuW0AiGQfPSo6R3Fn5ajrhia5+vla1uXZ6e8ScGWjbdk1\r\n8vV3vjiUehbYEabG/YM8castpAvKQBUX5E/Owz3GoElx9Q+ms/xMGIkL+dEV\r\nH9E/voF2+W8hb8Ds3pN42JBNv2bYuBoxpDb/OrpVX2mY+ESxddj3AsclA5l4\r\n4CjiC8nq+XfdVcyjo04Os83iazjHNNJ1YasUgz4udRv8MKevS/iiYbWPf5xJ\r\n6X8iYqE8w8q37ogawzLRnLqiRyc801oLjLordTHjj3fHSUK6Bz4KqGCZPhu2\r\nElgaW0VpIFVtu6AgtGa8KftldxIeLhixF2c7RvYp9HEQ962LjDUL5uaezEry\r\nyxo+eIQy7OCs80m80atkG7sT6q9yooo9+og=\r\n=yy2+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.4.3_1676462236246_0.6946308332566065", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-regex-util", "version": "29.6.3", "license": "MIT", "_id": "jest-regex-util@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "4a556d9c776af68e1c5f48194f4d0327d24e8a52", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "fileCount": 4, "integrity": "sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==", "signatures": [{"sig": "MEQCIDns8VncXeQb7BENjkRILmxNnL+cNZnJTSDhvvKzzpt3AiBAfbHouLpIRDk2l25IDgF7AOCL0MhrIguMsyVRyJeIrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3515}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_29.6.3_1692621537581_0.9259955475404187", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "jest-regex-util", "version": "30.0.0-alpha.0", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "ad3ad7141b6c17db8df2b80bce8efa31c18edf86", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-6Pm3dFcAs2WJ+gfy9rElXMNdUVEd0rEPozNvhw2M7yHBwqH7DLHvLG7Ip3I2X1v5hw7fw0XJADrpJZF88WM9ew==", "signatures": [{"sig": "MEUCIQDpfeFyHLXLe73TxSdgpF1txc7S+3NCXF+mlNTaJEp5YQIgF9bM9i9uaNPY95P2A+IMomkR1dXb/5vTZnoDW0CirE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4312}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.0_1698671619542_0.2788637161238554", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-regex-util", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "54e5089b21594d295c3d4196a8baeb23eef795bd", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-KYU3Y4SK0cRLNZNcxhgC72lBKDICaq1EXMJWma2I05OUh5dJV8mQX2zp0SoswM4mu/y9JyewO0+KqdjFX9+Icw==", "signatures": [{"sig": "MEMCIDY9JD/RxMLGWBWWG/KqR50njFPvz/QxnIHR2KOjNTzNAh9A9m8NxFEsppdJN03xKmxg2YnZMvq6Txx5/EjO6VhG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4312}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.1_1698672766193_0.42110133769169655", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-regex-util", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "435e1c039e6112d57033d78975e713a30f3c2c60", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-jb8WuG87I4K9lxbyijxBd8SwNgkqkAclNiFOTb83/WXTmIG3lVPRvAjR4TWAq+v703+7cdlztJlT90BLoA65VQ==", "signatures": [{"sig": "MEYCIQC/yyuOQ1zX2wnpXS93dPkEbqXRixr3EjsvKomTVfC1cQIhAIlwe93SU9l+j/9LWTPAUH3nVrlfF8zCCd+r19Pkjjl/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4313}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.2_1700126894381_0.7450070526069745", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-regex-util", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f83f1cd0917163ebd901e74d2738d51a947f4ac7", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-XFCQrDRreQmrU/HCDxVdTlz9nFaLSJJSBbBoxTfKz7coMyPw2s0Zag2LwrslNJBPfuTtbokXbS1e2RtDAwPv+A==", "signatures": [{"sig": "MEUCIEfvLG1asXiva/VLvkR0luQPI7fZau46n9C3GCzG9t0OAiEA9ONP2SpQuo72D4tVhzT6h3bRR2JAo5geVPuUsUHzOic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4321}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.3_1708427327872_0.8785265644561926", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-regex-util", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "71259478bbc93c90988dd1cfc9fcbbaaf0ff541a", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-smK/jzd6Cgc8iMbBw0tsG0lnPJ2ue9u+h1c7uckyWHZh1MwwAq9PRf7qVQ6ys2//r5104bGWgb8q646iR8pROw==", "signatures": [{"sig": "MEUCIQDZp9RKxF/ZkhMPNFpkFOBXWhbhvblXPTi3EryeDcVk/QIgX533kbdOO+k3UW+7zmuPvgqeLut0RPGJe5ZEwXDRo2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4365}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.4_1715550194197_0.8930237020990575", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-regex-util", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "6e6881927ae36ac63b47fa97fe262e239bd965d1", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-b8LtZJTG17X/1Hm+s2A+YOh3pJbBoOV/K91N2Pl29MjcwsKwwlOUjkeY2GJwid+Z8kFZ7cbLYadDI7YCV+YEOA==", "signatures": [{"sig": "MEUCID3aytnGxWqjtckPUDvRPUuG9DR/d+MrnluY6oFYWqQ7AiEAsyCAYanOoTwv2iivpO4OuiggtLuPD6GT0CXFRC0RIT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4365}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.5_1717073032866_0.7334306002143018", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-regex-util", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "44b6e55bca4aea953f1a8946431a31fa9a054989", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-XcsAVaqc69QyMz1/FChyhWSoAMaKcDPhFOuWJz/H51LppsyZRAJPXkPnMopsS+qfut8cggExr9QLcsYaX6hqqA==", "signatures": [{"sig": "MEYCIQCphf1zGTT6v4L5rGoe1fi6MVh4Pi9wUUruwWtKkeHvRAIhAKpoFBIQF7xyB0ByaQ3QmmxKkVELYjPFDfPcvW5Nvj/1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4365}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.6_1723102975999_0.5721039171646769", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-regex-util", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-regex-util@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "d76bc217fa0670a5cbb626857b83212015ba903d", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-EUHpErwer3WQVcX1wKlvGHtIzVnGz5PzFVzp3/PzI+kTGduDvo0JuOnKHWuBYVwc5TADQmrdNiVU3+25swZoug==", "signatures": [{"sig": "MEUCIQDuusUMbnZBye9FNVwCvlmAhd1AXERtl6/i7mF0cMSkjgIgS8Clx3NxHc0LcvcFfCa0CkBAqx50RLWMTdnFLkS8muc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4366}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-alpha.7_1738225702747_0.6191978536547291", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.1": {"name": "jest-regex-util", "version": "30.0.0-beta.1", "license": "MIT", "_id": "jest-regex-util@30.0.0-beta.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "2bc0e79d45e1778c52c2c68ba56ae51dd26cf409", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-rwTQLbHe2kqHJ+XeXuWveWmLeliHFvOqsc+MxjAFeUh7CRIp8C+9o8UkjLRS5pLGARsh2UB9o+1znptr1I9vSQ==", "signatures": [{"sig": "MEUCIQDQRb4k6zQuKlIpTYjn9rOWzWLecdLAy3yfcyIUffay6gIgHu12vD+AH6NVX1zVRDeokuVzScR+PfC528ar4PabsJ8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4364}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ca9c8835e3c74ec17450cac43c7cd3e8bb5747b2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-beta.1_1748306895243_0.15413790378593273", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "jest-regex-util", "version": "30.0.0-beta.3", "license": "MIT", "_id": "jest-regex-util@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "ea7e91608e57e59ee80d981a0e18f22dd2367f93", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-ki<PERSON>aZ35ogPivxgLEGJ1jNW2KBtvmPwGlPjy5ASHiVE3kjn3g80galEIcWC0hZV6g5BtTx15VKzSyfOTiKXPnxQ==", "signatures": [{"sig": "MEUCIGNqa1zENRHEPaGdaW5+CDTBK1HR75oD+0nSSOqgcW0xAiEAvUOmJptbI0ArSp5mumZlgcpLsmIxwm+ETPNAAKr23Mw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4364}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-beta.3_1748309254782_0.030085215878448368", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "jest-regex-util", "version": "30.0.0-beta.6", "license": "MIT", "_id": "jest-regex-util@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "e33a302d18d50e0469e19aef13b1188dedf7d8d9", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-1b7faO9wyxDJwPLPardG1+MoGxB/Py6g0nHqEoBEmqpz+ia60ZWGpJOUkbD7TP+YRAkkWx2KKQPoZC9ua66zPg==", "signatures": [{"sig": "MEUCIQCx/DTztxjOgEVAe2i3/dN2hLFe1LF80yvxFd7J8pjc5wIgNUV18h4DfBqUA1FgDeGDQmqWAGx+FjqCPy3jWafsATU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4375}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0-beta.6_1748994637354_0.5181242963485857", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "jest-regex-util", "version": "30.0.0", "license": "MIT", "_id": "jest-regex-util@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "031f385ebb947e770e409ede703d200b3405413e", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-rT84010qRu/5OOU7a9TeidC2Tp3Qgt9Sty4pOZ/VSDuEmRupIjKZAb53gU3jr4ooMlhwScrgC9UixJxWzVu9oQ==", "signatures": [{"sig": "MEQCIBOlYvJdEXcRwqf/rGTYPc/qhcuqGpM53X9HSFXb0AYRAiBJYI9ECLQLrRHzlGXXBjhK1hW4plAi78UXmxpY57Y/zQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4368}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-regex-util"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-regex-util_30.0.0_1749521739697_0.6227331711052149", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "jest-regex-util", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-regex-util"}, "devDependencies": {"@types/node": "*"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "jest-regex-util@30.0.1", "dist": {"integrity": "sha512-jHEQgBXAgc+Gh4g0p3bCevgRCVRkB4VB70zhoAE48gxeSr1hfUOsM/C2WoJgVL7Eyg//hudYENbm3Ne+/dRVVA==", "shasum": "f17c1de3958b67dfe485354f5a10093298f2a49b", "tarball": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.1.tgz", "fileCount": 5, "unpackedSize": 4368, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDbk9vmpGxNB5ygjKmJMR9tdrwYN7/yQ2weTg3IM/9SrAIgLF/tza1cwUCSqB8CCgzKtPlIrzoyvaLP3bJN71qwfUI="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jest-regex-util_30.0.1_1750285876832_0.9567932065949878"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-02-07T17:24:20.639Z", "modified": "2025-06-18T22:31:17.208Z", "0.0.0": "2017-02-07T17:24:20.639Z", "18.5.0-alpha.7da3df39": "2017-02-17T16:57:48.695Z", "19.0.0": "2017-02-21T09:30:23.433Z", "19.1.0-alpha.eed82034": "2017-03-17T00:41:23.349Z", "19.2.0-alpha.993e64af": "2017-05-04T15:37:38.839Z", "19.3.0-alpha.85402254": "2017-05-05T11:48:19.902Z", "20.0.0": "2017-05-06T12:32:33.138Z", "20.0.1": "2017-05-11T10:50:04.753Z", "20.0.2": "2017-05-17T10:50:19.270Z", "20.0.3": "2017-05-17T10:57:09.767Z", "20.1.0-alpha.1": "2017-06-28T10:16:20.568Z", "20.1.0-alpha.2": "2017-06-29T16:36:47.409Z", "20.1.0-alpha.3": "2017-06-30T14:20:53.762Z", "20.1.0-beta.1": "2017-07-13T10:33:42.746Z", "20.1.0-chi.1": "2017-07-14T10:25:04.432Z", "20.1.0-delta.1": "2017-07-18T08:46:54.083Z", "20.1.0-delta.2": "2017-07-19T12:56:43.654Z", "20.1.0-delta.3": "2017-07-25T22:12:24.175Z", "20.1.0-delta.4": "2017-07-27T17:19:08.017Z", "20.1.0-delta.5": "2017-08-01T16:33:36.547Z", "20.1.0-echo.1": "2017-08-08T16:49:56.291Z", "21.0.0-alpha.1": "2017-08-11T10:14:05.093Z", "21.0.0-alpha.2": "2017-08-21T22:06:48.371Z", "21.0.0-beta.1": "2017-08-24T21:26:46.987Z", "21.0.0": "2017-09-04T15:01:51.222Z", "21.0.2": "2017-09-08T14:19:16.140Z", "21.1.0": "2017-09-14T01:50:08.691Z", "21.2.0": "2017-09-26T20:22:14.647Z", "21.3.0-alpha.1e3ee68e": "2017-09-28T14:20:37.390Z", "21.3.0-alpha.eff7a1cf": "2017-10-01T16:46:48.612Z", "22.0.3": "2017-12-19T14:58:53.474Z", "22.0.5": "2018-01-09T15:09:53.218Z", "22.0.6": "2018-01-11T09:46:41.616Z", "22.1.0": "2018-01-15T11:57:14.189Z", "22.4.3": "2018-03-21T16:08:06.923Z", "23.0.0-alpha.5": "2018-04-10T19:18:17.122Z", "23.0.0-alpha.5r": "2018-04-11T05:52:47.179Z", "23.0.0-alpha.6r": "2018-04-12T07:01:31.994Z", "23.0.0-alpha.7": "2018-04-17T18:55:01.330Z", "23.0.0-beta.0": "2018-04-20T10:10:25.350Z", "23.0.0-beta.1": "2018-04-21T15:44:16.825Z", "23.0.0-beta.2": "2018-04-26T21:17:29.793Z", "23.0.0-alpha.3r": "2018-04-30T13:10:03.071Z", "23.0.0-beta.3r": "2018-04-30T13:14:44.630Z", "23.0.0-charlie.0": "2018-05-02T10:56:06.574Z", "23.0.0-charlie.1": "2018-05-03T12:10:05.668Z", "23.0.0-charlie.2": "2018-05-15T09:51:14.248Z", "23.0.0-charlie.3": "2018-05-22T14:58:50.539Z", "23.0.0-charlie.4": "2018-05-23T10:42:06.311Z", "23.0.0": "2018-05-24T17:26:13.539Z", "23.3.0": "2018-07-04T12:22:49.312Z", "24.0.0-alpha.0": "2018-10-19T12:12:36.809Z", "24.0.0-alpha.1": "2018-10-22T15:35:42.444Z", "24.0.0-alpha.2": "2018-10-25T10:51:01.659Z", "24.0.0-alpha.4": "2018-10-26T16:33:09.570Z", "24.0.0-alpha.5": "2018-11-09T13:12:40.579Z", "24.0.0-alpha.6": "2018-11-09T17:49:36.437Z", "24.0.0-alpha.7": "2018-12-11T16:07:42.530Z", "24.0.0-alpha.9": "2018-12-19T14:25:00.986Z", "24.0.0-alpha.10": "2019-01-09T17:03:04.571Z", "24.0.0-alpha.11": "2019-01-10T18:33:26.820Z", "24.0.0-alpha.12": "2019-01-11T14:59:48.853Z", "24.0.0-alpha.13": "2019-01-23T15:15:24.307Z", "24.0.0-alpha.15": "2019-01-24T17:52:27.471Z", "24.0.0-alpha.16": "2019-01-25T13:41:57.274Z", "24.0.0": "2019-01-25T15:04:53.762Z", "24.2.0": "2019-03-05T11:22:43.934Z", "24.2.0-alpha.0": "2019-03-05T14:46:38.843Z", "24.3.0": "2019-03-07T12:59:18.849Z", "24.9.0": "2019-08-16T05:55:47.017Z", "25.0.0": "2019-08-22T03:23:44.026Z", "25.1.0": "2020-01-22T00:59:44.564Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.005Z", "25.2.0": "2020-03-25T17:57:54.289Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.466Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.280Z", "25.2.1": "2020-03-26T09:01:04.106Z", "25.2.6": "2020-04-02T10:29:08.180Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.494Z", "26.0.0-alpha.1": "2020-05-03T18:47:55.505Z", "26.0.0": "2020-05-04T17:52:56.256Z", "27.0.0-next.0": "2020-12-05T17:25:07.657Z", "27.0.1": "2021-05-25T10:06:23.991Z", "27.0.6": "2021-06-28T17:05:31.383Z", "27.4.0": "2021-11-29T13:36:54.903Z", "27.5.0": "2022-02-05T09:59:17.823Z", "27.5.1": "2022-02-08T10:52:12.063Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.409Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.146Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.551Z", "28.0.0": "2022-04-25T12:08:01.580Z", "28.0.2": "2022-04-27T07:44:00.349Z", "29.0.0-alpha.0": "2022-07-17T22:07:05.959Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.393Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.028Z", "29.0.0": "2022-08-25T12:33:24.628Z", "29.2.0": "2022-10-14T09:13:41.292Z", "29.4.2": "2023-02-07T13:45:22.298Z", "29.4.3": "2023-02-15T11:57:16.447Z", "29.6.3": "2023-08-21T12:38:57.756Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.823Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.477Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.525Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.034Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.383Z", "30.0.0-alpha.5": "2024-05-30T12:43:53.055Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.128Z", "30.0.0-alpha.7": "2025-01-30T08:28:22.934Z", "30.0.0-beta.1": "2025-05-27T00:48:15.431Z", "30.0.0-beta.3": "2025-05-27T01:27:34.957Z", "30.0.0-beta.6": "2025-06-03T23:50:37.539Z", "30.0.0": "2025-06-10T02:15:39.888Z", "30.0.1": "2025-06-18T22:31:17.002Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-regex-util"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}