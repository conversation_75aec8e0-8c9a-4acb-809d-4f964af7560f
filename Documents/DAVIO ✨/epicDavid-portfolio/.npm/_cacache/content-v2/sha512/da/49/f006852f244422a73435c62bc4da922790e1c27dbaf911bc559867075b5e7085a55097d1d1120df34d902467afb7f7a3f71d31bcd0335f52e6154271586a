{"_id": "picocolors", "_rev": "13-58fe5f16d1f02728e1d3449e7c45aa95", "name": "picocolors", "dist-tags": {"latest": "1.1.1"}, "versions": {"0.0.0": {"name": "picocolors", "version": "0.0.0", "keywords": [], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "49a918ed3d7b45b6eb66ece846ff633d4a8ca998", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-0.0.0.tgz", "fileCount": 6, "integrity": "sha512-u+nZsm/+tsU/kZVwfIx2kiQK+mrWeAfZhWUgv/WjMxPZMo7LbtxsqaW3tLj0oLPGFfddCN27VVSHVXPBCS52Hw==", "signatures": [{"sig": "MEUCIA4mrnT4dIJ7k4RwkoozvhFix/vpZnLUDUhQI6OgCzIWAiEAmB6ofYzri8+Csw2OHWWWzybqCMjo6BJdpoOTQuj3nsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7574}, "main": "picocolors.js", "type": "module", "types": "./picocolors.d.ts", "module": "picocolors.js", "exports": "picocolors.js", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"printWidth": 100}, "_npmVersion": "7.24.0", "description": "npm install picocolors", "directories": {}, "sideEffects": false, "_nodeVersion": "16.1.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/picocolors_0.0.0_1632702821501_0.20486214350583465", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "picocolors", "version": "0.0.1", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "bf72dffc7f5865238d4ca0b5b454426318e5f317", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-0.0.1.tgz", "fileCount": 6, "integrity": "sha512-z+EC2NUawv06CzNJ7mKl0AxlsZ6ScBjowssoNQjg86gJ7ApYBmy+4LSdTJJC9vNVqorZqnQfWBTiBA22XvijwQ==", "signatures": [{"sig": "MEUCIAPlVOJ3Wfz2zXwo5YwtCrZdhDU2G1pGgyWFMLI7gV9pAiEAllDZXwcOwMVAnqd564adeP0mPRncDhmGDLURII2D0KE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8630}, "main": "picocolors.js", "type": "module", "types": "./picocolors.d.ts", "module": "picocolors.js", "exports": "picocolors.js", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"printWidth": 100}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "The tiniest and the fastest coloring library ever", "directories": {}, "sideEffects": false, "_nodeVersion": "16.1.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/picocolors_0.0.1_1632716840457_0.9972960427570001", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "picocolors", "version": "0.1.0", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "e10d446a03ca5f127e9680865fbfe80ee5148266", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-W+3MFREUEjPt0MnYaR51VkLw8tY8UubrLOqcBmaQocZhM34hQhjg50FQ0c6f0UldPlecieoqUqI6ToM/dNblDw==", "signatures": [{"sig": "MEUCIFS+Gg/jzKDrEC5SXggXa/rQWLGy49z1jKm5V1Kf748VAiEAosYOXjuUJHQBrjXIOpT3A3tDvLWaqVTWB/LNfuCtjkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9293}, "main": "./picocolors.cjs", "type": "module", "types": "./picocolors.d.ts", "module": "./picocolors.js", "browser": {"./picocolors.js": "./picocolors.browser.js", "./picocolors.cjs": "./picocolors.browser.cjs"}, "exports": {".": {"import": "./picocolors.js", "default": "./picocolors.js", "require": "./picocolors.cjs"}, "./package.json": "./package.json"}, "gitHead": "5e8b0ecdd4c957efa9e5d754ede84e0074b3a11f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "The tiniest and the fastest coloring library ever", "directories": {}, "sideEffects": false, "_nodeVersion": "16.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/picocolors_0.1.0_1632762597116_0.6149763019653798", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "picocolors", "version": "0.2.0", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "acaed2addf94979a88a88937be5c0ee383ff5fc8", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-0.2.0.tgz", "fileCount": 6, "integrity": "sha512-ugstvUBHAKmJT290ILoc6h1ru8HUoG9rDdvUPZTwnTui5SjraaFvVs51yo6fPffeC+oqgj8AOJUIrIH7XhoqDQ==", "signatures": [{"sig": "MEUCIFZJgGz1DuUbDC2aw4br4YQjSN5tWVBsdNrWg9lUe7nYAiEAvPhr7CiFRaHFQic9MZim/XT09jAXNqS9owod7va5ml0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5764}, "main": "./picocolors.js", "types": "./picocolors.d.ts", "browser": {"./picocolors.js": "./picocolors.browser.js"}, "gitHead": "e55798dd0f103aa819f5d6b49a06b0eb1721bb68", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "The tiniest and the fastest coloring library ever", "directories": {}, "sideEffects": false, "_nodeVersion": "16.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/picocolors_0.2.0_1633231838357_0.9170208672045521", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "picocolors", "version": "0.2.1", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "570670f793646851d1ba135996962abad587859f", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "signatures": [{"sig": "MEYCIQCDJ8P2zS1VDbCPIt5X/Xe5a5lT466xo2jTZ0d5OQD9pAIhAKG+U/jHs7kLWrNMsBYFCit/suSHUwhHLI1ZXFmdTnD8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5813}, "main": "./picocolors.js", "types": "./picocolors.d.ts", "browser": {"./picocolors.js": "./picocolors.browser.js"}, "gitHead": "aaf57e14b250112c6ad4fbeff08ad78cafc6c887", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "The tiniest and the fastest coloring library ever", "directories": {}, "sideEffects": false, "_nodeVersion": "16.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/picocolors_0.2.1_1633362735585_0.028342782810027556", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "picocolors", "version": "1.0.0", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "cb5bdc74ff3f51892236eaf79d68bc44564ab81c", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==", "signatures": [{"sig": "MEUCIQDxCChHxcIJX0CufwbTxL7HtG+9h9Me4kC3TCHewLhm0QIgGrMSC82ddA69hW03p8i/KWOfPQ57gHU1C9FyWjtpdzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh22nwCRA9TVsSAnZWagAAufkP/R/CYVS1T3kooVHNOWZ3\nsIFVEZ0qe/zdgbUt3+leoirZ6ZGt16TuzynSZWLgZgLH9TELft0P3CXZbCuM\nuqWdH6NxFwNwadD8hRos2+XG81xHrrb7rDHdrjjwJXZq1e66mqJuYDDX1MOe\nUNIYuZUQ+qiBhzjMKj6tvkOPpuf8t8kl0j1lPlunityAjGOmrmv4JGhtYKB5\nUZtkz39+BEiDL6NDzCsjMg3v9In+T9phNlpNYup44cgJ29U+zAxAatEOA8PM\nbBB+ynEz+R0ewz9s+eo5WYP9dJgH3ztmadcXUxOhqSiZ9AMtsJyS3JSqYZUX\nEY+9Bk8J7t0CJxI1ErrphVoKc5rU8gykxC7RFvLCKGsu9EtPqHmnApObtlh6\nYZDBikEKtyEi5F4lzFhrYIkX78pJm53/zM43vST5igJ5z26crugx1DzEdUhO\ngfYGvR8/NoTMotY1UJPocEGLmWm6rt+es1a3z0lgxbeVBbcV7Bevpr8ZB1/9\njtmrdVvNuAOduDaQKr7JfeEbyJXePTzfsuocechG0POku0BQ9CPb4dLGqMAr\nFW+4jNef32pfTzy1ElFtSzH9hPKqRQ2iuldLRKqupjN6MFOHJehUc88PH6Ij\n7bBxxTtWh/fRhHEDEub4BFJ7b+iZ80yc59mb279VTdnzTChku4LHwPJwLU/I\nY10t\r\n=jcIN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./picocolors.js", "types": "./picocolors.d.ts", "browser": {"./picocolors.js": "./picocolors.browser.js"}, "gitHead": "228cea3fa726857785b8c069cccc58e9743cd71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "directories": {}, "sideEffects": false, "_nodeVersion": "16.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/picocolors_1.0.0_1634093321958_0.004333165104213554", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "picocolors", "version": "1.0.1", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "a8ad579b571952f0e5d25892de5445bcfe25aaa1", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==", "signatures": [{"sig": "MEQCIHuZZ408Sz7LgMrYOGWawXknta+2zyOqyVD0M4bMjgi9AiAjuoOvgUdpZW4GWBAgkeJO4zlcqUy2OALH0kVakgXMNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5152}, "main": "./picocolors.js", "types": "./picocolors.d.ts", "browser": {"./picocolors.js": "./picocolors.browser.js"}, "gitHead": "a014200ab27c654a40072d671654b1a898a0940b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/picocolors_1.0.1_1715654655866_0.39768819945887346", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "picocolors", "version": "1.1.0", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "picocolors@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "dist": {"shasum": "5358b76a78cde483ba5cef6a9dc9671440b27d59", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-TQ92mBOW0l3LeMeyLV6mzy/kWr8lkd/hp3mTg7wYK7zJhuBStmGMBG0BdeDZS/dZx1IukaX6Bk11zcln25o1Aw==", "signatures": [{"sig": "MEYCIQCGdOz7aCAxkzvKMgNtO3M+vMu+g8n6qL7uzUQj2R540AIhAMhGMaNYxFxFJDBszlKINZn1UatN7BbskLVJo1EitaE7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11429}, "main": "./picocolors.js", "types": "./picocolors.d.ts", "browser": {"./picocolors.js": "./picocolors.browser.js"}, "gitHead": "6f6011182c0f16cf119019ca9bef1fadbe86b913", "scripts": {"test": "node tests/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "tabWidth": 2, "printWidth": 100, "arrowParens": "avoid"}, "repository": {"url": "git+https://github.com/alexeyraspopov/picocolors.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "directories": {}, "sideEffects": false, "_nodeVersion": "22.4.1", "clean-publish": {"cleanDocs": true}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.2", "kleur": "^4.1.4", "prettier": "^2.4.1", "benchmark": "^2.1.4", "cli-color": "^2.0.0", "colorette": "^2.0.12", "nanocolors": "^0.2.12", "ansi-colors": "^4.1.1", "clean-publish": "^3.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/picocolors_1.1.0_1725320790830_0.6018633231704895", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "picocolors", "version": "1.1.1", "main": "./picocolors.js", "types": "./picocolors.d.ts", "browser": {"./picocolors.js": "./picocolors.browser.js"}, "sideEffects": false, "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "author": {"name": "<PERSON><PERSON>"}, "repository": {"type": "git", "url": "git+https://github.com/alexeyraspopov/picocolors.git"}, "license": "ISC", "_id": "picocolors@1.1.1", "gitHead": "6f0a4638348ed20633d623ee973f9c9a96f65104", "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "shasum": "3d321af3eab939b083c8f929a1d12cda81c26b6b", "tarball": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "fileCount": 7, "unpackedSize": 6373, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1ga0Ghvdul0FY1X3tDEHWjLF2ImSjnhKBBPgfOq1CvAIgSrqciFjzA8uVTKSuA6NKBADmO/cxtGcYXQhHjD0U1Nw="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/picocolors_1.1.1_1729102803686_0.7395028803649382"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-09-27T00:33:41.501Z", "modified": "2024-10-16T18:20:04.161Z", "0.0.0": "2021-09-27T00:33:41.649Z", "0.0.1": "2021-09-27T04:27:20.577Z", "0.1.0": "2021-09-27T17:09:57.265Z", "0.2.0": "2021-10-03T03:30:38.511Z", "0.2.1": "2021-10-04T15:52:15.750Z", "1.0.0": "2021-10-13T02:48:42.115Z", "1.0.1": "2024-05-14T02:44:16.014Z", "1.1.0": "2024-09-02T23:46:31.018Z", "1.1.1": "2024-10-16T18:20:03.921Z"}, "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "repository": {"type": "git", "url": "git+https://github.com/alexeyraspopov/picocolors.git"}, "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# picocolors\n\nThe tiniest and the fastest library for terminal output formatting with ANSI colors.\n\n```javascript\nimport pc from \"picocolors\"\n\nconsole.log(\n  pc.green(`How are ${pc.italic(`you`)} doing?`)\n)\n```\n\n- **No dependencies.**\n- **14 times** smaller and **2 times** faster than chalk.\n- Used by popular tools like PostCSS, SVGO, Stylelint, and Browserslist.\n- Node.js v6+ & browsers support. Support for both CJS and ESM projects.\n- TypeScript type declarations included.\n- [`NO_COLOR`](https://no-color.org/) friendly.\n\n## Docs\nRead **[full docs](https://github.com/alexeyraspopov/picocolors#readme)** on GitHub.\n", "readmeFilename": "README.md", "users": {"yang.shao": true, "flumpus-dev": true, "supan_20220713": true}}