{"_id": "why-is-node-running", "_rev": "40-1a9c56edf0a8fcc7a8cf331acfcf0371", "name": "why-is-node-running", "dist-tags": {"latest": "3.2.2"}, "versions": {"1.0.0": {"name": "why-is-node-running", "version": "1.0.0", "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "dist": {"shasum": "8038ee217590d091bb5dd4652ff427658eb13541", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.0.0.tgz", "integrity": "sha512-fvL395Y59ILMhak2UYL69K0ZjFjbhIS8ZFyOYMO7EjeYpuMvSBX89OvaHe89Gg+77tYjmjMg16Du3m2QulZCwg==", "signatures": [{"sig": "MEQCIEbYYpaEHz/kt5Amk0lXSFEbkjhX+segJsYEaSY5+XJkAiBaHRttpKJWE5vrhPhA9ZGpIRuLBYvTXklxOeK53Ed7qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8038ee217590d091bb5dd4652ff427658eb13541", "gitHead": "dc3351016550b432a549eb62e8becf119416e14e", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"stackback": "0.0.2"}, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.0.0.tgz_1455031783349_0.3272082884795964", "host": "packages-9-west.internal.npmjs.com"}}, "1.1.0": {"name": "why-is-node-running", "version": "1.1.0", "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "dist": {"shasum": "5a6e8258c8295e663c08306bad49668c4862a1f3", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.1.0.tgz", "integrity": "sha512-7C373iQN+iA0iP68zhMixM3nj7PA93S+gR7FptjMGhgWxfuSXmO3wmxuMT+PTNi8p6+92x9eY05UUWKQz6BbZw==", "signatures": [{"sig": "MEUCICf6w3ScXc/Hw/9W0YrcEuuGb4nRaQurgKKUl2eEPfMLAiEAgZoSb2FyhL1+QW2iqr6UTBHHo47LrP7ohZz6XhI0SVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5a6e8258c8295e663c08306bad49668c4862a1f3", "gitHead": "4e3c14f4ce28747e0ca2fbdc114775c2308a58ea", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"stackback": "0.0.2"}, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.1.0.tgz_1455033498679_0.15505575784482062", "host": "packages-5-east.internal.npmjs.com"}}, "1.1.1": {"name": "why-is-node-running", "version": "1.1.1", "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "dist": {"shasum": "1093ec96feed0cfe5d50c0f9df6fbe650af2e8f5", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.1.1.tgz", "integrity": "sha512-LUQL548gFJiaKInXGzun3RFQ0OQ8cpMtBt2RJPSsm7YVNTjmot3Ayi6dEOYaP09rfknroNpxNoRRzzHS4js+hQ==", "signatures": [{"sig": "MEUCIQDWfLyMZ2ZH3agHu3clVY+xvUlCE9V/6OzoMf/HE3BWuwIgegZ6lt4u9q3l2Au37d0aZaRxzB3IZcm8zGhCxfBikqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1093ec96feed0cfe5d50c0f9df6fbe650af2e8f5", "gitHead": "4bb0cccb42963defd8af411d85e7847dece77d82", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"stackback": "0.0.2"}, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.1.1.tgz_1455192052962_0.5559456376358867", "host": "packages-9-west.internal.npmjs.com"}}, "1.1.2": {"name": "why-is-node-running", "version": "1.1.2", "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "dist": {"shasum": "b2d08060170930a574f5ce2d2299d5fc3912a1f1", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.1.2.tgz", "integrity": "sha512-gDvCwpvev28KD28BlT5xby3r2PBPWpNtE+KSbYKzjMkBLHOKnZ5GUk5s8xYLeI2T8kC6D7q1gQPM6fEl8qIV+A==", "signatures": [{"sig": "MEYCIQCYaT/4ORI2FP1ZGS7/MwRrxcUuxxJ/Qy18I0+L4IXWfAIhALWCavpASnF3YtAKW9fbearUXIsFTC6DaZq6Jz9DtNSP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b2d08060170930a574f5ce2d2299d5fc3912a1f1", "gitHead": "8f7d989992d1053457611a009f3133ed4d88d2e6", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"stackback": "0.0.2"}, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.1.2.tgz_1455323946141_0.6603264682926238", "host": "packages-9-west.internal.npmjs.com"}}, "1.2.0": {"name": "why-is-node-running", "version": "1.2.0", "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "cbbf8e37357a873ea7d5c7c658724827add80d6a", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.2.0.tgz", "integrity": "sha512-UYhZwcRa50H323wjq6d75gwI1y4/O/7Scit0akokv5kVcbQpkqyhtjgqQmjm3JXjMwLvqTNF1cXXY6aVl9/wAg==", "signatures": [{"sig": "MEUCICCQ6MIlSuyqxvsWSHyt2XS2w/cZ7oT217QxvvE/xlQNAiEAxQHFGRf/UeUjUJhNYkL+KHSF8AhLerBQRkD3uJGfaCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cbbf8e37357a873ea7d5c7c658724827add80d6a", "gitHead": "b1d17018e1e0b6bdec31bd4116a1e75b36d212c9", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"stackback": "0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.2.0.tgz_1455365308908_0.4343762774951756", "host": "packages-5-east.internal.npmjs.com"}}, "1.2.1": {"name": "why-is-node-running", "version": "1.2.1", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "cbd818c471e1c53fd6f09f10fadbeeefa30c272f", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.2.1.tgz", "integrity": "sha512-a6wbkcMYe4W9HLrM7C84i4TpOAxTpm+KdBOHBnmi8nHlm75fJGaFJnSbgfqpo9Zixdfi7Crq1waPg/1QIYfw5Q==", "signatures": [{"sig": "MEQCIF8ABFlBYw2w+tnmBsL+OUiXpIVepxlrNDHsSpXHM1hMAiBOQ4XjzWWHQ9HEgRGxZ0+U6r9l4uO4VjiXVdMuqsnsUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cbd818c471e1c53fd6f09f10fadbeeefa30c272f", "gitHead": "69cc3cfe3959755b5c560ec8ea6408605ec42a5e", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"stackback": "0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.2.1.tgz_1455618314779_0.5188400293700397", "host": "packages-5-east.internal.npmjs.com"}}, "1.2.2": {"name": "why-is-node-running", "version": "1.2.2", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.2.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "ca36282e3d93ba4bf4886f709b6d1cba4cc8824b", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.2.2.tgz", "integrity": "sha512-JP1gJ5aCIo3x8/u8K0qR7m84SEVslxs4SYDxXPo/mO40En1mcwPgQUascqh8MCxVyCxCQjXKvC4r/H7UMRQkuA==", "signatures": [{"sig": "MEQCIHhQsjrT1mopZxn5jqBMs4EAVhuz2FwbQzRwHxCjpmKmAiALyhIRNy8OPk4hSWnfEWV/D863ol3R4cBDupd3uGXriw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ca36282e3d93ba4bf4886f709b6d1cba4cc8824b", "gitHead": "5a843e45fdbd437a0c2011df462f8f81b9a5da67", "scripts": {}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "4.4.1", "dependencies": {"stackback": "0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.2.2.tgz_1458855088556_0.8565164131578058", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.3": {"name": "why-is-node-running", "version": "1.2.3", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "85fe9a20b269a8db80e2a8489db7e5f78e710742", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-1.2.3.tgz", "integrity": "sha512-Y8ip4Gagz6GI7P7Q8AbKkyNR52LZEXH98rfncNaOHHBvPk/L409CYvheqpRJUoaDcyx9pxwLiEzEf1ZJ7U1KPg==", "signatures": [{"sig": "MEUCIGnOmwC+PfWQoof5iR58sxTbY6JPcSMHWMypB4GMP5MaAiEAzDcwBtILV9uz1z3dT4tpw/RSk9/cuF9j5SDXg7lzYos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "bf9701b28829a88bde624cc9f22aa011aa46b1ef", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"stackback": "0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running-1.2.3.tgz_1508329947606_0.08386365999467671", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "why-is-node-running", "version": "2.0.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "d56bb4145b079744d21eab01d4585f994821a298", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-6iUgV0Kogq5bsf/uU0xnqsXLMyNECQGrjccbLt+uHQ/JJe9GNaRCOsWgyf9sy5e/XNPyTOyvyhMr5pEMRAs96Q==", "signatures": [{"sig": "MEYCIQCCzTWiF4XzhOV08jaqTMgjkPcpVNp0sgKW9pur/K4CuQIhANEEUKCiPB59B0ycTKhdSha9kFTBDh8OynUvASk1OzSZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5999}, "main": "index.js", "gitHead": "614e98c3e7e9fb22b6d06c499ba0593978fdf4c1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.0.0_1518626132035_0.5411721022298808", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "why-is-node-running", "version": "2.0.1", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "13563ac19b7d541a0c62f99b10737c07eb34e75b", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-KyC4iyAUtuecMjFZVTajMQdUhvNaqeaKm8XqXYaI47FQSuLXAKtUjeD0QZm3cDmBZk0bawMQzWVt0M40XHZqsA==", "signatures": [{"sig": "MEQCIFMe36wdaVsD702cFur4tSPhCojVao7LflltAXw4yE3nAiA6mZ38PXvUGurv+LkIDbUV3IiFxeXx0hjmNjfqTEaTBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6011}, "main": "index.js", "gitHead": "0e1f2415f46aa5ee05b0f2d2b2d31e2d33d167b2", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.0.1_1519908374674_0.3557258783811994", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "why-is-node-running", "version": "2.0.2", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "faf352f095356c8c37a28bf645f874e5648c8d02", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-tZABMETAIVjlRGn66r9GPBxboN41ECwYl7dlKABxMY4ow/XyMkSF67h7k206Y+K75VB2kB4Ymx+BfYlB2I3Opw==", "signatures": [{"sig": "MEYCIQCU5f80orcHzRGgojkGir/7PJJtL8ne8eoppYzKnUr0+AIhAIyeSH+YeiHmiHa5aqpFTeoDIRT0tqeIeaKHdEP63K1n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6039}, "main": "index.js", "gitHead": "fe400e68a5329427507797189bda075a06e6c8d0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.0.2_1520251554646_0.45468180358679167", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "why-is-node-running", "version": "2.0.3", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.0.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "86619c2861405d3509f55268684fc85e7f4ce145", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-XmzbFN2T859avcs5qAsiiK1iu0nUpSUXRgiGsoHPcNijxhIlp1bPQWQk6ANUljDWqBtAbIR2jF1HxR0y2l2kCA==", "signatures": [{"sig": "MEUCIQC0zKiz/AglpmyHm07vXlOLPSR5sIPRygLxXa5foV4fOgIgXu3thIoGpzKysLcxLI9U2GJB9L1OTD6Go6mo6DkZzIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiFh/CRA9TVsSAnZWagAAyQAP/RFvbDEv7ay5XXaQ25mH\n2w/Ge71nzkXNyYnjyqXjp2T77mj1fmcaCLr0GFDiW5f0qsQD2n02wCtJjkzT\na0bV5KEzW3qtzlj0mK5QidiF0yRXElG727hPkrfs2XQiBhXNrgb7LPaGqsvo\nF2lwNzDLRLcxjVww9ziEGqBWiLDcU6WE75B52QW3VGoly8GybcxJf4LnPrVZ\n1XVAqOZz2ztE0b3mfVpoqPbzCBGaoQ0XA1E5IWkZEKEPyKgMfJ8aGcqQKP1V\ntysVAThv75NxCzLKgs1GnxgfspdSSLOzPHLnBhPojEM88pRXxGb4iH8tNnEK\nX9Dr12bf/VERpVFtliWLnasDLTX0vFnBUh5r5ZhBft39BP4zu2SH3cX4HG4L\nmuuCMPqEP1QFzwBlNv3hdjIgXYG6WqUi8/Z/SzEyMigrWyamALehPUSoA5et\nlfGyLp3x5lnRQ9kcbCZGnQ1NM8uFVrhfsQ4h2KFjwDoBpFfZy7gEcdQLU00K\n/41xcRriXohESQaWARVSs1Z+IXN1kSwtGA7GVuuuj/izaA/jSUEHv+tQWrWs\nSz80gtfMluHDdI/8wngOkuU1xd3nEBYdglWQWUZz9FaiIUvaw8byyr0mmm+W\nhco+IAkIi6Ef1Dzg/ak1PD/bfi3tuuWX9SRYoNrG4dekW+BPNSg7SAk5emxv\n3H3A\r\n=4SrA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "edb5c64e7041dae4bdb4d71dd7a8a1902f6f4b12", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.0.3_1535662206587_0.6520656088519554", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "why-is-node-running", "version": "2.1.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "e71a9816a32ca4d4f759aabe90edd1007d805f48", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-oLmJ1uZOaKra+GDmYcUHMnVhi4CnZnlt4IE3J05ZDSEAiejeB5dMoR4a4rGcMWRy1Avx24dGTw8yxJ/+EmwPBQ==", "signatures": [{"sig": "MEUCIQDNVQWudLy8GxNrpXJ300YbUBhMS6ZXKILbttpcOabyKwIgdo/rpLXTUtnRzUskiJogb29fZHtgdtCQ1C4fBs0KvRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpMcLCRA9TVsSAnZWagAAF9kP/j28ejoA1pgHgq8fau1l\nL1OvWR9H4firOaPbCu/7APzY6svoKIYMixJeMxrUSXryJPIYgXVo4zThoDmE\nMTVLlQZNkei6QKFoyuNbeGwkYlBWAWoWaeLdmUUg4VCsSS64lIr72BjK08SW\n/41v1/QruedI2VBAFZwaguGYAQVxCKEYo2RGd/tfmPjon8ZuZj0rqQ/I2cff\nH7uAUDZko2fo6fG9xLHtQVsm1kDrWufkX8Crh45nWw/CmgM/M9gHq8oZqdXM\nxikg5ICMykKd55dqkShBOwOR1vnKylLeNrq9w70kVFLsdRuyIM0gbYfx2dz0\nwy6IJ9UqI7xHpiZs1XJt0GvjE75yQTmbLECrt8XOjiQUEIrV2QYYPOMJJxIE\nv/EgCCTo422Yf4S1Q+zqVsNFKqtZ6Z7hAWCGmiI2yTg0Sxkb9qWvr1Zxr3AX\nZdoLuSsR2mMyf2GhK9Lvc9/Du0zovdoBm9w/iTke8Z/2rK272a9vBiFTkIDK\nC+hRQ/HbpSMl3B/BvOU5X4pNWS0T/qwA0Va2qhIyRTo7dTutQsZ/eiSpn8U3\nJlHUSvHqHX5oopCUNQz3N/lomjMCBdnOlBc6ZsAy1SGKnM3XG5qcIZjRnjxz\nUhw7r/hrYevI/I51gXgOUiiC9YUmr5u+e2ucVnPYH2aB55N4Q5oeLpi9YX0X\nGE/B\r\n=l782\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "e854ebc03715419653e75c50d2b2816f02f7ff44", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.1.0_1554302730824_0.15283536376210582", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "why-is-node-running", "version": "2.1.1", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "205b16274484e9d809239540027217081b078c47", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.1.1.tgz", "fileCount": 7, "integrity": "sha512-g4MpO2pzYh6lfJEululqxvOg088iypPJEaPMresRSOo4zE7VPg+6ZrN2TYSYUuOVFZSv6zH7iD/KmxexbTUubQ==", "signatures": [{"sig": "MEYCIQCoZJj5NfQBpt7S/1kT8FlMTKpP+RfHMHepJ3BIwSUfRAIhAMF2ZJv5f5uObRdAuN0sew2RLEOw84JJz83ZQpdIf/4V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYrcKCRA9TVsSAnZWagAAEkQP/20NrCVyf47+fYgauzhW\n6uz5G5vQC0ktivxkdOMdypC+/IYbcCWoRya7puNwDxZcXw6H2S+xadN4xH1g\nL3V+Y3chKNAS6+YRY35g+1BG1n7wUCkYsb6mF7DR7EpCgCLbwotRfoYxKKdj\nIiWgowDpyhqdCjURLmHiJ6reeG/3ii+P8ggPj4DYbPtFANA32sdQJebQuOiJ\nzafzUj0XtXyITnHUNUb/eU+rqcPN/vfMUrvAPMi2F55jW0IzBn3h+qeYQHh/\nlRiXcshUsUv+21IuVDLc/Sxv3u8ScQf5MZrtGP2kVK5ROZkZB/F02NykGroB\nhmsvkHEN7JyU1zszswqCnYi6yaYZ+Uf+9LIJPB8zZcUDaX6nzt3Xl+NL1fz2\nIDWo+UsLxSYPQW+hEBR22Z/l7s1RX9v/vlC4RIpGKPYebD13TD6BY688C35H\n3NrgU+Lu4/foZksyNOCRi7TktNIkwvDDenpz/GxCQrXReAYIzJL4drIo3jTQ\nZZQrjiPiQwNB06wzzg4qO9UAt2PDDyJb9UzZsUcNOx1cc/2NfPtMMXurjZmI\n7/ETQqb/WqAGE/m2azXT4SJl8v32labR+yaFWlQ6h7wey+opn2kTnpEoFgNQ\noTxkoaSZFwMgpadiKxwjGnB9yejcrkWNwQgNET4tY6o/TxgRyTLGKIsOK623\nvdYv\r\n=JPGS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "2d83bded68c57507b636cc8f96c73c43f3eb380d", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.1.1_1583527689936_0.10490039061286738", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "why-is-node-running", "version": "2.1.2", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "05465c17ca3ea4ab9624c8b7407535234d4b0ce1", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-TwUeoRNMAWy8jAD8oFLtgmYKecZkH3yCtbQ17CYVCxd1WaPJAEB6oqkNgm0o+wIzxJi1oHUkOxMk0M/t5jCGeA==", "signatures": [{"sig": "MEYCIQCndcBE2u6Hqy+fCaN4GrzuuEodD+yOwA0nLj2PGCyg2AIhALAMkbRMj9/pB5Cs6D8V5paNdFkZPaIq3T3XtV+lWK3a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeY6EBCRA9TVsSAnZWagAA0+8P/35q0sZyOlV2O8OHIyHO\n0t3AsIXHxQEQkSx09zMxMvz7d+jLQaRViM87VOVwjd/RjgW/TNFdzmyg5GV/\nYWd665653MTIVp/APbgr1aJBLrkKkgvmRvuPTq7f/7mqi7WaUfZCqyrYFhFs\ns6S327JvQKQ/+1m70ZZJVCbWTTyixsNvkXP1W/DnxkP6Z9ZGMWHgt2c8oPJg\ndKJ8lFzbvZxnyLdbkXBGXXVBxBU3yQ7iXVZFIZY9c32FfaiBV2PNBtIsuQvC\nos8vtEE2szruYISvYXTHnS7CHPGgsBfNd8YaJ2K3DA2CIq7WT/fS6t60oiPz\nVj8nEbtyAXGDwZMs6ZCtk4j8onmD7bWfMpRctD9AIP+axscIGl+yHHH/4BAD\nm8fXuW5Q+/+5RhjLEyUxqBhed3uQnXnnraMlo1wIdV20V61k9hRhzLkeO9Ul\nHlWWl77usBUOhQfOupmiSe+FhQLmVFk6YvFXroi6MSKNmo3/oz6jNG/7eZ/E\nlNzNPR3X5HR4+FIqmnIf8hbhRQVtaj8aIubKdnJJrYrDoz0kn3T1Nauz7z+e\nowf4uQftN090pkCkg1ySaks/MMlv1UESqxpvgGwpxGbfXNowUjIPD0l0/WTi\nrMHDnJW1JLiDiHtXmcsLNnGYycvxFxnp2N36PAPRjJTPkrHxJgVtM6Q7JMlu\nxxfZ\r\n=JPx7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "d1299a92bd5dae0f8514ca3b1fabe06dbf1c5044", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.1.2_1583587585230_0.7847027824482988", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "why-is-node-running", "version": "2.2.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "fd0a73ea9303920fbb45457c6ecc392ebec90bd2", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.2.0.tgz", "fileCount": 7, "integrity": "sha512-rxtN9D0lJaYyP92BR5yoyWecK2txBKmBIuS7GRbOPP5bXsT37/hBqcmTrlrt25DBr9p4WJb6c9LuYSJd89vHRQ==", "signatures": [{"sig": "MEUCIQCRkvYdOCezSYv3Vk0X93rP2H436Lr/dDnSHkGFvj1XCAIgTLw2f3jvma61A8rSIq94N15cu7JXHh9Wl9Q4+m5Ov2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6S/TCRA9TVsSAnZWagAAV6EQAIy04YROKrfFNvMfK0ZS\nAKve7zatulwRauj1SpE0YsMBQGkUKijqJudVfaBx4fo/R6vQOLRiVDXMayUr\n8o0E6Hbqgv72f5xDiOJ3n+/IUeRny/RAWNeIy44LaIYp+EXJuJrYyAtbJKQY\n8GQTG5ahTBYkjItvWEOfOaoEBFn3rwHIM+WLEzAK03GLlLDvhJ74zmOShm4J\njNVD6PiDUN3LeG1PRwZDYHc2Tn2eYL9gccSfZIDrR0U+HBIWdyyVjF6olhcY\nm8FJg3P/SObzh2U3FySMerSjU7bODZD7aWctnEVlHCggHX76MBsh6wUMZK7N\nnkzaMaZu0vi8QgpBX1GQniY/v7CW8XxK/yX/BEBEqG+DMAx8ddCOJslenE8a\nr+qE/Y/l116r7Zq3aRrvmGfWC1HDtqy2iTl6iHl+4j6kP158iLgYZDe7+7K0\nl0AmFX8HP3YTFrHNZherktk7NMMNdF6talCmnhu1iRfmCa50peToNHt7WlbX\nXN3GckfVmYvnxKN7IF0eGpvrKwyL7bOLyjAyptlov+5D9cuW6uMnY//Mu87W\nwV5WlTISIyc1cCZyRHaqqLqUvdmUvfUVAJJCmLdpjLmYGoLpCcYzLZ44bHhC\nkjIkcDoamB1oZLEIc+LUXRthA4iNKVHk4TjrWkpw7RncTOltaPplUvDVqmqW\n5RJ8\r\n=1nqF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "4c4349765e89ea569568c12b09bacc4eb0b80946", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.2.0_1592340434568_0.5158844372431384", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "why-is-node-running", "version": "2.2.1", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "f17921d473b15283cde2acf8d5ca9d2f9d9f05cc", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.2.1.tgz", "fileCount": 8, "integrity": "sha512-8lT29hvpJh5tdVQfyUKB1FWuRuqms2ucPD+mt64ofv48j0CgMFhkQZxlWcEefNfr/8pJNqbH/gTB8z7jkyngUA==", "signatures": [{"sig": "MEYCIQCH7XPfhHM6AogLV2JTVCw8dNPaTIheAGmYWdeCBQY4rQIhAIfYlqCTCQ+Qfu61bcFNPIUg4j8jkgCeo+zLxVneLI6Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8rqcCRA9TVsSAnZWagAAakQP/jNE+qEFn+wOEFtKqOjQ\nSDlTAAzZtFRAwEM960Ng1kkLNQ6YueZEUCdX3CVdJ0Wl262fyQq/Fx536nvC\n+H6dcpyXQGPYus3J2nG7GHYqf8my2yHxxiTFVAbq+JYbTpxJm7xK0/0DgGnX\nBqf9BhSsOqWNOKVLR6lfLxxJTQFlOqL9+BZZtN8BDFhEy5GsyOlIqv62E4yi\ni8Dn6rzhAuknMd3VHByzu4VX+SsdyPTKQxuFiSdt9lnfxw1BhYCKl/HrSoX7\nOERJevPF7wEyOTtQKhp5BC8rnXpV0kHXfFdQwsZk4lBZeQ3OW/UaHNCDVwRy\n2s19siTjPov4g+QVbsHcnw6JuuT8jjlsmofWMwKUxzKjxHGo1IEREQe0yvqw\nihICA5DyTcjysktkD4hj0Lxw+KxnH068fo4J5K5YHR0iIhpstXLgP6qcbZWO\nkB7uOyoj1Vnf0CW1rHmCSxmldIR5A0pfxaDHlpnWCawjiXKcnJj/1fUA2Taj\nhK2KlEF5UrOCQ6QXKK/fzKMe1DFM8y5OUwW2ei/iT9poqeuU+Zz0Sum3mCys\nVatSh86OjSEKeWROWbvenZS881ojTnCZY9MZPyMw86K1gvNsVLsKu0VcFLpW\nTnA4cXGSN6898dlcY28yBRlV37JTxNztq0OuQnsAesbzVcfx7zmhjwmb8tXq\nGjm9\r\n=uS8u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "24fb4c878753390a05d00959e6173d0d3c31fddd", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.2.1_1643297436054_0.8712901023635271", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "why-is-node-running", "version": "2.2.2", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.2.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "4185b2b4699117819e7154594271e7e344c9973e", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.2.2.tgz", "fileCount": 8, "integrity": "sha512-6tSwToZxTOcotxHeA+qGCq1mVzKR3CwcJGmVcY+QE8SHy6TnpFnh8PAvPNHYr7EcuVeG0QSMxtYCuO1ta/G/oA==", "signatures": [{"sig": "MEQCIFEOnZMP/3cKpA4pthcQJR7QAF7fhZkEL9WwUOkIAGW7AiBAby8JyHi+D/IBMGY56QgRmH+4f1BNz6amS1K91VD99g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ/9aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHGg/7BBuguKm0+Emj65OfzU/MLNP1heY/BpHoZK2I5yz5T+W5dzZF\r\nJ1Gd38EL4nLdPZZRc3tHVBQAAWeXs2ur6xcEqCzXoZkoDoQWw5vmYTXiWNEH\r\nN7teGxp2ILRR8q/npjUgw9mJvYOBBWMk36bCDXx69fv/zyvk4faWGWXyytng\r\nH2xpzdAUqWoOfbUSZfrFlo8hLaNwyDQS25ytPwNKS1jtWK1beFazRFqNlIrl\r\nppXcGTDNHFf0zsNi2RXbjOC7PznKPBEifcspymBzwFVXwW3An47nBUgoRibc\r\ndBpE9mF3bQkdtgPDzQxrM8/i9ApHFXLbtfzu4OUAwRQRbL4emIiGcx65BM+u\r\nOVFM8/yTSnOLCtL/NBCy7aEyDERJm9rmVaCTGtMqVCBaZrXmH4gbWCy/T5mv\r\nxVjRW21dDSiWslIpAhyOGYXc+/MGrEH2I7zBDDx+onOfeUaTAptLt0eRXAuB\r\nRMtSYdMwnMLOmElF+Jw5gANF57AK7S70IF5AVxxm2Gbv5tnIWuULBsIqxR5J\r\nhSnq37x0HctTBFu0AugyYke0pJNh7qi5j+nRvQ6faBF+ghKVPDp+otICuCJI\r\nI2uVWNWETEh4uoWixqB8s8NW0GEonpsTBvfSVP+wAHVHLjmGfVhcgSRqGA17\r\nlsFSONeiPIwB2b7+x7OTcKzmu1+K/sc0EF0=\r\n=9GeJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "c667c181ee646582dcb5ef34600037d33a535e31", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.2.2_1650982746612_0.22877513861694787", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "why-is-node-running", "version": "2.3.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@2.3.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "a3f69a97107f494b3cdc3bdddd883a7d65cebf04", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.3.0.tgz", "fileCount": 8, "integrity": "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==", "signatures": [{"sig": "MEUCIQCmXkHBlr2EaYbr5TecVbZwcvzWMAPBQYYZLY47dMs0fwIgdHCXSE696PkpcshWS6pCbDvHkuv3bCLVomrhVX0szS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7296}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "93efc6ac4cbe33bd1dfa638e33019abc71190dd1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_2.3.0_1720443443823_0.770060472021302", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "why-is-node-running", "version": "3.0.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@3.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "c054d86b75fbd2da53371b089c5b1f2913211c41", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-2yG/VDi2bC76rjyeBEV5oBL6XZF1G2C9lvR9JluAV3+gFlHj72KzN93SREMu+6MjS6h0zwc+djjVa0DgwEQq5w==", "signatures": [{"sig": "MEUCIEkgw8l5/Ax/pBQ4ZYsVUlhwVdNuRd+jwUPAd09qpQLDAiEAmzycf3Sr+zAXEnNumUQPRL9yei8E8rHZxy42imH8ZgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7678}, "type": "module", "engines": {"node": ">=20.11"}, "exports": {".": "./index.js", "./includes": "./include.js"}, "gitHead": "273b0fdfd1dba8fd2ecde7126da57aa9f784da9e", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_3.0.0_1720443549456_0.028865027599455084", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "why-is-node-running", "version": "3.1.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@3.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkoops", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "2cad5af5baf822f4140946aa39ec37991fb614c0", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-3.1.0.tgz", "fileCount": 10, "integrity": "sha512-RBngGQEebpETZZu3FOcxpcoOWAE5mwwEk/0RTMC4rUYDEECwGabdNWvUILlQu1sS4ThQ5VAd4glBsjvvwu9r2A==", "signatures": [{"sig": "MEQCIH0q5KwBx7R3WwCKh+x0Zu9StH6VZZ+lu/9mlkORCmKcAiAs3fhOUt6VBWYYKFi7MJbdRcFMN2Exjv2xQO+VcXPReQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9154}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=20.11"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./include": "./include.js"}, "gitHead": "eb7a1aad222cc4906fbbec5d85b0065258b8c1c3", "_npmUser": {"name": "jkoops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_3.1.0_1720544899566_0.712765182072236", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "why-is-node-running", "version": "3.2.0", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@3.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkoops", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "8dee22b5074375f53ce43667f310183a2d392d14", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-3.2.0.tgz", "fileCount": 7, "integrity": "sha512-euOPfZRxdZBcD/vL0sH+NtuC/nDpdsZ/aeoq9w5sOcQtfkmhztf/JS76IiD0CJEULlSAPSMy/ZLejex5MsFCcw==", "signatures": [{"sig": "MEYCIQD50OuQMZc6mkGbRG8afNRy4nv7F1jS/CGxr7jcsB5U2gIhAO0c+WwqkkCVqIZAwR/B/i+skdUyXW+zDw1Xxg/iMGW6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/why-is-node-running@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8496}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=20.11"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./include": "./include.js"}, "gitHead": "37b48fbc9f572ffae1d3df130db69ff4e48e8f70", "_npmUser": {"name": "jkoops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_3.2.0_1720804953522_0.9436123524615563", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "why-is-node-running", "version": "3.2.1", "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "why-is-node-running@3.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkoops", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/why-is-node-running", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "bin": {"why-is-node-running": "cli.js"}, "dist": {"shasum": "f66f0d61f861e4d3965a7ad63645a953f550830c", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-3.2.1.tgz", "fileCount": 7, "integrity": "sha512-Tb2FUhB4vUsGQlfSquQLYkApkuPAFQXGFzxWKHHumVz2dK+X1RUm/HnID4+TfIGYJ1kTcwOaCk/buYCEJr6YjQ==", "signatures": [{"sig": "MEUCIQCucuAsjXEuisOncp6M8GGLfLAVs+3ESNFkW9S2diz9zgIgR+Icfb6ioukEdJ3hV6O95OqEhodevNTr01F0xGuDxg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/why-is-node-running@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 8564}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=20.11"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./include": "./include.js"}, "gitHead": "35e08215151fc8254d8e1397e25beece4907dfb7", "_npmUser": {"name": "jkoops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/why-is-node-running.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "directories": {}, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/why-is-node-running_3.2.1_1730221728181_0.12992851064332278", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "why-is-node-running", "type": "module", "version": "3.2.2", "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./include": "./include.js"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=20.11"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/why-is-node-running.git"}, "keywords": ["debug", "devops", "test", "events", "handles"], "author": {"name": "<PERSON>", "url": "@mafintosh"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "homepage": "https://github.com/mafintosh/why-is-node-running", "_id": "why-is-node-running@3.2.2", "gitHead": "6de8dfd78aaa548fcef1dd0cdc5b6c04f37e6d70", "types": "./index.d.ts", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-NKUzAelcoCXhXL4dJzKIwXeR8iEVqsA0Lq6Vnd0UXvgaKbzVo4ZTHROF2Jidrv+SgxOQ03fMinnNhzZATxOD3A==", "shasum": "4c563f5068c5960167220f1b7102ae501cefefb9", "tarball": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-3.2.2.tgz", "fileCount": 7, "unpackedSize": 8851, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgO5mUOWrZ71ZNbOVNNDbv0wZ6h11dh2ErkG75CcT0+wIhAIxKvcaImeFCxkDeOmQPTf0wD9VqIC2I+wrdvhZ5Gx9/"}]}, "_npmUser": {"name": "jkoops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkoops", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/why-is-node-running_3.2.2_1736348547791_0.6937311868525562"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-02-09T15:29:46.299Z", "modified": "2025-01-08T15:02:28.146Z", "1.0.0": "2016-02-09T15:29:46.299Z", "1.1.0": "2016-02-09T15:58:20.182Z", "1.1.1": "2016-02-11T12:00:56.271Z", "1.1.2": "2016-02-13T00:39:09.556Z", "1.2.0": "2016-02-13T12:08:30.993Z", "1.2.1": "2016-02-16T10:25:17.075Z", "1.2.2": "2016-03-24T21:31:28.938Z", "1.2.3": "2017-10-18T12:32:28.549Z", "2.0.0": "2018-02-14T16:35:32.932Z", "2.0.1": "2018-03-01T12:46:14.723Z", "2.0.2": "2018-03-05T12:05:54.691Z", "2.0.3": "2018-08-30T20:50:06.973Z", "2.1.0": "2019-04-03T14:45:31.043Z", "2.1.1": "2020-03-06T20:48:10.059Z", "2.1.2": "2020-03-07T13:26:25.421Z", "2.2.0": "2020-06-16T20:47:14.706Z", "2.2.1": "2022-01-27T15:30:36.186Z", "2.2.2": "2022-04-26T14:19:06.804Z", "2.3.0": "2024-07-08T12:57:23.951Z", "3.0.0": "2024-07-08T12:59:09.598Z", "3.1.0": "2024-07-09T17:08:19.984Z", "3.2.0": "2024-07-12T17:22:33.688Z", "3.2.1": "2024-10-29T17:08:48.344Z", "3.2.2": "2025-01-08T15:02:27.953Z"}, "bugs": {"url": "https://github.com/mafintosh/why-is-node-running/issues"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "homepage": "https://github.com/mafintosh/why-is-node-running", "keywords": ["debug", "devops", "test", "events", "handles"], "repository": {"type": "git", "url": "git+https://github.com/mafintosh/why-is-node-running.git"}, "description": "<PERSON><PERSON> is running but you don't know why? why-is-node-running is here to help you.", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkoops", "email": "<EMAIL>"}], "readme": "# why-is-node-running\n\nNode.js is running but you don't know why? `why-is-node-running` is here to help you.\n\n## Installation\n\nIf you want to use `why-is-node-running` in your code, you can install it as a local dependency of your project. If you want to use it as a CLI, you can install it globally, or use `npx` to run it without installing it.\n\n### As a local dependency\n\nNode.js 20.11 and above (ECMAScript modules):\n\n```bash\nnpm install --save-dev why-is-node-running\n```\n\nNode.js 8 or higher (CommonJS):\n\n```bash\nnpm install --save-dev why-is-node-running@v2.x\n```\n\n### As a global package\n\n```bash\nnpm install --global why-is-node-running\nwhy-is-node-running /path/to/some/file.js\n```\n\nAlternatively if you do not want to install the package globally, you can run it with [`npx`](https://docs.npmjs.com/cli/commands/npx):\n\n```bash\nnpx why-is-node-running /path/to/some/file.js\n```\n\n## Usage (as a dependency)\n\n```js\nimport whyIsNodeRunning from 'why-is-node-running' // should be your first import\nimport { createServer } from 'node:net'\n\nfunction startServer () {\n  const server = createServer()\n  setInterval(() => {}, 1000)\n  server.listen(0)\n}\n\nstartServer()\nstartServer()\n\n// logs out active handles that are keeping node running\nsetImmediate(() => whyIsNodeRunning())\n```\n\nSave the file as `example.js`, then execute:\n\n```bash\nnode ./example.js\n```\n\nHere's the output:\n\n```\nThere are 4 handle(s) keeping the process running\n\n# Timeout\nexample.js:6  - setInterval(() => {}, 1000)\nexample.js:10 - startServer()\n\n# TCPSERVERWRAP\nexample.js:7  - server.listen(0)\nexample.js:10 - startServer()\n\n# Timeout\nexample.js:6  - setInterval(() => {}, 1000)\nexample.js:11 - startServer()\n\n# TCPSERVERWRAP\nexample.js:7  - server.listen(0)\nexample.js:11 - startServer()\n```\n\n## Usage (as a CLI)\n\nYou can run `why-is-node-running` as a standalone if you don't want to include it inside your code. Sending `SIGUSR1`/`SIGINFO` signal to the process will produce the log. (`Ctrl + T` on macOS and BSD systems)\n\n```bash\nwhy-is-node-running /path/to/some/file.js\n```\n\n```\nprobing module /path/to/some/file.js\nkill -SIGUSR1 31115 for logging\n```\n\nTo trigger the log:\n\n```\nkill -SIGUSR1 31115\n```\n\n## Usage (with Node.js' `--import` option)\n\nYou can also use Node's [`--import`](https://nodejs.org/api/cli.html#--importmodule) option to preload `why-is-node-running`:\n\n```bash\nnode --import why-is-node-running/include /path/to/some/file.js\n```\n\nThe steps are otherwise the same as the above CLI section\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "users": {"mkrufky": true, "erikvold": true, "jsumners": true, "wmhilton": true, "boneskull": true, "brave_cgx": true, "fleischer": true, "craigpatten": true, "scottfreecode": true, "oliversalzburg": true, "shanewholloway": true}}