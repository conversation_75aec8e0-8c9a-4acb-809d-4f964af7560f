{"_id": "strip-final-newline", "_rev": "3-77ae76650f8fefb659a2ac3a4b8e808e", "name": "strip-final-newline", "dist-tags": {"latest": "4.0.0"}, "versions": {"2.0.0": {"name": "strip-final-newline", "version": "2.0.0", "description": "Strip the final newline character from a string/buffer", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-final-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["strip", "trim", "remove", "delete", "final", "last", "end", "file", "newline", "linebreak", "character", "string", "buffer"], "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "gitHead": "51c93d6c8683d9fd1cacdb32f2afebac9a1155a1", "bugs": {"url": "https://github.com/sindresorhus/strip-final-newline/issues"}, "homepage": "https://github.com/sindresorhus/strip-final-newline#readme", "_id": "strip-final-newline@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==", "shasum": "89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad", "tarball": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "fileCount": 4, "unpackedSize": 3046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1XUbCRA9TVsSAnZWagAADe4QAIKfZKKn8CFHig0M8lfl\nZWkRAV+2dJAbhILvIrrCYcIZC3A8ICj5GwY5ujeKev8hqkxDe+VfRQRylDTn\nUXCLYMbpV+197qpQb7X6Wdu8ISfIE/zQpBqbGReK/ddJKFHQaIIRG0VWSqWm\nP6R0ICgQPAbMBZ8nYjBjRNvgiseqMwlyxXj1hNKuAR404dGDVIhSzwmg4vku\nmp4FOCDptmFvA/mSzOE/Ochwcdz2wQd6PLtHmCIHzLEvl2z8J4Qaagxwi8yn\neZtQs3B8xtNCKaXOZQYcOMAqZxC4dMGzIEqW3CX9Me9y2p1lHbws55uvNVcX\nHh88Y6/05jr6L6N19ZULpPv9bb5FUcHAOHZFvLhhAl9eQ2WxkZV0IYub5QT7\ngHfcTadB8Mqmw0y58qDwomMLcOXVTwhQYi22OnjTg8pIRq+bfHJzOSgnK7Aa\n464iiikCjO7hG9OfPlpiWdibmFzj8WEsEkzMs6Zeb3uMw6fS8UXTkYLQPzoc\nEtAtOx6PtyKg5Ajl4xlIt4cA24hzsnJs6lJOD89Pd+VIJwihKltOgvOtPN3q\nQ4d2o7S/Ea+e4wIPs6ls4ZWUs5vB7TRQWfrOAeANGxuUSWT+7foTMFBSpOXe\nvxVGokKs1tlzCcbGvew1ZIe+b7RcDbSBr1RwZEo9U41ETveKELS33/Y7oeeL\n2j7u\r\n=W7iG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGUXZVFTxzUEpaEA+kyMDOgCbSmNMZnjYfafIwLWnwN6AiEA+mTwVnuB99UwLZ8AhO9YiC+QgZ5iqZjZEYAk2thqntE="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-final-newline_2.0.0_1540715802035_0.22560465362477622"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "strip-final-newline", "version": "3.0.0", "description": "Strip the final newline character from a string/buffer", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-final-newline.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava"}, "keywords": ["strip", "trim", "remove", "delete", "final", "last", "end", "file", "newline", "linebreak", "character", "string", "buffer"], "devDependencies": {"ava": "^3.15.0", "xo": "^0.39.1"}, "gitHead": "5383387eec921ba4e201879dd03d278c2a43c2f1", "bugs": {"url": "https://github.com/sindresorhus/strip-final-newline/issues"}, "homepage": "https://github.com/sindresorhus/strip-final-newline#readme", "_id": "strip-final-newline@3.0.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==", "shasum": "52894c313fbff318835280aed60ff71ebf12b8fd", "tarball": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz", "fileCount": 4, "unpackedSize": 3361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/G/CRA9TVsSAnZWagAAyLwP/3rltNytAactYmxVGMvn\nnNSbUoRycXhaFeXaqSjUmtKBusZVqg3Jv0mNBkNqGSza+DHCpkP41OV67CLG\nhwVC6wvCuBx7W/3yaSC5ieDIhmXSpzsrsDpggSxvIup7lqRbF0drzWYZwdbq\nlLvYcR5TlIOcQZgv1pdsMSUFsgrHooDLvZr/6Bnui/NwvyxsgwEl0mhZdsNH\nsytwr4dMTTYsoL2HZfKPtqSGSj43L8vVuDoYAAYkrMPzJemJaHVucZMjTHZG\nFP7Nc7XqLWf2wdtlRmMjChK7yfzyqyR3WnX2C0cOv3tbK2oOvZJSFi6bLiB1\nsPjc9X4FmCfrlL6Vnp0mJsBTrAOaeca0zh3A6JZY26A/DxwCYLzMpZOiRij/\nWFA4sX+4ChJ+lXupqCZBdHgkiV6tC6MoNMp7JOeRR5UGCWzFiYPPFmDBZ7/S\nAF0ygBstEBXI1vyfiKNHB5DFkr1dzcZyuzdvzcmqzxTgx1FOmg8ZLIbmkP+9\nCSC6xqDtCoLMlKTcxFPaBvMU1IQh+oQCMvN625w4gqtG6Uyyvk1xdRUMJwTC\nQ4/KMrtZjk/xzs+QCwXQgvZfBuMRYg/QaWCAHfXFfX+Owx/8O5S/XdEJOf4e\nQOfZ7LvhcA0BnJjPvZFYThYBLb0rMAvIFjDeFGiZOwP+tIIGsnfEVZDEDe9q\n257+\r\n=x2bA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEWSCbDmIn+4gQ832wAA45dVA7JZPDoZ+qkRD+78rRWGAiEA3dhajcK1Ij2hwcvBEgTQjcur+OzqUO6C/MTBbgt3baQ="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-final-newline_3.0.0_1620046271483_0.9109171270738188"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "strip-final-newline", "version": "4.0.0", "description": "Strip the final newline character from a string or Uint8Array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-final-newline.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "trim", "remove", "delete", "final", "last", "end", "file", "newline", "linebreak", "character", "string", "uint8array"], "devDependencies": {"ava": "^6.0.1", "tsd": "^0.29.0", "xo": "^0.56.0"}, "types": "./index.d.ts", "gitHead": "a20b4f27fdbc96d7418a723269905340e3605276", "bugs": {"url": "https://github.com/sindresorhus/strip-final-newline/issues"}, "homepage": "https://github.com/sindresorhus/strip-final-newline#readme", "_id": "strip-final-newline@4.0.0", "_nodeVersion": "18.19.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-aulFJcD6YK8V1G7iRB5tigAP4TsHBZZrOV8pjV++zdUwmeV8uzbY7yn6h9MswN62adStNZFuCIx4haBnRuMDaw==", "shasum": "35a369ec2ac43df356e3edd5dcebb6429aa1fa5c", "tarball": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-4.0.0.tgz", "fileCount": 5, "unpackedSize": 4382, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcCxexozx8d+HGfQZEDtg6J5ok+bntRYwLpBLrrVfnVwIhANci9SZ3s28qKg+v3yk/KnRTiYJfAqtLz0lBezf6KOWI"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-final-newline_4.0.0_1702495910048_0.21989528729359664"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-10-28T08:36:42.035Z", "2.0.0": "2018-10-28T08:36:42.180Z", "modified": "2023-12-13T19:31:50.456Z", "3.0.0": "2021-05-03T12:51:11.608Z", "4.0.0": "2023-12-13T19:31:50.243Z"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "description": "Strip the final newline character from a string or Uint8Array", "homepage": "https://github.com/sindresorhus/strip-final-newline#readme", "keywords": ["strip", "trim", "remove", "delete", "final", "last", "end", "file", "newline", "linebreak", "character", "string", "uint8array"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-final-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-final-newline/issues"}, "license": "MIT", "readme": "# strip-final-newline\n\n> Strip the final [newline character](https://en.wikipedia.org/wiki/Newline) from a string or Uint8Array.\n\nThis can be useful when parsing the output of, for example, `ChildProcess#execFile()`, as [binaries usually output a newline at the end](https://stackoverflow.com/questions/729692/why-should-text-files-end-with-a-newline). You cannot use `stdout.trimEnd()` for this as it removes all trailing newlines and whitespaces at the end.\n\n## Install\n\n```sh\nnpm install strip-final-newline\n```\n\n## Usage\n\n```js\nimport stripFinalNewline from 'strip-final-newline';\n\nstripFinalNewline('foo\\nbar\\n\\n');\n//=> 'foo\\nbar\\n'\n\nconst uint8Array = new TextEncoder().encode('foo\\nbar\\n\\n')\nnew TextDecoder().decode(stripFinalNewline(uint8Array));\n//=> 'foo\\nbar\\n'\n```\n\n## Performance\n\nWhen using an `Uint8Array`, the original value is referenced, not copied. This is much more efficient, requires almost no memory, and remains milliseconds fast even on very large inputs.\n\nIf you'd like to ensure that modifying the return value does not also modify the value passed as input, please use `.slice()`.\n\n```js\nconst value = new TextDecoder().decode(stripFinalNewline(uint8Array).slice());\n```\n", "readmeFilename": "readme.md"}