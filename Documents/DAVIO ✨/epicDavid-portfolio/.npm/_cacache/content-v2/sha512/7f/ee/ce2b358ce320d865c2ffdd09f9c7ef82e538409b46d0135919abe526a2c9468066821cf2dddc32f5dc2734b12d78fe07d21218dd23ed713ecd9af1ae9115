{"_id": "stackback", "_rev": "9-2ccebb3f954d6828796f3c69914ae7a9", "name": "stackback", "description": "return list of CallSite objects from a captured stacktrace", "dist-tags": {"latest": "0.0.2"}, "versions": {"0.0.1": {"name": "stackback", "version": "0.0.1", "description": "return list of CallSite objects from a captured stacktrace", "main": "index.js", "scripts": {"test": "mocha --ui qunit"}, "repository": {"type": "git", "url": "git://github.com/shtylman/node-stackback.git"}, "keywords": ["stacktrace", "trace", "stack"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stackback@0.0.1", "dist": {"shasum": "26c40dca3b4af5f3e5a8b493e0af34fdebe324fc", "tarball": "https://registry.npmjs.org/stackback/-/stackback-0.0.1.tgz", "integrity": "sha512-p32ZnMo0yQ9L4eq9lVuIMPGFCpQb24MixIMnl89SjFKUWMKZpheQeAUiHCsPPB4ndzlJ8eNyB21cSwqbdUtjsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHw+FmSn2JKUMo8zkmLQNZx++7q5IxsMVdOfd6hu5P1gIgemTF8D0xrL2t9SgBhXdcnz8Z53/25PN2Qxfv/eKCGiI="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.2": {"name": "stackback", "version": "0.0.2", "description": "return list of CallSite objects from a captured stacktrace", "main": "index.js", "scripts": {"test": "mocha --ui qunit"}, "repository": {"type": "git", "url": "git://github.com/shtylman/node-stackback.git"}, "keywords": ["stacktrace", "trace", "stack"], "devDependencies": {"mocha": "~1.6.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "stackback@0.0.2", "dist": {"shasum": "1ac8a0d9483848d1695e418b6d031a3c3ce68e3b", "tarball": "https://registry.npmjs.org/stackback/-/stackback-0.0.2.tgz", "integrity": "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGpxTiylRzQ9v4nPDVhyOPCrXyWFmTk3O40AgsMDU50AIgfRIy26auFC/u3QJ/zUlDJdxnk0vgwPSZqaLO0TPKa9s="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}]}}, "readme": "# stackback\n\nReturns an array of CallSite objects for a captured stacktrace. Useful if you want to access the frame for an error object.\n\n## use\n\n```javascript\nvar stackback = require('stackback');\n\n// error generated from somewhere\nvar err = new Error('some sample error');\n\n// stack is an array of CallSite objects\nvar stack = stackback(err);\n```\n\n## CallSite object\n\nFrom the [V8 StackTrace API](https://code.google.com/p/v8/wiki/JavaScriptStackTraceApi)\n\nThe structured stack trace is an Array of CallSite objects, each of which represents a stack frame. A CallSite object defines the following methods\n\ngetThis: returns the value of this  \ngetTypeName: returns the type of this as a string. This is the name of the function stored in the constructor field of this, if available, otherwise the object's [[Class]] internal property.  \ngetFunction: returns the current function  \ngetFunctionName: returns the name of the current function, typically its name property. If a name property is not available an attempt will be made to try to infer a name from the function's context.  \ngetMethodName: returns the name of the property of this or one of its prototypes that holds the current function  \ngetFileName: if this function was defined in a script returns the name of the script  \ngetLineNumber: if this function was defined in a script returns the current line number  \ngetColumnNumber: if this function was defined in a script returns the current column number  \ngetEvalOrigin: if this function was created using a call to eval returns a CallSite object representing the location where eval was called  \nisToplevel: is this a toplevel invocation, that is, is this the global object?  \nisEval: does this call take place in code defined by a call to eval?  \nisNative: is this call in native V8 code?  \nisConstructor: is this a constructor call?  \n\n## install\n\n```shell\nnpm install stackback\n```\n", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T23:39:38.250Z", "created": "2012-10-08T18:08:07.417Z", "0.0.1": "2012-10-08T18:08:07.982Z", "0.0.2": "2012-10-20T00:56:54.591Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/shtylman/node-stackback.git"}, "keywords": ["stacktrace", "trace", "stack"], "license": "MIT", "readmeFilename": ""}