{"_id": "string-length", "_rev": "24-512dcabd64b3336bbbff3e609c3ccf97", "name": "string-length", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "dist-tags": {"latest": "6.0.0"}, "versions": {"0.1.0": {"name": "string-length", "version": "0.1.0", "description": "Get the real length of a string by correctly counting astral symbols", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/string-length"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "browserify -s $npm_package_name -o browser.js ."}, "files": ["index.js"], "keywords": ["unicode", "string", "str", "length", "astral", "symbol", "surrogates", "codepoints"], "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length", "_id": "string-length@0.1.0", "dist": {"shasum": "ecedc88d6f5393cc7d13a4439b111cf60fb56ad1", "tarball": "https://registry.npmjs.org/string-length/-/string-length-0.1.0.tgz", "integrity": "sha512-SCvIFxrMAQ2XZ78miYE4QToDW/hdNUxxWxMXejH79uHlXpubFkDFJyzFuhBc5WXan4vlM1IF9jNApD/Ay/ldFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSPisTac1SaA/1Ne7O27yZJPteZQqoJtXXh+cuTUsMygIgVOpCMzBMVydzm/axOMt7qjfYk7JgrCmYIl6g2zOisc0="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "string-length", "version": "0.1.1", "description": "Get the real length of a string by correctly counting astral symbols", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/string-length"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "browserify -s $npm_package_name -o browser.js ."}, "files": ["index.js"], "keywords": ["unicode", "string", "str", "length", "astral", "symbol", "surrogates", "codepoints"], "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length", "_id": "string-length@0.1.1", "dist": {"shasum": "ab77870cddfd4a41deab673e5f6b03b92f3d4172", "tarball": "https://registry.npmjs.org/string-length/-/string-length-0.1.1.tgz", "integrity": "sha512-aVazvnjmWtbpwhN9OVLAMvH2s2LYw14jyQoeoFAx6V08Lv0hiBzMBLWeH9QsySrAdz07QEu94tKOv2Pw2T4EDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDp6yH2+/vw5FmyrkqXfyAS3W3Q81NmHvyZuQFX518T8AiEAkB1Wwwxpii8eYumBeE+XKXTUfVw9Rwt/EearusJo8xc="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "string-length", "version": "0.1.2", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/string-length"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "browserify -s $npm_package_name -o browser.js ."}, "files": ["index.js"], "keywords": ["unicode", "string", "str", "length", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"strip-ansi": "^0.2.1"}, "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length", "_id": "string-length@0.1.2", "dist": {"shasum": "ab04bb33867ee74beed7fb89bb7f089d392780f2", "tarball": "https://registry.npmjs.org/string-length/-/string-length-0.1.2.tgz", "integrity": "sha512-Z7ZCUHl1mkGe1v4rybhTTVm0LSWeeX1hJ4pVsX0jcYNCwMJsLByDfVV3PUJ5kyC/ZigzPbUP+nEBNCbZza5VyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYinRCa9e+1nz5pu55PDvb0l1Ld29aTJEc927WfrYDKQIgCdp7slrngdDLrcuyJ8WWZb3AL0rkIAo5nTH1aXL9UVk="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "string-length", "version": "1.0.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/string-length"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["unicode", "string", "str", "length", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"strip-ansi": "^2.0.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "5aa9b5946c927c2884a49741a066fdb3f30a6aa1", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length", "_id": "string-length@1.0.0", "_shasum": "5f0564b174feb299595a763da71513266370d3a9", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5f0564b174feb299595a763da71513266370d3a9", "tarball": "https://registry.npmjs.org/string-length/-/string-length-1.0.0.tgz", "integrity": "sha512-Ix7an0W1kNzF/vf6U5y0dNBJ0eFe8OIz4DcgDVOAKX/hLgU9I7//dMhuS9AvxZP4p5Sp6adP68IKUQIeGe2zdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/ge6MEurzCRjdyeklvv/UV+duRqHJvZUm4VzhuWA33AiA3MSYPA7MiiuP1tclXX8GuGOysB6mL5/ttxvYgNzNBtw=="}]}, "directories": {}}, "1.0.1": {"name": "string-length", "version": "1.0.1", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/string-length"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["unicode", "string", "str", "length", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"strip-ansi": "^3.0.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "18c47a338a5f7b8df45566579ff588ba7a82ac6e", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length", "_id": "string-length@1.0.1", "_shasum": "56970fb1c38558e9e70b728bf3de269ac45adfac", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "56970fb1c38558e9e70b728bf3de269ac45adfac", "tarball": "https://registry.npmjs.org/string-length/-/string-length-1.0.1.tgz", "integrity": "sha512-MNCACnufWUf3pQ57O5WTBMkKhzYIaKEcUioO0XHrTMafrbBaNk4IyDOLHBv5xbXO0jLLdsYWeFjpjG2hVHRDtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICmb/FY97ew+1D7vLwYughyc8Xn0d/XzJwR08H4hwl3IAiAf66OMTbJPLrgvzfMV1f+s/2RX4KCn+r6Svp+90ANM3g=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "string-length", "version": "2.0.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"astral-regex": "^1.0.0", "strip-ansi": "^4.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "dcdf2d91dcb155923ece3b8f77f2279e49bb1d3f", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@2.0.0", "_shasum": "d40dbb686a3ace960c1cffca562bf2c45f8363ed", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d40dbb686a3ace960c1cffca562bf2c45f8363ed", "tarball": "https://registry.npmjs.org/string-length/-/string-length-2.0.0.tgz", "integrity": "sha512-Qka42GGrS8Mm3SZ+7cH8UXiIWI867/b/Z/feQSpQx/rbfB8UGknGEZVaUQMOUVj+soY6NpWAxily63HI1OckVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ50xiVzXofoZUDit69ZBX06aW2SiZlbYFkuETHM2OvwIhAL671lsZsFR39goHYgJhDYG6KzzwYjuybHqfG+mlTWq1"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length-2.0.0.tgz_1501117430278_0.7992605706676841"}, "directories": {}}, "3.0.0": {"name": "string-length", "version": "3.0.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"astral-regex": "^1.0.0", "strip-ansi": "^5.1.0"}, "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "c6c96299320f9b2707de2ac858c2210298219826", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VeZa3u8MpEpDi7NMsZfp6ggk2x6ncBdE+sObPEVlczaUciCc+3ktm46P3HU2KdXArigN4XeEFNaj1Sg9TYAznw==", "shasum": "74ae8d207c716322d4a8fe79736170d3b5a4ed94", "tarball": "https://registry.npmjs.org/string-length/-/string-length-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj1NaCRA9TVsSAnZWagAAIj8QAICp1SooW059jCwp49DK\nQ2wPRMaIUKGNoAG6dg2/zF42if82Vec6kZDnJaIFXYNDeaAOVhjp+ffamj8M\ncKI1DeK4WbZhlAQfBLE015N+b0OkhLiAoZIgT6RtZDkIyRIAdSA2BAY5MzJ4\nqLFER+sJjWUZJ1rRw1t8tq5u0hNyLUlpuzo22Fq/vTt2dNuCYpWVc83XytZj\nOK4Q6gYEwqYtmDZR3k7LVPJiMW0Ey3Cb1av2DAMCDPoxmvhdM/2qWuxmhhO0\nOppmDG7uM5UJwW+YO4RwYRhy/fBrY+AqpkAbT8csfObjYxHi3hdwmvqNV7aD\nuy1GCTSo73RzDTRd43j9kyC7oO2mJWLgGhyJOqXgvVKv1iCkDFMxUPa5Jljf\nU2UkKA91wP+TNsxdgESYUgQidiW5XIiPhXgx1EfixL51pz2lzTKOBNozNkY5\nQybqrsnvuxoMc/u+t2US1ZD2S/zKNGIhOM7Tj3eE4uNy0KLyfQLNa3mLrqUj\nL1PUTwFKhD615M4uKSfu0Q3WbXm37UX/cI9cwUSUb0LSov38QOIPUotomVQR\ns9ZQ9dl3u0E1glI6wz5Qnguru7Ra5Dt1DCRCXypBXf3xM6f66bLCgNe3FD8C\nuJmzpv0Cw7wHWhS3hI/8ThdVhaBwiTPCP7+rnsGZEu7Ho78sA/vEAvi3+nqI\nzO5Y\r\n=LGLh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCS5maBx03R+w3uhDbX8yS3Ea0ZOyVRUVKeg9feNhtt9AIgCIG9FIrNOwEOOrz+1pOnxCQ9yqlNhQERVfOBlPMY2wI="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_3.0.0_1552896857942_0.1703975027346425"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "string-length", "version": "3.1.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"astral-regex": "^1.0.0", "strip-ansi": "^5.2.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "083c3ae16f416b9b51ab5d1a6763ca883ab94806", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@3.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ttp5YvkGm5v9Ijagtaz1BnN+k9ObpvS0eIBblPMp2YWL8FBmi9qblQ9fexc2k/CXFgrTIteU3jAw3payCnwSTA==", "shasum": "107ef8c23456e187a8abd4a61162ff4ac6e25837", "tarball": "https://registry.npmjs.org/string-length/-/string-length-3.1.0.tgz", "fileCount": 5, "unpackedSize": 3886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcosqLCRA9TVsSAnZWagAAfz8P/2VKDarayTFywfgo43OL\nSrJX2coUS2QEtLmVp8N072fmjxFgvbPZ/bkUgVqZqeD4YEl+kl4uUwhRIlA0\nvLc2fZL1oBKQjqMANkdzN/4CoW93Wc3XodoR01KxUk3cetOlmlrMtdV+VWQO\n3wjeH1YsNgYCafmeU12MReWgPCSbac3TCcrKUHOWlWUO4ieKhJidl9iiV8RN\n74alhuCzhVnyjTpfoPKvxyMOC5LOS95UcTws3T9yy2IUgoSVby0g/TqDFuNj\nApWIIlf0tHWZR2tcSB21lEXfdsdZGOqDUPywmgG84f1OCTIb4uD+tzOb1YUU\nP9aaigq6e99dqAOqV27Lvx/2gbsfeVoMc1K041YoAEYWJD0cKffRrwkg8FYs\nAtvMFJ/lrCIe86Hv13M/HAS/mgp16W7FYtgaNIspeW8+8hD/L3th1suFl9qs\nJwylYOUYVDVGWKLvTFatrSk1XvD/DDzIFqUu8r++8ONOeyEFqbDc0VIpHd9Z\norN3tNM2mWWeu+o8HcBe4lkWsXsBpATo8JsBv1XZ7oQIQc/fl49Uw484yWP6\nmNDTzbKBol2IlVdnwEFkyrhjaibBG2iQKO5oQIZuClB5tbKB7Fcy+mQHGErD\nZlI9EJo/nbQu2wosCkez0AjdWFjQuqVZc2M+e7bx0H+PkYWolZZ+Vs2j2Go9\nb18I\r\n=oJfb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCr68ajAWehV4sWjCzfzTJkowflU0EJXVM9VuoSh4rxqgIhANxoemj2WXRw2R38djqeCX+K06S23HbA1Uazl0aBSWd9"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_3.1.0_1554172554425_0.292397781999574"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "string-length", "version": "4.0.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^3.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "ad032d35f027513ee0aa3d80112625133f8b1b25", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@4.0.0", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GWDgJDQvvIwP4e5IEu0tuOyRUs/bVvQNNdh0HhMnZwo0Kuo4NKmjI/as/9I34+GHYWKkuS0nUrHubgRTs4O4fw==", "shasum": "16fda4aa6a5d0e0ca31d9f86ea238c9c8624f69e", "tarball": "https://registry.npmjs.org/string-length/-/string-length-4.0.0.tgz", "fileCount": 5, "unpackedSize": 4050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeT7fwCRA9TVsSAnZWagAAZwsP/3LJb1ofHdZVvcsGdvJ6\nCWaCIKscBP96eRWJblHF4h2zIkCqoDs9UZA25KrZ478K3B6HH+9Y8WMkI60e\nlhrc0nq14fGd6dxOtzXMlokemRVIUzzIjC/iCD76bhF9+fWa1cvb3j/HeF45\nEUNFabhVKuZzyg5a1HPipt7eHVSv6RQv9RWwHFqNsLgOddWhoLL1EZDLKX8+\ntEbtrxGgf6Dbnm+YeEpyB5jph/uprZpqtVkf88LpxZppx8DvCSW5PPzkeevR\noAiuFanyHuM6TeS8kEk0inHNkqFauaobywvgCS0PetZg4eKVzrVQA04Wf6qd\n7upcQfyhtdLOnLQseEC8uuAEnizrXG2u4xPTty+iCR5ioW+FKWr1lIfXm2Lz\nbedA1WqA44qAUpHT5lQstLEfvbIUM7n+moW/bUgBp5Ph2qGblno/twSkYhAz\nyNrjDP2Bf4WbPt2HHRdVz3a5YscPddHWrUZ8gE9j/UewXpH6mq80DVbt7vLu\nwd2sho6vLAHYks0XLs9OPmWoela093J6YtRk60B2BlKizv/wpBSxqdJOtc39\n074Q9m43fy1gq5R08oifJ/zMsL4mG+tAdmcQDAXv6bUAAmBztMhtGhcIc0hv\nrNsVUkyqQ+UrvF3xmgPYvKZ5idXLEGWG3eYyBWWOZwG66hb2rKRS2C7ydxnP\nnTA/\r\n=zvjD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuFRwJbVSW7aFN8M4azPzZFoVkq3OiEofBWFz9JCqQ8wIhALGjYUn11no2JUPbO1v0cG1QzylxyTiio2mIsaMNHp1h"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_4.0.0_1582282735738_0.6335634559747516"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "string-length", "version": "4.0.1", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^3.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "20a1ad355fbbc11c9b3c4194e70c1302596ea17f", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@4.0.1", "_nodeVersion": "10.18.1", "_npmVersion": "6.14.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PKyXUd0LK0ePjSOnWn34V2uD6acUWev9uy0Ft05k0E8xRW+SKcA0F7eMr7h5xlzfn+4O3N+55rduYyet3Jk+jw==", "shasum": "4a973bf31ef77c4edbceadd6af2611996985f8a1", "tarball": "https://registry.npmjs.org/string-length/-/string-length-4.0.1.tgz", "fileCount": 5, "unpackedSize": 4101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJec5/1CRA9TVsSAnZWagAAZs0P/1UwoOmVWj2JriHeofBO\nHHhy8TyFo/HIXNO5Apz8rDzadNnKoTGVmUWD4rBbPEfOqG2jpc75R9lKxS+7\nGwLJDdp8Tl+0RfXWCQDZKXD6Et6sSw7fEoKgwSu5LjdCtDc1MIMJCw/hKIpi\n7etcxvVj4od/Z/aN+xlXCaObIQgRsSHDH02RGzW1gV4MzIa6DlrrzCUOWF6c\nC+9yH6wQv7045WkHOjFrl1SxubSZxJ0L5O7W/f2yyGtrF5efUooCPv+lOdTl\nv6estwxbVxUN8b5yZBxWn8toj9wLAnuUqAxDLbsFAu/leBu7yHTIrPvc2hlP\nZBsso4FxgBLjhZaa5OudNwiItUrQR5dqeyy3ziO5R3pRaIfdnQWFqAXVCorM\nBvhrxkbbkWxL/Tz5n/cRDWxDIo9T9xoNW34xFmDdYCMq/csJrWNjh+phJ7SF\nhR2de534DS7IRRFm9nXtuUkjP4fYm1POx8C/hI5lu+VnP7juaTvf3LuemzSs\ndLfm5O9XOoG4T1FOj/Km/3F9SEayEtZO4LPxfBWfe7uZcnENbVjhZDuSgHPC\nJ6nPLDhxEOP43QvQOMnsN+FefNTnebF12mgFq+3fk9bCQqcEIjN0BLjctnt1\nI/DMR3ljnpIQz8Hl8Dm5ac0HHy2EgA41H2icV6E5eYnDfSuNmytD9OSIhcwv\nq92f\r\n=CwcQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmia7i4vjdsFmRBKjvIV8hB7oAp7oR8kRggcx5KHIe/AIgbxeIwOtG4GaKIpmDQVoF1OwFBwIE3NQkVWs/HAJhlAs="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_4.0.1_1584635892393_0.5498585482578426"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "string-length", "version": "4.0.2", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^3.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "0656975df3a4378731b89f313ec60e5fb59ea088", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@4.0.2", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==", "shasum": "a8a8dc7bd5c1a82b9b3c8b87e125f66871b6e57a", "tarball": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz", "fileCount": 5, "unpackedSize": 4052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUaX3CRA9TVsSAnZWagAAkm4QAIeRssWNoufJj+uO29wW\nUgptdfPYdqP5cCsz1iy09bBWRXsfGAU75d/Z82sZ/16VMK6sIoftALf2B7Am\nzqAo55bQcfJfWxGaBsuwlMWDHE+AEDFDpgpfBWuELgWaMvCm4Ycn8zNBjDin\nZkYTpACQ5UP+YazIezjUlUDOCdumZ3+8OXm0s0L9/0JM8fCk0206jDhgEkRz\ntpojxB3JHke0eFEooxmZ3detEgWtPyxH4lEIHj+4WZNKYIYnV71svSiIDIBZ\nKfyv01Cj6KewHytBjlvXJisaQX+eZy4BFRnMpg8Wrz2vDaomxmUQMonGQyPf\nFpYojj1ZcAPQyQRqe7d/gEQs1xmCShvTClfiUUbSnVwFeTohEvbbOUMwKbxk\nkupaRrfN6zOFXtTnGa6A7bKQ/3rXkXbNFmfvknGQRHP5tYNFfldFvw6TT6JE\nVLPXmkknJwMPTDrFTcSadclEbZjti5r7MkIwJJe6468bKjMNO5gVpQZrBTJ6\nU00D8tdLvAr/lzhDwpS6NIRiAg+rIpqIgvTq7eBpDQ9ky36rs6ZHBJ5ErNqc\nz3Z/2LKidWTvFI+RJAw3QWg7bPN793tgjhMJvZrlU9ta+ZieNs3zzv5gCURc\n9zozdF2rcIbDxUEMmlURtnb2a3rGFcYHe6BwDMo4tazMc4deXoe7wDF6cwMo\nl+M/\r\n=XvqX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvrgjCx/IYD3QJ+S5AUScE1u/9WdaLfKa+/LwWk+Jq8QIhAK4TRGiokyJ6/lYFrt1MNZbCUSl5fxM9ZHK6X1Lnql0r"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_4.0.2_1615963639274_0.20207543649070225"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "string-length", "version": "5.0.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=12.20"}, "type": "module", "exports": "./index.js", "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^2.0.0", "strip-ansi": "^7.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.40.2"}, "gitHead": "00b57061333f0d3f416b3d6e591b626dc5633502", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@5.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-1qjpci3h1G7BUi6dt5uAVyrO2FMUdjj3XWU970/eQh0YKhlYZ8bJMC/+BUpD21YdEhmuHv2nnbUUn024+cC7YQ==", "shasum": "fc6c1de7850d81255d944d10951ae65c7304d228", "tarball": "https://registry.npmjs.org/string-length/-/string-length-5.0.0.tgz", "fileCount": 5, "unpackedSize": 4631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgu4/RCRA9TVsSAnZWagAAeXUP/3zkKX3hm5FFBlirnZMN\nDfd+LDaWiNvjZa9N2/YIiqSqM67aii7A3P5Fy5pl1CSN3I3yGdA60M3sdF5h\nPrYTWuuvs2eIu75jmArwuzek56ZK0vrn5IcUsRsdeAaw2tLYPmrSEm5i0zaZ\nxWk1kH8/9BoJn5WjEeoeTTckD8YPqHwjW/yiVoYBqvuQg4qaDKvVs/ZPyrB8\nEDnIgIFzbdbnmH+T8QTIdFcze+R4AIP3cppKMH0KLEVwzR4ZruMDS1uTBMqB\nU5MyIDgATgJZhZwmFt8kB4m/t4qExjBuxWaYkNmbH3ZhzQIeXOwbh6bDBumL\nMzW1LO+B5GrC/J8EKt9PwuWgVjyG1nWgKYy6MKBUzOTkCIEY5OaeQAafqlKp\nSx5YaKIEmvz/3ImR76aTQOpfop3rBm8j1y4rF5YOgOYpx+Qq8tcOM0ZyJTmy\nS1u+OcirwZZo7i3OPnJkPR5CUzIi2xI2M3GU3fO2JvaHlZZpt6P3G8MH0Q+O\n08JVs/yCu4NwriNc7cJDxeIvPgbN3c+GC+Art3ydTP7aAJnX7YdGG+U7CzRi\n82i5RR031wXkc4bRontREIraVPgS6ofHPdq0YwEERTQhlSeAxzy0WouAV3fo\nV9ZJed3gSfpLifq+HOxiHceDoq4CfSpuMnSRgm942kZXi2of8saWtAKAiYQL\nUiO7\r\n=o1Vf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9d65fsrCABYi33GcM8Z6b/PJnKpvf+aN5cjKoY2tJ1gIhANGLqSwK0dGhT0wrGBOZz0HHjeh2kgRJpxaBQlJWQdxo"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_5.0.0_1622904784877_0.07725316336050447"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "string-length", "version": "5.0.1", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=12.20"}, "type": "module", "exports": "./index.js", "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^2.0.0", "strip-ansi": "^7.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "a1cdfe8c1f2e57832a4bba741bfca292e69d681c", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@5.0.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-9Ep08KAMUn0OadnVaBuRdE2l615CQ508kr0XMadjClfYpdCyvrbFp6Taebo8yyxokQ4viUd/xPPUA4FGgUa0ow==", "shasum": "3d647f497b6e8e8d41e422f7e0b23bc536c8381e", "tarball": "https://registry.npmjs.org/string-length/-/string-length-5.0.1.tgz", "fileCount": 5, "unpackedSize": 4631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP5aDCRA9TVsSAnZWagAAGpcQAI5dwdBnoVHAYdI8XdIB\nZGVp+zhTmGmLGeAepQUTT/1ViwelKNK72CccRkftbotq+PPxvYNBp856mpRS\n52lIaigl0vuY6zjNaByMkWe+2XGfxttff7QibnNRquB7DYFe2M0cUlqTyQTs\n3UVZFOxviTlU6V7O0t2bEBFTIUecFeOlhpzgOKo/4z2EI+1IS2c4CTLbnEXM\neeHKawVRlLd3LC5L0NHbPdcV7cLl42x6qEhZuHGM0uyILai4Tk9lba+zwSb+\ngWZ3fbrFzHYwbtPgPofuD6FtZnadETdd2xD1cNtr5zkBpskSnmHBo7WKA3/C\nuUwiWkC3L0zwm01hm1mqIFLZZ04X7dj315OT97SbOy4GVtwTLrPPWGUGoeWc\nHr7auHztKr+HXo7OfzyfxrKWuf/7PcNja+wkywbPEXEtBSwTZBwQlwTvt/mx\ne6FQUwWIU13bSY31FsP56Ov+fvUxTZgLVvPfb5R3xW0PmdwdMtuB1/zCxYVi\npKu9ndabDZcy+KBKl62xOn2s2SxFfYvOok3oTdC2LqfAqvRfFZ7+uo0sWPls\niwMQC836CSitg1ZTSF530xEmSOxo9xrWOZNeaN4YYyJ7dng5APUUXP4jH2kR\nRCWH0mUKZFmh3rZHMFDXRhLR5iy11dacKedB6qJcjoNbnSTcAL3kvHaU2EWH\nlh6T\r\n=hHCg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2nnTH0tGemCj4d63yZL0GTmkzvQAZMhP3Kpt1z+nB6AIgYNiSVxwH6PYP2e8jihlWhr9FDOSsWqFqJVyTqv1IMQw="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_5.0.1_1631557250714_0.4392555917221974"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "string-length", "version": "6.0.0", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=16"}, "type": "module", "exports": "./index.js", "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"strip-ansi": "^7.1.0"}, "devDependencies": {"ava": "^5.3.0", "tsd": "^0.28.1", "xo": "^0.54.2"}, "types": "./index.d.ts", "gitHead": "116dcae33d878452991e9d8ddafa74b6e2a183eb", "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "_id": "string-length@6.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-1U361pxZHEQ+FeSjzqRpV+cu2vTzYeWeafXFLykiFlv4Vc0n3njgU8HrMbyik5uwm77naWMuVG8fhEF+Ovb1Kg==", "shasum": "1c7342bbf032129b2f80003e69f889c70231d791", "tarball": "https://registry.npmjs.org/string-length/-/string-length-6.0.0.tgz", "fileCount": 5, "unpackedSize": 4252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIMOYvdDfcPitluid39Vfdud/Ne49G1zixbKKuidq+bAiA3i0zmEk4F399LWVA51sCNhb8oxT0NGOFZb+7aiEUt1w=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-length_6.0.0_1685867756118_0.8540791256961788"}, "_hasShrinkwrap": false}}, "readme": "# string-length\n\n> Get the real length of a string - by correctly counting astral symbols and ignoring [ansi escape codes](https://github.com/sindresorhus/strip-ansi)\n\n`String#length` erroneously counts [astral symbols](https://web.archive.org/web/20150721114550/http://www.tlg.uci.edu/~opoudjis/unicode/unicode_astral.html) as two characters.\n\n## Install\n\n```sh\nnpm install string-length\n```\n\n## Usage\n\n```js\nimport stringLength from 'string-length';\n\n'🐴'.length;\n//=> 2\n\nstringLength('🐴');\n//=> 1\n\nstringLength('\\u001B[1municorn\\u001B[22m');\n//=> 7\n```\n\n## API\n\n### stringLength(string, options?)\n\n#### options\n\nType: `object`\n\n##### countAnsiEscapeCodes\n\nType: `boolean`\\\nDefault: `false`\n\nWhether [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) should be counted. They are ignored by default.\n\n## Related\n\n- [string-length-cli](https://github.com/LitoMore/string-length-cli) - CLI for this module\n- [string-width](https://github.com/sindresorhus/string-width) - Get visual width of a string\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:32:37.976Z", "created": "2014-04-16T21:49:45.503Z", "0.1.0": "2014-04-16T21:49:45.503Z", "0.1.1": "2014-04-30T22:51:26.244Z", "0.1.2": "2014-05-01T21:49:01.365Z", "1.0.0": "2014-08-13T13:37:13.130Z", "1.0.1": "2015-07-16T21:16:09.833Z", "2.0.0": "2017-07-27T01:03:51.257Z", "3.0.0": "2019-03-18T08:14:18.073Z", "3.1.0": "2019-04-02T02:35:54.651Z", "4.0.0": "2020-02-21T10:58:55.900Z", "4.0.1": "2020-03-19T16:38:12.544Z", "4.0.2": "2021-03-17T06:47:19.439Z", "5.0.0": "2021-06-05T14:53:05.037Z", "5.0.1": "2021-09-13T18:20:51.093Z", "6.0.0": "2023-06-04T08:35:56.366Z"}, "homepage": "https://github.com/sindresorhus/string-length#readme", "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-length.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/string-length/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"rocket0191": true, "wvlvik": true, "flumpus-dev": true}}