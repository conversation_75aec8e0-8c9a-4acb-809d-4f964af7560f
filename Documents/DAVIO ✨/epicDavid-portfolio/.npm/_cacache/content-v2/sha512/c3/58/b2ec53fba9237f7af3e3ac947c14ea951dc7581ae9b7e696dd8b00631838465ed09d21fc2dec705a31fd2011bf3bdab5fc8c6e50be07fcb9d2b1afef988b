{"_id": "@babel/plugin-transform-block-scoping", "_rev": "155-2588388fb5964bc3a36ef2cec36799ea", "name": "@babel/plugin-transform-block-scoping", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7dd0b9ee8c33c27af81073a72ac62ed769257cc5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.4.tgz", "integrity": "sha512-8xYAKnDcTazivF5NYajyxRObpnrqT3/Gtlu+DUEAUab56oMqcRFQU82uYbo+umZdrXl898w+lz3+UsigkgCGWA==", "signatures": [{"sig": "MEUCIQDeoi+NE0iA9Vbc51E8wqfnIle8Ynjf0MUKtcNpYnV03AIgL08qlrOk0ohNpBQUG9znFeeEpjeVg1D89HDTpAtOsJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.4.tgz_1509388569700_0.9646476812195033", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1fa82d763aec031253554d99e56d8be25a68adf0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.5.tgz", "integrity": "sha512-UfcOmT2gqMvwy83mhfIDRWnDh/mv/60Ds/0JZrgLeBCzansP1TtNHJz+1VgYwPyBS+IV+XxXZNGvYMr1FdUFSg==", "signatures": [{"sig": "MEQCIB5oZYfNXRpcMav0O0E1+95r4X5zKly5sjytzKMafLSLAiA2gB91WWsKiMlyfAgpa6/9lfxYmbChF6ccMjth72QWCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.5.tgz_1509397065669_0.5155099909752607", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "51642859e367c21deafbd5f0654fb7d4d822c9b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.31.tgz", "integrity": "sha512-d6OF1I73cc3Kv39xVoNDdNwfdYzFzryN+2hm3XJR4BbnG9OiTSfdXqKzC9tXmyiX3k1ZkiwqSL4tCYBjhw1O0Q==", "signatures": [{"sig": "MEUCIQDiT7p8XixuvScEEmdnHlqvJ9ekQWcDrq82qEI5zMNHaQIgS2TrahF2mSsIwDJUh0Rdkr6zMkxHBc0177V9RiIQYGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.31.tgz_1509739464989_0.9145984577480704", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c414c31603aa6798206b5097eeb5fda1b335d987", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.32.tgz", "integrity": "sha512-wpgRdXmUk27+ztlhtrQwNsx1CqJgyh30jQZ8MDBpG2vH/+P1rS7ZoKMsNVSu7RvSCuZDusS+Sg9Y7xMs7SnnsQ==", "signatures": [{"sig": "MEUCIQDS20G6hNI+H25o3roCarr87BG/odZ3BC29MwuEaNhXZgIgCvjwcAxwEKiQlfnoA5yKtkdzyvmKrGZUgwymFGWOMkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.32.tgz_1510493590817_0.16232298430986702", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4dd530c0037feb5a04c519e3e080085908ab28e5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.33.tgz", "integrity": "sha512-4lNnUUYe6b95A4n4UxtI9d4DpNj6d3J9jdzzY8sPpTMj1FVXcVP51TI84pV/5okj0942TyOrnpL7J354v3m1JQ==", "signatures": [{"sig": "MEQCIG56gpEhBfBwzmiuXvfkxowB8WBNdhdp3EhGAq3bp3lgAiBAGLJ7/wBw8u74rtjvV/QBVMo2x3QrkbQfI3fUoVtByA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.33.tgz_1512138493884_0.7802609817590564", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2d168b5cabf333095de1ee4d0b669db0beb20356", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.34.tgz", "integrity": "sha512-O1uLtQyaDYDDbKcib27yQnVmWQQYGWq1aR9wsQCHG9+qt24TOqN3NIUMWako1Blfq9w4bVX/2rIp4kIr1DPiOQ==", "signatures": [{"sig": "MEQCIHanhqqitUkvhicG1gt0mCELB6tVVkjDj/HqURMZ6jcuAiAMPh3jEzKLE/lzmWnBm6NRAr3Uwmk80C7lEFywOstKOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.34.tgz_1512225553417_0.70230449619703", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "065a1bfebe60be2c1c433edfad20591df4ef76f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.35.tgz", "integrity": "sha512-6PytBpASHBhPi9pSyOnCip6ZXsOu0yd5ufjs8A06coeVlp6R57IMGOBD8Klk29FUS+FJo367G2s8PxUKDuFQgw==", "signatures": [{"sig": "MEUCIQD3dHM9KmG3sccQn1VnbZY9ScS0/YQZAX6xjGJMqzA0PwIgbZ8bL7DLutElYswwfZTzSlXooevSqItn2T/3GBKv+jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.35.tgz_1513288061213_0.7157117689494044", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ca51f5e8131050a528e7464966bba6771c0567b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.36.tgz", "integrity": "sha512-seF/NY6BCOf1f75987VeEElUM4x9FA7gVw0INYfTQrf8KfWnYGa+9T0/JasK8gJikkZ9xNs9rJ8ui9d3zuA+9Q==", "signatures": [{"sig": "MEUCIQCxd18oeWmkmoV/V/DdEjF6sqh/ENqi10ZBSDU66HI9bwIgMk1Be4p9vFA6NXb9GFs1FoDj2j3oLFsp7umS7qlXsw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.36.tgz_1514228671105_0.30417622858658433", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9127cd51ed6a88caba4ea9a6f6ea017dd8c9d861", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.37.tgz", "integrity": "sha512-5sxiqaWM41z25lC2CahlrH9GBnBXa5sVAgUN7Ba9vZeqOkFgixjdNK1vOsTl4Z0kbs54DTarrAuZJizv9WVOgQ==", "signatures": [{"sig": "MEUCIGmi61l70TENyQmiyty9iyNvK4WsQNQSoGBCRDeMbVziAiEAthtNjPaRC3qnS3+rjTVti77bCUD/96WTfSISsswukP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.37.tgz_1515427346591_0.5143287284299731", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1d8dd4d080be2999115f575831ee05eb157aeb15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.38.tgz", "integrity": "sha512-GoYqWrbwEQty36dUzjbBoIBW+hymFrE8S7IifH6Lzess3a9z9ST1APNYk4XHrGYYyn7lOGOWe3SL8tPLWoo+OA==", "signatures": [{"sig": "MEYCIQDCCGzlytnxb9iXQ5i6DH5a5kjKZVpSf5SflWAyhgtHrwIhAM/r1DcinxKeQfXfrf4za8M8n5pczYjs2STKssaqeG7n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.38.tgz_1516206709394_0.19544042460620403", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "44a6f10edc255f600c5616e55f9f48b5fe1ed5d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.39.tgz", "integrity": "sha512-IZ398KtlXSKLqjxVF7745k2R18oSyF+NwPy5Xmc0+VjdPb/5hk9HcGWCvHhLEfZQFVcBxLkZj6/3PWX/xRpV2A==", "signatures": [{"sig": "MEYCIQCkeUJoTfF8X4aod6wvyzympXsVd4F3Igv+xLhog5YHHgIhAJ71zs2NBVxLlyYNgqD8ELrQfzhIHZVhyBzu6bUN2uqz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping-7.0.0-beta.39.tgz_1517344050685_0.8175438982434571", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "23197ee6f696b7e5ace884f0dc5434df20d7dd97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.40.tgz", "fileCount": 4, "integrity": "sha512-8QpOK9lXdzrq1QIrP3Hfx/BmGPaCKjBORd2QSjdghPNNRlQFZmO2l3kb0I6yC7w75U1M5q26KvUbAcPrE68E4w==", "signatures": [{"sig": "MEYCIQDz6KvmKCSX6QiznsduwuTn6m0vo2O145qLdLVz2cSOggIhAIA8RzK36Gg4/kj4/yRuoej3v+wHiZztejf2OI6OZyjo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26819}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.40_1518453691575_0.2698057426019165", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0adba984d5332d879879f98204ff89e9f316905f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.41.tgz", "fileCount": 4, "integrity": "sha512-cv39U/ttIa4at0FG1LQdZW6a2kcOJRGklcbGZzBe5WRCciYw8HFQZo8taEYuJak8KOqsEV78w/HZ9vW7igAG1g==", "signatures": [{"sig": "MEQCIA5DwvXVVTGUPUC1JTj2Jb8/qJTXVMlap2rFXRoIxlxEAiAPz6HtJ/AoekrtkHhk6YM8mYPLIP7Fg7Nrw+466zxWXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27027}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.41_1521044762255_0.27724174221930875", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "272c5cc2b46613ebcd2e19491b19263c36d2c3f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.42.tgz", "fileCount": 4, "integrity": "sha512-qvlAR1L7gZ1gqdj81a2AEfuar3lFsr7FSad4JrN5CJinQlVn/1eJe8oB1DQ7U8ocAzDDjn3tGit9lN7uKBWZsQ==", "signatures": [{"sig": "MEUCIGyNY0X3I+hrbE4eYcJA9j5PQzI6CfLYc1kt3dX1RjPEAiEAv4IVKTGSRZj1uDbFKJ5JKkRSa/qOt16gm3ThrC6ksSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27027}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.42_1521147039146_0.6136945166256653", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "35d7ffb0f70e215fc7e237a7c477255f859832b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.43.tgz", "fileCount": 4, "integrity": "sha512-Z8QMRxRBUb5k/7g6CYuREJ8pS95MiYiB6iiNl34/BMkJXzbSzNiVf2de0EsvgfpBT3cMFf6e2Fj2EChL66pvbA==", "signatures": [{"sig": "MEUCICBiXcHsPgq/man4V4JStGwFtigQmn1OTEk/FwhACwhzAiEA3D1bo/PqoXJ7odfJxoKHOlelF9gcl0b/r65PC1STItI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26287}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.43_1522687701988_0.641396956543123", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a7b640e112743634b9226996e58ab92cdebb4ff0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.44.tgz", "fileCount": 4, "integrity": "sha512-a9mt8PwVaudo8LbLrp7TpbwV/HoO7T9Wjyr/aHB4UisUfQoE89iWBJYIweL/ho831Nzy8mNJlXfNxAfZc7Fojw==", "signatures": [{"sig": "MEQCIDvGWxUDft/M3tjAEYkRVZytSYMhzHo2RbTgs0Na3ux7AiBmisAPl+KoOsUhnZuwPfmHjy2qt1BNlr2pWD5C6fz8cA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27787}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.44_1522707603787_0.8059303633429524", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1f46f86f1936ac264bd7a8ecd3ac81dfb88d6224", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.45.tgz", "fileCount": 4, "integrity": "sha512-Ij3yHKNn24pf1wM9mZhTRlSZ9XrMM+mlsNFHAo3PjHI/mK72ndCs3VSVJXMEp2a3hNaGouHrpcI5fTecuO5UEA==", "signatures": [{"sig": "MEUCIAuh9PCun7ehjN3AQXzKTtxso0WiX7bC9VAb8A4T7e/EAiEA34ru6E+BLn97OymkpP2BtG4vn2E3Px6jHy+OrigZkC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1VCRA9TVsSAnZWagAA1VwP/0EQTUaoBctCbFj4OT0p\n2lamHU39GamIUtqolgf3fVMYKWUAWQ4qQVAoukUXkP4BjvcP2KixmM2oqUW4\nxUDxKRjBaMlmCs1BXcihMGC+K7W1azmnAfLgTVb8hFngVB1dU2ahCUjgHSFv\nrX+GCgys9ZD+9CUGGJpoE3Ef5hQsDt8AotbDRZ3gPgMXSM2TaWnddBT9bRbv\nH1u4zEolXl/bgLwybMnf0uwptl5dSrqLFbEmD85i8eIpe0LkUy2ePOE/ux4M\nBpEF5Bi29BITo9F9xk7hlPAaCeHpH7+6NrxDsdaJljL+yiLa9hpwdbP1h3dx\nXGJwy7TyNkH5tWYbir+OAqqMHYQkuwvV2po/bt+uEABDCG8/4KNJ6AK26AkV\nNYVaP6RLzMMoUV6E2wRzjzruXfAPLV86frKp0Ce2M2RB9JQnbidzog1SQaW+\nccggoHV77k0R1aZh1ARyL7UOejojbtpFnxzZ4wdM0cSTiXlMG1XFb3Aygg5I\n5eZsdMhVjKJIJr8zoHhdvRp0u4LFwlfZpk5RsxE/bU7z8R0Yviwzklc4dsm6\n/gwS8EnEOwPNn7GGpG1tQv4o8DvNnMZWSCV/sEbnXgZyEDcMZm8YdFV3m0D/\nVznjoU6Bp3Cpgz3jrjw5IStJJffeQBbJYOXLl0+wGCo+2h3yTxYSMLDewmHc\neMEP\r\n=HAnF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.45_1524448597169_0.3965018630199557", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "da42dd17fbed675c72233988dbad9ace5ab9e4a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.46.tgz", "fileCount": 4, "integrity": "sha512-7OwS0ObI6nLacEKP1HCdnoIQnHBqOV6IgtKGiPO+Nj03OnZ1Yo2aeK9sfOtwL43aNztnKqFVt2L5PfZg4VGidA==", "signatures": [{"sig": "MEUCIBEhTr4HpZ0XSo/CA8CP8UAVtpI9Hh3T2Fb/fYWfupQwAiEAiC26JCVQao670hXaTsA2ECpJuSuOek7AwbH37Ep1XfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGGCRA9TVsSAnZWagAArZ4P/itkEJtRA6zuDcmyujqo\ndKBW7UMlHlFn/Ah1QtdUrIeGIvEswTSwQrqvUR+SoxWvHGY5cZe8IknfRhuM\n1cHBjkW8whcDqxvIh2HX3OYwP+OvxwUiSMITBYH19YGNCL+Rjk1yJcvzLWcv\nrGSXP6wQMHvfDB9m9KvQM8A5DNbekFnn2MueImo0IvKbsGzk0GqMN3MuRjZ0\nmYl1AkXVPkxKtI8RhwXviz/JbNBmCEvqSQqwjTkWJEg2VUfIIduSGymyOxA0\nxzsX4bk60bV3aw9YdxmrPyQ4j0S6/Ug+hfnGh5qagCPez1lCQxPIJgskwOOA\nIsWJB8P7atrw46ATqqCsAJswb4cARQDNSyTSaDHn4tDKY7TZqfTZmBg6VgoM\np/Eb9cxFETz2kbITvsyU5j7uy1iLRCDwpgBS+TDQct+at1W5SP3yfRBroy/b\nsVious5UDkzRhsFypiBrp1KANUzZCatJBvqC3c6bXfvXD46M6YcIedcdZ0ML\nmhQ4v6h4FxtsFo/UNd9cb5sZPEmQIVX1cfAYK5hHlzacjHkb4BA2GT9ILq9t\njmaL9X0aautwaxIytQ5iT6SfRWvYG9zoV6OjYNuyNel5RT0E1rkzHrN8tz2h\n48qZFiDSaEcenS7fIE/W9+vbaeP5Kx30ttAu+lCdK3VTiAc4pNTmq0UJTaDY\nluH1\r\n=6U+X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"lodash": "^4.2.0", "@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.46_1524457861706_0.42895027875215685", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b737cc58a81bea57efd5bda0baef9a43a25859ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.47.tgz", "fileCount": 4, "integrity": "sha512-V/u3Zdy40KjVQeyYUaQnCGiHQbRNJoc6IEtNDERltuW9vYPHS1n6YGc+EHKi8JVYT4kE6UHOjD+BrbCCV4kjRw==", "signatures": [{"sig": "MEUCIQDk5I48FLR4ZcBp2J4UapqCB9gW/qgBox6+XnGFZCg3fQIgZZRBnDmbe6bp+agZjBUA66RnG7H1E19zKV9tdhNcf6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUMCRA9TVsSAnZWagAA3poP/j9smoOXw8Ez2WxXmf0o\ng0U8eFhPr5GpGtvBL0TskYkaXHlkvvAzTH/QSte2v3xSH5zWFXbgMj61h84X\n/K+yRlI0rQ/ojoC2kRPfk+ewrhiRisvuhGu2DIbuR3hwow6KIMhgY5bnPdwt\nKb3wNJrG7s2SDqmbr3C+ldKgDKT61SUGm54gQqtbauRx4OWRM14Pxw6vVeZ8\nKK5DzM/WwkWZEUA/a9jqL4FI7B1VfiAsvnNc8RpiIOHcccvkVsOo/wSZMulR\nQ9Phebts5QFaYuQND3ve6RhWyA0yL79pu9NnXWA7k5i+fq24f1lvL0z6/Zwc\ngudZ8D9KkFRmx/2E1jmu8SeZIuqwCt5VuCpJWsLtfokoCP5xu+hjypSdXkhT\n9VU6/ac5CfNnf+mIvm0Xg8Q6N5Ux/bHH2Vc19wRh+2mIHwHpnwQzgl1k9ZYN\nJvOM39wUCbYFcEABsPHLuQ0XrZt1skBRvXMdkmDCWiNhbrhguUH7euJAboyX\nTRl50IQ7PIKsu/6QpZNztQsz7s4CQuYqkVNhgiEFqStNJvdTWHFskouNb2iY\njCX9W+OF53Ipcd+HamNSZoZ6fNqFT4EGlZp8iQsYmwnFGLOG53/Eldl8GDSH\nXOI9YdQc15Uk9HyaMVknk3xMgJ4HrORRnyPPXsf7qNd4v0CCJq0Sf7GMGbQX\nILy8\r\n=151J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.47_1526342923509_0.988950644429025", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "849f58dd9cb7c7544af5a7ce92751931d928fc18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.48.tgz", "fileCount": 4, "integrity": "sha512-KMWeGBAn1FmyNZAW4HHfGzSjswW4YWxFhUEOif/RYfJt7h8Ny7ELdqh+A97q5JNru9ZjPyQMBvGgXTOi4fANxg==", "signatures": [{"sig": "MEUCIQDyUAfExReQslclpmBEv7lT4V6bMqunGa4SQgN/tajXngIgY4zzumzAfm6ncCbwjMs/QCPsNWiPA5WUf5oYCi7JIjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDlCRA9TVsSAnZWagAAW7MP/204U09uz2DyUv/wE3r9\nWPBaQn2FMd3nPPMwNvHm3uKXJUKv/oLUipxW8GUyuizIlWsPKk6OBfXROtei\nynK4itLW2MD715G34rCvBaZuLGZcF6YAui6bqZM4pi9hx1fJohoPdluBfsNH\nPVRrZGrWkWBNQpQVcNQj7rJUIXdgbW7pu0g5qhRYqHEUTwb+gPQysXJLOzJz\nLXcmh+mq9i52gitjH6JKxgAYgQ1UP4x9r8w9zLwI1ksQyjp4kR8tg30BuAVf\nWbuMsfXE86lCrhWFeOYQwjyl35NPGtd0wym0YNf1YmLGrkMgSMkVG7lMVWLN\n4/UNj88OTaMTsURqAVa6cSXiS+U2gdmhJd8ZgWf6nR4nm3XlDT1aWCt6gORa\n9YmEHY5Zhkn7xwGlc//FlQxTbYuGbxd+8OzpyCzLO2I0Sd+m7zwpusjjHGMD\nVHaIZvXZ1kEHkd2kYCGhubeTzRrNu9j1BDIG8c3hJegeLN7wDfOLsNgzfRKJ\nn+8zmKmlVkYoppgWpstQyjNN8OshOo67Ya9N2hPC9beFtfATDz9pDrCh8qEE\n/UxmkqyvwS8azHKTHAnmaHuxB+82m0zQTHzLyAg0yRdJy7lStuOKNOUvjvY5\nGEpNBDMKaw7lmww15niyxPXSDH3E932ZKPawYca2COPN3EhNcNGazZO9hOll\nsP2q\r\n=2r6V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.48_1527189732581_0.7808042963478197", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dd5a9ddd986775c8b20cf5b61065afb3dd9eaac9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.49.tgz", "fileCount": 5, "integrity": "sha512-MjC6Ay37QnGMq65EEC2nS6jpuNBIE54qykYOVElFkriceYdmaOQEt9X52T6j6TS9nRFvHIK4LJi02QWgkNmZgw==", "signatures": [{"sig": "MEYCIQCNLWRWftXuT+Xnj53ftWSK1lKRzbDA13RIUlIT2cP+awIhAJ3dgTh6RBlTm9IDrbs1+qm1JkBi1BUxTEDl3xOCXuc5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDN3CRA9TVsSAnZWagAAwFoP/A7ZqzzCJe9oEsvMHckQ\n93Z+4QfSpoDcsTd7cY2Jt6BgRndWtyi++rtUaZd6xpB+1r4CtS+Fl7IgSrz6\nYZvcNbt4QmSUvFX83QXNCY8ATGvzU5nXpCVFAdgLDRVzQUM1pmyK+BMCUchA\n80xd2fqAe0XJdVceRj4+YFDOF0Q+JQpnA+yTYFdALFYqrpPXt2prE++X/mJJ\nV4tRMVetXn1e6b45xOlW78Zuti/h45XzCg+jHuYeMM20pXnwwzedbAwL7IPu\niCXjAuCc2qAfxPILfM1rKOrhHvyw3lbYcKUO6SxXw4pwUpMCSV5V1AlguiNB\nQRpj6C2m6CTHe2B+uzdx2Ojxl23LozHdgEF65M0h/1aXbBXhvnXFhgH/cEKj\nVlsdLGBj2ocRdkvMvZMZG696VSe+96BO3nReOrxYGVUfF7iIdQMK7yjlirNE\ng4bacA7D+rIOTz6KzcGUlMeLBnYI9xUCaBBFsPHGcSGITNn8AZ/RSxVV7cwm\nempfaLGXUvdJokjCo/kYqkW2ZWcMyxOfQsNPgkjrFIdLQ1z53gD4qe5rshfl\nj5iqqeoukFD/Meg5Kj8O6rQNMDf5npGtiySWKgz808E6+YLW70twwfW5X9Pa\n0eoauM+LtAMZOv2LqUqdUoCUWjddKxmVDyZcOIJLq4QLDyDzqpZil9Gquep+\n3LqH\r\n=5shU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "dd5a9ddd986775c8b20cf5b61065afb3dd9eaac9", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.49_1527264119105_0.33178130336834033", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9cfe8234eacc19d10a864a32b81ba484c860fb45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.50.tgz", "fileCount": 6, "integrity": "sha512-e836JO0IJ4Q67fjQx6s638K/iW78qaljd3/ikcOVy73QhcqCapWSNfjlDCCBU8mEMX1EyPG4RPPuMcSj5URFiw==", "signatures": [{"sig": "MEYCIQCFZ94/hN8TBbotq46UhvdCUvJ9VYfKBPSyBPt778336AIhAND7qxbpOsaM9+LJQnz8CStVk1Kfks+j/L57xAD4NKFF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24691}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.50_1528832832705_0.4479710678064537", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "be555c79f0da4eb168a7fe16d787a9a7173701e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.51.tgz", "fileCount": 6, "integrity": "sha512-xRKVTFnChi3+AFJj5Gkbu6vsSuOAlMtZHmFvq8VAjLNHCD+/Uk2yaaokAy6jbvnPxwwkWFxccNV4DUGimFSg9g==", "signatures": [{"sig": "MEUCIA8Ut5CBwNZ0eA1l+hio5CooghXoKvb4EcRA+iqYCGgJAiEA9sD9pQ1dib1K/3jaGXFSUrOM4HUduH2mqoucPs79c6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24705}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.51_1528838383524_0.13547243127927389", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "52e994d77085c6fdf05b2d89654755ec008eb54a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.52.tgz", "fileCount": 6, "integrity": "sha512-78L9aSuK1WwbrAnHA9O5Ek+m30gQZ6G1XwDjCUxvsCqQOC4MIeTTqfQ6ajIG4VxRJesr8sjEhPShM3lrtY0p0Q==", "signatures": [{"sig": "MEUCIQDCC9VGMkpNnnRZLJ/3htBhxgkhGh9D+JEeuztjCfCjmgIgUO61rFZOIBEQxjLk11ub9qYB8LS+cPz20yI8sWuA6Fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24704}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.52_1530838763083_0.8131049143336624", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9efd6e50ca1fa398dcaa7119621da3f1fbb821b6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.53.tgz", "fileCount": 6, "integrity": "sha512-lBDSm853wqigbxRR9m71Ow91whK/gjOJzFo2kFwJulUZJaV/pdQGSXopANPRjITplKbDufIbClPdIK1V6dnN+w==", "signatures": [{"sig": "MEUCIBmIfb+WEqGz5n041BlrUkt69upLOLYCtdHuYHQLPKXmAiEAvBILBCOXVNgONGtIK7AR5FvCvzAVhP8fLkIVJVtBH3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24704}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.53_1531316413003_0.2030057716213003", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bcae1c2ffae4cc3b7b3e5455f0a98daecc09a3c6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.54.tgz", "fileCount": 6, "integrity": "sha512-e/7N2M73x9I1c8+STcjVCmlU7BUA1PbW1Z+N0kKt+aU/i35N9fJKNzMoAfyCGi1QIEBHZsIfc7IUqgF2soJSkQ==", "signatures": [{"sig": "MEYCIQCV1frhgwg710c/5PPtUU5tL3+VWbRDTcs+s+kSyBtjZwIhAKhK7+3CpRQ23ccroEb7KErcWowu+6cLOLPKxxKQFrgQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24704}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.54_1531764004236_0.3268001794790558", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c826f8c20304ac39f6cdd11d14f1cd7d90aa5470", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.55.tgz", "fileCount": 6, "integrity": "sha512-sdas0oHXHGhNSkEn1Y+d8G7HK/uhSGVoDjCljDRA3VN9DuuadkrJzUEZfuRPUU+IRs3e/3GVbxFc+BNSqZMTRQ==", "signatures": [{"sig": "MEQCIGCu20b+PA8Pb+grBBnwULo+D1E1PY41ohjAmQDNu84AAiBW5xjNMsrfFtcGzza148QnlvFtSVndWs0LA4nXe0sK4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24705}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.55_1532815632252_0.9465470848377258", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b4a3ec153d19b92680db95f4c3823b88e6f93197", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-beta.56.tgz", "fileCount": 6, "integrity": "sha512-hqeciuZPUfsZIhJ6MaB68U3+G5eS12ahidn98oUxyOl+BnS/aN9EhSt877mJPlEBe3oQy35qNwg/HG3rq33O2A==", "signatures": [{"sig": "MEQCIF8HQf0C34N8gQ68q08wIrmBDwq4Lf2oHTkHlqtOTTI1AiB64VryQ3IzaAVQGzwQaA0YDvlStLrMcw3OqxncalqS5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvUCRA9TVsSAnZWagAAcOUP/2UgL0PV3NFflyJJHGLD\ntMvhloUxQuggawD3ZcoFlOQLrJWwZ/RF/xyRutVguTL/oQ80zTneIGO0uBw0\nW8F78khSCpvevOvoD0LQpxSHs/q/gzDkuDffzPWA4VxZ5Z8vN4mvG1BNU7Cw\nE3iBRvpeRudwNvPfHnWmttiKnKWzkgLa7nEYaSy7jnqpOB1lba6CeUl7iDoY\nEEjYDad+VFjSZnOI5FYqWrAe7zgV/7Mk0QPDBMYBLpAhF4Q26CgUyZli1p9t\ntHnjjNuoCmXaue+1ARd49nOIhgo6AvvIcnIUu3EIyIIyWRAyxGlidlEHZ2P7\nGjAbpwNJfK0f/wOmsbtieBHP5OuZf7TiAqqUphwyXEiqbA2YT+Jy1QcBQR6G\nQiaEYIQsN6se/2c058SfqWvE/3TQgu7nVi1UosY5yc731uLyFZ57zwPq67Wf\ng5Ah+JHVXdIwqMjd26kOKROAzmp1faw2oj5aEj6fW1qzUT2IwkkCAedpxNpa\nvz9zflAF3DNAYL/QWbUnJSIiVHwl8qLKgJgJ01YQoeOvQrt3yO6+lkhFZfmE\n3vY/RUURW9FIzX6h59m55VVa5Sy+miqDwd42vNR2Hkj4MwR+FvuRrUVHEM77\nuhSzxfcdseMhGjbgiFqBo6BLHXq+tl/a01dciIF4RXZTiKU0ipEj3ZeOVi5N\nd8gW\r\n=ImPQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-beta.56_1533344716624_0.05172871988070171", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e70c3430f2ca2e7754747e42b5663c2702375bad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-rc.0.tgz", "fileCount": 6, "integrity": "sha512-cta4/u/l4I8SWVKbG1q/ksCy4wUUjzOOfC0htcBhACmoLT7ATgCYzf+wbBMhlhSvVJXWLswfAqhO4/1C8flIFQ==", "signatures": [{"sig": "MEUCIQDafPOiIkHs5FY6NZI94V7/SF6oxg7eSJ+gguzuo0tvRwIgKfTNgj5V2/dJczRIrb3AAjCVlWq/cF7rSdg9+oc4eME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSLCRA9TVsSAnZWagAAjgMP/0vugbMEhDOlYD4HDjRp\niLbbh+Jawmq/jmlxVxewMy4g0ZyhZTLuy2tNh0ldyGoqUYVQZVv1M/y6da51\ndTKKmDxY1j/seiIlr7KGls7hq7rpOexq/H/hM5Jc5sWIldY6/fsAaNFlPkeL\nHVHq1purXqFS5UFD0X+If8d9GUveB/gt5hhHNP+DPseKNierZuAWi2M3EOH1\ndHBL8409tuQtbNkIpZwiFt8F1Hd2CzrEE91IwW4I5y4bOtzM4qhHQc15XwXe\nPNVw/zncpZC1Kv/yW/YwZ321Nq0vXkbnbFvPhwFvC36Q+FOq4LU6D+VaYnNs\njZs78NB7b+EEWFsb1eMx45HDOeknTQ4od6ARphMxPRk3+kidE6IVVL4vp9bl\nw5dWIu/jO4gCKlAX0/n5a+MYE+4W/MxKwCgKFFkv/wXWs0bvcQOKf6eAq6fy\nv2a7E5tiPI3o24zof28y+4O40pPpz4nMgHwJHKG/EdMeYGU8EjCsAcU2BNnM\nnCiXPSZx8/Zs8mqNEQj8+SU1ai2aBRVK/3EvM0EZnuh/jU4hKDWlrl9nkj8D\ng2R86fdOW+WixFIZIwkh8EPeF+QGETJ8aM2UIbjSIN+ErpcLYCczFCCCqBAV\n97h5qRHFNe/mED1XCdagtMi8DbZOBoySCLhAoipWYZN2rcGxFElgO2QT5tqh\nUco0\r\n=TEqZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-rc.0_1533830283181_0.6342817433398098", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1a61565131ffd1022c04f9d3bcc4bdececf17859", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-9uGwvSqJcmcKPEkLHA7ffrG0lKXTXprupwGjEKDw27OoRWXHdWUmA4VwpuzMrUsYyV+q+P6mgj6TPzoGJA3fAw==", "signatures": [{"sig": "MEUCIQC59vnRL+2cVrOpsdDGH2hX9UTpkt41MrW/GePhDCAN9gIgR5yKIrABKp/DCl7nNBI6Oi4hk/6HcXwf09aHW5yoSOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8SCRA9TVsSAnZWagAAKS0P/3Cg567U8HT9KTnxbpAZ\nbIXOz40j6XvPIMXW1tON0K0GboWwuKMcwrJKWlbEnvy/hpcJumKWsrGxwwgZ\nsMvd9avAr3Un49mCsZNFYBFravKJX8+iWnAnoT0kFx3isf7QGU2KGVyMmx2r\nEiJaCqxpIx32Xh/QwJTjbeVtuzAEHIiqQrYs2vRdtBBRm5Ixo4wSseeayU/k\neY/RvsZk0/Tt9tYVj6SmMiiJR0WKD/veKXzVwrpYKHkJJKHMkSbbo0QeN3gN\nNEpwrN1OdcnFxjuxvUi42RVBA0jMBRR0CSefUC5YuYM18Itz8Fle7eaHs2UI\nay++ZyzgTXnoIZJE6p/ZNS5fzTebENV/DAYrRHj4YkQw3k/zqg76k8jhN879\nKpU7x01zGARJ7JPTiBnwvv2eu5zqFPlHnyDb9Pz9LSECMSu1JhuQ487Gql4k\naSDNgJ+FGPlSbwkUwBPTc0AwHZ/xV3igiuA8R6yuDStjNDRlHCHc9faZEhpA\nER28niNw3cjWtj/ouNw2u94IFIBpR42OXP1vaycXakfcpgaygUKn3N4Y7xFC\nL4BWvCWFEeL90U4F+qFQM1ZyZdpgUd7DSXOurPP6YQBrPK5RlPBZ4wF/5qj9\nKl6u/AVKl1UiY2WtaYrWNrN38NJh805WucMhu+S7jTOHMovzmWp7J6h9AQIQ\nwCWm\r\n=aZ4X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-rc.1_1533845266065_0.4835811036632731", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ce5aaebaabde05af5ee2e0bdaccc7727bb4566a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-rc.2.tgz", "fileCount": 6, "integrity": "sha512-bJ8717eZ0Y3oY8t5qnCS0f9AQd6aDt5gJiNjqtEYs9OZkwxKfcadnRuIiC49UdfrgbNqVPbXHz/y6ueJiLtheA==", "signatures": [{"sig": "MEYCIQCFCZSFYXML3aOyKtWoj2JqPjx7Rj3uw6u0jNeZMuMufwIhAJr4xjNyfcz7QxI+qKSeYrRwb/+sQWwdkFypN+ZLegT6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGa/CRA9TVsSAnZWagAAwm4QAIfa2qYoDzo7S3syiXJd\nf+o79IGyu0bej+ILeGrTgHm6W1WBAgnPq8bH4LBfZOJGH8u7K7iLfoH+Fu2b\nFgWA4PaBsff9jdf3udk10btDFpx6882qHrMfLLl9fH2IypqpbWbjuAzR5jmu\n6G06Gi3hZL+IsmFkhTrxEj9O7DQwCDONT379+qWBwszHDSdoD12nIy+u9wBh\nANH6LWZQTBUtiSq9l6ZfOBp37nJC4B3Kf/yBDCJ20NQPpqrshlzcRlSGjFED\nZzPOST8BKIl/LHjVdvTq71hvhhCER6IRqkdH+4xVvjok+AgU7Egtv4kjD9K9\nUAaND3NUcWFKeL8nAtegfK00Pb63F7AqOewU+r9KBDXplEnIvFMqqV4C+ANB\nPCq8ID39GFm/vN++cJzm0Cv36/sCYAcY1RYCx1N2zKZScVuWQ58UsixEbEjP\nyWPtEGZ2BoB/sJiaTlaaSRLj03DFJ0w3Vc6LTI1mOsQ9PiWqUjxD2uE+/nVY\nhHAaCJ7SHYE/kixN48gOWdQfJx8Wz6+vQVDt1SARoIcUBBpOPJe6aychlAyF\nEf5ikn5v5S6o5eEkG1ENek+M0iejMGMIrZMcW1KAc1rD7i8eBAAuERoGoErq\n00LlfydOqTVcXgZChf+7nHlETGxJqDzVvHFI2gV+DjFxLJ6nOEk/56nuxG1W\nRf0P\r\n=f31h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-rc.2_1534879421963_0.15388393567603176", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "65ea3edc061b09375218ae86edf6e0897769abc0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-rc.3.tgz", "fileCount": 7, "integrity": "sha512-pAy46r7lLRu6vX0npWWBE9jI605UTDbLHyhNWvrWj07q6bsgXF8HrLkObZwxmQC1rBHp5TN/qBEZqHs4z46Xhg==", "signatures": [{"sig": "MEYCIQChhYrBqZuujeyUIyFwHh9b3yGDwDTx+blTcW03VkXmgAIhAJKc8FyLc1zaLN9LT3VYB6KBH9VcJamBTHDMgESTTudS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEl0CRA9TVsSAnZWagAA1NAP/2jYZoCwq3Y2P/evw/Po\nSdEkV8fCUsdQOPRwEsTjSbRPPcq3aLph3s5wLnIKeaOeRo6x1V5re1cVDEPB\nVZwwEfaN+rMShbZ7juyjMEWygRZnb7HvehLwB6TlrdT05e4iPCJAFNRkrgsH\nNU0/nuUrFX4LWhmldlByAPV5+N/L5+WV1Ys23RHmFbZRTro0rwEWJlnK+jDZ\nqPQIKI+vrSxI9U0DZ89akby4KX511TW8VJB3QrzyiGYtdYx1hDvSDpiw1s8/\nFyrS4Pbf/4rt0pyWumB7wKrGMOCHMt11xerMkVwQWpj2Gly0uJwYnb4RzDgg\nBmxkFsk/n44dQj6h3sx9WPbEoOejpR0Ls6xPUHIagkRsxjpOgzJVFoB76UWh\n9eErKVchWj2Lj6g6mKhxmRlbhywOeyzBIZOEPWZTlLRh3klEfFlWLqdw0rn4\n2lRqOWpm48evjPtFqS/K6kvwH/is1SGEr4h08sTH1aTjsOg+nq7yZVmkb3u7\nm4CU7bc9mingTmEiZBxaoqrnoo5HwRYRTl9r3y4KM4Br/ZHbnmgX5f6f4rym\nM3Phlg8duMnyMwINhgdqgg7yYvM96iAcHprhoFSyWW/A5kskTXEKIh4azWdD\nhKV0jc/cI+3nEsnqqq0X5POetKc2XlI7aH3WiKU/+LZajyoRZh7Gk90iVI+5\nuzHJ\r\n=Y7PP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-rc.3_1535134068291_0.8708830251412882", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5ae8f2779ddf9e5eb4647cfbf1d0092f74d75cba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0-rc.4.tgz", "fileCount": 7, "integrity": "sha512-z8cwb0fRKWLVZTlzffUmdh2N4XztbEMkcixxsdJ86AeBjVeDDB5mwuYutSNvVig+FcDmbkrCNFijlppvrC+MgQ==", "signatures": [{"sig": "MEQCIFgja0/lvG1tzmu2nFmppPf0nzzwWb1ShiPvqYUsqQu6AiB1bnwUDuJGxMzS+D9CEXdxQ6k5Jc1OnswpWI1arCGM3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpNCRA9TVsSAnZWagAA8k0P+gKyNPvSxop6x1GmpacV\ncq4FZs0nXLtZ1enODSxWjd1ukWvks2cj1nUZEpKve/Ca7fExLCrUCUPsBF8m\nvIkJ03pd07C9OffSFawaeLGcCAbRhNuxXN7pOPnEu219/0ozxXoKrEtb76ZJ\nf4JX4cG7WErsl1CotbzmZauvTCXdNvlP9QPf3P7I/sk5y1KxnfLm4Hz0kReV\n8aw/hdbxCLirKHKkirnC/74SjB+3HoG5P1De8rr1KAwV4LwHQiypp6Fq4Cib\njVDQw947ZCOpADSf8oYSRGrVWDyONjJP8bSuDGAEUyT4KJe7YfLSz8w2znRb\namYrJJwE2PTcKyUkjHZuk9yjcuFU51FDmwgVA3sREBEFDZHcg2FNxUq8dBqd\nrNTX79Joqa9S6Kw9qUi0v041Eslqqirt8M9xl44c4zybNYjhVWp92ExoxHXw\nNI82zIbUXQO2D+sRXT+Mv/j0PvpC/Jpc5yN06zhBmC0HhoOsK7nZE2VwXdsy\nmywO5Fhd4XUJSM2FlUve0IgJqDiBZ7JwWkawUvk/pCDZZ5C+pcwDSId5I7m2\nYxcaiyA1pQe7ld61gntdREF3Gr8rAIWviN2UBEzT58023NhAIdg35yiXsFQO\na1d0RgmoGx9zv/M//vpMbhbwbbs8s67LLyr1gVbcaLI0Pd9sxI3fisLO6/by\nrBPW\r\n=GKTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0-rc.4_1535388236534_0.011964378181340773", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1745075edffd7cdaf69fab2fb6f9694424b7e9bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-GWEMCrmHQcYWISilUrk9GDqH4enf3UmhOEbNbNrlNAX1ssH3MsS1xLOS6rdjRVPgA7XXVPn87tRkdTEoA/dxEg==", "signatures": [{"sig": "MEUCIBUyZttGJpaSR+0Uf3QcSTefBpoMd1O6nSVcorAyACWpAiEA6EgeiVKE6PAFHoBxHGHzlUle1qeCNAIejnhaxqOHABw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBhCRA9TVsSAnZWagAAVIYP/1OleKeP4b2EGoa3VlpW\n1970EYFVhaV62RqH3MWqNRoT7lNzTMR+XtgbwiOfxzx7ysKXS4SLrKpInrh8\nyYHOOV+8uh54xxvTRpg1jL1EjeUB9aWgqds5Z45/c4sTmESH9X0xMvJeTZHJ\nW6ukGjeY47lcmJtG14gpck2mMglleTt7ZiodHByMAK1lxQtJ+gCPNS8238Zi\n2cbHJXmI+aLu7R7kl5HpVsHV0yr9LWBT9seR+4LrbNmpPhkwPbvw0NEoHWk2\nDlEkJ0mj+S9KOYNqFCsClajoF53zV1FRMYc1G0/IAiBV9VbxKTXICc+sOevV\nCsnVjEoSLzdYT1Ax4+yR2UcRy5KICfykUm5Kkkx2vt57hFy3OS7RepVqgebo\nPqZFmmZ2BbM4NQm4r1Ck5TGmKc7R1NwqQpxxbxxdR8pXYaCxchaKzavG15KH\npvrsOSD8TNTWryDgjgqKDQB0gpNGgYWc9hnPnUFGCfg1TV4Ps3gF+W353HFh\nOG6H3+G12yYw5UVHcOWMKviokgYqJ7K0BZaPvDVMTe7AgoioMYjeJ+dX20Mo\nxDYrc7RXKt9ZzLQyla3Ow+g628UO3j5/PmxBpJblfKkuuSAJbcdZEHF/OGkX\nbqkE8c15zoZi/+UMHI6rGAvuaMt/UfZCW+XwfWztXnRM5jOHK5twD8uvKudE\nymk2\r\n=/2/T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.0.0_1535406176517_0.31981167653687126", "host": "s3://npm-registry-packages"}}, "7.1.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.1.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.1.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3e8e0bc9a5104519923302a24f748f72f2f61f37", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.1.5.tgz", "fileCount": 7, "integrity": "sha512-jlYcDrz+5ayWC7mxgpn1Wj8zj0mmjCT2w0mPIMSwO926eXBRxpEgoN/uQVRBfjtr8ayjcmS+xk2G1jaP8JjMJQ==", "signatures": [{"sig": "MEYCIQD4KynXZJbPkq1SntJBg02Xz3LD+VjaDOkE1oIbtx7UfAIhAO8GrzS7TjHfak9XJ/fH8VFqPURcqFNj2pKAotUuCqmU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4hPlCRA9TVsSAnZWagAAwRsP/394tkVfiQmmcKIl4krS\n28IlwRMW0MWd1qXohpAvWiQH2Y0YoGYU2deCB3MBXuBHhqPwpTIze6WdtMqH\nebdaJdupGpNlehJ632gy7tGuvzE8qZDS+uc9s5XzB8UiuvmFxCSRMDcs8eEC\nM193O2DoKtNxCR3L87KH28tw0i8LDSPhsqyJ97PBRkYfSy7+bs/N9LR31OJI\n80ohKhyI8+f8jmL5dTsogijMg6cX43HjUily5DN+QxazhMlP1DmVDDDV4FAJ\nbKX0f7Cm6/uF7khEhupx8xjzRWx2YzgQKdMP5qMI8qi/NG9WElG/LrkZTgSg\n9HbQ01G6SvhCZwfldaReWu38n6jOY2swPG5u+YB773xpITXP7iHzsRypDkhp\n0Uz5Qq+4vsz36j66B8dfq3c5f9+C8h3F4fOwazjWM+LvlJhfkx8I35Qd6Q7P\nKuNcUbolR61lRw0DRIUddlehXBb2L+LiHkzYw+2a8sujfq9ujj60jPYDl7AS\n+DYvzWA5c6fGSZOUuPA3D722V1A+0SoLUPJUy0KgqtE4KKEYuNmbB5jpw/+F\nSqQikvhJMRPv1u362v9X5FrA9/RDohhtXVjUqVoWDkxNXGj2g5z8V079obkL\nfprwQ01uzFfSlbVT5ljAbUZLQbdKtkF4LfQChsaY5fWNQfMpg8RV/FFm0ccf\nO3dk\r\n=Qi3O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.1.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.1.5_1541542884641_0.10289786646984855", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f17c49d91eedbcdf5dd50597d16f5f2f770132d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.2.0.tgz", "fileCount": 7, "integrity": "sha512-vDTgf19ZEV6mx35yiPJe4fS02mPQUUcBNwWQSZFXSzTSbsJFQvHt7DqyS3LK8oOWALFOsJ+8bbqBgkirZteD5Q==", "signatures": [{"sig": "MEUCIBUMw/HzNEhg7XD9VWkkN/uI6y33JCvBWdqvK+jFYgW8AiEAr+ZWsVHrLrKfNVXFiqmAtMCniD7zXVPuo7J1Mb+yaA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX15CRA9TVsSAnZWagAAgxUP/RvpmOSYCBoNz2Bveaxl\n7Nk01kFMSicFWv3lj5S/mUQps9EllExEJga6LkXyE9Lqxbl+j3Rzz+JSHoDy\nWUQInqgdfV0c0L+I2rXNVKXpMqnxUiqj7DN1d+ZCxg50L5TgYl/T0tdvpDTd\nCeuifTEDWgD/ws38eV0DlTUcGEbpaHcE7QdUdWIUlIbAQjGsPTTEEwZSGkti\ntLl+o/VmgUqqdVK7tWvNUHAM79D9q7B6NolnPj6+5Tn+htxL/OtVKKh1Iz/4\n9JR5Zq21xdugU3I+QiQy4MlEI66373FdVeJ/qbeI3Bm92yESksKqkwHBaxfv\nvtz+hjzO5sbG6Y4H1pEvdKe7Ru1HuOTRGaOWyeR9y/XCE0IAX1MKA0xGY8mI\nMz0O0MMN7SnkGMnTpDD64jIOIpiIRZoj0J9uddtw4mQi0CKiYBq5y4NP8v7P\n4GgVbXNpVDfqLEIbWx0h1Mq7dUSwEmHvBu2eUl2us7INR0cxAnptlPnA4V1z\nhwN8XBsvQtt2qhDhZm0y9bCHzB2i4oPCwStlXkWSM6qO72VRZQoM6jC5iYkw\nhgvJYaTNki7m32VBm2I3htSaosn8tQXOUN4MDCwemXIaicxWyCSXKvAMSK5s\nAPPUq+tRbPQ5n+E6/ZqCdUL/ReIg/IoottV8dJt+reic6HQP1v1T7IsG8aXN\n4uB+\r\n=aUHv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.2.0_1543863672470_0.7573958150364606", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.3.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5c22c339de234076eee96c8783b2fed61202c5c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.3.4.tgz", "fileCount": 5, "integrity": "sha512-blRr2O8IOZLAOJklXLV4WhcEzpYafYQKSGT3+R26lWG41u/FODJuBggehtOwilVAcFu393v3OFj+HmaE6tVjhA==", "signatures": [{"sig": "MEQCIDthfhbjtepIipDffyv11zS68XWEcA9yXGTYG/cYSjCfAiAljzrf68E6Gu8wVc694hR6FOq1i614iyJNGpVw+fY8Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDVlCRA9TVsSAnZWagAAcy0P/3ZklZVvjPg1WrUUu4LI\nSZJBl9eHTBgD9xEMcI9hitkFiu86c0n1mtONxjCuoA6b2slvhAxGmx5JPd4k\nMLAEmEbLbMsi2VSdlWhv7/56hcfBm3/cOUCj/IqXSnPCrypNiOuZ5IDLakN7\nTcgO6lfsVbs5AuusIJDTyDs1mLEWiai+EQfy2AYFx3Lv1UonS61M6/hiM8Sp\nD4B68bjAo740x0sozvPE2zgZfbC9MJN0tAMXSJZE/PCkViou5V4NOxuRxLsR\n/TvvuGoD+vZhOLcTzY+O93h8/RpicB7SRXjVPeFyp2l7IuHja9+MuCscaYkb\nCWgH3jbQoRscWI+r+ykU519GIfnJAGVUDb9n0AByCp8ZlyS2u2vuVA8m+tod\n4nF0c2XTqk8ULIzRZzxgDdasFMVIvTT6bXoZ6/YT106jcAqKrolAIaPWUhrV\n8pFamEKR84B6/U37D+XkoJzAd/EZBjTAg4NUeJm0gVnfGyLyxOaR1koRf7DE\nezlDf5qw9vReBoMZ1Mlzk9jwH2sZg7bL/J5NZfOkxJvcIgOWIN974saU1VF4\nFinAsy4VsUVC4LhTPy5Qx66yiSz6/EkYNKspjmo7QjU1FI3WOnUqCqzmYFKx\neWIIC16TRIdLs+bhPX50IbF0Ht+0w0TSHjvJiD0OhNHiAsdCFdhJd728oeLo\nTgpc\r\n=jw2K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.3.4_1551119717101_0.7485418439269089", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "164df3bb41e3deb954c4ca32ffa9fcaa56d30bcb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.4.0.tgz", "fileCount": 5, "integrity": "sha512-AWyt3k+fBXQqt2qb9r97tn3iBwFpiv9xdAiG+Gr2HpAZpuayvbL55yWrsV3MyHvXk/4vmSiedhDRl1YI2Iy5nQ==", "signatures": [{"sig": "MEUCIFVM3cduh6vDx5n25elvtntfKFENJ3rhvfmXMGRNJo2YAiEAhoVnQWcvuCOH3ZuQwP32GAPw9BhhoVOBDcDzgDrYu4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVSnCRA9TVsSAnZWagAA/V0QAJE4BSYwEfs/GFwde4O/\nDHC+F3zgEBzpMhHmHinvgmACXeEEqjgKWFyisiaU4Cu48VEUiYtCGDYuyuq7\nOFt2niXYAKeaRoBuLCDG6V3FB3qpIEoIV+Qd6DOPbnMeyDiSI9Q3zqAZkzaG\nVQ86prDHMZBtwk/swJNju7Jiighc2wfU8fLACTcs3EiIhOsaoqqQKRsmVfV8\noArho+ijtmB8rauFMaygBpWBBbIAso5m18DzwAp+Piqn8RmzX3Qrxr3oChus\nfKJLTECfNz7p7tiK1I74rp/WImeP97+c0DSdh/tkp2ceeOpxecYVKPVwnC8l\nrkgfkxglippy9LqaV1i42DAcc/cjtWI+otF5zif0GbGPXNFeUGXDCJ7RQCSF\n4hMeXhZZinoyb+JOSAxMlIz9E6fF3cgfnLXZz39D2PQxI4KLpyM7knMUUGqL\n6H6OZwDwMbyMP1vGtfXNeLN86qY6UIe5PVSZxDe1S+IpxUH+7d1DFbQxd48D\nBPtwFJa5x9OalHbDVz6gPFAWlTb2tII9e4ezzo7PyiqhotUhZP4RIzxVSZZt\ntMcwOpfjYg0mhgn8IgcxI6pxHiH3emceE9o7vszhUF4taTSUNJhB6ioI5v1y\nTvFEtKqAq4d1A6BF6zbZ0Crp6EGETWSYWG+NmIeeRctu9l0XN7xE8zrWBl09\nXMjq\r\n=Klg7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.4.0_1553028262610_0.2447229587805715", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c13279fabf6b916661531841a23c4b7dae29646d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.4.4.tgz", "fileCount": 5, "integrity": "sha512-jkTUyWZcTrwxu5DD4rWz6rDB5Cjdmgz6z7M7RLXOJyCUkFBawssDGcGh8M/0FTSB87avyJI1HsTwUXp9nKA1PA==", "signatures": [{"sig": "MEQCIEue7DNsUcoPh67lolp43Xgh7mcWH/qbcDC8YUXec6NmAiArRp7xqKGW8/0NoIJpA9psGMvXKeLGbY+zVr8b6NeO4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3I5CRA9TVsSAnZWagAA9RkP/j1uc6MWH0af0KwcbHw1\n4js1BDqChb45mGTb9mebPALOydvDJG+LnWDyPazA+MVa2WKG27kTxfcfhzqq\nIvatD4kBv9X0O5+fJVGF2K3wyKyYbaINPoO5iltsUxOo9D0okdhxvPXGp9dY\nRdQ7aySJqyRoUWuNVy+ZLqzoGrH6f/ZzOeTdBYDNQstC1dAI90gqxMvqn/pQ\nIUcPcxIxq7guPI41g7yKgehtlc8TUAgCt75DqOWO5i1GvVQMTDkMyL3d7oIv\nXCccxwawwHclAw9/f5wI8KsEA4dZ4ik83z/seQvTtbn5UgrnHyXPLWL2FbVW\nuJl4qsIJu2WB+PznsVD3/qymusm9hiNQw+KUETUsO7UAk42S/C/XrT4Oh3Ac\nt2qjsS9TH/bJdiIi6gULTAbtzM+lPYLoEh2IBjhCai8M3VVPWTTdP/7diTlT\nDjSoH8dGDzbag6wQ1afFdXAEGoUmu6bDqmFQq76kQmgdKaPYeinTdeI0gdDZ\nDIiiqV07tQePiypEjaloeCHjjybrbzcXlIWlOI6j6pLNtUMvT22iueUb3JKe\nWZgHaFA/ljWLvSvZ37MG3ZGLXePtsIYrh7XF8MD/hz/jTqIjI8QHBJ7OTHEm\np3TYKQmNQ1257XRu8LdT/jF24VwFbPjR8PQmkxZCrqOFeeLcErcUU0k+6/TC\n+gvd\r\n=dVY2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.4.4_1556312632356_0.23903884964123878", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.5.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a35f395e5402822f10d2119f6f8e045e3639a2ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.5.5.tgz", "fileCount": 5, "integrity": "sha512-82A3CLRRdYubkG85lKwhZB0WZoHxLGsJdux/cOVaJCJpvYFl1LVzAIFyRsa7CvXqW8rBM4Zf3Bfn8PHt5DP0Sg==", "signatures": [{"sig": "MEUCIQDx8geMFd7gWJ+D0N0vs8LvfBOaTSv2T/2KDbmVKT8weAIgOE91pjzAxyLP/JcLuQf3hzIJiraSlQyajyySlW9FPgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FWCRA9TVsSAnZWagAA/coQAKURRIGjnc2Q2RzcEeMV\nasPhOsiKmu0QRbhXvyQnXj6NRdbXvQK46h18DonyKiAB0Ghtd1X2DPwZNUsa\nr6Wa7B4X7kViDiRPIcDNdvn5/af2V9YnaZis4DqKQEgACbOCBzcEEO7XYmSo\n9j7+/E+rh7cHCgfJKO89w1P/fMog1mlCK/VeUnT/axuVfKpAQ6a7i+SAkZEo\n5n1UxBJe9rqJ/JsRCLgskGH1r9Jey4FTCiB+LAHzD2cnkagxFR/6qPpl6oJ+\nr/V8+cFNdmqCqvWosReGSfCPPoPMxlAFHfrMAFniqJVasQ6yDok4EwCp/tf7\n50iWaEsXxRPrJH4h2yFufXsIWsJMAatKktPFer5G0wk0+LNaBLuVvAQf65rD\nm0MAQW71o1I+xDY+VoXucnx1OX7Ta0/4p8xhvfjWcRWp6pUOuIwRrJyMm67Q\nfR+NGAds4Mggh151yA9tcJrWEM4oqZNaBeN28iochieiaXT/QfQPru7cLcGs\nR+fHOGSMhzK9Q7ZdqX128uh8CfybLz4rY4RMQE96HFaT+6X45xZpzdd5QW2w\nvMXPrquQZStaUkTWnrcfS5fgF72gUzMQpxATPrR+egDpNxOI5krjfYi+nhTZ\ngu9Zt7ha+HdjBAgp770tSmvua4x5YwMWyV7qcy8/NpMNuw8RWzV+JIJb587P\nfteT\r\n=FOg/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.5.5_1563398485719_0.30636851354702155", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.6.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c49e21228c4bbd4068a35667e6d951c75439b1dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.6.0.tgz", "fileCount": 5, "integrity": "sha512-tIt4E23+kw6TgL/edACZwP1OUKrjOTyMrFMLoT5IOFrfMRabCgekjqFd5o6PaAMildBu46oFkekIdMuGkkPEpA==", "signatures": [{"sig": "MEUCIQD3dv5zOFhJTE1GU9C0FvptEDUfMmHg//RQm61Z5dIbhAIgBToEgp61kXqUYPC1Lu7Et+w7cczh7SokjeiHbGmmk7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26102, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcphxCRA9TVsSAnZWagAAoR4P/1lw9UYG/x85NxUS0DKO\nPnOArvNsVucOy466X264vNe03AoNQaUBQDlmqYmE4WSus+cEqOBthV3ts0+g\ndniKGr5QE6N5C2fgxnHH7f9LhSb2/8KrsEd3+2ANLLcOBNar5PGHsjFS3fzU\nybV7ULc+m80DIJ0k01Ax5cekCEk1spYxuq43iDFWLMpv6d1548+M+7NYr3dh\nsCbFl6CCJLgZcqJTbBtjGBQvWwGQdSFb92rl0OOE7ZSacbfO0FlyhZlI/LFP\nK8TdUbAVpcur8zqFVdSoywSbIWSVvVdd9Ep+h/54Ys1eYMp5SBpEUtYnZ9Fg\ntli9F7Ba5I5HaH/o5F+KG2b/wrP4qeVhKv2mWooOKo4dnoTbOwbTScw6iGe+\nJBfckVWYb9B/ptxh70naPztCi+cmS4OArxVpSr4NBXf7bReOj66SSgtvy5y7\nPsjl4sfrchAc934P0SCxHMONem07I76X8uazyFH1KIhTPLp/gnjvirrAunS2\nU25NjKtMEnQnrVO4xZ6uH2tnEsKL9WJAx+Fm2iGlJo191mapm7qtzOXhRZQ9\nqt7IhVv++ad33l4eUgyZUa0yk2SzxL9v4S6aeUCxDGaymzM66rInXmXMSDEs\nHXxX4/KHqfKkLssbXtuZyJ4z+nX0hQvHx7g9AJjd8nxx+21TzMxQpFdL6BSg\ndGc3\r\n=tSUd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.6.0_1567791216602_0.059477671226507045", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "@babel/plugin-transform-block-scoping", "version": "7.6.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.6.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "96c33ab97a9ae500cc6f5b19e04a7e6553360a79", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.6.2.tgz", "fileCount": 5, "integrity": "sha512-zZT8ivau9LOQQaOGC7bQLQOT4XPkPXgN2ERfUgk1X8ql+mVkLc4E8eKk+FO3o0154kxzqenWCorfmEXpEZcrSQ==", "signatures": [{"sig": "MEQCIGnUKOMycV/VLrulgpN13Y8OREzCdX1sLMeEw54JHEOQAiA644PB6FQmpgHyMT8UZatFV6/pnU1isjQ3IH7lljnxJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdfCRA9TVsSAnZWagAAG/0QAKSsZLzKsx+z8K1GC7eN\nBLdnGZfr9liP7KUTd3AlBzvXbRBrJ/Jj6krLYQJZQPXXVntE6uVyxCqe0FIu\nC68zfrZdjM5wZ6oGgxlLQ/w8F4p+VA4ApnDgEVwAhEj5BHifNkAq+jtPt9pe\n1pjfiwHR/GhoeZidgsPRhIO+lSJAOZk2Wq2hO2cxyeQiTufKmQEkkaqLs2A0\nfTYcF2TS23omlzreFOyiAG5yRPZKMiQ21f2FHTBwYd/32oQENT0M3boxscpb\nMvvSWbAMo8RZqCVV5koqIPzXgDMy0B75xlwwNwpO3pQsfR0Vv3VxBhmRIA9E\n+U8lIYUITpvmX7V0XYJthEI7ajwrwNobNAJmOsELCW0717cT9XsjL0moPrjf\nebISagtsM3SYfZJe5MK0seYZv8epqlu59wWrYs1QrGYKThhO7nc6yPuPaa+u\nTYM2++wNs/hxAo1MxO398Hqt9A8qfpYSpT/+eeB+Om9fQA8KR/oZKOXfGpXg\nz1bppBdSjW0SwL79uHv30HchrxRFC6qKZda8aBoHcJyIfzVJMj3svSxNDJbo\ngKVKxmfQxd5t9TV3Xqw9IoBV/R4Iif/ki4Yf80G3/A6O4JLZCdmg0EXV9VU7\nmKz6yYOHGIA5G4x5q/uda8TSudAfl3JJFeTSFrrofqHvxTpCOA5wNK31dHT1\naued\r\n=FyKN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b9cb4af953afb1a5aeed9b18526192ab15bb45c1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.6.2_1569273694886_0.16888405760108705", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.6.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.6.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6e854e51fbbaa84351b15d4ddafe342f3a5d542a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.6.3.tgz", "fileCount": 5, "integrity": "sha512-7hvrg75dubcO3ZI2rjYTzUrEuh1E9IyDEhhB6qfcooxhDA33xx2MasuLVgdxzcP6R/lipAC6n9ub9maNW6RKdw==", "signatures": [{"sig": "MEUCIQCaxKKjLjhLDRkYbgSz2VGk6qQsVNlkFzNA3PTIRQ+SBQIgJtH8sGW8mSS9YFPV19ReFyYmXIaKBxRssNiCiFQmNDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhMCRA9TVsSAnZWagAAMv0P/RRcpvUSYkuJRUBDpT6j\ny9v+MoI00OWRujIk4iSKjHad8nwekpjnlqZWpBbS9EvLUACBvwkWN2Lu+OMa\nF0CPMFI+AE5cK8QHkaxDoDc+deDgDlu0PLWE/RUcU3G7VqWK7qspndcLOB8x\nUAORTVAIjHuABPtx9Jk98FJyONIEm7f81UxNx4gbJa8KBWNOH+4m/Vvdn9X0\nEeMPJef7KJHu7+krmdr7AXyNQPgoa2i9v5mxU3w641BSVBb4wCVtHVa5UKSO\n3BtwGev2qTn8Bt88rObuIKWxBqKfG2qFYbBkm7Nu5QxMEKyjKEZoCF1p34GE\nQnkNCsAJGwNbIfUnhmxgrwPzUi/2oFx9Pg4oZ8FyE9eczS6KLsZ+QmEBfbNS\npNgHU/QvBGCV1HMv2vuJ1HyTTetd2vqFxSvYX3h/0cYSsOvIE2SzbLDQc+61\nRxLd5lv8+ArF4lDAel+FTKBlotmucwjm08q/VDP89cyWtyVxXaN4i3d5SoEq\nXkdobA1qArNTTKv99F7CpMpRKHMIIWbsjMR7LPBm6oSzWQhbIJKrSKa1/V1I\naIG87XevYJ9kSSte+JUEuCcCbVbvlZnBN5uaZ8xzx89kWPLiFgwDIeNS8eNK\nJd/0sLwjPvIB/yqGu4NAsT3eDVL4w33EyBx9fRI5+YZIPf/6FKKGvqJ/jmTb\nC9M3\r\n=Zb4m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.6.3_1570564171614_0.7039624102936888", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "200aad0dcd6bb80372f94d9e628ea062c58bf224", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.7.4.tgz", "fileCount": 5, "integrity": "sha512-2VBe9u0G+fDt9B5OV5DQH4KBf5DoiNkwFKOz0TCvBWvdAN2rOykCTkrL+jTLxfCAm76l9Qo5OqL7HBOx2dWggg==", "signatures": [{"sig": "MEUCIQDUPpUEOcyepayhpMzplOhYyqlj3C1mcujkjB+oJVq8cgIgN6MEpsSBTzAeTcYSle5lTbVXgY5VXOQ6IcdUyz2+nMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25566, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/7CRA9TVsSAnZWagAAhSQP/RdLp0EjI/Mgft/fs7Sy\nsRtPgaLKD3KawwB3qAlemcaz5wG45DPYIGWK1mz3HFpuB4FFUHUHD8H5hjt/\nETPXKIZz16YHLSFAIuq2IIL8wlRBuInANSfNPsmwMuCyubPDphtgdVjzQX34\nSk8ekL6GZLPECuHSJCWqJ5F+vjKlYqMewVv4zPrWCNQ8aBt7pNTEGomo8sUZ\nOjkEXeHDHJEHfhT/LV7zRz2L3zWxyh5dfmHCZ2tNShLycSxvvHhAT8olgyfJ\nkZKhds2wxF9urf6mW33Pqtri6kq3xQx0zoClfYzTHwhkjdYTXR+sFVvIEcMz\niN/ZdshllFV6X4UOV97gdFTwm9GktpFGU8uLbUGgX9bfwxq7MBpJ0SKVjT7c\nqmZagpDay7uEE1kd0K3f2KBK8nm4zy0pt0smTrt5dI7BQmnXkt0fuc/S/r4p\nIrGrnVPZCWUnLY4HdBd97gNZgNp1i1/8N0e58Ic/wwJ7I05mUm+ZYdYqx7pt\na9XsCXefxNVWscZfjRGs8HQH5sjgFs4vCQG9m9aTY/nFxTKEfyhHxwgHVsw0\nw6115IjSDtKNLt28gSAx47ctJISu+iijmyRHBGRoUCKN3UENuiGDWB9FynE3\nQWnlHqkt8nhpJIZkbeaUDwfwkmuKMYWllrl34fkj0alaVpVnR1LFc2P9+0qY\n3Mbn\r\n=aBOc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.7.4_1574465530771_0.7344639738920966", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f6a81bc8c76dbbd202b718cb9e681a27f1d0af8f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.8.0.tgz", "fileCount": 5, "integrity": "sha512-FKTK4hzg7W950Yu9iqMl12WBixCmusMc5HBt3/ErvpFLnvr3/6mu/EBTZoCEJ0mw/lQUDfU01vTcZY9oEahlMg==", "signatures": [{"sig": "MEQCIHLELjFzLfw8kkct3gaIyt3/sq3DnNIFKHD2EMU0mRE4AiBN7Abl4xQPobXA/k+fFLEqJi4zcS41y3WZqjP4Og+3bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVhCRA9TVsSAnZWagAAZOgQAIQgoedJn0zbKxIQw6nd\nFjtBhUugN12F9+NJdvTLUZm40ajygc6PfeGj+GCFsXV6N4KEtcMRi40DZI9b\nMC8WflYUqNYi3N3OdS8GhSiMuhSEBo7OXc6PZ2HwcNTuwpJLSCO30JZ9uAQZ\ndnIvrjaGu4dLTKYDfT5GjGBWq9UDOx7QpDLVg6BC71LIL7GF5CX/MR06eT4v\nOpW+uYeb1KT/PeBBgZ9X/gL9GY8tB6iAqJmRjd/ix9wAlMSHISuEYzZEqDq3\nG1YMFEC2w3esov9T2SzfVxB5JtJ/KsBiFO1Gssc7QwSP1I6hpavVTXFOK1u5\nTorlwI20sN3PBWmP5cinipU3wyA6DxZld4VGZtr60mZ+XhuPCB3ttfhyg8mx\nsziTGp21mT8Ts5dv5U4iotIWkfvm1WKks6xOCAp5xt3faWcJxTplSC2sT18W\nOlFilLhPQ+bCr59gQ3Mg/WiQk5a5o5bzGQ5g9+Y8qGvk7msgo5gaYm2r5v2L\njA394zQVv+GGADVJmyUXdhgdwZFKs/WJ+iolkeKYoM/WSp9RJvVNlGEUSTnC\n4Wh//k29Z18Kq/J4Ixdm1taRcWl8C8krzuISuznyI0A2EtE73bkkI6bdV3VY\nJlh7/LpkyBF7G75ASVw0utZxTsJ3gBnZAyWyA4apWh3Nhj4sVQZSMSth3uQ0\n6hKn\r\n=xLOL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.8.0_1578788192945_0.7458322201175147", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "97d35dab66857a437c166358b91d09050c868f3a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.8.3.tgz", "fileCount": 5, "integrity": "sha512-pGnYfm7RNRgYRi7bids5bHluENHqJhrV4bCZRwc5GamaWIIs07N4rZECcmJL6ZClwjDz1GbdMZFtPs27hTB06w==", "signatures": [{"sig": "MEUCIEKgO/2iAzbMYzyQZNiPLgnsBHJ8T408KdZQ36rvxMeKAiEA27CJ3CzWRX8uJKQhuTkmQt1z+NHpLUqLzV4u21s/q9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQHCRA9TVsSAnZWagAAieMQAKR2zwDqC3xw9WFikrof\nMBaLa5hd/HiXcm0fCnmjpRAxlM0Bz5zLwh7j3dBBhJVsqaJxoiypD/z38nx4\nAymvFSWjrZxh0IeP7k8+v4/PXZhnkMWVk5E/gFc/im4yJ5kxh2N5f915ZgF0\nkifuUpXdA22FYCRwrO1BGZ8m/xuHqPtNkvRaXPOCjW/oC0oLilGu+tfFfeXY\nLX67ca8Fh721de+l+y9aK8Er38UX/FAGrm7E+0YHAy2XZSB/MEIbBbrNIMia\nwugqiL6n5ElGj7gVDtWRl0VDm39n+rNevjCdqncYrp5U4+rDzN3BkYPVQqHV\nb5RE2uaElUFHCTSkCHr+AsYz/HtFOIz14DDwcHWXIlJBmeR/87+723ZOCM4s\n/Kg5iFv+jymjvt9zjKOMDnxatbcy5U/qEmcBZR2xyRvTk0uEzDVChzDe2Iy6\nkcaIsdB+IbKOIi1e4HHS1dVwkcn2seYtYJLiumUSbhr9brPgKPpiivO5MtQ8\n255Do6xEL+HYajfZnWwVpLpmrJtH1fIiTjfOgU6HimnUFWdhLfxoC7U7J/3A\nMMzRpYisNfXPLWZi7fHScJVUxn0n7aeAxwg5c1LlEtOruw5rc3LbnwS0ZggB\niFSncL2tdkzUwgljeIvtNAFqBuVmaZhNUZYViN4Z6zNirKOp6jAwKSRorAGH\nXBEC\r\n=5bGE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.8.3_1578951687303_0.2289082467453709", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5d7aa0cf921ec91bdc97c9b311bf1fce0ea979b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.10.0.tgz", "fileCount": 5, "integrity": "sha512-AoMn0D3nLG9i71useuBrZZTnHbjnhcaTXCckUtOx3JPuhGGJdOUYMwOV9niPJ+nZCk52dfLLqbmV3pBMCRQLNw==", "signatures": [{"sig": "MEYCIQCXq8GBVBn/DdwOti6JXG7sG6Yswnoq4AWz7hh/YOU9qwIhAMnpYuDSpolZJkQ9YYbb5TFoIQ2Q5/X1CaY5DrlnZCEk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY13CRA9TVsSAnZWagAAuHMQAJnCnING0Gw8qlI5yl2j\nvxi+/ms403JVzPCocf6m+zKg1c/PYlr+46QZL6GXGmixdVQyVhiGgXKPK1Qy\n+xg34zF6Ti44bGN301i7zpVIgj4pzmjXIN3TMZG2tgA1E8PSfAE4P25Jf0BK\n/ajwmyoig7wmJUZuQ90XKSZqp7leS7XQkEjM6rRAEpEpVT+r4Uhwo5mUZGC/\nkGoYz9ODrtIRDXy4rMk2htP617PimYZMjgPV/W66eXGfCZLCSWnuoRDsLpqm\ngoOeE/Eea7/SeYpOFnJF8gx3YJXo7wzU4eO+kjIZCRIKFo1IPicGGvZ5Dcuh\nB8sGKEFZp34AIOStSY7qj6qwHAp/WjQGlmn/IQojtDDbyTHYL9SyvH5XHSES\nn7rLrQZUgbwH/VgDwPtZDz/dUChZ8kiibECrVgfDIkpdHXtqmzhfScU5H61J\nAC8WSe3ZWYn200U9cV0nFRYGHW1TbPnWDCs3OaQu2p8TbzE0WVrl9/Q72NPe\nWMTGbIPusqslB2aEDI4/AK5u2oMO1Lyeh8VpZm/wtVHmlcn1hPPknrfFiDv7\n+nMTO12qsNeCK1k4zdHqZ4wtcUiuMxmHgPPCQMM+vZT3TQrqb27VIEk3ehCs\nNBGw13JU0/RZ8I8eIcUOx+m0IUTHSshPfaw9sfANr3Y/7g2bvUiWHYRoNTCE\n0KyJ\r\n=NaPA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-block-scoping", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.10.0_1590529398591_0.42510636673629065", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "47092d89ca345811451cd0dc5d91605982705d5e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.10.1.tgz", "fileCount": 5, "integrity": "sha512-8bpWG6TtF5akdhIm/uWTyjHqENpy13Fx8chg7pFH875aNLwX8JxIxqm08gmAT+Whe6AOmaTeLPe7dpLbXt+xUw==", "signatures": [{"sig": "MEUCIFIPQRN7Pdh3cq/I9jV0d4bBTBKz5ut/DVAFP2aCcQ+SAiEA1Svvmq6c7IhR8LxG2WcdyKYYY1bzfUNiCxm9gBbwGgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSUCRA9TVsSAnZWagAA064P/3uyCagLpzrDibMpB5y3\ntzW8NwODO+EljQG6aG3OOAQv+mDvHwh8gY1L7CYa9tHL6vC9vZxwR4wexdJT\nQ8b5ropiohffBa8mjVANvlMlcsMuEONvKJ9VFifi0BrF+ck+63dHplwu9pTD\ne9noVMggwKbVGiIScz5PDodTO+/zbCfsIhA6THA46YBmoevh+qhkyGodfY+f\ndid3Tx9aRBDnDGqX5Ani1vDpfZ8IDzlhvHCFOAy6G865s9T1TnXkckMSGyGm\nk5dpLck1T9P8m+NxdCZo7hk6+8rtcUQuVLY9BY4eGtDu4u3GGq796pb4TRtM\nSv6u4NeqzkIYUFMndp0uYxhbddX01+UaVm+ehor2dKBxqZz4KFIQ89XpNRHo\nJXpCRKtQVY5p1BrKQD2WKQNzx4OwXfkSK7PnPRockkIQ9e5zmA2Hg/O7r7mi\ne0yVngCjK318N0CWWR6X+8ZUbqUSFmHl2rdpkC3rNuErhY8kzuc4KaaFkL0e\nSWIh5PMQXwOjL9Hh+abQHq/h8GqcGjtbsYovZpviyH6m/8F7VR9Sq96MKGQo\nvdWRaQmeNg8IgBvlgMWDho7lWD/Y6RXOU5HcqnLevHzHUe5VHoRj2P3W8MbO\nFb2kP087G2/9iR+Noj7gOaJ4mF7VOhDigBL104KH953GZFIOBRyCUQQA/D1s\njQvy\r\n=UulK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.10.1_1590617235780_0.9146572712846266", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a670d1364bb5019a621b9ea2001482876d734787", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.10.4.tgz", "fileCount": 5, "integrity": "sha512-J3b5CluMg3hPUii2onJDRiaVbPtKFPLEaV5dOPY5OeAbDi1iU/UbbFFTgwb7WnanaDy7bjU35kc26W3eM5Qa0A==", "signatures": [{"sig": "MEQCIF2gdarVk0NYt9GbPlW/ooQIqxUMf+GwUobHKt7eyo1WAiANB6fsBt3ft/q5Znuu5v8Ir9PV2Qw+RhlHHLkg5MLEgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zobCRA9TVsSAnZWagAAy48P/iz1EUIjhTLUHI0Dybe7\nWS/OJeQNdPor+FH/ElfJLCEQWuBOG61GaCJb1wchw0EbkTWuvjNosevhqV2Y\nRXlkGoRsw1MGRv6h6GWE1bFF6Vx7X/HSkrNh1NJE3wDYYriedMoJefb8kUkT\nNsAcy86vjjOjnEAA8MUVXIIIwEt1wRqGxdhzMrr1dr7j+siTqq8J8PqAJkhQ\nA6oq1ZUt7JZGnrJjvkn8T20ltK4DPZI3yh1L7M8HWc0JGGhU44Sod5fmNgNg\n5UJDBH9giQ058IdlcojiI3SVdJGh8/fZbRqCEJbmnzPFnqcLhoJO0ERYR3nK\niC2r5BTo0kCedqG7CxyCQdI8GOKTb+aNKzeiuXNUSBhX03qHzXoM6V2fpBOb\n3iSO7a2faa0PAGHnQfogBqTb/DX7OAr5NSqwglNHCrj4oD9KerHJukkIL7pL\ndDsIPU/xiQbjrvnfdxMKcKPnfCODjBZfrfF1MkQlPvcU/PdW0GTFMojrJpN+\n5ZqBob5tqBKAQTvHS1cs5pomrHOywIwvD+pwxMTsvw3u8f7yQyuix3JiFVy7\nYf2UlgV3k/RUoEUEsTr2JgfsrEu4vdH2MPPWwPEnVqhgQ9F3DwABwAPV/Mjj\nzGnFCdP/+TJgdJCMDjQFugoe01rL8TVNy+AtjYbRtHVzYsSsn/cpdXSpc9Hu\nzh56\r\n=M2zm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"lodash": "^4.17.13", "@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.10.4_1593522715073_0.4012875420924302", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.10.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b81b8aafefbfe68f0f65f7ef397b9ece68a6037d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.10.5.tgz", "fileCount": 5, "integrity": "sha512-6Ycw3hjpQti0qssQcA6AMSFDHeNJ++R6dIMnpRqUjFeBBTmTDPa8zgF90OVfTvAo11mXZTlVUViY1g8ffrURLg==", "signatures": [{"sig": "MEYCIQCECyD7VapKYZMfczyklxMfjPL+/25t8OtgksKYVWTujQIhAK3OmPerVAoHTVp/YBwoHr2EtZzoq3fzg+BlyHW/tARU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbLCRA9TVsSAnZWagAAitUP/05nP4xhqOqzzxCQOOQR\nS3WI1uaIu5tQf4KcD4huAFMmDcJG2qyuZ4yq+Qum4QzbO/nyA/V2KmXzKj5k\n2lLNU9cpT0fmsKO6bcKb3dhj9ZPzAHqZtbG+e6rgXYvL/VvQsCJPvvf0OzhW\nshUNd2OSysQJrT8DnfQqm60k/UVggLP1q/Py9ktb0HugZ9brVxPXI5GB54lr\neHhudauNkyGOCZFQsF3vZLUS/FscBqc1lRK/AJGtFT/c+TC+sH8ceBtW/xuq\ndVOu6uK5e6qhC2RAmrWc2ZwrS8qOGhdgB0+sADYysKc4hJwpS3FAZyjn9lQc\nPJtE05SG43/nN6K9oGfg8dUWEB1rf5iWvvzP0eFl1pUwX0GOCghuENu3cS1o\n8wmgRbhuaUu4C3oBpgW8rkEVn2MAstg4nHMFoDf4wFUzNzg+JN9LBCYj1bOs\nWsV7leEtJ1nYZTnSDrGR0qIdnEwn4fDMJBx5CsKkg58mBeD2YI+LlSpnE/Ha\nWWGKaecmfq0CBeC4huRqNVWUqQpaIiZgQVNgBikgJv01Yc8n7ACdwuPUqepR\nwNrySm3Z9fAY+/0/Is8enFbJrC+VMv+aunmH5swEGlzDTRLU6tqxfpt9v58v\nFVFhIa6mB5lkVc940l/Lhq8ctUjtfxkClf6QhS6XCHTUGGe3CShUIxqkfadS\nMrqv\r\n=y3Mj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.10.5_1594750666638_0.5757035527660308", "host": "s3://npm-registry-packages"}}, "7.11.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.11.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.11.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5b7efe98852bef8d652c0b28144cd93a9e4b5215", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.11.1.tgz", "fileCount": 5, "integrity": "sha512-00dYeDE0EVEHuuM+26+0w/SCL0BH2Qy7LwHuI4Hi4MH5gkC8/AqMN5uWFJIsoXZrAphiMm1iXzBw6L2T+eA0ew==", "signatures": [{"sig": "MEUCIH4iId1lforIJOQMjJIwpoMl6Izo3W7dmJ7dvNqD/ADcAiEA98EqgG3ff0Kimw8Ja/b8xVuQkdB378LxHrvN+XF3Rgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKdu7CRA9TVsSAnZWagAAcM8P/i1LqlxGREPGiqGnUe6Q\nXSsZLlg8x9a/G8JNhdgSsXgFfxJIQZ79XyX2UNbD3dYyCkF9JBOWrjjRJh3R\nyQ9OIOcyqTSSqkfTSMJ1LMfeJrv4wclVnxlJybWBoR0pgqLQw2++fQJxKQeW\n57hG8b1kbpqUb98ABcT2Cx9ofzDrmjW2tI1H5ZQ3ru6L03j1pSp+gD1jN1J0\nIQbYpUAaplR1/x5kb83yId0N3ABvx+cYk0wPzKoc0Haz0ztaUvv4r33vJSsj\nzLvyYd0WkRH+eF6IPK8WWr9N25DkSS5sgXGV3viebWJjom4yzxnQULEdvmHA\noA9oVniCN/Ewik/hyaDdWLUuxfhRerDTMZZgnauni7mLPVcEJZtd5E++V9RO\nek3fhts4T4PvUcS3fiRSVkgkZqnb8yEh7XvKfMy6+zPHUUIBzZeti1YOT3ha\ntaqpCmETkL9W1hhjygqE8jbg/kI6w47+6tuUHDoWe620Y1SkK56XNNjpiINq\n1IIfx8FmJJLyhIvilXcvgf5SpIIO9ETcfoIZztlV6pfBcihxLUBBpfX4qMhC\nOQnUtYkxWTaNCI77DWRmnOg1rhsfHRYl2v5aJfuHddGdxjcCgEis3FF8OCB0\nafBMN8AZoDdZmPW26WW1Me/y96DZ76jKZa1SuNxMU6ghd5OqUUp0d+jjzH0j\nIBTS\r\n=4So+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "953ae82159b67b4487f837a17a1b8d5e305a8e5c", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "_npmVersion": "lerna/3.19.0/node@v14.7.0+x64 (darwin)", "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.1", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.11.1_1596578746698_0.021405839310361774", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "f0ee727874b42a208a48a586b84c3d222c2bbef1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.12.1.tgz", "fileCount": 5, "integrity": "sha512-zJyAC9sZdE60r1nVQHblcfCj29Dh2Y0DOvlMkcqSo0ckqjiCwNiUezUKw+RjOCwGfpLRwnAeQ2XlLpsnGkvv9w==", "signatures": [{"sig": "MEUCIQD20JdLiW5hBKi9nkR/OnoDnDiGkx66C/c9C24Glov0DQIgd3XCkmGMgX3LxmaDm44n7GvaIQMDbH9bAnF76cXFPV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM++CRA9TVsSAnZWagAAi/oP/iBhoUONhzaIfpQemOcD\nmv4SRGMf3pSgZslXgonO4gwvuhMOvMIFLHb0uXlV0z3tWI9cUeJtsJ+LrTuT\njz9zIwMeQhvk1nUk9IRhl6xn5/7YvkediLscvQDN1dI+Ga+fp7iv/jQCxm/G\nNolf+KOKTYzDAN17vdeTJ69/rZIYDu+8KfnlF/vGQBipnojfVfPBMK8P+bU6\nOnO7M0KgRngIY02OLItqkW8PxYSBQdw84e1Yxg/gRDe2uf6bNKy4d1ckc3xt\nMUziWd9oEUDjW4gNT8nZX+5bUDh+0Dw4ztQbP5vVTgxVwmjkjxOIVwfyToS1\n99LBZZ3XYsu31WYVyXXHHSxdOnvXf0EwL8DdR5k607WTl8lb113F/ZZcpn1d\nXwxCXSE+swhBMZwCs9XsTuqM7cw7j/CPbMYx84tHb41Tuona8Cc6YPp7jcXX\nck1MCIlV6XWClgLF/YqK7oriISO4jjNbbdh9dXEX3JODNRmOwb2ZeHl668Lr\nqqicXqGv2s8fiM0GR9hpGJSSficozapjaX7H0pJpzS+NIuovmvoeckZWOwjm\n5A+AHf+z4yAXQDJU3A/3ruzF2NzzVrNCkpNcbFfJJTh4gbXSFhUKNkYTVeqH\nivCVKcNyx8SjKCeZl8zrXwJ1HGH2RD8F8SBNRLkJUqAN1Bs4Fls7k8XNl/2y\nHk27\r\n=FwIU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.12.1_1602801597714_0.3354435497754331", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/plugin-transform-block-scoping", "version": "7.12.11", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "83ae92a104dbb93a7d6c6dd1844f351083c46b4f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.12.11.tgz", "fileCount": 5, "integrity": "sha512-atR1Rxc3hM+VPg/NvNvfYw0npQEAcHuJ+MGZnFn6h3bo+1U3BWXMdFMlvVRApBTWKQMX7SOwRJZA5FBF/JQbvA==", "signatures": [{"sig": "MEUCIBy1FrsjBpZzM4HK1F8R+AKWhaKwlyvXUI2ngfXm+X4lAiEAz/VEvzLE3Ns7keDhwZTL+6JxvFAiiMSJR7pdutoH69o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3XCRA9TVsSAnZWagAAiZ8P/imAxwWwg0BnTT2E/QxS\nrQf1PYZEGd1LeLJ49d5qLhyKEZ1hxAb235zIcyTAOU/RZgULZrYSA+/ovB6l\nItT+mJ41UweeVjocYV8e2FRbkLZCQQrmKe5yip5PRbwSmD1wKp+lWI8Cjvgu\ns/sDpCmDXQXfs8AMvffyVWX18Ds1/5193kC4Gq7imjhBq+QKHG1Mma4uqU5k\n26L8FqUJMEnaqlgaV9zrWJemN5IHXhae+cRcYz8gj3A3cRF98rehOqfrxgaf\n7onMOsSLTfCuYmSs5Q7mCyVg4NUehcUCemmv+5Q7FtMfLah7MajvWYqlBtZl\nkuUdvdEmbdHpBxsIv3blyWbW0rS6b2/R5DVoY0WH5iWGU9MHhX4oCrQdLuy7\nrohY1hSqfjHkhH+UyeVgmSskFEMGJDceQ0v04AqQiEyOt2rRpItnVHfhqf2/\n9maqU9MQcsyfsFi+xx8rnAgSBoCeCfvo1rdXED7jhrSc+rLQE9NmRiYepTmr\n5n07QxOkzmpqiIJgb3IoHTM4kv+ukvPRvUy9bY9lmwCGPRTHsRBmyOSWnkjS\njpVrnyG2TU8r38daI0f42tyK45k2B23jYYua/EeYOc98kB+Vt9lcA8iZu8tA\nCCRt2Bfo66OFbCFq+OIGxAFVV+HPsxLCyqc+HEehlQF9P9fJj76z47r1WwTu\n5lv7\r\n=9+ku\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.12.11_1608076758686_0.14288781520617366", "host": "s3://npm-registry-packages"}}, "7.12.12": {"name": "@babel/plugin-transform-block-scoping", "version": "7.12.12", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.12.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d93a567a152c22aea3b1929bb118d1d0a175cdca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.12.12.tgz", "fileCount": 5, "integrity": "sha512-VOEPQ/ExOVqbukuP7BYJtI5ZxxsmegTwzZ04j1aF0dkSypGo9XpDHuOrABsJu+ie+penpSJheDJ11x1BEZNiyQ==", "signatures": [{"sig": "MEQCIBpFGzVxFtFbgXakJtx39ROlk1n3RVXcE4axK0AAXFJaAiBQWOmGcMSowF068/uhfQIXrxsD5hsLHahVKz07DDZlLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf406eCRA9TVsSAnZWagAANf8QAJNK3CqwDjwFLmfOUpL6\nFzlhz0QbCnK+PkQhOjG2yE4rDYtdVvsMD4uBaNmHy+hyZ+Hj1CgZdQrvl/j8\nk4nMFEWrdYZJyE2gOYGfz4mNSektaWhRhST5W5BYzdq67oAzk2Z4DGvLN0Ke\nKNh6Z2gsE+VC+0FaHXYD4rjcIuVhshM8se1eDvkUVNdwFBbV069hgG1lkqjh\n8O9RJi77BZQcxUJu+Ow4k5SqG84oDs4FIn2nysBn1vRobMqnbjQUxe/GPCXi\nPU5RiTlVQErZ3bT4b//1uYlyjjRGyPHDY1YDEieEh1/MXe770UcwAuxBCi8F\n2q/F9ThIGdngd9bpzcX2gJelN0+WK55b5fnC76IX/925EVX1jD26LAzG8kUX\nWlM3nMmmuryJqJ2vHN4LJmnBD+W8jO54GyM1ie3HK8a6M/QHpmjXJkUImh0t\npPxtBLSG4C9XM2G2FReRKS22gR2zhMRXqb04leb42g7LPPqoz3lkpN8Yt9w0\nI+zeY97EhzewdWn1xQTjNMt7OkuOudN97SI0KeV5v/KDBA+TOYuHY3ykHgyS\nNbYdpzjCUK9NOEL8YnUIA+lS+GgCKUCeOsQAW7M3LWm+RgRl3Q+VCMIwaFJ3\n3Gs7y0nDRcZfrCxHC0bEm/98LC0EgFssKmjao12bGZuwvWIncTKkUjXQifQh\nHMty\r\n=Vz7J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.12.12_1608732318019_0.49201556552794945", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-block-scoping", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "f36e55076d06f41dfd78557ea039c1b581642e61", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.12.13.tgz", "fileCount": 5, "integrity": "sha512-Pxwe0iqWJX4fOOM2kEZeUuAxHMWb9nK+9oh5d11bsLoB0xMg+mkDpt0eYuDZB7ETrY9bbcVlKUGTOGWy7BHsMQ==", "signatures": [{"sig": "MEUCIQC+FeqZmk71LHO4LGdGmhwTBif8bI5giW/eJsOWwtN6bQIgVrsVSTfhJsDCrKIRLTD51vVolfd7cAWqmXcjPiXH/e0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgYCRA9TVsSAnZWagAAHCQP/0WVvLT1yr/+P8+TP5qj\nT4VX67Y8/xCxV3QncCg1DBlhRb2o8xlcDNhYgYnNCBsYlnQmVYHwZsHGyLjs\nob/GBLddXHjjRB+ovaBRLGbp7IK0td7aX5C/7yOZTntKCx1vq38JA3IfNAWv\ns8Ath+Xtn/f1jcNfRZBa8CwCY7B/zyWssHxUN+meg5ctJZ4Reikcjj121t5D\nrcCAy6uB/Mei400QR06OrX+4CpBfQyBLd3mw4xG6onZGECgH3GjNGVQCWQc/\nhKtraro+qSFswOGNCEib1QnjHpPTUjAQDExDs6y0Bc5QlCCHLIKIlCk6TvWq\npgJZqguIybJbhML0kAvZ5Q8I9IQkhnDi6jxAYSag2yczwA4Wv/Np0sqSD+fd\nvgN5H1U+IEXHCYhhPRkksgm9F7XCIptIVM8rPcAAZkOs3vE55JjQz0kRKTWX\nuLe8OfEWAQrZ2R27eA61WtLZLX+lnJpWjQqSJ/sLCHq+mgT4eImBOXPBxKLW\nKwbbPaORVS+zbPHWBrCznLhzCKHqB9bPAuRKi3+2Tfhn06ifXaT3NUFeSxBA\nH+lhanaHY9/l8T7z0MC/4BAkZfxjn9YcPoPLBuWSkABhK+RdfdeukJAqqPn3\nL/n3PfM18oJXam+PCtrfm2TeQh4FnrVQKvRUNuYJ4ED6k3bTuj+Xq2/DdD8h\npssd\r\n=ToPG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.12.13_1612314647560_0.4298999295962489", "host": "s3://npm-registry-packages"}}, "7.13.16": {"name": "@babel/plugin-transform-block-scoping", "version": "7.13.16", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.13.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "a9c0f10794855c63b1d629914c7dcfeddd185892", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.13.16.tgz", "fileCount": 5, "integrity": "sha512-ad3PHUxGnfWF4Efd3qFuznEtZKoBp0spS+DgqzVzRPV7urEBvPLue3y2j80w4Jf2YLzZHj8TOv/Lmvdmh3b2xg==", "signatures": [{"sig": "MEUCIGUWeNWQWSzwKTJ35xGNfc3/FAvrgxKvupYRU5X0JAGnAiEAsvrsl3NBPLTph7nmugMIoy1rzLxlO76BqZAhOdrmkis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfrkkCRA9TVsSAnZWagAAEicP/2wrYznIFZ0lfsYMofK2\n8tLdNTnloxXeNJ0G22h3vnRHFN+C7b3mytNNRzXJ0U+6rQd2wZR3Ua0Rm7cc\nmilxy5L9MVxk+ESFuqMpqwRqTcqFnGSxY1Jojmrf5gXHT1sguRrFb2Z1SAJL\nzbcMQhctpPriqags1qAWDir98dPxQAuTYyf4iHJ1sOkkgXJ/EoLnsO2f17jl\nAZ0uJA8eZ/PwjdIn8xP8TQI9+OI3VIHPLw/Q2b14pJq9XiahaeeEqRscfbpm\n8eOjRrN5Pz4xGZCREa30B/CGiH0rMREeE0eLGbhMNJ1bxamdVJdW4rKdjfOY\njNW840MI900sO0gKXX3H0Qw5KwaOfHeF+9xd7bRZ3S7aIm+HLE8kE9xFHv85\nq7NtkhsfBjxFkkC9QpHYMJx2k+3nBmFxF+3YxR3YR8vXkw1Mvn5wozQfwrms\nBbBU7HIopFX+U2dEvgqdIG6WbsKz9PM0n5os2Wd5UHC48JAAg8AgY4OtUsui\ns8PkQyBqmpwrVa0+s3wFT9cJfs2GuyS2BIwYmy5uqzrQGnqooSo0xkbmcj+9\ng7u4TXq7TdskQ5L/dK7r+Mjcdw4alxyQSRQJn0fwPV2mUuPeEZL8l8T7kAR4\nAYPEsGJ6P9ab/Xac2aLgdVA2HC9+0qw5NKaInQqdvulN88hF/J+4mJHamc9t\nUprw\r\n=5m2+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.16", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.13.16_1618917668401_0.8512958635682186", "host": "s3://npm-registry-packages"}}, "7.14.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.14.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.14.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "ac1b3a8e3d8cbb31efc6b9be2f74eb9823b74ab2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.14.1.tgz", "fileCount": 5, "integrity": "sha512-2mQXd0zBrwfp0O1moWIhPpEeTKDvxyHcnma3JATVP1l+CctWBuot6OJG8LQ4DnBj4ZZPSmlb/fm4mu47EOAnVA==", "signatures": [{"sig": "MEYCIQDsL/2rcv6JafuIvEinTpiqc2Yyiyl1h9ExMs7iSkwDOgIhAKrIxvlx5O8bMNW/XCCJYnAWY9vwzsvbaVPG0mhdYzo1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkKmvCRA9TVsSAnZWagAAF38QAJs4KaW/0Mln/vq8Vo45\n5TAG2Ihb7bBvIcjr60ZxlpCZCwyvR3hL503EP+qAyAWRjQgFccBvOd4XXOf0\nvEl4m6RgEZNr7DhYdtDzewwYQP1G0RLGL1oWdxdfnXizkMFiOPzxFT28ntWS\nvOQM650MYlBnxmxiVU2ftXxQRGklxeV/qbIJ3YrNa6oS6acxJurFlLlU+APA\ndn4gM+X6ohPWEsy919Y2RVYjXZl8PPib3WJwXs8er7mzZ2hPAiPiE/kqzhtR\ne+l19OvUKImAvcONSaBLtDV71USgCgUQw30ybiOvlbuQoSQFlxcd+uYR0W1e\nr0dXQbimq1NBStaRZvFXhqJ9akqsEfxHsMh6MN5GJor1e+MLH/YZqQZr3p+v\n3xd1yLQEkJiAFbL/X2IlAjgv5bQJPJnKjh2WNpviw0KyTIfmJ80ncRYoqdUH\nHVKGSnetSjhj23shF/Gk4EOisI8emDnMPQZDQ+Qwj+ML2dlq4b4Ik0Iwam7X\nPYZbzjZxStZ6RvVHZNt3cKERhj5hDxfIfJrGhUJXGJ/wnuAj9k/kTtXAyDir\nAGQbm3DHpPnIZobP2GbnyoxPyx6S9zYTwvKa/q3LJhfu7Bl29TMAlKJs344h\nqZZpmcRjAqfOtxc8FfoxXhdniGQ0XB5sXuw1R5id65h75cFJYE9iluFkBYIl\nw8v6\r\n=ZjuI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.0", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.14.1_1620093359291_0.7677362558268248", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/plugin-transform-block-scoping", "version": "7.14.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "761cb12ab5a88d640ad4af4aa81f820e6b5fdf5c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.14.2.tgz", "fileCount": 5, "integrity": "sha512-neZZcP19NugZZqNwMTH+KoBjx5WyvESPSIOQb4JHpfd+zPfqcH65RMu5xJju5+6q/Y2VzYrleQTr+b6METyyxg==", "signatures": [{"sig": "MEYCIQD7Nr8azaQvZcjWaTbM+05MvpdIMvCb9etcMMTe5gLZIgIhAJc8hnT80qowqIN9cWWCAbR7yQFbkpmCtjdYH4qlRY0Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvECRA9TVsSAnZWagAAVvEP/0HiGk7vOmAO5a8Rrxbb\n3OVvoy93WM3L2OWnvAHDYm0jmtnCZefFaq/savufiSTj8rWCCwHdfu6892yV\nGtVgz1LlL5T+F8EbDkCWAVrMmJHkwJXzLRroSyP8+Q8p+5aTy76c3fP+JGj2\nY67+7wZ+Wy9l6Hx2TT8IwNCflx7EE4BXJJoTOSBC8JwO8v/vAO2g6bcwCAmv\n+P+fA4waysLyeZTUgaNjPx0iXtSdk+yffyjTICA/t4rdwyFk+0MFN3lHsEWn\n5NHp8Ecm4FUpV4TvV9+4/tC3vsjVFcjI6ymAwL2Qjk+50tHy1FcGHrH7kQcG\nKkRV5fIwZWpK81utEkDRdnCtvt+f7tknNmgjEuPDNCrwkWJF8xTO3o9RAEuY\nNgla9LNFDXtFc39/wh4GNz9ZXsmYN+oC/SluznN9t7/EkjaPtFtTCxfsDShd\nUbfHUJUPk40fqU1O8Kn4Nb7Vbiu3kYkzQN3iyQbjveHrb6uoixHxunZUabZb\nIMHrnMQZKgxDP/IFgCqmX0AkCX1ps6tab2m13PM5YfrOlDPfaR4VxFPpYgeN\n4Op/0Mceq6vmo/llP1vpYaVEygUXbop4BtDTlzLLpGvGiT+Sr/PdNiBly7/2\n7ZqW0AU42ys5MMtGCKJfRJdVeJgWJUbBQSEi1iPBdtQmF+tlUGA60CX2xr0u\nc5q7\r\n=Q/Sb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.14.2_1620839364082_0.8556798106587813", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.14.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "caf140b0b2e2462c509553d140e6d0abefb61ed8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.14.4.tgz", "fileCount": 5, "integrity": "sha512-5KdpkGxsZlTk+fPleDtGKsA+pon28+ptYmMO8GBSa5fHERCJWAzj50uAfCKBqq42HO+Zot6JF1x37CRprwmN4g==", "signatures": [{"sig": "MEUCIAEydR8CrwH4rFm/056NnFGZppAXSbq5Wz11VAMgb5+1AiEAg4ICV6/rh93M4CBhq3aKAtMLMqWUrngaJo7B/1P4+zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGCCRA9TVsSAnZWagAA588P/jLXGgE/gD0gRHVvw33o\nqZNtS2gQysaH46v+8eYA4C5DJ00cFE/1PDBpczF+hS/CSpOjH61tl5lMyLIN\nok/qtfv/7CTaa27UoFKFs+zQvMG/QpiqdjuC5PJsuWIYmifkx3zgoAHpz7eP\nSfUt1XGk60Q+NnSLnxvDzHjqUxMKBlu/tyReBRr+ehx8/ZRSWh4QEVzD8Anr\ndMr3WzCSoGx4gHSwBaIrUfaDGhCmxR5cor9jQEkdXEe8Cj4JiyEEVs+H+iZO\n1bALxWYtuFHZZB1sWEY9qhkBYR/kQB3OhLH8HUY1pnSQqiIhAWz4lDxv586l\nKPUR82m9LhYKvyaTiAB28NMZ7EEFByQ1UFPfl272AbMtoiwoKyfMSTLSFlsS\nm/D+uALSlu+2U0iN/qeV4y8qtjIUsf7ujmVOKJFTgKZTZrBL+U/WqGVeVoAv\nvr4U0rUv+Kp2JFlI8QLzKTRbif9XcOnpBIW+7oHWCXD5dQ6z0BxMw9wcWOrA\ni/VKKi/niMtiOdJ80yDjdwm9xirKkB6hyq8+zcCoW0aG6fqHzeaXHt/mVr9p\nJQ2fNNib9OEZEql0cgIu3S+xI2ab1VRrJ+rINVlCwKDSrjQyGc+A8n5x8+DQ\nSv9qslx1YdCjGBrTWbTNSnGJ1PF4PEMgY8F13jzZojoaeCP4kwW7GsGO8+Mx\nH5LF\r\n=lcrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.14.4_1622221186121_0.7780472130914695", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "8cc63e61e50f42e078e6f09be775a75f23ef9939", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.14.5.tgz", "fileCount": 5, "integrity": "sha512-LBYm4ZocNgoCqyxMLoOnwpsmQ18HWTQvql64t3GvMUzLQrNoV1BDG0lNftC8QKYERkZgCCT/7J5xWGObGAyHDw==", "signatures": [{"sig": "MEUCIBThP1SdGwEapPdYyFQpWC4xDOliPtGiyotMNr157nS1AiEA0q2iF3jXpg69VOjz7xSXF+Mz3IY7O5+wJAgp/g3OcsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq+CRA9TVsSAnZWagAA+2kP/243+7wI4b+6I4aEAa3C\nfgn0XgeZZPoFKqLgEDj1ndd9OcsBDwzlJkPayDe5uAyZgmLCrTQ3WtF6azKa\nwTgKkE5bO4KcWUl6S4M8pfn69v0r0LRg6PgQtXO0mgU/PqcN5L5rdA1cCw7P\nS8v/6Mj6zwt2SsNY74z75eVvpUEULW2ILLKcWSJLPHRsawkbW8yvXEEOfRQ5\nkrxpAtxBM6PQ7uShhhTlVLRMyE2ZbfyGyaoSzLKN1yTaRdRPz29OzXXGfvuW\nOfxwSbhg5NRu5+11o/KTvQL6UWGZXGQmDWeOxVJDnKyElBck1jO1wVWDS9md\nIBtukugw6BQ8s2SwMXrS9Xh9sjj9csiySY1NIDUencYrSKcUns0q7dNviYiY\n3N63BxSkc9Uz/jnXjvHmqdWLT1XlEQQXTujL+VZmBZAwMxp/wSc812Nbln6g\n1hfO8kNWwxz9yNacyQKPug37w7xALyVfED2gqRTDcZBEsJtGTqjQpTCy76ss\nc34wKZ1JzrgJoJefZzo6DZoW3wEt/QiXihZydx/9gX8WJ5W1guQ8oso3QiM4\n/RwjbLrbmlW0SIO0lVccDdMdIUCAzo2B2jzU/OXYz41cLU2MG6iUJUm7YisI\n7619mBirCCXX9WH+Ohqn5ZKA+y1ERNsyaWgtZTkQM2MCo6BINdTYjiFY2mAc\nqZI7\r\n=6NhA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.14.5_1623280318391_0.5856503684850776", "host": "s3://npm-registry-packages"}}, "7.15.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.15.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.15.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "94c81a6e2fc230bcce6ef537ac96a1e4d2b3afaf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.15.3.tgz", "fileCount": 5, "integrity": "sha512-nBAzfZwZb4DkaGtOes1Up1nOAp9TDRRFw4XBzBBSG9QK7KVFmYzgj9o9sbPv7TX5ofL4Auq4wZnxCoPnI/lz2Q==", "signatures": [{"sig": "MEQCIAd85rW/B/SWe/r8JJyB0oh54FEm/Yd9QT7EEAKVfAWeAiBkuzS3Xtx+u0g1jvNUA9RlDEb6UOE3G/pMMwqBLGLkow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhE3oFCRA9TVsSAnZWagAAVXYQAKHuvEZ1lvsp9+F+lnGE\niYE28dgG0hD2LfvDOilolualr4Whmz1OTh2o9cTbknIGlsST30iXd7LEC2M0\nEMMsDIExR9Nw+L0XvBwcROsXvj73hYcbmgaSNCCGkrNyYkLUxUzTAcjYtMrp\nbVX9gY5nCc9JC6Cu6qdo9JjNaL3pnD1VRf0K4jcWwwOg7aAFdqgz9cqPnExT\ngWxFLqmwq17xZQOLYfEX9VPGZekliTulOHm8OmM/kNqCs68WZ3c7iVvwnNG2\nXkwWc94C4wfOoOcG+BSzHNZmq9qRY/GqjMrVVNCchNOiL+mlCPP8BXXRR/hY\ndWTCy0F48KGIH0bWdXOfgb7F3pOWPeX2IiKB1IiO2LWtA58/QJx2Xhq0beEU\nEoVJ5F6tauBWiJbegxdLmTSRvBdIuWI+B09AWxR2et/DALQZEXQq4/Vm2mab\nDxy10pFthqxusAI/GlPtJv1GhHz/nfYiIpWMBgKo8xE3g2814IRiA7CVq1ed\njK3uRl/8Xyx8GaPZhkBgpzRn62iJZLxIarpUOONYP8WLrBVOJh/P1vzR2NXo\nE/HYiqzAF0CjcfYkMixgJhXFRxrn8kZl5DG5Jc2R65qmdJLETjx4kZ/NwuQ1\n8zM9I/W2ybMN1/BpxawNfmjhJPQh7AEeD+GTntVTubxbkJoSexnwyx2qvC3a\nHvft\r\n=fBn+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.0", "@babel/traverse": "7.15.0", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.15.3_1628666372927_0.7421864038375927", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "bcf433fb482fe8c3d3b4e8a66b1c4a8e77d37c16", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-27n3l67/R3UrXfizlvHGuTwsRIFyce3D/6a37GRxn28iyTPvNXaW4XvznexRh1zUNLPjbLL22Id0XQElV94ruw==", "signatures": [{"sig": "MEQCIHoTqQ9GDpZmqtcKT/F1fxAY64p1Ykugb1YnK3xixwE+AiBk27DaZPFQI782grhPfSLAWTYmfUh3IVt4HnxRKBBqUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27175}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.16.0_1635551248537_0.16384051563981128", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "b91f254fe53e210eabe4dd0c40f71c0ed253c5e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.5.tgz", "fileCount": 5, "integrity": "sha512-JxjSPNZSiOtmxjX7PBRBeRJTUKTyJ607YUYeT0QJCNdsedOe+/rXITjP08eG8xUpsLfPirgzdCFN+h0w6RI+pQ==", "signatures": [{"sig": "MEUCIQDG/NiN+7X6XWN9oFK0zviGEs64jzdE4HaXr5UcUaWyCQIgXzQVEfJAvOYykFfwdIyHPQ0n05aF0XNogTW/hQsLu54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j5CRA9TVsSAnZWagAAQCwP/33ztN2ZArD+8mURjraJ\nkb3Dl1/bREJ9xadHwypH64bMvSH4CUwnOHM7kd+gta9nWGIzLcFzdF1ItY5C\nq9u3niulROWXku10j7EC4z1icMAf69bE6C8Hs3dFMynu4Zzg2cMahTGDYtW+\nT6fjUfgvK8PWCLTStvyiX9tDJQDVIE4UFsp9jy/YjWI6msIA3kbSuwT+Tqhv\n5+2HwpV13E8Ip2I2/tYoKt0yeAAmsU3p2z/viiIE8kgbRq4AB/kuiMUX75hD\nF8xjZc/vu/9+4TPt8URA+rxkksszt7HOYrX9jkS7xlgVCNj7dB23vTwW4g8h\nFJKfue0BD9syxv18EOxDdAYg6ztV52HRF2AwjJ8ykrXuaLqy8ipg55wwBzaQ\nD51/54OROfiv9Odkd7+aEF3J8c+EBIZE0ptKsi0PpaF3GD0Mf5CD1gXm3Tnq\nRspK2KDVSVzJ1y3Q59tSxd4oDeRaJfnL4FhmdpSwIg10mHTaRkDsgwzrgJ1J\n41kNwO7O6HeLPQoA9L1ZBV47hVo5dYEJCDvK5OzBtoMeNU6n2z/EbZXgswhx\nelEZb2OWoNgteelJmzM0wYA2hwz0phwZCkWlEDxr+qWk8WqN6/sQsDJdbvjI\nb2Zngb8qdJJi/W9fL92vTpXnncHH/ZZPYz1GhJBJ19bjkjCLEq5uhOmZ4I1g\n0Z6/\r\n=KxoO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/traverse": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.16.5_1639434488918_0.11031009614706355", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-block-scoping", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "f50664ab99ddeaee5bc681b8f3a6ea9d72ab4f87", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-ObZev2nxVAYA4bhyusELdo9hb3H+A56bxH3FZMbEImZFiEDYVHXQSJ1hQKFlDnlt8G9bBrCZ5ZpURZUrV4G5qQ==", "signatures": [{"sig": "MEUCIQCPego3Nt8fBTejMVPJC+BDq/vLV0MwgYflIi9jCe0M3QIgJcbY1tpsnOih2KLXHJGyQMwnM9f2RgXLC4/7LOBQMV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0hCRA9TVsSAnZWagAAGiwP/A2pdb8sg7K4qxFwCkjQ\n/iGhopQx7fFvpRYcC9Pq/voznGcpJ0lt/TzwHzBT0oFO4Xnxd/F7pF/cO3Cf\nIHeD4QgXgtjKp5GHGdKK7uVXlOauMIv1A1nncKKiRbNpUNIyJgZLZPJzMT5G\ngJtyiw82ZRiAp9ThXcdeaaYNV94pyDC0zQBMNWAUvzl/8Di+WfBcvRmPtdk2\nTrg6cd6WHpImKhY/i6hTlUDlo8TzU7ZtCaotEUx5ecWGowu0U4pYNrIhY1Wu\nvTflqyjZOtRCxNtBcvSqfl7gRzwsGsJbiUpm3DlscYNrJxTDZceDQ3p+N+AE\nmg7cvvthIyZV/nw7XtIMLRVTLE9uf3rsqjRn5w9B1V7h9odKy92vfyN08u4N\nwbsA+c1BVODWzHp9Gk4/Jdbp3f/e9ZIh/8NiLyqG5WVBqgqVOuqhsgubhULe\nHLb87vmJQ+1m/owO4ihys06O2/6qdFGDJJyL63hWNT96tjf3XS5AeRlBvfz7\nsHuOSq/uBoUxKcyp17pNJG7kY0JYtJfXc5R3g8WUYVoHOJ1fuRVJ8ByLVXXK\ne3F80K/mM7O3pVL0ZchzCls6XxJ00IlLExyTcTbnl6IVdHFDcJo6usro9YH6\nx8ne4epicP8u5uMVGEMdDIL7B3HMFaeEYZMpRiubYQ5HyP3X+8yIlsnKtv0N\nKcyN\r\n=1aY8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.16.7_1640910113112_0.5813486320770367", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-block-scoping", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "68fc3c4b3bb7dfd809d97b7ed19a584052a2725c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.17.12.tgz", "fileCount": 5, "integrity": "sha512-jw8XW/B1i7Lqwqj2CbrViPcZijSxfguBWZP2aN59NHgxUyO/OcO1mfdCxH13QhN5LbWhPkX+f+brKGhZTiqtZQ==", "signatures": [{"sig": "MEUCIATjRPoXYdD+QVh02DDgT86+5IJytgwX68hjTmb1VBcFAiEAjG1XxGs4BQOjUb2xceLiMAL5N+u8X7X90CZM9ghhhEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ/A/7BMb8xx1TQ6dBPjuSKxk/i6bcEoIGfORARu/8hMDJvFLzgV54\r\nmr3xTJJLvYmPY8HlSgEBTadoK02T0YPUMwxUHBbnq5BuRHmd/sRIeaF3f7DJ\r\nre4mjEXh8nLy4LPrfh8HbQdOBFNdvefIPZmK3GymrbcbOCAyK1n3L2Wfe8Hq\r\noe1YWgmdAiKvU5Qmz+edf9YOJwcPCh7hgM7w+ECjUHh05SIDm0pJqcQM6rmr\r\nG91dSZ18ASTB1ut0gEzKFOeEJvtRyukW/Resy7PCLMssV+5IPlMgZSj0tCGb\r\n1KtQs8ibVS5kFOJJ62blCdwdXbuxIw4XHzG0v/WpUFKVLrdJsgpVAfUr3j8P\r\nBQcvZgDaHLt0VWItHZh9O2c/adKRYs0ZEFkkwlZufVDtRSbU9FXbyFemMFlw\r\nbGqR8ElCJNxN3jrMR7LDtRqE6hovAKqKA+qdUD2IZpg7D81Fz3JUojEywLvb\r\ndSWH7EUbB8qjYiLB9swnyDS0RHtP/ZU4v2VFawxpWcAdDFJasiCqTtMbRahV\r\naaTj1PvzK41U5qIvw7jJX/S/gnpw1vC60iKYdR95AkVe9XREQmG06d3cEAXx\r\nE8HChE5SedSavH6InH/ytw1LfmIsBKkq27wqfOTJ20n43HbGuNTnkbO5UB09\r\nrWZaHuAjW6RhD9SmMQ93HigrE+L0f2PKqbc=\r\n=Aq1L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/traverse": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.17.12_1652729564566_0.8915766328204151", "host": "s3://npm-registry-packages"}}, "7.18.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.18.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.18.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "7988627b3e9186a13e4d7735dc9c34a056613fb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.18.4.tgz", "fileCount": 5, "integrity": "sha512-+Hq10ye+jlvLEogSOtq4mKvtk7qwcUQ1f0Mrueai866C82f844Yom2cttfJdMdqRLTxWpsbfbkIkOIfovyUQXw==", "signatures": [{"sig": "MEQCIAab3RlAzqnEH6TA7xYEfIDciz9wd7gbqKSK5mMwxSsdAiBscc/ftxa64mxQuNbdEfVib/7h0edNts+SQ4WowW3VCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpISQ//eFfk7e2ErG5Cw7lOrUJEGVa6tBTsnotWNKReJyMT4JPD5FMW\r\nybZ6hDwatkNEo7NNnS7CUWL5ODZQUFSa6vF4AW7RqP0SzZzQDOZ2f21iTkvv\r\nr/NTrSzFBc0jVq4Eh8k+4NRRAiq+QjbkVe4H7UmlQi/nUzUTvKCCCSKAGSh8\r\nkfr2kbLO78ltD4eAIUK9cGZ+nwT2vo415aaV9GPnWd93hRfg80azwuBWSAO7\r\nssZ5tPeTQ9T/2YiZkLVZEBqO10b4RQZV1VzMuAdIYNAbGr436SVi4w8YqBEI\r\nmkfcTAD0DpXjoWuqiNlj3xBiSp4Inbks5TlZUz7HYeB3EVnS92sgf8t7RM/c\r\nzjIyv0z8dEMwHaoz6z0AJAr5oGw9J/HyLX3YbB7a3SuzVkFPLIh9Mh3r8tEQ\r\n0/CspaFjQQ7zwjdZKkmFDWjO2seWVS4yEle7RtoYZz7wtjguMz+aaCslCYWh\r\nwJozVy3SObPHidPoI6TK5524JDkp1J4O6BqfSm8orKpStYWMIF9/0IHa98AV\r\nESU602b0nhJx9Sk2wJ7svylaacgrpFCrREJWhXohubHEXqvrkPSxNCdZhFeA\r\nWTRPOedy2DlEGhET4uYY3zK/oHq+MXyFMORmePbz0prg2BG43Lee/wA3pm0j\r\ndMj/H7qZt++pv6dzqvF0FUeEf+3N8NseZ8w=\r\n=FsY/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.2", "@babel/traverse": "^7.18.2", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.18.4_1653861011519_0.7785396598713878", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-block-scoping", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "b5f78318914615397d86a731ef2cc668796a726c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.18.6.tgz", "fileCount": 5, "integrity": "sha512-pRqwb91C42vs1ahSAWJkxOxU1RHWDn16XAa6ggQ72wjLlWyYeAcLvTtE0aM8ph3KNydy9CQF2nLYcjq1WysgxQ==", "signatures": [{"sig": "MEYCIQC0DG5uyR84F7rwpBORo9sYGl8Js52YFmglhfdta2zcrgIhAOZhX4n+Zcd3JCz9rxz+LoSdciglrhXEhbbI1hgnmsBH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJFg/+Jyt36svIxRyWRYk6Fpii6XioTVQm6DPnIJvIBviZmjJWo2lo\r\n3yugQnG8pwBcGciNTQPHbe2wBaC74NXFVmBeB1BTXaPGCRLOXhAZzWsrvvZC\r\nTvJLk0y4pFcRKtEhrn8iiRzEIsiEjCwCrgDv7ZNH2yUyneDT+3kaVHcSJAM3\r\nZB9baDxcASkUhgV9DAagU+J7lqcbdNcY8lLgJdKBphM2p674qsIwWRh77cD0\r\ntapw9hlhMAIFkNlL44Z9UCVieuOsca5OcGDFyle/UgiWNdfIvlhrHVm8/YQL\r\nhSkIl8ZRkm21S3zbOwcHy/BOux5Y2S4KqADQET93b7ScPRXJJ4VS1cIHwG0W\r\npnGe2HSAh+2/ExUGbvxM5vfnT7mvx/ZpC1tuyou+/ub+mvtRlJY56ACANdfD\r\n5aY59JmomGlJmXXupmjlNvGoJuyVfqD6pbOr2AbjGj28PxcVnvzvz8c7iHvn\r\nGuqYgvm6X3oW0nghCjfB0c5wXB4hiI4liNpURFsHDFxG+1TpCE5h9Um8TBIr\r\n3fzN4HsOpsxZMAxuDcvkjd484bQuirFpHq6IKj+pEpGJhr1+eXZbTiR7u88E\r\niE2Ud0Dtu+V/LvTroHT/NIlgYsPEUWKZlBFhLIYrV4NcCAfGkwOcKpFkfzzO\r\n4ARBWQ52M/YMIKfXpHsCSSewz+wQBjXw9gg=\r\n=NkvS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.18.6_1656359398581_0.7221851000189374", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-block-scoping", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "f9b7e018ac3f373c81452d6ada8bd5a18928926d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.18.9.tgz", "fileCount": 5, "integrity": "sha512-5sDIJRV1KtQVEbt/EIBwGy4T01uYIo4KRB3VUqzkhrAIOGx7AoctL9+Ux88btY0zXdDyPJ9mW+bg+v+XEkGmtw==", "signatures": [{"sig": "MEUCIQDQY2aRJHHPFDbYicCLpzOSJvxBWPqTMG8wZZ5kjYUsLgIgA22CiEWO7pXN+heOEjRyoVug3IVnxAFz82N/nPwxPJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27239, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXmQ/7B0dDFW/JRvi2Cpts7bX11FnEeK6YTJGQ5QxCsor0vm12faUk\r\nWQbTAEFisaule/7z630ynKgUoUWo/Ubwa54N5L4wsHAWQtJgjYB8UMibcmq3\r\nSW6JqThnGnemYktdqDoh8tQN1iRUe5Y1o/H3ZjB9T8psu8Z+13OY53C6DUv0\r\nom+C/nMSzd6+as042Hxo+pA/TeZx428O/lHGCECuMexNDnQYsohyynYTptv9\r\nIGhvPqvhk5MB+EZiF/DD1L2V531JMGJIjU4GFbhg9JxjcOB76MSHDIT6g+rj\r\nvzAzU3A0rOIglo2K7MknQ8jtQRy2y9pmT7o/P5uU6ktdZB58YRXLIfledWgX\r\nB8SPRtwhDSBl+FgNwEi9qJWSbmdQmGz5gJlioU9a3dyx/GoFKHJno+2ChdTa\r\nPpxkxmd95hSdSKf5U2UsEkiG+7Z/sND281PoINWlcQIzth2vsDFo2r50iae5\r\nCRAXkKmapDowulvuI9g2MasKECm3uMMPkEAbw1OQsVM0pdkYsQswkkgfcqJz\r\ny+0ktK/nYxoJDPQz8zC6lhhuTwMdCrHin8z3Jb7f6KdfXEBqKQGfyQn7Bqsf\r\nh79KWVGyudwuM4bZbR1hNQDhKrhGChhYVKr/dPA05pZhA7xL0YISXf5UiYZU\r\ngs5D2CEEUeKR64tvk2aiWvIhjJL+yeUg09o=\r\n=2Qn7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/traverse": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.18.9_1658135849929_0.6425399268778427", "host": "s3://npm-registry-packages"}}, "7.19.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.19.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.19.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "315d70f68ce64426db379a3d830e7ac30be02e9b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.19.4.tgz", "fileCount": 7, "integrity": "sha512-934S2VLLlt2hRJwPf4MczaOr4hYF0z+VKPwqTNxyKX7NthTiPfhuKFWQZHXRM0vh/wo/VyXB3s4bZUNA08l+tQ==", "signatures": [{"sig": "MEQCIEL81A2AzspYk7P+8XtGMjPU1cdGonddteBAIqdreL9xAiBdpi9JS+xr0IrnpHeLzi1+nV99aiDciy0tLSg9ysfQhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry3w//dXdyyOj8u30RkzU5l5VRW/rJWjaTfdl5MKLCK86kx5QEbyLO\r\nMTS9mFXzrXjRrfiGOo3Q/pN995mzF5SOpSaOg1RQVGUOxWLDt6MJZ6tuvTsR\r\nZutLBjmg60qfsBVLQBXxqm99UXNCNADgoZ7HwV0YLxbMkx3OJdRly8Eya10z\r\n3C8cN6knLTbpg0cGHGT13Hm5bMrfkjbE8BGqJP4gzABfktqdCtT/MrpdRNe6\r\n9LxFpOtoz8XiwHinDEAdPKMrg7lL4vhavTct4JeBlyirdreqzxrA5D2u/LBg\r\nxQzlWGTFO11c2XBLHk3hD0dIvO15/T+NX1XXnlwbQ2+u4iTn+4TOuEDt4xCE\r\nQQO1XAweUOnkpjiUNT+X9LSPVMi1yvXPpqSFqlVuh2++mc6iczbDjv561m7L\r\n/KexmXehI/TKdrQy5X3ySevRaFO1kbYA3fsGleHXjDd+hWF5fYILBLpEtVnB\r\nY7aM4F7DDxn7yP5U+yo+MowYRqGNyrtTWOKm9TTTUYrIs7dQ82v6G12Zaj8C\r\nF5exj/xloVhKlQ9RrALKm64BzHFGGG1LbSC4RW+VFdaqdBGSX640Ol0/e3i9\r\no5vw3Vnz22kFw4iKPv3D/4wD9STsjjNT7SnaW9RN+ildTqFUFDmCfT/xTJsI\r\nKw2zgugPRiEzxrJYUjCeYoIqz3AouK8bC6k=\r\n=MYCU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.3", "@babel/traverse": "^7.19.4", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.19.4_1665398840447_0.04994415701420851", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "91fe5e6ffc9ba13cb6c95ed7f0b1204f68c988c5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.0.tgz", "fileCount": 7, "integrity": "sha512-sXOohbpHZSk7GjxK9b3dKB7CfqUD5DwOH+DggKzOQ7TXYP+RCSbRykfjQmn/zq+rBjycVRtLf9pYhAaEJA786w==", "signatures": [{"sig": "MEUCIQDa4ogNG+DzLgm/6QiJf1uJ40YKxTURo3LQiQW3V7yHSAIgM5STvIBlLCOrfnTtOlynsGM/gApIogDEKFGlEaO3rsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrISA//SgETBjC3Eree4iNU23VTidVR9noh6ZSY/oZxHzd6dXdG/nh6\r\nIclbmYrJ+gnPUyJS9+TaPDKx8A3T60VCK/GN0oSfSqGpecAlgZApzUG+qePx\r\n5VwAByFZiawgUfT/oIYG26grDBco4YfBKdJoTFgxZWQfDW8DtAoSHmAwPLld\r\nMtFSA3m65OdA8N0ySqtP3iMO1/fF/6EDYN8UL0dv1i76AZJwGljZ/ulTfPlH\r\nc1UfI5keN7UnNA3srErBNhIbpWwDn0c1oC0PxEmvtO/WtghER0uzltgsqKP1\r\nmDfAyefDrXCJ8dq90s8+LNHx9WltD2ue80bLuk2aVAQHpEyehQE/HVF8F4yX\r\ngs/mxKILDGjP+SESkXnelcJSaIeBIpi6lRADmc+KOPD6wVx4sED2KE0QOFpF\r\nFzWqyyUzZBYL+2PzDQBJpxU7w92HFlI7CXtmrUixl9OqsL5ecFD9OHVmlMMN\r\nsr5+0hczWeHqAmMt1xAKyyWHp6IMwyQQid3yZNRnnZiX9KFNANk2kPs+vh94\r\nuCCryM7Fp2aR9J/IaMqRSj7pct+Ecdhgg4nIXhv23WDpr/qfvWm+pyTT6k+/\r\nrS0yMUl/SpaE18vxNeTL7XzEbyPHG0eU7aRyrMlRsHgIsRAw8os9jJHpr8eX\r\ndXnXgTa6Z0r/EC8ZRgA+vQTA587UZJZDiJk=\r\n=r+Ny\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@babel/traverse": "^7.20.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.0_1666876753065_0.6016837524568386", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "f59b1767e6385c663fd0bce655db6ca9c8b236ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.2.tgz", "fileCount": 7, "integrity": "sha512-y5V15+04ry69OV2wULmwhEA6jwSWXO1TwAtIwiPXcvHcoOQUqpyMVd2bDsQJMW8AurjulIyUV8kDqtjSwHy1uQ==", "signatures": [{"sig": "MEYCIQD+MC6MAeR3mzUg82Gi4DPQDPiFhu2sLyxtuFumv6dSnAIhAJujFq0935WvyauB/5WJ9hDaVi3NpIrhuwxJZE5CTijL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpxvg//b8JDB8T7sn7O5NIq9mbfptuPX2T2ps94BvBl+/hMmNbU9eL3\r\nF5qfO8GpMDypjpLydGWWFPcG/X4lrcJzzCRFvQMCKUY5z5x3PHjsEJcOpqst\r\nBJpfkKYxmL4TcWFsOamCJbC/W0uDxPDPPf8Wm9G29GG94UeSOm6jtpXEcJX2\r\nBjSv9O7lgSIQebX4gKpWxOBa1U5tKIdw+FxQ/yEMHEEPFOqLJbRv4WHtvbit\r\nIV84xHzDxL8MxyVYg6aAC6x334pLvmiqCKF46jX3xybtFkVT5jC7On7oZWA2\r\nVDIIu0nWn7GDSFPZ9UFKPFBqHuwy4SkEcdDkNH8N6Wzvjr9nhoS9RPLLAHQT\r\ndk4skd1DOdnktWebFlGa5pj+wupB2zrvMuQBn7+gBvpkYN/FwvfhvHREoUph\r\nhck6fWjeiW0aJWhqZr3mL+NpdOF4JpCJUO6fDFjwcQj0nRDDyFG9fLdH0n3G\r\nXPVs5WfUpPHHuxGGcJv6Q0xrqvLDBB6TYTlnESTOW5ASenGepQrG33ajSUMe\r\nJ7ICB5pzbCKivDWknvjgHK6CXTm7ovwKeAWECUPRE72BoRA8OXjafhq1tFi5\r\nKyp0UOhSlSjmd/ZT5ZAmuc0OnZ+byx9q2pOLPgK00iF6an1Nynaac1YA2xMY\r\nVNIH/IhiUB9+DoUQxyAZjj2YmyCirCB/sHw=\r\n=a1w3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.2", "@babel/traverse": "^7.20.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.2_1667587867586_0.03144848244149201", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "401215f9dc13dc5262940e2e527c9536b3d7f237", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.5.tgz", "fileCount": 7, "integrity": "sha512-WvpEIW9Cbj9ApF3yJCjIEEf1EiNJLtXagOrL5LNWEZOo3jv8pmPoYTSNJQvqej8OavVlgOoOPw6/htGZro6IkA==", "signatures": [{"sig": "MEUCIQDVEkBjp7Hj/RyTaX5SMiVsnb2124IliwgNdWFSGo5KWwIgJSH5lOHdy6YvuxYRKBQIgbX3g5rLXakdrCgEMMdudeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96636, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgpQ/+PCmNXVJdHps43Xfknkl5KT4QrxZZ4T0xC3IsCyyBJb5MJKpn\r\n9CwEvMtISzF8UOyRSUTgsyMeBRRDd0udTeYNSj4nCgaV56okOONY812YhE/u\r\nGP9OQUrPpjd3iCkAKHwwlefWAXz7fHWAv8WNuk9As+2ADJHk6JZCCognxzin\r\nPI8cEfr459VY29IEgm67n+kREPMk/BwTybZsiMeTMWc+EeCEYPoBkwsqi7aX\r\nc1/4KLQ+6PJaaKTU+qIrURtq9pzGK1oyTWU8I038FhwEb0dJbJhheDN5c/1E\r\n/YX4dFZ9sVd5mgfoxdHCJJhfmEeML0MFIKclW6sLIkOUfoPj9Hav7H0y0amX\r\nm+eVInof07pIycJoqSqvVQjOOrrtoX7PmgQgsKUdhcS3M1bQadoD7WjN2XMb\r\n2wBX9FEKrYokFmOnfyXvM5q/9NL+N38FtMRZhK94Q953EgfPXKppiT5AHm6g\r\nS7gEjrmB10emFHc43kW5OAP45AcMJYNyNI0lVViZN3dI/qJaJ8eeiDJkd3T9\r\nsRRO2Ks97drl7qllnGg8mXcy8wW5jmCO7UILIhm/iuCxKFx5b8eain3azyJK\r\nT7szkt6yjv0E6i54Hev/4XOOOOVKz0lXrQnq6hj5pBsPAAvzU7UgtJh0+sQA\r\nwnLW33MFslOB/oxet19C9TaBMnokuXnMC8A=\r\n=L0nQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.5", "@babel/traverse": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.5_1669630363341_0.6629138557325782", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "5cc9cc3f3976de7f632d3f20eab3abee299ed36e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.7.tgz", "fileCount": 11, "integrity": "sha512-C1njwSKnumUgtgc4j1LAWR48PkfwfHHRd8bWyolSCLShKnqA52VX1+B+GZhJteQlwZeSqYddCQh9Str816Jxtw==", "signatures": [{"sig": "MEQCIDq3dXGoksB7tvy+eeIbR7EjU2zAkyhFkIzf5OMRGYnZAiARdVMnVXlN8WWkBiNppvkRDqP0NAIJyRc9Ea/KGAOX+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCcxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLyQ//bI+5+IUcYczQEwFEMyP2OwTFv5inxLFf3ybd028o9bpNxoQS\r\n4VeXlAus3SSWEPCEmnoyosq55gKF1rMIni7o4bHhTB+Woip0yxbfkBUz+XcZ\r\nCF0kHO1im69n3WVmyxYcoMfjpypVOfNrhXo/mI56TZdEbqo0nC4/HTGTypXM\r\nsv2cAFCPCkzf+qigiKV7xk3gHrKxMd0cbrMUK9zQVhGYXTmtTXS5ZoVEKDtp\r\nNk+MUQYsTqt5ZND1QJH3ewBKZhaBUtXbMiSdLXotHePyQDstr0vH1XmWhM1R\r\nWm1o8PHjpmGy/WvgPbvql/qCf+m8nJJef4YecpRFHt1YdsMDBJMozVjP8uxB\r\nQjH6ckzbKbnAMcF93DPkvHsS/VfgBS9TjXf95xvHY55sXlBhbW7dZJDk/eWN\r\nE+tJ7E9KB2dWV3f/en1jAJyIN+5FOkro/GAVwRirlM9HNH/RXH0eteiHG0Hg\r\n1bqAEFASUm9ACxnFMmux1yB5NFIIA1r3XwT/vUAUdoSVX2FGIDaxX3bZpZTt\r\nZ9gWKjwDU6W4QkJdMl4QrB5/kpaaoIaMdYtxpPkgHq6zGNVQr6moJkHdhI0X\r\n+Ibo8FDWp/WUGO/TwNVod6CHJDblIAmjgqGHr/3cqKrbMck3U4+0t/L4MEcV\r\nH/Sqq/egs5OrFkHEQeKXo5YFzxR5tUTnojk=\r\n=s9zL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.7_1671702321206_0.5749634150238356", "host": "s3://npm-registry-packages"}}, "7.20.8": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "ad142bce04ae32225e2379ca54f06d5cea92fdb8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.8.tgz", "fileCount": 11, "integrity": "sha512-ztBCIWJvcWJvtxhMqIrItLmGlbxaa/5hl7HlZvV4f9oS08wWn/mEtf5D35qxFp3rTK8KjV9TePEoeal8z02gzA==", "signatures": [{"sig": "MEUCIQDsgvTOkRJqOYzbN4bkiAGjYzwTaA1p1pQ+n+54Yoy/CAIgJ1fpm9o57R+TR6mUfDyp3vAUzL9FbxmRkARDMYKbW+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpIbBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQ/Q/7BEvqkbj6tPRNvFSae/2C9ib5MTtZcct4PENwq8TIJn0v15qo\r\nxNsXTpjdY7egjR6jNyHt1EjrNDN5iNqdEMAXohxztJ192EL9pkUFsgig0w9r\r\ntbNGXxW/apeS05zmjqo+yxq4UhMCmgtxBabe1aiAxZtDYifN7JtxYP32IVlN\r\nA9YBlo+8dX5teadWyNige86ZkmJb1N1uqiQuDtpToM/KHlQsHKc/Kylu1vSL\r\ndGFfGwX51FPcJmR+bj94DujhWDnfLqb75hWzqLQmmjH9W3EUsP6kqUxUoZOc\r\n8EPQc0X41hdtZ2wKNZSbVNnQE/JaTmFH39CiqzoE2N5Ea9EIOD3RQzbfnIHc\r\nOjkCQCBBQDaj40WPOWNzwcJaXajv+tWaOsfzz0kMl9hz7TGHQu/2FOkvcMSa\r\nE+6c5F7kBUyJmwt22iOICAtDkUSlo+5A5VhtWZZarJBwZ0FK1I24fHLYx2Wy\r\nAg9+T7+K2bVjfPLxtyq410zLzvr9f9P7k2pZmum1qEK0o9fhcZyw3e/HpT1C\r\nt6Bi1NVBIF9JiXYhiwHBxWoTRa1paZ8pJGBKbNyzyhLlT5vwOGVUNROXqOFv\r\nT2hmcNA8+BIiWik7H8iKNoJv3FXMvLtenYOy4O8M2oX05WfdEyp6vx7wry1O\r\nD05gFlDF//71WGwKmT3vlUhQB66XAgV3vYo=\r\n=qkA1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/traverse": "^7.20.8", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.8_1671726784851_0.3557279381553673", "host": "s3://npm-registry-packages"}}, "7.20.9": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "2034307073e243fd476dddb5472ec35d13802853", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.9.tgz", "fileCount": 11, "integrity": "sha512-hwZN0kr16UkIF/kR9F9x8gd1kTkQl1vyAF2lkUmlTuCtTKOGLE5blQctuxEeKXwz0dkArQ9RYL8+HLb/75KGMA==", "signatures": [{"sig": "MEUCIBxyzoupAioBetFt9qUNnRKFnpbOI3DMR/dntwh/tzgdAiEAn2Qn4NUtYKyKkTgWNWpoikY0SPXM1efzffUeuDKZKlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpPrOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKeg//U7lVHa00TOpnlmfSGZz4T9Iu/HvjQ6F7/8GaZOJQ++ijHmL+\r\ndEFQVp54V7ekq4chFwbsX++3iVQSJGMwgmy1aYQuyybxZE5Fgphwe+TNv4nb\r\nr6w+8wY8CdkpXyB8yNvnVEc96hLSHWgglZC3nBZcv13EBKipCCe/1PnCc6m3\r\n28aWacPIEDTdD2DuFpx94kpuL7StUDLh14+IC1LQ0poQE9NmEJDw1k5eZ3BJ\r\nY58dwSUnEA9+ipQm9NROLUDyHrfSlnfOMhHwu1GVnHOMV94B7N+mfE63EAW7\r\nNIzQrJ9HuW7xA7xG3BKal6s61HLAnMtSIKf3ZQ6v4lDsywZK/ZQ+vrHH737x\r\n2uSuM+6XddB4B9p7K8mpOZ3ZvZVNrylRijS+hRzIAng8856N++hdo2v4qyzw\r\nBimZIBWEZbVHhrWKUUIS0tULR4Udq2rX8neNg0V9FjTAYZsSPLShZOod/lXU\r\nNUG3n3jZhr4yYonL0ILVOtkY23OBc3bm0P8a26kfCriBpcZRtAxY3LnMrE2e\r\nSgbp99K1sXuPnb7Jj7O+hSHNzNNP9yafrZsbClUtQ2Kd6bm8UtWicTIZMfbO\r\nd2P6lDQEaHOWmtwNttl6EapKr2JxYEmHsxpd43cE4JWwsKcylRDmWLlGGmeS\r\nt63UkxLqiV+83zWFFxw49iyH3AlCjHxQ7NI=\r\n=Jnns\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/traverse": "^7.20.8", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.9_1671756494139_0.6965450511867914", "host": "s3://npm-registry-packages"}}, "7.20.11": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "9f5a3424bd112a3f32fe0cf9364fbb155cff262a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.11.tgz", "fileCount": 11, "integrity": "sha512-tA4N427a7fjf1P0/2I4ScsHGc5jcHPbb30xMbaTke2gxDuWpUfXDuX1FEymJwKk4tuGUvGcejAR6HdZVqmmPyw==", "signatures": [{"sig": "MEUCIQDfIsMNxKLpgsUd2a75el0VbNKTRof6rkKouoCdh7AXHQIgWNsfM6gxJuDcUrutVAG0PfsgLx9hLBnpBVTZZQNXmME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjphi0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaFg//ZtEA/mZe4OXHMitZD0wFXNvWKdohyq3pue5m9IJ9/x9wwdf6\r\nOQSNfEho9E6htVvBrDGQVdUM1Dj5I//pM/6iO9BLbdOer1ZRFFCfyzGSl9bX\r\n0hn5HY1CzxSR9vF5+4VF7lWCb16v9jv1tkIdrbt44YPxJ5SaNoPt8BH7io63\r\nXlJmt0s3eAZFgJWrJmXbHfKmGQaRtocySCZnxYpeFADxJB56ISudpnGAuUHc\r\nmZvxGiaX55KZYjrN9DpqRNYVSDTCySaNixBzwIGZBStm5T66xrcb4LuiVtIa\r\nPRDvORu2zFalbC7l0ndC9Oys1akTMjFJcgLVxxazBh2gTLy4sGOOnANugoqC\r\nAdtNfYsEHwaoWKUb+hzNT4AbABfygdcVyvNY/fNMRcvGEnBQmFz+rNOlDn+g\r\n+DGF75zux16zJfG98yqaMHw07qhPtnNrrXtSRw7U83JgHiPe6vZxuyrgmQNs\r\n+Qcqm3n4KlVpZl3rirg/7ueBIrr6Up5ofH80Mrdh/giwXqB7+0dAn63UZLNE\r\n+AzhTUBVkSVgFXZek1XI8BV6Ducvn5RxHZX2ARgsxQLET/bHqb5NxR20Vbld\r\ncpv6fXV+b2gyxvh6mKciOoQfNrLRDfXwxFSV8HtAednhFXW65pIG3gQgkGM1\r\novGYpNofE+F3pZNugGn+6oNzYe3nRhLERNs=\r\n=nEna\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/traverse": "^7.20.10", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.11_1671829684385_0.8435753598493128", "host": "s3://npm-registry-packages"}}, "7.20.14": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "2f5025f01713ba739daf737997308e0d29d1dd75", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.14.tgz", "fileCount": 11, "integrity": "sha512-sMPepQtsOs5fM1bwNvuJJHvaCfOEQfmc01FGw0ELlTpTJj5Ql/zuNRRldYhAPys4ghXdBIQJbRVYi44/7QflQQ==", "signatures": [{"sig": "MEQCIHEYviq5m7vMCN5h39eV9BuRx+KL3tMiC08gUkqUdfvtAiBQG4aJnhNA2qwBF0bTg9nPwiGDsUrYJM+pRDx1s51I5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1Dq4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr93g/+OTuUpUzXJYurxs4dq05tQITvtye3Z0p/1on6HFtRt2ns2vcf\r\ndzW3D14LPmLm8GJR63EVE+q7vb9SWm2NBdG41ZG4wb27hWJsNhc1/l7JKVNL\r\nB5sjYKw2F4IcKmP699pS9jc3Wiydoctyf4BX9FQfvC/kuWvXGfOJ34a2jWZU\r\nOvvcPXoad53EEpdZ/Eme0Ijg8e73rzpoWk04F8qq/4ldZTlynhXPnD2tu7oi\r\nFlrnhrYiMz9TYGz43+/JlvPdoNz40dsgnB8CBzNJCK/aqbXnmfmbmzKprOnP\r\nNU8tcp1XL2YlPFDv35jLNkE7aBJc7A8M8x3SEd0aUWk/vGPuM3Vvv5mhE1Cw\r\nxuGlkxUVkL0kmxmkqtM23bUHRApNuknVJMdHshtU0CoJXznbn1a+Mr7wzyi3\r\nRePe5UKuwSj3Vfa0/UqjozUjjxKVppa/BzK4yeT0MgRQg49LixHFjudxhpNf\r\n1KFditXkY46NcO4z2IVLdplVcN7CDQgn/iupKWDfiCWBGOkRv2EthUvZF+SA\r\nRBGNKGirt7U3lTZ1MjnJRZLu+QrGhiC0YG3EJNpRqmBqf+aMYojeDDX1jUTe\r\n7v8XWhsREGB0au/fccBmKBnTZYTTymPjJ/5QgRnuedpfXwtwDGSFssOT4hhV\r\n1prMu/PzA40kabPnbmeJUA3YrEAS3kV73I0=\r\n=pa1j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.12", "@babel/traverse": "^7.20.13", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.14_1674853048692_0.7092756827104143", "host": "s3://npm-registry-packages"}}, "7.20.15": {"name": "@babel/plugin-transform-block-scoping", "version": "7.20.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.20.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "3e1b2aa9cbbe1eb8d644c823141a9c5c2a22392d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.15.tgz", "fileCount": 11, "integrity": "sha512-Vv4DMZ6MiNOhu/LdaZsT/bsLRxgL94d269Mv4R/9sp6+Mp++X/JqypZYypJXLlM4mlL352/Egzbzr98iABH1CA==", "signatures": [{"sig": "MEUCIQDxP3iexNxUF6+pbWX8HHQYvv2ASqnNhDt5kkjbysBBTgIgE16xsfRcx3cEhMq1/Id5zBwuFaqF1Wudps5qwatIueQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2/nkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXZhAAohe3+Sdsi27MzyM+bwQqOgiIBGd8ziNI3VLEmW42oCe9LtmB\r\n9O2mlABRNJpOUdN9AUY/QnTK4iHaRONGDBPRqup8boh0v5wADuP6KWaDpuFq\r\nbDU5+w9VFuDy0mRICNnpfGfTsl6Hf11U4b+qjM/aQM1xmXjlJzg8DC7dlf7Z\r\nsAhLbcq0iUoPtunpIj7NZL2IU450viA9IdYK+kGDZiLJudf7ONjKRM32p4Oa\r\nWto0xcvy8lmTELsE0UnuyGsErInDWrvio47dYXnNFuR8Zpo1sFOsx2Psrfw6\r\n1QqhNHgWPsqaL22RH029VZnv82qUs4rYFOJJG/chTCyi6kGsbRpBWfqJ25KA\r\nDFTF9MhkBmGdRvHjWqEQJl57j+sJDmXpmO761ttvVAG4kGsHgjy3JaZ/udLg\r\nF/wY3qkbUDJs/yAtNuz9x2kRFlmoY41lWgrGoMSRunQuVWBZ+ebmi0dzsXgA\r\nVwPdDJTCY/vzzgKf3eJ+I57rddKoT05sEbq60kc0f6fNfqGDqTyA2ax8F1TQ\r\njSzn/oWHyCiD01OuDYMgHRSuBD226PAlt5vpMcExn68PL1ZLkp62Asde2YAF\r\n7XOVUFnf+i1l3pJe6NesMc/51LlK9es5q3gZbXuIjAa5szjrV3iRxpkNKP55\r\nUmMi+yD8WIOeLqUK9+tILPZG+h7nFjuxDL4=\r\n=Y1vX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.12", "@babel/traverse": "^7.20.13", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.20.15_1675360739830_0.455114795538093", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.21.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "e737b91037e5186ee16b76e7ae093358a5634f02", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.0.tgz", "fileCount": 11, "integrity": "sha512-Mdrbunoh9SxwFZapeHVrwFmri16+oYotcZysSzhNIVDwIAb1UV+kvnxULSYq9J3/q5MDG+4X6w8QVgD1zhBXNQ==", "signatures": [{"sig": "MEUCIHGlCe4wqcUO7ociKButVDPWcz7ie6l6484v8nSeRWiUAiEAh+RKdVCxRL1m3XqXhGDPhC0dMFi4a49/RlM1WUObH6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVDQ/+JVSk+YxyBhfkzP55Ueu+YhelYUGMvj6SeItFMuVM62ZPrMV4\r\nbvpJwMU8aIllB4zj/5G8T76Ywdsor6bmg4gXwVluH7suCbdH/bxjkWuloExI\r\ncheFUSbKYacIQOcJ13tKnvuNko79Co329jIdNaYHH6PwcRi42pG8x8ncv3Vn\r\nUG/y3VrGnySCeo6AmBjvrGPHX+aMa9x6Y7JrpSBqaR9rw0Lpxp28S/2AcGSG\r\nlx68OspbnGOjaWAFpChUy4CcNeHM/RX+XToNHtekzZHxDah71DH5lj5S78A0\r\neTkB44qDSL7X9f5/TUcyvI7qHbb8K9nimqwyWtGDdIIwt2W21v4V4Gx6RmdD\r\nHhTRzW4sgFC7g1G/8UNVxKDIGU/47AX+az760SnmQVpaIp5oI5xqhQP9UUUh\r\n8Cml5QoFqGwRylBdDHdI+Hkl2rT5blgF3Ld1qqClhCt5dF1mE+RnOfxjRB3y\r\nA/zYr+0Y99UsjMNW8MemioMa6adUklG+U+4nYxCbqr5Ft32tInQk9QCarMFn\r\nYlut31mNcQR5nqmXzq1qPh2zs69gB1nUInBcZ6vIU4EiHNNX/EvSEWnMzlLU\r\nrQgsHNp0y+pjh8Bc1/Di3D8DBe29AsBVV5AzXX0j/dsBKu+Muyy5lYP7OsJA\r\numiPleUsKFnSCcgvX4I+rx9/QRVEUR9PFuM=\r\n=oExs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/traverse": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.21.0_1676907057149_0.6212971361085831", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-block-scoping", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "296745b0a71e2461296560818dbbc392f20c4cb3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.4-esm.tgz", "fileCount": 12, "integrity": "sha512-8hX7T7EVlGPz8bnNgdk7jfTb3KeXxGTV/8dJgDSOwXhRM3f92/6xAsbkNkPddj360voK6vvCHi++5X8gKe9nSw==", "signatures": [{"sig": "MEUCIQDvF4EE0kZet/pgbzqwcUyZMk23dCRLolnU1ZOkOJdYXQIgZg5201wAvaPoa8NX+YqDdslS3D9AuFde6a6FwlnsdJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp88w//V6g7PRJeXaNYKBifXtQ38IWImM2mPZw3PXFafJ56QWwLKNBF\r\npJL3z01N3LUepVhg2l1Z7M5rAT2SZBWdVUlfhnWnX+DX7O59FpynWn62z1qB\r\nCJANLRZ/ArbK3+XokiXC2m853HX5vN/Qxonh4oKUJ1BRPa/guo++QV0Hw7lv\r\n4zIDliifmN24sHdR3srWxm7TeblRRaP667wk6TpNhLwHhMU/DRN1hNp75/qU\r\nTP0qNKoKfVaRRZT1+BMy7OIrYFKctXONWsgV6A5uC5a/b9BDhBj9PwEAeVWd\r\nw5MdGqUPJ9UemUA/K2YMNxfmHQFytCVk9b5jp2Yyl1BFvW+EWn/uiTdOykWM\r\n5Rxy1tnqb1ohWHMcLMVjxls85ZCdbceDx4jVwbn/anrAZcP7ZNjFMheuY9ew\r\nFTx8sQiGMrX+iehwTwp0M3MBMnqwsMs3Dt4Nt8tUGWeybnTB6+kpBN1PcHLK\r\n59JWVKBe5yqcu2SlMc6xemW4r6H+G/Md1fng5kNdHGKNNUqziLAeWcczMCyp\r\n6pvnTyR7E64gUOGRfCMOjMNF+Q+jgPBwEuJnN/7sLWy8P3wvZ/bO6uSK9Tnd\r\nRuXW4AXBlHr0f+Uba4ljIrd1A1nuYfX+O7oApaD09Nvz727WxdxjKMn8l4Hs\r\nY5yb7giyXfJlAV7mIhQDyldyqCGfQ1iwsE8=\r\n=5vwU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.21.4-esm_1680617361524_0.6258611242554146", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "8d23d85413b97d0042ad97ebc18dfdc573f60972", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.4-esm.1.tgz", "fileCount": 12, "integrity": "sha512-bo2B3VCHz2AbxmbwtSlcAPLz9Kh9g9uxXDlEHysxlZScec0rLKIOl5vF+F6EoQBXAhzBCUAvkIucIBqWo1SteA==", "signatures": [{"sig": "MEUCIGVFvJT+PPqFIfyMwcfuhk9mAT/e9vG7ndbNCcNbE5ZiAiEAmtATTAILZdLbahUw3tsfCeEGKTx4n7vRia9T4r44Tws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNdhAAoJubKRrE5iOIRvR7EJmVjAJx8Qw8uYKJ6yohspXN+bqnIuaD\r\nOD/DyQ2aalGlg/znt9JzUZU6gaiHrNoLqnwzpdF7ELpTYHZl4RgV7F82n/Cl\r\n1VfiNNGhC84W9xvbJsa+dxQH1H4SxPlfeBNDFY/AdiVZ+0aKIS4iTmu6OTc6\r\n1gR0SszWWAZpBDqGseY4qv6BlD0I8rwUZe9c//ZyCAq5YIXO2TFtsA6JCWRR\r\nw45odYvX/H7Q7ibxOQXwRoo57p8h5VPtd/mANzn5VQiJ7Dmf6ugWW0sm5qK2\r\niRluYKP7SMuFA4YleDg582EB5Y/2egPofY8fLcwnex/POEyVl4GIg0FeBy6U\r\nPfcDDgjMsLPe+1DgZaFgacG//FbPyZRdOABjjtQxTYa+eD8HK9172BjgdWAf\r\n3mFFJrIOz51rEmAdVXWf2SRjYGl8imPV2JxF1jqZKKzDvzIbDHgYKt9JSxS2\r\nGRzhocXjqGGkLla17UcYSbdm0OmjcDI6sLS413aCdTNtvExmsjn4kg1MYHX0\r\nfP3u61ZRALFRydqB2wvDvJWzerm3uAzpHvn72IV33SUsXWU3KYsBuFMdh0JU\r\nJBRi+4w0YfHQMz//nV4iHLCQaL41axhcf8tTOBuvnax4Vc0kZYQhQCqGD3cC\r\nR1hrEzltAAELFvUKg4JJvPcPmC3tct2JlZs=\r\n=kSN8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.21.4-esm.1_1680618070225_0.0827228352493996", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-block-scoping", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "31524300ad3bb267d90ca53255fbfbf4e6f469ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.4-esm.2.tgz", "fileCount": 11, "integrity": "sha512-pkCY8p7tbxwC4byyjprZXSfVmEMWNX5oItI/wsAoZGgdHC9vaC8bO34cIYRo+hlGykd/X8eaC1U+CvRHfeDSuw==", "signatures": [{"sig": "MEUCIQCsyNJH0hWXvV72TDNbuB/tlcLGYZ0EISuJuS9OL9B7MwIgCZT7frVTzx1UnzvJfN0HBmSgr693nuQD5UK0ZyMTdnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6rQ//ehtj0E0akojKbcisJnuG+n3XSLyBh+nNq1NCv0txYkOkRT7t\r\nk82DvKtu+YjY33wnd117HwgR+2DYyNS8nlbu7C/FMZgOQGCdvJa37/KLMlPI\r\n82HxohA5g5Qr6fKiOQTRIrb/B5Zr+Ki4xUaOOTaQnRw0edHLmxPyTqYRfbf7\r\n38VDJoHdnykQjQd2JBIUmn091cEBGWqEqbxJ7K1KbTdCMzTAHZhRpHZLvaQt\r\nKB5tDtvar75DQbPa3xkbybxiKobk2hqSAr+B7sufzMV9ItsjZINxU9swVRdD\r\nJklBoX6blU6HPG0d/nUUXvnF1UoixZJnUFlazl0KAXaeizK6jq9bcfHJBFZp\r\nIy6tT1lTsmBHB8WWvj0n6rWdtbHMHB3jcd/Av0syRHOnL19BDYjRRN6ZyAus\r\nuJNM7t1ZWyVLyJev0MqiTzUSevxPvpVruR7PmhRta9Z/t8EiJ+NO5yUwgBs0\r\n7wtlAz6/jMjXbHchdShop3Od/rgQF2MDUDeZ3KyLsqf1Kdv2JjgTj1c7k1Ad\r\nzj8ZAWFqXDjylHMooFEdZmD1JWGEteLaKYOfcr4sGURwdtEB2Ry/aVvBgphn\r\nQnAfvSrKfk3uHZftHEbzO7Ro/awkQEV0xtxd5UalSm1odf4ApHUh8pyGQbox\r\ni+t/N8VOS354AxC0QOaVDSKxjwZ6ug8PVjo=\r\n=3Zs4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.21.4-esm.2_1680619154151_0.7120933947395374", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "dca8d945e9ed2cbf55e65a3f186875ef118388ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.4-esm.3.tgz", "fileCount": 11, "integrity": "sha512-LX77lo+CewKdcRcn1Y2v+9v3EmphZYVt8gORAEZpkwb8HEc2pCHf8fck84k5R9md70n1JGVt0IzlZnSQIuVhQw==", "signatures": [{"sig": "MEUCIBlfHIsayCrQtaSGGcS8jlsuYfA6Q2yk5wDBMIs+YpsKAiEAtV3EvDb0DEQ67cquCG0nDOaNtek5F9jl83l5nnq+4IU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtCBAAnRZNhm84c6txCwbQWubQK/BAIieSr/f7XHl4aHb4Vpr2N6mK\r\n/VjHWS0waVK6sVyFpCgiwuRzuOYzR3iKo6wTYqjuenF93jLrQczjiPKvzXLq\r\nAOkHefeFgJNBnRbneth260iVGRYpvGtZoi0JIm/lxi2UNXQa454/XuTfeTME\r\nG4PPkQcEFF8rel2X7CmBv4r3R3qldrWpzmbF0ZyDgviAWXJX6JZSwufg+FZF\r\nYk9DJ5TnF2XBDEMJlkdUi4ToEnz1nf0kjY+gDy0f2viZ2FlW3lBIMdagE1DL\r\n0uPpDhQhVDthk0S1855deMgp5usfkujKqlfyGpluJP7Qn4NGsCsPnva8mUmq\r\n4q3nQvGkMppcJxf0ll/m/AqAK489Xk8+QRhaI046L9NUrO2Nr+eFOju4mQci\r\nB9uBdJS04GJhz3jLDBdOka+mYaYA8A5RPvn5QGAwtJ8tUgTAaYK6AzrHm7TP\r\nTd3cp5OI30HIRm7f2CLMJ2ksXtIyHFxHvpexDTb2JLtHnrYl84X/A/z9My2F\r\na7/8sHu/sTMGpa4KhAXGtYb/3xsJ0jLmezrW8+YZDNLrZdplhPw7r1llQ8CU\r\nk6Y0Khrfqacl5goXuZpU3glrbZQziKnkxKFU6o8A60KmPBVEOCfSc2lm9dLt\r\nzPbPYYVbzUI0MLK+5E+9USnP8VMXlqzeRoo=\r\n=EkkT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.21.4-esm.3_1680620165133_0.09010005614728533", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "a85b1db7e3ee44a8bee150a261a630db587317af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.4-esm.4.tgz", "fileCount": 12, "integrity": "sha512-HPXt97cWnNJSGIjFUwYJiVOTwO2MrpVADg8b20aTEwAA+qGq3vTQJ5WwapAMVbDzYR0XVTj5J9VOnMOf46EYIw==", "signatures": [{"sig": "MEUCIQCZjMQ8u40QP903YXlmqRJkN4I3eXnNzVaiWtipanL/qwIgaCohAKRSoInjj3cWk/o0DHkZ8rqdOsYCYPXR6V+9QGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrFw/8DNFopyTUVX76QCYGWFapsWLooggIK8aeuk8JvqiCZjDzWYVy\r\ne1lGjfl7uNYGUFb88FEpcSCDprLAZDYWfpgwGXohpop3fP8So4qWCHhwgHDF\r\nkHaVW/SMxOMGQrvUmap2kussSwjzaS42VE4PRVBem9v7GBbC8LGQe5+D1uNg\r\nut25G/4T4llIl3Ic8sTbCXJLmYGq6FgfqP0vEbWvchG4tVE59br+GBtj2Oyb\r\nj2EsX/BRVMDkFI4B6cAO2BjFNg20c1mqqilD1aL1VZnAjno2qXOafVPzxuAf\r\npPj6aroLJvXYspCWlY+ocjH9acoyvbX5rSCxVts+4HORjW/kvfYO1cS/S66o\r\nEQ9Km0tkzVN0xYbd4yW56zUJR1+EnN9reZUKUBdF0ooGEVNNpF+btEsxKQ9/\r\ncNQOc7NqQ4b1+AOwk74deVKhY198VoJSBA9krdRGH436s+1q3zczw1bMAqdp\r\nf3twqnW0AHI8KcxdjRBPHJV93WfM8c5+5GkbrHe1axm/xF4yTMzE3IVQRXIU\r\nCdbF2G992x4Fawf5+ji9PoWKhPWjAKxJwVfcW39eXQYit1cN5EoNa6D8G7JA\r\npi/Ln9O8g0o5UPUmID8C7Tz4d+xFx10TSdU5g8/anaOTk9//01BX7DaGLQdy\r\nOT2F4Sr+YO0BApZP+zA+ey+AI+1HDTReXh4=\r\n=wxLs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.21.4-esm.4_1680621197583_0.8545481694278312", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "8bfc793b3a4b2742c0983fadc1480d843ecea31b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.22.5.tgz", "fileCount": 11, "integrity": "sha512-EcACl1i5fSQ6bt+YGuU/XGCeZKStLmyVGytWkpyhCLeQVA0eu6Wtiw92V+I1T/hnezUv7j74dA/Ro69gWcU+hg==", "signatures": [{"sig": "MEYCIQD/+yzKdYWynPeK7lTYublsP5+FLAHCxwnhHqUqEg9vjwIhAOsUtwOGES0qNxmfGw4B6IwocPiIChSUMGA9dp+MHHfe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84648}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.22.5_1686248473837_0.8646624821204381", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "96bca447c4d36a168c05409c7faef7e832d92c30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.0.tgz", "fileCount": 11, "integrity": "sha512-OMaN/htNRuNW3hv+cCIZQmc29Z2OxHZ0pwK/i8jTo5PFByA2+ahP/cfmK/LsRiKmc8X4/62bmeYRcr1vat2z4g==", "signatures": [{"sig": "MEUCIQDScbme0I+FLMdQ3onRKPw3qT1SNWujO/kNTUHW6vERRgIgd5yqSLh7430VW2RFGBkpbQlUuDFr7Ua4hmCqbhSQ51g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148954}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.0_1689861588608_0.5926834191203434", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "e648a5b649077e76229048c380ab0712ecdc7a94", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.1.tgz", "fileCount": 11, "integrity": "sha512-pLar9wS0f6DNQiUCXOF9b4uMsbM8FOpEFHwoT4EUtWys4w9b94bXrjVCbNKdwb+lgJbB+CLCL/yfsrol2eoYuQ==", "signatures": [{"sig": "MEUCIHYvLo8+2s4u3SsQp+mGcX6Tr5AwWUPzOF9AMFELTgWnAiEAvJ9clUtTBMTueLpCzBe/VFOUwNVUlPJM83YflqAnjAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148954}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.1_1690221092103_0.6087860663136258", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-transform-block-scoping", "version": "7.22.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "88a1dccc3383899eb5e660534a76a22ecee64faa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.22.10.tgz", "fileCount": 11, "integrity": "sha512-1+kVpGAOOI1Albt6Vse7c8pHzcZQdQKW+wJH+g8mCaszOdDVwRXa/slHPqIw+oJAJANTKDMuM2cBdV0Dg618Vg==", "signatures": [{"sig": "MEQCIB81yLS94pkwZOSVotLJizoLWYPa7Oiw/WVWfON3W8v4AiB2IbTeGiyAwh+hOXc4cuQjECcgj4fpqllzryv2lW4Z3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88323}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "@babel/traverse": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.22.10_1691429113230_0.3615290864070746", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "0bf531dd14a34bbdfb6f42d3c32496137d6f4c38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-U3vjW/1Ta3bOktViJ09k3+krSizyuDGMIsmNPzh7inWvB80yH5MPcNNdXnhwEbOLWUpdMfJX92Lr4Zi2z4sgpw==", "signatures": [{"sig": "MEUCIAqX6ffy1Q8qE1fPVokPuf7Hk5siLAqr/uaS0BqGdaKYAiEA9u96aE9poT7RtJxi3blb+w4Bc/EaOT9chMjOKlnmB4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148954}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.2_1691594088452_0.2513210186223567", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-block-scoping", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "494eb82b87b5f8b1d8f6f28ea74078ec0a10a841", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.22.15.tgz", "fileCount": 11, "integrity": "sha512-G1czpdJBZCtngoK1sJgloLiOHUnkb/bLZwqVZD8kXmq0ZnVfTTWUcs9OWtp0mBtYJ+4LQY1fllqBkOIPhXmFmw==", "signatures": [{"sig": "MEQCIHUig/7Z7rfLRxHbsUYITNz4xWid1MscHHagQgRriUMEAiBdjZJ7yVUejDxrrBbsFR1O7wwg3dDMz3KeJb/BZP5svg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88341}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.22.15_1693830304918_0.3959493059445427", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.23.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "8744d02c6c264d82e1a4bc5d2d501fd8aff6f022", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.23.0.tgz", "fileCount": 11, "integrity": "sha512-cOsrbmIOXmf+5YbL99/S49Y3j46k/T16b9ml8bm9lP6N9US5iQ2yBK7gpui1pg0V/WMcXdkfKbTb7HXq9u+v4g==", "signatures": [{"sig": "MEUCIBnpms3xsPiFcsHgxtIhuTky6Nhy0yYoOjTAQQTV9yGsAiEArpWAJ7FkWBhr+5wz16i7ukHywongce9RZr62IWqwVuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88468}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/traverse": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.23.0_1695629447436_0.1138775598837396", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "432619d04866ba84c26cf89b9e2473d97f23f3d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-dIxZV0vkcbTPeYePzBIafCF2iu4fT8I+LqlrPOyqTs0J2Z5vy6z/7y4ePjspOkS86AjJm5hzY0pvoj0+GGVJLg==", "signatures": [{"sig": "MEQCIEDEBdH28hW/suhVjZ0q3S6slOTQzrgWWt65p5GHcNDNAiAk7eSWlY8iZObFf4MUlMZ1AYJMMZKwD2zkROMhQQ+7Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87466}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.3_1695740204530_0.7033187269219823", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "3c7903fba2936bb1dbdc476eef4114794db7c85a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-sP+f5PalQgiMNjlv8iUMcNMJGss09qU0TnMdjXGjg1x3LPPEfVgzLFT6e1+ASepT7isH1wXXTfIpSR7x4w6X4Q==", "signatures": [{"sig": "MEUCID3aN/XaDQnYQuqJpodsf7NyiE5a/kYk+VTy40hO1hLaAiEA4mltn+98WFBYQSuWV+0CiJGOR940b1F2CJEeKsn1X9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87466}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.4_1697076369744_0.8606185216060434", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "e99a3ff08f58edd28a8ed82481df76925a4ffca7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.23.3.tgz", "fileCount": 11, "integrity": "sha512-QPZxHrThbQia7UdvfpaRRlq/J9ciz1J4go0k+lPBXbgaNeY7IQrBj/9ceWjvMMI07/ZBzHl/F0R/2K0qH7jCVw==", "signatures": [{"sig": "MEUCIH4ZpGzfXqTkY+gem4rFg3nqePhoqYlJbQ4n9rdyCSw6AiEA/8r/HeCXDkjJ1acevfIg08Z9l8XOmcVv06OpCIgUetQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88507}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.23.3_1699513426579_0.2362408057913954", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "b2d38589531c6c80fbe25e6b58e763622d2d3cf5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.23.4.tgz", "fileCount": 11, "integrity": "sha512-0QqbP6B6HOh7/8iNR4CQU2Th/bbRtBp4KS9vcaZd1fZ0wSh5Fyssg0UCIHwxh+ka+pNDREbVLQnHCMHKZfPwfw==", "signatures": [{"sig": "MEYCIQDm+Z9sq/VKVpJgYQKu/f8f2f1a4XjUMmB1M7TUcvr3wgIhANQ4UM74PwFYAJZKQHlIWCip4LuQutPjlJsgJmh4+x56", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88515}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.23.4_1700490125855_0.23998260213837153", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "562805697c20a69a5f09c51d7d8dc968ea9529b5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-kkJdAtDef5cG0Fq2TQVSoqJuawxH0q3ozWRXwqQdJNuYC0Pwb+1eILN9TFGUe5Pl7/rkdPBiZsfdqOnmPNRt0Q==", "signatures": [{"sig": "MEQCIG/aNCZOH5NvIXBdrCoJKOiAJ3pydFyHSk9MAWzhMHppAiANz7WUcaS7JlGinf957K1zXnTzyKO5UUe16PWNB7JtGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87587}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.5_1702307913506_0.6285027992704086", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "800fc2a05effc266bcf97813febb8e6cc6e94098", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-lak23Ad5b6ZPxNaZdzQZaW19MrZKdowL+vx7gWA2zqRKQEwatkEEo3EENah5btTiBaJFKUkvx/wzdu16ZekNtg==", "signatures": [{"sig": "MEUCID2aC7ytVb56SPo0GKdpjwCt57jCpllqtjvPTeqNL0HjAiEAnZfWAnOJViNInpRmOVcVyNnmSUnC+rEhw3rBdE6HGms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87587}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.6_1706285634173_0.19517488283183448", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "88e1863f66e341c829eeaed44155f7d2b4049921", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-EgGClWTtmUrLsUPXL+RigY9pZ/Afp5pX94hCKt2/uQXDI6n3yJzK91J0zD/7Zr0F31LlVNtE7SLGH4qWQtDA/g==", "signatures": [{"sig": "MEUCIQCQ5sN7BDqcCcLSwDGrxaCSKuYSzC/3tcP4e/0d6B4oSAIgFOSfCteoxrKaLLk58lxbCmfpT1yPPFUkNpoh1DbiWuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87587}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.7_1709129082661_0.8774346467523664", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "27af183d7f6dad890531256c7a45019df768ac1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.1.tgz", "fileCount": 11, "integrity": "sha512-h71T2QQvDgM2SmT29UYU6ozjMlAt7s7CSs5Hvy8f8cf/GM/Z4a2zMfN+fjVGaieeCrXR3EdQl6C4gQG+OgmbKw==", "signatures": [{"sig": "MEYCIQCymy89UMbp3TTI/BHjFdVj1lwV1sDPNrLlZ8jS2uvX2wIhAMXB9YBkJF4fcSGYIGJm1i5aAFm8qNjTsyJi8g9y9uIO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88494}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.24.1_1710841711579_0.827970379260137", "host": "s3://npm-registry-packages"}}, "7.24.4": {"name": "@babel/plugin-transform-block-scoping", "version": "7.24.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "28f5c010b66fbb8ccdeef853bef1935c434d7012", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.4.tgz", "fileCount": 11, "integrity": "sha512-nIFUZIpGKDf9O9ttyRXpHFpKC+X3Y5mtshZONuEUYBomAKoM4y029Jr+uB1bHGPhNmK8YXHevDtKDOLmtRrp6g==", "signatures": [{"sig": "MEUCIQDp97uKo+exDLtPEFkwZmRdOfoTxY7U3Ur149mWxmgm6wIgNr0mskMobd/ImR9r5a4egVhooYjUbBmXsUUaRmdwWd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88140}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.4", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.24.4_1712163226417_0.08074153759571989", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "d2335d8b79e7a3d8e3ac1fe6b1b60b6c820602de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-mG4IhpbE2cPapiz/nP1fAsXAx7FRunV8NeEUjp3ff5MuuBWY6k5a2W50LsD/CnkHx7xukleaVZPOm1u6p086ZA==", "signatures": [{"sig": "MEQCIE2MUK3RQHuKNJ8h6SAMnvLDlLd4QAKPUplfZcL+fxH4AiBFTD1ncNoIHwlMioga4cQ6tfsnvN0LiMyqpp1QnOdj8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87148}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.8_1712236784291_0.2216006094840557", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "89574191397f85661d6f748d4b89ee4d9ee69a2a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.5.tgz", "fileCount": 13, "integrity": "sha512-sMfBc3OxghjC95BkYrYocHL3NaOplrcaunblzwXhGmlPwpmfsxr4vK+mBBt49r+S240vahmv+kUxkeKgs+haCw==", "signatures": [{"sig": "MEQCIHX+p3K9gRW3BYWAK/HpOyiqD6gvhiuL8bDOWGf98TsPAiBcFeQBS9b3/+wUm04Lgea5qvW8sQmsrCAmUG8NEZ6FjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154586}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/traverse": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.24.5_1714415658284_0.6950876989958708", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-block-scoping", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "a03ec8a4591c2b43cf7798bc633e698293fda179", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.6.tgz", "fileCount": 13, "integrity": "sha512-S/t1Xh4ehW7sGA7c1j/hiOBLnEYCp/c2sEG4ZkL8kI1xX9tW2pqJTCHKtdhe/jHKt8nG0pFCrDHUXd4DvjHS9w==", "signatures": [{"sig": "MEYCIQCihTFcj6+qwPV5RMZY3FYyUP8WVNv4+Vmols2czAkm2AIhANGhStyEcuK0p9hRUAqAN+mPLMYmesmWZGat+4V+nAox", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154755}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.24.6_1716553465450_0.5329441771111008", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "118123039af5ad3f6df36af6d47d1d2433a7b2af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-y1cW14EY+YmNPJxgSLN7sMmN/ckoiYjIzlq7LD2EWoFfoOpY6yZBt0yuXA8FRaw2JNWUmBtcB4zgjfWhzPUOiQ==", "signatures": [{"sig": "MEQCIGYW3FbRodZX/NsxNjP8eM4W9xAiP8cb2F/nIy83PC6MAiAKTeRogEVr9aUh6pdqAh/89sng9lfLBaVTbcx7rpolOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154146}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.9_1717423446534_0.9564313409304903", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "0e159bb1e8a3e8a94392fdc60743eb9b1d2e9ef1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-IgrkzYkc3yK0wGgdjV3+Hw1E34gcZNDjgGCZQa9WxqNkHLEhGKvIBGmpghQouDw6zvdu4l2q88SKSSWW19hSHw==", "signatures": [{"sig": "MEUCIQC4DZOLwCJa+6xVijwXvc8ZCSI9bfsfvsheKfSrAvoSrQIgFJ9CpY7kLOqzmIgdoJP+8tIFx5WSHpiGtlnK1fJLWXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154154}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.10_1717499996016_0.020976120367334428", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-block-scoping", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "42063e4deb850c7bd7c55e626bf4e7ab48e6ce02", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.7.tgz", "fileCount": 13, "integrity": "sha512-Nd5CvgMbWc+oWzBsuaMcbwjJWAcp5qzrbg69SZdHSP7AMY0AbWFqFO0WTFCA1jxhMCwodRwvRec8k0QUbZk7RQ==", "signatures": [{"sig": "MEQCIADFZJCrEc0yHKXGo4Z2sP9uxMFAXZYJW5ajZxUzlLDhAiAI4D4NwiihCREX8S4wkd82euD9O+THOPrm8TrQbpPXcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154732}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.24.7_1717593315635_0.5547820514378989", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "ab330766bcfd0d58c8a292c5815630ad5f084947", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-DKfOG/U8jlR09JqNSDSYCfP6dyl+dmG9BpR/sSHhuZkOtv+lHMc+g3BijsTVDv/5kDqkT04S+fR9DWvQZmCRWQ==", "signatures": [{"sig": "MEUCIGegjDDEa2Iwe3nsYra9UqpnKqoUvKlKeTewYFbXIXNtAiEAt1JTRf0ROJ4rg3KQLpFcJHY6U0aa0ye6guTZ7oOjwDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154045}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.11_1717751725210_0.39177715099274124", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.25.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "23a6ed92e6b006d26b1869b1c91d1b917c2ea2ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.25.0.tgz", "fileCount": 13, "integrity": "sha512-yBQjYoOjXlFv9nlXb3f1casSHOZkWr29NX+zChVanLg5Nc157CrbEX9D7hxxtTpuFy7Q0YzmmWfJxzvps4kXrQ==", "signatures": [{"sig": "MEYCIQDVRb/Ly3VgR/qPiARFQdaJ+dF9st2p5JFhyp+GvvX7DgIhAIy8G89P7PRL/lKKWo2H5u7ZzRLpLTrM3tDrN0vO6HDC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151439}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/traverse": "^7.25.0", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.25.0_1722013161358_0.0915735611110049", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "cc362c275119df8bc979ca10885b7a1e645babca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-1Jo4e/hnahN0FeOhJ1fhgldX6H4IM0yNeIPNXaIvdEfeO7Hp/AKYk/JIsp+2vsLXVZQ9zTilW04voqkrY3RdNw==", "signatures": [{"sig": "MEUCICeBH3Ecu1PSbdFr1wrYiX6LFhI0jBVtTsuid1Wcqp6DAiEAg4rAtXTb/+micyOjV5l00OoZyJFU+f8SNR8upZWGWlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150896}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.12_1722015201683_0.46537167198584406", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-block-scoping", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "6dab95e98adf780ceef1b1c3ab0e55cd20dd410a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.25.7.tgz", "fileCount": 13, "integrity": "sha512-ZEPJSkVZaeTFG/m2PARwLZQ+OG0vFIhPlKHK/JdIMy8DbRJ/htz6LRrTFtdzxi9EHmcwbNPAKDnadpNSIW+Aow==", "signatures": [{"sig": "MEQCIDbCjN3G4h3RAaDhgF/4T/5iBPG0MyEr7Wp/4d7DH0O5AiBtEPAWlzHAuidZmoScmnZd+IQYBzI+6U6T2pbrKkrQhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159481}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.25.7_1727882081075_0.9930517443445774", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-block-scoping", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "c33665e46b06759c93687ca0f84395b80c0473a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.25.9.tgz", "fileCount": 11, "integrity": "sha512-1F05O7AYjymAtqbsFETboN1NvBdcnzMerO+zlMyJBEz6WkMdejvGWw9p05iTSjC85RLlBseHHQpYaM4gzJkBGg==", "signatures": [{"sig": "MEUCIFGxFAJT/FR8Dyv7sZTPgTIF60aTFDNP+t6wJ5ZKzLPJAiEAoPWkW4ab3LQK73ikdcxW9LO3UKfcas/CPPdqbBuyguA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88444}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.25.9_1729610458716_0.746663263068263", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "01c1efb24b69da4024cc82aaed03c4e8b601136b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-qiRDBHyjsFuLEGdycpxIsAcQkiPcCJVWOSGNA299lE3Hg+bh7t7eZIlx5WqUcVtoerrssK+hw4Zp52XxHn6baw==", "signatures": [{"sig": "MEUCIQCqhLPn7bT0WcXTw5ABn+inbSX6fjwLh0vvMpEaI7Ax1AIgRE4Z1i/w3QfDJ4BFiZvom4kAC+LbENLmS69fPhr7TGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.13_1729864442715_0.2483424528461058", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "3fbaaf93e2efa8f52bb0574d4c2da0abf96bd0ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-nkfPXeTbPBzn5ysv0b+VX6XNX3Dw56r4dQ6Wne8AEYaUtXNu+Np6HHhhIt0k/Q2kysmhIcFUr4yuRHQfW5luaw==", "signatures": [{"sig": "MEQCIFtJ4yXV4tpOrmTUueAPptfeLhS/i15TMD04ZCXz3avsAiAXE96rRpeXf2v/fcy62flluFxmreiOnu29tjYkYmK+OA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.14_1733504033602_0.08865213460347654", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "4d1c0884bcfe0eacabaf33311ffeacd041b1b701", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-kkDKxgOK1OdkyLg+VWQWSVjdH+RMj/kPdwjU8jpXFWj7h01GKLJiItxUOJ2aYvs59q4ExOV0DzKOpED6a8JCZQ==", "signatures": [{"sig": "MEUCIQDoOTphSlG4v2Jt17aa5ntRlFQd/56/KP5pnFphVph1twIgTCyLxMolk5kqms4ySZkuIH+suVIFO2I4GQx46aUK8Jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.15_1736529858584_0.25822913489801524", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "a368e46d725b424de85d96cec6f562ff38b6b639", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-S2tVK8od2wzROSryBV9kzj1hgM2wPGoyxikIOfA/J6SQ48hlviN5mZBl46sYuo4Ry39AwzzA6/9Qq8/wWXPr5w==", "signatures": [{"sig": "MEQCIDlMS1wgaWr/hk/pUWfm0+7YurZMdjbi5Dpqq/Xcb3b+AiBHMvJ4xpRkyEtZhHldiCFBE6EYr9Kb2YpnElUWRr6G5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.16_1739534334422_0.22590975737002394", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "625daf110047cb60ecad539aea7c39cd1d246fd0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-2OGtQBC0t/cIugdJ0ddZ0QZKd6OzEz39IfsCtCYHVUietzMgbhaZmBmUfZPl27LMN4XdXsIqAyTx1kZdWR+O6Q==", "signatures": [{"sig": "MEUCIAihm4LZ7PMRqw6WURt+jYpewfuSYWa0M1THkbw0MA3wAiEAib5/TUJRmJJnpznnZLcIEf65copck54DjIiGqm4oLXs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-alpha.17_1741717487212_0.11658538758312065", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.27.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "acc2c0d98a7439bbde4244588ddbd4904701d47f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.0.tgz", "fileCount": 11, "integrity": "sha512-u1jGphZ8uDI2Pj/HJj6YQ6XQLZCNjOlprjxB5SVz6rq2T6SwAR+CdrWK0CP7F+9rDVMXdB0+r6Am5G5aobOjAQ==", "signatures": [{"sig": "MEUCIDdygITWZ0X6ZrJhLhJcLc6rl1s7iruk8JXAEtabsosMAiEAnVdomki7bb7JsPKrf7Z3KAZXaAehLvCcR7D0HsjhV0s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88154}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.10", "@babel/traverse": "^7.27.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.27.0_1742838101932_0.049826056137225905", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-block-scoping", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "bc0dbe8ac6de5602981ba58ef68c6df8ef9bfbb3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.1.tgz", "fileCount": 11, "integrity": "sha512-QEcFlMl9nGTgh1rn2nIeU5bkfb9BAjaQcWbiP4LvKxUot52ABcTkpcyJ7f2Q2U2RuQ84BNLgts3jRme2dTx6Fw==", "signatures": [{"sig": "MEUCIFAWxAQsODdhLMwEvuIQ3bSOe86mTZ00Hl6UxUMqJ13nAiEArZComl8tEf2TRBWlOfxJct42ALIaOHAtjJqoEeuuEPg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88153}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.27.1_1746025725464_0.4941547886872739", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/plugin-transform-block-scoping", "version": "7.27.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "a21f37e222dc0a7b91c3784fa3bd4edf8d7a6dc1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.3.tgz", "fileCount": 11, "integrity": "sha512-+F8CnfhuLhwUACIJMLWnjz6zvzYM2r0yeIHKlbgfw7ml8rOMJsXNXV/hyRcb3nb493gRs4WvYpQAndWj/qQmkQ==", "signatures": [{"sig": "MEQCIF5pokATCzhMIL19yUrO4nK9cRDw8pNMVx9nQQhcolfFAiATtY/3jj3VK3F5NrXHhs2B8doOnKIA3SyCAm7LTXvCHg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88814}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.3", "@babel/traverse": "^7.27.3", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.27.3_1748335154654_0.1821341800252232", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "b1bb86c293535eacb0944ca0d808603a5f76515e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-Gx3Ye6gKLAL9bf6QdaZuS8zBbHpRfkSjY73ICVIqt12WOJE430StzQw0/kVIvYHnFL+T86tFymE1VZwot/liDA==", "signatures": [{"sig": "MEYCIQCp8PcKya7orvP614ptcGwGFLSwrdnVNGgqLkYWOnRs8QIhAMpKhKkGalsz6KPfywlJAzwkOAIRTFF6pCXG4AOMdeTW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86254}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_8.0.0-beta.0_1748620256833_0.10463701695668592", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.5": {"name": "@babel/plugin-transform-block-scoping", "version": "7.27.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.27.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "98c37485d815533623d992fd149af3e7b3140157", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.5.tgz", "fileCount": 11, "integrity": "sha512-JF6uE2s67f0y2RZcm2kpAUEbD50vH62TyWVebxwHAlbSdM49VqPz8t4a1uIjp4NIOIZ4xzLfjY5emt/RCyC7TQ==", "signatures": [{"sig": "MEUCIQDgMzILRra/3JnWiQLUbbd1PNgScuYaQ+bDlykH5arPcgIgdIgCTUNsQqrdL5NBz1t+fd5516agSVvG8ptXWWIw8pA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88816}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.4", "@babel/traverse": "^7.27.4", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.27.5_1748950023437_0.7278430667203053", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-transform-block-scoping", "version": "7.28.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-block-scoping@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "dist": {"shasum": "e7c50cbacc18034f210b93defa89638666099451", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.28.0.tgz", "fileCount": 11, "integrity": "sha512-gKKnwjpdx5sER/wl0WN0efUBFzF/56YZO0RJrSYP4CljXnP31ByY7fol89AzomdlLNzI36AvOTmYHsnZTCkq8Q==", "signatures": [{"sig": "MEUCIQDQgSxvSNQeK9OvIcsj+bSYVFyFn9vSIBh7lMO2aAuCCwIgPTfzFcFPmoa5qqyPaTkiefGSaaW2i79zfdmG3EJ0PeU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89097}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.28.0", "@babel/traverse": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-block-scoping_7.28.0_1751445495984_0.7685777714797035", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-block-scoping", "version": "8.0.0-beta.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-block-scoping"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-block-scoping@8.0.0-beta.1", "dist": {"shasum": "d0d491c609fb569e6c004a7d84a10715674ff468", "integrity": "sha512-98nfLRM6S/nelY7xcRMkd1WtluMxGejLVWSAPJfhqqxjZ4DTQSPXICi9B2ENjlUnjRBHhPK9ujhnzk1lgR9ugQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 86537, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDJDQNuZqE9GvgPrGXTrc7YT39Z1k6WTpEe0waAldKAVAiB85a3gFCoy9FayKX3zdsIvr02/nb7Vf9Ewyk1CB/eU7g=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-block-scoping_8.0.0-beta.1_1751447051163_0.844261228950616"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:09.801Z", "modified": "2025-07-02T09:04:11.517Z", "7.0.0-beta.4": "2017-10-30T18:36:09.801Z", "7.0.0-beta.5": "2017-10-30T20:57:46.607Z", "7.0.0-beta.31": "2017-11-03T20:04:25.055Z", "7.0.0-beta.32": "2017-11-12T13:33:11.682Z", "7.0.0-beta.33": "2017-12-01T14:28:14.727Z", "7.0.0-beta.34": "2017-12-02T14:39:14.293Z", "7.0.0-beta.35": "2017-12-14T21:47:41.285Z", "7.0.0-beta.36": "2017-12-25T19:04:31.172Z", "7.0.0-beta.37": "2018-01-08T16:02:26.651Z", "7.0.0-beta.38": "2018-01-17T16:31:49.460Z", "7.0.0-beta.39": "2018-01-30T20:27:30.971Z", "7.0.0-beta.40": "2018-02-12T16:41:31.640Z", "7.0.0-beta.41": "2018-03-14T16:26:02.313Z", "7.0.0-beta.42": "2018-03-15T20:50:39.199Z", "7.0.0-beta.43": "2018-04-02T16:48:22.069Z", "7.0.0-beta.44": "2018-04-02T22:20:03.899Z", "7.0.0-beta.45": "2018-04-23T01:56:37.242Z", "7.0.0-beta.46": "2018-04-23T04:31:01.832Z", "7.0.0-beta.47": "2018-05-15T00:08:43.602Z", "7.0.0-beta.48": "2018-05-24T19:22:12.679Z", "7.0.0-beta.49": "2018-05-25T16:01:59.175Z", "7.0.0-beta.50": "2018-06-12T19:47:12.772Z", "7.0.0-beta.51": "2018-06-12T21:19:43.595Z", "7.0.0-beta.52": "2018-07-06T00:59:23.169Z", "7.0.0-beta.53": "2018-07-11T13:40:13.070Z", "7.0.0-beta.54": "2018-07-16T18:00:04.299Z", "7.0.0-beta.55": "2018-07-28T22:07:12.333Z", "7.0.0-beta.56": "2018-08-04T01:05:18.185Z", "7.0.0-rc.0": "2018-08-09T15:58:03.279Z", "7.0.0-rc.1": "2018-08-09T20:07:46.115Z", "7.0.0-rc.2": "2018-08-21T19:23:42.992Z", "7.0.0-rc.3": "2018-08-24T18:07:48.408Z", "7.0.0-rc.4": "2018-08-27T16:43:56.613Z", "7.0.0": "2018-08-27T21:42:56.583Z", "7.1.5": "2018-11-06T22:21:24.773Z", "7.2.0": "2018-12-03T19:01:12.641Z", "7.3.4": "2019-02-25T18:35:17.187Z", "7.4.0": "2019-03-19T20:44:22.727Z", "7.4.4": "2019-04-26T21:03:52.709Z", "7.5.5": "2019-07-17T21:21:25.916Z", "7.6.0": "2019-09-06T17:33:36.689Z", "7.6.2": "2019-09-23T21:21:35.031Z", "7.6.3": "2019-10-08T19:49:31.774Z", "7.7.4": "2019-11-22T23:32:10.898Z", "7.8.0": "2020-01-12T00:16:33.097Z", "7.8.3": "2020-01-13T21:41:27.388Z", "7.10.0": "2020-05-26T21:43:18.756Z", "7.10.1": "2020-05-27T22:07:15.887Z", "7.10.4": "2020-06-30T13:11:55.173Z", "7.10.5": "2020-07-14T18:17:46.771Z", "7.11.1": "2020-08-04T22:05:46.822Z", "7.12.1": "2020-10-15T22:39:57.871Z", "7.12.11": "2020-12-15T23:59:18.818Z", "7.12.12": "2020-12-23T14:05:18.188Z", "7.12.13": "2021-02-03T01:10:47.710Z", "7.13.16": "2021-04-20T11:21:08.536Z", "7.14.1": "2021-05-04T01:55:59.445Z", "7.14.2": "2021-05-12T17:09:24.403Z", "7.14.4": "2021-05-28T16:59:46.246Z", "7.14.5": "2021-06-09T23:11:58.507Z", "7.15.3": "2021-08-11T07:19:33.062Z", "7.16.0": "2021-10-29T23:47:28.626Z", "7.16.5": "2021-12-13T22:28:09.077Z", "7.16.7": "2021-12-31T00:21:53.295Z", "7.17.12": "2022-05-16T19:32:44.700Z", "7.18.4": "2022-05-29T21:50:11.652Z", "7.18.6": "2022-06-27T19:49:58.733Z", "7.18.9": "2022-07-18T09:17:30.124Z", "7.19.4": "2022-10-10T10:47:20.621Z", "7.20.0": "2022-10-27T13:19:13.269Z", "7.20.2": "2022-11-04T18:51:07.845Z", "7.20.5": "2022-11-28T10:12:43.503Z", "7.20.7": "2022-12-22T09:45:21.320Z", "7.20.8": "2022-12-22T16:33:05.019Z", "7.20.9": "2022-12-23T00:48:14.339Z", "7.20.11": "2022-12-23T21:08:04.558Z", "7.20.14": "2023-01-27T20:57:28.862Z", "7.20.15": "2023-02-02T17:59:00.048Z", "7.21.0": "2023-02-20T15:30:57.362Z", "7.21.4-esm": "2023-04-04T14:09:21.680Z", "7.21.4-esm.1": "2023-04-04T14:21:10.360Z", "7.21.4-esm.2": "2023-04-04T14:39:14.300Z", "7.21.4-esm.3": "2023-04-04T14:56:05.315Z", "7.21.4-esm.4": "2023-04-04T15:13:17.738Z", "7.22.5": "2023-06-08T18:21:14.027Z", "8.0.0-alpha.0": "2023-07-20T13:59:48.825Z", "8.0.0-alpha.1": "2023-07-24T17:51:32.279Z", "7.22.10": "2023-08-07T17:25:13.395Z", "8.0.0-alpha.2": "2023-08-09T15:14:48.740Z", "7.22.15": "2023-09-04T12:25:05.167Z", "7.23.0": "2023-09-25T08:10:47.656Z", "8.0.0-alpha.3": "2023-09-26T14:56:44.763Z", "8.0.0-alpha.4": "2023-10-12T02:06:09.956Z", "7.23.3": "2023-11-09T07:03:46.824Z", "7.23.4": "2023-11-20T14:22:06.029Z", "8.0.0-alpha.5": "2023-12-11T15:18:33.688Z", "8.0.0-alpha.6": "2024-01-26T16:13:54.301Z", "8.0.0-alpha.7": "2024-02-28T14:04:42.819Z", "7.24.1": "2024-03-19T09:48:31.717Z", "7.24.4": "2024-04-03T16:53:46.611Z", "8.0.0-alpha.8": "2024-04-04T13:19:44.493Z", "7.24.5": "2024-04-29T18:34:18.453Z", "7.24.6": "2024-05-24T12:24:25.610Z", "8.0.0-alpha.9": "2024-06-03T14:04:06.711Z", "8.0.0-alpha.10": "2024-06-04T11:19:56.151Z", "7.24.7": "2024-06-05T13:15:15.815Z", "8.0.0-alpha.11": "2024-06-07T09:15:25.384Z", "7.25.0": "2024-07-26T16:59:21.598Z", "8.0.0-alpha.12": "2024-07-26T17:33:21.885Z", "7.25.7": "2024-10-02T15:14:41.257Z", "7.25.9": "2024-10-22T15:20:58.907Z", "8.0.0-alpha.13": "2024-10-25T13:54:02.934Z", "8.0.0-alpha.14": "2024-12-06T16:53:53.778Z", "8.0.0-alpha.15": "2025-01-10T17:24:18.778Z", "8.0.0-alpha.16": "2025-02-14T11:58:54.632Z", "8.0.0-alpha.17": "2025-03-11T18:24:47.385Z", "7.27.0": "2025-03-24T17:41:42.101Z", "7.27.1": "2025-04-30T15:08:45.661Z", "7.27.3": "2025-05-27T08:39:14.956Z", "8.0.0-beta.0": "2025-05-30T15:50:57.012Z", "7.27.5": "2025-06-03T11:27:03.657Z", "7.28.0": "2025-07-02T08:38:16.222Z", "8.0.0-beta.1": "2025-07-02T09:04:11.334Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-block-scoping", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-block-scoping"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}