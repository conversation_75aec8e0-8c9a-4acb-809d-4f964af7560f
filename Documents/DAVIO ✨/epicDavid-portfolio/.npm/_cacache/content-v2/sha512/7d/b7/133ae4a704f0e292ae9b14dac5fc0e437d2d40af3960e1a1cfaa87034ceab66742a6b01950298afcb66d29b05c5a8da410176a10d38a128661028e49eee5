{"_id": "https-proxy-agent", "_rev": "64-a6a5e74466678bac1d819c6d55408483", "name": "https-proxy-agent", "dist-tags": {"latest": "7.0.6"}, "versions": {"0.0.1": {"name": "https-proxy-agent", "version": "0.0.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "c3390d917e6c5fbc25559f3ec73b711f9dd4c063", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.0.1.tgz", "integrity": "sha512-AM0D9xJF3QHMlvCmVS4cZ7ApSfI549tTsdD213PUxWc85Wkg4SS2d0RVnKi8a9/2yr3Gt5DE/ZljpNzoAs9F5Q==", "signatures": [{"sig": "MEUCIQDZllQxUyAW6rwtV0Nr2/TEMM4Vxx3Z5ow0eKRX9NcBsQIgCYQ+kGMUWwiz0McQsbgJQM3LL6xQ4dpJMl3aK7qmvL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.0.2": {"name": "https-proxy-agent", "version": "0.0.2", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "8822f8626f62b09d299cff9a1d7ed56b552df4b0", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.0.2.tgz", "integrity": "sha512-ChwhpTj9TppuU3r0JhpwqW9bALryvWJafQxzU5vi3Z56CWKrs01CMZYB1yJSl2mezfghUHd2IfNy20hmrRENWw==", "signatures": [{"sig": "MEUCIQD7zTjh9sjcv6JMTyReWXYQN2St2i9XsNcBIoo2dEWvAgIgMebeg8656gjW8ZQ009l1bhAf1yJXGhASfT+WV7LpB54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.1.0": {"name": "https-proxy-agent", "version": "0.1.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "e976799604f1528cc657900d5e10a82d1595e0cd", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.1.0.tgz", "integrity": "sha512-AOpJByKEwbB5HKACHNFL1IwWIRRLNSjesEGxNBio9LpT5vj/wF8/YS1B2QwQ+Mc4suand8qhS4/+3sdTzjeXlQ==", "signatures": [{"sig": "MEUCIC1ahNkNGFIhmNZZWFJl6OkKir+WckbLxtGP2xVGpUJ1AiEA0arfcYk9lKcWt4JLmazXR60A9j/jCU0f4QG37qOHE3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.2.0": {"name": "https-proxy-agent", "version": "0.2.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "bee5100afdb79e7839f2f0031ac3f6dfc811ea9f", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.2.0.tgz", "integrity": "sha512-50IQ7WI2x11W6628PDoml2tfw4yWUpmvdk6VJs+6rPLy/B+k6yTr7+eyfFsmF1ZFU6jGtQxLPcKWp8SBkejhcA==", "signatures": [{"sig": "MEQCICxRqdv1smWG3/LszFYgkqv2fAyOHkgO453GED/+J45kAiAA9CWCd+PL79Nnl2yrAd1C5oo++tSK0J/mdGpAu9reGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.3.0": {"name": "https-proxy-agent", "version": "0.3.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "a0bc015df43de954e41fb7e55a2d93ca4be2b3a3", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.0.tgz", "integrity": "sha512-QMMtoSeRFH3q39cgnMNPKtHRyLB+kcXz89YqbQ1SLV1C0uFIZChZvgqUPxpF8gslj8EPe1blAhsVTIFbvRFgcw==", "signatures": [{"sig": "MEQCIGr5tTAMnwH8buM1l3YwnTjgJUsR6cUFt17dSYxpunucAiBTAoa1ybXGYZzWoITmk4O8EAGxNM7WOrcQ4xcPLJqSfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"debug": "~0.7.2", "extend": "~1.2.0", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}}, "0.3.1": {"name": "https-proxy-agent", "version": "0.3.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "24c95499957359e69b3ab8f061a5d2fa96ba88e7", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.1.tgz", "integrity": "sha512-2A85U9l3ubcsCYGR4Lde12Hf2lKPV7HXtYSZNygkFbGhDE/5QsL89e2IMhB3N4X0K+yDoU0YwgC1tPk6ASOT1Q==", "signatures": [{"sig": "MEYCIQDov7CPpJDeg7J73ZvyqxGp11DPn42eLqe3WZjG/dUtVQIhALjE8knSWk/GoTRwVBvDdZ3zwCKkUEqBzj9DEgQX9UJb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"debug": "~0.7.2", "extend": "~1.2.0", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}}, "0.3.2": {"name": "https-proxy-agent", "version": "0.3.2", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "fbe571fd2b52406faa8c5b77cd85b2d3241522e9", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.2.tgz", "integrity": "sha512-oqMw5RBH85DcXOze6XSsWhu2CqCg7DcH/TTRUKewS5/C/I2YH1WGHuzxQpruiXj3wWCV2e7sBpyBoM8Kvz9h6A==", "signatures": [{"sig": "MEQCIEpMOT7gUhR/ssZD98xjSelV/QIva+9UgW8OVguzb05wAiAELXJsF6QVv+0sVCN44vicjR3L3g77k4lTBbP792u8Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"debug": "~0.7.2", "extend": "~1.2.0", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}}, "0.3.3": {"name": "https-proxy-agent", "version": "0.3.3", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "00d5f4c1e656c8a9b2b1b479ebd417c69a16e251", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.3.tgz", "integrity": "sha512-TdYjY9pMwUdADfiHLhrL0roHEySW46vTqsDfJUt51fsllEG1Xrr6O7nfsZR/DKONJ4zCo3R158BMD2ov7pLlmw==", "signatures": [{"sig": "MEUCIQDI03DUaXbsAOOAflAeeoOBVVRxwImGCT+XXAojReuDMgIgVv0z8u5Q6H02ReFv949dAHysXWgV96NVyXWWmMRA6RE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"debug": "~0.7.2", "extend": "~1.2.0", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}}, "0.3.4": {"name": "https-proxy-agent", "version": "0.3.4", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "df7229a25ca7b446c677e41ee2b9dd60e5bd8680", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.4.tgz", "integrity": "sha512-OCxoWrj5EiQpid+BLxYOLdFcqE+i22ufjftHTTmAwWdbiGfiZUekSFbdr9CjN4v0KJediDI3CcPp+ltmsR9ofQ==", "signatures": [{"sig": "MEUCIQC95u9RTz3VwJn+pORqcZQLJy34Q2s/gR/qOWxgPHoBKgIgCQvzYm+kRDtk35u13jIy4/TJW7xM3xzCccqHPVKIcGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"debug": "~0.8.0", "extend": "~1.2.1", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "proxy": "~0.2.3", "semver": "~2.2.1"}}, "0.3.5": {"name": "https-proxy-agent", "version": "0.3.5", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.5", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "d41d43a912c0592f17552fc1a29cd484a2145648", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.5.tgz", "integrity": "sha512-1x3AWuqw03J1FinFWBoWONQFQOP+ejo43nFGOsEd2ifTdcSP83i0dT3z4gov22vePw2zw+fCb0yNa57gOCVYpA==", "signatures": [{"sig": "MEYCIQCiHB+f5JcMJA9cgmVZJ/lyB4i7wGKKllhjkbP5lx4pFgIhAPYYrZ5TlwVwphrY8jDdqWzLHdLyfkoiUUcAba+uAYL6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "_shasum": "d41d43a912c0592f17552fc1a29cd484a2145648", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "dependencies": {"debug": "~1.0.0", "extend": "~1.2.1", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "proxy": "~0.2.3", "semver": "~2.2.1"}}, "0.3.6": {"name": "https-proxy-agent", "version": "0.3.6", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@0.3.6", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "713fa38e5d353f50eb14a342febe29033ed1619b", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.6.tgz", "integrity": "sha512-ZuLafAeUu97abfbpAO9Cwjl3slsx6yZ7apTYBNVtMdoDhlVzUhxXO0qh+Xxqc5FAm7oq747k2jjbICYJdEYShg==", "signatures": [{"sig": "MEUCIB+mbm0ruSaMAnjy6Bp3oam2JwYylfEV6I8oqmyJ/9BRAiEApqWj5FbsliA26nKBq1COMCkqTDC8p3K0b+fHNOEzXrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "_shasum": "713fa38e5d353f50eb14a342febe29033ed1619b", "gitHead": "328299fa0481be2ba34d654dba9252494cf380c2", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"debug": "2", "extend": "3", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "2", "proxy": "~0.2.3", "semver": "~2.2.1"}}, "1.0.0": {"name": "https-proxy-agent", "version": "1.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@1.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "35f7da6c48ce4ddbfa264891ac593ee5ff8671e6", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz", "integrity": "sha512-OZhm7//JDnQthMVqlPAfkZyPO2fMhfHY6gY+jZcX8rLfFiGtHiIQrfD80WvCDHNMQ77Ak3r5CiPRDD2rNzo2OQ==", "signatures": [{"sig": "MEUCIQC5aD8hYmiMF2we8VMsj5lMVxQRXvIpgR8E42J06xB9zwIgY6TJbWYl3V1yizsadR08hcFVOOkln4Gp0/IO02B9a0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "https-proxy-agent.js", "_from": ".", "_shasum": "35f7da6c48ce4ddbfa264891ac593ee5ff8671e6", "gitHead": "cb7577b6aa9a2466ca7612b1ebd6fc281407187f", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"debug": "2", "extend": "3", "agent-base": "2"}, "devDependencies": {"mocha": "2", "proxy": "~0.2.3", "semver": "~2.2.1"}}, "2.0.0": {"name": "https-proxy-agent", "version": "2.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "ffaa4b6faf586ac340c18a140431e76b7d7f2944", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.0.0.tgz", "integrity": "sha512-Nbsiz3zjp5zmJHvbIY3PGHoxh3Y4q+wFAA2UHvLAPAa3K7yzSGgyW3WBxV05xJUb0K76KjDJWhb6CsYErwUHaA==", "signatures": [{"sig": "MEUCIDsz9E7+toHbrO5qpqGSsQq43g+u+YBTEQ3xYHXdRpc5AiEAzQnbiBBGDRwzhEuJSjdwYuwHks+M4HiXsHHHle3SBVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "ffaa4b6faf586ac340c18a140431e76b7d7f2944", "gitHead": "6c50a9acc5b00a2f559bba19c7b5d78120b0415d", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"debug": "^2.4.1", "agent-base": "^4.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent-2.0.0.tgz_1498523934878_0.9455580299254507", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "https-proxy-agent", "version": "2.1.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "1391bee7fd66aeabc0df2a1fa90f58954f43e443", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.1.0.tgz", "integrity": "sha512-/DTVSUCbRc6AiyOV4DBRvPDpKKCJh4qQJNaCgypX0T41quD9hp/PB5iUyx/60XobuMPQa9ce1jNV9UOUq6PnTg==", "signatures": [{"sig": "MEUCIC1eDnzN+zLbhJPslB6U7KCfmr9mHKEW5PH/G+r2uSLyAiEA4okQ+JY1roHPErwWNHAnw+d7PrdDlStxZiVncGxhwoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "gitHead": "5543d28b3c3b6519cdc7346fb517261cd47998b1", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"debug": "^2.4.1", "agent-base": "^4.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent-2.1.0.tgz_1502235155845_0.9402012808714062", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "https-proxy-agent", "version": "2.1.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "a7ce4382a1ba8266ee848578778122d491260fd9", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.1.1.tgz", "integrity": "sha512-LK6tQUR/VOkTI6ygAfWUKKP95I+e6M1h7N3PncGu1CATHCnex+CAv9ttR0lbHu1Uk2PXm/WoAHFo6JCGwMjVMw==", "signatures": [{"sig": "MEUCIQDDrZ3S5RazS77vhAj5QH9X0puvrRNotUx+zgDRvp1wVwIgQ24mEfhzwk2s802CDne39CFqR/8EPLM1cnX18iPmW1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "gitHead": "c58d365dd153104d1147967a0a6b4e1dd1698e50", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent-2.1.1.tgz_1511894451711_0.7408082666806877", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "https-proxy-agent", "version": "2.2.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "7fbba856be8cd677986f42ebd3664f6317257887", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-uUWcfXHvy/dwfM9bqa6AozvAjS32dZSTUYd/4SEpYKRg6LEcPLshksnQYRudM9AyNvUARMfAg5TLjUDyX/K4vA==", "signatures": [{"sig": "MEUCIQDZBmdovEhREJf1bjnJ3/ZvT79+Z+UKSJqHVbUCyh27vAIgONE+DsKuPNu1fLRMg7N3oW4HSwUEYprSCEoRLCAdpOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26412}, "main": "./index.js", "engines": {"node": ">= 4.5.0"}, "gitHead": "b9d5b7ec336264e9d8208287654060ae9a880976", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_2.2.0_1520105685829_0.726763197736217", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "https-proxy-agent", "version": "2.2.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.2.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "51552970fa04d723e04c56d04178c3f92592bbc0", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.2.1.tgz", "fileCount": 8, "integrity": "sha512-HPCTS1LW51bcyMYbxUIOO4HEOlQ1/1qRaFWcyxvwaqUS9TY88aoEuHUY33kuAh1YhVVaDQhLZsnPd+XNARWZlQ==", "signatures": [{"sig": "MEQCIF5WIj4bULTWmAtUh7Ci1msXDktTSIdPcObYMB1pcOZWAiBRsPiJme4OR0uLsSXp827ujGjw2cSsmUR9QI6WVdgt9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27578}, "main": "./index.js", "engines": {"node": ">= 4.5.0"}, "gitHead": "8c3a75baddecae7e2fe2921d1adde7edd0203156", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_2.2.1_1522310546565_0.5165395674470354", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "https-proxy-agent", "version": "2.2.2", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.2.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "271ea8e90f836ac9f119daccd39c19ff7dfb0793", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.2.2.tgz", "fileCount": 5, "integrity": "sha512-c8Ndjc9Bkpfx/vCJueCPy0jlP4ccCCSNDp8xwCZzPjKJUm+B+u9WX2x98Qx4n1PiMNTWo3D7KK5ifNV/yJyRzg==", "signatures": [{"sig": "MEYCIQCXDRr9cx8h7MDmUD7VgQhl1Nu0FsEKpeSixi9XHbt//wIhAMlAz7OobsgsSr+miqCkidf7h6FwcNAKky/0HqhSIEK4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIAqyCRA9TVsSAnZWagAAElYP/1rWE5pCCvQIyCIj4Dyz\nSkLsRlU+mzAa4wm5eMtYyAKJSaeSC9vzjhU408jokuAZTew3xexeKeZky2Py\n4/Y97oBdmVzCtas6vzy5lHYdmD9tiWnvq+PmPOnL1XwmCTuViDU4dxq0LIuh\n+tYN5JD6NBD3h3a8jmSFI2qmeTYz+g32OeZ29qlktfSSlUVJb9bXIsjs0bil\nAJSAnxeI/1CR3a30Rmvl3599vMEgYi6CASoI3Jy2hXWMfgO7XOONc0slzv3N\nK0HNFf4y8yM/yABahpL1ifoolXhYoQk5/uKSDJQzy0uWFW1wKPnRbFU/xVQS\nLGnSaTdXm4wLoJVxRjR3WiNGewOuyWzeKGIJPDjjY+ENVarnIeHY3a9JQzN5\n6ie1IfJK6ahrIcwCZ0oKRSU6Vgh6DSvm/XIo50kgY0xr40PAH3vSD1TdTK8R\njKFPhtG7dWfJhQB2e7D64xkA4ygHqY1d+I4/rCWP2Apfi4UcZ9c9D3u0i5WX\nkh8yG/PA4F93soOmS99vzBFX3W/g0Wp1Flhip93y0yE/DYugEVAT8d9f22s1\nM36trJk4R6z9IfhDL4Im6fZTS3atXnm/3Ozq0kKRajDASvtMh9r0fhWdFTmb\n618lZQSzMHj0bgoT+MsOcHqpCYDDzXRx2lgszLMFTDYaojEBItRhSEcgZm7W\nLDJz\r\n=Uz/o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">= 4.5.0"}, "gitHead": "e1457142b9978b7491af0c6a505985aea2aa35c5", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_2.2.2_1562380977572_0.40384044301767585", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "https-proxy-agent", "version": "3.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@3.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "0106efa5d63d6d6f3ab87c999fa4877a3fd1ff97", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-y4jAxNEihqvBI5F3SaO2rtsjIOnnNA8sEbuiP+UhJZJHeM2NRm6c09ax2tgqme+SgUUvjao2fJXF4h3D6Cb2HQ==", "signatures": [{"sig": "MEUCID5VqNJY864JjM7pLaRqvmZmwZy1gi6rmgaB+AW7UIt7AiEA5b/0HpKZH+3QDC/OmWU5KR4L7kauHdNikqeezabKzBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm5x7CRA9TVsSAnZWagAAvT8P/1X6lymzbneNPZ3a0+fe\n0KH7ioKBqODDJHl4e3yQbQPTT0h2BBFLcJgUgNeAjfVIM5aBG8wW2pbFDm1b\nPKC31p7W2+XPFdeaGpW1S+pVNeTjihzg2qvzibBNH/ItRZRtI/MW0qkBD4tt\nAalb4sd16m5gKJFcwADPH9eyaSy4NZ9G49oKQ/+Zy5gHjmdt9x2kowYIPY8l\nYPtT6ZGSQONu8LbAPOcWWZQGskpYsmFjMQUvcR/A3P+6su6R2OQPv+wvbpuX\n0cE8HZPw2LANjW8TXYdNN/aU/fLQ0zLBg7D3TkXyo01lzib2DDSnNuJvv4cQ\njAb435N4C2j28zFY52I5csFUu/jiR43h5IKXvY7CJiIQxC8wfLaZAZr1RL1L\nkce5F7DHBnvt7ns3MxzI0NhA0nytyHZonO6sF/lnHO63jScrezCeUWbAX6B9\nooICXq1NJQt/pDtzbhiragaEsRzbd828YBphUgLX4fDBnXMddMBbyH9xgyeJ\n7vJjLsEYYDSG5pc6B2lKdmKGtjQoEwk1w+eukYGZNX2m3lDAmF3GmIYU8T/Z\nATfkU2GHmnBEcpPICwqXARFm9cygJYjjocKrYbcY1iAqnJcW8Hb8+AD/cSs1\n4dWVuhw5+T4XSt/S+En27e0Km8cNEEj6mCh9zU8ccyk434kULzt1UVYVGiuP\nAeeo\r\n=KfVk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">= 4.5.0"}, "gitHead": "200cc9f18ff25e6cb8e5f1d61db5fea159a103dd", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_3.0.0_1570479226835_0.5288499934220063", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "https-proxy-agent", "version": "2.2.3", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.2.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "fb6cd98ed5b9c35056b5a73cd01a8a721d7193d1", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.2.3.tgz", "fileCount": 5, "integrity": "sha512-Ytgnz23gm2DVftnzqRRz2dOXZbGd2uiajSw/95bPp6v53zPRspQjLm/AfBgqbJ2qfeRXWIOMVLpp86+/5yX39Q==", "signatures": [{"sig": "MEUCIQDmFoqsZDiuuhz6Tonrpk3/srTSUCiBfQBjYj3gN8r0qgIgBxfYyJqQgbkz90uigGp3IhJfjwPp3DxqGf0+KX3V7H0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrlppCRA9TVsSAnZWagAA36cQAJKE1FCpND4Bmc/xJEfw\n8WVOVEKkHBXH20junIf4hBOApimz2+Efw1cDTNDWsghPSBlhMLFt4RVLFeAp\nFER7Ue/Kk5bue/+1x+8VQePXdvU1cYxtYW9cq5Xj5LY8F+0puEHrLI1FbJci\nK8NqJrkZt71IJMsH4xNcVDmMiwxei8oqct5mB1X2o9uhmn0ThXfFy0gVRVNP\noURlafNo6zLzI4gLRr85goHId92RUNjWrmdVLoKRKwrUMjqCsXzZtwWB2Vk8\nE/IdRCueUYmICXrImkba8JvPXNBc0JwnnyVItpphzIkh4RAE2f6+U3g003ZO\n589bm3RQMNGBefrUlDxddADX8YCWGuxzbzNhUuxWi94lo8SPPA08WDA9H7rO\neHnilbCVLxckXh83aFfitKo01u88nuG2fZTqqnGcLigTKxHbd3BLaw1dcGd9\ntOuiPuGZTLsXNe4h9x1r5rvsfqT+uA5rw6sN0j+ki/PBw86vpKv4Xz0Bx5zh\ndpAsg/qRZFty6DqOYy4/Gmi7xFPoJM7c7X+tBME1CxT93BSmHLJMVDmtSSsY\nIVDFU+B3yxPPMuDIyx0AOZRQPOe6il0GmuqPvvXlG6EYHUuRW0BAtJIgrhde\nFFjNE1QoMXcenwMaPpsubr30No6WGojOmUTz6P88fPn1pR6joCJOYBZbVaGv\nssPK\r\n=jB5V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">= 4.5.0"}, "gitHead": "0d8e8bfe8b12e6ffe79a39eb93068cdf64c17e78", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_2.2.3_1571707496608_0.4908296147655695", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "https-proxy-agent", "version": "3.0.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@3.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "b8c286433e87602311b01c8ea34413d856a4af81", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-+ML2Rbh6DAuee7d07tYGEKOEi2voWPUGan+ExdPbPW6Z3svq+JCqr0v8WmKPOkz1vOVykPCBSuobe7G8GJUtVg==", "signatures": [{"sig": "MEUCIQD8uVFzStLbwqmA1ij2NSx2kmtwAE2rHOiMKPdFgPsfagIgEvtEfMh4DqGUdT9UrPq3DCmFVoLZfSz1YqxledmD8GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsKYQCRA9TVsSAnZWagAAYboQAIj8j1jnk5/kHy+GdD6q\nWUSr2NcZNswLB9f3A8UjwU7yeWDQHG0Sa1H5VCBCm2EiNVjf2l5dh3EIITGM\nM2ubWJafV7TcdaTNF98H0oHVJ9LkCTHoock2DKJoJeEK/jE8F21sQBkGHjwJ\nDUIflZaL+zOoFjK5AqgiPX8m5nAJttG+h6RGGZzKJalbbDTHKPnuJ82jhXyC\ncbaJWh9K9Q18RarEFF1HMHfbXkWmXeiW/6lMKOyZ0DqNmih55suV02FIG62j\nwDi9ph75PFKcGtPhtxW/rfE6KemBd6RxbPMC2aXXuO7yULIbSXN28f00jkzJ\nfK+/XaYg3a6NAXZWmCoSlfLrcqpzKyM6CZjxLG85YlLTzLs0hmpW9K68g02w\nyYGcXm8kCyMtqFA13xqiO11QUDJdvmsJISqUiZpVzy8DwIY+39eaNYmAciL9\n/pKJT2j6nID+yalGte5+h+uogO3UNNmf77hgvabZ78lgq7llYwvnok0+pcRp\nSXFNgG2H5ynit6W49JZWUfJ8NVHmnWcHRKt3/XEHzM1onA1wwoJrwWcY1/jy\nFRv2/rnRQ/xWuh81YU1ZIHptc4GXAvjTjTu2kw4H2kYYhvOpfzinX2vstOGC\n11Qr6VWAzQ7Kwcrbjx+vtraEyLiilkyDhS3oxUSxwyMc3x3lC5um52AKq/KK\njJ6n\r\n=ubeR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">= 4.5.0"}, "gitHead": "c562fb665646e72e3285fe1e6df238a89658d574", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "proxy": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_3.0.1_1571857935665_0.858421721447781", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "https-proxy-agent", "version": "2.2.4", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@2.2.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "4ee7a737abd92678a293d9b34a1af4d0d08c787b", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.2.4.tgz", "fileCount": 7, "integrity": "sha512-OmvfoQ53WLjtA9HeYP9RNrWMJzzAz1JGaSFr1nijg0PVR1JaD/xbJq1mdEIIlxGpXp9eSe/O2LgU9DJmTPd0Eg==", "signatures": [{"sig": "MEQCIGb8ujHezqbUqm+FODCrmboSWRp8+GvVnf5xf0zHzyxUAiB6bxOmndPyd9mjv2PiCO8R7kOtqla7RSGEfO4oVNHclQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJds1dACRA9TVsSAnZWagAAGKgP/RKItYGuMXvo0v1opBH2\ndJx8uQHpNtZJjrzsV/uloabow+dzza7T2H+jVdcQWyosF6DWRzfejUpLZsiw\nXJDasoli1BjsHcvEfhB3vh75AqMSLchCiwbHoMEEZvOPEU4KK69YcP6QMsE2\n7Z2ZihrGJSZTlnZ+wgy0WTtjv0DXwf2YMa01TNJEpgBxHrIm8gcu+RgnGaUz\nB+n6uTjx0kyxnUwxsP5ugh0GwcvEyje2krZw2Vo8eHJpkl9Yk5dAeVmbrYOF\n1nxi0MOg1jatk/497vtAjnziP6tixc3FFnz2/ayuxXOezWa0L1edtmr16KrA\nsNyFcGb6O8kLjofIE5gpVOsKfjiBvfE0nrMT8PWfts4E1OBexLuziK0lIA/O\nm37Ox2gFOmo5lh83oTcBbfXHIs8Vz7qZw00sCGXlEkUI6+wQADngqmzkTbT1\nO4YfXLJNkgM49VP8+RARHWmOs25leJ8v8hsPGILfhSQowbSY6DGLEoAkv8uT\nDTpF9nq6jeEMvS1dN0LbEMihYVBsQSUvNY3/7Y3Uy3HfHb0tBMLgBVH6bikK\n/goojH14Vx/hQXqpwEqJtluidOqgjiu/Mk8zcYFyJduL+hz6HaYky+4Ius4Y\nmOxd20AjnysbvO6rplcsMtvHrJbnJ19ZF/h61CJikwEroDGzeDIhPchCMutu\nv8dL\r\n=iLov\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">= 4.5.0"}, "gitHead": "4c4cce8cb60fd3ac6171e4428f972698eb49f45a", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"debug": "^3.1.0", "agent-base": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_2.2.4_1572034367919_0.9750376921194577", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "https-proxy-agent", "version": "4.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@4.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "702b71fb5520a132a66de1f67541d9e62154d82b", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-zoDhWrkR3of1l9QAL8/scJZyLu8j/gBkcwcaQOZh7Gyh/+uJQzGVETdgT30akuwkpL8HTRfssqI3BZuV18teDg==", "signatures": [{"sig": "MEUCIBmKPyiu0frYmCml+V4gv4MPtcuWuwgAATXHuIunj9lXAiEAx23gapCfDSDaaiB9CGRp7zhPDCah0hkea0Al+93VWyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8AyaCRA9TVsSAnZWagAAlIAP+wWHOvui7ExnHmNvwpha\nJMe3kwMunYYA2X7+hmxOSXnAz3Sd9NB8xPvwWxR4Ebj2DlWewdZArczTRj6y\nFYSa5LWgGGgHql+I92d6YNWD65F7xTwL2qrqH5HeFgZj7J9easne2B0cQ3wM\nA3boNOOBPM5MxP70HsvtZTLNnGdksjd4MaYmMvZr+qlvm/DiLOhTWUnY2/XZ\n+Q4x1MUTgMJQE/ELVmAA9nzKnv45fnZB2zu1AN04rhMHSNJyJPOG8LbgF0L4\nZZzzS/aY5o19LUrOEB3G1M2Oy0Flv8K/NM7i++0ZjBlRx2LQLXiEYPJ3uV1g\nqo8axPv42uBElGMU5m42yPW4YcvN460aSGQq2mUL2M3TRvIcjTHz0BYkSm8o\n8PpZPmNndPFWcXvt/qI87BzdDuw25koeqt2eEd6w3dujJhGSHL2ArjqcjP4z\nYcfjWFfnHPsS90/kJjCoZqQ+rmOTx4bbc2ea3j1VZdKQqYqVxTPL1oSMBYY/\nHLv7yVc3YCV7JMLG4jwbn5xCmYYPgZ/zqHGLRfoRU7GN33TxHw826Nb0uYuG\nGLBK4TXc01L29TpENQ7BgA/UmTq3z/j3TJW97pCvMje00tntZqKZqGnLJ/yr\nZ2fvvPODxvn1RmupGNdfF2W4cYQzhb27zpkYLKHJ0Rx10RyHam4QmdtZyLm7\nhupd\r\n=ktWn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">= 6.0.0"}, "gitHead": "176d4b4fb20e229cf6cd1008f06bf97833fd725f", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"debug": "4", "agent-base": "5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6", "proxy": "1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_4.0.0_1576012954457_0.5260466677417492", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "https-proxy-agent", "version": "5.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@5.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "e2a90542abb68a762e0a0850f6c9edadfd8506b2", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA==", "signatures": [{"sig": "MEYCIQDvonm+lOlmkgcjLG3mS6GAzYPDCYdrYy0Kh5YC5RK/swIhAPXLmHUlRRHNtwr5jDTR1YZAgCCd8VfoCkIVMoIYhfYW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePcVWCRA9TVsSAnZWagAAlhYP/2AhfWu/0x96L5N3efav\nPCJ5cfcdx/WO0DzVCqM5dQyPS20KqXRfLFLJyzquskacdp7qwYtqkW0nM2cr\norgNpxpkCcCpFQIW9AqebEYlToJJFp1Qhqc8M9fNK7IKwnxQAIrjs60gwxhZ\nF93c2Fhr/q8K55ew4up/twLkgtxAQTOC/3Jkkmkubk5gZqgBzL4BXkj/bUED\n2kGEZLEJmRxURMbvnRabf6LIpB06srCl+vl0N410pYOygDOia+AsVTgmr89d\nv8fkFKtfNEwJDtD1vTlDRtpl/3CIxE4pyg5O9WyxKTXhYISOFlIqZjd551tY\nKPnAynDsPv1WnqPAj4QRzfyl5Q5kUpgFZ9GKTNxDmyRlwUzpx+LaB0QhD5FL\nHyGL3TfEEU/3wzvUrmHfJq+MROH4xMHuqKdFk0OeTD3kMczGc8WQm/XV3QFV\npvi+mV+E82gAF6U0ULTkKZtvLXWFdS/0SX6sWuTsg9TLjM2vmq3J8RYhDENc\nfYnrQBfuereNA5azHvSR8utTbeM5no/UWIDQbOTabn+El37QT1O33TmTQ/vb\nMpbS6VfPYY6lW/xcptWKgDbLmt7BZxR8JzKCcmxtP7IvDDpr5BHDOqB/hlet\nzb0Mx8Zd5YVSS48bEn+ljAbo5ParxI4PnVRf0uJAQFU7jr8oeC+bTlRONCwf\nf45j\r\n=erXH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "types": "dist/index", "engines": {"node": ">= 6"}, "gitHead": "8fdb1a5dd6a124951db39cb33f2438a89e0bb027", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"debug": "4", "agent-base": "6"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_5.0.0_1581106518210_0.20402709357454718", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "https-proxy-agent", "version": "5.0.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@5.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dist": {"shasum": "c59ef224a04fe8b754f3db0063a25ea30d0005d6", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "signatures": [{"sig": "MEQCIAX0ip0Ku5jpGaVVN/e5guGb0LZF5ysJ5/lBZsGMFRDjAiBI4+fcp/pgtGWTIMFBpL8Hj0bq2tSCUiLUtjMTB7cAhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWGr4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLrRAAnV1xcQL29ZKTpBVOZKXtPbM1HeDWeslZay5K7BNGdVEIVcib\r\nenaV7LvSuV4tsWmThRzEaCV8YpQ/rLmU3MhJAQ4gL36BWzyBZLbpH8GXA/ms\r\norVG3Brn+rsBn5qd3Rf1API4QEO97nb7dEqec1vfvneJ0I1Pj6bkYOp0GSeP\r\nL8reHwboA2R/rSnpDGphEDTRclUH3GC10YKrJ5+GJGQlwERf+FrdJlHtI2RK\r\nWXkras3bf3WTcZ0RucJOrJmYFkGT3jAlNmnnspzNB2TeXYIdSTOf3AV8pQYA\r\npqIj7f6UE0U782ni7b8tDnQ+dUSk5WQylF1zsCcao+wnwm+ScatirnIfMU4t\r\nP1ZdiwREgslVwMmKK+U5NjDOTPRJF+cJq9AnDsIcxhaCkpQDWseR4VfVnE0o\r\nAc6YIaEIWVdj+yGK8aXk7j3LYsK1D4fSBZxiGCXqZuOFXsBpwslA3dAMD7f4\r\nrZfVBiO5I660nH+aBn6T3doBGv6OuXjR+kn5MKWB8RNwymCNzrE+aeyjpVf+\r\nTBSSgdJEskMowLJe4D7yIoKfQDHJ+fWAKkOPNMV1SpZkY8Q5e2dmwXRSPfP8\r\nM7y2ghrIK5w8CwK8zucn3ul1111ZpM5syjtp79GquAYGwqwuiDwO/io0hCqI\r\nDtV4891qc3Mi6yTW6g7t/JKEQ1UlS+ktku0=\r\n=iREH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "types": "dist/index", "engines": {"node": ">= 6"}, "gitHead": "d0d80cc0482f20495aa8595f802e1a9f3b1b3409", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-https-proxy-agent.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"debug": "4", "agent-base": "6"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_5.0.1_1649961720594_0.7177664695298436", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "https-proxy-agent", "version": "6.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@6.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "036adbaba1c6810ce87216ed13cf698ec8672c17", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-6.0.0.tgz", "fileCount": 11, "integrity": "sha512-g821um/ZvXlENs8tqKh96b6G0wafab6ypfkZdFZImJEGZrn47oLeRhWMKvCYxrasOgNi3Yh6Cxkws2Zn13v2QA==", "signatures": [{"sig": "MEQCIGtJ5lhTZrkGqYsKhaKC1bAT/Lo74zmlY57mWm5V0ojOAiBulZ+RnRcqxd5XTiTjMGrYsqxNYY+y+YR5RpMk501Jjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8Hw//TUhpuvJNxvXRFzLRmseDOmAwBuGyjYsfEFMvB2/ZBXXiZFP2\r\ndurDy/kh5YFQEC3K85DRDg0kazG93/Aot+YfkC29l0xhyV+oZ+XdfKEp8O+j\r\nQtKTWFKVRzxFH4/5HpodgFEdjW2Rp6UUgibcH+VddILj9QnqsxWbbLwCEBNs\r\nZ+g8SeAiR5tWZRXSUqNUdMLgWls6mTcBv0HdyztyWs/XD+qlmOvqvzdjdof3\r\nGH4hpcnNrYqcV/kAyhm5nwCgC+8cXeZV5Px2XypfQyS34Fn1YI1WWN/JS92Z\r\n7l5v9V51wW+KJCK79fN8e7UE2YogV70qyYZFPpJpS3Ia8t1dWtR/o84Nj+vi\r\nP49Cah1sH6jqc1IRhE8+w6/9m0PgJ0q5LzWscCiJWBOXUIxDxMIEWr5N78Wi\r\nAYQ8dnQDuqdW6V/ubm0I9gOOYDg713ZBeEzwWZ008k2mf91O51jmP52b7Py1\r\nm9ZIY4+ioC5Gm/LVL6yBfNBSuznxVvNbnYGnUB8MnPp7Sp8/RV7JpeegAR8J\r\n1mOC6XiCR+EzuOvPEKVGeOvPl49On5/pyS5XjDBHkQ8v4Df9NAANp1b7pk4f\r\nJANYie5mcUxkyfYj+EAlihMlCrfDcraQOODsJg53BwbOocHNzhvLDXuhR+A+\r\ngbGwAD3c2fJZM5Y9Bdi5zVCZTLrZ042ZUoI=\r\n=LlBd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-6.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/2b1b67f3f6142559d3aaf8c5a2592304/https-proxy-agent-6.0.0.tgz", "_integrity": "sha512-g821um/ZvXlENs8tqKh96b6G0wafab6ypfkZdFZImJEGZrn47oLeRhWMKvCYxrasOgNi3Yh6Cxkws2Zn13v2QA==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.6.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "4", "agent-base": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^2.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_6.0.0_1683232399696_0.010486679555168354", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "https-proxy-agent", "version": "6.1.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@6.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "e00f1efb849171ea349721481d3bcbef03ab4d13", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-6.1.0.tgz", "fileCount": 11, "integrity": "sha512-rvGRAlc3y+iS7AC9Os2joN91mX8wHpJ4TEklmHHxr7Gz2Juqa7fJmJ8wWxXNpTaRt56MQTwojxV5d82UW/+jwg==", "signatures": [{"sig": "MEYCIQCfJVw0ptoTfxRewi4X9O0H4bjaXloOG0z8LEqIYj/LawIhAOgszZo+GnveXuUri3zNsCpxiG1YLtf5BU/nIfcUWokv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34504}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-6.1.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/250c446b8463b8afd2e7bae14150fee9/https-proxy-agent-6.1.0.tgz", "_integrity": "sha512-rvGRAlc3y+iS7AC9Os2joN91mX8wHpJ4TEklmHHxr7Gz2Juqa7fJmJ8wWxXNpTaRt56MQTwojxV5d82UW/+jwg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.6.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "4", "agent-base": "^7.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^2.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_6.1.0_1683324250258_0.7834208137850858", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "https-proxy-agent", "version": "6.2.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@6.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "58c525a299663d958556969a8e3536dd1e007485", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-6.2.0.tgz", "fileCount": 11, "integrity": "sha512-4xhCnMpxR9fupa7leh9uJK2P/qjYIeaM9uZ9c1bi1JDSwX2VH9NDk/oKSToNX4gBKa2WT31Mldne7e26ckohLQ==", "signatures": [{"sig": "MEQCIE7rtxXaj9dt3EZLp9R6PSvd4znf2OaO+NldfsRPqfbNAiBi8JjkVyCVbiJOX9SP4CoRYuHEIwZtBDiG3o0uOs9X8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34885}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-6.2.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/f8dcb3e5788b410b17908bf09c26bf3e/https-proxy-agent-6.2.0.tgz", "_integrity": "sha512-4xhCnMpxR9fupa7leh9uJK2P/qjYIeaM9uZ9c1bi1JDSwX2VH9NDk/oKSToNX4gBKa2WT31Mldne7e26ckohLQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.6.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^2.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_6.2.0_1684438283929_0.2928485102130345", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "https-proxy-agent", "version": "6.2.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@6.2.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "0965ab47371b3e531cf6794d1eb148710a992ba7", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-6.2.1.tgz", "fileCount": 11, "integrity": "sha512-ONsE3+yfZF2caH5+bJlcddtWqNI3Gvs5A38+ngvljxaBiRXRswym2c7yf8UAeFpRFKjFNHIFEHqR/OLAWJzyiA==", "signatures": [{"sig": "MEUCIGmNkv0v8mXGKa2m7LkJa+z83J3CKn4GDCVXeH8sOoZDAiEA3V/6s8RyX5xUJ73K+A9x8l1E7BQJFq7U3DHd8q9ywMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34972}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-6.2.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/e74649210c0d30d3a55211d70d4addc3/https-proxy-agent-6.2.1.tgz", "_integrity": "sha512-ONsE3+yfZF2caH5+bJlcddtWqNI3Gvs5A38+ngvljxaBiRXRswym2c7yf8UAeFpRFKjFNHIFEHqR/OLAWJzyiA==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.6.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_6.2.1_1684915915307_0.3856108065272996", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "https-proxy-agent", "version": "7.0.0", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@7.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "75cb70d04811685667183b31ab158d006750418a", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-0euwPCRyAPSgGdzD1IVN9nJYHtBhJwb6XPfbpQcYbPCwrBidX6GzxmchnaF4sfF/jPb74Ojx5g4yTg3sixlyPw==", "signatures": [{"sig": "MEUCIQC/XLDDRDf8SyZFjx6CkNuPG+GeVGboK0xc1Od7C0kSkAIgJlVhM3IrT1Gey4OWnXaPu0Wp5fvSlHM6+sC+8WJPOeo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34399}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-7.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/f0f11c9fa125e61e57609041ddb23641/https-proxy-agent-7.0.0.tgz", "_integrity": "sha512-0euwPCRyAPSgGdzD1IVN9nJYHtBhJwb6XPfbpQcYbPCwrBidX6GzxmchnaF4sfF/jPb74Ojx5g4yTg3sixlyPw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.6.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_7.0.0_1684974179969_0.7839919362984578", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "https-proxy-agent", "version": "7.0.1", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@7.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "0277e28f13a07d45c663633841e20a40aaafe0ab", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.1.tgz", "fileCount": 11, "integrity": "sha512-Eun8zV0kcYS1g19r78osiQLEFIRspRUDd9tIfBCTBPBeMieF/EsJNL8VI3xOIdYRDEkjQnqOYPsZ2DsWsVsFwQ==", "signatures": [{"sig": "MEUCIQCilg8KABNJhORBdvxh3w9vyxlxdjtAOrOQJ2yf8qg/gwIgXZz+f8R1s/3T/fAORPjyFXgb3huGQxPoYdOrS9/KlBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34457}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-7.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/4f3480c301f735c9e62fa7ea35493a29/https-proxy-agent-7.0.1.tgz", "_integrity": "sha512-Eun8zV0kcYS1g19r78osiQLEFIRspRUDd9tIfBCTBPBeMieF/EsJNL8VI3xOIdYRDEkjQnqOYPsZ2DsWsVsFwQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.7.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_7.0.1_1689017974671_0.3514824815405546", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "https-proxy-agent", "version": "7.0.2", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@7.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "e2645b846b90e96c6e6f347fb5b2e41f1590b09b", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.2.tgz", "fileCount": 11, "integrity": "sha512-NmLNjm6ucYwtcUmL7JQC1ZQ57LmHP4lT15FQ8D61nak1rO6DH+fz5qNK2Ap5UN4ZapYICE3/0KodcLYSPsPbaA==", "signatures": [{"sig": "MEUCIQCMPfNzixuLG6UUpXqZh0X3szvo3Q3zQszUqLJhimdJrwIgRYEFLigSxWtoE60CNgIk5gfvFVp51tpEui3Ef1ZLUG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35127}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-7.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/93dc39357900debe1b384fbf5a463e16/https-proxy-agent-7.0.2.tgz", "_integrity": "sha512-NmLNjm6ucYwtcUmL7JQC1ZQ57LmHP4lT15FQ8D61nak1rO6DH+fz5qNK2Ap5UN4ZapYICE3/0KodcLYSPsPbaA==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "9.8.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_7.0.2_1693814979144_0.2430773099728103", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "https-proxy-agent", "version": "7.0.3", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@7.0.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "93f115f0f106a746faf364d1301b2e561cdf70de", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.3.tgz", "fileCount": 12, "integrity": "sha512-kCnwztfX0KZJSLOBrcL0emLeFako55NWMovvyPP2AjsghNk9RB1yjSI+jVumPHYZsNXegNoqupSW9IY3afSH8w==", "signatures": [{"sig": "MEYCIQDTkD6889RNzZo5yuFP4D79l+mDruU9T3YbzdBiBJ3esQIhAJ6QGfpixR3ZNp3SF6YrCwxlgC5CzM53hp7hboihOggH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35291}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-7.0.3.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/cfab0f985f918976bf1320d7991f27e1/https-proxy-agent-7.0.3.tgz", "_integrity": "sha512-kCnwztfX0KZJSLOBrcL0emLeFako55NWMovvyPP2AjsghNk9RB1yjSI+jVumPHYZsNXegNoqupSW9IY3afSH8w==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "10.2.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_7.0.3_1707762279552_0.8572989034685161", "host": "s3://npm-registry-packages"}}, "7.0.4": {"name": "https-proxy-agent", "version": "7.0.4", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@7.0.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "8e97b841a029ad8ddc8731f26595bad868cb4168", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.4.tgz", "fileCount": 12, "integrity": "sha512-wlwpilI7YdjSkWaQ/7omYBMTliDcmCN8OLihO6I9B86g06lMyAoqgoDpV0XqoaPOKj+0DIdAvnsWfyAAhmimcg==", "signatures": [{"sig": "MEUCIQDyoL7nQuArTb0nvfi88zDY+FDvdCnEQUZ/N5f/APkM7AIgUkiqvmQhYTXROlBEnybz66orsb9Y4lhqR4zuC2QWgf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35256}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-7.0.4.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/591b0714b04acbac1c4451828dc84a64/https-proxy-agent-7.0.4.tgz", "_integrity": "sha512-wlwpilI7YdjSkWaQ/7omYBMTliDcmCN8OLihO6I9B86g06lMyAoqgoDpV0XqoaPOKj+0DIdAvnsWfyAAhmimcg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "10.2.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_7.0.4_1708024462970_0.2415221627294155", "host": "s3://npm-registry-packages"}}, "7.0.5": {"name": "https-proxy-agent", "version": "7.0.5", "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "https-proxy-agent@7.0.5", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "9e8b5013873299e11fab6fd548405da2d6c602b2", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.5.tgz", "fileCount": 12, "integrity": "sha512-1e4Wqeblerz+tMKPIq2EMGiiWW1dIjZOksyHWSUm1rmuvw/how9hBHZ38lAGj5ID4Ik6EdkOw7NmWPy6LAwalw==", "signatures": [{"sig": "MEQCIDFZmWUU+D3oE97K/cAiDmaGBWqWbaJGdP4cXqLZkp/qAiBUAt9Z2J0FL8xGKJYYpXlFk+nOjdkWPWIy0xRwtfTq6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34878}, "main": "./dist/index.js", "_from": "file:https-proxy-agent-7.0.5.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/5180433bb9463a934c98795a9595b7a5/https-proxy-agent-7.0.5.tgz", "_integrity": "sha512-1e4Wqeblerz+tMKPIq2EMGiiWW1dIjZOksyHWSUm1rmuvw/how9hBHZ38lAGj5ID4Ik6EdkOw7NmWPy6LAwalw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/https-proxy-agent"}, "_npmVersion": "10.7.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"debug": "4", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.2.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "4", "async-listen": "^3.0.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/https-proxy-agent_7.0.5_1719560028200_0.2922565123601131", "host": "s3://npm-registry-packages"}}, "7.0.6": {"name": "https-proxy-agent", "version": "7.0.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "https-proxy-agent@7.0.6", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "_resolved": "/tmp/6eec5581e8f502f3b759131ee0e89546/https-proxy-agent-7.0.6.tgz", "_from": "file:https-proxy-agent-7.0.6.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "shasum": "da8dfeac7da130b05c2ba4b59c9b6cd66611a6b9", "tarball": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "fileCount": 12, "unpackedSize": 35157, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQmE/GtFh47FHNYydO0kHVKVPJ3xHC8374L5yFaZwr7AiBPZDC76EJqXcrgPjMTGm9K5WZjFnK9aQJZh1/K2XXGXQ=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.6_1733542329054_0.7202664074443708"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-09T20:44:51.282Z", "modified": "2024-12-07T03:32:09.481Z", "0.0.1": "2013-07-09T20:44:52.547Z", "0.0.2": "2013-07-11T20:30:01.860Z", "0.1.0": "2013-08-21T18:49:18.059Z", "0.2.0": "2013-09-03T22:55:56.886Z", "0.3.0": "2013-09-16T23:30:53.989Z", "0.3.1": "2013-11-16T21:01:26.573Z", "0.3.2": "2013-11-18T19:51:53.270Z", "0.3.3": "2014-01-13T18:43:35.709Z", "0.3.4": "2014-04-09T23:50:27.653Z", "0.3.5": "2014-06-11T21:55:19.250Z", "0.3.6": "2015-07-06T22:53:04.798Z", "1.0.0": "2015-07-11T01:01:57.036Z", "2.0.0": "2017-06-27T00:38:55.004Z", "2.1.0": "2017-08-08T23:32:35.950Z", "2.1.1": "2017-11-28T18:40:52.995Z", "2.2.0": "2018-03-03T19:34:45.914Z", "2.2.1": "2018-03-29T08:02:26.610Z", "2.2.2": "2019-07-06T02:42:57.697Z", "3.0.0": "2019-10-07T20:13:47.003Z", "2.2.3": "2019-10-22T01:24:56.755Z", "3.0.1": "2019-10-23T19:12:15.824Z", "2.2.4": "2019-10-25T20:12:48.053Z", "4.0.0": "2019-12-10T21:22:34.549Z", "5.0.0": "2020-02-07T20:15:18.381Z", "5.0.1": "2022-04-14T18:42:00.761Z", "6.0.0": "2023-05-04T20:33:19.866Z", "6.1.0": "2023-05-05T22:04:10.473Z", "6.2.0": "2023-05-18T19:31:24.151Z", "6.2.1": "2023-05-24T08:11:55.474Z", "7.0.0": "2023-05-25T00:23:00.180Z", "7.0.1": "2023-07-10T19:39:34.860Z", "7.0.2": "2023-09-04T08:09:39.350Z", "7.0.3": "2024-02-12T18:24:39.723Z", "7.0.4": "2024-02-15T19:14:23.182Z", "7.0.5": "2024-06-28T07:33:48.371Z", "7.0.6": "2024-12-07T03:32:09.302Z"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["https", "proxy", "endpoint", "agent"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "readme": "https-proxy-agent\n================\n### An HTTP(s) proxy `http.Agent` implementation for HTTPS\n\nThis module provides an `http.Agent` implementation that connects to a specified\nHTTP or HTTPS proxy server, and can be used with the built-in `https` module.\n\nSpecifically, this `Agent` implementation connects to an intermediary \"proxy\"\nserver and issues the [CONNECT HTTP method][CONNECT], which tells the proxy to\nopen a direct TCP connection to the destination server.\n\nSince this agent implements the CONNECT HTTP method, it also works with other\nprotocols that use this method when connecting over proxies (i.e. WebSockets).\nSee the \"Examples\" section below for more.\n\nExamples\n--------\n\n#### `https` module example\n\n```ts\nimport * as https from 'https';\nimport { HttpsProxyAgent } from 'https-proxy-agent';\n\nconst agent = new HttpsProxyAgent('http://************:3128');\n\nhttps.get('https://example.com', { agent }, (res) => {\n  console.log('\"response\" event!', res.headers);\n  res.pipe(process.stdout);\n});\n```\n\n#### `ws` WebSocket connection example\n\n```ts\nimport WebSocket from 'ws';\nimport { HttpsProxyAgent } from 'https-proxy-agent';\n\nconst agent = new HttpsProxyAgent('http://************:3128');\nconst socket = new WebSocket('ws://echo.websocket.org', { agent });\n\nsocket.on('open', function () {\n  console.log('\"open\" event!');\n  socket.send('hello world');\n});\n\nsocket.on('message', function (data, flags) {\n  console.log('\"message\" event! %j %j', data, flags);\n  socket.close();\n});\n```\n\nAPI\n---\n\n### new HttpsProxyAgent(proxy: string | URL, options?: HttpsProxyAgentOptions)\n\nThe `HttpsProxyAgent` class implements an `http.Agent` subclass that connects\nto the specified \"HTTP(s) proxy server\" in order to proxy HTTPS and/or WebSocket\nrequests. This is achieved by using the [HTTP `CONNECT` method][CONNECT].\n\nThe `proxy` argument is the URL for the proxy server.\n\nThe `options` argument accepts the usual `http.Agent` constructor options, and\nsome additional properties:\n\n * `headers` - Object containing additional headers to send to the proxy server\n   in the `CONNECT` request.\n\n[CONNECT]: http://en.wikipedia.org/wiki/HTTP_tunnel#HTTP_CONNECT_Tunneling\n", "readmeFilename": "README.md", "users": {"cr8tiv": true, "itskdk": true, "keenwon": true, "faraoman": true, "limingv5": true, "fanyegong": true, "grumpycat": true, "mikestaub": true, "flumpus-dev": true, "joehancock95": true}}