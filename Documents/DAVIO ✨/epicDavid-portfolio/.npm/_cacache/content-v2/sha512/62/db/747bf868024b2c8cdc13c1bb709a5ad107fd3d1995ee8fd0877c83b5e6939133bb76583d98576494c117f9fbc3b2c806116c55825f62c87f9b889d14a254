{"_id": "form-data", "_rev": "146-931a6b7f9a28a3eca17db645ee4f9fc0", "name": "form-data", "dist-tags": {"v2-backport": "2.5.3", "v3-backport": "3.0.3", "latest": "4.0.3"}, "versions": {"0.0.0": {"name": "form-data", "version": "0.0.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.0", "dist": {"shasum": "c18c31c227bbb33b053217e8fec0c2255e06a1e8", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.0.tgz", "integrity": "sha512-tWZJTTXUMBG3OGHTOnqioxmBWoRePNJOAlGiIermah/lgFzAXMnEzSAJCY2PI0bBOfiTh1lgBPSvViTZYzUeYw==", "signatures": [{"sig": "MEUCIHIcrXnz6BPtiCQr0Q6ym1Pgo0If+EC4tLKURnFINIqDAiEAio/K3TvTv58XIgpvxfzx1OHRVGE6uhU9WJR/eTZ0IAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": "git://github.com/felixge/form-data.git", "type": "git"}, "_npmVersion": "1.0.3", "description": "A module to create readable `\"application/x-www-form-urlencoded\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "v0.4.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.0.2": {"name": "form-data", "version": "0.0.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.2", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}], "dist": {"shasum": "6d1470b4355088034bb28c0a5417845facf10068", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.2.tgz", "integrity": "sha512-eqyg6Clj1yljJt6wb9qf66B++Nrq6EYwhbflWHoVk5/Zy3AnfpFDlldL9GpKLCJSXEeLuGZkhRjWMk0A4fLkmw==", "signatures": [{"sig": "MEUCIHPoAhx/ssDmSlQ1JR/heLLWdbdugE1DlGCA42QCfc/JAiEArWdGogmQA58UauH6NcCZeUTGj0A5YdamfpK40LtI80g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "engines": {"node": "*"}, "_npmUser": {"name": "idralyuk", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "v0.6.15", "dependencies": {"mime": "1.2.2", "async": "0.1.9", "combined-stream": "0.0.3"}, "_defaultsLoaded": true, "devDependencies": {"far": "0.0.1", "fake": "0.2.1", "formidable": "1.0.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.3": {"name": "form-data", "version": "0.0.3", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.3", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}], "dist": {"shasum": "6eea17b45790b42d779a1d581d1b3600fe0c7c0d", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.3.tgz", "integrity": "sha512-+UDRxY5KMkUvW23fx4+oICuoAy4s4YRXsva2vQ2bFoU/RtrNeTB727dIPZt6GRQhl1W1mXI9BX+bYAJ2BopLiA==", "signatures": [{"sig": "MEUCIEm4dhMhM+eRdp+kD/IwMSJhFxhZXps4M7YeUl0BNA6PAiEAj7UsnK32jqiH+Xhib95CXjG7gd8HhXcnYPw0z1/dyPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "engines": {"node": "*"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "1.2.2", "async": "0.1.9", "combined-stream": "0.0.3"}, "devDependencies": {"far": "0.0.1", "fake": "0.2.1", "request": "~2.9.203", "formidable": "1.0.2"}}, "0.0.4": {"name": "form-data", "version": "0.0.4", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.4", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}], "dist": {"shasum": "564115f4a26826903510ec6a94488b4cb11ea317", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.4.tgz", "integrity": "sha512-ZsOJYryx6jikIr9mTBTgMwew/Ebkobi5ssCeIZ8g2ZsEU4oy8ASbNn27XPPJAam4Rhiy0wXZn009WeXwzYvYNA==", "signatures": [{"sig": "MEUCIQDDXogmFrBDEsKbVWrmN+VpvPhEPat9/L/aBm0+iRu11gIgOz1n7ihl+2Cr7PfVT6pON5+YJqFZRFvDFChQpCgMUE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "engines": {"node": "*"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.1.51", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.1.9", "combined-stream": "0.0.3"}, "devDependencies": {"far": "0.0.1", "fake": "0.2.1", "request": "~2.9.203", "formidable": "1.0.2"}}, "0.0.5": {"name": "form-data", "version": "0.0.5", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.5", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "2fe21ccdb8c09cf52d60f78d67f2dd242f2a6102", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.5.tgz", "integrity": "sha512-8pb178BuxUkO8Etx1MyqYFTqe804p6jZj2dC9wwmoOMkWD9QLKOKuFHOgqYlukawYQjY5eqxv9vzsDmk4rcgDA==", "signatures": [{"sig": "MEYCIQD6hl4gCzdpzBThPaVlbt99K1TiU/JsSQc109P0WFc87gIhAIRsfXhHF3NoZPaJhV3nnM0ZsSXXmJEZGthlZFQD7bDZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "engines": {"node": "*"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.1.9", "combined-stream": "0.0.3"}, "devDependencies": {"far": "0.0.1", "fake": "0.2.1", "request": "~2.9.203", "formidable": "1.0.2"}}, "0.0.6": {"name": "form-data", "version": "0.0.6", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.6", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "77ba50dea04bc6eb9bc7bd2d119e64b1f8070a41", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.6.tgz", "integrity": "sha512-0BZ4TJPFMMIBeCjiRpQUkZimbt2hgMXpovD9hq5sWdiOp+dIHAcsD5fyv9q+fIRVa0kJ8y02qKhK3rInyIXNNQ==", "signatures": [{"sig": "MEUCIQDDB6A6Q3iaWGySOaMXiJxkDic7ZEWnxd0S73qlyw25MAIgbQ2763P3mFQkEzZZOfYv+zq0fb2BptVy8CMiopis0jU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "engines": {"node": "*"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.1.9", "combined-stream": "0.0.3"}, "devDependencies": {"far": "0.0.1", "fake": "0.2.1", "request": "~2.9.203", "formidable": "1.0.2"}}, "0.0.7": {"name": "form-data", "version": "0.0.7", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.7", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "dist": {"shasum": "7211182a26a266ce39710dc8bc4a81b7040859be", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.7.tgz", "integrity": "sha512-Y9kJwAs/7CCx1nGC5nBTHiLACbNxlKIx/YUHprIqiDCo/i2eD3qtumFy4x/DbKINCLsBbwcGLEsFGrfGU9tvCg==", "signatures": [{"sig": "MEUCIQCvNJvvywyfFZg/QNMIpxR84Nm0N32E4nmBuWJ2Myq2jwIgPv+rpfygWJyW0dA/hnrjaqYKkfGiHfPVMKOHc7dVIw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": "*"}, "_npmUser": {"name": "celer", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.2.10", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.1.9", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "0.0.1", "fake": "0.2.1", "request": "~2.9.203", "formidable": "1.0.2"}}, "0.0.8": {"name": "form-data", "version": "0.0.8", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.8", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "dist": {"shasum": "0890cd1005c5ccecc0b9d24a88052c92442d0db5", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.8.tgz", "integrity": "sha512-yzpBIhe8Ll+dYTXjd+4ORxbQktke+abD0dJjedvqsVVayMkb+PgLGatJNLwo95Va75l3YDZ01SrouzyW9bC2Fg==", "signatures": [{"sig": "MEQCIAdw5pSfL2i4XmUJ8afuO/XhX/UajiMNGUypC7wqJHAEAiAQW2zipGvex+mxmdfpad/pwWYKOG2BFljAvoOBlgsU7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.2.10", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.2.7", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.1", "request": "~2.16.6", "formidable": "~1.0.13"}}, "0.0.9": {"name": "form-data", "version": "0.0.9", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.9", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "dist": {"shasum": "f229b4e39bed528c75836045a23bf8f8ff4db16d", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.9.tgz", "integrity": "sha512-6wrR4vIlNGFXMu+wC+JGEIHso4Wo8QwxaoN5DO0rEbN8y7N/nRBlA/KVTyu2FGJm6oUswpdCXSq7OxoBbERmbA==", "signatures": [{"sig": "MEUCIQCC7fyMazBLyLlhVIFGxdKkJa3/t2r7tDcyZGMwQZcgzwIgKiWAlQuuR19wYn+9tNBGONRd80ZfNDOD6Q9wpk4pPYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.2.10", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.2.7", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.1", "request": "~2.16.6", "formidable": "~1.0.13"}}, "0.0.10": {"name": "form-data", "version": "0.0.10", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.0.10", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "dist": {"shasum": "db345a5378d86aeeb1ed5d553b869ac192d2f5ed", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.0.10.tgz", "integrity": "sha512-Z9/PpT/agxXi80nMpOH6GFD7XOr6mwk5aWMxDt/KMY+Nm7e4FnRMjddM4/mLPJhpmp6alY1F/1JQpRE6z07xng==", "signatures": [{"sig": "MEQCIB/IgmqTRdhs0+jyUxP18yHeG3i6mFvr3Y7vEYUougcpAiAVm3B6qs/iid9C9uW9lAyr9UDfpP2NTLRWWDUhxZoykw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "A module to create readable `\"multipart/form-data\"` streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.2", "async": "~0.2.7", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.1", "request": "~2.16.6", "formidable": "~1.0.13"}}, "0.1.0": {"name": "form-data", "version": "0.1.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.1.0", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "dist": {"shasum": "d36b59baf9b292bb2e5034d7a6079b2bd1e9df83", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.1.0.tgz", "integrity": "sha512-YG8xdtf5UK8lcFeIuNF2Kief3BLvv16qShJkynAEwW70TJjxuLm8GC9IjrdmDrrqdr8rOZQQ56vx70sOWnT6gA==", "signatures": [{"sig": "MEQCICc0hyAZgejF9+OR+AkPMfDRBwM6M6iOy5D9abM4Bf6iAiBVPH15lGTblWovbcovUEwetElppTnYoOBca88b0dyYGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.9", "async": "~0.2.9", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.2", "request": "~2.22.0", "formidable": "~1.0.14"}}, "0.1.1": {"name": "form-data", "version": "0.1.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.1.1", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "dist": {"shasum": "0d5f2805647b45533ba10bc8a59cf17d1efa5f12", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.1.1.tgz", "integrity": "sha512-+jCaXVx4daeev23hAVaKzaU8Jm020oCDjjkO1yzkZgbIRUwS/904YH+GHmHgeZ5wrYifIpA7Gt/T/EvN/Zl+Fg==", "signatures": [{"sig": "MEQCIDK49s3mqrHfKpBpp3j2SmQKJz5NBQC/NeiZzv/DQTDWAiBXSe6N+2N2Ad5S2Kr87ngjjRZVZAUA3auDosdYJoOGkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/felixge/node-form-data/master/License", "type": "MIT"}], "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.11", "async": "~0.2.9", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.2", "request": "~2.27.0", "formidable": "~1.0.14"}}, "0.1.2": {"name": "form-data", "version": "0.1.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.1.2", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "dist": {"shasum": "1143c21357911a78dd7913b189b4bab5d5d57445", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.1.2.tgz", "integrity": "sha512-ynryYjLcBdZ6Iujum9BMhIRq84ViJBe85+gr5nL+gvRGU4Skti1fUwvggb0pdanQB+ji9IsLGimXUbJq22fuiA==", "signatures": [{"sig": "MEYCIQCmrpWOuYZbZHUKa1iNHQh+hgltR6alf1D2rtuaxTgBngIhAM7cOl/0Ps46ALd9r0j7LucOp9dReP9sG8BmPjka9Mht", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.6"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/felixge/node-form-data/master/License", "type": "MIT"}], "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.11", "async": "~0.2.9", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.2", "request": "~2.27.0", "formidable": "~1.0.14"}}, "0.1.3": {"name": "form-data", "version": "0.1.3", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.1.3", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "homepage": "https://github.com/felixge/node-form-data", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "dist": {"shasum": "4ee4346e6eb5362e8344a02075bd8dbd8c7373ea", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.1.3.tgz", "integrity": "sha512-khpfkwI/RQybQdwruvz89OCmcXiFZstZ88llcc552BrzvOhqIOHC6YCRJ44GLK7BRFBEMGH9zJ2zMy0nz27Y9w==", "signatures": [{"sig": "MEUCIBUEnlYLIf73mybvcYvnF8ItxDcpOJY+zbXHENsCoBNtAiEAwenbSJoFKX25PaGkF1Q0twRHDIBvaX39NuqVn/d4a7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/felixge/node-form-data/master/License", "type": "MIT"}], "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.11", "async": "~0.9.0", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.2", "request": "~2.36.0", "formidable": "~1.0.14"}}, "0.1.4": {"name": "form-data", "version": "0.1.4", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.1.4", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "homepage": "https://github.com/felixge/node-form-data", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "dist": {"shasum": "91abd788aba9702b1aabfa8bc01031a2ac9e3b12", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.1.4.tgz", "integrity": "sha512-x8eE+nzFtAMA0YYlSxf/Qhq6vP1f8wSoZ7Aw1GuctBcmudCNuTUmmx45TfEplyb6cjsZO/jvh6+1VpZn24ez+w==", "signatures": [{"sig": "MEQCIFLUhihBeJwhkJwdKze9fvQhuhB+WAMCN+GHr3wDAxkyAiAcZrjKN3FDAnCc84EeAMaaiRMY6bYk9g5rgsHcAHtvZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "91abd788aba9702b1aabfa8bc01031a2ac9e3b12", "engines": {"node": ">= 0.8"}, "gitHead": "5f5f4809ea685f32658809fa0f13d7eface0e45a", "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/felixge/node-form-data/master/License", "type": "MIT"}], "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"mime": "~1.2.11", "async": "~0.9.0", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.2", "request": "~2.36.0", "formidable": "~1.0.14"}}, "0.2.0": {"name": "form-data", "version": "0.2.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@0.2.0", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "homepage": "https://github.com/felixge/node-form-data", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "dist": {"shasum": "26f8bc26da6440e299cbdcfb69035c4f77a6e466", "tarball": "https://registry.npmjs.org/form-data/-/form-data-0.2.0.tgz", "integrity": "sha512-LkinaG6JazVhYj2AKi67NOIAhqXcBOQACraT0WdhWW4ZO3kTiS0X7C1nJ1jFZf6wak4bVHIA/oOzWkh2ThAipg==", "signatures": [{"sig": "MEQCIFb52AQltoNZ2viXBDoDE+GGNpVRucuZIWzm0qJF0V6EAiAEIqDYqhki9v70fy4vW5mhjP28TnRfQtj9ltgfPIz2Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "26f8bc26da6440e299cbdcfb69035c4f77a6e466", "engines": {"node": ">= 0.8"}, "gitHead": "dfc1a2aef40b97807e2ffe477da06cb2c37e259f", "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/felixge/node-form-data/master/License", "type": "MIT"}], "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "dependencies": {"async": "~0.9.0", "mime-types": "~2.0.3", "combined-stream": "~0.0.4"}, "devDependencies": {"far": "~0.0.7", "fake": "~0.2.2", "request": "~2.36.0", "formidable": "~1.0.14"}}, "1.0.0-rc1": {"name": "form-data", "version": "1.0.0-rc1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "form-data@1.0.0-rc1", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "homepage": "https://github.com/felixge/node-form-data#readme", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "dist": {"shasum": "de5d87ff28439596f4f5500bff58d1244d54793a", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc1.tgz", "integrity": "sha512-KVOHPgpHRLfzsIjcGyHN22MgwWSK4MaXPyURwv4rdUbY32vcngVUo7KqJvgDeYJ0eFnT3zM3x2H2JTuX9QKloA==", "signatures": [{"sig": "MEUCIQDnmMs98zEBCXf0ocXyJXjmmFhrKyeVxEKPsvmHyATaawIgftaYwp5MQ/MMTOFyPMEe9JcB24kjjGeBh9h5wTslJ4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "de5d87ff28439596f4f5500bff58d1244d54793a", "engines": {"node": ">= 0.10"}, "gitHead": "e6650a4c078fd09c130ed712848d71d8609c6518", "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/felixge/node-form-data/master/License", "type": "MIT"}], "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"async": "^1.2.1", "mime-types": "^2.1.1", "combined-stream": "^1.0.3"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "request": "^2.57.0", "formidable": "^1.0.17"}}, "1.0.0-rc2": {"name": "form-data", "version": "1.0.0-rc2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@1.0.0-rc2", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "homepage": "https://github.com/felixge/node-form-data#readme", "bugs": {"url": "https://github.com/felixge/node-form-data/issues"}, "dist": {"shasum": "5bc9c9b3dd3dec1977b0abf58790192081d95235", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc2.tgz", "integrity": "sha512-VMd2h5jDswSjdINqjjvjKCQO6EYbjpjBRuX5qEMuojpiN5jAP3NxQqY3AHUuumWE3OBw3frw7Y8v0c2e0Xvniw==", "signatures": [{"sig": "MEYCIQDSsq4qtaFnScAhWUnMnPfwPHEcMIgkLws/16+PdevTawIhALjJ87Ulx4Yj4SHbwRG0L7clSx7lE8arqKTbIkh4WqAJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "5bc9c9b3dd3dec1977b0abf58790192081d95235", "browser": "./lib/browser", "engines": {"node": ">= 0.10"}, "gitHead": "9f29fefe9633f3adae72d6416fd6822c060ff6b6", "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/felixge/node-form-data.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"async": "^1.2.1", "mime-types": "^2.1.1", "combined-stream": "^1.0.3"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "request": "^2.57.0", "formidable": "^1.0.17"}}, "1.0.0-rc3": {"name": "form-data", "version": "1.0.0-rc3", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@1.0.0-rc3", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "d35bc62e7fbc2937ae78f948aaa0d38d90607577", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc3.tgz", "integrity": "sha512-Z5JWXWsFDI8x73Rt/Dc7SK/EvKBzudhqIVBtEhcAhtoevCTqO3YJmctGBLzT0Ggg39xFcefkXt00t1TYLz6D0w==", "signatures": [{"sig": "MEYCIQC8oco29ZAVk5XP3hQcAu7SQgs14V4Q2rk+QuUyhazMXAIhAIJwUe57zDEqXCzhasfr5mqptFM4VtD3QlkFhLmg/kyx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "d35bc62e7fbc2937ae78f948aaa0d38d90607577", "browser": "./lib/browser", "engines": {"node": ">= 0.10"}, "gitHead": "c174f1b7f3a78a00ec5af0360469280445e37804", "scripts": {"test": "./test/run.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, "pre-commit": ["test"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"async": "^1.4.0", "mime-types": "^2.1.3", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "request": "^2.60.0", "formidable": "^1.0.17", "pre-commit": "^1.0.10"}}, "1.0.0-rc4": {"name": "form-data", "version": "1.0.0-rc4", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@1.0.0-rc4", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "05ac6bc22227b43e4461f488161554699d4f8b5e", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc4.tgz", "integrity": "sha512-HSbGQ2uxT2+Mi1E0KP5lM5lv/r88xkGCpiLdq546q/3vXz94egfIIBpY45NTWMjMzb4nLopsfkG1A3suNa5qUg==", "signatures": [{"sig": "MEUCIQChLXzA/mXEKobxaoHpt1cVkSXJiT4eLI1K1LkWsF2UCwIgJSW8dGZF5VQ4DP+Bz/uHDCC+FU9VGEAD8ANDuxj9TE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "05ac6bc22227b43e4461f488161554699d4f8b5e", "browser": "./lib/browser", "engines": {"node": ">= 0.10"}, "gitHead": "f73996e0508ee2d4b2b376276adfac1de4188ac2", "scripts": {"lint": "eslint lib/*.js test/*.js test/**/*.js", "test": "istanbul cover --report none test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "pretest": "rimraf coverage test/tmp", "coverage": "codacy-coverage < ./coverage/lcov.info; true", "posttest": "istanbul report", "predebug": "rimraf coverage test/tmp"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "0.12.11", "dependencies": {"async": "^1.5.2", "mime-types": "^2.1.10", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "eslint": "^2.4.0", "rimraf": "^2.5.2", "request": "^2.69.0", "istanbul": "^0.4.2", "coveralls": "^2.11.8", "formidable": "^1.0.17", "pre-commit": "^1.1.2", "cross-spawn": "^2.1.5", "codacy-coverage": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-1.0.0-rc4.tgz_1458059747097_0.14101114077493548", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "form-data", "version": "1.0.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@1.0.0", "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "2285b4456dae81efdbc391949347f4d3e2dce03f", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.0.tgz", "integrity": "sha512-QDT0s+89sJ37Z0PxJl0gB0FoBrogbSI+GgxBRMvPPXE8/1GgjHgEOFSdaMn1MXJhGXA0KCZzCV/BFpHxeEd9FA==", "signatures": [{"sig": "MEYCIQD2XGa+Tqxbp8US7A75FNcndCUQ4lXMPzulkDVDAnNxNAIhAKwWt0DpiBdrvWNxHva5iiK4wvmr59nN/yaA4ACqyZav", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "2285b4456dae81efdbc391949347f4d3e2dce03f", "browser": "./lib/browser", "engines": {"node": ">= 0.10"}, "gitHead": "6fd2c6555b5c685dbd791f364486df45472f70a4", "scripts": {"lint": "eslint lib/*.js test/*.js test/**/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"async": "^2.0.1", "mime-types": "^2.1.11", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "eslint": "^2.13.1", "rimraf": "^2.5.4", "request": "^2.74.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.12", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-1.0.0.tgz_1472202064764_0.1383217212278396", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "form-data", "version": "1.0.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@1.0.1", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "ae315db9a4907fa065502304a66d7733475ee37c", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.1.tgz", "integrity": "sha512-M4Yhq2mLogpCtpUmfopFlTTuIe6mSCTgKvnlMhDj3NcgVhA1uS20jT0n+xunKPzpmL5w2erSVtp+SKiJf1TlWg==", "signatures": [{"sig": "MEUCIGDMk1tL6IBIJuFSDCnOClhpcCSl38XriiZBX0/IVYNvAiEA/SQz3F5ZLpKR3uQTX40yROMNl4c+G3bYdVjP+fTrnv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "ae315db9a4907fa065502304a66d7733475ee37c", "browser": "./lib/browser", "engines": {"node": ">= 0.10"}, "gitHead": "158443da3b2ce221f0a06ccb3b8ab8c56b68b034", "scripts": {"lint": "eslint lib/*.js test/*.js test/**/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"async": "^2.0.1", "mime-types": "^2.1.11", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "eslint": "^2.13.1", "rimraf": "^2.5.4", "request": "^2.74.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.12", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-1.0.1.tgz_1472204677067_0.1879937476478517", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "form-data", "version": "2.0.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.0.0", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "6f0aebadcc5da16c13e1ecc11137d85f9b883b25", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.0.0.tgz", "integrity": "sha512-BWUNep0UvjzlIJgDsi0SFD3MvnLlwiRaVpfr82Hj2xgc9MJJcl1tSQj01CJDMG+w/kzm+vkZMmXwRM2XrkBuaA==", "signatures": [{"sig": "MEUCIGS011DbyqGb7fxX7wYRoJG3ZGuovDxUdEy4SEsGiCzgAiEAlUL3MW0zSgs0LwkvKKZ7iYCWDEMbaFWJouyP8/PGmWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "6f0aebadcc5da16c13e1ecc11137d85f9b883b25", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "652b16ff5b9077bdf65eb66b67286c823c2a1040", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "ci-lint": "is-node-modern && npm run lint || is-node-not-modern", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.11", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "eslint": "^3.5.0", "rimraf": "^2.5.4", "request": "^2.74.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.13", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.0", "is-node-modern": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.0.0.tgz_1474092617403_0.5404838663525879", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.0": {"name": "form-data", "version": "2.1.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.1.0", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "58870d83386cf0592165241a5380942276bb9134", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.1.0.tgz", "integrity": "sha512-xEWs0bJE3c5fHML4Na+f15lKgQ+s50bEIYB39gTjRE4kHAd5xKe1mu7sBABUG37jXOqXnJFhU+VdkGQnDsZz+Q==", "signatures": [{"sig": "MEQCIHCGEin9c55MuYEDkEHbu3mRkA6E8VY4mycTvxHO5RQCAiAyQ8XAGz5xiluVom6yMGusMWxKec9J5vI93G1uxGGvxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "58870d83386cf0592165241a5380942276bb9134", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "4718daefac2d16bfb3b32b9761c356cdd2461d71", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "ci-lint": "is-node-modern && npm run lint || is-node-not-modern", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.11", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "eslint": "^3.5.0", "rimraf": "^2.5.4", "request": "^2.74.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.13", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.0", "is-node-modern": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.1.0.tgz_1474841659621_0.4027709998190403", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.1": {"name": "form-data", "version": "2.1.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.1.1", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "4adf0342e1a79afa1e84c8c320a9ffc82392a1f3", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.1.1.tgz", "integrity": "sha512-UtB210Ec3l/KmwXZ5xdOdYjVcVtKbN1FZTivzfPKmn2SbniUyTxYYeMXwvVL3b+DdF4SvWu5Qqv31bXePkm7nw==", "signatures": [{"sig": "MEQCIGZ643iCd0aH9S0Ln8mcb8lnQ1RSsbx+a5tMrlT9/sypAiAYxtVbFV7Fut2XjoHujolSF7rMKuP5i4v0RfQTUd40zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "4adf0342e1a79afa1e84c8c320a9ffc82392a1f3", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "ebee8412f79798b87fd3ebed44748c1ca06fc1ac", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "ci-lint": "is-node-modern && npm run lint || is-node-not-modern", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "eslint": "^3.7.1", "rimraf": "^2.5.4", "request": "^2.75.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.1.1.tgz_1475562797683_0.23411617754027247", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.2": {"name": "form-data", "version": "2.1.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.1.2", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "89c3534008b97eada4cbb157d58f6f5df025eae4", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.1.2.tgz", "integrity": "sha512-qJCdAGsMWKS90wemiAqqkz+sz4pKoffKqOeVkmV83xX619JRoYJzqsZjoQ1qzGW8ZzmuhvHv4qBHxpSfKSwvLg==", "signatures": [{"sig": "MEUCIATVFRgKyeV7hnUlFTBIXt/HuyeFv2xAXVZqa2k620ZcAiEAyt7roXlsqK/msiyZlj3zhP9745jPS7Q2nidvKV8vTvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "89c3534008b97eada4cbb157d58f6f5df025eae4", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "03444d21961a7a44cdc2eae11ee3630f6969023d", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "6.4.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.1.2.tgz_1478577739404_0.6574864208232611", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.4": {"name": "form-data", "version": "2.1.4", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.1.4", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "33c183acf193276ecaa98143a69e94bfee1750d1", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.1.4.tgz", "integrity": "sha512-8HWGSLAPr+AG0hBpsqi5Ob8HrLStN/LWeqhpFl14d7FJgHK48TmgLoALPz69XSUR65YJzDfLUX/BM8+MLJLghQ==", "signatures": [{"sig": "MEQCIFCOmFY0O36MdIxbAlkItXnjCO2maZMl6kLcsIzdogz3AiAU/NbHRQf0Mx6o68abb/H4Wejsd8m9BuUr7Vg/uW6S8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "33c183acf193276ecaa98143a69e94bfee1750d1", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "d7398c3e7cd81ed12ecc0b84363721bae467db02", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.1.4.tgz_1491750597266_0.5097400255035609", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.0": {"name": "form-data", "version": "2.2.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.2.0", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "9a5e3b9295f980b2623cf64fa238b14cebca707b", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.2.0.tgz", "integrity": "sha512-eszcMNxZ9pMS8vU1F6tXl82DNfIq+XBc3D0MebyJd9Pe1WoCol0Bwv/v2uQhV4F1xBmjAEY9DQp5LvsevzE2oA==", "signatures": [{"sig": "MEYCIQDHbYYzTXgifsoYPPWp7pCKn5HY4jskxi6TUoWEj30+AQIhAKOmlp2qNloOlA1BbyE/YUIxZECf2WMpXIxSD+F0NYiV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "9a5e3b9295f980b2623cf64fa238b14cebca707b", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "6edf2cd4fdf4e61aba23bd538025fd8746a94fa7", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "4.7.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.2.0.tgz_1497170760607_0.7442727568559349", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "form-data", "version": "2.3.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.3.1", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "6fb94fbd71885306d73d15cc497fe4cc4ecd44bf", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.3.1.tgz", "integrity": "sha512-ZznzvgkNMfVvSHP0rlg09OeW/g7ib4+NpwNGxLFJOrwUcjN0O8OUASn5cvnpnWve9ZlzW6GUa6NhhlCdb6DqCw==", "signatures": [{"sig": "MEUCIBXOKUBAE9lzviSqbK0x5rTvDCvWwRvskWCwGibqwgdGAiEAnuafyffI2H9FJ18h5g9VscpDwUx9QZS7zcq1/qVI2bA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/form_data", "_from": ".", "_shasum": "6fb94fbd71885306d73d15cc497fe4cc4ecd44bf", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "7629e30d4175fa07965a59f70ba5022172f9494a", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.5"}, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data-2.3.1.tgz_1503615654461_0.18250337382778525", "host": "s3://npm-registry-packages"}}, "2.3.2-rc1": {"name": "form-data", "version": "2.3.2-rc1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.3.2-rc1", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "4d2cac539eec1ac2a8cfb27a61137a3b546db44f", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.3.2-rc1.tgz", "fileCount": 9, "integrity": "sha512-6xnzoVpu2mLUf4EymxYh2bdM7XAwNqesF9BMa1HQ40BGdCyQ3RpsiESlblj6JxWttlMw/qDXaXrAs54BrjaecQ==", "signatures": [{"sig": "MEQCIEjcvfG5/30aK8Og3jv4eRgXumoyC5ZgEylXaC/1h/YIAiAYjueP/xPLcHGbEOy4yc1frLkY0b13dW4c83Pp1OVcGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30180}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "licenseText": "Copyright (c) 2012 <PERSON> (<EMAIL>) and contributors\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "1.0.6-rc1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.3.2-rc1_1518458302643_0.061536252107149414", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "form-data", "version": "2.3.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.3.2", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "4970498be604c20c005d4f5c23aecd21d6b49099", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.3.2.tgz", "fileCount": 9, "integrity": "sha512-6DD2fGWwyxCca2EASUT50GsxWEuwNQDpjMhD9TTaBvI1NE3nLkCr5v7nRdtlmG5g+mNqosdOVHVro+UGmp0Kcw==", "signatures": [{"sig": "MEUCIQC1l0o43F/eWRRiXLYE1PCuDx8powvuSENX4N4D2kDgTgIgeDNSJmKNnHdPO+2JF3GA8TcsBupZkZqsFs2zPO2DI0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30150}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "licenseText": "Copyright (c) 2012 <PERSON> (<EMAIL>) and contributors\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.3.2_1518568309628_0.869333162629065", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "form-data", "version": "2.3.3", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.3.3", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "dcce52c05f644f298c6a7ab936bd724ceffbf3a6", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz", "fileCount": 8, "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "signatures": [{"sig": "MEUCIQCgZCF/Spg4VA7X4Q5OzC0gG2XMk9Y2K9hQhd8Yj3PmggIgDW7D5Q9rGkVZ8CxMrfWRt7jsBFDHh+nKPspsJafGLMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxuQfCRA9TVsSAnZWagAAiRgP/35Rr4anNT4tkKaF5BjA\nqyn2Rqik/V1A2uEXoVRZjwFAn4c3xH8riq4suJeOS0LbjaPoj6pWJXdiYkdN\nT2MCeTfUsGbT+LY5s2WOXQsDD5I78MzG88jn5QLSiNc72kbqmCAw2lDTr3b6\nm1ds6tFRb8xdkSGloXjpZ7BY/mex1Mkx+MS9pcXOVU9K7w8NbFXzRGO+A1Zh\ng5bQvcWjm98IjC6PRutLhbp4g4v502zpdYDRXtVW6w5fnC7uN7Mcv2GraRqN\nFIGEDhhcTeGomrb62Ea3imTiTkUliRvE7tSwGPJz894Rq1ej66oP/wfkF3yL\npe0DEAHlpqoG2328Fkcrzy1+dKxaqSqGf2LSGIPUNrjF76bPY+3VpXAcMS+h\nGK9brzI9I5KAfdJ6Gbgj2IX/A6xtIV5Ru7/kc2diVFIWkZXGaIQZVmho7Xkz\nMc3kLnIzT92b1xkk4bxiVFnLq7UaiheTEUxn8A1V8k1wWLMebJfndK3fMKJ8\n0fZKgPaPk+qG9J9Ld1AZWXZPg8nO06PkpCNwtb0TaUP7j2meQzjRqrCJqz+c\nuQwFbhQqEsT6TKd98dY7tcKKQf4t5hWL8InjeiqQ3+fe5Yehn264oKnJcnTo\ndHIKlpiqxdP9Natp/cCM6zCYcORN1mEinwf8zalChSk+XVCQ3NGKsHWywVc6\nZCxO\r\n=NorT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "b16916a568a0d06f3f8a16c31f9a8b89b7844094", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.3.3_1539761182623_0.0008897599197468242", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "form-data", "version": "2.4.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.4.0", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "4902b831b051e0db5612a35e1a098376f7b13ad8", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.4.0.tgz", "fileCount": 8, "integrity": "sha512-4FinE8RfqYnNim20xDwZZE0V2kOs/AuElIjFUbPuegQSaoZM+vUT5FnwSl10KPugH4voTg1bEQlcbCG9ka75TA==", "signatures": [{"sig": "MEUCICQP5KBOc6NYSRO45Ciddh8kCyfAMdtTE7ZgLSAhuBrxAiEAmYu5FQ1QNC1/LryJdLmWXgYGDlfpBPTTD3KjU2ftKcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCePLCRA9TVsSAnZWagAAwsEP/3HA3hkx7sUGw/n2uu+m\no6c5k6O4mfU85NwHSNfw1Ck0JZFXwHKPIACNJJVGAbWJezFyf8NYaYxiz+st\nge1pMsT0rIgpqLfA4xgoLwUyKokqmPesMJPB4e1USuHRHuoDmoy5MmL7X9l8\nPXWzDG9V7UnJHiHyv/fOm9f7rw/sGdPMOrwRMSW1+mySXl7vCtWXoiebzsoR\nFQ2Z2RBtPOEm0O91InbM0D3wjZHAPMn+aweruf8hVT42rRUb8NtL7NByHZRo\nqr7iULDrKAjPGcRgjCHFvtvxdqrryxVy8Q7TMF2avE7VQUEOjGUY6Nc99lSW\nF6Ds3Eyk3Kr2thrIRzdVhK5XH16ybfwlqAFtyjFvT7R+W5FPBUaMBwfgk6vr\nqBKAU2FL7Flot2PANgv9V7XhBrBJgOonaHbtYCldVDBS2aEYnXEQLJDIphd3\npDbosxqZPw+dci9B8aZsURTvyQ4bOexFNpvVceR0TT2uKVKgaIhzpWlv863/\n59Oxq6XIIhKbrdowlGu4pVnCNPWRSer0fDFjKmE3x3+9W/riPQQoVkG4/DpW\n2lTNg89a68hRrYKdsAByTESMifijxsJAiSiZ7I+U+U8qbVDFpNdKfihfr5g9\npeHVWFjFfDKl65dvEf3Wg0BGAbvS3XyqqclN6p1cErdPsXEhZ1hC5NADKLC9\n/czT\r\n=Q+UL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "8512eef436e28372f5bc88de3ca76a9cb46e6847", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "11.10.1", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^3.9.1", "rimraf": "^2.5.4", "request": "2.76.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^2.11.14", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.4.0_1560929226437_0.46886561193583787", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "form-data", "version": "2.5.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.5.0", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "094ec359dc4b55e7d62e0db4acd76e89fe874d37", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.5.0.tgz", "fileCount": 8, "integrity": "sha512-WXieX3G/8side6VIqx44ablyULoGruSde5PNTxoUyo5CeyAMX6nVWUd0rgist/EuX655cjhUhTo1Fo3tRYqbcA==", "signatures": [{"sig": "MEUCIQC4NtPobDwYIcptzVoMFZMZJsmaQpEOen4w3pFLmGJgfwIgVmbKUiX6YK5t9YYCwNhFW+4GhnJREYgsFdfhbCnXrV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHYQrCRA9TVsSAnZWagAAbnwP/2DoqmD3laXNhcTL3g8F\nQ5d2jqn4kLSLh/cf3i1lmpKzpUvQu5Q2QS8zYZ/8smk+OSzwto3mBvO06ooM\nNXPPSc9pbruWimcjAMngx/mjUoLCtLtXYmx5M/c4TX+P9iI7bVmdDYDm7PKx\nBeVzBMGGkweJTEd42+6Ayek9D4b/hw5HbZog8TO0bOYreVXfGqEebRyUoor9\n31OdClzB6jdqshub2K7zDLxKW1mzMeg4ptF6wresADclYaR3q7t/X0aO1M2n\nzQRJl+gFNGP0cEuuf73+h2netWpyVKu9Ve8kkEW+6i1K2nxySkKhsMJqcSXq\nyRChBOI6jZCy+dZMMsaDE2WcxlGl8Us0Hn4tpeq5wAPd49WzIm4lWSrSr7wV\n6alyHoTi2CK/hgsz5sotOUNYx5K3ets/rm2vJFt1TibhH3onHNDovgvf91Df\nqh+iXgrbcRb5yn4BpFXSBq0Jmjppsk7idZLXUFFXb8mR96UOqrcR7qooaGlB\nAKl+MWJXq3P+4n2kUvEc1TWixzBenGjVUTkQynOWT+/XJnvhyTjX6oJG41V+\noUw5Uwk+fMkhA0HM4BAVElOIIyGZ42XxSRrnNlVnj3bg4wHgL2Z3BTWWnieI\nUaMXjDiWQkPh7wKoWMMyas5wTLzPgN4bQhtqCJc2YwsGHpmEsYm4iEg2h7mr\nRboh\r\n=MPlV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "905f173a3f785e8d312998e765634ee451ca5f42", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.5.4", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.5.0_1562215466927_0.3140293290591394", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "form-data", "version": "2.5.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.5.1", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "f2cbec57b5e59e23716e128fe44d4e5dd23895f4", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.5.1.tgz", "fileCount": 8, "integrity": "sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA==", "signatures": [{"sig": "MEYCIQCx4kBFuZNvkAxrEHj5veG6mqQuyhcZR+AOHG0q9yKCnAIhAOpn4z6Nd2qnqehrgkUMlMLxynhK6V45lga8Hufkhpl/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZ3VXCRA9TVsSAnZWagAAuQoP/2DBbsCYCzkamVIA8L0n\nIlIyMQFSWRRNSnobSmN7yEmmbJ7ulNu3X4ucDiFAjXslxKcAxosu9hplF9Bj\nR1Dya9R8FEPkSN92I0a8TJD6NxmFom6E9z+xJX6pdDkrDv7nD0oGKbDz0rb6\nkFXY+J+qoMmuzXNJiTBKDvNOK5s6DgSQVLcTPuz4zkybOojQTVsaZ1Yjr3eQ\nMT3grpC/KUb9aBHqWF9MQsfQySuGGvCDky8M6VzKsXRSs9qFYQSKPzzQCC3x\ngJ1ZR4ut0eTP8fEE/shr3KXZUi9PSKiHLnz7sc3cHITuf9dTVV0kfMU7BWYm\nwpXlLSLvfG0GbC6uCXVKNdQFE0KAk1ogCeeA56npPz1Cc1Mg4f8aNF69cT0U\n7iAaDotopO5zu0s+2KfI+gxXqowp/r43r0BwUu9nRw22WYOxHM4WotuBxS0R\nrlqY0HJYKnN36+mXk3Lq/R8XBawhM8kS7FPlBASDsz13in5zIBELcXbiHEwX\ngLQ2o1L1XaqMFqjpNlJJpndY46+0vTibiTK4Xd2FEAcwX1MTguw3Wl0EGoZ9\nR0LU1eDtseZ3LxAJN8UpTRm0UzNeTVLCNXEDssC8mZtA6H/1PhlkQtGhEUDR\npo6Y9thr35ACKq7W3+APZ0AZjX3sATC0YP9vFGNt5kc+OgrjuVacGRkZk4qs\nZruz\r\n=acOe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "8ce81f56cccf5466363a5eff135ad394a929f59b", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.5.1_1567061334343_0.40267661488012263", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "form-data", "version": "3.0.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@3.0.0", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "31b7e39c85f1355b7139ee0c647cf0de7f83c682", "tarball": "https://registry.npmjs.org/form-data/-/form-data-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-CKMFDglpbMi6PyN+brwB9Q/GOw0eAnsrEZDgcsH5Krhz5Od/haKHAX0NmQfha2zPPz0JpWzA7GJHGSnvCRLWsg==", "signatures": [{"sig": "MEQCIHX6L2qeS3FbeKfxo0mO2j7seyB2LQA8UtiK0oD1EEosAiAJqgj5b3uodpd2uy8wTiwQn2tF3hxH+52ZTJxS2tt/bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwnzKCRA9TVsSAnZWagAArjkP/1xHgToPzckLrz0QjGUA\n3SplmiwrwOlbrHdet95fMQI4Sq7SmFWMWAvCiGiKOfKEgiAnbjDkLVp1wHSe\n+m5xEt7Z8l820egr+JDf0nCXI8XOtgH/l9yEt1bcyvRXabCriZM/oKxr9xgN\nPT+/prrIlYUaJpCvIeH+4XqnFwbO11+EntkC2ds2kvUQDCsW8DEPZ6FNSFXn\nJNsHDQgOi7lU5wrKSUYLgDEDATE1rlpUjjUVbsGPrM4XNqKc8UilQYs1b8Uj\nfWUTcDQIwyEja9YzXuXXbQOsIj+WP2zXK1VJh+3IcJp396pqLrAbpVa+xKD8\nBaLNVZ7olMTPJOlGRL9P44aDKTL4DfYv+kuQtYuygCzE5qSFddInL56kaiWp\npX0laDPzDt3DDMUj8AMKNyPRrpXT+Twhp3hvbfh7dvgJmf7TFFLh1iVlQU4w\nU3/gyzPu4//UM3rx27Mz3KDnMq1bgKjlADOOTIyq6qpErHGoLWeFVRgtgIY8\nblQJFdPEpzZtpjUkyPAK/EhAdyK9sv8p+96yYUgbAAhYLk0DB9ZmnI+F1z8g\n/ojv78e44qOPCsGcuTRdwHjS/j9gFLgMlaiRoRTHc7JNn3XM9JURxBSkKRgd\nyVWBe6FhLUVZQ7IrhDI69s3pUZ5dTO6+M6jXhVaQaoQuEu6cHgmPNl8Kxh60\nx/VO\r\n=7wkA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "5c9b3a94aad8482ad8126cd7405a3060cd8e022e", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "puppeteer": "^1.19.0", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_3.0.0_1573027018258_0.8075773865628744", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "form-data", "version": "3.0.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@3.0.1", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "ebd53791b78356a99af9a300d4282c4d5eb9755f", "tarball": "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==", "signatures": [{"sig": "MEYCIQD8ToRVeb9Do7dVu5ZWfk21nswNFXTbbl+at8Pv5gc13AIhAOevy/idYv03N3RExg5mYjvsRBdbuHOzGLtdQC7kfH9M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKrCGCRA9TVsSAnZWagAARKwP+gK/94Q1j+b6jTvptJtx\n1CwXewRBe4UYZle0tLUZ4XnMXQUP9jg1gysVafigTvGmaQlM8VidLzQohLce\n1nM7A0rPOjF2ATbkPH8jUZnVCV081Q07LHprnaC9p3PkuIc/Q8kJJLM4/kH/\nKvpuPq3IRta1SzY+bi86mBEN6GQ9YWv+ATyE3i7gefeF+mDf1dD+H0dydMOz\nktFiixOdEEHP6ioMwJIbuNLs8w+LtP2Ngki5jRNK8+4lEsKBnwpy/zMsrqva\nzwX2P/mM5UjNrcyiDqvj5AZqw3+4g5QKZzChyUWGmkiCF5g4L1CF/sLxp/tA\nJOY131/T9cfn7QB9MzeVDj4Fk/eG1kSxlUId8cG80FjWzYgJs4glJqSa5fum\nKBRmetVwLF88U/g8YZlAFMUQ6KHMUaiI36hZhz5qQ3TyUxInNOj0Ga76quGi\nNS6ACqLB1dTs00ShaNsOz02YsAMnEEYdZMdqYXRWndk6Z5njz5e9JnOWLAMB\nhBW82n1xnvlxKmNfKrgbn3a17yOUxEf+C/HdFOT57h6s6bpkqvPk0URBF9Tk\n7nEUbFhuglFGLD9zfRsK4vLw2rpU2JARYTaF+1KUlA4UDifP3hrgIeM9IJHS\ncjG3tRqe1qVArDahWiHqrLcQHg2k/YIRxZxxseioLoEAVLas6Zygkk3dDZ00\n7OMy\r\n=j43U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "c72ad6728ae394e55c0f7a025864429edd656879", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "puppeteer": "^1.19.0", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_3.0.1_1613410438253_0.6573676413560436", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "form-data", "version": "4.0.0", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@4.0.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "93919daeaf361ee529584b9b31664dc12c9fa452", "tarball": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "signatures": [{"sig": "MEUCIH7WD5apin6s0wQkFp+WGhmqZ8W0vC4YeWhfXxSX255wAiEA8VdxUC00NCkR7CMut8QTU8vm0+DZ6UuzNIYDha/+0wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKrGvCRA9TVsSAnZWagAAOQMP/3vh/yE2RCY4rnpkUZCw\nVknqEhD9kL80TPX0ziHUhTN76mF75hBi9d4+GlkrLEnVmI+bKjQdX0jUAS1S\nLGSwqyfhYeckCg1u2FwBdkjaJCx5RmwwyQwZb728dhgbYRmTwEh5N1JHO/6Q\nJcP5n/nSILKWn8GyV5210prixhzXkV5Mjpy0nnI5wziSdFcFoCjIiZ3+6KqT\na482SwjJxscjSwUvE9RZSri5CUd1YMmd3b+N9LPJuC13Kw7U1PR68nN7xaRI\nj3oMcdqfZBvgKjZzEUVsSmZd2fuaHJg+n7sIymUDoMwGIJfqeZHdZdPIWqlg\nw7rK8ynv2kgkwmea3LM4j83VstnalKhkKLFCq0AnH08bl7+yvUX6HdR68EWM\n1wbG5ksnpR5Zj/ghXe/NEVLhcH7PEi/g7ZlpkaYl5ekEC0vAHJ8qCz+J5x8h\njChCsdKlWUnA3qW8hKQB+rj31cDsqTr6khXA6EH19nb/CepQW6zgtnyAea3G\nt4/hXHo9DasEpC96L5/GTKf2xo8u2ZdVs167yGMjQgwft1p/+z5niFS8SsX7\n+m1PyYujfQT0uz82GUUeVqYzTZhCVQK3sNaX9g/+jqUWVP3aZo/BP9QII8NI\nugRhsnWhqHR2gqTsSleU9T5EdLyhMQbPXmk388ZAVdVbKggXN8zsBwnGL3Ep\nC/12\r\n=9EXi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "53adbd81e9bde27007b28083068f2fc8272614dc", "scripts": {"lint": "eslint lib/*.js test/*.js test/integration/*.js", "test": "istanbul cover test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "pretest": "rimraf coverage test/tmp", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "puppeteer": "^1.19.0", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_4.0.0_1613410734571_0.08216993276960127", "host": "s3://npm-registry-packages"}}, "2.5.2": {"name": "form-data", "version": "2.5.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.5.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "dc653743d1de2fcc340ceea38079daf6e9069fd2", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.5.2.tgz", "fileCount": 7, "integrity": "sha512-GgwY0PS7DbXqajuGf4OYlsrIu3zgxD6Vvql43IBhm6MahqA5SK/7mwhtNj2AdH2z35YR34ujJ7BN+3fFC3jP5Q==", "signatures": [{"sig": "MEUCIQCdPFoscjxRNsLU7GO8n0LHQXccKfNPa86hAAbrhg6cYgIgMDdc+FZNbkq5Rwa4l9+ykfhxdY8kuKlHPchwKIgh8go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30046}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "7020dd4c1260370abc40e86e3dfe49c5d576fbda", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "safe-buffer": "^5.2.1", "combined-stream": "^1.0.6"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "phantomjs-prebuilt": "^2.1.13", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.5.2_1728531993604_0.17849460895581437", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "form-data", "version": "3.0.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@3.0.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "83ad9ced7c03feaad97e293d6f6091011e1659c8", "tarball": "https://registry.npmjs.org/form-data/-/form-data-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-sJe+TQb2vIaIyO783qN6BlMYWMw3WBOHA1Ay2qxsnjuafEOQFJ2JakedOQirT6D5XPRxDvS7AHYyem9fTpb4LQ==", "signatures": [{"sig": "MEQCIDiM3L2FXLHwbkn8tTH5KO/v+qJ/lNSjm8ehbvRm0uFPAiBY9q8dBK1QkSOnD3RjGHWv8dDTyDyw0FYhwE1piKuwPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31035}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "f06b0d85d10bc942b3bf586b01ace6e874ac61b3", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "puppeteer": "^1.19.0", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_3.0.2_1728532130262_0.5520780399754868", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "form-data", "version": "4.0.1", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@4.0.1", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48", "tarball": "https://registry.npmjs.org/form-data/-/form-data-4.0.1.tgz", "fileCount": 7, "integrity": "sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==", "signatures": [{"sig": "MEUCIGtHBWWsiRDX0+3zwj7vExb0JnWd4wVT85pGe9spI5RVAiEAi9CB2MmO6rWyrbGifa0MMebcCtIgyZA18pvJqcW73Bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31430}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "d04f7381b0111c707baa47190de2d48a02988b5b", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^4.6.2", "obake": "^0.1.2", "eslint": "^6.0.1", "rimraf": "^2.7.1", "request": "^2.88.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.0", "coveralls": "^3.0.4", "puppeteer": "^1.19.0", "browserify": "^13.1.1", "formidable": "^1.0.17", "in-publish": "^2.0.0", "pre-commit": "^1.1.3", "typescript": "^3.5.2", "@types/node": "^12.0.10", "cross-spawn": "^6.0.5", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_4.0.1_1728532185836_0.4433628713393647", "host": "s3://npm-registry-packages"}}, "2.5.3": {"name": "form-data", "version": "2.5.3", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@2.5.3", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "f9bcf87418ce748513c0c3494bb48ec270c97acc", "tarball": "https://registry.npmjs.org/form-data/-/form-data-2.5.3.tgz", "fileCount": 7, "integrity": "sha512-XHIrMD0NpDrNM/Ckf7XJiBbLl57KEhT3+i3yY+eWm+cqYZJQTZrKo8Y8AWKnuV5GT4scfuUGt9LzNoIx3dU1nQ==", "signatures": [{"sig": "MEYCIQDXVzrquqGQwUqb/HpsZAqAWoH/LcbR5CjTrp1NoodQBwIhANExbX2SC+7RslSWF4rrZ53MIFen/tAHaSBfHhcG8Pog", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30454}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 0.12"}, "gitHead": "9457283e1dce6122adc908fdd7442cfc54cabe7a", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "23.8.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.35", "safe-buffer": "^5.2.1", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^5.9.0", "obake": "^0.1.2", "eslint": "^6.8.0", "rimraf": "^2.7.1", "request": "~2.87.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.2", "coveralls": "^3.1.1", "browserify": "^13.3.0", "formidable": "^1.2.6", "in-publish": "^2.0.1", "pre-commit": "^1.2.2", "typescript": "^3.9.10", "@types/node": "^12.20.55", "cross-spawn": "^4.0.2", "is-node-modern": "^1.0.0", "@types/mime-types": "^2.1.4", "phantomjs-prebuilt": "^2.1.16", "browserify-istanbul": "^2.0.0", "@types/combined-stream": "^1.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_2.5.3_1739570176821_0.47804769752077725", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "form-data", "version": "3.0.3", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@3.0.3", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "349c8f2c9d8f8f0c879ee0eb7cc0d300018d6b09", "tarball": "https://registry.npmjs.org/form-data/-/form-data-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-q5Y<PERSON>eWy6E2Un0nMGWMgI65MAKtaylxfNJGJxpGh45YDciZB4epbWpaAfImil6CPAPTYB4sh0URQNDRIZG5F2w==", "signatures": [{"sig": "MEUCIQDQXlKzYmVUnZHg6BAjq9x1/osM0oAVS0WwZmRt/t7SKAIgQPvHk9Hk4tDcaZyH5wJwgwPmoaVp3cDeS3+6BIUVO44=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31368}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "e46f09bb5338bff090f5cceea5119a8b37c9b147", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "23.8.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.35", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^5.9.0", "obake": "^0.1.2", "eslint": "^6.8.0", "rimraf": "^2.7.1", "request": "~2.87.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.2", "coveralls": "^3.1.1", "puppeteer": "^1.20.0", "browserify": "^13.3.0", "formidable": "^1.2.6", "in-publish": "^2.0.1", "pre-commit": "^1.2.2", "typescript": "^3.9.10", "@types/node": "^12.20.55", "cross-spawn": "^6.0.6", "is-node-modern": "^1.0.0", "browserify-istanbul": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_3.0.3_1739574012492_0.7782722656980241", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.2": {"name": "form-data", "version": "4.0.2", "author": {"url": "http://debuggable.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "form-data@4.0.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "homepage": "https://github.com/form-data/form-data#readme", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dist": {"shasum": "35cabbdd30c3ce73deb2c42d3c8d3ed9ca51794c", "tarball": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz", "fileCount": 7, "integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "signatures": [{"sig": "MEUCIDsc8+kG0IfOthXSQCLdi2rgzXN0YYGzQ3D4tXCl7U1OAiEA4FxyHLX1TtCQUPk1pkXej4soPFGPkYRytEm5gBcrRoQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31836}, "main": "./lib/form_data", "browser": "./lib/browser", "engines": {"node": ">= 6"}, "gitHead": "7465e1337244831e91f9a2413a2bf49bdc2a2e04", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "check": "istanbul check-coverage coverage/coverage*.json", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "report": "istanbul report lcov text", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "pretest": "npm run lint", "posttest": "npx npm@'>=10.2' audit --production", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "tests-only": "istanbul cover test/run.js", "get-version": "node -e \"console.log(require('./package.json').version)\"", "postpublish": "npm run restore-readme", "pretests-only": "rimraf coverage test/tmp", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "posttests-only": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md"}, "typings": "./index.d.ts", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "pre-commit": ["lint", "ci-test", "check"], "repository": {"url": "git://github.com/form-data/form-data.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "directories": {}, "_nodeVersion": "23.8.0", "dependencies": {"asynckit": "^0.4.0", "mime-types": "^2.1.12", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"far": "^0.0.7", "fake": "^0.2.2", "tape": "^5.9.0", "obake": "^0.1.2", "eslint": "^6.8.0", "rimraf": "^2.7.1", "request": "~2.87.0", "istanbul": "^0.4.5", "pkgfiles": "^2.3.2", "coveralls": "^3.1.1", "puppeteer": "^1.20.0", "browserify": "^13.3.0", "formidable": "^1.2.6", "in-publish": "^2.0.1", "pre-commit": "^1.2.2", "typescript": "^3.9.10", "@types/node": "^12.20.55", "cross-spawn": "^6.0.6", "is-node-modern": "^1.0.0", "@types/mime-types": "^2.1.4", "browserify-istanbul": "^2.0.0", "@types/combined-stream": "^1.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/form-data_4.0.2_1739575437467_0.440653080386882", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "4.0.3", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "typings": "./index.d.ts", "scripts": {"pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "tests-only": "istanbul cover test/run.js", "posttests-only": "istanbul report lcov text", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "report": "istanbul report lcov text", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "restore-readme": "mv README.md.bak README.md", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "npm run update-readme", "postpublish": "npm run restore-readme"}, "engines": {"node": ">= 6"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^6.0.6", "eslint": "=8.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.2", "request": "~2.87.0", "rimraf": "^2.7.1", "tape": "^5.9.0"}, "license": "MIT", "_id": "form-data@4.0.3", "gitHead": "d8d67dc8ac79285154edf7d3f57dbab593b9a146", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_nodeVersion": "24.1.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==", "shasum": "608b1b3f3e28be0fccf5901fc85fb3641e5cf0ae", "tarball": "https://registry.npmjs.org/form-data/-/form-data-4.0.3.tgz", "fileCount": 8, "unpackedSize": 44398, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC98ZP7eRRzRiynqPOLaLqNLd2gz95DwW0wCH0MuUEciwIhANjlKLBtWIxHtFlG6b9esJjnxwrBPv4XRL0OfgiagUfr"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/form-data_4.0.3_1749135071283_0.22305244563165738"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-05-16T14:58:21.870Z", "modified": "2025-06-05T14:51:11.981Z", "0.0.0": "2011-05-16T14:58:22.532Z", "0.0.2": "2012-07-02T21:59:37.447Z", "0.0.3": "2012-08-10T03:05:05.412Z", "0.0.4": "2012-09-06T16:47:43.385Z", "0.0.5": "2012-12-05T07:06:20.011Z", "0.0.6": "2013-01-08T08:04:51.296Z", "0.0.7": "2013-02-10T02:53:05.643Z", "0.0.8": "2013-04-21T23:32:42.861Z", "0.0.9": "2013-04-29T17:09:12.258Z", "0.0.10": "2013-05-09T05:35:21.282Z", "0.1.0": "2013-07-08T18:46:13.834Z", "0.1.1": "2013-08-22T04:22:58.445Z", "0.1.2": "2013-10-03T04:06:55.129Z", "0.1.3": "2014-06-02T07:48:24.579Z", "0.1.4": "2014-06-24T06:35:44.934Z", "0.2.0": "2014-12-06T21:14:10.402Z", "1.0.0-rc1": "2015-06-13T15:50:04.021Z", "1.0.0-rc2": "2015-07-22T02:48:28.688Z", "1.0.0-rc3": "2015-07-30T04:10:06.749Z", "1.0.0-rc4": "2016-03-15T16:35:47.585Z", "1.0.0": "2016-08-26T09:01:06.786Z", "1.0.1": "2016-08-26T09:44:37.316Z", "2.0.0": "2016-09-17T06:10:17.649Z", "2.1.0": "2016-09-25T22:14:22.111Z", "2.1.1": "2016-10-04T06:33:17.917Z", "2.1.2": "2016-11-08T04:02:19.622Z", "2.1.4": "2017-04-09T15:09:59.126Z", "2.2.0": "2017-06-11T08:46:00.717Z", "2.3.1": "2017-08-24T23:00:54.577Z", "2.3.2-rc1": "2018-02-12T17:58:22.688Z", "2.3.2": "2018-02-14T00:31:49.698Z", "2.3.3": "2018-10-17T07:26:22.767Z", "2.4.0": "2019-06-19T07:27:06.596Z", "2.5.0": "2019-07-04T04:44:27.090Z", "2.5.1": "2019-08-29T06:48:54.586Z", "3.0.0": "2019-11-06T07:56:58.467Z", "3.0.1": "2021-02-15T17:33:58.367Z", "4.0.0": "2021-02-15T17:38:54.740Z", "2.5.2": "2024-10-10T03:46:33.800Z", "3.0.2": "2024-10-10T03:48:50.494Z", "4.0.1": "2024-10-10T03:49:46.006Z", "2.5.3": "2025-02-14T21:56:16.969Z", "3.0.3": "2025-02-14T23:00:12.690Z", "4.0.2": "2025-02-14T23:23:57.699Z", "4.0.3": "2025-06-05T14:51:11.479Z"}, "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "license": "MIT", "homepage": "https://github.com/form-data/form-data#readme", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"name": "titanism", "email": "<EMAIL>"}], "readme": "# Form-Data [![NPM Module](https://img.shields.io/npm/v/form-data.svg)](https://www.npmjs.com/package/form-data) [![Join the chat at https://gitter.im/form-data/form-data](http://form-data.github.io/images/gitterbadge.svg)](https://gitter.im/form-data/form-data)\n\nA library to create readable ```\"multipart/form-data\"``` streams. Can be used to submit forms and file uploads to other web applications.\n\nThe API of this library is inspired by the [XMLHttpRequest-2 FormData Interface][xhr2-fd].\n\n[xhr2-fd]: http://dev.w3.org/2006/webapi/XMLHttpRequest-2/Overview.html#the-formdata-interface\n\n[![Linux Build](https://img.shields.io/travis/form-data/form-data/v4.0.3.svg?label=linux:6.x-12.x)](https://travis-ci.org/form-data/form-data)\n[![MacOS Build](https://img.shields.io/travis/form-data/form-data/v4.0.3.svg?label=macos:6.x-12.x)](https://travis-ci.org/form-data/form-data)\n[![Windows Build](https://img.shields.io/travis/form-data/form-data/v4.0.3.svg?label=windows:6.x-12.x)](https://travis-ci.org/form-data/form-data)\n\n[![Coverage Status](https://img.shields.io/coveralls/form-data/form-data/v4.0.3.svg?label=code+coverage)](https://coveralls.io/github/form-data/form-data?branch=master)\n[![Dependency Status](https://img.shields.io/david/form-data/form-data.svg)](https://david-dm.org/form-data/form-data)\n\n## Install\n\n```\nnpm install --save form-data\n```\n\n## Usage\n\nIn this example we are constructing a form with 3 fields that contain a string,\na buffer and a file stream.\n\n``` javascript\nvar FormData = require('form-data');\nvar fs = require('fs');\n\nvar form = new FormData();\nform.append('my_field', 'my value');\nform.append('my_buffer', new Buffer(10));\nform.append('my_file', fs.createReadStream('/foo/bar.jpg'));\n```\n\nAlso you can use http-response stream:\n\n``` javascript\nvar FormData = require('form-data');\nvar http = require('http');\n\nvar form = new FormData();\n\nhttp.request('http://nodejs.org/images/logo.png', function (response) {\n  form.append('my_field', 'my value');\n  form.append('my_buffer', new Buffer(10));\n  form.append('my_logo', response);\n});\n```\n\nOr @mikeal's [request](https://github.com/request/request) stream:\n\n``` javascript\nvar FormData = require('form-data');\nvar request = require('request');\n\nvar form = new FormData();\n\nform.append('my_field', 'my value');\nform.append('my_buffer', new Buffer(10));\nform.append('my_logo', request('http://nodejs.org/images/logo.png'));\n```\n\nIn order to submit this form to a web application, call ```submit(url, [callback])``` method:\n\n``` javascript\nform.submit('http://example.org/', function (err, res) {\n  // res – response object (http.IncomingMessage)  //\n  res.resume();\n});\n\n```\n\nFor more advanced request manipulations ```submit()``` method returns ```http.ClientRequest``` object, or you can choose from one of the alternative submission methods.\n\n### Custom options\n\nYou can provide custom options, such as `maxDataSize`:\n\n``` javascript\nvar FormData = require('form-data');\n\nvar form = new FormData({ maxDataSize: 20971520 });\nform.append('my_field', 'my value');\nform.append('my_buffer', /* something big */);\n```\n\nList of available options could be found in [combined-stream](https://github.com/felixge/node-combined-stream/blob/master/lib/combined_stream.js#L7-L15)\n\n### Alternative submission methods\n\nYou can use node's http client interface:\n\n``` javascript\nvar http = require('http');\n\nvar request = http.request({\n  method: 'post',\n  host: 'example.org',\n  path: '/upload',\n  headers: form.getHeaders()\n});\n\nform.pipe(request);\n\nrequest.on('response', function (res) {\n  console.log(res.statusCode);\n});\n```\n\nOr if you would prefer the `'Content-Length'` header to be set for you:\n\n``` javascript\nform.submit('example.org/upload', function (err, res) {\n  console.log(res.statusCode);\n});\n```\n\nTo use custom headers and pre-known length in parts:\n\n``` javascript\nvar CRLF = '\\r\\n';\nvar form = new FormData();\n\nvar options = {\n  header: CRLF + '--' + form.getBoundary() + CRLF + 'X-Custom-Header: 123' + CRLF + CRLF,\n  knownLength: 1\n};\n\nform.append('my_buffer', buffer, options);\n\nform.submit('http://example.com/', function (err, res) {\n  if (err) throw err;\n  console.log('Done');\n});\n```\n\nForm-Data can recognize and fetch all the required information from common types of streams (```fs.readStream```, ```http.response``` and ```mikeal's request```), for some other types of streams you'd need to provide \"file\"-related information manually:\n\n``` javascript\nsomeModule.stream(function (err, stdout, stderr) {\n  if (err) throw err;\n\n  var form = new FormData();\n\n  form.append('file', stdout, {\n    filename: 'unicycle.jpg', // ... or:\n    filepath: 'photos/toys/unicycle.jpg',\n    contentType: 'image/jpeg',\n    knownLength: 19806\n  });\n\n  form.submit('http://example.com/', function (err, res) {\n    if (err) throw err;\n    console.log('Done');\n  });\n});\n```\n\nThe `filepath` property overrides `filename` and may contain a relative path. This is typically used when uploading [multiple files from a directory](https://wicg.github.io/entries-api/#dom-htmlinputelement-webkitdirectory).\n\nFor edge cases, like POST request to URL with query string or to pass HTTP auth credentials, object can be passed to `form.submit()` as first parameter:\n\n``` javascript\nform.submit({\n  host: 'example.com',\n  path: '/probably.php?extra=params',\n  auth: 'username:password'\n}, function (err, res) {\n  console.log(res.statusCode);\n});\n```\n\nIn case you need to also send custom HTTP headers with the POST request, you can use the `headers` key in first parameter of `form.submit()`:\n\n``` javascript\nform.submit({\n  host: 'example.com',\n  path: '/surelynot.php',\n  headers: { 'x-test-header': 'test-header-value' }\n}, function (err, res) {\n  console.log(res.statusCode);\n});\n```\n\n### Methods\n\n- [_Void_ append( **String** _field_, **Mixed** _value_ [, **Mixed** _options_] )](https://github.com/form-data/form-data#void-append-string-field-mixed-value--mixed-options-).\n- [_Headers_ getHeaders( [**Headers** _userHeaders_] )](https://github.com/form-data/form-data#array-getheaders-array-userheaders-)\n- [_String_ getBoundary()](https://github.com/form-data/form-data#string-getboundary)\n- [_Void_ setBoundary()](https://github.com/form-data/form-data#void-setboundary)\n- [_Buffer_ getBuffer()](https://github.com/form-data/form-data#buffer-getbuffer)\n- [_Integer_ getLengthSync()](https://github.com/form-data/form-data#integer-getlengthsync)\n- [_Integer_ getLength( **function** _callback_ )](https://github.com/form-data/form-data#integer-getlength-function-callback-)\n- [_Boolean_ hasKnownLength()](https://github.com/form-data/form-data#boolean-hasknownlength)\n- [_Request_ submit( _params_, **function** _callback_ )](https://github.com/form-data/form-data#request-submit-params-function-callback-)\n- [_String_ toString()](https://github.com/form-data/form-data#string-tostring)\n\n#### _Void_ append( **String** _field_, **Mixed** _value_ [, **Mixed** _options_] )\nAppend data to the form. You can submit about any format (string, integer, boolean, buffer, etc.). However, Arrays are not supported and need to be turned into strings by the user.\n```javascript\nvar form = new FormData();\nform.append('my_string', 'my value');\nform.append('my_integer', 1);\nform.append('my_boolean', true);\nform.append('my_buffer', new Buffer(10));\nform.append('my_array_as_json', JSON.stringify(['bird', 'cute']));\n```\n\nYou may provide a string for options, or an object.\n```javascript\n// Set filename by providing a string for options\nform.append('my_file', fs.createReadStream('/foo/bar.jpg'), 'bar.jpg');\n\n// provide an object.\nform.append('my_file', fs.createReadStream('/foo/bar.jpg'), { filename: 'bar.jpg', contentType: 'image/jpeg', knownLength: 19806 });\n```\n\n#### _Headers_ getHeaders( [**Headers** _userHeaders_] )\nThis method adds the correct `content-type` header to the provided array of `userHeaders`.\n\n#### _String_ getBoundary()\nReturn the boundary of the formData. By default, the boundary consists of 26 `-` followed by 24 numbers\nfor example:\n```javascript\n--------------------------515890814546601021194782\n```\n\n#### _Void_ setBoundary(String _boundary_)\nSet the boundary string, overriding the default behavior described above.\n\n_Note: The boundary must be unique and may not appear in the data._\n\n#### _Buffer_ getBuffer()\nReturn the full formdata request package, as a Buffer. You can insert this Buffer in e.g. Axios to send multipart data.\n```javascript\nvar form = new FormData();\nform.append('my_buffer', Buffer.from([0x4a,0x42,0x20,0x52,0x6f,0x63,0x6b,0x73]));\nform.append('my_file', fs.readFileSync('/foo/bar.jpg'));\n\naxios.post('https://example.com/path/to/api', form.getBuffer(), form.getHeaders());\n```\n**Note:** Because the output is of type Buffer, you can only append types that are accepted by Buffer: *string, Buffer, ArrayBuffer, Array, or Array-like Object*. A ReadStream for example will result in an error.\n\n#### _Integer_ getLengthSync()\nSame as `getLength` but synchronous.\n\n_Note: getLengthSync __doesn't__ calculate streams length._\n\n#### _Integer_ getLength(**function** _callback_ )\nReturns the `Content-Length` async. The callback is used to handle errors and continue once the length has been calculated\n```javascript\nthis.getLength(function (err, length) {\n  if (err) {\n    this._error(err);\n    return;\n  }\n\n  // add content length\n  request.setHeader('Content-Length', length);\n\n  ...\n}.bind(this));\n```\n\n#### _Boolean_ hasKnownLength()\nChecks if the length of added values is known.\n\n#### _Request_ submit(_params_, **function** _callback_ )\nSubmit the form to a web application.\n```javascript\nvar form = new FormData();\nform.append('my_string', 'Hello World');\n\nform.submit('http://example.com/', function (err, res) {\n  // res – response object (http.IncomingMessage)  //\n  res.resume();\n} );\n```\n\n#### _String_ toString()\nReturns the form data as a string. Don't use this if you are sending files or buffers, use `getBuffer()` instead.\n\n### Integration with other libraries\n\n#### Request\n\nForm submission using  [request](https://github.com/request/request):\n\n```javascript\nvar formData = {\n  my_field: 'my_value',\n  my_file: fs.createReadStream(__dirname + '/unicycle.jpg'),\n};\n\nrequest.post({url:'http://service.com/upload', formData: formData}, function (err, httpResponse, body) {\n  if (err) {\n    return console.error('upload failed:', err);\n  }\n  console.log('Upload successful!  Server responded with:', body);\n});\n```\n\nFor more details see [request readme](https://github.com/request/request#multipartform-data-multipart-form-uploads).\n\n#### node-fetch\n\nYou can also submit a form using [node-fetch](https://github.com/bitinn/node-fetch):\n\n```javascript\nvar form = new FormData();\n\nform.append('a', 1);\n\nfetch('http://example.com', { method: 'POST', body: form })\n    .then(function (res) {\n        return res.json();\n    }).then(function (json) {\n        console.log(json);\n    });\n```\n\n#### axios\n\nIn Node.js you can post a file using [axios](https://github.com/axios/axios):\n```javascript\nconst form = new FormData();\nconst stream = fs.createReadStream(PATH_TO_FILE);\n\nform.append('image', stream);\n\n// In Node.js environment you need to set boundary in the header field 'Content-Type' by calling method `getHeaders`\nconst formHeaders = form.getHeaders();\n\naxios.post('http://example.com', form, {\n  headers: {\n    ...formHeaders,\n  },\n})\n  .then(response => response)\n  .catch(error => error)\n```\n\n## Notes\n\n- ```getLengthSync()``` method DOESN'T calculate length for streams, use ```knownLength``` options as workaround.\n- ```getLength(cb)``` will send an error as first parameter of callback if stream length cannot be calculated (e.g. send in custom streams w/o using ```knownLength```).\n- ```submit``` will not add `content-length` if form length is unknown or not calculable.\n- Starting version `2.x` FormData has dropped support for `node@0.10.x`.\n- Starting version `3.x` FormData has dropped support for `node@4.x`.\n\n## License\n\nForm-Data is released under the [MIT](License) license.\n", "readmeFilename": "Readme.md", "users": {"306766053": true, "nex": true, "bam5": true, "cedx": true, "jmeo": true, "doruk": true, "floby": true, "haeck": true, "hanhq": true, "miloc": true, "ritsu": true, "stany": true, "ucdok": true, "xrush": true, "456wyc": true, "adamlu": true, "bojand": true, "ecelis": true, "edin-m": true, "ggomma": true, "monjer": true, "ndfool": true, "skymap": true, "xgenvn": true, "cslater": true, "hitalos": true, "nichoth": true, "trackds": true, "ungurys": true, "wenbing": true, "edmooney": true, "liveinjs": true, "moimikey": true, "portilha": true, "calmwinds": true, "edmondnow": true, "godoshian": true, "hellboy81": true, "mojaray2k": true, "nickeljew": true, "sree.meda": true, "alexindigo": true, "leizongmin": true, "machinabio": true, "muzi131313": true, "rocket0191": true, "seangenabe": true, "xenohunter": true, "flumpus-dev": true, "haiyang5210": true, "lishuminvip": true, "sessionbean": true, "tunnckocore": true, "vladgolubev": true, "donecharlton": true, "mpinteractiv": true, "wangsong1224": true, "caravanpetrol": true, "chinawolf_wyp": true, "mdedirudianto": true, "maemichi-monosense": true, "programmer.severson": true}}