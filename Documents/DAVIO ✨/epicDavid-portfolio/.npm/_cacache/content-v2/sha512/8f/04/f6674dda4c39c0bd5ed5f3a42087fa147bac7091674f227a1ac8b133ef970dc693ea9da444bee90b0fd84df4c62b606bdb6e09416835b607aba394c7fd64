{"_id": "char-regex", "_rev": "7-60609fb133fff84488a461bfa91fc110", "name": "char-regex", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.0": {"name": "char-regex", "version": "1.0.0", "keywords": ["character", "regex", "match", "split", "length"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "char-regex@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "xo": {"extends": "richienb/node"}, "dist": {"shasum": "6aa144f51f4be35fb998dc8a0eb6449880684836", "tarball": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-a2ue6WHuC3MjMJe6cPB1i1WOT0gZG4L2t1fPquAvruYDB6JpDt4LhU6bTm3xo2YC8xVWniokX8QkDsHeiHAPJw==", "signatures": [{"sig": "MEUCIQDKD4gWOLQcf9W9Xt0u+vmUGSLBbm8BoLAYyr/lhiS+HgIgUSs8Ro/ViKwOaitALxNMI+ckiwOTB4Yi2TGD080LYYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNAbzCRA9TVsSAnZWagAAIagP/R4+RBBKuysZy2Oz59FN\nzsbrxceGnlv1wOfVlAkWyVEJdyihgWWf+YZ60/KgxcPPUUFztpsFlaYzTz8l\n9GKeTzpqasb9Ny7TRW454kZ7UKl+mI1fUX67vK1OJnCgv8/DC6nvkoSe8nIz\nyGIv89FczTQxaHKZsxQhY5j2XQ7oXEauyRvPQAE5Qz+Ir5dgVhYrWNympMBh\nT89VvWiB3YMD+j0vwqcEyfbpN6KP3nELPMh7oLKl9HbvbHzJDSLNO2H0l5v9\n0cos4xuM60Gcf5KZwZsKbxq0WPUbAmosA/Dc8ayVcNNMBBWsQALwRi+4LHpd\nB2pzxAw7X90a+RqFAY5qYdJMV9LkNM4dmdVBy8vWTbZTS6eTqkVhdwkpdtzs\nGrleSpZ+O8AeRX+Uj7p91qApj3Ox5e2fpry5FlBlDo+YR6oZ9SSt9667ypME\nCz6omJf7ZV0wXt0P9LJVCQ0yzWHEDSTy97DtYFtZdDsdxL3KsZJKAyGRjUsa\nGjBL4/mSF7E2Bf6F1h98ZGGx/JqpsFCIrOJVmIfQQU7hlR4zyfxCs9m6FWoG\n+YcN0vkiSAFGcZu4DcE6keH6qLWmn4d21xS9WNeWJBT/CWQT7dM1dIEGehTf\nYzAxql3cQ3WGhNqTBywmwsZBhwYSkB850DWdSzgJH9mJ7tMV9POgJlTPMigN\n4P35\r\n=5Y1u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"lint": "xo", "test": "yarn lint && ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/Richienb/char-regex.git", "type": "git"}, "description": "A regex to match any full character, considering weird character ranges.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019 Richie <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "resolutions": {"eslint": "^6.8.0"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^3.0.0", "eslint-config-richienb": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/char-regex_1.0.0_1580467955352_0.7804541324865057", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "char-regex", "version": "1.0.1", "keywords": ["character", "regex", "match", "split", "length"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "char-regex@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "xo": {"extends": "richienb/node"}, "dist": {"shasum": "05f02a47ab6e8d945876273a9ea85c67bac48122", "tarball": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-MbgzaFvYIfIjO1jCAafL6OEHvuYyr/E831q2+JHR39wzyebZj5CF2UdA3VZ3WVC44+gmujK3e5UILsFE3WFAiw==", "signatures": [{"sig": "MEUCIBbtQTJXxVpdEv3EnEt6WPY2+LQ/OVvkAQWxJbGSYxcZAiEAzbcir+e3CpecdKo5tzBZksqg3IZUBXBTdzX/OHdgGxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNAngCRA9TVsSAnZWagAApLEP/iKbuaaVvTsRSl8yS9wD\nA1YxchgM377vPl/IgARjHOH2wPOuk994vhLLNWi4WMxuJliZ/kkBDam5AB/s\nVYQye0Vg47q20HviozLIt5DRA1fkxpWfqODCcoEEVA28cwFUEpEXLYIkuEMN\n1W86E9tV+bJ/3hliQHWGOZeVJojZJyBwulM5ViuWehZdkPdEizTdLxLXM+s/\nUksXH9Ur6L7FxHJUdT/l8nqVj31X/9pXhE/shnXw2jBpFDUSoxyN5tng0tSo\nWdE/kY30+nQkiM8XVpzwi9ZnfYgdQpPD6VlYuC169S5dCRp/Y5/BZVz73DNa\nCzrxunFKDu6gINuKtOxkWyIbUPyOd59LKN5iCkQjzpkycdHB8gvlTCvbzHCA\n4oTHbxDd3gpHaAKOHkpBRvMb6r2zNam7qCwEzFvM9EMYzjiBmwPF9Z3Bd/Jp\nQnj3HxYNaqgxzGEsvjzJOKdIfFzxgIxjLCfxltX3KVYq057RNY/LhLMBQxxK\nxtA7AyTKBOREhgES8X+Dl6jB4rdUEfKScOizs6Epy5XBYn/HsafPiHpZmOua\nQ2JV2TFm/BcRsE41yzV++MfXnTA6UvGImRWmpXGw8Ymek8AVREAwkxaiMYU+\n33Nak480svfGYVy775KrfYSLLLzw7jcIQaQ/5su7HgddkkjyAwxVsQS4qDDj\nqMJE\r\n=wlHi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"lint": "xo", "test": "yarn lint && ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/Richienb/char-regex.git", "type": "git"}, "description": "A regex to match any full character, considering weird character ranges.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019 Richie <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "resolutions": {"eslint": "^6.8.0"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^3.0.0", "eslint-config-richienb": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/char-regex_1.0.1_1580468704454_0.25866643399971245", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "char-regex", "version": "1.0.2", "keywords": ["character", "regex", "match", "split", "length"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "char-regex@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "xo": {"extends": "richienb/node"}, "dist": {"shasum": "d744358226217f981ed58f479b1d6bcc29545dcf", "tarball": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==", "signatures": [{"sig": "MEYCIQDHoatxqbbVfBcB0vRfPfbTLx2Pki7FBBlTuRZk77yLIwIhAK9+cm4f4QySQYXPaBBjWELw05RdQqZmu10KKkA/79Gn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS7OiCRA9TVsSAnZWagAAI6oP/2HrRK6reqD98hIGAl9Q\nPooA8vWtIQSm7a00yFXiyNnBj76A5oS2Q+9bHFTTpCtUKFa47lwi+uaA0v70\nXIF9OD6nQ8dZtavEmMNX3PLnuHf6gPjee4S3/4MTmiO7HLCZn99Av5Y+0d1r\nrINIrWBz5EporAGGWFYaWF1W4IdCFUfkwC/TipbsiOCNbke7MzzML9LysMxo\nTBX238NxfPalTr/XPyCrAqHGuMAfmevnhp+EPBsF8C99j/LDFHEiSkBd5m4r\nYSQO10NMp+eY6XmyS9UkgwUzWR1Jf9WyB4k6LIBnjaCVXXdWkznwRrs52pOT\nIDo+YFoEqk//qtRXPUmUjmyAJDqXAjkQFN7XLfZ7tDxSbO6aCGafHt/wb0Gs\nF8xswtWzm7ZwoTNYLOmU2zyuCTURU0GskmiBHmdCcYWYumsueM0PZzZwO0Cf\nqUYmcOHz3OLF3PHZPHe+wADJP2Wpqm+zXB6uDn0GEnSYPIekdG2zVACFtFEc\nlKo+VLiguzmQ8IpnBr2DcKDEWR0ahpuIeLdwBP+cJ3RmbdMnQV++zCtYU3mm\nAV/glKFtm7wxawifVG8t0rN4B4ECp80J2n3Qo1Ai2eRimO/R3vjdPOjwf2eS\nAwgM4KWY7MysAPGYijUv+V5ywocJ3kz9ykMWWt5VVz6GCDi6DcVgrGReP/p5\nNboc\r\n=Gsn0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"lint": "xo", "test": "yarn lint && ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/Richienb/char-regex.git", "type": "git"}, "description": "A regex to match any full character, considering weird character ranges.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019 Richie <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "resolutions": {"eslint": "^6.8.0"}, "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^3.0.0", "array-uniq": "^2.1.0", "emoji.json": "^12.1.1", "@babel/core": "^7.8.4", "unicode-chars": "^1.0.1", "eslint-config-richienb": "^0.3.0", "@babel/plugin-proposal-unicode-property-regex": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/char-regex_1.0.2_1582019489643_0.6313595750874197", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "char-regex", "version": "2.0.0", "keywords": ["character", "regex", "match", "split", "length"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "char-regex@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "xo": {"extends": "<PERSON><PERSON><PERSON>"}, "dist": {"shasum": "16f98f3f874edceddd300fda5d58df380a7641a6", "tarball": "https://registry.npmjs.org/char-regex/-/char-regex-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-oGu2QekBMXgyQNWPDRQ001bjvDnZe4/zBTz37TMbiKz1NbNiyiH5hRkobe7npRN6GfbGbxMYFck/vQ1r9c1VMA==", "signatures": [{"sig": "MEQCIFqfIcQwefqh4ZaJSLroOix3stZQCMf6bMsUA/zoCrEQAiAl9fPS9+MdUPspDecaiFvQdg9vEuE/qQl+3NfMvuOWew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguhkFCRA9TVsSAnZWagAA5tEP/0byv9ikttnAT1gyu2Vr\nwxEtZtYdetu7J2EBqnW9KB864Ob9VLV3XZxdZfZljfg7ScZj4kirxYh5+tzp\npzPRJSd5qXIzaeNpGqYBWr56x8TYSvk7gGDCjZhRvKP2InltmTw3Lb8R8lTy\nRijqA3h4lBa+XHTBAXZ2Xyx47hC8jHpgcArK/dICwtO03J8kT7g9ZPHLE/lc\nAcm3BNBlRiy7XKLN7eEqzX0dmZyEmIOjIBGAqpz9NEkshkLgLEE1Xs/GMEEB\nj4sSunnbo5u5Avjy3pZDHoxJNJZlW2nldBjv8LPXY1U4R0GFidkPbEZVTW2Y\nch+ZmGrP1EqmMps6g2PIPuVrhY4zFyhrl5W6CquLCvsDSH/LD8unY3Gp5S2/\ncl2Rb3oe2PMjw4gy/cZ9wWx7kwvLh6sB3TYcMGiRkWlOtZXDrowqzAZ66hUF\nb8w5Oos0IbQUQpOQneJ5OlEPT4gjCevlzxcOMDfqIk/OXyNWsXEaqX8ZNAmM\nr8vYP4/a53QiCIoLiaB//YTyclay2GlASgltE89LHIa0gO0LCjXU87lP5vV8\nKOBQHQCrgMXG1zHjrzl/yeZG+jgmhHadp1m3NDoEjcL39KNJGpopy/1BnHFh\n5XbzDZboKIAp5nyaf6/UvQxvKhDp3X+JtkcQJoupQWt9duYsxFrcgs7AKbdE\nPjuA\r\n=CFgi\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12.20"}, "exports": "./index.js", "scripts": {"lint": "xo", "test": "xo && ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/Richienb/char-regex.git", "type": "git"}, "description": "A regex to match any full character, considering weird character ranges.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020 - 2021 Richie <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.40.2", "ava": "^3.15.0", "tsd": "^0.17.0", "all-chars": "^1.0.0", "eslint-config-richienb": "^0.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/char-regex_2.0.0_1622808837208_0.04120753013395628", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "char-regex", "version": "2.0.1", "keywords": ["character", "regex", "match", "split", "length"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "char-regex@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6dafdb25f9d3349914079f010ba8d0e6ff9cd01e", "tarball": "https://registry.npmjs.org/char-regex/-/char-regex-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-oSvEeo6ZUD7NepqAat3RqoucZ5SeqLJgOvVIwkafu6IP3V0pO38s/ypdVUmDDK6qIIHNlYHJAKX9E7R7HoKElw==", "signatures": [{"sig": "MEUCIFgnecbKR0wbkGa8obNmr07h3PyFEYASQzNZATvoKfxWAiEA/Ac/crQgXTjIONnfENf5yXVbXqBEJ9W8IRHl0jHGWUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCz5LCRA9TVsSAnZWagAAynMP/3AEyAiqc6Adk4Gjanzg\n2RllCcMFeewTyiLZlw0SjSzNhnI49PJesmmDkbrZREbe+wZ3u7EvTAt5W0+O\nDWzwafsshfSTomw64UvoTDo1aXqn/hEXl7SC/oCqXttAQqU7YB8okR35XMYb\nTu77IetqQFN/6Q6XgnHydz4wO6+eqfj/36J37T7RY+upY5jvzcVtLbQEHYGO\nwDuOUWgwwJPuJlWQq0RVXr7BdObj96hzLwU0+vjWCFMuaHP9fVKEl2qwGe61\nfQzh95v4zD/fbEqaJp0NJsBbhfyGIwN/rznRpK0SNgzq30d+rTaYzSYasuOf\nNXzHYhPtGmRAY+zvZqxOVStZpneBz1EQ9VvwJhaTEHGhUgT5RSektlc2ulpb\nIgefeMQQTOTG0grTy4LJ3CK6sNNA1I2OhgVph1FeCNdmt3S4XWUyLuAq6UuM\nc1B77OhwAUm6vbpP+dPdPU6MDo2Uv8x2ih7vOeBtD4pVb8qVIfdMptdzGn7W\n/St7ClVL7Q5foNiBW9KK1Z8NTzCX32/7oZpUTX4F86Bg8e0vlTbQWoSQ/HW8\n/JQA9X49X9r90NX/K/tm+fLL24ftV8U9N19mgk4ygdUS7vG28ZQz5XxpSX4T\n71frX08yP5TZ4YO0dxJNprO7AHzrxqdvL09UWAPye1bqeerXdWUn+hG5W7Xn\nlxw7\r\n=vpI1\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12.20"}, "exports": "./index.js", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/Richienb/char-regex.git", "type": "git"}, "description": "A regex to match any full character, considering weird character ranges.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020 - 2021 Richie <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "all-chars": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/char-regex_2.0.1_1644904010848_0.8809394980724325", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "char-regex", "version": "2.0.2", "description": "A regex to match any full character, considering weird character ranges.", "repository": {"type": "git", "url": "git+https://github.com/Richienb/char-regex.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "type": "module", "exports": "./index.js", "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["character", "regex", "match", "split", "length"], "dependencies": {}, "devDependencies": {"all-chars": "^1.0.0", "ava": "^6.2.0", "tsd": "^0.31.2", "xo": "^0.59.3"}, "_id": "char-regex@2.0.2", "gitHead": "b2a1dc9967b556f9a576ce0f7ded2a457ba4aefa", "types": "./index.d.ts", "bugs": {"url": "https://github.com/Richienb/char-regex/issues"}, "homepage": "https://github.com/Richienb/char-regex#readme", "_nodeVersion": "20.15.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-cbGOjAptfM2LVmWhwRFHEKTPkLwNddVmuqYZQt895yXwAsWsXObCG+YN4DGQ/JBtT4GP1a1lPPdio2z413LmTg==", "shasum": "81385bb071af4df774bff8721d0ca15ef29ea0bb", "tarball": "https://registry.npmjs.org/char-regex/-/char-regex-2.0.2.tgz", "fileCount": 5, "unpackedSize": 5416, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPh/Z2pVBsb6j+c6ykG4OfGCgv6AmGbEHW2l1sw3HbCgIhAJ7fKczY//brwJiUVYpMgiQP/Pu+SJ9cnNRhFVgF1kPI"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/char-regex_2.0.2_1732112722213_0.692578100107337"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-01-31T10:52:35.351Z", "modified": "2024-11-20T14:25:22.545Z", "1.0.0": "2020-01-31T10:52:35.465Z", "1.0.1": "2020-01-31T11:05:04.655Z", "1.0.2": "2020-02-18T09:51:29.803Z", "2.0.0": "2021-06-04T12:13:57.342Z", "2.0.1": "2022-02-15T05:46:51.033Z", "2.0.2": "2024-11-20T14:25:22.380Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["character", "regex", "match", "split", "length"], "repository": {"type": "git", "url": "git+https://github.com/Richienb/char-regex.git"}, "description": "A regex to match any full character, considering weird character ranges.", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# char-regex\r\n\r\nA regex to match any full character, considering weird character ranges. Tested on every single emoji and unicode character. Based on the Lodash implementation.\r\n\r\n## Install\r\n\r\n```sh\r\nnpm install char-regex\r\n```\r\n\r\n## Usage\r\n\r\n```js\r\nimport charRegex from 'char-regex';\r\n\r\n'❤️👊🏽'.match(/./);\r\n//=> ['', '', '', '', '', '', '']\r\n\r\n'❤️👊🏽'.match(charRegex());\r\n//=> ['❤️', '👊🏽']\r\n```\r\n\r\n## Related\r\n\r\n- [string-length](https://github.com/sindresorhus/string-length) - Get the real length of a string\r\n", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}, "homepage": "https://github.com/Richienb/char-regex#readme", "bugs": {"url": "https://github.com/Richienb/char-regex/issues"}}