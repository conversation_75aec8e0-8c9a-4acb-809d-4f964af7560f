{"_id": "@testing-library/jest-dom", "_rev": "91-b6f4ff96ad262b88b549b120c35e0b40", "name": "@testing-library/jest-dom", "dist-tags": {"beta": "5.7.0-beta.1", "latest": "6.6.3"}, "versions": {"0.0.0": {"name": "@testing-library/jest-dom", "version": "0.0.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@0.0.0", "maintainers": [{"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "9fa2a2b57e039aa838c099c7190d630ca7e5dba9", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-0.0.0.tgz", "fileCount": 44, "integrity": "sha512-9nGiznVoYD3k6c1YWpAyB6MSQlj0FmJ6I7bV+y/ox4yH/lIcCm3MRF02acluOnhKeVExeui7AdSfV0TRFiX1QA==", "signatures": [{"sig": "MEQCIGwczR3GmA+XD5YDXUQoho0MQpDuD60Z6wI3TxchjDOXAiBRi0ihhbos3jOR7UOCljcz1Jxhw7QihhWfYqE+9+U89g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI2UyCRA9TVsSAnZWagAAr24P/it7cjxtkFBvYg9GXkzy\nMbi4yEnREaP9orYRhAXF1G87cvnVyzYZXkaKseUUPGSSH7D1vxKnD9RrZmZ0\nGJpqJPvwaJEP7reKHOeScBcQdKW9oGpNy9t735WJ5Vu9ldsfURzZ7bNfZd1y\nAx22Ho6cr6/5Nn0onntI5WTGeyeYsQMvDKSD118M0YzvLqWhFMbIx11JBiy1\nME6yUmSa5xPgbCQZuXBJvCZTGm9eiBEAGnr7MT8no8yMptF3LnVfZo11mjDe\nwO+YtLepj3DlFf2PH1LPcFYPKuYT8fwBgt1MCz9JeNUwJPzg4aKSA2P2f67j\nCOVBsugcbI23Sxmnopt8Q87L57+IReOBiWH+a0nIOzkb0dgha5Lm8z8wnz6O\nFozt98nxcOm4/9f7FGteOinallxIuLuABRKXpTKaEkk1iJXs6NGHg3BscQ0i\nTOrRMQK/ihadtcNFOjsOATcDLgmzvGrlXT1nJmBN2mh1du/rZ1/A3mXw906E\n6DxIqDHgjjSzdpztFv6yBhPRWDhUgDbpk8ouCoDw4DWHJODQUB8vmOr3FtGm\nHoE4CcwJb8Fy8LtUTj4kPic4myGQUSzL6J+WZQFwCO7cdAWvMnpa/JRK1zq0\nBXm3/saV3imeMoyH5T7bmOKH8Z3y7tsFUntMBfsPnpUyENB7TgwNDbPM8fbm\n663E\r\n=XuGE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "0d04699ce875d3c80ae3967741a49892185f3ef0", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_0.0.0_1562600754064_0.9737372112609586", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@testing-library/jest-dom", "version": "4.0.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.0.0", "maintainers": [{"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "56eee8dd183fe14a682fda7aca6413ec4e5303d2", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.0.0.tgz", "fileCount": 44, "integrity": "sha512-YQA/LnRRfqHV5YRauawOGgMDgq43XfyqCz3whmxIPyrfvTdjLCNyY/BseGaa48y54yb3oiRo/NZT0oXNMQdkTA==", "signatures": [{"sig": "MEYCIQDxj9gZmAACTU8Ip570/D2btzBW1FLEhIusdfDzkSVyzgIhAMdBmArptXoomR3kPKjeZWjeh9ScrcPgGA3f1KFiThRJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI25OCRA9TVsSAnZWagAAGlsQAJdE1oP8rPksSve3vP3A\nA6usFwnB36OtSMY/SoSKYsbln8CZYuOcJU1ERagUCufZffyC3jks/hX/UFSU\n3Zpm9bCB6QvG3DM1ayo5f1ej9q2zlW83eU9UM7bpEb6iFkn/aLoZIvS7iUg2\nVZ4wMfOTz6bRSGoc6elvWAg24rnRjVO1lZGIGL6XQ/z6qJHQK3Mrhey3Jk5S\nbwteBbAR5y7bIXotm0DaxSSD4oRVlNIgvbbx0QkvTWXxxmUO2zUEi50+4hqz\nTqbkbRYdGO79yLJsXWKiL6oOLwcEGtKl2nA2KsUPwU1TO5FdgHC6aMUdkIw/\n2cjIVTtz0HQSy8iMaIynm0QuFJXk9iBj5upYVIzxBXu8aJYrZ/2PiPgBffBv\n4KwIiNYjhZAP6QXK0aH5esc+gTENbYIXclLeW86dfdoSkltbCVw81/LFsMhs\nYJLiPuIbeWY9wndl1b+R5s1OVGIJXTcZwMjzwss9S+MpkYKXx+9AlI4xv9xT\nb1DAYrGQhcNiTf8QoTy9rHs/MzrJT2tu+0XtDjZbCmMsfudRIyPcOSBp5JzJ\nd4902hfRfY4+eTxlFYPL61SefECFTTDlWVPWRhROrhM0rk7dXNClGiZ5TVRJ\ndOdNqWt+8enXhn+LV/4J++Vr/n87/pNQRtWKQshsFtoucqQFnK2Pm/rIRc8b\nAJt9\r\n=x3//\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "b20fcf7ec0fcbe5231eaee1440d930a536b43e99", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.0.0_1562603084939_0.33759018001543883", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@testing-library/jest-dom", "version": "4.0.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.0.1", "maintainers": [{"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "edc4901d309b82fe6149dff3f817c6ea247bc22f", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.0.1.tgz", "fileCount": 25, "integrity": "sha512-UHai1O5zN6UIqZ0SR35BsAEM0FED4xXoOQnE6tzdeIPa8FaAor5zaM30qK49Ynjd/rcC3DbA9jqR+0C5WD2Xng==", "signatures": [{"sig": "MEUCIGtX0X1Lho2bHqKkKtJcNkjGNGgGSWM61BWM44bcyFPSAiEA8CI7qGOnGsDtmJczUgx97jqG67PunY8vu6JNrIPffHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWhlwCRA9TVsSAnZWagAAlvEP/AxFrLBNMdwVP3OdRp5V\n36p0Jju7kEcNCjE4zFivPKvFvaDzJonviYYJCa8Xi6Sx5JLuaA27m9XhYF3f\nKYQ2M6UGnlHe4FJoLjiXm9nXHfVM1LNMk7OZYwqTYThwf9PFw5i1TIed++oZ\n7KP2rq/MLgvWXjRYUiscRHvRSk5HXAOPy+iypU+ZzohgMZzC2IaAqWmcBcd6\n/o5T+bRZsKEkjuRZ+HfURY2lmVRbNq/5o7GtNrJB/g/NPglLzDqqygMdUfLb\nr8PvGtlfKuYJ9DqXMq7714zFuw+vti8dg4Q/FN+G8b/7w3xrlAo/TdAwkAme\nJk8h6NqZj9WS5Ct0F0DYdT/hMueuG0L9hEOjsYph5LgtMe3qLBVDHDAoKKLP\nOr8KP3Z0KH+Jq2zTcmc4pJ4TktWnpOKUWVmqIpJzARZMMctD/c1w8qrNdwd4\nwU5syogCNarWXsal6s/+SWIHXRYph0MBWzdJURP9ibj0zEUm9MyV/P451xYp\nf5aL/jiBV9DWGf3lU5tA7Qk9QJ2dwezh9maO7wrQNhC9PEZQqdrGF9oUK2Lr\nn1UICY97EbZWoB80ZxV9R4FGn3/veOt5+jZ3ce6SRpvlWFBy57nphnkrRLkS\n68KVd8EOzd9gsIq+u8gzroFRn45V4X69K2euVKoTtp1WO2izpZKVNILcFs7n\nmXqP\r\n=3g4C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "170b5ed9d0593c588615a54be84062a1b44f9731", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.1", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.0.1_1566185839182_0.41724935898745286", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@testing-library/jest-dom", "version": "4.1.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.1.0", "maintainers": [{"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "69d372e54e4e33be3fd34f3848ec0e8e9d099276", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.1.0.tgz", "fileCount": 25, "integrity": "sha512-cKAONDmJKGJ2DSu6R/+lgA8i8uyZIx4CaOiiK0yMjp+2UecH6kfjunJiy5hfExKMtR74eyzFriqO1w9aTC8VyQ==", "signatures": [{"sig": "MEQCIDapk9V3KpXnF/fyAarkoaHkTI6Id5DOH7XU3h3GMHz/AiBnoJ0ds9NTjJ72Rqvji/yL1X7ETx1qPdKlrTOx9tPytQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXXg5CRA9TVsSAnZWagAAcgAP/1QtAUxDJ8Eo3bWU9GEz\nB9mRSUW//cxWtCbQLhLTvWS0TlZgd+V4+TnDYHel3y0a/I0nVZ1Wbnc6psms\nUz+rO4QLtvVcHy2eukIXmbEVQQsVvnaXC0Fq4FCCIUo7ybu3nCwupjFC77F4\nU5Q1+W0qbRCM82R8seM9R81N5OsBV9AnU/el+OoBaRSadGtfYba/42e2LV+K\nM/kDONTyeneC3tJ5ll6AVtnsrxVhUG0lvKuGbqnAXXHejQm2m5qk9+Z5uAG4\nbJrORb7L9wQoqpg15gzg8eXqSFSt++8UHRgWFlrsZgTMqQ+IhMygFi36zWEH\n7ks/7ZaFpf3ZmvdCKB4zvSqIIFFFLoPFiDteXNp40Y9SNTe/aaiOmiNrPwFA\n1AXNJOUgEC4UksNPXffQ2VagwQRZ0deKdeQLFLmoI3DdnVlMTondVKjb5s6b\n05ebOhF2syQOE9YpWUSbVFf721Bul6XrfLJaTP3jtBKmIiU3EoIrOsf59TgU\nX1h5xtq+dkonGc7UEg4NeYXuVjwTUidLMeM+oRPO+pG8dcEEoepEJDrxonng\nvztlHdludRcpf4m4vRd9Ld+Oq9HAUblBmBmB7nSfwEMKr8Bnv7TX58JcrL4z\nec85R+HwkmFMoo8u9/WeD11P2VsWMWl5ZbIjnKDKV/FzQz+UfoXAf+HUqE5S\nJ74a\r\n=awSA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "567d3ad77a19680d4279103e4c9c72ee5f609775", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.1", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.1.0_1566406710569_0.414962324482252", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@testing-library/jest-dom", "version": "4.1.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.1.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "fe063a97784c8e58fc0498869e689151faeceda3", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.1.1.tgz", "fileCount": 25, "integrity": "sha512-4tMKJ6loIPPoERJoeqSWd0CQDsU+RPdXB1V4dgHgUpJVrObjb+TsWTG2VL32tVeUMZoXqk7cUh14YQXceFzfxg==", "signatures": [{"sig": "MEQCIHu2GUa9eDFtBySxFnzDAieTKgVxQfo2qzAlTSoM21XJAiBOEAIrBhpsAPp/eUutFzWPuEfpRhPy89NM0FQ9OUClVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm1WaCRA9TVsSAnZWagAAOqQP/jxQd/YN8bMzs/Nmv1+x\nV911oCy8Xh3l4ZxcQQSRGYLB7ktSAFneX/iFknCHgpn7yli+oUQ6mwYlylWn\nLzMPcwoo7BAbqln0lVqxhwMy+nsBkvvR5HMNuC1eN3wMaDgya2dIizOnh4y8\nsV9gVBgtYvdgD/xfkCOPALpwj3oJp19Na+yysVigpx9u/qDQ7Mdu+Aiopy7z\nzt1zvDb3zwiiR+8YwxuewwQoRxFmKlZPY+2CmgJsEBHzJvOi8cp3JtitGvoY\nVmFhtDEK8U1jF+Ti3Z60Ss5KW36oREdjFQQYWhz1Cqff/1Fxsyb1JLivvqw3\nan7L1xsxBHdaTVwGwPkQQYms6qemhaDBtvDOpO1TKvQHJq2V57OK/aMJQjsM\nVrRNED4z1OoSJDvdOAtESYYZarp15p9EN89njqVyzPh6L5ZtUiQ9E4HOEb35\nr+6CNspNJOj5SEvZma1ye8VnPbaZtQJceeI9En4yuoG/mM+LSmE9ulAoIbun\nMe5DI6Cumd3r16TWfU/ZRhSVFyBEPtN9zTaGXn/TjnKoTqHBkTKjsGPjCsXy\nqsZnKnektrLRzmDI3r6tp5LT3hGl14b4+Pkzkg60QtXB51LBAfwHPYcw4ej+\nW+VtVR/LT7izg8/Y17ad51D6qJz9Io97D6wdrTbGKfZgZgQrhI6TI3pDTtJS\nJKtS\r\n=K9VI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "cdc8a06fb2709367427f02459ca4e3e705e00431", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.1", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.1.1_1570461081989_0.48899821877839855", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "@testing-library/jest-dom", "version": "4.1.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.1.2", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "e523047191379abd67cf0896dfd93cabc7e33eab", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.1.2.tgz", "fileCount": 25, "integrity": "sha512-fNf2rCfu0dBD4DmpzqR2ibsaFlFojrWI/EuU8LLqv73CzFIMvT2RMq88p5JVRe4DfeNj0mu0MQ5FTG4mQ0qFaA==", "signatures": [{"sig": "MEYCIQCG7xy1KlHc2dpm7cUwyKpS04BJSUG6/DcPVCOR+pmsOQIhAMHZtCo3xZJB8BdmOrPLACW5jX6S9fLa5eMnFkmS2kWQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdniOPCRA9TVsSAnZWagAAjL4P/1GOgsW9CeyS9aUvpI+j\noxvd5kybotLzzW28nXEFFu+vUe+OMfXGI8bdiHg1ORi3iZN86o/svbVpr2g1\ndkcRgrlPmOZXGOzMk+cisv1QGLOFyzRbXggMiBtZxDWo7vZEWXVYyPnLGgwY\nTLIghngTaYbJohGIZIo5x1mx/BstoDM7uclPNdgDbYcMWYZx+qwIaL5Xav8d\nt9tjHs4XC8ExkAc3VAJIs05gs0D6QIL2dTcHJT8T1fjGdVjkQtByDMradY53\ndVMbYzy16+pbqXzBQSrxEwTbWCjkZZzofOnu+ql6qsF6lFKWkbJ6Oh4MFFW0\n/CsE8Xp3fdb3aLMQFQitsIlmvvyrHw/vyEoZNYYNh2b7d2k5na2b76Mam+F5\nVMWgsBlpXPXC/29DI/c6CoBU4z0pYVN/k1qDaLLVVP5l4DoebdgVHhLwIC3Y\nmOwELWEqDkQ+4+D5sS5V7oFBx/KTMW7QlRTbqN/aSyRq3PF0AtMm5cLN9TAf\nQLV2vCX3cEy6l79HLDwWVnwfCiLrm1K3WWjie7H58fMZzHnOYpYJZAGB+kPs\nWeJ9KeJ5v7KlxdQS7LY1lI0tII7PfOweL8B/T5gizydgTVDVLX+aayx2ZWx8\n6iEOKwxA03OIz30Ks4qQYvxDkB71PEoxMuABC8yZnrc3XQ5gXAuD2xwOIsmV\n8J/Y\r\n=PruH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "d048768dc01abd8e5fa5144689e4d27f6e024b99", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.1", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.1.2_1570644878577_0.20851227495900027", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@testing-library/jest-dom", "version": "4.2.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.2.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "32f8df3a78511b347d39374ea89dc8e0a1c2fb69", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.2.0.tgz", "fileCount": 26, "integrity": "sha512-H61OmRhGPWLrj9emyISx0qjp8jvC9RWyRniuLAq75Ny5XfPiOvWfnY3Wm2Tf0HXusX+PG40I94Gw792IAtSKKg==", "signatures": [{"sig": "MEYCIQCZBxS7PEt/mVhr8inbgHHiuoIWQWaeahgoKS32onX7RAIhAPNTUvM1TUmuKfIVo4Xwtf4Ij7vmPRBQl6wEu3kpcV8F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsuUOCRA9TVsSAnZWagAAl0QP/R57ctcorjstZDhCUdBD\nFzvEx1U87OoE1KVowX3MgVz5nf6PCgbfcEQNw92bWwB22hG4P66rkXiKiPb9\nwQRn3K4E/s63767/yPWc2pc52vY1g8INKwQQeStOxM5i9SE9t9j3rhExSWZ/\nVKYr37yamBv83jFD0fiteTo+aol2Fb64GPklu4lK1HKR7K160JNAjv8ayBfB\n38rgdtlKpjm4Pj1uvD3PsoYAIM/HI6TUhOheimu6RsknyfZlhoQVcZWGsZfk\n8rkAeoUJlJLHZh9Xbl+izQ5KL50D7Daaq4bHtIWlIf8y9vIEuxMGvfGMrsxN\nh3EvQ2ecejvEgMmFUb7sDMhDuoHuV+3gZJww39I7YDNfgLPdRubLWNwH5lo/\n7+u8kEpGuIHD/WlPu0Y4RVT2i0f1fh9DTaycDbqu5gJaUSHyTenQAgLUbQmP\n+HjzkQFg0N4zP5C7gXCFCiwNjLjRMyzGL8Jehs7f/LfywoDaEylJt7GZITM7\n7kMIJDda/jv63ErR9iB7BDHv4mPOjLNXSHgq8eCQS6bCcRUsWOZMV10yEFno\nC3W/fTrZJipEHCB+5IAb5Oic+PpnEinJzF+RotQICuTddw24oPmVsnHz9rgU\nmic2ZJ4oPXQ/17BhRYQz6ijy6PHBi12lPVjNa9n0HtDXGwUFowq8BPlh40Z3\n4hkq\r\n=P5Mg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "ed0b27293bee589a999ab39d920ae29473ee3eef", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.2", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.2.0_1572005133707_0.861523072377423", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "@testing-library/jest-dom", "version": "4.2.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.2.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "e7fad6e85cc1435f82bb97d2704057981b2a43c7", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.2.1.tgz", "fileCount": 26, "integrity": "sha512-rzjutoDBsjid1fBvTwwjxJG6tNwEaC669Fljmwenmvm5FN4s+SKeB0DXJGWZOECMN8YSBhLM5k0figtbr/KDDw==", "signatures": [{"sig": "MEUCIQDW8yooy+xxpUDTWftDMJY6X9vZsMorZGie92A4JDJBNwIgFflL+DqN76mY8cnmtORzFPLCxMqiA0u6RJlwLNz+2dc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduu8VCRA9TVsSAnZWagAAqZkP/ioaNn1oA7JEXlNE+Hz0\n9LXjiqTVTg9ohle0u6dnqS3IU06pvFo7DKBmJEcDER96fKK5bf/5eYPpGOpN\n+iSYonr9+U5s/bAQ2+HND8kN70WBGHg2RzdiKeWThQP2nHLfARoJ0s6JufbC\nT2p4ZmohzW/z6/LWyNnwFI3UaPw0X3+jq1Nce9E0IDN1USicel8s9uEsm6uY\nNcBXBiyUxWySvDFhG6lUZ3X37yB4M2mPQR/9+q+Y/z4FKb99y0KHP0dIBhTY\nWH+KtaatJZ9tnH9nFZgrQv6crj7Q9Aahn/B2LiQpmJp6sjJ1b8MKdKfT9b4K\n1skNR2+qTy3jofepp2HjzlARmcB4aXaAXQKBz1QBJThs3NQksIYUGjfqLiFT\nVdLUlQySYaZiHo65nifmljY1xzK4ty3+z+rEw73qZ51feVAsnKwDzSSSq7H4\nXmUgObzU+Goan8ahRlE4E/9bBQpexa2uIDl7va0yDTn8JoTnNS2E3jPRa3zw\n0akr0iooXjWK3vFE3cSfYMEcDDU9xwObbBZ+dY9xHRDdAZdBO7/SdYCykxQq\n6grFzCx51iXmypo5tnF6r8Blm2RDc7WXlvhmiw4jOIZcap7dK4toxA3F3haX\nQLAt41bsyenuS6B6mIxAjS5XvOL4lDWuPYLhlcOcxNR3fg98js7txaVN5Een\n77e6\r\n=FC1a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "f881e689eb8c8d764f7828d93497bd8ac435108e", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.2", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.2.1_1572531988567_0.2655053410580974", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "@testing-library/jest-dom", "version": "4.2.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.2.2", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "f1463a329697682ecfeb4a7adc0fa51a1df7d2bd", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.2.2.tgz", "fileCount": 26, "integrity": "sha512-CvGutOOtep63cJ/bLRjnMQ+dOvzNPpVfXIWr9iLL9Ydbkqt64xG0/O67IFgZ05LSXoxGsnsjvH3gigvMbw30gA==", "signatures": [{"sig": "MEUCIADG+mGkDub3KWxDEpZnxoHyD6Fg+qjtOaqxstyj4B28AiEAwazvn41ZQHJ9U9WzOBJCJJb5cwRIS1X9FcBPCBHIrQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduvGbCRA9TVsSAnZWagAATNQQAKTCF64GeNTNtjYCvt3T\nZ4S/xQA0jJ9fABqj20Or2OVCx2OrMDy+UldUo/GDP8aLSCcWEyIUGWW0qOjR\nck38JVFRNY+XfKzWDrmHckE+9a0XxLuDt7D3i2gAW29wPwchcHz/0JcTbTcD\n0XPlM/GfpuozNaigN93bXnIw3eJdviE0kc9LRniAt1bg0ZEkMCaZsr3oQ6i0\nW7FJbvkSsYSxRIeR4xNNIA3SIFOjuF23vIlCJ5CCcvqe0EpEUOGZjWhp0UQU\nkaswvU5VMPQbR1E0PfvL1saCgP1pg9bhp9f1WEbL/PmvAqFJWMpYTqCBxF4/\nnmFOH0WEm4RpSK0BGTqAMMvDcUe81PoIec80MKHK24ElVaYT5IQtPPJzm34I\n4LcfLjcGoTiQUYfcZz7mS7AaYxNTeSphbTq8dSIOVT9wUMd52mjVoS6bNd50\n0wqnyc+LXgHKRQPbGOokcIXzJjHRMlQWKx/2cCxv8/EP994oqWiqFK0Ujfqk\nTH5TbToalWvUyoSTc5VdGIZzjccfifv1gn5xnV8GKoB1COTfhqvGE3ZZJMDR\n3wMTgTj80qrh3KIV82b4gKNpxK7/y31FYVuTBJVCLKaUaNws66xajnyeEjRM\nnud4/2l+Mj+Cos6c2bSQiXolxduRbMOVp/rCveOjqghaOMGmUa+MeNU+38lz\nk3TS\r\n=PUmn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "9a36d665f826539f8867fff3e74a98b6b0e8fadf", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.2", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.2.2_1572532634677_0.7843484460872145", "host": "s3://npm-registry-packages"}}, "4.2.3": {"name": "@testing-library/jest-dom", "version": "4.2.3", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.2.3", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "2b40d463d6cb00b5550d04381912e048e8e35966", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.2.3.tgz", "fileCount": 26, "integrity": "sha512-lrsm8OMaOLjh8AJhTNZW85Vur+a2U00ej1r/dNzABN4vfJ2kllsP/eLgkOdfCHuspdXn3/Q6rLt/41dSueVCyg==", "signatures": [{"sig": "MEYCIQCXV07sR9nHBKsFSIxqATO57Q/0z3kuC8OOSrfxIwpnsAIhANF2y3otrWbTvtNHhQjvCJP/c1W7hxbim4NdRdjArX0b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvB9qCRA9TVsSAnZWagAAsVEP+wZ9Fo3V+kVZ2HE/mYiN\ngoKNdL9fNq96bQzyzoS0AShJWtSfYzJjA74e+Hrh7YPgdXaEiGSS80WXVuG6\nre7jg7DjdhKmTz5MlH05F/95ygrJfHWnE4jtK5GAJP6sNoxoP8Iz+W0GcCi1\nRKpqPf4wPDs73z5NsnctE68O+kqMw9NHVHGBhUctLOt4Td/yDYiM7ZRZvNxg\n1mZLHt29M2UMbmTGTx4tF2agY0a+kMbTnbGfeID2iuI5l0o4cf/e5NJgADiP\nHdN6z9mWzZUt38whUqD7wxcBv+2MxYilgfIsYZtWN/PU4ZOl8tk1d0VqUdua\ngcI7nWVABDfK+l0DF8y2FVp85AAD9BEonqbG/vgnZsfdwXoKExzPaYDbOWDx\nDAxxNvVJ6XcANUXEEx8A0HfGv57XVia028hBjjCozVc6TJu6/ZTdeQ5Au/JC\n+lxCVr9+rHX8BjEsVpVhB7l/XA6RcVZanM8ok+22avee0OxKPz96VKRRHmF6\nwhA8Wpeavpf/eAqEk2CHUBSRR964MerM8blhUgCqmGH9I2HCMY3YflPh+BrV\nfzIswF05ofovIlzdSzKAIHxXbT+8aLSWrNaSNMruCHe/fyhRqkmWN/ExMgWI\nqM8uKo73rpqnDXj6tI1LJXIIK5vmWfbIN++A41O7SgcncXNMGJ4g804wUYJT\nmcvX\r\n=mS8l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "c12a476b66c9d921bad017394c2b4e65a4315790", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.2", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.2.3_1572609898265_0.8292952822894863", "host": "s3://npm-registry-packages"}}, "4.2.4": {"name": "@testing-library/jest-dom", "version": "4.2.4", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@4.2.4", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "00dfa0cbdd837d9a3c2a7f3f0a248ea6e7b89742", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-4.2.4.tgz", "fileCount": 26, "integrity": "sha512-j31Bn0rQo12fhCWOUWy9fl7wtqkp7In/YP2p5ZFyRuiiB9Qs3g+hS4gAmDWONbAHcRmVooNJ5eOHQDCOmUFXHg==", "signatures": [{"sig": "MEUCIQDTRbMI1zt+zX1cRgGTAIpZvRahTLLdj2a2kG1HX7qP7AIgP4ve5+ArNa0gLdEXplkWeUBOtT3C3Kzw64R80Pb3DjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzXYACRA9TVsSAnZWagAAslUP/A9BYob4pO6xkIcisi0o\nC4bTFAiegNrZ4RuOfP7YcYY+dgCIG0JdvmWrR1/aS0ezLDqvSEP1GCHh6o/P\nk8yKMIDRiX7mGVIsS8vLC5Cy8QF5foINRUtcIhVN0HduYoMHka9J0HnwX6V3\n1+pLScSp0/slsYr08bCMK9m8gI4y/XZh1L2F8XmUsJuk07pwwE8QQR23PlQS\nsOzqg58ID3UVZC2eoN0pFlJnOfUKZap19SOETcpz/S4GPyOm9HLeLp2vmJ0g\nTg9YqDRbC0OVUnDAUnCdVLlwJlhaHLfrrwu3DORg0meCWzwOgGxiwpsHRhRR\nFegDyp/0XevsUkt1GmJNXIdCCyoWvKF6BGVKRbYWNuBotmRWM+OtrcXif1CB\ngQYg1/Qev2F628FigVaZkAbZKIqgEsQp9ti9q9fyaj/ZL3LAbgHXEUEdSMMq\nciQdMk8vukn58+PdBXDdHZXsyCSFG3MFpOWRmeaUgQA3zDaDQv9T4ngsWCt0\nTVNqM78E4DzNLAJO4KfWgl37inHrBNBNqGl+dkdVcPTXakZeUt/nABPy9wC5\nB1iNLyW40Z6s4lNBDxvg8982pag7/njiaF/o2Tay87V60HxTBox3YpKBZp/o\n0S4YREH4VrVYqIvDjUMFAaGjOCG3VxDwGsNv/3pVTXB0WpR8sUA9vxtBJyiJ\nxwqK\r\n=Em2e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "d5f21b3b8487a0aae9ba1be8ee5b62893adb7b3a", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.16.2", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_4.2.4_1573746175899_0.6781906015807015", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "@testing-library/jest-dom", "version": "5.0.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.0.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "1c7abd55c90d5ea9fc9211e461967010188e186d", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.0.0.tgz", "fileCount": 26, "integrity": "sha512-PRrB+Y5hTyp6CSFAPe83aP7rPPgAcO29wPyGYlmHhfE9YUQIYsOigODFhgeq+R3T+AT6WYn6bU9rIKNmRCgvWg==", "signatures": [{"sig": "MEUCIQD+GS2+X4iDo0NOdXJ80IFkyJ7CSgjh0CmtE4n6hySb/wIgYMlzqNuYN9t2Xb2cswjPWkY/lCmqBplH7UNzwtxn390=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIPnXCRA9TVsSAnZWagAAU54P/1XicjK6jrmEOe2Qq/Ua\nLqTU8mDHonhUabPqS0oGIVnjLDjal6V2nN8qGMCTA1ord9ERllyRHhSCtFTO\ntHpxZwZ3NVSLTOYF1Zk3drHcGPgRbyTNgQoXeVUI9UCZDN0R/sZ89yxdeGPN\n+OIpmjwXXKcuM+6Fc6+WwcWv8H28xRiRYK01+BCx30FBvrsh3lc2T4XDJMbi\nZ9lhjAVK4pkhPSTtfb71kNVVleDjbW3V4MqyVA8J17QPEWzJ4X84CJg3Jngs\n8yC9x24+75gM9KJzSM6HDfz4VAS/yjW5qfxgJmXg7y1jF9wz09xApgn6K3lO\niaK33qYWfvx4to7DtYR8mjSVYoPrpm8RtLOED4Dsh1tP63PO2N5B5rhXNa5a\n6CZUiBeQLBXWaGDh+B2s+/xTMgifGjZFzuUoJk+6GCt1hUCSfsKPZ2Zt8aIy\n/YygUjK9sR5r7RcuNMuL2OZFWjGRJWYfeS4547aKbRwvFJArNZn4yfjMkCer\nh9jETSETw7xMQ8XMUXUXE1FNp22ji3ysO2pYB0wpAtmDlLYlIDEplIzuQ/uo\nylgTwlmBFXiBVM/SAjOLc6mH1Fx9434M3TkS9G/GJmCzaJCp2otus1rQJgzV\n6b82JqodTJXuG6rAXBdwrOV4f7wVyTRaLA36Baw33WEsHfOuRl4bh88R3Wbp\nSKAP\r\n=8CzH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "760409aeb9a7e76773c4607169a0cac40c59b9c8", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.17.0", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.0.0_1579219414620_0.8685626766863992", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "@testing-library/jest-dom", "version": "5.0.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.0.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "426e74208eac673360c3ea5df78a836300f989c3", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.0.1.tgz", "fileCount": 26, "integrity": "sha512-+ZkwvLfCGfcA9fD5tCkx6p6OPgrRTe/Jd4Xib8r3aWZrgWSZ5TftrakHqVIVj42pZQLXWzprmfLG+Rjx+KrkAA==", "signatures": [{"sig": "MEUCICO2Yi7h1SNSKkYUQAfhlVeTwSFIgOokxLTRXN89ggbLAiEAkIvx6dTTSy0bPmXJT+62f/dYoUFmuyjkDOIHx3iJi4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKEzPCRA9TVsSAnZWagAA1G8QAJHa4H/WS0F8IoOA9z/h\nAGKqReGrWW+mr497/E3lzx+TnaPu08EwKCQVtnGWQ460EXpm8NfqLp8QpjvY\nem0mX/GSl46lwSNK1KkGYku3ftT5oF1ug7gN7ioPHLq64GMeJ8NsakuKOwIA\nowI3O5/ccQ0LS7/0KVBLsXy4GpwBjockQeZJjC4MMa6CQcMgEXqZ9XEd0yzz\nu27bEasL+YbKUvZvldzy7LZJMXH+Az96kLsQpVnBxRQScHZZa4CmxeGhYzfs\njnr7XUmhCSQ4XOfAawNDG+maHqX8NlID+Pya3QpDyR1utXJkrmGsfc/KicJZ\nHSdGzp+v865ytSs9ytzi3eRLeV9mU6mM0QxEQmlKsjOm61jRG6jIPQyTZ5Dn\n8ajHnMlkmJSg2I/+RN9RVtN9OOJ803XiZnNHQKW5kPTwkg41nwWGsoTkF6/v\nu8QZS2+ZtTb3E+5Gang8Gi0XDmZ1lXZp5mvw9OA2hIIKRfNfsGj67fFq74Ni\nsuBceSdMAalDycfbpzb9ny1ufLHui+CF0xECv0oshNxY2KY3uPZVQqe3Hzxn\nA3gBn4zV3B62A8ZH7884f6vFpsUhcpkyEV5SF8K+JkERsjt6Q7Jx22QzgldE\nXnYl90vpKNb+83DoG01YnZFHyCH5XyC7UGfFHblcoOZWTj/Q8hGurv+sO7ld\nserj\r\n=JtAz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "030da6207a2e4862035b7b9d9f915328c5ff4e2f", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.17.0", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0", "@types/testing-library__jest-dom": "^5.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.0.1_1579699407116_0.22326369007542413", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "@testing-library/jest-dom", "version": "5.0.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.0.2", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "dbd06cc9a392347327597dfd633b9d7623da5c80", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.0.2.tgz", "fileCount": 26, "integrity": "sha512-Pl3si4eH88+5wlVz6NjecLfeO7PjTbbgWKwVZXUtBfrGuJLjGdUUo+O92XRu/59wft8HAmwvXHeMWpqWTg2Uwg==", "signatures": [{"sig": "MEUCIQC5cSKQ/3ZM0zL35jeLj3OZyqlyEIVZ3FJ92XDVwyrO4QIgKdZPld/iXxzMRYCLxlY65PtZG0T9j9OpNdLRYLP9i5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKF6zCRA9TVsSAnZWagAAgVcP/iSD6yxyeuBw40LaGYr+\nzod5103wtFWsgzNTbR5PFcy5286Z5k9CqdCgYwJlT03WF3HonrVgehZpPnMV\nQRjlBGnvUnWtX/bkBv/jOz903GFwL7gD2Gea8MOSXOCbThzbWdP/4A+5kN0Y\ng61V6q4GmxYZcbJ41YTX3FcksElmaozGhQ2i8L/87A+ULsxXLYsSv87aN4MD\nZ4mhsppkwzhvUjWlutrK/pqvPHHAOXsLEEakXy/vsxmAupBrMrtmbwxzp8YX\nA1VF+UgR3JK1sbu3BAam/9iVk2M7uZJ0RknU7oTQbpXGgSX+KkM+1Xi/Zy2h\n7HuTMB+x4p2kJr9eDxU7PMRVosTxNe2GPXhUYV9rOKC11eGkIFMe/r+L+YgQ\nT9CE18tbsYzCVE7AguOBwju55tWcAkAs3PJzHVVmn12Myg5Wq0Sm9M6z2plK\nrwKPOK7iZw2V8Hq0fuudfs8zWjP/Rq1KIzthE1Wq8H4KVXw5pj5ld1e1750Z\nFaA4TFjT6Weggq8LEWkWl1QbfuByc3BLxyfLQiRD6yZLT10d70Tk5MCdvAWR\niA7sXlYVfJ9Ou925CQuFBGaCdpmh54ePImSoESMh9Ru7kWpN0buCrsFXDpPx\n3b4Qs2ChnRD/5I7og71jmf9yBgXPT+hBoIrs+peLfQslLwnP6UnbQygtrkBO\n9poN\r\n=fYna\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8"}, "gitHead": "e4d61c2ef16018197c316135f57f905bf5b2ca2a", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "8.17.0", "dependencies": {"css": "^2.2.3", "chalk": "^2.4.1", "lodash": "^4.17.11", "redent": "^3.0.0", "jest-diff": "^24.0.0", "css.escape": "^1.5.1", "pretty-format": "^24.0.0", "@babel/runtime": "^7.5.1", "jest-matcher-utils": "^24.0.0", "@types/testing-library__jest-dom": "^5.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off", "import/no-unassigned-import": "off", "import/prefer-default-export": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^15.1.0", "kcd-scripts": "^1.4.0", "jest-watch-select-projects": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.0.2_1579703987012_0.5416185032586545", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "@testing-library/jest-dom", "version": "5.1.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.1.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "1de92c4ad1e500ba2a5548bfbed8eb2097f2b87b", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.1.0.tgz", "fileCount": 26, "integrity": "sha512-7XyY9OQUk7JyHsgQTD8b7be4iOjr6afX8XnKg2hWpKW08TLqcHG7nJ/er9N6Q/ZEfmi1pA2mTWbMZBaXbcgEdg==", "signatures": [{"sig": "MEYCIQCOVbu3dGiu9C5+0xqrm6Tr2V3hBFFFE+dWJAC/pXZubgIhAN2mmXJxOnFI1hbMVEsOy2m1YfyD9vbeJOVmsLlMG6i5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNIIqCRA9TVsSAnZWagAAZIcP/0cf0xHaVdsmY0dPdhjY\nrqdRUscjBcT1AxP3aHUnEpCmE/rLfddVry2txfh46YbqiV+G9RUPgFJ+tFJO\nikXDn+6DMopgpoxke8KPS1InOri+nbFptySD6u/KmRQbNMaUYJZt5tuVAt8d\nLEtSy3fGRdN6Agrg1ZaJyIVlpey4nJ1jDUlHsEIHtP8Vitv26GfmacxbeeM3\nZ4zzUEJy2FMdwzBpW3j6rwM2RmYgSLOoFH6zWsiQxoRPA1mELxghzuJHwgd/\nzCTRxeITGsU5HIimJzmL0uZ3acDGy9auKnX+8xz5uzsmrmL4wagcUx4Sdtuz\nf2aS26XsLD/TzCrgjG+GKfHc0cq33R0uIaUskhgHb3qPhtKHL4TDPO4pH7Do\nRjwrSd7X9pEnDPYF5z+JLBZfIUwnCwYqGrsSSM7hJSIYJu1bmSwQhKPzfAI6\naEIzGA42HAOPbZvVMYf/Xt1/UmuAOTnEzJgxBEHYKbvu3DdKWIgMH9KVRggt\nUDtwTbOb56AvRq/t5papMLf2ykfVF5kNjs6011u8TP25gaZO61Yh2AEzNkpf\nIav+cvDSPTVX+MDgBTvFfZ3v1Ck+Gn87s3lAG5aA1n2SfZhPslQSgShL2hYr\nTaK8HiCi3hjWSgAS874l8gQvqsAmcyb6JyfDK2Dy/oMsyW16mFcObCIIeuJG\nLJPW\r\n=Pz8V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "7921e4a0a9e21e419122951eb28ed270c13209c5", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.3", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.0.1", "kcd-scripts": "^4.1.0", "jest-watch-select-projects": "^1.0.0", "jest-environment-jsdom-sixteen": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.1.0_1580499497726_0.3935254035136728", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "@testing-library/jest-dom", "version": "5.1.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.1.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "e88a5c08f9b9f36b384f948a0532eae2abbc8204", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.1.1.tgz", "fileCount": 27, "integrity": "sha512-7xnmBFcUmmUVAUhFiZ/u3CxFh1e46THAwra4SiiKNCW4By26RedCRwEk0rtleFPZG0wlTSNOKDvJjWYy93dp0w==", "signatures": [{"sig": "MEUCIQCkzBleTnXMn/rFFbD1/3t3DPOKOZdeTDqKZKLpCSwAEwIgPE5MFeZAisdj2eYxPw1s7PsvmwmJLK78VO6YFLsJDIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOFlPCRA9TVsSAnZWagAAzcIQAKMWofTv1RFhLlmkfjYJ\no3RD+M+NM5SQn/yJoWRxfOMCoIjtVxdrilRKctpRptfsD8hyOAdJ5xPUWdqW\nf+22opWY8T1aWcL2ux0mj74JsblW2EDI++mqfVpKhMGfKQFE1V2c37BtyQqs\n9Y0u369JeCte/T83BizJZL4UthbCiC4Kst/FPdMaAidqlMw9QPkAV4QQtpRu\nB46X4NKCGSXNNX0sHSgSp5ReRXcZG58/BEZexNQGK0mN5pmW0qcRlGt4zOmH\nInCJgoBAwNYePRdsMeCR0MMW+tPnP21AWucBpEWP4S1SLTS7m3wyR29x0OQu\ng2ex55tVWz3U/qsCL1bv0PAOcCzJoOHFfyI/rTJTT8PBKl/ZVA7oqFjnLL+q\nxqWSmBmybIF1MRvjU6/BTBRpFu52Km6OV4Gg3UNHIWJz0WB6Lvq+mY2R+385\nVYlQwVeP2cE4fm8T86xal/jYI3hKHGI52zhau97mMgoOOdusB+3N+S12hiR9\nnv0EC8th+14X8u9t1u5D7MXyf+wNOzrUVhyDHTrcZ0gXT1aSNXHWTCAx9MSM\n0FR/6f4vES0yz4hWGlHyt0gcuoRh4zS9ou3WoOLsDCxx3hm1uwpde+bjkLoh\nXWnooZy1x83d1cSKsrHHPcQWSbVIaOINhtCegI3BD5Eb9Xi0uXopmo0zBBb7\np7cS\r\n=IOw8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "3b98d4d16b51b7b8d9d9ca77172b5367503f203f", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.3", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.0.1", "kcd-scripts": "^4.1.0", "jest-watch-select-projects": "^1.0.0", "jest-environment-jsdom-sixteen": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.1.1_1580751182919_0.2093388057565655", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "@testing-library/jest-dom", "version": "5.2.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.2.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "9c3886dd88b5c7c9e25bfaa05cf16d17480c565b", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.2.0.tgz", "fileCount": 27, "integrity": "sha512-ugXtbRmH5wJhx9FzkV5X5hw1RDZPEm9LR08eRdLqaGWWmUpu4KdazJdPwGN0C0szNtZUc/ox2RIQUv9QZeTXbg==", "signatures": [{"sig": "MEQCIBvmTdj1iAe8qVhRxWqK1RLjdjZBbl65hM6Us1ULZWwaAiAjap5IAmlkCx9S93ffud4LkaJ1+h/e/Pq1zI0CPJI3Lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee45CCRA9TVsSAnZWagAApU0P/35Af1Wj+OOl6V7IRNoK\nnecZ4vSdE1f7w+QDGV7LyrljJUqeoqPiirUMN69mgzGuJsZ5s8KvuxIsur3a\nJZUUnUgblI+lQgU1Zw+w91Ty9JwqwAn0ndAmXcmd6QBPb6wjQjiulY7j3O/p\nQ/jF8dv+3hVUrDZKKudc9LHglQb7YCtSX7siZXo0I/XPLE2aM4pkdmOvfvZM\nmOeTOstMAJiwQbnRYelpHPGV343536Q/YweqTDRd2q4Cvg0jNv+Vllti2PBA\nDLOfGehW+oJAiLm3O4P+lrY9+hjaCY7+bUgXjj2BCLeFW5qiqOy9diF2E9Kf\nt2C7XVRSt1YUavoeuQM5/3Fs9mGtrcneAIf7j9YMuvhjVgp2KsJ6fqwdfE5i\nQyOF3CvJKySfKmcF0+9o/0Fm534GoMLEWw/bi4ULOKSu8i0NJdBz68DZrDTR\nwfDWbD2gKo3ADgsZZBM3pAO2icdSjTxY0WaO9IPf/bevJcwJV4rPDF453WWB\nBEe2wyKyJth3vqmRJ6p6ys/zZsdo9nu9OyulokJ+k+3XN8p4GGYz4urexfF1\nwjBGqiZ4GhJfae3nvhjTJKjfshp3Y0BUhKMibmaKBSs67VXKHF4iTRmXHmpc\nUtSDVrGsDV10Y1Vbr6le8BRb8MuLMd3pbR+PzjjlvB1sGAWncM4k9w5Z+VMb\nTKzV\r\n=I5XZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "eb51c17438a6fbdaa027bc35cdd07a031a167338", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.2.0_1585155650236_0.4772876643770858", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "@testing-library/jest-dom", "version": "5.3.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.3.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "2ae813b8b0eb69e8808f75d3af8efa3f0dc4d7ec", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.3.0.tgz", "fileCount": 27, "integrity": "sha512-Cdhpc3BHL888X55qBNyra9eM0UG63LCm/FqCWTa1Ou/0MpsUbQTM9vW1NU6/jBQFoSLgkFfDG5XVpm2V0dOm/A==", "signatures": [{"sig": "MEYCIQCPLosqjRvXEp3Z3qV2bhsOx/IsqdlFLNZj6wFN43tNngIhAPbVwt/cjuNqPdk88r2/Zjrb0nQYfWO4QRklZG/MTC+Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefKRZCRA9TVsSAnZWagAAzbQP/RHuEh6Xd03DvHEaQ2WZ\ns4QNahKBzminhqzHLM40xhTA5gxMPNlq+K59A1kU9brHF9cXt9iK4NIqaidr\nTsxcG8h8yVKMpWLtTm618cj/lBumX+jpv3Vhr8j47lUJ604W9bhEwwOcHoSd\n9EERO76iJr/Twnf5RNo1tS988uBumwat5TRQeXu07p9VEs7xRIM+rNNFdyo6\nRALSRlmHJtE8XQvaGlP+pKar+5x9vpj29JpEKliuownqCsj4J6iU/zq31c4f\nvcr7JRdc0xLw9x2QaEt72Hi8hoOdGloxl5K65Wg3AC4aiTSiTSdkt0ZIotYD\neehdmFH8jya3jz8rOJ6OhB6Iljj+FlBTr5EpcMwAG8bfc4Kq1elGTeDD/QNJ\nREeW/R5QDq44CzcAyo/UWP2uhDljejhKHiOErKtAJh0feRTytp3NR3x/XKMr\nhiLyG3MKXW1GR2BkDhN3qona3LzUqSL0Y+88x9iXN9PqVVp9IAS5jNwpOBfV\nXtvcsl2/MLPPCwkmolyjYKEWG8f0VvNCxVeGz2W0LKFWQ1L0ZbDqJpKjn8wL\nFIWRzwwZoPpSf9r+tuLS3LgsIEzq5XRwGDOR6Dv52cZs5tqueNm8ToPoN+ve\ni0LLLGi0VXJgDNTYcY9MSE0GASrAlrqSwZGsA2i4F2icRyXjXbkeG1pZk0hd\nHfgJ\r\n=vlOI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "cc8721e10637137ec15e731db2aa8af4b13bf21e", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.3.0_1585226841391_0.4490966256544955", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "@testing-library/jest-dom", "version": "5.4.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.4.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "9ac08b6dc7a750fe3bf7658116dd246890f836d2", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.4.0.tgz", "fileCount": 27, "integrity": "sha512-dsXGGNiZhZVrBgQ7lxznZmzKvJ3DfhmTH7jkgfyDCjgyGbviLrGVWLQO6rXYfootU81mw96HaYH2L7DthJEC7A==", "signatures": [{"sig": "MEUCIQCHjEoIaDvMnQ717aVDkKnbaIR1bhauDWTHhOC1UMYMXQIgH2MMpuBchc+q797nHLw+t6CSBo15ucy5W8oHkjU1bJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejed3CRA9TVsSAnZWagAAWUgQAJXkZJb4DMtbIZP+uSyG\nKlKAhK5CX/wBEHPJSHTo9IPM42PbCYfTqcMm6M3m4H91jmTAhJAgBKsvgwQV\n+rsaA6NIhwBGgNN3wsIGCdDekw/Geg+3h64GC0ociVh6wdSdp2uO8VZBFcZk\npTc4MTp5WLjIj5cD15Ghe2PIs8UzepF695+tzWpQsMogxApJXefixVbOhDtu\nnKfb96dOV16jQkFY32hagKXlSaOFQEIhhbcgElIwwEfj+ajAFhohG7srbPLp\net++VetKgQ5ZT1oTz74UM5wJkIDT9tkcWYZxsGAzf0VPg9y1uOcMJiqT7UQN\nyLf7k3mN7PxCSWuyi45gq5rwT41xwU7V9gs9w9Ll4ICy+XmWNGrxVE00sNUE\nTGhyGA7Kevk4uy5S0qPQho4Nm4gak7wT6lLaprPP0hHxJwSoxwcADl49X1Oq\nzROQPT7gZQF7FMmidVs5S8VsRW7SBR0LAAtQ7p24OeaQgxbTdWDpzcoJJVvE\n5+SXSAkU9a6R8q6RKF9h+AEIRf94Y+TaaKF/WUlI7G6Q9s+xXgTxI7dEg+3c\nXB57XTY5GuKxYCYVZR4AlYaoukzPu4ZRlQdlKHBe9RepAqeKeJndK4ermyFu\n47RcXc8qSNL3cb9PceL1//lvdt7XJBMWgs5q9oB80p7rilsasmbK6ybtNJ5Q\nUIJz\r\n=s4jf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "144c647fcf5586312ae7e1a16ff4654713097209", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.4.0_1586358135113_0.3407141958027404", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "@testing-library/jest-dom", "version": "5.5.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.5.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "4707023e8f572021e8a84f65721303ff60828d88", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.5.0.tgz", "fileCount": 28, "integrity": "sha512-7sWHrpxG4Yd8TmryI7Rtbx8Ff4mbs3ASye3oshQIuHvsCR+QHgr7rTR/PfeXvOmwUwR36wSTTAvrLKsPmr6VEQ==", "signatures": [{"sig": "MEUCIGYxACyts/d5a4URNZdlPdAOcNOaXwRdgjfHtnLoIDrpAiEAk5MjqAA6S/pHjYSAva6N8kpALk3w5bjxZL8OfAu4Upc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJej3IYCRA9TVsSAnZWagAA8zYP/jgaDYWP3holwsQ82nYU\nCjQW9ja4ofQly8dkw1yeQY28DJlzXWFqy1sBDycVN6iE0pBrU7xwfNNcy6Ge\n3RUlbP+jBRbYgY+/SK1Ozz4qw4NIcqGnYe885TPboHVCL/SsW58oin/aqQ97\nRrRpnTZ4NfYBIgdFHapq2TXH+IyrwxhhsjmyxLbKKLoxrO+4lxhkJ+sODgP7\nIsrZQXp2dpvzWRazkXpNii122KSwPfZi+vK2wborbW7G8ChKxjpCMBqt2L+I\nlwTxedf8o1AyQuh3vhQJfyBDEME0fMV7QkwS0Q03o8hmQ3znH2MN35/N0too\noNxuWqBx5zJ8TP6sfi5SPO9/fNtWG+QE5s5cvLRcTw62c0j8fgvweQo07HwO\nuncuI4sESY7lSgb8M4fpMktyq/r3v3KCjftHg67OlAM+pFvXZtGlgC+eFGIc\nY+zZXEa/ZIp+CVkNaQuYIJY/PpURFFVh3/usLrLT3cyMqIatmSLZSLp2wIQC\n9GjaI4xvxoS1taI6JNKkHhi9J96Gu3IJ4f96nUbovPsRrtfNeQlN3HogCWrw\nnR0NG4C84oqZh0suMILhIULVf0MTF2r1dRTO3pNGQuI/sn4wzMqqgcAwpENu\nHBy1GwRLcwNmOIQStxhO5YN7hGN49YtjPqaUg+cPgfhQWGwk3NSRmq+riK+B\nQtNp\r\n=o1Zx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "025c81d4ac1f018a48e01e759bdc34ad696a42d9", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.5.0_1586459159605_0.7861576291633618", "host": "s3://npm-registry-packages"}}, "5.6.0": {"name": "@testing-library/jest-dom", "version": "5.6.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.6.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "85b2391233100794e381770de0f2a5b8fd16c8e2", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.6.0.tgz", "fileCount": 29, "integrity": "sha512-9PRpjREPL8EnullMLxAVJhPEBaRJuV06as7KU9Zgw+3rRrcMAZiPTcrfq6qMAbZVQEnruQav7Zb5lJfEnQDCuw==", "signatures": [{"sig": "MEUCIFYT+gcVDb76tskmK3GZHG/d1VhodYkc+z8Z8bE05nhBAiEAg+dymAij/8EQuTvLQmH3GQgAeq19dXGiIkaLArAWV88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetFU+CRA9TVsSAnZWagAAqngP/10fm2D9dKEfIb134ahk\n+F8LAKUxJKvVbAfPWHEP2NHmyh4fL5nJ+5KB1xQcNu1IfpIcQWVtcdMMWKXg\ncM1bFWGJZi9d7itZKQMenq8De5bpLK4kqzjxe4um66yLm1hJOLCk2sbd00CH\nsvnDupBMJEeTEtgCq+2dYDjF5PTqCtcb4EVbrXrsEqO6PyVssFjgjVJkFZ05\ncesd6ZwJ3YmQs7OjkvC5zPm88srDDo2dlzpyq6A2D9XqjIHig0DMrD5Gx94K\n6oz10SQW1xp2tDnK6B7kgnCW6IdZMFXo+vxmmQ/hqA964T6NpzOVBn6qY2Gt\nHNC/cb/3I2FTgLPefqil47E+sOnxX3woIFWIXDzUjJZgaQIf/D0lSTAdBcBW\noHczCD8Vx6jHp/xvwxT/7c8inIr0fGTzfSfD5QoBtLkxEruwpMHU3RbMyNJO\nJs+PAqwfyzS/xg9topMB2z68xisAal99xLXbih2ttusXKy6KDMzKiIH9tW+H\nHJWtKS8IeoLcOvE4Hz45PBCls9DiC2tW1GyFq91MJskJfJUu82RnvZD94BX5\n8oOQgKCJ3KsD7hlhOpQSHEM/+oJ2a3+Yqc0CFZZ0OsZdKK74EpYpktLTI0TY\nqZvAcJ7VDqP3zAdyV4Ypbxq2X4EiA+YpmMQruCErBowLHTnCce/Gcv8E8i1o\neAoV\r\n=/nLF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "943a0c9783de6acc83a397b5dab22570f2611c3c", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.6.0_1588876605436_0.4522731071860657", "host": "s3://npm-registry-packages"}}, "5.7.0": {"name": "@testing-library/jest-dom", "version": "5.7.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.7.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "b2e2acb4c088a293d52ba2cd1674b526282a2f87", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.7.0.tgz", "fileCount": 29, "integrity": "sha512-ZV0OtBXmTDEDxrIbqJXiOcXCZ6aIMpmDlmfHj0hGNsSuQ/nX0qPAs9HWmCzXvPfTrhufTiH2nJLvDJu/LgHzwQ==", "signatures": [{"sig": "MEUCIQDCzZkqtW3GjmsW5NoCl9152g1PnlPdrEBd9nRr5ppyOQIgC6tn2ggh5g/Akv14fW0Uj7GoDjFvDhbDHSv1FJyUNTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetIcNCRA9TVsSAnZWagAA/tgP/R7l3BGdvGjsIgS0WFVD\nTCCad2tLhfWLk84M79ajfRvJN+nODzPXLwG2QSpkyxYFHjtstDVQ3gqNjfQr\nXYMwt+ZrbMidtbq2OHIRx56bS9nMQsXhKkTbB7jeBA/Db+DE7nnAAXx1f4x7\nNZOaoufviRbqxlIWZ2irfDHKaFv1k06D/jzKqPDJ4xPew3vVWtr1qAkDgQoZ\nmD1mssusk4HqO5OrGi8qo5KZleW+qfrvlEAYGrCBlK/Jhbw1PoCxK0DfHEDe\n+MAPFi8ur16EdqZ0ukiV6fgljDZEsqf5/QmTZOGmwfFt56anrcs5fSGThpzo\nc4n3lQkFPbfZJqCTuX4Kf8yOp5O76/b6+0gNTTJNri8DceB6m3+K9awxIHRK\n9zecStoqDN4q7/C4uIvr/yLbRVNEL7vDr+46xrKrSxak6VSA4BkgBpn1ehMG\n+E6/zy3KTGBd6sI1xreWyVhdOro5eGlN1JDkTwP4ffkASC7E6l4qZhwnUkPT\nVBKQdkOVr1ogA9+tQoPYnvo7vcbjUi+7gm+venBqfh6EyRIqP9aEXMXWOik4\nVWQsJqdZyNoBkiwYSGZTIwJw6+kiV/dLyQ2OYqYbZposXv1HD5ytCh4CL2z2\nF2J1MTbqD5lVlTwUKQ1zv2OuxzUBlzjL12UKA9skFSB0kSLZIYB9izZHwwbr\nqDCa\r\n=sxOU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "5c9e8e5a1a18e58c0bf8147baaf568259c3c2a13", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.7.0_1588889356854_0.17293571135995278", "host": "s3://npm-registry-packages"}}, "5.7.0-beta.1": {"name": "@testing-library/jest-dom", "version": "5.7.0-beta.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.7.0-beta.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "52f927d9aa81d241c3e98fe0e849bbc0a0ca2228", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.7.0-beta.1.tgz", "fileCount": 56, "integrity": "sha512-yI6H9wzL+OeKkzeWqp+8vm9uTKYup1dQKrHMgGXrAUK5kzRvqIUib3qnZ495gniI2Xs5b7nbVfRrK5Ycxb9TYQ==", "signatures": [{"sig": "MEQCICjLc9a4bRA7567tLZDItbKuGqj9hvTvqawanhnJgAvQAiBIaj22+2BJQEEzsQSSMW/q1cJ0sz808SDLnM/MyRt1UQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetWILCRA9TVsSAnZWagAAwVsQAJG/cS1429lYonzCCCz7\nugvPjeoUz3x4gspyZwiDe92FAws3sJU5wGduJQ93zA8EFJ+4UHIMPRmob6ig\nwDhJfNFreAKpwORz3vYR9bbBLmwhl5v85V5Cx+sDhtAf3GAxBkS9oPywizBD\nr/kDw3oF6v906TyJAk7wtQN7zx3iT8JgbauZ40CRPHMWa6HtKIjzic2O/Oqo\n8ztH64g+U8F1UghP0Nh8Uaug+332x+v0cDQqptL+AXxxmNt7uj/MHKQf+odY\nnch5iZhnyEoPUoXvXxFv1R+Y5lexbcaVgU5/+DbEU8HzUsgfA7KSR6uQ4O8l\n6/LQ7MPuZyKILspjnk9g7HPvp69KlNJ6MxwjX7iJpLm+F2S800hs2inLmZOE\nfDgP914wJ92HX3PAjqVIhYieAO2gldearDv1rjfvmCr2xDquEuJJGgbNKM6/\nCQy1m4hDPbcSIlb+/AfzIHdSIIIeJSJNz/b06y4iusQy6U98w1ri2CS5QNfM\nj36o4skOEi9607XRItWxg//OOSY6K999gujpPLLuB0QIvRxM6wmpMQbwdJ0W\n2DjpyLv/dlUfPgCxQKIEW5KFfg5yVTIArQUFL4PdG9qHQwR6I0Ztb1teSHd6\nETMZag+w5aDCm0hAFiyjxMXvnzDrZaKtGuH1XxlUPepAnAEC0Svb52JT0PAV\nA2VP\r\n=6t3M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "9c55ac4bca115e2e07c4053204b573174e3dee38", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "npm run build:source && npm run build:types", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "build:types": "tsc -p tsconfig.build.json --emitDeclarationOnly", "test:update": "npm test -- --updateSnapshot --coverage", "build:source": "kcd-scripts build"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jsdom": "^16.2.1", "ts-jest": "^25.5.0", "@types/css": "^0.0.31", "typescript": "^3.8.3", "kcd-scripts": "^5.6.0", "@types/jsdom": "^16.2.1", "@types/lodash": "^4.14.150", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.7.0-beta.1_1588945418724_0.8381132854665962", "host": "s3://npm-registry-packages"}}, "5.8.0": {"name": "@testing-library/jest-dom", "version": "5.8.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.8.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "815e830129c4dda6c8e9a725046397acec523669", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.8.0.tgz", "fileCount": 30, "integrity": "sha512-9Y4FxYIxfwHpUyJVqI8EOfDP2LlEBqKwXE3F+V8ightji0M2rzQB+9kqZ5UJxNs+9oXJIgvYj7T3QaXLNHVDMw==", "signatures": [{"sig": "MEUCIQDWUWLuCpKnkjJoaKlBN3rZczVZA6i190ae3oZryLc1uQIgUzxakIZAzf2qja31+cXp1P3SJ+t3rBDSY3qyzMIiBKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexFaUCRA9TVsSAnZWagAAdSEQAKQtjWmJJhDAok6nB9LT\n/UMrUxMoOYD+4nAsHdPCVPGRVkMhZlHgLr/VNL3BGHuprlwR6sQHXUTiSoLn\nXfjsYBdnD0jupDrR+UDT/Kz4UBSRmWzcwnjSP6PVtf0clXmyYVhy3zo4/UqC\nr7kd/WIlZR+xztVZe9ZiPOJSKesuItbKROwEupzSaG7Kt6rwc//QBg8+sJi+\npjbnFGLORfrLTSdQKSAHJ3asZlPtCUYwRGoIqm8yeR1Isce0Z4MV6o5YPtuY\n5INlkU3VUOP7TRP5Acj+mrylhOxkQMf5guxEAFh0w1o/Nett5WiOmTCAvixO\nG0o1e7VvwG9APdCdi4QID2vvzjMQQmlyXyRkJUZostQkdaVUnpS3NGbxNBtD\nw2Y6aPlzIDtCnVtwYVouTfvok334+392Ys6td8xlBisIVJNYNpZunXwwKduk\n/Z5PHOVPQf5Mt5KKh4JGRIIa0iEFs/DiJJa0/zOS0OPVIwBDkA/zBDGCIJv6\nqUMe+CyIVOVM0pn6/hw6hRz+4wijpGlg15a6R+GQZI8/+xNmFpG2Zam+OdL2\njP6Lww0SoSmbvIWxX9C6jGpdY0hFQjT8uhjaKkLV9Ay3AYJ6wyu63T3LuzZK\nYCM81bauCC7B/wfBLS2oYsC7zHlADEelhyTbjolGHPc7VVgn+Svdivp45FtW\nw9Iu\r\n=0+Ce\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "e46299b8fe4d57b6fbfc5ae8e312eacc8419ee34", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.8.0_1589925523870_0.2489898674285147", "host": "s3://npm-registry-packages"}}, "5.9.0": {"name": "@testing-library/jest-dom", "version": "5.9.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.9.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "86464c66cbe75e632b8adb636f539bfd0efc2c9c", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.9.0.tgz", "fileCount": 31, "integrity": "sha512-uZ68dyILuM2VL13lGz4ehFEAgxzvLKRu8wQxyAZfejWnyMhmipJ60w4eG81NQikJHBfaYXx+Or8EaPQTDwGfPA==", "signatures": [{"sig": "MEUCIQDmacNmjpKAKxAwfX1hpKYnZM+gOi8O1CvQ1nfdCM3qHQIgXBmN/eTDVOaUBzhsiUcZE4n4Ug7avAm6HdgAQM/IipM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJez8GOCRA9TVsSAnZWagAAOOMP/iSEdwRg+jO1V8aEStRA\nuLW67m4tT7MYDQIRYf3qfj/tORL2K1K0MrVIqeOWx/BaTniBwSxx90iRQDoI\nSqEgbxOe3RxuFy6tq0oCuEIso0r2gSnScpL72KJHUqnC+zoPDNWT63pv3ZBz\nLTmKT1RWFIE3YDrxg9zgCjleZ3U5MBKhQsXmM7DfKqHvdmTfxVgIUea2uBsC\nErlqPJA2zFTAhpr2hIb2rk700ZsgGn17lUl1G6JF2OFpwuye0lyG1I8NDlRg\n8fbz7IsxZ0AFX25FyL3Wh5uC/RIMtXU/gYDMU4Ar/6ZDXjfZxCTQiN1/IcPU\nOqlKFOAhaOHGKV+3bmysgyZ5yURhDc6p/NdZpAJXLAHOAEZ47hMjI82hrprd\nKC/5hbERGA6MrsU5XgQfVZUTEZupI75UJ7Cl73uRNP/KsCIr36POFoU11se5\nihx/Wdw2u1F8qL1m5Kv87sMVASub4Qep0tY0B2IjmJvw8EZzOMnoDhABf1LF\nwf4fFwFGgg3VfzSunuGvDf7/A2dKgC7sGc8mS6rubl19/YVJU2VeQfpnR+4b\n2jmUfA2Bu5/hD5ajSRW9KeTJ5uQXshrDyslgooL7nOeeLCjgeUCqCjldDcNZ\n/W5Fo2iwTjBowDIpeCQXY/BhD9qRfOcpH0u4fxGmFp5NbXXCWKauCFaEwCDX\n2kI+\r\n=gGUj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "927c5a4f898369524ab6e96d67f2e1004f1714d5", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.17.0", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.0.2"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.9.0_1590673805948_0.44713672877391253", "host": "s3://npm-registry-packages"}}, "5.10.0": {"name": "@testing-library/jest-dom", "version": "5.10.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.10.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "6fc80de4760995b3b4281d2f3214e6bb95dd0754", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.10.0.tgz", "fileCount": 31, "integrity": "sha512-t6e6Y6cu9cnTRwgNzgGtMn+bluLa/JkQ8j876g0XoZ6QKK/VBPPAIJ2PVWK4pjTD3U/BROP0o29cMwpZHcqltA==", "signatures": [{"sig": "MEUCICPVwH2nKaWd5aLNHgCxkqf9m/BZgp6zoAeZUROrCNduAiEAhSZMTMRnZCMTHGH1VaByhVnHtN5BwmfUuu01sZIPTZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4kSgCRA9TVsSAnZWagAAgacP/3E/DOZB8CIDVIuptoN9\nkNWzU+BDotILI27UWR6GnKzaMIKgRiA67k9ZAH7LGliEo77t4+vfx7XUnGt2\nERGDg4AQ498w/Cx6GzbzUwO/FqS4Jdc0uBzSiRKg8kcuL/VI+EkeS2mZHJbx\njnSO1xXXJP2HCBJDGwxJk0/8LKFMH58DGYUtp3Qyf9SgvTO+bkUqKHvyEYb6\nvKfJ+aVPas+PxpEMcaEo9Ler9S6QzUOI0sJvJTSsZW4Kmekw0DlOaLWCp4+Z\n24m54W+xfwGUSNUVpphGHLKV05tPR1sPMy0bDlgc0BQ7WYaZ2BMmfIa8gECp\nvzp0gyw1wDdzOnonOcyTZA3RfKjRaoUBA8DqsMpIcmOTKM0W5yk1AEXlWj4X\ng/Q1fGfoJv15j5MHIhq2Ui40fTEgokAez/pPxMLksd5GGYwmvbiyRsGwtL3R\nnAZG9mIVxXxkJ0E9cYpnVswDSYnZT1EfXTHZ9JOVGSVYEcgj5XEqWcBNHjSm\nEfYDt6mP+n+9Av9kK1p6Ismq0+5iaTTg57YCeOnIYTUbT6jj+GC2JGJafkIJ\nbsC+V0v6BBTH/NwwME3FTG3FqHuHGc63+zU0E0WjfIFafXUflxDTuwP46YgK\nBXr8x5tLqrbAExz4MCd6Xwnd502MwwMWy80oXz+iGwH6Lrg1c+Xmf/jxWNH/\nlC8b\r\n=BtAt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "14d163045d486322113c305d7ef34abe790d6a70", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.10.0_1591887007570_0.8762809703958647", "host": "s3://npm-registry-packages"}}, "5.10.1": {"name": "@testing-library/jest-dom", "version": "5.10.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.10.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "6508a9f007bd74e5d3c0b3135b668027ab663989", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.10.1.tgz", "fileCount": 31, "integrity": "sha512-uv9lLAnEFRzwUTN/y9lVVXVXlEzazDkelJtM5u92PsGkEasmdI+sfzhZHxSDzlhZVTrlLfuMh2safMr8YmzXLg==", "signatures": [{"sig": "MEQCIBz3C7hGp0KO/m13Ch/ZlGT8xRcelr1/+JLlOq14QBa6AiA8CzKemIEFjKCLV9nKC4StGTsZKB1oz9xloiMRmU1Oww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5a4SCRA9TVsSAnZWagAAO6kP/2LX6RIdLgZC0RuCuM0O\ns02wFiIlh7PFFFM7eJd9mNfwN6I6Lmpqzy9V9+ZarWHdmt1Sq2sT1OfzJGJs\n8F2H+FLK+nEICd+j1POpmbvHKgWpZ/D0w3FB+44eAeD00+Qe5/RRrprU/TZ/\n0Ys3WgkEmpm+7M5AtovmFV6wa3k2C97MmWWIozd4gUA1e9bh3djLzppa5tdm\nTfs12+Ur5ulGjnEZ02egZX/v2wHeqHF4zFJnt3BMSEWc2Z20l38paB6Y5swz\nnFqlmIp9jJpJbF0NR61x2iSr0/SRxxpSoI+iVBpEz78KlB8dRRUi6kzzHjhy\ng6iNwOweNWkFY5DjnYC4cPhqYBzrKtp4hdm9q75yIO78/CnScGSpPmMaJmsF\n+BTaydm1/ZhK3WmUXO0K8I2h+fgXKQdFHuIXwyb6PcjLc55+RnswZBrJlisA\nWK1d5Gv2gYqYH/5BAltOfndO7JqpqPj8ksIMV7n4UcxpnSlBYe+TlTDnpxg1\n8l9k5O+cQEAnq9p39AZ100FodI7+DHvxrH3JQWpVi+jLreibNl3H/I2bI9W+\nuRGjtBhYqT2YxEHbHuKYzyPCnAUatbkbYVm6ebQPD3ml9he6qUNuXc5Com/b\n1g0P+JdgOC/lgGWzG87G9uHhBM7oNrB+EIyxvNDyzwYiXp11GiK3q5lSMLe/\nEcWI\r\n=WFwf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "5e392226d34b2318f21a4f5ffcfd8c9a16d6377b", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.10.1_1592110609928_0.6409632244292247", "host": "s3://npm-registry-packages"}}, "5.11.0": {"name": "@testing-library/jest-dom", "version": "5.11.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.0", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "1439f08dc85ce7c6d3bbad0ee5d53b2206f55768", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.0.tgz", "fileCount": 31, "integrity": "sha512-mhaCySy7dZlyfcxcYy+0jLllODHEiHkVdmwQ00wD0HrWiSx0fSVHz/0WmdlRkvhfSOuqsRsBUreXOtBvruWGQA==", "signatures": [{"sig": "MEUCIHJyPZBQGtNIHEoJ9cSAcos93lIntDd5j+YYFI5k+mZVAiEAsd+GhU2ttGv86GqtoXBiM9v6gJf5sNHkxzJaw2dr+Ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9MkmCRA9TVsSAnZWagAArYsQAJNpb0ODWcqZgXy7esK7\nlbSiJKht8HMn7b/YVIKPUN7eYchfQuVrLAbGrb/3cwWlU1LA/lQ0ZlGx62Mn\nAhhhvG7oWj1ahNXTDPxI/M8GLInIa2+9vIusBnPO9ZoP4GdcQlkkz6uyCWtA\nuWjb0BiU9eeHN8UPCZUhLgV9HyC4MYOoQDKke3vwog8cOByptqckQhUhe/cu\npkZ/OfjQvA/g7Hd3S0h7MS/em7/Yo07sO5OkYJwfYo9b3L0xZZp/8TJdG9Ba\nXO4EXRmSHsCNovLKClUTNgCBIkZVBt8v4dFszSL0Cat96srcY5j9GfDgL00I\no23ZcWkqXsa+6MKBMyAY5CC6kdtmUsDlqjUQONFdyr44HVDmH5dIoKsLZUjg\neEYZ7EbpVJOYYJdqimk3IpKuL0NBdW1P8figT2GWGrIlHjcCKjBLKkvqTVuI\nxLEWmiRaIQjmJJGsCyLIMZw2GjPD4RtzgU2HAr9mGB1LbQFiP4G3wNA+HXJF\nVhEAgIi7EBny3J9+LulOHlljDP2SjH1YbokPh71CRRubD3VazNN9HPWkWHDV\nr2DnlYmoq86bXCoz2lWGx8lHXJRhe8qGg8HBiuCfgqD6CZvBn9WOJ5T4bLF6\nPw0za1gJS7FttJqTCXOGZQDxZCP5ntUakTtCbKYjkJYJtiYkU31rZxATZtH+\nUQN7\r\n=GyDb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "c135d0be8370af61c8f34b3125765f5bba813133", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"css": "^2.2.4", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.0_1593100581356_0.9028957804690168", "host": "s3://npm-registry-packages"}}, "5.11.1": {"name": "@testing-library/jest-dom", "version": "5.11.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.1", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "b9541d7625cec9e5feb647f49a96c43f7c055cdd", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.1.tgz", "fileCount": 31, "integrity": "sha512-NHOHjDwyBoqM7mXjNLieSp/6vJ17DILzhNTw7+RarluaBkyWRzWgFj+d6xnd1adMBlwfQSeR2FWGTxHXCxeMSA==", "signatures": [{"sig": "MEYCIQDdLsGdrZrvrZ9eYZfDBqOKJnXunoICgAdLzEK2oITzhwIhAKRIQxnjAsY6XkrQ3y+o358gsJlH7sxqksxLPHceCaDE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDwtaCRA9TVsSAnZWagAAirUP/2KwsedKu/qGTpr4a88j\nlLjzaPQLIVZ1mGp/XjSO+9S1TYhckODuE6tQLALbr9hyA7ZRpe5Eks2g7vTl\nBpyiEY/HNGfXwNGJmYt2zO943FGAKt22IuKGPE0p9Qos1T1kLSDKxu1m532Z\nCR40bqXkdqrO9/IiUR7ibBtmYCmrnQ7M1cyx1S4bO+zgyu+ikjOxMF7fM4DP\nb6XOe/DapmJxF75TpN+RS5I/UYRBo33fEcwBlY0keRJnm1OLER5qTmBhQU3h\nq9JefxSY9ZR9GCJXmYX5HIkZJgJRnQjkKk1YrEfT1Pl13D/KErk2y/J5alot\nKthn7nzIof2c03tbapzJPLyYqzHkunWHbCD+JOkzcShTjuf6Q2psL/kkUGmh\nyLnZW2hk9v78+bdeK87alqSMT7Hxs8DRZQ1f435BN/L/S8O1dw0buYWxwHtE\nkwpJKZRB7xuGaZ0/0TkYYra7EwrU26uejgWxgVc/vhz1w5f2An0rvaXHvyUb\nRGl8EXbhepj/779i/DqxYUuSkZ/WU88tohyyKebhiu+2umzMNABLC7ilxbcT\n5kU5NtroPIzcRsufagitNzh5mMnbdXxVDlmiXDZT0u1X9ahQHhsrIDcg6NiY\n2bDRvZfTtW7dohGvCnQ3N+K6cELjU6ArAtbook1DRFR/V5WmaDyXAAk5sd/M\nlJEm\r\n=qTC7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "5bea35075d54a7ccf4c93b1bd06a7182307dd809", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.1_1594821465804_0.9334428342201739", "host": "s3://npm-registry-packages"}}, "5.11.2": {"name": "@testing-library/jest-dom", "version": "5.11.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.2", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "c49de331555c70127b5d7fc97344ad5265f4c54c", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.2.tgz", "fileCount": 31, "integrity": "sha512-s+rWJx+lanEGKqvOl4qJR0rGjCrxsEjj9qjxFlg4NV4/FRD7fnUUAWPHqwpyafNHfLYArs58FADgdn4UKmjFmw==", "signatures": [{"sig": "MEQCIBABApPVHD4gRZ+FW4W+zBo0Z5eGw3iLfAWuo1H8imy1AiBtNL/f6myv9omZvPiQ6Vynepq8Ks54FUSZPLZ8xrb+ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIGubCRA9TVsSAnZWagAASEsP/RoGYHyk9VC95TqUXLEd\ngHhXvUXSvup0PQUaQ1tvAtOmTI0NVflP6G+DyiW9rAzi/drlbE1VUZ8XQHYx\nejqRlQLy2ozcmuNfZjWdDNCzSP16PXFxlFVkrCp06BtLfcY+ITCp8DsklIZR\n7XCF9fTiBRg436MJ5XQDGQJDJSxgKaOc38YPyK3kEGrB6Fshun2R4UbN8VEy\nljWLx3Afep1ep+VgojBy1a2X3HAd4vcwrjR0SckA3W3wOY51S9MJ9HE9yZOp\nptaYIPhdvTvVgTSdFE+SM9mANt2RnxlZMb7bBT1Qxx97b7QhWq0hCTouG0z5\n6zSd0YAb2TeiODLtBykuuPdluvhZl80+T8MaRpLAc4fFdwjut0JArct8QexJ\ndTqAvOcK+heagtAKgMUDk2DNZTH2O3UjWTAge4htH9A/Yw1PeAANZ8gdRzEB\nDn/Bcc+5EP22VT/TTJA44vI2+dVeLuDSIfrAQpvH8KVPwHUwY3FOStHZqyZA\n/anvOdaNJqs3bHJrC+xw4Cpd/1BnwJKe3PuE/D+/3cGqzwwLHKVN5VtrbxG8\nmxVmbWtUVi8hcD/jZC5ho106uDFaSKOW3KpCFfmdW+znxOz3QixgbQjX7Qos\nnFi0Kl0xuWYLzvVpS0RSM+w2W5tWRcziSPSJ9RWe0uyGM9KB5F/ckT9ZGY29\nbGQM\r\n=I63B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "2cd17d34acd67529e5f87b66ca380e9302cdcb23", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.3", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.2_1595960219320_0.5806146035494306", "host": "s3://npm-registry-packages"}}, "5.11.3": {"name": "@testing-library/jest-dom", "version": "5.11.3", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.3", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "3802cb244e9ab50559a20344698a2d41f9bf11ec", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.3.tgz", "fileCount": 31, "integrity": "sha512-vP8ABJt4+YIzu9UItbpJ6nM5zN3g9/tpLcp2DJiXyfX9gnwgcmLsa42+YiohNGEtSUTsseb6xB9HAwlgk8WdaQ==", "signatures": [{"sig": "MEUCIH3PIXe18ss47AS0AqolQzgybIgoTSlzKsTRoJcI+ZLQAiEAwu8VV4DfXAgqcM+/lKIR3aktEE+qmekgOBl80lkhcaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMul2CRA9TVsSAnZWagAAhAcP/3lvOtfYETxetsi5HlSz\nxoRM5KMsGUTr5eBlqmylfS0S3gwVSY9qjdFoTX/qNbwUP1x22IYXScOOnh1m\nLK6TIRkkn4dVnMmnIUjQrsW5OKuS3TpQLMLcDpfX36d2Fw/ngIrMh2bsyy3o\nBVaYyePaU3SoC3ob7DxlShyjdM7xIa1GCx9AXAsNN0EiTh2bwRNV2UP5r9Sd\ndI/2PTOKcz8o2sYX8DFwu/0kAauhChMFuk16OBZ7XXq8SATNKLHdP47F1+E0\npFRrlPY6hfkk8QjSrwA0Qm2yu4HpxcoHWU8tMoJ5TQlu3TAOsy3h2c2Gze/h\nYgkroZyT/uiwJ89b6hl/DymjaaMINTNKudFLQYxc88L7ZtQkK7JOzm1njc2I\nlgWfAEwAJr64DRQ1bWHrbobUOVUI5GoylZXQfFtzMkXUF8EONm4ZR5IC5OcJ\n6Vzu8ZPAW2j/Yak7bzMR1+NvW+ngN/mHL1H7UgBbMq47Mq1OYcedSIhXgf3B\nVRunEs117oKw67Ccz0reDyuz7ytl3h0qwE2Ow9GlTTNGAqZ8d5HXIgs4lNye\n0ENv7ePs8wmu+LsMOA7eB2LQ/6+vHchRJCn0aNGfc+kNgybgFsMuybHJ8fxX\noP2zy+sl0bjjVo1owO4tIsgiBXzvtUlKWjiOV8zmlwTRCl4vKQipSOPZ67I/\nRbLS\r\n=fyxb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "92176e1ae018a9c4077e25cfefd3907ab8b61c85", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.3", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "jest-diff": "^25.1.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "jest-matcher-utils": "^25.1.0", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.3_1597172085300_0.789467310013523", "host": "s3://npm-registry-packages"}}, "5.11.4": {"name": "@testing-library/jest-dom", "version": "5.11.4", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.4", "maintainers": [{"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "f325c600db352afb92995c2576022b35621ddc99", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.4.tgz", "fileCount": 31, "integrity": "sha512-6RRn3epuweBODDIv3dAlWjOEHQLpGJHB2i912VS3JQtsD22+ENInhdDNl4ZZQiViLlIfFinkSET/J736ytV9sw==", "signatures": [{"sig": "MEUCICSN1ovieTUOA4PFT+fhw1KYkG4cbwm0gmWQz1YIZCGUAiEAinlKaYo511tYO7CCwNMCaPTX4D/4wpo+uhw43draH/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQPaKCRA9TVsSAnZWagAAzTIP/2RjcBCNU19QjmO+msX1\nTblggDKgTiOofYPEctu2KMCysLgm/DxoFeD5TJGOmcKQ4A+7ZMgScBaY9GCF\nkhw6RZAs1uV6Z1A1HV3Vw8A1jui8175dUjjiugnPHrKoadQmpUA42rcf4Rkv\n34bP8rYssB4Q9xRcAoMvev6+BYKazEEASAGoNLhjYbX2QHFikARLBMKX7d5a\n9Cg66DfTW1gkoifAuT2QPLxP02ejEGY6mCCEBNg/7yKnRHEJp/EWF1PWkwR3\nmB9hW1UOj3qLwK4YpzQUAMLyTsckS3qPJrK1yxzi7NBJOBNYMb+qGhb0K/ZI\nOMHQU27aB10QiG4hX3BgWmhGbFlnlxMbFOZQXk/Y2gyV7kPqfwm5U5Z8Cl/j\n8m8fVLYN71prvkbj9JaJubx5WlFLAyjhGqMyN4TEDmIdFNQhcs3bvwpwn3r7\narlIbYQnJ+upuBVOeZln9tR2R5aGidKHRqZShjxf36opuZFawr9yga7RTbLk\n1T/U/o1z7nqcrrsIIeutOCBQKsZMek6ywvMBiDA1wg4pKe3Y8ZWlo14EIfuY\nKt1xbLEE0Ida0Y1x4sSaKxPSK5DSR/G5aFYX9o5J1YiTpE3vmluvkLAeOAhV\nyo5U/PJRY1NVtMWPzkyXnwYoDRFdR82jgUZreru7RQeam62bf3i1d2w0xcjL\njBNe\r\n=KY+a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "2da8c71eef4bdfaf7787710a29cbc7956d8529d9", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.18.3", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.4_1598092937457_0.5529045226091285", "host": "s3://npm-registry-packages"}}, "5.11.5": {"name": "@testing-library/jest-dom", "version": "5.11.5", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "44010f37f4b1e15f9d433963b515db0b05182fc8", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.5.tgz", "fileCount": 31, "integrity": "sha512-XI+ClHR864i6p2kRCEyhvpVejuer+ObVUF4cjCvRSF88eOMIfqw7RoS9+qoRhyigGswMfT64L6Nt0Ufotxbwtg==", "signatures": [{"sig": "MEUCIQC6o0uDt5uWRC6PMoPKuDxk1Roa26xR4TuhVz/nbM3y7QIgM4Xajy5DuqA7n08XHmmBD5N3HblWDWwK+Q86guX7La4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfktZLCRA9TVsSAnZWagAAVTsP/RvHE8rn8KzQV4d5gAL3\nDefMnwaf97vRFici6+JbpObL45eXvWjnViAb1ab3fh4is/7VflsQ1jmoMqvO\nU+zlZDj8vjlJAjs+DsMw8veoisluaNcs14h77rxofGQLsmw0O/uK3vdZNdIx\nyY0QGqNvOX/88vLdLRVrXwlet4zjWhGkh1ETzoEjxR1k+Zeji6PgjLqrcR6S\n6Mzu9n8Td+aUgEitLlL5dGI8C269an0KybypfTdRmKB8cm2gyF6EfLFU4ZGk\nH5cy44MiOcg0h7KWPsdg6L7GDJtJzEQKfqZp+Z2Rc73M4nMZ+byK+A3BTRaU\nqOob1bsi2vupY6L9lLFSCskGgSKctjt2P8l85QJPOpOcnhg89V+qZkcf4ALV\nCkpoOdcuNDT5lGlvD7qeJx2KJSdKbR7gY4vJzgaeLZP4fmUZIICK3D0LiKlo\nZLwzoFuR5jl/+ckl5VcZhwYEBrNuxGogsDTjeA5ng2pL7WBiZdyTCKddyV0O\n5WWPI3LiIz1jCsQSN/qApoV21p8A2fODqth93hKctkIXVCV4VqDJGBTL7ROR\np/NjO2s/JjAY+HJ55iEqHhw6KVQ8K+/mX8yMPF+t8M9Zm12IlqRBQyMHtnke\nDw0/98cEf83frpDHwtDt3Iqo5qlCmWx3v7S3de8cYp9iWjeG/+FnlAcU/QZq\nXw/O\r\n=6Dc1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "c11f9c53bbf94c1aa48b401f4f523500e904e128", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.5_1603458635036_0.5969778986519294", "host": "s3://npm-registry-packages"}}, "5.11.6": {"name": "@testing-library/jest-dom", "version": "5.11.6", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "testing-library-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "782940e82e5cd17bc0a36f15156ba16f3570ac81", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.6.tgz", "fileCount": 31, "integrity": "sha512-cVZyUNRWwUKI0++yepYpYX7uhrP398I+tGz4zOlLVlUYnZS+Svuxv4fwLeCIy7TnBYKXUaOlQr3vopxL8ZfEnA==", "signatures": [{"sig": "MEYCIQC796wkFrirpU21YS92VhEAFyZF2CffplmO9mrUuNjDmgIhAJoi7mfzyFuOYZIaGP5sECaHPyrny+/bsAM3GDf8vJSa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrp1fCRA9TVsSAnZWagAAQZYP/0hI1+3iH4oyXDIkTQuH\nOYLF/QeUNjHIv4zYcSbU1DTSy9EsVZbmcJGdwVphxhQIO7RuTmK8x3L5F4gk\n4polNh1TYhunwo+1Vc6GJPzzfoNl3+OE7iCkD7ALEbw7B2X+/K9x14oOCM4H\nyA0PS9cGmqb2u+lodISeNJHCeedalnQ7h2KJBuSYEZpnfRdlPnJ2ddMmzkKO\nnVeuaTKc6zHnvnYNHIH1WU89aF6wAlzjp9UIw7lbQ+0BCV3ZLsLn1MSHGFpp\n92CH+oaSjVPSw4c27Bx5rqWLYtcW0NoiOBu/uO0R0/IVUps9Xu9E1iE3WBN9\nHeoGn3gZ4ZDfRc0utw7HpNVIolFElx8xmtCS3H4kUq7p12uNGeCBaJ2yvL0w\n/OSbb9ygBkH1aepquqzbknKm1nJDBTlTzuWDaqUMxW+/wNY90HEBSIhPlSgE\nM+0pFTSJZCPDiXQbSdgkdXeY+x6MGzAC1kb8tlumvREKp0fwjHF9IdC78S7I\nzvyBZ41DgTMN3XmO7/8/bziqB3h9sobLMfC0TFKLNJ/VvdwJAx7i0BMcacGJ\nFIdyS/HaOy/v2ZBtjaqDXzoK3FP+UOX8g85zI9jhYHMIWWrTBvwX6q4l6ePM\nlYawu4nUnYxgTK/57lSZOFaD0LxW/BnnPQgFbOhWhCENLrDuKRfjBDYELf1U\nrvNM\r\n=0zoe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "0309dcf8c80d31f0feed7c549da74e5eff24a450", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.6_1605279070468_0.950698478269151", "host": "s3://npm-registry-packages"}}, "5.11.7": {"name": "@testing-library/jest-dom", "version": "5.11.7", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.7", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "f788867007eb8b71940ee3100eb69ec45f85b1c6", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.7.tgz", "fileCount": 31, "integrity": "sha512-EMU0upXVGTaloWIQNdf3+Q4YMuGFaZQUYEQwdJJ5Pq33uL65rIeMLbv23jC6aCVnxiqNYCOhurIt85ntvoqaag==", "signatures": [{"sig": "MEUCIQCZFvkxEPWCaBOHxMdSVXy0DGkfbR23BCNxM9IR9l9EdwIgLd9a70/4gqPDhXwa8piuT8REX8H/nL5M9ctC2O9tPxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf69syCRA9TVsSAnZWagAAbsIP/RVoxQv2PAjGlGZnil3O\nkCZ7a87e+lSWZED9c0jvdguPI2A25p85LxaekQxqh4+hrtwKFzjbFuESmNni\nHo7FPPRlHamrLhxVAs+C1vr0T/iruFeVQavN5vTzFgmrrTXU/CFlzcl6Rgnd\n9/dvbtJiDBPxA5XS91IEwxrpX9OIOaD+Hu6r1HLy06f5oIkD60lSTSfLlYeE\nwrD0snnrCSCuguZrpLMKlstQYa9qyGy0RnYzQplZtZMNQyib3GSPAgJUolK+\nbYg4DSHbY8fBFr3UkCvbBcR8inl3oDFGNSF9u8seXvOU82cobJA3KxE5eVWV\nDPh3pidas2BPJ8FBb+6W5PhjsWYHo+yzOpES/2XrlSfjJTc9d3V4DkkYj5YF\nL2F3MJ0nidrNJGvIPT1YLtwuO61K5qTpHsh36Q3lm5O0Stql/9064aDFJmFB\nSKx+4rAPWNqhUc+ElsP24WqAWjWfj/1I30pQFqQzb0mxPYq//8LWh1/jopn1\nHZMfCdz0O65qG8UB/h9JLq3Q2VFQguND1z8Oi9B4+cPlxuhjoGTlxzj/mCQQ\n6lQNayK4vcWITPsPfes9NgsCOyL2kceDe4dQIFcVJuaZl20VNe2cSHNQqkL8\n6u+DQ23hfZ7b9msnaUU4filGoLw67GreQwa7MQvLKVLMJm1rjlGNufNWOVpv\n2V3a\r\n=xkg3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "4179117d3df1f2cd6c7a203759b3668dcd092ac7", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.7_1609292593803_0.46534501445756504", "host": "s3://npm-registry-packages"}}, "5.11.8": {"name": "@testing-library/jest-dom", "version": "5.11.8", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.8", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "433a84d6f9a089485101b9e112ef03e5c30bcbfc", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.8.tgz", "fileCount": 31, "integrity": "sha512-ScyKrWQM5xNcr79PkSewnA79CLaoxVskE+f7knTOhDD9ftZSA1Jw8mj+pneqhEu3x37ncNfW84NUr7lqK+mXjA==", "signatures": [{"sig": "MEUCIQDnpc3twHUPucf1PsogHZOd9y+3TDqlUA8FWjjl5y2WMAIgYYJLpNsKCtIdgHmK+ojriHELVOroAGNk9bo1ETR1SDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6/SjCRA9TVsSAnZWagAAOk4P/AvJZWLLdWW1+N3KW9If\nYJXq9uxDRlWIhxudSyXWsDhbf/tDDHp/71zCYKGRgWVcC4v4ljWyoE/iJnti\nHuMkROnxb6ZjJ9Fy54fnZPpdumrxSyS3P/AZ/T27iilSBCWbGRFVrTua2bY2\nc/FrDYKiNHmRbR3MIp+IEhRWZ2h+QInbIczf7rshlnyII06zzUwXOUTtryO4\n+2ON8UhD/4U0tREWZvsUl46ItqkQTTaK7xI6lZCk07fOBOyH8NRisvBNCM+g\n189rt2OaQGiy/8XmYIvKXfpzmGbw3YjS9zK9Rs5W6wrhsf5Sirw/q/CXUmva\nBduLNDkt/xEkSQdfF1BANMDCc4qnumRatrJ7917YWCxiKMHxDEvv0/Evtk2u\nsmW6y8IHCcKSc0025CJgRYn1dzwQFx/RVQhwhugr1w90WXrvOu2LTE8cebWJ\nZ9Yi+T9JrlN2Pv1mNlNjMkPyRgYoKECkwsFyfIPOVLfHRtsGC5eZQSz1zI41\na8R8T42BKHeyZlRdPFSFu4fV0xCBvttXk+qzE5a8epRuNhk5eOFrWok5Bn1Y\n6M/n0ed5eiHUrlebKFKE55mQuEK3Al0DJiilHnBrx2FQOGuHqDgGym3ZZPcl\nsVmPyN+OvCoBAmgnD3zAGY/F6gNGBC2uTPtgemKNvpcYBlK+FrsuZt7mYsps\nqVNy\r\n=2sAY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "0bd1ed9c14ca23067d81f4c04967839776e94bbf", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.8_1609299107203_0.7647788118259129", "host": "s3://npm-registry-packages"}}, "5.11.9": {"name": "@testing-library/jest-dom", "version": "5.11.9", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.9", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "e6b3cd687021f89f261bd53cbe367041fbd3e975", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.9.tgz", "fileCount": 31, "integrity": "sha512-Mn2gnA9d1wStlAIT2NU8J15LNob0YFBVjs2aEQ3j8rsfRQo+lAs7/ui1i2TGaJjapLmuNPLTsrm+nPjmZDwpcQ==", "signatures": [{"sig": "MEUCIQDuLLpGz2a2C2WsJzrH2srqtAQDhxJm3mfBbKNZFv04EQIgE+uTM2rrEoYvvdSWgP+sJBFfRw3X1QqXBHrHutr8ZbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/fOjCRA9TVsSAnZWagAA1FAP/2Uz1N+Obrrc/fpbvIbu\n2fDR4WKCW57mOYSahgCIrJlCFHKyxTnITnd4DJKyIU8kVhWDcv/CVllWSR8x\n0JzU77auagIZk24nOMWVThGTxlDQeSYRE8417k3Dx4Gd/HVAxQKCVcrrdThc\niWMMJvSBz4x4b32dFbUrD7a4AxNcrVjzahZzlbHCBY80RXss0qRxtvzsyTup\nufxFHDmMuwksJQTUdVTWM+yXvtyCrHKIiL9lCvbpH69P1bUt7kKQ+uVrjoqg\nYtxnifflaifvVjhHTrkSWxDYYuvl6CnexImKZ7nVjWn14uJVRWFxkr/Y3DOM\nlKQPrSz0J9POaFNnYbdITGNSpvvgqaa0YYO0gcIyqMiUAVjYaiff/QDHgKdo\ndgdvZv+bG+FmbUq32/Iz47XO+KNFuQafR956VLbd11bME7FlFaBDZmMiPbYf\nC/PjL3pp3AtFFYTRsRlE0TuNqK0ML+JNrUE5+Rw1j+0swgxoPFPAwxtokx+9\nr+niIxaZLvOccRElIL06Kjk4wZ0k1MKHr0qiexEG6E+xL0QSvdHXUE1gfhhd\noSxglpQINnv8wleZtTfS207xlwfw+MVFhdxldSbBkt9CSYTzZq1lsZvRcaCj\n6zU+3g9gMVo5w0UK0JvYbkiOKPrhRcV1yERJTLSsj4rQMNo0BZfiZTbPFPMD\nxH5G\r\n=z/Xp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "6a6531d17b69583590dea67dc2b6ca5fb18351b3", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.9_1610478498856_0.8478813406465964", "host": "s3://npm-registry-packages"}}, "5.11.10": {"name": "@testing-library/jest-dom", "version": "5.11.10", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.11.10", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "1cd90715023e1627f5ed26ab3b38e6f22d77046c", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.11.10.tgz", "fileCount": 31, "integrity": "sha512-FuKiq5xuk44Fqm0000Z9w0hjOdwZRNzgx7xGGxQYepWFZy+OYUMOT/wPI4nLYXCaVltNVpU1W/qmD88wLWDsqQ==", "signatures": [{"sig": "MEUCIGcBT1bzl51eefgn+bfM8nzEjrZUaK645JSiD2UNepN6AiEAhZpnLb0FmDBSN39lD6TZLQ5Tn5H86CZImCvCHFJxl58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXLLcCRA9TVsSAnZWagAAN+UP/jasC0k4zHV59Pn/RY9J\nDqdMigA/byUX+SxroyM24ZgDOrLM2ZloCu3VCioBQsA0C4xo3vO40E0dvjs0\nbg18zU5psnXyTz1au/JfmtmwVDy+eefStGbaeCZ4Zbwe+HMaEAWc59RD6YPc\nmnCgS9S9O7gUJ6vkc2cQXBMRy92zadBe9NCtaObLA3Y8uEAFpT6JyT9PtlIN\ngzV4YXvqGXvvt+9I9N53+qj6eMouTLbz/tXrWdlim9eQ2XFGvsNRaYdydG4/\nebWlI4j6+iTAlO4DXgAcajkVe13MMjCRanS8k0cIOfsgSMUAJL8/gTu6wH1a\n/UimUyWgaPGjfxZyEuosrvOtLksdt6Fn/qB/icneuJ0zlp/iUdPgPaueZ7xf\nklENzxMqPPWtnLzGopM6mRgMR1suFgy5nyDsh0DVeACBPr1PaJe3YNlrv53b\nqCjBb3dCbwDus4/7M/nE+67GZOpU8Z5w4DZYAiU83yKSOTosCsjZD4AZzs58\nuSgNTia22rZL2bHGfQChvrCBpoUD2jRNH52EYOFtS4lH2WkoSavC3Ie6ecT9\nKHvN4UD4ytKgiTtu6PXQLkuOkyTQP22hgnjv4sKjvlFPF4zhNJX7kEXfrlqZ\nD0AJTtj9oIXym6Zbza3hCsGXV73E1t9e5driXahxaFq5g2uy/Zf8eZ3LoonH\nC0m+\r\n=ZY0+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "21ad89bc707b6e41af887d1bd103dd6dbc665dee", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.11.10_1616687835518_0.9763420555162794", "host": "s3://npm-registry-packages"}}, "5.12.0": {"name": "@testing-library/jest-dom", "version": "5.12.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.12.0", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "6a5d340b092c44b7bce17a4791b47d9bc2c61443", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.12.0.tgz", "fileCount": 31, "integrity": "sha512-N9Y82b2Z3j6wzIoAqajlKVF1Zt7sOH0pPee0sUHXHc5cv2Fdn23r+vpWm0MBBoGJtPOly5+Bdx1lnc3CD+A+ow==", "signatures": [{"sig": "MEYCIQD1jAnJjqX5No8UXGG0Ytmk6Js2UInUz+qecLhnQUIk+gIhAIsYDIMKw4fq2ECVTKvb7ZOmw7hsxgTIEyUSdAd5tQW5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggWAfCRA9TVsSAnZWagAADOwP/R6PyFOBJcyQr6EwV9HR\nDHr8YCSGklnKFgijwvxhrm82S5+eLQXuR6wdeotMVjPOvHSAXLgk+n35BfQO\ns3fScalecB+rGd6duOTZq+Rl/CBQrxyl1B4YgKTPBKRYrPe1hHQPEbfPUAUN\nBDk1vqDLUX5iTpnezh9AqVpzrC7v2y5gg8vSypGnZZTR18qDmepUqWq/JygF\nJCMzps44nJCwn7B94msdsomCwDU9sxzRQjpdJP/bT+xybUhRY3DwYzkhB19f\neMA7JBns18MvaLAa5Nxu6nzazzGpt+QVhMX7GkgeOjBdAwt+iqT7qumdN5k3\nu+RBtwoGC/jCr3DNhMAco8KYlVppRSGyHOTQtFBa5s5ssX4pyHhzN1tCby4o\nwgF94EN3sh3r5mRIbSdf1CsR7Kbs84C7v2v5JXl8bugIqcWxxpfYV+o+bSaS\nWF3+S1h56wPG070cs7mYo9u1mEa1pKLCwCPycr5vv116p33Lyt4RUIZy2knE\nF2YqUTY1kUyTxtvCkWxwdTmiNo4z/UPwjLU+fzmN1mf7yPUVfUURcJw2Yv+s\n3j1NheME70iBTKlYSNnllzE8pWWst86LuCLhX0k6jTzl/jU2lk29b/yDmfZw\nm1ExBVi/qJP9H9aMACHCiHayBYUzRddbfazc7fbxNMDpQpspdgwpwN0lGHTp\nYAkY\r\n=Uicd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "fa0d91d17ed0735db33f70397cd4b4a43f26f6bc", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.12.0_1619091487255_0.04177336078738669", "host": "s3://npm-registry-packages"}}, "5.13.0": {"name": "@testing-library/jest-dom", "version": "5.13.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.13.0", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "0a365684e2c1159f857f5915be50089fc5657df0", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.13.0.tgz", "fileCount": 32, "integrity": "sha512-+jXXTn8GjRnZkJfzG/tqK/2Q7dGlBInR412WE7Aml7CT3wdSpx5dMQC0HOwVQoZ3cNTmQUy8fCVGUV/Zhoyvcw==", "signatures": [{"sig": "MEYCIQCyfVimqzY/9P1wZYxsEumOmwfAFlyVL9ERNlFHOSRlqwIhAPRmclK9FDuvKOtI6CKcabSxSSSTUuvLkMh0CoKUxKcB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguQviCRA9TVsSAnZWagAAM/QP+QFVp7AwI6vWjuH9I7qR\n/e8XGPpoPkhmgUJ/1V9RbWCKY+BE+Oxr9Eu4wPB90VtsQsNtAuATtLzASgcs\nriHpYRJulN02Z9DeYuYNbrSoz/JSN+UTvrco11yKL4/aOx7LjYZ2csryFZBn\nXARP9CX2OI6mz9b50iywdfhsJEo7j8NoDDv58LiyTNQ5BWcA+81Q5jxnMFMg\npHV2L24ifgiEbFd60VM37g9uOGjkpyueOQ0oJSsnYBVftV6AoG2bk+u9guYE\nGiAQL4dQ6cowK8Yio+jlYgUTaVlSkyJYt8pqx0xU9WyT5S6FoKBowKxklsxg\nTjrL07ohkHW0Em/lVvGWhbW3y2K8zxNJJz8McdNT7nHrpfOMwwgDkGWQlzwr\nGlmcG5DTEC+2MqZCM+DK4Urr1RDFOF4XmHQv1w4+YP2d59WDHbwkK3V1VZe8\n5b7CcJaz/ImbORxCTMGVZNwq6rAvGwOpMeK/6x8VhY7wahrK5UEsGgAEXUgB\n050odzAdx7D00KfvcqGTCLXdC+GV7rU8+HSO5GofyV3R5RDjMhaHOgstQlPP\nYStqaS0n1wl0vp7XMGOnZLTyW87rT6ZoAkDHjsLMJ1p8Pok6dJHJfyFKeWxh\nA9aOhvv+yVjdRqd2arVX6cD08fy7gEcTu6tARnxR18wIPmd0+4Cz8q7koPlA\nvDOB\r\n=HkJj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "217fdcc2377bc24bfdbd2e121289704128048fa9", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.13.0_1622739938543_0.3178875532839547", "host": "s3://npm-registry-packages"}}, "5.14.0": {"name": "@testing-library/jest-dom", "version": "5.14.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.14.0", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "8ce902f2b66eafca8eacbd2f709b680c4d00a342", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.14.0.tgz", "fileCount": 34, "integrity": "sha512-dSa157Gdn7ogL9zy8kZzBMraS2QAp2J5LUXDXRssqqaBVM7z7Mu/bcfuOEnUFzi6JDYZ5dqGs3dkaRc+dTdKYQ==", "signatures": [{"sig": "MEUCIQD35mAg/xcMJlXN2jB5Iijuf+dYPlbpRDg7ZywlwlSYjgIgZZFcL7z3yD7r8K+yyd7ZPDQ6kVOoWPscLWim7kOmrTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgw3BPCRA9TVsSAnZWagAAypUP/jN6fWBT9troFD6ZFuRU\nx4pugOPwRExLwsLGdKcTaeYl16/jdukq8x+QmI5FTn0pWx/NTOifMhMprhgV\nAl4vrxtsnRoJpouOT3WaTmnw5uUr7MlWL9Ncy3ilnVPJ5Xt0+0ZerDDHu5m8\n4Zj/dYCDTe5qoUpFlQoKcXZyhRtVR9LHD2g75TEhRjg5UFFIuShAmW6VeqZR\n3SORtjkpUQfAGbQqzXbWWihGKcGfIx1eILNdFkoZbQpe53Y1YQTLQuwhwebO\np86Ekvuju7CDPZIG10L5BmvmsJ5VyLzA35bRvl3zAHV3mdIsTtnFYrE8oUF/\n/gCLF99vouvHB0GcE+WSC0Ed19KZfNeHUztud3rnZRU+mVncDi254ziBtt4b\nW90KCFGBFM2DN/ZPaIr/l0Nw94ShxRdNVfeRF/Q4TYIa2zN0LHahafxdWFUI\nq93uKE8WC1G7AwfZL81kUKPjFdrJzcHjCsudWib5mzD3iVvW2CFcs3O+laiI\nNfwz8Ze0+cFBiR9aIoLoakYDvKrVkloYjBHLKLuZaela627+Jty94tvDypS1\nimSnQv0n+hz+zCj8PhrH1qXAgfVB/0wR89svkK6LVJexLbaHI3AhNlFw8rTq\nMbFUhKe+OiqfgSu9M737fUav5DWGr/sObelJ7mCDiNrCows4hX/P9sglIjAv\n+QP8\r\n=XySY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "87ffd2a639dcf91fb59e38066136976e163df618", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.14.0_1623421007574_0.518712545659876", "host": "s3://npm-registry-packages"}}, "5.14.1": {"name": "@testing-library/jest-dom", "version": "5.14.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.14.1", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "8501e16f1e55a55d675fe73eecee32cdaddb9766", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.14.1.tgz", "fileCount": 34, "integrity": "sha512-dfB7HVIgTNCxH22M1+KU6viG5of2ldoA5ly8Ar8xkezKHKXjRvznCdbMbqjYGgO2xjRbwnR+rR8MLUIqF3kKbQ==", "signatures": [{"sig": "MEYCIQC/QhXpLVdWb3mL2rUWSjMJTTWV1j/Z+jaEal+jbfToWwIhALAM3uNbPi1Yr2FFb91vvx82lLiaYkuXKja40zd0TLHq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgw44oCRA9TVsSAnZWagAAUHUP/iZg2aKFvdyvgkj2oT7T\nWS9hNuzQ3LA7c/rcsCY+uybV7rXnsUdOQ14OkN4yGkdLm8HQzneI5IGKxjY4\nafdR4Xp0exTBi5v+5nh1PwJJ0rgsulAHG5UpO7B0npO8G4RC+XV+RTKGp6CI\nDCo+5xn+ICvf6i9F446yi9zIFVPS4fFEL22L8vcJhojRbI8DVl2FHoxNc9un\nm+vzsfihLoVV4IjJJArF0P8DUsW8sLf5CdcqTjsTzW34ANxUdgcqdoS2KtQJ\nI3TVLbaS5x+0mIyE7WBrtSmevWEoFtny78hx4FqXn06MwzuQ5gjjW62sLNw0\nEJRHGH/Bzq5jXfRquii7qjeH+blkYVXcu65L1nSfwPArHz+5/fVACnztgY0I\nQocn8bLu+UBycunjbLydJMJu63f7Q/1Srb5M2tVqzxUbIv2f7AW2DW0STbPf\nt5MTRof7C/umVVDxwIc7OIGeyI0IWVLH/OXVkF1nx7v3hWDII+lMmHagJtZy\nfhBgZluVtOl9uZ2LY2Qc3hmjHXYd4oSNgyIf5A20PgwrH9vK4UHevpscnKBg\nizuRfV3kLW06kgzKqMDJutZEYm4nR9Mssfu4jFZeFCQdcxkZS6nkYytNygbJ\nwORteZYMD3WXBzKIF+w29jzOGl1Fb9smeLCxT8hhChZ4hazkdVKZbmzfiqUS\nlayf\r\n=R1ps\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "fc9ce6d406f7c3379aac3fa7aed756af105dfdb4", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.14.1_1623428648577_0.20895775940679573", "host": "s3://npm-registry-packages"}}, "5.15.0": {"name": "@testing-library/jest-dom", "version": "5.15.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.15.0", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "4f5295dbc476a14aec3b07176434b3d51aae5da7", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.15.0.tgz", "fileCount": 34, "integrity": "sha512-lOMuQidnL1tWHLEWIhL6UvSZC1Qt3OkNe1khvi2h6xFiqpe5O8arYs46OU0qyUGq0cSTbroQyMktYNXu3a7sAA==", "signatures": [{"sig": "MEUCIQCst135pHu4UXh2849FehXsUZdcs1mYC+Q7rhyNPZootwIgHUnfAap2aWGId+3sUvCbjTQTS4VsL469sS+rAeBwvi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123352}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "4cb606cc591345a2f44240d7e74740b26fa9fa85", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.15.0_1635832431215_0.45635953619410574", "host": "s3://npm-registry-packages"}}, "5.15.1": {"name": "@testing-library/jest-dom", "version": "5.15.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.15.1", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "4c49ba4d244f235aec53f0a83498daeb4ee06c33", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.15.1.tgz", "fileCount": 34, "integrity": "sha512-kmj8opVDRE1E4GXyLlESsQthCXK7An28dFWxhiMwD7ZUI7ZxA6sjdJRxLerD9Jd8cHX4BDc1jzXaaZKqzlUkvg==", "signatures": [{"sig": "MEUCIQDT7j5tiJKDc7YSGp9JFzB0M2/a0pFUzKwfJI5Y1L0XFAIgdHN0p6uHpaaspepFPiBQXNLT9u0qDJsrync62NJiDB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnPPACRA9TVsSAnZWagAAmJAP/2TZvaaonwV98Y3fEC2+\nDD5F+jMW1zf5wIxOHt77+7Z9CxQqxZ+wx3rkBU3H48WqsSt81Vq/G8V03N/s\nmvDfQ7oS//V6Jj/D07PXkQzmq7kIxUX0z0tp+Fd7EY020mlo03OwSZrIlCkX\nlItUw+JE4EMZx9K6NCH6joiwloT94HwnxP/c1Ku73mVCHRcdrhUGQEtz74lr\nbWXzQxvdNGP9YO3n6HJzXU7tL4DLQmMbXAPRXgVxEMh3ya8WOQDpwa0OWYep\nCaCq3wj4H/mnPU+WIijhFWbC57uQdag/CK+V31peJoLSXmuB/81ViPcPBgyN\nOGSEFr54W3Z/OM0kFCwBwk8MblXR16LCES+il4aKB4nlPvMfu7dOWBpfZ1k+\nEo7+EP0BLy2r9yXE0vApZJgnc2Qth8ws5w3iyI2KndJBIyVVRazzbpqe5DYa\nYR1FFJN8JQ1+WAkinvFZsyyaU94gceG74o2YLdM1qgD1CEGhmSSNCFz0eMDK\nwZNFIidY4FqUM5cbEhJkubmQq8T1oYRDZPX96B3gOS2AmVVBF/prCh9e0Irg\ntzsZXcLNJq4x7c+byJVxV+GJzoomIrki5uh07iE5ArCY+2hnPzBEg80UNDE7\nlN+1d9tXYZ/gfmjcqZKhwbqO5QHsKg2D5/GRliRyvYaCkQm62kZMo/wS9laH\nnNbL\r\n=rynZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "dfcefa2e0262002b4eb0c2e382ea750e7f582347", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^4.2.2", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.15.1_1637675968696_0.6833507428348271", "host": "s3://npm-registry-packages"}}, "5.16.0": {"name": "@testing-library/jest-dom", "version": "5.16.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.16.0", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "de1a7c5fedfeb80eb2be9fc81f61473973b302b3", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.0.tgz", "fileCount": 34, "integrity": "sha512-ECygvCL6ufPfHna4fsk7o24+3PVNhRbioDpFbfSVEZaglT6EjuRP+w8I5tzigFz1fobpvCrVRoKyR4qx2QUCxw==", "signatures": [{"sig": "MEUCIQC7N7Zo2PM9SAqIeH/p9e+wGasGrd4CCGApzgDNOC9BRAIgNClXPmbFOm5UjY7GFNr1rTsy+SbcI0aVWEcSMZzp1oE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqofGCRA9TVsSAnZWagAAMD0P/j9M5YLKGj7+Tds3I+e9\n4Ak0XRjINtD+HJaSQMGeSBJ6FWiIIl/0apozloIsqhibRYMY+TfQpgGKj+HZ\nHhiLE/ZhNJAbDyDjYHAiEYeu3D4FAtVF5kktnIyKf+7ZRvGS3wFHHYXDPbah\n+zpKELEYhOPogJX4NTFGVgqVdvxX0IXrkf4lCaYdHzW4KMPSiU4BCqQQGeGF\nELMarY48D8frDHGa3CFX++itcUe9RZLAsmhEud/nYrM7OpV8v52ybUh9tz3Y\nZxdI9hOEeZw7iS+xwQ/Kkh3pA30+4uk6yGQiTOhzeFYkzxRJQHbJxwB3kiO3\n9SNltb1Qe4m2q4ukAuwQ4tvWAn2dfLVXqXSUXG3B4d643akKESOb22N0SunX\n4eIrxVYWzJmOfdZdEZqMM7kcaeurbu23NqIFV3bjUoZ7UStMZSsFhiJbszxw\nK9Agr8YILeiish0LaA8IRSza82SU5VCBpkhINjMxc/gfTwVjiPsx8Iev7eFm\nZj6L8p0fm0GSx7MR9zbM+miBG0DiQKGm/vwTtCl5DgV81UY2PDf6uK8psK2u\nB47QIAiX2xK6yRfTb/i4ANQhepOo1YMhU9djFEe+qULvIW1Ficg8udoNR7QT\nk2awVBqQxzE0xHPZYNgmqgSIjJIC/Mlpr8JkT9lSwD4tnFT6XuG0GDDA2dbL\ncVzB\r\n=j5uz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "de26c7ae2f502f284d5d4320634571877dbcf7fc", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.16.0_1638565829936_0.4287832268546199", "host": "s3://npm-registry-packages"}}, "5.16.1": {"name": "@testing-library/jest-dom", "version": "5.16.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.16.1", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "3db7df5ae97596264a7da9696fe14695ba02e51f", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.1.tgz", "fileCount": 34, "integrity": "sha512-ajUJdfDIuTCadB79ukO+0l8O+QwN0LiSxDaYUTI4LndbbUsGi6rWU1SCexXzBA2NSjlVB9/vbkasQIL3tmPBjw==", "signatures": [{"sig": "MEYCIQC4Yyx0sHCUlZQQysUrH4iT++P+NuDeaJ54u2C1fMmngQIhAIo8qF55CTNQrC6CEK74L7IhEySQuGYzPWW6o1t7AfQU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrfzPCRA9TVsSAnZWagAAjA8P+gLqmx9n29AxmbUMeLva\nulHwGcUrDpo4Vskht1eKh1tPXd3M0f3CdEEPX3Tt22sV8raNS/SzPkQoUpR1\nJPH+mTKrX/PMBs7yfN9eQI2aePu4gNR2ilwrt0OnW1lGsxnpBWOflhhsVRAF\nLygtuzIzJQsy/X2E/TXyXgEfGTZBop9wcBgpDmlqtFRKnSIJEEJR0eQZCJ/8\njMBEpOMDO6gL3nMGk/lJgVhHZ3Xx5MWRVp17PlJD5H7r3Xhu9qDeJQieElc5\nnjmyINAIlnDLIg7XzySZvmSrsJL3/VjHKPC4VTNe76uNYhfkQLKU9I9kmqZX\n6qvfBm1ugzEoxWc1nWNJ6dU7MCNpDV93ney2HB5ezvOR+mdvz6rdVJ54KUTr\nSHTiqsqs4/5wCflLUh4fcmEE1c/KSbAh8IO44RfXXTLIQeios1ql0JIncX0F\nt7rUSvvOXXEdM5ChIcUPphnTLAkY7lP8sD99IZj588SxoHAG1QlIW3bAhssb\nh8h5NiEYb/+DyJTBOy/K+NCSoqsRmlH0x66IE3Vyk6fx8wUTqk70fB+n4f7/\neZ+4f5LefPgW0P9HRofAsbHPiR/wIRPGXz4sopYyIcGG6e/6qmeH8kPB2+bC\nPqcbQ+qndyoaQI8qCxMoxkg9aUtXVRPeZHUos+aAiM5776sshGemH3/SXq1n\niQ1m\r\n=K1VB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "a9beb47455dae0f455ddacc473d0ddabe09f0b43", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.16.1_1638792399592_0.712890277117227", "host": "s3://npm-registry-packages"}}, "5.16.2": {"name": "@testing-library/jest-dom", "version": "5.16.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.16.2", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "f329b36b44aa6149cd6ced9adf567f8b6aa1c959", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.2.tgz", "fileCount": 34, "integrity": "sha512-6ewxs1MXWwsBFZXIk4nKKskWANelkdUehchEOokHsN8X7c2eKXGw+77aRV63UU8f/DTSVUPLaGxdrj4lN7D/ug==", "signatures": [{"sig": "MEUCIHB/wIx2XFskpEhMLv613ynqEVy7FfhMb5wqk+sVhP1GAiEArXrb93hdVH0cRZXy0oraTM1CkFGntez4YN5z2KL5UkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+ycOCRA9TVsSAnZWagAAgtMP/ii0XS7tSpa42JuZomi0\nl+MN+DANTYNtxej/v/qIE+UKXX69h5q6esKbLjblh/3dp06fA/2a6gwz+I4M\nGn6/eszDBvzeMsjPl4WqCuwL3f3Wt1jc5pa9Koo0XE7T10IpGWC0obqwehUe\n7RoLPmDAtP0SeM6x9fzJMqdJdYA6eT3z0d//cid0RmcHAXGbYnQkiWUWsiMk\nVWAx4SwVBkIDX+a1QzZjIPiwtCY4SsNQceoIPQUSHKcyP1x3W9KJrI9B/2Ck\nZhg5Y1lvNBxHlM0CQzAerHtjzxymGHoLnsebL9FwcPENvdVeCRM/ib3pQyea\n22A5gceznK3imHRC/6x9hgVCJFQmstl0XfNUB12OeWw7UIgvW6+s6oDRoN0I\nnaG6BOP6mthnn3Ha0CTW7jfi0/3G+vevOvQjJfEBomh8Gze9TWx1ggwzTXne\nmpTHRTF+q6VtS3xtUOB8s2Q/Kk5RdFnxC/hzQQwV3jeUgAR2+nwqAqkuy6IE\nRsif7cbCvJAs3S5SIcq/QL/9w7/1DViV/fykTfw9RfZKTHuiCnn6i9qSCQIR\npVgOVqvMzXDZOVcVjolc2cujPL2fk1C5R4XvEIX2b9uDLrNaNb34zFXfxkRd\nsABTZUhJAiTrMLJGhmrVFrAq/j/7ug07g3MAyLgmEPfAZpxZh71j+9TD43qt\nqO6w\r\n=1arq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "4d0ceeb3ef140bf924a9ffc7665b6996e1ea6961", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.18.3", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.16.2_1643849486655_0.550692897196646", "host": "s3://npm-registry-packages"}}, "5.16.3": {"name": "@testing-library/jest-dom", "version": "5.16.3", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.16.3", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "b76851a909586113c20486f1679ffb4d8ec27bfa", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.3.tgz", "fileCount": 34, "integrity": "sha512-u5DfKj4wfSt6akfndfu1eG06jsdyA/IUrlX2n3pyq5UXgXMhXY+NJb8eNK/7pqPWAhCKsCGWDdDO0zKMKAYkEA==", "signatures": [{"sig": "MEYCIQCw2Ow/eN5FfQsvP5IXYbBrHsTe9qfwyoHJGXNeOzE9ngIhAMsbOLvkVAIpLHc5e4igR7n/uzhmQ5GbhL12zvHWvvTd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPGuLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIfhAAjkm+yyvi8lkNHAYfc5OkdyBgxc2v3NGj0eoOTLLB1ADDUnmV\r\nv6ag5L4gal8q8q+stofET2qDhiw8a6YUmos+VC723DKoJrGpI5C8+5Svr8C1\r\nanzo8HJd5TWVqpo474ovl4BH8BtQWCRyqVU8bw+tLA3IU0zEbLKVzVVcMRee\r\n9yRu6wT7pBWF3XhjbM9X3XydlLEqbjHtN+3WBNG/sxLo4ct7+pEH82WgqGly\r\nuyc26OwNrjMk5lSCxNSJCJGZpreuVlUBNzhdXKXgCZ/p+Dgs1wqxHfDqbdE2\r\n7tzyM4suVt3NC0IE7f9n+j7ZMwul1Py48y44rT8ZdjyXis5fagL2j+R97DGa\r\nlDBpASNLj4Cde0ePhPj7yibBoSvqa+3nb6PfTwBp7BcGMjd90wIT668/cYPx\r\nOfgSrKBL480OMV+70SirYvav/qF1ZzDf62zjJULtKlMWHrLG2PTfWK2HEsNO\r\ndiLvXzllcFj46jPYAOjeOIfIENeeyLwiR3uWYHyMx7o9wMFUYRZdzQtTxPzW\r\nvwy5WDUr2grP2mknWk0sQi19Rv7i+UYeHVMblKR/Hv3EwW0iPMENzUrPhYCn\r\nWrB0VjUOvdorL3IY24wGD04Ols/gJZdno1VQ9Ag44W8HiFWkXC9dggGX/a2X\r\n91bsk5BnP2dcxsLdKhkLgXmzj2vQ/K55iis=\r\n=vUpw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "6988a67b1c2a98a5873c952beace08fc07eedbe2", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.19.0", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.16.3_1648126859453_0.6916970241455718", "host": "s3://npm-registry-packages"}}, "5.16.4": {"name": "@testing-library/jest-dom", "version": "5.16.4", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.16.4", "maintainers": [{"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "bcarroll22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "we<PERSON><PERSON>_ad<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "938302d7b8b483963a3ae821f1c0808f872245cd", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.4.tgz", "fileCount": 34, "integrity": "sha512-Gy+IoFutbMQcky0k+bqqumXZ1cTGswLsFqmNLzNdSKkU9KGV2u9oXhukCbbJ9/LRPKiqwxEE8VpV/+YZlfkPUA==", "signatures": [{"sig": "MEUCIQCk8jXZP8SVs1jjaKdbQj7rneBHmA3Opt0wx2JzHg80+QIgb4ufp9xrocbPNe0AcEqAVFHL/NRe3wpkWhMxnF3CLPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFjNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaBBAAoyl+59cKFtzTDKyHXDBhVqPa76kE85PYjPTtHqJFXAH6EZpD\r\nG3PqShOMl0KjZrdGU65CpuVAIAdU1TzSBo8weyIPM3dMhZBYb5Y1sFdRdwHK\r\n7g/xpsDLCNUCzmIN5jUfKnrymVB3hdcVemLZdIEGnl0rdA8NUCmQ1lzic7TH\r\nFJBl9/L9WZhaQGT7zhy8J1h4xfGS9NSBDcV+uU897oP7yBeRE8iAGN0BbjAV\r\nicG+kFDo4FUM9AkeSN2GHzWENkGYnnyw/8oKIHgVdrnAc8RGWXBaZuwZi8vv\r\nMwxU6c0yI8J646ayxIimYUaRUTMWy3BxjY9LEh5Fc3Ul7OellJc1xw/gIKSF\r\naTBLImkDb+DQ7pSgDgr8qc8blIv8hD+bGlOEUBusHRw7SNEPTOQGlfDyfK3h\r\ny73ZiSxlnNDnMncRyDYfH9p66qUayGMfqjnXNTBJy/fxPPfHLoXLIYPfQ6lA\r\niszKCoPAb+6yCczMJ87ZrKf1LngrEANs71Fw6/T6S9dfq85MHJ3qA10OIxwO\r\nvtdLxmB4HpmfxJbbfkIbjvq4RMhz2xm+wtV39g8hZhqS+RdGpvLn3DRIdKPK\r\nEj5S2Vw0ApXyrBp/Bx3shtgrv9AmGPb8ndFLBX9ilksb5C8S4/iFrfAS4HRE\r\nEUA1nvBPEZC9kGGDny2ZD+r1B+2vEjB2TzU=\r\n=BlIy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "af1845383ee2cba007f43460104f73409e7618ac", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"css": "^3.0.0", "chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.16.4_1649170636836_0.597011826433129", "host": "s3://npm-registry-packages"}}, "5.16.5": {"name": "@testing-library/jest-dom", "version": "5.16.5", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.16.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "3912846af19a29b2dbf32a6ae9c31ef52580074e", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.16.5.tgz", "fileCount": 34, "integrity": "sha512-N5ixQ2qKpi5OLYfwQmUb/5mSV9LneAcaUfp32pn4yCnpb8r/Yz0pXFPck21dIicKmi+ta5WRAknkZCfA8refMA==", "signatures": [{"sig": "MEYCIQCy/2Nbqq5ob+d3WfcBwnw/9I9ve6JIDmgm5VZaEiKL0gIhAIinGGi/gefLUV7yP5NYmQXUq0CbtT6EcwjZ1cWTNYvO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7D5+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo7g/+L5GiGMOM7/dar1v6LPY16V07rAW5yBNsxAzyMXOgPQIu6h3N\r\nOmbDE951ngQIDvcovMYt8NyoMoIgi9TiCcTtnVfzMTgI4/7W0GprNJZ8IuNX\r\nUPwRKI9CgaqdiIrO1VnIL3wfFnrQDlJ0XVyxazKnuTYrYcwT4bdkfRGiPA8D\r\nofzBwMxACN2GkPlxxg1f3B35QvgwD3ZFKfNMWqKqmspEJchcqWWh+13weCPF\r\nMIinetiekqZ7ABdWT5IaGNJKa1WmTNmVmepTMaIemf6eTAa9UG4kE7/EHBDL\r\nvuMDBSoGFx13A3bCykvUOwajVa1k0uJNKaR9j6dBbN7JPrvTuOfYEFRAj2OQ\r\n+62OWyP2jgnrl6UaBMiQlG6RmLsk6QYjHOhPD46tzWHyaia25LYIwj3SLr0S\r\n6c1bm4PC89JKXVRB4sTbt9SU9okfWjzF/tAq3S1El1wrvii9UofTxhZ92dv2\r\n1OZweki9YUhNB0tmiZpADkHnWKADYP1yK8relS/Apcfdtr/methO8Sa7ePye\r\nw+fgBz2v2ny1dn9KKU+Ua/rP1izOhSfOUrxVbKeaaKnxS3BxGDUB3a2v7D4b\r\nDuHBM0fVyiA2D7xJRoh/uCSTHq9LM22piZv7QY4CsJi0yU5aaPD4gOZfF1OP\r\nW6sPlvrRsUrOU7IbJlogkexkN0MnUEnv7qM=\r\n=dzMT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "948d90f32cc79339bdeebea0454599db74c5d071", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.0.1", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.16.5_1659649662183_0.5309095110916431", "host": "s3://npm-registry-packages"}}, "5.17.0": {"name": "@testing-library/jest-dom", "version": "5.17.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@5.17.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "5e97c8f9a15ccf4656da00fecab505728de81e0c", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-5.17.0.tgz", "fileCount": 35, "integrity": "sha512-ynmNeT7asXyH3aSVv4vvX4Rb+0qjOhdNHnO/3vuZNqPmhDpV/+rCSGwQ7bLcmU2cJ4dvoheIO85LQj0IbJHEtg==", "signatures": [{"sig": "MEQCIDHxT20un0iGAd1aoMbcgujOQn9fmLTT8tbG0bvTJSSXAiALokmHe5IbEQHemy5jNzSLusLllx/0kxRX5jLCw+y7Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130400}, "main": "dist/index.js", "engines": {"npm": ">=6", "node": ">=8", "yarn": ">=1"}, "gitHead": "d717c66cb4a32c806e53b287418a4013d37898fb", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.0.1", "dom-accessibility-api": "^0.5.6", "@types/testing-library__jest-dom": "^5.9.1"}, "eslintConfig": {"rules": {"@babel/no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^11.1.0", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_5.17.0_1689684618913_0.5804031572311217", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "@testing-library/jest-dom", "version": "6.0.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.0.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "d2ba5a3fd13724d5966b3f8cd24d2cedcab4fa76", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.0.0.tgz", "fileCount": 43, "integrity": "sha512-Ye2R3+/oM27jir8CzYPmuWdavTaKwNZcu0d22L9pO/vnOYE0wmrtpw79TQJa8H6gV8/i7yd+pLaqeLlA0rTMfg==", "signatures": [{"sig": "MEUCIQCHaCeFyIPUsFQ0Y03/ggKXALDR5hEj+UNMd1+RAr0FcQIgTrl8SBZYh2TQogTcC74dHfJH5LEcyAVDRs7bGcrVtCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162332}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "gitHead": "4b764b9f6a7b564d7f8ec0e9b0c6ba9cc875f2b8", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.0.1", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.0.0_1691944326086_0.24396476866742867", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "@testing-library/jest-dom", "version": "6.0.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.0.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "60c2aa05e0e3c07cbfaa319689e43227d95fa7c4", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.0.1.tgz", "fileCount": 52, "integrity": "sha512-0hx/AWrJp8EKr8LmC5jrV3Lx8TZySH7McU1Ix2czBPQnLd458CefSEGjZy7w8kaBRA6LhoPkGjoZ3yqSs338IQ==", "signatures": [{"sig": "MEYCIQDUTqppiz6HCgU3u8DyVCwUwJNU277Cvrm+iMtvxNqhWwIhAJvNoWqyraA1S/ClrkzwbbprJLzPu9D9u4QLhMbANrj7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189989}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "gitHead": "bdb34f12959578c77b18b0c0910d512768b20ab0", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "kcd-scripts build", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.0.1", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.0.1_1692336509771_0.5578625109425199", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "@testing-library/jest-dom", "version": "6.1.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "8e5017514d374b8e7abe294441130063e2b74a49", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.0.tgz", "fileCount": 34, "integrity": "sha512-EUAg9pvOkzmGXUSyAPt0h6yAXHxsn+FMNS1o7OX8TErmldZML2ywt10lotZXx/a1PDiSnq0fGGyEV/ybKSLPWQ==", "signatures": [{"sig": "MEYCIQDjsM5nU914A/0A9cy/HvgLxzwoAyGZZLMR7zWqIL5uCgIhAJmpmTuixnZyW3YQYATLjceLpJ8fi2kQLVeNweH7tL/8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235530}, "main": "dist/cjs/index.js", "types": "types/index.d.ts", "module": "dist/esm/index.js", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "3d834bbab9e5c43b1e94b3e0a1ca0fd13c00801e", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.0.1", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.0_1692765043076_0.282866954925469", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "@testing-library/jest-dom", "version": "6.1.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "1a159b93f03f55ffc00adae86c6d412af39588b1", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.1.tgz", "fileCount": 34, "integrity": "sha512-PEDx4fwesRjrUqhnqMz2+DxJtA0Qfy8IChXfx2P+f1mOI4ssc2b+fnzcmaevWPtfqFDE1q3veB9iJRFDw+xUVg==", "signatures": [{"sig": "MEUCID+7rvyHUxFab5B9RCoBkPK1u/hGjwJfs8Plf+HrhusZAiEAj20mHMsnBp2W+SG2UabPJvmRTZNci0xazLfpQ/OY+88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235523}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "853a3e51ba6757a34780e32953525b6142eadcf9", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.0.1", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.1_1692803962954_0.9680169479418952", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "@testing-library/jest-dom", "version": "6.1.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.2", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "3e7422904349223cb1e04968adada63f65f40d5b", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.2.tgz", "fileCount": 34, "integrity": "sha512-NP9jl1Q2qDDtx+cqogowtQtmgD2OVs37iMSIsTv5eN5ETRkf26Kj6ugVwA93/gZzzFWQAsgkKkcftDe91BJCkQ==", "signatures": [{"sig": "MEQCIB0lh12kvfkz7gdUUAScuy1Uk7OeOOzTCpu3fbgtsWefAiAY5x9ybiSKc5N6+IEIlmuuNxBbuoO7xcF32p6eYRFR0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235523}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "b959a681386164bf5d64f5b2b9c8bf891301bc12", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.0", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.2_1692864595267_0.4892078461549152", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "@testing-library/jest-dom", "version": "6.1.3", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.3", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "443118c9e4043f96396f120de2c7122504a079c5", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.3.tgz", "fileCount": 34, "integrity": "sha512-YzpjRHoCBWPzpPNtg6gnhasqtE/5O4qz8WCwDEaxtfnPO6gkaLrnuXusrGSPyhIGPezr1HM7ZH0CFaUTY9PJEQ==", "signatures": [{"sig": "MEUCIHWntm+WZ+62CiPaJyl34SEYlcCCaemc/Atflhnmc9FbAiEAtpHa9UCCeTPTJrOHIOYjmmqPeMik2LBsOw84rqKEwdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235518}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "5b492ace23d52b7cb7d3f91913ed0b5311905a26", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.0", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.3_1693968995044_0.10262881632372123", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "@testing-library/jest-dom", "version": "6.1.4", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.4", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "cf0835c33bc5ef00befb9e672b1e3e6a710e30e3", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.4.tgz", "fileCount": 34, "integrity": "sha512-wpoYrCYwSZ5/AxcrjLxJmCU6I5QAJXslEeSiMQqaWmP2Kzpd1LvF/qxmAIW2qposULGWq2gw30GgVNFLSc2Jnw==", "signatures": [{"sig": "MEUCIQC13/mB6J1mOuyHAxCdOyi63lQN9z6dpXyLXqOPsmU19AIgGQ06bGarmzHaV206cvCNnyO1R9FE+cBUHZ9RiXAyJHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235518}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "44f1eabc79b848f45daffac21c5f7383424e893c", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.1", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.4_1697086711209_0.2153230352082447", "host": "s3://npm-registry-packages"}}, "6.1.5": {"name": "@testing-library/jest-dom", "version": "6.1.5", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.5", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "0a635d0ad4a1a880089d967299d94e9cfc81fbe1", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.5.tgz", "fileCount": 34, "integrity": "sha512-3y04JLW+EceVPy2Em3VwNr95dOKqA8DhR0RJHhHKDZNYXcVXnEK7WIrpj4eYU8SVt/qYZ2aRWt/WgQ+grNES8g==", "signatures": [{"sig": "MEUCIEBxdkn+ltVbjjHNuKUehSAptN2SfB/JnUzwiiPE8ffNAiEA9n7mCRODzjPitDQIbOGRurBMECqBzxksGtdFECgV+tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235958}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "b7b7c6a9652f259434d13a22e4319826a4bd4d8b", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.1", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.5_1701373263125_0.41664858448212017", "host": "s3://npm-registry-packages"}}, "6.1.6": {"name": "@testing-library/jest-dom", "version": "6.1.6", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.1.6", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "d9a3ce61cd74ea792622d3da78a830f6786e8d93", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.1.6.tgz", "fileCount": 34, "integrity": "sha512-YwuiOdYEcxhfC2u5iNKlvg2Q5MgbutovP6drq7J1HrCbvR+G58BbtoCoq+L/kNlrNFsu2Kt3jaFAviLVxYHJZg==", "signatures": [{"sig": "MEUCIQD3BiWnHZXLGExmLB0+odC/QlAJ1Mu1roUq72R7ByLiGgIgBDCZP7Qq56ul/UfdTXdBPCNlGbrXpIkHs04kGC+LbO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235957}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "b64b953f76170f282c73329ee3479ede13610713", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.5.6"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.1.6_1703686282841_0.5322067518373645", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "@testing-library/jest-dom", "version": "6.2.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.2.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "b572bd5cd6b29314487bac7ba393188e4987b4f7", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.2.0.tgz", "fileCount": 34, "integrity": "sha512-+BVQlJ9cmEn5RDMUS8c2+TU6giLvzaHZ8sU/x0Jj7fk+6/46wPdwlgOPcpxS17CjcanBi/3VmGMqVr2rmbUmNw==", "signatures": [{"sig": "MEUCIQDIHRYIPc9BpaBcbNID4NTeYQQjyKwGgJddoixkS0fqKwIgQh3IUDjJiQ/x5reR9iNV9SMK7BtMn9ksVma9x/pJT18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236159}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "1fb156c2b544e0069c56a72a2f1909fe04850f6c", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.2.0_1704307832422_0.7575757201117306", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "@testing-library/jest-dom", "version": "6.2.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.2.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "0beba7515caad06de8733e1dd5a040fe34ad212d", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.2.1.tgz", "fileCount": 39, "integrity": "sha512-Nuy/uFFDe9h/2jwoUuMKgoxvgkUv4S9jI9bARj6dGUKJ3euRhg8JFi5sciYbrayoxkadEOZednRT9+vo6LvvxQ==", "signatures": [{"sig": "MEYCIQCEUgRZoCb8MztVK8FhPh/D81Y1mq/8F+trCXDpPpgKoQIhANA7aDFUF1hfO2qkFoKjoMcylinyXg8p9YvpE0zyMiS8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246551}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "5675b8668c09345e064001784338a85b7bf9f2af", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.2.1_1705944982768_0.473391119871581", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "@testing-library/jest-dom", "version": "6.3.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.3.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "e8d308e0c0e91d882340cbbfdea0e4daa7987d36", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.3.0.tgz", "fileCount": 39, "integrity": "sha512-hJVIrkFizEQxoWsGBlycTcQhrpoCH4DhXfrnHFFXgkx3Xdm15zycsq5Ep+vpw4W8S0NJa8cxDHcuJib+1tEbhg==", "signatures": [{"sig": "MEYCIQCD5EKL4iA3IpOl3qoEpXZMw9mzFS2X2sc+Yy2NJzxh8AIhAKboJUVkRr5woxuK6wb3l3Pm8umbXdZByHl9PY5xl40D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248028}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "9787ed59fcc930e3d33c8a6efe473da3eca01707", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.3.0_1706095770665_0.5430067955329738", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "@testing-library/jest-dom", "version": "6.4.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "e7391967af57273effdaa181fc291be0ecc155bd", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.0.tgz", "fileCount": 39, "integrity": "sha512-GgGT3OR8qhIjk2SBMy51AYDWoMnAyR/cwjZO4SttuBmIQ9wWy9QmVOeaSbgT5Bm0J6qLBaf4+dsJWfisvafoaA==", "signatures": [{"sig": "MEUCIQCu0KSyi7SFtPZyYjARerozzzEbb5tZto1mkAE1QXSxmgIgGTI+ow+NJaJXsv/90N5hkTEu8rrg8cg5f1EypsN7aLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260352}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "f7dc673dbc13e508f9867a9eb9c547ce3964b22c", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.0_1706574064804_0.3834465559926239", "host": "s3://npm-registry-packages"}}, "6.4.1": {"name": "@testing-library/jest-dom", "version": "6.4.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.1", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "1b0cc222c3a59f9cba2cc7947dc5fadc01210a37", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.1.tgz", "fileCount": 39, "integrity": "sha512-Z7qMM3J2Zw5H/nC2/5CYx5YcuaD56JmDFKNIozZ89VIo6o6Y9FMhssics4e2madEKYDNEpZz3+glPGz0yWMOag==", "signatures": [{"sig": "MEUCIE+h8ePRFbj67/45ezPb3MtZIpXtvlU3cEHrM8M3Yms9AiEAiIy1y0WBfwcd5g5kKJOwJ/vSxtAGJX/C+spkr7dDTV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260388}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "dd1c4dd504973efced2f9e02a35e7df3079914af", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.1_1706749414765_0.5508974743434307", "host": "s3://npm-registry-packages"}}, "6.4.2": {"name": "@testing-library/jest-dom", "version": "6.4.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.2", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "38949f6b63722900e2d75ba3c6d9bf8cffb3300e", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.2.tgz", "fileCount": 39, "integrity": "sha512-CzqH0AFymEMG48CpzXFriYYkOjk6ZGPCLMhW9e9jg3KMCn5OfJecF8GtGW7yGfR/IgCe3SX8BSwjdzI6BBbZLw==", "signatures": [{"sig": "MEUCIGDJ3etvm14JHiKWlIP3AuVul/sxGWj1eK1pN542+rBhAiEAiSIraBlh4p8ll7rwcd5p6gpizokVrunSq01Px7T/0jE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265804}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "a93c0c4a20ed4d3a9656261ea6a449d1015b7879", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.15", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.2_1707138044157_0.3189796102253626", "host": "s3://npm-registry-packages"}}, "6.4.3": {"name": "@testing-library/jest-dom", "version": "6.4.3", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.3", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "64f3a178b1f83c89c6cb93546aad1cfdc31a4cc2", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.3.tgz", "fileCount": 39, "integrity": "sha512-d1NUtNEN0hSUB/XWdF1GgdlD5S2tS0huQb2tkFL2usXRatR/EiHS6AhLtDcCb/iD9CS7kRmbAHt2O5JadkKyuA==", "signatures": [{"sig": "MEYCIQCLDXonARaXsB92YfrgM3WMnaK0vh2VG9nJTA6xyJ6xWwIhAINUQ6GH+ZVvGF4HWNniEg2pkPDHx7sAMU2yiTMG2urn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265336}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "20aca338d09062a2a71f6dd4e0daad65b26f6210", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.3_1714717369562_0.8650475440056886", "host": "s3://npm-registry-packages"}}, "6.4.4": {"name": "@testing-library/jest-dom", "version": "6.4.4", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.4", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "175c302f2ed0c2d7c04973ddeef31306610ef26f", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.4.tgz", "fileCount": 39, "integrity": "sha512-ZoJpNCaljSdUKF/NWEnWzjRSW6h+BJPGns24vL717eUI16Ltw3j5YV2KdjpQ7nAdxRzrnjeIIcntO6nXDEdHoA==", "signatures": [{"sig": "MEUCIGjvlvwmmbcRAb62JNkKOOayFPFcZON2Byv0Cr8z64Y5AiEA4+iYfYkbJzF5ANyB0QP+Dz7XnEdhGTmu7z52KhS2aRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265336}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "f03a582827453de6fac4510dcf4fa3148c7ed68a", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.4_1714749226522_0.10135608801184048", "host": "s3://npm-registry-packages"}}, "6.4.5": {"name": "@testing-library/jest-dom", "version": "6.4.5", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.5", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "badb40296477149136dabef32b572ddd3b56adf1", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.5.tgz", "fileCount": 39, "integrity": "sha512-AguB9yvTXmCnySBP1lWjfNNUwpbElsaQ567lt2VdGqAdHtpieLgjmcVyv1q7PMIvLbgpDdkWV5Ydv3FEejyp2A==", "signatures": [{"sig": "MEUCIQCwpgmqpYVJ3CfuqIvrwi/OTgp7aPSGnTDFqGzpue2ZAQIgZil189tH12j8zZiw3YXkBDtiJJKEoeZLXZwme7CP+yE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265366}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "e8c8b13c6de2a0ccffaa6539809c8c11f141beca", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.3.2", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.5_1714761133269_0.6366426587429761", "host": "s3://npm-registry-packages"}}, "6.4.6": {"name": "@testing-library/jest-dom", "version": "6.4.6", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.6", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "ec1df8108651bed5475534955565bed88c6732ce", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.6.tgz", "fileCount": 39, "integrity": "sha512-8qpnGVincVDLEcQXWaHOf6zmlbwTKc6Us6PPu4CRnPXCzo2OGBS5cwgMMOWdxDpEz1mkbvXHpEy99M5Yvt682w==", "signatures": [{"sig": "MEUCIDQZ+pr9RhUQ/Zc3iDwhC9QmjpEP6+MPLUGKwOI23aKPAiEAweLPL+cgMSHczPOGY3KTrMMv4L6e0sSGCsYX0x6kMvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265366}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "fd9ee68ae4ca93c47f0781ad23113d45118b8476", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.6_1718062063122_0.09774522858950885", "host": "s3://npm-registry-packages"}}, "6.4.7": {"name": "@testing-library/jest-dom", "version": "6.4.7", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.7", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "0f882b745f17a6a094d1d64d835a9440429f90f4", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.7.tgz", "fileCount": 39, "integrity": "sha512-GaKJ0nijoNf30dWSOOzQEBkWBRk4rG3C/efw8zKrimNuZpnS/6/AEwo0WvZHgJxG84cNCgxt+mtbe1fsvfLp2A==", "signatures": [{"sig": "MEUCIQCDpoGozcwvLHVzik5WjlTDT0G+n4I8PK17Bogxm8kF9wIgFlOU1I2MCATBJ8+v8xWANsTb7WRcHbir3mm29s9CcYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265360}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "5cc6298847e08872b79f827921c64c9ba261cc54", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "peerDependencies": {"jest": ">= 28", "vitest": ">= 0.32", "@types/bun": "latest", "@types/jest": ">= 28", "@jest/globals": ">= 28"}, "peerDependenciesMeta": {"jest": {"optional": true}, "vitest": {"optional": true}, "@types/bun": {"optional": true}, "@types/jest": {"optional": true}, "@jest/globals": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.7_1721676948030_0.7880724003763013", "host": "s3://npm-registry-packages"}}, "6.4.8": {"name": "@testing-library/jest-dom", "version": "6.4.8", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.4.8", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "9c435742b20c6183d4e7034f2b329d562c079daa", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.4.8.tgz", "fileCount": 39, "integrity": "sha512-JD0G+Zc38f5MBHA4NgxQMR5XtO5Jx9g86jqturNTt2WUfRmLDIY7iKkWHDCCTiDuFMre6nxAD5wHw9W5kI4rGw==", "signatures": [{"sig": "MEUCIQC0M8D428SHT1A5WeLDokrO9OavyoRJKFP0ecA/8xyhFAIgKOfK8WSHp6R/9wlOH8QxB8WE2BoBfSPw66+157llx3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265616}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "d02e80b1b7ed38090975bdfe49ab8063eec040f5", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@babel/runtime": "^7.9.2", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.4.8_1721737440395_0.5513282735422043", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "@testing-library/jest-dom", "version": "6.5.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.5.0", "maintainers": [{"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "50484da3f80fb222a853479f618a9ce5c47bfe54", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.5.0.tgz", "fileCount": 39, "integrity": "sha512-xGGHpBXYSHUUr6XsKBfs85TWlYKpTc37cSBBVrXcib2MkHLboWlkClhWF37JKlDb9KEq3dHs+f2xR7XJEWGBxA==", "signatures": [{"sig": "MEUCIQDE4Kw1Vx1uTf12/blO+D9ATsMWoekYf1E0wOvUBv9RZgIgC17oX8dzGLFwXfgrbsrWFPugcdCOg9I/yJ4D8h/m3fU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267621}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "62f0e3a43262b945bbf6bdd9bf231bf3e4ac7778", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.5.0_1724442466156_0.12083684299852737", "host": "s3://npm-registry-packages"}}, "6.6.0": {"name": "@testing-library/jest-dom", "version": "6.6.0", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.6.0", "maintainers": [{"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "f27a59e3550f0e29d3d362eefd47036b61a86bc1", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.0.tgz", "fileCount": 39, "integrity": "sha512-Y76dmd7C85xekWqylJqRmO6lr83cdVprTs0muSvkXr6M73auYK5OvZMc3tKe1F7wMFdzfeBCwVbkoGrRKWb+fg==", "signatures": [{"sig": "MEUCIQDOVZcdmAiYSPej8tfNBQttAHDLLJirbldHC41SjchRIwIgRoqaPK2XAWuBmac0wdAU0WQDZOnyXju1SbAzVKdgKbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281280}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "9b148043d082a83f0ae5cdc03cdfc6a7c4573e6e", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.6.0_1729096093234_0.0798970507638006", "host": "s3://npm-registry-packages"}}, "6.6.1": {"name": "@testing-library/jest-dom", "version": "6.6.1", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.6.1", "maintainers": [{"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "835612c9d8c529c835b15bbc1d1a924310c6c73c", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.1.tgz", "fileCount": 39, "integrity": "sha512-mNYIiAuP4yJwV2zBRQCV7PHoQwbb6/8TfMpPcwSUzcSVDJHWOXt6hjNtIN1v5knDmimYnjJxKhsoVd4LVGIO+w==", "signatures": [{"sig": "MEQCIHvz/ZAYIPv3ZVliEebmRoff5oDMWXG8QIAStYyZqFSvAiBsbMvP+BkyGvI0jHR6kpDRLJfSCVtCwAfJTGOT187kMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 280937}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "ced792e2f2773f16c249c6ce59fa8df968d28a20", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.6.1_1729104452914_0.08752995052374657", "host": "s3://npm-registry-packages"}}, "6.6.2": {"name": "@testing-library/jest-dom", "version": "6.6.2", "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"url": "http://gnapse.github.io", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@testing-library/jest-dom@6.6.2", "maintainers": [{"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/testing-library/jest-dom#readme", "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "dist": {"shasum": "8186aa9a07263adef9cc5a59a4772db8c31f4a5b", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.2.tgz", "fileCount": 39, "integrity": "sha512-P6GJD4yqc9jZLbe98j/EkyQDTPgqftohZF5FBkHY5BUERZmcf4HeO2k0XaefEg329ux2p21i1A1DmyQ1kKw2Jw==", "signatures": [{"sig": "MEYCIQDuH79wY61JDa4dhAjT6gUalvN9gkV0PbwXCV3DKPtJnAIhALvFjpGOQhsAi3WDFAMSJGvaPdpICwDga9fSN7b+gWU+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281549}, "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/index.mjs", "engines": {"npm": ">=6", "node": ">=14", "yarn": ">=1"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./vitest": {"import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}, "require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}}, "./matchers": {"import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}, "require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}}, "./jest-globals": {"import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}, "require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}}, "./package.json": "./package.json"}, "gitHead": "4468378fb4986018e0bacdebd02244decb9f0718", "scripts": {"lint": "kcd-scripts lint", "test": "kcd-scripts test", "build": "rollup -c", "setup": "npm install && npm run validate -s", "format": "kcd-scripts format", "validate": "kcd-scripts validate && npm run test:types", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage"}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/testing-library/jest-dom.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Custom jest matchers to test the state of the DOM", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"chalk": "^3.0.0", "lodash": "^4.17.21", "redent": "^3.0.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "@adobe/css-tools": "^4.4.0", "dom-accessibility-api": "^0.6.3"}, "eslintConfig": {"rules": {"no-invalid-this": "off"}, "extends": "./node_modules/kcd-scripts/eslint.js", "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/triple-slash-reference": "off"}}], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "_hasShrinkwrap": false, "devDependencies": {"jsdom": "^16.2.1", "expect": "^29.6.2", "rollup": "^3.28.1", "vitest": "^0.34.1", "@types/bun": "latest", "@types/web": "latest", "typescript": "^5.1.6", "kcd-scripts": "^14.0.0", "@jest/globals": "^29.6.2", "pretty-format": "^25.1.0", "rollup-plugin-delete": "^2.0.0", "@rollup/plugin-commonjs": "^25.0.4", "jest-watch-select-projects": "^2.0.0", "jest-environment-jsdom-sixteen": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/jest-dom_6.6.2_1729181236003_0.48616291805886624", "host": "s3://npm-registry-packages"}}, "6.6.3": {"name": "@testing-library/jest-dom", "version": "6.6.3", "description": "Custom jest matchers to test the state of the DOM", "main": "dist/index.js", "module": "dist/index.mjs", "exports": {".": {"require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}}, "./jest-globals": {"require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}, "import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}}, "./matchers": {"require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}, "import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}}, "./vitest": {"require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}, "import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}}, "./package.json": "./package.json"}, "types": "types/index.d.ts", "engines": {"node": ">=14", "npm": ">=6", "yarn": ">=1"}, "scripts": {"build": "rollup -c", "format": "kcd-scripts format", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "validate": "kcd-scripts validate && npm run test:types"}, "keywords": ["testing", "dom", "jest", "jsdom"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://gnapse.github.io"}, "license": "MIT", "dependencies": {"@adobe/css-tools": "^4.4.0", "aria-query": "^5.0.0", "chalk": "^3.0.0", "css.escape": "^1.5.1", "dom-accessibility-api": "^0.6.3", "lodash": "^4.17.21", "redent": "^3.0.0"}, "devDependencies": {"@jest/globals": "^29.6.2", "@rollup/plugin-commonjs": "^25.0.4", "@types/bun": "latest", "@types/web": "latest", "expect": "^29.6.2", "jest-environment-jsdom-sixteen": "^1.0.3", "jest-watch-select-projects": "^2.0.0", "jsdom": "^16.2.1", "kcd-scripts": "^14.0.0", "pretty-format": "^25.1.0", "rollup": "^3.28.1", "rollup-plugin-delete": "^2.0.0", "typescript": "^5.1.6", "vitest": "^0.34.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js", "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}, "rules": {"no-invalid-this": "off"}, "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/triple-slash-reference": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "repository": {"type": "git", "url": "git+https://github.com/testing-library/jest-dom.git"}, "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "homepage": "https://github.com/testing-library/jest-dom#readme", "gitHead": "5ba015651c7b10c154e5a4ae54f85df6010c5295", "_id": "@testing-library/jest-dom@6.6.3", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-IteBhl4XqYNkM54f4ejhLRJiZNqcSCoXUOG2CPK7qbD322KjQozM4kHQOfkG2oln9b9HTYqs+Sae8vBATubxxA==", "shasum": "26ba906cf928c0f8172e182c6fe214eb4f9f2bd2", "tarball": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.3.tgz", "fileCount": 39, "unpackedSize": 281564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJEWsMPFkYgL5r+b3aYndikbiXKJlI4mPmkchCCW5gyAiEAuXvGeQlQwCwBNMubuKNWchqEUQyIGMHx8wLBnseYvRM="}]}, "_npmUser": {"name": "testing-library-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-dom_6.6.3_1730387484896_0.8541983722892255"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-07-08T15:45:53.843Z", "modified": "2024-10-31T15:11:25.523Z", "0.0.0": "2019-07-08T15:45:54.187Z", "4.0.0": "2019-07-08T16:24:45.206Z", "4.0.1": "2019-08-19T03:37:19.298Z", "4.1.0": "2019-08-21T16:58:30.765Z", "4.1.1": "2019-10-07T15:11:22.093Z", "4.1.2": "2019-10-09T18:14:38.696Z", "4.2.0": "2019-10-25T12:05:33.932Z", "4.2.1": "2019-10-31T14:26:28.691Z", "4.2.2": "2019-10-31T14:37:14.785Z", "4.2.3": "2019-11-01T12:04:58.468Z", "4.2.4": "2019-11-14T15:42:56.032Z", "5.0.0": "2020-01-17T00:03:34.864Z", "5.0.1": "2020-01-22T13:23:27.245Z", "5.0.2": "2020-01-22T14:39:47.107Z", "5.1.0": "2020-01-31T19:38:17.935Z", "5.1.1": "2020-02-03T17:33:03.041Z", "5.2.0": "2020-03-25T17:00:50.399Z", "5.3.0": "2020-03-26T12:47:21.523Z", "5.4.0": "2020-04-08T15:02:15.240Z", "5.5.0": "2020-04-09T19:05:59.766Z", "5.6.0": "2020-05-07T18:36:45.643Z", "5.7.0": "2020-05-07T22:09:17.095Z", "5.7.0-beta.1": "2020-05-08T13:43:38.858Z", "5.8.0": "2020-05-19T21:58:44.026Z", "5.9.0": "2020-05-28T13:50:06.116Z", "5.10.0": "2020-06-11T14:50:07.672Z", "5.10.1": "2020-06-14T04:56:50.047Z", "5.11.0": "2020-06-25T15:56:21.694Z", "5.11.1": "2020-07-15T13:57:46.018Z", "5.11.2": "2020-07-28T18:16:59.418Z", "5.11.3": "2020-08-11T18:54:45.401Z", "5.11.4": "2020-08-22T10:42:17.602Z", "5.11.5": "2020-10-23T13:10:35.196Z", "5.11.6": "2020-11-13T14:51:10.642Z", "5.11.7": "2020-12-30T01:43:14.073Z", "5.11.8": "2020-12-30T03:31:47.416Z", "5.11.9": "2021-01-12T19:08:18.983Z", "5.11.10": "2021-03-25T15:57:15.663Z", "5.12.0": "2021-04-22T11:38:07.398Z", "5.13.0": "2021-06-03T17:05:38.673Z", "5.14.0": "2021-06-11T14:16:47.710Z", "5.14.1": "2021-06-11T16:24:08.728Z", "5.15.0": "2021-11-02T05:53:51.417Z", "5.15.1": "2021-11-23T13:59:28.863Z", "5.16.0": "2021-12-03T21:10:30.095Z", "5.16.1": "2021-12-06T12:06:39.739Z", "5.16.2": "2022-02-03T00:51:26.816Z", "5.16.3": "2022-03-24T13:00:59.606Z", "5.16.4": "2022-04-05T14:57:16.992Z", "5.16.5": "2022-08-04T21:47:42.327Z", "5.17.0": "2023-07-18T12:50:19.096Z", "6.0.0": "2023-08-13T16:32:06.346Z", "6.0.1": "2023-08-18T05:28:29.942Z", "6.1.0": "2023-08-23T04:30:43.293Z", "6.1.1": "2023-08-23T15:19:23.173Z", "6.1.2": "2023-08-24T08:09:55.470Z", "6.1.3": "2023-09-06T02:56:35.264Z", "6.1.4": "2023-10-12T04:58:31.439Z", "6.1.5": "2023-11-30T19:41:03.320Z", "6.1.6": "2023-12-27T14:11:23.105Z", "6.2.0": "2024-01-03T18:50:32.694Z", "6.2.1": "2024-01-22T17:36:22.946Z", "6.3.0": "2024-01-24T11:29:30.825Z", "6.4.0": "2024-01-30T00:21:04.990Z", "6.4.1": "2024-02-01T01:03:34.924Z", "6.4.2": "2024-02-05T13:00:44.302Z", "6.4.3": "2024-05-03T06:22:49.822Z", "6.4.4": "2024-05-03T15:13:46.721Z", "6.4.5": "2024-05-03T18:32:13.437Z", "6.4.6": "2024-06-10T23:27:43.370Z", "6.4.7": "2024-07-22T19:35:48.255Z", "6.4.8": "2024-07-23T12:24:00.580Z", "6.5.0": "2024-08-23T19:47:46.321Z", "6.6.0": "2024-10-16T16:28:13.518Z", "6.6.1": "2024-10-16T18:47:33.342Z", "6.6.2": "2024-10-17T16:07:16.355Z", "6.6.3": "2024-10-31T15:11:25.079Z"}, "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://gnapse.github.io"}, "license": "MIT", "homepage": "https://github.com/testing-library/jest-dom#readme", "keywords": ["testing", "dom", "jest", "jsdom"], "repository": {"type": "git", "url": "git+https://github.com/testing-library/jest-dom.git"}, "description": "Custom jest matchers to test the state of the DOM", "maintainers": [{"name": "phr<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eps1lon", "email": "<EMAIL>"}, {"name": "mdjas<PERSON><PERSON><PERSON><PERSON>", "email": "md<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "decroock<PERSON><PERSON>@gmail.com"}, {"name": "testing-library-bot", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dfcook", "email": "<EMAIL>"}, {"name": "gpx", "email": "<EMAIL>"}, {"name": "mpe<PERSON>", "email": "<EMAIL>"}, {"name": "mihar-22", "email": "<EMAIL>"}, {"name": "pago", "email": "<EMAIL>"}, {"name": "cmckinstry", "email": "<EMAIL>"}, {"name": "thymikee", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<div align=\"center\">\n<h1>jest-dom</h1>\n\n<a href=\"https://www.emojione.com/emoji/1f989\">\n  <img\n    height=\"80\"\n    width=\"80\"\n    alt=\"owl\"\n    src=\"https://raw.githubusercontent.com/testing-library/jest-dom/main/other/owl.png\"\n  />\n</a>\n\n<p>Custom jest matchers to test the state of the DOM</p>\n\n</div>\n\n---\n\n<!-- prettier-ignore-start -->\n[![Build Status][build-badge]][build]\n[![Code Coverage][coverage-badge]][coverage]\n[![version][version-badge]][package] [![downloads][downloads-badge]][npmtrends]\n[![MIT License][license-badge]][license]\n\n[![All Contributors](https://img.shields.io/badge/all_contributors-28-orange.svg?style=flat-square)](#contributors-)\n[![PRs Welcome][prs-badge]][prs] [![Code of Conduct][coc-badge]][coc]\n[![Discord][discord-badge]][discord]\n\n[![Watch on GitHub][github-watch-badge]][github-watch]\n[![Star on GitHub][github-star-badge]][github-star]\n[![Tweet][twitter-badge]][twitter]\n<!-- prettier-ignore-end -->\n\n## The problem\n\nYou want to use [jest][] to write tests that assert various things about the\nstate of a DOM. As part of that goal, you want to avoid all the repetitive\npatterns that arise in doing so. Checking for an element's attributes, its text\ncontent, its css classes, you name it.\n\n## This solution\n\nThe `@testing-library/jest-dom` library provides a set of custom jest matchers\nthat you can use to extend jest. These will make your tests more declarative,\nclear to read and to maintain.\n\n## Table of Contents\n\n<!-- START doctoc generated TOC please keep comment here to allow auto update -->\n<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->\n\n- [Installation](#installation)\n- [Usage](#usage)\n  - [With `@jest/globals`](#with-jestglobals)\n  - [With Vitest](#with-vitest)\n  - [With TypeScript](#with-typescript)\n  - [With another Jest-compatible `expect`](#with-another-jest-compatible-expect)\n- [Custom matchers](#custom-matchers)\n  - [`toBeDisabled`](#tobedisabled)\n  - [`toBeEnabled`](#tobeenabled)\n  - [`toBeEmptyDOMElement`](#tobeemptydomelement)\n  - [`toBeInTheDocument`](#tobeinthedocument)\n  - [`toBeInvalid`](#tobeinvalid)\n  - [`toBeRequired`](#toberequired)\n  - [`toBeValid`](#tobevalid)\n  - [`toBeVisible`](#tobevisible)\n  - [`toContainElement`](#tocontainelement)\n  - [`toContainHTML`](#tocontainhtml)\n  - [`toHaveAccessibleDescription`](#tohaveaccessibledescription)\n  - [`toHaveAccessibleErrorMessage`](#tohaveaccessibleerrormessage)\n  - [`toHaveAccessibleName`](#tohaveaccessiblename)\n  - [`toHaveAttribute`](#tohaveattribute)\n  - [`toHaveClass`](#tohaveclass)\n  - [`toHaveFocus`](#tohavefocus)\n  - [`toHaveFormValues`](#tohaveformvalues)\n  - [`toHaveStyle`](#tohavestyle)\n  - [`toHaveTextContent`](#tohavetextcontent)\n  - [`toHaveValue`](#tohavevalue)\n  - [`toHaveDisplayValue`](#tohavedisplayvalue)\n  - [`toBeChecked`](#tobechecked)\n  - [`toBePartiallyChecked`](#tobepartiallychecked)\n  - [`toHaveRole`](#tohaverole)\n  - [`toHaveErrorMessage`](#tohaveerrormessage)\n  - [`toHaveSelection`](#tohaveselection)\n- [Deprecated matchers](#deprecated-matchers)\n  - [`toBeEmpty`](#tobeempty)\n  - [`toBeInTheDOM`](#tobeinthedom)\n  - [`toHaveDescription`](#tohavedescription)\n- [Inspiration](#inspiration)\n- [Other Solutions](#other-solutions)\n- [Guiding Principles](#guiding-principles)\n- [Contributors](#contributors)\n- [LICENSE](#license)\n\n<!-- END doctoc generated TOC please keep comment here to allow auto update -->\n\n## Installation\n\nThis module is distributed via [npm][npm] which is bundled with [node][node] and\nshould be installed as one of your project's `devDependencies`:\n\n```\nnpm install --save-dev @testing-library/jest-dom\n```\n\nor\n\nfor installation with [yarn](https://yarnpkg.com/) package manager.\n\n```\nyarn add --dev @testing-library/jest-dom\n```\n\n> Note: We also recommend installing the jest-dom eslint plugin which provides\n> auto-fixable lint rules that prevent false positive tests and improve test\n> readability by ensuring you are using the right matchers in your tests. More\n> details can be found at\n> [eslint-plugin-jest-dom](https://github.com/testing-library/eslint-plugin-jest-dom).\n\n## Usage\n\nImport `@testing-library/jest-dom` once (for instance in your [tests setup\nfile][]) and you're good to go:\n\n[tests setup file]:\n  https://jestjs.io/docs/en/configuration.html#setupfilesafterenv-array\n\n```javascript\n// In your own jest-setup.js (or any other name)\nimport '@testing-library/jest-dom'\n\n// In jest.config.js add (if you haven't already)\nsetupFilesAfterEnv: ['<rootDir>/jest-setup.js']\n```\n\n### With `@jest/globals`\n\nIf you are using [`@jest/globals`][jest-globals announcement] with\n[`injectGlobals: false`][inject-globals docs], you will need to use a different\nimport in your tests setup file:\n\n```javascript\n// In your own jest-setup.js (or any other name)\nimport '@testing-library/jest-dom/jest-globals'\n```\n\n[jest-globals announcement]:\n  https://jestjs.io/blog/2020/05/05/jest-26#a-new-way-to-consume-jest---jestglobals\n[inject-globals docs]:\n  https://jestjs.io/docs/configuration#injectglobals-boolean\n\n### With Vitest\n\nIf you are using [vitest][], this module will work as-is, but you will need to\nuse a different import in your tests setup file. This file should be added to\nthe [`setupFiles`][vitest setupfiles] property in your vitest config:\n\n```javascript\n// In your own vitest-setup.js (or any other name)\nimport '@testing-library/jest-dom/vitest'\n\n// In vitest.config.js add (if you haven't already)\nsetupFiles: ['./vitest-setup.js']\n```\n\nAlso, depending on your local setup, you may need to update your\n`tsconfig.json`:\n\n```json\n  // In tsconfig.json\n  \"compilerOptions\": {\n    ...\n    \"types\": [\"vitest/globals\", \"@testing-library/jest-dom\"]\n  },\n  \"include\": [\n    ...\n    \"./vitest.setup.ts\"\n  ],\n```\n\n[vitest]: https://vitest.dev/\n[vitest setupfiles]: https://vitest.dev/config/#setupfiles\n\n### With TypeScript\n\nIf you're using TypeScript, make sure your setup file is a `.ts` and not a `.js`\nto include the necessary types.\n\nYou will also need to include your setup file in your `tsconfig.json` if you\nhaven't already:\n\n```json\n  // In tsconfig.json\n  \"include\": [\n    ...\n    \"./jest-setup.ts\"\n  ],\n```\n\n### With another Jest-compatible `expect`\n\nIf you are using a different test runner that is compatible with Jest's `expect`\ninterface, it might be possible to use it with this library:\n\n```javascript\nimport * as matchers from '@testing-library/jest-dom/matchers'\nimport {expect} from 'my-test-runner/expect'\n\nexpect.extend(matchers)\n```\n\n## Custom matchers\n\n`@testing-library/jest-dom` can work with any library or framework that returns\nDOM elements from queries. The custom matcher examples below are written using\nmatchers from `@testing-library`'s suite of libraries (e.g. `getByTestId`,\n`queryByTestId`, `getByText`, etc.)\n\n### `toBeDisabled`\n\n```typescript\ntoBeDisabled()\n```\n\nThis allows you to check whether an element is disabled from the user's\nperspective. According to the specification, the following elements can be\n[disabled](https://html.spec.whatwg.org/multipage/semantics-other.html#disabled-elements):\n`button`, `input`, `select`, `textarea`, `optgroup`, `option`, `fieldset`, and\ncustom elements.\n\nThis custom matcher considers an element as disabled if the element is among the\ntypes of elements that can be disabled (listed above), and the `disabled`\nattribute is present. It will also consider the element as disabled if it's\ninside a parent form element that supports being disabled and has the `disabled`\nattribute present.\n\n#### Examples\n\n```html\n<button data-testid=\"button\" type=\"submit\" disabled>submit</button>\n<fieldset disabled><input type=\"text\" data-testid=\"input\" /></fieldset>\n<a href=\"...\" disabled>link</a>\n```\n\n```javascript\nexpect(getByTestId('button')).toBeDisabled()\nexpect(getByTestId('input')).toBeDisabled()\nexpect(getByText('link')).not.toBeDisabled()\n```\n\n> This custom matcher does not take into account the presence or absence of the\n> `aria-disabled` attribute. For more on why this is the case, check\n> [#144](https://github.com/testing-library/jest-dom/issues/144).\n\n<hr />\n\n### `toBeEnabled`\n\n```typescript\ntoBeEnabled()\n```\n\nThis allows you to check whether an element is not disabled from the user's\nperspective.\n\nIt works like `not.toBeDisabled()`. Use this matcher to avoid double negation in\nyour tests.\n\n> This custom matcher does not take into account the presence or absence of the\n> `aria-disabled` attribute. For more on why this is the case, check\n> [#144](https://github.com/testing-library/jest-dom/issues/144).\n\n<hr />\n\n### `toBeEmptyDOMElement`\n\n```typescript\ntoBeEmptyDOMElement()\n```\n\nThis allows you to assert whether an element has no visible content for the\nuser. It ignores comments but will fail if the element contains white-space.\n\n#### Examples\n\n```html\n<span data-testid=\"not-empty\"><span data-testid=\"empty\"></span></span>\n<span data-testid=\"with-whitespace\"> </span>\n<span data-testid=\"with-comment\"><!-- comment --></span>\n```\n\n```javascript\nexpect(getByTestId('empty')).toBeEmptyDOMElement()\nexpect(getByTestId('not-empty')).not.toBeEmptyDOMElement()\nexpect(getByTestId('with-whitespace')).not.toBeEmptyDOMElement()\n```\n\n<hr />\n\n### `toBeInTheDocument`\n\n```typescript\ntoBeInTheDocument()\n```\n\nThis allows you to assert whether an element is present in the document or not.\n\n#### Examples\n\n```html\n<span data-testid=\"html-element\"><span>Html Element</span></span>\n<svg data-testid=\"svg-element\"></svg>\n```\n\n```javascript\nexpect(\n  getByTestId(document.documentElement, 'html-element'),\n).toBeInTheDocument()\nexpect(getByTestId(document.documentElement, 'svg-element')).toBeInTheDocument()\nexpect(\n  queryByTestId(document.documentElement, 'does-not-exist'),\n).not.toBeInTheDocument()\n```\n\n> Note: This matcher does not find detached elements. The element must be added\n> to the document to be found by toBeInTheDocument. If you desire to search in a\n> detached element please use: [`toContainElement`](#tocontainelement)\n\n<hr />\n\n### `toBeInvalid`\n\n```typescript\ntoBeInvalid()\n```\n\nThis allows you to check if an element, is currently invalid.\n\nAn element is invalid if it has an\n[`aria-invalid` attribute](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/ARIA_Techniques/Using_the_aria-invalid_attribute)\nwith no value or a value of `\"true\"`, or if the result of\n[`checkValidity()`](https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/HTML5/Constraint_validation)\nis `false`.\n\n#### Examples\n\n```html\n<input data-testid=\"no-aria-invalid\" />\n<input data-testid=\"aria-invalid\" aria-invalid />\n<input data-testid=\"aria-invalid-value\" aria-invalid=\"true\" />\n<input data-testid=\"aria-invalid-false\" aria-invalid=\"false\" />\n\n<form data-testid=\"valid-form\">\n  <input />\n</form>\n\n<form data-testid=\"invalid-form\">\n  <input required />\n</form>\n```\n\n```javascript\nexpect(getByTestId('no-aria-invalid')).not.toBeInvalid()\nexpect(getByTestId('aria-invalid')).toBeInvalid()\nexpect(getByTestId('aria-invalid-value')).toBeInvalid()\nexpect(getByTestId('aria-invalid-false')).not.toBeInvalid()\n\nexpect(getByTestId('valid-form')).not.toBeInvalid()\nexpect(getByTestId('invalid-form')).toBeInvalid()\n```\n\n<hr />\n\n### `toBeRequired`\n\n```typescript\ntoBeRequired()\n```\n\nThis allows you to check if a form element is currently required.\n\nAn element is required if it is having a `required` or `aria-required=\"true\"`\nattribute.\n\n#### Examples\n\n```html\n<input data-testid=\"required-input\" required />\n<input data-testid=\"aria-required-input\" aria-required=\"true\" />\n<input data-testid=\"conflicted-input\" required aria-required=\"false\" />\n<input data-testid=\"aria-not-required-input\" aria-required=\"false\" />\n<input data-testid=\"optional-input\" />\n<input data-testid=\"unsupported-type\" type=\"image\" required />\n<select data-testid=\"select\" required></select>\n<textarea data-testid=\"textarea\" required></textarea>\n<div data-testid=\"supported-role\" role=\"tree\" required></div>\n<div data-testid=\"supported-role-aria\" role=\"tree\" aria-required=\"true\"></div>\n```\n\n```javascript\nexpect(getByTestId('required-input')).toBeRequired()\nexpect(getByTestId('aria-required-input')).toBeRequired()\nexpect(getByTestId('conflicted-input')).toBeRequired()\nexpect(getByTestId('aria-not-required-input')).not.toBeRequired()\nexpect(getByTestId('optional-input')).not.toBeRequired()\nexpect(getByTestId('unsupported-type')).not.toBeRequired()\nexpect(getByTestId('select')).toBeRequired()\nexpect(getByTestId('textarea')).toBeRequired()\nexpect(getByTestId('supported-role')).not.toBeRequired()\nexpect(getByTestId('supported-role-aria')).toBeRequired()\n```\n\n<hr />\n\n### `toBeValid`\n\n```typescript\ntoBeValid()\n```\n\nThis allows you to check if the value of an element, is currently valid.\n\nAn element is valid if it has no\n[`aria-invalid` attribute](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/ARIA_Techniques/Using_the_aria-invalid_attribute)s\nor an attribute value of `\"false\"`. The result of\n[`checkValidity()`](https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/HTML5/Constraint_validation)\nmust also be `true` if it's a form element.\n\n#### Examples\n\n```html\n<input data-testid=\"no-aria-invalid\" />\n<input data-testid=\"aria-invalid\" aria-invalid />\n<input data-testid=\"aria-invalid-value\" aria-invalid=\"true\" />\n<input data-testid=\"aria-invalid-false\" aria-invalid=\"false\" />\n\n<form data-testid=\"valid-form\">\n  <input />\n</form>\n\n<form data-testid=\"invalid-form\">\n  <input required />\n</form>\n```\n\n```javascript\nexpect(getByTestId('no-aria-invalid')).toBeValid()\nexpect(getByTestId('aria-invalid')).not.toBeValid()\nexpect(getByTestId('aria-invalid-value')).not.toBeValid()\nexpect(getByTestId('aria-invalid-false')).toBeValid()\n\nexpect(getByTestId('valid-form')).toBeValid()\nexpect(getByTestId('invalid-form')).not.toBeValid()\n```\n\n<hr />\n\n### `toBeVisible`\n\n```typescript\ntoBeVisible()\n```\n\nThis allows you to check if an element is currently visible to the user.\n\nAn element is visible if **all** the following conditions are met:\n\n- it is present in the document\n- it does not have its css property `display` set to `none`\n- it does not have its css property `visibility` set to either `hidden` or\n  `collapse`\n- it does not have its css property `opacity` set to `0`\n- its parent element is also visible (and so on up to the top of the DOM tree)\n- it does not have the\n  [`hidden`](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/hidden)\n  attribute\n- if `<details />` it has the `open` attribute\n\n#### Examples\n\n```html\n<div data-testid=\"zero-opacity\" style=\"opacity: 0\">Zero Opacity Example</div>\n<div data-testid=\"visibility-hidden\" style=\"visibility: hidden\">\n  Visibility Hidden Example\n</div>\n<div data-testid=\"display-none\" style=\"display: none\">Display None Example</div>\n<div style=\"opacity: 0\">\n  <span data-testid=\"hidden-parent\">Hidden Parent Example</span>\n</div>\n<div data-testid=\"visible\">Visible Example</div>\n<div data-testid=\"hidden-attribute\" hidden>Hidden Attribute Example</div>\n<details>\n  <summary>Title of hidden text</summary>\n  Hidden Details Example\n</details>\n<details open>\n  <summary>Title of visible text</summary>\n  <div>Visible Details Example</div>\n</details>\n```\n\n```javascript\nexpect(getByText('Zero Opacity Example')).not.toBeVisible()\nexpect(getByText('Visibility Hidden Example')).not.toBeVisible()\nexpect(getByText('Display None Example')).not.toBeVisible()\nexpect(getByText('Hidden Parent Example')).not.toBeVisible()\nexpect(getByText('Visible Example')).toBeVisible()\nexpect(getByText('Hidden Attribute Example')).not.toBeVisible()\nexpect(getByText('Hidden Details Example')).not.toBeVisible()\nexpect(getByText('Visible Details Example')).toBeVisible()\n```\n\n<hr />\n\n### `toContainElement`\n\n```typescript\ntoContainElement(element: HTMLElement | SVGElement | null)\n```\n\nThis allows you to assert whether an element contains another element as a\ndescendant or not.\n\n#### Examples\n\n```html\n<span data-testid=\"ancestor\"><span data-testid=\"descendant\"></span></span>\n```\n\n```javascript\nconst ancestor = getByTestId('ancestor')\nconst descendant = getByTestId('descendant')\nconst nonExistantElement = getByTestId('does-not-exist')\n\nexpect(ancestor).toContainElement(descendant)\nexpect(descendant).not.toContainElement(ancestor)\nexpect(ancestor).not.toContainElement(nonExistantElement)\n```\n\n<hr />\n\n### `toContainHTML`\n\n```typescript\ntoContainHTML(htmlText: string)\n```\n\nAssert whether a string representing a HTML element is contained in another\nelement. The string should contain valid html, and not any incomplete html.\n\n#### Examples\n\n```html\n<span data-testid=\"parent\"><span data-testid=\"child\"></span></span>\n```\n\n```javascript\n// These are valid uses\nexpect(getByTestId('parent')).toContainHTML('<span data-testid=\"child\"></span>')\nexpect(getByTestId('parent')).toContainHTML('<span data-testid=\"child\" />')\nexpect(getByTestId('parent')).not.toContainHTML('<br />')\n\n// These won't work\nexpect(getByTestId('parent')).toContainHTML('data-testid=\"child\"')\nexpect(getByTestId('parent')).toContainHTML('data-testid')\nexpect(getByTestId('parent')).toContainHTML('</span>')\n```\n\n> Chances are you probably do not need to use this matcher. We encourage testing\n> from the perspective of how the user perceives the app in a browser. That's\n> why testing against a specific DOM structure is not advised.\n>\n> It could be useful in situations where the code being tested renders html that\n> was obtained from an external source, and you want to validate that that html\n> code was used as intended.\n>\n> It should not be used to check DOM structure that you control. Please use\n> [`toContainElement`](#tocontainelement) instead.\n\n<hr />\n\n### `toHaveAccessibleDescription`\n\n```typescript\ntoHaveAccessibleDescription(expectedAccessibleDescription?: string | RegExp)\n```\n\nThis allows you to assert that an element has the expected\n[accessible description](https://w3c.github.io/accname/).\n\nYou can pass the exact string of the expected accessible description, or you can\nmake a partial match passing a regular expression, or by using\n[expect.stringContaining](https://jestjs.io/docs/en/expect.html#expectnotstringcontainingstring)/[expect.stringMatching](https://jestjs.io/docs/en/expect.html#expectstringmatchingstring-regexp).\n\n#### Examples\n\n```html\n<a\n  data-testid=\"link\"\n  href=\"/\"\n  aria-label=\"Home page\"\n  title=\"A link to start over\"\n  >Start</a\n>\n<a data-testid=\"extra-link\" href=\"/about\" aria-label=\"About page\">About</a>\n<img src=\"avatar.jpg\" data-testid=\"avatar\" alt=\"User profile pic\" />\n<img\n  src=\"logo.jpg\"\n  data-testid=\"logo\"\n  alt=\"Company logo\"\n  aria-describedby=\"t1\"\n/>\n<span id=\"t1\" role=\"presentation\">The logo of Our Company</span>\n<img\n  src=\"logo.jpg\"\n  data-testid=\"logo2\"\n  alt=\"Company logo\"\n  aria-description=\"The logo of Our Company\"\n/>\n```\n\n```js\nexpect(getByTestId('link')).toHaveAccessibleDescription()\nexpect(getByTestId('link')).toHaveAccessibleDescription('A link to start over')\nexpect(getByTestId('link')).not.toHaveAccessibleDescription('Home page')\nexpect(getByTestId('extra-link')).not.toHaveAccessibleDescription()\nexpect(getByTestId('avatar')).not.toHaveAccessibleDescription()\nexpect(getByTestId('logo')).not.toHaveAccessibleDescription('Company logo')\nexpect(getByTestId('logo')).toHaveAccessibleDescription(\n  'The logo of Our Company',\n)\nexpect(getByTestId('logo2')).toHaveAccessibleDescription(\n  'The logo of Our Company',\n)\n```\n\n<hr />\n\n### `toHaveAccessibleErrorMessage`\n\n```typescript\ntoHaveAccessibleErrorMessage(expectedAccessibleErrorMessage?: string | RegExp)\n```\n\nThis allows you to assert that an element has the expected\n[accessible error message](https://w3c.github.io/aria/#aria-errormessage).\n\nYou can pass the exact string of the expected accessible error message.\nAlternatively, you can perform a partial match by passing a regular expression\nor by using\n[expect.stringContaining](https://jestjs.io/docs/en/expect.html#expectnotstringcontainingstring)/[expect.stringMatching](https://jestjs.io/docs/en/expect.html#expectstringmatchingstring-regexp).\n\n#### Examples\n\n```html\n<input\n  aria-label=\"Has Error\"\n  aria-invalid=\"true\"\n  aria-errormessage=\"error-message\"\n/>\n<div id=\"error-message\" role=\"alert\">This field is invalid</div>\n\n<input aria-label=\"No Error Attributes\" />\n<input\n  aria-label=\"Not Invalid\"\n  aria-invalid=\"false\"\n  aria-errormessage=\"error-message\"\n/>\n```\n\n```js\n// Inputs with Valid Error Messages\nexpect(getByRole('textbox', {name: 'Has Error'})).toHaveAccessibleErrorMessage()\nexpect(getByRole('textbox', {name: 'Has Error'})).toHaveAccessibleErrorMessage(\n  'This field is invalid',\n)\nexpect(getByRole('textbox', {name: 'Has Error'})).toHaveAccessibleErrorMessage(\n  /invalid/i,\n)\nexpect(\n  getByRole('textbox', {name: 'Has Error'}),\n).not.toHaveAccessibleErrorMessage('This field is absolutely correct!')\n\n// Inputs without Valid Error Messages\nexpect(\n  getByRole('textbox', {name: 'No Error Attributes'}),\n).not.toHaveAccessibleErrorMessage()\n\nexpect(\n  getByRole('textbox', {name: 'Not Invalid'}),\n).not.toHaveAccessibleErrorMessage()\n```\n\n<hr />\n\n### `toHaveAccessibleName`\n\n```typescript\ntoHaveAccessibleName(expectedAccessibleName?: string | RegExp)\n```\n\nThis allows you to assert that an element has the expected\n[accessible name](https://w3c.github.io/accname/). It is useful, for instance,\nto assert that form elements and buttons are properly labelled.\n\nYou can pass the exact string of the expected accessible name, or you can make a\npartial match passing a regular expression, or by using\n[expect.stringContaining](https://jestjs.io/docs/en/expect.html#expectnotstringcontainingstring)/[expect.stringMatching](https://jestjs.io/docs/en/expect.html#expectstringmatchingstring-regexp).\n\n#### Examples\n\n```html\n<img data-testid=\"img-alt\" src=\"\" alt=\"Test alt\" />\n<img data-testid=\"img-empty-alt\" src=\"\" alt=\"\" />\n<svg data-testid=\"svg-title\"><title>Test title</title></svg>\n<button data-testid=\"button-img-alt\"><img src=\"\" alt=\"Test\" /></button>\n<p><img data-testid=\"img-paragraph\" src=\"\" alt=\"\" /> Test content</p>\n<button data-testid=\"svg-button\"><svg><title>Test</title></svg></p>\n<div><svg data-testid=\"svg-without-title\"></svg></div>\n<input data-testid=\"input-title\" title=\"test\" />\n```\n\n```javascript\nexpect(getByTestId('img-alt')).toHaveAccessibleName('Test alt')\nexpect(getByTestId('img-empty-alt')).not.toHaveAccessibleName()\nexpect(getByTestId('svg-title')).toHaveAccessibleName('Test title')\nexpect(getByTestId('button-img-alt')).toHaveAccessibleName()\nexpect(getByTestId('img-paragraph')).not.toHaveAccessibleName()\nexpect(getByTestId('svg-button')).toHaveAccessibleName()\nexpect(getByTestId('svg-without-title')).not.toHaveAccessibleName()\nexpect(getByTestId('input-title')).toHaveAccessibleName()\n```\n\n<hr />\n\n### `toHaveAttribute`\n\n```typescript\ntoHaveAttribute(attr: string, value?: any)\n```\n\nThis allows you to check whether the given element has an attribute or not. You\ncan also optionally check that the attribute has a specific expected value or\npartial match using\n[expect.stringContaining](https://jestjs.io/docs/en/expect.html#expectnotstringcontainingstring)/[expect.stringMatching](https://jestjs.io/docs/en/expect.html#expectstringmatchingstring-regexp)\n\n#### Examples\n\n```html\n<button data-testid=\"ok-button\" type=\"submit\" disabled>ok</button>\n```\n\n```javascript\nconst button = getByTestId('ok-button')\n\nexpect(button).toHaveAttribute('disabled')\nexpect(button).toHaveAttribute('type', 'submit')\nexpect(button).not.toHaveAttribute('type', 'button')\n\nexpect(button).toHaveAttribute('type', expect.stringContaining('sub'))\nexpect(button).toHaveAttribute('type', expect.not.stringContaining('but'))\n```\n\n<hr />\n\n### `toHaveClass`\n\n```typescript\ntoHaveClass(...classNames: string[], options?: {exact: boolean})\n```\n\nThis allows you to check whether the given element has certain classes within\nits `class` attribute. You must provide at least one class, unless you are\nasserting that an element does not have any classes.\n\nThe list of class names may include strings and regular expressions. Regular\nexpressions are matched against each individual class in the target element, and\nit is NOT matched against its full `class` attribute value as whole.\n\n#### Examples\n\n```html\n<button data-testid=\"delete-button\" class=\"btn extra btn-danger\">\n  Delete item\n</button>\n<button data-testid=\"no-classes\">No Classes</button>\n```\n\n```javascript\nconst deleteButton = getByTestId('delete-button')\nconst noClasses = getByTestId('no-classes')\n\nexpect(deleteButton).toHaveClass('extra')\nexpect(deleteButton).toHaveClass('btn-danger btn')\nexpect(deleteButton).toHaveClass(/danger/, 'btn')\nexpect(deleteButton).toHaveClass('btn-danger', 'btn')\nexpect(deleteButton).not.toHaveClass('btn-link')\nexpect(deleteButton).not.toHaveClass(/link/)\nexpect(deleteButton).not.toHaveClass(/btn extra/) // It does not match\n\nexpect(deleteButton).toHaveClass('btn-danger extra btn', {exact: true}) // to check if the element has EXACTLY a set of classes\nexpect(deleteButton).not.toHaveClass('btn-danger extra', {exact: true}) // if it has more than expected it is going to fail\n\nexpect(noClasses).not.toHaveClass()\n```\n\n<hr />\n\n### `toHaveFocus`\n\n```typescript\ntoHaveFocus()\n```\n\nThis allows you to assert whether an element has focus or not.\n\n#### Examples\n\n```html\n<div><input type=\"text\" data-testid=\"element-to-focus\" /></div>\n```\n\n```javascript\nconst input = getByTestId('element-to-focus')\n\ninput.focus()\nexpect(input).toHaveFocus()\n\ninput.blur()\nexpect(input).not.toHaveFocus()\n```\n\n<hr />\n\n### `toHaveFormValues`\n\n```typescript\ntoHaveFormValues(expectedValues: {\n  [name: string]: any\n})\n```\n\nThis allows you to check if a form or fieldset contains form controls for each\ngiven name, and having the specified value.\n\n> It is important to stress that this matcher can only be invoked on a [form][]\n> or a [fieldset][] element.\n>\n> This allows it to take advantage of the [.elements][] property in `form` and\n> `fieldset` to reliably fetch all form controls within them.\n>\n> This also avoids the possibility that users provide a container that contains\n> more than one `form`, thereby intermixing form controls that are not related,\n> and could even conflict with one another.\n\nThis matcher abstracts away the particularities with which a form control value\nis obtained depending on the type of form control. For instance, `<input>`\nelements have a `value` attribute, but `<select>` elements do not. Here's a list\nof all cases covered:\n\n- `<input type=\"number\">` elements return the value as a **number**, instead of\n  a string.\n- `<input type=\"checkbox\">` elements:\n  - if there's a single one with the given `name` attribute, it is treated as a\n    **boolean**, returning `true` if the checkbox is checked, `false` if\n    unchecked.\n  - if there's more than one checkbox with the same `name` attribute, they are\n    all treated collectively as a single form control, which returns the value\n    as an **array** containing all the values of the selected checkboxes in the\n    collection.\n- `<input type=\"radio\">` elements are all grouped by the `name` attribute, and\n  such a group treated as a single form control. This form control returns the\n  value as a **string** corresponding to the `value` attribute of the selected\n  radio button within the group.\n- `<input type=\"text\">` elements return the value as a **string**. This also\n  applies to `<input>` elements having any other possible `type` attribute\n  that's not explicitly covered in different rules above (e.g. `search`,\n  `email`, `date`, `password`, `hidden`, etc.)\n- `<select>` elements without the `multiple` attribute return the value as a\n  **string** corresponding to the `value` attribute of the selected `option`, or\n  `undefined` if there's no selected option.\n- `<select multiple>` elements return the value as an **array** containing all\n  the values of the [selected options][].\n- `<textarea>` elements return their value as a **string**. The value\n  corresponds to their node content.\n\nThe above rules make it easy, for instance, to switch from using a single select\ncontrol to using a group of radio buttons. Or to switch from a multi select\ncontrol, to using a group of checkboxes. The resulting set of form values used\nby this matcher to compare against would be the same.\n\n[selected options]:\n  https://developer.mozilla.org/en-US/docs/Web/API/HTMLSelectElement/selectedOptions\n[form]: https://developer.mozilla.org/en-US/docs/Web/API/HTMLFormElement\n[fieldset]: https://developer.mozilla.org/en-US/docs/Web/API/HTMLFieldSetElement\n[.elements]:\n  https://developer.mozilla.org/en-US/docs/Web/API/HTMLFormElement/elements\n\n#### Examples\n\n```html\n<form data-testid=\"login-form\">\n  <input type=\"text\" name=\"username\" value=\"jane.doe\" />\n  <input type=\"password\" name=\"password\" value=\"12345678\" />\n  <input type=\"checkbox\" name=\"rememberMe\" checked />\n  <button type=\"submit\">Sign in</button>\n</form>\n```\n\n```javascript\nexpect(getByTestId('login-form')).toHaveFormValues({\n  username: 'jane.doe',\n  rememberMe: true,\n})\n```\n\n<hr />\n\n### `toHaveStyle`\n\n```typescript\ntoHaveStyle(css: string | object)\n```\n\nThis allows you to check if a certain element has some specific css properties\nwith specific values applied. It matches only if the element has _all_ the\nexpected properties applied, not just some of them.\n\n#### Examples\n\n```html\n<button\n  data-testid=\"delete-button\"\n  style=\"display: none; background-color: red\"\n>\n  Delete item\n</button>\n```\n\n```javascript\nconst button = getByTestId('delete-button')\n\nexpect(button).toHaveStyle('display: none')\nexpect(button).toHaveStyle({display: 'none'})\nexpect(button).toHaveStyle(`\n  background-color: red;\n  display: none;\n`)\nexpect(button).toHaveStyle({\n  backgroundColor: 'red',\n  display: 'none',\n})\nexpect(button).not.toHaveStyle(`\n  background-color: blue;\n  display: none;\n`)\nexpect(button).not.toHaveStyle({\n  backgroundColor: 'blue',\n  display: 'none',\n})\n```\n\nThis also works with rules that are applied to the element via a class name for\nwhich some rules are defined in a stylesheet currently active in the document.\nThe usual rules of css precedence apply.\n\n<hr />\n\n### `toHaveTextContent`\n\n```typescript\ntoHaveTextContent(text: string | RegExp, options?: {normalizeWhitespace: boolean})\n```\n\nThis allows you to check whether the given node has a text content or not. This\nsupports elements, but also text nodes and fragments.\n\nWhen a `string` argument is passed through, it will perform a partial\ncase-sensitive match to the node content.\n\nTo perform a case-insensitive match, you can use a `RegExp` with the `/i`\nmodifier.\n\nIf you want to match the whole content, you can use a `RegExp` to do it.\n\n#### Examples\n\n```html\n<span data-testid=\"text-content\">Text Content</span>\n```\n\n```javascript\nconst element = getByTestId('text-content')\n\nexpect(element).toHaveTextContent('Content')\nexpect(element).toHaveTextContent(/^Text Content$/) // to match the whole content\nexpect(element).toHaveTextContent(/content$/i) // to use case-insensitive match\nexpect(element).not.toHaveTextContent('content')\n```\n\n<hr />\n\n### `toHaveValue`\n\n```typescript\ntoHaveValue(value: string | string[] | number)\n```\n\nThis allows you to check whether the given form element has the specified value.\nIt accepts `<input>`, `<select>` and `<textarea>` elements with the exception of\n`<input type=\"checkbox\">` and `<input type=\"radio\">`, which can be meaningfully\nmatched only using [`toBeChecked`](#tobechecked) or\n[`toHaveFormValues`](#tohaveformvalues).\n\nIt also accepts elements with roles `meter`, `progressbar`, `slider` or\n`spinbutton` and checks their `aria-valuenow` attribute (as a number).\n\nFor all other form elements, the value is matched using the same algorithm as in\n[`toHaveFormValues`](#tohaveformvalues) does.\n\n#### Examples\n\n```html\n<input type=\"text\" value=\"text\" data-testid=\"input-text\" />\n<input type=\"number\" value=\"5\" data-testid=\"input-number\" />\n<input type=\"text\" data-testid=\"input-empty\" />\n<select multiple data-testid=\"select-number\">\n  <option value=\"first\">First Value</option>\n  <option value=\"second\" selected>Second Value</option>\n  <option value=\"third\" selected>Third Value</option>\n</select>\n```\n\n##### Using DOM Testing Library\n\n```javascript\nconst textInput = getByTestId('input-text')\nconst numberInput = getByTestId('input-number')\nconst emptyInput = getByTestId('input-empty')\nconst selectInput = getByTestId('select-number')\n\nexpect(textInput).toHaveValue('text')\nexpect(numberInput).toHaveValue(5)\nexpect(emptyInput).not.toHaveValue()\nexpect(selectInput).toHaveValue(['second', 'third'])\n```\n\n<hr />\n\n### `toHaveDisplayValue`\n\n```typescript\ntoHaveDisplayValue(value: string | RegExp | (string|RegExp)[])\n```\n\nThis allows you to check whether the given form element has the specified\ndisplayed value (the one the end user will see). It accepts `<input>`,\n`<select>` and `<textarea>` elements with the exception of\n`<input type=\"checkbox\">` and `<input type=\"radio\">`, which can be meaningfully\nmatched only using [`toBeChecked`](#tobechecked) or\n[`toHaveFormValues`](#tohaveformvalues).\n\n#### Examples\n\n```html\n<label for=\"input-example\">First name</label>\n<input type=\"text\" id=\"input-example\" value=\"Luca\" />\n\n<label for=\"textarea-example\">Description</label>\n<textarea id=\"textarea-example\">An example description here.</textarea>\n\n<label for=\"single-select-example\">Fruit</label>\n<select id=\"single-select-example\">\n  <option value=\"\">Select a fruit...</option>\n  <option value=\"banana\">Banana</option>\n  <option value=\"ananas\">Ananas</option>\n  <option value=\"avocado\">Avocado</option>\n</select>\n\n<label for=\"multiple-select-example\">Fruits</label>\n<select id=\"multiple-select-example\" multiple>\n  <option value=\"\">Select a fruit...</option>\n  <option value=\"banana\" selected>Banana</option>\n  <option value=\"ananas\">Ananas</option>\n  <option value=\"avocado\" selected>Avocado</option>\n</select>\n```\n\n##### Using DOM Testing Library\n\n```javascript\nconst input = screen.getByLabelText('First name')\nconst textarea = screen.getByLabelText('Description')\nconst selectSingle = screen.getByLabelText('Fruit')\nconst selectMultiple = screen.getByLabelText('Fruits')\n\nexpect(input).toHaveDisplayValue('Luca')\nexpect(input).toHaveDisplayValue(/Luc/)\nexpect(textarea).toHaveDisplayValue('An example description here.')\nexpect(textarea).toHaveDisplayValue(/example/)\nexpect(selectSingle).toHaveDisplayValue('Select a fruit...')\nexpect(selectSingle).toHaveDisplayValue(/Select/)\nexpect(selectMultiple).toHaveDisplayValue([/Avocado/, 'Banana'])\n```\n\n<hr />\n\n### `toBeChecked`\n\n```typescript\ntoBeChecked()\n```\n\nThis allows you to check whether the given element is checked. It accepts an\n`input` of type `checkbox` or `radio` and elements with a `role` of `checkbox`,\n`radio` or `switch` with a valid `aria-checked` attribute of `\"true\"` or\n`\"false\"`.\n\n#### Examples\n\n```html\n<input type=\"checkbox\" checked data-testid=\"input-checkbox-checked\" />\n<input type=\"checkbox\" data-testid=\"input-checkbox-unchecked\" />\n<div role=\"checkbox\" aria-checked=\"true\" data-testid=\"aria-checkbox-checked\" />\n<div\n  role=\"checkbox\"\n  aria-checked=\"false\"\n  data-testid=\"aria-checkbox-unchecked\"\n/>\n\n<input type=\"radio\" checked value=\"foo\" data-testid=\"input-radio-checked\" />\n<input type=\"radio\" value=\"foo\" data-testid=\"input-radio-unchecked\" />\n<div role=\"radio\" aria-checked=\"true\" data-testid=\"aria-radio-checked\" />\n<div role=\"radio\" aria-checked=\"false\" data-testid=\"aria-radio-unchecked\" />\n<div role=\"switch\" aria-checked=\"true\" data-testid=\"aria-switch-checked\" />\n<div role=\"switch\" aria-checked=\"false\" data-testid=\"aria-switch-unchecked\" />\n```\n\n```javascript\nconst inputCheckboxChecked = getByTestId('input-checkbox-checked')\nconst inputCheckboxUnchecked = getByTestId('input-checkbox-unchecked')\nconst ariaCheckboxChecked = getByTestId('aria-checkbox-checked')\nconst ariaCheckboxUnchecked = getByTestId('aria-checkbox-unchecked')\nexpect(inputCheckboxChecked).toBeChecked()\nexpect(inputCheckboxUnchecked).not.toBeChecked()\nexpect(ariaCheckboxChecked).toBeChecked()\nexpect(ariaCheckboxUnchecked).not.toBeChecked()\n\nconst inputRadioChecked = getByTestId('input-radio-checked')\nconst inputRadioUnchecked = getByTestId('input-radio-unchecked')\nconst ariaRadioChecked = getByTestId('aria-radio-checked')\nconst ariaRadioUnchecked = getByTestId('aria-radio-unchecked')\nexpect(inputRadioChecked).toBeChecked()\nexpect(inputRadioUnchecked).not.toBeChecked()\nexpect(ariaRadioChecked).toBeChecked()\nexpect(ariaRadioUnchecked).not.toBeChecked()\n\nconst ariaSwitchChecked = getByTestId('aria-switch-checked')\nconst ariaSwitchUnchecked = getByTestId('aria-switch-unchecked')\nexpect(ariaSwitchChecked).toBeChecked()\nexpect(ariaSwitchUnchecked).not.toBeChecked()\n```\n\n<hr />\n\n### `toBePartiallyChecked`\n\n```typescript\ntoBePartiallyChecked()\n```\n\nThis allows you to check whether the given element is partially checked. It\naccepts an `input` of type `checkbox` and elements with a `role` of `checkbox`\nwith a `aria-checked=\"mixed\"`, or `input` of type `checkbox` with\n`indeterminate` set to `true`\n\n#### Examples\n\n```html\n<input type=\"checkbox\" aria-checked=\"mixed\" data-testid=\"aria-checkbox-mixed\" />\n<input type=\"checkbox\" checked data-testid=\"input-checkbox-checked\" />\n<input type=\"checkbox\" data-testid=\"input-checkbox-unchecked\" />\n<div role=\"checkbox\" aria-checked=\"true\" data-testid=\"aria-checkbox-checked\" />\n<div\n  role=\"checkbox\"\n  aria-checked=\"false\"\n  data-testid=\"aria-checkbox-unchecked\"\n/>\n<input type=\"checkbox\" data-testid=\"input-checkbox-indeterminate\" />\n```\n\n```javascript\nconst ariaCheckboxMixed = getByTestId('aria-checkbox-mixed')\nconst inputCheckboxChecked = getByTestId('input-checkbox-checked')\nconst inputCheckboxUnchecked = getByTestId('input-checkbox-unchecked')\nconst ariaCheckboxChecked = getByTestId('aria-checkbox-checked')\nconst ariaCheckboxUnchecked = getByTestId('aria-checkbox-unchecked')\nconst inputCheckboxIndeterminate = getByTestId('input-checkbox-indeterminate')\n\nexpect(ariaCheckboxMixed).toBePartiallyChecked()\nexpect(inputCheckboxChecked).not.toBePartiallyChecked()\nexpect(inputCheckboxUnchecked).not.toBePartiallyChecked()\nexpect(ariaCheckboxChecked).not.toBePartiallyChecked()\nexpect(ariaCheckboxUnchecked).not.toBePartiallyChecked()\n\ninputCheckboxIndeterminate.indeterminate = true\nexpect(inputCheckboxIndeterminate).toBePartiallyChecked()\n```\n\n<hr />\n\n### `toHaveRole`\n\nThis allows you to assert that an element has the expected\n[role](https://www.w3.org/TR/html-aria/#docconformance).\n\nThis is useful in cases where you already have access to an element via some\nquery other than the role itself, and want to make additional assertions\nregarding its accessibility.\n\nThe role can match either an explicit role (via the `role` attribute), or an\nimplicit one via the\n[implicit ARIA semantics](https://www.w3.org/TR/html-aria/).\n\nNote: roles are matched literally by string equality, without inheriting from\nthe ARIA role hierarchy. As a result, querying a superclass role like 'checkbox'\nwill not include elements with a subclass role like 'switch'.\n\n```typescript\ntoHaveRole(expectedRole: string)\n```\n\n```html\n<button data-testid=\"button\">Continue</button>\n<div role=\"button\" data-testid=\"button-explicit\">Continue</button>\n<button role=\"switch button\" data-testid=\"button-explicit-multiple\">Continue</button>\n<a href=\"/about\" data-testid=\"link\">About</a>\n<a data-testid=\"link-invalid\">Invalid link<a/>\n```\n\n```javascript\nexpect(getByTestId('button')).toHaveRole('button')\nexpect(getByTestId('button-explicit')).toHaveRole('button')\nexpect(getByTestId('button-explicit-multiple')).toHaveRole('button')\nexpect(getByTestId('button-explicit-multiple')).toHaveRole('switch')\nexpect(getByTestId('link')).toHaveRole('link')\nexpect(getByTestId('link-invalid')).not.toHaveRole('link')\nexpect(getByTestId('link-invalid')).toHaveRole('generic')\n```\n\n<hr />\n\n### `toHaveErrorMessage`\n\n> This custom matcher is deprecated. Prefer\n> [`toHaveAccessibleErrorMessage`](#tohaveaccessibleerrormessage) instead, which\n> is more comprehensive in implementing the official spec.\n\n```typescript\ntoHaveErrorMessage(text: string | RegExp)\n```\n\nThis allows you to check whether the given element has an\n[ARIA error message](https://www.w3.org/TR/wai-aria/#aria-errormessage) or not.\n\nUse the `aria-errormessage` attribute to reference another element that contains\ncustom error message text. Multiple ids is **NOT** allowed. Authors MUST use\n`aria-invalid` in conjunction with `aria-errormessage`. Learn more from\n[`aria-errormessage` spec](https://www.w3.org/TR/wai-aria/#aria-errormessage).\n\nWhitespace is normalized.\n\nWhen a `string` argument is passed through, it will perform a whole\ncase-sensitive match to the error message text.\n\nTo perform a case-insensitive match, you can use a `RegExp` with the `/i`\nmodifier.\n\nTo perform a partial match, you can pass a `RegExp` or use\n`expect.stringContaining(\"partial string\")`.\n\n#### Examples\n\n```html\n<label for=\"startTime\"> Please enter a start time for the meeting: </label>\n<input\n  id=\"startTime\"\n  type=\"text\"\n  aria-errormessage=\"msgID\"\n  aria-invalid=\"true\"\n  value=\"11:30 PM\"\n/>\n<span id=\"msgID\" aria-live=\"assertive\" style=\"visibility:visible\">\n  Invalid time: the time must be between 9:00 AM and 5:00 PM\n</span>\n```\n\n```javascript\nconst timeInput = getByLabel('startTime')\n\nexpect(timeInput).toHaveErrorMessage(\n  'Invalid time: the time must be between 9:00 AM and 5:00 PM',\n)\nexpect(timeInput).toHaveErrorMessage(/invalid time/i) // to partially match\nexpect(timeInput).toHaveErrorMessage(expect.stringContaining('Invalid time')) // to partially match\nexpect(timeInput).not.toHaveErrorMessage('Pikachu!')\n```\n\n## Deprecated matchers\n\n### `toBeEmpty`\n\n> Note: This matcher is being deprecated due to a name clash with\n> `jest-extended`. See more info in #216. In the future, please use only\n> [`toBeEmptyDOMElement`](#toBeEmptyDOMElement)\n\n```typescript\ntoBeEmpty()\n```\n\nThis allows you to assert whether an element has content or not.\n\n#### Examples\n\n```html\n<span data-testid=\"not-empty\"><span data-testid=\"empty\"></span></span>\n```\n\n```javascript\nexpect(getByTestId('empty')).toBeEmpty()\nexpect(getByTestId('not-empty')).not.toBeEmpty()\n```\n\n<hr />\n\n### `toBeInTheDOM`\n\n> This custom matcher is deprecated. Prefer\n> [`toBeInTheDocument`](#tobeinthedocument) instead.\n\n```typescript\ntoBeInTheDOM()\n```\n\nThis allows you to check whether a value is a DOM element, or not.\n\nContrary to what its name implies, this matcher only checks that you passed to\nit a valid DOM element. It does not have a clear definition of what \"the DOM\"\nis. Therefore, it does not check whether that element is contained anywhere.\n\nThis is the main reason why this matcher is deprecated, and will be removed in\nthe next major release. You can follow the discussion around this decision in\nmore detail [here](https://github.com/testing-library/jest-dom/issues/34).\n\nAs an alternative, you can use [`toBeInTheDocument`](#tobeinthedocument) or\n[`toContainElement`](#tocontainelement). Or if you just want to check if a value\nis indeed an `HTMLElement` you can always use some of\n[jest's built-in matchers](https://jestjs.io/docs/en/expect#tobeinstanceofclass):\n\n```js\nexpect(document.querySelector('.ok-button')).toBeInstanceOf(HTMLElement)\nexpect(document.querySelector('.cancel-button')).toBeTruthy()\n```\n\n> Note: The differences between `toBeInTheDOM` and `toBeInTheDocument` are\n> significant. Replacing all uses of `toBeInTheDOM` with `toBeInTheDocument`\n> will likely cause unintended consequences in your tests. Please make sure when\n> replacing `toBeInTheDOM` to read through the documentation of the proposed\n> alternatives to see which use case works better for your needs.\n\n<hr />\n\n### `toHaveDescription`\n\n> This custom matcher is deprecated. Prefer\n> [`toHaveAccessibleDescription`](#tohaveaccessibledescription) instead, which\n> is more comprehensive in implementing the official spec.\n\n```typescript\ntoHaveDescription(text: string | RegExp)\n```\n\nThis allows you to check whether the given element has a description or not.\n\nAn element gets its description via the\n[`aria-describedby` attribute](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/ARIA_Techniques/Using_the_aria-describedby_attribute).\nSet this to the `id` of one or more other elements. These elements may be nested\ninside, be outside, or a sibling of the passed in element.\n\nWhitespace is normalized. Using multiple ids will\n[join the referenced elements’ text content separated by a space](https://www.w3.org/TR/accname-1.1/#mapping_additional_nd_description).\n\nWhen a `string` argument is passed through, it will perform a whole\ncase-sensitive match to the description text.\n\nTo perform a case-insensitive match, you can use a `RegExp` with the `/i`\nmodifier.\n\nTo perform a partial match, you can pass a `RegExp` or use\n`expect.stringContaining(\"partial string\")`.\n\n#### Examples\n\n```html\n<button aria-label=\"Close\" aria-describedby=\"description-close\">X</button>\n<div id=\"description-close\">Closing will discard any changes</div>\n\n<button>Delete</button>\n```\n\n```javascript\nconst closeButton = getByRole('button', {name: 'Close'})\n\nexpect(closeButton).toHaveDescription('Closing will discard any changes')\nexpect(closeButton).toHaveDescription(/will discard/) // to partially match\nexpect(closeButton).toHaveDescription(expect.stringContaining('will discard')) // to partially match\nexpect(closeButton).toHaveDescription(/^closing/i) // to use case-insensitive match\nexpect(closeButton).not.toHaveDescription('Other description')\n\nconst deleteButton = getByRole('button', {name: 'Delete'})\nexpect(deleteButton).not.toHaveDescription()\nexpect(deleteButton).toHaveDescription('') // Missing or empty description always becomes a blank string\n```\n\n<hr />\n\n### `toHaveSelection`\n\nThis allows to assert that an element has a\n[text selection](https://developer.mozilla.org/en-US/docs/Web/API/Selection).\n\nThis is useful to check if text or part of the text is selected within an\nelement. The element can be either an input of type text, a textarea, or any\nother element that contains text, such as a paragraph, span, div etc.\n\nNOTE: the expected selection is a string, it does not allow to check for\nselection range indeces.\n\n```typescript\ntoHaveSelection(expectedSelection?: string)\n```\n\n```html\n<div>\n  <input type=\"text\" value=\"text selected text\" data-testid=\"text\" />\n  <textarea data-testid=\"textarea\">text selected text</textarea>\n  <p data-testid=\"prev\">prev</p>\n  <p data-testid=\"parent\">\n    text <span data-testid=\"child\">selected</span> text\n  </p>\n  <p data-testid=\"next\">next</p>\n</div>\n```\n\n```javascript\ngetByTestId('text').setSelectionRange(5, 13)\nexpect(getByTestId('text')).toHaveSelection('selected')\n\ngetByTestId('textarea').setSelectionRange(0, 5)\nexpect('textarea').toHaveSelection('text ')\n\nconst selection = document.getSelection()\nconst range = document.createRange()\nselection.removeAllRanges()\nselection.empty()\nselection.addRange(range)\n\n// selection of child applies to the parent as well\nrange.selectNodeContents(getByTestId('child'))\nexpect(getByTestId('child')).toHaveSelection('selected')\nexpect(getByTestId('parent')).toHaveSelection('selected')\n\n// selection that applies from prev all, parent text before child, and part child.\nrange.setStart(getByTestId('prev'), 0)\nrange.setEnd(getByTestId('child').childNodes[0], 3)\nexpect(queryByTestId('prev')).toHaveSelection('prev')\nexpect(queryByTestId('child')).toHaveSelection('sel')\nexpect(queryByTestId('parent')).toHaveSelection('text sel')\nexpect(queryByTestId('next')).not.toHaveSelection()\n\n// selection that applies from part child, parent text after child and part next.\nrange.setStart(getByTestId('child').childNodes[0], 3)\nrange.setEnd(getByTestId('next').childNodes[0], 2)\nexpect(queryByTestId('child')).toHaveSelection('ected')\nexpect(queryByTestId('parent')).toHaveSelection('ected text')\nexpect(queryByTestId('prev')).not.toHaveSelection()\nexpect(queryByTestId('next')).toHaveSelection('ne')\n```\n\n## Inspiration\n\nThis whole library was extracted out of Kent C. Dodds' [DOM Testing\nLibrary][dom-testing-library], which was in turn extracted out of [React Testing\nLibrary][react-testing-library].\n\nThe intention is to make this available to be used independently of these other\nlibraries, and also to make it more clear that these other libraries are\nindependent from jest, and can be used with other tests runners as well.\n\n## Other Solutions\n\nI'm not aware of any, if you are please [make a pull request][prs] and add it\nhere!\n\nIf you would like to further test the accessibility and validity of the DOM\nconsider [`jest-axe`](https://github.com/nickcolley/jest-axe). It doesn't\noverlap with `jest-dom` but can complement it for more in-depth accessibility\nchecking (eg: validating `aria` attributes or ensuring unique id attributes).\n\n## Guiding Principles\n\n> [The more your tests resemble the way your software is used, the more\n> confidence they can give you.][guiding-principle]\n\nThis library follows the same guiding principles as its mother library [DOM\nTesting Library][dom-testing-library]. Go [check them out][guiding-principle]\nfor more details.\n\nAdditionally, with respect to custom DOM matchers, this library aims to maintain\na minimal but useful set of them, while avoiding bloating itself with merely\nconvenient ones that can be easily achieved with other APIs. In general, the\noverall criteria for what is considered a useful custom matcher to add to this\nlibrary, is that doing the equivalent assertion on our own makes the test code\nmore verbose, less clear in its intent, and/or harder to read.\n\n## Contributors\n\nThanks goes to these people ([emoji key][emojis]):\n\n<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://kentcdodds.com\"><img src=\"https://avatars.githubusercontent.com/u/1500684?v=3?s=100\" width=\"100px;\" alt=\"Kent C. Dodds\"/><br /><sub><b>Kent C. Dodds</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=kentcdodds\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=kentcdodds\" title=\"Documentation\">📖</a> <a href=\"#infra-kentcdodds\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=kentcdodds\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://audiolion.github.io\"><img src=\"https://avatars1.githubusercontent.com/u/2430381?v=4?s=100\" width=\"100px;\" alt=\"Ryan Castner\"/><br /><sub><b>Ryan Castner</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=audiolion\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://www.dnlsandiego.com\"><img src=\"https://avatars0.githubusercontent.com/u/8008023?v=4?s=100\" width=\"100px;\" alt=\"Daniel Sandiego\"/><br /><sub><b>Daniel Sandiego</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=dnlsandiego\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/Miklet\"><img src=\"https://avatars2.githubusercontent.com/u/12592677?v=4?s=100\" width=\"100px;\" alt=\"Paweł Mikołajczyk\"/><br /><sub><b>Paweł Mikołajczyk</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=Miklet\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://co.linkedin.com/in/alejandronanez/\"><img src=\"https://avatars3.githubusercontent.com/u/464978?v=4?s=100\" width=\"100px;\" alt=\"Alejandro Ñáñez Ortiz\"/><br /><sub><b>Alejandro Ñáñez Ortiz</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=alejandronanez\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/pbomb\"><img src=\"https://avatars0.githubusercontent.com/u/1402095?v=4?s=100\" width=\"100px;\" alt=\"Matt Parrish\"/><br /><sub><b>Matt Parrish</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Apbomb\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=pbomb\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=pbomb\" title=\"Documentation\">📖</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=pbomb\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/wKovacs64\"><img src=\"https://avatars1.githubusercontent.com/u/1288694?v=4?s=100\" width=\"100px;\" alt=\"Justin Hall\"/><br /><sub><b>Justin Hall</b></sub></a><br /><a href=\"#platform-wKovacs64\" title=\"Packaging/porting to new platform\">📦</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/antoaravinth\"><img src=\"https://avatars1.githubusercontent.com/u/1241511?s=460&v=4?s=100\" width=\"100px;\" alt=\"Anto Aravinth\"/><br /><sub><b>Anto Aravinth</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=antoaravinth\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=antoaravinth\" title=\"Tests\">⚠️</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=antoaravinth\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/JonahMoses\"><img src=\"https://avatars2.githubusercontent.com/u/3462296?v=4?s=100\" width=\"100px;\" alt=\"Jonah Moses\"/><br /><sub><b>Jonah Moses</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=JonahMoses\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://team.thebrain.pro\"><img src=\"https://avatars1.githubusercontent.com/u/4002543?v=4?s=100\" width=\"100px;\" alt=\"Łukasz Gandecki\"/><br /><sub><b>Łukasz Gandecki</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=lgandecki\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=lgandecki\" title=\"Tests\">⚠️</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=lgandecki\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://sompylasar.github.io\"><img src=\"https://avatars2.githubusercontent.com/u/498274?v=4?s=100\" width=\"100px;\" alt=\"Ivan Babak\"/><br /><sub><b>Ivan Babak</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Asompylasar\" title=\"Bug reports\">🐛</a> <a href=\"#ideas-sompylasar\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/jday3\"><img src=\"https://avatars3.githubusercontent.com/u/4439618?v=4?s=100\" width=\"100px;\" alt=\"Jesse Day\"/><br /><sub><b>Jesse Day</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=jday3\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://gnapse.github.io\"><img src=\"https://avatars0.githubusercontent.com/u/15199?v=4?s=100\" width=\"100px;\" alt=\"Ernesto García\"/><br /><sub><b>Ernesto García</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=gnapse\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=gnapse\" title=\"Documentation\">📖</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=gnapse\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://ociweb.com/mark/\"><img src=\"https://avatars0.githubusercontent.com/u/79312?v=4?s=100\" width=\"100px;\" alt=\"Mark Volkmann\"/><br /><sub><b>Mark Volkmann</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Amvolkmann\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=mvolkmann\" title=\"Code\">💻</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/smacpherson64\"><img src=\"https://avatars1.githubusercontent.com/u/1659099?v=4?s=100\" width=\"100px;\" alt=\"smacpherson64\"/><br /><sub><b>smacpherson64</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=smacpherson64\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=smacpherson64\" title=\"Documentation\">📖</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=smacpherson64\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/jgoz\"><img src=\"https://avatars2.githubusercontent.com/u/132233?v=4?s=100\" width=\"100px;\" alt=\"John Gozde\"/><br /><sub><b>John Gozde</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Ajgoz\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=jgoz\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/callada\"><img src=\"https://avatars2.githubusercontent.com/u/7830590?v=4?s=100\" width=\"100px;\" alt=\"Iwona\"/><br /><sub><b>Iwona</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=callada\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=callada\" title=\"Documentation\">📖</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=callada\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/6ewis\"><img src=\"https://avatars0.githubusercontent.com/u/840609?v=4?s=100\" width=\"100px;\" alt=\"Lewis\"/><br /><sub><b>Lewis</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=6ewis\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://blog.lourenci.com/\"><img src=\"https://avatars3.githubusercontent.com/u/2339362?v=4?s=100\" width=\"100px;\" alt=\"Leandro Lourenci\"/><br /><sub><b>Leandro Lourenci</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Alourenci\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=lourenci\" title=\"Documentation\">📖</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=lourenci\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=lourenci\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/mufasa71\"><img src=\"https://avatars1.githubusercontent.com/u/626420?v=4?s=100\" width=\"100px;\" alt=\"Shukhrat Mukimov\"/><br /><sub><b>Shukhrat Mukimov</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Amufasa71\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/dreyks\"><img src=\"https://avatars3.githubusercontent.com/u/1481264?v=4?s=100\" width=\"100px;\" alt=\"Roman Usherenko\"/><br /><sub><b>Roman Usherenko</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=dreyks\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=dreyks\" title=\"Tests\">⚠️</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://josephhsu.com\"><img src=\"https://avatars1.githubusercontent.com/u/648?v=4?s=100\" width=\"100px;\" alt=\"Joe Hsu\"/><br /><sub><b>Joe Hsu</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=jhsu\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://twitter.com/diegohaz\"><img src=\"https://avatars3.githubusercontent.com/u/3068563?v=4?s=100\" width=\"100px;\" alt=\"Haz\"/><br /><sub><b>Haz</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Adiegohaz\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=diegohaz\" title=\"Code\">💻</a> <a href=\"#ideas-diegohaz\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://blog.revathskumar.com\"><img src=\"https://avatars3.githubusercontent.com/u/463904?v=4?s=100\" width=\"100px;\" alt=\"Revath S Kumar\"/><br /><sub><b>Revath S Kumar</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=revathskumar\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://raccoon.studio\"><img src=\"https://avatars0.githubusercontent.com/u/4989733?v=4?s=100\" width=\"100px;\" alt=\"hiwelo.\"/><br /><sub><b>hiwelo.</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=hiwelo\" title=\"Code\">💻</a> <a href=\"#ideas-hiwelo\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=hiwelo\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/lukaszfiszer\"><img src=\"https://avatars3.githubusercontent.com/u/1201711?v=4?s=100\" width=\"100px;\" alt=\"Łukasz Fiszer\"/><br /><sub><b>Łukasz Fiszer</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=lukaszfiszer\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/jeanchung\"><img src=\"https://avatars0.githubusercontent.com/u/10778036?v=4?s=100\" width=\"100px;\" alt=\"Jean Chung\"/><br /><sub><b>Jean Chung</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=jeanchung\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=jeanchung\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/CarlaTeo\"><img src=\"https://avatars3.githubusercontent.com/u/9220147?v=4?s=100\" width=\"100px;\" alt=\"CarlaTeo\"/><br /><sub><b>CarlaTeo</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=CarlaTeo\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=CarlaTeo\" title=\"Tests\">⚠️</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/YardenShoham\"><img src=\"https://avatars3.githubusercontent.com/u/20454870?v=4?s=100\" width=\"100px;\" alt=\"Yarden Shoham\"/><br /><sub><b>Yarden Shoham</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=YardenShoham\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://jagascript.com\"><img src=\"https://avatars0.githubusercontent.com/u/4562878?v=4?s=100\" width=\"100px;\" alt=\"Jaga Santagostino\"/><br /><sub><b>Jaga Santagostino</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/issues?q=author%3Akandros\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=kandros\" title=\"Tests\">⚠️</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=kandros\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/connormeredith\"><img src=\"https://avatars0.githubusercontent.com/u/4907463?v=4?s=100\" width=\"100px;\" alt=\"Connor Meredith\"/><br /><sub><b>Connor Meredith</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=connormeredith\" title=\"Code\">💻</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=connormeredith\" title=\"Tests\">⚠️</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=connormeredith\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/pwolaq\"><img src=\"https://avatars3.githubusercontent.com/u/10261750?v=4?s=100\" width=\"100px;\" alt=\"Pawel Wolak\"/><br /><sub><b>Pawel Wolak</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=pwolaq\" title=\"Tests\">⚠️</a> <a href=\"https://github.com/testing-library/jest-dom/commits?author=pwolaq\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://michaeldeboey.be\"><img src=\"https://avatars3.githubusercontent.com/u/6643991?v=4?s=100\" width=\"100px;\" alt=\"Michaël De Boey\"/><br /><sub><b>Michaël De Boey</b></sub></a><br /><a href=\"#infra-MichaelDeBoey\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/jzarzeckis\"><img src=\"https://avatars3.githubusercontent.com/u/919350?v=4?s=100\" width=\"100px;\" alt=\"Jānis Zaržeckis\"/><br /><sub><b>Jānis Zaržeckis</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=jzarzeckis\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/koala-lava\"><img src=\"https://avatars0.githubusercontent.com/u/15828770?v=4?s=100\" width=\"100px;\" alt=\"koala-lava\"/><br /><sub><b>koala-lava</b></sub></a><br /><a href=\"https://github.com/testing-library/jest-dom/commits?author=koala-lava\" title=\"Documentation\">📖</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://jpblanco.dev\"><img src=\"https://avatars1.githubusercontent.com/u/16567863?v=4?s=100\" width=\"", "readmeFilename": "README.md", "users": {"bcowgi11": true}}