{"_id": "@babel/helper-optimise-call-expression", "_rev": "121-c0f2d2251a97c9585776863e72c5de61", "name": "@babel/helper-optimise-call-expression", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.4", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4b0c58d25a3ff70c4ed2bbdad79255e9b6920a57", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.4.tgz", "integrity": "sha512-Ojx2xLUqv9igmmNHIh6+xZBA7sZh0OLiPVeUnWK2K6h0ifmEvDIIu8HpX13tpCIo/txsxo6lrosbEtEISWB+Nw==", "signatures": [{"sig": "MEUCIQCfQgBPEtGKTIpfPqXLnQCAEzwG/7G/VIJ3oRqjviEp3gIgQT8Aob/vvei3AkuoC7O0LadVyGaIc5I5+FG6jzBmusg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.4.tgz_1509388505672_0.6145376458298415", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.5", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d0a91482ed82291b8bfe5c2965791eaf2035e035", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.5.tgz", "integrity": "sha512-UIwLrMAdsUIeyn3i+IoKc2gA84v/etZF3pvZ1usnvfaucb5VvBRx0IgIgHsBiPDLcmYV+jJ3Jjmq3PeI/mZ1jQ==", "signatures": [{"sig": "MEYCIQD/OMO3PhyoLDY85n0/o9ZIpzed/tbCj+Bzp4NwuwQ0lQIhAN19CvKwCXQCJYNItKhIbdC2Z+9Xi1gJACkJOY8mQood", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.5.tgz_1509397005125_0.4100940970238298", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.31", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d570d784e13a692e90bc3eb03fd9102f85e962e0", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.31.tgz", "integrity": "sha512-0H42RkczJxG/BV+zP18E9Evjs5V6/yQQuTQq+no2fQL+bPRozSN8n2ezuPStHVOOGYhqYDDzfEYzuQxpSm9Y6w==", "signatures": [{"sig": "MEUCIFZcP0zYejpA/jEle6VwLP7bOZxQDOswjUErSxPJpvQdAiEAnXTdxcjBGjbSN10Aqd7dNAQ5xP/AxC+p7mTuw7yDuDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.31.tgz_1509739422301_0.347364824032411", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.32", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5851ce4e49d08f360cdc079b03dd23c376eb76d4", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.32.tgz", "integrity": "sha512-7Y6U6biSYJekqXfp3Tv7kbNcZoO5HH7utmUkcpQzBonV8bvp3VyEnJHHsNgz5iJo2OEh7Tdzwapt88Z0j7+0dQ==", "signatures": [{"sig": "MEQCIA/ayN8FUKanYPokM2+dYWoIU4JAhUuG9mzPAuRCl/DZAiBMnl4nS8ee9IlkLoYpgFzKHlcrp+6H+RVr+JKdHosuXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.32.tgz_1510493613603_0.2241504560224712", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.33", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1df983fac8ad576191265128ccd0bf193b176719", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.33.tgz", "integrity": "sha512-9s/h77OFuyofsiLs5qSxMumJ+oANrm8lVISd8GC0qb2gwwRfvc54e9AAnR8TIHOfHAdWg0ITM7INpdS8w3sb9A==", "signatures": [{"sig": "MEUCIEopocBn4+t6o8s/a7qVULiOxqzmJoKOu9P3lyTKNhCPAiEA8d5WG0jjpfp9kUIrnI+O1XV5352WWLo70PQiuwq17vI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.33.tgz_1512138520027_0.8093233467079699", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.34", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d792d2a806441b4b6d9af66513e5ef2c366104b5", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.34.tgz", "integrity": "sha512-amewaBxr7Sv/O+CtdQB1kjewMNOwl5C54W0nNuDsvj7Ria1xsyj5UHuA9Z432Hn9pAC6+BJwh0/dxqcBVOC3AQ==", "signatures": [{"sig": "MEUCIECLBw50qqTrZn3uMFJoQoC/ad/Gf78ObyULUVhahZxHAiEAmYmSKdXGlcHgaP/4zZiHdKYXy/P+GwG4/Xts9FPm9SY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.34.tgz_1512225580663_0.38233865424990654", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.35", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3adc8c5347b4a8d4ef8b117d99535f6fa3b61a71", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.35.tgz", "integrity": "sha512-hr/P3XTAtN5wppGLP4yrOUbvIyOQPmEG6EVsCSE5z0yUueNQzuCxXp0v7sx7/V+c0eP3XLy/lVsuM96cS3VUKQ==", "signatures": [{"sig": "MEUCIBMLS+CcfYfE2D5Zc3CPWmKVYQmXL2tj8U/6KnPqfAHuAiEA8WCtLcxpsYEeSsdS/plL6vgEa+J2JE6CnKuUfu+a1/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.35.tgz_1513288082149_0.11993686622008681", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.36", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a35f3ce99e20dc9cda7e23e24530cd0adb33ed2f", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.36.tgz", "integrity": "sha512-xKqC//WIWVzgN4Z7yNvA/i/F2jEHfpu9P1PVxW0+1TREi80OSSu92c0V6lh+wASngFpbpBabZZiRFfX5FD3DNQ==", "signatures": [{"sig": "MEQCIA7BYGdtgjc+uV3pV5QyhMJi35UMfkMcUr0MtNWj8gDhAiAg3YniAp0A1sydn5sOz7sDVkKo7bXi/kCnWd/Nt+lfrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.36.tgz_1514228700528_0.22272413223981857", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.37", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6879401a6da76ac70d9ab125d394c12ac09c0eb5", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.37.tgz", "integrity": "sha512-cJjZ218M0r45GzM/JD8j0T4ZUXEvrEihIEabSbKFjj+l9sXA9oDa/j8uCqViuMrSYjZZa7rZrJsyUlHBLb3/Kg==", "signatures": [{"sig": "MEYCIQChL932JNoZY0AA9s5YuahyNV8+riiK7XTOWt0vvXYV3wIhAIP/txtw8DhXsfs0rh6YxpKaYVVVpJP7ZLUyQeL6Y6bE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.37.tgz_1515427363407_0.9061883888207376", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.38", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b71cd35565f3396a7a122ee1d6bf04444e864406", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.38.tgz", "integrity": "sha512-BK5wW2bh2TEFhYkXdmB4TIF75dx3OlBZSVVMbzeA9zW/lJrvnQS2a+vm8xmZI6B4aQ5myuAuwzM37Ai29EKhnw==", "signatures": [{"sig": "MEUCIQCdBdAXAjVjKBwcKc3Jm1UXUPvjaKVmo8y/Lv2totkwagIgMPIGCFzsOXfyonTyBXQlm+mDp4lvmzWeRHfWl4e3CzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.38.tgz_1516206731843_0.8081992205698043", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.39", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2f2c76665fb9128feb0b84a162b3dcaecc53a102", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.39.tgz", "integrity": "sha512-Ji9ppOl59Vt5n2cMJdzIz5rw3wzHkd7zUti+kSNPx6ySw3eQLAI2mxHUd/Z2qArqtOGBsfHG5xTwOIMumsLRyQ==", "signatures": [{"sig": "MEYCIQD/lk40x7urDmy1lkAkm8FLQqWEQ/VRh0TJDA4rduVHZAIhAOrRE2nHDhi/Uro/jw9/KayuRf/epxxmmH9iUXnMYySW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression-7.0.0-beta.39.tgz_1517344064946_0.7159676898736507", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.40", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f0e7f70d455bff8ab6a248a84f0221098fa468ac", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-2f4ZKEkvdnKiTUA/Nhju+oEoRcyHcpf6lFuQI5cxbo1Toxqa8E9HBO5tiOWwlIwuak7RZPYSnxnrJQy/0d4YUw==", "signatures": [{"sig": "MEUCIG1IYd50F8iPHc8C9Fk6yRpLJROtimmM8/N3fpRHeaOpAiEA8OxTaxt1eTKkfn7i+fSOOkpgB8wTUcckYy6DrZGtmlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1422}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.40_1518453717023_0.046521134010879095", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.41", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4e2df61f5a0900ef113a3b861253136593d47f3d", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-da1uUVXUcGvgI0YhnTo2HxXKMEofei+n1/6OFP/rTvfa3+2SHm77E+RzbbnpSm+61u270hk7FC9jxhF7n9IBDA==", "signatures": [{"sig": "MEUCIAkoYIOZaYrFJT3GxvK3fZ9tiTTAuFJg8xqq1+w9VMITAiEArvRA1E+Jfr5VtrPRNm9FlV2LHl+M0WYTMF6tDrLGwM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1422}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.41_1521044745376_0.1749764348755003", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.42", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9ba770079001672a578fe833190cf03f973568b1", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-4Sssg3iFnLH/1fZQFCPNJ7ISZzrRwq/X8/T5OaURGP3NMVTR4mnEUqrc3v8/SfL3pfa57q3Fe4zIC2h7FuPkww==", "signatures": [{"sig": "MEUCIQDcm8xFCoUuMFo9zbDFGhhMjIBGMfnouySWp9xP9O3z4gIgZtXkyU2Ob3badwUq8lyIKKMoYgVREV1lyWL5022eziE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1422}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.42_1521147019401_0.35268109956880545", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.43", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "046a3c719e392ac12fae1b89001757c2578509f8", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-59zZu+EQYT3FGyYFWNYMB64KQIUH9hbSU7M7btpRQhzxfm+oDkjNtHL8cmeRkmWLiYHJM638fk9axg8cKiHN/g==", "signatures": [{"sig": "MEUCIQCjN0owwisySIWmlxHEDwrNHZgPPXaSkHIpfxe4h7TI9wIgSQ2jSY1r72ETcNs1eLiOV4/ZQ+Hug0Sl2+OoKaL69Uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1555}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.43_1522687689194_0.20782710787562486", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.44", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "84ceabfb99afc1c185d15668114a697cdad7a5d0", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-A9JodnSG8mNiazppInvrAtw4rN+TW61He/EnZ9szVgWkivonZeuG2D0hVLpE5sYuqw128seyw+tY9NEY7txmcQ==", "signatures": [{"sig": "MEUCIB4pis+NikB1l+3EDoxQT9Pqi9kcT9sB1ImabrnnjZ+aAiEAyzBS3ORAd+4THbq6JQzeVfzypO+Y+og3bW3z5HMZORw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1558}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.44_1522707590719_0.5217032280535092", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.45", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "360549b55b87e77cb275cf321bee0bb94d0d9123", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-kvH1gZ7Ap6aL4A6lUKPNOsfUgAmP9LFfJPGnpVdKD8kTRlstFo+f1c5n5C+1uZJdp0iwaWUAslC3+VrlgV6rbg==", "signatures": [{"sig": "MEQCIHHo3YpQH6t0ED7T6KU2S6JKg3r61I3MNhrTQ/kiuC2iAiBnLf9r9R5PcgEaU952lGV8TubL9Ab9LSwu9DxGvqWVSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0qCRA9TVsSAnZWagAAX68QAKSFE59SvS6ACSxwa4Ef\nfjX8Z40nccKziDsBFgHsqYrzD2DdQkIh9S2eSloeKWvq3YduLXDrIfjP+emJ\n4JPgnKS14BE0k2BD0jYqHhbEUBKmmQ1HP7ORHX0/iBScK2EIvD5fwmam1KRF\nGlL8bUPrzvdWnyWQckBtZ5fqMKxtmi4Eqni33E1GVbXjVypx04Vm+e7eRWlV\nvfkoNwmQgKbNLMwLf5BfJHY5b1RpJvEGPoMN8kz8eZpMN7wXHlmlXc+kNh9o\nbpnvWbftjQ6Y5YC264tXFCHhPeYwrpn8wSC4Qs1p5W5Hwn3uCR60ojzh0MP0\nFE2LVReZQtH3C3eQ7OTEw9G1Nw6SO1DJ7NfLE5ZW2GO3Y8740aJKTxNGW0G9\nLDEOuW+Ek6AgfQ3yOIeldCLrvQ19ZroxMEUFS2RtobVKkRfeVcRw7GMGj4yG\ndbX5UjcKhkmTq5NhZLpMIdN0i/h7GpMhmGytWyfYS7o2ygIswd7DTIX0DKC+\nVyHIBdiGWNs0uivlsp6SBTcC3UoAOmQuSjXtyQ2aqT8cUGuE0wBVqtvMtxj0\nUwZ/CT+OBLleZfvunJDA7SOVL3L0F/somh2JTOEIDARCNotZcGAftccJR16O\ntzM125PsXAi5N/tzlUQ+1lZJvAv0ouEEoo7jgb1eHqKAX2YhIf57ZUfD3ZVo\n6X8Y\r\n=7C/6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.45_1524448553657_0.6749373015718558", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.46", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "50f060b4e4af01c73b40986fa593ae7958422e89", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-PVd7/PGxi82pEKyuDcEpMmlenMLhJCII3lIK4MhXGWrT/6cNMpY6ob5rWOarpXgZjy+JNI5uLPOce28bqq0Wtw==", "signatures": [{"sig": "MEQCIEvD1OW0nJKWWg4/x3KteH8CYN7zNz0i/xL3TrbdQ6gDAiB2gnxl6dHpVayUL5wdeQ8oPi7gODDa9XQ6ZH6Fox/U0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFaCRA9TVsSAnZWagAAS+sP/1WLbWAPjc/j4EZ8ymtO\n7E289wTGixAhTkiykvBFqlBqy/fibDWohQtxdXOxXMw0EPM9VOcMcKQZOtoK\nyLsmWU0TAxaWLCsQUWaPY/l2XIGyTi0STQjvaqVSkR8ZxFs0TsuqLDQaGDDA\nva9LJk8aOxUS56zZFmwkx5lJa5b9sGqaD9PdPzIWBda/oO/+uHiSKVXby0bm\nuKasuxv6rIRhrD4DSDRRiU5RPg9qWzSb0ouCeDXL4SqmBb05dk8F7m8S/vdR\n7dp608xfFtdxbotwWGfZkllFFJabFuOA/Eomeku2N/qL/rM6PWdKYPgsn2bj\nx+5lcr4eIHwIG6xZnr9iNQgrLxhAqFLhjGeRAyScVgDkVBHtpxBGrZ9BNNnX\nS7noV52JlqtDNlQdSCu//kxkY+IaOdHPwsnA2twLQOA8RQc1WNouQmoaYK5y\n+ju8/SPhauXeysik9BzAF79ESZRWlcNfPd0/N0QkJ6VFY6qLuQvVKiYVPcGF\nYcc9gaVrvS90xudWzNjsNP7vPtp5IzbARsGiU46xec7+FkAgQf92cYVKc5Vp\ndXlZ+bMSbhPg19njVlgshBjcfMsWisIS9FN4/yRD2oSKXTl56XbZg/QUf92G\nillLaE82NUfeLpHNqvEGiig7BnTWyp3Xvv3v4xH9CE4cIUrmHSKeqPv8siCk\n9NU4\r\n=naVX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/types": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.46_1524457817915_0.5394552535221839", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.47", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "085d864d0613c5813c1b7c71b61bea36f195929e", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-NhnGhjwrhzGas4A/PoBDEtEPCGJHrzhaT6qGmo1hmkA2orG4UNi7KENC38DhJII0n2oUrKUuzTwgCvxKOTiHbw==", "signatures": [{"sig": "MEUCIAjW8H2dfFP+4WiNxfAEWIz7crXGVKVt282R2Y9pAnwXAiEAwVKDNIsc+aKJ+uuhlYuBs3U3lHYlof1fJbxocwwfCSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTTCRA9TVsSAnZWagAAncQP/Rx1PHZ/JiRs9C2joFck\nWdZ7EZ588OIb3jagknoFVs7RfUZgg8DDWEk7KOCnqKWe31DLl+pKANIq6gZd\nNIL622BPRbwLz61i0YE52Lj2DVvx9qzLPbme6pJKkyxnToDkZiR/2jEEl8ky\nKbZvCp7I4KU647pMu/xGxyHHWMBwNEEJCYDvLXcn0nsG1m7PUgruDifhIEUv\nG73Kk2wHc2AeazU3BkT0P6d0iuycJzRvLjVZULR/J+y52eZoyjEHNoWTKHho\ntaOnhLb58tTYoMOnVu6r2Wh60ARbVy9CAGJpb4X5TlVHyH4Bn+N4Vt8Cxj0i\n1hLGRmG37kX6P936evWkM+7VRbOQGCl6K0/kDZi4D3hnvugLhT4yv/HGMWC7\nCC9HOebcg7bQ+Tr3OFLfSx5/osAEo2/ifPQqqSUno0JjvRXPFtPEw6s9Rqzg\nWtHKUQMtlCGgRuAdGcUL8smUl+YZSipnFM/iEl8pcnfDIXPFj/Wq5E0xNoXd\nddP+8KqaU5gYjzts8NAYFe8tHETburFuu4EjdZAAFs3DNy5jbEBMceQkkOfO\nS3q1Mtd+BCGtY1FWSgyjwtgYGxEZlx0xW+TrrvU3yDGNsUSEP1p+kh7eqz4P\nxMwmt9MPskPBWxlTuf3t2yLRbjYBf8I5zFN0PQS99uLDMebvmUdFif4d7d+z\nnuCy\r\n=+4kr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.47_1526342866724_0.8643895142926321", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.48", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "793b7af6f44b449a208062334a75c6aa491b72c5", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-JymM1V2kkDkizXAGmmhOFMqshOxR/gsG2xOYhM6dd5dpNEFSbEU0PTL/O6QEHJsgedq4pg6LKNwdo1EXIij0Uw==", "signatures": [{"sig": "MEUCIFtlZnx6xTEbtiWQq37WytBsARiuVzrBqZi56AHOWF8XAiEA7R2k8sBS5ormKo+tsCpiFfVqPzJEua0QSQvySGK/nNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCjCRA9TVsSAnZWagAAW8gQAJh9QQTIuaW7UoAbXLAO\n03utx2kyjyjnPdgpx/8F4Uf7WjAXI1ScbEpJjQjitNfn8iY83wx+ANXIr16N\nzEIW/NP/Lxeul3OGZv0GxLNo+pAlC3ZiO4es31pWNYYWy/qhUQZhdCXQfWos\nt93pAuNMZVJziEABXuCz9z3CWn8cMxcxtVjM5qPrznxTa3Tfmamcm0t9gI+D\neJx8BWY2uyYWTQkTPMKKe5pGknOdMzoN22ZgfHEIOGbXW/+5S/NzG9yCKM6x\necVzZTb5NgNXszW4K7pMTh7EJPt3Cns0DXKXBH7WkEfzAeToOfF+eYFJ5V6Y\n2d/FG+CbGh3VWmcCGsPybmTeWIGi9HoAX092dn2x9gboiQRenRxnWQ426HpO\nXo7SikdKDqVvuhqB8sV+tQo473+G2g2/6hx2ptIgEw61iGb61UkfdMovP6Pe\neIfed7MBf63/MjC59apdt+G8vEVRMDCP+g5FcqK9ZAU3fAg5/mkugFUKz92B\n2cEMjWFzQLbYtgWl1AiTshE6VtrjRItcukc2IRGNeTianf2wquMHCmyjILG4\nESl3JQyXeSo6Igr31RiAjI6xrTt5qvdzs5gCUZPKEQwrSKP1UnynE4528ELD\nb4ONtPtCAx70LSRCLvAzRjjYeT/1VoSOY3jvEoIbAhalKiL9MRwtfey1X0H0\nZboV\r\n=8wgO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.48_1527189667584_0.12947728100047606", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.49", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a98b43c3a6c54bef48f87b10dc4568dec0b41bf7", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-JZIkuOEtkL1IfyIBDrjpGpGXMKFU4mJDk8SbyMLiMo7OZbefuokntRFD/p6Bg6VqRR4jeI06lI0SUC1/ZGQ57g==", "signatures": [{"sig": "MEUCIDoCo7l30iqwRw+H5yP/3x0yzc6wyWuFjTzpo1N4eMtBAiEAnCb0Y209AYw2ax72A4ZhfbL7EGNjxe4rgjSwHEM8804=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMzCRA9TVsSAnZWagAAz1AP/0opeQEPP+a8h+w/3sOi\ndMwVPqcwwW7OUab6bK57UaJN1jkh3wTn4xQsHkuPF4po2olGQ8zmk0IvZm1d\nAxa5L83/RyuGeMzbnI4me6S5kk39HWPqMBeeZ3P3sekOWfqkOPxWUky7as5M\niBx70cyxnm8jhfj1ISO2biQVUidrKnFsRnvjHYdBsjvVLHreszHf7AKpm8yP\naGAYXFpHBzatdbW2qSgmsBgB8HvYxOXemNZDBIBrK+Rxl3XOPx8bO+LtDs5q\n1jfD6aJos7fFptg6+GhwrDFtCE7tRKVPig0BhOVHBNQsgpdbqiu1acnkc3IL\nujTi5VO7gjcapJ0DYx6XVNjD65QNv8JsI1tKuwN39pfvBUVOtFRGTHXzu8Mv\nhF1U6usgsctbaA4Se9yYcG/EPkTefDaR9FvmMNXbzY0teZbJFiknJ2JMYw/p\ngXPb/hpHZvtTSsIdZIcd7KLzog8sws/vseO3OuNyuHfNOYpMx6EdSaaQYjNf\ntyP/qP229ehua6f2Xl5b5vQrMD2TaWa3zzrcFKOZx2DFzNhNd+nIQW/G/pXN\nhEPAjMazDAniOnXoARF/0Afen037okDtClb+yGU6/+hlg0oe/aAF481tpUFz\nB3BV8tw0LHcjgcCd5oLzUWaLy8xHyZF+cVJhsTimN1CuWLvZk6QvqC/MP2Iy\nXyem\r\n=QkZO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "a98b43c3a6c54bef48f87b10dc4568dec0b41bf7", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "3.10.10", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/types": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.49_1527264050417_0.2745817027292323", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.50", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8319250ca30acaf5ed43a96c9de066bd16b2cfdd", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-S+2keYDg2fiXSuuXtvVLcLrJ2zD0krh41y/U/AC3Wdo/bSMh6sp/Yi4CYnCX3mBsJe3MamsQn9xAFWMGZ3WKsw==", "signatures": [{"sig": "MEUCIGUsqoW5BPKJkrjX1pOt74Szfy5bbS/Vgytm2SH22FvTAiEAkU76FAC2T19Ftvyk9g70YDzI3vIxOdwJ82RDhanVoeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1921}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.50_1528832814723_0.6922024631008563", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.51", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "21f2158ef083a123ce1e04665b5bb84f370080d7", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-n1SAU6yAD/6fnHA1iT+ha2YPQSBALTr7hkjrI10hVvqjIr4ZhSQk7VaKGzad1v0utofm1gU613aPSYZ9ncyj0w==", "signatures": [{"sig": "MEQCICVPaQX87IGznrhizkMBzihNUbHIX/8WH4GmcscqiLG6AiAaF8IcehZQMZiWmx2Q5wOIvs8zbQUkA+tNUJMSzu35PA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1921}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.51_1528838363439_0.4301574219747062", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.52", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0aad65208f2db5feb47c393f5ba26da5a5b04617", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-ID49rI/vr2S6WXn33C2t23o0+32kKu2uJl36IkLF980tY3NTnLulAovqxsPpWaWlVCz5QfylC4wncNLsPEL7Vg==", "signatures": [{"sig": "MEQCIBgsFkANhzIQ35EI6KOq77EM/6Wn/6aFfZUBCGHkGdUrAiAgqikrawD5IYoucKLZhLNYNrmbJ3yp+5SeOLaH87LU2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.52_1530838754157_0.1687010338386674", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.53", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8fc78ef4c0f69f8bb3bbdf34cd232c20120414c8", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-B6DMEnC9slZtBDRRjLi7OTcfmsXPPZsRLldqQ0TZjWj4QuZWFSDlonVWIYI+2Fb9DiA/dZMXMv9JDgVGGibMkw==", "signatures": [{"sig": "MEYCIQCEkZNeuLxnTJMuV9z8+CUiLvnAmbc7gnyww1j5/sxqBwIhAJXJc3dJ/jHjwlDaOvK4CyVUgonPne9aYBGHDFaalmTz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.53_1531316403805_0.21630576039478577", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.54", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4af8dd4ff90dbd29b3bcf85fff43952e2ae1016e", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-h5JJ2kUbeltpDS/IXmrsWj1QNAphVqoiu9IldllWsAI5gv3KXwzH+bnhyu4lFzp1U9n2qxPxKQPWCg21xuhW8Q==", "signatures": [{"sig": "MEYCIQDSPItGHiXJoLUb+xEG0SPvoH73kM0zRlI2tfeP1BFz5gIhAIvYFfjRW9N2Aqw9BfaqV+M6NlDUQgtoRRW+eJoX65LR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.54_1531763994235_0.3917860359240217", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.55", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "57fdc6898bc53f02da78bf4a39509d4dfc3b33cb", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-klYqi3FGcpKH3VVL+G1H9M9YIVN8i+srJPKN8jphZ0DqkeHDdpWL9Yx70rMqlYkPfZauzTtPQbHo8LkcoYFkuw==", "signatures": [{"sig": "MEUCIE5cXOD+c6ueWxp5ue07KfhG90PZ6feWVoe0xevDrrvGAiEA/TO0w8Abywecp6ya0YPQPe+ghDZEX130/tVVhmM2P2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.55_1532815617967_0.5554319198674136", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-beta.56", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e9814da0d175ca39901f2d895d6dfef658fe8953", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-T+eZePA6kM+3wHXDPKKFZGHtMJGfK2/xmdk9pVjFHppdg4zwEqGaqLQaOlqfk5ekx2vxO22tmL4Caf2A/MVm0w==", "signatures": [{"sig": "MEQCIErePOKFwhsch30CQMQOPhNtJ6/+QcoRZcaL3BqnpBU+AiBeQVDig/iCOpxZLEKOg7xkEiw58tC1nECCPwPlZAaduw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPt2CRA9TVsSAnZWagAAqSkP/iIFdp6naXCLlqbmKv/d\nhh9dHvGTrFGDtf9ZStpK0ODzRMCoapuZxMAn4wz59dQvK3Walo+2+2IuAsH3\noiMadbdQbCi5u3ookCM3Odg2bKqle+ntVxtiH+gUZF8BrNSidY7YX8SHiw6r\nyeDEsoXMuWgCsuYLNzmH8d+XglIbPNy3Uldw2iH2Yq6yLb0ALsoW3WHnK61f\n1izMebqjPYELrsYZEHUm7+VFAW2KVF6iS3QASSbwgMpq+JCe7Nd85nwm6Z7x\n+I3k9LUC3ZHlgkNQ22lwFIOZZUrCtE08MC78UOlYe008WPSi5yieFbVtbXAW\nZqZw+mLDKcjdfdBz+7fn/or4kioHAB7kSoYpoFfMi85u8CoBOtksogpHtAJs\nnaxOVF+dmyA0ZyYl9EfVvENOO1x2i2ohvPDtj/mGANIfhfgiPodN6pMgePXs\nglPn16ROyH7NYIzr70SGN8G9RlkoBIEq6jEDbd0KB5Vq6Cm/vhW5XKQCH833\ng5UqN6gwXbjn/OzppH27gEVtBl4T0C4D4KELbCBRT/usY/SRZJe3UlBE83FW\nNAiV+Vnqrru/+UADDBCxv3+ztCWCcUTjLdI3D/c3TY/YTxqjsjzjbIIaGhOC\n3BUVijhEBpwSBPeUt/bd9IdFTFKpPurTm86ZYGZP/xI2XXEA1Z3qre8o1rom\nmn7f\r\n=IYkl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-beta.56_1533344630442_0.7806938175669231", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-rc.0", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1c757e5981451ed81e1386e7050bd3b35ba7106c", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-MH8FlehCp7rA8i1Lr5r3Tbpqn4lH7kqkQag4Rmzc+5SZRUvkUpBD/Nft7otFNWMZEv7YJMJoUaXnP9fYrXdCVQ==", "signatures": [{"sig": "MEYCIQDKje9C49OaUdfGUFoExedpaawDpGHczUE+C+/LnH2YJQIhAM/ZxzK/3wwJvALN1W2Um8r6/NTvqyKJiEEnAro6h5Rh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRMCRA9TVsSAnZWagAAhHsQAJIs2Hu054rAxcOzbnal\n4cJ++R9SY+xAaq3cjuX43Ss2NyuRrIZLYlB/G5hqkzRyOexxFb8SQcAn2dJO\n7Oiei8VR2VJf3IQMlbzytaY5oH0LM2kwObLSuCykQX28Tc/Fy0a3+8FRBSyv\n74SDUpo+e5WMuIdLFOzjlkbvVlnQODQlA0QvPY2PmuZGEX/TJny+xbl0p6JX\ndpOaP903F5x3tkSlHSuxnRONc/DVU/nAIsPNSMlY8QOEuseGwHuEhwqn8MJ1\nXOWyGKHcM8uSKWf0phpsVolhLcpGrVUhCP49k8aup+AcmBD1rv7gmujFLkj/\nSmpOa7Z6G8QctUVip2AdIgFBgxwXz8yqcuy9br7SQZnbaYo5igNlav/Br9UG\nlJxTZCNkBVL4L+nq+WQXcWX5voVodJqe2PL3ues9uX9ZB43AfE5Yd1z+zbP0\n3t+Y7JDgvn5vfhQpHm5QFd1xvQZoCVE7d3+El4VBDlhG3rtE5nmMLjMLHlPh\niTmPM6p+lTujhaEZ17rb7sg6SxGtlhRSc0qMNt2Git4esb6UCRrmKbff7DID\nNSIwWVEpM7Nvs+mRz5gDdX3IJzqnXCxmvpIfPYC65fhAksun8rQDnGT7jcb/\nwdkdwGXqk/qV323ISebk636MznSJEzcrVLHgvKHRMo30R9/60c8cDiIwuu8k\nwR4t\r\n=w200\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-rc.0_1533830220234_0.2620555896884036", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-rc.1", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "482d8251870f61d88c9800fd3e58128e14ff8c98", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-XOKPnL/AJz8ZyY553FsMAVt9g/mE1+RQfg5/m3X0K4+RqYviPGZlxwe5mGSd8s2kPSB6D6nZRUfvZFtmFIXEvA==", "signatures": [{"sig": "MEUCIHc7g9rnaEJo9dGlgsQ7ypCnVvEnwDEIK9Pdk48WQY9EAiEA12iV+/se9n6OauqCIENIXKbBJBPVLpTJn0NII7yZu2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7ZCRA9TVsSAnZWagAA0lsQAJwTmDnKB3BzKuOlLqLu\ne6Qz5QAbTdSPY+fpdVv8w6Z6fz+ok0ffwXLUtzA3TmISOovYzcOMMtI2/w8Z\naCrxAxGABfSeN/BuJkDscjtfNhdqZeuOy1mLMfnlt/NZxL3AnJoOUSRZID6J\ncf2vxBNiM4KeSK7XoGZgklOlQxTL0h29MwZ0SIkK7GPBYDfvYg+cMo7m4Bzo\nD1S+WXpp3plMLNLbXnb82RxYsxMj5o26Vfa2FjDNdGs2SFiASY7QrbXxIDm+\nGYyiEVB4fAPsq1mpIwW704ZimAfpXr0pZR2uJleY8sm5zu+Ky+XlMFIg/IyF\ntRFE/cJkjjBnyRe5ockxWQ8/DK7IbfUIRKdwRnX39yeWkdB2FV+Gk2Kj93un\nhxLAsfj9ES0iOEdztqy8SYCvOsEjgS8IvhMi2p/6tHkTJCN1SI5yGJJTY0FK\nTYAt9AWRkRR23dnwVCmPF/avOG4NwDYEnwXQRtHu610MdxpRGoE0UowT6UuL\np2ollutkTBKoYF6lnhvdQi/+5YGSVFYgC8hSiqrz96XuSUkmGNtLhgEVgi+9\nN8tU/Rq1UQ0sa2SIVakEsIjP8oaWojSBak0oAVcwlDX4vgD1G/hA4WfARIWq\nlR0ElGx+nmDcERPyHQxf+z3L7zY33aovtY+AZdL2Gas3oXQjvpQ+NjjqPc+l\nY7m/\r\n=wNZL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-rc.1_1533845209232_0.01733423352458896", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-rc.2", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6ddfecaf9470f96de38704223646d9c20dcc2377", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-O/rWpfst/rKucph1U0Hy8YDwoHBNwAZJqv7rDOp00S7eMYbU8wDYgG43J+mJcYV+2WV2dqoym6j9QdV4xTvEsw==", "signatures": [{"sig": "MEQCIEMkSnezzUT3cPNloaePJ6DegIS5BnyirIGbACLwX7ITAiBCuYVLrXbqQW8f3Orsovq1DiFQgDatV9gz5PU4tHvj2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaLCRA9TVsSAnZWagAAUlwQAJ4E8mIoxqChKkIqG+ZP\nQwbj3aILtJ/xRGgBdr5hTBlBk/dT8UnyZgZXfbltHrl3u97jzCA+sLTNhqOh\nPny4SFOyBAD4NhZXzW+4X09kx1p+aJ45X/K9bJD66lVlXlBZSNulMltnswJe\nSdrqXJZezUjgVs8PzWRgq03gtoZygkRM2Oh+mHIrtTIQ7b05QBaC7pMlH95J\nuEne8H4dGiLzdtEa4O+GBjjxQXbeGq/8ZNgC8nDS9Z5FeM1bnCncv1sDnueP\nxXRSNbxTjr16VolzTgAyvFRLeAy8kTHL2wejhPmF9ei1cXFIVlNbP8/2yS+/\nrNdTIJdJn6KIZkoh4LDZ2Ckr6HFfDSWwyQJzOg5cMHEzpI4FhKwANkt9onKK\nIvdgaW2cCDMHEAAI6Kv5CieFmkGcg08AR/6nNC7fHLXOEwQUgnZ02dwfqeSi\nOl4NAK+esC/hGEiB+1e9jDv8TZfo9I9ZVuVRj6kf0cTEKrZsnsF9S7ignuDV\nQjuj6JQFkuwRBeS70h5Pf75aI3+JwCCMno63NZwvMVX4qQmNpiZvsadVZIsQ\no5Djhzb/srmjLxwQOc7ml9R4iqiLb8J3fNnYZ47LIR1Hu3zqznnvTeO2Zvxq\n5SzXr+qPPckH6CsSYHeWbVpM+tmtb4SMnT0pesUSJ16WyJ4ATsEePq+o8kwB\nUCMd\r\n=fmdp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-rc.2_1534879370977_0.4970627658355613", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-rc.3", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6a594f922c73c3266f5c59c3374b0e176aefd8a5", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-SfvMeEEtWspkh015Al2IymW7Ld/fm06BsrZeXHa+cePUKMBsUpcWODjUryN7roPhSLdnaLb2zBB7p9zFTe2Zbg==", "signatures": [{"sig": "MEQCIDF1G5C2q8YeBfOrWD6OE6R02gOACnsi0cIauhh+xH+0AiBCIHZIc9S37bqaq2M0+v44bKa/BBeTN+Vlt+hNth5q6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEk7CRA9TVsSAnZWagAAWugP/3bfozI2qmtq9zX2Tv1Z\n47vGcYqTDFF7DtNihhAfFeOqlViE9VZsKYnRX+TFa4ogwF3d/UNYJOgtT2xL\nZoC+QoZtaIDmwJk8qvEaojRZHxDcUI9dRU91h/Ny2wgsHMVd/hGb8+V8tCvx\nBJjspwposDKkwwuasfjCmKymhOC18X9/0ddYS6JDVd9mOVZyMUVZa1qdBEYn\nGvejrux0WKlQ9b96xFa5Sl1C6YMJsA+PS/13+KUF+27iN4SI/I8Y0dItyvIk\n2LGl0C5KrXWRWAKAk0xz7KUEKK+S+0COuJ2C+nJ1Z+FJrNMKe1s9qQ7B6Uv6\ncrKSr4QKLXvyfeGInITuQ2grkK98x7dm15tXesYSOrsN5ztaC3muN6uzCrTK\nDnhnRzV+1ZkG5XvminkxN1gK5GLZlXX0gQwT13e6ARCwKdCgOjSKRqKPKasP\n3mUkuQ/fvdEB/mFAFti1nH730M1zVvSbvp68HsAnNwpZ5muBDyCAnG+Dt2qV\nBZXjJF2SkTtVOzGEHF3YeaHp5l2cp/H7CeyjVGFgJEwaQB8dHEVxzlCmVuhW\njXJst/36KS+U/pYCnfe3dS+pB9YnXMCIlPKISex6iz1ZOZXLMOruCegk4C85\nnSDjpAI/K5GBUC1uvQVwEsSQhKBwEdwdX8t+W7Fi5OCgg50DBbNLoSLszu3F\nU1yT\r\n=XJRN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-rc.3_1535134010854_0.08288835624547741", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0-rc.4", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e5c2d01e13b74b852c4859602dfdcb205a3d4e46", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-6h2ZdrQ4szqpiyxii1LqRXl6Ygw1iKZfTdD4fGBau4DtpNrDA6EsJG1KZ69gLZkHk5O4LXnkYNkZjEpmskH10Q==", "signatures": [{"sig": "MEYCIQDTiOuZdPlx6gyPEhbtZbd2N09KffPtErFOy0+aK0+6DQIhANeMHQ4C4R4E/udsfsW/aMwn1jrQ2C5y6ZYFES4933GS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoWCRA9TVsSAnZWagAApdMQAJdm6YrTj8PwaGfPOpfz\nh2DS7wL5q2MfLdndj6msLcjH0xpBKEklLpOHzUUQeZ3lCFTle+WI95rtOyQ1\nVO4zKT2KXXTNc7MiQ+UHopYHuFBNI58Vi8KO+iSDisAHcTmESrickxQak1Wh\nmdL2Gt0ugzP+h7Lx0UFlivlzA/vzukYxjSZi2wYPRLRarOfmb8VF6CQOsUKv\nIu+142q2qCkZ6en++qsegJyvbTlK+aZ3aRAVVetIeO3WdYHEN+4705J+OdVz\n6yFoQikVJDjpVvC8Pj1b8ijqiPFW0qJuECtfMhg5+1/DEoiriuTcVoZVjKij\navFgptJbJrURhfclDZB1v54ygD6pp9i+JlKPtxfdDMJ+SbzX34fo+g39GYPf\nnsHDJWMdUqvjyRFr1w52g+3EWles4p1p5r0Bb1cwjtOkTec2lXjxM8ck6xW4\nAQKk2l8NQPHLYnXAaYbEiAtvTKxCvQr9lxi5rXEmoTUhbzvEAhyjmP2XwkxE\nJD+lrZLaTUXHiF3JW4M8JaXZqJRltb7XBo61jkxmDzeXig+c1Wu5GZ6vxpFG\nGgxqhTA0M8jPAgtPEWtYyqeANcWzh0uIFr+mcls4FQ5Knjq0gj44qudj5++D\nESnVBeUSi7w16PYaZBVgyOnEmKvKKEEXC+JZWgI/YH3reSOsSTWZwkvA3vjT\nLrzg\r\n=6BuY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0-rc.4_1535388181345_0.09137022699583941", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-optimise-call-expression", "version": "7.0.0", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a2920c5702b073c15de51106200aa8cad20497d5", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-u8nd9NQePYNQV8iPWu/pLLYBqZBa4ZaY1YWRFMuxrid94wKI1QNt67NEZ7GAe5Kc/0LLScbim05xZFWkAdrj9g==", "signatures": [{"sig": "MEUCIAdh3c1kLzUHjYNcvwmypVHMWeNGHZvKaF7tskuykvxpAiEAntrRoPA2Kr9XOYnhFWLWCtizBISGfK0M6UBxAvNCF98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAxCRA9TVsSAnZWagAATX4P/1vVg+IL7lNlff6PiSjS\nQYv0uc1U7refIYQiNggXngdy6nXZklYA0Fsr8LonG2lEhQtJNglhxuUxW+gy\n2czYUDpP2+qAPA1TnS3drz+SZ0rWWbn/kiHJlb6ObXu9PmSXwX0At7ctxtyA\ndQq7OJcTSxV1z5GNHBpFxvycAIqONF0BbUfxuDIgm63b79Cx3yJcBoP+L42r\nGxfKzT+mL3gAEa5E1c65awDwH1ZLVME1/wog0fxDa59syNX11DRWY3+GJtDa\nH/nyTuHHkI3QwzIe9iiMDVq2ETo9D1asZm9hgDlltUmaDMs5qJYb/aSZUYX8\nJIN02038NrKHqJP6tf7v1HHLl9Kz4AfiUpZM2WUJsV+jgi9SXsfGHQROyL8r\n29MXVvk9DxHdT4/hpAsBNQCF451D2k74l1LtImyYBvQj2BulB5sDbugt5nNT\nE+f2EGVspxE4uF02nNWDuXCfEAHijGqRJxYhhhK1CG26ynJtfRBzse7ci77P\nk8DLuK1cU/mTaAqASYh9SxboysvApLVWZNviOTFoF1T3Xl4k90Feu4MAuZGK\ncG8GFtPkJs7r+5h/v0DE31rLe6ikrJqoWCatSCE2ttnNIBffaoEtSjie+EhD\ndaHHSZAVl29YC535ISf5ScFoCNk/LgcDH54ukQwEhayC9mxoOmwV2apmx4bP\nXFOF\r\n=OOYd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "description": "Helper function to optimise call expression", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.0.0_1535406128723_0.7025303647542418", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-optimise-call-expression", "version": "7.7.0", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4f66a216116a66164135dc618c5d8b7a959f9365", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-48TeqmbazjNU/65niiiJIJRc5JozB8acui1OS7bSd6PgxfuovWsvjfWSzlgx+gPFdVveNzUdpdIg5l56Pl5jqg==", "signatures": [{"sig": "MEYCIQD6O9kprLq08h7Xe7oE/ETRf7jvAThQ8tKwz429wMLgGgIhAJ/e//eVqaCfUsqZK+YjZY7fMABv9LgPMx6EEfyJq7sX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSuCRA9TVsSAnZWagAAMqAP+wbJY7s3AaapJrlsxqgq\nEKi6q8ir3Pp16OPw1aQSrIGNletCDMmamLwBy5ntID0TK/vLZPCxkBC7Jvl1\nxAOaAUlBg5e5eE9cxvcxHhA26+dZ3jL3V4V/6OFw952gcG9me9CekbrqPTfx\n8OYIuGSjBIraC5RLckA9W5oN1u/uF8ArYsP5mGtjsun1boK+wNBCmRf7bILv\nhaZDYm4y3S1bBnsAHecXAEOnTyJ7vz5feX+YEj7/CyzkX+FUb4t1gnuvg53G\n2QkQcxW5EkpfNBF1ml8fiEYip41GHDsSyqcH5JcCoTC8Hs2APH4FfLB572b7\nhNfX7zUnEu4fXwQlAboFhucpBbacqJRwLE8ihZgqfn6F7HXYVJik5aT/sCZn\nX/3BqVi3JMOeCi78IBIhsgkK6zKN/vfHYkiVsw+foBIEaYUVvbVYlNHkjb+t\nnkW9P5JVHn0efmfuqhUSQKJ5mGW3ON1rz7cedBm0mCcm4MXiIYdk8pnWqs9D\n+zDIrlh12zshfPXIEsUKlVh5MvkDEvKBCUvsqCQDgwEVagp9CnYEgZ3Xu8OK\n/k0Rr9sjqcRb7BYrUzdagnVs2EWjAbk0VOuQIAzdnMZL3HRSYCLMU2UtPEmY\nPtipxB+19a+9MZWS71VQf8K4R+bMQOwbbGM14DbaoIQH8TNhY3tmSx+KhkPp\nSW2P\r\n=7hgF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.7.0_1572951208888_0.1765684354895083", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-optimise-call-expression", "version": "7.7.4", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "034af31370d2995242aa4df402c3b7794b2dcdf2", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-VB7gWZ2fDkSuqW6b1AKXkJWO5NyNI3bFL/kK79/30moK57blr6NbH8xcl2XcKCwOmJosftWunZqfO84IGq3ZZg==", "signatures": [{"sig": "MEUCIQC617ysCeWAwnAszdHefDTwof8FTLorDaxCBStZ1OHmZgIgK/ai6ZOtTNCOf66uwprQ4XAOlRSYZGQ+hQ3KB2KkBSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBDCRA9TVsSAnZWagAApK8P/Rtmz4cY6w/JbeFicz4a\nSjhpfm6hX0Yp8Zyb68mqC0qA7usds+MIsWzeuAXY+9DHBRw/yW9BMOHF7Sta\nhRKsxTcVPxDCBG28IiIeaMfTt4NS/dVDU/vI3ZABkguss4KweLgL0RrsscE8\n5cHBh4m1txOE59JL1frNqYpQV62G/oBcCLxzsMDxSxOgbSOlrrmqB9XwXEEL\neGTsLvt8MrfL5MoKBKA1UeBHZrHaMvfNS8+/t8uUFHAPRXYOahKCrRKieAaW\nmEYK/W/Au1yZH3QZMmszM24wZewEiN/Z7vM5Tn+i7+XoGQp8ckivD32AwG+7\nLx0hehe/aIMltmPh+yXxqxRC9mVp9qvMGCEmcGonPGSpcC6lCaOGTIaABS3n\nXQ1eMDE8fx54KKPbPPfpp9hM7i/jnxRCyvD+3oeYSX8Qh7IU3fxxWcH3DY4c\nGMMZ6m05ysRce1f+qRpo7oYBmJ/GtmXKpkohUmlsyI5lejb0C4tSyyRDdMa9\nkWhw95U9dzhPfrhLfpQVMNv0UETLAlVX55RoFWKzriAqP5jH6WDgDv6LYP7L\nx24S4+kKoe9oq2EDdBKCcc0av3MLbH0wVf70HSHLiiulz9sNNlUqkJBlg2qw\nup1UIF7Kcu9HtkgngbkxLmbfu3J96B7i3CHS14kKewQKOjxmnFDwqtK5chpt\n4dTF\r\n=WIMF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.7.4_1574465603415_0.7692287710459371", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-optimise-call-expression", "version": "7.8.0", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "3df62773cf210db9ed34c2bb39fece5acd1e1733", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-aiJt1m+K57y0n10fTw+QXcCXzmpkG+o+NoQmAZqlZPstkTE0PZT+Z27QSd/6Gf00nuXJQO4NiJ0/YagSW5kC2A==", "signatures": [{"sig": "MEUCIByzCMFGMcA+HA1QEyqbX0u39yB/RDHfWk23qbtmjq4sAiEAnYRv1vZvWXQaPxI4ILwN46nbz62cg7YoKCrlgjQDpBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVwCRA9TVsSAnZWagAA/24P/Ru1Sv2gCYZ7HyOnwbul\ntN2TcONBub+nGgsn3hJ7xZ1uCFOHbVekapIgaYOh2gRZH9gAAQBwx6DoL5rC\n8+qtkrrkUMdK6i7htjEfvmtKM34nBT1MlF0uoJqFWz1ZWk22jdHZ6Bw4LS1B\nochb2LWcIuyQCq0tnHYHyIxD3RXNZ23v9rgU2mlMDNqJCgpAIPCSxpQXsvmJ\nLe39x1OmpnRmpR0mUHS7WZeBE4e9coiSDMgHCm/v4PxODEST6sAX+fW/YeUk\nkxT7rGUKjJDtDGPi/NXzs3UhC6PsSKM8PcCDit3BBVjR46pwhktTaNzfqqe/\nflm3H3BvMvYMIdYz4js9Iw90YZFq5demN3ydkC0R7nY+bdnpubKKXlA2AM0r\n00SZC9/r40p0fK7IlrSutiTy5OcXMp8wQIYx1WX/oAbgk8ppyQG90gJj1M4S\nh99HVRwDpKAruVC+LH7k7XtBE+RcRKo6+nm6pL+Bk1B1Xc3aGTE4m7o2a4is\naq4zDttwRzqr8JYkSnzt5mkcFNwk97flnXxOdnIyKuZrASrMP9qqFwN2YibV\n8PWFjVGqaY7u1ay8jWyw8Je7pxgGdnEvbE8VTkPQQyV2rLooAc5SiG4IbU/j\nn3vwPnvfey9366FMfubDTVgYxj+AZVE1nC+R7HC3XvEeugEEOFu/eN0xmIIw\neN7Y\r\n=9u3R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.8.0_1578788207896_0.6770358037310702", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-optimise-call-expression", "version": "7.8.3", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "7ed071813d09c75298ef4f208956006b6111ecb9", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-Kag20n86cbO2AvHca6EJsvqAd82gc6VMGule4HwebwMlwkpXuVqrNRj6CkCV2sKxgi9MyAUnZVnZ6lJ1/vKhHQ==", "signatures": [{"sig": "MEUCIQCPaM/QP0ky4bH/AxpR7Xp9lpFXNI6H6YHgBB64OPwMuwIgNend6ptezaZYXGNosWmz0nZjcUdRPAhpgxILYfoX44A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQgCRA9TVsSAnZWagAABigP/iGc2Bg2xgDMrSZuNiRv\ne1Suo7ZPxfKKTE5X42LuBTf1pQYeV6zNeVgMg133tFaIyvSi4h1VvbG/v3B1\nfAK9+UIVysqiU24IbkM+0V0sE1aOJRFobLMNfH1nyPUcPp7aqX+/5GpoJRDd\nvUCQ2ZZhjN2uXSe33NNrttBrC9oIJePQnm7b0bi1Cyj6Pw9TeL06A/sm5kLo\njZEk7sIqgtmpt0lUHyKEkJo4NY/PB/X3kYnoVXIKQXP5hmikZZ1RfjmnzerT\ncR2HNQ7MLo5WQBk+Ys+rHwTJ2SpwELHX6DMw5nmPi7i7obXopHFO9Kr95erP\nGijK4pihVoCnutAQVMCf2o4O46Ug7/iW7Btce6hvCDSoIhy5vnC6mjhZgvvi\nrZy4Q8FWpYGdn5K5svILd0yX4qqaIyj+h74ufo/D+g9726k0UY6VyszYI+9E\nYdF7DGA5KZSp/ldzI0tJ/NETkFqXfXtF2zT3UDljCGzDGagX35w9XgsxxRxg\nh2Lh4iCKMqQzj6QNn+8qQtBLChu5cwWQvFV/f2sAXbIQ8Pyl2VVuDuBfvlYo\n0cOwhojvBcpQaS8he5aM20VHR/xB7EO0M84FPM0gUvSGJp71eiYd0uP3SBQR\nifoPu06xjmmaxVWKhjWAmMh8+Nq76r+b3Cr2NN+wazRa8Mu35modH83IvXES\nEW0u\r\n=J393\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.8.3_1578951711689_0.06951230627079186", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/helper-optimise-call-expression", "version": "7.10.0", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6dcfb565842f43bed31b24f3e4277f18826e5e79", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-HgMd8QKA8wMJs5uK/DYKdyzJAEuGt1zyDp9wLMlMR6LitTQTHPUE+msC82ZsEDwq+U3/yHcIXIngRm9MS4IcIg==", "signatures": [{"sig": "MEUCIQCuTxKZdw56eohDZISCHKQWaPkguMDhkDoAbC6GzFBSJwIgL/6jaj+Xwu02QwCSt8ivdOLvZa38Kurxt9lcY4P4uiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2ICRA9TVsSAnZWagAA2QUP/jhcLDAbh8KEXYAKWi4m\nYRCou6cwNyQ0iezbYGIS/EzwIHL7TLt6BJPom3rrbCmo2ndJWhJpB47geTV7\nj7V5znxj728ERxNgdGWwm5N16F5ttUMRYPYPVg4kAD1Ww2KJipRiAajd7ia9\nEssZu4AdlzeoVh4gkXIqZoM4LC369cC0GmZUz90hibgn6NryOavOpjxLfDZQ\nINwfpUHBejzfmJpAAw/lDeKichpO38qMLiIcqFRVdUAgovR1JbiODX+hi6y8\n41Cr84qTvevFKMU6XkQgExx5qvi3O3Vv4THle7E030Igvx8bG0H0BlmbOAx7\n3FG5NEySrbKg7TmbfX/n7w+8kxHbRw9BIjDO94uJJsf3Zid2jYlAKj0QLqIw\nDw40apOBkxcUQcNOWkW4CpwcaANXi7TTx6P6NgRwmzNb9oYGhnz0hgUev7+4\niL0Q19k9+dfbyted6xkgOK1MQOopAyFrx7yDRo8sfDK6H8N4tKLdHdqiDmXA\nB4CJoPOuL7MiL2HAM3zrSnNoIy4FprDCh90f0MCmZ4qX15rxqtZQsxgzIpsk\nGSZXZyKBl9TgBbqXbPJuAWpAi35/kCZdW8Zw0HsH1PdwZPsGSUHq7wq/7A4f\n3noXsrvhpos8JFsfC30tLOrmIVHd1qpBDkBzXd25y5xHQGH4eRjjUKvnrJDP\nRFgf\r\n=r917\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-optimise-call-expression", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/types": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.10.0_1590529410577_0.6005251523457127", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-optimise-call-expression", "version": "7.10.1", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b4a1f2561870ce1247ceddb02a3860fa96d72543", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-a0DjNS1prnBsoKx83dP2falChcs7p3i8VMzdrSbfLhuQra/2ENC4sbri34dz/rWmDADsmF1q5GbfaXydh0Jbjg==", "signatures": [{"sig": "MEQCICE6ya7xBbVqKCkWdBhw1C03AxsTSg1uz+cELKk3OAQcAiBbpwaVw2wtAaG7MnhTOvhq2NDI9fw/W3+Sna90m7CVwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS0CRA9TVsSAnZWagAArzcP/2XOoxxyh8Mp7q/8Um8n\nqvZSTCtxTjagRhsUFoFGk+mN3s2s3b7q7YlNxnC2IekSttZvkKho8uTSm3rw\npK5UdP0ls4Rj/l++NswgWpdHGAp9b6NZeJTFR1ByrnZIw9pChV7gk4aYAdg8\nUWtX6rLykBFa//1DFqpHlN7s+EILCu49BubRaHpWY8OHbFwv4TkSQs2WzQXt\nZ+yNFZpXW1OtxGckpcashu1wpn6o7Q1UXnrFW6IkplwN/cHIAINNq+2tiuKW\nruqCEi8QaaBGxS0BjK7BISC4pjEbKx33E6wJgD5lw9NlTfaT6tZoY0StGcs3\nPittw6wa7hARGHEO4ACCMLXsx8VxW7AfhgK+AJ7FrV/YlBAyC9tKcSTTTcsi\nKVJBFjFi7Ld2Uwyjq9t5zMRK8Ro2/CbsQFkOg8izuchNfVEhAhu5xqwmQvPq\nJnCQfD+f5fnAlp6M8P6OZBkjcu6ks7DVxmhsDFAz4a5yHeavmjTk7Foxekr7\nCCUSAGDPFuoQ9A1ryKEL0ryu+QOhc3zZlsgw/zkVZLQi4CbwJMhqTa06AFOT\nceg6ePakd5TopEqdKMoal+JGl92zp0ATIR5sOkDHv2tn+xR1evWG+kc5ec3E\nB54tyjNJ0D941pCK6DqpaXENLlmxTGxDbY5zPhlohPAeC5xYQK4MYXqyBJav\n7+oV\r\n=9Bd9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.10.1_1590617267821_0.8788808284286087", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/helper-optimise-call-expression", "version": "7.10.3", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f53c4b6783093195b0f69330439908841660c530", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-kT2R3VBH/cnSz+yChKpaKRJQJWxdGoc6SjioRId2wkeV3bK0wLLioFpJROrX0U4xr/NmxSSAWT/9Ih5snwIIzg==", "signatures": [{"sig": "MEUCIQC4pDv5dxplG89OhWKfimmxlsAjyzgOPA7oywKJZLD2lwIgLJFTN91GvZZSbPV6MDByY6Bmd3z10u5hj/vA45m11Yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYICRA9TVsSAnZWagAAqzMP/2etmbG0s0cn4rhvJ5kU\nBfdU7AWOIpIiOwn9pdLEm3I42O1vieR+J2XO7Dofi4U5+/GA23kAxKMFhapv\ncpr3JAJvbA5DHm1KiuZLz4h2GumIIRY1/EsfA6HgDl4bQIK6f7Qez5iUb9A5\np6RM8B9lur/oFRBCKW9oBj/9dpwKrldlPRhI0m8Ie8BPRs06HXVQGj7V+1IJ\nX5y0CFYEuU7q3igRMTo4QbwNIHStsbfibuadgkM3jiDKsxiqIR2R0sjgdwxt\ne2MIq49icjqMLu8v3kBTdBraVOPjsFzlCDG0BG4CL2sZyL+FFgODW8pksP3p\nvdJ2CXTvq84JlbXCDJtiGO+5aZUb3XpJOu4GHCA/cXCtWNCDy8HzUQKTm/2X\nK+kYBw8728CK6IbHeRFD0wITRp0kQF7DF9vv1zPsWQBNi11uGh5S+J8hXxjb\nhrsrvlNkh1Hft8YY5yYYvy2IRm4GecGp5/t+ZSh8z5uCsKX500Cw4EpxrALl\nr5FT4cKCNmwpF9YIfMwbi7b5Zk41CTMqpdUJCSUiedR0QXX36ZSNlK1PT2lw\nywWiEbgXPktPTXV99IycU6h07fKwPg3uRAGxbI9a+elH4zavKJ9eK5iwTy1o\n8me3o/7RtvzzjW9Q2nZNMcqjkbreDN7XQGYosWm2aAfeHLKJO/QoFK9CTFU5\nXaV2\r\n=CV18\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.10.3_1592600072063_0.4756659314045568", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-optimise-call-expression", "version": "7.10.4", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "50dc96413d594f995a77905905b05893cd779673", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-n3UGKY4VXwXThEiKrgRAoVPBMqeoPgHVqiHZOanAJCG9nQUL2pLRQirUzl0ioKclHGpGqRgIOkgcIJaIWLpygg==", "signatures": [{"sig": "MEQCICK7+cq1cqOE10XxQS6wuPMhbWlWeYVHsx0NgFVwMO8fAiA5bzsJyApI0T56QVTfbsBL335altjn6MVHG5V66ZUvtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zo6CRA9TVsSAnZWagAA1TAP/2fAVbFtPLonODL3+tih\nkvc/MYRBDBgUzaOAKib/YJWVzwu4+q7y0r7ubGcN7b+odFouWMLh5r3GiRhf\nsvR12keJYpFRoRp/M26gWlvIXTH9Nib9ba3yujRHus84TDMIxZPBLO+LvT1d\n5s1ygGwl4opMo3IWBIO8t0nTyQ5lji0iWP2qSMFG7PTR4ZB2rqV5c/GKAUrE\nVMqKO7rSchaNg3KkTdkDdOKzV2NY/WoLeVle+ww2OwA5luAQPSBUFuVSETiC\nO3Wyz18XLoVncijhOpX7vaQqOyPdj8jHQLuIQpNPw/pf5+AT5r5Phc38k7J2\nkRWybgj5ma8fy6qvnZ2ryLRPzbcKCaZ/yGJC5Na0VSJppYXkhAZw/aKgs3/z\nKuUPdkHQ800QZ4z1l2Dltx8L0L1mOIUjXJcGwkL3+1jP6FuYl2atEz9Q9AeM\npsO4EPsvtGsi6tbZnRcnmcs8AfA6WYkwDKW4rmbdP/8hj+zZC6IXKzikK5O1\nFNOUpHpDDPu0W6etKtAkvNNfNovXiDOrxgVXhYaquG/8P3ly/jpSa2GOA9s9\nkqyX3jFNZTUMkGm3tQ3MJiLcGyB4nnllUYi8n+lBe4a8G0Xzao2qzbC6WwUe\nw8EeMiE+ddxoHqfJW1UoJdum+OYn8ksGJpVBL0PzD/70E9/6rQuPs0l5Vvd4\ndEHC\r\n=Vpoo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper function to optimise call expression", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.10.4_1593522746322_0.6803118488404547", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/helper-optimise-call-expression", "version": "7.12.7", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "7f94ae5e08721a49467346aa04fd22f750033b9c", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.12.7.tgz", "fileCount": 4, "integrity": "sha512-I5xc9oSJ2h59OwyUqjv95HRyzxj53DAubUERgQMrpcCEYQyToeHA+NEcUEsVWB4j53RDeskeBJ0SgRAYHDBckw==", "signatures": [{"sig": "MEUCIBd3Y2UyGBdL/MU6NJyW8R60N9he2igxJwqx2S0LxCTqAiEAiIVopsXiRtAP+kZ+8V0QQUnnRW3npqcPzMnaCihDKO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+uCRA9TVsSAnZWagAANH8QAJDWjKu1cI7IC90q06dZ\nQ4AFpjvdnOq+Lz6v/rYZV8lWNRUihKn4DmwJUuaN3h2uLu5FvXusyrfrpzzt\nZOWpH8zRuN7wMwGENm9IrqRJnJcO1weBw17MQtOtgVcZPIlAsmfS4BG0E3Jb\nHOceJ+XoMw9o0gHMW67VWgXQjDU2P9zqkmUJwFx5ppKp9VitC2mgc7ouvns3\nX8m5/Aift2Ksr/FzLXyNseD30z7I7FNBKPg5crRH88moW5KiykOVK6lrNV5v\nJAZ+mgSYBZAorAuQuBh3CaPKqIg/LCvTrOKA7ZDq1kchLD/kM4PZCVZxZiKx\n0llBQeZ/S3Ll396YmDri5Q8fRCLDIqOBZ5Q2dEz0VDV/g0XBuENcp908iw4E\nZX2+6i2gnlX6zXw2y6EwonRGze5J5zf3TZWaGJGijmZ8U8Ori1IUhfvCf4Ti\nQCEq7/ZraE+nRj7byDSXgsh1te3Q9aBg/M54xqwbbwjh9aPhIE8v7ZLX9yKB\no8sJ+V4CExbyaiItdaPqZo+V7nee3pb8QzXS9srSfEGb8+NGzaAOXSUHl4V7\nHkims8E7Tp3XTZzH5fI7A9MxdiMNXnyHXMQ15JjOKlVaG6pL40tySfIppASx\n5KTAuvaNAGuo0YOVuTp/4NCTInt7MTFOBTwXemHzqiAwNU9QuNEIco8JYp/T\n1hZ8\r\n=HnTN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.12.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.12.7", "@babel/generator": "7.12.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.12.7_1605906350100_0.030712745372488026", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/helper-optimise-call-expression", "version": "7.12.10", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "94ca4e306ee11a7dd6e9f42823e2ac6b49881e2d", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.12.10.tgz", "fileCount": 4, "integrity": "sha512-4tpbU0SrSTjjt65UMWSrUOPZTsgvPgGG4S8QSTNHacKzpS51IVWGDj0yCwyeZND/i+LSN2g/O63jEXEWm49sYQ==", "signatures": [{"sig": "MEUCIGIPVoS6m3Fs1RCJjUKkeoOTXFhaWW6J5iOZODG4lhxOAiEA6PgRTPU24elK6xtFOhLS3F/c+D1x6tBYWjc6URrOmHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQjCRA9TVsSAnZWagAAoUsP/14MWtqHJjNAR13q3wdv\np5/Y4YFrnLiEBMEh9PjKvDPVyob/5ylSnaiLW2VWlm/W1LqGwsokZGQ5Okf4\niQruKVFgtLRTqYS9BhaVmq2JqjlY2A6cGwz7fW9B3FYC7M+IZ2yoWexIDfgH\nhJqdp5tOMnfNL68EuNNuO1SAcgaTqZ/Fcu5dbHuBMJrcmRRq+Tbaf3QuPTP3\nwwLiWA0elUwOw6AG/CxqZxT7Ye226cPuyTIjgXHnUw+KoJoQKII01ml4O5M4\n/K6FmMLTMU3JvyV0laWf2H3Kzm+cp/8sSdVgzORU2J/L4ukCzOxbXA5ZLWvR\n8PfbXBNFMpXSig6qFTkduVbiRMEdQWG0fjim+g3V3OmKVsfB79mkKpP6jqNI\nSaOWhhcEsclW4PRpNp8tJAxXwnkEWMh2dGlDho3cK3cSDzuou6Emum5Q4MbR\nwLJG/TaSevSRyvJAne/T5zPMDmxvqyz+XifdTZZB+ec1IS3OXJ1ImlnYcLJj\nI4Z68ab5NM3da0oM8N7/0gi/o9xEx4e/1VNQQ3HeHCdNgSuV2GqClXRj+mmP\n14kVJbOidPbHEp3V6D1S+jYD3LtixyrVKnv8RFX9UwYMGwo+VuG5nlYmC7nN\n7iwfC3DfQutGc95Nziknc9n0l0BNnVjeSGOKoo/3mDGJAelI4XqILHxvgYe2\n6c69\r\n=rStt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.12.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.12.10", "@babel/generator": "7.12.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.12.10_1607554083069_0.2114564708544613", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-optimise-call-expression", "version": "7.12.13", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "5c02d171b4c8615b1e7163f888c1c81c30a2aaea", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-BdWQhoVJkp6nVjB7nkFWcn43dkprYauqtk++Py2eaf/GRDFm5BxRqEIZCiHlZUGAVmtwKcsVL1dC68WmzeFmiA==", "signatures": [{"sig": "MEQCIBgGkJ909KRE1G1wUxcBPrUovaqyf0tSio4L1mw80Oe6AiA3mqwnrGqHRlPBeueu5Ivz2tsy512zyY4R9z96W1A6HA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4019, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGff2CRA9TVsSAnZWagAA1Y0P/3rW23Sy9VpB1Wtoz9Dr\nj8Nz8itrM56FFXT2EAY8UwRwJMj1VwqI8BCiTI7N+oBX0jyMyLEy3L0khgJb\n/k29A1e7PAiVMp14MKQ9oAA11vdwZhttI6XsdX2DyRI7kVYGMBAKWp4Bp4CJ\nsuwfBr9ejfPRWEEqWVnDexjMnMZFRKJSozf1GHF91clHxhKjMKiaGTmmYv12\n7TCUEzBsmBIjgJwykgTB+V7MzoniR0WUi8SgKPp1I8zgZygLFBscNjsAKKkY\nevV0+Z8g0zBa71xg/WqVg+aoGEYHFXIG7oeTvpCFaWN9d98zigtARQfxKDQN\nvIDnQ3zYVVJVu+XnlpNXPu8hfVizjbNGTuoDB+r95+cxjy0S2R6vd6JH9yRk\nWzHAKNKS2uITpIIyREySczB/EL8PYis5WiEiWSyNnAhUm7QRy3fR/hHetzam\n6eBRwvoMEmnrxy5lxjLMurfgykSNZEl8kxhG6z12iKOZLHzPnJ4vKeiEOZsv\niHbplVHCzlGll59wtB1/tculNG20uRE+wRW5QlO9wfi0OAp5UqnRqK/F+s60\nx68RwPFHD/FDA+CFuiWBbZ9jc/nowB7wr8AQs2EYGVglF04uHnmc3i6FXSK3\n2ck/KEU5ZRLOTxDl+M506fBvk3gecglALNo4htspGpldIojekReDMTk0+v1q\nIJg4\r\n=N9js\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.12.13", "@babel/generator": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.12.13_1612314613585_0.003026114121152723", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-optimise-call-expression", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "f27395a8619e0665b3f0364cddb41c25d71b499c", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-IqiLIrODUOdnPU9/F8ib1Fx2ohlgDhxnIDU7OEVi+kAbEZcyiF7BLU8W6PfvPi9LzztjS7kcbzbmL7oG8kD6VA==", "signatures": [{"sig": "MEYCIQDDWtz4DLpDJJxv9eg5Ophdur6XgRlO+2R8cI9gAU5qEQIhANxfJUZrrUB4YKV2wpKoVGDXl3zPtu6waYnpRFnT1Von", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrbCRA9TVsSAnZWagAAWzYP/32AVlK4ScG46SOKWoOk\nskKaoq3kOxKFm5TiOREEFDdSG8uER0dimGD90/h501VLD8TZPtMWwp30Zpl7\n3hesHRK4WFNrYxwQ5Xae+brzaa9Z+XlM8Jdg6XdMGvZSd6uZscXw2xQlp8hC\nbtMYNVKJZhRnsxuktsN47ToQqTNmYkForfVLpuhwBGBgRPJyT3CubS9+nyqf\ndD5ihSFdVG6wfuPu60I9glR78uPJit1d+3/CUz2+p/8HOK2Ug2IX1ZhzmSlb\nRL3/QdYq2DDeA8QtiKhBaDcs6grd6962HT5iIwgh2eRST3yfRhKXMhTEBJDV\nMsiMZhKzRV556pS4DL6/ggC1BUxycMyq57Cj6+1gMr33hUqivBebRffohtPi\ngzXjHrovIi8PXNQuGx+DdYW3P1WZnB+RMZUqaq4DsHzKSAM2pnnouzr1xfIJ\nh/Rg5AFdkmVYAhbaq6FhpNKvMa2vVjAgB0GP6auKMvxXkoKXFrpfmd0g/rQu\nJUwB8zbZ480eXHdhUAHw1ntfVdoat595zIKSn2CBjeRxiFzcwOGpvL2shkyl\n/dGWNPcyLV8DIjstsrmOh9rJBO3KV04Sb8dtHz9YPxkx/g31Mm9Ub4Tkfsfn\nMwNSI1zMGu21RIkwZ3r+bQ3vYMi9XcKBUgHfs7ZmT+iU+KgsAWgmKET+0egZ\nABPA\r\n=tHsy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.14.5", "@babel/generator": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.14.5_1623280347004_0.24964669078660062", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-optimise-call-expression", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "f310a5121a3b9cc52d9ab19122bd729822dee171", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-E/z9rfbAOt1vDW1DR7k4SzhzotVV5+qMciWV6LaG1g4jeFrkDlJedjtV4h0i4Q/ITnUu+Pk08M7fczsB9GXBDw==", "signatures": [{"sig": "MEUCIQCfrb+qhTxIWFHEZEtFo8fThEC7wGVXhS5kB0PAMvcvpwIgfrC78vZk5HXvIcctdL2wVK1pbCDh1gNAbJfFXJXx1LI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSWCRA9TVsSAnZWagAAztoP/RYgSaUotP09iLUbOeMd\n9Ez8V1n7yI05JwaSbvRouEsuYaCsJTJHngbmo+mhFGgzPzNeh/Ez3sIFe8Ls\no7csqMRVJI/ScO8OBeE6h2ZPioZdU8nzxdAZoK1WG2OdRui1K6Cai6aOsaw+\nmFtxqvX0a+gSqWA638ffG2HA88nWHF4tWd574I9wfieeqbX264n0O/vRsHwN\n85GfwXvYZmAkcmHpLu0Rr/cIN5U5EIr4mQ3kRnP7sOVTfNYlZGnODBwcsyq7\n4CloCZ1jMxw/wGrdH4aLU53QGjxogqEeIbgCbVz+bMlN+auYVIJ75LeNDSsN\n+XMXGwvPV4dXfL1zrhzfHMSvVSZROWPCJrp6Y3xy3QEZrdXqrpNHCjKo8ybY\n7QPHMnJDhETpmAMovPAzsOa3SxOm/H27HGI3hIP3bilELmO4KPE9WvX+5jMB\nsw4MjvPqr/pCG65LEfIz+GJYwSFGSwTRoAI2b9Hr9L5bPCLJzAOMIJRaQQkg\n7EgAxTmveESy0VzGsXWEPWiqRPul8d0yDF16TicN7nYMv8YEl4dlSTzyy8RT\ne8wWwCfwVhVheHAizPe4s8z2J/L5XyHDmOcHgQCY3xl0UthjUNnGu4BIoLCk\nJCxXVL+7snBbtn6uzmC5lOltKrzNVNDDWe5lMg+y7Pxypzpd2cIzhChpEm9m\n2eY8\r\n=IRnL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.15.4", "@babel/generator": "7.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.15.4_1630618774331_0.9425369643135284", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-optimise-call-expression", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "cecdb145d70c54096b1564f8e9f10cd7d193b338", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-SuI467Gi2V8fkofm2JPnZzB/SUuXoJA5zXe/xzyPP2M04686RzFKFHPK6HDVN6JvWBIEW8tt9hPR7fXdn2Lgpw==", "signatures": [{"sig": "MEUCIBNFR67RMG+V0XWxJp6i8asaaOteHqQD7xDYIwvccxroAiEA8NZ6qkiXkBxZDW52exOs96xtz/w1Hjgup56Cog8Cs6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDbCRA9TVsSAnZWagAA1GYP/R5k1TC0iLSj7kbCiFh6\nfFl0jRTVQGDrF10SMVi91chhnf1c253xGEfqO/GlTEgbRjJSQCOy854bejZ9\nDGaBLncCtcYGnkkLACdyZ5WRBy2VBFCqnXX9Z3uS1kUzNJvmdLfz8sdf4Rim\nezZt4vUWvPOSfloQ+whPfV8Qj2CzX/kYin1if16ymdfCQTzDaR8b78vIGIIH\nYSfF1FVKDwYSd4R3vF9WV0e8jJ4Fl9kheGZ4DHxVOC8aiKoUHh8yELj1UMY/\nlvnsEvqW8lmFLIuC8QzVjk4yJBFhPdn8w8DMlAy2L+IrlHjiVSphyZ2/XeKP\nIW4YsXnOLmHhGsS5WT1dWqfVgfnAw+yzx3dTFKuSZ90C0UfDlEei7G+N5BTU\nQqwSWLmzu//QyfddsIX3439iOrDedDpHzHB5jGvz6dHH6CBTimi1Tdu0cqsq\nKTHfGwiu0KJYRUsXR1TpIc0p9j3re7QFCbirohWWrnubqGBTPtWE1d2OrbRe\n5Z5H4VOgHBjwYBiBhgm3w3jgvFSHn1CqHkQK9399em+rCFe86Z573TM0dMph\nNmasdWWvJjFOPuAHdvboT5bwahjdZWR7kt8Y+Hw9qYj0RBDcjeJ+YVTzY0Em\nQ5MxTnSd2wCIszZc+XXtxztaoeOZGnRVFBExKKQz5lSJahlXUDf3PoiStUdZ\nUriY\r\n=GJMx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.16.0", "@babel/generator": "^7.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.16.0_1635551262610_0.9332946068996792", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-optimise-call-expression", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "a34e3560605abbd31a18546bd2aad3e6d9a174f2", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-EtgBhg7rd/JcnpZFXpBy0ze1YRfdm7BnBX4uKMBd3ixa3RGAE002JZB66FJyNH7g0F38U05pXmA5P8cBh7z+1w==", "signatures": [{"sig": "MEUCIQCRJRHcHVMUkLtV8jV5tsVNz70w5AGTHLxC4mQ8gPNl+AIgb7eCCocTXn2ktPZ4OG0xUg3Jpx5Hj/7nTNYizhH4ujk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0+CRA9TVsSAnZWagAAu5EP/2yeKBStzykuvDmwqrgM\n0KRnFdqGF8kHiGLuY3gq8j9nypMMeQSe0Ui77bimXLUmeHoIuBDEQj005orO\nxI7zAVxBgqhfo0kYj6h8ry7qu/f+YgGGAOl5mExhsCIlEO6zh3l8xs6zQ/B3\ndkMTqWZeTCncG+zuAH3TIGEGEHnvAmxsYQKa9e5kiBTF8V9/KI9pFyU32+p4\n2BP3oU089tK/lkWwY8ERr31sFHogBfq0+dzKJdjbeHPlIPT1sArh+sv62yLQ\ni82YfgpkVAr+8mzPDnXPEBC+0l0ZC40vjwIxk7bOe1N/qqjqA419+0F2wh81\nFhcxoHUfL0+F02deA/wpalzsI4vzaawH5jhEddGgtIO4I++LYqW1lQZ2BSgI\nq8r4pvxSvX0HhkyCMcVo3mN4QGulMypRV49g0pGad+W+nCZqBow44Y3xL4cj\nqJ4ek5OuV0v7GLg7c2UYs/RcAZ8COpiCxGSSjENY5oPme2YuO4gceBOQKJq5\nWTwSY+DH/to0enc8hjxUjPrEyEEDIQc3HTHrkx9D+zIGSMOF3rGPvF6q2rEj\nIQbVUnfS0h4UjxGTgf0aza7MGRPsYtrnr3x+lE5IHsbGWFdHhdpgOF5GoW4c\n316QbtbCSG+CNGbeB0Lz7RcQZG6JFxfvLn6KtyMMgiiTf1XtrSV1yMZU0Ocd\ncM+f\r\n=l6ff\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.16.7", "@babel/generator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.16.7_1640910142502_0.480609130816372", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-optimise-call-expression", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "9369aa943ee7da47edab2cb4e838acf09d290ffe", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==", "signatures": [{"sig": "MEYCIQCr7I3V9Z/NKj7NqBUUDkvfWZJS2prYfZP4aQatLrsfRgIhAJRN8EoXHY2tWNOHQQhQj102L1YWVh8ZLZbZoAmicV/j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUghAAiVaZl/qA4inU8Qf9PKrXDYV983FhWvsLYg0ndt8HCLPX6ed8\r\nuMf6EI8fP+3Nr4MD+J7O0PqsGmFqa9bLTiTQcgMqELkMLI7HCpy+1v+MupqY\r\nbwLDsDJrsWGgQUz5Z/kzdkLWoTXTspz0y5honwOeBZPO6ykZZX7zjrgRRil2\r\nc1Ge1NWSnbqKkdweJzI+RFX0ee/dNL0GuZTu3HCKKPpxxOd4ea8tuFD7d3tm\r\nmHu2qdC3/BkrgUkASONnJ3cNB6LQR0fW/ZMCtLrGphZsyPdreNezO5cVOdBy\r\n+cQDEF2L38Ei0HaDaYLqoFsW7HfwskSMgaR3XpYvpo0NkNu3oJoMC7Pl62zn\r\nRvZI7tE58Z6spoFyHSkCg6QJsA0r25MIds5IZPes5YaCQ6sV/a5dwCy8DjJd\r\nU5Ii5DAdMfXKccITNv38mu9V6JRsRPw7jbBJtwCEYWn+hevLDyqEfTP3/Q+V\r\nMKAsEvuzGE6AoZ+41WcSSKw+J3htMrzzzK61/++Hkwj20eaFO62omGXVc4kh\r\nswBvEuk88VuXCREMULgxJobfD4sfhpkONlpM8wFC7FrTA5990FUAdxBbFKgl\r\nrYv44lKzn3HuK9omFQ55FbLT+RqYCkl3qQYBbbnlMqWCoWwUUmMgGm67njx1\r\nAw8/zzTtirOtnU3qrJrC29YqQhKt4lolRh0=\r\n=gLZf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.18.6", "@babel/generator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.18.6_1656359411075_0.24132980210512378", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-optimise-call-expression", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "7fbfd5b024fa7f9b6b5afbcc45b209ad2d0fe4d1", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-dLFEJL4mQuLcxwR5NetEO5TJjMyvHyT/7GVKt5hii1QnxsbuSgb2+r8TpBvTKOm6FzCXetArjziY9IgnSPy1Ug==", "signatures": [{"sig": "MEQCIGU7SYM4F6gqV0K4RwqUUjMxnjVviGbySdsJlubKnPXVAiB+ISajhpx4q6ni3npAzu8nxm0vqbN4sJoEaPyOzu4U1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodEQ//QVeNeIjNdjZ34vGijICe37Uw6HXVq7QRZSQ8llBMa0878URZ\r\nrZG/AFNKCnbP55azuvZTkb3D9ByySr0USkUmm29kBS7XPSbjkfV7mGPwfrOA\r\n8P5BZPIfqhidJyHOt6mKHnApQfqh4OuETsOa0oGkuKSHmq42qW+ad94kia6/\r\nGavxJ5ms/RCvlLsHpR5f5bEiqFmN7tcPMbvh1rIWq6f6+azHq2JKdOgNXZ1C\r\ndXav9WIRfkcKi0l+JHcUvzvo/vt8TOI91vwhzQthzxTp30jm3p+naFerlSHN\r\nXxc6hXX9QaS/OsASAQzCSF/+cYAeXsBBWTHv/Jxx6E3MvWXNoyAuCV58HVJu\r\nuImb4kRTH+nNfi6wEnPMRDgtBbl9ahqU5cMa5U8Gz+dh0Es/lr1ETljnHUw7\r\nRdJhWyOiL8G1pVXGFYHSHURrU+qrR8A1JbvFiBSXJh9ZmetuEYpYJ3jD7jAF\r\nZwS8WjxgM8zo9tnup01udYoxt+97kUtQMRKRL6XQGukxKbudwbLgpGYpuQGV\r\n/VdGNTWYFThwCAKSHW4zBWUYoOxKg5WqlMreJdEg3z8C1L24BSbA9JSKlHhJ\r\nzpvXQ1zra7L2Fn/BhJsyUS0jYopG7Civ5VvxcEymMNLVx0Xsbcwxpy1MXb1n\r\nx3ELhcC0DzIoBKyMhobLn78qB5xu1DVdxKI=\r\n=rhAi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.21.4-esm", "@babel/generator": "^7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.21.4-esm_1680617376537_0.9027379089866869", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-optimise-call-expression", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "0e167bde2cc96dae96c01f4d6574f79ae4ea69da", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-3/wHlkqzSOFCS0JIY4EOW740uNQ1lO7fvJRbHb6aO67aG8P1h6q/BwEpuElzj/G5EAspI1321jhNSQ1JD5M5aA==", "signatures": [{"sig": "MEUCIDupCqMxoKcg6E/xAvj5rmxfCjv2CYQmi1732867c2jcAiEA0bAH1KVsAvUIC1BZubVDXzQLtmCk3MqH6Z6FQSLTcsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9+g/+J7aqKTKM4dc8xiOipzKHFbQDoFGctpZxhg228uqAXcYG9lmS\r\nei7fzUDOBEuN1RETpJpeRIl2y71lA0JXLy5RAmUSCNfW5IWpIVCdd+c/Xr+U\r\nmdYqtYNR/aBHEDVTZIvGrwK//iUMkdu/GeTWowok7JQTodQoI58yVdmkOkfB\r\nzjnEIfwZrJhXDeKL4xKv/jM6ybUllt8ecjxvkEF1BEabZVfq+hY/THvW8aqE\r\n1xVUz1rleSYIQmzWqFzGZwGPxJ+/weQlWZQXN+vVExEjQeYCTTW5E0OQihH4\r\nQ3CNKdVS2HTVmH4uT5lKmTVdwbyP/m4j5hLkUmSEvW6lloXq1+JJ46KJxFxB\r\n5VXFBv6OHJ3+wsqb9kXpXbjZuNP+fDPqSMn9x3PTcs9sgbuooc0QK5+r596y\r\nw35QHRk/OojN8OBtr0Z5YMesOcB1G6HSduvpQboCHlv/u5qx4lJvMZEYdfMx\r\nXocnzMDyI1jUw2KDJierDVGBT0ASPPcF0QhIrsPsSVixC/QO0MsLUBx+nUVY\r\nFZHz8DRpuYDLkJ88EFGc2Apg3B4VrRq1FipP9vndjBDD7VkjmwKx58fwcsT+\r\nJQllFLPcFX+5FCR/eQSLoXrjAGGkA+Af6mVzMJQ0SP78okQvg5khvj3X+QWg\r\ntfEHuT2MFTPSt1eoMubOdodBOhthAe1Y6ho=\r\n=nKpO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.21.4-esm.1", "@babel/generator": "^7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.21.4-esm.1_1680618089682_0.15859739572207254", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-optimise-call-expression", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "d1ea9a2ad1b73b55802c1a8069db19a0e7edbaf0", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-d+ljBeq/RP7leTzkM6LnxHIJEIKDTh8g9tIe4Me4go4UHmUKx2IXKpm712K3cxVNwzgLv4zYaic/9kkxUSbvHA==", "signatures": [{"sig": "MEUCIQCEj5bq+lPSUviUVf/wcmc4At3JKt73GVt5e5OlGNZdlAIgVAJlQNo15s7RbhOvJ8xNQK6b0Mi6apWM3nb80b/NWNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDahACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm6A//bmiIoyRwtszUUcQVdlBs0RgAd4SaoqYSFVlREBXU8k3PKfnC\r\nSkhezOz6Qs81eT9BV+DIYU1mVjP9sPXdDt1/OvkTTQysGFeLEVyyXaqN/jgq\r\nGSfdWKiBjgymhUvvkZzEMXNxXu00RPHSVAVBn20y0VaVdO9rYEzZ+ANzhoHH\r\ndUM+bM/sd3MPNDrHLi4W6NGaBVgQU58VX6eyuaWw6l4SjPIOpVgAix+B0gJ6\r\nYgxS83RBdIAGLnQ+U1igN70g17RKvi8Vam2w72CJZmGAm95ImMXxDvBeP1nJ\r\nOsjrS3GZWrMqY40ya0AAJkCDeeSqvhMui80xC4sGaWnJR9ZwWqFeZ9+A9KbF\r\nWh9Px18ZFQn+TYLprcITSOuQ7l2wDTgN1+4VMKCm8EpWCiHB/ZqhjvvT2MtB\r\nwPH04lVDBnSGLo1eWX2NZp8hTcSDXjc7/zKAZuvavn0FUcuMZEk08nNGEQTa\r\nWAvv9ipAcCen6QSD3JnLfYBWjn74hrxAi3Bj80clNmXze7qtOAI6uAxkQ22Q\r\nYkBib/LTTdKLMnHEs+OgMGRzaDAZbKujWhcyh3dXgoGsEhr9/LLNA01svovd\r\nRB4Un5hEw1rHULbet9Aj3Gaj1i5vkCx6odI8ZgFeVAgKq7Oisw1zFdb8OoRv\r\nPJCTyo/+W5+l9nlvGR3FwpN0lRkdp0LJEBM=\r\n=8wZw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.21.4-esm.2", "@babel/generator": "7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.21.4-esm.2_1680619169408_0.713665671490523", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-optimise-call-expression", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "1cbd89d9c9032f07098ea8b88512da0e983a303f", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-41poYAMwD2hIxveOd0+ftlIp6NJOm1ek4pxuqzKVm3xQDGKypeGSX2z/V/5uwF+kXOfDN3yKkuiZmXUlMQBGpQ==", "signatures": [{"sig": "MEYCIQCZ0aUt51YK18b6SH1WV7I0YfOZereEluH6rKbqqLM/QgIhAL9KK/1lKRq5SGEoSJb0CLLuk6UA4GUB0TPkJROEjQjR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdNw//Q0RqsA3/JM1jiUodXTg3ZBgx3fP20GUJi+UopTrB+6Z6owUN\r\nFsotW6STuUUCca73SpWsZ3qDuM75s9PLapTFm2RbTbPdKw9L/UPZflfTnWCq\r\nKlE9YlTI6AEn+05e14z3qMsIQRJbay1v8nu1Xck60Bo1rVMYb6/8nQO1CCen\r\nP9TwqQ7YTZNKxaO+MuhrlxPlFWvT5FwOvjFxXrZbyuWWRYb5re7IqPhS26bY\r\n88JMOyGT9P+/KTZrkCbhJt8tOOKvj6EjEan74uEqifMmpey7Sj0QDI7F4wl3\r\nwWGiINcfIMhhvojvqzgGaaXH8KvwBPqt4Ql+D0WaFK+r876+UMWgXYrFrTNS\r\nDtAj1ZYwAmXwkTLO8kj+CB0OvBlt9kk/dPf8NERl+sPzGwh6hrg9w+wQat2p\r\nYAGV9/lNTuULXYOETYfCUmS5CgcL1ZbU+Zkr3JpPW5CkqxVTA9dr2XlobQpo\r\nFQzaYw8hAvrBB8pvv24m3spCsl8DYdZ8GnBfqrP/V4R5U2/IaMF6GWyUqNMc\r\nxIPxI9UlASFZ1lIiM0KKUNmwsLhnR7jXM41LL/fXHWH8b9m8+pWh43N9CSO0\r\nDsc4Q5fw/d8jsdujKYn88M19N3nAYc2W2ETQuWco6rcbr9nUUWlsQksf14KP\r\n3Cfur2jFVhVzxuYfvfV1G9DacotvJNDXI/4=\r\n=3wB3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.21.4-esm.3", "@babel/generator": "7.21.4-esm.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.21.4-esm.3_1680620178092_0.16513873945386814", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-optimise-call-expression", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "332953e033945996fb0e36e14e3b6d2da11899a9", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-o32oHxTZtPn27O//V9sC2qYF6u2aa/l3XFWJVviOej3o1fQZSU4TI62Y8gtvQ5ALYSCjlmS3Ac944waa9ceOHg==", "signatures": [{"sig": "MEYCIQD7/lUzmf0LZk8KVV1Qvzd1t8vSOdkZMeTIqHMOJEqKYQIhAKq7C8lO0Cn6OaUwFGkCwubbCwSK4jUGQm1ea1jXitxW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhxA/+O/zsSiMv8Kejit2HkDgk5hCjaAxaMa5jBo3+jwWbL4/IwePQ\r\nAniN1SrYY1+EzLwKL3jifoF0SoDWBGciQ7r8AJGOhFSs/KajJf3LdDLfGEaS\r\naVO3E9/a41GSG1RnJjLSrLFSO4PM+Kl1J5HTYQ65X+GcrgX++wJ3Bq4WXl0O\r\nU2//NuE2269vAkdgFlHt3ukfDJcsSIPctFSp8Uss7oXdD0AmLHR2tpBKOio8\r\nzWHm29pc1vcPIPxTDQbwuTJhoREYghhT4fn7cYYCl1zHogj+Qq1kltilLK6b\r\ns6W4YQmWme0viHWLd/3fWbdQXUZxYPpb10xs+UX47RWfDuQYmR/LFwczWbU0\r\ndJl/vFZao1x8Cqf9zNM7NdZUejfuhBVlpbnqgHEk54uRiB46B8/5sbcFb2CO\r\nqoalRC9eGhOrToriMix1zKJd8pLLKGlPGVUOmR/HQzoHgn+GM7+xKQfP5mat\r\netTlSc0TzTJxLQiSdCh4xg5lyfTGzPoNsxolv3gzxeuG3QTAWEyhTWSabW9f\r\nRP0ekxUmLEiYX4pOTxKis6UQco4pTEp9u+/MHAD/o1K7494/uVa2MbNq/xgF\r\njmImNN6TlokPFc6zQ0O7I2RagaDOjT9FbfUXBEmIxAs6tnkEyBBggWskRumg\r\nVtfKZxR3zTiqOxPWPMRQZNdj2MUT9zIzNRg=\r\n=IBx0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.21.4-esm.4", "@babel/generator": "7.21.4-esm.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.21.4-esm.4_1680621209898_0.7375342140839225", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-optimise-call-expression", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "f21531a9ccbff644fdd156b4077c16ff0c3f609e", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==", "signatures": [{"sig": "MEUCIEO+CRPAYfFFAzUiBXsntMHC0B8vbkZNdZMN5UYaorluAiEAlNUzb8YNjXskuCkYFNwSta6ekp6enLXDndYbOt2ljLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6659}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.22.5", "@babel/generator": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.22.5_1686248487376_0.6490337714631778", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "235b5912285ac1ad8d75dc3f78948fa3759103fd", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-d0qPNGTw1NNDwShFe51t2zR+ucYa9zkpQFI6fwrEsHw/XKg0u91ETWqIzjmB+h7zd04t+coYiAsHJGklrGT1ig==", "signatures": [{"sig": "MEQCIGUAlAJyZKM1rIExR69oVmY9w/7d72+GFXqEGwaTNv6GAiA/jJaGLNkE8jFOwJeOkm+8ty5EtWdMCCyyT+Fmw7vA6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.0", "@babel/generator": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.0_1689861605046_0.2212980021215698", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "510dbdbb19594d805ea326cd72c8cf856c77be5f", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-5eS23IlmY1/vmdVvf8nnOBWv7I2T4YiEMycoWlWicZReaNcWwgfZN5hjbMO9y3D6iqqUfOnl0kR6ofFvua2O7w==", "signatures": [{"sig": "MEYCIQDDJdZ+7V7PPk50QRJh598BF9m7G1R+O0ZF+asOZIruAgIhAL9LKyzQ5toCuoaoFa+wbfRs0L/Q3AvzEaTEQBB81I3x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.1", "@babel/generator": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.1_1690221132481_0.5634442945420544", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "cee74afd4476da4255bbb531b42908e65b684590", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-gtAf+F4H0xYjklnVKiLje8eGK1iLmD0ep93c50/9qQR2BdYImMfnT7R3HY4viwaN4mngXq5/nf48rkYpR2/nYQ==", "signatures": [{"sig": "MEUCIQDDstSLjk8LI8hhGENGrxvyVSqwK50bwadYeEk+TrCz0AIgHdbGEkmgEZn30V5lV1TIFHEk1bRHgaPtDzDsi2YMqBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.2", "@babel/generator": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.2_1691594104398_0.29828981936070287", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "26520f346fadc0a5bd87a42e6f537cb3b61b1eb4", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-c+DlmxhN+p+ba0rRxITdtJXQFCgsr9O9J4b7QELERQ7hijRPVxB+pcpye/NUOXsVdBAGyI7w3yzw/6eEqCLzqQ==", "signatures": [{"sig": "MEUCIBSxOD9G+rKNcr7+xErlxZ/kYkBzioJEoZFkA+497v6KAiEA2j2Y5X4xDWpZhiBVhqYwVZLCW6L8fq1jSAnOKfwe3d8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.3", "@babel/generator": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.3_1695740228191_0.5697979954004138", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "74b026e5157082d17fe99922e9519ac6502288e0", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-xwRmm0gOvbqwOYMViGYpHRdL+Q1ia8SimJzClapyBU9j/SKRzcQgWDIDMSekM3lZXy6VbpD9cgAdFGSxmnT2Ow==", "signatures": [{"sig": "MEUCIFMFfrLutTs2/uXEAUR4Pe8cueVDHJuiSKeEWIIfwjdUAiEAzC+b5y/Lw4J1FnGJmrpP7du8wRyYMdxOVVVCjUUbL4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.4", "@babel/generator": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.4_1697076388360_0.7705153035965357", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "268560472597db4b8a186049b685b5aafbee73a1", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-oBQ178ReZKRXR6lay2tJXkEvu/wxPQ/2swUoX8WDRdnft5EmQzVTfMpkFEdQe24FWPGxTeqEBymOWydGZqnTqg==", "signatures": [{"sig": "MEUCIC02fyTiScpRUuuOfAFBFcCLgszRWhbIVOsTFvQ/m7QnAiEAgE6m+q86VhSdInjM7USRyvCTlA3LbgQx+m7loRSHVJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.5", "@babel/generator": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.5_1702307948265_0.01575163863750495", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "296fde4a0700b6040d474d4b559d70ec82bab9ac", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-LFdf2224h7jD3KWNkZNdm2XhfWgeOW43MXYnyG4buvgTL10OsjdiG68TZ+EkjWtqjuQ/H70IoN1E7MHMaFX3DA==", "signatures": [{"sig": "MEYCIQC4vq0lrTEqhf/UQnePt/p+ekgbL9R9Lf/KBdF8uPM6YAIhAP7yA1BwFP5HhxYbJPG6TfDH3eQYcziN5Rw5NLx9a8C1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.6", "@babel/generator": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.6_1706285656454_0.040869225073536075", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "360dbf8f2122e5d56d2384c228b45566a1aa584f", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-TH3ZW94KOuwjWCbBA2ZGcvQpGgpul4hPZ12zq6iDj07MM27eFtJPmsoas4DceEGgiH76XZGt9TyHnfzgkRSOhA==", "signatures": [{"sig": "MEQCIARKITcciERY9jt5f0UfcONXal081vusw80l3ZahAEMZAiBb74EboRNcxHb+AMaIwQAu8yG7YyJdIosRQHi72apxFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.7", "@babel/generator": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.7_1709129108325_0.9507977644907171", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "40f9f7a49f5fb30cd88c8a7f80033146bd019000", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-hSpvLUcPZsr7XuxvwLd54rXjk6qA0dKWKaXS6eNtr7WCurUC2d1c5nZncgHiJYzCzH3IeSX1Y7bXjIH+IV11Sg==", "signatures": [{"sig": "MEYCIQCxwV2OXcv8yDBUXI26nS9pYNFUmJQUYwvMqKmXx0tO8gIhAMQS1jloBVla79xGpPDn2LGYOhpuGKTjDvcxw+aUj+D4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.8", "@babel/generator": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.8_1712236798986_0.3516984378767851", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-optimise-call-expression", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "f7836e3ccca3dfa02f15d2bc8b794efe75a5256e", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-3SFDJRbx7KuPRl8XDUr8O7GAEB8iGyWPjLKJh/ywP/Iy9WOmEfMrsWbaZpvBu2HSYn4KQygIsz0O7m8y10ncMA==", "signatures": [{"sig": "MEQCIAog0VKgE0pR3F7iTjX82B1+q0h8pUvwZSpuI2nOEuywAiBDO6t3bt7q/cHbkGc56hwrax/avIy1hUFZeK/WWvqJfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55045}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.24.6", "@babel/generator": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.24.6_1716553481864_0.6115474586881438", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "ccd2de6228a56e5d6470578803fa56a9f29c59b0", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON><PERSON>qihn8ByiT2ufMB8LEnzK46Jc1t/WZUSSO85k1rosM0Y8Bhd9QRpQoMOypxu5867Eg39hdYMBzdHLtxxw==", "signatures": [{"sig": "MEYCIQDj+cvk4p9y+JHYvdyLYNXurI1HkZojzRtlp9b7bAaRVQIhALIYNtlYF1S2HSDLn2S6sOEo+uTzKQjpfZUFycFydpw6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56084}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.9", "@babel/generator": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.9_1717423473558_0.6680085200811869", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "e0948b78b42fbc159300ce1dcc53e06a6cb57f9a", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-pjUXX3ST9ae7Bp9h234aOGhFNEa7Ira/yMZ5Fx4idNMXNhd2PF2AEcoAxa4cwfIMJtOqX81V+cLl0ztFhomk6w==", "signatures": [{"sig": "MEYCIQD9fJgPIWA2e6Uj7N1+YajlP2AigwGh3zP1am+lRPhNcAIhAKW9Gli9sccBL9FPNrqpu8tduGv/66U+Lm44e9G9ylD6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56088}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.10", "@babel/generator": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.10_1717500016885_0.6157952566683105", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-optimise-call-expression", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "8b0a0456c92f6b323d27cfd00d1d664e76692a0f", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-jKiTsW2xmWwxT1ixIdfXUZp+P5yURx2suzLZr5Hi64rURpDYdMW0pv+Uf17EYk2Rd428Lx4tLsnjGJzYKDM/6A==", "signatures": [{"sig": "MEQCIHo0yXapPrsw5SsmvowPVu4qL/wE3Dy8pXgj5yx8xsW1AiA1Ly58+NntPF0uQ+9Xu1fdLcTjoiKJKMGSEi+F2S34GA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55045}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.24.7", "@babel/generator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.24.7_1717593330080_0.15235921381909323", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "b793d23e0611ae1d75c7d7c05a340c8fb7346960", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-BAmYI1zSXfQgVIAfMnFIp5e/czzaNWLdtV0MP8knbQhMWi23KWa7bht+trJ3HmBCEb6qRecqfeaxpO/SjxkhZg==", "signatures": [{"sig": "MEQCIC1SBQZaiWr8ZpI5E901EUY4RglODMFG1+ToR6/VXAauAiB50fSk83SdUoAbsY3OSCEpkFwOBYveaH0ZMlKqJVf+4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55977}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.11", "@babel/generator": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.11_1717751740385_0.7442459075760328", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "5cf76538eab302d506b58f74ef1fe5bebb8a39e6", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-Oj27JRZ6PDusLthW6WVdO8P4HV4mv9Ycz2xanCz2lb6za9hhR4XIQZRDo2UcEo43eCrPBpI3xlVmRtvpf1ssCQ==", "signatures": [{"sig": "MEQCICe67Tih273YdWu6DMepN0ISQPYoFvK8Q4p/mh3T3DT+AiAjsvfSoSdOE+tw45lkwvlwiM/U3GzMfB1LU5/9CLhj1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54111}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.12", "@babel/generator": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.12_1722015217030_0.03980766451726314", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-optimise-call-expression", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "1de1b99688e987af723eed44fa7fc0ee7b97d77a", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-VAwcwuYhv/AT+Vfr28c9y6SHzTan1ryqrydSTFGjU0uDJHw3uZ+PduI8plCLkRsDnqK2DMEDmwrOQRsK/Ykjng==", "signatures": [{"sig": "MEUCIB7p59EhshrzO8dsHwyjz9BNVXp9yCHjyknLsXlo0LkgAiEAzqYERixHIVAabGrQD8ORsmiU14IZx99I15KrCXjgxGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60646}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.25.7", "@babel/generator": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.25.7_1727882099055_0.8972122481623539", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-optimise-call-expression", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "3324ae50bae7e2ab3c33f60c9a877b6a0146b54e", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==", "signatures": [{"sig": "MEUCIDjtSrdtg/h5VpcHfxSAHqH+fXdxhVKaLQHBVpriOt0VAiEA5Pf7fwbtp39nZ48zRcW5G/z8eUsfJixTuGex02O5FV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6672}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.25.9", "@babel/generator": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.25.9_1729610477262_0.5717297805973938", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "da476056ecc92487908327f1dd9a4491d28d395c", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-LQfOgv3x5AcpsbzbBKIv7Bt4xbBmPwa52ST5luYoyBTjwFeDyyC8bIDoMXtiS8BASf1RsGdxH25TCXRQlYgw/A==", "signatures": [{"sig": "MEUCIQDArOrouxT4xSryfMhlZtwit9iXLSZJEkWg0ZUhLKX2IwIgdL2VmKox6/Mv6v+cuKpviI7OWAR3YdNj7gcl7lAAvmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.13", "@babel/generator": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.13_1729864458492_0.2798698983843986", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "6fb5b2ec4a8c1ca3ec21e782d834b728e0ec51f0", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-0Ag8E7fxYF2/N7PsZS6mQFnDQR6D47Eb8dWL+/Wp7wcdAIkJPtuUuZcs+hU9Cmg454rdIjHelPSeSki6GM9XUw==", "signatures": [{"sig": "MEYCIQClItgYXdbD2Q0d5qOK6BgDcRyq6WTuJgDI6VDxwGXGyAIhAPkJ2qqTy+2i3LrnCV3hzdrhWDqioi33Mc0h4kSXSW8/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.14", "@babel/generator": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.14_1733504049455_0.1200491394586134", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "8e482716b9ac47af220856c1b4dff90d0d03a0ee", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-tlkjzCVPvVwJUXvaj3fv+p3DKMjVdIILGoD0dOPnPk7vBzWUsTDdFphSxNhB93M2mxWvf1nL6OpG6oIIJPvlEg==", "signatures": [{"sig": "MEYCIQD8fizJ5bdsx0F6CrTHizrR5OgiHmDETlisLI0K9IEdBwIhAIbtgg1j0xinsfVxbX9IV5gQHYg9aL1wwPXv4Bsigat4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.15", "@babel/generator": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.15_1736529875968_0.008975974066812054", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "bcc317112f9252575d2b4ad012086947c8fa8449", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-hnrbcKY9rREPFIzDiWc25/nvILDJ/lsxbFLX48TvzU7bgv5Klpk28FjNxy9vvWVO31hEzbfJ9wTdM28l+dFTEQ==", "signatures": [{"sig": "MEYCIQD+PNunpty38gyfLStjqCOPoEv0k8rjwEIFqbHTnvMfKAIhANhhI/TnPZT7Sd+dijNpV6Tuh0tcDBkoLpGOXXEOuAWV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.16", "@babel/generator": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.16_1739534351923_0.08205734804146037", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "3591589ed8ba3cec814e76392a736baedd9bef35", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-a7TSBZpm0I7KFCDRdjyt14B3D78vA+SPTfMPIptxvZb4HB3XA3aKrIok/HxgJliym+X3phsszPC+hiqKwVoz2Q==", "signatures": [{"sig": "MEYCIQDC8N4CWsVn7QCKfwiWf5OI1QhzPNtZuLkVqnsns/8gNgIhALJ0nR/iypuJzcIh8vtf+gyFzvrKxpPn2zJnZunVQAob", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-alpha.17", "@babel/generator": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-alpha.17_1741717505039_0.6833615762060867", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-optimise-call-expression", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "c65221b61a643f3e62705e5dd2b5f115e35f9200", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==", "signatures": [{"sig": "MEUCIAVMFsXKdkSBC/iGpkpNUxj/S5sCgYmnff3GytiSZtqXAiEA8EyL5e6zif2fQ1NYxwOntehqbeJ+5p+u8I5g8kvzgHs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6672}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.27.1", "@babel/generator": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_7.27.1_1746025741626_0.049603959094749905", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-optimise-call-expression@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "dist": {"shasum": "b14404cc36d714f4102ca451574e7fce64d00f04", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-D4zS5Gmei0lSJLazoNrc58BPNk5RGOA4IIiSt3Uo+ViI6bDK7v4RTeR0WoHe+T64I12OVNNQcx90xOS7vpOnBw==", "signatures": [{"sig": "MEYCIQC74B/Zu1xNzxNF8Cdz/Qzt89EiBRJ6DzktbcVS80ThTgIhAK+3ovCZ9ekmyMnRoIAqViRJ4hAUwTVHAyQ99oEYFPwh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7696}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^8.0.0-beta.0", "@babel/generator": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-optimise-call-expression_8.0.0-beta.0_1748620273133_0.009512621119438291", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-optimise-call-expression", "version": "8.0.0-beta.1", "description": "Helper function to optimise call expression", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-optimise-call-expression"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/types": "^8.0.0-beta.1"}, "devDependencies": {"@babel/generator": "^8.0.0-beta.1", "@babel/parser": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-optimise-call-expression@8.0.0-beta.1", "dist": {"shasum": "9445664afbfde9f57021aca9f5edef681648347a", "integrity": "sha512-wvJYfCOLHYI/YkK0Pf8p+/ycjTqfw2yIWUlRrY09Y9OzwVdgIo4XInwkrlIT9DClaslI9AWCB++UVtXvkejUFA==", "tarball": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 7696, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDSgMiOV6Yd0+GsvCNfjoZ0bPvKmdSQRevjdNx1YAAHAAiAP2ykRbSiP34gwHbzvVBYCkrzgcrkDtIkKRGlCiX8vkQ=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-optimise-call-expression_8.0.0-beta.1_1751447063974_0.8901504482246636"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:05.730Z", "modified": "2025-07-02T09:04:24.680Z", "7.0.0-beta.4": "2017-10-30T18:35:05.730Z", "7.0.0-beta.5": "2017-10-30T20:56:45.883Z", "7.0.0-beta.31": "2017-11-03T20:03:42.387Z", "7.0.0-beta.32": "2017-11-12T13:33:35.014Z", "7.0.0-beta.33": "2017-12-01T14:28:41.037Z", "7.0.0-beta.34": "2017-12-02T14:39:41.486Z", "7.0.0-beta.35": "2017-12-14T21:48:02.237Z", "7.0.0-beta.36": "2017-12-25T19:05:01.495Z", "7.0.0-beta.37": "2018-01-08T16:02:44.263Z", "7.0.0-beta.38": "2018-01-17T16:32:11.903Z", "7.0.0-beta.39": "2018-01-30T20:27:45.013Z", "7.0.0-beta.40": "2018-02-12T16:41:57.072Z", "7.0.0-beta.41": "2018-03-14T16:25:45.422Z", "7.0.0-beta.42": "2018-03-15T20:50:20.317Z", "7.0.0-beta.43": "2018-04-02T16:48:09.253Z", "7.0.0-beta.44": "2018-04-02T22:19:50.779Z", "7.0.0-beta.45": "2018-04-23T01:55:53.974Z", "7.0.0-beta.46": "2018-04-23T04:30:17.983Z", "7.0.0-beta.47": "2018-05-15T00:07:46.779Z", "7.0.0-beta.48": "2018-05-24T19:21:07.667Z", "7.0.0-beta.49": "2018-05-25T16:00:50.474Z", "7.0.0-beta.50": "2018-06-12T19:46:54.807Z", "7.0.0-beta.51": "2018-06-12T21:19:23.637Z", "7.0.0-beta.52": "2018-07-06T00:59:14.212Z", "7.0.0-beta.53": "2018-07-11T13:40:03.841Z", "7.0.0-beta.54": "2018-07-16T17:59:54.399Z", "7.0.0-beta.55": "2018-07-28T22:06:58.015Z", "7.0.0-beta.56": "2018-08-04T01:03:50.543Z", "7.0.0-rc.0": "2018-08-09T15:57:00.293Z", "7.0.0-rc.1": "2018-08-09T20:06:49.568Z", "7.0.0-rc.2": "2018-08-21T19:22:51.043Z", "7.0.0-rc.3": "2018-08-24T18:06:50.952Z", "7.0.0-rc.4": "2018-08-27T16:43:01.398Z", "7.0.0": "2018-08-27T21:42:08.785Z", "7.7.0": "2019-11-05T10:53:29.110Z", "7.7.4": "2019-11-22T23:33:23.577Z", "7.8.0": "2020-01-12T00:16:48.025Z", "7.8.3": "2020-01-13T21:41:51.819Z", "7.10.0": "2020-05-26T21:43:36.004Z", "7.10.1": "2020-05-27T22:07:48.073Z", "7.10.3": "2020-06-19T20:54:32.288Z", "7.10.4": "2020-06-30T13:12:26.434Z", "7.12.7": "2020-11-20T21:05:50.228Z", "7.12.10": "2020-12-09T22:48:03.207Z", "7.12.13": "2021-02-03T01:10:13.740Z", "7.14.5": "2021-06-09T23:12:27.156Z", "7.15.4": "2021-09-02T21:39:34.492Z", "7.16.0": "2021-10-29T23:47:42.767Z", "7.16.7": "2021-12-31T00:22:22.643Z", "7.18.6": "2022-06-27T19:50:11.210Z", "7.21.4-esm": "2023-04-04T14:09:36.688Z", "7.21.4-esm.1": "2023-04-04T14:21:30.875Z", "7.21.4-esm.2": "2023-04-04T14:39:29.565Z", "7.21.4-esm.3": "2023-04-04T14:56:18.244Z", "7.21.4-esm.4": "2023-04-04T15:13:30.260Z", "7.22.5": "2023-06-08T18:21:27.543Z", "8.0.0-alpha.0": "2023-07-20T14:00:05.210Z", "8.0.0-alpha.1": "2023-07-24T17:52:12.629Z", "8.0.0-alpha.2": "2023-08-09T15:15:04.594Z", "8.0.0-alpha.3": "2023-09-26T14:57:08.438Z", "8.0.0-alpha.4": "2023-10-12T02:06:28.577Z", "8.0.0-alpha.5": "2023-12-11T15:19:08.473Z", "8.0.0-alpha.6": "2024-01-26T16:14:16.637Z", "8.0.0-alpha.7": "2024-02-28T14:05:08.519Z", "8.0.0-alpha.8": "2024-04-04T13:19:59.137Z", "7.24.6": "2024-05-24T12:24:42.024Z", "8.0.0-alpha.9": "2024-06-03T14:04:33.794Z", "8.0.0-alpha.10": "2024-06-04T11:20:17.089Z", "7.24.7": "2024-06-05T13:15:30.250Z", "8.0.0-alpha.11": "2024-06-07T09:15:40.553Z", "8.0.0-alpha.12": "2024-07-26T17:33:37.254Z", "7.25.7": "2024-10-02T15:14:59.274Z", "7.25.9": "2024-10-22T15:21:17.442Z", "8.0.0-alpha.13": "2024-10-25T13:54:18.654Z", "8.0.0-alpha.14": "2024-12-06T16:54:09.629Z", "8.0.0-alpha.15": "2025-01-10T17:24:36.167Z", "8.0.0-alpha.16": "2025-02-14T11:59:12.116Z", "8.0.0-alpha.17": "2025-03-11T18:25:05.190Z", "7.27.1": "2025-04-30T15:09:01.801Z", "8.0.0-beta.0": "2025-05-30T15:51:13.303Z", "8.0.0-beta.1": "2025-07-02T09:04:24.131Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-optimise-call-expression"}, "description": "Helper function to optimise call expression", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}