{"_id": "@types/tough-cookie", "_rev": "552-84d370e2e2b80d2705ea4a5a8349d7ef", "name": "@types/tough-cookie", "dist-tags": {"ts2.2": "2.3.5", "ts2.3": "2.3.5", "ts2.4": "2.3.5", "ts2.5": "2.3.5", "ts2.6": "2.3.5", "ts2.7": "2.3.5", "ts2.8": "4.0.0", "ts2.9": "4.0.0", "ts3.0": "4.0.0", "ts3.1": "4.0.0", "ts3.2": "4.0.0", "ts3.3": "4.0.0", "ts3.4": "4.0.0", "ts3.5": "4.0.0", "ts3.6": "4.0.1", "ts3.7": "4.0.1", "ts3.8": "4.0.1", "ts3.9": "4.0.2", "ts4.0": "4.0.2", "ts4.1": "4.0.2", "ts4.2": "4.0.2", "ts4.3": "4.0.3", "ts4.4": "4.0.3", "ts5.6": "4.0.5", "latest": "4.0.5", "ts4.5": "4.0.5", "ts4.6": "4.0.5", "ts4.7": "4.0.5", "ts4.8": "4.0.5", "ts4.9": "4.0.5", "ts5.0": "4.0.5", "ts5.1": "4.0.5", "ts5.2": "4.0.5", "ts5.3": "4.0.5", "ts5.4": "4.0.5", "ts5.5": "4.0.5", "ts5.7": "4.0.5"}, "versions": {"2.3.0": {"name": "@types/tough-cookie", "version": "2.3.0", "license": "MIT", "_id": "@types/tough-cookie@2.3.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>"}], "dist": {"shasum": "1f4432df50c5d1b5a9a237ab745f3b449a0c2722", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.0.tgz", "integrity": "sha512-bJgf+gJF+zohYkhOOdY7cXVKa0jMZGICqIcdPRb1SFyPHI4sKau0tXTRLmT2T4S5bjx6cY9AuoMkB5v+TIL4wA==", "signatures": [{"sig": "MEUCIBigncPpXRVwDEAcXVrD1EVH0Hvnr8YJeU9+LwM2zxavAiEA8ahF0G/Gvng+quMUzeQpzgNat9sD963HBhgYABE9C6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.0.tgz_1496293270960_0.44910892681218684", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d6a239b5d6e7cceccb3f3f3d4a77b5895a4cf4ada36aff02e6c8d13767fe2994"}, "2.3.1": {"name": "@types/tough-cookie", "version": "2.3.1", "license": "MIT", "_id": "@types/tough-cookie@2.3.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>"}], "dist": {"shasum": "2688836dc4535d3156d1789e5ee58d068b992715", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.1.tgz", "integrity": "sha512-Ei8DBGk7P2BNGMglIqnFJKYY//0pKV1ctXCbf+hjdVF/2wxYgfuUSZOonVrYu3sy8wToKtnDQWE5rSpNgWpmrw==", "signatures": [{"sig": "MEUCICj32tAm5V7WcMELv6O0QIToH+KP5UebP+pqF7DU8CuZAiEAyt0EHUsz0JrD4qIhrNn6gcvjgpkGiLSHI0AToFgC7y0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.1.tgz_1500041992482_0.23870908678509295", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2a7d2b6b4c215d564939a5afc228cec98bc527bb3743f878b8e5fd6758e332bc"}, "2.3.2": {"name": "@types/tough-cookie", "version": "2.3.2", "license": "MIT", "_id": "@types/tough-cookie@2.3.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "e0d481d8bb282ad8a8c9e100ceb72c995fb5e709", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.2.tgz", "integrity": "sha512-vOVmaruQG5EatOU/jM6yU2uCp3Lz6mK1P5Ztu4iJjfM4SVHU9XYktPUQtKlIXuahqXHdEyUarMrBEwg5Cwu+bA==", "signatures": [{"sig": "MEQCICgtqxhEYuL5LDG7m6w37QkWHeyscY08nAwlFbRSbAGpAiA8zVBck5KQcI2LkXIu7b8cTUyILNlN9ua4JK9A77deXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.2.tgz_1508053694833_0.5164882028475404", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "732ad8dc0487919ea9849183b7dd2e47c20d757fa3785cc896cc4175e93dfa1b"}, "2.3.3": {"name": "@types/tough-cookie", "version": "2.3.3", "license": "MIT", "_id": "@types/tough-cookie@2.3.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "dist": {"shasum": "7f226d67d654ec9070e755f46daebf014628e9d9", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.3.tgz", "fileCount": 4, "integrity": "sha512-MDQLxNFRLasqS4UlkWMSACMKeSm1x4Q3TxzUC7KQUsh6RK1ZrQ0VEyE3yzXcBu+K8ejVj4wuX32eUG02yNp+YQ==", "signatures": [{"sig": "MEYCIQDm6D6z3HAqp9bhgimPv6UeCMYGjmOFdyqQ1R3s4sMcKgIhAOZm1unHRaRPOWae/ykzHqegEUs7RgMWKeyi2VAGff0d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8iH8CRA9TVsSAnZWagAAwJYP/3vwPOqKPmlHcoCFCmzi\ny2NzuIUJ2X3CClQndOKRP5YzqicLXDx3rqrN2a46e76BiGrIefD5DFl94/x3\nqUUcxIIoMJ4uIoTs9D0lXiXf8yjQ4sTAqPtnmuCla8CAZZxFKtt3CP/s+YYM\nt6K2dP0iIZDfAsm6irg+LINfRolXqroqEPRpq9PnfQw5UT0v7j2cVT1kq0uJ\nk6gqsy9ZIN32SUbeY3i6U9G4jaQ5gpMCltf1Zlgp2HjiF7PAUgCA2dmsrZwo\nF5nyvFchXEWFZxcXuHRSR2w5Z8PSI0O+rmlq5y3AijVLIjvSTksffZTshvPj\n6HkaRT8lQM52Jxd+q+9blD+i6ii51BpESIDQsXVorwd2IqW4f8mQkzReoY3U\ntwV4ZpHF5Ml6HJZFicmhx+wjUwgTZ1BTqsyZl8ORDGvVgmZ3KnbyMgAYQ0D0\nTswj+6BqAhztL08f41mTvfa2OXjQ10R2F3OPQYYC5agCalLFhlfhAe2HZ/hv\nuQgqQfEDF1QRC+BsujJ1GHwBqCE0Olba8cPflnjr+LNTqLnEcpxa66uIsIBV\nn+M2epbmDP4hqQIAaYwMBJRRkmT4F0x70cdYbnuhRDChs8uJpA7ncb6hThV2\nR4pqo71dLSRMKE6rIqzq9fy4A0M+ctsrCulHS/afzT1ADspi5O3bZlxEX8cR\nKOq5\r\n=+LWN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git", "type": "git"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.3_1525817851577_0.9229990630209013", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9e1c460bd876543784250bfe8e617c1d0413eda93f509efcf07ff11d482ab6b1"}, "2.3.4": {"name": "@types/tough-cookie", "version": "2.3.4", "license": "MIT", "_id": "@types/tough-cookie@2.3.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "dist": {"shasum": "821878b81bfab971b93a265a561d54ea61f9059f", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.4.tgz", "fileCount": 4, "integrity": "sha512-Set5ZdrAaKI/qHdFlVMgm/GsAv/wkXhSTuZFkJ+JI7HK+wIkIlOaUXSXieIvJ0+OvGIqtREFoE+NHJtEq0gtEw==", "signatures": [{"sig": "MEUCIQDrrDdrkF4onsRZBTb1tyx3wDzMoxE0W19Kv/RC9yw7jAIgKfW8LqDt1/CbO3m2MvOwg9flLiXiYZJu8L8pCK2Oteo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2zsiCRA9TVsSAnZWagAAc4sQAIDhrlAQxovAQ+ajL9fw\nIxScjBJX7y9c7LtozNrkcmVs7tjKOBXitmszVMO0/U/88Ioto+xIH1ovPRHn\n8vhWL+YWoiJZLIBrBTN6sNX20ir6EMu/am2Evf0+i+yTPsqtJJNn8D+I3u7d\ntlE4YgTKTNMPRgdJd2MoSvbbNVYkIKP98ekB13IoafoEH3yXFxsToEcZX6c7\neDNddyRoBQQDieIhTLhPoZaQKg1uMVzqsZjuih6P09URvV2SNHD9hp+hrYlC\nbEoJpUswdEq7q/DtyHED39BrFay1KmYXHaOoCXXgYwTs49jT9OqoY5OKvWX1\nPJobIFwS+Rgo3uAkw7xom+IvxKa0KVXhLAkJhpkABLsfPA+56iLkU8zxDXck\nIyNKPlB3BDU3naIgiLlBMx5I7uofjN/YDDOArgvMIo/PkHdWfWXodZdeNXM1\nVPjnv9JFu0t0TC+kg1J3RfjhHz91u4yAWVIYAyDioP9nAyZf4hEXjfoPYYEF\nAjxXS0s1Ab1Z/m58BAmOKKX7tJUu6071EiJ5/D5p8EkIeNc9Ak7DoQpzf38W\nsw+rjgWKvUm8Hv2puuTDTxVVDm5gIJGGa+o4BmsdBVXjVmq5V+QUpvw17Ahh\n/5/Knx0PXfKYMpasLALoRQOn12Od+tY+/60907T0edco3iPYUyliJBLig7mv\n7ddG\r\n=gPuW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.4_1541094177455_0.6590258018675061", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "be39a364314ff26d1ecd6011d3cdfd5d806c25eb7d880d15746c491494da6565"}, "2.3.5": {"name": "@types/tough-cookie", "version": "2.3.5", "license": "MIT", "_id": "@types/tough-cookie@2.3.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "dist": {"shasum": "9da44ed75571999b65c37b60c9b2b88db54c585d", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.5.tgz", "fileCount": 4, "integrity": "sha512-SCcK7mvGi3+ZNz833RRjFIxrn4gI1PPR3NtuIS+6vMkvmsGjosqTJwRt5bAEFLRz+wtJMWv8+uOnZf2hi2QXTg==", "signatures": [{"sig": "MEUCIQCFmCazVp3Fc/yg5Z90unEWy+SeFYkbARHxPC9SyiHpRQIgEZdqZfvRdFxkboA+eOnf8MGiM9zi6W+qbuYoyqY8TVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQRL2CRA9TVsSAnZWagAAN/IP/05UrRlh+l9PTWidM/cL\nxwPCK1owHXfiFPIZppVh2CSWmYNdPgcSGsnuOkwzFdxYjSTT7Ft0iKvpNk9j\nHUzeMHGQp6Bvo6zKXqQEFI0PiU2tu1wqCONCKGskWiD1W22SmtH69DZVHoF1\nF6RPNrvaEiFurr8K34r9k4b+vM+QizwkMJWppEeiAxMh3YAfUh3CppZO3eXm\nD9gB9pVdRsdUu/X39HAtzwECrzklGSkshMRogymod1rGTR/6DARA/3m2ogxI\nNwNuvO6YpU2QRXg6cEuexwUmWCMLFv2+7pJn7i+kZDYERd8q5tyMjsFkAer9\niHvtanwsVS08Bo6j94L7nbvz6lnK6zUUaZWxIdbPZq+UCCgZT7iqg9ir484p\nPww4gHIxLhWzsZbycMt+WbIqSJTNhDvIrhQUEfqLYq25677PZb0tZvmJRPqZ\nZE1zGvuktwT5yxQtWlsAkrWb4wE8Y+sZM+1Yfblp0gcm3UGpddwhWvdvUjTw\nUNXr3mnwE0PuXwcinVHt3aiDl5Xjpna4R0Y3krGZQgAzaht/6XLqFNJXIIY3\nWVK3PVeQOj2ER8DkgndISamcbKGrNE7WIXNZ4wkZyzUDWcGyaIqEse9dz/Q9\nEAAgJbridgOQ7s2RjJzG+333ehrcyH/fy8CNQgAeKXq4whPSM56J2wJSU0dt\nFgLY\r\n=pMXI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.5_1547768566120_0.10439425676875591", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0efe9044a43862c414e1f5ad50de0dd23a0e8521446aef5a86a7e42aa73d292d"}, "2.3.6": {"name": "@types/tough-cookie", "version": "2.3.6", "license": "MIT", "_id": "@types/tough-cookie@2.3.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "dist": {"shasum": "c880579e087d7a0db13777ff8af689f4ffc7b0d5", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.6.tgz", "fileCount": 4, "integrity": "sha512-wH<PERSON>BMnkoEBiRAd3s8KTKwIuO9biFtTf0LehITzBhSco+HQI0xkXZbLOD55SW3Aqw3oUkHstkm5SPv58yaAdFPQ==", "signatures": [{"sig": "MEUCIFTwOkLJS7QlajfudV4nVNwPSO1ErIx2QaoMH/CHjrRWAiEAv4RYAdANJ/A69/jtW3fYpMbBjJAKHCtrkreP6dJLvFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6e87CRA9TVsSAnZWagAALDgP/A1+P9nn3Fg0bruVZaHQ\nqf9VqjPDt7cqdJk36PF7x+ENdLl9ON0esQKyU9agVJzQvVbxEa8LJjxLiJ5H\nWTBs9egABRSidNaNth87maqOXfdcOe/xm7+5DZlKVmLVicE9+EHSjciU9WbH\ntcjSyskCxHHqM/GXbTZ9tSXFNenBjy/1dzs7R/uMuRmfoEhuao4Wz9AAeYz/\nn+pTp0GiRfQSIGcgDAe6kMQiEiCMYNT5MOk/LSyWH5xeVo/Sp5wN4HZtN74h\nEW/elaDmov9W5N8suR2fG8EMVOD4TAGwhHAq+5dknQK6bHVyfUlK8ERtNiv4\nbqc7L94XbAEA50DCkNvHVCNCfAoPcme3EK6F8j1DAHrbbR8lRQdvTB3kye2J\nVSn4yWmKOaHzR0PoxNscAYKTtRApmdNfvBqRDAA/FumH3AzsH7dykpSwTwlT\nIoqhjscf6RfVeFDPNkUJwu2ttKgYnnHhIKm6h5De2EAdyecFqPIER3KXGbtW\nCPmoRbmAExjA76djSTOsCkAp3oDmt4koBmtxSM0WDS87GHP55eoOuvXSyMVN\n8bUDV0hkfnvtdETfo3YDs6aGe4uBtBM871GypNo8etz4zVKIe/F8AckB6okE\nai0xeGhSXxcgIN4ggUqANGxlmmxcDMxKW1KE3oGKzjOmffyBbdNaqYTiiaLu\nZRa7\r\n=BFyb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.6_1575612219250_0.9041879327447626", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f99963c45ef837887ac7831d070f874eb7ced3aded2d0a245b0d5cb7b7bf937e"}, "4.0.0": {"name": "@types/tough-cookie", "version": "4.0.0", "license": "MIT", "_id": "@types/tough-cookie@4.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "dist": {"shasum": "fef1904e4668b6e5ecee60c52cc6a078ffa6697d", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-I99sngh224D0M7XgW1s120zxCt3VYQ3IQsuw3P3jbq5GG4yc79+ZjyKznyOGIQrflfylLgcfekeZW/vk0yng6A==", "signatures": [{"sig": "MEQCIBdagYLeDiT7ikDjFTbd0Z3Hod5foNPhgUjfHtGuGsD8AiBdZtfPhxKOd7xo/H+hcjCmgrCHsdUyz8BT4WBSDl6BjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg9U/CRA9TVsSAnZWagAAPIUP/1FAt/jBto9z7iqEkrKZ\nAO7W/BR4QIMFx3EW5TF6J8F/v6WV/pDjH7Tv/TN7CreIi2VW/JtoFN0FDhHC\nT97Um245iZTYR21E1lzEzt8YmXgIVUBqKCYU9vthtS4Ns+kZ83AxeBNjE7hv\ntE57iWbmiagoT8iXaiLf07w7PDwadcXxwE9AM7N2gNMesDWbsA1rYpEQ74Sg\nZncvYEIo+deOkqj+In/5H80RbJYNSe+VgaBOZQC4DdKxEHQQiTxNjzPATpfY\nu7jXM6c8Z8GSh4tQQXGylfCUgQK+kwBXjWpxBZqtvJ/rpKG7PxyJZp7SHsiV\nF94owvgci3r0rUKUTFsPx/wi7x+5TSqG/h8DEicAEVrm/2PUlHILagkxnLLM\ntRRptdogvnFYQXy/M0mZRWfnCrzMJ0Dcc+22PkVFXBDSASEEi/v1DIPipZz8\nyybYJff5vMAqR7AtotzThOSb2PfcMsC+IltwvyUfIe2oGYwBbTRlXCVHEW4A\n6Z2KWxJkBemQ54vQTWy2tHzWDtucNzzkkYBGgHbgzEAg/htacjITzh+vepX7\nnfbBc0zaUIRHqq9s8xK2mqePjvSco8VG9AHTSyfoQMBaGBM3I0IeoOYKEzlY\nImxd7QZhEBXc6TySDsqXWkluSh6sOyzrAvvtqEh6WQnQEm/27xK9jfeJME5a\ngAvR\r\n=ESA4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.0_1585698111474_0.8218430668718022", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ca0d31f2e9fc2a883ed38f569a7c21cb47c3ad66089c59faa840a59b14c0daf5"}, "2.3.7": {"name": "@types/tough-cookie", "version": "2.3.7", "license": "MIT", "_id": "@types/tough-cookie@2.3.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "dist": {"shasum": "979434b5900f9d710f5d4e15c466cadb8e9fdc47", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.7.tgz", "fileCount": 4, "integrity": "sha512-rMQbgMGxnLsdn8e9aPVyuN+zMQLrZ2QW8xlv7eWS1mydfGXN+tsTKffcIzd8rGCcLdmi3xvQw2MDaZI1bBNTaw==", "signatures": [{"sig": "MEUCIQCRbnA3xlcCC9oh/kXi63JbJbgOe6Ee0p3OS08+DWDEBwIgVE/M+4Vz4uVj5EH4KDN11iwdwVTcCHnpClm+ZPeVdvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg9VTCRA9TVsSAnZWagAAgaQQAJVn7N29/HSELoWe6vFp\nZ8lWxI8eKCeRGMgrfx8klNupnm37fByq35BmgEWvUt2SJL5k2hyo6YfCjuqo\nbyu23ANXnhQguYFjoXUwP2blHQZXHJDR1U34VrFXZbl8aq8wKiMzFy9p0B1J\n7bjQCe+jgmBLHYCef5ruTl4kJ+OAq2OPnKAlIHXjNk6eak8zD2/qK9NbiX7I\nZDuAzb9ZtSEdw2YJOvSaSnZHhV2NQk6m0ZxV6VNoEiKYPleWaDwf8XscPd4j\ndch73FREXggsqpPU6uN7xoZ+aSbdEsJAo/iitaZwPLJhi568qqTm1fWp+1zr\nM3FCCi4TSQ2vK1MPMxrftYJekQJvoWMrNoOYmVsJJ2O1guV5ND8EWYd/yIVo\n9hC2iGpk20861M78BpIUyDPk7vBbVnVeKlHgxjyVheLAYVU6B17FdG41BSeU\nTlYDb1Gkb3kRtZX2Mb1fZZp3FXluNvxp8TmkENO4os7GdPIfyxha3G7B4XcL\nAysahVujNWWuUUsECvcszF3h2OI+v2ac51SaS6elsfuezdRGRguLeaII50lV\nuh1gXPyp1PCE81rr88BOIf0dzVlmIUAZsJZOMoRrypbrK5tUQg8/96qOco5n\nNVgYcrmU/yX11LaYvhKtJfB3l5UQ6bUyT2OukBX37eJNr8CHVDOz+ga0isfi\nToL3\r\n=Gwsk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.7_1585698131370_0.43696112235736284", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dc46de5a3612c861347639a5c7c23487cdbffc5d144fc5f08640208a875cfdbe"}, "4.0.1": {"name": "@types/tough-cookie", "version": "4.0.1", "license": "MIT", "_id": "@types/tough-cookie@4.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "8f80dd965ad81f3e1bc26d6f5c727e132721ff40", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-Y0K95ThC3esLEYD6ZuqNek29lNX2EM1qxV8y2FTLUB0ff5wWrk7az+mLrnNFUnaXcgKye22+sFBRXOgpPILZNg==", "signatures": [{"sig": "MEYCIQCuZErmr8OUMMx9bxd/LpK3+KRLbPO1cmfPoqQ91jiFzAIhAO8AdjZvGikeJV6it7JnaWvtMIoAFynRqFjo9GWIcNwk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg322RCRA9TVsSAnZWagAAjs0QAKCcVOsC8lZQU+ApSWnF\n3zZVR3dVMHdz9PH/0VLmMg1efMPASDGdzmQdKZ6lBuo0S5aqIdyZ7pxR1ijL\nFidCQfopW7tAvlcqabonBGiO63uBFCexQVMB0gPsEJybgi3AswNMz2Fcn1Uo\nwg3VdePwsJK5Vcqi5ut09pdYSaFbldUnOjcJFKojfhH0DfdWxpsj3ttjJIdQ\n/veqad8/bxGJm0+t+Nc1lfJZAgZu/nHS2yEyRMTWIESW7o3Q9A/36bTr+PO1\nQVJjfYDalcOADwDA0a4PsL4B+VNbLRJUvpBhSvcxIRWzMFF4zaDR90+XGPdA\nBWN9CJ0xXCaxUfY1eFQSoSHqFLYBqkubpg/Smj255BndfzOQwGvjQm4fiC14\nFW2ZsJ5v4AjvcTSaKXToeux965Se0/8qpfkXM17RTc6HY8YZ3rmF0Opd75uM\nWM0pWC+AefInYGzeHzfU0O6Zuhw8C9/QPfRHzCeREZYgVQgisYgOSXcHX5S9\nXCZDMAE3X5M0uOuf+LrndrNVMUMRmCFjlbxlyplS1gjfHMzjW0iWO2zvce2s\na2N8zVJrpVlEaXgKuuGF1mftnWnioPZeHQfpwg7xbpTd5IpGmjvhqKuH8dJX\n+znEAR1sPbP/YSrNpFHZnUJVN/sZh5fw0/Y9NO0faG2TWnkxc4UkcC3fJ8QU\nGyVB\r\n=N5i9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.1_1625255313592_0.13801504814848697", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "35dd1719b4f8b273457e3e24babcf7140e293129fb277b499985b53427852cbd"}, "2.3.8": {"name": "@types/tough-cookie", "version": "2.3.8", "license": "MIT", "_id": "@types/tough-cookie@2.3.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "511fc1569cc32b0cf50941fe9f00bf70f94116bb", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.8.tgz", "fileCount": 4, "integrity": "sha512-7axfYN8SW9pWg78NgenHasSproWQee5rzyPVLC9HpaQSDgNArsnKJD88EaMfi4Pl48AyciO3agYCFqpHS1gLpg==", "signatures": [{"sig": "MEUCIQCChhVb/OobfMSWT66fPlg3lEskPo5ojNHzt21wYJmnTAIgbV+9uhL56dArzY+6qEmJB/UPARrvGk1dvKNbrnmlHkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg322eCRA9TVsSAnZWagAARrgP/AxEuIwaH3mauH1wiyaB\nfcrBEhk5Gfm7L55h8XgbnBqZqt+qae3v9rHBODakPHHigljnKJdG6nswwk63\nBtScPpK3r7Pt6X6ite0Ck1Nqc/++xxaucb0TXtOV+AG5/cqGch1QeoOFGZzP\nVtF8kSsF2veskjvoT5WG9j3OLsbiJuW5Cf4r/b9smYjSi5/WmRTHZrPjb92v\nMLnPXW+0r49LoubEYsptecuqWf8qrM0CF1rzEAfXuKuV9l9bnC/BnEH/ll2H\nLeXR3s/HXS8Ves4HPMQXkpt4lzY32tAdm7UnIqtaUIxj+AnEv2plvFkOR0jQ\nxOBri6VRBZUuhaB5B3ZmJ3Z52M/JFoJk1LtfnoknzcTR6A4yXdJB46oaAcku\nVnlTwHcb1HScH/XMH4PG6phwkAPKhfZtu69XyY3Z3IuPmuPZTGQgkTYXCOJo\nko6x9Kr6bwjuy5mus5mWUBBs/WPhQWahXi0afCJ29v2M98QKhGN4gY7mKrHx\nSyNS9csiflQISdPZJuEkfXn39nKzuyhlhZXhMqP8v3GaQEPeNe05DModM+q0\nonqzmxRAsRtuHotqD00dR3kY9wJZUBTvCdzogPILCPmwI/5kdBs6exhUlBUr\nndH4ATpfmYJnHrgaLDTW0mKn914CtQzVvq0dRuLjc/YuKkhwYVfrLacm/nzw\nBf56\r\n=CoU5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.8_1625255325874_0.11003133133260534", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bf10ffd40d956f8962f08b300553847536edd659be8078204b9ab39c8f7b809a"}, "4.0.2": {"name": "@types/tough-cookie", "version": "4.0.2", "license": "MIT", "_id": "@types/tough-cookie@4.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "6286b4c7228d58ab7866d19716f3696e03a09397", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-Q5vtl1W5ue16D+nIaW8JWebSSraJVlK+EthKn7e7UcD4KWsaSJ8BqGPXNaPghgtcn/fhvrN17Tv8ksUsQpiplw==", "signatures": [{"sig": "MEQCIGEZJfB/KtWH099prlRKktH1gex6GuR/IDdEJi++61P5AiA+xI6eYPtvfv7tTZ4y6pSliRbo+2hn2aZ1yW64QN4krQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiV8b9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoefQ/+JHFu1uio8A1u2vcl29y1HkEzL+MkoNzVonf/XzYOYr2C4664\r\nIn0bvUVK9gtElm1hv/BZG5574xL+RF0F3XPp0xLt1ORIwNXpMzh/id9i8oyw\r\nb+Qvzp34XD5reZDv1yjn1dmffoeqBzvl/K7m7pt6i4Y0mMuXVNiWxf1CwZB0\r\nzReqMER7HUWkf9QCDw3U/ZJ29637EZ2ruxa8gNOSqhDZW4akeKKPhl/+xp8J\r\n+wOcxbjgRab2Nx3YmXTmDwPMhR8l2S2npKotyRUDUQI+boxUcAsR9AOmv5gl\r\nSB6B1Mib+O02U/Iq/ufFwIS63pd6+ZvXgs3p7yBKOavaYgvBbu5rXz08syL7\r\nsIP88Sa6O87NIzWw69BLCepokBz8Gqw9Ku/tmLnxhxXUILMOFhZVoDUNGn9q\r\n3NMPyEdagKV512aguLKp+Tx7LOe4Keh63mBDBF3mqURakZJbQG2Oeyk3p7fY\r\nGekpYLV+jG0raeLIoeXYt9b+S7pa4l1pLZEfldppmU3ttN4qlCunut5OknjR\r\nhwkq9eh3LLcIkVaGyUR1x21FrLV/1mmXNoJ0bpVE5FQF/lJYXIIWLWDjxrQd\r\nbbiW8w9PF9O6osCh5lBNTs8psVl4YPEGnls6n7/5V2eddGhFB8QZtlY76VJL\r\nggd0lLPNyHRp4sr3wfbzM6ZCTVwEdahNFpg=\r\n=SQWw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.2_1649919741730_0.08727026539593297", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b7a67b8b87baf3d5d1b8d6f2b4493dad84d9ec7e93abf68824da9da25ac176d6"}, "4.0.3": {"name": "@types/tough-cookie", "version": "4.0.3", "license": "MIT", "_id": "@types/tough-cookie@4.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "3d06b6769518450871fbc40770b7586334bdfd90", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-THo502dA5PzG/sfQH+42Lw3fvmYkceefOspdCwpHRul8ik2Jv1K8I5OZz1AT3/rs46kwgMCe9bSBmDLYkkOMGg==", "signatures": [{"sig": "MEQCIGxgbt5do0VLAr4hBpXzgg6EFvtDXlUgu2lOMnXpEqtyAiAmkW21Ul642EN+oqkYitxFxrVVq/O4UE+qwYwLITiBnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13937}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.3_1694523111288_0.6624407281556783", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7f8bc783bb0082516d3360845f537312ee527ca2a6f2d381c41c22d6c0981d9c"}, "2.3.9": {"name": "@types/tough-cookie", "version": "2.3.9", "license": "MIT", "_id": "@types/tough-cookie@2.3.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "904becefb2ccab4a33d96908704525bb4dbc048e", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.9.tgz", "fileCount": 5, "integrity": "sha512-jLZVDbs/2XRjcgg6X5aXlni7AC85LYOjIQ5H9Eouny0t1SDKiXWxkbTPLkMdFKYxlKVZtMstlkpU1v6d14hVbA==", "signatures": [{"sig": "MEUCIQCXFRx6X/kzqFNAr4AMG3TEkyP2cvEsyhrvR+iDxQ//MQIgNhSNwEPJ8EKDS29S/48rXI0U4e5o0jXnCWWHOOyd6WQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11241}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.9_1694523117302_0.397472379706878", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e3f17f1a58e6fdacf4f004390fbe412f8c9d8446a76ec800f670f19ccb0c802a"}, "4.0.4": {"name": "@types/tough-cookie", "version": "4.0.4", "license": "MIT", "_id": "@types/tough-cookie@4.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "cf2f0c7c51b985b6afecea73eb2cd65421ecb717", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-95Sfz4nvMAb0Nl9DTxN3j64adfwfbBPEYq14VN7zT5J5O2M9V6iZMIIQU1U+pJyl9agHYHNCqhCXgyEtIRRa5A==", "signatures": [{"sig": "MEUCIBmzgioPBvSLwXc41n1uDVOneWLkxFyoas71xLop88ZwAiEA6Wte/tH1CrzvH1SZm9I4Q6aqtSVcGMdaom7bPYQFlM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13536}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.4_1697647079670_0.6378576116502168", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0e92e38d3eda5478d78467bcaa4e1770952206e27b2af72f9f4c9c64d491e35e"}, "2.3.10": {"name": "@types/tough-cookie", "version": "2.3.10", "license": "MIT", "_id": "@types/tough-cookie@2.3.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "69537b138495c618acdc996949c9fcf6bf535110", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.10.tgz", "fileCount": 5, "integrity": "sha512-D9<PERSON>h9yyZjg35G2d2dpPOunjKj1VxLQgHI1X4YwVS5IDXESHwR4oSFxcxp2ro2xFSlu3qI+xZQK9fQH67E8M1A==", "signatures": [{"sig": "MEUCIQDXrwHSTLBYktfxGHgKoZdUvy7fUZhJcVGsdQVGs34qSgIgddJTg36aZmYfLgtRPdrVA5aEtiORFES83iWn20Qbjgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10841}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.10_1697647088375_0.9440364893391968", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f1580c082e5c023543b8ebff51165c9b7b34da0d6d0810ede54bd9b69256ce33"}, "4.0.5": {"name": "@types/tough-cookie", "version": "4.0.5", "license": "MIT", "_id": "@types/tough-cookie@4.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "cb6e2a691b70cb177c6e3ae9c1d2e8b2ea8cd304", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.5.tgz", "fileCount": 5, "integrity": "sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==", "signatures": [{"sig": "MEUCIAZ0+SQ3YjsbgIeU11R+o1/6dLxLNDGILflAppqtvAJLAiEA4195GKNklEm57afhHEIVhqfvO5eDpne1wT8sDQG6diA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13536}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.5_1699390095110_0.2933948581056627", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "adafa53ff34d3665a708d3ab1af232a46d4efb906d46f48d7d0cd3206c987a58"}, "2.3.11": {"name": "@types/tough-cookie", "version": "2.3.11", "license": "MIT", "_id": "@types/tough-cookie@2.3.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "dist": {"shasum": "1c5431f11b274d1c768dc38a86006c58c68fa813", "tarball": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-2.3.11.tgz", "fileCount": 5, "integrity": "sha512-xtFyCxnfpItBS6wRt6M+be0PzNEP6J/CqTR0mHCf/OzIbbOOh6DQ1MjiyzDrzDctzgYSmRcHH3PBvTO2hYovLg==", "signatures": [{"sig": "MEQCIA/PR6sLO28CRCsTeg4Bf344S5pZ+WhIbPoZ1XXOv5DGAiAdoPp9WDJNyeUEOA/yFBr2jgmufiUSzGuqmJq9RlNLSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10841}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.11_1699390101058_0.45430141813201663", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5e72cff0ca4151953599492de59cb77da8e1306b22f087bae6f2507d664e617f"}}, "time": {"created": "2017-06-01T05:01:11.116Z", "modified": "2024-08-26T09:49:12.255Z", "2.3.0": "2017-06-01T05:01:11.116Z", "2.3.1": "2017-07-14T14:19:52.618Z", "2.3.2": "2017-10-15T07:48:14.901Z", "2.3.3": "2018-05-08T22:17:31.798Z", "2.3.4": "2018-11-01T17:42:57.572Z", "2.3.5": "2019-01-17T23:42:46.257Z", "2.3.6": "2019-12-06T06:03:39.381Z", "4.0.0": "2020-03-31T23:41:51.654Z", "2.3.7": "2020-03-31T23:42:11.558Z", "4.0.1": "2021-07-02T19:48:33.711Z", "2.3.8": "2021-07-02T19:48:45.995Z", "4.0.2": "2022-04-14T07:02:21.865Z", "4.0.3": "2023-09-12T12:51:51.510Z", "2.3.9": "2023-09-12T12:51:57.477Z", "4.0.4": "2023-10-18T16:37:59.993Z", "2.3.10": "2023-10-18T16:38:08.573Z", "4.0.5": "2023-11-07T20:48:15.280Z", "2.3.11": "2023-11-07T20:48:21.290Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tough-cookie", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/tough-cookie"}, "description": "TypeScript definitions for tough-cookie", "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/LiJinyao", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/no2chem", "name": "<PERSON>", "githubUsername": "no2chem"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}