{"_id": "@csstools/css-calc", "_rev": "26-f1c6fb2c8169aad5486ad4d73f5a4f3d", "name": "@csstools/css-calc", "dist-tags": {"latest": "2.1.4"}, "versions": {"1.0.0": {"name": "@csstools/css-calc", "version": "1.0.0", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "f93e4cc9d80b504467aee1b5251bb2fd8be435a7", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.0.0.tgz", "fileCount": 63, "integrity": "sha512-Xw0b/Jr+vLGGYD8cxsGWPaY5n1GtVC6G4tcga+eZPXZzRjjZHorPwW739UgtXzL2Da1RLxNE73c0r/KvmizPsw==", "signatures": [{"sig": "MEYCIQC9i2+TEiXmRqt+27ZYY6hnnXe1AYygH5xUUcjb9GUCtQIhAPeL+tVSb72VYoN9e5EAeLGRb023KwC6i2s/hUvn5EDi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9O+IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq31w//Q6c41BtXML+dggHh1ZVavOENZg8Afcm1TwW6POisnAMcgRhi\r\nhBwrIEy8VFsESTxZOXJ3PwztlIZMqYQPPh8zVZyzWbQPZWTRSdeWSkkEnj76\r\nQM+zqG7nApVAM2bGLnTtumLRYfWYdl/UOiP+a01X4AbjJzqGcr/YtiNu60YT\r\npBk1qM/S6Zz+TD1f/J5QkqAl1JWGDstLw/iEZ8RU9l9PWyPwAbPuZNquaUk+\r\n/hto+W/xaxBUIj8/B1ixCoD3fwrZDzVhbLMt5Nq3A/bQKCwY11pHPYlfZQw2\r\nCssJxwNXASgJBu4mdgjuLniKckkGmN57f861yT6xQ6e88hUJ5zPqidCs2d5B\r\nF/WXtSpqnUBpFZa+09+WabVEdU+2NgSlRKzNUINcCTfKXCIg+BHuKeC8rv/c\r\n+9cmqlr36MbIDQ8SPttRidoUjKRnk1cG6QtFOeSKBe5M20vRESRMfhr0cAmP\r\nMuYCdEuN991MZWrfkRFgFTcNMshiFcNhi7PBgvN3fEqZlCVP3fwYgHeUCx3v\r\nIH6+F1ScmuqZzWewCCypwNXzQgHMxjqnSXtBHXO+SM50Aukh8MAY/ezaGw0m\r\ntOcVObN20F1p1X9xy9sox43PPo7/GE8MDN3BQu7qolhzrrbFSvWK0Gsz8iA8\r\nfyWKAwn2wRFCSYDfFWtcPpIpkBAEla4jt+o=\r\n=byev\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "1c9406d2ec54fff81022d1a2126902559c50a8ae", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports && node ./test/test.mjs", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "stryker": "stryker run --logLevel error", "prebuild": "npm run clean", "benchmark": "node ./test/benchmark.mjs", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.4.1", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.0.1", "@csstools/css-parser-algorithms": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.0.0_1676996487918_0.003670461940096903", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@csstools/css-calc", "version": "1.0.1", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.0.1", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "c478dbfb2c10e22741b261b2a64998960d69bfc7", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.0.1.tgz", "fileCount": 64, "integrity": "sha512-VBI8X0bmStfc85wWTa2bsbnlBQxgW4FmJ0Ts9ar9UqytE6kii3yg6GO+wpgzht2oK5Qlbpkm1Fy2kcqVmu6f3Q==", "signatures": [{"sig": "MEYCIQC/IhuiSmF1SBudY7jjzVZ3ZTtoGrDN2kdFQJN0wCEEuwIhAMhhQFCwIgjOCzlHcb1b+XR1W+cep2jdlgQtShnH5Vpq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHqg9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUIA/9GRCLduRMxblkq+RQGp+RnvOfW+wgoRsIAAzKD6WICluNQaIc\r\naQ9mOX3FO5CIPiKdwNB2ctREp+rWITelWj2L5VZ580R8Ww5sj/3Au5es/Pf0\r\nJXFGk8buoiAiMMF1AiIGe7Axq9nxpyZv1S/izTRHMkssQmGr2tewaWPqZR9n\r\noR2ms6UCly+KBqXBTY9r9TxW3D/Gd0HYjGnTrtMlrEEKngnLTPXeTcsZ0mRK\r\nnX5ulLCAuoZJkqOlD/m9ZD84Lvfpg6k8a/fehz9inxaLZ2RFGBDaF64p2J5n\r\nOEVln5DxSTfw9Ry/pKqJm3ghax2u4R0NyirxFaBFZyjKMZNrJSNRilRqoMVu\r\nwsUyzqoos08tIPbVSdtulGwFC1H0XFiLlGB3oZJT1H3Iod82/MskTO901gAm\r\naaC9CyMI9MA5gbEC+ZD5mAhWCsgS26zSqPILgSzDm4Z851uoyTDQYt1Da2Gh\r\nowQSXLdB2ZNdDS4Id8EdumDqOADWdMwnTyY6psJr1MnKBngKmpNscY1SQ4FB\r\n73D4nEHwOQiM9f/8wq24nP2dgOkvOCX+3Mwal119k4L2XeVm8P2ncoEeBs/B\r\n5Ewj5vKwradf+7g8u/GpDGZWLXtoMosCo6LVYyRKLpdgGxBVwJRjeiSzgn3k\r\nXAxPhLW6BxTiCllTnMU9jQLhfvEncxWrCq8=\r\n=YPXn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "7fc17bb85ca4fa83104d04a07a3a063762d9d72d", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.4.1", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.0.1", "@csstools/css-parser-algorithms": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.0.1_1679730749219_0.06314273352123267", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@csstools/css-calc", "version": "1.1.0", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "1470b40c59669fb0acb04fde41a483fe11ef5249", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.0.tgz", "fileCount": 64, "integrity": "sha512-/d0vIpFLa3aam5vxbv+u8sKEoSVS6oJkJcPdrp54n4O1zkkp8Ob6mYSYCNmY+PgOu5/EE+L5mHJobLOQOGZqdw==", "signatures": [{"sig": "MEQCIFgLvG04vY0SeQXWlyyGzY32Ax+78On8YWeARWEX5HzEAiAUDzM94Q74YxeSglfr8kG7b+D+Tkpee6ATgXdLyH2ZUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM78wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGbw/7BapV7fIa8301CZUGv9+amgnlBERfw0PTfgI/QQIfkqAOjYbK\r\nsce2mlLSgrazQYec7WOgEJfNSQi1odcYLO+hqVxH1syB3PlyOjd+95JrvQqX\r\nBcbW6b371vIB++3SXYh7a0pUsfF6PqdBrOm/s2N6PLvfr4RAFOqG5hCeHOT9\r\n67tgMJ6YSGid+3SwHs9VxJbNaXRckK02pQWlpDSB2OYWvuylDgGDsnTxhFUt\r\nPzbNIUxwuuL7pW/ERvbDBjUW+vpv3U56W7EILbc37/I/Scel3sXHcRp7ksig\r\nk8mdmqst89jRXCiqnV6Zy9d+yXI9hFt3D0ELOVpfuclQhb8oaUTISYuXx4aw\r\nIKGP0W9tmk1gN2E853JNXZr+eKNjlfDhvBE6Tz1yIs47XRkv1hkQltgy4N7i\r\nD/nfgznH/2/a/oBKuQ5ZestI1xlLWg9yJjuo9lfPbKCYF6LSHqPa9nRt1adW\r\nyPgjO/+I1XVhruwLopHZLn0TQoR8HEqC3GflV5PM3GM63z0a51IE+Q2JeaV7\r\nuaPuXcXVgaITLf92uEEqz7GeflC4n1n06C2L32SMrron6tlOPUQy0nwrbHbR\r\nEPSuDURMy1tx4L5iv2q9t8icG/9M2duGpOYI2tVHWILebEHmxNf1U0QTJXDI\r\nAm+qM6N2GTf2uK3nOoN0VLfZfYjqniz4SkI=\r\n=xs3c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "cf2d562db0d7f00b3e93b6d9ad9574fd530d4520", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.4.1", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.0.1", "@csstools/css-parser-algorithms": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.0_1681112879962_0.5689317046926385", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@csstools/css-calc", "version": "1.1.1", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.1", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "c622728b7f0c9aae70952623c2b0d3d114752987", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.1.tgz", "fileCount": 64, "integrity": "sha512-Nh+iLCtjlooTzuR0lpmB8I6hPX/VupcGQ3Z1U2+wgJJ4fa8+cWkub+lCsbZcYPzBGsZLEL8fQAg+Na5dwEFJxg==", "signatures": [{"sig": "MEQCIC8YGWipEENfyyOHtaoMYSnZSuPv4jabrKOB1MVJ2Zm9AiA3mfrSfqCMztscCXA/s5C/1P4+ysvrqSqeOHToXsB3yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM+zKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNiRAAmgF31OGb1XdKQfM5UJo5ibs14cF/PPxXix1ABOXTwVYG+rZD\r\nwQXSQzkUi3zZgc/mfcFE95JUBCIgwgPbMOVGIv7w9/sv9Iw0vwlfIlo8fAT4\r\nNIW50j2bSb5ADvYOx67cFe2hwDUXU4jIhqh/0Ma2t6KNbMl7rYkyLu3/VPHc\r\nTOE4heKid/PS7xrfPf+YljVRnm15j1RvXNZfPxKArO7wJZ4QjTOh5VSxNk2U\r\nEADp4o/4mBF6+RBHmzN6ikKl4qTDeDywOt1zpc4toSC3OfgHk56RIAdnAjOt\r\n8NOTrWEAotwGwMzqX8mCT8dV5pigntSePyFgqcHo1IuBgzzBoxG0sauCgG66\r\nbCm7T0PiUzhMwFLzZfIryMgOxxUuOllnTP8cF/kxBAbR+uybif1p3DNS4fpr\r\nfGaCd4qIbZ0thaM1UugEwgtgzZpF9TX7+y4vDii/RyjWV1XJIdLFCAv2IpJs\r\nqRirP4jxjko2i7J7n7LRj5rqcFpN7JoEhK15Faqgmm8IDQkwx+qWkOXrmsNs\r\np3VAkxoGbHqVtgnKBp9OXdMfwRnLDUZiHvA0IjMGbfDVfCBb3mbbfvoiHz8R\r\n0ZFn9L8w96j6KL8CIQ1VnLbL7Pold2oDEu0CPtquZ5cGarXhBLDVzoXGh6h+\r\n0ImqY7rfifviexNOJPr60PvP60c34np5Tho=\r\n=zZhi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "e0ab90cef89b2d526bd0e754dc6795cdf248aed1", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.4.1", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.1_1681124554452_0.7458737941330591", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@csstools/css-calc", "version": "1.1.2", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.2", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "bf2c376bbb9a43de4851a7efcde1818d18e0fe7d", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.2.tgz", "fileCount": 64, "integrity": "sha512-qzBPhzWz4tUNk2tM1fk6tOSGaWlrhmH66w6WyUDoB+2Pj7pxvu6mlvXVwOGODGJBIF158aPWPheVQgcoBTszkg==", "signatures": [{"sig": "MEYCIQD5B4jS/ZzcpRbPG81XoNQZAoURb7gQb57Gjc9W1TnkAAIhAJh895TE23lyxJDgldTTsGcnmimjHJUAifOTVc49YIzP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72317}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "26ff1f90ee491a58c9ec14ff2e928ac566525f5b", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.5.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.2_1688371717942_0.9068198161168797", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "@csstools/css-calc", "version": "1.1.3", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "75e07eec075f1f3df0ce25575dab3d63da2bd680", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.3.tgz", "fileCount": 64, "integrity": "sha512-7mJZ8gGRtSQfQKBQFi5N0Z+jzNC0q8bIkwojP1W0w+APzEqHu5wJoGVsvKxVnVklu9F8tW1PikbBRseYnAdv+g==", "signatures": [{"sig": "MEYCIQDvXKrqJuDVr9Ayv70KCnL3428oIn7Kq3sKsMEBZruq1wIhAOA/SX+BuCkRo1AHa20zUBZ69P/vnomcOG/QXuQ4aHbE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73040}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "deb765a006638c2961ac2e499f28be91cd201728", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.6.7", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.0", "@csstools/css-parser-algorithms": "^2.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.3_1690215618258_0.6772062896956461", "host": "s3://npm-registry-packages"}}, "1.1.4": {"name": "@csstools/css-calc", "version": "1.1.4", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.4", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "70bf4c5b379cdc256d3936bf4a21e3a3454a3d68", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.4.tgz", "fileCount": 64, "integrity": "sha512-ZV1TSmToiNcQL1P3hfzlzZzA02mmVkVmXGaUDUqpYUG84PmLhVSZpKX+KfxAuOcK7de04UXSQPBrAvaya6iiGg==", "signatures": [{"sig": "MEYCIQCAvIKmvnTNH8Hz9KPDycFKNKSG9O1I66q8EpYVJIREHAIhAJPdlOQNSVQWOVPnC+HiIx4dcWaetxGW/Pv9MZAMZCng", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73562}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "855cbced329e0b90af97ccabfe4abd5ab22b59d1", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "9.8.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.1", "@csstools/css-parser-algorithms": "^2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.4_1695584665339_0.9252859185765576", "host": "s3://npm-registry-packages"}}, "1.1.5": {"name": "@csstools/css-calc", "version": "1.1.5", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.5", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "cf5ac0b51a0533bd69be258b15ae243a7a47e3e1", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.5.tgz", "fileCount": 7, "integrity": "sha512-Uh<PERSON>5oSRAUtTHY3MyGahqn0ZzQOHVoPpfvUcOmYipAZ1rILAvCBoyiLSsa/clv1Xxct0SMKIq93KO5Bfl1cb6tQ==", "signatures": [{"sig": "MEYCIQD8QSyG0X0WbhucCkoGDiYpxcmhys7CgtQwXdXyaXUc7AIhAOKshEENsZZR/4LjnsqXyNczuR8PIN0WGMaCMdGmsnoA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60195}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "78f2a27272dd2040944788d1a455fe63273f111c", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.2.3", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.2", "@csstools/css-parser-algorithms": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.5_1702682554986_0.18892231062209208", "host": "s3://npm-registry-packages"}}, "1.1.6": {"name": "@csstools/css-calc", "version": "1.1.6", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.6", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "2d4e16725c3f981f7c6e469c306bcb1f490e1082", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.6.tgz", "fileCount": 7, "integrity": "sha512-YHPAuFg5iA4qZGzMzvrQwzkvJpesXXyIUyaONflQrjtHB+BcFFbgltJkIkb31dMGO4SE9iZFA4HYpdk7+hnYew==", "signatures": [{"sig": "MEUCIQDejJXLlipi2WkB52OsSpcRY5U4GF9Q+xnvKhLj696I8AIgb4mOQ4MEwlkH1EMc76hk1FgAVVrmBYYCSFnrpAyKtmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60157}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "382651a2c16f68e43d83a41ef7ee9ef7f2514535", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.2.3", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3", "@csstools/css-parser-algorithms": "^2.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.6_1704040291519_0.379141247602764", "host": "s3://npm-registry-packages"}}, "1.1.7": {"name": "@csstools/css-calc", "version": "1.1.7", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.1.7", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "89b5cde81ecb4686d9abd66b7eb54015cf39c442", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.7.tgz", "fileCount": 7, "integrity": "sha512-+7bUzB5I4cI97tKmBJA8ilTl/YRo6VAOdlrnd/4x2NyK60nvYurGKa5TZpE1zcgIrTC97iJRE0/V65feyFytuw==", "signatures": [{"sig": "MEQCIGI3oEVo3OphIKa0sWeO8jJB/71WbsGc7IZckeEOLklxAiAjn5PN1QcnogRlLAyeOdLYEZ+r8QNi61T2unu89xt1kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59924}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "be499d01602db8bef2749b51ec12534bcd7ed5fd", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.2.3", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3", "@csstools/css-parser-algorithms": "^2.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.1.7_1708329950060_0.4662222590863716", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@csstools/css-calc", "version": "1.2.0", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.2.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a45145a868e644c31c79baf74c8de64fd09b3415", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.0.tgz", "fileCount": 7, "integrity": "sha512-iQqIW5vDPqQdLx07/atCuNKDprhIWjB0b8XRhUyXZWBZYUG+9mNyFwyu30rypX84WLevVo25NYW2ipxR8WyseQ==", "signatures": [{"sig": "MEUCIGvESM2I9FoaYDNAkCQpFyTa/zX7qO5/8GK5WD8358RDAiEAxmlVwZDFUc7q5d+Can7mIsZ43McpQ7GYoYf3vNuukas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60749}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "839c28fe63a0b99baa8cc88feb386caefe3cf723", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.2.4", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.2.0_1710355672899_0.44415029298660325", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@csstools/css-calc", "version": "1.2.1", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.2.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "ad105be9960b9ea072198204f3862fb79eb541ce", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.1.tgz", "fileCount": 7, "integrity": "sha512-xNLOBhlXe3qE3REPtPOQVY6WV3XJoJ3RFRf83fLiFFWbOZd5AT7mqcIZB014I72KMn033DAahDXZWIyoxe5p8A==", "signatures": [{"sig": "MEUCIHVuUvYJezDz9L6EOvcMfN7ZJFKUAroAYDvchkZb5jh7AiEA6gOw4q0jjOmMCsBGo44D/C7HUTXjaGaCQFOU+NVT/aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59285}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "9e16589a3afbe5fc8099aad780742224c445b303", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.5.1", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.3.0", "@csstools/css-parser-algorithms": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.2.1_1714838790960_0.45254385557010046", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@csstools/css-calc", "version": "1.2.2", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.2.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "bcb856e63ecc16a7508f43e77ea43ac5daaf2833", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.2.tgz", "fileCount": 7, "integrity": "sha512-0owrl7AruDRKAxoSIW8XzJdz7GnuW3AOj4rYLfmXsoKIX2ZZzttzGXoiC8n8V08X7wIBlEWWVB4C8fAN18+I6Q==", "signatures": [{"sig": "MEUCIFcmpVVp52UrPhCAWVL94kdXn0eFCvRLU65l9gudGe6gAiEAqIYqEH8wY1pvE1KWiG4Jh1IWAYzuN1t++qvtLMb/Pf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59511}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "34ccd23e5cd5dfee0b2bed4b05aba04f90b9b481", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.5.1", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.3.1", "@csstools/css-parser-algorithms": "^2.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.2.2_1714857224205_0.7997666533136236", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "@csstools/css-calc", "version": "1.2.3", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.2.3", "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "4ea248c39d27b8e326dce3e82268249e2cbccaf5", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.3.tgz", "fileCount": 7, "integrity": "sha512-rlOh81K3CvtY969Od5b1h29YT6MpCHejMCURKrRrXFeCpz67HGaBNvBmWT5S7S+CKn+V7KJ+qxSmK8jNd/aZWA==", "signatures": [{"sig": "MEQCICZzNqhCEa78nYKIMuIy1HOMU7ggyzwU1eBql5WTJqPCAiAZuvr7h/eT26W9mfER6oBm+uxu94RevkyiSpr1FXNWmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59377}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "e535bbfd16aeb2161545f7f85b4e19dbf9d7b28c", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.3.2", "@csstools/css-parser-algorithms": "^2.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.2.3_1719698309176_0.7512795778603836", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "@csstools/css-calc", "version": "1.2.4", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@1.2.4", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "9d9fb0dca33666cf97659f8f2c343ed0210e0e73", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.4.tgz", "fileCount": 7, "integrity": "sha512-tfOuvUQeo7Hz+FcuOd3LfXVp+342pnWUJ7D2y8NUpu1Ww6xnTbHLpz018/y6rtbHifJ3iIEf9ttxXd8KG7nL0Q==", "signatures": [{"sig": "MEQCIGIz61uE+oLaWLmE2C9wJ5MdBYs0Cs1C3n0zYvGxsOIOAiAc9PkgUebYZaLZGqDKIM4ar4B72kQocTfYiMkyyIo6xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59260}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "6cfe20f127d1b335cd512bc224176e75e3f8266b", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^2.4.1", "@csstools/css-parser-algorithms": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_1.2.4_1720258485704_0.3997896611457714", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@csstools/css-calc", "version": "2.0.0", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.0.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "f75d4b2f17b38ffe56c151a80d4b97e8f8968cc5", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-fxPxNrEVGeej4F35Xt69Q7gPMKa7oEGNxeP1DpA01sWpTF3Yhgux/0slVX3jLHd7dhlszeQlNAUhpAorVxoHdQ==", "signatures": [{"sig": "MEUCIBakxJEq9MqiAsUpbw7bWX+bRs06DnfoVIahRCiuRhJ4AiEAnHejeTiw8sM0ax0DlKeN2iL8nOWgQSdTBxRPgBi1rxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58263}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "0cc37364b0415968fb22a820b5e342e8df84a098", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.0", "@csstools/css-parser-algorithms": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.0.0_1722721214900_0.830108272164688", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@csstools/css-calc", "version": "2.0.1", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.0.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "1675297b19f0933c729fdd7f4f5279b855ae724f", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-e59V+sNp6e5m+9WnTUydA1DQO70WuKUdseflRpWmXxocF/h5wWGIxUjxfvLtajcmwstH0vm6l0reKMzcyI757Q==", "signatures": [{"sig": "MEUCIG7A+7AKEkUOSrN5zJPevbfo3Edp0GGytMDy/9nNBQtBAiEAv4y3d9UBdtmn0ozErFkEmTqVQC2gDE4Ft1eJlCg0UU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59630}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "6394f68dbe6558e90bf5fcdac27fe56ac6079b16", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.1", "@csstools/css-parser-algorithms": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.0.1_1723996139253_0.7085090529231222", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@csstools/css-calc", "version": "2.0.2", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.0.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "20f55c1c5857999b9cb0acca38e4eaf1a38b53e4", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-N70YZw+R6WDP9EEd5xAT3xd+SgZFZsllXR6kclq6U8e2thlakNpWCKhuOiWfCKU8HpeWOyL+2ArSX8uDszMytA==", "signatures": [{"sig": "MEUCICr8eNVrifVVXFjPs2p0NGEmBte/8VlcUtETCNSDDCqzAiEAz5pj5RViEPOusSFLSs+33T+CPbH908GvULnolMFD2Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59397}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "0cdd5603e8b88f5d75abe0dd9122ef1b9ecc27d3", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2", "@csstools/css-parser-algorithms": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.0.2_1728563165394_0.3439044306301666", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@csstools/css-calc", "version": "2.0.3", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.0.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "51d68e75b19c686a4aa916bbe647055f42687682", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-UAhqOt43s8e4MfLAnIS1OmB/lDN32t03YObodmFyy60+1i6ZsT2rlwBEdajH6zDFS/TGogsvgMamV5GzZt2muA==", "signatures": [{"sig": "MEQCIDf8jt2xfHTHEadlLT87hCUlB+Hl/YxH2jP+E6OtBJZbAiAf8p9MorBdmbhq/uZBbBCnOSclNla8LtbHzQeF1imLIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59165}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "36114deb65e50650a96d575d79d2a23d6b5ddec8", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2", "@csstools/css-parser-algorithms": "^3.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.0.3_1729719951118_0.897057770397756", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@csstools/css-calc", "version": "2.0.4", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.0.4", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "c04be9b80a65dc70b82a6acd8ab3bcf3301827ad", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.4.tgz", "fileCount": 7, "integrity": "sha512-8/iCd8lH10gKNsq5detnbGWiFd6PXK2wB8wjE6fHNNhtqvshyMrIJgffwRcw6yl/gzGTH+N1i+KRhjqHxqYTmg==", "signatures": [{"sig": "MEYCIQDXLgLPNmWTcmPX7lkp9HyoWEzwJR7ca9NScIRFLBcEGwIhAIHbqU8yd4ICrfDnWwhFaz9n9jFbEG+6+AJ8BUf6cPwZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59397}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "6eed18ad7375a5befc15f918647a522fbec2e18b", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.7.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.0.4_1730497639862_0.6250386546869229", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@csstools/css-calc", "version": "2.1.0", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.1.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "3f28b8f8f736b8f78abbc75eebd55c756207e773", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-X69PmFOrjTZfN5ijxtI8hZ9kRADFSLrmmQ6hgDJ272Il049WGKpDY64KhrFm/7rbWve0z81QepawzjkKlqkNGw==", "signatures": [{"sig": "MEUCIQDIaqN2slxawRfmjUNWqlyiRD5Qjd0OALmCWsivnx/MhgIgXDtE8asWZa4ytBKs3NewjJeILvby5ixGbNUN+xPMC1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67522}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "2d82f961d6153a69812e5b13c30ecb6004d95398", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.9.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.11.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.1.0_1731322692252_0.7010667454174457", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@csstools/css-calc", "version": "2.1.1", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.1.1", "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a7dbc66627f5cf458d42aed14bda0d3860562383", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.1.tgz", "fileCount": 7, "integrity": "sha512-rL7kaUnTkL9K+Cvo2pnCieqNpTKgQzy5f+N+5Iuko9HAoasP+xgprVh7KN/MaJVvVL1l0EzQq2MoqBHKSrDrag==", "signatures": [{"sig": "MEYCIQCWlxP3fKOaOlkUEwuJQueioJ1/24QXH3lB1A9CVx9NHwIhANlzc1YNDq8ntzZhqVm4RiIHXe/dA55mlX6KgNXqulQY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67658}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "e6a65c3eb424441f875ed510323af3a504369fd7", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.9.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.1.1_1735321331055_0.13647923564809172", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.2": {"name": "@csstools/css-calc", "version": "2.1.2", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.1.2", "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "bffd55f002dab119b76d4023f95cd943e6c8c11e", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-TklMyb3uBB28b5uQdxjReG4L80NxAqgrECqLZFQbyLekwwlcDDS8r3f07DKqeo8C4926Br0gf/ZDe17Zv4wIuw==", "signatures": [{"sig": "MEYCIQDdeMxYe/fv4iscvcZ/dr+R/eSV1Jf76nPA4wRgqvyX/gIhAJY66bjlC0NEkeGJpow+3+XCpfIF8uhDMkxx6HqE8W7H", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67883}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "f69cb90649694f8fcbd33cf1368a7d0f068e107a", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.9.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.1.2_1740330316487_0.12995291725608515", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.3": {"name": "@csstools/css-calc", "version": "2.1.3", "keywords": ["calc", "css"], "license": "MIT", "_id": "@csstools/css-calc@2.1.3", "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "6f68affcb569a86b91965e8622d644be35a08423", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.3.tgz", "fileCount": 7, "integrity": "sha512-XBG3talrhid44BY1x3MHzUx/aTG8+x/Zi57M4aTKK9RFB4aLlF3TTSzfzn8nWVHWL3FgAXAxmupmDd6VWww+pw==", "signatures": [{"sig": "MEYCIQCCunXbKJ/vhz/DJnIHCdpbZKrusWtylm+N/lohSpGBTwIhALo8mrtNNwjta+YMXSYJBTgCinX98MXBMrqXYADckkSz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70683}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "269a9554c499478fcc1efc6c41a02c3fc15157a5", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-calc"}, "_npmVersion": "10.9.0", "description": "Solve CSS math expressions", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/css-calc_2.1.3_1745079190028_0.5699906192203676", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.4": {"name": "@csstools/css-calc", "description": "Solve CSS math expressions", "version": "2.1.4", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-calc"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "keywords": ["calc", "css"], "_id": "@csstools/css-calc@2.1.4", "gitHead": "7a21bdf28305a0915af8e002b98ba82bc25a1573", "types": "./dist/index.d.ts", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ==", "shasum": "8473f63e2fcd6e459838dd412401d5948f224c65", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.4.tgz", "fileCount": 7, "unpackedSize": 71012, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDvhPy0nwpA33lpHyiREpwn9tAedgWa3g2RLmzkaZROQAIgHFqbHVR1KAIJ819dfJZ5CWGvWM11/aLgFxIz7dqTug0="}]}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/css-calc_2.1.4_1748342870596_0.7471333810167216"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-02-21T16:21:27.801Z", "modified": "2025-05-27T10:47:50.974Z", "1.0.0": "2023-02-21T16:21:28.079Z", "1.0.1": "2023-03-25T07:52:29.420Z", "1.1.0": "2023-04-10T07:48:00.113Z", "1.1.1": "2023-04-10T11:02:34.696Z", "1.1.2": "2023-07-03T08:08:38.131Z", "1.1.3": "2023-07-24T16:20:18.410Z", "1.1.4": "2023-09-24T19:44:25.539Z", "1.1.5": "2023-12-15T23:22:35.191Z", "1.1.6": "2023-12-31T16:31:31.753Z", "1.1.7": "2024-02-19T08:05:50.213Z", "1.2.0": "2024-03-13T18:47:53.132Z", "1.2.1": "2024-05-04T16:06:31.133Z", "1.2.2": "2024-05-04T21:13:44.407Z", "1.2.3": "2024-06-29T21:58:29.356Z", "1.2.4": "2024-07-06T09:34:45.844Z", "2.0.0": "2024-08-03T21:40:15.145Z", "2.0.1": "2024-08-18T15:48:59.479Z", "2.0.2": "2024-10-10T12:26:05.580Z", "2.0.3": "2024-10-23T21:45:51.323Z", "2.0.4": "2024-11-01T21:47:20.112Z", "2.1.0": "2024-11-11T10:58:12.407Z", "2.1.1": "2024-12-27T17:42:11.375Z", "2.1.2": "2025-02-23T17:05:16.689Z", "2.1.3": "2025-04-19T16:13:10.270Z", "2.1.4": "2025-05-27T10:47:50.767Z"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "license": "MIT", "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc#readme", "keywords": ["calc", "css"], "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-calc"}, "description": "Solve CSS math expressions", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# CSS Calc <img src=\"https://cssdb.org/images/css.svg\" alt=\"for CSS\" width=\"90\" height=\"90\" align=\"right\">\n\n[<img alt=\"npm version\" src=\"https://img.shields.io/npm/v/@csstools/css-calc.svg\" height=\"20\">][npm-url]\n[<img alt=\"Build Status\" src=\"https://github.com/csstools/postcss-plugins/actions/workflows/test.yml/badge.svg?branch=main\" height=\"20\">][cli-url]\n[<img alt=\"Discord\" src=\"https://shields.io/badge/Discord-5865F2?logo=discord&logoColor=white\">][discord]\n\nImplemented from : https://drafts.csswg.org/css-values-4/ on 2023-02-17\n\n## Usage\n\nAdd [CSS calc] to your project:\n\n```bash\nnpm install @csstools/css-calc @csstools/css-parser-algorithms @csstools/css-tokenizer --save-dev\n```\n\n### With string values :\n\n```mjs\nimport { calc } from '@csstools/css-calc';\n\n// '20'\nconsole.log(calc('calc(10 * 2)'));\n```\n\n### With component values :\n\n```mjs\nimport { stringify, tokenizer } from '@csstools/css-tokenizer';\nimport { parseCommaSeparatedListOfComponentValues } from '@csstools/css-parser-algorithms';\nimport { calcFromComponentValues } from '@csstools/css-calc';\n\nconst t = tokenizer({\n\tcss: 'calc(10 * 2)',\n});\n\nconst tokens = [];\n\n{\n\twhile (!t.endOfFile()) {\n\t\ttokens.push(t.nextToken());\n\t}\n\n\ttokens.push(t.nextToken()); // EOF-token\n}\n\nconst result = parseCommaSeparatedListOfComponentValues(tokens, {});\n\n// filter or mutate the component values\n\nconst calcResult = calcFromComponentValues(result, { precision: 5, toCanonicalUnits: true });\n\n// filter or mutate the component values even further\n\nconst calcResultStr = calcResult.map((componentValues) => {\n\treturn componentValues.map((x) => stringify(...x.tokens())).join('');\n}).join(',');\n\n// '20'\nconsole.log(calcResultStr);\n```\n\n### Options\n\n#### `precision` :\n\nThe default precision is fairly high.\nIt aims to be high enough to make rounding unnoticeable in the browser.\n\nYou can set it to a lower number to suit your needs.\n\n```mjs\nimport { calc } from '@csstools/css-calc';\n\n// '0.3'\nconsole.log(calc('calc(1 / 3)', { precision: 1 }));\n// '0.33'\nconsole.log(calc('calc(1 / 3)', { precision: 2 }));\n```\n\n#### `globals` :\n\nPass global values as a map of key value pairs.\n\n> Example : Relative color syntax (`lch(from pink calc(l / 2) c h)`) exposes color channel information as ident tokens.\n> By passing globals for `l`, `c` and `h` it is possible to solve nested `calc()`'s.\n\n```mjs\nimport { calc } from '@csstools/css-calc';\n\nconst globals = new Map([\n\t['a', '10px'],\n\t['b', '2rem'],\n]);\n\n// '20px'\nconsole.log(calc('calc(a * 2)', { globals: globals }));\n// '6rem'\nconsole.log(calc('calc(b * 3)', { globals: globals }));\n```\n\n#### `toCanonicalUnits` :\n\nBy default this package will try to preserve units.\nThe heuristic to do this is very simplistic.\nWe take the first unit we encounter and try to convert other dimensions to that unit.\n\nThis better matches what users expect from a CSS dev tool.\n\nIf you want to have outputs that are closes to CSS serialized values you can pass `toCanonicalUnits: true`.\n\n```mjs\nimport { calc } from '@csstools/css-calc';\n\n// '20hz'\nconsole.log(calc('calc(0.01khz + 10hz)', { toCanonicalUnits: true }));\n\n// '20hz'\nconsole.log(calc('calc(10hz + 0.01khz)', { toCanonicalUnits: true }));\n\n// '0.02khz' !!!\nconsole.log(calc('calc(0.01khz + 10hz)', { toCanonicalUnits: false }));\n\n// '20hz'\nconsole.log(calc('calc(10hz + 0.01khz)', { toCanonicalUnits: false }));\n```\n\n[cli-url]: https://github.com/csstools/postcss-plugins/actions/workflows/test.yml?query=workflow/test\n[discord]: https://discord.gg/bUadyRwkJS\n[npm-url]: https://www.npmjs.com/package/@csstools/css-calc\n\n[CSS calc]: https://github.com/csstools/postcss-plugins/tree/main/packages/css-calc\n", "readmeFilename": "README.md"}