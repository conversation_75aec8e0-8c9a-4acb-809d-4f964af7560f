{"_id": "json-stable-stringify-without-jsonify", "_rev": "4-0905a28a1152be0c16d3397f26092543", "name": "json-stable-stringify-without-jsonify", "description": "deterministic JSON.stringify() with custom sorting to get deterministic hashes from stringified results, with no public domain dependencies", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.1": {"name": "json-stable-stringify-without-jsonify", "version": "1.0.1", "description": "deterministic JSON.stringify() with custom sorting to get deterministic hashes from stringified results, with no public domain dependencies", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "ff/5", "ff/latest", "chrome/15", "chrome/latest", "safari/latest", "opera/latest"]}, "repository": {"type": "git", "url": "git://github.com/samn/json-stable-stringify.git"}, "homepage": "https://github.com/samn/json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "sort", "stable"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "c0b3c36d976c54e31a814c492cd1c2557a4d3758", "bugs": {"url": "https://github.com/samn/json-stable-stringify/issues"}, "_id": "json-stable-stringify-without-jsonify@1.0.1", "_shasum": "9db7b59496ad3f3cfef30a75142d2d930ad72651", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.1", "_npmUser": {"name": "samn", "email": "<EMAIL>"}, "dist": {"shasum": "9db7b59496ad3f3cfef30a75142d2d930ad72651", "tarball": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFjOwGi+hLz7kZMui+xHJqM0hFHK7eZAu52byVYk1E9nAiA3GpU/bRQUwrysA4y0HARDeHLkYpY5vCulOewuhM77kA=="}]}, "maintainers": [{"name": "samn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/json-stable-stringify-without-jsonify-1.0.1.tgz_1481839233500_0.5462748887948692"}, "directories": {}}}, "readme": "# json-stable-stringify\n\nThis is the same as https://github.com/substack/json-stable-stringify but it doesn't depend on libraries without licenses (jsonify).\n\ndeterministic version of `JSON.stringify()` so you can get a consistent hash\nfrom stringified results\n\nYou can also pass in a custom comparison function.\n\n[![browser support](https://ci.testling.com/substack/json-stable-stringify.png)](https://ci.testling.com/substack/json-stable-stringify)\n\n[![build status](https://secure.travis-ci.org/substack/json-stable-stringify.png)](http://travis-ci.org/substack/json-stable-stringify)\n\n# example\n\n``` js\nvar stringify = require('json-stable-stringify');\nvar obj = { c: 8, b: [{z:6,y:5,x:4},7], a: 3 };\nconsole.log(stringify(obj));\n```\n\noutput:\n\n```\n{\"a\":3,\"b\":[{\"x\":4,\"y\":5,\"z\":6},7],\"c\":8}\n```\n\n# methods\n\n``` js\nvar stringify = require('json-stable-stringify')\n```\n\n## var str = stringify(obj, opts)\n\nReturn a deterministic stringified string `str` from the object `obj`.\n\n## options\n\n### cmp\n\nIf `opts` is given, you can supply an `opts.cmp` to have a custom comparison\nfunction for object keys. Your function `opts.cmp` is called with these\nparameters:\n\n``` js\nopts.cmp({ key: akey, value: avalue }, { key: bkey, value: bvalue })\n```\n\nFor example, to sort on the object key names in reverse order you could write:\n\n``` js\nvar stringify = require('json-stable-stringify');\n\nvar obj = { c: 8, b: [{z:6,y:5,x:4},7], a: 3 };\nvar s = stringify(obj, function (a, b) {\n    return a.key < b.key ? 1 : -1;\n});\nconsole.log(s);\n```\n\nwhich results in the output string:\n\n```\n{\"c\":8,\"b\":[{\"z\":6,\"y\":5,\"x\":4},7],\"a\":3}\n```\n\nOr if you wanted to sort on the object values in reverse order, you could write:\n\n```\nvar stringify = require('json-stable-stringify');\n\nvar obj = { d: 6, c: 5, b: [{z:3,y:2,x:1},9], a: 10 };\nvar s = stringify(obj, function (a, b) {\n    return a.value < b.value ? 1 : -1;\n});\nconsole.log(s);\n```\n\nwhich outputs:\n\n```\n{\"d\":6,\"c\":5,\"b\":[{\"z\":3,\"y\":2,\"x\":1},9],\"a\":10}\n```\n\n### space\n\nIf you specify `opts.space`, it will indent the output for pretty-printing.\nValid values are strings (e.g. `{space: \\t}`) or a number of spaces\n(`{space: 3}`).\n\nFor example:\n\n```js\nvar obj = { b: 1, a: { foo: 'bar', and: [1, 2, 3] } };\nvar s = stringify(obj, { space: '  ' });\nconsole.log(s);\n```\n\nwhich outputs:\n\n```\n{\n  \"a\": {\n    \"and\": [\n      1,\n      2,\n      3\n    ],\n    \"foo\": \"bar\"\n  },\n  \"b\": 1\n}\n```\n\n### replacer\n\nThe replacer parameter is a function `opts.replacer(key, value)` that behaves\nthe same as the replacer\n[from the core JSON object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Using_native_JSON#The_replacer_parameter).\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install json-stable-stringify\n```\n\n# license\n\nMIT\n", "maintainers": [{"name": "samn", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:32:42.071Z", "created": "2016-12-15T22:00:35.805Z", "1.0.1": "2016-12-15T22:00:35.805Z"}, "homepage": "https://github.com/samn/json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "sort", "stable"], "repository": {"type": "git", "url": "git://github.com/samn/json-stable-stringify.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/samn/json-stable-stringify/issues"}, "license": "MIT", "readmeFilename": "readme.markdown", "users": {"flumpus-dev": true}}