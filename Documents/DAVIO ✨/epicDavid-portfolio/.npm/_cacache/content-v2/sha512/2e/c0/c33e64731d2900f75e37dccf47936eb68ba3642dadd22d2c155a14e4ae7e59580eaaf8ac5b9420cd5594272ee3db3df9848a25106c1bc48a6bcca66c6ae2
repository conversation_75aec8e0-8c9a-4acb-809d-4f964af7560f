{"_id": "any-promise", "_rev": "27-ff4600162a7dddb4298528aa91fa559f", "name": "any-promise", "description": "Resolve any installed ES6 compatible promise", "dist-tags": {"latest": "1.3.0"}, "versions": {"0.0.1": {"name": "any-promise", "version": "0.0.1", "description": "Resolve any installed ES6 compatible promise", "main": "any-promise.js", "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "https://github.com/kevinbeaty/any-promise"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"promise": "~6.0.1", "es6-promise": "~2.0.0", "rsvp": "~3.0.14", "bluebird": "~2.3.10", "when": "~3.5.2", "q": "~1.0.1", "native-promise-only": "~0.7.6-a", "promises-aplus-tests": "~2.1.0", "mocha": "~2.0.1"}, "_id": "any-promise@0.0.1", "dist": {"shasum": "8d0c4a1910cede7ba0410ebd76503a69659f5aca", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-0.0.1.tgz", "integrity": "sha512-bBfATBzzw3DEj8NueSKx/1+voY16dEsWXYnPB2s57vpMGOH/g3DL0B6s7fWRe1UfQWClVGDAq/LbEumiTKNRuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHT+vclvX51GwVanbXX4TEtf7+yjSRlV3NC/L2cvalPRAiEAyd8lWJC/OBjJ6AsqTaarqL23+JItq3/gB2gmRb/LBhk="}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.2": {"name": "any-promise", "version": "0.0.2", "description": "Resolve any installed ES6 compatible promise", "main": "any-promise.js", "browser": "any-promise-shim.js", "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "https://github.com/kevinbeaty/any-promise"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"promise": "~6.0.1", "es6-promise": "~2.0.0", "rsvp": "~3.0.14", "bluebird": "~2.3.10", "when": "~3.5.2", "q": "~1.0.1", "native-promise-only": "~0.7.6-a", "promises-aplus-tests": "~2.1.0", "mocha": "~2.0.1"}, "_id": "any-promise@0.0.2", "dist": {"shasum": "352babf996ab367c5145cee93c0ec63171d0941e", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-0.0.2.tgz", "integrity": "sha512-RtGU9vJvaqIlRDJR9hvBbncMoA0Jsr7zgYoNMJDziUxuJqKO83GC/Vsp1336IscMEAxvd3xcznCY7/czxFKkIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdOcgdjw4sHgsZkwyGIK6Os+lFQfJOSF4rvKIY0SithQIgEmw8sOG2cBJxSnlOun7f8TPfRBb+sXQKhfNNFjEHIn8="}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.0": {"name": "any-promise", "version": "0.1.0", "description": "Resolve any installed ES6 compatible promise", "main": "any-promise.js", "browser": "any-promise-shim.js", "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "https://github.com/kevinbeaty/any-promise"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"promise": "~6.0.1", "es6-promise": "~2.0.1", "rsvp": "~3.0.16", "bluebird": "~2.5.3", "when": "~3.6.4", "q": "~1.1.2", "native-promise-only": "~0.7.6-a", "promises-aplus-tests": "~2.1.0", "mocha": "~2.1.0"}, "_id": "any-promise@0.1.0", "dist": {"shasum": "830b680aa7e56f33451d4b049f3bd8044498ee27", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-0.1.0.tgz", "integrity": "sha512-lqzY9o+BbeGHRCOyxQkt/Tgvz0IZhTmQiA+LxQW8wSNpcTbj8K+0cZiSEvbpNZZP9/11Gy7dnLO3GNWUXO4d1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCglEZmU+29esIDfE4fwKnFQ5/DATlOtIIcSdlEhqTBAQIgZ9J6XPBvzsDjB3enfuG2W6F5tlxiq0TPMjeA9kvfhXY="}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.2.0": {"name": "any-promise", "version": "0.2.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"promise": "~7.1.1", "es6-promise": "~3.0.2", "rsvp": "~3.1.0", "bluebird": "~3.1.5", "when": "~3.7.7", "q": "~1.4.1", "native-promise-only": "~0.8.1", "promises-aplus-tests": "~2.1.0", "mocha": "~2.4.2"}, "gitHead": "3f75f1c79285b8137e671a362bccd0f2db7e26ec", "_id": "any-promise@0.2.0", "_shasum": "0ad4f1d32108b13edc663a72a0146b26845cc4f5", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0ad4f1d32108b13edc663a72a0146b26845cc4f5", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-0.2.0.tgz", "integrity": "sha512-lrammFVMf5x4hZa5rnKN90JHhn60lY4w/6oVz3Mb7KjSRKBcT2UjMgcW5HJ9ueeLzH7wm4dcVqMLN7F8zrD3EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCH39bqh85bXqJfx7uLa8wCMk/jahhbyFynYYblSGwIcAIgWkL9L/Esqw3Jp5IN9B/gCqEV2K5n2AHaUVpkCY5ck3g="}]}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}]}, "1.0.0": {"name": "any-promise", "version": "1.0.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"promise": "~7.1.1", "es6-promise": "~3.0.2", "rsvp": "~3.1.0", "bluebird": "~3.1.5", "when": "~3.7.7", "q": "~1.4.1", "native-promise-only": "~0.8.1", "promises-aplus-tests": "~2.1.0", "mocha": "~2.4.2"}, "gitHead": "968ac51c7ff38b32047efde4eee101ce82bc709a", "_id": "any-promise@1.0.0", "_shasum": "033bc631313e235e739e1e4219ffdff026881c4f", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "033bc631313e235e739e1e4219ffdff026881c4f", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-1.0.0.tgz", "integrity": "sha512-4KJpnW+HX/1VGfbe3++J5m49/42A32BpaoeTgkK4gKviNqEyaZM54VbufhT6ZC55pAXWzDS8WhNXDbKMhZfF5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCA0kBEHE6W33Xatwp2p1WXnO93e/34G0wZwAAOC/J2mAIhAIVCcXasuCAr/kxG0JaG9O2U+rT+R6MGtAgSEeQpHBrZ"}]}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}]}, "1.1.0": {"name": "any-promise", "version": "1.1.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"promise": "^7.0.0", "es6-promise": "^3.0.0", "rsvp": "^3.0.0", "bluebird": "^3.0.0", "when": "^3.0.0", "q": "^1.0.0", "native-promise-only": "^0.8.0", "promises-aplus-tests": "^2.0.0", "mocha": "^2.0.0"}, "gitHead": "192f4ef393db3193582199dd3595990c9acaf1cd", "_id": "any-promise@1.1.0", "_shasum": "00e2a4f78b22454ff6dfec436edf2f93a8c59fa8", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "00e2a4f78b22454ff6dfec436edf2f93a8c59fa8", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-1.1.0.tgz", "integrity": "sha512-81vSRKbMk+wid+WqwJ4Hzwyih5f3RXsWqGBuAFuUzMP2r+zI63fUkr68yM0kgI8glYhmSURYvN0borZ8Cspl8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBfZvv+Um3FI/9eOPTdxDzJ44IO95Ov/aAon6+7nJZwQAiB1FHLJDK3n68O7eytd1k4YILrAXXpm3aji4zdwuyH/bg=="}]}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/any-promise-1.1.0.tgz_1454468553798_0.9758589719422162"}}, "1.2.0": {"name": "any-promise", "version": "1.2.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "mocha test/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"bluebird": "^3.0.0", "es6-promise": "^3.0.0", "mocha": "^2.0.0", "native-promise-only": "^0.8.0", "phantomjs-prebuilt": "^2.1.7", "promise": "^7.0.0", "promises-aplus-tests": "^2.0.0", "q": "^1.0.0", "rsvp": "^3.0.0", "when": "^3.0.0", "zuul": "^3.10.1"}, "gitHead": "cecab1f12408387ce87eef570573be10758556d9", "_id": "any-promise@1.2.0", "_shasum": "4f2c28e424e2830b34ec95bd77699940c834a4a9", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4f2c28e424e2830b34ec95bd77699940c834a4a9", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-1.2.0.tgz", "integrity": "sha512-+mGzsRaheypSV+AKVaLz6M4Lc4gVo2t9O/lDvafuxVdmQyMwsS5iKe2aYh/IJxObZODd0K3Gd+k8Ba6KFiHOZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA3agm2K+5iX+63a2iJHKs6W5gvJ8k6poHcm+nJXdc1MAiB0ysa/vBf7/zzvZ6jpFzHqXVOAXRl7P2EFaE46vsjEjQ=="}]}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/any-promise-1.2.0.tgz_1462284979104_0.33836774318479"}}, "1.3.0": {"name": "any-promise", "version": "1.3.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "typings": "index.d.ts", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "ava"}, "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"ava": "^0.14.0", "bluebird": "^3.0.0", "es6-promise": "^3.0.0", "is-promise": "^2.0.0", "lie": "^3.0.0", "mocha": "^2.0.0", "native-promise-only": "^0.8.0", "phantomjs-prebuilt": "^2.0.0", "pinkie": "^2.0.0", "promise": "^7.0.0", "q": "^1.0.0", "rsvp": "^3.0.0", "vow": "^0.4.0", "when": "^3.0.0", "zuul": "^3.0.0"}, "gitHead": "39a1034e7345ca5f0f0a2e6cc82f3bf8e308b2cc", "_id": "any-promise@1.3.0", "_shasum": "abc6afeedcea52e809cdc0376aed3ce39635d17f", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "abc6afeedcea52e809cdc0376aed3ce39635d17f", "tarball": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "integrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSkTzUFQ/JyEvNjiVMv8AUdfL3dH7W22562obmjYAOTAIhAI0GdkMrhA3oAdJJRX0Xm6DESeC6a8TEmC+cQxK29TYO"}]}, "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/any-promise-1.3.0.tgz_1462709704988_0.08345960266888142"}}}, "readme": "## Any Promise\n\n[![Build Status](https://secure.travis-ci.org/kevinbeaty/any-promise.svg)](http://travis-ci.org/kevinbeaty/any-promise)\n\nLet your library support any ES 2015 (ES6) compatible `Promise` and leave the choice to application authors. The application can *optionally* register its preferred `Promise` implementation and it will be exported when requiring `any-promise` from library code.\n\nIf no preference is registered, defaults to the global `Promise` for newer Node.js versions. The browser version defaults to the window `Promise`, so polyfill or register as necessary.\n\n### Usage with global Promise:\n\nAssuming the global `Promise` is the desired implementation:\n\n```bash\n# Install any libraries depending on any-promise\n$ npm install mz\n```\n\nThe installed libraries will use global Promise by default.\n\n```js\n// in library\nvar Promise = require('any-promise')  // the global Promise\n\nfunction promiseReturningFunction(){\n    return new Promise(function(resolve, reject){...})\n}\n```\n\n### Usage with registration:\n\nAssuming `bluebird` is the desired Promise implementation:\n\n```bash\n# Install preferred promise library\n$ npm install bluebird\n# Install any-promise to allow registration\n$ npm install any-promise\n# Install any libraries you would like to use depending on any-promise\n$ npm install mz\n```\n\nRegister your preference in the application entry point before any other `require` of packages that load `any-promise`:\n\n```javascript\n// top of application index.js or other entry point\nrequire('any-promise/register/bluebird')\n\n// -or- Equivalent to above, but allows customization of Promise library\nrequire('any-promise/register')('bluebird', {Promise: require('bluebird')})\n```\n\nNow that the implementation is registered, you can use any package depending on `any-promise`:\n\n\n```javascript\nvar fsp = require('mz/fs') // mz/fs will use registered bluebird promises\nvar Promise = require('any-promise')  // the registered bluebird promise \n```\n\nIt is safe to call `register` multiple times, but it must always be with the same implementation.\n\nAgain, registration is *optional*. It should only be called by the application user if overriding the global `Promise` implementation is desired.\n\n### Optional Application Registration\n\nAs an application author, you can *optionally* register a preferred `Promise` implementation on application startup (before any call to `require('any-promise')`:\n\nYou must register your preference before any call to `require('any-promise')` (by you or required packages), and only one implementation can be registered. Typically, this registration would occur at the top of the application entry point.\n\n\n#### Registration shortcuts\n\nIf you are using a known `Promise` implementation, you can register your preference with a shortcut:\n\n\n```js\nrequire('any-promise/register/bluebird')\n// -or-\nimport 'any-promise/register/q';\n```\n\nShortcut registration is the preferred registration method as it works in the browser and Node.js. It is also convenient for using with `import` and many test runners, that offer a `--require` flag:\n\n```\n$ ava --require=any-promise/register/bluebird test.js\n```\n\nCurrent known implementations include `bluebird`, `q`, `when`, `rsvp`, `es6-promise`, `promise`, `native-promise-only`, `pinkie`, `vow` and `lie`. If you are not using a known implementation, you can use another registration method described below.\n\n\n#### Basic Registration\n\nAs an alternative to registration shortcuts, you can call the `register` function with the preferred `Promise` implementation. The benefit of this approach is that a `Promise` library can be required by name without being a known implementation.  This approach does NOT work in the browser. To use `any-promise` in the browser use either registration shortcuts or specify the `Promise` constructor using advanced registration (see below).\n\n```javascript\nrequire('any-promise/register')('when')\n// -or- require('any-promise/register')('any other ES6 compatible library (known or otherwise)')\n```\n\nThis registration method will try to detect the `Promise` constructor from requiring the specified implementation.  If you would like to specify your own constructor, see advanced registration.\n\n\n#### Advanced Registration\n\nTo use the browser version, you should either install a polyfill or explicitly register the `Promise` constructor:\n\n```javascript\nrequire('any-promise/register')('bluebird', {Promise: require('bluebird')})\n```\n\nThis could also be used for registering a custom `Promise` implementation or subclass.\n\nYour preference will be registered globally, allowing a single registration even if multiple versions of `any-promise` are installed in the NPM dependency tree or are using multiple bundled JavaScript files in the browser. You can bypass this global registration in options:\n\n\n```javascript\nrequire('../register')('es6-promise', {Promise: require('es6-promise').Promise, global: false})\n```\n\n### Library Usage\n\nTo use any `Promise` constructor, simply require it:\n\n```javascript\nvar Promise = require('any-promise');\n\nreturn Promise\n  .all([xf, f, init, coll])\n  .then(fn);\n\n\nreturn new Promise(function(resolve, reject){\n  try {\n    resolve(item);\n  } catch(e){\n    reject(e);\n  }\n});\n\n```\n\nExcept noted below, libraries using `any-promise` should only use [documented](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) functions as there is no guarantee which implementation will be chosen by the application author.  Libraries should never call `register`, only the application user should call if desired.\n\n\n#### Advanced Library Usage\n\nIf your library needs to branch code based on the registered implementation, you can retrieve it using `var impl = require('any-promise/implementation')`, where `impl` will be the package name (`\"bluebird\"`, `\"when\"`, etc.) if registered, `\"global.Promise\"` if using the global version on Node.js, or `\"window.Promise\"` if using the browser version. You should always include a default case, as there is no guarantee what package may be registered.\n\n\n### Support for old Node.js versions\n\nNode.js versions prior to `v0.12` may have contained buggy versions of the global `Promise`. For this reason, the global `Promise` is not loaded automatically for these old versions.  If using `any-promise` in Node.js versions versions `<= v0.12`, the user should register a desired implementation.\n\nIf an implementation is not registered, `any-promise` will attempt to discover an installed `Promise` implementation.  If no implementation can be found, an error will be thrown on `require('any-promise')`.  While the auto-discovery usually avoids errors, it is non-deterministic. It is recommended that the user always register a preferred implementation for older Node.js versions.\n\nThis auto-discovery is only available for Node.jS versions prior to `v0.12`. Any newer versions will always default to the global `Promise` implementation.\n\n### Related\n\n- [any-observable](https://github.com/sindresorhus/any-observable) - `any-promise` for Observables.\n\n", "maintainers": [{"name": "kevin<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T03:07:19.066Z", "created": "2014-11-06T01:20:39.140Z", "0.0.1": "2014-11-06T01:20:39.140Z", "0.0.2": "2014-11-09T02:40:29.490Z", "0.1.0": "2014-12-31T17:45:57.008Z", "0.2.0": "2016-01-29T23:37:50.143Z", "1.0.0": "2016-01-31T19:54:53.013Z", "1.1.0": "2016-02-03T03:02:34.874Z", "1.2.0": "2016-05-03T14:16:21.155Z", "1.3.0": "2016-05-08T12:15:06.060Z"}, "homepage": "http://github.com/kevinbeaty/any-promise", "keywords": ["promise", "es6"], "repository": {"type": "git", "url": "git+https://github.com/kevinbeaty/any-promise.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"allain": true, "rsp": true, "zaptun": true, "tarunbk": true, "btd": true, "coalesce": true, "jsumners": true, "scottfreecode": true, "nickeltobias": true, "andfaulkner": true, "panlw": true}}