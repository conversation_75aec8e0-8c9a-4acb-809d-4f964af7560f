{"_id": "@types/yargs-parser", "_rev": "506-9e0c6826099337bebc6834de2d0120e1", "name": "@types/yargs-parser", "dist-tags": {"ts2.2": "13.1.0", "ts2.3": "13.1.0", "ts2.4": "13.1.0", "ts2.5": "13.1.0", "ts2.6": "13.1.0", "ts2.7": "13.1.0", "ts2.8": "15.0.0", "ts2.9": "15.0.0", "ts3.0": "15.0.0", "ts3.1": "15.0.0", "ts3.2": "15.0.0", "ts3.3": "20.2.0", "ts3.4": "20.2.0", "ts3.5": "20.2.0", "ts3.6": "20.2.1", "ts3.7": "20.2.1", "ts3.8": "20.2.1", "ts3.9": "21.0.0", "ts4.0": "21.0.0", "ts4.1": "21.0.0", "ts4.2": "21.0.0", "ts4.3": "21.0.0", "ts4.4": "21.0.0", "ts5.8": "21.0.3", "ts5.7": "21.0.3", "latest": "21.0.3", "ts4.5": "21.0.3", "ts4.6": "21.0.3", "ts4.7": "21.0.3", "ts4.8": "21.0.3", "ts4.9": "21.0.3", "ts5.0": "21.0.3", "ts5.1": "21.0.3", "ts5.2": "21.0.3", "ts5.3": "21.0.3", "ts5.4": "21.0.3", "ts5.5": "21.0.3", "ts5.6": "21.0.3", "ts5.9": "21.0.3"}, "versions": {"11.0.0": {"name": "@types/yargs-parser", "version": "11.0.0", "license": "MIT", "_id": "@types/yargs-parser@11.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "514933c83ec38b8e06b8c9e5ffff637f20474f2e", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.0.tgz", "fileCount": 4, "integrity": "sha512-ZfKjiy9zRHQWWRiQLeushSHE2IgHJ8U/OaABsNodkuufZoWED7Plz6egREZiE8dfrTAnrD8Rw/kUh2S6Iu1W0w==", "signatures": [{"sig": "MEUCIQDrTBDaTBalvKpFWmhYhl2znONWmgKxEiEIQwnrDl63NwIgAxbK8U10RZEiA7AfDSJejr/lhGQeHzAtue/SWFkNqxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv9UECRA9TVsSAnZWagAAMOoQAIkaorqlkF7IAHOo6K8Q\nisiNarXb0yXg8raV9CQ9Mqjd0zAQz2WunP70y5f78O0GcKAvFEqTYmDi4JmJ\nhXMu9rXhhYEuRV14ebMBv3I8vBs80YAXXNOdSd+iVlEdbFVNPuQ72r7G8W1V\nqHj9VJRaXvA+sS53m+TwlfUgAyL8LO+lTxiZgU+Owew9alV6H+QAD+XIRyka\nK14PBxV0ljLjM1e/23ZsKSpE9Wi/pGolGMDJ2xKGCiBKyvrDpZSk0d8TvQXK\npnpeJ+xI14LrZA2P322PpBoDDWD0XhFqRwiDyhOn5qZZBhdlZAGddUlqGsgG\nebEWsMQxBlNKbbsTQmjCmjXqjhKZBozkqJVCOnHc1uhQnhVmzr16XX7oenlR\nNd0bKppbLrlzAxkeQQBZ8IsOzBtrmjreyvMhq53jKroT1Wo3ZVSSu0xwJjqx\n7V5sucHa3w7oj1HW8rBTTVxwRfaU7wtlkgr0wmpcV+1ovCcktrbMm91uNuVs\nWYyO5EVw/DYV9VgRh4N23/cDkLZUJ1q613WusoUTf58yHY6RZN1om7pwIYZT\nFMH2mgr5orARvdMybaxq2V5TcKaBhIsx1vIlBC9xeXFR2zEkhpri7RTXbXOq\nc9UROPkBMXRrbT8thkbin7U5qO6MO/AlnSUq1funlJKiauzqXnxF5ByffS1k\nUT/5\r\n=It4i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {"@types/yargs": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.0_1539298564306_0.04041523982175366", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c72cccda4d802b284757e2db84e0da3b0425327f9e34650473a9524e86067e44"}, "11.0.1": {"name": "@types/yargs-parser", "version": "11.0.1", "license": "MIT", "_id": "@types/yargs-parser@11.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "356284379af0e50eb9fb96ea18aeba335867f620", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.1.tgz", "fileCount": 4, "integrity": "sha512-iLm59ELvdPCqdDAIpxsauiDFob7R2MNV7VivMfdmcrJwz+y7xlPYNvtp9rrBlvuBoeOOtH0hPFokknoSuW5lOQ==", "signatures": [{"sig": "MEQCIHdSyrKRpVRk1bswYRI8s/1+NOgYS3Al/YxLW6yB92ceAiBg5bgud3mb82pWGBXi9BsEPtU9rSojbP0pMSTuGRz4Yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGuC7CRA9TVsSAnZWagAASdYP/RU0WbCVt9BTQubSr0k+\nDyl85YwUoK0F0QiVrwrke9Nl4/rYVaBgOd9MTtbsOAxs+HjZkknThCEQ2Lf/\nKeoUuAYdVGPgImix1nwMwUD8fssHZl5iWnabeskxQmeeIbUFPeQLKm6EGfUS\n+7OJ2Qi0ykR4JbpFzycdf2ANNK46yhPyNZZqK1n5okFYzPa264HnEsQDF3i+\nWJw/6PWdZcKLHuBwMYocWC8pdQO6k+TZ7LtuhHKujx5q2A++ExWUGvmVj+Bz\nJafbDwuntVHAlHm/f/fc1zJfnm4K3aMBkyG1Im64cp7YF083ThXcRh9+jUqK\nDPk8CtKmdQhqjECUS90TxVvmlaQaf8SxLssPCr7VwMUmLD/KfVk1TdgodbLQ\nZWkPv8pyTHfIx+oliSsbZ6pKHo3Sed5zLzpNjwKNVlrR/AYlLsQDi780Rg79\nMPXQCNXEugXe8+YtmlTWAkqQqenWuSDPavzHRgI9/QZyR/iWOvWpyA9MhWSj\nvlBLzJfpRFGt2luxUaTLtzJlRAUAgI6l1o376oMf7DPsEQI372i01vZvHGFG\nDpP4ft+myTUdPBSug2lj0IWhgF8u7e/078gjeh0l2L2yydyhAWQo5qWnsZ2Q\nntxc+bNT3mflxEl+87xbPbmznvi1g4RJcja8dGz5PlKv0oSI0L62+S8aG0D8\n5F2m\r\n=oX7d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {"@types/yargs": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.1_1545265338259_0.3432001673032474", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0c8a4db20ad1516ab478a7d3ce241cf1e6362d0164ed7f4a2cd950e18c75684b"}, "11.0.2": {"name": "@types/yargs-parser", "version": "11.0.2", "license": "MIT", "_id": "@types/yargs-parser@11.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "bee412b129dd04c867c33a8d00e0e3e30d602a46", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.2.tgz", "fileCount": 4, "integrity": "sha512-GClTGwqOZrc0xpaqgg2E6DdZ63ZxL13E3UesvgOiGKMYNrsbKiYO60FSm8a282F9V+ldA9kENGw7QnHPkWuu1A==", "signatures": [{"sig": "MEQCIA/n7sp//+HwB52hz6uQgJE0wFRRXPXBhobpIQEq0W94AiBhv+GvMqqJqkM5WcllBWTQAV/BThVcdCqUyKgejT+LSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIbs9CRA9TVsSAnZWagAAxVUP/1TijRb4qd+wKtJeaLEj\nkAx+SOhPtTJEWhLeByhIPIacT2SycADnQH7MdMBmDJTIBf99+Xh7LCRIx0eM\nLZVUJLAEnVs8MnIctyhdOxHMn2LsACZK/n8rlbOZln+GRV+FXT8x1Sa07Nky\nJGBG6gcDk2aquvFaoHTVQNqYiU2ShbegL3zwPKSCub6uSGg1mD6KpesjFTs9\niuRFN8rKAbTXb45FwlTG8aLx7OYEj2zXU7LJeUoSAkyNZZO1kD37eui5hrbv\nui+WKF6i4TjSIQDVel/wsUDiDEqEO0uiEJC4r/OLEe45vDkmjNr39YAK/Lnh\nmi3LLt50k3+vTtifbRaufo+O9QVbaNnVBHP0tcZRNM+WaDe9JBP9ZwWniEbr\nV+pQjsC3WCt+/ZQmalSwpedc3E2ECIAVVfWr8eZ27LQ1LtrTR6XfHRw09pxU\n4swbBH6s2HLmK5DAu8KqYDLf3KfXWGdy3iIj0HphTZgvUnKFvz1qehdJSR4f\n454XU8HxRGdlE768R/QSZJSiZHlDig0Eg4F5swuby6Pm1ccj89FGe5ee/oqT\nHrG9+XCxpR+VkmY3ybgBXJfZP5pXiqSGDU198AKfsWROC/WzSU5rdRMPK9h+\nD5PngGcMk71CHEnrllweDlRU3bHEtfNqKt2JRvsodbaFZLQSXNOBUFpmcn62\n8WGz\r\n=DZJU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.2_1545714492670_0.09770808373957984", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "30e27a1460573b64b81501c24dcfbc569843a1b72b7ab26d73abd289eea5afb1"}, "13.0.0": {"name": "@types/yargs-parser", "version": "13.0.0", "license": "MIT", "_id": "@types/yargs-parser@13.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "453743c5bbf9f1bed61d959baab5b06be029b2d0", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-13.0.0.tgz", "fileCount": 4, "integrity": "sha512-wBlsw+8n21e6eTd4yVv8YD/E3xq0O6nNnJIquutAsFGE7EyMKz7W6RNT6BRu1SmdgmlCZ9tb0X+j+D6HGr8pZw==", "signatures": [{"sig": "MEUCIGYint9BZamHzgTj6yu3fAsqk08NoZiic5expgr39gwwAiEA97KSoc3pzYTBAgkVKiniuZ4WJJwV6RYd7xCt2LHM9zo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqqlfCRA9TVsSAnZWagAAapwP/jM3FXr+e7IjmZhwVlKj\nX5xp49zJpN7WKF/vAkQEp95WF+H0UDuBgs9lVcH0bJGjklzuXDVmHXggko/9\nbbO+2FFrwdndpFfBUVdGcUNi1MlkWs61Ku7U9K0+yshh99mPOGv0VUjySLQy\nU3yTP2D/obEOgdS2R0g7K9PZrb2fwoQ9BacGSqAXf3v77eoovDQYhLU0AiuR\nL2UVa9pBetlwqzZ2qDtB9X0UH7+ELlTgzHrY6CJzuCK2TxBi7aPUEt7yUWNb\nR8ssDs16hWdlfbOlMGomJblVqsl2/9QeqIYeF14KIi6MV9RZpH7F+QjtaHPK\n5WwXAf4xfV7Y9wwKdoY8vQY5/b9vgDtD660/M53n+LF6ovNfPMulwp9uhs28\nk8dvlhlYHBuxThx0DAIuO5Vls9jZCZCzXle8wlJf561DLlcQnvFc9qj3qWhd\n07stOc0NYeT96DNMvsxznP5FhQPGWuMZDI4tB/UgYQQ0DGblCuVu7IhFLo7j\nkTxqTepvyqtXpQbuUJiC8OSI6vzEtZP70tdVwNl09X0gTJLLO4lCn1KBCQ80\nUAcCAY0vhdM8zaPnpiFhlpKfLbr1aSNyJXb5nS7dQMLwPVYYKTIcq/di9rRS\n5oQ6eysWlzq1SY54Bq6StZfaqNzP63SVr5fllDlqEeleowbb0HtKZ7BNIhzc\n9sV8\r\n=lVFV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.0.0_1554688350389_0.18542212673175684", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "583a6e7f0bfbeca09d249f4b21de121d66fd94ad2073a26ee92fb257b960794e"}, "11.0.3": {"name": "@types/yargs-parser", "version": "11.0.3", "license": "MIT", "_id": "@types/yargs-parser@11.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "8e2020da68b5e361ede75481f584a613b47951de", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.3.tgz", "fileCount": 4, "integrity": "sha512-sH9fKms0dImyf6CEaMb5PpeiMqIwIK9rm8c0Wchac/vuOxaYeQa+w/w8L+eBnpxc9Ih92MtYrI3a40Huk25UVQ==", "signatures": [{"sig": "MEYCIQDePYy8H+K0Bujv1jG4YP7lsZAJWnGvYxZoq+v/JI+x/gIhAJ6iHkE9dNKuN+3hUmstrrJgWsL0rTK1YT0p8Gt3jyBS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqqlyCRA9TVsSAnZWagAAifcP+wcGwic10KPRNLZxUyBM\nmS093pwepSTsALy2VwYd7MF3DV5iSpXctKAxmqjEtpJ8nE8LdxzG+/ZDt1gr\nvLc7gZ2tKhcA0XusqbC9WBimaHJLJ3Zhag88+aeHJ4do7nX9b4CKfm+v1mjj\novHIgggQXpc1qXQsgU7mniXXxl0LMAi5GCT+2VUrCUf0Vjr4fa4ei2nXbGDN\nCtBd6tKSvoMihRAlzG/Nb4AeFmQ/T0dncKm3Wo72MiocJBqk/vosnUdAThcq\nc+G/XM72UFjX6Pw8UQKSgLUg5BTplfusYRkVHFep7Cpr/eSlU2F5x2P8d4e0\nhFITWftcBnA8SPMCPlPt0qVzoqcTVto1cf/kSyvXa+sw5KxRZX7cWILs0LSN\nrmHSL7jTONrGDYIGPlPhJHrQ5WRTEQUDxkC0nGC0chNOgHUKjMRJvTT5RyHw\nA20GM5TSsMXf3WYgZ5FYvnuZ5sGvy4wepXhXDfndiNULHQug20VXMiTzXxY/\nGP4u5bE4dAelhyXX6ZhOBSolFgv+VXHP0Ycs6ieIOEzIXrbOZ8bhn9MVl29C\nIdgYUHPBQ1gJzQUZLrNyABJP/s5o5IlebY+V8Di/5yF863CcRfQPAY7wC+ms\nhZoOVWEhzlAM4hqLjULgSE6X3zlFwG3jTA55YUr4z84m1sNzCoXXtymUC4NX\nzdpG\r\n=vkDN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.3_1554688370330_0.14821276718397836", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e288fc8dfac0820cd5e2d3e8f025c4be515a7138bd597be9fd41c7345029d666"}, "13.1.0": {"name": "@types/yargs-parser", "version": "13.1.0", "license": "MIT", "_id": "@types/yargs-parser@13.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "c563aa192f39350a1d18da36c5a8da382bbd8228", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-13.1.0.tgz", "fileCount": 4, "integrity": "sha512-gCubfBUZ6KxzoibJ+SCUc/57Ms1jz5NjHe4+dI2krNmU5zCPAphyLJYyTOg06ueIyfj+SaCUqmzun7ImlxDcKg==", "signatures": [{"sig": "MEQCIBTk1VbsxIFyuvF9NA7w3aS3RqvUWQHsBsz67rcrhx5CAiAGaXk3dO55XBKALBmtaCx/84JijtGq1hOuGqV2tYyF8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcVKUCRA9TVsSAnZWagAASEMQAJS/1yqim/9p2eF1zDpP\nlz4Gh/UL1X0ND9csGnK6zpaUrWxoBFkQfdWH9kzHiqgQtnKPlV2GW3yyY6o1\nLwmMtCSdAPBWs+ePN5PUS6mAzvdjFlnkesiLrzbR4ofrX0CFF149lJAi0Pgf\nYi6uRl3lf6amRcy+9MJUhf2DinoZ1soyhXuL4xDuR1VfUmhcCaf20fnDEc69\nmNY1B2TS0vty2ViFsTLEa0YJlDM2pnM9FbQFf/1OqXqBvON8ABj7WwyyciT3\nEE94NZMaUMJ5W+pCywgcag8FAfqzZ2qcyuGmMNdULExVQDuTAG5gNLJoiF6Z\n+AqThZSORMLTs84FKZnjMP29wf1CQINd6v3bZUfEX4G3LbwQlLleA+uhf4FR\nxj13ssLAZANfce5kRZu9wqauItdWqMtOIxVxLqrGmAcWDksbDTuLf9AArh+S\nKzNSHqdFg2oYGNiL1WmIeVNSxGrw1BM+4tmmuElPAoIAawzobItjgl/Xzz+U\nCaJEbnfaVy/4qlwILi/3Pi6RgIdXZETip0hrjjNP/GbDVG5TbXnRiRH29PJ4\n/Z4/nygJCLcVNUHGi+SLLf2jjjS3h+NrQIi+W5v2cAvPQTWnK19i+hZKAuK2\n7/tYHoDmr9CNlTNsXHKFVM5+Bg01XjvorGCMVT1+eScgOMa6vdQ/U6/60qS7\n0wZJ\r\n=4D70\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.1.0_1567707795529_0.9251251580340272", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "72de0ca12c18b0e895e8b04198e0599f4b08e2766251f330f2c7e8a1b008986d"}, "15.0.0": {"name": "@types/yargs-parser", "version": "15.0.0", "license": "MIT", "_id": "@types/yargs-parser@15.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "cb3f9f741869e20cce330ffbeb9271590483882d", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-15.0.0.tgz", "fileCount": 4, "integrity": "sha512-FA/BWv8t8ZWJ+gEOnLLd8ygxH/2UFbAvgEonyfN6yWGLKc7zVjbpl2Y4CTjid9h2RfgPP6SEt6uHwEOply00yw==", "signatures": [{"sig": "MEYCIQCszD0UqLDnDpOIv/wZy4WHmgEnxe3tHQsNX77K5S6C3QIhAMfovCIEIWnm7hP6wqEbosG3DnDfUYyOxy/JVIzu+nLg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7996, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHNqsCRA9TVsSAnZWagAAMB4QAJj46LCCL4PdOmLjuVAg\nKeVcZKYFgtrOgkAvCyL/5PjUm/vgKws7AJkX5iwS4xXGHRoUXLTUY6A4M4VK\nPCPZy0G5wpGtBZhhO/FfNDIkawtVDoSueP5VaRMBkKospQgnkrUwIn6dZjK3\nZyG2e3R77JtUEEcMzrJg0e8Ch2T9pEyNdLOU5pd2ocHhYPXTVOmOZB8f0HgW\nPKCzvnsKPsfXAA20ZVUxzG+S5fr6CnMyTP0yiw3pINS7+UzfCBp4eLo/pDxj\nKiTRVNFcMyqqtOPAIWT+2+2Qg+t2ekYaEQDsWrMSZIe7tc/8rcJrpI5iyaKW\nKVoD+X8mrPmEmg4l/ksxQxsc/lOnvHDZu/Wjvm/x1AfsZlG1GPz4VEits3+m\nbXrm6dGs5SHAzFe8uDPQ0Y+QD9InR6foFY2CchNUGTclcsmqPEfI+wLXplCv\nrIpxHetbDyzgzssgi6iRzrHjXR3/eSQ1SF4yZAjn0x/MJADWnoGBOA04Dziv\nlWunCoriMX5tYdIVnGSNo5Mrgo87RFb0/S6oBJsJjKkDscH/WvyP9wXcXkJG\n3X/1ixyllJFU79b932tIqNzkWyTQ+N+lkcWA0puafNx13VNAxHMSET5yfcPf\ncf1urGlW1+MioOHmHfmg7mqMeWVQBSX/o+ciiDUI5YBqC97zWB62oAxrmGLK\naQid\r\n=KNm+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_15.0.0_1578949292493_0.7828167893425924", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dfb678a3324e6b48900f50da7ca18c044ba7c18cf0bfecb96c1c1a6998d736d6"}, "20.2.0": {"name": "@types/yargs-parser", "version": "20.2.0", "license": "MIT", "_id": "@types/yargs-parser@20.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "dist": {"shasum": "dd3e6699ba3237f0348cd085e4698780204842f9", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.0.tgz", "fileCount": 4, "integrity": "sha512-37RSHht+gzzgYeobbG+KWryeAW8J33Nhr69cjTqSYymXVZEN9NbRYWoYlRtDhHKPVT1FyNKwaTPC1NynKZpzRA==", "signatures": [{"sig": "MEQCIDAB+Fdai80LdnIXB6XQOSXCmySN8/Fd3hdMlp3Mjbu8AiApPh9a/YQCSfDMqwgGQWtHlE+piBkQ/IC1QhqRDcMexg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf45WGCRA9TVsSAnZWagAAdjcP/2BhZuUoDq02/uQFbxYi\nJ1+MGJDW1M2fS8la9y6auQKp9UjIGHHVisv2cqx5Mjx1Z95uW4DhF2mGJNlJ\nXpFkVA4psr+yYNJdJKj/CTAFbKGPJ1QRorKy1dj+VU75jXybWSeW1RCJrCsg\n4C3TfCpGOCf4+gOIzxd/2t65NmNFvrG3KuA14kHfF3chyaKwvYl6E+u8fo/u\nu/Uu0bRVtlzuTiwqx/kyKDQkf6wP0Bv7Ncq/2omEZF7PO6JYKQg8y0lzlqYE\nysaxBVCJRrWVPW270sW96/IV3FSGK81VRRZNetpOMTuWqsWIwCVyAX8FDGTN\nG83Hurn5O8FZ3EOyfeO5CS3etaPQ9Lkk18Tx2i/NTJUjcy/giRfq7nd3QMvV\nym4mEg6to4x5xbgQDyuunBInZgjYI0WB0mNNN7ivaYmHC+RriheTDYwOK16Z\nyvl2HsSovrFby5C4QbcHqjcoDO5O7TfSV5JkIqW42ykLuXR4rAo/LTKHeBwX\nZOe8z9CxJc2QvWMge16sbAANg8Is4fWIa5jJNWDoPPA50RCIyMU8alM27sld\ngFu5cAMPfPhnMuILmOrTZOTGaOsZzCMubl7Fj5mOHteCsqd/yNZGBjRE2wYL\nfF4y1gtEdgQ+FrfO1s9191PSpdsmshhG/jHd6w+hunmp/bwGp7fcolugZNlL\nB1FI\r\n=0APJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.0_1608750470215_0.953541447772656", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8910a9d6eab5721fa5cb05ff2f01d5042c4ecba6967efd4739f53c5f0e6b3c33"}, "20.2.1": {"name": "@types/yargs-parser", "version": "20.2.1", "license": "MIT", "_id": "@types/yargs-parser@20.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "3b9ce2489919d9e4fea439b76916abc34b2df129", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.1.tgz", "fileCount": 4, "integrity": "sha512-7tFImggNeNBVMsn0vLrpn1H1uPrUBdnARPTpZoitY37ZrdJREzf7I16tMrlK3hen349gr1NYh8CmZQa7CTG6Aw==", "signatures": [{"sig": "MEUCIQCrGG4XwD0LU/mFaf8wPkWofUjHp5Bg9TP/0eHDyKh4UgIgYP+q11pTw2JKha375FttBtORlseH8ah5Txk13Wqekak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30BmCRA9TVsSAnZWagAARp8P/3fMzxKPowKF1lOlfLcY\nFLs26/ZOqi29pnVLpas590Dy2lZc1mprYtHEewwsyYGMQX9WKAcUXEofSVd4\nMy0p0YlA+XJy4xwF+GYSn9xMejE5OYwzRbcn/VDkLHvGeqi51sJsW2UhGpa8\nBiwjbDUzxAW6KMGW3IE7JtgAJamICr8LHypx3f2UJQ6VKApnPF3xcxEwVQC0\n3txG6bJTdwYUPSr2V/Upern6h1YBoC7NYJZByqzNCpzGctlfwFZJWeyxALHo\nUOYvjyivbNBpOo7hDYB/760p3xlzpE04tWojlkh3GzeUQwF5+pxBUZq9AhZc\n6slVBgHhb2Wx0eWWZDh+5Rsx25UDvB6pRq4Y9RAtF/BdV1DhbNBFAM6NiN4z\nRKgtPQUTQuWO8aO8lrG10S8l4SPkmaeTtz+LN+CIBu/MgsL/qnH0SzMncgJ8\neYeGfecqBl7aNaLU7G5XdshSsYZuHcMqRLyqYcK5bjlFaIo3Nf/GKEgh7qYb\nuJLd1/P1QbDkMYxF6DysXBAR/cokEFoyJZtPf/zlnJJ/BYMFI9i1pihAgKoE\nBPA+NA0RmkqP8vC4pzgQSm67HqGoOEINWTQrQ4QViJX5w952vWEbykUWFJM3\nlH0wNPwgBgp+DmBqwk0vQkfvzzBtD2AgMsfCVlYIvVxnvRBeNVRrK7OhoWGo\nB+0l\r\n=U1Ux\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.1_1625243750128_0.299749846496455", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "692e79ed176a53400e7007c2c3a8ab37780ca4e58b46acaec3e61cd1e365d4d2"}, "11.0.4": {"name": "@types/yargs-parser", "version": "11.0.4", "license": "MIT", "_id": "@types/yargs-parser@11.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "6fcac3b95ab1d22866acfad62153b46fca066e90", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.4.tgz", "fileCount": 4, "integrity": "sha512-l+jxGV/WPsyrdpPi9+jUzZ/lKuL1oJ39MFfj/fiW/a2Dom7BqbZaGDtapIfR2eJMNCkLEZ45yoAarDlGjG4sqQ==", "signatures": [{"sig": "MEYCIQC3evT7BJQbMlH9sOMLrXQ+pyjhdmDb3mId57xtjZdJOgIhALIPElfjzx1NxTZg4AnE+FCIZFRmab9sl6pXrfe+9a3/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30BvCRA9TVsSAnZWagAAXNwP/1nr0aYK6Nc2DLAW6D6q\n+S1U627+KQdnHBDEBHU176Ze/DwhinfblPwoP8MkWkUzCVwfAc6YpZsTIARg\n8iFlf6wfEuZNRUqVe2dgBIJrmuWHZJQbnAsMUlE7NhHdIsicad6dUWJV6gmd\nBBPkQM7BVD7XDe+i0W8n73mVQcie8+fIX/5UkaNegRilZo63U/esfJhHd+lz\n5q+fvbkGI9kza4yVwu/xzae+YsB4IEfcHXEBYZVS34Ya2Xsz6BGvB7phDKkX\nwXwMnw/Eyolj4FEoMLO8VGVLGyN8CY/+YF5yCbaUyTc2z1brP4d91I1bp3TF\nVXWvHJGfcLBRJ4q2uWVFDPigbp9UO5ov/CgzvYaLchCTpXSwTjCVMVHGvaZZ\nEe50kYL4aQWpI16394Na8vRAijOyzVgjiddfWFvuNiHdMr+jli2WptEHNmba\nhPXNkRpOGjRFIw6Yvcay8zkVVPSb5y8+qNgtiBqNsejgIgL77iPrSKBYCX7B\nRES/gcpGCO9JyP5gFBPOLK3ZHM52VpuYLS46R47gSUEbXL4KG8c6J1rXl/sO\nke7lGAcOHiDri74HvTlTng29XFZqV1fUowgYAZzLWRoIUHVUUt94sKSDMXti\nemAKs/p1XrMXYZBXqtRy5rC3W1jsTtJCnIHneGaEYd3bOPC7jDnPgtEhu4xJ\ny/cB\r\n=xPBP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.4_1625243759114_0.9608566062585024", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6477ce298d83e75fe4c73a7dc9f4e2976f50c6d3372d272b511a05b6e982209d"}, "21.0.0": {"name": "@types/yargs-parser", "version": "21.0.0", "license": "MIT", "_id": "@types/yargs-parser@21.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "0c60e537fa790f5f9472ed2776c2b71ec117351b", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.0.tgz", "fileCount": 4, "integrity": "sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==", "signatures": [{"sig": "MEQCIEEO4Jbnu4tXOwSaVFkOmZMLfhyVkNxPfA+DTOMSOAwZAiAdD5AQBz4rcBElbompd+yEoEKbfci91rE+zMSPvGdWig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH6piACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoimRAAkBnKfUEsnKgjRFhNAI1489cmc57+cqvbs2AtpSnIZnWmkidC\r\ne/4cDarQY6nb+XPV43ZLK2JiEMtO0fJt9C9P9WBsEJLnf/qdI+KPUq+6Yg/2\r\n7NM9rrL7unJs9PhfrMGZ2sfhxxA6gGUXtfCXAi8aXxcWknQiyDTIchs37ZNZ\r\nvWPEbQCxGlHNrFFUuzAC4v/8fCdZ9m4TPymCEhYWZs9wKij808j4b4ZLbptP\r\n7JBvyVQz5KX++x0qJTxbnya2e6GTi5f/wSoUVmnemJcziIHIzIwwmx16MXKb\r\nBlvYN8vOSPK/KPQDyZfzpgTOLiMrTCDFtiis6Wbcnl7XAXaAjGEuR5UpQt9w\r\nPFKWLHSAiOc4JLIES/ttZu8EjiVSHCFIitg7DYfakJpOODseiaF8tIfDAne2\r\nKCTPKzH9Khkwhj0DiqY1FdyZeMtDtgl9VaEMe34o8Ee6etW5UWbiKK+k9HL4\r\nMphfE4RK9pr12lzHiRF1off0VdUyajxoDJkgDuzD7EU2KIx3k/sgGLGir4vZ\r\nYzlYU/ekw6NQSy4jO4CXhtyZR++n5Qz22UAvdTDF3N4nuce0jn0WS/MiYGs4\r\nODz5FhbbLTWpAS1w/+gdc++pREp3EFLkPVvbA787nEHlRIAqnBU8NerHaJ7F\r\na8jcc6IUne7dlHK+7AUbdqKl7zcPK6KU8u8=\r\n=G5q/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.0.0_1646242402614_0.971314747534769", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d2b7dfcae0e606f2e97101ab9036dd1d69720cda94e57b265c5abb90d7778874"}, "20.2.2": {"name": "@types/yargs-parser", "version": "20.2.2", "license": "MIT", "_id": "@types/yargs-parser@20.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "c3924f56e32ae39ce713a1d5dec5872fedf85f6d", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.2.tgz", "fileCount": 4, "integrity": "sha512-sUWMriymrSqTvxCmCkf+7k392TNDcMJBHI1/rysWJxKnWAan/Zk4gZ/GEieSRo4EqIEPpbGU3Sd/0KTRoIA3pA==", "signatures": [{"sig": "MEUCIBR3uI/4rPlsY1VMuHrdSbHv8OuWIaS1Pudy7eX2dnsMAiEA/H2ZJXi8tHHsUYOIBoD09h/CmmU0ckByWP26BQtijc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH6pnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoN5w//aoFxlk56gefx4h52/Q8XLbXcmyfvLPMtLdAKnx9l7T6LIssf\r\now1xKW5/mhvlebOdJTYvWltZk2kL5aGw/GCBbqNk2Aw4Nr5BAAkIIfCST12S\r\nv2Hqz2n2QAQgX+mImGfOIXRwHo5IE2Q13OJRjwyB0sglIRUFWdfCi8qH4IX3\r\nABX60NRKU/EtzunC+ejJO9qzhJTOlhvLGtOhPRqOUzZ2/JkTKkwYWAq/jq2v\r\n84W1DIRZu/lpYWagWx/wYW29EOY1XWA473+wUZdTMF87MJhTnjToK94HsJva\r\ngTN0ohDQC9VfuzhU3CS1wd5VzUAFls44E7I+CS9wpHUiHCQr7Ajkj1o/sYZ1\r\nVzoXHRQe2TKBWcuf2UDjDH1dpa/6X6Zq+zXY5YCTFxnocEE0vZeob3jH4x3y\r\nmFvqWCzHDRE0EGjiFkDhGy51DYfx0aa8Kd4FLrJVqO4q3IRyhEPj7SjmTAtU\r\nAs0K/HFWMnR8YFSdAluO0l3Z9yVF6EIBQUmWx/KprA6McNhE1Vb+1IAsfY70\r\n7lL9rg+OUVojOQeQzXNy4nSUvMM6lwaf6mVmL2+v8AfFTuHRSLBl881yq7bj\r\nsjwSjAcLky+FEk30xxtx5h42MK/XxbKoecgt/xGd1iQQJ3PjJkYOyth0TlEt\r\nz+bXkIgUHsLv2skiXS/YWZL78aGvpD3WqsM=\r\n=HNlV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.2_1646242406961_0.12611607347886267", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0f7204b5861cc3ea0116579ae58fdb089a577d80667cbb1e3f5e2f96c43ff730"}, "21.0.1": {"name": "@types/yargs-parser", "version": "21.0.1", "license": "MIT", "_id": "@types/yargs-parser@21.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "07773d7160494d56aa882d7531aac7319ea67c3b", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.1.tgz", "fileCount": 5, "integrity": "sha512-axdPBuLuEJt0c4yI5OZssC19K2Mq1uKdrfZBzuxLvaztgqUtFYZUNw7lETExPYJR9jdEoIg4mb7RQKRQzOkeGQ==", "signatures": [{"sig": "MEQCICPfgngkKuQIuCx7GTMMjltgEJ0HDxopnP5+1O0GfOZBAiBY2HAQhd6nvXj9DaVmI6S1qQ66ckcsuYnFk29s314gEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8928}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.0.1_1695620289692_0.7017122105338169", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dfcbf5bb47d65470b02e30dcc79515397c4f808bf25a230e0646d699cf30a517"}, "20.2.3": {"name": "@types/yargs-parser", "version": "20.2.3", "license": "MIT", "_id": "@types/yargs-parser@20.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "696bbf626a826864ebd94c42f57d133a32d5ba0d", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.3.tgz", "fileCount": 5, "integrity": "sha512-PXVDzW/BeYFd2ePf0H1OgN1SRKupQ7tfOVebkJ0MhB5cJrL5mGwQs+d8+TESjgujbPGctyMJwqcIvaW7aogWZg==", "signatures": [{"sig": "MEUCIQCZoX4TN/PqL/CfqIWNtf9vm+G/ESorzLsNgu/Weit5ZgIgJ4GM2WckY5fpNK6k8rG3xiXygUebcaunKPLUiwL1BUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8707}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.3_1695620295319_0.5190851987130494", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fc3968da5de6553967f16506e40c6d1c139ebea20dada8f7a836873d21577f0f"}, "11.0.5": {"name": "@types/yargs-parser", "version": "11.0.5", "license": "MIT", "_id": "@types/yargs-parser@11.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "cf76a83282fdc6e86e7dbbd2aa17942874fbaf18", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.5.tgz", "fileCount": 5, "integrity": "sha512-8bGFQp1KzNyhgOOtfYhMDmT75lz1hrz7SmuJZ9I62szskoTdAgJBk44Vu6cOoEokNomJioONy/q4LsO4DInohg==", "signatures": [{"sig": "MEUCIAC3rDcuVm37OnfyZlwkndPmKj6ngsUZvIW+ArSfCP9zAiEAimR4IJZJ5b48ojoMUTgGSOOejR5LVzsOTO6wDjzx+Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6909}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.5_1695620297466_0.8326823756436132", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7cb000ff8446f4731f75474d1a224e76b07f11540e22310e9da63e42f4f12c87"}, "21.0.2": {"name": "@types/yargs-parser", "version": "21.0.2", "license": "MIT", "_id": "@types/yargs-parser@21.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "7bd04c5da378496ef1695a1008bf8f71847a8b8b", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.2.tgz", "fileCount": 5, "integrity": "sha512-5qcvofLPbfjmBfKaLfj/+f+Sbd6pN4zl7w7VSVI5uz7m9QZTuB2aZAa2uo1wHFBNN2x6g/SoTkXmd8mQnQF2Cw==", "signatures": [{"sig": "MEYCIQCkIQbL31/tuxUrrj+mB71ZN/tDz+MGckZIlMwlWwBYQwIhAL09nEQLz2mU6BTJP8KXANbuVTl+Oq2pJ79fZoNR6ZDR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8649}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.0.2_1697657089203_0.3729825123093933", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5b6a90ece27d3cd001e4b807ff40bc71c7d73fd2830f66b9d27be3baf0a7dfe6"}, "20.2.4": {"name": "@types/yargs-parser", "version": "20.2.4", "license": "MIT", "_id": "@types/yargs-parser@20.2.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "63fe9697dba064a6e99c05f97c1aaecf5d3e2e7d", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.4.tgz", "fileCount": 5, "integrity": "sha512-XiMNdWPwW270PixfVRUd5cEI/0pu5TsmJtHcHZC6wMEAWFJXfOYd0Pj4P///9EnrGyyMmyaaoagEk67fNFQtbg==", "signatures": [{"sig": "MEUCIQCicGTQwYv43u8kMG0mliYKVatwDWOu5VoBdWxYpec3jQIgDpocdmqaRVI3SvRv+brEEt7Xn1yeBcVAHeBG9euCKq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8428}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.4_1697657102055_0.19377193801402992", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5a4c75a5f78d8a3753c56ba8fc0d434afcf0907bffc43772ff313467c4676fab"}, "11.0.6": {"name": "@types/yargs-parser", "version": "11.0.6", "license": "MIT", "_id": "@types/yargs-parser@11.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "632b84c79789884315b0529882663b1718c46c7b", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.6.tgz", "fileCount": 5, "integrity": "sha512-JdH5uRfuFqEgqIDu30lSEcj0nqcCzFFoLq8j/tNi5LPt2rrpHfw643fEd8+lRC6aYnWXzi0ljyoH/SOCxZzg0w==", "signatures": [{"sig": "MEQCIHAzvqGxljZA3VBuqQ6DOyjLrTesPs9nayXtrNPNGJllAiB9AXBCeuy3mafNl/WBpgYjCF2G9oamqQf0vcYUFGrc/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6375}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.6_1697657104375_0.03686866804886635", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7f02679fce36574289f2a8bf188ca453268d3e6069f36843a68ed7d18f388397"}, "21.0.3": {"name": "@types/yargs-parser", "version": "21.0.3", "license": "MIT", "_id": "@types/yargs-parser@21.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "815e30b786d2e8f0dcd85fd5bcf5e1a04d008f15", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "fileCount": 5, "integrity": "sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==", "signatures": [{"sig": "MEUCIQDmt4zZGqX+f9EUHxytww9Ey57AOMYIDVE27BgxYlsBEwIgHPjJJE3+b3+ZOErWjlgqRiva15H98YsG3cYhn+SaxGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8649}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.0.3_1699393024900_0.013638488282131656", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c8ba48d493937115b81658ba71ff9ae3452d2f1b86a9fea357703f8745fa34d7"}, "20.2.5": {"name": "@types/yargs-parser", "version": "20.2.5", "license": "MIT", "_id": "@types/yargs-parser@20.2.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "4a759fb279614f630ce18777eaedf560293fbf7f", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.5.tgz", "fileCount": 5, "integrity": "sha512-pAJfNtUhU/xDwMKb3rR8MnEUSqHIfsmo3vw09baYY0rEp6AzM6Mb5ZzTuZbe/YXiYL04E6coxe+3qhVOVG98dA==", "signatures": [{"sig": "MEYCIQDwX6U3dDxgeBN0u1WzgLiBg1+s38RUMR8ZMKuydzJaMgIhAMXSMoglCUpoEO6XtF+I1g3bspcEUI0ZBI0xFFgnQZHv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8428}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.5_1699393082921_0.010271879638461545", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f008846a53ebdef2356fb25f75b79d026e5389801b8b7197cd7e5d9e3a440720"}, "11.0.7": {"name": "@types/yargs-parser", "version": "11.0.7", "license": "MIT", "_id": "@types/yargs-parser@11.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "dist": {"shasum": "fcceee7d4c96790f2d0d4cf27483df44fb3b82f7", "tarball": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-11.0.7.tgz", "fileCount": 5, "integrity": "sha512-APQyPwgjwrb+kCO+sKYFHYBIMX1Cs+UQn5yKcNHpBhoVIL7hZLuOAXpB8iJuQUM3yoafsTxa717w2A2MDI6uwg==", "signatures": [{"sig": "MEUCIBqH6/OMuy0C1QWM2REltlkO9fohl8kU6pZsstrHuGU+AiEA2T/Hv+6lHHjmwAPDSDD6VRWoa5Yv4yN3hU9Z2pn0nBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6375}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.7_1699393085946_0.9753105330530063", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c08a47166bd1fb8fde168c3d66ea79ee766f583eff8edff3dee9b73029a04fbc"}}, "time": {"created": "2018-10-11T22:56:04.152Z", "modified": "2025-02-23T08:10:04.396Z", "11.0.0": "2018-10-11T22:56:04.439Z", "11.0.1": "2018-12-20T00:22:18.510Z", "11.0.2": "2018-12-25T05:08:12.786Z", "13.0.0": "2019-04-08T01:52:30.506Z", "11.0.3": "2019-04-08T01:52:50.425Z", "13.1.0": "2019-09-05T18:23:15.624Z", "15.0.0": "2020-01-13T21:01:32.611Z", "20.2.0": "2020-12-23T19:07:50.383Z", "20.2.1": "2021-07-02T16:35:50.253Z", "11.0.4": "2021-07-02T16:35:59.235Z", "21.0.0": "2022-03-02T17:33:22.966Z", "20.2.2": "2022-03-02T17:33:27.109Z", "21.0.1": "2023-09-25T05:38:09.871Z", "20.2.3": "2023-09-25T05:38:15.533Z", "11.0.5": "2023-09-25T05:38:17.596Z", "21.0.2": "2023-10-18T19:24:49.426Z", "20.2.4": "2023-10-18T19:25:02.402Z", "11.0.6": "2023-10-18T19:25:04.596Z", "21.0.3": "2023-11-07T21:37:05.125Z", "20.2.5": "2023-11-07T21:38:03.085Z", "11.0.7": "2023-11-07T21:38:06.167Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs-parser"}, "description": "TypeScript definitions for yargs-parser", "contributors": [{"url": "https://github.com/milesj", "name": "<PERSON>", "githubUsername": "milesj"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}