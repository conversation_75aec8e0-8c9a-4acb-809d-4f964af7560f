{"_id": "pathe", "_rev": "30-7aab69202d92f9cc65c771d8903de757", "name": "pathe", "dist-tags": {"latest": "2.0.3"}, "versions": {"0.0.0": {"name": "pathe", "version": "0.0.0", "license": "MIT", "_id": "pathe@0.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "1f1f9e19e101787f7de0222fe9801743e6c36825", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-3vq1I6O4OkfxSHRtegFytppmI3rlyjhrAnn9EdRAo1iBSJYxeCSmezoklodKU0dlY9zFxgTm9NGEzTzn19byMg==", "signatures": [{"sig": "MEUCIQC2a12KkwOz5Xh6s2p/KH/plMQchFhOGc0IGiT1CaAJyAIgVGw5neOy/YzwxdPuPg323K0izs3gY/ELEWU4UT0KX5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.14", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.0.0_1632329665748_0.8175875749507184", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "pathe", "version": "0.0.1", "license": "MIT", "_id": "pathe@0.0.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "957ddbe0e0e2dfeecdd6cd13e3bafc6c6adcb026", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.0.1.tgz", "fileCount": 7, "integrity": "sha512-YOiqhIIVQ1/XfNrOPeoVM0vnvEnBwN8nanEIKLDNI/Vc50affngUpAP5QLpo0d3kU48puOpQEvNL6Mf6tkSyOw==", "signatures": [{"sig": "MEYCIQCFwP2hYSO/Jcfp2XneqUU77pvtipH2ynRw8bKZOHDoZAIhALJVquM9Aw0cIaPa2HBboZjFJHpiir5o3AWW2NrrVQp6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8942}, "main": "./dist/index.cjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "490942da88bbc4dcccb8fec728855a6fff9b1e97", "scripts": {"lint": "eslint --ext .ts .", "test": "yarn lint", "build": "sir<PERSON> build", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Normalized paths for Node.js", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"siroc": "latest", "eslint": "latest", "typescript": "latest", "@types/node": "^16.9.6", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.0.1_1632334832157_0.01990023069365976", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "pathe", "version": "0.0.2", "license": "MIT", "_id": "pathe@0.0.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "d690780e578a8127e1d65828387609c153afc309", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.0.2.tgz", "fileCount": 7, "integrity": "sha512-mmK20YtPb4yXHlaPuOD/uPIpRu7iIK45GA/GiRSlNpIdfWDG5aEQmFT1HHtBmJB+t/6DvFOtOsEipsPA8Bx2cw==", "signatures": [{"sig": "MEYCIQDqc82r8MEoLkf4zt2pkIv9o28ZdfPKQ1NyYqvEoEXBWQIhAI0IdSwjLL22QvvNXuxMwACNSTIdhxl4w1zxE/JjNTmm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9923}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "784f9b7015e2c485115118dfcf67546a65803798", "scripts": {"lint": "eslint --ext .ts .", "test": "yarn lint", "build": "sir<PERSON> build", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Normalized paths for Node.js", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"siroc": "latest", "eslint": "latest", "typescript": "latest", "@types/node": "^16.9.6", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.0.2_1632336391242_0.9977523190521054", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "pathe", "version": "0.0.3", "license": "MIT", "_id": "pathe@0.0.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "6d1989151d596312f4915a3e420a6f7bb922f915", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.0.3.tgz", "fileCount": 7, "integrity": "sha512-SylxypN+8yYjDfBjKEOg7+ZVBd68JQrDCGX6HMykrTwQgEv7ogwfmnQN5UrSvqZjGfN8PlBgw477w4rpOaVY7g==", "signatures": [{"sig": "MEQCIEpx5PcVgD9U341ouVcKff2OaOqf+OMTbFgtTSv2Y1neAiBEyoWmAx+t3I/ZZDMJHZemgpbXZtuy7jpqUZ8BJL5gwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10113}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "5f43b04d22ce79b6f07da9373f447f6bdaa153c4", "scripts": {"lint": "eslint --ext .ts .", "test": "yarn lint", "build": "sir<PERSON> build", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Normalized paths for Node.js", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"siroc": "latest", "eslint": "latest", "typescript": "latest", "@types/node": "^16.9.6", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.0.3_1632485679687_0.14460034950461909", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "pathe", "version": "0.1.0", "license": "MIT", "_id": "pathe@0.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "1bdbc17943dba1050b41f90ad8408e11f71ffcc3", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-kCE63iJEFi0MHgw3AsdEyitvK0pb1NIKQpknz9mb2/weUr+8HWkNJs4+biXjSnoIjkqaHk5xReZavtufoTJOXQ==", "signatures": [{"sig": "MEUCIQDR8s2m8tp+yL1P8RP60P3kYV1SGmIQvsLEECvNM1HS3gIgLPC3XydjoK7wVsmJCmvCBvqoCPL+NvMPjNvS28qCFZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10193}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "b90fdafda9c0d96f1238c4e72d80821e2a1bc3ab", "scripts": {"lint": "eslint --ext .ts .", "test": "yarn lint", "build": "sir<PERSON> build", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Normalized paths for Node.js", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"siroc": "latest", "eslint": "latest", "typescript": "latest", "@types/node": "^16.9.6", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.1.0_1632485690927_0.7294697970367614", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "pathe", "version": "0.2.0", "license": "MIT", "_id": "pathe@0.2.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "30fd7bbe0a0d91f0e60bae621f5d19e9e225c339", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-sTitTPYnn23esFR3RlqYBWn4c45WGeLcsKzQiUpXJAyfcWkolvlYpV8FLo7JishK946oQwMFUCHXQ9AjGPKExw==", "signatures": [{"sig": "MEUCIQCIK+aUH6RLM0C8NTDOrNAy2+vTZH9D/oPOwCD09A5sFwIgFdwFbeflkdsbTikEbfaTzLcZXL+HVo7/PoYQPg1B4Ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh22XoCRA9TVsSAnZWagAAANQP+wQxH2m5jwTam3gzm2XL\nGbQgF6c0ecF3fTECKlWLtuLfTRutwhnppuJ186IDvzA05m0pLOAkjeN3GSWk\ngd4kpowPNFqmJTlniLkqxBpNcsIQS45sb250JURiAJNB2trrf3OeEA+Nu2u/\ndBn5gjjAOgxjpfg2HVhs21B8wkGM7AfOl62RPLTGbpNEKAzgnXNPcBLjiXwt\nhxZmkA/0Bul7lnLZpd1dJHSetlQRcKWJ+I35UUMLW/5Tm1xsNQGBOSKG3AnV\nOde+AySoFQtWKtvWo2RIYSw6VPOB4aGQ0URI0LBeWWhwqSvN07QPpR1vhUAG\nlZERe40Je8R64nm9y0Zmo2868660L8cMJsDxzHG1jNUPtQx/3yAOxF+YgKQy\nPvSLBuu19z9OcrIeZyrCBvNgI5sePcZtBa1GvCdkRybzEV7Nt1QUes5Ep+ri\nDA+yJRJp/+azCnSMDTFexjAHAdZ7Ri7HEPFgnTRGeS3GE5krIaONozWkF1Hc\nXe0qavL+27iXgtyQu08BzNSy1oCnk1a8JhFEpci1KRLapGCAIcw8EyGOt0WZ\nYsNHjMAsbVijJKgSifufufsdQhM/To2mjyhCBu62gg1k3S2dZeFOJMHMb/oH\nj8HhV70dKgGQWVOg6nc0yquyw4SM+RxNlPCnXY7RSZvKQRBVSxpsibzPPv3I\n95qG\r\n=Rzyv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "02b9c7532c84a6b2c6f4c0e31c4dcaef79391a70", "scripts": {"lint": "eslint --ext .ts .", "test": "nyc mocha -r jiti/register test/*", "build": "sir<PERSON> build", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Normalized paths for Node.js", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.3.4", "jiti": "^1.12.3", "mocha": "^9.1.1", "siroc": "latest", "eslint": "latest", "typescript": "^4.4.3", "@types/chai": "^4.2.22", "@types/node": "^16.9.6", "@types/mocha": "^9.0.0", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.2.0_1632744997902_0.5690464111914704", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "pathe", "version": "0.3.0", "license": "MIT", "_id": "pathe@0.3.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd95bc16208263fa6dc1c78c07b3907a528de6eb", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.0.tgz", "fileCount": 6, "integrity": "sha512-3vUjp552BJzCw9vqKsO5sttHkbYqqsZtH0x1PNtItgqx8BXEXzoY1SYRKcL6BTyVh4lGJGLj0tM42elUDMvcYA==", "signatures": [{"sig": "MEYCIQDtrhwzSbFe2kHDN7ggfyx4s2KEwttF6GlI3hgH5Bb+wAIhAIj+EDAIGYNPlqDLWRq1VW4+FnoQWHHvsHEta70sXufG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidBH3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRlA/+PqoEJA/sz0ncNVnHXgW0zDFZUz/oPh9jxP5heYyDeDhmJMO7\r\nZQOLzgiz7PjnZ730xP6xZby9//mBd21VNm7dpeLCAWU6JQig6UgPuuW0+G8d\r\nYn9kzJVYU5d0FzTN9KwFkCMmCjl7xag1HZn3QgKlTtjodihJ9Jkze6h1jiPj\r\nUElo9vQvueiaGjSKnrcbA+jO2K0Mh28HPtdhg6X0tHfUjWXHnWa8RLAeBNri\r\nNqry1Yei0Ipby4cMjbQSDb1hOH3UrpFeQeRhyU/xrZwVTSmc5zTOSqHx3LXY\r\nocaiOAGcqkkBu5j5hd5/6rAYFcOHVXMu+N1H4zyOmsF0s4EZAxr6cfSRM4LZ\r\nfWRQVSGCPp+XkjvnpCSNrIULDiXcawFNBMuFNGEtN9zTRFgUWJnQs0vb8YqB\r\n88QXZLIknhicNafqqmAeSnE2/PzOGQBTfDY+AzVRXX0WrX0Foo4vfEajnQJV\r\nlRoOY24LPxTa80P68nS89TmIyAYHMHRdGiw4J8wImjDP5RBbNDpge+O0HZgy\r\nCmG/XkSBZhnZtd+3ocCfmdoDelaGJhwesLHR3C9/gzjz+fg7NHJcnc94n2JU\r\n1tdlNAEmlacZhfgol45X7D9wN93NAEc/RZol1RP1btq2DOi7CMFqbnTOt55U\r\nBaHF7GIg7noelepXrNDVUqL9NXWBRXEGAP4=\r\n=mAuu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.0.tgz", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/tmp/687e8bafae3367a808dfd9fad55f8769/pathe-0.3.0.tgz", "_integrity": "sha512-3vUjp552BJzCw9vqKsO5sttHkbYqqsZtH0x1PNtItgqx8BXEXzoY1SYRKcL6BTyVh4lGJGLj0tM42elUDMvcYA==", "repository": "unjs/pathe", "_npmVersion": "8.3.1", "description": "Universal path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.0.0", "devDependencies": {"c8": "^7.11.2", "jiti": "^1.13.0", "eslint": "latest", "vitest": "^0.10.4", "unbuild": "^0.7.4", "typescript": "^4.6.4", "@types/node": "^16.11.33", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.0_1651773943414_0.8435953245045944", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "pathe", "version": "0.3.1", "license": "MIT", "_id": "pathe@0.3.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "e0e47bc0e8a1775fd001e2fdbf67d7f649a4a4f4", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.1.tgz", "fileCount": 6, "integrity": "sha512-cKMVe/pikPpreoODbjoliSk0cIT66JjEOWN30akc9WTo8kUjxFPYqmXvMBVPMSAJGJlx6v/nYzqSSHkp1cfezQ==", "signatures": [{"sig": "MEQCIF3t0npNw67XMBlaH4qOJdxt/eCXnHnbiXYKpZkTcoUwAiAlwACmbFcnHMv5VcV3HSAsRcq9ijIXBVi4LT3vV2SAkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuZcIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIgA/9EbH3Xh6llGetcYP9nfsSsro4pciisIzEOjyj8xjjBcf8xU7W\r\niHk3O4SpbwKrUW0MSaYPoWD7n/M0lahE1Q8G3oBJfGhBqGm3u8zI35o7QTQq\r\nJgzVv1kZfpKu1NFXgsaRRlNW2HyApqsmyQXGzh5xtET3vP+xh7cSsTiHeYmo\r\nkm/mI4vm9KlHcLdR5LTHCA5hjzy2/W08Oovd22YKmYPmc7s+GBVJKT0Ieo0m\r\nesybSonpqwk+JCMcpZqqEThddVNCzWfkSRYMnjztB75IZR0xpDSbuxG3pNKI\r\nuzeE8JP5NQbzgYOQn5dFjMYUdQVPh0asrxzhOYbz009Ms71UKu9UogdgdrnQ\r\n8OFTER3Z2dSklthsAslLXcwrW1hvmd4M633yc2s+WtPOqjBYHjpQ5RztUuT9\r\nLUseKJj4agqh4rgavmQhuqJJ94of2yLGKYj64NsBJbnfyoKAKv2uX/u1K/Mw\r\nvpOvwHxnHeBh3eruEARY7TCcKrJ/RtjhXQ0Cn5wI1vPYWBBpSwp/IahuQ19M\r\nbKyaINe4+96uoSeN6jpBt+NGZTc8W2V7i2V7RS+97hSJu73EbVdMxGAZTDv9\r\n8YL6W0zrBClvbApWekeIjEe0R7lfsxtvlJ/YIbGLJ4m0Mc9KOttDgkz+/xv6\r\ndabvzPMutKPNnPVdTCHNujtI2DTxsHCLY9k=\r\n=rNI5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.1.tgz", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/tmp/3a7b40052f118f9a67819f6fcbd2cbb6/pathe-0.3.1.tgz", "_integrity": "sha512-cKMVe/pikPpreoODbjoliSk0cIT66JjEOWN30akc9WTo8kUjxFPYqmXvMBVPMSAJGJlx6v/nYzqSSHkp1cfezQ==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Universal path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.3.0", "devDependencies": {"c8": "^7.11.3", "jiti": "^1.13.0", "eslint": "latest", "vitest": "^0.15.1", "unbuild": "^0.7.4", "typescript": "^4.7.4", "@types/node": "^16.11.41", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.1_1656329992378_0.005073136657846478", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "pathe", "version": "0.3.2", "license": "MIT", "_id": "pathe@0.3.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "016345ed643027404d7a9ed8d1454ad997a1483a", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.2.tgz", "fileCount": 6, "integrity": "sha512-qhnmX0TOqlCvdWWTkoM83wh5J8fZ2yhbDEc9MlsnAEtEc+JCwxUKEwmd6pkY9hRe6JR1Uecbc14VcAKX2yFSTA==", "signatures": [{"sig": "MEQCIAaLhhOU9jhzON+2HV1dYyjwIEpKX6YU0oBO9Whik17jAiBV9OqXfcRh8zDg/vwknglUIqmu3fN/KWWD95p2hCe9JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivNcFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq91hAAnyYNfJZgAIspc/ex/j+veEIzBdi5SyeIvoosOE5G4vHycQiI\r\nhLhLWVV90BNEVVjfwy7aRrBl+Dj3RwTDF2Vy9ANcYuOjU9QtCOSqm8914VdD\r\n7fBj6yuW8FsMZz1rWL91WIRFs6G0qV2cvruaTUkxL67enJv2KtQA8vAOKp+A\r\nMTMP/vnRp7HxZXRpLTwoYr3k/XgNVIQgyy9PfDmazq1KaRemxwChbPZ94d5k\r\n6V4CPnOpELmj24likCIMFrDBzKU4ygbZB03vPNx+NADxsg3b2/0uGhi7MRai\r\n8N6i6+o6chCVMLdjt+tSaA57bui+qLeYVB2y4fbza+IMoexhuRZUxBFFVfzz\r\nvwiM3dI9RcA9H5dku91SmIKNO+Yj5y98621xKHh/yvThSJtlpm/6dfiB4jhw\r\n19gRhUnvnZAcFKUs3aL6K5bbjAlNtvHNvFlbsnODeBovy0BRijpdLXUe6Gvi\r\nwcrnPG5OL5FIIU3zCyylarjdm3T0gl5JvvYca0O2kqjTUrXoA5QqNbtG0DNI\r\n+7rEO5hjWKtcM7c9MKwLbfddeaqskWvlgXtjryGL5zWE4YOug63RrwTCJoCn\r\nX0awUMBF/HZ2aiNm/PYZdszk2CZFdDCxxvPPQBV7jYwiwwGwDeMhgXx9eI5S\r\nTnWuqqBEsi2IRlmdkiPq6RvBJCI2fVqv/h4=\r\n=YWZq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.2.tgz", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/tmp/55b230b7d3a2ab92024fa0c3f8992546/pathe-0.3.2.tgz", "_integrity": "sha512-qhnmX0TOqlCvdWWTkoM83wh5J8fZ2yhbDEc9MlsnAEtEc+JCwxUKEwmd6pkY9hRe6JR1Uecbc14VcAKX2yFSTA==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Universal path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.4.0", "devDependencies": {"c8": "^7.11.3", "jiti": "^1.14.0", "eslint": "latest", "vitest": "^0.16.0", "unbuild": "^0.7.4", "typescript": "^4.7.4", "@types/node": "^16.11.42", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.2_1656542981078_0.27900288918168337", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "pathe", "version": "0.3.3", "license": "MIT", "_id": "pathe@0.3.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "8d6d70a25d4db6024ed4d59e59c1bf80fcf18753", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.3.tgz", "fileCount": 6, "integrity": "sha512-x3nrPvG0HDSDzUiJ0WqtzhN4MD+h5B+dFJ3/qyxVuARlr4Y3aJv8gri2cZzp9Z8sGs2a+aG9gNbKngh3gme57A==", "signatures": [{"sig": "MEQCIQCoDd3c+fhRYSVftw8WHpZiblzNmuNxKyjp5vCFhwhPogIfDH4GqQmoSG6zEq5ut6PimJO49Hv5FANO0Fvu/kAxTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi57wkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorxQ//dEMQeRKwSIZkc4E1UvGW1UGZ++w/qwle144x1DzZOsUlLXap\r\n1lEGIyeF/LgdV3ZsQvzmzZGL9Ef9NH1RVRWQdITlH+U5tHJ/X9FVUuF/4TuP\r\n3USvDRzTmHJrefNgahYpULgda+jgP1PvjF2MZUvwEVHwUfbWRZSfzFWYv0tm\r\n7ijiiKcQHaCL92Kqbc+w4C0Rbh9Tb+RDPSHpuP5tVWWWZD9VvUZZPwSoM4u3\r\nazKqNvZBlwpjqLW33Jjqtl4Zs6rUM1btlh+upCXUbirhYNddMIuxVomfMiku\r\nx0Z6+t088s/Xu27OFAIh6JQDizv2CXLJEcLji8KBcGqPv1GYtPmLTIzunwzy\r\nKo2IQnCjVRhAq2GhAemq1Zyjvm19LbwTXYOvkBQ2ed59cvAY0sHnkGdusCHk\r\n/6bEgTe270cZJ0sAxObEm9SzuJTMZELWRTjf2g5b8l6riJdyQQ0gOwQerqSY\r\n0AaktiGIo72Y5qAbC3Ap/HBKLHiObzTRQg+s+Pu9l6wo4Xkr4qx6rCzUbGzo\r\nA5IdFcZ+Yu2+sZm2oB1fVzep9sC6iMgQAkLMggyYoQ7UlIQgGE+fa1EAybQS\r\nAo7leioFv8lNyx1odabWHz6lRvNY+tTGCdPHalKkcldJlwUOoTFpZy1pzrob\r\nqdRWtLPvKBZwR9KNPXWBEbPs01VcRcFs7hY=\r\n=IDVa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.3.tgz", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/065c831835f8ca1ddd7f15a4b93000e0/pathe-0.3.3.tgz", "_integrity": "sha512-x3nrPvG0HDSDzUiJ0WqtzhN4MD+h5B+dFJ3/qyxVuARlr4Y3aJv8gri2cZzp9Z8sGs2a+aG9gNbKngh3gme57A==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Universal path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.8.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.14.0", "eslint": "latest", "vitest": "^0.20.2", "unbuild": "^0.7.6", "typescript": "^4.7.4", "@types/node": "^16.11.47", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.3_1659354147870_0.700822039690685", "host": "s3://npm-registry-packages"}}, "0.3.4": {"name": "pathe", "version": "0.3.4", "license": "MIT", "_id": "pathe@0.3.4", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "35bfb1e92373f98d9711cc74e7d6475a0654deae", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.4.tgz", "fileCount": 10, "integrity": "sha512-YWgqEdxf36R6vcsyj0A+yT/rDRPe0wui4J9gRR7T4whjU5Lx/jZOr75ckEgTNaLVQABAwsrlzHRpIKcCdXAQ5A==", "signatures": [{"sig": "MEQCICrmNUFJdONGdT1dL2twF32Tgip6farjOmkjf3ph5L1vAiBygxUbe/Ch3aVk/luJ/1ddld2Ai12rHO38FCz+CsPNVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi88o+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrIQ/8CPsyEcooFDJJoO9bJ17AstPR5IYe6uEGQMuBAPXaz9u7uhw8\r\nRftRZpjJjGtRaqnV9I4GG4iuXxn/kNlX9GpBgmZPE7QLC5GKRee2AcbwEFbp\r\nVOuriQ+HN2sGkokeXN6iQWgTib707uGluioXqzFN8o3HeYNN/YFu1CjXhpl3\r\nl8FN/tbxX29moOAn9ekIEin6YHP2CWDriBCAiAieJp7XqwSCNVcOAv0X0Pml\r\nK16w0AwCGH+RregAaxFClCTzL1OuxHlL+SLjA9+oN6s0zUJ/LiMtea1W6rg2\r\n4eHjLGZPT/PtjvKbfAH70r4v6GamwjCZOeSHgx/1qy2AlpxYZFSdWgUyYM3E\r\nIhoT81ZI2FwDKknt/V15WNOVjzz46wCVnApYIWqQMgXPAmPhasL9F/6pOntF\r\nar/LQ6KqolaL34dccWHeC8pSuSDkivv5jfCIN/ZhssCInn23qFF5WtAFUCQ4\r\nlUPNjIrqwLO6SotJML+YbR8L+o39ikEjqtiC3OQfIePc6Y+emRHTdXjENSi/\r\nLuEUmNi+aypCIiIA4WBN+7TqABHRWGYZgc2WJyQHkSsaDZ9Mi6BGIaQbdxOX\r\nEpgyKshMRBch4SvA5NgR3y75QfEeazwcnEm51j8I/do8XsQ3bxAotTs084/P\r\n9ZbRA4Em+zeBYScOm2kO/2qhBfK+39UDHfM=\r\n=pH2X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.4.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/f8e7261777bd72f8b38e10f8195c58f3/pathe-0.3.4.tgz", "_integrity": "sha512-YWgqEdxf36R6vcsyj0A+yT/rDRPe0wui4J9gRR7T4whjU5Lx/jZOr75ckEgTNaLVQABAwsrlzHRpIKcCdXAQ5A==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.14.0", "eslint": "latest", "vitest": "^0.21.1", "unbuild": "^0.8.4", "typescript": "^4.7.4", "@types/node": "^16.11.47", "standard-version": "latest", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.4_1660144190620_0.5619785976313405", "host": "s3://npm-registry-packages"}}, "0.3.5": {"name": "pathe", "version": "0.3.5", "license": "MIT", "_id": "pathe@0.3.5", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "87e5c1164ded1bebeb9dea5dab63563144062303", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.5.tgz", "fileCount": 10, "integrity": "sha512-grU/QeYP0ChuE5kjU2/k8VtAeODzbernHlue0gTa27+ayGIu3wqYBIPGfP9r5xSqgCgDd4nWrjKXEfxMillByg==", "signatures": [{"sig": "MEQCIEvBgCx8eaHIewqov/0x9f/UPcxFhcje2H3z5y3SVRmWAiAW3PzNINuIl7yAypnXi90zGAJOxr8CtSNe4wqxfX8s6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/gkRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxBA//ZSCmiKrazHCNVhYm7iXwYBGPAE238F/7oV+HkiHYjEX74zy+\r\ngXqMZ+PEgvf9TrMPWGuVL4gWYqZPlTevR71gJdoG/tGV14wsGyBhKso7piRu\r\nUaRLS6WavDoP8brBAmKjuVmR+0mT83jsb/PDxG4lEGrkAArJ8UFkjPE19yKn\r\n7c7+d1/V40Jpr0SX7LpbeAsl8uDe6RUw1XWSMRoXHN1/Bub93Y/gwsAeRPYC\r\nJnhHZ7M7CDTJKOPJoFUcLvMeGxJmakWCoUFllLhVR5cIQAwAkgqb6y7M+q+a\r\n1yzRpU6QdaYLQ5+JQfRWKklVXIo1ppED4DuK8k4sWUug05v6ak+dC/NjVWBE\r\nyctXDgpuRwFrTs9llNsERTUyD58idhCPiHvfSHda9RcdQUbHKrQ1BX0hUYpb\r\n/ezfdX9lkcgDiMJyeNmLFz6O5XH1KIM0ld7W5XbaSfkCkL0saKUoI0hkeabY\r\nIXUQiylXD9Hq69Kb7mEMj9DKl9k/e4sLrU06K/9G25kU6a84t+iEagIXvPc6\r\nmF26v+84TMHKEfa+w3s96yJfXGpbc9SrD2xqNOeI2zFlTMA0MOUlp6Q+3xFq\r\nBldrOfW74W+p//GA0YhEPi5jI+jmJtQ4jJQ65h6q9cvfTegVGQoa2tBKSaes\r\njZxapk/rjnkdJyHO1ZDNwMf+NDvGwNioSrI=\r\n=uNKc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.5.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/59583d6770728081aae1d540adfa0665/pathe-0.3.5.tgz", "_integrity": "sha512-grU/QeYP0ChuE5kjU2/k8VtAeODzbernHlue0gTa27+ayGIu3wqYBIPGfP9r5xSqgCgDd4nWrjKXEfxMillByg==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.3", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.14.0", "eslint": "latest", "vitest": "^0.22.0", "unbuild": "^0.8.8", "typescript": "^4.7.4", "@types/node": "^16.11.49", "standard-version": "latest", "@vitest/coverage-c8": "^0.22.0", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.5_1660815633407_0.45151160111702704", "host": "s3://npm-registry-packages"}}, "0.3.6": {"name": "pathe", "version": "0.3.6", "license": "MIT", "_id": "pathe@0.3.6", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "aa7b979647f604974244d29a0775814d8795a2c5", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.6.tgz", "fileCount": 12, "integrity": "sha512-oGkbljpfqFPhK8gFOXEYHfJQ2ZR0c7vgXEE65ySpXFlFUHubCNYnz5VCAAfnf0ns+2LAhNTrfcqqzzowWEuePg==", "signatures": [{"sig": "MEYCIQCyY0lWzxNnA8ImSndMPg+fO/vNkzWAy9d3xXr59T0+BgIhAMNq0kzXHkpl/MEFnNC1MxgS6jOF2ttyYnUt1aSNhdRB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFwF+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBsA/9HIw0TTEEGp7vhOJxQ03hyBF6Xxh2Q/+y111Bps8zUCI/C5kQ\r\nCWm0JsekruYEDvDFUG3y8is+U2/iHo2x+oWjDFegzdsZf53Yn0PcwZt2AXIT\r\nP3j8lhsRdiA1ylo+N0xUsrJXz4WvHP48jeepcJe4m9unpOOmbqTA5PE6Ele2\r\nc8p7zROa1BoaQg+CLyTZpCyd7FBEHyqo/YTjZRR2KU0jBCs7i6Bg+LtC5zYP\r\nxvPzlxvFYLHJdmjtLO1BFhL/1/GXRSVg6edDHOYMabddLNkGqqAR39VKiPny\r\n+RwqfQLvKjFyt0tqWtM7nXOZjnMB3bJHR87H95cEhnC6skK9ikp4PJOo8zVc\r\nZJDKnWVNI8tR65Ut6h4z6MIzJACBlU/wYWXlBo5DS7Wc0NomKvVdEawE/ZNd\r\nYAQuUbLabzArphsZhblErbZVDZT2oAl2Bw0KJ20rggg1xPcXJFmaGqF8HR7r\r\nukrFbyJcshYnU3/79TnALzLc0JCAt2MZJJ2NQjCikf2wVbsehH20Wz6xCVvI\r\neRSTaDzWbQVP2g/ynux9WoXM1y1626oHumyI3lVJtIm69pl/seOQgSr8+tq0\r\nyjeKDpPtxiEs9ZVnyW19gUURCKKnX8knOM3IBqBmPeC1AjlZVt2nXpT5bZEn\r\n5HM2LVEtNALKEb4hmf3Kkx8lYERQlrEGtr4=\r\n=Qa0C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.6.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/5529b77471c3ce281fbe18788bce511e/pathe-0.3.6.tgz", "_integrity": "sha512-oGkbljpfqFPhK8gFOXEYHfJQ2ZR0c7vgXEE65ySpXFlFUHubCNYnz5VCAAfnf0ns+2LAhNTrfcqqzzowWEuePg==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.11.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.14.0", "eslint": "latest", "vitest": "^0.23.1", "unbuild": "^0.8.10", "typescript": "^4.8.2", "@types/node": "^16.11.57", "standard-version": "latest", "@vitest/coverage-c8": "^0.23.1", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.6_1662452094084_0.4311455239629427", "host": "s3://npm-registry-packages"}}, "0.3.7": {"name": "pathe", "version": "0.3.7", "license": "MIT", "_id": "pathe@0.3.7", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "83860c096cb11d9614c17e0d70d91622931676ce", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.7.tgz", "fileCount": 12, "integrity": "sha512-yz7GK+kSsS27x727jtXpd5VT4dDfP/JDIQmaowfxyWCnFjOWtE1VIh7i6TzcSfzW0n4+bRQztj1VdKnITNq/MA==", "signatures": [{"sig": "MEUCIEwq5uaJRIQB07dpZU1S2fYpgcmytnLBVHoQXvzBiShwAiEAwIa+eEF5pZ1Cnhtp9/Em29bUAt0kruwjDRRUTCBCdjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFwYIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXJA//bmv1TkixAgYiQPF8oBR0CPqjnIX8ayVe8BgxQ7wKSFFj5S0k\r\nWLK74MsHKYKVAbpsVWAhWiSELltNoonhl1F7gvWslRpiytUhzc9zetDpKinu\r\nPsDoZioHL2EKVOXC39/HPEWYpSlAPnESKGjBs6+xSFtR7JtdupW0DnChT7C3\r\n0LHBesgmX7oqRxY5G4QKoJAN0jT+ji/ryCLPTCTUErcJ7smlNOKw/fjGaMlw\r\nEKQWkajv4Es26/V2ND4kdoFzqvXmhrafxTtQJrdupaPMUes7mqBBiVR3t8sP\r\nKqiqzAGxQdo1nkXfprNA9zVvQKlO+DDkJfwUs+6Xf51Jm3bDlaGqi/74mYrB\r\n2NaVwRxwf+3E3JWWZwUPkBcUXBEbjqts5wX1KyJ8ITxOWeHUKQdRr44Sga36\r\nlZ7ALsrWOy7cCqyFj6nleNs3vWthXLWZPQjtF/hLQSK7CANogbTntw2QDY0t\r\n1kiEz33fbkaHU+Ag1+aTNq2ClhxobJ5GriDOffsn4SWaJayRJOw9+eiD6nv/\r\nmr3lGNnGZ9HuR9LqZIXVjpeAST3DIgYsTiRS/0i+ISaNuEqp3VI4sQ5U8QYT\r\n15dNckeVvw9LrnLLBj6cm7ovO4afOxOW+iEpN+r7ysI0MofxXS+Foh75vgFl\r\nOwulGKk3t9A2mbBaiDx/wkl2+DoiQk1khw0=\r\n=CBZz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.7.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/a2637417a970c99b6192a411e8b09f70/pathe-0.3.7.tgz", "_integrity": "sha512-yz7GK+kSsS27x727jtXpd5VT4dDfP/JDIQmaowfxyWCnFjOWtE1VIh7i6TzcSfzW0n4+bRQztj1VdKnITNq/MA==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.11.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.14.0", "eslint": "latest", "vitest": "^0.23.1", "unbuild": "^0.8.10", "typescript": "^4.8.2", "@types/node": "^16.11.57", "standard-version": "latest", "@vitest/coverage-c8": "^0.23.1", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.7_1662453256370_0.13244104062519924", "host": "s3://npm-registry-packages"}}, "0.3.8": {"name": "pathe", "version": "0.3.8", "license": "MIT", "_id": "pathe@0.3.8", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "3584f03fda1981a6efe8bc24623d82cbb4219acb", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.8.tgz", "fileCount": 12, "integrity": "sha512-c71n61F1skhj/jzZe+fWE9XDoTYjWbUwIKVwFftZ5IOgiX44BVkTkD+/803YDgR50tqeO4eXWxLyVHBLWQAD1g==", "signatures": [{"sig": "MEUCIQDi2qGto5enoQ6gMw3U+aGAs8Z08WwkQutIZc0BLnPXtwIgTx72+t8yFsj4W134mZxwCjx7pmV6QmNPbUTAOULgXhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKDJ0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI0RAAkbLekjRa3v8JxFpsDRb2tImsJYE5IJukYcE3gTEn+MEEYA8Z\r\nlZATwg/910rTNcwY60eGRIv5rVQ4KG8pk44EfhblQ+ppsi7gLEjxfABaNUjD\r\nVp6ZgrXZn/zVq9qe+jSJIZuDhmoEz+G9xPQlB9FnnX1g+MIvAS2AZZmuZzO/\r\nM/DUa2B9XVdbwBGqx8sRCkaJw6M8D6dZRzqcknW1WLI51NdooregMXBs6kyB\r\nO4mOQQ3/ppdfnvgX+aoCrDaar9gEZGtIVboe6fFyTPvcGOmMf8+hhA/aK9cA\r\ntHDAyEEPYL7jhnSbi986TxGqqBwawNhcIVBqzS6LaN8WY9YaN/7FOHmXk6eh\r\n/Ut6UOnuqp7PFZc6mMmSYEcBtPRKkYQBTbItCTs1i2vfwItZp7TfPRgQpJgN\r\nScJ5tpdqNYE5JyeYcCJMExUy5XrgF4z4O8P8Wr+GI439ayr5yMXju5tnvE3S\r\npzmhLU4MIdYzlB+G19jXDg+WbSdK4oHVBmjcN/AJDjs7V34vyXaZkRrlPp3C\r\n4+dsDw7PnijgTLRSyK8RuaC8AHQIFwEB6O4ik5wXBlEwF9bqkSVJPgAusb5D\r\nfkdgxuUWNmR45zyyhpA2PKVr+qlu9damqIGGahySlW8Hfu9WZqbYuS51B1P6\r\nxzD/pIJLC25v4M5mmhN7JQagjPzCJiZYGc4=\r\n=yMEE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.8.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/53157f14fb1ee26b05093dbc66d078df/pathe-0.3.8.tgz", "_integrity": "sha512-c71n61F1skhj/jzZe+fWE9XDoTYjWbUwIKVwFftZ5IOgiX44BVkTkD+/803YDgR50tqeO4eXWxLyVHBLWQAD1g==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.12.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.15.0", "eslint": "latest", "vitest": "^0.23.4", "unbuild": "^0.8.11", "typescript": "^4.8.3", "@types/node": "^16.11.59", "standard-version": "latest", "@vitest/coverage-c8": "^0.23.4", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.8_1663578740564_0.4043847697357297", "host": "s3://npm-registry-packages"}}, "0.3.9": {"name": "pathe", "version": "0.3.9", "license": "MIT", "_id": "pathe@0.3.9", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "4baff768f37f03e3d9341502865fb93116f65191", "tarball": "https://registry.npmjs.org/pathe/-/pathe-0.3.9.tgz", "fileCount": 12, "integrity": "sha512-6Y6s0vT112P3jD8dGfuS6r+lpa0qqNrLyHPOwvXMnyNTQaYiwgau2DP3aNDsR13xqtGj7rrPo+jFUATpU6/s+g==", "signatures": [{"sig": "MEQCIAJDvUpnycOl5BrmbJcoymJQN3k6WmAD1LMn/ZrAort0AiAcErQSOrknEGZTBfWUwokIGAz/FLgz3t8XRHnhnG4/MA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP/h8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJmg//WojY/7MYcEmLCYm7EayjJ+uhYXmg0Pn18WH+ZWPW5Qmfdxk2\r\nDci9r8LDDdNI5sRaTdAFwa2DTi8cMFLXX6IT789JAaP9r/8Bgk9/U2/+NEOa\r\nselfUhZiz+b3gkCjGFopOhdtpoEhrgIfY7r+3+NJ04Se9aK22kEapCLapPHP\r\ncWS3+gGkDH4mLnTnAIDXiFqJAe/e3K4sfa4k8X8+RdXAF2xGLuJWcPFQIdsJ\r\nhXfCdXL1aeI/w1/7+lOshMYgov2bILndbQLxE21Uqmea+xlaYL/CekaI8cUK\r\n2vkxhQvtsi+bL62TAUqaiBQCb5/35t8o2+ngM5yPimT019lnmrKhVgGVXSoo\r\nTvjFu7ZH79ONZjHQPZiJWKIMwwpRMD/KsZ5XNPFZsQRQfmYWXVuSntQc4PGv\r\nuqv280WqD46RWACxAMtd0a4Z/PRwRcRIZFXdjlpsSWh8PSnHEbnMRzJh4aJd\r\nwuOZUp+9n7zVASXRRJkUMbjoZ0ZsygvSnUf8jMlW80mmYt0vOWy/wx6N60B9\r\nR0CFHtE9CrLqzBPt3CypIRJMJ0UrbXmeYJl8kD7+WEEdnpY48yPf9rXamx2F\r\nsy1q2W2Me/iw0gT+B1PpSjNh3xOm/02M39N2vNIM4bdxLmA8EqNf3vv50fd5\r\nRDayCSE54ozren+dHCpf52WVjDA2eGh2bM0=\r\n=G0VH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-0.3.9.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/bt/x_p40zk53316b437tbd0t1jw0000gn/T/63529e8d20beaf93b68cc85edc024d75/pathe-0.3.9.tgz", "_integrity": "sha512-6Y6s0vT112P3jD8dGfuS6r+lpa0qqNrLyHPOwvXMnyNTQaYiwgau2DP3aNDsR13xqtGj7rrPo+jFUATpU6/s+g==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.13.2", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.16.0", "eslint": "latest", "vitest": "^0.23.4", "unbuild": "^0.8.11", "typescript": "^4.8.4", "@types/node": "^16.11.64", "standard-version": "latest", "@vitest/coverage-c8": "^0.23.4", "@nuxtjs/eslint-config-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_0.3.9_1665136764328_0.5362446128638056", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "pathe", "version": "1.0.0", "license": "MIT", "_id": "pathe@1.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "135fc11464fc57c84ef93d5c5ed21247e24571df", "tarball": "https://registry.npmjs.org/pathe/-/pathe-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-nPdMG0Pd09HuSsr7QOKUXO2Jr9eqaDiZvDwdyIhNG5SHYujkQHYKDfGQkulBxvbDHz8oHLsTgKN86LSwYzSHAg==", "signatures": [{"sig": "MEUCIEBNumViaTMyZD4JUlELmSTBS0XDtSNg4DIkQ9NkmxfeAiEA2EU6kIjEK6uJ8IF/oF/NgJEa3hR9wHKl0aSezmmfPw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjckHtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorHhAAgmp8mHpR0EiFBmrShFnD09tRONOx80ms61lB6DPTMF9uyiXZ\r\nAQctgQlIJ1wFEY9RlslM3usXBde9xJqGaf5nh54qQJpA0dx6/Xst4JI+p8Cz\r\nrM0KPVDe+XYFZ+Q7cpBJt3/yFgInI/ZHk5bMMMWH9hnZ9icUSrVLRW8yW+eN\r\nG6ma4CtTPqX0RPDDrpk0dXSrDMxqKC6ZGi2D7kWbvW3TWfkK4Ez4q1W7kmK7\r\nLiONJpwmo9JSVM0+8g6CykfJ/+JrSsOM4Jb2X/eEzYW3lJGLcQLkepBjdkH8\r\nLrHrX+jcQNDQbHUezn4V+husjw2tl6TtPaq0NAeSfUef5cCqQiwD5seYQfFI\r\nq7oRqSQ3JCorm6BxdiwVIZZn9aIv/fCTnTZCGgvIZIEXpGs0OFyiRA2VZlrM\r\ngyB3lRS2uCRjn6gbJST+pvqTbc/YHrY8+65zQQqjCXJAbaSzpqDvux8n7H7w\r\nHEmBrc+qh3bhmu+6AABItPjEyPTdmJRCNdmcoeIQI53WdX16TfLs1sMXz50B\r\nUbxX7koSydXySHv8g2ipsBYdwXkFb4fQ5Eg0vM1rNRm5M3fGiuK20c9sQ8my\r\nYJiHcoxhoChOU6YpRSJnKwCxmVFEjna8SjVPgE3S/GsqE3xF0C+DhfQqKPEN\r\niY+fHuNy7SVv8A6IWYj9In3Bb89s3VBkpzI=\r\n=6kMO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "gitHead": "70bf7c98357deaf773f54345120d1e7b4d4fce00", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit", "prepublishOnly": "pnpm build"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.15.0", "devDependencies": {"jiti": "^1.16.0", "eslint": "latest", "vitest": "^0.25.1", "unbuild": "^0.9.4", "prettier": "^2.7.1", "typescript": "^4.8.4", "@types/node": "^18.11.9", "standard-version": "latest", "eslint-config-unjs": "^0.0.2", "@vitest/coverage-c8": "^0.25.1"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_1.0.0_1668432365565_0.5419740440891472", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "pathe", "version": "1.0.1", "license": "MIT", "_id": "pathe@1.0.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "3a0b91a25539e73331171ed309448df848a036ab", "tarball": "https://registry.npmjs.org/pathe/-/pathe-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-SxIsbJOlu6F+XhUbX9OqTaYiUtq5yr2oqsjSJp5ivh/M6beS19qdJcPgQiKjnxjN8X6wgKGPAGtXElotr4tVrA==", "signatures": [{"sig": "MEQCIDmj/gZr+rTge7VnsD4Mi747YPPJ44wgliCyFDuQXZPEAiAg8VI1yK5EizyA5j1WI4wxrg+u8prejF2HNwny+yHp9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyvtTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0SxAAgMZ1WF6AeEH4rPHr15807sv0CRkhF497+ebliLyAGvUx2Dvg\r\nCBGwmI9UlHCr3Ti0+4LCnWLvq+imSptPX3wntSKYNOiFTF8UzTMLelDtLXnt\r\n7lVhWqelmIf3p+wboa+djVtenabOyBm0ZbnO4J5Yf/d+a4c4g9IIN0avWngE\r\ndnDkmsJxi+J6QUsGWW1FkG/ueVb0gGdGDChC6t/6DwaLvIoK9jqPt2zgNDrQ\r\nYrNKaoOdCKhE5qAeVCruU1ohvX6lAuNlfSqegZ5S9Hje77My0/qTazaeOTXI\r\n9A1Y00GOpCI5Kk+kkh/ED0m+r7fAzvVdV6izKvukk3U3A0rIkxhu15HnoH5f\r\najB3kf69ym0sXrSbXAaMg5+rDcBv+jYD7iXL8AqjS3XPq+hI8M9SWC290bTf\r\nwPDBIS4ZF6KdoxPEspHUGMHdfL2iEE1dWkQaPtS0HR4zy5y0fpl9FZXmwRHK\r\nGYjyHoO3g14xeXJ0S+rZ0lycTcDSVWC+g4Udro4sBrW/s8WA1NpJAzvXG/uP\r\nDLJt4K0I/Q9CYTsR7gdlZTqQVETZnb4lBNizvcTH3KSMtEgWG+aY0RK0vhp6\r\nQoiuErAbaldqUw216ri17B/zX72w1pSFGOdvH0e//Dwuyz+frAQSFYJtMo15\r\nAPxDvR1wBhfjAnzllp9/jDiazCoxwf6dL9s=\r\n=Xz6J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-1.0.1.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/2d5fe96cf553a01692f4e28da509f92e/pathe-1.0.1.tgz", "_integrity": "sha512-SxIsbJOlu6F+XhUbX9OqTaYiUtq5yr2oqsjSJp5ivh/M6beS19qdJcPgQiKjnxjN8X6wgKGPAGtXElotr4tVrA==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.25.1", "devDependencies": {"jiti": "^1.16.2", "eslint": "latest", "vitest": "^0.27.2", "unbuild": "^1.1.1", "prettier": "^2.8.3", "typescript": "^4.9.4", "@types/node": "^18.11.18", "standard-version": "latest", "eslint-config-unjs": "^0.0.3", "@vitest/coverage-c8": "^0.27.2"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_1.0.1_1674246995219_0.5207350927490952", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "pathe", "version": "1.1.0", "license": "MIT", "_id": "pathe@1.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "e2e13f6c62b31a3289af4ba19886c230f295ec03", "tarball": "https://registry.npmjs.org/pathe/-/pathe-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-ODbEPR0KKHqECXW1GoxdDb+AZvULmXjVPy4rt+pGo2+TnjJTIPJQSVS6N63n8T2Ip+syHhbn52OewKicV0373w==", "signatures": [{"sig": "MEUCIQDZmBRxd4Q1HAFQK2aBqN7ALArtLwcjd2uhCWtv/ebSkQIgeGk7edQoNTeoq5iVVhXFhP5JlBrItP6f1s5eDzTd+iE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyxkqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo40g/+P7ljGU6/m75H8pu7B99Kch7W6cnL+xb4ofkOCKpw8rFdNFtq\r\n7TRPyj3aiEF6qFlbKsa+nzZXBks0yIz19baZfsUCrZNnisZnwSjg4lbxinlE\r\nQdzXeXsDR1VtSBBN51Jx4dHJswSTCcb4MMr/8mvHgyuoJb+bG+QmhRxMNc5z\r\n2DwpgmphRhbWRT2kU5oeSD1HokeVev/nuC0THCAtNFRV6jDDGc0o170VCOul\r\n3aKOCdkKrSmurJFc6ThaUsz7QEYEPXP8KJrP0gJ5RtxHRkH+OHpwmNYtKTAg\r\ndHUclInWCHSPDRCLkSPA6DDkGtNIT3d5Dls6yKu693aKlf1y5XQyywq2s0bm\r\n/qB97IRHr2nzEXWMk6z68J7aQnjzonNBr6PB4l/+CxXin9JxnTf+0SvvkUA1\r\nBmZ6v157LpgIegryarKW7oTU86VZLX19de2WEzJnEJRMDvGoZTsBvDTGiQi0\r\nDyN3jcvNhlqLIfOeuZISjSr9t8Wkb1yTG0oD/QTk/dhjjsJMIskMcrSwbUfv\r\n5slPDHvo7cYe5FSUKKlEAs3O9YeUto7e9mDGwOLFvAdGUR/lxG9ZXLUYYFex\r\ntjJy2oz0/bs/0KQORvnOK1f6LAy52LxmYN+3/GP/WU+aju2DWTwj24+2D4Ve\r\n7MGgcfxfdLPHzc8gs4bpZtVNMMPb45/0mJY=\r\n=tQpM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:pathe-1.1.0.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/fd1113dcb9cdc5bf70fe7054dc4801f7/pathe-1.1.0.tgz", "_integrity": "sha512-ODbEPR0KKHqECXW1GoxdDb+AZvULmXjVPy4rt+pGo2+TnjJTIPJQSVS6N63n8T2Ip+syHhbn52OewKicV0373w==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.25.1", "devDependencies": {"jiti": "^1.16.2", "eslint": "latest", "vitest": "^0.27.2", "unbuild": "^1.1.1", "prettier": "^2.8.3", "typescript": "^4.9.4", "@types/node": "^18.11.18", "standard-version": "latest", "eslint-config-unjs": "^0.0.3", "@vitest/coverage-c8": "^0.27.2"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_1.1.0_1674254634542_0.7831399332389091", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "pathe", "version": "1.1.1", "license": "MIT", "_id": "pathe@1.1.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "1dd31d382b974ba69809adc9a7a347e65d84829a", "tarball": "https://registry.npmjs.org/pathe/-/pathe-1.1.1.tgz", "fileCount": 13, "integrity": "sha512-d+RQGp0MAYTIaDBIMmOfMwz3E+LOZnxx1HZd5R18mmCZY0QBlK0LDZfPc8FW8Ed2DlvsuE6PRjroDY+wg4+j/Q==", "signatures": [{"sig": "MEUCIQD2uOUFK5m8f8r+P1WQlv0iJKGNd5lldpNlHkDH7nfzuwIgOnOyk5IcXb29/xDtVXoOha+75hfE9TSQYf0eN3llyZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25908}, "main": "./dist/index.cjs", "_from": "file:pathe-1.1.1.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm vitest run && standard-version && git push --follow-tags && pnpm publish", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/48/0bsggqvn049_nfcq7msw2njw0000gn/T/7fe7ecbd71ee3da4f357d5335ff15ce9/pathe-1.1.1.tgz", "_integrity": "sha512-d+RQGp0MAYTIaDBIMmOfMwz3E+LOZnxx1HZd5R18mmCZY0QBlK0LDZfPc8FW8Ed2DlvsuE6PRjroDY+wg4+j/Q==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "20.1.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "devDependencies": {"jiti": "^1.18.2", "eslint": "latest", "vitest": "^0.31.0", "unbuild": "^1.2.1", "prettier": "^2.8.8", "typescript": "^5.0.4", "@types/node": "^18.16.3", "standard-version": "latest", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.31.0"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_1.1.1_1685659535856_0.6393099301076497", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "pathe", "version": "1.1.2", "license": "MIT", "_id": "pathe@1.1.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "6c4cb47a945692e48a1ddd6e4094d170516437ec", "tarball": "https://registry.npmjs.org/pathe/-/pathe-1.1.2.tgz", "fileCount": 16, "integrity": "sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==", "signatures": [{"sig": "MEUCIQCAJSu+Yz/pg0k/Iv5CF30gC3npwDOotSfA8qbzWh/tqgIgcNYGzOK7aPgBTapOCuZWH2log26zNrBc1j609UFWvGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30806}, "main": "./dist/index.cjs", "_from": "file:pathe-1.1.2.tgz", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hf/lrc_fvsd0qv7rd6q57mkqp180000gn/T/78c2480a965308308bc7a71eeb62d1ac/pathe-1.1.2.tgz", "_integrity": "sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.14.0", "devDependencies": {"jiti": "^1.21.0", "eslint": "^8.56.0", "vitest": "^1.1.3", "unbuild": "^2.0.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "@types/node": "^20.10.8", "changelogen": "^0.5.5", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_1.1.2_1704912393274_0.131670463943095", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "pathe", "version": "2.0.0", "license": "MIT", "_id": "pathe@2.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "886588913857d23d64df1c6660e058b69f57f46d", "tarball": "https://registry.npmjs.org/pathe/-/pathe-2.0.0.tgz", "fileCount": 16, "integrity": "sha512-G7n4uhtk9qJt2hlD+UFfsIGY854wpF+zs2bUbQ3CQEUTcn7v25LRsrmurOxTo4bJgjE4qkyshd9ldsEuY9M6xg==", "signatures": [{"sig": "MEUCIGkiEm6UQqub+/ues9fmB9aHvCy6jZgdNacbRnSPmmj9AiEA3KmkwLS84axthVqDxkKuXyoM1KhuVdDi8oo2du9VOzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53395}, "main": "./dist/index.cjs", "type": "module", "_from": "file:pathe-2.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./utils": {"import": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "require": {"types": "./dist/utils.d.cts", "default": "./dist/utils.cjs"}}}, "scripts": {"dev": "vitest", "lint": "eslint . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "lint:fix": "eslint . --fix && prettier -w src test", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hq/c07mx_4n6tg50tlvwps5l8600000gn/T/b6dee29b2805be04ddaf654d93ec3443/pathe-2.0.0.tgz", "_integrity": "sha512-G7n4uhtk9qJt2hlD+UFfsIGY854wpF+zs2bUbQ3CQEUTcn7v25LRsrmurOxTo4bJgjE4qkyshd9ldsEuY9M6xg==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "devDependencies": {"jiti": "^2.4.2", "eslint": "^9.17.0", "vitest": "^2.1.8", "esbuild": "^0.24.2", "unbuild": "^3.2.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "zeptomatch": "^2.0.0", "@types/node": "^22.10.2", "changelogen": "^0.5.7", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^2.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_2.0.0_1735947890299_0.8460490434226775", "host": "s3://npm-registry-packages-npm-production"}}, "2.0.1": {"name": "pathe", "version": "2.0.1", "license": "MIT", "_id": "pathe@2.0.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "ee1e6965c5ccfc98dc5a4b366a6ba6dd624a33d6", "tarball": "https://registry.npmjs.org/pathe/-/pathe-2.0.1.tgz", "fileCount": 16, "integrity": "sha512-6jpjMpOth5S9ITVu5clZ7NOgHNsv5vRQdheL9ztp2vZmM6fRbLvyua1tiBIL4lk8SAe3ARzeXEly6siXCjDHDw==", "signatures": [{"sig": "MEYCIQDQMVznPly+26MpQDL6VLwbZgdGNq93RDxQ3gSCrHZK6QIhAM0Fve2UTrmzVxvtr1n45/6+bdHdrljAfqMwKTYWErQg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50397}, "main": "./dist/index.cjs", "type": "module", "_from": "file:pathe-2.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./utils": {"import": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "require": {"types": "./dist/utils.d.cts", "default": "./dist/utils.cjs"}}}, "scripts": {"dev": "vitest", "lint": "eslint . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "lint:fix": "eslint . --fix && prettier -w src test", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hq/c07mx_4n6tg50tlvwps5l8600000gn/T/f4e289ed926e36c70073c9e344c10e29/pathe-2.0.1.tgz", "_integrity": "sha512-6jpjMpOth5S9ITVu5clZ7NOgHNsv5vRQdheL9ztp2vZmM6fRbLvyua1tiBIL4lk8SAe3ARzeXEly6siXCjDHDw==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "devDependencies": {"jiti": "^2.4.2", "eslint": "^9.17.0", "vitest": "^2.1.8", "esbuild": "^0.24.2", "unbuild": "^3.2.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "zeptomatch": "^2.0.0", "@types/node": "^22.10.5", "changelogen": "^0.5.7", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^2.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_2.0.1_1736421286229_0.5510983514079015", "host": "s3://npm-registry-packages-npm-production"}}, "2.0.2": {"name": "pathe", "version": "2.0.2", "license": "MIT", "_id": "pathe@2.0.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/pathe#readme", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "dist": {"shasum": "5ed86644376915b3c7ee4d00ac8c348d671da3a5", "tarball": "https://registry.npmjs.org/pathe/-/pathe-2.0.2.tgz", "fileCount": 16, "integrity": "sha512-15Ztpk+nov8DR524R4BF7uEuzESgzUEAV4Ah7CUMNGXdE5ELuvxElxGXndBl32vMSsWa1jpNf22Z+Er3sKwq+w==", "signatures": [{"sig": "MEQCIAIEu+nGDOunDC+HMDHW4IWUaegzcaKuaylf8OGfih8NAiBVCUqLojUdChWHpfe/OZlOrOs9F1B8tI2Z0BL2zRTMcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51362}, "main": "./dist/index.cjs", "type": "module", "_from": "file:pathe-2.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./utils": {"import": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "require": {"types": "./dist/utils.d.cts", "default": "./dist/utils.cjs"}}}, "scripts": {"dev": "vitest", "lint": "eslint . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "lint:fix": "eslint . --fix && prettier -w src test", "test:types": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hq/c07mx_4n6tg50tlvwps5l8600000gn/T/8c6c92d3a49c5d7903758d1692583916/pathe-2.0.2.tgz", "_integrity": "sha512-15Ztpk+nov8DR524R4BF7uEuzESgzUEAV4Ah7CUMNGXdE5ELuvxElxGXndBl32vMSsWa1jpNf22Z+Er3sKwq+w==", "repository": {"url": "git+https://github.com/unjs/pathe.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Universal filesystem path utils", "directories": {}, "sideEffects": false, "_nodeVersion": "22.13.0", "_hasShrinkwrap": false, "devDependencies": {"jiti": "^2.4.2", "eslint": "^9.18.0", "vitest": "^3.0.1", "esbuild": "^0.24.2", "unbuild": "^3.3.1", "prettier": "^3.4.2", "typescript": "^5.7.3", "zeptomatch": "^2.0.0", "@types/node": "^22.10.7", "changelogen": "^0.5.7", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/pathe_2.0.2_1737118711924_0.6051132592521362", "host": "s3://npm-registry-packages-npm-production"}}, "2.0.3": {"name": "pathe", "version": "2.0.3", "description": "Universal filesystem path utils", "repository": {"type": "git", "url": "git+https://github.com/unjs/pathe.git"}, "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./utils": {"import": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "require": {"types": "./dist/utils.d.cts", "default": "./dist/utils.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "devDependencies": {"@types/node": "^22.13.1", "@vitest/coverage-v8": "^3.0.5", "changelogen": "^0.5.7", "esbuild": "^0.25.0", "eslint": "^9.20.1", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.0", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.5", "zeptomatch": "^2.0.0"}, "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint . --fix && prettier -w src test", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage", "test:types": "tsc --noEmit"}, "_id": "pathe@2.0.3", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "homepage": "https://github.com/unjs/pathe#readme", "_integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "_resolved": "/private/var/folders/hq/c07mx_4n6tg50tlvwps5l8600000gn/T/15e46780c8f375ff733b6a4d49e4b75e/pathe-2.0.3.tgz", "_from": "file:pathe-2.0.3.tgz", "_nodeVersion": "22.13.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "shasum": "3ecbec55421685b70a9da872b2cff3e1cbed1716", "tarball": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "fileCount": 16, "unpackedSize": 51332, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDzM704JU5xv2pHW6vgkO9qvD2Ol5+/NegZnN5zKrnOfQIgQTONgEww6dMrAOJCeXz2w/EKhJEnU4gznS3Qdnh62v8="}]}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/pathe_2.0.3_1739303255661_0.5690093091666735"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-09-22T16:54:25.748Z", "modified": "2025-02-11T19:47:36.057Z", "0.0.0": "2021-09-22T16:54:25.905Z", "0.0.1": "2021-09-22T18:20:32.296Z", "0.0.2": "2021-09-22T18:46:31.356Z", "0.0.3": "2021-09-24T12:14:39.836Z", "0.1.0": "2021-09-24T12:14:51.068Z", "0.2.0": "2021-09-27T12:16:38.059Z", "0.3.0": "2022-05-05T18:05:43.548Z", "0.3.1": "2022-06-27T11:39:52.537Z", "0.3.2": "2022-06-29T22:49:41.242Z", "0.3.3": "2022-08-01T11:42:28.019Z", "0.3.4": "2022-08-10T15:09:50.764Z", "0.3.5": "2022-08-18T09:40:33.610Z", "0.3.6": "2022-09-06T08:14:54.250Z", "0.3.7": "2022-09-06T08:34:16.518Z", "0.3.8": "2022-09-19T09:12:20.731Z", "0.3.9": "2022-10-07T09:59:24.495Z", "1.0.0": "2022-11-14T13:26:05.748Z", "1.0.1": "2023-01-20T20:36:35.382Z", "1.1.0": "2023-01-20T22:43:54.667Z", "1.1.1": "2023-06-01T22:45:36.018Z", "1.1.2": "2024-01-10T18:46:33.437Z", "2.0.0": "2025-01-03T23:44:50.505Z", "2.0.1": "2025-01-09T11:14:46.413Z", "2.0.2": "2025-01-17T12:58:32.081Z", "2.0.3": "2025-02-11T19:47:35.862Z"}, "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "license": "MIT", "homepage": "https://github.com/unjs/pathe#readme", "repository": {"type": "git", "url": "git+https://github.com/unjs/pathe.git"}, "description": "Universal filesystem path utils", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}, {"name": "da<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# 🛣️ pathe\n\n> Universal filesystem path utils\n\n[![version][npm-v-src]][npm-v-href]\n[![downloads][npm-d-src]][npm-d-href]\n[![size][size-src]][size-href]\n\n## ❓ Why\n\nFor [historical reasons](https://docs.microsoft.com/en-us/archive/blogs/larryosterman/why-is-the-dos-path-character), windows followed MS-DOS and used backslash for separating paths rather than slash used for macOS, Linux, and other Posix operating systems. Nowadays, [Windows](https://docs.microsoft.com/en-us/windows/win32/fileio/naming-a-file?redirectedfrom=MSDN) supports both Slash and Backslash for paths. [Node.js's built-in `path` module](https://nodejs.org/api/path.html) in the default operation of the path module varies based on the operating system on which a Node.js application is running. Specifically, when running on a Windows operating system, the path module will assume that Windows-style paths are being used. **This makes inconsistent code behavior between Windows and POSIX.**\n\nCompared to popular [upath](https://github.com/anodynos/upath), pathe provides **identical exports** of Node.js with normalization on **all operations** and is written in modern **ESM/TypeScript** and has **no dependency on Node.js**!\n\nThis package is a drop-in replacement of the Node.js's [path module](https://nodejs.org/api/path.html) module and ensures paths are normalized with slash `/` and work in environments including Node.js.\n\n## 💿 Usage\n\nInstall using npm or yarn:\n\n```bash\n# npm\nnpm i pathe\n\n# yarn\nyarn add pathe\n\n# pnpm\npnpm i pathe\n```\n\nImport:\n\n```js\n// ESM / Typescript\nimport { resolve, matchesGlob } from \"pathe\";\n\n// CommonJS\nconst { resolve, matchesGlob } = require(\"pathe\");\n```\n\nRead more about path utils from [Node.js documentation](https://nodejs.org/api/path.html) and rest assured behavior is consistently like POSIX regardless of your input paths format and running platform (the only exception is `delimiter` constant export, it will be set to `;` on windows platform).\n\n### Extra utilities\n\nPathe exports some extra utilities that do not exist in standard Node.js [path module](https://nodejs.org/api/path.html).\nIn order to use them, you can import from `pathe/utils` subpath:\n\n```js\nimport {\n  filename,\n  normalizeAliases,\n  resolveAlias,\n  reverseResolveAlias,\n} from \"pathe/utils\";\n```\n\n## License\n\nMade with 💛 Published under the [MIT](./LICENSE) license.\n\nSome code was used from the Node.js project. Glob supported is powered by [zeptomatch](https://github.com/fabiospampinato/zeptomatch).\n\n<!-- Refs -->\n\n[npm-v-src]: https://img.shields.io/npm/v/pathe?style=flat-square\n[npm-v-href]: https://npmjs.com/package/pathe\n[npm-d-src]: https://img.shields.io/npm/dm/pathe?style=flat-square\n[npm-d-href]: https://npmjs.com/package/pathe\n[github-actions-src]: https://img.shields.io/github/workflow/status/unjs/pathe/ci/main?style=flat-square\n[github-actions-href]: https://github.com/unjs/pathe/actions?query=workflow%3Aci\n[size-src]: https://packagephobia.now.sh/badge?p=pathe\n[size-href]: https://packagephobia.now.sh/result?p=pathe\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}