{"_id": "cssom", "_rev": "52-2ab5d788b53685f3b2e13777fbbddfe9", "name": "cssom", "description": "CSS Object Model implementation and CSS parser", "dist-tags": {"latest": "0.5.0"}, "versions": {"0.2.0": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.2.0", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "directories": {"doc": "./doc", "lib": "./lib", "test": "./test"}, "main": "./lib/index.js", "engines": {"node": ">=0.2.0"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "_id": "cssom@0.2.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.97", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "ce47d8d63b6bfe46b447476c7feac487b44a8a25", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.2.0.tgz", "integrity": "sha512-IAHXeVCawAZXqmV5nUdlRCPXuDwoiwm3nU6hyGaEsr1tMKOeTEuUlIEA4ULcCfOnvnHJvG/HIM+yowvhZxoOBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDl0VU0h8DIMppuW4NMYH+c3f2zHl707jtpFMOo40UbAiBD9vI7gSi2gLhLevnw8dgaL1hDnQg1RQ3+KR1k9OpqRw=="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}]}, "0.2.1": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.2.1", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "directories": {"doc": "./doc", "lib": "./lib", "test": "./test"}, "main": "./lib/index.js", "engines": {"node": ">=0.2.0"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "_id": "cssom@0.2.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.97", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "6f8353134a3462df471827ae768cd1715885f652", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.2.1.tgz", "integrity": "sha512-ggJxRSam/+6kZzrgm+fPFaTSmbj8gvpJ34govDlW1Wf0EMUzSqcH8BeChyar8t3XPJvNO6qMZuHJsh4CiRNZyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtOw3TRBS7XCPDU2LV8jS2dxVHCZdCU5WC5gGLMvrhhAIgavzVo3oNzKUnEzeqdAz0JAesoihroBxi92FeqhkY5T4="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}]}, "0.2.2": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.2.2", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/index.js", "engines": {"node": ">=0.2.0"}, "devDependencies": {"jake": "0.2.x"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "_id": "cssom@0.2.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "9da13abfa0de8cfba73cf9d5dc3e5d992882613f", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.2.2.tgz", "integrity": "sha512-RFyXl+ruOWy2eL3Ic0a29JrA9GFqWHE9lEyWB5BsQ5NXnwiPqw9Lho6+1lJmUhMBM9bcOziZrXBcQ8lwTkHC9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7I2wozBbYXo0OV14y2xCqM47mUPv1YD+9WckEqOpOSQIgP3vAvTFi9RtbK/gw6UzHfiY5a1xPCJlsoYRCzPii8o4="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}]}, "0.2.3": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.2.3", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/index.js", "engines": {"node": ">=0.2.0"}, "devDependencies": {"jake": "0.2.x"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "scripts": {"prepublish": "jake lib/index.js"}, "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "_id": "cssom@0.2.3", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.9", "_defaultsLoaded": true, "dist": {"shasum": "7f9a236c0b05dbc06945ae055ba1c6d8eab91c69", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.2.3.tgz", "integrity": "sha512-BIQF59nG8UTQpFcjnNpZdLN4u/lSmtSQkKL31brHN9xJTStdgVctMEbpJSwjR1NpqsQ34D6e8SIVAt8QQpf7JQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAsKlZ/ZVJzemlUo+7vy5nuKrFNJI7gfm0L7dbOEKyQgIgYeCirNWkxePeUXOd6zBuUjT2VC7CZmlo6DXEp8+TUAA="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}]}, "0.2.4": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.2.4", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/index.js", "engines": {"node": ">=0.2.0"}, "devDependencies": {"jake": "0.2.x"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "scripts": {"prepublish": "jake lib/index.js"}, "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "_id": "cssom@0.2.4", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.6.19", "_defaultsLoaded": true, "dist": {"shasum": "3d989f0ef82910df370ee03d97f5d546759c8b5f", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.2.4.tgz", "integrity": "sha512-0OAJk015AcxboFzepwyKvspVXseHS4lCIR2rhm0NzPezeZoxDau+3VLcZJiXCERWuerhqEIoGEiqzxCz0bBBMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtPKViZhtoUf8EkgRcw3BDmNs95LmUUI+zpRa0zeC7hgIgfjm7tRY+qZqcueGwdQeHFgFBDVJI9UiKWibbORPontU="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}]}, "0.2.5": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.2.5", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/index.js", "engines": {"node": ">=0.2.0"}, "devDependencies": {"jake": "0.2.x"}, "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "scripts": {"prepublish": "jake lib/index.js"}, "_id": "cssom@0.2.5", "dist": {"shasum": "2682709b5902e7212df529116ff788cd5b254894", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.2.5.tgz", "integrity": "sha512-b9ecqKEfWrNcyzx5+1nmcfi80fPp8dVM8rlAh7fFK14PZbNjp++gRjyZTZfLJQa/Lw0qeCJho7WBIl0nw0v6HA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgr1XoQ73vVq3qL234PhNdlx+KFmpcOuNdgOuxLduF1AiBnDAIk6fs7etofgdJZjiCw6LAjaxuFgk7n3hShFguhGA=="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}]}, "0.3.0": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.0", "homepage": "https://github.com/NV/CSSOM", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/NV/CSSOM.git"}, "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": {"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}, "scripts": {"prepublish": "jake lib/index.js"}, "_id": "cssom@0.3.0", "dist": {"shasum": "386d5135528fe65c1ee1bc7c4e55a38854dbcf7a", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.0.tgz", "integrity": "sha512-rdm8ap6kLpJjI9MDKoECB/Eb8ft+JZNDWJ6zETzR34vUTLLgztSNUVFxOzojw3F1WJewhzQETeh5EHwHMUumbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZuolIl+Be2F5djZZqqCm7UXD41eep1KqQyW1r/3MOvQIhANv2XZp0Wpo5/1poABLnRfQjvwbr0ijk9Lj7PVXMz70H"}]}, "_resolved": "c:\\Users\\<USER>\\AppData\\Local\\Temp\\npm-7124-1Hep516w\\1383965915535-0.7970385393127799\\tmp.tgz", "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/nv/CSSOM.git"}, "files": ["lib/"], "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": "MIT", "scripts": {"prepublish": "jake lib/index.js"}, "gitHead": "c82ca18e35e207bb8ce57ffa2d3b783c026f7a52", "bugs": {"url": "https://github.com/nv/CSSOM/issues"}, "homepage": "https://github.com/nv/CSSOM#readme", "_id": "cssom@0.3.1", "_shasum": "c9e37ef2490e64f6d1baa10fda852257082c25d3", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "c9e37ef2490e64f6d1baa10fda852257082c25d3", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.1.tgz", "integrity": "sha512-y+pT9rKcn6hVx6J2UTPMEgUR1jTuF+ea0iA9j6e+YX0ELrQgxCYPKH4XfB7SIOkkLJyicIZPVEjq9kHxDJshfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDC+PlMkuTDkVGsjCcVmhIQ3svFRTzCxa5dCT1sr5tw/QIhAN73zrFvnZqx4dA7gJ9Oei7+KT4/S7IcPCB5fJ1i8qvB"}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/NV/CSSOM"}, "files": ["lib/"], "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": "MIT", "scripts": {"prepublish": "jake lib/index.js"}, "gitHead": "d600816ea9442c5e33f27ff59de536d7b7ccd239", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM", "_id": "cssom@0.3.2", "_shasum": "b8036170c79f07a90ff2f16e22284027a243848b", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"shasum": "b8036170c79f07a90ff2f16e22284027a243848b", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.2.tgz", "integrity": "sha512-N72oNABuYrOUdPYkT64J8mvYNxFU35kU4Zer3N69aWz4AylPUlSiS/75J+VMAMz9OFf+TTrN3UBrPbcEegOqtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAe/5yCGNXp1zZh1sqhIXfHXlHDTzsHvOL+B8BjrogkjAiAKZcyEYeRtepISgAvqy2ZRtk2c6lCwwfyEV1DjSzRkOQ=="}]}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cssom-0.3.2.tgz_1484956361462_0.48309571552090347"}, "directories": {}}, "0.3.3": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "files": ["lib/"], "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": "MIT", "scripts": {"prepublish": "jake lib/index.js"}, "gitHead": "bfe6d7eb9ece90d5f3419d2219b7ac4bdbdce6af", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.3.3", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pjE/I/NSp3iyeoxXN5QaoJpgzYUMj2dJHx9OSufoTliJLDx+kuOQaMCJW8OwvrKJswhXUHnHN6eUmUSETN0msg==", "shasum": "7b344769915759c2c9e3eb8c26f7fd533dbea252", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.3.tgz", "fileCount": 26, "unpackedSize": 58104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNSXOCRA9TVsSAnZWagAAuGIP/RplTpNuFWt6j6fQxxb/\nXaebzHT8IvGc1AkAtgLFlEfmebCEQ14hHizwojILWvpiEt3DoV19zz9EBWX6\nZNn65AUuFv8mt3rAgkIMKS1fmGGpLcKHKQh/lIDK9pp9SyG8EDm429QNVqNq\nFdPv2y2jcqJQ7O5Q8d8gcMaJar+bVlnuwNqMX+12rJbYvFhH+sVYiqM48k9q\nmZJUnbE0U1GJmRbf19fUdgPhd0mSafrEl3AxIa04Y390dZ9+n1/qYD7ciktg\nmGHzMGKfarEGufUvvuJMHoE1kCgXjvWrQEspvPPPMBDUtn6vGMC+2FkK7AYi\nwDcNUdxaxmc+SlXVT31+GKEb0tY2vmv0qz7VJeTl7ePUNwkegzyFMW7gMjoN\nIgPv2E2idc//G7y6uC+VhBVw6YBSMgMtA3uOs6Uhsc8Apq6Q1j0WIEf+Z7DQ\n9zxAmQVdwjlxMSYMy54tsGahZjdd9srTQqleE6SVvY2QYq5TlcL0s4Ie3V+M\nLQv0eJyymsybr/DidNfxlP2r1B8L0J2ATCI5qjg3Z+MC/Iyzk0riYrMuxFey\nj22BdPCBna3FaOXjddJJhmFQLessK2rndpbmRzd8MNiLgnUG6AXetcOE3mzQ\nUV583Sl6/9ocB2KL44+h88xMMsAyx213+/GjsOEhqTFce0L9ZWju4/hr4DMt\n3aHm\r\n=kdO+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0jPzkwPfpgUDJxgnl1ocCtNZY5z9j0qUSddfPbPwaaAIgJVBdhMuPUXISf4zlmaXXohtBoFNJUjcGSbrChT7a0Fw="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.3.3_1530209742443_0.028199153019609957"}, "_hasShrinkwrap": false}, "0.3.4": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "files": ["lib/"], "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": "MIT", "scripts": {"prepublish": "jake lib/index.js"}, "gitHead": "16186bf80b98949a79c82bc35b3052e4b51c3e4c", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.3.4", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+7prCSORpXNeR4/fUP3rL+TzqtiFfhMvTd7uEqMdgPvLPt4+uzFUeufx5RHjGTACCargg/DiEt/moMQmvnfkog==", "shasum": "8cd52e8a3acfd68d3aed38ee0a640177d2f9d797", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.4.tgz", "fileCount": 26, "unpackedSize": 58176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbOyLsCRA9TVsSAnZWagAA+FIQAJYwWQvWLrVvV6V2PIrT\nuNe0Y2y2aP44W8r+5kp/goyM6l/41iAdNUEELaVYdQJmh8G8qKdno9FBxSr6\n34GzOmdbduhMRDI9OuQ4UltZ2egMdK2BP3Qcz8qkjCQM7cds+BgQeB2r9IJX\nYhRIf0/fmskSXNYrDRP51xg7V149o1hXRA2YtBOP+gPE7pHEhTZ2AyA2Tfl4\ngCgKprKiUvLtTSxbVPmLd/E7DGZwWqEJ6bt5g7fLQK6qMfpRSOMC23TytCWN\nZ6ZzMjuWhweZ5+A5pU1Ql07Qgj/Cx/nyFNOW0GtqIAw/5cQ0mbI+FL3kpu50\npSGv+TFEEx5vHv0mD2SMN+PVRA4XkCUkG5Uc6/6vnZvoA+xaqZgM2XnmXuPD\nHuWg+iCtTeypY5w+60J3Qf4Z9NXc0Hs97w9hwNxqXNNTnvFABwBFeRVG0UIi\nJNu0JQp5nHIOTWhmHnunZSPzOqeoRkgKp23plfgbwYRhQ1wQRRx5hOVVMq91\nzXb40YFlEFFOcomqWu/bqgi5kIkylRmptDXb3nDHbgakUYEMxGIKznL9ZUWI\nm7kTHCNoOorVaT4bnKrQRujZPiYkc3ZjBmt7eWDvFO1sdME5qOqEMK1kaVvB\nGxX6r7OIDf42VbNMG5fjj0NED4GLnvMh8aym/qiueKhLT599XPI39ykRc2+S\npo4s\r\n=LBOU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFWDMrzMaxc+/iylhjyJ+x9Nt56H9LEChYudZQFXaqfGAiALKK2GkN//BUPN5WuNAtCoIwjJysJe3kIMchy7YNbn2g=="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.3.4_1530602220273_0.5727537425865918"}, "_hasShrinkwrap": false}, "0.3.5": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": "MIT", "scripts": {"prepublish": "jake lib/index.js"}, "gitHead": "16186bf80b98949a79c82bc35b3052e4b51c3e4c", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.3.5", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2yqFovCQZAJ/a2y6VUi8JLLs7WSkOYgGV9/5fQmBFz0sEpUDKMXzS0Sn2gmAWr9RF/5Vo0D4oTitifcYVSr/sA==", "shasum": "f2faeb733e4cf7e5d705fa419927a91da331d771", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.5.tgz", "fileCount": 26, "unpackedSize": 58176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWOuJCRA9TVsSAnZWagAAKiIP/2obaaBU8q0N1gCipazd\nE1jZH2+lONDI5dHSdy2zXotqySvhoOTNXyVxtiCeSBsz2ATUp+FZJ4ZIsHwy\ngNINbMB4ybXy8PlXW2K2Tdg9acct/6yd3J0d2KHBp4GgJzUGlTA3zJ62hrWj\n2ZSfK3BCa4Ev6yF+W/+LGuPd3LjCN9hRj9emjky+4f9ftU3FQ5uk/lEWU+dw\naNOVVeyd9QXGaQb+OHfQoSe2TZNJsLj1GZNxvaRdL6Bg9fZQorEfBmM4JIij\n1DOa8EvFpd45HoxmEPI3mY1Z2e7KAaYgI3L6375b4rXlpI2HzJetS25xQ4Ub\nL3pC50suN7dyqpNpOs7zQfsmAqN6ID+Hmiw/y7A6R/1ob/nsrWVTydL/0tQO\nnkjcXTkRNQL43oDFq7QlUenQNZi95WvRVE4Ix2Nr6raJ0mY7+cRXP9rE9gEN\nCoKGxfuSuehH7w55MjtnDvQSK2En0bWQX/SpXC4rHAAhSd8wjh4eGq6GULri\nGuYyvR8Bh+dYi4c+hngoDYcJtgg5FLRF8EYEa7jHQ58lRyopzrGovGsYN2b+\nKFtPvhmeAO0VrWj7KUagb85LsUqPCwFXDaLd7ql5eqPY8mJA993hu8sayojb\ngGH0axauqHKRSRujvBs9IAPyZ5JqTz7LYybjhk9Bj5WVatVopNnb5tHHd3YD\n9Cr6\r\n=0Jma\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLFsIK/bMd3w9WnxobQLxtZ59YdxHwj1Vk5iYHjx+WbAIhAKQfQl8JLoKyZM4wN/VlRJYOHSOIC+Pptg+9HbjURVA2"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.3.5_1549331336364_0.9618972218564223"}, "_hasShrinkwrap": false}, "0.3.6": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "devDependencies": {"jake": "~0.7.3"}, "license": "MIT", "scripts": {"prepublish": "jake lib/index.js"}, "gitHead": "5938d78789bcb2292f1d2671b623ab75bd81ca59", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.3.6", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DtUeseGk9/GBW0hl0vVPpU22iHL6YB5BUX7ml1hB+GMpo0NX5G4voX3kdWiMSEguFtcW3Vh3djqNF4aIe6ne0A==", "shasum": "f85206cee04efa841f3c5982a74ba96ab20d65ad", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.6.tgz", "fileCount": 26, "unpackedSize": 58734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWO3pCRA9TVsSAnZWagAAHo0P/A0uMQ9wsbuzTSS6QRsp\nTS9jVocS/xfS/Xgr07V6job313Mvt2T7028+ml5/VWdHjX95dcb6Dj6VX3fT\nqsBwMzD6JpJCC7UslyeppURBiVXJgQI/FGII2NBqtw8638lFhLNNPubeKP1D\nlg97L5y9vMEYUgYv2AMt6IKBKd0gA/1oojzBcvayL5NukH2mKJiOEQ9gYtH4\nCU3Y7unzcKneS1zqvifirauwiq5A/OjbQjka0Yj+z1YrEWxRLiAcykFAzwdx\nU5TnT5/uSAGeeIVQPtFG3EZVTv4eb28eHH0N5QVO+g8a9wmv5j/5JNtcOjn8\ncl8Rce6HNSfKHU9WHpiHewPr/95C/FQHn0szRl8IBkOkHhDsN4slX0yIc/qC\nfj40OX1ANYZF/kMgCLOwFhaIJFjg8rxtG67Q/KF1Cau7uVcesLJgh2U68/ov\nP5qRkBlc41GoPxVS/zSPHp0nSCVJxi1vnIITxSdNeamDvQvXTJCFZCMzeNDB\nKUnS2YWEB7+aOwH7nY7W8TbHwhnjgMRvMRDqztIM9mqaQKIvwuyY25RjyP0M\nzC2ScSfbyLvxVwRH+ZxaxAJXBtMRHoW5g88t5BpisQ/jfLNwvE3A7/VTyJH8\ncPe7WnQrYP2Do2NQ51DBIk6x0l41I0u2F7vNNhs3EVw0eLJmX9jy7etBKRUn\nYwGp\r\n=2pik\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGMEqDOVA+rWZ2cUITT0e6QMyXlHM4AY1rHG7LukwJdgIhAMZHXAYbFIDIEMRwwWCnuqETAvDqHF72WKHFz2hSb7NX"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.3.6_1549331944759_0.7002217265935236"}, "_hasShrinkwrap": false}, "0.3.7": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "7f740ff8db02269346cb954da03fdf91098dfc18", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.3.7", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tW5VpHfHaWiPXE+qhg7lCIbd5PcbdUHRkEe/CnL5dfaUMQiqxczBwbxAmWnOtOjgNye2yviH4JdlS/NDZnODwQ==", "shasum": "19079ee89e65d5b1584abcd0be35b9e7402d5592", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.7.tgz", "fileCount": 26, "unpackedSize": 58559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHWRxCRA9TVsSAnZWagAADo8QAJdq5piFdam/2PcrRay0\nKzQPZ+zX20i3fGWfxoX8k6YLj81wYhG/11R44WBoAoAdiB+34386CNDLOwQw\n2ZkaZ1h1KY5nBGbW04JkPfsMn4ei93iRFsgpCovwrhYAmex/zqWQy+J5/Rxe\nFGXhiYCcDAwVafTlie/sIh/Fx1pka9cTApGyCEqnmlnnkyjDdNhECXYGVio6\nC7Q3p105Ty979KT7D31xDo77UCAO03xw2bky4x6Pu/daVMe/34CFILeQBzyw\nOOaTW8Te1zSwQewtEJZ5MTsoOoQ4qe01uuQu1EwfVa184XeV9OeBP8L9c7SL\nBY4jHtQVHpL+PPK7w0jammHNKYLNVp/DA9W4cpcIiRZgzmKUwBr8qDmJcKtr\nX3upX63YA7wbhQfpEmuyDNA/DCF0KN6KrWei9VI1FOoN0F6YjClgsa8ElaNa\nnWP2hfUZsiYt3KlAzftBO0tZngZgiFoSxa2/j/TPBWenVHkRQT8m4hYQ/KkV\n6RH/0CEJd4y/S9kWTjfMFd/O5aSucIMFQFjDunBcnnbaHoib7IpcklndSIjL\nQJdXhDg3XXXHZ3ZmEelX60nQriNQXBt5zcUGYUmTfjp1sS4sJ7AemPgQUj03\n9cuUQ+qA8cofp1n3ekgnkcTwD8bZJKJOlkajis00tRvBIgDMXQqsc+kf87qj\nkIFe\r\n=9HRu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCg3/tAxrM6LwAaoKEP6cqikltnQ6U9ESu/9bk3AIwAwQIhAO9hCAAwpI7Z0bsKYgQQBXlkDxKGSstQGOeu27DTiHEH"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.3.7_1562207345079_0.0033368420546759925"}, "_hasShrinkwrap": false}, "0.3.8": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.3.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "c1d40c63bb06545637958f21a7214929bf833a3d", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.3.8", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==", "shasum": "9f1276f5b2b463f2114d3f2c75250af8c1a36f4a", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz", "fileCount": 24, "unpackedSize": 48996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHWVqCRA9TVsSAnZWagAAoEsP/jXIxFfTdz0IIyORSH5W\nEMA2NfXjljgazgDMtDZBWwrFHmaYUtV1Wp7qTiB+iCjftkHy9x4H2tcf7I7G\nfIHJiURGGiSPjfzngHEhpyPWVTYNydxY3lSqtfP6jgkJrwRjLWbRkyCHoQ6p\nV/KhKxno29AsoW+WrHlJqrsbmQmlq3hYlZcjCxKcrV2oEZcGKJnVlAvndx7K\n1CytTQKHh8XJCClD6/g5QzU3lfrSCLMJO7+zBTQBHcFOxxGPfKVZAbjcs5R2\nFtrlFW9Ul99a6vWowgM1DPR21Tr/iJ7SDQMcAG2AecC3Swa/O5GcuYgH64DO\n0QgZFVbxovlz2CWWeqs9bbYbhE29JsOklXCL7ysOlabapnUSZ83fvFsa46Cl\n9xUXX1RD/h+laaX2VOqBMImmT4Sw3BhhUQ4THceLmLkaA9xVeJyVXw7LpeEm\n649dcM8cBlyyNQKJ+kippDuCIqeB9hyRHz/m9r6wFaDMxAaZkKGxAO6Izvkh\nwkYr/Z3vELDObe+mtooJwlJBcJsiQYXYVeBUvInfARVW2Ar8Tgtf49JbIQmD\nju+SYGI984Y5v/KdzPMgJHPJaXYh45ZZPIVQvPPcZM8OFLiwMH864owXS++X\nSmDh/+jnprgOWrv9THHF1+A/sT4b7d5lrNYxB1mbALo6Wvjff/kx2m8P43cW\nK8g0\r\n=f0PI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsQAfd86mnQIJXP1g6cHkc4pxHQW6h0AK8lx6G6HPtsgIgIpIGQOdkNOOIFoeuZAa7kYrIFGO1R5DprhTw3aBhf5o="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.3.8_1562207593964_0.4169974114837407"}, "_hasShrinkwrap": false}, "0.4.0": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "7b0d9aaf2dd8ab9fb0a5c9177539fdf01e06291f", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.4.0", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-i7nvDQYa6VUSza54xWZxSP5b+nKf4tUSqC9L77zyvNyG054sqhyqawBmAuHu+bI08CrUju9FnR649PRM4tkr7w==", "shasum": "d9f41c3ba7be95568d3c17bba3a50c087f22601f", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.4.0.tgz", "fileCount": 24, "unpackedSize": 48996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHWicCRA9TVsSAnZWagAA2H4P/0ueSXmYb3MkjdXrkez5\n2z3TiGqZA+YmSps4O88Xvl92uLKuSrBZXXZmcdHY2yigfcCzypmIg48aN5cj\nzvgwOUK5uDv5iir63iOUOA5qG2iWgF+yT/4teL+IEL4jS2+7fWtZ5oljq6o3\nIstWFryc3GgvnwVm3a2+wX51ktzlbT0MVGLzMb7BMz5CWVcLiFYgx4OQhYZ4\naZCHhQgYPZBeQ1FlK1Habokf8AJGbJo6xKm3P0V4N+/gpSWXuofmojLaSoN3\nNu62f70se1pCp2LrCsDR0tJltz7aaQRJ1EvpuF0YGSexa0rXyPO55jTGQetJ\nOQHHAfNhtOsqCWN+w1LLtwRmdg55oCb6B1a02KStirhBqu/Glz0gEFec/VOW\nyp13MNbvC54zCgiXMkQJkE7I7Pqtkutl1EyfmiGYwsVywTMghT4KxQEUXNY8\nQsx1hPnzJsCStm6kf33rP5tOPd4Cptvmu7FpPXXU0p9xg6IkBKExSVlFWC9S\nRlOxtHz8O1sP1T6yOaAY1JMWwIikmizQsDQFK6U3oHQG84zVWQ1szwHcezsL\nVh6L0UmQ7NUwdkfvggBHoJMRqm44MSGLLfoFVfD60riqIN7IjYA7OocvkK6G\nXADUis7MsiYuxWmxMLTBWji57t+cozIAKMOeKKSOLATyUOl6LRohwyds+P5E\nHLxt\r\n=nfh6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBwYwdbbNCjkXJrQv9q/V4JmkIRSzc5RutOp3H8RgEBQIhAOut55hCuaz8PvwpPAGpxSo/BFloJBVE8G6clrzze+Es"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.4.0_1562208410651_0.25736851094807367"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.4.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "7b0d9aaf2dd8ab9fb0a5c9177539fdf01e06291f", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.4.1", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6Aajq0XmukE7HdXUU6IoSWuH1H6gH9z6qmagsstTiN7cW2FNTsb+J2Chs+ufPgZCsV/yo8oaEudQLrb9dGxSVQ==", "shasum": "b24111d236b6dbd00cdfacb5ab67a20473381fe3", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.4.1.tgz", "fileCount": 24, "unpackedSize": 48996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHXAiCRA9TVsSAnZWagAA21wP/2AHlraUt/limi7eKJil\n29l3fySNz9/ToB46OcFE+2/rzhzLdQ47Nk67nd1E2a0V6bix3stm9fVjfEmp\nZiC48Ppfgx1NtAooQ/59B75gZPhP2WEUlQeOWP+TkAjW7rOytdDI8BMnSrA4\ndZNS7jKedfU/CIH7XyDsHeqLVrFWQIiBf+wkZUzlR82pqCFHXrc0g/qwCNnP\n0uRMiQlIxaMgz5sS/A6NcsKwnTB3+iz09AT1uldr6NWUgKdK8Y/LXe6wvknm\nIv+q4VGQk6EuFWaBAW/V7U6api1IscB4+s2esMIIX7zFAQCxsOkuHUaba2Vc\noHOq1DZG5on48lSTCJuW2Y3623HkT4PJF32InJ62r8hy8kApkl0qTVzA93Tb\nOohjSkpMkdGJE2s+gdfS0Xk9PtER6GA/gszeMXgkDwV0fcaZUSP/kAjcbQPC\nPsYXfLbi/4Vs04Ff7vkSAlJ5izm+PAwLJUiTEpDCW/Ac8NJPZOBbBrAD1N7N\nQJexoxrkfUUjQZxBREsXSte3+Sz7H9llr0tVlPaCAt+hLNmCXTnZKRrtIoYL\nDWQR9TY4ur+G/VNh/Q9FTN/jgF9210QCKl1sQjEbuAbsJcRXJGmKfWqU2Lps\nve4O9Z7uBNKuqHitw+yNpMtZS+HVBm5wswBP7foWqOgTQoziuK/GQVoZ7huT\nkHBZ\r\n=v20Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjfteOXOVvH3941qz9qwa16NLxtjjzHWCH7cpwMnHMhAIgPiBQcn+c+a4O3m/rBGH+vpLx+8CjC5g0Xx7tg98oQQs="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.4.1_1562210337658_0.8607138506233405"}, "_hasShrinkwrap": false}, "0.4.2": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.4.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "ab29a6e3951960438f6fb9f7c2ae4466a171889b", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.4.2", "_nodeVersion": "8.11.1", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-fVXFVBr7JPDcgqa92UNr6HIpeMypyG/XVloB+512KH43Z2aum8ZNVzRapWR4mZ/f2UlRMymIoDO3aFJmQ6Y3RA==", "shasum": "e2abd06e1267a7f2e5eccd7770c9ebe1bd88648b", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.4.2.tgz", "fileCount": 23, "unpackedSize": 48723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyND6CRA9TVsSAnZWagAAp9cP/0IOLNWK7CGqjrkAMcLW\nlBQow93b91TV3PzFqojuP5Rx+W4jsnIDMwVPKfjfD2v4RyKP5FbdBNCbvnx3\nG/mrjwk++49rEPhylPqDCl3ACsWjYhsrrXwniRnpvwZW1EpXziGOL4vMrxJD\nBN5P4AOSws7svTc6V+2memcaI9VT+c+OoFgbEXKBUkXyEKNhowTOEDPfVBWL\nCZQtt2XTBXzGcb1mRQz173M3jbrN/F3ZK7SWAr8FgMyszZiDc0MDLlURIzKN\nnlygcCOCPE6jh1o4EN+6Cgt9lZ6SLqBpYXYzItFBTOqqI95G/JY3ZSK5nGli\n9cGAbL7yPLgOC4taJ+qKlGuAWs01RaWU56jhYNegAYosfGa+dP7yYg7W/eD7\n0UDFJCGRPY0Cswlr63zDU0z99uIrSMYUwyAB0ura6rr767pGe680K8Coqdv0\nmOKhWGwSltLOLSNXQwOLYwkvlnLzMbbWaSUEFkkB2xFrzY+EUHnHvpNH5UKL\n8AeBo5UqgQ4gX4U/h3dnHT4fQ0BC5IYuZTSJRdCbPDCHhTViq9aGxpWpwz/p\nwYfCsr+YpmeE8Co7hNrQ6COKnjdpD/APLWrEHtWa+twfim0XLs1Hx2LTeFp2\nBUCSVcxjqrxbo/2Bsrl0Wi498quAne44ylbRM7gVk6SK0iSSKI5+//jrabaK\nqMub\r\n=YBAS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB/tK8i4HJg89xCgvCk+bWXIapw5vsUadkomwqgeVpYVAiEA1qd+dEcV8OQNuoAtHP0ePcMO+wrC6UnKs6IkWHHMCEU="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.4.2_1573441786379_0.10361997872216167"}, "_hasShrinkwrap": false}, "0.4.3": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.4.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "a4b46da3a2f3eeeca488f098aaedee2a02825e86", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.4.3", "_nodeVersion": "8.11.1", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-dZisaUJnxZM8VCmsYqCNws8YsULg+nwLSa37lc5ulwp0m+QN9gego/XB3p99hdah3PFErlReovXaqBDrRws4yg==", "shasum": "264eef6452e7bf30b2a4ff218af5e02f19b30bbe", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.4.3.tgz", "fileCount": 23, "unpackedSize": 48737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzJLjCRA9TVsSAnZWagAAjDIP/2idDr8QpGEhl8H/vYR5\naU8G68uHotkg2dm4FrqFGV5azNjAmqA9LZcCALmxmVEjGxwnghaRBxcF7guM\nYYRu0AaQBWpvT2iwFYulg8usl/uk5vStqYUO16OKJnBUHlsuiMclM/68XDio\nxdYJ5Wyhce5Hnp1IoEBwf00WfCKhsEYCSgpSMZRbGOfE/Xv9BvLrN+gPdPn7\n6sEElRG+jPtzhuOCWhO3Pk5wPOMiZD6tbWdKGmwhvehJqIg/+u3SCvMDmOZ7\nFwoGnjqgJEzMgubTUG8dUaFlISJ6bFyWlIJen7QiW1z8pAfFN+A9uZtiV4PE\n9JFO3lgFPdoimqYxoptcC5VP5YPpg7HlGHragr6sIY4rbaOqwX8qZcLjjQ5V\n3fZsfjheT60ny4zWMgRK2pYKBrwnA21sR7b3dlCZPI405kzhhCbKWq5iy6WL\n1cKAeeWAz3xvaPq4e+3cw6fYX2ndez0LfU/0U8Mq7IRjJNNoaD4Skln3/IeP\nthSuUI48Ub03KFPsCz2j9c9PLg/8rWBBaMdXykX5Mtdk86kEmhkhqlUdjfx6\niOL2ZqDrVF22nFRbsLAEkUzF2lOFNM4KySfnDbltjigdgGMOOAwme3zjNHuv\naXAgZrpXyYcFxwQuAF9QgUFCiEHSvzPyimiVp243Co8GHwo/lxlfgPvN+/6/\nOwC7\r\n=zZfW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCw/8JvyY5VdZHNn+iR3LAyY8uxLYhjTteBS9Tzh3KwgIhAJ3pvHeLdlwC3J2pQgEXh9iMOjYKGdT+Dzi24ymtgCTf"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.4.3_1573688035075_0.3057085574804457"}, "_hasShrinkwrap": false}, "0.4.4": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.4.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "9dad7db025396eda3da34101a4474c53e7b22cb9", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.4.4", "_nodeVersion": "8.11.1", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==", "shasum": "5a66cf93d2d0b661d80bf6a44fb65f5c2e4e0a10", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz", "fileCount": 24, "unpackedSize": 48718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzKJmCRA9TVsSAnZWagAALLEP/0gS1tarWhXBdZIv4nvD\nFq5yus8MPBv/QGNS1qAuV/6o4FOGS10M9RN8yrpD058nKmsQlupeOYu3lZI2\nzcpoTkd1+uCSEWKSuYyzry2LNhBykc+gKuEB/ZDvHAwkVfR7+ltklqSFbo0P\nknPd2SuiZ/AQk459qYxoem6D9WlHnlyJPPPCdsdFUb2iyI3UGpfnQLYzbtna\ntRM1Xul4jbo/oLfG6C96f+1OQ7PHJifQ9XNq+qGji6XzR6Mfi2bvSobPs+gS\no9sSEhvu8CTbJ4LC9mLYtjtrS6J5ZOz4L0uixuVP0N/9BjRa5rGpVPO/Z/nH\nb7puA+spDDVKH62Ip1X1Tj88z3PQW/3U8Izg/7QH2NQ20LkGul3D787dohrT\n+wu0y5wM1zSkr41zr/n/l+01DXeh5RHJB+DXiYPYcDivlfI05oc0Bs4cSZhl\n4KAqfBmdalmTjBV0KIettzvqffLMCuP+48eo+bsfO0XSI4I7NphobzpxURbl\n8m4Fw0dHgfPyo8Wgk2FVVwm6+rfFaoRxt1Nt+rlET26hcSxNOFTiVuDpmehM\ndyazs0g4KtknSw9wrNyfVSz6esknOHTRR5XvClAmoAG6vW4Y1b+zMsNRK9aL\ncMPE1hYvn53d7UAaCUH8MqL2GgHVRWv+LNICQVOl+XUPdzOy9qvvy/07qBji\n84yL\r\n=5R0L\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOYTcUKrwzHPNBZzBsHhB1sfxxMnyvXg9xaBj1+qNo+wIhAISiy52DpoSyAsxdnVDpMuZibAPt5hZ9uEwjxu+rqOLF"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "nv", "email": "<EMAIL>"}], "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.4.4_1573692005607_0.9330277761553716"}, "_hasShrinkwrap": false}, "0.5.0": {"name": "cssom", "description": "CSS Object Model implementation and CSS parser", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "version": "0.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "main": "./lib/index.js", "license": "MIT", "gitHead": "1fd2d1dfbbe58d3c38986ee59ec214152bb53bdb", "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "homepage": "https://github.com/NV/CSSOM#readme", "_id": "cssom@0.5.0", "_nodeVersion": "12.16.3", "_npmVersion": "7.18.1", "dist": {"integrity": "sha512-iKuQcq+NdHqlAcwUY0o/HL69XQrUaQdMjmStJ8JFmUaiiQErlhrmuigkg/CU4E2J0IyUKUrMAgl36TvN67MqTw==", "shasum": "d254fa92cd8b6fbd83811b9fbaed34663cc17c36", "tarball": "https://registry.npmjs.org/cssom/-/cssom-0.5.0.tgz", "fileCount": 26, "unpackedSize": 52065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgz50fCRA9TVsSAnZWagAAnigP/RVQm+Q6w3z3WrIVcK7/\npro9nBZguzvqpADwFtlMvPIdzzb6nbyv1OOKsa0iwzstvC3E6pWsFdcUByPg\noEU/aKYf/DJFSRRlGdj6hrm/b1Fh/tMjCdDTT3md0A/Qm7c8AXCxFomSdUMQ\nXFjTZyDmAYsQGCsZhojnlnz+bqMK2ZRa8Wa99dBGMi7lPQN6CsH0v+65DDcZ\nv99IjpjcC3Xp7qleZ5fYsBzoqgDW8nvIAFBo2yvVkmiHqUpTsHFC3mNBdNd3\nXh48FRX1PlxKhNSroUR+CvJDg0vZqKJ3ff//5V2B0CqUQYTyD01Q2JrlNVEP\nBT7LTqaeH0ZTSJxzPeyE8oKodQBG8XTMSZskaxUJR+CWZYTfATzS/iudD8wO\n9KAXrM97hSEes+aqWrxTxC9nqoG0LTRRO1mgKkmGLIMw8G/v/WYOEgT+37rp\nZO2bzTto1FE4If0KwdIvW6DX9L3Ckaj2dtVSgjq0lsOonc/llOQy8+avjTdD\ny3CJVDU3zQifY5NEIm829ZZ7WbbmVoIpH+QqoLF5R0YMtgverWkuauIT+gxx\nXDCgfFUZLT+E38dIebqBXvVgeQectfl2ght4cJJUbrJI4AAZulihKjsWG60Y\nx80ip6c2qpLiTnTfMsC1AIYyNUBWAh30TdAGd6gPLpu3IsG5RbB0FP5JIk+0\nATBc\r\n=Q9bN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSLkUmMX6oTrAyNA6vicqIkOOKfTZzDJeXiCbczqtBgAIhAMojxcoW8caC5Kz05yMGXgkUEN0dBDlka//0F2sGfTa8"}]}, "_npmUser": {"name": "nv", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cssom_0.5.0_1624218911464_0.4215993294285114"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "nv", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T05:08:59.281Z", "created": "2011-10-24T21:58:19.383Z", "0.2.0": "2011-10-24T21:58:20.263Z", "0.2.1": "2011-11-20T12:18:49.773Z", "0.2.2": "2012-01-04T22:33:00.885Z", "0.2.3": "2012-02-18T14:10:14.717Z", "0.2.4": "2012-06-26T14:13:39.119Z", "0.2.5": "2012-07-15T22:21:54.083Z", "0.3.0": "2013-11-09T02:58:33.931Z", "0.3.1": "2016-02-01T00:37:48.624Z", "0.3.2": "2017-01-20T23:52:41.684Z", "0.3.3": "2018-06-28T18:15:42.549Z", "0.3.4": "2018-07-03T07:17:00.381Z", "0.3.5": "2019-02-05T01:48:56.443Z", "0.3.6": "2019-02-05T01:59:04.845Z", "0.3.7": "2019-07-04T02:29:05.209Z", "0.3.8": "2019-07-04T02:33:14.137Z", "0.4.0": "2019-07-04T02:46:50.862Z", "0.4.1": "2019-07-04T03:18:57.815Z", "0.4.2": "2019-11-11T03:09:46.503Z", "0.4.3": "2019-11-13T23:33:55.281Z", "0.4.4": "2019-11-14T00:40:05.800Z", "0.5.0": "2021-06-20T19:55:11.575Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/NV/CSSOM.git"}, "users": {"fgribreau": true, "sjonnet19": true, "m42am": true, "chad3814": true, "pandao": true, "alexbaumgertner": true, "rubiadias": true}, "readme": "# CSSOM\n\nCSSOM.js is a CSS parser written in pure JavaScript. It is also a partial implementation of [CSS Object Model](http://dev.w3.org/csswg/cssom/). \n\n    CSSOM.parse(\"body {color: black}\")\n    -> {\n      cssRules: [\n        {\n          selectorText: \"body\",\n          style: {\n            0: \"color\",\n            color: \"black\",\n            length: 1\n          }\n        }\n      ]\n    }\n\n\n## [Parse<PERSON> demo](http://nv.github.com/CSSOM/docs/parse.html)\n\nWorks well in Google Chrome 6+, Safari 5+, Firefox 3.6+, Opera 10.63+.\nDoesn't work in IE < 9 because of unsupported getters/setters.\n\nTo use CSSOM.js in the browser you might want to build a one-file version that exposes a single `CSSOM` global variable:\n\n    ➤ git clone https://github.com/NV/CSSOM.git\n    ➤ cd CSSOM\n    ➤ node build.js\n    build/CSSOM.js is done\n\nTo use it with Node.js or any other CommonJS loader:\n\n    ➤ npm install cssom\n\n## Don’t use it if...\n\nYou parse CSS to mungle, minify or reformat code like this:\n\n```css\ndiv {\n  background: gray;\n  background: linear-gradient(to bottom, white 0%, black 100%);\n}\n```\n\nThis pattern is often used to give browsers that don’t understand linear gradients a fallback solution (e.g. gray color in the example).\nIn CSSOM, `background: gray` [gets overwritten](http://nv.github.io/CSSOM/docs/parse.html#css=div%20%7B%0A%20%20%20%20%20%20background%3A%20gray%3B%0A%20%20%20%20background%3A%20linear-gradient(to%20bottom%2C%20white%200%25%2C%20black%20100%25)%3B%0A%7D).\nIt does **NOT** get preserved.\n\nIf you do CSS mungling, minification, or image inlining, considere using one of the following:\n\n  * [postcss](https://github.com/postcss/postcss)\n  * [reworkcss/css](https://github.com/reworkcss/css)\n  * [csso](https://github.com/css/csso)\n  * [mensch](https://github.com/brettstimmerman/mensch)\n\n\n## [Tests](http://nv.github.com/CSSOM/spec/)\n\nTo run tests locally:\n\n    ➤ git submodule init\n    ➤ git submodule update\n\n\n## [Who uses CSSOM.js](https://github.com/NV/CSSOM/wiki/Who-uses-CSSOM.js)\n", "homepage": "https://github.com/NV/CSSOM#readme", "keywords": ["CSS", "CSSOM", "parser", "styleSheet"], "bugs": {"url": "https://github.com/NV/CSSOM/issues"}, "license": "MIT", "readmeFilename": "README.mdown"}