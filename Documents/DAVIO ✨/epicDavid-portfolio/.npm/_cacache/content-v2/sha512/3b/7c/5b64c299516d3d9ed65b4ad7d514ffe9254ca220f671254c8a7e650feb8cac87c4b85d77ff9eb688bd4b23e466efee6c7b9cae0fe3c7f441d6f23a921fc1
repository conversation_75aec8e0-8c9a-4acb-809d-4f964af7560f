{"_id": "@babel/plugin-transform-regenerator", "_rev": "130-77ce2a82a1ecb701fa6709eeb6f67d97", "name": "@babel/plugin-transform-regenerator", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "53bc1bac140a1e29aac5828bdc47e7c26d3a733d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.4.tgz", "integrity": "sha512-+HyoLe9jaEq9YP+tPfa/ndjYdTsGiA9OOwDytvuRLw2q5NfsGjcADh5cpLmJiy8SbxFP4DIqmfuS2utb6DQO+g==", "signatures": [{"sig": "MEYCIQCuzRi2pN/V3yVzrqQbxI6RhETps7rSAzi+mSrU1O8bRQIhAM+Yk5cf3GZvBGa5DcCubVfb57ZXrkL7t5E+Icng9e5F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"regenerator-transform": "^0.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.4.tgz_1509388489232_0.8225119637791067", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "6e897607be996a274d9cf5055bedba8a82372721", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.5.tgz", "integrity": "sha512-OS2MLaTab7wmlub6FUhov5zYhXSKvutq4uzPm5fMLD8NZvC5tEOm70vMM/aq/03TBHbGRR5PkhL16SmajHkw4g==", "signatures": [{"sig": "MEUCIQDDbh9lpjDiUeGs6Qp1E2VENIbijhKjUQkkP5UlKkblhgIgUQh3fjvjR0Ao8p4ftp8u4z3ZgM2oRoTL16j2ymhPkAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"regenerator-transform": "^0.11.0"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.5.tgz_1509396987317_0.3936116879340261", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "8c8519d8d1b550d1edc8f8ee99dc0478c3c0d128", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.31.tgz", "integrity": "sha512-mJ2UniwT8y9gMG6Yok2gyuAOtpT73cz+OQ+7iSYc/+i741huODSPhy2p/h0ZAVp+uRWnFO6c5VDmUlQCvAFEJg==", "signatures": [{"sig": "MEYCIQDo8jWeON0Wf05bj6O8bDarHLakx7QinhGf0n6xFWhI4AIhALY90fo4D9/gStSwwPTZJzn5+GWNik6jSX/FjQ2kvZ1b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"regenerator-transform": "^0.11.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.31.tgz_1509739410326_0.1371102724224329", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "b7a3c2feb87bb0f1a813e5092f80a3f43c5cf950", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.32.tgz", "integrity": "sha512-AVZFTyFTOk1E5MC3NnSwVh0rWgVLOK2D+DqLVknaAVEU29ZKPKLVgoGJOwEPTQS99GWlRh3yAZ8jGMQT6ZiDpA==", "signatures": [{"sig": "MEQCICKYz+xPpnFDkEBucg2gBsxz0dGoSsNFTBqVxyTpJt5GAiAR2uzHo0yyBgsUd+5Lc8LB0jO/NOp1z90ylkZ6MZnU6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.11.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.32.tgz_1510493600781_0.811252334387973", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "97a3968ef60bb30aa7726d90fc8e2a7cdf8913e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.33.tgz", "integrity": "sha512-Oe5dG5IjLwub6khRMeIvD6ARTsPfKdHfBUFw8B/nKbAlyvuFax0g8/SXYsAbs2MX1hlroUNT918Bm0zNY1c3qQ==", "signatures": [{"sig": "MEUCIQD6uQXdMKdsB+AZmzHvYqcNL1kZjmXOjOKtKjr2+s+tWQIgf7CubtUPXFSlJigBjeyyPZtl2E72xRnFbcLEOvfO1m0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.1"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.33.tgz_1512138503486_0.13229134818539023", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "71bb288e2897e80dae8ca9b29f1c00e77e3c1c54", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.34.tgz", "integrity": "sha512-r+RFXpfW11wlq9omPaw4ELCBQdTJDdYRoiO45G161xVRUBRVURU+acRe6VhNIhMv+fZGTVi9Y5/tuIZc/0sOIw==", "signatures": [{"sig": "MEQCIGPivMGPwITxcDryPTPqm5L9DiVzT0NAJcKqLqMSUut7AiBrPRdfJ5itsWtukCIr7KGC75odxS4jHkF2TcAqjV7/2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.1"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.34.tgz_1512225564786_0.14048482151702046", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "91d87172445d0238dde257e01de74c63fa92c818", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.35.tgz", "integrity": "sha512-P621u2eXvPyzkVZd6mAw7omaAZLkZoLF66CavxEsW4YTbeO9aDBM/GgrvbaNUrffv9DfvFbLZVbvJ6LAXSEVYg==", "signatures": [{"sig": "MEYCIQCZ7ecvjjLXS9K9VwM9qBuCmA0BTupzlTJblb9ZnOPSDQIhAMaiLTys2l2o+PjmL2BWbX7BXSp/wWVDZdVtND7VOp3z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.2"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.35.tgz_1513288071224_0.6650019793305546", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "23853fa15297143aa4bb2730458d48ec57a01015", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.36.tgz", "integrity": "sha512-Mwo4gfH8r7FaITIkkGx1S9cedxHhx4exkxa794wgTfaQyVEfiqnt1W+B42aWlW34iKedDuvCRuQxsGw55o+MUA==", "signatures": [{"sig": "MEYCIQCpaKKTwCV+B0OYdqnt06hfYaqdmFfZswDsa27JIZvBaAIhAO/hxCw4Tc36swuoNmoW+NENpj5inhY0m1z4w58FwSff", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.2"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.36.tgz_1514228682437_0.9809732132125646", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "820e2afcafd0b71c818acb1e029de8a0bc52c880", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.37.tgz", "integrity": "sha512-Gn4pTdFIheCgjlcX5HB6xAWj6jSEnb/dv+1Wxct6VWEer4eToJFKOO67lZhmGe8Ge1DkLgIlKyI5ut6Xz9m/OA==", "signatures": [{"sig": "MEYCIQDBfPfgvIANbMGJEpB523/r5hpIgL5DJWTb/yS/NgoA0gIhAKAxEh5Ug5g/PEodvWpOGhhC97bSCkuG3Xwd6BzDJ0U2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.3"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.37.tgz_1515427353766_0.6653033951297402", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "b013fcd4956302c8c5d44e50829fa764f6abcfed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.38.tgz", "integrity": "sha512-jbvExdAuxDndPbK3oO54QdkKqMeUAt/XYMW9wovYAWLA5R2loJkMjneZ5iU8VbfNcqVk2baWdgInBBUy6dtoXQ==", "signatures": [{"sig": "MEQCIFUFRFFJ9MXNUd0FMj5obQ0FdHn5EoM1DJIn5QmW3ympAiBQrqV6ygtywVJItyjjz2CS+Mif6kxGNnVVy1330WabdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.3"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.38.tgz_1516206719129_0.10618166741915047", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "6f85c3ac987824fe037fc83dcda75c4d7502344c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.39.tgz", "integrity": "sha512-FA4hKu/EJzzDJFBKgFvaIZfiu0RkYJ/PP0tV9+V//28HSV4Yu2QcOWpqiTZCZzPqn1PAfwP2FRFZYTvI0iU3nQ==", "signatures": [{"sig": "MEUCIQCkBmifkS0xu1XPOvzzt69u5dgQl4IfxywonDWS6Qm5kwIgfg+rlnBi2deJzLOFkvTnD74p2fakRdbpTNMIjZ9iGTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.3"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator-7.0.0-beta.39.tgz_1517344056264_0.6237854985520244", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "f8a89ce89a0fae8e9cdfc2f2768104811517374a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-hFj52wAXbEpXwwfKsMol5Y967D3L8tz46Jin9n/gYPgcNWugvsw6d7g+HknBJ8FzaUESrDruFRkGPXgD+FyjvQ==", "signatures": [{"sig": "MEYCIQDNDtGxUNalUbothn3Pcbv9bi597mwoZT7HDv29WE+SGAIhAMiHjxCHthRIXcA0jVWy2BUagw/HoKboQIcaJtG5yvVi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.40_1518453701432_0.9975679939692628", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "1b376823ea0b3b357b4ff605a256cfa001017608", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-gnIIdjw267a9RrRCIXo4wg0iJmfuAZ0vH6Esssd63/f7DLZQZoBGqtK1dLX3IKzWvJb/woVAX0Q5N4UKX/hBeg==", "signatures": [{"sig": "MEMCHwlXc6c3OmOuDs8/aWvYoOOjJVM9KCwe0TnxVy4jWYsCIHNwsw6IoSb1yYOMd/Qi/hnEfytzM+k5NcPhpX+02B5J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.41_1521044732298_0.8401667987880117", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "af164751340a7e513c53e614c6f1f90279e459ef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-E1s/MBk8ztbXqxbeUvFH26x8vAWq/7qX3UdbB8fKoN3EX2Wg9+yXWyuI50jOhXOq7jfmbOrVe0BWoUOjCOqWPQ==", "signatures": [{"sig": "MEUCIQCRElOFDAa3vILuBLZeRPoKkVbvfnIFJsEm47WEwlG7PQIgcqHIUTHmAw8Y0DjEg2MZtxBoMURTh6aK/7LQ4FqpF98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2317}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.42_1521147008375_0.5141129941176172", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "535020663bcc1d72546d6e01b46546de9d8ba347", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-65HVbZ0XFw0SdaPRuU5ZbHKnnlk6HTndDga4q+fg50xrDg5sNXanjyBXXphe6qOvGDeGJzbkIfsK5X/5AkmAXQ==", "signatures": [{"sig": "MEUCIFk9OuMhsFmiyhb4Eknh0X1P4x0Am/5vOQnGE/78SzcdAiEAmeQTR5mZl/9+EyVPxdxN4UUPEd0Ej1BC6evqw/M5IdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2519}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.43_1522687680150_0.15861585617596585", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "e9a21db8fbedfd99b9e5d04ac405f7440d36b290", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-G2M4SqLMVJK5y3fs0qgGaBscUmEhAXEY25qNtPBgYsGmdl8k0sdBAf2/4s97iLmhU234DqJYSGa4VS38sau0ig==", "signatures": [{"sig": "MEUCIFPRIKWZNAFgKdzk2KMDJAEE7lYuRkP1M2WLpdmvXNgrAiEAoYp2qeqvZjm35ZcsCkGgZ3D7DO2EN8zV3Pu4qC8+bUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.44_1522707582324_0.5076453340190168", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "2a90db73c9b50c5dab0ef3cb107811b249557d1c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-Y+xsPxorOyRwMEPKK+Rfe+eLS45ZF6CifLR74P7/1kLTLqVPeJ4ZMyetLZhJO1iW+jF8HscOCk5fzLw+H36Jeg==", "signatures": [{"sig": "MEQCIExHvqWuIeYfZ49eQ8tGH3munO+qdJX4BgSoDeyKIJdVAiAaULTOWhYleArHUBqhKrtlF+oysqayZBETjydPHhn5BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0OCRA9TVsSAnZWagAAcx4P/jIQSk1Z1gHXf6Xpyj1Z\nvC12otNW7qe1t8MkhvBUnduGo9TPmMsEB4QrcveS2sLxV+apM+U6hqhMTaYI\nATTPZ2cUTWxt1JmJMT/rCBYsdfPgmHRWgHCR9zKmHvnEg78Sk3dwJhT8yvlL\ndMEqhDnGoWhLPjqVR6t0znoGKrkaRQV2j1R1AjYWtzKerSraGC5Cqqke4NMe\nle4CMmxRSVB3gnoZqf1vspIaGtqNC4QkZYHzfiih3moYu0kwI4RiN3DciTbj\n0oF4HH9YJuccYj+lBt6+dWCfiXINYQzfue3+zRjba6rUhlFwM7pA/sbom+VI\nHBPR5lZf1wsfiKhQe40dk8QQCcxf+cYPC9eV9Ldsmm6rGseJqnkLt2IA/kIo\n1fe+4sui3/EGxreObtbDNWJZj8phmaRdpIlMh0p38c2FoQ/pd3CpDmagUwaN\nar+ZE149gR3j5/LL0SO44QWKgYEUV+WkXnnC/ONHs6n+rVe93vADRKloZ9EJ\nEw5wvUDrziglYmVGiqHO8qGP4aQEHMRW2NvyADPT5RO0iulVY++Z3qeffSDh\n+LWR0B3Tc/nKrb+iApEAwOhnpZx92HWhngNxxjz5o1UvVxlp1P9yXx9Bmuj6\nRTFsLDxvRX8HL9A2BF7+zWvVH7lRr1eDuU6cjDhzlcLBxO4TYxXQ431DKZ54\ns/3d\r\n=L/Pb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.45_1524448525419_0.1918356812384674", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "875ceb5b37ec0e898c23b60af760715d9d462b4f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-P6d8ckSjKlbr/1SL1NBO6ieFxSebTiRWd2R8/styUIizJWQlEB0ITQ7l8vv3jXGjJ0mh7lxBTegXejRkTGKKgw==", "signatures": [{"sig": "MEUCIHpABAFDVCHCZxFc53h0nDpzZz/vdLuAM3NUys+Fa7wJAiEAt5XKRt8CX96VZJsHafYuGzbEK0e2d84x9NM80U0ONhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFACRA9TVsSAnZWagAAHWAP/RIg78O3vaSYpZPqO0dZ\nBWZh1KKyRw1vfx1X5Orjkz1zQjJXC3TeyK6NcywHYWKC295ulWUrZFslcozk\noEpQd6BBgOVR3Xo+kQKZ6OdhVP0/BNwJEpPi2uVMGIloRHebZl3ZL1nofmYb\nuAe/xBlt3RW/Dd1i8Q8AMSOoj0lrWAAYKtYzTuPjJQV/+4tLAuY0EWT0C3wW\nyMUK8FTc6eeDz2ntsdkivf85Y1C78f9UfK6a4/hsYHLVPYwR3+r1xtzIONsJ\nx11nWf0TIx8YVBfEjmRoCTSmA0nIVbbAt/H3R1NEncf/qvf3cA2ikWb1kA+3\nc6WQFsYjvFo/hSGe3aEMZGaGranahjYyK1H7wF2MAJRUJHtrrTr1lkOOQgmk\nKtsYuoTiPlCn7H9u3AFLGwE6WGCybcSDvsB5pg7dbUN0M6HqHnx+uMwLyOsP\nrCkd+CrdgfDq8H/MBnheCfKai3Q4Fd3jLRtTy4Df84/HCzKyInnF8UHx/Nwl\nl6hjUFDt7XyPaq8bAzNbHA/SOdT87NwXHvWhh5HIT+GdWnRTV+qiWEEwBolE\nJ+AOnYNaaxUu0KLbZYJ/ND1EUemRxf3++hAMuu8dkub6ObQTu5q1d6DIR+it\n/wodIILzTUswohTBKRcFeQvhqBlRoQIQ0EnbA7J7T8k6Uur7X+/LjI2nuxJa\ny4GW\r\n=JTJb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.46_1524457791742_0.37981053510276097", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "86500e1c404055fb98fc82b73b09bd053cacb516", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-JEPIiJyqYRfjOYUTZguLkb2HTwudReqLyOljpOXnJ/1ymwsiof4D6ul611DGlMxJMZJGQ6TBi59iY9GoJ6j4Iw==", "signatures": [{"sig": "MEYCIQDT54Vc/Us+yd/E3CWrxTHSXOG6NhKRNlPPJ0SHFwka0AIhANkQigfaXn+W3W9wYRSlFxESL338AO4ZWDZ+W5yfgORA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iSwCRA9TVsSAnZWagAA2YkP/RE7o8oBo3n9kQK8Y3Ja\neNbDwBWZT0I2V/giNu+8jiqjwOBMbjwBi5Rg4jeHjqOzIZBTKYGhxRqw5qSm\nWUlmfPR+bDUH9ICEqlbKQNBJRcT/LPmWqRONc+06qFBwK2lqXrxDWqEP6p9U\nPJvHGQhFNeukyB8tZgz8ec4B9X8BmLdw0+IglQLizm2GuL44or7FCpS647g1\nbTJfscKxic+M++PveoMe3U1HZIljtmsMuJyHyN/iSTXnVl4KKYErrRgj042r\nuoEmpt7PpRGLsuIeCk1nq7nVGHf0rgqVw1uP0IIyjvmfc2BJtf1vo42rKDP6\nt3gacaUPpf/Ku1xfXkkN2j/ebP4o6OyFXWngfito/yQk5uVjXY4FmseV8fLP\n+3K1XE0x1GEJbU3yLPzQcNXjbiS5sR4yZ039zGXxaVZW9H6ZctSqQVakxqHk\njXUDQU5ygbGDkV9uVLKU1bKwsVdrIbL5rmXCmjogmNM7HqRBS/d/UAP2D7hy\nGM6oe2KnThbM+3uRgreBy80fga7KsNSRudPzFZybIQQX2P8bpjk9FCX0Oj2M\n7/ldl8FSYNvmWhpoJRx7SCnqtK4mzucO5Kv0kBZXkNbS9n07zdbKjRWBADKp\nJHSYX5CFbKewCvFytejnuGyRWiJCTP4vJhJmfQxkILsg8oQt3BHpL/YPyaR9\nCiLR\r\n=ciff\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.47_1526342831509_0.37235029579614753", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "a5ca16f6f4876d75063ae4df471f2671a416ec11", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-+XG3RFR8RZS0SUgQ+Zc7Xns4TRcvIoLRnkGNEXk00kG5I6i3OxJRROChQHXSL90JOpcXIVcSj54z6gxOS+d85Q==", "signatures": [{"sig": "MEYCIQDm0aoCxcTG2aGaLj4gMMrVriDeh0qR2CJH6YLf87AgBwIhALLeY8yrqmWdzWkkSj/BiZc05f1rFjKY1q/FONPl8Ksz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBw8fCRA9TVsSAnZWagAAjl8P/R0FAxNkc6I/ZxK20qXN\n7S0yhPqTY0da9fjvUFYc/xtHwBiQaQfl55ETrjJGMrI5B4P1Z2AHXRy2G/Yv\nCjzrmdalnSoLthdFFto3kTZIz0QOizVMdYOWgt8aMHq1q4Het+FaYLjS2D1h\n5xyAZSKCCSRvH7Tk1QtZl/2iY84Cwav8Dg5EvBM6DbeL8euMMlCY8gT6i3f6\n4zl8tGPkYdjDzJSoPR2sY8DfKnMMMkN/Zj9Px+ZJfDCbpN7iKCMKw2XNmtUe\nRYPUiKLseMwq6Q6Ya1cm8WXMVo/rySH1ndc7doW5QlipsX8BThC2IPPIHutk\nKSE/9bpH3O1xBy6Taqg2nYck9Dd/7L7BoAbd/CpTfmCOnMc2FllGRc2FwfAc\nx4XVl3RVPL6pQTi4zVNEg02FRIx8YeoibPsT+wUl8ZGQLADgWUiYVdtAGuvp\nWWy1gnthDjdKQS6Z7quDf7OBBtfru29iohdLaKj2r9CqnbPU5P3VHwOoXavQ\n8H25GsJHHOrfdCqvQfj4tezrPmVgExiDr/OQIiYV3790+Z+RQlxcm73O2WQn\n4ehl6MwFGcYqa2oxsPxcsPR3+z85rpsPXjiJKpsQxNIbfF5UPfrYMMWwwahu\nKa5V3v6T76b+SplYdcBEGvn6+IUtMAnfxzm/UzeOmMmQn3AqIrvMRIE4NEQj\ng9pK\r\n=UpiR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.48_1527189278630_0.9824455686569338", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "d4ed7967033f4f5b49363c203503899b8357cae2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-p6Ur2C3CWzutMq7z3aR9CDAk8B0PYLgDeHo6dWtj+Nf0t5YCMK8FjNNgVGXOJtmKWNAiKDGaLCwFPBknB00yHg==", "signatures": [{"sig": "MEYCIQCyBuAS6VHAMFaMFF2UrOy3pf3HCVV4Uh3akLeDfdVZKAIhAJrUDJ6l5QTY+1CS7KhRojZHCcaqbzh54uaiRuYNloxr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMNCRA9TVsSAnZWagAAwAEP/3xozqteo5AULl/NnqmC\nFgX9sLPUxqewUIpsOnOJiuqkuyw6/ojEppQMZhGX3dMnuhZjZRjvzLMV7JSR\n6WcKOBmhkV+3vYkFvz1NCQV4PPrlg2nYFdmilZRCRdHkCHec974Njf48sNRW\ngn/wEwu6ZlVk1CIfSU+dwO2pdypLNM8FCwtrUzE7QRilaZZVj4Fy8AZ6cfS2\naFS8iNQ8p4bYpti7jEJKy4tYlg42jBBf96lIQrzSuMyOaCyuFVxblAxakuq/\nY4++sJJqLvnNVVL8y/Bq5elqF1gU26+3hc7Po+jFd0Xuk/V+nWpSMUY7jCOm\nrm0BoVMB+MeuJC9YITyraV/U66SXc1NXgFQs6UlR//NH7KVgQOiXtR1dlaUH\nBBdd1qNI6neXnieYmUtGNH0OA6DxtNIhYmCjH6tuvO2Qn2Dlw4r+dGs/+6ao\nCNSfwSLD/bVKLY656Lk0gfoBMbEYGpiXetU13IovqrVnHTVcp59O9rMbHJRl\nadVK57kKE1IGfoWvS2cXr0xrA6ILEa+XScpO5gi7/7Z235AW/kMyanxwmIYG\nVQNe5LpxBXqHKYHhy1o+x712PR248cgIOcL5pe43958S9diurI4qigSIpNM4\nebDSZ5MMyiKI11wTEf7aeuUbCRvdFN5TrH3TAxyzGWmiV1zH3Io9dEtyYMr8\nrqkw\r\n=PD+K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "d4ed7967033f4f5b49363c203503899b8357cae2", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "3.10.10", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"regenerator-transform": "^0.12.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.49_1527264012749_0.15292073720026278", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "6f9c571f8aa360d46e813fb847c187a7898b276d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-DXY5ozBosc0XPnehF3vFGIW9IJyeoyoWLsUWDKo8FQAVW2WSZhMZSdCUrx1oeDvnzCrnCwYMUC60iSafHg500w==", "signatures": [{"sig": "MEYCIQDW1CGW8b529ymt7jVxjXVjdEZYLGkUO+LWnkSYOJjkMQIhAMZ6uYqyfczEzvjZRm3QY15Jlm10hNfdyk2jJtuOEx6P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1640}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.12.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.50_1528832802709_0.22648335956845989", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "536f0d599d2753dca0a2be8a65e2c244a7b5612b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-dTvz0ggad7ZxFTMLXxiFhFOC5A2X3AMgKx2uiA1zbt2Z6yiGlhl4YRnQZjNfGN6+eFy6vvd1HmBgGcWGUpPeTQ==", "signatures": [{"sig": "MEUCIBGHvLftmh7UPmc5IlopPW9ti26E4IsmU+4F3I6AvLi4AiEA6Izjtn1Mza6Z2jbltRNjaxF4T8NtWsFqb8gN2Qj4+2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1654}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.12.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.51_1528838347512_0.9079577613086214", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "54ffe4b9d7d0d338b9ad46e1ec99b360a5524c9f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-Ip8VNODBSfq8tcyjlfTmgT8SrPdBRN2yuuxXLiK2bgDSbkr3CiHop7241+bLy2btg9C40hstP48M5dOTmRGTrg==", "signatures": [{"sig": "MEYCIQDXCgkINorUNYIy8CIgwkcmgAFkYGVIK4H/X3hNFTFzJQIhAKb3pEzyua2eyqyetncDK5E0SmwXAzLNktEjb6nuunAK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.52_1530838746268_0.9540000982313908", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "4febbf6084afa0c1c9ec8497de68c0695fe9da0b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-mGWykD0r9/7isJjTMG45kgUb63zWp9Rx1Mrd0tt8928IlNx4V2/1zjB1RqObiBE+ylkmhz6G3ywNmXuXSy9haQ==", "signatures": [{"sig": "MEUCIQDD/MxIeimgQtrt33ybkvcFLbYPdksRztjC5azypFx/eQIgMcSLcPrzm7/DEG8IaWX73g1io++utV1tAcBfRJHqgRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.53_1531316395572_0.2185702039864843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "8b46e192f3bfe096bbbf86e27764e7662e5f9a0f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-Xi2IjcCg7zJxYJnPqZswbqzUGSsY9cNVpT+9BxuGivq98OX2zQ4a9WD+P+Y0rmQsnnsRW7TEClYtLDJi4scuVA==", "signatures": [{"sig": "MEQCIC3j1sMB8DbTHNowwfC7VmVY6wF/pqML6D8my9e5xHMFAiAbMqPClsgxMq6WhVIFFKr5fqgKXSSa0vnwGYH8oFHckQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.54_1531763986267_0.3570178867173248", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "a12ba1376c647cf0b777dea8a7b55fe4665ed1ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-JoF3Dk24H5Bquqt5TU2WuXekhZaAf+Biyi/m0uVvn4nvUzK63fr1i8wU9IhNSnATUCdlSrsC80DAK1QvWhwbuQ==", "signatures": [{"sig": "MEUCIQDRYJ3Nv5NylIjkZdIxkpwHJGxCyiHfzQ+sntmcmOAxsgIge3v8Cc1ZnIhh5ST5NpVgXJd/yjnFHamzhvhERQuWPBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.55_1532815604665_0.7239160469229402", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "03999df0a27e5cc38686d90e83be0190660f3960", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-wSRlSwXSXlpAdAKethZu+JyrnSL1NvLn3VtomlOCqHWhRhjOkjehIBlAe/AmguSn9JTUja0vqBWn1FS8sSnp7Q==", "signatures": [{"sig": "MEQCICt+e/5uLC1x2oVwBpbQAkWCGvdvWA3vhbTrELl95yuUAiBn4KiIo3/e7nRnPx+W6BAguT/sVHLWQ0U0EilaW4+OIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPs+CRA9TVsSAnZWagAAzVkQAI+X/FIkTJA/idE1NuAm\nwrO3LHV2FIxABHjxHq5mu9vDP/5eXxJLTwQXf2qIWMlYAbywvTArumrKkSR1\nuFxJak7pscjppdgUdLGRS7ftfPaml/GfPsauk7acO8WVt2BxYZtN6pWI4n57\nVZ2PYU8ie+9EbyP1zIjcM5IMqjwnTmk6nz6Zshx/mWtzpdOIvAlsH914zeJJ\ngGmVJwMsTmb5i1b5lJrqXg7K4Rlp3LMkq6+zTz04n/wZkLAsxwS1AXf9g0GS\nlsptym7+yC2C6n7Y3mXdvE1xunaHuI6Dd8gr4IOZmQMG+Xv9sJGgLcLKwmBy\nB4uzf1/1Gvd72O76GnqVl6SA4QJ0bLmQZLdkc/X7VEL7PbwleDTie/Dt55aB\nfARbNqWxdCmw1mbBseMCGa0W0/ReosRFyCGLHi+gcacAafOaDfXKvQkWWYJ/\nbZrVlpe/wECPr4SekVJX7XdjIyJelAIRAi+Kei61KitIhDIO6+wDnVZFRzqg\ns21Rwf48OntKdvCUx3TIl/MNuYCQ8QwnZraPCbQBY47KUWh4ChVxJ7GFLKmb\ngKoRnOk1Hy+gRBeZOzAxIX0uzuASTPMNn8WmSvZWscvmGW4OGC5mnS+ekUOg\n9FLcI3FCRKBZohWG2ojgK91ipcd+RrxpcwE18EYG4j0voQS4L4uHi//B+g1C\nGbCg\r\n=RD+a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-beta.56_1533344574413_0.578917691783067", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "9cf87c0a4d09604a22d8acdbefd11bb2ef54bd0a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-oE4960B644T7ACVSm3XWnRrkjzI9N5n5FfCegAYTI7S0eYkMBK/vPBF0fnilmGPufS/67eUKNS2y9CbLNmvMXg==", "signatures": [{"sig": "MEQCIFBYDMVHjLiF4bWRRSH7qtDa4x1Dn11nCKpF1OYOpYeHAiBRljstXF9j3NZJgkHv9za3T4q716Mf/GhvJRf5gjBuFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGQdCRA9TVsSAnZWagAAvVUP/0wUu3824dcmrDkXZ0zR\n0zQoYxRBATcV4Rg8GM7JjygJS92tf+BCT4cDXnvZM3waAsJ75aXpIXyotrg0\nu8QnUAN75ytXknYkTJXWm3kOcYfkbN9VdDDXt04CsMzWWlTV++cxRMP6q1pV\nUeSylZLpqWRK2Yc5Ul21v4wt1Baud2nPoJ4+VwNkTmzMvKo0xK+wCQkfavdX\n4VGW2jUaA3RhyTqedO/FRPGEmbJTwkmityeKea6pu+lG1FooxxRGFNueZv1y\n4TzuGldOQyuJRTTR/skNEs/K9gBa3xqO/u3vw0cxSLXCARVcxrSoBmk4zwCE\nyhGuiXQuKd4WGxzHQ/ZReCpCbv1B0k2nTBtuYh5R8syhcfav0DYNGqTDRYSP\nwrSKFcrQ0AylXPRswvHRp2MX5jiIR8hGGAnHnj9zWUmyESsCVvOPaM9PUYQF\neeeXSdVyR8oRTLT2mrAGvPMZybpVW9owIZnPNzLalOugo6QaJFn0fxt8ooGS\nmt+UM/FCayLx08mEkY/v3p6jfU5BpBLC3B87BILO3R/QG6hN25ikCB4MXO4k\n/wBrHS1bi5ILDaGv9dlGToMQkhU+eMYBLvNG7w96C8dQpuN1QZwjMnsbO5Gg\nym/dMkMC7Zhi10Vjvy7Y15uEn4AcRMO38Kr/mbEpOn98OBkWaEZPw2KCwkbe\nFjP1\r\n=bhzU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-rc.0_1533830172689_0.29358258111607927", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "8c5488ab75b7c9004d8bcf3f48a5814f946b5bb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-a73XZOJGt0Ft8/YbRAUl0Vs1GuPpjB6QVQNYPxWUNXblSiywhkkZxLssHZnao2xTD26kLRfMoXfOtj9FMz5fcw==", "signatures": [{"sig": "MEUCIQDJFLhlCd41v4gKht8LGGEsMQXIFJEZ0mho+jhxeMlMXQIgQhI83Pcezoit7r9un2R/dGUzEgfe9QUW3cN/S5vkCXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ67CRA9TVsSAnZWagAAiugP/0m6HrfMrm7EqCmiGqu3\nkZGZb97bVAq1ZR4WWYIaHnhz5SkKgLAZBZqhPZ26jhABgMpysM7cKxMTpWXl\n5ce0eoxELsqV3pC6dZIW13QcyAzHyApa63HkKhQET+MpKtaEHtBPcRrkFEVl\n6OjM5+VhnS/X7j5ZCbftFg3A/nGG8qbBbZc0Kjv8E5rxokQMApgc0c0uh/r2\nSxTbpsoIaYaITG+65rBPRyrNjgZhUsBZpOTmH/M+tFuUXJ6BgeZDMIWg7a6q\nI7wmqJlJsvzBR3mkCAhI6EYNJG5gn3lBGzRwokzLK+OJkqnGs6KkJjwRWr3E\n5Er+qjC1amhUFDNKZec+XdJH0fGVzaSNo/9J/gdOs3A5OseDbssqOEg8T8N/\n11kfOrZgoDPDgSYYIbhi7e55RwTiLeQkAToGZFKo5IYO+cU74R6Hs+mTsUM2\nNsuxe6e5nkIZ6B87zXOyoueZPjwtHhobe9sAz1B0UnCke+INT1cSM3CfS2CK\nZMf+DcjLnJ1gUCiXAjeur2BkKE5McEqQjYwsbBSaydZZ3+KUEmLUt0dEdt/h\nqe/OHkrT+UaIX2T2tTiB21uJI7jrEOgmmfSAJz1AWjRLephwFPy364fAmcMq\nSgOf1bz5OY6TRBvl/2ZiK2c0xAqblb566YdLKIx3nh5SYBIwObXxXtEzyHmw\nbWpC\r\n=8r9B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-rc.1_1533845178827_0.9392630862734186", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "35c26152b0ddff76d93ca1fcf55417b16111ade8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-t4d/kBjxwLgmLWwI/Kz9CnO4z8rsiQ5dS3EYgGRua4VGkczde6m3jpzZx0+7b6iw+frGI12W27V50D5Ruv5juw==", "signatures": [{"sig": "MEUCIFNzjXBxjNH7a5HbwbUlV/6ihW+/vFATAPuSYoi4icaBAiEAqTJzQ6N0Z/Qq2aiiu1Gm0XmpiM+dlkV+NtAohJBvhY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGZoCRA9TVsSAnZWagAACokQAIvPeLip2YGxv0I6giZh\nfsOR99RFtsO4BEwMXfzOuQJrGtjwopN6w/Z/4r4NJ0r89E/IUyl0WDYxZaIY\n2I+ht3h3yvPd85qlmcKZCSd3v0HDr18+08Pl6JPYJWOdDazbPh0DB78RtIYn\nnGy99+eU9zu0HoW1nZkQWLIiFNrDCqflNsKtD279BOgQKrdQ4+i0mQV+ghnk\n4VGURmxARVa7XcijQnXz44vDNU7vNlDbzdyC8l3hqAl41Jhph6pBykgs/ojZ\n2kXRbx1gig525LMSBE8Hssrm8CCl0G1NnOtEDlZHzbJowOTy4ibettEmcbHh\nseqKM28Iy7xe/c2OTKrhb4pVB9HXfsoDlzFmSms8PtwzYzYVbedM2hhBU1Ex\n9jJReqoqXmQDGf5+N0o6CvzZfOrA3+eXs/3eO5zVDEUErRopcDaPgWTXymAq\njiF1iJ7zFIlaDwVFDWzkBv15Ur1aNxI2D8REbaCJfC7Zu8ufJrZKmP+5tSrL\nl3dzPhhYPwhRDHFvkYYoFUK3y5navLH7jvS8flZebGrDTMxqDDu+8cpXGDDY\nl0VV488jPlxXWjjWgD8MRAbvob+0b92OIfnRfghghdyU8jmRflBl0F/baFkn\nfNsxxIBraLrdrjABvZSc6c8kvpGMlDbC+OnmP96S8EwbX4F7WdjHtHjXk4nr\nPKwk\r\n=0oEU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-rc.2_1534879336273_0.5812438182034965", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "d28f5dae6a2cbb3748abf4b8b89678ea3b1ff029", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-bg4LhaOlk+lIY2wiHdLl/PUQJXjiMgIJOgnxo43PyF/kBEAk2ir/oIeQNGzCCZHf1Pmq41B2Lpm0D+FV5RHc1Q==", "signatures": [{"sig": "MEUCIHvKjDIe1XncZq7xAy+JJztZq6zuIRqR/DsfLKxWmucqAiEA4/gwnCjR3Fen9axDxc9+BpgmsuhD75hPT4wOuTpgI+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkTCRA9TVsSAnZWagAAAf8P/RJhwKB8j2e625FZdz2D\nzuOGmGnONMOvWmBuTMa/4bsgGLRGi8GjVuNzT0kw+VVR81ABw4GcLnxT6iPw\nFXLEnfi5TZQSnxBghhMCRJWLqGl7sofeWBDYO73Ic6uV/3Vu3BUj0pHwQ28o\n26YEmiGpu7b8o36cOcLWbSfmxmvmIY5zVU9V2bgK4S6OtzZ3Rqv8XGul0M10\nHWClzPg4v4C8zKfAv8Jo2e8spZtgpxdqcRaTAUUM0+DbSUPWswKl3jnyqOZL\nomxHnkWsgYjdcj8/UWMQ8HivMmMS/mpjS4IkYeKp/9BKz1y5yalOrrxdhFKv\nh2nYBPf3iDBu0aO+kPvFdh7S2qC5XhL65T+j78kMoUXXFvBKFaGyyiHx+fJM\nlVIy4F+jE/dA7pHT/DjmCddgd+SYnqOGH7ECn92iCD7aILeDaqlvDcL8iT1L\nNtCx87wJgHrXUR8JCvXX/Qc02J/qWOZLFodl5HOc21HnLTrpeZE2GXGIQA0i\nxi7qISA/8j1WfZjXqJLG29//bDbggSAh32tWrrkag/zAdgs3p4RXLoBYRULl\n4lNavHu0QT/EICZBUNQECGk6JYlK7EDSC57H444onevS0JlvZx7P5s2NTcUb\nM1AAnoCe6p0hz9GBjUr1q5OG3fQkvvTfLkw97x8OVriGQVHYQ/8iUF9S+6QF\nhqHQ\r\n=leZX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-rc.3_1535133970739_0.5841045054796679", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "de75faf8657b3af5e58974595076a758627f5553", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-29Ep8XDN5asP6ZaP5VZCFLr0SeyhChV89ED2DAg4JVSRzJI7STywPyi+3ybZG8Ol46BRqGFhXNMJd7E15vPnHg==", "signatures": [{"sig": "MEUCIQC3CFDxGVbylyhTElmaNA+8D1N+niigYpVO7EiNCdUkwQIgI0Q3ef7neUZJCmbAl0zLq6CSTnGPVHVJK6IJy6gvIpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCnnCRA9TVsSAnZWagAACqsP/0jRjLdQThm+WGrebuSt\nW87elkrB2RqaBN8BnKnn+KCNkqR5NVyrLVLZUVoR2u3hHJsnKIfOjcXDsS34\nPBl6ODbQB95pkvs8zhbXtTCNGHsqthAat0EVBrGpvx8SRprCCcURpqeLwFQR\n13A1t7X8DTHnad6SnJOBmADZg/kLuPAGPCBjOy55PxduEqwiRwOnscAddQFd\nD44UK53l7rl668EmI1z+5MEF2/+y0RRWrax7fE5mJjOkc6WUkMcoDe9jKoJ9\nM4LixAkUiE3uUB7B1jlL6lUTtmkyWmQWrjZIGKFzauaM/OFBNXwy5Z8BwcgD\nL/wibVbltO9o+dc9E+knjFUMLEN1pkBtVOHc9Cslqv4weHuBGU3QHoUZvJsH\nD84PWA3Sx9cgAnBkCqwKYUo2PtLGlnzzQ7ZZD4zw5SSkPmZXkAjwZhBfwe3Z\ntiJCFvgRj0C+IvCrvc6Tn+Jc05o2okLh+TsYg/jNkJW0VMFjzuJBR1HCOx1v\nGVe+dk8YYTy0e6oDGRgEU0HAyh/oDe3nIO8HP1sKv0115Z54IZcXsLj9NQlz\n9bFG/a2sE0EmYl+tV7oIyAeXnCe1V+e08MtHGLeg4CW5ovY9Y2UHecuI83c9\nQ7hv3waOqC3CDMYSoj7/9Cq+EatzXfcefTIVT6gW2mIZZuVwbeH1jrsnQjnC\nxUrm\r\n=vv2w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0-rc.4_1535388134330_0.9655420058449897", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "5b41686b4ed40bef874d7ed6a84bdd849c13e0c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-sj2qzsEx8KDVv1QuJc/dEfilkg3RRPvPYx/VnKLtItVQRWt1Wqf5eVCOLZm29CiGFfYYsA3VPjfizTCV0S0Dlw==", "signatures": [{"sig": "MEUCIQDYZr3UryGJbYNop9u3gDFV+3Z5KHYSNKVSM7kMtWXKdwIgLJLARNRQho7gbK2Zubbfdfsn4hww7J1LG6whpOCQaP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHANCRA9TVsSAnZWagAAWBgP/2yEEXhAyWB9eIOhZVBA\n9Wsp6UoJBtsflOxJZFsk1ksG9p8som4MMQYxbGPs2dyh6v4peDbgKw/QeFXZ\nhuSB3GweBLo6SvoE80Rq3hKPiyMVyFODdaquDcH5LAh2/CTryUl8W2zXCpB/\nLpmEdWQmAI9dfnffAnh/8h9mWcJd2cq8D87k2EGq7HzZLbQElwjxna37Hd+P\nzAk6Ib11Ta4qkiysQ7rFDXmLKrY0nfF702bceundc4y0vDnzDNHZRup9jrIy\nZ5GYSPp0HRANCbk6MdQK+lAlXKlS/6cnEjrh6ds8K7QKO8jb8BVWxf27CvFk\nDTE4xtl7qTquFXCVRKgQa4PBmS941/Zp6D7d99VZxt3SbAZoAedk3ypVIsyw\nNO0k01X60jnnNMyirE58agFq01Z3qLB/Byc5BmAJ1Mm4NJ4Nj4qat4GiMNTu\ndFLcbBsQlXA4pE11a44xrf5M/kWLiZjgbUukqbhNtBtQp1H6IVzEyDDP3ghK\nYoUIaKi8yNHO04VmnEGnPmoNNW5B2jkdFbMQID7mB0VdPMRvtIQXwejaeu+Y\nyAFDDdlqGqigFjk6Cp6D5wDIymLHDTiOU4Npqh/JCWYHAfXMpnIAIk91ZEui\nAn4z+5mFqBYJXzuDNfl9PA+/8TRhhpPGeEU0no2M0GNsvBbXEbIBxvvUr9td\nUCWK\r\n=mdRR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.0.0_1535406092943_0.0127925707594152", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.3.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "1601655c362f5b38eead6a52631f5106b29fa46a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.3.4.tgz", "fileCount": 4, "integrity": "sha512-hvJg8EReQvXT6G9H2MvNPXkv9zK36Vxa1+csAVTpE1J3j0zlHplw76uudEbJxgvqZzAq9Yh45FLD4pk5mKRFQA==", "signatures": [{"sig": "MEYCIQCngdyyYY0PE/TO0CwIebjRqbfk72RdH0slcePXjvOJSgIhAJbOppm029UTCDcgxmHeE6kuZnIPfroW4np2s5qkrg9g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDVrCRA9TVsSAnZWagAAnHUP/jEszCZnwnMJFsBo3COh\nwVigAKXG2QQ9JOCt7yMlyH5zR2D0O2NGIAZetsBXjlZmS9L2Xs2AKk9DWOFL\nBEv22X1SFmAoS8D+x6mhjdDBuymK+xWf8XJn5V4MO5OfY76BfwdshzmC1rIb\nYw1BCoSYxRilcp+VKInvxj11iqqD3NaIk2lJN3Y8Ebf30bMYnjHBX77BLeEM\nMe9Pe8GUw7Xq+y+vY2VqBwXYn/zhs0lhf2FnxFGIENVpNYOccpG5pbUE8ETP\nvIOMpaIP+VNRvkhYyhBphNN1kDLCoQ6/zb3SFAlf/Li40Uv7yrM38h6z3hTt\nejBv2sOCQvhshLs7t1P6/fM0ZoItRx8XTOFWcaJPDLlA2XSobMfrDawqS9Hj\ns6N131DiVVKui82L/8WKz06SYbdvOIoAdfxCzHXpjUOb6E/b2WiiDMg14xxg\n4+r26wwlTM43xAboAo5lEnYjMCGHUl7p/8TqQ1M4HGAx4VGW3SiYIR6xesEg\nO5HOLXMGLn/1KRaq6b4cEGDIrwRrzUut+xZJ9c/dsUdCKlLPL9hhPwa9sSIt\nHpBVOhlWfthzmKVpnuZoV2JlKKfnpq/prY7zRa465sjVnNtZ74rw1tAeBs52\nUUr4hjRTkYaYaGpalaGIJ1dq68Iw3cKNm4d1CgkqlEWjLGtZGUxCxXDVElDg\nT/Lf\r\n=D7pN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.3.4_1551119722813_0.10747223972271369", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "0780e27ee458cc3fdbad18294d703e972ae1f6d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-SZ+CgL4F0wm4npojPU6swo/cK4FcbLgxLd4cWpHaNXY/NJ2dpahODCqBbAwb2rDmVszVb3SSjnk9/vik3AYdBw==", "signatures": [{"sig": "MEQCIE+UqQo2NjRtdKV0Woc47Ii3KaoHo3HEUwG6pqjLOIqfAiBX/xfHsukMVr/kDCAzlpUfduvslxglQKbs3GiVMrTZwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVS0CRA9TVsSAnZWagAAXwoQAIAbb1bnabJSd3+16OXz\naW7QuUMeIC7m133uWKfbaveVRwTKHWn+qqteVClEbdCV1/Oybb7tiqERpXJZ\nK6IplH2zFjsBlJRNstZmsxKaNxGt84QrkIJYJBMWJhFzcl4FCXpacgHzzary\nGPU+RjtkiyeN0j94GW1bZI5WoKjvyqrW0oErgDgblLDOABNpgtbKrCjdKY3S\nMQSLFp58++zAN0VSjaK/7XgUl7EpQ9blSV8wQ4h8tP2amW3kYbuyb3I20Nu8\nfFBpqIOsUiyv3WsxwxjeMRk123EFArhQQuTsVOJ5ErOnlF5f65FmqqJOiuMs\nn2GvmeuhEY54nhMq+gby9t0nwjVf92ttd9dcltt97oMvzl7QwnNTcOnY/png\n+jzDwPRb9HCHmBgCJCsbCEKbgFCuGKh4nR57enmDawbnVucSGycbM9DvRKBq\nzDnEFvLMIM6AZ+zkeH7XFYBSNoyDJ/u9nwkLCvfYJ8HkaAmvjIw4xHOsXGtn\nz/o+T/Zn3W+75rxJkybKbTbXEQieKMnMgk6uSCllulMsB974YieISMFgeKek\n85XhLED15ZNWQJ6ShtHCTBfNDa9pdYApHx6oKvB7hwzK6uqhYjxhkbdbaltZ\nzXCcBJaCOCRg6y5FKQA3lShHWFGwca/g4bFH65Rfa8mQxM+uW+HUeTYlfETv\n9FOn\r\n=QFZQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.4.0_1553028275670_0.5320134942682724", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-regenerator", "version": "7.4.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "2a697af96887e2bbf5d303ab0221d139de5e739c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.4.3.tgz", "fileCount": 4, "integrity": "sha512-kEzotPuOpv6/iSlHroCDydPkKYw7tiJGKlmYp6iJn4a6C/+b2FdttlJsLKYxolYHgotTJ5G5UY5h0qey5ka3+A==", "signatures": [{"sig": "MEUCIQDrW8z5Qj6VQhiXwiUYCcx00UFaCKpj9BN7Gp5alze3rgIgKi29LJ7Owy87KRQm596PSwYtwIk1FsyQQwfwhhGHJRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75LCRA9TVsSAnZWagAA5WYP/iOZ0LOwux+mQLFrzwOX\nIAijRyaW0IWv+XoIS5FTuv9y9HkfQn4VftedTSqciqg5VuF2giKZMF6W4vy+\nqVOdEdT2eSEejG7dlq4VWSTgALdj5UUqmhv+Ql3o4fcO2B1HfXv8Rvpl5Iua\nKzts5y0Guo7I3VxiYu4at60iGOHFQXACqe65n03HGwrbKibh95d77KPRZ3Nx\nuY9Ca4rmNoj9bG7rkpioIp+IE9i0DVBqbAbpddb13axjRQojGpMbRtVRF+Cr\nYhzk+OVvsMevapZcltK2ktF6osIz4AwAf5TaSvV2V2BXOQBM0gshu4inV/7k\nPKyFshxZZJUxrMW3iczP5MalJ/9fdI644qJMQB+znzBqptDLbQrmbTjtyqUL\nA6QQM3sd3XLgedpkrE8cqjcl1waN9q6QP6m02SefizQcRIbLFOZzdbmOGURV\nLw53W/rMV4GLV5c6+BKc3P5ERtSdpSnlTw3R3vOZpUVHD1JSCB3eO6hvcMjq\nbV2CN3/W+Yi5R3TBHY29MOTCinuGeTv59HNc+lBEdmFP/FqrUvPPA1/pwA0N\nTTSG1KuBUAb/SivnYrXXoAVXayxUxr3h9QtEXKE/QELdc+HQ1qDVMAEO4yjn\nW4u9B9BOZmZ5ra+YS02FC0MPu6pobOCkkSdYZrc9ERzEFe2p3AIIsZ73I+my\nl6Xn\r\n=4f/Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.4.3_1554234954767_0.3504452654657786", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "5b4da4df79391895fca9e28f99e87e22cfc02072", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-Zz3w+pX1SI0KMIiqshFZkwnVGUhDZzpX2vtPzfJBKQQq8WsP/Xy9DNdELWivxcKOCX/Pywge4SiEaPaLtoDT4g==", "signatures": [{"sig": "MEUCIEaPnKVEr2Gp3lENLLZgpdmH+pq6GksUX4Uuh3SfyTMXAiEAxvvhRmgzsdviqEpNjJmYztHwpf2/axu3TzVIkxRBZ1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JECRA9TVsSAnZWagAAaTAQAIVa2mR+7w7vyKcEJRWz\nugpomh7cQOHvIjJFCN7jsJmOXGnJ2fxaPcmKqJf/TQ9mLiFatOIJ86EKDcPX\nCrmCrtyjCWP7Jx7pmU4IG3m0pWqVBWdsT/iatbR2U+VM40G0f5fDvoUwFvUS\nES+jQrbapzR60k1GQkEdanQcZ8SZGJIiBthLsv/d7zfc5lU+bKYCev83xlFY\nl1Cfgaxq3uoAIyWstVkhgT91HCxXADOg7u29VY+Tt9r47aEP5DzSSoztterF\nr++67PGWONdF5AVDD6R3IE3IRjJTaEZ1gSY9FGERtH7PswoF7GaUIASNKFD5\ng9gju5WUE6XuT2h4dyt54G8ocM1Z72WKfEUDD/3SKQAKKLy2hrTKI6Da6IQb\nATPQhu6UlDm33W9xnnrXenqq1dCfzKWW9AFNf9jZZo8zCBFK9Rnimj4PL3Xn\noacYE0typdfHSl1N4+ZxeDIbpSCIuTw6nm6Kll2FZjBsMG93fa/cRkqWDdqB\nYO/EY8fNol2Y8sOYkdaeXKkltKvsH2HXPF+rkEb0zgxnb9zKtUWZ/7wXlvQ9\nOCS3+ZrVrtvxQtJGeBY01DJjRJWZzfSahqgXUV1qDNg+eUlAv9bEY+9roiBn\n0pO6X/gcpbOzR8CgcIFdpVtGqRzlst0BU4FYtE0Yk9TX/dLTlkOAJ4VvHhNO\nfKIa\r\n=iJGf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.13.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.4.4_1556312643140_0.913604966089244", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.4.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.4.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "629dc82512c55cee01341fb27bdfcb210354680f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.4.5.tgz", "fileCount": 4, "integrity": "sha512-gBKRh5qAaCWntnd09S8QC7r3auLCqq5DI6O0DlfoyDjslSBVqBibrMdsqO+Uhmx3+BlOmE/Kw1HFxmGbv0N9dA==", "signatures": [{"sig": "MEUCIHGAc4PiQk40BgMtYpUeePTDG6dwSG2NXJLJ5nGxiY40AiEApM4vqI8wI2vSsnnLG1zGQpD9M0YbyB3k0fAYLJToFCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5DlACRA9TVsSAnZWagAAlGQP/3RTuRBycYb91Rxe/gxH\nLVxGhqJiP8npFM9IA5pI3fryICtgglX4TBgjmItVMiYFMSMxsIgtN3cQIAz6\ncIo50F4NOumyNGgssGSrYJfnkeFheKzUaWuNjK98WWMao5SmifXKo0ppHCgx\nm5Qfugpx1tGTkevvAxhZrky6gkJ4rSzpoVPTY0jFZmyz6gSxP4lRMfVRROBM\nEFskf47E0mWNk3Xepr1k/8vtaE632OVH6Rgqa1mibZHPo3DBIZOXX02ablZa\njsQEhR+bdK9BgEaQbooNCxSdvKJfI0KZrsCwNJpvezh/+w+auURoPjPWJvhQ\naO69cK3LK2BrHqdfjFLUow266kPGtsAUbKjRgMGxtDmSX6bZ8GQnR6HZveH7\nh2ymM68y3xkTZzjhV1ReqQwU+GSb4qdgNmU5ouShaAlrs25nIDWF4+xCJtJr\nTkwGe2ZBw9SNLvNTZ7HRhZkfooaEpeaeOdnkv+giv2dUcRDhSEkC8c4D9lTp\nYm8hrU9voIIfhoIoo/oGr6uHZImV9MQXCyoGTPEuoBmXI67YBnxuj4LKY48i\n7lGZn2hzGxInHRCyaovUFzry7ksxJ85tIoaWSriLADUWN4qlMy+kJFtLFVUW\njF6ghiMYNwm8hAZ2AHMYPUVga+ZQabE+h6WXakCQNZRULBHv6Cuvpuh7iMBi\naKAe\r\n=/4Eq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "33ab4f166117e2380de3955a0842985f578b01b8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regenerator-transform": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.4.5_1558460735970_0.3060072366655544", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "f1b20b535e7716b622c99e989259d7dd942dd9cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-AXmvnC+0wuj/cFkkS/HFHIojxH3ffSXE+ttulrqWjZZRaUOonfJc60e1wSNT4rV8tIunvu/R3wCp71/tLAa9xg==", "signatures": [{"sig": "MEYCIQCdJeLkSjSTDtDcZCjVZ6ytiRNvi6sA0kJAegHcRyhalAIhAPA2ctjYcA7GEUcE53sbSCU+hQJ0wd/lWl8EVidD/MeY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2719, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSZCRA9TVsSAnZWagAABXcP/jZf+UW36I6h9XjvP+rn\nc5U46zv5Dw+guRZ4EYzs+Wh3IwlHf6DRo92o6cjZBkCT/Vlcn1qGHEtj3uo8\n9DGWGE99kgCNcLvpwXZFTJ7PHkPZlIsql2IZexR86AM+ERMbhlh9knXIGI0w\nZ9O7Xwb+8LwYEB6vQkI4Qe+g6Nl7xohoxMh8zffFy8l//a248WmWVAisFK+N\nt00muaqwubqz/+IEtHYwnPRByh85ZOUGhjV9TBr7LDPsrmPx8BtbUh9fXVUD\nrw0GMznfbFpWV3ZEW21+BH/Y4Z/pO41YTh6c4pxf/nxhZGLUzukzaOQbbyJ5\nWWM5UoA3ojTANhtwoaSNADCEAGITq4svHVGU6mOnKFQwd8EWoCEJkQ+mWY9Y\nBxmtctsktGgtElrBOaDxGiznKBlAiIr3qCuDlwoAmbZejpZ7i36TuWVKhKHc\nMW1yB+vuxnvTBJ0ZuHCoReoSlhl9BfDkTNLNWM3ptddblXfjR+1SOO9XD/sT\n1uccjcC/R5qO62KlaGCidqPtrdm4ajXaep9NhAFddxhfTuZOFSeqg3BsvIqM\nuIqbnh9WttJGYt//yOhPLreSCweUzBFzgYs3qfGgjQovfWq6ZE96lh7K88H+\ngm9BHCkPdZrAEM02j3qEWMxD8FO0/hi8fLPK9EfxjzxdMp/sEUimdJWq5e4t\nGTzf\r\n=BmET\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"regenerator-transform": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.7.0_1572951192662_0.9719039073099096", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "d18eac0312a70152d7d914cbed2dc3999601cfc0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-e7MWl5UJvmPEwFJTwkBlPmqixCtr9yAASBqff4ggXTNicZiwbF8Eefzm6NVgfiBp7JdAGItecnctKTgH44q2Jw==", "signatures": [{"sig": "MEYCIQCKhAPnC0U6C31AqzV9zNa98NqwIC9U4ish8bjPJH+83wIhAOoRceej9XDccySliihyOgLToA9m1y93p75JckehMlfw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HAGCRA9TVsSAnZWagAAgGUP/3duANxqnjCWqFlRhdt4\n9ygEqUPoNGQH/VtTnIylblPu05lkBQxYmbs8F1Gc2FH7BnbmUfJq3v7wFTJm\nSFKjRbUpXd2E28Qo51uUd4AkcdfUDnfivrYw7hQvcdRcQcgvJFEg7kxaWU98\n4zMLiG1KCkcGZJIFg42wvTWSF/7S6LOSwpFb1wxdCGVgp5figtzZ01+naOxD\nefeoDy8K18Ly000Pn4kPJ4bVady4+zEzmPl4x+2MBQqVdy6KdQyR34ZvlLKO\nGKZ+jA4TIcrpxlOZkh80bHTwZmof/bUIk5pAA92Wxhkfj84dedBAgF7sBFhR\nBoo+q9dblq0O4hWJXk4kVdD6NLO6LDEi21LqfL9TrS5/oZHTkHrOwONohDNz\nvisNu5iUr5Q5Ily0DNL6EFg6OsDZk5VG/VRihGK1kbBr5bbfjRg3lZlGbobG\nJ649uu97JdszEK8jYEt2gJqLUiDhuNjq0t9dAxg8KwHjwfj1FgdxZA9//QSJ\n0e9Nv0gPx0BMRWAQkuAvibQznU2NvRkqV6PcJW9kBqCmHbcJLf5IeVmaj9Bg\nbtsdIUfCQu15V+owlV86t+SCw7kcDgXkvQ0PezRUv1iBkOiuk0a5o5EV5IZK\nmWO5lksg+uDOCp8ZwczIJAr2is2+quaVKFBWaOrn4fsZFeISSpvoCfuntlOx\n7GBh\r\n=mOcO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"regenerator-transform": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.7.4_1574465541856_0.05589485411182182", "host": "s3://npm-registry-packages"}}, "7.7.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.7.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.7.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "3a8757ee1a2780f390e89f246065ecf59c26fce9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.7.5.tgz", "fileCount": 4, "integrity": "sha512-/8I8tPvX2FkuEyWbjRCt4qTAgZK0DVy8QRguhA524UH48RfGJy94On2ri+dCuwOpcerPRl9O4ebQkRcVzIaGBw==", "signatures": [{"sig": "MEYCIQDcwA5G93wQhcLZDrmNib6OzZmm9+BpgX/PRTXGUn/UMAIhAPo3IrMFRrpZRmMYgAPctpJLz0hxMyhSGMSCRgFalrDF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6lT9CRA9TVsSAnZWagAALlwQAJK0ybHwrfQktZL9Lvd+\nf7C9pHGb7Zm1nczv+7HsR1qr8yLJn1YbWATaX6+rxHWA/g+IU3x2VBHtuI0R\nugvSnIts/NHfcGBRpBkKiXNwk0N28vmr6YlJbOkJ/qHTPXfUAeEKaECDzluF\ndMw426ybdQs6NPXFwlkk86Qb6XPv4XIfCPmc+nsJ8x75oOgNOryhy5abJHch\nK8l8woGlbLWeLgvgIsKlpNXGkDEUCCzK/kh7KmW79amIPecQSBV6MsnTWGIH\nuoAuBZ1/BzYkcafNDZSsubPnRi9TEu8r/tRMoMva2bxjoAm6hrHW8KtBhAjO\nUIvKrj44bq+++IKoP3tUxbPpjs7oY5kt32EgwkdwF1MaGGQtRJ5FOsrX03yj\nzo+nQgN2SbpZBOLB9tbkFpjThA0bB86YOU+psnrbJ+YTwQa8y6C4chsNjgc5\n+HMMUXa1DsXYyXe5INgqBTarkxe4xY31vB3MU01Vh7uR2r1AtYRSYXLXkjvu\nF29v47+rnRZElrm79j6/Ki/42s4Yhr5HQjotVXBy4RlIfmzJjlxfYqCXd8ZZ\nqkaYuGCEpetZupU5VCbuJSesw10TiWVCQTwHsndfDozCRbWI7UrBkUfXe9PN\n+CbBxW7iBSWGChHQ9mdkVeeMGfHtp5gvYegWqdeCjzI8TKC8iBZG5NOXBTfU\njCtz\r\n=VFPH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d04508e510abc624b3e423ff334eff47f297502a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"regenerator-transform": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.5", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.7.5_1575638269007_0.8262506990708425", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "4a0a40af2f7d60a54a3fd7ce58f06b12ab14eaf9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-n88GT8PZuOHWxqxCJORW3g1QaYzQhHu5sEslxYeQkHVoewfnfuWN37t7YGaRLaNUdaZUlRPXhDcLGT7zBa/u0g==", "signatures": [{"sig": "MEUCIQCQubpNrlawZZD7e3KpxV2F7kUg6lyFHEIh++i9JIw15AIgCQ1kHpBufnX8UTo019zZRxkb6wrlMV5Z2p1D9J/E+ZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmU8CRA9TVsSAnZWagAAiyYP/AxOEn9IiHNDPzQhFWIH\n/5vb7Z7Pf2W3RGTAl705bSq/IYK0grg2s5wYpB97xKPu19ASutFtcUz9avWL\no3ieBBFjx3N7NvUjbf1qV5Hg3OvVCaUuAby6M98MFFFseLD8ApYuigMVCJjR\ndGiwAz8jYUxT3CmJfypJOlmqOiVP7mV3fSgR2Q9YC3qMcdufjdzv3NhjZg5E\nDKvVkmWj2GmBKXN/PGnKtt9hWBWfhG2yaS+bEV3zPzRexbqcrPOUkjfl62Nl\nqHB4U8z88tjy9A/SBaPjeqecmMaWsc2M8FbdDNVvaAsMsUo4AsLfGRoXEdhi\n7T6pE/ERu5wSZ7UHprMP9EtatMbXrvMR7gtBgzlr/yHlzVdHOQps0ffSvmko\nDid3cxynihJI8/ky8NHMbwOxYJpY2hDIVMhbT/60G8BLOS1k5bkEUPNrglyS\nIquLS65QDsaZuCJr/ZymEgqYriLopaBxKz1DWcyRuGwm8clHaJC9bmcvI/GG\n7ts2XpyOYVsKHurVuRLjit5Sfj5QHpcghVtR6LJo9ptnYA6+Z1/SvOcMK1X1\n9RzQCwh6IVQvrmIQVb0596c6TBvlwXQ3n4Bito6xz0kmM7AAENM0XaEHDH1N\nvsE9klmSKrnHhmDLt0vK1xj0WVhvwKARW9Y3eXWBqUQtZuVgmsjyRvVLyjhj\nK6xU\r\n=3wdZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"regenerator-transform": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.8.0_1578788155642_0.8702169256998022", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-regenerator", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "b31031e8059c07495bf23614c97f3d9698bc6ec8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-qt/kcur/FxrQrzFR432FGZznkVAjiyFtCOANjkAKwCbt465L6ZCiUQh2oMYGU3Wo8LRFJxNDFwWn106S5wVUNA==", "signatures": [{"sig": "MEUCIQC/hvl7mZWtGe0h2aqbD2WWUAupcF71hckkzO2z3qyENgIgeFd0oI6w0WPKwLRFXgY4CHbXicDUjkE3tmSl6bDGtaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOPqCRA9TVsSAnZWagAA744P/RUSzH1WlB7w1U72fl1a\nIn8G89CvZNNLxD6G8t1XgV69xik+ChO+2mUMCrmzVLl9cD5mNOmV6kRobLsS\nhPVwbgF6WiSPPCUXjeDAB0fkjct5L+bh9qOKBZP5enDkE+QmsvLJllo6DZgS\nHmPf4TxXJ8CmoPGyRPOqeyJZ2p+LwZgktC3QXA6qJpBcE6OkD8qoHBB3mv0w\nfVDMNmeL6Jv1GzQ4xQbJwI5td6YUpvnMhKgHzT+X0lLhZTuuFSmoMjckrgeq\nADxc5CY/tV5xpOia4NzRFfWlQPVbtTxN/xAvSLkRdK//I0ezpG5qRd5r7AvS\nf+8CzW77oEKQKlCpq3xWXdMx+8UYstVzPeScnOvG6MpHYHorchB0TWZbfWzi\n+aZ7ldjoSM5RGgum9iNb6xoxq0ptjqBK308CSrBVa+6ropkcbrfclPnAWpfe\n+2kwAgvQ3JnFC3p82NHjnoKFNbb8YaUmxa4fCpGVXx3b9dalRAaBJl88nztR\n5G1nhsYwAOnXBiOO1+bg7mPPvVSE+AuL8kJPtRrIctkQ9+hksLqW2bb2TpUh\nkub5uLFO8cs0UJsBAFAI5UX+0SNfAhshdA1oj5FMG5tWAFv5p1KtgP7Oj25g\noc5aJmgh8JQgmlv5x0BbiBTAYOM76blOzcBlON5Rw+96RwMJtJoxYCEoeVDL\naqqe\r\n=DQez\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"regenerator-transform": "^0.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.8.3_1578951657615_0.4827140891390096", "host": "s3://npm-registry-packages"}}, "7.8.7": {"name": "@babel/plugin-transform-regenerator", "version": "7.8.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.8.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "dist": {"shasum": "5e46a0dca2bee1ad8285eb0527e6abc9c37672f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.8.7.tgz", "fileCount": 4, "integrity": "sha512-TIg+gAl4Z0a3WmD3mbYSk+J9ZUH6n/Yc57rtKRnlA/7rcCvpekHXe0CMZHP1gYp7/KLe9GHTuIba0vXmls6drA==", "signatures": [{"sig": "MEQCIFn/W4vuRph+RTmbmQg62HVlheqdViTxJa+1w70JLAkbAiACuyo6CnPd4aMEXvkHHNzWZE8pFRieXM+/qhKC5av3vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFwyCRA9TVsSAnZWagAARdAQAIKYpM2eKP1tjTG6vIO1\nt6ueRriVae4M9xlqFpv792CO3/7NlCYd5Ex3l41a9rWp7zr/gOHmFKSu5/zL\n3++D2l1fgH28dGa4Vngmgvgcq1HDyHbPOmBiqTINzQjklNu1aIeerrp7AaX2\nz7v7a5Iw8c86Uvm3UjAK2S4H6bvNjuZOab0ck6cp9JpVjEhjvnc6QyVLWD8K\ntIb13JmRsWbKxqVs444/EZHzvW3BLdjeHmSdctYiQqld7PGRLY3G+AWm12jo\nGIMqwinC/x7A8HjEM2QcdvPri4D5QrlJbYgI0MZXsT58QQsh3JrdoYVBTR1b\nIzYY2xEOzXxTQtHBf2e6Se8bbE7sQfg0+LJIX3/hAfacyg/qQYygn0IPT/NJ\n/vh4bLmo+x1E9i0ScnyjWlylje0BgpzEs5XXnxbhlPZ6y/iqEr2ec04kirGr\nIm9fki2gp09r8F3jERhmiyez+VhwIEv7IaDcA2XaVpXNx7XtmLZnI6UcCplE\npyCdPTcFYvj8R/5p5Ze3kUtDItYKZPI4+US8J5dDQ+eTK+qztv/C5jU3pi5D\nIyFIMX2uPd/Drg9X+lJ64GV4AoPsS5vCpoPZd8MzjFShfXGLaDzvCdPoVcoA\nZkowdD3i689/7+2qa18QUsoRk6sz7CyhODo+reFHVB4IaCPX/wGrpP0m7sPk\nes6A\r\n=FKUC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.7", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.8.7_1583373361711_0.4165220691406073", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-regenerator", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-regenerator", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "10e175cbe7bdb63cc9b39f9b3f823c5c7c5c5490", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-B3+Y2prScgJ2Bh/2l9LJxKbb8C8kRfsG4AdPT+n7ixBHIxJaIG8bi8tgjxUMege1+WqSJ+7gu1YeoMVO3gPWzw==", "signatures": [{"sig": "MEUCIQC96GDrO2nFlGgJljs2rVjt4UzoCRhQW9+qfICzhHyHKAIgWpEpGh/jLG4i6FavB9gQsqL7aTTOiYe1kbB2QoDOEf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuR7CRA9TVsSAnZWagAAjJQP/AtpjJS5lxPI7Tbsyi4B\n3f2SzrrTjCEnvnSasDzqQuxNvIUOib0czpVkdPrs/oNpgp8m/pkIxphFl2BG\n9XYeKIS6z6e3dMYoWXioqMWAv8yTQP5ENPXg3a+bTTo/O4vTpCBhOdRW/DIT\nvkWTA0AnTmk3/67NW6Y/e0WF+yjnfLDOTNMUfFDaMrXMzNFm4y+6k4SBoUCx\nhFsAgt8FDXqCzfike5hmZB8bAdoH6x8h1j8WGN/WfImqySA6Hm0MCvNtUcS0\npyVN3o3rE4YTGeFmEk5xLgIQ91nnuIkTUUgGm5V4YDivBcPRRHex41EqORpp\nmCZJmx/X6bJWQelb5gXT98hIkJxEjQn2A+mEnkpxUXW+rpZ58zIPJT1FHy9a\n6SMqTiSDVKqLOa2ZFafZ0VNtZ4LCfC+NmiKxRuUXIk7izkEER7NdDpFBqY51\nK6B9bzWABV6L982zqSsah01TrYWvqM5o7pcNSBaEy7muTE8hcSl4nbTbKDOt\ncWCU8PWRknW6fEjjNtPdKKtmIp/XbOfvLpd/WvCdrhGEHRHmDUh+O8mzsz0+\n8SKGkVy4CsOmBi8K9dcuSJ0NmvUZ8DtqZjs3WFAGFuBsDZ5b+KdFsnjJrCpy\nxZUd7Vp+iOY0SohvE342y91rh/4qFynId8HHGlaNFvDq7dmacRmGjFQvdRcF\nWADm\r\n=fPuE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.10.1_1590617211318_0.9902704642343603", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-regenerator", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6ec680f140a5ceefd291c221cb7131f6d7e8cb6d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-H5kNeW0u8mbk0qa1jVIVTeJJL6/TJ81ltD4oyPx0P499DhMJrTmmIFCmJ3QloGpQG8K9symccB7S7SJpCKLwtw==", "signatures": [{"sig": "MEUCIE+QIEAC02ufMXAwQdtukxUXgpKYWMk8rIjOS0yBxnl0AiEAiNPv6AkvjPg83MZhFD/3xmZGAaxnYD7feiv2CJfrxEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SXwCRA9TVsSAnZWagAAG9gP/RH7drLKSarSSpK111Cv\n2rGf7+B7qhwjXmbW7xvjEPWAoWjOJIh+/8cOv5I+GznWKMdDcMLB/guOg+LF\na5ws0+s/q1pFzmeHdPOFImP2v4AgpmlM3sO2MjHugAEK8BpgRQjMUtVQJrd/\n5fYy4bbdKXswXXs4BNIsJhL3recbw06Q6K3i95WyVhFNfG0EyVkITukToG6M\nxp7H51nuF/HB9vGtlLCeYOkXQ6hczS/65wacknMGNodIE+ry0EK/8IfMUF5t\ncNosgSTafNqB/VH3xSenbniGKpL7h9Q0anskH+bgMvrziTlnoJuH0R29z8wn\nSyUBrLwXp9gxM4Cqsmq//Z1iiifcCNj68ZqUjCGYrg2z+o7P7SPnPxcWvix9\nKhuh7OUnn8E2XUnIt+LFDkn6Mtfz6okPWLUPNzAOSIlG44N5hPyNA6PL66Rm\n3RA8Syo5dKhMLJ56IK8JRquPmbtuwwPO/V/0XezEO5NBNKOlPuUbbSUXSGpw\nCZUKLspWY4P0inedfL64+RWjKzfByRr1WzV+RNIg5d8pqYxGayjV4cyLvuoB\nhSNTOjYwRlrhXloKdz0WsY5h3FIPsYKViBtmBZqGZzHUnLj6yc8dvPTi4Ti4\n0/pYjEhFALBeQE0Hgrr3PyRkVNjHWCbgw3mMeLwzqNYuMHEsV+BVybtHChwU\nORpi\r\n=nTQL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.10.3_1592600048110_0.6787361494234625", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2015e59d839074e76838de2159db421966fd8b63", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-3thAHwtor39A7C04XucbMg17RcZ3Qppfxr22wYzZNcVIkPHfpM9J0SO8zuCV6SZa265kxBJSrfKTvDCYqBFXGw==", "signatures": [{"sig": "MEQCIE53f7Pn1D2aFkk+Era2PplCdSQ8g+I28K6xtczizaTIAiBVpg1yO92LO6ni8ibd7C8gxNVY2p+4oq/jJqGG79IuoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zn6CRA9TVsSAnZWagAAo/IQAJ6Sr0KNyUjk5FZgvnfW\nOZfO3IZM8BnXyCLNeP01EV7YVRwSHntQgA7wbpjfeMTqq883k4MojEMv5nJ6\n5YNlOOAZVFGWxA4C85tlfpAwWaehxQnCaNlUU2Qi6fD3pHmu2EYOMUHWPQ7H\n1f2+sqV+0HRh7zIxwZc/D2lnH54GCwTQDWgz3hVBbjN9S5E/GhTiJ29clnmY\nvp9qau38aQZM6aSMgRUJLxBrp7avo7/JU25m//CJixD4l9Zc6OwIcKmc6ikd\nFnYPOWrfkoxGKWBcE3UZZRxjj77aNF5ISZeRoadHTkobHsbBXS+luVa0ta1i\nJoeHtgv5E7FL4Io/RuZBQgRAjtIZtswDkFRtjIQsWSgr8Qq67V9WT2enli70\nZKTtLSAUVMzmgInHB+2vOMWFxqWYtNrrOKC+CcIQm1mhG9bZivkLNVAdVYa8\n6TueThKreil/w6x7hWRzDsVTNypBAInaB8s4zNYROvK9hNvy0tManzjwx76F\n4S8p3S3CdRq1B1lx2ttBHfx6yAMx9GLekIxoMu+lU+ptvozlAT1UAiO1q4Ya\n777DwbpV+WZitnBr0Azo+NtrWWYar2nAq6bmD6DWj3bYitOr3tqQWVQQK13n\nSzHbqko+q04cPfpcZVdQP0fpeQRdctX4eIhunuMbNxMOAwEkRLiCSDuapVYD\n+9ga\r\n=R/Wi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Explode async and generator functions into a state machine.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.10.4_1593522682193_0.5064364373430619", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-regenerator", "version": "7.12.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5f0a28d842f6462281f06a964e88ba8d7ab49753", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-gYrHqs5itw6i4PflFX3OdBPMQdPbF4bj2REIUxlMRUFk0/ZOAIpDFuViuxPjUL7YC8UPnf+XG7/utJvqXdPKng==", "signatures": [{"sig": "MEUCICDb+xVkZz4GlmIpAlPMeU8IsumjwaVyFAGBaHksb5CiAiEAytCpWn92juNIYyW8klkTco59NKMdGcRx6bTZNsmcs7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/bCRA9TVsSAnZWagAA9HgP/R3GWUbLVqkW9zHRnX92\nR3Sifm46LXffdVANfCRJNyoiQugrgdY1plszxY9RNtCKDV3h7YYZtJvSRWYw\n3ffGfTww/LolX8KB9lSPkJr96HVs15W87XhehzjrSMyG9S+PXOpSxkpu9+nn\nPdASjXgvHbFRIF9DSmDCIHlu1lZYb7Eviy7yoVk0ldlJFr5ZhU0sRfXlOi0x\nBl7tZ15SiGLnDXnzP/JXqhfVIOZjzZJAxIxCjvuKn+ZAjsg1CCjGI6gnuVAC\nCc6/pT/Mm4Hc8H34h25fdpl5GkpkTLDifMvyIAOdgTgd3I5hAsc6z03qebfA\nWpDhSKOIoR9RyeeALTyk0ChDCxwi+pk4+AuayinY6hJJdN6tL81CEA9UFGYA\n5zyoCwc2Af+YyLIbQA2mdO2MNv3VJ0VFcl4pxvSfjuYRZGaRkx/D6SyuonJb\ngx1gJuV8A9Y6qF8MkXKu6UhVKwl+t2RPs0gm4M9nik/TPJUsjbZcMQnOvMOm\nJu7iTsbJlX90/ebMwiiquXKQhfbimoilx7SZ0/vfsGJrPfzcs0WTqoaaUrH9\n30AzHMQMF5QJRpk7sSGCm1bdF4MteandSE7RBL+s6wdl4/164yiKmXdaoGfF\nO1gbYs6oUXPiR1TfwBJtT5pxKon11okKjanF2SVDi2G8WNleOQSz0rAoVEbg\nQhzu\r\n=58+o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.12.1_1602801627110_0.7123265476822447", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-regenerator", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "b628bcc9c85260ac1aeb05b45bde25210194a2f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-lxb2ZAvSLyJ2PEe47hoGWPmW22v7CtSl9jW8mingV4H2sEX/JOcrAj2nPuGWi56ERUm2bUpjKzONAuT6HCn2EA==", "signatures": [{"sig": "MEUCIQCHehzup2EJO4jKuZvS4CE2Sa96CHmFysbHpLCOZL7MgAIgaOwY4AtE0SYz2SQOqJ3gpcsCxUJkiBhB/HFwE2T5fMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffiCRA9TVsSAnZWagAAd74P/ijCeMxmchHJOxsXi4ha\nR8rMrejZIv6m5mON87xxfwVziC/xPaRHA7by9K6pSgMF2FSfvQ37ryUtCwJX\nOMmbUFxNL0MIFMCCwlpzHJ1sjQQJjwjdlSPrxtkdeFFW7gXRymrt1uyfmaDk\n+a+GB6dKcEIRM9b0ZsIok/AUasmcyxL8ZXgHanEnV5f80XPGeA02S4VmGrnb\nQ0W0mFKzpJuuQ7awVDqrNo6EdmSpGOnWrxmWKRbYOhm1A53HlZObn92njHr1\n/SfcfhnFY40Gbi4Tj6M3NHJiXE3bdONB/woFx/uYiwie3ciac8iwFQ3moyFh\nn5amp4pxhBVK8Fz4SSClO0Z74jLNoUZW73JHQMaGETgTKPOgi6eTVnFYEC97\nidYRZ5hP3rB5z0ZaPxTr1837J3ZlH1XVZRvTY15+TzPW4x5fV/CNRL+2AiuC\nA0zbjog+c3dvqtvFdKUqs2+s72OviDkmeAHeJhmr4Ivhg/TXjjvC0T2JnaKy\nqyM6zIMgH1Pwp/D7bU7pcWs/tWJ8l9pi6eYYv7tDLNFzNHh7GimoBRqPvjFB\n9nXsRRUbu3UAolbF9fG5FI7sVVgQGBMndDo4o554oPtBx9UVZAwIYY8J+bRS\nFrtgfb0YUeP8igu7/hT33L77FLddXIUl7JOtQelrqCdyM2K8xFBFXER+AJuu\nDEvn\r\n=QMc3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.12.13_1612314593622_0.608065859470101", "host": "s3://npm-registry-packages"}}, "7.13.15": {"name": "@babel/plugin-transform-regenerator", "version": "7.13.15", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.13.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "e5eb28945bf8b6563e7f818945f966a8d2997f39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.13.15.tgz", "fileCount": 4, "integrity": "sha512-Bk9cOLSz8DiurcMETZ8E2YtIVJbFCPGW28DJWUakmyVWtQSm6Wsf0p4B4BfEr/eL2Nkhe/CICiUiMOCi1TPhuQ==", "signatures": [{"sig": "MEYCIQDHmiH+ZR9r2UpqR5XYMS/YwTSxZ0JuUOydhQRvUL5xSwIhAOBksxCCKq8CRZ8/Wtjnm34L3HrO0bd2J1FZ2UARRjcK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbyY/CRA9TVsSAnZWagAAv28P/0/4ItxeORTq9r4tAX3j\n8eCziHoioY5kh8nalaKq5iLNT4PKFJgz1L2qggO+PyvlNNUXaNFFDyYsSnI+\nKR8m3hB5O8Swi2YMSr/sr5/LXjW65+32zPbTMBwj+NNgp5OZG+NWwLY/0s73\nOX9/RWPMXg7PSSz5XB7vWQX8sDTnBfhUs2MaGRsGoX25rHAWl4DDbi67xd5w\nqSTmN/CJgTbEbo44bdJ7rXRZ1UnMbVR0ZYX67k9FGwFuu1xRoRe9wucgXy57\nNnG256scHHIjus5WJr7k6xyZdzHB+VjWqnE27kf9wScH9/ShrVxhc2jUScFn\nWLKSO5xFLkqU8xxytJevyJ7uPZwDhggin011465J7Fny2ZQPO6HpvlJXXKc0\nYh3aVBQaLTBp5zbLq9kd5IRFLzjr+60QX6m8SyTXfOf9g8/qiBbJ3PRtPgmw\nFzUaj+AXhTbRfhQ74aGsCbO4H3Nj5sNu0e5f1ssgnhhD9m2r7tNZo1gLwcnX\nYJt5bDwx2snxzSIBxO86TkOR9+uiwae7zNj2R14RS2mtJD9TF6s6EBW5dUr4\ne44gNugG0tRBHS0prh9t+LnIM2Vdrpk5dwJxrgKd8P19t0fbvRy9wBAXQ7hR\nXupwwHd/JqoubwcHTa49nj7DunvfdN+FrneodqArlNGdOe6sIf2E0e/36qYM\ndwSn\r\n=OEaK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.15", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.13.15_1617897022777_0.20415275717390857", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "9676fd5707ed28f522727c5b3c0aa8544440b04f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-NVIY1W3ITDP5xQl50NgTKlZ0GrotKtLna08/uGY6ErQt6VEQZXla86x/CTddm5gZdcr+5GSsvMeTmWA5Ii6pkg==", "signatures": [{"sig": "MEYCIQCIFrtnUoHuodSiXHCuusCsa/of/Zn0W6eNTB7/pWBbawIhAJV07kAnoNG2qh/ZONe87RH1oxtCTl95WZnKZAgvk+/U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqaCRA9TVsSAnZWagAA9jUP/i+oqaSWcXDMe8DOEvK7\nkDJ+ElEOmiBVndnj3s36q1MWXneNZoj9VKgW8J5/XjdRKdddLscPwtjKjX+z\njvlmcZhIIF2sQYv8D3QQU5cgyVdBDDEwsMosuAAG7G4KNmkYllpHI38BD4b3\n6I/4U7VMEthXxoxcjxSiQj6p0DI9UYdo6oOUuFhrBvWX4RC1DfHIj/XQmeFa\nI9TwCuCshfWi6yt2LPmYGmAN2bd13y9H7eRmCYIV7JzE/PSsjyzEUo92O93Z\nJ4A8d30UPRczTFRQ5lMcas7GObfWsh8cxJ/CuFRY/Y9wX5DOehh64y2OOgQe\nwwMywDPlaicjW+KOgAyZbveZy4OV5kHdKdFEEa/5Aao9kNmT92/U3ivoujkB\n1PwY+hN5N5n0E7KKyyq94/65rLXTPGnvDfcd8c3DCacZ5j+1oVj57WPi6Egm\nYu825kDzYGpXBI7ki7erCDPdferxRIw2zuLa4gxhcTbE5KkKe3M9FG02n8qK\nQpvHvMANxxR/j9axeQhV2k/oigSAF+cNAHJOr16gZcBXvLRK6NM47kFI9fnW\nnppuPFwi2927ExlCC7+ARmRYgXSAQ72Hxl/N5gZYmFPs/EtONL9PL4XTApT9\nbWrUZrd2lEp/94ZfYcjhxH/z994i/FlzVpmQKlFjv7ji9ep+4pBYJMRGQAZn\njvPe\r\n=DmOW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.14.5_1623280281967_0.262142556682875", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "eaee422c84b0232d03aea7db99c97deeaf6125a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-JAvGxgKuwS2PihiSFaDrp94XOzzTUeDeOQlcKzVAyaPap7BnZXK/lvMDiubkPTdotPKOIZq9xWXWnggUMYiExg==", "signatures": [{"sig": "MEYCIQC+zM3RbisJI5WuaodpqfPVesBN+6XfdqHy6JiqnOoPzAIhAJWl3VhOPcqYGL/Jfh7+rJodvKhl9M8rzCqvc6ix7wuH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2578}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.16.0_1635551253648_0.8977098542562205", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "704cc6d8dd3dd4758267621ab7b36375238cef13", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-2z+it2eVWU8TtQQRauvGUqZwLy4+7rTfo6wO4npr+fvvN1SW30ZF3O/ZRCNmTuu4F5MIP8OJhXAhRV5QMJOuYg==", "signatures": [{"sig": "MEUCIFjm79Zu0daossGX5C3VwjejBBrsuulQkWcfksdEeeNCAiEAlMAwTlagk4oyrWBtG8X0tc9Cw3OLpIJoXBuY5tdZw9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jVCRA9TVsSAnZWagAAdRQP/iRhCMeBcFzgU8MiurVr\nRhNVtO2w7qSDl8ITVM27ehLhGT35qIbtmqkbDP4EGBEhzd3j7uMWywydyv5S\nVQCWIZ6sQUInmRxo0Wy1YQT5Uqj4NDBY7s/YEqXDovC6nItAvjRBs/J0d4tz\n5WVF8UgqvWbjm+X1tYuPkMCa9OUvEWu2kKA+bWdL6EBWH+fvOb5HS869oEw2\n23xKnKxC0kryeEYy8Hez9qHMjYnjt8oCsIKiMMY+kN4pD5nN3kQbYkmbFcyd\n14fKUvync66HUESxxbHvEV1ATODpgsPfDf87IwcJPkifyXalC/jFTp5NskTi\nDmDT3MxYsoIYBnub1jzCFF0Ra1V3wyuVxmP9tkTU1yMmi9ydtaN9Q2vbyNwP\nSWMfoanOecY+h6qk5cgfVONeY/wKMd9OunfxIVYo+mJ/dURJu6NBF1JtmUvt\n68EUtZ29t0paC6JPIH1nyo/RyKBnMpU7J7//mdx+YEQzBBQg6D+XAJBLKI4u\n1nHRtdWtcnTqPlyGo/G1N61FJYKtMKavzegteBSzGzt3pPIM5j/iS9jnVXJA\n8m6Qqx2Pe+Gl/HtLhblEZb+Ns49kkZVe30M8foWw23P3AXBM3lI9hl+Mydj0\nYSp4Tn5mUSD7rIiWbt9OO49W+3erC2dkQp8wKtofXr2/I8SOsQNM561nGYL+\n0Stl\r\n=oalH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.16.5_1639434453803_0.19504349734926363", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-regenerator", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "9e7576dc476cb89ccc5096fff7af659243b4adeb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-mF7jOgGYCkSJagJ6XCujSQg+6xC1M77/03K2oBmVJWoFGNUtnVJO4WHKJk3dnPC8HCcj4xBQP1Egm8DWh3Pb3Q==", "signatures": [{"sig": "MEYCIQCSYRjlwQQ1uTSRg7WrYDEGgBFSEg1pO2EQEl/ymQAY8QIhANI1DKeSMdsRzJ5S9hTpmdE05xBYL8a2iLRKyFHUdhsU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0DCRA9TVsSAnZWagAAEdEP/0E1SlACf+9SBMoCZWy0\n5GwLzKHaND4qpWr6958QoIVYPgrsLbxscvlqyi2KIRLMRmP+vj/xUEaAxQgL\ntW1lRPQJaKye0/GTQTA9+qD9lhXZJSLBnh81Is1ROWGTFXTLGz/Umh88uLS0\njxHFCFSq2iWzuJsoU+aNhf4CVC7Aq5TmzL9Cud4gE8rXbHjwpUKlWJZouXSc\nKT8qJgMklD+6ewifafJsJf4CdmOl316Ray0dXtEVpSCXTC10tN3Z8eZGWdnK\nXf8wwLMhw+zIDn5ne/+hsEiyx4E8ecDBu+YzrzdqteGp+dbKY8ohks6hD9Xt\nbQl436auBIK+MWPd66uMBXGyEUrY5SJBXu+Pbzuz8dAOnqAU9BWZFBhJ1wmZ\n+uOmByibFtx1XPzU9U7mYtHATW7u5xueLtzY1AGumHvR6sfu1UskgeOJqVL0\nPAORSLt0zbhaCtpw2laowyixIuL6PYd0E1YAcEJ0QEc5DkQGHC5lL+WQH7KR\nQh7w4Ry4XcvNGAZG04rbt/nRMmXOfIqKVCNtajC1EAoZw6q2uiweO3CXm2Ik\n7aJSXXqiy6xjOQx+mgkelH0Aoccp/3eMykhDPhKHTTKHUh6jZS3u43GAkDU3\nm5TT63Wv+3El8nk5C6xsybkADg9c+sHwxxglFI5Qg2M5BSRK9WGDnlG6sgS9\noAl1\r\n=mNF3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.16.7_1640910083312_0.1840254419400673", "host": "s3://npm-registry-packages"}}, "7.17.9": {"name": "@babel/plugin-transform-regenerator", "version": "7.17.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.17.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "0a33c3a61cf47f45ed3232903683a0afd2d3460c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.17.9.tgz", "fileCount": 4, "integrity": "sha512-Lc2TfbxR1HOyn/c6b4Y/b6NHoTb67n/IoWLxTu4kC7h4KQnWlhCq2S8Tx0t2SVvv5Uu87Hs+6JEJ5kt2tYGylQ==", "signatures": [{"sig": "MEQCIFNlGHgmB1TrlCC+7T/E0+QcWV9d3D/yIcNTGwcpPrHKAiAwQEN4AMkEC4D8v/Dzca/Ecc7WRjy/aVUX9wmbRU7R+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTbfpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpS4A/+O5bTZuWdmRtN0LOkbhbDfd7x5j0R/Cy7vJFukvWudBvM6tIP\r\n2hhaY3ca0zVQIPsAHFpYiGmfvkUK2BV2I7nPgS71FfFKhmNWpsHjFxIkVkKs\r\nIQqK4tuBQHXzmQBet8mMJFC+dbgQir/XAxmtNI840vvg5UOXUdZ0L6lRMQ1b\r\naNWjgV2QUJXG4S1XFVOu+vFow6jdZq/BHJ10eiY1pf2TLjtZ3CUEfUciWuEW\r\n4M23H70B6ULDYHm+UhkPLXyfHmMkF+q9LSqtp+eZ0i129sLsVoFfCQI4HqNu\r\nbTAEv1WQztEnr/2H8KZaODfZPvAqtddwDTzL2ewPnr0x8MNfcVB92Do4MyAS\r\nzDpzv9JzXjiVPPkbckV92AwksmGvB6Hd4YzPuhrU6t1tzZl5UkP2DK1WyJI+\r\nAVQuay33T8yFSc/QP3HotWUE0mQBCw5FcYYbVSMTIwfPkjAFhGBpASpYBYbQ\r\n0Y5L56F10nkygL5tzzr/ByJ9Gw+s6uw1m0OLT5Sqhpy51QIEcmWJ6uqUEFNJ\r\ncdoJwGrnln+EojmzH/nQB38bZF+jV9K6wo8q1Pha82TiIqu39Y8K6G7BsGL+\r\naXfFByAoJ8XOhjac7dQbo//gJplb8DDXqrwBmz7IMJ8KYPew/MP70xeASc4m\r\nEOhpvpaiZW2WvZmJIpoADju/E024vOhYTfU=\r\n=iRmk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.9", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.17.9_1649260521623_0.16657030421369168", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.18.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "44274d655eb3f1af3f3a574ba819d3f48caf99d5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.0.tgz", "fileCount": 4, "integrity": "sha512-C8YdRw9uzx25HSIzwA7EM7YP0FhCe5wNvJbZzjVNHHPGVcDJ3Aie+qGYYdS1oVQgn+B3eAIJbWFLrJ4Jipv7nw==", "signatures": [{"sig": "MEUCIQDVvUDJXsgIWl3Sv6kBfCwaBJcCLhStoJTts7nOoqm7UgIgVvnDwQZfjEQcjjzqev5h8wg3XIE7AicwD9UW73DVlWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqmfg//eYEHpegFLlEet2f1lcrOdeg3fkr+oi4EKUE1OPh6Pi2G10be\r\n6jjzWiGa6sdQjGigwEA+ztUxmIi3j7juALQwKKY2hQvycRf+03A58wFEH42H\r\nqTzVn4fcsSeYxbYWi658zjdDSsiZo8AwGuLSmR4gUNRxA1zgVb7H3KmHANJW\r\n0tkeTv+svsH53vBDfiAmKqbOP0jXoM/KlJ0iOQ5ztEz/VprM7yfaInx0t6uu\r\nCw97cTLPAzJhqZ1ePSHwUzymFiNrRmIxuF4gUZ4z9FHJcWQsr7WRKNk3CZi/\r\nGE+Hy6XgIv9Hqnxfo5ALqyEYKRh8zC6b5Og9PUNAmDoZaImJd/79Vcwm5FAK\r\nOAy+eBDI5zqQLHW687SLSqcScGvRdje1TIuJOPWju0IQgfmihI6R/+iU0UnM\r\n/rpXkOlv3L9kJ2wbSde4QQi7lJhkl2pPNRUw5BK67aiIe07Cgwt7r4sDcxhf\r\nd7mWgF1q1X9AIgsI72Wg49o44eltvmV9BT5XPD0MCdma4KUIJLcWqdzylIuD\r\njda/TDZwWKADeYYkQbTseZvsw/gN0/F/psMtmJnlAF/oUG5Lj1yr1kM3acgV\r\ndv3NrWk0GqkRJSHh3J34UvUq7M8Htj/Pa5HwUXOJjW9Zam8RD3C5GM2pJfl6\r\n+4NoAehINiu/Y5xP1WJdxm0NneSX4tD4l1w=\r\n=SPMB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.0", "@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.18.0_1652984194217_0.725501285738438", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-regenerator", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "585c66cb84d4b4bf72519a34cfce761b8676ca73", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==", "signatures": [{"sig": "MEQCIBP300zd+YUOK3FHT381Z+T7745PPE5GBMGyDqiUwYoHAiBAUnnMvskqfJRAf7F7Ob2zv2tj9/C1fnBYEbjrIsLA1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXTQ//eCJrn7r/qtPIg32a3SY8Pe0OLJZqhtBy2hjdgT+3OnFdy3DC\r\n4Awt2Qmv2NiAq8wtjLR031TG7eNj64FnUXmgrnmkGivHOA4fDxK+QfeYamvK\r\n4P1YkNq3iKMa06pWFUzGixnxx71PbZqOnNVW3E4NRxnHZqrtWOsqOvwTZaGw\r\n8LpCPM3PWok1ylaQixLQUY1bF1X2kU/7E2BTvgOAqDZ2ciZBedB7mst1LjUB\r\nIIHAaDwjsdhwJ+staMhATSJp9VrrnWv5fjl5RZzra06XdvY7YrHEIpULuLhc\r\nxKGT1ZY6iSc8VNxd/C9dN14VJKT4bqWy4PFPhzpoZiycXH8TL5GwavyQFTaj\r\nJUu8VG7sdtPix+ehpxhtCto7yVlmmGayJtNFp+htKcir6sMuT1D18HeJ6Opc\r\n2Vi82UjjOR6XdgnISIgun3g8bWe/gNiRbreOo/NT9zA8SiQAPMdRjUkLO/yM\r\nk7ZOZWaliBPVwoquM4FQnksxWNK6WL8cpobTytGXobK7tfIPprYEKQoX3U/W\r\nm+JNR2zsd5qEKknhqnsCMzvpneFbQD+mPac6w4KLajPGILVJ41NtuTvyK1p0\r\nDMetbQk3EKJL5r3bU3QUlHzRVm+APTMtTEsxFHOpwhyay+H/takOD4UI08ye\r\nuxpKv4rVLdNVb18bV+dPlgHWgAIpR0uUnzM=\r\n=ylSf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.0", "@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.18.6_1656359403518_0.382288589914056", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.20.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "57cda588c7ffb7f4f8483cc83bdcea02a907f04d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.20.5.tgz", "fileCount": 5, "integrity": "sha512-kW/oO7HPBtntbsahzQ0qSE3tFvkFwnbozz3NWFhLGqH75vLEg+sCGngLlhVkePlCs3Jv0dBBHDzCHxNiFAQKCQ==", "signatures": [{"sig": "MEUCIQDlaHluNUVzmA4ez7wKH8fnRgwiAYzSyg8ITCrxS1znGQIgJWT0clLeyNLPy/kDB37MPc3yFu0gHL90hEHlhdUgqng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkPg//cCq6Vmy+Aw3XIx7rFLIHQCCSt3W4ucZ4Yr/ULkoQY3gUtZCa\r\nb07n4UTZ4du/6SeaUp5M947yxHNZv2RX8q34ZjJAzCdidxDcni+tErn/Nn4d\r\nsv4psaHuTQ/wXs2mw3wWS67v93OSBIXTUiIJqR4+QMJUh5gLhJ+sQ0xbhLlz\r\nGR3MwCfwcCzsEr0wLIeGP/yHlep+atiePBgmCGZOiUzcsfRcLvWKiCUGt1Gz\r\nl/TPhVAk8G1QN8E+BR3U+1inCj+KCeL/fIvMorLc6d35KLyoOEqLlfLCYhYB\r\nETydLTxXlR1x7mDLuYR4xfeKGinBje18o0a06JN4JS5yYP2hS39ZwMg29hRc\r\nzgwzOj85jckH3+GXJnK9bQH3CkQmr7jzNDHvs7ZICDxOvZK4qvmclZfSycHt\r\n1M/hMvdqUYRzTNGubQ+ZfaJBRMEDoh0OzBTB17fPu/LKODs5K0LZIQkk8+NE\r\nGiDMxJNZLYuLHmGRrBCWst3hiu73GBfNd7WRxXPI/z+lvr5yVs8pXkeAP5tW\r\ns0Kr34f/1LWSXcK32ZPa++qdsa9hiu2omHGO4ZMGaEOf81FaiLc8fr9XCZz+\r\nmrqIBJnf/SRsiOfnRcksaM/ZPzwm/rYWoLn42yNJkT62kW/Kx4v9KOF6KYa6\r\nXu9zWO1mjVwR887MVaoMQJfVMO47m7+oazA=\r\n=vNSV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.20.5_1669630363629_0.9299056851104466", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-regenerator", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "50eaa1fdaf4a0a3321ee4c7fe31fcf88b0cbcee2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-QdrOezgh96WGps8MLxyNr/P/0HyY0P1qpsWsV+raZaYInFXdI3Xpn2lwjy10vWMPUAapHMsaC/AInoy9BjwHxA==", "signatures": [{"sig": "MEYCIQCyvUbtr9rzCNV5YjjRzx/1x9SBGfhc2h8mZ6LEm4dQUgIhAMO1z1eCPvWGmddqr7lO2pOaX+NCpJ9vb9H23dn1qC3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqq+g//c/ixBU68QmS1GJkBGcZBd9zhEEJUsmnp4qhYmAQlQAfYULKv\r\n7xgFY4/VacAg23vMxm8JDVLyF2Yz+nVXttd2Cdvq2EbgKbB05E+z2SUwneV7\r\nxlGJWQIWwTE/Z58qHUbiRoWJQ2TzGI5j2j+C4XZ8PoyyWvPD3YKP2IABRUUn\r\n29o3vQMnwhR1C1ADiGwUFInvSudsPySEgSYsnYm7xsp/M+x487pCOp3q2L3r\r\n+aK9Ke3/P7zwrlQmCmZxy/MCL3muq2wSLBNZx+WJPLVVRIxWxLzcXoCkvPlm\r\nEJbjhBL9eFuuuBBHttK2Rz8pPbruWD4VhWrg5Eahp7wxwh9SGgC9gzVrhqZk\r\nxssKR3PmD4WJ852GUG6ZeSu89SpaQtadVBvzMjGbHCsrhRlQasWSvNvY6bD1\r\nAqX3BqMc/F49xhEqt8ZX5Yu7xavl3JW8Eh5tEIHMBjk6oAyd828tf92ngKQA\r\nYpUjC4elTC/qlCnkQ3ttwxc/6DfHuXw8uGN8/JFya07oQucaV6yqCK64+SdX\r\nLSTwz15df/IGn8rjDfJi44DZftbDb0WXz6aGHSohPDFkTWdQutiRdR4obLQB\r\nEjEkVUx1U59xBLMxpHJpX3bPZ2LwOUk474Hwic1vvBPwJalSbSwK71ahfTjZ\r\n6o6OTJvEhVNPahxroqLcdE+AY2dUNmVbric=\r\n=QzYO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.21.4-esm_1680617368989_0.71664160224302", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-regenerator", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "2223c29b6e2a9eac4aaffe2c0ccf3659f7a25495", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-42AyuHo2nvG0HBMTiJmEBS9GabuzKDFiV0OE1I30se06z6Vx5gON9p2o71E6bl5zEMXAiMOc5EuTVN8rfbNSEg==", "signatures": [{"sig": "MEUCIAHj+5Z8cXF/VaAfXvu5dmZZJZPIG8z09q249q5BJdf3AiEAg0Hbu40hNLIZoAP9L67wh1NgqZNtdZ0bSVY1b+jfbls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBEw//RPXxvu2oTWe827MUCvKZtal1pY6bp3FKU5rHsrfe8QTIB3JH\r\nfRaTbf5dR+yl3O8m1jP70awfcQw61X/9L0H2qbWJIF4tZ3j9tHAkKZbR9Uy7\r\n2Fi3hXs0sWbHs7euluvgAW56ACK4GSg1+fJQCyhdE/SGb2QXzohyc/YJ57YL\r\nU/M6K6IsXRKlf5Sc/+Qrb89G3PcfHc7BYBnPN/2oTRPJJjTX/tUn1I+/rRSP\r\nxqja6W6inJTElKLsqMlLZtsx++NSqwtb9PE7jC5rGOANFhI9F3nY+gYUyzhF\r\nswe11HvibVT20+EEV5SxGshYSzAM6pinBtZop1K69hW17w36g0Jbh5uIP0SW\r\nW0gLcJWvyN+ZA7VPzBwF8N1502MtILu/6KMIylvLQ47ZyECxXHgDZg94vOzZ\r\nUJ8JqzDRy371mCbKCTuXaLoCpQ2H/fNYN1dp0dySzL6TSOU10dkqT6uj1Txr\r\nCKdw9KXRFdACDymSJKSUGJsN8XBWdzj067Dmm8UglmWXIimv902HU7WhUMMM\r\n4t7W0vndJc2Nreg4ed2qs6q3NSNs+OOC62TlZmxgQGGmBgH14xAsMbB429F+\r\n2BcBiJT1d05R9blX4fc7D/CNYohOdfAg/Fuppn6Uh+aLgExcrq0V5laTrzQs\r\nGF7c+NhsA0ye7o/ZAZDVMM17Azlpi8tmtCY=\r\n=Cz2G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.21.4-esm.1_1680618081578_0.35845997513453587", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-regenerator", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "6edc8e7f8a630d9bdb9d00d999f17b6999a9c728", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-mvA/PDDVuX+qu2Tm/fqRHgTmD1+QlWkU0ytmh9xS0JEHmk6X4aJIbgDY+rwETdpTSn3RgbNrmYM+6Dck8CE9tw==", "signatures": [{"sig": "MEUCIQDWCDFi8Zrlds8a+ehjuvtOg7vkymy7KnD/irBbVKtbnQIgc4nsdGUYnoEpfNNzEGPdQFNfUPnI/Ty0DeniBIAnI6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpraw/+Jn3pBEgDLDCT5a95FSD8FI3ShOOkuKZ/LB3LKV77onkcu9Gu\r\nWSTLsX1NAths4eDYcxSOorJPvA/tvN7gLIVnX29AR/VZZlZAfQtYRzh98jxy\r\nR11k+osMGO8Qk7b8dIO0m7AYNIslAxTJE1JXC3gVhDt+IkeGTLK6qDN9juRc\r\nK8+CFsaChH7VnPhA19HBG175nBptRl3uXvl301EFaZgHNDq8mLv4AYBYYNdn\r\nxM3TfIv2Fzku6E9xEwekjSPqCL9hxJXLKhhyd+lGqGYGcQzUWvdUkAs/KJNY\r\nQTLwAfN4ZpWBa+P91mDPUli2X5wQMeiCve4nJpwf69AS3xOz0/RHtMBwf3Rg\r\nJrDNJvyz5XGupSH24UzqVy537kNDbBS2Fmq5U9gRNwKZOMlXN4vk2wsa2wR2\r\nmIoYGTJAPzoFeCdxc9XRD/BSWmcyhimhF+bw73bRIYuJrGCOl7Pab5NNY/a/\r\nQuvYpnhIAC77eeiYkmheVLk5g0rPL25KLPzIyx4zF5HXrlnhlR3eEnepWvLU\r\nqTj0jmH9iNOK2X2CgrWkHZ2LKNIo2Uze8vUCcbsUbijnm2fgYoinbWTRgxmc\r\nLfv/tgNydKKeelBCaK4wgbfuZVe4ITT9AiAxPdreHf/UWPq0deaLU9O8Ah/B\r\nkM5InbSK9/Ic2cYBsn8Uak62tfeUzhA25pM=\r\n=ksj/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.21.4-esm.2_1680619162750_0.07485212138896058", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-regenerator", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "877d786fbd987d4303e0fb46df2c7e5a35dcd6f6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-yadJVFZIfpwlXEo5wsOM3+E56oeh0fJ5cmkNy51lj0PK+C/hDgvy3PAQ6mv0xNTDDeXTmoeMTn1sqcXVgU8fPA==", "signatures": [{"sig": "MEUCIGcpX+/9PFKvPDdwpvN+pB0opzB96i3lsI15VT8vFu7QAiEAppeq2xYAR3GGiktwlmuFammjKirSJE/5IGD36/J3TBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAihAAnh1uluUejRjSm1gk7XWVOVjCoZ3QjDpI2gILavYMu6g4s5FA\r\nY2uLMnKf6ZOnrafLAPwZQFsRV7Jgw/KHdtmLUArVYAwAvmfRan54UWHf6A/t\r\nrJU+OtMK+xuFEC7AAdFPn3y+At/MQNaSxZrSpjF28b0mfwlWvdHZjhuT+aHE\r\nYPO6oBhaEeGmjjwWzSY8JQ6zA/pMU93/NqMxoG0/n/+DSnS64JwpxJOEavMy\r\nYV9KHcZ0TW7RgW/S6pzVAlQWH0LJXeDpwYBLxQx5xfDZRIFiVAXx2QR68lfL\r\nm6Z+PfZ35NIbQvTOuEWrLydfCHgmpEiUx/7JhBEx+AnooRulO7eiQSjyG/zL\r\nnlwul9+DTSM+R8z1FvXuHfMBtnCU1MKtRS5u2DGZwJRNL3YlF2EvvKKqY+Zq\r\nxNeQ9LCquVKo3yVxLyCCweCaAc3R8cDPFHsWFt9KvDy8Lu60CB9QZyUNdNqZ\r\nUyPYoLgZR4F+hThPJ0pYR9dQNda8XowwJJPJsr721xMw0h+hhvll8R36m0rx\r\nuSQoxQjChzOqYmMoiULItlK+yKgvPzunAG8K0tIqNdxv1e8hbZKLZK3PgwpC\r\ndrd4gH1huP9SjW3Q0hiMxmGdGIsde4h/jVoKPfU0uluIW22gr+Ad0US1QqKI\r\nUjTRsJcGtJ0tJFl8X5U23g6T0P36aENuuCw=\r\n=qQxJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.21.4-esm.3_1680620171448_0.6635116577534872", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "ddaf612ab1e72597a62bfdd1ed8859e906c67596", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-ctUr5V4Ddxin3ZtWiuHFOb3/TGJPZuKWr0p4RdOGcPKSMjiD64T0fsgxeYi/G+0PGU9DPuWRFEGlDirIbh6rhQ==", "signatures": [{"sig": "MEYCIQD2ieeRz+kqEBuxipu5KoFn5MgWf26M/doZarRcCMKKJQIhANl+4R6SqCyIxLAjR0aheJbrDNarVpi1Bbsvr/29wvoE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvShAAhJQ/H8bOqzqplOzCuCnGXguQu2XiNKIu7C1tvKdvQ7ysB1Aq\r\nvR3TZ7o/dRejfJ5b82cWj7twCX5oY6yQfVq9GDFjvqwajFN44WzIliHfWQtP\r\nR3L0S9X1VzKu8UE5qgyIS/7OQizFTyKNPHhzXPUWr/SLbqKMkc2FIHQ9PAiU\r\nw0AV6VZe+KIn52MrZAzWc118L/jor5FTDYbov2nZ4xD8NGWBBHknj1KWfAEw\r\nuNs7DyYTyXyq7DbS6I5n0PyiPdu+g8FUOqT/2fNLQ6CW2NEmkBwkdcqrw8eN\r\nsKReVSA32/N22SIWrwmD8mYhuKEY5V5uJY9vtG21bkC/Z8mqIAENWgKhOjhA\r\nmz0LcvcxEX4Xzimw4juA0gudtqCn5lOmKL3tq0Qz2I/+5j1/Zs6ONi0rpWwR\r\nj1xuEnBSIE6dh79eubx8fqcTaeqID66qidhBA4SIJ+WFSamCcTDhG2sVgKN5\r\nxHB6CYARSWcid2hahT3f8ulG7KouH0N/Crf5TP3F0wytfwCgaGX5WslUL6eh\r\n6DP+LtKhrv0ehWf75MzQpTUdjWl+9uTU0mhyjujSWbeTEpxG64nNu204p1JE\r\nRT5ApcXIUgLQQjSZpheURm96yN7rEdlrJhSMpnZSz0WiC5Qwn0qpNAVCsLQj\r\nSLd8IbK+lfsn3NOh814AD4Wy0aut1DDTCPI=\r\n=w03O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.21.4-esm.4_1680621203471_0.4880521858279163", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "576c62f9923f94bcb1c855adc53561fd7913724e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-ZoYBKDb6LyMi5yCsByQ5jmXsHAQDDYeexT1Szvlmui+lADvfSecr5Dxd/PkrTC3pAD182Fcju1VQkB4oCp9M+w==", "signatures": [{"sig": "MEUCIGMsiBqyqbT4pLd6GGjS9pY4gR+0wodliz2vt+DFS7APAiEArPKtRhcxL2hvNfpzGMT82yCteGfV5TTmn7GnyS47gn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmockhAAhsAGVJuE+haQ1fGfr0WfBwYwTsTr6EkQhUDqWgvLnumOZBeQ\r\nBi3KKHmxSnNxknJUdgXoToAesCkpcFMBdbflRzoYL4B2lPKsM5dk57DAyiSl\r\nY/eSgYzpsUNHYA0gxGygCfDNBLKABsVD6crc5caDwUp4R1GuXHd0t6Wfmeg5\r\nMn+vInHq+8FszXKQMgZopM1ZlLdvwvvNVuN6sUPpHCERI2meKL46r4fxS1w4\r\nC1AN32+zaRtSFN44OI+pZuLTU0FR11jMfFEIVhxVrzifWmOs2VYd+340EDYo\r\nEuLT+Iyf29PQhUg4B+Lm0G2jEcwDQtGdad1agKPKr30hxR2Fgkwcyv9FeMVZ\r\n1GnX/in3JUmmgx8F/yh9+nqbQVIsXoQFVrXZXhArYHmGr95iEB/2FKZlzuGg\r\ncLhwOcrNvMPi2UTiWUeJ3GfYpL9TDbi2QR0HoRhFdyptzJerTR9Vl206Ijq7\r\nP7gv8N7PetsZT+krujYzjQsmSUU6qxwwKJWNP1c26xo6g9PzvFrTasqvgfQf\r\nGq7sJY0zN2Ha9niVltfvjM4vFLKi3UOHDT/fkYR09rAu43jtwk1xPnpWjNV/\r\nikD4vVM8puIVVFf/e0l6VqsI5ANYPJ3RqzXoDlQRvSFHUCY1Md0dJKBk5yBH\r\nCFl049UiVqoqy6JwB1BD270VEX2jh/X62hQ=\r\n=ZYct\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.21.5_1682711421189_0.46774547714932035", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "cd8a68b228a5f75fa01420e8cc2fc400f0fc32aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-rR7KePOE7gfEtNTh9Qw+iO3Q/e4DEsoQ+hdvM6QUDH7JRJ5qxq5AA52ZzBWbI5i9lfNuvySgOGP8ZN7LAmaiPw==", "signatures": [{"sig": "MEUCIQCIKlZqTwuESbm1brt7ur+2Z6iZBxV7NzwB9Y8VdJXw0wIgBLgwlsboatZTG+QazgBGi2dBquKdAWa0enzcGs8ufV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6488}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.22.5_1686248481644_0.8700048905425886", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "25f2db13d0585aa36bbfaa9e83aa17b7d35ba848", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-vks4U0g3mZixPmmrSHt7pXNG/Tg+QWHPRhMG6v1/Ww3FbX8tnoOaO4wF/rEyZ6JM2ujd3A343WvKu6xvHA4Pig==", "signatures": [{"sig": "MEUCIQCLRvTwwTpR7vYQbond0j0wBYmpw2R4siE/jMFyBYqtUwIgEAPrxzbTJFtEdGuYxP5Ff6YGv5jyzM0ROMCx5kukw8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.0_1689861596901_0.826801010699393", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "48c8264c5f3cf273cc2c056aaad737c30ba9a854", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-5K4nq6vSIrBOY9B1+nFHtJuF/ZlebhN2RgAxxsiVYY1pOckAYXKVj0hq5BalE6btJRRvCulHuXeOcD1SDqRNNg==", "signatures": [{"sig": "MEYCIQCaq6p6wF2L2Mc0DOkycONIbhyMrKDprjbJj91cr5a7+AIhAMEY9WkjtkxgGBk1AkBBCN8TyS9VOV2QTM1gUEhbgtIp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.1", "@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.1_1690221120096_0.2684078078692682", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-transform-regenerator", "version": "7.22.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "8ceef3bd7375c4db7652878b0241b2be5d0c3cca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.22.10.tgz", "fileCount": 5, "integrity": "sha512-F28b1mDt8KcT5bUyJc/U9nwzw6cV+UmTeRlXYIl2TNqMMJif0Jeey9/RQ3C4NOd2zp0/TRsDns9ttj2L523rsw==", "signatures": [{"sig": "MEUCIH/EfLVD2kQcK+aEbdFF5thdfEIZlBYh8bOCw3jFBbD2AiEAz6DWiVAo+xomgm/sN5N1ULsuctUxdF/dPHE2DOJ7HkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6487}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.22.10_1691429114166_0.4061168126279844", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "6a278c687349d62194ff8cd758d7de444a25d08b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-Gt/GRV7u6kd+/bauoKwYofz+5CwoPTzxRZeqL4+KoUg8VELU9UXKclwK0YnrJW6tmDo1FyHBf2K7Eex+nr0FKw==", "signatures": [{"sig": "MEUCIQDEGApyBqv11Ez6m1vg0ssfXkbh4asSEEQ6m9RcZ1lXSgIgXBVFKqpIRTW0h4nB+aWptsHzIIPTPIm+oZWSaRS7HtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.2_1691594096945_0.1304175257190061", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "4f8f93b7fd1917ad16af0744820be0d47ef0a587", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-zYHmhNl2696bHCAjffxJ2c8T9lNATAJ3j8o5OygOr2VfeBFo2EkT0fAQTa2TUu5Em6AWRrdcQdTj50cjLp3Tkg==", "signatures": [{"sig": "MEQCIGTO86fryzonHOIsVUyOL71nCmCmFIsvXMv7rAdu5KFrAiBL+8A8hPF3gviV3ZvxgvYTPGaUs31kpatSk2kywVU/zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.3_1695740213735_0.5520076848123552", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "bde4be1e988c0d77995ef3e6e371f3e174499cdd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-EkqHq/FVsmnmf7ew+kqwWcgG4OUgPu6qSRg5s1IZbx4xaPZnCtLaJqRchKh/JRfMoBahfuzOnSdBxc3ShQtjyw==", "signatures": [{"sig": "MEUCIAZ69jzWTVg+mXQ5oa9WADvixxPcVsxioYNTTPyO+ge5AiEA78wdDvKMDxb5AX1FxXmcn12yswQP9kAyg4LaNiAOP1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.4_1697076380360_0.16376046328495963", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-regenerator", "version": "7.23.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "141afd4a2057298602069fce7f2dc5173e6c561c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-KP+75h0KghBMcVpuKisx3XTu9Ncut8Q8TuvGO4IhY+9D5DFEckQefOuIsB/gQ2tG71lCke4NMrtIPS8pOj18BQ==", "signatures": [{"sig": "MEQCIA4Ro6fpiWTYvgmLKKaMmb0HGjcaPIMrpDfi2YqnlQiSAiA4gt7cBi3pB6qQeXcMzzDON8v6xGXMHG88oE4o+JVqSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6566}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.23.3_1699513439582_0.8267314299465414", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "86acc95f282d942f9c6aca254f2b8cdd8a9a5531", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-SCP1LGXGUP+mmTfx5872A96uhnmMrOOd+6+eNzc/+I8JoX1PJO4ib6JKuW9iQSOPlJ+LL/4FkdaAUP+CNgnBVQ==", "signatures": [{"sig": "MEQCIEPXpMVmnlTYszWXMsrsijlPnC6HPgjgD8rs4Q4jhQS0AiBXgWZADyRqwPhruTIm1LPllaiAve7Adf55xsq5ybQLgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5800}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.5_1702307934047_0.9929309404425848", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "37cf1acea50898538eeabddf91c7d9440b0b95f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-V6S8aETmFv4P+J26Pk3a8Sg7iXV9NPzqk+481HjvwvChBgtdIKPjQygAfZlExfV1G4BNkdMgn3C7aOYiuNd2VA==", "signatures": [{"sig": "MEQCIFt1VPyH6l1YYj7fW8lda+1a9bXt8sfUsahLXy+y0TnVAiB6ZqucEeuTXFX3t/AEob+W+U+Xkj9ZB1QmyYdBOv50tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5800}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.6_1706285646724_0.403296507480448", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "940d5efde025d19778502de1b41d18317c3574c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-aZEbMFKcNNdATsPzMeGLAziQye7kfHmUAbWaDyyE50Gqec4pRxNciRBpH1OCDI5DMR9s29virF50Tb54woZKgQ==", "signatures": [{"sig": "MEMCIBEqrPCJLt09K9Sf0a6uWL1z5UEYXXc0NR5XLhFnn9fzAh8rkkNq14EmjX6oETj/isADeFDsw324GA2yTPV4Sc4M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5800}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.7_1709129096195_0.3617035863014131", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-regenerator", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "625b7545bae52363bdc1fbbdc7252b5046409c8c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-sJwZBCzIBE4t+5Q4IGLaaun5ExVMRY0lYwos/jNecjMrVCygCdph3IKv0tkP5Fc87e/1+bebAmEAGBfnRD+cnw==", "signatures": [{"sig": "MEUCIQCKSBYVI7EL9oubezEFDyRv7+xJZQHhI+qTK4K89fLHDwIgeO0Ej7zDdpthw9hgOd4UlHyCvRHvXXS25Ft1ILQN7Yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6823}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.24.1_1710841740775_0.8898321373402576", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "995af8abedd8cab792b2689c0733bff5ae11b873", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-l5lEfuoYNgPjW6pT9NMfxOILFI1ulofVIHxpllQovE/lLCKz2jCgN+Iypuqhpoyk8mC5Texzoh7u8XcIrjWx8w==", "signatures": [{"sig": "MEUCIE0H6au5m9QrrwuMOFw5Scml4eROPs+G0eVYjfmPt3RJAiEAhVrLxZ5Cc1sA3Z3ZNaEW80mWngjWUKXwrSbjFB83E7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6056}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.8_1712236792220_0.2979209352146923", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-regenerator", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "ed10cf0c13619365e15459f88d1b915ac57ffc24", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-SMDxO95I8WXRtXhTAc8t/NFQUT7VYbIWwJCJgEli9ml4MhqUMh4S6hxgH6SmAC3eAQNWCDJFxcFeEt9w2sDdXg==", "signatures": [{"sig": "MEYCIQCKgRm9oflImE+aeC314xXMyBoMw9SXfaAe+Odbcg9zlwIhAI7dQOtYEXkICQQjXxlpsLedfLNR7eAfL8oZv4A6tofu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73016}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.24.6_1716553474548_0.5446946461289275", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "effcf3bef31fd2ee3150361948edbce73bab85f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-Gi8gHndH74kma1q14elAWEqrjRnjSXfhNWsXemB4wYY4JAHC51p9Nmt03ndb1KPVdclgozWXLchmArNh1FpWug==", "signatures": [{"sig": "MEQCIH2ir8SUEsR8uvJ+n7+fH5mukk4o4JMXjUfg+436EOfAAiBCLsss+5eOuE3KjhAH0l5kGA0itLe/eQ8PGk8JQKakVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72874}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.9_1717423459067_0.48221458417505203", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "801a036dde56097ac5f8ce01025792b7763f2e93", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-R3+9ernvynraPPYZHHVuvQogL5wlcl/434P++njmytxn7BoG6hPXVnJwKjN98wAhB+Tc2DL/FamFqoJylXQ5Nw==", "signatures": [{"sig": "MEYCIQCmFpZzWNXa+3DLvY4MU9CEdfFJ95g9RLoTipXeIpEPxgIhANyJ04REZ1AwePQ1IqT6d+k+iIiBDQnF+fAfNmfyT0Io", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72880}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.10_1717500009069_0.8019313056122834", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-regenerator", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "021562de4534d8b4b1851759fd7af4e05d2c47f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-lq3fvXPdimDrlg6LWBoqj+r/DEWgONuwjuOuQCSYgRroXDH/IdM1C0IZf59fL5cHLpjEH/O6opIRBbqv7ELnuA==", "signatures": [{"sig": "MEUCIQC/emtPVIRvVPC6mt1UosXK/4m08fzeNPg/YmkpRj7VSgIgPW+HCAYkXkQ0fekFNte1p+r1D5slK1OEHHz0GpD+1pU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73327}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.24.7_1717593324266_0.049546617953128536", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "313bd940fe821a7652bd0137f5b7751e485ef56f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-6+YUOjDYceM4f42NSyurfW1AK6LwiFYVIuBDuSwtN1pqshhyesuZZ558Axq/FuGx2SQDO/Pr/TmD64bL/HDKyg==", "signatures": [{"sig": "MEUCIQCtslc8pOF6xCCS9rJeKrITx17mqbST3mkSj9/3kAiBpgIgAPvceuVAXIEgK5qho25uZbnhSIqnkz0Ptl5JThpnFBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72769}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.11_1717751734018_0.410002149397769", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "b2ef124735dacd20a7dbd06c818fd6f78d85e949", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-0WJtAPfT9ftc6HSp37bDZD84mZ4CUpWiFGTdCny7kHtSfJRroTvFfR3mz8Ugc8Bb9CKjudVCS3Yv6xxiKSMNsQ==", "signatures": [{"sig": "MEUCIEfCE40cyTTva/nkwtRYH2yMPQDKTRcio5t8iMflE6guAiEA3T5u07bYbCGA2NaJsp5cD+DWeEr2RTEtiY3SkFtWWYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69530}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.12_1722015210938_0.892501394914043", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-regenerator", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "6eb006e6d26f627bc2f7844a9f19770721ad6f3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-mgDoQCRjrY3XK95UuV60tZlFCQGXEtMg8H+IsW72ldw1ih1jZhzYXbJvghmAEpg5UVhhnCeia1CkGttUvCkiMQ==", "signatures": [{"sig": "MEUCIGeK3THraenCbWqyCwnhXjPavfQp8vk7rsxEGlphwyHZAiEA3bK6wC1QNLuHo4iThfHR9wNPFd51HVWb7OZZg+5NH2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77921}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.25.7_1727882091312_0.18566539908112167", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-regenerator", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "03a8a4670d6cebae95305ac6defac81ece77740b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-vwDcDNsgMPDGP0nMqzahDWE5/MLcX8sv96+wfX7as7LoF/kr97Bo/7fI00lXY4wUXYfVmwIIyG80fGZ1uvt2qg==", "signatures": [{"sig": "MEQCIC4EBa8OqxUYT/dbZKKkb8JSI1hFgfBkUOxsLqRu/ceNAiBRcMJAJn+2Mnka7EycENTnaip4/5UIuQDOiaB8RscDDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6823}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.25.9_1729610470370_0.9072286383596944", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "e8606cf4ccfc2920d440b20e3b0e98adb884044a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-dTrBeoFP8pbEd8leI2DLaJE0C7i3VpC1NW6T59pojlYeMMoCgB5jckvGB4JALckNPVwWisbOdVG8NnOPK1OM2g==", "signatures": [{"sig": "MEUCIQDnCInvvxSHsYJoZYTlPMlXhsrHrUDhjIbmC0P1/QBDcQIgJyOkAnUxMCzbjw1+dlnrqxGbyRfsUF77FNnURNBkJdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6393}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.13_1729864453154_0.7645483927794161", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "b40266ac80f01604da26bd2bd7087a6304475751", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-uzLWkP2qJulklsQCDzAQcJmOxL234fq4TmZq+M2N3xZy+4IIlTVTv9Rb/5VXiTBv3UBynI23nIYbWPtZMcwCHg==", "signatures": [{"sig": "MEUCIDuNB5W3AjDGtLrv6CMrZPsRLPXYbvhMWZoWwGvIrjECAiEAtHzAqSdXRZWGMiaaaUGkns0UWbWGXf6q5bJCGGts8YY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6393}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.14_1733504044545_0.6246253917619813", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "7fc5709fbd4dd2aa281c0845362828b2b9cea0ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-xdsxsJwoVIwHkZN95fd5JGaYJlP2M1jToTzZfjbRVYuDP35ALLCfOvb6NgKtqmK3H+ub4Sfp3l7OT/A2hMSSnA==", "signatures": [{"sig": "MEYCIQDb1XEKm29h2o6IKXCMtp5tXymx5gmU3BWd+rWWUPs8XgIhAKvJuIq4mf25+eWcvTPh8mw4DBqr/48By9n3NaJqx+oS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6393}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.15_1736529870400_0.8844560105099619", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "d034eb889b39053706b6d7fc2cb7911316519714", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-GGS52S5VRGWpIejZiS4JSpMcv7g/rCAHfzyofZwhvnGz2DTGKXFYii9Mq/o0h9ORMEX83/DbVmxdK4ecPdIR8A==", "signatures": [{"sig": "MEYCIQC0KUb/aIK4IshAXm6i0eHiduCvUhHvYtFNGxv80weWJgIhAKW1EiuWK81FKZHgDRhg10ush4PS+p5bH1VPu1fdBDCT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6393}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.16_1739534346205_0.36763354065320675", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "7cb37b3884be0fc860fd50bb361b6d80a97b35f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-ALOYS6gqqdgZm4AVL/EjCm9P1LMYIZMGSZ1FqYMFE4iLiMN2NEXAfNIbuncJqLeuV3RMt7ZC0GXSS4y2m3kXGA==", "signatures": [{"sig": "MEQCIDPnRyMf+mLhmIME+4d9ycjAj1rGcJy6UbqX64HPD6DxAiAdkQHFWAnRIQCUp1nHYwn/xchSc5wwt/iYDf+XrPmPlw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6393}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-alpha.17_1741717498832_0.4699589018043864", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.27.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "822feebef43d6a59a81f696b2512df5b1682db31", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.0.tgz", "fileCount": 5, "integrity": "sha512-LX/vCajUJQDqE7Aum/ELUMZAY19+cDpghxrnyt5I1tV6X5PyC86AOoWXWFYFeIvauyeSA6/ktn4tQVn/3ZifsA==", "signatures": [{"sig": "MEUCIFflib+gsqva8KsIuiuBNbLg6yClTjv6c6YS0Z1QPCf7AiEAlpF9njwpD6ZDBISbweEWFUGJovAJBk+X7lB37cCFp3M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6730}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"regenerator-transform": "^0.15.2", "@babel/helper-plugin-utils": "^7.26.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.27.0_1742838101879_0.39167021654694123", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-regenerator", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "0a471df9213416e44cd66bf67176b66f65768401", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.1.tgz", "fileCount": 19, "integrity": "sha512-B19lbbL7PMrKr52BNPjCqg1IyNUIjTcxKj8uX9zHO+PmWN93s19NDr/f69mIkEp2x9nmDJ08a7lgHaTTzvW7mw==", "signatures": [{"sig": "MEYCIQCIL5ye+SChVEhZ/Q4RHrhx/ZQqG/3mhiUkoy2y/w92egIhANZwXEfTV5qXb0aESZXN0hwYEPdEM1UWg88iwt7S2S2B", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 173843}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0", "@babel/core": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.1", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.27.1_1746025736045_0.5351923243576042", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.4": {"name": "@babel/plugin-transform-regenerator", "version": "7.27.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.27.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "05d006c09f482b34ba5dc16d630c204a7d06d31f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.4.tgz", "fileCount": 19, "integrity": "sha512-Glp/0n8xuj+E1588otw5rjJkTXfzW7FjH3IIUrfqiZOPQCd2vbg8e+DQE8jK9g4V5/zrxFW+D9WM9gboRPELpQ==", "signatures": [{"sig": "MEUCIAd9oFoouKnoqCqs44Kau5s7Gvfl27k1l9AMd0lbfg7JAiEAoNj59Of0i7Lvue2wJhbgtvQwPSF0n+vrz94vy+eI2UM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 189986}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0", "@babel/core": "^7.27.4", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/helper-plugin-test-runner": "^7.27.1", "babel-plugin-polyfill-regenerator": "^0.6.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.3", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.27.4_1748617301341_0.5419112433977842", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-regenerator", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "40704d99369109d7550b2e76d07b619f01242cb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-BDyRW+5K2Xpe33eAvHfoDEv0jtcvwCM0t8r+D0rkLPW4lz7ifer91EuVVpRqSgbIZnhKKuwiaoiquOCG+OYgZQ==", "signatures": [{"sig": "MEUCIHIqaPP7eUAHkGnvtUI3u5FQ0xcHVmXhvWcBB/xw0CtbAiEAp2blsWuWdzIFV7a1DiO+50qww9y98hZvh0fMdICq1pw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 180472}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0", "@babel/core": "^8.0.0-beta.0", "@babel/plugin-transform-for-of": "^8.0.0-beta.0", "@babel/plugin-transform-classes": "^8.0.0-beta.0", "@babel/plugin-transform-runtime": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "@babel/plugin-transform-parameters": "^8.0.0-beta.0", "@babel/helper-check-duplicate-nodes": "^8.0.0-beta.0", "@babel/plugin-proposal-function-sent": "^8.0.0-beta.0", "@babel/plugin-transform-block-scoping": "^8.0.0-beta.0", "@babel/plugin-transform-arrow-functions": "^8.0.0-beta.0", "@babel/plugin-transform-modules-commonjs": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_8.0.0-beta.0_1748620267623_0.5685381094401316", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.5": {"name": "@babel/plugin-transform-regenerator", "version": "7.27.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.27.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "0c01f4e0e4cced15f68ee14b9c76dac9813850c7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.5.tgz", "fileCount": 19, "integrity": "sha512-uhB8yHerfe3MWnuLAhEbeQ4afVoqv8BQsPqrTv7e/jZ9y00kJL6l9a/f4OWaKxotmjzewfEyXE1vgDJenkQ2/Q==", "signatures": [{"sig": "MEUCIE6zNgXXiPxikIEvwaCxukx3J1e/Xr1mVRIKZ0QiZHnIAiEA0ow1aRfPmDI1BhuKXwmpm876fdiog+AGN+//asVZssY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 190904}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0", "@babel/core": "^7.27.4", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/helper-plugin-test-runner": "^7.27.1", "babel-plugin-polyfill-regenerator": "^0.6.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.5", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.27.5_1748950025384_0.1888029811376979", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-transform-regenerator", "version": "7.28.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regenerator@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "dist": {"shasum": "f19ca3558f7121924fc4ba6cd2afe3a5cdac89b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.28.0.tgz", "fileCount": 19, "integrity": "sha512-LOAozRVbqxEVjSKfhGnuLoE4Kz4Oc5UJzuvFUhSsQzdCdaAQu06mG8zDv2GFSerM62nImUZ7K92vxnQcLSDlCQ==", "signatures": [{"sig": "MEUCIQDB79BZvHWdfhxc+ohALez0uKwAIWFz1Bdq+iS2yMYRzQIgKghiXDhYhZ/G3CR94ZORJjMf5jJiHB+wjCozgHk5A2o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 190904}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0", "@babel/core": "^7.28.0", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-classes": "^7.28.0", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1", "babel-plugin-polyfill-regenerator": "^0.6.5", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.28.0", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regenerator_7.28.0_1751445497323_0.04848208961746048", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-regenerator", "author": "The Babel Team (https://babel.dev/team)", "description": "Explode async and generator functions into a state machine.", "version": "8.0.0-beta.1", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regenerator"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-check-duplicate-nodes": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-proposal-function-sent": "^8.0.0-beta.1", "@babel/plugin-transform-arrow-functions": "^8.0.0-beta.1", "@babel/plugin-transform-block-scoping": "^8.0.0-beta.1", "@babel/plugin-transform-classes": "^8.0.0-beta.1", "@babel/plugin-transform-for-of": "^8.0.0-beta.1", "@babel/plugin-transform-modules-commonjs": "^8.0.0-beta.1", "@babel/plugin-transform-parameters": "^8.0.0-beta.1", "@babel/plugin-transform-runtime": "^8.0.0-beta.1", "babel-plugin-polyfill-regenerator": "^0.6.5", "mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-regenerator@8.0.0-beta.1", "dist": {"shasum": "7621c7de04a0d5e18fab3a8c8442ef81c93aecbf", "integrity": "sha512-SUguLMHpdtBDcn73ImgI3KqaIsNoZSfFhlYMFHogbgUhRocwYiTzBWQH6F5uI/ow2gnWmDjkKk63Y0aSNO66Hg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 181432, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDRGDTqMS5YsHAvoel2zyzKYFcshPlmubAoybBdqsZREAIhALW+AlyFeRQeNjH0a/zOwecyw1ylpMDyY3MO07PJtTC7"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-regenerator_8.0.0-beta.1_1751447059173_0.4812441548539297"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:49.395Z", "modified": "2025-07-02T09:04:19.591Z", "7.0.0-beta.4": "2017-10-30T18:34:49.395Z", "7.0.0-beta.5": "2017-10-30T20:56:27.376Z", "7.0.0-beta.31": "2017-11-03T20:03:30.379Z", "7.0.0-beta.32": "2017-11-12T13:33:21.736Z", "7.0.0-beta.33": "2017-12-01T14:28:23.667Z", "7.0.0-beta.34": "2017-12-02T14:39:25.834Z", "7.0.0-beta.35": "2017-12-14T21:47:51.316Z", "7.0.0-beta.36": "2017-12-25T19:04:43.403Z", "7.0.0-beta.37": "2018-01-08T16:02:33.907Z", "7.0.0-beta.38": "2018-01-17T16:31:59.195Z", "7.0.0-beta.39": "2018-01-30T20:27:36.314Z", "7.0.0-beta.40": "2018-02-12T16:41:41.538Z", "7.0.0-beta.41": "2018-03-14T16:25:32.426Z", "7.0.0-beta.42": "2018-03-15T20:50:08.424Z", "7.0.0-beta.43": "2018-04-02T16:48:00.215Z", "7.0.0-beta.44": "2018-04-02T22:19:42.447Z", "7.0.0-beta.45": "2018-04-23T01:55:25.468Z", "7.0.0-beta.46": "2018-04-23T04:29:51.787Z", "7.0.0-beta.47": "2018-05-15T00:07:11.595Z", "7.0.0-beta.48": "2018-05-24T19:14:38.711Z", "7.0.0-beta.49": "2018-05-25T16:00:12.843Z", "7.0.0-beta.50": "2018-06-12T19:46:42.804Z", "7.0.0-beta.51": "2018-06-12T21:19:07.598Z", "7.0.0-beta.52": "2018-07-06T00:59:06.339Z", "7.0.0-beta.53": "2018-07-11T13:39:55.622Z", "7.0.0-beta.54": "2018-07-16T17:59:46.349Z", "7.0.0-beta.55": "2018-07-28T22:06:44.743Z", "7.0.0-beta.56": "2018-08-04T01:02:54.522Z", "7.0.0-rc.0": "2018-08-09T15:56:12.805Z", "7.0.0-rc.1": "2018-08-09T20:06:18.886Z", "7.0.0-rc.2": "2018-08-21T19:22:16.376Z", "7.0.0-rc.3": "2018-08-24T18:06:10.856Z", "7.0.0-rc.4": "2018-08-27T16:42:14.440Z", "7.0.0": "2018-08-27T21:41:33.034Z", "7.3.4": "2019-02-25T18:35:22.919Z", "7.4.0": "2019-03-19T20:44:35.830Z", "7.4.3": "2019-04-02T19:55:54.953Z", "7.4.4": "2019-04-26T21:04:03.250Z", "7.4.5": "2019-05-21T17:45:36.154Z", "7.7.0": "2019-11-05T10:53:12.829Z", "7.7.4": "2019-11-22T23:32:21.954Z", "7.7.5": "2019-12-06T13:17:49.112Z", "7.8.0": "2020-01-12T00:15:55.752Z", "7.8.3": "2020-01-13T21:40:57.763Z", "7.8.7": "2020-03-05T01:56:01.915Z", "7.10.1": "2020-05-27T22:06:51.458Z", "7.10.3": "2020-06-19T20:54:08.217Z", "7.10.4": "2020-06-30T13:11:22.380Z", "7.12.1": "2020-10-15T22:40:27.231Z", "7.12.13": "2021-02-03T01:09:53.783Z", "7.13.15": "2021-04-08T15:50:22.887Z", "7.14.5": "2021-06-09T23:11:22.156Z", "7.16.0": "2021-10-29T23:47:33.786Z", "7.16.5": "2021-12-13T22:27:33.952Z", "7.16.7": "2021-12-31T00:21:23.456Z", "7.17.9": "2022-04-06T15:55:21.833Z", "7.18.0": "2022-05-19T18:16:34.379Z", "7.18.6": "2022-06-27T19:50:03.679Z", "7.20.5": "2022-11-28T10:12:43.799Z", "7.21.4-esm": "2023-04-04T14:09:29.151Z", "7.21.4-esm.1": "2023-04-04T14:21:21.766Z", "7.21.4-esm.2": "2023-04-04T14:39:22.853Z", "7.21.4-esm.3": "2023-04-04T14:56:11.756Z", "7.21.4-esm.4": "2023-04-04T15:13:23.627Z", "7.21.5": "2023-04-28T19:50:21.341Z", "7.22.5": "2023-06-08T18:21:21.822Z", "8.0.0-alpha.0": "2023-07-20T13:59:57.048Z", "8.0.0-alpha.1": "2023-07-24T17:52:00.285Z", "7.22.10": "2023-08-07T17:25:14.312Z", "8.0.0-alpha.2": "2023-08-09T15:14:57.167Z", "8.0.0-alpha.3": "2023-09-26T14:56:53.884Z", "8.0.0-alpha.4": "2023-10-12T02:06:20.597Z", "7.23.3": "2023-11-09T07:03:59.818Z", "8.0.0-alpha.5": "2023-12-11T15:18:54.254Z", "8.0.0-alpha.6": "2024-01-26T16:14:06.906Z", "8.0.0-alpha.7": "2024-02-28T14:04:56.402Z", "7.24.1": "2024-03-19T09:49:00.910Z", "8.0.0-alpha.8": "2024-04-04T13:19:52.401Z", "7.24.6": "2024-05-24T12:24:34.700Z", "8.0.0-alpha.9": "2024-06-03T14:04:19.221Z", "8.0.0-alpha.10": "2024-06-04T11:20:09.201Z", "7.24.7": "2024-06-05T13:15:24.408Z", "8.0.0-alpha.11": "2024-06-07T09:15:34.227Z", "8.0.0-alpha.12": "2024-07-26T17:33:31.110Z", "7.25.7": "2024-10-02T15:14:51.597Z", "7.25.9": "2024-10-22T15:21:10.600Z", "8.0.0-alpha.13": "2024-10-25T13:54:13.398Z", "8.0.0-alpha.14": "2024-12-06T16:54:04.696Z", "8.0.0-alpha.15": "2025-01-10T17:24:30.578Z", "8.0.0-alpha.16": "2025-02-14T11:59:06.351Z", "8.0.0-alpha.17": "2025-03-11T18:24:58.995Z", "7.27.0": "2025-03-24T17:41:42.050Z", "7.27.1": "2025-04-30T15:08:56.237Z", "7.27.4": "2025-05-30T15:01:41.563Z", "8.0.0-beta.0": "2025-05-30T15:51:07.887Z", "7.27.5": "2025-06-03T11:27:05.594Z", "7.28.0": "2025-07-02T08:38:17.529Z", "8.0.0-beta.1": "2025-07-02T09:04:19.387Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regenerator"}, "description": "Explode async and generator functions into a state machine.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}