{"_id": "@babel/plugin-transform-react-jsx", "_rev": "142-1959f612e08abca43bcc88cbd325c203", "name": "@babel/plugin-transform-react-jsx", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ad2a56cc0bf7c4c1622227205fd66df915574bc9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.4.tgz", "integrity": "sha512-lMLzOkAsOwo2vh/bVh3CsNwL2h7RGo0gw+1Fhfnffcfvx78xbPjL+u7c793PGGDCIFCNng2vlzjX2dqAWXPYcg==", "signatures": [{"sig": "MEUCIQD0M9/Nkj2l+SPECXdLGDmk6TxK4oTrBKjTJDvCeJStJAIgT+YdpazXEi4pe2u1igP7tjqEV+fxH5x2//fXvpSSl4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.4", "@babel/helper-builder-react-jsx": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.4.tgz_1509388543117_0.6228925436735153", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6207d501db2c9b8108a1ccef230567fb653f6cea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.5.tgz", "integrity": "sha512-FaRDfXNRX01JECds/mTd6VoArlQ1S92BztVN0+YSSffecAyvEm4tOi1E77fNXCk/1vCsC3wHLGw6p7y1izYG+g==", "signatures": [{"sig": "MEQCID4ZCW8STie66oWuzgR0jIgIha1/ElNe3owV9dG2c80vAiBdIpEOmXnhruvySyaqI86yDNJAdLvGsqcg/Byp+A4T0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.5", "@babel/helper-builder-react-jsx": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.5.tgz_1509397042419_0.9636840100865811", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a9465a50498302f1669cb2f14ea5e7049af79d07", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.31.tgz", "integrity": "sha512-bvl3xi/wVPMUS6L6PdXWfnV+3C3DyG2io3YRXq2YgsGAr7FjPOczblT1JtOn/DCaWHUzx5itGpowDvvldvagcQ==", "signatures": [{"sig": "MEUCIBtmsYg27NpmypVV6xPbaVe1vqVrBaj/zGXUjWqIBjXEAiEAuWKQnOHD20RBEMJ60GZ8DnetGBYiFAUeoKl9lNHCq6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.31", "@babel/helper-builder-react-jsx": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.31.tgz_1509739447280_0.48462074901908636", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "bc0042ef4d51d94ef6faa04b055190075f1c325e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.32.tgz", "integrity": "sha512-+H7pWyb+uy/zru07me9jS0pCpEexDGEH6buV263xsa7fXysFOGIxnz50zY0/ehAjxhUhZZTAmRBE1u4R9TJXuA==", "signatures": [{"sig": "MEUCIQDQ+dFZ+r+/BqMeKeejAfQPAzIlZ+0It4G7P+8jWD6CowIgW0OS5puPoJGXoHDvuiy9CxxuR+MZpkHM3e4dwAEG4jE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.32", "@babel/helper-builder-react-jsx": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.32.tgz_1510493628274_0.024811682756990194", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "39d5feb0f33b4547d472dc769e0eca6a70dc0019", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.33.tgz", "integrity": "sha512-dvhAHzsPelahtBNSQb9veasRqkIbcAvHjPqCQZUtdtS6jXBUiRYx1/gZKMOFN5BNo3+K10X9Z+eBtG0NzLokbA==", "signatures": [{"sig": "MEUCIQC6VX/nvEw9H5L1iH/xyCWqq5XCM/taHlQ/FlgHg9BT6gIgU3U7iLHHufrLFKVES1VHGX77dK0LWAiHRLp5SFQNrLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.33", "@babel/helper-builder-react-jsx": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.33.tgz_1512138544106_0.7615504576824605", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e34e47c71c7efa6a9362176fdf5d3ae16368a117", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.34.tgz", "integrity": "sha512-J2TSjbhuXf9kEarsU3DlwwarVsQky5H/H5XNoIMPbWMoaTaG5q1GjRhQaYylvVe58siAkTvmXdy9JD/XJBDYcA==", "signatures": [{"sig": "MEYCIQDEG/4FWiIIHe7a4jTChfixAnXzdDRjDcPKbZUuI0fVFgIhAM/X7Onr3yYAiDfmTkrs6aOzpmPcyB5jGxxdWuAcWIs/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.34", "@babel/helper-builder-react-jsx": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.34.tgz_1512225601579_0.2463587475940585", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f5f35e2c50f6254cc3fb3d9c9e76d27ead302b43", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.35.tgz", "integrity": "sha512-ncvq+nD3SqqvXzRgb8H8Dp95Ul0UVihg+z1eanM2GvI6bN7B/ZRvb8y+26g3vmBbwqBwBX7OfFF1JwGPRcrymg==", "signatures": [{"sig": "MEUCIQD7WsyIJn6F1DBJBh3MZizh1WUTGg851FVvzFpJTQ9aBQIgMI46lHKTYrQHv/qkEfxn5tMJi+1SG6blr5aCO8M0rZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.35", "@babel/helper-builder-react-jsx": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.35.tgz_1513288097687_0.9340925244614482", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8e672b7340beaa235d38801a6181f1dc2aed6e8a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.36.tgz", "integrity": "sha512-OzBufQ6yt3nUZGU2bcJFdTEOQLRq9mi5wnoCFyw+AARXhWwuwQ9mxpuPp9CLOORHOMNY8lfyASPpDxmBShGyGw==", "signatures": [{"sig": "MEUCIHt6bo6/K1BCLZVfPywtm2DCZUGwxAvS1WzdcNnSylWzAiEAjNXZlawYPl2gi+H6KJfNr09LV1XP5bGtccYy3GKH9ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.36", "@babel/helper-builder-react-jsx": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.36.tgz_1514228724702_0.49506930494681", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c7fd43161d760d93532d33d19dbbe9d1237e7ce9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.37.tgz", "integrity": "sha512-Djz9xbCi5UweAx9YOFqvySqpRWBXld+g1PgxPiH3Zza01eMfcckLHFVIIp91qfvYbHxy+q4/bEELDrcY71dvVA==", "signatures": [{"sig": "MEUCIQDcoFNQXyKycQGgb7+bqclgjzzEzYbqG44N2r27ZTAO+wIgED8j6VUzdQD5W53TNTloDQYUY+MMJ1GReSr5KCeRblw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.37", "@babel/helper-builder-react-jsx": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.37.tgz_1515427412791_0.6182263656519353", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "6abd97c2dde5a007f0fba77d4c4bd7b2a68e7316", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.38.tgz", "integrity": "sha512-8mT1KlhPMKnTaV6gm7j53Z2TtJ/AFfoTbZOfixZ/ghRKMZD7yoYU8pKmLlHilSLHE77HXeyqiQwXqby0TieyLA==", "signatures": [{"sig": "MEQCID92NkfZ3V1LZEJ7dP3W+ihtrFXAiaKGsZ04/zS03Zg1AiA2Jwuh3UJ6Fxp9DgEQvFj1gm036TdgxR8PwpnMqpcFDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.38", "@babel/helper-builder-react-jsx": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.38.tgz_1516206749788_0.7003339338116348", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "43157bc2274fa3b6925f67ae1ccec03f9421432f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.39.tgz", "integrity": "sha512-OA8PKBLfdmSwLdJ1Tx2bV35MXnMEiDZNXqgKZMpIoz1HDY3ShQbdev8hgtzCgJDu4M0/xx3c1skgAfN+hluSvg==", "signatures": [{"sig": "MEUCIQC1wP7cOHimVQBC1pf44UP7P/PeX/yTgrBEk1M1eQ+yAAIgb3rxkKp27oIKpZLtInXLusXMSr0vj8OTc207WBY58yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.39", "@babel/helper-builder-react-jsx": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx-7.0.0-beta.39.tgz_1517344076407_0.04642990720458329", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "93af0b0ef691cda86ab52d912b50f72eb538349d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-7dqeoFtVL7Yl06+/NNHzgbQ5vAUiBtVQms01zSLRT3PmGQg5Z32JynIp6MV6b5fhxJpy7MQn5NHLYjAaOq4vgg==", "signatures": [{"sig": "MEUCIQCv0n/REQj/Mj8oDbPtmzcXq+YgklNmnErqOoHV2/NIrQIgKlUdQbgKsggUCUSBiu5QKXQtFvhJdIamsAKbIEQLE/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7235}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.40", "@babel/helper-builder-react-jsx": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.40_1518453740023_0.2391226588604627", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6cded90682ca83146b512ff288ce58836b1d404e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-TVzUpgFckawSvNsN+YmRe4FDC9A25K+KmQwEjMCKOYmBNWn1muzEzcWJU+cn+41s12GD7kUuFuVIu4iWMT8Umg==", "signatures": [{"sig": "MEUCIQCOywiUklF8QrT9UvC+Env6vYRXtyXtWDpa/W0SeOADzQIgOagC7mUDmqt5Npwp2RrTjxscb3KHXbRPb5KwAgh6gbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7443}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-builder-react-jsx": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.41_1521044794636_0.8097631353055394", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a25731396ca87b07f10362a950deab4526345fac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-XT8M4cZmr/Gaw6Cp2UELhYajB/PT6xNERtv8d+Eu08fULfAbtZJBFVxmm68T9LT+JZkcI35O1gTP17yJz5PJrA==", "signatures": [{"sig": "MEUCIQCxFeU3JMrEwPJDRIQ7x8Nznc3CL/J6m403DLK4GCFovwIgYEBXoDCV/Df/sCjNSOT8VhuhQexpq1JKdxKTcmHPO1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7443}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-builder-react-jsx": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.42_1521147107238_0.2919196323415125", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "41253fa259f43c83e33a0646222695142c6fa113", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-70DnSQWPNVfyvqavmU7gIiJGA8Lmwt44CoGqWJC/bLx7V1ROObdzUrN0601T7GckH21MPFfC9qvtGz68qtM5Pg==", "signatures": [{"sig": "MEUCIQD0L08WvDxECzGMy4a1Lq1JCJOeEwIHL9X8MWbUeiZBtgIgBGIrdD3XJGLB+BeGvZZhkcv9OGuXEOyhyUir80z09zk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7662}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-builder-react-jsx": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.43_1522687723363_0.017302289231816204", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "656a2582002ff1b0eea4cd01b7c8f6cbbf3990bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-g25pZKtPRndcaqt+ZRoeV02P37HZKhQyswkpoqwGYfzlKy0SaCCu1ZrhxsXgLBYr86RXP6huaGM9ZSzC9xjufw==", "signatures": [{"sig": "MEUCIHIoLYV7y8PsUdFaH1WEJOvhq2IwyIXZVUE0qpgZCcJVAiEAwoapwShPAtRnl0R28rDXVDosopWQEJbRaQnvG1Ea5ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7941}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-builder-react-jsx": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.44_1522707623750_0.6209331021880193", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d7402d2e66e1bee50c8d8aa80e2f7f79ec9864b2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-Wm4AoD/FoEVEo6j7EhnGIbL6bi+3lX1faphsTsIuB3yuX+HxWb50SxdCg8S+fknlwwFp4vONhY4e0hif4ZoBKw==", "signatures": [{"sig": "MEUCIFRKcNFk/ZJc0eiUBOWL42VFf1vxum/E3rAdVN9CysDmAiEA6ntTWrh0A55cCllJul5A1hBaIh292QVFzIRwrtW/FQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2eCRA9TVsSAnZWagAAOIEP/RHOSjmn8C+4NHlZ7y+6\nZw5mtf4DLMJPy6QXMFAu54oqVX3G8mDjoD7LCnFQYuxFVBdlVziNMgZXjubO\nXVJ6BfyEGhV/J1FJWqC/K/PXk/zEuwmaQbNKAp56eXN4C9fQitBXA1nw8eru\njoNGUfLjyl1zdr/Vi8pm2mwr0c1qkOP5MZ4+ku/GE/J43AnEJIeQC50wCjDv\npHcQXwmjClmMPbZq4NpLS2DxriojRr/YMCzW2fzz5Q2vkczQd7zph1qVmMPL\nZ0kYZbN6vKsmI6hNMAPhb/oLziewzmbKz863+qkErOqqK3/R6TNaWx9amycZ\nl9CdXscJIKR5Nt85jbR6gsV6Vg2eqMdn/2kOr/i8tKAggzGeVr9czHcaVh/o\nIsfbFCP8ufhRuev9qz0WxucMe75+G/oCj9BWP3r2TsyiVx/f4zfPVFR/3SnQ\n1cRczPJeokVR+iv3TsmZgRRh6s2iz68kNoqEZ5GxfR6oT9T1AEuWGbpTvzEA\nQRTldcyBdCFlWS6bdbs79EgeSe8dXRLoxBOb+pSxK2LXMHxJDKe0JqS+dFya\n7lClEjnnAaZRwB5h7AtCycgGueDDPwplJxsIM7slnOZmyr0Vnia13wFyAOpw\nMkTMEKxvskA2jkkxJ5XNIih6q1WLAKnJZ7DEbi/dr7abXBT8hhmvdy01CR6+\nw1QP\r\n=karc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-builder-react-jsx": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.45_1524448670106_0.034783798592032955", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9aa0c491ced30a0d1a8414da2d45462c66912d1e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-b8VtHVQub3h7lXG1ShaCKgGJdra7fRlUK6hx1eCcIWAPYnJMz4soLMSPiXmyjDA5L0CbYmyTkceU1GjbeJmaaw==", "signatures": [{"sig": "MEQCIBedDnXUolnpGYWWF72d1sdiRRzdSegnCrexPKprpIKjAiB0O8nVrt28KW3ZUi+O3WnyPtoB7nvLuL4iqCZ+BUr17Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHJCRA9TVsSAnZWagAA1zMP/R1/bm2fTe6R931YMFH7\nJ2L8Qd4ajZ1YlKr0ygGNUxvYhG4heuhKHM66xftqxVXfcC5c7F202yQhSpAs\nYe61IVwIIttS794qFrTsVV5G8ifp9+DU+AjzllWhtAmaf++2JM3HredrKiUU\nCJD8pnbNS1bsJBHDg4k65S4OQrxjXWXJuzJKC+9TezSGikKDftE+/2MiC2k3\nkTA2moLmWjY231qBxLWX3AA2XBB2FQKU/EmiRw6usz7aL9Wr0uEUkV7qEeql\nDQT0byNzCxEIET81MYNprwpwrdVIkc5KHFzv4rSyXyir2QyyxRAS7dtIE7NX\nyARun8BdrjNniX9y2BinPBWPpFY/3u4e3ItSVCSTKO4AdSvk2l9mQvDTR000\naV04bXti1GBMcHF+La1T0sJPp6RliNCOWd9hQcnL1kdfK6ki4P0wgZHGYq8M\nGJOeqal5G/skie/3ju4aYiUMsl1Z5JYD28DFkuxgvFVMO5lxau9mWpWiTrqP\nnjx7fNmdg97/CBln8988GPDhLe/Qt5bkmQKWJHoUfMOGQSDeRVdrFhSeW0+0\nLNy0Gagd/IRs46QcZZ2puB/Qbs1vR6FKD8wDs9g2fMBJDLBTwoHzk0iYh57d\nkHQur6YgE9kQeVspmINSs0t4UJVTiDnF/SXTnczEnXXvkDduhSchxxn4BK9Z\nY/MQ\r\n=e748\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-builder-react-jsx": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.46_1524457928320_0.7402786594478734", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "98c99a69be748d966c0aea08b5ca942ba3fc9ed1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-HGian2BbCsyAqs6LntVVRpjXG9TkzhHfTynjUoMxOFL29doKEy/0s96SMvmbBSR/wMRKMd1OPvCiEYYxqZtr3g==", "signatures": [{"sig": "MEQCIDNne2hDXTr5hfe7V1vK1o8TyarilgkWp6LscwiTGEcGAiBrWe0EjRZI1CvbVuCD/CPkDnZKAMC9Jb1pqIRcul3u9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iVeCRA9TVsSAnZWagAAezMQAJQpAEaj9k7ERy9EChtf\nvm7LODSvGp//PjMVoRJ57hJnDyUX+cUuggkkXpC1VHQqeJDuxpJCXGYDVRhV\n8Gx6hE1pO5Vpcy2/Ju4EpK7stImgVti3CL+fuGsDg0xVYlVHZN4Y6dn11e5s\nB0iMFwPhd5hpdjeKw7+WX2FwQevQBa5URhHfcdfUiOKAMrfdkBwkwj0JeF/P\nOQUbtg2Rw/6A+KjDfXPKK77bUNTn75ZJiRyGTNcvEedPU6Ihwaxgkt7Y6m4x\nveEKpDbL39xUMUWpEX/sDyzHBCi1jzo3DHYWKBSXdmL6c+6UO7xJpjQKEbJx\n0dOykzVeeQPeJ1CiYnbKr9YWfKZbANoSgF1mCG8lCgjT9KvJpW22WRbSPS6R\nZNCvm2fwkOLt65CO4dZLBghAeej4YBNT4X7MuoNEmiRUpwoGOS2u58KNTehr\nBhM/3T75CCIloPkvYOiEh8xysXtty1psAIjmmY/0Yv9jE7T0YiVajO8K08vE\nKuqsGTHUytcb2+37iEl/0TW22XmIpPgCl58vVk9V9R1NmclVAHI/D2OhJGdp\ncN2SCNMCsdIur5iXTwAduiqhtQbgfMNWUrRCUtJFYikMKUoAglPrWV3zv5sC\n1aSvonEgmULS+FaqIiAvjluEmlfGplcTT06MocCmqOfkT7g0UUUNRaiIk7QE\nmiag\r\n=mfnG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-builder-react-jsx": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.47_1526343005156_0.0076865805330974535", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4fc3fe8e257416160e36c9113c68fac3f9f17952", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-0lGYrdINNhP9dpgVwG6JvBD2bu49FjkMgTtGzfeJvpWJb1naUWTWkdupYSZ+izkY1fxCqY7Zki+gDWpU+Dih3Q==", "signatures": [{"sig": "MEQCIFCx5jSsmYHzqiuEiQTtYgFY5jrAdlQNcCXOvjLJTROtAiBTn1i5nQX9Nw1CXDEhkVdU2pBFoTIfKRTPoj0Zx9GyfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFTCRA9TVsSAnZWagAADSgP/idGr76FA1hnlewAnUI/\nB00MdOM13oJuaJl5l9aWp/90HQ6Ki0cXtt4/TlaI2O6pkTxlu0PMdR2qeIK3\nkduMfQ5SqU9LkRZgRZRqiX/5q8eMeQqcT3v+bybA7W7w+qeu1it0ktAvUnQ3\nwVqbuHWurpZJZBYLLPPX6bjqEX3R70uJWXB2HRm572e569MaLcezTvl8QA52\nmPPhwzTQCl8WPZaVv4H9BfETTyQZ8tDfUZxKonX1LzDOVQXbJETM1ViJDLL2\nJAUCaQ5UF7Esntif99mXBvF6qMn69Qg5Zhj17roJ6vzevFq0XvDLRgT38s2X\nenXQKSpkFCyLaW+2MH8OmmpeCJoMdiSwDULu4W2t9XinTG6hbvb3b7h0eqgs\nr7PiYtH53FgX88IfBsbQXzuqklVqBadOANCQJEtPpGbeyrPmFzxEB3spzeRx\nRFhbE5fFrNNa7hTRZbiuD1Kgkx5xzE+tXF0M6Vlsl9KZgWfcvH4IaClar9uw\nSZQgPYgy5I10ycCgH/NBrYaWvyV77MpPntkQqZr5HHZuXDXoYT8LHL1eF2lK\n+bYx6pon/Yds1r3KN0jEHU4tAdjDy3qaBctdrGpfBG0HGlFgv2DWY2oZwa2H\nJbbqhB3sRJqzaQNInagBaAq1GmNKMZl5ZgkNOUILgE4IREoClMeWWQc1jXZZ\nO5R+\r\n=Eba3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-builder-react-jsx": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.48_1527189842160_0.0020209539818762945", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0f2789fde305c3c14151848f8514a2af1441af58", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-QDXcmwjn6qncfnKtepomgsQnif0HnJdWwEtS/nE5UNfjvP87fnjrpYQDVfnJjmBD3f292ow03y1Rcs93tFLezg==", "signatures": [{"sig": "MEQCIEXJCJqk4h0NLJwBrYn3fR4uLohj7UCW8PhAnTPXbrRKAiAP0mZINgmRDq6bnXIrhzj1/SUZXEifvwrzvIhSeqPD3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPPCRA9TVsSAnZWagAATaYP/Ar6x4EXgTPkNi27D4pd\n7sqzpY87yVe2UOFQ9YMmHkCMqrc5dwLyyvALpvrMfHmxrpE3xHFTxpb/oEaa\nrUsvXTgMKgY5I7CFOwFToK0YiY2oXUL4VGQj+Hma/IS8KKTS/ENAPdtqyze5\ngRj6gLw7FxMUL2gJIKSw+xZhpTqzBu/YNbV/qF3scBb5q/KqlYRwH596pHsH\nbS8nx4jC2RedvSexumiFWfsdhiB4s6iZurr2Q8VVYM22EUPhewGAycLH8Pmi\nyt3PV8ICcKuFsl8q//nKbCRSD6yGqTbjSWNZGC/+clSgNYzhGEzHzgQobQkO\nI5SIwx8AA0wbCeR3TjnDn5VcihRM5f7p6sopx5slDmiYOqS6iz/a9r8QB3ew\nLl/O/y+Wf18tPVc0qIt/HFM7RaHiDH/0J1Vjrkax5lok3sGtdaurMLNROaZH\nHnaYaMFF2mFJ1mlfDzdUseGL9krSSYH76XbnKcUDBD1E/Rd6qQjnVSIeT20w\n5ApL9ZBx5goAu1HCw+FrzXajZLdW1ipoDXsYm1qD271gt2J8XWWi5jZhjwyK\nurKhiKFCQpHCiEffguXuy/jC0PSANQXGfBMzDxU+gsfSZgsaE5+4lig/qxiu\nC7VRDZH7jydszKPuUiXAn9akTb0zFpql/dwq/yLC3n0UnG4LzOxsYMxr0V3Z\nFNXI\r\n=zK0z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "0f2789fde305c3c14151848f8514a2af1441af58", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "3.10.10", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-builder-react-jsx": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.49_1527264206756_0.5761363474345147", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "07e5b7905d69e876483176171d31e635037aae18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-j/UaZGu0g/mWYZbyPwtl34NZgQgFvO2EIN+fZeDoC73JhsEtACGL+G2fSe1HYePwLtfr5FcZq5k51AJjG6EwiQ==", "signatures": [{"sig": "MEUCIQDtDS6mV+CpYUBNoTwQLVyzLe/ilp0efVQh2CM5ZsST+gIgQlDE+cEuel1Xw2rtspNYdMCRmpyfB4vfGATKVxKRvQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4558}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-builder-react-jsx": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.50_1528832865246_0.9067038515599013", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7af8498518b83906405438370198808ca6e63b10", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-lGZ51RyBTNftNhKN6vXD37uRkimCZpH71xR9VBogP4nb4PQj8gfFgpeaKdHCwp0oqFuaVD9C723mTLPtHAplyw==", "signatures": [{"sig": "MEUCIQCL/LTs/+hlnUWuErWjplrSOOGu3AwHPPWdH9kvTKRVugIgd6kYsQYJf2xmM/x+V1RA4T/bUpacImHTmBv4KWnJJpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4572}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-builder-react-jsx": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.51_1528838428189_0.6835826345955285", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "658e49cb6f8fa35ed7391fae155c842c7a12555b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-QxKgEOgKT0Y8MR5/SvtoNQeQ5UpLEP5CLyUBYSExcH6XqiOYIaFD5WXIKYAxNNaMr6OWKbKmJlVx0c/6n8FNhw==", "signatures": [{"sig": "MEYCIQDJBnknf+hncsaKZ/ToCXzYhoh84WQvp8ys+F9+md49CwIhAP8e1qIcRPRc64vvU7F7S0i2TqUCGNCiuptTu9yrCmeu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4571}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-builder-react-jsx": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.52_1530838777673_0.7583329723446646", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6580b7bf6665f14c8582b9fc266ca01f00e0a047", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-vCj30isIErYAE0/FgOgzYW1SgsMryKkymx5HGSn2EavOE2BsVHHIUieuI/nqZb2RFmQOAAOyZ2JY+CPSQhyzNA==", "signatures": [{"sig": "MEYCIQDSNLmwmkkNdBlPmlbFoqqxdW6f71aVgTio7z/yxx5sJwIhAKYLyttJqDwoV049A4TdcAIc21zZl4Yaoob25TQMW4o0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4571}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-builder-react-jsx": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.53_1531316436925_0.20560530478389727", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7e9b6a624b6406d438b44a8fad23d437a7613b66", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-Yj8XlXH75ZXAeJlxRN6nUmXi0l4pez7x4+XXzNKmUzhZxuQ55Ko6C1k6kS12DyeFpllZXmxXfvxezF4/4Wvh7A==", "signatures": [{"sig": "MEQCICkBLALVYhSwF1CldAIck/4XqNbzfuWeR1FbYgzd/mX2AiBbz9Lz0mThq+4Ll9jcFpbPk/mY/BnK78prPBsPYMTOoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4571}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-builder-react-jsx": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.54_1531764019550_0.8602626220778054", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "202b8c80c2eb1ce60bf1f33e113af55d9fb9ad5a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-pKpdLm6U7KPyAdNvsdVBRLymlaQvrX8XOyQFs0Mh70uoNUgyufcmhDXBKxKH/YoIfbl6+orjpNsouvsUU1gy5g==", "signatures": [{"sig": "MEQCIHydJl8Bcy5OIPbjtYXHNO4I6xvy/dMYtU57a2H9zSehAiBRvUTWFQvuQxj/yN55ndlCkRxSdvPOTQNNDzGczL1ODg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4571}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-builder-react-jsx": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.55_1532815662512_0.9343899306585333", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d1d92fa3ff51670f4174660fe7a649c7e4067338", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-xg1l6yyWxxE4NdCIs+d3xx9Q03BmktxI9fIfxKaT0y+UjABg6J1eGCJRBPa1s3X212gkUcEmYPjG6jo4ew40Sw==", "signatures": [{"sig": "MEQCIAC5ZimRDm1yjEBL6HmXMRkBT29MNSLMGpRilROJ5qWfAiB5V/A+MlnQv1jWaq/QUNclQOIWqy0QPLm/n9JA8xKP8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPxRCRA9TVsSAnZWagAANuQP/1QZHELiMr/SY5/9OkCd\n/r5b1SadMyJQToycMUaBoea9DRTf3NI4sQs7jlpDiYlaEBhU675OScL+OiL8\nGspFCCQunVNPvVqD55nLBgWTTefEgIALaytyIGxpmF1URh79NjoNUgUkinKX\nCqysvmiutt9OYjxEnodr+UAC9cGNpRL8CPsYr4Yc0Gn/bnw3ae8qeYS9eXna\nbcZuzirPUiZs3TP7/q/PkvcRJLmSBPCkf1Of7Q++cWqfajdeh6CkhFNGdRno\nt/XOfV9vkBi2lKjTHcy8pkKBn2ia0Z73Gb5HCuHEUZmDjdcz9l5zRGOsMxAL\nLnCzTIle7v7u3zdKAYYd+pX3RSzqS4t8T1B6B1j80+ddHMzlrXnH0fjZ4ub6\nShP9+rf2woYLPOdAYQ+HqokU2pi9fwJU9vLt/VhYaNKbCH+syhUVfBrYpOFZ\naudxk18iqmUWVsn9YbcyyTPbEMiVQtuok4Kn1VEOuTJHyH3RefFbJTK1k6Fa\nt6zs3bNnj7ko11+jfPj23PwOFc3Z6qT/LDFZeEmNYlze7nAwAiikcJHuZc0v\ngab5zxljAJAfG/UAKGhPo5s+9gxalnxfv6KBn/lj+fJca7IsGeU7KXVQ7i0Y\n+WsVawSHTL8BKfndQR4AjgEb5a7SgZ2sRQLSlG12soBNjpBB/MIt2D4dVDoQ\n/ik3\r\n=Jv8U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-builder-react-jsx": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-beta.56_1533344848985_0.5908041128069332", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a34e0496f1d191c08953f0ed0031e1a5969ba127", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-IQAnoOK630BwE94njJHLEKD2qCwFDTDlx37oNBF7UDl0QXSnuqAvfkUYwqcD/pAy/h3jXK2aOlA4G7P5nPHkzg==", "signatures": [{"sig": "MEUCIQDEzqLcmKS7uVZ45ZShNOnFW7R8wPAE9UwOS6d1uEy8AQIgBB/gVLepthhhcJO04JQ3CcfrOB6O5IW/VCKm/epul8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTXCRA9TVsSAnZWagAAatAP/iN/QoZ7vpQXhHKaQENw\nS8E7/LCOSJgw/VpOZqxeXGrsUW3VY9IhC4/pIZGZ4ON5VD8442rGaRAXbSQR\nbtbu0D1kLLcGsUgggoTJy1d0oEUJAHC6GbpLWXGDTHVxu5rcbqggj2rFtjJo\nSfgyKKkre6a8+5UqntaFrrRu2BiauwUkKJxQve06FCqa9qTpllYBsAfeUsxj\n93soHHM3RbH0t44v+vxqoMOCl3p4i6+dPwJswnK97fjaqw7XMB340+4w1Dtv\nuwjBR30/ncNXV3OEFohL/6zR3iB/8fpGOFvXipSRa33qrm2XUpotJKwyolB4\nxsEjtCPki1pBD4Kcjs/DTXMdrfvCmBhCVCYJUhnZNpx/rClHGMtbs6M8WkbL\nOr2H3hZMcpFfA09IE77Chu9jUGHtePSz9PtMbwdiFdStTtuSrB4okI7pNCH3\nd5JdaeZCJGIbx4NdRoqR0v+kzdiWKH0OoscqBCaDjkN+tElfXaPp0GZZcMsw\nT1j8NqvfG3NR6+4Nq9sZyE966hYQXKA2uL3bVInLbsV/6CW0BczktYXyuLn/\nLFRrJev6tyH/zFtntXQcubOHrMJVieke5myZBSANqYxIvmDsSjSzbip26ssX\n6QzX4YEO+v2w1wB78DcTMhrD4AW42sPEkBJwSkDgnEkeT3k0izG3NzXOUxoB\nNuJp\r\n=R6/c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-builder-react-jsx": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-rc.0_1533830359083_0.832486057602803", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d2eb176ca2b7fa212b56f8fd4052a404fddc2a99", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-lGSDCkRp8/2JMu0vBeayMLF2xLSiD1n9KZFH+zRSLtrvdNJFhifmzHJ9dYYBcDY7qDQayEpj/Ze9UpyxaU+oSA==", "signatures": [{"sig": "MEQCIAt4LYZYSmWfN9B5nXv3sjiShrjJbkyFsTXFsWM4WSImAiBrgNPQGs3rqHI+CQNFlDbdfNm6FpYX8GvqMq6GRyFAIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9gCRA9TVsSAnZWagAAaF8QAJEDo2xUbMzClYDWG835\n4IaxYjyStSIOIqjRzOqaKuB3JWFv7wtY9ywtc4s5TbHw0zw9qpFJFPoQYnu+\n7DlD4UZpjJX0qwmbNNlAnB1BBZbUmPcqcHdz4RbdACZPqk396LpoyERxQ3Hg\nHVmA1vpEL5uOPJKl5UUJsfL+T3df5Qx/xYfCifZsDIdqzAssp7rTZcV5bJ8+\nyxETIsADkTmN/66fY9j5mo1LkY0ReCZF0zAZvKjLDAhsceuBwra4UEOZOlgi\nwUNZ1R3FdsM9bw4W+jwNmNEXGubOxAOvLNHnRNu6mQcisuPXjwdLh6hTTo33\nYDWtFdilHTN3gfq84od89pCuxJSWC5HQtKmSOtyUhjuH7w0fSe9krWduAli5\natWlqkL+4YcHTPdVVxX6OM1gIWKKTM3pNxxl9RZBuiKVt3O/R6zJSHGAOrJ/\nSu9AMmhMMlRdzwsQlUuNb9b3OJ/2ZnZV2tg/NGQbR1L/kOMkygnLMVfiYugv\nkQ1D6JxFdtGA2MkwBxoeLvzC6eHhAL8kw3q04YXWFALQgtif2piQl931pplH\n/PBcMKiW8qdh5ybc1NQ5ZI2Kmhrl1sbu7f9ctrNghOKNB6VJvg/w6mmtz3PS\nqV+dITaXkUoWE4F0qz7LiYTvq/jBHlhVvT8u4qNMWLuRDZDZdh7ySpyqK4qW\n8aj4\r\n=WtfO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-builder-react-jsx": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-rc.1_1533845344168_0.5804784860395049", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "43f40c43c3c09a4304b1e82b04ff69acf13069c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-lxGw4nT8AO0QVdmGdWzi35rZxkTp10+igJZXnp37X7BYTWzap/gayBCvchIT21k1noUowgJoeURPoAFCyIk8kQ==", "signatures": [{"sig": "MEYCIQCPOZVQ4ZvKZo0BLGSCISpKqFeAwM/zGzcXTuz+YJ0k/wIhAJZyQHUu7Ft6IxAb05jtJzhZIzjEa9ZZSoMvR2jeDix1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcaCRA9TVsSAnZWagAAShQQAJFHhTANjgDx38U5SXmM\nkY8yXey6v403US2/fq3oQvCUOtahdq8nSmA2LSPUM1q8G4iWhzpm6oo0Q+j+\nsVs3rRU3ePSKkNt+IDwHH9vjKS1vlmE57OuMl+hXb0ECIzlSUTrBfsUsKwYB\nG+gijYv4JQkBk240mwGlkDcKX81kN9g8XLqqYGJoK8m7Du7tAHEH5GM1p5Bv\noPmqmsVKbyrTUtNBTMFVKH2ic0010meTma6lQ/gf5UHX/v201vTzllfzPT9j\ngT+XMTFw0TXE6BMp1UHyu12lh8A9fdacbkpb3wPFTiBC7rClyNlkgFQC3S8S\n0XjxlNdowsORor00gK35C/9huGkbgt9YjBRjegiRybeYI96EfQ8NSWznPamG\nohoBA3iLXPl+R7X4ZwTaoolucmQaTfIKnkiVhS1LbxRrgQsSMs6MD9YJ+KEx\notwNoc7euMM6gatu43NzJe1jCWsQKdOFWR/MY0nxa4ewfvZLMH3VnyHW21oL\nZHbOKMGLd7PrqZeQEq4q19eTdZfErmBoe8jqDYo4yTzTh+XSkRdY8Xybn5+s\n2Xf0E1RxDdVUiNGEGC9pSD2Gi8AlqcZ8biKyu8BdUoXeickodlA1zvLVLI5X\nVfWrq2/zw94lCxmlxJ60HbYBUpK0NHzJ5UWdWGHiDvBGnxaPd7IbN3sJO5gy\nOXEu\r\n=n/8N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-builder-react-jsx": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-rc.2_1534879514024_0.9090188284975942", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8985dd8c54367ab66f4a5f329bc31503024490c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-dDMD0a5sW5U669UWz/Nw9HtAKQCJtDoQ56k/q1icF4ISZ6EMmLz7DnQotYsoxyIXeq0bOQQ5/WziBgj4Tpel6g==", "signatures": [{"sig": "MEUCIQDHJaX453LaznunIVNuFjgMblSuREpLflUnF2EINsc+EQIgccpD6d+Gkd9duw756AhSf07W1qzAa6zXmA+ISdwpY8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEm2CRA9TVsSAnZWagAAXqcP/R/L2FRCloKMqNAt/MYX\nYuM0NFUQmOpdGBzgs8+tjUnX1w10tBzjiwSGNVUMgNeQ64pWLF5asD3axMXM\nCBB3e6Lw7Yy8aa4vn5eP0VA2LYznR2llddHuc/3HRksqLCwOMgrjV9kuzOgg\nQVL7Cc/ORUABWoAC/oJSCic67QYycsmWn3aiZkeTgve2g9IggT+Ue1fRnyYw\nx8/R0I1yTKuK78J/w4aHVvTsdMkpF78zlIAPJ0GlkxbGDOHV62Ov9JTcjGKL\nmuKFaiTkWZ/MkldkG7BB084bvoGvBKOPxCW8ZRa5wbIgtfn+Udfc+T6ecQot\n/h013XDtcYIRcBfbC3NCs03IRbd3uDD9xxJ2vDa+9DS5R25pD4lDcBRC1fwZ\n6m2nBKXnmEHIXhw8JEXMX2FJTrTYWAPZoqn4k6LKoNpezUIvHMpuptHppKj6\nCTWW2yAecgv3iF+hYDjVfno+g3RtZ9mlGwcfqfn6pQwjLxA+VT59KpWHWGUg\ncbV9MlTz5sBTcGMxQFJBg8tGeP3CwFnTeKx6YrVkVBnNeTRiRsBZchPlfdBb\niXsaBo6ZBKOPagbhzeKRGbrKgc0rFDWUhb6zQXL2rllDh9Il65VUFJr+LIuL\nyfUv5yTqWOyRvTMKIUDMrtp9X0wK0NbL0IuK2bmzF9H/zuNFQOCIcVnnIw6r\nCifk\r\n=1WBD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-builder-react-jsx": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-rc.3_1535134133552_0.34484701414130314", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "85aa56045f9852933dc6a23fed6ab3783bf22f32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-NrWuWaffAumfrP10/BJs8oEXNNhSEzCovP3Sp8SMgOCA7LcvS4IkwAy6Nwbf6H3y42PwofKYy7+fIz3cncqpgw==", "signatures": [{"sig": "MEUCIGUQ9LJrQBasr82LA6/feQfUf/g0qhk/CWqdn7NVGxGKAiEA80OykeDwPiKlS3ySJ7xpjRenHfhV4GM3dyqUfqG+rC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCqgCRA9TVsSAnZWagAA0HoP/3dSVIhJHi6BAUbaFwzf\n2OcJZ+aEDSK83qnssvTui3xvh/oSYIfnz0vjJ6wPFqstTAPhRu5+AZssIkw0\nn+3aWhSMhqxtz8GmbzTV48Oezl4IIhwmEWmGY4NwqpZA/rB3BaWfHcsi5NWL\n9BiZDjfnGrErQLt5Q08F/rybffsPr+7sTcjp7GXnmuQ6fs347frO4mXkAm5r\nkmKqMfnmU6ux44if+YCKo8xaN2y3pAiX6IRkdkSAfv25GUN/wt9fk14T+uxs\nTxO5sOieNCEr+gWymsjhgr9U1FB66g4FAU6bOTuloKmSd1FGvQj9sjex4v9y\nkb9RagirXR2tqaxF6TSMeDAvEIwIxJjFB2XzZWNC1YYPNzdMS2P6mKU1PDuu\nrSpnRzdEErWxJyaMSdkfbKJKmtEtrjprC9J43zfa2yQaV5o4+/92U0usI3b0\n/cspeQJx/ga0hNKcxIz4oA6ESmg1rLhb0iB9kIWLN4gwb4nocNfcIfiDu9zr\n9op9NNMegNXupHEJv9m7DHo6NBEosXjyHDF5N548xvhXK/FRFrO7yRP7iwzm\nDeYEj4d4CLt8qVTHCS7u4yL0M4ixgO6Ti2lNAxSM1+QXLDWfTYp7JsZN1+S4\nPLJOgJ1OzEdHLZhIWsy80WzksfjslgGVY0IWT68YCaw9J+ezqZHD+sVDv2Ud\neDTD\r\n=glaH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-builder-react-jsx": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0-rc.4_1535388319971_0.15148198095778231", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "524379e4eca5363cd10c4446ba163f093da75f3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-0TMP21hXsSUjIQJmu/r7RiVxeFrXRcMUigbKu0BLegJK9PkYodHstaszcig7zxXfaBji2LYUdtqIkHs+hgYkJQ==", "signatures": [{"sig": "MEUCIQD6pej6jsv9Ov8Gzao+aVQ0x7wrCbDhRWCYIRJnDPaoUQIgBkluC4o/970pB21Ij21dyHittsbHDwxi3yncPay+6LU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCqCRA9TVsSAnZWagAAMtEP/1QW3x9LxGEjCzzrQF88\ndcjoru5qOJC7ykIw/YXcuwTlGkTI6+H7LdXRKSPu5cbKeUD6wo3+xFNHjT+Q\nDp/VDS91GEuqUPxUA5oxX7aKS6sf7fTN/uwsDuEyA6ZczKZ4rp+mnApFqpUx\n76n59OzFqxf4Q8cyYYhh0dLWXyOHI+II8+t+xRpUZs9jwW76Pl8rNcEvjvrV\nwEeUu5JjEN0mWqChroYJp8TPc56ITfROSnayuyC+3SDqwjursVObQnfsJhM+\nXi0jnhqS/l5ZbkSahAji3VtLPtvhuwRTAIElaLDFtky8Et7K0cyu6J6wyTfS\n5zwjGXLu5PXL5EOT1AJq61dOe0WYbXBuk4VNEWA3BbO2Y2WtiInlxIkpSaDf\nglLoU9eL2s4vVvUiuHLv7h4NCRsfw2Qrhy+ApEWP+mjmKbLLnu1fZ7N+P6/y\niBjx0XW1EZqnNlk/fuMRfs55n3K9jgoHb+hszdW3XFZBJ10ZQChB3W5xly2B\n2vVsHVEHq1mIcGA8uWm4iJLdHLJrXv0zSs1ci6c2R3YpSHf9LS2KxRuwY1Zf\nYpQnhX6Q9uYK4lAU46x3zk55AiPB8wvAGL4DLokxf4wi+Jz2SGGPPpJ/zTPs\nWjw9j8GPoq8UNQJVxDX3/NQMVQYpcFlFtGYtnln1tmKdQXfLSzkKAPB+iB5P\nwaWN\r\n=uhsw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.0.0_1535406250138_0.7134588488797431", "host": "s3://npm-registry-packages"}}, "7.1.6": {"name": "@babel/plugin-transform-react-jsx", "version": "7.1.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.1.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e6188e7d2a2dcd2796d45a87f8b0a8c906f57d1a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.1.6.tgz", "fileCount": 6, "integrity": "sha512-iU/IUlPEYDRwuqLwqVobzPAZkBOQoZ9xRTBmj6ANuk5g/Egn/zdNGnXlSoKeNmKoYVeIRxx5GZhWmMhLik8dag==", "signatures": [{"sig": "MEUCIAI7KTzEgQVP0bNZO5VHJtib0h+5IvczORHCeUPaMnPGAiEAiNrp/58DseATuRC7+iw1zOKxoh+g6Lm6uSU3EKFBgN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6z3CCRA9TVsSAnZWagAA3QwP/1eIdEqd1tiqxSBYClTe\nkz4EVvM8KklOCJv78LA+di+FC0FjDa1j0KlY881HT2RQD/WEEioq6qi2XzHh\nFGdiAN1dp6jxXKpjAJibMFDaNaD30oWBmxpzKNTjXf9GL1OMkgPORtVeQ5Xx\nqW7EtSSbY/LvEoHlCwElapIYp69/IJxal6IlvDAo9FSzpsVFj4wx9TMvm+jj\n6Xe9ZvrWCHWkuGSoK8FIMJDccf02UmJhZhDQ8gigQBJrSLCmotMioYol9zUK\n4PlSnaTVgWY9ebMwKjCqooTisgUDC0i4klErpXphahpsnBBICrCPFsLBWvWy\nevJYg6kLe3NyHZ0j+MxLS157Ge2ffe6SsVJS4k0nkYRi6zxAWOh72zxAM9AC\n/nkPDG9iZjExdTHom25rHJECmKQogStoyPj2N97G/VwhfkF+AcG5a8GXBrLi\n+Z0q6swPrPBXQtGUETWWd7D+sVRtpw6A+GYYaCm8zYzMHGTFwBhO0AYCJJh1\nVm2hagRB7hCcfacxWTB8H8Y1xc+wBEsdMjP0+4DNiSdpIEOOs2LJLZGxBdOc\n8cTv9CU4EHIaoV2Xq1V6sw7V9esHFNDPqccQXW/M5IcyOyCIOAbVMN3cg9J+\nWhwggzV5cALHNyrt9mj+rsHirjMSt2IaHlnq90UT7KVHDAyCA+zVQyebPiaX\nqkDq\r\n=CPwl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.1.6", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.1.6_1542143425963_0.44045996577674473", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ca36b6561c4d3b45524f8efb6f0fbc9a0d1d622f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-h/fZRel5wAfCqcKgq3OhbmYaReo7KkoJBpt8XnvpS7wqaNMqtw5xhxutzcm35iMUWucfAdT/nvGTsWln0JTg2Q==", "signatures": [{"sig": "MEQCICPQTuPErManQjJ/K8OHUA3PCwv8qke4NmbueknNrZHdAiA+2DVYS6sPO4b5bX9lPNVPIhctG1gv6GxL2k+fZoHm0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX3YCRA9TVsSAnZWagAAZIcP/2D0EbTl29MTZUtls8XQ\nTLMOkZWDrFZvYvncnBLQJ8DRvPgmRz2aovB123f8gTtcZZ0uXXw/iV3Mp9Gk\ncBvVrf7I3QBzDdYU6FysJLhMYuC4/RdCLQ80SufKjvuy8XDDRAcsKWjUWska\nQuWkrvQ7E6fQ26+/LlrRULla2BuU7G7Sa0liR752lHBSlxbBL/r/AOFJvm2V\nB21TqfLl/YagbMZIm8MqNPv9vQLEW6UYrXwomoVZAl3X72Qi4/xjtilg3u0Y\nLHRvlEeXKjIqBiuwg0h+SvWLHrfvJ+0o9H/bYWVv2iEqbM797hJlveC7Q8Vl\nmtIZrDa6umyvrzoLyyBRubNSi2NQ/V/UTZFI69sbKOvkvG05de8bk8mQanNy\n/sYkxQ9MdT5jNhxXhnet897oMisBufkjVkpSimqUt63szSVcYTB5owXIrHLw\n5+P7DQyAfIMan41cDV9gbjbAuBDHvl41igjGAkZZr1MI+BnxcSAjLkkgOH9g\nNPM9wV4bV0EQW+Fp21UoAoj5FdfZ58aK8/2UmBNpJ61/spbyfHZBmXMC0U5f\nBvgQKz8oECXWbHtFrjjmfQsHPXf8qPFs5XSjQOojxmIcMt+NbT60TSekSPoy\njjZKCC3SwG5Lp4pOf+URf/nKMbTi2byPzvHnA/0N2WU9ZraI/iPNk9o/zuAW\ns0az\r\n=Isyl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "description": "Turn JSX into React function calls", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.2.0_1543863768158_0.5179808321028434", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.3.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.3.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f2cab99026631c767e2745a5368b331cfe8f5290", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.3.0.tgz", "fileCount": 4, "integrity": "sha512-a/+aRb7R06WcKvQLOu4/TpjKOdvVEKRLWFpKcNuHhiREPgGRB4TQJxq07+EZLS8LFVYpfq1a5lDUnuMdcCpBKg==", "signatures": [{"sig": "MEQCIEHD5cZTB6dSuwnA+Ium2O7MdBtM8rCOrKUQ3LnfNxrTAiAethRGXU7qseOrnM/MuomAjNl/doiJgb1pEDy7n4dNsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjxaCRA9TVsSAnZWagAA5ygP/R7EcnAdHr9opKRw5NAR\nwEH8pVC6SKqbytOjhVJnZgfBdwqAgOoDNQIBlSZFJXCO0Cv789UbzTbPJ/k7\nu5IRynC2HY4pjhkQVq01QJQ574CfONDXpGyEvS0GoO6Jj/lUWSx91sGSB/LV\nFksazqNWCBkb2oJc6q6n1LST9i2Y4EWvnybIVUp4AK6+ZqAUVhObri0kp7n3\nvVceD2DRFv1fYpI5gZrTyd+NP93WezonZQOlgEepdkmhTRTBD4cf9sQAvh0P\nIhRK8d4N045+R0cfYkqQdyRqBjz/Xl8R/LlE1WRRgG2vQXaZjpg/rcs1v3sv\n1Kg2LHGJrqtFcsod24LLie4Jf26QsLnFSH0O/JXL36dndgqOFm6IRHnPaiJu\nwF5J5r13UdJvjEr/O0TUMaffhN+zuuljeuHs9dO3/ic3mGrRirO4tVOqS50p\ndYuimmO3dYLEBvs12y1BHLZ3CWW0CgYRZkcfB/NRW7ByKDBSwhrnfiBCgoKf\nJ9ZzrKsC9d/5A1mrnV7Njwd3OrqplR6uAHnjIDIbwsUuIDTwix6RvgHPXRNT\nXH9mJkEA6+yMj1TlP9lUZ+TkUfUk8HD6finIi9/coXPqXM7XeWvCr2K731+/\nkK/Q30zPQ3KXfkMXzLMLtj6mQIzH7Rg6AKNgm9J7vVzYrrNJ0NI4oj1RzEen\n9siT\r\n=TtF1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "6.4.1", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.3.0_1548106841782_0.16092438730348269", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "834b0723ba78cd4d24d7d629300c2270f516d0b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-mXhBtyVB1Ujfy+0L6934jeJcSXj/VCg6whZzEcgiiZHNS0PGC7vUCsZDQCxxztkpIdF+dY1fUMcjAgEOC3ZOMQ==", "signatures": [{"sig": "MEQCIBilc8g7+DNPKWvFKIx0lOyL7hZm0QIXqozTSNIph9e+AiB7rCL0JsqLrh9mcmnk5dSEo1lPIN7kjKxnFBaEp5SBUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSwCRA9TVsSAnZWagAA+UMP/0a0WctqZEflXFCOx11P\nKNuRy0NPhEWvbdAMACOLbaZ7gVJmvbsdeQWmwejDx9XncuEzwyBi/3kHM5OH\n3mf/uob+0hKouw+ars6ZxNk9elTl5snFNsQvw//BB9q5D9oUaL4VbJWVo3fd\nraqM6XNTp5FtYS/WbzaYRiVKQKnjJr5oVBq7Mp3WZYkOcgOp/dhelCqos5sG\nJo4bgl5xVlz6yc3ZdARzJOnj0q0gMfWnxE9k9QgUMUTlDrB2gRcEzA5TDyb/\n8J9E6nV5P5lPJDckizZzXjvQQX+Q2i8l/ZGMW1nZKmo2F0d0lUQVpt+YdB5t\niPPmdxEBglwx4I2wZEC/nS07E+4OCwN65focdTBybJAJ6X5MDmgAz18PlE/s\nf0bIDlLBrbQdwRbMasCTdEfDE6wQP9Q855A5Glk0Z9lJY/m5J6gLC9KWO6yC\nkN9KsQy4PzFdWPRQQo35IxF+Oqpysn/1Y4vgyTw3cT5zn7JRz7mUhvuv+8p+\n2uXTAoHun2szaKOgs8bcEgEHria/dbD06f5e/Y/DDqWxk1drA//PIDM9DQm+\n4HmkoPi0DYMuMoI/eSUx2ZydkPZFmsliG3IGqwwOE+YHKB26dvpZgyO628Vk\nWThmmXIOQKzUgzgQ0wy/D32AfG86XLn9XftFPQZwVWTO5IaskiNHRpKrvhfZ\nA84b\r\n=DWBm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.7.0_1572951215725_0.9286565532606241", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d91205717fae4e2f84d020cd3057ec02a10f11da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-LixU4BS95ZTEAZdPaIuyg/k8FiiqN9laQ0dMHB4MlpydHY53uQdWCUrwjLr5o6ilS6fAgZey4Q14XBjl5tL6xw==", "signatures": [{"sig": "MEUCIQCQa8lAsEA8bN7bO/8fHRw6tXH856VhD27ZvcHT/NNB/gIgWo7HiAyS5fkvp6BxWfBp1EQcCjuc6q057V1+Ja57afI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBECRA9TVsSAnZWagAAg7MP/iJC6Lku9QeOFNtsCygt\ngyxpNPSvyMqUW0uIdYgjZuAQIcq9+G4rQmQgJ//Rcy438D33OQ6h1UNPW8qP\nN0kO7eFkmFBJqHBaMeLQieX4EOosQWc/9fVEjfg37M0o5HhjO0xVXnr850NS\nNx725zotacAb7M1gQAuXJkmuQ2z6lPKNdVmevYyK09snO1eDcghIGHTb67W8\nslks9RXvyolZNR88i9zc3LeaK6AchyIPgXiUo0Fzk+HfXBoe9j6gMladS+AC\nk2+tZIpwHxb+xEiDALxZCISINbA6rz0i4ho7dFQHz47RCbHPQYO+UjWwLBan\nHSvQ6r0r5yJ8C0yEUidJQaJav85lqYNF2b5nAidcxPRcd4CER2leeVut9tbi\nE9hktZLCJ7kv/yh7BR3bYGoJWvLoVxtUwy3MYCOMYWuPLvY7gzj7luAgTq15\nK+CfYJsOomPS0NcpPfYpV8NmKQmvy2F3o8iomcq668lD5qmGqO7PtVXLsvTK\n+WTM0A3vX7225JDaXywm6UwSy2fLFTAmcBGhJvYbw433SnWGT1d0twVDkybW\ndOaZ0C4a69syWOrI/GdTNstt3ANJhPt5zQxtF/XRjsl3k/MuMQ8kTdTyQZZF\n87qqrQY1xPCWQ32qmbjSKJsHCx5QDY7N5abFM5FZ/GZsRRokMRqAnMcpH/3U\nu1rF\r\n=7mtA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.7.4_1574465603804_0.7029697783122215", "host": "s3://npm-registry-packages"}}, "7.7.7": {"name": "@babel/plugin-transform-react-jsx", "version": "7.7.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.7.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5cbaa7445b4a09f774029f3cc7bb448ff3122a5d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.7.7.tgz", "fileCount": 4, "integrity": "sha512-SlPjWPbva2+7/ZJbGcoqjl4LsQaLpKEzxW9hcxU7675s24JmdotJOSJ4cgAbV82W3FcZpHIGmRZIlUL8ayMvjw==", "signatures": [{"sig": "MEQCIC4ZcjDhJ49r2cx41T3TeP0uG+1PIaHgWMk4pVAaljO3AiB/b03h6FIT5K5UaMh75lzG71RKEurzji8Z5Ch0iOwtmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+snyCRA9TVsSAnZWagAA9ScP/2wDNsi8ZWkGVQKsQZMc\nWYc4kAw2Fk8ukEedauN3EuXsYD5wNsZz24mM4FOwgJ35nhBeraq3zoeJYNkA\nccRmFytjDLp5xc7UdR12XzH4wuLz+JKVeXB1Up5QbNOGpWfAehaMC/NJ3Wow\nMLAVvPq1o4/YQgN/1i1/m39B3eTMCgrGXYnbQVGMzBNuuOHbgSj5a7bcu1Ev\nzLNKMC2vF4Tp1jyNGF3Wm7CwJTrYJQEkqj8bzpEne9v1e7d7wA7TRFaRVzVm\nFXDz/vj7PtNRof9C5yOE3ArjiFzv47kcOAZdHmSAsj8mnA31Gr9Bw+iyVu37\nbbm6jRQw+G9RHGmG/oLlD5hVNfYV0qoIiSkMZuttOPfvwolzGXqY+dfGNy97\n/mOTbk0T8OWILnPhxHFfOt7L3yy/Qx67/QOf1vCDkNa/JEXmCFhE58LI/hDH\nxD6RHf7UDcAO8sISzMrJ4uwNTkt0qHn+TZFlaXYtPXM3OSXGGW0B56XWjuR/\nuxMILEJzbcfBGvCpVBtdP2F9lCnY8wZCLFugLF0YKT80aTLU9hE3hzhlgbL/\nX88EVcFuBqNPoVpdqI40nl3UpoDjIy0gCiPAxbscr72rGbu03IbJBEmYLo1Q\nUzaZd9qdBqtkmSnbCIEU27iWcV9gQEOBCUuHqeC9AcP/xScrk0hmh0NhmogM\nH9et\r\n=8O46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "12da0941c898987ae30045a9da90ed5bf58ecaf9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.4.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-builder-react-jsx": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.7", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.7.7_1576716785922_0.4651714876196509", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5676f2a13befc16fa2a78bc557e02ff150c02a28", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-r5DgP2ZblaGmW/azRS9rlaf3oY4r/ByXRDA5Lcr3iHUkx3cCfL9RM10gU7AQmzwKymoq8LZ55sHyq9VeQFHwyQ==", "signatures": [{"sig": "MEYCIQC+mxdETcZjypKdbyagXQ5VDuiKYLG6X7SNPDe4Z0QYgQIhALuuQWatGg5pwLeKflPnFyPiKdOtUuVVAEL+U59PX0nL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWLCRA9TVsSAnZWagAAreAP/0fhkeaoo2JtxXVh6Knq\n1hmLZTNvY2dERLkdhBNTyw6G2GtBBVvID0JE+LhCve5IFcdmzuniSLtDneU8\nsCVw6zntlM6BmH4wUPO/e0IC2yri8fAnQPs2ewXJB5ClivZY1dCJY4rqf0Wz\nQ8gQYEIoH09RmV46CoJrkDT1q7WMiYiqTGSgGuU7KX2som94/8T5xZMdOK9p\nD06gGpjFVVURAamlBrqcusAm3V0MZcAMWy51a0wyZmZmBOam4j5EibIBa+Nb\neqvYQImXa5dItPcZxlXUt2bJWtudMCfQ/au2RzNqCHNMHRDvrmc5VLGzRcBl\nEg7R3HHBV5NEOTRriLvk3pvWPWa9jXGs2TQtW9LTQZnvCr51Z5Y6+ysJcPgi\n+XjyPtSaeK6/+rzcWKYEpiUGURoQBexvqZY4Ro+QBZEFAULjs84zkwkN7pkt\nF1hIuBgjGZ97haTiYsy4zIsIpcLQSeazwciLoIXzYDLTskRJSORPLClvSh28\nAxYKUfSL+vN/asN6tGs4voWzO0JT3jdcl0iIX4ZmFQh/13F4zzjOXSTjXW7a\nImimNUsq0bKGhHSK12AkvBtg7jSnx+68bHdGnuqNtmtrJCGelznbqjd2k6t9\nngyMaBd/UGwDLjDl7jmfOGfHRXr/PUDk+zHLGZOrJqlIh0W+lrkSCzb8jumM\nalTo\r\n=g+sp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-builder-react-jsx": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.8.0_1578788234698_0.04868464943779749", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4220349c0390fdefa505365f68c103562ab2fc4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-r0h+mUiyL595ikykci+fbwm9YzmuOrUBi0b+FDIKmi3fPQyFokWVEMJnRWHJPPQEjyFJyna9WZC6Viv6UHSv1g==", "signatures": [{"sig": "MEUCIQCpZeLSRGyRDTxmwPThHzqEWQlrofNZZuSuNi93Yc/SyQIgPmtfbCsb9QT+u0okbh+sBSrn7gw96zo8IMaydtm3i/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQvCRA9TVsSAnZWagAA2kMP+wfk6Dz5vkZY8fcFQp1G\nXFLoEbnkN+0Tsxq8/jjiDFk6p+zF/E83/ilVpKpBnspiIWCbWPhVo0BDDJA/\niAcDL/WO74eLmPjohj2ydRX82V3CKCCxBZd7qRSchvHwUEHV59mW/uaal9K1\nBSD0opOsAaguNHDH1IVaFK4T5C45sjrZT2etsRBqABkzgRS1ZdokoOCA3sxJ\n52VwKHWXD63KzphLpbvGH8WOwGXrrtQjZtIZJsQU+gDkr5KKetcxYbXgRmVg\npxJEhfaAGOE1rBUwj6VFzvlyyA7OR5zMAuWitKM6K2UOOibDhymEtfHRQoz/\ntmGam8u+KjvDe60TnSw1FLu8/yGFUp1x1fUl8shJ0YnJOGYdzEvOCjVOGeBx\n38oNwnxqS76EpdXP0Tiiq5wE1KNqVhcoGoxIAB0SQaFTFczsKNeCMho6apEs\nE5t4g0c/O9nB9d8n7Hy1E+09YkRE00hISCZKa/yQcQrZAEg2o4vptXfxubhX\nFmoW+M8xZDqjqYIV+2dBok0QeJjunKOc++Ni58fvuF0ESAA9DDbjlVBl9amW\nivPc0i399tTrv9Rv/9LmpKaaIvtF2wNPU3ZkVsD3dNGkmch+V4htsddlDOk1\nx5UZQA9kRfl50ChlbJ8WhoPE8x4ow0/tsbmR0Au4FF6RQUxZAEVA5hrQqTKH\nAnWQ\r\n=/YQ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-builder-react-jsx": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.8.3_1578951727448_0.12831480999552936", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d9f0e10a0a154f86fb2f3b04ef856d709284cb25", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.9.0.tgz", "fileCount": 6, "integrity": "sha512-eOjxtrHDt0R3WRTyya6yxV7LkC+wRgTkiT8UGHUh7TPYVqIltDmY2djm60283OzD1R0YJB8tJij3tLNlKFgnYQ==", "signatures": [{"sig": "MEYCIQDuwG+pPnKkiWifH4qdry/5xe9yVqa8cH0NXiiRZQSkfwIhAKh7WIgjb6VSWfYjVvI2kR7Kx7z+JftcKbvnrnnj+ij2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPaCRA9TVsSAnZWagAAt/4P/jIcbkyCdPAPtFC/bvvb\ngiM2BPGi7/9RviAQXfVQttU3IjcxbKGytaW5INkUjl3ZXql3FWESMDUQgCFW\n0vlibZLJ7Fn1SkjcdSaBvEbqlvBm2X+q/SqeezljOYWq8ji6yHfB21yVLUIP\nIaiFvngHcWEeGazsLa+nfEhuKEXm22jboFeKlcBSthjKRLLY1smJWxZe5ByH\n6TwfWFZBA5jAuREQXkf/tLf92lexscKIsBF0xjknGN6gu9IW8BL+WyJl2oh8\niVtdHZ8I5HSU9m3xWsGo18VUQjZtDruX3qW7CnPw+ygzHCW8je0z16OWT02k\nKIdokx1zlJMy3rT0UAP9m5SMmdlBEgWclCSWlpxyRNFkGvM+dIrvXr8NAOnV\nD7d8F+9yU9ffCvDkbg3xue2ktgffpazgM+Ti/dY68y2If5Pxgw+CFVD9yOIX\nOSXDasIoGhB6HHLNYtpulj5Edhh4uvqJXmxARs1aC9mr3DcAmA+daS1qKN99\nGY7e+S40RbOHcxgZFOVmS0JyoudjIJDilQai/GAVSYrGTedDaHZzsaS3o8fw\nBXFYGNiC53fPYVkjZDEt9XSX40320bZ2rPFeyzhBstzjsab5Csl29q3XmVfu\nPQwb5vRnxmYeSNRdWWfeG7LgxSTtghk/Zs5dw/q1b5Ae2nhwdQD6cJlCSErp\nrn0K\r\n=Xduo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-builder-react-jsx": "^7.9.0", "@babel/helper-builder-react-jsx-experimental": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.9.0_1584718809836_0.9919163307718697", "host": "s3://npm-registry-packages"}}, "7.9.1": {"name": "@babel/plugin-transform-react-jsx", "version": "7.9.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.9.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d03af29396a6dc51bfa24eefd8005a9fd381152a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.9.1.tgz", "fileCount": 6, "integrity": "sha512-+xIZ6fPoix7h57CNO/ZeYADchg1tFyX9NDsnmNFFua8e1JNPln156mzS+8AQe1On2X2GLlANHJWHIXbMCqWDkQ==", "signatures": [{"sig": "MEUCIQDc53oqsmHdXsX8uuDyI97Dti9zYbez/KxfByYQUzPQiAIgFdW/e9nDU4JZrOpp7yM69ZGXOTaAYT+lWOoQYq+/FkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedTrlCRA9TVsSAnZWagAABoEP/RSqfPpzxSAvRKZbXi3/\n42dzOLVPZzYFJPLLwshA0Aw8soPm2koOIXB4fhrotnNJ9LhYu0riYBH1FUeX\nhH1x9640aj31hu6IxRx8FATIuWVMXeyOE2xVYycsOXEzhoKxIcMmNeJbqOjz\n/cjkGTanJ02jG/kXBpY3zSvgCVHt0lync1Ccgn7PmUliTfNe48Z1PtW4iWwV\nhDH58p4bkvuUv820LkdFatLgXQFjHbBe7Ycg9XaiGwJwBzeREnxRUXBhY7Vi\nyy2lxUtgtmBHV/kbhUBUUNV3X6j6GJoQY0l6QlnJgjTY8X7LPpeEW4oC+zzr\nnqImImG/FcYT5bMRzteTc7TeHQdGTbh/ne8CBZQM134IPPWAvNKzjGnAGII4\nE6AGgJMEJPNDygfZwm/jBN8CSoK7HuF9d8HtcPsmXyr3P1ITGr35qVlwiR70\nnz94IE6jEEjA0v2FNNK/sLWdgCI+/3D42V/2e8AimmY0hGC0+usAIixnBge5\nRZIKR0zD52uKgXM12f1d70v3+04DPKoMKVZ+XmZqZVoCTPM9mZl9ddyjuhpP\nQbuVBYzbl/kb+hPW7HhDY1J2IRj5sY4y9yjNU1QQjTOy5uY8MR/tS7f+93i+\nQXgO0N/T8MuHd60xY6c/rQYTiOmRVfJtEnb6TgB1OriQxvAR7fN84NVMt1/F\nA8n4\r\n=WnSi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "220ce702a1e02f7c32728985505e0392555b0ae7", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-builder-react-jsx": "^7.9.0", "@babel/helper-builder-react-jsx-experimental": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.9.1_1584741093357_0.8072867643448551", "host": "s3://npm-registry-packages"}}, "7.9.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.9.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.9.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "86f576c8540bd06d0e95e0b61ea76d55f6cbd03f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.9.4.tgz", "fileCount": 6, "integrity": "sha512-Mjqf3pZBNLt854CK0C/kRuXAnE6H/bo7xYojP+WGtX8glDGSibcwnsWwhwoSuRg0+EBnxPC1ouVnuetUIlPSAw==", "signatures": [{"sig": "MEQCICIkzGGFfqPlMDRF10V5UdL5G5Y3Nw0WooUhCjErDuoyAiBIivEnSwP42cLr1DRjzagEzrltNQdKydDmbfUQtQZJHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeecVXCRA9TVsSAnZWagAANt0P/juwFoFFYV0354n2EbVX\nLQH9ClVxbY6ydUSyFKoNVSxEMk8Q7wmaZaNA+3HmBf1rLa0e3wiwi3hu4j2R\nsW4oh6CmFtXOTvZB89SRh07hNRElT9K6Ei6LxBjcXiJsp3dh9S5JiMHtypPN\nIr8MTnSKbZa7o0owNF6zsB9ZEyrqA0oQIbC+dUd5aql9BK1JMz48h6USB/Wm\nle6NwygVsqnOSz7W6PSav+SJP+mCziXDC6cIQU1ODPXEWemftx+c8TX86lKx\nMFPgCLySIJtTAxloHEUDN0susj2rmYvfbrLFuAdgzUy/snXHOQeujZhIA/4M\nqq2cIrscYnGzBXxYMeR1QUmkbLBuIsl5HJMIiiemyqBw7dSsHfl0JY5z15iS\nB+3dNuDxWuIQQSjOWptfvAZD1EYtBneJZyq/m3aaD7v9zinnQzYhNjCyiUGl\nXt4r9NHkBKksAJBmx5/16AHjPt4cmtFJ9LBcOVcElYP0SNNgT7OfB6iGgdu7\n5j23zcNQu63AlsB8GvGyrJKfRE6WWRU/vD3N+LG8D1+SU7sQeESxb9OEMmmf\nGlArUIF9sdTP6P8N366NE+4+F5XUyGT8DRNQNvTHQAgw/2eO3Dmh6sE+JHqg\n+Rx7ZB45l2/46cgLA3q44DPG1ui29snp+DqeIawL435/BjVkWa9riRix8Gxm\nLPDm\r\n=hyUY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d3cf5fb5f4b46a1f7e9e33f2d24a466e3ea1e50f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-builder-react-jsx": "^7.9.0", "@babel/helper-builder-react-jsx-experimental": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.9.4_1585038678468_0.5090791824152814", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-react-jsx", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "91f544248ba131486decb5d9806da6a6e19a2896", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.10.1.tgz", "fileCount": 6, "integrity": "sha512-MBVworWiSRBap3Vs39eHt+6pJuLUAaK4oxGc8g+wY+vuSJvLiEQjW1LSTqKb8OUPtDvHCkdPhk7d6sjC19xyFw==", "signatures": [{"sig": "MEUCICemXG9WFcNgG+yVFIq9E+HtMzCpdhY6Wr460WiwbPrjAiEA889MOFSphOb907joCOTfTsv4wGaEpDpw9ZNDpUDp4Jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTLCRA9TVsSAnZWagAAWSEP/3JYpGOUygHdtBWSjGoB\nIQjysi12OJSx7ktaWCuYHZ06m79mKViI1KTfhfnnJqyMlkgw2LqR1RK37dj5\nqhkdZd/4gRJuliBnGabonbhZrPr0qUI7AQ6yoPimbLtbIngczMxjDOwVs5JN\nP+dXEOjfpqNEnJDTLtIItDkTN/jMhUNWMAl+iQuHTL+WVQEIMGHkjxDb7YMT\nmq8eWHukJsUMXi1DfazH4FpCJFysvdvlbGVSoTRZfkrX2ihWGUY1YdqbeVRO\nnrIWKJeeOdg0j6kOqX3UU5x1ItnBReuJfeH3eyG/Py5lP5u6ZG77LNHbbaV5\n+y4+LxMjPSZ8Wg0wUJjkhJRxPmORIRya5UK7+oW7WqnZ9WCcGVCXGKYDJRIs\nK/ZTJtZ0EX6gFSkkeBlOTI90hmgaB73N7rOaIT1q2SjeCgN+DpPuryXK2K5o\n7OGN6DKCsCipyGL7Kr7BoHhnJMjye0oCNtWjPave2uYI/IPCQ6BGIAIYDpaf\n6XKHASiiJzupPBoY92B+PNOEJVrY4AcAlqumin9cDASokSDP+fG9PXerMCJg\nCu7gwixHPDzprAlmPPO8Kb4mB8wFauulu7/LPGolYZcYZg8urVw9McYlIR0+\nbnb1Qx4NLNtzFgqoKnHmYOQISKpRPBmlUEF4R6j/lxsGRCUJEjDDrT9Xm00e\nDLOo\r\n=N98m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-builder-react-jsx": "^7.10.1", "@babel/helper-builder-react-jsx-experimental": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.10.1_1590617291089_0.6156745673702966", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.10.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c07ad86b7c159287c89b643f201f59536231048e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.10.3.tgz", "fileCount": 6, "integrity": "sha512-Y21E3rZmWICRJnvbGVmDLDZ8HfNDIwjGF3DXYHx1le0v0mIHCs0Gv5SavyW5Z/jgAHLaAoJPiwt+Dr7/zZKcOQ==", "signatures": [{"sig": "MEYCIQCduWQw4Y+ArfAIjGbzCYmr1NJpmi5fhXu6lqC/504TvgIhAK91wF7iZ0rxMzwpidBFLECHDoc4/9Q930aF6mx3yCUJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYMCRA9TVsSAnZWagAAWLIQAITaBUno3UIncDrxhrIa\nJggSfM1lsGj+hlk8MiK8SXgiASIn/++VoGjYUJj5bC7A6aCSb04kv5C9oHpI\nd4XBHjmnDZWlKIW7GFDaYQ55N3dGEQMvEqieAbw+XmmAAH2S6ewcigS36r6G\n8cph+YgeAZIernGpQnneU6okqD6arzooUEslCkYMa6oidzeTbZfPp+YzmjTf\nchZKyCgqQDFh30ShH7G2vqEhsDroIUUKSFeD0ccsWOne52tK3+1TfZLHFO+H\nR3kU3K8wiEbql4R5PKeHn8T232OvC5WNPoZ0KrZrr0vsnOZcQh2M5OyemBaV\n3oC9ynAKE1yJphc0AVWDkKfrmJq7ppw7n7iWLWV7umdejk/8+N7TnookTG2x\nn33vcpZYGimnH+91/ePMuHGsnJTMj3fbEo7rtWeR3h4HXozOxAKd4xt5oP/U\n65ni+wAnWqcQJUqbyFDvFBfv9/9Ng7sfXYFDhSxrSqB+9DmSn3KZ7mFZd7pp\nOyYCw7afyIzlDf2fmuu1aykO+7w4ebYdb6lunERqI5iiSmlMoqJDB6TMajVB\n/rQlILg5bXvVtl1QNVzoTHmYzJ/VfYFfqRXby1Zhz8oAE90UARRNhm5Rl2ww\n+uvOuMYUHvyXyKiyF9l2WbGKPjecVVgP5+ZX0y96r1dNJeCFZNeesPmDFw8k\nxr4b\r\n=XPZ1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.3", "@babel/helper-builder-react-jsx": "^7.10.3", "@babel/helper-builder-react-jsx-experimental": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.10.3_1592600076302_0.0741068538097851", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "673c9f913948764a4421683b2bef2936968fddf2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.10.4.tgz", "fileCount": 6, "integrity": "sha512-L+MfRhWjX0eI7Js093MM6MacKU4M6dnCRa/QPDwYMxjljzSCzzlzKzj9Pk4P3OtrPcxr2N3znR419nr3Xw+65A==", "signatures": [{"sig": "MEYCIQCEQ+gZOvbjW0fIj9ZJm6ufK7TNfOu/RSkZxLrbXobpOQIhAM6YrqMc4zXslgPvsfeSYeeOCMvBtBPlIn8ooW5/cNjm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpcCRA9TVsSAnZWagAAbbIQAIAGEVeGCHRk6TbSMdXm\nPDe6F/wdFitW69xzMuuSQnED6xAEEoYk90KIg1DqJ9x8DZRGW+OB1kagc7rP\naoV8rZq4Up9kWHUQ6JNr+PKxOvr3uugDHupGcI4Um4LwoefAthwk8oJOnCR3\nqYJi+UZIN+DOfE+xvLRHOyb1FTMf84BPPOvuCCsq+5btNguy7SSCyRcc5mIZ\nBHfV0s6h1gQRFtRsNW/qjU6pBW63jvKd1CWXKL4Wt1s+iSj83SLFwaZ7RlSL\nHpKWlI5wiaH5mXFXYU33jw4zIeEGdXXPAliwAT6KBSWMlNRRuxzfcIkwwBcz\nD+pwX6Hln0EjzRVyQ0L5JTyuJfTn9ZpUeAByv59KRaxXEU1wcIKcZoftf5PD\n11tJglviPg5EdiSnG6vdnmabCecvBfnd3W8RhWRkC/fYQCTVNN6zTpMNwGnZ\nsalvct6+6KW8UgztgAoFP29pTZNRHwKq2D0cAGav8zCO1JLgqWLCG5oQGiaE\nNncG04w0v8I5sbl33btZNHY3kTKzU0mqmPlJP4QVtzv2EUKI/dQqjb1QaWsH\nngodH9L4HXFMERz88w0MuTv/fwkfs8WPVF7OLVdQ4BRKQNHdzUGutGi61mEl\n5T1dDg8E8MGfX52pie1xiIfo6Orx5hCUP47BdFTx3GzCUghcD+CndVmU9vrn\nnNEl\r\n=pQXS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Turn JSX into React function calls", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/helper-builder-react-jsx-experimental": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.10.4_1593522780375_0.3387042862478631", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "c2d96c77c2b0e4362cc4e77a43ce7c2539d478cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-RmKejwnT0T0QzQUzcbP5p1VWlpnP8QHtdhEtLG55ZDQnJNalbF3eeDyu3dnGKvGzFIQiBzFhBYTwvv435p9Xpw==", "signatures": [{"sig": "MEYCIQCaOLrLRKaRykcJDex/6c1oAHBnptClpirJJiBPDQ1wRQIhAPD4J+osmCK9hbgn+A7/r81+GZTa9kNBJAhVIG+hgRNL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAfCRA9TVsSAnZWagAAouoP/RuQxXJ6QAGorTIQmiwW\ngEBja+T2I6HHSPSoOdOZDQePR2geTyl6vaMce0dhqnZcdNHp2AmFRgkapWGa\nx3qonY6WijdZ5RtlIwafEkR1M4KUwqgBrs3Dj5qy4vsqwSWLe93/yAq6KaFq\nnd/qoYerCn9GBV1zw+0Nh+hIDgMqAPie+kGc4uEgjCfyDWQDTH+vfx64mtFM\ny21QbfjjGWvlC37KCvq7iL7Iq9gdZGxmJd8GfJuHrrUAcHhzK3ZVaNBfyD09\nYssXCXRGIX/IxjZ2n3jfyIyZiHsoY6LlAmidECWSOr6MVypUo5/0Ivt+niko\nVnzQXKtxPyWWokG1NTQgW/YRNSBKKrCoUURoukDxWPr/Xd8Xe3NS5AZgT9l5\n3ZYFoTbwBrSpaJFR2V/5opAIjV69YirRfanKvWuHOMKhfFaivYyzYbKKZE+R\ngrm2lStox+TLWpF4DptaK9dEvnZeg673d+EjIz9me0CeRGRjuGECpGKhUqyN\nkcZQL42RmI5vDJiua9AWVFQi3uWce7nVS2n4Q8FvzRbWWQUFHjshzuWQoUsl\nlRcP6XmUA8Lvmg9kfQbSS0sMn7LoOnAvLzzdzEFAwtLbPyxaVhEDhnTdTndi\nfn0Vlf6Kj922BfYOf+eUdD6TMOOedcQotPtrUvTXHzYeMoz3+9KuLnaphCAs\nboK3\r\n=8NAG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/helper-builder-react-jsx-experimental": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.1_1602801695501_0.36964369747039383", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "39ede0e30159770561b6963be143e40af3bde00c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.5.tgz", "fileCount": 6, "integrity": "sha512-2xkcPqqrYiOQgSlM/iwto1paPijjsDbUynN13tI6bosDz/jOW3CRzYguIE8wKX32h+msbBM22Dv5fwrFkUOZjQ==", "signatures": [{"sig": "MEQCIEKOyRZDinOLVDgnss/bzFHIxfgkEUVapCJNoZFJtbB2AiA8o3xYM+bq+6DtwKGLEkTRR/W6tbROPg8qQTTTDObW9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodrkCRA9TVsSAnZWagAAPtkP/j8qZoaM7Rfa/m9Q0cM/\nE81c6EDfBg5Q8B/JVgkm5D1GVHLDJ4zWBVhgxqUHmzeO0kqzxthOb1RSD8xO\nonrrBozFP2eXUBAN9WTC+rNB9rRSPM3wRu0M2ngn524jZRX7Mb/uw57QE3/o\nHxerxO2Rw4HV4G7xphCaMcI4iQ7PHrfRvMquoQpZ6Civ5YqiKLJwXz+wEh01\nIpYkV1BJRBZRvoS9lafuwkChSral/QBiHg/hoWcYpe3jv8h98cumjAvUdJ95\nXOVQcWa7tRmKMT/b34ydeaFlN5RNSuT5pJm1BaI0+Bq3fOgY4f3ttumw4vD8\njVY8kV1iIAX8lQs2lk7GZTcWaa6qZT4lYvEAppF9rZ87WUBvWXHhjfKZSWgF\nw6HQcIz1rpt38Far2APchjsNrKHHVfOoLouuqQxJ5IoVg+7gu+1pqj/QrMK5\n7V60NGNZJCti/+xxIpwRrGq6eE7gYqPtb6+1ZcaNUyv+yTzHbhM2XApR6PFZ\n5qDdrs1x8gUCM//nh3/FWJAf0X/+Z6Hu/NVQ7qD6Vw86riw6IQ08HDm41cAA\nrEb4wsrbrrt8mma0krcgeKLNmbi/516Je/uBumre1fmiF7Ls6MYBpdTrZHlW\n5QYqqd/xYGMBD7sLUa9I2D1k0xFbaiOkDiFDEsLiKGAwLNIUSECAEBE5SEDh\n/AV7\r\n=sFQ+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/helper-builder-react-jsx-experimental": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.3", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.5_1604442851969_0.8920798155702163", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "8b14d45f6eccd41b7f924bcb65c021e9f0a06f7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.7.tgz", "fileCount": 6, "integrity": "sha512-YFlTi6MEsclFAPIDNZYiCRbneg1MFGao9pPG9uD5htwE0vDbPaMUMeYd6itWjw7K4kro4UbdQf3ljmFl9y48dQ==", "signatures": [{"sig": "MEUCIQCdN+J7LYX/hmK3cyR1d3EWIXprYYfg1F04EakYwVZJWAIgC8Y7h7vjKx1cE9iNUSXGJY0QwMo9WtBGzrRoMqVlg18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+kCRA9TVsSAnZWagAAoi0P/23ApPc09ZwbS07+Cqwd\n/T/cQEEfD42+vPNmNPs8W8OWCbMkPjZKU/LzEUewmpz03aJKJcXR6d2wpA2E\n19Bd+WR3h576wseAcO+wNXHKvK8TUPsUnmG7VqqocSs3M2Eakf5odNpDJ2OD\nMaFJmNBh8MQONF6N/aW1qDgoZHBhalTabOSa4aQ4QEa23epiJqFVqGm+8PCS\nEtq8UgrSV5DfHIRD25K7SVGJc6pQskuRyU3sPZT7im5DqqdUmkgFS8A9FUqi\nsjXoNkJtTVURZ2wr26UIQ1gcFuMfowCiyR2itL4j88XRkdH9YPn4NppOXkmA\nJjsxcjLvWGfZF7kWcLuJtzo5DfTKgWHYxtHjE5SvQPMLhkor7igHgjhu6JaU\npADHd/r+cgOL8HfEdfgOzLRGjePJ/cfdGqxqIzbpUcPEOOErXWhbJABqxRPw\nML4NdXzfksG6crzwxjyBbI7v90dvMk9WwM3tQVREWn5VpPuqnC5CBnWt9FnB\nBJGpAJv3BuP/pU8powrcyGCDD8WqjjU0nDO7SEE+gJpfVgRxhi8TOp0j7leY\n38H/jxj+fVpuGW8QIJHslCu86mU1qcMLo2a11nXjsIvCsV1PG0j2e+V3WMRB\ni96MzoShJczBAnZyLPF7F8RN60JXpV9ILahd6bPRQ9DM7t2HplF64qB2jYN9\nn9l7\r\n=7hbT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/helper-builder-react-jsx-experimental": "^7.12.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.7", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.7_1605906340146_0.9138686540443506", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.10", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "a7af3097c73479123594c8c8fe39545abebd44e3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.10.tgz", "fileCount": 4, "integrity": "sha512-MM7/BC8QdHXM7Qc1wdnuk73R4gbuOpfrSUgfV/nODGc86sPY1tgmY2M9E9uAnf2e4DOIp8aKGWqgZfQxnTNGuw==", "signatures": [{"sig": "MEQCIED1LBRlXLSyejtyTT4jsuLdN3IX7JCcpi9YwIIeWpXvAiA4AffVMBozP7JihTknMDjpSj7FU4YdENXMkSjRDkgs0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQyCRA9TVsSAnZWagAATFIQAJjRHZmIFHa+jdTt4aOX\nuIwD59QtdC+rBXkSr3A+lbeU+2KBTTZuZDC0ikRRhv7Xr+bGB+6aZBAkVvcB\nMknwVLZYBBfHRmV//XZon9hI5KaihudPCUJCSCtsedLa0zn6WZv/aZTucB9M\ncCxV4BB5zxceu15jeI5KCAK3UdaZfWScfdmRgVzFPSOgE8R36kZMMuZXOU2w\n4uJQw8qsU8OCjvV4MiPjCVHIUmEz/rDVHrX2eC7siXAlkGDmZJgk4Dr06Peu\nMpjXRkjqQ8MkPIowwC9fpmB1K9iyZdGRuQmkPEFLbmp2plaz8VzdjVSLW2Zy\nCF5ynszUAEpENuFtAyPFHKpNYVwufsNPpMmkPvm2uF8OI/PKV/zW2Tme6TUi\nbvLRVLJ/lCSDcsG+JhCYy4iuruksrl3iK6LdlZUZbJcB4kesa7T1M4KMbTta\nNJqu0nyyZ5GfI8aM7SktWiUnB+osVWuJFCSYl7ujV/RFJxnvFravDvht4xxx\nUwFw3jspi/Hr8hHNHCfF6H7oOR50BaEWFA6G91s2WNtgpOKOnutu90/1BinK\ncL9d5eAyPOuiVjaAlLWTau0I5ko74g0X2wPLNDnliZmmFJ7MlXYZhV1jqG6W\nAHMOzsT9LJgKkGp+hNlp3Vrl80s1htyo/8/95C9HduUvWhB27NsNG7C/vElA\nmfOQ\r\n=aiEx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/helper-builder-react-jsx-experimental": "^7.12.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.10_1607554097884_0.4229135089953209", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.11", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "09a7319195946b0ddc09f9a5f01346f2cb80dfdd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.11.tgz", "fileCount": 4, "integrity": "sha512-5nWOw6mTylaFU72BdZfa0dP1HsGdY3IMExpxn8LBE8dNmkQjB+W+sR+JwIdtbzkPvVuFviT3zyNbSUkuVTVxbw==", "signatures": [{"sig": "MEQCIEKRVIPokJeRqt+BaJUjXvnGia9SRqogSTKYjeQ0A+1gAiAbvS7Hrfje2FdyUtf+CBGClN54cR/hPuuqmHKAqf8LoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3sCRA9TVsSAnZWagAAC5sQAJ2HOtPtYioryz7qlI8Z\nJU2PBdeNUANzAdPsv6zxrZ4Th2p0VzRi6YmX7Oqn1GUQgINQMUCywdc0Cp2g\n5gIPhZMgZn7h6HMqIeM0X3gJG+bxan5QUZd4ZCP86rNkM21msWwvj9lOe41e\nNnTHHeP0+HHG7ze7k+l2/ni8xYssHNbvxCDli0t0X/50Vzuo3F6UxawQRF0R\nQNrvC6F7F746r7Pqq9di+Diza7/gFCX/pLiv80ttbYeWLZ1FzWXJqjVWt8Lu\nTvy01YUljxWLggOP6AIYeMBQRHuFFB5zz3Yenwqj/MM86+35FNOuIuhYmPWu\n0+Bj/TAJ1ijE5YHrAcAjyjyOmukhfxhj2nS5nJqXUk9kCepz9m0fVIRVdPFB\nXM//CSBtgBJNCFesEdpplvZfGkeJLQbv29Na+8EzSZK73lcpusxM26Jjrwqb\nYK1Xvx4hJG1bkmYQw5mCKaB0ISNc2hmV/eQNhkinbPgvM8Jw8byVeNPZpPoU\n0Wf1GYOfMevvXk1kfJSEde/dRZexLC/oaFI6BZNVp7YAMQd38NuHRC1M3DYY\nQYqkX0K54ZlolKONjzJv1nkA8UzLNUZk8vzhUKA9HoaABLAyK0oUrJGJ4gWT\n3F7vcv7XrKXNXluCRXe6F1xCGJ7ksBGT1vKkEPSGXoy/dS/sfJE8go8z3qpW\npMrg\r\n=6XG+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-builder-react-jsx": "^7.10.4", "@babel/helper-builder-react-jsx-experimental": "^7.12.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.11_1608076780151_0.03366653616316562", "host": "s3://npm-registry-packages"}}, "7.12.12": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.12", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b0da51ffe5f34b9a900e9f1f5fb814f9e512d25e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.12.tgz", "fileCount": 6, "integrity": "sha512-JDWGuzGNWscYcq8oJVCtSE61a5+XAOos+V0HrxnDieUus4UMnBEosDnY1VJqU5iZ4pA04QY7l0+JvHL1hZEfsw==", "signatures": [{"sig": "MEUCIQCgYWhUZiT6FqMEmPVJTt/ROuiRJU9eHPrH60dpaBEkkAIgPXFCkuYbRir6yVTDrRTWbmIWEpH5N2wNuSH4GipL+xE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf406oCRA9TVsSAnZWagAAaXsQAIutyzmwhICT86N0x2qh\ng6lU3oDRy0MfutkUONNy/K09lUc0xH1YaKax4BrRI+zbfAhViSdUo+ageVug\nnJAw+MazyR34wyFRIL5t1V/382X3i36XaCY/Clhlw+pdNVomK+GO8fRiDm2V\ndqfT+47KzqhHbqP2u7rJz5/itlDim59aLKZraBpYrelc7KUFLNyVThfHh1Hj\nEO7yH1w2aJvkb0mEqJ3bWHDV52JcXYSvADGsbm1UDrIlquiR/9dWSesc5trr\nJxLjpLuO261zIiZOLG7qAA1HM4CkbcW59+X5iWgmqVgkHRpok5c4q7ftoQ1d\narU1O8p2gNC1OLxpzHfFGDCrUj6nzU5woLm7istSq2dXrbd+5cGD9lpLdc6o\nMKYEpyChD5QGXZjXBOg1HYWj4wWikvjnFQwXy3cWjzyqx6QxDc7/FPbVo8oY\nNucKmL2uF7PndEZZCFIRd3RCNspfD+qGR112v0apvlL/BdgWRkLwiemQ/Uj8\nBXGCHuxYYA/8W0vrQ6zdydvvExFmKxi6FaTi0lGZIorA3nxbA5P9J+9gU7eW\nCp5W8FSZZ8PvPFgUu5SFAqVrSoMvHtPxt38tOXKBpD4lbUiK1GdUhQJb4QI7\nxYBTVQMeP0FzVVUVFbIyr5Z6x2eJpZ3NC10/Cdk4V6d1jrWWcRSnnkOPFzlg\nvDT4\r\n=K0oF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.12.12", "@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.12.5", "@babel/helper-annotate-as-pure": "^7.12.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.12_1608732327873_0.6333842355105113", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "6c9f993b9f6fb6f0e32a4821ed59349748576a3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.13.tgz", "fileCount": 6, "integrity": "sha512-hhXZMYR8t9RvduN2uW4sjl6MRtUhzNE726JvoJhpjhxKgRUVkZqTsA0xc49ALZxQM7H26pZ/lLvB2Yrea9dllA==", "signatures": [{"sig": "MEYCIQC5uh2P3nIlp69gzAETERwhMi5eJDcKooSA3UoqndiH8QIhAOaWXisFDGTqv7ddjyonYIo46HQBxGX56QwD2W7T46ip", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhBCRA9TVsSAnZWagAAM4AQAIFU65+lYH8wbZDPr7Tk\nT6kf51/hfBnx2ruhQqXdjPNon9cb+1GSbowC3cStk12sie6dfpAkSZzIUXcy\nCxSkGy0X43HhBlnHUbZ5+CSeQpp7uP42U0ebWRFfRdVeonDAmW6ZqnwzdOlD\nuXI02ZNHoMze+INhZnPVXbOhJJaQfYhXs6yn43szeeiohBBWujr0b1+K/kO2\n/n2zK2Aipo2LsGpIx0ODie4Nak1WHG+aocCLv6VdFlz09dhIoWvwcAY+iOj+\nVLb61ZTqBmnSmhA2sMD/nnkcODf6USELXaud6Dy4kYHWXe7QqtFBIHfs5gCS\nS3WQ54+JHfbXj3C10YBRJiCFAlN2PzNedcwUdMQ3VTIF3Gl8Up5ABHzUGvS7\nvc+8e1Dkru6zOTARUhI1cceMZ6dSiWhV1uTCZwa3J6lmMJLV8LOxU0O7nhYz\n+LSZ5x3fIiqrD+ksVXXGjBh8+TETfUlFu+hmw+YIgcKjtHUiwoH0+sNpxLFL\nWJ52ArbSOdg3VgUMpwMvVZD9KYcK86zVZIEHBn95mEuUfwUJ6lcCn+5Q2IHw\nAvGeagSUVTxzrjxHNzD2f5spPYXPSpPrwF43Gb1Zpa38/MMIMy2Pjuji8nlZ\ndoBWfqgMiVEpxAN5xOKD9PLCIQ8uJn0HxWdpyJYVm13GfcK0wiuaONgdfmoS\nwoGC\r\n=ctzW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.12.13", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.13_1612314689503_0.29482109576197524", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.16", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "07c341e02a3e4066b00413534f30c42519923230", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.16.tgz", "fileCount": 5, "integrity": "sha512-dNu0vAbIk8OkqJfGtYF6ADk6jagoyAl+Ks5aoltbAlfoKv8d6yooi3j+kObeSQaCj9PgN6KMZPB90wWyek5TmQ==", "signatures": [{"sig": "MEQCIA8BeZZlpKV5h5qrh+X/jqEWFFfPSlbYsE3vJjOObqqdAiAiGpeJR7WGVIZRt36+rmWaU8676wsUGScRRlLQa4sgIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPlCRA9TVsSAnZWagAApZUQAJtxWsF1qXqCDuxE0xd9\nsgdYyKg0fTZiFWAbdASRr/WWGUj7pCjb4hn39KS9iZ6CWHEUsG4guYByjh+e\nCIUnpG2n8OXwFdCd2mK6Vxv2dEwIibzq3Ws/Ymn+h8CtM3pZ0w+LA8D51zdW\ntx9KINJzIe56QwsX29Vu8BJuj3eEMPE6iZ7NHC8N14U7FkBRmHPMCns4NcRa\nQ6EwX5g8yQX9NTTGl73A9psDjJDz9hYmEMasEBH+8RPOZrGe97Jtyc3OhFyI\n6GRqCh6nL7PFintJ4sIybrqw4XAu132GuLzsVDQbfZbG3nceXM0DXf5eNyyO\nFR1hmkcWYaRGTeDEub7jgrW69A2688tQEZv4tpML8nQxlMtrLKnxcqngQT87\nKIACsSSZymm7HVO5iwIxclCMJUXjkTYJw1iazYuvpQhMphFL47g6PoDBE77o\np9eEKVxhDLAFAYZ3rYH9InPbR1UO/YJTZK70KhdiE8nphEsw9doaeiercGjy\nWjN069jQfLyFAy2DbY+l5pgq2FW8lpOJDqqUv7X8+CU/di99XH4E+P4EuAKs\nlJhzyJWxJrERRx4MtDHdUbnPy8+wo9+LVY/Q6Zl170GJ23g6wCrVVysFF8ca\nhRQuONYD7OtsJl9oI6ZoIP66F0ag2dXwRl5FnzqCwnTIjoqK2Smj+LZ/nCT5\ni++s\r\n=qEOU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.12.13", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.16_1613083621057_0.8179743522885567", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/plugin-transform-react-jsx", "version": "7.12.17", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "dd2c1299f5e26de584939892de3cfc1807a38f24", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.12.17.tgz", "fileCount": 6, "integrity": "sha512-mwaVNcXV+l6qJOuRhpdTEj8sT/Z0owAVWf9QujTZ0d2ye9X/K+MTOTSizcgKOj18PGnTc/7g1I4+cIUjsKhBcw==", "signatures": [{"sig": "MEYCIQCqb/vtmM+M0p8e7pB1deCQmh4So91x5KAxTHZ0+bPg8QIhAKvSCy0VrvM/7Lb7epef7mvRGtU9PPAhp3bGGTkF5uhJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoP3CRA9TVsSAnZWagAAmOMP/3PHbGk1knq3jYdgYkod\nZzpq6CMBdAiNZD4lXNWqgqlVPWMo+LsODo7Wk4TA4ZohzvM8tS1KLvNpjzM0\nyQHS/vMexMFPZ4iw1YCjRFWOe/7XVLX8e48witDS8Dnu5HSYnD5UPzvAp9aL\ndc/O9UZ1u3DAC0KOKK3OiOykrOLX+St6S62xMszRFiDlv4EbOCU85wvxJzOH\n6MT1DNnIsqVU3YftECEhBcHoOAg3vRKFKgzE1TVX3mQnZLR6YzL+anbfavew\nWizNYdLRFR9tBTajWxlMHCZ7UEOdKi+l+lT3VEQlFkBVdqgKxNeLJJ+5AEg+\nsPCOFqjNSf8K1rOUGtN6wl7lAkqa3asKwUYe6hfy30mhYE25AZlDA+y0W1cO\nblsNR7/M3WXWDtagKoEy9iLXsxvFby1SJ2ZGQNtO1rpAWzQzgX28FQThqyoV\nTTPO+vMdZVdqNraguDTOzI4fKYpKTnuRTxlJy0HYG/JZiTV9WeW+CgECJQun\nsP8S4cTevVTQc+K4eZapaOZljwVocDsAGFWRSgAMnbwxSLuORy//S4707LGg\nPhAOwa+zvWz90MlkaQL40+3a6zwA/KaXC9809Je/ypbpkQeHTWuMYz2aO7rI\npiY6+8LPB3jkl7XU8Vf0D7/w4a0C0i2HrMc7eUDRK7H/g1tO8QlG/q/Rvbod\nNVjo\r\n=1OmK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.12.17", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.12.17_1613661175088_0.5937844561795353", "host": "s3://npm-registry-packages"}}, "7.13.12": {"name": "@babel/plugin-transform-react-jsx", "version": "7.13.12", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.13.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "1df5dfaf0f4b784b43e96da6f28d630e775f68b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.13.12.tgz", "fileCount": 6, "integrity": "sha512-jcEI2UqIcpCqB5U5DRxIl0tQEProI2gcu+g8VTIqxLO5Iidojb4d77q+fwGseCvd8af/lJ9masp4QWzBXFE2xA==", "signatures": [{"sig": "MEYCIQDFE92Nur7CSzcaYFZxxSSh3PyciwLuEc24G1M9VxrFrwIhAKcju6CmYziLlW120GRB0Uztalq27MWB06bfoiWspshj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLwICRA9TVsSAnZWagAAH4EP/j1XPl06kMf2ZQ5ID9y4\nkGPEpcpyAEyNohXZUCekA+h0DhAXE6uVKhG1CV8bxZZJv6ymHjClG8iU3Uku\nJmKzjXV331ZiTxDRMQF3s4+lMF7qVq5M7TIyXPocZ+989ncXkS5T4jZCvILI\ncEGZuaB+MBqq10lyiNYrbnCQQ0djt6ON68mETz/79URTunLgFkPI2a+rBE10\nqyqDP6zbtxkl1Kzy9y/KboLsRrko+dW8oo9zidkh7t1pp0uizvtiDGePiMZb\n/5NfNeDK4py8vPJz91yffBeISEqiNjMGI6m5M6BXuzZ/DfNKtYhFN6z8NRY6\n1HmWqUJVoNzdmAdZevK1vP2HzM067ChXoygfSdm9Mdf3+fWhvQQLRqVaXcNe\njAeQ1wKxMS8PIdZnVc52kDXSwl0iwf5CGRxcHZHSC8nXY9+wdjxSKgWwpHD/\np5dlC+r9bLNx6L5vGl1j/UaEKDn9KXq+lw/05bu+OdwcVZhRxzG8wKElnmoD\ncrj0ak+l0tdBVWw0g70m9rGVQz0I3vj1Kx6+jCYYnYq+DJMwg1WE2uvZboC2\nsmCEBNYMWaXDQq4BXuPaaNAxVdNbH6s/jl1XyuR2ecsKF6tIiFZUZe4599WJ\nVoPEzVG7cl8jvx8rN6kFNa++pVepVdYTclFklPF5fdd/C0/AKZMDiEqGdjN7\nqpzB\r\n=Kdja\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.13.12", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.13.12", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.10", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.13.12_1616428039631_0.027329122908116066", "host": "s3://npm-registry-packages"}}, "7.14.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.14.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.14.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "0e26597805cf0862da735f264550933c38babb66", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.14.3.tgz", "fileCount": 6, "integrity": "sha512-uuxuoUNVhdgYzERiHHFkE4dWoJx+UFVyuAl0aqN8P2/AKFHwqgUC5w2+4/PjpKXJsFgBlYAFXlUmDQ3k3DUkXw==", "signatures": [{"sig": "MEUCIQDIKneUwHTALegsHKgeiYXCPULEmoZrKmQbQMtLOc5ibwIgGlGmwShQhn7QrDaI1eePrqLfHCs4E8jc4vGkJRT09iE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotWnCRA9TVsSAnZWagAA+MIP/1GW9mwgYVt4ZD+8CXKg\nvgYozti2uWGjanDYVSKvnTpBH2lFFuscUIdi8yru2zO1/U50cNNCJRKg+tAc\nEeMEThc7Dgg4a5qW2pyyRcK9o74lXe7ASsLzXjgZvbgUkp8odTsOsjLXMc/o\nfBBohEbMwsP/VeI8ockT6QwDMMgzKtKn1USEQqVYG7aj+Zk7wotFs+j+E6G9\n/tw/UBkYH7o5fnOoBLI+dsH3vB1C2Yx878i7xjcpWdkNSmN52ntFSLVgrKKl\ngtEi+R/8cBi04sgKJDVzJGkbpG+UK1Wr66+yqrT31vuxtKqr7fcS18J+vPXR\nFX1mj98v/KL5+2CQTCPG/+qicO+YWqHqjGRX2Cqtie4l0VTmOP8pIMIK4xLF\n+OxKHfnSFuxBDpSO0uBiq954O46NQidY2irF6VVgqYQ6nzXGfnUJTKfJD4xY\nvumBgfivzb9lf70gsWpqEiP03ke78Ci4WJi8eng4/0bToOvwwe/ZfVNywGmG\nVwtrQyZ0gSFUtxoBGuv8D9YftLVXaEWiBSJUgjYVDl7XNxGki3CIWzj2LGW1\nyLB/zF7AbbHOqqfes/4McopcuYDIjSpJQhb5zMGz8xNSD6VS7JYT76HyF/N4\nQHmIFfqaNyG8/XPfpALZh4AtvjJK5guy+MwVLpWSNmRu/wNqVuvx26Vxc03y\nw5vL\r\n=yUJQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.14.2", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.13.12", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/traverse": "7.14.2", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.14.3_1621284262991_0.18830791445122186", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-react-jsx", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "39749f0ee1efd8a1bd729152cf5f78f1d247a44a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-7RylxNeDnxc1OleDm0F5Q/BSL+whYRbOAR+bwgCxIr0L32v7UFh/pz1DLMZideAUxKT6eMoS2zQH6fyODLEi8Q==", "signatures": [{"sig": "MEQCIBBwQXA0s5p+gfvKQmd59awdxOtjw1te8Cgt1UPt9jSiAiA/aMDXf28r/wx1DnShDPceGjciCuCSw9MCPv1Af5iK8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrtCRA9TVsSAnZWagAA3DoP/A+BcIg3BIn5yaiC1VsA\nJGju3huf3EbQePhK+P3dsvNv+VdAInNlSwujd0Hfnhe8y78zvzPgZo0qQutq\n9ehHWkjtNMx7uPrEgmC0WRlMKZ5VLeTDU/R8m0s7Isx+xKecSnTyJeyeS0qa\njTEuS4z4TAxiLXz0v7A7G3C4BDcL2FHVYQkONa+m5UxTomb1ReRJd+C79D/H\n3DFak/gaqbtmXbkZmlznFCpq2y37gW5MWGgYTSnT1ETyAUvr6JwKyDLw957t\n6mN1Y5tUGSa7elGSUbJ9edQAEazv4BW8NOCqifziBa+Q4To5XrpRvm8uvm7g\nCsM6D8VpRDjsYs6jJhFHKqolgc6BEqmLklbcXO80a1oCl1H1oI5wWSTEU7VB\naQWCZx8BHb6vCX9tTJZU7mZsduiz7qmk/c4hjC/I9gQS5aHCzGrTX3CpzQ4D\nU119n/X65UL7zE0HB5PFTInNTrT4JGLnvDa98rFbkh2QgZ+TYVDAxTcfIv0E\nC2NlMJBdu52Cm4gXdQl+opA4LAKxh2ECuZKIDtbCcDtGW4cQQHDC3CvqHvHU\nG7FAalEcac/GlxfdiWyw/AX/jCe4MgQCQiyYLrIp1Mi+kuLqG3BnGcMgxIEI\ns3D3E2YwmQyBA+lj6w4VsrMpkzNpiAFxklURdRcZn5lUfi7/kABeWErqxQ9J\nGJm3\r\n=LvQp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.14.5", "@babel/plugin-syntax-jsx": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-imports": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/traverse": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.14.5_1623280365122_0.3888624713808435", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/plugin-transform-react-jsx", "version": "7.14.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "3314b2163033abac5200a869c4de242cd50a914c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.14.9.tgz", "fileCount": 6, "integrity": "sha512-30PeETvS+AeD1f58i1OVyoDlVYQhap/K20ZrMjLmmzmC2AYR/G43D4sdJAaDAqCD3MYpSWbmrz3kES158QSLjw==", "signatures": [{"sig": "MEUCIQD24ZKpHdotdGUm8PZ0GOopuPPx1F78W9aO0aXeFhxuSgIgDPo5hWw7msdx3fM2tEgM5rDvyHA3jYS/i08y2zcDs6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLzCRA9TVsSAnZWagAAJoQP/3fPJWuabC8DTvQACsFH\nrapoBNgirKldNJpFVtltS5K21XXaYQMnX9sbnzKwdpdFUDpnLkowkeI2jaOe\nQU8sDOK8hfLVCCidQKy1IPxkYFfGD0CuTIi0zAlgbPN6fAftydLJzcXfP5SI\ni2/IJglOfUPH1VPsZrB0n8RBUGSSnPjgowgqipmBVYoqglLhT6ps7koV6m8B\nmXluRngIX8M4yQR3+qOhNVz4WVWR1guojMxXOTqQDIJH4ExR63wDQDVv4tp/\n6/jeEjq5//Sw/GNCAvdoZFZmrQkx/7OsWUPlE9o3nG8ZbOE9MFWB6RCjSoJ2\n+Eah4g27VTRL9CdR305S5+dmgnSGObcZCBA56W+v1ZrSkc/YxpOf3E/9gfOH\nUUaBZK8pN1Foskd9qpdn3+/RLmT/1KVORJM5/Tm9i70K5bu0o3T7ZZoP5zMM\nh1bRKAE0Ce3g7pdhC0CWgkODgvUA6Fdb7TXdK/J5stWVfdddqiZmNQAaeyjy\ndC4SMpSWFIPa2AEKSYhJGzdhRCtlVk6yF91+smfbx4FHVu2jZRveOCE45JC2\ngFYf7PfqzMC6PrXyiKA6bOoZWCHWd+9fgKrSIwJau+33a0CuhGLZmPLSLER8\npRCPNzXxJwwdyVYmr700qw7Ba14ebUOG9qWy0UNs/LOYdmszgjJY3EDwLzOL\n+6cH\r\n=Hcg7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.14.9", "@babel/plugin-syntax-jsx": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-imports": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.8", "@babel/traverse": "7.14.9", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.14.9_1627804403259_0.5306192622875563", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "55b797d4960c3de04e07ad1c0476e2bc6a4889f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.16.0.tgz", "fileCount": 6, "integrity": "sha512-rqDgIbukZ44pqq7NIRPGPGNklshPkvlmvqjdx3OZcGPk4zGIenYkxDTvl3LsSL8gqcc3ZzGmXPE6hR/u/voNOw==", "signatures": [{"sig": "MEYCIQCSAuILveqkCpjK8ht1kkyGyEnn05UUJ1IGxq/UlrgAXQIhAJLRYoWznV1HDsy1d3swT5q5TDB9x+oYLIk8b/Uc8F33", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24260}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/plugin-syntax-jsx": "^7.16.0", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-imports": "^7.16.0", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.16.0_1635551268043_0.9467817144087547", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-react-jsx", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "5298aedc5f81e02b1cb702e597e8d6a346675765", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.16.5.tgz", "fileCount": 6, "integrity": "sha512-+arLIz1d7kmwX0fKxTxbnoeG85ONSnLpvdODa4P3pc1sS7CV1hfmtYWufkW/oYsPnkDrEeQFxhUWcFnrXW7jQQ==", "signatures": [{"sig": "MEUCIEhlvKizq/y42glSZ7Yg8uBUHFnDEaskgTZVp2YwrSRJAiEAmTGLPOUM963l0JO/Hyzf2lwePI3i7hexMAHgEKWpZVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kfCRA9TVsSAnZWagAATzIP/RDhyb15/iQk5q3qxN9P\n8PzE2qClJNVUrCBCFVuuIuBDKxYYI9kupUSht5DremDCThoJl/bT2dcaIAe1\ncLT8XttbhzyIq1Q8HiTItUG0Kik478lzsJyiykiRbMPj/pNx7dfSCaF5ZwLT\nU2d3zJc7F9l0uNr9J8EGTMph/uc+zvXe1R6Szo9RWI1zgjHZeZjAFXM6h/o/\nkUt1nQj8zOTiN6r9HGIid98qol2+5D4itB17M7AQKAhYUjjeOM5NXoC6pU1X\nrzoq4DD15FmutDrK3PZdOV2Gf8cALBNzspsKhj3SCH6P8Cl4VGEg9QJTWih+\nk5bg8E2EfdVm0976r4b8hCv1gw33irX/5nAz1JJhbtMgLdTYcEsUoXlTj8ns\nP7uFdSB+kwQHk/ksOGbUvQ1r9xQBzvYnlDjtzlj8ppBVOVMJf//8iWHkZsuF\nR6RMQE0O8wUSAFZ87nvzRdAEBrjqOOOYI1RGBHbo5uP97M8LWUxKEr80WCxp\n149a4mAluTUGQ7KyT2+WgdK58JEvfKbpUVZ8JY6SO+OwXjRE3jXpYkdOBq79\nPHHg8YQ0RnggNdw/1yiyDCofk/FFIziaNIFCHw7zAoROmJoST52zfp+TyXsf\nec/wcQQsihK1yi3VY86RWG1oOk1EZb85nF8//WDpUL7f99WynGzt16N65qJ3\nbKgw\r\n=/eVf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/plugin-syntax-jsx": "^7.16.5", "@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-module-imports": "^7.16.0", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/traverse": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.16.5_1639434527134_0.14691221810130184", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-react-jsx", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "86a6a220552afd0e4e1f0388a68a372be7add0d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.16.7.tgz", "fileCount": 6, "integrity": "sha512-8D16ye66fxiE8m890w0BpPpngG9o9OVBBy0gH2E+2AR7qMR2ZpTYJEqLxAsoroenMId0p/wMW+Blc0meDgu0Ag==", "signatures": [{"sig": "MEUCIQC5Mg4f58hJPCAT/vtSfiZZ0oI7BGp5JgzbkaT2yKS26wIgGB2Knmfr3qaxic/2nnpzHOSykESlVd1Ryk0NfRS72yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1NCRA9TVsSAnZWagAAxuoP/1/bKiO6zBJMm1m00aSO\nUaP6Hsikzr3o1mjnaq+cGbY7iZMdA3ws3PbiXkrxoL/qEkv9LN8crkhhKcfJ\naIVEQdJRcOhObjiZBTqN2hatG/j0AMj5UnQfyxpDmg80q1KL6yT/hqne8LbR\nCr60lfRmP22pNb3we+pOchq3ywhHfgAm/XVZRPjZvQImYcQwliMprPqNuUBk\n+LOeKQmgXcsa0XLXdryWc28uH/f8m4NU36RpeEzPRZCmwSKAVgSG+nQP/qCd\n+DqvzEFfsHKwT3zAWJZq4exyj4DyIEmGaesC0ZYFuGqJVXNfulncq5m/qZEN\nwkpUOdQSynotISlaFlRxHi45XdApP3/eNO4hLjofGURz1KjuFat+SUdhtkK6\nbhLUWZJiqQIhGsgbD/GXjGZtmlvCA2/UoT1DLRxowYWUO326gfTbM/7uxnGf\nih1gf2miS0lvCAL8X7VJZZR7Xmk7gVD3VA/bXYIe0ARkXIw0cEbym7Gx9x/M\nDsp6bS5NdJYAw1ri6tmC2fV1JbUjRuGTmSDc3ZX34pIJy2UGjhBxUPzTtc5q\nChdZlKzfBZdYYWq7RevApZF9WdZD2FBqHuKYsZhSEPyr6/+4a0osk1jn2AWw\nqqbc61wSRu2Teh2j/g0w2RHcNN5ei7zH9O4Xzw1cuCk50KM+wtmv7GO/ec0e\nARMS\r\n=bNf9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.16.7", "@babel/plugin-syntax-jsx": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.16.7_1640910157495_0.8536133856946784", "host": "s3://npm-registry-packages"}}, "7.17.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.17.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.17.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "eac1565da176ccb1a715dae0b4609858808008c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.17.3.tgz", "fileCount": 6, "integrity": "sha512-9tjBm4O07f7mzKSIlEmPdiE6ub7kfIe6Cd+w+oQebpATfTQMAgW+YOuWxogbKVTulA+MEO7byMeIUtQ1z+z+ZQ==", "signatures": [{"sig": "MEUCIHBeacR9r6JjcR5GeyZvvGMjx+wQCzoWxVTbci7IPIyoAiEAsh7eZSNFfnOGB/C2tzQQHSdySSUd5WHCXstJwcIfuUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC8pVCRA9TVsSAnZWagAAklAP/RFLXXJNuPeCrWHgu3kL\ntTyAIxGMjZnpAgHkZj+lBq24ycR5uRrru5rHJU943mWtdaL9kTFpXLlTFP9+\nvFLsdgd8xtxG4Uai+DysG7zMK+3I0nerEm79XjZvU1UHhagpoDUzNQ0wxCVL\nCF78vUY9JMw+J1j0g0ELVeiKh0uRksvYFfNScviYHDbpL06z+YfjvaNG1jK2\nr3deBVmFZObog4VJs1cA/uLDU8p0ru2vCNzmKEHVBm5tF/1JpfcTbIuXkNNF\no+SiKJvJ5g97PH1wKNuhDKRNOrPMvFc6T0a8+3L+ylyZwzLC/zpFxj30M8Wd\nj2fd6E11X13Qzkj+/hGrSqbM2feGA2cRfCLF75JyMqAcIZfiO9uiK6Y/+9F2\nuoxSR97aqF0MBUpVtyEcKn1CorEqRaHaVl6LU4kXSs51MZmzVqm6lBGM0zRx\nRqyYBnYbOetSpn382ReBGXcTr2zDgseKwUsZCaoekdpoHKLBQMRbM9etk7vy\n8cUt3iOly4H54txLMF9bZq0kwBm+Oda8ZZ0oienjrT1KEXUtS7vDQwaoYNzE\n2HM8hZhhveh7wa0az6sfghoyuMLptkQp0c2umf/HPQL6spupDaE1dseiquhL\npvxPmb8oLLjrwPNOQd/fyYQb4T/hGwTEt2GC9xXVWTmBVabHko8vRK20sHk1\n1MNX\r\n=rICR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.17.0", "@babel/plugin-syntax-jsx": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.3", "@babel/traverse": "^7.17.3", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.17.3_1644939861724_0.6797767245965542", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-react-jsx", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "2aa20022709cd6a3f40b45d60603d5f269586dba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.17.12.tgz", "fileCount": 6, "integrity": "sha512-Lcaw8bxd1DKht3thfD4A12dqo1X16he1Lm8rIv8sTwjAYNInRS1qHa9aJoqvzpscItXvftKDCfaEQzwoVyXpEQ==", "signatures": [{"sig": "MEYCIQC3ynSFv7i6MJqHmy5/sXOqhLIBWc4I6vyEE6Rk/4ICiAIhANsL1rbJseOdKGAj8NLhk9h4+Z9sLVd+Boq2IXrSn57v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1/Q//Xoo/radlfzVEo1WkAPtjIOP5+znQW5RoPRZTEV4uB/MIPfdY\r\nr2B8CucMjfTfbhO7yFgGgFc2bXkSzPXIuQyR4XERjMYaql9V+rukuenyf3P6\r\nnfzprWZzTgkH5u21FlY+Oph42ERXlQ0Amrz7nQWrtXXKMDcnu0bk0uhHYdso\r\nUC54G3jPy4kaGxBp8fPtthgJM6Q2yb9LPB4POWvCV8Y1VQlSTsKfBrq80QBi\r\n7SyZDVU2sNzR8pna0PJSr/7nCqkCtnJ2ILDWv5fwAX8LQqeq3oTgteO54sy1\r\n4nOUiWQ3furh0lMfvJzXHsM0E87k3y2tC6ah0rS94FFgUOIXq5uAp/nFuKpV\r\nFYdCmw0HjC4ByRNBIixpPyf+DyM3GdDT5F2pMGM35O7k/LpwUc7Rp6xN8p2x\r\ni3iC1HELCjADLITTcZ2RDCcN7XaIaUxDA3X+OtxOYMROma4ItjgV26ubgLi/\r\nuNqpceDRH3wC61kVHjnthgqT1kBKfi/IuI5/eJm5Mf6hNbnQRpUm/1t5CodT\r\nX08/N+8fhFBmcAu3F8DeZBjJhmuoZuTqtG+dxKLb2pr8UhviD+jTr+wP94uW\r\nByxROMg/P6QSQQm6XmaWLhfkEzF1vcwVJ1dJz15zaAqGV/sw1isUUFKs2/K6\r\npDB0ROg3NylN9JggTPD24y0XPf8yj6HoiuA=\r\n=U1/f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.17.12", "@babel/plugin-syntax-jsx": "^7.17.12", "@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/traverse": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.17.12_1652729591543_0.35081710618347106", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-react-jsx", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "2721e96d31df96e3b7ad48ff446995d26bc028ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.18.6.tgz", "fileCount": 6, "integrity": "sha512-Mz7xMPxoy9kPS/JScj6fJs03TZ/fZ1dJPlMjRAgTaxaS0fUBk8FV/A2rRgfPsVCZqALNwMexD+0Uaf5zlcKPpw==", "signatures": [{"sig": "MEYCIQCOhufdnYd3wWX3btq8MqezyxpUHQFY3+hub9kXLq4aNQIhAMSYZ19K7MskYvcRUhU51fYD4w326m4ypwkyIw+V0d1J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugn7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0yhAAinCBVfndPZrM3+985yyyVR+qp3nPi8+HIHFcNNrdVoMAyzak\r\nui8LCnbUa1OWHy2jikgu1tXOn84gZfNpW6Iq6+alWP+X8CqtSufcPYSEBT4E\r\nRq3W0r+OilwddCSfWW/3EVJqvOQqml2ADE3Qpj3Ju11gdxmghwjWoS8NQEwQ\r\nqL8EjmoNXSsLIaV/wkrN6GYz5iKV8lqp0hYjTJ+zE3oxZ5fAvodsqRJ+<PERSON><PERSON><PERSON>\r\nrSDr7KB/a0rSK9A8sbUiDPY0ylEbykRJGaoH6PdKk89GbsLXLjMo2z0lnDux\r\ngSj3PkKWiqAd/DbDHVlNflRUP8NvlHzCf+NNa9cMn6IzwRWeHoDCM9owW1B6\r\nOD1XEAqxdDgrvUm9A9pM0cm3PRW/M+QYnUN6nzGBRwhQ84K4lu16YijLqM9h\r\nxU7Vn6z9IkGeeB2ALG5WtcIZqtoEl69WEsVZ7IQBZnBbl2pnXnCV8Ulb6wQC\r\nU4bWnMUYiN9H+qDDUq/q1W6rcOvAMnqcbgsiHesxf7VP4JI4+42FKD8WvIKX\r\nx90aE8gM9ZnRXjqKufstwLLoFdScu6xuJ0tMkcGWR7xNPviuVbGXS/O6hSnw\r\nOhGgdSUYkilXetASJY7qymHDAbyEsnguVpMfN2ZsSzr1qI57Auug4+vk6LuK\r\nOn2wJ4KOZ8WT0kwKr3IM16GD/g6xqEuu9mo=\r\n=6J8e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.18.6_1656359418943_0.5917233453972244", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/plugin-transform-react-jsx", "version": "7.18.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "ea47b2c4197102c196cbd10db9b3bb20daa820f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.18.10.tgz", "fileCount": 6, "integrity": "sha512-gCy7Iikrpu3IZjYZolFE4M1Sm+nrh1/6za2Ewj77Z+XirT4TsbJcvOFOyF+fRPwU6AKKK136CZxx6L8AbSFG6A==", "signatures": [{"sig": "MEUCICb42ChFUDionDcWjVUoMSmnQrT1hIOsQfboaYLOcGyZAiEAzuLlgwEXsyzKoTyLvrc5fdzlkIEKPBChoODjLU7tbPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgYRAAgSZntlCDaTrTIxkMTs8Vihvz7ItQqInox0Shora9FUt6amLe\r\ntKdxhEObRH0KQINjGZ2MEKGVImYsfED+0grSt5B9XSYyER26ob0fz9c5qZ2v\r\nTENvGFD2AwwjL7qAXlLaERFfQQJo8r1zHX7oTUJ+6aRxOJXOIHFdZfaDmcrt\r\nJmaglFMHcyfokmGZRjV7UnB50X/RRRovJFns2OQs2ulXyoxulOCit4vKYAi/\r\n29N+pSp/RqNsa6+sOMM6iW6UwewAtLkczcLEMgiiDPM7b3snW7xgFya0ZQok\r\nrnnkAJaJ46z0FmQpq7iPPHuhJSbBubl0CEgdfwBLccuBXH9n2KPM4adB803S\r\nsUryWvGe6cXgw5jxSx+m8NSPoO93HhYl2Kl1tJLyZR/T4RdTLpS6oh093PRS\r\nGsJO28Tn14bZdX+3CYio99ktK5tqty3j6T75eMTJ2bKlMzab+fNMCgVMbZHm\r\n+bLnZeX9eqP41u1rH7dXOYen+76qAoAvC5cZ1IUGMyb/ZF7u9riVj+Q7hkJ/\r\nwVEUAbkroWzIfeax0yxwp3X36e4Qc8DFJsDsbeW8VDjwMHp3xXuHUXhkd0Fm\r\nXVjB1ssa0+FwOA/eY+KnBUnySA4PwmYlga07rodOpQZ3rwgCh00WaJqUeBXC\r\nKADhbnmmPejeC1JiVtIdxugU0o/YdQJTyyM=\r\n=bFuW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.18.10", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.10", "@babel/traverse": "^7.18.10", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.18.10_1659379605318_0.5454434581971976", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.19.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "b3cbb7c3a00b92ec8ae1027910e331ba5c500eb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.19.0.tgz", "fileCount": 9, "integrity": "sha512-UVEvX3tXie3Szm3emi1+G63jyw1w5IcMY0FSKM+CRnKRI5Mr1YbCNgsSTwoTwKphQEG9P+QqmuRFneJPZuHNhg==", "signatures": [{"sig": "MEQCICx7wXyYr+vANxUcdEkRVPxhi5qjgvhKlab8scU6R8YMAiBDIrkO0+YuecemeeB+w7obBeGIyG5MdiVkstcUTXW5Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgMA//Z2nMtS2+aKVmz3yxfmhpEo3YY/geX4kHFx0mJWKYA65Z0ahP\r\nuB1VkBCMHYmjd5/Dkmwtd9qf4ut63EK9FReWr7U+tou3ijYTCk/WGJ7kg/sx\r\np0YJb3HA3tscbDICQFIVY2LaZG91uc9RjxvMioE+X+j2RnjvJBy0aW+R0adA\r\nRPUu7SYL93dMxGwl+1CjSYkF91E6shLzvv5it9i+SzzZtXdxToRO++2DF7dm\r\n0mlZAeJ/8XCgJYVuWES/E+HV2ihdyBWZbAeqAoqMBrvt5xP1slA6fvzwKIYy\r\n6fQoW1qBcLqwl9f3lp5cAzZd4wnJ7+6bDvXRZtPotVQg5TQT2CzpLsILQk/b\r\nFzlFLMVE975aQuiE5Q+BhSR1ZHbABrencD+WmN+mm05ApX5E4GkgNAZynQTR\r\nQsO156RHeEgJV/z8Gr11PLFAIaBg2sBFwfHC58BmU2DY8TLF8qWLwKVYw8qS\r\nWoP9AKMAGEPW80lnTetGc8Im9rpKXFP2aQskJplr7Dh53iOOlX/Iyhrwa2lp\r\nzw4EYRyrr9Gdb5rqPAbnfknP5EZTJzxKw+ReljFi4WrG+bsTzvdkCMRhoRsy\r\nKCsd9HS/DSWC7kGD21/EEnn2C7BUGsqzh/9Sj1qrNVUWahdSc9y9PCM3d91U\r\nNcrX1JbC2W6bVlp0lvfu4vQCD+Zqx0A0jog=\r\n=7ksD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.19.0", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/traverse": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.19.0_1662404538744_0.8238182287148408", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-react-jsx", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "025d85a1935fd7e19dfdcb1b1d4df34d4da484f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.20.7.tgz", "fileCount": 9, "integrity": "sha512-Tfq7qqD+tRj3EoDhY00nn2uP2hsRxgYGi5mLQ5TimKav0a9Lrpd4deE+fcLXU8zFYRjlKPHZhpCvfEA6qnBxqQ==", "signatures": [{"sig": "MEYCIQCe+KkGnt527y6P5zo0DA3IIlKuPG2BMpmGVsecZo2snwIhAKKJutNT6PGlyxnAL/tvo73KS5KT1oz5eZ2lwH0FF54e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCc5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8Ig/+MeUlGoZKLydc7mlpJD197J3cTfUL+dTb9iamWUTd9KWnRoKP\r\nXj2UXpunhdOlicwcIqYbhcT9lRPcCfL+E9jgUPSRTtG+DIPoHU/BDm2EavQc\r\nRst1xyLbyJXF+AX/+0D551VwMubIBfRtFQBe/nWsneI8Txw4b/G98HX4uG23\r\nqYaA3q53aHRPtzNUkI4o1/XUoWQfhfCN2+6rb346LSrGGrs+YApG5961M9j2\r\nPgTvVVfOmvLHDVHgbQbAWTe7cJYXyy68FqbVkW7OQ8Oc9XGgWclxi8GGa+0t\r\nqwwsErzlesSvtAj/dWI7v4mODjRjY5Sba/kCb5mmLf7cl2gVWcBr4Kjbyq/P\r\nXkrvubPHiP4XxG3fWv3bVQ1+nJG+PPZuclyI6ao+BJIGUxs5QvJ5Gnl1tSgd\r\n6CT1Np/YoP8MihE9HHRucEVFAbWJh/1+IfQXCDwBZbjs+3B/nT3SPYps94AC\r\nBSzakVQyeDtX7c3eMxvw/E3Mco65MC/Ho92FMei2PybSpDMv15qfyFtr2g3x\r\nmXj84HsjLJabHX8aoAKKDQXxX2PoYUh6PZ/otnmgB8cAkBjSZowgU5howHTG\r\n+4jCEvcW5uhVrJlhNChsaqGJ1XMHos41X+lLslI1pUJLS5Di/8tGu7qzeeZM\r\na55t+L9S0nmSr83SmUwEuMoaM53duzkpB5U=\r\n=LAgv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.20.7_1671702328993_0.4443645172075088", "host": "s3://npm-registry-packages"}}, "7.20.13": {"name": "@babel/plugin-transform-react-jsx", "version": "7.20.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.20.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "f950f0b0c36377503d29a712f16287cedf886cbb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.20.13.tgz", "fileCount": 9, "integrity": "sha512-MmTZx/bkUrfJhhYAYt3Urjm+h8DQGrPrnKQ94jLo7NLuOU+T89a7IByhKmrb8SKhrIYIQ0FN0CHMbnFRen4qNw==", "signatures": [{"sig": "MEUCIQDxmtJxXPqIUbvEwXTYPfeGDIIMSa0tMonvQ8/B+mUACAIgFrt2gvncNmJ52N68k+6lWKVKlGLbZJMThgDX2eaBa8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy/cQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc9w/8DlQ+vkTzyaj2ssiEBat18njpLfy43XtbXKM29RXkP2TWBdIp\r\ngXGwU5tCaUQlkreL3l45VooEVE4CxDEiSnk/6BcAYDW4riDq3h+e0DMurV6F\r\n1ObX7Im/1aTRkYh4wc1dXhmm4eh8G8MCc6hNCNLPSVpuO26Z3ZVceWYOqdFB\r\nNHTyTBv6gcGQcGvLPHjFwuUO1E2AIxcFjSZyN4DQPNOdWlGc5rOPVCMPXOZg\r\nunO3e+1LVoHBRubIlKk6ZW36fokKjWbt0WN8VLLxwRJYB0oZLVS7GZmKkNWx\r\nSaJatbhEi87JDiDNj4tsUy68WW6quEVa8ET/XdvB7+QK9L8x46Y2hxagN7CO\r\nywh6255CAhQUARU2CxB/Ip8NdefOB29Ph2PBAwaOuYanWlVZ9a/Wch+cIEtY\r\nuO9R9E7j4TmNPAscASJWmiMI36WVgruY3/kBpLy5bbAeNWQmiPEXa3G/u49w\r\nuCDetQ4tP3qzUXMi0xe99Y/ObTfqngqQbNeOYCV3Ezkb/aizZ6lcXWoJE9lS\r\nY9EYG4Ihn50Tr9+nVbf70Ec9sJR4Ewx1MT2SgTnHpCYsfqEGkANkp6oUaiId\r\nA6ucptWfNd8yLPePXxmQKFJMLIeXSjtinNfgeiDxVmjoFMF1sm2s9aofIJU1\r\nbe76leLDQesyqA7KPj9vGTU/vQsjlX1GBC4=\r\n=/sNW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.12", "@babel/traverse": "^7.20.13", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.20.13_1674311440178_0.17262320560319133", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "656b42c2fdea0a6d8762075d58ef9d4e3c4ab8a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-6OAWljMvQrZjR2DaNhVfRz6dkCAVV+ymcLUmaf8bccGOHn2v5rHJK3tTpij0BuhdYWP4LLaqj5lwcdlpAAPuvg==", "signatures": [{"sig": "MEUCIQC+Tp+gPJcYvmSog26ZlpVycRCBcjC8UO6GZXL924KwAgIgGI/CJ0UmM4ApcuH15Mn+K825ljNHJMJ+ECl50CO+pgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79054, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85I/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbtQ//RSdSdHR+KA5P4b9ipbiymSELSIDlMHdbNwsg34EpxEhTHhr1\r\nND6i+Y2dpkZ+F9kN/QJThIz22b61TcTqmgdt5w58/14pzu4HYDmR7Hn/7nTz\r\nHWoC/3f7ZF6JjcCyS0QG+bayd9MhUvWDEDxJavaVb166zzy/cn0nd4sADEKT\r\n3ukJ3YniDM0ADAgo5JlCxyV8Ayj3auailRVkD026cp/p3yNYI3ZOnUbc0e4C\r\nFcTlmWc/PUXG11nNH8AYkUwUtOr6tXYdqhbyn446fH0JoSNJSmLf507WKrlN\r\nTFEAMyqEQf+VVt7ASvG04LiXzuc5hY0mFRyxA5lBjAVs/RtNYENUvo/RniK6\r\ncUhnZoilEUet/Vt3TJ+atOFgT+muA+MvUo/i33FKvYpAcJ2oeevfFaoaifeO\r\nvK864UJdmsaIzp/Ns/jF9xseGwMc30ihCc55hdTgiJIIwAC5omROZyFKJPBG\r\nO6+d1z3CfNPASC7/lZyasl/VTD9O6IimKFByg/vzMb+PUrzrNPKogNxv+/SC\r\nD4z4Be0JnzVR4ylaQrn6+sr+1bKF8Nr56B/iJzuoDw5lS6YP8BuX0AdVgTnS\r\nI2buOeNkGIYIHR5vMCEp6pXwyL49DMKaLTSvdPqrCTd1K5IVZQufvedKPYDi\r\ndq5UyJSjKHr36mJYKBVOC5JWq11abRftI9Y=\r\n=um7F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.21.0", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/traverse": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.0_1676907071716_0.24669823146977365", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "1747c847de83b8d6864d537a231a28b94603ae7e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-54hrW8h6g/YvyLlf6h3PA0SGVGX4S6UESSGR60Ffm0nlrtNMKTapYhDC8WhTPxs7Qio1UbTiZAEculkSAD9tdw==", "signatures": [{"sig": "MEUCIQCXCEYwaTwoRlCADubKEmI43UcER43tNZSGB1D3uW+fbAIgcm2XLlwTMh2VxQtWDB4LHOI57wDNHfgEk52gbT+pj6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+ahAAn8GXsMqUK5MU7DlDf788qGSOWsU6giQEVv+KjrvRfI8yL4Ky\r\n5zhsZmdanfMU8OB/NkhD3VobYxzPgYenQ1C4n0Ox6sU/FlYdMggAz96jsaRT\r\nSbjGYvJ4aiO6PvSaOPAgQnwOAjM77kosNJlewylOZ6tBBzpMhrFIPlwdkhQp\r\na+oVqioX83det9eH5XONpgfuhZN9ZQURCc4eqC1fGIGHFbDDVdBJuzOIQVMh\r\nT2UMxd+Nmr2dh0x4IhhvWvCUolJLqYvyioVeNwjvWzneVaCBaqXMn7A7PGni\r\nqDquXJy3ZjYRnalIKA/mjsbaCzZH0rnhjEVmA0KSfu2vu113KwRxYe8cs4h6\r\nnEA98JabvbAsS8sQG5Nj1aWLCQTRdA9Y/+MWPazteUlqn+pEQr7YKCe1A6kB\r\n60QPwlAShsELxS44CjNm3m78pqahSI3s5ZyO41KnnB6Egi2mtgIzMlQJ0+Xo\r\ng4q1hIdn318EZo0AS9lYJiuKPQI0zuVYWc/Cxg2G3Lp9K1w5lSLRiCVl+55z\r\nqF6dvdCeUmLWiFQIfh2rDfi5TiAalPiFwZfxPuJHgNmAnRKn2Iri4bBkBkml\r\nDeJw2GjH/PUVHhNaKJ9gCvDF8X+Skq+czP7FCxtuPgiN1pMmMMbYlqD7BfO6\r\nnl+izFPDQ4+f+g81WxdpSP0MFXi/Gi3/Gg8=\r\n=/Sv8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm", "@babel/plugin-syntax-jsx": "^7.21.4-esm", "@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-module-imports": "^7.21.4-esm", "@babel/helper-annotate-as-pure": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.4-esm_1680617383726_0.6116986610877471", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "7b73cd0677ec2c0a71fba6db05daa3860b6a712c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.4-esm.1.tgz", "fileCount": 10, "integrity": "sha512-ngfDFMz4AsMWs0YYLu1qR5gnvYVKz/IEzKbllrFpL7NvoSY19YVdN3+twUWIvNtIiRBTkEEHAvIr/wB744VQbw==", "signatures": [{"sig": "MEUCIQDw0irPruT7ssBKhkByVERxRfKOU7TknIM1RQRSwJfoVwIgclqnE8CXx0Ik+gh12bqBI1aBd3aWQlW0S3RA4HXdFMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXFw/+P+PBpfZvJ23RUdDlNGg30RGUQLSV+Vv/SZhVlg3DRhRsw2nK\r\n4I/66gdB9gMvv0NPluMwx/YR9jfqzLxDTEsKYblFzRRxB5a4NDNGcyCFbYP4\r\n7lFx61y6JGKl9tT3vKy5IwAFi+Tl5cIEQ1G0aKPI3yr/tAkU49UQXh/y9AVp\r\nHoQxXfZ4DKjQc6fNNRyTvfnglgpx4ZABw6tyxDJ0vzKfKJBj738vSvS1S9hM\r\nL5NaoE/aCvKg6TYGC7XHKG0+5AIui4lSTVU54VXgx7gaHfTtbCDQTzetl1rC\r\nTxF+pc/7eVgv4g1sWzJ1oyFJq25E5xSLxJWF6OSm4XeyF3QTbCASXqfhJT+J\r\nNvsg6N4D534qBJIMjEEu7OjY7kiMxdpurZ9CJY89/i1gWesQHx3JSaWTXwwO\r\nq2I/bhVtS5jLlUuE4ADqCI5Qy6wNV/DqIikVEembH6PXW/rErra2LaRD4qOz\r\n9Rdjk8UepWOFLqeh4rLCLZviNt1EWskNWPtKPq7IgtapYVeGQQ34tIDKGHbB\r\nlM8SQSn4CNhe6bku3UQzhQhR2xx8UNjCZCd2TYg0phWVlzzJflUvf0Ub5Ejb\r\n10GGH8VL2bhnrxMDCXYmNSRB4r61AJG3d9uKoFZ4c5nHF3y4kaDhgAZijA+r\r\nyhKouITgHC/yXEIfeub6bGmXp7Fpl0AXYmc=\r\n=/29P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1", "@babel/plugin-syntax-jsx": "^7.21.4-esm.1", "@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-module-imports": "^7.21.4-esm.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.4-esm.1_1680618101827_0.26818724528380145", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "3f108f87ad5a15ccfb7ae6bb3dec251d284421d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.4-esm.2.tgz", "fileCount": 9, "integrity": "sha512-UGXdrYA8q2XkAH2lMnmPrBEiY9szzMHIm7TBtFzyqUuQeQhgiiPNgUIpoTCMvvim/q3n7rLIh09ueqvBzI3soA==", "signatures": [{"sig": "MEUCIGizR8zTx85OTXDTzQBH7mo2nEsM7IgqbTzl+D9h51e8AiEAtNUB8502cj72ZiF7W7bG/A5rl+nChwa/CmXXdtZcooA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/Nw/8CLH8AyIWUugPU0P3B6RMZI83MA8NfpXvwA9F/pmip3UlQhq3\r\nRoMBpJEd3zzJSFR12eJlKbNU0uPZk2V8iabCk0FcfZiWGhmM4bGwM52pnZfQ\r\nJlsV607x32FmF9XFqO+aM6NJX8gvjc/3AphP2gGdpXosqMeI4bSSJ22cRSOJ\r\nLV4R5C3HNRu8Z4i8tsHS05XEK/rD6rKTUXGNtGbQ5wPRs0VMkRDYBaxZT+JJ\r\nqJiaWNgH+Jk3VzMuQNkZsukBWUMA3jNOWDV4lcXnlUWK7uBtdPKoIVflxozA\r\nScMz99egUhtQOLjC33cSbWsTHQFsH43THHql4wG0vKyCsE6w5t4MI1JLIIGt\r\niqctoWGzg9EZLZZP9TdEJTOqQPUbt+pSHDTrxhrlteudKLi6WtQJ4UzQ83hz\r\nL7sYDU1fAS5MsM/v68rG7hDyUT2a+8QV1vw4Ikb2ZsZnhkK7E6v9ItGea4EN\r\njfI0wmlyJwmPx7RPR/83hhZbodtdMOecpViY7kwFnfyEj2PdKuvN3Ivu9ZKZ\r\ngoryHdw1Lc3Upvko+3hwgHUKzs9vkjulntSPbWtV4YlMqYaAQNxx1yngCxI1\r\n1OEriOiP0+CsLv8NQhHsxgZAnux9ZvEGRMTUi6baV0tEwZ/gdFCaPeU14q8W\r\nUT7j4QCbrtNrPwICclLH2LuCWmmdyMENrAY=\r\n=FZZU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2", "@babel/plugin-syntax-jsx": "7.21.4-esm.2", "@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-module-imports": "7.21.4-esm.2", "@babel/helper-annotate-as-pure": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.4-esm.2_1680619176724_0.5900229545992777", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "cacef07ae9a7b308aac43ab43831ec08afa24480", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-Lk9yuN81dJUky/C/IqwpRgz7oLu6w+cpctGYtMzlkpxyId4dYWl1jD4oGOZGHL6BR8EPBhKfJAI6XfbhdIux+g==", "signatures": [{"sig": "MEUCIQD2KGaWRIQ3UZuh7nZg/Vkg6CyxY7jhgBRjB842SDWphQIgYqVd2g8li71Ni+9lFslyvo7ubNSv50Ss/uSuiK5ObUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopcQ/7Bi0KTRYE+zccm1CBpHdnf+K1T5fvuABTlN2bomt5jAdYlxj3\r\nZ3jpx8ovmJLn3AKN/iYyngaSh/JkuyvWPx52ahKheUGlcD0SdvQsUJHuI4cr\r\nymVfFoKnJEUmAvwtsODRIPeJSeWkhZX5c/TwrYp1gyjUeJuErxfeZvdswE2Q\r\nkb07ZYLsK5ZeqYOh6Y0a2VoicFyCMZHinSbyiv5chbcDK3aA15Psu++aCBt6\r\nxGm6nnn1DFpIJldjIq+E1WTJ/JLRNLxkYaRbiuje+/ahom7TJqC9PQ17fVXE\r\nQrgdfm5i228EH2HtTp7yFDfwDC/DAJtyzWd5SWn5tXION64XCtVFPEMe2JOw\r\n0FfhOaPJMBsWpF0ti12QnU+PEB6XnY8BRHxogC1fq1WbZYQ3MMWTNKKjPnuB\r\nloS0DGkAMVBna0PLWiI5s8sLSO6Ms8WvXISbzxcu2sl3ju+WrpTyiF4NMIxG\r\nQ6bCQaKkIv4IP9zGvFEN88gtN/obMpKj1U3/2hfjlcrI9MOSNYL1Owp8ojey\r\nw8GcBPKeAqJJ5/llx2bZjo9AYzRZvhz0aPvRHi2NDJ2VQ8t5bDGpGkBsNSna\r\nz8zggeSF+CpzZcE3CM7otE8oyHIRWKT2uZsPWgtBBnSYbLr5GBIxSRUXL9Zb\r\n6Njcq6mU90SC+Zx/6uVmGaKwRxLO8VEY574=\r\n=73dX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3", "@babel/plugin-syntax-jsx": "7.21.4-esm.3", "@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-module-imports": "7.21.4-esm.3", "@babel/helper-annotate-as-pure": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.4-esm.3_1680620185950_0.404973191514455", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "d0dd32213ab8dd6243ef167e95dddc50fafc6562", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.4-esm.4.tgz", "fileCount": 10, "integrity": "sha512-ubtIVJjZ4xJf+NfiC1aL6iEBjoZd1EIkvfP3mL5rYi2ztv9FJyinIuVp7D5UnsKEIa9XNhoSbbAxLeuKeZzR4A==", "signatures": [{"sig": "MEYCIQDlWynhD6IlKzCWPThD8J3G70NVru/wcRFgI5F43B0qBQIhAI9tinrcgbckOC7Y9zHUP/ViuNE+u0ncY7V1w4AGtwVH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoqBAAlqgSzR4YVIjjiEqyHnLji4Hk8EQqzvPY5zOVlEkgp1TIDLEb\r\n+DmFgFhi30KtoUH1+vafg0t6xe2cGYOzXEekoU0gv+/PE1bPLEmshbvgjpFB\r\nimid3uWnjzigseGp9Dm/KN70DKMopLioMXAnOb4xWq8XPx/cXmLS3EmSpUio\r\nBcSEO/Vni2R/oRDPqpgrbzcj8ygxp9xfG+JNVVTyPHumWvfu8wGJmfRee6ZU\r\ny5vrwUqp0HT4yl+y/l0tWXheaJhhfIrbbp0cQY8AQ1bMDEa6es8FvSyCKL6M\r\npwQ4vzfNRlFEO6tvhQ6M8GIp/NQ3fkoSn+CrKLPVCw4jkczB4XtxXhDoh3c7\r\nWCWTZA+nDi+8eLVSE6anaH7sRQFkkqcDsbFjrLQMIJ64+QcKzkrppq63FdKL\r\nG5a3ezYj4UOccCJKnChV97Y65O5CE8qz9qa+ADhIu3f6E7qbkzyvQK1WkLIR\r\nW/WW/OUk4qISEMq/lNjB0NXyrGLMo/LAh/kAc2zhYRuLmhytnLwjwpTsW++1\r\nBsvdc52M8et1nbkltLJ3FZ6u1c3CXClAeiAHl7Odk208m6ckcwFvQ/HnQJ+V\r\nnfsumCnNCOEzbXtSHWJPQAhrPNrUxkC4+1VwjEjG3jhEg7zPhYcNZKvCwUyj\r\na9kKALYOpquFeWDZW7r/D3yLig7gt+DLjPc=\r\n=HFYt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4", "@babel/plugin-syntax-jsx": "7.21.4-esm.4", "@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-module-imports": "7.21.4-esm.4", "@babel/helper-annotate-as-pure": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.4-esm.4_1680621216633_0.00798456588559704", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/plugin-transform-react-jsx", "version": "7.21.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "bd98f3b429688243e4fa131fe1cbb2ef31ce6f38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.21.5.tgz", "fileCount": 9, "integrity": "sha512-ELdlq61FpoEkHO6gFRpfj0kUgSwQTGoaEU8eMRoS8Dv3v6e7BjEAj5WMtIBRdHUeAioMhKP5HyxNzNnP+heKbA==", "signatures": [{"sig": "MEQCIQDYlpZqR1GfGId8TcllYi8uwP+A5Uf6Vmwal+MgIOsEcwIfUOQY3Pz9ZJzvfsnM/ZU3jPv8Nfoe5n1abNDbAUdCAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWiA/+MDlvEeIdCBFCVI0smBef34a1j1Gx1kUe1UqsU5hTZfnzRlX0\r\nl/58qap/YXSdTSgEHikaam/oGZUy/AsBCWoZzKiV+XQeTOEEdba4mG71Olqy\r\nZHsqphcjuLYOkYJftgj2/c3Ru02y962RbeETXpgScYnDC5USjgIdUvsvda5y\r\ncEOJESBw40rfpG/ypdgJh9zAOxWc97JKRP3eaN7TLOg7NDvUeunwCfwX8tWv\r\niLJtPu6pN+VVLm8gAXPNxyW9V+0AfbppKMfmAzL6XokpS2Iu6htB1CfDbPaA\r\nHR2Q/GeABYREU3lzOwETfTSg3lbkoTD/KGmS31cinuu2YeBuKlIRxMlt8uOT\r\nwXrsV9UHesZYxr1ATp+ktZIUYQ1QaGK/n4WHcnCqHlTbDtWpjcn5NPiqlf1N\r\nORYuEYx7niMCO/tnvGCWkCEPtIhjJgCmLDpYPXmCEuePWPn93+3aT6v0dS7u\r\nQOK+5dWinERL40rFhQoMzoGEea5ByakrvDMFkmFRiKgSzkpmaBeLwhteGZCP\r\nEBOhSVV5T2guVV1fumUvE2ft3nPnOwZdeRt5pNL2XeypcE/4aw0QTffbE7Z8\r\ndBR1wm13klnxtgkD5r5Qr0BWRf6hgLW82y3MpIBHFh0c0JmDdXFv7TdnWixQ\r\n3FkBUXlJRNRQ7s0mRN5WkldM1AQub/ONm90=\r\n=wY7m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.21.5", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/traverse": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.21.5_1682711425005_0.3689554102137802", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-transform-react-jsx", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "5b187d73d43f726a332823012711fd3f3391965c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.0.tgz", "fileCount": 10, "integrity": "sha512-Li7gdm7eGZJidME4KlXmzQdnuUwE4jhPnICgGpWN56W7GWhmCQ2LmDepyZX4zBsoSNWP9bqDcJo5wQFndcAd9Q==", "signatures": [{"sig": "MEQCIB9ENdjTmtHdc4fKltQxTEVE5gZ2jRCRdeBCvV3Bit44AiAychGfuXn6mZj0e4nOb1zUPXTZ9xz+Tzx1pYjhFUjj8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79979}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.22.0", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/traverse": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.22.0_1685108741116_0.3147422382067664", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-react-jsx", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "5a1f380df3703ba92eb1a930a539c6d88836f690", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.3.tgz", "fileCount": 9, "integrity": "sha512-JEulRWG2f04a7L8VWaOngWiK6p+JOSpB+DAtwfJgOaej1qdbNxqtK7MwTBHjUA10NeFcszlFNqCdbRcirzh2uQ==", "signatures": [{"sig": "MEUCIEA2fkAmNj7bDq0fzX95c50Vq0wM8+UUseouvC/5k82rAiEAjdwhqixor1B7ErlKBTnxMOxxbW8sTWouXmIfVrGg1Wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80036}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.22.3", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/traverse": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.22.3_1685182270067_0.9459317458833238", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-react-jsx", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "932c291eb6dd1153359e2a90cb5e557dcf068416", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-rog5gZaVbUip5iWDMTYbVM15XQq+RkUKhET/IHR6oizR+JEoN6CAfTTuHcK4vwUyzca30qqHqEpzBOnaRMWYMA==", "signatures": [{"sig": "MEUCIDu3bnb4/PZug6EL7O0FLNYzCSDgxuR2vyI8hmc7y6BCAiEA5I1qxdRolFOTQ//aiGkvzpV8ZsD/9pvgsUGoXhnQKZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80036}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.22.5_1686248493540_0.20704837797783493", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "019b6cce498568cdd746d0a1295ed9fd13a435d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-LNK9QU6Jylw/ZqJxn5NtwcFBDPFF5LGjU6Wu+mPWnoO/sbiO3DT5D4ltR1NllzePT7Bg67wjLsUpp4SFxmi7bA==", "signatures": [{"sig": "MEQCIHvWFHJD6JN8ZnzrpnrAL9ayB6nQEcYK+Qydh+AFXAAbAiARdp3QRjM32wzr2G6R3mOAf1oQ5/OFK9uHzJ8UlubOkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75744}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-module-imports": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.0_1689861616268_0.3592677828924946", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "2b7be1e0f8a03319f7b4a998980b2840ae26b11e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-F9vpm4A+N1t7TFoR4DD6ifBr84ptRuj/VrbC7exwgHZaGFQtWJBf64OsBbANMwpbYytgVdOZmN6dWs49qgHNIg==", "signatures": [{"sig": "MEYCIQChgBhrpmgenfVlw2Cncvd+ZsJsy7uUgjAYMv6yQlysHwIhANhWsfLDpT7OrcV19X+CYmCh6xtL51GH1lCgDxH7DksQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75744}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.1", "@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-module-imports": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.1_1690221169287_0.9987438866549414", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "8f057ffdc271f86faed25a83a9df6194767491b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-SWxwNwsa428tE2s59WkBvItUv1FVhBFN171IKkANxnzTuT0BDLRgr5zQ8MZrLqDbfK2il+kP6LnrmKeATLE3NA==", "signatures": [{"sig": "MEUCIC8wlX8s/lqBW3R7jiVWmV8x384w0JzjfBAVFd+uCdFmAiEA2cVMYNYWoRSBW9rrmdarU4Hq0nM/n7zN6euNAE8pICo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75744}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-module-imports": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.2_1691594112912_0.08006425844561749", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-react-jsx", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "7e6266d88705d7c49f11c98db8b9464531289cd6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-oKckg2eZFa8771O/5vi7XeTvmM6+O9cxZu+kanTU7tD4sin5nO/G8jGJhq8Hvt2Z0kUoEDRayuZLaUlYl8QuGA==", "signatures": [{"sig": "MEQCIF7JIDqp4tJeFCI2rE8+hhqxl1MtI4diSO7kFAjkag0DAiBdVpGxaNb6Z72UGvVXKWOOoRPFX/Gr31XNcdUrDHPHEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80046}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.22.15", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.22.15_1693830319910_0.8293642800351213", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "56aaa7e47ce585415d87e510b202c417f84e781f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.3.tgz", "fileCount": 9, "integrity": "sha512-WbfbeGIwvTTHirAf3NiQoNBYu+C00smplWlzjuCg5HxQ3HsQxipBAQHVw3n0bKMv8mE7UqTbxu8hAK22TAkhEQ==", "signatures": [{"sig": "MEUCIQCAc+FTUBLfveqGTGYktW6GYNkbQm9LMdDChR+7CWzUwgIgA7de+2R7uKy1g3WLJDxuwAooRljCEPjZqOE7JoHJVqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75753}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.3", "@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-module-imports": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.3_1695740243438_0.3877596013160385", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "b50e1eca0a05e13aaa3821a3c69088093b01e44b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.4.tgz", "fileCount": 9, "integrity": "sha512-U1BPNGDfzy91D4Zw2JrYyS7kTOLbcDWT3RaE9j6n/qaOkj3hJi7FKeHyA1+N8CUNBNS8kp8xRR+IgGHT+3vS5w==", "signatures": [{"sig": "MEUCIQCZVrKLOGqibp8PZfeFmzZD6a8689xe/77Z93nUpah/NwIgBx7XmHZBlnQ/dUYALq3ewofyk55fTYbRZozWj60+uJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75753}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.4", "@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-module-imports": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.4_1697076397311_0.1761246326164574", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-react-jsx", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "393f99185110cea87184ea47bcb4a7b0c2e39312", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.23.4.tgz", "fileCount": 9, "integrity": "sha512-5xOpoPguCZCRbo/JeHlloSkTA8Bld1J/E1/kLfD1nsuiW1m8tduTA1ERCgIZokDflX/IBzKcqR3l7VlRgiIfHA==", "signatures": [{"sig": "MEQCIA0vXOdz1TV2TijjqatYKbAaWaJPvJSag6wJ6/dDaA2kAiAY8QUPH3p6zqKxiiN7yPP1pu6EcvuK/BR35j7YmdIMqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80007}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.23.4", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.23.4_1700490137214_0.14805009038102934", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "2868af531b9e61ff7107205e3103f88a42059f9d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.5.tgz", "fileCount": 9, "integrity": "sha512-RgVRnFoD/hPDaK4eMu5Kek+yncPtjGSCbBjaWPKTqQodPFyBGRi7rEuHktguTG8NtKixXmD8VKxygiC3QPvbhQ==", "signatures": [{"sig": "MEUCIQC2+LPn4S+V7OeLpo8Y+bhPbQvtOifnXElJJNDlvb3xLQIgO7fXoIFSsoT4vUfQSaPrLymA/Z6Beo4zZ/+vo83fBG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.5", "@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-module-imports": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.5_1702307967236_0.47513160115826514", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "0b7b1e4075c19ab2c459e60186ee22b4b093d542", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.6.tgz", "fileCount": 9, "integrity": "sha512-GRU5saa16UincKWids6GZzgTDLrDimlbTrprGFR9jEt+PplpTHftlnq8HDjnWfBlBz6jOU4JgopaLM7lVx9WVw==", "signatures": [{"sig": "MEQCIH9E+tUOfTVHG9LMMxv//kV7G2LSiqTi70OF50qKMv0hAiAUnb03RcfjQGRhA2Drky+9HgGffpiDGrCZH4fpsGHuQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.6", "@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-module-imports": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.6_1706285665405_0.6125569246401397", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "46e49f162338bfce366a0baa887193ab918562a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.7.tgz", "fileCount": 9, "integrity": "sha512-iBpaxMc9IX6U2xhA8/SfWnFLXaPFVgKJSFSloVNqs5lMZZV7yNYOW2M7C2L9w5RAaWRkPZpGjsUNgsz8w363Zg==", "signatures": [{"sig": "MEYCIQCXBc8Y7UchMAijPWaFp0ebxinPDJEknB24uO/9zCFB9AIhAPuzV+Ncn0mQBod93Vk6Ak9hTNcwstydNiXHPXM+Z0Ky", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.7", "@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-module-imports": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.7_1709129124753_0.6314870422304231", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "5afb999f4b5b07b0bb8007c579db34378a8b46aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.8.tgz", "fileCount": 9, "integrity": "sha512-Kd+4G/G3EQztTZkr3n5oa4SvM59OIxzkhdShCHEUzxbnaciDMDBCwPBy+7BiYX/ZfCiRa0uOL6gWCpaql8ciqA==", "signatures": [{"sig": "MEUCIAb7pe0UutNzWrnoanW5PtE2I2//8lfyEx22im+zzo/AAiEAtYw6uepZn6DLSuPUjeq9ONshtNIkig6E2s6FS4YJGSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75808}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./lib/development": "./lib/development.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.8", "@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-module-imports": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.8_1712236807211_0.3318240255647156", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-react-jsx", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "4ca3660ca663d20095455571615d6263986cdfe4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.24.6.tgz", "fileCount": 11, "integrity": "sha512-pCtPHhpRZHfwdA5G1Gpk5mIzMA99hv0R8S/Ket50Rw+S+8hkt3wBWqdqHaPw0CuUYxdshUgsPiLQ5fAs4ASMhw==", "signatures": [{"sig": "MEUCIEe9+f+bcv2l4mGcYbo4WJXKUqqsnr1bFEdi8HePxtqwAiEAiBxH10g7GOD4jW3mFCkU8CV9DM4Bl96H+K4QjsxJ/a8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147384}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.24.6", "@babel/plugin-syntax-jsx": "^7.24.6", "@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-module-imports": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.24.6_1716553497053_0.10632856578649141", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "1a4372c7ddd56e0cea6c5d1476403593df97884e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.9.tgz", "fileCount": 12, "integrity": "sha512-Ud/oOIv4sSWYc5NLFc272Gh2O4CzTcsPBohCpOuL5mwpCEUrQDQDLgZVyu+VVvvIIpjGU0uxwO07QLMpylO9sQ==", "signatures": [{"sig": "MEUCIC/NkedYV8djr2EonvomSCVCAdRFdPNc0njzkV14YX5UAiEAklb57mT/YR5TQzcTtAIx+rt+/R4pxUeITLGldf6gsXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143813}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.9", "@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-module-imports": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.9_1717423536337_0.5848107295392753", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "33e45bdec16f1c5cb30f71ebb545a03a0dfc6321", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.10.tgz", "fileCount": 12, "integrity": "sha512-bYSJrGWHX/NMsJcKr5xlulhpI0BGHq6jOY/ngKMXFJnbi3UnpWwatEq0OwZKR4XKEEdlQeWEQYgiBi8ausLPPg==", "signatures": [{"sig": "MEUCIQDbGs4Db58M2p8s7gshHaXM1hh4GBG9M0GKMeIBIfOCagIgGLy420Z3xgXjiNuTpTLJubAQr602vMXxho26b5FdmKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143823}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.10", "@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-module-imports": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.10_1717500036973_0.16672150822244336", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-react-jsx", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "17cd06b75a9f0e2bd076503400e7c4b99beedac4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.24.7.tgz", "fileCount": 11, "integrity": "sha512-+Dj06GDZEFRYvclU6k4bme55GKBEWUmByM/eoKuqg4zTNQHiApWRhQph5fxQB2wAEFvRzL1tOEj1RJ19wJrhoA==", "signatures": [{"sig": "MEUCIDxqUHVSvQYYGck4GMD5DzTe9C/V647CM0j8vO2PCMB5AiEAr0vBCol4xFx4C+NxlWMwgcItbW8bDy/fSONPQ6yIpeA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147318}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.24.7_1717593351306_0.9032613779169656", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "887bcac5099157baf0c57d846e521c4dc320cce1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.11.tgz", "fileCount": 12, "integrity": "sha512-HJMXVNn0wMq4iP3nQV4cmVVFU6JT9Cw013RaGusrxfI+V3UCK5YMQjzCbgxsPncsAVlWS6SbrvEUMMdUNcdZGA==", "signatures": [{"sig": "MEYCIQCnmWBWAVmphgG85gSZnqU8Yhaseu6fS3o40AWnvQ2H+AIhAN410AoBs0rbWYyKEePwlyOcXBoUw1gpPCv+78SLsvEy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143714}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.11", "@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-module-imports": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.11_1717751762063_0.4373546075555679", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "639d9b25bf5428b74d35360df5f1ff3cbd14d295", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.12.tgz", "fileCount": 12, "integrity": "sha512-tqjsVjwEdNOz7yQkTOdSHLae63Ah67cMYCxL8TsvYm1die/9mHJQoaYVLCMVNkMg3EXGqCmXkYhUNLkeXE0K7w==", "signatures": [{"sig": "MEQCIAv26/2Sda/9YRLmHlsD1HW6TFnppeAcEiaXZZMVm36zAiACRNcVR3f7lV8omkS2OTxCXi+4i1HdbfceNrbFa0F5MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140401}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.12", "@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-module-imports": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.12_1722015236199_0.4625802856003436", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/plugin-transform-react-jsx", "version": "7.25.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "e37e8ebfa77e9f0b16ba07fadcb6adb47412227a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.2.tgz", "fileCount": 11, "integrity": "sha512-KQsqEAVBpU82NM/B/N9j9WOdphom1SZH3R+2V7INrQUH+V9EBFwZsEJl8eBIVeQE62FxJCc70jzEZwqU7RcVqA==", "signatures": [{"sig": "MEQCIDksmZ2zrjAvLEV0+H4hkXfafZIVddKJBWZznjjA92EPAiA5jra/BAPVcZi8iHaUtibqz6Ag2csPhPijyG6QEFSn9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143903}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.25.2", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/traverse": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.25.2_1722308091511_0.512932191225631", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-react-jsx", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "f5e2af6020a562fe048dd343e571c4428e6c5632", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.7.tgz", "fileCount": 11, "integrity": "sha512-vILAg5nwGlR9EXE8JIOX4NHXd49lrYbN8hnjffDtoULwpL9hUx/N55nqh2qd0q6FyNDfjl9V79ecKGvFbcSA0Q==", "signatures": [{"sig": "MEUCIQDFGlG5v8x/eP9MjwhlFA9EhOHlAC2FhTcxErt/yVuZpAIgNN6PtkVeFTbeZ8tgcRUqMtmWCOOKbgeHZNSsQQiahmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151889}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/traverse": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.25.7_1727882125298_0.05568552518082437", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-react-jsx", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "06367940d8325b36edff5e2b9cbe782947ca4166", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-s5XwpQYCqGerXl+Pu6VDL3x0j2d82eiV77UJ8a2mDHAW7j9SWRqQ2y1fNo1Z74CdcYipl5Z41zvjj4Nfzq36rw==", "signatures": [{"sig": "MEYCIQCbkV39r1Tx8oLRe0fGFQaDlLMJ2sboK0kn8F4HqR2TsAIhAMN9Zm9IQNBVPKwpO0qk8U4p2oV+0FEK52Ys2fp5GxMK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80019}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.25.9_1729610501170_0.7578463445281967", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "1c6e354bc815013840e54aa8c13bbb870fc57d85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.13.tgz", "fileCount": 10, "integrity": "sha512-hFEYfsn9PR3D6YKvIMNjyVHVYMhG/5zHId8ffNofVTt6OnZ7LyIUTxXKAsug73o1kz6s4NTSzr04EOdDSUPxQQ==", "signatures": [{"sig": "MEQCIBhV49u2Jyt+ixgNPySawZgWf+RZyvxRG2iErS2S7bJ5AiAFhskrxL7Wv3rkqTxYDHZ3L/kSdzUktfkkT28abYHg5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76537}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.13", "@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-module-imports": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.13_1729864481175_0.9026367115364258", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "a084745bcef5b6b1cdc0c8f7e74c0443a087fb75", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.14.tgz", "fileCount": 10, "integrity": "sha512-L+UdAavkROUp3XjdJcd3PE1P3mjwdCnaSzXSFPrrGJrAcwtoc/4wfxk1FTag8wc63YXnFsPBIgVFtVaJCdcX7g==", "signatures": [{"sig": "MEUCIHImECVbQgdQBTfz2WrQ7FKv8K0KH0q+lCEMe4LLmCdSAiEA1pyDh6dhBKmGHRaPB+9lR4jRsK1Eto2NDZHrBJAeukI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76537}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.14", "@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-module-imports": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.14_1733504070782_0.8690986008530577", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "ede0fb2c39ff4131c4fe72518f3c7ee4fa92ec99", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.15.tgz", "fileCount": 10, "integrity": "sha512-e4tqSnx2DyK3kluxSg2Xn+ojavUP1zJWLFn8KZaSbzhnl11vpqZZ3u/XR2e7FK7//NjEYYf/UxPxDFiaEL3kuA==", "signatures": [{"sig": "MEUCIGFvKX52yBKM//4ZH3xneS7hjxh9iIVL70uDe2wMbr4gAiEAzqoqOjnMFG0sdsU8LO9OlxP3IOKSeJF1WKZMKAc5/cQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76537}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.15", "@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-module-imports": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.15_1736529898825_0.17949968015486117", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "7c488582f944e298a50b429bf50d7680409a4a66", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.16.tgz", "fileCount": 10, "integrity": "sha512-rIku3jrHs9UJyO+c46+egkKMtpU459D5A50lr+KFd0mW1OTt8j/zIZiQmXaEaxyyehPdvm9c+kVBAGDV9HExxA==", "signatures": [{"sig": "MEUCIDC6Ycne4pqpi4uXknIxufmEwhirVKXnNoeEv2sNpmflAiEAo9NKx6NvUVhV/vuzrDU2zaKR84M2rqegI/JLxQXmeis=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76537}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.16", "@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-module-imports": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.16_1739534374981_0.18011891572795524", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "54f3b16635b8c8449a54047139e08c11d1b087f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-alpha.17.tgz", "fileCount": 10, "integrity": "sha512-VVZ1r8sPCDE5VleKc1SKtQkJ6JxAaZELLQwi7TbBOyPxhHN6n1A/Im+AV6RPGSxVnuHEvcR1d5rg1UwvRiPV5A==", "signatures": [{"sig": "MEUCIQCKLZnosYaCnUpp878IDOfYxPMP/1/osyAzGlEbomTraQIgQuC8ru9Z7efqfDXW1KNedjlRG5a/qO7K2rSYXpZgr9w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76537}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.17", "@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-module-imports": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-alpha.17_1741717528319_0.7729581270585437", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-react-jsx", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "1023bc94b78b0a2d68c82b5e96aed573bcfb9db0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz", "fileCount": 9, "integrity": "sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw==", "signatures": [{"sig": "MEYCIQCZ36TEzJzr4TNNnmiDv0RaVLvzfy7fLAmiHUUJLtOkrQIhALlNxNRm32jcQxkeftWO8R0uX3phbmreLlphwfZPeJH4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 79340}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_7.27.1_1746025763455_0.7802397985616978", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-jsx@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "dist": {"shasum": "a51fb12e8085a9de707495fc3892b421b29f2265", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-beta.0.tgz", "fileCount": 10, "integrity": "sha512-ByjLyfPHchJCRW3API9ttUd0rHsMuK4mK7R2t17gTFe84ApFN/yV/fhp6/fQs+xaUNlhiE4RpKocYMTHMs96pw==", "signatures": [{"sig": "MEUCIQD6fnBFNc5I5EoFnFKm1TrzlnSmgFEJvNdhPK8IEQ4gsQIgHN9nKU4cWRUNfSwooOOghCrw9zupFX87OXaBVr6zh4c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75827}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json", "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0", "@babel/plugin-syntax-jsx": "^8.0.0-beta.0", "@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-module-imports": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-jsx_8.0.0-beta.0_1748620299913_0.2313597072920086", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-react-jsx", "version": "8.0.0-beta.1", "description": "Turn JSX into React function calls", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-react-jsx"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-module-imports": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/plugin-syntax-jsx": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./lib/development": {"types": "./lib/index.d.ts", "default": "./lib/development.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-react-jsx@8.0.0-beta.1", "dist": {"shasum": "c355a0db38dcc828267c442e0854344f79db90d4", "integrity": "sha512-HgxDc6xb7PZ9tT83PdDd7D6qOR9vUpeVpuKcyP/LJEWFNxzvpgTNy68ELBhtOt05vq0jdP5DiX6d4b6qvZoJaQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-8.0.0-beta.1.tgz", "fileCount": 10, "unpackedSize": 75827, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCID0nYEo1ITbN6azAmXUnFCCvlC7KohsO4X5ZakzELxEjAiEAiyrE3FcyGtNF0/SP9xiwQJFWA7BSn9EzYnYjqRrySF0="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-react-jsx_8.0.0-beta.1_1751447083106_0.43945678736493843"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:43.183Z", "modified": "2025-07-02T09:04:43.470Z", "7.0.0-beta.4": "2017-10-30T18:35:43.183Z", "7.0.0-beta.5": "2017-10-30T20:57:22.467Z", "7.0.0-beta.31": "2017-11-03T20:04:07.382Z", "7.0.0-beta.32": "2017-11-12T13:33:48.329Z", "7.0.0-beta.33": "2017-12-01T14:29:04.949Z", "7.0.0-beta.34": "2017-12-02T14:40:02.691Z", "7.0.0-beta.35": "2017-12-14T21:48:17.783Z", "7.0.0-beta.36": "2017-12-25T19:05:25.564Z", "7.0.0-beta.37": "2018-01-08T16:03:32.922Z", "7.0.0-beta.38": "2018-01-17T16:32:30.001Z", "7.0.0-beta.39": "2018-01-30T20:27:56.474Z", "7.0.0-beta.40": "2018-02-12T16:42:20.076Z", "7.0.0-beta.41": "2018-03-14T16:26:34.732Z", "7.0.0-beta.42": "2018-03-15T20:51:47.348Z", "7.0.0-beta.43": "2018-04-02T16:48:43.498Z", "7.0.0-beta.44": "2018-04-02T22:20:23.845Z", "7.0.0-beta.45": "2018-04-23T01:57:50.186Z", "7.0.0-beta.46": "2018-04-23T04:32:08.405Z", "7.0.0-beta.47": "2018-05-15T00:10:05.240Z", "7.0.0-beta.48": "2018-05-24T19:24:02.227Z", "7.0.0-beta.49": "2018-05-25T16:03:26.850Z", "7.0.0-beta.50": "2018-06-12T19:47:45.323Z", "7.0.0-beta.51": "2018-06-12T21:20:28.276Z", "7.0.0-beta.52": "2018-07-06T00:59:37.734Z", "7.0.0-beta.53": "2018-07-11T13:40:37.093Z", "7.0.0-beta.54": "2018-07-16T18:00:19.599Z", "7.0.0-beta.55": "2018-07-28T22:07:42.615Z", "7.0.0-beta.56": "2018-08-04T01:07:29.069Z", "7.0.0-rc.0": "2018-08-09T15:59:19.211Z", "7.0.0-rc.1": "2018-08-09T20:09:04.220Z", "7.0.0-rc.2": "2018-08-21T19:25:14.106Z", "7.0.0-rc.3": "2018-08-24T18:08:53.665Z", "7.0.0-rc.4": "2018-08-27T16:45:20.049Z", "7.0.0": "2018-08-27T21:44:10.204Z", "7.1.6": "2018-11-13T21:10:26.101Z", "7.2.0": "2018-12-03T19:02:48.297Z", "7.3.0": "2019-01-21T21:40:41.901Z", "7.7.0": "2019-11-05T10:53:35.910Z", "7.7.4": "2019-11-22T23:33:23.920Z", "7.7.7": "2019-12-19T00:53:06.056Z", "7.8.0": "2020-01-12T00:17:14.841Z", "7.8.3": "2020-01-13T21:42:07.630Z", "7.9.0": "2020-03-20T15:40:09.925Z", "7.9.1": "2020-03-20T21:51:33.452Z", "7.9.4": "2020-03-24T08:31:18.678Z", "7.10.1": "2020-05-27T22:08:11.184Z", "7.10.3": "2020-06-19T20:54:36.515Z", "7.10.4": "2020-06-30T13:13:00.520Z", "7.12.1": "2020-10-15T22:41:35.606Z", "7.12.5": "2020-11-03T22:34:12.115Z", "7.12.7": "2020-11-20T21:05:40.323Z", "7.12.10": "2020-12-09T22:48:18.036Z", "7.12.11": "2020-12-15T23:59:40.259Z", "7.12.12": "2020-12-23T14:05:28.052Z", "7.12.13": "2021-02-03T01:11:29.619Z", "7.12.16": "2021-02-11T22:47:01.195Z", "7.12.17": "2021-02-18T15:12:55.195Z", "7.13.12": "2021-03-22T15:47:19.787Z", "7.14.3": "2021-05-17T20:44:23.120Z", "7.14.5": "2021-06-09T23:12:45.237Z", "7.14.9": "2021-08-01T07:53:23.395Z", "7.16.0": "2021-10-29T23:47:48.187Z", "7.16.5": "2021-12-13T22:28:47.247Z", "7.16.7": "2021-12-31T00:22:37.615Z", "7.17.3": "2022-02-15T15:44:21.897Z", "7.17.12": "2022-05-16T19:33:11.728Z", "7.18.6": "2022-06-27T19:50:19.139Z", "7.18.10": "2022-08-01T18:46:45.453Z", "7.19.0": "2022-09-05T19:02:18.914Z", "7.20.7": "2022-12-22T09:45:29.147Z", "7.20.13": "2023-01-21T14:30:40.343Z", "7.21.0": "2023-02-20T15:31:11.866Z", "7.21.4-esm": "2023-04-04T14:09:43.891Z", "7.21.4-esm.1": "2023-04-04T14:21:41.977Z", "7.21.4-esm.2": "2023-04-04T14:39:36.886Z", "7.21.4-esm.3": "2023-04-04T14:56:26.137Z", "7.21.4-esm.4": "2023-04-04T15:13:36.815Z", "7.21.5": "2023-04-28T19:50:25.208Z", "7.22.0": "2023-05-26T13:45:41.333Z", "7.22.3": "2023-05-27T10:11:10.261Z", "7.22.5": "2023-06-08T18:21:33.730Z", "8.0.0-alpha.0": "2023-07-20T14:00:16.453Z", "8.0.0-alpha.1": "2023-07-24T17:52:49.478Z", "8.0.0-alpha.2": "2023-08-09T15:15:13.105Z", "7.22.15": "2023-09-04T12:25:20.077Z", "8.0.0-alpha.3": "2023-09-26T14:57:23.609Z", "8.0.0-alpha.4": "2023-10-12T02:06:37.565Z", "7.23.4": "2023-11-20T14:22:17.395Z", "8.0.0-alpha.5": "2023-12-11T15:19:27.491Z", "8.0.0-alpha.6": "2024-01-26T16:14:25.559Z", "8.0.0-alpha.7": "2024-02-28T14:05:24.890Z", "8.0.0-alpha.8": "2024-04-04T13:20:07.438Z", "7.24.6": "2024-05-24T12:24:57.241Z", "8.0.0-alpha.9": "2024-06-03T14:05:36.503Z", "8.0.0-alpha.10": "2024-06-04T11:20:37.228Z", "7.24.7": "2024-06-05T13:15:51.487Z", "8.0.0-alpha.11": "2024-06-07T09:16:02.256Z", "8.0.0-alpha.12": "2024-07-26T17:33:56.408Z", "7.25.2": "2024-07-30T02:54:51.721Z", "7.25.7": "2024-10-02T15:15:25.624Z", "7.25.9": "2024-10-22T15:21:41.353Z", "8.0.0-alpha.13": "2024-10-25T13:54:41.445Z", "8.0.0-alpha.14": "2024-12-06T16:54:31.000Z", "8.0.0-alpha.15": "2025-01-10T17:24:59.060Z", "8.0.0-alpha.16": "2025-02-14T11:59:35.183Z", "8.0.0-alpha.17": "2025-03-11T18:25:28.502Z", "7.27.1": "2025-04-30T15:09:23.634Z", "8.0.0-beta.0": "2025-05-30T15:51:40.095Z", "8.0.0-beta.1": "2025-07-02T09:04:43.273Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-jsx"}, "description": "Turn JSX into React function calls", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}