{"_id": "@babel/plugin-transform-function-name", "_rev": "119-f159f26a3ed97720b0806188ab725b4c", "name": "@babel/plugin-transform-function-name", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7b54cec2ff9e42a6226748dba7f587dbee8b27a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.4.tgz", "integrity": "sha512-FInXg0c853GHvHq+8kU7ptYuksXtNxwTo3R3dk+gYYxVbbjxNbIvl2Av1vjEmaf0POQnKVgPz87k29z13L9aSg==", "signatures": [{"sig": "MEYCIQCMicJPLSRZMV/bZnmyv8wbI7taCgIcJLajEVCPzPoSDwIhANqx4chiOHPubXaNzez5jTwgoMS7Qe2+6+nBJ9PbgFly", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/helper-function-name": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.4.tgz_1509388579852_0.9836130635812879", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fdd9af44e7d84b68b2ef50f7e8c93ca1142ff506", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.5.tgz", "integrity": "sha512-/Yqfgm7EL3to4kP9q60m+YzBbbGnm0lxOmQMVwDB0EUyFdpDdBwmrT+bHVfC240xhFrdmf+lhAKUHBaWJPKTjQ==", "signatures": [{"sig": "MEUCIC20FkODZ+6EjzBb6W+iRsgTnVdftB5IKwnNHom0+y3wAiEA01cupU4nglG6X3chCk6MzJYXZ5SscpTmmrZEVojH/GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/helper-function-name": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.5.tgz_1509397076871_0.13796312338672578", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f5230b0b6d83a5a7ecd1a6b78162ded62a707847", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.31.tgz", "integrity": "sha512-MmtRWMVMUd0yFRccV+WdKi99OrbybR/FzcltRrK9uii4RxDmBx5m6rZdkTC/O/LMfU9Bv4658KgaRW1cShzvSg==", "signatures": [{"sig": "MEQCIAFhWbzkkGr1Zzz3ZkxowNd6RAJpQU+yk9v6r+qbZKk4AiABMk/JMFEpKR4vSJ6boalXz9Srd+A+lD8z798u31/aNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.31.tgz_1509739471193_0.22287352965213358", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "06f4662df9836f0af9699cd9471822ef3ad8a809", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.32.tgz", "integrity": "sha512-+6ZET0e1/kvmgilGpjwGdGNt/7Hmre9vxvhVIw1ztbM+se4iEKP79lceLNA+red22jnlNzRHVo5KzbU7vCNLwA==", "signatures": [{"sig": "MEUCIQDRUoTG38boViTULBLycmAK5wd25e0tEETBdWhdYOugIAIgHuAL67mNxxyOtgUxfyFvX3oE8OOBfbY70488Kr5+Q3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.32.tgz_1510493633818_0.5139146216679364", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3650d43c2ecf679072409fbdcdcb870dc55b10b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.33.tgz", "integrity": "sha512-2UsIbd58m2rMfBsnJcGcS+D63zK4EMIaULYFNDTbLnFs018SFgtSv67zZsvKf6Dy3/fWz8bn+Za4RBr4qHWcpw==", "signatures": [{"sig": "MEYCIQD9Bb3MeYs3ICvXIOxzYFY/p2tKi9DeL9cqF9NCyrT54wIhAPy8+GfLUtWydnJm2159+7nuMMsDZc0dvN5NWD9g8D/s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.33.tgz_1512138550760_0.048050965648144484", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cd0eab350cefa24c07277389b7cd21e7c1a5116c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.34.tgz", "integrity": "sha512-JO/xCDIiQnNbnkNQNVeOXA5rzcBGotxWH5Z8K5NROeMofIJqB30cJWJwF45WpkPlSVJZV9nMVgMVASD9LEJpnQ==", "signatures": [{"sig": "MEUCIQDuSd6ZLKF+xCwWczsKcn9UbOxdt9u8s//S2zvt6DRiRQIge7DdLSEiKp7RYGH0YwopLhX3Hz91iRKagpAECkkqXpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.34.tgz_1512225606723_0.7385215798858553", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "efad92ef7c63bc34b4f3379f319bcafdccce7b7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.35.tgz", "integrity": "sha512-qHVKPcszzmg6mOJv4Q13zGwmN5hG24yQWVQCtYxpuTIFxu/3EUSI42ocp9hPABw9BhnEI2RiXq82s/2olWglqQ==", "signatures": [{"sig": "MEYCIQCpc3wIJK4ndBa/dHq8blBLNqGfeTqBb9f8MOuGhbk+iQIhAORRh6/KOE+9DxEarjHfQDve5+d9IoHZbuxTkxAv7msw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.35.tgz_1513288101364_0.21380884130485356", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f9a61c88fe6693e3a92906934a8cabc9e0742a41", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.36.tgz", "integrity": "sha512-pet0HtP9hmklcxUc0c4TEDH7FVEYYvp5OVPhF2HNQZYuERZq+1jx5uv4Bw+lbttvd1dKnIO6tZmZIVc74suW3Q==", "signatures": [{"sig": "MEUCIQCacoOGsvdBv0uUsHMgT6i59P/DzgAgox5iHpBYKEpsLAIgP4yKTcThddG6QncVNkKzeTCIIAygfhEnGuOtUksg5Hk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.36.tgz_1514228729212_0.9979800863657147", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "405569043850f07f51b6a34ff9dd60228afabe62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.37.tgz", "integrity": "sha512-kg246z6XTsksWjGMTKSj/FG3J9h7XX2850qDiDton1nykGKWOTXMQ0lDLngiMybmZKxQUoClBe80mWhAJzdZww==", "signatures": [{"sig": "MEMCIH7CmbWn+HM5u7UGL8/ard5xzb6d7YtiyetBWHQ0X72KAh9Hxa/3rnPTxiO6a1GNEoZKrE3RghEaBIc0isdLnvaw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.37.tgz_1515427415859_0.8644451645668596", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "90bfa9f6a109e5878d9472869aa4032fce2f205a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.38.tgz", "integrity": "sha512-jvkRISpOr8b+bsnQ3d7oNgjUJqrtME2T8ken0Z8o98TY3YdFGwQVdpyWgoe5JKA5iV9WQK131en+LHj169R6gA==", "signatures": [{"sig": "MEYCIQDdGNrMVui31Rl4YhJ8T2KIQTjJcK4l8ny0LEiHssZo6gIhAKUxGj68LkWqIdUnZesxYuvSPIMW//AjPZk8oQ8Ba6er", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.5.1", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.38.tgz_1516206754381_0.35142351570539176", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c677d2cb90462b12fec9ce6e9972524a253340f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.39.tgz", "integrity": "sha512-R5bUGaAVm5Kdrkytd/8QwG6czKdcWT6CFBIz3P1CxX5msdPIpBxvCtknsU2qeIJtAqDRrpQDCLTbv7jmbJQWeQ==", "signatures": [{"sig": "MEQCIBWnp6Q4J3nZP1rzsvgG94bcBuKpwPFVEu7tgDEqEr3OAiB3eBoOq/HU9Ev7mp4AuO4kLejOTrAzwJMzkFuQCHMt7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name-7.0.0-beta.39.tgz_1517344120433_0.9375566167291254", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "37b5ca4f90fba207d359c0be3af5bfecdc737a3d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-wvpswFciLQ2eAnHAs6/NSWymPg88LhHH87BljjXDxNnyGBzckip/iEa051Dz6lDumVUUjXLukw3D2fv5NBitVA==", "signatures": [{"sig": "MEUCIQC0evyZCLa9M3BMg/mL7xDfwVnuMOs986uUyP8EOSKEfgIgD9/53WoP2zr08dDwgOBB9t/K2a1gQZBi0FuV1sd7LQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2175}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-function-name": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.40_1518453751835_0.005201757564648579", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "700bd4414fe43d789eaad7b608ad83f789deb90f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-4Q3KniM52y2DTxzPueADpbrZkYQ6ipau3jtR6HtlWYGiZMqlHs4qyQFvfbTwzC1VOff45Rqocba7TebarrVegw==", "signatures": [{"sig": "MEYCIQCUqd83dadgA5rhncw7rU9z5vJdMksfwXo3o3EozLJIogIhAI4lVzfT1c7mzQrHTVn/KiAp788L8WRHc8/VgFg34i2J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2386}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-function-name": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.41_1521044804253_0.7273787512791432", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1eb004a9abde01010d47ec7629d46b1e4e2c6228", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-OopJXZQAgBsPUAHr49Z4S4X24XJa9Iu0zPUPCML9weHloyFnkw5SGQIDLC6BxxPOmqfiB49gfIuHjbtJfUsOJw==", "signatures": [{"sig": "MEYCIQCUPyi+hqgJYD75AduduTPspSQBsQ+1y4+UJ6ll9SPAgwIhAOIP1SpHUF/raqJqJ+Q8HIhAr44aqPlBZ3Gsh9s3lFpm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2386}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-function-name": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.42_1521147121562_0.9390984687832711", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "beccd356d18654e2798c2b4a39f75fbf2470c0fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-INsMskgBJBz3VLapa6FPXPDbSWP/z1Pag6N9wHsWD7wayjG9bxcD+xcwSLkgyZNxQS9i7/ECa56DHwSU6t0AiQ==", "signatures": [{"sig": "MEYCIQCz29frZZo3qAMk3C2Xqys6DLlDaiwNSU/wNIj89Smt0AIhALgwwVz+GTwwUd4gvaBqrZEm1kSl8aI+SC3zFv0+w5nF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-function-name": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.43_1522687732387_0.2692411252942264", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8cd5986dac8a0fd0df21b79e9a20de9b2c37b4c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-GDxrbHs4VsbJjZbozMZY7AfMqGZ4U0LNp2YpZ6rMi++1LOo/pmSZ95VSX2T/XfI1c7xNVuVQDbH0QjaJUzUepw==", "signatures": [{"sig": "MEUCIQD6VaAWSLQ5qblGV8f3AYzG1XKK0h5hf/53+7Mk/yZYqAIgJFiFGXoM+M0ZZlTmBIWPAHW34biVuatokbLdCWN/LEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2664}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-function-name": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.44_1522707631526_0.1041707392417961", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0e6f82e159d4a7ed9afa9ecea07567909ed0c45a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-Y4eubAygKFysKgcCVHkOZeBUCOChTYMVmilxyaGhvmLbSq9NZiU4zfdZuZeqtnuB3R1yi8dn47Y8zrYUlYzO0Q==", "signatures": [{"sig": "MEYCIQDkchlf9LeWQznAy6izvfueHChXEDtOgjuUiJrGVgD+TwIhAIvCiLaYK0buDNd01cngcJwPfXk6AgdHpm5xto9wsX6Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T24CRA9TVsSAnZWagAAUUUP/0SCF6S3uTpHBBqpmYCp\nPtJQjzbyS/kh2Enh6E7vrpSJ4WKxjNss6JH7TTIQU0TMk+QJtFt1YiJvqVT0\nx9PdCMW76hp15Lu6SH7ejJcEeI3ni5exOuFKVFJt+KuySq+ZV5tvJLniOvaE\n6o0mR0qRFi7ATdQe6n/V5WSVw5xlEuNOZFKCnlIoo1pxkqJ4W221+kTDz6bP\nQWHElw9HBLlM/fsFuRgfbK2MmDN3QJYhd+1MauESPxhu1fOrNKdCEX4q2ovw\niNSbHVJXtIo8A3CbSvGT6NrT/voknZuVv3o93Y4PUp+gI7N2VlpJ71lespZe\nQpCEY6nEOdga9BdiulF3zK3fom5fEn1r35JSyAfEZghVv/ZWWS50p4ufmbWu\nUWb6RrAI9at90rjwXxCDjNojgdejPsYMp2et9+Dx4Zw9UdSTcAB6RM86EwsB\nX0E6YiQ6wra5uFCBCP7HPlVwzWNiWWzBV5Mb3M+p1oFDHQLnrnaA4UPWrbt2\nciFoZ4vRgHUw/opNMGW0h0HDifwTMB0xyyHsy3tzXjStM3DYRnn6cGMwBNnS\nrQsMBmXfNzTdernzGIXM2I1/zmcuvl1TAQZDF5PF8Pv/zuXMmZfxzFDAPad7\nbapZTdD95f020kJYhcoNcXMPmI0tDOkPgcQ5ZbsR6+XoFAP4XOm1I54cPnNg\nXAYj\r\n=zbNY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-function-name": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.45_1524448695099_0.4936442720443843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2479f5188de9ab1f99396bce83b3b9d39bc13bdb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-XyxSW1jm7WKOoPYHUJA0mbOkDFdlHzGR4DzlWAEwXrzEI5ep0ZP1AttAbVkxsF63XG8p2t9VtKlgbyBq4Tyr7A==", "signatures": [{"sig": "MEUCIQD8oMlwa5hXyjxkgROZAA1frkdmIV8LuLenpOXMP+B5jgIgZtCw7hsoKengEqG8gFpOCCo3ChgA2F2dJxJtk77mitk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHhCRA9TVsSAnZWagAAqcoP/jw6aSoasGyiG6urpELZ\nV350I+7T9li1Qwi6OBYMvpNypekFiZvYoPuFy8D0EXAxgnmPPi/f18/h3hYi\nsBP43ujg/iOvGWe3EeEGXbr3KoLRr80MWBDNMKeu5HoRlkJOt/dTQWxRPXBv\nu9DZLAPfNFA/S2luiIFskz3zmtgs175QI7MNVJvbvj8yZJ2xnjNz6VW83tGk\n5OAeFtWRKmA2vYElD+YjJEphH0MeFvj7Jugn/puUbAPn+gKqUEy1mviKGovR\na2sa9Tbj7dYWPtbBU7u5IzDorppJOFD+cmXCIeKw5kxFMX5rsYmDMvvH4d4t\n3UZ46dZ358HXt0vBihQ2HAeLxGzjUV7bAci/uyN/6V5IFRhn1UmOa+9Cy381\nKsFNmM2uiNSAehGRHw0MGSJZz/GbsrUWKGHKBcZvL99dHusaXlion/EtrEtN\n+jJ0Jy3GikYkyZtwfAj/vUhq/bgSPDz2BNeFhj7xa8StRqK5NwvB28zUFwpt\n+lZbunqBmGhyqq+H3YSD/lg4wxCUsaMwUUCa58bIC9prVZVMTy/cNonuWbD8\ncQxoO0AAdWSoqPRUetkgYzNeuallzv5IVNDqVz4mZZQVjXUpqWeRS6f6OV3V\nNf74Ih/mcr7kNZNkeZanAu4EbbTNQytWUHTLl9f0sYPDwzCOMLhHInUGBer9\nLNjJ\r\n=ZoNF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-function-name": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.46_1524457952982_0.371985389549385", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fb443c81cc77f3206a863b730b35c8c553ce5041", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-/5I/f8NCouugsRT6ORB1UjCP3N+Rgv/OB6SzmaeIUEpYYPM6D7WQ+4BaRYXQn4eqtOJmTgxDXYa8FgYtoeqP9A==", "signatures": [{"sig": "MEUCICIqvVJHffgFpfS/lL4XZZfOq7RImUKlvEAovygYUgnpAiEA5iKUFicKq/NjoN/Rt2cUYaYrDfIl47ePWym0HFbaUPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icfCRA9TVsSAnZWagAAeT0P/iKaLqrcDZUMnLTfSZQZ\nVUca+09wstZLKL0IaK/vKkSbvVNhkRtN+v5C3FQjt/JjG6i4Zs8o9JDO1BJd\nHPz0CPmC/FJxAwN0E0BzW5iy4r4L9HM+TpKvaDI+Mn/Qh832syxVE4ihHapx\nh9fktYWg79Y6TD94puc9bXKMFEgESy4115CMhYkw+8CUn3Xu0WaR5eCqwXyH\nw40bTE/i5MddAB2Ea+TxloTaOPFkRHllIWmhn1ODWo96dOuZ3GU5kenhkB9m\nysaA54qWTIug0oMy4CtcWyliEobdATwfVnzPAgncZb+ZUbowPHdmInT+Lbtp\nHdcoQsFZtRc4ut2jrTeA7yYcaVZti+F3ximAqafjDUBoLzfTn3hxTuCjeqBL\nin0Hpogd3LMIdI3EVtVMsdGHhguKs/L7uTQrzimNRgivZN6PvDYc0hu0YjTR\nHBfWt6/lYzmiEhMtJbZh4ghke58yv1lcP1kONN5iZdha5icjWvMXNQOUdyEW\nxol9zDHQAUrQ5SFaTcv9TXYY0fSe+ng02b3uYc9GSZXuY+CsV5ZVyzhGMKMU\nm9f4vGb83suEPeu5Tp5lpsOn7RIG3KuFJ1E21VAtGGKOJHAM254B5XUki+9J\nfgVQm3Gylfxg+dr2wieDkTKNXPt9bJ3n7GA64PVdYjMT92pE6xhlFEV/h30f\nH3Xe\r\n=oyHg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-function-name": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.47_1526343454520_0.6057636182927699", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0b7f49dbf4f8d1f37e42f2c4c8fb4d1fc1a85b14", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-RzGsBzsp6XvKLYZGlwBsSo44zvO4g1cL85eRLjwP6RtTh7J1CFWIRwioy1YQLBJsbhMfatCq98qWU1RNOBjtpw==", "signatures": [{"sig": "MEUCIQCmDP3/cMiqCZWqdymcSJ+/CMjUhUiRvxeHcohh5cNfxgIgEOkCgib4cDCPQQ4qR/zSw0lEcMT3knzj7MSkOgDmcYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFvCRA9TVsSAnZWagAA7uAP+waA/ghug1+F7QYpsix/\n8qrG9pP4Mo+2AS0hJ7fjN7nuJiA+x4k5ESddZLo85oftWTF9n+Ic/5iemnqL\nFkaPkSHrwpAWUKdmc4GRnkYUV2GgQScMjelTAol5rklCytc7OgyhtDzf9/bN\nRpQDs67elrtxGdOpzm7tCAgE3alwsO7q+GPzpe4umG+F8cvihi/qfcRxw1X1\numYQgwclDYhGml3LPrEamP4l/X2cukNfRazSAZpXyBJaBqatzOleBbZj4mPp\nP9pHmSifR+ZbxjqvPTAKlJWt1EJy06dK4Sx5u41pps937FQS81cym5ww5A9Y\n6rivWHf6WbvXMaCoPmL9Mfc91ONtNF7WqgPEX2ILvNmYXyheYkBmomD/NLRz\nEe8cHwAd+AjV48bt4C+gV5suFaB+RIzEnkDbFt553EOPiVK000qKhl4nAAGP\nCOzNM/5f/5xjSX4f8UrDBmCjKwXS7ciRw43b6q/7JtdgsGRHWi8Uw2NHV/hY\n1y2iJf1aUxqcmcEUjazNfOWOlieXxrSJ6EtG8OtrUMYWuQIgEKPjxophJxQH\nwq8FQ4jtr+KALL5u15KT4S+QM4cVj7ir4MADWWOIznFA+mU+T/TPGC9dqUe3\nON2Rl84ZTaw9TuTIwhI8DnMqkaPO13ofXJ6PEdo74Lt4KIUgapgLgMr43Jgu\nq51e\r\n=rIjm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "5.6.0", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-function-name": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.48_1527189869800_0.23769986754551242", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "af39f60e7aefce9b25eb4adcedd04d50866ce218", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-OalD7goQQ1aDucOJjfUjLrbHaZRj4gn7fn2+oUt0ibnOi6ZY10wuyfWCSVhowhVwl0/fJj9kihrZPsOgHNx7+Q==", "signatures": [{"sig": "MEYCIQDf7a2390V00UcOfBJ3IvLWc+2zm7ljSYOgYDo51pvhOwIhAP8sMQn8PPuh5+wOR1T6HztaS9CXpu/lSsbni0YWwm7U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDP5CRA9TVsSAnZWagAAgqUP/2LmVTyQs7i/IW0JpTV/\nXgjoMzaTyHKZbsX87J8kZjbi44kxT8SfF9TGjn1L20HbHq40k/9DPgp7Vrnm\n763+nvw7E4+L9lc4+tqmcC6DbrZYMKIuUNoKPvT+tm93cYvAjBofBGA64kfH\nsh1+TVYR0E8bga496bPFafd6z3d6dI+PYFYLNJaxEUVr2D93E3PuiexxYfQd\nZG2jvyhrqABek6NEq0CpDA+hWahYWOBFItTMBiXmJ+NIjWaWxKlfWVkW2zId\nRv/zEinrahUlTpdH5TqGFRMlElpunO2CZe8msqQRFk4lb7cdx7QzS88yUhdi\nqvmp/XaL2+OqX3L81mJ8c+p7yK/5acVxYdPSd/8Ykut+5RH4cZT0iRNCu7yZ\njd89M2Y0PjJ/RzvsWS6MrohGyh8M78GnW6bUiNlEXFtrMlKVj4/uEvPqrYq7\ngdTcyIt+AG3bmIN5JUsrSG9zIC6RlLagrc6R00XmNpQpcnnGat1iFnpiG7zc\nipCOpQS76C5cfQ6ix347qNJsOSmhXT76DUarAsXZj1aXxaKT+d7j+pk69iLc\nx5HGq8zLDJ/HFYFkvnrKox973nJbF1xG9jDhPIrnvs/aCKs5cKb9DQoEd4dd\nPWqzj5jvZp3A7TI75s1NI/Qa2okcT45/1mbWF/gAbpyQyHqOMQX0bqszB9Au\nG343\r\n=tb+E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "af39f60e7aefce9b25eb4adcedd04d50866ce218", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "3.10.10", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-function-name": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.49_1527264248301_0.9586452029940413", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e69d91a88ef7c7c8dbc7d14e010c0b5725d747d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-Re/MS5zqT7ZyxqOQtLuEW9G5GQKND7odDn7qiVNPZlbCJBYaKthE2VVs9bwBMXKM3QqXuM1wMOyBUUw3altD9g==", "signatures": [{"sig": "MEUCIG79+WQ/O6LZpJPOTqaJa2QlorHLKDWuisL1Zvu3m8O8AiEAsolOJX/yRwg40qhgk3rP2VdGQ3XsOUMCq7VLvSJCQnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2340}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-function-name": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.50_1528832876300_0.46762716075290855", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "70653c360b53254246f4659ec450b0c0a56d86aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-2JyPq64kHdq7oTmI5VBKmCTZSnZGg6wQc5TtTBI2xbmI6jbXdMUqhV0Dy986fXq6dzLqsGv9wMhGNJ41ey4DGw==", "signatures": [{"sig": "MEYCIQD5mWUMuVq3+9L8UqIin0CMByFWedek/+4uToJYbFrQXgIhAJDoowfV9EDtQTaghULkg4ZJAG8Geh23enS1wYZeLHld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2354}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-function-name": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.51_1528838435563_0.07442756394617445", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2401dbb7bf8af0149845283034f39b127ccc4d5e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-/5r1dOv4FBQvm6noGPoIGlU3Na7TBcX+bIcKoHZvkr0EzZkBV/UNd8JmER3p6vSUjY6R2r0IEqauEfx37G52Rw==", "signatures": [{"sig": "MEUCIQCapQ70642NGv6IGaM8fPnrv5zBg+K+aiatCbky+WiHUwIgYuXWyvNj8W2sraU197ySzoySbvC/UqoiERGpS7/1668=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2353}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-function-name": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.52_1530838784097_0.8637648897537886", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2b3a5bb364c1e1c57eccbfe25c6bf55f2804113e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-mvkWR0ay5U8IZQiCRV02jnhk0uY+DHZCbmBVQ9KAYq0mvunmtxHsk3NtEzSTHMSyXr69BUkQlVnYlWtYN0HP6w==", "signatures": [{"sig": "MEQCIEHK8M9EMqydVe02HAbHBop3vlboGx//TjpI66AXmce2AiBFiendncJOo7RlFtCk9Xp8mhgYy2ZyUgh4PWU7xWzjAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2353}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-function-name": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.53_1531316443281_0.5926655679553843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cc722f9973931337def3d1e6c55138581edd371e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-Wpe0eYtTnzuYke6Asuig5E/LKg4T+Y6EnH2Jy4TtjigsL2WUmLSVwPch7n1VRecFiEo9FYaGFmiAnweypisZuA==", "signatures": [{"sig": "MEUCIQDu0FFwsPwUHEzF1UOlMBXaN+F4IOgySYBwE+EV1kJiagIgb9LMbigx0+JCg6wxmflBMx0wVpcLMtYeOPSyqaqBHSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2353}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-function-name": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.54_1531764025019_0.7429084982527723", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "114384d56e1739492bd4ce9337dd158acde14801", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-FO5G4SLpnQxAuRRpkIc5Fo7oc18Me8Wkn0zcgiUymylDkeoc7jPRMwiualbNJPwR6F2WbVbAF2TMQcSotZknCw==", "signatures": [{"sig": "MEYCIQDEeuth6WGhjeqvXbRq3na4G1RO43fGGWe+2vOJ7lpSaAIhAI7FbzCNNFtJjuiRmzbM+KmkBJ7MTA2vazVxx1iGErx1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2353}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-function-name": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.55_1532815670136_0.5297611834388101", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7382a1cc8df0d8b04be36d83773dd6b451ddd045", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-olcHC1WD2L36/vUl/aw5STpv/+O4C/mUGOytQ2NJn0VPKqeaYCBaEboz+4nnttlvTojaxWzBURTl675YvlPcWw==", "signatures": [{"sig": "MEYCIQC6Qh+eyyGC2Oskiv/9lqhqaDujV4FjPhPYZ0Yh0WsPKwIhAN22vgV4sShr5T1BvqjivyDEMeqKtnB+Tqc4JWtC1wRl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPx+CRA9TVsSAnZWagAAqzEP/0Y4oQUaoREXvHsJXmFr\nfLHR7ms1AKZLvJfY6yE6wPjuNZSkWU4EsVhxXRsZHsmofRXclmlSlHPlBaym\nkpWXqIDSvDsskD22ZPQGiBHUoKy8ewc2K9hhTsKNIy9AZhw7tnW/MmjnS6L+\nZXjouAhpyH4plCoP52rpr7QBM6DMq/L2dWDM9ILQP1/8ZpzqJzg2kW9UPUZw\nO4klrG/kwdksBbau0G6vYb1TDA5sNGbbqx1SPvPzUsymlRfNn8eprNDjG2zi\nAXVCTv2IysK6WifpQ/VJMErkbJYQfX5UzWeFWxjX1Q6ntcPkTp3n+LDBd3F9\nL1hDOjD/aYdpSjULBcCr5QJvHqL7xdvOIWPFnjONxAkSMMObcRKORyhriEdI\n4TUYNy6BvAR7JagaWN+eIuFPU8HyFeFc5YXUVV1Z4NNedw0FAmnumB3Y3fC5\npzkeL9SSCApmir6o+z8nIzwNplZDriK63bmdlyhun2x5xVEXnNBQoBzwv6tA\nfSmQ2XUof5sJOkcjL3I3vIN/F7nndlpRxexxBhhX79OM/AeQkJaYqt8wwPnw\ntReN3yq1uFEnsuiVAQNq//pedu5VRBio4nEZ8OBhjqEM7DLshzOnPel7elL8\nQbymFL861RIA+5kQJ1hCDpVrGpOs7b4N5R1jpq32jJeEL6LmJbgUNnj6Tg8g\nl/sp\r\n=RqJm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-function-name": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-beta.56_1533344893779_0.6359203939063429", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7f278f735b7f73b01b79997b9e8cb82ab3445668", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-iqXkoh4L/OTh8lvvHr4KHv0au+oNI+9Is8SmCiVbVxRRn/dsK1VeQMoUdAYzt82Vxb2qbMd4wH08JBLbqlG7gg==", "signatures": [{"sig": "MEUCIB2TSYyRSfi3LxXvpWglypoxDLliz1kmKfDF3xBQkztNAiEA01lz2+d50sp1ZxRgo7h7kMEeO3Dwkr/MDrX0qeMm2Ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTzCRA9TVsSAnZWagAASfcP/07BjnphxCgloljzJKSV\n5KOXeYkZ9XpndwsRMLZsypEL0qdW/YJdLCkzrpP4AdexdfhXJ6vm7evJV5QA\nDzpzaECbJpQd5Tw0DAnbVwmH2jsY8gaG2DhpOGoHvSos64lZd7lR+FZQrIdj\nJ3/8yMjLrRiBe4d12iFxPBxUmT2Z8r2WGIhqu05OPdxEeSB6euNX80XaSM8Z\nOfRGqtR/yG51QzNM57BlWXvh3JDeePuEWvpZwiaV18mnfsWVQtbMAXsmhtw1\n9+OsxGqWXGqVISx58jR6DkXOekebVDtnn0lDXXh+gHbKna7cnUotvm/nqerB\n+3qVmEAzpg/9Y0anyQGNK1W54F64+sIPq9d763kZJYWCfh9Vbt/dtwELAesT\ncq0fr8g98A7DPhL/Y2CXhjmW2xMWTYfPRv7Lsh0ZTHSMLFgAh7zvM94zu2dA\nB97LQue1dXPmciGqOlLVikhArr3IGM3RhwKIKM9vYSZwcLiUEQCGKeSyaQ4+\nhKoJKLuVEsxmQQyIqDX5d2TB9vWSu54L6UB29z5K9CqBmsRJ8qtNM7Q7eQ9G\nxhfs5tFjfTYSUToingk3/HVM3HlLK3P3qED9WYbEUlw5muymzk6jVulgXWiP\nhqhB6/KyYBHE9j9xd7Dya990qr4cvtWbNNKhtKH9AqMf/nRyHcTgT/l1a11R\nFiSy\r\n=4pZE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-function-name": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-rc.0_1533830386910_0.8053205668130499", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e61149309db0d74df4ea3a566aac7b8794520e2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-MiUORPQo3kvSCYBn/T6kKIfdDKqFAnEsaiRnTz36Y6M/p6NX7br5MgqPumVNgDboYKQ9kzaFNM8YJvWLcjL6SQ==", "signatures": [{"sig": "MEUCIC4HBoylfQfF0vdczARSAznXe4gXVg5xJIaf8hkS46c1AiEA/WTrdxnvi1kGr4mLb8OP6pVqvVxX+grlCHuDLgUmHHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+DCRA9TVsSAnZWagAAClMP/ikSjwVALejjXTV/AFLF\n//qRUIGBZtEHJL/UQ2ANm9okehrWO9SdAVhJTrIuIzIqo5VCn9CZgY+sR/JL\nnDmFxf+eoiwiqNlydwE+OoRXuRfYMQana0Pm42+SXycOk+tfNuklqN0tywng\nnmvO1zykq9WiSrRYvIRMU2JGbPB8/DTAs721/TfxKIXwkMXzWlVk0gPoauEu\nqDpmQRvQO1G7P2yQLFdEpm4Gwc76duKhbKIi2UdgfetMzbBM8B0VmuaC5TWK\ntYNFqzTDLCFNp16qcE0VTaNDbWXbM8tTiji1CQ3JGK7Ia55eiaC5tr0gFd2H\nKKJ7y6hxlAT3Te/gXoBVRI0E0YGZUGopKeD7Q/9ZDuvLKduk2pUfD/vIf6jX\n04rFKRHE2BXxKrPr6FffzAA5oTQAAS7702J9iFfgEPaS3ZabmO0t1sxSc77t\ny74ucpjnEOadwAEP8QgHaIJ8qxbB11jQHiz9x7S/QLIyMoYEgyBIWqKEb7K/\nYypQyoB++gMpaGNyKserH1n/AUATEH9wKwHoOdLFsh8p7oXIX8iqSuofq+iz\nAbOhoUFbXOPz5r09GWoak+xx86Sr4UMCd6QXFbQd3Uj2kxfSMFFhvdM5Feaq\n02sE9+5PtshqKk5D9J47qLHMAEl52jXe83LbOvrGG6VPobgVwu7kSQVnSS7G\nlr7k\r\n=Aml0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-function-name": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-rc.1_1533845379432_0.18746249253267422", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c69c2241fdf3b8430bd6b98d06d7097e91b01bff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-UmP+TGefcAAakV54Gs1RPwt0WCD1MogSMlWjEjJXUCrjvWlGGUIlcz5YR2bxtvPHBv3anpt9fxS1LZb0fjd2Mw==", "signatures": [{"sig": "MEUCIQDiSg27+RzlSXlbWk0KpKncRDAeA884R36Dl68tQIGpWgIgG/j5QS9Z8nd6VNcM89uqx6hZjsSs3Ryr3qK/Arxr7kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGc5CRA9TVsSAnZWagAAJtgP/RtHDfzoY95kWOPBhfCC\nxXMHQnODZunlcGnFr7HnwZSTGdTG3f9mIZRxrPA9NSynuQUhOLirBHvKEJ60\nG2vJeQHS+7o5XLo6I2XQeP9HEPiPGhWSSvJXUm3XFF2tZYEorj+49hiZgBK+\nqpqUVtyYbdCHrmuf8jrvmQKtklxz6JmlmzSWged+sbVE5kwQdJYpFh8kVEdf\no6aIPcfd98QZhImPpue22EIWFqrqpfoPo4zVB8KnnOD16i+8oG+VFsfctI+m\nRCMtfCrT1sy0u2gJo7pJmnp/gGEdXMv8X8pmp+RrO+569LCwUqFrcKd84/tV\nAwnuIakcGGDRfJ4JIPDoLt0SpYziKvNCWsWMur/VPztW3PIWHc89wYQnCZC7\n4xg9lMduPAoc80qLNCFCuuAgAFmPihTMLHjNb8aqzbZTnGtyI2j1IPoVD+iO\n39U0a/jFGsFQ6jaEc1jCUtI09Qinf4bE5+OX+s/m+m6MHtHJy0hjV7KhI3EI\nJa3YzEpnGQlSJ3qswC3TMlP0/FXTdG8lARpHOYxeRji3hBtfNmNj37kEPlRZ\nMPfaT0x/pyc1wLoLOkjw6HgTI+2xePH49WL1ngL4DHr+8A+ChXXvhbKauWVr\nQdRTw5an45T2n1BBvj74x/onm4QihUkVwT2czRhhfvpyyDQCMWS2ZJEAqF64\n0Qga\r\n=bLdo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-function-name": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-rc.2_1534879544486_0.7774907663545021", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d947d63e9d50e900885cfe86df7e5d3f772cf66e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-NGhopsJ4Ui4uN+9uwn1Fe8QJKBGJXmB87jZZNLt5gunzugLBv+PTX3xDs63fuUVA4WFYH47YFQxnZYPqCS0w1g==", "signatures": [{"sig": "MEQCIGGNXhPd/J1KehOUypCCSBmAfV7Xtd2NVq3T+/GqOCmrAiA3d8qB+ugyXupa3j9QZULWN8oBjRVXvstnzVAIqU0eXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnaCRA9TVsSAnZWagAA6bgP/RQKy9dSHIPNhShxGvZc\nt18r+y2oh0oSIX9iaEigjK2nt33e8idW19h02XjzVX99UuIYd7+a5uHUYlkL\n9SOGqTLGAn8Rgrpvu4p+yYrzw21riqJhNQ1T8HiIrIWyxmB1kv9bTTEboqAJ\ntNp6FY8yzxkyrB6dXP6BtdpEXNoVRzfGlQ9kze2w+7FWNE9D00RD/0DK/tDl\nygdzkVTGdtLypmtdfbwGrxNi9mgJQAQ6HbgnuwsazWjNAAh/vDz5uGi1G+iq\nfCNT75B39yN98sUZaLgjV26dQu5YJq/9LgoCYDQnb8j70dm7SsEZRVtdgo5+\n+1elamooERd6MdswPNaxsoxrlH10/MKvHUS33RFP9ailk5KkOFu4GLT7b6fP\nmE64r2YtxW6FEWG29QeKbOPyEoZtD/5HjY2avudxO/bqUED2UEQP+5E+x7kP\n1E5gcAFLrFUIfQ8OKTmDLhsJQ3qer8vNIyt2Uuqv8rt7GZzFai6PLH/2XNFR\nrjy9OjgBy+dibSKDhUoo5uMyXlOfkZtESfX03Lyfu/Izbx8hAo1rbkf9gHT3\n60zTUu/5PvRdGAt9rhVAU19AFBQQjvq0ADDI1FEAqFOfQSoLp7YiHrH9S/yI\nHldBX89PImdAZVgdfS29KzizwOsG0TPf2KRtaqwP5cU6walBsAsZRCcupMr/\nlTB6\r\n=ZTel\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-function-name": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-rc.3_1535134169479_0.0387286211831992", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "69542bcb1a1a775f8b64e80f8617ff1c4eff944c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-VpSwUSBcv4KLKpsXMX8SczJRcBegWYbSKui1nq+7qcqz4KhDsHtwOw1tjYcpmV8em8RqHW3pqlDbsNtJU+eTrg==", "signatures": [{"sig": "MEUCIQCR1z2ooHNq3+HPnyd53JUADCxOM/HIzR03LebLAsXQ7gIgZVS/xwKZ8aGfJG9N7QICUfDGnmyvLO5as/bYdEQ+fOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrUCRA9TVsSAnZWagAAb/kQAKTjDs7McmxQabbRORSM\nkHJEU3zZhv4lpWEJP3XQ8LkDz29102fExT/UM3e7o9XBEDsRTvaFs26vT6p5\nAFMPqG1CBo+nk1KRiQh6cQMdvn2y076zPloN0EWb+poa72Pe1UDtjC1CYN80\n+H7r3gfdoQIeVyX7td+sFcUjG05i2afn/yD14fbyE9wIEYSXk2KWd56uWPz0\nUutUE8/kFixUndQREz7yGYE335I7Kw9hHo2qJJ85IDhzaydCKm0JHNNivIfA\n22HhDZAoG2TcQEnSL3+sODBPxKBkZbnyb5E5qraKAOkPoV6RI8pZsEADW8Us\nDX1HmO7TtWbamG3SL16k+cZSgCUtiYaHVdRv0Ca/721tSA8pukd2fHESF6Nj\nLl80n4znmxqLkH01dFpVGpz9hACLAfKcF0rkCMKC4Sbl1KWvK/ENFIuI5szV\nfEN0MkapyccciW+Y9B+KlAb0KnckWlau+LzS+pLIvtOyaXJUfMDblTin+icN\nR4cV+WByTcOqQYPTV0flJPXiwjOWKV9a0vOIELnrHaG2AWfaIbafA4g+5Fpi\nj7sJ9VPBYfb7mobVczPEeaGIGyAwkhv09WJzDmsj3wZb/iJzYV56aR+yGwh+\nTvrbdvVhTxlZKWxAqoZNxCbl51gOfkcBf3j1nXaxevcF9cGICG1DoMyTYlal\n/RQy\r\n=u/r2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-function-name": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0-rc.4_1535388372056_0.6922569060060517", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-function-name", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "eeda18dc22584e13c3581a68f6be4822bb1d1d81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-mR7JN9vkwsAIot74pSwzn/2Gq4nn2wN0HKtQyJLc1ghAarsymdBMTfh+Q/aeR2N3heXs3URQscTLrKe3yUU7Yw==", "signatures": [{"sig": "MEUCIQD/eQVPoXzxU7o7w3ZpOkmpxvO8DHOdtgG4PoVCL1OiRAIgAgiuuuC+GBcaOEkTZ2vc0u86SpQ6tzhqa9TNkDhUFNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDICRA9TVsSAnZWagAAZGwP/jtaReaoIyDsDn8n7G8T\nTnE2sTEtrSjWMegfc0nYrHq2Xy2mNBaL3pbU01fKpFliAUBx+M2cNhahkXCz\n6p9bOOSNmnYs+90Ap3RU0/AT758ruFyzX1nql8P6UPx6MfMARUQPNQS2ypTI\nPNanD5EwLIgK7Fj74aPUQ0edIcLwcnTmQYeoV1vp2b2Aarm2rp+BFGxVYSa6\nihwnmuBsr3S01Hehc0jmzC9c472dfLvODau34KfWgBVu3kcw8gBJLkLS+QVY\nSuTNMhla9lJO1huP/HMEoVm2IhCVeDYI3t7eKZfuo85o6327JG7MEHozwlwg\nfpX2QHXl9Wwnqr2IQyshJQiIxTuCG4OHGw5zM02msltxwMvx+NgbFkbyx3kb\ny7Vo03tQliNhChWVeza25o6CYZyKbwa+H1l10eLDyur/Vvg0DvVDYNa1TSep\nsA/R6ZkGXRrC2TAjtaesizqdOgF9cy/zy8B9qk/+xbWoTUMVF7bJolQ1FgX7\nWEOAlpctBxFKMBKLm6GwGwoeU1BYU2iVmZ4goRIcgqqdmCV1sjU3vNm6KZQo\nIdkzb73ddSLoXxgDQ+G3qJZad4RIXh6pAM5QFXZ4Zl3weisy8D/Chy0UxaWB\nVJbmnmkcWDMllc7A/pBFGhT8013xcbOw+/XiBqrGlJfIA/46adz3zerIkcMq\nZdv0\r\n=51SY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.0.0_1535406280390_0.07129278476404477", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-function-name", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "29c5550d5c46208e7f730516d41eeddd4affadbb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-VxOa1TMlFMtqPW2IDYZQaHsFrq/dDoIjgN098NowhexhZcz3UGlvPgZXuE1jEvNygyWyxRacqDpCZt+par1FNg==", "signatures": [{"sig": "MEYCIQC6P4wYgEzTZfnszjv/hQ33stxykXDwGz2J6vZmHfVtGgIhAL1m4ou+HMTgx8mCoKYTV9wL5A/ONoJeoyOfg8HZo3yN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADnCRA9TVsSAnZWagAAeVwQAJVucOZ70SI1n20EwmyY\nFMQ2oho9W3Pt0wGaUpKqtTJqrqYiNuZtOxtk49PCI3ZwPmwSunXyK7lOZ0Hk\nPmojGKY4hQs/lUf3vG1KT7qCbuZEsUc2bVMcpaF4FhK/ygPPt32zMpHqIRGb\nvmXpRmFueSfWyBF4uJ7k4KxAZHP6/qKff+96lmMVIDK27daOStvdPR/5FlQ2\nVEXgoAahf1MxdNnXrREe5zzYzlmXtZ3ctSip3uP+Qvf7t7vPn6TBhXk5+CTX\n/54HRvZPHTrpnZqm3+1HqPR3C066YOPnbf51sy95eC+tZWF9DcljCG4RU98A\nr/VK79KI1VdFi6njsY8iRVVgeLzysOYjNFQt3TjwdsA4sUDALiQ7aHQ5Ao9k\nMAmTT2ywGcBOMHQSJdJUzQ/c6CRU05vlbFc3nMkBA31LJT8M05wB77Pgyj1C\nAksMsgVT2d8bvJENqlhvSXQImbCpST3gYVRD6finRKgCks+SYqyGjmdrS9i0\nAy6sz0hdt67xr9a/3WTRwSKKTIBYlE2TpgvCNXvtih14spVOR3bGGwLGu5Z6\ngoSwsaaRsvvv88nkUvoVP48i9BNP8j45auOOWCsvHQhWKG2YRNj/QYM0TWqj\ndC7ZmNoHeGZXPSNirEYl8hBbkgx46UB4EQtfn/giT9OyY8xwLmHmOKhYJjgV\nBKJA\r\n=g0n8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.1.0_1537212646934_0.38104105269772015", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-function-name", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "f7930362829ff99a3174c39f0afcc024ef59731a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-kWgksow9lHdvBC2Z4mxTsvc7YdY7w/V6B2vy9cTIPtLEE9NhwoWivaxdNM/S37elu5bqlLP/qOY906LukO9lkQ==", "signatures": [{"sig": "MEYCIQD8rDbcaszfqPJ+3HkjLj3roFgXW15Wkv/0JpulHc1j8wIhAO6BhFOEnBqs327kH97lc4vFs0aoe/0fCvGWT2tCvz67", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2DCRA9TVsSAnZWagAAkK8P/Rbs32kF15qJCbgDsdDT\nBcxCab39MGB06rbdyVJ+d47ovwxjsSDfeQDBENiwqVJeFXVHf5jwjnNWtHuY\n6zyrcJpsh2O//BefAWdxBI02Ot7rCXgJW5y1fEMtDx3ih9vdHKdHo5L9TrlC\nkLRadPY0vNwk9wSQyZgJfwLh8d/raaLfziF24rvV8Bn87cQa6D3eEW3CTikG\nd9QA2dYk3ZAb5jc2h5BgKw3l0BvkAREhnQ8aTB/XfLVKKhxHZj7nc14Dlfeh\nRIOO9pcAdsnvzp0sqYxSx9TpwFN9lSXmUbkAyFXX5aHMV2VIdVGiXqpZH3Di\nmnFpysA52RZuB7YcnxOum0g9EWEpGhklxCghMqoYDmOTNAWI2UrV6bYNTPmm\nlwueO8VHtywn70VZ2ksDhevKXW/SPZ8B53EK6ln2+gW/9W0s7evySZOCyVzv\n+p6fkmpJfpAeIvXHpHBtmnZIeQXWBadhK8FBivZBcT5NzkGoAZVkLzOPhFva\nFlFS/55AKgAxLYX6CzHsDwyEAByzFvfCtb7BxPVTuTMblKZwxy18N5j+79Yc\n3OM9Y5PUwZ+B/xaFfbY4Czn+iadVxL3BXBUfWK5DRJJMpavWL9yWm18ZW3EI\nIN6dGuqyT1ouY/sI5iDDwY86ARamcVCfHP+mUiEjB8j+8nQ2su9c8U5d+Qs0\nBqwG\r\n=PJgO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.2.0_1543863682839_0.40681328508608106", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/plugin-transform-function-name", "version": "7.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "130c27ec7fb4f0cba30e958989449e5ec8d22bbd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.4.3.tgz", "fileCount": 4, "integrity": "sha512-uT5J/3qI/8vACBR9I1GlAuU/JqBtWdfCrynuOkrWG6nCDieZd5przB1vfP59FRHBZQ9DC2IUfqr/xKqzOD5x0A==", "signatures": [{"sig": "MEQCICdEGELRqce9Zsi8+wjdELhtZb/QNdON7+9kQO8fJGK8AiBmvj68Rrb+rFumUovG9t4wHl2a2sPAYEDwZOnDU3qIBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco75GCRA9TVsSAnZWagAA3RcP/RcDMFpToEiaaV6V5QIX\nYNudVPLrxHLkt0gJZ9fRt+fB3BVAoY70J8jXB1Gv01V+YbSe2G5aZcIJ8PeF\n4UxFoZUPkjGiRJBM+8HbDz8srj+X7BGfFxEh+WTnoHeqqdFlOSksUiL6xhHa\noVAGeSvuSyDszm/94/pWzufP5yz78zgiphdlkr33WYPgFd2+2CA0m8BDs8p2\nETaTM4kJOO5lNaCtb32knKzLLfOw9W9+5AIn3FYVdBQOgNRFZug2DgTZmmPs\nHuDudaVYtYcRt8RIdQiZP/iyD70dCvsCYIPuZAULfa3t8COxXyoPnZPEE3L+\nnUJTlsYVM8fLQMiCqnGs3kqJjRnaQEs5d5CzNY+eAq6MfbdJDr/JBFXIdzrj\nS1l5wXcr+8MmEEdlkOYAgS2L4rTr9Z0RUyVa0MXkwooZrE7pReoQIkxI8edt\nkjvx56EzCJzov/iwrP/6Cwo9AwXliCfMFqAoQY9pew57QvCdbaQGUWe8eRYL\nsWWRYWeihQKecyLmRJDxZlL48Y+O/kSjrCkL0uI/k+ORcQdpJjoAmSEDJJlw\njChqgMey3VAyJAsDEnr1RkYqOiQqA+In87kQfKz60ezrH4q1nBOpuIUp+/7m\nAnNNdCY3cDOhy/+pxOeJ7l/TGKN+OSR5b4EQ4dUlW+RTdqDVNUWp2phJmBtu\nuAsJ\r\n=Diqn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.3", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.4.3_1554234949787_0.4650298588095074", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-function-name", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e1436116abb0610c2259094848754ac5230922ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-iU9pv7U+2jC9ANQkKeNF6DrPy4GBa4NWQtl6dHB4Pb3izX2JOEvDTFarlNsBj/63ZEzNNIAMs3Qw4fNCcSOXJA==", "signatures": [{"sig": "MEYCIQD0LbQBp5Si0Blh2EIfiGGO7nBf+NeeDfI5zeiTbEbrgAIhALX4tBluBVqbBiLbQIWxuT7NDCzIWbMZro11IIw4Ry47", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3I/CRA9TVsSAnZWagAAPlwP/3bCg9mt5QsKramXLgeE\nGZu4y9xAJucjes1SjB7f40+B9dBzsJMJvt0vscWJZTXXhjkMGolQnp5il8Rv\nkqg9Y5brkn7yVpdOlZikYjreZubG/UTVBez5EMvwsquH7wjRjCZW1AVh4Pe7\nozy1RpsevQzyTYPLOijpHaSdpZEA5TZAiRxluR6Xn4vMM14AdNQ4SQTxQw9/\n1ItddLLBmgA7yBiWCeXECjm/6LUGlqZYxIua9kR2oAN1FB7yWRksKo20KN5t\nr5+70DvgEiWqT8ZsiAc11Ym7tmH9y1ITNuPlAQIAnOmDlfDqlDr3vRKQUa2G\nCDW5SiI3PdSgtscLGXEnamMp1gWZQYwf6zqzuh2YrkPj066B+VALZZC/fSad\nlWcim7xRg67WJc7XaQFYpdZwS2vjtLx/Wk/cWfSZ/Di2IxBfNSaM/I3yTFuq\nUdwe620H6EJxL3OJaeoaHyYp7RLjJWfTSpQX+K0Z9S5QJJvvNEzGgJyjloYT\nVKg6u+7lwnWq0+Lly88QlzM+6CDpkna0KvOodPkFHgEYwydcE05aXxRmyu6X\nW2uzyC7VLxTXtohhXrqusRSTodifp5Z2+z3pT0SU9iCSZoHpSRc1Jy1vKB9l\n6lXpAEVuo0xC8nhgI+yOchSoAcZcnE0xBLcEAU8WEpQm7KbzWbEDMTP1NTv2\nwRk7\r\n=ryfw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.4.4_1556312638536_0.6306864721770942", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-function-name", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0fa786f1eef52e3b7d4fc02e54b2129de8a04c2a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-P5HKu0d9+CzZxP5jcrWdpe7ZlFDe24bmqP6a6X8BHEBl/eizAsY8K6LX8LASZL0Jxdjm5eEfzp+FIrxCm/p8bA==", "signatures": [{"sig": "MEUCID7zjsDvICaWJ4CoFILEfucAU5aKotg8mgUb7MSLvu5bAiEAl9+gxOpdubi6spRCFING+LurkIuEjPfL7FiHBsl4Gis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVS8CRA9TVsSAnZWagAAvSIQAIFaMEHszKvMtAlRuFI9\nDdjul4Mke2HjWMPRviMEdtEStRlKWSvSV/4dgBWdt7/B3GM+5cMK0nkZtxeu\nhnERizWqfrqnslwbFXWtPTh2qCMkaHozUCgTpj8yCRs/LROAxjVZTL7yvRf7\n6SvPNrT5cf2dKfbgj4eP5IFs/rGqU2VncWHjJJmybmggqK9RnajThn4RHpac\nnaLEMwvTuQP0BeX0pj+YQvmef22h8sZs80i4rU7j9zEkf3j2fqvbGErcXHZV\nP+7/cyWRbcrLNab0+9EIHK/Z0Gq8Lv7OutrH6Bl2xixlUgEYwZFYHIzLchJi\nGCw/0nQomLSSMWYACf3uyXiZrPzWBOcmbcrQPYAfuvllA2fyXLtmg9z3S5Z5\ne0il5Klow1Sqo/9Wtn1064T7K08GCuOqzw3RWH4Josa7WfgMuU+F92/WZS2z\ntB0RmwaZv7IlUtenVFbJT6R+tFf28BhnqMz5rVB45R2D00WXBPDMS/0hb9pR\n+Ny9zIjX8IER7xSimyaZrek7hLKzL3eMVQt8sEtU6vqWYOdCRko/K1cIbdS0\nkGeJWCXTBdIC9ACXY+T5p+6Yjq7CKehKP+X8jgu825+KTHp1PABldLklmoyk\neRs2OiDwadyI1RrEI+E49vRQRVVrzyhDB2DBXdhx65BF5xgAWjlkqPpTsEDX\nFphF\r\n=ePOC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.7.0_1572951227860_0.4231073831049752", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-function-name", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "75a6d3303d50db638ff8b5385d12451c865025b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-E/x09TvjHNhsULs2IusN+aJNRV5zKwxu1cpirZyRPw+FyyIKEHPXTsadj48bVpc1R5Qq1B5ZkzumuFLytnbT6g==", "signatures": [{"sig": "MEYCIQDo86ookRDkFtp8f98NgKpM49ncOWUKCuU8P1NJ4RaFCgIhAMTzMbly4fdKU8I+rC8OT676mEpsY2IDqAe0OMeK1x7t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBVCRA9TVsSAnZWagAAtHIQAJGKVeVRVC1bskJiF6Iy\nVdLxFiY5b0Bg0Uh+VIwkS+PbTJ5xxQcWzIj/Sr3R4yx6IHE6He5yqhK94Bik\nNioQo0gJnFcXl57AJCm2XV9BAQY44EsM0DconqrU500YwGJc+iraCfbPbLs3\n2WD9I25FYs9EKhRLSFP20VtxwGS2XPB96QpEfhBBMlafhK/+FN9Ite+h03+P\nxCj9aYeLEcWMqBodQh+dfW19VCfyz5j7Ne7XXkMztR/hN+Id/a+jLC64U0Bo\ne7cuha4pZrwIqHQb0R0QcBRGUXr8WTJQ6u9M3vLpa4XRBYoVKNktsFPsyH+e\nHUa/FNViepMxTKE6LpN1aPntLfCeCxjwxAPubyfA4xvNoRe7yqVXKP+M8BOz\nPO4YNCwdDR0ka/9A9fOGMBQ+3D0VqS2h6HBdKMF4PH1PpFMhzfKzJYGdn7LX\nwAG+Rn/Ep2xIsb5yyJx4ldfktTDu6UQM8gNWnruuY1g+2byZ1UcvGeQw5nFt\nNUqrFtZ4AndzzpGFHoLuHOe16+Dt2dbnmZiq7u2Mfan/zEeuCYJiBFZPNbHT\npKkI4IjI6lQqbYbvjklxUjVvskZjCrchfh9uyzX3l22FLg0I13kIIW71BmXG\nObJvBUexhZuwjsd9BHhhVYccPBQb8MLHosVGXedOpOikW5ppdYl8YyhwYqjy\ntdSH\r\n=iwpc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-function-name": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.7.4_1574465620559_0.8524979654798572", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-function-name", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9c5fdb20967f151c0e06419621d56d63120653c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-YL8Ol54UKeIyY1uUGfry+B9ppXAB3aVBB1gG9gxqhg/OBCPpV2QUNswmjvfmyXEdaWv8qODssBgX7on792h44w==", "signatures": [{"sig": "MEQCIDSsv8f9sZ7iy7surNJ45varuFww+Lag8l1bBTd8Yv8iAiAi9u+T6LnaHS7ZwlJl21TC7KSAICTTC8QPPbMmTPEDuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWNCRA9TVsSAnZWagAAIvcP+wRhDChZo8LdiB375qij\n+E867amcIVFQNomok52FWnxMlFphzRlDGEzMp3bKbvHB9WP5xNyZRW7hAjjf\nm2hj5ACMLOMgzGQB/VIx9U3C/Qq4k5KuGWyECh3ya76HS0n8EtwxkHZashYz\nBotYCYzqIbJUc+CnkCKbCxW8lnQaGB5ISypMdxhdwJnQ0XPBsCqnTQQd7MUZ\niEIQeyGhR62U2fIqHu5gFtMxmPMblbX+tZkVl5IQK3etiQe2fD3QESSlgeWr\njh8wLxO5nN3QW1dKMbs2RFu/15FzE9W0hyaC9i3KEip18S/PGRJHds7YF3dq\nCrgyegl/ZElhTdybQmIN1tB5cbXz9EA9Hu43mDMRSO6h1kijGcb8S4Oupp8H\nhw+9U4X5h6IZEJGQHmpgL/46pX6IFzt92KSBMDLqO0orsbXHJiUTgnJtWFqD\nGuSOt+y4u3kPgY5grpkiQEBY4X8eUJX/3w+2Hvwev0x4VXki7z3Jp9CbK0v6\n+Co5CzX/AeO1I0OMcZCuzfNb89kD4VNh+r0TV+x6pjB2p0a8EcteNVFXWe+x\nf7aX24MCmnXXFhgYAlsXNsRDxTWZ+QVxNoYvNRs+EOkKPSJHp18eCwMeiYh9\nyQmGyFWyJVuMRpvloI51kk7xYMmvGAOXpKyVPw4FSiDQWh6/tEbwSEzn9/HM\n+3ZZ\r\n=w2gl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-function-name": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.8.0_1578788237087_0.9569588627471184", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-function-name", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "279373cb27322aaad67c2683e776dfc47196ed8b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-rO/OnDS78Eifbjn5Py9v8y0aR+aSYhDhqAwVfsTl0ERuMZyr05L1aFSCJnbv2mmsLkit/4ReeQ9N2BgLnOcPCQ==", "signatures": [{"sig": "MEUCIQCkla3RAzzXu6e3opvvFekHgNKx/OpvPEY8t35gk/h6TgIgNbAo6gTWvDrKtxuv+dvpufRGNQR/uGUSWubqoHmQXlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ5CRA9TVsSAnZWagAATL4P/3w9zzdvMWB+8fjT53l8\npR1t2QPnlPAnDIHyvjuoZy68JByYoGq4xevn7GArV7UJrXA/IoeUa+jcyZQd\n0Z/edGCGFipHm+tF4GU1eOb4OGFTNPR2Hdl+khJ6UBnqcHsQJyQ0snLb1tGW\nkxi/Li8WIFjvGvkZx4+pOoFubmEWMfOpx05xVoFxEHgdmPaFjNzPhrXRCRdO\npLc6+PTRqvKphHAFKHmL5XpRf8K+4cUhARHx53cGDNY/DMj+Y2N2Dm5fizbv\nR2PL0cRwvBMNubBtUclN4C87PRChvaCDcXwQ0qKxf9MDvwDHSApj4nBHgFVs\nmY7JpF6boDREqSIoy1qZCinDIwlsngA+DOjc3/QUrJquTC50QljizJonBSFW\nYkCbDvymQMRYef6OTy2zHby0dzgr/QFooXb6CwQclmYSzrzhrAQHCrKOE1ur\nLJzM7fLfz/U6txPihGe1gBKGI7hKiLOMqDxqKUXSA4ixZxLbl0gV3mkvs5s8\naTaZHCDkyTUhvPHt2UuV88sA9k2jVmmKQt4EMh4ymOwN+XcxNzISHVbfeFVf\ngv2j9E1uetuT/BY/tDMcZ855UZfdm6KhghJYgzCFFM+uTcGjtMHMT7bS7I6O\nEQ1ardcHOu4TxJpV1P6BQuh4bVGTqUX7qdYpGcRJPRPsaypRqNpawzQXJUoc\nqE28\r\n=qvpI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-function-name", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-function-name": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.8.3_1578951736956_0.7561232758754672", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-function-name", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4ed46fd6e1d8fde2a2ec7b03c66d853d2c92427d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-//bsKsKFBJfGd65qSNNh1exBy5Y9gD9ZN+DvrJ8f7HXr4avE5POW6zB7Rj6VnqHV33+0vXWUwJT0wSHubiAQkw==", "signatures": [{"sig": "MEYCIQCOYq1R1qXvUYuSySWBBA6d5bn+2HwpZgsB3SLBleT3dQIhAK7Dl+3+eKxHLri63ehkuZBT4X21aMJVoRlj79nf2Czp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTDCRA9TVsSAnZWagAAs24P/RYuJlo5nKSrUFvfn69M\n0sqBN8DERCHhxWNOEhR5VxGE66fZ0anBW34MSTDA0+dZNSSzrQ/8aaIHN5IW\nftdS8h3sVMKYZRGBX9MJPRDj3BHLYw3zc6vDPaVu18nv84cOb7bpKS/HCIJD\ncB023LYR0VnPrqqERwdzi1A732HzWDEwFKII+ZkdmCvql3MJW3DY71N0mA9x\nURfUbfPx9HgVChJO9th/2FvfYEy4dhRsi1V3bPBtYzG/jFuZCXafmOli5dgx\nF71xeD0Pk5VlAL2TTGhKwoTc1PwX0OqtkOtWvmXhlskkBXvSGJTThWuU7Op8\nlZcvJoiwbAC5N4I8hGOEZBUojrRM99SOJDyvdTYQW5CWShoHblXeCGhu2pKi\nsTfM3NlkAyO3Uq4me9XP1xkWgId9wWYZEl5BAbOJnbsqkyJcdnLV4OVsXsep\nv254MLLXg77adP+VSZec4y3U7Sxv5Byimf1j3c7Zfj6vIOy0mrnq+Cff3Az+\npYq5G6uv3Iv5BxioK9tsfSFSc7oYBm2wr9mLfSzJaMmxrII3CIGiOOcHikuB\nwiO18mHPYoQ9dOCL+bKfcRFsuXo9TcsCW6hVXEATn9z9mvZseIy/5G3ZzEkT\n08jlj4Rx455RLHpE4E5XpH9QCit8mH/PRVUeSYS8BpJITcfTko5Tp9LfS5FQ\nZeNQ\r\n=Mrjs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-function-name": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.10.1_1590617283242_0.4323983251703474", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-function-name", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6a467880e0fc9638514ba369111811ddbe2644b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-OcDCq2y5+E0dVD5MagT5X+yTRbcvFjDI2ZVAottGH6tzqjx/LKpgkUepu3hp/u4tZBzxxpNGwLsAvGBvQ2mJzg==", "signatures": [{"sig": "MEQCIHRXLqFbfiTIlejWAk41XLG2z8Z48iGAzwFCAkepJCZtAiAmHwG1wNhgt2IuIRWkU7CN7NO9t1lbJtde2OQbLp+//Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zplCRA9TVsSAnZWagAAeMgP/RLyClBLkC3f9y18rOpE\n4N72VWZIRCL8YzbC6pqE5/gH5FFaB6JM6gNsGz8uOt6gnOgHsg3TOYkwyXAH\nUfjb/GEFgv5iKMhbHJbvz5mTCvLExh24ZCd5cbga8ksDBwpL42o5yfTV02qX\nWpIaV3yzFhp27y6BzH38kRwyYJdQyB1h89aTTvK+PwtuS8jlpGKLqWybxpjw\n0jRK5jZW1Znx35ncSS5YmrsGM/aYJ0isOBHhutB6Gb29eXxRdYNhiZ86VV6d\n3SxJ1R2YRvcTYsMbUXWlafWJ+7KfiiXmbAArZO43bfNxoANU0wcDFwgDAIwV\n8EXt6f74B5xM0/uAnDIgl8xq76qrzajye5TF9NQaw9N0/fLQNdt9OEQu3A3L\noRON/f7XvGpAQfZ+ygzYLLj35q50UlEv4et461mjQQ02C3i1d8nuMRkDGisV\nFGTgTa1Z8fGYRpwlIFeDzitlr1GSKDfdUlQg0aPn1xC0zYoxrajqqy7RoT9R\ngernVJGEmvkfnQChZBnkKwhjIoqKl7esIw++k8zXM4wt/X1ZWohZSfzucoj8\nbsPQY/x1AWVM2rgTaHNHzkZOB90MPstJEKvgSo/PGQp0N2Jw7/rlwlUdGx8V\n9RSd+qjLsErNCGyZP56Ciq0P2qHj8omDmQYynjeIWuumNo7tA9uf7OFynfX2\nQK34\r\n=vwKu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.10.4_1593522788685_0.15764695346456237", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-function-name", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "2ec76258c70fe08c6d7da154003a480620eba667", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-JF3UgJUILoFrFMEnOJLJkRHSk6LUSXLmEFsA23aR2O5CSLUxbeUX1IZ1YQ7Sn0aXb601Ncwjx73a+FVqgcljVw==", "signatures": [{"sig": "MEQCIAvKIXMvui78GY1H36PXiFy5Bk8QGUSBzvu8AW5ZiYRnAiBGrtOvtYLOrI3AnT497mv/z+Sd71nLJT2hMsriqQA2fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/FCRA9TVsSAnZWagAAbjUP/14eOdERCFzp572aa8sq\nonmh4qFEFJZK5pjpQEhF6G7m0np31jemAUXJUZBCpF2YQoWEyBZSNpl//4ze\nLyTeC2g8FSFc/YOwxcs1hXn/FbSopfit6FXkN6k+kiKQsjJ+gNnlT0fZhJdo\nUwTxLwFhSzQcLqPWSeNa+zkdlV1uMM/0dQrC/U02CaMoLjGBIqWEbyKx8bte\nZScLpgL4lgKkvMHYEhmDcPFUe45kHtbzZTjIu3rHOJ3Zx8q3SeegNlm6yhUj\nLkRKMRw4aVywKCGWsIS0vw1hU6SHtXOPKD+CMB8DCF4nzHlaAfvIgK22pelY\ntixuMACO4bKR8Hp8PknpkN2DSmb4l0LPlZYaObaswLrm1Pd7nkeyj3Y7P6zS\nw8LonyKk/JJX9OG1j2QPqYpTeBooG4JUBiyiPWZjpfeI2zWtc4w07cn4H/X0\nMISAU5MwmKuhDkkmkMMkjWTMP3b2CKcSEabVKwnckctNAaa3ifVmoYVgukZs\n+rgeuSOrdpJ8FskQFjx2hwqEhczHoFqUhpZNat/BcgQxZ6uMNhjcQBKoHxm+\nGROgmyeTr7cklygyFuL8KAVXoBymnJKP2vtJJ/6EcAMz93dXJ6B5lI7sUzrc\netDaKMYPOhnOgioYBeJYoWi1xaGd3CkysR0Gcc2ZgRUns8i1yBc8Rff7pkHG\nlk/p\r\n=SXpo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-function-name": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.12.1_1602801605169_0.4954473908410051", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-function-name", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "bb024452f9aaed861d374c8e7a24252ce3a50051", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-6K7gZycG0cmIwwF7uMK/ZqeCikCGVBdyP2J5SKNCXO5EOHcqi+z7Jwf8AmyDNcBgxET8DrEtCt/mPKPyAzXyqQ==", "signatures": [{"sig": "MEUCIHkXePMBj6+bgVSbS9h4n7sW97aO+OV1lHaa4ZqvIu5pAiEAvtT1ln757iHqbdsXfCM8QfS0RcQYI8VUGY1Yp7C15SM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhRCRA9TVsSAnZWagAA1oQQAKOJmGuKNPZBNJUO3Bhh\nIM6htfodXiNq7k9+uBG/ujFu7ltfL74YNxysSCT83j3HNjtXz5B2UtwSRoui\nWypgl0jOUkXGUEtmpNvBx8CWQZ9sjgSN/CSBG5NsE+Kdd96d/NQ8y661WILp\ndZHuZeDavWo7dfQs9bltqftlyEEs9FTWlO1o7IUxICuGYvar7yVpa4RnL9LM\nDjW3WIp79HTmRUcB+ZNjGVdBaJuJI8GSsN0jK6Fb40os9SBF8QkzyB1U36I7\nvP/8x/LSyK/s0y5W/EzmC7nMG/AuJ9eTv4kdLnKKvtkuC7Rz+iiR2X5Ikr6O\neXcij0b1Te5y/lkeqkuntQVx75Y5AQ88zo5lQXDR53CmJ5bGIAJPDitzl2ht\nvEurDSll2eDSeY6i1N7mgCd3yEQ4cILkMGd5MXnDGwsnQRYWhB2e2rQdv62+\nAmgAoP1phVCd+PdEgwPOl/IaVH1sOmGo8KMZpgn8YBceykr7+xGNBDL+ZKRW\n8Oy9vwX9a5TXGXwKmxh7p5HtE9lb3FuZfIBPF6fnnMUSX+GHaBjEg91wVKFV\nhiMVx4SgGAEeNFtJCC1qwuyd2MzdGrez5YN6rAgvNau5Xtq56cNQA0sKkaVN\nmA/nPK/ZJu2KNUkW8ZRePeQ2GOMaHmtHbEeICaogACp7b4pbYNFm1Zi6tkpa\nWm8o\r\n=Icnz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-function-name": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.12.13_1612314704646_0.5132495280299538", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-function-name", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "e81c65ecb900746d7f31802f6bed1f52d915d6f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-vbO6kv0fIzZ1GpmGQuvbwwm+O4Cbm2NrPzwlup9+/3fdkuzo1YqOZcXw26+YUJB84Ja7j9yURWposEHLYwxUfQ==", "signatures": [{"sig": "MEYCIQD/6GHTtpnBwbHaIgxT1iURwJBVBdlvoU9N7ckc4jIoBgIhAMb51cvmKJRDEgo4nprQyuB4WPDva0kRN7O/ToTeNHTt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr8CRA9TVsSAnZWagAA3eYP/1Q330jbjNzhNdYv/561\n+hJD2DTdv7ggjXd/8Z87Aa5kP6i6tVtklxhfYDGdOjBjGot87rBc3Myik16g\ntjYgq2fjfI+t/1Sa/hbdXUXNvuBEFVT8Bez0RMvNICWTtxoWxsUuGw+OPYsb\niwiL1aIeYhHbEVigOxktQR386Nk8qgqOQD/HPIWqLJZcCRDTGwU0fVSIsgGd\nC+qymB5WeoaRWwOtaYEBfWXr74QYCnUdmVa18UKfCxyva3B78RN8UT8djE42\nyGmhn8rxhGOk+sqBlgJkiNr0yo9QIn6t3DzwMT/KzGzuQA7VSvjxvapYyvp5\n5qVm86sCkr9EISi2nKqZFd5fwNPL08OKnQ783nqu1RbmJTtzxsKV6OQxs3/2\n5HsYUvogA5fOlZgO/zdQifnih4M1FYZ99r3/QZtYeRYxkqlSRngrifJjRkqX\n4CKOBVrNXprWYqvtBGnEex7rSj71ABU42Bz7PsYZ/XQD8iS61PqOhoaEOpVG\n0K8XF/JTp7LMUZYqFs1LQTHvUD5yfsgdRNXFQhJ+AGC+EClCBDs7vNma2tUm\nTTKXRiwUg3R8KnPrIwgelQ90sSDZsbj97Cv8vhO85CtHxhJF6cBJJ63wCQyi\nYsiZh1BWVjhWJE6H9FevItDHHkSmG2X6CMUNqmgu/R4xtUmsrgkKZAi+6XH3\nmbRQ\r\n=6AWj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-function-name": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.14.5_1623280380864_0.10545806970187788", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-function-name", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "02e3699c284c6262236599f751065c5d5f1f400e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-lBzMle9jcOXtSOXUpc7tvvTpENu/NuekNJVova5lCCWCV9/U1ho2HH2y0p6mBg8fPm/syEAbfaaemYGOHCY3mg==", "signatures": [{"sig": "MEUCIQC1LMs/nPViqyt/1uZDAtBsYC/eRTguaqmnQE6bPT6YdgIgGW7IScOxZWo6X628tBtFHon+BG/zCTaGhHbSUd3vqVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3388}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-function-name": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.16.0_1635551272452_0.06055978980714505", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-function-name", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "6896ebb6a5538a75d6a4086a277752f655a7bd15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-Fuec/KPSpVLbGo6z1RPw4EE1X+z9gZk1uQmnYy7v4xr4TO9p41v1AoUuXEtyqAI7H+xNJYSICzRqZBhDEkd3kQ==", "signatures": [{"sig": "MEQCIC1pQH8ghd1HJSOFnwoEY4V6GLwhgWS5CFPk0FrvWx7QAiBHEZsBxCpFGNFWH0bTcEn5rT3QStyrGBd28FzShP28tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j9CRA9TVsSAnZWagAAyacP/R9t+oUTegMVRmWXFd0y\nbFbxg5B90hCSnlkGnAnQkK+yVaK0sezW1D4jQ/gwAtCVYMr7KTgEUUYaafGD\nlMyrpbUtgoQzW63XOpp/rsdN68AYWiQl9AlBP77kSTw7MFvlBGsbcz7CeDOZ\nT+rwL4a9o/mqk9Fd+OiQk+/xeBETMvLxZDQO5iDEWUgIKp/wVt/5n66R9XkP\nS5l+zNmOEqz5LCEwxS7C5qZ1YO2kgtLP6RXusjQhhvGsWMzRynmah7y2ubqz\n/VqQF0EVI5+nb5feaeQg+lR0fWo73Wx53r1KlI643ZIRKKXW/1bMqVu1mzXe\nrVndcFjkrjhiCDMSPcLSWItQB5re2h921/CXyGewAi95N/EBcpWk/z7AuFyb\nAoxhIg9YUqpG17Bg4MI1OeWlDvElb93Daw7t1Jj0d5alZQWi+gNSUQB2ALfe\nsRCmGTjFpq/+p+Xtl2hyRTH+FwryDpPn0gEAFWfphJaPT+lXxqWqaXMEvrAi\nfkbNRhc+NqVlNHVHaI82YrcjA+NKe5Bnycc+jXI+2kOdMw5GBCuSU26adyEq\nLwtj5GFhu38bFPjj5JFFDEoz+ON+B0Ya4HuxjWSmArsqzJq3JsQJL11NdlOF\ntMD1l2KdyyFmEigeopsfOnYUaIsqh7/SZktXvPlatczYC2yajBaQCJisv0fO\nLZ/h\r\n=bzVH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-function-name": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.16.5_1639434493243_0.6076713346376019", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-function-name", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "5ab34375c64d61d083d7d2f05c38d90b97ec65cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-SU/C68YVwTRxqWj5kgsbKINakGag0KTgq9f2iZEXdStoAbOzLHEBRYzImmA6yFo8YZhJVflvXmIHUO7GWHmxxA==", "signatures": [{"sig": "MEUCIB0QIb2s/emjiwISv+zF5AJoU7WCNtmcO0D/OKZeBOXXAiEA+602ub4GyTo3ptR672iRaHrm1xw44XKziSXIwX/hZQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1cCRA9TVsSAnZWagAAX2UQAKFR9FqvNvHe7eb0fkOA\noLi1CElKPh/3e9VlpzQ+T31/0F1p7skCHPw0XveKCkKGkn5qrZSevWg67YxG\nXqS9U0oy5kI1ltD/SpTr9Av7/BiiRNr12aY7R/UCSxHauD5zvjkCLccqJ80O\nCdjBwVKDP2oCoyW1E4ddWew8yqjS6l3tymjvMK8+Rvr2kppGpjfjhR5ZjCPd\n4EhvAdREV+RTSgA80Mi5n9tCVTeAVUPhlPOYOvzBwp9P0Bg+MdGKmSajNsVz\nOGOUKM1dVonInOLezmONLWC6C9ewYrJTRYn6lW0jt/1H5Ja3ULgcgKMSW+zN\nVzYmWI+3sKAPo6bKRSVxhgqvndb90BNProBva4D3kBqP1mh56mZ3D43WLfMb\n5uIuor2urTud2nRhOKLZBd6vb5tWPd+PqNOFMws+aGUviry/5Q9+57rZbP+8\nTbyvGCY92xr3g8id5jtlaYb3LOjjHbNkDuefvtEVkkpz+tT5WfjrG9+5UN2d\nT++4Q3/GhQrSczucgYVVFwerqSvrSZafaHat0e7PQelcVxYVdm2FTbQV1Rts\nUd1tL+DBSII3YntZvABpQ5Iwo0EgYslNRzbkHTXGtQQn/PzQATarw1J6Mw35\nHfX+oJwGzcTBwY9RT6z4TUopNutgJKcQSREuO//Vs34yf16Mz2v4nD4g9nYu\nRxrA\r\n=F+rR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-function-name": "^7.16.7", "@babel/helper-compilation-targets": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.16.7_1640910172405_0.3024611528203114", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-function-name", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "6a7e4ae2893d336fd1b8f64c9f92276391d0f1b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-kJha/Gbs5RjzIu0CxZwf5e3aTTSlhZnHMT8zPWnJMjNpLOUgqevg+PN5oMH68nMCXnfiMo4Bhgxqj59KHTlAnA==", "signatures": [{"sig": "MEUCIQDZKVmW0vD2uS05MA7UD1ZI6iRew3Ttg8TUp03cAT39KQIgPEhD3a54Z8iN2oLRHTSvu8CJcppthNwc03lWZ5GsQ2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEWw//ZsjhAtXeX2PjDN0TEMgUMYP25Nju3K9pcvgF2zWlLu2DHym9\r\nn+aqvlKauXepLWoZs0UBt6fc5fEi4wL08974upRoWO+SXC6e3G9Wc6GtABqD\r\n0WIS/gm+jXAzF724I+vS7BBMEoWM6jtA/Sa0hlgen17eHWmEZ8JQ4aHadtZP\r\n4db6tr+Bd18jYSQiB4aOFk/b4i4mSMUwqavHro2SVM9gcOMy987HcZuNVDis\r\n4j4FIYqT9dSYC2e/P9AJAraH2Ub3RtNi5n1WrQuiCCDpY4stuNDi6onPuXuc\r\nfpHoQJTOAonZ5MSuefyAo8QU4NqN2NJDruEPdYRWCgQLdESYB42p7QIO8uLq\r\ncbtkYtMOF92kLFwXLauOoq16XB2ljIpfQnJIT586FVipH2P95gtvkkUytGkA\r\njy7nvtZOZyF9OEShck0hCkmBs+kmGoNWywn2aM5zAESIlXb8ISff7a6surOL\r\n6JQIjNawnadpWkEi2pppeGcdvtqVpYegflPA1jMztSGsUkDDwDuG8yYDOItm\r\n0xFFBaVtGQFWodWJmCjIM+RNHrSlG6Uf7vy9G5y4zuWsLGmg6uEIffinbrUI\r\n3yk6CPFFu2VGsRX09N2GRZfkzNntaUL2xi5qm52vmC2GEC2xX7gf4vslQcZW\r\nsbEJY/KzHqn5rkRGtyukh25Hm3jl+vy9nnA=\r\n=vYFO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-function-name": "^7.18.6", "@babel/helper-compilation-targets": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.18.6_1656359429632_0.763447896415504", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-function-name", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "cc354f8234e62968946c61a46d6365440fc764e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==", "signatures": [{"sig": "MEQCIHzBe9FLeaLaAeTRugjHUBboFHz47enn6/vLnL+xAq7zAiBTaqODWV5QfT8Y/7VH/etfqn8mTO4nJhJ4ykRSXDIubQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/PA/+Jo+TUu4RVUV2Rzjyv/k4/PS92CjwHhAownsG6W3C0ZpJyS0B\r\nqX3SxiLrlVymajHRCmm3hBfQY/aiUttVwS0qZzyemAwW3RKZDtRj96fdg5zX\r\ntrwzG5N86yB/5+U2VA1enbNY/74V5TvKVomsxWt+z8nBzLqP65ewfUBb+o5o\r\nieyehDwEkjjUsCp5cBROTAe/qu3TfrZYGn3eltE8DRw72viTO1JxlyaLjFf2\r\ngV+ksCKLGOdLrL1cR9tP97ncHVzwQlXxWdRfJ4MNRVEYfLxC1QyQwVxOBPc+\r\n0792Fz6KAHWYUlZ+8K6Ytl27T7Y8qvhGm2OClqhiAhfKZTQvpDGaCeCpXTuw\r\nba33auueiG4S5wJ4Ge0tTdMgqBrJt3FwIlgKtgnp/Fa0GsNI0ngzKldlBszE\r\n7EeeKc9WlWBCIDMSoTKTqxP+bOUvqymn1DpBpOkBJou2Zw3SVJsOdacL3CB/\r\nweDBzvE1rWRqvMCF8hlhiIoT3uKMj3lQSIkcndZgF0JZCPLq3TNHqSbJq/2S\r\n8+RdRpFWEgT2t6giarC9nMr53Q49R7azeamqSvox6eVRBpk0I9DB8mke2Hrk\r\njAejQshHyHq/fV8KA7pgXjS85zKfyE15WY27hPo71mZCX83mp78HxaJAGtHl\r\nQv2IjHNEmVUzyIYdgbxfDhdeYzZfCxVJ0zI=\r\n=RDFC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-function-name": "^7.18.9", "@babel/helper-compilation-targets": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.18.9_1658135857126_0.7851258757956936", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-function-name", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "0ffff12bcea04fb0f06b69b9688a2c45eb757b74", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-wV2WYS3jEM/HYSdP8MGjBMVUUKpkktSfgBxZqxr6KSI/mvQGkD0POl69AKkGCniB01nnN4nOGjIwy9YDZXMDQw==", "signatures": [{"sig": "MEUCIQC7+MSQcW28UOru5Np858T5zdVDB2ZeGWh77P3fdDUkeAIgZqcU28dj9McFPr9JLx5QOPsnTMzDvaveXgI7W0bgRaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrCxAAkswbfBKds7mXtezl0P7W0RxxUkmSSIqC0M+ZoN9ouWW8T7WP\r\nlwyNmjNmC+NSaRrm3ySU/YgvkbharYVFdUH7ksLEStbTYYdGeoPcBxcRoobq\r\nSWBhrEztX9q2SabcKiYaR1+PCrS9De6lv7vgxUsv95vUr3ZZ37h9gkBLLPFL\r\nqGG14YS2Ot64wGzT2JUv8fwAY3JG0+d8zEnslSs9rxqayJHLIcbrVFyX20mx\r\nboXUIuBolIe4DVSgqpBjsR0zA5h5ruE4zU+PjAghAjpt0E65/YwzfmEUnWCe\r\nQag6dkeUVfjBnrJD02gD+v7TRi5Bw2UjKU7q3kmKe608Qjd0WAdqDZaE5f5U\r\nSkKg3WYcKvPNUxcK6+OMrJ3g7peShUjifWp4BW0uIZOnfdh7kWmGENDSiDXj\r\nNI37NzP1l7HRduVASMxreykzzphEwmJQ7eV8cKOLw8KZRraSZDk6Ok4goRp3\r\nxyodfXJ1/gdPZKYXrVGEH5RRvfm9+0uuDquySqen8extAdmK0+IVgOrE3j3y\r\n48hwVIP++WRWTcGlRjl7SScnUbqOrPAnxMg52yV341ixzSUBu++NE+yUAHtx\r\nOH2AlGzq8rjHbH/kqSS4RX8tmZU0O6QHq1P/6PWXz/bf+WkZkXoT7dJBAN5S\r\nCEktasbU44pPrPfpQaBOZDUJJBr7C+PgZk8=\r\n=xVQJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-function-name": "^7.21.4-esm", "@babel/helper-compilation-targets": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.21.4-esm_1680617392579_0.7233847733849361", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-function-name", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "f3ca38d48ebd9d208daeea2765abac7879937415", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-vUpoC5ZZnUMeF5nXgqJUfNNP0NTGgRQnKgrRAFkV/vMmrXyC4l3fbq214x4XrlFD30jUAcFVpd08t7qwVV6LvA==", "signatures": [{"sig": "MEUCIC/q+KN8Hv8jtms2N2OZ/m0vxStZ5zMv7Brgc2Lif4AvAiEArZJh33xn31KCrtDyVZkhKA9rFI9ZIV4psinbwjqlVAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOpA/+LHeNR3tLZTxKkOGbu+giekrOtCfRFX3wjAsBcVJUUNiW/cy1\r\ns9E7CIVYfS4LRb1N53Pl2aH13RDh8xgKRAQbIHpwsqGizjm3H0OPQjGI84vv\r\numsaaWXloRbyyGg1Ga0D60NdybqmZW08ulij+V/zud7xEbsETZ7OdQZ6pYIl\r\nmdG0NSs8V32Xp6p9olWZkdk8cozfNqRHtNos1APc7w25SXSxdXpNS8ZeNF5B\r\nNiDuHsCKEgz6hH+uOZFYCQzJlIqdl4EunQZVMqvSFpStD2d7LxZ57T+kLTMm\r\nTEGHeo8X8fVlyCKjq/iubRVlhofUanmjAdrv9FXSj9LYcmYDiVJZDq7vwV8Y\r\nySwmsEeyreSMRfrinASumitQFZ+LkjSCLcM8WU+LnA/BEVQf/yLtGm+nfDlf\r\nQjekQfYosMT9MOB0+QBWrXidSzRrIbWkj1aSlFk2ywAxVFVUTNK02DB9XbHN\r\nVl9Hixc4nKcNz5j7p7E/z3vFb7Itjk3gKGpgcFUFVW58K6pCUGJGR5DEcIAU\r\nnxom/eCI9gV503xhhchN0NGtoZz77RAa5+cBQ1AUq9pbVe0Hv1hSWUOiklCg\r\ngxlE2YNgMBwK/0x60x6YVmZNV7ZEmE2Tis0hwVDsJ8LbEy4bix7ca3MOJtoZ\r\noUGf+oj9fDvkl0JSzo0TLt3jklCrjgqmLKo=\r\n=N/Fn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-function-name": "^7.21.4-esm.1", "@babel/helper-compilation-targets": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.21.4-esm.1_1680618110092_0.9003136931252047", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-function-name", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "0d4a9e291740f18ba7d89b6a63ae14814ab96af9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-6TBRF+kJg+rBVdZ2oZ1AkpjRpKaXvE4yGtunAkGjVE2eRTUvImM6M7L6M1nqCzoKg2lH1jEVlxpJSsrebPhzSQ==", "signatures": [{"sig": "MEYCIQDjy7uOqIrb+xr8BZWRXAxNNDt52a4vZGd6kMHskDEf4wIhAOGQKJkd157utEt6HBOFHpuRzcT6a2cH67QCfdxtqyir", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfzA/9Gho/yyMSoenBDG5vtlr9XEIUftxpoTiqFIJOcZcxCmtHdkUk\r\ncE0XTm0PaRtUkH02WoNC+2dM49wcXUOZ/I7HI65LdSptj3evu4l2CB48JgHJ\r\nHYiv+hIupAfZGzr/u1JxWOpqGsTtXx1Fa682DVrfS7Bw1aA6PekcqSuTUKUk\r\n4regrwANISdpQzUcHzClrjD7jS8cp1Clus6hK5NDq/MBoEHvKTJQr7DEmfaQ\r\nsMepa4TF8Q+z2xirFmO6mrAVqfgkhEJbyfy6sTc96VTV9lbi0T3dEsa63/yK\r\nItz5Ek3K/zk9zrsr8mfCeYi6xTXizZ87vpE3K6dTTe6QgT2DRll6ETMbTtyU\r\nafsnA+3mIFCP07FtwvnUPyVOMdxfYXG3l5MNEQMvjzcoeT7G52hHm8RXulhu\r\nAbZ8AwCWYcvezHQV4G9x3I+BwhdNDHWEypR1F1dYs6KX/XmYoV6QaIMdrcPl\r\nACrtjZdJDRo2rGAwzLEKi4Ccm72npJEAjrmna+s/+FuwfgOHQ2XzEAVHqqia\r\nIEyfP8R43UHzKoXE5N624sCWKdFis2WKy3f65O4DvAU1I60iM9jQSNMpL8N3\r\nDysemiXqO+yuj8V9ONNUckQ8Xa/2lF8JsjzIUuVKycb55yRu8+f7oCv05EdA\r\n//BwnOFd7WxeLFQmIAKqzkdjwOGXi03puS8=\r\n=6Tbg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-function-name": "7.21.4-esm.2", "@babel/helper-compilation-targets": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.21.4-esm.2_1680619191438_0.01455156732779228", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-function-name", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "d11cb92dad3c54b5058df56ecf1ac57a54e511b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-FzESCABqgcuHzDrmwxdyJZaQxwh9l0BS4krq/vkPw+gFFVerQerMfCMYV6yyqD9ExF19FUjkr3cVSGaUJLah5A==", "signatures": [{"sig": "MEUCIQCSo0Uu2pivYLwsFji8omhVfa5DAl/4EodiDIxMccQ6DgIgb0lMrntSP0rDkLaJvJDQZq/zhvqx2fPrI1sa8b8PXz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUyA//aXYVFQ0/MTm8KaLeBMDF9N72aln7A+XmUF8+CPMkvRSMx+Eq\r\nKoMJwvsJ3PnWZY1hx/slimpCA4FyqlR9G7jfNxM+15igAHm8C5CMPhZfoYXY\r\n7GaDPuKnRP/LFVqSoW8igNZO/LL+NMty83wiVKwx4fHIh95Lkwu9iT3tGyR6\r\nnueNiOW0ByNsvJwrJKiqSf1sv340kl7Xn0h089y6Zg+9YlwIdMIgDVN+m7PF\r\nmddocifSFMBGwW9G4EpQDbRVWwy7fXS/JuNccJ+NhzZk/Sp7nSWEnyLKcfxK\r\nWkiNN9JoSKQ8B+KmCDxe4j29rxeJrirNgNJ+1mbksWSq2DDV1eV1/IGO+DJu\r\nFiLAwbjf5d5qNmhOiMjb/9KBB7H2tQlF/8pu9s5m6fmU/aC5NoR8a4OB/hVH\r\nlYhi0Wgk2fNllg3pXLuX6VkvCIimmOP46SijK1nxWwRuQ0oRchSK66/VUmZX\r\nMYkE1CPoKMzBpYCql9yGDNET1VJfda5VQoFzm5mfvAN16yVDYNHwqWSOJ8vL\r\nx9Hcf8lkNrGb4mLQrwo+FdPj39vB5lWm23h0wOQF0lzDLK6EhMKPPSQ6jMwT\r\nW7oE7BPYC8lulEXthdu5YV+0OHEMm8X/X5kw5SPDakwrOIDXIZcSqH9VjFtr\r\nuWtPZCnz5MvQAQRGJWHWxXlKT2ZDVs3wYew=\r\n=r9sX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-function-name": "7.21.4-esm.3", "@babel/helper-compilation-targets": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.21.4-esm.3_1680620197141_0.6362703919989883", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-function-name", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "a540c1a5bf9ebbb6134813197292ee598e56208d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-3G0yIKQd/OMPxbs2KIM6pbl3cCgCnRCrYaYgGIpFUH5IenE1DpQrHHc1zyWuj+Pr4w2FVH6StjCJHW/fbYJNuA==", "signatures": [{"sig": "MEUCICjDFsg3GO1Fu4CcW9mseShC4BN2mPzpPM1Oxj+5lKAQAiEAr6yhlgPuqYqyh5iSv0vGukBEmtvCO6Q6BhTukrENFB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyuQ//QZursgjRNfBwzd7W663NJ9WAlw3d37EiQgCCO7ftEMzxnG7d\r\nrkbIsfMVYNfwNNBgIHKnpZSRCbrNQ37+7xQ3CD5Q4ekVZeJe4gBXRwVeYNpe\r\nB8AbK/jtxEaIqjcNd1vEYeexKUzE9d4h3sD7684Vw5FWiyV2MwH/SAa1Jq8V\r\nniEmqLH43jXbnKQdqHVB5T84kfMXhfb9es6tGKPfIWBllgTget/L4lEnobq1\r\nAuQ4PHTdYoqiBX20WKwljDrJRsLa9JYdkyKVjRYuGkCiaoohFTkDJ6qg9Gz6\r\n3pByYYO5AsFvOjpTaSczI/YOmUBPiOM0t2jsDT0YPNEfEtSEfl0utkEMYAjm\r\nLa5ogznHB4hes7hB9FL/E6V9qKnoSl8j4qG3OqSEYG4GIxVSobtjNHF6fr2R\r\nw0boUOGxoU9wgMwso6H/N9pn0rwg7rdL+bUK2CvXZhI7heyD/qw0qeZzXGn4\r\nR57+sPirf3v8PMWZpkFVrpMiSACdWcomwgvPL5QfJM6oLbBnwn5DZ/bYRQcL\r\njy2Si2jpX5abIt8cI2utc1qoZKSIrj9g5jmsMI4Bn/OdPij9dsXBAqezx8rW\r\nqcwbIU+dUyF72F/5fwN7QQ1eLVzEtVBV8yy3P54eFSeSnBD2i48L31Rq95+j\r\nIJb8SMjHrFL8V+LfPeEnNy3roW4A9+d7jb8=\r\n=8SGK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-function-name": "7.21.4-esm.4", "@babel/helper-compilation-targets": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.21.4-esm.4_1680621225925_0.2733652005321283", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-function-name", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "935189af68b01898e0d6d99658db6b164205c143", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-UIzQNMS0p0HHiQm3oelztj+ECwFnj+ZRV4KnguvlsD2of1whUeM6o7wGNj6oLwcDoAXQ8gEqfgC24D+VdIcevg==", "signatures": [{"sig": "MEUCIBfBvmEKM6M04bsYiOdvr0drb8P0MfZjCD0ZRDV5dKPjAiEA+ru6SnjyofiZkH2GqNEM80wh/5G4hLaGNx54RB+WaII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6187}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.22.5_1686248500952_0.7598328028471557", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "78d2a86186b610fea8e4cf7b96d814c117986d11", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-eZ286QkWzLxRDQMTVb4F7QNDyw6duMQd3IrkHAS8RrIQfBQpOWS+jPA6+tV+LJzAvOrC/SFORkplLpW/wsMwZA==", "signatures": [{"sig": "MEUCIBdYZk8ORWFtlxxnb7AnSKJCahIyOPXX7fo1cbISb7R1AiEAhkkGe6e0XAdbeVD2/n32j9xHJD56s9FSCEIhAmdUG8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5957}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-function-name": "^8.0.0-alpha.0", "@babel/helper-compilation-targets": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.0_1689861626571_0.09775736365484322", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "72d1ffc2220c4dc69044d4368a2a5ffefaf67078", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-rLKTDfbM0wk553j2OvtZmKIq/0l21MFisSQCRebQsPP6f6zSwtT/ARxb7QYGffH+T00kMOcJz9MYnbF+JJPMKA==", "signatures": [{"sig": "MEYCIQDXxdwTrqUuLx1XLGZ6VVI5g716y6nZwMohq+gpoY0/RwIhALmrTtgEXvUo2Vx4mVoQ4JIZj14jIoPeW25dwHqkJ6MJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5957}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-function-name": "^8.0.0-alpha.1", "@babel/helper-compilation-targets": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.1_1690221179012_0.02636103135573875", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "7364cda44cd8247392391c98d371c2f7d599d676", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-IvBdRY4UwlntIzC0XKJio+NtfTNai7emQ+1/f10gh31NLFrOpRhorOuUUKriYJtE+Sqd6v+MfVm92tgSSZObwA==", "signatures": [{"sig": "MEUCIQCzWxXqlJyYrvA5uAgYqInSJ2pr2AdFJpwaNhFL60I/vgIgbSfD0eIEBNW4OoUTxQmMnoHeFwr7+c289JIGE8eo8Ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5957}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-function-name": "^8.0.0-alpha.2", "@babel/helper-compilation-targets": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.2_1691594121743_0.026988353703399692", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "1c302f679df50832a51f7b19e618c4bca57b363f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-6+x4HZ+l2zdBS/jisZBx9DZuoV38e/4UNuo1avzAAEENvtdrghx9SBYOJo7C6VtpWfEgnMFec8PwQiVOuKmWuw==", "signatures": [{"sig": "MEYCIQCcFl2uO8Zxo7pLAqY7Z+FG3Dh8KC58G5PqR2RIDOrt7AIhALGSeHfRkBWWQw9qP0j0XL5kjP2bN+h/EtMBRejPotUd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5957}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-function-name": "^8.0.0-alpha.3", "@babel/helper-compilation-targets": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.3_1695740256580_0.5597790262858096", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "daa8a569233232f3af854ad8e2c9cbdd120c5a3b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-zwwKfOx56NhiSVoBAWmrd/KoOnnBQ++O32pnKGn1XaJ2h/u1EtninIpK4CWHdmMGAeTaYUKwrQpodF7HOqtsVw==", "signatures": [{"sig": "MEYCIQD3dRvZzNG2adSJRXQY/1gHul8P1GoxyAJdwu3Yu67N1wIhAIESRtP6pB0ZdpwnGkhXvnIYAZrPkmfHU2jpzDhcsMKj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5957}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-function-name": "^8.0.0-alpha.4", "@babel/helper-compilation-targets": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.4_1697076409456_0.8881070842570509", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-function-name", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "8f424fcd862bf84cb9a1a6b42bc2f47ed630f8dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-I1QXp1LxIvt8yLaib49dRW5Okt7Q4oaxao6tFVKS/anCdEOMtYwWVKoiOA1p34GOWIZjUK0E+zCp7+l1pfQyiw==", "signatures": [{"sig": "MEQCICoc/OWyZmMpEOMl4SpdFsOcESBtea6sRTg+u4nJJc0AAiAvmS3eBomBOfkRkeH2FL+4R39Ctkp8uo4MBGEM5i9AJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6267}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-function-name": "^7.23.0", "@babel/helper-compilation-targets": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.23.3_1699513430485_0.3500729518625376", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "0df7691faf77f621de9d962a6fb16a76aeff4d17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-JIWYgl4LkDjFL/OgZ38/T9mzrJjPCu5w/LER+XhEiwdrFaenHM67Gq1x85cLJyPri/AVHIt+YCDRbWgkozSwsg==", "signatures": [{"sig": "MEQCIGYc+5uxXFymr/+VsKykNiviAyJhoVIAvZKidjtXEoTxAiB/35quKJ2CsU2U8WMV+zBjFxy8hNj5Dz3jX6RErmVWHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6070}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-function-name": "^8.0.0-alpha.5", "@babel/helper-compilation-targets": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.5_1702307984295_0.8673200774955303", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "cf6d0acfecbb55da136bc30eca860b55c7e0f9af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-7eWkcy5rwS7+tGdA+eb1ACz9qvcbvY/kCf2PtU7GyoWA5kB7/Ycp7GKdiVV4YXhAHB7R9m2udEAYCEUDdwKMDw==", "signatures": [{"sig": "MEQCIHI03fFlh0nAzukZnTnjTP7WJ6CmpXV68Jdxa8sdMc6eAiA1tqgbhwnf+xosvm4AOaWRu0ijgcEkLS6S/s6pwiFPDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6070}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-function-name": "^8.0.0-alpha.6", "@babel/helper-compilation-targets": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.6_1706285681852_0.24997545277753663", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "a18031ec8121fafeec4fa66dd6b57006ed250cd9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-wRZrT10+hjwFCEitBmzmS3FM9X7JrNA6siTxCWW3n6tB6k907fCnngqbu56s3VepB5jLc6FoW0w6HaQZ/sMFPQ==", "signatures": [{"sig": "MEUCIDD8umXljdBMfo7S/7haOXIBIWxBk9oLJwvGU53E6tMJAiEAh8QSbO4gJC8jy4BNhFxwtEDhl6YEZp1zIQNcf8UTBwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6070}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-function-name": "^8.0.0-alpha.7", "@babel/helper-compilation-targets": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.7_1709129144381_0.6425959672497354", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-function-name", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "8cba6f7730626cc4dfe4ca2fa516215a0592b361", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-BXmDZpPlh7jwicKArQASrj8n22/w6iymRnvHYYd2zO30DbE277JO20/7yXJT3QxDPtiQiOxQBbZH4TpivNXIxA==", "signatures": [{"sig": "MEUCIDZkivjkOa+g+FuTkJJ168qGzh0TFGw6/MpvAsrQB2HQAiEA7SVgSnMMb9Y2UkQSneGkCwj5hN7b9VrIqkMIzbe9KPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6197}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-function-name": "^7.23.0", "@babel/helper-compilation-targets": "^7.23.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.24.1_1710841716883_0.11729451192306017", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "4b695ed5c8c19b8b745729139d5f2e6f843f9687", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-lPYcFXKIezPnkAr6jaLuQotaLdGcoLw//qPn2XZNnj1oAcFaxRkRjRyHKKxcmRProqomAuoAcUKN1Un6BCvMcw==", "signatures": [{"sig": "MEUCIEIRsKJBHpM+KkwnD3oZnsKvEwYLOysxCN2Zk/CPqFw2AiEA7dTQGJ9hKmhbo/GgszeQdNCUkXYvApJgZDqVpK1HbiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5984}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-function-name": "^8.0.0-alpha.8", "@babel/helper-compilation-targets": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.8_1712236819541_0.5824166645489461", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-function-name", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "60d1de3f6fd816a3e3bf9538578a64527e1b9c97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-sOajCu6V0P1KPljWHKiDq6ymgqB+vfo3isUS4McqW1DZtvSVU2v/wuMhmRmkg3sFoq6GMaUUf8W4WtoSLkOV/Q==", "signatures": [{"sig": "MEYCIQDFzjGMBsa3uWfEEPhIW+J61/pSPB7LTplMl1qtXEgIoAIhAIezoHl7NC4rOn83KyESYYEqX58XVmsZL77oRQH7NLL1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72283}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-function-name": "^7.24.6", "@babel/helper-compilation-targets": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.24.6_1716553510254_0.6856241817469901", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "ddee5aa739a5919e6d47eaaaf4cf51baddfff3d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-D/QIxL1ijWauVQcHR/51zLHTpLN6vTGjHrUbrbzFj9nna94VnWb1cqXaS2sXApGHAJegSy4hVfiPCJvLWsTg4g==", "signatures": [{"sig": "MEUCIHf/UWlnEfVanSZEXfJQS4ghBfWK0TXhB4GYk048gcDiAiEAiXXiUH0pd+UiZ5iHnRvCMpfAHYt8lhzaiUVKLXW+07Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72380}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-function-name": "^8.0.0-alpha.9", "@babel/helper-compilation-targets": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.9_1717423493324_0.7479263985779638", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "eb0e55fa69650ac1c0ecc61b55f22f95e8540abb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-P1bWTuY9E3gbwqYtBkcSchoQ+Y988lQvbosBglAVU/A54lZiK7/3MrcYxSBlPr49A2rw102cG0O1uoW/NOZXAw==", "signatures": [{"sig": "MEUCIBKqMrnfx3i6zuuLvr9Vz+7OK2DjOd17OWdsKB/AJMRGAiEAwXPtFApgKhdGtJ4hL6+evDKtOtssCiCGLBH/f7w92ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72389}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-function-name": "^8.0.0-alpha.10", "@babel/helper-compilation-targets": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.10_1717500030025_0.8426258889040985", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-function-name", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "6d8601fbffe665c894440ab4470bc721dd9131d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-U9FcnA821YoILngSmYkW6FjyQe2TyZD5pHt4EVIhmcTkrJw/3KqcrRSxuOo5tFZJi7TE19iDyI1u+weTI7bn2w==", "signatures": [{"sig": "MEUCIEjcU00ZyUnaqicrZ8mlwWwaunlFJRi7sMoI1+kYLBVpAiEAsS+lPKGHqmfgvxIog8fHEBL+hZG5ydzECwzUvgO8lQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72279}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-function-name": "^7.24.7", "@babel/helper-compilation-targets": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.24.7_1717593344483_0.47221589830877253", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "fa91ce68979b6ac05a132f801fb10c119723a765", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-1dHzojM/C8qBXHd0qpZ+cCEFfo+dXgSR3QLcpODOQys2pClI0tb2CcRAXS3ns0AWlonctveBO52/3Qp8BVa0SA==", "signatures": [{"sig": "MEUCIQDd42IDcQ7FBdKqUZRp1biI18IvZzr60hgBR8KNIeiYogIgTWtPwQe5Td5mP50mmev32WSoauAK2RFR2LPwEHNe3BI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72278}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-function-name": "^8.0.0-alpha.11", "@babel/helper-compilation-targets": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.11_1717751754763_0.1472822479590592", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-transform-function-name", "version": "7.25.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "d17890029ceefb45189ea203b404a496263a8412", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-CQmfSnK14eYu82fu6GlCwRciHB7mp7oLN+DeyGDDwUr9cMwuSVviJKPXw/YcRYZdB1TdlLJWHHwXwnwD1WnCmQ==", "signatures": [{"sig": "MEYCIQD25B4EDGWN4zjpmOkhN4tjBrJP2zY6rJuWin0V8DfRggIhAJvtIpYKAPGb7YO+Q0rtlZAHaZLIoJxn2bokCVLCGIf2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69706}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-compilation-targets": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.25.0_1722013170559_0.8650786790980216", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "92554ef1cdefb6528bd9a22eb520d2ec28ad4c44", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-jVnGCxSPljrrXHRYoUD1EgYrLC1YcNbNQ7vK8EgRwZueUYdglFCpmbB9zEPwoXxiD+/5asgiqUNTuYbl12a8Tg==", "signatures": [{"sig": "MEUCIQDlio2wxRDdSAYrYh+mZ81RBhnrsj2Xsv/rXGckFHCs0gIgC3oNP4pPxMJDsTLnnE3nFLgSyEZQczgPwLNe1dAkRXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68951}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-compilation-targets": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.12_1722015231844_0.432086784048473", "host": "s3://npm-registry-packages"}}, "7.25.1": {"name": "@babel/plugin-transform-function-name", "version": "7.25.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.25.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "b85e773097526c1a4fc4ba27322748643f26fc37", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.1.tgz", "fileCount": 7, "integrity": "sha512-TVVJVdW9RKMNgJJlLtHsKDTydjZAbwIsn6ySBPQaEAUU5+gVvlJt/9nRmqVbsV/IBanRjzWoaAQKLoamWVOUuA==", "signatures": [{"sig": "MEUCIGjT6H0MxToW72Fe52Kxa+1Hz3V88FGtjwy9h0ZAbza0AiEAnrrV7mRIQbo+i0kjEajhhTyrOfIkA4jtTLYkZ0nvALQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69705}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.1", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-compilation-targets": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.25.1_1722195402897_0.7755176148009648", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-function-name", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "7e394ccea3693902a8b50ded8b6ae1fa7b8519fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-5MCTNcjCMxQ63Tdu9rxyN6cAWurqfrDZ76qvVPrGYdBxIj+EawuuxTu/+dgJlhK5eRz3v1gLwp6XwS8XaX2NiQ==", "signatures": [{"sig": "MEUCIEXtM7qgfPr2Em66XIRsTTkRvqQGXaZB5drzlbD8zHZUAiEA+tjiSlpFXlzWdxrzURohW4VOJiFypY7dIWFhH5/kPAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77703}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-compilation-targets": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.25.7_1727882119971_0.1618966510621216", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-function-name", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "939d956e68a606661005bfd550c4fc2ef95f7b97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-8lP+Yxjv14Vc5MuWBpJsoUCd3hD6V9DgBon2FVYL4jJgbnVQ9fTgYmonchzZJOVNgzEgbxp4OwAf6xz6M/14XA==", "signatures": [{"sig": "MEUCIFTPWRVe8ZqZWfR3ujB5r0oruFuZ9EFyakDSXkQT5IpTAiEAuCuEEeG6ueyc1iMfZ3r4KzDGwlvQEVAjqBR7pD5HrSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7115}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-compilation-targets": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.25.9_1729610495724_0.8563807762243938", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "a4f205d395415e2c01f62cf0547d25d2cb7b2a59", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-iWdbpA8alxZi6qtM865luc8ovzvScsrCYHoa7xQe3JomrHawnJAziJkGEFcftI16vLPi4WLD0QR0JL2z16bhdA==", "signatures": [{"sig": "MEUCIQCVm8rVbAjU0tyX51gi9epY/wH6xK/jZWSo3n1oSqStxwIgaG0t6Sb26CiRkXmCjcDUyLj2yyqOE1L4MW/+avp4QEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6370}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-compilation-targets": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.13_1729864476295_0.8964673189300241", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "59c40441af36fcdeb1d1adf1658c21843d4b0232", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-4xuc2Qnfa2/v8i6BQiii8UgGGU8iY14fREZzDv+5vpHf5IyaRNv7RVn7VFz9v+WE1ajLRc7tbDGaM+ddVRV53Q==", "signatures": [{"sig": "MEUCIDxHsD2lzm7976xxpmtX9r0cQQEgeS3MyuBQ9vSfEXPJAiEA9/91qQF8qBA6UnYyneByogFiOiYwQmr4azu7SkJyMQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6370}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-compilation-targets": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.14_1733504065955_0.2586982686778898", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "4fe15e8109962146dda18ed62e83bd46287e2b79", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-NcXcZMNSyuduH7rpSEO4tOn1czQ6qq8t+LMUa5PebgS9Mmrs9b/LMy9c71p97Mxx9n2sEiXyYTaWjjSiJIYphg==", "signatures": [{"sig": "MEUCIQDuR80PrcUsLHtAY1Qo84mFGBovpG6eq7NYYPZDyMoEJgIgAbTkJ6GE05WtevKCwzadFs6H3T3Nd9Pgitz4ZOwD5x8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6370}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-compilation-targets": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.15_1736529894042_0.8000943996406613", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "5da8bf8914d34281c1580577c61fffb82af19fb7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-8iPHs1YgzqFmC/qMzO459EwGG9czKzLLr5FX8B4ItuNcZIWQ1AELk7mEhYLpnqL046o2mJQDQA2/+vsV7f+KHw==", "signatures": [{"sig": "MEUCIFq1f74C4hntEpYEj95kmy+ggTUQIKRj8Sx/Pl6BR+sNAiEAlux0RCQvA+WI9s522+zGP9P4D0wgnEj8GyFeZEwAOrQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6370}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-compilation-targets": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.16_1739534370007_0.29419291786964874", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "d04dbf290ebec6bab1a6b4f2ec4f8c71bf6295c7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-ZyWKO2mfsO5rvGLsGDMYoBO8OrFipqy8Qtwi6yVpCq8jF6ZdAvIyKZpCJK85P7PTbFjI2PF8hU9xmp8a+XROgg==", "signatures": [{"sig": "MEQCIHC3vQUPLKvP7TSnYj8ksdhe5kasjocHtgPVXVI2aAg1AiAOJybzaJAHLWGdxCjX/X7efGFBgJVLo/Mwbnd5Hjzy1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6370}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-compilation-targets": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-alpha.17_1741717523430_0.22880337913828308", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-function-name", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "4d0bf307720e4dce6d7c30fcb1fd6ca77bdeb3a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==", "signatures": [{"sig": "MEUCICYL2fu8+662KA8r1x+00EhVQ/3E9kUhk/wK1TBNgzFDAiEAhk8Ct3Audkv+uPN4MLbjHIZ9A1coeoXgRhBvynB1Mv0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7115}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_7.27.1_1746025758875_0.4215718091292946", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-function-name@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "dist": {"shasum": "2053958982baed9715560435ecf8864a40277d4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-/85L+GRJf3DLaiwTDjJODZknIFI7E2faBdXjN5uvYxevV9C9UkMGb1CrWNIxRZNw+PqBrhtrMzbrMscUjTbq1A==", "signatures": [{"sig": "MEQCIE4UEi2xFERfYlRqeg6DTxmWM1Nx0BnP9o1q64Kq/QFtAiBBol+ZINPrmkeyE88VJITiusz1tjmOv4w3ZJ9GMcsqfg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6344}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-compilation-targets": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-function-name_8.0.0-beta.0_1748620295484_0.2241525745009958", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-function-name", "version": "8.0.0-beta.1", "description": "Apply ES2015 function.name semantics to all functions", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-function-name"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-compilation-targets": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-function-name@8.0.0-beta.1", "dist": {"shasum": "66f5665803c1b18a4ffc9132419a73c2c6b7a830", "integrity": "sha512-6XF+atpgEzlUerj0bPRfogW7R8lJ5EyPdFryfwYlQdOUuLFMxdBdA5OFq6BhJ19isSnLgJ+fWucJ0cinVQ2gBw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 6344, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDNj8XD1b2rnROfhI8FfutRC6ULY5vVmWC2SUmMwrVMSwIgFXF33x/5LCqE1ouo0lhVOjU3QOC/hXXfk9FEUEhWMms="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-function-name_8.0.0-beta.1_1751447078919_0.26088206960223936"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:19.906Z", "modified": "2025-07-02T09:04:39.354Z", "7.0.0-beta.4": "2017-10-30T18:36:19.906Z", "7.0.0-beta.5": "2017-10-30T20:57:57.061Z", "7.0.0-beta.31": "2017-11-03T20:04:31.251Z", "7.0.0-beta.32": "2017-11-12T13:33:54.706Z", "7.0.0-beta.33": "2017-12-01T14:29:11.691Z", "7.0.0-beta.34": "2017-12-02T14:40:06.908Z", "7.0.0-beta.35": "2017-12-14T21:48:21.453Z", "7.0.0-beta.36": "2017-12-25T19:05:29.348Z", "7.0.0-beta.37": "2018-01-08T16:03:36.728Z", "7.0.0-beta.38": "2018-01-17T16:32:34.467Z", "7.0.0-beta.39": "2018-01-30T20:28:40.535Z", "7.0.0-beta.40": "2018-02-12T16:42:31.906Z", "7.0.0-beta.41": "2018-03-14T16:26:44.323Z", "7.0.0-beta.42": "2018-03-15T20:52:01.623Z", "7.0.0-beta.43": "2018-04-02T16:48:52.492Z", "7.0.0-beta.44": "2018-04-02T22:20:34.011Z", "7.0.0-beta.45": "2018-04-23T01:58:15.177Z", "7.0.0-beta.46": "2018-04-23T04:32:33.030Z", "7.0.0-beta.47": "2018-05-15T00:17:34.620Z", "7.0.0-beta.48": "2018-05-24T19:24:29.874Z", "7.0.0-beta.49": "2018-05-25T16:04:08.357Z", "7.0.0-beta.50": "2018-06-12T19:47:56.362Z", "7.0.0-beta.51": "2018-06-12T21:20:35.620Z", "7.0.0-beta.52": "2018-07-06T00:59:44.137Z", "7.0.0-beta.53": "2018-07-11T13:40:43.371Z", "7.0.0-beta.54": "2018-07-16T18:00:25.062Z", "7.0.0-beta.55": "2018-07-28T22:07:50.183Z", "7.0.0-beta.56": "2018-08-04T01:08:13.889Z", "7.0.0-rc.0": "2018-08-09T15:59:46.973Z", "7.0.0-rc.1": "2018-08-09T20:09:39.526Z", "7.0.0-rc.2": "2018-08-21T19:25:44.572Z", "7.0.0-rc.3": "2018-08-24T18:09:29.583Z", "7.0.0-rc.4": "2018-08-27T16:46:12.170Z", "7.0.0": "2018-08-27T21:44:40.471Z", "7.1.0": "2018-09-17T19:30:47.036Z", "7.2.0": "2018-12-03T19:01:22.954Z", "7.4.3": "2019-04-02T19:55:49.932Z", "7.4.4": "2019-04-26T21:03:58.628Z", "7.7.0": "2019-11-05T10:53:48.010Z", "7.7.4": "2019-11-22T23:33:40.700Z", "7.8.0": "2020-01-12T00:17:17.198Z", "7.8.3": "2020-01-13T21:42:17.066Z", "7.10.1": "2020-05-27T22:08:03.394Z", "7.10.4": "2020-06-30T13:13:08.825Z", "7.12.1": "2020-10-15T22:40:05.320Z", "7.12.13": "2021-02-03T01:11:44.757Z", "7.14.5": "2021-06-09T23:13:00.997Z", "7.16.0": "2021-10-29T23:47:52.580Z", "7.16.5": "2021-12-13T22:28:13.379Z", "7.16.7": "2021-12-31T00:22:52.547Z", "7.18.6": "2022-06-27T19:50:29.805Z", "7.18.9": "2022-07-18T09:17:37.260Z", "7.21.4-esm": "2023-04-04T14:09:52.833Z", "7.21.4-esm.1": "2023-04-04T14:21:50.265Z", "7.21.4-esm.2": "2023-04-04T14:39:51.595Z", "7.21.4-esm.3": "2023-04-04T14:56:37.296Z", "7.21.4-esm.4": "2023-04-04T15:13:46.124Z", "7.22.5": "2023-06-08T18:21:41.228Z", "8.0.0-alpha.0": "2023-07-20T14:00:26.707Z", "8.0.0-alpha.1": "2023-07-24T17:52:59.163Z", "8.0.0-alpha.2": "2023-08-09T15:15:21.941Z", "8.0.0-alpha.3": "2023-09-26T14:57:36.747Z", "8.0.0-alpha.4": "2023-10-12T02:06:49.720Z", "7.23.3": "2023-11-09T07:03:50.671Z", "8.0.0-alpha.5": "2023-12-11T15:19:44.535Z", "8.0.0-alpha.6": "2024-01-26T16:14:42.029Z", "8.0.0-alpha.7": "2024-02-28T14:05:44.508Z", "7.24.1": "2024-03-19T09:48:37.056Z", "8.0.0-alpha.8": "2024-04-04T13:20:19.723Z", "7.24.6": "2024-05-24T12:25:10.402Z", "8.0.0-alpha.9": "2024-06-03T14:04:53.642Z", "8.0.0-alpha.10": "2024-06-04T11:20:30.176Z", "7.24.7": "2024-06-05T13:15:44.692Z", "8.0.0-alpha.11": "2024-06-07T09:15:54.907Z", "7.25.0": "2024-07-26T16:59:30.729Z", "8.0.0-alpha.12": "2024-07-26T17:33:52.038Z", "7.25.1": "2024-07-28T19:36:43.073Z", "7.25.7": "2024-10-02T15:15:20.210Z", "7.25.9": "2024-10-22T15:21:35.909Z", "8.0.0-alpha.13": "2024-10-25T13:54:36.512Z", "8.0.0-alpha.14": "2024-12-06T16:54:26.149Z", "8.0.0-alpha.15": "2025-01-10T17:24:54.222Z", "8.0.0-alpha.16": "2025-02-14T11:59:30.181Z", "8.0.0-alpha.17": "2025-03-11T18:25:23.631Z", "7.27.1": "2025-04-30T15:09:19.043Z", "8.0.0-beta.0": "2025-05-30T15:51:35.694Z", "8.0.0-beta.1": "2025-07-02T09:04:39.087Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-function-name", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-function-name"}, "description": "Apply ES2015 function.name semantics to all functions", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}